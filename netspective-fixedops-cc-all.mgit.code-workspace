{"folders": [{"name": "FOC3 Dashboard UI", "path": "git.netspective.com/netspective-foc3/fixedops-multi-chart-dashboard-ui"}, {"name": "FOC3 UI cubejs server", "path": "git.netspective.com/netspective-foc3/fixedops-cubejs-server"}, {"name": "FOC3 Drill down view for verification", "path": "git.netspective.com/netspective-foc3/verification-ag-grid-view"}, {"name": "FOC3 middleware Db automation script", "path": "git.netspective.com/netspective-foc3/liquibase"}, {"name": "FOC3 middleware Postgraphile BaaS", "path": "git.netspective.com/netspective-foc3/postgraphile-baas"}, {"name": "FOC3 Documentataion for reference to others", "path": "git.netspective.com/netspective-foc3/reference-document"}, {"name": "FOC3 Docsy site for Customer IT Portal", "path": "git.netspective.com/netspective-foc3/docs"}, {"name": "FOC3 IGS transformation repository", "path": "git.netspective.com/netspective-foc3/foc3-next-architecture/foc3-giac-specification"}, {"name": "Fixed Ops Lite Hugo", "path": "git.netspective.com/netspective-foc3/fixed-ops-lite"}, {"name": "Fixed Ops Postgres to SQLite Exporter", "path": "git.netspective.com/netspective-foc3/fixed-ops-pg-to-sqlite"}, {"name": "Fixed Ops Lite Cube Server", "path": "git.netspective.com/netspective-foc3/fixed-ops-lite-cube-server"}, {"name": "Fixed Ops Lite React UI", "path": "git.netspective.com/netspective-foc3/fixed-ops-lite-react-ui"}, {"name": "Keycloak Telemetry", "path": "https://git.netspective.com/netspective-foc3/keycloak-telemetry"}], "settings": {"git.autofetch": true}}