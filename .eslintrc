{
  "parser": "babel-eslint",
  "env": {
    "es6": true,
    "browser": true
  },
  "extends": ["eslint:recommended", "airbnb", "airbnb/hooks"],
  "settings": {
    "import/resolver": {
        "node": {
          "paths": ["src"],
          "extensions": [".js", ".jsx", ".ts", ".tsx"]
        }
    }
  },
  "rules": {
    "comma-dangle": "off",
    "no-plusplus": "off",
    "import/no-unresolved": "off", // Temporary fix
    "react/jsx-filename-extension": "off",
    "react/jsx-props-no-spreading": "off",
    "react/require-default-props": "off",
    "react/forbid-prop-types": "off",
    "jsx-a11y/click-events-have-key-events": "off",
    "jsx-a11y/no-static-element-interactions": "off",
    "jsx-a11y/control-has-associated-label": "off",
    "jsx-a11y/anchor-is-valid": "off"
  }
}