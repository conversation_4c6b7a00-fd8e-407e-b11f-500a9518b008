/* eslint-disable react/no-multi-comp */
/* eslint-disable react/display-name */
import React, { lazy } from 'react';
import { Redirect } from 'react-router-dom';
import AuthLayout from './layouts/Auth';
import DashboardLayout from './layouts/Dashboard';
import ErrorLayout from './layouts/Error';
import AddOns from './views/AddOns';
import AnalyzeData from './views/AnalyzeData';
import ROLevelRawDataGrid from './views/AnalyzeData/Component/ROLevelRawDataGrid';
import DrilldownTechDetailView from './views/AnalyzeData/DrilldownTechDetailView';
import Reports from './views/AnalyzeData/Reports';
import ChartMaster from './views/ChartMaster';
import CPELROpportunity from './views/CPELROpportunity';
import CPLaborOverview from './views/CPLaborOverview';
import CPOverview from './views/CPOverview';
import CPPartsOverview from './views/CPPartsOverview';
import Discounts from './views/Discounts';
import GraphDetailsView from './views/GraphDetailsView';
import LaborGrossAndVolumeOpportunity from './views/LaborGrossAndVolumeOpportunity';
import LaborItemization from './views/LaborItemization';
import LaborTranches from './views/LaborTranches';
import PartsGrossAndVolumeOpportunity from './views/PartsGrossAndVolumeOpportunity';
import PartsItemization from './views/PartsItemization';
import PartsTranches from './views/PartsTranches';
import ServiceAdvisorDetails from './views/ServiceAdvisorDetails';
import PayTypeMaster from './views/PayTypeMaster';
import SpecialMetrics from './views/SpecialMetrics';
import OPcodes from './views/OPcodes';
import Redirection from './components/Redirect';
import { getVerificationDashboardBaseURL } from './utils/Utils';
import MyFavorites from './views/Favourites';
import StoreComparison from './views/StoreComparison';
import SearchByRO from './views/SearchByRo';
import TrancheReport from './views/AnalyzeData/TranchReport';
import OpportunityDetailsReport from './views/AnalyzeData/TranchReport/OpportunityDetailsReport';
import Makes from './views/Makes';
import WorkMixVolume from './views/AnalyzeData/WorkMixVolume';
import WorkMixVolumeVersion2 from './views/AnalyzeData/WorkMixVolume2.4';
import WorkMixVolumeParts from './views/AnalyzeData/WorkMixVolumeParts';
import FixedopsMapperOpcodes from './views/FixedopsMapperOpcodes';
import SharedSequencesByMake from './views/SharedSequencesByMake';
import NewCarWarranty from './views/NewCarWarranty';
import RevenueStatement from './views/RevenueStatement';
import RevenueSummaryDrilldown from './views/RevenueStatement/Drilldown';
import WarrantyRatesVolumes from './views/WarrantyRatesVolumes';
import WarrantyReference from './views/WarrantyRatesVolumes/WarrantyReference';
import Technicians from './views/Technicians';
import Homenew from './views/Home';
import Home from './views/Homenew';
import LaborMisses from './views/AnalyzeData/LaborMisses';
import PartsMisses from './views/AnalyzeData/PartsMisses';
import Glossary from './views/Glossary';
import KeycloakUserCreation from './views/KeycloakUserCreation';
import CustomerHistory from './views/CustomerHistory';
import ScoreCardGoalSetting from './views/KPIScoreCardGoalSettings';
import StoreSetting from './views/StoreSettings';
import ChangeLog from './views/Changelog';
import KpiReport from './views/KPIReport';
import MPIOpcode from './views/MPIOpcode';
import MenuOpcode from './views/MenuOpcode';
import LaborGridPricing from './views/KPILaborGridPricing';
import PartsMatrixPricing from './views/KPIPartsMatrixPricing';
import UserLoginHistory from './views/UserLoginHistory';
// import PartsMatrix from './views/PartsMatrix/PartsMatrixOld';
import PartsMatrix from './views/PartsMatrix/PartsMatrix';
//import PartsMatrixOld from './views/PartsMatrixOld/PartsMatrixOld';

import Mpi from './views/Mpi/Mpi';
import DailyDataImports from './views/DailyDataImports';
import ChangeLogInternal from './views/ChangelogInternal';
import EditHistory from './views/EditHistory';
import EditHistoryAll from './views/EditHistoryAll';
import EditHistoryOld from './views/EditHistoryOld';
import FleetAccounts from './views/FleetAccounts';
import FleetAccountsNew from './views/FleetAccountsNew';
import ROCalendar from './views/ROCalendar';
import FixedRates from './views/FixedRates';
import KPIReportComparative from './views/KPIReportComparative';

import Login from './views/Login';
import JobCountGrid from './views/JobCountGrid';
import TrendReport from './views/TrendReport';
import StoreAdvisors from './views/StoreAdvisors';
import KpiGraphics from './views/KpiGraphics';
import LaborMissesOld from './views/AnalyzeData/LaborMissesOld';
import PartsMissesOld from './views/AnalyzeData/PartsMissesOld';
import MenuModelMapping from './views/MenuModelMapping';
import GridModelMapping from './views/Model/Model';
import EmailList from './views/Email';
import ReportSaved from './views/ReportSaved';
// import Home from './views/Homenew';
import LaborGridSetup from './views/LaborGridSetup';
import PartsMatrixSetup from './views/PartsMatrixSetup';
import RoShowHide from './views/RoShowHide';
import NonCPOpcodes from './views/OPcodes/Non-CustomerPayOpcodes/NonCPOpcodes';
import KPIReportTechComparative from './views/KPITechComparative';
import MPIStats from './views/AnalyzeData/MPIStats';
import ThreeMonthReportPage from './views/ReportCard/ThreeMonthReportCard';
import OneMonthReportPage from './views/ReportCard/OneMonthReportCard';
export default [
  {
    path: '/',
    exact: true,
    component: () => <Redirect to="/auth/login?provenance=fopc" />
  },
  {
    path: '/auth',
    component: AuthLayout,
    routes: [
      {
        path: '/auth/login',
        exact: true,
        component: lazy(() => import('src/views/Login'))
      },
      {
        component: () => <Redirect to="/errors/error-404" />
      }
    ]
  },

  {
    path: '/errors',
    component: ErrorLayout,
    routes: [
      {
        path: '/errors/error-401',
        exact: true,
        component: lazy(() => import('src/views/Error401'))
      },
      {
        path: '/errors/error-404',
        exact: true,
        component: lazy(() => import('src/views/Error404'))
      },
      {
        path: '/errors/error-500',
        exact: true,
        component: lazy(() => import('src/views/Error500'))
      },
      {
        component: () => <Redirect to="/errors/error-404" />
      }
    ]
  },
  {
    route: '*',
    component: DashboardLayout,
    routes: [
      {
        path: '/2.4.0/Home',
        exact: true,
        component: Homenew
      },
      {
        path: '/Home',
        exact: true,
        component: Home
      },
      {
        path: '/CPOverview',
        exact: true,
        component: CPOverview
      },
      {
        path: '/CPLaborOverview',
        exact: true,
        component: CPLaborOverview
      },
      {
        path: '/LaborTranches',
        exact: true,
        component: LaborTranches
      },
      {
        path: '/LaborItemization',
        exact: true,
        component: LaborItemization
      },
      {
        path: '/CPPartsOverview',
        exact: true,
        component: CPPartsOverview
      },
      {
        path: '/MyFavorites',
        exact: true,
        component: MyFavorites
      },
      {
        path: '/PartsTranches',
        exact: true,
        component: PartsTranches
      },
      {
        path: '/PartsItemization',
        exact: true,
        component: PartsItemization
      },

      {
        path: '/CPELROpportunity',
        exact: true,
        component: CPELROpportunity
      },

      {
        path: '/GraphDetailsView',
        exact: true,
        component: GraphDetailsView
      },

      {
        path: '/SpecialMetrics',
        exact: true,
        component: SpecialMetrics
      },
      {
        path: '/Discounts',
        exact: true,
        component: Discounts
      },
      {
        path: '/AddOns',
        exact: true,
        component: AddOns
      },

      {
        path: '/LaborGrossAndVolumeOpportunity',
        exact: true,
        component: LaborGrossAndVolumeOpportunity
      },
      {
        path: '/PartsGrossAndVolumeOpportunity',
        exact: true,
        component: PartsGrossAndVolumeOpportunity
      },
      {
        path: '/AnalyzeData',
        exact: true,
        component: AnalyzeData
      },
      {
        path: '/ServiceAdvisorPerformance',
        exact: true,
        component: AnalyzeData,
        state: {
          drillDownType: 'ServiceAdvisorEfficiency'
        }
      },
      {
        path: '/PartsWorkMixAnalysis',
        exact: true,
        component: AnalyzeData,
        state: {
          drillDownType: 'PartsWorkMixAnalysis'
        }
      },
      {
        path: '/LaborWorkMixAnalysis',
        exact: true,
        component: AnalyzeData,
        state: {
          drillDownType: 'LaborWorkMixAnalysis'
        }
      },
      {
        path: '/TechnicianPerformance',
        exact: true,
        component: AnalyzeData,
        state: {
          drillDownType: 'TechnicianEfficiency'
        }
      },
      {
        path: '/ChartMaster',
        exact: true,
        component: ChartMaster
      },
      {
        path: '/ServiceAdvisor',
        exact: true,
        component: ServiceAdvisorDetails
      },
      {
        path: '/Technician',
        exact: true,
        component: Technicians
      },
      {
        path: '/PayTypeMaster',
        exact: true,
        component: PayTypeMaster
      },
      {
        path: '/ROLevelRawDataGrid',
        exact: true,
        component: ROLevelRawDataGrid
      },
      {
        path: '/DrilldownTechDetailView',
        exact: true,
        component: DrilldownTechDetailView
      },
      {
        path: '/Reports',
        exact: true,
        component: Reports
      },
      {
        path: '/OPcodes',
        exact: true,
        component: OPcodes
      },
      {
        path: '/Glossary',
        exact: true,
        component: Glossary
      },
      {
        path: '/LaborGridPricing',
        exact: true,
        component: LaborGridSetup
      },
      // {
      //   path: '/PartsMatrixPricing',
      //   exact: true,
      //   component: PartsMatrixPricing
      // },
      {
        path: '/PartsMatrixPricing',
        exact: true,
        component: PartsMatrix
      },
      {
        path: '/Email',
        exact: true,
        component: EmailList
      },
      {
        path: '/PartsMatrixPricings',
        exact: true,
        component: PartsMatrixPricing
      },
      {
        path: '/MenuModelMapping',
        exact: true,
        component: MenuModelMapping
      },
      {
        path: '/GridModelMapping',
        exact: true,
        component: GridModelMapping
      },
      {
        path: '/MPIOpcode',
        exact: true,
        component: MPIOpcode
      },
      {
        path: '/PartsMatrix',
        exact: true,
        component: PartsMatrixSetup
      },
      {
        path: '/RoShowHide',
        exact: true,
        component: RoShowHide
      },
      {
        path: '/Mpi',
        exact: true,
        component: Mpi
      },
      {
        path: '/SavedReports',
        exact: true,
        component: ReportSaved
      },
      {
        path: '/ThreeMonthReport',
        exact: true,
        component: ThreeMonthReportPage
      },
      {
        path: '/OneMonthReport',
        exact: true,
        component: OneMonthReportPage
      },
      {
        path: '/MenuOpcode',
        exact: true,
        component: MenuOpcode
      },
      {
        path: '/ScoreCardGoalSetting',
        exact: true,
        component: ScoreCardGoalSetting
      },
      {
        path: '/StoreSettings',
        exact: true,
        component: StoreSetting
      },

      {
        path: '/Makes',
        exact: true,
        component: Makes
      },
      {
        path: '/ServiceAdvisors',
        exact: true,
        component: ServiceAdvisorDetails
      },
      {
        path: '/Technicians',
        exact: true,
        component: Technicians
      },
      {
        path: '/FixedopsMapperOpcodes',
        exact: true,
        component: FixedopsMapperOpcodes
      },
      {
        path: '/SharedSequencesByMake',
        exact: true,
        component: SharedSequencesByMake
      },
      {
        path: '/NewCarWarranty',
        exact: true,
        component: NewCarWarranty
      },
      {
        path: '/WorkMixVolume',
        exact: true,
        component: WorkMixVolume
      },
      {
        path: '/2.4.0/WorkMixVolume',
        exact: true,
        component: WorkMixVolumeVersion2
      },
      {
        path: '/WorkMixVolumeParts',
        exact: true,
        component: WorkMixVolumeParts
      },
      {
        path: '/RevenueSummary',
        //component: Redirection,
        exact: false,
        component: RevenueStatement
        // loc:
        //   getVerificationDashboardBaseURL() +
        //   '/FOC3_revenueStatement/ag-grid.html'
      },
      {
        path: '/WarrantyRatesLabor',
        exact: true,
        component: WarrantyRatesVolumes
        // loc:
        //   getVerificationDashboardBaseURL() +
        //   '/FOC3_warrantyRatesLabor/ag-grid.html'
      },
      {
        path: '/WarrantyMarkupParts',
        exact: true,
        component: WarrantyRatesVolumes
        // loc:
        //   getVerificationDashboardBaseURL() +
        //   '/FOC3_warrantyMarkupParts/ag-grid.html'
      },
      {
        path: '/UserLoginHistory',
        exact: true,
        component: UserLoginHistory
        // loc:
        //   getVerificationDashboardBaseURL() +
        //   '/FOC3_warrantyMarkupParts/ag-grid.html'
      },
      {
        path: '/DailyDataImports',
        exact: true,
        component: DailyDataImports
        // loc:
        //   getVerificationDashboardBaseURL() +
        //   '/FOC3_warrantyMarkupParts/ag-grid.html'
      },
      {
        path: '/WarrantyReferenceLabor',
        exact: true,
        component: WarrantyReference
        // loc:
        //   getVerificationDashboardBaseURL() +
        //   '/FOC3_warrantyMarkupParts/ag-grid.html'
      },
      {
        path: '/WarrantyReferenceParts',
        exact: true,
        component: WarrantyReference
        // loc:
        //   getVerificationDashboardBaseURL() +
        //   '/FOC3_warrantyMarkupParts/ag-grid.html'
      },
      {
        path: '/RevenueSummaryDrilldown',
        exact: true,
        // component: Redirection,
        // loc: getVerificationDashboardBaseURL() + "/FOC3_Searchbyro/ag-grid.html"
        component: RevenueSummaryDrilldown
      },
      {
        path: '/SearchByRO',
        exact: true,
        // component: Redirection,
        // loc: getVerificationDashboardBaseURL() + "/FOC3_Searchbyro/ag-grid.html"
        component: SearchByRO
      },
      {
        path: '/StoreComparison',
        exact: true,
        component: StoreComparison
      },
      {
        path: '/TrancheReport',
        exact: true,
        component: TrancheReport
      },
      {
        path: '/TrancheReport/OpportunityDetailsReport',
        exact: true,
        component: OpportunityDetailsReport
      },
      {
        path: '/LaborMisses',
        exact: true,
        component: LaborMisses
      },
      {
        path: '/PartsMisses',
        exact: true,
        component: PartsMisses
      },
      {
        path: '/LaborGridMisses',
        exact: true,
        component: LaborMissesOld
      },
      {
        path: '/PartsTargetMisses',
        exact: true,
        component: PartsMissesOld
      },
      {
        path: '/ChangeLog',
        exact: true,
        component: ChangeLog
      },
      {
        path: '/ChangeLogInternal',
        exact: true,
        component: ChangeLogInternal
      },
      {
        path: '/KpiReport',
        exact: true,
        component: KpiReport
      },
      {
        path: '/JobCountGrid',
        exact: true,
        component: JobCountGrid
      },
      {
        path: '/TrendReport',
        exact: true,
        component: TrendReport
      },

      {
        path: '/KPIReportComparative',
        exact: true,
        component: KPIReportComparative
      },
      {
        path: '/KpiGraphics',
        exact: true,
        component: KpiGraphics
      },
      {
        path: '/MPIStats',
        exact: true,
        component: MPIStats
      },
      {
        path: '/KPIReportStoreComparative',
        exact: true,
        component: KPIReportComparative
      },
      // {
      //   path: '/SavedKpiReport',
      //   exact: true,
      //   component: SavedKpiReport
      // },
      // {
      //   path: '/EditHistoryOld',
      //   exact: true,
      //   component: EditHistoryOld
      // },
      {
        path: '/EditHistory',
        exact: true,
        component: EditHistory
      },
      {
        path: '/EditHistoryAll',
        exact: true,
        component: EditHistoryAll
      },
      {
        path: '/ROCalendar',
        exact: true,
        component: ROCalendar
      },
      {
        path: '/FleetAccounts',
        exact: true,
        component: FleetAccountsNew
      },
      {
        path: '/FleetAccountsOld',
        exact: true,
        component: FleetAccounts
      },
      {
        path: '/FixedRates',
        exact: true,
        component: FixedRates
      },

      {
        path: '/KeycloakUserCreation',
        exact: true,
        component: KeycloakUserCreation
      },
      {
        path: '/CustomerHistory',
        exact: true,
        component: CustomerHistory
      },
      {
        path: '/StoreAssignments',
        exact: true,
        component: StoreAdvisors
      },
      {
        path: '/NonCPOpcodes',
        exact: true,
        component: NonCPOpcodes
      },
      // {
      //   path: '/NewPartsMatrix',
      //   exact: false,
      //   component: PartsMatrixSetup
      // },
      // {
      //   path: '/NewLaborGrid',
      //   exact: false,
      //   component: LaborGridSetup
      // },
      {
        path: '/ReportSaved',
        exact: true,
        component: ReportSaved
      },
      {
        path: '/KPIReportTechComparative',
        exact: true,
        component: KPIReportTechComparative
      },
      {
        component: () => <Redirect to="/errors/error-404" />
      }
    ]
  }
];
