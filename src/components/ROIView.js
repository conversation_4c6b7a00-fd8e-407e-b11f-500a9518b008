import React, { useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import Paper from '@material-ui/core/Paper';
import {
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
  Tooltip
} from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(1),
    maxWidth: 500
  },
  image: {
    width: 100,
    height: 75,
    cursor: 'default'
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '50%'
  },
  container: {
    alignItems: 'center',
    '@media (max-width: 1920px)': {
      width: 250
    },
    '@media (max-width: 1440px)': {
      width: 220
    },
    '@media (max-width: 1280px)': {
      width: 200
    },
    '@media (min-width: 2304px)': {
      width: 350
    }
  },
  titleLabel: {
    display: 'flex',
    fontSize: 15,
    cursor: 'default'
  },
  valLabel: {
    display: 'flex',
    cursor: 'default'
  },
  dataItem: {
    marginLeft: 20,
    padding: '0px !important'
  },
  tooltip: {
    marginTop: '-20px'
  },
  tooltipAll: {
    marginTop: '-20px',
    marginLeft: '-80px',
    width: '35px'
  }
}));

const ROIView = ({ selectedOptions, setSelectedOptions, options }) => {
  const classes = useStyles();
  const handleChange = key => {
    setSelectedOptions(prev => {
      if (key === 'all') {
        // If "All" is selected, either select all or clear all
        const allSelected = !prev.includes('all');
        return allSelected ? options.map(option => option.key) : [];
      }

      // Toggle individual checkbox
      const isSelected = prev.includes(key);
      const updated = isSelected
        ? prev.filter(item => item !== key)
        : [...prev, key];

      // Check if "All" should be selected
      const allKeys = options
        .filter(option => option.key !== 'all')
        .map(option => option.key);
      const isAllSelected = allKeys.every(item => updated.includes(item));

      return isAllSelected
        ? [...updated, 'all']
        : updated.filter(item => item !== 'all');
    });
  };

  return (
    <Grid container xs={12} className={clsx('sidebar-card', classes.container)}>
      <Typography className="sidebar-view-title" variant="h5" gutterBottom>
        ROI View
      </Typography>
      <Grid item>
        <Paper className="sidebar-box">
          <FormGroup>
            {options.map(({ key, label }) => (
              <Tooltip
                key={key}
                title={label}
                classes={{
                  tooltip: label == 'All' ? classes.tooltipAll : classes.tooltip
                }}
                placement="bottom"
              >
                <div>
                  <FormControlLabel
                    key={key}
                    classes="sidebar-checkbox"
                    style={{ pointerEvents: 'none' }}
                    control={
                      <Checkbox
                        checked={selectedOptions.includes(key)}
                        style={{ pointerEvents: 'auto' }}
                        onChange={() => handleChange(key)}
                      />
                    }
                    label={label}
                  />
                </div>
              </Tooltip>
            ))}
          </FormGroup>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default ROIView;
