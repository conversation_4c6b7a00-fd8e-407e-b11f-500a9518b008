import {
  Button,
  Checkbox,
  FormControlLabel,
  Menu,
  MenuItem,
  Card,
  CardContent,
  CardActions,
  CardHeader,
  Tooltip,
  IconButton,
  Divider,
  Typography,
  Radio
} from '@material-ui/core';
import ArrowDropDownIcon from '@material-ui/icons/ArrowDropDown';
import DoneIcon from '@material-ui/icons/Done';
import clsx from 'clsx';
import EditOutlinedIcon from '@material-ui/icons/EditOutlined';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import React, { useRef, useState, useEffect } from 'react';
import ScrollBar from 'react-perfect-scrollbar';
import { useHistory, useLocation } from 'react-router';
import $ from 'jquery';

import {
  getAdvisorenabledCharts,
  isAdvisorenabledCharts
} from 'src/utils/Utils';
import { useDispatch, useSelector } from 'react-redux';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

const useStyles = makeStyles(theme => ({
  root: {},
  menu: {
    zIndex: '1600 !important'
  },
  menuItem: {
    padding: 0
  },
  menuadvi: {
    padding: 0
  },
  labelRoot: {
    color: '#ccc'
  },
  noadvisorsection: {
    fontSize: 12
  },
  cardControl: {
    padding: 0,
    overflowX: 'hidden'
  },
  selectBox: {
    border: '2px solid #fff',
    marginLeft: 8,
    color: '#fff',
    textTransform: 'none',
    padding: '5px',
    fontSize: 12,
    position: 'relative',
    bottom: 2
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),

    width: '100%',
    color: '#FFF',
    margin: 0
  },
  active: {
    color: theme.palette.primary.main
  },
  item: {
    paddingTop: 0
  }
}));

function MultiSelect({
  label,
  options,
  value,
  onChange,
  advisorNames,
  selected,
  keycloak,
  onControlSelect
}) {
  const classes = useStyles();
  const anchorRef = useRef(null);
  const [openMenu, setOpenMenu] = useState(false);
  const checkedValues = [];
  const history = useHistory();
  const session = useSelector(state => state.session);
  const [isApplied, setApply] = useState(false);
  const handleMenuOpen = () => {
    setOpenMenu(true);
  };

  const handleMenuClose = isApply => {
    setApply(isApply);
    value.sort();
    selected.sort();
    if (isApply != true && !lodash.isEqual(value, selected)) {
      while (value.length > 0) {
        value.pop();
      }
      if (selected.indexOf('All') > -1) {
        value.push('All');
      } else {
        let sortedArray = [];
        options.map(first => {
          sortedArray[selected.findIndex(def => def === first)] = first;
        });
        value = sortedArray.filter(v => v);
        setApply(true);

        //  value.push(value.concat((selected.toString()).split(',')) )
      }
    }
    setOpenMenu(false);
  };
  const handleOptionToggleApply = val => {
    if (onChange) {
      handleMenuClose(true);
      onChange(value, 'apply');
    }
  };
  const handleOptionToggle = event => {
    value = value.filter(
      item => item !== 'Total Selected' && item !== 'Total Shop'
    );
    let newValue = value.length > 0 ? [...value] : selected;

    if (event.target.checked) {
      //checked
      if (event.target.value == 'All') {
        newValue = ['All'];
      } else {
        newValue = newValue.filter(item => item !== 'All');
        newValue.push(event.target.value);
      }
    } else {
      //unchecked
      if (event.target.value == 'All') {
        newValue = ['All'];
      } else {
        // newValue = newValue.filter(item => item !== 'All');
        newValue = newValue.filter(item => item !== event.target.value);
        if (newValue.length === 0) {
          newValue = ['All'];
        }
      }
    }
    checkedValues.push(newValue);
    if (onChange) {
      onChange(newValue, 'userselection');
    }

    const controlType = event.target.type === 'checkbox' ? 'Checkbox' : 'Radio';
    if (onControlSelect) {
      onControlSelect(controlType);
    }

    // if (onChange) {
    //   handleMenuClose();
    //   onChange(newValue);
    // }
  };
  const addServiceAdvisor = () => {
    if (isApplied == false) {
      if (value.length > 0) {
        value.splice(0, value.length);
      }
      value.push('All');
    }
    if (isApplied == true && selected.length > 0) {
      let unCommonElements = lodash.xor(value, selected);
      if (unCommonElements.length > 0) {
        if (value.length > 0) {
          value.splice(0, value.length);
        }
        value.push(...selected);
      }
    }
    if (label == 'Technicians') {
      // history.push('/Technicians');
      history.push({
        pathname: '/Technicians',
        state: {
          isFrom: window.location.pathname
        }
      });
    } else if (
      label == 'Service Advisors' &&
      history.location.pathname == '/ServiceAdvisorPerformance' &&
      document.getElementById('Service Advisor Summary') &&
      document.getElementById('Service Advisor Summary').attributes[4].value ==
        'true'
    ) {
      history.push({
        pathname: '/ServiceAdvisors',
        state: {
          isFrom: window.location.pathname,
          tabSelection: 'eleven'
        }
      });
    } else if (
      label == 'Service Advisors' &&
      history.location.pathname == '/ServiceAdvisorPerformance' &&
      document.getElementById('Service Advisor Detailed View') &&
      document.getElementById('Service Advisor Detailed View').attributes[4]
        .value == 'true'
    ) {
      history.push({
        pathname: '/ServiceAdvisors',
        state: {
          isFrom: window.location.pathname,
          tabSelection: 'twelve'
        }
      });
    } else if (
      label == 'Service Advisors' &&
      history.location.pathname == '/ServiceAdvisorPerformance' &&
      document.getElementById('Service Advisor Performance') &&
      document.getElementById('Service Advisor Performance').attributes[4]
        .value == 'true'
    ) {
      history.push({
        pathname: '/ServiceAdvisors',
        state: {
          isFrom: window.location.pathname,
          tabSelection: 'eight',
          selectedSubTab:
            history.location.pathname == '/ServiceAdvisorPerformance' &&
            document.getElementById('Comparison by Month') &&
            document.getElementById('Comparison by Month').attributes[4]
              .value == 'true'
              ? 'two'
              : history.location.pathname == '/ServiceAdvisorPerformance' &&
                document.getElementById('Category by Month') &&
                document.getElementById('Category by Month').attributes[4]
                  .value == 'true'
              ? 'three'
              : 'one'
        }
      });
    } else if (
      label == 'Service Advisors' &&
      (history.location.pathname == '/LaborItemization' ||
        history.location.pathname == '/PartsItemization' ||
        history.location.pathname == '/JobCountGrid')
    ) {
      history.push({
        pathname: '/ServiceAdvisors',
        state: {
          isFrom: window.location.pathname,
          tabSelection:
            (history.location.pathname == '/LaborItemization' ||
              history.location.pathname == '/PartsItemization' ||
              history.location.pathname == '/JobCountGrid') &&
            document.getElementById('Last 6 Months') &&
            document.getElementById('Last 6 Months').attributes[4].value ==
              'true'
              ? 'three'
              : (history.location.pathname == '/LaborItemization' ||
                  history.location.pathname == '/PartsItemization' ||
                  history.location.pathname == '/JobCountGrid') &&
                document.getElementById('Last 90 Days') &&
                document.getElementById('Last 90 Days').attributes[4].value ==
                  'true'
              ? 'two'
              : (history.location.pathname == '/LaborItemization' ||
                  history.location.pathname == '/PartsItemization' ||
                  history.location.pathname == '/JobCountGrid') &&
                document.getElementById('Last 30 Days') &&
                document.getElementById('Last 30 Days').attributes[4].value ==
                  'true'
              ? 'one'
              : 'four'
        }
      });
    } else if (
      label == 'Service Advisors' &&
      history.location.pathname == '/TrendReport'
    ) {
      history.push({
        pathname: '/ServiceAdvisors',
        state: {
          parent: window.location.pathname
        }
      });
    } else {
      history.push({
        pathname: '/ServiceAdvisors',
        state: {
          isFrom: window.location.pathname
        }
      });
    }
    handleMenuClose(true);
  };

  return (
    <>
      <Button
        className={classes.selectBox}
        onClick={handleMenuOpen}
        ref={anchorRef}
        id={'advisor-selection'}
      >
        {label}
        <ArrowDropDownIcon />
      </Button>

      <Menu
        anchorEl={anchorRef.current}
        className={clsx(classes.menu, 'menuadvi .MuiMenu-paper')}
        classes={{ root: classes.item }}
        onClose={handleMenuClose}
        open={openMenu}
        elevation={1}
        id="service-advisor-list"
        // eslint-disable-next-line react/jsx-sort-props
        PaperProps={{ style: { minWidth: 250 } }}
      >
        <Card raised={false} elevation={0} className={classes.cardControl}>
          {history.location.pathname != '/Discounts' ? (
            <ScrollBar
              style={{
                height: options.length < 9 ? 'auto' : '350px',
                overflowX: 'unset !important'
              }}
            >
              <CardContent
                className={classes.cardControl}
                style={{ height: options.length < 9 ? 'auto' : '350px' }}
              >
                {options.map((option, index) => (
                  <MenuItem
                    className={classes.menuItem}
                    key={option}
                    disabled={
                      history.location.pathname === '/CPOverview' ||
                      history.location.pathname === '/ROCalendar' ||
                      history.location.pathname === '/CPLaborOverview' ||
                      history.location.pathname === '/CPPartsOverview' ||
                      history.location.pathname === '/SpecialMetrics' ||
                      history.location.pathname === '/RevenueSummary' ||
                      history.location.pathname ===
                        '/RevenueSummaryDrilldown' ||
                      // history.location.pathname === '/MyFavorites' ||
                      history.location.pathname == '/LaborItemization' ||
                      history.location.pathname == '/PartsItemization' ||
                      history.location.pathname == '/Home' ||
                      history.location.pathname == '/KPIReportComparative' ||
                      history.location.pathname == '/2.4.0/Home' ||
                      history.location.pathname == '/KpiGraphics' ||
                      history.location.pathname ==
                        '/ServiceAdvisorPerformance' ||
                      history.location.pathname == '/LaborMisses' ||
                      history.location.pathname == '/MPIStats' ||
                      (history.location.pathname == '/AnalyzeData' &&
                        history.location.search == '?OneLine') ||
                      history.location.pathname == '/PartsMisses' ||
                      history.location.pathname == '/KpiReport' ||
                      history.location.pathname ==
                        '/KPIReportTechComparative' ||
                      history.location.pathname ==
                        '/LaborGrossAndVolumeOpportunity' ||
                      history.location.pathname ==
                        '/PartsGrossAndVolumeOpportunity' ||
                      history.location.pathname == '/CPELROpportunity' ||
                      history.location.pathname == '/JobCountGrid' ||
                      history.location.pathname == '/TrendReport' ||
                      history.location.prevPath ==
                        '/GraphDetailsView?chartId=1090' ||
                      history.location.prevPath ==
                        '/GraphDetailsView?chartId=1096' ||
                      (history.location.pathname == '/LaborWorkMixAnalysis' &&
                        document.getElementById('Work mix tab') &&
                        document.getElementById('Work mix tab').attributes[4]
                          .value == 'true') ||
                      (history.location.pathname == '/PartsWorkMixAnalysis' &&
                        document.getElementById('Work mix tab') &&
                        document.getElementById('Work mix tab').attributes[4]
                          .value == 'true') ||
                      (history.location.pathname == '/GraphDetailsView' &&
                        (history.location.search == '?chartId=1090' ||
                          history.location.search == '?chartId=1096')) ||
                      (history.location.pathname == '/GraphDetailsView' &&
                        Number(
                          history.location.search.split('?chartId=').pop()
                        ) >= 1243 &&
                        Number(
                          history.location.search.split('?chartId=').pop()
                        ) <= 1248) ||
                      (Number(
                        history.location.search.split('?chartId=').pop()
                      ) >= 1253 &&
                        Number(
                          history.location.search.split('?chartId=').pop()
                        ) <= 1258) ||
                      isAdvisorenabledCharts(session.currentPath) === true
                        ? false
                        : history.location.prevPath ==
                            '/LaborWorkMixAnalysis' ||
                          history.location.prevPath == '/PartsWorkMixAnalysis'
                        ? false
                        : true
                    }
                    style={{
                      backgroundColor:
                        advisorNames[index] &&
                        advisorNames[index].split('-status-')[1] == 0
                          ? Dealer === 'Armatus'
                            ? '#ddeaf4'
                            : '#F4E1E7'
                          : '',
                      overflowX: 'hidden'
                    }}
                  >
                    <FormControlLabel
                      classes={{
                        root: clsx(classes.formControlLabel, classes.labelRoot)
                      }}
                      control={
                        history.location.pathname == '/KpiReport' ||
                        history.location.pathname ==
                          '/LaborGrossAndVolumeOpportunity' ||
                        history.location.pathname ==
                          '/PartsGrossAndVolumeOpportunity' ||
                        history.location.pathname == '/CPELROpportunity' ||
                        (history.location.pathname == '/GraphDetailsView' &&
                          (history.location.search == '?chartId=931' ||
                            history.location.search == '?chartId=921' ||
                            history.location.search == '?chartId=926')) ? (
                          // ||
                          // (history.location.pathname == '/GraphDetailsView' &&
                          //   (history.location.search == '?chartId=931' ||
                          //     history.location.search == '?chartId=921' ||
                          //     history.location.search == '?chartId=926' ||
                          //     (Number(
                          //       history.location.search.split('?chartId=').pop()
                          //     ) >= 1243 &&
                          //       Number(
                          //         history.location.search.split('?chartId=').pop()
                          //       ) <= 1248) ||
                          //     (Number(
                          //       history.location.search.split('?chartId=').pop()
                          //     ) >= 1253 &&
                          //       Number(
                          //         history.location.search.split('?chartId=').pop()
                          //       ) <= 1258)))

                          <Radio
                            checked={
                              selected.indexOf('All') > -1 ||
                              value.indexOf('All') > -1
                                ? value.indexOf(option) > -1
                                : value.some(r => selected.includes(r)) ||
                                  (!value.some(r => selected.includes(r)) &&
                                    value.length >= 1)
                                ? value.indexOf(option) > -1
                                : value
                                    .concat(selected.toString().split(','))
                                    .indexOf(option) > -1
                            }
                            // checked={value.indexOf(option) > -1}
                            color="primary"
                            onClick={handleOptionToggle}
                            value={option}
                            style={{
                              width: 36,
                              height: 36,
                              transform: 'scale(.75)'
                            }}
                            // disabled={(keycloak.realmAccess.roles.includes('user') == true ? true : false )}
                          />
                        ) : (
                          <Checkbox
                            checked={
                              selected.indexOf('All') > -1 ||
                              value.indexOf('All') > -1
                                ? value.indexOf(option) > -1
                                : value.some(r => selected.includes(r)) ||
                                  (!value.some(r => selected.includes(r)) &&
                                    value.length >= 1)
                                ? value.indexOf(option) > -1
                                : value
                                    .concat(selected.toString().split(','))
                                    .indexOf(option) > -1
                            }
                            // checked={value.indexOf(option) > -1}
                            color="primary"
                            onClick={handleOptionToggle}
                            value={option}
                            style={{
                              width: 36,
                              height: 36,
                              transform: 'scale(.75)'
                            }}
                            // disabled={(keycloak.realmAccess.roles.includes('user') == true ? true : false )}
                          />
                        )
                      }
                      label={
                        (advisorNames[index] == null ||
                        advisorNames[index] == ''
                          ? ''
                          : advisorNames[index].split('-status-')[0]) +
                        (advisorNames[index] == 'All'
                          ? ''
                          : ' [' + option + ']')
                      }
                    />
                  </MenuItem>
                ))}
                <Divider variant="fullWidth" style={{ width: '100%' }} />
              </CardContent>
            </ScrollBar>
          ) : (
            ''
          )}
          {history.location.pathname == '/Discounts' ? (
            <Typography className={classes.noadvisorsection}>
              {' '}
              Not Available on this dashboard!
            </Typography>
          ) : (
            <CardActions style={{ padding: 5, display: 'inline-flex' }}>
              {/* <Tooltip title="Edit Service Advisor">
              <IconButton
                size="small"
                margin="dense"
                color="inherit"
               
                onClick={addServiceAdvisor}
              >
                <EditOutlinedIcon color="inherit" onClick={addServiceAdvisor} />
              </IconButton>
            </Tooltip> */}
              <Button
                size="small"
                className={classes.active}
                color="inherit"
                variant="text"
                style={{
                  textTransform: 'none'
                }}
                onClick={addServiceAdvisor}
                disabled={
                  // keycloak.realmAccess.roles.includes('user') == true ||
                  keycloak.realmAccess.roles.includes('client') == true
                    ? true
                    : history.location.pathname == '/CPOverview' ||
                      history.location.pathname == '/ROCalendar' ||
                      history.location.pathname == '/CPLaborOverview' ||
                      history.location.pathname == '/CPPartsOverview' ||
                      history.location.pathname == '/SpecialMetrics' ||
                      history.location.pathname == '/RevenueSummary' ||
                      history.location.pathname ===
                        '/RevenueSummaryDrilldown' ||
                      // history.location.pathname == '/MyFavorites' ||
                      history.location.pathname == '/LaborItemization' ||
                      history.location.pathname == '/PartsItemization' ||
                      history.location.pathname == '/Home' ||
                      history.location.pathname == '/KPIReportComparative' ||
                      history.location.pathname == '/2.4.0/Home' ||
                      history.location.pathname == '/KpiGraphics' ||
                      history.location.pathname ==
                        '/ServiceAdvisorPerformance' ||
                      history.location.pathname == '/LaborMisses' ||
                      history.location.pathname == '/MPIStats' ||
                      (history.location.pathname == '/AnalyzeData' &&
                        history.location.search == '?OneLine') ||
                      history.location.pathname == '/PartsMisses' ||
                      history.location.pathname == '/KpiReport' ||
                      history.location.pathname ==
                        '/KPIReportTechComparative' ||
                      history.location.pathname ==
                        '/LaborGrossAndVolumeOpportunity' ||
                      history.location.pathname ==
                        '/PartsGrossAndVolumeOpportunity' ||
                      history.location.pathname == '/CPELROpportunity' ||
                      history.location.pathname == '/JobCountGrid' ||
                      history.location.pathname == '/TrendReport' ||
                      history.location.prevPath ==
                        '/GraphDetailsView?chartId=1090' ||
                      history.location.prevPath ==
                        '/GraphDetailsView?chartId=1096' ||
                      (history.location.pathname == '/LaborWorkMixAnalysis' &&
                        document.getElementById('Work mix tab') &&
                        document.getElementById('Work mix tab').attributes[4]
                          .value == 'true') ||
                      (history.location.pathname == '/PartsWorkMixAnalysis' &&
                        document.getElementById('Work mix tab') &&
                        document.getElementById('Work mix tab').attributes[4]
                          .value == 'true') ||
                      (history.location.pathname == '/GraphDetailsView' &&
                        (history.location.search == '?chartId=1090' ||
                          history.location.search == '?chartId=1096')) ||
                      (history.location.pathname == '/GraphDetailsView' &&
                        Number(
                          history.location.search.split('?chartId=').pop()
                        ) >= 1243 &&
                        Number(
                          history.location.search.split('?chartId=').pop()
                        ) <= 1248) ||
                      (Number(
                        history.location.search.split('?chartId=').pop()
                      ) >= 1253 &&
                        Number(
                          history.location.search.split('?chartId=').pop()
                        ) <= 1258) ||
                      isAdvisorenabledCharts(session.currentPath) === true
                    ? false
                    : history.location.prevPath == '/LaborWorkMixAnalysis' ||
                      history.location.prevPath == '/PartsWorkMixAnalysis'
                    ? false
                    : true
                }
              >
                <EditOutlinedIcon color="inherit" />
                Edit
              </Button>
              <Button
                style={{
                  textTransform: 'none',
                  float: 'right',
                  marginLeft: '75px'
                }}
                color="primary"
                disabled={
                  // (keycloak.realmAccess.roles.includes('user')) == true ?
                  // true :
                  history.location.pathname == '/CPOverview' ||
                  history.location.pathname == '/ROCalendar' ||
                  history.location.pathname == '/CPLaborOverview' ||
                  history.location.pathname == '/CPPartsOverview' ||
                  history.location.pathname == '/SpecialMetrics' ||
                  history.location.pathname == '/RevenueSummary' ||
                  history.location.pathname === '/RevenueSummaryDrilldown' ||
                  //   history.location.pathname == '/MyFavorites' ||
                  history.location.pathname == '/ServiceAdvisorPerformance' ||
                  history.location.pathname == '/LaborItemization' ||
                  history.location.pathname == '/PartsItemization' ||
                  history.location.pathname == '/Home' ||
                  history.location.pathname == '/KPIReportComparative' ||
                  history.location.pathname == '/2.4.0/Home' ||
                  history.location.pathname == '/KpiGraphics' ||
                  history.location.pathname == '/LaborMisses' ||
                  history.location.pathname == '/MPIStats' ||
                  (history.location.pathname == '/AnalyzeData' &&
                    history.location.search == '?OneLine') ||
                  history.location.pathname == '/PartsMisses' ||
                  history.location.pathname == '/KpiReport' ||
                  history.location.pathname == '/KPIReportTechComparative' ||
                  history.location.pathname ==
                    '/LaborGrossAndVolumeOpportunity' ||
                  history.location.pathname ==
                    '/PartsGrossAndVolumeOpportunity' ||
                  history.location.pathname == '/CPELROpportunity' ||
                  history.location.pathname == '/JobCountGrid' ||
                  history.location.pathname == '/TrendReport' ||
                  history.location.prevPath ==
                    '/GraphDetailsView?chartId=1090' ||
                  history.location.prevPath ==
                    '/GraphDetailsView?chartId=1096' ||
                  (history.location.pathname == '/LaborWorkMixAnalysis' &&
                    document.getElementById('Work mix tab') &&
                    document.getElementById('Work mix tab').attributes[4]
                      .value == 'true') ||
                  (history.location.pathname == '/PartsWorkMixAnalysis' &&
                    document.getElementById('Work mix tab') &&
                    document.getElementById('Work mix tab').attributes[4]
                      .value == 'true') ||
                  (history.location.pathname == '/GraphDetailsView' &&
                    (history.location.search == '?chartId=1090' ||
                      history.location.search == '?chartId=1096')) ||
                  (history.location.pathname == '/GraphDetailsView' &&
                    Number(history.location.search.split('?chartId=').pop()) >=
                      1243 &&
                    Number(history.location.search.split('?chartId=').pop()) <=
                      1248) ||
                  (Number(history.location.search.split('?chartId=').pop()) >=
                    1253 &&
                    Number(history.location.search.split('?chartId=').pop()) <=
                      1258) ||
                  isAdvisorenabledCharts(session.currentPath) === true
                    ? false
                    : history.location.prevPath == '/LaborWorkMixAnalysis' ||
                      history.location.prevPath == '/PartsWorkMixAnalysis'
                    ? false
                    : true
                }
                //component={RouterLink}
                size="small"
                onClick={val => handleOptionToggleApply(val)}
                variant="text"
              >
                Apply Filter
                {/* <DoneIcon style={{ fontSize: 14 }} /> */}
              </Button>
            </CardActions>
          )}
        </Card>
      </Menu>
    </>
  );
}

MultiSelect.propTypes = {
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  options: PropTypes.array.isRequired,
  value: PropTypes.array.isRequired,
  onControlSelect: PropTypes.func
};

export default MultiSelect;
