import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import ButtonBase from '@material-ui/core/ButtonBase';
import LocationCityIcon from '@material-ui/icons/LocationCity';
import BuildIcon from '@material-ui/icons/Build';

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(1),
    //marginRight: 'auto',
    maxWidth: 500
  },
  image: {
    width: 100,
    height: 75,
    cursor: 'default',
    position: 'absolute'
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '50%'
  },
  container: {
    alignItems: 'center'
    // '@media (max-width: 1920px)': {
    //   width: 250
    //   //fontSize: 25,
    //   //marginLeft: '23%'
    // },

    // '@media (max-width: 1440px)': {
    //   width: 220
    //   //fontSize: 25,
    //   //marginLeft: '23%'
    // },

    // '@media (max-width: 1280px)': {
    //   width: 200
    //   //marginLeft: '25%'
    // },
    // '@media (min-width: 2304px)': {
    //   width: 350
    //   //fontSize: 30,
    //   // marginLeft: '45%'
    // }
  },
  titleLabel: {
    display: 'flex',
    fontSize: 15,
    cursor: 'default'
  },
  valLabel: {
    display: 'flex',
    cursor: 'default',
    color: '#FFF'
  },
  dataItem: {
    marginLeft: 20
  }
}));

export default function DataCard(props) {
  const classes = useStyles();

  return (
    <Paper
      className={classes.paper}
      style={{
        backgroundColor: props.color,
        border: '0px',
        height: props.height,
        position: 'relative'
      }}
    >
      <Grid
        container
        className={classes.container}
        style={{ marginTop: props.marginTopIcon }}
      >
        {/* <div> */}
        <div className={classes.image}>
          <img
            className={classes.icon}
            alt="Fixed Ops"
            src={`/images/kpis/${props.icon}.png`}
          />
        </div>
        {/* </div> */}
        <div
          item
          className={'flatRateHrsBlock'}
          style={{ marginTop: props.marginToph6 }}
        >
          {/* <Typography
            variant="h5"
            className={classes.valLabel}
            style={{ marginLeft: props.margin, float: props.float, marginTop: props.marginTop }}
          > */}
          <span className={'flatRateHrsBlockValue'}>{props.value}</span>
          {/* </Typography> */}
          {/* <Typography
            gutterBottom
            variant="subtitle1"
            className={classes.titleLabel}
            style={{ color: '#FFF' }}
          > */}
          <span className={'flatRateHrsBlockTitle'}>{props.title}</span>
          {/* </Typography> */}
        </div>
        {/* <Grid item xs={2}>
          <Typography
            variant="h5"
            className={classes.valLabel}
            style={{ marginLeft: props.margin, float: props.float, marginTop: props.marginTop }}
          >
            {props.value}
          </Typography>
        </Grid> */}
      </Grid>
    </Paper>
  );
}
