import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';

import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';

import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import DashboardLineRenderer from 'src/components/charts/DashboardLineRenderer';
import { Grid } from '@material-ui/core';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
import TechBarChartRenderer from 'src/views/AnalyzeData/TechEfficiency/BarChartRenderer';
import TechBarChartRendererPostGraphile from 'src/views/AnalyzeData/TechEfficiency/BarChartRendererPostgraphile';
import TechBarColumnRenderer from 'src/views/AnalyzeData/TechEfficiency/ColumnRenderer';
import DiscountColumnRenderer from 'src/views/Discounts/DiscountColumnRenderer';
import OpCategoryChartRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/OpCategoryChartRenderer';
import AdvisorColumnRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/ColumnRenderer';
import AdvisorBarChartRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/AdvisorChartRenderer';
import BarChartRenderer from 'src/views/AnalyzeData/WorkMixCharts/BarChartRenderer';
import CPPartsMarkupVsPartsCost from 'src/components/charts/CPPartsMarkupVsPartsCost';
import CPMovingPartsMarkup from 'src/components/charts/CPMovingPartsMarkup';
import CPELRVsLaborSoldHours from 'src/components/charts/CPELRVsLaborSoldHours';
import { getDataGridConfiguration, getDbdName } from 'src/utils/Utils';
import ColumnRenderer from 'src/views/AnalyzeData/ComparisonCharts/ColumnRenderer';
import AddonSAColumnRenderer from 'src/views/AddOns/ServiceAdvisor/ColumnRenderer';
import AddonColumnRenderer from 'src/views/AnalyzeData/ComparisonCharts/ColumnRenderer';
import OpportunityCharts from 'src/components/charts/OpportunityCharts';
import CPMovingELR from 'src/components/charts/CPMovingELR';
import HasuraDashboardLineRenderer from 'src/components/charts/HasuraDashboardLineRenderer';
import AdvisorOpportunityCharts from 'src/components/charts/AdvisorOpportunityCharts';

import { getDashboardGraphQuery } from 'src/components/DashboardGraphQuery';
import 'src/styles.css';

var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {},
  // backDrop: {
  //   backdropFilter: 'blur(3px)',
  //   backgroundColor: 'rgba(0, 0, 0, 0)',
  //   margin: 20
  // },
  // scrollPaper: { backgroundColor: '#fff' },
  menuItem: {
    padding: 0
  },
  cardControl: {
    padding: 0
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),
    width: '100%',
    color: '#FFF',
    margin: 0
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  paper: {
    '@media (max-width: 1920px)': {
      maxWidth: '1150px',
      maxHeight: '700px'
    },

    '@media (max-width: 1440px)': {
      maxWidth: '975px',
      maxHeight: '610px'
    },

    '@media (max-width: 1280px)': {
      maxWidth: '975px',
      maxHeight: '600px'
    },
    '@media (min-width: 2304px)': {
      maxWidth: '1350px',
      maxHeight: '900px'
    }
  },
  paperTranches: {
    maxWidth: '1250px',
    maxHeight: '800px'
  },
  paperItemization: {
    maxWidth: '100%',
    maxHeight: '1400px'
  },
  flexItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  paperBar: {
    // maxWidth: '1300px',
    maxWidth: '100%',
    maxHeight: '1400px'
    // marginLeft: '17%',
    // marginTop: '8%',
    // marginRight: '0%'
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function ChartDialog({
  open,
  chartId,
  filters,
  realm,
  chartType,
  handlePopupClose,
  isPartsCharts,
  advisor,
  advisorName,
  parentCallback,
  tech,
  techName,
  datatype,
  checkEmpty,
  mon1,
  mon2,
  tabSelection,
  handleResetClicked,
  chartData,
  completeData,
  showCurrentMonth,
  timeZone,
  headerClick,
  selectedChartId,
  selected,
  handleHighlight,
  selectedPoints,
  allData,
  metaArr,
  chart,
  LegendClicked,
  legendArr,
  legendItemClicked,
  enabledlegendItems,
  setActions,
  dbdName,
  //chartData,
  isDataLoaded
}) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(open);
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    console.log('chartIds', chartId, chartType, chartData, allData);
    setOpenDialog(open);
  }, [open, chartId]);
  const handleClose = () => {
    setOpenDialog(false);
    handlePopupClose(1);
    // dispatch(setZoomChartId(''));
    // dispatch(setZoomStatus(false));
  };

  const removeFav = value => {
    setIsLoading(false);
  };
  var dashboardName = getDbdName(chartId);
  return (
    <Dialog
      fullWidth
      // TransitionComponent={Transition}
      transition={Fade}
      classes={{
        paper:
          chartType == 'line' || chartType == 'barChartRenderer'
            ? classes.paper
            : chartType == 'partsMarkup'
            ? classes.paperTranches
            : chartType == 'itemization' || chartType == 'partsItemization'
            ? classes.paperItemization
            : classes.paperBar

        // paperFullScreen: classes.paperFullScreen
      }}
      BackdropProps={{
        classes: {
          root: classes.backDrop
        }
      }}
      //maxWidth="xl"
      //style={{ maxWidth: 900, maxHeight: 700 }}
      open={openDialog}
      onClose={handleClose}
    >
      {/* <DialogTitle id="max-width-dialog-title" style={{ marginBottom: 20 }}>
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle> */}
      <DialogContent style={{ marginBottom: 20 }}>
        <Box
          noValidate
          component="form"
          className={
            chartType == 'itemization' || chartType == 'partsItemization'
              ? classes.flexItem
              : ''
          }
          sx={{
            display: 'flex',
            flexDirection: 'column',
            m: 'auto'
            //width: 'fit-content'
          }}
        >
          {// chartType == 'line' &&
          // dashboardName != 'CP Overview' &&
          // dashboardName != 'CP Labor Overview' ? (
          //   <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
          //     <DashboardLineRenderer
          //       filterCharts={filters}
          //       removeFav={removeFav}
          //       handleClose={handleClose}
          //       handleClosePopup={handleClose}
          //       chartId={chartId}
          //       showCurrentMonth={showCurrentMonth}
          //       headerClick={headerClick}
          //       selected={selected}
          //       selectedChartId={selectedChartId}
          //       handleHighlightContainer={handleHighlight}
          //     />
          //   </Grid>
          // ) :

          chartType == 'line' ? (
            <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
              <DashboardLineRenderer
                filterCharts={filters}
                chartId={chartId}
                isFrom={'retail'}
                type={'popup'}
                realm={realm}
                showCurrentMonth={showCurrentMonth}
                handleClose={handleClose}
                //   backTobutton={handleBackToButton}
                handleHighlight={handleHighlight}
                selected={selected}
                selectedChartId={selectedChartId}
                removeFavourite={removeFav}
                headerClick={headerClick}
                chartData={
                  typeof chartData != 'undefined'
                    ? chartData.filter(chart => chart.chartId == chartId)
                    : []
                }
                isDataLoaded={isDataLoaded}
                dbdName={dbdName}
              />
            </Grid>
          ) : chartType == 'bar' ? (
            <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
              <BarChartRenderer
                title={
                  typeof chartId != 'undefined' ? getChartName(chartId) : ''
                }
                parentCallback={parentCallback}
                removeFav={removeFav}
                handleClosePopup={handleClose}
                chartId={chartId}
                type={'popup'}
                realm={realm}
              />
            </Grid>
          ) : chartType == 'comparison' ? (
            <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
              <ColumnRenderer
                month1={mon1}
                month2={mon2}
                datatype={''}
                type={'popup'}
                isFrom={'comparisonChartsGrid'}
                handleClosePopup={handleClose}
                // datatype={item.chartName}
                isPartsCharts={isPartsCharts}
                chartId={chartId}
                removeFav={removeFav}
                session={session}
                parentCallback={parentCallback}
                workmixChartData={chartData}
              />
            </Grid>
          ) : chartType == 'serviceAdvisorComparison' ? (
            <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
              <AdvisorColumnRenderer
                month1={mon1}
                month2={mon2}
                datatype={''}
                type={'popup'}
                handleClosePopup={handleClose}
                // datatype={item.chartName}
                checkEmpty={checkEmpty}
                isPartsCharts={isPartsCharts}
                chartId={chartId}
                removeFav={removeFav}
                parentCallback={parentCallback}
                advisorChartData={chartData}
                isFrom={'ServiceAdvisorEfficiency'}
              />
            </Grid>
          ) : chartType == 'opCategoryChartRenderer' ? (
            <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
              <OpCategoryChartRenderer
                month1={mon1}
                datatype={''}
                type={'popup'}
                handleClosePopup={handleClose}
                chartId={chartId}
                // datatype={item.chartName}
                checkEmpty={checkEmpty}
                isPartsCharts={isPartsCharts}
                removeFav={removeFav}
                parentCallback={parentCallback}
                advisorChartData={chartData}
                isFrom={'ServiceAdvisorEfficiency'}
              />
            </Grid>
          ) : chartType == 'techBarChartRenderer' ? (
            <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
              <TechBarChartRendererPostGraphile
                chartId={chartId}
                chartTitle={
                  typeof chartId != 'undefined' ? getChartName(chartId) : ''
                }
                tech={tech}
                techName={techName}
                handleClosePopup={handleClose}
                checkEmpty={checkEmpty}
                type={'popup'}
                realm={realm}
                parentCallback={parentCallback}
                tabSelection={'one'}
                mainTabSelection={'seven'}
                removeFav={removeFav}
              />
            </Grid>
          ) : chartType == 'techComparison' ? (
            <TechBarColumnRenderer
              chartId={chartId}
              chartTitle={
                typeof chartId != 'undefined' ? getChartName(chartId) : ''
              }
              handleClosePopup={handleClose}
              tech={tech}
              month1={mon1}
              month2={mon2}
              datatype={datatype}
              checkEmpty={checkEmpty}
              type={'popup'}
              realm={realm}
              parentCallback={parentCallback}
              removeFav={removeFav}
              session={session}
              techChartData={chartData}
              parent={'techefficiency'}
            />
          ) : chartType == 'barChartRenderer' ? (
            <DashboardBarRenderer
              removeFav={removeFav}
              handleClosePopup={handleClose}
              chartId={parseInt(chartId)}
              type={'popup'}
              isFrom={'retail'}
              parentFrom={'detail_page'}
              realm={realm}
              headerClick={headerClick}
              selected={selected}
              chartData={
                typeof allData != 'undefined'
                  ? allData.filter(
                      chart => chart.chartId == parseInt(chartId)
                    )[0]
                  : []
              }
              allData={allData}
              selectedChartId={selectedChartId}
              handleHighlightContainer={handleHighlight}
            />
          ) : chartType == 'discounts' ? (
            <DiscountColumnRenderer
              datatype={datatype}
              handleClosePopup={handleClose}
              month1={mon1}
              month2={mon2}
              type={'popup'}
              chartId={chartId}
              removeFav={removeFav}
            />
          ) : chartType == 'movingELR' ? (
            <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
              <CPMovingELR
                handleClosePopup={handleClose}
                type={'popup'}
                chartId={1089}
                removeFav={removeFav}
              />
            </Grid>
          ) : chartType == 'itemization' ? (
            <div
              className={clsx(
                classes.container,
                'diagram-section',
                'container-popup'
              )}
            >
              <CPELRVsLaborSoldHours
                handleClosePopup={handleClose}
                type={'popup'}
                chartId={1090}
                removeFav={removeFav}
                tabSelection={tabSelection}
                handleResetClicked={handleResetClicked}
                data={chartData}
                completeData={completeData}
                timeZone={timeZone}
                selectedPoints={selectedPoints}
                allData={allData}
                metaArr={metaArr}
                chart={chart}
                open={open}
                LegendClicked={LegendClicked}
                legendArr={legendArr}
                legendItemClicked={legendItemClicked}
                enabledlegendItems={enabledlegendItems}
              />
            </div>
          ) : chartType == 'partsMarkup' ? (
            <CPMovingPartsMarkup
              handleClosePopup={handleClose}
              type={'popup'}
              chartId={1090}
              removeFav={removeFav}
            />
          ) : chartType == 'partsItemization' ? (
            <div
              className={clsx(
                classes.container,
                'diagram-section',
                'container-popup'
              )}
            >
              <CPPartsMarkupVsPartsCost
                handleClosePopup={handleClose}
                type={'popup'}
                chartId={1096}
                removeFav={removeFav}
                tabSelection={tabSelection}
                handleResetClicked={handleResetClicked}
                data={chartData}
                completeData={completeData}
                timeZone={timeZone}
                selectedPoints={selectedPoints}
                allData={allData}
                metaArr={metaArr}
                chart={chart}
                open={open}
                LegendClicked={LegendClicked}
                legendArr={legendArr}
                legendItemClicked={legendItemClicked}
                enabledlegendItems={enabledlegendItems}
              />
            </div>
          ) : chartType == 'opportunityCharts' ? (
            <AdvisorOpportunityCharts
              title={typeof chartId != 'undefined' ? getChartName(chartId) : ''}
              handleClose={handleClose}
              type={'popup'}
              chartId={chartId}
              removeFav={removeFav}
              showTopBar={true}
            />
          ) : chartType == 'addonComparison' ? (
            datatype === 'addonvsnonaddonrevenue%' ||
            datatype === 'addonrevenue' ? (
              <Grid
                item
                xs={12}
                justify="flex-start"
                style={{ padding: '5px' }}
              >
                <AddonColumnRenderer
                  month1={mon1}
                  month2={mon2}
                  datatype={datatype}
                  type={'popup'}
                  handleClosePopup={handleClose}
                  // datatype={item.chartName}
                  isPartsCharts={isPartsCharts}
                  chartId={chartId}
                  removeFav={removeFav}
                />
              </Grid>
            ) : (
              <AddonSAColumnRenderer
                month1={mon1}
                month2={mon2}
                datatype={datatype}
                type={'popup'}
                handleClosePopup={handleClose}
                // datatype={item.chartName}
                isPartsCharts={isPartsCharts}
                chartId={chartId}
                removeFav={removeFav}
              />
            )
          ) : (
            chartType == 'advisorCharts' && (
              <Grid
                item
                xs={12}
                justify="flex-start"
                style={{ padding: '5px' }}
              >
                <AdvisorBarChartRenderer
                  title={
                    typeof chartId != 'undefined' ? getChartName(chartId) : ''
                  }
                  removeFav={removeFav}
                  chartId={chartId}
                  type={'popup'}
                  handleClosePopup={handleClose}
                  advisor={advisor}
                  advisorName={advisorName}
                  checkEmpty={checkEmpty}
                  realm={realm}
                  parentCallback={parentCallback}
                  session={session}
                  serviceAdvisor={session.serviceAdvisor}
                />
              </Grid>
            )
          )}
        </Box>
      </DialogContent>
    </Dialog>
  );
}

export default ChartDialog;
