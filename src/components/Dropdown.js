import {
  <PERSON><PERSON>,
  Checkbox,
  FormControlLabel,
  Menu,
  MenuItem,
  Card,
  CardContent,
  CardActions,
  CardHeader,
  Tooltip,
  IconButton,
  Divider,
  Typography
} from '@material-ui/core';
import ArrowDropDownIcon from '@material-ui/icons/ArrowDropDown';
import DoneIcon from '@material-ui/icons/Done';
import clsx from 'clsx';
import EditOutlinedIcon from '@material-ui/icons/EditOutlined';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import React, { useRef, useState, useEffect } from 'react';
import ScrollBar from 'react-perfect-scrollbar';
import { useHistory, useLocation } from 'react-router';

import {
  getAdvisorenabledCharts,
  isAdvisorenabledCharts
} from 'src/utils/Utils';
import { useDispatch, useSelector } from 'react-redux';
import SwapHorizontalCircleOutlinedIcon from '@material-ui/icons/SwapHorizontalCircleOutlined';
import { getKpiScoreCardDataStatus } from 'src/utils/hasuraServices';
import { setKpiToggle, setKpiHomeToggle } from 'src/actions';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

const useStyles = makeStyles(theme => ({
  root: {},
  menuItem: {
    padding: 4,
    paddingLeft: 16,
    paddingRight: 16
  },
  menuadvi: {
    padding: 0
  },
  labelRoot: {
    color: '#ccc'
  },
  noadvisorsection: {
    fontSize: 12
  },
  cardControl: {
    padding: 0,
    overflowX: 'hidden',
    paddingBottom: '0px !important'
  },
  selectBox: {
    // marginLeft: 8,
    color: '#fff',
    textTransform: 'none',
    fontSize: 12,
    //marginLeft: '-5px'
    minWidth: 30,
    position: 'relative',
    bottom: 2
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),

    width: '100%',
    color: '#FFF',
    margin: 0
  },
  active: {
    color: theme.palette.primary.main
  }
}));

function Dropdown({
  label,
  options,
  value,
  onChange,
  advisorNames,
  selected,
  keycloak,
  storeName
}) {
  const classes = useStyles();
  const anchorRef = useRef(null);
  const [openMenu, setOpenMenu] = useState(false);
  const checkedValues = [];
  const history = useHistory();
  const session = useSelector(state => state.session);
  const [isApplied, setApply] = useState(false);
  const handleMenuOpen = () => {
    setOpenMenu(true);
  };
  const dispatch = useDispatch();

  const handleMenuClose = isApply => {
    setOpenMenu(false);
    setApply(isApply);
    value.sort();
    selected.sort();
    // if (isApply != true && !lodash.isEqual(value, selected)) {
    //   while (value.length > 0) {
    //     value.pop();
    //   }
    //   if (selected.indexOf('All') > -1) {
    //     value.push('All');
    //   } else {
    //     let sortedArray = [];
    //     options.map(first => {
    //       sortedArray[selected.findIndex(def => def === first)] = first;
    //     });
    //     value = sortedArray.filter(v => v);
    //     setApply(true);

    //     //  value.push(value.concat((selected.toString()).split(',')) )
    //   }
    // }
    setOpenMenu(false);
  };
  const handleOptionToggleApply = val => {
    if (onChange) {
      handleMenuClose(true);
      onChange(value, 'apply');
    }
  };
  const handleOptionToggle = event => {
    let newValue = value.length > 0 ? [...value] : selected;

    if (event.target.checked) {
      //checked
      if (event.target.value == 'All') {
        newValue = ['All'];
      } else {
        newValue = newValue.filter(item => item !== 'All');
        newValue.push(event.target.value);
      }
    } else {
      //unchecked
      if (event.target.value == 'All') {
        newValue = ['All'];
      } else {
        // newValue = newValue.filter(item => item !== 'All');
        newValue = newValue.filter(item => item !== event.target.value);
        if (newValue.length === 0) {
          newValue = ['All'];
        }
      }
    }
    checkedValues.push(newValue);
    if (onChange) {
      onChange(newValue, 'userselection');
    }

    // if (onChange) {
    //   handleMenuClose();
    //   onChange(newValue);
    // }
  };
  const addServiceAdvisor = () => {
    history.push('/ServiceAdvisor');
    handleMenuClose(true);
  };
  const onChanged = event => {
    console.log('event=', event.target);
    if (event.target.innerText != selected) {
      onChange(event.target.innerText, 'login');
      localStorage.setItem('showCurrentMonth', true);
      localStorage.setItem('storeChange', true);
      // let toggleSet = ['MTD', 'YESDT', 'DBYESDT', 'LWEEK'];
      let toggleSet = ['MTD'];
      toggleSet.map(item => {
        getKpiScoreCardDataStatus(item, result => {
          if (result.data.statelessDbdKpiScorecardGetDataStatus) {
            let data =
              result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat;

            if (item == 'MTD') {
              localStorage.setItem(
                'kpiDataStatus',
                result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
              );
            }
            // if (item == 'YESDT') {
            //   localStorage.setItem(
            //     'kpiDataStatusYesdt',
            //     result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
            //   );
            // }
            // if (item == 'DBYESDT') {
            //   localStorage.setItem(
            //     'kpiDataStatusDbYesdt',
            //     result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
            //   );
            // }
            // if (item == 'LWEEK') {
            //   localStorage.setItem(
            //     'kpiDataStatusDbLweek',
            //     result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
            //   );
            // }
            dispatch(setKpiToggle(data == 1 ? 'MTD' : 'LMONTH'));
            dispatch(setKpiHomeToggle(data == 1 ? 'MTD' : 'LMONTH'));
          }
        });
      });
    }
    setOpenMenu(false);
  };

  return (
    <>
      <Button
        className={classes.selectBox}
        onClick={handleMenuOpen}
        ref={anchorRef}
      >
        <Tooltip title="Change Store">
          <SwapHorizontalCircleOutlinedIcon />
        </Tooltip>
      </Button>
      <Menu
        anchorEl={anchorRef.current}
        className={clsx(classes.menu, 'menuadvi .MuiMenu-paper')}
        onClose={handleMenuClose}
        open={openMenu}
        elevation={1}
        PaperProps={{
          style: {
            width: 'auto'
          }
        }}
      >
        <Card raised={false} elevation={0} className={classes.cardControl}>
          {/* {history.location.pathname != '/Discounts' ? ( */}
          <ScrollBar
            style={{
              height: storeName.length > 10 ? '300px' : 'auto',
              overflowX: 'unset !important'
            }}
          >
            <CardContent
              style={{ height: storeName.length > 10 ? '300px' : 'auto' }}
              className={classes.cardControl}
            >
              {storeName.map((option, i) => (
                <>
                  {(option.split('-').length > 3
                    ? option.replace(/.*?-/, '')
                    : option.split('-').length > 2
                    ? option.split('-')[option.split('-').length - 2] +
                      '-' +
                      option.split('-')[option.split('-').length - 1]
                    : option.split('-')[1]) == selected ? (
                    <MenuItem
                      onClick={onChanged}
                      className={classes.menuItem}
                      // key={option}
                      selected={true}
                      key={
                        storeName[i].split('-').length > 3
                          ? storeName[i].replace(/.*?-/, '')
                          : storeName[i].split('-').length > 2
                          ? storeName[i].split('-')[
                              storeName[i].split('-').length - 2
                            ] +
                            '-' +
                            storeName[i].split('-')[
                              storeName[i].split('-').length - 1
                            ]
                          : storeName[i].split('-')[1]
                      }
                      value={
                        storeName[i].split('-').length > 3
                          ? storeName[i].replace(/.*?-/, '')
                          : storeName[i].split('-').length > 2
                          ? storeName[i].split('-')[
                              storeName[i].split('-').length - 2
                            ] +
                            '-' +
                            storeName[i].split('-')[
                              storeName[i].split('-').length - 1
                            ]
                          : storeName[i].split('-')[1]
                      }
                    >
                      {option.split('-').length > 3
                        ? option.replace(/.*?-/, '')
                        : option.split('-').length > 2
                        ? option.split('-')[option.split('-').length - 2] +
                          '-' +
                          option.split('-')[option.split('-').length - 1]
                        : option.split('-')[1]}
                    </MenuItem>
                  ) : (
                    <MenuItem
                      onClick={onChanged}
                      className={classes.menuItem}
                      key={option}
                    >
                      {option.split('-').length > 3
                        ? option.replace(/.*?-/, '')
                        : option.split('-').length > 2
                        ? option.split('-')[option.split('-').length - 2] +
                          '-' +
                          option.split('-')[option.split('-').length - 1]
                        : option.split('-')[1]}
                    </MenuItem>
                  )}
                </>
                // disabled={
                //   history.location.pathname === '/CPOverview' ||
                //   history.location.pathname === '/CPLaborOverview' ||
                //   history.location.pathname === '/CPPartsOverview' ||
                //   history.location.pathname === '/SpecialMetrics' ||
                //   history.location.pathname === '/MyFavorites' ||
                //   history.location.pathname == '/LaborItemization' ||
                //   history.location.pathname == '/PartsItemization' ||
                //   history.location.pathname == '/Home' ||
                //   history.location.pathname == '/ServiceAdvisorPerformance'||
                //   isAdvisorenabledCharts(session.currentPath) === true
                //     ? false
                //     : true
                // }

                // style={{
                //   backgroundColor:
                //     advisorNames[index] &&
                //     advisorNames[index].split('-status-')[1] == 0
                //       ? Dealer === 'Armatus'
                //         ? '#ddeaf4'
                //         : '#F4E1E7'
                //       : '',
                //   overflowX: 'hidden'
                // }}
              ))}
              {session.versionFlag == 'TRUE' && (
                <MenuItem
                  value="All"
                  onClick={onChanged}
                  selected={
                    localStorage.getItem('storeSelected') == 'All Stores'
                      ? true
                      : false
                  }
                >
                  All Stores
                </MenuItem>
              )}
            </CardContent>
          </ScrollBar>
          {/* ) : ( */}
          {/* '' */}
          {/* )} */}
        </Card>
      </Menu>
    </>
  );
}

Dropdown.propTypes = {
  label: PropTypes.string.isRequired,
  onChange: PropTypes.func,
  options: PropTypes.array.isRequired,
  value: PropTypes.array.isRequired
};

export default Dropdown;
