import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import FormGroup from '@material-ui/core/FormGroup';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField
} from '@material-ui/core';
import Checkbox from '@material-ui/core/Checkbox';
import $, { param } from 'jquery';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles, withStyles } from '@material-ui/styles';
import PropTypes, { element } from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import MuiTableCell from '@material-ui/core/TableCell';
import { getEmail, sendSavedReportEmails } from 'src/utils/hasuraServices';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import FormHelperText from '@material-ui/core/FormHelperText';
import Chip from '@material-ui/core/Chip';
import _ from 'lodash';
import { isArray } from 'validate.js';

var lodash = require('lodash');
const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);
const useStyles = makeStyles(theme => ({
  root: {},
  // backDrop: {
  //   backdropFilter: 'blur(3px)',
  //   backgroundColor: 'rgba(0, 0, 0, 0)',
  //   margin: 20
  // },
  // scrollPaper: { backgroundColor: '#fff' },
  menuItem: {
    padding: 0
  },
  formControl: {
    minWidth: 110
  },
  formControl1: {
    minWidth: 160
  },
  cardControl: {
    padding: 0
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),
    fontSize: 12
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  paper: {
    '@media (max-width: 1920px)': {
      maxWidth: '1150px',
      maxHeight: '700px'
    },

    '@media (max-width: 1440px)': {
      maxWidth: '1500px',
      maxHeight: '610px'
    },

    '@media (max-width: 1280px)': {
      maxWidth: '1500px',
      maxHeight: '600px'
    },
    '@media (max-width: 2304px)': {
      maxWidth: '1350px',
      maxHeight: '900px'
    }
  },
  paperTranches: {
    maxWidth: '1250px',
    maxHeight: '800px'
  },
  paperItemization: {
    maxWidth: '100%',
    maxHeight: '1400px'
  },
  flexItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  paperBar: {
    // maxWidth: '1300px',
    maxWidth: '100%',
    maxHeight: '1400px'
    // marginLeft: '17%',
    // marginTop: '8%',
    // marginRight: '0%'
  },
  divDisplay: {
    //marginBottom: 45
  },
  divDisplayAdv: {
    //marginTop: 47
  },
  parentCell: {
    display: 'flex',
    justifyContent: 'space-around'
  },
  emailCc: {
    display: 'block'
  },
  emailBcc: {
    display: 'block'
  },
  emailCcDisable: {
    display: 'none'
  },
  emailBccDisable: {
    display: 'none'
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function EmailDialogSavedReports({
  open,
  emailListDialog,
  handlePopupClose,
  selectedRow,
  mailUsers
}) {
  let checkedData = selectedRow.visibility == 'private' ? true : false;

  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(open);
  const [emailList, setEmailList] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [selectedDay, setSelectedDay] = useState([]);
  const [selectedDate, setSelectedDate] = useState([]);
  const [selectedOptionsAdv, setSelectedOptionsAdv] = useState([]);
  const [selectedDayAdv, setSelectedDayAdv] = useState([]);
  const [selectedDateAdv, setSelectedDateAdv] = useState([]);
  const [alertMsg, setAlertMsg] = useState('');
  const [openAlert, setOpenAlert] = React.useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState('daily');
  const [selectedScheduleAdv, setSelectedScheduleAdv] = useState('daily');
  const [advisorId, setAdvisorId] = useState(null);
  const [advisor, setAdvisor] = useState(null);
  const [storeId, setStoreId] = useState(null);
  const [emailvalue, setEmailvalue] = useState('');
  const [error, setError] = useState('');
  const [errorChecked, setErrorChecked] = useState('');
  const [requiredText, setRequiredText] = useState(false);
  const [requiredSelect, setRequiredSelect] = useState(false);
  const [requiredSelectOptions, setRequiredSelectOptions] = useState(false);
  const [requiredSelectOptionsAdv, setRequiredSelectOptionsAdv] = useState(
    false
  );
  const [checked, setChecked] = useState(checkedData);
  const [checkedAdvisor, setCheckedAdvisor] = useState(false);
  const [requiredSelectAdv, setRequiredSelectAdv] = useState(false);
  const [filterDisabled, setfilterDisabled] = useState(true);
  const [deleteflg, setDeleteflg] = useState(false);
  const [rowDataPreviousAdv, setRowDataPreviousAdv] = useState([]);
  const [value, setValue] = useState([]);
  const [valueCc, setValueCc] = useState([]);
  const [valueBcc, setValueBcc] = useState([]);

  const [emailvalueCc, setEmailvalueCc] = useState('');
  const [emailvalueBcc, setEmailvalueBcc] = useState('');
  const [emailCcClicked, setEmailCcClick] = useState(false);
  const [emailBccClicked, setEmailBccClick] = useState(false);
  const [alertMsgErr, setAlertMsgErr] = useState('');
  const [openAlertErr, setOpenAlertErr] = React.useState(false);

  const [alertMesg, setAlertMesg] = useState('');
  const [alertType, setAlertType] = useState('');
  const [openAlertt, setOpenAlertt] = React.useState(false);
  useEffect(() => {
    setOpenDialog(open);
    setEmailList(emailListDialog);
    setfilterDisabled(false);
    setSelectedSchedule('daily');
    if (document.getElementById('daily')) {
      document.getElementById('daily').style.display = 'block';
    }
    setChecked(checkedData);
    setErrorChecked('');
    // if (storeId) {
    //   setDeleteflg(false);
    // }

    // handleSetEmailvalues();
  }, [open, emailList]);
  let kpiReportTypeCasing = '';
  if (selectedRow && selectedRow.kpi_report_type) {
    if (
      (selectedRow &&
        selectedRow.kpi_report_type === 'Client_Report_Card_3_Month_Total') ||
      (selectedRow &&
        selectedRow.kpi_report_type === 'Client_Report_Card_1_Month') ||
      (selectedRow && selectedRow.kpi_report_type === 'Parts_Target_Misses') ||
      (selectedRow && selectedRow.kpi_report_type === 'MPI_Stats') ||
      (selectedRow && selectedRow.kpi_report_type === 'Labor_Target_Misses') ||
      (selectedRow &&
        selectedRow.kpi_report_type === 'CP_1-Line-RO_Count_Over_60k') ||
      (selectedRow &&
        selectedRow.kpi_report_type === 'CP_1-Line-RO_Count_Under_60k')
    ) {
      var reportType = selectedRow && selectedRow.kpi_report_type;
      kpiReportTypeCasing = reportType
        .split('_')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    } else {
      const kpiReportType = selectedRow.kpi_report_type;
      kpiReportTypeCasing =
        kpiReportType.charAt(0).toUpperCase() +
        kpiReportType.slice(1).toLowerCase();
    }
  } else {
    kpiReportTypeCasing = '';
  }
  // [open, emailList]
  let days = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday'
  ];
  const getMonthtext = i => {
    if (i == 1 || i == 21 || i == 31) {
      return 'st of every month';
    }
    if (i == 2 || i == 22) {
      return 'nd of every month';
    }
    if (i == 3 || i == 23) {
      return 'rd of every month';
    } else return 'th of every month';
  };

  const displayContent = e => {
    var setindexedData;
    setSelectedSchedule(e.target.value);
    if (selectedSchedule) {
      document.getElementById(selectedSchedule).style.display = 'none';
      setRequiredSelectOptions(false);
      if (selectedSchedule == 'weekly') setSelectedDay('');
      if (selectedSchedule == 'monthly') setSelectedDate('');
      if (selectedSchedule == 'biweekly') setSelectedOptions([]);
    }
    document.getElementById(e.target.value).style.display = 'block';
    if (e.target.value) setRequiredSelect('');
  };

  const displayWeekly = e => {
    setSelectedDay(e.target.value);
    if (e.target.value) setRequiredSelectOptions(false);
  };

  const displayMonthly = e => {
    setSelectedDate(e.target.value);
    if (e.target.value) setRequiredSelectOptions(false);
  };
  const resetStates = () => {
    setSelectedSchedule('');
    setSelectedDay('');
    setSelectedDate('');
    setSelectedOptions([]);
    setSelectedScheduleAdv('');
    setSelectedDayAdv('');
    setSelectedDateAdv('');
    setSelectedOptions([]);
    setEmailvalue('');
    setEmailvalueCc('');
    setEmailvalueBcc('');
    setError('');
    setRequiredSelect(false);
    setRequiredSelectAdv(false);
    setRequiredSelectOptions(false);
    setRequiredSelectOptionsAdv(false);
    setRequiredText(false);
    setCheckedAdvisor(false);
    // setChecked(checkedData);
    setErrorChecked('');
    setStoreId('');
    setAdvisorId('');
    setfilterDisabled(true);
    setError('');
    setValue([]);
    setValueCc([]);
    setValueBcc([]);
  };
  const handleOk = () => {
    setOpenDialog(false);
    setEmailCcClick(false);
    setEmailBccClick(false);
    handlePopupClose();
    if (document.getElementById(selectedSchedule))
      document.getElementById(selectedSchedule).style.display = 'none';
    resetStates();
  };

  const onChangeAutoComplete = (e, value) => {
    setSelectedOptions(value);
    if (value) setRequiredSelectOptions(false);
  };

  const handleSaveEmail = emailList => {
    console.log('emailList', emailvalue, value);
    var rowDataEmail = [];
    var rowDataPrevious = [];
    var listMail = [];
    let noEmail = 0;
    let noDataChange = 0;
    let scheduledonValue;
    let scheduledonValueAdv;
    var deleteSet = [];
    //store
    //  if (checked) {
    if (
      selectedSchedule &&
      selectedSchedule == 'weekly' &&
      selectedDay &&
      selectedDay.length > 0
    )
      scheduledonValue = selectedDay.toLowerCase();
    if (
      selectedSchedule &&
      selectedSchedule == 'biweekly' &&
      selectedOptions.length > 0
    )
      scheduledonValue = selectedOptions.map(e => e.toLowerCase()).toString();
    if (selectedSchedule && selectedSchedule == 'daily')
      scheduledonValue = 'all';
    if (selectedSchedule && selectedSchedule == 'monthly')
      scheduledonValue = selectedDate;

    if (selectedSchedule == undefined || selectedSchedule == '') {
      setRequiredSelect('Please choose an option');
      noEmail = 1;
    }
    if (
      (selectedSchedule && scheduledonValue == '') ||
      typeof scheduledonValue == 'undefined' ||
      (selectedSchedule == 'biweekly' && selectedOptions.length != 2)
    ) {
      setRequiredSelectOptions(true);
      noEmail = 1;
    }

    var isValid = emailvalue && validateEmail(emailvalue);
    if (!isValid) {
      noEmail = 1;
    }

    // const isValidTo = emailvalue && validateEmail(emailvalue);
    // const isValidCc = emailvalueCc && validateEmailCc(emailvalueCc);
    // const isValidBcc = emailvalueBcc && validateEmailBcc(emailvalueBcc);

    // if (!isValidTo || !isValidCc || !isValidBcc) {
    //   noEmail = 1;
    // }
    if (error) {
      noEmail = 1;
      setError(error);
    }

    if ((emailvalue == undefined || emailvalue == '') && error == '') {
      setError(false);
      setRequiredText(true);
      noEmail = 1;
    }
    let emailRecipientAll = [value];
    let emailCcAll = [valueCc];
    let emailBccAll = [valueBcc];
    if (emailvalue && !emailRecipientAll.includes(emailvalue)) {
      emailRecipientAll.push(emailvalue);
    }
    if (emailvalueCc && !emailCcAll.includes(emailvalueCc)) {
      emailCcAll.push(emailvalueCc);
    }
    if (emailvalueBcc && !emailBccAll.includes(emailvalueBcc)) {
      emailBccAll.push(emailvalueBcc);
    }
    emailRecipientAll = emailRecipientAll.filter(arr => arr.length > 0);
    emailCcAll = emailCcAll.filter(arr => arr.length > 0);
    emailBccAll = emailBccAll.filter(arr => arr.length > 0);
    rowDataEmail.push({
      recipientid:
        emailRecipientAll.length > 0
          ? emailRecipientAll.join(',')
          : emailRecipientAll,
      mailfrequency: selectedSchedule,
      scheduledon: scheduledonValue.toString(),
      mailstatus: 'active',
      id: !deleteflg && emailList.id ? emailList.id : storeId ? storeId : null,
      mailCc:
        emailCcAll.length > 0
          ? emailCcAll.join(',')
          : emailCcAll.length == 0
          ? ''
          : emailvalueCc,
      mailBcc:
        emailBccAll.length > 0
          ? emailBccAll.join(',')
          : emailBccAll.length == 0
          ? ''
          : emailvalueBcc
    });
    if (noEmail == 0 && noDataChange == 0) {
      console.log(
        'sendSavedReportEmails',
        rowDataEmail[0].recipientid,
        rowDataEmail[0].mailCc,
        rowDataEmail[0].mailBcc
      );
      sendSavedReportEmails(
        'insert',
        selectedRow.store_id,
        selectedRow.report_name,
        selectedRow.kpi_report_type,
        rowDataEmail[0].mailstatus,
        rowDataEmail[0].mailfrequency,
        rowDataEmail[0].scheduledon,
        rowDataEmail[0].recipientid,
        rowDataEmail[0].mailCc,
        rowDataEmail[0].mailBcc,
        // null,
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
              .statelessDbdKpiScorecardKpiScorecardMailJobs[0].storeId ==
            'Successfully Inserted'
          ) {
            setOpenAlert(true);
            setAlertMsg('Report email recipients saved successfully');
            setEmailCcClick(false);
            setEmailBccClick(false);
            insertEmailToEmailList(rowDataEmail[0].recipientid);
            setTimeout(() => {
              setOpenAlert(false);
              setOpenDialog(false);
              handlePopupClose();
              resetStates();
            }, 2000);
          } else {
            setOpenAlertErr(true);
            setAlertMsgErr(
              result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
                .statelessDbdKpiScorecardKpiScorecardMailJobs[0].storeId
                ? result.data
                    .statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
                    .statelessDbdKpiScorecardKpiScorecardMailJobs[0].storeId
                : 'Something went wrong'
            );
            setTimeout(() => {
              setOpenAlertErr(false);
              // setOpenDialog(false);
              // handlePopupClose();
              // resetStates();
              setEmailvalue('');
              setEmailvalueCc('');
              setEmailvalueBcc('');
              setValue([]);
              setValueCc([]);
              setValueBcc([]);
            }, 2000);
          }
        }
      );
    }
  };

  const insertEmailToEmailList = email => {
    let array = lodash.concat(
      email ? email : emailvalue,
      emailvalueCc,
      emailvalueBcc
    );
    let elementsNotExist = array.filter(
      element => element && !lodash.includes(mailUsers[0], element)
    );
    elementsNotExist =
      elementsNotExist.length == 1
        ? elementsNotExist[0].split(',')
        : elementsNotExist;
    getEmail(elementsNotExist, elementsNotExist, 'insert', 'NULL', result => {
      this.setState({ isLoading: true });
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        this.setState({ isLoading: false });
      } else {
        this.setState({ isLoading: false });
      }
    });
  };

  const handleSave = () => {
    handleSaveEmail(emailListDialog);
  };

  const validateEmail = email => {
    let error = null;
    var isEmail;
    emailvalue &&
      emailvalue.map(item => {
        isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
          item
        );
        // isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        //   item
        // );
      });

    if (!isEmail) {
      if (emailvalue && emailvalue != '') {
        error = 'Email address is not valid';

        setOpenAlertt(true);
        setAlertType('warning');

        setAlertMesg(error);
        setTimeout(() => {
          setOpenAlertt(false);
          setEmailvalue('');
          setEmailvalue(value);
          setValue([]);
          setValue(value);
          setError('');
          setRequiredText(false);
        }, 2000);
        return false;
      } else {
        setError('');
        setEmailvalue(value);
        setAlertMesg('');
        return true;
      }
    }
    if (emailvalue) {
      let hasDuplicates = lodash.uniq(emailvalue).length !== emailvalue.length;

      let arrays = [emailvalue, emailvalueCc, emailvalueBcc];

      let hasCommonElements = false;

      // Compare each array with every other array
      for (let i = 0; i < arrays.length; i++) {
        for (let j = i + 1; j < arrays.length; j++) {
          if (_.intersection(arrays[i], arrays[j]).length > 0) {
            hasCommonElements = true;
            break;
          }
        }
      }
      if (hasDuplicates || hasCommonElements) {
        error = 'This email address has already been added';
      }
    }

    if (error) {
      setOpenAlertt(true);
      setAlertType('warning');
      setAlertMesg(error);
      setError(error);
      setEmailvalue('');
      setValue([]);
      setRequiredText(false);
      setTimeout(() => {
        setOpenAlertt(false);
        setError('');
      }, 2000);
      return false;
    } else {
      setError('');
      return true;
    }
  };
  const validateEmailCc = email => {
    let error = null;

    var isEmail;
    emailvalueCc &&
      emailvalueCc.map(item => {
        isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
          item
        );
        // emailvalueCc.map(item => {
        //   isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        //     item
        //   );
      });

    if (!isEmail) {
      if (emailvalueCc && emailvalueCc != '') {
        error = 'Email address is not valid';
        setOpenAlertt(true);
        setAlertType('warning');
        setAlertMesg(error);
        setTimeout(() => {
          setOpenAlertt(false);
          setValueCc([]);
          setEmailvalueCc('');
          setError('');
        }, 2000);
        return false;
      } else {
        setError('');
        setAlertMesg('');
        return true;
      }
    }
    if (emailvalueCc) {
      let hasDuplicates =
        lodash.uniq(emailvalueCc).length !== emailvalueCc.length;

      let arrays = [emailvalue, emailvalueCc, emailvalueBcc];

      let hasCommonElements = false;

      // Compare each array with every other array
      for (let i = 0; i < arrays.length; i++) {
        for (let j = i + 1; j < arrays.length; j++) {
          if (_.intersection(arrays[i], arrays[j]).length > 0) {
            hasCommonElements = true;
            break;
          }
        }
      }
      if (hasDuplicates || hasCommonElements) {
        error = 'This email address has already been added';
      }
    }

    if (error) {
      setError(error);
      setOpenAlertt(true);
      setAlertType('warning');
      setAlertMesg(error);
      setEmailvalueCc('');
      setValueCc([]);
      setRequiredText(false);
      setTimeout(() => {
        setOpenAlertt(false);
        setError('');
      }, 2000);
      return false;
    } else {
      setError('');
      return true;
    }
  };

  const validateEmailBcc = email => {
    let error = null;

    var isEmail;
    emailvalueBcc &&
      emailvalueBcc.map(item => {
        isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
          item
        );
        // emailvalueBcc.map(item => {
        //   isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        //     item
        //   );
      });
    if (!isEmail) {
      if (emailvalueBcc && emailvalueBcc != '') {
        error = 'Email address is not valid';
        setOpenAlertt(true);
        setAlertType('warning');
        setAlertMesg(error);
        setTimeout(() => {
          setValueBcc([]);
          setEmailvalueBcc('');
          setOpenAlertt(false);
          setError('');
        }, 2000);
        return false;
      } else {
        setError('');
        return true;
      }
    }
    if (emailvalueBcc) {
      let hasDuplicates =
        lodash.uniq(emailvalueBcc).length !== emailvalueBcc.length;

      let arrays = [emailvalue, emailvalueCc, emailvalueBcc];

      let hasCommonElements = false;

      // Compare each array with every other array
      for (let i = 0; i < arrays.length; i++) {
        for (let j = i + 1; j < arrays.length; j++) {
          if (_.intersection(arrays[i], arrays[j]).length > 0) {
            hasCommonElements = true;
            break;
          }
        }
      }
      if (hasDuplicates || hasCommonElements) {
        error = 'This email address has already been added';
      }
    }
    if (error) {
      setError(error);
      setOpenAlertt(true);
      setAlertType('warning');
      setAlertMesg(error);
      setValueBcc([]);
      setEmailvalueBcc('');
      setRequiredText(false);
      setTimeout(() => {
        setOpenAlertt(false);
        setError('');
      }, 2000);
      return false;
    } else {
      setError('');
    }

    return true;
  };

  const emailCcClick = () => {
    setEmailCcClick(true);
  };
  const emailBccClick = () => {
    setEmailBccClick(true);
  };
  const label = { inputProps: { 'aria-label': 'Checkbox demo' } };

  return (
    <Dialog
      transition={Fade}
      classes={{
        paper: classes.paper
      }}
      BackdropProps={{
        classes: {
          root: classes.backDrop
        }
      }}
      //maxWidth="xl"
      // style={{ maxWidth: 900, maxHeight: 700 }}
      open={openDialog}
    >
      <DialogTitle id="confirmation-dialog-title">
        <Typography
          variant="h5"
          color="primary"
          style={{
            textTransform: 'none'
          }}
        >
          Email Schedule - {selectedRow.report_name}
        </Typography>
      </DialogTitle>
      <Collapse in={openAlert}>
        <Alert
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Collapse>
      <Collapse in={openAlertErr}>
        <Alert
          severity="error"
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlertErr(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsgErr}
        </Alert>
      </Collapse>
      <Collapse in={openAlertt}>
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMesg}
        </Alert>
      </Collapse>
      <DialogContent style={{ overflowX: 'hidden' }}>
        {/* {error && <p className="error">{error}</p>} */}
        {errorChecked && <p className="errorChk">{errorChecked}</p>}
        <TableContainer
          component={Paper}
          style={{
            margin: 4,
            padding: 1,
            display: 'block',
            width: '100%'
          }}
        >
          <Table
            className="email-table"
            id="maildetails"
            // style={{ minWidth: 300 }}
            size="small"
            aria-label="a dense table"
          >
            <TableHead
              style={{
                textAlign: 'center',
                backgroundColor: '#003d6b'
              }}
            ></TableHead>
            <TableRow key={'email'}>
              <TableCell
                align="left"
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                <span
                  style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                  <span>
                    Email <span style={{ color: 'red' }}>*</span>
                  </span>
                  <span>
                    <DialogActions>
                      <Button
                        variant="contained"
                        className={clsx('reset-btn')}
                        onClick={emailCcClick}
                        color="primary"
                      >
                        Cc
                      </Button>
                      <Button
                        variant="contained"
                        className={clsx('reset-btn')}
                        onClick={emailBccClick}
                        color="primary"
                      >
                        Bcc
                      </Button>
                    </DialogActions>
                  </span>
                </span>
              </TableCell>
              <TableCell
                align="left"
                colSpan={2}
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                <div class="parentCell">
                  <div
                    style={{
                      display: 'inline-block',
                      width: 220,
                      marginLeft: 6
                    }}
                  >
                    Report Type
                  </div>
                  <div
                    style={{
                      display: 'inline-block',
                      fontSize: 14,
                      width: 125
                    }}
                  >
                    Scheduled
                  </div>
                  <div
                    style={{
                      display: 'inline-block',
                      fontSize: 14,
                      marginLeft: 15,
                      width: 208,
                      paddingLeft: 85
                      //   color:
                      //     checked || checkedAdvisor
                      //       ? '#003d6b'
                      //       : 'rgb(170 183 194)'
                    }}
                  >
                    Scheduled On
                  </div>
                </div>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell height={10}>
                <Autocomplete
                  multiple
                  freeSolo
                  getOptionDisabled={option => value.includes(option)}
                  ListboxProps={{ style: { maxHeight: 150 } }}
                  options={mailUsers.length > 0 ? mailUsers[0] : []}
                  value={value}
                  // onChange={(event, newValue) => setValue(newValue)}
                  onChange={(event, newValue) => {
                    setValue(newValue);
                    setEmailvalue(newValue);
                    if (newValue) setRequiredText(false);
                    setError('');
                  }}
                  onInputChange={(event, newInputValue) => {
                    if (event && event.type === 'change') {
                      setEmailvalue([newInputValue]);
                    }
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip label={option} {...getTagProps({ index })} />
                    ))
                  }
                  renderInput={params => (
                    <TextField
                      {...params}
                      style={{
                        maxWidth: '500px',
                        minWidth: '500px',
                        bottom: 4
                      }}
                      variant="standard"
                      fullWidth
                      margin="normal"
                      required
                      id="email"
                      name="email"
                      value={emailvalue}
                      helperText={requiredText && 'This is required!'}
                      onBlur={validateEmail}
                      // onChange={onChangeEmail}
                      onChange={event => {
                        setEmailvalue([event.target.value]);
                      }}
                    />
                  )}
                />
              </TableCell>
              <TableCell>
                {/* <FormGroup style={{ marginTop: '19px' }}> */}
                <Table>
                  <TableRow>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b',
                        width: 220
                      }}
                    >
                      <FormControlLabel
                        control={
                          <Checkbox
                            defaultChecked
                            size="small"
                            color="primary"
                            checked={
                              selectedRow.visibility == 'private' ? true : false
                            }
                            disabled
                            style={{ display: 'none' }}
                            //  onChange={handleCheckboxChange}
                          />
                        }
                        label={
                          <Typography className={classes.formControlLabel}>
                            {/* Private */}
                            {kpiReportTypeCasing}
                          </Typography>
                        }
                      />
                    </TableCell>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b',
                        paddingLeft: 15
                      }}
                    >
                      <FormControlLabel
                        control={
                          <div
                            style={{ display: 'block' }}
                            id="store_schedule"
                            // className={
                            //   (checked &&
                            //     !checkedAdvisor &&
                            //     type != 'store_goal') ||
                            //   (checked && !checkedAdvisor && advisorSelected)
                            //     ? classes.divDisplay
                            //     : ''
                            // }
                          >
                            <FormControl
                              variant="standard"
                              sx={{ m: 1, width: 140 }}
                              className={classes.formControl}
                            >
                              <Select
                                value={selectedSchedule}
                                onChange={displayContent}
                                id="schedule"
                                // disabled={!checked ? true : false}
                                style={{ textAlign: 'left' }}
                              >
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'daily'}
                                >
                                  Daily
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'weekly'}
                                >
                                  Weekly
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'biweekly'}
                                >
                                  Biweekly
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'monthly'}
                                >
                                  Monthly
                                </MenuItem>
                              </Select>
                              {requiredSelect ? (
                                <FormHelperText
                                  style={{ color: 'rgb(255,91,71)' }}
                                >
                                  This is required!
                                </FormHelperText>
                              ) : (
                                ''
                              )}
                            </FormControl>
                          </div>
                        }
                      />
                    </TableCell>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b',
                        paddingLeft: 0
                      }}
                    >
                      <FormControlLabel
                        control={
                          <div
                          // className={
                          //   (checked &&
                          //     !checkedAdvisor &&
                          //     type != 'store_goal') ||
                          //   (checked && !checkedAdvisor && advisorSelected)
                          //     ? classes.divDisplay
                          //     : ''
                          // }
                          >
                            <div id={'daily'} style={{ marginRight: 26 }}>
                              <span
                                style={{
                                  fontSize: '12px'
                                }}
                              >
                                Daily
                              </span>
                            </div>
                            <div
                              id={'weekly'}
                              style={{
                                display: 'none',
                                marginRight: '23px',
                                marginBottom: '16px'
                              }}
                            >
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 150 }}
                                className={classes.formControl}
                                id={'weekly'}
                              >
                                <InputLabel
                                  style={{ fontSize: '12px' }}
                                  id="demo-simple-select-filled-label"
                                >
                                  Choose day
                                </InputLabel>
                                <Select
                                  id={'selectweekly'}
                                  label="ScheduleOn"
                                  name={'selectweekly'}
                                  style={{ textAlign: 'left' }}
                                  MenuProps={{
                                    anchorOrigin: {
                                      vertical: 'bottom',
                                      horizontal: 'left'
                                    },
                                    transformOrigin: {
                                      vertical: 'top',
                                      horizontal: 'left'
                                    },
                                    getContentAnchorEl: null
                                  }}
                                  value={selectedDay}
                                  onChange={displayWeekly}
                                >
                                  {/* **Weekly**  */}
                                  {days.map((val, index) => (
                                    <MenuItem
                                      style={{ fontSize: '12px' }}
                                      value={val.toLowerCase()}
                                    >
                                      {val}
                                    </MenuItem>
                                  ))}
                                </Select>
                                {requiredSelectOptions ? (
                                  <FormHelperText
                                    style={{ color: 'rgb(255,91,71)' }}
                                  >
                                    Please choose a day!
                                  </FormHelperText>
                                ) : (
                                  ''
                                )}
                              </FormControl>{' '}
                            </div>
                            <div
                              id={'monthly'}
                              style={{
                                display: 'none',
                                marginBottom: 15,
                                marginRight: '23px'
                              }}
                            >
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 150 }}
                                className={classes.formControl}
                                id={'monthly'}
                              >
                                <InputLabel
                                  style={{ fontSize: '12px' }}
                                  id="demo-simple-select-filled-label"
                                >
                                  Choose Date
                                </InputLabel>
                                <Select
                                  label="ScheduleOn"
                                  style={{ textAlign: 'left' }}
                                  MenuProps={{
                                    anchorOrigin: {
                                      vertical: 'bottom',
                                      horizontal: 'left'
                                    },
                                    transformOrigin: {
                                      vertical: 'top',
                                      horizontal: 'left'
                                    },
                                    getContentAnchorEl: null
                                  }}
                                  value={selectedDate}
                                  onChange={displayMonthly}
                                  id="selectmonthly"
                                >
                                  {_.times(31, i => (
                                    <MenuItem
                                      style={{ fontSize: '12px' }}
                                      value={i + 1}
                                    >
                                      {i + 1}
                                      {getMonthtext(i + 1)}
                                    </MenuItem>
                                  ))}
                                </Select>
                                {requiredSelectOptions ? (
                                  <FormHelperText
                                    style={{ color: 'rgb(255,91,71)' }}
                                  >
                                    Please choose a date!
                                  </FormHelperText>
                                ) : (
                                  ''
                                )}
                              </FormControl>
                            </div>
                            <div
                              id={'biweekly'}
                              style={{ display: 'none', marginRight: '23px' }}
                            >
                              <InputLabel
                                style={{ fontSize: '12px' }}
                                id="demo-simple-select-filled-label"
                              >
                                Choose days
                              </InputLabel>
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 170 }}
                                className={classes.formControl1}
                                id={'biweekly'}
                              >
                                <Autocomplete
                                  multiple
                                  id={'selectbiweekly'}
                                  size="small"
                                  style={{
                                    width: '100%',

                                    fontSize: 11
                                  }}
                                  value={selectedOptions}
                                  options={days.map(option => option)}
                                  getOptionDisabled={option =>
                                    selectedOptions.length === 2 ||
                                    selectedOptions.includes(option)
                                      ? true
                                      : false
                                  }
                                  onChange={onChangeAutoComplete}
                                  open={selectedOptions.length < 2}
                                  renderInput={params => (
                                    <TextField
                                      {...params}
                                      variant="standard"
                                      helperText={
                                        requiredSelectOptions
                                          ? 'Please choose days!'
                                          : ''
                                      }
                                    />
                                  )}
                                />{' '}
                              </FormControl>
                            </div>
                          </div>
                        }
                      />
                    </TableCell>
                  </TableRow>
                </Table>
              </TableCell>
            </TableRow>
            <TableRow
              className={
                emailCcClicked ? classes.emailCc : classes.emailCcDisable
              }
            >
              <TableCell height={10}>
                <Autocomplete
                  multiple
                  freeSolo
                  ListboxProps={{ style: { maxHeight: 150 } }}
                  options={mailUsers.length > 0 ? mailUsers[0] : []}
                  value={valueCc}
                  getOptionDisabled={option => valueCc.includes(option)}
                  // onChange={(event, newValue) => setValueCc(newValue)}
                  onChange={(event, newValue) => {
                    setValueCc(newValue);
                    setEmailvalueCc(newValue);
                    if (newValue) setRequiredText(false);
                    setError('');
                  }}
                  onInputChange={(event, newInputValue) => {
                    if (event && event.type === 'change') {
                      setEmailvalueCc([newInputValue]);
                    }
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip label={option} {...getTagProps({ index })} />
                    ))
                  }
                  renderInput={params => (
                    <TextField
                      {...params}
                      style={{
                        maxWidth: '500px',
                        minWidth: '500px',
                        bottom: 18
                      }}
                      variant="standard"
                      fullWidth
                      label="Cc"
                      margin="normal"
                      //required
                      id="emailCc"
                      name="emailCc"
                      value={emailvalueCc}
                      onBlur={validateEmailCc}
                      // onChange={onChangeEmailCc}
                      InputLabelProps={{
                        style: { color: 'rgb(0, 61, 107)' }
                      }}
                      // InputProps={{
                      //   ...params.InputProps,
                      //   endAdornment: null // Remove the clear button
                      // }}
                    />
                  )}
                />
              </TableCell>
            </TableRow>
            <TableRow
              className={
                emailBccClicked ? classes.emailBcc : classes.emailBccDisable
              }
            >
              <TableCell height={10}>
                <Autocomplete
                  multiple
                  freeSolo
                  ListboxProps={{ style: { maxHeight: 150 } }}
                  options={mailUsers.length > 0 ? mailUsers[0] : []}
                  value={valueBcc}
                  getOptionDisabled={option => valueBcc.includes(option)}
                  //onChange={(event, newValue) => setValueBcc(newValue)}
                  onChange={(event, newValue) => {
                    setValueBcc(newValue);
                    setEmailvalueBcc(newValue);
                    if (newValue) setRequiredText(false);
                    setError('');
                  }}
                  onInputChange={(event, newInputValue) => {
                    if (event && event.type === 'change') {
                      setEmailvalueBcc([newInputValue]);
                    }
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip label={option} {...getTagProps({ index })} />
                    ))
                  }
                  renderInput={params => (
                    <TextField
                      {...params}
                      style={{
                        maxWidth: '500px',
                        minWidth: '500px',
                        bottom: 18
                      }}
                      variant="standard"
                      fullWidth
                      label="Bcc"
                      margin="normal"
                      //required
                      id="emailBcc"
                      name="emailBcc"
                      value={emailvalueBcc}
                      onBlur={validateEmailBcc}
                      //  onChange={onChangeEmailBcc}
                      InputLabelProps={{
                        style: { color: 'rgb(0, 61, 107)' }
                      }}
                    />
                  )}
                />
              </TableCell>
            </TableRow>
          </Table>
        </TableContainer>
        {/* {error && <p className="error">{error}</p>}
        {errorChecked && <p className="errorChk">{errorChecked}</p>} */}
      </DialogContent>
      <DialogActions
        style={{
          paddingRight: '19px', // Common paddingRight for both buttons
          justifyContent: 'flex-end' // Ensure buttons align to the right
        }}
      >
        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleOk}
          color="primary"
        >
          Cancel
        </Button>

        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleSave}
          color="primary"
          disabled={filterDisabled}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function extractValues(mappings) {
  return Object.keys(mappings);
}
const DayTypeMapping = {
  daily: 'Daily',
  weekly: 'Weekly',
  biweekly: 'Biweekly',
  monthly: 'Monthly'
};

const DayType = extractValues(DayTypeMapping);
const lookupValue = (mappings, key) => {
  return mappings[key];
};

export default EmailDialogSavedReports;
