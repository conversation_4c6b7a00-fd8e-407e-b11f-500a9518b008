import React, { useRef, useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import {
  Card,
  CardHeader,
  CardContent,
  CardActions,
  Button,
  Dialog,
  TextField,
  InputLabel,
  Divider,
  Grid,
  Link,
  Paper,
  Typography,
  colors,
  FormControlLabel,
  Checkbox
} from '@material-ui/core';
//import NetworkService from 'src/services/NetworkServices';
//import { HeaderBodyLayout } from '@fullcalendar/timeline';
import moment from 'moment';
import { Base64 } from 'js-base64';
//import ShowLoader from 'src/views/Loader/loader';
import LoaderSkeleton from '../LoaderSkeleton';

const useStyles = makeStyles(theme => ({
  root: {
    // width: 960
    padding: '30px'
  },
  header: {
    maxWidth: 600,
    margin: '0 auto',
    padding: theme.spacing(3)
  },
  content: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    maxWidth: 720,
    margin: '0 auto'
  },
  product: {
    overflow: 'visible',
    position: 'relative',
    padding: theme.spacing(5, 3),
    cursor: 'pointer',
    transition: theme.transitions.create('transform', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen
    }),
    '&:hover': {
      transform: 'scale(1.1)'
    }
  },
  image: {
    borderRadius: theme.shape.borderRadius,
    position: 'absolute',
    top: -24,
    left: theme.spacing(3),
    height: 48,
    width: 48,
    fontSize: 24
  },
  divider: {
    margin: theme.spacing(2, 0)
  },
  options: {
    lineHeight: '26px'
  },
  recommended: {
    backgroundColor: theme.palette.primary.main,
    '& *': {
      color: `${theme.palette.common.white} !important`
    }
  },
  contact: {
    margin: '20px 0px'
  },
  actions: {
    backgroundColor: colors.grey[100],
    padding: theme.spacing(2),
    display: 'flex',
    justifyContent: 'center'
  },
  startButton: {
    color: theme.palette.common.white,
    backgroundColor: colors.green[600],
    '&:hover': {
      backgroundColor: colors.green[900]
    }
  },
  closeButton: {
    color: theme.palette.common.white,
    backgroundColor: colors.red[600],
    '&:hover': {
      backgroundColor: colors.red[900]
    }
  }
}));

function FeedBackForm({ open, onClose, canvased, page, className, ...rest }) {
  const [feedback, setFeedback] = useState({
    name: '',
    email: '',
    subject: '',
    description: '',
    attachScreenShot: true,
    phone: '',
    query_type: ''
  });
  const [queryType, setQueryType] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [developerList, setDeveloperList] = useState([]);
  const [errorSet, setErrorSet] = useState({
    name: {
      0: false,
      1: null
    },
    email: {
      0: false,
      1: null
    },
    subject: {
      0: false,
      1: null
    },
    phone: {
      0: false,
      1: null
    },
    query_type: {
      0: false,
      1: null
    },
    description: {
      0: false,
      1: null
    }
  });
  const classes = useStyles();

  const handleChange = event => {
    event.persist();
    setFeedback(prevFormState => ({
      ...prevFormState,
      [event.target.name]:
        event.target.type === 'checkbox'
          ? event.target.checked
          : event.target.value
    }));
  };

  const mailHandler = (emailAddress, emailSubject, emailContent) => {
    // NetworkService.MailHandler(emailAddress, emailSubject, emailContent).then((mailResponse) => {
    // });
  };

  const queryExecute = bodyData => {
    setIsLoading(false);
    onClose('success');
    // NetworkService.submitFeedback(bodyData).then((response) => {
    //   if (!response.error) {
    //     let emailSet = [];
    //     developerList.map(devs => {
    //       emailSet.push(devs.email)
    //     })
    //     let emailSubject = 'Support & Feedback';
    //     let emailContent = `Hi, A feedback posted by ${response.data.name} <${response.data.email}>`;
    //     for (let i = 0; i < emailSet.length; i++) {
    //       mailHandler(emailSet[i], emailSubject, emailContent);
    //     }
    //     setIsLoading(false);
    //     onClose('success');
    //   } else {
    //     if (response.error.message === 'Unauthorized request') {
    //       // history.push('/');
    //     }
    //   }
    // }).catch((error) => error);
  };

  function b64toBlob(b64Data, contentType, sliceSize) {
    contentType = contentType || '';
    sliceSize = sliceSize || 512;
    var byteCharacters = Base64.atob(b64Data);
    var byteArrays = [];
    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);
      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      var byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    var blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

  useEffect(() => {
    let mounted = true;
    // if (page !== 'login') {
    //   setFeedback((prevFormState) => ({
    //     ...prevFormState,
    //     name: JSON.parse(localStorage.getItem('loginData')).data.user.first_name + ' ' + JSON.parse(localStorage.getItem('loginData')).data.user.last_name,
    //     email: JSON.parse(localStorage.getItem('loginData')).data.user.email,
    //   }));
    // }

    function networkQueryTypeCall() {
      // NetworkService.getQueryTypeList().then((response) => {
      //   if (response.data) {
      //     if (mounted) {
      //       setQueryType(response.data);
      //       setFeedback((prevFormState) => ({
      //         ...prevFormState,
      //         query_type: response.data ? response.data[0].id : ''
      //       }));
      //     }
      //   } else {
      //     if (response.error.message === 'Unauthorized request') {
      //       // history.push('/');
      //     }
      //     setQueryType([]);
      //   }
      // });
    }

    function networkDeveloperCall() {
      // NetworkService.getDeveloperList().then((response) => {
      //   if (response.data) {
      //     if (mounted) {
      //       setDeveloperList(response.data);
      //     }
      //   } else {
      //     if (response.error.message === 'Unauthorized request') {
      //       // history.push('/');
      //     }
      //     setDeveloperList([]);
      //   }
      // });
    }

    networkQueryTypeCall();
    networkDeveloperCall();
    return () => {
      mounted = false;
    };
  }, []);

  const closeForm = () => {};

  const submitFeedback = () => {
    // setIsLoading(true);
    //  let bodyData = {}
    //  queryExecute(bodyData);
    var phoneno = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
    if (
      feedback.email &&
      /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(feedback.email)
    ) {
      if (feedback.phone && feedback.phone.match(phoneno)) {
        if (/^[A-Za-z ]+$/.test(feedback.name)) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            }
          });
          setIsLoading(true);
          let bodyData = {};
          queryExecute(bodyData);
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: true,
              1: null
            }
          });
        }
      } else {
        if (/^[A-Za-z ]+$/.test(feedback.name)) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            }
          });
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: true,
              1: null
            }
          });
        }
      }
    } else if (feedback.phone && feedback.phone.match(phoneno)) {
      if (/^[A-Za-z ]+$/.test(feedback.name)) {
        setErrorSet({
          email: {
            0: true,
            1: null
          },
          phone: {
            0: false,
            1: null
          },
          name: {
            0: false,
            1: null
          }
        });
      } else {
        setErrorSet({
          email: {
            0: true,
            1: null
          },
          phone: {
            0: false,
            1: null
          },
          name: {
            0: true,
            1: null
          }
        });
      }
    } else {
      if (/^[A-Za-z ]+$/.test(feedback.name)) {
        setErrorSet({
          email: {
            0: true,
            1: null
          },
          phone: {
            0: true,
            1: null
          },
          name: {
            0: false,
            1: null
          }
        });
      } else {
        setErrorSet({
          email: {
            0: true,
            1: null
          },
          phone: {
            0: true,
            1: null
          },
          name: {
            0: true,
            1: null
          }
        });
      }
    }
    // if (page !== 'login') {
    //   bodyData.owner = JSON.parse(localStorage.getItem('loginData')).data.user.id;
    // }
    // bodyData.name = feedback.name;
    // bodyData.email = feedback.email;
    // bodyData.phone = feedback.phone;
    // bodyData.query_type = feedback.query_type;
    // bodyData.subject = feedback.subject;
    // bodyData.description = feedback.description;
    // let now = moment().format('x');
    // var block = encodeURI(canvased).split(";");
    // var contentType = block[0].split(":")[1];
    // var realData = block[1].split(",")[1];
    // var blob = b64toBlob(realData, contentType);
    // if (feedback.attachScreenShot === true) {
    //   const formData = new FormData();
    //  // formData.append('data', blob);
    //   // NetworkService.fileUpload(formData, onUploadProgress).then((response) => {
    //   //   if (!response.error) {
    //   //     bodyData.screenshot = response.data.id;
    //   //     queryExecute(bodyData);
    //   //   } else if (response.error.message === 'Unauthorized request') {
    //   //     // this.props.history.push('/');
    //   //   }
    //   // }).catch((error) => error);
    // } else {
    //   queryExecute(bodyData);
    // }
  };

  const onUploadProgress = progressEvent => {
    const percentCompleted = Math.round(
      (progressEvent.loaded * 100) / progressEvent.total
    );
  };

  return (
    <Dialog maxWidth="sm" onClose={onClose} open={open}>
      <div {...rest} className={clsx(classes.root, className)}>
        <div className={classes.header}>
          <Typography align="center" gutterBottom variant="h5">
            Feedback
          </Typography>
          <Typography
            align="center"
            className={classes.subtitle}
            variant="subtitle2"
          >
            Please provide your feedback.
          </Typography>
        </div>
        <div className={classes.content}>
          <Grid container spacing={4}>
            <Grid item md={12} xs={12} style={{ padding: '25px 16px 5px' }}>
              <TextField
                fullWidth
                // margin="dense"
                label="Email"
                style={{ fontSize: '14px' }}
                required
                error={errorSet.email[0]}
                helperText={false}
                name="email"
                type="email"
                onChange={handleChange}
                value={feedback.email}
                variant="standard"
                helperText={
                  errorSet.email[0] === true ? 'Enter valid email' : ''
                }
              />
            </Grid>
            <Grid item md={12} xs={12} style={{ padding: '5px 16px' }}>
              <TextField
                fullWidth
                // margin="dense"
                label="Name"
                style={{ fontSize: '14px' }}
                required
                error={errorSet.name[0]}
                helperText={false}
                name="name"
                onChange={handleChange}
                value={feedback.name}
                variant="standard"
                helperText={errorSet.name[0] === true ? 'Enter valid name' : ''}
              />
            </Grid>
            <Grid item md={12} xs={12} style={{ padding: '5px 16px' }}>
              <TextField
                fullWidth
                // margin="dense"
                label="Phone"
                style={{ fontSize: '14px' }}
                required
                error={errorSet.phone[0]}
                helperText={false}
                name="phone"
                onChange={handleChange}
                value={feedback.phone}
                variant="standard"
                helperText={
                  errorSet.phone[0] === true ? 'Enter valid phone number' : ''
                }
              />
            </Grid>
            <Grid item md={12} xs={12} style={{ padding: '5px 16px' }}>
              {/* <TextField
                fullWidth
                // margin="dense"
                label="Subject"
                style={{ fontSize: '14px' }}
                required
                error={errorSet.subject[0]}
                helperText={false}
                name="subject"
                onChange={handleChange}
                value={feedback.subject}
                variant="standard"
              /> */}
              {/* <TextField
                fullWidth
                // margin="dense"
                name="query_type"
                style={{ fontSize: '14px' }}
                label="Query Type"
                onChange={handleChange}
                required
                select
                SelectProps={{ native: true }}
                // eslint-disable-next-line react/jsx-sort-props
                value={feedback.query_type}
                variant="standard"
              >
                {queryType.map((option) => (
                  <option
                    key={option.id}
                    value={option.id}
                  >
                    {option.value}
                  </option>
                ))}
              </TextField> */}
            </Grid>
            <Grid item md={12} xs={12} style={{ padding: '5px 16px' }}>
              <TextField
                fullWidth
                // margin="dense"
                label="Description"
                style={{ fontSize: '14px' }}
                required
                // error={errorSet.description[0]}
                helperText={false}
                name="description"
                onChange={handleChange}
                value={feedback.description}
                variant="standard"
              />
            </Grid>
            {/* {canvas && ( */}

            {/* )} */}
          </Grid>
          <Grid item md={12} xs={12} style={{ padding: '16px 0px 0px' }}>
            <FormControlLabel
              name="attachScreenShot"
              onChange={handleChange}
              value={feedback.attachScreenShot}
              control={<Checkbox color="primary" defaultChecked />}
              label="Click to automatically attach a screenshot of this page."
            />
          </Grid>
          {feedback.attachScreenShot && (
            <div
              id="canvaseeee"
              style={{
                position: 'relative',
                height: '150px'
              }}
            >
              <img
                src={encodeURI(canvased)}
                style={{
                  border: ' 1px solid #333',
                  borderRadius: '5px',
                  position: 'absolute',
                  height: '125px',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%,-50%)'
                }}
              />
            </div>
          )}

          {/*  <Typography
            align="center"
            className={classes.contact}
            variant="subtitle2"
          >
            Attaching a screenshot for better understanding.
           {' '}
            <Link
              color="secondary"
              component={RouterLink}
              to="#"
            >
              Contact us
            </Link>
            {' '}
            for information about more enterprise options. 
          </Typography>*/}
        </div>
        <div>
          <Button
            className={classes.closeButton}
            onClick={onClose}
            variant="contained"
          >
            Close
          </Button>
          {/* </div>
        <div className={classes.actions} style={{ position: 'relative' }}> */}
        </div>
        <div style={{ marginTop: -36, marginLeft: 378, width: '100%' }}>
          <Button
            className={classes.startButton}
            onClick={submitFeedback}
            variant="contained"
          >
            Send Feedback
          </Button>
          {isLoading && (
            <div style={{ right: '5%', position: 'absolute' }}>
              <LoaderSkeleton />
            </div>
          )}
        </div>
      </div>
    </Dialog>
  );
}

FeedBackForm.propTypes = {
  className: PropTypes.string,
  page: PropTypes.string,
  onClose: PropTypes.func,
  canvased: PropTypes.object,
  open: PropTypes.bool
};

export default FeedBackForm;
