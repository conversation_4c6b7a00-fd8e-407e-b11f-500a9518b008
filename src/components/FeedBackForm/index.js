import React, { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import {
  Popover,
  CardHeader,
  CardActions,
  Divider,
  Button,
  colors
} from '@material-ui/core';
import FeedBack from './FeedBack';
import HelpOutlineIcon from '@material-ui/icons/HelpOutline';
import html2canvas from 'html2canvas';
import SuccessSnackbar from './SuccessSnackbar';

const useStyles = makeStyles(() => ({
  root: {
    width: 350,
    maxWidth: '100%'
  },
  actions: {
    backgroundColor: colors.grey[50],
    justifyContent: 'center'
  }
}));

function FeedBackForm({ notifications, page, anchorEl, ...rest }) {
  const classes = useStyles();
  const [feedBackStatus, setFeedBackStatus] = useState(false);
  const [canvasVal, setCanvasVal] = useState({});
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const trigger = () => {
    html2canvas(document.body, {
      allowTaint: true,
      useCORS: true
    }).then(function(canvas) {
      var data = canvas.toDataURL('image/jpeg', 0.9);
      var src = encodeURI(data);
      

      // document.getElementById('screenshot').src = src;
      // document.getElementById('size').innerHTML = src.length + ' bytes';
      // document.body.appendChild(canvas);
      setCanvasVal(data);
      setFeedBackStatus(!feedBackStatus);
    });
  };

  const onClose = type => {
    setFeedBackStatus(false);
    if (type === 'success') {
      setOpenSnackbar(true);
    }
  };

  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
  };

  return (
    <div>
      <Button
        onClick={() => trigger()}
        color="secondary"
        variant="contained"
        style={{
          background: '#5eb562',
          position: 'fixed',
          bottom: 52,
          right: -48,
          transform: 'rotate(-90deg)',
          padding: '2px 6px'
        }}
      >
        FeedBack <HelpOutlineIcon style={{ marginLeft: '10px' }} />
      </Button>
      <FeedBack
        open={feedBackStatus}
        page={page}
        canvased={canvasVal}
        onClose={onClose}
      />
      <SuccessSnackbar onClose={handleSnackbarClose} open={openSnackbar} />
    </div>
  );
}

FeedBackForm.propTypes = {
  page: PropTypes.string
};

export default FeedBackForm;
