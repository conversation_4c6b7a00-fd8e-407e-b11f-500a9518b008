import React from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import { Snackbar, SnackbarContent, colors } from '@material-ui/core';
import CheckCircleIcon from '@material-ui/icons/CheckCircleOutlined';

const useStyles = makeStyles(theme => ({
  content: {
    backgroundColor: colors.green[600]
  },
  contentError: {
    backgroundColor: colors.red[600]
  },
  message: {
    display: 'flex',
    alignItems: 'center'
  },
  icon: {
    marginRight: theme.spacing(2)
  },
  close: {
    padding: '0px',
    paddingLeft: '5px',
    color: '#fff'
  }
}));

function SuccessSnackbarHelp({ open, onClose, error }) {
  const classes = useStyles();
  return (
    <Snackbar
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'center'
      }}
      autoHideDuration={2000}
      onClose={onClose}
      open={open}
    >
      <>
        {error === true ? (
          <SnackbarContent
            className={classes.contentError}
            message={
              <span className={classes.message}>
                <CheckCircleIcon className={classes.icon} />
                Failed to send message.
              </span>
            }
            variant="h6"
          />
        ) : (
          <SnackbarContent
            className={classes.content}
            message={
              <span className={classes.message}>
                <CheckCircleIcon className={classes.icon} />
                Thank You. Your message has been sent and we will be responding
                soon.
              </span>
            }
            variant="h6"
          />
        )}
      </>
    </Snackbar>
  );
}

SuccessSnackbarHelp.propTypes = {
  onClose: PropTypes.func,
  open: PropTypes.bool
};

SuccessSnackbarHelp.defaultProps = {
  open: true,
  onClose: () => {}
};

export default SuccessSnackbarHelp;
