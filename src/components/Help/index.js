import React, { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import {
  Popover,
  CardHeader,
  CardActions,
  // Divider,
  Button,
  colors
} from '@material-ui/core';
import Help from './Help';
import HelpOutlineIcon from '@material-ui/icons/HelpOutline';
import html2canvas from 'html2canvas';
import SuccessSnackbarHelp from './SuccessSnackbarHelp';

const useStyles = makeStyles(() => ({
  root: {
    width: 350,
    maxWidth: '100%'
  },
  actions: {
    backgroundColor: colors.grey[50],
    justifyContent: 'center'
  },
  modalPopup:{
    
  }
}));

function HelpForm({ notifications, page, anchorEl, ...rest }) {
  const classes = useStyles();
  const [helpStatus, setHelpStatus] = useState(false);
  const [canvasVal, setCanvasVal] = useState({});
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const trigger = () => {
    const canvas = await html2canvas(document.body);
    const image = canvas.toDataURL("image/png", 1.0);
    setCanvasVal(image);
      setHelpStatus(!helpStatus);
  
    // html2canvas(document.body, {
    //   allowTaint: true,
    //   useCORS: true
    // }).then(function(canvas) {
    //   var data = canvas.toDataURL('image/jpeg', 0.9);
    //   var src = encodeURI(data);
      

    //   // document.getElementById('screenshot').src = src;
    //   // document.getElementById('size').innerHTML = src.length + ' bytes';
    //   // document.body.appendChild(canvas);
    //   setCanvasVal(data);
    //   setHelpStatus(!helpStatus);
    // });
  };

  const onClose = type => {
    setHelpStatus(false);
    if (type === 'success') {
      setOpenSnackbar(true);
    }
  };

  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
  };

  return (
    <div>
      <Button
        onClick={() => trigger()}
        color="secondary"
        variant="contained"
        style={{
          background: '#5eb562',
          position: 'fixed',
          bottom: 52,
          right: -48,
          transform: 'rotate(-90deg)',
          padding: '2px 6px'
        }}
      >
        Help <HelpOutlineIcon style={{ marginLeft: '10px' }} />
      </Button>
      <Help
        open={helpStatus}
        page={page}
        canvased={canvasVal}
        onClose={onClose}
        className={classes.modalPopup}
      />
      <SuccessSnackbarHelp onClose={handleSnackbarClose} open={openSnackbar}  />
    </div>
  );
}

HelpForm.propTypes = {
  page: PropTypes.string
};

export default HelpForm;
