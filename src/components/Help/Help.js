import React, { useRef, useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import {
  Card,
  CardHeader,
  CardContent,
  CardActions,
  Button,
  Dialog,
  TextField,
  InputLabel,
  Divider,
  Grid,
  Link,
  Paper,
  Typography,
  colors,
  FormControlLabel,
  Checkbox,
  TextareaAutosize
} from '@material-ui/core';
//import NetworkService from 'src/services/NetworkServices';
//import { HeaderBodyLayout } from '@fullcalendar/timeline';
import moment from 'moment';
import { Base64 } from 'js-base64';
//import ShowLoader from 'src/views/Loader/loader';
import LoaderSkeleton from '../LoaderSkeleton.js';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgresWrite';
import { SEND_FEEDBACK } from 'src/graphql/queries';
import { useScreenshot } from 'use-screenshot-hook';
import PhoneInput from 'react-phone-input-2';
import { isValidPhoneNumber } from 'react-phone-number-input';
import 'react-phone-input-2/lib/style.css';
import './style.css';
import { traceSpan } from 'src/utils/OTTTracing';
const useStyles = makeStyles(theme => ({
  // root: {
  //   // width: 960
  //   padding: '30px',
  //   border: 'solid #003d6b'
  // },
  popup: {
    scrollbarColor: '#ee7600 #dddddd'
    // height: '50%'
  },

  // navigation: {
  //   overflow: 'auto',
  //   padding: theme.spacing(0, 0, 1, 1),
  //   // flexGrow: 1,
  //   display: 'flex'
  //   // flexDirection: 'column-reverse'

  //   // justifyContent: 'center',
  // },
  modalPopups: {},
  modalPopup: {
    padding: '30px',
    border: 'solid #003d6b',
    scrollbarColor: '#ee7600 #dddddd',
    paddingTop: '8px'
  },
  header: {
    maxWidth: 600,
    margin: '0 auto'
    // padding: theme.spacing(3)
  },
  content: {
    marginTop: theme.spacing(2),
    padding: theme.spacing(2),
    maxWidth: 720,
    margin: '0 auto'
  },
  product: {
    overflow: 'visible',
    position: 'relative',
    padding: theme.spacing(5, 3),
    cursor: 'pointer',
    transition: theme.transitions.create('transform', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen
    }),
    '&:hover': {
      transform: 'scale(1.1)'
    }
  },
  image: {
    borderRadius: theme.shape.borderRadius,
    position: 'absolute',
    top: -24,
    left: theme.spacing(3),
    height: 48,
    width: 48,
    fontSize: 24
  },
  divider: {
    margin: theme.spacing(2, 0)
  },
  options: {
    lineHeight: '26px'
  },
  recommended: {
    backgroundColor: theme.palette.primary.main,
    '& *': {
      color: `${theme.palette.common.white} !important`
    }
  },
  contact: {
    margin: '20px 0px'
  },
  actions: {
    backgroundColor: colors.grey[100],
    padding: theme.spacing(2),
    display: 'flex',
    justifyContent: 'center'
  },
  startButton: {
    color: theme.palette.common.white,
    backgroundColor: colors.green[600],
    // '&:hover': {
    //   backgroundColor: colors.green[900]
    // }
    '&:hover': {
      background: 'rgb(7, 177, 77, 0.42)'
    }
  },
  closeButton: {
    color: theme.palette.common.white,
    backgroundColor: colors.red[600],
    // '&:hover': {
    //   backgroundColor: colors.red[900]
    // }
    '&:hover': {
      background: 'rgb(7, 177, 77, 0.42)'
    }
  }
}));

function HelpForm({
  open,
  onClose,
  keycloak,
  canvased,
  page,
  className,
  ...rest
}) {
  const [help, setHelp] = useState({
    name: keycloak.tokenParsed.given_name,
    lastName: keycloak.tokenParsed.family_name,
    email: keycloak.tokenParsed.email,
    subject: '',
    description: '',
    attachScreenShot: false,
    phone: '',
    query_type: '',
    attachScreenShot: false,
    comment: false,
    something: false,
    improvement: false,
    technical: false,
    phonevalue: ''
  });
  const { image, takeScreenshot, clear } = useScreenshot();
  const [queryType, setQueryType] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadImg, setLoadImg] = useState(false);
  const [developerList, setDeveloperList] = useState([]);
  const [disabled, setDisabled] = useState(true);
  const [check, setCheck] = useState(false);
  const [checkDisable, setCheckDisable] = useState(false);
  const [errorSet, setErrorSet] = useState({
    name: {
      0: false,
      1: null
    },
    lastName: {
      0: false,
      1: null
    },
    email: {
      0: false,
      1: null
    },
    subject: {
      0: false,
      1: null
    },
    phone: {
      0: false,
      1: null
    },
    query_type: {
      0: false,
      1: null
    },
    description: {
      0: false,
      1: null
    }
  });
  const [descriptions, setDescriptions] = useState('');
  const [phoneVal, setPhoneVal] = useState('');
  const [phoneFormattedValue, setPhoneFormattedValue] = useState('');
  const classes = useStyles();
  const handleOnChangePhoneNumber = (value, data, event, formattedValue) => {
    const desValue = help.descriptions;
    if (
      phoneFormattedValue &&
      isValidPhoneNumber(phoneFormattedValue) == true &&
      !phoneFormattedValue.startsWith('+91 1')
    ) {
      if (descriptions && descriptions.length > 0) {
        if (loadImg && !image) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
          setDisabled(true);
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
          setDisabled(false);
        }
      } else {
        if (errorSet.description[0] === true) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: true,
              1: null
            }
          });
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
        }
        setDisabled(true);
      }
    } else {
      if (descriptions && descriptions.length > 0) {
        setErrorSet({
          email: {
            0: false,
            1: null
          },
          phone: {
            0: true,
            1: null
          },
          name: {
            0: false,
            1: null
          },
          lastName: {
            0: false,
            1: null
          },
          description: {
            0: false,
            1: null
          }
        });
        setDisabled(true);
      } else {
        if (errorSet.description[0] === true) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: true,
              1: null
            }
          });
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
        }
        setDisabled(true);
      }
    }
  };
  const handleOnChangePhone = (value, data, event, formattedValue) => {
    var phoneValue = value.replace(data.dialCode, '');
    var phoneVal = '';

    // if(phoneValue.length <= 10){
    //   var res = phoneValue.substr(0, phoneValue.length - 1) + '';
    //   console.log("phoneValue===",res,res.length);
    //   phoneVal = formattedValue;
    // }else{
    //   // var res = formattedValue.substr(0, formattedValue.length - 1) + '';
    //   var count = 0;
    //   count=(data.dialCode).length+14
    //   if(phoneValue.length>=10){
    //     var mystring = formattedValue.substring(0, count);
    //     console.log("phoneValue===s=",mystring,(data.dialCode).length,count,phoneValue.length);
    //     // phoneVal = data.dialCode;
    //     phoneVal = mystring;
    //   }

    // }

    setPhoneVal(phoneValue);
    setHelp(prevFormState => ({
      ...prevFormState,
      ['phone']:
        event.target.type === 'checkbox' ? event.target.checked : formattedValue
    }));
    setHelp(prevFormState => ({
      ...prevFormState,
      ['phoneValue']:
        event.target.type === 'checkbox' ? event.target.checked : phoneValue
    }));
    setPhoneFormattedValue(formattedValue);
    let onlyLetters = /^[a-zA-Z]+$/;

    if (isValidPhoneNumber(formattedValue)) {
      const desValue = help.descriptions;
      if (
        formattedValue &&
        isValidPhoneNumber(formattedValue) == true &&
        !formattedValue.startsWith('+91 1')
      ) {
        if (descriptions && descriptions.length > 0) {
          if (loadImg && !image) {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
            setDisabled(true);
          } else {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
            setDisabled(false);
          }
        } else {
          if (errorSet.description[0] === true) {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          }
          setDisabled(true);
        }
      } else {
        if (descriptions && descriptions.length > 0) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
          setDisabled(true);
        } else {
          if (errorSet.description[0] === true) {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          }
          setDisabled(true);
        }
      }
    } else {
      setDisabled(true);
      const desValue = help.descriptions;

      if (phoneValue.length > 0 && event.type == 'click') {
        if (descriptions && descriptions.length > 0) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
          setDisabled(true);
        } else {
          if (errorSet.description[0] === true) {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          }
          setDisabled(true);
        }
      }
    }
    // var phoneno = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
    // var phoneno = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{3,20}$/im;
    // if (phoneValue && phoneValue.match(phoneno)) {

    // if(phoneValue){
    //   if (formattedValue && isValidPhoneNumber(formattedValue) == true && formattedValue !='+12345678'  && formattedValue !='+123456789') {
    //     console.log("enter 1");
    //   if ((descriptions.trim()).length>0) {console.log("enter 2");
    //     if(loadImg && !image){
    //       setErrorSet({
    //         email: {
    //           0: false,
    //           1: null
    //         },
    //         phone: {
    //           0: false,
    //           1: null
    //         },
    //         name: {
    //           0: false,
    //           1: null
    //         },
    //         lastName: {
    //           0: false,
    //           1: null
    //         },
    //         description: {
    //           0: false,
    //           1: null
    //         }

    //       });
    //       setDisabled(true);
    //       }else{
    //         setErrorSet({
    //           email: {
    //             0: false,
    //             1: null
    //           },
    //           phone: {
    //             0: false,
    //             1: null
    //           },
    //           name: {
    //             0: false,
    //             1: null
    //           },
    //           lastName: {
    //             0: false,
    //             1: null
    //           },
    //           description: {
    //             0: false,
    //             1: null
    //           }

    //         });
    //         setDisabled(false);
    //       }
    // }else{console.log("enter 3");
    //   if(errorSet.description[0] === true){
    //     setErrorSet({
    //       email: {
    //         0: false,
    //         1: null
    //       },
    //       phone: {
    //         0: false,
    //         1: null
    //       },
    //       name: {
    //         0: false,
    //         1: null
    //       },
    //       lastName: {
    //         0: false,
    //         1: null
    //       },
    //       description: {
    //         0: true,
    //         1: null
    //       }
    //     });
    //   }else{
    //     setErrorSet({
    //       email: {
    //         0: false,
    //         1: null
    //       },
    //       phone: {
    //         0: false,
    //         1: null
    //       },
    //       name: {
    //         0: false,
    //         1: null
    //       },
    //       lastName: {
    //         0: false,
    //         1: null
    //       },
    //       description: {
    //         0: false,
    //         1: null
    //       }
    //     });
    //   }
    //   setDisabled(true);
    // }

    // }else{console.log("enter 4");
    //   if ((descriptions.trim()).length>0) {console.log("enter 5");
    //     setErrorSet({
    //       email: {
    //         0: false,
    //         1: null
    //       },
    //       phone: {
    //         0: true,
    //         1: null
    //       },
    //       name: {
    //         0: false,
    //         1: null
    //       },
    //       lastName: {
    //         0: false,
    //         1: null
    //       },
    //       description: {
    //         0: false,
    //         1: null
    //       }
    //     });
    //     setDisabled(true);
    //   }else{console.log("enter 6");
    //     if(errorSet.description[0] === true){
    //       setErrorSet({
    //         email: {
    //           0: false,
    //           1: null
    //         },
    //         phone: {
    //           0: true,
    //           1: null
    //         },
    //         name: {
    //           0: false,
    //           1: null
    //         },
    //         lastName: {
    //           0: false,
    //           1: null
    //         },
    //         description: {
    //           0: true,
    //           1: null
    //         }
    //       });
    //     }else{
    //       setErrorSet({
    //         email: {
    //           0: false,
    //           1: null
    //         },
    //         phone: {
    //           0: true,
    //           1: null
    //         },
    //         name: {
    //           0: false,
    //           1: null
    //         },
    //         lastName: {
    //           0: false,
    //           1: null
    //         },
    //         description: {
    //           0: false,
    //           1: null
    //         }
    //       });
    //     }

    //     setDisabled(true);
    //   }

    // }
    // }
  };
  const handleChangeDescription = event => {
    if (
      phoneFormattedValue &&
      isValidPhoneNumber(phoneFormattedValue) == true &&
      !phoneFormattedValue.startsWith('+91 1')
    ) {
      if (descriptions.trim().length > 0) {
        if (loadImg && !image) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
          setDisabled(true);
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
          setDisabled(false);
        }
      } else {
        setErrorSet({
          email: {
            0: false,
            1: null
          },
          phone: {
            0: false,
            1: null
          },
          name: {
            0: false,
            1: null
          },
          lastName: {
            0: false,
            1: null
          },
          description: {
            0: true,
            1: null
          }
        });
        setDisabled(true);
      }
    } else {
      if (descriptions.trim().length > 0) {
        if (errorSet.phone[0] === true) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
        }
        setDisabled(true);
      } else {
        if (errorSet.phone[0] === true) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: true,
              1: null
            }
          });
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: false,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: true,
              1: null
            }
          });
        }
        setDisabled(true);
      }
    }
  };
  const handleChange = event => {
    if (
      event.target.name == 'attachScreenShot' &&
      event.target.checked == true
    ) {
      setCheck(true);
      setDisabled(true);
      setLoadImg(true);
      takeScreenshot();
      setCheckDisable(true);
    } else if (
      event.target.name == 'attachScreenShot' &&
      event.target.checked == false
    ) {
      setLoadImg(false);
      setCheck(false);
      clear();
      setCheckDisable(false);
    }
    event.persist();
    setHelp(prevFormState => ({
      ...prevFormState,
      [event.target.name]:
        event.target.type === 'checkbox'
          ? event.target.checked
          : event.target.value
    }));
    if (event.target.name == 'description') {
      const desc = event.target.value.trim().length;

      setDescriptions(event.target.value);
      if (desc > 0) {
        // handleChangeDescription();
      }
      // let onlyLetters = /^[a-zA-Z]+$/;&& onlyLetters.test((event.target.value).charAt(0))
      // var phoneno = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
      var phoneno = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,14}$/im;
      // if (phoneVal && phoneVal.match(phoneno)) {
      //   if (phoneFormattedValue && isValidPhoneNumber(phoneFormattedValue) == true ) {
      //   if (desc>0 ) {
      //     if(loadImg && !image){
      //     setErrorSet({
      //       email: {
      //         0: false,
      //         1: null
      //       },
      //       phone: {
      //         0: false,
      //         1: null
      //       },
      //       name: {
      //         0: false,
      //         1: null
      //       },
      //       lastName: {
      //         0: false,
      //         1: null
      //       },
      //       description: {
      //         0: false,
      //         1: null
      //       }

      //     });
      //     setDisabled(true);
      //     }else{
      //       setErrorSet({
      //         email: {
      //           0: false,
      //           1: null
      //         },
      //         phone: {
      //           0: false,
      //           1: null
      //         },
      //         name: {
      //           0: false,
      //           1: null
      //         },
      //         lastName: {
      //           0: false,
      //           1: null
      //         },
      //         description: {
      //           0: false,
      //           1: null
      //         }

      //       });
      //       setDisabled(false);
      //     }
      // }else{
      //   setErrorSet({
      //     email: {
      //       0: false,
      //       1: null
      //     },
      //     phone: {
      //       0: false,
      //       1: null
      //     },
      //     name: {
      //       0: false,
      //       1: null
      //     },
      //     lastName: {
      //       0: false,
      //       1: null
      //     },
      //     description: {
      //       0: true,
      //       1: null
      //     }
      //   });
      //   setDisabled(true);
      // }

      // }else{
      //   if (desc>0 ) {
      //     if(errorSet.phone[0] === true){
      //       setErrorSet({
      //         email: {
      //           0: false,
      //           1: null
      //         },
      //         phone: {
      //           0: true,
      //           1: null
      //         },
      //         name: {
      //           0: false,
      //           1: null
      //         },
      //         lastName: {
      //           0: false,
      //           1: null
      //         },
      //         description: {
      //           0: false,
      //           1: null
      //         }
      //       });
      //     }else{
      //       setErrorSet({
      //         email: {
      //           0: false,
      //           1: null
      //         },
      //         phone: {
      //           0: false,
      //           1: null
      //         },
      //         name: {
      //           0: false,
      //           1: null
      //         },
      //         lastName: {
      //           0: false,
      //           1: null
      //         },
      //         description: {
      //           0: false,
      //           1: null
      //         }
      //       });
      //     }
      //     setDisabled(true);
      //   }else{
      //     if(errorSet.phone[0] === true){
      //       setErrorSet({
      //         email: {
      //           0: false,
      //           1: null
      //         },
      //         phone: {
      //           0: true,
      //           1: null
      //         },
      //         name: {
      //           0: false,
      //           1: null
      //         },
      //         lastName: {
      //           0: false,
      //           1: null
      //         },
      //         description: {
      //           0: true,
      //           1: null
      //         }
      //       });
      //     }else{
      //       setErrorSet({
      //         email: {
      //           0: false,
      //           1: null
      //         },
      //         phone: {
      //           0: false,
      //           1: null
      //         },
      //         name: {
      //           0: false,
      //           1: null
      //         },
      //         lastName: {
      //           0: false,
      //           1: null
      //         },
      //         description: {
      //           0: true,
      //           1: null
      //         }
      //       });
      //     }

      //     setDisabled(true);
      //   }

      // }
    }
  };

  const mailHandler = (emailAddress, emailSubject, emailContent) => {
    // NetworkService.MailHandler(emailAddress, emailSubject, emailContent).then((mailResponse) => {
    // });
  };
  useEffect(() => {
    if (image) {
      setCheckDisable(false);
      checkFormImage();
    }
  }, [image]);
  const queryExecute = bodyData => {
    let name = help.name;
    let lastName = help.lastName;
    let phone = help.phone;
    let attachScreenShot = help.attachScreenShot;
    let description = help.description;
    let email = help.email;
    // if(help.attachScreenShot == true){
    //   let chkGeneral = 1;
    // }else{
    //   let chkGeneral = 0;
    // }
    let chkElse = 0;
    let chkGeneral = 0;
    let chkImprvIdea = 0;
    let chkTechorfunc = 0;
    if (help.improvement == true) {
      chkImprvIdea = 1;
    } else {
      chkImprvIdea = 0;
    }
    if (help.comment == true) {
      chkGeneral = 1;
    } else {
      chkGeneral = 0;
    }
    if (help.technical == true) {
      chkTechorfunc = 1;
    } else {
      chkTechorfunc = 0;
    }
    if (help.something == true) {
      chkElse = 1;
    } else {
      chkElse = 0;
    }
    let base64Img = '';
    if (image) {
      base64Img = image.replace('data:image/png;base64,', '');
    }

    const client = makeApolloClientPostgres;
    if (base64Img) {
      const start = new Date();
      client
        .mutate({
          mutation: SEND_FEEDBACK,
          variables: {
            chkElse: chkElse,
            chkGeneral: chkGeneral,
            chkImprvIdea: chkImprvIdea,
            chkTechorfunc: chkTechorfunc,
            content: base64Img,
            description: description,
            email: keycloak.tokenParsed.email,
            mobile: phone,
            name: keycloak.tokenParsed.given_name,
            lastName: keycloak.tokenParsed.family_name
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/Home',
            origin: '',
            event: 'Menu Load',
            is_from: 'SEND_FEEDBACK',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          clear();
          setDescriptions('');
          setPhoneFormattedValue('');

          setDisabled(true);
          setIsLoading(false);
          setLoadImg(false);
          onClose('success');
          const initialState = {
            name: keycloak.tokenParsed.given_name,
            lastName: keycloak.tokenParsed.family_name,
            email: keycloak.tokenParsed.email,
            subject: '',
            description: '',
            attachScreenShot: false,
            phone: '',
            query_type: '',
            attachScreenShot: false,
            comment: false,
            something: false,
            improvement: false,
            technical: false,
            phonevalue: ''
          };
          setHelp({ ...initialState });
        })
        .catch(error => {
          clear();
          setDescriptions('');
          setPhoneFormattedValue('');
          setDisabled(true);
          setIsLoading(false);
          setLoadImg(false);
          onClose('error');
          const initialState = {
            name: keycloak.tokenParsed.given_name,
            lastName: keycloak.tokenParsed.family_name,
            email: keycloak.tokenParsed.email,
            subject: '',
            description: '',
            attachScreenShot: false,
            phone: '',
            query_type: '',
            attachScreenShot: false,
            comment: false,
            something: false,
            improvement: false,
            technical: false,
            phonevalue: ''
          };
          setHelp({ ...initialState });
        });
    } else {
      const start = new Date();
      client
        .mutate({
          mutation: SEND_FEEDBACK,
          variables: {
            chkElse: chkElse,
            chkGeneral: chkGeneral,
            chkImprvIdea: chkImprvIdea,
            chkTechorfunc: chkTechorfunc,
            description: description,
            email: keycloak.tokenParsed.email,
            mobile: phone,
            name: keycloak.tokenParsed.given_name,
            lastName: keycloak.tokenParsed.family_name
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/Home',
            origin: '',
            event: 'Menu Load',
            is_from: 'SEND_FEEDBACK',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          setDescriptions('');
          setPhoneFormattedValue('');
          setDisabled(true);
          setIsLoading(false);
          setLoadImg(false);
          onClose('success');
          const initialState = {
            name: keycloak.tokenParsed.given_name,
            lastName: keycloak.tokenParsed.family_name,
            email: keycloak.tokenParsed.email,
            subject: '',
            description: '',
            attachScreenShot: false,
            phone: '',
            query_type: '',
            attachScreenShot: false,
            comment: false,
            something: false,
            improvement: false,
            technical: false,
            phonevalue: ''
          };
          setHelp({ ...initialState });
        })
        .catch(error => {
          clear();
          setDescriptions('');
          setPhoneFormattedValue('');
          setDisabled(true);
          setIsLoading(false);
          setLoadImg(false);
          onClose('error');
          const initialState = {
            name: keycloak.tokenParsed.given_name,
            lastName: keycloak.tokenParsed.family_name,
            email: keycloak.tokenParsed.email,
            subject: '',
            description: '',
            attachScreenShot: false,
            phone: '',
            query_type: '',
            attachScreenShot: false,
            comment: false,
            something: false,
            improvement: false,
            technical: false,
            phonevalue: ''
          };
          setHelp({ ...initialState });
        });
    }

    // NetworkService.submitFeedback(bodyData).then((response) => {
    //   if (!response.error) {
    //     let emailSet = [];
    //     developerList.map(devs => {
    //       emailSet.push(devs.email)
    //     })
    //     let emailSubject = 'Support & Feedback';
    //     let emailContent = `Hi, A feedback posted by ${response.data.name} <${response.data.email}>`;
    //     for (let i = 0; i < emailSet.length; i++) {
    //       mailHandler(emailSet[i], emailSubject, emailContent);
    //     }
    //     setIsLoading(false);
    //     onClose('success');
    //   } else {
    //     if (response.error.message === 'Unauthorized request') {
    //       // history.push('/');
    //     }
    //   }
    // }).catch((error) => error);
  };
  function _base64ToArrayBuffer(base64) {
    var binary_string = window.atob(base64);
    var len = binary_string.length;
    var bytes = new Uint8Array(len);
    for (var i = 0; i < len; i++) {
      bytes[i] = binary_string.charCodeAt(i);
    }
    return bytes.buffer;
  }
  function toBinary(string) {
    const codeUnits = new Uint16Array(string.length);
    for (let i = 0; i < codeUnits.length; i++) {
      codeUnits[i] = string.charCodeAt(i);
    }
    return String.fromCharCode(...new Uint8Array(codeUnits.buffer));
  }
  function b64toBlob(b64Data, contentType, sliceSize) {
    contentType = contentType || '';
    sliceSize = sliceSize || 512;
    var byteCharacters = Base64.atob(b64Data);
    var byteArrays = [];
    for (var offset = 0; offset < byteCharacters.length; offset += sliceSize) {
      var slice = byteCharacters.slice(offset, offset + sliceSize);
      var byteNumbers = new Array(slice.length);
      for (var i = 0; i < slice.length; i++) {
        byteNumbers[i] = slice.charCodeAt(i);
      }
      var byteArray = new Uint8Array(byteNumbers);
      byteArrays.push(byteArray);
    }
    var blob = new Blob(byteArrays, { type: contentType });
    return blob;
  }

  // useEffect(() => {
  // if(check == true){
  //   if(loadImg && !image){
  //     checkForm();
  //   }else{
  //     setCheckDisable(false);
  //     checkForm();
  //     // setDisabled(false)
  //   }

  // }
  //   // setLoadImg(false);
  //   let mounted = true;
  //   // if (page !== 'login') {
  //   //   setFeedback((prevFormState) => ({
  //   //     ...prevFormState,
  //   //     name: JSON.parse(localStorage.getItem('loginData')).data.user.first_name + ' ' + JSON.parse(localStorage.getItem('loginData')).data.user.last_name,
  //   //     email: JSON.parse(localStorage.getItem('loginData')).data.user.email,
  //   //   }));
  //   // }

  //   function networkQueryTypeCall() {
  //     // NetworkService.getQueryTypeList().then((response) => {
  //     //   if (response.data) {
  //     //     if (mounted) {
  //     //       setQueryType(response.data);
  //     //       setFeedback((prevFormState) => ({
  //     //         ...prevFormState,
  //     //         query_type: response.data ? response.data[0].id : ''
  //     //       }));
  //     //     }
  //     //   } else {
  //     //     if (response.error.message === 'Unauthorized request') {
  //     //       // history.push('/');
  //     //     }
  //     //     setQueryType([]);
  //     //   }
  //     // });
  //   }

  //   function networkDeveloperCall() {
  //     // NetworkService.getDeveloperList().then((response) => {
  //     //   if (response.data) {
  //     //     if (mounted) {
  //     //       setDeveloperList(response.data);
  //     //     }
  //     //   } else {
  //     //     if (response.error.message === 'Unauthorized request') {
  //     //       // history.push('/');
  //     //     }
  //     //     setDeveloperList([]);
  //     //   }
  //     // });
  //   }

  //   networkQueryTypeCall();
  //   networkDeveloperCall();
  //   return () => {
  //     mounted = false;
  //   };
  // });

  const closeForm = () => {};
  const checkForm = (value, country, event, formattedValue) => {
    setDisabled(true);
    // var phoneno = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
    var phoneno = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,14}$/im;
    let onlyLetters = /^[a-zA-Z]+$/;
    if (
      help.email &&
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/i.test(
        help.email
      )
    ) {
      // if (help.phoneValue && help.phoneValue.match(phoneno)) {console.log("enter 1");
      if (
        help.phone &&
        isValidPhoneNumber(help.phone) == true &&
        !phoneFormattedValue.startsWith('+91 1')
      ) {
        if (/^[A-Za-z ]+$/.test(help.name)) {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            if (help.description.trim().length > 0) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
              setDisabled(false);
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            }
          }
        }
      } else {
        if (help.description.trim().length > 0) {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: false,
              1: null
            }
          });
        } else {
          setErrorSet({
            email: {
              0: false,
              1: null
            },
            phone: {
              0: true,
              1: null
            },
            name: {
              0: false,
              1: null
            },
            lastName: {
              0: false,
              1: null
            },
            description: {
              0: true,
              1: null
            }
          });
        }
      }
    } else if (
      help.phone &&
      isValidPhoneNumber(help.phone) == true &&
      !phoneFormattedValue.startsWith('+91 1')
    ) {
      // }else if (help.phoneValue && help.phoneValue.match(phoneno)) {console.log("enter 1");
      if (/^[A-Za-z ]+$/.test(help.name)) {
        if (/^[A-Za-z ]+$/.test(help.lastName)) {
          if (help.description.trim().length > 0) {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: false,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          }
        }
      }
    } else {
      if (help.description.trim().length > 0) {
        setErrorSet({
          email: {
            0: false,
            1: null
          },
          phone: {
            0: true,
            1: null
          },
          name: {
            0: false,
            1: null
          },
          lastName: {
            0: false,
            1: null
          },
          description: {
            0: false,
            1: null
          }
        });
      } else {
        setErrorSet({
          email: {
            0: false,
            1: null
          },
          phone: {
            0: true,
            1: null
          },
          name: {
            0: false,
            1: null
          },
          lastName: {
            0: false,
            1: null
          },
          description: {
            0: true,
            1: null
          }
        });
      }
    }
  };

  const checkFormImage = (value, country, event, formattedValue) => {
    setDisabled(true);

    if (errorSet.phone[0] == false && errorSet.description[0] == false) {
      if (
        phoneFormattedValue &&
        isValidPhoneNumber(phoneFormattedValue) == true &&
        !phoneFormattedValue.startsWith('+91 1') &&
        descriptions.trim().length > 0
      ) {
        setDisabled(false);
      }
    }
  };
  const submitHelp = () => {
    // setIsLoading(true);
    //  let bodyData = {}
    //  queryExecute(bodyData);

    // var phoneno = /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/;
    var phoneno = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,14}$/im;
    let onlyLetters = /^[a-zA-Z]+$/;
    if (
      help.email &&
      /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/i.test(
        help.email
      )
      // /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(help.email)
    ) {
      if (
        help.phone &&
        isValidPhoneNumber(help.phone) == true &&
        !phoneFormattedValue.startsWith('+91 1')
      ) {
        // if (help.phoneValue && help.phoneValue.match(phoneno)) {

        if (help.name) {
          if (help.description.trim().length > 0) {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
              setDisabled(true);
              setIsLoading(true);
              let bodyData = {};
              queryExecute(bodyData);
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
            }
          } else {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            }
          }
        } else {
          if (help.description.trim().length > 0) {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
            }
          } else {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: false,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            }
          }
        }
      } else {
        if (/^[A-Za-z ]+$/.test(help.name)) {
          if (help.description.trim().length > 0) {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
            }
          } else {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: false,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            }
          }
        } else {
          if (help.description.trim().length > 0) {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: false,
                  1: null
                }
              });
            }
          } else {
            if (/^[A-Za-z ]+$/.test(help.lastName)) {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: false,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            } else {
              setErrorSet({
                email: {
                  0: false,
                  1: null
                },
                phone: {
                  0: true,
                  1: null
                },
                name: {
                  0: true,
                  1: null
                },
                lastName: {
                  0: true,
                  1: null
                },
                description: {
                  0: true,
                  1: null
                }
              });
            }
          }
        }
      }
      // } else if (help.phoneValue && help.phoneValue.match(phoneno)) {console.log('enter 16');
    } else if (
      help.phone &&
      isValidPhoneNumber(help.phone) == true &&
      !phoneFormattedValue.startsWith('+91 1')
    ) {
      if (/^[A-Za-z ]+$/.test(help.name)) {
        if (help.description.trim().length > 0) {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          }
        } else {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          }
        }
      } else {
        if (help.description.trim().length > 0) {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          }
        } else {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: false,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          }
        }
      }
    } else {
      if (/^[A-Za-z ]+$/.test(help.name)) {
        if (help.description.trim().length > 0) {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          }
        } else {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: false,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          }
        }
      } else {
        if (help.description.trim().length > 0) {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: false,
                1: null
              }
            });
          }
        } else {
          if (/^[A-Za-z ]+$/.test(help.lastName)) {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: false,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          } else {
            setErrorSet({
              email: {
                0: true,
                1: null
              },
              phone: {
                0: true,
                1: null
              },
              name: {
                0: true,
                1: null
              },
              lastName: {
                0: true,
                1: null
              },
              description: {
                0: true,
                1: null
              }
            });
          }
        }
      }
    }
  };

  const onUploadProgress = progressEvent => {
    const percentCompleted = Math.round(
      (progressEvent.loaded * 100) / progressEvent.total
    );
  };
  // useEffect(() => {

  //   if(open == null){
  //     setErrorSet({
  //       name: {
  //         0: false,
  //         1: null
  //       },
  //       lastName: {
  //         0: false,
  //         1: null
  //       },
  //       email: {
  //         0: false,
  //         1: null
  //       },
  //       subject: {
  //         0: false,
  //         1: null
  //       },
  //       phone: {
  //         0: false,
  //         1: null
  //       },
  //       query_type: {
  //         0: false,
  //         1: null
  //       },
  //       description: {
  //         0: false,
  //         1: null
  //       }
  //     })
  //     clear();
  //     // setHelp({
  //     //   name: keycloak.tokenParsed.given_name,
  //     //   email: keycloak.tokenParsed.email,
  //     //   subject: '',
  //     //   description: '',
  //     //   attachScreenShot: false,
  //     //   phone: '',
  //     //   query_type: '',
  //     //   attachScreenShot: false,
  //     //   comment:  false,
  //     //   something:  false,
  //     //   improvement:  false,
  //     //   technical:  false,
  //     // });
  //   }
  // });
  const onclickClose = () => {
    setDescriptions('');
    setPhoneFormattedValue('');
    setDisabled(true);
    setCheckDisable(false);
    onClose();
    setLoadImg(false);
    clear();
    setHelp({
      name: keycloak.tokenParsed.given_name,
      lastName: keycloak.tokenParsed.family_name,
      email: keycloak.tokenParsed.email,
      subject: '',
      description: '',
      attachScreenShot: false,
      phone: '',
      query_type: '',
      attachScreenShot: false,
      comment: false,
      something: false,
      improvement: false,
      technical: false
    });
    setErrorSet({
      email: {
        0: false,
        1: null
      },
      phone: {
        0: false,
        1: null
      },
      name: {
        0: false,
        1: null
      },
      lastName: {
        0: false,
        1: null
      },
      description: {
        0: false,
        1: null
      }
    });
  };
  return (
    <Dialog
      maxWidth="sm"
      open={open}
      id="scroll"
      className={classes.modalPopups}
      style={{ height: '107%', marginTop: '-30px' }}
    >
      <div
        {...rest}
        className={clsx(classes.root, className, classes.modalPopup)}
      >
        <div className={classes.header}>
          <Typography align="center" gutterBottom variant="h5">
            Support &amp; Feedback
          </Typography>
          {/* <Typography
            align="center"
            className={classes.subtitle}
            variant="subtitle2"
          >
            Please provide your feedback.
          </Typography> */}
        </div>
        <div className={classes.content}>
          <Grid container spacing={4}>
            <Grid
              item
              md={12}
              xs={12}
              style={{ padding: '0px 16px', marginTop: '-5px' }}
            >
              <TextField
                fullWidth
                // margin="dense"
                label={
                  <span>
                    First Name <span className="mandatorySign">*</span>
                  </span>
                }
                style={{ fontSize: '1px' }}
                //required
                // error={errorSet.name[0]}
                name="name"
                onChange={handleChange}
                value={help.name}
                variant="standard"
                // helperText={
                //   errorSet.name[0] === true ? 'Enter valid first name' : ''
                // }
                InputLabelProps={{ style: { fontSize: 12, color: '#212121' } }}
                disabled={true}
                onBlur={checkForm}
                InputProps={{
                  readOnly: true
                }}
              />
            </Grid>
            <Grid
              item
              md={12}
              xs={12}
              style={{ padding: '0px 16px', marginTop: '3px' }}
            >
              <TextField
                fullWidth
                label={
                  <span>
                    Last Name <span className="mandatorySign">*</span>
                  </span>
                }
                style={{ fontSize: '1px' }}
                //required
                error={errorSet.lastName[0]}
                name="lastName"
                onChange={handleChange}
                value={help.lastName}
                variant="standard"
                helperText={
                  errorSet.lastName[0] === true ? 'Enter valid last name' : ''
                }
                InputLabelProps={{ style: { fontSize: 12, color: '#212121' } }}
                disabled={true}
                onBlur={checkForm}
                InputProps={{
                  readOnly: true
                }}
              />
            </Grid>
            <Grid
              item
              md={12}
              xs={12}
              style={{ padding: '10px 16px', width: '100%', marginTop: '-5px' }}
            >
              <InputLabel style={{ fontSize: 10.1, paddingBottom: 4 }}>
                Phone <span className="mandatorySign">*</span>
              </InputLabel>
              <PhoneInput
                placeholder="Enter phone number"
                value={help.phone}
                onChange={handleOnChangePhone}
                onBlur={handleOnChangePhoneNumber}
                country={'us'}
                countryCodeEditable={false}
                containerStyle={{
                  width: '100% !important',
                  borderColor: '#fff'
                }}
                name="phone"
                label="Phone"
                required
                error={errorSet.phone[0]}
                helperText={
                  errorSet.phone[0] === true ? 'Enter valid phone number' : ''
                }
                rules={{ required: true }}
                // onKeyDown={checkForm}
              />
              {/* {errorSet.phone[0] === true  && ( */}
              <label
                style={{
                  visibility: errorSet.phone[0] === true ? 'visible' : 'hidden',
                  color: '#e53935',
                  fontSize: '11px'
                }}
              >
                Enter valid phone number
              </label>
              {/* )} */}

              {/* <PhoneInput
                country={'us'}
                fullWidth
                // margin="dense"
                label="Phone"
                style={{ fontSize: '14px',border: '#fff' }}
                required
                error={errorSet.phone[0]}
                helperText={false}
                name="phone"
                onChange={handleOnChangePhone}
                value={help.phone}
                variant="standard"
                helperText={
                  errorSet.phone[0] === true ? 'Enter valid phone number' : ''
                }
                InputLabelProps={{ style: { fontSize: 12, color: '#212121' } }}
        /> */}
            </Grid>
            <Grid
              item
              md={12}
              xs={12}
              style={{ padding: '0px 16px', marginTop: '-5px' }}
            >
              <TextField
                fullWidth
                // margin="dense"
                label={
                  <span>
                    Email <span className="mandatorySign">*</span>
                  </span>
                }
                style={{ fontSize: '14px' }}
                //required
                error={errorSet.email[0]}
                name="email"
                type="email"
                onChange={handleChange}
                value={help.email}
                variant="standard"
                helperText={
                  errorSet.email[0] === true ? 'Enter valid email' : ''
                }
                InputLabelProps={{ style: { fontSize: 12, color: '#212121' } }}
                disabled={true}
                onBlur={checkForm}
                InputProps={{
                  readOnly: true
                }}
              />
            </Grid>
            {/* <Grid item md={12} xs={12} style={{ padding: '5px 16px' }}>
              
            </Grid> */}
          </Grid>
          <Grid
            item
            md={12}
            xs={12}
            style={{ padding: '8px 0px 0px', marginTop: '-5px' }}
          >
            <FormControlLabel
              name="attachScreenShot"
              onChange={handleChange}
              value={help.attachScreenShot}
              control={<Checkbox color="primary" disabled={checkDisable} />}
              label="Click to automatically attach a screenshot of this page."
            />
          </Grid>
          <Grid
            item
            md={12}
            xs={12}
            style={{ padding: '0px 0px 0px', marginTop: '-5px' }}
          >
            <FormControlLabel
              name="technical"
              onChange={handleChange}
              value={help.technical}
              control={<Checkbox color="primary" />}
              label="Technical or functional issue."
            />
          </Grid>
          <Grid
            item
            md={12}
            xs={12}
            style={{ padding: '0px 0px 0px', marginTop: '-5px' }}
          >
            <FormControlLabel
              name="improvement"
              onChange={handleChange}
              value={help.improvement}
              control={<Checkbox color="primary" />}
              label="Website improvement idea."
            />
          </Grid>
          <Grid
            item
            md={12}
            xs={12}
            style={{ padding: '0px 0px 0px', marginTop: '-5px' }}
          >
            <FormControlLabel
              name="something"
              onChange={handleChange}
              value={help.something}
              control={<Checkbox color="primary" />}
              label="Something else."
            />
          </Grid>
          <Grid
            item
            md={12}
            xs={12}
            style={{ padding: '0px 0px 0px', marginTop: '-5px' }}
          >
            <FormControlLabel
              name="comment"
              onChange={handleChange}
              value={help.comment}
              control={<Checkbox color="primary" />}
              label="General comment."
            />
          </Grid>
          <Grid
            item
            md={12}
            xs={12}
            style={{ padding: '0px 0px', marginTop: '-5px' }}
          >
            <InputLabel style={{ fontSize: 10.1, paddingBottom: 4 }}>
              Description <span className="mandatorySign">*</span>
            </InputLabel>
            <TextareaAutosize
              rowsMin={3}
              rowsMax={3}
              placeholder="Maximum 256 letters"
              fullWidth
              style={{
                padding: '4px',
                fontSize: '14px',
                width: '100%',
                resize: 'vertical'
              }}
              required
              error={errorSet.description[0]}
              name="description"
              onChange={handleChange}
              onKeyUp={handleChangeDescription}
              value={help.description}
              variant="standard"
              helperText={
                errorSet.description[0] === true
                  ? 'Enter valid description'
                  : ''
              }
              maxLength={257}
              // onKeyDown={checkForm}
              // inputProps={{ maxLength: 100 }}
            />
            <label
              style={{
                visibility:
                  errorSet.description[0] === true ? 'visible' : 'hidden',
                color: '#e53935',
                fontSize: '11px'
              }}
            >
              Enter valid description
            </label>

            {/* <TextField
                fullWidth
                // margin="dense"
                label="Description"
                style={{ fontSize: '14px' }}
                required
                error={errorSet.description[0]}
                helperText={false}
                name="description"
                onChange={handleChange}
                value={help.description}
                variant="standard"
                helperText={
                  errorSet.description[0] === true ? 'Enter valid description' : ''
                }
              /> */}
          </Grid>
          {/* {help.attachScreenShot && (
            <div
              id="canvaseeee"
              style={{
                position: 'relative',
                height: '40px'
              }}
            >
              <img
                src={encodeURI(canvased)}
                style={{
                  border: ' 1px solid #333',
                  borderRadius: '5px',
                  position: 'absolute',
                  height: '50px',
                  width: '100px',
                  top: '90%',
                  left: '50%',
                  transform: 'translate(-50%,-50%)'
                }}
              />
            </div>
          )} */}
          {/* {image && (
            <div className="imageContainer">
              <img 
                style={{
                  border: ' 1px solid #333',
                  borderRadius: '5px',
                  position: 'absolute',
                  height: '50px',
                  width: '100px',
                  top: '92%',
                  left: '50%',
                  transform: 'translate(-50%,-50%)',
                  // background: '#fff'
                }}
                src={image}
             />
            </div>
          )} */}
          {/*  <Typography
            align="center"
            className={classes.contact}
            variant="subtitle2"
          >
            Attaching a screenshot for better understanding.
           {' '}
            <Link
              color="secondary"
              component={RouterLink}
              to="#"
            >
              Contact us
            </Link>
            {' '}
            for information about more enterprise options. 
          </Typography>*/}
        </div>
        <div>
          <Button
            variant="contained"
            className={'close-btn'}
            onClick={onclickClose}
            style={{ marginLeft: 15 }}
            disabled={checkDisable}
          >
            CLOSE
          </Button>
          {/* <Button
            className={classes.closeButton}
            onClick={onClose}
            variant="contained"
          >
            Close
          </Button> */}
          {/* </div>
        <div className={classes.actions} style={{ position: 'relative' }}> */}
        </div>
        {image && (
          <div
            className="imageContainer"
            style={{ width: '100%', marginTop: -15 }}
          >
            <img
              style={{
                border: ' 1px solid #333',
                borderRadius: '5px',
                position: 'absolute',
                height: '50px',
                width: '100px',
                // top: '92%',
                left: '50%',
                transform: 'translate(-50%,-50%)'
                // background: '#fff'
              }}
              src={image}
            />
          </div>
        )}
        <div style={{ marginTop: -35, paddingLeft: 450, width: '100%' }}>
          {/* <Button
            className={classes.startButton}
            onClick={submitHelp}
            variant="contained"
          >
            SEND
          </Button> */}
          <Button
            onClick={submitHelp}
            variant="contained"
            className={'send-btn'}
            disabled={disabled}
            style={{ backgroundColor: '#fff' }}
          >
            SEND
          </Button>
          {loadImg && !image && (
            <div
              style={{ right: '54%', marginTop: '-30%', position: 'absolute' }}
            >
              <LoaderSkeleton />
            </div>
          )}
          {isLoading && (
            <div
              style={{ right: '54%', marginTop: '-30%', position: 'absolute' }}
            >
              <LoaderSkeleton />
            </div>
          )}
        </div>
      </div>
    </Dialog>
  );
}

HelpForm.propTypes = {
  className: PropTypes.string,
  page: PropTypes.string,
  onClose: PropTypes.func,
  canvased: PropTypes.object,
  open: PropTypes.bool
};

export default HelpForm;
