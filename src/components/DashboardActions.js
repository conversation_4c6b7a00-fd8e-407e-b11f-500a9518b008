import {
  Divide<PERSON>,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  Box,
  LinearProgress,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardHeader
} from '@material-ui/core';

import DoneIcon from '@material-ui/icons/Done';
import EditIcon from '@material-ui/icons/Edit';
import FilterListIcon from '@material-ui/icons/FilterList';
import RestoreIcon from '@material-ui/icons/Restore';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import React, { memo, useEffect, useState } from 'react';
import {
  GET_GOAL_SETTINGS_BY_GROUP_NAME,
  UPDATE_GOAL_SETTINGS_BY_GROUP_NAME
} from 'src/graphql/queries';
import {
  getLaborTotalOpportunity,
  getPartsTotalOpportunity,
  getELRTotalOpportunity,
  getLaborOpportunityBaseline,
  getpartsOpportunityBaseline,
  getElrOpportunityBaseline
} from 'src/utils/hasuraServices';
import makeApolloClient from 'src/utils/apolloRootClientPostgres';
import makeApolloClientWrite from 'src/utils/apolloRootClientPostgresWrite';
import Compare from '@material-ui/icons/Compare';
import { useHistory } from 'react-router';
import { getLatestClosedDate } from 'src/utils/hasuraServices';
import Checkbox from '@material-ui/core/Checkbox';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { red } from '@material-ui/core/colors';
import { checkClosedDateInCurrentMonth } from 'src/utils/Utils';
import InputBase from '@material-ui/core/InputBase';
import Chip from '@material-ui/core/Chip';
import moment from 'moment';
import { styled } from '@material-ui/core/styles';
import Paper from '@material-ui/core/Paper';
import SaveIcon from '@material-ui/icons/Save';
import { traceSpan } from 'src/utils/OTTTracing';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;
require('dotenv').config();

const useStyles = makeStyles(theme => ({
  select: {
    '&:before': {
      borderColor: '#FFF'
    },
    '&:after': {
      borderColor: '#FFF'
    }
  },
  icon: {
    fill: '#FFF'
  },
  switch: {
    marginLeft: 0,
    flexDirection: 'row-reverse',
    display: 'inline-flex',
    marginTop: -30,
    marginRight: 176
  },
  volumeOpportunity: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#FFF'
  },
  grossOpportunity: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#FFF',
    textAlign: 'center'
  },
  totalOpportunity: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityCompetitive: {
    fontWeight: 'bold',
    //  fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityMaintenance: {
    fontWeight: 'bold',
    // fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityRepair: {
    fontWeight: 'bold',
    //  fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityTotal: {
    fontWeight: 'bold',
    //fontSize: 14,
    color: '#FFF'
  }
}));
const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'center',
  color: '#FFF'
}));
function DashboardActions({
  className,
  filterCharts,
  resetDashboard,
  saveClick,
  zoomed,
  setTitle,
  checkSuccess,
  showCurrentMonth,
  ...rest
}) {
  const classes = useStyles();

  const [formState, setFormState] = useState({});
  const [chartData, setChartData] = useState([]);
  const [editForm, setEditForm] = useState(false);

  const storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  const history = useHistory();
  const [isLoading, setIsLoading] = useState(false);
  const [updateState, setUpdateState] = useState(0);
  const [closedDate, setClosedDate] = useState('');
  const [checked, setChecked] = useState(false);
  const [openDate, setOpenDate] = useState('');
  const [click, setClick] = useState(0);

  let [rowData, setTotalOpportunity] = useState('');
  let [baseline, setBaseline] = useState('');
  useEffect(() => {
    if (rest.goalSettings) {
      getGoalDetails(rest.chartGroup);
      getTotalOpportunity(rest.chartGroup);
      getOpportunityBaseLine(rest.chartGroup);
    }
  }, [rest.goalSettings]);
  const handleResetLayout = () => {
    resetDashboard(true);
  };
  const handleStoreComapare = () => {
    history.push({
      pathname: '/StoreComparison',
      SelectedLocation: window.location.pathname
    });
  };
  const handleFormEdit = event => {
    // console.log('enter values=',formState,click);
    if (editForm) {
      if (click == formState) {
        setClick(formState);
        setEditForm(false);
      } else {
        setEditForm(false);
        checkSuccess(true);
        saveFormData(event);
      }
    } else {
      setClick(formState);
      setEditForm(true);
    }
  };
  // const handleFormEdit = event => {
  //   if (editForm) {
  //     setEditForm(false);
  //     checkSuccess(true);
  //     saveFormData(event);
  //   } else {
  //     setEditForm(true);
  //   }
  // };

  const saveFormData = async event => {
    event.preventDefault();

    Object.keys(formState).forEach(function(key) {
      console.table('Key : ' + key + ', Value : ' + formState[key]);
      updateGoalSettings(key, formState[key]);
    });
    //saveClick(true);
  };

  async function updateGoalSettings(goal_name, goal_value) {
    setIsLoading(true);
    setUpdateState(1);
    const client = makeApolloClientWrite;
    await client
      .mutate({
        mutation: UPDATE_GOAL_SETTINGS_BY_GROUP_NAME,
        variables: {
          goal_name: goal_name,
          group_name: rest.chartGroup,
          goal_value: goal_value,
          store_id: storeId
        }
      })
      .then(result => {
        setTimeout(() => {
          setUpdateState(2);
          setIsLoading(false);
          getTotalOpportunity(rest.chartGroup);
        }, 8000);
      });
  }

  useEffect(() => {
    if (updateState === 1) {
      filterCharts(2);
    } else if (updateState === 2) {
      filterCharts(3);
    }
    if (showCurrentMonth) {
      if (
        checked == true ||
        localStorage.getItem('showCurrentMonth') == 'true'
      ) {
        showCurrentMonth(true);
        setChecked(true);
      } else {
        showCurrentMonth(false);
        setChecked(false);
      }
    }

    getClosedDate();
  }, [updateState, checked]);

  const getClosedDate = () => {
    console.log('pppp');
    getLatestClosedDate(result => {
      if (result) {
        var resultDate = '';
        var openDate = '';
        var Date1 = result[0].value;
        var Date2 = result[1].value;
        if (new Date(Date1) >= new Date(Date2)) {
          resultDate = Date1;
          openDate = Date2;
        } else {
          resultDate = Date2;
          openDate = Date1;
        }
        setClosedDate(resultDate);
        setOpenDate(openDate);
      }
    });
  };

  const handleFormChange = event => {
    event.persist();
    setFormState(prevFormState => ({
      ...prevFormState,
      [event.target.name]: event.target.value
    }));
  };

  const [filterSelected, setFilterSelected] = useState(2);

  const handleclick = val => {
    filterCharts(val);
  };

  const handleChange = event => {
    setFilterSelected(event.target.value);
    handleclick(event.target.value);
  };
  function getOpportunityBaseLine(groupName) {
    if (groupName == 'Labor') {
      getLaborOpportunityBaseline(result => {
        if (
          result.data.statelessDbdCpOpportunityLaborGetLaborOpportunityBaseline
            .nodes.length > 0
        ) {
          setBaseline(
            result.data
              .statelessDbdCpOpportunityLaborGetLaborOpportunityBaseline
              .nodes[0]
          );
        }
      });
    } else if (groupName == 'Parts') {
      getpartsOpportunityBaseline(result => {
        if (
          result.data.statelessDbdCpOpportunityPartsGetPartsOpportunityBaseline
            .nodes.length > 0
        ) {
          setBaseline(
            result.data
              .statelessDbdCpOpportunityPartsGetPartsOpportunityBaseline
              .nodes[0]
          );
        }
      });
    } else if (groupName == 'ELR') {
      getElrOpportunityBaseline(result => {
        if (
          result.data.dbdCpOpportunityElrGetElrOpportunityBaseline.nodes
            .length > 0
        ) {
          setBaseline(
            result.data.dbdCpOpportunityElrGetElrOpportunityBaseline.nodes[0]
          );
        }
      });
    }
  }

  function getTotalOpportunity(groupName) {
    if (groupName == 'Labor') {
      getLaborTotalOpportunity(result => {
        if (
          result.data
            .statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunity
            .nodes.length > 0
        ) {
          setTotalOpportunity(
            result.data
              .statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunity
              .nodes[0]
          );
        }
      });
    } else if (groupName == 'ELR') {
      getELRTotalOpportunity(result => {
        if (
          result.data
            .statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByCategory
            .nodes.length > 0
        ) {
          setTotalOpportunity(
            result.data
              .statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByCategory
              .nodes[0]
          );
        }
      });
    } else {
      getPartsTotalOpportunity(result => {
        if (
          result.data
            .dbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunity.nodes
            .length > 0
        ) {
          setTotalOpportunity(
            result.data
              .dbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunity
              .nodes[0]
          );
        }
      });
    }
  }
  function getGoalDetails(groupName) {
    const start = new Date();
    const client = makeApolloClient;
    client
      .query({
        query: GET_GOAL_SETTINGS_BY_GROUP_NAME,
        variables: { group_name: groupName, store_id: storeId }
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/LaborGrossAndVolumeOpportunity',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_GOAL_SETTINGS_BY_GROUP_NAME',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        if (result.data.statefulCcPhysicalRwGoalSettings.nodes.length > 0) {
          setChartData(result.data.statefulCcPhysicalRwGoalSettings.nodes);
          var formData = {};
          result.data.statefulCcPhysicalRwGoalSettings.nodes.map(item => {
            formData[item.goalName] = item.goalValue;
          });
          setFormState(formData);
        }
      });
  }

  const handleChangeCurrentMonth = event => {
    setChecked(event.target.checked);
    localStorage.setItem('showCurrentMonth', event.target.checked);
  };

  return (
    <>
      <div>
        <Grid
          container
          spacing={3}
          style={
            localStorage.getItem('realm') == 'demo_store' &&
            window.location.pathname == '/MyFavorites'
              ? { paddingLeft: '16px', paddingRight: '16px', marginTop: '3px' }
              : {
                  paddingLeft: '16px',
                  paddingRight: '16px',
                  display: zoomed ? 'none' : 'flex',
                  height: '67px'
                }
          }
        >
          {!rest.goalSettings && (
            <Grid item md={4} xs={12}>
              <Typography variant="h5">{setTitle}</Typography>
            </Grid>
          )}
          {chartData.length > 0 && (
            <>
              <Grid item xs={12} style={{ marginTop: '12px' }}>
                <TextField
                  InputLabelProps={{ shrink: true }}
                  label={
                    rest.chartGroup == 'ELR'
                      ? 'Last Qtr – ELR Comp'
                      : 'Last Qtr - Hours/RO'
                  }
                  value={
                    rest.chartGroup == 'ELR'
                      ? baseline.lastQtrCompElr
                      : baseline.lastQtrHrsPerRo
                  }
                  InputProps={{
                    readOnly: true
                  }}
                  variant="outlined"
                  size="small"
                  style={{
                    marginTop: '-11px',
                    padding: 5,
                    float: 'left',
                    width: window.innerWidth == 1366 ? '13%' : '11%'
                    // display: rest.chartGroup == 'ELR' ? 'none' : 'block'
                  }}
                />
                <TextField
                  InputLabelProps={{ shrink: true }}
                  label={
                    rest.chartGroup == 'Labor'
                      ? 'Last Qtr - Labor GP%'
                      : rest.chartGroup == 'Parts'
                      ? 'Last Qtr - Parts GP%'
                      : rest.chartGroup == 'ELR'
                      ? 'Last Qtr – ELR Maint'
                      : ''
                  }
                  value={
                    rest.chartGroup == 'ELR'
                      ? baseline.lastQtrMaintElr
                      : baseline.lastQtrGpPercentage
                  }
                  InputProps={{
                    readOnly: true
                  }}
                  variant="outlined"
                  size="small"
                  style={{
                    marginTop: '-11px',
                    padding: 5,
                    float: 'left',
                    width: window.innerWidth == 1366 ? '13%' : '11%'
                    // display: rest.chartGroup == 'ELR' ? 'none' : 'block'
                  }}
                />
                <TextField
                  InputLabelProps={{ shrink: true }}
                  label={'Last Qtr – ELR Repair'}
                  value={baseline.lastQtrRepairElr}
                  InputProps={{
                    readOnly: true
                  }}
                  variant="outlined"
                  size="small"
                  style={{
                    marginTop: '-11px',
                    padding: 5,
                    float: 'left',
                    width: window.innerWidth == 1366 ? '13%' : '11%',
                    display: rest.chartGroup == 'ELR' ? 'block' : 'none'
                  }}
                />
                <div style={{ marginTop: '-7px', display: 'flex' }}>
                  {lodash.uniqBy(chartData, 'goalName').map((item, index) => {
                    return (
                      <TextField
                        disabled={!editForm}
                        onChange={handleFormChange}
                        size="small"
                        name={item.goalName}
                        key={index}
                        autoFocus={true}
                        value={formState[item.goalName]}
                        type="number"
                        id={item.goalId}
                        label={
                          item.goalName == 'Target Labor Gross Profit %'
                            ? 'Target Labor GP%'
                            : item.goalName == 'Target Parts Gross Profit %'
                            ? 'Target Parts GP%'
                            : item.goalName
                        }
                        style={{
                          width: window.innerWidth == 1366 ? '18%' : '15%',
                          marginRight: 10
                        }}
                        variant="outlined"
                      />
                    );
                  })}
                  {!editForm && (
                    <>
                      <Tooltip
                        title="Edit Goals"
                        disabled={isLoading ? true : false}
                        onClick={handleFormEdit}
                      >
                        <Button
                          style={{
                            backgroundColor: isLoading
                              ? 'grey'
                              : Dealer === 'Armatus'
                              ? '#003d6b'
                              : '#C2185B',
                            color: '#FFF'
                            //marginTop: 5
                          }}
                          onClick={handleFormEdit}
                          disabled={isLoading ? true : false}
                        >
                          <EditIcon color="white" style={{ width: 20 }} />
                          <span
                            style={{
                              textTransform: 'capitalize',
                              fontSize: 12,
                              paddingLeft: 4
                            }}
                          >
                            Edit
                          </span>
                        </Button>
                      </Tooltip>
                    </>
                  )}
                  {editForm && (
                    <>
                      <Tooltip
                        title="Save Goals"
                        disabled={isLoading ? true : false}
                        onClick={handleFormEdit}
                      >
                        <Button
                          style={{
                            backgroundColor:
                              Dealer === 'Armatus' ? '#003d6b' : '#C2185B',
                            color: '#FFF',
                            marginTop: 5
                          }}
                          onClick={handleFormEdit}
                        >
                          <SaveIcon color="white" style={{ width: 17 }} />
                          <span
                            style={{
                              textTransform: 'capitalize',
                              fontSize: 12,
                              paddingLeft: 4
                            }}
                          >
                            Save
                          </span>
                        </Button>
                      </Tooltip>
                    </>
                  )}
                </div>
              </Grid>
            </>
          )}
          {setTitle != 'CP Labor Gross & Volume Opportunity' &&
          setTitle != 'CP Parts Gross & Volume Opportunity' ? (
            <Grid item md={8} xs={12} style={{ textAlign: 'right' }}>
              {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
              window.location.pathname == '/CPOverview' ? (
                <Tooltip
                  onClick={handleStoreComapare}
                  title="Compare Stores"
                  style={{ marginBottom: 5 }}
                >
                  <IconButton size="small">
                    <Compare color="primary" />
                  </IconButton>
                </Tooltip>
              ) : (
                ''
              )}

              {closedDate ? (
                <Typography
                  variant="h6"
                  color="primary"
                  align="right"
                  style={{ marginRight: 37 }}
                >
                  {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
                </Typography>
              ) : (
                ''
              )}
              {checkClosedDateInCurrentMonth(closedDate, openDate) &&
              (setTitle == 'Labor Overview' || setTitle == 'Parts Overview') ? (
                <Tooltip
                  title={checked ? 'Hide Partial Month' : 'Show Partial Month'}
                  style={{ marginLeft: 200, float: 'right' }}
                >
                  <FormGroup>
                    <FormControlLabel
                      className={classes.switch}
                      control={
                        <Switch
                          checked={checked}
                          onChange={handleChangeCurrentMonth}
                          name="checkedA"
                          inputProps={{ 'aria-label': 'secondary checkbox' }}
                        />
                      }
                      label={
                        <span style={{ fontWeight: 500 }}>
                          {checked
                            ? 'Hide Partial Month'
                            : 'Show Partial Month'}
                        </span>
                      }
                      value="start"
                      labelPlacement="start"
                    />
                  </FormGroup>
                </Tooltip>
              ) : (
                ''
              )}
              {/* {!rest.noFilters && (
              <>
                <IconButton size="small">
                  <FilterListIcon color="primary" />
                </IconButton>
                <Select
                  onChange={handleChange}
                  disableUnderline
                  className={classes.select}
                  labelId="demo-simple-select-label"
                  id="demo-simple-select"
                  value={filterSelected}
                >
                  <MenuItem value={2}>Last Year</MenuItem>
                  {!rest.restrictFilters && (
                    <MenuItem value={1}>Last 6 Months</MenuItem>
                  )}
                </Select>
              </>
            )}  */}

              <Tooltip
                onClick={handleResetLayout}
                title="Reset Layout"
                style={
                  checkClosedDateInCurrentMonth(closedDate, openDate) &&
                  (setTitle == 'Labor Overview' || setTitle == 'Parts Overview')
                    ? { marginTop: -46, marginRight: -580 }
                    : { marginTop: -43, marginLeft: 5 }
                }
              >
                <IconButton size="small">
                  <RestoreIcon />
                </IconButton>
              </Tooltip>
            </Grid>
          ) : closedDate ? (
            <React.Fragment>
              <Grid xs={12} item style={{ textAlign: 'right' }}>
                <Typography
                  variant="h6"
                  color="primary"
                  align="right"
                  style={{
                    marginRight: 37,
                    marginTop:
                      window.location.pathname ==
                        '/LaborGrossAndVolumeOpportunity' ||
                      window.location.pathname ==
                        '/PartsGrossAndVolumeOpportunity'
                        ? 16
                        : 25,
                    marginTop: -57
                  }}
                >
                  {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
                </Typography>

                <Tooltip
                  onClick={handleResetLayout}
                  title="Reset Layout"
                  style={{ marginTop: -43 }}
                >
                  <IconButton size="small">
                    <RestoreIcon color="primary" />
                  </IconButton>
                </Tooltip>
              </Grid>
            </React.Fragment>
          ) : (
            <Tooltip
              onClick={handleResetLayout}
              title="Reset Layout"
              style={{ marginTop: -2, marginLeft: 310 }}
            >
              <IconButton size="small">
                <RestoreIcon color="primary" />
              </IconButton>
            </Tooltip>
          )}
        </Grid>
        <Divider></Divider>
      </div>
      {rest.goalSettings &&
      rowData &&
      (rest.chartGroup == 'Labor' || rest.chartGroup == 'Parts') ? (
        <div
          className="contentHeader"
          style={{
            marginRight: 14,
            marginTop: 10,
            lineHeight: 2,
            marginLeft: 10
          }}
        >
          <Typography
            color="primary"
            style={{
              fontSize: 12,
              fontWeight: 'bold'
            }}
          >
            {' '}
            Annual Opportunity
          </Typography>
          <hr></hr>
          <Grid container spacing={2} style={{ marginTop: '-5px' }}>
            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span>Hours/RO :</span>
                <span className={classes.volumeOpportunity}>
                  &nbsp;&nbsp;
                  {rowData.totalLbrvolumeOpportunity > 0 ||
                  rowData.totalPrtvolumeOpportunity > 0
                    ? '$'
                    : '-$'}
                  {(rest.chartGroup == 'Labor'
                    ? Math.abs(Math.round(rowData.totalLbrvolumeOpportunity))
                    : Math.abs(Math.round(rowData.totalPrtvolumeOpportunity))
                  )
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                </span>
              </Item>
            </Grid>
            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span>Gross Profit % :</span>
                <span className={classes.grossOpportunity}>
                  &nbsp;&nbsp;
                  {rowData.totalLbrgrossOpportunity > 0 ||
                  rowData.totalPrtgrossOpportunity > 0
                    ? '$'
                    : '-$'}
                  {(rest.chartGroup == 'Labor'
                    ? Math.abs(Math.round(rowData.totalLbrgrossOpportunity))
                    : Math.abs(Math.round(rowData.totalPrtgrossOpportunity))
                  )
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                </span>
              </Item>
            </Grid>

            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span>Joint:</span>
                <span className={classes.totalOpportunity}>
                  &nbsp;&nbsp;
                  {rowData.totalLbrjointOpportunity > 0 ||
                  rowData.totalPrtjointOpportunity > 0
                    ? '$'
                    : '-$'}
                  {(rest.chartGroup == 'Labor'
                    ? Math.abs(Math.round(rowData.totalLbrjointOpportunity))
                    : Math.abs(Math.round(rowData.totalPrtjointOpportunity))
                  )
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                </span>
              </Item>
            </Grid>

            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span>Combined :</span>
                <span className={classes.totalOpportunity}>
                  &nbsp;&nbsp;
                  {rowData.totalLbropportunity > 0 ||
                  rowData.totalPrtsopportunity > 0
                    ? '$'
                    : '-$'}
                  {(rest.chartGroup == 'Labor'
                    ? Math.abs(Math.round(rowData.totalLbropportunity))
                    : Math.abs(Math.round(rowData.totalPrtsopportunity))
                  )
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                </span>
              </Item>
            </Grid>
          </Grid>
        </div>
      ) : rest.goalSettings && rest.chartGroup == 'ELR' && rowData ? (
        <div
          className="contentHeader"
          style={{
            marginRight: 14,
            marginTop: 6,
            lineHeight: 2,
            marginLeft: 10
          }}
        >
          <Typography
            color="primary"
            style={{
              fontSize: 12,
              fontWeight: 'bold'
            }}
          >
            {' '}
            Annual Opportunity
          </Typography>
          <hr></hr>
          <Grid container spacing={2} style={{ marginTop: '-5px' }}>
            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span>
                  Competitive :
                  {window.innerWidth < 1400 ? (
                    <span className={classes.elrOpportunityCompetitive}>
                      &nbsp;
                      {rowData.totalCompetitiveOpportunity > 0 ? '$' : '-$'}
                      {Math.abs(Math.round(rowData.totalCompetitiveOpportunity))
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  ) : (
                    <span className={classes.elrOpportunityCompetitive}>
                      &nbsp;&nbsp;&nbsp;
                      {rowData.totalCompetitiveOpportunity > 0 ? '$' : '-$'}
                      {Math.abs(Math.round(rowData.totalCompetitiveOpportunity))
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  )}
                </span>
              </Item>
            </Grid>
            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span
                  style={{ marginLeft: window.innerWidth < 1400 ? -8 : '' }}
                >
                  Maintenance :
                  {window.innerWidth < 1400 ? (
                    <span
                      className={classes.elrOpportunityMaintenance}
                      style={{ marginLeft: -2 }}
                    >
                      &nbsp;
                      {rowData.totalMaintenanceOpportunity > 0 ? '$' : '-$'}
                      {Math.abs(Math.round(rowData.totalMaintenanceOpportunity))
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  ) : (
                    <span className={classes.elrOpportunityMaintenance}>
                      &nbsp;&nbsp;&nbsp;
                      {rowData.totalMaintenanceOpportunity > 0 ? '$' : '-$'}
                      {Math.abs(Math.round(rowData.totalMaintenanceOpportunity))
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  )}
                </span>
              </Item>
            </Grid>
            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span>
                  Repair :
                  <span className={classes.elrOpportunityRepair}>
                    &nbsp;&nbsp;
                    {rowData.totalRepairOpportunity > 0 ? '$' : '-$'}
                    {Math.abs(Math.round(rowData.totalRepairOpportunity))
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  </span>
                </span>
              </Item>
            </Grid>
            <Grid item xs={3}>
              <Item
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
              >
                <span>
                  Total :
                  <span className={classes.elrOpportunityTotal}>
                    &nbsp;&nbsp;
                    {rowData.totalOpportunity > 0 ? '$' : '-$'}
                    {Math.abs(Math.round(rowData.totalOpportunity))
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  </span>
                </span>
              </Item>
            </Grid>
          </Grid>
        </div>
      ) : (
        ''
      )}
    </>
  );
}

DashboardActions.propTypes = {
  className: PropTypes.string,
  filterCharts: PropTypes.func,
  setTitle: PropTypes.string,
  resetDashboard: PropTypes.func,
  checkSuccess: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default memo(DashboardActions);
