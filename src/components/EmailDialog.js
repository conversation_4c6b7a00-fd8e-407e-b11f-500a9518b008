import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import FormGroup from '@material-ui/core/FormGroup';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField
} from '@material-ui/core';
import Checkbox from '@material-ui/core/Checkbox';
import $, { param } from 'jquery';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles, withStyles } from '@material-ui/styles';
import PropTypes, { element } from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import MuiTableCell from '@material-ui/core/TableCell';
import { getOrSetKPIScorecardEmail } from 'src/utils/hasuraServices';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import FormHelperText from '@material-ui/core/FormHelperText';

import _ from 'lodash';

var lodash = require('lodash');
const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);
const useStyles = makeStyles(theme => ({
  root: {},
  // backDrop: {
  //   backdropFilter: 'blur(3px)',
  //   backgroundColor: 'rgba(0, 0, 0, 0)',
  //   margin: 20
  // },
  // scrollPaper: { backgroundColor: '#fff' },
  menuItem: {
    padding: 0
  },
  formControl: {
    minWidth: 110
  },
  formControl1: {
    minWidth: 160
  },
  cardControl: {
    padding: 0
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),
    fontSize: 12
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  paper: {
    '@media (max-width: 1920px)': {
      maxWidth: '1150px',
      maxHeight: '700px'
    },

    '@media (max-width: 1440px)': {
      maxWidth: '1500px',
      maxHeight: '610px'
    },

    '@media (max-width: 1280px)': {
      maxWidth: '1500px',
      maxHeight: '600px'
    },
    '@media (max-width: 2304px)': {
      maxWidth: '1350px',
      maxHeight: '900px'
    }
  },
  paperTranches: {
    maxWidth: '1250px',
    maxHeight: '800px'
  },
  paperItemization: {
    maxWidth: '100%',
    maxHeight: '1400px'
  },
  flexItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  paperBar: {
    // maxWidth: '1300px',
    maxWidth: '100%',
    maxHeight: '1400px'
    // marginLeft: '17%',
    // marginTop: '8%',
    // marginRight: '0%'
  },
  divDisplay: {
    //marginBottom: 45
  },
  divDisplayAdv: {
    //marginTop: 47
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function EmailDialog({
  open,
  handlePopupClose,
  emailListDialog,
  active,
  type,
  emailId,
  emailIdAdv,
  statusInsert,
  advisorSelected,
  selectedAdvisorId,
  flgInsert,
  mailFrequency,
  mailStatus,
  recipientId,
  scheduledOn,
  serviceAdvisor
}) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(open);
  const [emailList, setEmailList] = useState([]);
  const [selectedOptions, setSelectedOptions] = useState([]);
  const [selectedDay, setSelectedDay] = useState([]);
  const [selectedDate, setSelectedDate] = useState([]);
  const [selectedOptionsAdv, setSelectedOptionsAdv] = useState([]);
  const [selectedDayAdv, setSelectedDayAdv] = useState([]);
  const [selectedDateAdv, setSelectedDateAdv] = useState([]);
  const [alertMsg, setAlertMsg] = useState('');
  const [openAlert, setOpenAlert] = React.useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState('daily');
  const [selectedScheduleAdv, setSelectedScheduleAdv] = useState('daily');
  const [advisorId, setAdvisorId] = useState(null);
  const [advisor, setAdvisor] = useState(null);
  const [storeId, setStoreId] = useState(null);
  const [emailvalue, setEmailvalue] = useState('');
  const [error, setError] = useState('');
  const [errorChecked, setErrorChecked] = useState('');
  const [requiredText, setRequiredText] = useState(false);
  const [requiredSelect, setRequiredSelect] = useState(false);
  const [requiredSelectOptions, setRequiredSelectOptions] = useState(false);
  const [requiredSelectOptionsAdv, setRequiredSelectOptionsAdv] = useState(
    false
  );
  const [checked, setChecked] = useState(false);
  const [checkedAdvisor, setCheckedAdvisor] = useState(false);
  const [requiredSelectAdv, setRequiredSelectAdv] = useState(false);
  const [filterDisabled, setfilterDisabled] = useState(true);
  const [deleteflg, setDeleteflg] = useState(false);
  const [rowDataPreviousAdv, setRowDataPreviousAdv] = useState([]);

  useEffect(() => {
    setOpenDialog(open);
    setEmailList(emailListDialog);
    handleSetEmailvalues();
  }, [open, emailList]);
  // [open, emailList]
  let days = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday'
  ];
  const handleSetEmailvalues = () => {
    console.log('123', type);
    if (type == 'Both') {
      setfilterDisabled(false);
      setEmailvalue(emailListDialog[0] && emailListDialog[0].recipientId);
      console.log('emaillist2', emailListDialog);
      emailListDialog.map(e => {
        if (e.serviceAdvisor == 'store_goal') {
          setStoreId(e.id);
          setChecked(true);
          setSelectedSchedule(e.mailFrequency);

          if (e.mailFrequency == 'weekly') {
            setSelectedDay(e.scheduledOn);
          }
          if (e.mailFrequency == 'monthly') {
            setSelectedDate(e.scheduledOn);
          }
          if (e.mailFrequency == 'biweekly') {
            var eList = [];
            var days = e.scheduledOn.split(',');
            for (let day of days) {
              day = day.charAt(0).toUpperCase() + day.substr(1);
              eList.push(day);
            }
            setSelectedOptions(eList);
          }
          setTimeout(() => {
            if (document.getElementById(e.mailFrequency)) {
              document.getElementById(e.mailFrequency).style.display = 'block';
            }
          }, 500);
        } else {
          setAdvisorId(e.id);
          setAdvisor(e.serviceAdvisor);
          setCheckedAdvisor(true);

          setSelectedScheduleAdv(e.mailFrequency);
          if (e.mailFrequency == 'weekly') {
            setSelectedDayAdv(e.scheduledOn);
          }
          if (e.mailFrequency == 'monthly') {
            setSelectedDateAdv(e.scheduledOn);
          }
          if (e.mailFrequency == 'biweekly') {
            var eList = [];
            var days = e.scheduledOn.split(',');
            for (let day of days) {
              day = day.charAt(0).toUpperCase() + day.substr(1);
              eList.push(day);
            }
            setSelectedOptionsAdv(eList);
          }
          if (e.mailFrequency != 'daily') {
            setTimeout(() => {
              if (document.getElementById('dailyadv')) {
                document.getElementById('dailyadv').style.display = 'none';
              }
            }, 500);
          }

          setTimeout(() => {
            if (document.getElementById(e.mailFrequency + 'adv')) {
              document.getElementById(e.mailFrequency + 'adv').style.display =
                'block';
            }
          }, 500);
        }
      });
    }

    if (typeof emailListDialog.id != 'undefined') {
      setEmailvalue(emailListDialog.recipientId);

      if (type == 'store_goal') {
        setfilterDisabled(false);
        setChecked(true);
        setStoreId(emailListDialog.id);
        setSelectedSchedule(emailListDialog.mailFrequency);
        console.log('emaillist3', emailListDialog.mailFrequency);
        if (emailListDialog.mailFrequency == 'weekly') {
          setSelectedDay(emailListDialog.scheduledOn);
        }
        if (emailListDialog.mailFrequency == 'monthly') {
          setSelectedDate(emailListDialog.scheduledOn);
        }
        if (emailListDialog.mailFrequency == 'biweekly') {
          var eList = [];
          var days = emailListDialog.scheduledOn.split(',');
          for (let day of days) {
            day = day.charAt(0).toUpperCase() + day.substr(1);
            eList.push(day);
          }
          setSelectedOptions(eList);
        }
        setTimeout(() => {
          if (document.getElementById(emailListDialog.mailFrequency)) {
            document.getElementById(
              emailListDialog.mailFrequency
            ).style.display = 'block';
          }
        }, 500);
        setSelectedScheduleAdv('daily');
        setTimeout(() => {
          if (
            document.getElementById('dailyadv') &&
            selectedScheduleAdv == 'daily'
          ) {
            document.getElementById('dailyadv').style.display = 'block';
          }
        }, 100);
      } else {
        setfilterDisabled(false);
        setCheckedAdvisor(true);
        setChecked(false);
        setAdvisorId(emailListDialog.id);
        setSelectedScheduleAdv(emailListDialog.mailFrequency);
        if (emailListDialog.mailFrequency == 'weekly') {
          setSelectedDayAdv(emailListDialog.scheduledOn);
        }
        if (emailListDialog.mailFrequency == 'monthly') {
          setSelectedDateAdv(emailListDialog.scheduledOn);
        }
        if (emailListDialog.mailFrequency == 'biweekly') {
          var eList = [];
          var days = emailListDialog.scheduledOn.split(',');
          for (let day of days) {
            day = day.charAt(0).toUpperCase() + day.substr(1);
            eList.push(day);
          }
          setSelectedOptionsAdv(eList);
        }
        if (emailListDialog.mailFrequency != 'daily') {
          setTimeout(() => {
            if (document.getElementById('dailyadv')) {
              document.getElementById('dailyadv').style.display = 'none';
            }
          }, 500);
        }
        setTimeout(() => {
          if (document.getElementById(emailListDialog.mailFrequency + 'adv')) {
            document.getElementById(
              emailListDialog.mailFrequency + 'adv'
            ).style.display = 'block';
          }
        }, 500);
        setSelectedSchedule('daily');
        setTimeout(() => {
          if (document.getElementById('daily') && selectedSchedule == 'daily') {
            document.getElementById('daily').style.display = 'block';
          }
        }, 100);
      }
    } else {
      if (type && flgInsert) {
        setSelectedSchedule('daily');
        setTimeout(() => {
          if (document.getElementById('daily') && selectedSchedule == 'daily') {
            document.getElementById('daily').style.display = 'block';
          }
        }, 100);
        if (type != 'store_goal' && flgInsert == true) {
          setSelectedScheduleAdv('daily');
          setTimeout(() => {
            if (
              document.getElementById('dailyadv') &&
              selectedScheduleAdv == 'daily'
            ) {
              document.getElementById('dailyadv').style.display = 'block';
            }
          }, 100);
        }
      }
    }
    var rowdataPrvAdv = [];

    rowdataPrvAdv.push({
      recipientid: emailListDialog.recipientId,
      mailfrequency: emailListDialog.mailFrequency,
      scheduledon: emailListDialog.scheduledOn,
      mailstatus: emailListDialog.mailStatus,
      id: emailListDialog.id ? emailListDialog.id : null,
      serviceadvisor: type,
      reporttype: 'advisor_individual_report'
    });
    setRowDataPreviousAdv(rowdataPrvAdv);
  };
  const resetStates = () => {
    setSelectedSchedule('');
    setSelectedDay('');
    setSelectedDate('');
    setSelectedOptions([]);
    setSelectedScheduleAdv('');
    setSelectedDayAdv('');
    setSelectedDateAdv('');
    setSelectedOptions([]);
    setEmailvalue('');
    setError('');
    setRequiredSelect(false);
    setRequiredSelectAdv(false);
    setRequiredSelectOptions(false);
    setRequiredSelectOptionsAdv(false);
    setRequiredText(false);
    setCheckedAdvisor(false);
    setChecked(false);
    setErrorChecked('');
    setStoreId('');
    setAdvisorId('');
    setfilterDisabled(true);
    setError('');
  };
  const handleOk = () => {
    setOpenDialog(false);
    handlePopupClose();
    if (document.getElementById(selectedSchedule))
      document.getElementById(selectedSchedule).style.display = 'none';
    resetStates();
  };
  const handleSaveEmail = (active, type, emailList) => {
    var rowDataEmail = [];
    var rowDataPrevious = [];
    var listMail = [];
    let noEmail = 0;
    let noDataChange = 0;
    let scheduledonValue;
    let scheduledonValueAdv;
    var deleteSet = [];
    //store
    if (checked) {
      if (
        selectedSchedule &&
        selectedSchedule == 'weekly' &&
        selectedDay &&
        selectedDay.length > 0
      )
        scheduledonValue = selectedDay.toLowerCase();
      if (
        selectedSchedule &&
        selectedSchedule == 'biweekly' &&
        selectedOptions.length > 0
      )
        scheduledonValue = selectedOptions.map(e => e.toLowerCase()).toString();
      if (selectedSchedule && selectedSchedule == 'daily')
        scheduledonValue = 'all';
      if (selectedSchedule && selectedSchedule == 'monthly')
        scheduledonValue = selectedDate;

      if (selectedSchedule == undefined || selectedSchedule == '') {
        setRequiredSelect('Please choose an option');
        noEmail = 1;
      }
      if (
        (selectedSchedule && scheduledonValue == '') ||
        typeof scheduledonValue == 'undefined' ||
        (selectedSchedule == 'biweekly' && selectedOptions.length != 2)
      ) {
        setRequiredSelectOptions(true);
        noEmail = 1;
      }
    }
    if (checkedAdvisor) {
      if (
        selectedScheduleAdv &&
        selectedScheduleAdv == 'weekly' &&
        selectedDayAdv.length > 0
      )
        scheduledonValueAdv = selectedDayAdv.toLowerCase();
      if (
        selectedScheduleAdv &&
        selectedScheduleAdv == 'biweekly' &&
        selectedOptionsAdv.length > 0
      )
        scheduledonValueAdv = selectedOptionsAdv
          .map(e => e.toLowerCase())
          .toString();
      if (selectedScheduleAdv && selectedScheduleAdv == 'daily')
        scheduledonValueAdv = 'all';
      if (selectedScheduleAdv && selectedScheduleAdv == 'monthly')
        scheduledonValueAdv = selectedDateAdv;

      if (selectedScheduleAdv == undefined || selectedScheduleAdv == '') {
        setRequiredSelectAdv('Please choose an option');
        noEmail = 1;
      }
      if (
        (selectedScheduleAdv && scheduledonValueAdv == '') ||
        typeof scheduledonValueAdv == 'undefined' ||
        (selectedScheduleAdv == 'biweekly' && selectedOptionsAdv.length != 2)
      ) {
        setRequiredSelectOptionsAdv(true);
        noEmail = 1;
      }
    }
    var isValid = emailvalue && validateEmail(emailvalue);
    if (!isValid) {
      noEmail = 1;
    }

    if ((emailvalue == undefined || emailvalue == '') && error == '') {
      setError(false);
      setRequiredText(true);
      noEmail = 1;
      console.log('advisor1');
    }
    if (!checked && !checkedAdvisor) {
      setErrorChecked('please choose an option ');

      noEmail = 1;
    }
    if (checked && !checkedAdvisor) {
      rowDataEmail.push({
        recipientid: emailvalue,
        mailfrequency: selectedSchedule,
        scheduledon: scheduledonValue,
        mailstatus: 'active',
        id:
          !deleteflg && emailList.id ? emailList.id : storeId ? storeId : null,
        serviceadvisor: 'store_goal',
        reporttype: 'advisor_individual_report'
      });
    }
    if (!checked && checkedAdvisor) {
      rowDataEmail.push({
        recipientid: emailvalue,
        mailfrequency: selectedScheduleAdv,
        scheduledon: scheduledonValueAdv,
        mailstatus: 'active',
        id:
          !deleteflg && emailList.id
            ? emailList.id
            : advisorId
            ? advisorId
            : null,
        serviceadvisor:
          type == 'Both'
            ? advisor
            : deleteflg && advisorSelected
            ? selectedAdvisorId
            : type,
        reporttype: 'advisor_individual_report'
      });
    }
    if (checked && checkedAdvisor) {
      let storeData = {
        recipientid: emailvalue,
        mailfrequency: selectedSchedule,
        scheduledon: scheduledonValue,
        mailstatus: 'active',
        id: storeId ? storeId : null,
        serviceadvisor: 'store_goal',
        reporttype: 'advisor_individual_report'
      };
      let advisorData = {
        recipientid: emailvalue,
        mailfrequency: selectedScheduleAdv,
        scheduledon: scheduledonValueAdv,
        mailstatus: 'active',
        id: advisorId ? advisorId : null,
        serviceadvisor:
          type == 'Both' ? advisor : advisorSelected ? selectedAdvisorId : type,
        reporttype: 'advisor_individual_report'
      };
      rowDataEmail = rowDataEmail.concat(storeData, advisorData);
      if (emailListDialog.length != undefined) {
        emailListDialog.map(e => {
          listMail.push({
            recipientid: e.recipientId,
            mailfrequency: e.mailFrequency,
            scheduledon: e.scheduledOn,
            mailstatus: e.mailStatus,
            id: e.id ? e.id : null,
            serviceadvisor: e.serviceAdvisor,
            reporttype: 'advisor_individual_report'
          });
        });
        setRowDataPreviousAdv(listMail);
      }
    }

    if (emailList.id || type == 'Both') {
      //  console.log(
      //   'rowDataPreviousAdv',
      //   rowDataEmail,
      //   listMail,
      //   rowDataPreviousAdv
      // );
      if (JSON.stringify(rowDataEmail) != JSON.stringify(listMail)) {
        noDataChange = 0;
      } else {
        noDataChange = 1;
        setOpenAlert(true);
        setAlertMsg('Please make any change');
        setTimeout(() => {
          setOpenAlert(false);
          setAlertMsg('');
        }, 2000);
      }
    }

    var emailType =
      type == 'store_goal'
        ? emailId
        : type != 'store_goal' && type != 'both'
        ? emailIdAdv
        : '';
    if (
      (emailvalue &&
        emailList.id == undefined &&
        type != 'Both' &&
        emailType &&
        emailType.includes(emailvalue)) ||
      (emailList.id &&
        emailvalue !=
          (rowDataPreviousAdv && rowDataPreviousAdv[0].recipientid) &&
        emailType.includes(emailvalue))
    ) {
      noEmail = 1;
      var errormsg = 'This email address has already been added';
      setError(errormsg);
    } else if (
      type == 'Both' &&
      checked &&
      checkedAdvisor &&
      noDataChange == 0 &&
      emailId &&
      emailId.includes(emailvalue) &&
      emailvalue != (listMail && listMail[0].recipientid)
    ) {
      noEmail = 1;
      var errormsg = 'This email address has already been added';
      setError(errormsg);
    }

    if (noEmail == 0 && noDataChange == 0) {
      getOrSetKPIScorecardEmail('set', JSON.stringify(rowDataEmail), result => {
        for (let i = 0; i < rowDataEmail.length; i++) {
          if (
            rowDataEmail[i].mailfrequency != mailFrequency ||
            rowDataEmail[i].mailstatus != mailStatus ||
            rowDataEmail[i].recipientid != recipientId ||
            rowDataEmail[i].scheduledon != scheduledOn ||
            rowDataEmail[i].serviceadvisor != serviceAdvisor
          ) {
            if (
              result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
                .statelessDbdKpiScorecardKpiScorecardMailJobs
            ) {
              setOpenAlert(true);
              setAlertMsg('KPI Report email recipients saved successfully');
              if (deleteflg) {
                deleteSet.push({
                  recipientid: emailList.recipientId,
                  mailfrequency: emailList.mailFrequency,
                  scheduledon: emailList.scheduledOn,
                  mailstatus: 'delete',
                  id: !checked ? storeId : !checkedAdvisor ? advisorId : '',
                  serviceadvisor:
                    type == 'Both' && !checkedAdvisor
                      ? advisor
                      : type != 'Both' && !checkedAdvisor
                      ? type
                      : 'store_goal',

                  reporttype: 'advisor_individual_report'
                });
                onDeleteEmail(deleteSet);
              }
              setTimeout(() => {
                setOpenAlert(false);
                setOpenDialog(false);
                handlePopupClose();
                resetStates();
              }, 1000);
            }
          }
        }
      });
    }
  };
  const handleSave = () => {
    handleSaveEmail(active, type, emailListDialog);
  };
  const onDeleteEmail = deleteSet => {
    getOrSetKPIScorecardEmail('set', JSON.stringify(deleteSet), result => {
      if (
        result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
          .statelessDbdKpiScorecardKpiScorecardMailJobs
      ) {
        setDeleteflg(false);
      }
    });
  };
  const displayContent = e => {
    var setindexedData;

    setSelectedSchedule(e.target.value);
    if (selectedSchedule) {
      document.getElementById(selectedSchedule).style.display = 'none';
      setRequiredSelectOptions(false);
      if (selectedSchedule == 'weekly') setSelectedDay('');
      if (selectedSchedule == 'monthly') setSelectedDate('');
      if (selectedSchedule == 'biweekly') setSelectedOptions([]);
    }
    document.getElementById(e.target.value).style.display = 'block';
    if (e.target.value) setRequiredSelect('');
  };
  const displayContentAdv = e => {
    setSelectedScheduleAdv(e.target.value);
    if (selectedScheduleAdv) {
      document.getElementById(selectedScheduleAdv + 'adv').style.display =
        'none';
      setRequiredSelectOptionsAdv(false);
      console.log('ccc1', selectedScheduleAdv);
      if (selectedScheduleAdv == 'weekly' + 'adv') setSelectedDayAdv('');
      if (selectedScheduleAdv == 'monthly' + 'adv') setSelectedDateAdv('');
      if (selectedScheduleAdv == 'biweekly' + 'adv') setSelectedOptionsAdv([]);
    }
    document.getElementById(e.target.value + 'adv').style.display = 'block';
    if (e.target.value) setRequiredSelectAdv('');
  };
  const displayWeekly = e => {
    setSelectedDay(e.target.value);
    if (e.target.value) setRequiredSelectOptions(false);
  };
  const displayMonthly = e => {
    setSelectedDate(e.target.value);
    if (e.target.value) setRequiredSelectOptions(false);
  };
  const displayWeeklyAdv = e => {
    setSelectedDayAdv(e.target.value);
    if (e.target.value) setRequiredSelectOptionsAdv(false);
  };
  const displayMonthlyAdv = e => {
    setSelectedDateAdv(e.target.value);
    if (e.target.value) setRequiredSelectOptionsAdv(false);
  };
  const getMonthtext = i => {
    if (i == 1 || i == 21 || i == 31) {
      return 'st of every month';
    }
    if (i == 2 || i == 22) {
      return 'nd of every month';
    }
    if (i == 3 || i == 23) {
      return 'rd of every month';
    } else return 'th of every month';
  };

  const validateEmail = email => {
    let error = null;
    var isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
      emailvalue
    );

    if (!isEmail) {
      if (emailvalue) {
        error = 'Email address is not valid';
      } else {
        setError('');
      }
    }

    // if (type == 'store_goal') {
    //   if (
    //     (emailvalue &&
    //       emailList.id == undefined &&
    //       emailId &&
    //       emailId.includes(emailvalue)) ||
    //     (emailList.id &&
    //       emailvalue != emailList.recipientId &&
    //       emailId.includes(emailvalue))
    //   ) {
    //     error = 'This email address has already been added';
    //   }
    // } else if (type != 'Both') {
    //   if (
    //     (emailvalue &&
    //       emailList.id == undefined &&
    //       emailIdAdv &&
    //       emailIdAdv.includes(emailvalue)) ||
    //     (emailList.id &&
    //       emailvalue != emailList.recipientId &&
    //       emailIdAdv.includes(emailvalue))
    //   ) {
    //     error = 'This email address has already been added';
    //   }
    // }

    if (error) {
      setError(error);
      setEmailvalue('');
      setRequiredText(false);
      return false;
    } else {
      setError('');
    }

    return true;
  };
  const onChangeEmail = e => {
    setEmailvalue(e.target.value);
    if (e.target.value) setRequiredText(false);
    setError('');
  };
  const onChangeAutoComplete = (e, value) => {
    setSelectedOptions(value);
    if (value) setRequiredSelectOptions(false);
  };
  const onChangeAutoCompleteAdv = (e, value) => {
    setSelectedOptionsAdv(value);
    if (value) setRequiredSelectOptionsAdv(false);
  };
  const handleCheckboxChange = event => {
    setChecked(event.target.checked);

    if (!event.target.checked) {
      if (!checkedAdvisor) {
        setfilterDisabled(true);
      }
      if (selectedSchedule) {
        document.getElementById(selectedSchedule).style.display = 'none';
        document.getElementById('daily').style.display = 'block';
        setSelectedSchedule('daily');
      }
      if (storeId) {
        setDeleteflg(true);
      }
    } else {
      setfilterDisabled(false);
      setSelectedSchedule('daily');
      document.getElementById('daily').style.display = 'block';
      setErrorChecked('');
      if (storeId) {
        setDeleteflg(false);
      }
    }
  };
  const handleCheckboxChangeAdvisor = event => {
    setCheckedAdvisor(event.target.checked);

    if (!event.target.checked) {
      if (!checked) {
        setfilterDisabled(true);
      }
      if (selectedScheduleAdv) {
        document.getElementById(selectedScheduleAdv + 'adv').style.display =
          'none';
        setSelectedScheduleAdv('daily');
        document.getElementById('dailyadv').style.display = 'block';
      }
      if (advisorId) {
        setDeleteflg(true);
      }
    } else {
      setfilterDisabled(false);
      setSelectedScheduleAdv('daily');
      setErrorChecked('');
      if (advisorId) {
        setDeleteflg(false);
      }
      if (document.getElementById('dailyadv')) {
        document.getElementById('dailyadv').style.display = 'block';
      }
    }
  };
  const label = { inputProps: { 'aria-label': 'Checkbox demo' } };

  return (
    <Dialog
      transition={Fade}
      classes={{
        paper: classes.paper
      }}
      BackdropProps={{
        classes: {
          root: classes.backDrop
        }
      }}
      //maxWidth="xl"
      // style={{ maxWidth: 900, maxHeight: 700 }}
      open={openDialog}
    >
      <DialogTitle id="confirmation-dialog-title">
        <Typography
          variant="h5"
          color="primary"
          style={{
            textTransform: 'none'
          }}
        >
          Schedule for Mail Trigger
        </Typography>
      </DialogTitle>
      <Collapse in={openAlert}>
        <Alert
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Collapse>
      <DialogContent style={{ overflowX: 'hidden' }}>
        <TableContainer
          component={Paper}
          style={{
            margin: 4,
            padding: 1,
            display: 'block',
            width: '100%'
          }}
        >
          <Table
            className="email-table"
            id="maildetails"
            // style={{ minWidth: 300 }}
            size="small"
            aria-label="a dense table"
          >
            <TableHead
              style={{
                textAlign: 'center',
                backgroundColor: '#003d6b'
              }}
            ></TableHead>
            <TableRow key={'email'}>
              <TableCell
                align="left"
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                Email
              </TableCell>
              <TableCell
                align="left"
                colSpan={2}
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                <div class="parentCell">
                  <div
                    style={{
                      display: 'inline-block',
                      width: statusInsert || advisorSelected ? 268 : 256,
                      marginLeft: 16
                    }}
                  >
                    Access Type
                  </div>
                  <div
                    style={{
                      display: 'inline-block',
                      fontSize: 14,
                      color:
                        checked || checkedAdvisor
                          ? '#003d6b'
                          : 'rgb(170 183 194)'
                    }}
                  >
                    Scheduled
                  </div>
                </div>
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell height={10}>
                <TextField
                  style={{ width: '300px', bottom: statusInsert ? 18 : 20 }}
                  variant="standard"
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  name="email"
                  value={emailvalue}
                  onBlur={validateEmail}
                  onChange={onChangeEmail}
                  helperText={requiredText && 'This is required!'}
                  InputProps={{
                    readOnly: false
                  }}
                />
              </TableCell>
              <TableCell>
                {/* <FormGroup style={{ marginTop: '19px' }}> */}
                <Table>
                  <TableRow>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      <FormControlLabel
                        control={
                          <Checkbox
                            defaultChecked
                            size="small"
                            color="primary"
                            checked={checked}
                            onChange={handleCheckboxChange}
                          />
                        }
                        label={
                          <Typography className={classes.formControlLabel}>
                            Store
                          </Typography>
                        }
                      />
                    </TableCell>

                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      <FormControlLabel
                        control={
                          <div
                            style={{ display: 'block' }}
                            id="store_schedule"
                            className={
                              (checked &&
                                !checkedAdvisor &&
                                type != 'store_goal') ||
                              (checked && !checkedAdvisor && advisorSelected)
                                ? classes.divDisplay
                                : ''
                            }
                          >
                            <FormControl
                              variant="standard"
                              sx={{ m: 1, width: 140 }}
                              className={classes.formControl}
                            >
                              <Select
                                value={selectedSchedule}
                                onChange={displayContent}
                                id="schedule"
                                disabled={!checked ? true : false}
                                style={{ textAlign: 'left' }}
                              >
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'daily'}
                                >
                                  Daily
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'weekly'}
                                >
                                  Weekly
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'biweekly'}
                                >
                                  Biweekly
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'monthly'}
                                >
                                  Monthly
                                </MenuItem>
                              </Select>
                              {requiredSelect && checked ? (
                                <FormHelperText
                                  style={{ color: 'rgb(255,91,71)' }}
                                >
                                  This is required!
                                </FormHelperText>
                              ) : (
                                ''
                              )}
                            </FormControl>
                          </div>
                        }
                      />
                    </TableCell>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      <FormControlLabel
                        control={
                          <div
                            className={
                              (checked &&
                                !checkedAdvisor &&
                                type != 'store_goal') ||
                              (checked && !checkedAdvisor && advisorSelected)
                                ? classes.divDisplay
                                : ''
                            }
                          >
                            <div
                              id={'daily'}
                              style={{ display: 'none', marginRight: 23 }}
                            >
                              <span
                                style={{
                                  fontSize: '12px',
                                  color: !checked ? '#C6C6C6' : '#212121'
                                }}
                              >
                                Daily
                              </span>
                            </div>
                            <div
                              id={'weekly'}
                              style={{
                                display: 'none',
                                marginRight: '23px',
                                marginBottom: '16px'
                              }}
                            >
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 150 }}
                                className={classes.formControl}
                                id={'weekly'}
                              >
                                <InputLabel
                                  style={{ fontSize: '12px' }}
                                  id="demo-simple-select-filled-label"
                                >
                                  Choose day
                                </InputLabel>
                                <Select
                                  id={'selectweekly'}
                                  label="ScheduleOn"
                                  name={'selectweekly'}
                                  style={{ textAlign: 'left' }}
                                  MenuProps={{
                                    anchorOrigin: {
                                      vertical: 'bottom',
                                      horizontal: 'left'
                                    },
                                    transformOrigin: {
                                      vertical: 'top',
                                      horizontal: 'left'
                                    },
                                    getContentAnchorEl: null
                                  }}
                                  value={selectedDay}
                                  onChange={displayWeekly}
                                >
                                  {/* **Weekly**  */}
                                  {days.map((val, index) => (
                                    <MenuItem
                                      style={{ fontSize: '12px' }}
                                      value={val.toLowerCase()}
                                    >
                                      {val}
                                    </MenuItem>
                                  ))}
                                </Select>
                                {requiredSelectOptions && checked ? (
                                  <FormHelperText
                                    style={{ color: 'rgb(255,91,71)' }}
                                  >
                                    Please choose a day!
                                  </FormHelperText>
                                ) : (
                                  ''
                                )}
                              </FormControl>{' '}
                            </div>
                            <div
                              id={'monthly'}
                              style={{
                                display: 'none',
                                marginBottom: 15,
                                marginRight: '23px'
                              }}
                            >
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 150 }}
                                className={classes.formControl}
                                id={'monthly'}
                              >
                                <InputLabel
                                  style={{ fontSize: '12px' }}
                                  id="demo-simple-select-filled-label"
                                >
                                  Choose Date
                                </InputLabel>
                                <Select
                                  label="ScheduleOn"
                                  style={{ textAlign: 'left' }}
                                  MenuProps={{
                                    anchorOrigin: {
                                      vertical: 'bottom',
                                      horizontal: 'left'
                                    },
                                    transformOrigin: {
                                      vertical: 'top',
                                      horizontal: 'left'
                                    },
                                    getContentAnchorEl: null
                                  }}
                                  value={selectedDate}
                                  onChange={displayMonthly}
                                  id="selectmonthly"
                                >
                                  {_.times(30, i => (
                                    <MenuItem
                                      style={{ fontSize: '12px' }}
                                      value={i + 1}
                                    >
                                      {i + 1}
                                      {getMonthtext(i + 1)}
                                    </MenuItem>
                                  ))}
                                </Select>
                                {requiredSelectOptions && checked ? (
                                  <FormHelperText
                                    style={{ color: 'rgb(255,91,71)' }}
                                  >
                                    Please choose a date!
                                  </FormHelperText>
                                ) : (
                                  ''
                                )}
                              </FormControl>
                            </div>
                            <div
                              id={'biweekly'}
                              style={{ display: 'none', marginRight: '23px' }}
                            >
                              <InputLabel
                                style={{ fontSize: '12px' }}
                                id="demo-simple-select-filled-label"
                              >
                                Choose days
                              </InputLabel>
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 170 }}
                                className={classes.formControl1}
                                id={'biweekly'}
                              >
                                <Autocomplete
                                  multiple
                                  id={'selectbiweekly'}
                                  size="small"
                                  style={{
                                    width: '100%',

                                    fontSize: 11
                                  }}
                                  value={selectedOptions}
                                  options={days.map(option => option)}
                                  getOptionDisabled={option =>
                                    selectedOptions.length === 2 ||
                                    selectedOptions.includes(option)
                                      ? true
                                      : false
                                  }
                                  onChange={onChangeAutoComplete}
                                  renderInput={params => (
                                    <TextField
                                      {...params}
                                      variant="standard"
                                      helperText={
                                        requiredSelectOptions && checked
                                          ? 'Please choose days!'
                                          : ''
                                      }
                                    />
                                  )}
                                />{' '}
                              </FormControl>
                            </div>
                          </div>
                        }
                      />
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      <div
                        id="ChkAdvisor"
                        style={{
                          display:
                            type == 'store_goal' && advisorSelected == false
                              ? 'none'
                              : 'block'
                        }}
                      >
                        <FormControlLabel
                          control={
                            <Checkbox
                              size="small"
                              color="primary"
                              checked={checkedAdvisor}
                              onChange={handleCheckboxChangeAdvisor}
                            />
                          }
                          label={
                            <Typography className={classes.formControlLabel}>
                              Advisor
                            </Typography>
                          }
                        />
                      </div>
                    </TableCell>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      <FormControlLabel
                        control={
                          <div
                            style={{
                              display:
                                type == 'store_goal' && !advisorSelected
                                  ? 'none'
                                  : 'block'
                            }}
                            className={
                              !checked && checkedAdvisor
                                ? classes.divDisplayAdv
                                : ''
                            }
                          >
                            <FormControl
                              variant="standard"
                              sx={{ m: 1, width: 170 }}
                              className={classes.formControl}
                            >
                              {/* <InputLabel
                            id="demo-simple-select-filled-label"
                            style={{ fontSize: '12px' }}
                          >
                            Choose Schedule
                          </InputLabel> */}
                              <Select
                                value={selectedScheduleAdv}
                                onChange={displayContentAdv}
                                id="schedule"
                                style={{ textAlign: 'left' }}
                                disabled={!checkedAdvisor ? true : false}
                              >
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'daily'}
                                >
                                  Daily
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'weekly'}
                                >
                                  Weekly
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'biweekly'}
                                >
                                  Biweekly
                                </MenuItem>
                                <MenuItem
                                  style={{ fontSize: '12px' }}
                                  value={'monthly'}
                                >
                                  Monthly
                                </MenuItem>
                              </Select>
                              {requiredSelectAdv && checked ? (
                                <FormHelperText
                                  style={{ color: 'rgb(255,91,71)' }}
                                >
                                  This is required!
                                </FormHelperText>
                              ) : (
                                ''
                              )}
                            </FormControl>
                          </div>
                        }
                      />
                    </TableCell>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      {' '}
                      <FormControlLabel
                        control={
                          <div
                            style={{
                              display:
                                type == 'store_goal' && !advisorSelected
                                  ? 'none'
                                  : 'block'
                            }}
                            className={
                              !checked && checkedAdvisor
                                ? classes.divDisplayAdv
                                : ''
                            }
                          >
                            <div
                              id={'dailyadv'}
                              style={{
                                marginRight: 23
                              }}
                            >
                              <span
                                style={{
                                  fontSize: '12px',
                                  color: !checkedAdvisor ? '#C6C6C6' : '#212121'
                                }}
                              >
                                Daily
                              </span>
                            </div>
                            <div
                              id={'weeklyadv'}
                              style={{
                                display: 'none',
                                marginRight: '23px',
                                marginBottom: 15
                              }}
                            >
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 150 }}
                                className={classes.formControl}
                                id={'weekly'}
                              >
                                <InputLabel
                                  style={{ fontSize: '12px' }}
                                  id="demo-simple-select-filled-label"
                                >
                                  Choose day
                                </InputLabel>
                                <Select
                                  id={'selectweekly'}
                                  label="ScheduleOn"
                                  name={'selectweekly'}
                                  style={{ textAlign: 'left' }}
                                  MenuProps={{
                                    anchorOrigin: {
                                      vertical: 'bottom',
                                      horizontal: 'left'
                                    },
                                    transformOrigin: {
                                      vertical: 'top',
                                      horizontal: 'left'
                                    },
                                    getContentAnchorEl: null
                                  }}
                                  value={selectedDayAdv}
                                  onChange={displayWeeklyAdv}
                                >
                                  {/* **Weekly**  */}
                                  {days.map((val, index) => (
                                    <MenuItem
                                      style={{ fontSize: '12px' }}
                                      value={val.toLowerCase()}
                                    >
                                      {val}
                                    </MenuItem>
                                  ))}
                                </Select>
                                {requiredSelectOptionsAdv && (
                                  <FormHelperText
                                    style={{ color: 'rgb(255,91,71)' }}
                                  >
                                    This is required!
                                  </FormHelperText>
                                )}
                              </FormControl>{' '}
                            </div>
                            <div
                              id={'monthlyadv'}
                              style={{
                                display: 'none',
                                marginBottom: 15,
                                marginRight: '23px'
                              }}
                            >
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 150 }}
                                className={classes.formControl}
                                id={'monthly'}
                              >
                                <InputLabel
                                  style={{ fontSize: '12px' }}
                                  id="demo-simple-select-filled-label"
                                >
                                  Choose Date
                                </InputLabel>
                                <Select
                                  label="ScheduleOn"
                                  style={{ textAlign: 'left' }}
                                  MenuProps={{
                                    anchorOrigin: {
                                      vertical: 'bottom',
                                      horizontal: 'left'
                                    },
                                    transformOrigin: {
                                      vertical: 'top',
                                      horizontal: 'left'
                                    },
                                    getContentAnchorEl: null
                                  }}
                                  value={selectedDateAdv}
                                  onChange={displayMonthlyAdv}
                                  id="selectmonthly"
                                >
                                  {_.times(30, i => (
                                    <MenuItem
                                      style={{ fontSize: '12px' }}
                                      value={i + 1}
                                    >
                                      {i + 1}
                                      {getMonthtext(i + 1)}
                                    </MenuItem>
                                  ))}
                                </Select>
                                {requiredSelectOptionsAdv && (
                                  <FormHelperText
                                    style={{ color: 'rgb(255,91,71)' }}
                                  >
                                    This is required!
                                  </FormHelperText>
                                )}
                              </FormControl>
                            </div>
                            <div
                              id={'biweeklyadv'}
                              style={{ display: 'none', marginRight: '23px' }}
                            >
                              <FormControl
                                variant="standard"
                                sx={{ m: 1, width: 170 }}
                                className={classes.formControl1}
                                id={'biweekly'}
                              >
                                <Autocomplete
                                  multiple
                                  id={'selectbiweekly'}
                                  size="small"
                                  style={{
                                    width: '100%',

                                    fontSize: 12
                                  }}
                                  value={selectedOptionsAdv}
                                  options={days.map(option => option)}
                                  getOptionDisabled={option =>
                                    selectedOptionsAdv.length === 2 ||
                                    selectedOptionsAdv.includes(option)
                                      ? true
                                      : false
                                  }
                                  onChange={onChangeAutoCompleteAdv}
                                  renderInput={params => (
                                    <TextField
                                      {...params}
                                      variant="standard"
                                      helperText={
                                        requiredSelectOptionsAdv &&
                                        'This is required!'
                                      }
                                    />
                                  )}
                                />{' '}
                              </FormControl>
                            </div>
                          </div>
                        }
                      />{' '}
                    </TableCell>
                  </TableRow>
                </Table>
              </TableCell>
            </TableRow>
          </Table>
        </TableContainer>
        {error && <p className="error">{error}</p>}
        {errorChecked && <p className="errorChk">{errorChecked}</p>}
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleOk}
          color="primary"
        >
          Cancel
        </Button>

        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleSave}
          color="primary"
          disabled={filterDisabled}
        >
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function extractValues(mappings) {
  return Object.keys(mappings);
}
const DayTypeMapping = {
  daily: 'Daily',
  weekly: 'Weekly',
  biweekly: 'Biweekly',
  monthly: 'Monthly'
};

const DayType = extractValues(DayTypeMapping);
const lookupValue = (mappings, key) => {
  return mappings[key];
};

export default EmailDialog;
