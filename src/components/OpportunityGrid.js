import {
  Di<PERSON><PERSON>,
  Grid,
  <PERSON>con<PERSON>utton,
  MenuItem,
  Select,
  <PERSON><PERSON>ield,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Box,
  LinearProgress,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Snackbar
} from '@material-ui/core';

import DoneIcon from '@material-ui/icons/Done';
import EditIcon from '@material-ui/icons/Edit';
import FilterListIcon from '@material-ui/icons/FilterList';
import RestoreIcon from '@material-ui/icons/Restore';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import React, { memo, useEffect, useState } from 'react';
import {
  GET_GOAL_SETTINGS_BY_GROUP_NAME,
  GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_LABOR,
  UPDATE_GOAL_SETTINGS_BY_GROUP_NAME,
  UPDATE_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR,
  GET_LABOR_TOTAL_OPPORTUNITY_ADVISOR,
  GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_PARTS,
  GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_ELR
} from 'src/graphql/queries';
import {
  getLaborTotalOpportunity,
  getPartsTotalOpportunity,
  getELRTotalOpportunity,
  getLaborOpportunityBaseline,
  getpartsOpportunityBaseline,
  getElrOpportunityBaseline,
  mwRefreshOpportunity,
  getLaborOpportunityBaselineAdvisor,
  getLaborTotalOpportunityAdvisor,
  getpartsOpportunityBaselineAdvisor,
  getPartsTotalOpportunityAdvisor,
  getElrOpportunityAdvisor,
  getElrOpportunityBaselineAdvisor
} from 'src/utils/hasuraServices';
import makeApolloClient from 'src/utils/apolloRootClientPostgres';
import makeApolloClientWrite from 'src/utils/apolloRootClientPostgresWrite';
import Compare from '@material-ui/icons/Compare';
import { useHistory } from 'react-router';
import SuccessSnackbar from 'src/views/KPIScoreCardGoalSettings/SuccessSnackbar';
import {
  getLatestClosedDate,
  getLatestOpenDate
} from 'src/utils/hasuraServices';
import Checkbox from '@material-ui/core/Checkbox';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { red } from '@material-ui/core/colors';
import { checkClosedDateInCurrentMonth } from 'src/utils/Utils';
import InputBase from '@material-ui/core/InputBase';
import Chip from '@material-ui/core/Chip';
import moment from 'moment';
import { styled } from '@material-ui/core/styles';
import Paper from '@material-ui/core/Paper';
import SaveIcon from '@material-ui/icons/Save';
import DataCard from './DataCard';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import clsx from 'clsx';
import { useDispatch, useSelector } from 'react-redux';
import 'src/styles.css';
import { withKeycloak } from '@react-keycloak/web';
import $ from 'jquery';
import { Alert } from '@material-ui/lab';
import { traceSpan } from 'src/utils/OTTTracing';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;
require('dotenv').config();

const useStyles = makeStyles(theme => ({
  select: {
    '&:before': {
      borderColor: '#FFF'
    },
    '&:after': {
      borderColor: '#FFF'
    }
  },
  icon: {
    fill: '#FFF'
  },
  switch: {
    marginLeft: 0,
    flexDirection: 'row-reverse',
    display: 'inline-flex',
    marginTop: -30,
    marginRight: 176
  },
  volumeOpportunity: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#FFF'
  },
  grossOpportunity: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#FFF',
    textAlign: 'center'
  },
  totalOpportunity: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityCompetitive: {
    fontWeight: 'bold',
    //  fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityMaintenance: {
    fontWeight: 'bold',
    // fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityRepair: {
    fontWeight: 'bold',
    //  fontSize: 14,
    color: '#FFF'
  },
  elrOpportunityTotal: {
    fontWeight: 'bold',
    //fontSize: 14,
    color: '#FFF'
  },
  container: {
    alignItems: 'center',
    margin: '20px 0px'
  },
  root: {
    flexGrow: 1,
    width: '110px'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  input: {
    margin: '0px 5px',
    width: '200px'
  },
  dataContainer: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  textContainer: {
    alignItems: 'start',
    display: 'flex'
  },
  TextField: {
    '& label': {
      color: '#212121 !important'
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#7575753d'
      },
      '&:hover fieldset': {
        borderColor: '#7575753d'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#7575753d'
      }
    }
  },
  flexItem: {
    alignItems: 'center',
    justifyContent: 'space-between'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    align: 'right',
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  sublLabel: {
    display: 'flex'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  dividerRoot: {
    backgroundColor: '#4a4646'
  },
  edit: {
    background: theme.palette.button.primary,
    color: '#fff',
    marginLeft: 10,
    alignSelf: 'self-end'
  },
  alertInfo: {
    fontSize: 16,
    fontWeight: '500',
    color: '#242f48',
    width: '100%',
    marginTop: '2%',
    marginBottom: '5%',
    '@media (max-width: 2560px)': {
      height: '45px !important'
    },
    '@media (max-width: 2304px)': {
      height: '45px !important'
    },
    '@media (max-width: 1920px)': {
      height: '50px !important'
    }
  },
  opportunityTab: {
    marginRight: 0,
    position: 'absolute',
    marginTop: 20,
    '@media (min-width: 2560px)': {
      marginLeft: '78% !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '67% !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '58% !important'
    },
    '@media (max-width: 1680px)': {
      marginLeft: '51% !important'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '42% !important'
    }
  },
  opportunityMsg: {
    display: 'flex',
    '@media (min-width: 2560px)': {
      marginLeft: '-72% !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '-54% !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '-39% !important'
    },
    '@media (max-width: 1680px)': {
      marginLeft: '-28% !important'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '-13% !important',
      width: '97% !important'
    }
  },
  opportunityTabElr: {
    marginRight: 0,
    position: 'absolute',
    marginTop: 20,
    '@media (min-width: 2560px)': {
      marginLeft: '68% !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '54% !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '45% !important'
    },
    '@media (max-width: 1680px)': {
      marginLeft: '43% !important'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '39% !important'
    }
  },
  opportunityMsgElr: {
    display: 'flex',
    '@media (min-width: 2560px)': {
      marginLeft: '-49% !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '-23% !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '-8% !important'
    },
    '@media (max-width: 1680px)': {
      marginLeft: '-8% !important'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '-6% !important',
      width: '89% !important'
    }
  }
}));
const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'center',
  color: '#FFF'
}));
function OpportunityGrid({
  className,
  filterCharts,
  resetDashboard,
  saveClick,
  zoomed,
  setTitle,
  checkSuccess,
  showCurrentMonth,
  keycloak,
  baselineStatusLbr,
  goalSettingsLbr,
  ...rest
}) {
  const classes = useStyles();
  const [value, setValue] = useState();
  const [formState, setFormState] = useState({});
  const [formStateAdvisor, setFormStateAdvisor] = useState({});
  const [chartData, setChartData] = useState([]);
  const [chartDataAdvisor, setChartDataAdvisor] = useState([]);
  const [editForm, setEditForm] = useState(false);
  const [submitForm, setSubmitForm] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const session = useSelector(state => state.session);

  const storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  const history = useHistory();
  const [isLoading, setIsLoading] = useState(false);
  const [updateState, setUpdateState] = useState(0);
  const [closedDate, setClosedDate] = useState('');
  const [checked, setChecked] = useState(false);
  const [openDate, setOpenDate] = useState('');
  const [click, setClick] = useState(0);

  const [error, setError] = useState('');
  const [successMsg, setSuccessMsg] = useState('');
  let [totalOpportunity, setTotalOpportunity] = useState('');
  let [baseline, setBaseline] = useState('');
  const [goalSettingsLabor, setGoalSettingsLabor] = useState(true);
  useEffect(() => {
    if (rest.goalSettings) {
      goalSettingsLbr(true);
      setGoalSettingsLabor(true);
      baselineStatusLbr(true);
      getGoalDetails(rest.chartGroup);
      getTotalOpportunity(rest.chartGroup);
      getOpportunityBaseLine(rest.chartGroup);
      setError('');
      setEditForm(false);
    }
    // if(baseline) {
    //   if(baseline == 'null') {
    //     baselineStatusLbr(false);
    //   }else {
    //     baselineStatusLbr(true);
    //   }
    // }
  }, [rest.goalSettings, session.kpiAdvisor, session.serviceAdvisor]);
  const handleResetLayout = () => {
    resetDashboard(true);
  };
  const handleStoreComapare = () => {
    history.push({
      pathname: '/StoreComparison',
      SelectedLocation: window.location.pathname
    });
  };
  const handleFormEdit = event => {
    if (editForm) {
      if (click == formState) {
        setClick(formState);
        setEditForm(false);
      } else {
        setEditForm(false);

        saveFormData(event);
        setSubmitForm(true);
      }
    } else {
      setClick(formState);
      setEditForm(true);
    }
  };
  const hidesnackbar = () => {
    setopenSnackbar(false);
  };
  // const handleFormEdit = event => {
  //   if (editForm) {
  //     setEditForm(false);
  //     checkSuccess(true);
  //     saveFormData(event);
  //   } else {
  //     setEditForm(true);
  //   }
  // };

  const saveFormData = async event => {
    event.preventDefault();
    let result = [];
    Object.keys(formState).forEach(function(key) {
      if (formState[key] !== '') {
        result.push(1);
      } else {
        result.push(0);
      }
    });
    let nullFlag = result.includes(0);
    if (nullFlag == false) {
      Object.keys(formState).forEach(function(key) {
        console.table('Key : ' + key + ', Value : ' + formState[key]);
        updateGoalSettings(key, formState[key]);
        checkSuccess(true);
        setError('');
        setSuccessMsg('Goals Updated Successfully!');
        setopenSnackbar(true);
      });
    } else {
      setEditForm(true);
      setError('Please enter the goal value');
    }
  };
  async function updateGoalSettings(goal_name, goal_value) {
    setIsLoading(true);
    setUpdateState(1);
    goalSettingsLbr(true);
    setGoalSettingsLabor(true);
    const client = makeApolloClientWrite;
    if (session.kpiAdvisor.includes('All')) {
      await client
        .mutate({
          mutation: UPDATE_GOAL_SETTINGS_BY_GROUP_NAME,
          variables: {
            goal_name: goal_name,
            group_name: rest.chartGroup,
            goal_value: goal_value,
            store_id: storeId
          }
        })
        .then(result => {
          setTimeout(() => {
            setIsLoading(false);
            //  mwRefreshOpportunity(rest.chartGroup);
            refreshMVOpportunity(rest.chartGroup);
          }, 8000);
        });
    } else {
      await client
        .mutate({
          mutation: UPDATE_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR,
          variables: {
            goal_value: goal_value,
            goal_name: goal_name,
            group_name: rest.chartGroup,
            advisor: session.kpiAdvisor[0],
            chartId: rest.chartId,
            store_id: storeId
          }
        })
        .then(result => {
          setTimeout(() => {
            setIsLoading(false);
            //  mwRefreshOpportunity(rest.chartGroup);
            checkSuccess(false);
            setUpdateState(2);
            getTotalOpportunity(rest.chartGroup);
            getGoalDetails(rest.chartGroup);
            //refreshMVOpportunity(rest.chartGroup);
          }, 8000);
        });
    }
  }

  useEffect(() => {
    if (updateState === 1) {
      filterCharts(2);
    } else if (updateState === 2) {
      filterCharts(3);
    }
    if (showCurrentMonth) {
      if (
        checked == true ||
        localStorage.getItem('showCurrentMonth') == 'true'
      ) {
        showCurrentMonth(true);
        setChecked(true);
      } else {
        showCurrentMonth(false);
        setChecked(false);
      }
    }
    console.log('ccccccc===', localStorage.getItem('closedDate'));

    getClosedDate();
  }, [updateState, checked, session.serviceAdvisor]);

  const getClosedDate = () => {
    if (
      localStorage.getItem('closedDate') == null &&
      localStorage.getItem('closedDate') == ''
    ) {
      getLatestClosedDate(result => {
        if (result) {
          var openDate = '';
          var Date1 = result[0].value;

          setClosedDate(Date1);

          localStorage.setItem('closedDate', Date1);
        }
      });
      getLatestOpenDate(result => {
        if (result) {
          var resultDate = '';
          var openDate = '';
          var Date1 = result[0].value;
          localStorage.setItem('openDate', Date1);
          setOpenDate(Date1);
        }
      });
    } else {
      setClosedDate(localStorage.getItem('closedDate'));
      setOpenDate(localStorage.getItem('openDate'));
    }
  };

  const handleFormChange = (event, i) => {
    var decVal = /^(\d*\.{0,1}\d{0,2}$)/;
    const re = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    console.log('hitest', i);
    event.persist();
    const prevVal = event.target.value;
    setFormState(prevFormState => ({
      ...prevFormState,
      [event.target.name]:
        event.target.value === '' ||
        (re.test(event.target.value) && decVal.test(event.target.value))
          ? event.target.value
          : prevFormState[i]
    }));
    setError('');
  };

  const [filterSelected, setFilterSelected] = useState(2);

  const handleclick = val => {
    filterCharts(val);
  };

  const handleChange = evt => {
    var charCode = evt.which ? evt.which : evt.keyCode;
    if (charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57))
      return false;

    return true;
  };
  function getOpportunityBaseLine(groupName) {
    if (session.kpiAdvisor.includes('All')) {
      if (groupName == 'Labor') {
        getLaborOpportunityBaseline(result => {
          if (
            result.data
              .statelessDbdCpOpportunityLaborGetLaborOpportunityBaseline.nodes
              .length > 0
          ) {
            setBaseline(
              result.data
                .statelessDbdCpOpportunityLaborGetLaborOpportunityBaseline
                .nodes[0]
            );
            baselineStatusLbr(true);
          }
        });
      } else if (groupName == 'Parts') {
        getpartsOpportunityBaseline(result => {
          if (
            result.data
              .statelessDbdCpOpportunityPartsGetPartsOpportunityBaseline.nodes
              .length > 0
          ) {
            setBaseline(
              result.data
                .statelessDbdCpOpportunityPartsGetPartsOpportunityBaseline
                .nodes[0]
            );
            baselineStatusLbr(true);
          }
        });
      } else if (groupName == 'ELR') {
        getElrOpportunityBaseline(result => {
          if (
            result.data.statelessDbdCpOpportunityElrGetElrOpportunityBaseline
              .nodes.length > 0
          ) {
            setBaseline(
              result.data.statelessDbdCpOpportunityElrGetElrOpportunityBaseline
                .nodes[0]
            );
            baselineStatusLbr(true);
          }
        });
      }
    } else {
      if (groupName == 'Labor') {
        getLaborOpportunityBaselineAdvisor(session.kpiAdvisor[0], result => {
          if (
            result.data
              .statelessDbdCpOpportunityLaborGetLaborOpportunityBaselineServiceadvisor
              .nodes.length > 0
          ) {
            setBaseline(
              result.data
                .statelessDbdCpOpportunityLaborGetLaborOpportunityBaselineServiceadvisor
                .nodes[0]
            );
            baselineStatusLbr(true);
          } else {
            setBaseline('null');
            baselineStatusLbr(false);
          }
        });
      } else if (groupName == 'Parts') {
        getpartsOpportunityBaselineAdvisor(session.kpiAdvisor[0], result => {
          if (
            result.data
              .statelessDbdCpOpportunityPartsGetPartsOpportunityBaselineServiceadvisor
              .nodes.length > 0
          ) {
            setBaseline(
              result.data
                .statelessDbdCpOpportunityPartsGetPartsOpportunityBaselineServiceadvisor
                .nodes[0]
            );
            baselineStatusLbr(true);
          } else {
            setBaseline('null');
            baselineStatusLbr(false);
          }
        });
      } else if (groupName == 'ELR') {
        getElrOpportunityBaselineAdvisor(session.kpiAdvisor[0], result => {
          if (
            result.data
              .statelessDbdCpOpportunityElrGetElrOpportunityBaselineServiceadvisor
              .nodes.length > 0
          ) {
            setBaseline(
              result.data
                .statelessDbdCpOpportunityElrGetElrOpportunityBaselineServiceadvisor
                .nodes[0]
            );
            baselineStatusLbr(true);
          } else {
            setBaseline('null');
            baselineStatusLbr(false);
          }
        });
      }
    }
  }

  function getTotalOpportunity(groupName) {
    if (session.kpiAdvisor.includes('All')) {
      if (groupName == 'Labor') {
        getLaborTotalOpportunity(result => {
          if (
            result.data
              .statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunity
              .nodes.length > 0
          ) {
            setTotalOpportunity(
              result.data
                .statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunity
                .nodes[0]
            );
          }
        });
      } else if (groupName == 'ELR') {
        getELRTotalOpportunity(result => {
          if (
            result.data
              .statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByCategory
              .nodes.length > 0
          ) {
            setTotalOpportunity(
              result.data
                .statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByCategory
                .nodes[0]
            );
          }
        });
      } else {
        getPartsTotalOpportunity(result => {
          if (
            result.data
              .dbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunity
              .nodes.length > 0
          ) {
            setTotalOpportunity(
              result.data
                .dbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunity
                .nodes[0]
            );
          }
        });
      }
    } else {
      if (groupName == 'Labor') {
        getLaborTotalOpportunityAdvisor(session.kpiAdvisor[0], result => {
          if (
            result.data
              .statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunityServiceadvisor
              .nodes.length > 0
          ) {
            setTotalOpportunity(
              result.data
                .statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunityServiceadvisor
                .nodes[0]
            );
          } else {
            setTotalOpportunity('');
          }
        });
      } else if (groupName == 'Parts') {
        getPartsTotalOpportunityAdvisor(session.kpiAdvisor[0], result => {
          if (
            result.data
              .statelessDbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunityServiceadvisor
              .nodes.length > 0
          ) {
            setTotalOpportunity(
              result.data
                .statelessDbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunityServiceadvisor
                .nodes[0]
            );
          } else {
            setTotalOpportunity('');
          }
        });
      } else if (groupName == 'ELR') {
        getElrOpportunityAdvisor(session.kpiAdvisor[0], result => {
          if (
            result.data
              .statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByServiceadvisor
              .nodes.length > 0
          ) {
            setTotalOpportunity(
              result.data
                .statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByServiceadvisor
                .nodes[0]
            );
          } else {
            setTotalOpportunity('');
          }
        });
      }
    }
  }

  function refreshMVOpportunity(groupName) {
    // mwRefreshOpportunity(groupName, result => {
    //   if (result.data.statelessCcPhysicalRwOpportunityRefreshMatView) {
    checkSuccess(false);
    setUpdateState(2);
    getTotalOpportunity(groupName);
    //   }
    // });
  }

  function getGoalDetails(groupName) {
    const client = makeApolloClient;
    if (session.kpiAdvisor.includes('All')) {
      const start = new Date();
      client
        .query({
          query: GET_GOAL_SETTINGS_BY_GROUP_NAME,
          variables: { group_name: groupName, store_id: storeId },
          fetchPolicy: 'no-cache'
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/LaborGrossAndVolumeOpportunity',
            origin: '',
            event: 'Menu Load',
            is_from: 'GET_GOAL_SETTINGS_BY_GROUP_NAME',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          if (result.data.statefulCcPhysicalRwGoalSettings.nodes.length > 0) {
            setChartData(result.data.statefulCcPhysicalRwGoalSettings.nodes);
            setChartDataAdvisor(
              result.data.statefulCcPhysicalRwGoalSettings.nodes
            );
            var formData = {};
            result.data.statefulCcPhysicalRwGoalSettings.nodes.map(item => {
              formData[item.goalName] = item.goalValue;
            });
            setFormState(formData);
            setFormStateAdvisor(formData);
            goalSettingsLbr(true);
            setGoalSettingsLabor(true);
          }
        });
    } else {
      if (groupName == 'Labor') {
        client
          .query({
            query: GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_LABOR,
            variables: { advisor: session.kpiAdvisor[0] },
            fetchPolicy: 'no-cache'
          })
          .then(result => {
            if (
              result.data.statelessDbdCpOpportunityLaborGetLbrGoalsettingsValue
                .nodes.length > 0
            ) {
              let data =
                result.data
                  .statelessDbdCpOpportunityLaborGetLbrGoalsettingsValue.nodes;
              var sortedObjs = lodash.sortBy(data, 'goalName');
              setChartData(sortedObjs);
              var formData = {};
              sortedObjs.map(item => {
                formData[item.goalName] = item.goalValue;
              });
              setFormState(formData);
              // if(sortedObjs[0].goalValue == "0.00" && sortedObjs[1].goalValue == "0.00") {
              //   goalSettingsLbr(false);
              //   setGoalSettingsLabor(false);
              // } else {
              goalSettingsLbr(true);
              setGoalSettingsLabor(true);
              // }
            }
          });
      } else if (groupName == 'Parts') {
        client
          .query({
            query: GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_PARTS,
            variables: { advisor: session.kpiAdvisor[0] },
            fetchPolicy: 'no-cache'
          })
          .then(result => {
            if (
              result.data.statelessDbdCpOpportunityPartsGetPrtsGoalsettingsValue
                .nodes.length > 0
            ) {
              let data =
                result.data
                  .statelessDbdCpOpportunityPartsGetPrtsGoalsettingsValue.nodes;
              var sortedObjs = lodash.sortBy(data, 'goalName');
              setChartData(sortedObjs);
              var formData = {};
              sortedObjs.map(item => {
                formData[item.goalName] = item.goalValue;
              });
              setFormState(formData);
              // if(sortedObjs[0].goalValue == "0.00" && sortedObjs[1].goalValue == "0.00") {
              //   goalSettingsLbr(false);
              //   setGoalSettingsLabor(false);
              // } else {
              goalSettingsLbr(true);
              setGoalSettingsLabor(true);
              //}
            }
          });
      } else if (groupName == 'ELR') {
        client
          .query({
            query: GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_ELR,
            variables: { advisor: session.kpiAdvisor[0] },
            fetchPolicy: 'no-cache'
          })
          .then(result => {
            if (
              result.data.statelessDbdCpOpportunityElrGetElrGoalsettingsValue
                .nodes.length > 0
            ) {
              let data =
                result.data.statelessDbdCpOpportunityElrGetElrGoalsettingsValue
                  .nodes;
              var sortedObjs = lodash.sortBy(data, 'goalName');
              setChartData(sortedObjs);
              var formData = {};
              sortedObjs.map(item => {
                formData[item.goalName] = item.goalValue;
              });
              setFormState(formData);
              // if(sortedObjs[0].goalValue == "0.00" && sortedObjs[1].goalValue == "0.00" && sortedObjs[2].goalValue == "0.00") {
              //   goalSettingsLbr(false);
              //   setGoalSettingsLabor(false);
              // } else {
              goalSettingsLbr(true);
              setGoalSettingsLabor(true);
              //}
            }
          });
      }
    }
  }

  const handleChangeCurrentMonth = event => {
    setChecked(event.target.checked);
    localStorage.setItem('showCurrentMonth', event.target.checked);
  };
  const HandleOnChange = e => {
    const rx_live = /\D/g;
    // const rx_live=/^[0-9]+([.][0-9]+)?$/;
    if (rx_live.test(e.target.value)) {
      e.target.value = e.target.value.replace(rx_live, '');
    }
  };

  // return baseline == 'null' && session.kpiAdvisor.includes('All') == false ? (
  //   <Alert variant="outlined" severity="error" className={classes.alertInfo}>
  //     <span>
  //       <h4>For this Advisor , there is no Last Qtr data available.</h4>
  //     </span>
  //   </Alert>
  // ) : (
  return (
    <>
      <Grid container className={clsx(classes.container, 'last-qtr')}>
        <Grid item xs={2}>
          <Typography
            variant="h6"
            gutterBottom
            color="primary"
            className={clsx(classes.sublLabel, 'field-label')}
          >
            Last Qtr Baseline:
          </Typography>
        </Grid>
        <Grid item xs={5} className={classes.textContainer}>
          <TextField
            inputProps={{ id: 'Last Qtr - ELR Comp-input' }}
            size="small"
            label={
              rest.chartGroup == 'ELR'
                ? 'Last Qtr - ELR Comp'
                : 'Last Qtr - Hours Per RO'
            }
            key={1}
            classes={{ root: classes.TextField }}
            color="secondary"
            value={
              rest.chartGroup == 'ELR'
                ? typeof baseline.lastQtrCompElr != 'undefined'
                  ? Number(baseline.lastQtrCompElr)
                  : '0'
                : typeof baseline.lastQtrHrsPerRo != 'undefined'
                ? Number(baseline.lastQtrHrsPerRo)
                : '0'
            }
            required
            id="Last Qtr - ELR Comp"
            variant="outlined"
            disabled="false"
            className={classes.input}
          />

          <TextField
            inputProps={{
              id:
                rest.chartGroup == 'ELR'
                  ? 'Last Qtr - ELR Maint-input'
                  : 'Last Qtr - Labor GP%-input'
            }}
            id={
              rest.chartGroup == 'ELR'
                ? 'Last Qtr - ELR Maint'
                : 'Last Qtr - Labor GP%'
            }
            size="small"
            classes={{ root: classes.TextField }}
            label={
              rest.chartGroup == 'Labor'
                ? 'Last Qtr - Labor GP%'
                : rest.chartGroup == 'Parts'
                ? 'Last Qtr - Parts GP%'
                : rest.chartGroup == 'ELR'
                ? 'Last Qtr – ELR Maint'
                : ''
            }
            color="secondary"
            disabled="false"
            value={
              rest.chartGroup == 'ELR'
                ? typeof baseline.lastQtrMaintElr != 'undefined'
                  ? Number(baseline.lastQtrMaintElr)
                  : '0'
                : typeof baseline.lastQtrGpPercentage != 'undefined'
                ? Number(baseline.lastQtrGpPercentage)
                : '0'
            }
            required
            variant="outlined"
            className={classes.input}
          />
          {rest.chartGroup == 'ELR' && (
            <TextField
              inputProps={{ id: 'Last Qtr - ELR Repair-input' }}
              id="Last Qtr - ELR Repair"
              label="Last Qtr - ELR Repair"
              color="secondary"
              classes={{ root: classes.TextField }}
              size="small"
              disabled="false"
              value={
                typeof baseline.lastQtrRepairElr != 'undefined'
                  ? Number(baseline.lastQtrRepairElr)
                  : '0'
              }
              variant="outlined"
              className={classes.input}
            />
          )}
          {/* <Grid item xs={5} className={classes.flexItem1}> */}

          {/* </Grid> */}
        </Grid>
        {baseline == 'null' ||
        (baseline.lastQtrGpPercentage == '0.00' &&
          baseline.lastQtrHrsPerRo == '0.00') ||
        (baseline.lastQtrCompElr == 0 &&
          baseline.lastQtrMaintElr == 0 &&
          baseline.lastQtrRepairElr == 0) ? (
          <Grid item xs={5} className={classes.flexItem}>
            {/* <span style={{display: 'flex', marginRight: '16%'}}> */}
            <span
              className={
                rest.chartGroup == 'ELR'
                  ? classes.opportunityMsgElr
                  : classes.opportunityMsg
              }
            >
              <Grid item xs={1}></Grid>
              <span>
                <Paper
                  square
                  style={{
                    border: '1px solid #084676',
                    borderRadius: '4px',
                    width: '100%',
                    height: 40
                  }}
                >
                  <Alert severity="error" className="opportunityGrid">
                    <span>
                      For this Advisor , there is no Last Qtr data available.
                    </span>
                  </Alert>
                </Paper>
              </span>
              <Grid
                item
                xs={5}
                className={
                  rest.chartGroup == 'ELR'
                    ? classes.opportunityTabElr
                    : classes.opportunityTab
                }
              >
                {closedDate ? (
                  <Typography
                    variant="body1"
                    color="secondary"
                    align="right"
                    className={clsx(classes.dataLabel, 'date-asof')}
                  >
                    {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
                  </Typography>
                ) : (
                  ''
                )}
              </Grid>
              {/* <Tooltip onClick={handleResetLayout} title="Reset Layout">
            <IconButton
              size="small"
              className={('reset-layout-icon', 'date-asof')}
            >
              <RestoreIcon />
              <Typography
                variant="body1"
                color="secondary"
                align="left"
                className={clsx(classes.dataLabel, 'date-asof')}
              >
                Reset
              </Typography>
            </IconButton>
          </Tooltip> */}
            </span>
          </Grid>
        ) : (
          <Grid item xs={2}>
            {closedDate ? (
              <Typography
                variant="body1"
                color="secondary"
                align="right"
                className={clsx(classes.dataLabel, 'date-asof')}
              >
                {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
              </Typography>
            ) : (
              ''
            )}
          </Grid>
        )}
      </Grid>
      <Grid container className={clsx(classes.container, 'set-goal')}>
        <Grid item xs={2}>
          <Typography
            variant="h6"
            gutterBottom
            color="primary"
            className={clsx(classes.sublLabel, 'field-label')}
          >
            "What If" Goal:
          </Typography>
        </Grid>
        <Grid item xs={8} className={classes.textContainer}>
          {lodash.uniqBy(chartData, 'goalName').map((item, index) => {
            const maxLength = item.goalId === 3 || item.goalId === 4 ? 12 : 12;

            return (
              <TextField
                inputProps={{
                  id: editForm
                    ? item.goalName + '-input'
                    : item.goalName + '-input-readonly',
                  maxLength: item.goalId == 3 || item.goalId == 4 ? 12 : 12,
                  pattern:
                    item.goalId == 3 || item.goalId == 4
                      ? '^\\d{0,9}(\\.\\d{0,2})?$'
                      : '^\\d{0,9}(\\.\\d{0,2})?$'
                }}
                onChange={e => {
                  const value = e.target.value;
                  const isValidPattern =
                    item.goalId == 3 || item.goalId == 4
                      ? /^\d{0,9}(\.\d{0,2})?$/.test(value)
                      : /^\d{0,9}(\.\d{0,2})?$/.test(value);

                  if (isValidPattern) {
                    handleFormChange(e, item.goalName);
                  }
                }}
                classes={{ root: classes.TextField }}
                size="small"
                name={item.goalName}
                label={item.goalName}
                key={index}
                value={formState[item.goalName]}
                id={item.goalId}
                variant="outlined"
                className={classes.input}
                InputProps={{
                  readOnly: !editForm,
                  inputProps: {
                    maxLength: item.goalId === 3 || item.goalId === 4 ? 12 : 12
                  }
                }}
                InputLabelProps={{ shrink: true }}
                focus={true}
              />
            );
          })}

          {!editForm && (
            <>
              <Tooltip
                title="Edit Goals"
                disabled={isLoading ? true : false}
                onClick={handleFormEdit}
              >
                <Button
                  style={{ width: '60px', height: '36px' }}
                  variant="contained"
                  id="editGoals"
                  // className={clsx(classes.edit, 'orange-btn')}
                  className={'reset-btn'}
                  onClick={handleFormEdit}
                  // disabled={
                  //   typeof keycloak.realmAccess.roles != 'undefined' &&
                  //   keycloak.realmAccess.roles.length >= 1 &&
                  //   keycloak.realmAccess.roles.includes('client') === true
                  //     ? true
                  //     : false
                  // }
                >
                  Edit
                </Button>
              </Tooltip>
            </>
          )}
          {editForm && (
            <>
              <Tooltip
                title="Save Goals"
                disabled={isLoading ? true : false}
                onClick={handleFormEdit}
              >
                <Button
                  variant="contained"
                  id="saveGoals"
                  className={clsx(classes.edit, 'reset-btn')}
                  onClick={handleFormEdit}
                >
                  Save
                </Button>
              </Tooltip>
            </>
          )}

          {saveFormData && !error && (
            <SuccessSnackbar
              onClose={hidesnackbar}
              open={openSnackbar}
              msg={successMsg}
            />
          )}

          {error && <p className="errorOpp">{error}</p>}
        </Grid>
        <Grid item xs={2} className={classes.flexItem}></Grid>
      </Grid>
      <Divider classes={{ root: classes.dividerRoot }} />
      {goalSettingsLabor == false &&
      session.kpiAdvisor.includes('All') == false ? (
        <Alert variant="outlined" severity="info" className={classes.alertInfo}>
          <span>
            <h4>
              No targets set for the selected advisor. Please set your goals to
              be able to see any possible opportunities.
            </h4>
          </span>
        </Alert>
      ) : isLoading == true ? null : (
        <React.Fragment>
          <Typography
            variant="h5"
            color="primary"
            gutterBottom
            className={clsx(classes.mainLabel, 'main-title')}
          >
            Annual Opportunity
          </Typography>
          <Grid
            container
            className={clsx(
              classes.container,
              classes.dataContainer,
              'annual-opportunity-block'
            )}
          >
            {lodash.isEmpty(totalOpportunity) &&
            session.kpiAdvisor.includes('All') ? (
              <LoaderSkeleton />
            ) : (
              <>
                <DataCard
                  icon={
                    rest.chartGroup == 'ELR'
                      ? 'competitive'
                      : rest.chartGroup == 'Labor'
                      ? 'labor-hours'
                      : 'parts-hours'
                  }
                  title={
                    rest.chartGroup == 'ELR' ? 'Competitive' : 'Hours Per RO '
                  }
                  value={(() => {
                    const value =
                      rest.chartGroup === 'ELR'
                        ? (totalOpportunity.totalCompetitiveOpportunity ===
                            undefined ||
                          totalOpportunity.totalCompetitiveOpportunity >= 0
                            ? '$'
                            : '-$') +
                          (totalOpportunity.totalCompetitiveOpportunity
                            ? Math.abs(
                                Math.round(
                                  totalOpportunity.totalCompetitiveOpportunity
                                )
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : (totalOpportunity.totalLbrvolumeOpportunity ===
                            undefined ||
                          totalOpportunity.totalPrtvolumeOpportunity ===
                            undefined ||
                          totalOpportunity.totalLbrvolumeOpportunity >= 0 ||
                          totalOpportunity.totalPrtvolumeOpportunity >= 0
                            ? '$'
                            : '-$') +
                          (rest.chartGroup === 'Labor'
                            ? totalOpportunity.totalLbrvolumeOpportunity
                              ? Math.abs(
                                  Math.round(
                                    totalOpportunity.totalLbrvolumeOpportunity
                                  )
                                )
                              : 0
                            : totalOpportunity.totalPrtvolumeOpportunity
                            ? Math.abs(
                                Math.round(
                                  totalOpportunity.totalPrtvolumeOpportunity
                                )
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

                    if (value.length > 8) {
                      const truncatedValue =
                        value.length > 9 ? value.slice(0, 9) + '...' : value;

                      return (
                        <Tooltip title={value}>
                          <span
                            style={{
                              whiteSpace: 'nowrap',
                              textOverflow: 'ellipsis',
                              display: 'inline-block',
                              maxWidth: '100px'
                            }}
                          >
                            {truncatedValue}
                          </span>
                        </Tooltip>
                      );
                    }

                    return value;
                  })()}
                />
                <DataCard
                  icon={
                    rest.chartGroup == 'ELR'
                      ? 'maintenance'
                      : rest.chartGroup == 'Labor'
                      ? 'labor-gross-profit'
                      : 'parts-gross-profit'
                  }
                  title={
                    rest.chartGroup == 'ELR' ? 'Maintenance' : 'Gross Profit % '
                  }
                  value={(() => {
                    // Calculate the value based on the chart group
                    const value =
                      rest.chartGroup === 'ELR'
                        ? (totalOpportunity.totalMaintenanceOpportunity ===
                            undefined ||
                          totalOpportunity.totalMaintenanceOpportunity >= 0
                            ? '$'
                            : '-$') +
                          (totalOpportunity.totalMaintenanceOpportunity
                            ? Math.abs(
                                Math.round(
                                  totalOpportunity.totalMaintenanceOpportunity
                                )
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : (totalOpportunity.totalLbrgrossOpportunity ===
                            undefined ||
                          totalOpportunity.totalPrtgrossOpportunity ===
                            undefined ||
                          totalOpportunity.totalLbrgrossOpportunity >= 0 ||
                          totalOpportunity.totalPrtgrossOpportunity >= 0
                            ? '$'
                            : '-$') +
                          (rest.chartGroup === 'Labor'
                            ? totalOpportunity.totalLbrgrossOpportunity
                              ? Math.abs(
                                  Math.round(
                                    totalOpportunity.totalLbrgrossOpportunity
                                  )
                                )
                              : 0
                            : totalOpportunity.totalPrtgrossOpportunity
                            ? Math.abs(
                                Math.round(
                                  totalOpportunity.totalPrtgrossOpportunity
                                )
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

                    if (value.length > 8) {
                      const truncatedValue =
                        value.length > 9 ? value.slice(0, 9) + '...' : value;

                      return (
                        <Tooltip title={value}>
                          <span
                            style={{
                              whiteSpace: 'nowrap',
                              textOverflow: 'ellipsis',
                              display: 'inline-block',
                              maxWidth: '100px'
                            }}
                          >
                            {truncatedValue}
                          </span>
                        </Tooltip>
                      );
                    }
                    return value;
                  })()}
                />
                <DataCard
                  icon={
                    rest.chartGroup == 'ELR'
                      ? 'repair'
                      : rest.chartGroup == 'Labor'
                      ? 'parts-joint'
                      : 'parts-joint'
                  }
                  title={rest.chartGroup == 'ELR' ? 'Repair' : 'Joint Effect'}
                  value={(() => {
                    // Calculate the value based on the chart group
                    const value =
                      rest.chartGroup === 'ELR'
                        ? (totalOpportunity.totalRepairOpportunity ===
                            undefined ||
                          totalOpportunity.totalRepairOpportunity >= 0
                            ? '$'
                            : '-$') +
                          (totalOpportunity.totalRepairOpportunity
                            ? Math.abs(
                                Math.round(
                                  totalOpportunity.totalRepairOpportunity
                                )
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : (totalOpportunity.totalLbrjointOpportunity ===
                            undefined ||
                          totalOpportunity.totalPrtjointOpportunity ===
                            undefined ||
                          totalOpportunity.totalLbrjointOpportunity >= 0 ||
                          totalOpportunity.totalPrtjointOpportunity >= 0
                            ? '$'
                            : '-$') +
                          (rest.chartGroup === 'Labor'
                            ? totalOpportunity.totalLbrjointOpportunity
                              ? Math.abs(
                                  Math.round(
                                    totalOpportunity.totalLbrjointOpportunity
                                  )
                                )
                              : 0
                            : totalOpportunity.totalPrtjointOpportunity
                            ? Math.abs(
                                Math.round(
                                  totalOpportunity.totalPrtjointOpportunity
                                )
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

                    if (value.length > 8) {
                      const truncatedValue =
                        value.length > 9 ? value.slice(0, 9) + '...' : value;

                      return (
                        <Tooltip title={value}>
                          <span
                            style={{
                              whiteSpace: 'nowrap',
                              textOverflow: 'ellipsis',
                              display: 'inline-block',
                              maxWidth: '100px'
                            }}
                          >
                            {truncatedValue}
                          </span>
                        </Tooltip>
                      );
                    }
                    return value;
                  })()}
                />
                <DataCard
                  icon={
                    rest.chartGroup == 'ELR'
                      ? 'total'
                      : rest.chartGroup == 'Labor'
                      ? 'labor-combined'
                      : 'parts-combined'
                  }
                  title={rest.chartGroup == 'ELR' ? 'Total' : 'Combined'}
                  value={(() => {
                    // Calculate the value based on the chart group
                    const value =
                      rest.chartGroup === 'ELR'
                        ? (totalOpportunity.totalOpportunity === undefined ||
                          totalOpportunity.totalOpportunity >= 0
                            ? '$'
                            : '-$') +
                          (totalOpportunity.totalOpportunity
                            ? Math.abs(
                                Math.round(totalOpportunity.totalOpportunity)
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : (totalOpportunity.totalLbropportunity === undefined ||
                          totalOpportunity.totalPrtsopportunity === undefined ||
                          totalOpportunity.totalLbropportunity >= 0 ||
                          totalOpportunity.totalPrtsopportunity >= 0
                            ? '$'
                            : '-$') +
                          (rest.chartGroup === 'Labor'
                            ? totalOpportunity.totalLbropportunity
                              ? Math.abs(
                                  Math.round(
                                    Number(totalOpportunity.totalLbropportunity)
                                  )
                                )
                              : 0
                            : totalOpportunity.totalPrtsopportunity
                            ? Math.abs(
                                Math.round(
                                  totalOpportunity.totalPrtsopportunity
                                )
                              )
                            : 0
                          )
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

                    if (value.length > 8) {
                      const truncatedValue =
                        value.length > 9 ? value.slice(0, 9) + '...' : value;

                      return (
                        <Tooltip title={value}>
                          <span
                            style={{
                              whiteSpace: 'nowrap',
                              textOverflow: 'ellipsis',
                              display: 'inline-block',
                              maxWidth: '100px'
                            }}
                          >
                            {truncatedValue}
                          </span>
                        </Tooltip>
                      );
                    }
                    return value;
                  })()}
                />
              </>
            )}
          </Grid>
        </React.Fragment>
      )}
    </>
  );
}

OpportunityGrid.propTypes = {
  className: PropTypes.string,
  filterCharts: PropTypes.func,
  setTitle: PropTypes.string,
  resetDashboard: PropTypes.func,
  checkSuccess: PropTypes.func,
  showCurrentMonth: PropTypes.func,
  baselineStatusLbr: PropTypes.func
};

export default memo(withKeycloak(OpportunityGrid));
