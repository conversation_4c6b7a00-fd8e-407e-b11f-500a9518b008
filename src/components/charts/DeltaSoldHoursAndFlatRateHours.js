import { QueryRenderer } from '@cubejs-client/react';
import {
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider,
  Typography,
  CardActions
} from '@material-ui/core';
import moment from 'moment';
import React, { useState } from 'react';
import { Line } from 'react-chartjs-2';
import cubejsApi from 'src/utils/cubeUtils';
import MoreActions from '../MoreActions';
// import * as zoom from 'chartjs-plugin-zoom';
import { useHistory } from 'react-router';
import { getChartConfiguration, getDataforLineChart } from '../../utils/Utils';

const dateFormatter = item => moment(item).format('MMM');
const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <CircularProgress />;

const DeltaSoldHoursAndFlatRateHours = () => {
  const history = useHistory();
  const [queryState, queryChangedState] = useState({
    measures: ['DeltaSoldFlatHours.laborsoldhours_flatratehours'],
    timeDimensions: [
      {
        dimension: 'DeltaSoldFlatHours.ro_date',
        dateRange: 'Last 13 months'
      }
    ],
    dimensions: ['DeltaSoldFlatHours.ro_date'],
    filters: [],
    order: {
      'DeltaSoldFlatHours.ro_date': 'asc'
    }
  });
  const COLORS_SERIES = ['#90ee90'];
  const lineRender = ({ resultSet }) => {
    const data = getDataforLineChart(resultSet, 15);
    const options = getChartConfiguration(15);
    const result = (
      <Card bordered={false} style={{ height: '100%' }}>
        <CardHeader
          title="Delta - Sold Hours And Flat Rate Hours"
          action={
            <MoreActions setActions={setactions} chartId={766}></MoreActions>
          }
        ></CardHeader>

        <Divider />
        <CardContent>
          <Line
            data={data}
            options={options}
            getElementAtEvent={dataset => {
              if (dataset.length > 0) {
                var dataIndex = dataset[0]._datasetIndex;
                var valueIndex = dataset[0]._index;
                var xAxis = data.datasets[dataIndex].data[valueIndex];
                var yAxis = data.labels[valueIndex];
                history.push({
                  pathname: '/AnalyzeData',
                  prevPath: window.location.pathname,
                  state: {
                    chartId: 766,
                    x: xAxis,
                    y: yAxis,
                    drillDown: 22,
                    chartName: 'Delta - Sold Hours And Flat Rate Hours',
                    category: 0,
                    title: 'DSFH'
                  }
                });
              }
            }}
          />
        </CardContent>
      </Card>
    );

    return result;
  };

  const setactions = val => {
    if (val === 1) {
      queryChangedState(prevState => ({
        ...prevState,
        timeDimensions: [
          {
            dimension: 'DeltaSoldFlatHours.ro_date',
            dateRange: 'Last 6 months'
          }
        ]
      }));
    }
    if (val === 2) {
      queryChangedState(prevState => ({
        ...prevState,
        timeDimensions: [
          {
            dimension: 'DeltaSoldFlatHours.ro_date',
            dateRange: 'Last 13 months'
          }
        ]
      }));
    }
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=DSFH?chartId=766'
      });
    }
  };

  return (
    <QueryRenderer
      query={queryState}
      cubejsApi={cubejsApi()}
      render={renderChart(lineRender)}
    />
  );
};

export default DeltaSoldHoursAndFlatRateHours;
