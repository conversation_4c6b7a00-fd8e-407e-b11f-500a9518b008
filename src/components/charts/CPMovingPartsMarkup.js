import React, { PureComponent } from 'react';
import { QueryRenderer } from '@cubejs-client/react';
import {
  Card,
  CardContent,
  CardHeader,
  Divider,
  Paper,
  CircularProgress,
  Grid,
  IconButton,
  Typography
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import { Line, Doughnut } from 'react-chartjs-2';
import moment from 'moment';
import {
  getChartConfiguration,
  getDataforLineChart,
  getChartTitle
} from '../../utils/Utils';
import PropTypes from 'prop-types';
import 'chartjs-plugin-annotation';
// import * as zoom from 'chartjs-plugin-zoom';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton.js';
import MoreActions from '../MoreActions';
import clsx from 'clsx';
//import { useHistory } from 'react-router';
import { withKeycloak } from '@react-keycloak/web';
import {
  getStoreIdFilter,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import ChartDialog from 'src/components/Dialog';
import { faCloudShowersHeavy } from '@fortawesome/free-solid-svg-icons';
const dateFormatter = item => moment(item).format('MMM YY');
const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || (
    <Paper square style={{ margin: 8 }}>
      <Grid
        justify="center"
        style={{
          height: 250,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress size={60} />
      </Grid>
    </Paper>
  );
const listItemStyle = {
  color: '#333',
  listStyle: 'none',
  textAlign: 'left',
  display: 'flex',
  flexDirection: 'row',
  margin: '8px'
};
class CPMovingPartsMarkup extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      queryState: {
        measures: [
          'CPMovingPartsMarkup.moving_markup',
          'CPMovingPartsMarkup.moving_markup_after_discount'
        ],
        timeDimensions: [],
        dimensions: [
          'CPMovingPartsMarkup.current_group',
          'CPMovingPartsMarkup.average',
          'CPMovingPartsMarkup.cost',
          'CPMovingPartsMarkup.startopendate',
          'CPMovingPartsMarkup.endopendate',
          'CPMovingPartsMarkup.moonthgroupno',
          'CPMovingPartsMarkup.month_year',
          'CPMovingPartsMarkup.warranty_markup',
          'CPMovingPartsMarkup.markupafterdiscount',
          'CPMovingPartsMarkup.markup'
        ],
        filters: getStoreIdFilter(
          'CPMovingPartsMarkup',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        ),
        order: {
          'CPMovingPartsMarkup.current_group': 'asc'
        }
      },
      queryStateTenant: {
        measures: ['CPMovingPartsMarkup.moving_markup'],
        timeDimensions: [],
        dimensions: [
          'CPMovingPartsMarkup.current_group',
          'CPMovingPartsMarkup.average',
          'CPMovingPartsMarkup.cost',
          'CPMovingPartsMarkup.startopendate',
          'CPMovingPartsMarkup.endopendate',
          'CPMovingPartsMarkup.moonthgroupno',
          'CPMovingPartsMarkup.month_year',
          'CPMovingPartsMarkup.warranty_markup',
          'CPMovingPartsMarkup.markupafterdiscount',
          'CPMovingPartsMarkup.markup'
        ],
        filters: getStoreIdFilter(
          'CPMovingPartsMarkup',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        ),
        order: {
          'CPMovingPartsMarkup.current_group': 'asc'
        }
      },
      yAxisRange: [],
      realm: this.props.keycloak.realm,
      open: false
    };
    localStorage.setItem('realm', this.props.keycloak.realm);
  }
  chartPopup = val => {
    // handleChartPopup(val);
    this.setState({ open: true });
  };
  handleClose = () => {
    this.setState({ open: false });
    this.props.handleClosePopup(1);
  };
  lineRender = ({ resultSet }) => {
    var resultArray = resultSet.loadResponse.data;
    var trancheArray = [];
    var monthYearArray = [];
    for (let value of resultArray) {
      trancheArray.push(value['CPMovingPartsMarkup.moonthgroupno']);
      monthYearArray.push(value['CPMovingPartsMarkup.month_year']);
    }
    //remove repeated values
    var startingTrancheNo = trancheArray.filter(
      (x, i, a) => a.indexOf(x) === i
    );
    var distinctMonths = monthYearArray.filter((x, i, a) => a.indexOf(x) === i);
    var monthYear = this.getMonthyear(distinctMonths);
    var medianValue =
      resultSet.loadResponse.data[0]['CPMovingPartsMarkup.average'];
    var warrantyRate =
      resultSet.loadResponse.data[0]['CPMovingPartsMarkup.warranty_markup'];
    let chartTitle = getChartTitle(1095);
    let getYaxisRanges = this.getMinMaxValues(resultSet);
    let yaxisHighValue = Math.round(getYaxisRanges[0] * 10) / 10;
    let yaxisLowValue = Math.round(getYaxisRanges[1] * 10) / 10;

    const data = {
      labels: resultSet.categories().map(c => c.category),
      datasets: resultSet.series().map((s, index) => ({
        label: s.title,
        data: s.series.map(r => r.value),
        borderColor:
          s.title == '  Markup after Discount' ? '#999190' : '#9999ff',
        backgroundColor:
          s.title == '  Markup after Discount' ? '#999190' : '#9999ff',
        // '#9999ff',
        pointBorderColor: function(context) {
          if (context.dataset.label != '  Markup after Discount') {
            var index = context.dataIndex;
            var str = data.labels[index];
            var str_array = str.split(',');
            var value = context.dataset.data[index];
            if (str_array[2] < 90) {
              return '#e6b800';
            } else if (str_array[2] > 90 && str_array[2] <= 100) {
              return '#005ce6';
            } else if (str_array[2] > 100 && str_array[2] <= 200) {
              return '#9900cc';
            } else if (str_array[2] > 200 && str_array[2] <= 300) {
              return '#cc00cc';
            } else if (str_array[2] > 300) {
              return '#cc0000';
            }
          }
        },
        pointBackgroundColor: function(context) {
          if (context.dataset.label != '  Markup after Discount') {
            var index = context.dataIndex;
            var str = data.labels[index];
            var str_array = str.split(',');
            var value = context.dataset.data[index];
            if (str_array[2] < 90) {
              return '#e6b800';
            } else if (str_array[2] > 90 && str_array[2] <= 100) {
              return '#005ce6';
            } else if (str_array[2] > 100 && str_array[2] <= 200) {
              return '#9900cc';
            } else if (str_array[2] > 200 && str_array[2] <= 300) {
              return '#cc00cc';
            } else if (str_array[2] > 300) {
              return '#cc0000';
            }
          }
        },
        pointHoverBackgroundColor: '#000066',
        pointHoverBorderColor: '#000066',
        pointBorderWidth: 1,
        pointHoverRadius: 1,
        pointHoverBorderWidth: 1,
        pointRadius: 0.75,
        fill: false,
        borderWidth: 1.5
      }))
    };

    const options = {
      maintainAspectRatio:
        localStorage.getItem('popup') != 'true' ? false : true,
      plugins: {
        datalabels: { display: false }
      },
      tooltips: {
        callbacks: {
          title: function(tooltip) {},
          label: (tooltipItem, data) => {
            if (
              this.state.realm == 'lupient' ||
              this.state.realm == 'demo_store' ||
              this.state.realm == 'fisherhonda' ||
              this.state.realm == 'haleyag'
            ) {
              if (tooltipItem.datasetIndex == 0) {
                var labelStringResult =
                  'Markup before Discount:' + tooltipItem.yLabel;
              } else if (tooltipItem.datasetIndex == 1) {
                labelStringResult =
                  'Markup after Discount: ' + tooltipItem.yLabel;
              }
            } else {
              labelStringResult = ' Parts Markup: ' + tooltipItem.yLabel;
            }
            return labelStringResult;
          },
          beforeFooter: (tooltipItem, data) => {
            var labelDiscount;
            var labelString = tooltipItem[0].xLabel;
            var stringArray = labelString.split(',');
            if (
              this.state.realm == 'lupient' ||
              this.state.realm == 'demo_store' ||
              this.state.realm == 'fisherhonda' ||
              this.state.realm == 'haleyag'
            ) {
              if (tooltipItem[0].datasetIndex == 0) {
                labelDiscount = 'Markup after Discount:' + stringArray[8];
              } else if (tooltipItem[0].datasetIndex == 1) {
                labelDiscount = 'Markup before Discount:' + stringArray[9];
              }
            } else labelDiscount = '';

            return [labelDiscount];
          },
          footer: function(tooltipItem, data) {
            var labelString = tooltipItem[0].xLabel;
            var stringArray = labelString.split(',');
            var openDateFrom = stringArray[3].substr(0, 11);
            var openDateTo = stringArray[4].substr(0, 11);

            var labelStringResult = 'Tranche:' + stringArray[0];
            var partsCost = 'Parts Cost/RO:' + '$' + stringArray[2];
            var labelStringResult2 = 'Start Open date:' + openDateFrom;
            var endDate = 'End Open date:' + openDateTo;

            return [labelStringResult, partsCost, labelStringResult2, endDate];
          }
        }
      },
      legend: {
        position: 'bottom',
        display: false
      },
      pan: {
        enabled: false,
        mode: 'x',
        speed: 20,
        threshold: 10,
        onPan: function() {}
      },
      zoom: {
        enabled: false,
        drag: false,
        mode: 'xy',
        limits: {
          max: 10,
          min: 0.5
        }
      },
      scales: {
        yAxes: [
          {
            ticks: {
              padding: 20,

              min:
                this.props.keycloak.realm == 'ferrarioat_store' &&
                JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
                  '256778041'
                  ? 1.35
                  : this.props.keycloak.realm == 'ferrarioat_store'
                  ? 1.4
                  : this.props.keycloak.realm == 'fisherhonda'
                  ? 1.55
                  : 1.1,
              // this.props.keycloak.realm == 'greatlakesan' ||
              // this.props.keycloak.realm == 'keatingag'

              max:
                this.props.keycloak.realm == 'lupient' ||
                this.props.keycloak.realm == 'demo_store'
                  ? 2.3
                  : this.props.keycloak.realm == 'ferrarioat_store' &&
                    JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
                      '236951998'
                  ? 1.75
                  : this.props.keycloak.realm == 'ferrarioat_store'
                  ? 1.7
                  : this.props.keycloak.realm == 'fisherhonda'
                  ? 1.8
                  : 2.0,
              callback: function(value, index, values) {
                return value.toFixed(2);
              },
              stepSize: this.props.keycloak.realm == 'fisherhonda' ? 0.01 : ''
            },
            gridLines: {
              drawTicks: false
            }
          }
        ],
        xAxes: [
          {
            autoSkip: false,
            gridLines: {
              display: false,
              zeroLineColor: 'transparent'
            },
            ticks: {
              callback: function(value, index) {
                var nameArr = value.split(',');
                let filtered = [];
                var filteredNumber = parseInt(nameArr[0]);
                console.log('filter', localStorage.getItem('realm'));
                if (localStorage.getItem('realm') == 'ferrarioat_store') {
                  return (
                    Math.round(filteredNumber / 10) * 10
                  ).toLocaleString();
                } else {
                  return (
                    Math.round(filteredNumber / 100) * 100
                  ).toLocaleString();
                }
              },

              maxTicksLimit:
                this.props.keycloak.realm == 'ferrarioat_store' &&
                JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
                  '232297966'
                  ? 4
                  : 8
            }
          }
        ]
      },
      legendCallback: function(chart) {
        var text = [];
        text.push('<ul>');
        for (var i = 0; i < chart.data.datasets.length; i++) {
          text.push('<li>');
          text.push(
            '<span style="background-color:' +
              chart.data.datasets[i].borderColor +
              '">' +
              chart.data.datasets[i].label +
              '</span>'
          );
          text.push('</li>');
        }
        text.push('</ul>');
        return text.join('');
      },
      annotation: {
        drawTime: 'afterDatasetsDraw', // (default)
        events: ['click'],

        dblClickSpeed: 350, // ms (default)

        annotations: [
          {
            drawTime: 'afterDraw', // overrides annotation.drawTime if set
            id: 'a-line-1', // optional
            type: 'line',
            mode: 'horizontal',
            scaleID: 'y-axis-0',
            value: medianValue,
            borderColor: '#1340c9',
            borderWidth: 1,
            label: {
              //  backgroundColor: '',
              content: 'Median:' + medianValue,
              enabled: true,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1,
              yPadding: 3,
              xAdjust:
                this.props.keycloak.realm == 'fisherhonda' ||
                this.props.keycloak.realm == 'ferrarioat_store'
                  ? -155
                  : '',
              yAdjust:
                this.props.keycloak.realm == 'fisherhonda' ||
                this.props.keycloak.realm == 'ferrarioat_store'
                  ? -5
                  : ''
            }
          },
          {
            id: 'hlineh3',
            type: 'line',
            mode: 'horizontal',
            scaleID: 'y-axis-0',
            value: warrantyRate,
            borderColor: '#00cc44',
            borderWidth: 1,
            label: {
              content: 'Current Warranty Parts Markup:' + warrantyRate,
              enabled: true,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1,
              yPadding: 3,
              yAdjust:
                this.props.keycloak.realm == 'fisherhonda' ||
                this.props.keycloak.realm == 'ferrarioat_store'
                  ? 2
                  : ''
            }
          },
          {
            id: 'hline0',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[0],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[0],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              xAdjust: -15,
              cornerRadius: 1
            }
          },
          {
            id: 'hline1',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[1],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[1],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline2',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[2],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[2],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline3',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[3],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[3],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline4',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[4],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[4],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline5',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[5],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[5],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          }
        ]
      }
      // }
    };
    const { classes } = this.props;
    this.result = (
      <Card bordered={false} className={classes.cardContainer}>
        <CardHeader
          //title="CP Moving Parts Markup"
          title={chartTitle}
          action={
            <MoreActions
              setActions={this.setactions}
              type={this.props.type}
              chartPopup={this.chartPopup}
              handleClose={this.handleClose}
              chartId={1095}
              favoritesDisabled={true}
            ></MoreActions>
          }
          subheader={getSubHeader(1095)}
          className={classes.cardHeader}
        ></CardHeader>
        <Divider />
        <CardContent>
          <div className={classes.chartContainter}>
            <Line
              id="canvas"
              data={data}
              options={options}
              getElementAtEvent={dataset => {
                this.props.history.push({
                  pathname: '/AnalyzeData',
                  prevPath:
                    window.location.pathname == '/PartsTranches'
                      ? window.location.pathname
                      : window.location.pathname + '?chartId=' + 1095,
                  state: {
                    chartId: 1095,
                    drillDown: 15,
                    chartName: chartTitle,
                    category: 0
                  }
                });
              }}
            />
          </div>
          <Grid
            container
            xs={12}
            className={clsx(classes.container, 'chart-footer')}
          >
            {this.state.realm == 'lupient' ||
            this.state.realm == 'demo_store' ||
            this.state.realm == 'fisherhonda' ||
            this.state.realm == 'haleyag' ? (
              <Grid item xs={4} className={classes.flexItem}>
                <Grid item className={classes.flexItemCenter}>
                  <span className={classes.markupBefore}></span>
                  <Typography variant="subtitle2" className={classes.itemLabel}>
                    Markup before Discount
                  </Typography>
                </Grid>
                <Grid item className={classes.flexItemCenter}>
                  <span className={classes.markupAfter}></span>
                  <Typography variant="subtitle2" className={classes.itemLabel}>
                    Markup after Discount
                  </Typography>
                </Grid>
              </Grid>
            ) : (
              <Grid item xs={4}>
                <span className={classes.markupBefore}></span>
                <Typography variant="subtitle2" className={classes.itemLabel}>
                  Parts Markup
                </Typography>
              </Grid>
            )}
            <Grid item xs={6} className={classes.flexItemCenter}>
              <Typography variant="subtitle2" className={classes.itemLabel}>
                Parts Cost/RO:
              </Typography>

              <span className={classes.less90}></span>
              <Typography variant="subtitle2">{'<$90'}</Typography>

              <span className={classes.less90to100}></span>
              <Typography
                variant="subtitle2"
                // style={{
                //   fontSize: '12px',
                //   position: 'absolute',
                //   right: '60px',
                //   marginTop: '-5px',
                //   fontWeight: 'bold'
                // }}
              >
                {'$90-$100'}
              </Typography>

              <span className={classes.less100to200}></span>
              <Typography
                variant="subtitle2"
                // style={{
                //   fontSize: '12px',
                //   position: 'absolute',
                //   right: '60px',
                //   marginTop: '-5px',
                //   fontWeight: 'bold'
                // }}
              >
                {'$100-$200'}
              </Typography>

              <span className={classes.less200to300}></span>
              <Typography
                variant="subtitle2"
                // style={{
                //   fontSize: '12px',
                //   position: 'absolute',
                //   right: '60px',
                //   marginTop: '-5px',
                //   fontWeight: 'bold'
                // }}
              >
                {' $200-$300'}
              </Typography>

              <span className={classes.above300}></span>
              <Typography
                variant="subtitle2"
                // style={{
                //   fontSize: '12px',
                //   position: 'absolute',
                //   right: '60px',
                //   marginTop: '-5px',
                //   fontWeight: 'bold'
                // }}
              >
                {'>$300'}
              </Typography>
            </Grid>
            <Grid item xs={2}>
              <Typography variant="subtitle1" className={classes.itemLabel}>
                Tranche Size: 100 ROs
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    );

    return this.result;
  };
  getMinMaxValues = resultSet => {
    let arrayResult = resultSet.loadResponse.data;
    let arraydata = [];
    let resultArray = [];
    for (let value of arrayResult) {
      arraydata.push(value['CPMovingPartsMarkup.moving_markup']);
    }
    var maxValue = Math.round(Math.max.apply(null, arraydata));
    var increasedmaxValue = (maxValue * 10) / 100;
    var updatedMaxValue = (increasedmaxValue + maxValue).toFixed(2);
    var minValue = Math.round(Math.min.apply(null, arraydata));
    var decreasedminValue = (minValue * 10) / 100;
    var updatedMinValue = (minValue - decreasedminValue).toFixed(2);
    resultArray.push(updatedMaxValue, updatedMinValue);
    return resultArray;
  };
  getMonthyear = distinctMonths => {
    var monthValues = distinctMonths;
    var yearMonth = [];
    var months = [
      'none',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    for (let index = 0; index < monthValues.length; index++) {
      var mon = monthValues[index];
      var res = mon.split('-');
      var month1 = res[1];
      month1 = month1.replace(/^0+/, '');
      yearMonth.push(months[month1] + "'" + res[0].slice(2));
    }
    return yearMonth;
  };
  setactions = val => {
    if (val === 4) {
      this.props.history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=1095',
        SelectedLocation: window.location.pathname
      });
    }
  };

  render() {
    const { classes } = this.props;
    return (
      <>
        <QueryRenderer
          query={
            this.props.keycloak.realm == 'lupient' ||
            this.props.keycloak.realm == 'demo_store' ||
            this.props.keycloak.realm == 'fisherhonda' ||
            this.props.keycloak.realm == 'haleyag'
              ? this.state.queryState
              : this.state.queryStateTenant
          }
          cubejsApi={cubejsApi()}
          render={renderChart(this.lineRender)}
        />
        <ChartDialog
          open={this.state.open}
          chartId={1095}
          chartType="partsMarkup"
          realm={this.props.keycloak.realm}
          handlePopupClose={this.handleClose}
        />
      </>
    );
  }
}
const styles = theme => ({
  cardContainer: {
    height: '100%',
    borderRadius: 0,
    border: '1px solid #003d6b',
    paddingBottom: 20
  },
  cardHeader: {
    borderBottom: '1px solid #003d6b'
  },
  chartContainter: {
    backgroundColor: '#FFF',
    position: 'relative',
    cursor: 'pointer',
    margin: 'auto',
    height:
      window.location.pathname == '/GraphDetailsView'
        ? 380
        : window.innerHeight - 280 + 'px',
    width: 'auto'
  },
  flexItem: {
    display: 'flex',
    justifyContent: 'end'
  },
  flexItemCenter: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  itemLabel: {
    fontWeight: 'bold'
  },
  markupBefore: {
    marginRight: '5px',
    float: 'left',
    width: '15px',
    height: '4px',
    margin: '5px',
    backgroundColor: '#9999ff'
  },
  markupAfter: {
    marginRight: '5px',
    float: 'left',
    width: '15px',
    height: '4px',
    margin: '5px',
    backgroundColor: '#999190'
  },
  less90: {
    marginRight: '5px',
    float: 'left',
    width: '12px',
    height: '12px',
    margin: '2px',
    backgroundColor: '#e6b800'
  },
  less90to100: {
    marginRight: '5px',
    float: 'left',
    width: '12px',
    height: '12px',
    margin: '2px',
    backgroundColor: '#005ce6'
  },
  less100to200: {
    marginRight: '5px',
    float: 'left',
    width: '12px',
    height: '12px',
    margin: '2px',
    backgroundColor: '#9900cc'
  },
  less200to300: {
    marginRight: '5px',
    float: 'left',
    width: '12px',
    height: '12px',
    margin: '2px',
    backgroundColor: '#cc00cc'
  },
  above300: {
    marginRight: '5px',
    float: 'left',
    width: '12px',
    height: '12px',
    margin: '2px',
    backgroundColor: '#cc0000'
  }
});
export default withStyles(styles)(withKeycloak(CPMovingPartsMarkup));
