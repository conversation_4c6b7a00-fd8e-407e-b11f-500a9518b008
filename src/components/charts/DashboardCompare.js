import React, { useState, useEffect } from 'react';
import { useCubeQuery } from '@cubejs-client/react';
import {getCompareStoreQuery} from 'src/components/CompareStoreQuery';
import RenderComparisonCharts from './RenderComparisonCharts';
import PropTypes from 'prop-types';
var lodash = require('lodash');


const DashboardCompare = ({ chartId, chartName}) => {
  const [data, setData] = useState([]);
  const [queryState, queryChangedState] = useState(
    getCompareStoreQuery(chartId)
  );
  const { resultSet, isLoading, error, progress  } = useCubeQuery(queryState)
 
  useEffect(() => {
    if (resultSet) {
      let dimension = resultSet.loadResponse.query.dimensions[0];
      let measure = resultSet.loadResponse.query.measures[0];
      let dimension1 = resultSet.loadResponse.query.timeDimensions[0].dimension;
      let storeNames = localStorage.getItem('storeNames').split(",");
      let data = createJSON()

      function createJSON() {
        let jsonObj = [];
        storeNames.forEach((items,i) => {
          var name = items.trim()+' ';
          var resultendData = lodash
          .chain(resultSet.tablePivot())
          .groupBy(dimension)
          .map(value => {
            return value.map(data => data[measure]);
          })
          .value()[i];

          var resultendDimensions = lodash
          .chain(resultSet.tablePivot())
          .groupBy(dimension)
          .map(value => {
            return value.map(data => data[dimension1+'.month']);
          })
          .value()[i];
  
          let item = {}

          item["name"] = name;
          item ["data"] = resultendData;
          item ["dimension"] = resultendDimensions;

          jsonObj.push(item);
        });
        return jsonObj;
      }
      setData(data);

    }
  }, [resultSet]);
  return <RenderComparisonCharts resultSet={data} chartName={chartName} chartId={chartId}/>;
};
DashboardCompare.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
 
};
export default DashboardCompare;