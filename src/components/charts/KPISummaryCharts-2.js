import React, { useEffect, useState } from 'react';
import { Bar, Doughnut, Pie, HorizontalBar } from 'react-chartjs-2';
import {
  getDataforKPIs,
  getChartConfigurationKPIs,
  getOptionsForPie1,
  getOptionsForPie2,
  getKpiChartTitle,
  getOptionsForHigh<PERSON>hart,
  getOptionsForHighchartsPie,
  getOptionsForColumnHighcharts,
  getOptionsForHighchartsLinearGauge,
  getOptionsForPie1346
} from '../../utils/Utils';
import Paper from '@material-ui/core/Paper';
import clsx from 'clsx';
import {
  Card,
  Grid,
  CardHeader,
  CardContent,
  Divider,
  Tooltip,
  Typography,
  IconButton
} from '@material-ui/core';
import { useHistory } from 'react-router';
import 'chartjs-plugin-doughnutlabel';
import 'chartjs-plugin-datalabels';
import { useSelector, useDispatch } from 'react-redux';
import { makeStyles } from '@material-ui/styles';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import funnel from 'highcharts/modules/funnel.js';
import Gauge from 'react-svg-gauge';
import GaugeChart from 'react-gauge-chart';
import bullet from 'highcharts/modules/bullet.js';
import DataCard from 'src/components/DataCardKpi';
import ReactSpeedometer from 'react-d3-speedometer';
import ArrowBackOutlinedIcon from '@material-ui/icons/ArrowBackOutlined';
import AssignmentReturnOutlinedIcon from '@material-ui/icons/AssignmentReturnOutlined';

import $ from 'jquery';

funnel(Highcharts);
bullet(Highcharts);

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  cardContentAll: {
    cursor: 'pointer',
    paddingBottom: 6
  },
  pieContainerRo1: {
    float: 'none',
    marginRight: 0
  },
  pieContainerRo2: {
    float: 'right',
    marginRight: 97
  },
  pieContainerRo3: {
    float: 'left',
    marginLeft: 75,
    position: 'fixed',
    marginTop: -5,
    width: '88%'
  },
  pieContainerRo4: {
    display: 'none'
  },
  pieContainerLine1: {
    float: 'right',
    marginRight: 83,
    width: '100%'
  },
  pieContainerLine2: {
    float: 'left',
    marginLeft: 66,
    position: 'fixed',
    marginTop: 12,
    width: '88%'
  },
  pie13363: {
    display: 'none'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  chartRoot: {
    maxWidth: '30%',
    marginRight: 20
  },
  chartRoot1336: {
    maxWidth: '40%'
  },

  chartRoot1340: {
    maxWidth: '26%'
  },
  chartRoot1344: {
    maxWidth: '80%'
  },
  chartRoot1341: {
    maxWidth: '49%'
  },
  chartRoot1343: {
    maxWidth: '40%'
  },
  chartRoot1339: {
    maxWidth: '30%'
  },
  lineROChart: {
    display: 'flex',
    justifyContent: 'center'
  },
  roShareChart: {
    display: 'flex',
    justifyContent: 'center'
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 600,
    fontFamily: 'Roboto',
    marginBottom: 25,
    textAlign: 'left',
    marginTop: 15,
    marginLeft: 13,
    color: '#003d6b'
  },
  mileageChart: {
    display: 'flex',
    justifyContent: 'center'
    // marginTop: 40
  },
  mileageChart1343: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 8
  },
  PaperLaborSale1341: {
    height: '22%',
    width: 296,
    display: 'flex',
    justifyContent: 'center',
    marginLeft: 88
  },
  PaperLaborSale13411: {
    height: '22%',
    width: 296,
    display: 'flex',
    justifyContent: 'center'
    //marginLeft: 88
  },
  HeaderComponent1341: {
    paddingTop: 2,
    marginBottom: 5,
    paddingBottom: 9
  },
  imageComponent1341: {
    display: 'flex',
    height: 70,
    width: 70,
    margin: 'auto',
    marginTop: 20
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '100%'
  },
  PaperLaborSale1343: {
    display: 'none'
  },
  highChartContainer1340: {
    marginTop: -17,
    '@media (max-width: 1920px)': {
      width: '300px !important'
      //marginLeft: -13,
    },
    '@media (max-width: 1280px)': {
      width: '300px !important'
      //marginLeft: -37,
    },
    '@media (min-width: 2304px)': {
      width: '500px !important'
      //marginLeft: 20,
    }
  },
  visualization: {
    display: 'block'
  },
  chartHeader: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  PaperSale1343: {
    // width: '47%',
    width: '56%',
    marginLeft: 4,
    border: '1px solid #75757575'
  },
  PaperSale1341: {
    width: '38%',
    marginLeft: 4,
    border: '1px solid #75757575'
  },
  PaperGpp1343: {
    dispaly: 'none',
    width: 0
  },
  PaperGpp1341: {
    width: '20%',
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#109618',
    border: '1px solid #75757575',
    '@media (max-width: 1920px)': {
      marginTop: '11% !important'
    },
    '@media screen and (min-width: 1900px)': {
      marginTop: '10% !important'
    },
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '10% !important',
    // },
    height: '20%'
  },
  PaperGpp1342: {
    width: '20%',
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#109618',
    border: '1px solid #75757575',
    '@media (max-width: 1920px)': {
      marginTop: '11% !important'
    },
    '@media screen and (min-width: 1900px)': {
      marginTop: '10% !important'
    },
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '10% !important',
    // },
    height: '20%'
  },
  PaperGp1343: {
    width: '47%',
    border: '1px solid #75757575',
    display: 'none'
  },
  PaperGp1341: {
    width: '37%',
    marginRight: 4,
    border: '1px solid #75757575'
  },
  centerText1341: {
    fontWeight: 600,
    fontSize: 17,
    marginTop: 10,
    marginBottom: 10,
    color: '#FFF',
    textAlign: 'center'
    //marginLeft: 39
  },
  centerValue1341: {
    fontWeight: 600,
    fontSize: 30,
    color: '#FFF',
    textAlign: 'center'
    //marginLeft: 22
  },
  container1340_1: {
    textAlign: 'center',
    '@media (max-width: 1920px)': {
      // marginTop: '-19px !important'
      marginTop: '8px !important'
    },
    '@media screen and (min-width: 1900px)': {
      // marginTop: '10% !important',
      marginTop: '13% !important'
    }
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '10% !important',
    // }
  },

  PaperGpp1351: {
    display: 'none'
  },
  PaperGpp1337: {
    width: 130,
    height: '20%',
    marginTop: '8%',
    marginLeft: -65,
    marginRight: 43,
    paddingBottom: 10,
    backgroundColor: '#FF9900'
    //e1ad01
  },
  centerText1337: {
    color: '#FFF',
    marginTop: 10,
    textAlign: 'center',
    fontWeight: 600,
    marginBottom: 10,
    '@media (max-width: 1920px)': {
      fontSize: '14px !important'
    },
    '@media screen and (min-width: 1900px)': {
      fontSize: '16px !important'
    }
    // '@media screen and (min-width: 1400px)': {
    //   fontSize: '16px !important',
    // },
  },
  chartContainer1339: {
    '@media (max-width: 1920px)': {
      marginTop: '15px !important'
    }
  },
  chartContainer1335: {
    '@media (max-width: 1920px)': {
      marginTop: '25px !important'
    },
    '@media screen and (min-width: 1900px)': {
      marginTop: '1px !important'
    }
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '1px !important',
    // }
  }
}));
const KPISummaryCharts = props => {
  const history = useHistory();
  const session = useSelector(state => state.session);
  const classes = useStyles();
  let partsMatrixTypes = JSON.parse(localStorage.getItem('partsMatrixTypes'));
  var gridTitle = 'CP';
  var gridTitleParts = 'CP';
  if (
    props.internalMisses == true &&
    localStorage.getItem('realm') == 'billknightag'
  ) {
    gridTitle = 'Int';
  }
  if (
    props.internalMisses == true &&
    partsMatrixTypes.length > 1 &&
    localStorage.getItem('realm') == 'billknightag'
  ) {
    gridTitleParts = 'Int';
  }
  let data =
    props.chartId == 1336
      ? getDataforKPIs(props.ROShareData, props.chartId)
      : props.chartId == 1337
      ? getDataforKPIs(props.LineROLtSixtyK, props.chartId)
      : props.chartId == 1338
      ? getDataforKPIs(props.LineROGtSixtyK, props.chartId)
      : props.chartId == 1339
      ? getDataforKPIs(props.FlatRateHrs, props.chartId)
      : props.chartId == 1340
      ? getDataforKPIs(props.AvgAgeMiles, props.chartId)
      : props.chartId == 1341
      ? getDataforKPIs(props.LaborGpRo, props.chartId)
      : props.chartId == 1342
      ? getDataforKPIs(props.PartsGpRo, props.chartId)
      : props.chartId == 1343
      ? getDataforKPIs(props.TotalGpRo, props.chartId)
      : props.chartId == 1344
      ? getDataforKPIs(props.WorkMix, props.chartId)
      : props.chartId == 1351
      ? getDataforKPIs(props.LineRO, props.chartId)
      : props.chartId == 1346
      ? getDataforKPIs(props.LaborGrid, props.chartId)
      : props.chartId == 1353
      ? getDataforKPIs(props.PartsGrid, props.chartId)
      : getDataforKPIs(props.allCWITData, props.chartId);

  let data1 = {};
  let data2 = {};
  let data3 = {};
  let data4 = {};
  if (
    props.chartId == 1336 &&
    !lodash.isEmpty(data) &&
    data.labels.length > 0
  ) {
    data1 = {
      labels:
        session.serviceAdvisor == 'All'
          ? [data.labels[0]]
          : [data.labels[4], data.labels[0]],
      datasets: [
        {
          label: 'My First Dataset',
          data:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].data[0]]
              : [data.datasets[0].data[0], data.datasets[0].data[4]],
          backgroundColor:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].backgroundColor[0]]
              : [
                  data.datasets[0].backgroundColor[0],
                  data.datasets[0].backgroundColor[2]
                ],
          hoverOffset: 4
        }
      ]
    };
    data2 = {
      labels:
        session.serviceAdvisor == 'All'
          ? [data.labels[1]]
          : [data.labels[5], data.labels[1]],
      datasets: [
        {
          label: 'My First Dataset',
          data:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].data[1]]
              : [data.datasets[0].data[1], data.datasets[0].data[5]],
          backgroundColor:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].backgroundColor[1]]
              : [
                  data.datasets[0].backgroundColor[1],
                  data.datasets[0].backgroundColor[3]
                ],
          hoverOffset: 4
        }
      ]
    };
  } else if (
    (props.chartId == 1337 || props.chartId == 1338 || props.chartId == 1351) &&
    !lodash.isEmpty(data) &&
    data.labels.length > 0
  ) {
    if (props.chartId == 1337) {
    }
    data1 = {
      labels: [data.labels[1], data.labels[2]],
      datasets: [
        {
          label: 'My First Dataset',
          data: [data.datasets[0].data[2], data.datasets[0].data[1]],
          // data: [ data.datasets[0].data[1],data.datasets[0].data[2]],
          backgroundColor: [
            data.datasets[0].backgroundColor[5],
            data.datasets[0].backgroundColor[4]
          ],
          hoverOffset: 4
        }
      ]
    };
    data2 = {
      labels: [data.labels[3], data.labels[4]],
      datasets: [
        {
          label: 'My First Dataset',
          // data: [data.datasets[0].data[3], data.datasets[0].data[4]],
          data: [data.datasets[0].data[4], data.datasets[0].data[3]],
          backgroundColor: [
            data.datasets[0].backgroundColor[5],
            data.datasets[0].backgroundColor[4]
          ],
          hoverOffset: 4
        }
      ]
    };
    data3 = {
      labels: ['Labor Sale', 'Parts Sale', 'Total Sale'],
      datasets: [
        {
          label: ['1 Line RO'],
          data: [
            data.datasets[0].data[5],
            data.datasets[0].data[6],
            data.datasets[0].data[7]
          ],
          backgroundColor:
            props.chartId == 1337
              ? [
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4]
                ]
              : props.chartId == 1351
              ? [
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4]
                ]
              : [
                  data.datasets[0].backgroundColor[6],
                  data.datasets[0].backgroundColor[6],
                  data.datasets[0].backgroundColor[6]
                ],
          hoverOffset: 4
        },
        {
          label: ['Multi-Line RO'],
          data: [
            data.datasets[0].data[8],
            data.datasets[0].data[9],
            data.datasets[0].data[10]
          ],
          backgroundColor:
            props.chartId == 1337
              ? [
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5]
                ]
              : props.chartId == 1351
              ? [
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5]
                ]
              : [
                  data.datasets[0].backgroundColor[7],
                  data.datasets[0].backgroundColor[7],
                  data.datasets[0].backgroundColor[7]
                ],
          hoverOffset: 4
        }
      ]
    };
  } else if (
    props.chartId == 1344 &&
    !lodash.isEmpty(data) &&
    data.labels.length > 0
  ) {
    data1 = {
      data: [
        {
          name: 'Repair',
          y: Number(data.datasets[0].data[2])
        },
        {
          name: 'Maint',
          y: Number(data.datasets[0].data[1])
        },
        {
          name: 'Comp',
          y: Number(data.datasets[0].data[0]),
          sliced: true,
          selected: true
        }
      ]
    };
    data2 = {
      data: [
        ['Repair', Number(data.datasets[0].data[5])],
        ['Maint', Number(data.datasets[0].data[4])],
        ['Comp', Number(data.datasets[0].data[3])]
      ]
    };
  } else if (props.chartId == 1346 || props.chartId == 1353) {
    data1 = {
      labels: ['Target Hits', 'Target Misses'],
      datasets: [
        {
          label: 'My First Dataset',
          data: [data.datasets[0].data[2], data.datasets[0].data[1]],
          backgroundColor: [
            data.datasets[0].backgroundColor[1],
            data.datasets[0].backgroundColor[0]
          ],
          hoverOffset: 4
        }
      ]
    };
  }

  const backToKpiScoreCards = chartId => {
    return (
      <Tooltip title={<span style={{ fontSize: 11.5 }}>Return to Top</span>}>
        <IconButton
          size="medium"
          className="infoIcon"
          onClick={() => backToKpi(chartId)}
        >
          <AssignmentReturnOutlinedIcon />
        </IconButton>
      </Tooltip>
    );
  };
  const backToKpiOld = chartId => {
    var element = document.getElementById('kpiScoreCards');
    element.scrollIntoView({ block: 'center' });
    // element.scrollIntoView({
    //   behavior: 'smooth',
    //   block: 'center',
    //   inline: 'center'
    // });
    $('#chartContainer_' + chartId).removeClass('selected');
    // $('#visualization_' + chartId).css('display', 'none');
  };
  const backToKpi = chartId => {
    $('#chartContainer_' + chartId).removeClass('selected');
    history.push({
      pathname: '/Home'
      // state: {
      //   selectedToggle: props.toggleOption,
      //   parent: 'Home',
      //   payType: checked == true ? 'I' : 'C',
      //   gridType: selectedValue
      // }
    });
  };
  const options = getChartConfigurationKPIs(props.ROShareData, props.chartId);
  let title = getKpiChartTitle(
    props.chartId.toString(),
    props.chartId == 1353 ? gridTitleParts : gridTitle
  );

  options.plugins = {
    datalabels: {
      display: true,
      //   color: '#FFF',
      align: 'top',
      anchor: 'end',
      borderRadius: 3,
      font: {
        color: '#737373',
        weight: '600',
        size: 16
      },
      formatter: value => {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
    }
  };
  options.hover = { mode: null };
  const formatChartValues = (value, status) => {
    return value == null
      ? 0
      : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };
  return (
    <React.Fragment>
      {props.chartId == 1335 && (
        <div>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div className={classes.chartContainer1335}>
            <Bar data={data} options={options}></Bar>
          </div>
        </div>
      )}
      {props.chartId == 1336 && (
        <div>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div
            class="top"
            style={{ display: 'flex', justifyContent: 'space-around' }}
          >
            <div className="pie13361" style={{ width: '50%' }}>
              <Pie
                data={
                  !lodash.isEmpty(data1) && data1.labels.length > 0
                    ? data1
                    : data
                }
                options={getOptionsForPie1(
                  props.ROShareData,
                  1336,
                  session.serviceAdvisor
                )}
              ></Pie>
            </div>
            {/* <div
              className={
                props.ROShareData.length > 0 &&
                (props.ROShareData[2].val == 1 ||
                  props.ROShareData[1].val == null)
                  ? classes.pie13363
                  : 'pie13362'
              }
              style={{ width: '50%' }}
            >
              <Pie
                data={
                  !lodash.isEmpty(data2) && data2.labels.length > 0
                    ? data2
                    : data
                }
                options={getOptionsForPie2(
                  props.ROShareData,
                  1336,
                  '',
                  session.serviceAdvisor
                )}
              ></Pie>
            </div> */}
          </div>
        </div>
      )}

      {(props.chartId == 1337 ||
        props.chartId == 1338 ||
        props.chartId == 1351) && (
        <Grid>
          <div>
            <div className={classes.chartHeader}>
              <span className={classes.chartTitle}>
                <p>{title}</p>
              </span>
              <span
                className={classes.visualization}
                id={'visualization_' + props.chartId}
              >
                {backToKpiScoreCards(props.chartId)}
              </span>
            </div>
            <div
              class="top"
              style={{ display: 'flex', justifyContent: 'space-around' }}
            >
              <div style={{ marginLeft: -40 }}>
                <Doughnut
                  data={
                    !lodash.isEmpty(data1) && data1.labels.length > 0
                      ? data1
                      : data4
                  }
                  options={
                    props.chartId == 1337
                      ? getOptionsForPie1(props.LineROLtSixtyK, props.chartId)
                      : props.chartId == 1351
                      ? getOptionsForPie1(props.LineRO, props.chartId)
                      : getOptionsForPie1(props.LineROGtSixtyK, props.chartId)
                  }
                ></Doughnut>
              </div>
              <Paper className={classes.PaperGpp1337}>
                <div className={classes.HeaderComponent1337}>
                  <div>
                    <Typography variant="h6" className={classes.centerText1337}>
                      <span style={{ display: 'block' }}>Avg Jobs</span>
                      <span style={{ display: 'block' }}>Per Multi</span>
                    </Typography>
                    <Typography
                      variant="h5"
                      className={classes.centerValue1341}
                    >
                      {!lodash.isEmpty(data) &&
                      data.labels.length > 0 &&
                      data.datasets[0].data[11] != null
                        ? data.datasets[0].data[11]
                        : 0}
                    </Typography>
                  </div>
                </div>
              </Paper>
              <div style={{ marginLeft: -110 }}>
                <Doughnut
                  data={
                    !lodash.isEmpty(data2) && data2.labels.length > 0
                      ? data2
                      : data4
                  }
                  options={
                    props.chartId == 1337
                      ? getOptionsForPie2(props.LineROLtSixtyK, props.chartId)
                      : props.chartId == 1351
                      ? getOptionsForPie2(props.LineRO, props.chartId)
                      : getOptionsForPie2(props.LineROGtSixtyK, props.chartId)
                  }
                ></Doughnut>
              </div>
            </div>
            <div style={{ marginBottom: 30 }}></div>
            <div>
              <HorizontalBar
                height="200px"
                data={
                  !lodash.isEmpty(data3) && data3.labels.length > 0
                    ? data3
                    : data4
                }
                options={
                  props.chartId == 1337
                    ? getOptionsForPie2(
                        props.LineROLtSixtyK,
                        props.chartId,
                        'bar_chart'
                      )
                    : props.chartId == 1351
                    ? getOptionsForPie2(
                        props.LineRO,
                        props.chartId,
                        'bar_chart'
                      )
                    : getOptionsForPie2(
                        props.LineROGtSixtyK,
                        props.chartId,
                        'bar_chart'
                      )
                }
              ></HorizontalBar>
            </div>
          </div>
        </Grid>
      )}
      {(props.chartId == 1341 ||
        props.chartId == 1342 ||
        props.chartId == 1343) && (
        <div>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div
            className={
              props.chartId == 1343
                ? classes.mileageChart1343
                : classes.mileageChart
            }
          >
            <Grid xs={12} className={'LaborSale1341'}>
              <div
                style={{
                  display: 'flex',
                  justifyContent:
                    props.chartId == 1343 ? 'space-evenly' : 'center'
                }}
              >
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperSale1343
                      : classes.PaperSale1341
                  }
                >
                  <HighchartsReact
                    containerProps={{
                      style: {
                        height: props.chartId == 1343 ? 180 : 190,
                        width: 200
                      }
                    }}
                    options={getOptionsForColumnHighcharts(
                      !lodash.isEmpty(data) ? data.datasets[0] : data4,
                      'total',
                      props.chartId
                    )}
                    highcharts={Highcharts}
                  />
                </Paper>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperGpp1343
                      : props.chartId == 1342
                      ? classes.PaperGpp1342
                      : classes.PaperGpp1341
                  }
                >
                  <div className={classes.HeaderComponent1341}>
                    <div>
                      <Typography
                        variant="h6"
                        className={classes.centerText1341}
                      >
                        {/* Gross Profit */}
                        {props.chartId == 1342 ? 'Parts GP' : 'Labor GP'}
                      </Typography>
                      <Typography
                        variant="h5"
                        className={classes.centerValue1341}
                      >
                        {!lodash.isEmpty(data) &&
                        data.labels.length > 0 &&
                        data.datasets[0].data[2] != null
                          ? data.datasets[0].data[2] + '%'
                          : 0}
                      </Typography>
                    </div>
                  </div>
                  {/* </Grid> */}
                </Paper>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperGp1343
                      : classes.PaperGp1341
                  }
                >
                  <HighchartsReact
                    containerProps={{
                      style: {
                        height: props.chartId == 1343 ? 180 : 190,
                        width: 200
                      }
                    }}
                    options={getOptionsForColumnHighcharts(
                      !lodash.isEmpty(data) ? data.datasets[0] : data4,
                      'per ro',
                      props.chartId
                    )}
                    highcharts={Highcharts}
                  />
                </Paper>
              </div>
            </Grid>
          </div>
        </div>
      )}
      {props.chartId == 1344 && (
        <div>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div class="top">
            <div style={{ marginLeft: '29%' }}>
              <HighchartsReact
                containerProps={{ style: { height: 180, width: 300 } }}
                options={getOptionsForHighchartsPie(
                  !lodash.isEmpty(data1) ? data1.data : data4
                )}
                highcharts={Highcharts}
              />
            </div>
            <div></div>
            <div style={{ marginLeft: '2%', marginTop: '0%' }}>
              <HighchartsReact
                containerProps={{ style: { height: 180, width: 500 } }}
                options={getOptionsForHighChart(
                  !lodash.isEmpty(data2) ? data2.data : data4
                )}
                highcharts={Highcharts}
              />
            </div>
          </div>
        </div>
      )}
      {props.chartId == 1339 && (
        <div>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div className={classes.chartContainer1339}>
            <Grid
              container
              className={clsx(
                classes.container,
                classes.dataContainer,
                'kpi-soldhrs-block'
              )}
            >
              {/* <div style={{ display: 'flex', marginLeft: 10, width: '100%' }}> */}
              <div className="flatRateLeftBlock">
                {/* style={{width: '100%',marginLeft: 10, marginBottom: 10}} */}
                <DataCard
                  icon={'allsold-hours'}
                  title={'All Sold Hours'}
                  value={
                    !lodash.isEmpty(data) &&
                    data.labels.length > 0 &&
                    data.datasets[0].data[0] != null
                      ? data.datasets[0].data[0]
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                      : ''
                  }
                  color={'#20B2AA	'}
                  // margin={10}
                  float={'right'}
                />
                <DataCard
                  icon={'allsold-hours-perdayavg'}
                  title={'All Sold Hours Per Day Avg'}
                  value={
                    !lodash.isEmpty(data) &&
                    data.labels.length > 0 &&
                    data.datasets[0].data[1] != null
                      ? data.datasets[0].data[1]
                      : 0
                  }
                  color={'#3CB371	'}
                  margin={6}
                />
              </div>

              {/* <div style={{marginTop: 15, marginLeft: 157}}> */}
              <div className={'hrsPerRoGrid'}>
                <DataCard
                  icon={'cphours-perro'}
                  title={'CP Hours Per RO'}
                  value={
                    !lodash.isEmpty(data) && data.labels.length > 0
                      ? data.datasets[0].data[2] != null
                        ? data.datasets[0].data[2]
                        : 0
                      : ''
                  }
                  color={'#0B504F'}
                  height={157}
                  margin={-24}
                  marginToph6={10}
                  marginTop={-15}
                  marginTopIcon={'15%'}
                />
              </div>
            </Grid>
          </div>
        </div>
      )}
      {props.chartId == 1340 && (
        <div>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div className={'top'} style={{ fontFamily: 'Roboto' }}>
            <div className={classes.container1340_1}>
              <ReactSpeedometer
                minValue={0}
                maxValue={100000}
                labelFontSize={'14px'}
                valueTextFontSize={'20px'}
                fontFamily={[
                  'Roboto',
                  'Helvetica',
                  'Arial',
                  'sans - serif'
                ].join(',')}
                segments={4}
                ringWidth={70}
                segmentColors={['#008000', '#7ab55c', '#ec5555', '#b81414']}
                valueFormat={'~s'}
                // currentValueText={
                //   !lodash.isEmpty(data) &&
                //   data.labels.length > 0 &&
                //   data.datasets[0].data[1] != null
                //     ? data.datasets[0].data[1]
                //         .toString()
                //         .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' Mi'
                //     : ''
                // }
                currentValueText={
                  !lodash.isEmpty(data) &&
                  data.labels.length > 0 &&
                  data.datasets[0].data[1] != null
                    ? Math.round(data.datasets[0].data[0] * 2) / 2 +
                      ' Yrs' +
                      ' / ' +
                      data.datasets[0].data[1]
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
                      ' Mi'
                    : ''
                }
                value={
                  !lodash.isEmpty(data) && data.labels.length > 0
                    ? data.datasets[0].data[1]
                    : 0
                }
                // valueFormat={number => number+'%'}
              />
            </div>
            {/* <div></div>
            <div className={'container1340'}>
              <HighchartsReact
                options={getOptionsForHighchartsLinearGauge(
                  !lodash.isEmpty(data) && data.labels.length > 0
                    ? data.datasets[0].data[0]
                    : data4
                )}
                highcharts={Highcharts}
              />
            </div> */}
          </div>
        </div>
      )}

      {props.chartId == 1346 && (
        <div>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div className={'kpi-labor-grid'}>
            <div
              className={'kpi-labor-grid-top'}
              //   style={{ display: 'flex', justifyContent: 'left' ,marginLeft: '10%'}}
            >
              <div>
                <Typography
                  variant="h4"
                  color="primary"
                  style={{ marginLeft: '25%', fontSize: 25 }}
                  className={classes.valLabel}
                >
                  {props.LaborGrid.length > 0
                    ? formatChartValues(props.LaborGrid[0].val)
                    : 0}
                </Typography>
                <Typography
                  variant="h6"
                  color="primary"
                  style={{ marginLeft: '-16%' }}
                  className={classes.valLabel}
                >
                  {gridTitle + ' Repair Target Opportunities'}
                </Typography>
              </div>
            </div>
            <div class="top1353">
              <Doughnut
                id="chart-area-1346"
                width="610px"
                height="190px"
                data={
                  !lodash.isEmpty(data1) && data1.labels.length > 0
                    ? data1
                    : data4
                }
                plugins={[
                  {
                    afterDraw: chart => {
                      var ctx = chart.chart.ctx;
                      ctx.save();
                      var image = new Image();
                      image.src = '/images/kpis/target1.png';
                      // var imageSize = 70;
                      // ctx.drawImage(image, 224, 45, imageSize, imageSize);
                      var imageSize = 95;
                      ctx.drawImage(image, 199, 50, imageSize, imageSize);
                      //  ctx.drawImage(image, 87, 45, imageSize, imageSize);
                      ctx.restore();
                    }
                  }
                ]}
                options={getOptionsForPie1346(
                  props.LaborGrid,
                  props.chartId,
                  session.serviceAdvisor
                )}
              ></Doughnut>
            </div>
            <div
              className={'kpi-labor-grid-bottom'}
              //   style={{ marginRight: '2%', marginTop: '-7%', textAlign: 'right' }}
            >
              <Typography
                variant="h6"
                color="primary"
                style={{ marginLeft: '20%' }}
                className={classes.valLabel}
              >
                {'Non-Compliant'}
              </Typography>
              <Typography
                variant="h4"
                color="primary"
                className={classes.valLabel1346}
                style={{ marginRight: '5%', fontSize: 25, color: '#dc3912' }}
              >
                {props.LaborGrid.length > 0
                  ? (props.LaborGrid[2].val == null
                      ? 0
                      : props.LaborGrid[2].val) + '%'
                  : 0 + '%'}
              </Typography>
            </div>
          </div>
        </div>
      )}
      {props.chartId == 1353 && (
        <div style={{ height: 349 }}>
          <div className={classes.chartHeader}>
            <span className={classes.chartTitle}>
              <p>{title}</p>
            </span>
            <span
              className={classes.visualization}
              id={'visualization_' + props.chartId}
            >
              {backToKpiScoreCards(props.chartId)}
            </span>
          </div>
          <div className={'kpi-parts-grid'}>
            <div
              className={'kpi-labor-grid-top'}
              //   style={{ display: 'flex', justifyContent: 'left' ,marginLeft: '10%'}}
            >
              <div>
                <Typography
                  variant="h4"
                  color="primary"
                  style={{ marginLeft: '25%', fontSize: 25 }}
                  className={classes.valLabel}
                >
                  {props.PartsGrid.length > 0
                    ? formatChartValues(props.PartsGrid[0].val)
                    : 0}
                </Typography>
                <Typography
                  variant="h6"
                  color="primary"
                  style={{ marginLeft: '-16%' }}
                  className={classes.valLabel}
                >
                  {gridTitleParts + ' Parts Target Opportunities'}
                </Typography>
              </div>
            </div>
            <div class="top1353">
              <Doughnut
                id="chart-area-1353"
                width="610px"
                height="190px"
                data={
                  !lodash.isEmpty(data1) && data1.labels.length > 0
                    ? data1
                    : data4
                }
                plugins={[
                  {
                    afterDatasetsDraw: chart => {
                      var ctx = chart.chart.ctx;
                      ctx.save();
                      var image = new Image();
                      image.src = '/images/kpis/target1.png';
                      // var imageSize = 70;
                      // ctx.drawImage(image, 224, 45, imageSize, imageSize);
                      var imageSize = 95;
                      ctx.drawImage(image, 199, 50, imageSize, imageSize);
                      //  ctx.drawImage(image, 87, 45, imageSize, imageSize);
                      ctx.restore();
                    }
                  }
                ]}
                options={getOptionsForPie1346(
                  props.PartsGrid,
                  props.chartId,
                  session.serviceAdvisor
                )}
              ></Doughnut>
            </div>
            <div
              className={'kpi-labor-grid-bottom'}
              //    style={{ marginRight: '2%', marginTop: '-7%', textAlign: 'right' }}
            >
              <Typography
                variant="h6"
                color="primary"
                style={{ marginLeft: '20%' }}
                className={classes.valLabel}
              >
                {'Non-Compliant'}
              </Typography>
              <Typography
                variant="h4"
                color="primary"
                className={classes.valLabel1346}
                style={{ marginRight: '5%', fontSize: 25, color: '#dc3912' }}
              >
                {props.PartsGrid.length > 0
                  ? (props.PartsGrid[2].val == null
                      ? 0
                      : props.PartsGrid[2].val) + '%'
                  : 0 + '%'}
              </Typography>
            </div>
          </div>
        </div>
      )}
    </React.Fragment>
  );
};
export default KPISummaryCharts;
