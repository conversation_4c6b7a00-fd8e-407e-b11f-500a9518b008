import { QueryRenderer } from '@cubejs-client/react';
import {
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid
} from '@material-ui/core';
import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
import { useHistory } from 'react-router';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton.js';
import MoreActions from '../MoreActions';
import PropTypes from 'prop-types';
import {
  getDataforBarChart,
  getBarChartConfiguration,
  getChartDrillDown,
  getChartDataIndex,
  getChartPayType,
  getChartOpCategory,
  getSubHeader,
  GetyAxisRange
} from 'src/components/ViewGraphDetailsAction';
import { getQueryForDetails } from 'src/components/GraphDetailsQuery';
import { Bar } from 'react-chartjs-2';
import moment from 'moment';
import { useSelector } from 'react-redux';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import HasuraDashboardBarRenderer from './HasuraDashboardBarRenderer';
import { getHasuraRenderedCharts, getDbdName } from 'src/utils/Utils';
import { getDashboardAggrigationQuery } from 'src/components/DashboardAggrigationQuery';
import clsx from 'clsx';

const dateFormatter = item => moment(item).format('MMM');
var lodash = require('lodash');
const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;

const GraphDetailsView = ({
  chartId,
  yAxisRanges,
  title,
  isStacked,
  location,
  realm,
  parentId,
  isFrom,
  selected,
  selectedChartId,
  handleHighlightContainer,
  classes,
  chartData
}) => {
  const [hasuraData, setHasuraData] = useState([]);
  const [dashboard, setDashboard] = useState([]);
  const [loader, setLoader] = useState(false);
  const session = useSelector(state => state.session);
  const history = useHistory();

  const [searchText] = history.location.search
    .split('?title=')
    .pop()
    .split('?');
  const [queryState, queryChangedState] = useState([]);
  const getFilters = advisors => {
    let arraylenghth = advisors.length;
    let combiner = `'{`;
    advisors.map((each, index) => {
      combiner = combiner + each + ',';
    });
    combiner = combiner.substring(0, combiner.length - 1) + `}'`;
    return combiner;
  };

  useEffect(() => {
    var dashboardName = getDbdName(parentId);
    setDashboard(dashboardName);

    // if (session.serviceAdvisor.includes('All') ) {
    //   queryChangedState(
    //     getQueryForDetails(chartId, session.serviceAdvisor, realm)
    //   );
    // } else {
    if (
      dashboardName == 'CP Overview' ||
      dashboardName == 'CP Labor Overview' ||
      dashboardName == 'CP Parts Overview' ||
      (dashboardName == 'Special Metrics' && parentId == 1239)
    ) {
      setLoader(true);
      getChartsDataFromViews(
        dashboardName == 'CP Overview' ||
          dashboardName == 'CP Labor Overview' ||
          dashboardName == 'CP Parts Overview' ||
          (dashboardName == 'Special Metrics' && parentId == 1239)
          ? session.serviceAdvisor
          : getFilters(session.serviceAdvisor),

        dashboardName == 'CP Overview' &&
          (parentId == 942 || parentId == 939 || parentId == 940)
          ? parentId
          : chartId,

        callback => {
          if (callback) {
            dashboardName == 'CP Overview' ||
            dashboardName == 'CP Labor Overview' ||
            dashboardName == 'CP Parts Overview' ||
            (dashboardName == 'Special Metrics' && parentId == 1239)
              ? setHasuraData(JSON.parse(callback[0].jsonData))
              : setHasuraData(callback);
            setLoader(false);
          }
        }
      );
    } else {
      queryChangedState(
        getQueryForDetails(chartId, session.serviceAdvisor, realm)
      );
    }
    // }
  }, [session.serviceAdvisor]);
  const handleHighlight = chartId => {
    handleHighlightContainer(chartId);
  };

  const barRender = ({ resultSet }) => {
    const data = getDataforBarChart(resultSet, chartId);
    const options = getBarChartConfiguration(
      chartId,
      yAxisRanges,
      isStacked,
      realm,
      parentId,
      isFrom
    );
    const result = (
      <Card
        bordered={false}
        style={{ height: '100%', borderRadius: 0, border: '1px solid #003d6b' }}
      >
        <CardHeader
          title={title}
          subheader={getSubHeader(chartId)}
          style={{ borderBottom: '1px solid #003d6b' }}
        ></CardHeader>

        <Divider />
        <CardContent style={{ cursor: 'pointer' }}>
          <Bar
            data={data}
            options={options}
            getElementAtEvent={dataset => {
              if (dataset.length > 0) {
                var dataIndex = dataset[0]._datasetIndex;
                var valueIndex = dataset[0]._index;
                var xAxis = data.datasets[dataIndex].data[valueIndex];
                var yAxis = data.labels[valueIndex];
                var label = data.datasets[dataIndex].label.trim();

                history.push({
                  pathname: '/AnalyzeData',
                  search: '?chartId=' + 'drillDown',
                  prevPath: window.location.pathname + '?chartId=' + parentId,
                  // prevPath:window.location.pathname,
                  state: {
                    chartId: chartId,
                    x: xAxis,
                    y: yAxis,
                    drillDown: getChartDrillDown(chartId, searchText),
                    chartName: title,
                    category: getChartDataIndex(chartId, '', '', label),
                    payType: getChartPayType(chartId),
                    opCategory: getChartOpCategory(chartId),
                    titleCategory: searchText,
                    parentId: parentId,
                    prevPath: window.location.pathname + '?chartId=' + parentId
                  },
                  handleHighlight: selectedChartId,
                  selectedGrid: selected
                });
              }
            }}
          />
        </CardContent>
      </Card>
    );

    return result;
  };

  const dashboardParentMap = {
    'CP Overview': ['942', '939', '940', '1238'],
    'CP Labor Overview': [
      '960',
      '944',
      '1133',
      '1127',
      '1044',
      '955',
      '918',
      '1138'
    ],
    'CP Parts Overview': ['1049', '952', '953', '916', '1143', '1318', '1326'],
    'Special Metrics': ['1239']
  };

  const renderMultipleCharts = dashboardParentMap[dashboard]?.includes(
    parentId
  );

  return (
    <>
      {renderMultipleCharts ? (
        chartData.map((item, index) => {
          return (
            <Grid
              item
              xs={6}
              className={clsx(classes.gridContainer, 'diagram-section')}
            >
              <HasuraDashboardBarRenderer
                chartId={Number(item.chartId)}
                showTopBar={false}
                resultSet={hasuraData}
                yAxisRanges={GetyAxisRange(item.chartId)}
                title={item.chartName}
                isStacked={isStacked}
                searchText={searchText}
                parentId={parentId}
                realm={realm}
                serviceAdvisors={session.serviceAdvisor}
                handleHighlight={handleHighlight}
                selected={selected}
                selectedChartId={selectedChartId}
                dashboard={dashboard}
                loader={loader}
              />
            </Grid>
          );
        })
      ) : (!session.serviceAdvisor.includes('All') &&
          getHasuraRenderedCharts().includes(chartId.toString()) == true) ||
        dashboard == 'CP Overview' ||
        dashboard == 'CP Labor Overview' ||
        dashboard == 'CP Parts Overview' ||
        (dashboard == 'Special Metrics' && parentId == 1239) ? (
        <HasuraDashboardBarRenderer
          chartId={chartId}
          showTopBar={false}
          resultSet={hasuraData}
          yAxisRanges={yAxisRanges}
          title={title}
          isStacked={isStacked}
          searchText={searchText}
          parentId={parentId}
          realm={realm}
          serviceAdvisors={session.serviceAdvisor}
          handleHighlight={handleHighlight}
          selected={selected}
          selectedChartId={selectedChartId}
          dashboard={dashboard}
          loader={loader}
        />
      ) : (
        <QueryRenderer
          query={queryState}
          cubejsApi={cubejsApi()}
          render={renderChart(barRender)}
        />
      )}
    </>
  );
};
GraphDetailsView.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
  yAxisRanges: PropTypes.array
};

export default GraphDetailsView;
