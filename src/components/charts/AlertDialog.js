import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import Slide from '@material-ui/core/Slide';
import { Grid } from '@material-ui/core';
import SearchByRo from 'src/views/SearchByRo';
import { makeStyles } from '@material-ui/core/styles';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {},
  menuItem: {
    padding: 0
  },
  cardControl: {
    padding: 0
  },
  formControlLabel: {
    width: '100%',
    color: '#FFF',
    margin: 0
  },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(2),
    top: theme.spacing(1.5),
    color: theme.palette.grey[500]
  },
  paper: {
    maxWidth: '1150px',
    maxHeight: '700px'
  },
  paperTranches: {
    // maxWidth: '1250px',
    // maxHeight: '700px'
    maxWidth: window.innerWidth - 80 + 'px',
    maxHeight: window.innerHeight - 40 + 'px'
  },
  paperBar: {
    maxWidth: '1300px',
    maxHeight: '1400px'
  }
}));
// const Transition = React.forwardRef(function Transition(props, ref) {
//   return <Slide direction="up" ref={ref} {...props} />;
// });
export default function AlertDialog({
  open,
  selectedRo,
  handlePopupClose,
  isFRom,
  type,
  data,
  tab
}) {
  const classes = useStyles();
  // const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(open);
  // const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setOpenDialog(open);
  }, [open]);
  const handleClose = () => {
    setOpenDialog(false);
    handlePopupClose(1);
    // dispatch(setZoomChartId(''));
    // dispatch(setZoomStatus(false));
  };

  // const removeFav = value => {
  //   setIsLoading(false);
  // };

  return (
    <Dialog
      fullWidth
      //  TransitionComponent={Transition}
      classes={{
        paper: classes.paperTranches
        // chartType == 'line' || chartType == 'barChartRenderer'
        //   ? classes.paper
        //   : chartType == 'partsMarkup'
        //   ? classes.paperTranches
        //   : classes.paperBar
        // paperFullScreen: classes.paperFullScreen
      }}
      //maxWidth="xl"
      //style={{ maxWidth: 900, maxHeight: 700 }}
      open={openDialog}
      //onClose={handleClose}
    >
      {/* <DialogTitle id="max-width-dialog-title" style={{ marginBottom: 20 }}>
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle> */}
      <DialogContent style={{ marginBottom: 20 }}>
        <Box
          noValidate
          component="form"
          sx={{
            display: 'flex',
            flexDirection: 'column',
            m: 'auto',
            width: '100%'
            //  width: 'fit-content'
          }}
        >
          <IconButton
            aria-label="close"
            className={classes.closeButton}
            onClick={handleClose}
          >
            <CloseIcon />
          </IconButton>
          <Grid item xs={12} justify="flex-start" style={{ padding: '5px' }}>
            <SearchByRo
              selectedRo={selectedRo}
              isFrom="itemization"
              type={type}
              data={data}
              tab={tab}
            />
          </Grid>
        </Box>
      </DialogContent>
    </Dialog>
  );
}

//export default ChartDialog;
