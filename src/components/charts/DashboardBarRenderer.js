import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import {
  getChartType,
  getLineChartType,
  GetyAxisRange,
  getChartName,
  getChartDrillDown,
  getChartDataIndex,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import {
  getChartConfiguration,
  getDataforLineChart,
  getDataforLineChartFromHasura,
  applyPartialMonthFilterLineCharts,
  getDataforLineChartFromPostgraphile,
  getDbdName
} from '../../utils/Utils';
import {
  Card,
  CardHeader,
  CardContent,
  Divider,
  Tooltip,
  Grid,
  CircularProgress
} from '@material-ui/core';
import MoreActions from '../MoreActions';
import { useHistory } from 'react-router';
import { makeStyles } from '@material-ui/core/styles';
import ChartDialog from 'src/components/Dialog';
import { useSelector, useDispatch } from 'react-redux';
import { Bar } from 'react-chartjs-2';

var lodash = require('lodash');

const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
const DashboardBarRenderer = ({
  chartId,
  removeFav,
  isFrom,
  parentId,
  handleClosePopup,
  type,
  realm,
  tabSelection,
  parent,
  selectedToggle,
  backTobutton,
  handleHighlightContainer,
  selected,
  selectedChartId,
  headerClick,
  chartData,
  allData,
  parentFrom
}) => {
  const classes = useStyles();
  const history = useHistory();
  const [open, setOpen] = useState(false);
  const [loader, setLoader] = useState(false);
  const [mounted, setMounted] = useState(false);

  const [hasuraData, setHasuraData] = useState([]);
  const session = useSelector(state => state.session);
  const [stepSize, setstepSize] = useState(
    typeof GetyAxisRange(chartId, isFrom) !== 'undefined'
      ? GetyAxisRange(chartId, isFrom)
      : ''
  );

  useEffect(() => {
    setLoader(true);
    setMounted(true);
    //console.log('sssss', parentFrom, chartData, allData);
    // Use chartData if available, otherwise fetch from API
    if (chartData) {
      setHasuraData([chartData]);
      setLoader(false);
    } else if (
      parentFrom == 'detail_page' ||
      (parentFrom == 'source_page' && [938, 1239, 1316, 1317].includes(chartId))
    ) {
      // Only fetch from API if chartData is undefined AND chartId is in the allowed list

      getChartsDataFromViews(session.serviceAdvisor, chartId, callback => {
        if (callback) {
          if (chartId == 1115 || chartId == 1232) {
            let data = JSON.parse(callback[0].jsonData)[0].datasets;
            let arr = [];
            let arr1 = [];
            let obj = {};
            if (chartId == 1115) {
              arr.push(lodash.find(data, ['label', 'Total CP Sale %']));
            } else if (chartId == 1232) {
              arr.push(lodash.find(data, ['label', '% Discounted']));
            } else if (chartId == 1235) {
              arr.push(
                lodash.filter(data, ({ label }) => label !== 'Total CP Sale %')
              );
            }
            obj.datasets = arr;
            obj.labels = JSON.parse(callback[0].jsonData)[0].labels;
            arr1.push(obj);
            setHasuraData(arr1);
          } else if (chartId == 1235 || chartId == 1233) {
            let data = JSON.parse(callback[0].jsonData)[0].datasets;
            let dataSet;
            if (chartId == 1235) {
              dataSet = lodash.filter(
                data,
                ({ label }) => label !== 'Total CP Sale %'
              );
            } else if (chartId == 1233) {
              dataSet = lodash.filter(
                data,
                ({ label }) => label !== '% Discounted'
              );
            }
            let obj = {};
            let arr1 = [];
            obj.datasets = dataSet;
            obj.labels = JSON.parse(callback[0].jsonData)[0].labels;
            arr1.push(obj);
            setHasuraData(arr1);
          } else if (chartId == 1236 || chartId == 1165 || chartId == 1237) {
            let data = JSON.parse(callback[0].jsonData);
            if (chartId == 1237) {
              data = data[0].datasets.filter(
                i => i.chartId == '1165' && i.label != 'Total Discount'
              );
            } else {
              data = data[0].datasets.filter(i => i.chartId == chartId);
            }
            if (chartId == 1165) {
              data = data.filter(i => i.label == 'Total Discount');
            }
            let obj = {};
            let arr1 = [];
            obj.datasets = data;
            obj.labels = JSON.parse(callback[0].jsonData)[0].labels;
            arr1.push(obj);
            setHasuraData(arr1);
          } else if (chartId == 1239) {
            let data = JSON.parse(callback[0].jsonData);
            let arr1 = [];
            let obj = {};
            data = data[0].datasets
              .filter(i => i.label !== 'Combined')
              .map(dataset => ({
                ...dataset,
                data: dataset.data.map(value => (value < 0 ? 0 : value))
              }));

            obj.datasets = data;
            obj.labels = JSON.parse(callback[0].jsonData)[0].labels;
            arr1.push(obj);
            setHasuraData(arr1);
          } else {
            setHasuraData(JSON.parse(callback[0].jsonData));
          }
          setLoader(false);
        }
      });
    }
    // If chartData is null/empty array, don't fetch - wait for data
  }, [session.serviceAdvisor, chartData]);

  var dashboardName = getDbdName(chartId);
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        SelectedLocation: window.location.pathname,
        handleHighlight: selectedChartId,
        selectedGrid: selected
      });
    }
  };
  if (mounted == true) {
    var chartType = getChartType(chartId, isFrom);
    var lineChartType = getLineChartType(chartId);

    const data = getDataforLineChart(
      hasuraData,
      lineChartType,
      undefined,
      chartId
    );

    let isEmpty = false;
    if (hasuraData.length > 0) {
      isEmpty = lodash.every(hasuraData[0].datasets, item => {
        return lodash.every(item.data, value => parseFloat(value) === 0);
      });
    }
    const options = getChartConfiguration(
      chartType,
      stepSize,
      '',
      '',
      hasuraData,
      chartId,
      session.serviceAdvisor.includes('All') ? false : true,
      '',
      isFrom,
      '',
      '',
      '',
      type
    );

    const removeFavourite = val => {
      removeFav(val);
      if (selectedChartId) handleHighlightContainer(val);
    };
    const chartPopup = val => {
      localStorage.setItem('popup', true);
      // handleChartPopup(val);
      console.log('chartIds==11', chartId, chartData);
      setOpen(true);
    };
    const handleClose = () => {
      localStorage.setItem('popup', false);
      setOpen(false);
      handleClosePopup(1);
    };
    const handleBackToButton = chartId => {
      backTobutton(chartId);
    };
    const handleHighlight = chartId => {
      handleHighlightContainer(chartId);
    };
    return (
      <>
        {loader == true || (!chartData && hasuraData.length === 0) ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : (
          <Card
            bordered={false}
            style={{
              height: '100%',
              borderRadius: 0,
              border: '1px solid #003d6b'
            }}
          >
            <CardHeader
              title={getChartName(chartId)}
              action={
                <MoreActions
                  removeFavourite={removeFavourite}
                  setActions={setactions}
                  chartId={chartId}
                  type={type}
                  chartPopup={chartPopup}
                  handleClose={handleClose}
                  handleBackToButton={handleBackToButton}
                  handleHighlight={handleHighlight}
                  selectedChartId={selectedChartId}
                ></MoreActions>
              }
              subheader={getSubHeader(chartId)}
              style={{ borderBottom: '1px solid #003d6b' }}
              titleTypographyProps={{ variant: 'h6' }}
              //   onClick={() => headerClick(chartId)}
              id={'card-header-' + chartId}
            ></CardHeader>
            <Divider />
            <CardContent
              className={
                history.location.pathname === '/GraphDetailsView'
                  ? 'barChartContainer'
                  : ''
              }
              style={{
                cursor: 'pointer',
                height:
                  (chartId == 923 ||
                    chartId == 1355 ||
                    chartId == 1354 ||
                    chartId == 930 ||
                    chartId == 935 ||
                    chartId == 936 ||
                    chartId == 1357 ||
                    chartId == 938 ||
                    chartId == 948 ||
                    chartId == 1239 ||
                    chartId == 1316 ||
                    chartId == 1317) &&
                  window.location.pathname != '/GraphDetailsView'
                    ? '90%'
                    : window.location.pathname == '/MyFavorites'
                    ? '90%'
                    : window.location.pathname == '/GraphDetailsView'
                    ? '70%'
                    : '80%'
              }}
            >
              {isEmpty ? (
                <div id={chartId} className={'divNoData'}>
                  No Data To Display
                </div>
              ) : (
                <Bar
                  id={'chart_' + chartId}
                  data={data}
                  options={options}
                  getElementAtEvent={dataset => {
                    if (dataset.length > 0) {
                      var dataIndex = dataset[0]._datasetIndex;
                      var valueIndex = dataset[0]._index;
                      var xAxis = data.datasets[dataIndex].data[valueIndex];
                      var yAxis = data.labels[valueIndex];
                      var label = data.datasets[dataIndex].label.trim();
                      if (
                        Number(chartId) !== 942 &&
                        Number(chartId) !== 939 &&
                        Number(chartId) !== 946 &&
                        Number(chartId) !== 1127 &&
                        Number(chartId) !== 940 &&
                        Number(chartId) !== 920 &&
                        Number(chartId) !== 925 &&
                        Number(chartId) !== 1143 &&
                        Number(chartId) !== 1138 &&
                        Number(chartId) !== 1099 &&
                        Number(chartId) !== 1100 &&
                        Number(chartId) !== 1101 &&
                        Number(chartId) !== 1102 &&
                        Number(chartId) !== 1103 &&
                        Number(chartId) !== 1104 &&
                        Number(chartId) !== 1105 &&
                        Number(chartId) !== 1106 &&
                        Number(chartId) !== 1107 &&
                        Number(chartId) !== 1108 &&
                        Number(chartId) !== 1109 &&
                        Number(chartId) !== 1110 &&
                        Number(chartId) !== 1117 &&
                        Number(chartId) !== 1116 &&
                        Number(chartId) !== 1111 &&
                        Number(chartId) !== 1113 &&
                        Number(chartId) !== 948 &&
                        Number(chartId) !== 923 &&
                        Number(chartId) !== 1354 &&
                        Number(chartId) !== 1355 &&
                        Number(chartId) !== 938 &&
                        Number(chartId) !== 935 &&
                        Number(chartId) !== 930 &&
                        Number(chartId) !== 936 &&
                        // Number(chartId) !== 1357 &&
                        Number(chartId) !== 1133 &&
                        Number(chartId) !== 957 &&
                        Number(chartId) !== 1123 &&
                        Number(chartId) !== 1115 &&
                        Number(chartId) !== 1164 &&
                        Number(chartId) !== 1165 &&
                        Number(chartId) !== 1174 &&
                        Number(chartId) !== 1232 &&
                        Number(chartId) !== 1233 &&
                        Number(chartId) !== 1234 &&
                        Number(chartId) !== 1235 &&
                        Number(chartId) !== 1236 &&
                        Number(chartId) !== 1237 &&
                        Number(chartId) !== 1175 &&
                        Number(chartId) !== 1239 &&
                        Number(chartId) !== 1316 &&
                        Number(chartId) !== 1317 &&
                        Number(chartId) !== 1326
                      ) {
                        var splittedMonth = yAxis.split('-', 2);
                        var yearMonth =
                          data.datasets[dataIndex].label +
                          '-' +
                          splittedMonth[1];
                      }
                      if (headerClick) headerClick(chartId);
                      history.push({
                        pathname: '/AnalyzeData',
                        prevPath:
                          window.location.pathname === '/GraphDetailsView'
                            ? //Number(chartId) === 1234 ||
                              Number(chartId) === 1235 ||
                              Number(chartId) === 1233 ||
                              //Number(chartId) === 1236 ||
                              Number(chartId) === 1237
                              ? window.location.pathname +
                                '?chartId=' +
                                parentId
                              : window.location.pathname + '?chartId=' + chartId
                            : window.location.pathname,
                        search:
                          Number(chartId) === 948 ||
                          Number(chartId) === 930 ||
                          Number(chartId) === 923 ||
                          Number(chartId) === 1355 ||
                          Number(chartId) === 935 ||
                          Number(chartId) === 936 ||
                          // Number(chartId) === 1357 ||
                          Number(chartId) === 938 ||
                          Number(chartId) === 1174 ||
                          Number(chartId) === 1175
                            ? '?chartId=' + 'drillDown'
                            : '?chartId=' + chartId,
                        state: {
                          chartId: chartId,
                          x: xAxis,
                          y:
                            typeof yearMonth !== 'undefined' &&
                            Number(chartId) != 1357
                              ? yearMonth
                              : yAxis,
                          drillDown: getChartDrillDown(chartId, isFrom),
                          chartName: getChartName(chartId),
                          parent: parent,
                          selectedToggle: selectedToggle,
                          tabSelection:
                            isFrom == 'Discounts' ? 'three' : tabSelection,
                          category: getChartDataIndex(
                            chartId,
                            dataIndex,
                            isFrom,
                            label
                          ),

                          isFromMetrices:
                            Number(chartId) === 938 ? true : false,
                          prevPath:
                            window.location.pathname === '/GraphDetailsView'
                              ? //Number(chartId) === 1234 ||
                                Number(chartId) === 1235 ||
                                Number(chartId) === 1233 ||
                                //Number(chartId) === 1236 ||
                                Number(chartId) === 1237
                                ? window.location.pathname +
                                  '?chartId=' +
                                  parentId
                                : window.location.pathname +
                                  '?chartId=' +
                                  chartId
                              : window.location.pathname
                        },
                        handleHighlight: selectedChartId,
                        selectedGrid: selected
                      });
                    }
                  }}
                />
              )}
            </CardContent>

            <ChartDialog
              open={open}
              chartId={chartId}
              chartType="barChartRenderer"
              realm={realm}
              handlePopupClose={handleClose}
              headerClick={headerClick}
              selected={selected}
              selectedChartId={selectedChartId}
              handleHighlight={handleHighlight}
              chartData={allData}
              allData={allData}
            />
          </Card>
        )}
      </>
    );
  } else {
    return null;
  }
};

DashboardBarRenderer.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
  yAxisRanges: PropTypes.array,
  filterCharts: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default DashboardBarRenderer;
