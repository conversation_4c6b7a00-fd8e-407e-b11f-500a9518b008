import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { useHistory } from 'react-router';
import {
  getDataforHasuraBarChart,
  getBarChartConfiguration,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import MoreActions from '../MoreActions';
import ChartDialog from 'src/components/Dialog';
import { getColorScheme } from '../../utils/Utils';
import LoaderSkeleton from '../LoaderSkeleton';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import { useSelector, useDispatch } from 'react-redux';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
const AdvisorOpportunityCharts = ({
  chartId,
  title,
  resultSet,
  showTopBar,
  realm,
  handleClose,
  type,
  backTobutton,
  handleHighlight,
  selected,
  selectedChartId,
  removeFavourite,
  headerClick,
  isFrom
}) => {
  const history = useHistory();
  const [open, setOpen] = useState(false);
  const session = useSelector(state => state.session);
  const [isLoading, setLoading] = useState(true);
  const [hasuraData, setHasuraData] = useState([]);
  const [mounted, setMounted] = useState(false);
  // const [loader, setLoader] = useState(false);

  useEffect(() => {
    setLoading(true);
    setMounted(true);
    getChartsDataFromViews(session.kpiAdvisor[0], chartId, callback => {
      if (callback) {
        setHasuraData(callback);

        setLoading(false);
        //  console.log('callback', callback, localStorage.getItem('12Months'));
      }
    });
  }, [session.kpiAdvisor]);
  if (mounted == true && isLoading == false) {
    const data = getDataforHasuraBarChart(hasuraData, chartId);

    data.datasets.forEach(dataset => {
      dataset.data = dataset.data.map(value =>
        parseFloat(value) > 0 ? value : ''
      );
    });
    const options = getBarChartConfiguration(
      chartId,
      undefined,
      true,
      '',
      '',
      '',
      hasuraData,
      true
    );

    const chartPopup = val => {
      localStorage.setItem('popup', true);
      setOpen(true);
    };
    const handleCloses = () => {
      localStorage.setItem('popup', false);
      setOpen(false);
      handleClose();
    };
    const setactions = val => {
      if (val === 4) {
        history.push({
          pathname: '/GraphDetailsView',
          search: '?chartId=' + chartId,
          SelectedLocation: window.location.pathname,
          handleHighlight: selectedChartId,
          selectedGrid: selected
        });
      }
    };
    return isLoading ? (
      <LoaderSkeleton />
    ) : (
      <Card
        bordered={false}
        style={{ height: '100%', borderRadius: 0, border: '1px solid #003d6b' }}
      >
        <CardHeader
          title={title ? title : getChartName(chartId)}
          action={
            showTopBar === true ? (
              <MoreActions
                removeFavourite={removeFavourite}
                setActions={setactions}
                chartId={chartId}
                type={type}
                chartPopup={chartPopup}
                handleClose={handleCloses}
                favoritesDisabled={true}
              />
            ) : null
          }
          subheader={getSubHeader(chartId)}
          style={{ borderBottom: '1px solid #003d6b' }}
          //id={'card-header-' + chartId}
        ></CardHeader>

        <Divider />
        {/* <CardContent style={{ cursor: 'pointer', height: '85%' }}> */}
        <CardContent
          style={
            window.location.pathname == '/MyFavorites'
              ? localStorage.getItem('popup') == 'true'
                ? { cursor: 'pointer', height: '320px' }
                : { cursor: 'pointer', height: '330px' }
              : { cursor: 'pointer' }
          }
        >
          <div
            style={{
              backgroundColor: '#FFF',
              // borderRadius: 0,
              // border: '1px solid #003d6b',
              position: 'relative',
              margin: 'auto',
              // height: '50vh',
              height:
                type == 'popup' &&
                isFrom != 'details' &&
                chartId == 921 &&
                window.location.pathname != '/MyFavorites'
                  ? // ? window.innerHeight - 110 + 'px'
                    window.innerHeight - 490 + 'px'
                  : isFrom == 'details'
                  ? '483px'
                  : (chartId == 931 &&
                      window.location.pathname != '/MyFavorites') ||
                    (chartId == 926 &&
                      window.location.pathname != '/MyFavorites') ||
                    (chartId == 921 &&
                      isFrom != 'details' &&
                      window.location.pathname != '/MyFavorites')
                  ? window.innerHeight - 490 + 'px'
                  : '300px',

              width:
                chartId == 931 || chartId == 926 || chartId == 921
                  ? isFrom != 'details' && type != 'popup'
                    ? '100%'
                    : isFrom == 'details'
                    ? '100%'
                    : '100%'
                  : '100%'
            }}
          >
            <Bar
              data={data}
              options={options}
              getElementAtEvent={dataset => {
                if (dataset.length > 0) {
                  var dataIndex = dataset[0]._datasetIndex;
                  var valueIndex = dataset[0]._index;
                  var xAxis = data.datasets[dataIndex].data[valueIndex];
                  var yAxis = data.labels[valueIndex];
                  var label = data.datasets[dataIndex].label.trim();
                  var yAxis = data.labels[valueIndex];
                  localStorage.setItem(
                    'prevLocation',
                    window.location.pathname
                  );
                  if (xAxis > 0) {
                    history.push({
                      pathname:
                        chartId == 926 ||
                        chartId == 927 ||
                        chartId == 928 ||
                        chartId == 929
                          ? '/PartsWorkMixAnalysis'
                          : '/LaborWorkMixAnalysis',
                      prevPath:
                        chartId == 921 &&
                        window.location.pathname == '/GraphDetailsView'
                          ? '/CPELROpportunity'
                          : window.location.pathname,
                      isFrom: 'opportunity',
                      state: {
                        x: xAxis,
                        y: yAxis,
                        isPartsCharts: false,
                        tabSelection: 'one'
                      }
                    });
                  }
                }
              }}
            />
          </div>
        </CardContent>
        <ChartDialog
          open={open}
          chartId={chartId}
          realm={realm}
          chartType="opportunityCharts"
          handlePopupClose={handleCloses}
          //  setActions={setActions}
          title={title}
        />
      </Card>
    );
  } else {
    return null;
  }
};
const getChartColorScheme = chartId => {
  let coloreScheme;
  switch (chartId) {
    case 932:
    case 929:
      coloreScheme = getColorScheme(8)[0];
      break;
    case 933:
    case 927:
      coloreScheme = getColorScheme(8)[2];
      break;
    case 934:
    case 928:
      coloreScheme = getColorScheme(8)[1];
      break;
    case 924:
      coloreScheme = getColorScheme(8)[3];
      break;
  }
  return coloreScheme;
};
export default AdvisorOpportunityCharts;
