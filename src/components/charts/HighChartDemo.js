import React, { useState } from 'react';
import { render } from 'react-dom';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { QueryRenderer } from '@cubejs-client/react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Divider,
  Icon<PERSON>utton
} from '@material-ui/core';
import LoaderSkeleton from '../LoaderSkeleton.js';

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;
const options = {
  title: {
    text: 'My chart'
  },
  series: [
    {
      data: [
        1.69,
        1.68,
        1.68,
        1.68,
        1.68,
        1.67,
        1.67,
        1.67,
        1.67,
        1.68,
        1.67,
        1.67,
        1.67,
        1.67,
        1.67,
        1.67,
        1.69,
        1.7,
        1.7,
        1.71,
        1.69,
        1.67,
        1.67,
        1.69,
        1.69,
        1.71,
        1.71,
        1.7,
        1.7,
        1.71,
        1.63,
        1.63,
        1.63,
        1.63,
        1.64,
        1.65,
        1.68,
        1.69,
        1.69,
        1.69,
        1.57,
        1.59,
        1.59,
        1.6,
        1.6,
        1.6,
        1.6,
        1.62,
        1.63,
        1.64,
        1.63,
        1.55,
        1.55,
        1.56,
        1.57,
        1.44,
        1.46,
        1.46,
        1.46,
        1.46,
        1.47,
        1.47,
        1.49,
        1.49,
        1.5,
        1.49,
        1.52,
        1.54,
        1.51,
        1.5,
        1.51,
        1.51,
        1.5,
        1.5,
        1.5,
        1.49,
        1.49,
        1.48,
        1.46,
        1.46,
        1.45,
        1.44,
        1.51,
        1.51,
        1.51,
        1.51,
        1.51,
        1.5,
        1.51,
        1.51,
        1.52,
        1.52,
        1.5,
        1.51,
        1.49,
        1.49,
        1.48,
        1.49,
        1.49,
        1.5
      ],
      zones: [
        {
          value: 1.67,
          color: '#f7a35c'
        },
        {
          value: 1.7,
          color: '#7cb5ec'
        },
        { value: 1.71, color: '#90ed7d' }
      ]
    }
  ]
};

const HighChartDemo = () => (
  // const [queryState, queryChangedState] = useState({
  //   measures: ['ElrMovingAverage.moving_elr'],
  //   timeDimensions: [],
  //   dimensions: [
  //     'ElrMovingAverage.currentGroup',
  //     'ElrMovingAverage.average',
  //     'ElrMovingAverage.Hours'
  //   ],
  //   filters: [],
  //   order: {
  //     'ElrMovingAverage.currentGroup': 'asc'
  //   }
  // });
  // const lineRender = ({ resultSet }) => {};
  <div>
    <HighchartsReact highcharts={Highcharts} options={options} />
  </div>
);

render(<HighChartDemo />, document.getElementById('root'));

export default HighChartDemo;
