import { QueryRenderer } from '@cubejs-client/react';
import { <PERSON>, CardContent, CardHeader, Divider } from '@material-ui/core';
import moment from 'moment';
import React from 'react';
import {
  Bar,
  Bar<PERSON>hart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton.js';
import MoreActions from '../MoreActions';

const dateFormatter = item => moment(item).format('MMM YY');
const xAxisFormatter = item => {
  if (moment(item).isValid()) {
    return dateFormatter(item);
  } else {
    return item;
  }
};

const CartesianChart = ({ resultSet, children, ChartComponent }) => (
  <ResponsiveContainer width="100%" height={300}>
    <ChartComponent data={resultSet.chartPivot()}>
      <Tooltip labelFormatter={dateFormatter} />
      <XAxis dataKey="x" tickFormatter={xAxisFormatter} />
      <YAxis />
      <CartesianGrid />
      {children}
      <Legend />
    </ChartComponent>
  </ResponsiveContainer>
);

const colors = ['#1890ff', '#141446', '#7A77FF'];

const barRender = ({ resultSet }) => (
  <Card bordered={false} style={{ height: '100%' }}>
    <CardHeader
      title="CP Labor Revenue"
      action={<MoreActions></MoreActions>}
    ></CardHeader>

    <Divider />
    <CardContent>
      <CartesianChart resultSet={resultSet} ChartComponent={BarChart}>
        {resultSet.seriesNames().map((series, i) => (
          <Bar
            key={series.key}
            dataKey={series.key}
            name={series.title}
            fill={colors[i]}
            animationDuration={1200}
          />
        ))}
      </CartesianChart>
    </CardContent>
  </Card>
);

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;

const CPLabourRevenue = () => (
  <QueryRenderer
    query={{
      measures: ['PartsRevenueCustomerPay.Parts_Revenue'],
      timeDimensions: [
        {
          dimension: 'PartsRevenueCustomerPay.monthWise',
          dateRange: 'Last year'
        }
      ],
      dimensions: ['PartsRevenueCustomerPay.monthWise'],
      filters: [],
      limit: 10000,
      offset: 0,
      order: {
        'PartsRevenueCustomerPay.monthWise': 'asc'
      }
    }}
    cubejsApi={cubejsApi()}
    render={renderChart(barRender)}
  />
);

export default CPLabourRevenue;
