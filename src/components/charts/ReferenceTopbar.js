import {
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider,
  <PERSON>rid,
  <PERSON>ton,
  Tooltip,
  Typo<PERSON>,
  Link
} from '@material-ui/core';
import moment from 'moment';
import React, { useState } from 'react';
import clsx from 'clsx';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { traceSpan } from 'src/utils/OTTTracing';
import { withStyles } from '@material-ui/styles';
var Dealer = process.env.REACT_APP_DEALER;
const ReferenceTopbar = ({
  props,
  title,
  handleBackBtn,
  exportReport,
  setResetDashboard,
  pageType,
  classes
}) => {
  // const { classes } = props;
  const exportReportGrid = () => {
    exportReport();

    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title: props.title ? props.title : '',
      from: props.isFrom ? props.isFrom : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to Excel', spanAttribute);
  };
  const resetDashboard = () => {
    setResetDashboard(true);
  };
  const handleBackButton = () => {
    handleBackBtn();
  };
  const result = (
    <Grid
      container
      className={clsx(props.titleContainer, 'reset-dashboard')}
      style={{
        textTransform: 'none',
        backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
        border: Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B'
      }}
    >
      <Grid item xs={2} style={{ display: 'flex', alignItems: 'center' }}>
        {pageType == 'laborItemization' ? (
          <Button
            variant="contained"
            className={'bck-btn'}
            onClick={handleBackButton}
          >
            <Typography variant="body1" align="left">
              Back
            </Typography>
          </Button>
        ) : (
          ''
        )}
      </Grid>
      <Grid item xs={7} style={{ display: 'flex', alignItems: 'right' }}>
        <Typography
          variant="h6"
          // color="primary"
          className={
            pageType == 'laborItemization'
              ? clsx(classes.mainTitleJobCnt, 'main-title')
              : clsx(classes.mainTitle, 'main-title')
          }
        >
          {title}
        </Typography>
      </Grid>
      <Grid
        item
        xs={3}
        style={{
          display: 'flex',
          justifyContent: 'end',
          // paddingLeft: '10%',
          marginTop: 7,
          marginLeft: -9
        }}
      >
        <Tooltip title="Export To Excel">
          <Link
            id="export-to-excel"
            style={{ paddingRight: 8, cursor: 'pointer', marginTop: 3 }}
            onClick={exportReportGrid}
          >
            <ExportIcon />
          </Link>
        </Tooltip>
        <Button
          variant="contained"
          id="reset-layout"
          className={clsx(classes.back, 'reset-btn')}
          onClick={resetDashboard}
        >
          <RestoreIcon />
          <Typography variant="body1" align="left">
            Reset Layout
          </Typography>
        </Button>
      </Grid>
    </Grid>
  );

  return result;
};
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  mainTitle: {
    marginLeft: '51% !important',
    marginTop: '10px !important',
    color: '#084588'
  },
  mainTitleJobCnt: {
    marginLeft: '47% !important',
    marginTop: '10px !important',
    color: '#084588'
  },
  back: {
    marginTop: 2
  }
});
export default withStyles(styles)(ReferenceTopbar);
