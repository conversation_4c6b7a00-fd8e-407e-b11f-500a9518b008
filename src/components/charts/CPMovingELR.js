import { Query<PERSON>enderer } from '@cubejs-client/react';

import {
  Card,
  Paper,
  Grid,
  CardContent,
  CardHeader,
  Divider,
  CircularProgress,
  IconButton,
  Typography
} from '@material-ui/core';
import React, { useState } from 'react';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import { Line } from 'react-chartjs-2';
import moment from 'moment';
import {
  getChartConfiguration,
  getDataforLineChart,
  getChartTitle
} from '../../utils/Utils';
import PropTypes from 'prop-types';
import 'chartjs-plugin-annotation';
import { getLast13Months } from 'src/utils/Utils';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton';
import MoreActions from '../MoreActions';
import { useHistory, matchPath } from 'react-router';
import { withKeycloak } from '@react-keycloak/web';
import {
  getStoreIdFilter,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import ChartDialog from 'src/components/Dialog';

const dateFormatter = item => moment(item).format('MMM YY');
const renderChart = (Component, type) => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) ||
  (type == 'popup' ? (
    <Paper square style={{ margin: 8 }}>
      <Grid
        justify="center"
        style={{
          height: 250,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress size={60} />
      </Grid>
    </Paper>
  ) : (
    <Paper square style={{ margin: 8 }}>
      <Grid
        justify="center"
        style={{
          height: 250,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress size={60} />
      </Grid>
    </Paper>
  ));

const CPMovingELR = props => {
  const history = useHistory();
  let chartTitle = getChartTitle(1089);
  var queryTenant;
  if (
    props.keycloak.realm == 'lupient' ||
    props.keycloak.realm == 'fisherhonda' ||
    props.keycloak.realm == 'demo_store' ||
    props.keycloak.realm == 'haleyag'
  ) {
    queryTenant = {
      measures: [
        'ElrMovingAverage.moving_elr',
        'ElrMovingAverage.elr_after_discount'
      ],
      timeDimensions: [],
      dimensions: [
        'ElrMovingAverage.currentGroup',
        'ElrMovingAverage.average',
        'ElrMovingAverage.Hours',
        'ElrMovingAverage.startopendate',
        'ElrMovingAverage.endopendate',
        'ElrMovingAverage.moonthgroupno',
        'ElrMovingAverage.month_year',
        'ElrMovingAverage.warranty_rate',
        'ElrMovingAverage.elrafterdiscount',
        'ElrMovingAverage.elr'
      ],
      filters: getStoreIdFilter(
        'ElrMovingAverage',
        JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      ),
      order: {
        'ElrMovingAverage.currentGroup': 'asc'
      }
    };
  } else {
    queryTenant = {
      measures: ['ElrMovingAverage.moving_elr'],
      timeDimensions: [],
      dimensions: [
        'ElrMovingAverage.currentGroup',
        'ElrMovingAverage.average',
        'ElrMovingAverage.Hours',
        'ElrMovingAverage.startopendate',
        'ElrMovingAverage.endopendate',
        'ElrMovingAverage.moonthgroupno',
        'ElrMovingAverage.month_year',
        'ElrMovingAverage.warranty_rate',
        'ElrMovingAverage.elrafterdiscount',
        'ElrMovingAverage.elr'
      ],
      filters: getStoreIdFilter(
        'ElrMovingAverage',
        JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      ),
      order: {
        'ElrMovingAverage.currentGroup': 'asc'
      }
    };
  }
  const [queryState, queryChangedState] = useState(queryTenant);
  const [open, setOpen] = useState(false);
  var counter = 0;
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    props.handleClosePopup(1);
  };
  const lineRender = ({ resultSet }) => {
    var resultArray = resultSet.loadResponse.data;
    var trancheArray = [];
    var monthYearArray = [];
    for (let value of resultArray) {
      trancheArray.push(value['ElrMovingAverage.moonthgroupno']);
      monthYearArray.push(value['ElrMovingAverage.month_year']);
    }
    //remove repeated values
    var startingTrancheNo = trancheArray.filter(
      (x, i, a) => a.indexOf(x) === i
    );
    var distinctMonths = monthYearArray.filter((x, i, a) => a.indexOf(x) === i);
    var monthYear = getMonthyear(distinctMonths);

    var medianValue =
      resultSet.loadResponse.data[0]['ElrMovingAverage.average'];
    var warrantyRate =
      resultSet.loadResponse.data[0]['ElrMovingAverage.warranty_rate'];
    let getYaxisRanges = getMinMaxValues(resultSet, props.keycloak.realm);

    var pointBackgroundColors = getBorderColor(resultSet);
    let yaxisHighValue = Math.round(getYaxisRanges[0] * 10) / 10;
    yaxisHighValue >
    resultSet.loadResponse.data[0]['ElrMovingAverage.warranty_rate']
      ? (yaxisHighValue = yaxisHighValue)
      : (yaxisHighValue = yaxisHighValue + 15);
    let yaxisLowValue = Math.round(getYaxisRanges[1] * 10) / 10;
    localStorage.setItem(
      'currentWarrantyRate',
      resultSet.loadResponse.data[0]['ElrMovingAverage.warranty_rate']
    );
    const data = {
      labels: resultSet.categories().map(c => c.category),
      datasets: resultSet.series().map((s, index) => ({
        label: s.title,
        data: s.series.map(r => r.value),
        borderColor: s.title == '  ELR after Discount' ? '#999190' : '#9999ff',
        strokeColor: function(context) {
          if (context.dataset.label != '  ELR after Discount') {
            var index = context.dataIndex;

            var str = data.labels[index];
            var str_array = str.split(',');
            var value = context.dataset.data[index];

            if (str_array[2] < 1) {
              return '#e6b800';
            } else if (str_array[2] > 1 && str_array[2] <= 1.5) {
              return '#005ce6';
            } else if (str_array[2] > 2 && str_array[2] <= 2.5) {
              return '#cc00cc';
            } else if (str_array[2] > 1.5 && str_array[2] <= 2) {
              return '#9900cc';
            } else if (str_array[2] > 2.5) {
              return '#cc0000';
            }
          }
        },
        pointColor: function(context) {
          if (context.dataset.label != '  ELR after Discount') {
            var index = context.dataIndex;

            var str = data.labels[index];
            var str_array = str.split(',');
            var value = context.dataset.data[index];
            if (str_array[2] < 1) {
              return '#e6b800';
            } else if (str_array[2] > 1 && str_array[2] <= 1.5) {
              return '#005ce6';
            } else if (str_array[2] > 2 && str_array[2] <= 2.5) {
              return '#cc00cc';
            } else if (str_array[2] > 1.5 && str_array[2] <= 2) {
              return '#9900cc';
            } else if (str_array[2] > 2.5) {
              return '#cc0000';
            }
          }
        },
        backgroundColor:
          s.title == '  ELR after Discount' ? '#999190' : '#9999ff',
        pointBorderColor: function(context) {
          if (context.dataset.label != '  ELR after Discount') {
            var index = context.dataIndex;

            var str = data.labels[index];
            var str_array = str.split(',');
            var value = context.dataset.data[index];
            if (str_array[2] < 1) {
              return '#e6b800';
            } else if (str_array[2] > 1 && str_array[2] <= 1.5) {
              return '#005ce6';
            } else if (str_array[2] > 2 && str_array[2] <= 2.5) {
              return '#cc00cc';
            } else if (str_array[2] > 1.5 && str_array[2] <= 2) {
              return '#9900cc';
            } else if (str_array[2] > 2.5) {
              return '#cc0000';
            }
          }
        },
        pointBackgroundColor: function(context) {
          if (context.dataset.label != '  ELR after Discount') {
            var index = context.dataIndex;
            var str = data.labels[index];
            var str_array = str.split(',');
            var value = context.dataset.data[index];
            if (str_array[2] < 1) {
              return '#e6b800';
            } else if (str_array[2] > 1 && str_array[2] <= 1.5) {
              return '#005ce6';
            } else if (str_array[2] > 2 && str_array[2] <= 2.5) {
              return '#cc00cc';
            } else if (str_array[2] > 1.5 && str_array[2] <= 2) {
              return '#9900cc';
            } else if (str_array[2] > 2.5) {
              return '#cc0000';
            }
          }
        },
        pointHoverBackgroundColor: '#000066',
        // pointStyle: 'line',
        pointHoverBorderColor: '#000066',
        pointBorderWidth: 1,
        pointHoverRadius: 1,
        pointHoverBorderWidth: 1,
        pointRadius: 0.75,
        fill: false,
        borderWidth: 1.5
      }))
    };
    const data2 = {};
    const options = {
      legend: {
        position: 'bottom',
        display: false
      },
      plugins: {
        datalabels: { display: false }
      },
      maintainAspectRatio:
        localStorage.getItem('popup') != 'true' ? false : true,
      // aspectRatio: 0.72,
      tooltips: {
        footerFontStyle: 'bold',
        callbacks: {
          title: function(tooltip) {},
          label: function(tooltipItem) {
            var labelStringResult;
            var labelString = tooltipItem.xLabel;
            var stringArray = labelString.split(',');
            if (
              props.keycloak.realm == 'lupient' ||
              props.keycloak.realm == 'demo_store' ||
              props.keycloak.realm == 'fisherhonda' ||
              props.keycloak.realm == 'haleyag'
            ) {
              if (tooltipItem.datasetIndex == 0) {
                labelStringResult =
                  'ELR before Discount:$' + tooltipItem.yLabel;
              } else if (tooltipItem.datasetIndex == 1) {
                labelStringResult = 'ELR after Discount:$' + tooltipItem.yLabel;
              }
            } else {
              labelStringResult = 'Moving ELR :$' + tooltipItem.yLabel;
            }
            return [labelStringResult];
          },
          beforeFooter: function(tooltipItem, data) {
            var labelDiscount;
            var labelString = tooltipItem[0].xLabel;
            var stringArray = labelString.split(',');
            if (
              props.keycloak.realm == 'lupient' ||
              props.keycloak.realm == 'demo_store' ||
              props.keycloak.realm == 'fisherhonda' ||
              props.keycloak.realm == 'haleyag'
            ) {
              if (tooltipItem[0].datasetIndex == 0) {
                labelDiscount = 'ELR after Discount:$' + stringArray[8].trim();
              } else if (tooltipItem[0].datasetIndex == 1) {
                labelDiscount = 'ELR before Discount:$' + stringArray[9].trim();
              }
            } else labelDiscount = '';
            return [labelDiscount];
          },

          footer: function(tooltipItem, data) {
            var labelString = tooltipItem[0].xLabel;
            var stringArray = labelString.split(',');
            var openDateFrom = stringArray[3].substr(0, 11);
            var openDateTo = stringArray[4].substr(0, 11);

            var labelStringResult = 'Tranche:' + stringArray[0];
            var num = Math.round(stringArray[2] * 100) / 100;
            var soldhrs = 'Hours/RO:' + num;
            var labelStringResult2 = 'Start Open date:' + openDateFrom;
            var endDate = 'End Open date:' + openDateTo;

            return [labelStringResult, soldhrs, labelStringResult2, endDate];
          }
        }
      },
      pan: {
        enabled: true,
        mode: 'x',
        speed: 20,
        threshold: 10
      },
      zoom: {
        enabled: false,
        drag: false,
        mode: 'xy',
        limits: {
          max: 10,
          min: 0.5
        }
      },
      scales: {
        yAxes: [
          {
            ticks: {
              fontColor: 'rgba(0,0,0,0.5)',

              padding: 20,
              beginAtZero: true,
              steps: 10,
              stepSize: 5,
              min:
                props.keycloak.realm == 'ferrarioat_store' ? 70 : yaxisLowValue,
              max:
                props.keycloak.realm == 'ferrarioat_store' &&
                JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
                  '232297966'
                  ? yaxisHighValue + 10
                  : yaxisHighValue,
              callback: function(value, index, values) {
                return '$' + value;
              }
            },
            gridLines: {
              drawTicks: false
            }
          }
        ],
        xAxes: [
          {
            gridLines: {
              zeroLineColor: 'transparent'
            },
            ticks: {
              callback: function(value, index) {
                var nameArr = value.split(',');
                let filtered = [];
                var filteredNumber = parseInt(nameArr[0]);

                if (props.keycloak.realm == 'ferrarioat_store') {
                  return (
                    Math.round(filteredNumber / 10) * 10
                  ).toLocaleString();
                } else {
                  return (
                    Math.round(filteredNumber / 100) * 100
                  ).toLocaleString();
                }
              },
              beginAtZero: true,
              maxTicksLimit:
                props.keycloak.realm == 'ferrarioat_store' &&
                JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
                  '232297966'
                  ? 8
                  : 6
            }
          }
        ]
      },
      legendCallback: function(chart) {
        var text = [];
        text.push('<ul>');
        for (var i = 0; i < chart.data.datasets.length; i++) {
          text.push('<li>');
          text.push(
            '<span style="background-color:' +
              chart.data.datasets[i].borderColor +
              '">' +
              chart.data.datasets[i].label +
              '</span>'
          );
          text.push('</li>');
        }
        text.push('</ul>');
        return text.join('');
      },
      annotation: {
        drawTime: 'afterDatasetsDraw', // (default)
        events: ['click'],

        dblClickSpeed: 350, // ms (default)

        annotations: [
          {
            drawTime: 'afterDraw', // overrides annotation.drawTime if set
            id: 'a-line-1', // optional
            type: 'line',
            mode: 'horizontal',
            scaleID: 'y-axis-0',
            value: medianValue,
            borderColor: '#1340c9',
            borderWidth: 1,
            label: {
              //  backgroundColor: '',
              content: 'Median:$' + medianValue,
              enabled: true,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1,
              yPadding: 1,
              position: 'right',
              xAdjust:
                props.keycloak.realm == 'lupient' ||
                props.keycloak.realm == 'greatlakesan'
                  ? 30
                  : 40
            }
          },
          {
            id: 'hlineh3',
            type: 'line',
            mode: 'horizontal',
            scaleID: 'y-axis-0',
            value: warrantyRate,
            borderColor: '#00cc44',
            borderWidth: 1,
            label: {
              // backgroundColor: 'red',
              content: 'Current Warranty Labor Rate:$' + warrantyRate,
              enabled: true,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1,
              yPadding: 1
            }
          },
          {
            id: 'hline0',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[0],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[0],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              xAdjust: -15,
              cornerRadius: 1
            }
          },
          {
            id: 'hline1',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[1],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[1],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline2',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[2],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[2],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline3',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[3],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[3],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline4',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[4],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[4],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          },
          {
            id: 'hline5',
            type: 'line',
            mode: 'vertical',
            scaleID: 'x-axis-0',
            value: startingTrancheNo[5],
            borderColor: '#004d99',
            borderWidth: 1,
            label: {
              content: monthYear[5],
              enabled: true,
              position: 'top',
              backgroundColor: '#809fff',
              rotation: 100,
              fontFamily: 'sans-serif',
              fontSize: 10,
              cornerRadius: 1
            }
          }
        ]
      }
      // }
    };
    const result = (
      <Card
        bordered={false}
        style={{
          height: '100%',
          borderRadius: 0,
          border: '1px solid #003d6b',
          paddingBottom: 20
        }}
      >
        <CardHeader
          //title="CP Moving ELR"
          title={chartTitle}
          action={
            <MoreActions
              setActions={setactions}
              chartId={1089}
              type={props.type}
              chartPopup={chartPopup}
              handleClose={handleClose}
              favoritesDisabled={true}
              currentRate={
                resultSet.loadResponse.data[0]['ElrMovingAverage.warranty_rate']
              }
            ></MoreActions>
          }
          subheader={getSubHeader(1089)}
          style={{ borderBottom: '1px solid #003d6b' }}
        ></CardHeader>

        <Divider />
        <CardContent style={{ cursor: 'pointer' }}>
          <div
            style={{
              backgroundColor: '#FFF',
              position: 'relative',
              margin: 'auto',
              // height: '50vh',
              // width: '80vw'
              height:
                window.location.pathname == '/GraphDetailsView'
                  ? 350
                  : window.innerHeight - 280 + 'px',
              width: 'auto'
              //  height: window.innerHeight + 'px',
              // width: '900px'
            }}
          >
            <Line
              id="canvas"
              data={data}
              options={options}
              getElementAtEvent={dataset => {
                if (dataset.length > 0) {
                  var dataIndex = dataset[0]._datasetIndex;
                  var selectedLabel = data.datasets[dataIndex].label;
                  history.push({
                    pathname: '/AnalyzeData',
                    prevPath:
                      window.location.pathname == '/LaborTranches'
                        ? window.location.pathname
                        : window.location.pathname + '?chartId=' + 1089,
                    state: {
                      chartId: 1089,
                      drillDown: 14,
                      chartName: chartTitle,
                      category: 0
                    }
                  });
                }
              }}
            />
            {props.keycloak.realm == 'lupient' ||
            props.keycloak.realm == 'demo_store' ||
            props.keycloak.realm == 'fisherhonda' ||
            props.keycloak.realm == 'haleyag' ? (
              <ul
                style={{
                  marginTop: '-2px',
                  listStyle: 'none',
                  fontFamily: 'Roboto',
                  justifyContent: 'center',
                  display: 'flex',
                  color: '#5a5858',
                  fontSize: 'small',
                  marginRight: '468px'
                }}
              >
                <li
                  style={{
                    float: 'left',
                    marginRight: '10px',
                    lineHeight: '16px',
                    color: 'black'
                  }}
                >
                  <span
                    style={{
                      marginRight: '5px',
                      float: 'left',
                      width: '20px',
                      height: '4px',
                      margin: '5px',
                      backgroundColor: '#9999ff'
                    }}
                  ></span>
                  Moving ELR before Discount
                </li>
                <li
                  style={{
                    float: 'left',
                    marginRight: '10px',
                    lineHeight: '16px',
                    color: 'black'
                  }}
                >
                  <span
                    style={{
                      marginRight: '5px',
                      float: 'left',
                      width: '20px',
                      height: '4px',
                      margin: '5px',
                      backgroundColor: '#999190'
                    }}
                  ></span>
                  Moving ELR after Discount
                </li>
              </ul>
            ) : (
              <ul
                style={{
                  marginTop: '-2px',
                  listStyle: 'none',
                  fontFamily: 'Roboto',
                  justifyContent: 'center',
                  display: 'flex',
                  color: '#5a5858',
                  fontSize: 'small',
                  marginRight: '468px'
                }}
              >
                <li
                  style={{
                    float: 'left',
                    marginRight: '10px',
                    lineHeight: '16px',
                    color: 'black'
                  }}
                >
                  <span
                    style={{
                      marginRight: '5px',
                      float: 'left',
                      width: '20px',
                      height: '4px',
                      margin: '5px',
                      backgroundColor: '#9999ff'
                    }}
                  ></span>
                  Moving ELR
                </li>
              </ul>
            )}
            <ul
              style={{
                marginTop: '-16px',
                listStyle: 'none',
                fontFamily: 'Roboto',
                justifyContent: 'center',
                display: 'flex',
                color: '#5a5858',
                fontSize: 'small',
                marginLeft: '257px'
              }}
            >
              <li
                style={{
                  float: 'left',
                  marginRight: '10px',
                  lineHeight: '-2px',
                  fontWeight: 'bold'
                }}
              >
                Hours/RO:
              </li>
              <li
                style={{
                  float: 'left',
                  marginRight: '6px',
                  lineHeight: '16px'
                }}
              >
                <span
                  style={{
                    marginRight: '5px',
                    float: 'left',
                    width: '12px',
                    height: '12px',
                    margin: '2px',
                    backgroundColor: '#e6b800'
                  }}
                ></span>
                0-1
              </li>
              <li
                style={{
                  float: 'left',
                  marginRight: '10px',
                  lineHeight: '16px'
                }}
              >
                <span
                  style={{
                    marginRight: '5px',
                    float: 'left',
                    width: '12px',
                    height: '12px',
                    margin: '2px',
                    backgroundColor: '#005ce6'
                  }}
                ></span>
                1-1.5
              </li>
              <li
                style={{
                  float: 'left',
                  marginRight: '10px',
                  lineHeight: '16px'
                }}
              >
                <span
                  style={{
                    marginRight: '5px',
                    float: 'left',
                    width: '12px',
                    height: '12px',
                    margin: '2px',
                    backgroundColor: '#9900cc'
                  }}
                ></span>
                1.5-2
              </li>
              <li
                style={{
                  float: 'left',
                  marginRight: '10px',
                  lineHeight: '16px'
                }}
              >
                <span
                  style={{
                    marginRight: '5px',
                    float: 'left',
                    width: '12px',
                    height: '12px',
                    margin: '2px',
                    backgroundColor: '#cc00cc'
                  }}
                ></span>
                2-2.5
              </li>
              <li
                style={{
                  float: 'left',
                  marginRight: '10px',
                  lineHeight: '16px'
                }}
              >
                <span
                  style={{
                    marginRight: '5px',
                    float: 'left',
                    width: '12px',
                    height: '12px',
                    margin: '2px',
                    backgroundColor: '#cc0000'
                  }}
                ></span>
                {'>2.5'}
              </li>
            </ul>

            <Typography
              variant="subtitle1"
              style={{
                fontSize: '12px',
                position: 'absolute',
                right: '74px',
                marginTop: '-21px',
                fontWeight: 'bold'
              }}
            >
              Tranche Size: 100 ROs
            </Typography>
          </div>
        </CardContent>
      </Card>
    );

    return result;
  };
  const getMinMaxValues = (resultSet, tenant) => {
    let arrayResult = resultSet.loadResponse.data;
    let arraydata = [];
    let resultArray = [];
    var increasedmaxValue, decreasedminValue;
    var updatedMaxValue, updatedMinValue;
    for (let value of arrayResult) {
      arraydata.push(value['ElrMovingAverage.moving_elr']);
    }
    var maxValue = Math.round(Math.max.apply(null, arraydata));
    var minValue = Math.round(Math.min.apply(null, arraydata));

    increasedmaxValue = (maxValue * 10) / 100;
    decreasedminValue = (minValue * 10) / 100;
    updatedMaxValue = Math.round((maxValue + increasedmaxValue) / 10) * 10;
    updatedMinValue = Math.round((minValue - decreasedminValue) / 10) * 10;

    resultArray.push(updatedMaxValue, updatedMinValue);
    return resultArray;
  };
  const getBorderColor = resultSet => {
    var array = resultSet.loadResponse.data;

    for (let index = 0; index < array.length; index++) {
      var element = array[index]['ElrMovingAverage.Hours'];
      if (element > 1 && element <= 1.5) {
        return '#aa3531';
      } else if (element > 2 && element <= 2.5) {
        return '#005ce6';
      } else if (element > 1.5 && element <= 2) {
        return '#cc00cc';
      } else if (element > 2.5) {
        return '#009900';
      }
    }
  };

  const getMonthyear = distinctMonths => {
    var monthValues = distinctMonths;
    var yearMonth = [];
    var months = [
      'none',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    for (let index = 0; index < monthValues.length; index++) {
      var mon = monthValues[index];
      var res = mon.split('-');
      var month1 = res[1];
      month1 = month1.replace(/^0+/, '');
      yearMonth.push(months[month1] + "'" + res[0].slice(2));
    }
    return yearMonth;
  };
  const setactions = (val, currentRate) => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=1089',
        SelectedLocation: window.location.pathname
      });
    } else if (val === 6) {
      history.push({
        pathname: '/TrancheReport',
        SelectedLocation: window.location.pathname,
        state: {
          chartId: 1089,
          y: '2020-12',
          currentRate: currentRate
        }
      });
    }
  };

  return (
    <>
      <QueryRenderer
        query={queryState}
        cubejsApi={cubejsApi()}
        render={renderChart(lineRender, props.type)}
      />
      <ChartDialog
        open={open}
        chartId={1089}
        chartType="movingELR"
        realm={props.keycloak.realm}
        handlePopupClose={handleClose}
      />
    </>
  );
};

CPMovingELR.propTypes = {
  className: PropTypes.string,
  filterCharts: PropTypes.func
};
export default withKeycloak(CPMovingELR);
