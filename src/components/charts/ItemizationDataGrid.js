import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import { Box, LinearProgress, Typography, Tooltip } from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import $ from 'jquery';
import clsx from 'clsx';
import { withStyles } from '@material-ui/styles';
import {
  getDrillDownDataForLaborItemization,
  getDrillDownDataForPartsItemization
} from 'src/utils/hasuraServices';
// import 'reactjs-popup/dist/index.css';
import AlertDialog from './AlertDialog';
import CustomHeaderGroup from 'src/components/CustomHeaderGroup';
import CustomTooltip from 'src/components/CustomTooltip';
import { Alert } from '@material-ui/lab';
import { Snackbar } from '@material-ui/core';
import DeleteOutlineIcon from '@material-ui/icons/DeleteOutline';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
import DeleteIcon from '@material-ui/icons/DeleteOutline';

import 'src/styles.css';

var lodash = require('lodash');

class ItemizationDataGrid extends React.Component {
  // componentDidMount() {
  //     this.getAgGridData();
  // }
  componentDidUpdate(prevProps, prevState) {
    if (
      prevState.rowDataLabor != this.state.rowDataLabor ||
      prevState.rowDataParts != this.state.rowDataParts
    ) {
      this.getAgGridData();
      this.createRowData();
    }
    if (prevState.rowData != this.state.rowData) {
      this.setState({ prevRowData: prevState.rowData });
    }
  }
  constructor(props) {
    super(props);
    let i = 0;

    this.state = {
      open: false,
      selectedRo: '',
      selectedRowData: [],
      elr: this.props.elr,
      soldHours: this.props.soldHours,
      category: this.props.category,
      selectedData: this.props.data,
      allData: [],
      rowDataLabor: [],
      rowDataParts: [],
      openDialogue: false,
      filteredResult: [],
      myArray: [],
      repIndex: 0,
      maintIndex: 0,
      compIndex: 0,
      multiRepIndex: 0,
      multiMaintIndex: 0,
      multiCompIndex: 0,
      tabSelection: this.props.tabSelection,
      columnDefs: [
        // {
        //   field: '',
        //   width: 36,
        //   suppressCellFlash: true,
        //   lockPosition: true,
        //   chartDataType: 'category',
        //   //headerCheckboxSelection: true,
        //   //checkboxSelection: true,
        //   floatingFilterComponent: 'customHeaderGroupComponent',
        //   suppressMenu: true,
        //   cellStyle: function() {
        //     return {
        //       cursor: 'pointer'
        //     };
        //   }
        // },
        {
          field: '',
          width: 45,
          tooltip: params => 'Delete',
          onCellClicked: this.handleRows,
          cellRenderer: 'handleRowDelection',
          headerComponent: 'customHeaderGroupComponent'
          // headerComponentParams: {
          //   template: '<i class="fa fa-star" ></i>'
          // }
        },
        {
          headerName: 'RO #',
          field: 'ronumber',
          width: 50,
          chartDataType: 'category',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          //cellRendererFramework:TooltipRenderer,
          tooltip: params => 'View RO',
          unSortIcon: false,
          sortable: false,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              cursor: 'pointer',
              marginRight: '25px',
              paddingLeft: '0px'
            };
          }
        },
        {
          headerName: 'OPCODE',
          field: 'lbropcode',
          onCellClicked: this.handleOpcode,
          width: 95,
          dataType: 'category',
          unSortIcon: false,
          sortable: false,
          tooltip: params => 'View Opcode',
          //onCellClicked: this.handleOpcodeMouseOver,
          //tooltipField: 'lbropcodedesc',
          cellRenderer: 'opcodeCellRenderer',
          onCellClicked: this.handleOpcodeClick,
          cellStyle: function() {
            return {
              textAlign: 'left',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 45,
          hide: true,
          dataType: 'string',
          unSortIcon: false,
          sortable: false,
          cellRenderer: params => {
            return params.value == 'COMPETITIVE'
              ? '<span><i class="fa fa-circle" id="competitiveCircle"></i></span>'
              : params.value == 'MAINTENANCE'
              ? '<span><i class="fa fa-square" id="maintenanceSquare"></i></span>'
              : '<span><i class="fa fa-square" id="repairSquare"></i></span>';
          }
        }
      ],
      headerHeight: 43,
      rowStyle: { textDecoration: 'line-through' },
      context: { componentParent: this },
      frameworkComponents: {
        customHeaderGroupComponent: CustomHeaderGroup
      },
      components: {
        opcodeCellRenderer: opcodeCellRenderer,
        handleRowDelection: handleRowDelection
      },
      rowSelection: 'multiple',
      suppressRowClickSelection: true,
      suppressHorizontalScroll: true,
      defaultColDef: {
        filter: true,
        // resizable: true,
        suppressMovable: false
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right'
    };
  };

  formatCellValue = params => {
    if (params.value != null && params.value != '') {
      return true;
    }
  };

  onGridReady = params => {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.setState({ rawGridApi: params.api });
    this.getAgGridData();
    this.setState({ gridColumnApi: params.columnApi });
    if (window.colStateItemization) {
      this.gridColumnApi.setColumnState(window.colStateItemization);
    }
    // this.gridApi.sizeColumnsToFit();
    // this.createRowData();
  };
  handleRows = params => {
    window.colStateItemization = this.gridColumnApi.getColumnState();
    let dataArr = [];
    dataArr = this.state.rowData.splice(params.rowIndex, 1);
    this.setState({
      myArray: this.state.myArray.concat(dataArr)
    });

    let dataArr1 = this.state.rowData;
    this.setState({ rowData: dataArr1 });
    this.gridApi.setRowData(dataArr1);
    this.handleOpcodeMouseOut(params, 'delete');
    if (this.state.rowData.length == 0) {
      this.props.allItemDeleted(true);
      this.setState({ myArray: [] });
    } else {
      this.props.allItemDeleted(false);
    }
  };

  handleSearchByRo = params => {
    window.colStateItemization = this.gridColumnApi.getColumnState();
    let dataArrAll = this.getAllSelectedPoints();
    this.setState({
      open: true,
      selectedRo: params.value,
      tabType: 'Itemization',
      data: dataArrAll,
      itemizationTab: this.props.tabSelection,
      isLabor: this.props.isLabor ? true : false,
      tabSelection: this.state.tabSelection
    });
  };

  getAllSelectedPoints = params => {
    let myArrayData = this.state.myArray;
    let selectedPoints = this.props.data;
    if (this.state.myArray.length > 0 && this.props.data.length > 0) {
      if (this.props.isLabor) {
        for (let index = 0; index <= selectedPoints.length; index++) {
          selectedPoints.forEach(function(item) {
            myArrayData.forEach(function(item1) {
              if (item.category == 0 && item1.opcategory == 'REPAIR') {
                if (
                  item.elr == item1.elrRepair &&
                  item.lbrsoldhours == item1.lbrsoldhours
                ) {
                  var index1 = selectedPoints.indexOf(item);
                  return selectedPoints.splice(index1, 1);
                }
              }
              if (item.category == 2 && item1.opcategory == 'MAINTENANCE') {
                if (
                  item.elr == item1.elrMaintenance &&
                  item.lbrsoldhours == item1.lbrsoldhours
                ) {
                  var index1 = selectedPoints.indexOf(item);
                  return selectedPoints.splice(index1, 1);
                }
              }
              if (item1.opcategory == 'COMPETITIVE' && item.category == 1) {
                if (
                  item.elr == item1.elrCompetitive &&
                  item.lbrsoldhours == item1.lbrsoldhours
                ) {
                  var index1 = selectedPoints.indexOf(item);
                  return selectedPoints.splice(index1, 1);
                }
              }
            });
          });
        }
      } else {
        for (let index = 0; index <= selectedPoints.length; index++) {
          selectedPoints.forEach(function(item) {
            myArrayData.forEach(function(item1) {
              if (item.category == 0 && item1.opcategory == 'REPAIR') {
                if (
                  item.markup == item1.markupRepair &&
                  item.prtscost == item1.prtscost
                ) {
                  var index1 = selectedPoints.indexOf(item);
                  return selectedPoints.splice(index1, 1);
                }
              }
              if (item.category == 2 && item1.opcategory == 'MAINTENANCE') {
                if (
                  item.markup == item1.markupMaintenance &&
                  item.prtscost == item1.prtscost
                ) {
                  var index1 = selectedPoints.indexOf(item);
                  return selectedPoints.splice(index1, 1);
                }
              }
              if (item1.opcategory == 'COMPETITIVE' && item.category == 1) {
                if (
                  item.markup == item.markupCompetitive &&
                  item.prtscost == item1.prtscost
                ) {
                  var index1 = selectedPoints.indexOf(item);
                  return selectedPoints.splice(index1, 1);
                }
              }
            });
          });
        }
      }
    }
    return selectedPoints;
  };

  handleOpcodeClick = params => {
    window.colStateItemization = this.gridColumnApi.getColumnState();
    let dataArrAll = this.getAllSelectedPoints();
    this.props.history.push({
      pathname: '/OPcodes',
      state: {
        opcodeSelected: params.value,
        tabType: 'Itemization',
        data: dataArrAll,
        itemizationTab: this.props.tabSelection,
        isLabor: this.props.isLabor ? true : false,
        tabSelection: this.state.tabSelection
      }
    });
  };

  handleOpcodeMouseOut = (params, type) => {
    var filtered = [];
    let pointArrRep = [];
    let pointArrMnt = [];
    let pointArrCmp = [];
    this.props.chart.length > 1
      ? (filtered = this.props.chart.slice(-1).filter(function(x) {
          return x !== undefined;
        }))
      : (filtered = this.props.chart.filter(function(x) {
          return x !== undefined;
        }));
    if (this.props.chart.length > 1 && filtered.length == 0) {
      filtered = this.props.chart.slice(0, -1).filter(function(x) {
        return x !== undefined;
      });
    }
    if (filtered.length > 0) {
      if (filtered[0].lbl) {
        filtered[0].lbl.hide();
      }
      if (params.data.opcategory == 'REPAIR') {
        var i = 0;
        this.props.isLabor
          ? $.each(filtered[0].series[0].data, function(i, point) {
              if (
                (Number(params.data.lbrsoldhours).toFixed(2) <= 20.0 &&
                  Number(point.x).toFixed(2) ==
                    Number(params.data.lbrsoldhours) &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrRepair)) ||
                (Number(params.data.lbrsoldhours).toFixed(2) > 20.0 &&
                  Number(point.x).toFixed(2) == 20.0 &&
                  Number(point.y).toFixed(2) == Number(params.data.elrRepair))
              ) {
                pointArrRep.push(point);
              }
            })
          : $.each(filtered[0].series[0].data, function(i, point) {
              if (
                (Number(params.data.prtscost).toFixed(2) <= 500.0 &&
                  Number(point.x).toFixed(2) == Number(params.data.prtscost) &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupRepair)) ||
                (Number(params.data.prtscost).toFixed(2) > 500.0 &&
                  Number(point.x).toFixed(2) == 500.0 &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupRepair))
              ) {
                pointArrRep.push(point);
              }
            });
      } else if (params.data.opcategory == 'MAINTENANCE') {
        this.props.isLabor
          ? $.each(filtered[0].series[1].data, function(i, point) {
              if (
                (Number(params.data.lbrsoldhours).toFixed(2) <= 20.0 &&
                  Number(point.x).toFixed(2) ==
                    Number(params.data.lbrsoldhours) &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrMaintenance)) ||
                (Number(params.data.lbrsoldhours).toFixed(2) > 20.0 &&
                  Number(point.x).toFixed(2) == 20.0 &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrMaintenance))
              ) {
                pointArrMnt.push(point);
              }
            })
          : $.each(filtered[0].series[1].data, function(i, point) {
              if (
                (Number(params.data.prtscost).toFixed(2) <= 500.0 &&
                  Number(point.x).toFixed(2) == Number(params.data.prtscost) &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupMaintenance)) ||
                (Number(params.data.prtscost).toFixed(2) > 500.0 &&
                  Number(point.x).toFixed(2) == 500.0 &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupMaintenance))
              ) {
                pointArrMnt.push(point);
              }
            });
      } else if (params.data.opcategory == 'COMPETITIVE') {
        this.props.isLabor
          ? $.each(filtered[0].series[2].data, function(i, point) {
              if (
                (Number(params.data.lbrsoldhours).toFixed(2) <= 20.0 &&
                  Number(point.x).toFixed(2) ==
                    Number(params.data.lbrsoldhours) &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrCompetitive)) ||
                (Number(params.data.lbrsoldhours).toFixed(2) > 20.0 &&
                  Number(point.x).toFixed(2) == 20.0 &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrCompetitive))
              ) {
                pointArrCmp.push(point);
              }
            })
          : $.each(filtered[0].series[2].data, function(i, point) {
              if (
                (Number(params.data.prtscost).toFixed(2) <= 500.0 &&
                  Number(point.x).toFixed(2) == Number(params.data.prtscost) &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupCompetitive)) ||
                (Number(params.data.prtscost).toFixed(2) > 500.0 &&
                  Number(point.x).toFixed(2) == 500.0 &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupCompetitive))
              ) {
                pointArrCmp.push(point);
              }
            });
      }
      if (type != 'delete') {
        if (pointArrRep.length > 0) {
          filtered[0].tooltip.hide();
          pointArrRep[pointArrRep.length - 1].setState('select');
          pointArrRep[pointArrRep.length - 1].select(true);
          pointArrRep[pointArrRep.length - 1].update({
            selected: true,
            marker: {
              fillColor: '#D3D3D3',
              lineColor: '#000',
              lineWidth: 2
            }
          });
        }
        if (pointArrMnt.length > 0) {
          filtered[0].tooltip.hide();
          pointArrMnt[pointArrMnt.length - 1].setState('');
          pointArrMnt[pointArrMnt.length - 1].update({
            selected: true,
            marker: {
              fillColor: '#D3D3D3',
              lineColor: '#000',
              lineWidth: 2
            }
          });
        }
        if (pointArrCmp.length > 0) {
          filtered[0].tooltip.hide();
          pointArrCmp[pointArrCmp.length - 1].setState('');
          pointArrCmp[pointArrCmp.length - 1].update({
            selected: true,
            marker: {
              fillColor: '#D3D3D3',
              lineColor: '#000',
              lineWidth: 2
            }
          });
        }
      } else {
        if (pointArrRep.length > 0) {
          if (params.data.repairCount > 1) {
            this.setState({ repIndex: this.state.repIndex + 1 });
            this.setState({ multiRepIndex: this.state.multiRepIndex + 1 });
          }
          filtered[0].tooltip.hide();
          if (this.state.repIndex != pointArrRep.length) {
            if (params.data.repairCount == 1) {
              this.setState({ repIndex: 1 });
              pointArrRep[pointArrRep.length - 1].select(false);
              pointArrRep[pointArrRep.length - 1].update({
                selected: false,
                marker: {
                  fillColor: '#35af557a',
                  lineColor: 'rgba(4, 147, 114, 1)'
                }
              });
            } else {
              if (this.state.multiRepIndex == pointArrRep.length) {
                this.setState({ repIndex: this.state.multiRepIndex });
                pointArrRep[pointArrRep.length - 1].select(false);
                pointArrRep[pointArrRep.length - 1].update({
                  selected: false,
                  marker: {
                    fillColor: '#35af557a',
                    lineColor: '#35af557a'
                  }
                });
              } else {
                filtered[0].tooltip.hide();
                pointArrRep[pointArrRep.length - 1].setState('select');
                pointArrRep[pointArrRep.length - 1].select(true);
                pointArrRep[pointArrRep.length - 1].update({
                  selected: true,
                  marker: {
                    fillColor: '#D3D3D3',
                    lineColor: '#000',
                    lineWidth: 2
                  }
                });
              }
            }
          } else if (
            (this.state.repIndex == 0 &&
              this.state.repIndex + 1 == pointArrRep.length) ||
            this.state.repIndex == pointArrRep.length
          ) {
            pointArrRep.map((item, index) => {
              this.setState({ repIndex: 1 });
              pointArrRep[index].select(false);
              pointArrRep[index].update({
                selected: false,
                marker: {
                  fillColor: '#35af557a',
                  lineColor: 'rgba(4, 147, 114, 1)'
                }
              });
            });
          }
        }
        if (pointArrMnt.length > 0) {
          this.setState({ maintIndex: this.state.maintIndex + 1 });
          filtered[0].tooltip.hide();
          if (this.state.maintIndex != pointArrMnt.length) {
            if (params.data.maintenanceCount == 1) {
              this.setState({ maintIndex: 1 });
              pointArrMnt[pointArrMnt.length - 1].select(false);
              pointArrMnt[pointArrMnt.length - 1].update({
                selected: false,
                marker: {
                  fillColor: '#f17f7fa8',
                  lineColor: 'rgba(220,57,18,1)'
                }
              });
            } else {
              if (this.state.multiMaintIndex == pointArrMnt.length) {
                this.setState({ repIndex: this.state.multiMaintIndex });
                pointArrMnt[pointArrMnt.length - 1].select(false);
                pointArrMnt[pointArrMnt.length - 1].update({
                  selected: false,
                  marker: {
                    fillColor: '#f17f7fa8',
                    lineColor: '#f17f7fa8'
                  }
                });
              } else {
                filtered[0].tooltip.hide();
                pointArrMnt[pointArrMnt.length - 1].setState('select');
                pointArrMnt[pointArrMnt.length - 1].select(true);
                pointArrMnt[pointArrMnt.length - 1].update({
                  selected: true,
                  marker: {
                    fillColor: '#D3D3D3',
                    lineColor: '#000',
                    lineWidth: 2
                  }
                });
              }
            }
          } else if (
            (this.state.maintIndex == 0 &&
              this.state.maintIndex + 1 == pointArrMnt.length) ||
            this.state.maintIndex == pointArrMnt.length
          ) {
            pointArrMnt.map((item, index1) => {
              this.setState({ maintIndex: 1 });
              pointArrMnt[index1].select(false);
              pointArrMnt[index1].update({
                selected: false,
                marker: {
                  fillColor: '#f17f7fa8',
                  lineColor: 'rgba(220,57,18,1)'
                }
              });
            });
          }
        }
        if (pointArrCmp.length > 0) {
          this.setState({ compIndex: this.state.compIndex + 1 });
          filtered[0].tooltip.hide();
          if (this.state.compIndex != pointArrCmp.length) {
            if (params.data.competitiveCount == 1) {
              this.setState({ compIndex: 1 });
              pointArrCmp[pointArrCmp.length - 1].select(false);
              pointArrCmp[pointArrCmp.length - 1].update({
                selected: false,
                marker: {
                  fillColor: '#0389fc61',
                  lineColor: 'rgba(75,192,192,1)'
                }
              });
            } else {
              if (this.state.multiCompIndex == pointArrCmp.length) {
                this.setState({ repIndex: this.state.multiCompIndex });
                pointArrCmp[pointArrCmp.length - 1].select(false);
                pointArrCmp[pointArrCmp.length - 1].update({
                  selected: false,
                  marker: {
                    fillColor: '#0389fc61',
                    lineColor: '#0389fc61'
                  }
                });
              } else {
                filtered[0].tooltip.hide();
                pointArrCmp[pointArrCmp.length - 1].setState('select');
                pointArrCmp[pointArrCmp.length - 1].select(true);
                pointArrCmp[pointArrCmp.length - 1].update({
                  selected: true,
                  marker: {
                    fillColor: '#D3D3D3',
                    lineColor: '#000',
                    lineWidth: 2
                  }
                });
              }
            }
          } else if (
            (this.state.compIndex == 0 &&
              this.state.compIndex + 1 == pointArrCmp.length) ||
            this.state.compIndex == pointArrCmp.length
          ) {
            pointArrCmp.map((item, index2) => {
              this.setState({ compIndex: 1 });
              pointArrCmp[index2].select(false);
              pointArrCmp[index2].update({
                selected: false,
                marker: {
                  fillColor: '#0389fc61',
                  lineColor: 'rgba(75,192,192,1)'
                }
              });
            });
          }
        }
      }
      if (type == 'delete' && this.props.isLabor) {
        let dataArrAll = this.getAllSelectedPoints();
        this.props.selectedPointsData(dataArrAll);
      }
      filtered[0].redraw();
    }
  };

  handleOpcodeMouseOver = params => {
    var filtered = [];
    let pointArrRep = [];
    let pointArrMnt = [];
    let pointArrCmp = [];

    this.props.chart.length > 1
      ? (filtered = this.props.chart.slice(-1).filter(function(x) {
          return x !== undefined;
        }))
      : (filtered = this.props.chart.filter(function(x) {
          return x !== undefined;
        }));
    if (this.props.chart.length > 1 && filtered.length == 0) {
      filtered = this.props.chart.slice(0, -1).filter(function(x) {
        return x !== undefined;
      });
    }
    if (filtered.length > 0) {
      if (!filtered[0].lbl) {
        //filtered[0].lbl = filtered[0].renderer.label('',300,20)
        filtered[0].lbl = filtered[0].renderer
          .label('', 300, 20, 'callout')
          .attr({
            padding: 10,
            r: 5,
            zIndex: 8,
            fill: '#1aadce'
          })
          .css({
            color: '#FFFFFF',
            width: window.innerWidth - 800 + 'px'
          })
          .add();
      }
      filtered[0].lbl.show().attr({
        text: params.data.lbropcodedesc
      });
      if (params.data.opcategory == 'REPAIR') {
        var i = 0;
        this.props.isLabor
          ? $.each(filtered[0].series[0].data, function(i, point) {
              if (
                (Number(params.data.lbrsoldhours).toFixed(2) <= 20.0 &&
                  Number(point.x).toFixed(2) ==
                    Number(params.data.lbrsoldhours) &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrRepair)) ||
                (Number(params.data.lbrsoldhours).toFixed(2) > 20.0 &&
                  Number(point.x).toFixed(2) == 20.0 &&
                  Number(point.y).toFixed(2) == Number(params.data.elrRepair))
              ) {
                pointArrRep.push(point);
              }
            })
          : $.each(filtered[0].series[0].data, function(i, point) {
              if (
                (Number(params.data.prtscost).toFixed(2) <= 500.0 &&
                  Number(point.x).toFixed(2) == Number(params.data.prtscost) &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupRepair)) ||
                (Number(params.data.prtscost).toFixed(2) > 500.0 &&
                  Number(point.x).toFixed(2) == 500.0 &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupRepair))
              ) {
                pointArrRep.push(point);
              }
            });
      } else if (params.data.opcategory == 'MAINTENANCE') {
        this.props.isLabor
          ? $.each(filtered[0].series[1].data, function(i, point) {
              if (
                (Number(params.data.lbrsoldhours).toFixed(2) <= 20.0 &&
                  Number(point.x).toFixed(2) ==
                    Number(params.data.lbrsoldhours) &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrMaintenance)) ||
                (Number(params.data.lbrsoldhours).toFixed(2) > 20.0 &&
                  Number(point.x).toFixed(2) == 20.0 &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrMaintenance))
              ) {
                pointArrMnt.push(point);
              }
            })
          : $.each(filtered[0].series[1].data, function(i, point) {
              if (
                (Number(params.data.prtscost).toFixed(2) <= 500.0 &&
                  Number(point.x).toFixed(2) == Number(params.data.prtscost) &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupMaintenance)) ||
                (Number(params.data.prtscost).toFixed(2) > 500.0 &&
                  Number(point.x).toFixed(2) == 500.0 &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupMaintenance))
              ) {
                pointArrMnt.push(point);
              }
            });
      } else if (params.data.opcategory == 'COMPETITIVE') {
        this.props.isLabor
          ? $.each(filtered[0].series[2].data, function(i, point) {
              if (
                (Number(params.data.lbrsoldhours).toFixed(2) <= 20.0 &&
                  Number(point.x).toFixed(2) ==
                    Number(params.data.lbrsoldhours) &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrCompetitive)) ||
                (Number(params.data.lbrsoldhours).toFixed(2) > 20.0 &&
                  Number(point.x).toFixed(2) == 20.0 &&
                  Number(point.y).toFixed(2) ==
                    Number(params.data.elrCompetitive))
              ) {
                pointArrCmp.push(point);
              }
            })
          : $.each(filtered[0].series[2].data, function(i, point) {
              if (
                (Number(params.data.prtscost).toFixed(2) <= 500.0 &&
                  Number(point.x).toFixed(2) == Number(params.data.prtscost) &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupCompetitive)) ||
                (Number(params.data.prtscost).toFixed(2) > 500.0 &&
                  Number(point.x).toFixed(2) == 500.0 &&
                  Number(point.y).toFixed(4) ==
                    Number(params.data.markupCompetitive))
              ) {
                pointArrCmp.push(point);
              }
            });
      }

      if (pointArrRep.length > 0) {
        filtered[0].tooltip.refresh(pointArrRep[pointArrRep.length - 1]);
        pointArrRep[pointArrRep.length - 1].setState('hover');
        pointArrRep[pointArrRep.length - 1].select(false);
        pointArrRep[pointArrRep.length - 1].update({
          marker: {
            fillColor: '#ffc324',
            lineColor: '#000',
            radius: 7
          }
        });
      }
      if (pointArrMnt.length > 0) {
        filtered[0].tooltip.refresh(pointArrMnt[pointArrMnt.length - 1]);
        pointArrMnt[pointArrMnt.length - 1].setState('hover');
        pointArrMnt[pointArrMnt.length - 1].select(false);
        pointArrMnt[pointArrMnt.length - 1].update({
          marker: {
            fillColor: '#ffc324',
            lineColor: '#000',
            radius: 7
          }
        });
      }
      if (pointArrCmp.length > 0) {
        filtered[0].tooltip.refresh(pointArrCmp[pointArrCmp.length - 1]);
        pointArrCmp[pointArrCmp.length - 1].setState('hover');
        pointArrCmp[pointArrCmp.length - 1].select(false);
        pointArrCmp[pointArrCmp.length - 1].update({
          marker: {
            fillColor: '#ffc324',
            lineColor: '#000',
            radius: 7
          }
        });
      }
      filtered[0].redraw();
    }
  };

  handleRowSelection = () => {
    this.setState({ rowData: [] });
    this.props.allItemDeleted(true);
    this.setState({ myArray: [] });
    var filtered = [];
    this.props.chart.length > 1
      ? (filtered = this.props.chart.slice(-1).filter(function(x) {
          return x !== undefined;
        }))
      : (filtered = this.props.chart.filter(function(x) {
          return x !== undefined;
        }));
    if (this.props.chart.length > 1 && filtered.length == 0) {
      filtered = this.props.chart.slice(0, -1).filter(function(x) {
        return x !== undefined;
      });
    }
    var points = filtered[0].getSelectedPoints();
    if (points.length > 0) {
      points.forEach(point => {
        point.update(
          {
            selected: false,
            marker: {
              fillColor:
                point.colorIndex == 0
                  ? '#35af557a'
                  : point.colorIndex == 1
                  ? '#f17f7fa8'
                  : '#0389fc61',
              lineColor:
                point.colorIndex == 0
                  ? 'rgba(4, 147, 114, 1)'
                  : point.colorIndex == 1
                  ? 'rgba(220,57,18,1)'
                  : 'rgba(75,192,192,1)'
            }
          },
          false
        );
      });
      filtered[0].redraw();
    }
  };

  handleResetRowSelection = () => {
    this.gridApi.updateRowData({
      add: this.state.previousSelectedRows
    });
    this.setState({ previousSelectedRows: [] });
  };

  getAgGridData = () => {
    this.onBtShowLoading();
    let filteredArr = [];
    if (
      this.props.isLabor &&
      this.state.rowDataLabor.length == 0 &&
      this.state.isLoading != false
    ) {
      getDrillDownDataForLaborItemization(
        this.props.tabSelection,
        this.props.timeZone,
        result => {
          this.setState({ isLoading: false });

          if (
            result.data
              .statelessDbdLaborItemizationGetChartsLaborElrSoldhoursItemization
              .statelessDbdLaborItemizationChartsLaborElrSoldhoursItemizationDetails
          ) {
            let data =
              result.data
                .statelessDbdLaborItemizationGetChartsLaborElrSoldhoursItemization
                .statelessDbdLaborItemizationChartsLaborElrSoldhoursItemizationDetails;
            if (this.props.session.serviceAdvisor.includes('All') == false) {
              data.map(obj => {
                if (
                  this.props.session.serviceAdvisor.indexOf(
                    obj.serviceadvisor
                  ) != -1
                ) {
                  filteredArr.push(obj);
                }
              });
              data = filteredArr;
              this.setState({
                rowDataLabor: filteredArr
              });
            } else {
              data = data;
              this.setState({
                rowDataLabor: data
              });
            }
          }
        }
      );
    } else if (
      this.props.isParts &&
      this.state.rowDataParts.length == 0 &&
      this.state.isLoading != false
    ) {
      getDrillDownDataForPartsItemization(
        this.props.tabSelection,
        this.props.timeZone,
        result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessDbdPartsItemizationGetChartsPartsMarkupCostItemization
              .statelessDbdPartsItemizationChartsPartsMarkupCostItemizationDetails
          ) {
            let data =
              result.data
                .statelessDbdPartsItemizationGetChartsPartsMarkupCostItemization
                .statelessDbdPartsItemizationChartsPartsMarkupCostItemizationDetails;

            if (this.props.session.serviceAdvisor.includes('All') == false) {
              data.map(obj => {
                if (
                  this.props.session.serviceAdvisor.indexOf(
                    obj.serviceadvisor
                  ) != -1
                ) {
                  filteredArr.push(obj);
                }
              });
              data = filteredArr;
              this.setState({
                rowDataParts: filteredArr
              });
            } else {
              data = data;
              this.setState({
                rowDataParts: data
              });
            }
          }
        }
      );
    }
  };

  createRowData = () => {
    if (this.props.resetLayout == true) {
      this.state.gridColumnApi.resetColumnState();
      this.setState({ myArray: [] });
    }
    this.props.allItemDeleted(false);
    if (
      this.state.rowDataLabor.length == 0 ||
      this.state.rowDataParts.length == 0
    ) {
      let data;
      if (this.props.zoomData.labels) {
        var orderedData = lodash
          .chain(this.props.zoomData)
          .groupBy('datasets')
          .map(value => {
            return value.map(data => data);
          })
          .value();

        let dataArr;
        let zoomedData = [];
        if (orderedData.length > 0) {
          dataArr = orderedData[0][1];
          dataArr.map(item => {
            if (item.data.length > 0 && item.hidden != true) {
              zoomedData = zoomedData.concat(item.data);
            }
          });
          this.props.isLabor != undefined && this.props.isLabor == true
            ? (data = zoomedData.map(item => {
                return {
                  lbrsoldhours: item.label,
                  elr: item.y,
                  category:
                    item.category == 'ELR Repair'
                      ? 0
                      : item.category == 'ELR Competitive'
                      ? 1
                      : 2
                };
              }))
            : (data = zoomedData.map(item => {
                return {
                  prtscost: item.label,
                  markup: item.y,
                  category:
                    item.category == 'Markup Repair'
                      ? 0
                      : item.category == 'Markup Competitive'
                      ? 1
                      : 2
                };
              }));
        }
      }

      let inputData = this.props.zoomData.labels ? data : this.props.data;
      if (
        this.props.isLabor &&
        inputData.length > 0 &&
        this.state.rowDataLabor.length > 0
      ) {
        let filteredResult = [];
        let selectedData = inputData;
        this.setState({ isLoading: true });

        selectedData = lodash.uniqWith(selectedData, lodash.isEqual);

        this.setState({ isLoading: false });
        let data = this.state.rowDataLabor;
        data.forEach(function(item) {
          selectedData.forEach(function(item1, index) {
            if (item1.category == 0) {
              if (
                item.lbrsoldhours == item1.lbrsoldhours &&
                item.elrRepair == item1.elr &&
                item.opcategory == 'REPAIR'
              ) {
                // filteredResult.push(item);
                filteredResult.push({ item: item, index: index });
              }
            } else if (item1.category == 1) {
              if (
                item.lbrsoldhours == item1.lbrsoldhours &&
                item.elrCompetitive == item1.elr &&
                item.opcategory == 'COMPETITIVE'
              ) {
                // filteredResult.push(item);
                filteredResult.push({ item: item, index: index });
              }
            } else {
              if (
                item.lbrsoldhours == item1.lbrsoldhours &&
                item.elrMaintenance == item1.elr &&
                item.opcategory == 'MAINTENANCE'
              ) {
                //  filteredResult.push(item);
                filteredResult.push({ item: item, index: index });
              }
            }
          });
        });
        filteredResult.sort((a, b) => a.index - b.index);
        filteredResult = filteredResult.map(obj => obj.item);

        setTimeout(() => {
          this.setState({
            rowData: filteredResult
          });
        }, 500);
        if (this.state.myArray.length > 0 && filteredResult.length > 0) {
          let myArrayData = this.state.myArray;
          let selectedPoints = this.props.selectedPoints;
          if (selectedPoints.length > 0) {
            for (let index = 0; index <= myArrayData.length; index++) {
              myArrayData.forEach(function(item) {
                selectedPoints.forEach(function(item1) {
                  if (
                    item.opcategory == 'REPAIR' &&
                    item1.category == 'ELR Repair'
                  ) {
                    if (
                      item.elrRepair == item1.y &&
                      item.lbrsoldhours == item1.x
                    ) {
                      myArrayData.splice(0, 1);
                    }
                  }
                  if (
                    item.opcategory == 'MAINTENANCE' &&
                    item1.category == 'ELR Maintenance'
                  ) {
                    if (
                      item.elrMaintenance == item1.y &&
                      item.lbrsoldhours == item1.x
                    ) {
                      myArrayData.splice(0, 1);
                    }
                  }
                  if (
                    item.opcategory == 'COMPETITIVE' &&
                    item1.category == 'ELR Competitive'
                  ) {
                    if (
                      item.elrCompetitive == item1.y &&
                      item.lbrsoldhours == item1.x
                    ) {
                      myArrayData.splice(0, 1);
                    }
                  }
                });
              });
            }
          }

          let result = lodash.xor(filteredResult, this.state.myArray);

          result = lodash.difference(result, this.state.myArray);
          setTimeout(() => {
            this.setState({
              rowData: result
            });
          }, 500);
        }
      } else if (
        this.props.isParts &&
        inputData.length > 0 &&
        this.state.rowDataParts.length > 0
      ) {
        let filteredResult = [];
        let selectedData = inputData;
        selectedData = lodash.uniqWith(selectedData, lodash.isEqual);
        this.setState({ isLoading: false });
        let data = this.state.rowDataParts;
        data.forEach(function(item) {
          selectedData.forEach(function(item1, index) {
            if (item1.category == 0) {
              if (
                item.prtscost == item1.prtscost &&
                item.markupRepair == item1.markup &&
                item.opcategory == 'REPAIR'
              ) {
                //   filteredResult.push(item);
                filteredResult.push({ item: item, index: index });
              }
            } else if (item1.category == 1) {
              if (
                item.prtscost == item1.prtscost &&
                item.markupCompetitive == item1.markup &&
                item.opcategory == 'COMPETITIVE'
              ) {
                //filteredResult.push(item);
                filteredResult.push({ item: item, index: index });
              }
            } else {
              if (
                item.prtscost == item1.prtscost &&
                item.markupMaintenance == item1.markup &&
                item.opcategory == 'MAINTENANCE'
              ) {
                // filteredResult.push(item);
                filteredResult.push({ item: item, index: index });
              }
            }
          });
        });
        filteredResult.sort((a, b) => a.index - b.index);
        filteredResult = filteredResult.map(obj => obj.item);
        setTimeout(() => {
          this.setState({
            rowData: filteredResult
          });
        }, 500);
        // this.setState({
        //   rowData: filteredResult
        // });
        if (this.state.myArray.length > 0 && filteredResult.length > 0) {
          let myArrayData = this.state.myArray;
          let selectedPoints = this.props.selectedPoints;
          if (selectedPoints.length > 0) {
            for (let index = 0; index <= myArrayData.length; index++) {
              myArrayData.forEach(function(item) {
                selectedPoints.forEach(function(item1) {
                  if (
                    item.opcategory == 'REPAIR' &&
                    item1.category == 'Markup Repair'
                  ) {
                    if (
                      item.markupRepair == item1.y &&
                      item.prtscost == item1.x
                    ) {
                      myArrayData.splice(0, 1);
                    }
                  }
                  if (
                    item.opcategory == 'MAINTENANCE' &&
                    item1.category == 'Markup Maintenance'
                  ) {
                    if (
                      item.markupMaintenance == item1.y &&
                      item.prtscost == item1.x
                    ) {
                      myArrayData.splice(0, 1);
                    }
                  }
                  if (
                    item.opcategory == 'COMPETITIVE' &&
                    item1.category == 'Markup Competitive'
                  ) {
                    if (
                      item.markupCompetitive == item1.y &&
                      item.prtscost == item1.x
                    ) {
                      myArrayData.splice(0, 1);
                    }
                  }
                });
              });
            }
          }
          let result = lodash.xor(filteredResult, this.state.myArray);
          result = lodash.difference(result, this.state.myArray);
          setTimeout(() => {
            this.setState({
              rowData: result
            });
          }, 500);
          // this.setState({
          //   rowData: result
          // });
        }
      } else {
        this.setState({
          rowData: []
        });
      }
      if (this.props.isLabor) {
        if (
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.tabSelection &&
          this.props.history.location.state.tabSelection !=
            this.props.tabSelection
        ) {
          let dataArrAll = [];
          this.props.selectedPointsData(dataArrAll);
        } else {
          let dataArrAll = this.getAllSelectedPoints();
          this.props.selectedPointsData(dataArrAll);
        }
      }
      this.props.handleElementClicked();
    }
  };
  onBtShowLoading = () => {
    let dataArr = [];
    if (
      this.gridApi &&
      this.state.rowData.length > 0 &&
      this.props.legendClicked == false
    ) {
      if (this.props.dataArrSelected.length > 0) {
        dataArr = this.formatDataArray(this.props.dataArrSelected);
        const matchingCount = this.countMatchingElements(
          dataArr,
          this.props.data
        );
        if (matchingCount == 1) {
          this.gridApi.showLoadingOverlay();
        }
      }
    }
  };

  countMatchingElements = (arr1, arr2) => {
    let count = 0;
    for (let i = 0; i < arr2.length; i++) {
      const element = arr2[i];
      if (arr1.some(item => this.isEqual(item, element))) {
        count++;
      }
    }
    return count;
  };

  isEqual = (obj1, obj2) => {
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);
    if (keys1.length !== keys2.length) {
      return false;
    }
    for (const key of keys1) {
      if (obj1[key] !== obj2[key]) {
        return false;
      }
    }
    return true;
  };

  formatDataArray = data => {
    if (this.props.isLabor == 'Labor') {
      let dataArr;
      if (data.length > 0) {
        dataArr = data.map(item => {
          return {
            elr: item.y,
            lbrsoldhours: item.label,
            category:
              item.category == 'ELR Repair'
                ? 0
                : item.category == 'ELR Competitive'
                ? 1
                : 2
          };
        });
      }
      return dataArr;
    } else {
      let dataArr;
      if (data.length > 0) {
        dataArr = data.map(item => {
          return {
            markup: item.y,
            prtscost: item.label,
            category:
              item.category == 'Markup Repair'
                ? 0
                : item.category == 'Markup Competitive'
                ? 1
                : 2
          };
        });
      }
      return dataArr;
    }
  };
  setAutoHeight = filteredResult => {
    if (filteredResult.length == 0 || filteredResult.length <= 3) {
      // this.gridApi.setDomLayout("normal");
      if (document.getElementById('ItemizationGrid')) {
        document.getElementById('ItemizationGrid').style.height = '200px';
      }
    } else {
      this.state.rawGridApi.setDomLayout('autoHeight');
      document.querySelector('#ItemizationGrid').style.height = '';
    }
  };

  onSelectionChanged = event => {
    this.setState({ selectedRowData: event.api.getSelectedRows() });
    // this.setState({selectedRowData: event.api.getSelectedNodes()})
  };
  onRowSelected = e => {
    this.setState({ rowChecked: e.node.selected });
  };
  handleClose = () => {
    localStorage.setItem('popup', false);
    this.setState({ open: false });
    this.props.handleClosePopup(1);
  };

  handleClose1 = () => {
    this.setState({ ...this.state, openDialogue: false });
  };

  render() {
    const { classes } = this.props;
    let vertical = 'top';
    let horizontal = 'center';
    let top =
      this.props.type != 'popup'
        ? window.innerHeight - 215
        : window.innerHeight - 155;
    if (this.props.elementClicked == true) {
      this.onBtShowLoading();
      this.createRowData();
    }

    // if ((this.props.elementClicked == true && this.props.legendClicked == false) && ((this.props.dataArrSelected.length > 0 && this.props.data.length > 0) ||
    // (this.props.data.length > 0 && this.props.zoomData.labels))) {
    //   this.setState({openDialogue: true});
    // }
    $('.ag-header-cell-text').attr('id', 'itemizationHeader');
    $('.ag-cell.ag-cell-not-inline-editing.ag-cell-auto-height').attr(
      'id',
      'itemizationRows'
    );
    $('.ag-theme-balham .ag-checkbox-input-wrapper').attr(
      'id',
      'itemizationSelectHeader'
    );
    return (
      //<div style={{ width: this.props.type != 'popup' ? '100%' : '', height: '100%',marginTop: '-'+top+'px',marginLeft: this.props.type == 'popup' ? '87.5%' :'100.5%',}}>

      <div className={'container'} style={{ boxSizing: 'border-box' }}>
        {this.props.zoomData.labels == undefined &&
          this.props.dataArrSelected &&
          this.props.dataArrSelected.length > 0 && (
            <Snackbar
              open={this.state.openDialogue}
              onClose={this.handleClose1}
              autoHideDuration={2500}
              anchorOrigin={{ vertical, horizontal }}
              key={vertical + horizontal}
            >
              <Alert severity="success" sx={{ width: '100%' }}>
                {this.props.dataArrSelected[0].count == 1
                  ? this.props.dataArrSelected[0].count + ' Job Added'
                  : this.props.dataArrSelected[0].count + ' Jobs Added'}
              </Alert>
            </Snackbar>
          )}
        {this.props.zoomData.labels &&
          this.state.rowData &&
          this.state.rowData.length > 0 && (
            <Snackbar
              open={this.state.openDialogue}
              onClose={this.handleClose1}
              autoHideDuration={2500}
              anchorOrigin={{ vertical, horizontal }}
              key={vertical + horizontal}
            >
              <Alert severity="success" sx={{ width: '100%' }}>
                {this.state.rowData.length == 1
                  ? this.state.rowData.length + ' Job Added'
                  : this.state.rowData.length + ' Jobs Added'}
              </Alert>
            </Snackbar>
          )}
        <div
          id="ItemizationGrid"
          className={
            this.props.type == 'popup'
              ? clsx(classes.chartWrapper, 'ag-theme-balham', 'child-div-popup')
              : // : clsx(classes.cardContainerDetail, 'child-div')
                clsx(classes.chartWrapper, 'ag-theme-balham', 'child-div')
          }
        >
          <AgGridReact
            classes={{
              rootWrapper: classes.wrapper
            }}
            columnDefs={this.state.columnDefs}
            rowData={this.state.rowData}
            modules={AllModules}
            onGridReady={this.onGridReady}
            getRowHeight={this.getRowHeight}
            frameworkComponents={this.state.frameworkComponents}
            context={this.state.context}
            headerHeight={this.state.headerHeight}
            rowSelection={this.state.rowSelection}
            onSelectionChanged={this.onSelectionChanged}
            suppressRowClickSelection={this.state.suppressRowClickSelection}
            suppressHorizontalScroll={this.state.suppressHorizontalScroll}
            //floatingFilter={true}
            defaultColDef={this.state.defaultColDef}
            rowStyle={this.state.rowStyle}
            onRowSelected={this.onRowSelected.bind(this)}
            getRowStyle={this.getRowStyle}
            rowClassRules={this.state.rowClassRules}
            icons={this.state.icons}
            components={this.state.components}
            tooltipShowDelay={0}
            onCellMouseOver={this.handleOpcodeMouseOver}
            onCellMouseOut={this.handleOpcodeMouseOut}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
        <AlertDialog
          open={this.state.open}
          selectedRo={this.state.selectedRo}
          handlePopupClose={this.handleClose}
          isFrom={'itemization'}
          type={this.props.isLabor ? 'Labor' : 'Parts'}
          data={this.getAllSelectedPoints()}
          tab={this.props.tabSelection}
        />
      </div>
    );
  }
}
const opcodeCellRenderer = params => {
  if (params.value != null && params.value != '') {
    // this.state.rowData.filter(el => {

    if (params.data.lbropcode == params.value) {
      return params.data.opcategory == 'COMPETITIVE'
        ? '<span ><i class="fa fa-circle" id="competitiveCircle"></i></span> <label style="cursor: pointer">' +
            params.value +
            '</label>'
        : params.data.opcategory == 'MAINTENANCE'
        ? '<span><i class="fa fa-square" id="maintenanceSquare"></i></span> <label style="cursor: pointer">' +
          params.value +
          '</label>'
        : '<span ><i class="fa fa-square" id="repairSquare"></i></span>  <label style="cursor: pointer">' +
          params.value +
          '</label>';
      // : '<span ><i class="fa fa-square" id="repairSquare"></i></span>  <label title="' +
      //   params.data.lbropcodedesc +
      //   '">' +
      //   params.value +
      //   '</label>';
    }
    //});
  }
};
const handleRowDelection = () => {
  //return <span class="material-icons-outlined"></span>;
  return '<img style="width:20px;height:20px;cursor: pointer; margin-top:2px" id="btnRemove" src="./images/img/remove.png"/>';
};

const styles = theme => ({
  wrapper: {
    border: '1px solid #e1e5ed !important'
  },
  chartWrapper: {
    width: '14.5em'
    // '@media (max-width: 1920px)': {
    //   height: 480
    // },

    // '@media (min-width: 2304px)': {
    //   height: 630
    // },
    // '@media (max-width: 1440px)': {
    //   height: 510
    // },
    // '@media (max-width: 1280px)': {
    //   height: 540
    // }
  }
});
export default withStyles(styles)(ItemizationDataGrid);
