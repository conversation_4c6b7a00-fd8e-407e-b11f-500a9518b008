import {
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider
} from '@material-ui/core';
import moment from 'moment';
import React, { useState } from 'react';
import { Line } from 'react-chartjs-2';
import LoaderSkeleton from '../LoaderSkeleton.js';
import { getDataforComparisonChart } from 'src/utils/Utils';
import { getComparisonChartConfiguration } from 'src/utils/Utils';
import { getChartType } from 'src/components/ViewGraphDetailsAction';

const RenderComparisonCharts = ({ resultSet, chartName, chartId }) => {
  if (resultSet.length == 0) {
    return <LoaderSkeleton />;
  }
  var chartType = getChartType(chartId);
  const data = getDataforComparisonChart(resultSet);
  const options = getComparisonChartConfiguration(
    chartId,
    resultSet,
    chartType
  );

  const result = (
    <Card style={{ height: '100%', width: '100%' }}>
      <CardHeader title={chartName}></CardHeader>

      <Divider />
      <CardContent style={{ cursor: 'pointer', height: '85%' }}>
        <Line data={data} options={options} />
      </CardContent>
    </Card>
  );

  return result;
};

export default RenderComparisonCharts;
