import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import {
  getChartType,
  getLineChartType,
  GetyAxisRange
} from 'src/components/ViewGraphDetailsAction';
import {
  applyPartialMonthFilterLineCharts
} from '../../utils/Utils';
import {
  Paper
} from '@material-ui/core';
import { useHistory } from 'react-router';
import ReactApexChart from 'react-apexcharts';
import moment from 'moment';
import { makeStyles } from '@material-ui/core/styles';
import { applyPartialMonthFilterWorkMix } from 'src/utils/Utils';
import getYearLegend from 'src/utils/Utils';

const useStyles = makeStyles({
  formControl: {
    padding: 4,
    minWidth: 250
  },
  gridContainer: {
    padding: '8px 0px'
  },
  container: {
    cursor: 'pointer',
    borderRadius: 5,
    border: '1px solid #003d6b'
  }
});
const WorkmixTopCharts = ({
  chartId,
  callback,
  partialMonth,
  realm,
  parentCallback,
  userhistory
}) => {
  const history = useHistory();
  const classes = useStyles();
  let data = [];
  if( chartId == 966 ) {
    data = [
      { name: getYearLegend('present', realm), data: callback.map(e => e.partsgrossprofitpercentageCurrentyear) },
      { name: getYearLegend('past', realm), data: callback.map(e => e.partsgrossprofitpercentagePreviousyear) },
      { name: getYearLegend('previous', realm), data: callback.map(e => e.partsgrossprofitpercentagePastyear) }
    ];
  } else {
    data = [
      { name: getYearLegend('present', realm), data: callback.map(e => e.lgpcurrentyear) },
      { name: getYearLegend('past', realm), data: callback.map(e => e.lgppreviousyear) },
      { name: getYearLegend('previous', realm), data: callback.map(e => e.lgpyearbeforeprevious) }
    ];
  }
  if (partialMonth == false) {
    applyPartialMonthFilterWorkMix(data);
  }
  
  const options = {
    chart: {
      id: 'chart' + chartId,
      events: {
        dataPointSelection: function(event, chartContext, config) {
          // markerClick: function(event, chartContext, config) {
          var xAxis =
            config.w.globals.seriesLog[config.seriesIndex][
              config.dataPointIndex
            ];
          var year = config.w.globals.seriesNames[config.seriesIndex];
          var month =
            config.w.globals.seriesX[config.seriesIndex][
              config.dataPointIndex
            ];
          var formattedMonth = ('0' + month).slice(-2);
          var yAxis = year + '-' + formattedMonth;
          var isFrom = (userhistory && userhistory.location && userhistory.location.isFrom) ? userhistory.location.isFrom : '';
          var prevPaths = (userhistory && userhistory.location && userhistory.location.prevPath) ? userhistory.location.prevPath : '';
          history.push({
            pathname: '/AnalyzeData',
            prevPath: window.location.pathname,
            state: {
              chartId: chartId,
              x: xAxis,
              y: yAxis,
              drillDown: 4,
              category: 0,
              chartName: chartId == 966 ? 'CP Parts Gross Profit Percentage' : 'CP Labor Gross Profit Percentage',
              title: 'GP-CPLGP',
              isFrom: isFrom,
              prevPaths: prevPaths
            }
          });
          let data = {
            type: 'workmixdrilldown',
            chartId: 1073,
            x: xAxis,
            y: yAxis,
            drillDown: 4,
            category: 0,
            chartName: chartId == 966 ? 'CP Parts Gross Profit Percentage' : 'CP Labor Gross Profit Percentage',
            title: 'GP-CPLGP',
            isFrom: isFrom
          };
          parentCallback(data);
        }
      },
      fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
      toolbar: {
        show: false,
        offsetX: 5,
        offsetY: 0,
        tools: {
          download: true,
          selection: true,
          zoomin: true,
          zoomout: true,
          pan: true
        },
        autoSelected: 'zoom'
      },
      type: 'line',
      zoom: {
        enabled: false
      }
    },
  //  colors: ['#3366CC', '#DC3912', '#109618'],
    colors: ['#109618','#DC3912','#3366CC'],
    dataLabels: {
      enabled: false
    },
    markers: {
      size: 4,
      strokeWidth: 1,
      strokeOpacity: 0.7
    },
    stroke: {
      curve: 'straight',
      width: 2.5
    },
    title: {
      text: chartId == 966 ? 'CP Parts Gross Profit Percentage' : 'CP Labor Gross Profit Percentage',
      align: 'left'
    },
    grid: {
      row: {
        colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
        opacity: 0.5
      }
    },
    theme: {
      palette: 'palette8' // upto palette10
    },
    yaxis: {
      labels: {
        formatter: function(value) {
          if (value) return Math.round(value * 100) + ' %';
          else return value;
        }
      }
    },
    xaxis: {
      categories: callback
        .map(c => moment(c.closeddate).format('MMM')),
      tooltip: {
        enabled: false
      }
    },
    tooltip: {
      shared: false,
      intersect: true,
      y: {
        formatter: function(y) {
          if (typeof y !== 'undefined') {
            return (y * 100).toFixed(1) + '%';
          }
          return y;
        }
      }
    }
  };
 
  return (
  
    <Paper square className={classes.container}>
      <ReactApexChart
        options={options}
        series={data}
        type="line"
        height={250}
      />
    </Paper>
  );
};

WorkmixTopCharts.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
  yAxisRanges: PropTypes.array,
  filterCharts: PropTypes.func,
};

export default WorkmixTopCharts;
