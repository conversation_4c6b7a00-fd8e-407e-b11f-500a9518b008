import { Query<PERSON>enderer } from '@cubejs-client/react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Divider,
  IconButton
} from '@material-ui/core';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import React from 'react';
import {
  <PERSON><PERSON>ian<PERSON>rid,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON><PERSON>,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton.js';

const CartesianChart = ({ resultSet, children, ChartComponent }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <ChartComponent>
        <Tooltip />

        <XAxis
          dataKey="LaborEachJobElr.soldhours"
          type="number"
          domain={[0, 15]}
        />
        <YAxis
          dataKey="LaborEachJobElr.LbrRate"
          type="number"
          domain={[0, 300]}
        />
        <CartesianGrid />
        {children}
      </ChartComponent>
    </ResponsiveContainer>
  );
};

const colors = ['#9c27b0', '#141446', '#7A77FF'];

const scatterRenderer = ({ resultSet }) => (
  <Card bordered={false} style={{ height: '100%' }}>
    <CardContent bordered={false} style={{ height: '100%' }}>
      <CardHeader
        title="Labor For Each Job ElrScattered"
        action={
          <IconButton aria-label="settings">
            <MoreVertIcon />
          </IconButton>
        }
      ></CardHeader>

      <Divider />

      <CartesianChart resultSet={resultSet} ChartComponent={ScatterChart}>
        {
          <Scatter
            data={resultSet.tablePivot()}
            fill={colors[0]}
            animationDuration={1200}
          />
        }
      </CartesianChart>
    </CardContent>
  </Card>
);

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;

const LaborEachJobElrScattered = () => (
  <QueryRenderer
    query={{
      timeDimensions: [
        {
          dimension: 'LaborEachJobElr.closeddate',
          dateRange: 'Last 120 days'
        }
      ],
      dimensions: ['LaborEachJobElr.LbrRate', 'LaborEachJobElr.soldhours'],
      filters: [],

      offset: 0
    }}
    cubejsApi={cubejsApi()}
    render={renderChart(scatterRenderer)}
  />
);

export default LaborEachJobElrScattered;
