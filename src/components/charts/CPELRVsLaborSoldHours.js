import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import React, { useState, useEffect } from 'react';
import { useHistory } from 'react-router';
import MoreActions from '../MoreActions';
import { getSubHeader } from 'src/components/ViewGraphDetailsAction';
import ChartDialog from 'src/components/Dialog';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import $ from 'jquery';
import ItemizationDataGrid from './ItemizationDataGrid';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'src/styles.css';
import {
  getChartConfigurationItemization,
  getChartTitle
} from '../../utils/Utils';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import HighchartsBoost from 'highcharts/modules/boost';
import { LeakRemoveTwoTone } from '@material-ui/icons';
import { useDispatch, useSelector } from 'react-redux';

HighchartsBoost(Highcharts);

var lodash = require('lodash');
var chart = Highcharts.charts;

const useStyles = makeStyles(() => ({
  cardContainer: {
    height: '100%',
    borderRadius: 0,
    border: '1px solid #003d6b',
    paddingBottom: 20,
    marginRight: 5,
    width: '100%'
  },
  cardContainer1: {
    height: window.innerHeight - 260
  },
  cardContainerDetail: {
    height: '100%',
    borderRadius: 0,
    border: '1px solid #003d6b',
    paddingBottom: 20,
    width: '100%'
  },
  cardContainerDetail1: {
    height: '100%',
    paddingBottom: 10,
    width: '100%'
  },
  cardHeader: {
    borderBottom: '1px solid #003d6b'
  },
  chartContainter: {
    backgroundColor: '#FFF',
    position: 'relative',
    margin: 'auto',
    height:
      window.location.pathname == '/GraphDetailsView'
        ? 340
        : window.innerHeight - 280 + 'px'
  }
}));

/*
 * Display a temporary label on the chart
 */
function toast(chart, text) {
  if (chart) {
    if (chart.toast) {
      chart.toast.destroy();
      chart.toast = undefined;
    }
    chart.toast = chart.renderer
      .label(text, 500, 10)
      .attr({
        fill: Highcharts.getOptions().colors[0],
        padding: 10,
        r: 5,
        zIndex: 8
      })
      .css({
        color: '#FFFFFF'
      })
      .add();
    setTimeout(function() {
      if (chart.toast != undefined) {
        chart.toast.fadeOut();
      }
    }, 1000);
    setTimeout(function() {
      if (chart.toast != undefined) {
        chart.toast = chart.toast.destroy();
      }
    }, 1500);
  }
}

const CPELRVsLaborSoldHours = props => {
  const history = useHistory();
  let chartTitle = getChartTitle(1090);
  const session = useSelector(state => state.session);

  const [isLoading, setIsLoading] = useState(false);

  const [zoomPoints, setZoomPoints] = useState({});
  const [ctx, setCtx] = useState({});

  const [filteredArr, setFilteredArr] = useState(props.data);
  const [dataArr, setAllData] = useState([]);
  const [allData, setAllData1] = useState([]);
  const [allDataCopy, setAllDataCopy] = useState([]);
  const [clickedPoints, setClickedPoints] = useState([]);

  const [metaArr, setMetaArr] = useState([]);
  const [legendArr, setLegendArr] = useState([]);
  const [open, setOpen] = useState(false);
  const [elementClicked, setElementClicked] = useState(false);
  const [layout, setLayout] = useState(props.layout);
  const [resetLayout, setResetLayout] = useState(false);
  const [toggledItems, setToggledItems] = useState([]);
  const [dataArrSelected, setSelectedData] = useState([]);
  // const [LegendClicked, setLegendClicked] = useState(legendClick);
  const [LegendClicked, setLegendClicked] = useState(false);

  const [pointsDraged, isPointsDraged] = useState(false);
  const [selectedPoints, setSelectedPoints] = useState([]);
  const [AllItemDeleted, setAllItemDeleted] = useState(false);

  const chartPopup = () => {
    setOpen(true);
    // setResetDashboard(true);
    //   unselectByClick();
    //resetAllLayouts();
  };
  useEffect(() => {
    //if(history.location && history.location.state == undefined ) {
    setZoomPoints({});
    setFilteredArr([]);
    setResetLayout(true);
    //$('.reset-btn').click();
    // }
  }, [props.isReloaded]);

  const handleClose = () => {
    setOpen(false);
    props.handleClosePopup(1);
    // resetAllLayouts();
    if (
      (props.selectedPoints && props.selectedPoints.length > 0) ||
      selectedPoints.length > 0
    ) {
      let dataArr =
        selectedPoints.length > 0 ? selectedPoints : props.selectedPoints;

      setSelectedPoints(dataArr);
      let data;
      data = dataArr;
      var filtered = [];
      props.chart && props.chart.length > 1
        ? (filtered = chart.slice(-1).filter(function(x) {
            return x !== undefined;
          }))
        : (filtered = chart.filter(function(x) {
            return x !== undefined;
          }));
      if (chart.length > 1 && filtered.length == 0) {
        filtered = chart.slice(0, -1).filter(function(x) {
          return x !== undefined;
        });
      }

      if (data.length > 0 && filtered.length > 0 && filtered.length == 1) {
        if (filtered[0]) {
          options.chart.events.load();
        }
      }
    }
  };

  const markSelectedPoints = (series, data) => {
    if (data.length > 0 && series.length > 0) {
      $.each(data, function(j, dataPoints) {
        if (dataPoints.category == 0 && series[0]) {
          $.each(series[0].data, function(i, point) {
            if (
              (Number(dataPoints.lbrsoldhours).toFixed(2) <= 20.0 &&
                Number(point.x).toFixed(2) == Number(dataPoints.lbrsoldhours) &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr)) ||
              (Number(dataPoints.lbrsoldhours).toFixed(2) > 20.0 &&
                Number(point.x).toFixed(2) == 20.0 &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr))
            ) {
              point.select(true);
              point.update({
                selected: true,
                marker: {
                  fillColor: '#D3D3D3',
                  lineColor: '#000',
                  lineWidth: 2
                }
              });
            }
          });
        } else if (dataPoints.category == 2 && series[1]) {
          $.each(series[1].data, function(i, point) {
            if (
              (Number(dataPoints.lbrsoldhours).toFixed(2) <= 20.0 &&
                Number(point.x).toFixed(2) == Number(dataPoints.lbrsoldhours) &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr)) ||
              (Number(dataPoints.lbrsoldhours).toFixed(2) > 20.0 &&
                Number(point.x).toFixed(2) == 20.0 &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr))
            ) {
              point.select(true);
              point.update({
                selected: true,
                marker: {
                  fillColor: '#D3D3D3',
                  lineColor: '#000',
                  lineWidth: 2
                }
              });
            }
          });
        } else if (dataPoints.category == 1 && series[2]) {
          $.each(series[2].data, function(i, point) {
            if (
              (Number(dataPoints.lbrsoldhours).toFixed(2) <= 20.0 &&
                Number(point.x).toFixed(2) == Number(dataPoints.lbrsoldhours) &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr)) ||
              (Number(dataPoints.lbrsoldhours).toFixed(2) > 20.0 &&
                Number(point.x).toFixed(2) == 20.0 &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr))
            ) {
              point.select(true);
              point.update({
                selected: true,
                marker: {
                  fillColor: '#D3D3D3',
                  lineColor: '#000',
                  lineWidth: 2
                }
              });
            }
          });
        }
      });
    }
    setAllData(data);
    setElementClicked(true);
  };

  const formatPopupSelectedPoints = data => {
    let dataArr;
    if (data.length > 0) {
      dataArr = data.map(item => {
        return {
          lbrsoldhours: item.label ? item.label : item.lbrsoldhours,
          elr: item.y ? item.y : item.elr,
          category:
            item.category == 'ELR Repair' || item.category == 0
              ? 0
              : item.category == 'ELR Competitive' || item.category == 1
              ? 1
              : 2
        };
      });
    }
    return dataArr;
  };
  useEffect(() => {
    // if(history.location && history.location.state == undefined ) {
    setAllData([]);
    setFilteredArr([]);
    setLegendArr([]);

    setIsLoading(true);
    setElementClicked(true);
    //   }
  }, [props.tabSelection, props.resetClicked]);

  const handleElementClicked = () => {
    setElementClicked(false);
  };
  const allItemDeleted = value => {
    setAllItemDeleted(value);
    if (value == true) {
      setAllData([]);
      setAllData1([]);
    }
  };
  props.handleResetClicked();
  const handleAlertDialogClose = () => {
    props.handleClosePopup();
  };
  const setSelectedPointsData = data => {
    if (props.selectedPointsData) {
      props.selectedPointsData(data);
    }
  };
  const setactions = val => {
    localStorage.setItem('itemTab', props.tabSelection);
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=1090',
        SelectedLocation: window.location.pathname,
        timeZone: props.timeZone,
        state: {
          tabSelection: props.tabSelection,
          data: dataArr
        }
      });
    }
  };
  const setResetDashboard = value => {
    if (value) {
      setLayout([]);
      setAllData([]);
      setFilteredArr([]);
      setLegendArr([]);

      setElementClicked(true);
      setZoomPoints({});
      setResetLayout(true);
    }
  };

  $('.resetPopupLayout')
    .unbind()
    .click(function() {
      resetAllLayouts();
    });

  const classes = useStyles();
  const options = getChartConfigurationItemization(
    props.data,
    props.completeData,
    18
  );

  options.xAxis = {
    title: {
      enabled: true,
      text: 'Sold Hours'
    },
    min: 0,
    max: 20,
    tickInterval: 2,
    startOnTick: false,
    endOnTick: false,
    showFirstLabel: true,
    showLastLabel: true,
    gridLineWidth: 1,
    labels: {
      formatter: function() {
        return `<span style="font-size: 12px">${this.value}</span>`;
      }
    }
  };
  options.yAxis = {
    min: 0,
    max: 300,
    tickInterval: 50,
    startOnTick: false,
    endOnTick: false,
    labels: {
      formatter: function() {
        return (
          '$' +
          `<span style="font-size: 12px">${this.value
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}</span>`
        );
      }
    },
    title: {
      text: 'ELR'
    }
  };
  options.chart = {
    type: 'scatter',
    zoomType: props.parent != 'Details' ? 'xy' : false,
    animation: false,
    style: {
      fontFamily: 'Roboto',
      fontWeight: 450
    },
    className: 'chartid-1090',
    events: {
      selection: getSelection,
      load: function() {
        if (
          props.legendArr &&
          props.legendArr.length > 0 &&
          // props.LegendClicked &&
          // props.LegendClicked == true &&
          props.type == 'popup'
        ) {
          var filtered = [];
          props.chart && props.chart.length > 1
            ? (filtered = chart.slice(-1).filter(function(x) {
                return x !== undefined;
              }))
            : (filtered = chart.filter(function(x) {
                return x !== undefined;
              }));
          if (chart.length > 1 && filtered.length == 0) {
            filtered = chart.slice(0, -1).filter(function(x) {
              return x !== undefined;
            });
          }
          if (props.legendArr.includes(0)) {
            filtered[0].series[2].setVisible(false);
          }
          if (props.legendArr.includes(1)) {
            filtered[0].series[1].setVisible(false);
          }
          if (props.legendArr.includes(2)) {
            filtered[0].series[0].setVisible(false);
          }
        }
        var series = this.series;
        if (
          history.location &&
          history.location.state &&
          history.location.state.isFrom &&
          (history.location.state.isFrom == 'opcodes' ||
            history.location.state.isFrom == 'searchRo' ||
            history.location.state.isFrom == 'detail') &&
          history.location &&
          history.location.state &&
          history.location.state.tabSelection &&
          history.location.state.tabSelection == props.tabSelection
        ) {
          setTimeout(function() {
            markSelectedPoints(series, history.location.state.data);
          }, 10);
        } else if (
          history.location &&
          history.location.state &&
          history.location.state.isFrom &&
          (history.location.state.isFrom == 'opcodes' ||
            history.location.state.isFrom == 'searchRo' ||
            history.location.state.isFrom == 'detail') &&
          history.location &&
          history.location.state &&
          history.location.state.tabSelection &&
          history.location.state.tabSelection != props.tabSelection
        ) {
          history.location.state.data = [];
        }

        if (props.selectedPoints && props.selectedPoints.length > 0) {
          let data;
          if (selectedPoints.length > 0) {
            data = formatPopupSelectedPoints(selectedPoints);
          } else {
            data = formatPopupSelectedPoints(props.selectedPoints);
          }
          setSelectedPoints(data);

          var filtered = [];
          props.chart && props.chart.length > 1
            ? (filtered = chart.slice(-1).filter(function(x) {
                return x !== undefined;
              }))
            : (filtered = chart.filter(function(x) {
                return x !== undefined;
              }));
          if (chart.length > 1 && filtered.length == 0) {
            filtered = chart.slice(0, -1).filter(function(x) {
              return x !== undefined;
            });
          }

          if (filtered[0] && data.length > 0) {
            setTimeout(function() {
              markSelectedPoints(filtered[0].series[0].data, data);
            }, 10);
          }
        }
        let arr = [];
        let temp = [];
        if (
          history.location.state &&
          history.location.state.compClicked &&
          history.location.state.compClicked == true
        ) {
          setTimeout(function() {
            Highcharts.fireEvent(
              document.getElementsByClassName('highcharts-legend-item')[0],
              'click'
            );
            arr.push(0);
            temp = [...arr];
            props.legendItemClicked('Competitive');
            props.enabledlegendItems(temp);
          }, 30);
        }
        if (
          history.location.state &&
          history.location.state.maintClicked &&
          history.location.state.maintClicked == true
        ) {
          setTimeout(function() {
            Highcharts.fireEvent(
              document.getElementsByClassName('highcharts-legend-item')[1],
              'click'
            );
            arr.push(1);
            temp = [...arr];
            props.legendItemClicked('Maintenance');
            props.enabledlegendItems(temp);
          }, 30);
        }
        if (
          history.location.state &&
          history.location.state.repairClicked &&
          history.location.state.repairClicked == true
          // (props.LegendClicked &&
          // props.LegendClicked == true &&
          // props.legendArr &&
          // props.legendArr.length > 0 &&
          // props.legendArr.includes(2))
        ) {
          setTimeout(function() {
            Highcharts.fireEvent(
              document.getElementsByClassName('highcharts-legend-item')[2],
              'click'
            );
            arr.push(2);
            temp = [...arr];
            props.legendItemClicked('Repair');
            props.enabledlegendItems(temp);
          }, 30);
        }
      }
    }
  };

  // if (options.plotOptions && props.completeData.datasets.length > 0) {
  //   let alldataArr = [];
  //   alldataArr = alldataArr.concat(props.completeData.datasets[0].data, props.completeData.datasets[1].data, props.completeData.datasets[2].data);
  //     if (alldataArr.length > 100) {
  //     options.boost = {
  //       useGPUTranslations: true,
  //       usePreAllocated: true
  //     };

  //     options.plotOptions.series.boostThreshold =
  //       props.completeData.datasets.length > 0
  //         ? props.completeData.datasets[0].data.length
  //         : 6503;
  //   }
  // }
  let alldataArr = [];
  let greatestCount = 0;
  if (props.completeData.datasets.length > 0) {
    alldataArr = alldataArr.concat(
      props.completeData.datasets[0].data,
      props.completeData.datasets[1].data,
      props.completeData.datasets[2].data
    );
  }
  let arr1Lth = [];
  if (props.data.length > 0) {
    if (props.data[0]) {
      arr1Lth.push(props.data[0].length);
    }
    if (props.data[1]) {
      arr1Lth.push(props.data[1].length);
    }
    if (props.data[2]) {
      arr1Lth.push(props.data[2].length);
    }
    greatestCount = lodash.max(arr1Lth);
  }
  console.log('greatestCount', greatestCount);
  options.plotOptions = {
    scatter: {
      marker: {
        radius: 5,
        states: {
          hover: {
            enabled: true,
            lineColor: 'rgb(100,100,100)'
          },
          select: {
            enabled: true,
            lineColor: 'black'
          }
        }
      },
      states: {
        hover: {
          marker: {
            enabled: false
          }
        }
      }
    },
    series: {
      stickyTracking: false,
      allowPointSelect: true,
      boostThreshold:
        //alldataArr.length > 100
        greatestCount > 1000
          ? props.completeData.datasets.length > 0
            ? props.completeData.datasets[0].data.length
            : 6503
          : 0,

      //: 100,

      states: {
        inactive: {
          opacity: 1
        }
      },
      cursor: 'pointer',
      point: {
        events: {
          unselect: function() {
            this.select(true, true);
          },
          click: function(event) {
            var chart = this.series.chart;
            var colorIndex = event.point.colorIndex;
            var pointX = event.point.x;
            var pointY = event.point.y;
            if (chart) {
              chart.showLoading();
            }
            setTimeout(function() {
              setLegendClicked(false);

              if (
                lodash.isEmpty(filteredArr) &&
                colorIndex != null
                //  event.point.colorIndex != null
              ) {
                // var labelIndex = event.point.colorIndex;
                var labelIndex = colorIndex;

                var dataIndex =
                  labelIndex == 0 ? 2 : labelIndex == 2 ? 0 : labelIndex;
                var selectedLabel =
                  props.completeData &&
                  props.completeData.datasets &&
                  props.completeData.datasets[dataIndex]
                    ? props.completeData.datasets[dataIndex].label
                    : '';
                var valueIndex = event.point.index;
                var selectedData = !lodash.isEmpty(filteredArr)
                  ? lodash.filter(
                      filteredArr.datasets[dataIndex].data,
                      item => {
                        return (
                          // item.x == event.point.x && item.y == event.point.y
                          item.x == pointX && item.y == pointY
                        );
                      }
                    )
                  : lodash.filter(
                      props.completeData.datasets[dataIndex].data,
                      item => {
                        // if(event.point.x != null && event.point.y != null &&item.x == event.point.x &&item.y == event.point.y) {
                        return (
                          // item.x == event.point.x && item.y == event.point.y
                          item.x == pointX && item.y == pointY
                        );
                        //  }
                      }
                    );
                if (event.point.colorIndex != null) {
                  event.point.update({
                    marker: {
                      fillColor: '#D3D3D3',
                      lineColor: '#000',
                      lineWidth: 2
                    }
                  });
                }
                if (props.parent && props.parent == 'Details') {
                  history.push({
                    pathname: '/AnalyzeData',
                    prevPath:
                      window.location.pathname == '/LaborItemization'
                        ? window.location.pathname
                        : window.location.pathname + '?chartId=' + 1090,
                    state: {
                      chartId: 1090,
                      drillDown: 16,
                      chartName: chartTitle,
                      category: 0,
                      elr: selectedData[0].y,
                      slodhours: selectedData[0].label,
                      tabSelection: props.tabSelection,
                      timeZone: props.timeZone,
                      label:
                        selectedLabel == 'ELR Repair'
                          ? 0
                          : selectedLabel == 'ELR Competitive'
                          ? 1
                          : 2
                    }
                  });
                }
                let arr = {
                  elr: selectedData[0].y,
                  lbrsoldhours: selectedData[0].label,
                  category:
                    selectedLabel == 'ELR Repair'
                      ? 0
                      : selectedLabel == 'ELR Competitive'
                      ? 1
                      : 2
                };
                setSelectedPoints(selectedData);
                setSelectedData(selectedData);
                setClickedPoints(clickedPoints => [...clickedPoints, arr]);
                setAllData1(dataArr => [...dataArr, arr]);
                setAllDataCopy(dataArr => [...dataArr, arr]);
                setAllData(dataArr => [...dataArr, arr]);
                setElementClicked(true);
                setResetLayout(false);
                showSelectedPoints(selectedData);
                if (chart) {
                  chart.hideLoading();
                }
              }
            }, 1000);
          }
        }
      },
      events: {
        legendItemClick: function(legendItem) {
          var labelIndex = legendItem.target
            ? legendItem.target.colorIndex
            : legendItem;
          var index = labelIndex == 0 ? 2 : labelIndex == 2 ? 0 : labelIndex;
          var item =
            labelIndex == 0
              ? 'Repair'
              : labelIndex == 2
              ? 'Competitive'
              : 'Maintenance';
          var arr = legendArr;
          if (arr.indexOf(index) > -1) {
            arr = arr.filter(function(item) {
              return item !== index;
            });
          } else {
            arr.unshift(index);
          }
          setCtx(this.chart);
          const tempSelectedList = [...arr];
          isPointsDraged(false);
          setLegendClicked(true);
          setLegendArr(tempSelectedList);
          setResetLayout(false);

          props.legendItemClicked(item);
          props.enabledlegendItems(tempSelectedList);
        }
      }
    }
  };
  $('.reset-btn')
    .unbind()
    .click(function() {
      resetAllLayouts();
      if (history) {
        if (
          history.location.state &&
          (history.location.state.compClicked ||
            history.location.state.maintClicked ||
            history.location.state.repClicked)
        ) {
          history.location.state.compClicked = false;
          history.location.state.maintClicked = false;
          history.location.state.repClicked = false;
        }
      }
    });

  useEffect(() => {
    if (resetLayout == true) {
      reEnableLegends();
      setToggledItems([]);
    }
  }, [resetLayout]);

  function resetAllLayouts() {
    var filtered = [];
    chart.length > 1
      ? (filtered = chart.slice(-1).filter(function(x) {
          return x !== undefined;
        }))
      : (filtered = chart.filter(function(x) {
          return x !== undefined;
        }));
    if (chart.length > 1 && filtered.length == 0) {
      filtered = chart.slice(0, -1).filter(function(x) {
        return x !== undefined;
      });
    }
    var points = filtered[0].getSelectedPoints();
    //var chart = this.series.chart;
    if (points.length > 0) {
      if (filtered) {
        filtered[0].showLoading();
      }
      points.forEach(point => {
        point.update(
          {
            selected: false,
            marker: {
              fillColor:
                point.colorIndex == 0
                  ? '#35af557a'
                  : point.colorIndex == 1
                  ? '#f17f7fa8'
                  : '#0389fc61',
              lineColor:
                point.colorIndex == 0
                  ? 'rgba(4, 147, 114, 1)'
                  : point.colorIndex == 1
                  ? 'rgba(220,57,18,1)'
                  : 'rgba(75,192,192,1)'
            }
          },
          false
        );
      });
      setTimeout(() => {
        if (filtered) {
          filtered[0].hideLoading();
        }
      }, 100);
      filtered[0].redraw();
    }

    var commonPoints = lodash.intersection(dataArr, clickedPoints);

    if (commonPoints.length > 0 && filtered.length > 0) {
      $.each(commonPoints, function(j, dataPoints) {
        if (dataPoints.category == 0) {
          $.each(filtered[0].series[0].data, function(i, point) {
            if (
              (Number(dataPoints.lbrsoldhours).toFixed(2) <= 20.0 &&
                Number(point.x).toFixed(2) == Number(dataPoints.lbrsoldhours) &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr)) ||
              (Number(dataPoints.lbrsoldhours).toFixed(2) > 20.0 &&
                Number(point.x).toFixed(2) == 20.0 &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr))
            ) {
              point.select(false);
              point.update({
                marker: {
                  fillColor: '#35af557a',
                  lineColor: 'rgba(4, 147, 114, 1)'
                }
              });
            }
          });
        } else if (dataPoints.category == 2) {
          $.each(filtered[0].series[1].data, function(i, point) {
            if (
              (Number(dataPoints.lbrsoldhours).toFixed(2) <= 20.0 &&
                Number(point.x).toFixed(2) == Number(dataPoints.lbrsoldhours) &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr)) ||
              (Number(dataPoints.lbrsoldhours).toFixed(2) > 20.0 &&
                Number(point.x).toFixed(2) == 20.0 &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr))
            ) {
              point.select(false);
              point.update({
                marker: {
                  fillColor: '#f17f7fa8',
                  lineColor: 'rgba(220,57,18,1)'
                }
              });
            }
          });
        } else if (dataPoints.category == 1) {
          $.each(filtered[0].series[2].data, function(i, point) {
            if (
              (Number(dataPoints.lbrsoldhours).toFixed(2) <= 20.0 &&
                Number(point.x).toFixed(2) == Number(dataPoints.lbrsoldhours) &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr)) ||
              (Number(dataPoints.lbrsoldhours).toFixed(2) > 20.0 &&
                Number(point.x).toFixed(2) == 20.0 &&
                Number(point.y).toFixed(2) == Number(dataPoints.elr))
            ) {
              point.select(false);
              point.update({
                marker: {
                  fillColor: '#0389fc61',
                  lineColor: 'rgba(75,192,192,1)'
                }
              });
            }
          });
        }
      });
    }
    setAllData([]);
    setAllData1([]);
    setResetLayout(true);
    props.enabledlegendItems([]);
  }

  function showSelectedPoints(data) {
    var filtered = [];
    chart.length > 1
      ? (filtered = chart.slice(-1).filter(function(x) {
          return x !== undefined;
        }))
      : (filtered = chart.filter(function(x) {
          return x !== undefined;
        }));
    if (chart.length > 1 && filtered.length == 0) {
      filtered = chart.slice(0, -1).filter(function(x) {
        return x !== undefined;
      });
    }
    if (data.length > 0) {
      setTimeout(() => {
        toast(
          filtered[0],
          data[0].count == 1 || data.length == 1
            ? `<b>${data[0].count ? data[0].count : data.length} Job Added</b>`
            : `<b>${data[0].count ? data[0].count : data.length} Jobs Added</b>`
          // data.length == 1
          //   ? `<b>${data.length} Job Added</b>`
          //   : `<b>${data.length} Jobs Added</b>`
        );
      }, 200);
    }
  }

  const reEnableLegends = () => {
    var filtered = [];
    var disabledLegend = [];
    chart.length > 1
      ? (filtered = chart.slice(-1).filter(function(x) {
          return x !== undefined;
        }))
      : (filtered = chart.filter(function(x) {
          return x !== undefined;
        }));
    if (chart.length > 1 && filtered.length == 0) {
      filtered = chart.slice(0, -1).filter(function(x) {
        return x !== undefined;
      });
    }
    if (filtered[0]) {
      // if (filtered[0] && props.type != 'popup') {
      // filtered[0].legend.allItems.forEach(item => {
      //   if(item.visible == false) {
      //     disabledLegend.push(item.colorIndex);
      //   }

      // });
      filtered[0].legend.allItems.forEach(item => item.setVisible(true));
    }
    resetAllLayouts();
  };
  useEffect(() => {
    if (resetLayout == false) {
      //if (
      //   resetLayout == false &&
      //   ((allData.length == 0 && legendArr.length == 0) ||
      //     (allData.length > 0 && legendArr.length > 0))
      // ) {
      startFetchToggledItems();
    }
  }, [legendArr]);
  function startFetchToggledItems() {
    let labelIndex = legendArr.map(item => {
      if (item == 1) {
        return 2;
      } else if (item == 0) {
        return 1;
      } else {
        return 0;
      }
    });
    let data = allData.filter(word => {
      return !labelIndex.includes(word.category);
    });
    setAllData(data);
    setElementClicked(true);
    setResetLayout(false);
  }

  useEffect(() => {
    setFilteredArr([]);
    if (pointsDraged == true) {
      startFetch(dataArr, legendArr);
    }
  }, [legendArr]);

  function startFetch(data, legendArr) {
    var chart = Highcharts.charts;
    var filtered = [];
    chart.length > 1
      ? (filtered = chart.slice(-1).filter(function(x) {
          return x !== undefined;
        }))
      : (filtered = chart.filter(function(x) {
          return x !== undefined;
        }));
    if (chart.length > 1 && filtered.length == 0) {
      filtered = chart.slice(0, -1).filter(function(x) {
        return x !== undefined;
      });
    }
    if (filtered.length > 0) {
      filtered[0].showLoading();
    }

    setTimeout(function() {
      let legends = data.target.legend && data.target.legend.allItems;
      legends = legends.filter(x => x.visible == false);
      let labelIndex = legends.map(item => {
        if (item.index == 1) {
          return 2;
        } else if (item.index == 2) {
          return 1;
        } else {
          return item.index;
        }
      });

      var xAxisMin = data.xAxis[0].min;
      var xAxisMax = data.xAxis[0].max;
      var yAxisMin = data.yAxis[0].min;
      var yAxisMax = data.yAxis[0].max;
      if (resetLayout == false) {
        if (props.completeData.datasets.length > 0) {
          fetchData(
            xAxisMin,
            xAxisMax,
            yAxisMin,
            yAxisMax,
            labelIndex,
            filtered[0]
          );
        }
      } else {
        setFilteredArr([]);
        setElementClicked(true);
        setResetLayout(true);
      }
    }, 100);
  }

  /*
   * On click, unselect all points
   */
  function unselectByClick() {
    setResetDashboard(true);
    const points = metaArr;
    if (points.length > 0) {
      points.forEach(point => {
        if (point.category != null) {
          point.select(false, false);
        }
      });
    }
  }

  function getSelection(e) {
    // Select points
    if (this.series) {
      this.series.forEach(series => {
        series.points.forEach(point => {
          if (
            point.x >= e.xAxis[0].min &&
            point.x <= e.xAxis[0].max &&
            point.y >= e.yAxis[0].min &&
            point.y <= e.yAxis[0].max
          ) {
            point.select(true, true);
          }
        });
      });
      // Fire a custom event
      Highcharts.fireEvent(this, 'selectedpoints', {
        points: this.getSelectedPoints()
      });
      setMetaArr(this.getSelectedPoints());
      startFetch(e);
      return false;
    }
  }

  const fetchData = (x1, x2, y1, y2, labelIndex, chart) => {
    setLegendClicked(false);
    var data = [];
    let elrData = lodash.filter(props.completeData.datasets[2].data, item => {
      return (
        Number(item.y) >= y1.toFixed(2) &&
        Number(item.y) <= y2.toFixed(2) &&
        Number(item.x) >= x1.toFixed(2) &&
        Number(item.x) <= x2.toFixed(2)
      );
    });

    let mainData = lodash.filter(props.completeData.datasets[1].data, item => {
      return (
        Number(item.y) >= y1.toFixed(2) &&
        Number(item.y) <= y2.toFixed(2) &&
        Number(item.x) >= x1.toFixed(2) &&
        Number(item.x) <= x2.toFixed(2)
      );
    });

    let compData = lodash.filter(props.completeData.datasets[0].data, item => {
      return (
        Number(item.y) >= y1.toFixed(2) &&
        Number(item.y) <= y2.toFixed(2) &&
        Number(item.x) >= x1.toFixed(2) &&
        Number(item.x) <= x2.toFixed(2)
      );
    });
    if (legendArr.length > 0) {
      legendArr.map(item => {
        if (item == 2) {
          elrData = [];
        } else if (item == 1) {
          mainData = [];
        } else {
          compData = [];
        }
      });
    }
    let mainDatasets = {
      label: 'ELR Maintenance',
      data: mainData
    };
    let elrDatasets = {
      label: 'ELR Repair',
      data: elrData
    };
    let compDatasets = {
      label: 'ELR Competitive',
      data: compData
    };
    data = data.concat(elrData, mainData, compData);
    // setSelectedPoints(data);
    if (
      props.type == 'popup' &&
      props.selectedPoints &&
      props.selectedPoints.length > 0
    ) {
      data = data.concat(props.selectedPoints);
    }
    setSelectedPoints(data);

    let labels = data.map(c =>
      c.category == 'ELR Competitive'
        ? 'COMPETITIVE'
        : c.category == 'ELR Maintenance'
        ? 'MAINTENANCE'
        : 'REPAIR'
    );
    let data1 = {
      labels: labels,
      datasets: [compDatasets, mainDatasets, elrDatasets]
    };
    // setFilteredArr(data1);

    data = data.map(item => {
      return {
        lbrsoldhours: item.label,
        elr: item.y,
        category:
          item.category == 'ELR Repair'
            ? 0
            : item.category == 'ELR Competitive'
            ? 1
            : 2
      };
    });
    //appending previous data
    let dataArrAll = [...dataArr, ...data];
    setAllData1(dataArr => [...dataArr, ...data]);
    setAllDataCopy(dataArr => [...dataArr, ...data]);
    data = data.filter(word => {
      return !labelIndex.includes(word.category);
    });
    setAllData(dataArr => [...dataArr, ...data]);
    setElementClicked(true);
    setResetLayout(false);
    showSelectedPoints([...dataArr, ...data]);
    if (chart) {
      chart.hideLoading();
    }
    return data1;
  };

  return (
    <React.Fragment>
      <Card
        id={'chartConteiner-1090'}
        bordered={false}
        className={
          props.parent != 'Details'
            ? clsx(classes.cardContainer, 'child-div')
            : clsx(classes.cardContainerDetail, 'child-div')
        }
      >
        <CardHeader
          title={chartTitle}
          action={
            <MoreActions
              type={props.type}
              chartPopup={chartPopup}
              handleClose={handleClose}
              setActions={setactions}
              resetDashboard={setResetDashboard}
              chartId={1090}
              favoritesDisabled={true}
            ></MoreActions>
          }
          subheader={1090}
          className={classes.cardHeader}
        ></CardHeader>

        <Divider />
        <CardContent style={{ cursor: 'pointer' }}>
          <div className={classes.chartContainter}>
            <HighchartsReact
              id="container"
              containerProps={{
                className:
                  props.parent != 'Details'
                    ? clsx(classes.cardContainer1, 'child-div')
                    : clsx(classes.cardContainerDetail1, 'child-div')
              }}
              highcharts={Highcharts}
              options={options}
            />
          </div>
        </CardContent>
      </Card>
      {props.parent != 'Details' && (
        <ItemizationDataGrid
          history={history}
          data={dataArr}
          ///data={lodash.xorWith(deletedPoints, dataArr, lodash.isEqual)}
          //data={deletedPoints.length > 0 ? lodash.xorWith(deletedPoints, dataArr, lodash.isEqual) :dataArr}
          zoomData={[]}
          tabSelection={props.tabSelection}
          legendArr={legendArr}
          type={props.type}
          isLabor={true}
          handleClosePopup={handleAlertDialogClose}
          elementClicked={elementClicked}
          handleElementClicked={handleElementClicked}
          dataArrSelected={dataArrSelected}
          legendClicked={LegendClicked}
          chart={chart}
          timeZone={props.timeZone}
          selectedPoints={selectedPoints}
          resetLayout={resetLayout}
          allItemDeleted={allItemDeleted}
          selectedPointsData={setSelectedPointsData}
          session={session}
        />
      )}
      <ChartDialog
        open={open}
        chartId={1090}
        chartType="itemization"
        realm={localStorage.getItem('realm')}
        handlePopupClose={handleClose}
        tabSelection={props.tabSelection}
        handleResetClicked={props.handleResetClicked}
        chartData={props.data}
        completeData={props.completeData}
        metaArr={metaArr}
        timeZone={props.timeZone}
        selectedPoints={dataArr}
        dataArrSelected={dataArrSelected}
        data={dataArr}
        chart={chart}
        LegendClicked={LegendClicked}
        legendArr={legendArr}
        legendItemClicked={props.legendItemClicked}
        enabledlegendItems={props.enabledlegendItems}
      />
    </React.Fragment>
  );
};

export default CPELRVsLaborSoldHours;
