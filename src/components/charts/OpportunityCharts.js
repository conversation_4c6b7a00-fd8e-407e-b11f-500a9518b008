/* eslint-disable no-nested-ternary */
/* eslint-disable no-use-before-define */
import { QueryRenderer } from '@cubejs-client/react';
import {
  Card,
  CardContent,
  CardHeader,
  Divider,
  Chip
} from '@material-ui/core';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { useHistory } from 'react-router';
import { getDashboardGraphQuery } from 'src/components/DashboardGraphQuery';
import {
  getChartName,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import cubejsApi from 'src/utils/cubeUtils';
import { useSelector } from 'react-redux';
import {
  getChartConfiguration,
  getDataforLineChart,
  getColorScheme,
  getHasuraRenderedCharts
} from '../../utils/Utils';
import LoaderSkeleton from '../LoaderSkeleton.js';
import MoreActions from '../MoreActions';
import ChartDialog from 'src/components/Dialog';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import AdvisorOpportunityCharts from './AdvisorOpportunityCharts';

var Dealer = process.env.REACT_APP_DEALER;

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;

const OpportunityCharts = ({
  chartId,
  removeFav,
  realm,
  handleClosePopup,
  type,
  isFrom
}) => {
  const history = useHistory();
  const session = useSelector(state => state.session);
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(false);
  const [hasuraData, setHasuraData] = useState([]);
  const [queryState, queryChangedState] = useState(
    getDashboardGraphQuery(
      chartId,
      session.kpiAdvisor,
      JSON.parse(localStorage.getItem('selectedStoreId'))[0],
      realm
    )
  );
  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    localStorage.setItem('popup', true);
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    localStorage.setItem('popup', false);
    setOpen(false);
    handleClosePopup(1);
  };

  useEffect(() => {
    if (session.kpiAdvisor.includes('All')) {
      queryChangedState(
        getDashboardGraphQuery(
          chartId,
          session.kpiAdvisor,
          JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          realm
        )
      );
    } else {
      setLoading(true);
      getChartsDataFromViews(
        //getFilters(session.serviceAdvisor),
        session.kpiAdvisor[0],
        chartId,
        callback => {
          if (callback) {
            setLoading(false);
            setHasuraData(callback);
          }
        }
      );
    }
  }, [session.kpiAdvisor, chartId]);

  const getFilters = advisors => {
    let combiner = `'{`;
    advisors.map((each, index) => {
      combiner = combiner + each + ',';
    });
    combiner = combiner.substring(0, combiner.length - 1) + `}'`;
    return combiner;
  };

  const barRender = ({ resultSet }) => {
    const data = getDataforLineChart(
      resultSet,
      8,
      getChartColorScheme(chartId),
      chartId
    );

    let stacked = chartId == 931 || chartId == 926 ? true : false;
    const options = getChartConfiguration(
      8,
      '',
      stacked,
      '',
      resultSet,
      chartId
    );
    // options.plugins= {
    //   plugins: [ChartDataLabels],
    //   datalabels: {
    //           anchor: chartId==931 ||chartId==926 ?'end':'',
    //           align:  chartId==931 ||chartId==926 ?'top':'',
    //           color:chartId==931 ||chartId==926 ? '#737373':'',
    //           font: {
    //             weight:chartId==931 ||chartId==926 ? 'bold' :''
    //           },
    //        offset: (value,ctx)=> {
    //       let sum;
    //       if(chartId==926  || chartId==931){
    //       let total=value.dataset
    //          if( value.dataset.data[value.dataIndex] <0){
    //           console.log("ds",value.dataset.data[value.dataIndex])
    //        if(chartId==926  )
    //        {
    //         return '60'
    //        }else
    //            return '110'}
    //          else
    //             return '9'
    //         } },
    //     //  display:'auto',

    //           formatter: (value, ctx) => {
    //             let gpFlag=0;
    //             if(chartId==931 ||chartId==926)
    //             {
    //             let datasets = ctx.chart.data.datasets.filter(
    //               (ds, datasetIndex) => ctx.chart.isDatasetVisible(datasetIndex)
    //             )

    //             for (var i = datasets.length - 1; i > -1; i--) {
    //               if (datasets[i].label === "  Gross Profit %"){
    //                 gpFlag=1;
    //               }
    //             }
    //             if (ctx.datasetIndex === datasets.length - 1 || (gpFlag ==0 &&  ctx.datasetIndex ==2)) {

    //               let sum = 0;
    //               datasets.map(dataset => {
    //                 sum += dataset.data[ctx.dataIndex];
    //               });
    //               if(sum >0){
    //                 return '$'+Math.round(sum);
    //               }else{
    //                 return '-$'+Math.round(Math.abs(sum));
    //               }

    //             }
    //             else {
    //               return '';
    //             }
    //           }
    //           },

    //         }
    // }
    const result = (
      <Card
        bordered={false}
        style={{ borderRadius: 0, border: '1px solid #003d6b', width: '100%' }}
      >
        <CardHeader
          title={getChartName(chartId)}
          action={
            <MoreActions
              removeFavourite={removeFavourite}
              setActions={setactions}
              chartId={chartId}
              type={type}
              chartPopup={chartPopup}
              handleClose={handleClose}
              favoritesDisabled={true}
            />
          }
          subheader={getSubHeader(chartId)}
          style={{ borderBottom: '1px solid #003d6b' }}
        />
        <Divider />
        <CardContent
          style={
            window.location.pathname == '/MyFavorites'
              ? localStorage.getItem('popup') == 'true'
                ? { cursor: 'pointer', height: '520px' }
                : { cursor: 'pointer', height: '310px' }
              : { cursor: 'pointer' }
          }
        >
          <div
            style={{
              backgroundColor: '#FFF',
              // borderRadius: 0,
              // border: '1px solid #003d6b',
              position: 'relative',
              margin: 'auto',
              // height: '50vh',
              height:
                type == 'popup' &&
                isFrom != 'details' &&
                chartId == 921 &&
                window.location.pathname != '/MyFavorites'
                  ? window.innerHeight - 170 + 'px'
                  : isFrom == 'details'
                  ? '383px'
                  : (chartId == 931 &&
                      window.location.pathname != '/MyFavorites') ||
                    (chartId == 926 &&
                      window.location.pathname != '/MyFavorites') ||
                    (chartId == 921 &&
                      isFrom != 'details' &&
                      window.location.pathname != '/MyFavorites')
                  ? window.innerHeight - 445 + 'px'
                  : chartId == 921 &&
                    isFrom != 'details' &&
                    window.location.pathname == '/MyFavorites'
                  ? window.innerHeight - 345 + 'px'
                  : '300px',

              width:
                chartId == 931 || chartId == 926
                  ? isFrom != 'details' && type != 'popup'
                    ? '80%'
                    : '100%'
                  : '100%'
            }}
          >
            <Bar
              data={data}
              options={options}
              id={'chart-id-' + chartId}
              getElementAtEvent={dataset => {
                if (dataset.length > 0) {
                  var dataIndex = dataset[0]._datasetIndex;
                  var valueIndex = dataset[0]._index;
                  var xAxis = data.datasets[dataIndex].data[valueIndex];
                  var yAxis = data.labels[valueIndex];
                  if (xAxis > 0) {
                    history.push({
                      // pathname: '/AnalyzeData',
                      pathname:
                        chartId == 926 ||
                        chartId == 927 ||
                        chartId == 928 ||
                        chartId == 929
                          ? '/PartsWorkMixAnalysis'
                          : '/LaborWorkMixAnalysis',
                      prevPath:
                        chartId == 921 &&
                        window.location.pathname == '/GraphDetailsView'
                          ? '/CPELROpportunity'
                          : window.location.pathname,
                      isFrom: 'opportunity',
                      state: {
                        x: xAxis,
                        y: yAxis,
                        isPartsCharts: false,
                        tabSelection: 'one'
                      }
                    });
                  }
                }
              }}
            />
          </div>
        </CardContent>
      </Card>
    );
    return result;
  };

  const getChartColorScheme = chartId => {
    let coloreScheme;
    switch (chartId) {
      case 932:
      case 929:
        coloreScheme = getColorScheme(8)[0];
        break;
      case 933:
      case 927:
        coloreScheme = getColorScheme(8)[2];
        break;
      case 934:
      case 928:
        coloreScheme = getColorScheme(8)[1];
        break;
      case 924:
        coloreScheme = getColorScheme(8)[3];
        break;
    }
    return coloreScheme;
  };

  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        SelectedLocation: window.location.pathname
      });
    }
  };
  useEffect(() => {
    queryChangedState(
      getDashboardGraphQuery(
        chartId,
        session.serviceAdvisor,
        JSON.parse(localStorage.getItem('selectedStoreId'))[0],
        realm
      )
    );
  }, [session.serviceAdvisor]);
  return (
    <>
      {!session.kpiAdvisor.includes('All') &&
      getHasuraRenderedCharts().includes(chartId.toString()) == true &&
      hasuraData ? (
        <AdvisorOpportunityCharts
          chartId={chartId}
          resultSet={hasuraData}
          showTopBar={true}
          setActions={setactions}
          title={getChartName(chartId)}
          isStacked={Number(chartId) === 935 ? true : false}
          isFromMetrices={Number(chartId) === 938 ? true : false}
          isFrom={isFrom}
          realm={realm}
          handleClose={handleClose}
          type={type}
          isLoading={isLoading}
          removeFavourite={removeFavourite}
        />
      ) : (
        <QueryRenderer
          query={queryState}
          cubejsApi={cubejsApi()}
          render={renderChart(barRender)}
        />
      )}
      <ChartDialog
        open={open}
        chartId={chartId}
        chartType="opportunityCharts"
        realm={realm}
        handlePopupClose={handleClose}
      />
    </>
  );
};
OpportunityCharts.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
  filterCharts: PropTypes.func,
  removeFav: PropTypes.func
};

export default OpportunityCharts;
