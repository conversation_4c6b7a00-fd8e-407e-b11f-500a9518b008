import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import {
  getChartType,
  getLineChartType,
  GetyAxisRange,
  getChartName,
  getChartDrillDown,
  getChartDataIndex,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import {
  getChartConfiguration,
  getDataforLineChart,
  getDataforLineChartFromHasura,
  applyPartialMonthFilterLineCharts,
  getDataforLineChartFromPostgraphile,
  getDbdName
} from '../../utils/Utils';
import {
  Card,
  CardHeader,
  CardContent,
  Divider,
  Tooltip,
  Grid,
  CircularProgress
} from '@material-ui/core';
import MoreActions from '../MoreActions';
import { useHistory } from 'react-router';
import { makeStyles } from '@material-ui/core/styles';
import ChartDialog from 'src/components/Dialog';
import { useSelector, useDispatch } from 'react-redux';
var lodash = require('lodash');

const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
const HasuraDashboardLineRenderer = ({
  chartId,
  isFrom,
  callback,
  filterCharts,
  showCurrentMonth,
  realm,
  handleClose,
  type,
  backTobutton,
  handleHighlight,
  selected,
  selectedChartId,
  removeFavourite,
  headerClick,
  dbdName
}) => {
  const classes = useStyles();
  const history = useHistory();
  const [open, setOpen] = useState(false);
  const [loader, setLoader] = useState(false);
  const [hasuraData, setHasuraData] = useState([]);
  const session = useSelector(state => state.session);
  useEffect(() => {
    setactions(filterCharts);
  }, [filterCharts]);

  useEffect(() => {
    console.log('ccc==aaa[[[', session.toggleStatus);
    setLoader(true);
    getChartsDataFromViews(session.serviceAdvisor, chartId, callback => {
      if (callback) {
        var jsondataa = callback[0].jsonData;
        if (jsondataa.includes('0') || jsondataa.includes('0.00')) {
          jsondataa = jsondataa.replace(/"0(\.00)?"(?=,|\]|\})/g, 'null');
        }
        if (chartId == 1238) {
          let data = JSON.parse(jsondataa)[0].datasets;
          let arr = [];
          let arr1 = [];
          let obj = {};
          arr.push(lodash.find(data, ['chartId', '1226']));
          obj.datasets = arr;
          obj.labels = JSON.parse(jsondataa)[0].labels;
          arr1.push(obj);
          setHasuraData(arr1);
        } else {
          setHasuraData(JSON.parse(jsondataa));
        }
        setLoader(false);
      }
    });
  }, [session.serviceAdvisor]);
  var dashboardName = getDbdName(chartId);
  const setactions = val => {
    console.log('ppp===isFrom', isFrom);
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        SelectedLocation: window.location.pathname,
        handleHighlight: selectedChartId,
        selectedGrid: selected,
        isFrom: isFrom
      });
    }
  };
  const handleBackToButton = chartId => {
    backTobutton(chartId);
  };

  var stepSize =
    typeof GetyAxisRange(chartId, isFrom) != 'undefined'
      ? GetyAxisRange(chartId, isFrom)
      : '';
  var chartType = getChartType(chartId, isFrom);
  var lineChartType = getLineChartType(chartId, isFrom);
  const data = getDataforLineChartFromPostgraphile(
    hasuraData,
    lineChartType,
    undefined,
    chartId
  );
  if (session.toggleStatus == false) {
    applyPartialMonthFilterLineCharts(data);
  }
  const options = getChartConfiguration(
    chartType,
    session.serviceAdvisor.includes('All')
      ? stepSize
      : chartId == 916
      ? stepSize
      : undefined,
    '',
    '',
    '',
    chartId,
    true
  );
  if (
    session.toggleStatus == false &&
    (dbdName == 'CP Labor Overview' ||
      dbdName == 'CP Parts Overview' ||
      dbdName == 'details' ||
      type == 'popup')
  ) {
    applyPartialMonthFilterLineCharts(data);
  }
  const chartPopup = val => {
    localStorage.setItem('popup', true);
    setOpen(true);
  };
  const handleCloses = () => {
    localStorage.setItem('popup', false);
    setOpen(false);
    handleClose(1);
  };
  return (
    <>
      {loader == true ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <Card
          bordered={false}
          style={{
            height: '100%',
            borderRadius: 0,
            border: '1px solid #003d6b'
          }}
        >
          <CardHeader
            title={getChartName(chartId)}
            action={
              <MoreActions
                chartId={chartId}
                setActions={setactions}
                type={type}
                removeFavourite={removeFavourite}
                chartPopup={chartPopup}
                handleClose={handleCloses}
                handleBackToButton={handleBackToButton}
                handleHighlight={handleHighlight}
              ></MoreActions>
            }
            subheader={getSubHeader(chartId)}
            style={{ borderBottom: '1px solid #003d6b' }}
            onClick={() =>
              typeof headerClick === 'function' && headerClick(chartId)
            }
            id={'card-header-' + chartId}
          ></CardHeader>

          <Divider />

          <CardContent
            style={
              // chartId == 1334 ||
              // chartId == 1318 ||
              // chartId == 916 ||
              // chartId == 953 ||
              // chartId == 966 ||
              // chartId == 1073
              //   ? { cursor: 'pointer', height: '80%' }:
              { cursor: 'pointer', height: '85%' }
            }
          >
            <Line
              data={data}
              options={options}
              getElementAtEvent={dataset => {
                if (dataset.length > 0) {
                  var dataIndex = dataset[0]._datasetIndex;
                  var valueIndex = dataset[0]._index;
                  var xAxis = data.datasets[dataIndex].data[valueIndex];
                  var yAxis = data.labels[valueIndex];
                  if (
                    chartId != 942 &&
                    chartId != 939 &&
                    chartId != 946 &&
                    chartId != 940 &&
                    chartId != 920 &&
                    chartId != 925 &&
                    chartId != 766
                  ) {
                    var splittedMonth = yAxis.split('-', 2);
                    var yearMonth =
                      data.datasets[dataIndex].label + '-' + splittedMonth[1];
                  }
                  if (chartId == 1238) {
                    var splittedMonth = yAxis.split('-', 2);
                    yearMonth = splittedMonth[0] + '-' + splittedMonth[1];
                  }

                  history.push({
                    pathname: '/AnalyzeData',
                    search: '?chartId=' + 'drillDown',
                    //prevPath:window.location.pathname,
                    prevPath:
                      window.location.pathname === '/GraphDetailsView'
                        ? Number(chartId) === 1073 ||
                          Number(chartId) === 1098 ||
                          Number(chartId) === 1356 ||
                          Number(chartId) === 956 ||
                          Number(chartId) === 966 ||
                          Number(chartId) === 1334
                          ? window.location.pathname + '?chartId=' + chartId
                          : window.location.pathname
                        : window.location.pathname,
                    state: {
                      chartId: chartId,
                      x: xAxis,
                      y: typeof yearMonth != 'undefined' ? yearMonth : yAxis,
                      drillDown: getChartDrillDown(chartId, isFrom),
                      chartName: getChartName(chartId),
                      category: getChartDataIndex(chartId, dataIndex, isFrom)
                    },
                    handleHighlight: selectedChartId,
                    selectedGrid: selected
                  });
                }
              }}
            ></Line>
          </CardContent>
          <ChartDialog
            open={open}
            chartId={chartId}
            realm={realm}
            chartType="line"
            handlePopupClose={handleCloses}
            headerClick={headerClick}
            selected={selected}
            selectedChartId={selectedChartId}
            handleHighlight={handleHighlight}
          />
        </Card>
      )}
    </>
  );
};

HasuraDashboardLineRenderer.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
  yAxisRanges: PropTypes.array,
  filterCharts: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default HasuraDashboardLineRenderer;
