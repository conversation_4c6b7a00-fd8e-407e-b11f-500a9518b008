import React, { useEffect, useState } from 'react';
import { Bar, Doughnut, Pie, HorizontalBar } from 'react-chartjs-2';
import {
  getDataforKPIs,
  getChartConfigurationKPIs,
  getOptionsForPie1,
  getOptionsForPie2,
  getKpiChartTitle,
  getOptionsForHigh<PERSON>hart,
  getOptionsForHighchartsPie,
  getOptionsForColumnHighcharts,
  getOptionsForHighchartsLinearGauge,
  getOptionsForPie1346
} from '../../utils/Utils';
import Paper from '@material-ui/core/Paper';
import clsx from 'clsx';
import {
  Card,
  Grid,
  CardHeader,
  CardContent,
  Divider,
  Tooltip,
  Typography,
  IconButton
} from '@material-ui/core';
import { useHistory } from 'react-router';
import 'chartjs-plugin-doughnutlabel';
import 'chartjs-plugin-datalabels';
import { useSelector, useDispatch } from 'react-redux';
import { makeStyles } from '@material-ui/styles';
import HighchartsReact from 'highcharts-react-official';
import Highcharts from 'highcharts';
import funnel from 'highcharts/modules/funnel.js';
import Gauge from 'react-svg-gauge';
import GaugeChart from 'react-gauge-chart';
import bullet from 'highcharts/modules/bullet.js';
import DataCard from 'src/components/DataCardKpi';
import ReactSpeedometer from 'react-d3-speedometer';
import ArrowBackOutlinedIcon from '@material-ui/icons/ArrowBackOutlined';
import AssignmentReturnOutlinedIcon from '@material-ui/icons/AssignmentReturnOutlined';
import KPIExtendedSummary from 'src/views/KPI/KPIExtendedSummary';

import $ from 'jquery';

funnel(Highcharts);
bullet(Highcharts);

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  cardContentAll: {
    cursor: 'pointer',
    paddingBottom: 6
  },
  pieContainerRo1: {
    float: 'none',
    marginRight: 0
  },
  pieContainerRo2: {
    float: 'right',
    marginRight: 97
  },
  pieContainerRo3: {
    float: 'left',
    marginLeft: 75,
    position: 'fixed',
    marginTop: -5,
    width: '88%'
  },
  pieContainerRo4: {
    display: 'none'
  },
  pieContainerLine1: {
    float: 'right',
    marginRight: 83,
    width: '100%'
  },
  pieContainerLine2: {
    float: 'left',
    marginLeft: 66,
    position: 'fixed',
    marginTop: 12,
    width: '88%'
  },
  pie13363: {
    display: 'none'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  chartRoot: {
    maxWidth: '30%',
    marginRight: 20
  },
  chartRoot1336: {
    maxWidth: '40%'
  },
  
  chartRoot1340: {
    maxWidth: '26%'
  },
  chartRoot1344: {
    maxWidth: '80%'
  },
  chartRoot1341: {
    maxWidth: '49%'
  },
  chartRoot1343: {
    maxWidth: '40%'
  },
  chartRoot1339: {
    maxWidth: '30%'
  },
  lineROChart: {
    display: 'flex',
    justifyContent: 'center'
  },
  roShareChart: {
    display: 'flex',
    justifyContent: 'center'
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 600,
    fontFamily: 'Roboto',
    marginBottom: 25,
    textAlign: 'left',
    marginTop: 15,
    marginLeft: 13,
    color: '#003d6b'
  },
  mileageChart: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 7,
  },
  mileageChart1343: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 8
  },
  PaperLaborSale1341: {
    height: '22%',
    width: 296,
    display: 'flex',
    justifyContent: 'center',
    marginLeft: 88
  },
  PaperLaborSale13411: {
    height: '22%',
    width: 296,
    display: 'flex',
    justifyContent: 'center'
    //marginLeft: 88
  },
  HeaderComponent1341: {
    paddingTop: 2,
    marginBottom: 5,
    paddingBottom: 9
  },
  imageComponent1341: {
    display: 'flex',
    height: 70,
    width: 70,
    margin: 'auto',
    marginTop: 20
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '100%',
  },
  PaperLaborSale1343: {
    display: 'none'
  },
  highChartContainer1340: {
    marginTop: -17,
    '@media (max-width: 1920px)': {
      width: '300px !important'
      //marginLeft: -13,
    },
    '@media (max-width: 1280px)': {
      width: '300px !important'
      //marginLeft: -37,
    },
    '@media (min-width: 2304px)': {
      width: '500px !important'
      //marginLeft: 20,
    },
    '@media (min-width: 2560px)': {
      width: '500px !important'
      //marginLeft: 20,
    }
  },
  visualization: {
    display: 'block'
  },
  chartHeader: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  PaperSale1343: {
    // '@media (min-width: 2560px)': {
    //   width: '48.5% !important'
    // },
    '@media (min-width: 2560px)': {
      width: '48.5% !important',
    },
    '@media (max-width: 2304px)': {
      width: '51.5% !important',
    },
    '@media (max-width: 2300px)': {
      width: '27% !important',
    },
    '@media (max-width: 1920px)': {
      width: '47% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      // width: '44.5% !important',
      // width: '46.5% !important',
      width: '43% !important',
    },
    // width: '47%',
    marginLeft: 4,
    border: '1px solid #75757575',
    marginRight: 14
  },
  PaperSale1341: {
    '@media (min-width: 2560px)': {
      width: '42% !important',
    },
    '@media (max-width: 2304px)': {
      width: '42% !important',
    },
    '@media (max-width: 2300px)': {
      width: '38% !important',
    },
    '@media (max-width: 1920px)': {
      width: '38% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '37.5% !important',
    },
    // width: '38%',
    marginLeft: 4,
    border: '1px solid #75757575'
  },
  PaperGpp1343: {
    dispaly: 'none',
    width: 0
  },
  PaperGpp1341: {
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#109618',
    border: '1px solid #75757575',
    '@media (min-width: 2560px)': {
      marginTop: '4% !important',
      width: '16%',
    },
    '@media (max-width: 2304px)': {
      marginTop: '5% !important',
      width: '12%',
    },
    '@media (max-width: 1920px)': {
      marginTop: '5.3% !important',
      width: '20%',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginTop: '5% !important',
      width: '12%',
    },
    // '@media screen and (min-width: 1900px)': { 
    //   marginTop: '10% !important',
    // },
    // '@media screen and (min-width: 1400px)': { 
    //   marginTop: '10% !important',
    // },
    height: '20%'
  },
  PaperGpp1342: {
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#109618',
    border: '1px solid #75757575',
    '@media (min-width: 2560px)': {
      marginTop: '4% !important',
      width: '16%',
    },
    '@media (max-width: 2304px)': {
      marginTop: '5% !important',
      width: '12%',
    },
    '@media (max-width: 1920px)': {
      marginTop: '5.3% !important',
      width: '20%',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginTop: '5% !important',
      width: '12%',
    },
    // '@media screen and (min-width: 1900px)': { 
    //   marginTop: '10% !important',
    // },
    // '@media screen and (min-width: 1400px)': { 
    //   marginTop: '10% !important',
    // },
    height: '20%'
  },
  PaperGp1343: {
    border: '1px solid #75757575',
    marginRight: 5,

    '@media (min-width: 2560px)': {
      width: '48.5% !important',
    },
    '@media (max-width: 2304px)': {
      width: '51.5% !important',
    },
    '@media (max-width: 2300px)': {
      width: '27% !important',
    },
    '@media (max-width: 1920px)': {
      width: '47% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      // width: '44.5% !important',
      // width: '46.5% !important',
      width: '43% !important',
    },

    // '@media (min-width: 2560px)': {
    //   width: '47%',
    // },
    // '@media (max-width: 2304px)': {
    //   width: '27%',
    // },
    // '@media (max-width: 1920px)': {
    //   width: '47%',
    // },
    // '@media (min-width: 1920px) and (max-width: 2560px)':  {
    //   width: '27%',
    // },
  },
  PaperGp1341: {
    width: '37%',
    marginRight: 4,
    border: '1px solid #75757575',
  },
  centerText1341: {
    fontWeight: 600,
    fontSize: 17,
    marginTop: 10,
    marginBottom: 10,
    color: '#FFF',
    textAlign: 'center',
    '@media (min-width: 2560px)': {
      fontSize: '19px !important',
    },
    '@media (max-width: 2304px)': {
      fontSize: '19px !important',
    },
    '@media (max-width: 1920px)': {
      fontSize: '17px !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      fontSize: '19px !important',
    },
    //marginLeft: 39
  },
  centerValue1341: {
    fontWeight: 600,
    fontSize: 30,
    color: '#FFF',
    textAlign: 'center'
    //marginLeft: 22
  },
  container1340_1: {
    textAlign: 'center',
    '@media (max-width: 1920px)': {
      marginTop: '4px !important',
    },
    '@media screen and (min-width: 1900px)': { 
      marginTop: '3% !important',
    },
    // '@media screen and (min-width: 1400px)': { 
    //   marginTop: '10% !important',
    // }
   
  },
 
  PaperGpp1351: {
    display: 'none'
  },
  PaperGpp1337: {
    width: 130,
    height: '20%',
    marginLeft: -65,
    marginRight: 43,
    paddingBottom: 10,
    backgroundColor: '#FF9900',
    '@media (max-width: 2304px)': {
      marginTop: '6% !important',
    },
    '@media (max-width: 1920px)': {
      marginTop: '8% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginTop: '6% !important',
    },
    //e1ad01
  },
  centerText1337: {
    color: '#FFF',
    marginTop: 10,
    textAlign: 'center',
    fontWeight: 600,
    marginBottom: 10,
    '@media (max-width: 2304px)': {
      fontSize: '18px !important',
    },
    '@media (max-width: 1920px)': {
      fontSize: '14px !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      fontSize: '18px !important',
    },
    // '@media screen and (min-width: 1400px)': { 
    //   fontSize: '16px !important',
    // },
  },
  chartContainer1339: {
    marginTop: '-9px !important',
    '@media (min-width: 2560px)': {
      width: '100.5% !important',
    },
    '@media (max-width: 2304px)': {
      width: '90% !important',
    },
    '@media (max-width: 1920px)': {
      width: '100% !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '90% !important'
    },
  },
  chartContainer1335: {
    '@media (min-width: 2560px)': {
      marginTop: '17px !important',
      width: '90% !important',
      marginLeft: '5% !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '17px !important',
      width: '90% !important',
      marginLeft: '5% !important'
    },
    '@media (max-width: 1920px)': {
      marginTop: '0px !important',
      width: '100% !important',
      marginLeft: '0% !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginTop: '17px !important',
      width: '90% !important',
      marginLeft: '5% !important'
    },
    // '@media screen and (min-width: 1900px)': { 
    //   marginTop: '1px !important',
    // },
    // '@media screen and (min-width: 1400px)': { 
    //   marginTop: '1px !important',
    // }
  },
  parentContainer1336: {
    '@media (min-width: 2560px)': {
      marginTop: '30px !important',
      marginRight: 6,
      display: 'flex'
    },
    '@media (max-width: 2304px)': {
      marginTop: '30px !important',
      marginRight: 6,
      display: 'flex'
    },
    '@media (max-width: 1920px)': {
      marginTop: '10px !important',
      marginRight: 6,
      display: 'flex',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginTop: '30px !important',
      marginRight: 6,
      display: 'flex',
    },
  },
  parentContainer1336_single: {
    '@media (min-width: 2560px)': {
      marginTop: '30px !important',
      width: '100% !important',
      marginRight: 6
    },
    '@media (max-width: 2304px)': {
      marginTop: '30px !important',
      width: '100% !important',
      marginRight: 6
    },
    '@media (max-width: 1920px)': {
      marginTop: '10px !important',
      width: '99.8% !important',
      marginRight: 6
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginTop: '30px !important',
      width: '88% !important',
      marginRight: 6
    },
  },
  imageComponent: {
    display: 'flex',
    height: 100,
    marginTop: 10
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    //width: '40%'
    width: '100%',
  },
  HeaderComponent: {
    display: 'flex',
    marginBottom: 2,
    justifyContent: 'stretch',
    paddingTop: 2,
    paddingBottom: 2,
    height: 80
  },
  dataValue: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  dataComponent: {
    width: '62%',
    marginLeft: 15,
    marginTop: 10
  },
  dataSubValue: {
    display: 'flex',
    marginTop: 9,
    justifyContent: 'space-between',
    width: '55%'
  },
  valSubHead: {
    width: '100%',
    marginTop: 10,
    fontSize: 15,
    color: '#757575'
  },
  titleLabel: {
    color: '#ee7600'
  },
  PaperLineRo1337: {
    '@media (min-width: 2560px)': {
      width: '97% !important',
      marginTop: '-1px !important',
      height: '411px !important'
    },
    '@media (max-width: 2304px)': {
      width: '88% !important',
    },
    '@media (max-width: 1920px)': {
      width: '97% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '88% !important',
      height: '394px !important'
    },
    // '@media (max-width: 1440px)': { 
    //   width: '51% !important',
    // },
    '@media (max-width: 1440px)': { 
      width: '96.2% !important',
    },
    '@media (max-width: 1400px)': { 
      width: '51.2% !important',
    },
    border: '1px solid #75757575',
    marginLeft: 4,
    marginRight: 6,
    marginTop: 10
  },
  kpiLaborGrid: {
    '@media (min-width: 2560px)': {
      width: '96.9% !important',
      marginTop: '-4px !important'
    },
    '@media (max-width: 2304px)': {
      width: '87% !important',
      marginTop: '-4px !important'
    },
    '@media (max-width: 1920px)': {
      width: '96.8% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '87% !important',
      marginTop: '-4px !important'
    },
    '@media (max-width: 1440px)': { 
      width: '95.5% !important',
    },
    '@media (max-width: 1400px)': { 
      width: '51.2% !important',
    },
    marginRight: 6
  },
  kpi1336: {
    '@media (min-width: 2560px)': {
      width: '96.9% !important',
      height: '222px !important'
    },
    '@media (max-width: 2304px)': {
      width: '88% !important',
      height: '222px !important'
    },
    '@media (max-width: 1920px)': {
      width: '96% !important',
      height: '173px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '88% !important',
      height: '215px !important'
    },
    '@media (max-width: 1440px)': { 
      width: '51.2% !important',
      height: '173px !important'
    },
    '@media (max-width: 1400px)': { 
      width: '51.2% !important',
      height: '173px !important'
    },
    marginRight: 6,
    height: 173, 
    marginTop: -6
  },
  kpi1336TotalRo: {
    '@media (min-width: 2560px)': {
      width: '96.9% !important',
      height: '222px !important'
    },
    '@media (max-width: 2304px)': {
      width: '98.1% !important',
      height: '222px !important'
    },
    '@media (max-width: 1920px)': {
      width: '93% !important',
      height: '173px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '88% !important',
      height: '219px !important'
    },
    '@media (max-width: 1680px)': {
      width: '92% !important',
      height: '173px !important'
    },
    '@media (max-width: 1440px)': { 
      width: '91.5% !important',
      height: '173px !important'
    },
    marginRight: 6,
    height: 173, 
    marginTop: -6
  },
  mileAgeChart: {
    width: '96%',
    marginRight: 6,
    height: 236, 
    marginTop: -6,
    '@media (min-width: 2560px)': {
      height: '232px !important',
      width: '96.9% !important',
      marginTop: '-6px !important',
    },

    '@media (max-width: 2304px)': {
      height: '297px !important',
      width: '88% !important',
      marginTop: '-6px !important',
    },
    '@media (max-width: 1920px)': {
      height: '204px !important',
      width: '96% !important',
      marginTop: '-6px !important' ,
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      height: '297px !important',
      width: '88% !important',
      marginTop: '-6px !important',
    },
  },
  highchartContainer1341: {
    '@media (min-width: 2560px)': {
      width: '482px !important',
      height: '248px !important'
      // height: '248px !important'
    },
    '@media (max-width: 2304px)': {
      width: '353px !important',
      height: '248px !important'
    },
    '@media (max-width: 1920px)': {
      width: '200px !important',
      height: '190px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '312px !important',
      height: '248px !important'
    },
  },
  highchartContainer1343: {
    '@media (min-width: 2560px)': {
      // width: '530px !important',
      width: '599px !important',
      height: '248px !important'
    },
    '@media (max-width: 2304px)': {
      // width: '438px !important',
      width: '410px !important',
      height: '248px !important'
    },
    '@media (max-width: 1920px)': {
      width: '270px !important',
      height: '190px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '356px !important',
      // width: '410px !important',
      height: '248px !important'
    },
    '@media (max-width: 1440px)': {
      width: '250px !important',
      height: '190px !important'
    },
  },
  parentContainer1335: {
    marginRight: 6,
    height: 173, 
    marginTop: -6,
    '@media (min-width: 2560px)': {
      width: '96.9% !important',
      height: '220px !important'
    },
    '@media (max-width: 2304px)': {
      width: '88% !important',
      height: '220px !important'
    },
    '@media (max-width: 1920px)': {
      width: '96% !important',
      height: '173px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '88% !important',
      height: '215px !important'
    },
  },
  parentContainer1344: {
    marginRight: 6,
    height: 394, 
    marginTop: -6,
    '@media (min-width: 2560px)': {
      width: '96.9% !important',
    },
    '@media (max-width: 2304px)': {
      width: '88% !important',
    },
    '@media (max-width: 1920px)': {
      width: '96% !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      width: '88% !important',
    }
  },
  parentFullContainer1344: {
    width: '100%',
    '@media (min-width: 2560px)': {
      marginLeft: '18% !important',
    },
    '@media (max-width: 2304px)': {
      marginLeft: '18% !important',
    },
    '@media (max-width: 1920px)': {
      marginLeft: '0% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginLeft: '18% !important',
    },
  },
  kpiLaborGridTop: {
    display: 'flex',
    justifyContent: 'left',
    '@media (min-width: 2560px)': {
      marginLeft: '-2% !important',
    },
    '@media (max-width: 2304px)': {
      marginLeft: '-2% !important',
    },
    '@media (max-width: 1920px)': {
      marginLeft: '10% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginLeft: '-2% !important',
    },
  },
  valLabelGrid: {
    marginLeft: '-14%', 
    '@media (min-width: 2560px)': {
      fontSize: '30px !important',
      lineHeight: 1.2
    },
    '@media (max-width: 2304px)': {
      fontSize: '30px !important',
      lineHeight: 1.2
    },
    '@media (max-width: 1920px)': {
      fontSize: '25px !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      fontSize: '30px !important',
      lineHeight: 1.2
    },
  },
  valLabelGridBottom: {
    '@media (min-width: 2560px)': {
      fontSize: '18px !important',
    },
    '@media (max-width: 2304px)': {
      fontSize: '18px !important',
    },
    '@media (max-width: 1920px)': {
      fontSize: '14px !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      fontSize: '18px !important',
    },
  },
  kpiLaborGridBottom: {
    marginTop: '-7%',
    textAlign: 'right',
    '@media (min-width: 2560px)': {
      marginRight: '4% !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '4% !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '11% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginRight: '4% !important',
    },
  },
  valLabel1346: {
    marginRight: '5%', 
    color: '#dc3912',
    '@media (min-width: 2560px)': {
      fontSize: '30px !important',
    },
    '@media (max-width: 2304px)': {
      fontSize: '30px !important',
    },
    '@media (max-width: 1920px)': {
      fontSize: '25px !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      fontSize: '30px !important',
    },
  },
  valLabel1346Top: {
    '@media (min-width: 2560px)': {
      fontSize: '18px !important',
    },
    '@media (max-width: 2304px)': {
      fontSize: '18px !important',
    },
    '@media (max-width: 1920px)': {
      fontSize: '14px !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      fontSize: '18px !important',
    },
  },
  pieContainer1344: {
    '@media (min-width: 2560px)': {
      marginLeft: '18px !important',
      marginTop: '14px !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '50% !important',
      marginTop: '14px !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '29% !important',
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginLeft: '50% !important',
      marginTop: '14px !important'
    },
  },
  pyramidContainer1344: {
    '@media (min-width: 2560px)': {
      marginLeft: '10% !important',
      marginTop: '0% !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '10% !important',
      marginTop: '0% !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '2% !important',
      marginTop: '0% !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)':  {
      marginLeft: '10% !important',
      marginTop: '0% !important'
    },
  }
}));
const KPISummaryChartsExtended = props => {
  console.log('toggleOption', props);
  const history = useHistory();
  const session = useSelector(state => state.session);
  const classes = useStyles();
  var gridTitle = 'CP';
  var gridTitleParts = 'CP';
  let partsMatrixTypes = JSON.parse(localStorage.getItem('partsMatrixTypes'));
  if(props.internalMisses == true &&
    (localStorage.getItem('realm') == 'billknightag')) {
    gridTitle = 'Int';
  }
  if(props.internalMisses == true && partsMatrixTypes.length > 1 &&
    (localStorage.getItem('realm') == 'billknightag')) {
    gridTitleParts = 'Int';
  }
  let data =
    props.chartId == 1336
      ? getDataforKPIs(props.ROShareData, props.chartId)
      : props.chartId == 1337
      ? getDataforKPIs(props.LineROLtSixtyK, props.chartId)
      : props.chartId == 1338
      ? getDataforKPIs(props.LineROGtSixtyK, props.chartId)
      : props.chartId == 1339
      ? getDataforKPIs(props.FlatRateHrs, props.chartId)
      : props.chartId == 1340
      ? getDataforKPIs(props.AvgAgeMiles, props.chartId)
      : props.chartId == 1341
      ? getDataforKPIs(props.LaborGpRo, props.chartId)
      : props.chartId == 1342
      ? getDataforKPIs(props.PartsGpRo, props.chartId)
      : props.chartId == 1343
      ? getDataforKPIs(props.TotalGpRo, props.chartId)
      : props.chartId == 1344
      ? getDataforKPIs(props.WorkMix, props.chartId)
      : props.chartId == 1351
      ? getDataforKPIs(props.LineRO, props.chartId)
      : props.chartId == 1346
      ? getDataforKPIs(props.LaborGrid, props.chartId)
      : props.chartId == 1353
      ? getDataforKPIs(props.PartsGrid, props.chartId)
      : getDataforKPIs(props.allCWITData, props.chartId);
  let data1 = {};
  let data2 = {};
  let data3 = {};
  let data4 = {};
  if (
    props.chartId == 1336 &&
    !lodash.isEmpty(data) &&
    data.labels.length > 0
  ) {
    console.log('ro share', data);
    data1 = {
      labels:
        session.serviceAdvisor == 'All'
          ? [data.labels[0]]
          : [data.labels[4], data.labels[0]],
      datasets: [
        {
          label: 'My First Dataset',
          data:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].data[0]]
              : [data.datasets[0].data[0], data.datasets[0].data[4]],
          backgroundColor:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].backgroundColor[0]]
              : [
                  data.datasets[0].backgroundColor[0],
                  data.datasets[0].backgroundColor[2]
                ],
          hoverOffset: 4
        }
      ]
    };
    data2 = {
      labels:
        session.serviceAdvisor == 'All'
          ? [data.labels[1]]
          : [data.labels[5], data.labels[1]],
      datasets: [
        {
          label: 'My First Dataset',
          data:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].data[1]]
              : [data.datasets[0].data[1], data.datasets[0].data[5]],
          backgroundColor:
            session.serviceAdvisor == 'All'
              ? [data.datasets[0].backgroundColor[1]]
              : [
                  data.datasets[0].backgroundColor[1],
                  data.datasets[0].backgroundColor[3]
                ],
          hoverOffset: 4
        }
      ]
    };
  } else if (
    (props.chartId == 1337 || props.chartId == 1338 || props.chartId == 1351) &&
    !lodash.isEmpty(data) &&
    data.labels.length > 0
  ) {
    if(props.chartId == 1337) {
      console.log('Line ro Lt', data);

    }
    data1 = {
      labels: [data.labels[1], data.labels[2]],
      datasets: [
        {
          label: 'My First Dataset',
          data: [ data.datasets[0].data[2],data.datasets[0].data[1]],
          // data: [ data.datasets[0].data[1],data.datasets[0].data[2]],
          backgroundColor: [
            
            data.datasets[0].backgroundColor[5],
            data.datasets[0].backgroundColor[4],
          ],
          hoverOffset: 4
        }
      ]
    };
    data2 = {
      labels: [data.labels[3], data.labels[4]],
      datasets: [
        {
          label: 'My First Dataset',
          // data: [data.datasets[0].data[3], data.datasets[0].data[4]],
          data: [data.datasets[0].data[4], data.datasets[0].data[3]],
          backgroundColor: [
            data.datasets[0].backgroundColor[5],
            data.datasets[0].backgroundColor[4],
          ],
          hoverOffset: 4
        }
      ]
    };
    data3 = {
      labels: ['Labor Sale', 'Parts Sale', 'Total Sale'],
      datasets: [
        {
          label: ['1 Line RO'],
          data: [
            data.datasets[0].data[5],
            data.datasets[0].data[6],
            data.datasets[0].data[7]
          ],
          backgroundColor:
            props.chartId == 1337
              ? [
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4]
                ]
              : props.chartId == 1351
              ? [
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4],
                  data.datasets[0].backgroundColor[4]
                ]
              : [
                  data.datasets[0].backgroundColor[6],
                  data.datasets[0].backgroundColor[6],
                  data.datasets[0].backgroundColor[6]
                ],
          hoverOffset: 4
        },
        {
          label: ['Multi-Line RO'],
          data: [
            data.datasets[0].data[8],
            data.datasets[0].data[9],
            data.datasets[0].data[10]
          ],
          backgroundColor:
            props.chartId == 1337
              ? [
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5]
                ]
              : props.chartId == 1351
              ? [
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5],
                  data.datasets[0].backgroundColor[5]
                ]
              : [
                  data.datasets[0].backgroundColor[7],
                  data.datasets[0].backgroundColor[7],
                  data.datasets[0].backgroundColor[7]
                ],
          hoverOffset: 4
        }
      ]
    };
  } else if (
    props.chartId == 1344 &&
    !lodash.isEmpty(data) &&
    data.labels.length > 0
  ) {
    data1 = {
      data: [
       
        {
          name: 'Repair',
          y: Number(data.datasets[0].data[2])
        },
        {
          name: 'Maint',
          y: Number(data.datasets[0].data[1])
        },
        {
          name: 'Comp',
          y: Number(data.datasets[0].data[0]),
          sliced: true,
          selected: true
        },
      ]
    };
    data2 = {
      data: [
        ['Repair', Number(data.datasets[0].data[5])],
        ['Maint', Number(data.datasets[0].data[4])],
        ['Comp', Number(data.datasets[0].data[3])],

      ]
    };
  } else if (props.chartId == 1346 || props.chartId == 1353) {
    data1 = {
      labels: ['Target Hits', 'Target Misses'],
      datasets: [
        {
          label: 'My First Dataset',
          data: [data.datasets[0].data[2], data.datasets[0].data[1]],
          backgroundColor: [
            data.datasets[0].backgroundColor[1],
            data.datasets[0].backgroundColor[0]
          ],
          hoverOffset: 4
        }
      ]
    };
  }

  const backToKpiScoreCards = chartId => {
    return (
      <Tooltip title={<span style={{ fontSize: 11.5 }}>Return to Top</span>}>
        <IconButton
          size="medium"
          className="infoIcon"
          onClick={() => backToKpi(chartId)}
        >
          <AssignmentReturnOutlinedIcon />
        </IconButton>
      </Tooltip>
    );
  };
  const backToKpi = chartId => {
    var element = document.getElementById('kpiScoreCards');
    element.scrollIntoView({block: "center"});
    // element.scrollIntoView({
    //   behavior: 'smooth',
    //   block: 'center',
    //   inline: 'center'
    // });
    $('#chartContainer_' + chartId).removeClass('selected');
   // $('#visualization_' + chartId).css('display', 'none');
  };

  const options = getChartConfigurationKPIs(props.ROShareData, props.chartId, 'Expanded_View');
  let title = getKpiChartTitle(props.chartId.toString(), (props.chartId == 1353 ? gridTitleParts : gridTitle));

  options.plugins = {
    datalabels: {
      display: true,
      //   color: '#FFF',
      align: 'top',
      anchor: 'end',
      borderRadius: 3,
      font: {
        color: '#737373',
        weight: '600',
        size: window.innerWidth > 2000 ? 18 : 16
      },
      formatter: value => {
        return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
    }
  };
  options.hover = { mode: null };
  const formatChartValues = (value, status) => {
    return value == null
      ? 0
      : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };
  const formatSummaryValues = (value, status) => {
    return value == null
      ? 0
      : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };
  let orderItem =  (props.chartId == 1341) ? 1 : 2;
  return (
    <React.Fragment>
      {(props.chartId == 1341 ||
        props.chartId == 1342 ) && (
        <div>
          <div
            className={classes.mileageChart}
          >
            <Grid xs={12} className={'LaborSale1341'}>
              <div style={{ display: 'flex', justifyContent: props.chartId == 1343 ? 'space-evenly': 'center' }}>
                <div style={{marginLeft: 6, width: '90%'}}>
                  <KPIExtendedSummary props={props} data={data}/>
                </div>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperSale1343
                      : classes.PaperSale1341
                  }
                >
                  <HighchartsReact
                    containerProps={{
                      className: classes.highchartContainer1341
                    }}
                    options={getOptionsForColumnHighcharts(
                      !lodash.isEmpty(data) ? data.datasets[0] : data4,
                      'total',
                      props.chartId,
                      'Expanded_View'
                    )}
                    highcharts={Highcharts}
                  />
                </Paper>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperGpp1343
                      : props.chartId == 1342
                      ? classes.PaperGpp1342
                      : classes.PaperGpp1341
                  }
                >
                  <div className={classes.HeaderComponent1341}>
                    <div>
                      <Typography
                        variant="h6"
                        className={classes.centerText1341}
                      >
                        {props.chartId == 1342 ? 'Parts GP' : 'Labor GP'}
                      </Typography>
                      <Typography
                        variant="h5"
                        className={classes.centerValue1341}
                      >
                        {!lodash.isEmpty(data) && data.labels.length > 0 && data.datasets[0].data[2] != null
                          ? data.datasets[0].data[2] + '%'
                          : 0}
                      </Typography>
                    </div>
                  </div>
                </Paper>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperGp1343
                      : classes.PaperGp1341
                  }
                >
                  <HighchartsReact
                    containerProps={{
                      className: classes.highchartContainer1341
                    }}
                    options={getOptionsForColumnHighcharts(
                      !lodash.isEmpty(data) ? data.datasets[0] : data4,
                      'per ro',
                      props.chartId,
                      'Expanded_View'
                    )}
                    highcharts={Highcharts}
                  />
                </Paper>
              </div>
            </Grid>
          </div>
        </div>
      )}
      {(props.chartId == 1343) && (
        <div>
          <div
            className={ classes.mileageChart1343}
          >
            <Grid xs={12} className={'LaborSale1341'}>
              <div style={{ display: 'flex', justifyContent: props.chartId == 1343 ? 'space-evenly': 'center' }}>
                <div style={{marginLeft: 6, width: '90%'}}>
                  <KPIExtendedSummary props={props} data={data}/>
                </div>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperSale1343
                      : classes.PaperSale1341
                  }
                >
                  <HighchartsReact
                    containerProps={{
                      className: classes.highchartContainer1343
                    }}
                    options={getOptionsForColumnHighcharts(
                      !lodash.isEmpty(data) ? data.datasets[0] : data4,
                      'total',
                      props.chartId,
                      'Expanded_View'
                    )}
                    highcharts={Highcharts}
                  />
                </Paper>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperGpp1343
                      : props.chartId == 1342
                      ? classes.PaperGpp1342
                      : classes.PaperGpp1341
                  }
                >
                  <div className={classes.HeaderComponent1341}>
                    <div>
                      <Typography
                        variant="h6"
                        className={classes.centerText1341}
                      >
                        {/* Gross Profit */}
                        {props.chartId == 1342 ? 'Parts GP' : 'Labor GP'}
                      </Typography>
                      <Typography
                        variant="h5"
                        className={classes.centerValue1341}
                      >
                        {!lodash.isEmpty(data) && data.labels.length > 0 && data.datasets[0].data[2] != null
                          ? data.datasets[0].data[2] + '%'
                          : 0}
                      </Typography>
                    </div>
                  </div>
                  {/* </Grid> */}
                </Paper>
                <Paper
                  className={
                    props.chartId == 1343
                      ? classes.PaperGp1343
                      : classes.PaperGp1341
                  }
                >
                  <HighchartsReact
                    containerProps={{
                      className: classes.highchartContainer1343
                    }}
                    options={getOptionsForColumnHighcharts(
                      !lodash.isEmpty(data) ? data.datasets[0] : data4,
                      'per ro',
                      props.chartId,
                      'Expanded_View'
                    )}
                    highcharts={Highcharts}
                  />
                </Paper>
              </div>
            </Grid>
          </div>
        </div>
      )}
      {(props.chartId == 1339) && (
        <div>
          <div
            className={ classes.mileageChart1343}
          >
            <Grid xs={12} className={'LaborSale1341'}>
              <div style={{ display: 'flex', justifyContent: props.chartId == 1343 ? 'space-evenly': 'center' }}>
                <div style={{marginLeft: 6, width: '90%'}}>
                  <KPIExtendedSummary props={props} data={data}/>
                </div>
                <div className={classes.chartContainer1339}>
                {/* <Paper > */}
                <Grid
                  container
                  className={clsx(
                    classes.container,
                    classes.dataContainer,
                    'kpi-soldhrs-block-extended'
                  )}
                >
                  <div className="flatRateLeftBlock">
                    <DataCard
                      icon={'allsold-hours'}
                      title={'All Sold Hours'}
                      value={
                        !lodash.isEmpty(data) && data.labels.length > 0 && data.datasets[0].data[0] != null
                          ? data.datasets[0].data[0]
                              .toString()
                              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                          : ''
                      }
                      color={'#20B2AA	'}
                      // margin={10}
                      float={'right'}
                    />
                    <DataCard
                      icon={'allsold-hours-perdayavg'}
                      title={'All Sold Hours Per Day Avg'}
                      value={
                        !lodash.isEmpty(data) && data.labels.length > 0 && data.datasets[0].data[1] != null
                          ? data.datasets[0].data[1]
                          : 0
                      }
                      color={'#3CB371	'}
                      margin={6}
                    />
                  </div>
                  
                  {/* <div style={{marginTop: 15, marginLeft: 157}}> */}
                  <div className={'hrsPerRoGrid'}>
                    <DataCard
                      icon={'cphours-perro'}
                      title={'CP Hours Per RO'}
                      value={
                        !lodash.isEmpty(data) && data.labels.length > 0
                          ? (data.datasets[0].data[2] != null ? data.datasets[0].data[2] : 0)
                          : ''
                      }
                      color={'#0B504F'}
                      height={157}
                      margin={-24}
                      marginToph6={10}
                      marginTop={-15}
                      marginTopIcon={'15%'}
                    />
                  </div>
                </Grid>
                {/* </Paper> */}
              </div>
              </div>
            </Grid>
          </div>
        </div>
      )}
      {(props.chartId == 1337 ||
        props.chartId == 1338 ||
        props.chartId == 1351) && (
        <Grid>
          <div style={{display: 'flex',justifyContent: 'space-between'}}>
            <div style={{marginLeft: 6, width: '90%'}}>
              <KPIExtendedSummary props={props} data={data}/>
            </div>
            <Paper  className={classes.PaperLineRo1337}>
            <div
              class="top"
              style={{ display: 'flex', justifyContent: 'space-around' }}
            >
            
              <div style={{ marginLeft: -40 }}>
                <Doughnut
                  data={
                    !lodash.isEmpty(data1) && data1.labels.length > 0
                      ? data1
                      : data4
                  }
                  options={
                    props.chartId == 1337
                      ? getOptionsForPie1(props.LineROLtSixtyK, props.chartId, '', 'Expanded_View')
                      : props.chartId == 1351
                      ? getOptionsForPie1(props.LineRO, props.chartId, '', 'Expanded_View')
                      : getOptionsForPie1(props.LineROGtSixtyK, props.chartId, '', 'Expanded_View')
                  }
                ></Doughnut>
              </div>
              <Paper className={classes.PaperGpp1337}>
                <div className={classes.HeaderComponent1337}>
                    <div>
                      <Typography
                        variant="h6"
                        className={classes.centerText1337}
                      >
                        <span style={{display: 'block'}}>Avg Jobs</span>
                        <span style={{display: 'block'}}>Per Multi</span>
                      </Typography>
                      <Typography
                        variant="h5"
                        className={classes.centerValue1341}
                      >
                        {!lodash.isEmpty(data) && data.labels.length > 0 && data.datasets[0].data[11] != null
                          ? data.datasets[0].data[11]
                          : 0}
                      </Typography>
                    </div>
                  </div>
                </Paper>
              <div style={{ marginLeft: -110 }}>
                <Doughnut
                  data={
                    !lodash.isEmpty(data2) && data2.labels.length > 0
                      ? data2
                      : data4
                  }
                  options={
                    props.chartId == 1337
                      ? getOptionsForPie2(props.LineROLtSixtyK, props.chartId, '', '', 'Expanded_View')
                      : props.chartId == 1351
                      ? getOptionsForPie2(props.LineRO, props.chartId, '', '', 'Expanded_View')
                      : getOptionsForPie2(props.LineROGtSixtyK, props.chartId, '', '', 'Expanded_View')
                  }
                ></Doughnut>
              </div>
            </div>
            <div style={{ marginBottom: 30 }}></div>
            <div>
              <HorizontalBar
              height="200px"
                data={
                  !lodash.isEmpty(data3) && data3.labels.length > 0
                    ? data3
                    : data4
                }
                options={
                  props.chartId == 1337
                    ? getOptionsForPie2(
                        props.LineROLtSixtyK,
                        props.chartId,
                        'bar_chart',
                        '',
                        'Expanded_View'
                      )
                    : props.chartId == 1351
                    ? getOptionsForPie2(
                        props.LineRO,
                        props.chartId,
                        'bar_chart',
                        '',
                        'Expanded_View'
                      )
                    : getOptionsForPie2(
                        props.LineROGtSixtyK,
                        props.chartId,
                        'bar_chart',
                        '',
                        'Expanded_View'
                      )
                }
              ></HorizontalBar>
            </div>
            </Paper>
          </div>
        </Grid>
      )}

      {props.chartId == 1346 && (
        <div style={{display: 'flex',justifyContent: 'space-between', marginTop: 10}}>
          <div style={{marginLeft: 6, width: '90%'}}>
            <KPIExtendedSummary props={props} data={data}/>
          </div>
          <Paper className={classes.kpiLaborGrid}>
            <div className={'kpi-labor-grid_extended'} style={{width: '100% !important'}}>
            
            <div
              className={classes.kpiLaborGridTop}
            >
              <div style={{marginTop: 10}}>
                <Typography
                  variant="h4"
                  color="primary"
                  // style={{ marginLeft: '-14%', fontSize: 25 }}
                  className={classes.valLabelGrid}
                >
                  {props.LaborGrid.length > 0 ? formatChartValues(props.LaborGrid[0].val) : 0}
                </Typography>
                <Typography
                  variant="h6"
                  color="primary"
                  style={{ marginLeft: '-16%' }}
                  className={classes.valLabelGridBottom}
                >
                  {gridTitle+' Repair Target Opportunities'}
                </Typography>
              </div>
            </div>
            <div class="top1353">
              <Doughnut
                id="chart-area-1346" 
                width="610px"
                height="190px"
                data={
                  !lodash.isEmpty(data1) && data1.labels.length > 0
                    ? data1
                    : data4
                }
                plugins={[
                  {
                    afterDraw: chart => {
                      var ctx = chart.chart.ctx;
                      ctx.save();
                      var image = new Image();
                      image.src = '/images/kpis/target1.png';
                      var imageSize = 95;
                      ctx.drawImage(image, 199, 50, imageSize, imageSize);
                      ctx.restore();
                    }
                  }
                ]}
                options={getOptionsForPie1346(
                  props.LaborGrid,
                  props.chartId,
                  session.serviceAdvisor,
                  'Expanded_View'
                )}
              ></Doughnut>
            </div>
            <div
              className={classes.kpiLaborGridBottom}
          //   style={{ marginRight: '2%', marginTop: '-7%', textAlign: 'right' }}
            >
              <Typography
                variant="h6"
                color="primary"
                style={{ marginLeft: '20%' }}
                className={classes.valLabel1346Top}
              >
                {'Non-Compliant'}
              </Typography>
              <Typography
                variant="h4"
                color="primary"
                className={classes.valLabel1346}
              >
                {props.LaborGrid.length > 0 ? (props.LaborGrid[3].val == null ? 0 :props.LaborGrid[3].val) + '%' : 0+'%'}
              </Typography>
            </div>
            </div>
          </Paper>
        </div>
      )}
      {props.chartId == 1353&& (
        <div style={{display: 'flex',justifyContent: 'space-between', marginTop: 10}}>
          <div style={{marginLeft: 6, width: '90%'}}>
            <KPIExtendedSummary props={props} data={data}/>
          </div>
          <Paper className={classes.kpiLaborGrid}>
          <div className={'kpi-parts-grid_extended'}>
          <div
            className={classes.kpiLaborGridTop}
         //   style={{ display: 'flex', justifyContent: 'left' ,marginLeft: '10%'}}
          >
            <div style={{marginTop: 10}}>
              <Typography
                variant="h4"
                color="primary"
                // style={{ marginLeft: '-14%', fontSize: 25 }}
                className={classes.valLabelGrid}
              >
                {props.PartsGrid.length > 0 ? formatChartValues(props.PartsGrid[0].val) : 0}
              </Typography>
              <Typography
                variant="h6"
                color="primary"
                style={{ marginLeft: '-16%' }}
                className={classes.valLabelGridBottom}
              >
                {gridTitleParts+' Parts Target Opportunities'}
              </Typography>
            </div>
            
          </div>
          <div class="top1353">
            <Doughnut
              id="chart-area-1353" 
              width="610px"
              height="190px"
              data={
                !lodash.isEmpty(data1) && data1.labels.length > 0
                  ? data1
                  : data4
              }
              plugins={[
                {
                  afterDatasetsDraw: chart => {
                    var ctx = chart.chart.ctx;
                    ctx.save();
                    var image = new Image();
                    image.src = '/images/kpis/target1.png';
                    // var imageSize = 70;
                    // ctx.drawImage(image, 224, 45, imageSize, imageSize);
                    var imageSize = 95;
                    ctx.drawImage(image, 199, 50, imageSize, imageSize);
                    //  ctx.drawImage(image, 87, 45, imageSize, imageSize);
                    ctx.restore();
                  }
                }
              ]}
              options={getOptionsForPie1346(
                props.PartsGrid,
                props.chartId,
                session.serviceAdvisor,
                'Expanded_View'
              )}
            ></Doughnut>
          </div>
          <div
            className={classes.kpiLaborGridBottom}
        //    style={{ marginRight: '2%', marginTop: '-7%', textAlign: 'right' }}
          >
            <Typography
              variant="h6"
              color="primary"
              style={{ marginLeft: '20%' }}
              className={classes.valLabel1346Top}
            >
              {'Non-Compliant'}
            </Typography>
            <Typography
              variant="h4"
              color="primary"
              className={classes.valLabel1346}
            >
              {props.PartsGrid.length > 0 ? (props.PartsGrid[3].val == null ? 0 : props.PartsGrid[3].val) + '%' : 0+'%'}
            </Typography>
          </div>
        </div>
       </Paper>
        </div>
      )}

      {(props.chartId == 1336) && (
        <div style={{display: 'flex', justifyContent: 'space-between', marginTop: 10}}>
          <div style={{marginLeft: 6, width: '90%'}}>
            <KPIExtendedSummary props={props} data={data}/>
          </div>
          <Paper className={(props.ROShareData.length > 0 && (props.ROShareData[2].val == 1 || props.ROShareData[1].val == null ))? classes.kpi1336TotalRo : classes.kpi1336} >
          <div
            class="top"
            //className={classes.parentContainer1336}
            className={(props.ROShareData.length > 0 && (props.ROShareData[2].val == 1 || props.ROShareData[1].val == null)) ? classes.parentContainer1336_single : classes.parentContainer1336}

          ///  style={{width: (props.ROShareData.length > 0 && (props.ROShareData[2].val == 1 || props.ROShareData[1].val == null)) ? '100%' : '', display: 'flex',marginRight: 6}}
          //style={{ display: 'flex', justifyContent: 'space-around', marginTop: 8 }}
          >
            <div className="pie13361" style={{ width: (props.ROShareData.length > 0 && (props.ROShareData[2].val == 1 || props.ROShareData[1].val == null)) ? '100%' : '50%' }}>
              <Pie
                data={
                  !lodash.isEmpty(data1) && data1.labels.length > 0
                    ? data1
                    : data
                }
                options={getOptionsForPie1(
                  props.ROShareData,
                  1336,
                  session.serviceAdvisor,
                  'Expanded_View'
                )}
              ></Pie>
            </div>
            <div
              className={
                (props.ROShareData.length > 0 && (props.ROShareData[2].val == 1 || props.ROShareData[1].val == null) )
                  ? classes.pie13363
                  : 'pie13362'
              }
              style={{ width: '50%' }}
            >
              <Pie
                data={
                  !lodash.isEmpty(data2) && data2.labels.length > 0
                    ? data2
                    : data
                }
                options={getOptionsForPie2(
                  props.ROShareData,
                  1336,
                  '',
                  session.serviceAdvisor,
                  'Expanded_View'
                )}
              ></Pie>
            </div>
          </div>
          </Paper>
        </div>
      )}
      {(props.chartId == 1335) && (
           <div style={{display: 'flex', justifyContent: 'space-between', marginTop: 10}}>
            <div style={{marginLeft: 6, width: '90%'}}>
              <KPIExtendedSummary props={props} data={data}/>
            </div>
            <Paper className={classes.parentContainer1335} >

           <div className={classes.chartContainer1335}>
             <Bar data={data} options={options}></Bar>
           </div>
           </Paper>
         </div>
      )} 
      {/* {(props.chartId == 1344) && (
          <div style={{display: 'flex', justifyContent: 'space-between', marginTop: 10}}>
            <div style={{marginLeft: 6, width: '90%'}}>
              <KPIExtendedSummary props={props} data={data}/>
            </div>
          <Paper className={classes.parentContainer1344}>
          <div class="top" className={classes.parentFullContainer1344}>
            <div style={{ marginLeft: '29%' }}>
              <HighchartsReact
                containerProps={{ style: { height: 180, width: 300 } }}
                options={getOptionsForHighchartsPie(
                  !lodash.isEmpty(data1) ? data1.data : data4,
                  '',
                  'Expanded_View'
                )}
                highcharts={Highcharts}
              />
            </div>
            <div></div>
            <div style={{ marginLeft: '2%', marginTop: '0%' }}>
              <HighchartsReact
                containerProps={{ style: { height: 180, width: 500 } }}
                options={getOptionsForHighChart(
                  !lodash.isEmpty(data2) ? data2.data : data4,
                  'Expanded_View'
                )}
                highcharts={Highcharts}
              />
            </div>
          </div>
          </Paper>
        </div>
      )} */}
       {(props.chartId == 1344) && (
          <div style={{display: 'flex', justifyContent: 'space-between', marginTop: 10}}>
            <div style={{marginLeft: 6, width: '90%'}}>
              <KPIExtendedSummary props={props} data={data}/>
            </div>
          <Paper className={classes.parentContainer1344}>
          <div class="top">
            <div className={classes.pieContainer1344}>
              <HighchartsReact
                containerProps={{ style: { height: 180, width: 300 } }}
                options={getOptionsForHighchartsPie(
                  !lodash.isEmpty(data1) ? data1.data : data4, '',
                  'Expanded_View'
                )}
                highcharts={Highcharts}
              />
            </div>
            <div></div>
            <div className={classes.pyramidContainer1344} >
              <HighchartsReact
                containerProps={{ style: { height: 180, width: 500 } }}
                options={getOptionsForHighChart(
                  !lodash.isEmpty(data2) ? data2.data : data4,
                  'Expanded_View'
                )}
                highcharts={Highcharts}
              />
            </div>
          </div>
        </Paper>
        </div>
      )}
      {(props.chartId == 1340) && (
        <div style={{display: 'flex', justifyContent: 'space-between', marginTop: 10}}>
          <div style={{marginLeft: 6, width: '90%'}}>
            <KPIExtendedSummary props={props} data={data}/>
          </div>
          <Paper className={classes.mileAgeChart} >
          <div className={'top'} style={{ fontFamily: 'Roboto', width: '100%' }}>
            <div className={classes.container1340_1}>
              <ReactSpeedometer
                minValue={0}
                maxValue={100000}
                width={window.innerWidth > 2000 ? 400 : 300}
                height={window.innerWidth > 2000 ? 400 : 300}
                paddingHorizontal={window.innerWidth > 2000 ? 17 : 0}
                paddingVertical={window.innerWidth > 2000 ? 17 : 0}
                labelFontSize={window.innerWidth > 2000 ? '20px' : '14px'}
                valueTextFontSize={window.innerWidth > 2000 ? '25px' : '22px'}
                fontFamily={[
                  'Roboto',
                  'Helvetica',
                  'Arial',
                  'sans - serif'
                ].join(',')}
                segments={4}
                ringWidth={70}
                segmentColors={['#008000', '#7ab55c', '#ec5555', '#b81414']}
                valueFormat={'~s'}
                currentValueText={
                  !lodash.isEmpty(data) && data.labels.length > 0 && data.datasets[0].data[1] != null
                    ? Math.round(data.datasets[0].data[0]) + ' Yrs' +' / '+ data.datasets[0].data[1]
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' Mi'
                    : ''
                }
                value={
                  !lodash.isEmpty(data) && data.labels.length > 0
                    ? data.datasets[0].data[1]
                    : 0
                }
                // valueFormat={number => number+'%'}
              />
            </div>
            {/* <div></div>
            <div className={'container1340Linear'}>
              <HighchartsReact
                options={getOptionsForHighchartsLinearGauge(
                  !lodash.isEmpty(data) && data.labels.length > 0
                    ? data.datasets[0].data[0]
                    : data4, 'extended_kpi'
                )}
                highcharts={Highcharts}
              />
            </div> */}
          </div>
          </Paper>
        </div>
      )}
      {(props.chartId == 13401) && (
        <div style={{display: 'flex', justifyContent: 'space-between', marginTop: 10}}>
          <div style={{marginLeft: 6, width: '90%'}}>
            <KPIExtendedSummary props={props} data={data}/>
          </div>
          <Paper className={classes.mileAgeChart} >
          <div className={'top'} style={{ fontFamily: 'Roboto', width: '100%' }}>
            <div className={classes.container1340_1}>
              <ReactSpeedometer
                minValue={0}
                maxValue={100000}
                width={400}
                height={400}
                labelFontSize={window.innerWidth > 2000 ? '16px' : '14px'}
                valueTextFontSize={window.innerWidth > 2000 ? '20px' : '22px'}
                fontFamily={[
                  'Roboto',
                  'Helvetica',
                  'Arial',
                  'sans - serif'
                ].join(',')}
                segments={4}
                ringWidth={70}
                segmentColors={['#008000', '#7ab55c', '#ec5555', '#b81414']}
                valueFormat={'~s'}
                currentValueText={
                  !lodash.isEmpty(data) && data.labels.length > 0 && data.datasets[0].data[1] != null
                    ? Math.round(data.datasets[0].data[0]) + ' Yrs' +' / '+ data.datasets[0].data[1]
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' Mi'
                    : ''
                }
                value={
                  !lodash.isEmpty(data) && data.labels.length > 0
                    ? data.datasets[0].data[1]
                    : 0
                }
                // valueFormat={number => number+'%'}
              />
            </div>
            <div></div>
            {/* <div className={'container1340Linear'}>
              <HighchartsReact
                options={getOptionsForHighchartsLinearGauge(
                  !lodash.isEmpty(data) && data.labels.length > 0
                    ? data.datasets[0].data[0]
                    : data4, 'extended_kpi'
                )}
                highcharts={Highcharts}
              />
            </div> */}
          </div>
          </Paper>
        </div>
      )}
    </React.Fragment>
  );
};
export default KPISummaryChartsExtended;
