import { QueryRenderer } from '@cubejs-client/react';
import {
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider
} from '@material-ui/core';
import moment from 'moment';
import React, { useState } from 'react';
import { Bar } from 'react-chartjs-2';
import cubejsApi from 'src/utils/cubeUtils';
import MoreActions from '../MoreActions';
import { useHistory } from 'react-router';
import { getChartConfiguration, getDataforLineChart } from '../../utils/Utils';
const dateFormatter = item => moment(item).format('MMM');
const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <CircularProgress />;

const DeltaTechFlatHours = () => {
  const history = useHistory();
  const [queryState, queryChangedState] = useState({
    measures: ['DeltaTechFlatHours.laboractualhours_flatratehours'],
    timeDimensions: [
      {
        dimension: 'DeltaTechFlatHours.ro_date',
        dateRange: 'Last 13 months'
      }
    ],
    dimensions: ['DeltaTechFlatHours.ro_date'],
    filters: [],
    order: {
      'DeltaTechFlatHours.ro_date': 'asc'
    }
  });
  const COLORS_SERIES = ['#FFDE59'];

  const barRender = ({ resultSet }) => {
    const data = getDataforLineChart(resultSet, 16);
    const options = getChartConfiguration(16, [0, 200]);
    const result = (
      <Card bordered={false} style={{ height: '100%' }}>
        <CardHeader
          title="Delta - Labor Tech Hours And Flat Rate Hours"
          action={
            <MoreActions setActions={setactions} chartId={957}></MoreActions>
          }
        ></CardHeader>

        <Divider />
        <CardContent>
          <Bar data={data} options={options}
            getElementAtEvent={dataset => {
              if (dataset.length > 0) {
                var dataIndex = dataset[0]._datasetIndex;
                var valueIndex = dataset[0]._index;
                var xAxis = data.datasets[dataIndex].data[valueIndex];
                var yAxis = data.labels[valueIndex];
                history.push({
                  pathname: '/AnalyzeData',
                  prevPath:window.location.pathname,
                  state: {
                    chartId: 957,
                    x: xAxis,
                    y: yAxis,
                    drillDown: 22,
                    chartName: "Delta - Labor Tech Hours And Flat Rate Hours",
                    category: 1,
                    title: 'DTFH'
                  }
                });
              }
            }}
          />
        </CardContent>
      </Card>
    );

    return result;
  };

  const setactions = val => {
    if (val === 1) {
      queryChangedState(prevState => ({
        ...prevState,
        timeDimensions: [
          {
            dimension: 'DeltaTechFlatHours.ro_date',
            dateRange: 'Last 6 months'
          }
        ]
      }));
    }
    if (val === 2) {
      queryChangedState(prevState => ({
        ...prevState,
        timeDimensions: [
          {
            dimension: 'DeltaTechFlatHours.ro_date',
            dateRange: 'Last 13 months'
          }
        ]
      }));
    }
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=DTFH?chartId=957'
      });
    }
  };

  return (
    <QueryRenderer
      query={queryState}
      cubejsApi={cubejsApi()}
      render={renderChart(barRender)}
    />
  );
};

export default DeltaTechFlatHours;
