import { Query<PERSON>enderer } from '@cubejs-client/react';
import {
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider,
  IconButton
} from '@material-ui/core';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import moment from 'moment';
import React from 'react';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton.js';
import { getStoreIdFilter } from 'src/components/ViewGraphDetailsAction';

const dateFormatter = item => moment(item).format('MMM YY');

function CustomTooltip({ payload, label, active }) {
  if (active) {
    return (
      <div>
        <p>{`${label} : ${payload[0].value}`}</p>
      </div>
    );
  }

  return null;
}

const CartesianChart = ({ resultSet, children, ChartComponent }) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <ChartComponent data={resultSet.tablePivot()}>
        //
        <Tooltip />
        <Tooltip content={<CustomTooltip />} />
        <XAxis dataKey="ElrMovingAverage.currentGroup" type="number" />
        <YAxis
          dataKey="ElrMovingAverage.moving_elr"
          type="number"
          domain={[0, 250]}
        />
        <ReferenceLine
          y={resultSet.tablePivot()[0]['ElrMovingAverage.average']}
          label={
            'AVG :' + resultSet.tablePivot()[0]['ElrMovingAverage.average']
          }
          stroke="red"
        />
        <CartesianGrid />
        {children}
        <Legend />
      </ChartComponent>
    </ResponsiveContainer>
  );
};

const colors = ['#5F04B4', '#141446', '#7A77FF'];

const lineRender = ({ resultSet }) => (
  <Card bordered={false} style={{ height: '100%' }}>
    <CardHeader
      title="ELR Moving Average"
      action={
        <IconButton aria-label="settings">
          <MoreVertIcon />
        </IconButton>
      }
    ></CardHeader>

    <Divider />
    <CardContent>
      <CartesianChart resultSet={resultSet} ChartComponent={LineChart}>
        {resultSet.seriesNames().map((series, i) => (
          <Line
            key={series.key}
            dataKey={series.key}
            name={series.title}
            stroke={colors[i]}
            dot={null}
          />
        ))}
      </CartesianChart>
    </CardContent>
  </Card>
);

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;

const ELRMovingAverage = () => (
  <QueryRenderer
    query={{
      measures: ['ElrMovingAverage.moving_elr'],
      timeDimensions: [],
      dimensions: ['ElrMovingAverage.currentGroup', 'ElrMovingAverage.average'],
      filters: getStoreIdFilter('ElrMovingAverage', JSON.parse(localStorage.getItem('selectedStoreId'))[0]),
      order: {
        'ElrMovingAverage.currentGroup': 'asc'
      }
    }}
    cubejsApi={cubejsApi()}
    render={renderChart(lineRender)}
  />
);

export default ELRMovingAverage;
