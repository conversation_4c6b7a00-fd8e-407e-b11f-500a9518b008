import { QueryRenderer } from '@cubejs-client/react';
import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import moment from 'moment';
import React from 'react';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton';
import MoreActions from '../MoreActions';

const dateFormatter = item => moment(item).format('MMM YY');
const xAxisFormatter = item => {
  if (moment(item).isValid()) {
    return dateFormatter(item);
  } else {
    return item;
  }
};

const CartesianChart = ({ resultSet, children, ChartComponent }) => (
  <ResponsiveContainer width="100%" height={300}>
    <ChartComponent data={resultSet.chartPivot()}>
      <Tooltip labelFormatter={dateFormatter} />
      <XAxis dataKey="x" tickFormatter={xAxisFormatter} />
      <YAxis />
      <CartesianGrid />
      {children}
      <Legend />
    </ChartComponent>
  </ResponsiveContainer>
);

const colors = ['#1890ff', '#141446', '#7A77FF'];

const lineRenderer = ({ resultSet }) => (
  <Card bordered={false} style={{ height: '100%' }}>
    <CardHeader
      title="CP Effective Labor Rate*"
      action={<MoreActions></MoreActions>}
    ></CardHeader>

    <Divider />
    <CardContent>
      <CartesianChart resultSet={resultSet} ChartComponent={LineChart}>
        {resultSet.seriesNames().map((series, i) => (
          <Line
            key={series.key}
            dataKey={series.key}
            name={series.title}
            stroke={colors[i]}
          />
        ))}
      </CartesianChart>
    </CardContent>
  </Card>
);

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;

const CPEffectivelabourRate = () => (
  <QueryRenderer
    query={{
      measures: ['CPEffectiveLabourRate.effective_labour_rate'],
      timeDimensions: [
        {
          dimension: 'CPEffectiveLabourRate.ro_date',
          dateRange: 'Last year'
        }
      ],
      dimensions: ['CPEffectiveLabourRate.ro_date'],
      filters: [],
      order: {
        'CPEffectiveLabourRate.ro_date': 'asc'
      }
    }}
    cubejsApi={cubejsApi()}
    render={renderChart(lineRenderer)}
  />
);

export default CPEffectivelabourRate;
