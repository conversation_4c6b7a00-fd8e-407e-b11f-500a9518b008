import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { useHistory } from 'react-router';
import {
  getChartDataIndex,
  getChartDrillDown,
  getChartName,
  getDataforHasuraBarChart,
  getDataforPostgraphileCharts,
  getBarChartConfiguration,
  getChartPayType,
  getChartOpCategory,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import MoreActions from '../MoreActions';
import ChartDialog from 'src/components/Dialog';
import { CircularProgress, Grid } from '@material-ui/core';
import { useSelector, useDispatch } from 'react-redux';
import { withStyles } from '@material-ui/core/styles';
import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles(theme => ({
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));
const HasuraDashboardBarRenderer = ({
  chartId,
  yAxisRanges,
  title,
  isStacked,
  resultSet,
  searchText,
  setActions,
  showTopBar,
  isFromMetrices,
  isFrom,
  parentId,
  realm,
  handleClose,
  type,
  serviceAdvisors,
  backTobutton,
  handleHighlight,
  selected,
  selectedChartId,
  removeFavourite,
  headerClick,
  loader,
  dashboard,
  barType
}) => {
  const history = useHistory();
  const [loader1, setLoader1] = useState(false);
  const [open, setOpen] = useState(false);
  const [hasuraData, setHasuraData] = useState([]);
  const session = useSelector(state => state.session);
  // const [type, setType] = useState('');

  // An unwanted useEffect. setHasuraData is used to set state but hasuraData is not used anywhere.
  // useEffect(() => {
  //   console.log("inner useEffect",chartId);
  //   getChartsDataFromViews(session.serviceAdvisor, chartId, callback => {
  //     if (callback) {
  //       setHasuraData(JSON.parse(callback[0].jsonData));
  //     }
  //   });
  // }, [session.serviceAdvisor]);

  const data =
    dashboard == 'CP Overview' ||
    dashboard == 'CP Labor Overview' ||
    dashboard == 'CP Parts Overview' ||
    dashboard == 'Special Metrics' ||
    dashboard == 'Discounts'
      ? getDataforPostgraphileCharts(resultSet, chartId)
      : getDataforHasuraBarChart(resultSet, chartId);
  const options = getBarChartConfiguration(chartId, undefined, isStacked);
  const classes = useStyles();

  const chartPopup = val => {
    localStorage.setItem('popup', true);
    // handleChartPopup(val);
    setOpen(true);
    // setType('popup');
  };
  const handleCloses = () => {
    localStorage.setItem('popup', false);
    setOpen(false);
    handleClose(1);
  };
  const handleBackToButton = chartId => {
    backTobutton(chartId);
  };
  // if (chartId == 938) {
  //   console.log('typessss=', type, loader, data);
  // }
  return loader == true ? (
    <Grid justify="center" className={classes.loaderGrid}>
      <CircularProgress size={60} />
    </Grid>
  ) : (
    <Card
      bordered={false}
      style={{ height: '100%', borderRadius: 0, border: '1px solid #003d6b' }}
    >
      <CardHeader
        title={title}
        action={
          showTopBar === true ? (
            <MoreActions
              // removeFavourite={''}
              setActions={setActions}
              chartId={chartId}
              type={type}
              chartPopup={chartPopup}
              handleClose={handleCloses}
              removeFavourite={removeFavourite}
              handleBackToButton={handleBackToButton}
              handleHighlight={handleHighlight}
            ></MoreActions>
          ) : null
        }
        subheader={getSubHeader(chartId)}
        style={{ borderBottom: '1px solid #003d6b' }}
        onClick={() =>
          typeof headerClick === 'function' && headerClick(chartId)
        }
        id={'card-header-' + chartId}
      ></CardHeader>

      <Divider />
      <CardContent style={{ cursor: 'pointer', height: '85%' }}>
        <Bar
          data={data}
          options={options}
          getElementAtEvent={dataset => {
            if (dataset.length > 0) {
              var dataIndex = dataset[0]._datasetIndex;
              var valueIndex = dataset[0]._index;
              var xAxis = data.datasets[dataIndex].data[valueIndex];
              var yAxis = data.labels[valueIndex];
              // var label = data.datasets[dataIndex].label.trim();
              var label = data.datasets[dataIndex].label[0];

              if (
                chartId == 1091 ||
                chartId == 1092 ||
                chartId == 1093 ||
                chartId == 1094
              ) {
                // var splittedMonth = yAxis.split('-', 2);
                // var yearMonth =
                //   data.datasets[dataIndex].label + '-' + splittedMonth[1];
                var yearMonth = yAxis.substring(0, 7);
              }

              history.push({
                pathname: '/AnalyzeData',
                search:
                  Number(chartId) === 948 ||
                  Number(chartId) === 930 ||
                  Number(chartId) === 923 ||
                  Number(chartId) === 1355 ||
                  Number(chartId) === 935 ||
                  Number(chartId) === 936 ||
                  Number(chartId) === 1357 ||
                  Number(chartId) === 938 ||
                  Number(chartId) === 1174 ||
                  Number(chartId) === 1175 ||
                  Number(parentId) === 916 ||
                  Number(parentId) === 940 ||
                  Number(parentId) === 946 ||
                  Number(parentId) === 1127 ||
                  Number(parentId) === 1044 ||
                  Number(parentId) === 955 ||
                  Number(parentId) === 1318 ||
                  Number(parentId) === 953 ||
                  Number(parentId) == 942 ||
                  Number(parentId) == 939 ||
                  Number(parentId) == 940 ||
                  Number(parentId) == 1238 ||
                  Number(parentId) == 920 ||
                  Number(parentId) == 946 ||
                  Number(parentId) == 960 ||
                  Number(parentId) == 944 ||
                  Number(parentId) == 1073 ||
                  Number(parentId) == 1133 ||
                  Number(parentId) == 1098 ||
                  Number(parentId) == 1356 ||
                  Number(parentId) == 918 ||
                  Number(parentId) == 1138 ||
                  Number(parentId) == 1049 ||
                  Number(parentId) == 952 ||
                  Number(parentId) == 966 ||
                  Number(parentId) == 1143 ||
                  Number(parentId) == 1326 ||
                  Number(parentId) == 1334
                    ? '?chartId=' + 'drillDown'
                    : '?chartId=' + chartId,
                prevPath:
                  window.location.pathname === '/GraphDetailsView'
                    ? Number(chartId) === 1234 ||
                      Number(chartId) === 1235 ||
                      Number(chartId) === 1233 ||
                      Number(chartId) === 1236 ||
                      Number(chartId) === 1237 ||
                      Number(chartId) === 1360 ||
                      Number(chartId) === 1361 ||
                      Number(chartId) === 1362 ||
                      Number(chartId) === 1359 ||
                      (serviceAdvisors && !serviceAdvisors.includes('All')) ||
                      dashboard == 'CP Overview' ||
                      dashboard == 'CP Labor Overview' ||
                      dashboard == 'CP Parts Overview'
                      ? window.location.pathname + '?chartId=' + parentId
                      : window.location.pathname + '?chartId=' + chartId
                    : window.location.pathname,
                state: {
                  chartId: chartId,
                  x: xAxis,
                  y: typeof yearMonth != 'undefined' ? yearMonth : yAxis,
                  drillDown: getChartDrillDown(chartId, searchText),
                  chartName: getChartName(chartId)
                    ? getChartName(chartId)
                    : title,
                  category: getChartDataIndex(
                    chartId,
                    dataIndex,
                    isFrom,
                    label
                  ),
                  payType: getChartPayType(chartId),
                  opCategory: getChartOpCategory(chartId),
                  titleCategory: searchText,
                  isFromMetrices: isFromMetrices,
                  parentId: parentId,
                  prevPath:
                    window.location.pathname === '/GraphDetailsView'
                      ? Number(chartId) === 1234 ||
                        Number(chartId) === 1235 ||
                        Number(chartId) === 1233 ||
                        Number(chartId) === 1236 ||
                        Number(chartId) === 1237 ||
                        (serviceAdvisors && !serviceAdvisors.includes('All')) ||
                        dashboard == 'CP Overview' ||
                        dashboard == 'CP Labor Overview' ||
                        dashboard == 'CP Parts Overview'
                        ? window.location.pathname + '?chartId=' + parentId
                        : window.location.pathname + '?chartId=' + chartId
                      : window.location.pathname
                },
                handleHighlight: selectedChartId,
                selectedGrid: selected
              });
            }
          }}
        />
      </CardContent>
      <ChartDialog
        open={open}
        chartId={chartId}
        realm={realm}
        chartType="barChartRenderer"
        handlePopupClose={handleCloses}
        headerClick={headerClick}
        selected={selected}
        selectedChartId={selectedChartId}
        handleHighlight={handleHighlight}
        dashboard={dashboard}
      />
    </Card>
  );
};

export default HasuraDashboardBarRenderer;
