import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  Di<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>con<PERSON>utt<PERSON>,
  But<PERSON>
} from '@material-ui/core';
import EditIcon from '@material-ui/icons/Edit';
import { makeStyles } from '@material-ui/styles';
import React, { useEffect, useState } from 'react';
import PerfectScrollbar from 'react-perfect-scrollbar';
import 'react-perfect-scrollbar/dist/css/styles.css';
import TextareaAutosize from '@material-ui/core/TextareaAutosize';
import CloseIcon from '@material-ui/icons/Close';
import Collapse from '@material-ui/core/Collapse';
import TextField from '@material-ui/core/TextField';
import CardActions from '@material-ui/core/CardActions';
import Input from '@material-ui/core/Input';
import Alert from '@material-ui/lab/Alert';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import { getChartMarkdown } from 'src/utils/hasuraServices';
import { faCloudShowersHeavy } from '@fortawesome/free-solid-svg-icons';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { UPDATE_CHART_MASTER_MARKDOWN } from 'src/graphql/queries';
import { TrendingUpTwoTone } from '@material-ui/icons';
import { object } from 'prop-types';
import { withKeycloak } from '@react-keycloak/web';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
const useStyles = makeStyles(theme => ({
  overView: {
    height: '100%',
    flexDirection: 'column',
    padding: 0
  },
  calculation: {
    height: '100%',
    flexDirection: 'column',
    padding: 0
  },
  calculationDetails: {
    padding: 3
  },
  calcRules: {
    // lineHeight: 2,
    paddingLeft: 10,
    marginBottom: 20,
    fontFamily: 'Roboto',
    fontSize: 12,
    marginTop: 10
  },
  cardContent: {
    textAlign: 'left'
    //height: '80%'
  },
  cardRoot: {
    borderRadius: '5px',
    border: '1px solid #003d6b',
    height: '100%'
  },
  cardRootUpdated: {
    borderRadius: 0,
    border: '1px solid #003d6b',
    overflowY: 'auto',

    position: 'absolute',

    '@media (max-width: 1920px)': {
      width: '39.3%',
      height: '48%'
    },
    '@media (max-width: 1440px)': {
      width: '38%',
      height: '39.5%'
    },
    '@media (max-width: 1280px)': {
      width: '37.5%',
      height: '35.5%'
    },
    '@media (min-width: 2304px)': {
      width: '43.5%',
      height: '37%'
    }
  }
}));

const getChartDetails = (title, flag, realm) => {
  var serviceAdvisorText = 'month';
  var paytypeText = '';
  if (flag == 1 && title != 1316 && title != 1317) {
    serviceAdvisorText = 'month for the selected Service Advisor/s';
  } else if (flag == 1 && (title == 1316 || title == 1317)) {
    serviceAdvisorText = 'for the selected service advisors';
  }
  if (realm == 'haleyag') {
    paytypeText =
      'The detailed graphs shows the Revenue from Shop Supplies for each of the pay types - Customer Pay and Internal.';
  }
  var graphDetail = '';
  switch (title) {
    case '942':
      graphDetail = {
        title: 'CP Revenue',
        Overview:
          'The graph shows the revenue for Labor and Parts respectively, grouped by ' +
          serviceAdvisorText +
          ' for Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Revenue  =  Sum of Labor Sale<br>' +
          'Parts Revenue  =  Sum of Parts Extended Sale<br>' +
          'Combined Revenue = Sum of Labor Sale + Sum of Parts Extended Sale<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '939':
      graphDetail = {
        title: 'CP Gross Profit',
        Overview:
          'The graph shows the gross profit for Labor and Parts grouped by ' +
          serviceAdvisorText +
          ' for Customer Pay Repair Orders only',
        Calculation:
          'Labor Gross Profit   = Labor Sale - Labor cost<br>' +
          'Parts Gross Profit   =  Parts Extended Sale - Parts Extended Cost<br>' +
          'Gross Profit Combined = (Labor Sale  + Parts Extended Sale) - (Labor Cost + Parts Extended Cost)<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '940':
      graphDetail = {
        title: 'Labor Gross Profit Percentage',
        Overview:
          'The graph shows the Labor Gross Profit % and Parts Gross Profit % grouped by ' +
          serviceAdvisorText +
          '.',
        Calculation:
          'Labor Gross Profit Percentage = ((Labor sale - Labor cost)/ Labor Sale)*100<br>' +
          'Parts Gross Profit Percentage = ((Parts Sale - Parts Cost)/ Parts Sale)*100<br>' +
          'Combined Gross Profit Percentage = (((Parts Sale+Labor sale) - (Parts Cost+Labor cost))/ (Parts Sale+Labor sale))*100<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '920':
      graphDetail = {
        title: 'Labor Sold Hours',
        Overview:
          'The main graph shows total hours sold for Labor for Customer Pay Repair Orders. In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          'Labor Sold Hours - Combined: All Pay types  - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.<br>' +
          'Labor Sold Hours - Customer Pay: This graph shows the Labor Sold Hours for Customer Pay Repair Orders.<br>' +
          'Labor Sold Hours - Warranty: This graph shows the Labor Sold Hours for Warranty Repair Orders.<br>' +
          'Labor Sold Hours - Internal: This graph shows the Labor Sold Hours for Internal Repair Orders.<br>' +
          'Labor Sold Hours - Maintenance Plan: This graph shows the Labor Sold Hours for Repair Orders on the Maintenance Plan.<br>' +
          'Labor Sold Hours - Factory Service Contract: This graph shows the Labor Sold Hours for Repair Orders on the Factory Service Contract.<br>' +
          'Labor Sold Hours - Extended Service Contract: This graph shows the Labor Sold Hours for Repair Orders on the Extended Service Contract.',
        Calculation:
          'Labor Sold Hours: Total Hours Spent on Labor grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.<br>'
      };
      return graphDetail;
    case '946':
      graphDetail = {
        title: 'Effective Labor Rate',
        Overview:
          'The main graph shows the Labor Rates grouped by ' +
          serviceAdvisorText +
          ' for Customer Pay Repair Orders.<br>' +
          'The detailed graphs represent the Labor Rates for each of the Pay types - Customer Pay, Warranty, Internal, Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Effective Labor Rate = Sum of Labor Sale / Sum of Labor Sold Hours for each Pay type.<br> ' +
          'All Op Categories - Repair, Maintenance and Competitive included.'
      };
      return graphDetail;
    case '1138':
      graphDetail = {
        title: 'Repair Order Count',
        Overview:
          'The main graph shows the Total Count of Customer Pay Repair Orders over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '. <br>' +
          ' In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          'Repair Order Count - Combined: Repair Orders that are paid with any of the  means of Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract.<br>' +
          'Repair Order Count - Customer Pay: Repair Orders with Customer Pay.<br>' +
          'Repair Order Count - Warranty: Repair Orders that are covered under Warranty.<br>' +
          'Repair Order Count - Internal: Repair Orders with Internal pay.<br>' +
          'Repair Order Count - Maintenance Plan: Repair Orders that are covered under Maintenance Plan.<br>' +
          'Repair Order Count - Factory Service Contract: Repair Orders that are covered under Factory Service Contract.<br>' +
          'Repair Order Count - Extended Service Contract: Repair Orders that are covered under Extended Service Contract.',
        Calculation:
          'Repair Order Count = count of all Repair Orders grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered for these graphs'
      };
      return graphDetail;

    case '960':
      graphDetail = {
        title: 'Labor Revenue',
        Overview:
          'The main graph shows the Labor Revenue for Customer Pay Repair orders over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed view shows the Labor Revenue for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Labor Revenue = Sum of Labor Sale grouped by month. <br>' +
          'All Op Categories - Repair, Maintenance, Competitive and Shop Supplies included.'
      };
      return graphDetail;
    case '944':
      graphDetail = {
        title: 'Labor Gross Profit',
        Overview:
          'The main graph shows the Gross Profit from Labor for Customer Pay Repair Orders on a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed view shows the Gross Profit from Labor for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.<br>' +
          'Labor Gross Profit - Combined: All Paytypes - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.<br>' +
          'Labor Gross Profit - Customer Pay: Customer Pay Repair Orders only.<br>' +
          'Labor Gross Profit - Warranty: Warranty Repair Orders only.<br>' +
          'Labor Gross Profit - Internal: Internal Repair Orders only.<br>' +
          'Labor Gross Profit - Maintenance Plan: Maintenance Repair Orders only.<br>' +
          'Labor Gross Profit - Factory Service Contract: Repair Orders on the Factory Service Contract.<br>' +
          'Labor Gross Profit - Extended Service Contract: Repair Orders on the Extended Service Contract',

        Calculation:
          'Labor Gross Profit = Sum of Labor Sale - Sum of Labor Cost<br>' +
          'All the Opcode categories - Repair, Maintenance & Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '1073':
      graphDetail = {
        title: 'Labor Gross Profit Percentage',
        Overview:
          ' The graph shows the Labor Gross Profit % for Customer Pay Repair Orders grouped by ' +
          serviceAdvisorText +
          ' on a 3 year trend.',
        Calculation:
          'Labor Gross Profit %  = ((Labor Sale - Labor Cost)/ Labor Sale)*100<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '1133':
      graphDetail = {
        title: 'Labor Sold Hours',
        Overview:
          'The main graph shows total hours sold for Labor for Customer Pay Repair Orders over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '. <br>In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          'Labor Sold Hours - Combined: All Pay types  - Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.<br>' +
          'Labor Sold Hours - Customer Pay: This graph shows the Labor Sold Hours for Customer Pay Repair Orders.<br>' +
          'Labor Sold Hours - Warranty: This graph shows the Labor Sold Hours for Warranty Repair Orders.<br>' +
          'Labor Sold Hours - Internal: This graph shows the Labor Sold Hours for Internal Repair Orders.<br>' +
          'Labor Sold Hours - Maintenance Plan: This graph shows the Labor Sold Hours for Repair Orders on the Maintenance Plan.<br>' +
          'Labor Sold Hours - Factory Service Contract: This graph shows the Labor Sold Hours for Repair Orders on the Factory Service Contract.<br>' +
          'Labor Sold Hours - Extended Service Contract: This graph shows the Labor Sold Hours for Repair Orders on the Extended Service Contract.',
        Calculation:
          'Labor Sold Hours =  Sum of Hours Sold grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '956':
      graphDetail = {
        title: 'CP Labor Tech Hours',
        Overview:
          'The graph shows the actual hours worked/punched by the techinicians on Customer Pay jobs on a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.',
        Calculation:
          ' CP Labor Tech Hours = Sum of Actual Hours worked/punched by Technicians grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '1127':
      graphDetail = {
        title: 'Effective Labor Rate',
        Overview:
          'The main graph shows the Labor Rates grouped by ' +
          serviceAdvisorText +
          ' for Customer Pay Repair Orders <br>' +
          'The detailed graphs represent the Labor Rates for each of the Pay types - Customer Pay, Warranty, Internal, Extended Service Contract and Factory Service Contract.<br>',
        Calculation:
          'Effective Labor Rate = Sum of Labor Sale / Sum of Labor Sold Hours for each Pay type. <br>' +
          'All Op Categories - Repair, Maintenance and Competitive included.'
      };
      return graphDetail;
    case '1044':
      graphDetail = {
        title: 'Labor Hours Per RO',
        Overview:
          'The main graph shows the Labor Hours sold per Customer Pay Repair Order over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed view shows the Labor Hours Sold Repair Order for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Labor Hours Per RO = Sum of Labor Sold Hours / Count of Repair Orders for each Pay type.<br> '
      };
      return graphDetail;
    case '1318':
      graphDetail = {
        title: 'Parts Hours Per RO',
        Overview:
          'The main graph shows the Parts Hours sold per Customer Pay Repair Order over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed view shows the Parts Hours Sold Repair Order for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Parts Hours Per RO = Sum of  Sold Hours where Parts Sale not equal to zero / Count of Repair Orders for each Pay type where Parts Sale not equal to zero.<br> '
      };
      return graphDetail;
    case '1098':
      graphDetail = {
        title: 'CP Effective Labor Rate For Repair And Competitive',
        Overview:
          'The graph shows the Labor Rates as a weighted average of the Op Categories - Repair and Competitive over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>',
        Calculation:
          'Effective Labor Rate  = Sum of Labor Sale / Sum of Labor Sold Hours where Op Category = Repair & Competitive'
      };
      return graphDetail;
    case '925':
      graphDetail = {
        title: 'Repair Order Count',
        Overview:
          'The main graph shows the Total Count of Customer Pay Repair Orders over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          ' In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          'Repair Order Count - Combined: Repair Orders that are paid with any of the  means of Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract.<br>' +
          'Repair Order Count - Customer Pay: Repair Orders with Customer Pay.<br>' +
          'Repair Order Count - Warranty: Repair Orders that are covered under Warranty.<br>' +
          'Repair Order Count - Internal: Repair Orders with Internal pay.<br>' +
          'Repair Order Count - Maintenance Plan: Repair Orders that are covered under Maintenance Plan.<br>' +
          'Repair Order Count - Factory Service Contract: Repair Orders that are covered under Factory Service Contract.<br>' +
          'Repair Order Count - Extended Service Contract: Repair Orders that are covered under Extended Service Contract.',
        Calculation:
          'Repair Order Count = count of all Repair Orders grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered for these graphs'
      };
      return graphDetail;
    case '918':
      graphDetail = {
        title: 'Job Count',
        Overview:
          ' The main graph shows Job Counts for Customer Pay ROs over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed graphs show the Jobs Counts for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Job Count by Category = Count of Jobs each Pay Types.<br>' +
          'All Op Categories - Repair, Maintenance, Competitive & Shop Supplies included.'
      };
      return graphDetail;
    case '955':
      graphDetail = {
        title: 'Average Labor Sale Per RO',
        Overview:
          ' The main graph shows the Average Labor Sale per Customer Pay Repair Order on a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed view shows the Average Labor Sale per Repair Order for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Average Labor Sale Per RO = Sum of Labor Sale / Count of ROs for each Paytype'
      };
      return graphDetail;

    case '1049':
      graphDetail = {
        title: 'Parts Revenue',
        Overview:
          'The main graph shows total revenue from Parts for  Customer Pay Repair Orders over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          'Parts Revenue - Combined: All Patypes - Customer Pay, Internal pay, Warranty, Maintenance Plan, Factory Service Contract and Extended Service Contract included.<br>' +
          'Parts Revenue - Customer Pay: Customer Pay Repair Orders only.<br>' +
          'Parts Revenue - Warranty: Warranty Repair Orders only.<br>' +
          'Parts Revenue - Internal:  Internal Repair Orders only.<br>' +
          'Parts Revenue - Maintenance Plan: Repair Orders on the Maintenance Plan only.<br>' +
          'Parts Revenue - Factory Service Contract:Repair Orders on the Factory Service Contract only.<br>' +
          'Parts Revenue - Extended Service Contract:Repair Orders on the Extended Service Contract only.',
        Calculation:
          'Parts Revenue = Sum of Parts Extended Sale grouped by month.' +
          '<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '952':
      graphDetail = {
        title: 'Parts Gross Profit',
        Overview:
          'The main graph shows Gross Profit from Parts sold for Customer Pay Repair Orders on a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          ' In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          'Parts Gross Profit - Combined: All Pay types - Customer pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract included.<br>' +
          'Parts Gross Profit - Customer Pay: Customer Pay Repair Orders only.<br>' +
          'Parts Gross Profit - Warranty: Warranty Repair Orders only.<br>' +
          'Parts Gross Profit - Internal: Internal Pay Repair Orders only.<br>' +
          'Parts Gross Profit - Maintenance Plan: Repair Orders on the Maintenance Plan.<br>' +
          'Parts Gross Profit - Factory Service Contract: Repair Orders on the Factory Service Contract.<br>' +
          'Parts Gross Profit - Extended Service Contract: Repair Orders on the Extended Service Contract.',
        Calculation:
          'Parts Gross Profit = Sum of Parts Sale - Sum of Parts Cost <br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs.'
      };
      return graphDetail;
    case '966':
      graphDetail = {
        title: 'Parts Gross Profit Percentage',
        Overview:
          ' The graph shows the Parts Gross Profit % for Customer Pay Repair Orders on a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.',
        Calculation:
          'Parts Gross Profit %  = ((Sum of Parts Sale - Sum of Parts Cost)/Sum of Parts Sale)*100<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered in these graphs'
      };
      return graphDetail;
    case '953':
      graphDetail = {
        title: 'Parts Revenue Per RO ',
        Overview:
          '  The graph shows the Parts Revenue per Repair Order on a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed view shows the Parts Revenue per Repair Order for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan,Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Parts Revenue Per RO = Sum of Parts Extended Sale/Repair Order Count'
      };
      return graphDetail;
    case '1143':
      graphDetail = {
        title: 'Repair Order Count',
        Overview:
          'The main graph shows Total Count of Repair Orders grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'This view shows Repair Orders that are paid with Customer Pay only.<br>' +
          'In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          ' Repair Order Count - Combined: Repair Orders that are paid with any of the  means of Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract.<br>' +
          'Repair Order Count - Customer Pay: Repair Orders with Customer Pay.<br>' +
          'Repair Order Count - Warranty: Repair Orders that are covered under Warranty.<br>' +
          'Repair Order Count - Internal: Repair Orders with Internal pay.<br>' +
          'Repair Order Count - Maintenance Plan: Repair Orders that are covered under Maintenance Plan.<br>' +
          'Repair Order Count - Factory Service Contract: Repair Orders that are covered under Factory Service Contract.<br>' +
          'Repair Order Count - Extended Service Contract: Repair Orders that are covered under Extended Service Contract.',
        Calculation:
          'Repair Order Count: count of all Repair Orders grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          ' All the opcode categories - Repair, Maintenance, Competitive are considered for these graphs'
      };
      return graphDetail;
    case '916':
    case '1238':
      graphDetail = {
        title: 'Parts Markup',
        Overview:
          'The graph shows the Parts Markup for Customer Pay Repair Orders on a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>' +
          'The detailed view shows the Markup for each of the Pay types - Customer Pay, Warranty, Internal, Maintenance Plan, Extended Service Contract and Factory Service Contract.',
        Calculation:
          'Parts Markup = Sum of Parts Sale/ Sum of Parts Cost grouped by ' +
          serviceAdvisorText +
          '.'
      };
      return graphDetail;

    case '931':
      graphDetail = {
        title: 'Total Labor Opportunity',
        Overview:
          'The graph shows the Combined view of Labor Gross Opportunity, Labor Volume Opportunity and Labor Joint Opportunity.'
      };
      return graphDetail;
    case '933':
      graphDetail = {
        title: 'Labor Volume Opportunity',
        Overview:
          'The graph shows how the Profit changes for Labor with increased Target Hours per Repair Order. Only Labor Sales with Customer Pay are considered for this graph.' +
          'A target goal value for Hours per Repair Order can be entered for comparison.',
        Calculation:
          'Labor Volume Opportunity = (Target Hours per Repair Order - Actual Hours per Repair Order) * Count of Repair Orders * Gross Labor Profit per Hour<br>' +
          'Actual Hours per Repair Order = Labor Sold Hours / RO Count<br>' +
          'Gross Labor Profit per Hour = (Labor Sale - Labor Cost) / Total Sold Hours'
      };
      return graphDetail;
    case '932':
      graphDetail = {
        title: 'Labor Gross Opportunity',
        Overview:
          'The graph shows how much additional Gross Profit you could  have earned on Labor if the monthly target has been met.' +
          'Only Labor Sales with Customer Pay are considered for this graph. Twelve target Labor Sale goal values can be entered to compare the target and additional Profit Values.',
        Calculation:
          '(Sum of Labor Cost / (1 - Target Labor Gross Profit per Hour / 100)) - Sum of Labor Sale'
      };
      return graphDetail;
    case '934':
      graphDetail = {
        title: 'Labor Joint Opportunity',
        Overview:
          'The graph shows how the Profit increases for Labor with increased Target Hours per Repair Order and increased Target Gross Profit per Hour.' +
          ' Only Customer Pay Labor Sales are considered for this graph. Two target values, one for Target Hours per Repair Order and the other for Target Gross Profit per Hour can be entered for analysis.',
        Calculation:
          'Labor Joint Opportunity = (Target Hours per Repair Order - Actual Hours per Repair Order) * Count of Repair Orders * (Target Gross Labor Profit per Hour - Actual Gross Labor Profit per Hour)<br>' +
          'Actual Hours per Repair Order = Total Sold Hours / RO Count<br>' +
          'Actual Gross Labor Profit per Hour = (Labor Sale - Labor Cost) / Total Sold Hours'
      };
      return graphDetail;
    case '926':
      graphDetail = {
        title: 'Total Parts Opportunity',
        Overview:
          'The graph shows the Combined view of Parts Gross Opportunity, Parts Volume Opportunity and Parts Joint Opportunity.',
        Calculation: ''
      };
      return graphDetail;
    case '927':
      graphDetail = {
        title: 'Parts Volume Opportunity',
        Overview:
          'The graph shows how the Profit Changes for Parts with increase in Target Hours per Repair Order. Only Parts Sales with Customer Pay are considered for this graph.' +
          ' A target goal value for Hours per Repair Order can be entered for comparison.',
        Calculation:
          'Parts Volume Opportunity = (Target Hours per RO - Actual Hours per RO) * Count of RO * Gross Parts profit per Hour<br>' +
          'Actual Hours per RO = Total Sold Hours / RO Count<br>' +
          'Gross Parts Profit per Hour = (Parts Sale - Parts Cost) / Total Sold Hours'
      };
      return graphDetail;
    case '929':
      graphDetail = {
        title: 'Parts Gross Opportunity',
        Overview:
          'The graph shows how much additional Gross Profit you could  have earned on Parts if the Monthly Sales Target has been met.' +
          'Only Parts Sales with Customer Pay are considered for this graph. Twelve Monthly Target Total Gross Profit Goal values can be entered to compare the Target and Additional Profit Values.',
        Calculation:
          'Monthly Gross Parts Profit Opportunity = (Sum of Parts Cost/(1-Target Gross Parts Profit % /100))- Sum of Parts Sale'
      };
      return graphDetail;
    case '928':
      graphDetail = {
        title: 'Parts Joint Opportunity',
        Overview:
          'The graph shows how the Profit increases for Parts with Increased Target Hours per Repair Order and Increased Target Gross Profit per Hour.' +
          ' Only Customer pay Parts Sales are considered for this graph. Two target values, one for Target Hours per Repair Order and the other for Target Gross Profit per Hour can be entered for analysis.',
        Calculation:
          'Parts Joint Opportunity = (Target Hours per RO - Actual Hours per RO) * Count of RO * (Target Gross Parts Profit per Hour - Actual Gross Parts Profit per Hour).<br>' +
          'Actual Hours per RO = Total Sold Hours / RO Count<br>' +
          'Actual Gross Parts Profit per Hour = (Parts Sale - Parts Cost) / Total Sold Hours'
      };
      return graphDetail;

    case '921':
      graphDetail = {
        title: 'Pricing Opportunity By Category',
        Overview:
          'The graph shows how much additional Labor Sale Price per Opcode Category could have been earned if the monthly target was met.' +
          'It is shown for the 3 Opcode Categories - Repair, Maintenance and Competitive. Here we consider Labor Sale Price for Customer Pay only. One monthly Target Sale rate input can be given as goal for this chart.',
        Calculation:
          'Total Pricing Opportunity = (Target ELR - Actual ELR) * Job Count for the month <br>' +
          'ELR = Labor Sale / Labor Sold Hours'
      };
      return graphDetail;

    case '924':
      graphDetail = {
        title: 'Total Pricing Opportunity',
        Overview:
          'The graph shows how much Additional Labor Sale Price could  have been earned if the monthly target was met.' +
          'Here we consider Labor Sales with Customer Pay only. One monthly target value for Labor Price or Effective Labor rate is given as input. ',
        Calculation:
          '(Target Effective Labor Rate - Average Actual Effective Labor Rate) * Job Count<br>' +
          'Effective Labor Rate = Labor Sale / Labor Sold Hours'
      };
      return graphDetail;

    case '948':
      graphDetail = {
        title: 'CP 1-Line-RO Count',
        Overview:
          'The graph shows the count of Repair Orders that only have a single Customer Pay job',
        Calculation:
          'CP 1-Line-RO Count = Count of Repair Orders where Count of CP Jobs = 1 '
      };
      return graphDetail;
    case '923':
      graphDetail = {
        title: 'CP 1-Line-RO Count Percentage',
        Overview:
          'The graph shows the count of repair orders that have a single Customer Pay job as a percentage of all Customer Pay Repair Orders for a given month',
        Calculation:
          'Repair Orders with Jobs that have a single Customer Pay job are termed as Single Job ROs.<br>' +
          'CP 1-Line-RO Count % = (CP 1-Line-RO Count/ Total CP RO Count ) *100'
      };
      return graphDetail;
    case '938':
      graphDetail = {
        title: 'CP Return Rate',
        Overview:
          'Return rate measures the service to service retention.The graph takes the number of unique vehicles that visited six or 12 months before the current month and calculates the return rate by identifying how many of them returned during the current month. Customer Pay Repair Orders only',
        Calculation:
          '6 Months Return Rate = (No of VINs that returned in the current month / Distinct VIN count in last six months before current month )*100 <br>' +
          '12 Months Return Rate = (No of VINs that returned in the current month / Distinct VIN count in last twelve months before current month )*100'
      };
      return graphDetail;
    case '935':
      graphDetail = {
        title: 'Labor Sold Hours Percentage By Pay type',
        Overview:
          'The graph shows the Labor Sold Hours for each Pay Type - Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract and Extended Service Plan as a percentage of the total Labor Sold Hours for Service Repair Orders.',
        Calculation:
          'Labor Sold Hours % - Customer Pay = Total Customer Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan<br>' +
          'Labor Sold Hours % - Internal = Total Internal Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan<br>' +
          'Labor Sold Hours % - Warranty = Total Warranty Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan <br>' +
          'Labor Sold Hours % - Maintenance Plan = Total Maintenance Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan <br>' +
          'Labor Sold Hours % - Factory Service Contract = Total Factory Service Contract Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan <br>' +
          'Labor Sold Hours % - Extended Service Plan = Total Extended Service Plan Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan'
      };
      return graphDetail;
    case '930':
      graphDetail = {
        title: 'CP Parts to Labor Ratio',
        Overview:
          "Parts-to-labor ratio is the ratio of labor sales to parts sold over a certain period. The parts-to-labor ratio provides insight into how much a company's revenue is derived" +
          ' from services performed and how much depends on selling parts. Customer Pay ROs Only.',
        Calculation:
          'Parts To Labor Ratio = Parts Extended Sale/ Labor Sale<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive  are considered in these graphs.'
      };
      return graphDetail;
    case '936':
      graphDetail = {
        title: 'CP Parts to Labor Ratio By Category',
        Overview:
          "Parts-to-labor ratio is the ratio of labor sales to parts sold over a certain period. The parts-to-labor ratio provides insight into how much a company's revenue is" +
          ' derived from services performed and how much depends on selling parts. Customer Pay ROs Only.',
        Calculation:
          'Parts To Labor Ratio = Parts Extended Sale/ Labor Sale for each Op Category'
      };
      return graphDetail;
    case '957':
      graphDetail = {
        title: 'Delta - Labor Tech Hours and Flat Rate Hours',
        Overview:
          'The graph shows the difference between Labor Tech Hours and Flat Rate Hours.',
        Calculation:
          'Value = Labor Tech Hours - Flat Rate Hours<br>' +
          'Here we are considering Labor Tech Hours and Flat Rate Hours and that should be in Customer-pay type.<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive - are considered in this graph.<br>' +
          '* Flat Rate Hours= Labor Sold Hours'
      };
      return graphDetail;
    case '766':
      graphDetail = {
        title: 'Delta - Sold Hours And Flat Rate Hours',
        Overview:
          'The graph shows the difference between the Labor Sold Hours and Flat Rate Hours. For CDK , Flat Rate Hours is considered to be equal to Labor Sold Hours. Customer Pay ROs only',
        Calculation:
          'Delta - Labor Sold Hours Vs Flat Rate Hours = Labor Sold Hours - Flat Rate Hours.<br>' +
          'All Op Categories - REPAIR, MAINTENANCE and COMPETITVE included in calculations'
      };
      return graphDetail;
    case '1089':
      graphDetail = {
        title: 'CP Moving ELR',
        Overview:
          'The graph shows the Effective Labor Rate over a running window of 100 Customer Pay Repair Orders for Op Categories - Repair & Competitive only.<br>' +
          'A reference line shows the median of the Effective Labor Rate over a period of 6 months.' +
          'Another reference line represents the Current Warranty Rate at any point of time.' +
          'The vertical lines help in identifying the months for which the tranche is calculated.',
        Calculation:
          'Moving ELR = (Sum of Labor Sale / Sum of Labor Sold Hours ) over a running frame of 100 ROs over the past 6 months.'
      };
      return graphDetail;
    case '1095':
      graphDetail = {
        title: 'CP Moving Parts Markup',
        Overview:
          'The graph shows the Parts Markup over a running window of 100 Customer Pay Repair Orders for Op Categories - Repair & Competitive only.<br>' +
          'A reference line shows the median of the Parts Markup over a period of 6 months.<br>' +
          'A second reference line shows the Current Warranty Rate at any point of time.',
        Calculation:
          'Moving Markup = (Sum of Parts Sale / Sum of Parts Cost ) over a running frame of 100 ROs over the past 6 months.'
      };
      return graphDetail;
    case '1090':
      if (localStorage.getItem('itemTab') == 'one') {
        graphDetail = {
          title: 'CP ELR Vs Labor Sold Hours',
          Overview:
            'The graph shows the Effective Labor Rate vs Labor Sold Hours for the past 1 month as a scatter plot for ' +
            'Customer Pay Repair Orders with Op Categories - Repair , Competitive and Maintenance with Maintenance disabled by default.',
          Calculation:
            'Plot the ELR against the Labor Sold Hours for every Customer Pay Job.'
        };
      } else if (localStorage.getItem('itemTab') == 'two') {
        graphDetail = {
          title: 'CP ELR Vs Labor Sold Hours',
          Overview:
            'The graph shows the Effective Labor Rate vs Labor Sold Hours for the past 3 months as a scatter plot for ' +
            'Customer Pay Repair Orders with Op Categories - Repair , Competitive and Maintenance with Maintenance disabled by default.',
          Calculation:
            'Plot the ELR against the Labor Sold Hours for every Customer Pay Job.'
        };
      } else if (
        localStorage.getItem('itemTab') == null ||
        localStorage.getItem('itemTab') == 'three'
      ) {
        graphDetail = {
          title: 'CP ELR Vs Labor Sold Hours',
          Overview:
            'The graph shows the Effective Labor Rate vs Labor Sold Hours for the past 6 months as a scatter plot for ' +
            'Customer Pay Repair Orders with Op Categories - Repair , Competitive and Maintenance with Maintenance disabled by default.',
          Calculation:
            'Plot the ELR against the Labor Sold Hours for every Customer Pay Job.'
        };
      }
      return graphDetail;
    case '1096':
      graphDetail = {
        title: 'CP Parts Markup Vs Parts Cost',
        Overview:
          'The graph shows the Markup vs Parts Cost for the past 6 months as a scatter plot for Customer Pay Repair ' +
          'Orders with Op Categories - Repair , Competitive and Maintenance with Maintenance disabled by default.',
        Calculation:
          'Plot the Markup against the Parts Cost for every Customer Pay Job.'
      };
      return graphDetail;
    case '1110':
      graphDetail = {
        title: 'CP Add On Parts Revenue by Op Category',
        Overview:
          'The graph shows the total Parts Revenue from Add On Jobs for Customer Pay Repair Orders grouped by the Op Categories ' +
          '- REPAIR, MAINTENANCE, COMPETITIVE and SHOP SUPPLIES.',
        Calculation:
          'Add On Parts Revenue = Sum of Parts Extended Sale.<br> ' +
          'Where Op Category = REPAIR/MAINTENANCE/COMPETITIVE/SHOP SUPPLIES and Pay Type = C and Add On flag = Y'
      };
      return graphDetail;
    case '1109':
      graphDetail = {
        title: 'CP Add On Labor Revenue by Op Category',
        Overview:
          'The graph show the total Labor Revenue from Add On Jobs for Customer Pay Repair ' +
          'Orders grouped by the Op Categories - REPAIR, MAINTENANCE,COMPETITIVE and SHOP SUPPLIES.',
        Calculation:
          'Add On Labor Revenue = Sum of Labor Sale.<br> ' +
          'Where Op COp Category = REPAIR/MAINTENANCE/COMPETITIVE/SHOP SUPPLIES and Pay Type = C and Add On flag = Y'
      };
      return graphDetail;
    case '1108':
      graphDetail = {
        title: 'CP Labor Sold Hours - Add Ons vs Non Add Ons',
        Overview:
          'The graph shows the Labor Sold Hours for Add On Jobs versusNon Add Jobs  ' +
          'grouped by ' +
          serviceAdvisorText +
          ' for Customer Pay Repair Orders.',
        Calculation:
          'Labor Sold Hours for Add On Jobs = Sum of Sold Hours where Add On Flag = Y <br> ' +
          'Labor Sold Hours for Non Add On Jobs = Sum of Sold Hours where Add On flag = N'
      };
      return graphDetail;
    case '1107':
      graphDetail = {
        title: 'CP Add On Parts Revenue',
        Overview:
          'The graph shows the total Parts Revenue from Add On Jobs for Customer Pay Repair Orders.<br>  ' +
          'All Op Categories - REPAIR, MAINTENANCE, COMPETITIVE and SHOP SUPPLIES included.',
        Calculation:
          'Add On Parts Revenue = Sum of Parts Extended Sale where Add On flag = Y '
      };
      return graphDetail;
    case '1106':
      graphDetail = {
        title: 'CP Add On Labor Revenue',
        Overview:
          'The graph shows the total Labor Revenue from Add On Jobs for Customer Pay Repair Orders.<br>  ' +
          'All Op Categories - REPAIR, MAINTENANCE, COMPETITIVE and SHOP SUPPLIES included.',
        Calculation:
          'Add On Labor Revenue = Sum of Labor Sale where Add On flag = Y '
      };
      return graphDetail;
    case '1105':
      graphDetail = {
        title: 'CP Total Add On Revenue',
        Overview:
          'The graph shows the total revenue (Labor and Parts) from Add On Jobs for Customer Pay repair orders grouped by ' +
          serviceAdvisorText +
          '. <br>  ' +
          'All Op Categories - REPAIR, MAINTENANCE, COMPETITIVE and SHOP SUPPLIES included.',
        Calculation:
          'Total Add On Revenue = Sum of Labor Revenue + Sum of Parts Revenue where Add On flag = Y  '
      };
      return graphDetail;
    case '1104':
      graphDetail = {
        title: 'CP RO Count - Add Ons vs Non Add Ons',
        Overview:
          'The graph shows the number of Repair Orders that have Add On jobs versus the number of Repair Orders ' +
          'that do not have Add On jobs. Only Customer Pay Repair Orders included.',
        Calculation:
          'CP RO Count Add On = Count of Distinct Repair Orders  where  Add On flag = Y<br>  ' +
          'CP RO Count Non Add On = Count of Distinct Repair Orders) where Add On flag = N'
      };
      return graphDetail;
    case '1103':
      graphDetail = {
        title: 'CP Add On Job Count By Op Category',
        Overview:
          'The graph shows the total number of Add On Jobs for each Op Category - ' +
          'REPAIR, MAINTENANCE, COMPETITIVE and SHOP SUPPLIES grouped by ' +
          serviceAdvisorText +
          '.',
        Calculation:
          'Add on Job Count = Count of Repair Order Jobs <br>' +
          'Where Line Add On flag = Y and Op Category = REPAIR/COMPETITIVE/MAINTENANCE/SHOP SUPPLIES'
      };
      return graphDetail;
    case '1102':
      graphDetail = {
        title: 'CP  % ROs with Add On Jobs',
        Overview:
          'The graph shows the the number of Repair Orders with Add On Jobs ' +
          'as a percentage of the total Customer Pay Repair Orders grouped by ' +
          serviceAdvisorText +
          '.',
        Calculation:
          '% ROs with Add On Jobs = (No. of Distinct ROs with Add On Jobs * 100)/Total No. of distinct Repair Orders'
      };
      return graphDetail;
    case '1101':
      graphDetail = {
        title: 'CP Add On Job Counts per RO',
        Overview:
          'The graph shows the number of Add On Jobs per Repair Order for Customer Pay Repair Orders grouped by ' +
          serviceAdvisorText +
          '.',
        Calculation:
          'Add On Jobs Per RO = No. of Add On Jobs / Total No. of distinct Repair Orders'
      };
      return graphDetail;
    case '1100':
      graphDetail = {
        title: 'CP Add On Job Counts vs Non Add On Job Counts',
        Overview:
          'The graph shows the total number of Add On jobs versus the total number of Non Add On jobs grouped by ' +
          serviceAdvisorText +
          '. ' +
          'For Customer Pay Repair Orders only.',
        Calculation:
          'Add On Job Count = Count of Repair Order jobs where Line Add On flag = Y <br>' +
          'Non Add On Job Count = Count of Repair Order jobs where Line Add On flag = N'
      };
      return graphDetail;
    case '1099':
      graphDetail = {
        title: 'CP Add On Job Count',
        Overview:
          'The graph shows the total number of Add On jobs grouped by ' +
          serviceAdvisorText +
          ' for Customer Pay Repair Orders only. ',
        Calculation:
          'Add On Job Count = Count of Repair Order jobs where Line Add On flag = Y'
      };
      return graphDetail;
    case '1116':
      graphDetail = {
        title: 'CP Add Ons - Effective Labor Rate',
        Overview:
          'The graph shows the Effective Labor Rate for Add On jobs . For Customer Pay Repair Orders only. ',
        Calculation:
          'Effective Labor Rate = Sum of Labor Revenue/ Sum of Labor Sold Hours  where Line Add On flag = Y'
      };
      return graphDetail;
    case '1117':
      graphDetail = {
        title: 'CP Add Ons - Parts Markup',
        Overview:
          'The graph shows the Parts Markup for Add On Jobs. For Customer Pay Repair Orders only.',
        Calculation:
          'Parts Markup = Sum of Parts Extended Sale/ Sum of Parts Extended Cost where Line Add On flag = Y'
      };
      return graphDetail;
    case '1118':
      graphDetail = {
        title: 'CP Add On Job Counts by Service Advisor',
        Overview:
          'The graph shows the number of Jobs per Service Advisor. This helps to identify the upselling skills of the Service Advisors.' +
          'For Customer Pay Repair Orders only.',
        Calculation:
          'Add On Job Counts by Service Advisor = Count of Add On Jobs grouped by Service Advisor compared over two months.'
      };
      return graphDetail;
    case '1119':
      graphDetail = {
        title: 'CP Add On Labor Revenues by Service Advisor',
        Overview:
          'The graph shows the total Labor Revenue from Add On Jobs grouped by Service Advisor. ' +
          'For Customer Pay Repair Orders only.',
        Calculation:
          'Labor Revenue by Service Advisor = Sum of Labor Sale grouped by Service Advisor compared over two months where Add On flag = Y'
      };
      return graphDetail;
    case '1120':
      graphDetail = {
        title: 'CP Add On Parts Revenues by Service Advisor',
        Overview:
          'The graph show the total Parts Revenue from Add On Jobs grouped by Service Advisor. ' +
          'For Customer Pay Repair Orders only.',
        Calculation:
          'Parts Revenue By Service Advisor = Sum of Parts Extended Sale grouped by Service Advisor compared over two months where Add On flag = Y'
      };
      return graphDetail;
    case '1121':
      graphDetail = {
        title: 'CP Add On vs Non Add On Revenue Percentage by Service Advisor',
        Overview:
          'The graph shows the Add On versus Non Add On Revenue grouped by Service Advisor as a percentage of the total revenue achieved ' +
          'by that Service Advisor comparable over any two months.',
        Calculation:
          'Add On Revenue = (Sum of Labor Sale + Sum of Parts Extended Sale)  grouped by Service Advisor where Add On flag = Y <br>' +
          'Non Add On Revenue = (Sum of Labor Sale + Sum of Parts Extended Sale) grouped by Service Advisor where Add On flag = N <br>' +
          '<br>' +
          'Total Revenue By Service Advisor = Add On Revenue + Non Add On Revenue grouped by Service Advisor <br>' +
          '<br>' +
          'Add On Revenue % By Service Advisor = Add On Revenue * 100 / Total Revenue grouped by Service Advisor<br>' +
          'Non Add On Revenue % By Service Advisor = Non Add On Revenue * 100/ Total Revenue grouped by Service Advisor'
      };
      return graphDetail;
    case '1111':
      graphDetail = {
        title: 'CP Labor and Parts Discount',
        Overview:
          'The graph shows the total Labor and Parts Discounts for Customer Pay Repair Orders over a 13 month trend.',
        Calculation:
          'Labor Discount = Sum of Discounts applied on Labor grouped by ' +
          serviceAdvisorText +
          ' <br>' +
          'Parts Discount = Sum of Discounts applied on Parts grouped by ' +
          serviceAdvisorText
      };
      return graphDetail;
    case '1113':
      graphDetail = {
        title: 'CP RO Count For Discounts By Discount Level',
        Overview:
          'The graph shows the Discounted RO Counts that have discounts applied either at the Repair Order level or Job Level. ' +
          'Customer Pay ROs only.',
        Calculation:
          'Discounted RO Counts at RO Level  = Distinct RO Count where Discount Level = RO<br>' +
          'Discounted RO Counts at Line Level = Distinct RO Count where Discount Level = LINE'
      };
      return graphDetail;
    // case '':
    //   graphDetail = {
    //     title: 'CP Discounted Jobs Per RO',
    //     Overview:
    //       'The graph shows the count of discounted Jobs per Repair Order over a 13 month trend. Customer Pay ROs only. ',
    //     Calculation:
    //       'Discounted Jobs Per RO = Total no of Jobs that have Labor or Parts Discount/ Distinct RO Count'
    //   };
    //   return graphDetail;
    case '1114':
      graphDetail = {
        title: 'Discounts By Service Advisor',
        Overview:
          'The graph shows the total Labor and Parts discounts grouped by Service Advisor comparable over two months. Customer Pay ROs only ',
        Calculation:
          'Discounts By Service Advisor = Total Labor Discount + Total Parts Discounts grouped by Service Advisor for each chosen month.'
      };
      return graphDetail;
    case '1122':
      graphDetail = {
        title: 'CP Add On Revenues by Service Advisor',
        Overview:
          'The graph shows the Add On Revenues for Labor and Parts grouped by Service Advisor comparable over any two months.For Customer Pay Repair Orders only. ',
        Calculation:
          'Labor Revenue by Service Advisor = Sum of Labor Sale grouped by Service Advisor compared over two months where Add On flag = Y <br>' +
          'Parts Revenue By Service Advisor = Sum of Parts Extended Sale grouped by Service Advisor compared over two months where Add On flag = Y'
      };
      return graphDetail;
    case '1123':
      graphDetail = {
        title: 'CP Discounted RO Percentage',
        Overview:
          'The graph shows the percentage of discounted ROs on a 13 month trend. Discounts are applied only to Customer Pay jobs on the Repair Orders.',
        Calculation:
          'Discounted RO Percentage = (Discounted Repair Order Count for any month /Total Repair Order ' +
          'Count for the month)*100'
      };
      return graphDetail;
    case '1124':
      graphDetail = {
        title: 'Discounted RO % by Service Advisor',
        Overview:
          'The graph shows the percentage of ROs discounted by Service Advisors comparable between two months.',
        Calculation:
          'Discounted RO % by Service Advisor = (No. of Repair Orders discounted by Service Advisor/ Total No. of Repair Orders by Service Advisor)*100'
      };
      return graphDetail;
    case '1125':
      graphDetail = {
        title: 'Discounted Job Count % by Opcategory by Service Advisor',
        Overview:
          'The graph shows the percentage of jobs discounted by Service Advisors for each of the Op Categories - Repair, Competitive and Maintenance comparable between two months.Customer Pay jobs only.',
        Calculation:
          'Discounted Job Count % by Service Advisor - Repair = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor )*100  where Op Category = REPAIR <br> ' +
          'Discounted Job Count % by Service Advisor -Competitive = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor )*100  where Op Category = COMPETITIVE <br> ' +
          'Discounted Job Count % by Service Advisor - Maintenance = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor )*100  where Op Category = MAINTENANCE'
      };
      return graphDetail;
    case '1126':
      graphDetail = {
        title: 'Discounted Job % by Service Advisor',
        Overview:
          'The graph shows the percentage of jobs discounted by Service Advisors comparable between two months.Customer Pay jobs only.',
        Calculation:
          'Discounted Job % by Service Advisor = (No. of discounted jobs on the Repair Orders discounted by Service Advisor / Total No. of jobs on the Repair Orders discounted by Service Advisor)*100'
      };
      return graphDetail;
    case '1115':
      graphDetail = {
        title: 'CP Discounted Sale %',
        Overview:
          'The graph shows the % of Labor and Parts Sale Discounted over a 13 month trend for all Customer Pay ROs.',
        Calculation:
          '% of Labor $ discounted = (Total Labor Discount for any given month *100/ Total CP Labor Sale for the given month) <br>' +
          '% of Parts $ discounted = (Total Parts Discount for any given month *100/ Total CP Parts Sale for the given month)'
      };
      return graphDetail;
    case '1164':
      graphDetail = {
        title: 'Discounts Per Total CP ROs',
        Overview:
          'The graph shows the Labor and Parts Discounts per total Customer Pay Repair Orders over a 13 month trend.',
        Calculation:
          'Labor Discount Per RO = (Total Labor Discount for any given month / Total CP RO for the given month) <br>' +
          'Parts Discount Per RO = (Total Parts Discount for any given month / Total CP RO for the given month)'
      };
      return graphDetail;
    case '1165':
      graphDetail = {
        title: 'Discounts Per Total Discounted CP ROs',
        Overview:
          'The graph shows the Labor and Parts Discounts per total discounted Customer Pay Repair Orders over a 13 month trend.',
        Calculation:
          'Labor Discount Per RO = (Total Labor Discount for any given month / Total Discounted CP RO for the given month) <br>' +
          'Parts Discount Per RO = (Total Parts Discount for any given month / Total Discounted CP RO for the given month)'
      };
      return graphDetail;
    case '1174':
      graphDetail = {
        title: 'Average Hours Sold Per Technician',
        Overview:
          'The graph shows the average labor hours sold per technician over a 13 month trend.' +
          ' The calculation is based one 8 daily available hours per technician.All Pay Types included.',
        Calculation:
          'Daily Available Hours  = Count of Distinct Technicians on any given day * 8 hrs <br>' +
          'Average Hours Per Technician = Sum of Labor Sold Hours for any given month / Sum of Daily Available Hours'
      };
      return graphDetail;
    case '1175':
      graphDetail = {
        title: 'Average Sale Per Technician',
        Overview:
          'The graph shows the Average Sales per technician over a 13 month trend . All Pay Types included.',
        Calculation:
          'Average Sale Per Technician = (Sum of Labor Sale + Sum of Parts Sale ) for any given month / Count of distinct Technicians for the month'
      };
      return graphDetail;
    case '1232':
      graphDetail = {
        title: '% Discounted Per Discounted CP ROs',
        Overview:
          'The graph shows the % of Labor and Parts Discounts over the total CP RO Discounts.',
        Calculation:
          ' % Discounted Per Discounted CP ROs  - Labor = (Total Labor Discount $ / Total Discounted CP Sale) * 100  <br>' +
          ' % Discounted Per Discounted CP ROs - Parts  = (Total Parts Discount $ / Total Discounted CP Sale)) * 100'
      };
      return graphDetail;
    case '1233':
      graphDetail = {
        title: 'CP Discounts % - All Discounted CP ROs - Labor & Parts',
        Overview:
          'The graph shows the % of Labor and Parts Discounts over the total CP RO Discounts.',
        Calculation:
          ' CP Discount % - All Discounted CP ROs  - Labor = (Total Labor Discount $ / Total Discounted CP Sale) * 100  <br>' +
          ' CP Discount % - All Discounted CP ROs - Parts  = (Total Parts Discount $ / Total Discounted CP Sale)) * 100'
      };
      return graphDetail;
    case '1234':
      graphDetail = {
        title: 'CP Discounts - Labor & Parts',
        Overview:
          'The graph shows the % of Labor and Parts Sale Discounted over a 13 month trend for all Customer Pay ROs.',
        Calculation:
          '% of Labor $ discounted = (Total Labor Discount for any given month *100/ Total CP Labor Sale for the given month) <br>' +
          '% of Parts $ discounted = (Total Parts Discount for any given month *100/ Total CP Parts Sale for the given month)'
      };
      return graphDetail;
    case '1235':
      graphDetail = {
        title: 'CP Discounts % - All CP ROs - Labor & Parts',
        Overview:
          'The graph shows the % of Labor and Parts Sale Discounted over a 13 month trend for all Customer Pay ROs.',
        Calculation:
          '% of Labor $ discounted = (Total Labor Discount for any given month *100/ Total CP Labor Sale for the given month) <br>' +
          '% of Parts $ discounted = (Total Parts Discount for any given month *100/ Total CP Parts Sale for the given month)'
      };
      return graphDetail;
    case '1236':
      graphDetail = {
        title: 'Discount $ Per Total CP ROs - Labor & Parts',
        Overview:
          'The graph shows the Labor and Parts Discounts per total Customer Pay Repair Orders over a 13 month trend.',
        Calculation:
          'Labor Discount Per RO = (Total Labor Discount for any given month / Total CP RO for the given month) <br>' +
          'Parts Discount Per RO = (Total Parts Discount for any given month / Total CP RO for the given month)'
      };
      return graphDetail;
    case '1237':
      graphDetail = {
        title: 'Discount $ Per Total Discounted CP ROs - Labor & Parts',
        Overview:
          'The graph shows the Labor and Parts Discounts per total discounted Customer Pay Repair Orders over a 13 month trend.',
        Calculation:
          'Labor Discount Per RO = (Total Labor Discount for any given month / Total Discounted CP RO for the given month) <br>' +
          'Parts Discount Per RO = (Total Parts Discount for any given month / Total Discounted CP RO for the given month)'
      };
      return graphDetail;
    case '1239':
      graphDetail = {
        title: 'CP Revenue - Shop Supplies',
        Overview:
          'The graph shows the revenue from Shop Supplies for Customer Pay Repair Orders grouped by ' +
          serviceAdvisorText +
          ' over a trend of 13 months.<br>' +
          paytypeText,
        Calculation:
          'Revenue - Shop Supplies = Total Labor Sale where Op Sub ' +
          'Category = Shop Supplies for each Paytype'
      };
      return graphDetail;
    case '1276':
      graphDetail = {
        title: 'Total Revenue',
        Overview:
          'The graph shows the total of Labor and Parts Revenue for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Total Revenue = Sum of Labor Sale + Sum of Parts Sale ' +
          'for the selected Service Advisor grouped by month'
      };
      return graphDetail;
    case '1277':
      graphDetail = {
        title: 'Labor Revenue',
        Overview:
          'The graph shows the total Labor Revenue for all service advisors or ' +
          'a selected service advisor over a 13 month trend.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Revenue = Sum of Labor Sale for the selected Service Advisor grouped by month '
      };
      return graphDetail;
    case '1278':
      graphDetail = {
        title: 'Parts Revenue',
        Overview:
          'The graph shows the total Parts Revenue for all service advisors or ' +
          'a selected service advisor over a 13 month trend.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Parts Revenue = Sum of Parts Sale for the selected Service Advisor grouped by month '
      };
      return graphDetail;
    case '1279':
      graphDetail = {
        title: 'Labor Sold Hours',
        Overview:
          'The graph shows the total Labor Sold Hours for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Sold Hours = Sum of Labor Sold Hours for the selected Service Advisor grouped by month '
      };
      return graphDetail;
    case '1280':
      graphDetail = {
        title: 'RO Count',
        Overview:
          'The graph shows the repair order count for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Repair Order Count = Distinct Count of Repair Orders for the selected Service Advisor grouped by month '
      };
      return graphDetail;
    case '1281':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the total Job Count for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Jobs Only.',
        Calculation:
          'Job Count = Count of CP Jobs for the selected Service Advisor grouped by month '
      };
      return graphDetail;
    case '1282':
      graphDetail = {
        title: 'Labor Gross Profit',
        Overview:
          'The graph shows the Labor Gross Profit for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Jobs Only.',
        Calculation:
          'Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost) for the selected Service Advisor grouped by month'
      };
      return graphDetail;
    case '1283':
      graphDetail = {
        title: 'Labor Gross Profit %',
        Overview:
          'The graph shows the Labor Gross Profit % for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Jobs Only.',
        Calculation:
          'Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost)*100/Sum of Labor Sale ' +
          'for the selected Service Advisor grouped by month'
      };
      return graphDetail;
    case '1284':
      graphDetail = {
        title: 'Parts Gross Profit',
        Overview:
          'The graph shows the Parts Gross Profit for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Jobs Only.',
        Calculation:
          'Parts Gross Profit = (Sum of Parts Sale - Sum of Parts Cost) for the selected Service Advisor grouped by month'
      };
      return graphDetail;
    case '1285':
      graphDetail = {
        title: 'Parts Gross Profit %',
        Overview:
          'The graph shows the Parts Gross Profit % for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Jobs Only.',
        Calculation:
          'Parts Gross Profit % = (Sum of Parts Sale - Sum of Parts Cost)*100/Sum of Parts Sale ' +
          'for the selected Service Advisor grouped by month'
      };
      return graphDetail;
    case '1286':
      graphDetail = {
        title: 'Effective Labor Rate',
        Overview:
          'The graph shows the ELR for all service advisors or ' +
          'a selected service advisor over a 13 month trend. <br>' +
          'Customer Pay Repair Orders Jobs Only.',
        Calculation:
          'Effective Labor Rate = Sum of Labor Sale/Sum of Labor Sold Hours for the selected Service Advisor grouped by month'
      };
      return graphDetail;
    case '1287':
      graphDetail = {
        title: 'Total Revenue',
        Overview:
          'The graph shows the total of Labor and Parts Revenue for all service advisors ' +
          'on a 2 month comparison. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Total Revenue = Sum of Labor Sale + Sum of Parts Sale  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1288':
      graphDetail = {
        title: 'Labor Revenue',
        Overview:
          'The graph shows the total Labor Revenue for all service advisors  on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Revenue = Sum of Labor Sale  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1289':
      graphDetail = {
        title: 'Parts Revenue',
        Overview:
          'The graph shows the total Parts Revenue for all service advisors on a 2 month comparison. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Parts Revenue = Sum of Parts Sale for the selected Service Advisor  grouped by month'
      };
      return graphDetail;
    case '1290':
      graphDetail = {
        title: 'Labor Sold Hours',
        Overview:
          'The graph shows the total Labor Sold Hours for all service advisors on a 2 month comparison. <br>' +
          ' Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Sold Hours = Sum of Labor Sold Hours  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1291':
      graphDetail = {
        title: 'RO Count',
        Overview:
          'The graph shows the repair order count for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Repair Order Count = Distinct Count of Repair Orders  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1292':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the total Job Count for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Job Count = Count of CP Jobs grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1293':
      graphDetail = {
        title: 'Labor Gross Profit',
        Overview:
          'The graph shows the Labor Gross Profit for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost)  grouped by Service Advisor, month '
      };
      return graphDetail;
    case '1294':
      graphDetail = {
        title: 'Labor Gross Profit %',
        Overview:
          'The graph shows the Labor Gross Profit % for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost)*100/Sum of Labor Sale  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1295':
      graphDetail = {
        title: 'Parts Gross Profit',
        Overview:
          'The graph shows the Parts Gross Profit for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Parts Gross Profit = (Sum of Parts Sale - Sum of Parts Cost)  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1296':
      graphDetail = {
        title: 'Parts Gross Profit %',
        Overview:
          'The graph shows the Parts Gross Profit % for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Parts Gross Profit % = (Sum of Parts Sale - Sum of Parts Cost)*100/Sum of Parts Sale  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1297':
      graphDetail = {
        title: 'Effective Labor Rate',
        Overview:
          'The graph shows the ELR for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Effective Labor Rate = Sum of Labor Sale/Sum of Labor Sold Hours grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1298':
      graphDetail = {
        title: 'Parts Markup',
        Overview:
          'The graph shows the Parts Markup for all service advisors on a 2 month comparison.<br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Parts Markup = Sum of Parts Sale/Sum of Parts Cost  grouped by Service Advisor, month'
      };
      return graphDetail;
    case '1299':
      graphDetail = {
        title: 'Total Revenue',
        Overview:
          'The graph shows the total of Labor and Parts Revenue for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Total Revenue = Sum of Labor Sale + Sum of Parts Sale  grouped by Service Advisor, Op Category and month'
      };
      return graphDetail;
    case '1300':
      graphDetail = {
        title: 'Labor Revenue',
        Overview:
          'The graph shows the total Labor Revenue for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Revenue = Sum of Labor Sale grouped by Service Advisor, Op Category and month '
      };
      return graphDetail;
    case '1301':
      graphDetail = {
        title: 'Parts Revenue',
        Overview:
          'The graph shows the total Parts Revenue for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Parts Revenue = Sum of Parts Sale grouped by Service Advisor, Op Category and month '
      };
      return graphDetail;
    case '1302':
      graphDetail = {
        title: 'Labor Sold Hours',
        Overview:
          'The graph shows the total Labor Sold Hours for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Sold Hours = Sum of Labor Sold Hours grouped by Service Advisor, Op Category and month '
      };
      return graphDetail;
    case '1303':
      graphDetail = {
        title: 'RO Count',
        Overview:
          'The graph shows the repair order count for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Repair Order Count = Distinct Count of Repair Orders grouped by Service Advisor, Op Category and month '
      };
      return graphDetail;
    case '1304':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the total Job Count for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Job Count = Count of CP Jobs grouped by Service Advisor, Op Category and month '
      };
      return graphDetail;
    case '1305':
      graphDetail = {
        title: 'Labor Gross Profit',
        Overview:
          'The graph shows the Labor Gross Profit for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Labor Gross Profit = (Sum of Labor Sale - Sum of Labor Cost) grouped by Service Advisor, Op Category and month '
      };
      return graphDetail;
    case '1306':
      graphDetail = {
        title: 'Parts Gross Profit ',
        Overview:
          'The graph shows the Parts Gross Profit  for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Parts Gross Profit = (Sum of Parts Sale - Sum of Parts Cost) grouped by Service Advisor, Op Category and month '
      };
      return graphDetail;
    case '1307':
      graphDetail = {
        title: 'Effective Labor Rate',
        Overview:
          'The graph shows the ELR for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          'Effective Labor Rate = Sum of Labor Sale/Sum of Labor Sold Hours grouped by Service Advisor, Op Category and month'
      };
      return graphDetail;
    case '1308':
      graphDetail = {
        title: 'Parts Markup',
        Overview:
          'The graph shows the Parts Markup for all service advisors for the Op Categories - ' +
          'Repair, Maintenance and Competitive for a selected month. <br>' +
          'Customer Pay Repair Orders Only.',
        Calculation:
          ' Parts Markup = Sum of Parts Sale/Sum of Parts Cost grouped by Service Advisor, Op Category and month'
      };
      return graphDetail;
    case '1315':
      graphDetail = {
        title: 'Parts Markup',
        Overview:
          'The graph shows the Parts Markup for all service advisors or a selected service advisor ' +
          'over a 13 month trend. Customer Pay Repair Jobs Only.',
        Calculation:
          ' Parts Markup = Sum of Parts Sale/Sum of Parts Cost for the selected Service Advisor grouped by month'
      };
      return graphDetail;

    case '1249':
      graphDetail = {
        title: 'Actual Tech Efficiency',
        Overview:
          'The graph shows the Actual Efficiency of Technicians for all Customer Pay CP Repair Orders over a 13 month trend.',
        Calculation:
          'Actual Efficiency = CP Flat Rate Hours/ CP Actual Tech Hours Punched as percentage'
      };
      return graphDetail;

    case '1250':
      graphDetail = {
        title: 'Estimated Tech Efficiency',
        Overview:
          ' The graph shows the Estimated Efficiency of Technicians for all Customer Pay CP Repair Orders over a 13 month trend.',
        Calculation:
          'Available Hours = 8 working hours per day * No of days worked by the Technician <br>' +
          'Estimated Efficiency = CP Flat Rate Hours/Total Available Hours for each technician'
      };
      return graphDetail;

    case '1251':
      graphDetail = {
        title: 'Job Count %',
        Overview:
          'The graph shows the Job Counts for the following as a percentage of the total CP Job Counts over a 13 month trend- <br>' +
          'Flat Rate Hours No Tech Hours <br> ' +
          'Flat Rate Hours with Tech Hours <br>' +
          'Tech Hours No Flat Rate Hours <br> ' +
          'No Tech Hours No Flat Rate Hours',
        Calculation:
          'Flat Rate Hours No Tech Hours <br> ' +
          'Job Count% = Count of Jobs with Flat Rate Hours No Tech Hours/Total CP Job Count <br>' +
          'Flat Rate Hours with Tech Hours <br>' +
          'Job Count% = Count of Jobs with Flat Rate Hours with Tech Hours/Total CP Job Count <br>' +
          'Tech Hours No Flat Rate Hours <br>' +
          'Job Count% = Count of Jobs with Tech Hours No Flat Rate Hours/Total CP Job Count <br>' +
          'No Tech Hours No Flat Rate Hours <br>' +
          'Job Count% = Count of Jobs with No Tech Hours No Flat Rate Hours/Total CP Job Count'
      };
      return graphDetail;

    case '1252':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the Job Counts for the following over a 13 month trend- <br>' +
          'Flat Rate Hours No Tech Hours <br> ' +
          'Flat Rate Hours with Tech Hours <br>' +
          'Tech Hours No Flat Rate Hours <br> ' +
          'No Tech Hours No Flat Rate Hours',
        Calculation:
          'Flat Rate Hours No Tech Hours <br> ' +
          'Job Count = Count of Jobs with Flat Rate Hours No Tech Hours <br>' +
          'Flat Rate Hours with Tech Hours <br>' +
          'Job Count = Count of Jobs with Flat Rate Hours with Tech Hours <br>' +
          'Tech Hours No Flat Rate Hours <br>' +
          'Job Count = Count of Jobs with Tech Hours No Flat Rate Hours <br>' +
          'No Tech Hours No Flat Rate Hours <br>' +
          'Job Count = Count of Jobs with No Tech Hours No Flat Rate Hours'
      };
      return graphDetail;
    case '1264':
      graphDetail = {
        title: 'Revenue by Technicians',
        Overview:
          'The graph shows the Total Revenue of technicians on a two month comparison basis. Customer Pay Repair Orders Only.',
        Calculation:
          'Revenue = Sum of Labor Sale + Sum of Parts Sale for each technician grouped by month'
      };
      return graphDetail;
    case '1265':
      graphDetail = {
        title: 'Hours sold by Technicians',
        Overview:
          '  The graph shows the Labor Hours Sold for technicians on a two month comparison basis. Customer Pay Repair Orders Only.',
        Calculation:
          'Hours Sold = Sum of Labor Sold Hours for each Technician grouped by month'
      };
      return graphDetail;

    case '1266':
      graphDetail = {
        title: 'Actual Tech Efficiency',
        Overview:
          ' The graph shows the actual efficiency of all technicians on a two month comparison basis. Customer Pay Repair Orders Only',
        Calculation:
          'Actual Efficiency = CP Flat Rate Hours/ CP Actual Tech Hours Punched'
      };
      return graphDetail;

    case '1267':
      graphDetail = {
        title: 'Estimated Technician Efficiency',
        Overview:
          'The graph shows the Estimated Efficiency of Technicians on a two month comparison basis.Customer Pay Repair Orders Only',
        Calculation:
          'Available Hours = 8 working hours per day * No of days worked by the Technician' +
          'Estimated Efficiency = CP Flat Rate Hours/Total Available Hours for each technician'
      };
      return graphDetail;
    case '1268':
      graphDetail = {
        title: 'Job Count % - Flat Rate Hours With Tech Hours',
        Overview:
          'The graph shows the Job Counts Percentage for  Flat Rate Hours With Tech Hours on a two month comparison basis.',
        Calculation: 'Job Count = Count of Jobs with Flat Rate Hours Tech Hours'
      };
      return graphDetail;

    case '1269':
      graphDetail = {
        title: 'Job Count % - Flat Rate Hours With No Tech Hours',
        Overview:
          'The graph shows the Job Counts Percentage for  Flat Rate Hours With no Tech Hours on a two month comparison basis.',
        Calculation:
          'Job Count = Count of Jobs with Flat Rate Hours with No Tech Hours'
      };
      return graphDetail;
    case '1270':
      graphDetail = {
        title: 'Job Count % - Tech Hours With No Flat Rate Hours',
        Overview:
          'The graph shows the Job Counts Percentage for  Tech Hours With No Flat Rate on a two month comparison basis.',
        Calculation:
          'Job Count = Count of Jobs with Tech Hours with No Flat Rate Hours'
      };
      return graphDetail;

    case '1271':
      graphDetail = {
        title: 'Job Count % - No Flat Rate Hours and No Tech Hours',
        Overview:
          ' The graph shows the Job Counts Percentage for  No Tech Hours With No Flat Rate on a two month comparison basis.',
        Calculation:
          'Job Count = Count of Jobs with No Tech Hours with No Flat Rate Hours'
      };
      return graphDetail;

    case '1272':
      graphDetail = {
        title: 'Job Count - Flat Rate Hours With Tech Hours',
        Overview:
          'The graph shows the Job Counts Percentage for  Tech Hours With Flat Rate on a two month comparison basis.',
        Calculation:
          'Job Count = Count of Jobs with  Tech Hours with  Flat Rate Hours'
      };
      return graphDetail;

    case '1273':
      graphDetail = {
        title: 'Job Count - Flat Rate Hours With No Tech Hours',
        Overview:
          'The graph shows the Job Counts Percentage for  No Tech Hours With Flat Rate on a two month comparison basis.',
        Calculation:
          'Job Count = Count of Jobs with No Tech Hours with  Flat Rate Hours'
      };
      return graphDetail;

    case '1274':
      graphDetail = {
        title: 'Job Count - Tech Hours With No Flat Rate Hours',
        Overview:
          'The graph shows the Job Counts Percentage for   Tech Hours With No Flat Rate on a two month comparison basis.',
        Calculation:
          'Job Count = Count of Jobs with   Tech Hours with No  Flat Rate Hours'
      };
      return graphDetail;

    case '1275':
      graphDetail = {
        title: 'Job Count - No Flat Hours and No Tech Hours',
        Overview:
          'The graph shows the Job Counts Percentage for  No Tech Hours With Flat Rate on a two month comparison basis.',
        Calculation:
          'Job Count = Count of Jobs with  No Tech Hours with No  Flat Rate Hours'
      };
      return graphDetail;
    case '1243':
      graphDetail = {
        title: 'Labor Sale',
        Overview:
          'The graph shows the Labor revenue for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Labor Sale = Sum of CP Labor Sale grouped by month and Op Category'
      };
      return graphDetail;
    case '1244':
      graphDetail = {
        title: 'Labor Sold Hours',
        Overview:
          'The graph shows the Labor Sold Hours for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.',
        Calculation:
          'Labor Sold Hours = Sum of CP Labor Sold Hours grouped by month and Op Category'
      };
      return graphDetail;
    case '1245':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Job Count = Sum of CP Jobs grouped by month and Op Category'
      };
      return graphDetail;
    case '1246':
      graphDetail = {
        title: 'Workmix %',
        Overview:
          'The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Work Mix Percentage = Sold Hours grouped by Op Category/ Total CP Sold Hours'
      };
      return graphDetail;
    case '1247':
      graphDetail = {
        title: 'ELR',
        Overview:
          'The graph shows the Effective Labor Rate as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Effective Labor Rate = CP Labor Sale grouped by Op Category/CP Labor Sold hours grouped by Op Category'
      };
      return graphDetail;
    case '1248':
      graphDetail = {
        title: 'Gross Profit %',
        Overview:
          'The graph shows the Labor Gross Profit  as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Labor Gross Profit % = CP Labor Sale - CP Labor Cost grouped by Op Category / CP Labor Sale grouped by Op Category'
      };
      return graphDetail;
    case '1253':
      graphDetail = {
        title: 'Parts Sale',
        Overview:
          'The graph shows the Parts revenue for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Parts Sale = Sum of CP Parts Sale grouped by month and Op Category'
      };
      return graphDetail;
    case '1254':
      graphDetail = {
        title: 'Parts Cost',
        Overview:
          'The graph shows the Parts Cost for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend.',
        Calculation:
          'Parts Cost = Sum of CP Parts Cost grouped by month and Op Category'
      };
      return graphDetail;
    case '1255':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Job Count = Sum of CP Jobs grouped by month and Op Category'
      };
      return graphDetail;
    case '1256':
      graphDetail = {
        title: 'Workmix %',
        Overview:
          'The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Work Mix % = Parts Cost grouped by Op Category/ Total CP Parts Cost'
      };
      return graphDetail;
    case '1257':
      graphDetail = {
        title: 'Parts Markup',
        Overview:
          'The graph shows the Parts Markup  for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          'Parts Markup = CP Parts Sale grouped by Op Category/CP Parts Cost grouped by Op Category'
      };
      return graphDetail;
    case '1258':
      graphDetail = {
        title: 'Gross Profit %',
        Overview:
          'The graph shows the Parts Gross Profit  as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders over a 13 month trend',
        Calculation:
          ' Parts Gross Profit % = CP Parts Sale - CP Parts Cost grouped by Op Category / CP Parts Sale grouped by Op Category'
      };
      return graphDetail;
    case '1259':
      graphDetail = {
        title: 'Sold Hours',
        Overview:
          'The graph shows the Labor Sold Hours for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Labor Sold Hours = Sum of CP Labor Sold Hours grouped by month and Op Category'
      };
      return graphDetail;
    case '1260':
      graphDetail = {
        title: 'Workmix %',
        Overview:
          'The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Work Mix Percentage = Sold Hours grouped by Op Category/ Total CP Sold Hours'
      };
      return graphDetail;
    case '1261':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Job Count = Sum of CP Jobs grouped by month and Op Category'
      };
      return graphDetail;
    case '1262':
      graphDetail = {
        title: 'ELR',
        Overview:
          'The graph shows the Effective Labor Rate as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Effective Labor Rate = CP Labor Sale grouped by Op Category/CP Labor Sold Hours grouped by Op Category'
      };
      return graphDetail;
    case '1263':
      graphDetail = {
        title: 'Gross Profit %',
        Overview:
          'The graph shows the Labor Gross Profit as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Labor Gross Profit % = CP Labor Sale - CP Labor Cost grouped by Op Category / CP Labor Sale grouped by Op Category'
      };
      return graphDetail;
    case '1309':
      graphDetail = {
        title: 'Parts Cost',
        Overview:
          'The graph shows the Parts Cost for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          ' Parts Cost = Sum of CP Parts Cost grouped by month and Op Category'
      };
      return graphDetail;
    case '1310':
      graphDetail = {
        title: 'Workmix %',
        Overview:
          ' The graph shows the Workmix as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Work Mix % = Parts Cost grouped by Op Category/ Total CP Parts Cost'
      };
      return graphDetail;
    case '1311':
      graphDetail = {
        title: 'Job Count',
        Overview:
          'The graph shows the Job Count for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Job Count = Sum of CP Jobs grouped by month and Op Category'
      };
      return graphDetail;
    case '1312':
      graphDetail = {
        title: 'Parts Markup',
        Overview:
          'The graph shows the Parts Markup for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Parts Markup = CP Parts Sale grouped by Op Category/CP Parts Cost grouped by Op Category'
      };
      return graphDetail;
    case '1313':
      graphDetail = {
        title: 'Gross Profit %',
        Overview:
          'The graph shows the Parts Gross Profit  as a percentage for the Op Categories - Repair, Competitive and Maintenance for Customer Pay Repair Orders on a two month comparison.',
        Calculation:
          'Parts Gross Profit % = CP Parts Sale - CP Parts Cost grouped by Op Category / CP Parts Sale grouped by Op Category'
      };
      return graphDetail;
    case '1316':
      graphDetail = {
        title: 'MPI Penetration Percentage',
        Overview:
          'The graph shows the MPI penetration as a percentage of total Repair orders over a 13 month trend ' +
          serviceAdvisorText +
          '.',
        Calculation:
          "MPI Penetration = Count of Repair Orders where OpCode = 'MPVI' *100 / Total Repair Orders " +
          serviceAdvisorText
      };
      return graphDetail;
    case '1317':
      graphDetail = {
        title: 'Menu Penetration Percentage',
        Overview:
          'The graph shows the Menu Penetration as a percentage of total Customer Pay Repair orders over a 13 month trend ' +
          serviceAdvisorText +
          '.',
        Calculation:
          "Menu Penetration = Count of Repair Orders where OpCode = Dealer Menu Opcodes * 100/ Count of Repair Orders where PayType = 'Customer Pay' " +
          serviceAdvisorText
      };
      return graphDetail;
    case '1326':
      graphDetail = {
        title: 'Repair Order Count',
        Overview:
          'The main graph shows the Total Count of Customer Pay Repair Orders over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '. <br>' +
          ' In the detailed view, the graphs are categorized into 7 based on the type of the payment.<br>' +
          'Repair Order Count - Combined: Repair Orders that are paid with any of the  means of Customer Pay, Internal Pay, Warranty, Maintenance Plan, Factory Service Contract or Extended Service Contract.<br>' +
          'Repair Order Count - Customer Pay: Repair Orders with Customer Pay .<br>' +
          'Repair Order Count - Warranty: Repair Orders that are covered under Warranty .<br>' +
          'Repair Order Count - Internal: Repair Orders with Internal pay .<br>' +
          'Repair Order Count - Maintenance Plan: Repair Orders that are covered under Maintenance Plan.<br>' +
          'Repair Order Count - Factory Service Contract: Repair Orders that are covered under Factory Service Contract.<br>' +
          'Repair Order Count - Extended Service Contract: Repair Orders that are covered under Extended Service Contract .',
        Calculation:
          'Repair Order Count = count of all Repair Orders grouped by ' +
          serviceAdvisorText +
          ' where Parts Sale not equal to zero.<br>' +
          'All the opcode categories - Repair, Maintenance, Competitive are considered for these graphs'
      };
      return graphDetail;

    case '1334':
      graphDetail = {
        title: 'CP Parts Markup - Repair And Competitive',
        Overview:
          'The graph shows the Parts Markup as a weighted average of the Op Categories - ' +
          'Repair and Competitive over a 3 year trend grouped by ' +
          serviceAdvisorText +
          '.<br>',
        Calculation:
          'Parts Markup  = Sum of Parts Sale / Sum of Parts Cost where Op Category = Repair & Competitive'
      };
      return graphDetail;
    case '1345':
      graphDetail = {
        title: 'Tech Productivity - Hours Sold',
        Overview:
          'The graph shows the hours flagged for the selected Technician for all Customer Pay  Repair Orders over 13 months on a weekly basis.',
        Calculation:
          'Tech Productivity - Hours Sold = Total Hours Flagged on the Technician'
      };
      return graphDetail;
    case '1357':
      graphDetail = {
        title: 'CP Average RO Open Date',
        Overview:
          'The graph shows the CP Average RO Open Date - Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract and Extended Service Plan as a percentage of the total Labor Sold Hours for Service Repair Orders.',
        Calculation:
          'Labor Sold Hours % - Customer Pay = Total Customer Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan<br>' +
          'Labor Sold Hours % - Internal = Total Internal Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan<br>' +
          'Labor Sold Hours % - Warranty = Total Warranty Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan <br>' +
          'Labor Sold Hours % - Maintenance Plan = Total Maintenance Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan <br>' +
          'Labor Sold Hours % - Factory Service Contract = Total Factory Service Contract Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan <br>' +
          'Labor Sold Hours % - Extended Service Plan = Total Extended Service Plan Pay Labor Sold Hours *100/ Total Labor Sold Hours where Paytype = Customer Pay, Internal, Warranty, Maintenance Plan, Factory Service Contract & Extended Service Plan'
      };
      return graphDetail;
  }
};

const GraphDetails = ({ realm, dbdName, keycloak }) => {
  const history = useHistory();
  const classes = useStyles();
  var Dealer = process.env.REACT_APP_DEALER;
  const session = useSelector(state => state.session);
  const [markdown, setMarkdown] = useState([]);
  const [overview, setOverview] = useState('');
  const [calculation, setCalculation] = useState([]);
  const [overviews, setOverviews] = useState('');
  const [calculations, setCalculations] = useState([]);
  const [isEdit, setIsEdit] = useState(false);
  const [rowUpdated, setRowUpdated] = useState(false);
  const [open, setOpen] = React.useState(false);
  const [chartName, setChartName] = useState('');
  var serviceAdvisor = session.serviceAdvisor.toString();
  var flag = 0;
  if (serviceAdvisor != 'All') {
    flag = 1;
  }
  let chartId = history.location.search
    .split('?chartId=')
    .pop()
    .split('?')[0];
  let chartDetails = getChartDetails(chartId, flag, realm);
  let calculationDetails = chartDetails
    ? chartDetails.Calculation
      ? chartDetails.Calculation.split('<br>')
      : ''
    : '';

  useEffect(() => {
    setChartName(getChartName(chartId));
    getChartMarkdown(chartId, callback => {
      //const data = JSON.parse(JSON.stringify(callback[0].markdown));
      const data = JSON.parse(callback[0].markdown);

      setOverview(data.overview);
      // setChartName(callback[0].chartName);
      // setCalculation( chartDetails.Calculation.split('<br>'));
      setCalculation(data.calculation);

      setOverviews(data.overview);
      setCalculations(data.calculation);

      setMarkdown(data);
    });
  }, []);
  const handleFormEdit = () => {
    setIsEdit(true);
  };
  const handleFormClose = () => {
    setIsEdit(false);
  };

  const handleMarkdownEdit = (index, e) => {
    if (index == 'overview') {
      setOverview(e.target.value);
    } else {
      let newArr = { ...calculation }; // copying the old datas array
      newArr[index] = e.target.value; // replace e.target.value with whatever you want to change it to

      setCalculation(newArr);
    }
  };
  const handleMarkdownSubmit = () => {
    // console.log('newArr', overview, calculation);
    const data = {
      overview: overview,
      calculation: calculation
    };
    let url = window.location.href;
    var arr = url.split('/');
    const settings = { ...process.env };
    if (process.env.REACT_APP_PRODUCTION == 'false') {
      var env =
        'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('-')[0].toUpperCase();
    } else {
      var env =
        'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('.')[0].toUpperCase();
    }
    const realmid = settings[env];
    const client = makeApolloClient;
    if (
      overviews != overview ||
      JSON.stringify(calculations) != JSON.stringify(calculation)
    ) {
      client
        .mutate({
          mutation: UPDATE_CHART_MASTER_MARKDOWN,
          variables: {
            chart_id: chartId,
            inUpdatedOnRealm: realmid,
            markdown: JSON.stringify(data)
          }
        })
        .then(result => {
          setOpen(true);
          setRowUpdated(true);
          setIsEdit(false);
          setOverviews(overview);
          setCalculations(calculation);
        });
    } else {
      setIsEdit(false);
    }
  };
  let overviewDetails = chartDetails ? chartDetails.Overview.split('<br>') : '';

  //var overviewArr1= overview ?overview.replace(".",".#").split("#"):'';
  let overviewArr = overview ? overview.split('\n') : '';
  const result = (
    <Card
      bordered={false}
      className={
        chartId == 920 ||
        chartId == 1133 ||
        chartId == 1138 ||
        chartId == 1049 ||
        chartId == 952 ||
        chartId == 1143 ||
        chartId == 1326 ||
        chartId == 935 ||
        chartId == 944
          ? classes.cardRootUpdated
          : classes.cardRoot
      }
    >
      <CardHeader
        title={
          <Typography variant="h5" color="primary">
            {chartName ? chartName : ''}
          </Typography>
        }
        action={
          !isEdit &&
          keycloak.realmAccess.roles !== 'undefined' &&
          keycloak.realmAccess.roles.includes('superadmin') === true ? (
            <Button
              style={{
                //  backgroundColor: Dealer === 'Armatus'   ? '#003d6b' : '#cccccc',
                backgroundColor:
                  (keycloak.realmAccess.roles !== 'undefined' &&
                    keycloak.realmAccess.roles.includes('client') === true) ||
                  keycloak.realmAccess.roles.includes('user') == true
                    ? '#cccccc'
                    : '#003d6b',
                color: '#FFF',
                marginTop: 0,
                height: 22
              }}
              onClick={handleFormEdit}
              disabled={
                (keycloak.realmAccess.roles !== 'undefined' &&
                  keycloak.realmAccess.roles.includes('client') === true) ||
                keycloak.realmAccess.roles.includes('user') == true
                  ? true
                  : false
              }
            >
              <EditIcon color="white" style={{ width: 17 }} />
              <span
                style={{
                  textTransform: 'capitalize',
                  fontSize: 12,
                  paddingLeft: 4
                }}
              >
                Edit
              </span>
            </Button>
          ) : (
            ''
          )
        }
      ></CardHeader>
      <Divider />
      <PerfectScrollbar>
        {rowUpdated && (
          <Collapse in={open}>
            <Alert
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => {
                    setOpen(false);
                  }}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
              style={{ margin: '10px 20px' }}
            >
              Chart info updated successfully!
            </Alert>
          </Collapse>
        )}
        <CardContent classes={{ root: classes.cardContent }}>
          <Typography variant="h6" color="textSecondary">
            Overview:
          </Typography>
          {isEdit ? (
            <TextareaAutosize
              aria-label="minimum height"
              minRows={3}
              placeholder="Minimum 3 rows"
              defaultValue={overview}
              onChange={e => handleMarkdownEdit('overview', e)}
              style={{ width: '100%' }}
            />
          ) : (
            <div id="container" className={classes.overView}>
              {overview != ''
                ? Object.entries(overviewArr).map((calc, val) => (
                    <div className={classes.calcRules}>{overviewArr[val]}</div>
                  ))
                : ''}
            </div>
          )}
          <Divider style={{ marginBottom: 8 }}></Divider>

          <div
            style={{
              display: calculation == '' ? 'none' : 'inline-block'
            }}
          >
            <Typography variant="h6" color="textSecondary">
              Calculation:
            </Typography>
          </div>

          <div
            className={classes.calculation}
            //   style={{ display: calculationDetails == '' ? 'none' : 'block' }}
          >
            {Object.entries(calculation).length > 0
              ? Object.entries(calculation).map((calc, val) =>
                  isEdit ? (
                    <TextareaAutosize
                      aria-label="minimum height"
                      minRows={3}
                      placeholder="Minimum 3 rows"
                      onChange={e => handleMarkdownEdit(val, e)}
                      value={calculation[val]}
                      style={{ width: '100%', marginBottom: 10 }}
                    />
                  ) : (
                    <div className={classes.calcRules}>{calculation[val]}</div>
                  )
                )
              : ''}
          </div>
        </CardContent>

        {isEdit && (
          <>
            <Divider style={{ marginBottom: 8 }}></Divider>
            <CardActions style={{ float: 'right', marginBottom: 20 }}>
              <Button size="small" color="primary" onClick={handleFormClose}>
                Cancel
              </Button>
              <Button
                style={{
                  backgroundColor: Dealer === 'Armatus' ? '#003d6b' : '#C2185B',
                  color: '#FFF',
                  marginTop: 0
                }}
                size="small"
                onClick={handleMarkdownSubmit}
                color="primary"
              >
                Update
              </Button>
            </CardActions>
          </>
        )}
      </PerfectScrollbar>
    </Card>
  );

  return result;
};

export default withKeycloak(GraphDetails);
