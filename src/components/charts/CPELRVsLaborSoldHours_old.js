import { Card, CardContent, CardHeader, Divider } from '@material-ui/core';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import { Scatter } from 'react-chartjs-2';

import { getChartConfiguration, getChartTitle } from '../../utils/Utils';
import { useHistory } from 'react-router';
import MoreActions from '../MoreActions';
import { getSubHeader } from 'src/components/ViewGraphDetailsAction';
import ChartDialog from 'src/components/Dialog';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import $ from 'jquery';
import ItemizationDataGrid from './ItemizationDataGrid';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import DashboardActions from 'src/components/DashboardActions';
import 'src/styles.css';

var lodash = require('lodash');
const useStyles = makeStyles(() => ({
  cardContainer: {
    height: '100%',
    borderRadius: 0,
    border: '1px solid #003d6b',
    paddingBottom: 20,
    marginRight: 5,
    width: '100%'
    // '@media (max-width: 1920px)': {
    //   width: '84%'
    // },

    // '@media (min-width: 2304px)': {
    //   width: '90%'
    // },
    // '@media (max-width: 1280px)': {
    //   width: '81%'
    // },
    // '@media (max-width: 1440px)': {
    //   width: '81%'
    // }
  },
  cardContainerDetail: {
    height: '100%',
    borderRadius: 0,
    border: '1px solid #003d6b',
    paddingBottom: 20,
    width: '100%'
  },
  cardHeader: {
    borderBottom: '1px solid #003d6b'
  },
  chartContainter: {
    backgroundColor: '#FFF',
    position: 'relative',
    margin: 'auto',
    height:
      window.location.pathname == '/GraphDetailsView'
        ? 340
        : window.innerHeight - 280 + 'px'
  }
}));
const CPELRVsLaborSoldHours = props => {
  const history = useHistory();
  let chartTitle = getChartTitle(1090);
  const [isLoading, setIsLoading] = useState(false);

  const [zoomPoints, setZoomPoints] = useState({});
  const [ctx, setCtx] = useState({});

  const [filteredArr, setFilteredArr] = useState(props.data);
  const [dataArr, setAllData] = useState([]);
  const [metaArr, setMetaArr] = useState([]);
  const [legendArr, setLegendArr] = useState([]);
  const [open, setOpen] = useState(false);
  const [panEnabled, setPanStatus] = useState(false);
  const [elementClicked, setElementClicked] = useState(false);

  const [layout, setLayout] = useState(props.layout);
  const chartPopup = () => {
    setOpen(true);
    setResetDashboard(true);
  };
  const enablePan = val => {
    setPanStatus(val);
  };
  useEffect(() => {
    setZoomPoints({});
    setFilteredArr([]);
  }, [props.isReloaded]);
  const handleClose = () => {
    setOpen(false);

    props.handleClosePopup(1);
  };

  useEffect(() => {
    setAllData([]);
    setFilteredArr([]);
    setLegendArr([]);

    setIsLoading(true);
    if (!lodash.isEmpty(ctx)) {
      let data = ctx.data;
      let legendItems = ctx.legend.legendItems;

      ctx.data.datasets.forEach(dataset => {
        dataset.hidden = false;
      });
      [
        ctx.getDatasetMeta(0),
        ctx.getDatasetMeta(1),
        ctx.getDatasetMeta(2)
      ].forEach(function(meta) {
        meta.hidden = meta.hidden == true ? false : null;
      });

      ctx.stop();
      ctx.update();
    }

    setElementClicked(true);
  }, [props.tabSelection, props.resetClicked]);

  const handleElementClicked = () => {
    setElementClicked(false);
  };

  props.handleResetClicked();
  const zoomOptions = {
    pan: {
      enabled: false,
      mode: 'xy'
    },
    zoom: {
      enabled: props.parent == 'Details' ? false : true,

      mode: 'xy',
      drag: {
        enabled: true
      },
      wheel: {
        enabled: false,
        modifierKey: 'alt'
      },
      pinch: {
        enabled: false
      },
      onZoomComplete: chart => {
        startFetch(chart);
        // $('.chartjs-render-monitor').mousemove(function(e) {
        //   e.stopPropagation();
        // });

        // canv.addEventListener('mousemove', e => {
        //   console.log({ x: e.offsetX, y: e.offsetY, z: e });
        // });
        // $('.chartjs-render-monitor').css('pointer-events', 'none');
        // chart.chart.ctx.canvas.removeEventListener(
        //   'wheel',
        //   chart.chart._wheelHandler
        // );
      }
    }
  };
  const startFetch = ({ chart }) => {
    const { min, max } = chart.scales['x-axis-1'];
    const minY = chart.scales['y-axis-1'].min;
    const maxY = chart.scales['y-axis-1'].max;

    // clearTimeout(timer);
    // timer = setTimeout(() => {
    setZoomPoints({
      minX: min, // minimum value
      maxX: max,
      minY: minY, // minimum value
      maxY: maxY
    });
    setCtx(chart);

    chart.data = fetchData(min, max, minY, maxY);
    chart.options.scales = {
      yAxes: [
        {
          display: true,
          scaleLabel: {
            display: true,
            labelString: 'ELR'
          },
          ticks: {
            min: minY, // minimum value
            max: maxY // maximum value
          }
        }
      ],
      xAxes: [
        {
          display: true,
          scaleLabel: {
            display: true,
            labelString: 'Sold Hours'
          },
          ticks: {
            min: min, // minimum value
            max: max // maximum value
          }
        }
      ]
    };

    chart.stop(); // make sure animations are not running
    chart.update();
    // props.handleChartReset(1);
    // $('.chartjs-render-monitor').bind('wheel mousewheel', function(e) {
    //   e.preventDefault();
    // });
    // window.addEventListener(
    //   'scroll',
    //   function() {
    //     $('.chartjs-render-monitor').bind('wheel mousewheel', function(e) {
    //       e.preventDefault();
    //     });
    //   },
    //   true
    // );
    // }, 1111500);
  };

  let allData = props.data;

  const fetchData = (x1, x2, y1, y2) => {
    var data = [];

    let elrData = lodash.filter(allData.datasets[2].data, item => {
      return (
        Number(item.y) >= y1.toFixed(2) &&
        Number(item.y) <= y2.toFixed(2) &&
        Number(item.x) >= x1.toFixed(2) &&
        Number(item.x) <= x2.toFixed(2)
      );
    });
    let elrDatasets = {
      label: 'ELR Repair',
      data: elrData,
      fill: true,
      fillOpacity: 0.3,
      backgroundColor: 'rgba(4, 147, 114,0.4)',
      borderColor: 'rgba(4, 147, 114, 1)',
      borderCapStyle: 'butt',
      borderDash: [],
      hidden: legendArr.indexOf(2) > -1 ? true : null,
      borderDashOffset: 0.0,
      borderJoinStyle: 'miter',
      pointBorderColor: 'rgba(4, 147, 114, 1)',
      pointBackgroundColor: '#35af557a',
      pointBorderWidth: 0.5,
      pointHoverRadius: 5,
      pointHoverBackgroundColor: 'rgba(4, 147, 114, 1)',
      pointHoverBorderColor: 'rgba(220,220,220,1)',
      pointHoverBorderWidth: 0,
      pointRadius: 5,
      pointHitRadius: 10,
      type: 'scatter',
      lineTension: 0,
      pointStyle: 'rectRot'
    };
    let mainData = lodash.filter(allData.datasets[1].data, item => {
      return (
        Number(item.y) >= y1.toFixed(2) &&
        Number(item.y) <= y2.toFixed(2) &&
        Number(item.x) >= x1.toFixed(2) &&
        Number(item.x) <= x2.toFixed(2)
      );
    });
    let mainDatasets = {
      label: 'ELR Maintenance',
      data: mainData,
      hidden: legendArr.indexOf(1) > -1 ? true : null,
      fill: true,
      fillOpacity: 0.3,
      backgroundColor: 'rgba(220,57,18,0.5)',
      borderColor: 'rgba(220,57,18,1)',
      borderCapStyle: 'butt',
      borderDash: [],
      borderDashOffset: 0.0,
      borderJoinStyle: 'miter',
      pointBorderColor: 'rgba(220,57,18,1)',
      pointBackgroundColor: '#f17f7fa8',
      pointBorderWidth: 0.5,
      pointHoverRadius: 5,
      pointHoverBackgroundColor: 'rgba(220,57,18,1)',
      pointHoverBorderColor: 'rgba(220,220,220,1)',
      pointHoverBorderWidth: 0,
      pointRadius: 5,
      pointHitRadius: 10,
      type: 'scatter',
      lineTension: 0,
      // hidden: 'boolean',
      pointStyle: 'rect'
    };
    let compData = lodash.filter(allData.datasets[0].data, item => {
      return (
        Number(item.y) >= y1.toFixed(2) &&
        Number(item.y) <= y2.toFixed(2) &&
        Number(item.x) >= x1.toFixed(2) &&
        Number(item.x) <= x2.toFixed(2)
      );
    });
    let compDatasets = {
      label: 'ELR Competitive',
      data: compData,
      hidden: legendArr.indexOf(0) > -1 ? true : null,
      fill: true,
      fillOpacity: 0.3,
      backgroundColor: 'rgba(75,192,192,0.4)',
      borderColor: 'rgba(75,192,192,1)',
      borderCapStyle: 'butt',
      borderDash: [],
      borderDashOffset: 0.0,
      borderJoinStyle: 'miter',
      pointBorderColor: 'rgba(75,192,192,1)',
      pointBackgroundColor: '#0389fc61',
      pointBorderWidth: 0.5,
      pointHoverRadius: 5,
      pointHoverBackgroundColor: 'rgba(75,192,192,1)',
      pointHoverBorderColor: 'rgba(220,220,220,1)',
      pointHoverBorderWidth: 0,
      pointRadius: 5,
      pointHitRadius: 10,
      type: 'scatter',
      lineTension: 0
    };

    data = data.concat(elrData, mainData, compData);
    let labels = data.map(c =>
      c.category == 'ELR Competitive'
        ? 'COMPETITIVE'
        : c.category == 'ELR Maintenance'
        ? 'MAINTENANCE'
        : 'REPAIR'
    );
    let data1 = {
      labels: labels,
      datasets: [compDatasets, mainDatasets, elrDatasets]
    };

    setFilteredArr(data1);
    data = data.map(item => {
      return {
        lbrsoldhours: item.label,
        elr: item.y,
        category:
          item.category == 'ELR Repair'
            ? 0
            : item.category == 'ELR Competitive'
            ? 1
            : 2
      };
    });
    setAllData(data);
    setElementClicked(true);
    return data1;
  };

  const options = getChartConfiguration(
    18,
    [50, 300],
    false,
    [1, 20],
    !lodash.isEmpty(filteredArr) ? filteredArr : props.data,
    1090,
    '',
    '',
    '',
    !lodash.isEmpty(filteredArr) ? true : false,
    'scatter-plot'
  );
  options.plugins = {
    zoom: zoomOptions,
    datalabels: { display: false }
  };
  options.legend = {
    position: 'bottom',
    align: 'center',
    labels: {
      boxWidth: 12,
      usePointStyle: true
    },
    onClick: function(e, legendItem) {
      var index = legendItem.datasetIndex;

      var arr = legendArr;

      if (arr.indexOf(index) > -1) {
        arr = arr.filter(function(item) {
          return item !== index;
        });
      } else {
        arr.unshift(index);
      }

      setCtx(this.chart);
      setLegendArr(arr);
      var ci = this.chart;
      var meta = ci.getDatasetMeta(index);
      setMetaArr(state => [...state, meta]);
      // See controller.isDatasetVisible comment
      meta.hidden =
        meta.hidden == null ? !ci.data.datasets[index].hidden : null;

      let x1, x2, y1, y2;
      if (!lodash.isEmpty(filteredArr)) {
        if (lodash.isEmpty(zoomPoints)) {
          x1 = this.chart.scales['x-axis-1'].min;
          x2 = this.chart.scales['x-axis-1'].max;
          y1 = this.chart.scales['y-axis-1'].min;
          y2 = this.chart.scales['y-axis-1'].max;
        } else {
          x1 = zoomPoints.minX;
          x2 = zoomPoints.maxX;
          y1 = zoomPoints.minY;
          y2 = zoomPoints.maxY;
        }

        let elrData = lodash.filter(ci.data.datasets[2].data, item => {
          return (
            Number(item.y) >= y1.toFixed(2) &&
            Number(item.y) <= y2.toFixed(2) &&
            Number(item.x) >= x1.toFixed(2) &&
            Number(item.x) <= x2.toFixed(2)
          );
        });
        let elrDatasets = {
          label: 'ELR Repair',
          data: elrData,
          fill: true,
          fillOpacity: 0.3,
          backgroundColor: 'rgba(4, 147, 114,0.4)',
          borderColor: 'rgba(4, 147, 114, 1)',
          borderCapStyle: 'butt',
          borderDash: [],
          hidden: arr.indexOf(2) > -1 ? true : null,
          borderDashOffset: 0.0,
          borderJoinStyle: 'miter',
          pointBorderColor: 'rgba(4, 147, 114, 1)',
          pointBackgroundColor: '#35af557a',
          pointBorderWidth: 0.5,
          pointHoverRadius: 5,
          pointHoverBackgroundColor: 'rgba(4, 147, 114, 1)',
          pointHoverBorderColor: 'rgba(220,220,220,1)',
          pointHoverBorderWidth: 0,
          pointRadius: 5,
          pointHitRadius: 10,
          type: 'scatter',
          lineTension: 0,
          pointStyle: 'rectRot'
        };
        let mainData = lodash.filter(ci.data.datasets[1].data, item => {
          return (
            Number(item.y) >= y1.toFixed(2) &&
            Number(item.y) <= y2.toFixed(2) &&
            Number(item.x) >= x1.toFixed(2) &&
            Number(item.x) <= x2.toFixed(2)
          );
        });
        let mainDatasets = {
          label: 'ELR Maintenance',
          data: mainData,
          hidden: arr.indexOf(1) > -1 ? true : null,
          fill: true,
          fillOpacity: 0.3,
          backgroundColor: 'rgba(220,57,18,0.5)',
          borderColor: 'rgba(220,57,18,1)',
          borderCapStyle: 'butt',
          borderDash: [],
          borderDashOffset: 0.0,
          borderJoinStyle: 'miter',
          pointBorderColor: 'rgba(220,57,18,1)',
          pointBackgroundColor: '#f17f7fa8',
          pointBorderWidth: 0.5,
          pointHoverRadius: 5,
          pointHoverBackgroundColor: 'rgba(220,57,18,1)',
          pointHoverBorderColor: 'rgba(220,220,220,1)',
          pointHoverBorderWidth: 0,
          pointRadius: 5,
          pointHitRadius: 10,
          type: 'scatter',
          lineTension: 0,
          // hidden: 'boolean',
          pointStyle: 'rect'
        };
        let compData = lodash.filter(ci.data.datasets[0].data, item => {
          return (
            Number(item.y) >= y1.toFixed(2) &&
            Number(item.y) <= y2.toFixed(2) &&
            Number(item.x) >= x1.toFixed(2) &&
            Number(item.x) <= x2.toFixed(2)
          );
        });
        let compDatasets = {
          label: 'ELR Competitive',
          data: compData,
          hidden: arr.indexOf(0) > -1 ? true : null,
          fill: true,
          fillOpacity: 0.3,
          backgroundColor: 'rgba(75,192,192,0.4)',
          borderColor: 'rgba(75,192,192,1)',
          borderCapStyle: 'butt',
          borderDash: [],
          borderDashOffset: 0.0,
          borderJoinStyle: 'miter',
          pointBorderColor: 'rgba(75,192,192,1)',
          pointBackgroundColor: '#0389fc61',
          pointBorderWidth: 0.5,
          pointHoverRadius: 5,
          pointHoverBackgroundColor: 'rgba(75,192,192,1)',
          pointHoverBorderColor: 'rgba(220,220,220,1)',
          pointHoverBorderWidth: 0,
          pointRadius: 5,
          pointHitRadius: 10,
          type: 'scatter',
          lineTension: 0
        };
        let data = [];

        data = data.concat(elrData, mainData, compData);
        let labels = data.map(c =>
          c.category == 'ELR Competitive'
            ? 'COMPETITIVE'
            : c.category == 'ELR Maintenance'
            ? 'MAINTENANCE'
            : 'REPAIR'
        );
        let data1 = {
          labels: labels,
          datasets: [compDatasets, mainDatasets, elrDatasets]
        };

        setFilteredArr(data1);

        setElementClicked(true);

        ci.data = data1;
        //setFilteredArr(filteredArr);
        ci.options.scales = {
          yAxes: [
            {
              display: true,
              scaleLabel: {
                display: true,
                labelString: 'ELR'
              },
              ticks: {
                min: zoomPoints.minY, // minimum value
                max: zoomPoints.maxY // maximum value
              }
            }
          ],
          xAxes: [
            {
              display: true,
              scaleLabel: {
                display: true,
                labelString: 'Sold Hours'
              },
              ticks: {
                min: zoomPoints.minX, // minimum value
                max: zoomPoints.maxX // maximum value
              }
            }
          ]
        };
        ci.options.onClick = null;
      } else {
        ci.data = props.data;
      }
      //console.log('leg==ci', ci.data);
      // We hid a dataset ... rerender the chart
      ci.stop(); // make sure animations are not running

      ci.update();
    }
  };
  const handleAlertDialogClose = () => {
    props.handleClosePopup();
  };
  const setactions = val => {
    localStorage.setItem('itemTab', props.tabSelection);
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=1090',
        SelectedLocation: window.location.pathname
      });
    }
  };

  const setResetDashboard = value => {
    if (value) {
      setLayout([]);
      setAllData([]);
      setFilteredArr([]);
      setLegendArr([]);
      setElementClicked(true);
      setZoomPoints({});
    }
  };
  const classes = useStyles();

  return (
    <React.Fragment>
      {/* {props.type == 'popup' && (
        <DashboardActions resetDashboard={setResetDashboard}></DashboardActions>
      )} */}

      <Card
        bordered={false}
        className={
          props.parent != 'Details'
            ? clsx(classes.cardContainer, 'child-div')
            : clsx(classes.cardContainerDetail, 'child-div')
        }
      >
        <CardHeader
          title={chartTitle}
          action={
            <MoreActions
              type={props.type}
              chartPopup={chartPopup}
              enablePan={enablePan}
              panEnabled={panEnabled}
              handleClose={handleClose}
              setActions={setactions}
              resetDashboard={setResetDashboard}
              chartId={1090}
              favoritesDisabled={true}
            ></MoreActions>
          }
          subheader={getSubHeader(1090)}
          className={classes.cardHeader}
        ></CardHeader>

        <Divider />
        <CardContent style={{ cursor: 'pointer' }}>
          <div className={classes.chartContainter}>
            <Scatter
              data={props.data}
              options={options}
              getElementAtEvent={dataset => {
                if (dataset.length > 0 && lodash.isEmpty(filteredArr)) {
                  var dataIndex = dataset[0]._datasetIndex;
                  var selectedLabel = props.data.datasets[dataIndex].label;
                  var valueIndex = dataset[0]._index;
                  var selectedData = !lodash.isEmpty(filteredArr)
                    ? filteredArr.datasets[dataIndex].data[valueIndex]
                    : props.data.datasets[dataIndex].data[valueIndex];
                  if (props.parent && props.parent == 'Details') {
                    history.push({
                      pathname: '/AnalyzeData',
                      prevPath:
                        window.location.pathname == '/LaborItemization'
                          ? window.location.pathname
                          : window.location.pathname + '?chartId=' + 1090,
                      state: {
                        chartId: 1090,
                        drillDown: 16,
                        chartName: chartTitle,
                        category: 0,
                        elr: selectedData.y,
                        slodhours: selectedData.label,
                        tabSelection: props.tabSelection,
                        label:
                          selectedLabel == 'ELR Repair'
                            ? 0
                            : selectedLabel == 'ELR Competitive'
                            ? 1
                            : 2
                      }
                    });
                  }
                  let arr = {
                    elr: selectedData.y,
                    lbrsoldhours: selectedData.label,
                    category:
                      selectedLabel == 'ELR Repair'
                        ? 0
                        : selectedLabel == 'ELR Competitive'
                        ? 1
                        : 2
                  };

                  setAllData(dataArr => [...dataArr, arr]);
                  setElementClicked(true);
                }
              }}
            />
          </div>
        </CardContent>
      </Card>

      {props.parent != 'Details' && (
        <ItemizationDataGrid
          history={history}
          data={dataArr}
          zoomData={filteredArr}
          tabSelection={props.tabSelection}
          legendArr={legendArr}
          type={props.type}
          isLabor={true}
          handleClosePopup={handleAlertDialogClose}
          elementClicked={elementClicked}
          handleElementClicked={handleElementClicked}
        />
      )}
      <ChartDialog
        open={open}
        chartId={1090}
        chartType="itemization"
        realm={localStorage.getItem('realm')}
        handlePopupClose={handleClose}
        tabSelection={props.tabSelection}
        handleResetClicked={props.handleResetClicked}
        chartData={props.data}
        metaArr={metaArr}
      />
    </React.Fragment>
  );
};

export default CPELRVsLaborSoldHours;
