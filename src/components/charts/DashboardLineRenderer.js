import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import {
  getChartType,
  getLineChartType,
  GetyAxisRange,
  getChartName,
  getChartDrillDown,
  getChartDataIndex,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import {
  getChartConfiguration,
  getDataforLineChart,
  getDataforLineChartFromHasura,
  applyPartialMonthFilterLineCharts,
  getDataforLineChartFromPostgraphile,
  getDbdName
} from '../../utils/Utils';
import {
  Card,
  CardHeader,
  CardContent,
  Divider,
  Tooltip,
  Grid,
  CircularProgress
} from '@material-ui/core';
import MoreActions from '../MoreActions';
import { useHistory } from 'react-router';
import { makeStyles } from '@material-ui/core/styles';
import ChartDialog from 'src/components/Dialog';
import { useSelector, useDispatch } from 'react-redux';
import { getLatestClosedDate } from 'src/utils/hasuraServices';
import { is } from 'immutable';
var lodash = require('lodash');

const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
const DashboardLineRenderer = ({
  chartId,
  isFrom,
  callback,
  filterCharts,
  showCurrentMonth,
  realm,
  handleClose,
  type,
  backTobutton,
  handleHighlight,
  selected,
  selectedChartId,
  removeFavourite,
  headerClick,
  dbdName,
  chartData,
  isDataLoaded
}) => {
  const classes = useStyles();
  const history = useHistory();
  const [open, setOpen] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loader, setLoader] = useState('');
  const [hasuraData, setHasuraData] = useState([]);
  const [allData, setAllData] = useState([]);
  const [closedDate, setClosedDate] = useState('');
  const session = useSelector(state => state.session);
  useEffect(() => {
    setactions(filterCharts);
  }, [filterCharts]);
  useEffect(() => {
    setIsLoading(false);

    if (isFrom == 'source_page') {
      setLoader(true);
      getChartsDataFromViews(session.serviceAdvisor, chartId, callback => {
        if (callback) {
          var jsondataa = callback[0].jsonData;

          setChartData(jsondataa);
        }
      });
    }
  }, [session.storeSelected]);
  useEffect(() => {
    setLoader(true);

    if (typeof chartData != 'undefined') {
      setIsLoading(false);

      if (chartData && chartData.length > 0) {
        setChartData(chartData);
      }
    }
  }, [chartData]);

  var dashboardName = getDbdName(chartId);
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        SelectedLocation: window.location.pathname,
        handleHighlight: selectedChartId,
        selectedGrid: selected
      });
    }
  };
  const getClosedDate = () => {
    getLatestClosedDate(result => {
      if (result) {
        var Date1 = result[0].value;
        setClosedDate(Date1);
      }
    });
  };

  const setChartData = chartData => {
    let jsondataa;
    if (isFrom == 'source_page') {
      jsondataa = chartData;
    } else {
      jsondataa = JSON.stringify(chartData.map(({ chartId, ...rest }) => rest));
    }

    if (jsondataa.includes('0') || jsondataa.includes('0.00')) {
      jsondataa = jsondataa.replace(/"0(\.00)?"(?=,|\]|\})/g, 'null');
    }

    setHasuraData(JSON.parse(jsondataa));
    // let dataAll = getDataforLineChartFromPostgraphile(
    //   JSON.parse(jsondataa),
    //   lineChartType,
    //   undefined,
    //   chartId,
    //   2
    // );

    // if (
    //   session.toggleStatus == false &&
    //   (dbdName == 'CP Labor Overview' ||
    //     dbdName == 'CP Parts Overview' ||
    //     dbdName == 'details' ||
    //     ((isFrom == 'source_page' || isFrom == 'retail') &&
    //       (getDbdName(chartId) == 'CP Labor Overview' ||
    //         getDbdName(chartId) == 'CP Parts Overview')))
    // ) {
    //   let data = applyPartialMonthFilterLineCharts(dataAll);

    //   if (data && data.datasets != false && data.labels != null) {
    //     const dataNew = filterValue(data);
    //     setAllData(dataNew);
    //   } else {
    //     setAllData(data);
    //   }
    // } else {
    //   if (dataAll && dataAll.datasets != false && dataAll.labels != null) {
    //     const dataNew = filterValue(dataAll);
    //     setAllData(dataNew);
    //   } else {
    //     setAllData(dataAll);
    //   }
    // }
    // // }
    // //  setLoader(false);

    setIsLoaded(true);
    setIsLoading(true);
  };

  const filterValue = data => {
    if (
      localStorage.getItem('closedDate') == null &&
      localStorage.getItem('closedDate') == ''
    ) {
      getClosedDate();
    } else {
      setClosedDate(localStorage.getItem('closedDate'));
    }

    if (session.toggleStatus) {
      let year = closedDate.split('-')[0]; // "2025"

      const { labels, datasets } = data;
      const [dataset1, dataset2, dataset3] = datasets;

      return {
        ...data,
        datasets: data.datasets.map(dataset => ({
          ...dataset,

          data: dataset.data.map(value =>
            parseFloat(value) >= 0 ? value : null
          )
        }))
      };
    } else {
      return {
        ...data,
        datasets: data.datasets.map(dataset => ({
          ...dataset,
          data: dataset.data.map(value => (parseFloat(value) >= 0 ? value : 0))
        }))
      };
    }
  };
  const handleBackToButton = chartId => {
    backTobutton(chartId);
  };
  var stepSize =
    typeof GetyAxisRange(chartId, isFrom) != 'undefined'
      ? GetyAxisRange(chartId, isFrom)
      : '';
  var chartType = getChartType(chartId, isFrom);
  var lineChartType = getLineChartType(chartId, isFrom);
  // let data = getDataforLineChartFromPostgraphile(
  //   hasuraData,
  //   lineChartType,
  //   undefined,
  //   chartId
  // );

  const options = getChartConfiguration(
    chartType,
    session.serviceAdvisor.includes('All')
      ? stepSize
      : chartId == 916
      ? stepSize
      : undefined,
    '',
    '',
    '',
    chartId,
    true,
    '',
    '',
    '',
    '',
    allData,
    type
  );
  //console.log('options===', options);
  useEffect(() => {
    setIsLoading(true);
    setIsLoaded(true);
  }, [showCurrentMonth]);
  useEffect(() => {
    setLoader(true);

    if (isLoading && hasuraData && hasuraData.length > 0) {
      let dataAll = getDataforLineChartFromPostgraphile(
        hasuraData,
        lineChartType,
        undefined,
        chartId
      );

      if (
        session.toggleStatus == false &&
        (dbdName == 'CP Labor Overview' ||
          dbdName == 'CP Parts Overview' ||
          dbdName == 'details' ||
          ((isFrom == 'source_page' || isFrom == 'retail') &&
            (getDbdName(chartId) == 'CP Labor Overview' ||
              getDbdName(chartId) == 'CP Parts Overview')))
      ) {
        let data = applyPartialMonthFilterLineCharts(dataAll);

        if (data && data.datasets != false && data.labels != null) {
          const dataNew = filterValue(data);
          setAllData(dataNew);
        } else {
          setAllData(data);
        }

        // setAllData(data);
      } else {
        const closedDate = localStorage.getItem('closedDate');
        const closedYear = new Date(closedDate).getFullYear(); // -> 2025

        dataAll.datasets.forEach(dataset => {
          const datasetYear = parseInt(dataset.label);
          if (datasetYear !== closedYear) {
            dataset.data = dataset.data.map(val =>
              val == null || val < 0 ? 0 : val
            );
          }
        });
        console.log('jsondataa==', dataAll);
        if (dataAll && dataAll.datasets != false && dataAll.labels != null) {
          const dataNew = filterValue(dataAll);

          setAllData(dataNew);
        } else {
          setAllData(dataAll);
        }
      }

      setLoader(false);
      setIsLoading(false);
    } else {
      setLoader(false);
    }
  }, [session.toggleStatus, showCurrentMonth, isLoading, hasuraData]);
  useEffect(() => {
    if (isDataLoaded) {
      setLoader(true);

      setIsLoading(false);
      console.log('isDataLoaded==', chartData);
      setChartData(chartData);
    }
  }, [isDataLoaded]);

  const chartPopup = val => {
    localStorage.setItem('popup', true);
    setOpen(true);
  };
  const handleCloses = () => {
    localStorage.setItem('popup', false);
    setOpen(false);
    handleClose(1);
  };
  //console.log('allData==', chartId, chartId == 1133 ? allData : '');
  return (
    <>
      {isFrom == 'source_page' &&
      typeof allData != 'undefined' &&
      typeof hasuraData != 'undefined' &&
      hasuraData.length == 0 ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : isFrom != 'source_page' &&
        (loader == true ||
          (typeof chartData != 'undefined' && chartData.length == 0)) ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <Card
          bordered={false}
          style={{
            height: '100%',
            borderRadius: 0,
            border: '1px solid #003d6b'
          }}
        >
          <CardHeader
            title={getChartName(chartId)}
            action={
              <MoreActions
                chartId={chartId}
                setActions={setactions}
                type={type}
                removeFavourite={removeFavourite}
                chartPopup={chartPopup}
                handleClose={handleCloses}
                handleBackToButton={handleBackToButton}
                handleHighlight={handleHighlight}
              ></MoreActions>
            }
            subheader={getSubHeader(chartId)}
            style={{ borderBottom: '1px solid #003d6b' }}
            onClick={() =>
              typeof headerClick === 'function' && headerClick(chartId)
            }
            id={'card-header-' + chartId}
          ></CardHeader>

          <Divider />
          <CardContent
            style={
              // chartId == 1334 ||
              // chartId == 1318 ||
              // chartId == 916 ||
              // chartId == 953 ||
              // chartId == 966 ||
              // chartId == 1073
              //   ? { cursor: 'pointer', height: '80%' }:
              { cursor: 'pointer', height: '85%' }
            }
          >
            <Line
              redraw={false}
              data={allData}
              options={options}
              getElementAtEvent={dataset => {
                if (dataset.length > 0) {
                  var dataIndex = dataset[0]._datasetIndex;
                  var valueIndex = dataset[0]._index;
                  var xAxis = allData.datasets[dataIndex].data[valueIndex];
                  var yAxis = allData.labels[valueIndex];
                  if (
                    chartId != 942 &&
                    chartId != 939 &&
                    chartId != 946 &&
                    chartId != 940 &&
                    chartId != 920 &&
                    chartId != 925 &&
                    chartId != 766
                  ) {
                    var splittedMonth = yAxis.split('-', 2);
                    var yearMonth =
                      allData.datasets[dataIndex].label +
                      '-' +
                      splittedMonth[1];
                  }
                  if (chartId == 1238) {
                    var splittedMonth = yAxis.split('-', 2);
                    yearMonth = splittedMonth[0] + '-' + splittedMonth[1];
                  }

                  history.push({
                    pathname: '/AnalyzeData',
                    search: '?chartId=' + 'drillDown',
                    //prevPath:window.location.pathname,
                    prevPath:
                      window.location.pathname === '/GraphDetailsView'
                        ? Number(chartId) === 1073 ||
                          Number(chartId) === 1098 ||
                          Number(chartId) === 1356 ||
                          Number(chartId) === 956 ||
                          Number(chartId) === 966 ||
                          Number(chartId) === 1334
                          ? window.location.pathname + '?chartId=' + chartId
                          : window.location.pathname
                        : window.location.pathname,
                    state: {
                      chartId: chartId,
                      x: xAxis,
                      y: typeof yearMonth != 'undefined' ? yearMonth : yAxis,
                      drillDown: getChartDrillDown(chartId, isFrom),
                      chartName: getChartName(chartId),
                      category: getChartDataIndex(chartId, dataIndex, isFrom),
                      dbdName: dbdName,
                      prevPath:
                        window.location.pathname === '/GraphDetailsView'
                          ? Number(chartId) === 1073 ||
                            Number(chartId) === 1098 ||
                            Number(chartId) === 1356 ||
                            Number(chartId) === 956 ||
                            Number(chartId) === 966 ||
                            Number(chartId) === 1334
                            ? window.location.pathname + '?chartId=' + chartId
                            : window.location.pathname
                          : window.location.pathname
                    },
                    handleHighlight: selectedChartId,
                    selectedGrid: selected
                  });
                }
              }}
            ></Line>
            <div id={'tooltip_' + chartId}></div>
          </CardContent>
          <ChartDialog
            open={open}
            chartId={chartId}
            realm={realm}
            chartType="line"
            handlePopupClose={handleCloses}
            headerClick={headerClick}
            selected={selected}
            selectedChartId={selectedChartId}
            handleHighlight={handleHighlight}
            dbdName={dbdName}
            chartData={chartData}
            isDataLoaded={isDataLoaded}
          />
        </Card>
      )}
    </>
  );
};

DashboardLineRenderer.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
  yAxisRanges: PropTypes.array,
  filterCharts: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default DashboardLineRenderer;
