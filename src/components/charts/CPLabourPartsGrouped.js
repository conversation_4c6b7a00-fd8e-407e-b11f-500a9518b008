import { QueryRenderer } from '@cubejs-client/react';
import { <PERSON>, CardContent, CardHeader, Divider } from '@material-ui/core';
import moment from 'moment';
import React from 'react';
import {
  <PERSON>,
  Bar<PERSON>hart,
  CartesianGrid,
  Legend,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from 'recharts';
import cubejsApi from 'src/utils/cubeUtils';
import LoaderSkeleton from '../LoaderSkeleton.js';
import MoreActions from '../MoreActions';

const dateFormatter = item => moment(item).format('MMM YYYY');
const xAxisFormatter = item => {
  if (moment(item).isValid()) {
    return dateFormatter(item);
  } else {
    return item;
  }
};

const CartesianChart = ({ resultSet, children, ChartComponent }) => (
  <ResponsiveContainer width="100%" height={300}>
    <ChartComponent data={resultSet.chartPivot()}>
      <Tooltip labelFormatter={dateFormatter} />
      <XAxis dataKey="x" tickFormatter={xAxisFormatter} />
      <YAxis />
      <CartesianGrid />
      {children}
      <Legend />
    </ChartComponent>
  </ResponsiveContainer>
);

const colors = ['#FF6492', '#141446', '#7A77FF'];

const barRender = ({ resultSet }) => (
  <Card bordered={false} style={{ height: '100%' }}>
    <CardHeader
      title="CP Labor Parts Grouped"
      action={<MoreActions></MoreActions>}
    ></CardHeader>

    <Divider />
    <CardContent>
      <CartesianChart resultSet={resultSet} ChartComponent={BarChart}>
        {resultSet.seriesNames().map((series, i) => (
          <Bar
            animationDuration={1200}
            key={series.key}
            dataKey={series.key}
            name={series.title}
            fill={colors[i]}
          />
        ))}
      </CartesianChart>
    </CardContent>
  </Card>
);

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || <LoaderSkeleton />;

const CPLabourPartsGrouped = () => (
  <QueryRenderer
    query={{
      measures: [
        'CPLabourRevenue.with_parts',
        'CPLabourRevenue.with_out_parts'
      ],
      timeDimensions: [
        {
          dimension: 'CPLabourRevenue.monYear',
          dateRange: 'Last year'
        }
      ],
      dimensions: ['CPLabourRevenue.monYear'],
      filters: [],
      limit: 10000
    }}
    cubejsApi={cubejsApi()}
    render={renderChart(barRender)}
  />
);

export default CPLabourPartsGrouped;
