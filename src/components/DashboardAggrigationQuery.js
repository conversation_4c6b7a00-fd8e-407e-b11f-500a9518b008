import { getDetailedGraphDateRange, getStoreIdFilter } from 'src/components/ViewGraphDetailsAction';


export function getDashboardAggrigationQuery(chartId, storeId) {
  let query = '';
 // let storeId =JSON.parse(localStorage.getItem('selectedStoreId'));
 let realm = localStorage.getItem('realm');
  switch (chartId) {
    case 940: {
      query = {
        measures: [
          'CPGrossProfitPercentage.lbraggrigateprofitpercent',
          'CPGrossProfitPercentage.partsaggrigateprofitpercent',
          'CPGrossProfitPercentage.combinedaggrigateprofitpercent'
        ],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfitPercentage.ro_date'],
        filters: getStoreIdFilter('CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'asc'
        }
      };
      return query;
    }
    case 946: {
      query = {
        measures: ['CPEffectiveLabourRate.elr_aggrigate'],
        timeDimensions: [
          {
            dimension: 'CPEffectiveLabourRate.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPEffectiveLabourRate.ro_date'],
        filters: getStoreIdFilter('CPEffectiveLabourRate', storeId),
        order: {
          'CPEffectiveLabourRate.ro_date': 'asc'
        }
      };
      return query;
    }
    case 1238: {
      query = {
        measures: ['CPPartsMarkupOverview.parts_markup_aggrigate'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupOverview.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupOverview.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupOverview', storeId),
        order: {
          'CPPartsMarkupOverview.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1072: {
      query = {
        measures: ['CPGrossProfitPercentage.combinedaggrigateprofitpercent'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfitPercentage.ro_date'],
        filters: getStoreIdFilter('CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'asc'
        }
      };
      return query;
    }
    case 1097: {
      query = {
        measures: ['CPGrossProfitPercentage.partsaggrigateprofitpercent'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfitPercentage.ro_date'],
        filters: getStoreIdFilter('CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'asc'
        }
      };
      return query;
    }
    case 967: {
      query = {
        measures: ['CPGrossProfitPercentage.lbraggrigateprofitpercent'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfitPercentage.ro_date'],
        filters: getStoreIdFilter('CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'asc'
        }
      };
      return query;
    }
    
    case 1176: {
      query = {
        measures: ['CPELRPaytypes.elr_aggrigate_combined'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'asc'
        }
      };
      return query;
    } 
    case 1177: {
      query = {
        measures: ['CPELRPaytypes.elr_aggrigate_customerpay'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'asc'
        }
      };
      return query;
    } 
    case 1178: {
      query = {
        measures: ['CPELRPaytypes.elr_aggrigate_warranty'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'asc'
        }
      };
      return query;
    } 
    case 1179: {
      query = {
        measures: ['CPELRPaytypes.elr_aggrigate_internal'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'asc'
        }
      };
      return query;
    } 
    case 1180: {
      query = {
        measures: ['CPELRPaytypes.elr_aggrigate_maintenanceplan'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'asc'
        }
      };
      return query;
    } 
    case 1181: {
      query = {
        measures: ['CPELRPaytypes.elr_aggrigate_extendedservice'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'asc'
        }
      };
      return query;
    } 
    case 1182: {
      query = {
        measures: ['CPELRPaytypes.elr_aggrigate_factoryservice'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'asc'
        }
      };
      return query;
    } 
    case 1225: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_aggrigate_combined'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1226: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_aggrigate_cp'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'asc'
        }
      };
      return query;
    }

    case 1227: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_aggrigate_warranty'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1228: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_aggrigate_internal'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1229: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_aggrigate_maintenance'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1230: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_aggrigate_extended'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1231: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_aggrigate_factory'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'asc'
        }
      };
      return query;
    }
  }
}