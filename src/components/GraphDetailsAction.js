import PropTypes from 'prop-types';
import React, { memo, useEffect, useState } from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import CPELRVsLaborSoldHours from 'src/components/charts/CPELRVsLaborSoldHours';
import CPPartsMarkupVsPartsCost from 'src/components/charts/CPPartsMarkupVsPartsCost';
import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import DeltaSoldHoursAndFlatRateHours from 'src/components/charts/DeltaSoldHoursAndFlatRateHours';
import DeltaTechFlatHours from 'src/components/charts/DeltaTechFlatHours';
import GraphDetails from 'src/components/charts/GraphDetails';
import GraphDetailsView from 'src/components/charts/GraphDetailsView';
import { GetyAxisRange } from 'src/components/ViewGraphDetailsAction';
import BarChartRenderer from 'src/views/AnalyzeData/WorkMixCharts/BarChartRenderer';
import { getDataGridConfigurationForDetails } from '../utils/Utils';
import ApexCharts from '../views/AddOns/ServiceAdvisor/ColumnRenderer';
import HighCharts from '../views/AnalyzeData/ComparisonCharts/ColumnRenderer';
import ApexChartsDiscount from '../views/Discounts/DiscountColumnRenderer';
import DashboardLineRenderer from './charts/DashboardLineRenderer';
import $ from 'jquery';
import { useHistory } from 'react-router';
//import makeApolloClient from 'src/utils/apolloRootClient';
import makeApolloClient from 'src/utils/apolloRootClientPostgres';
import AdvisorPerformanceRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/AdvisorChartRenderer';
import AdvisorComparisonCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/ColumnRenderer';
import AdvisorOpcategoryCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/OpCategoryChartRenderer';
import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select
} from '@material-ui/core';

import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from '@material-ui/core/styles';

import { setNavItemStatus, setNavItems, setMenuSelected } from 'src/actions';

import TechnicianMonthComparison from '../views/AnalyzeData/TechEfficiency/ColumnRenderer';
import {
  isTechComparisonChart,
  isWormixChart,
  isAdvisorChart,
  isTechChart,
  isAdvisorComparisonChart,
  isAdvisorOpcategoryChart,
  isBarChart,
  isAddonsComparisonChart,
  isDiscountComparisonApexChart,
  isOpportunityChart
} from 'src/utils/Utils';
import OpportunityCharts from 'src/components/charts/OpportunityCharts';
import clsx from 'clsx';
import PageHeader from 'src/components/PageHeader';
import 'src/styles.css';
import TechBarChartRendererPostGraphile from 'src/views/AnalyzeData/TechEfficiency/BarChartRendererPostgraphile';

const useStyles = makeStyles(theme => ({
  formControl: {
    padding: 4,
    minWidth: 250
  },
  gridContainer: {
    padding: 5
  },
  container: {
    padding: '5px 0px'
  }
}));
require('dotenv').config();
const ReactGridLayout = WidthProvider(RGL);
var lodash = require('lodash');

function GraphDetailsActions(props) {
  const parentId = props.parentId;
  const classes = useStyles();
  const month1 = props.month1;
  const month2 = props.month2;
  const datatype = props.datatype;
  const chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  const dispatch = useDispatch();
  const history = useHistory();
  const session = useSelector(state => state.session);

  useEffect(() => {
    if (parentId) {
      getChartDetails(parentId);
    }
  }, [parentId]);

  const [chartData, setChartData] = useState([]);
  const [formState, setFormState] = useState({});
  const [checkEmpty, setCheckEmpty] = useState(false);

  const allowedParentIds = [
    '942',
    '939',
    '940',
    '960',
    '944',
    '1133',
    '1127',
    '1044',
    '955',
    '918',
    '1138',
    '1239'
  ];
  const notAllowedParentIds = ['966', '1334'];

  function getChartDetails(parentId) {
    if (parentId == '1238') {
      parentId = '916';
    }
    const client = makeApolloClient;

    let filteredResult = chartList.filter(item => item.parentId == parentId);
    // if (parentId == '1239' && props.realm != 'haleyag') {
    //   filteredResult = [];
    // }
    if (parentId == '1239') {
      filteredResult.splice(0, 3);
    }
    if (filteredResult.length > 0) {
      let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
      if (
        parentId == 946 ||
        parentId == 1127 ||
        parentId == 960 ||
        parentId == 1044 ||
        parentId == 955
        // parentId == 1318
      ) {
        setChartData(orderedData.slice(5));
      } else if (parentId == 953 || parentId == 916) {
        setChartData(orderedData.slice(4));
        // setChartData(result.data.dms_physical_rw_chart_master.slice(0,4));
      } else if (parentId == 918) {
        setChartData(orderedData.slice(6, 13));
      } else {
        setChartData(orderedData);
      }
    } else {
      filteredResult = chartList.filter(
        item => item.parentId == null && item.chartId == parentId
      );
      setChartData(filteredResult);
    }
  }
  useEffect(() => {
    if (chartData.length > 0) {
      let chartDbdName = chartData[0].dbdName;

      if (
        chartDbdName == 'Labor Workmix' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'source_page'
      ) {
        dispatch(setMenuSelected('Labor Work Mix'));
        dispatch(setNavItems(['Labor']));
      } else if (
        chartDbdName == 'CP Overview' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'source_page'
      ) {
        dispatch(setMenuSelected('CP Summary Overview'));
        dispatch(setNavItems(['CP Summary Overview']));
      } else if (
        chartDbdName == 'CP Labor Overview' &&
        session.menuSelected == 'Labor Overview' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'source_page'
      ) {
        dispatch(setMenuSelected('Labor Overview'));
        dispatch(setNavItems(['Labor']));
      } else if (
        chartDbdName == 'CP Parts Overview' &&
        parentId != '1238' &&
        session.menuSelected == 'Parts Overview' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'source_page'
      ) {
        dispatch(setMenuSelected('Parts Overview'));
        dispatch(setNavItems(['Parts']));
      } else if (
        chartDbdName == 'Parts Workmix' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'source_page'
      ) {
        dispatch(setMenuSelected('Parts Work Mix'));
        dispatch(setNavItems(['Parts']));
      } else if (
        (chartDbdName == 'Service Advisor Perfomance' ||
          chartDbdName == 'Service Advisor-Month Comparison' ||
          chartDbdName == 'Service Advisor-Opcategory By Month') &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'source_page'
      ) {
        dispatch(setMenuSelected('Advisor Metrics'));
        // dispatch(setNavItems(['Advisor Metrics']));
      } else if (
        chartDbdName == 'Technician Efficiency' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'source_page'
      ) {
        dispatch(setMenuSelected('Tech Metrics'));
        // dispatch(setNavItems(['Tech Metrics']));
      } else if (
        chartDbdName == 'Discounts' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'details'
      ) {
        dispatch(setMenuSelected('Discount Metrics'));
        // dispatch(setNavItems(['Discount Metrics']));
      } else if (
        chartDbdName == 'Special Metrics' &&
        typeof history.location.isFrom == 'undefined' &&
        history.location.isFrom != 'Special Metrics'
      ) {
        dispatch(setMenuSelected('Special Metrics'));
        //dispatch(setNavItems(['Special Metrics']));
      }
    }
  }, [chartData]);
  useEffect(() => {
    if (session.menuSelected != '') {
      var element = document.getElementById(session.menuSelected);
      if (element) {
        element.classList.add('active-menu');
      }
    }
  }, []);

  const getChartText = index => {
    return <GraphDetails realm={props.realm} dbdName={chartData[0].dbdName} />;
  };

  const getChartDetailsView = (chart_id, chart_name, dbd_name, parent_id) => {
    if (
      chart_id == '1073' ||
      chart_id == '1098' ||
      chart_id == '1356' ||
      chart_id == '956' ||
      chart_id == '966' ||
      chart_id == '1334'
    ) {
      return (
        <DashboardLineRenderer
          chartId={Number(chart_id)}
          realm={props.realm}
          showCurrentMonth={
            props.showCurrentMonth
              ? props.showCurrentMonth
              : session.toggleStatus
            // : JSON.parse(localStorage.getItem('showCurrentMonth'))
          }
          selectedChartId={props.handleHighlight}
          selected={props.selectedGrid}
          dbdName="details"
          isFrom={'source_page'}
        />
      );
    } else if (isOpportunityChart(chart_id)) {
      console.log('enter=01');
      return (
        <OpportunityCharts chartId={Number(chart_id)} realm={props.realm} />
      );
    } else if (chart_id == '957') {
      console.log('enter=1');
      return <DeltaTechFlatHours />;
    } else if (chart_id == '766') {
      console.log('enter=1');
      return <DeltaSoldHoursAndFlatRateHours />;
    }
    // else if (chart_id == '1090') {
    //   return <CPELRVsLaborSoldHours />;
    // }
    // else if (chart_id == '1096') {
    //   return <CPPartsMarkupVsPartsCost />;
    // }
    else if (
      chart_id == '1114' ||
      chart_id == '1121' ||
      chart_id == '1122' ||
      chart_id == '1125'
    ) {
      console.log('enter=1');
      return (
        <HighCharts
          isPartsCharts={false}
          datatype={datatype}
          chartId={Number(chart_id)}
          month1={month1}
          month2={month2}
        />
      );
    } else if (isAddonsComparisonChart(chart_id)) {
      console.log('enter=11');
      return (
        <ApexCharts
          datatype={datatype}
          chartId={Number(chart_id)}
          month1={month1}
          month2={month2}
        />
      );
    } else if (isDiscountComparisonApexChart(chart_id)) {
      console.log('enter=112');
      return (
        <ApexChartsDiscount
          datatype={datatype}
          chartId={Number(chart_id)}
          month1={month1}
          month2={month2}
          isFrom={'source_page'}
        />
      );
    } else if (isWormixChart(chart_id)) {
      console.log('enter=113');
      return (
        <BarChartRenderer
          title={chart_id}
          chartId={Number(chart_id)}
          realm={props.realm}
          userhistory={props.userhistory}
        />
      );
    } else if (isTechComparisonChart(chart_id)) {
      console.log('enter=114');
      return (
        <TechnicianMonthComparison
          datatype={datatype}
          month1={month1}
          month2={month2}
          chartId={chart_id}
          realm={props.realm}
          session={session}
        />
      );
    } else if (isBarChart(chart_id)) {
      // console.log('enter=115', props.handleHighlight, props.selectedGrid);
      return (
        <DashboardBarRenderer
          chartId={Number(chart_id)}
          isFrom={dbd_name}
          parentId={parent_id}
          realm={props.realm}
          parent={props.parent}
          selectedToggle={props.selectedToggle}
          selectedChartId={props.handleHighlight}
          selected={props.selectedGrid}
          parentFrom={'detail_page'}
        />
      );
    } else if (isAdvisorChart(chart_id)) {
      console.log('enter=116');
      return (
        <AdvisorPerformanceRenderer
          chartId={Number(chart_id)}
          title={1}
          advisor={localStorage.getItem('selectedAdvisor')}
          advisorName={localStorage.getItem('selectedAdvisorName')}
          checkEmpty={handleChange}
          isFrom={'detail_page'}
          session={session}
          serviceAdvisor={session.serviceAdvisor}
        />
      );
    } else if (isTechChart(chart_id)) {
      console.log('enter=117');
      return (
        <TechBarChartRendererPostGraphile
          chartId={Number(chart_id)}
          title={chart_id}
          tech={
            localStorage.getItem('selectedTech')
              ? localStorage.getItem('selectedTech')
              : 'All'
          }
          techName={localStorage.getItem('selectedTechName')}
          checkEmpty={handleChange}
          callFrom={1}
          chartTitle={chart_name}
          realm={props.realm}
          isFrom={'detail_page'}
        />
      );
    } else if (isAdvisorComparisonChart(chart_id)) {
      console.log('enter=119');
      return (
        <AdvisorComparisonCharts
          datatype={datatype}
          month1={month1}
          month2={month2}
          chartId={chart_id}
          isFrom={'detail_page'}
        />
      );
    } else if (isAdvisorOpcategoryChart(chart_id)) {
      console.log('enter=118', datatype, month1, month2, chart_id);
      return (
        <AdvisorOpcategoryCharts
          datatype={datatype}
          month1={month1}
          isFrom={'detail_page'}
          // month2={month2}
          chartId={chart_id}
        />
      );
    }
    return (
      <GraphDetailsView
        chartId={Number(chart_id)}
        yAxisRanges={GetyAxisRange(chart_id)}
        title={chart_name}
        isStacked={''}
        location={''}
        realm={props.realm}
        parentId={parent_id}
        isFrom={'detail_page'}
        selected={props.selectedGrid}
        selectedChartId={props.handleHighlight}
        classes={classes}
        chartData={chartData}
      />
    );
  };
  const handleChange = val => {
    setCheckEmpty(val);
  };

  const shouldShowChart =
    allowedParentIds.includes(parentId) ||
    (chartData[0]?.dbdName === 'CP Parts Overview' &&
      !notAllowedParentIds.includes(parentId));

  return (
    chartData.length > 0 && (
      <Grid container spacing={12} className={classes.container}>
        <Grid
          item
          xs={6}
          className={clsx(
            classes.gridContainer,
            'diagram-section',
            chartData[0].dbdName == 'Service Advisor Perfomance' ||
              chartData[0].dbdName == 'Labor Workmix' ||
              chartData[0].dbdName == 'Parts Workmix' ||
              chartData[0].dbdName == 'Technician Efficiency'
              ? ''
              : 'custom-diagram-section'
          )}
        >
          {getChartText(0)}
        </Grid>
        {shouldShowChart
          ? getChartDetailsView(
              chartData[0].chartId,
              chartData[0].chartName,
              chartData[0].dbdName,
              parentId
            )
          : chartData.map((item, index) => {
              return (
                <Grid
                  item
                  xs={6}
                  className={clsx(classes.gridContainer, 'diagram-section')}
                >
                  {getChartDetailsView(
                    item.chartId,
                    item.chartName,
                    item.dbdName,
                    item.parentId
                  )}
                </Grid>
              );
            })}
      </Grid>
      /* <ReactGridLayout {...props} isResizable={false} margin={[5, 10]}>
            {getChartText(0)}
            {chartData.map((item, index) => {
              return (
                <div
                  className={clsx('diagram-section')}
                  key={index + 1}
                  data-grid={getDataGridConfigurationForDetails(
                    index + 1,
                    parentId,
                    item.chartId
                  )}
                >
                  {getChartDetailsView(
                    item.chartId,
                    item.chartName,
                    item.dbdName,
                    item.parentId
                  )}
                </div>
              );
            })}
          </ReactGridLayout> */
    )
  );
}

GraphDetailsActions.propTypes = {
  className: PropTypes.string,
  filterCharts: PropTypes.func,
  setTitle: PropTypes.string,
  resetDashboard: PropTypes.func,
  yAxisRanges: PropTypes.array,
  showCurrentMonth: PropTypes.func
};
GraphDetailsActions.defaultProps = {
  className: 'layout',
  cols: 12,
  rowHeight: 50,

  onLayoutChange: function() {}
};

export default memo(GraphDetailsActions);
