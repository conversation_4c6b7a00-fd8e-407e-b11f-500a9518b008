import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import { useDispatch, useSelector } from 'react-redux';
import Paper from '@material-ui/core/Paper';
import clsx from 'clsx';
import {
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
  Button,
  Tooltip,
  Box
} from '@material-ui/core';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import { UPDATE_PAYTYPE_RETAIL_FLAG } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { traceSpan } from 'src/utils/OTTTracing';
import { setRetailFlag } from 'src/actions';
import { getStoreRetialFlags } from 'src/utils/hasuraServices';
import _ from 'lodash';
const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(1),
    maxWidth: 500
  },
  image: {
    width: 100,
    height: 75,
    cursor: 'default'
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '50%'
  },
  container: {
    alignItems: 'center',
    marginTop: '-20px !important',
    '@media (max-width: 1920px)': {
      width: 250
    },
    '@media (max-width: 1440px)': {
      width: 220
    },
    '@media (max-width: 1280px)': {
      width: 200
    },
    '@media (min-width: 2304px)': {
      width: 350
    }
  },
  titleLabel: {
    display: 'flex',
    fontSize: 15,
    cursor: 'default'
  },
  valLabel: {
    display: 'flex',
    cursor: 'default'
  },
  dataItem: {
    marginLeft: 20,
    padding: '0px !important'
  },
  message: {
    fontFamily: 'Roboto, Helvetica, Arial, sans-serif',
    fontSize: 14,
    color: '#003d6b',
    textTransform: 'none'
  }
}));
const PaytypeList = ({
  allPaytypeNames,
  selectedPaytypeIds,
  updatePaytypeList,
  keycloak,
  isPaytypeLoading
}) => {
  const dispatch = useDispatch();
  const [isAllStoresSelected, setIsAllStoresSelected] = useState(false);
  const classes = useStyles();
  const [allPaytypeNameData, setAllPaytypeNameData] = useState([]);
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [isDisabled, setIsdisabled] = useState(true);
  useEffect(() => {
    setAllPaytypeNameData(allPaytypeNames);
  }, [allPaytypeNames]);

  const handleIndividualChange = (paytypeId, retailFlag) => {
    let updatedArr = allPaytypeNameData.map(item =>
      item.id === paytypeId ? { ...item, retailFlag: retailFlag } : item
    );

    console.log(updatedArr);
    setAllPaytypeNameData(updatedArr);
    setIsdisabled(false);
    checkOnChange(updatedArr);
  };
  const checkOnChange = updatedArr => {
    const isEqual = _.isEqual(allPaytypeNames, updatedArr);
    if (isEqual == false) {
      setIsdisabled(false);
    } else {
      setIsdisabled(true);
    }
  };

  const handleApplyRetail = () => {
    // const selectedPaytypeIds = getSelectedPaytypeIds();
    // updatePaytypeList(selectedPaytypeIds);
    let formattedArr = allPaytypeNameData.map(item => ({
      Paytype: item.sourcePaytype,
      retailFlagParam: item.retailFlag
    }));
    formattedArr = JSON.stringify(formattedArr);

    const client = makeApolloClient;
    const data = [];
    const start = new Date();

    const isEqual = _.isEqual(allPaytypeNames, allPaytypeNameData);
    if (isEqual == false) {
      client
        .mutate({
          mutation: UPDATE_PAYTYPE_RETAIL_FLAG,
          variables: {
            storeIdParam: JSON.parse(
              localStorage.getItem('selectedStoreId')
            )[0],
            paytypeRetailFlag: formattedArr
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/PayTypeMaster',
            origin: '',
            event: 'Menu Load',
            is_from: 'UPDATE_PAYTYPE_RETAIL_FLAG',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);

          if (
            result.data &&
            result.data.statelessCcPhysicalRwUpdatePaytypeRetailFlag &&
            result.data.statelessCcPhysicalRwUpdatePaytypeRetailFlag
              .updatePaytypeMasterStatuses &&
            result.data.statelessCcPhysicalRwUpdatePaytypeRetailFlag
              .updatePaytypeMasterStatuses[0] &&
            result.data.statelessCcPhysicalRwUpdatePaytypeRetailFlag
              .updatePaytypeMasterStatuses[0].status == 1
          ) {
            updatePaytypeList();
            setOpen(true);
            setMessage('Retail Flag Updated Successfully');
            setIsdisabled(true);
            // dispatch(setRetailFlag(formattedArr));
            getStoreRetialFlags(result => {
              let storeRetailFlags;

              if (result.data.statelessCcPhysicalRwGetMappedPaytype) {
                storeRetailFlags =
                  result.data.statelessCcPhysicalRwGetMappedPaytype.strings;
              } else {
                storeRetailFlags = ['C'];
              }
              // dispatch(setRetailFlag(storeRetailFlags));
              localStorage.setItem('retailFlags', storeRetailFlags);
            });
          } else {
            updatePaytypeList();
            setOpen(true);
            setMessage('Retail flag update failed.');
            setIsdisabled(true);
          }
          // setOpen(true);
          // setRowUpdated(true);
          // setIsEdit(false);
          // setOverviews(overview);
          // setCalculations(calculation);
        });
    } else {
      setOpen(true);
      setMessage('Please make a change to the Retail Flag.');
    }
  };
  const handleClose = () => {
    setOpen(false);
    setMessage('');
  };
  return (
    <>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              {message}
            </Typography>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary" autoFocus>
            OK
          </Button>
        </DialogActions>
      </Dialog>
      <Grid
        container
        xs={12}
        className={clsx('sidebar-card', classes.container)}
      >
        <Typography variant="h5" gutterBottom className="sidebar-view-title">
          Set Retail Flag
        </Typography>
        <Grid item>
          <Paper className="sidebar-box">
            <FormGroup>
              {/* <Tooltip title="All Pay Types">
              <FormControlLabel
                style={{ pointerEvents: 'none' }}
                control={
                  <Checkbox
                    checked={isAllStoresSelected}
                    style={{ pointerEvents: 'auto' }}
                    onChange={handleAllStoresChange}
                  />
                }
                label="All Pay Types"
              />
            </Tooltip> */}
              {allPaytypeNameData &&
                allPaytypeNameData.map(paytype => (
                  <Tooltip
                    key={paytype.id}
                    title={paytype.sourcePaytype}
                    placement="bottom-start" // Set the position
                  >
                    <FormControlLabel
                      key={paytype.id}
                      style={{ pointerEvents: 'none' }}
                      disabled={
                        paytype.sourcePaytype == 'C' || isPaytypeLoading == true
                          ? true
                          : typeof keycloak.realmAccess.roles !== 'undefined' &&
                            keycloak.realmAccess.roles.includes('client') ==
                              true
                          ? true
                          : false
                      }
                      control={
                        <Checkbox
                          checked={
                            paytype.sourcePaytype == 'C'
                              ? true
                              : paytype.retailFlag
                          }
                          style={{ pointerEvents: 'auto' }}
                          onChange={() =>
                            handleIndividualChange(
                              paytype.id,
                              !paytype.retailFlag
                            )
                          }
                          disabled={
                            (paytype.sourcePaytype == 'C' && true) ||
                            isPaytypeLoading
                          }
                        />
                      }
                      label={paytype.sourcePaytype}
                    />
                  </Tooltip>
                ))}
            </FormGroup>
            {allPaytypeNameData && allPaytypeNameData.length > 0 ? (
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'flex-end', // Aligns the button to the right
                  marginTop: 2
                }}
              >
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleApplyRetail}
                  className={clsx('reset-btn', 'btnClass')}
                  style={{
                    height: '20px',
                    fontSize: '13px'
                  }}
                  disabled={
                    (typeof keycloak.realmAccess.roles !== 'undefined' &&
                      keycloak.realmAccess.roles.includes('client') == true) ||
                    isDisabled ||
                    isPaytypeLoading == true
                      ? true
                      : false
                  }
                >
                  Apply
                </Button>
              </Box>
            ) : (
              <Typography
                className={classes.message}
                style={{
                  paddingLeft: '10px',
                  fontWeight: 'bold',
                  color: '#003d6b',
                  fontSize: 13
                }}
              >
                No Retail Flag Available
              </Typography>
            )}
          </Paper>
        </Grid>
      </Grid>{' '}
    </>
  );
};

export default PaytypeList;
