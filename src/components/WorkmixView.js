import React, { useState } from 'react';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import {
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
  Tooltip
} from '@material-ui/core';
import { workmixOption } from 'src/utils/constants';

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(1),
    maxWidth: 500
  },
  image: {
    width: 100,
    height: 75,
    cursor: 'default'
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '50%'
  },
  container: {
    alignItems: 'center',
    '@media (max-width: 1920px)': {
      width: 250
    },
    '@media (max-width: 1440px)': {
      width: 220
    },
    '@media (max-width: 1280px)': {
      width: 200
    },
    '@media (min-width: 2304px)': {
      width: 350
    }
  },
  titleLabel: {
    display: 'flex',
    fontSize: 15,
    cursor: 'default'
  },
  valLabel: {
    display: 'flex',
    cursor: 'default'
  },
  dataItem: {
    marginLeft: 20,
    padding: '0px !important'
  },
  tooltip: {
    marginTop: '-20px'
  },
  tooltipRepair: {
    marginTop: '-20px',
    marginLeft: '-70px',
    width: '60px'
  }
}));
const WorkmixView = ({ selectedWorkmixOptions, setSelectedWorkmixOptions }) => {
  const classes = useStyles();
  const handleChange = key => {
    // Toggle the option (add/remove from the selectedWorkmixOptions array)
    setSelectedWorkmixOptions(prevState => {
      if (prevState.includes(key)) {
        // Remove the key if it exists
        return prevState.filter(item => item !== key);
      } else {
        // Add the key if it doesn't exist
        return [...prevState, key];
      }
    });
  };

  return (
    <Grid container xs={12} className={clsx('sidebar-card', classes.container)}>
      <Typography className="sidebar-view-title" variant="h5" gutterBottom>
        Work Mix View
      </Typography>
      <Grid item>
        <Paper className="sidebar-box">
          <FormGroup>
            {workmixOption.map(({ key, label }) => (
              <Tooltip
                key={key}
                title={label}
                classes={{
                  tooltip:
                    label == 'Repair' ? classes.tooltipRepair : classes.tooltip
                }}
              >
                <div>
                  <FormControlLabel
                    key={key}
                    classes={{
                      root: 'sidebar-checkbox'
                    }}
                    // classes="sidebar-checkbox"

                    control={
                      <Checkbox
                        classes={{
                          root: 'sidebar-input'
                        }}
                        checked={selectedWorkmixOptions.includes(key)}
                        onChange={() => handleChange(key)}
                      />
                    }
                    label={label}
                  />
                </div>
              </Tooltip>
            ))}
          </FormGroup>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default WorkmixView;
