import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import FormGroup from '@material-ui/core/FormGroup';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField,
  Tooltip
} from '@material-ui/core';
import Checkbox from '@material-ui/core/Checkbox';
import $, { param } from 'jquery';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles, withStyles } from '@material-ui/styles';
import PropTypes, { element } from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import MuiTableCell from '@material-ui/core/TableCell';
import { sendSavedReportEmails } from 'src/utils/hasuraServices';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import FormHelperText from '@material-ui/core/FormHelperText';
import Chip from '@material-ui/core/Chip';
import TableCell from '@material-ui/core/TableCell';
import EditIcon from '@material-ui/icons/Edit';
import SaveIcon from '@material-ui/icons/Save';
import CancelIcon from '@material-ui/icons/NotInterested';

import { getRecipientEmails, getEmail } from 'src/utils/hasuraServices';

import _ from 'lodash';
import { i } from 'react-dom-factories';

var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {},
  // backDrop: {
  //   backdropFilter: 'blur(3px)',
  //   backgroundColor: 'rgba(0, 0, 0, 0)',
  //   margin: 20
  // },
  // scrollPaper: { backgroundColor: '#fff' },
  menuItem: {
    padding: 0
  },
  formControl: {
    minWidth: 110
  },
  formControl1: {
    minWidth: 160
  },
  cardControl: {
    padding: 0
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),
    fontSize: 12
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  paper: {
    width: '1380px', // Fixed width
    maxHeight: '90vh'
    // '@media (max-width: 1920px)': {
    //   maxWidth: '1150px',
    //   maxHeight: '700px'
    // },

    // '@media (max-width: 1440px)': {
    //   maxWidth: '1500px',
    //   maxHeight: '610px'
    // },

    // '@media (max-width: 1280px)': {
    //   maxWidth: '1500px',
    //   maxHeight: '600px'
    // },
    // '@media (max-width: 2304px)': {
    //   maxWidth: '1350px',
    //   maxHeight: '900px'
    // }
  },
  paperTranches: {
    maxWidth: '1250px',
    maxHeight: '800px'
  },
  paperItemization: {
    maxWidth: '100%',
    maxHeight: '1400px'
  },
  flexItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  paperBar: {
    // maxWidth: '1300px',
    maxWidth: '100%',
    maxHeight: '1400px'
    // marginLeft: '17%',
    // marginTop: '8%',
    // marginRight: '0%'
  },
  divDisplay: {
    //marginBottom: 45
  },
  divDisplayAdv: {
    //marginTop: 47
  },
  parentCell: {
    display: 'flex',
    justifyContent: 'space-around'
  },
  editicon: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  editiconDisable: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer',
    pointerEvents: 'none',
    color: 'gray'
  },
  actionDisable: {
    pointerEvents: 'none'
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function ViewEmailDialog({
  open,
  handlePopupClose,
  selectedRow,
  mailUserData,
  mailUsers,
  keycloak
}) {
  let checkedData = selectedRow.visibility == 'private' ? true : false;
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(open);
  const [editRowId, setEditRowId] = useState(null);
  const [data, setData] = useState(mailUserData);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = useState('');
  const [error, setError] = useState('');
  const [openAlert, setOpenAlert] = React.useState(false);

  const [recipientEmails, setRecipientEmails] = useState([]);
  const [ccEmails, setCcEmails] = useState([]);
  const [bccEmails, setBccEmails] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [ccValue, setCcValue] = useState('');
  const [bccValue, setBccValue] = useState('');
  const [savedEmails, setSavedEmails] = useState([]);
  const [savedCcEmails, setSavedCcEmails] = useState([]);
  const [savedBcccEmails, setSavedBccEmails] = useState([]);
  const [allEmails, setAllEmails] = useState([]);
  const [openConfirm, setOpenConfirm] = useState(false);
  const [deleteInfo, setDeleteInfo] = useState(null);
  const [openDilog, setOpenDilog] = useState(false);
  const [deleteRow, setDeleteRow] = useState(null);
  useEffect(() => {
    setOpenDialog(open);
  }, [open]);
  useEffect(() => {
    setData(mailUserData);
  }, [mailUserData]);

  const handleOk = () => {
    setOpenDialog(false);
    setEditRowId(null);
    handlePopupClose();
  };
  const cancelDelete = () => {
    setOpenConfirm(false);
    setOpenDilog(false);
    setDeleteInfo(null);
    setDeleteRow(null);
  };
  const handleDelete = (rowId, emailType, emailToDelete, row) => {
    setDeleteInfo({
      rowId,
      emailType,
      emailToDelete,
      row
    });
    if (
      emailType === 'recipientId' &&
      row.recipientId.split(',').length === 1
    ) {
      setOpenAlert(true);
      setAlertType('warning');
      setAlertMsg(
        'Recipient email cannot be deleted because it is the only one.'
      );
      setDeleteInfo(null);
      setTimeout(() => {
        setOpenAlert(false);
      }, 2000);
    } else {
      setOpenConfirm(true);
    }
  };
  const confirmDelete = () => {
    const { rowId, emailType, emailToDelete, row } = deleteInfo;
    // if (
    //   emailType === 'recipientId' &&
    //   row.recipientId.split(',').length === 1
    // ) {
    //   setOpenAlert(true);
    //   setAlertType('warning');
    //   setAlertMsg(
    //     'Recipient email cannot be deleted because it is the only one.'
    //   );
    //   setOpenConfirm(false);
    //   setDeleteInfo(null);
    //   setTimeout(() => {
    //     setOpenAlert(false);
    //   }, 2000);
    //   return;
    // }
    const updatedData = data.map(row => {
      if (row.id === rowId) {
        const updatedEmails = row[emailType]
          .split(',')
          .filter(email => email.trim() !== emailToDelete)
          .join(',');
        return { ...row, [emailType]: updatedEmails };
      }

      return row;
    });
    const updatedRow = updatedData.find(item => item.id === rowId);
    var ccType = emailType === 'ccId' ? updatedRow.ccId : row.ccId;
    var recipientType =
      emailType === 'recipientId' ? updatedRow.recipientId : row.recipientId;
    var bccType = emailType === 'bccId' ? updatedRow.bccId : row.bccId;

    // setData(updatedData);

    getRecipientEmails(
      'update',
      row.storeId,
      row.reportName,
      row.kpiReportType,
      row.mailStatus,
      row.mailFrequency,
      row.scheduledOn,
      recipientType,
      ccType,
      bccType,
      row.recipientId,
      result => {
        if (
          result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
            .statelessDbdKpiScorecardKpiScorecardMailJobs
        ) {
          setData(updatedData);
          setOpenAlert(true);
          setAlertType('success');
          setAlertMsg('Report email recipients deleted successfully');

          setTimeout(() => {
            setOpenAlert(false);
          }, 2000);
        }
      }
    );
    setOpenConfirm(false);
  };

  const handleEditClick = row => {
    setEditRowId(row.id);
    const recipientArray = row.recipientId
      .split(',')
      .filter(email => email.trim() !== '');
    const ccArray = row.ccId.split(',').filter(email => email.trim() !== '');
    const bccArray = row.bccId.split(',').filter(email => email.trim() !== '');
    const emailArray = [...recipientArray, ...ccArray, ...bccArray];
    console.log(
      'handleEditClick',
      recipientArray,
      ccArray,
      bccArray,
      emailArray
    );
    setRecipientEmails(recipientArray);
    setSavedEmails(recipientArray);
    setCcEmails(ccArray);
    setSavedCcEmails(ccArray);

    setBccEmails(bccArray);
    setSavedBccEmails(bccArray);
  };

  const handleDeleteRow = row => {
    setOpenDilog(true);
    setDeleteRow(row);
  };
  const ConfirmDeleteRow = () => {
    console.log('deleteRow', deleteRow);
    if (deleteRow) {
      const updatedRows = data.filter(r => r.id !== deleteRow.id);
      getRecipientEmails(
        'delete',
        deleteRow.storeId,
        deleteRow.reportName,
        deleteRow.kpiReportType,

        null,

        null,
        null,
        deleteRow.recipientId,
        null,
        null,
        null,
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
              .statelessDbdKpiScorecardKpiScorecardMailJobs
          ) {
            setData(updatedRows);
            setOpenAlert(true);
            setAlertType('success');
            setAlertMsg('Report email recipients deleted successfully');
            if (updatedRows.length == 0) {
              setTimeout(() => {
                handleOk();
              }, 1000);
            }
            setTimeout(() => {
              setOpenAlert(false);
            }, 2000);
          }
        }
      );
      setOpenDilog(false);
    }
  };
  const isValidEmail = (allEmails, rowData) => {
    console.log(
      'isValidEmail-',
      recipientEmails,
      savedEmails,
      ccEmails,
      savedCcEmails,
      bccEmails,
      savedBcccEmails,
      allEmails
    );
    const arraysEqual = (arr1, arr2) => {
      if (arr1.length !== arr2.length) {
        return false;
      }
      return arr1.every((value, index) => value === arr2[index]);
    };
    const hasDuplicates = array => {
      return new Set(array).size !== array.length;
    };
    const hasDuplicateEmails = hasDuplicates(allEmails);
    let allTypedEmails = [];
    allTypedEmails.push(ccValue, bccValue, inputValue);
    allTypedEmails = allTypedEmails.filter(email => email !== '');
    if (
      allEmails.length !==
        recipientEmails.length +
          ccEmails.length +
          bccEmails.length +
          allTypedEmails.length ||
      hasDuplicateEmails
    ) {
      setOpenAlert(true);
      setAlertType('warning');
      setAlertMsg('Duplicate Email id');
      setRecipientEmails(savedEmails);
      setCcEmails(savedCcEmails);
      setBccEmails(savedBcccEmails);
      setCcValue('');
      setInputValue('');
      setBccValue('');
      setTimeout(() => {
        setOpenAlert(false);
      }, 2000);
      return false;
    }
    if (recipientEmails == '' && inputValue == '') {
      setOpenAlert(true);
      setAlertType('warning');
      setAlertMsg('Please Enter Email Recipient');
      setTimeout(() => {
        setOpenAlert(false);
      }, 2000);
      return false;
    }
    if (inputValue || recipientEmails) {
      let arrayRecpient = [];

      if (inputValue) {
        arrayRecpient.push(inputValue);
      } else {
        arrayRecpient.push(recipientEmails);
      }
      var isEmail;
      arrayRecpient &&
        arrayRecpient.map(item => {
          isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
            item
          );
        });

      if (!isEmail) {
        setOpenAlert(true);
        setAlertType('warning');
        setAlertMsg('Invalid Email Format');
        setInputValue('');
        setTimeout(() => {
          setOpenAlert(false);
        }, 2000);
        return false;
      }
    } else if (ccValue && ccValue != '') {
      let arrayCc = [];
      arrayCc.push(ccValue);

      var isEmail;
      arrayCc &&
        arrayCc.map(item => {
          isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
            item
          );
        });

      if (!isEmail) {
        setOpenAlert(true);
        setAlertType('warning');
        setAlertMsg('Invalid Email Format');
        setCcValue('');
        setTimeout(() => {
          setOpenAlert(false);
        }, 2000);
        return false;
      }
    } else if (bccValue && bccValue != '') {
      let arrayBcc = [];
      arrayBcc.push(bccValue);

      var isEmail;
      arrayBcc &&
        arrayBcc.map(item => {
          isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
            item
          );
        });

      if (!isEmail) {
        setOpenAlert(true);
        setAlertType('warning');
        setAlertMsg('Invalid Email Format');
        setBccValue('');
        setTimeout(() => {
          setOpenAlert(false);
        }, 2000);
        return false;
      }
    }
    if (allEmails && allEmails != '') {
      let arrayAll = [];
      arrayAll.push(allEmails);
      var isEmail;
      allEmails &&
        allEmails.map(item => {
          isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
            item
          );
        });

      if (!isEmail) {
        setOpenAlert(true);
        setAlertType('warning');
        setAlertMsg('Invalid Email Format');

        setTimeout(() => {
          setOpenAlert(false);
        }, 2000);
        return false;
      }
    }
    if (
      arraysEqual(recipientEmails, savedEmails) &&
      arraysEqual(ccEmails, savedCcEmails) &&
      arraysEqual(bccEmails, savedBcccEmails) &&
      inputValue == '' &&
      ccValue == '' &&
      bccValue == ''
    ) {
      setOpenAlert(true);
      setAlertType('warning');
      setAlertMsg('Please make any change');
      setTimeout(() => {
        setOpenAlert(false);
      }, 2000);
      return false;
    }
    return true;
  };
  const handleSaveClick = (rowData, rowId) => {
    let noEmail = 0;
    let emailRecipientAll = [...recipientEmails];

    if (inputValue && !emailRecipientAll.includes(inputValue)) {
      emailRecipientAll.push(inputValue);
    }
    // setSavedEmails(emailRecipientAll);

    let emailCcAll = [...ccEmails];

    if (ccValue && !emailCcAll.includes(ccValue)) {
      emailCcAll.push(ccValue);
    }
    // setSavedCcEmails(emailCcAll);

    let emailBccAll = [...bccEmails];

    if (bccValue && !emailBccAll.includes(bccValue)) {
      emailBccAll.push(bccValue);
    }
    // setSavedBccEmails(emailBccAll);
    const emailObject = {
      recipientId: emailRecipientAll.join(','),
      ccId: emailCcAll.join(','),
      bccId: emailBccAll.join(',')
    };

    const updatedData = data.map(item => {
      if (item.id === editRowId) {
        return {
          ...item,
          ...emailObject
        };
      } else {
        return item;
      }
    });

    console.log('updatedData', updatedData, emailObject, data, editRowId);

    const allEmails = [
      ...new Set([...recipientEmails, ...ccEmails, ...bccEmails])
    ];
    console.log(
      'isValidEmail-11',
      recipientEmails,
      ccEmails,
      bccEmails,
      allEmails,
      ccValue,
      bccValue,
      inputValue
    );
    if (ccValue) {
      allEmails.push(ccValue);
    }
    if (bccValue) {
      allEmails.push(bccValue);
    }
    if (inputValue) {
      allEmails.push(inputValue);
    }
    // let to = emailRecipientAll.join(',');
    // let cc = emailCcAll.join(',');
    // let bcc = emailBccAll.join(',');

    // const emailsTo = to.split(',').map(email => email.trim());
    // const emailsCc = cc.split(',').map(email => email.trim());
    // const emailsBcc = bcc.split(',').map(email => email.trim());

    // Combine all emails into one array
    // const allEmails = [...emailsTo, ...emailsCc, ...emailsBcc];

    // setAllEmails(allEmail);

    // let noDataChange = 0;
    // const updatedData = data.map(row =>
    //   row.id === rowId ? { ...row, ...editData } : row
    // );

    var isValid = allEmails && isValidEmail(allEmails, rowData);

    if (!isValid) {
      noEmail = 1;
      setEditRowId(rowId);
    }
    if (noEmail == 0) {
      getRecipientEmails(
        'update',
        rowData.storeId,
        rowData.reportName,
        rowData.kpiReportType,
        rowData.mailStatus,
        rowData.mailFrequency,
        rowData.scheduledOn,
        emailRecipientAll.join(','),
        emailCcAll.join(','),
        emailBccAll.join(','),
        rowData.recipientId,
        result => {
          // this.setState({ isLoading: true });
          if (
            result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
              .statelessDbdKpiScorecardKpiScorecardMailJobs[0].storeId ==
            'Successfully Updated'
          ) {
            // setAlertMsg('Report email recipients saved successfully');
            setData(updatedData);

            setOpenAlert(true);
            setAlertType('success');
            setAlertMsg('Report email recipients saved successfully');
            setEditRowId(null);
            insertEmailToEmailList();

            setTimeout(() => {
              setOpenAlert(false);
            }, 2000);
          }
        }
      );
    }
  };
  const insertEmailToEmailList = () => {
    let array = lodash.concat(inputValue, ccValue, bccValue);

    let elementsNotExist = array.filter(
      element => element && !lodash.includes(mailUsers[0], element)
    );

    getEmail(elementsNotExist, null, 'insert', 'NULL', result => {
      this.setState({ isLoading: true });
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        this.setState({ isLoading: false });
      } else {
        this.setState({ isLoading: false });
      }
    });
  };
  const handleCancelClick = () => {
    setEditRowId(null);
  };
  const renderChips = (row, emailType, label) => {
    const emailList = row[emailType] || ''; // Default to an empty string if null or undefined

    const isDeleteAllowed =
      selectedRow &&
      selectedRow.created_by != localStorage.getItem('userID') &&
      keycloak &&
      keycloak.realmAccess &&
      keycloak.realmAccess.roles &&
      !keycloak.realmAccess.roles.includes('superadmin');

    return emailList
      .split(',')
      .filter(email => email.trim())
      .map((email, index) => (
        <Chip
          key={`${emailType}-${index}`}
          label={`${label}: ${email.trim()}`}
          style={{
            margin: 2,
            cursor: isDeleteAllowed ? 'default' : 'pointer',
            pointerEvents: isDeleteAllowed ? 'none' : 'auto'
          }}
          onDelete={() =>
            isDeleteAllowed
              ? null
              : handleDelete(row.id, emailType, email.trim(), row)
          }
        />
      ));
  };

  const label = { inputProps: { 'aria-label': 'Checkbox demo' } };
  const options = mailUsers.length > 0 ? mailUsers.flat() : [];
  const toPascalCase = str => {
    return str
      .replace(/(?:^\w|[A-Z]|\b\w|\s+)/g, (match, index) =>
        index === 0 ? match.toUpperCase() : match.toUpperCase()
      )
      .replace(/\s+/g, '');
  };
  const toRowSchedule = (scheduled, freq) => {
    console.log('toRowSchedule', scheduled, freq);
    if (freq == 'monthly') {
      if (scheduled == 1 || scheduled == 21 || scheduled == 31) {
        return scheduled + 'st of every month';
      }
      if (scheduled == 2 || scheduled == 22) {
        return scheduled + 'nd of every month';
      }
      if (scheduled == 3 || scheduled == 23) {
        return scheduled + 'rd of every month';
      } else return scheduled + 'th of every month';
    } else if (freq == 'biweekly') {
      return scheduled
        .split(',')
        .map(day => toPascalCase(day))
        .join(', ');
    } else {
      return toPascalCase(scheduled);
    }
  };
  return (
    <Dialog
      fullWidth
      maxWidth="lg"
      transition={Fade}
      classes={{
        paper: classes.paper
      }}
      BackdropProps={{
        classes: {
          root: classes.backDrop
        }
      }}
      open={openDialog}
    >
      <DialogTitle id="confirmation-dialog-title">
        <Typography
          variant="h5"
          color="primary"
          style={{
            textTransform: 'none'
          }}
        >
          Mail List - {selectedRow.report_name}
        </Typography>
      </DialogTitle>
      <Collapse in={openAlert}>
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Collapse>
      <DialogContent style={{ overflowX: 'auto', maxHeight: '60vh' }}>
        <TableContainer component={Paper}>
          <Table sx={{ minWidth: 700 }} aria-label="customized table">
            <TableHead>
              <TableRow>
                <TableCell>Recipient</TableCell>
                <TableCell align="left">Scheduled</TableCell>
                <TableCell align="left">Frequency</TableCell>
                <TableCell align="left">Actions</TableCell>
              </TableRow>
            </TableHead>
            {data.length == 0 ? (
              <TableRow>
                <TableCell colSpan={4} align="center">
                  No Rows to Show
                </TableCell>
              </TableRow>
            ) : (
              <TableBody>
                {data.map((row, index) => (
                  <TableRow
                    key={row.id}
                    style={{
                      borderBottom:
                        index !== data.length - 1 ? '2px solid #AFAFAF' : 'none'
                    }}
                  >
                    <TableCell component="th" scope="row">
                      {editRowId === row.id ? (
                        <div>
                          <Autocomplete
                            freeSolo
                            multiple
                            ListboxProps={{ style: { maxHeight: 150 } }}
                            options={options}
                            value={recipientEmails}
                            getOptionDisabled={option =>
                              recipientEmails.includes(option)
                            }
                            inputValue={inputValue}
                            onInputChange={(event, newInputValue) => {
                              setInputValue(newInputValue);
                            }}
                            onChange={(event, newValue) => {
                              setRecipientEmails(newValue);
                            }}
                            renderInput={params => (
                              <TextField
                                {...params}
                                label={
                                  <DialogActions>
                                    <Button
                                      variant="contained"
                                      className={clsx('reset-btn')}
                                      color="primary"
                                      style={{
                                        bottom: '35px',
                                        width: '38px',
                                        height: '21px'
                                      }}
                                    >
                                      To
                                    </Button>
                                  </DialogActions>
                                }
                                fullWidth
                                margin="normal"
                                InputProps={{
                                  ...params.InputProps,
                                  endAdornment: null // Remove the clear button
                                }}
                              />
                            )}
                            sx={{
                              cursor: 'default', // Change cursor to default
                              '& .MuiAutocomplete-inputRoot': {
                                cursor: 'default' // Change cursor to default for input
                              }
                            }}
                            closeIcon={null}
                          />
                          <Autocomplete
                            freeSolo
                            multiple
                            ListboxProps={{ style: { maxHeight: 150 } }}
                            options={options}
                            value={ccEmails}
                            getOptionDisabled={option =>
                              ccEmails.includes(option)
                            }
                            inputValue={ccValue}
                            onInputChange={(event, newInputValue) => {
                              setCcValue(newInputValue);
                            }}
                            onChange={(event, newValue) => {
                              setCcEmails(newValue);
                            }}
                            renderInput={params => (
                              <TextField
                                {...params}
                                label={
                                  // </Button>
                                  <DialogActions>
                                    <Button
                                      variant="contained"
                                      className={clsx('reset-btn')}
                                      color="primary"
                                      style={{
                                        bottom: '35px',
                                        width: '38px',
                                        height: '21px'
                                      }}
                                    >
                                      Cc
                                    </Button>
                                  </DialogActions>
                                }
                                fullWidth
                                margin="normal"
                                InputProps={{
                                  ...params.InputProps,
                                  endAdornment: null // Remove the clear button
                                }}
                              />
                            )}
                          />
                          <Autocomplete
                            freeSolo
                            multiple
                            ListboxProps={{ style: { maxHeight: 150 } }}
                            options={options}
                            value={bccEmails}
                            getOptionDisabled={option =>
                              bccEmails.includes(option)
                            }
                            inputValue={bccValue}
                            onInputChange={(event, newInputValue) => {
                              setBccValue(newInputValue);
                            }}
                            onChange={(event, newValue) => {
                              setBccEmails(newValue);
                            }}
                            renderInput={params => (
                              <TextField
                                {...params}
                                label={
                                  <DialogActions>
                                    <Button
                                      variant="contained"
                                      className={clsx('reset-btn')}
                                      color="primary"
                                      style={{
                                        bottom: '35px',
                                        width: '38px',
                                        height: '21px'
                                      }}
                                    >
                                      Bcc
                                    </Button>
                                  </DialogActions>
                                }
                                fullWidth
                                margin="normal"
                                InputProps={{
                                  ...params.InputProps,
                                  endAdornment: null // Remove the clear button
                                }}
                              />
                            )}
                          />
                        </div>
                      ) : (
                        <>
                          {renderChips(row, 'recipientId', 'To')}
                          {renderChips(row, 'ccId', 'Cc')}
                          {renderChips(row, 'bccId', 'Bcc')}
                        </>
                      )}
                    </TableCell>
                    <TableCell align="left">
                      {toPascalCase(row.mailFrequency)}
                    </TableCell>
                    <TableCell align="left">
                      {row.scheduledOn == 'all'
                        ? '-'
                        : toRowSchedule(row.scheduledOn, row.mailFrequency)}
                    </TableCell>
                    <TableCell>
                      {editRowId === row.id ? (
                        <Box display="flex" style={{ cursor: 'pointer' }}>
                          <Tooltip title="Save">
                            <Box m={1}>
                              <SaveIcon
                                htmlColor="rgb(0, 61, 107)"
                                classes={{
                                  paper: classes.editicon
                                }}
                                onClick={() => handleSaveClick(row, row.id)}
                              />
                            </Box>
                          </Tooltip>
                          <Tooltip title="Cancel">
                            <Box m={1}>
                              <CancelIcon
                                htmlColor="rgb(0, 61, 107)"
                                classes={{
                                  paper: classes.editicon
                                }}
                                onClick={handleCancelClick}
                              />
                            </Box>
                          </Tooltip>
                        </Box>
                      ) : (
                        <span
                          style={{
                            display: 'flex',
                            marginLeft: '10px',
                            // justifyContent: 'space-evenly',
                            pointerEvents:
                              selectedRow &&
                              selectedRow.created_by !=
                                localStorage.getItem('userID') &&
                              keycloak &&
                              keycloak.realmAccess &&
                              keycloak.realmAccess.roles &&
                              !keycloak.realmAccess.roles.includes('superadmin')
                                ? 'none'
                                : 'auto'
                          }}
                        >
                          <Tooltip title="Edit" style={{ cursor: 'pointer' }}>
                            <EditIcon
                              htmlColor={
                                selectedRow &&
                                selectedRow.created_by !=
                                  localStorage.getItem('userID') &&
                                keycloak &&
                                keycloak.realmAccess &&
                                keycloak.realmAccess.roles &&
                                !keycloak.realmAccess.roles.includes(
                                  'superadmin'
                                )
                                  ? 'grey'
                                  : 'rgb(0, 61, 107)'
                              }
                              classes={{
                                paper: classes.editicon
                              }}
                              id={'edit'}
                              onClick={() => handleEditClick(row)}
                            />
                          </Tooltip>
                          <Tooltip title="Delete" style={{ cursor: 'pointer' }}>
                            <DeleteIcon
                              style={{ marginLeft: '15px' }}
                              htmlColor={
                                selectedRow &&
                                selectedRow.created_by !=
                                  localStorage.getItem('userID') &&
                                keycloak &&
                                keycloak.realmAccess &&
                                keycloak.realmAccess.roles &&
                                !keycloak.realmAccess.roles.includes(
                                  'superadmin'
                                )
                                  ? 'grey'
                                  : 'rgb(0, 61, 107)'
                              }
                              classes={{
                                paper: classes.editicon
                              }}
                              onClick={() => handleDeleteRow(row)}
                            />
                          </Tooltip>
                        </span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            )}
          </Table>
        </TableContainer>
      </DialogContent>
      <DialogActions
        style={{
          paddingRight: '24px', // Common paddingRight for both buttons
          justifyContent: 'flex-end' // Ensure buttons align to the right
        }}
      >
        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleOk}
          color="primary"
        >
          Cancel
        </Button>
      </DialogActions>

      {openDilog || openConfirm ? (
        <Dialog
          open={openConfirm || openDilog}
          onClose={cancelDelete}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogContent>
            <Typography variant="h6" style={{ textTransform: 'none' }}>
              Are you sure you want to delete?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={cancelDelete}>Cancel</Button>
            <Button
              onClick={openConfirm ? confirmDelete : ConfirmDeleteRow}
              autoFocus
              color="primary"
            >
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      ) : null}
    </Dialog>
  );
}

export default ViewEmailDialog;
