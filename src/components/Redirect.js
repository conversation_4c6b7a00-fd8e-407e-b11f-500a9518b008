import React, { Component } from 'react';
import LoaderSkeleton from './LoaderSkeleton';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
export class Redirection extends Component {
  constructor(props) {
    super();
    this.state = { ...props };
  }
  componentWillMount() {
   
    // if (this.state.route.path == "/SearchByRO") {
    //   window.open(getVerificationDashboardBaseURL() + "/FOC3_Searchbyro/ag-grid.html?store=" + JSON.parse(localStorage.getItem('selectedStoreId'))[0]);
    // }
    // else {
    window.open(
      this.state.route.loc +
        '?store=' +
        JSON.parse(localStorage.getItem('selectedStoreId'))[0]
    );
    // }
    this.state.history.goBack();
  }
  render() {
    return <LoaderSkeleton></LoaderSkeleton>;
  }
}

export default Redirection;
