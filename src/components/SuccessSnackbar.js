import React from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import { Snackbar, SnackbarContent, colors } from '@material-ui/core';
import CheckCircleIcon from '@material-ui/icons/CheckCircleOutlined';
import { PermPhoneMsgTwoTone } from '@material-ui/icons';
import WarningIcon from '@material-ui/icons/Warning';
const useStyles = makeStyles(theme => ({
  content: {
    backgroundColor: colors.green[600]
  },
  contentFail: {
    backgroundColor: colors.red[600]
  },
  contentWarning: {
    backgroundColor: '#fff3cd', // Light amber background for warning
    color: '#856404', // Dark amber text
    border: '1px solid #ffeeba', // Amber border
    borderRadius: '4px'
  },
  message: {
    display: 'flex',
    alignItems: 'center'
  },
  icon: {
    marginRight: theme.spacing(2)
  },
  close: {
    padding: '0px',
    paddingLeft: '5px',
    color: '#fff'
  },
  dialog: {
    padding: theme.spacing(2)
  }
}));

function SuccessSnackbar({
  open,
  onClose,
  msg,
  goalFail,
  type,
  autoHideDuration
}) {
  const classes = useStyles();
  return (
    <Snackbar
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'center'
      }}
      autoHideDuration={
        autoHideDuration && autoHideDuration ? autoHideDuration : 2000
      }
      onClose={onClose}
      open={open}
    >
      {type === 'warning' ? (
        <SnackbarContent
          className={classes.contentWarning}
          message={
            <span className={classes.message}>
              <WarningIcon className={classes.icon} />
              {msg}
            </span>
          }
          variant="h6"
        />
      ) : goalFail ? (
        <SnackbarContent
          className={classes.contentFail}
          message={
            <span className={classes.message}>
              <CheckCircleIcon className={classes.icon} />
              {msg}
            </span>
          }
          variant="h6"
        />
      ) : (
        <SnackbarContent
          className={classes.content}
          message={
            <span className={classes.message}>
              <CheckCircleIcon className={classes.icon} />
              {msg}
            </span>
          }
          variant="h6"
        />
      )}
    </Snackbar>
  );
}

SuccessSnackbar.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  type: PropTypes.string
};

SuccessSnackbar.defaultProps = {
  open: true,
  onClose: () => {},
  type: null
};

export default SuccessSnackbar;
