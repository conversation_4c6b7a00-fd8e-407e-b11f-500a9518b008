/* eslint-disable react/no-danger */
import React from 'react';
import { Helm<PERSON> } from 'react-helmet';

// eslint-disable-next-line no-undef
const GA_MEASUREMENT_ID = process.env.REACT_APP_GA_MEASUREMENT_ID;

function GoogleAnalytics() {
  return (
    <Helmet>
      {/* <script
        async
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      /> */}
      {/* <script>
        {`
          window.dataLayer = window.dataLayer || [];

          function gtag() {
            dataLayer.push(arguments);
          }

          gtag('js', new Date());
          gtag('config', '${GA_MEASUREMENT_ID}');
        `}
      </script> */}
      <script
        type="text/javascript"
        src="//script.crazyegg.com/pages/scripts/0092/8173.js"
        async="async"
      ></script>
    </Helmet>
  );
}

export default GoogleAnalytics;
