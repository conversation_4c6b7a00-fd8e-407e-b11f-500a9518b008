import { text } from '@fortawesome/fontawesome-svg-core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { async } from 'validate.js';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

export const useExcelExport = () => {
  const formatCellValue = (value, symbol) => {
    var formattedValue =
      value &&
      parseFloat(value)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    if (symbol == '%') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + '%)';
      } else {
        formattedValue = formattedValue + '%';
      }
    } else if (symbol == '$') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = '($' + cellValue + ')';
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + ')';
      } else {
        formattedValue = formattedValue;
      }
    }

    return formattedValue;
  };
  const convertArray = A => {
    // Mapping object for conversion
    const mapping = {
      Competitive: 'B',
      Maintenance: 'C',
      Repair: 'D'
    };

    // Convert the array using mapping
    return A.map(item => mapping[item]);
  };
  const formatToCustomDate = value => {
    return moment(value, 'YYYY-MM').format("MMM' YY");
  };
  const getDocument = async (
    clientReportCardDetails,
    selectedOptions,
    selectedWorkmixOptions,
    type,
    imageData,
    imageAnnual,
    fileNameExport
  ) => {
    console.log(
      'clientReportCardDetails====',
      clientReportCardDetails,
      selectedOptions,
      selectedWorkmixOptions,
      type,
      imageData,
      imageAnnual
    );
    const includeMonthlyPts = selectedOptions.includes('monthlyPts');
    const includeMonthlyElr = selectedOptions.includes('monthlyElr');
    const includeAnnualizedPts = selectedOptions.includes('annualizedPts');
    const includeAnnualizedElr = selectedOptions.includes('annualizedElr');
    console.log(
      selectedOptions,
      'includeMonthlyPts=',
      includeMonthlyPts,
      'includeMonthlyElr=',
      includeMonthlyElr,
      'includeAnnualizedPts=',
      includeAnnualizedPts,
      'includeAnnualizedElr=',
      includeAnnualizedElr
    );
    const summaryData =
      clientReportCardDetails && clientReportCardDetails.summaryData;
    console.log('summaryData==', summaryData);
    const workbook = new ExcelJS.Workbook();
    if (type == 'ThreeMonth') {
      const imageId2 = workbook.addImage({
        base64: imageData,
        extension: 'png'
      });

      // const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('3 Month Total');
      // Insert required text into A1, A2, A3
      worksheet.getCell('A1').value =
        'Monthly FOPC:' + formatCellValue(summaryData.monthly_fopc || 0, '$');
      worksheet.getCell('A2').value =
        'Monthly DMS:' + formatCellValue(summaryData.monthly_dms || 0, '$');
      worksheet.getCell('A3').value = '';
      worksheet.getCell('A4').value =
        'Total:' + formatCellValue(summaryData.total_monthly || 0, '$');
      // Apply styles (bold, white text, background color) to A1, A2, A3
      ['A1', 'A2', 'A3', 'A4'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 };
        cell.alignment = { vertical: 'middle', horizontal: 'center' }; // Center both vertically & horizontally
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'be5014' } // Background color
        };
        cell.border = {
          left: { style: 'thick', color: { argb: 'FF000000' } },
          top: cellRef === 'A1' && {
            style: 'thick',
            color: { argb: 'FF000000' }
          },
          bottom: cellRef === 'A4' && {
            style: 'thick',
            color: { argb: 'FF000000' }
          }
        };
      });
      // if (includeMonthlyElr && includeMonthlyPts) {
      // Apply dark green background to B1, C1, D1, B2, C2, D2, B3, C3, D3
      [
        'B1',
        'C1',
        'D1',
        'B2',
        'C2',
        'D2',
        'B3',
        'C3',
        'D3',
        'B4',
        'C4',
        'D4'
      ].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
        cell.border = {
          top: (cellRef === 'B1' || cellRef === 'C1' || cellRef === 'D1') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          },
          bottom: (cellRef === 'B4' ||
            cellRef === 'C4' ||
            cellRef === 'D4') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          },
          right: (cellRef === 'D1' ||
            cellRef === 'D2' ||
            cellRef === 'D3' ||
            cellRef === 'D4') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          }
        };
      });
      // Apply dark green background to B1, C1, D1, B2, C2, D2, B3, C3, D3
      [
        'E1',
        'F1',
        'G1',
        'E2',
        'F2',
        'G2',
        'E3',
        'F3',
        'G3',
        'E4',
        'F4',
        'G4'
      ].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
        cell.border = {
          top: (cellRef === 'E1' || cellRef === 'F1' || cellRef === 'G1') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          },
          right: (cellRef === 'G1' ||
            cellRef === 'G2' ||
            cellRef === 'G3' ||
            cellRef === 'G4') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          },
          left: (cellRef === 'E1' ||
            cellRef === 'E2' ||
            cellRef === 'E3' ||
            cellRef === 'E4') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          },
          bottom: (cellRef === 'E4' ||
            cellRef === 'F4' ||
            cellRef === 'G4') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          }
        };
      });

      // Apply background color to the merged rows
      [1, 2, 3].forEach(row => {
        worksheet.getRow(row).eachCell(cell => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'be5014' } // Background color
          };
        });
      });
      // Insert text into B1, B2, B3
      worksheet.getCell('B1').value = includeMonthlyPts
        ? 'ROI = ' + formatCellValue(summaryData.roi_prior_lbr_parts || 0, '%')
        : '';
      // worksheet.getCell('B2').value =
      //   includeMonthlyElr || includeMonthlyPts ? 'Monthly' : '';

      worksheet.getCell('B3').value = '';
      worksheet.getCell('B4').value = includeMonthlyElr
        ? 'ROI = ' + formatCellValue(summaryData.roi_prior_repair_elr || 0, '%')
        : '';
      // Apply styles (bold, white text, center alignment, dark green background)
      ['B1', 'B2', 'B3', 'B4', 'E1', 'E4'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 }; // White text
        cell.alignment = { vertical: 'middle', horizontal: 'center' }; // Center text
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
      });
      // }
      if (includeMonthlyPts && includeMonthlyElr) {
        // Merge C1 and D1, C2 and D2, C3 and D3, C4 and D4
        worksheet.mergeCells('C1:D1');
        worksheet.mergeCells('C2:D2');
        worksheet.mergeCells('C3:D3');
        worksheet.mergeCells('C4:D4');
        // Merge F1 and G1
        // worksheet.mergeCells('F1:G1');
        // worksheet.mergeCells('F2:G2');
        // worksheet.mergeCells('F3:G3');
        // worksheet.mergeCells('F4:G4');

        // Insert text into the merged cell
        worksheet.getCell('C1').value = includeMonthlyPts
          ? 'Based on Total Pts & Lbr GP Change ofss'
          : '';
        worksheet.getCell('C2').value = includeMonthlyPts
          ? formatCellValue(summaryData.if_prior_parts_labor_gp || 0, '$')
          : '';
        worksheet.getCell('C3').value = includeMonthlyElr
          ? 'Based on Repair ELR Change of'
          : '';
        worksheet.getCell('C4').value = includeMonthlyElr
          ? formatCellValue(summaryData.if_prior_repair_elr || 0, '$')
          : '';

        // Apply font style and alignment to C2 and C4
        ['C2', 'C4'].forEach(cellRef => {
          const cell = worksheet.getCell(cellRef);
          cell.font = {
            bold: true,
            name: 'Roboto', // Apply the same font or change it if needed
            size: 12,
            color: { argb: 'FFFFFF' }
          };
          cell.alignment = {
            vertical: 'middle',
            horizontal: 'center'
          };
        });
      }

      if (includeAnnualizedElr && includeAnnualizedPts) {
        // Merge F1 and G1
        worksheet.mergeCells('F1:G1');
        worksheet.mergeCells('F2:G2');
        worksheet.mergeCells('F3:G3');
        worksheet.mergeCells('F4:G4');
        worksheet.getCell('E1').value = includeAnnualizedPts
          ? 'ROI = ' +
            formatCellValue(summaryData.roi_annual_lbr_parts || 0, '%')
          : '';
        // worksheet.getCell('E2').value = '';
        // worksheet.getCell('E3').value = '';
        worksheet.getCell('E4').value = includeAnnualizedElr
          ? 'ROI = ' +
            formatCellValue(summaryData.roi_annual_repair_elr || 0, '%')
          : '';
        worksheet.getCell('F1').value = includeAnnualizedPts
          ? 'Based on Total Pts & Lbr GP Change of'
          : '';
        worksheet.getCell('F2').value = includeAnnualizedPts
          ? formatCellValue(summaryData.if_annual_parts_labor_gp || 0, '$')
          : '';
        worksheet.getCell('F3').value = includeAnnualizedElr
          ? 'Based on Repair ELR Change of'
          : '';
        worksheet.getCell('F4').value = includeAnnualizedElr
          ? formatCellValue(summaryData.if_annual_repair_elr || 0, '$')
          : '';
        ['F2', 'F4'].forEach(cellRef => {
          const cell = worksheet.getCell(cellRef);
          cell.font = {
            bold: true,
            name: 'Roboto', // Apply the same font or change it if needed
            size: 12,
            color: { argb: 'FFFFFF' }
          };
          cell.alignment = {
            vertical: 'middle',
            horizontal: 'center'
          };
          cell.border = {
            right: {
              style: 'thick',
              color: { argb: 'FF000000' }
            }
          };
        });
      }
      // Center align text both horizontally and vertically
      ['C1', 'C3'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 10 }; // White text, bold
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
      });
      ['F1', 'F3'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 10 }; // White text, bold
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
        cell.border = {
          right: {
            style: includeAnnualizedElr && includeAnnualizedPts && 'thick',
            color: { argb: 'FF000000' }
          }
        };
      });
      // Apply white bottom border under text only for D2 and G2
      ['D2', 'G2'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        console.log(
          'cell====',
          cell._address == 'D2' && includeAnnualizedElr && includeAnnualizedPts
        );
        cell.alignment = { horizontal: 'center' }; // Center align to limit border width
        if (cell._address == 'D2' && includeMonthlyElr && includeMonthlyPts) {
          cell.border = {
            bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } } // White bottom border
          };
        }
        if (
          cell._address == 'G2' &&
          includeAnnualizedElr &&
          includeAnnualizedPts
        ) {
          cell.border = {
            bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } } // White bottom border
          };
        }
        cell.border = {
          right: {
            style: 'thick',
            color: { argb: 'FF000000' }
          }
        };
      });
      if (includeMonthlyElr || includeMonthlyPts) {
        if (includeMonthlyElr && includeMonthlyPts) {
          worksheet.mergeCells('B2:B3');
          const monthlyCell = worksheet.getCell('B2');
          worksheet.addImage(imageId2, {
            tl: { col: 1.8, row: 1.8 },
            ext: { width: 90, height: 18 }
          });
          monthlyCell.alignment = { vertical: 'middle', horizontal: 'center' };
          // monthlyCell.font = {
          //   bold: true,
          //   color: { argb: '000000' },
          //   size: 12
          // };
          monthlyCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // White background
          };
        }
        if (!includeMonthlyElr && includeMonthlyPts) {
          worksheet.mergeCells('B1:B3');
          worksheet.getCell('B1').value = `ROI = ${formatCellValue(
            summaryData.roi_prior_lbr_parts || 0,
            '%'
          )}`;
          worksheet.getCell('B1').alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          worksheet.getCell('B1').font = {
            bold: true,
            size: 14,
            color: { argb: 'FFFFFF' }
          };
          worksheet.getCell('B1').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' }
          };
          worksheet.addImage(imageId2, {
            tl: { col: 1.8, row: 2.5 },
            ext: { width: 90, height: 18 }
          });

          worksheet.mergeCells('C2:D3');
          const mergedCell = worksheet.getCell('C2');
          mergedCell.value = {
            richText: [
              {
                text: includeMonthlyPts
                  ? 'Based on Total Pts & Lbr GP Change of\n'
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Correct way to set font family
                  size: 8,
                  color: { argb: 'FFFFFF' } // White text
                }
              },
              {
                text: includeMonthlyPts
                  ? formatCellValue(
                      summaryData.if_prior_parts_labor_gp || 0,
                      '$'
                    )
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Apply the same font or change it if needed
                  size: 12,
                  color: { argb: 'FFFFFF' } // White text
                }
              }
            ]
          };
          mergedCell.alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          mergedCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // Dark Green background
          };
          mergedCell.border = {
            right: {
              style: 'thick',
              color: { argb: 'FF000000' }
            }
          };
        }

        if (includeMonthlyElr && !includeMonthlyPts) {
          worksheet.mergeCells('B2:B4');
          worksheet.getCell('B2').value = `ROI = ${formatCellValue(
            summaryData.roi_prior_repair_elr || 0,
            '%'
          )}`;
          worksheet.getCell('B2').alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          worksheet.getCell('B2').font = {
            bold: true,
            size: 14,
            color: { argb: 'FFFFFF' }
          };
          worksheet.getCell('B2').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' }
          };
          worksheet.addImage(imageId2, {
            tl: { col: 1.8, row: 1 },
            ext: { width: 90, height: 18 }
          });

          worksheet.mergeCells('C2:D3');
          const mergedCell = worksheet.getCell('C2');
          mergedCell.value = {
            richText: [
              {
                text: includeMonthlyElr
                  ? 'Based on Repair ELR Change of\n'
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Correct way to set font family
                  size: 8,
                  color: { argb: 'FFFFFF' } // White text
                }
              },
              {
                text: includeMonthlyElr
                  ? formatCellValue(summaryData.if_prior_repair_elr || 0, '$')
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Apply the same font or change it if needed
                  size: 12,
                  color: { argb: 'FFFFFF' } // White text
                }
              }
            ]
          };
          mergedCell.alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          mergedCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // Dark Green background
          };
        }
      }
      if (includeAnnualizedElr || includeAnnualizedPts) {
        console.log(
          'enter=10',
          includeAnnualizedElr,
          '==',
          includeAnnualizedPts
        );
        const imageAnnualized = workbook.addImage({
          base64: imageAnnual,
          extension: 'png'
        });
        if (includeAnnualizedElr && includeAnnualizedPts) {
          worksheet.mergeCells('E2:E3');
          const monthlyCell = worksheet.getCell('E2');
          worksheet.addImage(imageAnnualized, {
            tl: { col: 4.8, row: 1.9 },
            ext: { width: 90, height: 18 }
          });
          monthlyCell.alignment = { vertical: 'middle', horizontal: 'center' };
          // monthlyCell.font = {
          //   bold: true,
          //   color: { argb: '000000' },
          //   size: 12
          // };
          monthlyCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // White background
          };
        }
        if (!includeAnnualizedElr && includeAnnualizedPts) {
          console.log('enter=12');
          worksheet.mergeCells('E1:E3');
          worksheet.getCell('E1').value = `ROI = ${formatCellValue(
            summaryData.roi_annual_lbr_parts || 0,
            '%'
          )}`;
          worksheet.getCell('E1').alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          worksheet.getCell('E1').font = {
            bold: true,
            size: 14,
            color: { argb: 'FFFFFF' }
          };
          worksheet.getCell('E1').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' }
          };
          worksheet.addImage(imageAnnualized, {
            tl: { col: 4.8, row: 2.5 },
            ext: { width: 90, height: 18 }
          });

          worksheet.mergeCells('F2:G3');
          const mergedCell = worksheet.getCell('F2');
          mergedCell.value = {
            richText: [
              {
                text: includeAnnualizedPts
                  ? 'Based on Total Pts & Lbr GP Change of\n'
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Correct way to set font family
                  size: 8,
                  color: { argb: 'FFFFFF' } // White text
                }
              },
              {
                text: includeAnnualizedPts
                  ? formatCellValue(
                      summaryData.if_annual_parts_labor_gp || 0,
                      '$'
                    )
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Apply the same font or change it if needed
                  size: 12,
                  color: { argb: 'FFFFFF' } // White text
                }
              }
            ]
          };
          mergedCell.alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          mergedCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // Dark Green background
          };
          mergedCell.border = {
            right: {
              style: 'thick',
              color: { argb: 'FF000000' }
            }
          };
        }
        if (includeAnnualizedElr && !includeAnnualizedPts) {
          console.log('enter=123');
          worksheet.mergeCells('E2:E4');
          worksheet.getCell('E2').value = `ROI = ${formatCellValue(
            summaryData.roi_annual_repair_elr || 0,
            '%'
          )}`;
          worksheet.getCell('E2').alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          worksheet.getCell('E2').font = {
            bold: true,
            size: 14,
            color: { argb: 'FFFFFF' }
          };
          worksheet.getCell('E2').fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' }
          };
          worksheet.addImage(imageAnnualized, {
            tl: { col: 4.8, row: 1 },
            ext: { width: 90, height: 18 }
          });

          worksheet.mergeCells('F2:G4');
          const mergedCell = worksheet.getCell('F2');
          mergedCell.value = {
            richText: [
              {
                text: includeAnnualizedElr
                  ? 'Based on Repair ELR Change of\n'
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Correct way to set font family
                  size: 8,
                  color: { argb: 'FFFFFF' } // White text
                }
              },
              {
                text: includeAnnualizedElr
                  ? formatCellValue(summaryData.if_annual_repair_elr || 0, '$')
                  : '',
                font: {
                  bold: true,
                  name: 'Roboto', // Apply the same font or change it if needed
                  size: 12,
                  color: { argb: 'FFFFFF' } // White text
                }
              }
            ]
          };
          mergedCell.alignment = {
            vertical: 'middle',
            horizontal: 'center',
            wrapText: true
          };
          mergedCell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // Dark Green background
          };
          mergedCell.border = {
            bottom: {
              style: 'thick',
              color: { argb: 'FF000000' }
            },
            right: {
              style: 'thick',
              color: { argb: 'FF000000' }
            }
          };
        }
      }

      // Set A5 cell with "Report Card"
      const cellA5 = worksheet.getCell('A5');
      cellA5.value = 'Report Card';
      cellA5.alignment = { horizontal: 'center', vertical: 'middle' };
      cellA5.font = { bold: true, size: 12, color: { argb: 'FF000000' } }; // Black text
      cellA5.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'ffc000' } // Yellow background
      };

      // Add a black border on the right side of A5
      cellA5.border = {
        right: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thick', color: { argb: 'FF000000' } },
        top: { style: 'thick', color: { argb: 'FF000000' } },
        bottom: { style: 'thick', color: { argb: 'FF000000' } }
      };

      // Merge B5:G5
      worksheet.mergeCells('B5:G5');
      const mergedCell = worksheet.getCell('B5');
      mergedCell.border = {
        top: { style: 'thick', color: { argb: 'FF000000' } },
        bottom: { style: 'thick', color: { argb: 'FF000000' } },
        right: { style: 'thick', color: { argb: 'FF000000' } }
      };
      // Set A6 cell with an orange background color
      const cellA6 = worksheet.getCell('A6');
      cellA6.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'be5014' } // Orange orange color
      };
      // Apply thin black border on all sides
      cellA6.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } }, // Black Top Border
        // bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
        left: { style: 'thick', color: { argb: 'FF000000' } }, // Black Left Border
        right: { style: 'thin', color: { argb: 'FF000000' } } // Black Right Border
      };
      // Set text for B6 with line break
      const cellB6 = worksheet.getCell('B6');
      cellB6.value =
        moment(summaryData.prior_to_fopc_start_date).format('MM/YY') +
        ' - ' +
        moment(summaryData.prior_to_fopc_end_date).format('MM/YY') +
        '\nPrior to FOPC';
      cellB6.alignment = {
        vertical: 'middle',
        horizontal: 'center',
        wrapText: true // Enables text wrapping for multi-line display
      };
      cellB6.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 12 }; // White text
      cellB6.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'be5014' } // Orange background
      };
      // Apply thin black border on all sides
      cellB6.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } }, // Black Top Border
        bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
        left: { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
        right: { style: 'thin', color: { argb: 'FF000000' } } // Black Right Border
      };

      // Merge C6 and D6
      worksheet.mergeCells('C6:D6');

      // Get the merged cell reference
      const cellC6D6 = worksheet.getCell('C6');
      // Set text for cellC6D6 with line break

      cellC6D6.alignment = {
        vertical: 'middle',
        horizontal: 'center',
        wrapText: true // Enables text wrapping for multi-line display
      };
      cellC6D6.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 12 }; // White text
      // Set orange background
      cellC6D6.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'be5014' } // Orange background
      };

      // Apply thin black border on all four sides
      cellC6D6.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } }, // Black Top Border
        bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
        left: { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
        right: { style: 'thick', color: { argb: 'FF000000' } } // Black Right Border
      };
      // Get the E6 cell
      const cellE6 = worksheet.getCell('E6');

      // Set the text
      cellE6.value = 'Baseline Annualized';

      // Center align the text
      cellE6.alignment = { vertical: 'middle', horizontal: 'center' };

      // Set white text color
      cellE6.font = { color: { argb: 'FFFFFFFF' }, bold: true };

      // Set orange background
      cellE6.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'be5014' } // Orange background
      };

      // Apply thin black border on all four sides
      cellE6.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } }, // Black Top Border
        bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
        left: { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
        right: { style: 'thin', color: { argb: 'FF000000' } } // Black Right Border
      };
      // Merge F6 and G6
      worksheet.mergeCells('F6:G6');

      // Get the merged cell reference
      const cellF6G6 = worksheet.getCell('F6');
      cellF6G6.alignment = {
        vertical: 'middle',
        horizontal: 'center',
        wrapText: true // Enables text wrapping for multi-line display
      };
      cellF6G6.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 12 }; // White text
      // Set orange background
      cellF6G6.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'be5014' } // Orange background
      };

      cellF6G6.value =
        moment(summaryData.used_to_annul_cur_3_mon_start_date).format('MM/YY') +
        ' - ' +
        moment(summaryData.used_to_annul_cur_3_mon_end_date).format('MM/YY') +
        '\nLast 3 MTHs - Annualized'; // Add line break for stacked text
      // Set orange background
      cellF6G6.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'be5014' } // Orange background
      };

      // Apply thin black border on all four sides
      cellF6G6.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } }, // Black Top Border
        bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
        left: { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
        right: { style: 'thick', color: { argb: 'FF000000' } } // Black Right Border
      };

      // Set text and formatting
      mergedCell.value = localStorage.getItem('storeGroup');
      mergedCell.alignment = { horizontal: 'center', vertical: 'middle' };
      mergedCell.font = { bold: true, size: 12, color: { argb: 'FF000000' } }; // Black text
      mergedCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'ffc000' } // Yellow background
      };
      // Define headers in row 6
      worksheet.insertRow(7, [
        'KPIs',
        '3 MTH Avg (Baseline)',
        formatToCustomDate(summaryData.used_to_annul_cur_3_mon_end_date),
        'Variance',
        'Prior Annual Pace',
        'Annual Pace',
        'Variance'
      ]);

      // Style header row (row 7)
      worksheet.getRow(7).eachCell((cell, colNumber) => {
        console.log('dddddd===', cell, colNumber);
        cell.font = {
          bold: colNumber === 1 ? true : false, // Bold only for 'KPIs'
          size: colNumber === 1 ? 14 : 12, // Font size 14 for 'KPIs', default size 12 for others
          color: { argb: 'FFFFFFFF' }
        };

        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'be5014' } // Light pink background
        };

        cell.alignment = {
          vertical: 'middle',
          horizontal: colNumber === 1 ? 'right' : 'center'
        };

        // Apply thin black border (remove top border)
        cell.border = {
          top:
            colNumber === 1
              ? undefined
              : { style: 'thin', color: { argb: 'FF000000' } }, // No top border for column 1
          bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
          left: { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
          right: {
            style: colNumber == '4' || colNumber == '7' ? 'thick' : 'thin',
            color: { argb: 'FF000000' }
          } // Black Right Border
        };
      });

      // Set column widths
      worksheet.columns = [
        { key: 'kpi_name', width: 30 },
        { key: 'three_mon_avg', width: 30 },
        { key: 'last_month', width: 30 },
        { key: 'prior_variance', width: 30 },
        { key: 'prior_annual_pace', width: 30 },
        { key: 'current_annual_pace', width: 30 },
        { key: 'annual_variance', width: 30 }
      ];
      if (clientReportCardDetails && clientReportCardDetails.monthlyData) {
        let filteredData = [];
        console.log('selectedWorkmixOptions===', selectedWorkmixOptions);
        clientReportCardDetails.monthlyData.forEach((item, index) => {
          if (index < 12) {
            // First 12 rows, add them directly
            filteredData.push(item);
          } else if (index === 12 || index === 24 || index === 36) {
            // Check if kpi_name is in selectedWorkmixOptions
            if (selectedWorkmixOptions.includes(item.kpi_name)) {
              // Filter remaining data where kpi_category matches this kpi_name
              filteredData = filteredData.concat(
                clientReportCardDetails.monthlyData.filter(
                  data => data.kpi_category === item.kpi_name
                )
              );
            }
          }
        });
        console.log(
          'monthlyData====',
          clientReportCardDetails.monthlyData,
          '==',
          filteredData
        );
        // Add rows to Excel worksheet
        filteredData.forEach((item, index) => {
          console.log('item=', item, index);
          // Modify each item by appending '$' to numerical values
          const modifiedItem = Object.fromEntries(
            Object.entries(item).map(([key, value]) =>
              key == 'kpi_name'
                ? [key, value]
                : [key, formatCellValue(value, item.kpi_symbol)]
            )
          );

          const row = worksheet.addRow(modifiedItem); // Add modified row

          const rowNumber = index + 8; // Start from row 8
          const lastRowNumber = filteredData.length + 7; // Start from row
          // Apply Light Green Background for columns D and G
          ['D', 'G'].forEach(col => {
            const cell = worksheet.getCell(`${col}${rowNumber}`);
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'e7f1fa' } // Light Green background
            };
            cell.font = {
              bold: true,
              color: {
                argb:
                  typeof cell.value === 'string' &&
                  cell.value.includes('(') &&
                  cell.value.includes(')') > 0
                    ? 'FF0000'
                    : cell.value === '0' || cell.value === 0
                    ? '000000'
                    : '3c7d22'
              } // White text
            };
            cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally

            cell.border = {
              top: { style: 'thin', color: { argb: 'FF000000' } }, // No top border for column 1
              bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
              left: { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
              right: { style: 'thick', color: { argb: 'FF000000' } } // Black Right Border
            };
          });

          // Apply Orange Background & Bold White Text for rows 20, 32, 44 in columns B-G
          if ([20, 32, 44].includes(rowNumber)) {
            ['B', 'C', 'D', 'E', 'F', 'G'].forEach(col => {
              const cell = worksheet.getCell(`${col}${rowNumber}`);
              cell.value = ' '; // Set cell value as empty string
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'be5014' } // Orange background
              };
              cell.font = {
                bold: true,
                color: { argb: 'FFFFFF' } // White text
              };
              cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally
              cell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } }, // No top border for column 1
                bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
                left: { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
                right: {
                  style: col === 'D' || col === 'G' ? 'thick' : 'thin',
                  color: { argb: 'FF000000' }
                } // Black Right Border
              };
            });
            ['A'].forEach(col => {
              const cell = worksheet.getCell(`${col}${rowNumber}`);
              // cell.value = ' '; // Set cell value as empty string
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'be5014' } // Orange background
              };
              cell.font = {
                bold: true,
                color: { argb: 'FFFFFF' } // White text
              };
              cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally
              cell.border = {
                left: { style: 'thick', color: { argb: 'FF000000' } } // Black Right Border
              };
              cell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } }, // No top border for column 1
                bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
                left: { style: 'thick', color: { argb: 'FF000000' } }, // Black Right Border
                right: { style: 'thin', color: { argb: 'FF000000' } } // Black Right Border
              };
            });
          } else {
            ['A', 'B', 'C', 'D', 'E', 'F', 'G'].forEach(col => {
              console.log('dd=d==', item, index, col, lastRowNumber, rowNumber);
              const cell = worksheet.getCell(`${col}${rowNumber}`);
              cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally
              cell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } }, // No top border for column 1
                bottom: {
                  style: lastRowNumber == rowNumber ? 'thick' : 'thin',
                  color: { argb: 'FF000000' }
                }, // Black Bottom Border
                left: {
                  style: col == 'A' ? 'thick' : 'thin',
                  color: { argb: 'FF000000' }
                }, // Black Right Border
                right: {
                  style: col === 'D' || col === 'G' ? 'thick' : 'thin',
                  color: { argb: 'FF000000' }
                } // Black Right Border
              };
              if (index + 7 == lastRowNumber) {
                const cells = worksheet.getCell(`${col}${rowNumber}`);
                cells.border = {
                  bottom: { style: 'thick', color: { argb: 'FF000000' } }, // Black Right Border
                  left: {
                    style: col === 'A' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  },
                  right: {
                    style: col === 'D' || col === 'G' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  }
                };
              }
              if (7 == rowNumber) {
                console.log('item===1===1==', index + 7 == lastRowNumber);
                const cellVal = worksheet.getCell(`${col}${rowNumber}`);
                cellVal.border = {
                  top: { style: 'thick', color: { argb: 'FF000000' } }, // Black Right Border
                  left: {
                    style: col === 'A' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  },
                  right: {
                    style: col === 'D' || col === 'G' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  }
                };
              }
            });
          }
        });
      }
    } else {
      // const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('1 Month Total');
      // Insert required text into A1, A2, A3
      worksheet.getCell('A1').value =
        'Monthly FOPC:' +
        formatCellValue(summaryData.monthly_fopc_fee || 0, '$');
      worksheet.getCell('A2').value =
        'Monthly DMS:' + formatCellValue(summaryData.monthly_dms_fee || 0, '$');
      worksheet.getCell('A3').value = '';
      worksheet.getCell('A4').value =
        'Total:' +
        formatCellValue(summaryData.total_monthly_fopc_dms_fee || 0, '$');
      // Apply styles (bold, white text, background color) to A1, A2, A3
      ['A1', 'A2', 'A3', 'A4'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 };
        cell.alignment = { vertical: 'middle', horizontal: 'center' }; // Center both vertically & horizontally
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'be5014' } // Background color
        };
        cell.border = {
          left: { style: 'double', color: { argb: 'FF000000' } } // Dark thick left border
        };
      });
      ['A1', 'B1', 'C1', 'D1'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.border = {
          top: { style: 'thick', color: { argb: 'FF000000' } } // Thick dark border (black)
        };
      });

      // Apply dark green background to B1, C1, D1, B2, C2, D2, B3, C3, D3
      [
        'B1',
        'C1',
        'D1',
        'B2',
        'C2',
        'D2',
        'B3',
        'C3',
        'D3',
        'B4',
        'C4',
        'D4'
      ].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
        cell.border = {
          right: (cellRef === 'D1' || cellRef === 'D4') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          },
          top: (cellRef === 'B1' || cellRef === 'C1' || cellRef === 'D1') && {
            style: 'thick',
            color: { argb: 'FF000000' }
          } // Thick dark border (black)
        };
      });
      // Apply background color to the merged rows
      [1, 2, 3].forEach(row => {
        worksheet.getRow(row).eachCell(cell => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'be5014' } // Background color
          };
        });
      });

      // Insert text into B1, B2, B3
      worksheet.getCell('B1').value = includeMonthlyPts
        ? 'ROI = ' + formatCellValue(summaryData.roi_parts_labor_gp || 0, '%')
        : '';
      worksheet.getCell('B2').value = '';
      // includeMonthlyElr || includeMonthlyPts ? 'Monthly' : '';
      worksheet.getCell('B3').value = '';
      worksheet.getCell('B4').value = includeMonthlyElr
        ? 'ROI = ' + formatCellValue(summaryData.roi_repair_elr || 0, '%')
        : '';
      // Apply styles (bold, white text, center alignment, dark green background)
      ['B1', 'B2', 'B3', 'B4'].forEach(cellRef => {
        const cell = worksheet.getCell(cellRef);
        cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 14 }; // White text
        cell.alignment = { vertical: 'middle', horizontal: 'center' }; // Center text
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
      });
      if (
        (includeMonthlyPts && !includeMonthlyElr) ||
        (!includeMonthlyPts && includeMonthlyElr)
      ) {
        // Merge B1 to B4
        worksheet.mergeCells('B1:B4');
      }
      if (includeMonthlyPts && !includeMonthlyElr) {
        // Set the merged cell value
        const mergedCells = worksheet.getCell('B1');
        mergedCells.value = includeMonthlyPts
          ? 'ROI = ' + formatCellValue(summaryData.roi_parts_labor_gp || 0, '%')
          : '';
        // Center align the text
        mergedCells.alignment = { vertical: 'middle', horizontal: 'center' };
      }
      if (includeMonthlyElr && !includeMonthlyPts) {
        // Set the merged cell value
        const mergedCells = worksheet.getCell('B1');
        mergedCells.value = includeMonthlyElr
          ? 'ROI = ' + formatCellValue(summaryData.roi_repair_elr || 0, '%')
          : '';
        // Center align the text
        mergedCells.alignment = { vertical: 'middle', horizontal: 'center' };
      }
      if (includeMonthlyPts && includeMonthlyElr) {
        console.log('ENTER=1');
        // Merge C1 and D1, C2 and D2, C3 and D3, C4 and D4
        worksheet.mergeCells('C1:D1');
        worksheet.mergeCells('C2:D2');
        worksheet.mergeCells('C3:D3');
        worksheet.mergeCells('C4:D4');

        // Insert text into the merged cell
        worksheet.getCell('C1').value = includeMonthlyPts
          ? 'Based on Total Pts & Lbr GP Change of'
          : '';
        const mergedCell = worksheet.getCell('C1');
        mergedCell.border = {
          right: { style: 'thick', color: { argb: 'FF000000' } },
          top: { style: 'thick', color: { argb: 'FF000000' } }
        };
        worksheet.getCell('C2').value = includeMonthlyPts
          ? formatCellValue(summaryData.if_parts_labor_gp || 0, '$')
          : '';
        worksheet.getCell('C3').value = includeMonthlyElr
          ? 'Based on Repair ELR Change of'
          : '';
        worksheet.getCell('C4').value = includeMonthlyElr
          ? formatCellValue(summaryData.if_repair_elr || 0, '$')
          : '';

        // Center align text both horizontally and vertically
        ['C1', 'C3'].forEach(cellRef => {
          const cell = worksheet.getCell(cellRef);
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
          cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 10 }; // White text, bold
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // Dark Green background
          };
          cell.border = {
            right: { style: 'thick', color: { argb: 'FF000000' } } // Thick dark border (black)
          };
        });
        ['C1'].forEach(cellRef => {
          const cell = worksheet.getCell(cellRef);
          cell.border = {
            top: { style: 'thick', color: { argb: 'FF000000' } },
            right: { style: 'thick', color: { argb: 'FF000000' } }
          };
        });

        ['C2', 'C4'].forEach(cellRef => {
          const cell = worksheet.getCell(cellRef);
          cell.alignment = { vertical: 'middle', horizontal: 'center' };
          cell.font = { bold: true, color: { argb: 'FFFFFFFF' }, size: 12 }; // White text, bold
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: '3c7d22' } // Dark Green background
          };
          cell.border = {
            right: { style: 'thick', color: { argb: 'FF000000' } } // Thick dark border (black)
          };
        });
        // Apply white bottom border under text only for D2 and G2
        ['D2'].forEach(cellRef => {
          const cell = worksheet.getCell(cellRef);
          console.log(
            'cell====',
            cell._address == 'D2' &&
              includeAnnualizedElr &&
              includeAnnualizedPts
          );
          cell.border = {
            right: { style: 'thick', color: { argb: 'FF000000' } } // Thick dark border (black)
          };
          cell.alignment = { horizontal: 'center' }; // Center align to limit border width
          if (cell._address == 'D2' && includeMonthlyElr && includeMonthlyPts) {
            cell.border = {
              bottom: { style: 'thin', color: { argb: 'FFFFFFFF' } }, // White bottom border
              right: { style: 'thick', color: { argb: 'FF000000' } }
            };
          }
        });
      }

      if (
        (includeMonthlyPts && !includeMonthlyElr) ||
        (!includeMonthlyPts && includeMonthlyElr)
      ) {
        console.log('SSSS===1');
        // Merge B1 to B4
        worksheet.mergeCells('C2:D3');
      }
      if (includeMonthlyPts && !includeMonthlyElr) {
        console.log('SSSS===2');

        // worksheet.getCell('C1').value = includeMonthlyPts
        // ? 'Based on Total Pts & Lbr GP Change of'
        // : '';
        const mergedCell = worksheet.getCell('C2');
        mergedCell.value = {
          richText: [
            {
              text: includeMonthlyPts
                ? 'Based on Total Pts & Lbr GP Change of\n'
                : '',
              font: {
                bold: true,
                name: 'Roboto', // Correct way to set font family
                size: 8,
                color: { argb: 'FFFFFF' } // White text
              }
            },
            {
              text: includeMonthlyPts
                ? formatCellValue(summaryData.if_parts_labor_gp || 0, '$')
                : '',
              font: {
                bold: true,
                name: 'Roboto', // Apply the same font or change it if needed
                size: 12,
                color: { argb: 'FFFFFF' } // White text
              }
            }
          ]
        };
        mergedCell.alignment = {
          vertical: 'middle',
          horizontal: 'center',
          wrapText: true
        };
        mergedCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
        mergedCell.border = {
          right: { style: 'thick', color: { argb: 'FF000000' } } // Black thick border
        };
      }
      if (includeMonthlyElr && !includeMonthlyPts) {
        console.log('ENTER=1234');
        const mergedCell = worksheet.getCell('C2');
        mergedCell.value = {
          richText: [
            {
              text: includeMonthlyElr ? 'Based on Repair ELR Change of\n' : '',
              font: {
                bold: true,
                name: 'Roboto', // Correct way to set font family
                size: 8,
                color: { argb: 'FFFFFF' } // White text
              }
            },
            {
              text: includeMonthlyElr
                ? formatCellValue(summaryData.if_repair_elr || 0, '$')
                : '',
              font: {
                bold: true,
                name: 'Roboto', // Apply the same font or change it if needed
                size: 12,
                color: { argb: 'FFFFFF' } // White text
              }
            }
          ]
        };
        mergedCell.alignment = {
          vertical: 'middle',
          horizontal: 'center',
          wrapText: true
        };
        mergedCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '3c7d22' } // Dark Green background
        };
        mergedCell.border = {
          right: { style: 'thick', color: { argb: 'FF000000' } } // Black thick border
        };
      }

      // Set A5 cell with "Report Card"
      const cellA5 = worksheet.getCell('A5');
      cellA5.value = 'Report Card';
      cellA5.alignment = { horizontal: 'center', vertical: 'middle' };
      cellA5.font = { bold: true, size: 12, color: { argb: 'FF000000' } }; // Black text
      cellA5.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'ffc000' } // Yellow background
      };

      // Add a black border on the right side of A5
      cellA5.border = {
        right: { style: 'thin', color: { argb: 'FF000000' } }, // Black right border
        left: { style: 'thick', color: { argb: 'FF000000' } }, // Black right border
        top: { style: 'thick', color: { argb: 'FF000000' } } // Black right border
      };

      // Merge B5:G5
      worksheet.mergeCells('B5:D5');
      const mergedCell = worksheet.getCell('B5');

      // Set text and formatting
      mergedCell.value = localStorage.getItem('storeGroup');
      mergedCell.alignment = { horizontal: 'center', vertical: 'middle' };
      mergedCell.font = { bold: true, size: 12, color: { argb: 'FF000000' } }; // Black text
      mergedCell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'ffc000' } // Yellow background
      };
      mergedCell.border = {
        right: { style: 'thick', color: { argb: 'FF000000' } }, // Black right border
        top: { style: 'thick', color: { argb: 'FF000000' } } // Black right border
      };
      // Define headers in row 6
      worksheet.insertRow(6, [
        'KPIs',
        formatToCustomDate(
          clientReportCardDetails &&
            clientReportCardDetails.monthlyData &&
            clientReportCardDetails.monthlyData[0].measured_mon
        ),
        formatToCustomDate(
          clientReportCardDetails &&
            clientReportCardDetails.monthlyData &&
            clientReportCardDetails.monthlyData[0].prior_mon
        ),
        'Variance'
      ]);

      // Style header row (row 7)
      worksheet.getRow(6).eachCell((cell, colNumber) => {
        cell.font = {
          bold: colNumber === 1 ? true : false, // Bold only for 'KPIs'
          size: colNumber === 1 ? 14 : 12, // Font size 14 for 'KPIs', default size 12 for others
          color: { argb: 'FFFFFFFF' }
        };

        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'be5014' } // Light pink background
        };

        cell.alignment = {
          vertical: 'middle',
          horizontal: colNumber === 1 ? 'right' : 'center'
        };

        // Apply thin black border (remove top border)
        cell.border = {
          top: { style: 'thick', color: { argb: 'FF000000' } },
          bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Bottom Border
          left:
            colNumber === 1
              ? { style: 'thick', color: { argb: 'FF000000' } }
              : { style: 'thin', color: { argb: 'FF000000' } }, // Black Left Border
          right:
            colNumber === 4
              ? { style: 'thick', color: { argb: 'FF000000' } }
              : { style: 'thin', color: { argb: 'FF000000' } } // Black Right Border
        };
      });

      // Set column widths
      worksheet.columns = [
        { key: 'kpi_name', width: 30 },
        { key: 'measured_mon', width: 30 },
        { key: 'prior_mon', width: 30 },
        { key: 'variance', width: 30 }
      ];
      if (clientReportCardDetails && clientReportCardDetails.monthlyData) {
        let filteredData = [];
        console.log(
          'filteredData=====',
          clientReportCardDetails.monthlyData,
          'selectedWorkmixOptions===',
          selectedWorkmixOptions
        );
        const convertArrays = convertArray(selectedWorkmixOptions);
        console.log('convertArrays==', convertArrays);
        clientReportCardDetails.monthlyData.forEach((item, index) => {
          console.log('ppppp====', item, index);
          if (index < 13 && index != 0) {
            console.log('index===', index);
            // First 12 rows, add them directly
            filteredData.push(item);
          } else if (
            (index === 13 || index === 18 || index === 23) &&
            index != 0
          ) {
            // Check if kpi_name is in selectedWorkmixOptions
            if (convertArrays.includes(item.kpi_id)) {
              // Filter remaining data where kpi_category matches this kpi_name
              filteredData = filteredData.concat(
                clientReportCardDetails.monthlyData.filter(
                  data => data.kpi_id === item.kpi_id
                )
              );
            }
          }
        });
        console.log('filteredData==', filteredData.length);
        const lastRowNumber = filteredData.length + 6; // Start from row 8
        // Add rows to Excel worksheet
        filteredData.forEach((item, index) => {
          console.log('item=', item, index, lastRowNumber);
          // Modify each item by appending '$' to numerical values
          const modifiedItem = Object.fromEntries(
            Object.entries(item).map(([key, value]) =>
              key == 'kpi_name'
                ? [key, value]
                : [key, formatCellValue(value, item.symbol)]
            )
          );
          const row = worksheet.addRow(modifiedItem); // Add modified row
          const rowNumber = index + 7; // Start from row 8
          // Apply Light Green Background for columns D and G
          ['D'].forEach(col => {
            const cell = worksheet.getCell(`${col}${rowNumber}`);
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'e7f1fa' } // Light Green background
            };

            cell.font = {
              bold: true,
              color: {
                argb:
                  typeof cell.value === 'string' &&
                  cell.value.includes('(') &&
                  cell.value.includes(')') > 0
                    ? 'FF0000'
                    : cell.value === '0' ||
                      cell.value === 0 ||
                      cell.value === '0%'
                    ? '000000'
                    : '3c7d22'
              } // White text
            };
            cell.border = {
              right: { style: 'thick', color: { argb: 'FF000000' } }, // Black Right Border
              bottom: { style: 'thin', color: { argb: 'FF000000' } }, // Black Right Border
              top: { style: 'thin', color: { argb: 'FF000000' } },
              left: { style: 'thin', color: { argb: 'FF000000' } }
            };
            cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally
          });
          ['A'].forEach(col => {
            const cell = worksheet.getCell(`${col}${rowNumber}`);
            cell.border = {
              left: { style: 'thick', color: { argb: 'FF000000' } },
              bottom: { style: 'thin', color: { argb: 'FF000000' } },
              top: { style: 'thin', color: { argb: 'FF000000' } },
              right: { style: 'thin', color: { argb: 'FF000000' } }
            };
          });
          // Apply Orange Background & Bold White Text for rows 20, 32, 44 in columns B-G
          if ([19, 24, 29].includes(rowNumber)) {
            ['B', 'C', 'D'].forEach(col => {
              const cell = worksheet.getCell(`${col}${rowNumber}`);
              cell.value = ' '; // Set cell value as empty string
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'be5014' } // Orange background
              };
              cell.font = {
                bold: true,
                color: { argb: 'FFFFFF' } // White text
              };
              cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally
              cell.border = {
                right: col === 'D' && {
                  style: 'thick',
                  color: { argb: 'FF000000' }
                }, // Black Right Border
                bottom: { style: 'thin', color: { argb: 'FF000000' } },
                top: { style: 'thin', color: { argb: 'FF000000' } } //
              };
            });
            ['A'].forEach(col => {
              const cell = worksheet.getCell(`${col}${rowNumber}`);
              // cell.value = ' '; // Set cell value as empty string
              cell.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'be5014' } // Orange background
              };
              cell.font = {
                bold: true,
                color: { argb: 'FFFFFF' } // White text
              };
              cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally
              cell.border = {
                left: col === 'A' && {
                  style: 'thick',
                  color: { argb: 'FF000000' }
                },
                bottom: { style: 'thin', color: { argb: 'FF000000' } },
                top: { style: 'thin', color: { argb: 'FF000000' } }
              };
            });
          } else {
            ['A', 'B', 'C', 'D'].forEach(col => {
              const cell = worksheet.getCell(`${col}${rowNumber}`);
              cell.alignment = { vertical: 'middle', horizontal: 'right' }; // Center both vertically & horizontally
              if (col != 'D') {
                cell.border = {
                  bottom: { style: 'thin', color: { argb: 'FF000000' } },
                  top: { style: 'thin', color: { argb: 'FF000000' } },
                  left: {
                    style: col === 'A' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  },
                  right: {
                    style: 'thin',
                    color: { argb: 'FF000000' }
                  }
                };
              }

              if (index + 7 == lastRowNumber) {
                const cells = worksheet.getCell(`${col}${rowNumber}`);
                cells.border = {
                  bottom: { style: 'thick', color: { argb: 'FF000000' } }, // Black Right Border
                  left: {
                    style: col === 'A' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  },
                  right: {
                    style: col === 'D' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  }
                };
              }
              if (7 == rowNumber) {
                console.log('item===1===1==', index + 7 == lastRowNumber);
                const cellVal = worksheet.getCell(`${col}${rowNumber}`);
                cellVal.border = {
                  top: { style: 'thick', color: { argb: 'FF000000' } }, // Black Right Border
                  left: {
                    style: col === 'A' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  },
                  right: {
                    style: col === 'D' ? 'thick' : 'thin',
                    color: { argb: 'FF000000' }
                  }
                };
              }
            });
          }
        });
      }
    }

    // Export the Excel file
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    saveAs(
      blob,
      // type == 'ThreeMonth'
      //   ? 'Client Report Card - 3 Month - ' +
      //       localStorage.getItem('storeNames') +
      //       'Report.xlsx'
      //   : 'Client Report Card - 1 Month - ' +
      //       localStorage.getItem('storeNames') +
      //       'Report.xlsx'
      fileNameExport
    );
  };

  const exportToExcel = (
    clientReportCardDetails,
    selectedOptions,
    selectedWorkmixOptions,
    type,
    imageData,
    imageAnnual,
    fileNameExport
  ) => {
    getDocument(
      clientReportCardDetails,
      selectedOptions,
      selectedWorkmixOptions,
      type,
      imageData,
      imageAnnual,
      fileNameExport
    );
  };
  return {
    exportToExcel
  };
};
