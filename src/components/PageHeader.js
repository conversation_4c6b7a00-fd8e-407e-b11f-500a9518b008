import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import ButtonBase from '@material-ui/core/ButtonBase';
import LocationCityIcon from '@material-ui/icons/LocationCity';
import BuildIcon from '@material-ui/icons/Build';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import Compare from '@material-ui/icons/Compare';
import { useHistory } from 'react-router';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { checkClosedDateInCurrentMonth } from 'src/utils/Utils';
import {
  getLatestClosedDate,
  getLatestOpenDate
} from 'src/utils/hasuraServices';
import { IconButton, Tooltip, Button, Checkbox } from '@material-ui/core';
import moment from 'moment';
import clsx from 'clsx';
import 'src/styles.css';
import RestoreIcon from '@material-ui/icons/Restore';
import { traceSpan } from 'src/utils/OTTTracing';
import { useSelector, useDispatch } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { setToggleStatus } from 'src/actions';
// import { opacity } from 'html2canvas/dist/types/css/property-descriptors/opacity';
const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(1),
    //marginRight: 'auto',
    maxWidth: 500
  },
  image: {
    width: 100,
    height: 75,
    cursor: 'default'
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '50%'
  },
  container: {
    alignItems: 'center',
    '@media (max-width: 1920px)': {
      width: 250
      //fontSize: 25,
      //marginLeft: '23%'
    },

    '@media (max-width: 1440px)': {
      width: 220
      //fontSize: 25,
      //marginLeft: '23%'
    },

    '@media (max-width: 1280px)': {
      width: 200
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 350
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 10
  },
  titleContainer: {
    alignItems: 'center',

    display: 'flex',
    justifyContent: 'space-between',
    padding: 8
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex'
    //justifyContent: 'space-between'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  },
  sublLabel: {
    display: 'flex'
  },
  resetButton: {
    float: 'right'
  },
  titleLabel: {
    display: 'flex',
    fontSize: 15,
    cursor: 'default'
  },
  valLabel: {
    display: 'flex',
    cursor: 'default'
  },
  dataItem: {
    marginLeft: 20
  },
  flexItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginBottom: 5,
    flexBasis: '50% !important'
  },
  flexItemPartial: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    // marginBottom: 5,
    marginRight: '3%'
  },
  flexItemPartialDetails: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginRight: '3%'
  },
  linkItem: {
    cursor: 'pointer'
  },
  reportButtonSelect1: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  reportButton1: {
    height: 21,
    marginLeft: 4,
    marginRight: 7,
    //color: '#757575',
    border: '2px solid #7d97aa',
    borderColor: theme.palette.primary.main,
    color: '#003d6b',
    '@media (min-width: 2560px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 2304px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 1920px)': {
      fontSize: 12,
      width: 176
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 178
    }
  },
  reportButton: {
    height: 21,
    marginLeft: -3,
    marginRight: 4,
    height: 30,
    //color: '#757575',
    border: '2px solid #7d97aa',
    borderColor: theme.palette.primary.main,
    background: '#003d6b',
    color: '#fff',
    '@media (min-width: 2560px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 2304px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 1920px)': {
      fontSize: 12,
      width: 176
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 178
    },
    '&:hover': {
      background: '#003d6b',
      color: '#fff',
      cursor: 'pointer'
    }
  },
  reportButtonSelect: {
    height: 21,
    marginLeft: -3,
    marginRight: 4,
    border: '2px solid #7d97aa',
    height: 30,
    //color: '#757575',
    borderColor: theme.palette.primary.main,
    // background: 'rgb(221, 234, 244)',
    // color: 'rgb(0, 61, 107)',
    background: '#003d6b',
    color: '#fff',
    '@media (min-width: 2560px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 2304px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 1920px)': {
      fontSize: 12,
      width: 172
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 172
    },
    '&:hover': {
      background: '#003d6b',
      color: '#fff',
      cursor: 'pointer'
    }
  },
  reportButtonSelectJobCnt: {
    height: 21,
    marginLeft: 4,
    marginRight: 2,
    border: '0 !important',
    background: '#ee7600',
    color: '#fff',
    fontSize: 12,
    width: 106,
    '&:hover': {
      background: '#003d6b',
      color: '#fff',
      cursor: 'pointer'
    }
  },
  partialMth: {
    fontWeight: 500,
    color: '#003d6b'
  }
}));

const PageHeader = props => {
  // let showCurrentMonth = localStorage.getItem('showCurrentMonth')
  //   ? localStorage.getItem('showCurrentMonth')
  //   : false;

  // if (
  //   !checkClosedDateInCurrentMonth(
  //     localStorage.getItem('closedDate'),
  //     localStorage.getItem('openDate')
  //   )
  // ) {
  //   // showCurrentMonth = false;
  //   localStorage.setItem('showCurrentMonth', JSON.parse(false));
  // }
  const session = useSelector(state => state.session);
  const history = useHistory();
  // checkClosedDateInCurrentMonth(closedDate, openDate)
  const classes = useStyles();
  const [closedDate, setClosedDate] = useState('');
  const [openDate, setOpenDate] = useState('');
  const [partialChecked, setChecked] = useState(session.toggleStatus);
  const [exportReport, setExportReport] = useState(false);

  const dispatch = useDispatch();
  let showCurrentMonth = session.toggleStatus;
  useEffect(() => {
    if (session.toggleStatus == false) {
      let status = checkClosedDateInCurrentMonth(
        localStorage.getItem('closedDate'),
        localStorage.getItem('openDate')
      );

      // if (session.toggleStatus != undefined) {
      //   dispatch(setToggleStatus(session.toggleStatus));
      // } else {
      dispatch(setToggleStatus(status));
      // }
    }
  }, []);
  useEffect(() => {
    if (
      localStorage.getItem('closedDate') == null &&
      localStorage.getItem('closedDate') == ''
    ) {
      getLatestClosedDate(result => {
        if (result) {
          var openDate = '';
          var Date1 = result[0].value;

          setClosedDate(Date1);

          localStorage.setItem('closedDate', Date1);
        }
      });
      getLatestOpenDate(result => {
        if (result) {
          var resultDate = '';
          var openDate = '';
          var Date1 = result[0].value;
          localStorage.setItem('openDate', Date1);
          setOpenDate(Date1);
        }
      });
    } else {
      setClosedDate(localStorage.getItem('closedDate'));
      setOpenDate(localStorage.getItem('openDate'));
    }
  }, [session.serviceAdvisor]);

  useEffect(() => {
    setChecked(showCurrentMonth);
    if (props.showCurrentMonth) {
      props.showCurrentMonth(showCurrentMonth);
    }
    localStorage.setItem('showCurrentMonth', showCurrentMonth);
  }, [showCurrentMonth]);

  const handleChangeCurrentMonth = event => {
    // if(event.target.innerText == 'Show Partial Month') {

    if (partialChecked == false || partialChecked == 'false') {
      setChecked(true);
      props.showCurrentMonth(true);
      dispatch(setToggleStatus(true));
      localStorage.setItem('showCurrentMonth', JSON.parse(true));
    } else {
      setChecked(false);
      props.showCurrentMonth(false);
      localStorage.setItem('showCurrentMonth', JSON.parse(false));
      dispatch(setToggleStatus(false));
    }
    // setChecked(event.target.checked);
    // props.showCurrentMonth(event.target.checked);
    // localStorage.setItem('showCurrentMonth', JSON.parse(event.target.checked));
  };
  const handleJobCountReport = () => {
    history.push({
      pathname: '/TrendReport',
      isFrom: 'LaborOverview',
      state: {
        isFrom: 'LaborOverview'
      }
    });
  };
  const resetDashboard = () => {
    props.setResetDashboard(true);
  };
  const handleclick = () => {
    props.redirectHome();
  };
  const handleGridClick = () => {
    props.handleJobCountGrid();
  };
  const exportReportGrid = () => {
    props.exportReport();

    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title: props.title ? props.title : '',
      from: props.isFrom ? props.isFrom : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to Excel', spanAttribute);
  };
  const exportKpiReport = () => {
    props.exportKpiReportGrid();

    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to PDF',
      title: props.title ? props.title : '',
      from: props.isFrom ? props.isFrom : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to PDF', spanAttribute);
  };
  const handleStoreComapare = () => {
    history.push({
      pathname: '/StoreComparison',
      SelectedLocation: window.location.pathname
    });
  };
  const location = useLocation();
  const shouldAddClass =
    location.pathname.includes('PartsMisses') ||
    location.pathname.includes('LaborMisses') ||
    location.pathname.includes('KpiGraphics') ||
    location.pathname.includes('KPIReportComparative') ||
    location.pathname.includes('KPIReportStoreComparative');

  return (
    <Grid
      container
      className={clsx(
        classes.titleContainer,
        'report-header',
        'reset-dashboard'
      )}
    >
      {props.isFrom != 'details' && (
        // <Grid item xs={6} style={{ display: 'flex', alignItems: 'center' }}>
        //   {(props.isFrom == 'drilldown' || props.isFrom == 'compare_store'   ) && (
        <Grid
          item
          xs={
            session.toggleStatus &&
            (props.title == 'CP Labor Overview' ||
              props.title == 'CP Parts Overview')
              ? 5
              : 6
          }
          style={{ display: 'flex', alignItems: 'center' }}
        >
          {(props.isFrom == 'drilldown' ||
            props.isFrom == 'compare_store' ||
            props.parent == 'Home' ||
            props.parent == 'Extended_View') && (
            <Button
              variant="contained"
              className={'bck-btn'}
              onClick={handleclick}
            >
              <Typography variant="body1" align="left">
                Back
              </Typography>
            </Button>
          )}
          <Typography
            variant="h4"
            color="primary"
            className={clsx(classes.mainLabel, 'main-title')}
          >
            {props.title}
          </Typography>
        </Grid>
      )}
      {props.isFrom == 'details' && props.hideTitle == false && (
        <Typography
          variant="h4"
          color="primary"
          className={clsx(classes.mainLabel, 'main-title')}
        >
          {props.title}
        </Typography>
      )}
      {props.isFrom == 'details' && typeof props.hideTitle == 'undefined' && (
        <Grid item xs={4}>
          <Button
            variant="contained"
            className={'bck-btn'}
            onClick={handleclick}
          >
            <Typography variant="body1" align="left">
              Back
            </Typography>
          </Button>
        </Grid>
      )}

      <Grid
        item
        xs={
          session.toggleStatus &&
          (props.title == 'CP Labor Overview' ||
            props.title == 'CP Parts Overview')
            ? 8
            : (session.toggleStatus &&
                (props.title == 'CP Labor GP %' ||
                  props.title ==
                    'CP Effective Labor Rate - Repair And Competitive' ||
                  props.title == 'CP Parts Gross Profit Percentage' ||
                  props.title == 'CP Parts Markup - Repair and Competitive')) ||
              props.title == 'Scatter Plot - Labor - Jobs / Hours / ELR'
            ? 6
            : 6
        }
        className={classes.flexItem}
      >
        {closedDate &&
        !props.changeLog &&
        (props.advisorName == undefined || props.advisorName == '') ? (
          <Typography
            variant="body1"
            color="secondary"
            align="right"
            className={clsx(
              classes.dataLabel,
              'date-asof',
              shouldAddClass && 'date-asofrouting'
            )}
          >
            {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
          </Typography>
        ) : (
          ''
        )}

        {//checkClosedDateInCurrentMonth(closedDate, openDate) &&
        props.title == 'CP Labor Overview' ||
        props.title == 'CP Parts Overview' ? (
          // <Grid item xs={5} className={classes.flexItemPartial}>
          //   <Tooltip
          //     title={
          //       JSON.parse(checked)
          //         ? 'Hide Partial Month Data'
          //         : 'Show Partial Month Data'
          //     }
          //     //  style={{ marginLeft: 200, float: 'right' }}
          //   >
          //     <FormGroup>
          //       <FormControlLabel
          //         className={classes.switch}
          //         control={
          //           <Switch
          //             checked={JSON.parse(checked)}
          //             onChange={handleChangeCurrentMonth}
          //             name="checkedA"
          //             inputProps={{ 'aria-label': 'secondary checkbox' }}
          //           />
          //         }
          //         label={
          //           <span style={{ fontWeight: 500 }}>
          //             {JSON.parse(checked)
          //               ? 'Hide Partial Month Data'
          //               : 'Show Partial Month Data'}
          //           </span>
          //         }
          //         value="start"
          //         labelPlacement="start"
          //       />
          //     </FormGroup>
          //   </Tooltip>
          // </Grid>
          /* <Button
              className={
                JSON.parse(checked)
                  ? classes.reportButtonSelect
                  : classes.reportButton
              }
              id="partialMonthToggle"
              variant="outlined"
              style={{textTransform: 'none'}}
              onChange={handleChangeCurrentMonth}
            >
              {JSON.parse(checked) ? 'Hide Partial Month' : 'Show Partial Month'}
            </Button> */

          <span
            style={{
              marginLeft: 3,
              marginRight: -10,
              opacity: partialChecked === 1 ? '0.5' : 'initial'
            }}
          >
            <FormControlLabel
              control={
                <Checkbox
                  disabled={partialChecked === 1 ? true : false}
                  checked={partialChecked == true ? true : false}
                  onClick={handleChangeCurrentMonth}
                  style={{
                    transform: 'scale(0.8)',
                    color: '#003d6b'
                  }}
                />
              }
              label={
                <Typography
                  variant="h6"
                  style={{
                    color: '#003d6b',
                    fontWeight: 'bold',
                    marginLeft: -8,
                    fontSize: 12
                  }}
                >
                  {partialChecked == true ? 'Partial Month' : 'Partial Month'}
                </Typography>
              }
            />
          </span>
        ) : (
          ''
        )}
        {//checkClosedDateInCurrentMonth(closedDate, openDate) &&
        (props.title == 'CP Labor GP %' ||
          props.title == 'CP Effective Labor Rate - Repair And Competitive' ||
          props.title == 'CP Parts Gross Profit Percentage' ||
          props.title == 'CP Parts Markup - Repair and Competitive' ||
          props.title == 'ELR - Repair' ||
          props.title == 'CP ELR - Maintenance and Competitive') &&
        props.isFrom != 'drilldown' ? (
          // <Grid item xs={6} className={classes.flexItemPartialDetails}>
          //   <Tooltip
          //     title={
          //       JSON.parse(checked)
          //         ? 'Hide Partial Month Data'
          //         : 'Show Partial Month Data'
          //     }
          //     //  style={{ marginLeft: 200, float: 'right' }}
          //   >
          //     <FormGroup>
          //       <FormControlLabel
          //         className={classes.switch}
          //         control={
          //           <Switch
          //             checked={JSON.parse(checked)}
          //             onChange={handleChangeCurrentMonth}
          //             name="checkedA"
          //             inputProps={{ 'aria-label': 'secondary checkbox' }}
          //           />
          //         }
          //         label={
          //           <span style={{ fontWeight: 500 }}>
          //             {JSON.parse(checked)
          //               ? 'Hide Partial Month Data'
          //               : 'Show Partial Month Data'}
          //           </span>
          //         }
          //         value="start"
          //         labelPlacement="start"
          //       />
          //     </FormGroup>
          //   </Tooltip>
          // </Grid>
          /* <Button
            className={
              JSON.parse(checked)
                ? classes.reportButtonSelect
                : classes.reportButton
            }
            id="partialMonthToggle"
            variant="outlined"
            style={{textTransform: 'none'}}
            onClick={handleChangeCurrentMonth}
          >
            {JSON.parse(checked) ? 'Hide Partial Month' : 'Show Partial Month'}
          </Button>*/
          <span
            style={{
              marginLeft: 3,
              marginRight: -10,
              opacity: partialChecked === 1 ? '0.5' : 'initial'
            }}
          >
            <FormControlLabel
              control={
                <Checkbox
                  disabled={partialChecked === 1 ? true : false}
                  checked={partialChecked == true ? true : false}
                  onClick={handleChangeCurrentMonth}
                  style={{
                    transform: 'scale(0.8)',
                    color: '#003d6b'
                  }}
                />
              }
              label={
                <Typography
                  variant="h6"
                  style={{
                    color: '#003d6b',
                    fontWeight: 'bold',
                    marginLeft: -8,
                    fontSize: 12
                  }}
                >
                  {/* checked
                      ? 'Hide Partial Month'
                      : 'Show Partial Month' */}
                  {partialChecked == true ? 'Partial Month' : 'Partial Month'}
                </Typography>
              }
            />
          </span>
        ) : (
          ''
        )}
        {props.advisorName && props.advisorName != '' ? (
          <div className={clsx(classes.dataAsOf)}>
            <Typography
              variant="h6"
              align="right"
              style={{ fontSize: 12, color: '#7987a1', fontWeight: 'bold' }}
            >
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <div
                  style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                  <div>{'Advisor' + '\xa0\xa0'}</div>
                  <div style={{ width: 16 }}>{':'}</div>
                  <div style={{ marginLeft: 2 }}>{props.advisorName}</div>
                </div>
              </div>
            </Typography>
            {closedDate && !props.changeLog ? (
              <div>
                <Typography
                  variant="h6"
                  align="right"
                  style={{ fontSize: 12, color: '#7987a1', fontWeight: 'bold' }}
                >
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between'
                      }}
                    >
                      <div>{'Data as of' + '\xa0' + ' :'}</div>
                      <div style={{ marginLeft: 2 }}>
                        {moment(closedDate).format('MM/DD/YY')}
                      </div>
                    </div>
                  </div>
                </Typography>
              </div>
            ) : null}
          </div>
        ) : null}
        {/* {closedDate && !props.changeLog && (props.advisorName == undefined || props.advisorName == '') ? (
          <Typography
            variant="body1"
            color="secondary"
            align="right"
            className={clsx(classes.dataLabel, 'date-asof')}
          >
            {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
          </Typography>
        ) : (
          ''
        )} */}
        {props.isFrom != 'details' &&
          props.title == 'Scatter Plot - Labor - Jobs / Hours / ELR' && (
            <Button
              variant="contained"
              className={'job-count-grid-btn'}
              style={{ marginRight: 8 }}
              onClick={handleGridClick}
            >
              <Typography variant="body1" align="left">
                Job Count Grid
              </Typography>
            </Button>
          )}
        {props.title == 'CP Labor Overview' ? (
          // <Tooltip
          //   title={'View Trend Report'}
          //   //  style={{ marginLeft: 200, float: 'right' }}
          // >
          /* <Button
              className={classes.reportButtonSelectJobCnt
              }
              variant="outlined"
              style={{textTransform: 'none'}}
              onClick={handleJobCountReport}
            >
              {'Trend Report'}
            </Button> */

          <Button
            variant="contained"
            className={'job-count-grid-btn'}
            style={{ marginRight: 6 }}
            onClick={handleJobCountReport}
          >
            <Typography variant="body1" align="left">
              Trend Report
            </Typography>
          </Button>
        ) : // </Tooltip>
        null}
        {props.isFrom == 'drilldown' && (
          <Tooltip title="Export To Excel">
            <Link
              id="export-to-excel"
              className={classes.linkItem}
              onClick={exportReportGrid}
            >
              <ExportIcon />
            </Link>
          </Tooltip>
        )}
        {props.isFrom == 'details' && props.exportPDF == true && (
          <Tooltip title="Export To PDF">
            <Link
              id="export-to-pdf"
              className={classes.linkItem}
              onClick={exportKpiReport}
            >
              <ExportIcon />
            </Link>
          </Tooltip>
        )}
        {props.isFrom != 'details' && (
          <Button
            variant="contained"
            id="reset-layout"
            className={'reset-btn'}
            onClick={resetDashboard}
          >
            <RestoreIcon style={{ height: '0.8em', weight: '0.8em' }} />
            <Typography variant="body1" align="left">
              Reset Layout
            </Typography>
          </Button>
        )}
        {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
        window.location.pathname == '/CPOverview' ? (
          <Tooltip
            onClick={handleStoreComapare}
            title="Compare Stores"
            style={{ marginBottom: 5 }}
          >
            <IconButton size="small">
              <Compare color="primary" />
            </IconButton>
          </Tooltip>
        ) : (
          ''
        )}

        {/* <Toolti
        p onClick={props.setResetDashboard} title="Reset Layout">
          <IconButton
            size="small"
            className={clsx(
              classes.resetButton,
              'reset-layout-icon',
              'date-asof'
            )}
          >
            <RestoreIcon />
            <Typography
              variant="body1"
              color="secondary"
              align="left"
              className={clsx(classes.dataLabel, 'date-asof')}
            >
              Reset
            </Typography>
          </IconButton>
        </Tooltip> */}
      </Grid>
    </Grid>
  );
};
export default PageHeader;
