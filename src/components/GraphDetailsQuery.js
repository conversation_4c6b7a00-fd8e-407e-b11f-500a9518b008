import { getDetailedGraphDateRange, getQueryFilter, getStoreIdFilter } from 'src/components/ViewGraphDetailsAction';

export function getQueryForDetails(chartId, filters, realm) {
  let storeId = JSON.parse(localStorage.getItem('selectedStoreId'));

  let query = '';
  switch (chartId) {
    case 1068: {
      query = {
        measures: ['CPRevenue.combined'],
        timeDimensions: [
          {
            dimension: 'CPRevenue.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPRevenue.closeddate'],
        filters: getQueryFilter(filters, 'CPRevenue', storeId),
        order: {
          'CPRevenue.closeddate': 'desc'
        }
      };
      return query;
    }
    case 941: {
      query = {
        measures: ['CPRevenue.labor_revenue'],
        timeDimensions: [
          {
            dimension: 'CPRevenue.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPRevenue.closeddate'],
        filters: getQueryFilter(filters, 'CPRevenue', storeId),
        order: {
          'CPRevenue.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1067: {
      query = {
        measures: ['CPRevenue.parts_revenue'],
        timeDimensions: [
          {
            dimension: 'CPRevenue.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPRevenue.closeddate'],
        filters: getQueryFilter(filters, 'CPRevenue', storeId),
        order: {
          'CPRevenue.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1069: {
      query = {
        measures: ['CPGrossProfit.combinedgrossprofit'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfit.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfit.ro_date'],
        filters: getQueryFilter(filters, 'CPGrossProfit', storeId),
        order: {
          'CPGrossProfit.ro_date': 'desc'
        }
      };

      return query;
    }
    case 1070: {
      query = {
        measures: ['CPGrossProfit.laborgrossprofit'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfit.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfit.ro_date'],
        filters: getQueryFilter(filters, 'CPGrossProfit', storeId),
        order: {
          'CPGrossProfit.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1071: {
      query = {
        measures: ['CPGrossProfit.prt_gross_profit'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfit.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfit.ro_date'],
        filters: getQueryFilter(filters, 'CPGrossProfit', storeId),
        order: {
          'CPGrossProfit.ro_date': 'desc'
        }
      };
      return query;
    }
    case 968: {
      query = {
        measures: ['CPLaborSoldHoursCombined.lbr_soldhours_combined'],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursCombined.ro_date'],
        filters: getQueryFilter(filters, 'CPLaborSoldHoursCombined', storeId),
        order: {
          'CPLaborSoldHoursCombined.ro_date': 'desc'
        }
      };
      return query;
    }

    case 969: {
      query = {
        measures: ['CPLaborSoldHoursCombined.lbr_soldhours_warranty'],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursCombined.ro_date'],
        filters: getQueryFilter(filters, 'CPLaborSoldHoursCombined', storeId),
        order: {
          'CPLaborSoldHoursCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 970: {
      query = {
        measures: ['CPLaborSoldHoursCombined.lbr_soldhours_internal'],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursCombined.ro_date'],
        filters: getQueryFilter(filters, 'CPLaborSoldHoursCombined', storeId),
        order: {
          'CPLaborSoldHoursCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 971: {
      query = {
        measures: ['CPLaborSoldHoursCombined.lbr_soldhours_customerpay'],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursCombined.ro_date'],
        filters: getQueryFilter(filters, 'CPLaborSoldHoursCombined', storeId),
        order: {
          'CPLaborSoldHoursCombined.ro_date': 'desc'
        }
      };
      return query;
    }

    case 1134: {
      query = {
        measures: ['LaborSoldHoursCombinedDetail.lbr_soldhours_combined'],
        timeDimensions: [
          {
            dimension: 'LaborSoldHoursCombinedDetail.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborSoldHoursCombinedDetail.ro_date'],
        filters: getQueryFilter(filters, 'LaborSoldHoursCombinedDetail', storeId),
        order: {
          'LaborSoldHoursCombinedDetail.ro_date': 'desc'
        }
      };
      return query;
    }

    case 1136: {
      query = {
        measures: ['LaborSoldHoursCombinedDetail.lbr_soldhours_warranty'],
        timeDimensions: [
          {
            dimension: 'LaborSoldHoursCombinedDetail.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborSoldHoursCombinedDetail.ro_date'],
        filters: getQueryFilter(filters, 'LaborSoldHoursCombinedDetail', storeId),
        order: {
          'LaborSoldHoursCombinedDetail.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1137: {
      query = {
        measures: ['LaborSoldHoursCombinedDetail.lbr_soldhours_internal'],
        timeDimensions: [
          {
            dimension: 'LaborSoldHoursCombinedDetail.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborSoldHoursCombinedDetail.ro_date'],
        filters: getQueryFilter(filters, 'LaborSoldHoursCombinedDetail', storeId),
        order: {
          'LaborSoldHoursCombinedDetail.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1135: {
      query = {
        measures: ['LaborSoldHoursCombinedDetail.lbr_soldhours_customerpay'],
        timeDimensions: [
          {
            dimension: 'LaborSoldHoursCombinedDetail.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborSoldHoursCombinedDetail.ro_date'],
        filters: getQueryFilter(filters, 'LaborSoldHoursCombinedDetail', storeId),
        order: {
          'LaborSoldHoursCombinedDetail.ro_date': 'desc'
        }
      };
      return query;
    }

    case 937: {
      query = {
        measures: ['CPELRate.elr_combined'],
        timeDimensions: [
          {
            dimension: 'CPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRate.rodate'],
        filters: getQueryFilter(filters, 'CPELRate', storeId),
        order: {
          'CPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 1066: {
      query = {
        measures: ['CPELRate.elr_repair_competitive'],
        timeDimensions: [
          {
            dimension: 'CPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRate.rodate'],
        filters: getQueryFilter(filters, 'CPELRate', storeId),
        order: {
          'CPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 987: {
      query = {
        measures: ['CPELRate.elr_maintenance'],
        timeDimensions: [
          {
            dimension: 'CPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRate.rodate'],
        filters: getQueryFilter(filters, 'CPELRate', storeId),
        order: {
          'CPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 988: {
      query = {
        measures: ['CPELRate.elr_repair'],
        timeDimensions: [
          {
            dimension: 'CPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRate.rodate'],
        filters: getQueryFilter(filters, 'CPELRate', storeId),
        order: {
          'CPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 986: {
      query = {
        measures: ['CPELRate.elr_competitive'],
        timeDimensions: [
          {
            dimension: 'CPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRate.rodate'],
        filters: getQueryFilter(filters, 'CPELRate', storeId),
        order: {
          'CPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 1128: {
      query = {
        measures: ['LaborCPELRate.elrcombined'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRate.rodate'],
        // filters: filters.includes('All')
        //   ? []
        //   : [
        //       {
        //         member: 'LaborCPELRate.serviceadvisor',
        //         operator: 'equals',
        //         values: filters
        //       }
        //     ],
        order: {
          'LaborCPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 1132: {
      query = {
        measures: ['LaborCPELRate.competitiverepair'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRate.rodate'],
        // filters: filters.includes('All')
        //   ? []
        //   : [
        //       {
        //         member: 'LaborCPELRate.serviceadvisor',
        //         operator: 'equals',
        //         values: filters
        //       }
        //     ],
        order: {
          'LaborCPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 1130: {
      query = {
        measures: ['LaborCPELRate.maintenance'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRate.rodate'],
        // filters: filters.includes('All')
        //   ? []
        //   : [
        //       {
        //         member: 'LaborCPELRate.serviceadvisor',
        //         operator: 'equals',
        //         values: filters
        //       }
        //     ],
        order: {
          'LaborCPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 1129: {
      query = {
        measures: ['LaborCPELRate.repair'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRate.rodate'],
        // filters: filters.includes('All')
        //   ? []
        //   : [
        //       {
        //         member: 'LaborCPELRate.serviceadvisor',
        //         operator: 'equals',
        //         values: filters
        //       }
        //     ],
        order: {
          'LaborCPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 1131: {
      query = {
        measures: ['LaborCPELRate.competitive'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRate.rodate'],
        // filters: filters.includes('All')
        //   ? []
        //   : [
        //       {
        //         member: 'LaborCPELRate.serviceadvisor',
        //         operator: 'equals',
        //         values: filters
        //       }
        //     ],
        order: {
          'LaborCPELRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 1074: {
      query = {
        measures: ['ROCountCombined.combinedrocount'],
        timeDimensions: [
          {
            dimension: 'ROCountCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountCombined.ro_date'],
        filters: getQueryFilter(filters, 'ROCountCombined', storeId),
        order: {
          'ROCountCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1033: {
      query = {
        measures: ['ROCountCombined.customerpayrocount'],
        timeDimensions: [
          {
            dimension: 'ROCountCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountCombined.ro_date'],
        filters: getQueryFilter(filters, 'ROCountCombined', storeId),
        order: {
          'ROCountCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1034: {
      query = {
        measures: ['ROCountCombined.warrantyrocount'],
        timeDimensions: [
          {
            dimension: 'ROCountCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountCombined.ro_date'],
        filters: getQueryFilter(filters, 'ROCountCombined', storeId),
        order: {
          'ROCountCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1035: {
      query = {
        measures: ['ROCountCombined.internalrocount'],
        timeDimensions: [
          {
            dimension: 'ROCountCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountCombined.ro_date'],
        filters: getQueryFilter(filters, 'ROCountCombined', storeId),
        order: {
          'ROCountCombined.ro_date': 'desc'
        }
      };
      return query;
    }

    case 1139: {
      query = {
        measures: ['LaborROCountCombinedDetails.combinedrocount'],
        timeDimensions: [
          {
            dimension: 'LaborROCountCombinedDetails.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCountCombinedDetails.ro_date'],
        filters: getQueryFilter(filters, 'LaborROCountCombinedDetails', storeId),
        order: {
          'LaborROCountCombinedDetails.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1140: {
      query = {
        measures: ['LaborROCountCombinedDetails.customerpayrocount'],
        timeDimensions: [
          {
            dimension: 'LaborROCountCombinedDetails.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCountCombinedDetails.ro_date'],
        filters: getQueryFilter(filters, 'LaborROCountCombinedDetails', storeId),
        order: {
          'LaborROCountCombinedDetails.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1141: {
      query = {
        measures: ['LaborROCountCombinedDetails.warrantyrocount'],
        timeDimensions: [
          {
            dimension: 'LaborROCountCombinedDetails.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCountCombinedDetails.ro_date'],
        filters: getQueryFilter(filters, 'LaborROCountCombinedDetails', storeId),
        order: {
          'LaborROCountCombinedDetails.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1142: {
      query = {
        measures: ['LaborROCountCombinedDetails.internalrocount'],
        timeDimensions: [
          {
            dimension: 'LaborROCountCombinedDetails.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCountCombinedDetails.ro_date'],
        filters: getQueryFilter(filters, 'LaborROCountCombinedDetails', storeId),
        order: {
          'LaborROCountCombinedDetails.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1072: {
      query = {
        measures: ['CPGrossProfitPercentage.combinedprofitpercent'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfitPercentage.ro_date'],
        filters: getQueryFilter(filters, 'CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1097: {
      query = {
        measures: ['CPGrossProfitPercentage.partsprofitpercent'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfitPercentage.ro_date'],
        filters: getQueryFilter(filters, 'CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'desc'
        }
      };
      return query;
    }
    case 967: {
      query = {
        measures: ['CPGrossProfitPercentage.labourprofitpercent'],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPGrossProfitPercentage.ro_date'],
        filters: getQueryFilter(filters, 'CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1075: {
      query = {
        measures: ['LaborRevenueAllCategories.revenue'],
        timeDimensions: [
          {
            dimension: 'LaborRevenueAllCategories.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenueAllCategories.rodate'],
        filters: getQueryFilter(filters, 'LaborRevenueAllCategories', storeId),
        order: {
          'LaborRevenueAllCategories.rodate': 'desc'
        }
      };
      return query;
    }
    case 1020: {
      query = {
        measures: ['LaborRevenueAllCategories.lbrsalesrepair'],
        timeDimensions: [
          {
            dimension: 'LaborRevenueAllCategories.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenueAllCategories.rodate'],
        filters: getQueryFilter(filters, 'LaborRevenueAllCategories', storeId),
        order: {
          'LaborRevenueAllCategories.rodate': 'desc'
        }
      };
      return query;
    }
    case 1019: {
      query = {
        measures: ['LaborRevenueAllCategories.lbrsalesmaintenance'],
        timeDimensions: [
          {
            dimension: 'LaborRevenueAllCategories.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenueAllCategories.rodate'],
        filters: getQueryFilter(filters, 'LaborRevenueAllCategories', storeId),
        order: {
          'LaborRevenueAllCategories.rodate': 'desc'
        }
      };
      return query;
    }
    case 1018: {
      query = {
        measures: ['LaborRevenueAllCategories.lbrsalescompetitive'],
        timeDimensions: [
          {
            dimension: 'LaborRevenueAllCategories.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenueAllCategories.rodate'],
        filters: getQueryFilter(filters, 'LaborRevenueAllCategories', storeId),
        order: {
          'LaborRevenueAllCategories.rodate': 'desc'
        }
      };
      return query;
    }
    case 1022: {
      query = {
        measures: ['LaborRevenueAllCategories.lbrsalesshopsupplies'],
        timeDimensions: [
          {
            dimension: 'LaborRevenueAllCategories.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenueAllCategories.rodate'],
        filters: getQueryFilter(filters, 'LaborRevenueAllCategories', storeId),
        order: {
          'LaborRevenueAllCategories.rodate': 'desc'
        }
      };
      return query;
    }
    case 1007: {
      query = {
        measures: ['LaborProfitCombined.lbr_profit'],
        timeDimensions: [
          {
            dimension: 'LaborProfitCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitCombined.ro_date'],
        filters: getQueryFilter(filters, 'LaborProfitCombined', storeId),
        order: {
          'LaborProfitCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1010: {
      query = {
        measures: ['LaborProfitCombined.lbr_profit_customerpay'],
        timeDimensions: [
          {
            dimension: 'LaborProfitCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitCombined.ro_date'],
        filters: getQueryFilter(filters, 'LaborProfitCombined', storeId),
        order: {
          'LaborProfitCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1008: {
      query = {
        measures: ['LaborProfitCombined.lbr_profit_warranty'],
        timeDimensions: [
          {
            dimension: 'LaborProfitCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitCombined.ro_date'],
        filters: getQueryFilter(filters, 'LaborProfitCombined', storeId),
        order: {
          'LaborProfitCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1009: {
      query = {
        measures: ['LaborProfitCombined.lbr_profit_internal'],
        timeDimensions: [
          {
            dimension: 'LaborProfitCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitCombined.ro_date'],
        filters: getQueryFilter(filters, 'LaborProfitCombined', storeId),
        order: {
          'LaborProfitCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1073: {
      query = {
        measures: ['LaborProfitGrossPercentage.laborgrossprofitpercentage'],
        timeDimensions: [
          {
            dimension: 'LaborProfitGrossPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitGrossPercentage.ro_date'],
        filters: getStoreIdFilter('LaborProfitGrossPercentage', storeId),
        order: {
          'LaborProfitGrossPercentage.ro_date': 'desc'
        }
      };
      return query;
    }
    case 949: {
      query = {
        measures: ['LaborHoursPerRO.hours_per_repair_order'],
        timeDimensions: [
          {
            dimension: 'LaborHoursPerRO.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborHoursPerRO.rodate'],
        filters: getQueryFilter(filters, 'LaborHoursPerRO', storeId),
        order: {
          'LaborHoursPerRO.rodate': 'desc'
        }
      };
      return query;
    }
    case 1076: {
      query = {
        measures: ['LaborHoursPerRO.hours_per_repair_order_repair'],
        timeDimensions: [
          {
            dimension: 'LaborHoursPerRO.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborHoursPerRO.rodate'],
        filters: getQueryFilter(filters, 'LaborHoursPerRO', storeId),
        order: {
          'LaborHoursPerRO.rodate': 'desc'
        }
      };
      return query;
    }
    case 1077: {
      query = {
        measures: ['LaborHoursPerRO.hours_per_repair_order_maintenance'],
        timeDimensions: [
          {
            dimension: 'LaborHoursPerRO.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborHoursPerRO.rodate'],
        filters: getQueryFilter(filters, 'LaborHoursPerRO', storeId),
        order: {
          'LaborHoursPerRO.rodate': 'desc'
        }
      };
      return query;
    }
    case 1078: {
      query = {
        measures: ['LaborHoursPerRO.hours_per_repair_order_compet'],
        timeDimensions: [
          {
            dimension: 'LaborHoursPerRO.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborHoursPerRO.rodate'],
        filters: getQueryFilter(filters, 'LaborHoursPerRO', storeId),
        order: {
          'LaborHoursPerRO.rodate': 'desc'
        }
      };
      return query;
    }
    case 1079: {
      query = {
        measures: [
          'LaborROCategory.hours_per_repair_order_compet',
          'LaborROCategory.hours_per_repair_order_repair',
          'LaborROCategory.hours_per_repair_order_maintenance'
        ],
        timeDimensions: [
          {
            dimension: 'LaborROCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCategory.rodate'],
        filters: getStoreIdFilter('LaborROCategory', storeId),
        order: {
          'LaborROCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 753: {
      query = {
        measures: ['LaborJobCountAllCategoriesDetails.rocount'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountAllCategoriesDetails.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountAllCategoriesDetails.rodate'],
        filters: getQueryFilter(filters, 'LaborJobCountAllCategoriesDetails', storeId),
        order: {
          'LaborJobCountAllCategoriesDetails.rodate': 'desc'
        }
      };
      return query;
    }
    case 1080: {
      query = {
        measures: ['LaborJobCountAllCategoriesDetails.ro_countrepair'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountAllCategoriesDetails.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountAllCategoriesDetails.rodate'],
        filters: getQueryFilter(filters, 'LaborJobCountAllCategoriesDetails', storeId),
        order: {
          'LaborJobCountAllCategoriesDetails.rodate': 'desc'
        }
      };
      return query;
    }
    case 1081: {
      query = {
        measures: ['LaborJobCountAllCategoriesDetails.ro_countmaintenance'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountAllCategoriesDetails.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountAllCategoriesDetails.rodate'],
        filters: getQueryFilter(filters, 'LaborJobCountAllCategoriesDetails', storeId),
        order: {
          'LaborJobCountAllCategoriesDetails.rodate': 'desc'
        }
      };
      return query;
    }
    case 1082: {
      query = {
        measures: ['LaborJobCountAllCategoriesDetails.ro_countcompetitive'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountAllCategoriesDetails.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountAllCategoriesDetails.rodate'],
        filters: getQueryFilter(filters, 'LaborJobCountAllCategoriesDetails', storeId),
        order: {
          'LaborJobCountAllCategoriesDetails.rodate': 'desc'
        }
      };
      return query;
    }
    case 1083: {
      query = {
        measures: [
          'JobCountPercentageCategory.ro_countcompetitive',
          'JobCountPercentageCategory.ro_countrepair',
          'JobCountPercentageCategory.ro_countmaintenance',
          'JobCountPercentageCategory.ro_countshopsupplies'
        ],
        timeDimensions: [
          {
            dimension: 'JobCountPercentageCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['JobCountPercentageCategory.rodate'],
        filters: getStoreIdFilter('JobCountPercentageCategory', storeId),
        order: {
          'JobCountPercentageCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 998: {
      query = {
        measures: ['LaborJobCountAllCategoriesDetails.ro_countshopsupplies'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountAllCategoriesDetails.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountAllCategoriesDetails.rodate'],
        filters: getQueryFilter(filters, 'LaborJobCountAllCategoriesDetails', storeId),
        order: {
          'LaborJobCountAllCategoriesDetails.rodate': 'desc'
        }
      };
      return query;
    }
    case 1002: {
      query = {
        measures: ['CPPartsByCategory.partsrevenuecustomerpay'],
        timeDimensions: [
          {
            dimension: 'CPPartsByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsByCategory', storeId),
        order: {
          'CPPartsByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 456: {
      query = {
        measures: ['CPPartsByCategory.partsrevenueinternal'],
        timeDimensions: [
          {
            dimension: 'CPPartsByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsByCategory', storeId),
        order: {
          'CPPartsByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1001: {
      query = {
        measures: ['CPPartsByCategory.partsrevenuecombined'],
        timeDimensions: [
          {
            dimension: 'CPPartsByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsByCategory', storeId),
        order: {
          'CPPartsByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 457: {
      query = {
        measures: ['CPPartsByCategory.partsrevenuewarranty'],
        timeDimensions: [
          {
            dimension: 'CPPartsByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsByCategory', storeId),
        order: {
          'CPPartsByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1031: {
      query = {
        measures: ['CPPartsGPByCategory.prt_gp_customerpay'],
        timeDimensions: [
          {
            dimension: 'CPPartsGPByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsGPByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsGPByCategory', storeId),
        order: {
          'CPPartsGPByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1028: {
      query = {
        measures: ['CPPartsGPByCategory.prt_gp_combine'],
        timeDimensions: [
          {
            dimension: 'CPPartsGPByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsGPByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsGPByCategory', storeId),
        order: {
          'CPPartsGPByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1030: {
      query = {
        measures: ['CPPartsGPByCategory.prt_gp_internal'],
        timeDimensions: [
          {
            dimension: 'CPPartsGPByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsGPByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsGPByCategory', storeId),
        order: {
          'CPPartsGPByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1029: {
      query = {
        measures: ['CPPartsGPByCategory.prt_gp_warranty'],
        timeDimensions: [
          {
            dimension: 'CPPartsGPByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsGPByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsGPByCategory', storeId),
        order: {
          'CPPartsGPByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1015: {
      query = {
        measures: ['CPPartsPerROByCategory.parts_revenue_per_ro_repair'],
        timeDimensions: [
          {
            dimension: 'CPPartsPerROByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsPerROByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsPerROByCategory', storeId),
        order: {
          'CPPartsPerROByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1014: {
      query = {
        measures: ['CPPartsPerROByCategory.parts_revenue_per_ro_maintenance'],
        timeDimensions: [
          {
            dimension: 'CPPartsPerROByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsPerROByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsPerROByCategory', storeId),
        order: {
          'CPPartsPerROByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1013: {
      query = {
        measures: ['CPPartsPerROByCategory.parts_revenue_per_ro_competitive'],
        timeDimensions: [
          {
            dimension: 'CPPartsPerROByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsPerROByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsPerROByCategory', storeId),
        order: {
          'CPPartsPerROByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1012: {
      query = {
        measures: ['CPPartsPerROByCategory.parts_revenue_per_ro'],
        timeDimensions: [
          {
            dimension: 'CPPartsPerROByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsPerROByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsPerROByCategory', storeId),
        order: {
          'CPPartsPerROByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1084: {
      query = {
        measures: ['AverageLaborSalePerRo.averageratebycategory'],
        timeDimensions: [
          {
            dimension: 'AverageLaborSalePerRo.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AverageLaborSalePerRo.rodate'],

        order: {
          'AverageLaborSalePerRo.rodate': 'desc'
        }
      };
      return query;
    }
    case 1085: {
      query = {
        measures: ['AverageLaborSalePerRo.repair'],
        timeDimensions: [
          {
            dimension: 'AverageLaborSalePerRo.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AverageLaborSalePerRo.rodate'],

        order: {
          'AverageLaborSalePerRo.rodate': 'desc'
        }
      };
      return query;
    }
    case 1086: {
      query = {
        measures: ['AverageLaborSalePerRo.maintenance'],
        timeDimensions: [
          {
            dimension: 'AverageLaborSalePerRo.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AverageLaborSalePerRo.rodate'],

        order: {
          'AverageLaborSalePerRo.rodate': 'desc'
        }
      };
      return query;
    }
    case 1087: {
      query = {
        measures: ['AverageLaborSalePerRo.competitive'],
        timeDimensions: [
          {
            dimension: 'AverageLaborSalePerRo.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AverageLaborSalePerRo.rodate'],

        order: {
          'AverageLaborSalePerRo.rodate': 'desc'
        }
      };
      return query;
    }
    case 1088: {
      query = {
        measures: ['AverageLaborSalePerRo.shopsupplies'],
        timeDimensions: [
          {
            dimension: 'AverageLaborSalePerRo.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AverageLaborSalePerRo.rodate'],

        order: {
          'AverageLaborSalePerRo.rodate': 'desc'
        }
      };
      return query;
    }
    case 1091: {
      query = {
        measures: ['CPPartsMarkupByCategory.parts_markup_all'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByCategory.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByCategory.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByCategory', storeId),
        order: {
          'CPPartsMarkupByCategory.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1092: {
      query = {
        measures: ['CPPartsMarkupByCategory.parts_markup_repair'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByCategory.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByCategory.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByCategory', storeId),
        order: {
          'CPPartsMarkupByCategory.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1093: {
      query = {
        measures: ['CPPartsMarkupByCategory.parts_markup_maintenance'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByCategory.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByCategory.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByCategory', storeId),
        order: {
          'CPPartsMarkupByCategory.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1094: {
      query = {
        measures: ['CPPartsMarkupByCategory.parts_markup_competitive'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByCategory.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByCategory.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByCategory', storeId),
        order: {
          'CPPartsMarkupByCategory.closeddate': 'desc'
        }
      };
      return query;
    }

    case 1144: {
      query = {
        measures: ['CPPartsROCountByCategory.rocount'],
        timeDimensions: [
          {
            dimension: 'CPPartsROCountByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsROCountByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsROCountByCategory', storeId),
        order: {
          'CPPartsROCountByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1145: {
      query = {
        measures: ['CPPartsROCountByCategory.rocountcustomerpay'],
        timeDimensions: [
          {
            dimension: 'CPPartsROCountByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsROCountByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsROCountByCategory', storeId),
        order: {
          'CPPartsROCountByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1146: {
      query = {
        measures: ['CPPartsROCountByCategory.rocountwarranty'],
        timeDimensions: [
          {
            dimension: 'CPPartsROCountByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsROCountByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsROCountByCategory', storeId),
        order: {
          'CPPartsROCountByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1147: {
      query = {
        measures: ['CPPartsROCountByCategory.rocountinternal'],
        timeDimensions: [
          {
            dimension: 'CPPartsROCountByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsROCountByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsROCountByCategory', storeId),
        order: {
          'CPPartsROCountByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1148: {
      query = {
        measures: ['CPLaborSoldHoursCombined.lbrsldhrsmaintenanceplan'],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursCombined.ro_date'],
        filters: getQueryFilter(filters, 'CPLaborSoldHoursCombined', storeId),
        order: {
          'CPLaborSoldHoursCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1149: {
      query = {
        measures: ['CPLaborSoldHoursCombined.lbrsldhrsextendedservice'],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursCombined.ro_date'],
        filters: getQueryFilter(filters, 'CPLaborSoldHoursCombined', storeId),
        order: {
          'CPLaborSoldHoursCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1150: {
      query = {
        measures: ['ROCountCombined.maintenancerocount'],
        timeDimensions: [
          {
            dimension: 'ROCountCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountCombined.ro_date'],
        filters: getQueryFilter(filters, 'ROCountCombined', storeId),
        order: {
          'ROCountCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1151: {
      query = {
        measures: ['ROCountCombined.extendedservicerocount'],
        timeDimensions: [
          {
            dimension: 'ROCountCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountCombined.ro_date'],
        filters: getQueryFilter(filters, 'ROCountCombined', storeId),
        order: {
          'ROCountCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1152: {
      query = {
        measures: ['LaborProfitCombined.lbr_profit_maintenance'],
        timeDimensions: [
          {
            dimension: 'LaborProfitCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitCombined.ro_date'],
        filters: getQueryFilter(filters, 'LaborProfitCombined', storeId),
        order: {
          'LaborProfitCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1153: {
      query = {
        measures: ['LaborProfitCombined.lbr_profit_extended_service'],
        timeDimensions: [
          {
            dimension: 'LaborProfitCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitCombined.ro_date'],
        filters: getQueryFilter(filters, 'LaborProfitCombined', storeId),
        order: {
          'LaborProfitCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1154: {
      query = {
        measures: ['LaborSoldHoursCombinedDetail.lbr_soldhours_maintenance'],
        timeDimensions: [
          {
            dimension: 'LaborSoldHoursCombinedDetail.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborSoldHoursCombinedDetail.ro_date'],
        filters: getQueryFilter(filters, 'LaborSoldHoursCombinedDetail', storeId),
        order: {
          'LaborSoldHoursCombinedDetail.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1155: {
      query = {
        measures: ['LaborSoldHoursCombinedDetail.lbr_soldhours_extended'],
        timeDimensions: [
          {
            dimension: 'LaborSoldHoursCombinedDetail.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborSoldHoursCombinedDetail.ro_date'],
        filters: getQueryFilter(filters, 'LaborSoldHoursCombinedDetail', storeId),
        order: {
          'LaborSoldHoursCombinedDetail.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1156: {
      query = {
        measures: ['LaborROCountCombinedDetails.maintenancerocount'],
        timeDimensions: [
          {
            dimension: 'LaborROCountCombinedDetails.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCountCombinedDetails.ro_date'],
        filters: getQueryFilter(filters, 'LaborROCountCombinedDetails', storeId),
        order: {
          'LaborROCountCombinedDetails.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1157: {
      query = {
        measures: ['LaborROCountCombinedDetails.extendedservicerocount'],
        timeDimensions: [
          {
            dimension: 'LaborROCountCombinedDetails.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCountCombinedDetails.ro_date'],
        filters: getQueryFilter(filters, 'LaborROCountCombinedDetails', storeId),
        order: {
          'LaborROCountCombinedDetails.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1158: {
      query = {
        measures: ['CPPartsByCategory.partsrevenuemaintenance'],
        timeDimensions: [
          {
            dimension: 'CPPartsByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsByCategory', storeId),
        order: {
          'CPPartsByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1159: {
      query = {
        measures: ['CPPartsByCategory.partsrevenueextendedservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsByCategory', storeId),
        order: {
          'CPPartsByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1160: {
      query = {
        measures: ['CPPartsGPByCategory.prt_gp_maintenace'],
        timeDimensions: [
          {
            dimension: 'CPPartsGPByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsGPByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsGPByCategory', storeId),
        order: {
          'CPPartsGPByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1161: {
      query = {
        measures: ['CPPartsGPByCategory.prt_gp_externalservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsGPByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsGPByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsGPByCategory', storeId),
        order: {
          'CPPartsGPByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1162: {
      query = {
        measures: ['CPPartsROCountByCategory.rocountmaintenance'],
        timeDimensions: [
          {
            dimension: 'CPPartsROCountByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsROCountByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsROCountByCategory', storeId),
        order: {
          'CPPartsROCountByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1163: {
      query = {
        measures: ['CPPartsROCountByCategory.rocountextendedservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsROCountByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsROCountByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsROCountByCategory', storeId),
        order: {
          'CPPartsROCountByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1166: {
      query = {
        measures: ['CPLaborSoldHoursCombined.lbrsldhrsfactoryserviceplan'],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursCombined.ro_date'],
        filters: getQueryFilter(filters, 'CPLaborSoldHoursCombined', storeId),
        order: {
          'CPLaborSoldHoursCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1167: {
      query = {
        measures: ['ROCountCombined.factoryserviceplanrocount'],
        timeDimensions: [
          {
            dimension: 'ROCountCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountCombined.ro_date'],
        filters: getQueryFilter(filters, 'ROCountCombined', storeId),
        order: {
          'ROCountCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1168: {
      query = {
        measures: ['LaborProfitCombined.lbr_profit_factoryservice'],
        timeDimensions: [
          {
            dimension: 'LaborProfitCombined.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborProfitCombined.ro_date'],
        filters: getQueryFilter(filters, 'LaborProfitCombined', storeId),
        order: {
          'LaborProfitCombined.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1169: {
      query = {
        measures: ['LaborSoldHoursCombinedDetail.lbrsldhrsfactoryserviceplan'],
        timeDimensions: [
          {
            dimension: 'LaborSoldHoursCombinedDetail.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborSoldHoursCombinedDetail.ro_date'],
        filters: getQueryFilter(filters, 'LaborSoldHoursCombinedDetail', storeId),
        order: {
          'LaborSoldHoursCombinedDetail.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1173: {
      query = {
        measures: ['LaborROCountCombinedDetails.rocountfactoryservice'],
        timeDimensions: [
          {
            dimension: 'LaborROCountCombinedDetails.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborROCountCombinedDetails.ro_date'],
        filters: getQueryFilter(filters, 'LaborROCountCombinedDetails', storeId),
        order: {
          'LaborROCountCombinedDetails.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1172: {
      query = {
        measures: ['CPPartsByCategory.partsrevenuefactoryservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsByCategory', storeId),
        order: {
          'CPPartsByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1171: {
      query = {
        measures: ['CPPartsGPByCategory.partsgpfactoryservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsGPByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsGPByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsGPByCategory', storeId),
        order: {
          'CPPartsGPByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 1170: {
      query = {
        measures: ['CPPartsROCountByCategory.rocountfactoryservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsROCountByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsROCountByCategory.rodate'],
        filters: getQueryFilter(filters, 'CPPartsROCountByCategory', storeId),
        order: {
          'CPPartsROCountByCategory.rodate': 'desc'
        }
      };
      return query;
    }


    case 1176: {
      query = {
        measures: ['CPELRPaytypes.elr_combined'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1177: {
      query = {
        measures: ['CPELRPaytypes.elr_customerpay'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1178: {
      query = {
        measures: ['CPELRPaytypes.elr_warranty'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1179: {
      query = {
        measures: ['CPELRPaytypes.elr_internal'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1180: {
      query = {
        measures: ['CPELRPaytypes.elr_maintenanceplan'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1181: {
      query = {
        measures: ['CPELRPaytypes.elr_extendedservice'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1182: {
      query = {
        measures: ['CPELRPaytypes.elr_factoryservice'],
        timeDimensions: [
          {
            dimension: 'CPELRPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPELRPaytypes.ro_date'],
        filters: getStoreIdFilter('CPELRPaytypes', storeId),
        order: {
          'CPELRPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1183: {
      query = {
        measures: ['LaborCPELRPaytype.elr_combined'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRPaytype.ro_date'],
        filters: getStoreIdFilter('LaborCPELRPaytype', storeId),
        order: {
          'LaborCPELRPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1184: {
      query = {
        measures: ['LaborCPELRPaytype.elr_customerpay'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRPaytype.ro_date'],
        filters: getStoreIdFilter('LaborCPELRPaytype', storeId),
        order: {
          'LaborCPELRPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1185: {
      query = {
        measures: ['LaborCPELRPaytype.elr_warranty'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRPaytype.ro_date'],
        filters: getStoreIdFilter('LaborCPELRPaytype', storeId),
        order: {
          'LaborCPELRPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1186: {
      query = {
        measures: ['LaborCPELRPaytype.elr_internal'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRPaytype.ro_date'],
        filters: getStoreIdFilter('LaborCPELRPaytype', storeId),
        order: {
          'LaborCPELRPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1187: {
      query = {
        measures: ['LaborCPELRPaytype.elr_maintenanceplan'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRPaytype.ro_date'],
        filters: getStoreIdFilter('LaborCPELRPaytype', storeId),
        order: {
          'LaborCPELRPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1188: {
      query = {
        measures: ['LaborCPELRPaytype.elr_extendedservice'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRPaytype.ro_date'],
        filters: getStoreIdFilter('LaborCPELRPaytype', storeId),
        order: {
          'LaborCPELRPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1189: {
      query = {
        measures: ['LaborCPELRPaytype.elr_factoryservice'],
        timeDimensions: [
          {
            dimension: 'LaborCPELRPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborCPELRPaytype.ro_date'],
        filters: getStoreIdFilter('LaborCPELRPaytype', storeId),
        order: {
          'LaborCPELRPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1190: {
      query = {
        measures: ['LaborRevenuePaytype.lbrsalescombined'],
        timeDimensions: [
          {
            dimension: 'LaborRevenuePaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenuePaytype.ro_date'],
        filters: getQueryFilter(filters, 'LaborRevenuePaytype', storeId),
        order: {
          'LaborRevenuePaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1191: {
      query = {
        measures: ['LaborRevenuePaytype.lbrsalescustomerpay'],
        timeDimensions: [
          {
            dimension: 'LaborRevenuePaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenuePaytype.ro_date'],
        filters: getQueryFilter(filters, 'LaborRevenuePaytype', storeId),
        order: {
          'LaborRevenuePaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1192: {
      query = {
        measures: ['LaborRevenuePaytype.lbrsaleswarranty'],
        timeDimensions: [
          {
            dimension: 'LaborRevenuePaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenuePaytype.ro_date'],
        filters: getQueryFilter(filters, 'LaborRevenuePaytype', storeId),
        order: {
          'LaborRevenuePaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1193: {
      query = {
        measures: ['LaborRevenuePaytype.lbrsalesinternal'],
        timeDimensions: [
          {
            dimension: 'LaborRevenuePaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenuePaytype.ro_date'],
        filters: getQueryFilter(filters, 'LaborRevenuePaytype', storeId),
        order: {
          'LaborRevenuePaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1194: {
      query = {
        measures: ['LaborRevenuePaytype.lbrsalesmaintenanceplan'],
        timeDimensions: [
          {
            dimension: 'LaborRevenuePaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenuePaytype.ro_date'],
        filters: getQueryFilter(filters, 'LaborRevenuePaytype', storeId),
        order: {
          'LaborRevenuePaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1195: {
      query = {
        measures: ['LaborRevenuePaytype.lbrsalesextendedservice'],
        timeDimensions: [
          {
            dimension: 'LaborRevenuePaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenuePaytype.ro_date'],
        filters: getQueryFilter(filters, 'LaborRevenuePaytype', storeId),
        order: {
          'LaborRevenuePaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1196: {
      query = {
        measures: ['LaborRevenuePaytype.lbrsalesfactoryservice'],
        timeDimensions: [
          {
            dimension: 'LaborRevenuePaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborRevenuePaytype.ro_date'],
        filters: getQueryFilter(filters, 'LaborRevenuePaytype', storeId),
        order: {
          'LaborRevenuePaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1197: {
      query = {
        measures: ['LbrHrsROPaytype.hours_per_repair_order'],
        timeDimensions: [
          {
            dimension: 'LbrHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LbrHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('LbrHrsROPaytype', storeId),
        order: {
          'LbrHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1198: {
      query = {
        measures: ['LbrHrsROPaytype.hours_per_repair_order_customerpay'],
        timeDimensions: [
          {
            dimension: 'LbrHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LbrHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('LbrHrsROPaytype', storeId),
        order: {
          'LbrHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1199: {
      query = {
        measures: ['LbrHrsROPaytype.hours_per_repair_order_warranty'],
        timeDimensions: [
          {
            dimension: 'LbrHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LbrHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('LbrHrsROPaytype', storeId),
        order: {
          'LbrHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1200: {
      query = {
        measures: ['LbrHrsROPaytype.hours_per_repair_order_internal'],
        timeDimensions: [
          {
            dimension: 'LbrHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LbrHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('LbrHrsROPaytype', storeId),
        order: {
          'LbrHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1201: {
      query = {
        measures: ['LbrHrsROPaytype.hours_per_repair_order_maintenance'],
        timeDimensions: [
          {
            dimension: 'LbrHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LbrHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('LbrHrsROPaytype', storeId),
        order: {
          'LbrHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1202: {
      query = {
        measures: ['LbrHrsROPaytype.hours_per_repair_order_extendedservice'],
        timeDimensions: [
          {
            dimension: 'LbrHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LbrHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('LbrHrsROPaytype', storeId),
        order: {
          'LbrHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1203: {
      query = {
        measures: ['LbrHrsROPaytype.hours_per_repair_order_factoryservice'],
        timeDimensions: [
          {
            dimension: 'LbrHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LbrHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('LbrHrsROPaytype', storeId),
        order: {
          'LbrHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 

    case 1211: {
      query = {
        measures: ['AvgSalePerRoPaytype.averageratebycategory'],
        timeDimensions: [
          {
            dimension: 'AvgSalePerRoPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AvgSalePerRoPaytype.ro_date'],
        filters: getStoreIdFilter('AvgSalePerRoPaytype', storeId),
        order: {
          'AvgSalePerRoPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1212: {
      query = {
        measures: ['AvgSalePerRoPaytype.customerpay'],
        timeDimensions: [
          {
            dimension: 'AvgSalePerRoPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AvgSalePerRoPaytype.ro_date'],
        filters: getStoreIdFilter('AvgSalePerRoPaytype', storeId),
        order: {
          'AvgSalePerRoPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1213: {
      query = {
        measures: ['AvgSalePerRoPaytype.warranty'],
        timeDimensions: [
          {
            dimension: 'AvgSalePerRoPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AvgSalePerRoPaytype.ro_date'],
        filters: getStoreIdFilter('AvgSalePerRoPaytype', storeId),
        order: {
          'AvgSalePerRoPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1214: {
      query = {
        measures: ['AvgSalePerRoPaytype.internal'],
        timeDimensions: [
          {
            dimension: 'AvgSalePerRoPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AvgSalePerRoPaytype.ro_date'],
        filters: getStoreIdFilter('AvgSalePerRoPaytype', storeId),
        order: {
          'AvgSalePerRoPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1215: {
      query = {
        measures: ['AvgSalePerRoPaytype.maintenance'],
        timeDimensions: [
          {
            dimension: 'AvgSalePerRoPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AvgSalePerRoPaytype.ro_date'],
        filters: getStoreIdFilter('AvgSalePerRoPaytype', storeId),
        order: {
          'AvgSalePerRoPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1216: {
      query = {
        measures: ['AvgSalePerRoPaytype.extendedservice'],
        timeDimensions: [
          {
            dimension: 'AvgSalePerRoPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AvgSalePerRoPaytype.ro_date'],
        filters: getStoreIdFilter('AvgSalePerRoPaytype', storeId),
        order: {
          'AvgSalePerRoPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1217: {
      query = {
        measures: ['AvgSalePerRoPaytype.factoryservice'],
        timeDimensions: [
          {
            dimension: 'AvgSalePerRoPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AvgSalePerRoPaytype.ro_date'],
        filters: getStoreIdFilter('AvgSalePerRoPaytype', storeId),
        order: {
          'AvgSalePerRoPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 

    case 1218: {
      query = {
        measures: ['PrtsPerROPaytype.parts_revenue_per_ro_combined'],
        timeDimensions: [
          {
            dimension: 'PrtsPerROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PrtsPerROPaytype.ro_date'],
        filters: getStoreIdFilter('PrtsPerROPaytype', storeId),
        order: {
          'PrtsPerROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1219: {
      query = {
        measures: ['PrtsPerROPaytype.parts_revenue_per_ro_customerpay'],
        timeDimensions: [
          {
            dimension: 'PrtsPerROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PrtsPerROPaytype.ro_date'],
        filters: getStoreIdFilter('PrtsPerROPaytype', storeId),
        order: {
          'PrtsPerROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1220: {
      query = {
        measures: ['PrtsPerROPaytype.parts_revenue_per_ro_warranty'],
        timeDimensions: [
          {
            dimension: 'PrtsPerROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PrtsPerROPaytype.ro_date'],
        filters: getStoreIdFilter('PrtsPerROPaytype', storeId),
        order: {
          'PrtsPerROPaytype.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1221: {
      query = {
        measures: ['PrtsPerROPaytype.parts_revenue_per_ro_internal'],
        timeDimensions: [
          {
            dimension: 'PrtsPerROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PrtsPerROPaytype.ro_date'],
        filters: getStoreIdFilter('PrtsPerROPaytype', storeId),
        order: {
          'PrtsPerROPaytype.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1222: {
      query = {
        measures: ['PrtsPerROPaytype.parts_revenue_per_ro_maintenance'],
        timeDimensions: [
          {
            dimension: 'PrtsPerROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PrtsPerROPaytype.ro_date'],
        filters: getStoreIdFilter('PrtsPerROPaytype', storeId),
        order: {
          'PrtsPerROPaytype.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1223: {
      query = {
        measures: ['PrtsPerROPaytype.parts_revenue_per_ro_extendedwarranty'],
        timeDimensions: [
          {
            dimension: 'PrtsPerROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PrtsPerROPaytype.ro_date'],
        filters: getStoreIdFilter('PrtsPerROPaytype', storeId),
        order: {
          'PrtsPerROPaytype.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1224: {
      query = {
        measures: ['PrtsPerROPaytype.parts_revenue_per_ro_factoryservice'],
        timeDimensions: [
          {
            dimension: 'PrtsPerROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PrtsPerROPaytype.ro_date'],
        filters: getStoreIdFilter('PrtsPerROPaytype', storeId),
        order: {
          'PrtsPerROPaytype.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1225: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_all'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1226: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_customerpay'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'desc'
        }
      };
      return query;
    }

    case 1227: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_warranty'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1228: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_internal'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1229: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_maintenance'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1230: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_extendedservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1231: {
      query = {
        measures: ['CPPartsMarkupByPaytype.parts_markup_factoryservice'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupByPaytype.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsMarkupByPaytype.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkupByPaytype', storeId),
        order: {
          'CPPartsMarkupByPaytype.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1204: {
      query = {
        measures: ['LaborJobCountPaytypes.jobcountcountcombined'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountPaytypes.ro_date'],
        filters: getQueryFilter(filters, 'LaborJobCountPaytypes', storeId),
        order: {
          'LaborJobCountPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1205: {
      query = {
        measures: ['LaborJobCountPaytypes.jobcountcustomerpay'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountPaytypes.ro_date'],
        filters: getQueryFilter(filters, 'LaborJobCountPaytypes', storeId),
        order: {
          'LaborJobCountPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1206: {
      query = {
        measures: ['LaborJobCountPaytypes.jobcountwarranty'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountPaytypes.ro_date'],
        filters: getQueryFilter(filters, 'LaborJobCountPaytypes', storeId),
        order: {
          'LaborJobCountPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1207: {
      query = {
        measures: ['LaborJobCountPaytypes.jobcountinternal'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountPaytypes.ro_date'],
        filters: getQueryFilter(filters, 'LaborJobCountPaytypes', storeId),
        order: {
          'LaborJobCountPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1208: {
      query = {
        measures: ['LaborJobCountPaytypes.jobcountmaintenance'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountPaytypes.ro_date'],
        filters: getQueryFilter(filters, 'LaborJobCountPaytypes', storeId),
        order: {
          'LaborJobCountPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1209: {
      query = {
        measures: ['LaborJobCountPaytypes.jobcountextendedservice'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountPaytypes.ro_date'],
        filters: getQueryFilter(filters, 'LaborJobCountPaytypes', storeId),
        order: {
          'LaborJobCountPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1210: {
      query = {
        measures: ['LaborJobCountPaytypes.jobcountfactoryservice'],
        timeDimensions: [
          {
            dimension: 'LaborJobCountPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborJobCountPaytypes.ro_date'],
        filters: getQueryFilter(filters, 'LaborJobCountPaytypes', storeId),
        order: {
          'LaborJobCountPaytypes.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1240: {
      query = {
        measures: ['shopSupplies.totalshopsupply'],
        timeDimensions: [
          {
            dimension: 'shopSupplies.rodate',
          }
        ],
        dimensions: ['shopSupplies.rodate'],
        filters: getQueryFilter(filters, 'shopSupplies', storeId, realm),
        order: {
          'shopSupplies.rodate': 'desc'
        }
      };
      return query;
    }
    case 1241: {
      query = {
        measures: ['shopSupplies.customerpayshopsup'],
        timeDimensions: [
          {
            dimension: 'shopSupplies.rodate',
          }
        ],
        dimensions: ['shopSupplies.rodate'],
        filters: getQueryFilter(filters, 'shopSupplies', storeId, realm),
        order: {
          'shopSupplies.rodate': 'desc'
        }
      };
      return query;
    }
    case 1242: {
      query = {
        measures: ['shopSupplies.internalshopsup'],
        timeDimensions: [
          {
            dimension: 'shopSupplies.rodate',
          }
        ],
        dimensions: ['shopSupplies.rodate'],
        filters: getQueryFilter(filters, 'shopSupplies', storeId, realm),
        order: {
          'shopSupplies.rodate': 'desc'
        }
      };
      return query;
    }

    case 1319: {
      query = {
        measures: ['PartsHrsROPaytype.hours_per_repair_order'],
        timeDimensions: [
          {
            dimension: 'PartsHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PartsHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('PartsHrsROPaytype', storeId),
        order: {
          'PartsHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1320: {
      query = {
        measures: ['PartsHrsROPaytype.hours_per_repair_order_customerpay'],
        timeDimensions: [
          {
            dimension: 'PartsHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PartsHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('PartsHrsROPaytype', storeId),
        order: {
          'PartsHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1321: {
      query = {
        measures: ['PartsHrsROPaytype.hours_per_repair_order_warranty'],
        timeDimensions: [
          {
            dimension: 'PartsHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PartsHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('PartsHrsROPaytype', storeId),
        order: {
          'PartsHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1322: {
      query = {
        measures: ['PartsHrsROPaytype.hours_per_repair_order_internal'],
        timeDimensions: [
          {
            dimension: 'PartsHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PartsHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('PartsHrsROPaytype', storeId),
        order: {
          'PartsHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1323: {
      query = {
        measures: ['PartsHrsROPaytype.hours_per_repair_order_maintenance'],
        timeDimensions: [
          {
            dimension: 'PartsHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PartsHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('PartsHrsROPaytype', storeId),
        order: {
          'PartsHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1324: {
      query = {
        measures: ['PartsHrsROPaytype.hours_per_repair_order_extendedservice'],
        timeDimensions: [
          {
            dimension: 'PartsHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PartsHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('PartsHrsROPaytype', storeId),
        order: {
          'PartsHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1325: {
      query = {
        measures: ['PartsHrsROPaytype.hours_per_repair_order_factoryservice'],
        timeDimensions: [
          {
            dimension: 'PartsHrsROPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['PartsHrsROPaytype.ro_date'],
        filters: getStoreIdFilter('PartsHrsROPaytype', storeId),
        order: {
          'PartsHrsROPaytype.ro_date': 'desc'
        }
      };
      return query;
    } 
    case 1327: {
      query = {
        measures: ['ROCountPartsOnlyPaytype.rocount'],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnlyPaytype.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountPartsOnlyPaytype.rodate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnlyPaytype', storeId),
        order: {
          'ROCountPartsOnlyPaytype.rodate': 'desc'
        }
      };
      return query;
    }
    case 1328: {
      query = {
        measures: ['ROCountPartsOnlyPaytype.rocountcustomerpay'],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnlyPaytype.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountPartsOnlyPaytype.rodate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnlyPaytype', storeId),
        order: {
          'ROCountPartsOnlyPaytype.rodate': 'desc'
        }
      };
      return query;
    }
    case 1329: {
      query = {
        measures: ['ROCountPartsOnlyPaytype.rocountwarranty'],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnlyPaytype.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountPartsOnlyPaytype.rodate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnlyPaytype', storeId),
        order: {
          'ROCountPartsOnlyPaytype.rodate': 'desc'
        }
      };
      return query;
    }
    case 1330: {
      query = {
        measures: ['ROCountPartsOnlyPaytype.rocountinternal'],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnlyPaytype.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountPartsOnlyPaytype.rodate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnlyPaytype', storeId),
        order: {
          'ROCountPartsOnlyPaytype.rodate': 'desc'
        }
      };
      return query;
    }
    case 1331: {
      query = {
        measures: ['ROCountPartsOnlyPaytype.rocountmaintenance'],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnlyPaytype.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountPartsOnlyPaytype.rodate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnlyPaytype', storeId),
        order: {
          'ROCountPartsOnlyPaytype.rodate': 'desc'
        }
      };
      return query;
    }
    case 1332: {
      query = {
        measures: ['ROCountPartsOnlyPaytype.rocountextendedservice'],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnlyPaytype.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountPartsOnlyPaytype.rodate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnlyPaytype', storeId),
        order: {
          'ROCountPartsOnlyPaytype.rodate': 'desc'
        }
      };
      return query;
    }
    case 1333: {
      query = {
        measures: ['ROCountPartsOnlyPaytype.rocountfactoryservice'],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnlyPaytype.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountPartsOnlyPaytype.rodate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnlyPaytype', storeId),
        order: {
          'ROCountPartsOnlyPaytype.rodate': 'desc'
        }
      };
      return query;
    }
  }
}
