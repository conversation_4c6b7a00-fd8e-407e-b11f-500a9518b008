import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import FormGroup from '@material-ui/core/FormGroup';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField
} from '@material-ui/core';
import Checkbox from '@material-ui/core/Checkbox';
import $, { param } from 'jquery';
import { useHistory } from 'react-router';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles, withStyles } from '@material-ui/styles';
import PropTypes, { element } from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import MuiTableCell from '@material-ui/core/TableCell';
import { getEmail, sendSavedReportEmails } from 'src/utils/hasuraServices';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import FormHelperText from '@material-ui/core/FormHelperText';
import Chip from '@material-ui/core/Chip';
import moment from 'moment';
import { withKeycloak } from '@react-keycloak/web';
import _ from 'lodash';
import TextareaAutosize from '@material-ui/core/TextareaAutosize';

require('dotenv').config();

var lodash = require('lodash');
const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);
const useStyles = makeStyles(theme => ({
  root: {},
  // backDrop: {
  //   backdropFilter: 'blur(3px)',
  //   backgroundColor: 'rgba(0, 0, 0, 0)',
  //   margin: 20
  // },
  // scrollPaper: { backgroundColor: '#fff' },
  menuItem: {
    padding: 0
  },
  formControl: {
    minWidth: 110
  },
  formControl1: {
    minWidth: 160
  },
  cardControl: {
    padding: 0
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),
    fontSize: 12
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  paper: {
    '@media (max-width: 1920px)': {
      maxWidth: '1150px',
      maxHeight: '700px'
    },

    '@media (max-width: 1440px)': {
      maxWidth: '1500px',
      maxHeight: '610px'
    },

    '@media (max-width: 1280px)': {
      maxWidth: '1500px',
      maxHeight: '600px'
    },
    '@media (max-width: 2304px)': {
      maxWidth: '1350px',
      maxHeight: '900px'
    }
  },
  paperTranches: {
    maxWidth: '1250px',
    maxHeight: '800px'
  },
  paperItemization: {
    maxWidth: '100%',
    maxHeight: '1400px'
  },
  flexItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  paperBar: {
    // maxWidth: '1300px',
    maxWidth: '100%',
    maxHeight: '1400px'
    // marginLeft: '17%',
    // marginTop: '8%',
    // marginRight: '0%'
  },
  divDisplay: {
    //marginBottom: 45
  },
  divDisplayAdv: {
    //marginTop: 47
  },
  parentCell: {
    display: 'flex',
    justifyContent: 'space-around'
  },
  emailCc: {
    display: 'block'
  },
  emailBcc: {
    display: 'block'
  },
  emailCcDisable: {
    display: 'none'
  },
  emailBccDisable: {
    display: 'none'
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function EmailDialogkpi({
  keycloak,
  open,
  handlePopupClose,
  mailUsers,
  selAdvisors,
  selStores,
  filterStart,
  filterEnd,
  hideGoals,
  visibleTotals,
  expandedKPIs,
  hiddenKpis,
  sortOrder,
  reportName,
  storeNickName,
  kpiSortedArray,
  clientReportCardDetails,
  image,
  selectedWorkmixOptions,
  iKpiReportType,
  measuredMTH,
  priorMTH,
  selectedOptions,
  selectedStoreIds,
  selectStoreDetails,
  selectedStoreName,
  headerData,
  rowDataPdf,
  rowDataTotal,
  oneLineType,
  // Technicians,
  // serviceAdvisors
  selectedServiceAdvisors,
  selectedTechnicians,
  roValue,
  mileageValue,
  filterModelValue,
  field,
  draggedColumn,
  sortModel
}) {
  const classes = useStyles();
  const history = useHistory();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(open);
  const [openAlert, setOpenAlert] = React.useState(false);
  const [openAlertErr, setOpenAlertErr] = React.useState(false);
  const [emailvalue, setEmailvalue] = useState('');
  const [error, setError] = useState('');
  const [errorChecked, setErrorChecked] = useState('');
  const [requiredText, setRequiredText] = useState(false);
  const [filterDisabled, setfilterDisabled] = useState(true);
  const [value, setValue] = useState([]);
  const [valueCc, setValueCc] = useState([]);
  const [valueBcc, setValueBcc] = useState([]);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertMsgErr, setAlertMsgErr] = useState('');

  const [emailvalueCc, setEmailvalueCc] = useState('');
  const [emailvalueBcc, setEmailvalueBcc] = useState('');
  const [emailCcClicked, setEmailCcClick] = useState(false);
  const [emailBccClicked, setEmailBccClick] = useState(false);
  const [description, setDescription] = useState('');

  const [alertMesg, setAlertMesg] = useState('');
  const [alertType, setAlertType] = useState('');
  const [openAlertt, setOpenAlertt] = React.useState(false);
  const firstName = keycloak.tokenParsed?.given_name || '';
  const lastName = keycloak.tokenParsed?.family_name || '';
  const fullName = `${firstName} ${lastName}`.trim();
  useEffect(() => {
    //console.log('sssdata====', selAdvisors, selStores);
    setOpenDialog(open);
    setfilterDisabled(false);

    setErrorChecked('');
  }, [open]);

  const resetStates = () => {
    setEmailvalue('');
    setEmailvalueCc('');
    setEmailvalueBcc('');
    setError('');
    setRequiredText(false);
    setErrorChecked('');
    setfilterDisabled(true);
    setError('');
    setValue([]);
    setValueCc([]);
    setValueBcc([]);
    setEmailCcClick(false);
    setEmailBccClick(false);
  };
  const handleOk = () => {
    setOpenDialog(false);
    handlePopupClose();
    resetStates();
  };

  const handleDescription = e => {
    setDescription(e.target.value);
  };

  const handleSaveEmail = () => {
    let noEmail = 0;
    const pathaname = history.location.pathname.split('/')[1];
    console.log('handleSaveEmail', emailvalue, emailvalueBcc, emailvalueCc);
    if (
      (emailvalue.length == 0 && (emailvalue == '' || emailvalue == [])) ||
      (emailvalue.length == 1 && emailvalue[0] == '')
    ) {
      setError('Please Enter Email Recipient');
      let mesg = 'Please Enter Email Recipient';
      setOpenAlertt(true);
      setAlertType('warning');
      setAlertMesg(mesg);
      setTimeout(() => {
        setOpenAlertt(false);
      }, 2000);
      noEmail = 1;
    } else {
      setError('');
    }
    var isValid = emailvalue && validateEmail(emailvalue);

    if (!isValid) {
      noEmail = 1;
    }
    if (error) {
      noEmail = 1;
      setError(error);
      setOpenAlertt(true);
      setAlertType('warning');
      setAlertMesg(error);
      setTimeout(() => {
        setOpenAlertt(false);
      }, 2000);
    }

    if (noEmail == 0) {
      if (
        iKpiReportType === 'Parts_Target_Misses' ||
        iKpiReportType === 'Labor_Target_Misses' ||
        iKpiReportType === 'MPI_Stats' ||
        iKpiReportType === 'CP_1-Line-RO_Count_Over_60k' ||
        iKpiReportType === 'CP_1-Line-RO_Count_Under_60k'
      ) {
        var postData = {
          fromdate: moment(filterStart).format('MM/DD/YY'),
          todate: moment(filterEnd).format('MM/DD/YY'),
          recipients: emailvalue.length > 0 ? emailvalue.join(',') : emailvalue,
          ccrecipients:
            emailvalueCc.length > 0 ? emailvalueCc.join(',') : emailvalueCc,
          bccrecipients:
            emailvalueBcc.length > 0 ? emailvalueBcc.join(',') : emailvalueBcc,
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          realm: localStorage.getItem('realm'),

          emailContent: description,
          reportName: reportName,
          storeName: selectedStoreName,
          measuredMTH: measuredMTH,
          priorMTH: priorMTH,
          selectedWorkmixOptions: selectedWorkmixOptions,
          selectedOptions: selectedOptions,
          selectedStoreIds: selectedStoreIds,
          iKpiReportType: iKpiReportType,
          storeGroup: localStorage.getItem('storeGroup'),
          headerData: headerData,
          rowDataPdf: rowDataPdf,
          rowDataTotal: rowDataTotal,
          oneLineType: oneLineType,
          advisor: selectedServiceAdvisors,
          technician: selectedTechnicians,
          roValue: roValue,
          mileageValue: mileageValue,
          filterModelValue: filterModelValue,
          field: field,
          userId: fullName,
          draggedColumn: draggedColumn,
          sortModel: sortModel
        };
        fetch(process.env.REACT_APP_EMAIL_SERVICE_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(postData)
        })
          .then(response => {
            console.log('ressss', response);
            if (response.ok) {
              setOpenAlert(true);
              setAlertMsg('Email sent successfully');
              setDescription('');
              insertEmailToEmailList();
              setTimeout(() => {
                setOpenAlert(false);
                setOpenDialog(false);
                handlePopupClose();
                resetStates();
              }, 1000);
              return response;
            } else {
              setOpenAlertErr(true);
              setAlertMsgErr('Something went wrong');
              setTimeout(() => {
                setOpenAlertErr(false);
                setOpenDialog(false);
                handlePopupClose();
                resetStates();
              }, 1000);
            }

            throw new Error('Something went wrong');
          })
          .then(responseJson => {
            console.log(responseJson.status); // Will show you the status
          })

          .catch(error => console.error('Error posting data:', error));
      } else if (
        iKpiReportType === 'Client_Report_Card_1_Month' ||
        iKpiReportType === 'Client_Report_Card_3_Month_Total'
      ) {
        var postData = {
          fromdate: moment(filterStart).format('MM/DD/YY'),
          todate: moment(filterEnd).format('MM/DD/YY'),
          recipients: emailvalue.length > 0 ? emailvalue.join(',') : emailvalue,
          ccrecipients:
            emailvalueCc.length > 0 ? emailvalueCc.join(',') : emailvalueCc,
          bccrecipients:
            emailvalueBcc.length > 0 ? emailvalueBcc.join(',') : emailvalueBcc,
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          realm: localStorage.getItem('realm'),

          emailContent: description,
          reportName: reportName,
          storeName: selectedStoreName,
          measuredMTH: measuredMTH,
          priorMTH: priorMTH,
          selectedWorkmixOptions: selectedWorkmixOptions,
          selectedOptions: selectedOptions,
          selectedStoreIds: selectedStoreIds,
          iKpiReportType: iKpiReportType,
          storeGroup: localStorage.getItem('storeGroup'),
          userId: fullName
        };
        console.log('postDataaaaaaaa', postData);
        fetch(process.env.REACT_APP_EMAIL_SERVICE_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(postData)
        })
          .then(response => {
            console.log('ressss', response);
            if (response.ok) {
              setOpenAlert(true);
              setAlertMsg('Email sent successfully');
              setDescription('');
              insertEmailToEmailList();
              setTimeout(() => {
                setOpenAlert(false);
                setOpenDialog(false);
                handlePopupClose();
                resetStates();
              }, 1000);
              return response;
            } else {
              setOpenAlertErr(true);
              setAlertMsgErr('Something went wrong');
              setTimeout(() => {
                setOpenAlertErr(false);
                setOpenDialog(false);
                handlePopupClose();
                resetStates();
              }, 1000);
            }

            throw new Error('Something went wrong');
          })
          .then(responseJson => {
            console.log(responseJson.status); // Will show you the status
          })

          .catch(error => console.error('Error posting data:', error));
      } else {
        const allowedValues = ['Total Selected', 'Total Shop'];
        const containsOtherValues = selAdvisors.some(
          value => !allowedValues.includes(value)
        );
        var iKpiIdsAdv;

        if (containsOtherValues) {
          iKpiIdsAdv = selAdvisors;
        } else {
          iKpiIdsAdv = selAdvisors.filter(
            item => !visibleTotals.includes(item)
          );
        }
        const allowedValuesStore = ['Group Average', 'Group Roll-Up'];
        const containsOtherValuesStore = selStores.some(
          value => !allowedValuesStore.includes(value)
        );
        var iKpiIdsStore;

        if (containsOtherValuesStore) {
          iKpiIdsStore = selStores;
        } else {
          iKpiIdsStore = selStores.filter(
            item => !visibleTotals.includes(item)
          );
        }
        var postData = {
          kpireporttype:
            pathaname === 'KPIReportComparative'
              ? 'advisor'
              : pathaname === 'KPIReportTechComparative'
              ? 'tech'
              : 'store',

          advisors: iKpiIdsAdv,
          allStores: JSON.parse(
            localStorage.getItem('allPermittedStores')
          ).join(','),
          stores: iKpiIdsStore,
          fromdate: moment(filterStart).format('MM/DD/YY'),
          todate: moment(filterEnd).format('MM/DD/YY'),
          recipients: emailvalue.length > 0 ? emailvalue.join(',') : emailvalue,
          ccrecipients:
            emailvalueCc.length > 0 ? emailvalueCc.join(',') : emailvalueCc,
          bccrecipients:
            emailvalueBcc.length > 0 ? emailvalueBcc.join(',') : emailvalueBcc,
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          realm: localStorage.getItem('realm'),
          // storegroup: localStorage.getItem('storeSelected'),
          storegroup:
            pathaname == 'KPIReportComparative' ||
            pathaname == 'KPIReportTechComparative'
              ? // ? storeNickName
                localStorage.getItem('storeSelected')
              : localStorage.getItem('storeGroup'),
          emailContent: description,
          hideGoals: hideGoals,
          expandedKPIs: expandedKPIs,
          hiddenKpis: hiddenKpis,
          sortOrder: sortOrder,
          reportName: reportName,
          storeName: storeNickName,
          kpiSortedArray: kpiSortedArray,
          tenantName: localStorage.getItem('storeGroup'),
          userId: fullName
        };

        fetch(process.env.REACT_APP_EMAIL_SERVICE_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(postData)
        })
          .then(response => {
            console.log('ressss', response);
            if (response.ok) {
              setOpenAlert(true);
              setAlertMsg('Email sent successfully');
              setDescription('');
              insertEmailToEmailList();
              setTimeout(() => {
                setOpenAlert(false);
                setOpenDialog(false);
                handlePopupClose();
                resetStates();
              }, 1000);
              return response;
            } else {
              setOpenAlertErr(true);
              setAlertMsgErr('Something went wrong');
              setTimeout(() => {
                setOpenAlertErr(false);
                setOpenDialog(false);
                handlePopupClose();
                resetStates();
              }, 1000);
            }

            throw new Error('Something went wrong');
          })
          .then(responseJson => {
            console.log(responseJson.status); // Will show you the status
          })

          .catch(error => console.error('Error posting data:', error));
      }
    } else {
      console.log('errorcheck', emailvalue, emailvalueBcc, emailvalueCc);
      setError('Please Enter Email Recipient');
      let mesg = 'Please Enter Email Recipient';
      setOpenAlertt(true);
      setAlertType('warning');
      setAlertMesg(mesg);
      setTimeout(() => {
        setOpenAlertt(false);
      }, 2000);
    }
  };

  const insertEmailToEmailList = () => {
    let array = lodash.concat(emailvalue, emailvalueCc, emailvalueBcc);

    let elementsNotExist = array.filter(
      element => element && !lodash.includes(mailUsers[0], element)
    );
    elementsNotExist =
      elementsNotExist.length == 1
        ? elementsNotExist[0].split(',')
        : elementsNotExist;
    getEmail(elementsNotExist, elementsNotExist, 'insert', 'NULL', result => {
      this.setState({ isLoading: true });
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        this.setState({ isLoading: false });
      } else {
        this.setState({ isLoading: false });
      }
    });
  };

  const handleSave = () => {
    handleSaveEmail();
  };

  const validateEmail = email => {
    let error = null;
    var isEmail;
    emailvalue &&
      emailvalue.map(item => {
        isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
          item
        );
      });

    if (!isEmail) {
      if (emailvalue != '') {
        error = 'Email address is not valid';
        setOpenAlertt(true);
        setAlertType('warning');
        setAlertMesg(error);
        setEmailvalue('');
        setValue([]);
        setTimeout(() => {
          setOpenAlertt(false);
          setAlertMesg('');
          setError('');
        }, 2000);
        return false;
      } else {
        setError('');
        return true;
      }
    }
    if (emailvalue) {
      let hasDuplicates = lodash.uniq(emailvalue).length !== emailvalue.length;

      let arrays = [emailvalue, emailvalueCc, emailvalueBcc];

      let hasCommonElements = false;

      // Compare each array with every other array
      for (let i = 0; i < arrays.length; i++) {
        for (let j = i + 1; j < arrays.length; j++) {
          if (_.intersection(arrays[i], arrays[j]).length > 0) {
            hasCommonElements = true;
            break;
          }
        }
      }

      if (hasDuplicates || hasCommonElements) {
        console.log(
          'hasDuplicates33333333333',
          emailvalue,
          arrays,
          hasDuplicates,
          hasCommonElements
        );
        error = 'This email address has already been added';
      }
    }

    if (error) {
      setError(error);
      setEmailvalue('');
      setRequiredText(false);
      setOpenAlertt(true);
      setAlertType('warning');
      setAlertMesg(error);
      setEmailvalue('');
      setValue([]);
      setTimeout(() => {
        setOpenAlertt(false);
        setAlertMesg('');
        setError('');
      }, 2000);
      return false;
    } else {
      setError('');
      return true;
    }

    // return true;
  };
  const validateEmailCc = email => {
    let error = null;

    var isEmail;
    emailvalueCc &&
      emailvalueCc.map(item => {
        isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
          item
        );
        // emailvalueCc.map(item => {
        //   isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        //     item
        //   );
      });

    if (!isEmail) {
      if (emailvalueCc && emailvalueCc != '') {
        error = 'Email address is not valid';
        setOpenAlertt(true);
        setAlertType('warning');

        setAlertMesg(error);
        setTimeout(() => {
          setOpenAlertt(false);
          setEmailvalueCc('');
          setValueCc([]);
          setAlertMesg('');
          setError('');
        }, 2000);
        return false;
      } else {
        setError('');
        return true;
      }
    }
    if (emailvalueCc) {
      let hasDuplicates =
        lodash.uniq(emailvalueCc).length !== emailvalueCc.length;

      let arrays = [emailvalue, emailvalueCc, emailvalueBcc];

      let hasCommonElements = false;

      // Compare each array with every other array
      for (let i = 0; i < arrays.length; i++) {
        for (let j = i + 1; j < arrays.length; j++) {
          if (_.intersection(arrays[i], arrays[j]).length > 0) {
            hasCommonElements = true;
            break;
          }
        }
      }
      if (hasDuplicates || hasCommonElements) {
        error = 'This email address has already been added';
      }
    }

    if (error) {
      setError(error);
      setEmailvalueCc('');
      setRequiredText(false);
      setOpenAlertt(true);
      setAlertType('warning');
      setEmailvalueCc('');
      setValueCc([]);
      setAlertMesg(error);
      setTimeout(() => {
        setOpenAlertt(false);
        setError('');
      }, 2000);
      return false;
    } else {
      setError('');
      return true;
    }

    // return true;
  };

  const validateEmailBcc = email => {
    let error = null;

    var isEmail;
    emailvalueBcc &&
      emailvalueBcc.map(item => {
        isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))(\s*,\s*(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))*$/.test(
          item
        );
        // emailvalueBcc.map(item => {
        //   isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
        //     item
        //   );
      });
    if (!isEmail) {
      if (emailvalueBcc && emailvalueBcc != '') {
        error = 'Email address is not valid';
        setOpenAlertt(true);
        setAlertType('warning');
        setValueBcc([]);
        setEmailvalueBcc('');
        setAlertMesg(error);
        setTimeout(() => {
          setOpenAlertt(false);
          setError('');
        }, 2000);
        return false;
      } else {
        setError('');
        return true;
      }
    }
    if (emailvalueBcc) {
      let hasDuplicates =
        lodash.uniq(emailvalueBcc).length !== emailvalueBcc.length;

      let arrays = [emailvalue, emailvalueCc, emailvalueBcc];

      let hasCommonElements = false;

      // Compare each array with every other array
      for (let i = 0; i < arrays.length; i++) {
        for (let j = i + 1; j < arrays.length; j++) {
          if (_.intersection(arrays[i], arrays[j]).length > 0) {
            hasCommonElements = true;
            break;
          }
        }
      }
      if (hasDuplicates || hasCommonElements) {
        error = 'This email address has already been added';
      }
    }
    if (error) {
      setError(error);
      setEmailvalueBcc('');
      setRequiredText(false);
      setOpenAlertt(true);
      setAlertType('warning');
      setValueBcc([]);
      setEmailvalueBcc('');
      setAlertMesg(error);
      setTimeout(() => {
        setOpenAlertt(false);
        setError('');
      }, 2000);
      return false;
    } else {
      setError('');
      return true;
    }

    // return true;
  };

  const emailCcClick = () => {
    setEmailCcClick(true);
  };
  const emailBccClick = () => {
    setEmailBccClick(true);
  };
  const label = { inputProps: { 'aria-label': 'Checkbox demo' } };
  console.log('ffff===', headerData);
  return (
    <Dialog
      transition={Fade}
      classes={{
        paper: classes.paper
      }}
      BackdropProps={{
        classes: {
          root: classes.backDrop
        }
      }}
      //maxWidth="xl"
      // style={{ maxWidth: 900, maxHeight: 700 }}
      open={openDialog}
    >
      <DialogTitle id="confirmation-dialog-title">
        <Typography
          variant="h5"
          color="primary"
          style={{
            textTransform: 'none'
          }}
        >
          Email Now
        </Typography>
      </DialogTitle>
      <Collapse in={openAlert}>
        <Alert
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Collapse>
      <Collapse in={openAlertErr}>
        <Alert
          severity="error"
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlertErr(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsgErr}
        </Alert>
      </Collapse>
      <Collapse in={openAlertt}>
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMesg}
        </Alert>
      </Collapse>
      <DialogContent style={{ overflowX: 'hidden' }}>
        <TableContainer
          component={Paper}
          style={{
            margin: 4,
            padding: 1,
            display: 'block',
            width: '100%'
          }}
        >
          <Table
            className="email-table"
            id="maildetails"
            // style={{ minWidth: 300 }}
            size="small"
            aria-label="a dense table"
          >
            <TableHead
              style={{
                textAlign: 'center',
                backgroundColor: '#003d6b'
              }}
            ></TableHead>
            <TableRow key={'email'}>
              <TableCell
                align="left"
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                <span
                  style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                  <span>
                    Email <span style={{ color: 'red' }}>*</span>
                  </span>
                  <span>
                    <span
                      style={{
                        marginRight: '10px',
                        cursor: 'pointer',
                        display: 'none'
                      }}
                      onClick={emailCcClick}
                    >
                      Cc
                    </span>
                    <span
                      style={{ cursor: 'pointer', display: 'none' }}
                      onClick={emailBccClick}
                    >
                      Bcc
                    </span>
                  </span>
                </span>
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell height={10}>
                <Autocomplete
                  multiple
                  freeSolo
                  ListboxProps={{ style: { maxHeight: 150 } }}
                  options={
                    mailUsers.length > 0 ? [...new Set(mailUsers[0])] : []
                  }
                  value={value}
                  getOptionDisabled={option => value.includes(option)}
                  // onChange={(event, newValue) => setValue(newValue)}
                  onChange={(event, newValue) => {
                    setValue(newValue);
                    setEmailvalue(newValue);
                    if (newValue) setRequiredText(false);
                    setError('');
                  }}
                  onInputChange={(event, newInputValue) => {
                    if (event && event.type === 'change') {
                      setEmailvalue([newInputValue]);
                    }
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip label={option} {...getTagProps({ index })} />
                    ))
                  }
                  renderInput={params => (
                    <TextField
                      {...params}
                      style={{ minWidth: '300px', bottom: 4 }}
                      variant="standard"
                      fullWidth
                      margin="normal"
                      required
                      id="email"
                      name="email"
                      value={emailvalue}
                      helperText={requiredText && 'This is required!'}
                      onBlur={validateEmail}
                      // onChange={onChangeEmail}
                    />
                  )}
                />
              </TableCell>
            </TableRow>
            <TableRow
              className={
                classes.emailCc
                //emailCcClicked ? classes.emailCc : classes.emailCcDisable
              }
            >
              <TableCell height={10}>
                <Autocomplete
                  multiple
                  freeSolo
                  ListboxProps={{ style: { maxHeight: 150 } }}
                  options={mailUsers.length > 0 ? mailUsers[0] : []}
                  value={valueCc}
                  getOptionDisabled={option => valueCc.includes(option)}
                  // onChange={(event, newValue) => setValueCc(newValue)}
                  onChange={(event, newValue) => {
                    setValueCc(newValue);
                    setEmailvalueCc(newValue);
                    if (newValue) setRequiredText(false);
                    setError('');
                  }}
                  onInputChange={(event, newInputValue) => {
                    if (event && event.type === 'change') {
                      setEmailvalueCc([newInputValue]);
                    }
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip label={option} {...getTagProps({ index })} />
                    ))
                  }
                  renderInput={params => (
                    <TextField
                      {...params}
                      style={{ minWidth: '300px', bottom: 18 }}
                      variant="standard"
                      fullWidth
                      label="Cc"
                      margin="normal"
                      //required
                      id="emailCc"
                      name="emailCc"
                      value={emailvalueCc}
                      onBlur={validateEmailCc}
                      // onChange={onChangeEmailCc}
                      InputLabelProps={{
                        style: { color: 'rgb(0, 61, 107)' }
                      }}
                    />
                  )}
                />
              </TableCell>
            </TableRow>
            <TableRow
              className={
                classes.emailBcc
                //emailBccClicked ? classes.emailBcc : classes.emailBccDisable
              }
            >
              <TableCell height={10}>
                <Autocomplete
                  multiple
                  freeSolo
                  ListboxProps={{ style: { maxHeight: 150 } }}
                  options={mailUsers.length > 0 ? mailUsers[0] : []}
                  value={valueBcc}
                  getOptionDisabled={option => valueBcc.includes(option)}
                  //onChange={(event, newValue) => setValueBcc(newValue)}
                  onChange={(event, newValue) => {
                    setValueBcc(newValue);
                    setEmailvalueBcc(newValue);
                    if (newValue) setRequiredText(false);
                    setError('');
                  }}
                  onInputChange={(event, newInputValue) => {
                    if (event && event.type === 'change') {
                      setEmailvalueBcc([newInputValue]);
                    }
                  }}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip label={option} {...getTagProps({ index })} />
                    ))
                  }
                  renderInput={params => (
                    <TextField
                      {...params}
                      style={{ minWidth: '300px', bottom: 18 }}
                      variant="standard"
                      fullWidth
                      label="Bcc"
                      margin="normal"
                      //required
                      id="emailBcc"
                      name="emailBcc"
                      value={emailvalueBcc}
                      onBlur={validateEmailBcc}
                      //  onChange={onChangeEmailBcc}
                      InputLabelProps={{
                        style: { color: 'rgb(0, 61, 107)' }
                      }}
                    />
                  )}
                />
              </TableCell>
            </TableRow>
            <TableRow>
              <TableCell>
                <span
                  style={{
                    fontSize: 14,
                    color: '#003d6b'
                  }}
                >
                  Message
                </span>
                <TextareaAutosize
                  aria-label="minimum height"
                  // minRows={5}
                  rowsMin={5}
                  rowsMax={5}
                  maxLength={257}
                  placeholder="Maximum 256 letters"
                  onChange={e => handleDescription(e)}
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.preventDefault(); // Prevents the default behavior of Enter
                      const textarea = e.target;
                      const value = textarea.value;
                      const cursorPosition = textarea.selectionStart;

                      // Insert a newline character at the cursor position
                      const newValue =
                        value.slice(0, cursorPosition) +
                        '\n' +
                        value.slice(cursorPosition);

                      // Set the new value and update the cursor position
                      textarea.value = newValue;
                      textarea.setSelectionRange(
                        cursorPosition + 1,
                        cursorPosition + 1
                      );

                      handleDescription({ target: { value: newValue } });
                    }
                  }}
                  style={{
                    width: '100%',
                    padding: '5px',
                    // maxHeight: '80px',
                    overflow: 'auto',
                    resize: 'none'
                  }}
                />
              </TableCell>
            </TableRow>
          </Table>
        </TableContainer>
        {/* {error && <p className="error">{error}</p>} */}
        {errorChecked && <p className="errorChk">{errorChecked}</p>}
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleOk}
          color="primary"
        >
          Cancel
        </Button>

        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleSave}
          color="primary"
          disabled={filterDisabled}
        >
          Send Now
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function extractValues(mappings) {
  return Object.keys(mappings);
}
const DayTypeMapping = {
  daily: 'Daily',
  weekly: 'Weekly',
  biweekly: 'Biweekly',
  monthly: 'Monthly'
};

const DayType = extractValues(DayTypeMapping);
const lookupValue = (mappings, key) => {
  return mappings[key];
};

export default withKeycloak(EmailDialogkpi);
