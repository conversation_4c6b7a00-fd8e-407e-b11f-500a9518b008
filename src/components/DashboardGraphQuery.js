import {
  getDateRange,
  getDetailedGraphDateRange,
  getQueryFilter,
  getStoreIdFilter
} from 'src/components/ViewGraphDetailsAction';

export function getDashboardGraphQuery(chartId, filters, storeId, realm) {
  let filterArray = [];

  // if (!filters.includes('All')) {
  //   filterArray.push({
  //     member: 'CPRevenue.serviceadvisor',
  //     operator: 'equals',
  //     values: filters
  //   });
  // }

  let query = '';
  switch (chartId) {
    case 942: {
      query = {
        measures: [
          'CPRevenue.labor_revenue',
          'CPRevenue.parts_revenue',
          'CPRevenue.combined'
        ],
        timeDimensions: [
          {
            dimension: 'CPRevenue.closeddate',
            dateRange: getDetailedGraphDateRange(realm),
            granularity: 'month'
          }
        ],
        filters: getQueryFilter(filters, 'CPRevenue', storeId),
        order: {
          'CPRevenue.closeddate': 'desc'
        }
      };

      return query;
    }
    case 939: {
      query = {
        measures: [
          'CPGrossProfit.laborgrossprofit',
          'CPGrossProfit.prt_gross_profit',
          'CPGrossProfit.combinedgrossprofit'
        ],
        timeDimensions: [
          {
            dimension: 'CPGrossProfit.ro_date',
            dateRange: getDetailedGraphDateRange(realm),
            granularity: 'month'
          }
        ],

        filters: getQueryFilter(filters, 'CPGrossProfit', storeId),
        order: {
          'CPGrossProfit.ro_date': 'desc'
        }
      };
      return query;
    }
    case 940: {
      query = {
        measures: [
          'CPGrossProfitPercentage.labourprofitpercent',
          'CPGrossProfitPercentage.partsprofitpercent',
          'CPGrossProfitPercentage.combinedprofitpercent'
        ],
        timeDimensions: [
          {
            dimension: 'CPGrossProfitPercentage.ro_date',
            dateRange: getDetailedGraphDateRange(realm),
            granularity: 'month'
          }
        ],

        filters: getStoreIdFilter('CPGrossProfitPercentage', storeId),
        order: {
          'CPGrossProfitPercentage.ro_date': 'desc'
        }
      };
      return query;
    }
    case 920: {
      query = {
        measures: ['CPLabourhours.soldhours'],
        timeDimensions: [
          {
            dimension: 'CPLabourhours.ro_date',
            dateRange: getDetailedGraphDateRange(realm),
            granularity: 'month'
          }
        ],

        filters: getQueryFilter(filters, 'CPLabourhours', storeId),
        order: {
          'CPLabourhours.ro_date': 'desc'
        }
      };
      return query;
    }

    case 946: {
      query = {
        measures: ['CPEffectiveLabourRate.effective_labour_rate'],
        timeDimensions: [
          {
            dimension: 'CPEffectiveLabourRate.ro_date',
            dateRange: getDetailedGraphDateRange(realm),
            granularity: 'month'
          }
        ],

        filters: getStoreIdFilter('CPEffectiveLabourRate', storeId),
        order: {
          'CPEffectiveLabourRate.ro_date': 'desc'
        }
      };
      return query;
    }
    case 925: {
      query = {
        measures: ['CPRoCount.ronumber'],
        timeDimensions: [
          {
            dimension: 'CPRoCount.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPRoCount.ro_date'],
        filters: getQueryFilter(filters, 'CPRoCount', storeId),
        order: {
          'CPRoCount.ro_date': 'desc'
        }
      };
      return query;
    }
    case 960: {
      query = {
        measures: [
          'CPLaborRevenue.revenuecurrentyear',
          'CPLaborRevenue.revenuepreviousyear',
          'CPLaborRevenue.revenueyearbeforeprevious'
        ],

        dimensions: ['CPLaborRevenue.closeddate'],
        filters: getQueryFilter(filters, 'CPLaborRevenue', storeId),
        order: {
          'CPLaborRevenue.closeddate': 'asc'
        }
      };
      return query;
    }
    case 944: {
      query = {
        measures: [
          'CPLaborGrossProfit.laborgrossprofit_currentyear',
          'CPLaborGrossProfit.laborgrossprofit_previousyear',
          'CPLaborGrossProfit.laborgrossprofit_pastyear'
        ],

        dimensions: ['CPLaborGrossProfit.closeddate'],
        filters: getQueryFilter(filters, 'CPLaborGrossProfit', storeId),
        order: {
          'CPLaborGrossProfit.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1073: {
      query = {
        measures: [
          'CPLaborGrossProfitPercentage.lgpcurrentyear',
          'CPLaborGrossProfitPercentage.lgppreviousyear',
          'CPLaborGrossProfitPercentage.lgpyearbeforeprevious'
        ],

        dimensions: ['CPLaborGrossProfitPercentage.closeddate'],
        filters: getStoreIdFilter('CPLaborGrossProfitPercentage', storeId),
        order: {
          'CPLaborGrossProfitPercentage.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1044: {
      query = {
        measures: [
          'CPLaborHoursPerRO.hours_per_repair_order_currentyear',
          'CPLaborHoursPerRO.hours_per_repair_order_previousyear',
          'CPLaborHoursPerRO.hours_per_repair_order_pastyear'
        ],

        dimensions: ['CPLaborHoursPerRO.closeddate'],
        filters: getQueryFilter(filters, 'CPLaborHoursPerRO', storeId),
        order: {
          'CPLaborHoursPerRO.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1098: {
      query = {
        measures: [
          'CPRepairAndCompetitive.competitiverepair_currentyear',
          'CPRepairAndCompetitive.competitiverepair_previousyear',
          'CPRepairAndCompetitive.competitiverepair_pastyear'
        ],

        dimensions: ['CPRepairAndCompetitive.closeddate'],
        filters: getStoreIdFilter('CPRepairAndCompetitive', storeId),
        order: {
          'CPRepairAndCompetitive.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1356: {
      query = {
        measures: [
          'CPMaintandComp.competitivemaintenance_currentyear',
          'CPMaintandComp.competitivemaintenance_previousyear',
          'CPMaintandComp.competitivemaintenance_pastyear'
        ],

        dimensions: ['CPMaintandComp.closeddate'],
        filters: getStoreIdFilter('CPMaintandComp', storeId),
        order: {
          'CPMaintandComp.closeddate': 'asc'
        }
      };
      return query;
    }
    case 918: {
      query = {
        measures: [
          'CPJobCountAllCategories.rocount_currentyear',
          'CPJobCountAllCategories.rocount_previousyear',
          'CPJobCountAllCategories.rocount_pastyear'
        ],

        dimensions: ['CPJobCountAllCategories.closeddate'],
        filters: getQueryFilter(filters, 'CPJobCountAllCategories', storeId),
        order: {
          'CPJobCountAllCategories.closeddate': 'asc'
        }
      };
      return query;
    }
    case 956: {
      query = {
        measures: [
          'CPLaborTechHours.laboractualhours_pastyear',
          'CPLaborTechHours.laboractualhours_previousyear',
          'CPLaborTechHours.laboractualhours_currentyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPLaborTechHours.closeddate'
          }
        ],
        dimensions: ['CPLaborTechHours.closeddate'],
        filters: getQueryFilter(filters, 'CPLaborTechHours', storeId),
        order: {
          'CPLaborTechHours.closeddate': 'desc'
        }
      };
      return query;
    }
    case 955: {
      query = {
        measures: [
          'CPAverageLaborRo.sale_per_repair_order_currentyear',
          'CPAverageLaborRo.sale_per_repair_order_previousyear',
          'CPAverageLaborRo.sale_per_repair_order_pastyear'
        ],

        dimensions: ['CPAverageLaborRo.closeddate'],
        filters: getStoreIdFilter('CPAverageLaborRo', storeId),
        order: {
          'CPAverageLaborRo.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1049: {
      query = {
        measures: [
          'CPPartsRevenue.prtsrevenue_currentyear',
          'CPPartsRevenue.prtsrevenue_previousyear',
          'CPPartsRevenue.prtsrevenue_pastyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsRevenue.closeddate'
            //  dateRange: getDateRange()
          }
        ],
        dimensions: ['CPPartsRevenue.closeddate'],
        filters: getQueryFilter(filters, 'CPPartsRevenue', storeId),
        order: {
          'CPPartsRevenue.closeddate': 'asc'
        }
      };
      return query;
    }

    case 952: {
      query = {
        measures: [
          'CPPartsGrossProfit.pgp_currentyear',
          'CPPartsGrossProfit.pgp_previousyear',
          'CPPartsGrossProfit.pgp_pastyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsGrossProfit.closeddate'
          }
        ],
        dimensions: ['CPPartsGrossProfit.closeddate'],
        filters: getQueryFilter(filters, 'CPPartsGrossProfit', storeId),
        order: {
          'CPPartsGrossProfit.closeddate': 'asc'
        }
      };
      return query;
    }
    case 966: {
      query = {
        measures: [
          'CPPartsGPP.partsgrossprofitpercentage_currentyear',
          'CPPartsGPP.partsgrossprofitpercentage_pastyear',
          'CPPartsGPP.partsgrossprofitpercentage_previousyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsGPP.closeddate'
          }
        ],
        dimensions: ['CPPartsGPP.closeddate'],
        filters: getStoreIdFilter('CPPartsGPP', storeId),
        order: {
          'CPPartsGPP.closeddate': 'asc'
        }
      };
      return query;
    }
    case 953: {
      query = {
        measures: [
          'CPPartsRevenuePerRO.parts_revenue_per_ro_currentyear',
          'CPPartsRevenuePerRO.parts_revenue_per_ro_previousyear',
          'CPPartsRevenuePerRO.parts_revenue_per_ro_pastyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsRevenuePerRO.closeddate'
          }
        ],
        dimensions: ['CPPartsRevenuePerRO.closeddate'],
        filters: getStoreIdFilter('CPPartsRevenuePerRO', storeId),
        order: {
          'CPPartsRevenuePerRO.closeddate': 'asc'
        }
      };
      return query;
    }
    case 916: {
      query = {
        measures: [
          'CPPartsMarkup.parts_markup_currentyear',
          'CPPartsMarkup.parts_markup_previousyear',
          'CPPartsMarkup.parts_markup_pastyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkup.closeddate'
          }
        ],
        dimensions: ['CPPartsMarkup.closeddate'],
        filters: getStoreIdFilter('CPPartsMarkup', storeId),
        order: {
          'CPPartsMarkup.closeddate': 'asc'
        }
      };
      return query;
    }
    case 948: {
      query = {
        measures: [
          'CPSingleJobROCount.mileage_blw_60K',
          'CPSingleJobROCount.mileage_abv_60K',
          'CPSingleJobROCount.single_job_count'
        ],
        timeDimensions: [
          {
            dimension: 'CPSingleJobROCount.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPSingleJobROCount.rodate'],
        filters: getQueryFilter(filters, 'CPSingleJobROCount', storeId),
        order: {
          'CPSingleJobROCount.rodate': 'desc'
        }
      };
      return query;
    }
    case 923: {
      query = {
        measures: [
          'CPSingleJobROCountPercentage.mileage_blw_60K_perc',
          'CPSingleJobROCountPercentage.mileage_abv_60K_perc',
          'CPSingleJobROCountPercentage.percentage'
        ],
        timeDimensions: [
          {
            dimension: 'CPSingleJobROCountPercentage.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPSingleJobROCountPercentage.closeddate'],
        filters: getStoreIdFilter('CPSingleJobROCountPercentage', storeId),
        order: {
          'CPSingleJobROCountPercentage.closeddate': 'desc'
        }
      };
      return query;
    }
    case 938: {
      query = {
        measures: [
          'CPReturnRate.twelve_month_returnrate_chart',
          'CPReturnRate.six_month_returnrate_chart'
        ],
        timeDimensions: [
          {
            dimension: 'CPReturnRate.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPReturnRate.rodate'],
        filters: getStoreIdFilter('CPReturnRate', storeId),
        order: {
          'CPReturnRate.rodate': 'desc'
        }
      };
      return query;
    }
    case 935: {
      query = {
        measures: [
          'CPLaborSoldHoursByPaytype.lbrsldhrscustomerpay',
          'CPLaborSoldHoursByPaytype.lbrsldhrsextended',
          'CPLaborSoldHoursByPaytype.lbrsldhrsinternal',
          'CPLaborSoldHoursByPaytype.lbrsldhrsmaintenance',
          'CPLaborSoldHoursByPaytype.lbrsldhrswarranty',
          'CPLaborSoldHoursByPaytype.lbrsldhrfactoryservicecontract'
        ],
        timeDimensions: [
          {
            dimension: 'CPLaborSoldHoursByPaytype.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPLaborSoldHoursByPaytype.ro_date'],
        filters: getStoreIdFilter('CPLaborSoldHoursByPaytype', storeId),
        order: {
          'CPLaborSoldHoursByPaytype.ro_date': 'desc'
        }
      };
      return query;
    }
    case 930: {
      query = {
        measures: ['CPPartsToLaborRatio.partstolaborratio'],
        timeDimensions: [
          {
            dimension: 'CPPartsToLaborRatio.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsToLaborRatio.ro_date'],
        filters: getStoreIdFilter('CPPartsToLaborRatio', storeId),
        order: {
          'CPPartsToLaborRatio.ro_date': 'desc'
        }
      };
      return query;
    }
    case 936: {
      query = {
        measures: [
          'CPPartsToLaborRatioByCategory.rtiocompetitive',
          'CPPartsToLaborRatioByCategory.rtiomaintenance',
          'CPPartsToLaborRatioByCategory.rtiorepair'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsToLaborRatioByCategory.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPPartsToLaborRatioByCategory.ro_date'],
        filters: getStoreIdFilter('CPPartsToLaborRatioByCategory', storeId),
        order: {
          'CPPartsToLaborRatioByCategory.ro_date': 'desc'
        }
      };
      return query;
    }
    case 766: {
      query = {
        measures: ['DeltaSoldFlatHours.laborsoldhours_flatratehours'],
        timeDimensions: [
          {
            dimension: 'DeltaSoldFlatHours.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DeltaSoldFlatHours.ro_date'],
        filters: getStoreIdFilter('DeltaSoldFlatHours', storeId),
        order: {
          'DeltaSoldFlatHours.ro_date': 'desc'
        }
      };
      return query;
    }
    case 957: {
      query = {
        measures: ['DeltaTechFlatHours.laboractualhours_flatratehours'],
        timeDimensions: [
          {
            dimension: 'DeltaTechFlatHours.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DeltaTechFlatHours.ro_date'],
        filters: getStoreIdFilter('DeltaTechFlatHours', storeId),
        order: {
          'DeltaTechFlatHours.ro_date': 'desc'
        }
      };
      return query;
    }
    case 1099: {
      query = {
        measures: ['AddOnJobCounts.addonjobcount'],
        dimensions: ['AddOnJobCounts.closeddate'],
        filters: getStoreIdFilter('AddOnJobCounts', storeId),
        order: {
          'AddOnJobCounts.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1100: {
      query = {
        measures: [
          'AddOnVsNonAddOnJobCounts.addonjobcount',
          'AddOnVsNonAddOnJobCounts.nonaddonjobcount'
        ],
        dimensions: ['AddOnVsNonAddOnJobCounts.closeddate'],
        filters: getStoreIdFilter('AddOnVsNonAddOnJobCounts', storeId),
        order: {
          'AddOnVsNonAddOnJobCounts.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1101: {
      query = {
        measures: ['AddOnJobCountsPerRO.hoursperroforaddonjobs'],
        dimensions: ['AddOnJobCountsPerRO.closeddate'],
        filters: getStoreIdFilter('AddOnJobCountsPerRO', storeId),
        order: {
          'AddOnJobCountsPerRO.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1102: {
      query = {
        measures: ['AddOnsROCountByTotalRos.averageSum'],
        timeDimensions: [
          {
            dimension: 'AddOnsROCountByTotalRos.closeddate'
          }
        ],
        dimensions: ['AddOnsROCountByTotalRos.closeddate'],
        filters: getStoreIdFilter('AddOnsROCountByTotalRos', storeId),
        order: {
          'AddOnsROCountByTotalRos.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1103: {
      query = {
        measures: [
          'AddOnJobCountByOpCategory.addonjobcountcompetitive',
          'AddOnJobCountByOpCategory.addonjobcountmaintenance',
          'AddOnJobCountByOpCategory.addonjobcountrepair',
          'AddOnJobCountByOpCategory.addonjobcountshopsupply'
        ],
        dimensions: ['AddOnJobCountByOpCategory.closeddate'],
        filters: getStoreIdFilter('AddOnJobCountByOpCategory', storeId),
        order: {
          'AddOnJobCountByOpCategory.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1104: {
      query = {
        measures: [
          'ROCountAddOnsAndNonAddOns.addonrocount',
          'ROCountAddOnsAndNonAddOns.nonaddonrocount'
        ],
        dimensions: ['ROCountAddOnsAndNonAddOns.closeddate'],
        filters: getStoreIdFilter('ROCountAddOnsAndNonAddOns', storeId),
        order: {
          'ROCountAddOnsAndNonAddOns.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1105: {
      query = {
        measures: ['AddOnRevenues.totalSum'],
        dimensions: ['AddOnRevenues.closeddate'],
        filters: getStoreIdFilter('AddOnRevenues', storeId),
        order: {
          'AddOnRevenues.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1106: {
      query = {
        measures: ['AddOnLaborRevenue.addonlbrsale'],
        dimensions: ['AddOnLaborRevenue.closeddate'],
        filters: getStoreIdFilter('AddOnLaborRevenue', storeId),
        order: {
          'AddOnLaborRevenue.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1107: {
      query = {
        measures: ['AddOnPartsRevenue.addonpartsrevenue'],
        dimensions: ['AddOnPartsRevenue.closeddate'],
        filters: getStoreIdFilter('AddOnPartsRevenue', storeId),
        order: {
          'AddOnPartsRevenue.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1108: {
      query = {
        measures: [
          'AddOnVsNonAddOnSoldHours.addonlbrsoldhours',
          'AddOnVsNonAddOnSoldHours.nonaddlbrsoldhours'
        ],
        dimensions: ['AddOnVsNonAddOnSoldHours.closeddate'],
        filters: getStoreIdFilter('AddOnVsNonAddOnSoldHours', storeId),
        order: {
          'AddOnVsNonAddOnSoldHours.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1109: {
      query = {
        measures: [
          'AddOnLbrRevenueOpCategory.addonlbrsalecompetitive',
          'AddOnLbrRevenueOpCategory.addonlbrsalemaintenance',
          'AddOnLbrRevenueOpCategory.addonlbrsalerepair',
          'AddOnLbrRevenueOpCategory.addonlbrsaleshopsupply'
        ],
        dimensions: ['AddOnLbrRevenueOpCategory.closeddate'],
        filters: getStoreIdFilter('AddOnLbrRevenueOpCategory', storeId),
        order: {
          'AddOnLbrRevenueOpCategory.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1110: {
      query = {
        measures: [
          'AddOnPrtsRevenueOpCategory.addonpartsrevenuecompetitive',
          'AddOnPrtsRevenueOpCategory.addonpartsrevenuemaintenance',
          'AddOnPrtsRevenueOpCategory.addonpartsrevenuerepair',
          'AddOnPrtsRevenueOpCategory.addonpartsrevenueshopsupply'
        ],
        dimensions: ['AddOnPrtsRevenueOpCategory.closeddate'],
        filters: getStoreIdFilter('AddOnPrtsRevenueOpCategory', storeId),
        order: {
          'AddOnPrtsRevenueOpCategory.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1116: {
      query = {
        measures: [
          'AddOnElr.competitiveelr',
          'AddOnElr.maintenanceelr',
          'AddOnElr.repairelr',
          'AddOnElr.shopsupplyelr'
        ],
        dimensions: ['AddOnElr.closeddate'],
        filters: getStoreIdFilter('AddOnElr', storeId),
        order: {
          'AddOnElr.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1117: {
      query = {
        measures: [
          'AddOnPartsMarkup.competitivemarkup',
          'AddOnPartsMarkup.maintenancemarkup',
          'AddOnPartsMarkup.repairmarkup',
          'AddOnPartsMarkup.shopsupplymarkup'
        ],
        dimensions: ['AddOnPartsMarkup.closeddate'],
        filters: getStoreIdFilter('AddOnPartsMarkup', storeId),
        order: {
          'AddOnPartsMarkup.closeddate': 'desc'
        }
      };
      return query;
    }

    case 1111: {
      query = {
        measures: ['LaborPartsDiscount.totaldiscount'],
        timeDimensions: [
          {
            dimension: 'LaborPartsDiscount.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborPartsDiscount.closeddate'],
        filters: getStoreIdFilter('LaborPartsDiscount', storeId),
        order: {
          'LaborPartsDiscount.closeddate': 'desc'
        }
      };
      return query;
    }

    case 1112: {
      query = {
        measures: ['DiscountPercByDesc.discountpercentagebydescription'],
        timeDimensions: [
          {
            dimension: 'DiscountPercByDesc.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DiscountPercByDesc.closeddate'],
        filters: getStoreIdFilter('DiscountPercByDesc', storeId),
        order: {
          'DiscountPercByDesc.closeddate': 'desc'
        }
      };
      return query;
    }

    case 1113: {
      query = {
        measures: [
          'ROCountDescLevel.rorocount',
          'ROCountDescLevel.linecount',
          'ROCountDescLevel.lopcount'
        ],
        timeDimensions: [
          {
            dimension: 'ROCountDescLevel.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['ROCountDescLevel.closeddate'],
        filters: getStoreIdFilter('ROCountDescLevel', storeId),
        order: {
          'ROCountDescLevel.closeddate': 'desc'
        }
      };
      return query;
    }

    case 1133: {
      query = {
        measures: [
          'CPLaborSoldHours.laborsoldhours_currentyear',
          'CPLaborSoldHours.laborsoldhours_previousyear',
          'CPLaborSoldHours.laborsoldhours_pastyear'
        ],

        dimensions: ['CPLaborSoldHours.closeddate'],
        filters: getQueryFilter(filters, 'CPLaborSoldHours', storeId),
        order: {
          'CPLaborSoldHours.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1127: {
      query = {
        measures: [
          'CPLaborEffectiveLaborRate.effective_lbr_rate_currentyear',
          'CPLaborEffectiveLaborRate.effective_lbr_rate_previousyear',
          'CPLaborEffectiveLaborRate.effective_lbr_rate_pastyear'
        ],

        dimensions: ['CPLaborEffectiveLaborRate.closeddate'],
        filters: getStoreIdFilter('CPLaborEffectiveLaborRate', storeId),
        // filters: filters.includes('All')
        //  ? []
        // : [
        //     {
        //       member: 'CPLaborEffectiveLaborRate.serviceadvisor',
        //       operator: 'equals',
        //       values: filters
        //     }
        //   ],
        order: {
          'CPLaborEffectiveLaborRate.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1138: {
      query = {
        measures: [
          'CPLaborROCount.rocount_currentyear',
          'CPLaborROCount.rocount_previousyear',
          'CPLaborROCount.rocount_pasttyear'
        ],

        dimensions: ['CPLaborROCount.closeddate'],
        filters: getQueryFilter(filters, 'CPLaborROCount', storeId),
        order: {
          'CPLaborROCount.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1143: {
      query = {
        measures: [
          'CPPartsROCount.rocount_currentyear',
          'CPPartsROCount.rocount_previousyear',
          'CPPartsROCount.rocount_pasttyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsROCount.closeddate'
          }
        ],
        dimensions: ['CPPartsROCount.closeddate'],
        filters: getQueryFilter(filters, 'CPPartsROCount', storeId),
        order: {
          'CPPartsROCount.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1123: {
      query = {
        measures: ['DiscountedROPerc.percentagerocount'],
        timeDimensions: [
          {
            dimension: 'DiscountedROPerc.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DiscountedROPerc.closeddate'],
        filters: getStoreIdFilter('DiscountedROPerc', storeId),
        order: {
          'DiscountedROPerc.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1115: {
      query = {
        measures: ['DiscountedSalesPercentage.percentagepertotalcpsale'],
        timeDimensions: [
          {
            dimension: 'DiscountedSalesPercentage.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DiscountedSalesPercentage.closeddate'],
        filters: getStoreIdFilter('DiscountedSalesPercentage', storeId),
        order: {
          'DiscountedSalesPercentage.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1164: {
      query = {
        measures: ['DiscountedTotalCPRos.totaldiscountspercpro'],
        dimensions: ['DiscountedTotalCPRos.closeddate'],
        filters: getStoreIdFilter('DiscountedTotalCPRos', storeId),
        order: {
          'DiscountedTotalCPRos.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1165: {
      query = {
        measures: ['TotalDiscountedCPRos.totaldiscountsperdiscountedcpro'],
        dimensions: ['TotalDiscountedCPRos.closeddate'],
        filters: getStoreIdFilter('TotalDiscountedCPRos', storeId),
        order: {
          'TotalDiscountedCPRos.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1174: {
      query = {
        measures: ['CPAverageHoursSoldPerTech.averagehrspertech'],
        dimensions: ['CPAverageHoursSoldPerTech.closeddate'],
        filters: getStoreIdFilter('CPAverageHoursSoldPerTech', storeId),
        order: {
          'CPAverageHoursSoldPerTech.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1175: {
      query = {
        measures: ['CPAverageSalesPerTech.averagesalepertech'],
        dimensions: ['CPAverageSalesPerTech.closeddate'],
        filters: getStoreIdFilter('CPAverageSalesPerTech', storeId),
        order: {
          'CPAverageSalesPerTech.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1232: {
      query = {
        measures: ['DiscountPerTotalDiscounted.discountedsale'],
        timeDimensions: [
          {
            dimension: 'DiscountPerTotalDiscounted.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DiscountPerTotalDiscounted.closeddate'],
        filters: getStoreIdFilter('DiscountPerTotalDiscounted', storeId),
        order: {
          'DiscountPerTotalDiscounted.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1233: {
      query = {
        measures: [
          'DiscountPerTotalDiscounted.discountedlabor',
          'DiscountPerTotalDiscounted.discountedparts'
        ],
        timeDimensions: [
          {
            dimension: 'DiscountPerTotalDiscounted.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DiscountPerTotalDiscounted.closeddate'],
        filters: getStoreIdFilter('DiscountPerTotalDiscounted', storeId),
        order: {
          'DiscountPerTotalDiscounted.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1234: {
      query = {
        measures: [
          'LaborPartsDiscount.totallabordiscount',
          'LaborPartsDiscount.totalpartsdiscount',
          'LaborPartsDiscount.totaldiscount'
        ],
        timeDimensions: [
          {
            dimension: 'LaborPartsDiscount.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['LaborPartsDiscount.closeddate'],
        filters: getStoreIdFilter('LaborPartsDiscount', storeId),
        order: {
          'LaborPartsDiscount.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1235: {
      query = {
        measures: [
          'DiscountedSalesPercentage.discountedlabor',
          'DiscountedSalesPercentage.discountedparts'
        ],
        timeDimensions: [
          {
            dimension: 'DiscountedSalesPercentage.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['DiscountedSalesPercentage.closeddate'],
        filters: getStoreIdFilter('DiscountedSalesPercentage', storeId),
        order: {
          'DiscountedSalesPercentage.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1236: {
      query = {
        measures: [
          'DiscountedTotalCPRos.discountedlaborpercpro',
          'DiscountedTotalCPRos.discountedpartspercpro',
          'DiscountedTotalCPRos.totaldiscountspercpro'
        ],
        dimensions: ['DiscountedTotalCPRos.closeddate'],
        filters: getStoreIdFilter('DiscountedTotalCPRos', storeId),
        order: {
          'DiscountedTotalCPRos.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1237: {
      query = {
        measures: [
          'TotalDiscountedCPRos.discountedlaborperdiscountedro',
          'TotalDiscountedCPRos.discountedpartsrperdiscountedro'
        ],
        dimensions: ['TotalDiscountedCPRos.closeddate'],
        filters: getStoreIdFilter('TotalDiscountedCPRos', storeId),
        order: {
          'TotalDiscountedCPRos.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1238: {
      query = {
        measures: ['CPPartsMarkupOverview.parts_markup'],
        timeDimensions: [
          {
            dimension: 'CPPartsMarkupOverview.closeddate',
            dateRange: getDetailedGraphDateRange(realm),
            granularity: 'month'
          }
        ],

        filters: getStoreIdFilter('CPPartsMarkupOverview', storeId),
        order: {
          'CPPartsMarkupOverview.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1239: {
      query = {
        measures: ['shopSupplies.customerpayshopsup'],
        timeDimensions: [
          {
            dimension: 'shopSupplies.rodate'
          }
        ],
        dimensions: ['shopSupplies.rodate'],
        filters: getQueryFilter(
          filters,
          'shopSupplies',
          realm == 'haleyag' ? [storeId] : storeId,
          realm
        ),
        order: {
          'shopSupplies.rodate': 'desc'
        }
      };
      return query;
    }
    // case 1276: {
    //   filters.includes('All')
    //     ? (query = {
    //         measures: ['ServiceAdvisorDrillDownAll.totalrevenue'],
    //         dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
    //         filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
    //         order: {
    //           'ServiceAdvisorDrillDownAll.ro_date': 'desc'
    //         }
    //       })
    //     : (query = {
    //         measures: ['ServiceAdvisorDrillDown.totalrevenue'],
    //         dimensions: ['ServiceAdvisorDrillDown.ro_date'],
    //         filters: getQueryFilter(
    //           filters,
    //           'ServiceAdvisorDrillDown',
    //           storeId
    //         ),
    //         order: {
    //           'ServiceAdvisorDrillDown.ro_date': 'desc'
    //         }
    //       });
    //   return query;
    // }
    case 1277: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.lbrsale'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.lbrsale'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1278: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.prtssale'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.prtssale'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1279: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.lbrsoldhours'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.lbrsoldhours'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1280: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.rocount'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.rocount'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1281: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.jobcount'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.jobcount'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1282: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.lbrprofit'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.lbrprofit'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1283: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.lbrprftpercentage'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.lbrprftpercentage'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1284: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.prtsprofit'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.prtsprofit'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1285: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.prtprfpercentage'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.prtprfpercentage'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1286: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.avgelr'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.avgelr'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1315: {
      filters.includes('All')
        ? (query = {
            measures: ['ServiceAdvisorDrillDownAll.avgmarkup'],
            dimensions: ['ServiceAdvisorDrillDownAll.ro_date'],
            filters: getStoreIdFilter('ServiceAdvisorDrillDownAll', storeId),
            order: {
              'ServiceAdvisorDrillDownAll.ro_date': 'desc'
            }
          })
        : (query = {
            measures: ['ServiceAdvisorDrillDown.avgmarkup'],
            dimensions: ['ServiceAdvisorDrillDown.ro_date'],
            filters: getQueryFilter(
              filters,
              'ServiceAdvisorDrillDown',
              storeId
            ),
            order: {
              'ServiceAdvisorDrillDown.ro_date': 'desc'
            }
          });
      return query;
    }
    case 1249: {
      query = {
        measures: ['TechEfficiency.techefficiency'],
        dimensions: ['TechEfficiency.ro_date'],
        order: { 'TechEfficiency.ro_date': 'desc' },
        filters: getQueryFilter([filters], 'TechEfficiency', storeId)
      };
      return query;
    }
    case 1250: {
      query = {
        measures: ['EstimatedTechEfficiency.techefficiencynonzero'],
        dimensions: ['EstimatedTechEfficiency.ro_date'],
        order: { 'EstimatedTechEfficiency.ro_date': 'desc' },
        filters: getQueryFilter([filters], 'EstimatedTechEfficiency', storeId)
      };
      return query;
    }
    case 1251: {
      query = {
        measures: [
          'TechEfficiency.nonzeroflatratehrsandnonzeroactualhrs',
          'TechEfficiency.nonzeroflatratehrsandzeroactualhrs',
          'TechEfficiency.zeroflatratehrsandnonzeroactualhrs',
          'TechEfficiency.zeroflatratehrsandzeroactualhrs'
        ],
        dimensions: ['TechEfficiency.ro_date'],
        order: { 'TechEfficiency.ro_date': 'desc' },
        filters: getQueryFilter([filters], 'TechEfficiency', storeId)
      };
      return query;
    }
    case 1348: {
      //1348
      filters.includes('All')
        ? (query = {
            measures: ['TotalTechEfficiency.job_count'],
            dimensions: ['TotalTechEfficiency.month_year'],
            order: { 'TotalTechEfficiency.month_year': 'desc' },
            filters: getQueryFilter([filters], 'TotalTechEfficiency', storeId)
          })
        : (query = {
            measures: ['TechsEfficiency.job_count'],
            dimensions: ['TechsEfficiency.month_year'],
            order: { 'TechsEfficiency.month_year': 'desc' },
            filters: getQueryFilter([filters], 'TechsEfficiency', storeId)
          });
      return query;
    }
    case 1347: {
      //1347
      filters.includes('All')
        ? (query = {
            measures: ['TotalTechEfficiency.tech_perc'],
            dimensions: ['TotalTechEfficiency.month_year'],
            order: { 'TotalTechEfficiency.month_year': 'desc' },
            filters: getQueryFilter([filters], 'TotalTechEfficiency', storeId)
          })
        : (query = {
            measures: ['TechsEfficiency.tech_perc'],
            dimensions: ['TechsEfficiency.month_year'],
            order: { 'TechsEfficiency.month_year': 'desc' },
            filters: getQueryFilter([filters], 'TechsEfficiency', storeId)
          });
      return query;
    }
    case 1345: {
      filters.includes('All')
        ? (query = {
            measures: ['TotalEstimatedTechEfficiencyWeekly.lbrsoldhours'],
            dimensions: ['TotalEstimatedTechEfficiencyWeekly.weekstartdate'],
            order: {
              'TotalEstimatedTechEfficiencyWeekly.weekstartdate': 'desc'
            },
            filters: getQueryFilter(
              [filters],
              'TotalEstimatedTechEfficiencyWeekly',
              storeId
            )
          })
        : (query = {
            measures: ['EstimatedTechEfficiencyWeekly.lbrsoldhours'],
            dimensions: ['EstimatedTechEfficiencyWeekly.weekstartdate'],
            order: { 'EstimatedTechEfficiencyWeekly.weekstartdate': 'desc' },
            filters: getQueryFilter(
              [filters],
              'EstimatedTechEfficiencyWeekly',
              storeId
            )
          });
      return query;
    }
    case 1352: {
      filters.includes('All')
        ? (query = {
            measures: [
              'TotalEstimatedTechEfficiencySoldHrsWeekly.lbrsoldhours'
            ],
            dimensions: [
              'TotalEstimatedTechEfficiencySoldHrsWeekly.weekstartdate'
            ],
            order: {
              'TotalEstimatedTechEfficiencySoldHrsWeekly.weekstartdate': 'desc'
            },
            filters: getQueryFilter(
              [filters],
              'TotalEstimatedTechEfficiencySoldHrsWeekly',
              storeId
            )
          })
        : (query = {
            measures: ['EstimatedTechEfficiencySoldHrsWeekly.lbrsoldhours'],
            dimensions: ['EstimatedTechEfficiencySoldHrsWeekly.weekstartdate'],
            order: {
              'EstimatedTechEfficiencySoldHrsWeekly.weekstartdate': 'desc'
            },
            filters: getQueryFilter(
              [filters],
              'EstimatedTechEfficiencySoldHrsWeekly',
              storeId
            )
          });
      return query;
    }
    case 1252: {
      query = {
        measures: [
          'TechEfficiency.jobcountflathrsandactualhrs',
          'TechEfficiency.jobcountflatratehrsandzeroactualhrs',
          'TechEfficiency.jobcountzeroflatratehrsandnonzeroactualhrs',
          'TechEfficiency.jobcountzeroflatratehrsandzeroactualhrs'
        ],
        dimensions: ['TechEfficiency.ro_date'],
        order: { 'TechEfficiency.ro_date': 'desc' },
        filters: getQueryFilter([filters], 'TechEfficiency', storeId)
      };
      return query;
    }
    case 1243: {
      filters.includes('All')
        ? (query = {
            measures: [
              'LaborWorkMixCost.Competetivelbrsale',
              'LaborWorkMixCost.Maintenancelbrsale',
              'LaborWorkMixCost.RepairSupplieslbrsale'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCost.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCost.ro_date'],
            filters: getStoreIdFilter('LaborWorkMixCost', storeId),
            order: { 'LaborWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'LaborWorkMixCostAdvisor.Competetivelbrsale',
              'LaborWorkMixCostAdvisor.Maintenancelbrsale',
              'LaborWorkMixCostAdvisor.Repairlbrsale'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCostAdvisor.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'LaborWorkMixCostAdvisor',
              storeId
            ),
            order: { 'LaborWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1244: {
      filters.includes('All')
        ? (query = {
            measures: [
              'LaborWorkMixCost.CompetetivePartsCost',
              'LaborWorkMixCost.MaintenancePartsCost',
              'LaborWorkMixCost.RepairSuppliesPartsCost'
              // 'LaborWorkMixCost.ShopSuppliesPartsCost'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCost.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCost.ro_date'],
            filters: getStoreIdFilter('LaborWorkMixCost', storeId),
            order: { 'LaborWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'LaborWorkMixCostAdvisor.CompetetiveSoldHours',
              'LaborWorkMixCostAdvisor.MaintenanceSoldHours',
              'LaborWorkMixCostAdvisor.RepairSoldHours'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCostAdvisor.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'LaborWorkMixCostAdvisor',
              storeId
            ),
            order: { 'LaborWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1245: {
      filters.includes('All')
        ? (query = {
            measures: [
              'LaborWorkMixCost.CompetetiveJobCount',
              'LaborWorkMixCost.MaintenanceJobCount',
              'LaborWorkMixCost.RepairJobCount'
              // 'LaborWorkMixCost.ShopSuppliesJobCount'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCost.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCost.ro_date'],
            filters: getStoreIdFilter('LaborWorkMixCost', storeId),
            order: { 'LaborWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'LaborWorkMixCostAdvisor.CompetetiveJobCount',
              'LaborWorkMixCostAdvisor.MaintenanceJobCount',
              'LaborWorkMixCostAdvisor.RepairJobCount'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCostAdvisor.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'LaborWorkMixCostAdvisor',
              storeId
            ),
            order: { 'LaborWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1246: {
      filters.includes('All')
        ? (query = {
            measures: [
              'LaborWorkMixCost.competitiveworkmix',
              'LaborWorkMixCost.maintenanceworkmix',
              'LaborWorkMixCost.repairworkmix'
              // 'LaborWorkMixCost.shopsuppliesworkmix'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCost.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCost.ro_date'],
            filters: getStoreIdFilter('LaborWorkMixCost', storeId),
            order: { 'LaborWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'LaborWorkMixCostAdvisor.competitiveworkmix',
              'LaborWorkMixCostAdvisor.maintenanceworkmix',
              'LaborWorkMixCostAdvisor.repairworkmix'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCostAdvisor.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'LaborWorkMixCostAdvisor',
              storeId
            ),
            order: { 'LaborWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1247: {
      filters.includes('All')
        ? (query = {
            measures: [
              'LaborWorkMixCost.competitiveelr',
              'LaborWorkMixCost.maintenanceelr',
              'LaborWorkMixCost.repairelr'
              //  'LaborWorkMixCost.shopsupplieselr'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCost.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCost.ro_date'],
            filters: getStoreIdFilter('LaborWorkMixCost', storeId),
            order: { 'LaborWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'LaborWorkMixCostAdvisor.competitiveelr',
              'LaborWorkMixCostAdvisor.maintenanceelr',
              'LaborWorkMixCostAdvisor.repairelr'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCostAdvisor.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'LaborWorkMixCostAdvisor',
              storeId
            ),
            order: { 'LaborWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1248: {
      filters.includes('All')
        ? (query = {
            measures: [
              'LaborWorkMixCost.competitivegrossprofitpercentage',
              'LaborWorkMixCost.maintenancegrossprofitpercentage',
              'LaborWorkMixCost.repairgrossprofitpercentage'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCost.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCost.ro_date'],
            filters: getStoreIdFilter('LaborWorkMixCost', storeId),
            order: { 'LaborWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'LaborWorkMixCostAdvisor.competitivegrossprofitpercentage',
              'LaborWorkMixCostAdvisor.maintenancegrossprofitpercentage',
              'LaborWorkMixCostAdvisor.repairgrossprofitpercentage'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'LaborWorkMixCostAdvisor.ro_date',
                dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['LaborWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'LaborWorkMixCostAdvisor',
              storeId
            ),
            order: { 'LaborWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }

    case 1253: {
      filters.includes('All')
        ? (query = {
            measures: [
              'PartsWorkMixCost.Competetiveprtsale',
              'PartsWorkMixCost.Maintenanceprtsale',
              'PartsWorkMixCost.RepairSuppliesprtsale'
              // 'PartsWorkMixCost.ShopSuppliesprtsale'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCost.ro_date'
              }
            ],
            dimensions: ['PartsWorkMixCost.ro_date'],
            filters: getStoreIdFilter('PartsWorkMixCost', storeId),
            order: { 'PartsWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'PartsWorkMixCostAdvisor.Competetiveprtsale',
              'PartsWorkMixCostAdvisor.Maintenanceprtsale',
              'PartsWorkMixCostAdvisor.RepairSuppliesprtsale'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCostAdvisor.ro_date'
                //dateRange: getDetailedGraphDateRange(realm)
              }
            ],
            dimensions: ['PartsWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'PartsWorkMixCostAdvisor',
              storeId
            ),
            order: { 'PartsWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1254: {
      filters.includes('All')
        ? (query = {
            measures: [
              'PartsWorkMixCost.CompetetivePartsCost',
              'PartsWorkMixCost.MaintenancePartsCost',
              'PartsWorkMixCost.RepairSuppliesPartsCost'
              // 'PartsWorkMixCost.ShopSuppliesPartsCost'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCost.ro_date'
                // dateRange: getDateRange('workmix')
              }
            ],
            dimensions: ['PartsWorkMixCost.ro_date'],
            filters: getStoreIdFilter('PartsWorkMixCost', storeId),
            order: { 'PartsWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'PartsWorkMixCostAdvisor.CompetetivePartsCost',
              'PartsWorkMixCostAdvisor.MaintenancePartsCost',
              'PartsWorkMixCostAdvisor.RepairSuppliesPartsCost'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCostAdvisor.ro_date'
              }
            ],
            dimensions: ['PartsWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'PartsWorkMixCostAdvisor',
              storeId
            ),
            order: { 'PartsWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1255: {
      filters.includes('All')
        ? (query = {
            measures: [
              'PartsWorkMixCost.CompetetiveJobCount',
              'PartsWorkMixCost.MaintenanceJobCount',
              'PartsWorkMixCost.RepairJobCount'
              //'PartsWorkMixCost.ShopSuppliesJobCount'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCost.ro_date'
                // dateRange: getDateRange('workmix')
              }
            ],
            dimensions: ['PartsWorkMixCost.ro_date'],
            filters: getStoreIdFilter('PartsWorkMixCost', storeId),
            order: { 'PartsWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'PartsWorkMixCostAdvisor.CompetetiveJobCount',
              'PartsWorkMixCostAdvisor.MaintenanceJobCount',
              'PartsWorkMixCostAdvisor.RepairJobCount'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCostAdvisor.ro_date'
              }
            ],
            dimensions: ['PartsWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'PartsWorkMixCostAdvisor',
              storeId
            ),
            order: { 'PartsWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1256: {
      filters.includes('All')
        ? (query = {
            measures: [
              'PartsWorkMixCost.competitiveworkmix',
              'PartsWorkMixCost.maintenanceworkmix',
              'PartsWorkMixCost.repairworkmix'
              // 'PartsWorkMixCost.shopsuppliesworkmix'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCost.ro_date'
                // dateRange: getDateRange('workmix')
              }
            ],
            dimensions: ['PartsWorkMixCost.ro_date'],
            filters: getStoreIdFilter('PartsWorkMixCost', storeId),
            order: { 'PartsWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'PartsWorkMixCostAdvisor.competitiveworkmix',
              'PartsWorkMixCostAdvisor.maintenanceworkmix',
              'PartsWorkMixCostAdvisor.repairworkmix'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCostAdvisor.ro_date'
              }
            ],
            dimensions: ['PartsWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'PartsWorkMixCostAdvisor',
              storeId
            ),
            order: { 'PartsWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1257: {
      filters.includes('All')
        ? (query = {
            measures: [
              'PartsWorkMixCost.Competetivemarkup',
              'PartsWorkMixCost.Maintenancemarkup',
              'PartsWorkMixCost.Repairmarkup'
              // 'PartsWorkMixCost.ShopSuppliesmarkup'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCost.ro_date'
                // dateRange: getDateRange('workmix')
              }
            ],
            dimensions: ['PartsWorkMixCost.ro_date'],
            filters: getStoreIdFilter('PartsWorkMixCost', storeId),
            order: { 'PartsWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'PartsWorkMixCostAdvisor.Competetivemarkup',
              'PartsWorkMixCostAdvisor.Maintenancemarkup',
              'PartsWorkMixCostAdvisor.Repairmarkup'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCostAdvisor.ro_date'
              }
            ],
            dimensions: ['PartsWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'PartsWorkMixCostAdvisor',
              storeId
            ),
            order: { 'PartsWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }
    case 1258: {
      filters.includes('All')
        ? (query = {
            measures: [
              'PartsWorkMixCost.Competetivegrossprofitpercentage',
              'PartsWorkMixCost.Maintenancegrossprofitpercentage',
              'PartsWorkMixCost.Repairgrossprofitpercentage'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCost.ro_date'
                //dateRange: getDateRange('workmix')
              }
            ],
            dimensions: ['PartsWorkMixCost.ro_date'],
            filters: getStoreIdFilter('PartsWorkMixCost', storeId),
            order: { 'PartsWorkMixCost.ro_date': 'desc' }
          })
        : (query = {
            measures: [
              'PartsWorkMixCostAdvisor.Competetivegrossprofitpercentage',
              'PartsWorkMixCostAdvisor.Maintenancegrossprofitpercentage',
              'PartsWorkMixCostAdvisor.Repairgrossprofitpercentage'
              // 'LaborWorkMixCost.ShopSupplieslbrsale'
            ],
            timeDimensions: [
              {
                dimension: 'PartsWorkMixCostAdvisor.ro_date'
              }
            ],
            dimensions: ['PartsWorkMixCostAdvisor.ro_date'],
            filters: getQueryFilter(
              filters,
              'PartsWorkMixCostAdvisor',
              storeId
            ),
            order: { 'PartsWorkMixCostAdvisor.ro_date': 'desc' }
          });
      return query;
    }

    case 1316: {
      query = {
        measures: ['MPIPenetration.mpipenetrationperc'],
        timeDimensions: [
          {
            dimension: 'MPIPenetration.month_year'
          }
        ],
        dimensions: ['MPIPenetration.month_year'],
        order: { 'MPIPenetration.month_year': 'desc' },
        filters: getQueryFilter(filters, 'MPIPenetration', storeId)
      };

      return query;
    }

    case 1317: {
      query = {
        measures: ['MenuPenetration.menupenetrationperc'],
        timeDimensions: [
          {
            dimension: 'MenuPenetration.month_year'
          }
        ],
        dimensions: ['MenuPenetration.month_year'],
        order: { 'MenuPenetration.month_year': 'desc' },
        filters: getQueryFilter(filters, 'MenuPenetration', storeId)
      };

      return query;
    }

    case 1318: {
      query = {
        measures: [
          'CPPartsHoursPerRO.hours_per_repair_order_currentyear',
          'CPPartsHoursPerRO.hours_per_repair_order_previousyear',
          'CPPartsHoursPerRO.hours_per_repair_order_pastyear'
        ],
        timeDimensions: [
          {
            dimension: 'CPPartsHoursPerRO.closeddate'
          }
        ],
        dimensions: ['CPPartsHoursPerRO.closeddate'],
        filters: getQueryFilter(filters, 'CPPartsHoursPerRO', storeId),
        order: {
          'CPPartsHoursPerRO.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1326: {
      query = {
        measures: [
          'ROCountPartsOnly.rocount_currentyear',
          'ROCountPartsOnly.rocount_previousyear',
          'ROCountPartsOnly.rocount_pasttyear'
        ],
        timeDimensions: [
          {
            dimension: 'ROCountPartsOnly.closeddate'
          }
        ],
        dimensions: ['ROCountPartsOnly.closeddate'],
        filters: getQueryFilter(filters, 'ROCountPartsOnly', storeId),
        order: {
          'ROCountPartsOnly.closeddate': 'asc'
        }
      };
      return query;
    }
    case 931: {
      query = {
        measures: [
          'CPTotalLaborOpportunity.lbrvolume_opportunity',
          'CPTotalLaborOpportunity.lbrgross_opportunity',
          'CPTotalLaborOpportunity.lbrjoint_opportunity'
        ],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPTotalLaborOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPTotalLaborOpportunity.rodate'],
        renewQuery: true,
        filters: getStoreIdFilter('CPTotalLaborOpportunity', storeId),
        order: {
          'CPTotalLaborOpportunity.rodate': 'desc'
        }
      };

      return query;
    }
    case 932: {
      query = {
        measures: ['CPLaborGrossOpportunity.labor_gross_opportunity'],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPLaborGrossOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPLaborGrossOpportunity.rodate'],
        filters: getStoreIdFilter('CPLaborGrossOpportunity', storeId),
        order: {
          'CPLaborGrossOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 933: {
      query = {
        measures: ['CPLaborVolumeOpportunity.labor_volume_opportunity'],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPLaborVolumeOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPLaborVolumeOpportunity.rodate'],
        filters: getStoreIdFilter('CPLaborVolumeOpportunity', storeId),
        order: {
          'CPLaborVolumeOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 934: {
      query = {
        measures: ['CPLaborJointOpportunity.labor_joint_opportunity'],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPLaborJointOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPLaborJointOpportunity.rodate'],
        filters: getStoreIdFilter('CPLaborJointOpportunity', storeId),
        order: {
          'CPLaborJointOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 926: {
      query = {
        measures: [
          'CPTotalPartsOpportunity.prtvolume_opportunity',
          'CPTotalPartsOpportunity.prtgross_opportunity',
          'CPTotalPartsOpportunity.prtjoint_opportunity'
        ],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPTotalPartsOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPTotalPartsOpportunity.rodate'],
        filters: getStoreIdFilter('CPTotalPartsOpportunity', storeId),
        order: {
          'CPTotalPartsOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 927: {
      query = {
        measures: ['CPPartsVolumeOpportunity.parts_volume_opportunity'],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPPartsVolumeOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPPartsVolumeOpportunity.rodate'],
        filters: getStoreIdFilter('CPPartsVolumeOpportunity', storeId),
        order: {
          'CPPartsVolumeOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 928: {
      query = {
        measures: ['CPPartsJointOpportunity.parts_joint_opportunity'],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPPartsJointOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPPartsJointOpportunity.rodate'],
        filters: getStoreIdFilter('CPPartsJointOpportunity', storeId),
        order: {
          'CPPartsJointOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 929: {
      query = {
        measures: ['CPPartsGrossOpportunity.parts_gross_opportunity'],
        renewQuery: true,
        timeDimensions: [
          {
            dimension: 'CPPartsGrossOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPPartsGrossOpportunity.rodate'],
        filters: getStoreIdFilter('CPPartsGrossOpportunity', storeId),
        order: {
          'CPPartsGrossOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 921: {
      query = {
        measures: [
          'CPPricingOpportunityByCategory.competitive_opportunity',
          'CPPricingOpportunityByCategory.maintenance_opportunity',
          'CPPricingOpportunityByCategory.repair_opportunity',
          'CPPricingOpportunityByCategory.total'
        ],
        timeDimensions: [
          {
            dimension: 'CPPricingOpportunityByCategory.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPPricingOpportunityByCategory.rodate'],
        renewQuery: true,
        filters: getStoreIdFilter('CPPricingOpportunityByCategory', storeId),
        order: {
          'CPPricingOpportunityByCategory.rodate': 'desc'
        }
      };
      return query;
    }
    case 924: {
      query = {
        measures: ['CPTotalPricingOpportunity.total'],
        timeDimensions: [
          {
            dimension: 'CPTotalPricingOpportunity.rodate',
            dateRange: getDetailedGraphDateRange(realm, 'opportunity_chart')
          }
        ],
        dimensions: ['CPTotalPricingOpportunity.rodate'],
        renewQuery: true,
        filters: getStoreIdFilter('CPTotalPricingOpportunity', storeId),
        order: {
          'CPTotalPricingOpportunity.rodate': 'desc'
        }
      };
      return query;
    }
    case 1334: {
      query = {
        measures: [
          'PartsMarkupRepairCompetitive.competitiverepair_currentyear',
          'PartsMarkupRepairCompetitive.competitiverepair_previousyear',
          'PartsMarkupRepairCompetitive.competitiverepair_pastyear'
        ],
        timeDimensions: [
          {
            dimension: 'PartsMarkupRepairCompetitive.closeddate'
          }
        ],
        dimensions: ['PartsMarkupRepairCompetitive.closeddate'],
        filters: getStoreIdFilter('PartsMarkupRepairCompetitive', storeId),
        order: {
          'PartsMarkupRepairCompetitive.closeddate': 'asc'
        }
      };
      return query;
    }
    case 1354: {
      query = {
        measures: [
          'CPMultiLineROCount.mileage_blw_60K',
          'CPMultiLineROCount.mileage_abv_60K',
          'CPMultiLineROCount.multi_job_count'
        ],
        timeDimensions: [
          {
            dimension: 'CPMultiLineROCount.rodate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPMultiLineROCount.rodate'],
        filters: getQueryFilter(filters, 'CPMultiLineROCount', storeId),
        order: {
          'CPMultiLineROCount.rodate': 'desc'
        }
      };
      return query;
    }
    case 1355: {
      query = {
        measures: [
          'CPMultiLineROCountPercentage.mileage_blw_60K_perc',
          'CPMultiLineROCountPercentage.mileage_abv_60K_perc',
          'CPMultiLineROCountPercentage.percentage'
        ],
        timeDimensions: [
          {
            dimension: 'CPMultiLineROCountPercentage.closeddate',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['CPMultiLineROCountPercentage.closeddate'],
        filters: getStoreIdFilter('CPMultiLineROCountPercentage', storeId),
        order: {
          'CPMultiLineROCountPercentage.closeddate': 'desc'
        }
      };
      return query;
    }
    case 1357: {
      query = {
        measures: [
          'AverageDaysOpenByPaytypes.avg_working_days_customerpay',
          'AverageDaysOpenByPaytypes.avg_working_days_extended',
          'AverageDaysOpenByPaytypes.avg_working_days_internal',
          'AverageDaysOpenByPaytypes.avg_working_days_maintenance',
          'AverageDaysOpenByPaytypes.avg_working_days_warranty',
          'AverageDaysOpenByPaytypes.avg_working_days_factorysc'
        ],
        timeDimensions: [
          {
            dimension: 'AverageDaysOpenByPaytypes.ro_date',
            dateRange: getDetailedGraphDateRange(realm)
          }
        ],
        dimensions: ['AverageDaysOpenByPaytypes.ro_date'],
        filters: getStoreIdFilter('AverageDaysOpenByPaytypes', storeId),
        order: {
          'AverageDaysOpenByPaytypes.ro_date': 'desc'
        }
      };
      return query;
    }
  }
}
