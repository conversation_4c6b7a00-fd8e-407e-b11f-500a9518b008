import { Alert } from '@material-ui/lab';
import { logDOM } from '@testing-library/react';
import { getDbdName, getChartParentId } from 'src/utils/Utils';
var lodash = require('lodash');
export function myCustomSumAggregate(
  rowData,
  chartId,
  menuOpcodesArr,
  mpiOpcodes,
  filterValues,
  allData
) {
  var values;
  if (typeof filterValues != 'undefined') {
    values = rowData;
  } else {
    values = allData;
  }

  console.log('filterValues==pp', values);
  var LbrSale = 0;
  var LbrCost = 0;
  var LbrHours = 0;
  var LbrHoursAll = 0;

  var PrtCost = 0;
  var PrtSale = 0;
  var LbrGP = 0;
  var PrtGP = 0;
  var PrtGPPerc = 0;
  var LbrGPPerc = 0;
  var SHWarranty = 0;
  var LSWarranty = 0;
  var LCWarranty = 0;
  var GPWarranty = 0;
  var SHFactory = 0;
  var GPPercWarranty = 0;
  var LbrSaleCombined = 0;
  var LbrCostCombined = 0;
  var GPCombined = 0;
  var LsSumCombined = 0;
  var LsGrossSumCombined = 0;
  var LaborPercetSumCombined = 0;
  var ELR = 0;
  var ELRRepairCompet = 0;
  var LSInternal = 0;
  var LCInternal = 0;
  var SHInternal = 0;
  var GPInternal = 0;
  var LSCombined = 0;
  var ElrRepair = 0;
  var SaleRepair = 0;
  var HoursRepair = 0;
  var SaleMaintenance = 0;
  var SaleMaintenance1 = 0;
  var CostMaintenance = 0;
  var HoursMaintenance = 0;
  var ElrMaintenance = 0;
  var SaleCompetitive = 0;
  var SaleShop = 0;
  var SHMaintenance1 = 0;
  var SaleExtended = 0;
  var CostExtended = 0;
  var SaleFactory = 0;
  var CostFactory = 0;
  var SHExtended = 0;
  var GPMaintenanceP = 0;
  var GPExtended = 0;
  var GPFactor = 0;
  var HoursCompetitive = 0;
  var ElrCompetitive = 0;
  var ElrRepairCompetitive = 0;
  var HoursRepairCompetitive = 0;
  var SaleRepairCompetitive = 0;

  var SaleCompMaint = 0;
  var HoursMaintCompet = 0;
  var ElrCompMaint = 0;
  var SaleAllCategories = 0;
  var SaleAllCategoriesIncludeShop = 0;
  var HoursAllCategories = 0;
  var ElrAllCategories = 0;
  var LbrSaleRO = 0;
  var LbrTechHours = 0;
  var lbrSaleRC = 0;
  var lbrSoldhrsRC = 0;
  var Markup = 0;
  var CostRepair = 0;
  var CostMaintenances = 0;
  var CostCompetitive = 0;
  var CostShop = 0;
  var lbrlinecodeArrR = [];
  var lbrlinecodeArr = [];
  var lbrlinecodeArrCO = [];
  var lbrlinecodeArrM = [];
  var lbrlinecodeArrS = [];
  var ronumberArrR = [];
  var ronumberArrM = [];
  var ronumberArrC = [];
  var ronumberArrS = [];
  var ronumberArrAll = [];
  var ronumberArrPrtsHours = [];
  var ronumberArrMPVI = [];
  var mpiCountArr = [];
  var menuCountArr = [];
  var jobCountAddOn = [];
  var jobCountAddOnR = [];
  var jobCountAddOnC = [];
  var jobCountAddOnM = [];
  var jobCountAddOnS = [];
  var jobCountAddOnPerRo = [];
  var jobCountNonAddOn = [];
  const ronumberSetAll = new Set();
  const ronumberSetR = new Set();
  const ronumberSetM = new Set();
  const ronumberSetC = new Set();
  const ronumberSetS = new Set();
  const ronumberSetMPI = new Set();
  const ronumberSetMenu = new Set();
  const ronumberSetMPICalc = new Set();
  const ronumberSetPrtsHours = new Set();
  var PSWarranty = 0;
  var PCWarranty = 0;
  var PSInternal = 0;
  var PCInternal = 0;
  var PCSumComp = 0;
  var PSSumComp = 0;
  var PrtGpWar = 0;
  var prtGpE = 0;
  var PrtGpMP = 0;
  var PrtGpInt = 0;
  var PrtGpCombin = 0;
  var MarkupR = 0;
  var MarkupM = 0;
  var MarkupC = 0;
  var MarkupCmb = 0;
  var PrtsaleR = 0;
  var PrtsaleM = 0;
  var PrtsaleComp = 0;
  var PrtcostR = 0;
  var PrtcostM = 0;
  var PrtcostComp = 0;
  var laborHrsPerJob = 0;
  var prtsHrsPerJob = 0;
  var SHMaintenance = 0;
  var SHExtended = 0;
  var SHAll = 0;
  var SHByCategory = 0;
  var SHByCategoryC = 0;
  var SHByCategoryE = 0;
  var SHByCategoryI = 0;
  var SHByCategoryW = 0;
  var SHByCategoryM = 0;
  var SHByCategoryF = 0;
  var totalJobCount = [];
  var techHours = 0;
  var flatrateHours = 0;
  var LbrDiscount = 0;
  var TotalLbrDiscount = 0;
  var PrtsDiscount = 0;
  var TotalPrtsDiscount = 0;
  var percLbrDiscount = 0;
  var percPartsDiscount = 0;
  var lbrSoldhrsAddon = 0;
  var lbrSoldhrsNonAddon = 0;
  var lbrSaleAddOn = 0;
  var prtSaleAddOn = 0;
  var totalSaleAddOn = 0;
  var lbrSaleRAddOn = 0;
  var lbrSaleCAddOn = 0;
  var lbrSaleMAddOn = 0;
  var lbrSaleSAddOn = 0;
  var prtSaleRAddOn = 0;
  var prtSaleSAddOn = 0;
  var prtSaleMAddOn = 0;
  var prtSaleCAddOn = 0;
  var lbrhrsR = 0;
  var lbrhrsM = 0;
  var lbrhrsC = 0;
  var lbrhrsS = 0;
  var prtCostRAddOn = 0;
  var prtCostMAddOn = 0;
  var prtCostCAddOn = 0;
  var prtCostSAddOn = 0;
  var prtSaleNonAddOn = 0;
  var lbrSaleNonAddOn = 0;
  var totalSaleAddOn = 0;
  var totalLbrSaleAll = 0;
  var totalPrtSaleAll = 0;
  var PrtsDiscount = 0;
  var TotalPrtsDiscount = 0;
  var PSMaintenancePrt = 0;
  var PCMaintenancePrt = 0;
  var PSExtendedPrt = 0;
  var PCExtendedPrt = 0;
  var lbrsaleC = 0;
  var lbrcostC = 0;
  var ElrCombined = 0;
  var ElrCustomer = 0;
  var ElrWarranty = 0;
  var ElrInternal = 0;
  var ElrMaintenance = 0;
  var ElrExtended = 0;
  var ElrFactory = 0;
  var shopSuppliesC = 0;
  var shopSuppliesI = 0;
  var shopSuppliesCombined = 0;
  var shopSuppliesW = 0;
  const ronumberSetRS = new Set();
  const ronumberSetMS = new Set();
  const ronumberSetCS = new Set();
  const ronumberSetSS = new Set();
  const techNoSet = new Set();
  var ronumberArrRS = [];
  var ronumberArrMS = [];
  var ronumberArrCS = [];
  var ronumberArrSS = [];
  var techArr = [];
  var PSMaintenanceP = 0;
  var PCMaintenanceP = 0;
  var PSExtended = 0;
  var PCExtended = 0;
  var SHFactory = 0;
  var PSFactory = 0;
  var PCFactory = 0;
  var PrtGpF = 0;
  var SoldHoursTech = 0;
  var AvailableHours = 0;
  var AvgHrsTech = 0;
  var JobCntCombined = [];
  var JobCntCustomer = [];
  var JobCntWarranty = [];
  var JobCntInteranl = [];
  var JobCntMaintain = [];
  var JobCntExtended = [];
  var JobCntFactory = [];
  var prtsMarkupCombined = 0;
  var prtsMarkupCustomer = 0;
  var prtsMarkupWarranty = 0;
  var prtsMarkupInteranl = 0;
  var prtsMarkupMaintain = 0;
  var prtsMarkupExtended = 0;
  var prtsMarkupFactory = 0;
  var TotalSale = 0;
  var PartsSale = 0;
  var PrtSaleRC = 0;
  var PrtCostRC = 0;
  var menuTotalRoCountData = [];
  var TotalDiscountVal = 0;
  var PercPrtsDiscount = 0;

  let Params = {
    laborSaleSum: '',
    LsWarranty: '',
    LsInternal: '',
    saleRepair: '',
    saleMaintenance: '',
    saleMaintenance1: '',
    costMaintenance: '',
    saleCompetitive: '',
    saleRepairCompetitive: '',
    saleCompetMaint: '',
    saleCompetitive: '',
    saleAllCategories: '',
    saleShopSupplies: '',
    lbrSaleperRoR: '',
    lbrSaleperRoM: '',
    lbrSaleperRoC: '',
    lbrSaleperRoS: '',
    lbrSaleperRoAll: '',
    lbrHoursPerRO: '',
    prtsHoursPerRO: '',
    prtsHours: '',
    prtsHrsRoCount: '',
    laborCostSum: '',
    LcWarranty: '',
    LcInternal: '',
    elrRepair: '',
    elrMaintenance: '',
    elrCompetitive: '',
    elrRepairAddOn: '',
    elrMaintenanceAddOn: '',
    elrCompetitiveAddOns: '',
    elrShopAddOn: '',
    elrRepairCompetitive: '',
    elrCompMaint: '',
    elrAllCategories: '',
    laborHoursSum: '',
    ShWarranty: '',
    ShInternal: '',
    lbrTechHr: '',
    hoursRepair: '',
    hoursMaintenance: '',
    hoursCompetitive: '',
    hoursRepairCompetitive: '',
    hoursAllCategories: '',
    jobCountR: '',
    jobCountM: '',
    jobCountCO: '',
    jobCountS: '',
    lbrHrsPerRoR: '',
    lbrHrsPerRoM: '',
    lbrHrsPerRoC: '',
    lbrHrsPerRoAll: '',
    lbrHoursPerJob: '',
    lbrHoursAll: '',
    roCountAllCat: '',
    partsSaleSum: '',
    PsWarranty: '',
    PsInternal: '',
    partsCostSum: '',
    PcWarranty: '',
    PcInternal: '',
    laborGP: '',
    laborGPW: '',
    laborGPI: '',
    laborGPCombined: '',
    laborSaleCombined: '',
    laborCostCombined: '',
    partsMarkup: '',
    partsGP: '',
    laborGPPerc: '',
    partsGPPerc: '',
    laborSaleSumCombined: '',
    laborPercetSumCombined: '',
    lsGrossSumCombined: '',
    elr: '',
    LsCombined: '',
    lbrSaleRepairCompet: '',
    lbrHrsRepairCompet: '',
    elrRepairCompet: '',
    psSumComp: '',
    pcSumComp: '',
    prtGpWar: '',
    prtGpInt: '',
    prtGpCombin: '',
    prtsaleR: '',
    prtsaleM: '',
    prtsaleComp: '',
    prtcostR: '',
    prtcostM: '',
    prtcostComp: '',
    partsMarkupR: '',
    partsMarkupM: '',
    partsMarkupC: '',
    partsMarkupCmb: '',
    partsToLaborRatio: '',
    prtsToLbrRatioR: '',
    prtsToLbrRatioM: '',
    prtsToLbrRatioC: '',
    sHMaintenance: '',
    SHExtended: '',
    sHAll: '',
    sHByCategoryC: '',
    sHByCategoryE: '',
    sHByCategoryI: '',
    sHByCategoryW: '',
    sHByCategoryM: '',
    sHByCategoryF: '',
    deltaShFh: '',
    deltaThFh: '',
    techHours: '',
    flatrateHours: '',
    roCountAll: '',
    lbrDiscount: '',
    totallbrDiscount: '',
    roCountShop: '',
    RoCountAll: '',
    totalprtsDiscount: '',
    prtsDiscount: '',
    jobCountAddOn: '',
    jobCountNonAddOn: '',
    jobCountAddOnR: '',
    jobCountAddOnC: '',
    jobCountAddOnM: '',
    jobCountAddOnS: '',
    jobCountAddOnPerRo: '',
    ROCountPercAddOn: '',
    distinctROCountNonAddOn: '',
    distinctROCountAddOn: '',
    lbrSoldhrsNonAddon: '',
    lbrSoldhrsNonAddon: '',
    prtSaleAddOn: '',
    lbrSaleAddOn: '',
    lbrSaleRAddOn: '',
    lbrSaleCAddOn: '',
    lbrSaleMAddOn: '',
    lbrSaleSAddOn: '',
    prtSaleRAddOn: '',
    prtSaleCAddOn: '',
    prtSaleMAddOn: '',
    prtSaleSAddOn: '',
    MarkupR: '',
    MarkupS: '',
    MarkupC: '',
    MarkupM: '',
    addOnRevenueperc: '',
    nonAddOnRevenueperc: '',
    discountROPerc: '',
    ronumberArrAll: '',
    jobCountAll: '',
    ronumberArrMPVI: '',
    totalLbrSaleP: '',
    totalPrtSaleP: '',
    shMaintenance1: '',
    saleExtended: '',
    costExtended: '',
    shExtended: '',
    laborGPMP: '',
    laborGPE: '',
    laborGPF: '',
    PsMaintenanceP: '',
    PsExtended: '',
    PcMaintenanceP: '',
    PcExtended: '',
    prtGpMP: '',
    prtGpE: '',
    saleFactory: '',
    costFactory: '',
    shFactory: '',
    PsFactory: '',
    PcFactory: '',
    prtGpF: '',
    costRepair: '',
    costMaintenances: '',
    costCompetitive: '',
    costShop: '',
    SHByCategory: '',
    soldHoursTech: '',
    availableHours: '',
    avgHrsTech: '',
    techCount: '',
    avgSalesPerTech: '',
    totalSaleTech: '',
    ElrCombined: '',
    ElrCustomer: '',
    ElrWarranty: '',
    ElrInternal: '',
    ElrMaintenance: '',
    ElrExtended: '',
    ElrFactory: '',
    lbrSaleC: '',
    lbrCostC: '',
    JobCntCombined: '',
    JobCntCustomer: '',
    JobCntWarranty: '',
    JobCntInteranl: '',
    JobCntMaintain: '',
    JobCntExtended: '',
    JobCntFactory: '',
    prtsMarkupCombined: '',
    prtsMarkupCustomer: '',
    prtsMarkupWarranty: '',
    prtsMarkupInteranl: '',
    prtsMarkupMaintain: '',
    prtsMarkupExtended: '',
    prtsMarkupFactory: '',
    LbrSale: '',
    partsSale: '',
    shopSuppliesC: '',
    shopSuppliesCombined: '',
    shopSuppliesW: '',
    shopSuppliesI: '',
    mpiCount: '',
    totalROCount: '',
    mpiPercentage: '',
    menuPercentage: '',
    menuCount: '',
    partsMarkupRC: '',
    PrtSaleRC: '',
    PrtCostRC: '',
    menuTotalRoCountData: '',
    TotalDiscountVal: '',
    PercPrtsDiscount: ''
  };
  if (
    chartId == 1099 ||
    chartId == 1100 ||
    chartId == 1101 ||
    chartId == 1102 ||
    chartId == 1105 ||
    chartId == 1106 ||
    chartId == 1107 ||
    chartId == 1104 ||
    chartId == 1108 ||
    chartId == 1103 ||
    chartId == 1109 ||
    chartId == 1110 ||
    chartId == 1116 ||
    chartId == 1117 ||
    chartId == 1118 ||
    chartId == 1119 ||
    chartId == 1120 ||
    chartId == 1121 ||
    chartId == 1122 ||
    chartId == 1079 ||
    chartId == 1316 ||
    chartId == 1317
  ) {
    if (chartId == 1317) {
      allData.forEach(function(value) {
        if (
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          menuTotalRoCountData.push(value.ronumber);
        }
      });
    }
    if (chartId == 1316) {
      allData.forEach(function(value) {
        if (
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          menuTotalRoCountData.push(value.ronumber);
        }
      });
    }

    values.forEach(function(value) {
      /*var menuOpcodesArr = [
        '60K',
        '100KT8',
        '100KC4',
        '100K6',
        '45KT',
        '100K8',
        '100K4',
        '15K',
        '30K',
        '45K',
        '30KA',
        '90K',
        '100KT6',
        '100KT4'
      ];*/
      if (value.linaddonflag == 'Y' && value.paytypegroup == 'C') {
        ronumberArrC.push(value.ronumber);
        jobCountAddOn.push(value.linaddonflag);

        lbrSoldhrsAddon += value.lbrsoldhours;
        lbrSaleAddOn += value.lbrsale;
        prtSaleAddOn += value.prtextendedsale;
      }

      if (value.linaddonflag == 'N' && value.paytypegroup == 'C') {
        ronumberArrS.push(value.ronumber);
        jobCountNonAddOn.push(value.linaddonflag);
        lbrSoldhrsNonAddon += value.lbrsoldhours;
        lbrSaleNonAddOn += value.lbrsale;
        prtSaleNonAddOn += value.prtextendedsale;
      }
      //  if (value.lbropcode == 'MPVI') {

      // if (chartId == 1316 && mpiOpcodes.includes(value.lbropcode)) {
      //   mpiCountArr.push(value.ronumber);
      // }
      if (chartId == 1316) {
        if (mpiOpcodes.includes(value.lbropcode)) {
          mpiCountArr.push(value.ronumber);
        }
      }
      if (chartId == 1317) {
        if (menuOpcodesArr.includes(value.lbropcode)) {
          menuCountArr.push(value.ronumber);
        }
      }
      if (value.paytypegroup == 'C') {
        ronumberArrAll.push(value.ronumber);
        totalLbrSaleAll += value.lbrsale;
        totalPrtSaleAll += value.prtextendedsale;
      }
      ronumberArrMPVI.push(value.ronumber);
      CostMaintenance += value.lbrcost;

      if (
        value.opcategory == 'REPAIR' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnR.push(value.linaddonflag);
        lbrSaleRAddOn += value.lbrsale;
        prtSaleRAddOn += value.prtextendedsale;
        lbrhrsR += value.lbrsoldhours;
        prtCostRAddOn += value.prtextendedcost;
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnM.push(value.linaddonflag);
        lbrSaleMAddOn += value.lbrsale;
        prtSaleMAddOn += value.prtextendedsale;
        lbrhrsM += value.lbrsoldhours;
        prtCostMAddOn += value.prtextendedcost;
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnC.push(value.linaddonflag);
        lbrSaleCAddOn += value.lbrsale;
        prtSaleCAddOn += value.prtextendedsale;
        lbrhrsC += value.lbrsoldhours;
        prtCostCAddOn += value.prtextendedcost;
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnS.push(value.linaddonflag);
        lbrSaleSAddOn += value.lbrsale;
        prtSaleSAddOn += value.prtextendedsale;
        lbrhrsS += value.lbrsoldhours;
        prtCostSAddOn += value.prtextendedcost;
      }
    });

    ronumberArrC.map(obj => {
      if (obj) {
        ronumberSetC.add(obj);
      }
    });
    ronumberArrS.map(obj => {
      if (obj) {
        ronumberSetS.add(obj);
      }
    });
    ronumberArrAll.map(obj => {
      if (obj) {
        ronumberSetAll.add(obj);
      }
    });
    if (chartId == 1317 || chartId == 1316) {
      menuTotalRoCountData.map(obj => {
        if (obj) {
          ronumberSetMPICalc.add(obj);
        }
      });
    } else {
      ronumberArrMPVI.map(obj => {
        if (obj) {
          ronumberSetMPICalc.add(obj);
        }
      });
    }

    mpiCountArr.map(obj => {
      if (obj) {
        ronumberSetMPI.add(obj);
      }
    });

    menuCountArr.map(obj => {
      if (obj) {
        ronumberSetMenu.add(obj);
      }
    });
    var distinctROCount = ronumberSetC.size;

    var distinctROCountNonAddOn = ronumberSetAll.size - distinctROCount;
    var totalRevenueAddOn = lbrSaleAddOn + prtSaleAddOn;
    var totalRevenueNonAddOn = lbrSaleNonAddOn + prtSaleNonAddOn;
    var addOnRevenueperc =
      (totalRevenueAddOn / (totalLbrSaleAll + totalPrtSaleAll)) * 100;
    var nonAddOnRevenueperc =
      (totalRevenueNonAddOn / (totalLbrSaleAll + totalPrtSaleAll)) * 100;
    var ROCountPercAddOn = (distinctROCount / ronumberSetAll.size) * 100;
    var jobCountAddOnPerRo = jobCountAddOn.length / distinctROCount;
    var elrRepair = lbrSaleRAddOn / lbrhrsR;
    var elrCompetitive = lbrSaleCAddOn / lbrhrsC;
    var elrMaintenance = lbrSaleMAddOn / lbrhrsM;
    var elrShop = lbrSaleSAddOn / lbrhrsS;
    var MarkupR = prtSaleRAddOn / prtCostRAddOn;
    var MarkupM = prtSaleMAddOn / prtCostMAddOn;
    var MarkupC = prtSaleCAddOn / prtCostCAddOn;
    var MarkupS = prtSaleSAddOn / prtCostSAddOn;
    var menuPenetrationPerc = (
      (ronumberSetMenu.size / ronumberSetMPICalc.size) *
      100
    ).toFixed(2);
    var mpiROCountPercentage = (
      (ronumberSetMPI.size / ronumberSetMPICalc.size) *
      100
    ).toFixed(2);

    Params.mpiCount = ronumberSetMPI.size
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    Params.totalROCount = ronumberSetMPICalc.size
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    console.log('ronumberSetMPI===', Params.mpiCount, Params.totalROCount);
    Params.mpiPercentage = mpiROCountPercentage + '%';

    Params.menuCount = ronumberSetMenu.size;

    Params.menuPercentage = menuPenetrationPerc + '%';
    Params.ronumberArrAll = ronumberArrAll.length;
    Params.jobCountNonAddOn =
      Math.round((jobCountNonAddOn.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOn =
      Math.round((jobCountAddOn.length + Number.EPSILON) * 100) / 100;

    Params.jobCountAddOnPerRo =
      Math.round((jobCountAddOnPerRo + Number.EPSILON) * 100) / 100;
    Params.ROCountPercAddOn =
      Math.round((ROCountPercAddOn + Number.EPSILON) * 100) / 100;

    Params.distinctROCountNonAddOn =
      Math.round((distinctROCountNonAddOn + Number.EPSILON) * 100) / 100;
    Params.distinctROCountAddOn =
      Math.round((distinctROCount + Number.EPSILON) * 100) / 100;
    Params.lbrSoldhrsNonAddon =
      Math.round((lbrSoldhrsNonAddon + Number.EPSILON) * 100) / 100;
    Params.lbrSoldhrsAddon =
      Math.round((lbrSoldhrsAddon + Number.EPSILON) * 100) / 100;
    Params.lbrSaleAddOn =
      '$' +
      (
        Math.round((lbrSaleAddOn + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.prtSaleAddOn =
      '$' +
      (
        Math.round((prtSaleAddOn + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.totalSaleAddOn =
      '$' +
      (
        Math.round((totalRevenueAddOn + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.jobCountAddOnR =
      Math.round((jobCountAddOnR.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOnC =
      Math.round((jobCountAddOnC.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOnM =
      Math.round((jobCountAddOnM.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOnS =
      Math.round((jobCountAddOnS.length + Number.EPSILON) * 100) / 100;
    Params.lbrSaleRAddOn =
      '$' + lbrSaleRAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.lbrSaleCAddOn =
      '$' + lbrSaleCAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.lbrSaleMAddOn =
      '$' + lbrSaleMAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.lbrSaleSAddOn =
      '$' + lbrSaleSAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleRAddOn =
      '$' + prtSaleRAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleCAddOn =
      '$' + prtSaleCAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleMAddOn =
      '$' + prtSaleMAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleSAddOn =
      '$' + prtSaleSAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.elrRepairAddOn = Number(elrRepair)
      ? '$' + Math.round((elrRepair + Number.EPSILON) * 100) / 100
      : '$0.00';
    Params.elrCompetitiveAddOns = Number(elrCompetitive)
      ? '$' + Math.round((elrCompetitive + Number.EPSILON) * 100) / 100
      : '$0.00';
    Params.elrMaintenanceAddOn = Number(elrMaintenance)
      ? '$' + Math.round((elrMaintenance + Number.EPSILON) * 100) / 100
      : '$0.00';
    Params.elrShopAddOn = Number(elrShop)
      ? '$' + Math.round((elrShop + Number.EPSILON) * 100) / 100
      : '$0.00';

    Params.MarkupR = Number(MarkupR) ? MarkupR.toFixed(4) : '0.0000';
    Params.MarkupM = Number(MarkupM) ? MarkupM.toFixed(4) : '0.0000';
    Params.MarkupC = Number(MarkupC) ? MarkupC.toFixed(4) : '0.0000';
    Params.MarkupS = Number(MarkupS) ? MarkupS.toFixed(4) : '0.0000';
    Params.addOnRevenueperc =
      Math.round((addOnRevenueperc + Number.EPSILON) * 100) / 100 + '%';
    Params.nonAddOnRevenueperc =
      Math.round((nonAddOnRevenueperc + Number.EPSILON) * 100) / 100 + '%';
  }

  if (chartId == 1174 || chartId == 1175) {
    values.forEach(function(value) {
      totalLbrSaleAll += value.lbrsale;
      totalPrtSaleAll += value.prtextendedsale;
      SoldHoursTech += value.totalsoldhours
        ? value.totalsoldhours
        : value.soldhours;
      AvailableHours += value.totalavailablehrs
        ? value.totalavailablehrs
        : value.availablehrs;
      techArr.push(value.lbrtechno);
    });
  }

  var TotalSaleTech = totalLbrSaleAll + totalPrtSaleAll;
  AvgHrsTech = SoldHoursTech / AvailableHours;

  techArr.map(obj => {
    if (obj) {
      techNoSet.add(obj);
    }
  });
  var distinctTechCount = techNoSet.size;
  Params.totalSaleTech =
    '$' +
    (Math.round((TotalSaleTech + Number.EPSILON) * 100) / 100).toLocaleString();
  var AvgSalesPerTech = TotalSaleTech / distinctTechCount;
  Params.avgSalesPerTech =
    '$' +
    (
      Math.round((AvgSalesPerTech + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.soldHoursTech =
    Math.round((SoldHoursTech + Number.EPSILON) * 100) / 100;
  Params.availableHours =
    Math.round((AvailableHours + Number.EPSILON) * 100) / 100;
  Params.avgHrsTech = Math.round((AvgHrsTech + Number.EPSILON) * 100) / 100;
  Params.techCount = (
    Math.round((distinctTechCount + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  if (
    chartId != 988 &&
    chartId != 987 &&
    chartId != 986 &&
    chartId != 1066 &&
    chartId != 937 &&
    chartId != 1128 &&
    chartId != 1018 &&
    chartId != 1019 &&
    chartId != 1020 &&
    chartId != 1085 &&
    chartId != 1086 &&
    chartId != 1087 &&
    chartId != 1088 &&
    chartId != 1084 &&
    chartId != 1083 &&
    chartId != 1076 &&
    chartId != 1077 &&
    chartId != 1078 &&
    chartId != 1098 &&
    chartId != 1356 &&
    chartId != 1044 &&
    chartId != 949 &&
    chartId != 936 &&
    chartId != 1129 &&
    chartId != 1130 &&
    chartId != 1131 &&
    chartId != 1132 &&
    chartId != 1079 &&
    chartId != 1318
  ) {
    var dashboardName = getDbdName(chartId);
    var parentId = getChartParentId(chartId);
    values.forEach(function(value) {
      if (
        (((dashboardName == 'CP Overview' ||
          dashboardName == 'CP Labor Overview' ||
          dashboardName == 'CP Parts Overview') &&
          parentId == null) ||
          parentId == 942 ||
          parentId == 939 ||
          parentId == 940 ||
          chartId == 930 ||
          chartId == 936 ||
          chartId == 925 ||
          chartId == 1124 ||
          chartId == 1126) &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')
      ) {
        LbrSale += parseFloat(value.lbrsale);
        LbrDiscount += parseFloat(value.apportionedlbrdiscount);
        TotalLbrDiscount += value.apportionedlbrdiscount
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
      } else if (value.paytypegroup == 'C') {
        LbrSale += parseFloat(value.lbrsale);
        LbrDiscount += parseFloat(value.apportionedlbrdiscount);
        TotalLbrDiscount += value.apportionedlbrdiscount
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
      } else if (value.paytypegroup == 'W') {
        LSWarranty += parseFloat(value.lbrsale);
      } else if (value.paytypegroup == 'I') {
        LSInternal += parseFloat(value.lbrsale);
      } else if (value.paytypegroup == 'M') {
        SaleMaintenance1 += parseFloat(value.lbrsale);
        CostMaintenance += parseFloat(value.lbrcost);
        SHMaintenance1 += parseFloat(value.lbrsoldhours);
      } else if (value.paytypegroup == 'E') {
        SaleExtended += parseFloat(value.lbrsale);
        CostExtended += parseFloat(value.lbrcost);
        SHExtended += parseFloat(value.lbrsoldhours);
      } else if (value.paytypegroup == 'F') {
        SaleFactory += parseFloat(value.lbrsale);
        CostFactory += parseFloat(value.lbrcost);
        SHFactory += parseFloat(value.lbrsoldhours);
      }
    });
  } else {
    var dashboardName = getDbdName(chartId);
    var parentId = getChartParentId(chartId);
    values.forEach(function(value) {
      if (
        (((dashboardName == 'CP Overview' ||
          dashboardName == 'CP Labor Overview' ||
          dashboardName == 'CP Parts Overview') &&
          parentId == null) ||
          chartId == 925 ||
          chartId == 1124 ||
          chartId == 1126) &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M' ||
          value.partspaytypegroup == 'C' ||
          value.partspaytypegroup == 'E' ||
          value.partspaytypegroup == 'M')
      ) {
        if (
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrAll.push(value.ronumber);
        }
        SaleAllCategoriesIncludeShop += parseFloat(value.lbrsale);
        LbrSale += parseFloat(value.lbrsale);
        laborHrsPerJob += parseFloat(value.lbrsoldhours);
        if (value.prtextendedsale != 0) {
          prtsHrsPerJob += parseFloat(value.lbrsoldhours);
          ronumberArrPrtsHours.push(value.ronumber);
        }
        totalJobCount.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'C') {
        if (
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrAll.push(value.ronumber);
        }
        SaleAllCategoriesIncludeShop += parseFloat(value.lbrsale);
        LbrSale += parseFloat(value.lbrsale);
        laborHrsPerJob += parseFloat(value.lbrsoldhours);
        if (value.prtextendedsale != 0) {
          prtsHrsPerJob += parseFloat(value.lbrsoldhours);
          ronumberArrPrtsHours.push(value.ronumber);
        }
        totalJobCount.push(value.lbrlinecode);
      }
      if (
        value.opcategory == 'REPAIR' &&
        (((chartId == 936 || chartId == 1098) &&
          (value.paytypegroup == 'C' ||
            value.paytypegroup == 'E' ||
            value.paytypegroup == 'M')) ||
          (chartId != 936 && chartId != 1098 && value.paytypegroup == 'C'))
      ) {
        SaleRepair += parseFloat(value.lbrsale);
        CostRepair += parseFloat(value.lbrcost);
        ronumberArrR.push(value.ronumber);
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        (((chartId == 936 || chartId == 1356) &&
          (value.paytypegroup == 'C' ||
            value.paytypegroup == 'E' ||
            value.paytypegroup == 'M')) ||
          (chartId != 936 && chartId != 1356 && value.paytypegroup == 'C'))
      ) {
        SaleMaintenance += parseFloat(value.lbrsale);
        CostMaintenances += parseFloat(value.lbrcost);
        ronumberArrM.push(value.ronumber);
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        (((chartId == 936 || chartId == 1356) &&
          (value.paytypegroup == 'C' ||
            value.paytypegroup == 'E' ||
            value.paytypegroup == 'M')) ||
          (chartId != 936 && chartId != 1356 && value.paytypegroup == 'C'))
      ) {
        SaleCompetitive += parseFloat(value.lbrsale);
        CostCompetitive += parseFloat(value.lbrcost);
        ronumberArrC.push(value.ronumber);
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        value.paytypegroup == 'C'
      ) {
        SaleShop += parseFloat(value.lbrsale);
        CostShop += parseFloat(value.lbrcost);
        ronumberArrS.push(value.ronumber);
      }
      if (
        value.opcategory == 'REPAIR' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrRS.push(value.ronumber);
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrMS.push(value.ronumber);
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrCS.push(value.ronumber);
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrSS.push(value.ronumber);
      }
    });
    SaleRepairCompetitive = SaleRepair;
    SaleCompMaint = SaleCompetitive + SaleMaintenance;

    var lbrHoursPerJob = laborHrsPerJob / totalJobCount.length;
    ronumberArrR.map(obj => {
      if (obj) {
        ronumberSetR.add(obj);
      }
    });
    ronumberArrM.map(obj => {
      if (obj) {
        ronumberSetM.add(obj);
      }
    });
    ronumberArrC.map(obj => {
      if (obj) {
        ronumberSetC.add(obj);
      }
    });
    ronumberArrS.map(obj => {
      if (obj) {
        ronumberSetS.add(obj);
      }
    });
    ronumberArrAll.map(obj => {
      if (obj) {
        ronumberSetAll.add(obj);
      }
    });
    ronumberArrPrtsHours.map(obj => {
      if (obj) {
        ronumberSetPrtsHours.add(obj);
      }
    });
    ronumberArrRS.map(obj => {
      if (obj) {
        ronumberSetRS.add(obj);
      }
    });
    ronumberArrMS.map(obj => {
      if (obj) {
        ronumberSetMS.add(obj);
      }
    });
    ronumberArrCS.map(obj => {
      if (obj) {
        ronumberSetCS.add(obj);
      }
    });
    ronumberArrSS.map(obj => {
      if (obj) {
        ronumberSetSS.add(obj);
      }
    });
    SaleAllCategories = SaleRepair + SaleCompetitive + SaleMaintenance;
  }

  var roCountR = ronumberSetR.size;
  var roCountM = ronumberSetM.size;
  var roCountC = ronumberSetC.size;
  var roCountS = ronumberSetS.size;
  var roCountRS = ronumberSetRS.size;
  var roCountMS = ronumberSetMS.size;
  var roCountCompS = ronumberSetCS.size;
  var roCountSS = ronumberSetSS.size;
  var roCountAll = ronumberSetAll.size;
  var roCountPrtsHours = ronumberSetPrtsHours.size;
  var laborSaleROR = SaleRepair.toFixed(2) / roCountRS;
  var laborSaleROM = SaleMaintenance.toFixed(2) / roCountMS;
  var laborSaleROC = SaleCompetitive.toFixed(2) / roCountCompS;
  var laborSaleROS = SaleShop.toFixed(2) / roCountSS;
  console.log('mmppp===', laborHrsPerJob, roCountAll);
  var lbrHoursPerRO = laborHrsPerJob / roCountAll;
  var prtsHoursPerRO = prtsHrsPerJob / roCountPrtsHours;
  Params.distinctRORS = (
    Math.round((roCountRS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROMS = (
    Math.round((roCountMS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROCompS = (
    Math.round((roCountCompS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROSS = (
    Math.round((roCountSS + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.roCountShop = (
    Math.round((roCountS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.lbrHoursAll = (
    Math.round((laborHrsPerJob + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.roCountAllCat = (
    Math.round((roCountAll + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.laborSale = Math.round((LbrSale + Number.EPSILON) * 100) / 100;
  Params.laborSaleSum =
    '$' + (Math.round((LbrSale + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LsWarranty =
    '$' +
    (Math.round((LSWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LsInternal =
    '$' +
    (Math.round((LSInternal + Number.EPSILON) * 100) / 100).toLocaleString();

  Params.saleRepair =
    '$' +
    (Math.round((SaleRepair + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.saleMaintenance =
    '$' +
    (
      Math.round((SaleMaintenance + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  Params.saleCompetitive =
    '$' +
    (
      Math.round((SaleCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.saleRepairCompetitive =
    '$' +
    (
      Math.round((SaleRepairCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.saleCompetMaint =
    '$' +
    (Math.round((SaleCompMaint + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.saleAllCategories =
    '$' +
    (
      Math.round((SaleAllCategories + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.saleShopSupplies =
    '$' +
    (Math.round((SaleShop + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoR =
    '$' +
    (Math.round((laborSaleROR + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoM =
    '$' +
    (Math.round((laborSaleROM + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoC =
    '$' +
    (Math.round((laborSaleROC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoS =
    '$' +
    (Math.round((laborSaleROS + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrHoursPerRO = (
    Math.round((lbrHoursPerRO + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsHoursPerRO = (
    Math.round((prtsHoursPerRO + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsHours = (
    Math.round((prtsHrsPerJob + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsHrsRoCount = (
    Math.round((roCountPrtsHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.saleMaintenance1 =
    '$' +
    (
      Math.round((SaleMaintenance1 + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.costMaintenance =
    '$' +
    (
      Math.round((CostMaintenance + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.shMaintenance1 = (
    Math.round((SHMaintenance1 + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.saleExtended =
    '$' +
    (Math.round((SaleExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costExtended =
    '$' +
    (Math.round((CostExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.shExtended = (
    Math.round((SHExtended + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.saleFactory =
    '$' +
    (Math.round((SaleFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costFactory =
    '$' +
    (Math.round((CostFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.shFactory = (
    Math.round((SHFactory + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  var dashboardName = getDbdName(chartId);
  var parentId = getChartParentId(chartId);
  values.forEach(function(value) {
    if (
      (((dashboardName == 'CP Overview' ||
        dashboardName == 'CP Labor Overview' ||
        dashboardName == 'CP Parts Overview') &&
        parentId == null) ||
        parentId == 942 ||
        parentId == 939 ||
        parentId == 940 ||
        chartId == 925 ||
        chartId == 1124 ||
        chartId == 1126) &&
      (value.paytypegroup == 'C' ||
        value.paytypegroup == 'E' ||
        value.paytypegroup == 'M')
    ) {
      LbrCost += parseFloat(value.lbrcost);
    } else if (value.paytypegroup == 'C') {
      LbrCost += parseFloat(value.lbrcost);
    } else if (value.paytypegroup == 'W') {
      LCWarranty += parseFloat(value.lbrcost);
    } else if (value.paytypegroup == 'I') {
      LCInternal += parseFloat(value.lbrcost);
    }
  });

  Params.laborCostSum =
    '$' + (Math.round((LbrCost + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LcWarranty =
    '$' +
    (Math.round((LCWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LcInternal =
    '$' +
    (Math.round((LCInternal + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costCompetitive =
    '$' +
    (
      Math.round((CostCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.costMaintenances =
    '$' +
    (
      Math.round((CostMaintenances + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  Params.costRepair =
    '$' +
    (Math.round((CostRepair + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costShop =
    '$' +
    (Math.round((CostShop + Number.EPSILON) * 100) / 100).toLocaleString();
  if (
    chartId != 988 &&
    chartId != 987 &&
    chartId != 986 &&
    chartId != 1066 &&
    chartId != 937 &&
    chartId != 1128 &&
    chartId != 1018 &&
    chartId != 1019 &&
    chartId != 1020 &&
    chartId != 1080 &&
    chartId != 1081 &&
    chartId != 1082 &&
    chartId != 1083 &&
    chartId != 998 &&
    chartId != 1076 &&
    chartId != 1077 &&
    chartId != 1078 &&
    chartId != 937 &&
    chartId != 1128 &&
    chartId != 1098 &&
    chartId != 1356 &&
    chartId != 949 &&
    chartId != 1129 &&
    chartId != 1130 &&
    chartId != 1131 &&
    chartId != 1132 &&
    chartId != 1079
  ) {
    var dashboardName = getDbdName(chartId);
    var parentId = getChartParentId(chartId);
    let filterArr;
    if (chartId == 935) {
      if (!lodash.isEmpty(filterValues)) {
        Object.keys(filterValues).forEach(function(key) {
          if (key == 'opcategory' && filterValues[key].values.length > 0) {
            filterArr = lodash.filter(allData, function(o) {
              return filterValues[key].values.includes(o[key]);
            });
            filterArr.forEach(function(value) {
              LbrHoursAll += parseFloat(value.lbrsoldhours);
            });
          } else if (
            Object.keys(filterValues).length == 1 &&
            Object.keys(filterValues).some(key => key !== 'opcategory')
          ) {
            allData.forEach(function(value) {
              LbrHoursAll += parseFloat(value.lbrsoldhours);
            });
          }
        });
      }
    }
    values.forEach(function(value) {
      //LbrHours1 += parseFloat(value.lbrsoldhours);
      if (
        (((dashboardName == 'CP Overview' ||
          dashboardName == 'CP Labor Overview' ||
          dashboardName == 'CP Parts Overview') &&
          parentId == null) ||
          parentId == 942 ||
          parentId == 939 ||
          parentId == 940 ||
          chartId == 930 ||
          chartId == 936 ||
          chartId == 925 ||
          chartId == 1124 ||
          chartId == 1126) &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')
      ) {
        LbrHours += parseFloat(value.lbrsoldhours);
        if (
          value.lbrsale != 0 ||
          value.lbrsoldhours != 0 ||
          value.prtextendedsale != 0 ||
          value.prtextendedcost != 0
        ) {
          JobCntCustomer.push(value.lbrlinecode);
        }
      } else if (value.paytypegroup == 'C') {
        LbrHours += parseFloat(value.lbrsoldhours);
        LbrTechHours += parseFloat(value.lbrtechhours);
        techHours += parseFloat(value.techhours);
        flatrateHours += parseFloat(value.flatratehours);
        JobCntCustomer.push(value.lbrlinecode);
        if (chartId == 1207) {
          JobCntInteranl.push(value.lbrlinecode);
        }
        if (chartId == 1200) {
          SHInternal += parseFloat(value.lbrsoldhours);
        }
      } else if (value.paytypegroup == 'W') {
        SHWarranty += parseFloat(value.lbrsoldhours);
        JobCntWarranty.push(value.lbrlinecode);
        if (chartId == 1207) {
          JobCntInteranl.push(value.lbrlinecode);
        }
        if (chartId == 1200) {
          SHInternal += parseFloat(value.lbrsoldhours);
        }
      } else if (value.paytypegroup == 'I') {
        SHInternal += parseFloat(value.lbrsoldhours);
        JobCntInteranl.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'M') {
        SHMaintenance += parseFloat(value.lbrsoldhours);
        JobCntMaintain.push(value.lbrlinecode);
        if (chartId == 1207) {
          JobCntInteranl.push(value.lbrlinecode);
        }
        if (chartId == 1200) {
          SHInternal += parseFloat(value.lbrsoldhours);
        }
      } else if (value.paytypegroup == 'E' && chartId != 935) {
        //&& chartId != 935
        SHExtended += parseFloat(value.lbrsoldhours);
        JobCntExtended.push(value.lbrlinecode);
        if (chartId == 1207) {
          JobCntInteranl.push(value.lbrlinecode);
        }
        if (chartId == 1200) {
          SHInternal += parseFloat(value.lbrsoldhours);
        }
      } else if (value.paytypegroup == 'F' && chartId != 935) {
        //&& chartId != 935
        SHFactory += parseFloat(value.lbrsoldhours);
        JobCntFactory.push(value.lbrlinecode);
        if (chartId == 1207) {
          JobCntInteranl.push(value.lbrlinecode);
        }
        if (chartId == 1200) {
          SHInternal += parseFloat(value.lbrsoldhours);
        }
      } else if (value.paytypegroup == 'B') {
        SHAll += parseFloat(value.lbrsoldhours);
      }
    });
  } else {
    values.forEach(function(value) {
      if (value.paytypegroup == 'C') {
        lbrlinecodeArr.push(value.lbrlinecode);
      }
      if (
        value.opcategory == 'REPAIR' &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')
      ) {
        HoursRepair += parseFloat(value.lbrsoldhours);
        lbrlinecodeArrR.push(value.lbrlinecode);
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')
      ) {
        HoursMaintenance += parseFloat(value.lbrsoldhours);
        lbrlinecodeArrM.push(value.lbrlinecode);
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')
      ) {
        HoursCompetitive += parseFloat(value.lbrsoldhours);
        lbrlinecodeArrCO.push(value.lbrlinecode);
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')
      ) {
        lbrlinecodeArrS.push(value.lbrlinecode);
      }
    });
  }

  var JobCountAllR = (lbrlinecodeArrR.length / lbrlinecodeArr.length) * 100;
  var JobCountAllM = (lbrlinecodeArrM.length / lbrlinecodeArr.length) * 100;
  var JobCountAllC = (lbrlinecodeArrCO.length / lbrlinecodeArr.length) * 100;
  var JobCountAllS = (lbrlinecodeArrS.length / lbrlinecodeArr.length) * 100;
  Params.jobCountAllR =
    Math.round((JobCountAllR + Number.EPSILON) * 100) / 100 + '%';
  Params.jobCountAllM =
    Math.round((JobCountAllM + Number.EPSILON) * 100) / 100 + '%';
  Params.jobCountAllC =
    Math.round((JobCountAllC + Number.EPSILON) * 100) / 100 + '%';
  Params.jobCountAllS =
    Math.round((JobCountAllS + Number.EPSILON) * 100) / 100 + '%';
  SHByCategory = LbrHoursAll;

  SHByCategoryC = Math.round((LbrHours / SHByCategory) * 100);
  SHByCategoryE = Math.round((SHExtended / SHByCategory) * 100);
  SHByCategoryI = Math.round((SHInternal / SHByCategory) * 100);
  SHByCategoryM = Math.round((SHMaintenance / SHByCategory) * 100);
  SHByCategoryW = Math.round((SHWarranty / SHByCategory) * 100);
  SHByCategoryF = Math.round((SHFactory / SHByCategory) * 100);

  Params.SHByCategory = (
    Math.round((SHByCategory + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  HoursRepairCompetitive = HoursRepair;
  HoursMaintCompet = HoursCompetitive + HoursMaintenance;
  HoursAllCategories = HoursRepair + HoursCompetitive + HoursMaintenance;
  var laborSaleROAll = SaleAllCategoriesIncludeShop / roCountAll;
  var laborHrsPerROR = HoursRepair / roCountR;
  var laborHrsPerROM = HoursMaintenance / roCountM;
  var laborHrsPerROC = HoursCompetitive / roCountC;
  var laborHrsPerRoAll = HoursAllCategories / roCountAll;

  ElrRepair = SaleRepair / HoursRepair;

  ElrMaintenance = SaleMaintenance / HoursMaintenance;
  ElrCompetitive = SaleCompetitive / HoursCompetitive;

  ElrRepairCompetitive = SaleRepairCompetitive / HoursRepairCompetitive;
  ElrCompMaint = SaleCompMaint / HoursMaintCompet;

  ElrAllCategories = SaleAllCategories / HoursAllCategories;

  var DeltaShFh = flatrateHours - flatrateHours;
  var DeltaThFh = techHours - flatrateHours;

  var LbrHrsPerRoRPrcnt = (HoursRepair / roCountR) * 100;
  var LbrHrsPerRoMPrcnt = (HoursMaintenance / roCountAll) * 100;
  var LbrHrsPerRoCPrcnt = (HoursCompetitive / roCountAll) * 100;

  Params.lbrHrsPerRoRPrcnt =
    Math.round((LbrHrsPerRoRPrcnt + Number.EPSILON) * 100) / 100 + '%';
  Params.lbrHrsPerRoMPrcnt =
    Math.round((LbrHrsPerRoMPrcnt + Number.EPSILON) * 100) / 100 + '%';
  Params.lbrHrsPerRoCPrcnt =
    Math.round((LbrHrsPerRoCPrcnt + Number.EPSILON) * 100) / 100 + '%';
  Params.roCountAll = (
    Math.round((roCountAll + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.flatrateHours = (
    Math.round((flatrateHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.techHours = (
    Math.round((techHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.deltaShFh = (
    Math.round((DeltaShFh + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.deltaThFh = (
    Math.round((DeltaThFh + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.sHByCategoryC =
    (
      Math.round((SHByCategoryC + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryE =
    (
      Math.round((SHByCategoryE + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryW =
    (
      Math.round((SHByCategoryW + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryI =
    (
      Math.round((SHByCategoryI + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryM =
    (
      Math.round((SHByCategoryM + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryF =
    (
      Math.round((SHByCategoryF + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.roCountR = (
    Math.round((roCountR + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  if (SaleRepair != 0 && HoursRepair != 0) {
    Params.elrRepair =
      '$' +
      (Math.round((ElrRepair + Number.EPSILON) * 100) / 100).toLocaleString();
  } else {
    Params.elrRepair = 0;
  }

  Params.elrMaintenance =
    '$' +
    (
      Math.round((ElrMaintenance + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.elrCompetitive =
    '$' +
    (
      Math.round((ElrCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  if (SaleRepairCompetitive != 0 && HoursRepairCompetitive != 0) {
    Params.elrRepairCompetitive =
      '$' +
      (
        Math.round((ElrRepairCompetitive + Number.EPSILON) * 100) / 100
      ).toLocaleString();
  } else {
    Params.elrRepairCompetitive = 0;
  }

  Params.elrCompMaint =
    '$' +
    (Math.round((ElrCompMaint + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.elrAllCategories =
    '$' +
    (
      Math.round((ElrAllCategories + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.laborHoursSum = (
    Math.round((LbrHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.ShWarranty = (
    Math.round((SHWarranty + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.ShInternal = (
    Math.round((SHInternal + Number.EPSILON) * 100) / 100
  ).toFixed(2);
  Params.lbrTechHr = (
    Math.round((LbrTechHours + Number.EPSILON) * 100) / 100
  ).toFixed(2);

  Params.hoursRepair = (
    Math.round((HoursRepair + Number.EPSILON) * 100) / 100
  ).toFixed(2);
  Params.hoursMaintenance =
    Math.round((HoursMaintenance + Number.EPSILON) * 100) / 100;
  Params.hoursCompetitive =
    Math.round((HoursCompetitive + Number.EPSILON) * 100) / 100;
  Params.hoursRepairCompetitive =
    Math.round((HoursRepairCompetitive + Number.EPSILON) * 100) / 100;
  Params.hoursCompetMaint =
    Math.round((HoursMaintCompet + Number.EPSILON) * 100) / 100;
  Params.hoursAllCategories =
    Math.round((HoursAllCategories + Number.EPSILON) * 100) / 100;
  Params.jobCountR = (
    Math.round((lbrlinecodeArrR.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.jobCountM = (
    Math.round((lbrlinecodeArrM.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.jobCountCO = (
    Math.round((lbrlinecodeArrCO.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.jobCountS = (
    Math.round((lbrlinecodeArrS.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.lbrHrsPerRoR =
    Math.round((laborHrsPerROR + Number.EPSILON) * 100) / 100;
  Params.lbrHrsPerRoM =
    Math.round((laborHrsPerROM + Number.EPSILON) * 100) / 100;
  Params.lbrHrsPerRoC =
    Math.round((laborHrsPerROC + Number.EPSILON) * 100) / 100;
  Params.lbrHrsPerRoAll =
    Math.round((laborHrsPerRoAll + Number.EPSILON) * 100) / 100;
  Params.lbrHoursPerJob =
    Math.round((lbrHoursPerJob + Number.EPSILON) * 100) / 100;
  Params.lbrSaleperRoAll =
    '$' + Math.round((laborSaleROAll + Number.EPSILON) * 100) / 100;

  var dashboardName = getDbdName(chartId);
  var parentId = getChartParentId(chartId);
  values.forEach(function(value) {
    if (
      (((dashboardName == 'CP Overview' ||
        dashboardName == 'CP Labor Overview' ||
        dashboardName == 'CP Parts Overview') &&
        parentId == null) ||
        parentId == 942 ||
        parentId == 939 ||
        parentId == 940 ||
        chartId == 930 ||
        chartId == 936 ||
        chartId == 925 ||
        chartId == 1124 ||
        chartId == 1126) &&
      (value.paytypegroup == 'C' ||
        value.paytypegroup == 'E' ||
        value.paytypegroup == 'M' ||
        value.partspaytypegroup == 'C' ||
        value.partspaytypegroup == 'E' ||
        value.partspaytypegroup == 'M')
    ) {
      PrtSale += parseFloat(value.prtextendedsale);
      PartsSale += parseFloat(value.prtssale);
      PrtCost += parseFloat(value.prtextendedcost);
      PrtsDiscount += value.apportionedlbrdiscount
        ? parseFloat(value.apportionedlbrdiscount)
        : 0;
      console.log('oooo');
      TotalPrtsDiscount +=
        value.apportionedlbrdiscount != null
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
    } else if (
      (value.paytypegroup == 'C' || value.laborpaytypegroup == 'C') &&
      chartId != 1334 &&
      chartId != 1238 &&
      chartId != 1226 &&
      chartId != 1225 &&
      chartId != 916 &&
      chartId != 1227 &&
      chartId != 1228 &&
      chartId != 1229 &&
      chartId != 1230 &&
      chartId != 1231
    ) {
      PrtSale += parseFloat(value.prtextendedsale);
      PartsSale += parseFloat(value.prtssale);
      PrtCost += parseFloat(value.prtextendedcost);
      PrtsDiscount += value.apportionedlbrdiscount
        ? parseFloat(value.apportionedlbrdiscount)
        : 0;
      console.log('oooo');
      TotalPrtsDiscount +=
        value.apportionedlbrdiscount != null
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
    } else if (
      (chartId == 1238 ||
        chartId == 1226 ||
        chartId == 1225 ||
        chartId == 916) &&
      value.partspaytypegroup == 'C' &&
      (value.prtextendedsale != 0 || value.prtextendedcost != 0)
    ) {
      PrtSale += parseFloat(value.prtextendedsale);
      PartsSale += parseFloat(value.prtssale);
      PrtCost += parseFloat(value.prtextendedcost);
      PrtsDiscount += parseFloat(value.apportionedlbrdiscount);
      console.log('oooo');
      TotalPrtsDiscount +=
        value.apportionedlbrdiscount != null
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
    } else if (
      chartId == 1334 &&
      value.paytypegroup == 'C' &&
      (value.opcategory == 'REPAIR' || value.opcategory == 'COMPETITIVE')
    ) {
      PrtSale += parseFloat(value.prtextendedsale);
      PrtCost += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1227 || chartId == 1225) &&
      value.partspaytypegroup == 'W' &&
      (value.prtextendedsale != 0 || value.prtextendedcost != 0)
    ) {
      PSWarranty += parseFloat(value.prtextendedsale);
      PCWarranty += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'W' &&
      chartId != 1227 &&
      chartId != 1225
    ) {
      PSWarranty += parseFloat(value.prtextendedsale);
      PCWarranty += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1228 || chartId == 1225) &&
      value.partspaytypegroup == 'I' &&
      (value.prtextendedsale != 0 || value.prtextendedcost != 0)
    ) {
      PSInternal += parseFloat(value.prtextendedsale);
      PCInternal += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'I' &&
      chartId != 1228 &&
      chartId != 1225
    ) {
      PSInternal += parseFloat(value.prtextendedsale);
      PCInternal += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1229 || chartId == 1225) &&
      value.partspaytypegroup == 'M' &&
      (value.prtextendedsale != 0 || value.prtextendedcost != 0)
    ) {
      PSMaintenancePrt += parseFloat(value.prtextendedsale);
      PCMaintenancePrt += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'M' &&
      chartId != 1229 &&
      chartId != 1225
    ) {
      PSMaintenanceP += parseFloat(value.prtextendedsale);
      PCMaintenanceP += parseFloat(value.prtextendedcost);
      PSMaintenancePrt += parseFloat(value.prtextendedsale);
      PCMaintenancePrt += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1230 || chartId == 1225) &&
      value.partspaytypegroup == 'E' &&
      (value.prtextendedsale != 0 || value.prtextendedcost != 0)
    ) {
      PSExtendedPrt += parseFloat(value.prtextendedsale);
      PCExtendedPrt += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'E' &&
      chartId != 1230 &&
      chartId != 1225
    ) {
      PSExtended += parseFloat(value.prtextendedsale);
      PCExtended += parseFloat(value.prtextendedcost);
      PSExtendedPrt += parseFloat(value.prtextendedsale);
      PCExtendedPrt += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1231 || chartId == 1225) &&
      value.partspaytypegroup == 'F' &&
      (value.prtextendedsale != 0 || value.prtextendedcost != 0)
    ) {
      PSFactory += parseFloat(value.prtextendedsale);
      PCFactory += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'F' &&
      chartId != 1231 &&
      chartId != 1225
    ) {
      PSFactory += parseFloat(value.prtextendedsale);
      PCFactory += parseFloat(value.prtextendedcost);
    }
  });
  LbrDiscount = Math.abs(LbrDiscount);
  TotalLbrDiscount = Math.abs(TotalLbrDiscount).toFixed(2);

  PrtsDiscount = Math.abs(PrtsDiscount);
  console.log('lllll', TotalPrtsDiscount);
  TotalPrtsDiscount = Math.abs(TotalPrtsDiscount);

  // Params.lbrDiscount = '$' + (Math.round((LbrDiscount + Number.EPSILON) * 100) / 100).toLocaleString();
  var TotalLbrSaleP = TotalLbrDiscount / LbrSale;
  var TotalPrtSaleP = TotalPrtsDiscount / PrtSale;

  var TotalDiscounts = LbrSale + PartsSale;
  // Params.totalLbrSaleP =
  // (Math.round((TotalLbrSaleP + Number.EPSILON) * 100) / 100).toLocaleString() + '%';
  // Params.totalPrtSaleP =
  // (Math.round((TotalPrtSaleP + Number.EPSILON) * 100) / 100).toLocaleString() + '%';
  Params.totallbrDiscount = '$' + TotalLbrDiscount;

  Params.TotalDiscountVal = '$' + TotalDiscounts;

  Params.PercPrtsDiscount =
    (Math.round((TotalPrtsDiscount / TotalDiscounts) * 100) / 100).toFixed(1) +
    '%';

  Params.totalprtsDiscount =
    '$' +
    (
      Math.round((TotalPrtsDiscount + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.lbrDiscount =
    '$' +
    (Math.round((LbrDiscount + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsDiscount =
    '$' +
    (Math.round((PrtsDiscount + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.partsSale = Math.round((PartsSale + Number.EPSILON) * 100) / 100;
  Params.partsSaleSum =
    '$' + (Math.round((PrtSale + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PsWarranty =
    '$' +
    (Math.round((PSWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PsInternal =
    '$' +
    (Math.round((PSInternal + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.partsCostSum =
    '$' + (Math.round((PrtCost + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcWarranty =
    '$' +
    (Math.round((PCWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcInternal =
    '$' +
    (Math.round((PCInternal + Number.EPSILON) * 100) / 100).toLocaleString();

  Params.PsMaintenanceP =
    '$' +
    (
      Math.round((PSMaintenancePrt + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.PcMaintenanceP =
    '$' +
    (
      Math.round((PCMaintenanceP + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.PsExtended =
    '$' +
    (Math.round((PSExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcExtended =
    '$' +
    (Math.round((PCExtended + Number.EPSILON) * 100) / 100).toLocaleString();

  if (chartId == 1230 || chartId == 1225) {
    Params.PsExtended =
      '$' +
      (
        Math.round((PSExtendedPrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.PcExtended =
      '$' +
      (
        Math.round((PCExtendedPrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
  }
  if (chartId == 1229 || chartId == 1225) {
    Params.PSMaintenanceP =
      '$' +
      (
        Math.round((PSMaintenancePrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.PcMaintenanceP =
      '$' +
      (
        Math.round((PCMaintenancePrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
  }
  Params.PsFactory =
    '$' +
    (Math.round((PSFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcFactory =
    '$' +
    (Math.round((PCFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Markup = PrtSale / PrtCost;
  Params.partsMarkup = (((Markup + Number.EPSILON) * 100) / 100).toFixed(4);
  Params.partsMarkupRC = (((Markup + Number.EPSILON) * 100) / 100).toFixed(4);

  LbrGP = LbrSale - LbrCost;
  GPWarranty = LSWarranty - LCWarranty;
  GPInternal = LSInternal - LCInternal;
  LbrSaleCombined =
    LbrSale +
    LSWarranty +
    LSInternal +
    SaleMaintenance1 +
    SaleExtended +
    SaleFactory;

  LbrCostCombined =
    LbrCost +
    LCWarranty +
    LCInternal +
    CostMaintenance +
    CostExtended +
    CostFactory;
  GPCombined = LbrSaleCombined - LbrCostCombined;
  GPMaintenanceP = SaleMaintenance1 - CostMaintenance;
  GPExtended = SaleExtended - CostExtended;
  var GPFactor = SaleFactory - CostFactory;

  var PartsToLaborRatio = PrtSale / LbrSale;

  Params.laborGPMP =
    '$' +
    (
      Math.round((GPMaintenanceP + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.laborGPE =
    '$' +
    (Math.round((GPExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPF =
    '$' +
    (Math.round((GPFactor + Number.EPSILON) * 100) / 100).toLocaleString();

  Params.partsToLaborRatio =
    '$' + Math.round((PartsToLaborRatio + Number.EPSILON) * 100) / 100;

  Params.laborGP =
    '$' + (Math.round((LbrGP + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPW =
    '$' +
    (Math.round((GPWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPI =
    '$' +
    (Math.round((GPInternal + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPCombined =
    '$' +
    (Math.round((GPCombined + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborSaleCombined =
    '$' +
    (
      Math.round((LbrSaleCombined + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.laborCostCombined =
    '$' +
    (
      Math.round((LbrCostCombined + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  PrtGP = PrtSale - PrtCost;
  Params.partsGP =
    '$' + (Math.round((PrtGP + Number.EPSILON) * 100) / 100).toLocaleString();

  LbrGPPerc = (LbrGP / LbrSale) * 100;

  Params.laborGPPerc =
    (Math.round((LbrGPPerc + Number.EPSILON) * 10) / 10).toFixed(1) + '%';
  // Params.laborGPPerc =
  //   (Math.round((LbrGPPerc + Number.EPSILON) * 100) / 100).toFixed(1) + '%';

  PrtGPPerc = (PrtGP / PrtSale) * 100;
  Params.partsGPPerc =
    (Math.round((PrtGPPerc + Number.EPSILON) * 10) / 10).toFixed(1) + '%';

  LsSumCombined = LbrSale + PrtSale;

  LaborPercetSumCombined =
    ((PrtSale + LbrSale - (PrtCost + LbrCost)) / (PrtSale + LbrSale)) * 100;
  LsGrossSumCombined = PrtGP + LbrGP;
  Params.laborSaleSumCombined =
    '$' +
    (Math.round((LsSumCombined + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborPercetSumCombined =
    (Math.round((LaborPercetSumCombined + Number.EPSILON) * 100) / 100).toFixed(
      1
    ) + '%';
  Params.lsGrossSumCombined =
    '$' +
    (
      Math.round((LsGrossSumCombined + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  ELR = LbrSale / LbrHours;
  Params.elr =
    '$' + (Math.round((ELR + Number.EPSILON) * 100) / 100).toLocaleString();
  LSCombined =
    LbrHours +
    SHWarranty +
    SHInternal +
    parseFloat(Params.shExtended) +
    SHMaintenance +
    parseFloat(Params.shFactory);

  Params.LsCombined = (
    Math.round((LSCombined + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  values.forEach(function(value) {
    if (
      value.paytypegroup == 'C' &&
      (value.opcategory == 'REPAIR' || value.opcategory == 'COMPETITIVE')
    ) {
      lbrSaleRC += value.lbrsale;
      lbrSoldhrsRC += value.lbrsoldhours;
    }
    ELRRepairCompet =
      Math.round((lbrSaleRC / lbrSoldhrsRC + Number.EPSILON) * 100) / 100;
  });
  Params.lbrSaleRepairCompet =
    '$' +
    (Math.round((lbrSaleRC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrHrsRepairCompet = (
    Math.round((lbrSoldhrsRC + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.elrRepairCompet =
    '$' +
    (
      Math.round((ELRRepairCompet + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  PSSumComp =
    PrtSale +
    PSWarranty +
    PSInternal +
    PSMaintenancePrt +
    PSExtendedPrt +
    PSFactory;
  PCSumComp =
    PrtCost +
    PCWarranty +
    PCInternal +
    PCMaintenancePrt +
    PCExtendedPrt +
    PCFactory;
  PrtGpWar = PSWarranty - PCWarranty;
  PrtGpInt = PSInternal - PCInternal;
  PrtGpMP = PSMaintenancePrt - PCMaintenancePrt;
  prtGpE = PSExtendedPrt - PCExtendedPrt;
  PrtGpF = PSFactory - PCFactory;

  PrtGpCombin = PrtGpWar + PrtGpInt + PrtGP + PrtGpMP + prtGpE + PrtGpF;

  Params.prtGpMP =
    '$' + (Math.round((PrtGpMP + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpE =
    '$' + (Math.round((prtGpE + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.psSumComp =
    '$' +
    (Math.round((PSSumComp + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.pcSumComp =
    '$' +
    (Math.round((PCSumComp + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpWar =
    '$' +
    (Math.round((PrtGpWar + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpInt =
    '$' +
    (Math.round((PrtGpInt + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpCombin =
    '$' +
    (Math.round((PrtGpCombin + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpF =
    '$' + (Math.round((PrtGpF + Number.EPSILON) * 100) / 100).toLocaleString();
  values.forEach(function(value) {
    if (
      value.opcategory == 'REPAIR' &&
      ((chartId == 936 &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')) ||
        (chartId != 936 && value.paytypegroup == 'C'))
    ) {
      PrtsaleR += parseFloat(value.prtextendedsale);
      PrtcostR += parseFloat(value.prtextendedcost);
    } else if (
      value.opcategory == 'MAINTENANCE' &&
      ((chartId == 936 &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')) ||
        (chartId != 936 && value.paytypegroup == 'C'))
    ) {
      PrtsaleM += parseFloat(value.prtextendedsale);
      PrtcostM += parseFloat(value.prtextendedcost);
    } else if (
      value.opcategory == 'COMPETITIVE' &&
      ((chartId == 936 &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M')) ||
        (chartId != 936 && value.paytypegroup == 'C'))
    ) {
      PrtsaleComp += parseFloat(value.prtextendedsale);
      PrtcostComp += parseFloat(value.prtextendedcost);
    }
  });
  var PrtsToLbrRatioR = PrtsaleR / SaleRepair;
  var PrtsToLbrRatioM = PrtsaleM / SaleMaintenance;
  var PrtsToLbrRatioC = PrtsaleComp / SaleCompetitive;
  Params.prtsToLbrRatioR = (
    '$' +
    Math.round((PrtsToLbrRatioR + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsToLbrRatioM = (
    '$' +
    Math.round((PrtsToLbrRatioM + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsToLbrRatioC = (
    '$' +
    Math.round((PrtsToLbrRatioC + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.prtsaleR =
    '$' +
    (Math.round((PrtsaleR + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleM =
    '$' +
    (Math.round((PrtsaleM + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleComp =
    '$' +
    (Math.round((PrtsaleComp + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostR =
    '$' +
    (Math.round((PrtcostR + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostM =
    '$' +
    (Math.round((PrtcostM + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostComp =
    '$' +
    (Math.round((PrtcostComp + Number.EPSILON) * 100) / 100).toLocaleString();
  MarkupR = PrtsaleR / PrtcostR;
  MarkupM = PrtsaleM / PrtcostM;
  MarkupC = PrtsaleComp / PrtcostComp;
  MarkupCmb =
    (PrtsaleR + PrtsaleM + PrtsaleComp) / (PrtcostR + PrtcostM + PrtcostComp);
  Params.partsMarkupR = (
    Math.round((MarkupR + Number.EPSILON) * 100) / 100
  ).toFixed(4);
  Params.partsMarkupM = (
    Math.round((MarkupM + Number.EPSILON) * 100) / 100
  ).toFixed(4);
  Params.partsMarkupC = (
    Math.round((MarkupC + Number.EPSILON) * 100) / 100
  ).toFixed(4);
  Params.partsMarkupCmb = (
    Math.round((MarkupCmb + Number.EPSILON) * 100) / 100
  ).toFixed(4);

  values.forEach(function(value) {
    if (
      value.paytypegroup == 'I' ||
      value.paytypegroup == 'W' ||
      value.paytypegroup == 'C' ||
      value.paytypegroup == 'M' ||
      value.paytypegroup == 'E' ||
      value.paytypegroup == 'F'
    ) {
      lbrsaleC += value.lbrsale;
      lbrcostC += value.lbrcost;
      JobCntCombined.push(value.lbrlinecode);
      // PrtsaleAll += value.prtextendedsale;
      // PrtcostAll += value.prtextendedcost;
      // lbrlinecodeArrC.push(value.lbrlinecode);
      // ronumberArrC.push(value.ronumber);
    }
  });

  ElrCombined = lbrsaleC / LSCombined;
  ElrCustomer = LbrSale / LbrHours;
  ElrWarranty = LSWarranty / SHWarranty;
  ElrInternal = LSInternal / SHInternal;
  ElrMaintenance = SaleMaintenance1 / SHMaintenance1;
  ElrExtended = SaleExtended / parseFloat(Params.shExtended);
  ElrFactory = SaleFactory / parseFloat(Params.shFactory);
  Params.lbrSaleC =
    '$' +
    (Math.round((lbrsaleC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrCostC =
    '$' +
    (Math.round((lbrcostC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.ElrCombined =
    '$' + ElrCombined.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrCustomer =
    '$' + ElrCustomer.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrWarranty =
    '$' + ElrWarranty.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrInternal =
    '$' + ElrInternal.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrMaintenance =
    '$' + ElrMaintenance.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrExtended =
    '$' + ElrExtended.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrFactory =
    '$' + ElrFactory.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  Params.JobCntCombined = (
    Math.round((JobCntCombined.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntCustomer = (
    Math.round((JobCntCustomer.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntWarranty = (
    Math.round((JobCntWarranty.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntInteranl = (
    Math.round((JobCntInteranl.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntMaintain = (
    Math.round((JobCntMaintain.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntExtended = (
    Math.round((JobCntExtended.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntFactory = (
    Math.round((JobCntFactory.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  prtsMarkupCombined = PSSumComp / PCSumComp;
  prtsMarkupCustomer = PrtSale / PrtCost;
  prtsMarkupWarranty = PSWarranty / PCWarranty;
  prtsMarkupInteranl = PSInternal / PCInternal;
  prtsMarkupMaintain = PSMaintenancePrt / PCMaintenancePrt;
  prtsMarkupExtended = PSExtendedPrt / PCExtendedPrt;
  prtsMarkupFactory = PSFactory / PCFactory;
  Params.prtsMarkupCombined = prtsMarkupCombined.toFixed(4);
  Params.prtsMarkupCustomer = prtsMarkupCustomer.toFixed(4);
  Params.prtsMarkupWarranty = prtsMarkupWarranty.toFixed(4);
  Params.prtsMarkupInteranl = prtsMarkupInteranl.toFixed(4);
  Params.prtsMarkupMaintain = prtsMarkupMaintain.toFixed(4);
  Params.prtsMarkupExtended = prtsMarkupExtended.toFixed(4);
  Params.prtsMarkupFactory = prtsMarkupFactory.toFixed(4);
  if (
    chartId == 1239 ||
    chartId == 1240 ||
    chartId == 1241 ||
    chartId == 1242 ||
    chartId == 1360 ||
    chartId == 1359 ||
    chartId == 1361 ||
    chartId == 1362
  ) {
    values.forEach(function(value) {
      if (
        (chartId == 1239 ||
          chartId == 1241 ||
          chartId == 1240 ||
          chartId == 1360) &&
        value.lbrPaytype == 'C'
      ) {
        shopSuppliesC += value.customerpayshopsup;
      } else if (
        (chartId == 1242 ||
          chartId == 1239 ||
          chartId == 1240 ||
          chartId == 1362) &&
        value.lbrPaytype == 'I'
      ) {
        shopSuppliesI += value.internalshopsup;
      } else if (chartId == 1240) {
        shopSuppliesCombined = shopSuppliesC + shopSuppliesI;
      } else if (
        (chartId == 1239 || chartId == 1361) &&
        value.lbrPaytype == 'W'
      ) {
        shopSuppliesW += parseFloat(value.warrantyshopsup);
      } else if (
        chartId == 1359 &&
        (value.lbrPaytype == 'C' ||
          value.lbrPaytype == 'I' ||
          value.lbrPaytype == 'W')
      ) {
        shopSuppliesC += value.customerpayshopsup;
        shopSuppliesI += value.internalshopsup;
        shopSuppliesW += parseFloat(value.warrantyshopsup);
        shopSuppliesCombined = shopSuppliesC + shopSuppliesI + shopSuppliesW;
      }
    });
  }

  Params.shopSuppliesC =
    '$' + shopSuppliesC.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.shopSuppliesI =
    '$' + shopSuppliesI.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.shopSuppliesCombined =
    '$' + shopSuppliesCombined.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.shopSuppliesW =
    '$' + shopSuppliesW.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return Params;
}
export function myCustomSumAggregateRevisedTotals(
  rowData,
  chartId,
  filterValues,
  allData
) {
  var values;
  if (typeof filterValues != 'undefined') {
    values = rowData;
  } else {
    values = allData;
  }
  // console.log('filterValues', values, filterValues);
  var LbrSale = 0;
  var LbrCost = 0;
  var LbrHours = 0;
  var PrtCost = 0;
  var PrtSale = 0;
  var LbrGP = 0;
  var PrtGP = 0;
  var PrtGPPerc = 0;
  var LbrGPPerc = 0;
  var SHWarranty = 0;
  var LSWarranty = 0;
  var LCWarranty = 0;
  var GPWarranty = 0;
  var SHFactory = 0;
  var GPPercWarranty = 0;
  var LbrSaleCombined = 0;
  var LbrCostCombined = 0;
  var GPCombined = 0;
  var LsSumCombined = 0;
  var LsGrossSumCombined = 0;
  var LaborPercetSumCombined = 0;
  var ELR = 0;
  var ELRRepairCompet = 0;
  var LSInternal = 0;
  var LCInternal = 0;
  var SHInternal = 0;
  var GPInternal = 0;
  var LSCombined = 0;
  var ElrRepair = 0;
  var SaleRepair = 0;
  var HoursRepair = 0;
  var SaleMaintenance = 0;
  var SaleMaintenance1 = 0;
  var CostMaintenance = 0;
  var HoursMaintenance = 0;
  var ElrMaintenance = 0;
  var SaleCompetitive = 0;
  var SaleShop = 0;
  var SHMaintenance1 = 0;
  var SaleExtended = 0;
  var CostExtended = 0;
  var SaleFactory = 0;
  var CostFactory = 0;
  var SHExtended = 0;
  var GPMaintenanceP = 0;
  var GPExtended = 0;
  var GPFactor = 0;
  var HoursCompetitive = 0;
  var ElrCompetitive = 0;
  var ElrRepairCompetitive = 0;
  var HoursRepairCompetitive = 0;
  var SaleRepairCompetitive = 0;
  var HoursMaintCompet = 0;
  var SaleCompMaint = 0;
  var ElrCompMaint = 0;
  var SaleAllCategories = 0;
  var SaleAllCategoriesIncludeShop = 0;
  var HoursAllCategories = 0;
  var ElrAllCategories = 0;
  var LbrSaleRO = 0;
  var LbrTechHours = 0;
  var lbrSaleRC = 0;
  var lbrSoldhrsRC = 0;
  var Markup = 0;
  var CostRepair = 0;
  var CostMaintenances = 0;
  var CostCompetitive = 0;
  var CostShop = 0;
  var lbrlinecodeArrR = [];
  var lbrlinecodeArr = [];
  var lbrlinecodeArrCO = [];
  var lbrlinecodeArrM = [];
  var lbrlinecodeArrS = [];
  var ronumberArrR = [];
  var ronumberArrM = [];
  var ronumberArrC = [];
  var ronumberArrS = [];
  var ronumberArrAll = [];
  var ronumberArrPrtsHours = [];
  var ronumberArrMPVI = [];
  var mpiCountArr = [];
  var menuCountArr = [];
  var jobCountAddOn = [];
  var jobCountAddOnR = [];
  var jobCountAddOnC = [];
  var jobCountAddOnM = [];
  var jobCountAddOnS = [];
  var jobCountAddOnPerRo = [];
  var jobCountNonAddOn = [];
  const ronumberSetAll = new Set();
  const ronumberSetR = new Set();
  const ronumberSetM = new Set();
  const ronumberSetC = new Set();
  const ronumberSetS = new Set();
  const ronumberSetMPI = new Set();
  const ronumberSetMenu = new Set();
  const ronumberSetMPICalc = new Set();
  const ronumberSetPrtsHours = new Set();
  var PSWarranty = 0;
  var PCWarranty = 0;
  var PSInternal = 0;
  var PCInternal = 0;
  var PCSumComp = 0;
  var PSSumComp = 0;
  var PrtGpWar = 0;
  var prtGpE = 0;
  var PrtGpMP = 0;
  var PrtGpInt = 0;
  var PrtGpCombin = 0;
  var MarkupR = 0;
  var MarkupM = 0;
  var MarkupC = 0;
  var MarkupCmb = 0;
  var PrtsaleR = 0;
  var PrtsaleM = 0;
  var PrtsaleComp = 0;
  var PrtcostR = 0;
  var PrtcostM = 0;
  var PrtcostComp = 0;
  var laborHrsPerJob = 0;
  var prtsHrsPerJob = 0;
  var SHMaintenance = 0;
  var SHExtended = 0;
  var SHAll = 0;
  var SHByCategory = 0;
  var SHByCategoryC = 0;
  var SHByCategoryE = 0;
  var SHByCategoryI = 0;
  var SHByCategoryW = 0;
  var SHByCategoryM = 0;
  var SHByCategoryF = 0;
  var totalJobCount = [];
  var techHours = 0;
  var flatrateHours = 0;
  var LbrDiscount = 0;
  var TotalLbrDiscount = 0;
  var PrtsDiscount = 0;
  var TotalPrtsDiscount = 0;
  var percLbrDiscount = 0;
  var percPartsDiscount = 0;
  var lbrSoldhrsAddon = 0;
  var lbrSoldhrsNonAddon = 0;
  var lbrSaleAddOn = 0;
  var prtSaleAddOn = 0;
  var totalSaleAddOn = 0;
  var lbrSaleRAddOn = 0;
  var lbrSaleCAddOn = 0;
  var lbrSaleMAddOn = 0;
  var lbrSaleSAddOn = 0;
  var prtSaleRAddOn = 0;
  var prtSaleSAddOn = 0;
  var prtSaleMAddOn = 0;
  var prtSaleCAddOn = 0;
  var lbrhrsR = 0;
  var lbrhrsM = 0;
  var lbrhrsC = 0;
  var lbrhrsS = 0;
  var prtCostRAddOn = 0;
  var prtCostMAddOn = 0;
  var prtCostCAddOn = 0;
  var prtCostSAddOn = 0;
  var prtSaleNonAddOn = 0;
  var lbrSaleNonAddOn = 0;
  var totalSaleAddOn = 0;
  var totalLbrSaleAll = 0;
  var totalPrtSaleAll = 0;
  var PrtsDiscount = 0;
  var TotalPrtsDiscount = 0;
  var PSMaintenancePrt = 0;
  var PCMaintenancePrt = 0;
  var PSExtendedPrt = 0;
  var PCExtendedPrt = 0;
  var lbrsaleC = 0;
  var lbrcostC = 0;
  var ElrCombined = 0;
  var ElrCustomer = 0;
  var ElrWarranty = 0;
  var ElrInternal = 0;
  var ElrMaintenance = 0;
  var ElrExtended = 0;
  var ElrFactory = 0;
  var shopSuppliesC = 0;
  var shopSuppliesI = 0;
  var shopSuppliesCombined = 0;
  const ronumberSetRS = new Set();
  const ronumberSetMS = new Set();
  const ronumberSetCS = new Set();
  const ronumberSetSS = new Set();
  const techNoSet = new Set();
  var ronumberArrRS = [];
  var ronumberArrMS = [];
  var ronumberArrCS = [];
  var ronumberArrSS = [];
  var techArr = [];
  var PSMaintenanceP = 0;
  var PCMaintenanceP = 0;
  var PSExtended = 0;
  var PCExtended = 0;
  var SHFactory = 0;
  var PSFactory = 0;
  var PCFactory = 0;
  var PrtGpF = 0;
  var SoldHoursTech = 0;
  var AvailableHours = 0;
  var AvgHrsTech = 0;
  var JobCntCombined = [];
  var JobCntCustomer = [];
  var JobCntWarranty = [];
  var JobCntInteranl = [];
  var JobCntMaintain = [];
  var JobCntExtended = [];
  var JobCntFactory = [];
  var prtsMarkupCombined = 0;
  var revPrtsMarkupCustomer = 0;
  var prtsMarkupWarranty = 0;
  var prtsMarkupInteranl = 0;
  var prtsMarkupMaintain = 0;
  var prtsMarkupExtended = 0;
  var prtsMarkupFactory = 0;
  var TotalSale = 0;
  var PartsSale = 0;
  var PrtSaleRC = 0;
  var PrtCostRC = 0;

  let Params = {
    laborSaleSum: '',
    LsWarranty: '',
    LsInternal: '',
    saleRepair: '',
    saleMaintenance: '',
    saleMaintenance1: '',
    costMaintenance: '',
    saleCompetitive: '',
    saleRepairCompetitive: '',
    saleAllCategories: '',
    saleShopSupplies: '',
    lbrSaleperRoR: '',
    lbrSaleperRoM: '',
    lbrSaleperRoC: '',
    lbrSaleperRoS: '',
    lbrSaleperRoAll: '',
    lbrHoursPerRO: '',
    prtsHoursPerRO: '',
    prtsHours: '',
    prtsHrsRoCount: '',
    laborCostSum: '',
    LcWarranty: '',
    LcInternal: '',
    elrRepair: '',
    elrMaintenance: '',
    elrCompetitive: '',
    elrRepairAddOn: '',
    elrMaintenanceAddOn: '',
    elrCompetitiveAddOns: '',
    elrShopAddOn: '',
    elrRepairCompetitive: '',
    elrCompMaint: '',
    elrAllCategories: '',
    laborHoursSum: '',
    ShWarranty: '',
    ShInternal: '',
    lbrTechHr: '',
    hoursRepair: '',
    hoursMaintenance: '',
    hoursCompetitive: '',
    hoursRepairCompetitive: '',
    hoursAllCategories: '',
    jobCountR: '',
    jobCountM: '',
    jobCountCO: '',
    jobCountS: '',
    lbrHrsPerRoR: '',
    lbrHrsPerRoM: '',
    lbrHrsPerRoC: '',
    lbrHrsPerRoAll: '',
    lbrHoursPerJob: '',
    lbrHoursAll: '',
    roCountAllCat: '',
    revPartsSaleSum: '',
    partsSaleSum: '',
    PsWarranty: '',
    PsInternal: '',
    revPartsCostSum: '',
    PcWarranty: '',
    PcInternal: '',
    laborGP: '',
    laborGPW: '',
    laborGPI: '',
    laborGPCombined: '',
    laborSaleCombined: '',
    laborCostCombined: '',
    partsMarkup: '',
    partsGP: '',
    laborGPPerc: '',
    partsGPPerc: '',
    laborSaleSumCombined: '',
    laborPercetSumCombined: '',
    lsGrossSumCombined: '',
    elr: '',
    LsCombined: '',
    lbrSaleRepairCompet: '',
    lbrHrsRepairCompet: '',
    elrRepairCompet: '',
    psSumComp: '',
    pcSumComp: '',
    prtGpWar: '',
    prtGpInt: '',
    prtGpCombin: '',
    prtsaleR: '',
    prtsaleM: '',
    prtsaleComp: '',
    prtcostR: '',
    prtcostM: '',
    prtcostComp: '',
    partsMarkupR: '',
    partsMarkupM: '',
    partsMarkupC: '',
    partsMarkupCmb: '',
    partsToLaborRatio: '',
    prtsToLbrRatioR: '',
    prtsToLbrRatioM: '',
    prtsToLbrRatioC: '',
    sHMaintenance: '',
    SHExtended: '',
    sHAll: '',
    sHByCategoryC: '',
    sHByCategoryE: '',
    sHByCategoryI: '',
    sHByCategoryW: '',
    sHByCategoryM: '',
    sHByCategoryF: '',
    deltaShFh: '',
    deltaThFh: '',
    techHours: '',
    flatrateHours: '',
    roCountAll: '',
    lbrDiscount: '',
    totallbrDiscount: '',
    roCountShop: '',
    RoCountAll: '',
    totalprtsDiscount: '',
    prtsDiscount: '',
    jobCountAddOn: '',
    jobCountNonAddOn: '',
    jobCountAddOnR: '',
    jobCountAddOnC: '',
    jobCountAddOnM: '',
    jobCountAddOnS: '',
    jobCountAddOnPerRo: '',
    ROCountPercAddOn: '',
    distinctROCountNonAddOn: '',
    distinctROCountAddOn: '',
    lbrSoldhrsNonAddon: '',
    lbrSoldhrsNonAddon: '',
    prtSaleAddOn: '',
    lbrSaleAddOn: '',
    lbrSaleRAddOn: '',
    lbrSaleCAddOn: '',
    lbrSaleMAddOn: '',
    lbrSaleSAddOn: '',
    prtSaleRAddOn: '',
    prtSaleCAddOn: '',
    prtSaleMAddOn: '',
    prtSaleSAddOn: '',
    MarkupR: '',
    MarkupS: '',
    MarkupC: '',
    MarkupM: '',
    addOnRevenueperc: '',
    nonAddOnRevenueperc: '',
    discountROPerc: '',
    ronumberArrAll: '',
    jobCountAll: '',
    ronumberArrMPVI: '',
    totalLbrSaleP: '',
    totalPrtSaleP: '',
    shMaintenance1: '',
    saleExtended: '',
    costExtended: '',
    shExtended: '',
    laborGPMP: '',
    laborGPE: '',
    laborGPF: '',
    PsMaintenanceP: '',
    PsExtended: '',
    PcMaintenanceP: '',
    PcExtended: '',
    prtGpMP: '',
    prtGpE: '',
    saleFactory: '',
    costFactory: '',
    shFactory: '',
    PsFactory: '',
    PcFactory: '',
    prtGpF: '',
    costRepair: '',
    costMaintenances: '',
    costCompetitive: '',
    costShop: '',
    SHByCategory: '',
    soldHoursTech: '',
    availableHours: '',
    avgHrsTech: '',
    techCount: '',
    avgSalesPerTech: '',
    totalSaleTech: '',
    ElrCombined: '',
    ElrCustomer: '',
    ElrWarranty: '',
    ElrInternal: '',
    ElrMaintenance: '',
    ElrExtended: '',
    ElrFactory: '',
    lbrSaleC: '',
    lbrCostC: '',
    JobCntCombined: '',
    JobCntCustomer: '',
    JobCntWarranty: '',
    JobCntInteranl: '',
    JobCntMaintain: '',
    JobCntExtended: '',
    JobCntFactory: '',
    prtsMarkupCombined: '',
    revPrtsMarkupCustomer: '',
    prtsMarkupWarranty: '',
    prtsMarkupInteranl: '',
    prtsMarkupMaintain: '',
    prtsMarkupExtended: '',
    prtsMarkupFactory: '',
    LbrSale: '',
    partsSale: '',
    shopSuppliesC: '',
    shopSuppliesCombined: '',
    shopSuppliesI: '',
    mpiCount: '',
    totalROCount: '',
    mpiPercentage: '',
    menuPercentage: '',
    menuCount: '',
    partsMarkupRC: '',
    PrtSaleRC: '',
    PrtCostRC: ''
  };
  if (
    chartId == 1099 ||
    chartId == 1100 ||
    chartId == 1101 ||
    chartId == 1102 ||
    chartId == 1105 ||
    chartId == 1106 ||
    chartId == 1107 ||
    chartId == 1104 ||
    chartId == 1108 ||
    chartId == 1103 ||
    chartId == 1109 ||
    chartId == 1110 ||
    chartId == 1116 ||
    chartId == 1117 ||
    chartId == 1118 ||
    chartId == 1119 ||
    chartId == 1120 ||
    chartId == 1121 ||
    chartId == 1122 ||
    chartId == 1079 ||
    chartId == 1316 ||
    chartId == 1317
  ) {
    values.forEach(function(value) {
      var menuOpcodesArr = [
        '60K',
        '100KT8',
        '100KC4',
        '100K6',
        '45KT',
        '100K8',
        '100K4',
        '15K',
        '30K',
        '45K',
        '30KA',
        '90K',
        '100KT6',
        '100KT4'
      ];
      if (value.linaddonflag == 'Y' && value.paytypegroup == 'C') {
        ronumberArrC.push(value.ronumber);
        jobCountAddOn.push(value.linaddonflag);

        lbrSoldhrsAddon += value.lbrsoldhours;
        lbrSaleAddOn += value.lbrsale;
        prtSaleAddOn += value.prtextendedsale;
      }

      if (value.linaddonflag == 'N' && value.paytypegroup == 'C') {
        ronumberArrS.push(value.ronumber);
        jobCountNonAddOn.push(value.linaddonflag);
        lbrSoldhrsNonAddon += value.lbrsoldhours;
        lbrSaleNonAddOn += value.lbrsale;
        prtSaleNonAddOn += value.prtextendedsale;
      }
      if (value.lbropcode == 'MPVI') {
        mpiCountArr.push(value.ronumber);
      }
      if (chartId == 1317) {
        if (menuOpcodesArr.includes(value.lbropcode)) {
          menuCountArr.push(value.ronumber);
        }
      }
      if (value.paytypegroup == 'C') {
        ronumberArrAll.push(value.ronumber);
        totalLbrSaleAll += value.lbrsale;
        totalPrtSaleAll += value.prtextendedsale;
      }
      ronumberArrMPVI.push(value.ronumber);
      CostMaintenance += value.lbrcost;

      if (
        value.opcategory == 'REPAIR' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnR.push(value.linaddonflag);
        lbrSaleRAddOn += value.lbrsale;
        prtSaleRAddOn += value.prtextendedsale;
        lbrhrsR += value.lbrsoldhours;
        prtCostRAddOn += value.prtextendedcost;
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnM.push(value.linaddonflag);
        lbrSaleMAddOn += value.lbrsale;
        prtSaleMAddOn += value.prtextendedsale;
        lbrhrsM += value.lbrsoldhours;
        prtCostMAddOn += value.prtextendedcost;
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnC.push(value.linaddonflag);
        lbrSaleCAddOn += value.lbrsale;
        prtSaleCAddOn += value.prtextendedsale;
        lbrhrsC += value.lbrsoldhours;
        prtCostCAddOn += value.prtextendedcost;
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        value.paytypegroup == 'C' &&
        value.linaddonflag == 'Y'
      ) {
        jobCountAddOnS.push(value.linaddonflag);
        lbrSaleSAddOn += value.lbrsale;
        prtSaleSAddOn += value.prtextendedsale;
        lbrhrsS += value.lbrsoldhours;
        prtCostSAddOn += value.prtextendedcost;
      }
    });

    ronumberArrC.map(obj => {
      if (obj) {
        ronumberSetC.add(obj);
      }
    });
    ronumberArrS.map(obj => {
      if (obj) {
        ronumberSetS.add(obj);
      }
    });
    ronumberArrAll.map(obj => {
      if (obj) {
        ronumberSetAll.add(obj);
      }
    });
    ronumberArrMPVI.map(obj => {
      if (obj) {
        ronumberSetMPICalc.add(obj);
      }
    });

    mpiCountArr.map(obj => {
      if (obj) {
        ronumberSetMPI.add(obj);
      }
    });

    menuCountArr.map(obj => {
      if (obj) {
        ronumberSetMenu.add(obj);
      }
    });
    var distinctROCount = ronumberSetC.size;

    var distinctROCountNonAddOn = ronumberSetAll.size - distinctROCount;
    var totalRevenueAddOn = lbrSaleAddOn + prtSaleAddOn;
    var totalRevenueNonAddOn = lbrSaleNonAddOn + prtSaleNonAddOn;
    var addOnRevenueperc =
      (totalRevenueAddOn / (totalLbrSaleAll + totalPrtSaleAll)) * 100;
    var nonAddOnRevenueperc =
      (totalRevenueNonAddOn / (totalLbrSaleAll + totalPrtSaleAll)) * 100;
    var ROCountPercAddOn = (distinctROCount / ronumberSetAll.size) * 100;
    var jobCountAddOnPerRo = jobCountAddOn.length / distinctROCount;
    var elrRepair = lbrSaleRAddOn / lbrhrsR;
    var elrCompetitive = lbrSaleCAddOn / lbrhrsC;
    var elrMaintenance = lbrSaleMAddOn / lbrhrsM;
    var elrShop = lbrSaleSAddOn / lbrhrsS;
    var MarkupR = prtSaleRAddOn / prtCostRAddOn;
    var MarkupM = prtSaleMAddOn / prtCostMAddOn;
    var MarkupC = prtSaleCAddOn / prtCostCAddOn;
    var MarkupS = prtSaleSAddOn / prtCostSAddOn;
    var menuPenetrationPerc = (
      (ronumberSetMenu.size / ronumberSetMPICalc.size) *
      100
    ).toFixed(2);
    var mpiROCountPercentage = (
      (ronumberSetMPI.size / ronumberSetMPICalc.size) *
      100
    ).toFixed(2);
    Params.mpiCount = ronumberSetMPI.size;
    Params.totalROCount = ronumberSetMPICalc.size;
    Params.mpiPercentage = mpiROCountPercentage + '%';
    Params.menuCount = ronumberSetMenu.size;
    Params.menuPercentage = menuPenetrationPerc + '%';
    Params.ronumberArrAll = ronumberArrAll.length;
    Params.jobCountNonAddOn =
      Math.round((jobCountNonAddOn.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOn =
      Math.round((jobCountAddOn.length + Number.EPSILON) * 100) / 100;

    Params.jobCountAddOnPerRo =
      Math.round((jobCountAddOnPerRo + Number.EPSILON) * 100) / 100;
    Params.ROCountPercAddOn =
      Math.round((ROCountPercAddOn + Number.EPSILON) * 100) / 100;

    Params.distinctROCountNonAddOn =
      Math.round((distinctROCountNonAddOn + Number.EPSILON) * 100) / 100;
    Params.distinctROCountAddOn =
      Math.round((distinctROCount + Number.EPSILON) * 100) / 100;
    Params.lbrSoldhrsNonAddon =
      Math.round((lbrSoldhrsNonAddon + Number.EPSILON) * 100) / 100;
    Params.lbrSoldhrsAddon =
      Math.round((lbrSoldhrsAddon + Number.EPSILON) * 100) / 100;
    Params.lbrSaleAddOn =
      '$' +
      (
        Math.round((lbrSaleAddOn + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.prtSaleAddOn =
      '$' +
      (
        Math.round((prtSaleAddOn + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.totalSaleAddOn =
      '$' +
      (
        Math.round((totalRevenueAddOn + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.jobCountAddOnR =
      Math.round((jobCountAddOnR.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOnC =
      Math.round((jobCountAddOnC.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOnM =
      Math.round((jobCountAddOnM.length + Number.EPSILON) * 100) / 100;
    Params.jobCountAddOnS =
      Math.round((jobCountAddOnS.length + Number.EPSILON) * 100) / 100;
    Params.lbrSaleRAddOn =
      '$' + lbrSaleRAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.lbrSaleCAddOn =
      '$' + lbrSaleCAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.lbrSaleMAddOn =
      '$' + lbrSaleMAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.lbrSaleSAddOn =
      '$' + lbrSaleSAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleRAddOn =
      '$' + prtSaleRAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleCAddOn =
      '$' + prtSaleCAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleMAddOn =
      '$' + prtSaleMAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.prtSaleSAddOn =
      '$' + prtSaleSAddOn.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    Params.elrRepairAddOn = Number(elrRepair)
      ? '$' + Math.round((elrRepair + Number.EPSILON) * 100) / 100
      : '$0.00';
    Params.elrCompetitiveAddOns = Number(elrCompetitive)
      ? '$' + Math.round((elrCompetitive + Number.EPSILON) * 100) / 100
      : '$0.00';
    Params.elrMaintenanceAddOn = Number(elrMaintenance)
      ? '$' + Math.round((elrMaintenance + Number.EPSILON) * 100) / 100
      : '$0.00';
    Params.elrShopAddOn = Number(elrShop)
      ? '$' + Math.round((elrShop + Number.EPSILON) * 100) / 100
      : '$0.00';

    Params.MarkupR = Number(MarkupR) ? MarkupR.toFixed(4) : '0.0000';
    Params.MarkupM = Number(MarkupM) ? MarkupM.toFixed(4) : '0.0000';
    Params.MarkupC = Number(MarkupC) ? MarkupC.toFixed(4) : '0.0000';
    Params.MarkupS = Number(MarkupS) ? MarkupS.toFixed(4) : '0.0000';
    Params.addOnRevenueperc =
      Math.round((addOnRevenueperc + Number.EPSILON) * 100) / 100 + '%';
    Params.nonAddOnRevenueperc =
      Math.round((nonAddOnRevenueperc + Number.EPSILON) * 100) / 100 + '%';
  }

  if (chartId == 1174 || chartId == 1175) {
    values.forEach(function(value) {
      totalLbrSaleAll += value.lbrsale;
      totalPrtSaleAll += value.prtextendedsale;
      SoldHoursTech += value.totalsoldhours
        ? value.totalsoldhours
        : value.soldhours;
      AvailableHours += value.totalavailablehrs
        ? value.totalavailablehrs
        : value.availablehrs;
      techArr.push(value.lbrtechno);
    });
  }

  var TotalSaleTech = totalLbrSaleAll + totalPrtSaleAll;
  AvgHrsTech = SoldHoursTech / AvailableHours;

  techArr.map(obj => {
    if (obj) {
      techNoSet.add(obj);
    }
  });
  var distinctTechCount = techNoSet.size;
  Params.totalSaleTech =
    '$' +
    (Math.round((TotalSaleTech + Number.EPSILON) * 100) / 100).toLocaleString();
  var AvgSalesPerTech = TotalSaleTech / distinctTechCount;
  Params.avgSalesPerTech =
    '$' +
    (
      Math.round((AvgSalesPerTech + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.soldHoursTech =
    Math.round((SoldHoursTech + Number.EPSILON) * 100) / 100;
  Params.availableHours =
    Math.round((AvailableHours + Number.EPSILON) * 100) / 100;
  Params.avgHrsTech = Math.round((AvgHrsTech + Number.EPSILON) * 100) / 100;
  Params.techCount = (
    Math.round((distinctTechCount + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  if (
    chartId != 988 &&
    chartId != 987 &&
    chartId != 986 &&
    chartId != 1066 &&
    chartId != 937 &&
    chartId != 1128 &&
    chartId != 1018 &&
    chartId != 1019 &&
    chartId != 1020 &&
    chartId != 1085 &&
    chartId != 1086 &&
    chartId != 1087 &&
    chartId != 1088 &&
    chartId != 1084 &&
    chartId != 1083 &&
    chartId != 1076 &&
    chartId != 1077 &&
    chartId != 1078 &&
    chartId != 1098 &&
    chartId != 1356 &&
    chartId != 1044 &&
    chartId != 949 &&
    chartId != 936 &&
    chartId != 1129 &&
    chartId != 1130 &&
    chartId != 1131 &&
    chartId != 1132 &&
    chartId != 1079 &&
    chartId != 1318
  ) {
    values.forEach(function(value) {
      if (value.paytypegroup == 'C') {
        LbrSale += parseFloat(value.lbrsale);
        LbrDiscount += parseFloat(value.apportionedlbrdiscount);
        TotalLbrDiscount += value.apportionedlbrdiscount
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
      } else if (value.paytypegroup == 'W') {
        LSWarranty += parseFloat(value.lbrsale);
      } else if (value.paytypegroup == 'I') {
        LSInternal += parseFloat(value.lbrsale);
      } else if (value.paytypegroup == 'M') {
        SaleMaintenance1 += parseFloat(value.lbrsale);
        CostMaintenance += parseFloat(value.lbrcost);
        SHMaintenance1 += parseFloat(value.lbrsoldhours);
      } else if (value.paytypegroup == 'E') {
        SaleExtended += parseFloat(value.lbrsale);
        CostExtended += parseFloat(value.lbrcost);
        SHExtended += parseFloat(value.lbrsoldhours);
      } else if (value.paytypegroup == 'F') {
        SaleFactory += parseFloat(value.lbrsale);
        CostFactory += parseFloat(value.lbrcost);
        SHFactory += parseFloat(value.lbrsoldhours);
      }
    });
  } else {
    values.forEach(function(value) {
      if (value.paytypegroup == 'C') {
        ronumberArrAll.push(value.ronumber);
        SaleAllCategoriesIncludeShop += parseFloat(value.lbrsale);
        LbrSale += parseFloat(value.lbrsale);
        laborHrsPerJob += parseFloat(value.lbrsoldhours);
        if (value.prtextendedsale != 0) {
          prtsHrsPerJob += parseFloat(value.lbrsoldhours);
          ronumberArrPrtsHours.push(value.ronumber);
        }
        totalJobCount.push(value.lbrlinecode);
      }
      if (value.opcategory == 'REPAIR' && value.paytypegroup == 'C') {
        SaleRepair += parseFloat(value.lbrsale);
        CostRepair += parseFloat(value.lbrcost);
        ronumberArrR.push(value.ronumber);
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        value.paytypegroup == 'C'
      ) {
        SaleMaintenance += parseFloat(value.lbrsale);
        CostMaintenances += parseFloat(value.lbrcost);
        ronumberArrM.push(value.ronumber);
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        value.paytypegroup == 'C'
      ) {
        SaleCompetitive += parseFloat(value.lbrsale);
        CostCompetitive += parseFloat(value.lbrcost);
        ronumberArrC.push(value.ronumber);
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        value.paytypegroup == 'C'
      ) {
        SaleShop += parseFloat(value.lbrsale);
        CostShop += parseFloat(value.lbrcost);
        ronumberArrS.push(value.ronumber);
      }
      if (
        value.opcategory == 'REPAIR' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrRS.push(value.ronumber);
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrMS.push(value.ronumber);
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrCS.push(value.ronumber);
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        value.paytypegroup == 'C' &&
        value.lbrsale != 0
      ) {
        ronumberArrSS.push(value.ronumber);
      }
    });
    SaleRepairCompetitive = SaleRepair;
    SaleCompMaint = SaleCompetitive + SaleMaintenance;

    var lbrHoursPerJob = laborHrsPerJob / totalJobCount.length;
    ronumberArrR.map(obj => {
      if (obj) {
        ronumberSetR.add(obj);
      }
    });
    ronumberArrM.map(obj => {
      if (obj) {
        ronumberSetM.add(obj);
      }
    });
    ronumberArrC.map(obj => {
      if (obj) {
        ronumberSetC.add(obj);
      }
    });
    ronumberArrS.map(obj => {
      if (obj) {
        ronumberSetS.add(obj);
      }
    });
    ronumberArrAll.map(obj => {
      if (obj) {
        ronumberSetAll.add(obj);
      }
    });
    ronumberArrPrtsHours.map(obj => {
      if (obj) {
        ronumberSetPrtsHours.add(obj);
      }
    });
    ronumberArrRS.map(obj => {
      if (obj) {
        ronumberSetRS.add(obj);
      }
    });
    ronumberArrMS.map(obj => {
      if (obj) {
        ronumberSetMS.add(obj);
      }
    });
    ronumberArrCS.map(obj => {
      if (obj) {
        ronumberSetCS.add(obj);
      }
    });
    ronumberArrSS.map(obj => {
      if (obj) {
        ronumberSetSS.add(obj);
      }
    });
    SaleAllCategories = SaleRepair + SaleCompetitive + SaleMaintenance;
  }

  var roCountR = ronumberSetR.size;
  var roCountM = ronumberSetM.size;
  var roCountC = ronumberSetC.size;
  var roCountS = ronumberSetS.size;
  var roCountRS = ronumberSetRS.size;
  var roCountMS = ronumberSetMS.size;
  var roCountCompS = ronumberSetCS.size;
  var roCountSS = ronumberSetSS.size;
  var roCountAll = ronumberSetAll.size;
  var roCountPrtsHours = ronumberSetPrtsHours.size;
  var laborSaleROR = SaleRepair.toFixed(2) / roCountRS;
  var laborSaleROM = SaleMaintenance.toFixed(2) / roCountMS;
  var laborSaleROC = SaleCompetitive.toFixed(2) / roCountCompS;
  var laborSaleROS = SaleShop.toFixed(2) / roCountSS;
  var lbrHoursPerRO = laborHrsPerJob / roCountAll;
  var prtsHoursPerRO = prtsHrsPerJob / roCountPrtsHours;

  Params.distinctRORS = (
    Math.round((roCountRS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROMS = (
    Math.round((roCountMS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROCompS = (
    Math.round((roCountCompS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROSS = (
    Math.round((roCountSS + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.roCountShop = (
    Math.round((roCountS + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.lbrHoursAll = (
    Math.round((laborHrsPerJob + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.roCountAllCat = (
    Math.round((roCountAll + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.laborSale = Math.round((LbrSale + Number.EPSILON) * 100) / 100;

  Params.laborSaleSum =
    '$' + (Math.round((LbrSale + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LsWarranty =
    '$' +
    (Math.round((LSWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LsInternal =
    '$' +
    (Math.round((LSInternal + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.saleRepair =
    '$' +
    (Math.round((SaleRepair + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.saleMaintenance =
    '$' +
    (
      Math.round((SaleMaintenance + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  Params.saleCompetitive =
    '$' +
    (
      Math.round((SaleCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.saleRepairCompetitive =
    '$' +
    (
      Math.round((SaleRepairCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.saleCompetMaint =
    '$' +
    (Math.round((SaleCompMaint + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.saleAllCategories =
    '$' +
    (
      Math.round((SaleAllCategories + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.saleShopSupplies =
    '$' +
    (Math.round((SaleShop + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoR =
    '$' +
    (Math.round((laborSaleROR + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoM =
    '$' +
    (Math.round((laborSaleROM + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoC =
    '$' +
    (Math.round((laborSaleROC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleperRoS =
    '$' +
    (Math.round((laborSaleROS + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrHoursPerRO = (
    Math.round((lbrHoursPerRO + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsHoursPerRO = (
    Math.round((prtsHoursPerRO + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsHours = (
    Math.round((prtsHrsPerJob + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsHrsRoCount = (
    Math.round((roCountPrtsHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.saleMaintenance1 =
    '$' +
    (
      Math.round((SaleMaintenance1 + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.costMaintenance =
    '$' +
    (
      Math.round((CostMaintenance + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.shMaintenance1 = (
    Math.round((SHMaintenance1 + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.saleExtended =
    '$' +
    (Math.round((SaleExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costExtended =
    '$' +
    (Math.round((CostExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.shExtended = (
    Math.round((SHExtended + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.saleFactory =
    '$' +
    (Math.round((SaleFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costFactory =
    '$' +
    (Math.round((CostFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.shFactory = (
    Math.round((SHFactory + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  values.forEach(function(value) {
    if (value.paytypegroup == 'C') {
      LbrCost += parseFloat(value.lbrcost);
    } else if (value.paytypegroup == 'W') {
      LCWarranty += parseFloat(value.lbrcost);
    } else if (value.paytypegroup == 'I') {
      LCInternal += parseFloat(value.lbrcost);
    }
  });
  Params.laborCostSum =
    '$' + (Math.round((LbrCost + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LcWarranty =
    '$' +
    (Math.round((LCWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.LcInternal =
    '$' +
    (Math.round((LCInternal + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costCompetitive =
    '$' +
    (
      Math.round((CostCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.costMaintenances =
    '$' +
    (
      Math.round((CostMaintenances + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  Params.costRepair =
    '$' +
    (Math.round((CostRepair + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.costShop =
    '$' +
    (Math.round((CostShop + Number.EPSILON) * 100) / 100).toLocaleString();
  if (
    chartId != 988 &&
    chartId != 987 &&
    chartId != 986 &&
    chartId != 1066 &&
    chartId != 937 &&
    chartId != 1128 &&
    chartId != 1018 &&
    chartId != 1019 &&
    chartId != 1020 &&
    chartId != 1080 &&
    chartId != 1081 &&
    chartId != 1082 &&
    chartId != 1083 &&
    chartId != 998 &&
    chartId != 1076 &&
    chartId != 1077 &&
    chartId != 1078 &&
    chartId != 937 &&
    chartId != 1128 &&
    chartId != 1098 &&
    chartId != 1356 &&
    chartId != 949 &&
    chartId != 1129 &&
    chartId != 1130 &&
    chartId != 1131 &&
    chartId != 1132 &&
    chartId != 1079
  ) {
    values.forEach(function(value) {
      if (value.paytypegroup == 'C') {
        LbrHours += parseFloat(value.lbrsoldhours);
        LbrTechHours += parseFloat(value.lbrtechhours);
        techHours += parseFloat(value.techhours);
        flatrateHours += parseFloat(value.flatratehours);
        JobCntCustomer.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'W') {
        SHWarranty += parseFloat(value.lbrsoldhours);
        JobCntWarranty.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'I') {
        SHInternal += parseFloat(value.lbrsoldhours);
        JobCntInteranl.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'M') {
        SHMaintenance += parseFloat(value.lbrsoldhours);
        JobCntMaintain.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'E' && chartId != 935) {
        SHExtended += parseFloat(value.lbrsoldhours);
        JobCntExtended.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'F' && chartId != 935) {
        SHFactory += parseFloat(value.lbrsoldhours);
        JobCntFactory.push(value.lbrlinecode);
      } else if (value.paytypegroup == 'B') {
        SHAll += parseFloat(value.lbrsoldhours);
      }
    });
  } else {
    values.forEach(function(value) {
      if (value.paytypegroup == 'C') {
        lbrlinecodeArr.push(value.lbrlinecode);
      }
      if (value.opcategory == 'REPAIR' && value.paytypegroup == 'C') {
        HoursRepair += parseFloat(value.lbrsoldhours);
        lbrlinecodeArrR.push(value.lbrlinecode);
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        value.paytypegroup == 'C'
      ) {
        HoursMaintenance += parseFloat(value.lbrsoldhours);
        lbrlinecodeArrM.push(value.lbrlinecode);
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        value.paytypegroup == 'C'
      ) {
        HoursCompetitive += parseFloat(value.lbrsoldhours);
        lbrlinecodeArrCO.push(value.lbrlinecode);
      } else if (
        value.opcategory == 'SHOP SUPPLIES' &&
        value.paytypegroup == 'C'
      ) {
        lbrlinecodeArrS.push(value.lbrlinecode);
      }
    });
  }

  var JobCountAllR = (lbrlinecodeArrR.length / lbrlinecodeArr.length) * 100;
  var JobCountAllM = (lbrlinecodeArrM.length / lbrlinecodeArr.length) * 100;
  var JobCountAllC = (lbrlinecodeArrCO.length / lbrlinecodeArr.length) * 100;
  var JobCountAllS = (lbrlinecodeArrS.length / lbrlinecodeArr.length) * 100;
  Params.jobCountAllR =
    Math.round((JobCountAllR + Number.EPSILON) * 100) / 100 + '%';
  Params.jobCountAllM =
    Math.round((JobCountAllM + Number.EPSILON) * 100) / 100 + '%';
  Params.jobCountAllC =
    Math.round((JobCountAllC + Number.EPSILON) * 100) / 100 + '%';
  Params.jobCountAllS =
    Math.round((JobCountAllS + Number.EPSILON) * 100) / 100 + '%';
  SHByCategory =
    LbrHours + SHWarranty + SHInternal + SHMaintenance + SHExtended + SHFactory;

  SHByCategoryC = Math.round((LbrHours / SHByCategory) * 100);
  SHByCategoryE = Math.round((SHExtended / SHByCategory) * 100);
  SHByCategoryI = Math.round((SHInternal / SHByCategory) * 100);
  SHByCategoryM = Math.round((SHMaintenance / SHByCategory) * 100);
  SHByCategoryW = Math.round((SHWarranty / SHByCategory) * 100);
  SHByCategoryF = Math.round((SHFactory / SHByCategory) * 100);

  Params.SHByCategory = (
    Math.round((SHByCategory + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  HoursRepairCompetitive = HoursRepair;
  HoursMaintCompet = HoursCompetitive + HoursMaintenance;
  HoursAllCategories = HoursRepair + HoursCompetitive + HoursMaintenance;
  var laborSaleROAll = SaleAllCategoriesIncludeShop / roCountAll;
  var laborHrsPerROR = HoursRepair / roCountR;
  var laborHrsPerROM = HoursMaintenance / roCountM;
  var laborHrsPerROC = HoursCompetitive / roCountC;
  var laborHrsPerRoAll = HoursAllCategories / roCountAll;

  ElrRepair = SaleRepair / HoursRepair;
  ElrMaintenance = SaleMaintenance / HoursMaintenance;
  ElrCompetitive = SaleCompetitive / HoursCompetitive;

  ElrRepairCompetitive = SaleRepairCompetitive / HoursRepairCompetitive;
  ElrCompMaint = SaleCompMaint / HoursMaintCompet;
  ElrAllCategories = SaleAllCategories / HoursAllCategories;

  var DeltaShFh = flatrateHours - flatrateHours;
  var DeltaThFh = techHours - flatrateHours;

  var LbrHrsPerRoRPrcnt = (HoursRepair / roCountR) * 100;
  var LbrHrsPerRoMPrcnt = (HoursMaintenance / roCountAll) * 100;
  var LbrHrsPerRoCPrcnt = (HoursCompetitive / roCountAll) * 100;

  Params.lbrHrsPerRoRPrcnt =
    Math.round((LbrHrsPerRoRPrcnt + Number.EPSILON) * 100) / 100 + '%';
  Params.lbrHrsPerRoMPrcnt =
    Math.round((LbrHrsPerRoMPrcnt + Number.EPSILON) * 100) / 100 + '%';
  Params.lbrHrsPerRoCPrcnt =
    Math.round((LbrHrsPerRoCPrcnt + Number.EPSILON) * 100) / 100 + '%';
  Params.roCountAll = (
    Math.round((roCountAll + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.flatrateHours = (
    Math.round((flatrateHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.techHours = (
    Math.round((techHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.deltaShFh = (
    Math.round((DeltaShFh + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.deltaThFh = (
    Math.round((DeltaThFh + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.sHByCategoryC =
    (
      Math.round((SHByCategoryC + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryE =
    (
      Math.round((SHByCategoryE + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryW =
    (
      Math.round((SHByCategoryW + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryI =
    (
      Math.round((SHByCategoryI + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryM =
    (
      Math.round((SHByCategoryM + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.sHByCategoryF =
    (
      Math.round((SHByCategoryF + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.roCountR = (
    Math.round((roCountR + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.elrRepair =
    '$' +
    (Math.round((ElrRepair + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.elrMaintenance =
    '$' +
    (
      Math.round((ElrMaintenance + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.elrCompetitive =
    '$' +
    (
      Math.round((ElrCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.elrRepairCompetitive =
    '$' +
    (
      Math.round((ElrRepairCompetitive + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  if (SaleCompMaint != 0 && HoursMaintCompet != 0) {
    Params.elrCompMaint =
      '$' +
      (
        Math.round((ElrCompMaint + Number.EPSILON) * 100) / 100
      ).toLocaleString();
  } else {
    Params.elrCompMaint = 0;
  }

  Params.elrAllCategories =
    '$' +
    (
      Math.round((ElrAllCategories + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.laborHoursSum = (
    Math.round((LbrHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.ShWarranty = (
    Math.round((SHWarranty + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.ShInternal = (
    Math.round((SHInternal + Number.EPSILON) * 100) / 100
  ).toFixed(2);
  Params.lbrTechHr = (
    Math.round((LbrTechHours + Number.EPSILON) * 100) / 100
  ).toFixed(2);
  Params.hoursRepair = (
    Math.round((HoursRepair + Number.EPSILON) * 100) / 100
  ).toFixed(2);
  Params.hoursMaintenance =
    Math.round((HoursMaintenance + Number.EPSILON) * 100) / 100;
  Params.hoursCompetitive =
    Math.round((HoursCompetitive + Number.EPSILON) * 100) / 100;
  Params.hoursRepairCompetitive =
    Math.round((HoursRepairCompetitive + Number.EPSILON) * 100) / 100;
  Params.hoursCompetMaint =
    Math.round((HoursMaintCompet + Number.EPSILON) * 100) / 100;
  Params.hoursAllCategories =
    Math.round((HoursAllCategories + Number.EPSILON) * 100) / 100;
  Params.jobCountR = (
    Math.round((lbrlinecodeArrR.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.jobCountM = (
    Math.round((lbrlinecodeArrM.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.jobCountCO = (
    Math.round((lbrlinecodeArrCO.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.jobCountS = (
    Math.round((lbrlinecodeArrS.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.lbrHrsPerRoR =
    Math.round((laborHrsPerROR + Number.EPSILON) * 100) / 100;
  Params.lbrHrsPerRoM =
    Math.round((laborHrsPerROM + Number.EPSILON) * 100) / 100;
  Params.lbrHrsPerRoC =
    Math.round((laborHrsPerROC + Number.EPSILON) * 100) / 100;
  Params.lbrHrsPerRoAll =
    Math.round((laborHrsPerRoAll + Number.EPSILON) * 100) / 100;
  Params.lbrHoursPerJob =
    Math.round((lbrHoursPerJob + Number.EPSILON) * 100) / 100;
  Params.lbrSaleperRoAll =
    '$' + Math.round((laborSaleROAll + Number.EPSILON) * 100) / 100;

  values.forEach(function(value) {
    if (
      (value.paytypegroup == 'C' || value.laborpaytypegroup == 'C') &&
      chartId != 1334 &&
      chartId != 1238 &&
      chartId != 1226 &&
      chartId != 1225 &&
      chartId != 916 &&
      chartId != 1227 &&
      chartId != 1228 &&
      chartId != 1229 &&
      chartId != 1230 &&
      chartId != 1231
    ) {
      PrtSale += parseFloat(value.prtextendedsale);
      PartsSale += parseFloat(value.prtssale);
      PrtCost += parseFloat(value.prtextendedcost);
      PrtsDiscount += parseFloat(value.apportionedlbrdiscount);
      TotalPrtsDiscount +=
        value.apportionedlbrdiscount != null
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
    } else if (
      (chartId == 1238 ||
        chartId == 1226 ||
        chartId == 1225 ||
        chartId == 916) &&
      value.partspaytypegroup == 'C' &&
      value.prtextendedsale != 0 &&
      value.prtextendedcost != 0
    ) {
      PrtSale += parseFloat(value.prtextendedsale);
      PartsSale += parseFloat(value.prtssale);
      PrtCost += parseFloat(value.prtextendedcost);
      PrtsDiscount += parseFloat(value.apportionedlbrdiscount);
      TotalPrtsDiscount +=
        value.apportionedlbrdiscount != null
          ? parseFloat(value.apportionedlbrdiscount)
          : 0;
    } else if (
      chartId == 1334 &&
      value.paytypegroup == 'C' &&
      (value.opcategory == 'REPAIR' || value.opcategory == 'COMPETITIVE')
    ) {
      PrtSale += parseFloat(value.prtextendedsale);
      PrtCost += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1227 || chartId == 1225) &&
      value.partspaytypegroup == 'W' &&
      value.prtextendedsale != 0 &&
      value.prtextendedcost != 0
    ) {
      PSWarranty += parseFloat(value.prtextendedsale);
      PCWarranty += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'W' &&
      chartId != 1227 &&
      chartId != 1225
    ) {
      PSWarranty += parseFloat(value.prtextendedsale);
      PCWarranty += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1228 || chartId == 1225) &&
      value.partspaytypegroup == 'I' &&
      value.prtextendedsale != 0 &&
      value.prtextendedcost != 0
    ) {
      PSInternal += parseFloat(value.prtextendedsale);
      PCInternal += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'I' &&
      chartId != 1228 &&
      chartId != 1225
    ) {
      PSInternal += parseFloat(value.prtextendedsale);
      PCInternal += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1229 || chartId == 1225) &&
      value.partspaytypegroup == 'M' &&
      value.prtextendedsale != 0 &&
      value.prtextendedcost != 0
    ) {
      PSMaintenancePrt += parseFloat(value.prtextendedsale);
      PCMaintenancePrt += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'M' &&
      chartId != 1229 &&
      chartId != 1225
    ) {
      PSMaintenanceP += parseFloat(value.prtextendedsale);
      PCMaintenanceP += parseFloat(value.prtextendedcost);
      PSMaintenancePrt += parseFloat(value.prtextendedsale);
      PCMaintenancePrt += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1230 || chartId == 1225) &&
      value.partspaytypegroup == 'E' &&
      value.prtextendedsale != 0 &&
      value.prtextendedcost != 0
    ) {
      PSExtendedPrt += parseFloat(value.prtextendedsale);
      PCExtendedPrt += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'E' &&
      chartId != 1230 &&
      chartId != 1225
    ) {
      PSExtended += parseFloat(value.prtextendedsale);
      PCExtended += parseFloat(value.prtextendedcost);
      PSExtendedPrt += parseFloat(value.prtextendedsale);
      PCExtendedPrt += parseFloat(value.prtextendedcost);
    } else if (
      (chartId == 1231 || chartId == 1225) &&
      value.partspaytypegroup == 'F' &&
      value.prtextendedsale != 0 &&
      value.prtextendedcost != 0
    ) {
      PSFactory += parseFloat(value.prtextendedsale);
      PCFactory += parseFloat(value.prtextendedcost);
    } else if (
      value.paytypegroup == 'F' &&
      chartId != 1231 &&
      chartId != 1225
    ) {
      PSFactory += parseFloat(value.prtextendedsale);
      PCFactory += parseFloat(value.prtextendedcost);
    }
  });
  LbrDiscount = Math.abs(LbrDiscount);
  TotalLbrDiscount = Math.abs(TotalLbrDiscount).toFixed(2);
  PrtsDiscount = Math.abs(PrtsDiscount);
  TotalPrtsDiscount = Math.abs(TotalPrtsDiscount);

  // Params.lbrDiscount = '$' + (Math.round((LbrDiscount + Number.EPSILON) * 100) / 100).toLocaleString();
  var TotalLbrSaleP = TotalLbrDiscount / LbrSale;
  var TotalPrtSaleP = TotalPrtsDiscount / PrtSale;
  // Params.totalLbrSaleP =
  // (Math.round((TotalLbrSaleP + Number.EPSILON) * 100) / 100).toLocaleString() + '%';
  // Params.totalPrtSaleP =
  // (Math.round((TotalPrtSaleP + Number.EPSILON) * 100) / 100).toLocaleString() + '%';
  Params.totallbrDiscount = TotalLbrDiscount;
  Params.totalprtsDiscount = TotalPrtsDiscount;

  Params.lbrDiscount =
    '$' +
    (Math.round((LbrDiscount + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsDiscount =
    '$' +
    (Math.round((PrtsDiscount + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.partsSale = Math.round((PartsSale + Number.EPSILON) * 100) / 100;
  Params.revPartsSaleSum =
    '$' + (Math.round((PrtSale + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.partsSaleSum =
    '$' + (Math.round((PrtSale + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PsWarranty =
    '$' +
    (Math.round((PSWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PsInternal =
    '$' +
    (Math.round((PSInternal + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.revPartsCostSum =
    '$' + (Math.round((PrtCost + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcWarranty =
    '$' +
    (Math.round((PCWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcInternal =
    '$' +
    (Math.round((PCInternal + Number.EPSILON) * 100) / 100).toLocaleString();

  Params.PsMaintenanceP =
    '$' +
    (
      Math.round((PSMaintenanceP + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.PcMaintenanceP =
    '$' +
    (
      Math.round((PCMaintenanceP + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.PsExtended =
    '$' +
    (Math.round((PSExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcExtended =
    '$' +
    (Math.round((PCExtended + Number.EPSILON) * 100) / 100).toLocaleString();

  if (chartId == 1230 || chartId == 1225) {
    Params.PsExtended =
      '$' +
      (
        Math.round((PSExtendedPrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.PcExtended =
      '$' +
      (
        Math.round((PCExtendedPrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
  }
  if (chartId == 1229 || chartId == 1225) {
    Params.PSMaintenanceP =
      '$' +
      (
        Math.round((PSMaintenancePrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
    Params.PcMaintenanceP =
      '$' +
      (
        Math.round((PCMaintenancePrt + Number.EPSILON) * 100) / 100
      ).toLocaleString();
  }
  Params.PsFactory =
    '$' +
    (Math.round((PSFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.PcFactory =
    '$' +
    (Math.round((PCFactory + Number.EPSILON) * 100) / 100).toLocaleString();
  Markup = PrtSale / PrtCost;
  Params.partsMarkup = (((Markup + Number.EPSILON) * 100) / 100).toFixed(4);
  Params.partsMarkupRC = (((Markup + Number.EPSILON) * 100) / 100).toFixed(4);

  LbrGP = LbrSale - LbrCost;
  GPWarranty = LSWarranty - LCWarranty;
  GPInternal = LSInternal - LCInternal;
  LbrSaleCombined =
    LbrSale +
    LSWarranty +
    LSInternal +
    SaleMaintenance1 +
    SaleExtended +
    SaleFactory;

  LbrCostCombined =
    LbrCost +
    LCWarranty +
    LCInternal +
    CostMaintenance +
    CostExtended +
    CostFactory;
  GPCombined = LbrSaleCombined - LbrCostCombined;
  GPMaintenanceP = SaleMaintenance1 - CostMaintenance;
  GPExtended = SaleExtended - CostExtended;
  var GPFactor = SaleFactory - CostFactory;

  var PartsToLaborRatio = PrtSale / LbrSale;

  Params.laborGPMP =
    '$' +
    (
      Math.round((GPMaintenanceP + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.laborGPE =
    '$' +
    (Math.round((GPExtended + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPF =
    '$' +
    (Math.round((GPFactor + Number.EPSILON) * 100) / 100).toLocaleString();

  Params.partsToLaborRatio =
    Math.round((PartsToLaborRatio + Number.EPSILON) * 100) / 100;

  Params.laborGP =
    '$' + (Math.round((LbrGP + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPW =
    '$' +
    (Math.round((GPWarranty + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPI =
    '$' +
    (Math.round((GPInternal + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborGPCombined =
    '$' +
    (Math.round((GPCombined + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborSaleCombined =
    '$' +
    (
      Math.round((LbrSaleCombined + Number.EPSILON) * 100) / 100
    ).toLocaleString();
  Params.laborCostCombined =
    '$' +
    (
      Math.round((LbrCostCombined + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  PrtGP = PrtSale - PrtCost;
  Params.partsGP =
    '$' + (Math.round((PrtGP + Number.EPSILON) * 100) / 100).toLocaleString();

  LbrGPPerc = (LbrGP / LbrSale) * 100;
  Params.laborGPPerc =
    (Math.round((LbrGPPerc + Number.EPSILON) * 10) / 10).toFixed(1) + '%';

  PrtGPPerc = (PrtGP / PrtSale) * 100;
  Params.partsGPPerc =
    (Math.round((PrtGPPerc + Number.EPSILON) * 10) / 10).toFixed(1) + '%';

  LsSumCombined = LbrSale + PrtSale;

  LaborPercetSumCombined =
    ((PrtSale + LbrSale - (PrtCost + LbrCost)) / (PrtSale + LbrSale)) * 100;
  LsGrossSumCombined = PrtGP + LbrGP;
  Params.laborSaleSumCombined =
    '$' +
    (Math.round((LsSumCombined + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.laborPercetSumCombined =
    (Math.round((LaborPercetSumCombined + Number.EPSILON) * 100) / 100).toFixed(
      1
    ) + '%';
  Params.lsGrossSumCombined =
    '$' +
    (
      Math.round((LsGrossSumCombined + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  ELR = LbrSale / LbrHours;
  Params.elr =
    '$' + (Math.round((ELR + Number.EPSILON) * 100) / 100).toLocaleString();
  LSCombined =
    LbrHours +
    SHWarranty +
    SHInternal +
    parseFloat(Params.shExtended) +
    SHMaintenance +
    parseFloat(Params.shFactory);

  Params.LsCombined = (
    Math.round((LSCombined + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  values.forEach(function(value) {
    if (
      value.paytypegroup == 'C' &&
      (value.opcategory == 'REPAIR' || value.opcategory == 'COMPETITIVE')
    ) {
      lbrSaleRC += value.lbrsale;
      lbrSoldhrsRC += value.lbrsoldhours;
    }
    ELRRepairCompet =
      Math.round((lbrSaleRC / lbrSoldhrsRC + Number.EPSILON) * 100) / 100;
  });
  Params.lbrSaleRepairCompet =
    '$' +
    (Math.round((lbrSaleRC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrHrsRepairCompet = (
    Math.round((lbrSoldhrsRC + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.elrRepairCompet =
    '$' +
    (
      Math.round((ELRRepairCompet + Number.EPSILON) * 100) / 100
    ).toLocaleString();

  PSSumComp =
    PrtSale +
    PSWarranty +
    PSInternal +
    PSMaintenancePrt +
    PSExtendedPrt +
    PSFactory;
  PCSumComp =
    PrtCost +
    PCWarranty +
    PCInternal +
    PCMaintenancePrt +
    PCExtendedPrt +
    PCFactory;
  PrtGpWar = PSWarranty - PCWarranty;
  PrtGpInt = PSInternal - PCInternal;
  PrtGpMP = PSMaintenancePrt - PCMaintenancePrt;
  prtGpE = PSExtendedPrt - PCExtendedPrt;
  PrtGpF = PSFactory - PCFactory;

  PrtGpCombin = PrtGpWar + PrtGpInt + PrtGP + PrtGpMP + prtGpE + PrtGpF;

  Params.prtGpMP =
    '$' + (Math.round((PrtGpMP + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpE =
    '$' + (Math.round((prtGpE + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.psSumComp =
    '$' +
    (Math.round((PSSumComp + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.pcSumComp =
    '$' +
    (Math.round((PCSumComp + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpWar =
    '$' +
    (Math.round((PrtGpWar + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpInt =
    '$' +
    (Math.round((PrtGpInt + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpCombin =
    '$' +
    (Math.round((PrtGpCombin + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtGpF =
    '$' + (Math.round((PrtGpF + Number.EPSILON) * 100) / 100).toLocaleString();
  values.forEach(function(value) {
    if (value.opcategory == 'REPAIR' && value.paytypegroup == 'C') {
      PrtsaleR += parseFloat(value.prtextendedsale);
      PrtcostR += parseFloat(value.prtextendedcost);
    } else if (value.opcategory == 'MAINTENANCE' && value.paytypegroup == 'C') {
      PrtsaleM += parseFloat(value.prtextendedsale);
      PrtcostM += parseFloat(value.prtextendedcost);
    } else if (value.opcategory == 'COMPETITIVE' && value.paytypegroup == 'C') {
      PrtsaleComp += parseFloat(value.prtextendedsale);
      PrtcostComp += parseFloat(value.prtextendedcost);
    }
  });
  var PrtsToLbrRatioR = PrtsaleR / SaleRepair;
  var PrtsToLbrRatioM = PrtsaleM / SaleMaintenance;
  var PrtsToLbrRatioC = PrtsaleComp / SaleCompetitive;
  Params.prtsToLbrRatioR = (
    Math.round((PrtsToLbrRatioR + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsToLbrRatioM = (
    Math.round((PrtsToLbrRatioM + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.prtsToLbrRatioC = (
    Math.round((PrtsToLbrRatioC + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.prtsaleR =
    '$' +
    (Math.round((PrtsaleR + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleM =
    '$' +
    (Math.round((PrtsaleM + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleComp =
    '$' +
    (Math.round((PrtsaleComp + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostR =
    '$' +
    (Math.round((PrtcostR + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostM =
    '$' +
    (Math.round((PrtcostM + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostComp =
    '$' +
    (Math.round((PrtcostComp + Number.EPSILON) * 100) / 100).toLocaleString();
  MarkupR = PrtsaleR / PrtcostR;
  MarkupM = PrtsaleM / PrtcostM;
  MarkupC = PrtsaleComp / PrtcostComp;
  MarkupCmb =
    (PrtsaleR + PrtsaleM + PrtsaleComp) / (PrtcostR + PrtcostM + PrtcostComp);
  Params.partsMarkupR = (
    Math.round((MarkupR + Number.EPSILON) * 100) / 100
  ).toFixed(4);
  Params.partsMarkupM = (
    Math.round((MarkupM + Number.EPSILON) * 100) / 100
  ).toFixed(4);
  Params.partsMarkupC = (
    Math.round((MarkupC + Number.EPSILON) * 100) / 100
  ).toFixed(4);
  Params.partsMarkupCmb = (
    Math.round((MarkupCmb + Number.EPSILON) * 100) / 100
  ).toFixed(4);

  values.forEach(function(value) {
    if (
      value.paytypegroup == 'I' ||
      value.paytypegroup == 'W' ||
      value.paytypegroup == 'C' ||
      value.paytypegroup == 'M' ||
      value.paytypegroup == 'E' ||
      value.paytypegroup == 'F'
    ) {
      lbrsaleC += value.lbrsale;
      lbrcostC += value.lbrcost;
      JobCntCombined.push(value.lbrlinecode);
      // PrtsaleAll += value.prtextendedsale;
      // PrtcostAll += value.prtextendedcost;
      // lbrlinecodeArrC.push(value.lbrlinecode);
      // ronumberArrC.push(value.ronumber);
    }
  });

  ElrCombined = lbrsaleC / LSCombined;
  ElrCustomer = LbrSale / LbrHours;
  ElrWarranty = LSWarranty / SHWarranty;
  ElrInternal = LSInternal / SHInternal;
  ElrMaintenance = SaleMaintenance1 / SHMaintenance1;
  ElrExtended = SaleExtended / parseFloat(Params.shExtended);
  ElrFactory = SaleFactory / parseFloat(Params.shFactory);
  Params.lbrSaleC =
    '$' +
    (Math.round((lbrsaleC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrCostC =
    '$' +
    (Math.round((lbrcostC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.ElrCombined =
    '$' + ElrCombined.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrCustomer =
    '$' + ElrCustomer.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrWarranty =
    '$' + ElrWarranty.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrInternal =
    '$' + ElrInternal.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrMaintenance =
    '$' + ElrMaintenance.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrExtended =
    '$' + ElrExtended.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.ElrFactory =
    '$' + ElrFactory.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  Params.JobCntCombined = (
    Math.round((JobCntCombined.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntCustomer = (
    Math.round((JobCntCustomer.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntWarranty = (
    Math.round((JobCntWarranty.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntInteranl = (
    Math.round((JobCntInteranl.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntMaintain = (
    Math.round((JobCntMaintain.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntExtended = (
    Math.round((JobCntExtended.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.JobCntFactory = (
    Math.round((JobCntFactory.length + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  prtsMarkupCombined = PSSumComp / PCSumComp;
  revPrtsMarkupCustomer = PrtSale / PrtCost;
  prtsMarkupWarranty = PSWarranty / PCWarranty;
  prtsMarkupInteranl = PSInternal / PCInternal;
  prtsMarkupMaintain = PSMaintenancePrt / PCMaintenancePrt;
  prtsMarkupExtended = PSExtendedPrt / PCExtendedPrt;
  prtsMarkupFactory = PSFactory / PCFactory;
  Params.prtsMarkupCombined = prtsMarkupCombined.toFixed(4);
  Params.revPrtsMarkupCustomer = revPrtsMarkupCustomer.toFixed(4);
  Params.prtsMarkupWarranty = prtsMarkupWarranty.toFixed(4);
  Params.prtsMarkupInteranl = prtsMarkupInteranl.toFixed(4);
  Params.prtsMarkupMaintain = prtsMarkupMaintain.toFixed(4);
  Params.prtsMarkupExtended = prtsMarkupExtended.toFixed(4);
  Params.prtsMarkupFactory = prtsMarkupFactory.toFixed(4);
  if (
    chartId == 1239 ||
    chartId == 1240 ||
    chartId == 1241 ||
    chartId == 1242 ||
    chartId == 1359 ||
    chartId == 1360 ||
    chartId == 1361 ||
    chartId == 1362
  ) {
    values.forEach(function(value) {
      if (
        (chartId == 1239 ||
          chartId == 1241 ||
          chartId == 1240 ||
          chartId == 1360) &&
        value.lbrPaytype == 'C'
      ) {
        shopSuppliesC += value.customerpayshopsup;
      } else if (
        (chartId == 1242 || chartId == 1240 || chartId == 1362) &&
        value.lbrPaytype == 'I'
      ) {
        shopSuppliesI += value.internalshopsup;
      } else if (chartId == 1240) {
        shopSuppliesCombined = shopSuppliesC + shopSuppliesI;
      }
    });
  }

  Params.shopSuppliesC =
    '$' + shopSuppliesC.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.shopSuppliesI =
    '$' + shopSuppliesI.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.shopSuppliesCombined =
    '$' + shopSuppliesCombined.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  return Params;
}
export function distinctROCount(rowData, chartId, filterValues, allData) {
  var values;
  if (typeof filterValues != 'undefined') {
    values = rowData;
  } else {
    values = allData;
  }
  var ronumberArr = [];
  var lbrlinecodeArr = [];
  var lbrsale = 0;
  var ronumberArrW = [];
  var lbrlinecodeArrW = [];
  var lbrsaleW = 0;
  var ronumberArrI = [];
  var lbrlinecodeArrI = [];
  var lbrsaleI = 0;
  var ronumberArrC = [];
  var lbrlinecodeArrC = [];
  var jobcodeArrC = [];
  var lbrsaleC = 0;
  var lbrcostC = 0;
  var prtsale = 0;
  var ronumberArrR = [];
  var ronumberCombined = [];
  var ronumberArrM = [];
  var ronumberArrComp = [];
  var ronumberArrCmb = [];
  var ronumberArrCmbPerRo = [];
  var RODiscountArr = [];
  var LineDiscountArr = [];
  var LopDiscountArr = [];
  var ronumberArrMn = [];
  var ronumberArrE = [];
  var ronumberArrF = [];
  var PrtsaleR = 0;
  var PrtsaleM = 0;
  var PrtsaleComp = 0;
  var PrtsaleCmb = 0;
  var PrtcostCmb = 0;
  var PrtsaleC = 0;
  var PrtsaleI = 0;
  var PrtsaleW = 0;
  var PrtsaleAll = 0;
  var PrtcostAll = 0;
  var RODiscount = 0;
  var LineDiscount = 0;
  var HoursRepair = 0;
  var HoursMaintenance = 0;
  var HoursCompetitive = 0;
  var LbrHours = 0;
  var SHWarranty = 0;
  var SHInternal = 0;
  var SHMaintenance = 0;
  var SHExtended = 0;
  var SHFactory = 0;
  var DiscountJobArr = [];
  const ronumberSet = new Set();
  const ronumberSetCombined = new Set();
  const ronumberSetW = new Set();
  const ronumberSetI = new Set();
  const ronumberSetC = new Set();
  const ronumberSetR = new Set();
  const ronumberSetM = new Set();
  const ronumberSetComp = new Set();
  const ronumberSetCmb = new Set();
  const ronumberSetCmbPerRo = new Set();
  const ronumberSetDiscount = new Set();
  const jobCountSetDiscount = new Set();
  const jobCodeSetDiscount = new Set();
  const closedDateMap = new Map();
  const closedDateDiscountsMap = new Map();
  const closedDateDiscountsROMap = new Map();
  const closedDateDiscountsLopMap = new Map();

  var LHROCombined = 0;
  var LHROCustomer = 0;
  var LHROWarranty = 0;
  var LHROInteranl = 0;
  var LHROMaintain = 0;
  var LHROExtended = 0;
  var LHROFactory = 0;
  var LSaleROCombined = 0;
  var LSaleROCustomer = 0;
  var LSaleROWarranty = 0;
  var LSaleROInteranl = 0;
  var LSaleROMaintain = 0;
  var LSaleROExtended = 0;
  var LSaleROFactory = 0;
  var LbrHours = 0;
  var LSWarranty = 0;
  var LSInternal = 0;
  var SaleMaintenance1 = 0;
  var SaleExtended = 0;
  var SaleFactory = 0;
  var PSSumComp = 0;
  var PrtSale = 0;
  var PartsSale = 0;
  var PSWarranty = 0;
  var PSInternal = 0;
  var PSMaintenanceP = 0;
  var PSExtended = 0;
  var PSFactory = 0;
  var prtsRevenueRoCombined = 0;
  var prtsRevenueRoCustomer = 0;
  var prtsRevenueRoWarranty = 0;
  var prtsRevenueRoInteranl = 0;
  var prtsRevenueRoMaintain = 0;
  var prtsRevenueRoExtended = 0;
  var prtsRevenueRoFactory = 0;
  let totalDistinctRonumberCount = 0;

  let Params = {
    prtsaleM: '',
    prtsaleR: '',
    prtsaleComp: '',
    prtsaleCmb: '',
    prtsaleAll: '',
    prtcostAll: '',
    prtcostCmb: '',
    lbrSaleC: '',
    lbrCostC: '',
    distinctRO: '',
    distinctROW: '',
    distinctROI: '',
    distinctROC: '',
    distinctROR: '',
    distinctROM: '',
    distinctROComp: '',
    distinctROCmb: '',
    distinctROCmbPerRo: '',
    lbrSaleperRo: '',
    prtSaleperRo: '',
    jobCount: '',
    prtRoByM: '',
    prtRoByR: '',
    prtRoByComp: '',
    prtRoByCmb: '',
    distinctROPercent: '',
    roCountR: '',
    roDiscount: '',

    discountJobcount: '',
    jobCountAddOn: '',
    jobCountNonAddOn: '',
    roDiscount: '',
    lineDiscount: '',
    lopDiscount: '',
    ronumberArr: '',
    distinctRORS: '',
    distinctROMS: '',
    distinctROCompS: '',
    ronumberArrJobs: '',
    roMaintenance: '',
    roExtended: '',
    roFactory: '',
    lbrHrsPerRoRPrcnt: '',
    lbrHrsPerRoMPrcnt: '',
    lbrHrsPerRoCPrcnt: '',
    LsCombined: '',
    laborHoursSum: '',
    ShWarranty: '',
    ShInternal: '',
    shMaintenance1: '',
    shExtended: '',
    shFactory: '',
    LHROCombined: '',
    LHROCustomer: '',
    LHROWarranty: '',
    LHROInteranl: '',
    LHROMaintain: '',
    LHROExtended: '',
    LHROFactory: '',
    LSaleROCombined: '',
    LSaleROCustomer: '',
    LSaleROWarranty: '',
    LSaleROInteranl: '',
    LSaleROMaintain: '',
    LSaleROExtended: '',
    LSaleROFactory: '',
    laborSaleSum: '',
    LSWarranty: '',
    LSInternal: '',
    SaleMaintenance1: '',
    SaleExtended: '',
    SaleFactory: '',
    lbrSaleC: '',
    psSumComp: '',
    partsSaleSum: '',
    PsWarranty: '',
    PsInternal: '',
    prtsaleM: '',
    PsExtended: '',
    PsFactory: '',
    prtsRevenueRoCombined: '',
    prtsRevenueRoCustomer: '',
    prtsRevenueRoWarranty: '',
    prtsRevenueRoInteranl: '',
    prtsRevenueRoMaintain: '',
    prtsRevenueRoExtended: '',
    prtsRevenueRoFactory: '',
    distinctROPartsOnly: ''
  };
  if (
    chartId != 968 &&
    chartId != 1074 &&
    chartId != 1012 &&
    chartId != 1013 &&
    chartId != 1014 &&
    chartId != 1015 &&
    chartId != 937 &&
    chartId != 1128 &&
    chartId != 1129 &&
    chartId != 1130 &&
    chartId != 1131 &&
    chartId != 1132 &&
    chartId != 988 &&
    chartId != 987 &&
    chartId != 986 &&
    chartId != 1066 &&
    chartId != 1134 &&
    chartId != 1129 &&
    chartId != 1130 &&
    chartId != 1131 &&
    chartId != 1132 &&
    chartId != 1139 &&
    chartId != 1144 &&
    chartId != 1197 &&
    chartId != 1211 &&
    chartId != 1218 &&
    chartId != 1327 &&
    chartId != 1319
  ) {
    var dashboardName = getDbdName(chartId);
    var parentId = getChartParentId(chartId);
    values.forEach(function(value) {
      if (
        (((dashboardName == 'CP Overview' ||
          dashboardName == 'CP Labor Overview' ||
          dashboardName == 'CP Parts Overview') &&
          // ||
          //(dashboardName == 'Special Metrics' && chartId == 923)
          parentId == null) ||
          parentId == 942 ||
          parentId == 939 ||
          parentId == 940 ||
          chartId == 930 ||
          chartId == 936 ||
          chartId == 1123 ||
          chartId == 925 ||
          chartId == 1124 ||
          chartId == 1126) &&
        (value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M' ||
          value.partspaytypegroup == 'C' ||
          value.partspaytypegroup == 'E' ||
          value.partspaytypegroup == 'M')
      ) {
        lbrsale += parseFloat(value.lbrsale);
        PrtsaleC += parseFloat(value.prtextendedsale);
        prtsale += parseFloat(value.prtextendedsale);
        PartsSale += parseFloat(value.prtssale);
        if (
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArr.push(value.ronumber);
          // check distinct RO based on distinct closeddate
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
          lbrlinecodeArr.push(value.lbrlinecode);
        }
        if (
          value.dislevel == 'RO' &&
          chartId != 1115 &&
          chartId != 1235 &&
          value.lbrsale != 0
        ) {
          RODiscountArr.push(value.ronumber);
          if (!closedDateDiscountsROMap.has(value.closeddate)) {
            closedDateDiscountsROMap.set(value.closeddate, new Set());
          }
          closedDateDiscountsROMap.get(value.closeddate).add(value.ronumber);
        } else if (
          value.dislevel == 'LINE' &&
          chartId != 1115 &&
          chartId != 1235 &&
          value.lbrsale != 0
        ) {
          LineDiscountArr.push(value.ronumber);
          if (!closedDateDiscountsMap.has(value.closeddate)) {
            closedDateDiscountsMap.set(value.closeddate, new Set());
          }
          closedDateDiscountsMap.get(value.closeddate).add(value.ronumber);
          // LineDiscountArr.push(value.dislinecode);
        } else if (
          value.dislevel == 'LOP' &&
          chartId != 1115 &&
          chartId != 1235 &&
          value.lbrsale != 0
        ) {
          LopDiscountArr.push(value.ronumber);
          if (!closedDateDiscountsLopMap.has(value.closeddate)) {
            closedDateDiscountsLopMap.set(value.closeddate, new Set());
          }
          closedDateDiscountsLopMap.get(value.closeddate).add(value.ronumber);
          // LineDiscountArr.push(value.dislinecode);
        }
      } else if (
        value.paytypegroup == 'C' ||
        value.lbrPaytype == 'C' ||
        ((chartId == 1124 || chartId == 1123) &&
          (value.lbrPaytype == 'C' ||
            value.lbrPaytype == 'E' ||
            value.lbrPaytype == 'M'))
      ) {
        lbrsale += parseFloat(value.lbrsale);
        PrtsaleC += parseFloat(value.prtextendedsale);
        prtsale += parseFloat(value.prtextendedsale);
        PartsSale += parseFloat(value.prtssale);

        if (chartId == 1142) {
          if (!closedDateMap.has(value.closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(value.closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(value.closeddate).add(value.ronumber);
          ronumberArr.push(value.ronumber);
        }
        if (
          chartId != 1142 &&
          chartId != 1200 &&
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          if (!closedDateMap.has(value.closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(value.closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(value.closeddate).add(value.ronumber);
          ronumberArr.push(value.ronumber);
        }
        lbrlinecodeArr.push(value.lbrlinecode);
        if (
          value.dislevel == 'RO' &&
          chartId != 1115 &&
          chartId != 1235 &&
          value.lbrsale != 0
        ) {
          RODiscountArr.push(value.ronumber);
          if (!closedDateDiscountsROMap.has(value.closeddate)) {
            closedDateDiscountsROMap.set(value.closeddate, new Set());
          }
          closedDateDiscountsROMap.get(value.closeddate).add(value.ronumber);
        } else if (
          value.dislevel == 'LINE' &&
          chartId != 1115 &&
          chartId != 1235 &&
          value.lbrsale != 0
        ) {
          LineDiscountArr.push(value.ronumber);

          if (!closedDateDiscountsMap.has(value.closeddate)) {
            closedDateDiscountsMap.set(value.closeddate, new Set());
          }
          closedDateDiscountsMap.get(value.closeddate).add(value.ronumber);

          // LineDiscountArr.push(value.dislinecode);
        } else if (
          value.dislevel == 'LOP' &&
          chartId != 1115 &&
          chartId != 1235 &&
          value.lbrsale != 0
        ) {
          LopDiscountArr.push(value.ronumber);
          if (!closedDateDiscountsLopMap.has(value.closeddate)) {
            closedDateDiscountsLopMap.set(value.closeddate, new Set());
          }
          closedDateDiscountsLopMap.get(value.closeddate).add(value.ronumber);
          // LineDiscountArr.push(value.dislinecode);
        }
      } else if (value.paytypegroup == 'W') {
        lbrsaleW += parseFloat(value.lbrsale);
        PrtsaleW += parseFloat(value.prtextendedsale);
        lbrlinecodeArrW.push(value.lbrlinecode);
        if (chartId == 1142) {
          if (!closedDateMap.has(value.closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(value.closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(value.closeddate).add(value.ronumber);
          ronumberArr.push(value.ronumber);
        }
        if (
          chartId != 1142 &&
          chartId != 1200 &&
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrW.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      } else if (value.paytypegroup == 'I') {
        lbrsaleI += parseFloat(value.lbrsale);
        PrtsaleI += parseFloat(value.prtextendedsale);
        lbrlinecodeArrI.push(value.lbrlinecode);
        if (chartId == 1142) {
          if (!closedDateMap.has(value.closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(value.closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(value.closeddate).add(value.ronumber);
          ronumberArr.push(value.ronumber);
        }
        if (
          chartId != 1142 &&
          chartId != 1200 &&
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrI.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      } else if (value.paytypegroup == 'M') {
        if (chartId == 1142) {
          if (!closedDateMap.has(value.closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(value.closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(value.closeddate).add(value.ronumber);
          ronumberArr.push(value.ronumber);
        }
        if (
          chartId != 1142 &&
          chartId != 1200 &&
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrMn.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      } else if (value.paytypegroup == 'E') {
        if (chartId == 1142) {
          if (!closedDateMap.has(value.closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(value.closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(value.closeddate).add(value.ronumber);
          ronumberArr.push(value.ronumber);
        }
        if (
          chartId != 1142 &&
          chartId != 1200 &&
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrE.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      } else if (value.paytypegroup == 'F') {
        if (chartId == 1142) {
          if (!closedDateMap.has(value.closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(value.closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(value.closeddate).add(value.ronumber);
          ronumberArr.push(value.ronumber);
        }
        if (
          chartId != 1142 &&
          chartId != 1200 &&
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrF.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      }
    });
  } else if (
    chartId != 1012 &&
    chartId != 1013 &&
    chartId != 1014 &&
    chartId != 1015
  ) {
    values.forEach(function(value) {
      if (
        value.paytypegroup == 'I' ||
        value.paytypegroup == 'W' ||
        value.paytypegroup == 'C' ||
        value.paytypegroup == 'M' ||
        value.paytypegroup == 'E' ||
        value.paytypegroup == 'F'
      ) {
        lbrsaleC += parseFloat(value.lbrsale);
        lbrcostC += parseFloat(value.lbrcost);
        PrtsaleAll += parseFloat(value.prtextendedsale);
        PrtcostAll += parseFloat(value.prtextendedcost);
        lbrlinecodeArrC.push(value.lbrlinecode);

        if (chartId == 1327) {
          if (
            !(
              value.lbrsale == 0 &&
              value.prtextendedsale == 0 &&
              value.lbrsoldhours == 0 &&
              value.prtextendedcost == 0
            )
          ) {
            ronumberArrC.push(value.ronumber);
            if (!closedDateMap.has(value.closeddate)) {
              closedDateMap.set(value.closeddate, new Set());
            }
            closedDateMap.get(value.closeddate).add(value.ronumber);
          }
        } else {
          if (
            chartId != 1142 &&
            chartId != 1200 &&
            !(
              chartId != 1139 &&
              chartId != 1197 &&
              chartId != 1211 &&
              chartId != 1144 &&
              value.lbrsale == 0 &&
              value.prtextendedsale == 0 &&
              value.lbrsoldhours == 0 &&
              value.prtextendedcost == 0
            )
          ) {
            ronumberArrC.push(value.ronumber);
            if (!closedDateMap.has(value.closeddate)) {
              closedDateMap.set(value.closeddate, new Set());
            }
            closedDateMap.get(value.closeddate).add(value.ronumber);
          }
        }
      }
    });
  }
  if (
    chartId != 1012 &&
    chartId != 1091 &&
    chartId != 1098 &&
    chartId != 1138
  ) {
    values.forEach(function(value) {
      if (value.opcategory == 'REPAIR' && value.paytypegroup == 'C') {
        PrtsaleR += parseFloat(value.prtextendedsale);
        HoursRepair += parseFloat(value.lbrsoldhours);
        if (
          !(
            chartId != 1139 &&
            chartId != 1197 &&
            chartId != 1211 &&
            chartId != 1142 &&
            chartId != 1200 &&
            chartId != 1144 &&
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrR.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      } else if (
        value.opcategory == 'MAINTENANCE' &&
        value.paytypegroup == 'C'
      ) {
        PrtsaleM += parseFloat(value.prtextendedsale);
        HoursMaintenance += parseFloat(value.lbrsoldhours);
        if (
          !(
            chartId != 1139 &&
            chartId != 1197 &&
            chartId != 1211 &&
            chartId != 1142 &&
            chartId != 1200 &&
            chartId != 1144 &&
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrM.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      } else if (
        value.opcategory == 'COMPETITIVE' &&
        value.paytypegroup == 'C'
      ) {
        PrtsaleComp += parseFloat(value.prtextendedsale);
        HoursCompetitive += parseFloat(value.lbrsoldhours);
        if (
          !(
            chartId != 1139 &&
            chartId != 1197 &&
            chartId != 1211 &&
            chartId != 1142 &&
            chartId != 1200 &&
            chartId != 1144 &&
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrComp.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      }
    });
  } else if (chartId != 1098 && chartId != 1012) {
    values.forEach(function(value) {
      if (
        (value.opcategory == 'REPAIR' ||
          value.opcategory == 'MAINTENANCE' ||
          value.opcategory == 'COMPETITIVE') &&
        value.paytypegroup == 'C'
      ) {
        PrtsaleCmb += parseFloat(value.prtextendedsale);
        PrtcostCmb += parseFloat(value.prtextendedcost);
        if (
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrCmb.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      }
    });
  } else if (chartId == 1012) {
    values.forEach(function(value) {
      if (
        (value.opcategory == 'REPAIR' ||
          value.opcategory == 'MAINTENANCE' ||
          value.opcategory == 'COMPETITIVE') &&
        value.paytypegroup == 'C'
      ) {
        PrtsaleCmb += parseFloat(value.prtextendedsale);
        PrtcostCmb += parseFloat(value.prtextendedcost);
      }
    });
    values.forEach(function(value) {
      if (
        (value.opcategory == 'REPAIR' ||
          value.opcategory == 'MAINTENANCE' ||
          value.opcategory == 'COMPETITIVE') &&
        value.paytypegroup == 'C' &&
        value.prtextendedsale != 0
      ) {
        if (
          !(
            value.lbrsale == 0 &&
            value.prtextendedsale == 0 &&
            value.lbrsoldhours == 0 &&
            value.prtextendedcost == 0
          )
        ) {
          ronumberArrCmbPerRo.push(value.ronumber);
          if (!closedDateMap.has(value.closeddate)) {
            closedDateMap.set(value.closeddate, new Set());
          }
          closedDateMap.get(value.closeddate).add(value.ronumber);
        }
      }
    });
  }
  var JobCountArr = 0;
  DiscountJobArr.map(item => {
    JobCountArr = JobCountArr + item.toString().split(' ').length;
  });

  var DistinctCPPROR = ronumberArrR.length;
  var DistinctCPPROM = ronumberArrM.length;
  var DistinctCPPROComp = ronumberArrComp.length;
  const RODiscountSet = new Set();
  const lineDiscountSet = new Set();
  const lopDiscountSet = new Set();
  const DiscountSet = new Set();
  const jobDiscountSet = new Set();
  const ronumberSetMn = new Set();
  const ronumberSetE = new Set();
  const ronumberSetF = new Set();
  RODiscountArr.map(obj => {
    if (obj) {
      RODiscountSet.add(obj);
    }
  });
  LineDiscountArr.map(obj => {
    if (obj) {
      lineDiscountSet.add(obj);
    }
  });
  LopDiscountArr.map(obj => {
    if (obj) {
      lopDiscountSet.add(obj);
    }
  });
  ronumberArr.map(obj => {
    if (obj) {
      DiscountSet.add(obj);
    }
  });
  var roCountRODiscount = 0;
  var roCountLineDiscount = 0;
  var roCountLopDiscount = 0;

  closedDateDiscountsMap.forEach(set => {
    roCountLineDiscount += set.size;
  });
  closedDateDiscountsROMap.forEach(set => {
    roCountRODiscount += set.size;
  });
  closedDateDiscountsLopMap.forEach(set => {
    roCountLopDiscount += set.size;
  });
  // Params.discountJob = DiscountJobArr.length / ronumberArr.length;
  Params.discountJob = JobCountArr / DiscountSet.size;
  Params.ronumberArrJobs = ronumberArr.length;
  ronumberArr.map(obj => {
    if (obj) {
      ronumberSetDiscount.add(obj);
    }
  });
  lbrlinecodeArr.map(obj => {
    if (obj) {
      jobCountSetDiscount.add(obj);
    }
  });
  var discountROPerc = 0;
  closedDateMap.forEach(set => {
    discountROPerc += set.size;
  });
  // var discountROPerc = ronumberSetDiscount.size;
  var discountJob = jobCodeSetDiscount.size;
  Params.discountROPerc = discountROPerc;
  Params.ronumberArr = discountROPerc;

  //Params.discountJob = DiscountJobArr.length / ronumberArr.length;
  Params.distinctCPPROR = (
    Math.round((DistinctCPPROR + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.distinctCPPROM = (
    Math.round((DistinctCPPROM + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctCPPROComp = (
    Math.round((DistinctCPPROComp + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  // Params.roDiscount = (
  //   Math.round((RODiscountSet.size + Number.EPSILON) * 100) / 100
  // ).toLocaleString();
  // Params.lineDiscount = (
  //   Math.round((lineDiscountSet.size + Number.EPSILON) * 100) / 100
  // ).toLocaleString();
  Params.roDiscount = roCountRODiscount;
  Params.lineDiscount = roCountLineDiscount;
  Params.lopDiscount = roCountLopDiscount;
  // Params.lopDiscount = (
  //   Math.round((lopDiscountSet.size + Number.EPSILON) * 100) / 100
  // ).toLocaleString();
  Params.prtsaleM =
    '$' +
    (Math.round((PrtsaleM + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleR =
    '$' +
    (Math.round((PrtsaleR + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleComp =
    '$' +
    (Math.round((PrtsaleComp + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleCmb =
    '$' +
    (Math.round((PrtsaleCmb + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtsaleAll =
    '$' +
    (Math.round((PrtsaleAll + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostAll =
    '$' +
    (Math.round((PrtcostAll + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.prtcostCmb =
    '$' +
    (Math.round((PrtcostCmb + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrSaleC =
    '$' +
    (Math.round((lbrsaleC + Number.EPSILON) * 100) / 100).toLocaleString();
  Params.lbrCostC =
    '$' +
    (Math.round((lbrcostC + Number.EPSILON) * 100) / 100).toLocaleString();

  ronumberArr.map(obj => {
    if (obj) {
      ronumberSet.add(obj);
    }
  });
  ronumberArrW.map(obj => {
    if (obj) {
      ronumberSetW.add(obj);
    }
  });
  ronumberArrI.map(obj => {
    if (obj) {
      ronumberSetI.add(obj);
    }
  });
  ronumberArrC.map(obj => {
    if (obj) {
      ronumberSetC.add(obj);
    }
  });
  ronumberArrR.map(obj => {
    if (obj) {
      ronumberSetR.add(obj);
    }
  });
  ronumberArrM.map(obj => {
    if (obj) {
      ronumberSetM.add(obj);
    }
  });
  ronumberArrComp.map(obj => {
    if (obj) {
      ronumberSetComp.add(obj);
    }
  });
  ronumberArrCmb.map(obj => {
    if (obj) {
      ronumberSetCmb.add(obj);
    }
  });
  ronumberArrCmbPerRo.map(obj => {
    if (obj) {
      ronumberSetCmbPerRo.add(obj);
    }
  });
  ronumberArrMn.map(obj => {
    if (obj) {
      ronumberSetMn.add(obj);
    }
  });
  ronumberArrE.map(obj => {
    if (obj) {
      ronumberSetE.add(obj);
    }
  });
  ronumberArrF.map(obj => {
    if (obj) {
      ronumberSetF.add(obj);
    }
  });
  var roCount = 0;
  var roCountW = 0;
  var roCountI = 0;
  var roCountC = 0;
  var roCountR = 0;
  var roCountM = 0;
  var roCountComp = 0;
  var roCountCmb = 0;
  var roCountCmbPerRo = 0;
  var roCountMn = 0;
  var roCountE = 0;
  var roCountF = 0;
  closedDateMap.forEach(set => {
    roCount += set.size;
    roCountW += set.size;
    roCountI += set.size;
    roCountC += set.size;
    roCountR += set.size;
    roCountM += set.size;
    roCountComp += set.size;
    roCountCmb += set.size;
    roCountCmbPerRo += set.size;
    roCountMn += set.size;
    roCountE += set.size;
    roCountF += set.size;
  });

  // var roCount = ronumberSet.size;
  // var roCountW = ronumberSetW.size;
  // var roCountI = ronumberSetI.size;
  // var roCountC = ronumberSetC.size;
  // var roCountR = ronumberSetR.size;
  // var roCountM = ronumberSetM.size;
  // var roCountComp = ronumberSetComp.size;
  // var roCountCmb = ronumberSetCmb.size;
  // var roCountCmbPerRo = ronumberSetCmbPerRo.size;
  // var roCountMn = ronumberSetMn.size;
  // var roCountE = ronumberSetE.size;
  // var roCountF = ronumberSetF.size;

  var LbrHrsPerRoRPrcnt = (HoursRepair / roCountR) * 100;
  var LbrHrsPerRoMPrcnt = (HoursMaintenance / roCountM) * 100;
  var LbrHrsPerRoCPrcnt = (HoursCompetitive / roCountComp) * 100;

  Params.lbrHrsPerRoRPrcnt =
    (Math.round((LbrHrsPerRoRPrcnt + Number.EPSILON) * 100) / 100).toFixed(2) +
    '%';
  Params.lbrHrsPerRoMPrcnt =
    (Math.round((LbrHrsPerRoMPrcnt + Number.EPSILON) * 100) / 100).toFixed(2) +
    '%';
  Params.lbrHrsPerRoCPrcnt =
    (Math.round((LbrHrsPerRoCPrcnt + Number.EPSILON) * 100) / 100).toFixed(2) +
    '%';

  var lbrLineCount = lbrlinecodeArr.length;

  Params.distinctRO = (
    Math.round((roCount + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROPartsOnly = (
    Math.round((roCount + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROW = (
    Math.round((roCountW + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROI = (
    Math.round((roCountI + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROC = (
    Math.round((roCountC + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.distinctROR = (
    Math.round((roCountR + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROM = (
    Math.round((roCountM + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROComp = (
    Math.round((roCountComp + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROCmb = (
    Math.round((roCountCmb + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.distinctROCmbPerRo = (
    Math.round((roCountCmbPerRo + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.roMaintenance = (
    Math.round((roCountMn + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.roExtended = (
    Math.round((roCountE + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.roFactory = (
    Math.round((roCountF + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.discountJobcount = JobCountArr;
  Params.discountJob = (
    Math.round((JobCountArr / roCount + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.distinctROPercent = (roCount / 100) * 100;
  var laborSaleRo = lbrsale / roCount;
  var partsSaleRO = prtsale / roCount;
  Params.lbrSaleperRo =
    '$' + Math.round((laborSaleRo + Number.EPSILON) * 100) / 100;
  Params.prtSaleperRo =
    '$' + Math.round((partsSaleRO + Number.EPSILON) * 100) / 100;
  Params.jobCount = Math.round((lbrLineCount + Number.EPSILON) * 100) / 100;
  var PrtRoByM = PrtsaleM / DistinctCPPROM;
  var PrtRoByR = PrtsaleR / DistinctCPPROR;
  var PrtRoByComp = PrtsaleComp / DistinctCPPROComp;
  var PrtRoByCmb = PrtsaleCmb / roCountCmbPerRo;
  Params.prtRoByM = '$' + Math.round((PrtRoByM + Number.EPSILON) * 100) / 100;
  Params.prtRoByR = '$' + Math.round((PrtRoByR + Number.EPSILON) * 100) / 100;
  Params.prtRoByComp =
    '$' + Math.round((PrtRoByComp + Number.EPSILON) * 100) / 100;
  Params.prtRoByCmb =
    '$' + Math.round((PrtRoByCmb + Number.EPSILON) * 100) / 100;
  var LbrSale = 0;
  if (
    chartId == 1197 ||
    chartId == 1198 ||
    chartId == 1199 ||
    chartId == 1200 ||
    chartId == 1201 ||
    chartId == 1202 ||
    chartId == 1203 ||
    chartId == 1211 ||
    chartId == 1212 ||
    chartId == 1213 ||
    chartId == 1214 ||
    chartId == 1215 ||
    chartId == 1216 ||
    chartId == 1217 ||
    chartId == 1218 ||
    chartId == 1219 ||
    chartId == 1220 ||
    chartId == 1221 ||
    chartId == 1222 ||
    chartId == 1223 ||
    chartId == 1224 ||
    chartId == 1319 ||
    chartId == 1320 ||
    chartId == 1321 ||
    chartId == 1322 ||
    chartId == 1323 ||
    chartId == 1324 ||
    chartId == 1325
  ) {
    values.forEach(function(value) {
      if (value.paytypegroup == 'C') {
        LbrHours += value.lbrsoldhours;
        LbrSale += value.lbrsale;
        PrtSale += value.prtextendedsale;
        PartsSale += value.prtssale;
      } else if (value.paytypegroup == 'W') {
        SHWarranty += value.lbrsoldhours;
        LSWarranty += value.lbrsale;
        PSWarranty += value.prtextendedsale;
      } else if (value.paytypegroup == 'I') {
        SHInternal += value.lbrsoldhours;
        LSInternal += value.lbrsale;
        PSInternal += value.prtextendedsale;
      } else if (value.paytypegroup == 'M') {
        SHMaintenance += value.lbrsoldhours;
        SaleMaintenance1 += value.lbrsale;
        PSMaintenanceP += value.prtextendedsale;
      } else if (value.paytypegroup == 'E' && chartId != 935) {
        SHExtended += value.lbrsoldhours;
        SaleExtended += value.lbrsale;
        PSExtended += value.prtextendedsale;
      } else if (value.paytypegroup == 'F' && chartId != 935) {
        SHFactory += value.lbrsoldhours;
        SaleFactory += value.lbrsale;
        PSFactory += value.prtextendedsale;
      }
    });
  }

  var SHByCategory =
    LbrHours + SHWarranty + SHInternal + SHMaintenance + SHExtended + SHFactory;
  console.log('1197===', SHByCategory, roCountC);
  Params.laborHoursSum = (
    Math.round((LbrHours + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.ShWarranty = (
    Math.round((SHWarranty + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.ShInternal = (
    Math.round((SHInternal + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.shMaintenance1 = (
    Math.round((SHMaintenance + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.shExtended = (
    Math.round((SHExtended + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.shFactory = (
    Math.round((SHFactory + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LsCombined = (
    Math.round((SHByCategory + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  LHROCombined = SHByCategory / roCountC;

  LHROCustomer = LbrHours / roCount;
  LHROWarranty = SHWarranty / roCountW;
  LHROInteranl = SHInternal / roCountI;
  LHROMaintain = SHMaintenance / roCountMn;
  LHROExtended = SHExtended / roCountE;
  LHROFactory = SHFactory / roCountF;
  Params.LHROCombined = (
    Math.round((LHROCombined + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.LHROCustomer = (
    Math.round((LHROCustomer + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LHROWarranty = (
    Math.round((LHROWarranty + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LHROInteranl = (
    Math.round((LHROInteranl + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LHROMaintain = (
    Math.round((LHROMaintain + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LHROExtended = (
    Math.round((LHROExtended + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LHROFactory = (
    Math.round((LHROFactory + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  var saleAll =
    LbrSale +
    LSWarranty +
    LSInternal +
    SaleMaintenance1 +
    SaleExtended +
    SaleFactory;

  LSaleROCombined = saleAll / roCountC;
  LSaleROCustomer = LbrSale / roCount;
  LSaleROWarranty = LSWarranty / roCountW;
  LSaleROInteranl = LSInternal / roCountI;
  LSaleROMaintain = SaleMaintenance1 / roCountMn;
  LSaleROExtended = SaleExtended / roCountE;
  LSaleROFactory = SaleFactory / roCountF;
  Params.SaleFactory = (
    Math.round((SaleFactory + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.laborSaleSum = (
    Math.round((LbrSale + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LSWarranty = (
    Math.round((LSWarranty + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.LSInternal = (
    Math.round((LSInternal + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.SaleMaintenance1 = (
    Math.round((SaleMaintenance1 + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.SaleExtended = (
    Math.round((SaleExtended + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.SaleFactory = (
    Math.round((SaleFactory + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.lbrSaleC = (
    Math.round((saleAll + Number.EPSILON) * 100) / 100
  ).toLocaleString();

  Params.LSaleROCombined =
    '$' + Math.round((LSaleROCombined + Number.EPSILON) * 100) / 100;
  Params.LSaleROCustomer =
    '$' + Math.round((LSaleROCustomer + Number.EPSILON) * 100) / 100;
  Params.LSaleROWarranty =
    '$' + Math.round((LSaleROWarranty + Number.EPSILON) * 100) / 100;
  Params.LSaleROInteranl =
    '$' + Math.round((LSaleROInteranl + Number.EPSILON) * 100) / 100;
  Params.LSaleROMaintain =
    '$' + Math.round((LSaleROMaintain + Number.EPSILON) * 100) / 100;
  Params.LSaleROExtended =
    '$' + Math.round((LSaleROExtended + Number.EPSILON) * 100) / 100;
  Params.LSaleROFactory =
    '$' + Math.round((LSaleROFactory + Number.EPSILON) * 100) / 100;

  var PrtsSaleAll =
    PrtSale + PSWarranty + PSInternal + PSMaintenanceP + PSExtended + PSFactory;

  Params.psSumComp =
    '$' + Math.round((PrtsSaleAll + Number.EPSILON) * 100) / 100;
  Params.partsSaleSum =
    '$' + Math.round((PrtSale + Number.EPSILON) * 100) / 100;
  Params.PsWarranty =
    '$' + Math.round((PSWarranty + Number.EPSILON) * 100) / 100;
  Params.PsInternal =
    '$' + Math.round((PSInternal + Number.EPSILON) * 100) / 100;
  Params.prtsaleM =
    '$' + Math.round((PSMaintenanceP + Number.EPSILON) * 100) / 100;
  Params.PsExtended =
    '$' + Math.round((PSExtended + Number.EPSILON) * 100) / 100;
  Params.PsFactory = '$' + Math.round((PSFactory + Number.EPSILON) * 100) / 100;

  prtsRevenueRoCombined = PrtsSaleAll / roCountC;
  prtsRevenueRoCustomer = PrtSale / roCount;
  prtsRevenueRoWarranty = PSWarranty / roCountW;
  prtsRevenueRoInteranl = PSInternal / roCountI;
  prtsRevenueRoMaintain = PSMaintenanceP / roCountMn;
  prtsRevenueRoExtended = PSExtended / roCountE;
  prtsRevenueRoFactory = PSFactory / roCountF;
  Params.prtsRevenueRoCombined =
    '$' + Math.round((prtsRevenueRoCombined + Number.EPSILON) * 100) / 100;
  Params.prtsRevenueRoCustomer =
    '$' + Math.round((prtsRevenueRoCustomer + Number.EPSILON) * 100) / 100;
  Params.prtsRevenueRoWarranty =
    '$' + Math.round((prtsRevenueRoWarranty + Number.EPSILON) * 100) / 100;
  Params.prtsRevenueRoInteranl =
    '$' + Math.round((prtsRevenueRoInteranl + Number.EPSILON) * 100) / 100;
  Params.prtsRevenueRoMaintain =
    '$' + Math.round((prtsRevenueRoMaintain + Number.EPSILON) * 100) / 100;
  Params.prtsRevenueRoExtended =
    '$' + Math.round((prtsRevenueRoExtended + Number.EPSILON) * 100) / 100;
  Params.prtsRevenueRoFactory =
    '$' + Math.round((prtsRevenueRoFactory + Number.EPSILON) * 100) / 100;

  return Params;
}

export function myCustomSumAggregateForSummary(values, chart) {
  var LbrSale = 0;
  var PrtsSale = 0;
  var LbrCost = 0;
  var PrtsCost = 0;
  var TotalSale = 0;
  var LbrHours = 0;
  var LbrGP = 0;
  var LbrGPPerct = 0;
  var PrtsGP = 0;
  var PrtsGPPerct = 0;
  var RoCount = 0;
  var JobCount = 0;
  var ELR = 0;
  var PartsMarkup = 0;
  var ronumberArr = [];
  const ronumberSet = new Set();
  let Params = {
    lbrSale: '',
    prtsSale: '',
    totalSale: '',
    lbrHours: '',
    lbrGP: '',
    lbrGPPerct: '',
    prtsGP: '',
    prtsGPPerct: '',
    lbrCost: '',
    prtsCost: '',
    roCount: '',
    jobCount: '',
    elr: '',
    partsMarkup: ''
  };
  values.forEach(function(value) {
    LbrSale += value.lbrsale;
    PrtsSale += value.prtssale;
    LbrHours += value.lbrsoldhours;
    LbrCost += value.lbrcost;
    PrtsCost += value.prtscost;
    RoCount += value.rocount;
    JobCount += value.jobcount;
  });
  TotalSale = Number(LbrSale) + Number(PrtsSale);

  LbrGP = Number(LbrSale) - Number(LbrCost);
  PrtsGP = Number(PrtsSale) - Number(PrtsCost);
  LbrGPPerct = (LbrGP / Number(LbrSale)) * 100;
  ELR = Number(LbrSale) / Number(LbrHours);
  PrtsGPPerct = (PrtsGP / Number(PrtsSale)) * 100;
  PartsMarkup = Number(PrtsSale) / Number(PrtsCost);
  Params.lbrSale =
    '$' +
    Number(LbrSale)
      .toFixed(2)
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.prtsSale =
    '$' +
    Number(PrtsSale)
      .toFixed(2)
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.totalSale =
    '$' + TotalSale.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  Params.lbrHours = Number(LbrHours)
    .toFixed(2)
    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  Params.lbrGP = '$' + LbrGP.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.lbrGPPerct =
    LbrGPPerct.toFixed(1).replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
  Params.prtsGP = '$' + PrtsGP.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.prtsGPPerct =
    PrtsGPPerct.toFixed(1).replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
  Params.roCount = (
    Math.round((Number(RoCount) + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.jobCount = (
    Math.round((Number(JobCount) + Number.EPSILON) * 100) / 100
  ).toLocaleString();
  Params.elr = '$' + ELR.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.lbrCost =
    '$' +
    Number(LbrCost)
      .toFixed(2)
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.prtsCost =
    '$' +
    Number(PrtsCost)
      .toFixed(2)
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.partsMarkup = Number(PartsMarkup).toFixed(4);

  return Params;
}

export function getAggregateForTrancheReports(resultArr, reportType) {
  let elr = 0;
  let labrSale = 0;
  let soldHours = 0;
  let startDateArr = [];
  let startDate = '';
  let endDate = '';
  let laborsale = 0;
  let frh = 0;
  let monthlyImpact = 0;
  let annualImpact = 0;

  let Params = {
    elr: '',
    labrSale: '',
    soldHours: '',
    startDate: '',
    endDate: '',
    laborsale: '',
    frh: '',
    monthlyImpact: '',
    annualImpact: '',
    analysisElr: ''
  };
  resultArr.map(item => {
    labrSale += Number(item.lbrsale);
    soldHours += Number(item.lbrsoldhours);
    startDateArr.push(item.opendate);
    laborsale += Number(item.laborsale);
    frh += Number(item.frh);
    monthlyImpact +=
      Number(item.monthlyImpact) > 0 ? Number(item.monthlyImpact) : 0;
    annualImpact +=
      Number(item.annualImpact) > 0 ? Number(item.annualImpact) : 0;
  });
  startDate = lodash.min(startDateArr);
  endDate = lodash.max(startDateArr);
  reportType == 'Best 100 ROs'
    ? (elr = labrSale / soldHours)
    : (elr = laborsale / frh);
  Params.labrSale =
    '$' + labrSale.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.soldHours = soldHours.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.elr = '$' + Math.round(elr);
  Params.startDate = startDate;
  Params.endDate = endDate;
  Params.laborsale =
    '$' + laborsale.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.frh = frh.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.monthlyImpact =
    '$' + monthlyImpact.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.annualImpact =
    '$' + annualImpact.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.analysisElr = Math.round(elr);
  return Params;
}
export function getAvgROOpenDays(
  resultArr,
  queryMonth,
  filterValues,
  allData,
  category
) {
  var values;
  if (typeof filterValues != 'undefined') {
    values = resultArr;
  } else {
    values = allData;
  }
  console.log('values===', values);
  let Params = {
    AvgWorkingDaysCustomerpay: '',
    AvgWorkingDaysWarranty: '',
    AvgWorkingDaysInternal: '',
    AvgWorkingDaysMaintenance: '',
    AvgWorkingDaysExtended: '',
    AvgWorkingDaysFactorysc: ''
  };
  const targetYearMonth = queryMonth;

  // // Filter the labels to match the target year and month
  const filteredLabels = values[0].labels.filter(label =>
    label.startsWith(targetYearMonth)
  );
  const targetIndices = filteredLabels.map(label =>
    values[0].labels.indexOf(label)
  );

  if (category == 0) {
    const PayTypeData = targetIndices.map(
      index => values[0].datasets[0].data[index]
    );
    Params.AvgWorkingDaysCustomerpay = PayTypeData;
  } else if (category == 3) {
    const PayTypeData = targetIndices.map(
      index => values[0].datasets[4].data[index]
    );
    Params.AvgWorkingDaysWarranty = PayTypeData;
  } else if (category == 4) {
    const PayTypeData = targetIndices.map(
      index => values[0].datasets[2].data[index]
    );
    Params.AvgWorkingDaysInternal = PayTypeData;
  } else if (category == 7) {
    const PayTypeData = targetIndices.map(
      index => values[0].datasets[3].data[index]
    );
    Params.AvgWorkingDaysMaintenance = PayTypeData;
  } else if (category == 19) {
    const PayTypeData = targetIndices.map(
      index => values[0].datasets[1].data[index]
    );
    Params.AvgWorkingDaysExtended = PayTypeData;
  } else if (category == 20) {
    const PayTypeData = targetIndices.map(
      index => values[0].datasets[5].data[index]
    );
    Params.AvgWorkingDaysFactorysc = PayTypeData;
  }

  return Params;
}
export function getSpecialMetricsPageSummary(
  resultArr,
  chartId,
  filterValues,
  allData,
  category,
  mileageAbv,
  mileageBlw,
  rowDataLength,
  distinctROPercent
) {
  var values;
  if (typeof filterValues != 'undefined') {
    values = resultArr;
  } else {
    values = allData;
  }
  let Params = {
    AvgWorkingDaysCustomerpay: '',
    AvgWorkingDaysWarranty: '',
    AvgWorkingDaysInternal: '',
    AvgWorkingDaysMaintenance: '',
    AvgWorkingDaysExtended: '',
    AvgWorkingDaysFactorysc: '',
    MileageAbv: '',
    MileageBlw: '',
    TotalShop: '',
    MileageLbrSale: '',
    MileageSoldHrs: '',
    MileageAbvPerc: '',
    MileageBlwPerc: '',

    MileageAbvMulti: '',
    MileageBlwMulti: '',
    MileageAbvPercMulti: '',
    MileageBlwPercMulti: '',
    ROPercentMulti: '',
    ROCountMulti: ''
  };

  ////// Chart id - 948

  let totalShop = mileageBlw.length + mileageAbv.length;

  var lbrsale = 0;
  var lbrhrs = 0;
  values.map(item => {
    lbrsale += Number(item.lbrsale);
    lbrhrs += Number(item.lbrsoldhours);
  });
  Params.MileageAbv = mileageAbv.length;
  Params.MileageBlw = mileageBlw.length;

  Params.MileageLbrSale =
    '$' + lbrsale.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  Params.MileageSoldHrs = lbrhrs.toFixed(2);

  let mileageAbvPerc = (mileageAbv.length / rowDataLength) * 100;
  let mileageBlwPerc = (mileageBlw.length / rowDataLength) * 100;

  Params.MileageAbvPerc =
    (
      Math.round((mileageAbvPerc + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.MileageBlwPerc =
    (
      Math.round((mileageBlwPerc + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';

  // var RoPercent =
  //   (this.state.distinctROPercent / this.state.rowDataLength) *
  //   100;
  var totalShopPer = mileageAbvPerc + mileageBlwPerc;
  var totalShopPerVal = totalShopPer.toFixed(2) + '%';
  if (chartId == 923) {
    Params.TotalShop = totalShopPerVal;
  } else {
    Params.TotalShop = totalShop;
  }
  const closedDateMapMilegeAbve = new Map();

  mileageAbv.forEach(function(value) {
    if (!closedDateMapMilegeAbve.has(value.closeddate)) {
      closedDateMapMilegeAbve.set(value.closeddate, new Set());
    }
    closedDateMapMilegeAbve.get(value.closeddate).add(value.ronumber);
  });
  var distinctMilegeAbve1 = 0;
  closedDateMapMilegeAbve.forEach(set => {
    distinctMilegeAbve1 += set.size;
  });

  const closedDateMapMilegeBlw = new Map();

  mileageBlw.forEach(function(value) {
    if (!closedDateMapMilegeBlw.has(value.closeddate)) {
      closedDateMapMilegeBlw.set(value.closeddate, new Set());
    }
    closedDateMapMilegeBlw.get(value.closeddate).add(value.ronumber);
  });

  var distinctMilegeBlw1 = 0;
  closedDateMapMilegeBlw.forEach(set => {
    distinctMilegeBlw1 += set.size;
  });

  let mileageAbvMulti = (distinctMilegeAbve1 / rowDataLength) * 100;

  let mileageBlwMulti = (distinctMilegeBlw1 / rowDataLength) * 100;

  Params.MileageAbvMulti = distinctMilegeAbve1;
  Params.MileageBlwMulti = distinctMilegeBlw1;
  Params.MileageAbvPercMulti =
    (
      Math.round((mileageAbvMulti + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';
  Params.MileageBlwPercMulti =
    (
      Math.round((mileageBlwMulti + Number.EPSILON) * 100) / 100
    ).toLocaleString() + '%';

  var RoPercent = (distinctROPercent / rowDataLength) * 100;
  Params.ROPercentMulti =
    (Math.round((RoPercent + Number.EPSILON) * 100) / 100).toLocaleString() +
    '%';

  Params.ROCountMulti = distinctROPercent;

  return Params;
}
export function getDiscountMetricsPageSummary(
  resultArr,
  allData,
  laborData,
  partsData,
  chartId,
  filterValues,
  isFrom
) {
  var values;
  if (typeof filterValues != 'undefined') {
    values = resultArr;
  } else {
    values = allData;
  }

  let Params = {
    totalDiscountP: '',
    totallbrDiscount: '',
    TotalDiscountVal: '',
    totalDiscountPercentage: '',
    percPartsDiscount: '',
    totalDiscountLP: '',
    LbrDiscount: ''
  };

  var LbrSale = 0;
  var LbrDiscount = 0;
  var partSale = 0;
  var PrtsDiscount = 0;
  var TotalPrtsDiscount = 0;
  var TotalLbrDiscount = 0;

  if (!lodash.isEmpty(filterValues)) {
    Object.keys(filterValues).forEach(function(key) {
      if (key == 'opcategory' && filterValues[key].values.length > 0) {
        var filterArrLabor = lodash.filter(laborData, function(o) {
          return filterValues[key].values.includes(o[key]);
        });
        filterArrLabor.forEach(function(value) {
          if (
            value.paytypegroup == 'C' ||
            value.paytypegroup == 'E' ||
            value.paytypegroup == 'M'
          ) {
            LbrSale += Number(value.lbrsale);

            LbrDiscount += parseFloat(value.apportionedlbrdiscount);
            TotalLbrDiscount += value.apportionedlbrdiscount
              ? parseFloat(value.apportionedlbrdiscount)
              : 0;
          }
        });
        var filterArrParts = lodash.filter(partsData, function(o) {
          return filterValues[key].values.includes(o[key]);
        });
        filterArrParts.forEach(function(value) {
          if (value.paytypegroup == 'C' || value.laborpaytypegroup == 'C') {
            partSale += Number(value.prtssale);

            PrtsDiscount += value.apportionedlbrdiscount
              ? parseFloat(value.apportionedlbrdiscount)
              : 0;
            TotalPrtsDiscount +=
              value.apportionedlbrdiscount != null
                ? parseFloat(value.apportionedlbrdiscount)
                : 0;
          }
        });
      } else if (
        Object.keys(filterValues).length == 1 &&
        Object.keys(filterValues).some(key => key !== 'opcategory')
      ) {
        laborData.forEach(function(value) {
          if (
            value.paytypegroup == 'C' ||
            value.paytypegroup == 'E' ||
            value.paytypegroup == 'M'
          ) {
            LbrSale += Number(value.lbrsale);

            LbrDiscount += parseFloat(value.apportionedlbrdiscount);
            TotalLbrDiscount += value.apportionedlbrdiscount
              ? parseFloat(value.apportionedlbrdiscount)
              : 0;
          }
        });
        partsData.forEach(function(value) {
          if (value.paytypegroup == 'C' || value.laborpaytypegroup == 'C') {
            partSale += Number(value.prtssale);

            PrtsDiscount += value.apportionedlbrdiscount
              ? parseFloat(value.apportionedlbrdiscount)
              : 0;
            TotalPrtsDiscount +=
              value.apportionedlbrdiscount != null
                ? parseFloat(value.apportionedlbrdiscount)
                : 0;
          }
        });
      }
    });
  } else {
    if (isFrom != 'onLoad' && laborData.length != 0) {
      console.log('laborData===', laborData, partsData);
      laborData.forEach(function(value) {
        if (
          value.paytypegroup == 'C' ||
          value.paytypegroup == 'E' ||
          value.paytypegroup == 'M'
        ) {
          LbrSale += Number(value.lbrsale);

          LbrDiscount += parseFloat(value.apportionedlbrdiscount);
          TotalLbrDiscount += value.apportionedlbrdiscount
            ? parseFloat(value.apportionedlbrdiscount)
            : 0;
        }
      });
      partsData.forEach(function(value) {
        if (value.paytypegroup == 'C' || value.laborpaytypegroup == 'C') {
          partSale += Number(value.prtssale);
          console.log('ooo==');
          PrtsDiscount += value.apportionedlbrdiscount
            ? parseFloat(value.apportionedlbrdiscount)
            : 0;
          TotalPrtsDiscount +=
            value.apportionedlbrdiscount != null
              ? parseFloat(value.apportionedlbrdiscount)
              : 0;
        }
      });
    }
  }

  var totalSale = LbrSale + partSale;

  var percPrtsDiscount = Math.abs((TotalPrtsDiscount / totalSale) * 100);
  var TotalLbrDiscountP = Math.abs((TotalLbrDiscount / totalSale) * 100);
  var percPartsDiscountVal =
    parseFloat(percPrtsDiscount)
      .toFixed(2)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';

  var overAllDiscountVal =
    '$' +
    parseFloat(totalSale)
      .toFixed(2)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  var totallbrDiscountVal =
    '$' +
    parseFloat(Math.abs(TotalLbrDiscount))
      .toFixed(2)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  var totalLbrDiscountVal =
    parseFloat(TotalLbrDiscountP)
      .toFixed(2)
      .toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
  Params.totallbrDiscount = totallbrDiscountVal;
  Params.totalDiscountP = totalLbrDiscountVal;
  Params.TotalDiscountVal = overAllDiscountVal;
  Params.percPartsDiscount = percPartsDiscountVal;
  return Params;
}
