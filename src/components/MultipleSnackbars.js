import React, { useEffect, useState } from 'react';
import { Snackbar, IconButton } from '@material-ui/core';
import { Close, ErrorOutline, CheckCircleOutline } from '@material-ui/icons';

const DISPLAY_DURATION = 5000;

const MultipleSnackbars = ({ messages, setSnackbarMessages }) => {
  const [snackbarMessage, setSnackbarMessage] = useState(null);

  useEffect(() => {
    if (messages?.length) {
      const totalItems = messages.length;
      const failedItems = messages.filter(msg => msg.code === 0);
      const successfulItems = totalItems - failedItems.length;
      let message;
      if (successfulItems === 0) {
        message = (
          <div>
            <div
              style={{
                color: '#f44336',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <ErrorOutline style={{ marginRight: 8 }} />
              {failedItems[0].message}
            </div>
          </div>
        );
      } else {
        message = (
          <div>
            <div
              style={{
                color: '#4caf50',
                fontWeight: 'bold',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <CheckCircleOutline style={{ marginRight: 8 }} />
              {`${successfulItems} out of ${totalItems} grids added successfully`}
            </div>
            {failedItems.length > 0 && (
              <>
                <div
                  style={{
                    color: '#f44336',
                    fontWeight: 'bold',
                    display: 'flex',
                    alignItems: 'center',
                    paddingTop: 10
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <ErrorOutline
                      style={{ marginRight: 8, color: '#f44336' }}
                    />
                    <span style={{ fontWeight: 'bold', color: '#f44336' }}>
                      Failed grids:
                    </span>
                  </div>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ul
                    style={{
                      margin: 0,
                      paddingLeft: 45,
                      display: 'inline-block',

                      color: '#f44336',
                      fontWeight: 'bold'
                    }}
                  >
                    {failedItems.map(item => (
                      <li key={item.grid_type}>
                        {item.grid_type}-{item.message}
                      </li>
                    ))}
                  </ul>
                </div>{' '}
              </>
            )}
          </div>
        );
      }

      setSnackbarMessage({
        message,
        open: true
      });
    }
  }, [messages]);

  const handleClose = () => {
    setSnackbarMessage(null);
    setSnackbarMessages(null);
  };

  return (
    <>
      {snackbarMessage && (
        <Snackbar
          open={snackbarMessage.open}
          autoHideDuration={DISPLAY_DURATION}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
          message={
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                fontWeight: 500,
                fontFamily: 'Roboto',
                color: '#000000',
                position: 'relative'
              }}
            >
              <span>{snackbarMessage.message}</span>
              {/* <IconButton
                size="small"
                aria-label="close"
                color="inherit"
                onClick={handleClose}
                style={{
                  position: 'absolute',
                  right: 0,
                  top: 0
                }}
              >
                <Close style={{ color: '#000000' }} />
              </IconButton> */}
            </div>
          }
          ContentProps={{
            style: {
              backgroundColor: '#f5f5f5' // Light grey color
            }
          }}
        />
      )}
    </>
  );
};

export default MultipleSnackbars;
