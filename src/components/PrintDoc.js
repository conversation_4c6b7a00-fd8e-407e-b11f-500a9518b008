import { text } from '@fortawesome/fontawesome-svg-core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

/**
 * This function iterates over all of the columns to create a row of header cells
 */
export const usePDFExport = () => {
  const formatCellValue = (val, opt, slno, dec) => {
    var formattedValue =
      val == 0 || val == null
        ? '0'
        : slno == 30
        ? val
        : dec == 1
        ? parseFloat(val)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : parseFloat(val)
            .toFixed(1)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    if (
      ((slno == 1 ||
        slno == 2 ||
        slno == 12 ||
        slno == 13 ||
        slno == 32 ||
        slno == 35 ||
        slno == 31 ||
        slno == 34 ||
        slno == 33 ||
        slno == 37) &&
        opt == 2) ||
      (slno == 8 && opt == 1) ||
      ((slno == 21 || slno == 22 || slno == 26 || slno == 27) && opt == 1) ||
      slno == 19
    ) {
      formattedValue = formattedValue + '%';
    } else if (
      slno == 1 ||
      slno == 2 ||
      slno == 3 ||
      slno == 4 ||
      slno == 5 ||
      slno == 6 ||
      slno == 18 ||
      slno == 23 ||
      slno == 24 ||
      slno == 28 ||
      slno == 29 ||
      slno == 32 ||
      slno == 35 ||
      ((slno == 14 || slno == 15 || slno == 16 || slno == 17) &&
        (opt == 1 || opt == 2))
    ) {
      if (Number(formattedValue.replace(/,/g, '')) < 0) {
        formattedValue = '-$' + Math.abs(formattedValue);
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      formattedValue = formattedValue;
    }

    return formattedValue;
  };

  // Row colors
  const TITLE_ROW_COLOR = '#ffc000';
  const HEADER_ROW_COLOR = '#c65911';
  const EVEN_ROW_COLOR = '#fcfcfc';
  const ODD_ROW_COLOR = '#fff';

  const PDF_INNER_BORDER_COLOR = '#dde2eb';
  const PDF_OUTER_BORDER_COLOR = '#babfc7';

  const createLayout = (numberOfHeaderRows, rows) => ({
    fillColor: (rowIndex, node) => {
      if (rowIndex < numberOfHeaderRows) {
        return HEADER_ROW_COLOR;
      }
      if (rows.includes(rowIndex - 1)) {
        return TITLE_ROW_COLOR;
      }
      // return rowIndex % 2 === 0 ? EVEN_ROW_COLOR : ODD_ROW_COLOR;
    },

    //vLineHeight not used here.
    vLineWidth: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.widths.length ? 1 : 0,
    hLineColor: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.body.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR,
    vLineColor: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.widths.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR
  });

  /**
   * Returns a pdfMake shaped config for export, for more information
   * regarding pdfMake configuration, please see the pdfMake documentation.
   */
  function splitIntoGroups(array, groupSize) {
    const kpiElement = array[0]; // Assuming the first element is "KPI Name"
    const groups = [];

    // Start iterating from the second element
    for (let i = 1; i < array.length; i += groupSize - 1) {
      let group;
      // if (i == 1) {
      //   group = [kpiElement, ...array.slice(i, i + groupSize - 1)];
      // } else {
      //   if (kpiElement.text != 'KPI Name') {
      //     kpiElement.text.forEach(item => {
      //       if (item.text) {
      //         item.text = item.text + ' 1';
      //       }
      //     });
      //   } else {
      //     kpiElement.text = 'KPI Name 1';
      //   }

      //   group = [kpiElement, ...array.slice(i, i + groupSize - 1)];
      // }
      group = [kpiElement, ...array.slice(i, i + groupSize - 1)];
      groups.push(group);
    }

    return groups;
  }

  const getGoalVarColor = (variance, kpiname) => {
    var color =
      variance > -10.0 &&
      variance < 0.0 &&
      !kpiname.includes('1 Line Count / % Over 60K') &&
      !kpiname.includes('1 Line Count / % Under 60K') &&
      !kpiname.includes(
        'Repair Price Targets / Misses / % of Non-Compliance'
      ) &&
      !kpiname.includes('Parts Price Targets / Misses / % of Non-Compliance')
        ? 'red'
        : variance <= -10.0 &&
          !kpiname.includes('1 Line Count / % Over 60K') &&
          !kpiname.includes('1 Line Count / % Under 60K') &&
          !kpiname.includes(
            'Repair Price Targets / Misses / % of Non-Compliance'
          ) &&
          !kpiname.includes(
            'Parts Price Targets / Misses / % of Non-Compliance'
          )
        ? 'red'
        : variance > 0 &&
          (kpiname.includes('1 Line Count / % Over 60K') ||
            kpiname.includes('1 Line Count / % Under 60K') ||
            kpiname.includes(
              'Repair Price Targets / Misses / % of Non-Compliance'
            ) ||
            kpiname.includes(
              'Parts Price Targets / Misses / % of Non-Compliance'
            ))
        ? 'red'
        : variance == 0
        ? '#000'
        : 'green';

    return color;
  };
  const addKpiRows = (key, kpiArray, hideGoals, kpi_report_type) => {
    const row = [
      {
        text: [
          {
            text: kpiArray.kpi_no + '   ' + kpiArray.kpi_name,
            fontSize: 6,
            color: '#fff',
            bold: true
          }
        ],
        fillColor: '#c65911'
        // styles
      },
      ...kpiArray.data.map(item => {
        let data = item.kpi_data.split('/');
        let goal = item.goal;
        let variance = item.variance;
        let kpiName;
        if (kpi_report_type == 'advisor' || kpi_report_type == 'tech') {
          kpiName = item.adv_or_tech_name;
        } else {
          kpiName = item.res_storename;
        }

        if (
          data.length > 0 &&
          (kpiArray.kpi_slno == 12 ||
            kpiArray.kpi_slno == 13 ||
            kpiArray.kpi_slno == 9)
        ) {
          data.splice(-1);
        }
        if (
          kpiArray.kpi_name != 'Parts To Labor Ratio' &&
          kpiArray.kpi_name != 'CP & Wty - Avg Age / Miles Per Vehicle'
        ) {
          data = data
            .map((ele, i) => {
              return formatCellValue(
                kpiArray.kpi_slno >= 31 &&
                  kpiArray.kpi_slno <= 33 &&
                  kpiArray.kpi_slno >= 34 &&
                  kpiArray.kpi_slno <= 37
                  ? 0
                  : ele,
                i,
                kpiArray.kpi_slno,
                ((kpiArray.kpi_slno == 1 ||
                  kpiArray.kpi_slno == 2 ||
                  kpiArray.kpi_slno == 11) &&
                  i == 2) ||
                  ((kpiArray.kpi_slno == 33 || kpiArray.kpi_slno == 37) &&
                    (i == 3 || i == 2))
                  ? 2
                  : 1
              );
            })
            .join(' * ');
        } else if (kpiArray.kpi_name == 'Parts To Labor Ratio') {
          data =
            '$' +
            parseFloat(data[0])
              // .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' to ' +
            '$' +
            parseInt(data[1])
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        } else if (
          kpiArray.kpi_name == 'CP & Wty - Avg Age / Miles Per Vehicle'
        ) {
          data =
            parseFloat(data[0])
              .toFixed(1)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' Years' +
            ' * ' +
            parseInt(data[1])
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' Miles';
        }
        if (!hideGoals.includes(kpiName)) {
          var goalValue;
          if (goal != '' && goal != 0) {
            // if (
            //   kpiArray.kpi_slno != 9 &&
            //   kpiArray.kpi_slno != 11 &&
            //   kpiArray.kpi_slno != 33 &&
            //   kpiArray.kpi_slno != 37
            // ) {
            //   goalVal = goal + "%/" + variance + "%";
            // } else {
            //   goalVal = goal + "/" + variance;
            // }

            goalValue = goal + '%/' + variance + '%';
          } else {
            goalValue = '';
          }

          return [
            { text: data, alignment: 'left', fontSize: 6 },
            {
              text:
                typeof goal != 'undefined' &&
                goal != '' &&
                goal != 0 &&
                !kpiArray.kpi_name.includes(
                  'Total Sold Hrs / Avg Per Day / CP Avg Hrs Per RO'
                ) &&
                !kpiArray.kpi_name.includes('All ROs - Average Days Open') &&
                !kpiArray.kpi_name.includes(
                  'Potential Hours / Sold Hours / % / Hours Sold Per MPI'
                ) &&
                !kpiArray.kpi_name.includes(
                  'Potential Hours / Sold Hours / % / Hours Sold Per Menu'
                )
                  ? [
                      {
                        text: goal + '%',
                        alignment: 'center',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        // text: "% / ",
                        text: ' / ',
                        alignment: 'center',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: variance,
                        color: getGoalVarColor(variance, kpiArray.kpi_name),

                        alignment: 'center',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: '%',
                        alignment: 'center',
                        color: getGoalVarColor(variance, kpiArray.kpi_name),
                        fontSize: 6,
                        style: 'subheader'
                      }
                    ]
                  : goal != '' && goal != 0
                  ? [
                      {
                        text: goal,
                        alignment: 'center',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: ' / ',
                        alignment: 'center',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: variance,
                        color: getGoalVarColor(variance, kpiArray.kpi_name),
                        alignment: 'center',
                        fontSize: 6,
                        style: 'subheader'
                      }
                    ]
                  : [
                      {
                        text: '',
                        alignment: 'center',
                        fontSize: 6,
                        style: 'goals'
                      }
                    ],
              style: 'goals'
              // alignment: "left",
              // fontSize: 6,
              //column: advName + "----Goal/Var",
            }
          ];
        } else {
          return { text: data, alignment: 'left', fontSize: 6 };
        }
      })
    ];
    let resultArray = [];

    row.forEach(item => {
      if (Array.isArray(item)) {
        // If item is an array, push each element of the array individually to the resultArray
        item.forEach(innerItem => {
          resultArray.push(innerItem);
        });
      } else {
        // If item is not an array, push it directly to the resultArray
        resultArray.push(item);
      }
    });

    //console.log("row===", JSON.stringify(resultArray));
    //result.push(row);
    return resultArray;
  };
  function addGoalVar(arr, hideGoals) {
    let result = [];

    arr.forEach((item, index) => {
      result.push(item);

      if (
        item.text !== 'KPI Name' &&
        item.text !== 'Total Selected' &&
        item.text !== 'Group Average' &&
        item.text !== 'Group Roll-Up'
      ) {
        if (!hideGoals.includes(item.text)) {
          result.push({
            text: 'Goal/Var',
            alignment: 'center',
            fontSize: 8,
            bold: true,
            color: '#fff'
          });
        }
      }
    });

    return result;
  }

  async function downloadPDF(
    excelData,
    fileNameExport,
    kpi_report_type,
    store_grp,
    filterStart,
    filterEnd,
    hideGoals,
    expandedKPIs,
    hiddenKpis,
    sortOrder,
    selAdvisors,
    allStores,
    selStores,
    kpiSortedArray,
    image,
    session,
    storeNickName
  ) {
    //getDocument(data);

    const imageDataUrl = image;
    var data = [];
    excelData.map(node => {
      var hidKpis = session.hiddenKpis;

      var hidKpiArrSub = [];
      var hidKpiArr;
      var hidKpiSubArr;
      if (hidKpis != null) {
        hidKpis.map(item => {
          if (
            (item.type == 'Pricing - Customer Pay' && item.kpi == 6) ||
            (item.type ==
              'Opportunities - CP Vehicles <u>Under</u> 60K Miles' &&
              item.kpi == 3) ||
            (item.type == 'Opportunities - CP Vehicles <u>Over</u> 60K Miles' &&
              item.kpi == 3)
          ) {
            var subArr = {
              type: item.type,
              kpi: '',
              si_no: item.si_no + 1
            };
            hidKpiArrSub.push(subArr);
          }
        });

        var hidKpiArray = hidKpis.filter(
          item => item.type == node.kpi_type && item.kpi == node.kpi_no
        );
        hidKpiArr = hidKpiArray;
        var hidKpiSubArray = hidKpiArrSub.filter(
          item => item.type == node.kpi_type && item.si_no == node.kpi_slno
        );
        hidKpiSubArr = hidKpiSubArray;
      }

      if (
        typeof node.data != 'undefined' &&
        expandedKPIs != null &&
        !expandedKPIs.includes(node.kpi_type) &&
        hidKpiArr.length == 0 &&
        hidKpiSubArr.length == 0
      ) {
        var dataArr = node;
        let filteredData;
        if (kpi_report_type == 'advisor' || kpi_report_type == 'tech') {
          if (selAdvisors.length > 0) {
            filteredData = node.data.filter(item =>
              selAdvisors.includes(item.adv_or_tech_id)
            );

            //node.data.data = filteredData;
          } else {
            filteredData = node.data;
          }
        } else {
          if (selStores.length > 0) {
            filteredData = node.data.filter(item =>
              selStores.includes(item.store_id)
            );
            //node.data.data = filteredData;
          } else {
            filteredData = node.data;
          }
        }

        dataArr.data = filteredData;

        data.push(dataArr);
      } else if (typeof node.data != 'undefined' && expandedKPIs == null) {
        var dataArr = node;
        //return node.data;
        data.push(dataArr);
      }
    });

    if (kpi_report_type == 'advisor' || kpi_report_type == 'tech') {
      data.forEach(item => {
        item.data.sort((a, b) => {
          return (
            kpiSortedArray.indexOf(a.adv_or_tech_name) -
            kpiSortedArray.indexOf(b.adv_or_tech_name)
          );
        });
      });
    } else {
      data.forEach(item => {
        item.data.sort((a, b) => {
          return (
            kpiSortedArray.indexOf(a.res_storename) -
            kpiSortedArray.indexOf(b.res_storename)
          );
        });
      });
    }

    data.forEach(obj => {
      if (kpi_report_type == 'advisor' || kpi_report_type == 'tech') {
        //obj.data.reverse();
      } else {
        let groupRollUp = obj.data.filter(
          item => item.res_storename === 'Group Roll-Up'
        );
        let groupAverage = obj.data.filter(
          item => item.res_storename === 'Group Average'
        );

        // Filter out objects that are not "Group Roll-Up" or "Group Average"
        let filteredData = obj.data.filter(
          item =>
            item.res_storename !== 'Group Roll-Up' &&
            item.res_storename !== 'Group Average'
        );

        // Concatenate groupRollUp, groupAverage, and filteredData arrays
        obj.data = groupRollUp.concat(groupAverage, filteredData);
      }
      // Reverse the 'data' array within each object
    });

    let advOrTechNames;
    if (kpi_report_type == 'advisor' || kpi_report_type == 'tech') {
      advOrTechNames = data[0].data.map(item => {
        return {
          text: item.adv_or_tech_name,
          alignment: 'center',
          fontSize: 8,
          bold: true,
          color: '#fff'
        };
      });
    } else {
      advOrTechNames = data[0].data.map(item => {
        return {
          text: item.res_storename,
          alignment: 'center',
          fontSize: 8,
          bold: true,
          color: '#fff'
        };
      });
    }

    advOrTechNames.unshift({
      text: 'KPI Name',
      alignment: 'center',
      fontSize: 8,
      bold: true,
      color: '#fff'
    });

    let header = addGoalVar(advOrTechNames, hideGoals, kpi_report_type);

    var kpiHeaderAll = splitIntoGroups(header, 5);
    var repName =
      kpi_report_type == 'advisor' || kpi_report_type == 'tech'
        ? storeNickName
        : localStorage.getItem('storeGroup');

    var con = [];
    var colWidth;
    if (kpi_report_type == 'advisor' || kpi_report_type == 'tech') {
      if (repName != null && repName.length > 32) {
        colWidth = [180, 60, 273];
      } else {
        colWidth = [130, 60, 323];
      }
    } else {
      colWidth = [180, 60, 273];
    }
    var topbar = {
      style: 'tableExample',
      table: {
        layout: 'noBorders',
        // widths: [140, 60, 312],
        widths: colWidth,
        heights: [30],
        body: [
          [
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [false, false, false, false],
              text: [
                {
                  text:
                    kpi_report_type == 'advisor'
                      ? 'KPI Advisor Comparative' + '\n'
                      : kpi_report_type == 'tech'
                      ? 'KPI Tech Comparative' + '\n'
                      : 'KPI Store Comparative' + '\n',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                },
                {
                  text:
                    kpi_report_type == 'advisor' || kpi_report_type == 'tech'
                      ? storeNickName + '\n'
                      : localStorage.getItem('storeGroup') + '\n',
                  fontSize: 8,
                  color: '#fff'

                  // alignment: 'center'
                },
                {
                  text:
                    moment(filterStart).format('MM/DD/YY') +
                    ' - ' +
                    moment(filterEnd).format('MM/DD/YY'),
                  fontSize: 6,
                  color: '#fff'

                  //alignment: 'center'
                  //  background: '#c65911'
                }
              ]
            },
            {
              image: imageDataUrl,
              fit: [50, 50],
              // marginRight: 1000,
              backgroundColor: 'none',
              // left: 100,
              fillColor: '#c65911',
              border: [false, false, false, false],
              margin: [0, 4, 20, 0],
              alignment: 'left'
            },
            {
              border: [false, false, false, false],
              alignment: 'left',
              color: '#fff',
              fillColor: '#c65911',
              fontSize: 18,
              margin: [0, 4, 20, 0],
              text: 'Fixed Ops Performance Center'
            }
          ]
        ]
      }
    };

    con.push(topbar);

    kpiHeaderAll.map((head, index) => {
      const kpiData = data.reduce((acc, kpi) => {
        if (!acc[kpi.kpi_type]) {
          acc[kpi.kpi_type] = [];
        }
        acc[kpi.kpi_type].push(kpi);
        return acc;
      }, {});
      var kpiArr = [];
      let kpi_length = header.length;
      //console.log("kpi_length", kpi_length, advOrTechNames);
      for (const key in kpiData) {
        if (kpiData.hasOwnProperty(key)) {
          var firstRow = [];

          firstRow.push({
            text:
              kpiData[key][0].kpi_type_code +
              '   ' +
              key
                .replace('<p>', ' ')
                .replace('</p>', ' ')
                .replace('<u>', ' ')
                .replace('</u>', ' '),
            alignment: 'left',
            fontSize: 6,
            bold: true,
            style: 'kpi-main'
            //text: data.length > 0 ? data + '       Goal/Var' : '',
          });

          for (let i = 1; i <= kpi_length - 1; i++) {
            firstRow.push({ text: '' });
          }
          kpiArr.push(firstRow);

          kpiData[key].map((element, i) => {
            var kpiVal = addKpiRows(key, element, hideGoals, kpi_report_type);

            kpiArr.push(kpiVal);
          });
        }
      }
      console.log('row===', JSON.stringify(kpiArr));
      let elem = kpiArr;

      var page;
      if (kpi_report_type == 'advisor' || kpi_report_type == 'tech') {
        if (index == 0) {
          page = 5;
        } else {
          page = 5;
        }
      } else {
        if (index == 0) {
          page = 5;
        } else {
          page = 5;
        }
      }

      var colWidth = [];
      let rowval = [];
      var kpiRows = [];
      var kpiHeader;
      kpiHeader = splitIntoGroups(header, page);

      elem.map(subArray => {
        rowval.push(splitIntoGroups(subArray, page));
      });

      var elemIndex;
      if (typeof rowval[index] == 'undefined') {
        elemIndex = rowval[0].length;
      } else {
        elemIndex = rowval[index].length;
      }

      for (let i = 0; i < elemIndex; i++) {
        const combinedElement = [];
        for (let j = 0; j < rowval.length; j++) {
          combinedElement.push(rowval[j][i]);
        }
        kpiRows.push(combinedElement);
      }

      kpiRows[index][1].map((ele, p) => {
        if (p == 0) {
          colWidth.push(140);
        }

        // else if (ele.style == 'goals') {
        //   colWidth.push(40);
        // }
        else {
          colWidth.push(90);
          // if (advOrTechNames.length == hideGoals.length + 1) {
          //   colWidth.push(90);
          // } else {
          //   colWidth.push(110);
          // }
        }
      });

      var indexArr = [];
      elem.forEach((group, groupIndex) => {
        group.forEach((item, itemIndex) => {
          if (typeof item.text === 'string' && item.text.includes('  ')) {
            var cellVal = item.text.split('  ');
            if (cellVal[0].length === 1 && cellVal[0].match(/[a-z]/i)) {
              indexArr.push(groupIndex);
            }
          }
        });
      });
      console.log('body===', kpiRows);
      var body = {
        table: {
          widths: colWidth,
          // the number of header rows
          headerRows: 1,

          // the width of each column, can be an array of widths
          //widths: `${100 / columns.length}%`,

          // all the rows to display, including the header rows
          body: [kpiHeader[index], ...kpiRows[index]],

          // Header row is 40px, other rows are 15px
          heights: rowIndex => (rowIndex === 0 ? 10 : 10)
        },

        layout: createLayout(1, indexArr)
      };
      //console.log('body===', body);
      con.push(body);

      if (index + 1 != kpiHeader.length) {
        con.push({ text: '', pageBreak: 'before', style: 'subheader' });
      }
    });

    //   groupedKPIs.map((item) => {
    //     console.log("jjjson===", JSON.stringify(item));
    //   });
    console.log('jjjson===', con);
    var options = {
      pageOrientation: 'portrait', // can also be 'portrait'
      // pageSize: {
      //   width: 950,
      //   height: 'auto'
      // },
      content: con,

      pageMargins: [20, 45, 20, 20],
      styles: {
        subheader: {
          margin: [0, 0, 0, 0]
        },
        goals: {
          margin: [0, 0, 0, 0]
        }
      }
    };
    pdfMake.createPdf(options).download(fileNameExport);
  }

  const exportToPDF = (
    gridApi,
    filterStart,
    filterEnd,
    fileNameExport,
    expandedKPIs,
    hideGoals,
    image,
    data,
    isFrom,
    selAdvisors,
    selStores,

    session,
    kpiArray,
    storeNickName
  ) => {
    var kpi_report_type;
    if (isFrom == 'KPIReportComparative') {
      kpi_report_type = 'advisor';
    } else if (isFrom == 'KPIReportTechComparative') {
      kpi_report_type = 'tech';
    } else {
      kpi_report_type = 'store';
    }

    downloadPDF(
      data,
      fileNameExport,
      kpi_report_type,
      localStorage.getItem('storeGroup'),
      filterStart,
      filterEnd,
      hideGoals,
      expandedKPIs,
      session.hiddenKpis,
      session.sortOrder,
      selAdvisors,
      JSON.parse(localStorage.getItem('allPermittedStores')).join(','),
      selStores,
      kpiArray,
      image,
      session,
      storeNickName
    );
  };

  return {
    exportToPDF
  };
};
