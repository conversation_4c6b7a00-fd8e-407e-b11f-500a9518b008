import React, { Component } from 'react';

export default class extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isChecked: false
    };
    this.checkedHandler = this.checkedHandler.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;
    this.props.node.setDataValue(colId, checked);
    this.setState({ isChecked: checked });

    this.props.context.componentParent.handleRowSelection(
      this.props.node.data,
      checked,
      this.props.node.__objectId
    );
    // this.getSelectedRowData()
  }

  render() {
    // console.log('this.props.value', this.props);
    return (
      <input
        type="checkbox"
        onClick={this.checkedHandler}
        checked={this.state.isChecked}
      />
    );
  }
}
