import React, { useEffect, useRef } from 'react';
import clsx from 'clsx';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button,
  ListItemSecondaryAction,
  Radio,
  RadioGroup,
  CircularProgress,
  Grid,
  Tooltip
} from '@material-ui/core';
import FiberManualRecordIcon from '@material-ui/icons/FiberManualRecord';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import { makeStyles } from '@material-ui/styles';
import CreateMatrixName from 'src/views/PartsMatrixSetup/CreateMatrixName';
import CreateGridName from 'src/views/LaborGridSetup/CreateGridName';
import DeleteIcon from '@material-ui/icons/Delete';
import DeleteDialog from './DeleteDialog';
import {
  createMatrixName,
  createOrUpdateGridName
} from 'src/utils/hasuraServices';
const useStyles = makeStyles({
  dialogTitle: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    fontWeight: 'bold'
  },
  listItem: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
    // padding: '5px 20px'
  },
  listText: {
    display: 'flex',
    alignItems: 'center'
  },
  bullet: {
    // marginRight: 8
  },
  defaultText: {
    fontStyle: 'italic'
  }
});

const EditGridOrMatrixNameDialog = ({
  openDlg,
  parent,
  gridOrMatrixDetails,
  fetchGridOrMatrixTypes,
  handleCancelDlg,
  hasDefaultType,
  setUpdatedDefaultGridType,
  updatedDefaultGridType,
  saveDefaultGridType,
  defaultGridType,
  setGridTypeChanged,
  gridTypeChanged,
  isLoadingGridType,
  setIsLoadingGridType,
  getGridOrMatrixList
}) => {
  const classes = useStyles();
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [statusMessage, setStatusMessage] = React.useState('');
  const [isSuccessful, setIsSuccessful] = React.useState(true);
  const [selectedValue, setSelectedValue] = React.useState(null);
  const [defaultType, setDefaultType] = React.useState(false);

  const handleShowSnackbar = (message, success) => {
    setStatusMessage(message);
    setIsSuccessful(success);
    setOpenSnackbar(true);
    if (parent == 'grid') {
      getGridOrMatrixList({
        callType: 'Grid_Type'
      });
    } else {
      getGridOrMatrixList();
    }
  };

  useEffect(() => {
    if (gridOrMatrixDetails.length > 0) {
      setDefaultType(gridOrMatrixDetails.find(item => item.isDefaultGridType));
    }
  }, [gridOrMatrixDetails]);

  const deleteGridOrMatrixType = async (name, defaultGridType) => {
    setIsLoadingGridType(true);
    const isMatrix = parent === 'matrix';
    const input = isMatrix
      ? {
          inOldMatrixType: name,
          inNewMatrixType: name,
          inActivity: 'Delete'
        }
      : {
          inOldGridName: name,
          inNewGridName: name,
          inIsDefaultGridName: defaultGridType,
          inActivity: 'Delete'
        };

    const callback = result => {
      if (result?.[0]?.status === 1) {
        handleShowSnackbar(result[0].msg, true);
        fetchGridOrMatrixTypes();
      } else {
        handleShowSnackbar(result?.[0]?.msg || 'An error occurred', false);
      }
      setIsLoadingGridType(false);
    };

    isMatrix
      ? createMatrixName(input, callback)
      : createOrUpdateGridName(input, callback);
  };

  return (
    <>
      <Dialog open={openDlg} maxWidth="sm" fullWidth>
        <DialogTitle disableTypography>
          <div className={classes.dialogTitle}>
            <Typography variant="h6">
              {parent === 'matrix'
                ? 'All Source Matrix Names'
                : 'All Model Grid Names'}
            </Typography>

            <IconButton onClick={handleCancelDlg} style={{ paddingRight: 2 }}>
              <HighlightOffIcon color="action" />
            </IconButton>
          </div>
        </DialogTitle>
        {isLoadingGridType ? (
          <List
            style={{
              minHeight: '250px',
              maxHeight: '300px',
              overflowY: 'auto',
              paddingTop: '0px'
            }}
          >
            <ListItem
              style={{
                padding: '2px 8px',
                minHeight: '25px'
              }}
            >
              <Grid
                container
                justifyContent="center"
                alignItems="center"
                style={{ height: '100%' }}
                className={classes.loaderGrid}
              >
                <CircularProgress size={60} />
              </Grid>
            </ListItem>
          </List>
        ) : gridOrMatrixDetails.length == 0 ? (
          <List
            style={{
              maxHeight: '300px',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            <ListItem
              style={{
                justifyContent: 'center',
                textAlign: 'center',
                width: '100%'
              }}
            >
              <ListItemText
                primary={
                  parent === 'grid'
                    ? 'No grid names to list'
                    : 'No matrix names to list'
                }
                style={{ textAlign: 'center', marginBottom: '8px' }}
              />
            </ListItem>
            <ListItem style={{ justifyContent: 'center', width: '100%' }}>
              {parent === 'grid' ? (
                <CreateGridName
                  fetchGridOptions={fetchGridOrMatrixTypes}
                  isEdit={false}
                  isGridModal={true}
                  hasDefaultType={hasDefaultType}
                />
              ) : (
                <CreateMatrixName
                  fetchPartsMatrixRowData={fetchGridOrMatrixTypes}
                  isEdit={false}
                  isGridModal={true}
                  showSnackbar={handleShowSnackbar}
                />
              )}
            </ListItem>
          </List>
        ) : (
          <List
            style={{
              minHeight: '250px',
              maxHeight: '300px',
              overflowY: 'auto',
              paddingTop: '0px'
            }}
          >
            <RadioGroup
              value={updatedDefaultGridType}
              onChange={event => {
                setGridTypeChanged(event.target.value !== defaultGridType);
                setUpdatedDefaultGridType(event.target.value);
              }}
            >
              {gridOrMatrixDetails.map((item, index) => (
                <ListItem
                  key={index}
                  className={classes.listItem}
                  style={{
                    minHeight: '25px'
                  }}
                >
                  {defaultType ? (
                    <ListItemIcon>
                      <FiberManualRecordIcon
                        style={{
                          fontSize: '15px',
                          color: '#003d6b',
                          marginLeft: '8px'
                        }}
                      />
                    </ListItemIcon>
                  ) : item.gridCount > 0 ? (
                    <ListItemIcon>
                      <Radio
                        value={item.value}
                        size="small"
                        style={{
                          fontSize: '15px',
                          color: '#003d6b'
                          // marginRight: '8px'
                        }}
                      />
                    </ListItemIcon>
                  ) : (
                    <ListItemIcon>
                      <FiberManualRecordIcon
                        style={{
                          fontSize: '15px',
                          color: '#003d6b',
                          marginLeft: '8px'
                        }}
                      />
                    </ListItemIcon>
                  )}
                  <ListItemText
                    style={{
                      // paddingLeft: '18px',
                      margin: '0'
                    }}
                    primary={
                      <span
                        className={
                          item.isDefaultGridType ? classes.defaultText : ''
                        }
                      >
                        {item.value} {item.isDefaultGridType && '(Default)'}
                      </span>
                    }
                  />
                  <ListItemSecondaryAction style={{ paddingRight: '4px' }}>
                    {parent === 'matrix' ? (
                      <>
                        <CreateMatrixName
                          fetchPartsMatrixRowData={fetchGridOrMatrixTypes}
                          isEdit={true}
                          isGridModal={false}
                          matrixName={item.value}
                          showSnackbar={handleShowSnackbar}
                        />
                        <DeleteDialog
                          matrixOrGridName={item.value}
                          confirmDialog={deleteGridOrMatrixType}
                          isDefaultType={false}
                          parent={parent}
                        />
                      </>
                    ) : (
                      <>
                        <CreateGridName
                          fetchGridOptions={fetchGridOrMatrixTypes}
                          isEdit={true}
                          isGridModal={false}
                          gridName={item.value}
                          showSnackbar={handleShowSnackbar}
                          defaultGridType={item.isDefaultGridType}
                          hasDefaultType={hasDefaultType}
                          gridDetails={item}
                        />{' '}
                        <DeleteDialog
                          matrixOrGridName={item.value}
                          confirmDialog={deleteGridOrMatrixType}
                          isDefaultType={item.isDefaultGridType}
                          parent={parent}
                        />
                      </>
                    )}
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </RadioGroup>
            {parent !== 'matrix' &&
              gridOrMatrixDetails.some(
                item => item.gridCount > 0 && !defaultType
              ) && (
                <Grid item>
                  <Button
                    type="submit"
                    style={{
                      marginLeft: '20px',
                      marginTop: '5px',
                      height: '30px',
                      width: '100px',
                      fontSize: '13px',
                      textTransform: 'none'
                    }}
                    disabled={!gridTypeChanged}
                    onClick={() => saveDefaultGridType(handleShowSnackbar)}
                    variant="contained"
                    color="primary"
                    className={clsx('reset-btn', classes.btnDiv)}
                  >
                    Set as Default
                  </Button>
                </Grid>
              )}
          </List>
        )}
      </Dialog>
      <SuccessSnackbar
        open={openSnackbar}
        onClose={() => setOpenSnackbar(false)}
        msg={statusMessage}
        goalFail={!isSuccessful}
        autoHideDuration={6000}
      />
    </>
  );
};

export default EditGridOrMatrixNameDialog;
