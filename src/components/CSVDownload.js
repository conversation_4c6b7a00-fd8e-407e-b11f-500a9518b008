import React from 'react';
import { csvDataLabor, csvDataParts } from 'src/utils/constants';
import clsx from 'clsx';
import { Button } from '@material-ui/core';
const CSVDownload = ({ typeFor }) => {
  const downloadCSV = () => {
    let csvContent;
    if (typeFor === 'matrix') {
      csvContent = csvDataParts.map(e => e.join(',')).join('\n');
    } else {
      const filteredLaborData = csvDataLabor.map(row => row.slice(0, 11)); // Keep only the first 11 columns
      csvContent = filteredLaborData.map(e => e.join(',')).join('\n');
    }

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute(
      'download',
      typeFor === 'matrix' ? 'partsmatrix.csv' : 'laborgrid.csv'
    );
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'flex-start', marginTop: 20 }}>
    <Button
      variant="contained"
      color="primary"
      onClick={downloadCSV}
      className={clsx('reset-btn', 'btnClass')}
      style={{
        height: '20px',
        fontSize: '13px',
      }}
    >
      Download Sample CSV
    </Button>
  </div>
  );
};

export default CSVDownload;
