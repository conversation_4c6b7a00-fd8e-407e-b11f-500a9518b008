import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';

import Paper from '@material-ui/core/Paper';
import clsx from 'clsx';
import {
  Checkbox,
  FormControlLabel,
  FormGroup,
  Typography,
  Button,
  Tooltip,
  Box
} from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(1),
    maxWidth: 500
  },
  image: {
    width: 100,
    height: 75,
    cursor: 'default'
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '50%'
  },
  container: {
    alignItems: 'center',
    '@media (max-width: 1920px)': {
      width: 250
    },
    '@media (max-width: 1440px)': {
      width: 220
    },
    '@media (max-width: 1280px)': {
      width: 200
    },
    '@media (min-width: 2304px)': {
      width: 350
    }
  },
  titleLabel: {
    display: 'flex',
    fontSize: 15,
    cursor: 'default'
  },
  valLabel: {
    display: 'flex',
    cursor: 'default'
  },
  dataItem: {
    marginLeft: 20,
    padding: '0px !important'
  },
  tooltip: {
    marginTop: '-20px', // Adjust vertical positioning
  }
}));
const IncludedStores = ({
  allStoreNames,
  selectedStoreIds,
  setSelectedStoreIds
}) => {
  const [selectedStores, setSelectedStores] = useState({});
  const [isAllStoresSelected, setIsAllStoresSelected] = useState(false);
  const classes = useStyles();

  useEffect(() => {
    const storeIds = JSON.parse(localStorage.getItem('selectedStoreId')) || []; // IDs from localStorage

    // Extract all store IDs from allStoreNames
    const allStoreIds = allStoreNames.map(store => store.storeId);

    // Check if localStorage IDs exactly match allStoreIds
    const isExactMatch =
      storeIds.length === allStoreIds.length &&
      allStoreIds.every(id => storeIds.includes(id));

    // If exact match, set "All Stores" checked and clear individual checkboxes
    if (isExactMatch) {
      setIsAllStoresSelected(true);
      setSelectedStores(
        allStoreNames.reduce((acc, store) => {
          acc[store.storeId] = false; // Uncheck all individual stores
          return acc;
        }, {})
      );
    } else {
      // If not exact match, populate checkboxes from localStorage
      const initialState = allStoreNames.reduce((acc, store) => {
        // acc[store.storeId] = storeIds.includes(store.storeId);
        acc[store.storeId] = selectedStoreIds.includes(store.storeId);
        return acc;
      }, {});
      const allSelected = Object.values(initialState).every(value => value);
      if (allSelected) {
        setSelectedStores(initialState);
        setIsAllStoresSelected(true);
      } else {
        setSelectedStores(initialState);
        setIsAllStoresSelected(false);
      }
    }
  }, [allStoreNames]);

  const handleAllStoresChange = () => {
    if (isAllStoresSelected) {
      setIsAllStoresSelected(false);
      const storeIds =
        JSON.parse(localStorage.getItem('selectedStoreId')) || [];
      // Uncheck all stores except default selected stores from localStorage
      const updatedStores = allStoreNames.reduce((acc, store) => {
        acc[store.storeId] = storeIds.includes(store.storeId); // Keep default selected stores checked
        return acc;
      }, {});
      setSelectedStores(updatedStores);
    } else {
      setIsAllStoresSelected(true);
      // Check all stores
      const updatedStores = allStoreNames.reduce((acc, store) => {
        acc[store.storeId] = true;
        return acc;
      }, {});
      setSelectedStores(updatedStores);
    }
  };

  const handleIndividualChange = storeId => {
    setSelectedStores(prev => {
      const updatedStores = { ...prev, [storeId]: !prev[storeId] };
      const allSelected = Object.values(updatedStores).every(value => value);
      if (allSelected) {
        setIsAllStoresSelected(true);
        return updatedStores;
      } else {
        setIsAllStoresSelected(false);

        // If no stores are selected, reset to localStorage defaults
        const noneSelected = Object.values(updatedStores).every(
          value => !value
        );
        if (noneSelected) {
          const storeIds =
            JSON.parse(localStorage.getItem('selectedStoreId')) || [];
          return allStoreNames.reduce((acc, store) => {
            acc[store.storeId] = storeIds.includes(store.storeId);
            return acc;
          }, {});
        }

        return updatedStores;
      }
    });
  };

  const getSelectedStoreIds = () => {
    if (isAllStoresSelected) {
      return allStoreNames.map(store => store.storeId);
    }
    return Object.keys(selectedStores).filter(
      storeId => selectedStores[storeId]
    );
  };

  const handleApplyFilter = () => {
    const selectedStoreIds = getSelectedStoreIds();
    setSelectedStoreIds(selectedStoreIds);
  };

  return (
    <Grid container xs={12} className={clsx('sidebar-card', classes.container)}>
      <Typography variant="h5" gutterBottom className="sidebar-view-title">
        Stores Selected
      </Typography>
      <Grid item>
        <Paper className="sidebar-box">
          <FormGroup>
          <Tooltip
              title="All Stores"
              classes={{ tooltip: classes.tooltip }}
            >
              <div>
                <FormControlLabel
                  style={{ pointerEvents: 'none' }}
                  control={
                    <Checkbox
                      checked={isAllStoresSelected}
                      style={{ pointerEvents: 'auto' }}
                      onChange={handleAllStoresChange}
                    />
                  }
                  label="All Stores"
                />
              </div>
            </Tooltip>
            {allStoreNames.map(store => (
              <Tooltip
                key={store.storeId}
                title={store.storeNickname || store.storeName}
                classes={{ tooltip: classes.tooltip }}
              >
                <div>
                  <FormControlLabel
                    key={store.storeId}
                    style={{ pointerEvents: 'none' }}
                    control={
                      <Checkbox
                        checked={selectedStores[store.storeId] || false}
                        style={{ pointerEvents: 'auto' }}
                        onChange={() => handleIndividualChange(store.storeId)}
                      />
                    }
                    label={
                      store.storeNickname
                        ? store.storeNickname
                        : store.storeName
                    }
                  />
                </div>
              </Tooltip>
            ))}
          </FormGroup>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'flex-end', // Aligns the button to the right
              marginTop: 2
            }}
          >
            <Button
              variant="contained"
              color="primary"
              onClick={handleApplyFilter}
              className={clsx('reset-btn', 'btnClass')}
              style={{
                height: '20px',
                fontSize: '13px'
              }}
            >
              Apply
            </Button>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default IncludedStores;
