import * as React from 'react';

import { Snackbar, Button } from '@material-ui/core';
import Alert from '@material-ui/lab/Alert';
import { makeStyles } from '@material-ui/styles';
const useStyles = makeStyles(theme => ({
  snackbar: {
    top: '80px' // Adjust this value to control the vertical position
  }
}));
export default function CustomizedSnackbars({ onClose, open, msg }) {
  const classes = useStyles();
  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return;
    }

    onClose();
  };

  return (
    <Snackbar
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'center'
      }}
      open={open}
      autoHideDuration={1000}
      onClose={handleClose}
      className={classes.snackbar}
    >
      <Alert onClose={handleClose} severity="warning" sx={{ width: '100%' }}>
        {msg}
      </Alert>
    </Snackbar>
  );
}
