import React, { Component } from 'react';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import DeleteForeverOutlinedIcon from '@material-ui/icons/DeleteForeverOutlined';
import RestoreIcon from '@material-ui/icons/Restore';
import { IconButton, Tooltip } from '@material-ui/core';
export default class extends Component {
  constructor(props) {
    super(props);
    this.state = {
      header: this.props.frameworkComponentWrapper.agGridReact.props
        .frameworkComponents
    };
    this.onMenuClicked = this.onMenuClicked.bind(this);
    this.resetButtonClick = this.resetButtonClick.bind(this);
  }

  render() {
    let data = this.props.frameworkComponentWrapper.agGridReact.gridOptions
      .rowData;

    let menu = null;
    let sort = null;
    sort = (
      <div
        style={
          this.state.header.customHeaderComponentReports != undefined
            ? { display: 'inline-block', marginLeft: -34, marginTop: 17 }
            : { display: 'inline-block' }
        }
      >
        <Tooltip
          onClick={this.onMenuClicked}
          title="Delete All"
          style={{ marginLeft: -7, marginTop: -5 }}
        >
          <DeleteForeverOutlinedIcon
            style={{
              width: 20,
              marginLeft: 0,
              color: '#717171',
              marginTop: 4
            }}
          />
        </Tooltip>
      </div>
    );

    return (
      <div>
        {menu}
        <div className="customHeaderLabel">{this.props.displayName}</div>
        {sort}
      </div>
    );
  }

  onMenuClicked() {
    //let header = this.props.frameworkComponentWrapper.agGridReact.props.frameworkComponents;
    this.state.header.customHeaderComponentReports != undefined
      ? this.props.context.componentParent.handleRowSelection()
      : this.props.context.componentParent.handleRowSelection();
  }

  resetButtonClick() {
    //let header = this.props.frameworkComponentWrapper.agGridReact.props.frameworkComponents;
    this.state.header.customHeaderComponentReports != undefined
      ? this.props.context.componentParent.handleResetRowSelection()
      : this.props.filterParams.context.componentParent.handleResetRowSelection();
  }
}
