import React, { useCallback } from 'react';
import {
  Paper,
  Typography,
  FormControl,
  Grid,
  Button,
  TextField,
  Radio,
  RadioGroup
} from '@material-ui/core';
import MuiTableCell from '@material-ui/core/TableCell';
import clsx from 'clsx';
import { makeStyles, withStyles } from '@material-ui/styles';
import moment from 'moment';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Fade from '@material-ui/core/Fade';

var lodash = require('lodash');
const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  linkItem: {
    cursor: 'pointer'
  },
  linkItemDisable: {
    pointerEvents: 'none',
    color: 'grey'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  }
}));

const SaveReportDialog = ({
  openSaveDlg,
  parent,
  copyFile,
  copy,
  reportName,
  requiredText,
  errorReport,
  selectedType,
  filterText,
  reportNameCopy,
  onChangeReportName,
  handleCheckboxChange,
  handleCancelSaveReport,
  handleSaveAsReport,
  handleOkSaveReport
}) => {
  const classes = useStyles();
  return (
    <Dialog
      transition={Fade}
      classes={
        {
          // paper: classes.paper
        }
      }
      BackdropProps={{
        classes: {
          root: classes.backDrop
        }
      }}
      open={openSaveDlg}
    >
      <DialogTitle id="form-dialog-title">
        <Typography
          variant="h5"
          color="primary"
          style={{ textTransform: 'none' }}
        >
          {parent === 'savedReports' && !copy
            ? 'Edit Report'
            : parent === 'savedReports' && copyFile
            ? 'Rename and Copy'
            : 'Save Report'}
        </Typography>
      </DialogTitle>

      <DialogContent style={{ overflowX: 'hidden' }}>
        <TableContainer
          component={Paper}
          style={{ margin: 4, padding: 1, display: 'block', width: '100%' }}
        >
          <Table
            className="email-table"
            id="maildetails"
            size="small"
            aria-label="a dense table"
          >
            <TableHead
              style={{ textAlign: 'center', backgroundColor: '#003d6b' }}
            ></TableHead>

            <TableRow key={'email'}>
              <TableCell
                align="left"
                size="small"
                style={{ fontSize: 14, color: '#003d6b' }}
              >
                Report Name
              </TableCell>
              <TableCell
                align="left"
                colSpan={1}
                size="small"
                style={{ fontSize: 14, color: '#003d6b' }}
              >
                <div style={{ display: 'flex' }}>
                  <div
                    style={{
                      display: 'inline-block',
                      width: 118,
                      marginLeft: 16
                    }}
                  >
                    Access Type
                  </div>
                  <div
                    style={{
                      display: 'inline-block',
                      width: 78,
                      marginLeft: 16
                    }}
                  >
                    Created On
                  </div>
                </div>
              </TableCell>
            </TableRow>

            <TableRow>
              <TableCell height={10}>
                {parent === 'savedReports' && !copy ? (
                  <Typography
                    style={{
                      textTransform: 'none'
                    }}
                  >
                    {reportName ? reportName : reportNameCopy}
                  </Typography>
                ) : (
                  <TextField
                    autoFocus
                    onChange={onChangeReportName}
                    value={reportName}
                    helperText={
                      (reportName === undefined || reportName === '') &&
                      requiredText
                        ? 'This is required!'
                        : errorReport
                    }
                    margin="dense"
                    id="name"
                    type="text"
                    fullWidth
                    inputProps={{ maxLength: 25 }}
                    onInput={e => {
                      const value = e.target.value;
                      if (/[^a-zA-Z0-9 ]/.test(value)) {
                        e.target.value = value.replace(/[^a-zA-Z0-9 ]/g, '');
                      }
                    }}
                  />
                )}
              </TableCell>
              <TableCell>
                <Table>
                  <TableRow>
                    <TableCell
                      align="left"
                      size="small"
                      style={{ fontSize: 14, color: '#003d6b' }}
                    >
                      <FormControl>
                        <RadioGroup
                          aria-labelledby="demo-controlled-radio-buttons-group"
                          name="controlled-radio-buttons-group"
                          id="fixedratetypes"
                          value={selectedType}
                          onChange={handleCheckboxChange}
                        >
                          <FormControlLabel
                            value="public"
                            style={{ marginTop: -6 }}
                            control={<Radio size="small" />}
                            label="Public"
                          />
                          <FormControlLabel
                            value="private"
                            control={<Radio size="small" />}
                            label="Private"
                          />
                        </RadioGroup>
                      </FormControl>
                    </TableCell>
                    <TableCell>
                      <div>{moment().format('MM/DD/YY')}</div>
                    </TableCell>
                  </TableRow>
                </Table>
              </TableCell>
            </TableRow>
          </Table>
        </TableContainer>
      </DialogContent>

      <DialogActions>
        <Button
          variant="contained"
          className={clsx('reset-btn')}
          onClick={handleCancelSaveReport}
          color="primary"
        >
          Cancel
        </Button>

        <Button
          variant="contained"
          className={clsx('reset-btn')}
          disabled={
            (!reportName && !reportNameCopy) ||
            (reportName.trim() === '' && reportNameCopy.trim() === '') ||
            filterText === 'CRANGE'
          }
          onClick={() => {
            if (!reportName || reportName.trim() === '') {
              return;
            }
            parent === 'savedReports' && copyFile
              ? handleSaveAsReport()
              : handleOkSaveReport();
            //  onChangeReportName({ target: { value: '' } });
          }}
          color="primary"
          style={{
            width: 'fit-content',
            minWidth: 0,
            fontSize: '14px',
            height: '24px' // Set a fixed height for the button
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SaveReportDialog;
