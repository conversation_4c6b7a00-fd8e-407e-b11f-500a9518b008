import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import TextField from '@material-ui/core/TextField';
import React, { useEffect, useState } from 'react';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { UPDATE_GOAL_SETTINGS } from '../graphql/queries';
export default function FormDialog({
  openForm,
  closeClick,
  saveClick,
  chartData,
  ...rest
}) {
  const handleClose = () => {
    closeClick(false);
  };
  const [formState, setFormState] = useState({});
  const storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  useEffect(() => {
    if (chartData.length > 0) {
      chartData.map(item => {
        setFormState(prevFormState => ({
          ...prevFormState,
          [item.goal_id]: item.goal_value
        }));
      });
    }
  }, [chartData]);

  const handleSubmit = async event => {
    event.preventDefault();

    Object.keys(formState).forEach(function(key) {
      console.table('Key : ' + key + ', Value : ' + formState[key]);
      updateGoalSettings(formState[key], key);
    });
    handleClose();
    saveClick(true);
  };
  const handleChange = event => {
    event.persist();
    setFormState(prevFormState => ({
      ...prevFormState,
      [event.target.name]: event.target.value
    }));
  };

  function updateGoalSettings(goal_value, goal_id) {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_GOAL_SETTINGS,
        variables: {
          goal_value: goal_value,
          goal_id: goal_id,
          store_id: storeId
        }
      })
      .then(result => {});
  }

  return (
    <div>
      <Dialog
        open={openForm}
        onClose={handleClose}
        aria-labelledby="form-dialog-title"
      >
        <DialogTitle id="form-dialog-title">Goal Settings</DialogTitle>
        <DialogContent>
          <DialogContentText style={{ marginBottom: 25 }}>
            {chartData.description}
          </DialogContentText>

          {chartData.length > 0 && (
            <>
              {chartData.map(item => {
                return (
                  <div>
                    <TextField
                      autoFocus={true}
                      value={formState[item.goal_id]}
                      onChange={handleChange}
                      type="number"
                      key={item.goal_id}
                      style={{ margin: 16 }}
                      label={item.goal_name}
                      name={item.goal_id}
                      id={item.goal_id}
                      variant="standard"
                    />
                  </div>
                );
              })}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose} color="primary">
            Cancel
          </Button>
          <Button onClick={handleSubmit} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
