import React, { useState, useRef, useEffect } from 'react';
import { NavLink as RouterLink } from 'react-router-dom';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { useHistory } from 'react-router';

import $ from 'jquery';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from '@material-ui/styles';
import { ListItem, Button, Collapse } from '@material-ui/core';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ExpandLessIcon from '@material-ui/icons/ExpandLess';
import {
  setNavItemStatus,
  setNavItems,
  setMenuSelected,
  setSelectedTech
} from 'src/actions';
import { TramOutlined } from '@material-ui/icons';
import { traceSpan } from 'src/utils/OTTTracing';
import { ReactSession } from 'react-client-session';

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  item: {
    display: 'block',
    paddingTop: 0,
    paddingBottom: 0
  },
  itemExpanded: {
    width: 215,
    left: '2%'
    // paddingLeft: 10
  },
  itemLeaf: {
    display: 'flex',
    paddingTop: 0,
    borderRadius: 4,
    paddingBottom: 0,
    '&:hover': {
      backgroundColor: theme.palette.primary.active,
      color: theme.palette.primary.main,
      borderRadius: 4
    }
  },
  itemLeafAll: {
    display: 'flex',
    paddingTop: 0,
    borderRadius: 4,
    paddingBottom: 0,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: theme.palette.primary.active,
      color: theme.palette.primary.main,
      borderRadius: 4
    }
  },
  button: {
    padding: '10px 8px',
    justifyContent: 'flex-start',
    textTransform: 'none',
    letterSpacing: 0,
    width: '100%',
    color: '#fff'
  },
  buttonHome: {
    padding: '10px 8px',
    justifyContent: 'flex-start',
    textTransform: 'none',
    letterSpacing: 0,
    width: '100%',
    color: '#fff',
    '&:hover': {
      backgroundColor: theme.palette.primary.active,
      color: theme.palette.primary.main,
      borderRadius: 4,
      '& $icon': {
        color: theme.palette.primary.main
      }
    }
  },
  buttonLeaf: {
    padding: '10px 8px',
    justifyContent: 'flex-start',
    textTransform: 'none',
    letterSpacing: 0,
    width: '100%',
    color: '#fff',
    fontWeight: theme.typography.fontWeightRegular,
    '&:hover': {
      backgroundColor: theme.palette.primary.active,
      color: theme.palette.primary.main,
      borderRadius: 4,
      '& $icon': {
        color: theme.palette.primary.main
      }
    },
    '&.depth-0': {
      fontWeight: theme.typography.fontWeightMedium
    }
  },
  icon: {
    color: theme.palette.icon,
    display: 'flex',
    alignItems: 'center',
    marginRight: theme.spacing(1),
    color: '#fff',
    '&:hover': {
      backgroundColor: theme.palette.primary.active,
      color: theme.palette.primary.main
    }
  },
  expandIcon: {
    marginLeft: 'auto',
    height: 16,
    width: 16
  },
  label: {
    display: 'flex',
    alignItems: 'center',
    marginLeft: 'auto'
  },
  active: {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
    border: '1px solid ' + theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  title: {
    whiteSpace: 'nowrap'
  },
  subTitle: {
    fontSize: 11,
    //float: 'right'
    display: 'flex',
    justifyContent: 'start'
    //marginLeft: 15
  }
}));

function NavItem({
  title,
  href,
  depth,
  children,
  icon: Icon,
  className,
  handleMenuOpen,
  open: openProp,
  label: Label,
  ...rest
}) {
  const classes = useStyles();
  const itemsRef = useRef([]);
  const history = useHistory();
  let Dealer = process.env.REACT_APP_DEALER;
  const dispatch = useDispatch();
  const ref = useRef(null);
  const session = useSelector(state => state.session);
  const [open, setOpen] = useState(openProp);
  const [activeClass, setActiveClass] = useState('');
  const [menuTitle, setMenuTitle] = useState('');
  const [openNavItems, setOpenNavItems] = useState([]);
  useEffect(() => {
    $('#navBarDiv .active-menu').removeClass('active-menu');
    // if (window.location.pathname == '/Home') {
    //   dispatch(setMenuSelected('Home'));
    //   setOpenNavItems(['Home']);
    //   itemsRef.current = document.getElementById('Home');

    //   if (typeof itemsRef.current.offsetParent != 'undefined') {
    //     $('.leftNav')
    //       .stop(true, false)
    //       .animate(
    //         {
    //           scrollTop: itemsRef.current.offsetParent.offsetTop - 45
    //         },
    //         1000
    //       );
    //   }
    // }

    if (window.location.pathname == '/2.4.0/Home') {
      dispatch(setMenuSelected('Home'));
      setOpenNavItems(['Home']);
      itemsRef.current = document.getElementById('Homenew');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop - 45
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/Home') {
      dispatch(setMenuSelected('Home'));
      setOpenNavItems(['Home']);
      itemsRef.current = document.getElementById('Home');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop - 45
            },
            1000
          );
      }
      // var postData = {
      //   realm: localStorage.getItem('realm'),
      //   clientSecret: 'TSvhBfIKgkF4TZLeBYKa9Q2fuJx0Z8Wh',
      //   token: localStorage.getItem('keycloakToken')
      // };
      // fetch(process.env.REACT_APP_TOKEN_VALIDATOR, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json'
      //   },
      //   body: JSON.stringify(postData)
      // })
      //   .then(response => {
      //     if (response.status == 403 || response.status == 500) {
      //       localStorage.setItem('tokenexpired', true);
      //       window.location.href = '/auth/login';
      //     }
      //   })
      //   .catch(error => console.error('Error posting data:', error));
    }
    if (window.location.pathname == '/GraphDetailsView') {
      const params = new URLSearchParams(window.location.search);

      const parentId = params.get('chartId');
      if (parentId == 1090) {
        dispatch(
          setMenuSelected('Scatter Plot - Labor \n  Jobs / Hours / ELR')
        );
      } else if (parentId == 931) {
        dispatch(
          setMenuSelected('What If” Opportunity \n Hrs Per RO & Labor GP%')
        );
      } else if (parentId == 921) {
        dispatch(
          setMenuSelected('What If” Opportunity \n Effective Labor Rate')
        );
      } else if (parentId == 1096) {
        dispatch(
          setMenuSelected('Scatter Plot - Parts \n Cost / Jobs / Markup')
        );
      } else if (parentId == 926) {
        //  var element = document.getElementById('what-if-parts');
        dispatch(
          setMenuSelected('“What If” Opportunity \n Hrs Per RO & Parts GP%')
        );
      } else if (parentId == 1114 || parentId == 1125) {
        //  var element = document.getElementById('what-if-parts');
        console.log('history==', history);
        dispatch(setMenuSelected('Discount Metrics'));
      } else if (parentId == 1264 || parentId == 1265) {
        //  var element = document.getElementById('what-if-parts');

        dispatch(setMenuSelected('Tech Metrics'));
      } else if (
        parentId == 1243 ||
        parentId == 1244 ||
        parentId == 1245 ||
        parentId == 1246 ||
        parentId == 1247 ||
        parentId == 1248
      ) {
        setTimeout(() => {
          $('#navBarDiv')
            .find("li[id='Advisor Metrics']")
            .removeAttr('class');
        }, 500);
        dispatch(setMenuSelected('Labor Work Mix'));
        setOpenNavItems(['Labor']);
      } else if (
        parentId == 1253 ||
        parentId == 1254 ||
        parentId == 1255 ||
        parentId == 1256 ||
        parentId == 1257 ||
        parentId == 1258
      ) {
        setTimeout(() => {
          $('#navBarDiv')
            .find("li[id='Advisor Metrics']")
            .removeAttr('class');
        }, 500);

        dispatch(setMenuSelected('Parts Work Mix'));
        setOpenNavItems(['Parts']);
      }
    }
    if (window.location.pathname == '/MyFavorites') {
      dispatch(setMenuSelected('Favorites'));
    }
    if (window.location.pathname == '/2.4.0/Home') {
      dispatch(setMenuSelected('Home'));
    }
    if (window.location.pathname == '/LaborWorkMixAnalysis') {
      dispatch(setMenuSelected('Labor Work Mix'));
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/CPPartsOverview') {
      dispatch(setMenuSelected('Parts Overview'));
      setOpenNavItems(['Parts']);
    }
    if (window.location.pathname == '/PartsWorkMixAnalysis') {
      dispatch(setMenuSelected('Parts Work Mix'));
      setOpenNavItems(['Parts']);
    }
    if (window.location.pathname == '/AnalyzeData') {
      $('#navBarDiv')
        .find("a[id='" + session.menuSelected + "']")
        .addClass('active-menu');
    }
    if (window.location.pathname == '/PartsMisses') {
      dispatch(setMenuSelected('Repair Parts \n Target Misses'));
      setOpenNavItems(['Parts']);
    }
    if (window.location.pathname == '/LaborMisses') {
      dispatch(setMenuSelected('Repair Labor \n Target Misses'));
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/LaborGridMisses') {
      dispatch(setMenuSelected('Repair Labor \n Target Misses '));
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/PartsTargetMisses') {
      dispatch(setMenuSelected('Repair Parts \n Target Misses '));
      setOpenNavItems(['Parts']);
    }
    if (window.location.pathname == '/WorkMixVolume') {
      dispatch(setMenuSelected('Labor Work Mix \n Other'));
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/2.4.0/WorkMixVolume') {
      dispatch(setMenuSelected('Labor Work Mix \n Other'));
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/WorkMixVolumeParts') {
      dispatch(setMenuSelected('Parts Work Mix \n Other'));
      setOpenNavItems(['Parts']);
    }
    if (window.location.pathname == '/PayTypeMaster') {
      dispatch(setMenuSelected('Pay Types'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/OPcodes') {
      dispatch(setMenuSelected('Opcode Categorizations'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/NonCPOpcodes') {
      dispatch(setMenuSelected('Opcode Categorizations'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/ServiceAdvisors') {
      dispatch(setMenuSelected('Service Advisors'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/PartsMatrix') {
      dispatch(setMenuSelected('Parts Matrix(s)'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/Technicians') {
      dispatch(setMenuSelected('Technicians'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/LaborGridPricing') {
      dispatch(setMenuSelected('Labor Grid(s)'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/PartsMatrixPricing') {
      dispatch(setMenuSelected('Parts Matrix(s)'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/PartsMatrixPricings') {
      dispatch(setMenuSelected('Parts Matrix(s) '));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/ScoreCardGoalSetting') {
      dispatch(setMenuSelected('Goal Settings \n KPI Report'));
      setOpenNavItems(['Reference / Setups']);
    }
    // if (window.location.pathname == '/KpiReport') {
    //   dispatch(setMenuSelected('KPI Report #1  \n Individual Advisor'));
    //   setOpenNavItems(['Reports']);
    // }
    if (window.location.pathname == '/MPIStats') {
      dispatch(setMenuSelected('MPI Stats'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/KpiReport') {
      dispatch(setMenuSelected('KPI Advisor'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/ReportSaved') {
      dispatch(setMenuSelected('Reports Saved'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/KPIReportComparative') {
      dispatch(setMenuSelected('KPI Advisor Comparative'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/KPIReportStoreComparative') {
      dispatch(setMenuSelected('KPI Store Comparative'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/KPIReportTechComparative') {
      dispatch(setMenuSelected('KPI Tech Comparative'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/LaborGrossAndVolumeOpportunity') {
      dispatch(
        setMenuSelected('“What If” Opportunity \n Hrs Per RO & Labor GP%')
      );
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/CPELROpportunity') {
      dispatch(
        setMenuSelected('“What If” Opportunity \n Effective Labor Rate')
      );
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/PartsGrossAndVolumeOpportunity') {
      dispatch(
        setMenuSelected('“What If” Opportunity \n Hrs Per RO & Parts GP%')
      );
      setOpenNavItems(['Parts']);
    }
    // if(window.location.pathname == '/ServiceAdvisorPerformance') {
    //   dispatch(setMenuSelected('Advisor Metrics'));
    //   setOpenNavItems(['Advisor Metrics']);
    // }
    if (window.location.pathname == '/JobCountGrid') {
      dispatch(setMenuSelected('Job Count Grid'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/KpiGraphics') {
      dispatch(setMenuSelected('KPI Graphics'));
      setOpenNavItems(['Reports']);
    }
    if (window.location.pathname == '/DailyDataImports') {
      dispatch(setMenuSelected('Daily Data Imports'));
      setOpenNavItems(['Armatus Admin']);
    }
    if (window.location.pathname == '/FleetAccounts') {
      dispatch(setMenuSelected('Fleet Accounts'));
      setOpenNavItems(['Reference / Setups']);
      itemsRef.current = document.getElementById('setups');
      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 200
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/EditHistory') {
      dispatch(setMenuSelected('Edit History'));
      setOpenNavItems(['Reference / Setups']);
      itemsRef.current = document.getElementById('setups');
      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 600
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/EditHistoryAll') {
      dispatch(setMenuSelected('Edit History All'));
      setOpenNavItems(['Armatus Admin']);
    }
    if (window.location.pathname == '/RoShowHide') {
      dispatch(setMenuSelected('Hide RO - Rules'));
      setOpenNavItems(['Armatus Admin']);
    }
    if (window.location.pathname == '/TrendReport') {
      dispatch(setMenuSelected('Trend Report'));
      setOpenNavItems(['Reports']);
    }

    if (window.location.pathname == '/LaborItemization') {
      dispatch(setMenuSelected('Scatter Plot - Labor \n  Jobs / Hours / ELR'));
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/CPLaborOverview') {
      dispatch(setMenuSelected('Labor Overview'));
      setOpenNavItems(['Labor']);
    }
    if (window.location.pathname == '/CPOverview') {
      dispatch(setMenuSelected('CP Summary Overview'));
    }
    if (window.location.pathname == '/FixedRates') {
      dispatch(setMenuSelected('Fixed Rate History'));
      setOpenNavItems(['Reference / Setups']);
    }

    if (window.location.pathname == '/WarrantyRatesLabor') {
      dispatch(setMenuSelected('Labor - Wty Jobs'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/CustomerHistory') {
      dispatch(setMenuSelected('Customer History'));
      setOpenNavItems(['Reference / Setups']);
      itemsRef.current = document.getElementById('setups');
      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 600
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/GridModelMapping') {
      dispatch(setMenuSelected('Model Mapping - \n Grid'));
      setOpenNavItems(['Armatus Admin']);
      itemsRef.current = document.getElementById('armatus-admin');
      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 600
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/WarrantyMarkupParts') {
      dispatch(setMenuSelected('Parts - Wty Jobs'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/Glossary') {
      dispatch(setMenuSelected('Glossary'));
      setOpenNavItems(['Reference / Setups']);
    }
    if (window.location.pathname == '/Discounts') {
      dispatch(setMenuSelected('Discount Metrics'));
    }
    if (window.location.pathname == '/PartsItemization') {
      dispatch(setMenuSelected('Scatter Plot - Parts \n Cost / Jobs / Markup'));
      setOpenNavItems(['Parts']);
    }
    if (window.location.pathname == '/ChangeLog') {
      dispatch(setMenuSelected(''));
      setOpenNavItems(['']);
    }
    if (window.location.pathname == '/KeycloakUserCreation') {
      dispatch(setMenuSelected('Credentials'));
      setOpenNavItems(['Armatus Admin']);
    }
    if (window.location.pathname == '/TechnicianPerformance') {
      dispatch(setMenuSelected('Tech Metrics'));
      setOpenNavItems(['Tech Metrics']);
    }
    if (window.location.pathname == '/ServiceAdvisorPerformance') {
      dispatch(setMenuSelected('Advisor Metrics'));
      setOpenNavItems(['Advisor Metrics']);
    }
    if (window.location.pathname == '/SpecialMetrics') {
      dispatch(setMenuSelected('Special Metrics'));
      itemsRef.current = document.getElementById('special-metrics');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/StoreAssignments') {
      dispatch(setMenuSelected('Store Assignments'));
      setOpenNavItems(['Armatus Admin']);
      itemsRef.current = document.getElementById('armatus-admin');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 600
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/Mpi') {
      dispatch(setMenuSelected('Menu / MPI Setups'));
      setOpenNavItems(['Armatus Admin']);
      itemsRef.current = document.getElementById('armatus-admin');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 600
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/MenuModelMapping') {
      dispatch(setMenuSelected('Model Mapping - \n Menu'));
      setOpenNavItems(['Armatus Admin']);
      itemsRef.current = document.getElementById('armatus-admin');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 600
            },
            1000
          );
      }
    }
    if (window.location.pathname == '/GridModelMapping') {
      dispatch(setMenuSelected('Model Mapping - \n Grid'));
      setOpenNavItems(['Armatus Admin']);
      itemsRef.current = document.getElementById('armatus-admin');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop + 600
            },
            1000
          );
      }
    }

    if (window.location.pathname == '/ThreeMonthReport') {
      dispatch(setMenuSelected('3 Month Total'));
      setOpenNavItems(['Armatus Admin', 'Client Report Card']);
    }

    if (window.location.pathname == '/OneMonthReport') {
      dispatch(setMenuSelected('1 Month'));
      setOpenNavItems(['Armatus Admin', 'Client Report Card']);
    }
    if (window.location.pathname == '/SavedReports') {
      dispatch(setMenuSelected('Saved Reports'));
      setOpenNavItems(['Armatus Admin', 'Client Report Card']);
    }
    if (
      window.location.pathname == '/LaborMisses' ||
      window.location.pathname == '/LaborGridMisses' ||
      window.location.pathname == '/WorkMixVolume' ||
      window.location.pathname == '/CPLaborOverview' ||
      window.location.pathname == '/LaborItemization' ||
      window.location.pathname == '/CPELROpportunity' ||
      window.location.pathname == '/LaborGrossAndVolumeOpportunity' ||
      window.location.pathname == '/LaborWorkMixAnalysis'
    ) {
      itemsRef.current = document.getElementById('Labor');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop
            },
            1000
          );
      }
    }
    if (
      window.location.pathname == '/CPPartsOverview' ||
      window.location.pathname == '/PartsWorkMixAnalysis' ||
      window.location.pathname == '/WorkMixVolumeParts' ||
      window.location.pathname == '/PartsItemization' ||
      window.location.pathname == '/PartsGrossAndVolumeOpportunity' ||
      window.location.pathname == '/PartsMisses' ||
      window.location.pathname == '/PartsTargetMisses'
    ) {
      itemsRef.current = document.getElementById('Parts');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop
            },
            1000
          );
      }
    }
    if (
      window.location.pathname == '/KpiReport' ||
      window.location.pathname == '/KpiGraphics' ||
      window.location.pathname == '/JobCountGrid' ||
      window.location.pathname == '/TrendReport' ||
      window.location.pathname == '/MPIStats'
    ) {
      itemsRef.current = document.getElementById('Reports');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        $('.leftNav')
          .stop(true, false)
          .animate(
            {
              scrollTop: itemsRef.current.offsetParent.offsetTop
            },
            1000
          );
      }
    }
    if (
      window.location.pathname == '/ServiceAdvisors' ||
      window.location.pathname == '/Technicians' ||
      window.location.pathname == '/PayTypeMaster' ||
      window.location.pathname == '/OPcodes' ||
      window.location.pathname == '/NonCPOpcodes' ||
      window.location.pathname == '/FixedRates'
    ) {
      itemsRef.current = document.getElementById('setups');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        if (
          typeof history.location.state != 'undefined' &&
          history.location.state != null
        ) {
          $('.leftNav')
            .stop(true, false)
            .animate(
              {
                scrollTop:
                  itemsRef.current.offsetParent.offsetTop > 800
                    ? 413
                    : itemsRef.current.offsetParent.offsetTop
              },
              1000
            );
        } else {
          $('.leftNav')
            .stop(true, false)
            .animate(
              {
                scrollTop:
                  itemsRef.current.offsetParent.offsetTop > 600
                    ? 413
                    : itemsRef.current.offsetParent.offsetTop
              },
              1000
            );
        }
      }
    }
    if (
      window.location.pathname == '/PartsMatrixPricing' ||
      window.location.pathname == '/PartsMatrixPricings' ||
      window.location.pathname == '/LaborGridPricing' ||
      window.location.pathname == '/DailyDataImports' ||
      window.location.pathname == '/ScoreCardGoalSetting' ||
      window.location.pathname == '/PartsMatrix'
    ) {
      itemsRef.current = document.getElementById('setups');

      if (typeof itemsRef.current.offsetParent != 'undefined') {
        if (
          typeof history.location.state != 'undefined' &&
          history.location.state != null
        ) {
          $('.leftNav')
            .stop(true, false)
            .animate(
              {
                scrollTop:
                  itemsRef.current.offsetParent.offsetTop > 800
                    ? itemsRef.current.offsetParent.offsetTop
                    : itemsRef.current.offsetParent.offsetTop + 400
              },
              1000
            );
        }
      }
    }
  }, [window.location.pathname]);
  const handleToggle = () => {
    let navItems = openNavItems;

    if (openNavItems.includes(title)) {
      navItems = navItems.filter(function(item) {
        return item !== title;
      });
      handleMenuOpen(1);
      setOpenNavItems(navItems);
    } else {
      handleMenuOpen(0);
      navItems.unshift(title);
      setOpenNavItems(navItems);
      setOpen(prevOpen => !prevOpen);
    }

    if (title == 'Reference / Setups') {
      $('#Store Settings').css('display', 'none');
    }

    if (itemsRef.current.id != 'Client Report Card') {
      $('.leftNav').animate(
        {
          scrollTop: itemsRef.current.offsetParent.offsetTop
        },
        1000
      );
    }
  };

  const handleToggleExpandCollapse = () => {
    let status = session.navItemStatus;

    dispatch(setNavItemStatus(status ? false : true));
  };
  const handleMenuClick = title => {
    console.log('pppp===aaa');
    setTimeout(() => {
      ReactSession.set('selectedReport', '');
    }, 500);
    localStorage.setItem('title', title);
    localStorage.setItem('laborTabSelection', '');
    localStorage.setItem('partsTabSelection', '');
    dispatch(setSelectedTech('All[All]'));
    if (title != 'Discount Metrics') {
      localStorage.setItem('discountTabSelection', '');
    }
    if (title != 'Tech Metrics') {
      localStorage.setItem('technicianTabSelection', '');
    }
    if (title != 'Labor Work Mix') {
      localStorage.setItem('laborTabSelection', '');
    }
    if (title != 'Parts Work Mix') {
      localStorage.setItem('partsTabSelection', '');
    }
    if (title != 'Advisor Metrics') {
      localStorage.setItem('advisorTabSelection', '');
    }
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Menu Click',
      value: title,
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Menu Click', spanAttribute);
    // $('#navBarDiv .active-menu').removeClass('active-menu');
    dispatch(setMenuSelected(title));
    // var postData = {
    //   realm: localStorage.getItem('realm'),
    //   clientSecret: 'TSvhBfIKgkF4TZLeBYKa9Q2fuJx0Z8Wh',
    //   token: localStorage.getItem('keycloakToken')
    // };

    // fetch(process.env.REACT_APP_TOKEN_VALIDATOR, {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json'
    //   },
    //   body: JSON.stringify(postData)
    // })
    //   .then(response => {
    //     console.log('resss==', response);
    //     if (response.status == 403 || response.status == 500) {
    //       localStorage.setItem('tokenexpired', true);
    //       window.location.href = '/auth/login';
    //     }
    //   })

    //   .catch(error => console.error('Error posting data:', error));
  };
  let paddingLeft = 4;
  //console.log('title==', title, depth);
  if (depth > 0) {
    paddingLeft = 20;
  }
  if (
    // title == 'Labor' ||
    //   title == 'Parts' ||
    (title == 'Gross & Volume Opportunity' || title == 'ELR Opportunity') &
    (depth == 0)
  ) {
    paddingLeft = 18;
  }
  if (
    title == '1 Month' ||
    title == '3 Month Total' ||
    title == 'Saved Reports'
  ) {
    paddingLeft = 35;
  }
  if ((title == 'Gross & Volume Opportunity') & (depth == 0)) {
    paddingLeft = 21;
  }
  useEffect(() => {
    // if (window.location.pathname == '/KPIReportStoreComparative') {
    //   dispatch(setMenuSelected('KPI Store Comparative'));
    //   setOpenNavItems(['Reports']);
    // } else if (window.location.pathname == '/KpiGraphics') {
    //   dispatch(setMenuSelected('KPI Graphics'));
    //   setOpenNavItems(['Reports']);
    // } else if (window.location.pathname == '/Home') {
    //   dispatch(setMenuSelected('Home'));
    // } else {
    //   setOpenNavItems(session.navItems);
    // }
    setOpenNavItems(session.navItems);
  }, [session.navItems]);

  useEffect(() => {
    if (openNavItems.includes(title)) {
      setOpen(true);
    } else {
      setOpen(false);
    }
  }, [openNavItems]);

  const style = {
    paddingLeft

    //color: session.navItemStatus ? '#c2185b' : ''
  };
  const styleRoot = {
    paddingLeft,
    fontSize: 14
    //color: session.navItemStatus ? '#c2185b' : ''
  };
  const styleRootAllStores = {
    paddingLeft,
    fontSize: 14,
    pointerEvents: 'none'
    //color: session.navItemStatus ? '#c2185b' : ''
  };
  const styleList = {
    paddingLeft,
    fontSize: 14,
    color: session.navItemStatus
      ? Dealer == 'Armatus'
        ? '#fff'
        : '#C2185B'
      : ''
  };
  useEffect(() => {
    setOpen(session.navItemStatus);
  }, [session.navItemStatus]);
  // if (
  //   session.navItems[0] != null &&
  //   typeof session.navItems[0] != 'undefined' &&
  //   session.navItems[0] != 'Home'
  // ) {
  //   let id;
  //   if (session.navItems[0] == 'Setups & Other Data') {
  //     id = 'setups';
  //   } else {
  //     id = session.navItems[0];
  //   }
  //   const navElement = document.getElementById(id);

  //   if (navElement != null) {
  //     navElement.scrollIntoView();
  //   } else {
  //     document.getElementById('Customer Pay').scrollIntoView();
  //   }
  // }

  if (children) {
    return (
      <ListItem
        {...rest}
        className={clsx(
          classes.item,
          className,
          session.navItemStatus ? classes.itemExpanded : ''
        )}
        id={
          title == 'Reference / Setups'
            ? 'setups_li'
            : title == 'Armatus Admin'
            ? 'armatus-admin_li'
            : title + '_li'
        }
        disableGutters
        key={title}
        label={session.navItemStatus ? 'expanded' : 'collapsed'}
      >
        <Button
          ref={title =>
            (itemsRef.current =
              title == 'Reference / Setups'
                ? 'setups'
                : title == 'Armatus Admin'
                ? 'armatus-admin'
                : title)
          }
          className={classes.button}
          onClick={handleToggle}
          style={
            JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
            !title.includes('Reports') &&
            !title.includes('Labor') &&
            !title.includes('Parts') &&
            !title.includes('Armatus Admin') &&
            !title.includes('Client Report Card')
              ? styleRootAllStores
              : styleRoot
          }
          id={
            title == 'Reference / Setups'
              ? 'setups'
              : title == 'Armatus Admin'
              ? 'armatus-admin'
              : title
          }
        >
          {Icon && <Icon className={classes.icon} />}
          {title}
          {open ? (
            <ExpandLessIcon className={classes.expandIcon} color="inherit" />
          ) : (
            <ExpandMoreIcon className={classes.expandIcon} color="inherit" />
          )}
        </Button>
        <Collapse in={open}>{children}</Collapse>
      </ListItem>
    );
  }
  if (
    (title == 'Favorites' && href == '/MyFavorites') ||
    (title == 'Home' && href == '/Home') ||
    (title == 'Homenew' && href == '/2.4.0/Home')
  ) {
    return (
      <ListItem
        {...rest}
        className={clsx(
          classes.item,
          className,
          session.navItemStatus ? classes.itemExpanded : ''
        )}
        id={title + '_li'}
        disableGutters
        key={title}
      >
        <Button
          activeClassName={session.menuSelected == title ? classes.active : ''}
          className={classes.buttonHome}
          component={RouterLink}
          exact
          style={{ paddingLeft: paddingLeft, fontSize: 14 }}
          to={href}
          id={title}
          onClick={() => handleMenuClick(title)}
        >
          {title}
        </Button>
      </ListItem>
    );
  }
  if (title == 'Expand/Collapse All' && href == '#') {
    return (
      <ListItem
        {...rest}
        className={clsx(
          classes.item,
          className,
          session.navItemStatus ? classes.itemExpanded : '',
          JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
            ? classes.itemLeafAll
            : ''
        )}
        id={title + '_li'}
        disableGutters
        key={title}
      >
        <Button
          className={classes.button}
          onClick={handleToggleExpandCollapse}
          style={styleList}
          id={title}
        >
          {Icon && <Icon className={classes.icon} />}
          {title}
        </Button>
      </ListItem>
    );
  }
  return (
    <ListItem
      {...rest}
      className={clsx(
        JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
          !title.includes('KPI Store Comparative') &&
          !title.includes('KPI Graphics') &&
          !title.includes('MPI Stats') &&
          !title.includes('Repair Labor') &&
          !title.includes('Repair Parts') &&
          !title.includes('3 Month Total') &&
          !title.includes('1 Month') &&
          !title.includes('Reports Saved') &&
          !title.includes('Saved Reports')
          ? classes.itemLeafAll
          : classes.itemLeaf,
        className,
        session.navItemStatus ? classes.itemExpanded : ''
      )}
      id={
        title == 'Scatter Plot - Labor \n  Jobs / Hours / ELR'
          ? 'Scatter-Plot-Labor_li'
          : title == '“What If” Opportunity \n Hrs Per RO & Labor GP%'
          ? 'what-if-Labor_li'
          : title == '“What If” Opportunity \n Effective Labor Rate'
          ? 'what-if-elr_li'
          : title == 'Scatter Plot - Parts \n Cost / Jobs / Markup'
          ? 'Scatter-Plot-Parts_li'
          : title == '“What If” Opportunity \n Hrs Per RO & Parts GP%'
          ? 'what-if-parts_li'
          : title == 'Repair - Labor Target Misses'
          ? 'labor-grid-misses_li'
          : title == 'Repair - Parts Target Misses'
          ? 'parts-matrics-misses_li'
          : title == 'KPI Report \n Goal Settings'
          ? 'kpi-report-goal-settings_li'
          : title == 'Labor Work Mix \n Other'
          ? 'labor-workmix-other_li'
          : title == 'Parts Work Mix \n Other'
          ? 'parts-workmix-other_li'
          : title == 'Model Mapping - \n Menu'
          ? 'model-mapping-menu_li'
          : title == 'Model Mapping - \n Grid'
          ? 'model-mapping-grid_li'
          : title
      }
      disableGutters
      key={title}
    >
      <Button
        activeClassName={session.menuSelected == title ? classes.active : ''}
        className={clsx(
          classes.buttonLeaf,
          `depth-${depth}`,
          session.menuSelected == title ? classes.active : ''
        )}
        component={RouterLink}
        exact
        style={style}
        to={href}
        id={
          title == 'Scatter Plot - Labor \n  Jobs / Hours / ELR'
            ? 'Scatter-Plot-Labor'
            : title == '“What If” Opportunity \n Hrs Per RO & Labor GP%'
            ? 'what-if-Labor'
            : title == '“What If” Opportunity \n Effective Labor Rate'
            ? 'what-if-elr'
            : title == 'Scatter Plot - Parts \n Cost / Jobs / Markup'
            ? 'Scatter-Plot-Parts'
            : title == '“What If” Opportunity \n Hrs Per RO & Parts GP%'
            ? 'what-if-parts'
            : title == 'Repair - Labor Target Misses'
            ? 'labor-grid-misses'
            : title == 'Repair - Parts Target Misses'
            ? 'parts-matrics-misses'
            : title == 'KPI Report \n Goal Settings'
            ? 'kpi-report-goal-settings'
            : title == 'Labor Work Mix \n Other'
            ? 'labor-workmix-other'
            : title == 'Parts Work Mix \n Other'
            ? 'parts-workmix-other'
            : title == 'Special Metrics'
            ? 'special-metrics'
            : title
        }
        onClick={() => handleMenuClick(title)}
      >
        {Icon && <Icon className={classes.icon} />}
        {title.split('\n').length > 1 ? (
          <div>
            <span className={classes.title}>{title.split('\n')[0]}</span>
            <span className={classes.subTitle}>{title.split('\n')[1]}</span>
          </div>
        ) : (
          title
        )}
        {Label && (
          <span className={classes.label}>
            <Label />
          </span>
        )}
      </Button>
    </ListItem>
  );
}

NavItem.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string,
  depth: PropTypes.number.isRequired,
  href: PropTypes.string,
  icon: PropTypes.any,
  label: PropTypes.any,
  open: PropTypes.bool,
  title: PropTypes.string.isRequired
};

NavItem.defaultProps = {
  open: false
};

export default NavItem;
