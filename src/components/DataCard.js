import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';
import ButtonBase from '@material-ui/core/ButtonBase';
import LocationCityIcon from '@material-ui/icons/LocationCity';
import BuildIcon from '@material-ui/icons/Build';

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(1),
    //marginRight: 'auto',
    maxWidth: 500
  },
  image: {
    width: 100,
    height: 75,
    cursor: 'default'
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '50%'
  },
  container: {
    alignItems: 'center',
    '@media (max-width: 1920px)': {
      width: 250
      //fontSize: 25,
      //marginLeft: '23%'
    },

    '@media (max-width: 1440px)': {
      width: 220
      //fontSize: 25,
      //marginLeft: '23%'
    },

    '@media (max-width: 1280px)': {
      width: 200
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 350
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  titleLabel: {
    display: 'flex',
    fontSize: 15,
    cursor: 'default'
  },
  valLabel: {
    display: 'flex',
    cursor: 'default'
  },
  dataItem: {
    marginLeft: 20,
    padding: '0px !important'
  }
}));

export default function DataCard(props) {
  const classes = useStyles();

  return (
    <Paper className={classes.paper}>
      <Grid
        container
        xs={12}
        className={classes.container}
        id={props.title + 'grid'}
      >
        <Grid item xs={4}>
          <ButtonBase className={classes.image}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={`/images/img/${props.icon}.png`}
            />
          </ButtonBase>
        </Grid>
        <Grid item xs={8} sm>
          <Grid item xs container direction="column" spacing={2}>
            <Grid item xs className={classes.dataItem}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                {props.title}
              </Typography>

              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.value}
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
}
