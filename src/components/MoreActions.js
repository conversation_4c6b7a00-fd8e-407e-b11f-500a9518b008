import { I<PERSON><PERSON><PERSON><PERSON>, <PERSON>nackbar, Tooltip, Typography } from '@material-ui/core';
import ExitToAppIcon from '@material-ui/icons/ExitToApp';
import InfoIcon from '@material-ui/icons/Info';
import StarIcon from '@material-ui/icons/Star';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import { Alert } from '@material-ui/lab';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import React, { memo, useRef, useState, useEffect } from 'react';
import ReactGA from 'react-ga';
import AssignmentReturnOutlinedIcon from '@material-ui/icons/AssignmentReturnOutlined';
import RestoreIcon from '@material-ui/icons/Restore';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import {
  setFavouriteCharts,
  setFavouriteChartsComparisonMonths,
  setServiceAdvisor
} from 'src/actions';
import { GET_GOAL_SETTINGS } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClient';
import {
  addChartToFavourite,
  getAllFavouriteCharts,
  removeChartFromFavourite
} from 'src/utils/hasuraServices';
import ReportIcon from '@material-ui/icons/Assignment';
import ZoomOutMapIcon from '@material-ui/icons/ZoomOutMap';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { traceSpan } from 'src/utils/OTTTracing';
var lodash = require('lodash');

require('dotenv').config();
const useStyles = makeStyles(theme => ({
  active: {
    color: '#fff'
  },
  icon: {
    padding: 6
  }
}));
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 300,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b',

    backgroundColor: '#ddeaf4'
  }
}))(Tooltip);
function MoreActions({
  className,
  setActions,
  removeFavourite,
  chartPopup,
  enablePan,
  panEnabled,
  handleClose,
  handleBackToButton,
  resetDashboard,
  type,
  chartModel,
  dbdName,
  handleHighlight,
  selectedChartId,
  ...rest
}) {
  const classes = useStyles();
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const ChartData = window.$chartList;
  const history = useHistory();
  const moreRef = useRef(null);
  const [openMenu, setOpenMenu] = useState(false);
  const [popup, setPopup] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [openRemovedSnackbar, setopenRemovedSnackbar] = useState(false);
  const storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];

  const handleFavouriteClick = () => {
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'AddTo Favourites',
      chartname: rest.chartId ? getChartName(rest.chartId.toString()) : '',
      fromurl: history.location.pathname ? history.location.pathname : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Chart MoreActions', spanAttribute);
    if (session.favouriteCharts.includes(rest.chartId.toString())) {
      // already in favourites & delete from db
      removeChartFromFavourite(
        rest.chartId.toString(),
        session.userName,
        callback => {
          if (callback) {
            setopenRemovedSnackbar(true);
            setTimeout(() => {
              removeFavourite(2);
              refreshFavourites();
              // handlePopupClose();
              if (history.location.pathname == '/MyFavorites') {
                setTimeout(() => {
                  removeFavourite(3);
                }, 4000);

                // window.location.reload();
              }
            }, 1000);
          }
        }
      );
    } else {
      // not in favourites & add to db
      addChartToFavourite(
        rest.chartId.toString(),
        session.userName,
        callback => {
          if (callback) {
            setopenSnackbar(true);
            if (rest.month1 || rest.month2) {
              dispatch(
                setFavouriteChartsComparisonMonths([rest.month1, rest.month2])
              );
            }
            setTimeout(() => {
              refreshFavourites();
            }, 1000);
          }
        }
      );
    }

    // dispatch(setFavouriteCharts())

    if (
      selectedChartId &&
      selectedChartId == rest.chartId &&
      (history.location.pathname == '/CPOverview' ||
        history.location.pathname == '/CPLaborOverview' ||
        history.location.pathname == '/CPPartsOverview' ||
        history.location.pathname == '/SpecialMetrics' ||
        (history.location.pathname == '/Discounts' &&
          rest.chartId != '1125' &&
          rest.chartId != '1124' &&
          rest.chartId != '1126' &&
          rest.chartId != '1114'))
    ) {
      handleHighlight(rest.chartId);
    }
  };

  const refreshFavourites = () => {
    getAllFavouriteCharts(session.userName, callback => {
      if (callback) {
        dispatch(
          setFavouriteCharts(lodash.uniq(callback.map(data => data.favourites)))
        );
      }
    });
  };

  const handlePopupClick = () => {
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Chart Popup',
      chartname: rest.chartId ? getChartName(rest.chartId.toString()) : '',
      fromurl: history.location.pathname ? history.location.pathname : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Chart MoreActions', spanAttribute);
    chartPopup(rest.chartId);
    setPopup(true);
  };

  const handlePopupClose = () => {
    handleClose(rest.chartId);
    setPopup(false);

    //setChartOpen(true);
    //dispatch(setZoomStatus(true));
  };
  const handleBackButton = () => {
    handleBackToButton(rest.chartId);
  };

  const handleMenuClose = () => {
    setOpenMenu(false);
  };
  const hidesnackbar = () => {
    setopenSnackbar(false);
  };
  const hidesnackbar2 = () => {
    setopenRemovedSnackbar(false);
  };

  const handleclick = (val, currentRate) => {
    localStorage.setItem('popup', false);
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'GraphDetailsView Click',
      chartname: rest.chartId ? getChartName(rest.chartId.toString()) : '',
      fromurl: history.location.pathname ? history.location.pathname : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Chart MoreActions', spanAttribute);
    if (val == 5) {
      ReactGA.event({
        category: 'Click',
        action: 'Graph View Details',
        label: 'View Details',
        value: rest.chartId
      });
    }
    handleMenuClose();
    handleMenuClose();
    if (val != 5) {
      setActions(val, currentRate);
      localStorage.setItem('highlight', rest.chartId);
    } else {
      getChartlist(rest.chartId);
    }
  };

  function getChartlist(chartId) {
    const client = makeApolloClient;
    client
      .query({
        query: GET_GOAL_SETTINGS,
        variables: { chart_id: chartId, store_id: storeId }
      })
      .then(result => {
        if (result.data.dms_physical_rw_goal_settings) {
          setActions(6, result.data.dms_physical_rw_goal_settings);
        }
      });
  }

  const getChartInfo = chartId => {
    let title = '';
    if (global.localStorage) {
      try {
        let ls = JSON.parse(global.localStorage.getItem('chart-master')) || [];
        if (ls.length > 0) {
          ls.map(item => {
            if (chartId == item.chartId) {
              title = item.description;
            }
          });
        }
      } catch (e) {
        /*Ignore*/
      }
    }

    return title;
  };

  const getChartName = chartId => {
    let title = '';
    if (global.localStorage) {
      try {
        let ls = JSON.parse(global.localStorage.getItem('chart-master')) || [];
        if (ls.length > 0) {
          ls.map(item => {
            if (chartId == item.chartId) {
              title = item.chartName;
            }
          });
        }
      } catch (e) {
        /*Ignore*/
      }
    }

    return title;
  };

  const handleChangePanStatus = event => {
    enablePan(event.target.checked);
  };
  let searchText = history.location.search.split('?title=').pop();

  return (
    <>
      {/* {(rest.chartId == 1090 || rest.chartId == 1096) ? (
        <Tooltip
          title={
            panEnabled
              ? 'Disable Pan'
              : 'Enable Pan'
          }
          style={{ marginRight: -17, float: 'left', marginTop: 6 }}
        >
          <FormGroup>
            <FormControlLabel
              className={classes.switch}
              control={
                <Switch
                  checked={panEnabled}
                  onClick={handleChangePanStatus}
                  name="checkedA"
                  inputProps={{ 'aria-label': 'secondary checkbox' }}
                />
              }
              value="end"
              labelPlacement="end"
            />
          </FormGroup>
        </Tooltip>
      ) : (
        ''
      )} */}
      {/* <Tooltip
        title={'View Report'}
        style={{
          display: rest.currentRate && !searchText ? 'inline-block' : 'none'
        }}
        onClick={() => handleclick(6, rest.currentRate)}
      >
        <IconButton
          size="medium"
          classes={{
            root: classes.icon
          }}
        >
          <ReportIcon />
        </IconButton>
      </Tooltip> */}
      {type == 'popup' && (rest.chartId == 1090 || rest.chartId == 1096) && (
        <Tooltip
          className="resetPopupLayout"
          title="Reset Layout"
          style={{ display: searchText ? 'none' : 'inline-block' }}
          onClick={() => resetDashboard(true)}
        >
          <IconButton
            size="medium"
            classes={{
              root: classes.icon
            }}
          >
            <RestoreIcon color="primary" />
          </IconButton>
        </Tooltip>
      )}
      {type != 'popup' ? (
        <Tooltip
          title="Expand"

          style={{ display: searchText ? 'none' : 'inline-block' }}
          onClick={() => handlePopupClick()}
        >
          <IconButton
            size="medium"
            classes={{
              root: clsx(classes.icon, 'expand-charts')
            }}

          >
            <ZoomOutMapIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip
          title="Collapse"
          style={{ display: searchText ? 'none' : 'inline-block' }}
          onClick={() => handlePopupClose()}
        >
          <IconButton
            size="medium"
            classes={{
              root: clsx(classes.icon, 'collapse-charts')
            }}

          >
            <img src="/images/zoom-in.png" style={{ height: 20, width: 20 }} />
          </IconButton>
        </Tooltip>
      )}
      <HtmlTooltip
        title={
          <React.Fragment>
            <Typography color="inherit">
              {getChartInfo(rest.chartId)}
            </Typography>
          </React.Fragment>
        }
        style={{ display: searchText ? 'none' : 'inline-block' }}
      >
        <IconButton
          size="medium"
          id={'chart-details-' + rest.chartId}
          classes={{
            root: clsx(classes.icon, 'view-chart-info')
          }}

        >
          <InfoIcon />
        </IconButton>
      </HtmlTooltip>
      {/* <Tooltip
        title={getChartInfo(rest.chartId)}
        style={{ display: searchText ? 'none' : 'inline-block' }}
      >
        <IconButton size="medium">
          <InfoIcon />
        </IconButton>
      </Tooltip> */}
      <Tooltip
        title="View Details"
        style={{ display: searchText ? 'none' : 'inline-block' }}
        onClick={() => handleclick(4)}
      >
        <IconButton
          id={'view-details-' + rest.chartId}
          size="medium"
          classes={{
            root: clsx(classes.icon, 'view-chart-details')
          }}
        >
          <ExitToAppIcon />
        </IconButton>
      </Tooltip>
      {rest.favoritesDisabled &&
        rest.chartId &&
        rest.favoritesDisabled == true ? null : (
        <Tooltip
          title={
            session.favouriteCharts &&
              session.favouriteCharts.includes(rest.chartId.toString()) == false
              ? 'Add To Favorites'
              : 'Remove From Favorites'
          }
          style={{ display: searchText ? 'none' : 'inline-block' }}
          onClick={() => handleFavouriteClick()}
          className={session.favouriteCharts &&
            session.favouriteCharts.includes(rest.chartId.toString()) == false
            ? 'add-to-favorites'
            : 'remove-from-favorites'}
        >
          <IconButton
            id={
              session.favouriteCharts &&
                session.favouriteCharts.includes(rest.chartId.toString()) == false
                ? 'Add-to-Favorites'
                : 'Remove-from-Favorites'
            }
            size="medium"
            classes={{
              root: classes.icon
            }}
          >
            {session.favouriteCharts &&
              session.favouriteCharts.includes(rest.chartId.toString()) ==
              true ? (
              <StarIcon />
            ) : (
              <StarBorderIcon />
            )}
          </IconButton>
        </Tooltip>
      )}
      {/*   */}
      {type !== 'popup' &&
        (history.location.pathname == '/CPOverview' ||
          history.location.pathname == '/CPLaborOverview' ||
          history.location.pathname == '/CPPartsOverview' ||
          history.location.pathname == '/SpecialMetrics' ||
          (history.location.pathname == '/Discounts' &&
            rest.chartId != '1125' &&
            rest.chartId != '1124' &&
            rest.chartId != '1126' &&
            rest.chartId != '1114')) ? (
        <Tooltip title={<span>Return to Top</span>}>
          <IconButton
            size="medium"
            classes={{
              root: clsx(classes.icon, 'return-to-top')
            }}
            onClick={() => handleBackButton()}
          >
            <AssignmentReturnOutlinedIcon />
          </IconButton>
        </Tooltip>
      ) : (
        ''
      )}
      <Snackbar
        open={openSnackbar}
        autoHideDuration={6000}
        onClose={hidesnackbar}
      >
        <Alert onClose={hidesnackbar} severity="success">
          Added To Favorites
        </Alert>
      </Snackbar>

      <Snackbar
        open={openRemovedSnackbar}
        autoHideDuration={6000}
        onClose={hidesnackbar2}
      >
        <Alert onClose={hidesnackbar2} severity="error">
          Removed From Favorites
        </Alert>
      </Snackbar>
    </>
  );
}

MoreActions.propTypes = {
  className: PropTypes.string,
  setActions: PropTypes.func
};

export default memo(MoreActions);
