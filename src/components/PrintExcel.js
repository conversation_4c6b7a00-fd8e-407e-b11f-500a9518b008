import { text } from '@fortawesome/fontawesome-svg-core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { async } from 'validate.js';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

export const useExcelExport = () => {
  const addKpiRows = (key, kpiArray, hideGoals, isFrom) => {
    const row = [
      {
        'KPI Name': kpiArray.kpi_no + '   ' + kpiArray.kpi_name,
        fontSize: 6,
        color: '#fff',
        bold: true,
        fillColor: '#c65911'
        // styles
      },
      ...kpiArray.data.map(item => {
        let data = item.kpi_data.split('/');
        let goal = item.goal;
        let variance = item.variance;
        if (
          data.length > 0 &&
          (kpiArray.kpi_slno == 12 ||
            kpiArray.kpi_slno == 13 ||
            kpiArray.kpi_slno == 9)
        ) {
          data.splice(-1);
        }
        if (
          kpiArray.kpi_name != 'Parts To Labor Ratio' &&
          kpiArray.kpi_name != 'CP & Wty - Avg Age / Miles Per Vehicle'
        ) {
          data = data
            .map((ele, i) => {
              return formatCellValue(
                kpiArray.kpi_slno >= 31 &&
                  kpiArray.kpi_slno <= 33 &&
                  kpiArray.kpi_slno >= 34 &&
                  kpiArray.kpi_slno <= 37
                  ? 0
                  : ele,
                i,
                kpiArray.kpi_slno,
                ((kpiArray.kpi_slno == 1 ||
                  kpiArray.kpi_slno == 2 ||
                  kpiArray.kpi_slno == 11) &&
                  i == 2) ||
                  ((kpiArray.kpi_slno == 33 || kpiArray.kpi_slno == 37) &&
                    (i == 3 || i == 2))
                  ? 2
                  : 1
              );
            })
            .join(' * ');
        } else if (kpiArray.kpi_name == 'Parts To Labor Ratio') {
          data =
            '$' +
            parseFloat(data[0])
              // .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' to ' +
            '$' +
            parseInt(data[1])
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        } else if (
          kpiArray.kpi_name == 'CP & Wty - Avg Age / Miles Per Vehicle'
        ) {
          data =
            parseFloat(data[0])
              .toFixed(1)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' Years' +
            ' * ' +
            parseInt(data[1])
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' Miles';
        }
        var head;
        if (
          isFrom == 'KPIReportComparative' ||
          isFrom == 'KPIReportTechComparative'
        ) {
          head = item.adv_or_tech_name;
        } else {
          head = item.res_storename;
        }

        var keyval = {};

        var goalValue;
        if (goal != '' && goal != 0) {
          // if (
          //   kpiArray.kpi_slno != 9 &&
          //   kpiArray.kpi_slno != 11 &&
          //   kpiArray.kpi_slno != 33 &&
          //   kpiArray.kpi_slno != 37
          // ) {
          //   goalVal = goal + "%/" + variance + "%";
          // } else {
          //   goalVal = goal + "/" + variance;
          // }

          goalValue = goal + '%/' + variance + '%';
        } else {
          goalValue = '';
        }

        keyval[head] = data + '--' + goalValue;

        var goalval = {};
        var varval = {};
        var goalValue;
        var varValue;
        if (goal != '' && goal != 0) {
          // if (
          //   kpiArray.kpi_slno != 9 &&
          //   kpiArray.kpi_slno != 11 &&
          //   kpiArray.kpi_slno != 33 &&
          //   kpiArray.kpi_slno != 37
          // ) {
          //   goalVal = goal + "%/" + variance + "%";
          // } else {
          //   goalVal = goal + "/" + variance;
          // }
          if (
            kpiArray.kpi_slno == 9 ||
            kpiArray.kpi_slno == 11 ||
            kpiArray.kpi_slno == 33 ||
            kpiArray.kpi_slno == 37
          ) {
            goalValue = goal + ' //';
            varValue = variance + '     //';
          } else {
            goalValue = goal + '% //';
            varValue = variance + '%     //';
          }
        } else {
          goalValue = '';
          varValue = '';
        }
        if (
          isFrom == 'KPIReportComparative' ||
          isFrom == 'KPIReportTechComparative'
        ) {
          if (!hideGoals.includes(item.adv_or_tech_name)) {
            var user = item.adv_or_tech_name;
            var goalval = {};
            var varval = {};
            goalval[user + '-goal'] = goalValue;
            varval[user + '-var'] = varValue;
          }
        } else {
          if (!hideGoals.includes(item.res_storename)) {
            var user = item.res_storename;
            var goalval = {};
            var varval = {};
            goalval[user + '-goal'] = goalValue;
            varval[user + '-var'] = varValue;
          }
        }

        return [keyval, goalval, varval];
      })
    ];

    const outputArray = row.flatMap(item => {
      if (Array.isArray(item)) {
        return item.filter(innerItem => Object.keys(innerItem).length > 0);
      } else {
        return item;
      }
    });

    //result.push(row);
    return outputArray;
  };
  function rearrangeData(data, isFrom) {
    if (
      isFrom == 'KPIReportComparative' ||
      isFrom == 'KPIReportTechComparative'
    ) {
      return data[0].map(entry => {
        const {
          'KPI Name': kpiName,
          'Total Shop': totalShop,
          'Total Shop-goal': totalShopgoal,
          'Total Shop-var': totalShopvar,
          'Total Selected': totalSelected,

          ...rest
        } = entry;
        return {
          'KPI Name': kpiName,
          'Total Shop': totalShop,
          'Total Shop-goal': totalShopgoal,
          'Total Shop-var': totalShopvar,
          'Total Selected': totalSelected,
          ...rest
        };
      });
    } else {
      return data[0].map(entry => {
        const {
          'KPI Name': kpiName,
          'Group Roll-Up': grpTotal,
          'Group Average': grpAvg,

          ...rest
        } = entry;
        return {
          'KPI Name': kpiName,
          'Group Roll-Up': grpTotal,
          'Group Average': grpAvg,
          ...rest
        };
      });
    }
  }
  const formatCellValue = (val, opt, slno, dec) => {
    var formattedValue =
      val == 0 || val == null
        ? '0'
        : slno == 30
        ? val
        : dec == 1
        ? parseFloat(val)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : parseFloat(val)
            .toFixed(1)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    //console.log('formatCellValue>>>>>>>>>>', formattedValue, val, slno);
    if (
      ((slno == 1 ||
        slno == 2 ||
        slno == 12 ||
        slno == 13 ||
        slno == 32 ||
        slno == 35 ||
        slno == 31 ||
        slno == 34 ||
        slno == 33 ||
        slno == 37) &&
        opt == 2) ||
      (slno == 8 && opt == 1) ||
      ((slno == 21 || slno == 22 || slno == 26 || slno == 27) && opt == 1) ||
      slno == 19
    ) {
      formattedValue = formattedValue + '%';
    } else if (
      slno == 1 ||
      slno == 2 ||
      slno == 3 ||
      slno == 4 ||
      slno == 5 ||
      slno == 6 ||
      slno == 18 ||
      slno == 23 ||
      slno == 24 ||
      slno == 28 ||
      slno == 29 ||
      slno == 32 ||
      slno == 35 ||
      ((slno == 14 || slno == 15 || slno == 16 || slno == 17) &&
        (opt == 1 || opt == 2))
    ) {
      if (Number(formattedValue.replace(/,/g, '')) < 0) {
        formattedValue = '-$' + Math.abs(formattedValue);
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      formattedValue = formattedValue;
    }

    return formattedValue;
  };
  const getDocument = async (
    gridApi,
    filterStart,
    filterEnd,
    fileName,
    expandedKPIs,
    hideGoals,
    base64Image,
    allRowData,
    isFrom,
    selAdvisors,
    selStores,
    image,
    session,
    kpiSortedArray,
    storeNickName
  ) => {
    var allData = [];
    var excelData = [];
    var kpiDataAll = allRowData;

    kpiDataAll.map(node => {
      var hidKpis = session.hiddenKpis;

      var hidKpiArrSub = [];
      hidKpis.map(item => {
        if (
          (item.type == 'Pricing - Customer Pay' && item.kpi == 6) ||
          (item.type == 'Opportunities - CP Vehicles <u>Under</u> 60K Miles' &&
            item.kpi == 3) ||
          (item.type == 'Opportunities - CP Vehicles <u>Over</u> 60K Miles' &&
            item.kpi == 3)
        ) {
          var subArr = {
            type: item.type,
            kpi: '',
            si_no: item.si_no + 1
          };
          hidKpiArrSub.push(subArr);
        }
      });

      var hidKpiArr = hidKpis.filter(
        item => item.type == node.kpi_type && item.kpi == node.kpi_no
      );
      var hidKpiSubArr = hidKpiArrSub.filter(
        item => item.type == node.kpi_type && item.si_no == node.kpi_slno
      );

      if (
        typeof node.data != 'undefined' &&
        !expandedKPIs.includes(node.kpi_type) &&
        hidKpiArr.length == 0 &&
        hidKpiSubArr.length == 0
      ) {
        var data = node;

        let filteredData;
        if (
          isFrom == 'KPIReportComparative' ||
          isFrom == 'KPIReportTechComparative'
        ) {
          if (selAdvisors.length > 0) {
            filteredData = node.data.filter(item =>
              selAdvisors.includes(item.adv_or_tech_id)
            );
            //node.data.data = filteredData;
          } else {
            filteredData = node.data;
          }
        } else {
          if (selStores.length > 0) {
            filteredData = node.data.filter(item =>
              selStores.includes(item.store_id)
            );
            //node.data.data = filteredData;
          } else {
            filteredData = node.data;
          }
        }
        data.data = filteredData;

        excelData.push(data);
        //return node.data;
      }
    });
    if (
      isFrom == 'KPIReportComparative' ||
      isFrom == 'KPIReportTechComparative'
    ) {
      excelData.forEach(kpi => {
        kpi.data.forEach(item => {
          item.res_storename = item.adv_or_tech_name.replace(/\//g, '=='); // Replace / with a space or any other character
        });
      });
    } else {
      excelData.forEach(kpi => {
        kpi.data.forEach(item => {
          item.res_storename = item.res_storename.replace(/\//g, '=='); // Replace / with a space or any other character
        });
      });
    }

    hideGoals = hideGoals.map(name => name.replace(/\//g, '=='));

    let advOrTechNames;
    if (
      isFrom == 'KPIReportComparative' ||
      isFrom == 'KPIReportTechComparative'
    ) {
      advOrTechNames = excelData[0].data.map(item => {
        return item.adv_or_tech_name;
      });
    } else {
      advOrTechNames = excelData[0].data.map(item => {
        return item.res_storename;
      });
    }

    var totalRowCount = advOrTechNames.length * 3;

    advOrTechNames.unshift('KPI Name');
    // var goalData = [];
    // Object.keys(rowData[0].data).map(function(k) {
    //   var data;
    //   if (!hideGoals.includes(rowData[0].data[k].adv_or_tech_name)) {
    //     data = rowData[0].data[k].adv_or_tech_name;
    //     goalData.push(data);
    //   }
    // });

    // const elementToAdd = 'Goal / Var';

    // goalData.forEach(target => {
    //   let index = advOrTechNames.indexOf(target);
    //   if (index !== -1) {
    //     advOrTechNames.splice(index + 1, 0, elementToAdd);
    //   }
    // });

    const kpiData = excelData.reduce((acc, kpi) => {
      if (!acc[kpi.kpi_type]) {
        acc[kpi.kpi_type] = [];
      }
      acc[kpi.kpi_type].push(kpi);
      return acc;
    }, {});

    var kpiArr = [];
    for (const key in kpiData) {
      if (kpiData.hasOwnProperty(key)) {
        var firstRow = [];

        firstRow.push({
          'KPI Name':
            kpiData[key][0].kpi_type_code +
            '   ' +
            key
              .replace('<p>', ' ')
              .replace('</p>', ' ')
              .replace('<u>', ' ')
              .replace('</u>', ' ')

          //text: data.length > 0 ? data + '       Goal/Var' : '',
        });

        for (let i = 0; i < kpiData[key][0].data.length; i++) {
          if (typeof kpiData[key][0].data[i] != 'undefined') {
            var head;
            if (
              isFrom == 'KPIReportComparative' ||
              isFrom == 'KPIReportTechComparative'
            ) {
              head = kpiData[key][0].data[i].adv_or_tech_name;
            } else {
              head = kpiData[key][0].data[i].res_storename;
            }
            var keyval = {};
            keyval[head] = '';

            firstRow.push(keyval);
            if (
              isFrom == 'KPIReportComparative' ||
              isFrom == 'KPIReportTechComparative'
            ) {
              if (
                !hideGoals.includes(kpiData[key][0].data[i].adv_or_tech_name)
              ) {
                var goal = kpiData[key][0].data[i].adv_or_tech_name;
                var goalval = {};
                var varval = {};
                goalval[goal + '-goal'] = '';
                varval[goal + '-var'] = '';
                firstRow.push(goalval);
                firstRow.push(varval);
              }
            } else {
              if (!hideGoals.includes(kpiData[key][0].data[i].res_storename)) {
                var goal = kpiData[key][0].data[i].res_storename;
                var goalval = {};
                var varval = {};
                goalval[goal + '-goal'] = '';
                varval[goal + '-var'] = '';
                firstRow.push(goalval);
                firstRow.push(varval);
              }
            }
          }
        }

        kpiArr.push(firstRow);

        kpiData[key].map((element, i) => {
          var kpiVal = addKpiRows(key, element, hideGoals, isFrom);

          kpiArr.push(kpiVal.flat());
        });
      }
    }

    //  let elem = updateRows(kpiArr, isFrom);
    const formattedData = kpiArr.map(item => {
      const formattedItem = item.reduce((acc, curr) => {
        const [key, value] = Object.entries(curr)[0];
        acc[key] = value;
        return acc;
      }, {});

      return formattedItem;
    });
    const arrayToObject = advOrTechNames.reduce((obj, item) => {
      obj[item] = item; // You can set the value to null or any initial value you prefer

      if (item != 'KPI Name' && !hideGoals.includes(item)) {
        obj[item + '-goal'] = 'Goal';
        obj[item + '-var'] = 'Variance';
      }

      return obj;
    }, {});

    formattedData.unshift(arrayToObject);

    allData.push(formattedData);

    allData = rearrangeData(allData, isFrom);
    kpiSortedArray = kpiSortedArray.map(name => name.replace(/\//g, '=='));

    const sortedData = allData.map(item => {
      const sortedItem = { 'KPI Name': item['KPI Name'] };

      kpiSortedArray.forEach(key => {
        //if (item[key]) {
        sortedItem[key] = item[key];
        // }
      });
      return sortedItem;
    });
    console.log('worksheet==kpi', sortedData);
    const worksheet = XLSX.utils.json_to_sheet(sortedData);

    const json = XLSX.utils.sheet_to_json(worksheet);
    //console.log('excelData==', excelData);
    let title = '';
    if (isFrom === 'KPIReportComparative') {
      title = `KPI Advisor Comparative`;
    } else if (isFrom === 'KPIReportTechComparative') {
      title = `KPI Tech Comparative`;
    } else {
      title = `KPI Store Comparative`;
    }
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, title);

    const newWorkbook = new ExcelJS.Workbook();
    const newWorksheet = newWorkbook.addWorksheet(title);

    const imageId2 = newWorkbook.addImage({
      base64: image,
      extension: 'png'
    });

    const kpiTypes = json.filter(item => {
      if (
        typeof item['KPI Name'] === 'string' &&
        item['KPI Name'].includes('  ')
      ) {
        var cellVal = item['KPI Name'].split('  ');
        if (cellVal[0].length === 1 && cellVal[0].match(/[a-z]/i)) {
          return item;
        }
      }
    });

    const centerAlignment = {
      vertical: 'middle',
      horizontal: 'center'
    };

    const kpiNames = kpiTypes.map(item => item['KPI Name']);

    // newWorksheet.mergeCells('A1');
    newWorksheet.getCell('A1').value =
      isFrom == 'KPIReportComparative'
        ? 'KPI Advisor Comparative\n'
        : isFrom == 'KPIReportTechComparative'
        ? 'KPI Tech Comparative\n'
        : 'KPI Store Comparative\n';
    newWorksheet.getCell('A1').alignment = centerAlignment;

    newWorksheet.getCell('A2').value =
      isFrom == 'KPIReportComparative' || isFrom == 'KPIReportTechComparative'
        ? storeNickName
        : localStorage.getItem('storeGroup');
    newWorksheet.getCell('A2').alignment = centerAlignment;

    newWorksheet.getCell('A3').value =
      moment(filterStart).format('MM/DD/YY') +
      ' - ' +
      moment(filterEnd).format('MM/DD/YY');
    newWorksheet.getCell('A3').alignment = centerAlignment;
    newWorksheet.addImage(imageId2, {
      tl: { col: 1.5, row: 0.5 },
      ext: { width: 85, height: 60 }
    });
    var goalData = [];
    Object.keys(kpiDataAll[0].data).map(function(k) {
      var data;
      if (
        isFrom == 'KPIReportComparative' ||
        isFrom == 'KPIReportTechComparative'
      ) {
        if (!hideGoals.includes(kpiDataAll[0].data[k].adv_or_tech_name)) {
          data = kpiDataAll[0].data[k].adv_or_tech_name;
          goalData.push(data);
        }
      } else {
        if (!hideGoals.includes(kpiDataAll[0].data[k].res_storename)) {
          data = kpiDataAll[0].data[k].res_storename;
          goalData.push(data);
        }
      }
    });
    var goalLen = goalData.length * 2;
    const endColIndex = totalRowCount + advOrTechNames.length + goalLen;
    newWorksheet.mergeCells(1, 2, 3, endColIndex);
    //console.log('endColumnIdx', endColumnIdx, totalRowCount);
    //newWorksheet.mergeCells('B1', 'AH2');
    newWorksheet.getRow(1).height = 20;
    newWorksheet.getRow(2).height = 20;
    newWorksheet.getRow(3).height = 20;
    newWorksheet.getCell('B1').value = 'Fixed Ops Performance Center';

    newWorksheet.getCell('B1').alignment = {
      vertical: 'middle',
      horizontal: 'left',
      indent: 10
    };
    var parentRow = [];
    json.forEach((row, rowIndex) => {
      const excelRow = newWorksheet.addRow(Object.values(row));

      let newRowValues = [];

      if (rowIndex == 2) {
        const headers = [];
        newWorksheet.getRow(1).eachCell((cell, colNumber) => {
          // Split cell value by some delimiter, e.g., comma

          if (colNumber != 1) {
            headers.push({ header: cell.value, span: 5 });
          } else {
            headers.push({ header: cell.value, span: 1 });
          }
        });
        //console.log('headers==', headers);
        // Add headers with spans
        headers.forEach((header, index) => {
          newWorksheet.getCell(1, index + 1).value = header.header;
          //newWorksheet.mergeCells(1, index + 1, 1, index + header.span);
        });
      }

      excelRow.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        const cellValue = cell.value;

        if (typeof cellValue === 'string' && cellValue.includes('*')) {
          const splitValues = cellValue.split('--')[0].split('*');
          if (splitValues.length < 4) {
            var len = 4 - splitValues.length;
            for (var i = 0; i < len; i++) {
              splitValues.push('');
            }
          }

          newRowValues = newRowValues.concat(splitValues);
        } else if (
          typeof cellValue === 'string' &&
          !cellValue.includes('  ') &&
          !cellValue.includes(' //') &&
          cellValue.includes('/')
        ) {
          let splitValues = [];
          if (typeof cellValue === 'string' && cellValue.includes('--')) {
            splitValues.push(cellValue.split('--')[0]);
            if (splitValues.length < 4) {
              var len = 4 - splitValues.length;
              for (var i = 0; i < len; i++) {
                splitValues.push('');
              }
            }

            newRowValues = newRowValues.concat(splitValues);
          } else {
            newRowValues = newRowValues.concat(cellValue);
          }
        } else {
          if (
            typeof cellValue === 'string' &&
            !cellValue.includes('  ') &&
            cellValue != ''
          ) {
            let splitValues = [];
            if (
              cellValue != 'KPI Name' &&
              cellValue != 'Goal' &&
              cellValue != 'Variance' &&
              !cellValue.includes(' //') &&
              !cellValue.includes('     //')
            ) {
              var title = cellValue.split('--')[0];
              if (title.includes('==')) {
                title = title.replace('==', '/');
              } else {
                title = title;
              }
              splitValues.push(title);
              if (splitValues.length < 4) {
                var len = 4 - splitValues.length;
                for (var i = 0; i < len; i++) {
                  splitValues.push('');
                }
              }
              //console.log('cellValue===ppp', cellValue);
              newRowValues = newRowValues.concat(splitValues);
            } else if (cellValue.includes(' //')) {
              splitValues.push(cellValue.split(' //')[0]);
              newRowValues = newRowValues.concat(splitValues);
            } else {
              newRowValues.push(cellValue);
            }
          } else {
            if (cellValue.includes('     //')) {
              let splitValues = [];

              splitValues.push(cellValue.split('     //')[0] + '    ');
              newRowValues = newRowValues.concat(splitValues);
            } else {
              var cellData = cellValue.split('  ');
              if (cellData[0].length === 1 && cellData[0].match(/[a-z]/i)) {
                let splitValues = [];
                splitValues.push(cellValue);

                var len = splitValues.length;
                for (var i = 0; i < totalRowCount; i++) {
                  splitValues.push('');
                }

                newRowValues = newRowValues.concat(splitValues);
              } else {
                newRowValues.push(cellValue);
              }
            }
          }
        }
      });

      var newRows = newWorksheet.addRow(newRowValues);

      newWorksheet.columns.forEach(function(column, i) {
        let maxLength = 0;
        if (i == 0) {
          column['eachCell']({ includeEmpty: true }, function(cell) {
            var columnLength = cell.value ? cell.value.toString().length : 6;
            if (columnLength > maxLength) {
              maxLength = columnLength;
            }
          });
          column.width = maxLength < 10 ? 6 : maxLength;
        } else {
          column.width = 12;
        }
      });

      newRows.eachCell((cell, colNumber) => {
        if (colNumber == 1) {
          cell.font = {
            size: 12,
            // bold: true,
            color: { argb: 'FFFFFF' }
          }; // Bold for the header row
          // cell.alignment = { vertical: 'middle', horizontal: 'center' };
          // cell.border = {
          //   top: { style: 'thin' },
          //   left: { style: 'thin' },
          //   bottom: { style: 'thin' },
          //   right: { style: 'thin' }
          // };

          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'c65911' } // Yellow background for header
          };
        }

        if (rowIndex == 0) {
          cell.font = {
            size: 12,
            bold: true,
            color: { argb: 'FFFFFF' }
          }; // Bold for the header row
          // cell.alignment = { vertical: 'middle', horizontal: 'center' };
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };

          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'c65911' } // Yellow background for header
          };
        }
        if (colNumber != 1 && cell.value.indexOf('    ') >= 0) {
          var kpiname = row['KPI Name'];
          var value = cell.value.split('    ')[0].split('%')[0];
          var color =
            value > -10.0 &&
            value < 0.0 &&
            !kpiname.includes('1 Line Count / % Over 60K') &&
            !kpiname.includes('1 Line Count / % Under 60K') &&
            !kpiname.includes(
              'Repair Price Targets / Misses / % of Non-Compliance'
            ) &&
            !kpiname.includes(
              'Parts Price Targets / Misses / % of Non-Compliance'
            )
              ? //? '895129'
                'ff0000'
              : value <= -10.0 &&
                !kpiname.includes('1 Line Count / % Over 60K') &&
                !kpiname.includes('1 Line Count / % Under 60K') &&
                !kpiname.includes(
                  'Repair Price Targets / Misses / % of Non-Compliance'
                ) &&
                !kpiname.includes(
                  'Parts Price Targets / Misses / % of Non-Compliance'
                )
              ? 'ff0000'
              : value > 0.0 &&
                (kpiname.includes('1 Line Count / % Over 60K') ||
                  kpiname.includes('1 Line Count / % Under 60K') ||
                  kpiname.includes(
                    'Repair Price Targets / Misses / % of Non-Compliance'
                  ) ||
                  kpiname.includes(
                    'Parts Price Targets / Misses / % of Non-Compliance'
                  ))
              ? 'ff0000'
              : value == 0
              ? '000000'
              : //? 'FFFFFF'
                '008000';
          cell.font = {
            size: 12,
            bold: true,
            color: { argb: color }
          };
        }
      });
    });

    newWorksheet.eachRow(function(row, rowNumber) {
      if (rowNumber > 4) {
        newWorksheet.spliceRows(rowNumber - 1, 1);
      }
      if (rowNumber <= 4) {
        row.eachCell((cell, colNumber) => {
          cell.font = {
            size: 12,
            bold: true,
            color: { argb: 'FFFFFF' }
          }; // Bold for the header row

          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'c65911' } // Yellow background for header
          };
        });
      }

      if (kpiNames.includes(row.values[1])) {
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          // newWorksheet.spliceColumns(colNumber, 2);
          cell.font = {
            size: 12,
            bold: true,
            color: { argb: '0000000' }
          }; // Bold for the header row

          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'ffc000' } // Yellow background for header
          };
        });
      }
      //Do whatever you want to do with this row like inserting in db, etc
    });

    newWorksheet.getCell('B1').font = {
      color: { argb: 'FFFFFF' },
      size: 20,

      bold: true
    };
    const buffer = await newWorkbook.xlsx.writeBuffer();
    const fileType =
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    const fileExtension = '.xlsx';

    const blob = new Blob([buffer], { type: fileType });

    saveAs(blob, fileName + fileExtension);
    //gridApi.api.setFilterModel(null);
    // gridApi.api.redrawRows();
  };

  const exportToExcel = (
    gridApi,
    filterStart,
    filterEnd,
    fileName,
    expandedKPIs,
    hideGoals,
    base64Image,
    allRowData,
    isFrom,
    selAdvisors,
    selStores,
    image,
    session,
    kpiSortedArray,
    storeNickName
  ) => {
    console.log('excel', selAdvisors, selStores);
    getDocument(
      gridApi,
      filterStart,
      filterEnd,
      fileName,
      expandedKPIs,
      hideGoals,
      base64Image,
      allRowData,
      isFrom,
      selAdvisors,
      selStores,
      image,
      session,
      kpiSortedArray,
      storeNickName
    );
  };
  return {
    exportToExcel
  };
};
