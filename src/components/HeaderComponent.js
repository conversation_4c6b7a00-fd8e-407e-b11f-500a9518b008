import React, { Component } from 'react';
import { makeStyles } from '@material-ui/styles';

import { Grid } from '@material-ui/core';
const useStyles = makeStyles(theme => ({
  item: {
    display: 'block',
    paddingTop: 0,
    paddingBottom: 0
  },
  redDot: {
    height: '10px',
    width: '10px',
    backgroundColor: '#00b33c',
    borderRadius: '50%',
    display: 'inline-block',
    marginRight: 10
  },
  blueDot: {
    height: '10px',
    width: '10px',
    backgroundColor: '#e60000',
    borderRadius: '50%',
    display: 'inline-block',
    marginRight: 10
  }
}));
const HeaderComponent = () => {
  const classes = useStyles();
  return (
    <Grid style={{ width: 200 }}>
      <>
        <span className={classes.redDot}></span>
        <span style={{ marginBottom: 5 }}>Highest</span>{' '}
        <label style={{ float: 'right', paddingTop: 5 }}>Trend</label> <br />
        <span className={classes.blueDot}></span>Lowest
      </>
    </Grid>
  );
};
export default HeaderComponent;
