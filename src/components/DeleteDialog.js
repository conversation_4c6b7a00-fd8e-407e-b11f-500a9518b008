import React from 'react';
import {
  Dialog,
  DialogActions,
  DialogContent,
  Button,
  Typography,
  Tooltip,
  IconButton
} from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/Delete';
const DeleteDialog = props => {
  const [open, setOpen] = React.useState(false);
  const { confirmDialog, matrixOrGridName, isDefaultType, parent } = props;

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    setOpen(false);
    confirmDialog(matrixOrGridName, isDefaultType);
  };

  return (
    <>
      <Tooltip title={'Delete'}>
        <IconButton size="small" color="secondary" onClick={handleClickOpen}>
          <DeleteIcon />
        </IconButton>
      </Tooltip>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogContent>
          <Typography variant="h6" style={{ textTransform: 'none' }}>
            Are you sure you want to delete {matrixOrGridName}?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={onConfirm} autoFocus color="primary">
            Ok
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};
export default DeleteDialog;
