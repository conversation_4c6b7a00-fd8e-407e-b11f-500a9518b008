import moment from 'moment';
import {
  getChartLegendsforHasuraCharts,
  getColorScheme,
  getHasuraChartMeasures,
  getYAxisLabelforHasuraCharts,
  isDiscountComparisonApexChart,
  isMonthComparisonChart,
  getTooltipForDuplicateDashboards,
  getYAxisLabelforPostgraphileCharts,
  getPostgraphileChartMeasures,
  getChartLegendsforPostgraphileCharts
} from 'src/utils/Utils';
import React, { Component } from 'react';
import { Chart } from 'chart.js';
import { Tooltip } from '@material-ui/core';
export function getDataforBarChart(resultSet, chartId, specialColor) {
  const data = {
    labels: resultSet.categories().map(c => c.category),
    datasets: resultSet.series().map((s, index) => ({
      label: getDataLabel(s.title, chartId),
      data: s.series.map(r => r.value),
      borderColor:
        specialColor == undefined
          ? getColorSchemeDetailChart(chartId)[index]
          : specialColor,
      backgroundColor:
        specialColor == undefined
          ? getColorSchemeDetailChart(chartId)[index]
          : specialColor,
      fill: false,
      type: 'bar',
      borderWidth: 1.5
    }))
  };
  return data;
}
export function getDataforHasuraBarChart(resultSet, chartId, specialColor) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(item => item.chartId == chartId);
  const data = {
    labels: getYAxisLabelforHasuraCharts(chartId, resultSet),
    datasets: getHasuraChartMeasures(chartId).map((measures, index) => ({
      label: getChartLegendsforHasuraCharts(chartId)[index],
      data: resultSet.map(r => r[measures]),
      borderColor:
        chartId == 1316
          ? '#dc3912'
          : chartId == 1317
          ? '#993399'
          : filteredResult[0].dbdName.includes('Special Metrics') == true &&
            chartId != 923 &&
            chartId != 1355
          ? getColorScheme(getLineChartType(chartId))[index]
          : chartId == 923 || chartId == 1355
          ? getColorScheme(11)[index]
          : filteredResult[0].dbdName.includes('Special Metrics') == false
          ? getColorSchemeDetailChart(chartId)[index]
          : chartId == 1359 ||
            chartId == 1360 ||
            chartId == 1361 ||
            chartId == 1362
          ? getColorSchemeDetailChart(chartId)[index]
          : specialColor,
      backgroundColor:
        chartId == 1316
          ? '#dc3912'
          : chartId == 1317
          ? '#993399'
          : filteredResult[0].dbdName.includes('Special Metrics') == true &&
            chartId != 923 &&
            chartId != 1355
          ? getColorScheme(getLineChartType(chartId))[index]
          : chartId == 923 || chartId == 1355
          ? getColorScheme(11)[index]
          : filteredResult[0].dbdName.includes('Special Metrics') == false
          ? getColorSchemeDetailChart(chartId)[index]
          : specialColor,
      fill: false,
      type: 'bar',
      borderWidth: 1.5
    }))
  };
  return data;
}

export function getDataforPostgraphileCharts(resultSet, chartId, specialColor) {
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(item => item.chartId == chartId);
  let jsonData = resultSet;
  let labelToDataMap = [];
  jsonData[0] &&
    jsonData[0].labels &&
    jsonData[0].labels.forEach((label, index) => {
      const numericKey = index;
      labelToDataMap[numericKey] = {};
      jsonData[0].datasets.forEach(dataset => {
        labelToDataMap[numericKey][dataset.chartId] = dataset.data[index];
        labelToDataMap[numericKey][dataset.label] = dataset.label;
      });
    });

  const data = {
    labels: getYAxisLabelforPostgraphileCharts(chartId, resultSet),
    datasets: getPostgraphileChartMeasures(chartId).map((measures, index) => ({
      label: getChartLegendsforPostgraphileCharts(
        resultSet[0] && resultSet[0].datasets,
        chartId
      ),
      data:
        labelToDataMap.length > 0
          ? labelToDataMap.map(item => item[chartId])
          : [],
      borderColor:
        chartId == 1316
          ? '#dc3912'
          : chartId == 1317
          ? '#993399'
          : filteredResult[0].dbdName.includes('Special Metrics') == true &&
            chartId != 923 &&
            chartId != 1355 &&
            chartId != 1359 &&
            chartId != 1360 &&
            chartId != 1361 &&
            chartId != 1362
          ? getColorScheme(getLineChartType(chartId))[index]
          : chartId == 923 || chartId == 1355
          ? getColorScheme(11)[index]
          : (filteredResult[0].dbdName.includes('Special Metrics') == true &&
              (chartId == 1359 ||
                chartId == 1360 ||
                chartId == 1361 ||
                chartId == 1362)) ||
            filteredResult[0].dbdName.includes('Special Metrics') == false
          ? getColorSchemeDetailChart(chartId)[index]
          : specialColor,
      backgroundColor:
        chartId == 1316
          ? '#dc3912'
          : chartId == 1317
          ? '#993399'
          : filteredResult[0].dbdName.includes('Special Metrics') == true &&
            chartId != 923 &&
            chartId != 1355 &&
            chartId != 1359 &&
            chartId != 1360 &&
            chartId != 1361 &&
            chartId != 1362
          ? getColorScheme(getLineChartType(chartId))[index]
          : chartId == 923 || chartId == 1355
          ? getColorScheme(11)[index]
          : (filteredResult[0].dbdName.includes('Special Metrics') == true &&
              (chartId == 1359 ||
                chartId == 1360 ||
                chartId == 1361 ||
                chartId == 1362)) ||
            filteredResult[0].dbdName.includes('Special Metrics') == false
          ? getColorSchemeDetailChart(chartId)[index]
          : specialColor,
      fill: false,
      type: 'bar',
      borderWidth: 1.5
    }))
  };
  return data;
}

function getDataLabel(value, chartId) {
  return value;
}
const dateFormatter = item => moment(item).format('MMM');
const dateFormatterWithYear = item => moment(item).format('MMM YY');
const dateFormatterWithYearLabel = item =>
  moment(item).format('MMM') + "'" + moment(item).format('YY');

export function getChartName(chartId) {
  if (chartId != 0) {
    var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
    let filteredResult = chartList.filter(item => item.chartId == chartId);
    return (filteredResult[0].chartName.indexOf('CP') > -1 ||
      filteredResult[0].dbdName.indexOf('Workmix') > -1 ||
      filteredResult[0].dbdName.indexOf('Service Advisor Perfomance') > -1 ||
      filteredResult[0].dbdName.indexOf('Service Advisor-Month Comparison') >
        -1 ||
      filteredResult[0].dbdName.indexOf('Service Advisor-Opcategory By Month') >
        -1 ||
      filteredResult[0].dbdName.indexOf('Technician Efficiency') > -1 ||
      filteredResult[0].dbdName.indexOf('AddOns-Month Comparison') > -1) &&
      isMonthComparisonChart(filteredResult[0].chartId) == false &&
      isDiscountComparisonApexChart(filteredResult[0].chartId) == false &&
      filteredResult[0].dbdName.indexOf('AddOns-Month Comparison') == -1
      ? filteredResult[0].chartName
      : isMonthComparisonChart(filteredResult[0].chartId) ||
        isDiscountComparisonApexChart(filteredResult[0].chartId) ||
        filteredResult[0].dbdName.indexOf('AddOns-Month Comparison') > -1
      ? filteredResult[0].chartName.replace('CP', '')
      : filteredResult[0].chartName.indexOf('Penetration') > -1 ||
        chartId == 935 ||
        chartId == 1357 ||
        chartId == 1239
      ? filteredResult[0].chartName
      : filteredResult[0].chartName;
    //: 'CP ' + filteredResult[0].chartName;
  }
}
export function getBarChartConfiguration(
  chartId,
  yAxisRanges,
  isStacked,
  realm,
  parentId,
  isFrom,
  resultSet,
  isAdvisorChart
) {
  let offsetNeeded = false;
  let dataLabelArr = [];
  let pathName = window.location.pathname;
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(item => item.chartId == chartId);
  const options = {
    // maintainAspectRatio: chartId == 1357 ? false : true,
    maintainAspectRatio:
      (filteredResult[0].dbdName.includes('Special Metrics') &&
        chartId != 1359 &&
        chartId != 1360 &&
        chartId != 1361 &&
        chartId != 1362) ||
      chartId == 921 ||
      chartId == 931 ||
      chartId == 926
        ? false
        : true,

    plugins: {
      datalabels: { display: false }
    },
    tooltips: {
      backgroundColor: '#ddeaf4',
      titleFontColor: '#003d6b',
      bodyFontColor: '#003d6b',
      borderColor: '#003d6b',
      borderWidth: 1,
      mode: 'index',
      axis: 'y',
      yPadding: chartId == 931 ? 1 : 1,
      // yAlign: chartType == 8 ?( chartId ==931 ? 'bottom' : 'center') : 'top',
      // yAlign: chartType == 8 ? 'center' : 'top',
      position: 'nearest',
      titleFontColor: '#003d6b',
      bodyFontColor: '#003d6b',
      borderColor: '#003d6b',
      callbacks: {
        label: function(tooltipItem, data) {
          return getTootipLabel(chartId, tooltipItem, data, resultSet);
        },
        title: function(tooltipItem, data) {
          return dateFormatterWithYear(tooltipItem[0].label);
        }
      }
    },
    legend: {
      position: 'bottom',
      align: 'center',
      labels: {
        boxWidth: 12
      }
    },

    // plugins: {
    //   datalabels: {
    //     anchor: chartId == 931 || chartId == 1355 ? 'end' : '',
    //     align: chartId == 931 || chartId == 1355 ? 'end' : '',

    //     color: chartId == 931 || chartId == 1355 ? '#000000' : '',
    //     font: {
    //       weight: chartId == 931 || chartId == 1355 ? 'bold' : '',
    //       size: chartId == 931 || chartId == 1355 ? 13 : ''
    //     },

    //     borderWidth:function(ctx){

    //       console.log("123",ctx.datasetIndex)
    //       return 1},
    //     borderColor: '#a3a375',
    //     offset: ctx => {
    //       let arr = [];
    //       if (chartId == 931 || chartId == 1355) {
    //         let datasets = ctx.chart.data.datasets.filter((ds, datasetIndex) =>
    //           ctx.chart.isDatasetVisible(datasetIndex)
    //         );
    //         if (datasets.length ==2) {
    //           ctx.chart.config.data.datasets.map(dataPoint =>
    //             arr.push(dataPoint.data[ctx.dataIndex])
    //           );
    //         } else {
    //           datasets.map(dataset => {
    //             arr.push(dataset.data[ctx.dataIndex]);
    //           });
    //         }

    //         if (datasets.length < 2) return '0.5';
    //         else return '0.5';
    //       }
    //     },

    //     formatter: (value, ctx) => {
    //       var ci = ctx.chart;

    //       if (chartId == 931 || chartId == 1355) {
    //         let datasets = ctx.chart.data.datasets.filter((ds, datasetIndex) =>
    //           ctx.chart.isDatasetVisible(datasetIndex)
    //         );
    //         if (
    //           ctx.datasetIndex === datasets.length - 1 || datasets.length !=2  )
    //          {
    //           let sum = 0;
    //           datasets.map(dataset => {
    //             sum += Math.round(dataset.data[ctx.dataIndex]);
    //           });
    //           return   Math.round(sum).toLocaleString()+'%';
    //         } else {
    //           return '';
    //         }
    //       }
    //     }
    //   }
    // },
    hover: {
      animationDuration: 0
    },
    animation: {
      onComplete: function() {
        let type;
        if (chartId == 931 || chartId == 926) {
          var chartInstance = this.chart;

          var horizontal =
            chartInstance.config.type.lastIndexOf('horizontal', 0) === 0; //if type of graph starts with horizontal

          var ctx = chartInstance.ctx;
          ctx.save();
          ctx.globalCompositeOperation = 'destination-over';
          ctx.textAlign = horizontal ? 'left' : 'center';
          ctx.textBaseline = horizontal ? 'middle' : 'bottom';
          ctx.font = ' bold  13px  sans-serif';
          ctx.fillStyle = '#000';
          ctx.fontFamily = "'Helvetica Neue', 'Helvetica', 'Arial', sans-serif";

          var dataLabels = {}; //key = x or y coordinate (depends on grouping), value = Array[0] other coordinate , Array[1] = value to print
          var equationForGrouping = horizontal ? Math.max : Math.min; //equation to use for grouping
          let datasets = chartInstance.data;
          var sum = [];
          //get values from chart, fill them in dataLabels (as seen in https://github.com/chartjs/Chart.js/blob/master/samples/data_label_combo-bar-line.html )
          Chart.helpers.each(
            chartInstance.data.datasets.forEach(function(dataset, i) {
              var meta = chartInstance.controller.getDatasetMeta(i);
              Chart.helpers.each(
                meta.data.forEach(function(bar, index) {
                  //for each part of each stacked bar
                  if (meta.hidden != true) {
                    //if data is not hidden (by clicking on it in the legend)
                    var groupByCoordinate = horizontal
                      ? bar._model.y
                      : bar._model.x;
                    var otherCoordinate = horizontal
                      ? bar._model.x
                      : bar._model.y;

                    if (dataLabels[groupByCoordinate]) {
                      dataLabels[groupByCoordinate][0] = equationForGrouping(
                        otherCoordinate,
                        dataLabels[groupByCoordinate][0]
                      );
                      if (dataLabels[groupByCoordinate][1] < 0) {
                        dataLabels[groupByCoordinate][1] = [];
                      }
                      if (Number(dataset.data[index]) > 0) {
                        dataLabels[groupByCoordinate][1] =
                          Number(dataLabels[groupByCoordinate][1]) +
                          Number(dataset.data[index]);
                      }
                    }
                    // console.log(
                    //   'dlabels',
                    //   otherCoordinate,
                    //   dataset.data[index]
                    // );
                    else if (Number(dataset.data[index]) > 0) {
                      dataLabels[groupByCoordinate] = [
                        otherCoordinate,
                        dataset.data[index]
                      ];
                    }
                    //  sum.push(dataset.data[index]);
                  }
                }),
                this
              );
            }),
            this
          );

          var b = sum.splice(0, 13);
          //draw values onto graph
          for (var key in dataLabels) {
            // var total = data + this.data.datasets[1].data[index];
            if (horizontal) {
              ctx.fillText(
                Math.round(dataLabels[key][1]).toLocaleString(),
                Math.round(dataLabels[key][0]).toLocaleString(),
                key
              );
            } else {
              ctx.fillText(
                '$' + Math.round(dataLabels[key][1]).toLocaleString(),
                key,
                Math.round(dataLabels[key][0]).toLocaleString()
              );
            }
          }
          ctx.restore();
        }
      }
    },
    scales: {
      yAxes: [
        {
          display: true,
          ticks: {
            beginAtZero: true,
            display: true
          },
          gridLines: {
            display: true
          },
          ticks: {
            callback: function(value, index, values) {
              return getYAxisLabel(chartId, value);
            }
          }
        }
      ],
      xAxes: [
        {
          display: true,
          ticks: {
            reverse: chartId == 1357 ? true : false,
            beginAtZero: true,
            autoSkip: false,
            maxRotation: 45,
            minRotation: 45,
            callback: function(value, index, values) {
              return dateFormatterWithYearLabel(value);
            }
          }
        }
      ]
    }
  };

  options.scales.yAxes[0].ticks.min = getMinValue(chartId, yAxisRanges);

  options.scales.yAxes[0].ticks.stepSize = getStepSize(chartId, yAxisRanges);

  if (
    parentId == 942 ||
    parentId == 939 ||
    parentId == 920 ||
    parentId == 944 ||
    parentId == 1133 ||
    parentId == 1049 ||
    (parentId == 952 && realm == 'haleyag')
  ) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 10;
  }

  if (
    chartId == 1079 ||
    chartId == 1083 ||
    chartId == 935 ||
    chartId == 1357 ||
    chartId == 931 ||
    chartId == 926 ||
    chartId == 1239
  ) {
    options.scales.yAxes[0].stacked = true;
    options.scales.xAxes[0].stacked = true;
  }
  if (chartId == 931 || chartId == 926 || chartId == 921) {
    options.scales.yAxes[0].ticks.min = 0;
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }
  if (chartId == 938 || chartId == 930) {
    options.scales.yAxes[0].ticks.maxTicksLimit = 5;
  }
  if (resultSet && resultSet.length > 0 && (chartId == 931 || chartId == 926)) {
    var updatedMinMaxArray = getMinAndMaxValues(
      resultSet,
      chartId,
      yAxisRanges,
      realm
    );
    let min = updatedMinMaxArray[0] * 100;
    let roundedMin = Math.ceil(min / 5) * 5;

    let displayedMin = roundedMin / 100;
    let max = updatedMinMaxArray[1] * 100;
    let roundedMax = Math.floor(max / 5) * 5;

    let displayedMax = roundedMax / 100;

    options.scales.yAxes[0].ticks.min = 0;
    if (displayedMax) {
      options.scales.yAxes[0].ticks.max = displayedMax;
    }
  }
  if (
    chartId == 931 &&
    resultSet.every(
      e =>
        e.lbrgrossOpportunity == null &&
        e.lbrjointOpportunity == null &&
        e.lbrvolumeOpportunity == null
    )
  ) {
    options.scales.yAxes[0].ticks.min = 0;
    options.scales.yAxes[0].ticks.max = 100;
  }
  if (
    chartId == 926 &&
    resultSet.every(
      e =>
        e.prtgrossOpportunity == null &&
        e.prtjointOpportunity == null &&
        e.prtvolumeOpportunity == null
    )
  ) {
    options.scales.yAxes[0].ticks.min = 0;
    options.scales.yAxes[0].ticks.max = 100;
  }
  return options;
}

function getMinAndMaxValues(resultSet, chartId, yAxisRanges, realm) {
  let arrayResult = resultSet;
  var arraydata = [];

  var filteredArray = arrayResult.filter(function(el) {
    return el != null;
  });

  for (let index = 0; index < arrayResult.length; index++) {
    const o = arrayResult[index];
    if (chartId == 931) {
      arraydata.push(
        Number(o.lbrgrossOpportunity) > -1 ? Number(o.lbrgrossOpportunity) : 0,
        Number(o.lbrjointOpportunity) > -1 ? Number(o.lbrjointOpportunity) : 0,
        Number(o.lbrvolumeOpportunity) > -1 ? Number(o.lbrvolumeOpportunity) : 0
      );
    }
    if (chartId == 926) {
      arraydata.push(
        Number(o.prtgrossOpportunity) > -1 ? Number(o.prtgrossOpportunity) : 0,
        Number(o.prtjointOpportunity) > -1 ? Number(o.prtjointOpportunity) : 0,
        Number(o.prtvolumeOpportunity) > -1 ? Number(o.prtvolumeOpportunity) : 0
      );
    }
  }
  var groups = [];

  for (var i = 0; i < arraydata.length; i += 3) {
    groups.push(arraydata.slice(i, i + 3));
  }
  const result = groups.map(subArr => {
    return subArr.reduce((pre, item) => pre + item, 0);
  });

  const isAllZero = filteredArray.every(item => item === '0.00');

  var maxValue, increasedmaxValue, updatedMaxValue, divisor, updatedMinValue;
  if (chartId == 931 || chartId == 926) {
    maxValue = Math.round(Math.max.apply(null, result));

    increasedmaxValue = (maxValue * 10) / 100 + maxValue;

    if (Math.trunc(increasedmaxValue).toString().length == 3) divisor = 100;
    if (Math.trunc(increasedmaxValue).toString().length == 4) divisor = 1000;
    if (Math.trunc(increasedmaxValue).toString().length == 5) divisor = 10000;
    if (Math.trunc(increasedmaxValue).toString().length == 6) divisor = 50000;
    if (yAxisRanges == undefined && divisor) {
      updatedMinValue = 0;
      updatedMaxValue = Math.ceil(increasedmaxValue / divisor) * divisor;
    }
  }
  if (isAllZero) {
    updatedMinValue = 0;
    updatedMaxValue = 100;
  }
  var arr = [updatedMinValue, updatedMaxValue];

  return arr;
}
function getYAxisLabel(chartId, value) {
  if (
    chartId == 1068 ||
    chartId == 941 ||
    chartId == 1069 ||
    chartId == 1070 ||
    chartId == 1071 ||
    chartId == 937 ||
    chartId == 1066 ||
    chartId == 987 ||
    chartId == 988 ||
    chartId == 986 ||
    chartId == 1007 ||
    chartId == 1010 ||
    chartId == 1008 ||
    chartId == 1009 ||
    chartId == 1001 ||
    chartId == 1002 ||
    chartId == 457 ||
    chartId == 456 ||
    chartId == 1031 ||
    chartId == 1028 ||
    chartId == 1030 ||
    chartId == 1029 ||
    chartId == 1015 ||
    chartId == 1014 ||
    chartId == 1012 ||
    chartId == 1013 ||
    chartId == 1084 ||
    chartId == 1085 ||
    chartId == 1086 ||
    chartId == 1087 ||
    chartId == 1088 ||
    chartId == 1075 ||
    chartId == 1020 ||
    chartId == 1018 ||
    chartId == 1019 ||
    chartId == 1022 ||
    chartId == 1128 ||
    chartId == 1129 ||
    chartId == 1130 ||
    chartId == 1131 ||
    chartId == 1132 ||
    chartId == 1152 ||
    chartId == 1153 ||
    chartId == 1158 ||
    chartId == 1159 ||
    chartId == 1160 ||
    chartId == 1161 ||
    chartId == 1168 ||
    chartId == 1172 ||
    chartId == 1171 ||
    chartId == 1176 ||
    chartId == 1177 ||
    chartId == 1178 ||
    chartId == 1179 ||
    chartId == 1180 ||
    chartId == 1181 ||
    chartId == 1182 ||
    chartId == 1183 ||
    chartId == 1184 ||
    chartId == 1185 ||
    chartId == 1186 ||
    chartId == 1187 ||
    chartId == 1188 ||
    chartId == 1189 ||
    chartId == 1190 ||
    chartId == 1191 ||
    chartId == 1192 ||
    chartId == 1193 ||
    chartId == 1194 ||
    chartId == 1195 ||
    chartId == 1196 ||
    chartId == 1211 ||
    chartId == 1212 ||
    chartId == 1213 ||
    chartId == 1214 ||
    chartId == 1215 ||
    chartId == 1216 ||
    chartId == 1217 ||
    chartId == 1218 ||
    chartId == 1219 ||
    chartId == 1220 ||
    chartId == 1221 ||
    chartId == 1222 ||
    chartId == 1223 ||
    chartId == 1224 ||
    chartId == 1240 ||
    chartId == 1241 ||
    chartId == 1242 ||
    chartId == 931 ||
    chartId == 921 ||
    chartId == 926 ||
    chartId == 1359 ||
    chartId == 1360 ||
    chartId == 1361 ||
    chartId == 1362
  ) {
    return ' $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  if (chartId == 1067) {
    return Number.isInteger(value)
      ? '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : ' $' + Math.round(value);
  }
  if (chartId == 1070 || chartId == 1071) {
    return Number.isInteger(value)
      ? '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : ' $' + Math.round(value);
  }
  if (
    chartId == 968 ||
    chartId == 969 ||
    chartId == 970 ||
    chartId == 971 ||
    chartId == 1074 ||
    chartId == 1033 ||
    chartId == 1034 ||
    chartId == 1035 ||
    chartId == 753 ||
    chartId == 1080 ||
    chartId == 1081 ||
    chartId == 1082 ||
    chartId == 998 ||
    chartId == 1091 ||
    chartId == 1092 ||
    chartId == 1093 ||
    chartId == 1094 ||
    chartId == 1134 ||
    chartId == 1135 ||
    chartId == 1136 ||
    chartId == 1137 ||
    chartId == 1139 ||
    chartId == 1140 ||
    chartId == 1141 ||
    chartId == 1142 ||
    chartId == 1144 ||
    chartId == 1145 ||
    chartId == 1147 ||
    chartId == 1146 ||
    chartId == 1204 ||
    chartId == 1205 ||
    chartId == 1206 ||
    chartId == 1207 ||
    chartId == 1208 ||
    chartId == 1209 ||
    chartId == 1210 ||
    chartId == 1327 ||
    chartId == 1328 ||
    chartId == 1329 ||
    chartId == 1330 ||
    chartId == 1331 ||
    chartId == 1332 ||
    chartId == 1333
  ) {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  if (
    chartId == 1072 ||
    chartId == 967 ||
    chartId == 1097 ||
    chartId == 1073 ||
    chartId == 1079 ||
    chartId == 1083
  ) {
    return Math.round(value * 100) + ' %';
  }
  if (chartId == 935) {
    return Number.isInteger(value * 100)
      ? value * 100 + ' %'
      : parseFloat((value * 100).toFixed(2)) + ' %';
    //return ((value * 100) < 1 && (value * 100) !=0 ) ? (value * 100).toFixed(2) + ' %' :  (value * 100) + ' %';
  }
  if (chartId == 939) {
    return Number.isInteger(value)
      ? '$' + value
      : '$' + parseFloat(value.toFixed(2));
    //return ((value * 100) < 1 && (value * 100) !=0 ) ? (value * 100).toFixed(2) + ' %' :  (value * 100) + ' %';
  }
  if (
    chartId == 923 ||
    chartId == 938 ||
    chartId == 1316 ||
    chartId == 1317 ||
    chartId == 1355
  ) {
    return value + ' %';
  }
  if (chartId == 949 || chartId == 1076 || chartId == 1077) {
    // return Math.round((value / 100) * 100);
    return (value / 100) * 100;
  }
  if (chartId == 1078) {
    // return Math.round((value / 100) * 100);
    return ((value / 100) * 100).toFixed(2);
  } else return value;
}

function getMinValue(chartId, yAxisRanges) {
  return yAxisRanges == undefined ? null : yAxisRanges[0];
}

function getStepSize(chartId, yAxisRanges) {
  return yAxisRanges == undefined ? null : yAxisRanges[1];
}

function getTootipLabel(chartId, tooltipItem, data, resultSet) {
  if (tooltipItem.yLabel > 0) {
    var label = data.datasets[tooltipItem.datasetIndex].label || '';
    var label = label + ' ';
  }
  // var opporLabel = '';
  // if (chartId == 931) {
  //  const ds = resultSet[tooltipItem.index];
  //  if(label == 'Hours Per RO') {
  //   opporLabel = ds.lbrvolumeOpportunity;
  //  } else if(label == 'Gross Profit%') {
  //   opporLabel = ds.lbrgrossOpportunity;
  //  } else if(label == 'Joint Effect') {
  //   opporLabel = ds.lbrjointOpportunity;
  //  }

  // }
  if (isNaN(tooltipItem.yLabel)) {
    tooltipItem.yLabel = 0;
  }
  switch (chartId) {
    case 1068:
    case 1067:
    case 941:
    case 1069:
    case 1070:
    case 1071:
    case 937:
    case 1066:
    case 987:
    case 988:
    case 986:
    case 1007:
    case 1010:
    case 1008:
    case 1009:
    case 1001:
    case 1002:
    case 457:
    case 456:
    case 1031:
    case 1028:
    case 1030:
    case 1029:
    case 1015:
    case 1014:
    case 1012:
    case 1013:
    case 1084:
    case 1085:
    case 1086:
    case 1087:
    case 1088:
    case 1075:
    case 1020:
    case 1018:
    case 1019:
    case 1022:
    case 1128:
    case 1129:
    case 1130:
    case 1131:
    case 1132:
    case 1152:
    case 1153:
    case 1158:
    case 1159:
    case 1160:
    case 1161:
    case 1168:
    case 1172:
    case 1171:
    case 1175:
    case 1176:
    case 1177:
    case 1178:
    case 1179:
    case 1180:
    case 1181:
    case 1182:
    case 1183:
    case 1184:
    case 1185:
    case 1186:
    case 1187:
    case 1188:
    case 1189:
    case 1190:
    case 1191:
    case 1192:
    case 1193:
    case 1194:
    case 1195:
    case 1196:
    case 1211:
    case 1212:
    case 1213:
    case 1214:
    case 1215:
    case 1216:
    case 1217:
    case 1218:
    case 1219:
    case 1220:
    case 1221:
    case 1222:
    case 1223:
    case 1224:
    case 1240:
    case 1241:
    case 1242:
    case 1359:
    case 1360:
    case 1361:
    case 1362:
      Math.sign(tooltipItem.yLabel) > -1
        ? (label +=
            '$' +
            tooltipItem.yLabel
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ','))
        : (label +=
            '-$' +
            Math.abs(tooltipItem.yLabel)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ','));
      break;

    case 1072:
    case 967:
    case 1097:
      if (label) {
        label += ': ';
      }
      label += (tooltipItem.yLabel * 100).toFixed(1) + '%';
      break;
    case 1073:
    case 1079:
      if (label) {
        label += ': ';
      }
      label += (tooltipItem.yLabel * 100).toFixed(2);
      break;
    case 1083:
    case 1316:
    case 1317:
      if (label) {
        label += ': ';
      }
      label += (tooltipItem.yLabel * 100).toFixed(2) + '%';
      break;
    case 935:
      if (label) {
        label += ': ';
      }
      label += (tooltipItem.yLabel * 100).toFixed(2) + '%';
      break;
    case 923:
    case 1355:
      if (label) {
        label += ': ';
      }
      label += tooltipItem.yLabel + '%';
      break;

    case 968:
    case 969:
    case 970:
    case 971:
    case 1074:
    case 1033:
    case 1034:
    case 1035:
    case 753:
    case 1080:
    case 1081:
    case 1082:
    case 998:
    case 1134:
    case 1135:
    case 1136:
    case 1137:
    case 1148:
    case 1149:
    case 1166:
      if (label) {
        label += ': ';
      }
      label += tooltipItem.yLabel
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      break;
    case 1327:
    case 1328:
    case 1329:
    case 1330:
    case 1331:
    case 1332:
    case 1333:
    case 1144:
    case 1145:
    case 1147:
    case 1146:
    case 1139:
    case 1140:
    case 1141:
    case 1142:
    case 1204:
    case 1205:
    case 1206:
    case 1207:
    case 1208:
    case 1209:
    case 1210:
      if (label) {
        label += ': ';
      }
      label += tooltipItem.yLabel
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      break;
    case 1091:
    case 1092:
    case 1093:
    case 1094:
    case 1225:
    case 1226:
    case 1227:
    case 1228:
    case 1229:
    case 1230:
    case 1231:
      if (label) {
        label += ': ';
      }
      label += tooltipItem.yLabel.toFixed(4);
      break;
    case 949:
    case 1076:
    case 1077:
    case 1078:
      if (label) {
        label += ': ';
      }
      label += ((tooltipItem.yLabel / 100) * 100).toFixed(2);
      break;
    default:
      if (label) {
        label += ': ';
      }
      if (tooltipItem.yLabel > 0) {
        label += tooltipItem.yLabel;
      }
      break;
  }
  return label;
}
export function getColorSchemeDetailChart(chartId) {
  let COLORS_SERIES;
  switch (chartId) {
    case 1068: //combined
    case 1069:
    case 1072:
    case 968:
    case 1176:
    case 1225:
    case 1190:
    case 1007:
    case 1134:
    case 1183:
    case 1197:
    case 1139:
    case 1204:
    case 1211:
    case 1001:
    case 1028:
    case 1218:
    case 1144:
    case 1319:
    case 1327:
    case 1359:
      COLORS_SERIES = ['#109618'];
      break;
    case 970: //internal
    case 1179:
    case 1228:
    case 1193:
    case 1009:
    case 1137:
    case 1186:
    case 1200:
    case 1142:
    case 1207:
    case 1214:
    case 456:
    case 1030:
    case 1221:
    case 1147:
    case 1322:
    case 1330:
    case 1362:
      COLORS_SERIES = ['#993399'];
      break;
    case 1148:
    case 1180: //Maint
    case 1229:
    case 1194:
    case 1152:
    case 1154:
    case 1187:
    case 1201:
    case 1156:
    case 1208:
    case 1215:
    case 1158:
    case 1160:
    case 1222:
    case 1162:
    case 1323:
    case 1331:
      COLORS_SERIES = ['#e67300'];
      break;
    case 1149:
    case 1181: //E.service contract
    case 1230:
    case 1195:
    case 1153:
    case 1155:
    case 1188:
    case 1202:
    case 1157:
    case 1209:
    case 1216:
    case 1159:
    case 1161:
    case 1223:
    case 1163:
    case 1324:
    case 1332:
      COLORS_SERIES = ['#5c5c8a'];
      break;
    case 1166:
    case 1182: //Factory
    case 1231:
    case 1196:
    case 1168:
    case 1169:
    case 1189:
    case 1203:
    case 1173:
    case 1210:
    case 1217:
    case 1172:
    case 1171:
    case 1224:
    case 1170:
    case 1325:
    case 1333:
      COLORS_SERIES = ['#cc0044'];
      break;
    case 967:
    case 941:
    case 1070:
    case 969:
    case 937:
    case 1066:
    case 987:
    case 988:
    case 986:
    case 1074:
    case 1033:
    case 1034:
    case 1035:

    case 1128:
    case 1129:
    case 1130:
    case 1131:
    case 1132:
    case 1141:
    case 1146:
    case 1321:

    case 1150:
    case 1151:
    case 1167:
    case 1329:
    case 1178:
    case 1179:
    case 1008:
    case 1206:
    case 1185:
    case 1136:
    case 1192:
    case 1213:
    case 1220: //warr
    case 1199:
    case 1227:
    case 457:
    case 1029:
    case 1361:
      COLORS_SERIES = ['#3366CC'];
      break;
    case 1067:
    case 1135:

    case 1013: //customer
    case 1071:
    case 971:
    case 1177:
    case 1226:
    case 1191:
    case 1010:
    case 1184:
    case 1198:
    case 1140:
    case 1205:
    case 1212:
    case 1002:
    case 1031:
    case 1219:
    case 1145:
    case 1320:
    case 1328:
    case 1097:
    case 1360:
      COLORS_SERIES = ['#DC3912'];
      break;
    case 1075:
    case 1076:
    case 753:

    case 1012:
    case 1084:
    case 1091:
    case 1240:
    case 1241:
    case 1242:
    case 1359:
      COLORS_SERIES = ['#FF9900'];
      break;
    case 1020:
    case 1073:
    case 949:
    case 1080:
    case 1015:
    case 1085:
    case 1092:
      COLORS_SERIES = ['#DD4477'];
      break;
    case 1019:
    case 1077:
    case 1081:
    case 1014:
    case 1086:
    case 1093:
      COLORS_SERIES = ['#0099C6'];
      break;
    case 1018:
    case 1078:
    case 1082:
    case 1087:
    case 1094:
      COLORS_SERIES = ['#109618'];
      break;
    case 1022:
    case 1088:
    case 998:
      COLORS_SERIES = ['#22AA99'];
      break;
    case 1079:
      COLORS_SERIES = ['#FF9900', '#109618', '#3366CC', '#DD4477'];
      break;
    case 1083:
      COLORS_SERIES = ['#109618 ', '#DD4477', '#0099C6', '#22AA99'];
      break;
    case 921:
      COLORS_SERIES = [
        'rgb(102, 46, 155)',
        'rgb(248, 102, 36)',
        'rgb(249, 200, 14)',
        '#009933'
      ];
      break;
    case 931:
    case 926:
      COLORS_SERIES = ['#ffcc00', 'rgb(248, 102, 36)', '#3366CC'];
      break;
  }
  return COLORS_SERIES;
}

export function GetyAxisRange(chartId) {
  switch (chartId) {
    case 1068:
    case 941:
    case 1069:
    case 1075:
    case 1007:
    case 1001:
    case 939:
      return [0, 50000];
    case 1067:
    case 1070:
    case 1020:
    case 1010:
    case 1008:
    case 1002:
    case 457:
    case 1028:
      return [0, 20000];
    case 1071:
    case 1009:
    case 456:
    case 1031:
    case 1029:
      return [0, 10000];
    case 968:
    case 1074:
    case 1022:
    case 753:
    case 925:
    case 1100:
    case 1134:
    case 1138:
    case 1139:
    case 1143:
    case 1144:
      return [0, 500];
    case 971:
    case 969:
    case 970:
    case 1033:
    case 957:
    case 1135:
    case 1136:
    case 1137:
    case 1140:
    case 1145:
      return [0, 200];
    case 937:
    case 1066:
    case 986:
    case 998:
    case 1014:
    case 1084:
    case 1087:
    case 1128:
    case 1131:
    case 1132:
    case 1232:
    case 1113:
      return [0, 20];
    case 987:
    case 1086:
    case 1088:
    case 923:
    case 1102:
    case 1130:
    case 1355:
    case 1115:
    case 1235:
    case 1357:
      return [0, 10];
    case 988:
    case 1015:
    case 1012:
    case 1013:
    case 1085:
    case 946:
    case 1099:
    case 1116:
    case 1127:
    case 1129:
    case 948:
    case 1236:
    case 1354:
    case 1237:
      return [0, 50];
    case 1034:
    case 1035:
    case 1355:
    case 1141:
    case 1142:
    case 1146:
    case 1147:
    case 966:
    case 1165:
      return [0, 100];
    case 1072:
    case 967:
    case 1097:
    case 1073:
    case 1077:
    case 1078:
    case 935:
    case 930:
    case 1174:
      return [0, 0.2];
    case 1019:
    case 1018:
    case 1030:
    case 1175:
    case 1111:
    case 1234:
      return [0, 5000];
    case 949:
    case 1076:
    case 1079:
    case 1083:
    case 1091:
    case 1092:
    case 1093:
    case 1094:
    case 936:
      return [0, 0.5];

    case 1080:
    case 1081:
    case 1082:
      return [0, 100];
    case 940:
      return [0, 0.25, 1];
    case 920:
      //if (isFrom == 'retail') {
      return [0, 250];
    // }
    case 1238:
      return [0, 0.1];
    case 938:
      return [0, 0.02];
    case 942:
      return [0, 75000, null, 300000];
    case 1101:
      return [0, 1, 3];
    // case 1111:
    // case 1234:
    //   return [0, 2000];
    case 1123:
    case 1148:
      return [0, 15];
    case 1233:
    case 1164:
      return [0, 5];
  }
}

export function getChartDrillDown(chartId, searchText, isFrom) {
  switch (chartId) {
    case 1068:
    case 941:
    case 1067:
    case 968:
    case 971:
    case 969:
    case 970:
    case 1002:
    case 1018:
    case 1019:
    case 1020:
    case 1022:
    case 1075:
    case 971:
    case 969:
    case 970:
    case 968:
    case 942:
    case 920:
    case 925:
    case 960:
    case 1049:
    case 938:
    case 1133:
    case 1134:
    case 1135:
    case 1136:
    case 1137:
    case 1138:
    case 1143:
    case 1148:
    case 1149:
    case 1150:
    case 1151:
    case 1154:
    case 1155:
    case 1156:
    case 1157:
    case 1166:
    case 1167:
    case 1169:
    case 1173:
    case 1326:
    case 1334:
      return 1;
    case 1069:
    case 1070:
    case 1071:
    case 1010:
    case 1008:
    case 1009:
    case 1007:
    case 944:
    case 952:
    case 939:
    case 1152:
    case 1153:
    case 1168:
      return 3;
    case 1072:
    case 967:
    case 1097:
    case 1073:
    case 1073:
    case 966:
    case 940:
      return 4;
    case 457:
    case 456:
    case 1001:
    case 1158:
    case 1159:
    case 1172:
      return 10;
    case 1029:
    case 1030:
    case 1028:
    case 1031:
    case 1160:
    case 1161:
    case 1171:
      return 11;
    case 1012:
    case 1162:
    case 1163:
    case 1170:
      return 12;
    case 956:
      return 7;
    case 988:
    case 987:
    case 986:
    case 1066:
    case 937:
    case 1098:
    case 1356:
    case 946:
    case 1128:
    case 1129:
    case 1130:
    case 1131:
    case 1132:
      return 2;
    case 1080:
    case 1081:
    case 1082:
    case 998:
    case 753:
    case 918:
    case 1083:
      return 6;
    case 1084:
    case 1085:
    case 1086:
    case 1087:
    case 1088:
    case 955:
    case 953:
      return 5;
    case 1076:
    case 1077:
    case 1078:
    case 949:
    case 1044:
    case 1079:
    case 1318:
    case 1319:
    case 1320:
    case 1321:
    case 1322:
    case 1323:
    case 1324:
    case 1325:
      return 0;
    case 1092:
    case 1093:
    case 1094:
    case 1091:
      // case 916:
      return 9;
    case 1074:
    case 1033:
    case 1034:
    case 1035:
    case 1139:
    case 1140:
    case 1144:
    case 1145:
    case 1146:
    case 1147:
    case 1142:
    case 1141:
    case 1327:
    case 1328:
    case 1329:
    case 1330:
    case 1331:
    case 1332:
    case 1333:
      if (searchText == 'CPPRC') {
        return 12;
      } else {
        return 1;
      }
    case 946:
      return 1;
    case 1098:
    case 1356:
      return 2;
    case 1013:
    case 1014:
    case 1015:
      return 23;
    case 1099:
    case 1100:
    case 1101:
    case 1102:
    case 1103:
    case 1104:
    case 1105:
    case 1106:
    case 1107:
    case 1108:
    case 1109:
    case 1110:
    case 1116:
    case 1117:
      return 25;
    case 1118:
    case 1119:
    case 1120:
    case 1121:
    case 1122:
      return 26;

    case 1113:
    case 1234:
    //case 1115:
    case 1123:
    case 1124:
    case 1236:
    case 1237:
      return 30;
    case 1114:
      return 31;
    case 1125:
    case 1126:
      return 32;
    case 948:
    case 923:
    case 1358:
    case 1354:
    case 1355:
      return 18;
    case 935:
    case 1357:
      return 19;
    case 930:
      return 20;
    case 936:
      return 21;
    case 766:
    case 957:
      return 22;
    case 1127:
      return 2;

    case 1235:
      return 36;
    case 1174:
    case 1175:
      return 37;

    case 1233:
      return 40;

    case 1176:
    case 1177:
    case 1178:
    case 1179:
    case 1180:
    case 1181:
    case 1182:
    case 1183:
    case 1184:
    case 1185:
    case 1186:
    case 1187:
    case 1188:
    case 1189:
      return 38;
    case 1190:
    case 1191:
    case 1192:
    case 1193:
    case 1194:
    case 1195:
    case 1196:
    case 1197:
    case 1198:
    case 1199:
    case 1200:
    case 1201:
    case 1202:
    case 1203:
    case 1319:
    case 1320:
    case 1321:
    case 1322:
    case 1323:
    case 1324:
    case 1325:
    case 1211:
    case 1212:
    case 1213:
    case 1214:
    case 1215:
    case 1216:
    case 1217:
    case 1218:
    case 1219:
    case 1220:
    case 1221:
    case 1222:
    case 1223:
    case 1224:

    case 1204:
    case 1205:
    case 1206:
    case 1207:
    case 1208:
    case 1209:
    case 1210:
      // case 1238:
      return 39;
    case 1111:
      return 41;
    case 1115:
      return 42;
    case 1164:
      return 43;
    case 1165:
      return 44;
    case 1232:
      return 45;
    case 1239:
    case 1240:
    case 1241:
    case 1242:
    case 1360:
    case 1359:
    case 1361:
    case 1362:
      return 46;
    case 1316:
    case 1317:
      return 47;
    case 1238:
    case 1225:
    case 1226:
    case 1227:
    case 1228:
    case 1229:
    case 1230:
    case 1231:
    case 916:
      return 48;
  }
}

export function getChartDataIndex(chartId, dataIndex, isFrom, label) {
  switch (chartId) {
    case 1068:
    case 1069:
    case 1072:
      return 2;
    //Combined (labor parts)
    case 941:
    case 1070:
    case 967:
    case 971:
    case 1033:
    case 1073:
    case 1033:
    case 1010:
    case 960:
    case 944:
    case 1073:
    case 1044:
    case 1318:
    case 918:
    case 956:
    case 955:
    case 938:
    case 930:
    case 766:
    case 1135:
    case 1140:
    case 1145:
    case 1175:
    case 1177:
    case 1184:
    case 1191:
    case 1198:
    case 1212:
    case 1205:
    case 1219:
    case 1226:
    case 1238:
    // case 1239:
    case 1241:
    case 1316:
    case 1317:
      return 0;

    //Labor //Customer pay
    case 1239:
    case 1360:
    case 1361:
    case 1362:
    case 1359:
      return label == 'Combined'
        ? 5
        : label == 'Warranty'
        ? 1
        : label == 'Internal'
        ? 2
        : 0;

    case 1067:
    case 1071:
    case 1097:
    case 1002:
    case 1031:
    case 1033:
    case 1049:
    case 952:
    case 966:
    case 953:
    case 916:
    case 957:
    case 1145:
    case 1328:
    case 1334:
      return 1;
    //Parts //Customer pay
    case 969:
    case 1034:
    case 457:
    case 1029:
    case 1034:
    case 1008:
    case 1136:
    case 1141:
    case 1146:
    case 1178:
    case 1185:
    case 1192:
    case 1199:
    case 1213:
    case 1206:
    case 1220:
    case 1227:
    case 1329:
      return 3;
    //Warranty
    case 970:
    case 1035:
    case 456:
    case 1030:
    case 1035:
    case 1009:
    case 1137:
    case 1142:
    case 1147:
    case 1179:
    case 1186:
    case 1193:
    case 1200:
    case 1214:
    case 1207:
    case 1221:
    case 1228:
    case 1242:
    case 1330:
      return 4;
    //Internal
    case 968:
    case 1074:
    case 1001:
    case 1028:
    case 1074:
    case 1001:
    case 1028:
    case 1007:
    case 1134:
    case 1139:
    case 1144:
    case 1176:
    case 1183:
    case 1190:
    case 1197:
    case 1319:
    case 1211:
    case 1204:
    case 1218:
    case 1225:
    case 1240:
    case 1327:
      return 5;
    //Warranty - Internal And Customer Pay
    case 988:
    case 1015:
    case 1092:
    case 1020:
    case 1080:
    case 1085:
    case 1076:
    case 1129:
      return 6;
    //repair
    case 987:
    case 1014:
    case 1093:
    case 1019:
    case 1081:
    case 1086:
    case 1077:
    case 1130:
    case 1148:
    case 1150:
    case 1152:
    case 1154:
    case 1156:
    case 1158:
    case 1160:
    case 1162:
    case 1180:
    case 1187:
    case 1194:
    case 1201:
    case 1215:
    case 1208:
    case 1222:
    case 1229:
    case 1331:
      return 7;
    //Maintenance
    case 986:
    case 1013:
    case 1094:
    case 1018:
    case 1082:
    case 1087:
    case 1078:
    case 1131:
      return 8;
    //Competitive
    case 1066:
    case 1098:
    case 1132:
    case 1356:
      return 9;
    //Repair And Competitive
    case 937:
    case 1012:
    case 1091:
    case 949:
    case 1128:
      return 10;
    //Repair Maintenance And Competitive
    case 1022:
    case 998:
    case 1088:
      return 11;
    //shop supplies
    case 1075:
    case 753:
    case 1084:
      return 12;
    case 942:
    case 939:
    case 948:
    case 1358:
    case 923:
    case 1354:
    case 1355:
      return dataIndex;
    case 940:
      return dataIndex;
    case 920:
      return dataIndex;
    case 946:
      return dataIndex;
    case 925:
      return dataIndex;
    case 1138:
      return 0;
    case 1143:
    case 1326:
      return 1;
    case 1111:
    case 1234:
      return label == 'Labor Discount' ? 0 : label == 'Parts Discount' ? 1 : 2;
    case 1113:
      return label == 'RO' ? 2 : label == 'Line' ? 3 : 4;

    case 1114:
      return 16;
    case 936:
      return label == 'Competitive' || label == 'Competetive'
        ? 8
        : label == 'Repair'
        ? 6
        : 7;
    case 1083:
      return 17;
    case 1133:
    case 1127:
      return 0;
    case 1079:
      return label == 'Competitive%' ? 8 : label == 'Repair%' ? 6 : 7;
    //  return 18;
    case 1235:
    case 1115:
      return label == 'Labor Sale %' ? 0 : 1;
    case 1236:
    case 1233:
    case 1237:
      return label == 'Labor Discount' ? 0 : label == 'Parts Discount' ? 1 : 2;
    case 1149:
    case 1151:
    case 1153:
    case 1155:
    case 1157:
    case 1159:
    case 1161:
    case 1163:
    case 1181:
    case 1188:
    case 1195:
    case 1202:
    case 1216:
    case 1209:
    case 1223:
    case 1230:
    case 1332:
      return 19;
    //Extended Service
    case 935:
      return label == 'Customer Pay'
        ? 0
        : label == 'Extended Service'
        ? 19
        : label == 'Internal'
        ? 4
        : label == 'Maintenance Plan'
        ? 7
        : label == 'Factory Service Contract'
        ? 20
        : 3;
    case 1357:
      return label == 'Customer Pay'
        ? 0
        : label == 'Extended Service'
        ? 19
        : label == 'Internal'
        ? 4
        : label == 'Maintenance' || label == 'Maintenance Plan'
        ? 7
        : label == 'Factory Service Contract'
        ? 20
        : 3;
    case 1164:
    case 1165:
      return label == 'Labor Discount' ? 0 : 1;
    case 1166:
    case 1167:
    case 1168:
    case 1169:
    case 1171:
    case 1172:
    case 1173:
    case 1170:
    case 1182:
    case 1189:
    case 1196:
    case 1203:
    case 1217:
    case 1210:
    case 1224:
    case 1231:
    case 1333:
      return 20;
    //Factory Extended service
    case 1100:
      return label == 'Non-Add On Job Count' ? 0 : 1;
  }
}

export function getChartPayType(chartId) {
  switch (chartId) {
    case 969:
    case 1034:
    case 457:
    case 1029:
    case 1008:
    case 969:
    case 1034:
    case 1136:
    case 1141:
    case 1146:
    case 1227:
      return 'W';
    case 970:
    case 1035:
    case 456:
    case 1030:
    case 1009:
    case 970:
    case 1035:
    case 1137:
    case 1142:
    case 1147:
    case 1242:
    case 1228:
      return 'I';
    case 1240:
      return 'C,I';
    // case 968:
    // case 1001:
    // case 1028:
    // case 1074:
    // case 1134:
    // case 1139:
    // case 1144:
    //   //return '';
    //   return 'C,W,I,M,E';
    case 1148:
    case 1150:
    case 1152:
    case 1154:
    case 1156:
    case 1158:
    case 1160:
    case 1162:
    case 1229:
      return 'M';
    case 1149:
    case 1151:
    case 1153:
    case 1155:
    case 1157:
    case 1159:
    case 1161:
    case 1163:
    case 1230:
      return 'E';
    case 1166:
    case 1167:
    case 1168:
    case 1169:
    case 1171:
    case 1172:
    case 1173:
    case 1170:
    case 1231:
      return 'F';
  }
}
export function getChartOpCategory(chartId) {
  switch (chartId) {
    case 988:
    case 1015:
    case 1092:
    case 1020:
    case 1080:
    case 1085:
    case 1076:
    case 1129:
      return 'R';
    case 986:
    case 1013:
    case 1094:
    case 1018:
    case 1082:
    case 1087:
    case 1078:
    case 1131:
      return 'C';
    case 987:
    case 1014:
    case 1093:
    case 1019:
    case 1081:
    case 1086:
    case 1077:
    case 1130:
      return 'M';
    case 1066:
    case 1132:
      return 'R,C';
    case 1098:
      return 'R';
    case 1356:
      return 'M,C';
    case 1022:
    case 998:
    case 1088:
      return 'S';
    case 937:
    case 1012:
    case 1091:
    case 1128:
      return 'R,C,M';
    case 1075:
    case 1084:
      return 'R,C,M,S';
  }
}
// export function getChartText(chartId, isFrom) {
//   switch (chartId) {
//     case 942:
//       return 'CPR';
//     case 939:
//       return 'CPG';
//     case 940:
//       return 'CPGP';
//     case 960:
//       return 'CPLR';
//     case 944:
//       return 'CPLG';
//     case 1073:
//       return 'CPLGP';
//     case 920:
//       return 'CPLSH';
//     case 946:
//       return 'CPELR';
//     case 1044:
//       return 'LHRO';
//     case 1098:
//       return 'LCPERC';
//     case 925:
//       return 'LRO';
//     case 1143:
//       return 'CPPRC';
//     case 1138:
//       return 'CPRO';
//     case 918:
//       return 'JCAC';
//     case 956:
//       return 'CPTH';
//     case 955:
//       return 'ALSRO';
//     case 1049:
//       return 'CPPR';
//     case 952:
//       return 'CPPG';
//     case 966:
//       return 'CPPGP';
//     case 953:
//       return 'CPPRO';
//     case 916:
//       return 'CPPM';
//     case '918':
//       return 'CPPM';
//     case 948:
//       return 'SJRC';
//     case 923:
//       return 'SJRCP';
//     case 938:
//       return 'CPRR';
//     case 935:
//       return 'SHPT';
//     case 930:
//       return 'CPPLR';
//     case 936:
//       return 'CPPLRC';
//     case 766:
//       return 'DSFH';
//     case 957:
//       return 'DTFH';

//     case 1105:
//       return '1105';
//     case 1104:
//       return '1104';
//     case 1103:
//       return '1103';
//     case 1102:
//       return '1102';
//     case 1101:
//       return '1101';
//     case 1100:
//       return '1100';
//     case 1099:
//       return '1099';
//     case 1116:
//       return '1116';
//     case 1117:
//       return '1117';

//     case 1114:
//       return '1114';

//     // case 957:
//     //   return "LPDT";
//     // case 957:
//     //   return "ROCD";
//     // case 957:
//     //   return "DJRO";

//     case 1111:
//       return 'LPD';
//     case 1113:
//       return 'DRODL';

//     case 1110:
//       return '1110';
//     case 1109:
//       return '1109';
//     case 1108:
//       return '1108';
//     case 1107:
//       return '1107';
//     case 1106:
//       return '1106';
//     case 1105:
//       return '1105';

//     case 1104:
//       return '1104';
//     case 1103:
//       return '1103';
//     case 1102:
//       return '1102';
//     case 1101:
//       return '1101';
//     case 1100:
//       return '1100';
//     case 1099:
//       return '1099';
//     case 1116:
//       return '1116';
//     case 1117:
//       return '1117';
//     case 1118:
//       return '1118';
//     case 1119:
//       return '1119';
//     case 1120:
//       return '1120';
//     case 1121:
//       return '1121';
//     case 1122:
//       return '1122';
//     case 1114:
//       return '1114';
//     case 948:
//       return 'SJRC';
//     case 923:
//       return 'SJRCP';
//     case 938:
//       return 'CPRR';
//     case 935:
//       return 'SHPT';
//     case 930:
//       return 'CPPLR';
//     case 936:
//       return 'CPPLRC';
//     case 766:
//       return 'DSFH';
//     case 957:
//       return 'DTFH';
//     case 1133:
//       return 'LCPSH';
//     case 1127:
//       return 'LCPER';
//     case 1123:
//       return '1123';
//     case 1124:
//       return '1124';fCP
//     case 1125:
//       return '1125';
//     case 1126:
//       return '1126';
//     case 1115:
//       return '1115';
//     case 1164:
//       return '1164';
//     case 1165:
//       return '1165';
//     case 1174:
//       return '1174';
//     case 1175:
//       return '1175';
//     case 1232:
//       return '1232';
//     case 1233:
//       return '1233';
//     case 1234:
//       return '1234';
//     case 1235:
//       return '1235';
//     case 1236:
//       return '1236';
//     case 1237:
//       return '1237';
//     case 1238:
//       return '1238';
//     case 1239:
//       return "1239";
//     case 1276:
//       return '1276';
//     case 1277:
//       return '1277';
//     case 1278:
//       return '1278';
//     case 1279:
//       return '1279';
//     case 1280:
//       return '1280';
//     case 1281:
//       return "1281";
//     case 1282:
//       return "1282";
//     case 1283:
//       return "1283";
//     case 1284:
//       return '1284';
//     case 1285:
//       return '1285';
//     case 1286:
//       return '1286';
//     case 1287:
//       return '1287';
//     case 1288:
//       return "1288";
//     case 1289:
//       return "1289";
//     case 1290:
//       return "1290";
//     case 1291:
//       return "1291";
//     case 1292:
//       return "1292";
//     case 1293:
//       return "1293";
//     case 1294:
//       return "1294";
//     case 1295:
//       return "1295";
//     case 1296:
//       return "1296";
//     case 1297:
//       return "1297";
//     case 1298:
//       return "1298";
//     case 1299:
//       return "1299";
//     case 1300:
//       return "1300";
//     case 1301:
//       return "1301";
//     case 1302:
//       return "1302";
//     case 1303:
//       return "1303";
//     case 1304:
//       return "1304";
//     case 1305:
//       return "1305";
//     case 1306:
//       return "1306";
//     case 1307:
//       return "1307";
//     case 1308:
//       return "1308";
//     case 1315:
//       return "1315";
//     case 1249:
//       return '1249';
//     case 1250:
//       return '1250';
//     case 1251:
//       return '1251';
//     case 1252:
//       return '1252';
//     case 1264:
//       return '1264';
//     case 1265:
//       return '1265';
//     case 1266:
//       return '1266';
//     case 1267:
//       return '1267';
//     case 1268:
//       return '1268';
//     case 1269:
//       return '1269';
//     case 1270:
//       return '1270';
//     case 1271:
//       return '1271';
//     case 1272:
//       return '1272';
//     case 1273:
//       return '1273';
//     case 1274:
//       return '1274';
//     case 1275:
//       return '1275';

//     case 1243:
//       return '1243';
//     case 1244:
//       return '1244';
//     case 1245:
//       return '1245';
//     case 1246:
//       return '1246';
//     case 1247:
//       return '1247';
//     case 1248:
//       return '1248';
//     case 1243:
//       return '1243';
//     case 1244:
//       return '1244';
//     case 1245:
//       return '1245';
//     case 1246:
//       return '1246';
//     case 1247:
//       return '1247';
//     case 1248:
//       return '1248';
//     case 1253:
//       return '1253';
//     case 1254:
//       return '1254';
//     case 1255:
//       return '1255';
//     case 1256:
//       return '1256';
//     case 1257:
//       return '1257';
//     case 1258:
//       return '1258';
//   }
// }

export function getChartType(chartId, isFrom) {
  switch (chartId) {
    case 942:
    case 939:
      return 1;
    case 940:
      return 5;
    case 960:
    // case 944:
    case 1098:
    case 955:
    case 1049:
    case 953:
    case 1356:
      return 3;
    case 1073:
      return 2;
    case 966:
      return 2;
    case 920:
      return 6;
    case 946:
      return 1;
    case 1044:
    case 918:
    case 956:
    // case 955:
    case 944:
    case 952:
    case 916:
    case 925:
    case 1238:
    case 1318:
    case 1334:
      return 6;
    case 1143:
    case 1326:
      return 3;
    case 1138:
      return 3;
    case 948:
    case 1354:
    case 1099:
    case 1101:
    case 1105:
    case 1106:
    case 1107:
    case 1112:
    case 1174:
    case 1175:
      return 11;
    case 923:
    case 1102:
    case 1123:
    case 1232:
    case 1316:
    case 1317:
    case 1355:
      return 10;
    case 938:
    case 1115:
    case 1233:
    case 1235:
      return 12;
    case 935:
    case 1357:
      return 9;
    case 930:
    case 936:

    case 1100:
    case 1111:
    case 1113:
    case 1164:
    case 1165:
    case 1234:
    case 1236:
    case 1237:
    case 1239:
      return 13;
    case 766:
      return 15;
    case 957:
      return 16;
    case 1104:
    case 1108:
      return 17;
    case 1103:
    case 1109:
    case 1110:
    case 1116:
    case 1117:
      return 20;
    case 1133:
      return 2;
    case 1127:
      return 3;
  }
}

export function getTimeDimension(chartId, isFrom) {
  switch (chartId) {
    case 942:
      return 'CPRevenue.closeddate';
    case 939:
      return 'CPGrossProfit.ro_date';
    case 940:
      return 'CPGrossProfitPercentage.ro_date';
    case 960:
      return 'CPLaborRevenue.closeddate';
    case 944:
      return 'CPLaborGrossProfit.closeddate';
    case 1073:
      return 'CPLaborGrossProfitPercentage.closeddate';
    case 920:
      return 'CPLabourhours.ro_date';
    case 946:
      return 'CPEffectiveLabourRate.ro_date';
    case 1127:
      return 'CPLaborEffectiveLaborRate.closeddate';
    case 1044:
      return 'CPLaborHoursPerRO.closeddate';
    case 1318:
      return 'CPPartsHoursPerRO.closeddate';
    case 1098:
      return 'CPRepairAndCompetitive.closeddate';
    case 1356:
      return 'CPMaintandComp.closeddate';
    case 925:
      return 'CPRoCount.ro_date';
    case 1143:
    case 1326:
      return 'CPPartsROCount.closeddate';
    case 1138:
      return 'CPLaborROCount.closeddate';
    case 918:
      return 'CPJobCountAllCategories.closeddate';
    case 956:
      return 'CPLaborTechHours.closeddate';
    case 955:
      return 'CPAverageLaborRo.closeddate';
    case 1049:
      return 'CPPartsRevenue.closeddate';
    case 952:
      return 'CPPartsGrossProfit.closeddate';
    case 966:
      return 'CPPartsGPP.closeddate';
    case 953:
      return 'CPPartsRevenuePerRO.closeddate';
    case 916:
      return 'CPPartsMarkup.closeddate';
    case 1133:
      return 'CPLaborSoldHours.closeddate';
    case 1238:
      return 'CPPartsMarkupOverview.closeddate';
  }
}

export function getLineChartType(chartId) {
  switch (chartId) {
    case 942:
    case 939:
    case 940:
    case 920:
    case 925:
    case 946:
    case 1238:
      return 1;
    case 960:
    case 944:
    case 1073:
    case 1044:
    case 918:
    case 1098:
    case 956:
    case 955:
    case 1049:
    case 952:
    case 966:
    case 953:
    case 916:
    case 1318:
    case 1334:
    case 1356:
      return 2;
    case 1138:
    case 1143:
    case 1326:
      return 2;
    case 1127:
      return 3;
    case 948:
    case 1354:
    case 1099:
    case 1101:
    case 1105:
    case 1106:
    case 1107:
    case 1112:
    case 1174:
    case 1175:
      //case 1232:
      return 11;
    case 923:
    case 1102:
    case 1123:
    case 1115:
    case 1232:
    case 1316:
    case 1317:
    case 1355:
      return 10;
    case 938:
    case 1115:
    case 1233:
    case 1235:
      return 12;
    case 935:
    case 1357:
      return 9;
    case 930:
    case 936:
    case 1100:
    case 1111:
    case 1113:
    case 1164:
    case 1165:
    case 1234:
    case 1237:
    case 1236:
      // case 1239:
      return 13;
    case 766:
      return 15;
    case 957:
      return 16;
    case 1104:
    case 1108:
      return 17;
    case 1103:
    case 1109:
    case 1110:
    case 1116:
    case 1117:
    case 1239:
      return 20;
    case 1133:
      return 2;
  }
}
export function getDateRange(chartId, isFrom) {
  return 'Last Year';
}

export function getDetailedGraphDateRange(realm, type) {
  // return 'Last 13 months';
  // realm != 'ferrarioat_store' ?
  //   dateRange =  ['2019-12-01', '2020-12-31'] :
  //   dateRange  = ['2020-06-01', '2021-06-30']
  //   return dateRange;
  let dateRange = '';
  if (type == 'opportunity_chart') {
    dateRange = localStorage.getItem('12Months').split(',');
  } else {
    dateRange = localStorage.getItem('13Months').split(',');
  }
  //let dateRange = localStorage.getItem('13Months').split(',')
  return dateRange;
}

export function getQueryFilter(filters, schemaName, storeId, realm) {
  let filterArray = [];

  if (
    !filters.includes('All') &&
    schemaName != 'TechEfficiency' &&
    schemaName != 'EstimatedTechEfficiency' &&
    schemaName != 'EstimatedTechEfficiencyWeekly' &&
    schemaName != 'TotalTechEfficiency' &&
    schemaName != 'TechsEfficiency' &&
    schemaName != 'EstimatedTechEfficiencySoldHrsWeekly'
  ) {
    filterArray.push(
      {
        member: schemaName + '.serviceadvisor',
        operator: 'equals',
        values: filters
      },
      {
        member: schemaName + '.store_id',
        operator: 'equals',
        values:
          schemaName == 'CPSingleJobROCount' ||
          schemaName == 'CPMultiLineROCount' ||
          (schemaName == 'shopSupplies' && realm != 'haleyag')
            ? [storeId]
            : storeId
      }
    );
  } else if (
    !filters.includes('All') &&
    (schemaName == 'TechEfficiency' ||
      schemaName == 'EstimatedTechEfficiency' ||
      schemaName == 'EstimatedTechEfficiencyWeekly' ||
      schemaName == 'TotalTechEfficiency' ||
      schemaName == 'TechsEfficiency' ||
      schemaName == 'EstimatedTechEfficiencySoldHrsWeekly')
  ) {
    filterArray.push(
      {
        member: schemaName + '.lbrtechno',
        operator: 'equals',
        values: filters
      },
      {
        member: schemaName + '.store_id',
        operator: 'equals',
        values: [storeId]
      }
    );
  } else {
    filterArray.push({
      member: schemaName + '.store_id',
      operator: 'equals',
      values:
        schemaName == 'CPSingleJobROCount' ||
        schemaName == 'CPMultiLineROCount' ||
        (schemaName == 'shopSupplies' && realm != 'haleyag') ||
        schemaName == 'MPIPenetration' ||
        schemaName == 'TotalEstimatedTechEfficiencyWeekly' ||
        schemaName == 'MenuPenetration' ||
        schemaName == 'TotalTechEfficiency' ||
        schemaName == 'TechsEfficiency' ||
        schemaName == 'EstimatedTechEfficiencyWeekly' ||
        schemaName == 'EstimatedTechEfficiencySoldHrsWeekly' ||
        schemaName == 'TotalEstimatedTechEfficiencySoldHrsWeekly'
          ? [storeId]
          : storeId
    });
  }
  return filterArray;
}

export function getStoreIdFilter(schemaName, storeId) {
  let pathname = window.location.pathname;
  let filterArray = [];
  filterArray.push({
    member: schemaName + '.store_id',
    operator: 'equals',
    values:
      schemaName == 'CPGrossProfitPercentage' ||
      schemaName == 'CPEffectiveLabourRate' ||
      schemaName == 'CPPartsMarkupOverview' ||
      schemaName == 'CPLaborGrossProfitPercentage' ||
      schemaName == 'CPRepairAndCompetitive' ||
      schemaName == 'CPMaintandComp' ||
      schemaName == 'CPPartsGPP' ||
      schemaName == 'CPPartsRevenuePerRO' ||
      schemaName == 'CPPartsMarkup' ||
      schemaName == 'CPELRPaytypes' ||
      schemaName == 'CPPartsMarkupByPaytype' ||
      schemaName == 'LaborCPELRPaytype' ||
      schemaName == 'LbrHrsROPaytype' ||
      schemaName == 'PartsHrsROPaytype' ||
      schemaName == 'AvgSalePerRoPaytype' ||
      schemaName == 'PrtsPerROPaytype' ||
      schemaName == 'CPLaborEffectiveLaborRate' ||
      schemaName == 'CPAverageLaborRo' ||
      schemaName == 'PartsMarkupRepairCompetitive' ||
      schemaName == 'LaborWorkMixCost' ||
      schemaName == 'PartsWorkMixCost'
        ? storeId
        : pathname == '/MyFavorites' &&
          (schemaName == 'CPLaborVolumeOpportunity' ||
            schemaName == 'CPLaborGrossOpportunity' ||
            schemaName == 'CPPartsGrossOpportunity' ||
            schemaName == 'CPPartsVolumeOpportunity' ||
            schemaName == 'CPLaborJointOpportunity' ||
            //schemaName == 'CPTotalLaborOpportunity' ||
            // schemaName == 'CPTotalPartsOpportunity' ||
            schemaName == 'CPTotalPricingOpportunity')
        ? storeId
        : [storeId]
  });

  return filterArray;
}

export function getSubHeader(chartId) {
  let title = getTooltipForDuplicateDashboards(chartId);

  return title[0] ? (
    <Tooltip title={'Duplicated on: ' + title[0].join(', ')}>
      <span style={{ cursor: 'pointer' }}>{chartId}</span>
    </Tooltip>
  ) : (
    <span>{chartId}</span>
  );
}

export function processCells(params, type) {
  if (params.column.colId == 'targetPrice' && type == 'parts') {
    if (params.value == 0 || params.value == null) {
      return 'N/A';
    } else {
      return params.value;
    }
  }

  if (params.column.getColId() === 'salepercentage' && params.value != null) {
    const value = parseFloat(params.value).toFixed(1); // Fix to 1 decimal place
    return value + '%'; // Append % symbol
  }
  if (
    params.column.getColId() === 'discountpercentageperdiscountcpsale' &&
    params.value != null
  ) {
    const value = parseFloat(params.value).toFixed(2); // Fix to 1 decimal place
    return value + '%'; // Append % symbol
  }
  if (
    params.column.colId == 'prtsGrossprofitpercentage' ||
    params.column.colId == 'elrVariancePerc' ||
    params.column.colId == 'lbrGrossprofitpercentage' ||
    params.column.colId == 'markupVariancePerc' ||
    params.column.colId == 'variancePerc' ||
    params.column.colId == 'variancePerc_1' ||
    params.column.colId == 'workmix'
    // ||
    // params.column.colId == 'partGrossprofitpercentage' ||
    // params.column.colId == 'grossprofitpercentage'
  ) {
    if (params.value == null) {
      return '';
    } else {
      return params.value + ' %';
    }
  }
  if (
    params.column.colId == 'partGrossprofitpercentage' ||
    params.column.colId == 'grossprofitpercentage'
  ) {
    if (params.value == null) {
      return '';
    } else {
      return parseFloat(params.value).toFixed(1) + ' %';
    }
  }
  if (params.column.colId == 'salepercentage') {
    if (params.value == null) {
      return '0.0';
    } else {
      const roundedValue = parseFloat(params.value).toFixed(1);
      return roundedValue + ' %';
    }
  }
  if (
    params.column.colId == 'discountedlabor' ||
    params.column.colId == 'discountedparts' ||
    params.column.colId == 'percentagepertotalcpsale'
  ) {
    if (params.value == null) {
      return '0.0';
    } else {
      const roundedValue = parseFloat(params.value).toFixed(2);
      return roundedValue + '%';
    }
  }
  if (
    (params.column.colId == 'targetExtendedPrice' ||
      params.column.colId == 'variance' ||
      params.column.colId == 'variancePerc') &&
    type == 'parts'
  ) {
    if (
      params.node &&
      params.node.data &&
      (params.node.data.targetPrice == 0 ||
        params.node.data.targetPrice == null)
    ) {
      return 'N/A';
    } else {
      return params.value;
    }
  } else {
    return isValidDate(params.value)
      ? moment(params.value).format('MM/DD/YY')
      : isValidYearMonth(params.value)
      ? moment(params.value, 'YYYY-MM').format('MM/YY')
      : params.value;
  }

  // Check if the field is 'salepercentage' and format accordingly

  // return isValidDate(params.value)
  //   ? moment(params.value).format('MM/DD/YY')
  //   : isValidYearMonth(params.value)
  //   ? moment(params.value, 'YYYY-MM').format('MM/YY')
  //   : params.value;
}
export function isValidDate(dateString) {
  var regEx = /^\d{4}-\d{2}-\d{2}$/;
  if (dateString) {
    return String(dateString).match(regEx) != null;
  }
}
export function isValidMonthYear(dateString) {
  var regEx = /^\d{4}-\d{2}$/;
  return dateString.match(regEx) != null;
}
function isValidYearMonth(yearMonthString) {
  const regEx = /^\d{4}-\d{2}$/;
  return regEx.test(yearMonthString);
}
