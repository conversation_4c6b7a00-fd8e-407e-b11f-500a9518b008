import { getDetailedGraphDateRange } from 'src/components/ViewGraphDetailsAction';

export function getCompareStoreQuery(chartId) {
  let storeId = JSON.parse(localStorage.getItem('selectedStoreId'));
  let realm = localStorage.getItem('realm');

 let query = '';
  switch (chartId) {
    case 942: {
        query = {
            measures: ['CPRevenue.combined'],
            dimensions: ['CPRevenue.closeddate.month','CPRevenue.store_id'],
            timeDimensions: [
                {
                  dimension: 'CPRevenue.closeddate',
                  dateRange: getDetailedGraphDateRange(realm),
                  granularity: 'month',
                }
              ],
        }
        return query;
      }
    case 939: {
        query = {
          measures: ['CPGrossProfit.combinedgrossprofit'],
          dimensions: ['CPGrossProfit.ro_date.month','CPGrossProfit.store_id'],
          timeDimensions: [
            {
              dimension: 'CPGrossProfit.ro_date',
              dateRange: getDetailedGraphDateRange(realm),
              granularity: 'month',
            }
          ],
          
        };
        return query;
      }
      case 940: {
        query = {
            measures: ['CPGrossProfitPercentage.combinedprofitpercent'],
            dimensions: ['CPGrossProfitPercentage.ro_date.month','CPGrossProfitPercentage.store_id'],
            timeDimensions: [
                {
                    dimension: 'CPGrossProfitPercentage.ro_date',
                    dateRange: getDetailedGraphDateRange(realm),
                    granularity: 'month',
                }
            ],
        };
        return query;
      }
      case 920: {
        query = {
            measures: ['CPLabourhours.soldhours'],
            dimensions: ['CPLabourhours.ro_date.month','CPLabourhours.store_id'],
            timeDimensions: [
                {
                    dimension: 'CPLabourhours.ro_date',
                    dateRange: getDetailedGraphDateRange(realm),
                    granularity: 'month',
                }
            ],
        };
        return query;
      }
  
      case 946: {
        query = {
            measures: ['CPEffectiveLabourRate.effective_labour_rate'],
            dimensions: ['CPEffectiveLabourRate.ro_date.month','CPEffectiveLabourRate.store_id'],
            timeDimensions: [
                {
                    dimension: 'CPEffectiveLabourRate.ro_date',
                    dateRange: getDetailedGraphDateRange(realm),
                    granularity: 'month',
                }
            ],
        };
        return query;
      }
      case 925: {
        query = {
            measures: ['CPRoCount.ronumber'],
            dimensions: ['CPRoCount.ro_date.month','CPRoCount.store_id'],
            timeDimensions: [
                {
                    dimension: 'CPRoCount.ro_date',
                    dateRange: getDetailedGraphDateRange(realm),
                    granularity: 'month',
                }
            ],
        };
        return query;
      }
      case 1238: {
        query = {
            measures: ['CPPartsMarkupOverview.parts_markup'],
            dimensions: ['CPPartsMarkupOverview.closeddate.month','CPPartsMarkupOverview.store_id'],
            timeDimensions: [
                {
                    dimension: 'CPPartsMarkupOverview.closeddate',
                    dateRange: getDetailedGraphDateRange(realm),
                    granularity: 'month',
                }
            ],
        };
        return query;
      }
    
  }
}
