import {
  Divider,
  Grid,
  I<PERSON><PERSON><PERSON>on,
  MenuItem,
  Select,
  Tooltip,
  Typography
} from '@material-ui/core';
import FilterListIcon from '@material-ui/icons/FilterList';
import RestoreIcon from '@material-ui/icons/Restore';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import { makeStyles } from '@material-ui/styles';
import ApolloClient from 'apollo-boost';
import PropTypes from 'prop-types';
import React, { memo, useState, useEffect } from 'react';
import { GET_GOAL_SETTINGS } from 'src/graphql/queries';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { checkClosedDateInCurrentMonth } from 'src/utils/Utils';
import moment from 'moment';
import Chip from '@material-ui/core/Chip';

require('dotenv').config();
var Dealer = process.env.REACT_APP_DEALER;

const useStyles = makeStyles(theme => ({
  select: {
    '&:before': {
      borderColor: '#FFF'
    },
    '&:after': {
      borderColor: '#FFF'
    }
  },
  icon: {
    fill: '#FFF'
  },

  switch: {
    //  marginLeft: 0,
    flexDirection: 'row-reverse',
    display: 'inline-flex',
    // marginTop: -30,
    marginTop: -29,
    // marginRight: -161,
    whiteSpace: 'nowrap',
    marginLeft: -141
  }
}));

function DashboardDetailsActions({
  resetDashboard,
  setTitle,
  redirectHome,
  showCurrentMonth,
  ...rest
}) {
  const classes = useStyles();
  const [closedDate, setClosedDate] = useState('');
  const [checked, setChecked] = useState(false);
  const [openDate, setOpenDate] = useState('');

  const handleResetLayout = () => {
    resetDashboard(true);
  };

  const handleclick = () => {
    redirectHome();
  };

  useEffect(() => {
    if (showCurrentMonth) {
      if (
        checked == true ||
        localStorage.getItem('showCurrentMonth') == 'true'
      ) {
        showCurrentMonth(true);
        setChecked(true);
      } else {
        showCurrentMonth(false);
        setChecked(false);
      }
    }
  }, [checked]);

  const handleChangeCurrentMonth = event => {
    setChecked(event.target.checked);
    localStorage.setItem('showCurrentMonth', event.target.checked);
  };
  let closedDates = localStorage.getItem('closedDate');

  return (
    <>
      <div>
        <Grid
          container
          spacing={3}
          style={{ paddingLeft: '16px', paddingRight: '16px' }}
        >
          <Grid
            item
            md={
              setTitle ==
              'CP Add On vs Non Add On Revenue Percentage by Service Advisor'
                ? 8
                : 6
            }
            xs={12}
            style={{ textAlign: 'left' }}
          >
            <Tooltip
              onClick={handleclick}
              title="Go Back"
              style={{ marginRight: 15, marginLeft: 16 }}
            >
              <IconButton size="small">
                <ArrowBackIcon />
              </IconButton>
            </Tooltip>
            <Typography variant="h5" style={{ display: 'inline-block' }}>
              {setTitle}
            </Typography>
          </Grid>
          <Grid
            item
            md={
              setTitle ==
              'CP Add On vs Non Add On Revenue Percentage by Service Advisor'
                ? 4
                : 6
            }
            xs={12}
            style={{ textAlign: 'right' }}
          >
            {closedDates ? (
              <Typography
                variant="h6"
                color="initial"
                // align="right"
                style={
                  setTitle ==
                  'CP Add On vs Non Add On Revenue Percentage by Service Advisor'
                    ? { marginRight: 43, marginTop: 7 }
                    : { marginRight: 41, marginTop: 3 }
                }
              >
                {'Data as of: ' + moment(closedDates).format('MM/DD/YY')}
              </Typography>
            ) : (
              ''
            )}
            {checkClosedDateInCurrentMonth(closedDate, openDate) &&
            (setTitle == 'CP Labor Gross Profit Percentage' ||
              setTitle == 'CP Effective Labor Rate - Repair And Competitive' ||
              setTitle == 'CP Parts Gross Profit Percentage' ||
              setTitle == 'CP Parts Markup - Repair and Competitive') ? (
              <Tooltip
                title={
                  checked
                    ? 'Hide Partial Month'
                    : 'Show Partial Month'
                }
                // style={{marginLeft: 646}}
                style={{ marginLeft: 792 }}
              >
                <FormGroup>
                  <FormControlLabel
                    className={classes.switch}
                    control={
                      <Switch
                        checked={checked}
                        onChange={handleChangeCurrentMonth}
                        name="checkedA"
                        inputProps={{ 'aria-label': 'secondary checkbox' }}
                      />
                    }
                    label={
                      <span style={{ fontWeight: 500 }}>
                        {checked
                          ? 'Hide Partial Month'
                          : 'Show Partial Month'}
                      </span>
                    }
                    value="start"
                    labelPlacement="start"
                  />
                </FormGroup>
              </Tooltip>
            ) : (
              ''
            )}
            <Tooltip
              onClick={handleResetLayout}
              title="Reset Layout"
              // style={{ marginTop: -44, marginRight: 8 }}
              style={
                checkClosedDateInCurrentMonth(closedDate, openDate) &&
                (setTitle == 'CP Labor Gross Profit Percentage' ||
                  setTitle ==
                    'CP Effective Labor Rate - Repair And Competitive' ||
                  setTitle == 'CP Parts Gro8ss Profit Percentage' ||
                  setTitle == 'CP Parts Markup - Repair and Competitive')
                  ? { marginTop: -59, marginRight: 8 }
                  : setTitle ==
                    'CP Add On vs Non Add On Revenue Percentage by Service Advisor'
                  ? { marginTop: -43, marginRight: 9 }
                  : { marginTop: -44, marginRight: 8 }
              }
            >
              <IconButton size="small">
                <RestoreIcon />
              </IconButton>
            </Tooltip>
          </Grid>
        </Grid>
        <Divider></Divider>
      </div>
    </>
  );
}

DashboardDetailsActions.propTypes = {
  setTitle: PropTypes.string,
  resetDashboard: PropTypes.func,
  redirectHome: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default memo(DashboardDetailsActions);
