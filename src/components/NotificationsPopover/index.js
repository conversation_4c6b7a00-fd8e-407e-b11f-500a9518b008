import React from 'react';
import { Link as RouterLink, useHistory } from 'react-router-dom';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import {
  Popover,
  CardHeader,
  CardActions,
  Divider,
  Button,
  colors
} from '@material-ui/core';
import clsx from 'clsx';
import { setPayTypeTab } from 'src/actions';
import { useDispatch } from 'react-redux';
import NotificationList from './NotificationList';
import Placeholder from './Placeholder';

const useStyles = makeStyles(() => ({
  root: {
    width: 305,
    maxWidth: '100%',
    border: '2px solid #ee7600',
    borderRadius: 3
  },
  roots: {
    width: 220,
    maxWidth: '100%'
  },
  actions: {
    backgroundColor: colors.grey[50],
    justifyContent: 'center'
  },
  headerClass: {
    padding: '8px 10px'
  },
  titleClass: {
    fontSize: 12
  }
}));

function NotificationsPopover({
  notifications,
  anchorEl,
  handleNotificationsClose,
  error,
  ...rest
}) {
  const history = useHistory();
  const dispatch = useDispatch();
  const classes = useStyles();
  const fixErrors = () => {
    dispatch(setPayTypeTab('two'));
    history.push({
      pathname: '/PayTypeMaster'
    });
    handleNotificationsClose();
  };

  return (
    <Popover
      {...rest}
      anchorEl={anchorEl}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'center'
      }}
    >
      {error === true ? (
        <div className={classes.root}>
          <CardHeader
            classes={{ root: classes.headerClass, title: classes.titleClass }}
            title="Notifications"
          />
          <Divider />
          {notifications.length > 0 ? (
            <NotificationList
              notifications={notifications}
              handleNotificationsClose={handleNotificationsClose}
            />
          ) : (
            <Placeholder />
          )}
          <Divider />
        </div>
      ) : (
        <div className={classes.root}>
          <CardHeader title="Notifications" />
          <Divider />
          {notifications.length > 0 ? (
            <NotificationList
              notifications={notifications}
              handleNotificationsClose={handleNotificationsClose}
            />
          ) : (
            <Placeholder />
          )}
          <Divider />
          {/* {error === false ? (
            <CardActions className={classes.actions}>
              <Button
                component={RouterLink}
                size="small"
                onClick={fixErrors}
              >
                Fix Now
              </Button>
            </CardActions>
          ) : null} */}
        </div>
      )}
    </Popover>
  );
}

NotificationsPopover.propTypes = {
  anchorEl: PropTypes.any,
  className: PropTypes.string,
  notifications: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  handleNotificationsClose: PropTypes.func,
  error: PropTypes.bool.isRequired
};

export default NotificationsPopover;
