import React, { useRef } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import $ from 'jquery';
import moment from 'moment';
import { makeStyles } from '@material-ui/styles';
import {
  Avatar,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText
} from '@material-ui/core';
import PaymentIcon from '@material-ui/icons/Payment';
import PeopleIcon from '@material-ui/icons/PeopleOutlined';
import ErrorIcon from '@material-ui/icons/Error';
import CodeIcon from '@material-ui/icons/Code';
import StoreIcon from '@material-ui/icons/Store';
import gradients from 'src/utils/gradients';
import { useHistory } from 'react-router';
import { useDispatch } from 'react-redux';
import { setPayTypeTab } from 'src/actions';

const useStyles = makeStyles(theme => ({
  root: {},
  button: {
    backgroundColor: theme.palette.primary.main,
    color: '#fff',
    textTransform: 'none',
    height: '20px !important',
    fontSize: '12px',
    padding: '0px',
    '&:hover': {
      backgroundColor: '#000',
      color: '#fff'
    }
  },
  listItem: {
    '&:hover': {
      backgroundColor: theme.palette.background.default
    }
  },
  avatarBlue: {
    backgroundImage: gradients.blue
  },
  avatarGreen: {
    backgroundImage: gradients.green
  },
  avatarOrange: {
    backgroundImage: gradients.red,
    width: 30,
    height: 30
  },
  avatarIndigo: {
    backgroundImage: gradients.indigo
  },
  arrowForwardIcon: {
    color: theme.palette.icon
  },
  itemsList: {
    minWidth: 35
  },
  itemText: {
    fontSize: 13,
    marginRight: 5,
    marginLeft: 5
  }
}));

function NotificationList({
  notifications,
  className,
  handleNotificationsClose,
  ...rest
}) {
  const classes = useStyles();

  const avatars = {
    order: (
      <Avatar className={classes.avatarBlue}>
        <PaymentIcon />
      </Avatar>
    ),
    user: (
      <Avatar className={classes.avatarOrange}>
        <PeopleIcon />
      </Avatar>
    ),
    error: (
      <Avatar className={classes.avatarOrange}>
        <ErrorIcon />
      </Avatar>
    ),
    project: (
      <Avatar className={classes.avatarGreen}>
        <StoreIcon />
      </Avatar>
    ),
    feature: (
      <Avatar className={classes.avatarIndigo}>
        <CodeIcon />
      </Avatar>
    )
  };
  const history = useHistory();
  const dispatch = useDispatch();
  const itemsRef = useRef([]);
  const fixError = () => {
    history.push({
      pathname: '/PayTypeMaster'
    });
    handleNotificationsClose();
  };
  const fixErrorOpcode = () => {
    history.push({
      pathname: '/OPcodes',
      search: '',
      SelectedLocation: window.location.pathname
    });
    handleNotificationsClose();
  };
  const fixErrorAdvisor = () => {
    history.push({
      pathname: '/ServiceAdvisors',
      search: '',
      SelectedLocation: window.location.pathname
    });
    handleNotificationsClose();
  };
  const fixErrorTechnician = () => {
    history.push({
      pathname: '/Technicians',
      search: '',
      SelectedLocation: window.location.pathname
    });
    handleNotificationsClose();
  };
  const fixErrorDataImport = () => {
    history.push({
      pathname: '/DailyDataImports',
      search: '',
      SelectedLocation: window.location.pathname,
      state: {
        isFrom: 'topbar'
      }
    });
    handleNotificationsClose();
  };
  const fixErroNewAdvisors = () => {
    history.push({
      pathname: '/StoreAssignments',
      search: '',
      SelectedLocation: window.location.pathname,
      state: {
        isFrom: 'topbar',
        tabSelection: 'two'
      }
    });
    handleNotificationsClose();
  };
  const fixErroNewPaytypes = () => {
    history.push({
      pathname: '/StoreAssignments',
      search: '',
      SelectedLocation: window.location.pathname,
      state: {
        isFrom: 'topbar',
        tabSelection: 'one'
      }
    });
    handleNotificationsClose();
  };
  const fixErrorNewModels = () => {
    history.push({
      pathname: '/GridModelMapping',
      search: '',
      SelectedLocation: window.location.pathname,
      state: {
        isFrom: 'topbar',
        tabSelection: 'two'
      }
    });
    handleNotificationsClose();
  };
  return (
    <List {...rest} className={clsx(classes.root, className)} disablePadding>
      {notifications.map((notification, i) => (
        <ListItem
          classes={{ root: classes.listItem }}
          // component={RouterLink}
          divider={i < notifications.length - 1}
          key={notification.id}
        >
          <ListItemAvatar className={classes.itemsList}>
            {avatars[notification.type]}
          </ListItemAvatar>
          <ListItemText
            primary={notification.title}
            primaryTypographyProps={{
              variant: 'body1',
              className: classes.itemText
            }}
            // secondary={moment(notification.created_at).fromNow()}
          />
          {/* <ArrowForwardIcon className={classes.arrowForwardIcon} /> */}
          <ListItemAvatar>
            {notification.value === 'payType' ? (
              <Button
                //component={RouterLink}
                size="small"
                onClick={fixError}
                className={clsx(classes.button, 'reset-btn')}
              >
                Fix Now
              </Button>
            ) : notification.value === 'opcode' ? (
              <Button
                //component={RouterLink}
                size="small"
                onClick={fixErrorOpcode}
                className={clsx(classes.button, 'reset-btn')}
              >
                Fix Now
              </Button>
            ) : notification.value === 'advisor' ? (
              <Button
                //component={RouterLink}
                size="small"
                onClick={fixErrorAdvisor}
                className={clsx(classes.button, 'reset-btn')}
              >
                Fix Now
              </Button>
            ) : notification.value === 'technician' ? (
              <Button
                //component={RouterLink}
                size="small"
                onClick={fixErrorTechnician}
                className={clsx(classes.button, 'reset-btn')}
              >
                Fix Now
              </Button>
            ) : notification.value === 'labor_grid_models' ? (
              <Button
                //component={RouterLink}
                size="small"
                onClick={fixErrorNewModels}
                className={clsx(classes.button, 'reset-btn')}
              >
                Fix Now
              </Button>
            ) : notification.value === 'data_import' ? (
              <Button
                //component={RouterLink}
                size="small"
                onClick={fixErrorDataImport}
                className={clsx(classes.button, 'reset-btn')}
              >
                View
              </Button>
            ) : notification.value === 'paytype_without_store' ? (
              <Button
                //component={RouterLink}
                size="small"
                onClick={fixErroNewPaytypes}
                className={clsx(classes.button, 'reset-btn')}
              >
                Fix Now
              </Button>
            ) : (
              // : notification.value === 'advisor_without_store' ? (
              //   <Button
              //     //component={RouterLink}
              //     size="small"
              //     onClick={fixErroNewAdvisors}
              //     className={clsx(classes.button, 'reset-btn')}
              //   >
              //     Fix Now
              //   </Button>
              // )
              ''
            )}
            {/* <Button
              component={RouterLink}
              size="small"
              onClick={fixError}
              className={classes.button}
              {  notification.value === 'payType' ?
                 (disabled) : ''
              }
            >
              Fix Now
            </Button> */}
          </ListItemAvatar>
        </ListItem>
      ))}
    </List>
  );
}

NotificationList.propTypes = {
  className: PropTypes.string,
  notifications: PropTypes.array.isRequired,
  handleNotificationsClose: PropTypes.any
};

export default NotificationList;
