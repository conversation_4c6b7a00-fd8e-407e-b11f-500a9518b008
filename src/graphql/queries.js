import gql from 'graphql-tag';
export const GET_DASHBOARD_ITEMS = gql`
  query GetDashboardItems {
    dashboardItems {
      id
      layout
      vizState
      name
    }
  }
`;
export const GET_DASHBOARD_ITEM = gql`
  query GetDashboardItem($id: String!) {
    dashboardItem(id: $id) {
      id
      layout
      vizState
      name
    }
  }
`;

export const GET_CHARTMASTER = gql`
  query getChartMasterInfo($chart_id: bigint) {
    dms_physical_rw_vw_ux_chart_master(
      where: { chart_id: { _eq: $chart_id } }
    ) {
      chart_id
      description
      goal_name
      goal_value
      has_goal
      no_of_inputs
      slug
    }
  }
`;
export const EDIT_MENU = gql`
  mutation editMenu(
    $inMenuname: String
    $isdefault: BitString
    $storeid: String
    $username: String
  ) {
    statelessCcPhysicalRwInsertMenuDefaultStatus(
      input: {
        inMenuname: $inMenuname
        isdefault: $isdefault
        storeid: $storeid
        username: $username
      }
    ) {
      clientMutationId
    }
  }
`;

export const GET_MENU_NAMES = gql`
  mutation getMenuNames($storeid: String) {
    statelessCcPhysicalRwGetMenunames(input: { storeid: $storeid }) {
      getMenuNames {
        menuEnable
        menuName
        menuIsdefault
      }
    }
  }
`;
export const DELETE_MENU_MASTER = gql`
  mutation deleteMenuMaster(
    $storeid: String
    $userName: String
    $inMenuname: String
    $serviceType: Int
  ) {
    statelessCcPhysicalRwDeleteMenuMaster(
      input: {
        storeid: $storeid
        username: $userName
        inMenuname: $inMenuname
        serviceType: $serviceType
      }
    ) {
      clientMutationId
    }
  }
`;

export const GET_MENU_NAMES_DATA = gql`
  mutation getMenuData(
    $storeid: String
    $username: String
    $inMenuname: String
    $inServicetype: Int
  ) {
    statelessCcPhysicalRwGetMenuData(
      input: {
        storeid: $storeid
        username: $username
        inMenuname: $inMenuname
        inServicetype: $inServicetype
      }
    ) {
      getMdata {
        categoryList
        mName
        mInterval
        mServicetype
        mPrice
        mFrh
        mItems
        mOpcodes
        mSeries
      }
    }
  }
`;
export const INSERT_MENU = gql`
  mutation insertUser($menudata: String, $storeid: String, $username: String) {
    statelessCcPhysicalRwInsertMenuDetails(
      input: { menudata: $menudata, storeid: $storeid, username: $username }
    ) {
      clientMutationId
    }
  }
`;
export const GET_MENU_SERVICE_TYPE = gql`
  query getMenuServiceType {
    statelessCcPhysicalRwGetMenuServiceType {
      nodes {
        id
        serviceType
      }
    }
  }
`;
export const GET_GOAL_SETTINGS = gql`
  query getChartMasterInfo($chart_id: Int, $store_id: String) {
    dms_physical_rw_goal_settings(
      where: {
        chart_id: { _eq: $chart_id }
        _and: { store_id: { _eq: $store_id } }
      }
    ) {
      chart_id
      description
      goal_date
      goal_id
      goal_name
      goal_value
    }
  }
`;
export const EDIT_HISTORY_ALL = gql`
  mutation editHistoryAll(
    $startdate: Date
    $enddate: Date
    $storeid: String
    $argoptionName: String
    $timezoneOffset: String
  ) {
    statelessCcPhysicalRwGetDailyUpdateStatusWithoutfilter(
      input: {
        argoptionName: $argoptionName
        storeid: $storeid
        timezoneOffset: $timezoneOffset
        startdate: $startdate
        enddate: $enddate
      }
    ) {
      dailyUpdateStatuses {
        advisorName
        advisorid
        afterMiles
        bccId
        beforeMiles
        ccId
        chartId
        displaysort
        errorDesc
        fleetName
        goalName
        gridType
        isDefault
        keyName
        kpiIds
        kpiNo
        kpiReportType
        laboropcode
        logStatus
        logdate
        mailFrequency
        mailStatus
        make
        matrixInstalldate
        maxMiles
        menuName
        menuOpcodes
        mileageInterval
        model
        newChartdescription
        newChartname
        newFlg
        newFrh
        newGoalvalue
        newItems
        newKeyvalue
        newMarkup
        newMilegeIntervals
        newNickname
        newOpcategory
        newPaytypecode
        newPaytypedept
        newPrice
        newServiceType
        newStatus
        newStoreNickname
        newcategorized
        newfrdate
        newfrstatus
        newfrvalue
        newgridexcluded
        newlaborFixedRate
        newlaborFixedratedate
        newlaborFixedratevalue
        newmaintenancePlan
        newmarkdowndesc
        newmenuSales
        newmpiItem
        newpartsFixedRate
        newpartsFixedratedate
        newpartsFixedratevalue
        oldChartdescription
        oldChartname
        oldFlg
        oldFrh
        oldGoalvalue
        oldIsDefault
        oldItems
        oldKeyvalue
        oldMarkup
        oldMenuOpcodes
        oldMilegeIntervals
        oldNickname
        oldOpcategory
        oldPaytypecode
        oldPaytypedept
        oldPrice
        oldServiceType
        oldStatus
        oldStoreNickname
        oldcategorized
        oldfrdate
        oldfrstatus
        oldfrvalue
        oldgridexcluded
        oldlaborFixedRate
        oldlaborFixedratedate
        oldlaborFixedratevalue
        oldmaintenancePlan
        oldmarkdowndesc
        oldmenuSales
        oldmpiItem
        oldpartsFixedRate
        oldpartsFixedratedate
        oldpartsFixedratevalue
        optionName
        partSource
        paytype
        processDesc
        processFlag
        recipientId
        reportName
        scheduledOn
        settingType
        statusdate
        statustime
        storeId
        storeName
        techid
        techname
        ticketId
        toggleDuration
        userName
        userRole
        visibility
      }
    }
  }
`;
export const EDIT_HISTORY = gql`
  mutation editHistory(
    $startdate: Date
    $enddate: Date
    $storeid: String
    $argoptionName: String
    $timezoneOffset: String
  ) {
    statelessCcPhysicalRwGetDailyUpdateStatus(
      input: {
        argoptionName: $argoptionName
        storeid: $storeid
        timezoneOffset: $timezoneOffset
        startdate: $startdate
        enddate: $enddate
      }
    ) {
      dailyUpdateStatuses {
        advisorName
        advisorid
        afterMiles
        bccId
        beforeMiles
        ccId
        chartId
        displaysort
        errorDesc
        fleetName
        goalName
        gridType
        isDefault
        keyName
        kpiIds
        kpiNo
        kpiReportType
        laboropcode
        logStatus
        logdate
        mailFrequency
        mailStatus
        make
        matrixInstalldate
        maxMiles
        menuName
        menuOpcodes
        mileageInterval
        model
        newChartdescription
        newChartname
        newFlg
        newFrh
        newGoalvalue
        newItems
        newKeyvalue
        newMarkup
        newMilegeIntervals
        newNickname
        newOpcategory
        newPaytypecode
        newPaytypedept
        newPrice
        newServiceType
        newStatus
        newStoreNickname
        newcategorized
        newfrdate
        newfrstatus
        newfrvalue
        newgridexcluded
        newlaborFixedRate
        newlaborFixedratedate
        newlaborFixedratevalue
        newmaintenancePlan
        newmarkdowndesc
        newmenuSales
        newmpiItem
        newpartsFixedRate
        newpartsFixedratedate
        newpartsFixedratevalue
        oldChartdescription
        oldChartname
        oldFlg
        oldFrh
        oldGoalvalue
        oldIsDefault
        oldItems
        oldKeyvalue
        oldMarkup
        oldMenuOpcodes
        oldMilegeIntervals
        oldNickname
        oldOpcategory
        oldPaytypecode
        oldPaytypedept
        oldPrice
        oldServiceType
        oldStatus
        oldStoreNickname
        oldcategorized
        oldfrdate
        oldfrstatus
        oldfrvalue
        oldgridexcluded
        oldlaborFixedRate
        oldlaborFixedratedate
        oldlaborFixedratevalue
        oldmaintenancePlan
        oldmarkdowndesc
        oldmenuSales
        oldmpiItem
        oldpartsFixedRate
        oldpartsFixedratedate
        oldpartsFixedratevalue
        optionName
        partSource
        paytype
        processDesc
        processFlag
        recipientId
        reportName
        scheduledOn
        settingType
        statusdate
        statustime
        storeId
        storeName
        techid
        techname
        ticketId
        toggleDuration
        userName
        userRole
        visibility
      }
    }
  }
`;

export const ADD_MENU = gql`
  mutation addMenu(
    $storeid: String
    $username: String
    $inMaxMiles: BigFloat
    $inMenuname: String
    $inMenuInterval: BigFloat
    $inMilesAfter: BigFloat
    $inMilesBefore: BigFloat
    $isdefault: BitString
  ) {
    statelessCcPhysicalRwInsertMenuMaster(
      input: {
        storeid: $storeid
        username: $username
        inMaxMiles: $inMaxMiles
        inMenuname: $inMenuname
        inMenuInterval: $inMenuInterval
        inMilesAfter: $inMilesAfter
        inMilesBefore: $inMilesBefore
        isdefault: $isdefault
      }
    ) {
      clientMutationId
    }
  }
`;
export const MENU_DETAILS = gql`
  mutation menuDetails($storeid: String, $menuname: String) {
    statelessCcPhysicalRwGetMenuDetails(
      input: { storeid: $storeid, menuname: $menuname }
    ) {
      getMenudetails {
        intervalList
        jsonGeneral
        jsonOpcodeList
      }
    }
  }
`;
export const INSERT_MENU_DETAILS = gql`
  mutation insertMenuDetails(
    $storeid: String
    $username: String
    $menudata: JSON
  ) {
    statelessCcPhysicalRwInsertMenuDetails(
      input: { storeid: $storeid, username: $username, menudata: $menudata }
    ) {
      clientMutationId
    }
  }
`;
export const GET_MENU_POPUP = gql`
  mutation menuPopup($storeid: String, $menuName: String) {
    statelessCcPhysicalRwGetMenuPopup(
      input: { storeid: $storeid, argmenuName: $menuName }
    ) {
      getMenuPopupData {
        afterMiles
        beforeMiles
        maxMiles
        menuName
        milegaeInterval
      }
    }
  }
`;
export const GET_FILTERED_OPCODE = gql`
  mutation getFilteredOpcode($storeid: String, $menuName: String) {
    statelessCcPhysicalRwGetFilteredMenuopcodes(
      input: { menuname: $menuName, storeid: $storeid }
    ) {
      getFilteredMenuopcodesViews {
        opcodeList
      }
    }
  }
`;
export const DELETE_MENU = gql`
  mutation deleteMenu(
    $storeid: String
    $username: String
    $inMenuname: String
  ) {
    statelessCcPhysicalRwDeleteMenuDetails(
      input: { storeid: $storeid, username: $username, inMenuname: $inMenuname }
    ) {
      clientMutationId
    }
  }
`;
export const UPDATE_GOAL_SETTINGS = gql`
  mutation updateGoalSettings($goal_value: BigFloat, $goal_id: BigInt) {
    dmsPhysicalRwUpdateGoalSettings(
      input: { goalid: $goal_id, goalvalue: $goal_value }
    ) {
      bigInt
    }
  }
`;

export const UPDATE_GOAL_SETTINGS_BY_GROUP_NAME = gql`
  mutation updateGoalSettingsByGroupName(
    $goal_value: BigFloat
    $goal_name: String
    $group_name: String
  ) {
    statelessCcPhysicalRwUpdateGoalSettingsByGroupName(
      input: {
        goalname: $goal_name
        goalvalue: $goal_value
        groupname: $group_name
      }
    ) {
      bigInt
    }
  }
`;

export const GET_GOAL_SETTINGS_BY_GROUP_NAME = gql`
  query getGoalSettingsByGroupName($group_name: String, $store_id: String) {
    statefulCcPhysicalRwGoalSettings(
      orderBy: GOAL_NAME_ASC
      filter: {
        groupName: { equalTo: $group_name }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        goalId
        goalName
        goalValue
        storeId
      }
    }
  }
`;

export const GET_PARTS_GROSS_OPPORTUNITY_DRILL_DOWN = gql`
  query getDrillDownData($month_year: String, $store_id: String) {
    dbd_people_metrics_technician_vw_tech_revenues_and_profits(
      where: {
        month_year: { _eq: $month_year }
        _and: { store_id: { _eq: $store_id } }
      }
    ) {
      closeddate
      jobcount
      jobrole
      lbrcost
      lbropcode
      lbropcodedesc
      lbrsale
      lbrsoldhours
      month_year
      opcategory
      opsubcategory
      paytype
      paytypegroup
      personname
      prtscost
      prtssale
      ronumber
    }
  }
`;

export const GET_PARTS_GROSS_OPPORTUNITY_DRILL_DOWN_BY_DATE_RANGE = gql`
  query getDrillDownDataWithDaterange(
    $startDate: date
    $endDate: date
    $store_id: String
  ) {
    dbd_people_metrics_technician_vw_tech_revenues_and_profits(
      where: {
        closeddate: { _gte: $startDate, _lte: $endDate }
        _and: { store_id: { _eq: $store_id } }
      }
    ) {
      closeddate
      jobcount
      jobrole
      lbrcost
      lbropcode
      lbropcodedesc
      lbrsale
      lbrsoldhours
      month_year
      opcategory
      opsubcategory
      paytype
      paytypegroup
      personname
      prtscost
      prtssale
      ronumber
    }
  }
`;
export const GET_CHART_DETAILS_BY_PARENT_ID = gql`
  query getChartDetailsByParentId($parent_id: Int, $id: BigInt) {
    statefulCcPhysicalRwChartMasters(
      filter: {
        parentId: { equalTo: $parent_id }
        chartId: { notEqualTo: $id }
      }
      orderBy: SORT_ASC
    ) {
      nodes {
        chartId
        chartName
        dbdName
        parentId
        sort
      }
    }
  }
`;

export const GET_CHART_DETAILS_BY_CHART_ID = gql`
  query getChartDetailsByChartId($chart_id: BigInt) {
    statefulCcPhysicalRwChartMasters(
      filter: { chartId: { equalTo: $chart_id } }
      orderBy: SORT_ASC
    ) {
      nodes {
        chartId
        chartName
        dbdName
        parentId
      }
    }
  }
`;
export const GET_ALL_CHART_DETAILS = gql`
  query getAllChartDetails {
    fdwStatefulServiceConfigurationChartMasters {
      nodes {
        chartId
        chartName
        clientId
        createdAt
        createdBy
        description
        hasGoal
        id
        matViewName
        parentId
        slug
        sort
        dbdName
        viewDetails
        dbdId
        updatedAt
        updatedBy
        duplicateDbd
        active
        markdown
      }
    }
  }
`;

export const GET_COMPARATIVE_MIX_LEFT = gql`
  query getComparativeMixLeftSide(
    $dtfrom: date
    $dtto: date
    $group_by: String
    $mon: String
    $filter_by: String
  ) {
    dbd_cp_prediction_elr_fn_opp_analysis_of_revenue_profit_grouping(
      args: {
        dtfrom: $dtfrom
        dtto: $dtto
        group_by: $group_by
        mon: $mon
        filter_by: $filter_by
      }
    ) {
      closeddate
      elr
      jobcount
      lbrcost
      lbropcode
      lbrprftpercentage
      lbrprofit
      lbrsale
      lbrsoldhours
      month_year
      opcategory
      partsprofitpercentage
      personname
      prtscost
      prtsmarkup
      prtsprofit
      prtssale
      jobpercentage
      rocount
      singlejobrocount
      prtstolaborratio
      jobcount_repair
      jobcount_maintenance
      jobcount_competitive
    }
  }
`;
export const GET_PAY_TYPE_DETAILS_DRILL_DOWN = gql`
  query getDrillDownDataForPAyTypeErrors {
    statelessCcDrillDownTotalRevenueDetailsPaytypeMapings {
      nodes {
        closeddate
        department
        elr
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbractualhours
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        linaddonflag
        markup
        monthYear
        opcategory
        openMonth
        opendate
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        vin
      }
    }
  }
`;

export const GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN = gql`
  mutation getDrillDownDataForTotalRevenue(
    $month_year: String
    $paytype: [String!]
  ) {
    statelessCcDrilldownGetDrillDownTotalRevenueDetails(
      input: { monthyear: $month_year, payType: $paytype }
    ) {
      statelessCcDrillDownTotalRevenueDetails {
        opendate
        closeddate
        elr
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage

        prtsgrossprofit
        ronumber
        serviceadvisor
        vin
        storeId
        advisorName
        newPaytypegroup
      }
    }
  }
`;
export const GET_GROSS_PROFIT_DRILL_DOWN = gql`
  query getDrillDownDataForGrossProfit($month_year: String, $store_id: String) {
    dms_drilldown_vw_drill_down_cp_gross_profit(
      where: {
        month_year: { _eq: $month_year }
        _and: { store_id: { _eq: $store_id } }
      }
    ) {
      closeddate
      filter_by_laborparts
      filter_by_revenue
      laborgrossprofit
      laborgrossprofitpercentage
      lbrcost
      lbrlinecode
      lbropcode
      lbropcodedesc
      lbrsale
      lbrsequenceno
      lbrsoldhours
      lbrtechno
      month_year
      opcategory
      opsubcategory
      partsgrossprofit
      partsgrossprofitpercentage
      paytype
      paytypegroup
      prtextendedcost
      prtextendedsale
      ronumber
      serviceadvisor
      vin
    }
  }
`;
export const GET_PARTS_MARKUP_DRILL_DOWN = gql`
  query getDrillDownDataForPartsMarkup($month_year: String, $store_id: String) {
    statelessCcDrilldownGetDrillDownCpPartsMarkup(
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      closeddate
      lbropcode
      lbropcodedesc
      markup
      monthYear
      opsubcategory
      opcategory
      paytype
      paytypegroup
      prtextendedcost
      prtsGrossprofitpercentage
      prtextendedsale
      prtsgrossprofit
      ronumber
    }
  }
`;
export const GET_WORKMIX_REPORT_PARTS = gql`
  mutation getWorkMixReportParts($view_for: String) {
    statelessDbdPartsWorkmixGetWorkmixTrendReportParts(
      input: { viewFor: $view_for }
    ) {
      statelessDbdPartsWorkmixWorkmixTrendReportParts {
        opcategory
        opcode
        opcodedesc
        lbrtechno
        mon24
        mon25
        mon26
        mon27
        mon28
        mon29
        mon30
        mon31
        mon32
        mon33
        mon34
        mon35
        mon36
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_PARTS_OLD = gql`
  mutation getWorkMixReportParts($view_for: String) {
    statelessDbdPartsWorkmixGetWorkmixTrendReportParts(
      input: { viewFor: $view_for }
    ) {
      statelessDbdPartsWorkmixWorkmixTrendReportParts {
        opcategory
        opcode
        opcodedesc
        lbrtechno
        mon1
        mon2
        mon3
        mon4
        mon5
        mon6
        mon7
        mon8
        mon9
        mon10
        mon11
        mon12
        mon13
      }
    }
  }
`;

export const GET_WORKMIX_REPORT_LABOR = gql`
  mutation getWorkMixReportLabor($view_for: String) {
    statelessDbdLaborWorkmixGetWorkmixTrendReportLabor(
      input: { viewFor: $view_for }
    ) {
      statelessDbdLaborWorkmixTrendReportLabors {
        opcategory
        opcode
        opcodedesc
        lbrtechno
        mon24
        mon25
        mon26
        mon27
        mon28
        mon29
        mon30
        mon31
        mon32
        mon33
        mon34
        mon35
        mon36
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_LABOR_OLD = gql`
  mutation getWorkMixReportLabor($view_for: String) {
    statelessDbdLaborWorkmixGetWorkmixTrendReportLabor(
      input: { viewFor: $view_for }
    ) {
      statelessDbdLaborWorkmixTrendReportLabors {
        opcategory
        opcode
        opcodedesc
        lbrtechno
        mon1
        mon2
        mon3
        mon4
        mon5
        mon6
        mon7
        mon8
        mon9
        mon10
        mon11
        mon12
        mon13
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_LABOR = gql`
  mutation getDrillDownDataForLabor($duration: Int) {
    statelessDbdLaborWorkmixGetWorkmixOpcodesVolumeGroupingMaster(
      input: { numMonths: $duration }
    ) {
      statelessDbdLaborWorkmixOpcodeDetailsVolumeGroupingMasters {
        lbropcode
        opcategory
        tblcategory
        opcodedescription
        workmix
        elr
        grossprofitpercentage
        laborsoldhours
        jobcount
        monthYear
        storeId
        lbrsale
        lbrcost
        grossprofit
        dominantElr
        elrVariance
        paytypegroup
        opsubcategory
        rocount
        variancePerc
        partsale
        partcost
        partGrossprofit
        partGrossprofitpercentage
        totRoCount
        custRoCount
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_PARTS = gql`
  mutation getDrillDownDataForParts($duration: Int) {
    statelessDbdPartsWorkmixGetWorkmixOpcodesVolumeGroupingMaster(
      input: { numMonths: $duration }
    ) {
      statelessDbdPartsWorkmixWorkmixOpcodeDetailsVolumeGroupingMasters {
        lbropcode
        opcategory
        tblcategory
        opcodedescription
        workmix
        markup
        grossprofitpercentage
        partscost
        jobcount
        monthYear
        storeId
        partssale
        grossprofit
        dominantMarkup
        markupVariance
        paytypegroup
        opsubcategory
        rocount
        variancePerc
        totRoCount
        custRoCount
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_BY_OPCODE = gql`
  mutation getDrillDownDataByOpcode($lbropcode: String, $month_year: String) {
    statelessCcDrilldownGetDrillDownAllRevenueOpcodeDetails(
      input: { monthyear: $month_year, opcode: $lbropcode }
    ) {
      drillDownAllRevenueOpcodeDetailedViews {
        roOpendate
        closeddate
        elr
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        vin
        dominantElr
        elrVariance
        dominantMarkup
        markupVariance
        advisorName
        techName
        elrVariancePerc
        markupVariancePerc
        custRoCount
        totRoCount
      }
    }
  }
`;

export const GET_DRILL_DOWN_MONTH_YEAR_BY_OPCODE = gql`
  query getDrillDownMonthYearByOpcode($lbropcode: String, $store_id: String) {
    dms_drilldown_vw_drill_down_total_revenue_details(
      where: {
        lbropcode: { _eq: $lbropcode }
        _and: { store_id: { _eq: $store_id } }
      }
      distinct_on: month_year
    ) {
      month_year
    }
  }
`;

export const GET_COMPARISON_DATA_MONTHWISE_PARTS = gql`
  query getDrillDownDataForPartsMarkup(
    $mon1: String
    $mon2: String
    $view_for: String
  ) {
    statelessDbdPartsWorkmixGetWorkmixChartPartsMonthlyComparison(
      mon1: $mon1
      mon2: $mon2
      viewFor: $view_for
    ) {
      nodes {
        lbropcode
        mon1
        mon2
        opcategory
        lbropcodedesc
      }
    }
  }
`;
export const GET_COMPARISON_DATA_MONTHWISE_LABOR = gql`
  query getDrillDownDataForWorkmixCharts(
    $mon1: String
    $mon2: String
    $view_for: String
  ) {
    statelessDbdLaborWorkmixGetWorkmixChartsMonthlyComparison(
      viewFor: $view_for
      mon1: $mon1
      mon2: $mon2
      filter: { lbropcode: { notEqualTo: "" } }
    ) {
      nodes {
        lbropcode
        mon1
        mon2
        opcategory
        lbropcode
        lbropcodedesc
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_TOTAL_VOLUME = gql`
  mutation getWorkMixReportTotalVolume($table_for: String, $view_for: String) {
    statelessDbdLaborWorkmixGetWorkmixLaborOpcodesVolumeGrouping(
      input: { tableFor: $table_for, viewFor: $view_for }
    ) {
      statelessDbdLaborWorkmixLaborOpcodesVolumeGroupings {
        opcategory
        opcode
        mon24
        mon25
        mon26
        mon27
        mon28
        mon29
        mon30
        mon31
        mon32
        mon33
        mon34
        mon35
        mon36
        opcodedescription
        tblcategory
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_TOTAL_VOLUME_OLD = gql`
  query getWorkMixReportTotalVolume($table_for: String, $view_for: String) {
    statelessDbdLaborWorkmixGetWorkmixLaborOpcodesVolumeGrouping(
      tableFor: $table_for
      viewFor: $view_for
    ) {
      nodes {
        opcategory
        opcode
        mon1
        mon2
        mon3
        mon4
        mon5
        mon6
        mon7
        mon8
        mon9
        mon10
        mon11
        mon12
        mon13
        opcodedescription
        tblcategory
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_MOVING_ELR = gql`
  query getDrillDownDataForMovingElr {
    statelessDbdCpLaborRatesGetElrMovingAverage {
      nodes {
        currentGroup
        elrAfterDiscount
        endopendate
        monthYear
        moonthgroupno
        movingElr
        movingElrAvg
        movingElrMedian
        opendate
        roCountInCurrentGroup
        ronumber
        saleaftrdiscount
        soldhourspertranch
        startopendate
        totalLbrsale
        totalLbrsoldhours
        warrantyRate
        storeId
        make
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_MOVING_PARTS_MARKUP = gql`
  query getDrillDownDataForMovingPartsMarkup {
    statelessDbdCpPartsMarkupGetPartsMovingMarkups {
      nodes {
        currentGroup
        endopendate
        monthYear
        moonthgroupno
        movingMarkup
        movingMarkupAfterDiscount
        movingMarkupAvg
        movingMarkupMedian
        opendate
        prtscosttranche
        roCountInCurrentGroup
        ronumber
        startopendate
        totalPartsCost
        totalPartsSale
        totalSaleaftrdiscount
        warrantyMarkup
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_LABOR_ITEMIZATION = gql`
  mutation getDrillDownDataForLaborItemization(
    $Period: Int
    $timeZone: String
  ) {
    statelessDbdLaborItemizationGetChartsLaborElrSoldhoursItemization(
      input: { pPeriod: $Period, timezoneOffset: $timeZone }
    ) {
      statelessDbdLaborItemizationChartsLaborElrSoldhoursItemizationDetails {
        closeddate
        elrCompetitive
        elrMaintenance
        elrRepair
        lbrsale
        lbrsoldhours
        opcategory
        ronumber
        lbropcode
        lbropcodedesc
        competitiveCount
        maintenanceCount
        repairCount
        duration
        serviceadvisor
        opsubcategory
        advisorName
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_PARTS_ITEMIZATION = gql`
  mutation getDrillDownDataForPartsItemization(
    $duration: Int
    $timeZone: String
  ) {
    statelessDbdPartsItemizationGetChartsPartsMarkupCostItemization(
      input: { pPeriod: $duration, timezoneOffset: $timeZone }
    ) {
      statelessDbdPartsItemizationChartsPartsMarkupCostItemizationDetails {
        storeId
        closeddate
        markupCompetitive
        markupMaintenance
        markupRepair
        opcategory
        prtscost
        prtssale
        ronumber
        lbropcode
        lbropcodedesc
        competitiveCount
        maintenanceCount
        repairCount
        duration
        serviceadvisor
        opsubcategory
        advisorName
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_LABOR_BY_OPCODE = gql`
  mutation getDrillDownDataForLabor(
    $month_year: String
    $lbropcode: String
    $duration: Int
  ) {
    statelessDbdLaborWorkmixGetWorkmixOpcodesVolumeGroupingMaster(
      input: { monYear: $month_year, opcode: $lbropcode, numMonths: $duration }
    ) {
      statelessDbdLaborWorkmixOpcodeDetailsVolumeGroupingMasters {
        lbropcode
        opcategory
        tblcategory
        opcodedescription
        workmix
        elr
        grossprofitpercentage
        laborsoldhours
        jobcount
        monthYear
        storeId
        lbrsale
        lbrcost
        grossprofit
        dominantElr
        elrVariance
        paytypegroup
        opsubcategory
        rocount
        variancePerc
        partsale
        partcost
        partGrossprofit
        partGrossprofitpercentage
        totRoCount
        custRoCount
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_PARTS_BY_OPCODE = gql`
  mutation getDrillDownDataForParts(
    $month_year: String
    $lbropcode: String
    $duration: Int
  ) {
    statelessDbdPartsWorkmixGetWorkmixOpcodesVolumeGroupingMaster(
      input: { monYear: $month_year, opcode: $lbropcode, numMonths: $duration }
    ) {
      statelessDbdPartsWorkmixWorkmixOpcodeDetailsVolumeGroupingMasters {
        lbropcode
        opcategory
        tblcategory
        opcodedescription
        workmix
        markup
        grossprofitpercentage
        partscost
        jobcount
        monthYear
        storeId
        partssale
        grossprofit
        dominantMarkup
        markupVariance
        paytypegroup
        opsubcategory
        rocount
        variancePerc
        totRoCount
        custRoCount
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_RETURN_RATE = gql`
  query getDrillDownDataForReturnRate($month_year: String) {
    statelessDbdSpecialMetricsGetReturnRate(
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      nodes {
        lastSixMonVinsRevInCurrMon
        lastTwelveMonVinsRevInCurrMon
        monthYear
        rodate
        sixMonthReturnrate
        sixMonthReturnrateChart
        totalVinsInLastSixMonths
        twelveMonthReturnrate
        twelveMonthReturnrateChart
        totalVinsInLastTwelveMonths
      }
    }
  }
`;
export const GET_DATA_RETURN_CURRENT_MONTH = gql`
  query getDataReturnedForCurrentMonth($month_year: String) {
    statelessCcDrilldownGetDrillDownReturnRateCountDetail(
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      nodes {
        monthYear
        returnrate
        roDate
        vin
        vincountLastSixMonths
        vincountLastTwelveMonths
      }
    }
  }
`;
export const GET_DATA_RETURN_LAST_MONTHS = gql`
  query getDataReturnedForLastMonths($month_year: String) {
    statelessCcDrilldownGetDrillDownReturnRateVinDetails(
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      nodes {
        monthYear
        vin
        roDetails
      }
    }
  }
`;

export const GET_RO_COUNT_FOR_SINGLE_AND_MULTI_LINE_OLD = gql`
  query getROCountForSingleAndMultiLine($month_year: String) {
    statelessCcDrilldownGetDrillDownMultiJobRo(monthyear: $month_year) {
      nodes {
        ronumber
        serviceadvisor
        advisorName
        paytypegroup
      }
    }
  }
`;
export const GET_RO_COUNT_FOR_SINGLE_AND_MULTI_LINE = gql`
  mutation getROCountForSingleAndMultiLine($month_year: String) {
    statelessCcDrilldownGetDrillDownMultiJobRo(
      input: { monthyear: $month_year }
    ) {
      drillDownMultiJobRos {
        ronumber
        serviceadvisor
        advisorName
        paytypegroup
        mileage
        lbrsoldhours
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_SINGLE_JOB = gql`
  mutation getDrillDownDatForSingleJob($month_year: String) {
    statelessCcDrilldownGetDrillDownMultiJobRo(
      input: { multijob: "false", monthyear: $month_year }
    ) {
      drillDownMultiJobRos {
        closeddate
        elr
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrgrossprofit
        lbrcost
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        ronumber
        prtsgrossprofit
        vin
        serviceadvisor
        advisorName
        mileage
        technicianName
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_MULTI_JOB = gql`
  mutation getDrillDownDatForMultiJob($month_year: String) {
    statelessCcDrilldownGetDrillDownMultiJobRo(
      input: { multijob: "true", monthyear: $month_year }
    ) {
      drillDownMultiJobRos {
        closeddate
        elr
        filterByLaborparts
        filterByRevenue
        ismultijob
        lbrGrossprofitpercentage
        lbrgrossprofit
        lbrcost
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        ronumber
        prtsgrossprofit
        vin
        serviceadvisor
        advisorName
        technicianName
        mileage
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_SH_BY_PAY_TYPE = gql`
  query getDrillDownDatForSHByPayType($month_year: [String!]) {
    dbd_special_metrics_vw_labor_sold_hours_by_paytype(
      where: { month_year: { _in: $month_year } }
    ) {
      lbr_soldhours
      lbrsldhrscustomerpay
      lbrsldhrsextended
      lbrsldhrsinternal
      lbrsldhrsmaintenance
      lbrsldhrswarranty
      lbrsldhrfactoryservicecontract
      ro_date
      paytypegroup
    }
  }
`;

export const GET_RO_COUNT_FOR_TOTAL_REVENUE = gql`
  query getROCountForTotalRevenue($month_year: String) {
    statelessDbdPeopleMetricsTechnicianGetRonumberDrillDownTechRevenue(
      monthyear: $month_year
    ) {
      nodes {
        ronumber
      }
      totalCount
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_SOLD_HOURS = gql`
  query getDrillDownDataForSoldHours($month_year: String) {
    dms_drilldown_vw_drill_down_master_revenue_with_actualhours(
      where: { month_year: { _eq: $month_year } }
    ) {
      closeddate
      filter_by_laborparts
      filter_by_revenue
      flatratehours
      lbrcost
      lbrlinecode
      lbropcode
      lbropcodedesc
      lbrsale
      lbrsequenceno
      month_year
      opcategory
      opsubcategory
      paytype
      paytypegroup
      prtextendedcost
      prtextendedsale
      ronumber
      techhours
      vin
    }
  }
`;
export const UPDATE_CHART_MASTER = gql`
  mutation updateChartMaster(
    $chart_id: BigInt
    $chart_name: String
    $description: String
    $dbdname: String
    $dbd_id: BigInt
    $viewdetails: String
    $userid: String
    $inUpdatedOnRealm: String
  ) {
    statelessCcPhysicalRwUpdateChartMaster(
      input: {
        chartdesc: $description
        chartid: $chart_id
        chartname: $chart_name
        dbdname: $dbdname
        viewdetails: $viewdetails
        dbdid: $dbd_id
        userid: $userid
        inUpdatedOnRealm: $inUpdatedOnRealm
      }
    ) {
      bigInt
    }
  }
`;
export const GET_PAYTYPE_MASTER_DETAILS = gql`
  query getPayTypeMaster {
    statelessCcPhysicalRwGetPaytypeMasterDetails {
      nodes {
        department
        id
        lbrOrPrts
        payType
        payTypeCode
        storeId
        gridExcluded
        laborFixedratedate
        laborFixedratevalue
        partsFixedratedate
        partsFixedratevalue
        laborFixedRate
        partsFixedRate
        oldLaborFixedRate
        oldLaborFixedratedate
        oldLaborFixedratevalue
        oldPartsFixedRate
        oldPartsFixedratedate
        oldPartsFixedratevalue
      }
    }
  }
`;
export const GET_PAYTYPE_RETAIL_FLAG = gql`
  query getPaytypeRetailFlagSettings {
    statelessCcPhysicalRwGetPaytypeRetailFlagSettings {
      nodes {
        createdOn
        id
        mappedPaytype
        retailFlag
        sourcePaytype
        storeId
        updatedOn
      }
    }
  }
`;
export const UPDATE_PAY_TYPE_MASTER = gql`
  mutation updatePayTypeMaster(
    $id: Int!
    $pay_type: String
    $pay_type_code: String
    $department: String
    $store_id: String
  ) {
    updateDmsPhysicalRwPayTypeMaster(
      input: {
        patch: {
          payType: $pay_type
          payTypeCode: $pay_type_code
          department: $department
          storeId: $store_id
        }
        id: $id
      }
    ) {
      dmsPhysicalRwPayTypeMaster {
        id
      }
    }
  }
`;

export const UPDATE_PAY_TYPE_MASTER_BY_PAYTYPE = gql`
  mutation updatePayTypeMasterByCode(
    $p_paytype: JSON
    $store_id: String
    $user_id: String
  ) {
    statelessCcPhysicalRwUpdatePaytypeMasterByPaytypecode(
      input: { pPaytype: $p_paytype, storeid: $store_id, userid: $user_id }
    ) {
      updatePaytypeMasterStatuses {
        status
      }
    }
  }
`;

export const UPDATE_STORE_ADVISORS = gql`
  mutation updateStoreAdvisors(
    $p_Advisors: JSON
    $store_id: String
    $user_id: String
  ) {
    statelessCcPhysicalRwUpdateServiceadvisorStore(
      input: { pAdvisor: $p_Advisors, storeid: $store_id, userid: $user_id }
    ) {
      updateServiceadvisorStoreStatuses {
        status
      }
    }
  }
`;

export const GET_ADD_ON_SERVICE_ADVISOR_DETAILS = gql`
  query getAddOnServiceAdvisorDetails($month_year: [String!]) {
    statelessDbdAddonsGetChartsAddonsAllServiceadvisors(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        addonjobcount
        addonjobcount
        addonlbrsale
        addonlbrsoldhours
        addonpartscost
        addonpartsrevenue
        addonrevenuepercentage
        monthYear
        serviceadvisor
      }
    }
  }
`;
export const GET_ADD_ON_VS_NON_ADD_ON_REVENUE_PERCENT = gql`
  query getAddOnVsNonAddOnRevenuePercent($month_year: [String!]) {
    statelessDbdAddonsChartsAddonsGetChartsAddonsServiceadvisors(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        monthYear
        addonrevenuepercentage
        nonaddonrevenuepercentage
        addonlbrsale
        addonpartsrevenue
        serviceadvisor
      }
    }
  }
`;

export const GET_ALL_TECHNICIANS = gql`
  query getAllTechnicians {
    statelessCcPhysicalRwGetTechnicians {
      nodes {
        id
        active
        lbrtechno
        name
        nickname
        categorized
        teamAssignedTo
        isTeam
        endDate
        department
      }
    }
  }
`;

export const GET_TECH_EFFICIENCY_BY_MONTHS = gql`
  query getTechEfficiencyMonthwise(
    $mon1: String
    $mon2: String
    $view_for: String
  ) {
    statelessDbdPeopleMetricsTechnicianGetTechEfficiencyMonthlyComparison(
      viewFor: $view_for
      mon1: $mon1
      mon2: $mon2
    ) {
      nodes {
        lbrtechno
        mon1
        mon2
      }
    }
  }
`;
export const GET_SERVICE_EFFICIENCY_BY_MONTHS = gql`
  query getServiceEfficiencyByMonths(
    $mon1: String
    $mon2: String
    $view_for: String
  ) {
    statelessDbdPeopleMetricsServiceAdvisorRevenueMonthlyComparison(
      mon1: $mon1
      mon2: $mon2
      viewFor: $view_for
    ) {
      nodes {
        mon1
        mon2
        serviceadvisor
        serviceadvisorName
      }
    }
  }
`;

export const GET_ALL_SERVICEADVISORS = gql`
  query getAllServiceAdvisors {
    statelessCcPhysicalRwGetTblServiceAdvisors {
      nodes {
        active
        categorized
        department
        id
        name
        serviceadvisor
        nickname
        storeId
      }
    }
  }
`;

export const GET_TECH_DETAIL_DRILL_DOWN = gql`
  query getDrillDownDataForTechDetailView($lbrtechno: [String]) {
    statelessCcDrilldownGetDrillDownTechAdvisorsRevenues(
      lbrTechno: $lbrtechno
    ) {
      nodes {
        techefficiency
        serviceadvisor
        ronumber
        prtssale
        prtsprofit
        prtscost
        paytypegroup
        paytype
        opsubcategory
        opcategory
        monthYear
        markup
        lbrtechno
        lbrsoldhours
        lbrsale
        lbrprofit
        lbropcodedesc
        lbropcode
        lbrcost
        lbractualhours
        jobcount
        elr
        closeddate
        lbrsequenceno
        advisorName
        lbrtechname
        advisorActive
      }
    }
  }
`;
export const GET_SERVICE_ADVISOR_EFFICIENCY_BY_OPCATEGORY = gql`
  mutation getServiceAdvisorEfficiencyByOpCategory(
    $mon: String
    $view_for: String
  ) {
    statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategoryByMonth(
      input: { mon: $mon, viewFor: $view_for }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategories {
        competitive
        maintenance
        repair
        serviceadvisor
        serviceadvisorName
        storeId
      }
    }
  }
`;

export const GET_DISCOUNT_BY_SERVICE_ADVISOR = gql`
  query getDiscountsByServiceAdvisor($month1: [String!]) {
    statelessDbdDiscountsGetDiscountsByServiceAdvisors(
      filter: { monthYear: { in: $month1 } }
    ) {
      nodes {
        storeId
        closeddate
        serviceadvisor
        monthYear
        totallabordiscount
        totalpartsdiscount
        advisorName
      }
    }
  }
`;
export const GET_SERVICE_ADVISOR_DRILL_DOWN = gql`
  mutation getDrilldownServiceAdvisors {
    statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport(
      input: { monthyear: null, serviceAdvisor: null }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorDrillDownServiceAdvisorRevenueSummaryReports {
        monthYear
        serviceadvisor
        advisorName
        advisorActive
        opcategory
        totalrocount
        rocount
        jobcount
        lbrsale
        lbrcost
        lbrprofit
        lbrsoldhours
        prtssale
        prtscost
        prtsprofit
        avgelr
        avgmarkup
        totalrevenue
        storeId
        opsubcategory
      }
    }
  }
`;
export const GET_SERVICE_ADVISOR_REVENUES = gql`
  mutation getServiceAdvisorRevenues($view_for: String) {
    statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrend(
      input: { viewFor: $view_for }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorRevenueTrends {
        serviceadvisor
        advisorName
        isActive
        mon24
        mon25
        mon26
        mon27
        mon28
        mon29
        mon30
        mon31
        mon32
        mon33
        mon34
        mon35
        mon36
      }
    }
  }
`;
export const GET_SERVICE_ADVISOR_REVENUES_OLD = gql`
  mutation getServiceAdvisorRevenues($view_for: String) {
    statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrend(
      input: { viewFor: $view_for }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorRevenueTrends {
        serviceadvisor
        advisorName
        isActive
        mon1
        mon2
        mon3
        mon4
        mon5
        mon6
        mon7
        mon8
        mon9
        mon10
        mon11
        mon12
        mon13
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_ADD_ON = gql`
  query getDrillDownDataForAddOns($month_year: String) {
    statelessCcDrilldownGetDrillDownTotalRevenueDetailsAddons(
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      nodes {
        opendate
        closeddate
        elr
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        linaddonflag
        markup
        monthYear
        opcategory
        openMonth
        opendate
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        vin
        advisorName
      }
    }
  }
`;
export const GET_DATA_FOR_TECHNICIAN_SUMMARY_BY_TECH_NO = gql`
  query getDrillDownDataTechnicianSummary(
    $month_year: [String!]
    $lbrtechno: String
  ) {
    statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHrs(
      filter: {
        monthYear: { in: $month_year }
        lbrtechno: { equalTo: $lbrtechno }
      }
    ) {
      nodes {
        closeddate
        flatratehours
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrtechno
        monthYear
        techefficiencynonzero
        techefficiency
        techhours
        totaljobs
        lbrtechname
        lbrtechActive
      }
    }
  }
`;
export const GET_DATA_FOR_TECHNICIAN_SUMMARY = gql`
  query getDrillDownDataTechnicianSummary($month_year: [String!]) {
    statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHrs(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        closeddate
        flatratehours
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrtechno
        monthYear
        techefficiencynonzero
        techefficiency
        techhours
        totaljobs
        lbrtechname
        lbrtechActive
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_BY_TECH_NO = gql`
  query getDrillDownDataByTechNo($lbrtechno: [String], $month_year: String) {
    statelessCcDrilldownGetDrillDownTechAdvisorsRevenues(
      monthyear: $month_year
      lbrTechno: $lbrtechno
    ) {
      nodes {
        techefficiency
        serviceadvisor
        ronumber
        prtssale
        prtsprofit
        prtscost
        paytypegroup
        paytype
        opsubcategory
        opcategory
        monthYear
        markup
        lbrtechno
        lbrsoldhours
        lbrsale
        lbrprofit
        lbropcodedesc
        lbropcode
        lbrcost
        lbractualhours
        jobcount
        elr
        closeddate
        lbrsequenceno
        advisorName
        lbrtechname
        advisorActive
        lbrtechActive
      }
    }
  }
`;

export const GET_DATA_FOR_SERVICE_ADVISOR_SUMMARY_BY_SA = gql`
  mutation getDrillDownDataServiceAdvisorSummary(
    $month_year: String
    $serviceadvisor: [String!]
  ) {
    statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport(
      input: { monthyear: $month_year, serviceAdvisor: $serviceadvisor }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorDrillDownServiceAdvisorRevenueSummaryReports {
        monthYear
        serviceadvisor
        advisorName
        advisorActive
        opcategory
        totalrocount
        rocount
        jobcount
        lbrsale
        lbrcost
        lbrprofit
        lbrsoldhours
        prtssale
        prtscost
        prtsprofit
        avgelr
        avgmarkup
        totalrevenue
        storeId
        opsubcategory
      }
    }
  }
`;
export const GET_DATA_FOR_SERVICE_ADVISOR_SUMMARY = gql`
  mutation getDrillDownDataServiceAdvisorSummary {
    statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport(
      input: { monthyear: null, serviceAdvisor: null }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorDrillDownServiceAdvisorRevenueSummaryReports {
        monthYear
        serviceadvisor
        advisorName
        advisorActive
        opcategory
        totalrocount
        rocount
        jobcount
        lbrsale
        lbrcost
        lbrprofit
        lbrsoldhours
        prtssale
        prtscost
        prtsprofit
        avgelr
        avgmarkup
        totalrevenue
        storeId
        opsubcategory
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_BY_SERVICE_ADVISOR = gql`
  query getDrillDownDataByServiceAdvisor(
    $serviceadvisor: [String!]
    $month_year: String
  ) {
    statelessCcDrilldownGetDrillDownTechAdvisorsRevenues(
      monthyear: $month_year
      serviceAdvisor: $serviceadvisor
    ) {
      nodes {
        closeddate
        elr
        lbractualhours
        jobcount
        lbrcost
        lbropcode
        lbrlinecode
        lbropcodedesc
        lbrprofit
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechno
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtscost
        prtsprofit
        prtssale
        ronumber
        serviceadvisor
        techefficiency
        advisorName
        lbrtechname
        advisorActive
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_DISCOUNT = gql`
  query getDrillDownDataForDiscount($month_year: String) {
    dms_drilldown_vw_drill_down_discount_details(
      where: { month_year: { _eq: $month_year } }
    ) {
      disclassortype
      closeddate
      disdesc
      disdiscountid
      dislabordiscount
      dislevel
      dislinecode
      dislopseqno
      dispartsdiscount
      dissequenceno
      dissequenceno_idx
      month_year
      paytypegroup
      ronumber
      serviceadvisor
      vin
    }
  }
`;
export const GET_DATA_FOR_TECHNICIAN_VIEW = gql`
  query getDrillDownDataByTechNo($month_year: String) {
    statelessCcDrilldownGetDrillDownTechAdvisorsRevenues(
      monthyear: $month_year
    ) {
      nodes {
        closeddate
        elr
        lbractualhours
        jobcount
        lbrcost
        lbropcode
        lbrlinecode
        lbropcodedesc
        lbrprofit
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechno
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtscost
        prtsprofit
        prtssale
        ronumber
        serviceadvisor
        techefficiency
        advisorName
        lbrtechname
        advisorActive
        lbrtechActive
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_DISCOUNT_COMPARISON = gql`
  query getDrillDownDataForDiscountComparison(
    $month_year: String
    $store_id: String
  ) {
    dms_drilldown_vw_drill_down_total_revenue_details_addons(
      where: {
        month_year: { _eq: $month_year }
        _and: { store_id: { _eq: $store_id } }
      }
    ) {
      closeddate
      lbrcost
      lbrlinecode
      lbropcode
      lbropcodedesc
      lbrsale
      lbrsequenceno
      lbrsoldhours
      linaddonflag
      month_year
      opcategory
      opsubcategory
      paytype
      paytypegroup
      prtextendedcost
      prtextendedsale
      ronumber
      serviceadvisor
    }
  }
`;

export const GET_CP_GROSS_PROFITPERCENT_940 = gql`
  query get_CP_GrossProfitPercent_940(
    $advisor: String
    $month_year: [String!]
  ) {
    statelessDbdCpOverviewGetGrossProfitPercentageServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        combinedgrossprofitpercentage
        laborgrossprofitpercentage
        monthYear
        partsgrossprofitpercentage
        rodate
      }
    }
  }
`;

export const GET_CP_REVENUE_DATA = gql`
  mutation get_CP_Revenue($advisor: [String!]) {
    statelessDbdCpOverviewGetUxRevenue(input: { advisor: $advisor }) {
      statelessDbdCpOverviewUxRevenues {
        jsonData
      }
    }
  }
`;

export const GET_CP_GP_DATA = gql`
  mutation get_CP_GP($advisor: [String!]) {
    statelessDbdCpOverviewGetGrossProfit(input: { advisor: $advisor }) {
      statelessDbdCpOverviewUxGrossProfits {
        jsonData
      }
    }
  }
`;

export const GET_CP_GP_PERCENTAGE_DATA = gql`
  mutation get_CP_GP_PERC($advisor: [String!]) {
    statelessDbdCpOverviewGetGrossProfitPercentageAll(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpOverviewUxGrossProfitPercentageAlls {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR_SOLD_HOURS_DATA = gql`
  mutation get_CP_LBR_SOLDHOURS($advisor: [String!]) {
    statelessDbdCpOverviewGetLaborSoldHours(input: { advisor: $advisor }) {
      statelessDbdCpOverviewUxLaborSoldHours {
        jsonData
      }
    }
  }
`;
export const GET_CP_ELR_DATA = gql`
  mutation get_CP_ELR($advisor: [String!]) {
    statelessDbdCpOverviewGetElrAll(input: { advisor: $advisor }) {
      statelessDbdCpOverviewUxElrAlls {
        jsonData
      }
    }
  }
`;
export const GET_CP_PARTS_MARKUP_DATA = gql`
  mutation get_CP_PARTS_MARKUP($advisor: [String!]) {
    statelessDbdCpPartsGetMarkupbyPaytypes(input: { advisor: $advisor }) {
      markupbyPaytypes {
        jsonData
      }
    }
  }
`;

export const GET_CP_PARTS_MARKUP_DATA_1238 = gql`
  mutation get_CP_PARTS_MARKUP($advisor: [String!]) {
    statelessDbdCpOverviewGetPartsMarkup(input: { advisor: $advisor }) {
      statelessDbdCpOverviewPartsMarkupAlls {
        jsonData
      }
    }
  }
`;

export const GET_ALL_SERVICE_ADVISOR_DETAILS = gql`
  query getAllServiceAdvDetails {
    statelessCcPhysicalRwGetTblServiceAdvisors {
      nodes {
        active
        categorized
        department
        id
        name
        serviceadvisor
        nickname
        storeId
      }
    }
  }
`;

export const UPDATE_SERVICE_ADVISOR = gql`
  mutation updateServiceAdvisor(
    $advisor: String
    $pCategorized: Int
    $statusval: BitString
    $nickName: String
    $user_id: String
    $pDepartment: String
  ) {
    statelessCcPhysicalRwGetUpdateServiceAdvisorStatus(
      input: {
        advisor: $advisor
        statusval: $statusval
        pCategorized: $pCategorized
        nickName: $nickName
        userId: $user_id
        pDepartment: $pDepartment
      }
    ) {
      clientMutationId
    }
  }
`;
export const INSERT_SERVICE_ADVISOR = gql`
  mutation insertServiceAdvisor($name: String, $serviceadvisor: String) {
    createStatefulCcPhysicalRwServiceAdvisor(
      input: {
        statefulCcPhysicalRwServiceAdvisor: {
          name: $name
          serviceadvisor: $serviceadvisor
        }
      }
    ) {
      statefulCcPhysicalRwServiceAdvisor {
        id
      }
    }
  }
`;
export const DELETE_SERVICE_ADVISOR = gql`
  mutation deleteServiceAdvisor($id: Int!) {
    deleteStatefulCcPhysicalRwServiceAdvisor(input: { id: $id }) {
      statefulCcPhysicalRwServiceAdvisor {
        id
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_OPCODE_VIEW = gql`
  mutation getDrillDownDataByOpcode($month_year: String, $lbropcode: String) {
    statelessCcDrilldownGetDrillDownAllRevenueOpcodeDetails(
      input: { monthyear: $month_year, opcode: $lbropcode }
    ) {
      drillDownAllRevenueOpcodeDetailedViews {
        roOpendate
        closeddate
        elr
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        vin
        dominantElr
        elrVariance
        dominantMarkup
        markupVariance
        advisorName
        techName
        elrVariancePerc
        markupVariancePerc
        custRoCount
        totRoCount
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_MASTER_DISCOUNT_JOBS = gql`
  query getDrillDownDataForMasterDiscountJobs($month_year: String) {
    foc3_vw_master_discounts_job_details_by_discount_id(
      where: { month_year: { _eq: $month_year } }
    ) {
      dicountedrocount
      discount_job_details
      discountjobcount
      disdesc
      disdiscountid
      dislevel
      labordiscount
      lbrsale
      month_year
      non_discount_job_details
      nondiscountedjobcount
      partsdiscount
      prtsale
    }
  }
`;

export const GET_DISCOUNT_RO_PERC_BY_SERVICE_ADVISOR = gql`
  query getDiscountsROPercByServiceAdvisor(
    $mon1: String
    $mon2: String
    $view_for: String
  ) {
    statelessDbdDiscountsGetDiscountServiceAdvisorsMonthlyComparison(
      mon1: $mon1
      mon2: $mon2
      viewFor: $view_for
    ) {
      nodes {
        mon1
        mon2
        serviceadvisor
        advisorName
      }
    }
  }
`;
export const GET_DISCOUNT_BY_SERVICE_ADVISOR_OPCATEGORY = gql`
  mutation getDiscountsByServiceAdvisorOpcategory(
    $month1: String!
    $month2: String!
    $storeId: String
  ) {
    statelessDbdDiscountsGetDiscountJobcountPercentageServiceAdvisorOpcategory(
      input: { mon1: $month1, mon2: $month2, inStoreid: $storeId }
    ) {
      discountJobcountPercentageServiceAdvisorOpcategories {
        jobcountpercentagecompetitive
        jobcountpercentagemaintenance
        jobcountpercentagerepair
        monthYear
        serviceadvisor
        advisorName
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_DISCOUNT_JOB_COUNT = gql`
  query getDrillDownDataForDiscountJobCount($month_year: [String!]) {
    statelessCcDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        serviceadvisor
        ronumber
        prtsale
        partsdiscount
        opsubcategory
        monthYear
        lbrsequenceno
        opcategory
        lbrsale
        lbropcodedesc
        lbropcode
        lbrlinecode
        lbrlabortype
        lbrPaytype
        labordiscount
        disdesc
        advisorName
      }
    }
  }
`;
export const GET_CP_OVERVIEW_ELR_DETAILS_HASURA = gql`
  query get_CP_Overview_ELR_Details($advisor: String, $mon_year: [String!]) {
    dbdCpOverviewFnDetailElrServiceAdvisor(
      advisor: $advisor
      filter: { monYear: { in: $mon_year } }
    ) {
      nodes {
        elrCombined
        elrCompetitive
        elrMaintenance
        elrRepair
        elrRepairCompetitive
        monYear
        rodate
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_DISCOUNTED_JOBS_PER_RO = gql`
  query getDrillDownDataForDiscountedRo {
    dbd_discounts_vw_discounted_jobs_per_ro {
      discountedjobsperro
      min
      month_year
      totaldiscountedjobs
      totaldiscountedrocount
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY = gql`
  query getDrillDownDataForDiscountSummary($month_year: [String!]) {
    statelessDbdDiscountGetLaborDiscountSummary(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        cpdiscountedrocount
        discountedlbrsale
        disdesc
        discountjobcount
        discountedvolume
        disdiscountid
        eachlabordiscount
        lbrsaleforeachdiscountedjobs
        netlaborsale
        monthYear
        overallbrcost
        overallbrsale
        overallcouponmargin
        overalldiscountinmonth
        overallrocount
        singlecouponmargin
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_WITH_SA = gql`
  mutation getDrilldownforDiscountSummaryAdvisor {
    statelessDbdDiscountGetLaborDiscountSummaryByServiceAdvisor(
      input: { clientMutationId: "0" }
    ) {
      laborDiscountSummaryByServiceAdvisorDetails {
        cpdiscountedrocount
        discountedlbrsale
        discountedvolume
        discountjobcount
        disdesc
        disdiscountid
        eachlabordiscount
        lbrsaleforeachdiscountedjobs
        monthYear
        netlaborsale
        overallbrcost
        overallbrsale
        overallcouponmargin
        overalldiscountinmonth
        overallrocount
        serviceadvisor
        singlecouponmargin
        advisorName
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_PARTS = gql`
  mutation getDrillDownDataForDiscountSummaryParts($month_year: String) {
    statelessDbdDiscountGetPartsDiscountSummary(
      input: { pMonthYear: $month_year }
    ) {
      partsDiscountSummaryDetails {
        cpdiscountedrocount
        discountedprtsale
        discountedvolume
        discountjobcount
        disdesc
        disdiscountid
        eachpartsdiscount
        monthYear
        netpartssale
        overallcouponmargin
        overalldiscountinmonth
        overallprtcost
        overallprtsale
        overallrocount
        prtsaleforeachdiscountedjobs
        singlecouponmargin
        storeId
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_DISCOUNT_SUMMARY_WITH_SA_PARTS = gql`
  mutation getDrilldownforDiscountSummaryAdvisorParts {
    statelessDbdDiscountGetPartsDiscountSummaryByServiceAdvisor(
      input: { clientMutationId: "0" }
    ) {
      partsDiscountSummaryByServiceAdvisorDetails {
        cpdiscountedrocount
        discountedprtsale
        discountedvolume
        discountjobcount
        disdesc
        disdiscountid
        eachpartsdiscount
        monthYear
        netpartssale
        overallcouponmargin
        overalldiscountinmonth
        overallprtcost
        overallprtsale
        overallrocount
        prtsaleforeachdiscountedjobs
        serviceadvisor
        singlecouponmargin
        advisorName
      }
    }
  }
`;
export const GET_CLIENT_DETAILS = gql`
  query get_client_Details($client_id: BigInt) {
    dmsPhysicalRoVwClientMasters(
      filter: { clientId: { equalTo: $client_id } }
    ) {
      nodes {
        clientDescription
        clientId
        clientName
        isActive
        storeName
      }
    }
  }
`;

export const GET_DRILL_DOWN_DISCOUNT_SUMMARY_LABOR = gql`
  query getDrillDownDiscountSummaryLabor(
    $month_year: String
    $disdiscountid: String
  ) {
    statelessCcDrilldownGetDrillDownDiscountedLaborDetails(
      filter: {
        monthYear: { equalTo: $month_year }
        disdiscountid: { equalTo: $disdiscountid }
      }
    ) {
      nodes {
        apportionedlbrdiscount
        disdesc
        disdiscountid
        dislevel
        labordiscount
        lbrcost
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        monthYear
        opcategory
        paytype
        paytypegroup
        ronumber
        saleaftersdiscount
        salepercentage
        serviceadvisor
        advisorName
      }
    }
  }
`;
export const GET_DRILL_DOWN_DISCOUNT_SUMMARY_LABOR_SA = gql`
  query getDrillDownDiscountSummaryLabor(
    $month_year: String
    $disdiscountid: String
    $serviceadvisor: String
  ) {
    statelessCcDrilldownGetDrillDownDiscountedLaborDetails(
      filter: {
        monthYear: { equalTo: $month_year }
        disdiscountid: { equalTo: $disdiscountid }
        serviceadvisor: { equalTo: $serviceadvisor }
      }
    ) {
      nodes {
        apportionedlbrdiscount
        disdesc
        disdiscountid
        dislevel
        labordiscount
        lbrcost
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        monthYear
        opcategory
        paytype
        paytypegroup
        ronumber
        saleaftersdiscount
        salepercentage
        serviceadvisor
        advisorName
      }
    }
  }
`;
export const GET_CP_LABOR_GROSS_PROFITPERCENT_1073 = gql`
  query get_CP_Labor_GrossProfitPercent_1073($advisor: String) {
    statelessDbdCpLaborGetGrossProfitPercentageServiceAdvisor(
      advisor: $advisor
    ) {
      nodes {
        lgpcurrentyear
        lgppreviousyear
        lgpyearbeforeprevious
        closeddate
      }
    }
  }
`;
export const GET_DRILL_DOWN_DISCOUNT_SUMMARY_PARTS = gql`
  query getDrillDownDiscountSummaryParts(
    $month_year: String
    $disdiscountid: String
  ) {
    statelessCcDrilldownGetDrillDownDiscountedPartsDetails(
      filter: {
        monthYear: { equalTo: $month_year }
        disdiscountid: { equalTo: $disdiscountid }
      }
    ) {
      nodes {
        apportionedlbrdiscount
        disdesc
        disdiscountid
        dislevel
        laborpaytype
        laborpaytypegroup
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsequenceno
        monthYear
        opcategory
        partsdiscount
        prtcost
        prtlaborsequenceno
        prtlinecode
        prtsaleafterdiscount
        prtsequenceno
        prtssale
        ronumber
        salepercentage
        serviceadvisor
        advisorName
      }
    }
  }
`;
export const GET_DRILL_DOWN_DISCOUNT_SUMMARY_PARTS_SA = gql`
  query getDrillDownDiscountSummaryParts(
    $month_year: String
    $disdiscountid: String
    $serviceadvisor: String
  ) {
    statelessCcDrilldownGetDrillDownDiscountedPartsDetails(
      filter: {
        monthYear: { equalTo: $month_year }
        disdiscountid: { equalTo: $disdiscountid }
        serviceadvisor: { equalTo: $serviceadvisor }
      }
    ) {
      nodes {
        apportionedlbrdiscount
        disdesc
        disdiscountid
        dislevel
        laborpaytype
        laborpaytypegroup
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsequenceno
        monthYear
        opcategory
        partsdiscount
        prtcost
        prtlaborsequenceno
        prtlinecode
        prtsaleafterdiscount
        prtsequenceno
        prtssale
        ronumber
        salepercentage
        serviceadvisor
        advisorName
      }
    }
  }
`;

export const GET_CP_LABOR_CompetetiveRepair_1098 = gql`
  query get_CP_Labor_ComepetetiveRepair_1098($advisor: String) {
    statelessDbdCpLaborGetElrRepairServiceAdvisor(advisor: $advisor) {
      nodes {
        closeddate
        repairCurrentyear
        repairPastyear
        repairPreviousyear
      }
    }
  }
`;
export const GET_CP_LABOR_CompetetiveMaint_1356 = gql`
  query get_CP_Labor_ComepetetiveMaint_1356($advisor: String) {
    statelessDbdCpLaborGetElrCompetitiveMaintenanceServiceAdvisor(
      advisor: $advisor
    ) {
      nodes {
        closeddate
        competitivemaintenanceCurrentyear
        competitivemaintenancePastyear
        competitivemaintenancePreviousyear
      }
    }
  }
`;
export const GET_CP_PARTS_GROSS_PROFITPERCENT_966 = gql`
  query get_CP_Parts_GrossProfitPercent_966($advisor: String) {
    statelessDbdCpPartsGetPartsGrossProfitPercentageServiceAdvisor(
      advisor: $advisor
    ) {
      nodes {
        closeddate
        partsgrossprofitpercentageCurrentyear
        partsgrossprofitpercentagePastyear
        partsgrossprofitpercentagePreviousyear
        monthYear
      }
    }
  }
`;

export const GET_CP_LABOR_ELR_1127_details = gql`
  query get_CP_Labor_ELR_1127_Details($advisor: String, $mon_year: [String!]) {
    dbdCpLaborFnDetailElrServiceAdvisor(
      advisor: $advisor
      filter: { monYear: { in: $mon_year } }
    ) {
      nodes {
        elrCombined
        elrCompetitive
        elrMaintenance
        elrRepair
        elrRepairCompetitive
        rodate
        monYear
      }
    }
  }
`;

export const GET_CP_LABOR_RO_HOURS_DETAILS = gql`
  query get_CP_Labor_Ro_hours_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    dbdCpLaborFnDetailHoursPerRepairOrderServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        hoursPerRepairOrder
        hoursPerRepairOrderCompet
        hoursPerRepairOrderMaintenance
        hoursPerRepairOrderRepair
        hoursPerRepairOrderShopsupplies
        monthYear
        rodate
      }
    }
  }
`;

export const GET_CP_LABOR_1079_DETAILS = gql`
  query get_1079_details($advisor: String, $month_year: [String!]) {
    dbdCpLaborFnDetailHoursRepairOrderCountPercentageServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        hoursPerRepairOrder
        hoursPerRepairOrderCompet
        hoursPerRepairOrderMaintenance
        hoursPerRepairOrderRepair
        monthYear
        rodate
      }
    }
  }
`;

export const GET_CP_LABOR_1083_DETAILS = gql`
  query get_1083_details(
    $advisor: String
    $month_year: [String!]
    $store_id: String
  ) {
    dbd_cp_labor_fn_detail_repair_order_count_percentage_service_advisor(
      args: { advisor: $advisor }
      where: {
        month_year: { _in: $month_year }
        _and: { store_id: { _eq: $store_id } }
      }
    ) {
      ro_countcompetitive
      ro_countmaintenance
      ro_countrepair
      ro_countshopsupplies
      rodate
      month_year
    }
  }
`;

export const GET_CP_PARTSRO_BYCATEGORY_DETAILS = gql`
  query get_PartsRO_By_Category_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    dbd_cp_parts_fn_detail_revenue_per_ro_by_category_service_advisor(
      args: { advisor: $advisor }
      where: { month_year: { _in: $month_year } }
    ) {
      month_year
      parts_revenue_per_ro
      parts_revenue_per_ro_competitive
      parts_revenue_per_ro_maintenance
      parts_revenue_per_ro_repair
      parts_revenue_per_ro_shopsupplies
      rodate
    }
  }
`;
export const GET_CP_PARTMARKUP_DETAILS = gql`
  query get_PartsRO_By_Category_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    dbd_cp_parts_fn_detail_parts_markup_service_advisor(
      args: { advisor: $advisor }
      where: { month_year: { _in: $month_year } }
    ) {
      month_year
      closeddate
      parts_markup_all
      parts_markup_competitive
      parts_markup_maintenance
      parts_markup_repair
    }
  }
`;
export const GET_LABOR_HOURS_PER_RO_BYYEAR = gql`
  query get_GET_LABOR_HOURS_PER_RO_BYYEAR($advisor: String) {
    statelessDbdCpLaborGetHoursPerRepairOrderByYearServiceAdvisor(
      advisor: $advisor
    ) {
      nodes {
        closeddate
        hoursPerRepairOrderCurrentyear
        hoursPerRepairOrderPastyear
        hoursPerRepairOrderPreviousyear
      }
    }
  }
`;

export const GET_CP_PARTS_MARKUP_916 = gql`
  query get_CP_Parts_Markup_916($advisor: String) {
    statelessDbdCpPartsPartsMarkupServiceAdvisor(advisor: $advisor) {
      nodes {
        closeddate
        monthYear
        partsMarkupCurrentyear
        partsMarkupPastyear
        partsMarkupPreviousyear
      }
    }
  }
`;
export const GET_DASHBOARD_DETAILS = gql`
  query getDbdDetails {
    statelessCcPhysicalRwDashboardLists {
      nodes {
        dbdId
        dbdName
        schemaname
      }
    }
  }
`;
export const GET_VIEW_DETAILS = gql`
  query getViewDetails {
    dms_physical_rw_vw_tbl_views {
      mvname
      schemaname
    }
  }
`;
export const GET_CP_LABOR_RO_BY_YEAR = gql`
  query get_LaborROByYear_details($advisor: String) {
    statelessDbdCpLaborGetAverageRateByRepairOrderByYearServiceAdvisorr(
      advisor: $advisor
    ) {
      nodes {
        closeddate
        salePerRepairOrderCurrentyear
        salePerRepairOrderPastyear
        salePerRepairOrderPreviousyear
      }
    }
  }
`;
export const GET_CP_LABORSALE_PER_RO_DETAILS = gql`
  query get_LaborRO_By_Category_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    dbdCpLaborFnDetailAverageRateByRoCategoryServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        averageratebycategory
        competitive
        maintenance
        monthYear
        repair
        rodate
        shopsupplies
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_LABOR_DISCOUNT = gql`
  query getDrillDownDataForLaborDiscount($month_year: [String!]) {
    statelessCcDrilldownGetDrillDownDiscountedLaborDetails(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        apportionedlbrdiscount
        disdesc
        disdiscountid
        dislevel
        labordiscount
        lbrcost
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        monthYear
        opcategory
        paytype
        paytypegroup
        ronumber
        saleaftersdiscount
        salepercentage
        serviceadvisor
        advisorName
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_PARTS_DISCOUNT = gql`
  query getDrillDownDataForPartsDiscount($month_year: String) {
    statelessCcDrilldownGetDrillDownDiscountedPartsDetails(
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      nodes {
        apportionedlbrdiscount
        disdesc
        disdiscountid
        dislevel
        laborpaytype
        laborpaytypegroup
        lbrlinecode
        lbropcode
        lbropcodedesc
        serviceadvisor
        salepercentage
        ronumber
        prtssale
        prtsequenceno
        prtsaleafterdiscount
        prtlinecode
        partsdiscount
        opcategory
        prtcost
        prtlaborsequenceno
        monthYear
        lbrsequenceno
        advisorName
      }
    }
  }
`;
export const GET_DRILL_DOWN_DISCOUNT_BY_RO = gql`
  query getDrillDownDiscountByRO($month_year: [String!]) {
    statelessCcDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        serviceadvisor
        ronumber
        prtsale
        partsdiscount
        opsubcategory
        monthYear
        lbrsequenceno
        opcategory
        lbrsale
        lbropcodedesc
        lbropcode
        lbrlinecode
        lbrlabortype
        lbrPaytype
        labordiscount
        disdesc
        advisorName
      }
    }
  }
`;

// export const GET_OPCODE_DETAILS = gql`
//   query getOpcodeDetails($store_id: String) {
//     statelessCcPhysicalRwRoOpcodesDetails(
//       orderBy: OPCATEGORY_ASC
//       filter: { storeId: { equalTo: $store_id } }
//     ) {
//       nodes {
//         menuSales
//         opcategory
//         opcodedescription
//         opcode
//         paytype
//         slno
//         gridExcluded
//         categorized
//         mpiItem
//         maintenancePlan
//       }
//     }
//   }
// `;
export const GET_OPCODE_DETAILS = gql`
  query getOpcodeDetails($user_id: String) {
    statelessCcPhysicalRwGetRoOpcodesDetails(userid: $user_id) {
      nodes {
        menuSales
        opcategory
        opcodedescription
        opcode
        slno
        gridExcluded
        categorized
        mpiItem
        maintenancePlan
        recentUpdatedFlag
        department
        fixedRate
        fixedRateValue
        fixedRateDate
        lastUsedOn
        usedLast12Mths
        fixedRateParts
        partsFixedRateDate
        partsFixedRateValue
      }
    }
  }
`;

export const GET_NONCPOPCODE_DETAILS = gql`
  query getNonCpOpcodeDetails($user_id: String) {
    statelessCcPhysicalRwGetRoOpcodesDetailsExceptCp(userid: $user_id) {
      nodes {
        menuSales
        opcategory
        opcodedescription
        opcode
        slno
        gridExcluded
        categorized
        mpiItem
        maintenancePlan
        recentUpdatedFlag
        department
        fixedRate
        fixedRateValue
        fixedRateDate
        lastUsedOn
        usedLast12Mths
        fixedRateParts
        partsFixedRateDate
        partsFixedRateValue
        paytypegroup
      }
    }
  }
`;

export const GET_OPCODE_DETAILS_OLD = gql`
  query getOpcodeDetails($user_id: String) {
    statelessCcPhysicalRwGetRoOpcodesDetails(userid: $user_id) {
      nodes {
        menuSales
        opcategory
        opcodedescription
        opcode
        paytype
        slno
        gridExcluded
        categorized
        mpiItem
        maintenancePlan
        recentUpdatedFlag
        department
        fixedRate
        fixedRateValue
        fixedRateDate
        lastUsedOn
        usedLast12Mths
      }
    }
  }
`;

export const UPDATE_OPCODE = gql`
  mutation updateOpcodes($p_opcode: JSON, $store_id: String, $user_id: String) {
    statelessCcPhysicalRwUpdateRoopcodeMasterByOpcode(
      input: { pOpcode: $p_opcode, storeid: $store_id, userid: $user_id }
    ) {
      updateRoopcodeMasterStatuses {
        status
      }
    }
  }
`;

export const UPDATE_NONCPOPCODE = gql`
  mutation updateOpcodes($p_opcode: JSON, $store_id: String, $user_id: String) {
    statelessCcPhysicalRwUpdateRoopcodeMasterByOpcode(
      input: { pOpcode: $p_opcode, storeid: $store_id, userid: $user_id }
    ) {
      updateRoopcodeMasterStatuses {
        status
      }
    }
  }
`;

export const UPDATE_OPCODES = gql`
  mutation updateOpcodes($slno: BigInt!, $opcat: String, $store_id: String) {
    updateDmsPhysicalRwRoOpcode(
      input: { patch: { opcategory: $opcat, storeId: $store_id }, slno: $slno }
    ) {
      dmsPhysicalRwRoOpcode {
        slno
      }
    }
  }
`;

export const GET_RO_COUNT_FOR_TOTAL_REVENUE_SA = gql`
  query getROCountForTotalRevenue(
    $month_year: String
    $serviceadvisor: String
  ) {
    statelessCcDrillDownRoTotalRevenue(
      filter: { monthYear: { equalTo: $month_year } }
      serviceAdvisor: $serviceadvisor
    ) {
      nodes {
        ronumber
      }
      totalCount
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_RETURN_RATE_SA = gql`
  query getDrillDownDataForReturnRateSA(
    $month_year: String
    $serviceadvisor: [String!]
  ) {
    statelessCcDrilldownGetDrillDownReturnRateByServiceAdvisor(
      filter: {
        monthYear: { equalTo: $month_year }
        serviceadvisor: { in: $serviceadvisor }
      }
    ) {
      nodes {
        lastSixMonVinsRevInCurrMon
        lastTwelveMonVinsRevInCurrMon
        monthYear
        rodate
        serviceadvisor
        sixMonthReturnrate
        sixMonthReturnrateChart
        totalVinsInLastSixMonths
        twelveMonthReturnrate
        twelveMonthReturnrateChart
        totalVinsInLastTwelveMonths
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_TOTAL_VOLUME_PARTS = gql`
  mutation getWorkMixReportTotalVolumeParts(
    $table_for: String
    $view_for: String
  ) {
    statelessDbdPartsWorkmixGetWorkmixPartsOpcodesVolumeGrouping(
      input: { tableFor: $table_for, viewFor: $view_for }
    ) {
      statelessDbdPartsWorkmixWorkmixPartsOpcodesVolumeGroupings {
        opcategory
        opcode
        mon24
        mon25
        mon26
        mon27
        mon28
        mon29
        mon30
        mon31
        mon32
        mon33
        mon34
        mon35
        mon36
        opcodedescription
        tblcategory
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_TOTAL_VOLUME_PARTS_OLD = gql`
  query getWorkMixReportTotalVolumeParts(
    $table_for: String
    $view_for: String
  ) {
    statelessDbdPartsWorkmixGetWorkmixPartsOpcodesVolumeGrouping(
      tableFor: $table_for
      viewFor: $view_for
    ) {
      nodes {
        opcategory
        opcode
        mon1
        mon2
        mon3
        mon4
        mon5
        mon6
        mon7
        mon8
        mon9
        mon10
        mon11
        mon12
        mon13
        opcodedescription
        tblcategory
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_TECH_EFFICIENCY = gql`
  mutation getWorkMixReportTechEfficiency($view_for: String) {
    statelessDbdPeopleMetricsTechnicianGetTrendReportTechnicians(
      input: { viewFor: $view_for }
    ) {
      statelessDbdPeopleMetricsTechnicianTrendReportTechnicians {
        lbrtechno
        lbrtechname
        isActive
        mon24
        mon25
        mon26
        mon27
        mon28
        mon29
        mon30
        mon31
        mon32
        mon33
        mon34
        mon35
        mon36
      }
    }
  }
`;
export const GET_WORKMIX_REPORT_TECH_EFFICIENCY_OLD = gql`
  query getWorkMixReportTechEfficiency($view_for: String) {
    statelessDbdPeopleMetricsTechnicianGetTrendReportTechnicians(
      viewFor: $view_for
    ) {
      nodes {
        lbrtechno
        lbrtechname
        isActive
        mon1
        mon2
        mon3
        mon4
        mon5
        mon6
        mon7
        mon8
        mon9
        mon10
        mon11
        mon12
        mon13
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_TECHNICIAN_BY_TECH_NO = gql`
  mutation getDrillDownDataForTechnician(
    $month_year: [String!]
    $lbrtechno: [String!]
  ) {
    statelessCcDrilldownGetDrillDownTechnicianHrs(
      input: { monYear: $month_year, techno: $lbrtechno }
    ) {
      drillDownTechnicianHrs {
        actualhoursnonzeroflatratehrs
        actualhrszeroflatratehrs
        closeddate
        estimatedtechefficiency
        flatratehours
        flatratehrsnonzeroactualhrs
        flatratehrszeroactualhrs
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrsale
        lbrtechname
        lbrtechno
        monthYear
        nonzeroflatratehrsandnonzeroactualhrs
        nonzeroflatratehrsandzeroactualhrs
        prtssale
        storeId
        techefficiency
        techefficiencynonzero
        techhours
        totaljobs
        totalros
        zeroflatratehrsandnonzeroactualhrs
        zeroflatratehrsandzeroactualhrs
      }
    }
  }
`;

/*export const GET_DRILL_DOWN_DATA_FOR_TECHNICIAN_BY_TECH_NO = gql`
  query getDrillDownDataForTechnician(
    $month_year: [String!]
    $lbrtechno: String
  ) {
    statelessCcDrilldownGetDrillDownTechnicianHrs(
      filter: {
        lbrtechno: { equalTo: $lbrtechno }
        monthYear: { in: $month_year }
      }
    ) {
      nodes {
        closeddate
        flatratehours
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrtechno
        monthYear
        techefficiency
        techefficiencynonzero
        techhours
        totaljobs
        nonzeroflatratehrsandnonzeroactualhrs
        nonzeroflatratehrsandzeroactualhrs
        zeroflatratehrsandnonzeroactualhrs
        zeroflatratehrsandzeroactualhrs
        flatratehrszeroactualhrs
        flatratehrsnonzeroactualhrs
        actualhrszeroflatratehrs
        actualhoursnonzeroflatratehrs
        lbrsale
        prtssale
        estimatedtechefficiency
        lbrtechname
      }
    }
  }
`;
*/
export const GET_DRILL_DOWN_DATA_FOR_TECHNICIAN = gql`
  mutation getDrillDownDataForTechnician($month_year: [String!]) {
    statelessCcDrilldownGetDrillDownTechnicianHrs(
      input: { monYear: $month_year }
    ) {
      drillDownTechnicianHrs {
        actualhoursnonzeroflatratehrs
        actualhrszeroflatratehrs
        closeddate
        estimatedtechefficiency
        flatratehours
        flatratehrsnonzeroactualhrs
        flatratehrszeroactualhrs
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrsale
        lbrtechname
        lbrtechno
        monthYear
        nonzeroflatratehrsandnonzeroactualhrs
        nonzeroflatratehrsandzeroactualhrs
        prtssale
        storeId
        techefficiency
        techefficiencynonzero
        techhours
        totaljobs
        totalros
        zeroflatratehrsandnonzeroactualhrs
        zeroflatratehrsandzeroactualhrs
      }
    }
  }
`;
/*export const GET_DRILL_DOWN_DATA_FOR_TECHNICIAN = gql`
  query getDrillDownDataForTechnician($month_year: [String!]) {
    statelessCcDrilldownGetDrillDownTechnicianHrs(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        closeddate
        flatratehours
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrtechno
        monthYear
        techefficiency
        techefficiencynonzero
        techhours
        totaljobs
        nonzeroflatratehrsandnonzeroactualhrs
        nonzeroflatratehrsandzeroactualhrs
        zeroflatratehrsandnonzeroactualhrs
        zeroflatratehrsandzeroactualhrs
        flatratehrszeroactualhrs
        flatratehrsnonzeroactualhrs
        actualhrszeroflatratehrs
        actualhoursnonzeroflatratehrs
        lbrsale
        prtssale
        estimatedtechefficiency
        lbrtechname
      }
    }
  }
`;
*/
export const GET_WORKMIX_PERC_CALC_OPCAT = gql`
  query getWorkmixPercentageCalcOpcat {
    dbd_labor_workmix_vw_workmix_percentage_by_opcategory_by_month {
      closeddate
      competitiveelr
      competitivegrossprofitpercentage
      competitivejobcount
      competitivelbrsale
      competitivesoldhours
      competitivevolumepercentage
      competitiveworkmix
      maintenanceelr
      maintenancegrossprofitpercentage
      maintenancejobcount
      maintenancelbrsale
      maintenancesoldhours
      maintenancevolumepercentage
      maintenanceworkmix
      month_year
      opcategory
      repairelr
      repairgrossprofitpercentage
      repairjobcount
      repairlbrsale
      repairsoldhours
      repairvolumepercentage
      repairworkmix
      shopsupplieselr
      shopsuppliesgrossprofitpercentage
      shopsuppliesjobcount
      shopsupplieslbrsale
      shopsuppliesvolumepercentage
      shopsuppliessoldhours
      shopsuppliesworkmix
      totaljobcount
      totalsoldhours
    }
  }
`;

export const GET_ALL_FAVOURITE_CHARTS = gql`
  query getAllFavouriteCharts($username: String, $store_id: String) {
    statefulCcPhysicalRwChartFavourites(
      filter: {
        username: { equalTo: $username }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        favourites
        storeId
      }
    }
  }
`;

export const DELETE_ALL_FAVOURITE_CHARTS = gql`
  mutation deleteAllFavouriesCharts($username: String) {
    delete_dms_physical_rw_tbl_chart_favourites(
      where: { username: { _eq: $username } }
    ) {
      affected_rows
    }
  }
`;

export const REMOVE_CHART_FROM_FAVOURITE = gql`
  mutation removeChartFromFavourite($username: String, $favourites: String) {
    statelessCcPhysicalRwRemoveChartFromFavorites(
      input: { favorites: $favourites, userName: $username }
    ) {
      bigInt
    }
  }
`;

export const ADD_CHART_TO_FAVOURITE = gql`
  mutation addChartToFavourite($favourites: String, $username: String) {
    statelessCcPhysicalRwAddChartToFavorites(
      input: { favorites: $favourites, userName: $username }
    ) {
      bigInt
    }
  }
`;

export const GET_PAY_TYPE_ERRORS = gql`
  query getPayTypeErrors($store_id: String) {
    statelessCcPhysicalRwPayTypeCodeErrors(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        department
        payType
        payTypeCode
        storeId
      }
    }
  }
`;

// export const GET_PAY_TYPE_ERRORS = gql`
//   query getPayTypeErrors($store_id: String) {
//     statefulCcPhysicalRwPayTypeMasters(
//       condition: { payTypeCode: null }
//       filter: { storeId: { equalTo: $store_id } }
//     ) {
//       nodes {
//         department
//         payType
//         payTypeCode
//         storeId
//       }
//     }
//   }
// `;

export const GET_OPCODE_ERRORS = gql`
  mutation getAllOpcodeErrors {
    statelessCcPhysicalRwGetOpcodeCodeErrors(input: {}) {
      statelessCcPhysicalRwOpcodeCodeErrors {
        cType
        noncategorizedcount
      }
    }
  }
`;

export const GET_DRILL_DOWN_CALCULATION_DATA_SA_SUMMARY = gql`
  query getDrillDownCalculationDataSASummary(
    $month_year: [String!]
    $serviceadvisor: [String!]
  ) {
    statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrendCharts(
      filter: { monthYear: { in: $month_year } }
      advisor: $serviceadvisor
    ) {
      nodes {
        avgelr
        avgmarkup
        closeddate
        jobcount
        lbrcost
        lbrprftpercentage
        lbrprofit
        lbrsale
        lbrsoldhours
        monthYear
        prtprfpercentage
        prtscost
        prtsprofit
        prtssale
        rocount
        serviceadvisor
        totalrevenue
      }
    }
  }
`;
export const GET_DRILL_DOWN_FOR_AVERAGE_HOURS_SOLD_PER_TECH = gql`
  query getDrillDownForAverageHoursPerTech($month_year: String) {
    dmsDrilldownVwDrillDownAverageHoursPerTechnicians(
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      nodes {
        availablehrs
        closeddate
        monthYear
        soldhours
        techlist
      }
    }
  }
`;
export const GET_SPECIALMETRICS_1174 = gql`
  query get_SpecialMetric_1174($advisor: String, $month_year: [String!]) {
    dbdSpecialMetricsFnAverageSalePerTechnicianServiceAdvisor(
      filter: { monthYear: { in: $month_year } }
      advisor: $advisor
    ) {
      nodes {
        averagesalepertech
        lbrsale
        monthYear
        prtsale
        rodate
        techlist
      }
    }
  }
`;
export const GET_SPECIALMETRICS_1175 = gql`
  query get_SpecialMetric_1175($advisor: String, $month_year: [String!]) {
    dbdSpecialMetricsFnAverageSalePerTechnicianServiceAdvisor(
      filter: { monthYear: { in: $month_year } }
      advisor: $advisor
    ) {
      nodes {
        averagesalepertech
        lbrsale
        monthYear
        prtsale
        rodate
        techlist
      }
    }
  }
`;

export const GET_AVERAGE_HOURS_SOLD_PER_TECH_DATA = gql`
  query getAverageHoursSoldPerTechData($month_year: String) {
    dbd_special_metrics_vw_average_hours_per_technician(
      where: { month_year: { _eq: $month_year } }
    ) {
      averagehrspertech
      month_year
      rodate
      totalavailablehrs
      totalsoldhours
    }
  }
`;
export const GET_DRILL_DOWN_FOR_AVERAGE_HOURS_SOLD_PER_TECH_SA = gql`
  query getDrillDownForAverageHoursPerTech(
    $month_year: String
    $advisor: String
  ) {
    dmsDrilldownFnDrilldownAverageHoursPerTechnicianServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { equalTo: $month_year } }
    ) {
      nodes {
        averagehrspertech
        monthYear
        rodate
        totalavailablehrs
        totalsoldhours
      }
    }
  }
`;
export const GET_CP_OVERVIEW_ELR_PAYTYPE_DETAILS_HASURA = gql`
  query get_CP_Overview_ELR_Paytype_Details(
    $advisor: String
    $mon_year: [String!]
  ) {
    statelessDbdCpOverviewGetDetailElrPaytypeServiceAdvisor(
      advisor: $advisor
      filter: { monYear: { in: $mon_year } }
    ) {
      nodes {
        elrCombined
        elrCustomerpay
        elrExtendedservice
        elrFactoryservice
        elrInternal
        elrMaintenanceplan
        elrWarranty
        monYear
        rodate
      }
    }
  }
`;
export const GET_CP_LABOR_ELR_1127_PAYTYPE_DETAILS = gql`
  query get_CP_Labor_ELR_1127_Paytype_Details(
    $advisor: String
    $mon_year: [String!]
  ) {
    statelessDbdCpLaborGetDetailElrPaytypeServiceAdvisor(
      advisor: $advisor
      filter: { monYear: { in: $mon_year } }
    ) {
      nodes {
        elrCombined
        elrCustomerpay
        elrExtendedservice
        elrFactoryservice
        elrInternal
        elrMaintenanceplan
        elrWarranty
        monYear
        rodate
      }
    }
  }
`;
export const GET_CP_LABOR_RO_HOURS_PAYTYPE_DETAILS = gql`
  query get_CP_Labor_Ro_hours_Paytype_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    statelessDbdCpLaborGetDetailHoursPerRoByPaytypesServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        hoursPerRepairOrder
        hoursPerRepairOrderCustomerpay
        hoursPerRepairOrderExtendedservice
        hoursPerRepairOrderFactoryservice
        hoursPerRepairOrderInternal
        hoursPerRepairOrderMaintenance
        hoursPerRepairOrderWarranty
        monthYear
        rodate
      }
    }
  }
`;
export const GET_CP_LABORSALE_PER_RO_PAYTYPE_DETAILS = gql`
  query get_Labor_Sale_By_Paytype_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    statelessDbdCpLaborGetDetailAverageSaleByPaytypesServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        averagesalebypaytypes
        customerpay
        extendedservice
        factoryservice
        internal
        maintenance
        monthYear
        rodate
        warranty
      }
    }
  }
`;

export const GET_CP_PARTSRO_BY_PAYTYPE_DETAILS = gql`
  query get_PartsRO_By_Paytype_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    statelessDbdCpPartsGetPartsRevenuePerRoCountByPaytypeServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        monthYear
        partsRevenuePerRo
        partsRevenuePerRoCombined
        partsRevenuePerRoCustomerpay
        partsRevenuePerRoExtendedwarranty
        partsRevenuePerRoFactoryservice
        partsRevenuePerRoInternal
        partsRevenuePerRoMaintenance
        partsRevenuePerRoWarranty
        rodate
      }
    }
  }
`;

export const GET_LABOR_PARTS_DISCOUNT_BY_MONTH = gql`
  query getLaborAndPartsDiscountByMonth($month_year: [String!]) {
    statelessDbdDiscountsGetLaborAndPartsDiscountByMonthDetails(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        closeddate
        monthYear
        totaldiscount
        totallabordiscount
        totalpartsdiscount
      }
    }
  }
`;

export const GET_DISCOUNTED_SALE_PERECENTAGE = gql`
  query getDiscountedSalePercentage($month_year: [String!]) {
    statelessDbdDiscountsGetDiscountedSalePercentage(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        closeddate
        discountedlabor
        discountedparts
        partsdiscountdollars
        labordiscountdollars
        lbrsale
        monthYear
        partssale
        percentagepertotalcpsale
      }
    }
  }
`;

export const GET_DISCOUNTS_PER_TOTAL_CP_ROS = gql`
  query getDiscountsPerCPROs($month_year: [String!]) {
    statelessDbdDiscountsGetDiscountsPerRo(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        closeddate
        discountedlaborpercpro
        discountedlaborperdiscountedro
        discountedpartspercpro
        discountedpartsrperdiscountedro
        monthYear
        totaldiscountspercpro
        totaldiscountsperdiscountedcpro
      }
    }
  }
`;

export const GET_DISCOUNTS_PER_TOTAL_DISCOUNTED_CP_ROS = gql`
  query getDiscountsPerCPROs($month_year: [String!]) {
    statelessDbdDiscountsGetDiscountsPerRo(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        closeddate
        discountedlaborpercpro
        discountedlaborperdiscountedro
        discountedpartspercpro
        discountedpartsrperdiscountedro
        monthYear
        totaldiscountspercpro
        totaldiscountsperdiscountedcpro
      }
    }
  }
`;

export const GET_DISCOUNTED_CP_PER_TOTAL_DISCOUNTED_CP_ROS = gql`
  query getDiscountedCPPerTotalCPROs($month_year: [String!]) {
    statelessDbdDiscountsGetDiscountPercDiscountedPerTotalCpDiscountsDetails(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        closeddate
        discountedlabor
        discountedparts
        labordiscountdollars
        partsdiscountdollars
        discountpercentageperdiscountcpsale
        lbrsale
        monthYear
        prtssale
      }
    }
  }
`;
export const GET_TECHNICIAN_REVENUE_AND_HOURS = gql`
  mutation getTechEfficiencyRevenueHours(
    $mon1: String
    $mon2: String
    $view_for: String
  ) {
    statelessDbdPeopleMetricsTechnicianGetTechRevenueAndHoursByName(
      input: { mon1: $mon1, mon2: $mon2, viewFor: $view_for }
    ) {
      techRevenueAndHoursByNames {
        lbrtechno
        mon1
        mon2
        storeId
        teamId
        teamName
        techname
      }
    }
  }
`;
export const GET_MOST_FREQUENT_TECHNICIAN = gql`
  query getMostFrequentTechnician($store: String) {
    statelessDbdPeopleMetricsTechnicianGetMostFrequentTechnician(
      filter: { storeId: { equalTo: $store } }
      first: 1
    ) {
      nodes {
        count
        lbrtechno
        techname
      }
    }
  }
`;

export const GET_STORE_DETAILS = gql`
  query getStoreDetails($store_id: [String!]) {
    statelessCcPhysicalRoGetStoreMasters(
      filter: { storeId: { in: $store_id } }
    ) {
      nodes {
        storeId
        storeName
        storeDescription
        manufacturer
        website
        dms
        dmsImg
      }
    }
  }
`;

export const ALL_STORE_NAMES = gql`
  query getAllStoreNames($store_id: [String!]) {
    statelessCcPhysicalRoGetStoreMasterDetails(storeid: $store_id) {
      nodes {
        storeId
        storeName
        storeDescription
        manufacturer
        website
        dms
        sortOrder
        storeNickname
      }
    }
  }
`;
export const GET_STORE_NAMES_BY_STORE_ID = gql`
  query getStoreNamesByStoreId($storeId: [String!]) {
    statelessCcPhysicalRoGetStoreMasters(
      filter: { storeId: { in: $storeId } }
    ) {
      nodes {
        storeId
        storeName
      }
    }
  }
`;

export const GET_SHOP_SUPPLIES_DRILLDOWN = gql`
  mutation getShopSuppliesDrilldown($month_year: String) {
    statelessCcDrilldownGetDrillDownShopSupplies(
      input: { monYear: $month_year }
    ) {
      drillDownShopSupplies {
        closeddate
        customerpayshopsup
        internalshopsup
        lbrPaytype
        monthYear
        ronumber
        storeId
        warrantyshopsup
        serviceadvisor
        advisorName
      }
    }
  }
`;

export const REFRESH_VIEWS_DATA = gql`
  query updateRefreshViewsData($storeid: String, $user_id: String) {
    statelessCcPhysicalRwRetrunRefreshMviews(
      storeid: $storeid
      userId: $user_id
    ) {
      nodes {
        status
      }
    }
  }
`;
export const MV_REFRESH_STATUS = gql`
  mutation getMvRefreshStatus($reqSource: String, $storeid: String) {
    statelessCcPhysicalRwGetMvRefreshStatus(
      input: { reqSource: $reqSource, storeid: $storeid }
    ) {
      string
    }
  }
`;
export const REFRESH_VIEWS = gql`
  query updateRefreshViews {
    statelessCcPhysicalRwGetDataRefreshStatusLogs {
      nodes {
        status
        refreshEnd
        refreshStart
        refreshDate
      }
    }
  }
`;
export const REFRESH_VIEWS_STATUS = gql`
  query getRefreshViewsStatus {
    statelessCcPhysicalRwGetDataRefreshStatusLogs {
      nodes {
        refreshBy
        refreshEnd
        refreshStart
        refreshDate
        status
        storeId
      }
    }
  }
`;

export const REFRESH_STATUS_LOG = gql`
  query getRefreshStatus {
    statelessCcPhysicalRwGetPaytypeMasterUpdateLogStatuses {
      nodes {
        status
      }
    }
  }
`;

export const ADD_REFRESH_STATUS = gql`
  mutation addRereshStatus(
    $status: String!
    $refresh_by: String
    $store_id: String
  ) {
    createStatefulCcPhysicalRwDataRefreshStatusLog(
      input: {
        statefulCcPhysicalRwDataRefreshStatusLog: {
          status: $status
          refreshBy: $refresh_by
          storeId: $store_id
        }
      }
    ) {
      statefulCcPhysicalRwDataRefreshStatusLog {
        refreshDate
        refreshEnd
        status
        storeId
      }
    }
  }
`;
export const UPDATE_REFRESH_STATUS = gql`
  query updateRefreshStatus($status: String) {
    statelessCcPhysicalRwReturnUpdateMviewRefreshStatus(statusval: $status) {
      nodes {
        status
      }
    }
  }
`;
export const SEND_FEEDBACK = gql`
  mutation sendFeedback(
    $chkElse: Int
    $chkGeneral: Int
    $chkImprvIdea: Int
    $chkTechorfunc: Int
    $content: String
    $description: String
    $email: String
    $mobile: String!
    $name: String
    $lastName: String
  ) {
    statelessCcSupportAndNotificationsSetSupportAndFeedbackMailDetails(
      input: {
        chkElse: $chkElse
        chkGeneral: $chkGeneral
        chkImprvIdea: $chkImprvIdea
        chkTechorfunc: $chkTechorfunc
        content: $content
        description: $description
        email: $email
        mobile: $mobile
        name: $name
        lastname: $lastName
      }
    ) {
      clientMutationId
    }
  }
`;

export const GET_DAILY_DATA_LOAD = gql`
  mutation getDailyDataLoad(
    $storeid: String
    $timezoneOffset: String
    $fromdate: Date
    $todate: Date
  ) {
    statelessCcSupportAndNotificationsGetDailyDataloadStatusWithDaterange(
      input: {
        storeid: $storeid
        timezoneOffset: $timezoneOffset
        fromdate: $fromdate
        todate: $todate
      }
    ) {
      dailyDataloadStatuses {
        attempt
        attemptdate
        attempttime
        dashboardStatus
        enddate
        loaddate
        loadtime
        logError
        startdate
        status
        storeId
        storeName
      }
    }
  }
`;

export const GET_DAILY_DATA_LOAD_OLD = gql`
  mutation getDailyDataLoad(
    $storeid: String
    $timezoneOffset: String
    $fromdate: Date
    $todate: Date
  ) {
    statelessCcSupportAndNotificationsGetDailyDataloadStatusWithDaterange(
      input: {
        storeid: $storeid
        timezoneOffset: $timezoneOffset
        fromdate: $fromdate
        todate: $todate
      }
    ) {
      dailyDataloadStatuses {
        attempt
        attemptdate
        attempttime
        dashboardStatus
        enddate
        loaddate
        loadtime
        startdate
        status
        storeId
        storeName
      }
    }
  }
`;

export const GET_TOTAL_CUSTOMER_REVENUE_DETAILS_DRILL_DOWN = gql`
  mutation getDrillDownDataForTotalRevenueCustomerPay(
    $month_year: String
    $paytype: [String!]
  ) {
    statelessCcDrilldownGetDrillDownTotalRevenueDetails(
      input: { monthyear: $month_year, payType: $paytype }
    ) {
      statelessCcDrillDownTotalRevenueDetails {
        opendate
        closeddate
        elr
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        vin
        storeId
        advisorName
        newPaytypegroup
      }
    }
  }
`;

export const GET_PARTS_HOURS_PER_RO_BYYEAR = gql`
  query get_GET_PARTS_HOURS_PER_RO_BYYEAR($advisor: String) {
    statelessDbdCpPartsGetHoursPerRepairOrderByYearServiceAdvisor(
      advisor: $advisor
    ) {
      nodes {
        closeddate
        hoursPerRepairOrderCurrentyear
        hoursPerRepairOrderPastyear
        hoursPerRepairOrderPreviousyear
      }
    }
  }
`;
export const GET_CP_PARTS_RO_HOURS_PAYTYPE_DETAILS = gql`
  query get_CP_Parts_Ro_hours_Paytype_Details(
    $advisor: String
    $month_year: [String!]
  ) {
    statelessDbdCpPartsGetDetailHoursPerRepairOrderByPaytypesServiceAdvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        hoursPerRepairOrder
        hoursPerRepairOrderCustomerpay
        hoursPerRepairOrderExtendedservice
        hoursPerRepairOrderFactoryservice
        hoursPerRepairOrderInternal
        hoursPerRepairOrderMaintenance
        hoursPerRepairOrderWarranty
        monthYear
        rodate
      }
    }
  }
`;

export const GET_SEARCHBYRO_DETAILS = gql`
  mutation statelessDbdSearchRoGetUxRepairOrderDetails(
    $ronumberSelected: String
    $storeId: String
  ) {
    statelessDbdSearchRoGetUxRepairOrderDetails(
      input: { roNumber: $ronumberSelected, storeid: $storeId }
    ) {
      uxRepairOrderDetails {
        ronumber
        opendate
        closeddate
        monthYear
        serviceadvisor
        year
        make
        model
        vin
        custno
        address
        contactphonenumber
        deliverydate
        linlinecode
        prtlinecode
        prtdesc
        linservicerequest
        lbropcode
        lbrlinecode
        lbrsale
        lbrcost
        laborSoldHours
        laborActualHours
        prtpartno
        prtlist
        prtsource
        quantity
        prtunitcost
        prtunitsale
        prtextendedcost
        prtextendedsale
        prtpaytype
        prtpaytypegroup
        lbrsequenceno
        prtlaborsequenceno
        lbropcodedesc
        bookeddate
        lbrpaytype
        lbrpaytypegroup
        opcategory
        lincause
        linstorytext
        storeId
        customername
        advisorName
        mileage
        lbrtechname
        lbrtechno
        partsGpPercentage
        laborGpPercentage
        markup
        prtsequenceno
        advisorNickname
        techNickname
        storeName
        makeDesc
        lbrsequencenoIdx
      }
    }
  }
`;
export const GET_SEARCHBYRO_DETAILS_OLD = gql`
  mutation statelessDbdSearchRoGetUxRepairOrderDetails(
    $ronumberSelected: String
  ) {
    statelessDbdSearchRoGetUxRepairOrderDetails(
      input: { roNumber: $ronumberSelected }
    ) {
      uxRepairOrderDetails {
        ronumber
        opendate
        closeddate
        monthYear
        serviceadvisor
        year
        make
        model
        vin
        custno
        address
        contactphonenumber
        deliverydate
        linlinecode
        prtlinecode
        prtdesc
        linservicerequest
        lbropcode
        lbrlinecode
        lbrsale
        lbrcost
        laborSoldHours
        laborActualHours
        prtpartno
        prtlist
        prtsource
        quantity
        prtunitcost
        prtunitsale
        prtextendedcost
        prtextendedsale
        prtpaytype
        prtpaytypegroup
        lbrsequenceno
        prtlaborsequenceno
        lbropcodedesc
        bookeddate
        lbrpaytype
        lbrpaytypegroup
        opcategory
        lincause
        linstorytext
        storeId
        customername
        advisorName
        mileage
        lbrtechname
        lbrtechno
        partsGpPercentage
        laborGpPercentage
        markup
        prtsequenceno
        advisorNickname
        techNickname
      }
    }
  }
`;

export const GET_SEARCHBYRO_XML = gql`
  query cdkDataDetails($ronumberSelected: String, $store_id: String) {
    statefulCdkSourceRawCdkDataDetails(
      filter: {
        roNumber: { equalTo: $ronumberSelected }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        id
        roDataXml
        roNumber
      }
    }
  }
`;

export const GET_SEARCHBYRO_JSON = gql`
  query dmsSourceRawDtkDataDetailS(
    $ronumberSelected: String
    $store_id: String
  ) {
    dmsSourceRawDtkDataDetailS(
      filter: {
        roNumber: { equalTo: $ronumberSelected }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        id
        roDataXml
        roNumber
        roDataJson
      }
    }
  }
`;

export const GET_DATA_FOR_TOTAL_PRICING_OPPORTUNITY = gql`
  query getDataForTotalPricingOpportunity(
    $month_year: [Date!]
    $store_id: String
  ) {
    dbdCpOpportunityElrVwChartsTotalPricingOpportunities(
      filter: { rodate: { in: $month_year }, storeId: { equalTo: $store_id } }
    ) {
      nodes {
        rodate
        total
        storeId
      }
    }
  }
`;

export const GET_TRANCHE_REPORT_FOR_LABOR_RATE_ANALYSIS = gql`
  query getTrancheReporForLaborRateAnalysis($store_id: String) {
    statelessDbdCpLaborRatesGetElrMovingTrancheReport(storeid: $store_id) {
      nodes {
        ronumber
        opendate
        closeddate
        paytype
        paytypegroup
        lbropcode
        lbropcodedesc
        opcategory
        lbrsale
        lbrsoldhours
        elr
      }
    }
  }
`;

export const GET_TRANCHE_REPORT_FOR_INCLUDED_OPCODES = gql`
  query getTrancheReportForIncludedOpcodes {
    statelessDbdCpLaborRatesGetTrancheReportIncludedOpCodes {
      nodes {
        lbropcode
        lbropcodedesc
        opcategory
        laborsale
        frh
        elr
        analysisElr
        varianceElr
        reveImpact
        monthlyImpact
        annualImpact
      }
    }
  }
`;

export const GET_TRANCHE_REPORT_FOR_EXCLUDED_OPCODES = gql`
  query getTrancheReportForExcludedOpcodes {
    statelessDbdCpLaborRatesGetTrancheReportExcludedOpCodes {
      nodes {
        lbropcode
        lbropcodedesc
        opcategory
        laborsale
        frh
        elr
      }
    }
  }
`;

export const GET_ELR_WARRANTY_PAY_TYPES = gql`
  query getElrWarrantyPaytypes {
    statelessDbdCpLaborRatesGetElrWarrantyPaytype {
      nodes {
        lbropcode
        lbropcodedesc
        opcategory
        laborsale
        frh
        elr
        analysisElr
        varianceElr
        sixMonthImpact
        monthlyImpact
        annualImpact
        storeId
      }
    }
  }
`;

export const GET_TOP_FIVE_OPPORTUNITIES = gql`
  query getTopFiveOpportunities {
    statelessDbdCpLaborRatesGetTopFiveOpcodeDetail {
      nodes {
        lbropcode
      }
    }
  }
`;

export const GET_SEARCHBYRO_JSON_FERRARIO = gql`
  query atmDataDetails($ronumberSelected: String, $store_id: String) {
    atmDataDetails(
      filter: {
        roNumber: { equalTo: $ronumberSelected }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        id
        roDataXml
        roNumber
        roDataJson
      }
    }
  }
`;

export const GET_LATEST_CLOSED_DATE = gql`
  query getLatestDate($store_id: String) {
    statelessCcPhysicalRwGetConfigurationValues(
      condition: { shortKey: "CLD", partyId: $store_id }
    ) {
      nodes {
        value
        name
      }
    }
  }
`;
export const GET_LATEST_OPEN_DATE = gql`
  query getLatestDate($store_id: String) {
    statelessCcPhysicalRwGetConfigurationValues(
      condition: { shortKey: "SD", partyId: $store_id }
    ) {
      nodes {
        value
        name
      }
    }
  }
`;

export const GET_LAST_THIRTEEN_MONTHS = gql`
  query getlastThirteenMonths {
    statelessCcAggregateGetLastThirteenMonths(orderBy: MONTH_YEAR_ASC) {
      nodes {
        monthYear
      }
    }
  }
`;

export const GET_SEARCHBYRO_XML_RENOLDS = gql`
  query dmsSourceRawRldsDataDetails(
    $ronumberSelected: String
    $store_id: String
  ) {
    statefulRldsSourceRawRldsDataDetails(
      filter: {
        roNumber: { equalTo: $ronumberSelected }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        id
        closeddate
        clientId
        createdDate
        modifiedDate
        roDataJson
        roDataXml
        roNumber
        storeId
      }
    }
  }
`;

export const GET_VALID_MAKES = gql`
  query getValidMakes($store_id: String) {
    statelessCcPhysicalRwGetValidMake(inStoredId: $store_id)
  }
`;

export const GET_INCLUDED_MAKES_DETAILS = gql`
  query getIncludedMakesDetails($store_id: String) {
    statelessCcPhysicalRwGetIncludedMakes(inStoreId: $store_id) {
      nodes {
        storeId
        makesId
        storeName
        manufacturer
        validMakes
      }
    }
  }
`;

export const GET_EXCLUDED_MAKES_DETAILS = gql`
  query getExcludedMakesDetails($store_id: String) {
    statelessCcPhysicalRwGetExcludedMakes(inStoreId: $store_id) {
      nodes {
        makesId
        manufacturer
        excludedMakes
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_PARTS_MARKUP_DETAILS = gql`
  mutation getDrillDownDataForPartsMarkup(
    $month_year: String
    $payType: [String!]
  ) {
    statelessCcDrilldownGetDrillDownPartsRevenueDetails(
      input: { monthyear: $month_year, payType: $payType }
    ) {
      statelessCcDrilldownDrillDownPartsRevenueDetails {
        ronumber
        monthYear
        opendate
        closeddate
        vin
        linlinecode
        lbrlinecode
        lbrsequenceno
        laborpaytype
        laborpaytypegroup
        lbropcode
        opcategory
        opsubcategory
        lbropcodedesc
        prtsequenceno
        prtlinecode
        prtlaborsequenceno
        partspaytype
        partspaytypegroup
        prtsale
        prtcost
        prtqtysold
        prtextendedsale
        prtextendedcost
        prtdesc
        filterByRevenue
        markup
        lincause
        linstorytext
        prtsgrossprofit
        prtsGrossprofitpercentage
        storeId
        serviceadvisor
        advisorName
      }
    }
  }
`;

export const GET_QUALIFIED_MAPPER_OPCODES = gql`
  query getQualifiedMapperOpcodes {
    dbdReferencesGetFixedOpsAndMapperQualifiedOpcodes {
      nodes {
        lbropcode
        opcategory
        opdesc
        count
        lbrsale
        lbrsoldhours
        mapperDesc
        finalExcluded
        finalReason
      }
    }
  }
`;

export const GET_NON_QUALIFIED_MAPPER_OPCODES = gql`
  query getNonQualifiedMapperOpcodes {
    dbdReferencesGetFixedOpsAndMapperNonQualifiedOpcodes {
      nodes {
        lbropcode
        opcategory
        opdesc
        count
        lbrsale
        lbrsoldhours
        mapperDesc
        finalExcluded
        finalReason
      }
    }
  }
`;

export const GET_FIXEDOPS_VS_MAPPER_OPCATEGORIZATION = gql`
  query getFixedopsVsMapperOpCategorization($store_id: String) {
    statelessDbdReferencesGetFixedopsVsMapperOpCategorization(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        lbropcode
        opcategory
        opdesc
        count
        lbrsale
        lbrsoldhours
        mapperDesc
        excluded
        reason
        storeId
      }
    }
  }
`;
export const GET_MENU_OPCODE_DETAILS = gql`
  query getMenuOpcodeDetails($store_id: String) {
    statelessCcPhysicalRwGetMenuOpcodesDetails(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        opcode
        opcodeDescription
        slno
        storeId
      }
    }
  }
`;
export const GET_MPI_OPCODE_DETAILS = gql`
  query getMpiOpcodeDetails($store_id: String) {
    statelessCcPhysicalRwGetMpiOpcodesDetails(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        opcode
        opcodeDescription
        slno
        storeId
      }
    }
  }
`;
export const GET_MPI_PENETRATION_OPCODE_DETAILS = gql`
  query getMpiPenetrationOpcode($store_id: String) {
    statelessDbdReferencesGetFixedopsVsMapperOpCategorization(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        lbropcode
        opcategory
        opdesc
        count
        lbrsale
        lbrsoldhours
        mapperDesc
        excluded
        reason
        storeId
      }
    }
  }
`;
export const GET_SHARED_SEQUENCES_BY_MAKE_OVERALL = gql`
  query getSharedSequencesByMakeOverall($store_id: String) {
    statelessDbdReferencesGetMakeCountPercentage(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        rocount
        totalRocount
        make
        percentage
        storeId
      }
    }
  }
`;
export const GET_SHARED_SEQUENCES_BY_MAKE_MONTHLY = gql`
  query getSharedSequencesByMakeMonthly($store_id: String) {
    statelessDbdReferencesGetMakeCountPercentageMonthly(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        rocount
        monthYear
        make
        totalRocount
        percentage
        storeId
      }
    }
  }
`;
export const GET_NEW_CAR_WARRANTY_OVERALL = gql`
  query getNewCarWarrantyOverall($store_id: String) {
    statelessDbdReferencesGetWarrantyCountPercentage(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        rocount
        totalRocount
        paytypegroup
        percentage
        storeId
        make
      }
    }
  }
`;
export const GET_NEW_CAR_WARRANTY_MONTHLY = gql`
  query getNewCarWarrantyMonthly {
    statelessDbdReferencesGetWarrantyCountPercentageMonthly {
      nodes {
        rocount
        totalCount
        monthYear
        paytypegroup
        percentage
        storeId
        make
      }
    }
  }
`;

export const GET_NEW_CAR_WARRANTY_SIX_MONTHS = gql`
  query getNewCarWarrantySixMonths {
    statelessDbdReferencesGetWarrantyCountPercentageSixMonth {
      nodes {
        rocount
        paytypegroup
        percentage
        storeId
        make
        totalCount
      }
    }
  }
`;

export const GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR = gql`
  query getRevenueSummaryWarrantyVolumesLabor(
    $currMonth: String
    $optMonth: String
    $storeId: String
  ) {
    statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesLaborByMonth(
      currMonth: $currMonth
      optMonth: $optMonth
      currStoreId: $storeId
    ) {
      nodes {
        storeId
        selSale
        selJobcount
        selHours
        selCost
        optSelSale
        optSelJobCount
        optSelHours
        optSelCost
        optActualHours
        id
        heading
        actualHours
        elr
        optElr
      }
    }
  }
`;
export const GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS = gql`
  query getRevenueSummaryWarrantyVolumesParts(
    $currMonth: String
    $optMonth: String
    $storeId: String
  ) {
    statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesPartsByMonth(
      currMonth: $currMonth
      optMonth: $optMonth
      currStoreId: $storeId
    ) {
      nodes {
        storeId
        selSale
        selJobcount
        selHours
        selCost
        optSelSale
        optSelJobCount
        optSelHours
        optSelCost
        optActualHours
        id
        heading
        actualHours
        optMarkup
        markup
      }
    }
  }
`;
export const GET_USER_LOGIN_HISTORY_STAT = gql`
  query GetUserLoginHistoryStat($statusdate: Date) {
    statelessCcSupportAndNotificationsGetUserLoginHistoryStatus(
      statusdate: $statusdate
    ) {
      nodes {
        storeId
        selSale
        selJobcount
        selHours
        selCost
        optSelSale
        optSelJobCount
        optSelHours
        optSelCost
        optActualHours
        id
        heading
        actualHours
        optMarkup
        markup
      }
    }
  }
`;
export const GET_MONTH_YEARS = gql`
  query getMonthYears {
    statelessCcDrilldownGetMonthYears {
      nodes {
        monthYear
      }
    }
  }
`;

export const GET_DRILLDOWN_MONTHYEARS = gql`
  query getdmsDrilldownVwMonthYears {
    statelessCcDrilldownGetMonthYears {
      nodes {
        monthYear
      }
    }
  }
`;

export const GET_NOTIFICATIONS_USER_LOGIN_HISTORY_STATUS = gql`
  mutation getNotificationsUserLoginHistoryStatus(
    $enddate: Date
    $startdate: Date
    $storeid: String
    $realmid: String
    $timezoneOffset: String
  ) {
    statelessCcSupportAndNotificationsGetUserLoginHistoryStatusDaterange(
      input: {
        timezoneOffset: $timezoneOffset
        enddate: $enddate
        startdate: $startdate
        storeid: $storeid
        realmid: $realmid
      }
    ) {
      statelessCcSupportAndNotificationsUserLoginHistoryStatuses {
        userName
        storeName
        roleName
        storeId
        logouttime
        logoutdate
        logindate
        logintime
        loginDuration
      }
    }
  }
`;
export const GET_DAILY_DATA_LOAD_STATUS = gql`
  query GetDailyDataloadStatus(
    $statusdate: Date
    $fromdate: Date
    $storeid: String
    $timezoneOffset: String
  ) {
    statelessCcSupportAndNotificationsGetDailyDataloadStatus(
      timezoneOffset: $timezoneOffset
      statusdate: $statusdate
      fromdate: $fromdate
      storeid: $storeid
    ) {
      nodes {
        dashboardStatus
        enddate
        loaddate
        loadtime
        status
        startdate
        storeId
        storeName
      }
    }
  }
`;

export const GET_WARRANTY_RATES_PARTS = gql`
  query getWarrantyReimbursementsGetAnalysisWarrantyVolumesParts(
    $endDate: Date
    $startDate: Date
    $store_id: String
  ) {
    statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumesPartsDaterange(
      filter: { storeId: { equalTo: $store_id } }
      enddate: $endDate
      startdate: $startDate
    ) {
      nodes {
        brand

        opendate
        closeddate
        lbrlinecode
        lbropcode
        linstorytext
        lincause
        lbropcodedesc
        markup
        monthYear
        opcategory
        opsubcategory
        partspaytype
        partspaytypegroup
        prtdesc
        prtextendedcost
        prtextendedsale
        ronumber
        storeId
        warrantypaytype
      }
    }
  }
`;

export const GET_WARRANTY_REFERENCE_LABOR = gql`
  query getdmsPhysicalRwWarrantyLaborRateModelS($store_id: String) {
    statefulCcPhysicalRwWarrantyLaborRateModels(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        id
        maxDate
        maxWarranrtyRate
        minDate
        minWarrantyRate
        paytype
        storeId
        warrantyRate
      }
    }
  }
`;

export const GET_WARRANTY_REFERENCE_PARTS = gql`
  query getdmsPhysicalRwWarrantyPartsMarkupModelS($store_id: String) {
    statefulCcPhysicalRwWarrantyPartsMarkupModelOmits(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        id
        maxDate
        maxMarkup
        minDate
        minMarkup
        paytype
        storeId
        warrantyMarkup
      }
    }
  }
`;

export const UPDATE_WARRANTY_REFERENCE_LABOR = gql`
  mutation updateWarrantyReferenceLabor(
    $mindate: String
    $maxdate: String
    $minwarrantyrate: BigFloat
    $maxwarrantyrate: BigFloat
    $payType: String
    $storeid: String
  ) {
    statelessCcPhysicalRwUpdateWarrantyLaborRateModel(
      input: {
        maxdate: $maxdate
        mindate: $mindate
        maxwarrantyrate: $maxwarrantyrate
        minwarrantyrate: $minwarrantyrate
        payType: $payType
        storeid: $storeid
      }
    ) {
      statelessCcPhysicalRwGetUpdateWarrantyLaborRateModelStatuses {
        id
        maxDate
        maxWarranrtyRate
        minDate
        minWarrantyRate
        paytype
        storeId
        warrantyRate
      }
    }
  }
`;

export const UPDATE_WARRANTY_REFERENCE_PARTS = gql`
  mutation updateWarrantyReferenceParts(
    $mindate: String
    $maxdate: String
    $minmarkup: BigFloat
    $maxmarkup: BigFloat
    $payType: String
    $storeid: String
  ) {
    statelessCcPhysicalRwUpdateWarrantyPartsMarkupModel(
      input: {
        maxdate: $maxdate
        mindate: $mindate
        maxmarkup: $maxmarkup
        minmarkup: $minmarkup
        payType: $payType
        storeid: $storeid
      }
    ) {
      statelessCcPhysicalRwGetUpdateWarrantyPartsMarkupModelStatuses {
        id
        maxDate
        maxMarkup
        minDate
        paytype
        minMarkup
        warrantyMarkup
      }
    }
  }
`;

export const GET_WARRANTY_RATES_LABOR_DATERANGE = gql`
  query getWarrantyReimbursementsGetAnalysisWarrantyVolumesDaterange(
    $startdate: Date
    $enddate: Date
    $store_id: String
  ) {
    statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumesDaterange(
      filter: { storeId: { equalTo: $store_id } }
      startdate: $startdate
      enddate: $enddate
    ) {
      nodes {
        brand
        closeddate
        lbrcost
        lbrlinecode
        lbropcodedesc
        lbropcode
        lbrsale
        lbrsoldhours
        monthYear
        opcategory
        opendate
        opsubcategory
        paytype
        paytypegroup
        ronumber
        storeId
        warrantypaytype
        warrantyrate
      }
    }
  }
`;

export const GET_WARRANTY_RATES_LABOR = gql`
  query getWarrantyReimbursementsGetAnalysisWarrantyVolumes(
    $month_year: String
    $store_id: String
  ) {
    statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumes(
      filter: {
        monthYear: { equalTo: $month_year }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        opendate
        closeddate
        lbrcost
        lbrlinecode
        lbropcode
        lbrsoldhours
        lbropcodedesc
        lbrsale
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        ronumber
        warrantypaytype
        warrantyrate
        storeId
      }
    }
  }
`;

export const CLIENT_AUDITS = gql`
  mutation TblClientaudits(
    $dmlaction: String
    $newdata: String
    $olddata: String
    $schemaname: String
    $storeId: String
    $tablename: String
    $username: String
    $userrole: String
  ) {
    statelessCcAuditInsertClientAudit(
      input: {
        dmlaction: $dmlaction
        newdata: $newdata
        olddata: $olddata
        schemaname: $schemaname
        storeid: $storeId
        tablename: $tablename
        username: $username
        userrole: $userrole
      }
    ) {
      bigInt
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_ALL_REVENUE = gql`
  mutation getDrillDownDataForAllRevenue($month_year: String) {
    statelessCcDrillDownGetDrillDownAllRevenueDetails(
      input: { monthyear: $month_year }
    ) {
      statelessCcDrillDownDrillDownAllRevenueDetails {
        opendate
        storeId
        ronumber
        openMonth
        opendate
        monthYear
        closeddate
        vin
        lbrlinecode
        lbrsequenceno
        paytype
        paytypegroup
        lbropcode
        opcategory
        opsubcategory
        lbropcodedesc
        lbrsale
        lbrcost
        lbrsoldhours
        lbractualhours
        filterByRevenue
        prtextendedsale
        prtextendedcost
        filterByLaborparts
        linaddonflag
        serviceadvisor
        lbrtechno
        lbrtechhours
        lbrgrossprofit
        prtsgrossprofit
        elr
        markup
        prtsGrossprofitpercentage
        lbrGrossprofitpercentage
        department
        lbrsequencenoIdx
        make
        advisorName
      }
    }
  }
`;
export const GET_LABOR_TOTAL_OPPORTUNITY = gql`
  query getLaborTotalOpportunity {
    statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunity {
      nodes {
        totalLbrgrossOpportunity
        totalLbrvolumeOpportunity
        totalLbrjointOpportunity
        totalLbropportunity
        storeId
      }
    }
  }
`;
export const OPPORTUNITY_MW_REFRESH = gql`
  mutation getMWOpportunity($groupname: String) {
    statelessCcPhysicalRwOpportunityRefreshMatView(
      input: { groupname: $groupname }
    ) {
      bigInt
    }
  }
`;
export const GET_PARTS_TOTAL_OPPORTUNITY = gql`
  query getPartsTotalOpportunity {
    dbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunity {
      nodes {
        totalPrtgrossOpportunity
        totalPrtvolumeOpportunity
        totalPrtjointOpportunity
        totalPrtsopportunity
        storeId
      }
    }
  }
`;
export const GET_ELR_TOTAL_OPPORTUNITY = gql`
  query getELRTotalOpportunity {
    statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByCategory {
      nodes {
        totalCompetitiveOpportunity
        totalMaintenanceOpportunity
        totalRepairOpportunity
        totalOpportunity
        storeId
      }
    }
  }
`;

export const UPDATE_TECHNICIAN_STATUS = gql`
  mutation updateTechnicianStatus(
    $techno: String
    $pCategorized: Int
    $statusval: BitString
    $nickName: String
    $user_id: String
    $department: String
  ) {
    statelessPhysicalRwUpdateTechnicianStatus(
      input: {
        techno: $techno
        statusval: $statusval
        pCategorized: $pCategorized
        nickName: $nickName
        userId: $user_id
        pDepartment: $department
      }
    ) {
      clientMutationId
    }
  }
`;
export const GET_ALL_DUPLICATE_DASHBOARDS = gql`
  query getAllDuplicateDashboards($chartId: BigInt) {
    dmsPhysicalRwGetDashboardNames(chartid: $chartId) {
      nodes {
        dashboardName
      }
    }
  }
`;
export const GET_CHART_DETAILS = gql`
  query getChartDetails($chartId: BigInt) {
    statelessCcPhysicalRwGetChartMasters(condition: { chartId: $chartId }) {
      nodes {
        markdown
        chartName
      }
    }
  }
`;

export const UPDATE_CHART_MASTER_MARKDOWN = gql`
  mutation updateChartMasterMarkdown(
    $chart_id: BigInt
    $inUpdatedOnRealm: String
    $markdown: JSON
  ) {
    statelessCcPhysicalRwUpdateChartMasterMarkdown(
      input: {
        chartid: $chart_id
        inUpdatedOnRealm: $inUpdatedOnRealm
        markDown: $markdown
      }
    ) {
      bigInt
    }
  }
`;
export const UPDATE_PAYTYPE_RETAIL_FLAG = gql`
  mutation statelessCcPhysicalRwUpdatePaytypeRetailFlag(
    $storeIdParam: String!
    $paytypeRetailFlag: JSON!
  ) {
    statelessCcPhysicalRwUpdatePaytypeRetailFlag(
      input: {
        storeIdParam: $storeIdParam
        paytypeRetailFlag: $paytypeRetailFlag
      }
    ) {
      updatePaytypeMasterStatuses {
        status
      }
    }
  }
`;
export const GET_LABOR_OPPORTUNITY_BASELINE = gql`
  query getLaborTotalOpportunityBaseline($timeZone: String) {
    statelessDbdCpOpportunityLaborGetLaborOpportunityBaseline(
      timezoneOffset: $timeZone
    ) {
      nodes {
        lastQtrGpPercentage
        lastQtrHrsPerRo
        storeId
      }
    }
  }
`;
export const GET_PARTS_OPPORTUNITY_BASELINE = gql`
  query getpartsTotalOpportunityBaseline($timeZone: String) {
    statelessDbdCpOpportunityPartsGetPartsOpportunityBaseline(
      timezoneOffset: $timeZone
    ) {
      nodes {
        lastQtrGpPercentage
        lastQtrHrsPerRo
        storeId
      }
    }
  }
`;

export const GET_ELR_OPPORTUNITY_BASELINE = gql`
  query getELROpportunityBaseline($timeZone: String) {
    statelessDbdCpOpportunityElrGetElrOpportunityBaseline(
      timezoneOffset: $timeZone
    ) {
      nodes {
        lastQtrCompElr
        lastQtrMaintElr
        lastQtrRepairElr
        storeId
      }
    }
  }
`;

export const GET_KPI_TOGGLE_OPTIONS = gql`
  query getKpiToggleOptions {
    statelessCcPhysicalRoGetToggleDurations {
      nodes {
        today
        yesterday
        lastweekstartdate
        lastweekenddate
        mtdstartdate
        mtdenddate
        lastmonthstartdate
        lastmonthenddate
        ytdstartdate
        ytdenddate
        lastyearstartdate
        lastyearenddate
        lastquarterstartdate
        lastquarterenddate
        lastthirtydaystartdate
        lastthirtydayenddate
        lastninetydaystartdate
        lastninetydayenddate
        lastsixmonthstartdate
        lastsixmonthenddate
        dayBeforeYesterday
      }
    }
  }
`;
export const GET_DATA_FOR_CWITOTAL_CHARTS = gql`
  mutation getDataForCWITotalcharts($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardCwitotal(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardCwitotals {
        ctotal
        wtotal
        itotal
        ttotal
      }
    }
  }
`;
export const GET_DATA_FOR_CWITOTAL_ADVISOR_CHARTS = gql`
  mutation getDataForCWITotalAdvisorcharts(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardCwitotal(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardCwitotals {
        ctotal
        wtotal
        itotal
        ttotal
      }
    }
  }
`;
export const GET_DATA_FOR_ROSHARE_CHART = gql`
  mutation getDataForROSharechart($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardRoshare(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardRoshares {
        totalro
        totalshare
        workingdays
        totalvalue
        totalroDuration
        totalshareDuration
        totalvalueDuration
      }
    }
  }
`;
export const GET_DATA_FOR_ROSHARE_ADVISOR_CHART = gql`
  mutation getDataForROShareAdvisorchart(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardRoshare(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardRoshares {
        totalro
        totalshare
        workingdays
        totalvalue
        totalroDuration
        totalshareDuration
        totalvalueDuration
      }
    }
  }
`;

export const GET_DATA_FOR_KPI_LINERO_LT_SIXTY_K = gql`
  mutation getDataForKPILineRoLtSixtyk(
    $toggleOption: String
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardLineroLtSixtyk(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardLineros {
        totalRos
        onelineRos
        multilineRos
        onelinePercentage
        multilinePercentage
        onelineLbrsale
        onelinePrtsale
        onelineTotalsale
        multilineLbrsale
        multilinePrtsale
        multilineTotalsale
        multilineJobcount
      }
    }
  }
`;

export const GET_DATA_FOR_KPI_LINERO_ADVISOR_LT_SIXTY_K = gql`
  mutation getDataForKPILineRoAdvisorLtSixtyk(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardLineroLtSixtyk(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardLineros {
        totalRos
        onelineRos
        multilineRos
        onelinePercentage
        multilinePercentage
        onelineLbrsale
        onelinePrtsale
        onelineTotalsale
        multilineLbrsale
        multilinePrtsale
        multilineTotalsale
        multilineJobcount
      }
    }
  }
`;

export const GET_DATA_FOR_KPI_LINERO_GT_SIXTY_K = gql`
  mutation getDataForKPILineRoGtSixtyk(
    $toggleOption: String
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardLineroGtSixtyk(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardLineros {
        totalRos
        onelineRos
        multilineRos
        onelinePercentage
        multilinePercentage
        onelineLbrsale
        onelinePrtsale
        onelineTotalsale
        multilineLbrsale
        multilinePrtsale
        multilineTotalsale
        multilineJobcount
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_LINERO_ADVISOR_GT_SIXTY_K = gql`
  mutation getDataForKPILineRoAdvisorGtSixtyk(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardLineroGtSixtyk(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardLineros {
        totalRos
        onelineRos
        multilineRos
        onelinePercentage
        multilinePercentage
        onelineLbrsale
        onelinePrtsale
        onelineTotalsale
        multilineLbrsale
        multilinePrtsale
        multilineTotalsale
        multilineJobcount
      }
    }
  }
`;

export const GET_DATA_FOR_KPI_AVG_AGE_MILES = gql`
  mutation getDataForKPIAvgAgeMiles($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardAvgAgeMiles(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardAvgAgeMiles {
        avgYear
        avgMileage
        noOfRecords
        totalYear
        totalMileage
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_AVG_AGE_MILES_ADVISOR = gql`
  mutation getDataForKPIAvgAgeMilesAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardAvgAgeMiles(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardAvgAgeMiles {
        avgYear
        avgMileage
        noOfRecords
        totalYear
        totalMileage
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_FLAT_RATE_HRS = gql`
  mutation getDataForKPIFlatRateHrs($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardFlatratehrs(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardFlatratehrs {
        totalsoldhours
        avgsoldhoursperdays
        avgsoldhoursperro
        totaldays
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_FLAT_RATE_HRS_ADVISOR = gql`
  mutation getDataForKPIFlatRateHrsAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardFlatratehrs(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardFlatratehrs {
        totalsoldhours
        avgsoldhoursperdays
        avgsoldhoursperro
        totaldays
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_LABOR_GP_RO = gql`
  mutation getDataForKPILaborGpRo($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardLaborGpRo(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardLaborGpRos {
        salesperro
        gpperro
        gpperc
        totalsales
        gp
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_LABOR_GP_RO_ADVISOR = gql`
  mutation getDataForKPILaborGpRoAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardLaborGpRo(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardLaborGpRos {
        salesperro
        gpperro
        gpperc
        totalsales
        gp
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_PARTS_GP_RO = gql`
  mutation getDataForKPIPartsGpRo($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardPartsGpRo(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsGpRos {
        salesperro
        gpperro
        gpperc
        totalsales
        gp
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_PARTS_GP_RO_ADVISOR = gql`
  mutation getDataForKPIPartsGpRoAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsGpRo(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsGpRos {
        salesperro
        gpperro
        gpperc
        totalsales
        gp
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_TOTAL_GP_RO = gql`
  mutation getDataForKPITotalGpRo($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardTotalGpRo(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardTotGpRos {
        salesperro
        gpperro
        totalsales
        gp
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_TOTAL_GP_RO_ADVISOR = gql`
  mutation getDataForKPITotalGpRoAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardTotalGpRo(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardTotGpRos {
        salesperro
        gpperro
        totalsales
        gp
        totalros
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_WORK_MIX = gql`
  mutation getDataForKPIWorkmix($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardWorkmix(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardWorkmixes {
        competitiveperc
        maintenanceperc
        repairperc
        competitive
        maintenance
        repair
        totalMaintanaceRepairCompetitive
        totalMaintanaceRepairCompetitivePerc
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_WORK_MIX_ADVISOR = gql`
  mutation getDataForKPIWorkmixAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardWorkmix(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardWorkmixes {
        competitiveperc
        maintenanceperc
        repairperc
        competitive
        maintenance
        repair
        totalMaintanaceRepairCompetitive
        totalMaintanaceRepairCompetitivePerc
        startdate
        enddate
      }
    }
  }
`;

export const GET_DATA_FOR_KPI_LABOR_GRID = gql`
  mutation getDataForKPILaborGrid(
    $toggleOption: String
    $timeZone: String
    $payType: String
    $gridtype: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddata(
      input: {
        filterby: $toggleOption
        timezoneOffset: $timeZone
        payType: $payType
        gridtype: $gridtype
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddata {
        totalcount
        totalfalse
        totaltrue
        noncompliance
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_LABOR_GRID_ADVISOR = gql`
  mutation getDataForKPILaborGridAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
    $payType: String
    $gridtype: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddata(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        timezoneOffset: $timeZone
        payType: $payType
        gridtype: $gridtype
        technician: $technician
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddata {
        totalcount
        totalfalse
        totaltrue
        noncompliance
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_LINE_RO = gql`
  mutation getDataForKPILineRO($toggleOption: String, $timeZone: String) {
    statelessDbdKpiScorecardGetKpiScorecardLinero(
      input: { filterby: $toggleOption, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardLineros {
        totalRos
        onelineRos
        multilineRos
        onelinePercentage
        multilinePercentage
        onelineLbrsale
        onelinePrtsale
        onelineTotalsale
        multilineLbrsale
        multilinePrtsale
        multilineTotalsale
        multilineJobcount
        startdate
        enddate
      }
    }
  }
`;
export const GET_DATA_FOR_KPI_LINE_RO_ADVISOR = gql`
  mutation getDataForKPILineROAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardLinero(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        technician: $technician
        timezoneOffset: $timeZone
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardLineros {
        totalRos
        onelineRos
        multilineRos
        onelinePercentage
        multilinePercentage
        onelineLbrsale
        onelinePrtsale
        onelineTotalsale
        multilineLbrsale
        multilinePrtsale
        multilineTotalsale
        multilineJobcount
        startdate
        enddate
      }
    }
  }
`;

// export const GET_DATA_FOR_KPI_PARTS_GRID = gql`
//   query getDataForPartsGrid(
//     $toggleOption: String
//     $timeZone: String
//     $payType: String
//   ) {
//     statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddata(
//       filterby: $toggleOption
//       timezoneOffset: $timeZone
//       payType: $payType
//     ) {
//       nodes {
//         totalcount
//         totalfalse
//         totaltrue
//         noncompliance
//       }
//     }
//   }
// `;
export const GET_DATA_FOR_KPI_PARTS_GRID = gql`
  mutation getDataForPartsGrid(
    $toggleOption: String
    $timeZone: String
    $payType: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddata(
      input: {
        filterby: $toggleOption
        timezoneOffset: $timeZone
        payType: $payType
      }
    ) {
      statelessDbdKpiScorecardVwKpiScorecardPartsMatrixGriddata {
        totalcount
        totalfalse
        totaltrue
        noncompliance
      }
    }
  }
`;

export const GET_DATA_FOR_KPI_PARTS_GRID_ADVISOR = gql`
  mutation getDataForPartsGridAdvisor(
    $toggleOption: String
    $serviceAdvisor: [String!]
    $timeZone: String
    $payType: String
    $technician: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddata(
      input: {
        filterby: $toggleOption
        advisor: $serviceAdvisor
        timezoneOffset: $timeZone
        payType: $payType
        technician: $technician
      }
    ) {
      statelessDbdKpiScorecardVwKpiScorecardPartsMatrixGriddata {
        totalcount
        totalfalse
        totaltrue
        noncompliance
      }
    }
  }
`;

// export const GET_DATA_FOR_LABOR_MISSES = gql`
//   query statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
//     $filterby: String
//     $timeZone: String
//     $payType: String
//     $gridType: String
//   ) {
//     statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
//       filterby: $filterby
//       timezoneOffset: $timeZone
//       payType: $payType
//       gridtype: $gridType
//       filter: { compliance: { equalTo: "FALSE" } }
//     ) {
//       nodes {
//         ronumber
//         advisorName
//         closeddate
//         customerName
//         lbrsale
//         gridPricing
//         gridDate
//         compliance
//         variance
//         lbrsoldhours
//         currentgridasof
//         doorRate
//         opcode
//         opcodeDesc
//         techName
//       }
//     }
//   }
// `;

export const GET_DATA_FOR_LABOR_MISSES = gql`
  mutation statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
    $timeZone: String
    $payType: String
    $gridType: String
    $filterStart: Date
    $filterEnd: Date
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
      input: {
        timezoneOffset: $timeZone
        payType: $payType
        gridtype: $gridType
        startDate: $filterStart
        endDate: $filterEnd
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridPricing
        gridDate
        compliance
        variance
        lbrsoldhours
        currentgridasof
        doorRate
        opcode
        opcodeDesc
        techName
        paytype
        paytypegroup
        model
        variancePerc
        targetElr
        actualElr
        fleetOrPaytypeOrOpcodeFixedRate
        storeId
        storeName
        gridType
        gridFor
        vin
        opcategory
        lbrcost
        lbractualhours
        prtextendedsale
        prtextendedcost
        lbrgrossprofit
        prtsgrossprofit
        elr
        markup
        lbrGrossprofitpercentage
        prtsGrossprofitpercentage
        make
        year
        mileage
        lbrsequenceno
      }
    }
  }
`;
export const GET_DATA_FOR_LABOR_MISSES_OLD = gql`
  mutation statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
    $filterby: String
    $timeZone: String
    $payType: String
    $gridType: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
      input: {
        filterby: $filterby
        timezoneOffset: $timeZone
        payType: $payType
        gridtype: $gridType
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridPricing
        gridDate
        compliance
        variance
        lbrsoldhours
        currentgridasof
        doorRate
        opcode
        opcodeDesc
        techName
        paytype
        paytypegroup
        model
        variancePerc
        targetElr
        actualElr
        fleetOrPaytypeOrOpcodeFixedRate
      }
    }
  }
`;
// export const GET_DATA_FOR_PARTS_MISSES = gql`
//   query statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
//     $filterby: String
//     $timeZone: String
//     $payType: String
//   ) {
//     statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
//       filterby: $filterby
//       timezoneOffset: $timeZone
//       payType: $payType
//       filter: { compliance: { equalTo: "FALSE" } }
//     ) {
//       nodes {
//         ronumber
//         advisorName
//         closeddate
//         customerName
//         lbrsale
//         gridDate
//         compliance
//         variance
//         targetPrice
//         currentgridasof
//         prtdesc
//         prtpartno
//         prtcost
//         prtlist
//         prtsource
//       }
//     }
//   }
// `;
export const GET_DATA_FOR_PARTS_MISSES = gql`
  mutation statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
    $timeZone: String
    $payType: String
    $filterStart: Date
    $filterEnd: Date
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
      input: {
        timezoneOffset: $timeZone
        payType: $payType
        startDate: $filterStart
        endDate: $filterEnd
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridDate
        compliance
        variance
        targetPrice
        currentgridasof
        prtdesc
        prtpartno
        prtcost
        prtlist
        prtsource
        paytype
        paytypegroup
        lbropcode
        opcodedescription
        prtqtysold
        prtextendedsale
        prtextendedcost
        fleetOrPaytypeOrOpcodeFixedRate
        targetExtendedPrice
        variancePerc
        techName
        grossprofitpercentage
        storeId
        matrixType
        storeName
        partsFor
        elr
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrsequenceno
        lbrsoldhours
        make
        markup
        mileage
        model
        opcategory
        prtlaborsequenceno
        prtsequenceno
        prtsgrossprofit
        vin
        year
      }
    }
  }
`;
export const GET_DATA_FOR_PARTS_MISSES_OLD = gql`
  mutation statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
    $filterby: String
    $timeZone: String
    $payType: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
      input: {
        filterby: $filterby
        timezoneOffset: $timeZone
        payType: $payType
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridDate
        compliance
        variance
        targetPrice
        currentgridasof
        prtdesc
        prtpartno
        prtcost
        prtlist
        prtsource
        paytype
        paytypegroup
        lbropcode
        opcodedescription
        prtqtysold
        prtextendedsale
        prtextendedcost
        fleetOrPaytypeOrOpcodeFixedRate
        targetExtendedPrice
        variancePerc
        techName
        grossprofitpercentage
      }
    }
  }
`;
export const GET_GLOSSARY_DETAILS = gql`
  query getGlossaryDetails {
    statelessCcPhysicalRwGetFopcGlossaries {
      nodes {
        shortyKey
        value
      }
    }
  }
`;
export const GET_GRIDORMETRIX_DETAILS = gql`
  query getGridorMatrixDetailsPartSource(
    $gridormatrix: String
    $payType: String
    $storeId: String
    $pGridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      storeid: $storeId
      pGridorpartsfor: $pGridorpartsfor
    ) {
      nodes {
        hours
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col4
        col5
        col6
        col7
        col8
        col9
        storeId
        createdDate
        doorRate
        gridOrder
        storeInstallDate
        col3
        partSource
        gridormatrixtype
        calcBase
        breakField
        gridorpartsfor
      }
    }
  }
`;
export const GET_GRIDORMETRIX_DETAILS_OLD = gql`
  query getGridorMatrixDetailsPartSource(
    $gridormatrix: String
    $payType: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
    ) {
      nodes {
        hours
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col4
        col5
        col6
        col7
        col8
        col9
        storeId
        createdDate
        doorRate
        gridOrder
        storeInstallDate
        col3
        partSource
      }
    }
  }
`;

export const GET_GRIDORMETRIX_DETAILS_PRTS = gql`
  query getGridorMatrixDetails(
    $gridormatrix: String
    $payType: String
    $partSource: String
    $storeId: String
    $pGridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      partSrc: $partSource
      storeid: $storeId
      pGridorpartsfor: $pGridorpartsfor
    ) {
      nodes {
        hours
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col4
        col5
        col6
        col7
        col8
        col9
        storeId
        createdDate
        doorRate
        gridOrder
        storeInstallDate
        col3
        partSource
        gridormatrixtype
        calcBase
        breakField
        gridorpartsfor
      }
    }
  }
`;
export const GET_GRIDORMETRIX_DETAILS_PRTS_OLD = gql`
  query getGridorMatrixDetails(
    $gridormatrix: String
    $payType: String
    $partSource: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      partSrc: $partSource
    ) {
      nodes {
        hours
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col4
        col5
        col6
        col7
        col8
        col9
        storeId
        createdDate
        doorRate
        gridOrder
        storeInstallDate
        col3
        partSource
      }
    }
  }
`;
export const GET_MENU_OPCODES = gql`
  query getMenuOpcodes {
    statelessCcPhysicalRwGetMenuOpcodes {
      nodes
    }
  }
`;
export const GET_MPI_OPCODES = gql`
  query getMPIOpcodes {
    statelessCcPhysicalRwGetMpiOpcodes {
      nodes
    }
  }
`;
export const UPDATE_KPI_CURRENT_DATE = gql`
  mutation updateKpiCurrentdate {
    statelessDbdKpiScorecardUpdateKpiCurrentdate(input: {}) {
      clientMutationId
    }
  }
`;
export const INSERT_LOGIN_DETAILS = gql`
  mutation TblClientaudits(
    $username: String
    $storeid: String
    $logindate: Datetime
    $offset: String
    $logInout: String
  ) {
    statelessCcPhysicalRwInsertUserLoginHistory(
      input: {
        username: $username
        logindate: $logindate
        timezoneOffset: $offset
        logInout: $logInout
        storeid: $storeid
      }
    ) {
      clientMutationId
    }
  }
`;
export const GET_TARGET_GRID_RATE = gql`
  query getTargetGridRate {
    statelessCcPhysicalRwGetDoorRate {
      nodes {
        value
      }
    }
  }
`;

export const GET_KPI_TOGGLE_OPTIONS_WITH_TIMEZONE = gql`
  query getKpiToggleOptionsWithTimeZone($timezoneOffset: String) {
    statelessCcPhysicalRoGetToggleDurationsTimezone(
      timezoneOffset: $timezoneOffset
    ) {
      nodes {
        today
        yesterday
        lastweekstartdate
        lastweekenddate
        mtdstartdate
        mtdenddate
        lastmonthstartdate
        lastmonthenddate
        ytdstartdate
        ytdenddate
        lastyearstartdate
        lastyearenddate
        lastquarterstartdate
        lastquarterenddate
        lastthirtydaystartdate
        lastthirtydayenddate
        lastninetydaystartdate
        lastninetydayenddate
        lastsixmonthstartdate
        lastsixmonthenddate
        dayBeforeYesterday
        lastthreemonthenddate
        lastthreemonthstartdate
        lasttwelvemonthenddate
        lasttwelvemonthstartdate
        thisweekstartdate
        thisweekenddate
        lasttwoweekstartdate
        lasttwoweekenddate
      }
    }
  }
`;

export const GET_DATA_FOR_LABOR_MISSES_WITH_RO = gql`
  mutation getDataForLaborMissesWithRO(
    $timeZone: String
    $roNumber: String
    $closeDate: Date
    $payType: String
    $gridType: String
    $filterStart: Date
    $filterEnd: Date
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
      input: {
        timezoneOffset: $timeZone
        roNumber: $roNumber
        closeDate: $closeDate
        payType: $payType
        gridtype: $gridType
        startDate: $filterStart
        endDate: $filterEnd
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridPricing
        gridDate
        compliance
        variance
        lbrsoldhours
        lbrsequenceno
        currentgridasof
        doorRate
        opcode
        opcodeDesc
        techName
        paytype
        paytypegroup
        model
        variancePerc
        targetElr
        actualElr
        fleetOrPaytypeOrOpcodeFixedRate
        storeId
        storeName
        gridType
        gridFor
      }
    }
  }
`;
export const GET_DATA_FOR_LABOR_MISSES_WITH_RO_OLD = gql`
  mutation getDataForLaborMissesWithRO(
    $filterby: String
    $timeZone: String
    $roNumber: String
    $closeDate: Date
    $payType: String
    $gridType: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
      input: {
        filterby: $filterby
        timezoneOffset: $timeZone
        roNumber: $roNumber
        closeDate: $closeDate
        payType: $payType
        gridtype: $gridType
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridPricing
        gridDate
        compliance
        variance
        lbrsoldhours
        lbrsequenceno
        currentgridasof
        doorRate
        opcode
        opcodeDesc
        techName
        paytype
        paytypegroup
        model
        variancePerc
        targetElr
        actualElr
        fleetOrPaytypeOrOpcodeFixedRate
      }
    }
  }
`;
export const GET_DATA_FOR_PARTS_MISSES_WITH_RO = gql`
  mutation getDataForPartsMissesWithRO(
    $timeZone: String
    $roNumber: String
    $closedDate: Date
    $payType: String
    $filterStart: Date
    $filterEnd: Date
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
      input: {
        timezoneOffset: $timeZone
        roNumber: $roNumber
        closeDate: $closedDate
        payType: $payType
        startDate: $filterStart
        endDate: $filterEnd
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns {
        opendate
        ronumber
        closeddate
        gridDate
        lbrsale
        variance
        compliance
        advisorName
        customerName
        targetPrice
        targetExtendedPrice
        lbrsequenceno
        prtlaborsequenceno
        prtsequenceno
        prtcost
        lbropcode
        techName
        grossprofitpercentage
        storeId
        matrixType
        storeName
        partsFor
        paytypegroup
        prtextendedsale
        prtextendedcost
        markup
      }
    }
  }
`;
export const GET_DATA_FOR_PARTS_MISSES_WITH_RO_OLD = gql`
  mutation getDataForPartsMissesWithRO(
    $filterby: String
    $timeZone: String
    $roNumber: String
    $closedDate: Date
    $payType: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
      input: {
        filterby: $filterby
        timezoneOffset: $timeZone
        roNumber: $roNumber
        closeDate: $closedDate
        payType: $payType
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns {
        opendate
        ronumber
        closeddate
        gridDate
        lbrsale
        variance
        compliance
        advisorName
        customerName
        targetPrice
        targetExtendedPrice
        lbrsequenceno
        prtlaborsequenceno
        prtsequenceno
        prtcost
        lbropcode
        techName
        grossprofitpercentage
        paytypegroup
      }
    }
  }
`;
export const GET_MENU_OPCODES_LIST = gql`
  query getMenuOpcodesList {
    statelessCcPhysicalRwGetMenuOpcodesDetails {
      nodes {
        opcode
      }
    }
  }
`;
export const GET_MPI_OPCODES_LIST = gql`
  query getMPIOpcodesList {
    statelessCcPhysicalRwGetMpiOpcodesDetails {
      nodes {
        opcode
      }
    }
  }
`;

export const GET_ADVISOR_NAME = gql`
  query getAdvisorName($serviceadvisor: String) {
    statelessCcPhysicalRwGetTblServiceAdvisors(
      filter: { serviceadvisor: { equalTo: $serviceadvisor } }
    ) {
      nodes {
        name
        serviceadvisor
        nickname
      }
    }
  }
`;

export const GET_KPI_SCORE_CARD_GOAL = gql`
  mutation getKpiScoreCardGoal(
    $calltype: String
    $reporttype: String
    $serviceadvisor: [String]
    $timezoneOffset: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGoal(
      input: {
        calltype: $calltype
        reporttype: $reporttype
        storeOrAdvisorOrTech: $serviceadvisor
        timezoneOffset: $timezoneOffset
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGoals {
        advisorgoalval
        goalname
        prior12Val
        prior3Val
        storegoalval
        currentgoaldate
        kpino
        kpiTypeCode
      }
    }
  }
`;

export const GET_SET_KPI_SCORE_CARD_SETTINGS_EMAIL = gql`
  mutation getOrSetKpiScoreCardGoalEmail(
    $getorset: String
    $recipientlist: JSON
  ) {
    statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs(
      input: { getorset: $getorset, recipientlist: $recipientlist }
    ) {
      statelessDbdKpiScorecardKpiScorecardMailJobs {
        mailFrequency
        mailStatus
        recipientId
        scheduledOn
        id
        serviceAdvisor
      }
    }
  }
`;

export const GET_CLIENT_ID = gql`
  query getClientId {
    statelessCcPhysicalRoGetClientMasterS {
      nodes {
        clientId
      }
    }
  }
`;

export const UPDATE_KPI_SCORECARD_GOAL = gql`
  mutation updateKPIGoalSetting(
    $pGoal: JSON
    $serviceadvisor: String
    $reporttype: String
    $userid: String
  ) {
    statelessDbdKpiScorecardInsertOrUpdateKpiScorecardGoal(
      input: {
        pGoal: $pGoal
        pStoreOrAdvisorOrTech: $serviceadvisor
        reporttype: $reporttype
        userId: $userid
      }
    ) {
      string
    }
  }
`;

export const CHECK_INTERNAL_TOGGLE_EXISTANCE = gql`
  query checkInternalToggleExistance($partyid: String, $shortkey: String) {
    statelessCcPhysicalRoGetConfigurationValue(
      partyid: $partyid
      shortkey: $shortkey
    )
  }
`;
export const GET_SET_STORE_SETTINGS = gql`
  mutation SetStoreSetting(
    $getorset: String
    $pGoal: JSON
    $settingtype: String
    $timezoneValue: String
    $userid: String
    $storeNickName: String
    $dmsFee: BigFloat
    $fopcFee: BigFloat
  ) {
    statelessCcPhysicalRwGetorsetStoreSettings(
      input: {
        getorset: $getorset
        pGoal: $pGoal
        settingtype: $settingtype
        timezoneValue: $timezoneValue
        userid: $userid
        storeNickName: $storeNickName
        dmsFee: $dmsFee
        fopcFee: $fopcFee
      }
    ) {
      statelessCcPhysicalRwStoreSettings {
        keyname
        keyvalue
        active
      }
    }
  }
`;

export const GET_SEARCHBYRO_JSON_DATA = gql`
  query getSearchByROJSONData($ronumberSelected: String, $store_id: String) {
    statefulDtkSourceRawDtkDataDetails(
      filter: {
        roNumber: { equalTo: $ronumberSelected }
        storeId: { equalTo: $store_id }
      }
    ) {
      nodes {
        id
        roDataXml
        roNumber
        roDataJson
      }
    }
  }
`;
export const GET_MATRIX_DETAIL = gql`
  query getMatrixDetail(
    $gridormatrix: String
    $storeId: String
    $payType: String
    $partSrc: [String]
    $createddate: Date
    $pGridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      storeid: $storeId
      gridormatrix: $gridormatrix
      partSrc: $partSrc
      payType: $payType
      createddate: $createddate
      pGridorpartsfor: $pGridorpartsfor
    ) {
      nodes {
        breakField
        calcBase
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        createdDate
        doorRate
        gridOrder
        gridormatrixtype
        hours
        partSource
        storeId
        storeInstallDate
        gridormatrixtype
        calcBase
        breakField
        gridorpartsfor
      }
    }
  }
`;
export const GET_MATRIX_TYPE = gql`
  query getMatrixType(
    $gridormatrix: String
    $storeId: String
    $payType: String
    $partSrc: [String]
    $gridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      storeid: $storeId
      gridormatrix: $gridormatrix
      partSrc: $partSrc
      payType: $payType
      pGridorpartsfor: $gridorpartsfor
    ) {
      nodes {
        breakField
        calcBase
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        createdDate
        doorRate
        gridOrder
        gridormatrixtype
        hours
        partSource
        storeId
        storeInstallDate
        gridormatrixtype
        calcBase
        breakField
        gridorpartsfor
        dipsGridorpartsfor
      }
    }
  }
`;
export const GET_PARTS_LIST = gql`
  query getMatrixType(
    $gridormatrix: String
    $storeId: String
    $payType: String
    $partSrc: [String]
    $pGridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      storeid: $storeId
      gridormatrix: $gridormatrix
      partSrc: $partSrc
      payType: $payType
      pGridorpartsfor: $pGridorpartsfor
    ) {
      nodes {
        breakField
        calcBase
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        createdDate
        doorRate
        gridOrder
        gridormatrixtype
        hours
        partSource
        storeId
        storeInstallDate
      }
    }
  }
`;
export const GET_PARTS_DETAILS = gql`
  query getMatrixType(
    $gridormatrix: String
    $storeId: String
    $payType: String
    $partSrc: [String]
    $pGridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      storeid: $storeId
      gridormatrix: $gridormatrix
      partSrc: $partSrc
      payType: $payType
      pGridorpartsfor: pGridorpartsfor
    ) {
      nodes {
        breakField
        calcBase
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        createdDate
        doorRate
        gridOrder
        gridormatrixtype
        hours
        partSource
        storeId
        storeInstallDate
      }
    }
  }
`;
export const GET_MATRIX_PART_SOURCE = gql`
  query getMatrixPartSource(
    $gridormatrix: String
    $payType: String
    $storeId: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      storeid: $storeId
    ) {
      nodes {
        partSource
      }
    }
  }
`;
export const GET_MATRIX_PART_SOURCE_OLD = gql`
  query getMatrixPartSource($gridormatrix: String, $payType: String) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
    ) {
      nodes {
        partSource
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PAYTYPE_DETAILS = gql`
  query getGridorMatrixPayTypeDetails($gridormatrix: String, $storeId: String) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      storeid: $storeId
    ) {
      nodes {
        gridormatrixtype
        gridorpartsfor
        dipsGridorpartsfor
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PAYTYPE_DETAILS_OLD = gql`
  query getGridorMatrixPayTypeDetails($gridormatrix: String) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
    ) {
      nodes {
        gridormatrixtype
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PERIODS_PRTSOURCE = gql`
  query getGridorMatrixPayTypeDetailsPartSource(
    $gridormatrix: String
    $payType: String
    $partSource: String
    $storeId: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      partSrc: $partSource
      storeid: $storeId
    ) {
      nodes {
        gridOrder
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PERIODS_PRTSOURCE_OLD = gql`
  query getGridorMatrixPayTypeDetailsPartSource(
    $gridormatrix: String
    $payType: String
    $partSource: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      partSrc: $partSource
    ) {
      nodes {
        gridOrder
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PERIODS = gql`
  query getGridorMatrixPayTypeDetails(
    $gridormatrix: String
    $payType: String
    $partSource: [String!]
    $storeId: String
    $pGridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      partSrc: $partSource
      storeid: $storeId
      pGridorpartsfor: $pGridorpartsfor
    ) {
      nodes {
        gridOrder
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PERIODS_OLD = gql`
  query getGridorMatrixPayTypeDetails(
    $gridormatrix: String
    $payType: String
    $partSource: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      payType: $payType
      partSrc: $partSource
    ) {
      nodes {
        gridOrder
      }
    }
  }
`;

export const GET_OPCODE_DETAILED_VIEW_MONTH_YEARS = gql`
  query getOpcodeDetailedViewMonthYears {
    statelessCcDrilldownGetDrillDownTotalRevenueDetailsMonthYears {
      nodes {
        monthYear
      }
    }
  }
`;
export const GET_LAUNCH_DATE = gql`
  query getLaunchdate {
    statelessCcPhysicalRwGetLaunchdate {
      nodes
    }
  }
`;
export const GET_DAILY_UPDATE_STATUS_ALL = gql`
  query getDailyUpdateStatusAll(
    $startdate: Date
    $enddate: Date
    $storeid: String
    $optionName: String
    $timezoneOffset: String
  ) {
    statelessCcPhysicalRwGetDailyUpdateStatusWithoutfilter(
      storeid: $storeid
      enddate: $enddate
      startdate: $startdate
      timezoneOffset: $timezoneOffset
      filter: { optionName: { equalTo: $optionName } }
    ) {
      nodes {
        advisorName
        advisorid
        chartId
        displaysort
        errorDesc
        goalName
        keyName
        kpiNo
        laboropcode
        logStatus
        logdate
        newChartname
        newChartdescription
        newGoalvalue
        newKeyvalue
        newNickname
        newOpcategory
        newPaytypecode
        newPaytypedept
        newStatus
        newcategorized
        newfrdate
        newfrstatus
        newfrvalue
        newgridexcluded
        newlaborFixedRate
        newlaborFixedratedate
        newlaborFixedratevalue
        newmaintenancePlan
        newmarkdowndesc
        newmenuSales
        newmpiItem
        newpartsFixedRate
        newpartsFixedratedate
        newpartsFixedratevalue
        oldChartdescription
        oldChartname
        oldGoalvalue
        oldKeyvalue
        oldNickname
        oldOpcategory
        oldPaytypecode
        oldPaytypedept
        oldStatus
        oldcategorized
        oldfrdate
        oldfrstatus
        oldfrvalue
        oldgridexcluded
        oldlaborFixedRate
        oldlaborFixedratedate
        oldlaborFixedratevalue
        oldmaintenancePlan
        oldmarkdowndesc
        oldmenuSales
        oldmpiItem
        oldpartsFixedRate
        oldpartsFixedratedate
        oldpartsFixedratevalue
        optionName
        paytype
        settingType
        statusdate
        statustime
        storeId
        storeName
        techid
        techname
        ticketId
        userName
        userRole
      }
    }
  }
`;
export const GET_DAILY_UPDATE_STATUS = gql`
  query getDailyUpdateStatus(
    $startdate: Date
    $enddate: Date
    $storeid: String
    $optionName: String
    $timezoneOffset: String
  ) {
    statelessCcPhysicalRwGetDailyUpdateStatus(
      storeid: $storeid
      enddate: $enddate
      startdate: $startdate
      timezoneOffset: $timezoneOffset
      filter: { optionName: { equalTo: $optionName } }
    ) {
      nodes {
        advisorName
        advisorid
        chartId
        displaysort
        errorDesc
        goalName
        keyName
        kpiNo
        laboropcode
        logStatus
        logdate
        newChartdescription
        newChartname
        newGoalvalue
        newKeyvalue
        newNickname
        newOpcategory
        newPaytypecode
        newPaytypedept
        newStatus
        newcategorized
        newfrdate
        newfrstatus
        newfrvalue
        newgridexcluded
        newlaborFixedRate
        newlaborFixedratedate
        newlaborFixedratevalue
        newmaintenancePlan
        newmarkdowndesc
        newmenuSales
        newmpiItem
        newpartsFixedRate
        newpartsFixedratedate
        newpartsFixedratevalue
        oldChartdescription
        oldChartname
        oldGoalvalue
        oldKeyvalue
        oldNickname
        oldOpcategory
        oldPaytypecode
        oldPaytypedept
        oldStatus
        oldcategorized
        oldfrdate
        oldfrstatus
        oldfrvalue
        oldgridexcluded
        oldlaborFixedRate
        oldlaborFixedratedate
        oldlaborFixedratevalue
        oldmaintenancePlan
        oldmarkdowndesc
        oldmenuSales
        oldmpiItem
        oldpartsFixedRate
        oldpartsFixedratedate
        oldpartsFixedratevalue
        optionName
        paytype
        settingType
        statusdate
        statustime
        storeId
        storeName
        techid
        techname
        ticketId
        userName
        userRole
      }
    }
  }
`;
export const GET_UNIQUE_OPCODES = gql`
  query getUniqueOpcodes {
    statelessCcPhysicalRwGetOpcategoryCodes(orderBy: SORT_ASC) {
      nodes {
        opcategory
      }
    }
  }
`;
export const GET_LABOR_OPPORTUNITY_BASELINE_ADVISOR = gql`
  query getLaborTotalOpportunityBaselineAdvisor(
    $timeZone: String
    $advisor: String
  ) {
    statelessDbdCpOpportunityLaborGetLaborOpportunityBaselineServiceadvisor(
      timezoneOffset: $timeZone
      advisor: $advisor
    ) {
      nodes {
        lastQtrGpPercentage
        lastQtrHrsPerRo
        storeId
        serviceadvisor
      }
    }
  }
`;
export const GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_LABOR = gql`
  query getGoalSettingsByGroupNameAdvisorsLabor($advisor: String) {
    statelessDbdCpOpportunityLaborGetLbrGoalsettingsValue(advisor: $advisor) {
      nodes {
        goalId
        goalName
        goalValue
        storeId
        serviceAdvisor
      }
    }
  }
`;
export const GET_HOME_MESSAGE = gql`
  query GetHomemessage {
    statefulServiceConfigurationGetHomemessage {
      nodes
    }
  }
`;
export const UPDATE_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR = gql`
  mutation updateGoalSettingsByGroupNameAdvisor(
    $goal_value: BigFloat
    $goal_name: String
    $group_name: String
    $advisor: String
    $chartId: Int
  ) {
    statelessCcPhysicalRwUpdateGoalSettingsByServiceadvisor(
      input: {
        goalvalue: $goal_value
        goalname: $goal_name
        groupname: $group_name
        advisor: $advisor
        chartid: $chartId
      }
    ) {
      clientMutationId
    }
  }
`;

export const GET_TOTAL_LABOR_OPPORTUNITY = gql`
  query getTotalLaborOpportunity($advisor: String, $month_year: [String!]) {
    statelessDbdCpOpportunityLaborGetChartsTotalLaborOpportunityServiceadvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        rodate
        lbrjointOpportunity
        lbrvolumeOpportunity
        lbrgrossOpportunity
        totalOpportunity
        storeId
        monthYear
        serviceadvisor
      }
    }
  }
`;

export const GET_LABOR_TOTAL_OPPORTUNITY_ADVISOR = gql`
  query getLaborTotalOpportunityAdvisor($advisor: String) {
    statelessDbdCpOpportunityLaborGetLastTwelveMonthTotalLaborOpportunityServiceadvisor(
      advisor: $advisor
    ) {
      nodes {
        totalLbrgrossOpportunity
        totalLbrvolumeOpportunity
        totalLbrjointOpportunity
        totalLbropportunity
        storeId
        serviceadvisor
      }
    }
  }
`;
export const GET_PARTS_OPPORTUNITY_BASELINE_ADVISOR = gql`
  query getpartsTotalOpportunityBaselineAdvisor(
    $advisor: String
    $timeZone: String
  ) {
    statelessDbdCpOpportunityPartsGetPartsOpportunityBaselineServiceadvisor(
      advisor: $advisor
      timezoneOffset: $timeZone
    ) {
      nodes {
        lastQtrGpPercentage
        lastQtrHrsPerRo
        storeId
        serviceadvisor
      }
    }
  }
`;
export const GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_PARTS = gql`
  query getGoalSettingsByGroupNameAdvisorsParts($advisor: String) {
    statelessDbdCpOpportunityPartsGetPrtsGoalsettingsValue(advisor: $advisor) {
      nodes {
        goalId
        goalName
        goalValue
        storeId
        serviceAdvisor
        chartId
      }
    }
  }
`;
export const GET_TOTAL_PARTS_OPPORTUNITY = gql`
  query getTotalPartsOpportunity($advisor: String, $month_year: [String!]) {
    statelessDbdCpOpportunityPartsGetChartsTotalPartsOpportunityServiceadvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        rodate
        prtgrossOpportunity
        prtvolumeOpportunity
        prtjointOpportunity
        totalOpportunity
        storeId
        monthYear
        serviceadvisor
      }
    }
  }
`;

export const GET_PARTS_TOTAL_OPPORTUNITY_ADVISOR = gql`
  query getPartsTotalOpportunityAdvisor($advisor: String) {
    statelessDbdCpOpportunityPartsGetLastTwelveMonthTotalPartsOpportunityServiceadvisor(
      advisor: $advisor
    ) {
      nodes {
        totalPrtgrossOpportunity
        totalPrtvolumeOpportunity
        totalPrtjointOpportunity
        totalPrtsopportunity
        storeId
        serviceadvisor
      }
    }
  }
`;
export const GET_TOTAL_ELR_OPPORTUNITY = gql`
  query getTotalElrOpportunity($advisor: String, $month_year: [String!]) {
    statelessDbdCpOpportunityElrGetTotalPricingOpportunityByServiceadvisor(
      advisor: $advisor
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        rodate
        competitiveOpportunity
        maintenanceOpportunity
        repairOpportunity
        total
        serviceadvisor
        storeId
        monthYear
      }
    }
  }
`;
export const GET_ELR_OPPORTUNITY_BASELINE_ADVISOR = gql`
  query getElrOpportunityBaselineAdvisor($advisor: String, $timeZone: String) {
    statelessDbdCpOpportunityElrGetElrOpportunityBaselineServiceadvisor(
      advisor: $advisor
      timezoneOffset: $timeZone
    ) {
      nodes {
        lastQtrCompElr
        lastQtrMaintElr
        lastQtrRepairElr
        storeId
        serviceadvisor
      }
    }
  }
`;
export const GET_ELR_OPPORTUNITY_ADVISOR = gql`
  query getElrOpportunityAdvisor($advisor: String) {
    statelessDbdCpOpportunityElrGetAnnualPricingOpportunityByServiceadvisor(
      advisor: $advisor
    ) {
      nodes {
        totalCompetitiveOpportunity
        totalMaintenanceOpportunity
        totalRepairOpportunity
        totalOpportunity
        storeId
        serviceadvisor
      }
    }
  }
`;
export const GET_GOAL_SETTINGS_BY_GROUP_NAME_ADVISOR_ELR = gql`
  query getGoalSettingsByGroupNameAdvisorsElr($advisor: String) {
    statelessDbdCpOpportunityElrGetElrGoalsettingsValue(advisor: $advisor) {
      nodes {
        goalId
        goalName
        goalValue
        storeId
        serviceAdvisor
      }
    }
  }
`;
export const GET_FLEET_CUSTOMER_NAMES = gql`
  query getFleetCustomerNames($store_id: String) {
    statelessCcAggregateGetCustomers(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        customerName
        storeId
      }
    }
  }
`;
// export const GET_FLEET_CUSTOMER_NAMES = gql`
//   query getFleetCustomerNames {
//     statelessCcAggregateGetCustomers {
//       nodes {
//         customerName
//         storeId
//       }
//     }
//   }
// `;
export const GET_FLEET_LIST = gql`
  query getFleetListItems(
    $fleetname: [String!]
    $fleettype: String
    $store_id: String
  ) {
    statelessCcPhysicalRwGetLaborAndPartsFleetAccount(
      fleetname: $fleetname
      fleettype: $fleettype
      pStoreId: $store_id
    ) {
      nodes {
        id
        fleetName
        fleetFlag
        partsmarkup
        partsource
        laborOrParts
        gridOrder
        fleetRate
        installDate
      }
    }
  }
`;

export const ADD_FLEET_TO_LIST = gql`
  mutation addFleetToList(
    $fleetname: String
    $process: String
    $userid: String
    $pType: String
    $store_id: String
  ) {
    statelessCcPhysicalRwInsertOrUpdateFleetMaster(
      input: {
        pType: $pType
        process: $process
        userid: $userid
        fleetname: $fleetname
        pStoreId: $store_id
      }
    ) {
      statelessCcPhysicalRwKpiFleetMasters {
        fleetName
        partSource
      }
    }
  }
`;

export const GET_RO_LIST = gql`
  mutation getROList($month: String, $advisor: [String]) {
    statelessCcPhysicalRwGetDailyRocountCalendar(
      input: { _month: $month, advisor: $advisor }
    ) {
      json
    }
  }
`;

export const UPDATE_USER_ENABLE_STATUS = gql`
  mutation updateUserEnableStatus($username: String, $status: Int) {
    statelessKeycloakServiceEnableOrDisableUser(
      input: { userName: $username, activeFlag: $status }
    ) {
      json
    }
  }
`;

export const INSERT_FLEET_DETAILS = gql`
  mutation updateGoalSettingsByGroupName(
    $user_id: String
    $fleet_rate_grid: JSON
    $fleet_rate_parts: JSON
    $fleet_exists_labor_grid: JSON
    $fleet_exists_parts_matrix: JSON
  ) {
    statelessCcPhysicalRwInsertOrUpdateFleetAccount(
      input: {
        fleetRateGrid: $fleet_rate_grid
        fleetRateParts: $fleet_rate_parts
        fleetExistsLaborGrid: $fleet_exists_labor_grid
        fleetExistsPartsMatrix: $fleet_exists_parts_matrix
        userid: $user_id
      }
    ) {
      results {
        msg
        status
      }
    }
  }
`;

export const GET_SERVICE_ADVISOR_DATA = gql`
  query getServiceAdvisorData {
    statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReportAll {
      nodes {
        monthYear
        closeddate
        rocount
        jobcount
        lbrsale
        lbrcost
        lbrsoldhours
        prtssale
        prtscost
        lbrprofit
        prtsprofit
        lbrprftpercentage
        prtprfpercentage
        avgelr
        avgmarkup
        totalrevenue
        storeId
      }
    }
  }
`;
export const GET_SERVICE_ADVISOR_DATA_SA = gql`
  query getServiceAdvisorData($serviceadvisor: [String!]) {
    statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrendCharts(
      advisor: $serviceadvisor
    ) {
      nodes {
        monthYear
        closeddate
        serviceadvisor
        rocount
        jobcount
        lbrsale
        lbrcost
        lbrsoldhours
        prtssale
        prtscost
        lbrprofit
        prtsprofit
        lbrprftpercentage
        prtprfpercentage
        avgelr
        avgmarkup
        totalrevenue
        storeId
        advisorName
      }
    }
  }
`;
export const GET_FIXED_RATE_DETAILS = gql`
  mutation getFixedRateDetails(
    $pAction: String
    $pLaborOrParts: String
    $pOpcode: String
    $pPaytype: String
    $fixedratevalue: String
    $fixedratedate: Date
    $userid: String
    $storeId: String
  ) {
    statelessCcPhysicalRwGetOrSetFixedRateMaster(
      input: {
        pAction: $pAction
        pLaborOrParts: $pLaborOrParts
        pOpcode: $pOpcode
        pPaytype: $pPaytype
        fixedratevalue: $fixedratevalue
        fixedratedate: $fixedratedate
        userid: $userid
        storeid: $storeId
      }
    ) {
      statelessCcPhysicalRwFixedRateMasters {
        opcode
        fixedRateValue
        fixedRateDate
        storeId
        lastUpdatedBy
        fixrateOrder
      }
    }
  }
`;
export const GET_FIXED_RATE_DETAILS_OLD = gql`
  mutation getFixedRateDetails(
    $pAction: String
    $pOpcode: String
    $pPaytype: String
    $fixedratevalue: BigFloat
    $fixedratedate: Date
    $userid: String
  ) {
    statelessCcPhysicalRwGetOrSetFixedRateMaster(
      input: {
        pAction: $pAction
        pOpcode: $pOpcode
        pPaytype: $pPaytype
        fixedratevalue: $fixedratevalue
        fixedratedate: $fixedratedate
        userid: $userid
      }
    ) {
      statelessCcPhysicalRwFixedRateMasters {
        opcode
        fixedRateValue
        fixedRateDate
        lastUpdatedBy
        fixrateOrder
      }
    }
  }
`;
export const GET_SCATTER_PLOT_REPORT_DATA = gql`
  mutation getScatterPlotReportData(
    $type: String
    $advisor: [String!]
    $timeZone: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardScatterplot(
      input: { filterby: $type, advisor: $advisor, timezoneOffset: $timeZone }
    ) {
      statelessDbdKpiScorecardKpiScorecardScatterplots {
        startdate
        enddate
        coldesc
        col0
        col1
        col2
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        colr0
        colr1
        colr2
        colr3
        colr4
        colr5
        colr6
        colr7
        colr8
        colr9
        colm0
        colm1
        colm2
        colm3
        colm4
        colm5
        colm6
        colm7
        colm8
        colm9
      }
    }
  }
`;

export const GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN_DATE_RANGE = gql`
  mutation getDrillDownDataForTotalRevenueDateRange(
    $startdate: Date
    $enddate: Date
  ) {
    statelessCcDrilldownGetDrillDownTotalRevenueDetails(
      input: { startdate: $startdate, enddate: $enddate }
    ) {
      statelessCcDrillDownTotalRevenueDetails {
        advisorName
        closeddate
        dominantElr
        dominantMarkup
        elr
        elrVariance
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        markup
        markupVariance
        mileage
        monthYear
        opcategory
        opendate
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        storeId
        techName
        vin
        newPaytypegroup
      }
    }
  }
`;
// export const GET_TOTAL_REVENUE_DETAILS_DRILL_DOWN_DATE_RANGE = gql`
//   query getDrillDownDataForTotalRevenueDateRange(
//     $startdate: Date
//     $enddate: Date
//     $store_id: String
//   ) {
//     statelessCcDrilldownGetDrillDownTotalRevenueDetails(
//       filter: { storeId: { equalTo: $store_id } }
//       startdate: $startdate
//       enddate: $enddate
//     ) {
//       nodes {
//         advisorName
//         closeddate
//         dominantElr
//         dominantMarkup
//         elr
//         elrVariance
//         filterByLaborparts
//         filterByRevenue
//         lbrGrossprofitpercentage
//         lbrcost
//         lbrgrossprofit
//         lbrlinecode
//         lbropcode
//         lbropcodedesc
//         lbrsale
//         lbrsequenceno
//         lbrsoldhours
//         lbrtechhours
//         markup
//         vin
//         techName
//         storeId
//         serviceadvisor
//         ronumber
//         prtsgrossprofit
//         prtsGrossprofitpercentage
//         prtextendedsale
//         prtextendedcost
//         paytypegroup
//         paytype
//         opsubcategory
//         opendate
//         opcategory
//         monthYear
//         mileage
//         markupVariance
//       }
//     }
//   }
// `;
export const GET_AXCESSA_REPORT_SUMMARY = gql`
  mutation getAxcessaReportSummary($serviceAdvisor: [String!]) {
    statelessDbdLaborWorkmixGetAxcessaSummary(
      input: { advisor: $serviceAdvisor }
    ) {
      axcessaSummaryDetails {
        paytypegroup
        storeId
        categoryName
        averageData
        totalData
        mon1
        mon2
        mon3
        mon4
        mon5
        mon6
        mon7
        mon8
        mon9
        mon10
        mon11
        mon12
        mon13
      }
    }
  }
`;
export const GET_PAYTYPE_LIST = gql`
  query getPayTypeList($store_id: String) {
    statelessCcPhysicalRwGetPayTypeMasters(
      filter: { storeId: { equalTo: $store_id } }
    ) {
      nodes {
        payType
        payTypeCode
      }
    }
  }
`;

export const GET_KPI_REPORT_2_DATA = gql`
  mutation getKpiReport2Data(
    $advisor: [String!]
    $dayfilter: Int
    $filterby: String
    $timezoneOffset: String
    $pReportType: String
    $userid: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardAllPages(
      input: {
        advisor: $advisor
        dayfilter: $dayfilter
        filterby: $filterby
        timezoneOffset: $timezoneOffset
        pReportType: $pReportType
        userid: $userid
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardAllPages {
        serviceAdvisor
        startdate
        enddate
        laborgproTotalros
        laborgproAvgsales
        laborgproAvgGp
        laborgproGp
        laborgproGpPerc
        laborgproTotalsales
        partsgproTotalros
        partsgproAvgsales
        partsgproAvgGp
        partsgproGp
        partsgproGpPerc
        partsgproTotalsales
        totalgproTotalros
        totalgproAvgsales
        totalgproAvgGp
        totalgproGp
        totalgproTotalsales
        flatratehrsTotaldays
        flatratehrsTotalsoldhours
        flatratehrsAvgsoldhoursperdays
        flatratehrsAvgsoldhourspercpro
        flatratehrsTotalros
        lineroltsixtykOnelineLbrsale
        lineroltsixtykOnelinePrtsale
        lineroltsixtykOnelineTotalsale
        lineroltsixtykMultilineLbrsale
        lineroltsixtykMultilinePrtsale
        lineroltsixtykMultilineTotalsale
        lineroltsixtykTotalRos
        lineroltsixtykOnelinePercentage
        lineroltsixtykOnelineRos
        lineroltsixtykMultilinePercentage
        lineroltsixtykMultilineRos
        lineroltsixtykMultilibeAvgJobRo
        lineroOnelineLbrsale
        lineroOnelinePrtsale
        lineroOnelineTotalsale
        lineroMultilineLbrsale
        lineroMultilinePrtsale
        lineroMultilineTotalsale
        lineroTotalRos
        lineroOnelinePercentage
        lineroOnelineRos
        lineroMultilinePercentage
        lineroMultilineRos
        lineroMultilibeAvgJobRo
        linerogtsixtykOnelineLbrsale
        linerogtsixtykOnelinePrtsale
        linerogtsixtykOnelineTotalsale
        linerogtsixtykMultilineLbrsale
        linerogtsixtykMultilinePrtsale
        linerogtsixtykTotalRos
        linerogtsixtykOnelinePercentage
        linerogtsixtykOnelineRos
        linerogtsixtykMultilinePercentage
        linerogtsixtykMultilineRos
        linerogtsixtykMultilibeAvgJobRo
        gridJson

        roshareWorkingdays
        roshareTotalRoAfterfilter
        roshareTotalshareAfterfilter
        roshareTotalvalueAfterfilter
        roshareTotalro
        roshareTotalshare
        roshareTotalvalue
        cwitotalCptotal
        cwitotalWtotal
        cwitotalItotal
        cwitotalTotal
        workmixMaintenanceTotal
        workmixCompetitiveTotal
        workmixRepairTotal
        workmixMaintenancetotalperc
        workmixCompetitivetotalperc
        workmixRepairtotalperc
        workmixTotal
        workmixTotalperc
        avgagemilesAvgAge
        avgagemilesAvgMileage
        avgagemilesNoOfRecords
        avgagemilesTotalAge
        avgagemilesTotalMileage
        linerogtsixtykMultilineTotalsale
        partsmatrixJson
      }
    }
  }
`;
export const GET_USERS_LIST = gql`
  query getUsersList($realm: String) {
    statelessKeycloakServiceGetUsers(realm: $realm)
  }
`;
export const INSERT_USER = gql`
  mutation insertUser(
    $email: String
    $firstname: String
    $lastname: String
    $username: String
  ) {
    statelessKeycloakServiceCreateUserWithoutPassword(
      input: {
        email: $email
        firstname: $firstname
        lastname: $lastname
        username: $username
      }
    ) {
      string
    }
  }
`;
export const UPDATE_USER_PASSWORD = gql`
  mutation updateUserPassword(
    $username: String
    $password: String
    $isTempPwdStatus: Boolean
  ) {
    statelessKeycloakServiceUpdateUserPassword(
      input: {
        username: $username
        password: $password
        isTempPwdStatus: $isTempPwdStatus
      }
    ) {
      string
    }
  }
`;
export const GET_REALMS_ROLES = gql`
  query getRealms($realm: String) {
    statelessKeycloakServiceGetRealmRoles(realm: $realm)
  }
`;

export const GET_PAYTYPE_FIXED_RATE_DETAILS = gql`
  mutation getPaytypeFixedRateDetails(
    $pAction: String
    $pLaborOrParts: String
    $pPaytype: String
    $pPartsFixedratevalue: String
    $pLaborFixedratevalue: BigFloat
    $pFixedratedate: Date
    $userid: String
    $storeId: String
  ) {
    statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype(
      input: {
        pAction: $pAction
        pFixedratedate: $pFixedratedate
        pLaborFixedratevalue: $pLaborFixedratevalue
        pLaborOrParts: $pLaborOrParts
        pPartsFixedratevalue: $pPartsFixedratevalue
        pPaytype: $pPaytype
        userid: $userid
        storeid: $storeId
      }
    ) {
      statelessCcPhysicalRwFixedRateMasterPaytypes {
        paytype
        laborFixedratevalue
        partsFixedratevalue
        fixedRateDate
        storeId
        lastUpdatedBy
        fixrateOrder
      }
    }
  }
`;
export const GET_PAYTYPE_FIXED_RATE_DETAILS_OLD = gql`
  mutation getPaytypeFixedRateDetails(
    $pAction: String
    $pLaborOrParts: String
    $pPaytype: String
    $pPartsFixedratevalue: String
    $pLaborFixedratevalue: BigFloat
    $pFixedratedate: Date
    $userid: String
  ) {
    statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype(
      input: {
        pAction: $pAction
        pFixedratedate: $pFixedratedate
        pLaborFixedratevalue: $pLaborFixedratevalue
        pLaborOrParts: $pLaborOrParts
        pPartsFixedratevalue: $pPartsFixedratevalue
        pPaytype: $pPaytype
        userid: $userid
      }
    ) {
      statelessCcPhysicalRwFixedRateMasterPaytypes {
        paytype
        laborFixedratevalue
        partsFixedratevalue
        fixedRateDate
        lastUpdatedBy
        fixrateOrder
      }
    }
  }
`;
export const GET_GROUPS = gql`
  query getGroups($realm: String) {
    statelessKeycloakServiceGetGroups(realm: $realm)
  }
`;
export const ASSIGN_USER_ROLE = gql`
  mutation assignUserRole($rolename: String, $username: String) {
    statelessKeycloakServiceAssignUserRole(
      input: { rolename: $rolename, username: $username }
    ) {
      string
    }
  }
`;
export const REMOVE_USER_ROLE = gql`
  mutation removeUserRole($rolename: String, $username: String) {
    statelessKeycloakServiceRemoveUserRole(
      input: { rolename: $rolename, username: $username }
    ) {
      string
    }
  }
`;
export const ASSIGN_USER_GRP = gql`
  mutation assignUserGroup(
    $parentGroupName: String
    $subgroupName: String
    $username: String
  ) {
    statelessKeycloakServiceSubgroupUserAdd(
      input: {
        parentGroupName: $parentGroupName
        subgroupName: $subgroupName
        userName: $username
      }
    ) {
      string
    }
  }
`;
export const REMOVE_USER_GRP = gql`
  mutation removeUserGroup(
    $parentGroupName: String
    $subgroupName: String
    $username: String
  ) {
    statelessKeycloakServiceSubgroupUserRemove(
      input: {
        parentGroupName: $parentGroupName
        subgroupName: $subgroupName
        userName: $username
      }
    ) {
      string
    }
  }
`;
export const GET_CUSTOMERHISTORY_DETAILS = gql`
  query getCustomerHistoryDetails {
    statelessCcAggregateGetCustomerHistories {
      nodes {
        ronumber
        customerName
        make
        makedesc
        model
        modeldesc
        year
        vehiclecolor
        vin
        mileage
        custno
        address
        citystatezip
        contactphonenumber
        zip
        licensenumber
        bookeddate
        bookedtime
        statuscode
        storeId
      }
    }
  }
`;

export const GET_SPECIALMETRICS_1357 = gql`
  mutation get_SpecialMetric_1357($advisor: [String!]) {
    statelessDbdSpecialMetricsGetAverageDaysOpenByPaytypesServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      averageDaysOpenByPaytypes {
        jsonData
      }
    }
  }
`;
export const UPDATE_USER_DETAILS = gql`
  mutation updateUserDetails(
    $username: String
    $newUsername: String
    $lastname: String
    $firstname: String
    $email: String
  ) {
    statelessKeycloakServiceUpdateUserWithUserDetails(
      input: {
        username: $username
        newUsername: $newUsername
        lastname: $lastname
        firstname: $firstname
        email: $email
      }
    ) {
      string
    }
  }
`;

export const GET_USER_ASSIGNED_ROLES = gql`
  query getUserAssignedRoles($userName: String) {
    statelessKeycloakServiceGetUserRealmRoles(userName: $userName)
  }
`;

export const GET_USER_ASSIGNED_GRPS = gql`
  query getUserAssignedGroups($userName: String) {
    statelessKeycloakServiceGetAssignedGroupsOfUsers(userName: $userName)
  }
`;

export const DELETE_USER_ = gql`
  mutation deleteUser($username: String) {
    statelessKeycloakServiceDeleteUser(input: { userName: $username }) {
      string
    }
  }
`;
export const GET_USER_CREDENTIALS = gql`
  query getUserCredentials($userName: String) {
    statelessKeycloakServiceGetUsersCredentials(userName: $userName)
  }
`;

export const GET_CUSTOMERHISTORY_DATA = gql`
  mutation getCustomerHistoryData($enddate: Date, $startdate: Date) {
    statelessCcAggregateGetCustomerHistoryDetails(
      input: { endDate: $enddate, startDate: $startdate }
    ) {
      customerHistories {
        address
        bookeddate
        bookedtime
        closeddate
        citystatezip
        contactphonenumber
        custno
        customerName
        licensenumber
        make
        makedesc
        mileage
        model
        modeldesc
        ronumber
        statuscode
        vehiclecolor
        vin
        year
        zip
      }
    }
  }
`;
export const GET_STORE_ADVISOR_DETAILS = gql`
  mutation getStoreAdvisorDetails {
    statelessDtkSourceRawGetEmployeesWithoutStore(input: {}) {
      statelessDtkSourceRawEmployeesWithoutStores {
        empEnable
        empId
        empName
        empRevertChange
      }
    }
  }
`;
export const MODIFY_STORE_ADVISOR_DETAILS = gql`
  mutation updatetStoreAdvisorDetails(
    $p_Advisors: JSON
    $store_id: String
    $user_id: String
  ) {
    statelessCcPhysicalRwUpdateRoStoreWithAdvisor(
      input: { pAdvisor: $p_Advisors, pStoreId: $store_id, userid: $user_id }
    ) {
      updateRoStoreWithAdvisorStatuses {
        status
      }
    }
  }
`;

export const GET_TECHMETRICS_1352 = gql`
  query gettechMetrics1352($techno: String) {
    statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianSoldhoursWeeklyAll(
      techno: $techno
    ) {
      nodes {
        estimatedTechnicianSoldhoursAll
        estimatedTechnicianSoldhoursCustomerpay
        estimatedTechnicianSoldhoursWarranty
        estimatedTechnicianSoldhoursInternal
        monthYear
        paytype
        storeId
        weekestartdate
        weekofmonth
      }
    }
  }
`;
export const GET_PAYTYPE_WITHOUT_STORE = gql`
  mutation getPayTypeWithoutStore {
    statelessCdkSourceRawGetPayTypesWithoutStore(input: {}) {
      statelessCdkSourceRawPayTypesWithoutStores {
        brand
        payType
        ptEnable
        ptRevertChange
        storeId
      }
    }
  }
`;
export const GET_STORE_ADVISOR_DETAILS_CDK = gql`
  mutation getStoreAdvisorDetailsCDK {
    statelessCdkSourceRawGetServiceAdvisorsWithoutStore(input: {}) {
      statelessCdkSourceRawServiceAdvisorsWithoutStores {
        empEnable
        empId
        empName
        empRevertChange
      }
    }
  }
`;
export const MODIFY_STORE_PAYTYPE_DETAILS = gql`
  mutation updateStorePaytypeDetails(
    $p_Paytypes: JSON
    $store_id: String
    $user_id: String
  ) {
    statelessCcPhysicalRwUpdateRoStoreWithPaytype(
      input: { pPaytype: $p_Paytypes, pStoreId: $store_id, userid: $user_id }
    ) {
      updateRoStoreWithPaytypeStatuses {
        status
      }
    }
  }
`;
export const GET_KPI_SCORE_CARD_DATA_STATUS = gql`
  mutation getKpiScoreCardDataStatus($toggle: String, $timeZone: String) {
    statelessDbdKpiScorecardGetDataStatus(
      input: { filterby: $toggle, timezoneOffset: $timeZone }
    ) {
      bigFloat
    }
  }
`;
export const UPDATE_TECH_TEAM_SETTINGS = gql`
  mutation updateTechteamSettings(
    $status: BitString
    $storeid: String
    $teamActive: BitString
    $teamAssigned: String
    $techName: String
    $userId: String
    $techno: String
    $oldTeamAssigned: String
  ) {
    statelessCcPhysicalRwUpdateTechniciansTeamSettings(
      input: {
        oldTeamAssigned: $oldTeamAssigned
        status: $status
        storeid: $storeid
        teamActive: $teamActive
        teamAssigned: $teamAssigned
        techName: $techName
        userId: $userId
        techno: $techno
      }
    ) {
      string
    }
  }
`;

export const GET_DATA_FOR_ADVISOR_AND_TECH_LABOR_MISSES = gql`
  mutation getDataForAdviorAndTechLaborMisses(
    $timeZone: String
    $payType: String
    $gridType: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $filterStart: Date
    $filterEnd: Date
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
      input: {
        timezoneOffset: $timeZone
        payType: $payType
        gridtype: $gridType
        advisor: $serviceAdvisor
        technician: $technician
        startDate: $filterStart
        endDate: $filterEnd
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridPricing
        gridDate
        compliance
        variance
        lbrsoldhours
        currentgridasof
        doorRate
        opcode
        opcodeDesc
        techName
        paytype
        paytypegroup
        model
        variancePerc
        targetElr
        actualElr
        fleetOrPaytypeOrOpcodeFixedRate
        storeId
        storeName
        gridType
        gridFor
        vin
        opcategory
        lbrcost
        lbractualhours
        prtextendedsale
        prtextendedcost
        lbrgrossprofit
        prtsgrossprofit
        elr
        markup
        lbrGrossprofitpercentage
        prtsGrossprofitpercentage
        make
        year
        mileage
        lbrsequenceno
      }
    }
  }
`;
export const GET_DATA_FOR_ADVISOR_AND_TECH_LABOR_MISSES_OLD = gql`
  mutation getDataForAdviorAndTechLaborMisses(
    $filterby: String
    $timeZone: String
    $payType: String
    $gridType: String
    $serviceAdvisor: [String!]
    $technician: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown(
      input: {
        filterby: $filterby
        timezoneOffset: $timeZone
        payType: $payType
        gridtype: $gridType
        advisor: $serviceAdvisor
        technician: $technician
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridPricing
        gridDate
        compliance
        variance
        lbrsoldhours
        currentgridasof
        doorRate
        opcode
        opcodeDesc
        techName
        paytype
        paytypegroup
        model
        variancePerc
        targetElr
        actualElr
        fleetOrPaytypeOrOpcodeFixedRate
      }
    }
  }
`;
export const GET_DATA_FOR_ADVISOR_AND_TECH_PARTS_MISSES = gql`
  mutation getDataForAdvisorAndTechPartsMisses(
    $timeZone: String
    $payType: String
    $serviceAdvisor: [String!]
    $technician: [String!]
    $filterStart: Date
    $filterEnd: Date
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
      input: {
        timezoneOffset: $timeZone
        payType: $payType
        advisor: $serviceAdvisor
        technician: $technician
        startDate: $filterStart
        endDate: $filterEnd
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridDate
        compliance
        variance
        targetPrice
        currentgridasof
        prtdesc
        prtpartno
        prtcost
        prtlist
        prtsource
        paytype
        paytypegroup
        lbropcode
        opcodedescription
        prtqtysold
        prtextendedsale
        prtextendedcost
        fleetOrPaytypeOrOpcodeFixedRate
        targetExtendedPrice
        variancePerc
        techName
        grossprofitpercentage
        storeId
        matrixType
        storeName
        partsFor
        elr
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrsequenceno
        lbrsoldhours
        make
        markup
        mileage
        model
        opcategory
        prtlaborsequenceno
        prtsequenceno
        prtsgrossprofit
        vin
        year
      }
    }
  }
`;
export const GET_DATA_FOR_ADVISOR_AND_TECH_PARTS_MISSES_OLD = gql`
  mutation getDataForAdvisorAndTechPartsMisses(
    $filterby: String
    $timeZone: String
    $payType: String
    $serviceAdvisor: [String!]
    $technician: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown(
      input: {
        filterby: $filterby
        timezoneOffset: $timeZone
        payType: $payType
        advisor: $serviceAdvisor
        technician: $technician
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns {
        opendate
        ronumber
        advisorName
        closeddate
        customerName
        lbrsale
        gridDate
        compliance
        variance
        targetPrice
        currentgridasof
        prtdesc
        prtpartno
        prtcost
        prtlist
        prtsource
        paytype
        paytypegroup
        lbropcode
        opcodedescription
        prtqtysold
        prtextendedsale
        prtextendedcost
        fleetOrPaytypeOrOpcodeFixedRate
        targetExtendedPrice
        variancePerc
        techName
        grossprofitpercentage
      }
    }
  }
`;
export const GET_CP_LABOR__REVENUE_DATA = gql`
  mutation get_CP_LABOR_REVENUE($advisor: [String!]) {
    statelessDbdCpLaborGetRevenueByYear(input: { advisor: $advisor }) {
      statelessDbdCpLaborRevenueByYears {
        jsonData
      }
    }
  }
`;

export const GET_CP_LABOR_GP_DATA = gql`
  mutation get_CP_LABOR_GP($advisor: [String!]) {
    statelessDbdCpLaborGetLaborProfitByYear(input: { advisor: $advisor }) {
      statelessDbdCpLaborLaborProfitByYears {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR_GP_PERC_DATA = gql`
  mutation get_CP_LABOR_GP_PERC($advisor: [String!]) {
    statelessDbdCpLaborGetGrossProfitPercentageServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborGrossProfitPercentageServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR_SOLDHOURS_DATA = gql`
  mutation get_CP_LABOR_SOLD_HOURS($advisor: [String!]) {
    statelessDbdCpLaborGetLaborSoldHoursByYear(input: { advisor: $advisor }) {
      statelessDbdCpLaborLaborSoldHoursByYears {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR_ELR_DATA = gql`
  mutation get_CP_LABOR_ELR($advisor: [String!]) {
    statelessDbdCpLaborGetEffectiveLaborRateServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborEffectiveLaborRateServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR_ELR_REPAIR_DATA = gql`
  mutation get_CP_LABOR_ELR_REPAIR($advisor: [String!]) {
    statelessDbdCpLaborGetElrRepairServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborElrRepairServiceAdvisors {
        jsonData
      }
    }
  }
`;

export const GET_CP_LABOR_COMP_MAINT_DATA = gql`
  mutation get_CP_LABOR_COMP_MAINT($advisor: [String!]) {
    statelessDbdCpLaborGetElrCompetitiveMaintenanceServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborElrCompetitiveMaintenanceServiceAdvisors {
        jsonData
      }
    }
  }
`;

export const GET_CP_LABOR_HOURS_PER_RO_DATA = gql`
  mutation get_CP_LABOR_HOURS_PER_RO($advisor: [String!]) {
    statelessDbdCpLaborGetHoursPerRepairOrderByYearServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborHoursPerRepairOrderByYearServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__AVG_SALE_DATA = gql`
  mutation get_CP_LABOR_AVG_SALE($advisor: [String!]) {
    statelessDbdCpLaborGetAverageRateByRepairOrderByYearServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborAverageRateByRepairOrderByYearServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__JOB_COUNT_DATA = gql`
  mutation get_CP_LABOR_JOB_COUNT($advisor: [String!]) {
    statelessDbdCpLaborGetRepairOrderCountCpByCategoryByYear(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborRepairOrderCountCpByCategoryByYears {
        jsonData
      }
    }
  }
`;

export const GET_CP_LABOR__RO_COUNT_DATA = gql`
  mutation get_CP_LABOR_RO_COUNT($advisor: [String!]) {
    statelessDbdCpLaborGetRepairOrderCountByYear(input: { advisor: $advisor }) {
      statelessDbdCpLaborRepairOrderCountByYears {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__DETAILS_GP_DATA = gql`
  mutation get_CP_LABOR_DETAILS_GP($advisor: [String!]) {
    statelessDbdCpLaborGetDetailLaborProfitCombined(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborDetailLaborProfitCombineds {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__DETAILS_SOLD_HOURS_DATA = gql`
  mutation get_CP_LABOR_DETAILS_SOLD_HOURS($advisor: [String!]) {
    statelessDbdCpLaborGetDetailLaborSoldHoursCombined(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborDetailLaborSoldHoursCombineds {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__DETAILS_JOB_COUNT_DATA = gql`
  mutation get_CP_LABOR_DETAILS_JOB_COUNT($advisor: [String!]) {
    statelessDbdCpLaborGetDetailJobCountByPaytypes(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborDetailJobCountByPaytypes {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__DETAILS_RO_COUNT_DATA = gql`
  mutation get_CP_LABOR_DETAILS_RO_COUNT($advisor: [String!]) {
    statelessDbdCpLaborGetDetailRepairOrderCountCombined(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborDetailRepairOrderCountCombineds {
        jsonData
      }
    }
  }
`;
export const GET_ELR_BY_PAY_TYPE = gql`
  mutation get_ELR_BY_PAY_TYPE($advisor: [String!]) {
    statelessDbdCpOverviewGetDetailElrByPaytype(input: { advisor: $advisor }) {
      statelessDbdCpOverviewDetailElrByPaytypes {
        jsonData
      }
    }
  }
`;

export const GET_SOLD_HOURS_BY_PAY_TYPE = gql`
  mutation get_SOLD_HOURS_BY_PAY_TYPE($advisor: [String!]) {
    statelessDbdCpOverviewGetDetailLaborSoldHours(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpOverviewDetailLaborSoldHours {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__DETAILS_REVENUE_DATA = gql`
  mutation get_CP_LABOR_DETAILS_REVENUE($advisor: [String!]) {
    statelessDbdCpLaborGetDetailRevenueByPaytypes(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborDetailRevenueByPaytypes {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR__DETAILS_ELR_DATA = gql`
  mutation get_CP_LABOR_DETAILS_ELR($advisor: [String!]) {
    statelessDbdCpLaborGetDetailElrByPaytype(input: { advisor: $advisor }) {
      statelessDbdCpLaborDetailElrByPaytypes {
        jsonData
      }
    }
  }
`;
export const GET_CP_LABOR_DETAILS_HOURS_PER_RO_DATA = gql`
  mutation get_CP_LABOR_DETAILS_HOURS_PER_RO($advisor: [String!]) {
    statelessDbdCpLaborGetDetailHoursPerRepairOrderByPaytypes(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborDetailHoursPerRepairOrderByPaytypes {
        jsonData
      }
    }
  }
`;

export const GET_CP_LABOR_DETAILS_AVG_SALE_RO_COUNT_DATA = gql`
  mutation get_CP_LABOR_DETAILS_AVG_SALE_RO_COUNT($advisor: [String!]) {
    statelessDbdCpLaborGetDetailAverageSaleRoCountByPaytypes(
      input: { advisor: $advisor }
    ) {
      statelessDbdCpLaborDetailAverageSaleRoCountByPaytypes {
        jsonData
      }
    }
  }
`;

export const GET_PARTS_REVENUE_BY_SERVICE_ADVISOR = gql`
  mutation getPartsRevenueByYear($advisor: [String!]) {
    statelessDbdCpPartsGetPartsRevenueByYear(input: { advisor: $advisor }) {
      partsRevenueByYears {
        jsonData
      }
    }
  }
`;

export const GET_PARTS_GROSSPROFIT_PERCENTAGE_BY_SERVICE_ADVISOR = gql`
  mutation getPartsGrossProfitPercentageServiceAdvisor($advisor: [String!]) {
    statelessDbdCpPartsGetPartsGrossprofitPercentageServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      partsGrossprofitPercentageServiceAdvisors {
        jsonData
      }
    }
  }
`;

export const GET_PARTS_MARKUP_BY_YEAR = gql`
  mutation getPartsmarkupByYear($advisor: [String!]) {
    statelessDbdCpPartsGetPartsmarkupByYear(input: { advisor: $advisor }) {
      partsmarkupByYears {
        jsonData
      }
    }
  }
`;

export const GET_PARTS_GROSS_PROFIT_BY_YEAR = gql`
  mutation getPartsGrossProfitByYear($advisor: [String!]) {
    statelessDbdCpPartsGetPartsProfitByYear(input: { advisor: $advisor }) {
      partsprofitByYears {
        jsonData
      }
    }
  }
`;
export const GET_PARTS_REVENUE_PER_RO_BY_YEAR = gql`
  mutation getPartsRevenuePerROByYear($advisor: [String!]) {
    statelessDbdCpPartsGetPartsRevenuePerRoByYear(
      input: { advisor: $advisor }
    ) {
      partsrevenuePerRoByYears {
        jsonData
      }
    }
  }
`;
export const GET_PARTS_MARKUP_REPAIR_AND_COMPETITIVE = gql`
  mutation getPartsMarkupRepairComp($advisor: [String!]) {
    statelessDbdCpPartsGetPartsmarkupRepairAndCompetitiveServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      partsmarkupRepairAndCompetitiveServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET_PARTS_RO_COUNT_BY_YEAR = gql`
  mutation getPartsROCountByYear($advisor: [String!]) {
    statelessDbdCpPartsGetRepairorderCountByYear(input: { advisor: $advisor }) {
      repairorderCountByYears {
        jsonData
      }
    }
  }
`;
export const GET_PARTS_HOURS_PER_RO_BY_YEAR = gql`
  mutation getPartsHoursPerROByYear($advisor: [String!]) {
    statelessDbdCpPartsGetHoursperRepairOrderByYear(
      input: { advisor: $advisor }
    ) {
      hoursperRepairOrderByYears {
        jsonData
      }
    }
  }
`;
export const GET_PARTS__RO_COUNT_BY_YEAR_PARTS_ONLY = gql`
  mutation getPartsROCountByYear($advisor: [String!]) {
    statelessDbdCpPartsGetPrtsrepairOrderCountByYear(
      input: { advisor: $advisor }
    ) {
      prtsrepairOrderCountByYears {
        jsonData
      }
    }
  }
`;

export const GET_CP_PARTS_DETAILS_REVENUE = gql`
  mutation getCpPartsDetailsRevenue($advisor: [String!]) {
    statelessDbdCpPartsGetDetailPartsrevenueByCategory(
      input: { advisor: $advisor }
    ) {
      detailPartsrevenueByCategories {
        jsonData
      }
    }
  }
`;
export const GET_CP_PARTS_DETAILS_GP = gql`
  mutation getCpPartsDetailsGP($advisor: [String!]) {
    statelessDbdCpPartsGetDetailPartsProfitByCategory(
      input: { advisor: $advisor }
    ) {
      detailpartsProfitByCategories {
        jsonData
      }
    }
  }
`;

export const GET_CP_PARTS_DETAILS_REVENUE_PER_RO_COUNT = gql`
  mutation getCpPartsDetailsRevenuePerRoCount($advisor: [String!]) {
    statelessDbdCpPartsGetDetailPartsRevenuePerRoCountByPaytypes(
      input: { advisor: $advisor }
    ) {
      detailpartsRevenuePerRoCountByPaytypes {
        jsonData
      }
    }
  }
`;

export const GET_CP_PARTS_DETAILS_RO_COUNT = gql`
  mutation getCpPartsDetailsRoCount($advisor: [String!]) {
    statelessDbdCpPartsGetDetailPartsRepairOrderCountCombined(
      input: { advisor: $advisor }
    ) {
      detailpartsRepairOrderCountCombineds {
        jsonData
      }
    }
  }
`;
export const GET_CP_PARTS_DETAILS_HOURS_PER_RO_PARTS_ONLY = gql`
  mutation getCpPartsDetailsHoursPerRoPartsOnly($advisor: [String!]) {
    statelessDbdCpPartsGetDetailhoursPerRepairOrderByPaytypes(
      input: { advisor: $advisor }
    ) {
      detailhoursPerRepairOrderByPaytypes {
        jsonData
      }
    }
  }
`;
export const GET_CP_PARTS_DETAILS_RO_COUNT_PARTS_ONLY = gql`
  mutation getCpPartsDetailsRoCountPartsOnly($advisor: [String!]) {
    statelessDbdCpPartsGetDetailpartsOnlyRepairOrderCountCombined(
      input: { advisor: $advisor }
    ) {
      detailpartsOnlyRepairOrderCountCombineds {
        jsonData
      }
    }
  }
`;
export const GET_DISCOUNT_LABOR_PARTS_DATA = gql`
  mutation getDiscountLaborandParts {
    statelessDbdDiscountsGetLaborAndPartsDiscountByMonth(input: {}) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;

export const GET_DISCOUNTS_BY_DISC_LEVEL = gql`
  mutation getDiscountsByDiscLevel {
    statelessDbdDiscountsGetDiscountsByDiscountlevelDetails(input: {}) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;

export const GET_DISCOUNTED_RO_PERCENT = gql`
  mutation getDiscountedRoPercent {
    statelessDbdDiscountsGetDiscountRoPercentageDetails(input: {}) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;

export const GET_PERCENT_DISCOUNTS_1115 = gql`
  mutation getPercentDiscounts1115 {
    statelessDbdDiscountsGetDiscountedSalePercentageDetails(input: {}) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;

export const GET_TOTAL_DISCOUNTS_1232 = gql`
  mutation getTotalDiscounts1232 {
    statelessDbdDiscountsGetDiscountPercDiscountedPerTotalCpDiscounts(
      input: {}
    ) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;
export const GET_TOTAL_DISCOUNTS_1236 = gql`
  mutation getTotalDiscounts1236 {
    statelessDbdDiscountsGetDiscountsPerRoDetails(input: {}) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;

export const GET__SPECIALMETRICS_AVERAGE_OPEN_DATE_DATA = gql`
  mutation getAverageOpenDate($advisor: [String!]) {
    statelessDbdSpecialMetricsGetAverageDaysOpenByPaytypesServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      averageDaysOpenByPaytypes {
        jsonData
      }
    }
  }
`;

export const GET__SPECIALMETRICS_SINGLE_LINE_RO_COUNT_PERC_DATA = gql`
  mutation getSingleLineRoCountPerc($advisor: [String!]) {
    statelessDbdSpecialMetricsGetSingleJobRoCountPercentageServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      singleJobRoCountPercentageServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_MULTI_LINE_RO_COUNT_PERC_DATA = gql`
  mutation getMultiLineRoCountPerc($advisor: [String!]) {
    statelessDbdSpecialMetricsGetMultiJobRoCountPercentageServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      multiJobRoCountPercentageServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_PARTS_TO_LABOR_RATIO_DATA = gql`
  mutation getPartsToLaborRatio($advisor: [String!]) {
    statelessDbdSpecialMetricsGetPartsToLaborRatioServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      partsToLaborRatioServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_LABOR_SOLD_HOURS_DATA = gql`
  mutation getSpecialMetricsLaborSoldHours($advisor: [String!]) {
    statelessDbdSpecialMetricsGetLaborSoldHoursByPaytypeServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      laborSoldHoursByPaytypeServiceAdvisors {
        jsonData
      }
    }
  }
`;

export const GET__SPECIALMETRICS_PARTS_TO_TABOR_RATIO_BY_CATEGORY_DATA = gql`
  mutation getSpecialMetricsPartsToLaborRatioByCategory($advisor: [String!]) {
    statelessDbdSpecialMetricsGetPartsToLaborRatioByCategoryServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      partsToLaborRatioByCategoryServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_MPI_PENETRATION_PERC_DATA = gql`
  mutation getSpecialMetricsMpiPenetration($advisor: [String!]) {
    statelessDbdSpecialMetricsGetMpiPenetrationPercentageServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      mpiPenetrationPercentageServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_MENU_PENETRATION_PERC_DATA = gql`
  mutation getSpecialMetricsMenuPenetration($advisor: [String!]) {
    statelessDbdSpecialMetricsGetMenuPenetrationPercentageServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      menuPenetrationPercentageServiceAdvisors {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_SHOP_SUPPLIES_DATA_OLD = gql`
  mutation getSpecialMetricsShopSupplies($advisor: [String!]) {
    statelessDbdSpecialMetricsGetShopSupplies(input: { advisor: $advisor }) {
      shopsupplies {
        jsonData
      }
    }
  }
`;
// export const GET__SPECIALMETRICS_SHOP_SUPPLIES_DATA = gql`
//   mutation getSpecialMetricsShopSupplies($advisor: [String!]) {
//     statelessDbdSpecialMetricsGetShopSupplies(input: { advisor: $advisor }) {
//       shopsupplies {
//         jsonData
//       }
//     }
//   }
// `;
export const GET__SPECIALMETRICS_SHOP_SUPPLIES_DATA = gql`
  mutation getSpecialMetricsShopSupplies($advisor: [String!]) {
    sstatelessDbdSpecialMetricsGetTotalShopsuppliesDetailsCombined(
      input: { advisor: $advisor }
    ) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_MULTI_JOB_RO_COUNTS_DATA = gql`
  mutation getSpecialMetricsMultiJobRoCounts($advisor: [String!]) {
    statelessDbdSpecialMetricsGetMultiJobRoCount(input: { advisor: $advisor }) {
      multijobRoCounts {
        jsonData
      }
    }
  }
`;
export const GET__SPECIALMETRICS_SINGLE_JOB_RO_COUNTS_DATA = gql`
  mutation getSpecialMetricsSingleJobRoCounts($advisor: [String!]) {
    statelessDbdSpecialMetricsGetSingleJobRoCount(
      input: { advisor: $advisor }
    ) {
      singlejobRoCounts {
        jsonData
      }
    }
  }
`;

export const GET__SPECIALMETRICS_RETURN_RATE_DATA = gql`
  mutation getSpecialMetricsReturnRateByServiceAdvisor($advisor: [String!]) {
    statelessDbdSpecialMetricsGetReturnrateByServiceAdvisor(
      input: { advisor: $advisor }
    ) {
      returnRateByServiceAdvisors {
        jsonData
      }
    }
  }
`;
// export const GET_DISCOUNT_LABOR_PARTS_DATA = gql`
//   mutation getDiscountLaborandParts($advisor: [String!]) {
//     statelessDbdDiscountsGetLaborAndPartsDiscountByMonth(
//       input: { advisor: $advisor }
//     ) {
//        {
//         jsonData
//       }
//     }
//   }
// `;

export const GET_OPPORTUNITY_LABOR_TOTAL_LABOR_OPPORTUNITY = gql`
  query getTotalLaborOpportunity($month_year: [String!]) {
    statelessDbdCpOpportunityLaborGetTotalLaborOpportunityCharts(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        lbrgrossOpportunity
        lbrjointOpportunity
        lbrvolumeOpportunity
        monthYear
        storeId
        rodate
        totalOpportunity
      }
    }
  }
`;

export const GET_OPPORTUNITY_PARTS_TOTAL_PARTS_OPPORTUNITY = gql`
  query getTotalPartsOpportunity($month_year: [String!]) {
    statelessDbdCpOpportunityPartsGetTotalPartsOpportunityCharts(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        monthYear
        prtgrossOpportunity
        prtjointOpportunity
        prtvolumeOpportunity
        rodate
        storeId
        totalOpportunity
      }
    }
  }
`;
export const GET_ELR_TOTAL_PRICING_OPPORTUNITY = gql`
  query getElrChartsPricingOpportunity($month_year: [String!]) {
    statelessDbdCpOpportunityElrGetChartsPricingElrOpportunityByCategory(
      filter: { monthYear: { in: $month_year } }
    ) {
      nodes {
        competitiveOpportunity
        maintenanceOpportunity
        repairOpportunity
        rodate
        storeId
        total
      }
    }
  }
`;
export const GET_LABOR_WORKMIX_DATA = gql`
  mutation getLaborWorkmixData($advisor: [String!]) {
    statelessDbdLaborWorkmixsGetLbrWorkmixPercentageByOpcategoryByAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessDbdLaborWorkmixLbrWorkmixPercentageByOpcategoryByAdvisors {
        jsonData
      }
    }
  }
`;
export const GET_PARTS_WORKMIX_DATA = gql`
  mutation getPartsWorkmixData($advisor: [String!]) {
    statelessDbdPartsWorkmixsGetPartsWorkmixPercentageByOpcategoryByAdvisor(
      input: { advisor: $advisor }
    ) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;
export const GET__TECHNICIAN_TECH_JOB = gql`
  mutation getTechnicianGetTechJob($techno: [String!]) {
    statelessDbdPeopleMetricsTechnicianGetTechjob(input: { techno: $techno }) {
      techjobs {
        jsonData
      }
    }
  }
`;
export const GET_TECHNICIAN_ESTIMATEDTECHNICIAN_SOLDHOURS_WEEKLY = gql`
  mutation getTechnicianGetTechSoldHoursWeekly($techno: [String!]) {
    statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianSoldhoursWeeklyall(
      input: { techno: $techno }
    ) {
      chartsEstimatedTechnicianSoldhoursWeeklyalls {
        jsonData
      }
    }
  }
`;
export const GET_TECHNICIAN_ESTIMATEDTECH_PRODUCTIVITY_WEEKLY = gql`
  mutation getTechnicianGetTechProductivityWeekly($techno: [String!]) {
    statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechProductivityWeekly(
      input: { techno: $techno }
    ) {
      chartsEstimatedTechProductivityWeeklies {
        jsonData
      }
    }
  }
`;

export const GET_TECHNICIAN_DRILLDOWN_TECH_HOURS = gql`
  mutation getTechnicianGetDrilldownTechHours(
    $paytype: [String!]
    $techno: [String!]
    $weekdate: Date
  ) {
    statelessCcDrilldownGetDrilldownTechnicianHours(
      input: {
        argpaytype: $paytype
        argtechno: $techno
        argweekdate: $weekdate
      }
    ) {
      drillDownTechnicianHrs {
        actualhoursnonzeroflatratehrs
        actualhrszeroflatratehrs
        closeddate
        estimatedtechefficiency
        flatratehrsnonzeroactualhrs
        flatratehours
        jobcountflathrsandactualhrs
        flatratehrszeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrtechname
        lbrsale
        lbrtechno
        monthYear
        zeroflatratehrsandzeroactualhrs
        techhours
        techefficiencynonzero
        techefficiency
        storeId
        prtssale
        nonzeroflatratehrsandzeroactualhrs
        nonzeroflatratehrsandnonzeroactualhrs
        totalros
      }
    }
  }
`;

export const GET_TECHNICIAN_SINGLE_JOB_RO_COUNT = gql`
  mutation getTechnicianGetTechSingleJobROCount($techno: [String!]) {
    statelessDbdPeopleMetricsTechnicianGetTechMetricsSingleJobRoCount(
      input: { techno: $techno }
    ) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_TECH_METRICS_SINGLE_JOB = gql`
  mutation getDrillDownDatForTechMetricsSingleJob(
    $month_year: String
    $techno: [String!]
  ) {
    statelessCcDrilldownGetDrillDownTechMetricsSingleJobRoCount(
      input: { monthyear: $month_year, techno: $techno }
    ) {
      statelessCcDrillDownDrillDownTechMetricsSingleJobRos {
        technicianName
        storeId
        ronumber
        prtsgrossprofit
        prtextendedcost
        prtextendedsale
        paytype
        opsubcategory
        mileage
        lbrtechno
        lbrsoldhours
        lbrsequencenoIdx
        lbrsale
        lbropcodedesc
        lbropcode
        lbrlinecode
        lbrgrossprofit
        lbrcost
        filterByRevenue
        filterByLaborparts
        elr
        closeddate
        lbrGrossprofitpercentage
        lbrsequenceno
        markup
        monthYear
        opcategory
        paytypegroup
        prtsGrossprofitpercentage
        roOpendate
        mappedPaytype
        mileage
        vin
      }
    }
  }
`;

export const GET_MPI_VALUE = gql`
  mutation getMpiValue {
    statelessCcPhysicalRwGetMpiSetup(input: {}) {
      mpidataSetups {
        isActive
        id
        frh
      }
    }
  }
`;
export const INSERT_MPI_VALUE = gql`
  mutation insertMpiValue($frhValue: BigFloat, $username: String) {
    statelessCcPhysicalRwInsertMpiSetup(
      input: { frhValue: $frhValue, username: $username }
    ) {
      clientMutationId
    }
  }
`;

export const GET_PARTS_MATRIX = gql`
  query GetPartsMatrix($pCallType: String, $storeid: String) {
    statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
      pCallType: $pCallType
      pStore: $storeid
    ) {
      nodes {
        addPercentage
        breakField
        calcBase
        createdDate
        matrixOrder
        matrixType
        priceEndRange
        priceStartRange
        prtsource
        prtsourceList
        storeInstallDate
      }
    }
  }
`;

export const GET_PARTS_MATRIX_DETAILS = gql`
  query GetPartsMatrixDetails(
    $pCallType: String
    $storeid: String
    $pMatrixType: String
    $pCreatedDate: Date
  ) {
    statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
      pCallType: $pCallType
      pStore: $storeid
      pMatrixType: $pMatrixType
      pCreatedDate: $pCreatedDate
    ) {
      nodes {
        addPercentage
        breakField
        calcBase
        createdDate
        matrixOrder
        matrixType
        priceEndRange
        priceStartRange
        prtsource
        prtsourceList
        storeInstallDate
      }
    }
  }
`;

export const GET_DRILL_DOWN_DATA_FOR_TECH_REPORT = gql`
  query GetTechSummaryTechHours(
    $paytypeData: String
    $month_year: [String!]
    $lbrtechno: String
  ) {
    statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHours(
      paytypeData: $paytypeData
      filter: {
        lbrtechno: { equalTo: $lbrtechno }
        monthYear: { in: $month_year }
      }
    ) {
      nodes {
        closeddate
        flatratehours
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrtechActive
        lbrsale
        lbrtechname
        lbrtechno
        monthYear
        prtssale
        storeId
        techefficiency
        techefficiencynonzero
        techhours
        totaljobs
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_FOR_TECH_REPORT_ALL = gql`
  query GetTechSummaryTechHours($paytypeData: String) {
    statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHours(
      paytypeData: $paytypeData
    ) {
      nodes {
        closeddate
        flatratehours
        jobcountflathrsandactualhrs
        jobcountflatratehrsandzeroactualhrs
        jobcountzeroflatratehrsandnonzeroactualhrs
        jobcountzeroflatratehrsandzeroactualhrs
        lbrtechActive
        lbrsale
        lbrtechname
        lbrtechno
        monthYear
        prtssale
        storeId
        techefficiency
        techefficiencynonzero
        techhours
        totaljobs
      }
    }
  }
`;

export const GET_KPI_COMPARATIVE_REPORT = gql`
  mutation getKpiComparativeReport(
    $argstartdate: Date
    $argenddate: Date
    $tech: [String!]
    $dayfilter: Int
    $advisor: [String!]
    $pReportType: String
    $timezoneOffset: String
    $techAdvisor: Int
    $filterby: String
    $payType: String
    $gridtype: String
    $selectedRecalc: String
    $recalcAdvisor: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardDetails(
      input: {
        argstartdate: $argstartdate
        argenddate: $argenddate
        tech: $tech
        dayfilter: $dayfilter
        pReportType: $pReportType
        advisor: $advisor
        timezoneOffset: $timezoneOffset
        techAdvisor: $techAdvisor
        payType: $payType
        gridtype: $gridtype
        filterby: $filterby
        selectedRecalc: $selectedRecalc
        recalcAdvisor: $recalcAdvisor
      }
    ) {
      kpiScorecardDetails {
        jsonData
      }
    }
  }
`;
export const GET_KPI_COMPARATIVE_STORE_REPORT = gql`
  mutation getKpiComparativeReport(
    $argstartdate: Date
    $argenddate: Date
    $dayfilter: Int
    $pReportType: String
    $timezoneOffset: String
    $filterby: String
    $payType: String
    $gridtype: String
    $avgRecalcStore: String
    $avgRecalc: String
    $recalcStore: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardDetailsStorewise(
      input: {
        argstartdate: $argstartdate
        argenddate: $argenddate
        dayfilter: $dayfilter
        pReportType: $pReportType
        timezoneOffset: $timezoneOffset
        payType: $payType
        gridtype: $gridtype
        filterby: $filterby
        avgRecalcStore: $avgRecalcStore
        avgRecalc: $avgRecalc
        recalcStore: $recalcStore
      }
    ) {
      kpiScorecardDetailsStorewises {
        jsonData
      }
    }
  }
`;

export const GET_KPI_SCORE_CARD_VALUES = gql`
  mutation getKPIScoredCardsValues(
    $argenddate: Date
    $argstartdate: Date
    $dayfilter: Int
    $advisor: [String!]
    $tech: [String!]
    $pReportType: String
    $timezoneOffset: String
    $techAdvisor: Int
    $filterby: String
    $payType: String
    $gridtype: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardDetailsHomepage(
      input: {
        argenddate: $argenddate
        argstartdate: $argstartdate
        dayfilter: $dayfilter
        advisor: $advisor
        tech: $tech
        pReportType: $pReportType
        timezoneOffset: $timezoneOffset
        techAdvisor: $techAdvisor
        filterby: $filterby
        payType: $payType
        gridtype: $gridtype
      }
    ) {
      kpiScorecardDetailsHomepages {
        kpiScorecardJson
      }
    }
  }
`;

export const GET_HOME_KPIS = gql`
  mutation getHomeKpis(
    $argenddate: Date
    $argstartdate: Date
    $advisor: [String!]
    $tech: [String!]
    $dayfilter: Int
    $pReportType: String
    $timezoneOffset: String
    $techAdvisor: Int
    $filterby: String
    $payType: String
    $gridtype: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardDetails(
      input: {
        argenddate: $argenddate
        argstartdate: $argstartdate
        advisor: $advisor
        tech: $tech
        dayfilter: $dayfilter
        pReportType: $pReportType
        timezoneOffset: $timezoneOffset
        techAdvisor: $techAdvisor
        filterby: $filterby
        payType: $payType
        gridtype: $gridtype
      }
    ) {
      kpiScorecardDetails {
        jsonData
      }
    }
  }
`;
export const GET_MENU_MODEL_DATA = gql`
  mutation getMenuModalData {
    statelessCcPhysicalRwGetMenuModels(input: {}) {
      getModels {
        make
        menuName
        modelName
      }
    }
  }
`;

export const CRUD_MENU_MODELS = gql`
  mutation crudMenuModels($pVal: JSON, $userid: String) {
    statelessCcPhysicalRwCrudMenuModels(
      input: { pVal: $pVal, userid: $userid }
    ) {
      string
    }
  }
`;

export const GET_VERSION_FLAGS = gql`
  mutation getVersionFlags {
    statelessCcPhysicalRwGetVersionFlags(input: {}) {
      getVerFlags {
        margument
        mvalue
      }
    }
  }
`;
export const GET_EDIT_HISTORY_TOGGLE_OPTIONS_WITH_TIMEZONE = gql`
  query getEditHistoryToggleOptionsWithTimeZone($timezoneOffset: String) {
    statelessCcPhysicalRoGetToggleDurationsEdithistory(
      timezoneOffset: $timezoneOffset
    ) {
      nodes {
        today
        yesterday
        lastweekstartdate
        lastweekenddate
        mtdstartdate
        mtdenddate
        lastmonthstartdate
        lastmonthenddate
        ytdstartdate
        ytdenddate
        lastyearstartdate
        lastyearenddate
        lastquarterstartdate
        lastquarterenddate
        lastthirtydaystartdate
        lastthirtydayenddate
        lastninetydaystartdate
        lastninetydayenddate
        lastsixmonthstartdate
        lastsixmonthenddate
        dayBeforeYesterday
        lastthreemonthstartdate
        lastthreemonthenddate
        lasttwelvemonthstartdate
        lasttwelvemonthenddate
        lastsevendaystartdate
        lastsevendayenddate
        thisweekstartdate
        thisweekenddate
        lasttwoweekstartdate
        lasttwoweekenddate
      }
    }
  }
`;
export const GET_VERSION_FLAG_BEFORE_LOGIN = gql`
  query getVersionFlags {
    statefulServiceConfigurationGetConfigVersionFlags {
      nodes {
        mvalue
        mkey
        mtenatName
      }
    }
  }
`;
export const GET_TECH_PRODUCTIVITY_ALL_ROS = gql`
  mutation getTechProductivityAllRos($techno: [String!]) {
    statelessDbdPeopleMetricsTechnicianGetChartsEstimatedTechnicianRocountWeeklyall(
      input: { techno: $techno }
    ) {
      statelessCcPhysicalRwGetAllSchemaFunctionReturns {
        jsonData
      }
    }
  }
`;

export const GET_LAST_THREE_YEARS = gql`
  query getlastThreeYears {
    statelessCcAggregateGetLastThreeYears(orderBy: MONTH_YEAR_ASC) {
      nodes {
        monthYear
      }
    }
  }
`;
export const GET_LABOR_MISSES_MODELS = gql`
  mutation getLaborMissesModels {
    statelessDbdKpiScorecardGetLaborMissesModels(input: {}) {
      laborMissesModels {
        gridType
        make
        model
        categorized
      }
    }
  }
`;
export const INSERT_MAKE_DETAILS = gql`
  mutation insertMakeDetails($userid: String, $pVal: JSON) {
    statelessDbdKpiScorecardLaborMissesModelMapping(
      input: { userid: $userid, pVal: $pVal }
    ) {
      string
    }
  }
`;
export const GET_LABOR_MISSES_GRID_TYPES = gql`
  mutation getLaborGridTypes(
    $userid: String
    $newGridtype: String
    $oldGridtype: String
    $pProcess: String
  ) {
    statelessDbdKpiScorecardGetorsetLaborMissesGridTypeMaster(
      input: {
        pProcess: $pProcess
        oldGridtype: $oldGridtype
        newGridtype: $newGridtype
        userid: $userid
      }
    ) {
      clientMutationId
      laborMissesGridTypes {
        gridType
      }
    }
  }
`;
export const PARTS_MATRIX_FILE_UPLOAD = gql`
  mutation PartsMatrixFileUpload(
    $base64Data: String!
    $inFileName: String!
    $inMatrixType: String
    $inInstallationDate: Date
    $inStoreId: String!
    $inTenantId: String!
    $inPrtsource: [String!]!
    $inCreatedUser: String!
    $inMatrixOrFleet: String
  ) {
    statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog(
      input: {
        base64Data: $base64Data
        inFileName: $inFileName
        inMatrixType: $inMatrixType
        inInstallationDate: $inInstallationDate
        inStoreId: $inStoreId
        inTenantId: $inTenantId
        inPrtsource: $inPrtsource
        inCreatedUser: $inCreatedUser
        inMatrixOrFleet: $inMatrixOrFleet
      }
    ) {
      clientMutationId
      results {
        msg
        status
      }
    }
  }
`;
export const GRID_TYPE_FILE_UPLOAD = gql`
  mutation GridTypeFileUpload(
    $base64Data: String!
    $inFileName: String!
    $inGridTypes: [String]
    $inInstallationDate: Date
    $inStoreId: String!
    $inTenantId: String!
    $inCreatedUser: String!
    $inGridOrFleet: String
  ) {
    statelessCcPhysicalRwInsertGriddataDtlFileUploadLogMultiple(
      input: {
        base64Data: $base64Data
        inFileName: $inFileName
        inGridTypes: $inGridTypes
        inInstallationDate: $inInstallationDate
        inStoreId: $inStoreId
        inTenantId: $inTenantId
        inCreatedUser: $inCreatedUser
        inGridOrFleet: $inGridOrFleet
      }
    ) {
      clientMutationId
      json
    }
  }
`;
export const PARTS_MATRIX_TYPE = gql`
  query GetPartsMatrix($storeid: String) {
    statelessCcPhysicalRwPartsMatrixTypeMasterDetails(
      filter: { storeId: { equalTo: $storeid } }
    ) {
      nodes {
        classificationType
        storeId
        tenantId
        value
      }
    }
  }
`;
export const GET_PARTS_SOURCE = gql`
  query GetPartsSource($storeid: String, $pCallType: String) {
    statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
      pCallType: $pCallType
      pStore: $storeid
    ) {
      nodes {
        prtsource
        prtsourceList
      }
    }
  }
`;
export const GET_MATRIX_DETAIL_NEW = gql`
  query getMatrixDetail(
    $gridormatrix: String
    $storeId: String
    $payType: String
    $partSrc: [String]
    $createddate: Date
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      storeid: $storeId
      gridormatrix: $gridormatrix
      partSrc: $partSrc
      payType: $payType
      createddate: $createddate
    ) {
      nodes {
        breakField
        calcBase
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        createdDate
        doorRate
        gridOrder
        gridormatrixtype
        hours
        partSource
        storeId
        storeInstallDate
      }
    }
  }
`;
export const GET_MATRIX_TYPE_NEW = gql`
  query getMatrixType(
    $gridormatrix: String
    $storeId: String
    $payType: String
    $partSrc: [String]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      storeid: $storeId
      gridormatrix: $gridormatrix
      partSrc: $partSrc
      payType: $payType
    ) {
      nodes {
        breakField
        calcBase
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        createdDate
        doorRate
        gridOrder
        gridormatrixtype
        hours
        partSource
        storeId
        storeInstallDate
      }
    }
  }
`;
export const INSERT_FLEET_DETAILS_NEW = gql`
  mutation updateGoalSettingsByGroupName(
    $user_id: String
    $fleet_rate_grid: JSON
    $fleet_rate_parts: JSON
    $fleet_exists_labor_grid: JSON
    $fleet_exists_parts_matrix: JSON
  ) {
    statelessCcPhysicalRwInsertOrUpdateFleetAccount(
      input: {
        fleetRateGrid: $fleet_rate_grid
        fleetRateParts: $fleet_rate_parts
        fleetExistsLaborGrid: $fleet_exists_labor_grid
        fleetExistsPartsMatrix: $fleet_exists_parts_matrix
        userid: $user_id
      }
    ) {
      string
    }
  }
`;
export const GET_PARTS_LIST_NEW = gql`
  query getMatrixType(
    $gridormatrix: String
    $storeId: String
    $payType: String
    $partSrc: [String]
    $pGridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      storeid: $storeId
      gridormatrix: $gridormatrix
      partSrc: $partSrc
      payType: $payType
      pGridorpartsfor: $pGridorpartsfor
    ) {
      nodes {
        breakField
        calcBase
        col0OrpriceStartRange
        col1OrpriceEndRange
        col2OraddPercentage
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        createdDate
        doorRate
        gridOrder
        gridormatrixtype
        hours
        partSource
        storeId
        storeInstallDate
      }
    }
  }
`;
export const PARTS_MATRIX_FILE_UPLOAD_NEW = gql`
  mutation PartsMatrixFileUpload(
    $base64Data: String!
    $inFileName: String!
    $inMatrixType: String
    $inInstallationDate: Date
    $inStoreId: String!
    $inTenantId: String!
    $inPrtsource: [String!]!
    $inCreatedUser: String!
    $inMatrixOrFleet: String!
  ) {
    statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog(
      input: {
        base64Data: $base64Data
        inFileName: $inFileName
        inMatrixType: $inMatrixType
        inInstallationDate: $inInstallationDate
        inStoreId: $inStoreId
        inTenantId: $inTenantId
        inPrtsource: $inPrtsource
        inCreatedUser: $inCreatedUser
        inMatrixOrFleet: $inMatrixOrFleet
      }
    ) {
      clientMutationId
      results {
        msg
        status
      }
    }
  }
`;
export const GRID_TYPE_FILE_UPLOAD_NEW = gql`
  mutation GridTypeFileUpload(
    $base64Data: String!
    $inFileName: String!
    $inGridType: String
    $inInstallationDate: Date
    $inStoreId: String!
    $inTenantId: String!
    $inCreatedUser: String!
  ) {
    statelessCcPhysicalRwInsertGriddataDtlFileUploadLog(
      input: {
        base64Data: $base64Data
        inFileName: $inFileName
        inGridType: $inGridType
        inInstallationDate: $inInstallationDate
        inStoreId: $inStoreId
        inTenantId: $inTenantId
        inCreatedUser: $inCreatedUser
      }
    ) {
      clientMutationId
      results {
        msg
        status
      }
    }
  }
`;
export const PARTS_MATRIX_TYPE_NEW = gql`
  query GetPartsMatrix($storeid: String) {
    statelessCcPhysicalRwPartsMatrixTypeMasterDetails(
      filter: { storeId: { equalTo: $storeid } }
    ) {
      nodes {
        classificationType
        storeId
        tenantId
        value
      }
    }
  }
`;

export const GET_PARTS_SOURCE_NEW = gql`
  query GetPartsSource($storeid: String, $pCallType: String) {
    statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
      pCallType: $pCallType
      pStore: $storeid
    ) {
      nodes {
        prtsource
        prtsourceList
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PAYTYPE_DETAILS_BY_GRID_TYPE = gql`
  query getGridorMatrixPayTypeDetailsByGridType(
    $gridormatrix: String
    $pGridorpartsfor: String
    $storeId: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      pGridorpartsfor: $pGridorpartsfor
      storeid: $storeId
    ) {
      nodes {
        gridormatrixtype
        gridorpartsfor
        gridOrder
      }
    }
  }
`;
export const GET_STORE_NICKNAME = gql`
  query GetStoreNickName($storeid: String) {
    statelessCcPhysicalRoGetStoreNicknameDetails(storeid: $storeid) {
      nodes {
        storeId
        storeName
        storeNickname
        fopcMonthlyFee
        dmsMonthlyFee
      }
    }
  }
`;
export const GET_GRIDORMETRIX_MISSES_DETAILS = gql`
  query getGridorMatrixMissesDetails($gridormatrix: String, $storeId: String) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      storeid: $storeId
    ) {
      nodes {
        dipsGridorpartsfor
      }
    }
  }
`;
export const GET_GRIDORMETRIX_PAYTYPE_DETAILS_REPORT = gql`
  query getGridorMatrixPayTypeDetails(
    $gridormatrix: String
    $storeId: String
    $gridorpartsfor: String
  ) {
    statelessDbdKpiScorecardGetKpiScorecardGridormatrix(
      gridormatrix: $gridormatrix
      storeid: $storeId
      pGridorpartsfor: $gridorpartsfor
    ) {
      nodes {
        gridormatrixtype
        gridorpartsfor
        dipsGridorpartsfor
      }
    }
  }
`;

export const GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_SOLD_HOURS = gql`
  query getDrillDownTechAdvisorsweeklySoldhours(
    $wstartdate: Date
    $wenddate: Date
    $lbrTechno: [String]
    $serviceAdvisor: [String]
    $monthyear: String
  ) {
    statelessCcDrilldownGetDrillDownTechAdvisorsweeklyRevenues(
      wstartdate: $wstartdate
      wenddate: $wenddate
      lbrTechno: $lbrTechno
      serviceAdvisor: $serviceAdvisor
      monthyear: $monthyear
    ) {
      nodes {
        advisorActive
        advisorName
        closeddate
        elr
        jobcount
        lbractualhours
        lbrcost
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrprofit
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechActive
        lbrtechname
        lbrtechno
        markup
        monthYear
        opcategory
        opsubcategory
        paytype
        paytypegroup
        prtscost
        prtsprofit
        prtssale
        roOpendate
        ronumber
        serviceadvisor
        storeId
        techefficiency
      }
    }
  }
`;

export const GET_DRILL_DOWN_TECH_ADVISORS_WEEKLY_REVENUES = gql`
  mutation getDrillDownTechAdvisorsweeklyRevenues(
    $wstartdate: Date
    $wenddate: Date
    $lbrTechno: [String]
    $serviceAdvisor: [String]
    $monthyear: String
    $paytypes: [String!]
  ) {
    statelessCcDrilldownGetDrillDownTechWeeklyDetails(
      input: {
        wstartdate: $wstartdate
        wenddate: $wenddate
        lbrTechno: $lbrTechno
        serviceAdvisor: $serviceAdvisor
        monthyear: $monthyear
        paytypes: $paytypes
      }
    ) {
      drillDownTechWeeklyDetails {
        ronumber
        monthYear
        closeddate
        serviceadvisor
        advisorName
        lbrtechname
        lbrtechno
        lbropcode
        lbropcodedesc
        opcategory
        opsubcategory
        paytype
        paytypegroup
        lbrsale
        lbrcost
        lbrprofit
        jobcount
        lbrsoldhours
        prtssale
        prtscost
        prtsprofit
        lbractualhours
        techefficiency
        elr
        markup
        lbrlinecode
        lbrsequenceno
        storeId
        advisorActive
        roOpendate
        lbrtechActive
        roType
      }
    }
  }
`;

export const GET_DRILLDOWN_TECH_RO_COUNT = gql`
  mutation getTechDrillDownAllRos(
    $argpaytype: [String]
    $argtechno: [String]
    $argweekdate: Date
  ) {
    statelessCcDrilldownGetDrillDownTechnicianRocount(
      input: {
        argpaytype: $argpaytype
        argtechno: $argtechno
        argweekdate: $argweekdate
      }
    ) {
      statelessCcDrilldownDrillDownTechnicianRocounts {
        closeddate
        lbrsale
        lbrtechname
        lbrtechno
        monthYear
        prtssale
        storeId
        totalros
      }
    }
  }
`;
export const GET_DATA_FOR_ONE_LINE_RO_DRILLDOWN = gql`
  mutation getDataForOneLineRoDrilldown(
    $startDate: Date
    $endDate: Date
    $isMileageBelow: String
    $tech: [String!]
    $advisor: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiOnelineDrilldown(
      input: {
        argstartdate: $startDate
        argenddate: $endDate
        isMileageBelow: $isMileageBelow
        tech: $tech
        advisor: $advisor
        onelineType: "S"
      }
    ) {
      kpiScorecardDetails {
        jsonData
      }
    }
  }
`;

export const GET_MPI_STATS_DRILLDOWN_REPORT = gql`
  mutation getmpistatsdrilldownreport(
    $startDate: Date
    $endDate: Date
    $tech: [String!]
    $advisor: [String!]
    $storeId: [String!]
  ) {
    statelessDbdKpiScorecardGetKpiScorecardMpiStatsDrillDownReport(
      input: {
        startDate: $startDate
        endDate: $endDate
        tech: $tech
        advisor: $advisor
        store: $storeId
      }
    ) {
      mpiStatsDrillDownReports {
        jsonData
      }
    }
  }
`;

export const INSERT_KPI_REPORT_NAME_old = gql`
  mutation insertKPIReportName(
    $iReportName: String!
    $iKpiReportType: String!
    $iKpiIds: [String!]
    $iStartDate: Date
    $iEndDate: Date
    $iStoreId: String!
    $iUserId: String!
  ) {
    statelessDbdKpiScorecardInsertKpiSavedReport(
      input: {
        iEndDate: $iEndDate
        iKpiIds: $iKpiIds
        iKpiReportType: $iKpiReportType
        iReportName: $iReportName
        iStartDate: $iStartDate
        iStoreId: $iStoreId
        iUserid: $iUserId
      }
    ) {
      string
    }
  }
`;

export const INSERT_KPI_REPORT_NAME = gql`
  mutation insertKPIReportName(
    $pProcess: String
    $pReportName: String
    $pKpiReportType: String
    $pKpiIds: [String!]
    $pStoreId: String
    $pUserid: String
    $pUserRole: String
    $pVisibility: String
    $pFilterBy: String
    $pSelectedGoals: [String]
    $pHiddenKpiSection: [String]
    $pHiddenKpiNumb: JSON
    $pKpiSortOrder: JSON
    $pHeaderSortList: [String!]
  ) {
    statelessDbdKpiScorecardInsertKpiSavedReport(
      input: {
        pProcess: $pProcess
        pReportName: $pReportName
        pKpiReportType: $pKpiReportType
        pKpiIds: $pKpiIds
        pStoreId: $pStoreId
        pUserid: $pUserid
        pUserRole: $pUserRole
        pVisibility: $pVisibility
        pFilterBy: $pFilterBy
        pSelectedGoals: $pSelectedGoals
        pHiddenKpiSection: $pHiddenKpiSection
        pHiddenKpiNumb: $pHiddenKpiNumb
        pKpiSortOrder: $pKpiSortOrder
        pHeaderSortList: $pHeaderSortList
      }
    ) {
      results {
        status
        msg
      }
    }
  }
`;
export const GET_KPI_SAVED_REPORTS = gql`
  query getKpiSavedReports {
    statelessDbdKpiScorecardGetKpiSavedReportNames {
      nodes {
        id
        reportName
      }
    }
  }
`;
export const GET_KPI_REPORT = gql`
  mutation getKpiReports($iReportName: String) {
    statelessDbdKpiScorecardGetKpiSavedReportDetails(
      input: { iReportName: $iReportName }
    ) {
      json
    }
  }
`;
export const GET_SAVED_REPORT_DETAILS = gql`
  mutation getKpiSavedReportDetails(
    $pStoreId: String
    $pUserRole: String
    $pUserid: String
    $timezoneOffset: String
  ) {
    statelessDbdKpiScorecardGetKpiSavedReportDetails(
      input: {
        pStoreId: $pStoreId
        pUserRole: $pUserRole
        pUserid: $pUserid
        timezoneOffset: $timezoneOffset
      }
    ) {
      json
    }
  }
`;

export const DELETE_KPI_REPORT = gql`
  mutation deleteKPIReport(
    $pProcess: String
    $pReportName: String
    $pKpiReportType: String
    $pStoreId: String
    $pUserid: String
    $pUserRole: String
  ) {
    statelessDbdKpiScorecardInsertKpiSavedReport(
      input: {
        pProcess: $pProcess
        pReportName: $pReportName
        pKpiReportType: $pKpiReportType
        pStoreId: $pStoreId
        pUserid: $pUserid
        pUserRole: $pUserRole
      }
    ) {
      results {
        status
        msg
      }
    }
  }
`;
export const GET_EMAIL = gql`
  mutation getEmail(
    $pNewMailId: [String!]
    $pOldMailId: [String!]
    $pProcess: String
    $pSource: String
    $pStoreId: String
    $pUserid: String
  ) {
    statelessCcPhysicalRwGetorsetMailIdMaster(
      input: {
        pNewMailId: $pNewMailId
        pOldMailId: $pOldMailId
        pProcess: $pProcess
        pSource: $pSource
        pStoreId: $pStoreId
        pUserid: $pUserid
      }
    ) {
      results {
        status
        value
      }
    }
  }
`;

export const SEND_SAVED_REPORT_EMAILS = gql`
  mutation sendSavedReportEmails(
    $pProcess: String
    $pStoreId: String
    $pReportName: String
    $pKpiReportType: String
    $pMailStatus: String
    $pMailFrequency: String
    $pScheduledOn: String
    $pRecipientId: String
    $pCcId: String
    $pBccId: String
    $pUserid: String
  ) {
    statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs(
      input: {
        pProcess: $pProcess
        pStoreId: $pStoreId
        pReportName: $pReportName
        pKpiReportType: $pKpiReportType
        pMailStatus: $pMailStatus
        pMailFrequency: $pMailFrequency
        pScheduledOn: $pScheduledOn
        pRecipientId: $pRecipientId
        pCcId: $pCcId
        pBccId: $pBccId
        pUserid: $pUserid
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardMailJobs {
        bccId
        ccId
        id
        kpiReportType
        mailFrequency
        mailStatus
        recipientId
        reportName
        scheduledOn
        storeId
      }
    }
  }
`;

export const GET_RECIPIENT_EMAILS = gql`
  mutation getRecipientEmails(
    $pProcess: String
    $pStoreId: String
    $pReportName: String
    $pKpiReportType: String
    $pMailStatus: String
    $pMailFrequency: String
    $pScheduledOn: String
    $pRecipientId: String
    $pCcId: String
    $pBccId: String
    $pOldRecipientId: String
    $pUserid: String
  ) {
    statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs(
      input: {
        pProcess: $pProcess
        pStoreId: $pStoreId
        pReportName: $pReportName
        pKpiReportType: $pKpiReportType
        pMailStatus: $pMailStatus
        pMailFrequency: $pMailFrequency
        pScheduledOn: $pScheduledOn
        pRecipientId: $pRecipientId
        pCcId: $pCcId
        pBccId: $pBccId
        pOldRecipientId: $pOldRecipientId
        pUserid: $pUserid
      }
    ) {
      statelessDbdKpiScorecardKpiScorecardMailJobs {
        bccId
        ccId
        id
        kpiReportType
        mailFrequency
        mailStatus
        recipientId
        reportName
        scheduledOn
        storeId
      }
    }
  }
`;
export const GET_PARTSMATRIX_DETAILS = gql`
  query getPartsMatrixDetails($pCallType: String!, $pStore: String!) {
    statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
      pStore: $pStore
      pCallType: $pCallType
    ) {
      nodes {
        priceStartRange
        priceEndRange
        addPercentage
        prtsource
        createdDate
        matrixType
        storeInstallDate
        calcBase
        breakField
        matrixOrder
        prtsourceList
        partsFor
        partsForDisplay
        prtsourceDisplay
      }
    }
  }
`;
export const GET_GRID_TYPE_OPTIONS = gql`
  query MyQuery($storeId: String!) {
    statelessCcPhysicalRwPartsMatrixTypeMasterDetails(
      condition: { storeId: $storeId }
    ) {
      nodes {
        classificationType
        storeId
        tenantId
        value
      }
    }
  }
`;

export const GET_MATRIX_DETAILS = gql`
  query MyQuery(
    $pCallType: String!
    $pStore: String!
    $pMatrixType: String!
    $pCreatedDate: Date!
    $pPrtsource: [String!]!
  ) {
    statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix(
      pCallType: $pCallType
      pStore: $pStore
      pMatrixType: $pMatrixType
      pCreatedDate: $pCreatedDate
      pPrtsource: $pPrtsource
    ) {
      edges {
        node {
          priceStartRange
          priceEndRange
          addPercentage
          prtsource
          createdDate
          matrixType
          storeInstallDate
          calcBase
          breakField
          partsFor
        }
      }
    }
  }
`;

export const INSERT_OR_UPDATE_PARTSMATRIX = gql`
  mutation statelessDbdKpiScorecardInsertOrUpdateKpiScorecardPartsMatrix(
    $pCall: String!
    $pStore: String!
    $pNewPrtsource: [String!]!
    $pOldPrtsource: [String!]!
    $pMatrixType: String!
    $pCreatedDate: Date!
    $pStoreInstallDate: Date!
    $pMatrix: JSON
    $pUserId: String!
    $pMatrixOrFleet: String!
  ) {
    statelessDbdKpiScorecardInsertOrUpdateKpiScorecardPartsMatrix(
      input: {
        pCall: $pCall
        pStore: $pStore
        pNewPrtsource: $pNewPrtsource
        pOldPrtsource: $pOldPrtsource
        pMatrixType: $pMatrixType
        pCreatedDate: $pCreatedDate
        pStoreInstallDate: $pStoreInstallDate
        pMatrix: $pMatrix
        pUserId: $pUserId
        pMatrixOrFleet: $pMatrixOrFleet
      }
    ) {
      results {
        msg
        rStatus
      }
    }
  }
`;

export const GET_LABOR_GRID_LIST = callType => {
  const fields =
    callType === 'Grid_Type'
      ? `{
        createdDate
        gridType
        storeInstallDate
        gridOrder
        gridFor
        gridForDisplay
      }`
      : `{
        col0
        col1
        col2
        col3
        col4
        col5
        col6
        col7
        col8
        col9
        hours
      }`;

  return gql`
    query GetLaborGridList(
      $pCallType: String!
      $pStore: String!
      $pGridType: String
      $pCreatedDate: Date
      $pGridFor: String
    ) {
      statelessDbdKpiScorecardBzoGetKpiScorecardLaborGrid(
        pCallType: $pCallType
        pStore: $pStore
        pGridType: $pGridType
        pCreatedDate: $pCreatedDate
        pGridFor:$pGridFor
      ) {
        nodes ${fields}
      }
    }
  `;
};

export const CREATE_MATRIX_NAME = gql`
  mutation createMatrixName(
    $inActivity: String!
    $inNewMatrixType: String!
    $inOldMatrixType: String!
    $inCreatedUser: String!
    $inStoreId: String!
    $inTenantId: String!
  ) {
    statelessCcPhysicalRwInsertPartsMatrixTypeMasterDetails(
      input: {
        inActivity: $inActivity
        inNewMatrixType: $inNewMatrixType
        inOldMatrixType: $inOldMatrixType
        inCreatedUser: $inCreatedUser
        inStoreId: $inStoreId
        inTenantId: $inTenantId
      }
    ) {
      results {
        msg
        status
      }
    }
  }
`;

export const FETCH_OPCODE_LIST = gql`
  mutation GetOpcodesListQuery($inStoreId: String) {
    statelessCcPhysicalRwGetRoOpcodesList(input: { inStoreId: $inStoreId }) {
      results {
        opcode
        storeId
      }
    }
  }
`;

export const INSERT_OR_UPDATE_GRID_NAME = gql`
  mutation InsertUpdateGridType(
    $inActivity: String!
    $inCreatedUser: String!
    $inNewGridName: String!
    $inOldGridName: String!
    $inIsDefaultGridName: Boolean!
    $inStoreId: String!
    $inTenantId: String!
  ) {
    statelessCcPhysicalRwInsertOrUpdateLaborGridTypeMasterDetails(
      input: {
        inActivity: $inActivity
        inCreatedUser: $inCreatedUser
        inNewGridName: $inNewGridName
        inOldGridName: $inOldGridName
        inIsDefaultGridName: $inIsDefaultGridName
        inStoreId: $inStoreId
        inTenantId: $inTenantId
      }
    ) {
      results {
        msg
        status
      }
    }
  }
`;

export const FETCH_GRID_TYPES = gql`
  query GetGridTypeMaster($storeId: String!) {
    statelessCcPhysicalRwLaborGridTypeMasterDetails(
      condition: { storeId: $storeId }
    ) {
      nodes {
        value
        isDefaultGridType
        gridCount
      }
    }
  }
`;

export const CREATE_GRID_WITH_DOOR_RATE = gql`
  mutation InsertGriddataDtl(
    $inCreatedDate: Date!
    $inGridTypes: [String]
    $inStoreId: String!
    $inDoorRate: BigFloat!
    $inGridFor: String!
  ) {
    statelessCcPhysicalRwInsertGriddataDtlMultiple(
      input: {
        inCreatedDate: $inCreatedDate
        inGridFor: $inGridFor
        inGridTypes: $inGridTypes
        inStoreId: $inStoreId
        inDoorRate: $inDoorRate
      }
    ) {
      clientMutationId
      json
    }
  }
`;

export const INSERT_OR_UPDATE_LABOR_GRID = gql`
  mutation InsertUpdateLaborGrid(
    $pStore: String!
    $pOldGridType: String!
    $pNewGridTypes: [String]
    $pGridFor: String!
    $pCreatedDate: Date!
    $pStoreInstallDate: Date!
    $pGrid: JSON
    $pUserId: String!
    $pCall: String!
    $pLaborMissType: String!
    $pConfirmFlag: String!
    $pIsDefault: String!
  ) {
    statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGridMultiple(
      input: {
        pStore: $pStore
        pNewGridTypes: $pNewGridTypes
        pOldGridType: $pOldGridType
        pGridFor: $pGridFor
        pCreatedDate: $pCreatedDate
        pStoreInstallDate: $pStoreInstallDate
        pGrid: $pGrid
        pCall: $pCall
        pLaborMissType: $pLaborMissType
        pUserId: $pUserId
        pConfirmFlag: $pConfirmFlag
        pIsDefault: $pIsDefault
      }
    ) {
      clientMutationId
      json
    }
  }
`;
export const UPDATE_OR_DELETE_LABOR_GRID = gql`
  mutation InsertUpdateLaborGrid(
    $pStore: String!
    $pOldGridType: String!
    $pNewGridType: String!
    $pGridFor: String!
    $pCreatedDate: Date!
    $pStoreInstallDate: Date!
    $pGrid: JSON
    $pUserId: String!
    $pCall: String!
    $pLaborMissType: String!
    $pConfirmFlag: String!
    $pIsDefault: String!
  ) {
    statelessDbdKpiScorecardInsertOrUpdateKpiScorecardLaborGrid(
      input: {
        pStore: $pStore
        pNewGridType: $pNewGridType
        pOldGridType: $pOldGridType
        pGridFor: $pGridFor
        pCreatedDate: $pCreatedDate
        pStoreInstallDate: $pStoreInstallDate
        pGrid: $pGrid
        pCall: $pCall
        pLaborMissType: $pLaborMissType
        pUserId: $pUserId
        pConfirmFlag: $pConfirmFlag
        pIsDefault: $pIsDefault
      }
    ) {
      clientMutationId
      results {
        msg
        rStatus
      }
    }
  }
`;
export const GET_RO_HIDE_RULES = gql`
  mutation GetRoHideRules($inStoreId: String) {
    statelessCcPhysicalRwGetRoHideRules(input: { inStoreId: $inStoreId }) {
      showHideRoRules {
        active
        columnName
        createdTime
        id
        lastUpdatedBy
        lastUpdatedOn
        operandValue
        operator
        storeId
      }
    }
  }
`;
export const GET_RO_HIDE_RULES_LIST = gql`
  mutation GetRoHideRules($inStoreId: String, $inType: String) {
    statelessCcPhysicalRwCrudRoHideRules(
      input: { inStoreId: $inStoreId, inType: $inType }
    ) {
      results {
        actionName
        actionValue
        active
        excepActionName
        excepActionValue
        excepDateName
        excepDateValueFrom
        excepDateValueTo
        execPending
        id
        msg
        roleType
        status
        rvalueOne
        rvalueThree
        rvalueTwo
      }
    }
  }
`;

export const EDIT_RO_HIDE_RULES_LIST = gql`
  mutation GetRoHideRules(
    $inStoreId: String
    $inType: String
    $inUserid: String
    $inActive: BitString
    $argId: Int
    $argRoleType: String
    $argActionName: String
    $argActionValue: String
    $argExcepActionName: String
    $argExcepActionValue: String
    $argExcepDateName: String
    $argExcepDateValueFrom: Date
    $argExcepDateValueTo: Date
    $inTypeView: BitString
  ) {
    statelessCcPhysicalRwCrudRoHideRules(
      input: {
        inStoreId: $inStoreId
        inType: $inType
        inUserid: $inUserid
        inActive: $inActive
        argId: $argId
        argRoleType: $argRoleType
        argActionName: $argActionName
        argActionValue: $argActionValue
        argExcepActionName: $argExcepActionName
        argExcepActionValue: $argExcepActionValue
        argExcepDateName: $argExcepDateName
        argExcepDateValueFrom: $argExcepDateValueFrom
        argExcepDateValueTo: $argExcepDateValueTo
        inTypeView: $inTypeView
      }
    ) {
      results {
        actionName
        actionValue
        active
        excepActionName
        excepActionValue
        excepDateName
        excepDateValueFrom
        excepDateValueTo
        execPending
        id
        msg
        roleType
        status
        rvalueOne
        rvalueThree
        rvalueTwo
      }
    }
  }
`;

export const VIEW_RO_HIDE = gql`
  mutation GetRoHideRules(
    $inStoreId: String
    $inType: String
    $inUserid: String
    $argId: Int
  ) {
    statelessCcPhysicalRwCrudRoHideRules(
      input: {
        inStoreId: $inStoreId
        inType: $inType
        inUserid: $inUserid
        argId: $argId
      }
    ) {
      results {
        actionName
        actionValue
        active
        excepActionName
        excepActionValue
        excepDateName
        excepDateValueFrom
        excepDateValueTo
        execPending
        id
        msg
        roleType
        status
        rvalueOne
        rvalueThree
        rvalueTwo
      }
    }
  }
`;
export const DELETE_RO_HIDE_RULES = gql`
  mutation GetRoHideRules(
    $inStoreId: String
    $inType: String
    $inUserid: String
    $argId: Int
  ) {
    statelessCcPhysicalRwCrudRoHideRules(
      input: {
        inStoreId: $inStoreId
        inType: $inType
        inUserid: $inUserid
        argId: $argId
      }
    ) {
      results {
        actionName
        actionValue
        active
        excepActionName
        excepActionValue
        excepDateName
        excepDateValueFrom
        excepDateValueTo
        execPending
        id
        msg
        roleType
        status
        rvalueOne
        rvalueThree
        rvalueTwo
      }
    }
  }
`;
export const INSERT_RO_HIDE_RULES = gql`
  mutation InsertRoHideRules(
    $inActive: BitString
    $inFeildName: String
    $inOperandValue: String
    $inOperator: String
    $inStoreId: String
    $inUserid: String
    $inType: String
  ) {
    statelessCcPhysicalRwInsertRoHideRules(
      input: {
        inActive: $inActive
        inFeildName: $inFeildName
        inOperandValue: $inOperandValue
        inOperator: $inOperator
        inStoreId: $inStoreId
        inUserid: $inUserid
        inType: $inType
      }
    ) {
      results {
        msg
        status
      }
    }
  }
`;
export const GET_DETAIL_SUMMARY_DATA = gql`
  mutation getDetailSummaryData(
    $currMonth: String
    $optMonth: String
    $department: String
    $serviceAdvisor: [String!]
  ) {
    statelessDbdRevenueSummaryGetDetailSummary(
      input: {
        _currMonth: $currMonth
        _userMonth: $optMonth
        _department: $department
        serviceAdvisor: $serviceAdvisor
      }
    ) {
      detailSummaryDetails {
        jsonData
      }
    }
  }
`;
export const GET_REVENUE_DETAILS_DRILL_DOWN = gql`
  mutation getDrillDownDataForRevenueSummary(
    $department: String
    $enddate: Date
    $monthyear: String
    $startdate: Date
  ) {
    statelessCcDrilldownGetDrillDownRevenuSummaryDetails(
      input: {
        _department: $department
        enddate: $enddate
        monthyear: $monthyear
        startdate: $startdate
      }
    ) {
      drillDownRevenuSummaryDetails {
        advisorName
        closeddate
        dominantElr
        dominantMarkup
        elr
        elrVariance
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        markup
        markupVariance
        mileage
        monthYear
        opcategory
        opendate
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        storeId
        techName
        vin
      }
    }
  }
`;

export const GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_LABOR_NEW = gql`
  mutation getRevenueSummaryWarrantyVolumesLaborNew(
    $currMonth: String
    $optMonth: String
    $department: String
    $serviceAdvisor: [String!]
    $technician: [String!]
  ) {
    statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesLaborByMonth(
      input: {
        _currMonth: $currMonth
        _userMonth: $optMonth
        _department: $department
        serviceAdvisor: $serviceAdvisor
        technician: $technician
      }
    ) {
      detailSummaryDetails {
        jsonData
      }
    }
  }
`;
export const GET_REVENUE_SUMMARY_WARRANTY_VOLUMES_PARTS_NEW = gql`
  mutation getRevenueSummaryWarrantyVolumesPartsNew(
    $currMonth: String
    $optMonth: String
    $department: String
    $serviceAdvisor: [String!]
    $technician: [String!]
  ) {
    statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesPartsByMonth(
      input: {
        _currMonth: $currMonth
        _userMonth: $optMonth
        _department: $department
        serviceAdvisor: $serviceAdvisor
        technician: $technician
      }
    ) {
      detailSummaryDetails {
        jsonData
      }
    }
  }
`;
export const GET_RO_HIDE_COLUMNS = gql`
  mutation GetRoHideColumns {
    statelessCcPhysicalRwGetRoHideColumns(input: {}) {
      fieldNames
    }
  }
`;

export const GET_RO_HIDE_OPERATOR = gql`
  mutation GetRoHideOperators {
    statelessCcPhysicalRwGetRoHideOperators(input: {}) {
      operatorVals
    }
  }
`;
export const GET_REVENUE_DETAILS_DRILL_DOWN_SA = gql`
  mutation getDrillDownDataForRevenueSummary(
    $department: String
    $enddate: Date
    $monthyear: String
    $startdate: Date
    $payType: String
    $serviceAdvisor: [String!]
  ) {
    statelessCcDrilldownGetDrillDownRevenuSummaryDetails(
      input: {
        _department: $department
        enddate: $enddate
        monthyear: $monthyear
        startdate: $startdate
        payType: $payType
        advisor: $serviceAdvisor
      }
    ) {
      drillDownRevenuSummaryDetails {
        advisorName
        closeddate
        dominantElr
        dominantMarkup
        elr
        elrVariance
        filterByLaborparts
        filterByRevenue
        lbrGrossprofitpercentage
        lbrcost
        lbrgrossprofit
        lbrlinecode
        lbropcode
        lbropcodedesc
        lbrsale
        lbrsequenceno
        lbrsoldhours
        lbrtechhours
        markup
        markupVariance
        mileage
        monthYear
        opcategory
        opendate
        opsubcategory
        paytype
        paytypegroup
        prtextendedcost
        prtextendedsale
        prtsGrossprofitpercentage
        prtsgrossprofit
        ronumber
        serviceadvisor
        storeId
        techName
        vin
      }
    }
  }
`;
export const GET_MONTHLY_CLIENT_REPORT_CARD_DETAILS = gql`
  mutation getMonthlyClientReportCardDetails(
    $measuredMon: String!
    $priorMon: String!
    $storeids: [String!]
  ) {
    statelessDbdKpiScorecardGetMonthlyClientReportcardDetails(
      input: {
        measuredMon: $measuredMon
        priorMon: $priorMon
        storeids: $storeids
      }
    ) {
      clientReportcardDetails {
        jsonData
      }
    }
  }
`;
export const GET_THREE_MONTH_CLIENT_REPORT_CARD_DETAILS = gql`
  mutation getThreeMonthClientReportCardDetails(
    $userselStartDate: Date
    $userselEndDate: Date
    $lastmonth: String
    $storeIds: [String!]
  ) {
    statelessDbdKpiScorecardGetThreeMonthsClientReportCard(
      input: {
        userselStartDate: $userselStartDate
        userselEndDate: $userselEndDate
        lastMonthYear: $lastmonth
        storeIds: $storeIds
      }
    ) {
      clientReportcardDetails {
        jsonData
      }
    }
  }
`;
export const GET_ONEMONTH_REPORT_MONTHS = gql`
  query getOneMonthReportMonths($storeId: String!) {
    statelessCcPhysicalRwGetClientreportOneMonthDefaultDate(
      pStoreId: $storeId
    ) {
      nodes {
        keyName
        keyValue
      }
    }
  }
`;
export const ENABLE_USER_MFA_STATUS = gql`
  mutation updateUserMfaStatus($username: String, $realmName: String) {
    statelessKeycloakServiceUpdateUserEnableMfa(
      input: { email: $username, realmName: $realmName }
    ) {
      string
    }
  }
`;
export const DISABLE_USER_MFA_STATUS = gql`
  mutation updateUserMfaStatus($username: String, $realmName: String) {
    statelessKeycloakServiceUpdateUserDisableMfa(
      input: { email: $username, realmName: $realmName }
    ) {
      string
    }
  }
`;
export const INSERT_KPI_MONTH_CARD_REPORT_NAME = gql`
  mutation insertKPIMonthCardReportName(
    $pProcess: String
    $pStoreId: String
    $pReportName: String
    $pToggleDuration: String
    $pStartdate: Date
    $pEnddate: Date
    $pPrevMonthyear: String
    $pCurrentMonthyear: String
    $pUserid: String
    $pKpiReportType: String
    $pKpiIds: [String!]
    $pVisibility: String
    $pUserRole: String
    $pWorkmixView: [String!]
    $pStoreSelected: [String!]
    $pRoiView: [String!]
  ) {
    statelessDbdKpiScorecardInsertKpireportcardSavedReport(
      input: {
        pProcess: $pProcess
        pStoreId: $pStoreId
        pReportName: $pReportName
        pToggleDuration: $pToggleDuration
        pStartdate: $pStartdate
        pEnddate: $pEnddate
        pPrevMonthyear: $pPrevMonthyear
        pCurrentMonthyear: $pCurrentMonthyear
        pUserid: $pUserid
        pKpiReportType: $pKpiReportType
        pKpiIds: $pKpiIds
        pVisibility: $pVisibility
        pUserRole: $pUserRole
        pWorkmixView: $pWorkmixView
        pStoreSelected: $pStoreSelected
        pRoiView: $pRoiView
      }
    ) {
      results {
        status
        msg
      }
    }
  }
`;
export const GET_SAVED_REPORT_CARD_DETAILS = gql`
  mutation getKpiSavedReportCardDetails(
    $pStoreId: String
    $pUserRole: String
    $pUserid: String
    $timezoneOffset: String
  ) {
    statelessDbdKpiScorecardGetKpiReportcardSavedReportDetails(
      input: {
        pStoreId: $pStoreId
        pUserRole: $pUserRole
        pUserid: $pUserid
        timezoneOffset: $timezoneOffset
      }
    ) {
      json
    }
  }
`;
export const GET_STORE_RETAIL_FLAGS = gql`
  mutation getStoreRetialFlags($pStoreId: String, $inPayType: String) {
    statelessCcPhysicalRwGetMappedPaytype(
      input: { inStoreId: $pStoreId, inPayType: $inPayType }
    ) {
      strings
    }
  }
`;
export const INSERT_MPI_STATS_REPORT_NAME = gql`
  mutation insertMpiStatsReportName(
    $pProcess: String
    $pStoreId: String
    $pReportName: String
    $pUserid: String
    $pReportType: String
    $pVisibility: String
    $pUserRole: String
    $pSortColumns: JSON
    $pFilterColumns: JSON
    $pTech: [String!]
    $pAdvisor: [String!]
    $pDisplayColumns: [String!]
    $pDisplayOrder: [String!]
    $pFilterBy: String
  ) {
    statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport(
      input: {
        pProcess: $pProcess
        pStoreId: $pStoreId
        pReportName: $pReportName
        pUserid: $pUserid
        pReportType: $pReportType
        pVisibility: $pVisibility
        pUserRole: $pUserRole
        pSortColumns: $pSortColumns
        pFilterColumns: $pFilterColumns
        pTech: $pTech
        pAdvisor: $pAdvisor
        pDisplayColumns: $pDisplayColumns
        pDisplayOrder: $pDisplayOrder
        pFilterBy: $pFilterBy
      }
    ) {
      results {
        status
        msg
      }
    }
  }
`;
export const INSERT_LABOR_MISSES_REPORT = gql`
  mutation insertLaborMissesSavededReport(
    $pProcess: String
    $pStoreId: String
    $pReportName: String
    $pFilterBy: String
    $pUserid: String
    $pReportType: String
    $pVisibility: String
    $pUserRole: String
    $pSortColumns: JSON
    $pFilterColumns: JSON
    $pTech: [String!]
    $pDisplayColumns: [String!]
    $pGridType: String
    $pJobType: String
    $pPaytype: String
    $pDisplayOrder: [String!]
    $pAdvisor: [String!]
  ) {
    statelessDbdKpiScorecardInsertLabormissesSavedReport(
      input: {
        pProcess: $pProcess
        pStoreId: $pStoreId
        pReportName: $pReportName
        pFilterBy: $pFilterBy
        pUserid: $pUserid
        pReportType: $pReportType
        pVisibility: $pVisibility
        pUserRole: $pUserRole
        pSortColumns: $pSortColumns
        pFilterColumns: $pFilterColumns
        pTech: $pTech
        pDisplayColumns: $pDisplayColumns
        pGridType: $pGridType
        pJobType: $pJobType
        pPaytype: $pPaytype
        pDisplayOrder: $pDisplayOrder
        pAdvisor: $pAdvisor
      }
    ) {
      results {
        status
        msg
      }
    }
  }
`;
export const INSERT_PARTS_MISSES_REPORT = gql`
  mutation insertPartsMissesSavededReport(
    $pProcess: String
    $pStoreId: String
    $pReportName: String
    $pFilterBy: String
    $pUserid: String
    $pReportType: String
    $pVisibility: String
    $pUserRole: String
    $pSortColumns: JSON
    $pFilterColumns: JSON
    $pTech: [String!]
    $pDisplayColumns: [String!]
    $pGridType: String
    $pJobType: String
    $pPaytype: String
    $pDisplayOrder: [String!]
    $pAdvisor: [String!]
  ) {
    statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport(
      input: {
        pProcess: $pProcess
        pStoreId: $pStoreId
        pReportName: $pReportName
        pFilterBy: $pFilterBy
        pUserid: $pUserid
        pReportType: $pReportType
        pVisibility: $pVisibility
        pUserRole: $pUserRole
        pSortColumns: $pSortColumns
        pFilterColumns: $pFilterColumns
        pTech: $pTech
        pDisplayColumns: $pDisplayColumns
        pGridType: $pGridType
        pJobType: $pJobType
        pPaytype: $pPaytype
        pDisplayOrder: $pDisplayOrder
        pAdvisor: $pAdvisor
      }
    ) {
      results {
        status
        msg
      }
    }
  }
`;

export const INSERT_KPI_ONELINE_SAVED_REPORT = gql`
  mutation insertKpiOnelineSavedReport(
    $pProcess: String
    $pStoreId: String
    $pReportName: String
    $pFilterBy: String
    $pReportType: String
    $pAdvisor: [String]
    $pTech: [String]
    $pUserid: String
    $pUserRole: String
    $pVisibility: String
    $pDisplayColumns: [String]
    $pSortColumns: JSON
    $pFilterColumns: JSON
    $pDisplayOrder: [String]
    $pIsMileageBelow: String
    $pOnelineType: String
  ) {
    statelessDbdKpiScorecardInsertKpiOnelineSavedReport(
      input: {
        pProcess: $pProcess
        pStoreId: $pStoreId
        pReportName: $pReportName
        pFilterBy: $pFilterBy
        pReportType: $pReportType
        pAdvisor: $pAdvisor
        pTech: $pTech
        pUserid: $pUserid
        pUserRole: $pUserRole
        pVisibility: $pVisibility
        pDisplayColumns: $pDisplayColumns
        pSortColumns: $pSortColumns
        pFilterColumns: $pFilterColumns
        pDisplayOrder: $pDisplayOrder
        pIsMileageBelow: $pIsMileageBelow
        pOnelineType: $pOnelineType
      }
    ) {
      results {
        status
        msg
      }
    }
  }
`;
export const GET_DRILL_DOWN_DATA_LABOR_PARTS_DISCOUNT = gql`
  mutation getDrillDownDataForLaborPartsDiscount(
    $month_year: String!
    $storeid: String
  ) {
    statelessCcDrilldownGetDrillDownDiscountedLaborPartsDetails(
      input: { storeid: $storeid, monthyear: $month_year }
    ) {
      drillDownDiscountedLaborPartsDetails {
        ronumber
        disdiscountid
        monthYear
        serviceadvisor
        advisorName
        paytype
        paytypegroup
        opcategory
        dislevel
        lbrlinecode
        lbrsequenceno
        prtlaborsequenceno
        prtlinecode
        lbrsale
        lbrcost
        prtssale
        prtcost
        salepercentage
        apportionedlbrdiscount
        labordiscount
        partsdiscount
        disdesc
        lbropcode
        lbropcodedesc
        prtsequenceno
        saleaftersdiscount
        prtsaleafterdiscount
        storeId
        closeddate
        sourceType
      }
    }
  }
`;
export const GET_CP_LABOR_OVERVIEW_DATA = gql`
  mutation getCPLaborOverviewData($advisor: [String!], $chartIds: [String!]) {
    statelessDbdCpLaborCpLaborOverview(
      input: { advisor: $advisor, chartIds: $chartIds }
    ) {
      statelessDbdCpLaborRevenueByYears {
        jsonData
      }
    }
  }
`;
export const GET_CP_PARTS_OVERVIEW_DATA = gql`
  mutation getCPPartsOverviewData($advisor: [String!], $chartIds: [String!]) {
    statelessDbdCpPartsCpPartsOverview(
      input: { advisor: $advisor, chartIds: $chartIds }
    ) {
      statelessDbdCpPartsRevenueByYears {
        jsonData
      }
    }
  }
`;
export const GET_CP_SUMMARY_OVERVIEW_DATA = gql`
  mutation getCPSummaryOverviewData($advisor: [String!], $chartIds: [String!]) {
    statelessDbdCpOverviewCpSummaryOverview(
      input: { advisor: $advisor, chartIds: $chartIds }
    ) {
      statelessDbdCpOverviewRevenueByYears {
        jsonData
      }
    }
  }
`;
export const GET_COMPARISON_DATA_MONTHWISE_LABOR_ALL = gql`
  mutation getDrillDownDataForWorkmixChartsAll(
    $mon1: String
    $mon2: String
    $chartIds: [String!]
  ) {
    statelessDbdLaborWorkmixGetWorkmixChartsMonthlyComparisonAll(
      input: { mon1: $mon1, mon2: $mon2, chartIds: $chartIds }
    ) {
      statelessDbdLaborWorkmixChartLaborMonthlyComparisonTests {
        jsonData
      }
    }
  }
`;
export const GET_SERVICE_EFFICIENCY_BY_MONTHS_ALL = gql`
  mutation getServiceEfficiencyByMonthsAll(
    $mon1: String
    $mon2: String
    $chartIds: [String!]
  ) {
    statelessDbdPeopleMetricsServiceAdvisorRevenueMonthlyComparisonAll(
      input: { mon1: $mon1, mon2: $mon2, chartIds: $chartIds }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorRevenueComparisonAlls {
        jsonData
      }
    }
  }
`;
export const GET_COMPARISON_DATA_MONTHWISE_PARTS_ALL = gql`
  query getDrillDownDataForWorkmixChartsAll(
    $mon1: String
    $mon2: String
    $chartIds: [String!]
  ) {
    statelessDbdPartsWorkmixGetWorkmixChartPartsMonthlyComparisonAll(
      mon1: $mon1
      mon2: $mon2
      chartIds: $chartIds
    ) {
      nodes {
        jsonData
      }
    }
  }
`;
export const GET_SERVICE_ADVISOR_EFFICIENCY_BY_OPCATEGORY_ALL = gql`
  mutation getServiceAdvisorEfficiencyByOpCategory(
    $mon: String
    $chartIds: [String!]
  ) {
    statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategoryByMonthAll(
      input: { mon: $mon, chartIds: $chartIds }
    ) {
      statelessDbdPeopleMetricsServiceAdvisorRevenueComparisonAlls {
        jsonData
      }
    }
  }
`;
export const GET_TECHNICIAN_REVENUE_AND_HOURS_ALL = gql`
  mutation getTechEfficiencyRevenueHoursAll(
    $mon1: String
    $mon2: String
    $chartIds: [String!]
  ) {
    statelessDbdPeopleMetricsTechnicianGetTechRevenueAndHoursByNameAll(
      input: { mon1: $mon1, mon2: $mon2, chartIds: $chartIds }
    ) {
      techRevenueAndHoursByNameAlls {
        jsonData
      }
    }
  }
`;
export const GET_SPECIAL_METRICS_CHART_DATA = gql`
  mutation getSpecialMetricsData($advisor: [String!], $chartIds: [String!]) {
    statelessDbdSpecialMetricsGetJobRoCountSinglelineMultiline(
      input: { advisor: $advisor, chartIds: $chartIds }
    ) {
      singlejobRoCounts {
        jsonData
      }
    }
  }
`;
export const GET_SPECIAL_METRICS_SUMMARY_DATA = gql`
  mutation getSpecialMetricsSummaryData(
    $advisor: [String!]
    $chartIds: [String!]
  ) {
    statelessDbdSpecialMetricsGetServiceAdvisorOperationSummary(
      input: { advisor: $advisor, chartIds: $chartIds }
    ) {
      laborSoldHoursByPaytypeServiceAdvisors {
        jsonData
      }
    }
  }
`;
