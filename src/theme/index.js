import { createMuiTheme } from '@material-ui/core';
import baseTheme from './baseTheme';
import baseThemeArmatus from './baseThemeArmatus';

let Dealer = process.env.REACT_APP_DEALER;
let baseThemes = '';
if(Dealer === 'Armatus') {
   baseThemes = baseThemeArmatus(); 
} else {
   baseThemes = baseTheme(); 
}
export const theme = createMuiTheme(baseThemes);
export const themeWithRtl = createMuiTheme({ ...baseThemes, direction: 'rtl' });
