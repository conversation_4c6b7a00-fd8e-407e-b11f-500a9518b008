import { colors } from '@material-ui/core';

const white = '#FFFFFF';

export default {
  primary: {
    contrastText: white,
    dark: '#C2185B',
    main: '#C2185B',
    light: '#F8BBD0',
    active: '#F4E1E7',
    topGradient:
      'rgba(0, 0, 0, 0) linear-gradient(to right, rgba(181, 218, 246, 0.06), rgba(136, 188, 227, 0),rgb(221, 221, 221), rgba(219, 224, 227, 0.96),rgb(170, 191, 213), rgba(52, 127, 184, 0.98),rgb(39, 114, 172),rgb(30, 98, 150), rgb(22, 90, 142),rgb(15, 82, 133), rgb(6, 70, 119),rgb(5, 67, 114),rgb(0, 61, 107), rgb(5, 67, 114), rgb(6, 70, 119), rgb(15, 82, 133), rgb(22, 90, 142), rgb(30, 98, 150), rgb(39, 114, 172), rgb(52, 127, 184), rgb(70, 140, 193), rgb(118, 165, 201), rgb(164, 189, 210), rgb(232, 237, 242), rgb(230, 235, 241)) repeat scroll 0% 0%',
    navGradient:
      'linear-gradient(to right, rgb(5, 67, 114),rgb(0, 61, 107), rgb(5, 67, 114), rgb(6, 70, 119), rgb(15, 82, 133), rgb(22, 90, 142), rgb(30, 98, 150), rgb(39, 114, 172), rgb(52, 127, 184) )'
  },
  secondary: {
    contrastText: white,
    dark: '#C2185B',
    main: '#E91E63',
    light: '#FF4081'
  },
  error: {
    contrastText: white,
    dark: colors.red[900],
    main: colors.red[600],
    light: colors.red[400]
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
    link: colors.blue[600]
  },
  link: colors.blue[800],
  icon: colors.blueGrey[600],
  background: {
    default: '#F4F6F8',
    paper: white
  },
  divider: colors.grey[200]
};
