import { colors } from '@material-ui/core';

const white = '#FFFFFF';

export default {
  primary: {
    contrastText: white,
    dark: '#003d6b',
    main: '#003d6b',
    light: '#acd4f4',
    active: '#ddeaf4',
    footerGradient:
      'linear-gradient(90deg, rgba(242,245,247,1) 0%, rgba(53,126,183,1) 15%, rgba(3,64,111,1) 50%, rgba(53,126,183,1) 95%, rgba(242,245,247,1) 100%)',
    topGradient:
      'linear-gradient(90deg, rgba(242,245,247,1) 0%, rgba(53,126,183,1) 15%, rgba(3,64,111,1) 50%, rgba(53,126,183,1) 95%, rgba(242,245,247,1) 100%)',
    navGradient:
      'linear-gradient(to right, rgb(5, 67, 114),rgb(0, 61, 107), rgb(5, 67, 114), rgb(6, 70, 119), rgb(15, 82, 133), rgb(22, 90, 142), rgb(30, 98, 150), rgb(39, 114, 172), rgb(52, 127, 184) )'
  },
  secondary: {
    contrastText: white,
    dark: '#003d6b',
    main: '#084573',
    light: '#79a0be'
  },
  error: {
    contrastText: white,
    dark: colors.red[900],
    main: colors.red[600],
    light: colors.red[400]
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
    link: colors.blue[600]
  },
  button: {
    primary: '#ee7600'
  },
  link: colors.blue[800],
  icon: colors.blueGrey[600],
  background: {
    default: '#F4F6F8',
    paper: white
  },
  divider: colors.grey[200]
};
