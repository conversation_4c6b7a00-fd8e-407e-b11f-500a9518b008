.input-container {
  position: relative;
}

input.datepicker {
  height: 35px;
  width: 180px;
  border: 1px solid #c0c0c0;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 16px;
}
.fleet-grid input.datepicker {
  height: 35px;
  width: 100px;
  border: 1px solid #c0c0c0;
  border-radius: 4px;
  box-sizing: border-box;
  padding: 16px;
}
.labelpicker {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 16px;
  display: flex;
  align-items: center;
  pointer-events: none;
}
.rolabelpicker {
  position: absolute;
  top: 0;
  bottom: 0;

  display: flex;
  align-items: center;
  pointer-events: none;
}
.fleet-picker {
  font-weight: lighter !important;
}
input.datepicker,
.labelpicker .textpicker {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  font-weight: bold !important;
}
input.rodatepicker,
.rolabelpicker .textpicker {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  padding-left: 10px;
  /* font-weight: bold !important; */
}

input.fleet-picker-cls,
.labelpicker .textpicker {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  font-weight: normal !important;
}

.fleet-grid input.datepicker,
.labelpicker .textpicker {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  /* font-weight: 400 !important; */
}
.labelpicker .textpicker {
  transition: all 0.15s ease-out;
  color: #212121;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
  font-weight: 400;
}
.rolabelpicker .textpicker {
  transition: all 0.15s ease-out;
  color: #212121;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
  font-weight: 400;
}
.fleet-picker-label {
  margin-top: 13px !important;
}

input.datepicker:focus {
  outline: none;
  border: 2px solid rgb(0, 61, 107);
}
.daterangepicker .drp-calendar.left div {
  padding: 8px 0 165px 8px !important;
}
.daterangepicker .drp-calendar.single .calendar-table {
  height: 210px !important;
}
.daterangepicker .ranges ul {
  width: auto !important;
  font-weight: 700;
  font-size: 12px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
input.datepicker:focus + .labelpicker .textpicker,
:not(input.datepicker[value='']) + .labelpicker .textpicker {
  font-size: 11px;
  transform: translate(0, -150%);
  background-color: white;
  padding-left: 4px;
  padding-right: 4px;
  margin-top: 5px;
  /* font-weight: bold !important; */
}

input.datepicker:focus + .labelpicker .textpicker {
  color: #003d6b;
}

input.rodatepicker:focus + .rolabelpicker .textpicker,
:not(input.rodatepicker[value='']) + .rolabelpicker .textpicker {
  font-size: 11px;
  transform: translate(0, -150%);
  background-color: white;
  padding-left: 4px;
  padding-right: 4px;
  /* font-weight: bold !important; */
}

input.rodatepicker:focus + .rolabelpicker .textpicker {
  color: #003d6b;
}
.daterangepicker {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif !important;
}
