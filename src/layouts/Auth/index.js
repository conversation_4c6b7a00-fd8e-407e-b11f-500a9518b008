import React, { Suspense, useEffect } from 'react';
import { renderRoutes } from 'react-router-config';
import { Redirect, useParams } from 'react-router-dom';
import PropTypes from 'prop-types';
import $ from 'jquery';
import { useHistory } from 'react-router';
import { makeStyles } from '@material-ui/styles';
import { LinearProgress } from '@material-ui/core';
import Topbar from './Topbar';

const useStyles = makeStyles(theme => ({
  container: {
    minHeight: '100vh',
    display: 'flex',
    '@media all and (-ms-high-contrast:none)': {
      height: 0 // IE11 fix
    }
  },
  content: {
    flexGrow: 1,
    maxWidth: '100%',
    overflowX: 'hidden',
    overflowY: 'hidden',
    paddingTop: 64,
    [theme.breakpoints.down('xs')]: {
      paddingTop: 56
    }
  }
}));

function Auth({ route }) {
  const classes = useStyles();
  const history = useHistory();
  let { test } = useParams();
  useEffect(() => {
    var url = window.location.search;

    localStorage.setItem(
      'provenance',
      typeof url != 'undefined' && url.split('?provenance=').pop()
    );
  }, []);
  // $().ready(function() {
  //   if (
  //     window.location.pathname != '/auth/login' ||
  //     window.location.pathname != '/login'
  //   ) {
  //     window.history.pushState(null, null, 'login');
  //     window.addEventListener('popstate', function() {
  //       window.history.pushState(null, null, '/');
  //     });
  //   }
  // });
  return (
    <>
      <Topbar />
      <div className={classes.container}>
        <div className={classes.content}>
          <Suspense fallback={<LinearProgress />}>
            {renderRoutes(route.routes)}
          </Suspense>
        </div>
      </div>
    </>
  );
}

Auth.propTypes = {
  route: PropTypes.object
};

export default Auth;
