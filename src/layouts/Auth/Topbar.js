import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import Popover from '@material-ui/core/Popover';
import Tooltip from '@material-ui/core/Tooltip';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import ListSubheader from '@material-ui/core/ListSubheader';
import AccountCircle from '@material-ui/icons/AccountCircle';
import Divider from '@material-ui/core/Divider';
import List from '@material-ui/core/List';
import ListItem from '@material-ui/core/ListItem';
import {
  AppBar,
  Toolbar,
  Button,
  Typography,
  IconButton,
  Paper,
  MenuItem,
  MenuList
} from '@material-ui/core';
import { getAllChartDetails, getTimeZone } from '../../utils/Utils';
import InputIcon from '@material-ui/icons/Input';
import { withKeycloak } from '@react-keycloak/web';
import { useHistory, useLocation } from 'react-router';
import { Grid } from '@material-ui/core';
import ContactSupportIcon from '@material-ui/icons/ContactSupport';
import PhoneIcon from '@material-ui/icons/Phone';
import EmailIcon from '@material-ui/icons/Email';
import { i } from 'react-dom-factories';
import { INSERT_LOGIN_DETAILS } from 'src/graphql/queries';
import moment from 'moment';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgresWrite';
import { traceSpan } from 'src/utils/OTTTracing';
import { useDispatch, useSelector } from 'react-redux';
import { setCurrentUser } from 'src/actions';
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 500,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',

    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #fff',
    // visiblity: 'visible',
    backgroundColor: '#fff',
    boxShadow: '0 0 10px #8a8888'
  }
}))(Tooltip);

const useStyles = makeStyles(theme => ({
  root: {
    boxShadow: 'none',

    background: 'rgb(242,245,247)',
    background: theme.palette.primary.topGradient,
    borderBottom: '4px solid #ee7600',
    borderTop: '4px solid #ee7600',
    height: 75
  },
  toolbarWrapper: {
    '& .MuiToolbar-gutters': {
      paddingLeft: 0,
      paddingRight: 0
    }
  },
  logo: {
    width: '267px',
    cursor: 'default'
  },
  logoImg: {
    height: 'auto',
    width: 'auto',
    maxHeight: '60px',
    maxWidth: 250,
    marginLeft: 20,
    marginTop: 5
  },
  logoutButton: {
    marginLeft:
      process.env.REACT_APP_DEALER == 'Armatus' ? '' : theme.spacing(130),
    textTransform: 'none'
    //paddingTop: process.env.REACT_APP_DEALER == 'Armatus' ? '30px' : ''
  },
  logoutIcon: {
    marginLeft: theme.spacing(1)
  },
  accountButton: {
    // marginTop: '20px'
  },
  popover: {
    border: 'none',
    background: '#fff',
    height: 110
  },
  ListSubheader: {
    display: 'grid',
    marginTop: 14,
    lineHeight: '1.5em'
  },
  iconGrid: {
    marginRight: 35,
    display: 'flex',
    alignContent: 'center'
  },
  icon: {
    '&:hover': {
      backgroundColor: 'transparent'
    }
  },
  popper: {
    top: '-25px !important'
  }
}));

function Topbar({ className, ...rest }) {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [showMessage, setShowMessage] = React.useState('');
  const [userType, setUserType] = React.useState('');
  const [openSignOutDialog, setOpenSignOutDialog] = React.useState(false);
  const session = useSelector(state => state.session);
  const handleAccountClick = event => {
    setAnchorEl(event.currentTarget);
  };
  const setUserRole = () => {
    let userrole = '';
    if (rest.keycloak.realmAccess.roles.length >= 1) {
      if (rest.keycloak.realmAccess.roles.includes('superadmin')) {
        userrole = 'Super Admin';
      } else if (rest.keycloak.realmAccess.roles.includes('admin')) {
        userrole = 'Armatus Admin';
      } else if (
        rest.keycloak.realmAccess.roles.includes('user') &&
        rest.keycloak.tokenParsed.groupname.length > 1
      ) {
        userrole = 'Group Admin';
      } else if (
        rest.keycloak.realmAccess.roles.includes('user') &&
        rest.keycloak.tokenParsed.groupname.length <= 1
      ) {
        userrole = 'Store Admin';
      } else if (
        rest.keycloak.realmAccess.roles.includes('client') &&
        rest.keycloak.tokenParsed.groupname.length > 1
      ) {
        userrole = ' Group Viewer ';
      } else if (
        rest.keycloak.realmAccess.roles.includes('client') &&
        rest.keycloak.tokenParsed.groupname.length <= 1
      ) {
        userrole = 'Store Viewer';
      }
    }

    return userrole;
  };
  useEffect(() => {
    if (rest.keycloak.tokenParsed) {
      dispatch(setCurrentUser(rest.keycloak.tokenParsed.email));
      localStorage.setItem('storeValue', rest.keycloak.tokenParsed.email);
      let userRole = setUserRole();
      setUserType(userRole);
    }
  }, [
    // rest
    rest &&
      rest.keycloak &&
      rest.keycloak.tokenParsed &&
      rest.keycloak.tokenParsed.email
  ]);
  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);
  const handleLogout = () => {
    setOpenSignOutDialog(true);
    // keycloak.logout();
    // localStorage.removeItem('selectedStoreId');
    // localStorage.setItem('showCurrentMonth', false);
    // setTimeout(() => {
    //   history.push('/auth/login');
    //   localStorage.setItem('isAuthenticated', false);
    // }, 200);
  };
  useEffect(() => {
    if (rest.keycloak.tokenParsed) {
      dispatch(setCurrentUser(rest.keycloak.tokenParsed.email));
      localStorage.setItem('storeValue', rest.keycloak.tokenParsed.email);
      // localStorage.removeItem('showCurrentMonth');
    }
  }, [
    // rest
    rest &&
      rest.keycloak &&
      rest.keycloak.tokenParsed &&
      rest.keycloak.tokenParsed.email
  ]);
  const handleOk = () => {
    let storeValue = JSON.parse(localStorage.getItem('selectedStoreId'));
    let store;
    var offset = getTimeZone();
    if (storeValue) {
      let store = Object.values(storeValue);
      const client = makeApolloClientPostgres;
      const start = new Date();
      client
        .mutate({
          mutation: INSERT_LOGIN_DETAILS,
          variables: {
            username: session.currentuser,
            storeid: store[0],
            logindate: moment().format('MM-DD-YYYY HH:mm:ss'),
            offset: offset,
            logInout: 'OUT'
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/Home',
            origin: '',
            event: 'Menu Load',
            is_from: 'INSERT_LOGIN_DETAILS OUT',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
        });
    }

    rest.keycloak.logout();
    localStorage.removeItem('selectedStoreId');
    localStorage.clear();
    delete_cookie('otContext');
    delete_cookie('userIP');
    delete_cookie('userLocation');
    Object.keys(localStorage).forEach(function(key, i) {
      if (
        localStorage.key(i) != null &&
        localStorage.key(i) != 'isAuthenticated' &&
        localStorage.key(i) != 'realm' &&
        localStorage.key(i) != 'versionFlag' &&
        localStorage.key(i) != 'keycloakToken' &&
        localStorage.key(i) != 'userID' &&
        !localStorage.key(i).includes('kc-callback')
      ) {
        var item = localStorage.key(i);

        localStorage.removeItem(item);
      }
    });
    setTimeout(() => {
      // history.push('/auth/login');
      // history.push('/login');
      localStorage.setItem('isAuthenticated', false);
    }, 200);
    setOpenSignOutDialog(false);
  };
  const delete_cookie = function(name) {
    document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/';
  };
  const handleCancel = () => {
    setOpenSignOutDialog(false);
  };
  const handleHover = () => {
    return setShowMessage(renderList);
  };
  const handleLeave = () => {
    return setShowMessage('');
  };
  const handleCloseContact = () => {
    setShowMessage(false);
  };
  const renderList = () => {
    return (
      <div className="likes__list">
        <Typography
          variant="h5"
          style={{ textAlign: 'center' }}
          id="contact-us-header"
        >
          Contact Us{' '}
        </Typography>
        <MenuList style={{ padding: '2px' }} id="contact-us-menu-list">
          <MenuItem
            style={{ pointerEvents: 'none' }}
            id="contact-us-phone-menu-item"
          >
            <ListItemIcon>
              <PhoneIcon fontSize="small" id="phone-icon" />
            </ListItemIcon>

            <ListItemText id="phone-number">+****************</ListItemText>
          </MenuItem>
          <MenuItem
            style={{ pointerEvents: 'none' }}
            id="contact-us-email-menu-item"
          >
            <ListItemIcon id="email-icon">
              <EmailIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary="<EMAIL>"
              onClick={() => (window.location = 'mailto:<EMAIL>')}
              id="email-text"
            >
              <Link
                to="#"
                onClick={() => (window.location = 'mailto:<EMAIL>')}
                id="email-link"
              >
                <EMAIL>
              </Link>
            </ListItemText>
          </MenuItem>
        </MenuList>
      </div>
    );
  };
  //getAllChartDetails();
  let Dealer = process.env.REACT_APP_DEALER;

  return (
    <AppBar
      {...rest}
      className={clsx(classes.root, classes.toolbarWrapper, className)}
      color="primary"
      style={{
        borderBottom: '4px solid #ee7600',
        borderTop: '4px solid #ee7600'
      }}
      id="app-bar"
    >
      <Toolbar id="toolbar">
        <Grid justify="space-between" container spacing={12}>
          <Grid item>
            {/* <RouterLink to="/" className={classes.logo}> */}
            {Dealer === 'Armatus' ? (
              <img
                className={classes.logoImg}
                alt="Logo"
                src="/images/logos/logo_armatus.png"
                id="logo-armatus"
              />
            ) : (
              <img
                id="logo-netspective"
                alt="Logo"
                src="/images/logos/logo_netspective.png"
              />
            )}
            {/* </RouterLink> */}
          </Grid>
          {rest.keycloak.authenticated == false ? (
            <Grid item className={classes.iconGrid}>
              <HtmlTooltip
                // open={true}
                interactive={true}
                id="contact-popper-login"
                classes={{
                  popper: classes.popper
                }}
                title={
                  <>
                    <Typography variant="h5" style={{ textAlign: 'center' }}>
                      Contact Us{' '}
                    </Typography>
                    <MenuList style={{ padding: '2px' }}>
                      <MenuItem style={{ pointerEvents: 'none' }}>
                        <ListItemIcon>
                          <PhoneIcon fontSize="small" />
                        </ListItemIcon>

                        <ListItemText>+****************</ListItemText>
                      </MenuItem>
                      <MenuItem>
                        <ListItemIcon>
                          <EmailIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary="<EMAIL>"
                          onClick={() =>
                            (window.location = 'mailto:<EMAIL>')
                          }
                          id="email-contact"
                        >
                          <Link
                            to="#"
                            onClick={() =>
                              (window.location = 'mailto:<EMAIL>')
                            }
                          >
                            <EMAIL>
                          </Link>
                        </ListItemText>
                      </MenuItem>
                    </MenuList>
                  </>
                }
                //style={{ display: searchText ? 'none' : 'inline-block' }}
              >
                <IconButton
                  size="medium"
                  classes={{
                    root: classes.icon
                  }}
                  id="contact-button"
                >
                  <img
                    src="/images/call-email-icon.png"
                    style={{ height: 22, width: 23 }}
                  />
                </IconButton>
              </HtmlTooltip>
            </Grid>
          ) : (
            ''
          )}

          {rest.keycloakInitialized == true &&
          rest.keycloak.authenticated == true ? (
            <Grid item className={classes.iconGrid}>
              <HtmlTooltip
                // open={true}
                interactive={true}
                id="contact-popper-login"
                classes={{
                  popper: classes.popper
                }}
                title={
                  <>
                    <Typography variant="h5" style={{ textAlign: 'center' }}>
                      Contact Us{' '}
                    </Typography>
                    <MenuList style={{ padding: '2px' }}>
                      <MenuItem style={{ pointerEvents: 'none' }}>
                        <ListItemIcon>
                          <PhoneIcon fontSize="small" />
                        </ListItemIcon>

                        <ListItemText>+****************</ListItemText>
                      </MenuItem>
                      <MenuItem>
                        <ListItemIcon>
                          <EmailIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary="<EMAIL>"
                          onClick={() =>
                            (window.location = 'mailto:<EMAIL>')
                          }
                          id="email-contact-authenticated"
                        >
                          <Link
                            to="#"
                            onClick={() =>
                              (window.location = 'mailto:<EMAIL>')
                            }
                          >
                            <EMAIL>
                          </Link>
                        </ListItemText>
                      </MenuItem>
                    </MenuList>
                  </>
                }
                //style={{ display: searchText ? 'none' : 'inline-block' }}
              >
                <IconButton
                  size="medium"
                  classes={{
                    root: classes.icon
                  }}
                  id="contact-button-authenticated"
                >
                  <img
                    src="/images/call-email-icon.png"
                    style={{ height: 22, width: 23 }}
                  />
                </IconButton>
              </HtmlTooltip>

              <Button
                aria-owns={open ? 'mouse-over-popover' : undefined}
                aria-haspopup="true"
                onClick={handleAccountClick}
                color="inherit"
                className={classes.accountButton}
                id="account-button"
              >
                <Grid style={{ display: 'grid', textTransform: 'none' }}>
                  <span style={{ fontSize: 12, marginRight: 2 }}>
                    Hi, {rest.keycloak.tokenParsed.given_name}
                  </span>
                </Grid>
                <AccountCircle />
              </Button>

              <Popover
                id="mouse-over-popover"
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: 'bottom',
                  horizontal: 'center'
                }}
                transformOrigin={{
                  vertical: 'top',
                  horizontal: 'center'
                }}
              >
                <List
                  component="nav"
                  aria-labelledby="nested-list-subheader"
                  classes={{
                    root: classes.popover
                  }}
                  subheader={
                    <ListSubheader
                      component="div"
                      id="nested-list-subheader"
                      className={classes.ListSubheader}
                    >
                      <span> User : {rest.keycloak.tokenParsed.email}</span>
                      <span>Role :{' ' + userType}</span>
                    </ListSubheader>
                  }
                  className={classes.root}
                >
                  <Divider />
                  <ListItem
                    className={classes.logoutButton}
                    onClick={handleLogout}
                    color="inherit"
                    button
                    id="sign-out-button"
                  >
                    <ListItemText> Sign Out</ListItemText>
                    <ListItemIcon>
                      <InputIcon className={classes.logoutIcon} />
                    </ListItemIcon>
                  </ListItem>
                </List>
              </Popover>
            </Grid>
          ) : (
            ''
          )}
        </Grid>
        <Dialog
          //fullWidth
          //maxWidth="sm"
          id="sign-out-dialog"
          aria-labelledby="confirmation-dialog-title"
          open={openSignOutDialog}
        >
          <DialogTitle id="confirmation-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Sign Out
            </Typography>
          </DialogTitle>
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to sign out?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button id="cancel-signout-button" autoFocus onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              id="confirm-signout-button"
              onClick={handleOk}
              color="primary"
            >
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </Toolbar>
    </AppBar>
  );
}

Topbar.propTypes = {
  className: PropTypes.string
};

export default withKeycloak(Topbar);
