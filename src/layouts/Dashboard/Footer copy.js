import React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import Paper from '@material-ui/core/Paper';
import Typography from '@material-ui/core/Typography';

const useStyles = makeStyles(theme => ({
  root: {
    ...theme.mixins.gutters(),
    // paddingTop: theme.spacing.unit * 2,
    // paddingBottom: theme.spacing.unit * 2,
    // backgroundColor: theme.palette.primary.main,
    borderRadius: '1px',
    boxShadow: 'none'
  },
  footer: {
    height: 130,
    marginTop: 'auto',
    width: '100%',
    bottom: '0%',

    zIndex: '1201',
    // position: 'fixed',

    paddingTop: '0px',
    // backgroundColor: theme.palette.primary.main,
    boxShadow: 'none',
    background: theme.palette.primary.footerGradient
  },
  logo: {
    width: '267px'
  },
  h: {
    color: 'rgb(0, 61, 107)',
    fontSize: '10px',
    marginTop: '-4px'
  },
  h1: {
    color: 'rgb(0, 61, 107)',
    fontSize: '10px',
    marginLeft: '15px',
    marginTop: '-3px'
  },
  h2: {
    color: 'rgb(0, 61, 107)',
    fontSize: '10px',
    marginLeft: '25%',
    marginTop: '-4px'
  },
  header: {
    color: 'rgb(0, 61, 107)',
    fontSize: '11px'
    // marginLeft: '10px',
    // marginTop: '10px'
  },
  header2: {
    color: 'rgb(0, 61, 107)',
    fontSize: '11px',
    marginLeft: '25%'
    // marginTop: '10px'
  },
  p: {
    color: '#fff',
    fontSize: '9px',
    textAlign: 'center'
    // marginLeft: '30px'
  },
  row: {
    display: 'table',
    clear: 'both',
    width: '100%'
    // backgroundColor: theme.palette.primary.main
  },
  column: {
    float: 'left',
    width: '39%',
    padding: '0px',
    paddingLeft: '40px'
  },
  columnTwo: {
    float: 'left',
    width: '21%',
    padding: '0px',
    paddingLeft: '40px'
  },
  columnThree: {
    float: 'right',
    width: '20%',
    padding: '0px',
    paddingLeft: '0px'
  },
  columnFour: {
    float: 'left',
    width: '19%',
    padding: '0px',
    paddingLeft: '40px'
  },
  subcolumn: {
    float: 'left',
    width: '45%'
    // padding: '10px'
    // paddingTop: '0px',
  },
  subcolumnOne: {
    float: 'left'
  },
  img: {
    height: 36,
    width: 273
  }
}));

function Footer() {
  const classes = useStyles();

  return (
    <Paper className={clsx(classes.root, classes.footer)}>
      <div>
        <RouterLink to="/CPOverview" className={classes.logo}>
          {Dealer === 'Armatus' ? (
            <img
              alt="Fixed Ops Logo"
              src="/images/logos/Armatus-logo-new.svg"
            />
          ) : (
            <img
              alt="Fixed Ops Logo"
              src="/images/logos/logo_netspective.png"
            />
          )}
        </RouterLink>
      </div>
      <div className={classes.row}>
        <div className={classes.column} style={{ float: 'left' }}>
          <Typography variant="h6" component="h3" className={classes.header}>
            Jankowski Ferrari-Maserati
          </Typography>
          <div className={classes.subcolumn} style={{ float: 'left' }}>
            <Typography variant="h6" component="h3" className={classes.h}>
              General Manager
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*****
            </Typography>
            <Typography variant="h6" component="h3" className={classes.h}>
              Fixed Ops Director &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*****
            </Typography>
            <Typography variant="h6" component="h3" className={classes.h}>
              Service Manager
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*****
            </Typography>
            <Typography variant="h6" component="h3" className={classes.h}>
              Parts Manager
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*****
            </Typography>
          </div>
          {/* <div className={classes.subcolumn} style={{ float: 'left' }}>
              <Typography variant="h6" component="h3" className={classes.h}>
                *******
              </Typography>
              <Typography variant="h6" component="h3" className={classes.h}>
                *******
              </Typography>
              <Typography variant="h6" component="h3" className={classes.h}>
                *******
              </Typography>
              <Typography variant="h6" component="h3" className={classes.h}>
                *******
              </Typography>
            </div> */}
        </div>
        <div className={classes.columnTwo} style={{ marginTop: '1%' }}>
          <Typography component="p" className={classes.p}>
            <img
              className={classes.img}
              alt="Fixed Ops Logo"
              src="/images/logos/armatus-logo-footer.png"
            />
          </Typography>

          <Typography component="p" className={classes.p}>
            Copyright @ 2021 /Armatus Dealer Uplift / All Rights Reserved
          </Typography>
        </div>
        <div className={classes.columnFour} style={{ float: 'left' }}></div>
        <div className={classes.columnThree} style={{ float: 'right' }}>
          <Typography variant="h5" component="h3" className={classes.header2}>
            Armatus Fixed Ops Performance Center
          </Typography>
          <Typography variant="h6" component="h3" className={classes.h2}>
            Account
            Manager&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;***********
          </Typography>
          <Typography variant="h6" component="h3" className={classes.h2}>
            Phone&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            ***********
          </Typography>
          <Typography variant="h6" component="h3" className={classes.h2}>
            Email&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            ***********
          </Typography>
          {/* <div className={classes.subcolumnOne} style={{ float: 'left' }}>
              <Typography variant="h6" component="h3" className={classes.h2}>
                Account Manager
              </Typography>
              <Typography variant="h6" component="h3" className={classes.h2}>
                Phone
              </Typography>
              <Typography variant="h6" component="h3" className={classes.h2}>
                Email
              </Typography>
            </div> */}
          {/* <div className={classes.subcolumnOne} style={{ float: 'rigth' }}>
              <Typography variant="h6" component="h3" className={classes.h1}>
                *******
              </Typography>
              <Typography variant="h6" component="h3" className={classes.h1}>
                *******
              </Typography>
              <Typography variant="h6" component="h3" className={classes.h1}>
                *******
              </Typography>
              <Typography
                variant="h6"
                component="h3"
                className={classes.h1}
              ></Typography>
            </div> */}
        </div>
      </div>
    </Paper>
  );
}

Footer.propTypes = {
  classes: PropTypes.object.isRequired
};

export default Footer;
