/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-unneeded-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable react/jsx-boolean-value */
/* eslint-disable no-use-before-define */
/* eslint-disable no-shadow */
import {
  AppBar,
  Button,
  Chip,
  colors,
  FormControl,
  Hidden,
  IconButton,
  MenuItem,
  Grid,
  Toolbar,
  Tooltip,
  Typography,
  Badge,
  CircularProgress,
  InputLabel,
  Select,
  MenuList,
  DialogContentText
} from '@material-ui/core';
import ReplayIcon from '@material-ui/icons/Replay';
import SwapHorizontalCircleOutlinedIcon from '@material-ui/icons/SwapHorizontalCircleOutlined';
import CategoryIcon from '@material-ui/icons/Category';
import EditOutlinedIcon from '@material-ui/icons/EditOutlined';
import InputIcon from '@material-ui/icons/Input';
import MenuIcon from '@material-ui/icons/Menu';
import { makeStyles, withStyles } from '@material-ui/styles';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import { withKeycloak } from '@react-keycloak/web';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import { Link } from 'react-router-dom';
import ReactHtmlParser from 'react-html-parser';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import ListSubheader from '@material-ui/core/ListSubheader';
import Divider from '@material-ui/core/Divider';
import List from '@material-ui/core/List';
import ListItem from '@material-ui/core/ListItem';
import PhoneIcon from '@material-ui/icons/Phone';
import EmailIcon from '@material-ui/icons/Email';
import { getLastThirteenMonths } from 'src/utils/hasuraServices';
import {
  setActiveAdvisors,
  setServiceAdvisor,
  setPreviousPath,
  setCurrentPath,
  setFavouriteCharts,
  setPayTypeError,
  setToken,
  setRefreshStatus,
  setReloadStatus,
  setRefreshErrorStatus,
  setUsername,
  setRONumber,
  setPayTypeErrorCount,
  setStorValue,
  setStoreId,
  setSelectedadvisorWithId,
  setKpiReportAdvisor,
  setOpcodeError,
  setOpcodeErrorCount,
  setServiceAdvisorErrors,
  setTechnicianErrors,
  setServiceAdvisorErrorsCount,
  setTechnicianErrorsCount,
  setAllErrorsCount,
  setKpiToggle,
  setTechnician,
  setAllAdvisorNames,
  setAllAdvisorIds,
  setLaborGridTypes,
  setPartsMatrixTypes,
  setDailyImportError,
  setDailyImportErrorCount,
  setDateData,
  setNewAdvisors,
  setNewAdvisorsErrorCount,
  setNewPaytype,
  setNewPaytypeErrorCount,
  setActiveTechs,
  setToggleStatus,
  setSelectedTech,
  setKpiHomeToggle,
  setMenuSelected,
  setVersionFlag,
  setMpiFlag,
  setMenuFlag,
  setNewModelErrorCount,
  setNewModel,
  setKpiToggleStartDate,
  setKpiToggleEndDate,
  setKpiGraphicsToggleStartDate,
  setKpiGraphicsToggleEndDate,
  setRetailFlag
} from 'src/actions';
import SearchIcon from '@material-ui/icons/Search';
import AccountCircle from '@material-ui/icons/AccountCircle';
import InputBase from '@material-ui/core/InputBase';
// import FeedBackForm from 'src/components/FeedBackForm/index';

import MultiSelect from 'src/components/MultiSelect';
import ContactSupportIcon from '@material-ui/icons/ContactSupport';
import html2canvas from 'html2canvas';
import Popover from '@material-ui/core/Popover';
import LanguageOutlinedIcon from '@material-ui/icons/LanguageOutlined';
import NotificationsIcon from '@material-ui/icons/Notifications';
import {
  getAllServiceAdvisors,
  getDailyImportDataDetails,
  getAllFavouriteCharts,
  getAllPayTypeErrors,
  getStoreDetails,
  getRefreshViewsStatus,
  updateRefreshStatus,
  getAllTechnicians,
  getOpcodeDetails,
  getLatestClosedDate,
  getLatestOpenDate,
  getAllOpcodeErrors,
  getUniqueOpcodes,
  getGridorMatrixPayTypeDetails,
  getKpiToggleOptionsWithTimeZone,
  getStoreRetialFlags
} from 'src/utils/hasuraServices';
import styles from 'src/custom.css';
import {
  isAdvisorenabledCharts,
  getTimeZone,
  checkClosedDateInCurrentMonth
} from 'src/utils/Utils';
import NotificationsPopover from 'src/components/NotificationsPopover';
import Marquee from 'react-fast-marquee';
import SuccessSnackbar from '../../components/FeedBackForm/SuccessSnackbar';
import FeedBack from '../../components/FeedBackForm/FeedBack';
import Help from '../../components/Help/Help';
import Carousel from 'react-material-ui-carousel';
import SuccessSnackbarHelp from '../../components/Help/SuccessSnackbarHelp';
import Input from '@material-ui/core/Input';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import Menu from '@material-ui/core/Menu';
import Dropdown from 'src/components/Dropdown';
import { traceSpan } from 'src/utils/OTTTracing';
import { ReactSession } from 'react-client-session';
import { INSERT_LOGIN_DETAILS } from 'src/graphql/queries';
import moment from 'moment';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgresWrite';
import { getAllStoreNames } from 'src/utils/hasuraServices';
import $ from 'jquery';
import { DiagConsoleLogger } from '@opentelemetry/api';
import {
  getKpiScoreCardDataStatus,
  getVersionFlags
} from 'src/utils/hasuraServices';

var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

var Dealer = process.env.REACT_APP_DEALER;

export const ServiceAdvisorContext = React.createContext('All');
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 500,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',

    textAlign: 'left',
    fontFamily: "'Roboto', 'Helvetica', 'Arial', 'sans - serif'",
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #fff',
    // visiblity: 'visible',
    backgroundColor: '#fff',
    boxShadow: '0 0 10px #8a8888'
  }
}))(Tooltip);
const HtmlTooltipWithBackground = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 500,
    display: 'grid !important',
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',
    textAlign: 'left',
    fontFamily: "'Roboto', 'Helvetica', 'Arial', 'sans - serif'",
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#fff',
    border: '2px solid #003d6b',
    display: 'flex',
    alignItems: 'center',
    // visiblity: 'visible',
    fontWeight: 'bold',
    backgroundColor: '#ee7600',
    boxShadow: '0 0 10px #8a8888'
  }
}))(Tooltip);
const useStyles = makeStyles(theme => ({
  formControl: {
    padding: 4,
    minWidth: 150
  },
  techFormControl: {
    padding: 4,
    minWidth: 120,
    marginRight: -5
  },
  arrowPopper: {
    top: '105px !important',
    left: '50% !important',
    transform: 'translate(-50%, -50%) !important'
  },

  toolbarWrapper: {
    '& .MuiToolbar-gutters': {
      paddingLeft: 0,
      paddingRight: 0
    }
  },
  storeInfo: {
    // paddingTop: 10
    '@media (max-width: 1440px)': {
      width: '62% !important',
      marginLeft:
        JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1 &&
        localStorage.getItem('realm') != 'koeppelag' &&
        localStorage.getItem('realm') != 'kunescountryag'
          ? 10
          : JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1 &&
            localStorage.getItem('realm') == 'koeppelag'
          ? 6
          : localStorage.getItem('realm') == 'kunescountryag'
          ? 1
          : 10
    },
    '@media (max-width: 1920px)': {
      width: '84%',
      marginLeft:
        JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1 &&
        localStorage.getItem('realm') != 'koeppelag' &&
        localStorage.getItem('realm') != 'kunescountryag'
          ? 10
          : JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1 &&
            localStorage.getItem('realm') == 'koeppelag'
          ? 6
          : localStorage.getItem('realm') == 'kunescountryag'
          ? 1
          : 10
    },
    '@media (max-width: 1280px)': {
      width: '33%',
      marginLeft: 0
    },
    '@media (min-width: 2304px) and (max-width: 2575px)': {
      width: '90% !important',
      marginRight: '-37px'
    },
    '@media (min-width: 2576px) and (max-width: 2768px)': {
      width: '68% !important'
    },
    '@media (min-width: 2304px)': {
      width: '60%',
      marginLeft:
        JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
          ? 10
          : 10
    }
  },
  storeInfoClient: {
    '@media (max-width: 1920px)': {
      width: '52%',
      marginLeft:
        JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
          ? 10
          : 10
    },
    '@media (max-width: 1280px)': {
      width: '58%',
      marginLeft: 0
    },
    '@media (min-width: 2304px)': {
      width: '37%',
      marginLeft:
        JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
          ? 10
          : 10
    }
  },
  logo: {
    cursor: 'default',
    '@media (max-width: 1920px)': {
      width: '210px'
    },

    '@media (min-width: 2304px)': {
      width: '210px'
    },
    '@media (max-width: 1280px)': {
      width: '210px'
    },
    '@media (max-width: 1440px)': {
      width: '150px'
    }
  },
  logoImg: {
    height: 'auto',
    width: 'auto',
    maxHeight: '60px',
    maxWidth: 250,
    marginLeft: 20,
    marginTop: 5
  },
  clientNameStyle: {
    '@media (min-width: 1280px)': {
      lineHeight: '14px'
    }
  },
  clientGroupNameStyle: {
    '@media (min-width: 1280px)': {
      height: '18px'
    }
  },
  dmsName: {
    padding: '0px 6px',
    borderRadius: '14px',
    border: 'solid 1px white',
    fontWeight: 'bold',
    lineHeight: '12px',
    fontSize: 8,
    color: '#fff',
    fontFamily: "'Roboto', 'Helvetica', 'Arial', 'sans - serif'",
    '@media (min-width: 1280px) and (max-width: 1770px)': {
      padding: '0px 4px',
      fontSize: 8
    }
  },
  subtitle1: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
    fontFamily: "'Roboto', 'Helvetica', 'Arial', 'sans - serif'",
    '@media (max-width: 1920px)': {
      fontSize: 12
    },

    '@media (min-width: 2304px)': {
      fontSize: 12
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    }
  },
  subtitle2: {
    color: '#fff',
    fontFamily: "'Roboto', 'Helvetica', 'Arial', 'sans - serif'",
    // fontWeight: 'bold',
    fontSize: 12,
    '@media (max-width: 1920px)': {
      fontSize: 12
    },

    '@media (min-width: 2304px)': {
      fontSize: 12
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    }
  },
  subtitle3: {
    color: '#fff',
    marginTop: '-2px',
    //fontWeight: 'bold',
    fontFamily: "'Roboto', 'Helvetica', 'Arial', 'sans - serif'",
    fontSize: 11,
    '@media (max-width: 1920px)': {
      fontSize: 11
    },

    '@media (min-width: 2304px)': {
      fontSize: 11
    },
    '@media (max-width: 1280px)': {
      fontSize: 9
    },
    '@media (max-width: 1440px)': {
      fontSize: 9
    }
  },
  subtitle4: {
    color: '#fff',
    //fontWeight: 'bold',
    fontFamily: "'Roboto', 'Helvetica', 'Arial', 'sans - serif'",
    '@media (max-width: 1920px)': {
      fontSize: 11
    },

    '@media (min-width: 2304px)': {
      fontSize: 11
    },
    '@media (max-width: 1280px)': {
      fontSize: 9
    },
    '@media (max-width: 1440px)': {
      fontSize: 9
    }
  },
  iconGrid: {
    paddingTop: 5,
    '@media (max-width: 1920px)': {
      marginLeft: 20,
      marginRight: 20
    },

    '@media (min-width: 2304px)': {
      marginLeft: 20,
      marginRight: 20
    },
    '@media (max-width: 1280px)': {
      marginLeft: 0,
      marginRight: 0
    },
    '@media (max-width: 1440px)': {
      marginLeft: 0,
      marginRight: 0
    }
  },
  toolsGrid: {
    display: 'inline-flex',
    marginRight: 5,
    height: 70,
    alignItems: 'center'
  },
  accountGrid: {
    //height: 70,
    display: 'grid',
    marginTop: -18
  },
  root: {
    boxShadow: 'none',

    background: 'rgb(242,245,247)',
    background: theme.palette.primary.topGradient,
    borderBottom: '4px solid #ee7600',
    borderTop: '4px solid #ee7600',
    height: 75
  },
  signoutPopper: {
    zIndex: '1600 !important'
  },
  flexGrow: {
    flexGrow: 1
  },
  flexShowGroup: {
    display: 'inline-flex',
    alignItems: 'center'
  },
  flexShow: {
    display: 'inline-flex'
  },
  flexShowBlock: {
    display: 'inline-flex',
    height: '100%',

    '@media (max-width: 1920px)': {
      width: '35%'
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: '30%'
      //marginLeft: '25%'
    },
    '@media (max-width: 1440px)': {
      width: '33%'
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: '27%'
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  flexShowGrid: {
    display: 'inline-flex',
    alignItems: 'center',
    marginTop: '5px'
  },
  flexGrid: {
    display: 'grid',
    lineHeight: '1.25em'
  },
  topbarTitle: {
    marginLeft: '0%',
    '@media (max-width: 1920px)': {
      width: '23%'
    },

    '@media (min-width: 2304px)': {
      width: '46%'
    }
  },
  topbarTitleFerr: {
    marginLeft: '2%',
    '@media (max-width: 1920px)': {
      width: '25%'
    },

    '@media (min-width: 2304px)': {
      width: '46%'
    }
  },
  titleContent: {
    color: '#fff',
    fontWeight: 'bold',
    textAlign: 'center',

    '@media (max-width: 1920px)': {
      fontSize: 22
      //fontSize: 25,
      //marginLeft: '23%'
    },

    '@media (max-width: 1280px)': {
      fontSize: 22
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      fontSize: 38
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  icon: {
    '&:hover': {
      backgroundColor: 'transparent'
    }
  },
  popper: {
    top: '-15px !important'
  },
  contactIcon: {
    color: '#fff'
  },
  searchIcon: {
    marginRight: theme.spacing(2),
    color: 'inherit'
  },
  searchInput: {
    flexGrow: 1,
    color: 'inherit',
    '& input::placeholder': {
      opacity: 1,
      color: 'inherit'
    }
  },
  searchPopper: {
    zIndex: theme.zIndex.appBar + 100
  },
  searchPopperContent: {
    marginTop: theme.spacing(1)
  },
  trialButton: {
    marginLeft: theme.spacing(2),
    color: theme.palette.common.white,
    backgroundColor: colors.green[600],
    '&:hover': {
      backgroundColor: colors.green[900]
    }
  },
  trialIcon: {
    marginRight: theme.spacing(1)
  },
  menuButton: {
    marginRight: theme.spacing(1)
  },
  chatButton: {
    //marginLeft: theme.spacing(1)
  },
  notificationsButton: {
    // marginLeft: theme.spacing(1)
    // padding: 0,
    color: '#fff',
    minWidth: 30
  },
  gotoButton: {
    // marginLeft: theme.spacing(1)
    // padding: 0,
    color: '#fff',
    minWidth: 30
  },
  exploreIcon: {
    fontSize: '0.95em'
  },
  notificationsButtonFerr: {
    // marginLeft: theme.spacing(1)
    // padding: 0,
    color: '#fff',
    minWidth: 30,
    fontSize: 12
  },
  notificationsButtonFerrIcon: {
    color: '#fff',
    minWidth: 30,
    fontSize: 12,
    marginBottom: 20,
    top:
      JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
        ? ''
        : '-37px',
    left:
      JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
        ? ''
        : '38px'
  },
  notificationsButtonIcon: {
    // marginLeft: theme.spacing(1)
    // padding: 0,
    color: '#fff',
    minWidth: 30,
    marginBottom: 20
    // top:
    //   JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
    //     ? ''
    //     : '-37px',
    // left:
    //   JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
    //     ? ''
    //     : '38px'
  },

  notificationsBadge: {
    backgroundColor: '#ee7600',
    fontSize: '10px',
    width: ' 12px'
  },
  logoutButton: {
    //marginLeft: theme.spacing(1),
    textTransform: 'none'
  },
  logoutIcon: {
    //marginLeft: theme.spacing(1)
  },

  icon: {
    fill: '#FFF'
  },
  chips: {
    display: 'flex',
    flexWrap: 'wrap'
  },
  chip: {
    margin: 2
  },
  noLabel: {
    marginTop: theme.spacing(3)
  },
  options: {
    color: '#909090'
  },

  carouselRoot: {
    width: 250
  },
  search: {
    position: 'relative',
    borderRadius: theme.shape.borderRadius,
    backgroundColor: '#f4f6f8',
    '&:hover': {
      backgroundColor: '#f4f6f8'
    },
    marginLeft: 0,
    width: '100%',
    [theme.breakpoints.up('sm')]: {
      marginLeft: theme.spacing(1),
      width: 'auto'
    },
    height: '38px',
    marginTop: '4px'
  },
  searchIcon: {
    padding: theme.spacing(0, 2),
    height: '100%',
    position: 'absolute',
    pointerEvents: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
  },
  inputRoot: {
    color: 'inherit',
    paddingLeft: 8,
    paddingTop: 6
    //width: 120
  },
  inputRootFocus: {
    color: 'inherit',
    width: 0,
    //color: '#000',

    paddingLeft: 0
  },
  inputIcon: {
    minWidth: 30,
    padding: '0px 6px'
  },
  inputIcons: {
    minWidth: 30,
    padding: '0px 8px'
  },
  inputIco: {
    minWidth: 30,
    padding: '0px 8px',
    top: '2px',
    left: '2px'
  },
  inputInput: {
    color: '#000',
    fontSize: 12,

    // vertical padding + font size from searchIcon
    paddingLeft: 8,
    //transition: theme.transitions.create('width'),
    width: '95px',
    // [theme.breakpoints.up('sm')]: {
    //   width: '12ch',
    //   '&:focus': {
    //     width: '20ch'
    //   }
    // }
    '@media (max-width: 1280px)': {
      paddingLeft: 0,
      //transition: theme.transitions.create('width'),
      width: '80px'
    }
  },
  dot: {
    height: '10px',
    width: '10px',
    margin: 2,
    backgroundColor: '#fff',
    borderRadius: '50%',
    display: 'inline-block'
  },
  toolbar: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  popover: {
    border: 'none',
    background: '#fff',
    height: 110
  },
  ListSubheader: {
    display: 'grid',
    marginTop: 14,
    lineHeight: '1.5em'
  },
  dropdown: {},
  storeDropdown: {},
  dialogRoot: {
    backgroundColor: '#ee7600',
    color: '#fff'
  },
  dialogRootGrid: {
    display: 'flex',
    alignItems: 'center',
    color: '#fff',
    fontWeight: 'bold',
    fontSize: '16px'
  },
  reloadButton: {
    margin: 5,
    color: '#fff',
    border: '2px solid #003d6b'
  },
  dialogBoxRoot: {
    border: '2px solid #003d6b'
  },
  textRoot: {
    textTransform: 'none',
    color: '#fff !important',
    marginRight: 8,
    fontSize: 17
  }
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250
    }
  }
};

function TopBar({
  onOpenNavBarMobile,
  className,
  keycloak,
  route,
  storeval,
  ...rest
}) {
  const [helpStatus, setHelpStatus] = useState(false);
  const [feedBackStatus, setFeedBackStatus] = useState(false);
  const [canvasVal, setCanvasVal] = useState({});
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [openSnackbarHelp, setOpenSnackbarHelp] = useState(false);
  const [error, setError] = useState(false);
  const [technician, setTechnicianSelection] = useState([]);
  const [techNames, setTechNames] = useState([]);

  const [userType, setUserType] = React.useState('');
  const classes = useStyles();
  const history = useHistory();
  const searchRef = useRef(null);
  const dispatch = useDispatch();
  const notificationsRef = useRef(null);
  const [notifications, setNotifications] = useState([]);
  const [openNotifications, setOpenNotifications] = useState(false);
  const [notificationsError, setNotificationsError] = useState(false);
  const [openChatBar, setOpenChatBar] = useState(false);

  const [techName, setTechName] = useState(['All']);
  const [techPersonName, setTechPersonName] = useState(['All']);
  const [personName, setPersonName] = useState(['All']);
  const [personNameKpi, setPersonNameKpi] = useState(['All']);
  const [advisorName, setAdvisorName] = useState(['All']);
  const [names, setNames] = useState([]);
  const [advisorNames, setadvisorNames] = useState([]);
  const [clientName, setclientName] = useState('');
  const [dmsName, setDmsName] = useState('');
  const [clientWebsite, setClientWebsite] = useState('');
  const [clientDesc, setclientDesc] = useState('');
  // const [selected, setSelected] = useState([localStorage.getItem('storeSelected')]);
  const [techSelected, setTechSelected] = useState(['All']);
  const [selectedSto, setSelectedSto] = useState([storeval]);
  const [selected, setSelected] = useState(['All']);
  const [selectedKpiAdvisor, setSelectedKpiAdvisor] = useState([]);
  const [options, setoptions] = useState([]);
  const [path, setPath] = useState('');
  const [isApplied, setAdvisorFilter] = useState(true);
  const [clientGroup, setclientGroup] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshStatusVal, setRefreshStatusVal] = useState();
  const [reload, setReload] = useState(false);
  const [roNumber, setRoNumber] = useState('');
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [openSignOutDialog, setOpenSignOutDialog] = React.useState(false);
  const [openAllStoresDialog, setOpenAllStoresDialog] = React.useState(false);
  const [isRoOpen, setIsRoOpen] = React.useState(false);
  const [allPayTypeErrors, setAllPayTypeErrors] = React.useState(0);
  const [storeName, setstoreName] = useState([]);
  const [groupList, setGroups] = useState([]);
  const [storeSlected, setstoreSlected] = useState([]);
  const [openSnackBar, setSnackbar] = useState(false);
  const [openStore, setOpenStore] = useState(false);
  const [formState, setFormState] = useState({
    isValid: false,
    values: {},
    touched: {},
    errors: {}
  });
  const [selectedControl, setSelectedControl] = useState(null);
  const [allOpcodeErrors, setAllOpcodeErrors] = React.useState(0);
  const [allErrors, setAllErrors] = React.useState(0);
  const [showMessage, setShowMessage] = React.useState('');
  const [openAlertDialog, setOpenAlertDialog] = useState(false);
  const handleAccountClick = event => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);
  //const id = open ? 'account_profile' : undefined;

  const session = useSelector(state => state.session);
  useEffect(() => {
    setSelectedSto([storeval]);
  }, [storeval]);
  useEffect(() => {
    if (session.toggleStatus == false) {
      let status = checkClosedDateInCurrentMonth(
        localStorage.getItem('closedDate'),
        localStorage.getItem('openDate')
      );

      // if (session.toggleStatus != undefined) {
      //   dispatch(setToggleStatus(session.toggleStatus));
      // } else {
      dispatch(setToggleStatus(status));
      // }
    }
  }, [storeval]);
  useEffect(() => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;

        dispatch(setDateData(dataArr));

        if (
          localStorage.getItem('kpiStartDate') == null ||
          localStorage.getItem('kpiStartDate') == '' ||
          localStorage.getItem('kpiFilterEndDate') == null ||
          localStorage.getItem('kpiFilterEndDate') == '' ||
          localStorage.getItem('kpiHomeToggle') == null ||
          localStorage.getItem('kpiHomeToggle') == ''
        ) {
          if (localStorage.getItem('kpiDataStatus') == 1) {
            dispatch(
              setKpiToggleStartDate(
                moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiToggleEndDate(
                moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiGraphicsToggleStartDate(
                moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiGraphicsToggleEndDate(
                moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
              )
            );
            ReactSession.set(
              'kpiToggleStartDate',
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            ReactSession.set(
              'kpiToggleEndDate',
              moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
            );
            ReactSession.set('kpiHomeToggle', 'MTD');
            localStorage.setItem('kpiHomeToggle', 'MTD');
            localStorage.setItem(
              'kpiStartDate',
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            localStorage.setItem(
              'kpiFilterEndDate',
              moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
            );
          } else {
            dispatch(
              setKpiToggleStartDate(
                moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiToggleEndDate(
                moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
              )
            );
            ReactSession.set(
              'kpiToggleStartDate',
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            ReactSession.set(
              'kpiToggleEndDate',
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
            ReactSession.set('kpiHomeToggle', 'LMONTH');
            localStorage.setItem('kpiHomeToggle', 'LMONTH');
            localStorage.setItem(
              'kpiStartDate',
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            localStorage.setItem(
              'kpiFilterEndDate',
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
          }
        } else {
          if (localStorage.getItem('kpiDataStatus') == 1) {
            dispatch(
              setKpiToggleStartDate(
                moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiToggleEndDate(
                moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiGraphicsToggleStartDate(
                moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiGraphicsToggleEndDate(
                moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
              )
            );
            ReactSession.set(
              'kpiToggleStartDate',
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            ReactSession.set(
              'kpiToggleEndDate',
              moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
            );
            ReactSession.set('kpiHomeToggle', 'MTD');
            localStorage.setItem('kpiHomeToggle', 'MTD');
            localStorage.setItem(
              'kpiStartDate',
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            localStorage.setItem(
              'kpiFilterEndDate',
              moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
            );
          } else {
            dispatch(
              setKpiToggleStartDate(
                moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
              )
            );
            dispatch(
              setKpiToggleEndDate(
                moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
              )
            );
            ReactSession.set(
              'kpiToggleStartDate',
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            ReactSession.set(
              'kpiToggleEndDate',
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
            ReactSession.set('kpiHomeToggle', 'LMONTH');
            localStorage.setItem('kpiHomeToggle', 'LMONTH');
            localStorage.setItem(
              'kpiStartDate',
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            localStorage.setItem(
              'kpiFilterEndDate',
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
          }
        }
      }
    });
    // let toggleSet = ['MTD', 'YESDT', 'DBYESDT', 'LWEEK'];
    let toggleSet = ['MTD'];
    toggleSet.map(item => {
      getKpiScoreCardDataStatus(item, result => {
        if (result.data.statelessDbdKpiScorecardGetDataStatus) {
          let data = result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat;
          if (item == 'MTD') {
            localStorage.setItem(
              'kpiDataStatus',
              result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
            );
          }
          // if (item == 'YESDT') {
          //   localStorage.setItem(
          //     'kpiDataStatusYesdt',
          //     result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
          //   );
          // }
          // if (item == 'DBYESDT') {
          //   localStorage.setItem(
          //     'kpiDataStatusDbYesdt',
          //     result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
          //   );
          // }
          // if (item == 'LWEEK') {
          //   localStorage.setItem(
          //     'kpiDataStatusDbLweek',
          //     result.data.statelessDbdKpiScorecardGetDataStatus.bigFloat
          //   );
          // }
          dispatch(setKpiToggle(data == 1 ? 'MTD' : 'LMONTH'));
          dispatch(setKpiHomeToggle(data == 1 ? 'MTD' : 'LMONTH'));
        }
      });
    });
    getVersionFlags(result => {
      if (result.data.statelessCcPhysicalRwGetVersionFlags) {
        let data = result.data.statelessCcPhysicalRwGetVersionFlags.getVerFlags;
        if (data[0].margument == 'VRF') {
          dispatch(setVersionFlag(data[0].mvalue));
          ReactSession.set('versionFlag', data[0].mvalue);
          localStorage.setItem('versionFlag', data[0].mvalue);
        }
        if (data[1].margument == 'MNF') {
          dispatch(setMenuFlag(data[1].mvalue));
          ReactSession.set('menuFlag', data[1].mvalue);
        }
        if (data[2].margument == 'MPF') {
          dispatch(setMpiFlag(data[2].mvalue));
          ReactSession.set('mapiFlag', data[2].mvalue);
        }
      }
    });
  }, [storeval]);
  useEffect(() => {
    if (keycloak.authenticated) {
      const spanAttribute = {
        pageUrl: '',
        origin: '',
        event: 'Page Loaded',
        value: window.location.pathname,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Page Loaded', spanAttribute);

      if (keycloak.tokenParsed.groupname[0] != '') {
        let groups = keycloak.tokenParsed.groupname;
        let groupArray = [];
        let groupIds = [];
        groups = keycloak.realm != 'lupient' ? groups.sort() : groups;
        groups.map(value => {
          groupArray.push(value.split('/')[1]);
          groupIds.push(value.split('/')[2]);
        });
        setGroups(groups);

        let stores = lodash.uniqBy(groupArray);
        let selectedStores = lodash.uniqBy(groupIds);
      }
      setTimeout(() => {
        setSnackbar(false);
        // history.push('/CPOverview');
      }, 200);
    }
  }, [
    formState.values,
    keycloak.authenticated,
    keycloak,
    session.serviceAdvisor,
    session.storeSelected,
    storeval
  ]);
  useEffect(() => {
    dispatch(setRefreshStatus(false));
  }, [session.serviceAdvisor, session.storeSelected, storeval]);
  useEffect(() => {
    history.listen(location => {
      setPath(location.search);
      dispatch(setPreviousPath(session.currentPath));
      dispatch(setCurrentPath(location.search));
    });
  }, [
    refreshStatusVal,
    session.storeSelected,
    storeval
    // , session.serviceAdvisor
  ]);

  const trigger = () => {
    setHelpStatus(!helpStatus);
    html2canvas(document.body, {
      allowTaint: true,
      useCORS: true
    }).then(canvas => {
      const data = canvas.toDataURL('image/jpeg', 0.9);
      encodeURI(data);
      setCanvasVal(data);
      // setFeedBackStatus(!feedBackStatus);
    });
  };

  const onClose = type => {
    setHelpStatus(false);
    setFeedBackStatus(false);
    if (type === 'success') {
      setOpenSnackbar(true);
    }
  };
  const handleCloseAlert = () => {
    setOpenAlertDialog(false);
  };
  const onCloseHelp = type => {
    setHelpStatus(null);
    if (type === 'success') {
      setOpenSnackbarHelp(true);
      setError(false);
    } else if (type === 'error') {
      setOpenSnackbarHelp(true);
      setError(true);
    }
  };
  const onClick = type => {
    setHelpStatus(true);
    if (type === 'success') {
      setOpenSnackbarHelp(true);
    }
  };
  const onClickReload = () => {
    dispatch(setReloadStatus(false));
    window.location.reload();
  };
  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
  };
  const handleHelpSnackbarClose = () => {
    setOpenSnackbarHelp(false);
  };
  // const handleNotificationsOpen = () => {
  //   setOpenNotifications(true);
  //   setNotificationsError(false);
  //   setNotifications([
  //     { id: 1, type: 'error', title: 'Unmapped Pay Type Codes Found.' }
  //   ]);
  // };
  const handleNotificationsOpenError = () => {
    let errorArr = notifications;

    // if (session.refreshErrorStatus === true) {
    //   setOpenNotifications(true);
    //   setNotificationsError(true);

    //   let indexArr = errorArr.findIndex(({ value }) => value == 'refreshError');

    //   if (indexArr === -1) {
    //     errorArr.push({
    //       id: 2,
    //       type: 'error',
    //       title:
    //         'Dashboard refresh has failed. Please contact support team to resolve.',
    //       value: 'refreshError'
    //     });
    //   }

    //   setNotifications(errorArr);
    // }

    if (session.payTypeErrors === true) {
      setOpenNotifications(true);
      setNotificationsError(true);
      let indexArr = errorArr.findIndex(({ value }) => value == 'payType');

      if (indexArr === -1) {
        errorArr.push({
          id: 1,
          type: 'error',
          title: 'Unmapped Pay Type Found.',
          value: 'payType'
        });
      }

      setNotifications(errorArr);
    }

    if (session.opcodeErrors === true) {
      setOpenNotifications(true);
      setNotificationsError(true);
      //let indexArr = errorArr.some(arr => arr.value == 'opcode');
      let indexArr = errorArr.findIndex(({ value }) => value == 'opcode');

      if (indexArr == -1) {
        errorArr.push({
          id: 1,
          type: 'error',
          title: 'Unmapped Opcodes Found.',
          value: 'opcode'
        });
      }

      setNotifications(errorArr);
    }
    if (session.advisorErrors === true) {
      setOpenNotifications(true);
      setNotificationsError(true);
      //let indexArr = errorArr.some(arr => arr.value == 'opcode');
      let indexArr = errorArr.findIndex(({ value }) => value == 'advisor');

      if (indexArr == -1) {
        errorArr.push({
          id: 1,
          type: 'error',
          title: 'New Advisors Found.',
          value: 'advisor'
        });
      }

      setNotifications(errorArr);
    }
    if (session.techErrors === true) {
      setOpenNotifications(true);
      setNotificationsError(true);
      //let indexArr = errorArr.some(arr => arr.value == 'opcode');
      let indexArr = errorArr.findIndex(({ value }) => value == 'technician');

      if (indexArr == -1) {
        errorArr.push({
          id: 1,
          type: 'error',
          title: 'New Technicians Found.',
          value: 'technician'
        });
      }

      setNotifications(errorArr);
    }
    if (
      session.dailyImportErrors === true &&
      (keycloak.realmAccess.roles.includes('admin') === true ||
        keycloak.realmAccess.roles.includes('superadmin') === true)
    ) {
      setOpenNotifications(true);
      setNotificationsError(true);
      //let indexArr = errorArr.some(arr => arr.value == 'opcode');
      let indexArr = errorArr.findIndex(({ value }) => value == 'data_import');

      if (indexArr == -1) {
        errorArr.push({
          id: 1,
          type: 'error',
          title: 'Daily Data Import Failed.',
          value: 'data_import'
        });
      }

      setNotifications(errorArr);
    }
    // if (
    //   session.newAdvisorErrors === true &&
    //   (keycloak.realmAccess.roles.includes('admin') === true ||
    //     keycloak.realmAccess.roles.includes('superadmin') === true)
    // ) {
    //   setOpenNotifications(true);
    //   setNotificationsError(true);
    //   //let indexArr = errorArr.some(arr => arr.value == 'opcode');
    //   let indexArr = errorArr.findIndex(
    //     ({ value }) => value == 'advisor_without_store'
    //   );

    //   if (indexArr == -1) {
    //     errorArr.push({
    //       id: 1,
    //       type: 'error',
    //       title: 'Advisor Store Assignment Required.',
    //       value: 'advisor_without_store'
    //     });
    //   }
    //   setNotifications(errorArr);
    // }
    if (
      session.newPaytypeErrors === true &&
      (keycloak.realmAccess.roles.includes('admin') === true ||
        keycloak.realmAccess.roles.includes('superadmin') === true)
    ) {
      setOpenNotifications(true);
      setNotificationsError(true);
      //let indexArr = errorArr.some(arr => arr.value ==  'opcode');
      let indexArr = errorArr.findIndex(
        ({ value }) => value == 'paytype_without_store'
      );

      if (indexArr == -1) {
        errorArr.push({
          id: 1,
          type: 'error',
          title: 'Pay Type Store Assignment Required.',
          value: 'paytype_without_store'
        });
      }
      setNotifications(errorArr);
    }
    if (
      session.modelError === true &&
      (keycloak.realmAccess.roles.includes('admin') === true ||
        keycloak.realmAccess.roles.includes('superadmin') === true)
    ) {
      setOpenNotifications(true);
      setNotificationsError(true);
      //let indexArr = errorArr.some(arr => arr.value ==  'opcode');
      let indexArr = errorArr.findIndex(
        ({ value }) => value == 'labor_grid_models'
      );

      if (indexArr == -1) {
        errorArr.push({
          id: 1,
          type: 'error',
          title: 'Grid Model Mapping Required.',
          value: 'labor_grid_models'
        });
      }
      setNotifications(errorArr);
    } else {
      const updatedErrorArr = notifications.filter(
        item => item.value !== 'labor_grid_models'
      );
      setNotifications(updatedErrorArr);
    }
  };
  const handleNotificationsClose = () => {
    setOpenNotifications(false);
  };

  const handleChangeTech = (value, type, event) => {
    value = [...new Set(value)];
    if (value.length == techNames.length - 1) {
      value = ['All'];
    }
    if (type === 'apply') {
      if (value.length > 0) {
        $('#advisorSelection').removeClass('selected');
        $('#techSelection').addClass('selected');
        const spanAttribute = {
          pageUrl: '',
          origin: '',
          event: 'Selected Technician',
          value: value ? value.toString() : '',
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Topbar', spanAttribute);
        setTechName(value);
        setTechSelected(value);
        setTechPersonName(value);
        setLoading(true);
        if (value && value.includes('All')) {
          dispatch(setTechnician(['All']));
          ReactSession.set('technicians', ['All']);
          dispatch(setSelectedTech('All[All]'));
        }
        if (value && !value.includes('All')) {
          dispatch(setTechnician(value));
          ReactSession.set('technicians', value);

          if (value.length == 1) {
            techNames.indexOf(value[0]) > -1 === true
              ? dispatch(
                  setSelectedTech(
                    technician[techNames.indexOf(value[0])].split(
                      '-status-'
                    )[0] +
                      ' [' +
                      value[0] +
                      ']'
                  )
                )
              : dispatch(setSelectedTech(value));
          }
        }
      }
    } else {
      setTechPersonName(value);
    }
  };
  const handleControlSelect = controlType => {
    setSelectedControl(controlType);
    //console.log(`Selected ${controlType}: ${value}`);
  };
  const handleChange = (value, type, event) => {
    value = [...new Set(value)];
    if (
      value.length == advisorNames.length - 1 &&
      advisorNames.length > 1 &&
      selectedControl != 'Radio'
    ) {
      value = ['All'];
    }
    if (type === 'apply') {
      if (value.length > 0) {
        $('#advisorSelection').addClass('selected');
        $('#techSelection').removeClass('selected');
        const spanAttribute = {
          pageUrl: '',
          origin: '',
          event: 'Selected service advisor',
          value: value ? value.toString() : '',
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Topbar', spanAttribute);
        setAdvisorFilter(true);
        let advisors = [];
        // if(value.length>0){
        //   advisors=lodash.concat(value.map(e=>e[0]))
        // }
        setPersonName(value);
        setAdvisorName(value);

        setSelected(value);
        setLoading(true);
        if (value && value.includes('All')) {
          dispatch(setServiceAdvisor(['All']));
          ReactSession.set('serviceAdvisors', ['All']);
          if (
            history.location.pathname == '/KpiReport' ||
            history.location.pathname == '/LaborGrossAndVolumeOpportunity' ||
            history.location.pathname == '/PartsGrossAndVolumeOpportunity' ||
            history.location.pathname == '/CPELROpportunity' ||
            history.location.pathname == '/LaborWorkMixAnalysis' ||
            (history.location.pathname == '/GraphDetailsView' &&
              (history.location.search == '?chartId=931' ||
                history.location.search == '?chartId=921' ||
                history.location.search == '?chartId=926'))

            // ||
            // (history.location.pathname == '/GraphDetailsView' &&
            //   (history.location.search == '?chartId=931' ||
            //     history.location.search == '?chartId=921' ||
            //     history.location.search == '?chartId=926' ||
            //     (Number(history.location.search.split('?chartId=').pop()) >=
            //       1243 &&
            //       Number(history.location.search.split('?chartId=').pop()) <=
            //         1248) ||
            //     (Number(history.location.search.split('?chartId=').pop()) >=
            //       1253 &&
            //       Number(history.location.search.split('?chartId=').pop()) <=
            //         1258)))
          ) {
            setSelectedKpiAdvisor(['All']);
            setPersonNameKpi(['All']);
            dispatch(setKpiReportAdvisor(['All']));
          }
        }
        if (value && !value.includes('All')) {
          dispatch(setKpiReportAdvisor(value));

          dispatch(setServiceAdvisor(value));
          ReactSession.set('serviceAdvisors', value);
          //if (history.location.pathname == '/KpiReport') {
          if (value.length >= 1) {
            value = [...new Set(value)];
            dispatch(setKpiReportAdvisor(value));
            setPersonNameKpi(value);
            console.log('valueee===', value);
            setSelectedKpiAdvisor(value);
          }
        }
      }
    } else {
      setAdvisorFilter(false);
      if (
        history.location.pathname == '/KpiReport' ||
        history.location.pathname == '/LaborGrossAndVolumeOpportunity' ||
        history.location.pathname == '/PartsGrossAndVolumeOpportunity' ||
        history.location.pathname == '/CPELROpportunity' ||
        (history.location.pathname == '/GraphDetailsView' &&
          (history.location.search == '?chartId=931' ||
            history.location.search == '?chartId=921' ||
            history.location.search == '?chartId=926'))

        // ||
        // (history.location.pathname == '/GraphDetailsView' &&
        //   (history.location.search == '?chartId=931' ||
        //     history.location.search == '?chartId=921' ||
        //     history.location.search == '?chartId=926' ||
        //     (Number(history.location.search.split('?chartId=').pop()) >= 1243 &&
        //       Number(history.location.search.split('?chartId=').pop()) <=
        //         1248) ||
        //     (Number(history.location.search.split('?chartId=').pop()) >= 1253 &&
        //       Number(history.location.search.split('?chartId=').pop()) <=
        //         1258)))
      ) {
        var advisor = lodash.difference(value, personNameKpi);
        if (personNameKpi.length == 0) {
          advisor = lodash.difference(value, selectedKpiAdvisor);
        }
        //  advisor = lodash.difference(advisor, selectedKpiAdvisor);
        if (advisor.length == 0) {
          advisor = value;
        }
        setPersonNameKpi(advisor);
      } else {
        setPersonName(value);
      }

      //setAdvisorName(value);
      //setAdvisorName(value);
      //setSelected(value);
    }
  };
  const handleROChange = event => {
    const input = event.target.value;
    //  const alphanumericRegex = /^[a-zA-Z0-9]*$/;
    const alphanumericRegex = /^[a-zA-Z0-9!@#$%^&*()\-=_+\[\]{}|;:',.<>?_]*$/;

    if (alphanumericRegex.test(input) || input === '') {
      setRoNumber(event.target.value);
    }

    // dispatch(setRONumber(event.target.value));
  };
  const handleROChangeEnter = event => {
    if (event.key === 'Enter') {
      const spanAttribute = {
        pageUrl: '',
        origin: '',
        event: 'SearchBy RO',
        value: roNumber,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Topbar', spanAttribute);
      if (roNumber != '') {
        history.push({
          pathname: '/SearchByRO',
          state: {
            ronumber: roNumber,

            pageType: 'Topbar'
          }
        });
        // setRoNumber('');
        setIsRoOpen(false);
      } else {
        setIsRoOpen(false);
      }
      if (roNumber != '') {
        history.push({
          pathname: '/SearchByRO',
          state: {
            ronumber: roNumber,

            pageType: 'Topbar'
          }
        });
        // setRoNumber('');
        setIsRoOpen(false);
      } else {
        setIsRoOpen(false);
      }
    }

    // dispatch(setRONumber(event.target.value));
  };
  const handleLogout = () => {
    handleClose();
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Logout Click',
      value: '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Topbar', spanAttribute);
    setOpenSignOutDialog(true);
  };
  const handleOk = () => {
    let storeValue = JSON.parse(localStorage.getItem('selectedStoreId'));
    let store;
    var offset = getTimeZone();
    if (storeValue) {
      let store = Object.values(storeValue);
      const client = makeApolloClientPostgres;
      const start = new Date();
      client
        .mutate({
          mutation: INSERT_LOGIN_DETAILS,
          variables: {
            // username: localStorage.getItem('storeValue'),
            username: session.currentUser,
            storeid: store[0],
            logindate: moment().format('MM-DD-YYYY HH:mm:ss'),
            offset: offset,
            logInout: 'OUT'
          }
        })
        .then(result => {
          localStorage.removeItem('kpiFilterEndDate');
          localStorage.removeItem('kpiStartDate');
          const spanAttribute = {
            pageUrl: '/Home',
            origin: '',
            event: 'Menu Load',
            is_from: 'INSERT_LOGIN_DETAILS OUT',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
        });
    }
    var logoutOptions = { redirectUri: keycloak.redirectUri };

    keycloak
      .logout(logoutOptions)
      .then(success => {
        localStorage.removeItem('selectedStoreId');
        localStorage.setItem('showCurrentMonth', false);
        delete_cookie('otContext');
        delete_cookie('userIP');
        delete_cookie('userLocation');
        localStorage.removeItem('selectedTech');

        Object.keys(localStorage).forEach(function(key, i) {
          if (
            localStorage.key(i) != null &&
            localStorage.key(i) != 'isAuthenticated' &&
            localStorage.key(i) != 'realm' &&
            localStorage.key(i) != 'versionFlag' &&
            localStorage.key(i) != 'keycloakToken' &&
            localStorage.key(i) != 'userID' &&
            !localStorage.key(i).includes('kc-callback')
          ) {
            var item = localStorage.key(i);
            localStorage.removeItem(item);
          }
        });
        localStorage.clear();
        setTimeout(() => {
          localStorage.setItem('isAuthenticated', false);
        }, 200);
        // localStorage.setItem('isAuthenticated', false);
      })
      .catch(error => {
        console.log('--> log: logout error ', error);
      });
    setOpenSignOutDialog(false);
  };
  const delete_cookie = function(name) {
    document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/';
  };
  const handleCancel = () => {
    setOpenSignOutDialog(false);
  };
  const handleCancelAllStores = () => {
    setOpenAllStoresDialog(false);
  };
  const isFilterDisabled = () => {
    return history.location.pathname === '/CPOverview' ||
      history.location.pathname === '/CPLaborOverview' ||
      history.location.pathname === '/CPPartsOverview' ||
      history.location.pathname === '/SpecialMetrics' ||
      // history.location.pathname === '/MyFavorites' ||
      isAdvisorenabledCharts(session.currentPath) === true
      ? false
      : true;
  };

  const handleChatBarOpen = () => {
    setOpenChatBar(true);
  };
  // useEffect(() => {
  //   if (session.reloadStatus == true) {
  //     setOpenAlertDialog(true);
  //   }
  // }, [session.reloadStatus]);

  useEffect(() => {
    let mounted = true;

    let opError = 0;
    let adError = 0;
    let techError = 0;

    getAllOpcodeErrors(callback => {
      const opcodeErrorArr = callback.filter(function(el) {
        return el.cType === 'opcode';
      });
      const advisorErrorArr = callback.filter(function(el) {
        return el.cType === 'advisor';
      });
      const technicianErrorArr = callback.filter(function(el) {
        return el.cType === 'technician';
      });
      const paytypeErrorArr = callback.filter(function(el) {
        return el.cType === 'paytype_errors';
      });
      const dataImportErrorArr = callback.filter(function(el) {
        return el.cType === 'data_import';
      });
      // const advNewArr = callback.filter(function(el) {
      //   return el.cType === 'advisor_without_store';
      // });
      const paytypeNewArr = callback.filter(function(el) {
        return el.cType === 'paytype_without_store';
      });
      const modelNewArr = callback.filter(function(el) {
        return el.cType === 'labor_grid_models';
      });
      var err = callback.reduce(
        (acc, o) => acc + parseInt(o.noncategorizedcount),
        0
      );

      if (
        keycloak.realmAccess.roles.includes('user') === true ||
        keycloak.realmAccess.roles.includes('client') === true
      ) {
        callback = lodash.filter(callback, function(o) {
          if (
            o.cType != 'paytype_without_store' &&
            // o.cType != 'advisor_without_store' &&
            o.cType != 'labor_grid_models' &&
            o.cType != 'data_import'
          )
            return o;
        });
      }

      var b = lodash.filter(callback, function(o) {
        if (o.noncategorizedcount > 0) return o;
      }).length;
      dispatch(setAllErrorsCount(b));
      if (opcodeErrorArr.length > 0) {
        opError = Number(opcodeErrorArr[0].noncategorizedcount);
        if (mounted) {
          if (opcodeErrorArr[0].noncategorizedcount > 0) {
            dispatch(setOpcodeError(true));
            dispatch(
              setOpcodeErrorCount(opcodeErrorArr[0].noncategorizedcount)
            );
          } else {
            dispatch(setOpcodeError(false));
            dispatch(setOpcodeErrorCount(0));
          }
        }
      } else {
        setNotifications([]);
        dispatch(setOpcodeError(false));
        dispatch(setOpcodeErrorCount(0));
      }

      if (advisorErrorArr.length > 0) {
        adError = Number(advisorErrorArr[0].noncategorizedcount);
        if (mounted) {
          if (advisorErrorArr[0].noncategorizedcount > 0) {
            dispatch(setServiceAdvisorErrors(true));
            dispatch(
              setServiceAdvisorErrorsCount(
                advisorErrorArr[0].noncategorizedcount
              )
            );
          } else {
            dispatch(setServiceAdvisorErrors(false));
            dispatch(setServiceAdvisorErrorsCount(0));
          }
        }
      } else {
        setNotifications([]);
        dispatch(setServiceAdvisorErrors(false));
        dispatch(setServiceAdvisorErrorsCount(0));
      }
      if (technicianErrorArr.length > 0) {
        techError = Number(technicianErrorArr[0].noncategorizedcount);

        if (mounted) {
          if (technicianErrorArr[0].noncategorizedcount > 0) {
            dispatch(setTechnicianErrors(true));
            dispatch(
              setTechnicianErrorsCount(
                technicianErrorArr[0].noncategorizedcount
              )
            );
          } else {
            dispatch(setTechnicianErrors(false));
            dispatch(setTechnicianErrorsCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setTechnicianErrors(false));
        dispatch(setTechnicianErrorsCount(0));
      }
      if (paytypeErrorArr.length > 0) {
        //paytypeError = Number(paytypeErrorArr[0].noncategorizedcount);

        if (mounted) {
          if (paytypeErrorArr[0].noncategorizedcount > 0) {
            dispatch(setPayTypeError(true));
            dispatch(
              setPayTypeErrorCount(paytypeErrorArr[0].noncategorizedcount)
            );
          } else {
            dispatch(setPayTypeError(false));
            dispatch(setPayTypeErrorCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setPayTypeError(false));
        dispatch(setPayTypeErrorCount(0));
      }
      if (dataImportErrorArr.length > 0) {
        //paytypeError = Number(paytypeErrorArr[0].noncategorizedcount);

        if (mounted) {
          if (dataImportErrorArr[0].noncategorizedcount > 0) {
            dispatch(setDailyImportError(true));
            dispatch(
              setDailyImportErrorCount(
                dataImportErrorArr[0].noncategorizedcount
              )
            );
          } else {
            dispatch(setDailyImportError(false));
            dispatch(setDailyImportErrorCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setDailyImportError(false));
        dispatch(setDailyImportErrorCount(0));
      }

      // if (advNewArr.length > 0) {
      //   if (mounted) {
      //     if (advNewArr[0].noncategorizedcount > 0) {
      //       dispatch(setNewAdvisors(true));
      //       dispatch(
      //         setNewAdvisorsErrorCount(advNewArr[0].noncategorizedcount)
      //       );
      //     } else {
      //       dispatch(setNewAdvisors(false));
      //       dispatch(setNewAdvisorsErrorCount(0));
      //     }
      //   }
      // } else {
      //   setNotifications([]);

      //   dispatch(setNewAdvisors(false));
      //   dispatch(setNewAdvisorsErrorCount(0));
      // }
      if (paytypeNewArr.length > 0) {
        if (mounted) {
          if (paytypeNewArr[0].noncategorizedcount > 0) {
            dispatch(setNewPaytype(true));
            dispatch(
              setNewPaytypeErrorCount(paytypeNewArr[0].noncategorizedcount)
            );
          } else {
            dispatch(setNewPaytype(false));
            dispatch(setNewPaytypeErrorCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setNewPaytype(false));
        dispatch(setNewPaytypeErrorCount(0));
      }
      if (modelNewArr.length > 0) {
        if (mounted) {
          if (modelNewArr[0].noncategorizedcount > 0) {
            dispatch(setNewModel(true));
            dispatch(setNewModelErrorCount(modelNewArr[0].noncategorizedcount));
          } else {
            dispatch(setNewModel(false));
            dispatch(setNewModelErrorCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setNewModel(false));
        dispatch(setNewModelErrorCount(0));
      }
    });
    // getDailyImportDataDetails(result => {
    //   console.log('resttt==', result);
    // });
    getAllServiceAdvisors(result => {
      if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
        var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
          e => e.active == 1
        );
        const updatedAdvisors = advArr.map(advisor => ({
          ...advisor,
          nickname: advisor.nickname || advisor.name
        }));
        let sortedAdvisors = updatedAdvisors.sort((a, b) =>
          a.nickname.localeCompare(b.nickname)
        );
        sortedAdvisors = sortedAdvisors.filter(
          item => item.department === 'Service'
        );
        console.log('sortedAdvisors====', sortedAdvisors);
        var advisorsActive = advArr.map(function(i) {
          return i.serviceadvisor;
        });
        var aaa = sortedAdvisors.map(e =>
          e ? e.serviceadvisor.toString() : ''
        );
        dispatch(setActiveAdvisors(advisorsActive));
        setNames(
          ['All'].concat(
            sortedAdvisors.map(e => (e ? e.serviceadvisor.toString() : ''))
          )
        );

        if (!advisorName.includes('All')) {
          if (lodash.intersection(advisorName, advisorsActive).length > 0) {
            setAdvisorName(lodash.intersection(advisorName, advisorsActive));
            setSelected(lodash.intersection(selected, advisorsActive));
            setPersonName(lodash.intersection(advisorName, advisorsActive));

            dispatch(
              setServiceAdvisor(
                lodash.intersection(advisorName, advisorsActive)
              )
            );
            ReactSession.set(
              'serviceAdvisors',
              lodash.intersection(advisorName, advisorsActive)
            );
          } else {
            setAdvisorName(['All']);
            setSelected(['All']);
            setPersonName(['All']);
            dispatch(setServiceAdvisor(['All']));
            ReactSession.set('serviceAdvisors', ['All']);
          }
        }
        dispatch(
          setAllAdvisorIds(
            ['All'].concat(
              advArr.map(e => (e ? e.serviceadvisor.toString() : ''))
            )
          )
        );

        setadvisorNames(
          ['All'].concat(
            sortedAdvisors.map(e =>
              e
                ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active
                : ''
            )
          )
        );
        dispatch(
          setAllAdvisorNames(
            ['All'].concat(
              advArr.map(e =>
                e
                  ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active
                  : ''
              )
            )
          )
        );
      }
    });
    getAllTechnicians(result => {
      if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
        var techArr = result.data.statelessCcPhysicalRwGetTechnicians.nodes.filter(
          e => e.active == 1
        );
        const techDepartment = techArr.filter(
          item => item.department === 'Service'
        );
        var techActive = techDepartment.map(function(i) {
          return i.lbrtechno;
        });
        dispatch(setActiveTechs(techActive));
        // setTechnician(
        //   result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(
        //     e => `${e.name} [${e.lbrtechno.toString()}]`
        //   )
        // );
        const updatedArray = techDepartment.map(item => ({
          ...item,
          nickname: item.nickname ? item.nickname : item.name
        }));

        // Sort the array by `nickname`
        const sortedArray = updatedArray.sort((a, b) =>
          a.nickname.localeCompare(b.nickname)
        );
        setTechNames(
          ['All'].concat(
            sortedArray.map(e => (e ? e.lbrtechno.toString() : ''))
          )
        );
        if (!techName.includes('All')) {
          if (lodash.intersection(techName, techActive).length > 0) {
            setTechName(lodash.intersection(techName, techActive));
            setTechSelected(lodash.intersection(techSelected, techActive));
            setTechPersonName(lodash.intersection(techName, techActive));
            dispatch(setTechnician(lodash.intersection(techName, techActive)));
            ReactSession.set(
              'technicians',
              lodash.intersection(techName, techActive)
            );
          } else {
            setTechName(['All']);
            setTechSelected(['All']);
            setTechPersonName(['All']);
            dispatch(setTechnician(['All']));
            ReactSession.set('technicians', ['All']);
          }
        }
        // Sort the array by `nickname` in ascending order (case-insensitive)
        const sortedTechnicians = [...techDepartment].sort((a, b) => {
          const nicknameA = a.nickname ? a.nickname.toLowerCase() : '';
          const nicknameB = b.nickname ? b.nickname.toLowerCase() : '';

          // Sort null/empty nicknames last
          if (nicknameA === '' && nicknameB === '') return 0;
          if (nicknameA === '') return 1;
          if (nicknameB === '') return -1;

          // Case-insensitive comparison
          return nicknameA.localeCompare(nicknameB);
        });
        const dataValue = sortedTechnicians.map(e =>
          e ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active : ''
        );
        const sortedData = [...dataValue].sort((a, b) =>
          a.toLowerCase().localeCompare(b.toLowerCase())
        );
        setTechnicianSelection(['All'].concat(sortedData));

        //  settechnicianSelected(result.data.dms_physical_rw_vw_tbl_technicians[0].name+
        //   ' [' +result.data.dms_physical_rw_vw_tbl_technicians[0].lbrtechno.toString() + ']')
      }
    });
    // localStorage.setItem('showCurrentMonth', true);

    getUniqueOpcodes(result => {
      const filteredOpcategoryArry = [];
      if (result.data.statelessCcPhysicalRwGetOpcategoryCodes.nodes) {
        result.data.statelessCcPhysicalRwGetOpcategoryCodes.nodes.map(
          (item, i) => {
            filteredOpcategoryArry.push(
              result.data.statelessCcPhysicalRwGetOpcategoryCodes.nodes[i]
                .opcategory
            );
          }
        );
      }
      let unique = filteredOpcategoryArry.filter(
        (item, i, ar) => ar.indexOf(item) === i
      );

      localStorage.setItem('category', unique);
    });

    return () => {
      mounted = false;
    };
  }, [
    dispatch,
    session.advisorStatusChanged,
    session.advisorNickNameChanged,
    session.storeSelected,
    session.techStatusChanged,
    storeval
    //session.activeAdvisors
    // session.serviceAdvisor
  ]);

  useEffect(() => {
    if (
      history.location.pathname == '/WorkMixVolume' ||
      history.location.pathname == '/WorkMixVolumeParts' ||
      history.location.pathname == '/TechnicianPerformance' ||
      history.location.pathname == '/Discounts' ||
      history.location.pathname == '/MyFavorites' ||
      history.location.pathname == '/RevenueSummary' ||
      history.location.pathname == '/WarrantyReferenceParts' ||
      history.location.pathname == '/WarrantyReferenceLabor' ||
      history.location.pathname == '/DailyDataImports' ||
      history.location.pathname == '/StoreSettings' ||
      history.location.pathname == '/EditHistoryAll' ||
      history.location.pathname == '/UserLoginHistory' ||
      history.location.pathname == '/GridModelMapping' ||
      history.location.pathname == '/MenuModelMapping' ||
      history.location.pathname == '/Mpi' ||
      history.location.pathname == '/Email' ||
      history.location.pathname == '/ChartMaster' ||
      history.location.pathname == '/KeycloakUserCreation' ||
      history.location.pathname == '/Glossary' ||
      history.location.pathname == '/ServiceAdvisors' ||
      history.location.pathname == '/Technicians' ||
      history.location.pathname == '/Makes' ||
      history.location.pathname == '/PayTypeMaster' ||
      history.location.pathname == '/OPcodes' ||
      history.location.pathname == '/FixedRates' ||
      history.location.pathname == '/FleetAccounts' ||
      history.location.pathname == '/NewCarWarranty' ||
      history.location.pathname == '/SharedSequencesByMake' ||
      history.location.pathname == '/MPIOpcode' ||
      history.location.pathname == '/MenuOpcode' ||
      history.location.pathname == '/ScoreCardGoalSetting' ||
      history.location.pathname == '/LaborGridPricing' ||
      history.location.pathname == '/PartsMatrix' ||
      history.location.pathname == '/EditHistory' ||
      history.location.pathname == '/WarrantyRatesLabor' ||
      history.location.pathname == '/WarrantyMarkupParts' ||
      history.location.pathname == '/CustomerHistory' ||
      (history.location.pathname == '/GraphDetailsView' &&
        history.location.state &&
        history.location.state.SelectedLocation &&
        history.location.state.SelectedLocation == '/TechnicianPerformance')
    ) {
      $('#advisorSelection').css('display', 'none');
    }

    if (history.location.pathname != '/SearchByRO') {
      setRoNumber('');
    } else if (history.location.pathname == '/SearchByRO') {
      if (history) {
        if (history.location) {
          if (history.location.state) {
            if (history.location.state.pageType == 'Topbar') {
              if (history.location.state.ronumber) {
                setRoNumber(history.location.state.ronumber);
              }
            }
          }
        }
      }
    }
    if (
      history.location.pathname == '/KpiReport' ||
      history.location.pathname == '/LaborGrossAndVolumeOpportunity' ||
      history.location.pathname == '/PartsGrossAndVolumeOpportunity' ||
      history.location.pathname == '/CPELROpportunity' ||
      (history.location.pathname == '/GraphDetailsView' &&
        (history.location.search == '?chartId=931' ||
          history.location.search == '?chartId=921' ||
          history.location.search == '?chartId=926'))

      // ||
      // (history.location.pathname == '/GraphDetailsView' &&
      //   (history.location.search == '?chartId=931' ||
      //     history.location.search == '?chartId=921' ||
      //     history.location.search == '?chartId=926' ||
      //     (Number(history.location.search.split('?chartId=').pop()) >= 1243 &&
      //       Number(history.location.search.split('?chartId=').pop()) <= 1248) ||
      //     (Number(history.location.search.split('?chartId=').pop()) >= 1253 &&
      //       Number(history.location.search.split('?chartId=').pop()) <= 1258)))
    ) {
      if (selected.length == 1) {
        console.log('valueee===selected', session.serviceAdvisor);
        setSelectedKpiAdvisor(selected);
        setPersonNameKpi(selected);
        dispatch(setKpiReportAdvisor(selected));
      } else {
        setSelectedKpiAdvisor(['All']);
        setPersonNameKpi(['All']);
        dispatch(setKpiReportAdvisor(['All']));
        if (
          localStorage.getItem('prevLocation') &&
          (localStorage.getItem('prevLocation') ==
            '/LaborGrossAndVolumeOpportunity' ||
            localStorage.getItem('prevLocation') == '/CPELROpportunity' ||
            localStorage.getItem('prevLocation') ==
              '/PartsGrossAndVolumeOpportunity')
        ) {
          dispatch(setServiceAdvisor(['All']));
          setSelected(['All']);
          setPersonName(['All']);
          setAdvisorName(['All']);
          ReactSession.set('serviceAdvisors', ['All']);
          localStorage.setItem('prevLocation', '');
        }
      }
    }

    if (
      localStorage.getItem('prevLocation') &&
      (localStorage.getItem('prevLocation') ==
        '/LaborGrossAndVolumeOpportunity' ||
        localStorage.getItem('prevLocation') == '/CPELROpportunity' ||
        localStorage.getItem('prevLocation') ==
          '/PartsGrossAndVolumeOpportunity') &&
      selected.length > 1
    ) {
      dispatch(setServiceAdvisor(['All']));
      setSelected(['All']);
      setPersonName(['All']);
      setAdvisorName(['All']);
      ReactSession.set('serviceAdvisors', ['All']);
      localStorage.setItem('prevLocation', '');
    }
    // if (
    //   history &&
    //   history.location &&
    //   history.location.isFrom &&
    //   history.location.isFrom == 'opportunity' &&
    //   selected.length > 1
    // ) {
    //   dispatch(setServiceAdvisor(['All']));
    //   setSelected(['All']);
    //   setPersonName(['All']);
    // }
  }, [
    history.location.pathname,
    session.serviceAdvisor,
    session.storeSelected,
    storeval
  ]);

  useEffect(() => {
    dispatch(setUsername(keycloak.tokenParsed.preferred_username));
    if (session.userName) {
      getAllFavouriteCharts(session.userName, callback => {
        if (callback) {
          dispatch(setFavouriteCharts(callback.map(data => data.favourites)));
        }
      });
    }
  }, [
    dispatch,
    session.userName,
    session.storeSelected,
    storeval
    // , session.serviceAdvisor
  ]);

  const handleSearchByRo = params => {
    if (roNumber != '') {
      history.push({
        pathname: '/SearchByRO',
        state: {
          ronumber: roNumber,

          pageType: 'Topbar'
        }
      });
      // setRoNumber('');
      setIsRoOpen(false);
    } else {
      setIsRoOpen(false);
    }
  };
  useEffect(() => {
    if (keycloak.authenticated && keycloak.token) {
      dispatch(setToken(`Bearer ${keycloak.token}`));
      let userrole = setUserRole();

      setUserType(userrole);
    }
  }, [dispatch, keycloak.authenticated, keycloak.token]);
  useEffect(() => {
    localStorage.removeItem('popup');
    getStoreRetialFlags(result => {
      let storeRetailFlags;

      if (result.data.statelessCcPhysicalRwGetMappedPaytype) {
        storeRetailFlags =
          result.data.statelessCcPhysicalRwGetMappedPaytype.strings;
      } else {
        storeRetailFlags = ['C'];
      }
      // dispatch(setRetailFlag(storeRetailFlags));
      localStorage.setItem('retailFlags', storeRetailFlags);
    });
    getLatestClosedDate(result => {
      if (result) {
        var openDate = '';
        var Date1 = result[0].value;

        localStorage.setItem('closedDate', Date1);
      }
    });
    getLatestOpenDate(result => {
      if (result) {
        var resultDate = '';
        var openDate = '';
        var Date1 = result[0].value;
        localStorage.setItem('openDate', Date1);
      }
    });
    setclientGroup(localStorage.getItem('storeGroup'));
    const selectedStoreId = JSON.parse(localStorage.getItem('selectedStoreId'));

    if (keycloak.tokenParsed.groupname[0] != '') {
      let groups = keycloak.tokenParsed.groupname;
      console.log('groups===', groups);
      let groupArray = [];
      let groupIds = [];
      groups =
        keycloak.realm != 'lupient' &&
        keycloak.realm != 'billknightag' &&
        keycloak.realm != 'karlflammerford'
          ? groups.sort()
          : groups;
      groups.map(value => {
        if (value.split('/').slice(1, -1).length > 1) {
          groupArray.push(
            value
              .split('/')
              .slice(1, -1)
              .join('/')
          );
        } else {
          groupArray.push(value.split('/')[1]);
        }
        groupIds.push(value.split('/').pop());
      });
      console.log('groupIdss', groupIds);
      localStorage.setItem('allPermittedStores', JSON.stringify(groupIds));
      setGroups(groups);
      let stores = lodash.uniqBy(groupArray);
      let storeData = [];
      getAllStoreNames(groupIds, result => {
        if (result.length > 0) {
          result.map((val, i) => {
            storeData.push(stores[0].split('-')[0] + '-' + val.storeName);
          });
          localStorage.setItem('storeGroup', stores[0].split('-')[0]);
          // localStorage.setItem('storeSelected', result[0].storeName);
          let storeArr = [result[0].storeId];
          // localStorage.setItem('selectedStoreId', JSON.stringify(storeArr));
          setstoreName(storeData);
        }
      });

      let selectedStores = lodash.uniqBy(groupIds);

      // setstoreName(stores);
      setstoreSlected(localStorage.getItem('storeSelected'));
    }

    let clientName = '';
    let clientDesc = '';
    let clientSite = '';
    let storeDMS = '';
    let storeDMSImg = '';
    if (localStorage.getItem('storeSelected') == 'All Stores') {
      let clientDesc = '';
      let clientSite = '';
      let storeDMS = '';
      let storeDMSImg = '';
      // Do nothing or handle the case when 'All Permitted Stores' is selected
      let clientName = 'All Stores ';

      localStorage.setItem('storeNames', clientName);
      localStorage.setItem('storeManufacturer', clientName);

      localStorage.setItem('storeData', '');
      setDmsName(storeDMS);
      setclientName(clientName);
      setClientWebsite(clientSite);
      setclientDesc(clientDesc);
      localStorage.setItem('dms', storeDMS.toLowerCase());
    } else {
      getStoreDetails(selectedStoreId, result => {
        if (result.data.statelessCcPhysicalRoGetStoreMasters.nodes.length > 0) {
          clientName =
            result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
              'storeName'
            ] +
            ' ' +
            (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
              ? ' , ' +
                result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
                  .storeName
              : '');
          clientSite =
            result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0].website;
          storeDMS =
            result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0].dms;
          storeDMSImg =
            result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0].dmsImg;
          setDmsName(storeDMS);
          setclientName(clientName);

          setClientWebsite(clientSite);
          localStorage.setItem('dms', storeDMS.toLowerCase());
          localStorage.setItem('dmsImg', storeDMSImg);
          clientDesc =
            result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
              'storeDescription'
            ] +
            ' ' +
            (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
              ? ' , ' +
                result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1][
                  'storeDescription'
                ]
              : '');
          setclientDesc(clientDesc);
          localStorage.setItem('storeNames', clientName);
          localStorage.setItem(
            'storeManufacturer',
            result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
              'manufacturer'
            ]
          );
          localStorage.setItem(
            'storeData',
            JSON.stringify(
              result.data.statelessCcPhysicalRoGetStoreMasters.nodes
            )
          );
        }
      });
    }

    // getStoreDetails(selectedStoreId, result => {
    //   if (result.data.statelessCcPhysicalRoGetStoreMasters.nodes.length > 0) {
    //     console.log('111111111111111');
    //     clientName =
    //       result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
    //         'storeName'
    //       ] +
    //       ' ' +
    //       (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
    //         ? ' , ' +
    //           result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
    //             .storeName
    //         : '');
    //     clientSite =
    //       result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0].website;
    //     storeDMS =
    //       result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0].dms;
    //     setclientName(clientName);

    //     setClientWebsite(clientSite);
    //     localStorage.setItem('dms', storeDMS);
    //     clientDesc =
    //       result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
    //         'storeDescription'
    //       ] +
    //       ' ' +
    //       (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
    //         ? ' , ' +
    //           result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1][
    //             'storeDescription'
    //           ]
    //         : '');
    //     setclientDesc(clientDesc);
    //     localStorage.setItem('storeNames', clientName);
    //     localStorage.setItem(
    //       'storeManufacturer',
    //       result.data.dmsPhysicalRoVwStoreMasters.nodes[0]['manufacturer']
    //     );
    //     localStorage.setItem(
    //       'storeData',
    //       JSON.stringify(result.data.statelessCcPhysicalRoGetStoreMasters.nodes)
    //     );
    //   }
    // });
    // checkInternalToggle();
  }, [
    session.storeSelected,
    storeval,
    session.userGroupsChanged
    // session.retailFlag
    // session.serviceAdvisor
  ]);

  const checkInternalToggle = () => {
    // getPartsPayTypeList();
    let data = [];
    data.push('Customer');
    // getGridorMatrixPayTypeDetails('paytype_grid', '', result => {
    //   if (result.length > 0) {
    //     if (result.includes('Internal')) {
    //       result.sort();
    //     }
    //     if (
    //       result.includes('Heavy Duty') ||
    //       result.includes('Fleet') ||
    //       result.includes('Commercial')
    //     ) {
    //       if (
    //         result[0] == 'Heavy Duty' ||
    //         result[0] == 'Fleet' ||
    //         (result[0] == 'Commercial' && !result.includes('RV'))
    //       ) {
    //         result.reverse();
    //       }
    //     }
    //     if (
    //       result.includes('Commercial') &&
    //       result.includes('RV') &&
    //       result.includes('Retail')
    //     ) {
    //       result = ['Retail', 'Commercial', 'RV'];
    //     }
    //     if (
    //       result.includes('Warranty') &&
    //       result.includes('Standard') &&
    //       result.includes('Fleet')
    //     ) {
    //       result = ['Standard', 'Warranty', 'Fleet'];
    //     }
    //     if (
    //       result.includes('Warranty') &&
    //       result.includes('Standard') &&
    //       result.includes('Fleet')
    //     ) {
    //       result = ['Standard', 'Warranty', 'Fleet'];
    //     }
    //     if (result.includes('Highline')) {
    //       result.reverse();
    //     }
    //     if (
    //       result.includes('Diesel') ||
    //       result.includes('HD & Hybrid') ||
    //       result.includes('Electric')
    //     ) {
    //       result.reverse();
    //     }
    //     if (
    //       result.includes('Honda Grid') &&
    //       result.includes('Volvo Grid') &&
    //       result.includes('Merc-Benz Grid')
    //     ) {
    //       result = ['Honda Grid', 'Volvo Grid', 'Merc-Benz Grid'];
    //     }
    //     if (
    //       result.includes('25-5500/Dsl') &&
    //       result.includes('Car/Lt Trk') &&
    //       result.includes('Flt-Sptr')
    //     ) {
    //       result = ['Car/Lt Trk', '25-5500/Dsl', 'Flt-Sptr'];
    //     }
    //     if (
    //       result.includes('Hyundai') &&
    //       result.includes('Genesis') &&
    //       result.includes('Customer')
    //     ) {
    //       result = ['Hyundai', 'Genesis', 'Customer'];
    //     }
    //     if (
    //       result.includes('EV') &&
    //       result.includes('Medium/HD') &&
    //       result.includes('Standard')
    //     ) {
    //       result = ['Standard', 'Medium/HD', 'EV'];
    //     }
    //     if (result.includes('HD') && result.includes('Standard')) {
    //       result = ['Standard', 'HD'];
    //     }
    //     dispatch(setLaborGridTypes(result));
    //     localStorage.setItem('laborGridTypes', JSON.stringify(result));
    //   } else {
    //     dispatch(setLaborGridTypes(data));
    //     localStorage.setItem('laborGridTypes', JSON.stringify(data));
    //   }
    // });
  };
  const getPartsPayTypeList = () => {
    let data = [];
    data.push('Customer');
    // getGridorMatrixPayTypeDetails('paytype_matrix', '', result => {
    //   if (result.length > 0) {
    //     if (result.includes('Internal')) {
    //       result.sort();
    //     }
    //     if (
    //       result.includes('Heavy Duty') ||
    //       result.includes('Fleet') ||
    //       result.includes('Commercial')
    //     ) {
    //       if (
    //         result[0] == 'Heavy Duty' ||
    //         result[0] == 'Fleet' ||
    //         (result[0] == 'Commercial' && !result.includes('RV'))
    //       ) {
    //         result.reverse();
    //       }
    //     }
    //     if (
    //       result.includes('Commercial') &&
    //       result.includes('RV') &&
    //       result.includes('Retail')
    //     ) {
    //       result = ['Retail', 'Commercial', 'RV'];
    //     }
    //     if (
    //       result.includes('Warranty') &&
    //       result.includes('Standard') &&
    //       result.includes('Fleet')
    //     ) {
    //       result = ['Standard', 'Warranty', 'Fleet'];
    //     }
    //     if (
    //       result.includes('Warranty') &&
    //       result.includes('Standard') &&
    //       result.includes('Fleet')
    //     ) {
    //       result = ['Standard', 'Warranty', 'Fleet'];
    //     }
    //     if (result.includes('Highline')) {
    //       result.reverse();
    //     }
    //     dispatch(setPartsMatrixTypes(result));
    //     localStorage.setItem('partsMatrixTypes', JSON.stringify(result));
    //   } else {
    //     dispatch(setPartsMatrixTypes(result));
    //     localStorage.setItem('partsMatrixTypes', JSON.stringify(data));
    //   }
    // });
  };
  const addServiceAdvisor = () => {
    history.push('/ServiceAdvisor');
  };
  const handleHover = () => {
    return setShowMessage(renderList);
  };
  const handleLeave = () => {
    return setShowMessage('');
  };
  const handleCloseContact = () => {
    setShowMessage(false);
  };
  const renderList = () => {
    return (
      <div className={clsx('likes__list', 'contactPopup')}>
        <Typography
          id="contact-us-header"
          variant="h5"
          style={{ textAlign: 'center' }}
        >
          Contact Us{' '}
        </Typography>
        <MenuList style={{ padding: '2px' }} id="contact-us-menu-list">
          <MenuItem
            style={{ pointerEvents: 'none' }}
            id="contact-us-phone-menu-item"
          >
            <ListItemIcon>
              <PhoneIcon fontSize="small" id="phone-icon" />
            </ListItemIcon>

            <ListItemText id="phone-number">+****************</ListItemText>
          </MenuItem>
          <MenuItem id="contact-us-email-menu-item">
            <ListItemIcon>
              <EmailIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              id="email-text"
              primary="<EMAIL>"
              onClick={() => (window.location = 'mailto:<EMAIL>')}
            >
              <Link
                to="#"
                onClick={() => (window.location = 'mailto:<EMAIL>')}
                id="email-link"
              >
                <EMAIL>
              </Link>
            </ListItemText>
          </MenuItem>
        </MenuList>
      </div>
    );
  };
  // let storeidsSaved = JSON.parse(localStorage.getItem('selectedStoreId'))
  //   .length;
  // console.log('sel', storeidsSaved);
  let Dealer = process.env.REACT_APP_DEALER;
  const handleOpenStore = event => {
    setOpenStore(true);
  };
  const handleChanges = event => {
    let groups = keycloak.tokenParsed.groupname;
    let groupArray = [];
    let groupIds = [];
    groups = keycloak.realm != 'lupient' ? groups.sort() : groups;
    groups.map(value => {
      groupArray.push(value.split('/')[1]);
      groupIds.push(value.split('/')[2]);
    });
    setGroups(groups);
    setstoreSlected(event.target.innerText);
    let storeId = [];
    storeId.push(event.target.innerText);
    let groupIdss = [];
    groupList.map(value => {
      if (value.includes(event.target.innerText)) {
        groupIdss.push(value.split('/')[2]);
      }
    });
    localStorage.setItem('storeSelected', event.target.innerText);
    localStorage.setItem('selectedStoreId', JSON.stringify(groupIdss));
    ReactSession.set('selectedStoreId', JSON.stringify(groupIdss));
    if (event.target.id == 'login') {
      if (!keycloak.authenticated) {
        keycloak.login();
      } else {
      }
    } else {
      if (!keycloak.authenticated) {
        keycloak.login();
      } else {
        const spanAttribute = {
          pageUrl: '',
          origin: '',
          event: 'Store changed',
          value: event.target.innerText,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Store changed', spanAttribute);
        if (JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1) {
          localStorage.removeItem('popup');
          getLatestClosedDate(result => {
            if (result) {
              var openDate = '';
              var Date1 = result[0].value;

              localStorage.setItem('closedDate', Date1);
            }
          });
          getLatestOpenDate(result => {
            if (result) {
              var resultDate = '';
              var openDate = '';
              var Date1 = result[0].value;
              localStorage.setItem('openDate', Date1);
            }
          });
          setclientGroup(localStorage.getItem('storeSelected'));
          const selectedStoreId = JSON.parse(
            localStorage.getItem('selectedStoreId')
          );
          let stores = lodash.uniqBy(groupArray);
          let selectedStores = lodash.uniqBy(groupIds);

          setstoreName(stores);
          // setstoreSlected(stores[0]);
          let clientName = '';
          let clientDesc = '';
          getStoreDetails(selectedStoreId, result => {
            if (
              result.data.statelessCcPhysicalRoGetStoreMasters.nodes.length > 0
            ) {
              clientName =
                result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
                  'storeName'
                ] +
                ' ' +
                (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
                  ? ' , ' +
                    result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
                      .storeName
                  : '');

              setclientName(clientName);

              clientDesc =
                result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
                  'storeDescription'
                ] +
                ' ' +
                (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
                  ? ' , ' +
                    result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1][
                      'storeDescription'
                    ]
                  : '');
              setclientDesc(clientDesc);
              localStorage.setItem('storeNames', clientName);
              localStorage.setItem(
                'storeManufacturer',
                result.data.dmsPhysicalRoVwStoreMasters.nodes[0]['manufacturer']
              );
              localStorage.setItem(
                'storeData',
                JSON.stringify(
                  result.data.statelessCcPhysicalRoGetStoreMasters.nodes
                )
              );
            }
          });
          localStorage.setItem('storeChange', true);
          dispatch(setServiceAdvisor(['All']));
          let mounted = true;

          getAllServiceAdvisors(result => {
            if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
              var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
                e => e.active == 1
              );
              const serviceDepartment = advArr.filter(
                item => item.department === 'Service'
              );
              setNames(
                ['All'].concat(
                  serviceDepartment.map(e =>
                    e ? e.serviceadvisor.toString() : ''
                  )
                )
              );
              setadvisorNames(
                ['All'].concat(
                  serviceDepartment.map(e =>
                    e ? e.name + '-status-' + e.active : ''
                  )
                )
              );
            }
          });
          getAllTechnicians(result => {
            if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
              var techArr = result.data.statelessCcPhysicalRwGetTechnicians.nodes.filter(
                e => e.active == 1
              );
              const techDepartment = techArr.filter(
                item => item.department === 'Service'
              );
              // setTechnician(
              //   result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(
              //     e => `${e.name} [${e.lbrtechno.toString()}]`
              //   )
              // );
              setTechNames(
                ['All'].concat(
                  techDepartment.map(e => (e ? e.lbrtechno.toString() : ''))
                )
              );

              // Sort the array by `nickname` in ascending order (case-insensitive)
              const sortedTechnicians = [...techDepartment].sort((a, b) => {
                const nicknameA = a.nickname ? a.nickname.toLowerCase() : '';
                const nicknameB = b.nickname ? b.nickname.toLowerCase() : '';

                // Sort null/empty nicknames last
                if (nicknameA === '' && nicknameB === '') return 0;
                if (nicknameA === '') return 1;
                if (nicknameB === '') return -1;

                // Case-insensitive comparison
                return nicknameA.localeCompare(nicknameB);
              });
              const dataValue = sortedTechnicians.map(e =>
                e
                  ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active
                  : ''
              );
              const sortedData = [...dataValue].sort((a, b) =>
                a.toLowerCase().localeCompare(b.toLowerCase())
              );
              setTechnicianSelection(['All'].concat(sortedData));
              // setTechnicianSelection(
              //   ['All'].concat(
              //     techArr.map(e =>
              //       e
              //         ? (e.nickname ? e.nickname : e.name) +
              //           '-status-' +
              //           e.active
              //         : ''
              //     )
              //   )
              // );
              // setTechnician(
              //   result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(
              //     e => `${e.name} [${e.lbrtechno.toString()}]`
              //   )
              // );

              // setTechNames(
              //   ['All'].concat(
              //     result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(e =>
              //       e ? e.lbrtechno.toString() : ''
              //     )
              //   )
              // );

              // setTechnicianSelection(
              //   ['All'].concat(
              //     result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(e =>
              //       e
              //         ? (e.nickname ? e.nickname : e.name) +
              //           '-status-' +
              //           e.active
              //         : ''
              //     )
              //   )
              // );

              //  settechnicianSelected(result.data.dms_physical_rw_vw_tbl_technicians[0].name+
              //   ' [' +result.data.dms_physical_rw_vw_tbl_technicians[0].lbrtechno.toString() + ']')
            }
          });
        } else {
          history.push('/CPOverview');
        }
      }
      // }
    }
    setOpenStore(false);
  };

  const setUserRole = () => {
    let userrole = '';
    if (keycloak.realmAccess.roles.length >= 1) {
      if (keycloak.realmAccess.roles.includes('superadmin')) {
        userrole = 'Super Admin';
      } else if (keycloak.realmAccess.roles.includes('admin')) {
        userrole = 'Armatus Admin';
      } else if (
        keycloak.realmAccess.roles.includes('user') &&
        keycloak.tokenParsed.groupname.length > 1
      ) {
        userrole = 'Group Admin';
      } else if (
        keycloak.realmAccess.roles.includes('user') &&
        keycloak.tokenParsed.groupname.length <= 1
      ) {
        userrole = 'Store Admin';
      } else if (
        keycloak.realmAccess.roles.includes('client') &&
        keycloak.tokenParsed.groupname.length > 1
      ) {
        userrole = ' Group Viewer ';
      } else if (
        keycloak.realmAccess.roles.includes('client') &&
        keycloak.tokenParsed.groupname.length <= 1
      ) {
        userrole = 'Store Viewer';
      }
    }

    return userrole;
  };
  const handleChangeStore = (event, value) => {
    if (
      event == 'All Stores' &&
      history.location.pathname != '/Home' &&
      history.location.pathname != '/KpiGraphics' &&
      history.location.pathname != '/KPIReportStoreComparative' &&
      history.location.pathname != '/ChangeLog' &&
      history.location.pathname != '/LaborMisses' &&
      history.location.pathname != '/PartsMisses' &&
      history.location.pathname != '/MPIStats' &&
      history.location.pathname != '/ReportSaved' &&
      history.location.pathname != '/Email' &&
      history.location.pathname != '/ThreeMonthReport' &&
      history.location.pathname != '/OneMonthReport' &&
      history.location.pathname != '/SavedReports'
    ) {
      setOpenAllStoresDialog(true);
    } else {
      window.sortState = {};
      window.filterState = {};
      window.sortStateLbrMiss = {};
      window.filterStateLbrMiss = {};
      window.sortStatePrtMiss = {};
      window.filterStatePrtMiss = {};

      // let kpiToggle =
      //   localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';
      // dispatch(setKpiToggle(kpiToggle));
      dispatch(setServiceAdvisor(['All']));
      setSelectedKpiAdvisor(['All']);
      setPersonNameKpi(['All']);
      dispatch(setKpiReportAdvisor(['All']));
      setSelected(['All']);
      setPersonName(['All']);
      setTechPersonName(['All']);
      ReactSession.set('serviceAdvisors', ['All']);
      setAdvisorName(['All']);
      setNotifications([]);
      setTechName(['All']);
      setTechSelected(['All']);
      dispatch(setTechnician(['All']));
      dispatch(setSelectedTech('All[All]'));
      //dispatch(setMenuSelected('KPI Advisor Comparative'));
      // setSelected([event]);
      setSelectedSto([event]);
      localStorage.setItem('storeSelected', [event]);

      ReactSession.set('technicians', ['All']);
      let status = checkClosedDateInCurrentMonth(
        localStorage.getItem('closedDate'),
        localStorage.getItem('openDate')
      );
      dispatch(setToggleStatus(status));

      let groups = keycloak.tokenParsed.groupname;
      let groupArray = [];
      let groupIds = [];
      groups = keycloak.realm != 'lupient' ? groups.sort() : groups;
      groups.map(value => {
        groupArray.push(value.split('/')[1]);
        groupIds.push(value.split('/')[2]);
      });
      // setclientGroup(event.split('-')[0]);
      // localStorage.setItem('storeGroup', groups[0].split('-')[0]);

      setGroups(groups);
      setstoreSlected(event);
      let storeId = [];
      storeId.push(event);
      let groupIdss = [];
      // console.log('pppp===aaaa', event);
      // groupIdss.push(value.split('/')[2]);
      groupList.map(value => {
        if (value.includes(event)) {
          let storeVal =
            value.split('-').length > 3
              ? value.replace(/.*?-/, '')
              : value.split('-').length > 2
              ? value.split('-')[value.split('-').length - 2] +
                '-' +
                value.split('-')[value.split('-').length - 1]
              : value.split('-')[1];

          storeVal = storeVal.replace(/\/$/, '');

          const lastSlashIndex = storeVal.lastIndexOf('/');

          let store = storeVal.substring(0, lastSlashIndex); // "Fred Anderson Chevrolet/Cadillac"
          const selStoreId = storeVal.substring(lastSlashIndex + 1);

          if (store.trim() == event) {
            groupIdss.push(selStoreId);
          }
          // if(value.split('/')[1]==event){
          //   groupIdss.push(value.split('/')[2]);
          // }
        }
      });
      if (event == 'All Stores') {
        const extractedValues = groupList.map(str => str.split('/').pop());

        const distinctValues = [...new Set(extractedValues)];
        groupIdss = distinctValues;

        // groupIdss = distinctValues.join(',');
      }

      localStorage.setItem('selectedStoreId', JSON.stringify(groupIdss));
      ReactSession.set('selectedStoreId', JSON.stringify(groupIdss));

      dispatch(setStoreId(JSON.stringify(groupIdss)));
      if (value == 'login') {
        const spanAttribute = {
          pageUrl: '',
          origin: '',
          event: 'Store changed',
          value: event,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Store changed', spanAttribute);
      }
      if (event == 'login') {
        if (!keycloak.authenticated) {
          keycloak.login();
        } else {
        }
        // } else {
        if (!keycloak.authenticated) {
          keycloak.login();
        } else {
          // const spanAttribute = {
          //   pageUrl: '',
          //   origin: '',
          //   event: 'Store changed',
          //   value: event
          // };
          // traceSpan('Store changed', spanAttribute);

          let newDate = new Date();
          let date = newDate.getDate();
          Date().toLocaleString();

          let storeValue = JSON.parse(localStorage.getItem('selectedStoreId'));
          let store;
          var offset = getTimeZone();
          // if (storeValue) {
          //   let store = Object.values(storeValue);
          //   console.log(
          //     'stores===',
          //     storeSlected,
          //     storeName,
          //     store[0],
          //     localStorage.getItem('storeValue')
          //   );
          //   const client = makeApolloClientPostgres;
          //   client
          //     .mutate({
          //       mutation: INSERT_LOGIN_DETAILS,
          //       variables: {
          //         username: localStorage.getItem('storeValue'),
          //         storeid: store[0],
          //         logindate: moment().format('MM-DD-YYYY hh:mm:ss'),
          //         offset: offset
          //       }
          //     })
          //     .then(result => {
          //       console.log('data result=', result);
          //     });
          // }

          if (JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1) {
            localStorage.removeItem('popup');
            getLatestClosedDate(result => {
              if (result) {
                var openDate = '';
                var Date1 = result[0].value;

                localStorage.setItem('closedDate', Date1);
              }
            });
            getLatestOpenDate(result => {
              if (result) {
                var resultDate = '';
                var openDate = '';
                var Date1 = result[0].value;
                localStorage.setItem('openDate', Date1);
              }
            });
            getLastThirteenMonths(result => {
              const startMonth = result[0];
              const endMonth = result[12];
              const datestr = endMonth.split('-');
              const month = datestr[1];
              const year = datestr[0];
              const date = new Date(year, month, 0).getDate();

              let dateRange = [startMonth + '-01', endMonth + '-' + date];
              let dateRange12Months = [
                result[1] + '-01',
                endMonth + '-' + date
              ];
              localStorage.setItem('12Months', dateRange12Months);
              localStorage.setItem('13Months', dateRange);
            });
            setclientGroup(event.split('-')[0]);
            const selectedStoreId = JSON.parse(
              localStorage.getItem('selectedStoreId')
            );
            let stores = lodash.uniqBy(groupArray);
            let selectedStores = lodash.uniqBy(groupIds);

            let clientName = '';
            let clientDesc = '';
            let clientSite = '';
            let storeDMS = '';
            getStoreDetails(selectedStoreId, result => {
              if (
                result.data.statelessCcPhysicalRoGetStoreMasters.nodes.length >
                0
              ) {
                clientName =
                  result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
                    'storeName'
                  ] +
                  ' ' +
                  (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
                    ? ' , ' +
                      result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
                        .storeName
                    : '');
                clientSite =
                  result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0]
                    .website;
                storeDMS =
                  result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0].dms;
                setclientName(clientName);
                setDmsName(storeDMS);
                setClientWebsite(clientSite);
                localStorage.setItem('dms', storeDMS.toLowerCase());
                clientDesc =
                  result.data.statelessCcPhysicalRoGetStoreMasters.nodes[0][
                    'storeDescription'
                  ] +
                  ' ' +
                  (result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1]
                    ? ' , ' +
                      result.data.statelessCcPhysicalRoGetStoreMasters.nodes[1][
                        'storeDescription'
                      ]
                    : '');
                setclientDesc(clientDesc);
                localStorage.setItem('storeNames', clientName);
                localStorage.setItem(
                  'storeManufacturer',
                  result.data.dmsPhysicalRoVwStoreMasters.nodes[0][
                    'manufacturer'
                  ]
                );
                localStorage.setItem(
                  'storeData',
                  JSON.stringify(
                    result.data.statelessCcPhysicalRoGetStoreMasters.nodes
                  )
                );
              }
            });
            localStorage.setItem('storeChange', true);
            dispatch(setServiceAdvisor(['All']));
            let mounted = true;
            // getAllPayTypeErrors(callback => {
            //   if (callback.length > 0) {
            //     setAllPayTypeErrors(callback.length);
            //     if (mounted) {
            //       setNotifications([
            //         {
            //           id: 1,
            //           type: 'error',
            //           title: 'Unmapped Pay Type Codes Found'
            //         }
            //       ]);
            //       dispatch(setPayTypeError(true));
            //       dispatch(setPayTypeErrorCount(callback.length));
            //     }
            //   } else if (mounted) {
            //     setNotifications([]);
            //     dispatch(setPayTypeError(false));
            //     dispatch(setPayTypeErrorCount(0));
            //   }
            // });
            let opError = 0;
            let adError = 0;
            let techError = 0;
            getAllOpcodeErrors(callback => {
              const opcodeErrorArr = callback.filter(function(el) {
                return el.cType === 'opcode';
              });
              const advisorErrorArr = callback.filter(function(el) {
                return el.cType === 'advisor';
              });
              const technicianErrorArr = callback.filter(function(el) {
                return el.cType === 'technician';
              });
              const paytypeErrorArr = callback.filter(function(el) {
                return el.cType === 'paytype_errors';
              });
              const modelNewArr = callback.filter(function(el) {
                return el.cType === 'labor_grid_models';
              });

              var err = callback.reduce(
                (acc, o) => acc + parseInt(o.noncategorizedcount),
                0
              );
              var b = lodash.filter(callback, function(o) {
                if (o.noncategorizedcount > 0) return o;
              }).length;
              dispatch(setAllErrorsCount(b));
              if (opcodeErrorArr.length > 0) {
                opError = Number(opcodeErrorArr[0].noncategorizedcount);
                if (mounted) {
                  if (opcodeErrorArr[0].noncategorizedcount > 0) {
                    dispatch(setOpcodeError(true));
                    dispatch(
                      setOpcodeErrorCount(opcodeErrorArr[0].noncategorizedcount)
                    );
                  } else {
                    dispatch(setOpcodeError(false));
                    dispatch(setOpcodeErrorCount(0));
                  }
                }
              } else {
                setNotifications([]);
                dispatch(setOpcodeError(false));
                dispatch(setOpcodeErrorCount(0));
              }

              if (advisorErrorArr.length > 0) {
                adError = Number(advisorErrorArr[0].noncategorizedcount);
                if (mounted) {
                  if (advisorErrorArr[0].noncategorizedcount > 0) {
                    dispatch(setServiceAdvisorErrors(true));
                    dispatch(
                      setServiceAdvisorErrorsCount(
                        advisorErrorArr[0].noncategorizedcount
                      )
                    );
                  } else {
                    dispatch(setServiceAdvisorErrors(false));
                    dispatch(setServiceAdvisorErrorsCount(0));
                  }
                }
              } else {
                setNotifications([]);
                dispatch(setServiceAdvisorErrors(false));
                dispatch(setServiceAdvisorErrorsCount(0));
              }
              if (technicianErrorArr.length > 0) {
                techError = Number(technicianErrorArr[0].noncategorizedcount);

                if (mounted) {
                  if (technicianErrorArr[0].noncategorizedcount > 0) {
                    dispatch(setTechnicianErrors(true));
                    dispatch(
                      setTechnicianErrorsCount(
                        technicianErrorArr[0].noncategorizedcount
                      )
                    );
                  } else {
                    dispatch(setTechnicianErrors(false));
                    dispatch(setTechnicianErrorsCount(0));
                  }
                }
              } else {
                setNotifications([]);

                dispatch(setTechnicianErrors(false));
                dispatch(setTechnicianErrorsCount(0));
              }
              if (paytypeErrorArr.length > 0) {
                //paytypeError = Number(paytypeErrorArr[0].noncategorizedcount);

                if (mounted) {
                  if (paytypeErrorArr[0].noncategorizedcount > 0) {
                    dispatch(setPayTypeError(true));
                    dispatch(
                      setPayTypeErrorCount(
                        paytypeErrorArr[0].noncategorizedcount
                      )
                    );
                  } else {
                    dispatch(setPayTypeError(true));
                    dispatch(setPayTypeErrorCount(0));
                  }
                }
              } else {
                setNotifications([]);

                dispatch(setPayTypeError(true));
                dispatch(setPayTypeErrorCount(0));
              }
              // if (modelNewArr.length > 0) {
              //   //paytypeError = Number(paytypeErrorArr[0].noncategorizedcount);

              //   if (mounted) {
              //     if (modelNewArr[0].noncategorizedcount > 0) {
              //       dispatch(setNewModel(true));
              //       dispatch(
              //         setNewModelErrorCount(modelNewArr[0].noncategorizedcount)
              //       );
              //     } else {
              //       dispatch(setNewModel(true));
              //       dispatch(setNewModelErrorCount(0));
              //     }
              //   }
              // } else {
              //   setNotifications([]);

              //   dispatch(setNewModel(true));
              //   dispatch(setNewModelErrorCount(0));
              // }
            });
            getAllServiceAdvisors(result => {
              var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
                e => e.active == 1
              );
              if (
                result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes
              ) {
                const serviceDepartment = advArr.filter(
                  item => item.department === 'Service'
                );
                setNames(
                  ['All'].concat(
                    serviceDepartment.map(e =>
                      e ? e.serviceadvisor.toString() : ''
                    )
                  )
                );
                setadvisorNames(
                  ['All'].concat(
                    serviceDepartment.map(e =>
                      e ? e.name + '-status-' + e.active : ''
                    )
                  )
                );
              }
            });
            getAllTechnicians(result => {
              if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
                // setTechnician(
                //   result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(
                //     e => `${e.name} [${e.lbrtechno.toString()}]`
                //   )
                // );
                var techArr = result.data.statelessCcPhysicalRwGetTechnicians.nodes.filter(
                  e => e.active == 1
                );
                const techDepartment = techArr.filter(
                  item => item.department === 'Service'
                );
                // setTechnician(
                //   result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(
                //     e => `${e.name} [${e.lbrtechno.toString()}]`
                //   )
                // );
                setTechNames(
                  ['All'].concat(
                    techDepartment.map(e => (e ? e.lbrtechno.toString() : ''))
                  )
                );
                // Sort the array by `nickname` in ascending order (case-insensitive)
                const sortedTechnicians = [...techDepartment].sort((a, b) => {
                  const nicknameA = a.nickname ? a.nickname.toLowerCase() : '';
                  const nicknameB = b.nickname ? b.nickname.toLowerCase() : '';

                  // Sort null/empty nicknames last
                  if (nicknameA === '' && nicknameB === '') return 0;
                  if (nicknameA === '') return 1;
                  if (nicknameB === '') return -1;

                  // Case-insensitive comparison
                  return nicknameA.localeCompare(nicknameB);
                });
                const dataValue = sortedTechnicians.map(e =>
                  e
                    ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active
                    : ''
                );
                const sortedData = [...dataValue].sort((a, b) =>
                  a.toLowerCase().localeCompare(b.toLowerCase())
                );
                setTechnicianSelection(['All'].concat(sortedData));
                // setTechnicianSelection(
                //   ['All'].concat(
                //     techArr.map(e =>
                //       e
                //         ? (e.nickname ? e.nickname : e.name) +
                //           '-status-' +
                //           e.active
                //         : ''
                //     )
                //   )
                // );
                // setTechNames(
                //   ['All'].concat(
                //     result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(e =>
                //       e ? e.lbrtechno.toString() : ''
                //     )
                //   )
                // );

                // setTechnicianSelection(
                //   ['All'].concat(
                //     result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(e =>
                //       e
                //         ? (e.nickname ? e.nickname : e.name) +
                //           '-status-' +
                //           e.active
                //         : ''
                //     )
                //   )
                // );
                //  settechnicianSelected(result.data.dms_physical_rw_vw_tbl_technicians[0].name+
                //   ' [' +result.data.dms_physical_rw_vw_tbl_technicians[0].lbrtechno.toString() + ']')
              }
            });

            dispatch(setUsername(keycloak.tokenParsed.preferred_username));
            if (session.userName) {
              getAllFavouriteCharts(session.userName, callback => {
                if (callback) {
                  dispatch(
                    setFavouriteCharts(callback.map(data => data.favourites))
                  );
                }
              });
            }
          } else {
            history.push('/CPOverview');
          }
        }
        // }
      }
      setOpenStore(false);
    }
  };

  return (
    <AppBar
      {...rest}
      className={clsx(classes.root, classes.toolbarWrapper, className)}
      color="primary"
      id="app-bar"
    >
      <Toolbar className={classes.toolbar} id="toolbar">
        <Grid className={classes.flexShowBlock}>
          <Hidden lgUp>
            <IconButton
              className={classes.menuButton}
              color="inherit"
              onClick={onOpenNavBarMobile}
              id="menu-button"
            >
              <MenuIcon />
            </IconButton>
          </Hidden>

          <div className={classes.logo}>
            {Dealer === 'Armatus' ? (
              <img
                alt="Fixed Ops Logo"
                src="/images/logos/logo_armatus.png"
                className={classes.logoImg}
                id="logo-armatus"
              />
            ) : (
              <img
                alt="Fixed Ops Logo"
                id="logo-netspective"
                src="/images/logos/logo_netspective.png"
              />
            )}
          </div>
          {/* {clientGroup != '' ? (
            <div
              className={
                keycloak.realmAccess.roles.includes('client') === true
                  ? classes.storeInfoClient
                  : classes.storeInfo
              }
            >
              <Hidden smDown>
                <Typography variant="subtitle1" className={classes.subtitle1}>
                  {clientGroup}
                </Typography>
                {JSON.parse(localStorage.getItem('selectedStoreId')).length <=
                1 ? (
                  <>
                    <Typography
                      variant="subtitle2"
                      className={classes.subtitle2}
                    >
                      {clientName}
                    </Typography>
                    <Typography
                      variant="subtitle3"
                      className={classes.subtitle3}
                    >
                      {clientDesc}
                    </Typography>
                  </>
                ) : (
                  <Typography className={classes.subtitle2}>
                    <span className={classes.subtitle4}>
                      {clientName.split(' , ')[0] +
                        ' - ' +
                        clientDesc.split(' , ')[0]}
                    </span>
                    <br />
                    <span className={classes.subtitle4}>
                      {clientName.split(' , ')[1]
                        ? clientName.split(' , ')[1] +
                          ' - ' +
                          clientDesc.split(' , ')[1]
                        : ''}
                    </span>
                  </Typography>
                )}
              </Hidden>
            </div>
          ) : (
            ''
          )} */}
          <div
            style={{
              display:
                keycloak.authenticated === true &&
                keycloak.tokenParsed.groupname.length <= 1 &&
                (keycloak.realmAccess.roles.includes('admin') === true ||
                  keycloak.realmAccess.roles.includes('superadmin') === true ||
                  keycloak.realmAccess.roles.includes('user') === true)
                  ? 'block'
                  : 'grid',
              marginTop:
                keycloak.authenticated === true &&
                keycloak.tokenParsed.groupname.length <= 1 &&
                (keycloak.realmAccess.roles.includes('admin') === true ||
                  keycloak.realmAccess.roles.includes('superadmin') === true ||
                  keycloak.realmAccess.roles.includes('user') === true)
                  ? '5px'
                  : '0px'
            }}
            id="store-menu-container"
          >
            <Menu
              id="store-dropdown-menu"
              // anchorEl={anchorEl}
              // keepMounted onClose={handleClose}
              className={classes.storeDropdown}
              open={openStore}
              // onBlur={handleChanges}
            >
              {storeName.map(option => (
                <MenuItem
                  id={`store-menu-item-${option}`}
                  // className={classes.storeDropdown}
                  key={option}
                  onClick={handleChanges}
                  open={openStore}
                  style={{ top: '0px' }}
                >
                  {option}
                </MenuItem>
              ))}
            </Menu>
            {JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1 &&
            (keycloak.realmAccess.roles.includes('admin') === true ||
              keycloak.realmAccess.roles.includes('superadmin') === true ||
              keycloak.realmAccess.roles.includes('user') === true) ? (
              <Tooltip title="Notifications">
                <Button
                  id="notifications-button"
                  size="medium"
                  aria-label="show 11 new notifications"
                  color="inherit"
                  className={
                    localStorage.getItem('realm') == 'ferrarioat_store' ||
                    localStorage.getItem('realm') == 'lupient'
                      ? classes.notificationsButtonFerr
                      : classes.notificationsButton
                  }
                  onClick={handleNotificationsOpenError}
                  ref={notificationsRef}
                  //disabled={session.payTypeErrors == true ? false : true}
                >
                  <Badge
                    id="notifications-badge"
                    // badgeContent={session.payTypeErrors == true ? allPayTypeErrors:null}
                    max={1000}
                    badgeContent={Number(session.allErrorsCount)}
                    //variant="dot"

                    classes={{
                      badge: clsx(
                        classes.notificationsBadge,
                        'notification-icon'
                      )
                    }}
                    style={{ fontSize: 12 }}
                  >
                    <NotificationsIcon />
                  </Badge>
                </Button>
              </Tooltip>
            ) : null}
            {/* {keycloak.authenticated === true &&
              keycloak.tokenParsed.groupname.length > 1 && (
                <Button
                  color="inherit"
                  className={
                    localStorage.getItem('realm') == 'ferrarioat_store' ||
                    localStorage.getItem('realm') == 'lupient'
                      ? classes.notificationsButtonFerr
                      : classes.notificationsButton
                  }
                  onClick={handleStoreChange}
                >
                  <Tooltip title="Change Store">
                    <SwapHorizontalCircleOutlinedIcon />
                  </Tooltip>
                </Button>
              )}  */}
            {storeName.length > 1 ? (
              <FormControl
                id="store-dropdown-form"
                // className={classes.formControl}
                // disabled={isFilterDisabled}
                disabled={
                  (history.location.pathname === '/CPOverview' ||
                    history.location.pathname === '/CPLaborOverview' ||
                    history.location.pathname === '/CPPartsOverview' ||
                    history.location.pathname === '/SpecialMetrics' ||
                    history.location.pathname === '/MyFavorites' ||
                    history.location.pathname == '/LaborItemization' ||
                    history.location.pathname == '/PartsItemization' ||
                    history.location.pathname == '/Home' ||
                    history.location.pathname == '/2.4.0/Home' ||
                    history.location.pathname == '/ServiceAdvisorPerformance' ||
                    history.location.pathname == '/JobCountGrid' ||
                    history.location.pathname == '/TrendReport' ||
                    isAdvisorenabledCharts(session.currentPath) === true) &&
                  JSON.parse(localStorage.getItem('selectedStoreId')).length <=
                    1
                    ? false
                    : true
                }
              >
                <Dropdown
                  id="store-select-dropdown"
                  className={classes.select}
                  inputProps={{
                    classes: {
                      icon: classes.icon
                    }
                  }}
                  advisorNames={advisorNames}
                  options={names}
                  value={personName}
                  onChange={handleChangeStore}
                  selected={selectedSto}
                  keycloak={keycloak}
                  storeName={storeName}
                />
              </FormControl>
            ) : null}
          </div>
          {clientGroup != '' ? (
            <div
              id="client-group"
              className={
                keycloak.realmAccess.roles.includes('client') === true
                  ? classes.storeInfoClient
                  : classes.storeInfo
              }
            >
              <Hidden smDown>
                <Typography
                  id="client-group-name"
                  variant="subtitle1"
                  className={`${classes.subtitle1} ${classes.clientGroupNameStyle}`}
                >
                  {clientGroup && clientGroup} &nbsp;
                  {/* {(() => {
                    const dmsImg = localStorage.getItem('dmsImg');
                    return dmsImg &&
                      dmsImg !== 'null' &&
                      dmsImg.trim() !== '' ? (
                      <img
                        src={dmsImg}
                        alt="dmsImage"
                        style={{
                          maxWidth:
                            localStorage.getItem('dms') == 'tek' ? '20%' : '5%',
                          height: '5%',
                          verticalAlign: 'middle',
                          marginTop: '4px',
                          borderRadius:
                            localStorage.getItem('dms') == 'tek' ? '0%' : '50%',
                          border: '1px solid #ccc'
                        }}
                      />
                    ) : null;
                  })()} */}
                </Typography>

                {JSON.parse(localStorage.getItem('selectedStoreId')).length <=
                1 ? (
                  <div id="single-store" className={classes.flexGrid}>
                    <div className={classes.flexShowGrid}>
                      <Typography
                        id="client-name"
                        variant="subtitle2"
                        className={`${classes.subtitle2} ${classes.clientNameStyle}`}
                      >
                        {clientName && clientName}
                      </Typography>
                      <Button
                        id="client-website-button"
                        color="inherit"
                        className={classes.gotoButton}
                        onClick={() =>
                          clientWebsite != ''
                            ? window.open(clientWebsite, '_blank')
                            : window.open('/', '_blank')
                        }
                      >
                        <Tooltip title="Visit Website">
                          <LanguageOutlinedIcon
                            classes={{ root: classes.exploreIcon }}
                          />
                        </Tooltip>
                      </Button>
                      {dmsName && (
                        <Typography
                          id="dms-name"
                          variant="subtitle2"
                          className={classes.dmsName}
                        >
                          {dmsName}
                        </Typography>
                      )}
                    </div>
                    <Typography
                      id="client-description"
                      variant="subtitle3"
                      className={classes.subtitle3}
                    >
                      {clientDesc && clientDesc}
                    </Typography>
                  </div>
                ) : (
                  <Typography
                    id="multiple-stores"
                    className={classes.subtitle2}
                  >
                    {/* <span className={classes.subtitle4}>
                      {clientName.split(' , ')[0] +
                        ' - ' +
                        clientDesc.split(' , ')[0]}
                    </span> */}
                    <span className={classes.subtitle4}>
                      {localStorage.getItem('storeSelected') === 'All Stores'
                        ? clientName
                        : clientName.split(' , ')[0] +
                          ' - ' +
                          clientDesc.split(' , ')[0]}
                    </span>

                    <br />

                    <span className={classes.subtitle4}>
                      {clientName.split(' , ')[1]
                        ? clientName.split(' , ')[1] +
                          ' - ' +
                          clientDesc.split(' , ')[1]
                        : ''}
                    </span>
                  </Typography>
                  // <Typography className={classes.subtitle2}>
                  //   <span className={classes.subtitle4}>
                  //     {clientName.split(' , ')[0]}
                  //   </span>
                  //   <br />

                  //   <span className={classes.subtitle4}>
                  //     {clientName.split(' , ')[1]
                  //       ? clientName.split(' , ')[1]
                  //       : ''}
                  //   </span>
                  // </Typography>
                )}
              </Hidden>
            </div>
          ) : (
            ''
          )}

          {/* {keycloak.authenticated === true &&
            keycloak.tokenParsed.groupname.length > 1 && (
              <Button
                color="inherit"
                className={
                  localStorage.getItem('realm') == 'ferrarioat_store' ||
                  localStorage.getItem('realm') == 'lupient'
                    ? classes.notificationsButtonFerr
                    : classes.notificationsButton
                }
                onClick={handleStoreChange}
              >
                <Tooltip title="Change Store">
                  <SwapHorizontalCircleOutlinedIcon />
                </Tooltip>
              </Button>
            )} */}
        </Grid>
        <div
          id="topbarTitle"
          className={
            localStorage.getItem('realm') == 'ferrarioat_store' ||
            localStorage.getItem('realm') == 'lupient'
              ? classes.topbarTitleFerr
              : classes.topbarTitle
          }
        >
          <Hidden smDown>
            <Typography
              variant="h5"
              className={classes.titleContent}
              id="performanceCenterTitle"
            >
              Fixed Ops{' '}
              {/* {personName[0] != 'All' && isApplied === true ? <br></br> : ''} */}
              Performance Center
            </Typography>
          </Hidden>
        </div>
        <Grid className={classes.toolsGrid}>
          <Hidden smDown>
            <div className={classes.flexShow}>
              {/* {JSON.parse(localStorage.getItem('selectedStoreId')).length <=
                1 && ( */}
              <div className={classes.search}>
                <InputBase
                  id="search-ro-number"
                  placeholder="Search by RO#"
                  onKeyPress={handleROChangeEnter}
                  onChange={handleROChange}
                  value={roNumber}
                  classes={{
                    root: classes.inputRoot,
                    input: classes.inputInput
                  }}
                  inputProps={{ 'aria-label': 'search' }}
                />
                <Button
                  id="search-ro-button"
                  color="primary"
                  //onFocus={onSearchFocus}
                  className={
                    JSON.parse(localStorage.getItem('selectedStoreId'))
                      .length <= 1
                      ? classes.inputIcon
                      : classes.inputIcons
                  }
                  onClick={handleSearchByRo}
                >
                  <Tooltip title="Search by RO#">
                    <SearchIcon />
                  </Tooltip>
                </Button>
              </div>
              {/* )} */}
              {technician.length ? (
                <div
                  id="techSelection"
                  style={{
                    display:
                      (history.location.pathname == '/Home' ||
                        history.location.pathname == '/2.4.0/Home' ||
                        history.location.pathname == '/LaborMisses' ||
                        history.location.pathname == '/MPIStats' ||
                        history.location.pathname ==
                          '/KPIReportTechComparative' ||
                        history.location.pathname == '/KpiGraphics' ||
                        (history.location.pathname == '/AnalyzeData' &&
                          history.location.search == '?OneLine') ||
                        history.location.pathname == '/PartsMisses') &&
                      JSON.parse(localStorage.getItem('selectedStoreId'))
                        .length <= 1
                        ? 'block'
                        : 'none'
                  }}
                >
                  <FormControl
                    className={classes.techFormControl}
                    id="tech-form-control"
                  >
                    <MultiSelect
                      id="tech-multi-select"
                      label="Technicians"
                      className={classes.select}
                      inputProps={{
                        classes: {
                          icon: classes.icon
                        }
                      }}
                      advisorNames={technician}
                      options={techNames}
                      // value={techName}
                      value={techPersonName}
                      onChange={handleChangeTech}
                      selected={techSelected}
                      keycloak={keycloak}
                    />

                    {techSelected.length > 0 &&
                      //isApplied === true &&
                      techName[0] != 'All' && (
                        <div
                          style={{
                            display: 'inline-flex',
                            justifyContent: 'center'
                          }}
                        >
                          {techSelected
                            .filter(function(obj) {
                              return (
                                obj != 'Total Shop' && obj != 'Total Selected'
                              );
                            })
                            .map(data => (
                              <Tooltip
                                title={
                                  techNames.indexOf(data) > -1 === true
                                    ? technician[techNames.indexOf(data)].split(
                                        '-status-'
                                      )[0]
                                    : data
                                }
                              >
                                <span className={classes.dot}></span>
                              </Tooltip>
                            ))}
                        </div>
                      )}
                  </FormControl>
                </div>
              ) : null}
              {names.length ? (
                <div
                  id="advisorSelection"
                  style={{
                    display:
                      (history.location.pathname === '/CPOverview' ||
                        history.location.pathname === '/ROCalendar' ||
                        history.location.pathname === '/CPLaborOverview' ||
                        history.location.pathname === '/CPPartsOverview' ||
                        history.location.pathname === '/SpecialMetrics' ||
                        // history.location.pathname === '/MyFavorites' ||
                        //history.location.pathname === '/RevenueSummary' ||
                        history.location.pathname ===
                          '/RevenueSummaryDrilldown' ||
                        history.location.pathname == '/LaborItemization' ||
                        history.location.pathname == '/PartsItemization' ||
                        history.location.pathname == '/Home' ||
                        history.location.pathname == '/KPIReportComparative' ||
                        history.location.pathname == '/2.4.0/Home' ||
                        history.location.pathname == '/KpiGraphics' ||
                        history.location.pathname ==
                          '/ServiceAdvisorPerformance' ||
                        history.location.pathname == '/MPIStats' ||
                        history.location.pathname == '/LaborMisses' ||
                        history.location.pathname == '/PartsMisses' ||
                        history.location.pathname == '/KpiReport' ||
                        history.location.pathname ==
                          '/LaborGrossAndVolumeOpportunity' ||
                        history.location.pathname ==
                          '/PartsGrossAndVolumeOpportunity' ||
                        (history.location.pathname == '/AnalyzeData' &&
                          history.location.search == '?OneLine') ||
                        history.location.pathname == '/CPELROpportunity' ||
                        history.location.pathname == '/JobCountGrid' ||
                        history.location.pathname == '/TrendReport' ||
                        (history.location.pathname == '/LaborWorkMixAnalysis' &&
                          localStorage.getItem('laborTabSelection') &&
                          localStorage.getItem('laborTabSelection') == 'one') ||
                        history.location.prevPath ==
                          '/GraphDetailsView?chartId=1090' ||
                        history.location.prevPath ==
                          '/GraphDetailsView?chartId=1096' ||
                        //  history.location.pathname == '/PartsWorkMixAnalysis' ||

                        (history.location.pathname == '/GraphDetailsView' &&
                          (history.location.search == '?chartId=1090' ||
                            history.location.search == '?chartId=1096' ||
                            history.location.search == '?chartId=1253' ||
                            history.location.search == '?chartId=1254' ||
                            history.location.search == '?chartId=1255' ||
                            history.location.search == '?chartId=1256' ||
                            history.location.search == '?chartId=1257' ||
                            history.location.search == '?chartId=1258' ||
                            history.location.search == '?chartId=1243' ||
                            history.location.search == '?chartId=1244' ||
                            history.location.search == '?chartId=1245' ||
                            history.location.search == '?chartId=1246' ||
                            history.location.search == '?chartId=1247' ||
                            history.location.search == '?chartId=1248')) ||
                        //     ||
                        // (history.location.pathname == '/GraphDetailsView' &&
                        //   Number(
                        //     history.location.search.split('?chartId=').pop()
                        //   ) >= 1243 &&
                        //   Number(
                        //     history.location.search.split('?chartId=').pop()
                        //   ) <= 1248) ||
                        // (Number(
                        //   history.location.search.split('?chartId=').pop()
                        // ) >= 1253 &&
                        //   Number(
                        //     history.location.search.split('?chartId=').pop()
                        //   ) <= 1258) ||

                        (isAdvisorenabledCharts(session.currentPath) === true &&
                          ((history.location.prevPath !=
                            '/TechnicianPerformance' &&
                            history.location.state &&
                            history.location.state.chartId &&
                            history.location.state.chartId != 1358) ||
                            (history.location.state &&
                              history.location.state.chartId &&
                              history.location.state.chartId != 1358)))) &&
                      JSON.parse(localStorage.getItem('selectedStoreId'))
                        .length <= 1
                        ? 'block'
                        : 'none'
                  }}
                >
                  <FormControl
                    id="advisor-form-control"
                    className={classes.formControl}
                    // disabled={isFilterDisabled}

                    // disabled={
                    //   (history.location.pathname === '/CPOverview' ||
                    //     history.location.pathname === '/CPLaborOverview' ||
                    //     history.location.pathname === '/CPPartsOverview' ||
                    //     history.location.pathname === '/SpecialMetrics' ||
                    //     // history.location.pathname === '/MyFavorites' ||
                    //     history.location.pathname == '/LaborItemization' ||
                    //     history.location.pathname == '/PartsItemization' ||
                    //     history.location.pathname == '/Home' ||
                    //     history.location.pathname ==
                    //       '/ServiceAdvisorPerformance' ||
                    //     history.location.pathname == '/LaborMisses' ||
                    //     history.location.pathname == '/PartsMisses' ||
                    //     history.location.pathname == '/KpiReport' ||
                    //     isAdvisorenabledCharts(session.currentPath) === true) &&
                    //   JSON.parse(localStorage.getItem('selectedStoreId'))
                    //     .length <= 1
                    //     ? false
                    //     : true
                    // }
                  >
                    <MultiSelect
                      id="advisor-multi-select"
                      label="Service Advisors"
                      className={classes.select}
                      inputProps={{
                        classes: {
                          icon: classes.icon
                        }
                      }}
                      // advisorNames={history.location.pathname == '/KpiReport' ? advisorNames.filter(x => x != 'All') : advisorNames}
                      // options={history.location.pathname == '/KpiReport' ? names.filter(x => x != 'All') : names}
                      advisorNames={advisorNames}
                      options={names}
                      value={
                        history.location.pathname == '/KpiReport' ||
                        history.location.pathname ==
                          '/LaborGrossAndVolumeOpportunity' ||
                        history.location.pathname ==
                          '/PartsGrossAndVolumeOpportunity' ||
                        history.location.pathname == '/CPELROpportunity' ||
                        (history.location.pathname == '/GraphDetailsView' &&
                          (history.location.search == '?chartId=931' ||
                            history.location.search == '?chartId=921' ||
                            history.location.search == '?chartId=926'))
                          ? //  ||
                            // (history.location.pathname == '/GraphDetailsView' &&
                            //   (history.location.search == '?chartId=931' ||
                            //     history.location.search == '?chartId=921' ||
                            //     history.location.search == '?chartId=926' ||
                            //     (Number(
                            //       history.location.search.split('?chartId=').pop()
                            //     ) >= 1243 &&
                            //       Number(
                            //         history.location.search.split('?chartId=').pop()
                            //       ) <= 1248) ||
                            //     (Number(
                            //       history.location.search.split('?chartId=').pop()
                            //     ) >= 1253 &&
                            //       Number(
                            //         history.location.search.split('?chartId=').pop()
                            //       ) <= 1258)))
                            personNameKpi
                          : personName
                      }
                      onChange={handleChange}
                      onControlSelect={handleControlSelect}
                      selected={
                        history.location.pathname == '/KpiReport' ||
                        history.location.pathname ==
                          '/LaborGrossAndVolumeOpportunity' ||
                        history.location.pathname ==
                          '/PartsGrossAndVolumeOpportunity' ||
                        history.location.pathname == '/CPELROpportunity' ||
                        (history.location.pathname == '/GraphDetailsView' &&
                          (history.location.search == '?chartId=931' ||
                            history.location.search == '?chartId=921' ||
                            history.location.search == '?chartId=926'))
                          ? // ||
                            // (history.location.pathname == '/GraphDetailsView' &&
                            //   (history.location.search == '?chartId=931' ||
                            //     history.location.search == '?chartId=921' ||
                            //     history.location.search == '?chartId=926' ||
                            //     (Number(
                            //       history.location.search.split('?chartId=').pop()
                            //     ) >= 1243 &&
                            //       Number(
                            //         history.location.search.split('?chartId=').pop()
                            //       ) <= 1248) ||
                            //     (Number(
                            //       history.location.search.split('?chartId=').pop()
                            //     ) >= 1253 &&
                            //       Number(
                            //         history.location.search.split('?chartId=').pop()
                            //       ) <= 1258)))
                            selectedKpiAdvisor
                          : selected
                      }
                      keycloak={keycloak}
                    />

                    {history.location.pathname == '/KpiReport' ||
                    history.location.pathname ==
                      '/LaborGrossAndVolumeOpportunity' ||
                    history.location.pathname ==
                      '/PartsGrossAndVolumeOpportunity' ||
                    history.location.pathname == '/CPELROpportunity' ||
                    (history.location.pathname == '/GraphDetailsView' &&
                      (history.location.search == '?chartId=931' ||
                        history.location.search == '?chartId=921' ||
                        history.location.search == '?chartId=926'))
                      ? //  ||
                        // (history.location.pathname == '/GraphDetailsView' &&
                        //   (history.location.search == '?chartId=931' ||
                        //     history.location.search == '?chartId=921' ||
                        //     history.location.search == '?chartId=926' ||
                        //     (Number(
                        //       history.location.search.split('?chartId=').pop()
                        //     ) >= 1243 &&
                        //       Number(
                        //         history.location.search.split('?chartId=').pop()
                        //       ) <= 1248) ||
                        //     (Number(
                        //       history.location.search.split('?chartId=').pop()
                        //     ) >= 1253 &&
                        //       Number(
                        //         history.location.search.split('?chartId=').pop()
                        //       ) <= 1258)))
                        selectedKpiAdvisor.length > 0 &&
                        //isApplied === true &&
                        selectedKpiAdvisor[0] != 'All' && (
                          <div
                            style={{
                              display: 'inline-flex',
                              justifyContent: 'center'
                            }}
                          >
                            {selectedKpiAdvisor
                              .filter(function(obj) {
                                return (
                                  obj != 'Total Shop' && obj != 'Total Selected'
                                );
                              })
                              .map(data => (
                                <Tooltip
                                  title={
                                    names.indexOf(data) > -1 === true
                                      ? advisorNames[names.indexOf(data)].split(
                                          '-status-'
                                        )[0]
                                      : data
                                  }
                                >
                                  <span className={classes.dot}></span>
                                </Tooltip>
                              ))}
                          </div>
                        )
                      : selected.length > 0 &&
                        //isApplied === true &&
                        advisorName[0] != 'All' && (
                          <div
                            style={{
                              display: 'inline-flex',
                              justifyContent: 'center'
                            }}
                          >
                            {selected
                              .filter(function(obj) {
                                return (
                                  obj != 'Total Shop' && obj != 'Total Selected'
                                );
                              })
                              .map(data => (
                                <Tooltip
                                  title={
                                    names.indexOf(data) > -1 === true
                                      ? advisorNames[names.indexOf(data)].split(
                                          '-status-'
                                        )[0]
                                      : data
                                  }
                                >
                                  <span className={classes.dot}></span>
                                </Tooltip>
                              ))}
                          </div>
                        )}
                  </FormControl>
                </div>
              ) : null}
            </div>

            <div style={{ display: 'grid' }}>
              <HtmlTooltip
                // open={true}
                interactive={true}
                id="contact-popper"
                classes={{
                  popper: classes.popper
                }}
                title={
                  <>
                    <Typography variant="h5" style={{ textAlign: 'center' }}>
                      Contact Us{' '}
                    </Typography>
                    <MenuList style={{ padding: '2px' }}>
                      <MenuItem
                        id="phone-menu-item"
                        style={{ pointerEvents: 'none' }}
                      >
                        <ListItemIcon>
                          <PhoneIcon fontSize="small" />
                        </ListItemIcon>

                        <ListItemText id="phone-number">
                          +****************
                        </ListItemText>
                      </MenuItem>
                      <MenuItem id="email-menu-item">
                        <ListItemIcon>
                          <EmailIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText
                          primary="<EMAIL>"
                          onClick={() =>
                            (window.location = 'mailto:<EMAIL>')
                          }
                          id="email-contact-authenticated"
                        >
                          <Link
                            to="#"
                            onClick={() =>
                              (window.location = 'mailto:<EMAIL>')
                            }
                          >
                            <EMAIL>
                          </Link>
                        </ListItemText>
                      </MenuItem>
                    </MenuList>
                  </>
                }
                //style={{ display: searchText ? 'none' : 'inline-block' }}
              >
                <Button
                  size="medium"
                  //aria-label="show 11 new notifications"
                  color="inherit"
                  className={
                    localStorage.getItem('realm') == 'ferrarioat_store' ||
                    localStorage.getItem('realm') == 'lupient'
                      ? classes.notificationsButtonFerr
                      : classes.notificationsButton
                  }
                  id="contact-button-authenticated"
                >
                  <img
                    src="/images/call-email-icon.png"
                    id="contact-icon"
                    style={{ height: 22, width: 23 }}
                  />
                </Button>
              </HtmlTooltip>
              <Tooltip title="Support &amp; Feedback">
                <Button
                  id="support-feedback-button"
                  size="medium"
                  //aria-label="show 11 new notifications"
                  color="inherit"
                  className={
                    localStorage.getItem('realm') == 'ferrarioat_store' ||
                    localStorage.getItem('realm') == 'lupient'
                      ? classes.notificationsButtonFerr
                      : classes.notificationsButton
                  }
                  onClick={() => trigger()}
                >
                  <ContactSupportIcon id="support-icon" />
                </Button>
                {/* </IconButton> */}
              </Tooltip>
            </div>
            <Help
              open={helpStatus}
              page="other"
              canvased={canvasVal}
              onClose={onCloseHelp}
              keycloak={keycloak}
            />
            <Button
              id="account-button"
              aria-owns={open ? 'mouse-over-popover' : undefined}
              aria-haspopup="true"
              className={
                localStorage.getItem('realm') == 'ferrarioat_store'
                  ? //|| localStorage.getItem('realm') == 'lupient'
                    classes.notificationsButtonFerrIcon
                  : classes.notificationsButtonIcon
              }
              onClick={handleAccountClick}
              color="inherit"
              style={{
                marginTop: 20,
                width: '120px'
              }}
            >
              <Grid
                id="account-grid"
                style={{ display: 'grid', textTransform: 'none' }}
              >
                <span
                  id="account-greeting"
                  style={{ fontSize: 12, marginRight: 2 }}
                >
                  Hi, {keycloak.tokenParsed.given_name}
                </span>
              </Grid>
              <AccountCircle id="account-icon" />
            </Button>

            {/* <Tooltip title="Support &amp; Feedback">
              <IconButton
                className={classes.chatButton}
                color="inherit"
                style={{ padding: 0, color: 'rgb(0, 61, 107)' }}
              >
                <ContactSupportIcon />
              </IconButton>
            </Tooltip>
            <FeedBack
              open={feedBackStatus}
              page="other"
              canvased={canvasVal}
              onClose={onClose}
            /> */}

            <Popover
              id="mouse-over-popover"
              open={open}
              anchorEl={anchorEl}
              onClose={handleClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'center'
              }}
              className={classes.signoutPopper}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'center'
              }}
            >
              <List
                component="nav"
                classes={{
                  root: classes.popover
                }}
                aria-labelledby="nested-list-subheader"
                subheader={
                  <ListSubheader
                    component="div"
                    id="nested-list-subheader"
                    className={classes.ListSubheader}
                  >
                    <span id="user-email">
                      {' '}
                      User : {keycloak.tokenParsed.email}
                    </span>
                    <span id="user-role">Role : {' ' + userType} </span>
                  </ListSubheader>
                }
                className={classes.root}
              >
                <Divider id="list-divider" />
                <ListItem
                  className={classes.logoutButton}
                  onClick={handleLogout}
                  color="inherit"
                  button
                  id="sign-out-button"
                >
                  <ListItemText id="sign-out-text"> Sign Out </ListItemText>
                  <ListItemIcon id="sign-out-icon">
                    <InputIcon className={classes.logoutIcon} />
                  </ListItemIcon>
                </ListItem>
              </List>
            </Popover>
            {/* <Button
            className={classes.logoutButton}
            color="inherit"
            onClick={handleLogout}
          >
            Sign Out
            <InputIcon className={classes.logoutIcon} />
          </Button> */}
            <Dialog
              //fullWidth
              //maxWidth="sm"
              id="sign-out-dialog"
              aria-labelledby="confirmation-dialog-title"
              open={openSignOutDialog}
            >
              <DialogTitle id="confirmation-dialog-title">
                <Typography
                  id="dialog-title"
                  variant="h5"
                  color="primary"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Sign Out
                </Typography>
              </DialogTitle>
              <DialogContent dividers>
                <Typography
                  id="dialog-content"
                  variant="h6"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Are you sure you want to sign out?
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button
                  id="cancel-signout-button"
                  autoFocus
                  onClick={handleCancel}
                >
                  Cancel
                </Button>
                <Button
                  id="confirm-signout-button"
                  onClick={handleOk}
                  color="primary"
                >
                  Ok
                </Button>
              </DialogActions>
            </Dialog>
            <SuccessSnackbar
              onClose={handleSnackbarClose}
              open={openSnackbar}
            />
            <SuccessSnackbarHelp
              onClose={handleHelpSnackbarClose}
              open={openSnackbarHelp}
              error={error}
            />
            {/* <FeedBackForm page="other" /> */}
          </Hidden>
          {/* {keycloak.realmAccess.roles.includes('admin') === true ? (
          <>
            {session.refreshErrorStatus === true ? (
              <IconButton
                className={classes.notificationsButton}
                color="inherit"
                onClick={handleNotificationsOpenError}
                ref={notificationsRef}
              >
                <Badge
                  badgeContent={notifications.length}
                  classes={{ badge: classes.notificationsBadge }}
                  variant="dot"
                >
                  <NotificationsIcon />
                </Badge>
              </IconButton>
            ) : null}
          </>
        ) : null} */}
          {/* {keycloak.realmAccess.roles.includes('admin') === true ||
          keycloak.realmAccess.roles.includes('user') === true ? (
            <>
              {session.refreshStatus === true ? (
                <HtmlTooltipWithBackground
                  open={true}
                  classes={{
                    popper: classes.arrowPopper
                  }}
                  title={
                    <React.Fragment>
                      <div style={{ display: 'grid' }}>
                        <span style={{ fontSize: '17px' }}>
                          Reprocessing Data History.{' '}
                        </span>

                        <span>
                          {' '}
                          Please make no additional changes until complete.
                        </span>
                      </div>
                    </React.Fragment>
                  }
                >
                  <CircularProgress color="white" size={25} />
                </HtmlTooltipWithBackground>
              ) : null}
            </>
          ) : null} */}
          {/* <ReactModal
            initWidth={400}
            initHeight={200}
            onFocus={() => console.log('Modal is clicked')}
            className={'my-modal-custom-class'}
            //onRequestClose={this.closeModal}
            isOpen={session.refreshStatus != true ? true : false}
          >
            <React.Fragment>
              <div style={{ display: 'grid' }}>
                <span style={{ fontSize: '17px' }}>
                  Reprocessing Data History.{' '}
                </span>

                <span> Please make no additional changes until complete.</span>
              </div>
            </React.Fragment>
          </ReactModal> */}
          {/* {keycloak.realmAccess.roles.includes('admin') === true ||
          keycloak.realmAccess.roles.includes('user') === true ? (
            <>
              {session.reloadStatus != true ? (
                
                <HtmlTooltipWithBackground
                  open={true}
                  title={
                    <React.Fragment>
                     
                      <span>Please click this refresh </span>
                      <ReplayIcon
                        style={{ fontSize: '1.25em', fontWeight: 'bold' }}
                        color="white"
                      />{' '}
                      <span> button</span>
                    </React.Fragment>
                  }
                 
                >
                  <ReplayIcon
                    style={{ color: '#ee7600' }}
                    size={25}
                    onClick={onClickReload}
                    cursor="pointer"
                  />
                </HtmlTooltipWithBackground>
              ) : null}
            </>
          ) : null} */}
        </Grid>
      </Toolbar>
      <Dialog
        open={session.reloadStatus === true ? true : false}
        onClose={handleCloseAlert}
        classes={{
          paper: classes.dialogBoxRoot
        }}
        //fullWidth={true}
        maxWidth={'sm'}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        //onBackdropClick="false"
      >
        {/* <DialogTitle id="alert-dialog-title" style={{ paddingLeft: 108 }}>
            OpCodes
          </DialogTitle> */}
        <DialogContent
          classes={{
            root: classes.dialogRoot
          }}
        >
          <DialogContentText
            classes={{
              root: classes.dialogRootGrid
            }}
            id="alert-dialog-description"
          >
            <Typography
              variant="h5"
              classes={{
                root: classes.textRoot
              }}
              id="reprocess-complete-text"
            >
              Reprocess complete.
            </Typography>
            <Typography
              variant="h5"
              classes={{
                root: classes.textRoot
              }}
              id="please-click-text"
            >
              Please click
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              style={{ marginRight: 8 }}
              onClick={onClickReload}
              id="reload-button"
            >
              <Typography
                variant="h5"
                color="primary"
                style={{
                  fontStyle: 'italic',
                  fontWeight: 'bold',
                  fontSize: 17,
                  textDecoration: 'underline',
                  textDecorationColor: '#003d6b',
                  // margin: '0px 5px',

                  color: '#003d6b'
                }}
                id="reload-button-text"
              >
                Here
              </Typography>
            </Button>

            <Typography
              variant="h5"
              classes={{
                root: classes.textRoot
              }}
              id="continue-text"
            >
              to continue.{'  '}
            </Typography>
            {/* <Typography
              variant="h5"
              classes={{
                root: classes.textRoot
              }}
            >
              Thank you.
            </Typography> */}
          </DialogContentText>
        </DialogContent>
        {/* <DialogActions>
          <Button color="primary" autoFocus>
            OK
          </Button>
        </DialogActions> */}
      </Dialog>

      <Dialog
        //fullWidth
        //maxWidth="sm"
        id="confirmation-dialog"
        aria-labelledby="confirmation-dialog-title"
        open={openAllStoresDialog}
      >
        {/* <DialogTitle id="confirmation-dialog-title">
                <Typography
                  variant="h5"
                  color="primary"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Sign Out
                </Typography>
              </DialogTitle> */}
        <DialogContent dividers id="confirmation-dialog-content">
          <Typography
            variant="h6"
            style={{
              textTransform: 'none'
            }}
          >
            This option is not available from this page.
          </Typography>
        </DialogContent>
        <DialogActions id="confirmation-dialog-actions">
          <Button
            onClick={handleCancelAllStores}
            color="primary"
            id="confirmation-dialog-ok-button"
          >
            Ok
          </Button>
        </DialogActions>
      </Dialog>

      {session.payTypeErrors === true ||
      session.opcodeErrors === true ||
      session.advisorErrors === true ||
      session.techErrors === true ||
      session.dailyImportErrors === true ||
      session.newAdvisorErrors === true ||
      session.newPaytypeErrors === true ||
      session.modelError === true ||
      session.refreshErrorStatus === true ? (
        <NotificationsPopover
          handleNotificationsClose={handleNotificationsClose}
          anchorEl={notificationsRef.current}
          notifications={notifications}
          onClose={handleNotificationsClose}
          open={openNotifications}
          error={notificationsError}
        />
      ) : null}
      {/* {session.refreshErrorStatus === true ? (
        <NotificationsPopover
          handleNotificationsClose={handleNotificationsClose}
          anchorEl={notificationsRef.current}
          notifications={notifications}
          onClose={handleNotificationsClose}
          open={openNotifications}
          error={notificationsError}
        />
      ) : null} */}
    </AppBar>
  );
}

TopBar.propTypes = {
  className: PropTypes.string,
  onOpenNavBarMobile: PropTypes.func,
  keycloak: PropTypes.any,
  route: PropTypes.any
};

export default withKeycloak(TopBar);
