/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable import/no-cycle */
import { LinearProgress, Typography } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import { withKeycloak } from '@react-keycloak/web';
import PropTypes from 'prop-types';
import React, { Suspense, useState, useEffect, useCallback } from 'react';
import { renderRoutes } from 'react-router-config';
import { Redirect, useParams } from 'react-router-dom';
import $ from 'jquery';
import { Paper, CircularProgress } from '@material-ui/core';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import { useBottomScrollListener } from 'react-bottom-scroll-listener';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { useDispatch, useSelector } from 'react-redux';
import {
  setUsername,
  setToken,
  setRefreshStatus,
  setRefreshErrorStatus
} from 'src/actions';
import TopBar from './TopBar';
import NavBar from './NavBar';
import Footer from './Footer';
import { getRefreshViewsStatus } from 'src/utils/hasuraServices';
const useStyles = makeStyles(theme => ({
  container: {
    backgroundColor: '#F4F6F8',
    minHeight: '100vh',
    display: 'flex',
    '@media all and (-ms-high-contrast:none)': {
      height: 0 // IE11 fix
    }
  },
  content: {
    paddingTop:
      localStorage.getItem('storeGroup') == 'Ferrario Auto Team'
        ? 110
        : localStorage.getItem('storeGroup') == 'Metagroup Chrysler'
        ? 100
        : process.env.REACT_APP_DEALER == 'Armatus'
        ? 80
        : 74,
    flexGrow: 1,
    maxWidth: '100%',
    overflowX: 'hidden',
    overflowY: 'hidden',
    [theme.breakpoints.up('lg')]: {
      paddingLeft: 220
    },
    [theme.breakpoints.down('xs')]: {
      paddingTop: 56
    },
    '@media (min-width: 1280px)': {
      paddingLeft: 230
    }
  }
}));

function Dashboard({ route, keycloak, location, keycloakInitialized }) {
  const classes = useStyles();
  let { test } = useParams();
  const sessionStore = useSelector(state => state.session);
  const [openNavBarMobile, setOpenNavBarMobile] = useState(false);
  const [isBottom, setIsbottom] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const handleOnDocumentBottom = useCallback(() => {
    var top;

    window.addEventListener(
      'scroll',
      function(event) {
        top = this.scrollY;
        var scrollTop = $(window).scrollTop();

        var docHeight = $(document).height();
        var winHeight = $(window).height();
        var scrollPercent = scrollTop / (docHeight - winHeight);
        var scrollPercentRounded = Math.round(scrollPercent * 100);
        var scrollPercentRoundedValue = scrollPercent * 100;

        if (scrollPercentRoundedValue >= 98) {
          setIsbottom(true);
          $('.ktooltiptext').css('bottom', '-15em');
        } else {
          setIsbottom(false);
          $('.ktooltiptext').css('bottom', 'auto');
        }
      },
      false
    );
  }, []);

  useEffect(() => {
    console.log('urlsparms', test);
  }, []);

  useBottomScrollListener(handleOnDocumentBottom, {
    offset: 100
  });

  const dispatch = useDispatch();

  useEffect(() => {
    if (keycloakInitialized && keycloak.authenticated === true) {
      dispatch(setUsername(keycloak.tokenParsed.preferred_username));
    }
    if (
      keycloakInitialized &&
      keycloak.authenticated === true &&
      keycloak.token
    ) {
      dispatch(setToken(keycloak.token));
    }
  }, [sessionStore.storeSelected]);

  return keycloakInitialized === true ? (
    keycloak.authenticated === true && sessionStore.token ? (
      <>
        <TopBar storeval={localStorage.getItem('storeSelected')} />

        <NavBar
          onMobileClose={() => setOpenNavBarMobile(false)}
          openMobile={openNavBarMobile}
          isBottom={isBottom}
        />
        <div className={classes.container}>
          <div className={classes.content}>
            <Suspense fallback={<LinearProgress />}>
              {renderRoutes(route.routes)}
            </Suspense>
          </div>
        </div>

        <Footer />
      </>
    ) : (
      // <>
      //   {keycloak.authenticated === false ? (
      //     <Redirect to="/login" />
      //   ) : (
      //     // <Redirect to="/auth/login" />
      //     <Typography>No token </Typography>
      //   )}
      // </>
      <>
        {keycloak.authenticated === false ? (
          <Redirect to="/auth/login?provenance=fopc" />
        ) : (
          <Typography>No token </Typography>
        )}
      </>
    )
  ) : (
    <LoaderSkeleton />
  );
}

Dashboard.propTypes = {
  route: PropTypes.object,
  keycloak: PropTypes.any,
  location: PropTypes.any,
  keycloakInitialized: PropTypes.any
};

export default withKeycloak(Dashboard);
