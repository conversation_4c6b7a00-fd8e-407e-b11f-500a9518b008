/* eslint-disable react/no-multi-comp */
import React, { useEffect, useState } from 'react';
import { useLocation, matchPath } from 'react-router';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import 'src/styles.css';
import { Link as RouterLink } from 'react-router-dom';
import MoreIcon from '@material-ui/icons/MoreVert';
import $ from 'jquery';
import { makeStyles } from '@material-ui/styles';
import { withKeycloak } from '@react-keycloak/web';
import {
  Drawer,
  Grid,
  Box,
  List,
  ListSubheader,
  Typography,
  Hidden,
  IconButton,
  Button,
  Badge,
  Link,
  ListItem,
  ListItemText,
  colors
} from '@material-ui/core';
import { useDispatch, useSelector } from 'react-redux';
import NavItem from 'src/components/NavItem';
import navConfig from './navConfig';
import navConfigAdmin from './navConfigAdmin';
import navConfigUser from './navConfigUser';
import navConfigStoreCompare from './navConfigStoreCompare';
import { setNavItems } from 'src/actions';
import {
  version,
  date,
  versionDemo,
  dateDemo,
  launchDate
} from 'src/appversion.json';
import { nonExecutableDefinitionMessage } from 'graphql/validation/rules/ExecutableDefinitions';
import SettingsIcon from '@material-ui/icons/Settings';
import AddBoxIcon from '@material-ui/icons/AddBox';
import createSvgIcon from '@material-ui/icons/utils/createSvgIcon';
const TableViewIcon = createSvgIcon(
  <path d="M19,7H9C7.9,7,7,7.9,7,9v10c0,1.1,0.9,2,2,2h10c1.1,0,2-0.9,2-2V9C21,7.9,20.1,7,19,7z M19,9v2H9V9H19z M13,15v-2h2v2H13z M15,17v2h-2v-2H15z M11,15H9v-2h2V15z M17,13h2v2h-2V13z M9,17h2v2H9V17z M17,19v-2h2v2H17z M6,17H5c-1.1,0-2-0.9-2-2V5 c0-1.1,0.9-2,2-2h10c1.1,0,2,0.9,2,2v1h-2V5H5v10h1V17z" />,
  'ContentCut'
);

const useStyles = makeStyles(theme => ({
  root: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column'
  },
  item: {
    display: 'block',
    paddingTop: 0,
    paddingBottom: 0,
    paddingLeft: 5
  },
  mobileDrawer: {
    width: 260
  },
  paper: {
    height: 'calc(100vh - 76px)'
    // transition: 'height 0s ease-out'
  },
  paper1: {
    height: 'calc(100vh - 143px)',
    transition: 'height 0s ease-out'
  },
  listRoot: {
    width: '85%'
  },
  desktopDrawer: {
    width: 220,
    top:
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
        ? 75
        : process.env.REACT_APP_DEALER == 'Armatus'
        ? 75
        : 86,

    background: theme.palette.primary.navGradient,
    transition: 'height 0s',
    overflowX: 'hidden'
  },

  navigation: {
    overflow: 'auto',
    padding: theme.spacing(0, 0, 1, 1),
    marginTop: 10
    // flexGrow: 1,
    //display: 'flex'
    // flexDirection: 'column-reverse'

    // justifyContent: 'center',
  },
  leftNav: {},
  profile: {
    marginTop: 85,
    padding: theme.spacing(2),
    display: 'flex',
    alignItems: 'center'
  },
  img: {
    height: '100%',
    width: '100%'
  },
  badge: {
    boxShadow: `0 0 0 2px ${theme.palette.background.paper}`
  },
  badgeDot: {
    height: 9,
    minWidth: 9
  },
  onlineBadge: {
    backgroundColor: colors.green[600]
  },
  awayBadge: {
    backgroundColor: colors.orange[600]
  },
  busyBadge: {
    backgroundColor: colors.red[600]
  },
  offlineBadge: {
    backgroundColor: colors.grey[300]
  },
  avatar: {
    cursor: 'pointer',
    width: 40,
    height: 40
  },
  details: {
    marginLeft: theme.spacing(2)
  },
  moreButton: {
    marginLeft: 'auto',
    color: colors.blueGrey[200]
  },
  changeLog: {
    fontSize: 10,
    color: '#fff'
  },

  buttonHome: {
    // padding: '10px 8px',
    justifyContent: 'flex-start',
    textTransform: 'none',
    letterSpacing: 0,
    width: '100%',
    color: '#fff',
    '&:hover': {
      backgroundColor: theme.palette.primary.active,
      color: theme.palette.primary.main,
      borderRadius: 4,
      '& $icon': {
        color: theme.palette.primary.main
      }
    }
  },
  changelogDiv: {
    border: '1px solid #fff',
    marginTop: '8px',
    width: '97%',
    padding: 4
    // '@media (max-width: 1920px)': {
    //   marginTop: '27vh'
    // },

    // '@media (max-width: 1440px)': {
    //   marginTop: '18vh'
    // },

    // '@media (max-width: 1280px)': {
    //   marginTop: '17vh'
    // },
    // '@media (min-width: 2304px)': {
    //   marginTop: '51vh'
    // }
  },
  changelogDivBottom: {
    border: '1px solid #fff',
    marginTop: '8px',
    width: '97%',
    padding: 4
    // '@media (max-width: 1920px)': {
    //   marginTop: '17.5vh'
    // },

    // '@media (max-width: 1440px)': {
    //   marginTop: '21vh'
    // },

    // '@media (max-width: 1280px)': {
    //   marginTop: '24vh'
    // },
    // '@media (min-width: 2304px)': {
    //   marginTop: '45vh'
    // }
  },
  listPadding: {
    paddingTop: 8,
    paddingBottom: 2
  },
  buttonLeaf: {
    //padding: '10px 8px',
    justifyContent: 'flex-start',
    textTransform: 'none',
    letterSpacing: 0,
    width: '100%',
    color: '#fff',
    fontWeight: theme.typography.fontWeightMedium,
    '&:hover': {
      backgroundColor: theme.palette.primary.active,
      color: theme.palette.primary.main,
      borderRadius: 4,
      '& $icon': {
        color: theme.palette.primary.main
      }
    },
    '&.depth-0': {
      fontWeight: theme.typography.fontWeightMedium
    }
  },
  itemLeaf: {
    opacity: 0.5,
    pointerEvents: 'none'
  },
  sideTop: {
    borderBottom: '4px solid #ee7600'
  },
  active: {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
    border: '1px solid ' + theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
}));
function renderNavItems({
  // eslint-disable-next-line react/prop-types
  items,
  subheader,
  role,
  handleMenuOpen,
  navItemStatus,
  key,
  isBottom,
  ...rest
}) {
  return (
    <List key={key}>
      {subheader && <ListSubheader disableSticky>{subheader}</ListSubheader>}
      {/* eslint-disable-next-line react/prop-types */}
      {items.reduce(
        // eslint-disable-next-line no-use-before-define
        (acc, item) =>
          reduceChildRoutes({
            acc,
            item,
            role,
            handleMenuOpen,
            navItemStatus,
            ...rest
          }),
        []
      )}
    </List>
  );
}

function reduceChildRoutes({
  acc,
  pathname,
  item,
  handleMenuOpen,
  role,
  navItemStatus,
  depth = 0
}) {
  if (item.items) {
    let open = false;

    if (navItemStatus && typeof navItemStatus != 'undefined') {
      open = true;
    } else {
      if (
        (item.title == 'Home' || item.title == 'Homenew') &&
        pathname == '/MyFavorites'
      ) {
        open = true;
      } else {
        open = matchPath(pathname, {
          path: item.href,
          exact: false
        });
      }
    }

    acc.push(
      <NavItem
        depth={depth}
        icon={item.icon}
        key={item.href}
        label={item.label}
        handleMenuOpen={handleMenuOpen}
        open={Boolean(open)}
        title={item.title}
        disabled={
          JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
          (item.title == 'Reports' ||
            item.title == 'Labor' ||
            item.title == 'Client Report Card' ||
            item.title == 'Armatus Admin' ||
            item.title == 'Parts')
            ? false
            : JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
            ? true
            : false
        }
      >
        {renderNavItems({
          depth: depth + 1,
          pathname,
          handleMenuOpen,
          items: item.items
        })}
      </NavItem>
    );
  } else {
    acc.push(
      <NavItem
        depth={depth}
        href={item.href}
        icon={item.icon}
        key={item.href}
        handleMenuOpen={handleMenuOpen}
        label={item.label}
        title={item.title}
        disabled={
          JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
          (item.title.includes('KPI Store Comparative') ||
            item.title.includes('KPI Graphics') ||
            item.title.includes('MPI Stats') ||
            item.title.includes('3 Month Total') ||
            item.title.includes('1 Month') ||
            item.title.includes('Repair Labor \n Target Misses') ||
            item.title.includes('Repair Parts \n Target Misses') ||
            item.title.includes('Saved Reports') ||
            item.title.includes('Reports Saved'))
            ? false
            : JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
            ? true
            : false
        }
      />
    );
  }

  return acc;
}

function NavBar({
  openMobile,
  onMobileClose,
  isBottom,
  className,
  keycloak,
  ...rest
}) {
  const classes = useStyles();
  const location = useLocation();
  const session = useSelector(state => state.session);
  const dispatch = useDispatch();
  const [scrollHeight, setScrollHeight] = useState(false);
  useEffect(() => {
    if (openMobile && onMobileClose) {
      onMobileClose();
    }

    // eslint-disable-next-line
  }, [location.pathname]);
  useEffect(() => {
    //dispatch(setNavItems(['Home']));

    let array = navConfigAdmin;
    let arrayUser = navConfigUser;
    // if (localStorage.getItem('realm') != 'demoag') {
    //   remove(array, 'Fleet Accounts');
    // }
    // if (localStorage.getItem('realm') != 'demoenterprise') {
    //   remove(array, 'Fleet Accounts');
    // }
    if (session.versionFlag == 'FALSE') {
      remove(array, 'KPI Advisor Comparative');
      remove(array, 'KPI Store Comparative');
      remove(array, 'KPI Graphics');
      remove(arrayUser, 'KPI Advisor Comparative');
      remove(arrayUser, 'KPI Store Comparative');
      remove(arrayUser, 'KPI Graphics');
      remove(array, 'Model Mapping - \n Menu');
      remove(array, 'Model Mapping - \n Grid');
      remove(array, 'Menu / MPI Setups');
      remove(array, 'Labor Work Mix \n Other');
      remove(arrayUser, 'Labor Work Mix \n Other');
    }
    if (localStorage.getItem('versionFlag') == 'FALSE') {
      remove(array, 'Repair Labor \n Target Misses');
      remove(arrayUser, 'Repair Labor \n Target Misses');
    }
    if (session.versionFlag == 'TRUE') {
      remove(array, 'Repair Labor \n Target Misses ');
      remove(arrayUser, 'Repair Labor \n Target Misses ');
      remove(array, 'Labor Work Mix \n Other ');
      remove(arrayUser, 'Labor Work Mix \n Other ');
    }
    if (session.versionFlag == 'FALSE') {
      remove(array, 'Repair Parts \n Target Misses');
      remove(arrayUser, 'Repair Parts \n Target Misses');
    }
    if (session.versionFlag == 'TRUE') {
      remove(array, 'Repair Parts \n Target Misses ');
      remove(arrayUser, 'Repair Parts \n Target Misses ');
    }

    if (session.versionFlag == 'FALSE') {
      remove(array, 'Parts Matrix(s)');
      remove(arrayUser, 'Parts Matrix(s)');
    }
    if (session.versionFlag == 'TRUE') {
      remove(array, 'Parts Matrix(s) ');
      remove(arrayUser, 'Parts Matrix(s) ');
    }

    if (
      session.versionFlag == 'FALSE' &&
      JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
    ) {
      remove(array, 'KPI Store Comparative');
    }

    if (
      (localStorage.getItem('realm') == 'mullerag' &&
        JSON.parse(localStorage.getItem('selectedStoreId')) == '211726444') ||
      localStorage.getItem('realm') != 'mullerag'
    ) {
      remove(array, 'Store Assignments');
    } else if (
      localStorage.getItem('realm') == 'mullerag' &&
      JSON.parse(localStorage.getItem('selectedStoreId')) != '211726444'
    ) {
      let itemIndex = [];
      navConfigAdmin[0].items.map((item, index) => {
        if (item.title == 'Armatus Admin') {
          itemIndex.push(index);
        }
      });

      if (itemIndex.length > 0) {
        let assignmentArr = navConfigAdmin[0].items[itemIndex[0]].items.filter(
          item => item.title == 'Store Assignments'
        );
        if (assignmentArr.length == 0) {
          navConfigAdmin[0].items[itemIndex[0]].items.splice(2, 0, {
            title: 'Store Assignments',
            href: '/StoreAssignments',
            icon: SettingsIcon
          });
        }
      }
    }

    if (array.length > 0) {
      let arrayData = array[0].items;
      arrayData.filter(item => {
        if (item.title == 'Reference / Setups') {
          item.items.forEach(subItem => {
            if (subItem.title === 'Labor Grid(s)') {
              subItem.icon = AddBoxIcon;
            } else if (subItem.title === 'Parts Matrix(s)') {
              subItem.icon = AddBoxIcon;
            }
          });
        }
      });
    }

    if (keycloak.realmAccess.roles.includes('superadmin') && array.length > 0) {
      let arrayData = array[0].items;
      arrayData.filter(item => {
        if (item.title == 'Reference / Setups') {
          remove(item.items, 'Store Settings');
        }
      });
    }
    if (
      keycloak.realmAccess.roles.includes('superadmin') == false &&
      array.length > 0
    ) {
      let arrayData = array[0].items;
      arrayData.filter(item => {
        if (item.title == 'Armatus Admin') {
          remove(item.items, 'Store Settings');
        }
      });
    }
  }, [session.storeSelected]);
  const remove = (array, id) => {
    array.some((o, i, a) =>
      o.title == id ? a.splice(i, 1) : remove(o.items || [], id)
    );
  };

  useEffect(() => {
    let listItems = [];

    if (session.navItemStatus) {
      if (
        typeof keycloak.realmAccess.roles != 'undefined' &&
        typeof keycloak.realmAccess.roles[0] == 'string' &&
        (keycloak.realmAccess.roles[0] == 'admin' ||
          keycloak.realmAccess.roles[0] == 'superadmin' ||
          keycloak.realmAccess.roles.includes('admin') == true ||
          keycloak.realmAccess.roles.includes('superadmin') == true) &&
        JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
      ) {
        navConfigAdmin[0].items.map(list => {
          // console.log('list==', navConfigUser, list);
          listItems.push(list.title);
        });

        dispatch(setNavItems(listItems));
      } else if (
        typeof keycloak.realmAccess.roles != 'undefined' &&
        typeof keycloak.realmAccess.roles[0] == 'string' &&
        (keycloak.realmAccess.roles[0] == 'user' ||
          keycloak.realmAccess.roles.includes('user') ||
          keycloak.realmAccess.roles.includes('client')) === true &&
        // (keycloak.realm == 'lupient' || keycloak.realm == 'demo_store') &&
        JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
      ) {
        navConfigUser[0].items.map(list => {
          // console.log('list==', navConfigUser, list);
          listItems.push(list.title);
        });

        dispatch(setNavItems(listItems));
      }
      // else if (
      //   JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
      // ) {
      //   navConfigStoreCompare[0].items.map(list => {
      //     // console.log('list==', navConfigUser, list);
      //     listItems.push(list.title);
      //   });
      //   dispatch(setNavItems(listItems));
      //   navConfigUser[0].items.map(list => {
      //     // navConfig[0].items.map(list => {
      //     // console.log('list==', navConfigUser, list);
      //     listItems.push(list.title);
      //   });
      // }

      dispatch(setNavItems(listItems));
      setScrollHeight(true);
    } else {
      if (session.navItems.length > 1) {
        dispatch(setNavItems([]));
        setScrollHeight(false);
      }
    }

    // eslint-disable-next-line
  }, [session.navItemStatus]);
  const handleMenuOpen = val => {
    if (val == 0) {
      setScrollHeight(true);
    } else {
      if (session.navItemStatus == false) {
        setScrollHeight(false);
      }
    }
  };

  const content = (
    <div id="drawerRoot" {...rest} className={clsx(classes.root, className)}>
      <div className={'side-bar'}>
        <Box
          display="inline-flex"
          alignItems="center"
          justifyContent="space-between"
          id="changelog-div"
          className={
            isBottom ? classes.changelogDivBottom : classes.changelogDiv
          }
        >
          <Typography
            variant="subtitle1"
            color="primary"
            style={{
              fontSize: 9,
              color: '#fff'
            }}
          >
            <span>{session.versionFlag == 'TRUE' ? date : dateDemo}</span>
          </Typography>
          <Typography
            variant="h5"
            color="primary"
            style={{
              fontSize: 13,
              fontWeight: 'bold',
              color: '#fff'
            }}
          >
            Version&nbsp;
            {session.versionFlag == 'TRUE' ? version : versionDemo}
          </Typography>

          <RouterLink
            to="/ChangeLog"
            style={{
              fontSize: 9,
              color: '#fff'
            }}
          >
            <Typography
              variant="h5"
              color="primary"
              style={{
                fontSize: 9,
                fontStyle: 'italic',
                fontWeight: 'bold',
                textDecoration: 'underline',
                textDecorationColor: '#ee7600',

                color: '#fff'
              }}
            >
              See&nbsp;What's&nbsp;New
            </Typography>
          </RouterLink>
        </Box>

        <List
          classes={{
            padding: classes.listPadding
          }}
        >
          {/* {localStorage.getItem('realm') != 'demoenterprise' &&
            localStorage.getItem('realm') != 'firstteamag' && ( */}
          {session.versionFlag == 'FALSE' && (
            <ListItem disableGutters className={clsx(classes.item)}>
              <Button
                activeClassName={
                  session.menuSelected == 'Home' ? classes.active : ''
                }
                className={clsx(
                  classes.buttonLeaf,
                  classes.buttonHome,
                  session.menuSelected == 'Home' ? classes.active : ''
                )}
                component={RouterLink}
                style={{ paddingLeft: 20, fontSize: 14 }}
                to={'/2.4.0/Home'}
                id={'Homenew'}
                // onClick={() => handleMenuClick(title)}
              >
                {'Home'}
              </Button>
            </ListItem>
          )}

          {/* {(localStorage.getItem('realm') == 'demoenterprise' ||
            localStorage.getItem('realm') == 'firstteamag') && ( */}
          {session.versionFlag == 'TRUE' && (
            <ListItem disableGutters className={clsx(classes.item)}>
              <Button
                activeClassName={
                  session.menuSelected == 'Home' ? classes.active : ''
                }
                className={clsx(
                  classes.buttonLeaf,
                  classes.buttonHome,
                  session.menuSelected == 'Home' ? classes.active : ''
                )}
                component={RouterLink}
                style={{ paddingLeft: 20, fontSize: 14 }}
                to={'/Home'}
                id={'Home'}
                // onClick={() => handleMenuClick(title)}
              >
                {'Home'}
              </Button>
            </ListItem>
          )}
          <ListItem disableGutters className={clsx(classes.item)}>
            <Button
              activeClassName={
                session.menuSelected == 'Favorites' ? classes.active : ''
              }
              className={clsx(
                classes.buttonLeaf,
                classes.buttonHome,
                session.menuSelected == 'Favorites' ? classes.active : '',
                JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
                  ? classes.itemLeaf
                  : ''
              )}
              component={RouterLink}
              exact
              style={{ paddingLeft: 20, fontSize: 14 }}
              to={'/MyFavorites'}
              id={'Favorites'}
              // disabled={
              //   JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
              // }
              // onClick={() => handleMenuClick(title)}
            >
              {'Favorites'}
            </Button>
          </ListItem>
        </List>
      </div>
      <nav
        id="navelement"
        className={clsx(
          session.navItemStatus == false
            ? classes.navigation
            : classes.navigation,
          'leftNav'
        )}
        // style={{
        //   flexDirection: session.navItems.length == 9 ? '' : 'column-reverse'
        // }}
      >
        {/* <Box
          display="inline-flex"
          alignItems="center"
          justifyContent="space-between"
          id="changelog-div"
          className={
            isBottom ? classes.changelogDivBottom : classes.changelogDiv
          }
        >
          <Typography
            variant="h5"
            color="primary"
            style={{
              fontSize: 9,
              fontStyle: 'italic',
              fontWeight: 'bold',
              color: '#fff'
            }}
          >
            Launched&nbsp;On&nbsp;&nbsp;&nbsp;
          </Typography>
          <Typography
            variant="h5"
            color="primary"
            style={{
              fontSize: 9,
              fontStyle: 'italic',
              fontWeight: 'bold',
              color: '#fff',
              marginRight: 'auto'
            }}
          >
            {launchDate}
          </Typography>
        </Box> */}
        {typeof keycloak.realmAccess.roles != 'undefined' &&
        typeof keycloak.realmAccess.roles[0] == 'string' &&
        (keycloak.realmAccess.roles[0] == 'admin' ||
          keycloak.realmAccess.roles[0] == 'superadmin' ||
          keycloak.realmAccess.roles.includes('admin') == true ||
          keycloak.realmAccess.roles.includes('superadmin') == true)
          ? //   &&
            // JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
            navConfigAdmin.map(list =>
              renderNavItems({
                items: list.items,
                subheader: list.subheader,
                pathname: location.pathname,
                handleMenuOpen: handleMenuOpen,
                navItemStatus: session.navItemStatus,
                role: keycloak.realmAccess.roles[0],
                key: list.subheader,
                isBottom: isBottom
              })
            )
          : // : JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
          // ? navConfigStoreCompare.map(list =>
          //     renderNavItems({
          //       items: list.items,
          //       subheader: list.subheader,
          //       pathname: location.pathname,
          //       handleMenuOpen: handleMenuOpen,
          //       navItemStatus: session.navItemStatus,
          //       role: keycloak.realmAccess.roles[0],
          //       key: list.subheader,
          //       isBottom: isBottom
          //     })
          //   )
          typeof keycloak.realmAccess.roles != 'undefined' &&
            typeof keycloak.realmAccess.roles[0] === 'string' &&
            (keycloak.realmAccess.roles[0] == 'user' ||
              keycloak.realmAccess.roles.includes('user') ||
              keycloak.realmAccess.roles.includes('client')) === true &&
            //(keycloak.realm == 'lupient' || keycloak.realm == 'demo_store') &&
            JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1
          ? navConfigUser.map(list =>
              renderNavItems({
                items: list.items,
                subheader: list.subheader,
                pathname: location.pathname,
                handleMenuOpen: handleMenuOpen,
                navItemStatus: session.navItemStatus,
                role: keycloak.realmAccess.roles[0],
                key: list.subheader,
                isBottom: isBottom
              })
            )
          : navConfig.map(list =>
              renderNavItems({
                items: list.items,
                subheader: list.subheader,
                pathname: location.pathname,
                handleMenuOpen: handleMenuOpen,
                navItemStatus: session.navItemStatus,
                role: keycloak.realmAccess.roles[0],
                key: list.subheader,
                isBottom: isBottom
              })
            )}
      </nav>

      {/* <Divider className={classes.divider} /> */}
    </div>
  );

  return (
    <>
      <Hidden lgUp>
        <Drawer
          anchor="left"
          classes={{
            paper: classes.mobileDrawer
          }}
          onClose={onMobileClose}
          open={openMobile}
          variant="temporary"
        >
          {content}
        </Drawer>
      </Hidden>
      <Hidden mdDown>
        <Drawer
          id="navBarDiv"
          container
          anchor="left"
          classes={{
            paper: isBottom
              ? clsx(classes.desktopDrawer, classes.paper1)
              : clsx(classes.desktopDrawer, classes.paper)
          }}
          open
          variant="persistent"
        >
          {content}
        </Drawer>
      </Hidden>
    </>
  );
}

NavBar.propTypes = {
  className: PropTypes.string,
  onMobileClose: PropTypes.func,
  openMobile: PropTypes.bool
};

export default withKeycloak(NavBar);
