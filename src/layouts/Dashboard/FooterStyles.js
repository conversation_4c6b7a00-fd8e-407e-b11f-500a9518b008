import styled from 'styled-components';

export const Box = styled.div`
  @media (max-width: 1920px) {
    width: 40%;
  }
  @media (max-width: 1280px) {
    width: 45%;
  }
  @media (max-width: 2304px) {
    width: 35%;
  }
`;

export const Container = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  max-width: 1000px;
  margin: 0 auto;
  /* background: red; */
`;

export const Column = styled.div``;

export const Row = styled.div``;

export const FooterLink = styled.div`
  color: #fff;
  margin-bottom: 5px;
  font-size: 14px;
  text-decoration: none;
  display: flex;

  justify-content: space-between;
  @media (max-width: 2304px) {
    height: 15px;
    width: 225px;
  }
  @media (max-width: 1920px) {
    height: 12px;
    width: 175px;
  }
  @media (max-width: 1280px) {
    height: 12px;
    width: 175px;
  }
`;

export const Heading = styled.p`
  font-size: 14px;
  color: #fff;
  margin-bottom: 5px;
  font-weight: bold;
`;
