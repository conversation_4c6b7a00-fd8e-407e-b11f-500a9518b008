/* eslint-disable react/no-multi-comp */
/* eslint-disable react/display-name */
import React, { Component } from 'react';
import BarChartIcon from '@material-ui/icons/BarChart';
import ReceiptIcon from '@material-ui/icons/ReceiptOutlined';
import ShowChartIcon from '@material-ui/icons/ShowChart';
import LocalShippingIcon from '@material-ui/icons/LocalShipping';
import GraphicEqIcon from '@material-ui/icons/GraphicEq';
import FolderSpecialIcon from '@material-ui/icons/FolderSpecial';
import AttachMoneyIcon from '@material-ui/icons/AttachMoney';
import NoteIcon from '@material-ui/icons/Note';
import LocalPlayIcon from '@material-ui/icons/LocalPlay';
import PaymentIcon from '@material-ui/icons/Payment';
import AspectRatioIcon from '@material-ui/icons/AspectRatio';
import AllInboxIcon from '@material-ui/icons/AllInbox';
import ListAltIcon from '@material-ui/icons/ListAlt';
import AccountTreeIcon from '@material-ui/icons/AccountTree';
import FilterNoneIcon from '@material-ui/icons/FilterNone';
import AddBoxIcon from '@material-ui/icons/AddBox';
import SupervisorAccountIcon from '@material-ui/icons/SupervisorAccount';
import EmojiPeopleIcon from '@material-ui/icons/EmojiPeople';
import StoreIcon from '@material-ui/icons/Store';
import AssignmentIndIcon from '@material-ui/icons/AssignmentInd';
import HowToRegIcon from '@material-ui/icons/HowToReg';
import AssignmentTurnedInIcon from '@material-ui/icons/AssignmentTurnedIn';
import StarIcon from '@material-ui/icons/Star';
import SearchIcon from '@material-ui/icons/Search';
import SettingsIcon from '@material-ui/icons/Settings';
import PeopleAltOutlinedIcon from '@material-ui/icons/PeopleAltOutlined';
import PermDataSettingOutlinedIcon from '@material-ui/icons/PermDataSettingOutlined';
import MenuBookIcon from '@material-ui/icons/MenuBook';
import TableChartIcon from '@material-ui/icons/TableChart';
import ScreenShareIcon from '@material-ui/icons/ScreenShare';
import NewReleasesIcon from '@material-ui/icons/NewReleases';
import PeopleIcon from '@material-ui/icons/People';
import TrendingDownIcon from '@material-ui/icons/TrendingDown';
import EqualizerIcon from '@material-ui/icons/Equalizer';
import EmojiSymbolsIcon from '@material-ui/icons/EmojiSymbols';
import ImportExportIcon from '@material-ui/icons/ImportExport';
import HelpIcon from '@material-ui/icons/Help';
import PersonAddIcon from '@material-ui/icons/PersonAdd';
import PieChartIcon from '@material-ui/icons/PieChart';
import AccountBoxIcon from '@material-ui/icons/AccountBox';
import DirectionsCarIcon from '@material-ui/icons/DirectionsCar';
import BallotIcon from '@material-ui/icons/Ballot';
import CompassCalibrationIcon from '@material-ui/icons/CompassCalibration';
import PostAddIcon from '@material-ui/icons/PostAdd';
import AssessmentOutlinedIcon from '@material-ui/icons/AssessmentOutlined';
import EventNoteOutlinedIcon from '@material-ui/icons/EventNoteOutlined';
import PersonIcon from '@material-ui/icons/Person';
import BuildIcon from '@material-ui/icons/Build';
import LibraryBooksIcon from '@material-ui/icons/LibraryBooks';
import createSvgIcon from '@material-ui/icons/utils/createSvgIcon';
import CalendarTodayIcon from '@material-ui/icons/CalendarToday';
import Email from '@material-ui/icons/Email';
const PartsIcon1 = createSvgIcon(
  <svg
    id="Layer_1"
    data-name="Layer 1"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 120.41 122.88"
  >
    <title>repair-fix-repairing</title>
    <path d="M19,70.15c-4.75,1-9.66,1.1-14.42-3.79C1.4,63.1-.19,58.23,0,53.13c12.17,14.15,27-3.34,9-12.43,8.69-4.12,16.89-.25,21.24,7.76,2.06,3.78,3.29,5.76,4.86,7.11a8.58,8.58,0,0,0,.9.69A8.1,8.1,0,0,1,39.8,51.8,7.14,7.14,0,0,1,43.05,51c3.18,0,7.14,1.9,7.81,5.14h0a1.61,1.61,0,0,1,.44-.06h2.31a1.5,1.5,0,0,1,.37,0,5.6,5.6,0,0,1,5,4.59,1.65,1.65,0,0,1,.71-.16H62a1.5,1.5,0,0,1,.37,0c3.31.51,4.65,2.55,5.12,5.12l.26,0h2.31a1.5,1.5,0,0,1,.37,0c3.8.59,5.29,3.08,5.87,6.08,7-1.07,14.57-5.27,22,2.33a17.78,17.78,0,0,1,4.57,13.23c-12.17-14.15-27,3.34-9,12.43-7.47,3.54-14.59,1.17-19.2-4.66a30.06,30.06,0,0,1-2.34,4.92c-.38.62-.77,1.26-1.27,2.14a.16.16,0,0,0,0,.07,19.77,19.77,0,0,1-1.3,2h1.6a2.51,2.51,0,0,1,2.5,2.5v13.61a2.51,2.51,0,0,1-2.5,2.5H35.13a2.51,2.51,0,0,1-2.5-2.5V106.77a2.51,2.51,0,0,1,2.5-2.5H36a18,18,0,0,1-.9-3.79L22.64,80.07,22.58,80a22.48,22.48,0,0,1-2.5-4.24,12.37,12.37,0,0,1-1.08-4,10.22,10.22,0,0,1,0-1.57Zm92-33.29a2,2,0,0,0-.37-.74,1.51,1.51,0,0,0-.57-.46,1.61,1.61,0,0,0-.7-.22,2.62,2.62,0,0,0-.76.09l-.06,0-7.36,2.24a3,3,0,0,1-3.63-1.55c-.38-.64-.75-1.27-1.2-2s-1-1.38-1.39-2S94,31,93.4,30.38s-1-1.14-1.69-1.83A3.09,3.09,0,0,1,91.22,25l3.91-7.35a1.63,1.63,0,0,0,.13-1.46,2.07,2.07,0,0,0-.31-.65,1.82,1.82,0,0,0-.61-.49l-9.26-4.93a2.81,2.81,0,0,0-.68-.26,2.72,2.72,0,0,0-.82.11,1.31,1.31,0,0,0-.61.33,1.82,1.82,0,0,0-.49.61l-3.6,6.75a3.13,3.13,0,0,1-3.69,1.52c-.82-.21-1.59-.41-2.27-.55s-1.54-.28-2.36-.39a23.32,23.32,0,0,0-2.4-.19c-.84-.06-1.62,0-2.37-.06a3,3,0,0,1-3-2.18l-2.5-8.1c0-.06,0-.06,0-.1a2.52,2.52,0,0,0-.3-.66,2.09,2.09,0,0,0-1.26-.61,1.94,1.94,0,0,0-.82.11L48,9.48a1.9,1.9,0,0,0-.74.36,1.71,1.71,0,0,0-.47.57,1.5,1.5,0,0,0-.21.71,2.29,2.29,0,0,0,.09.75l0,.05,2.25,7.35a3.08,3.08,0,0,1-1.56,3.64c-.64.37-1.27.74-2,1.2s-1.38.95-2,1.39-1.3,1-1.91,1.54-1.15,1-1.83,1.7a3.1,3.1,0,0,1-3.59.48l-7.34-3.91a1.88,1.88,0,0,0-.75-.23,3.3,3.3,0,0,0-.69.07,1.31,1.31,0,0,0-.61.32,1.75,1.75,0,0,0-.49.62l-.4.74a24.3,24.3,0,0,0-6.73-.35l1.78-3.35a8.61,8.61,0,0,1,2-2.49,8.51,8.51,0,0,1,2.81-1.5,8.08,8.08,0,0,1,3.18-.33,8.57,8.57,0,0,1,3,1l5.43,2.9.4-.33c.7-.61,1.45-1.19,2.2-1.78s1.56-1.18,2.3-1.66l.26-.19-1.5-5.05a7.44,7.44,0,0,1-.33-3.17,7.84,7.84,0,0,1,.92-3.06,7.75,7.75,0,0,1,2-2.46,8.26,8.26,0,0,1,2.82-1.5L56.28.41l0,0A8.58,8.58,0,0,1,59.43,0a7.71,7.71,0,0,1,3.06.92A8.54,8.54,0,0,1,65,3a7.27,7.27,0,0,1,1.39,2.81l1.83,5.88a2.12,2.12,0,0,0,.43,0c.9.05,1.83.16,2.81.25s1.87.28,2.81.46a1,1,0,0,1,.39.1l2.46-4.63a7.84,7.84,0,0,1,4.82-3.92l.06,0a7.71,7.71,0,0,1,3.11-.31,8.44,8.44,0,0,1,3,.89l9.25,4.94a8.58,8.58,0,0,1,2.49,2,7.68,7.68,0,0,1,1.5,2.82,8,8,0,0,1,.33,3.17,8.31,8.31,0,0,1-1,3l-2.9,5.44c.11.11.23.28.34.39.6.71,1.19,1.45,1.77,2.2s1.19,1.56,1.66,2.3l.19.27,5.05-1.51a7.43,7.43,0,0,1,3.17-.32,8,8,0,0,1,3.06.91,7.67,7.67,0,0,1,2.46,2l0,.08a8.26,8.26,0,0,1,1.5,2.82l3,9.91.05,0a8.8,8.8,0,0,1,.35,3.13,7.87,7.87,0,0,1-.92,3.06,8.54,8.54,0,0,1-2,2.46A7.31,7.31,0,0,1,114.63,55l-5.88,1.83c0,.15,0,.32,0,.47,0,.91-.15,1.83-.24,2.71,0,0,0,.11,0,.15A26.84,26.84,0,0,1,108,63l-.07.34,4.67,2.49a7.52,7.52,0,0,1,2.47,2,8,8,0,0,1,1.45,2.79l0,.07a7.66,7.66,0,0,1,.31,3.1,8.25,8.25,0,0,1-.9,3l-.74,1.39-1.32-.71a25.71,25.71,0,0,0-4.34-7.75c-.14-.19-.29-.37-.45-.55a27.07,27.07,0,0,0-7.55-6.25c.09-.37.18-.75.26-1.13.13-.75.28-1.54.37-2.42,0,0,0-.11,0-.15.11-.81.17-1.54.23-2.27s.09-1.59.07-2.37a3,3,0,0,1,2.18-3l8.09-2.5s.07,0,.11,0a2.31,2.31,0,0,0,.65-.3,1.52,1.52,0,0,0,.41-.55,1.77,1.77,0,0,0,.21-.71,1.94,1.94,0,0,0-.11-.82L111,36.8l0,.06Zm-34-3.09a21.79,21.79,0,0,1,3.93,2.64,23.11,23.11,0,0,1,3.3,3.33,29.87,29.87,0,0,1,2.5,3.79,21.32,21.32,0,0,1,1.72,4.13,20.05,20.05,0,0,1,.86,4.44,20.89,20.89,0,0,1,0,4.54c0,.11,0,.19,0,.26a23,23,0,0,1-.68,3.77c-1.64.16-3.27.42-4.88.71l-1.48-.79c.13-.34.24-.69.35-1.05a18.63,18.63,0,0,0,.64-3.14.8.8,0,0,1,0-.22,17.14,17.14,0,0,0,0-3.44,17.56,17.56,0,0,0-.62-3.23,14.89,14.89,0,0,0-1.28-3,18.34,18.34,0,0,0-1.91-2.87A16.74,16.74,0,0,0,77,41.19a18.31,18.31,0,0,0-6.2-3.3,18.63,18.63,0,0,0-3.14-.64.57.57,0,0,1-.22,0,18.76,18.76,0,0,0-3.44,0,17.57,17.57,0,0,0-3.23.63,14.89,14.89,0,0,0-3,1.28,18.26,18.26,0,0,0-2.87,1.9,17.35,17.35,0,0,0-2.43,2.42c-.23.27-.46.56-.68.87l-5.46-2.91a20.4,20.4,0,0,1,1.42-1.9A22.63,22.63,0,0,1,51,36.26a28.91,28.91,0,0,1,3.79-2.51A24.63,24.63,0,0,1,59,32a22,22,0,0,1,4.39-.88,21.63,21.63,0,0,1,4.55,0c.1,0,.19.05.25,0a23.26,23.26,0,0,1,4.35.84,26.43,26.43,0,0,1,4.39,1.79l0,0Zm-37.89,24h0v.1h0l0,0V58h0v.21h0v.11h0v.11h0v.34h0V59h0v.12h0v.23h0v.13h0v.13h0V60h0v.13h0v.14h0v.25h0V79a1.68,1.68,0,1,1-3.35,0v-3.1h0a1.84,1.84,0,0,1-.25-.17L31.8,73.16l-4.34-3.57A6.52,6.52,0,0,0,26,68.66H24.87a2.57,2.57,0,0,0-1.45.06,1.46,1.46,0,0,0-.79.81h-.1a5.44,5.44,0,0,0-.2,2.11,8.87,8.87,0,0,0,.81,2.9A18.68,18.68,0,0,0,25.26,78s.07.1.1.16l12.7,20.76a1.68,1.68,0,0,1,.28.76h0a11.41,11.41,0,0,0,1.31,4.59H65a11,11,0,0,0,3.08-3.63.47.47,0,0,1,.06-.09c.36-.62.84-1.41,1.31-2.18a29.64,29.64,0,0,0,2.86-6.82v0l0-.06,0-.1,0-.06v0l0-.1,0-.07v0l0-.1.06-.2,0-.1h0l0-.09,0-.1v0l0-.07,0-.1v-.1l0-.1V90l0-.1,0-.09h0l0-.1,0-.1h0l0-.08,0-.1v0l0-.07,0-.1v-.1l0-.1,0-.07v0l0-.1,0-.08v0l0-.09,0-.1h0l0-.09,0-.1v-.1l0-.09v-.29l0-.1v-.1l0-.09v-.1h0v-.09l0-.1v-.84h0v-1l-.13-5.7a1.94,1.94,0,0,1,0-.24c0-.08,0-.62,0-1.34v-.18h0V76.84h0v-.19h0V75.36h0v-.19h0V74.1h0v-.17h0v-.18h0v-.19l0-.09v-.64l0-.08,0-.09,0-.09h0v-.09l0-.07h0l0-.09v-.08l0-.09V72a3.67,3.67,0,0,0-2.89-3H67.74c0,.47,0,.93,0,1.4h0v0h0v0h0v0h0v.1h0v0h0v.08h0V71h0v0h0v0h0v.1h0v0h0v0h0v0h0v.07h0v0h0v0h0v0h0v0h0v.05h0v0h0v0h0v0h0v0h0v.05h0v0h0v0h0v0h0v0h0v0h0v0h0v0h0v.05c-.06.93-.11,1.84-.11,2.7a1.68,1.68,0,0,1-3.36,0c0-.86.07-1.86.13-2.9v-.33h0v-.17h0v-.15h0v-.33h0v-.17h0v-.15h0v-.17h0V70.2h0v-.34h0v-.33h0V69.2h0V69c0-2.52-.45-4.71-2.48-5.08H59.7a1.37,1.37,0,0,1-.36,0c0,.94,0,1.92-.08,2.87v1c-.06.93-.11,1.83-.11,2.69a1.68,1.68,0,1,1-3.36,0c0-.86.07-1.86.13-2.9v-.17h0v-.15h0v-.16h0v-.17h0v-.17h0v-.17h0v-.32h0v-.16h0v-.17h0v-.14h0v-.17h0v-.17h0v-.07c.08-2.84-.23-5.46-2.47-5.87H51.29a1.49,1.49,0,0,1-.43-.06v6.49a1.68,1.68,0,0,1-3.36,0v-8.2c0-2.32-2.3-3.32-4.45-3.32a3.85,3.85,0,0,0-1.74.42,5,5,0,0,0-2.26,3ZM35.29,70l-1.58-.65a19.89,19.89,0,0,0-2.2-.75l2.41,2,1.37,1.12V70Z" />
  </svg>
);
const PartsIcon = createSvgIcon(
  <svg
    height="22px"
    width="22px"
    version="1.1"
    id="Capa_1"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 231.233 231.233"
  >
    <path
      d="M230.505,102.78c-0.365-3.25-4.156-5.695-7.434-5.695c-10.594,0-19.996-6.218-23.939-15.842
      c-4.025-9.855-1.428-21.346,6.465-28.587c2.486-2.273,2.789-6.079,0.705-8.721c-5.424-6.886-11.586-13.107-18.316-18.498
      c-2.633-2.112-6.502-1.818-8.787,0.711c-6.891,7.632-19.27,10.468-28.836,6.477c-9.951-4.187-16.232-14.274-15.615-25.101
      c0.203-3.403-2.285-6.36-5.676-6.755c-8.637-1-17.35-1.029-26.012-0.068c-3.348,0.37-5.834,3.257-5.723,6.617
      c0.375,10.721-5.977,20.63-15.832,24.667c-9.451,3.861-21.744,1.046-28.621-6.519c-2.273-2.492-6.074-2.798-8.725-0.731
      c-6.928,5.437-13.229,11.662-18.703,18.492c-2.133,2.655-1.818,6.503,0.689,8.784c8.049,7.289,10.644,18.879,6.465,28.849
      c-3.99,9.505-13.859,15.628-25.156,15.628c-3.666-0.118-6.275,2.345-6.68,5.679c-1.016,8.683-1.027,17.535-0.049,26.289
      c0.365,3.264,4.268,5.688,7.582,5.688c10.07-0.256,19.732,5.974,23.791,15.841c4.039,9.855,1.439,21.341-6.467,28.592
      c-2.473,2.273-2.789,6.07-0.701,8.709c5.369,6.843,11.537,13.068,18.287,18.505c2.65,2.134,6.504,1.835,8.801-0.697
      c6.918-7.65,19.295-10.481,28.822-6.482c9.98,4.176,16.258,14.262,15.645,25.092c-0.201,3.403,2.293,6.369,5.672,6.755
      c4.42,0.517,8.863,0.773,13.32,0.773c4.23,0,8.461-0.231,12.692-0.702c3.352-0.37,5.834-3.26,5.721-6.621
      c-0.387-10.716,5.979-20.626,15.822-24.655c9.514-3.886,21.752-1.042,28.633,6.512c2.285,2.487,6.063,2.789,8.725,0.73
      c6.916-5.423,13.205-11.645,18.703-18.493c2.135-2.65,1.832-6.503-0.689-8.788c-8.047-7.284-10.656-18.879-6.477-28.839
      c3.928-9.377,13.43-15.673,23.65-15.673l1.43,0.038c3.318,0.269,6.367-2.286,6.768-5.671
      C231.476,120.379,231.487,111.537,230.505,102.78z M115.616,182.27c-36.813,0-66.654-29.841-66.654-66.653
      s29.842-66.653,66.654-66.653s66.654,29.841,66.654,66.653c0,12.495-3.445,24.182-9.428,34.176l-29.186-29.187
      c2.113-4.982,3.229-10.383,3.228-15.957c0-10.915-4.251-21.176-11.97-28.893c-7.717-7.717-17.978-11.967-28.891-11.967
      c-3.642,0-7.267,0.484-10.774,1.439c-1.536,0.419-2.792,1.685-3.201,3.224c-0.418,1.574,0.053,3.187,1.283,4.418
      c0,0,14.409,14.52,19.23,19.34c0.505,0.505,0.504,1.71,0.433,2.144l-0.045,0.317c-0.486,5.3-1.423,11.662-2.196,14.107
      c-0.104,0.103-0.202,0.19-0.308,0.296c-0.111,0.111-0.213,0.218-0.32,0.328c-2.477,0.795-8.937,1.743-14.321,2.225l0.001-0.029
      l-0.242,0.061c-0.043,0.005-0.123,0.011-0.229,0.011c-0.582,0-1.438-0.163-2.216-0.94c-5.018-5.018-18.862-18.763-18.862-18.763
      c-1.242-1.238-2.516-1.498-3.365-1.498c-1.979,0-3.751,1.43-4.309,3.481c-3.811,14.103,0.229,29.273,10.546,39.591
      c7.719,7.718,17.981,11.968,28.896,11.968c5.574,0,10.975-1.115,15.956-3.228l29.503,29.503
      C141.125,178.412,128.825,182.27,115.616,182.27z"
    />
  </svg>
);

const OpportunityIcon = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    height="25"
    viewBox="0 96 960 960"
    width="25"
  >
    <path d="M240 976V804q-57-52-88.5-121.5T120 536q0-150 105-255t255-105q125 0 221.5 73.5T827 441l55 218q4 14-5 25.5T853 696h-93v140q0 24.75-17.625 42.375T700 896H600v80h-60V836h160V636h114l-45-180q-24-97-105-158.5T480 236q-125 0-212.5 86.5T180 533.54q0 64.417 26.324 122.392Q232.649 713.908 281 759l19 18v199h-60Zm257-370Zm-17 130q17 0 28.5-11.5T520 696q0-17-11.5-28.5T480 656q-17 0-28.5 11.5T440 696q0 17 11.5 28.5T480 736Zm-30-128h61q0-25 6.5-40.5T544 530q18-20 35-40.5t17-53.5q0-42-32.5-71T483 336q-40 0-72.5 23T365 419l55 23q7-22 24.5-35.5T483 393q22 0 36.5 12t14.5 31q0 21-12.5 37.5T492 507q-20 21-31 42t-11 59Z" />
  </svg>
);
const TableViewIcon = createSvgIcon(
  <path d="M19,7H9C7.9,7,7,7.9,7,9v10c0,1.1,0.9,2,2,2h10c1.1,0,2-0.9,2-2V9C21,7.9,20.1,7,19,7z M19,9v2H9V9H19z M13,15v-2h2v2H13z M15,17v2h-2v-2H15z M11,15H9v-2h2V15z M17,13h2v2h-2V13z M9,17h2v2H9V17z M17,19v-2h2v2H17z M6,17H5c-1.1,0-2-0.9-2-2V5 c0-1.1,0.9-2,2-2h10c1.1,0,2,0.9,2,2v1h-2V5H5v10h1V17z" />,
  'ContentCut'
);
export default [
  {
    // subheader: 'Pages',

    items: [
      // {
      //   title: 'Home',
      //   href: '/Home'
      // },
      // {
      //   title: 'Favorites',
      //   href: '/MyFavorites',
      //   icon: StarIcon
      // },
      {
        title: 'Expand/Collapse All',
        icon: ImportExportIcon,
        href: '#'
      },

      // {
      //   title: 'Customer Pay',
      //   //icon: AttachMoneyIcon,
      //   items: [
      //     {
      //       title: 'Summary Overview',
      //       href: '/CPOverview',
      //       icon: AspectRatioIcon
      //     },
      //     {
      //       title: 'KPI Report 1  \n Individual Advisor',
      //       href: '/KpiReport',
      //       icon: AssessmentOutlinedIcon
      //     }
      //   ]
      // },
      {
        title: 'CP Summary Overview',
        href: '/CPOverview',
        icon: AspectRatioIcon
      },

      {
        title: 'Labor',
        //icon: AttachMoneyIcon,
        icon: PersonIcon,
        items: [
          {
            title: 'Labor Overview',
            href: '/CPLaborOverview',
            icon: AspectRatioIcon
          },
          {
            title: 'Labor Work Mix',
            href: '/LaborWorkMixAnalysis',
            icon: PeopleAltOutlinedIcon
          },
          // {
          //   title: 'Labor Rates',
          //   href: '/LaborTranches',
          //   icon: AccountTreeIcon
          // },
          {
            title: 'Labor Work Mix \n Other',
            href: '/WorkMixVolume',
            icon: PeopleAltOutlinedIcon,
            params: {
              category: 'labor'
            }
          },
          {
            title: 'Labor Work Mix \n Other ',
            href: '/2.4.0/WorkMixVolume',
            icon: PeopleAltOutlinedIcon,
            params: {
              category: 'labor'
            }
          },
          {
            title: 'Scatter Plot - Labor \n  Jobs / Hours / ELR',
            href: '/LaborItemization',
            icon: AllInboxIcon
          },
          {
            title: '“What If” Opportunity \n Hrs Per RO & Labor GP%',
            href: '/LaborGrossAndVolumeOpportunity',
            icon: OpportunityIcon
          },
          {
            title: '“What If” Opportunity \n Effective Labor Rate',
            href: '/CPELROpportunity',
            icon: OpportunityIcon
          },
          {
            title: 'Repair Labor \n Target Misses',
            href: '/LaborMisses',
            icon: ListAltIcon
          },
          {
            title: 'Repair Labor \n Target Misses ',
            href: '/LaborGridMisses',
            icon: ListAltIcon
          }
        ]
      },
      {
        title: 'Parts',
        //icon: AttachMoneyIcon,
        icon: PartsIcon,
        items: [
          {
            title: 'Parts Overview',
            href: '/CPPartsOverview',
            icon: AspectRatioIcon
          },
          {
            title: 'Parts Work Mix',
            href: '/PartsWorkMixAnalysis',
            icon: PermDataSettingOutlinedIcon
          },
          {
            title: 'Parts Work Mix \n Other',
            href: '/WorkMixVolumeParts',
            icon: PermDataSettingOutlinedIcon,
            params: {
              category: 'parts'
            }
          },
          // {
          //   title: 'Parts Markup',
          //   href: '/PartsTranches',
          //   icon: AccountTreeIcon
          // },

          {
            title: 'Scatter Plot - Parts \n Cost / Jobs / Markup',
            href: '/PartsItemization',
            icon: AllInboxIcon
          },
          {
            title: '“What If” Opportunity \n Hrs Per RO & Parts GP%',
            href: '/PartsGrossAndVolumeOpportunity',
            icon: OpportunityIcon
          },
          {
            title: 'Repair Parts \n Target Misses',
            href: '/PartsMisses',
            icon: ListAltIcon
          },
          {
            title: 'Repair Parts \n Target Misses ',
            href: '/PartsTargetMisses',
            icon: ListAltIcon
          }
        ]
      },
      {
        title: 'Reports',
        //icon: AttachMoneyIcon,
        icon: LibraryBooksIcon,
        items: [
          {
            title: 'Reports Saved',
            icon: AssessmentOutlinedIcon,
            href: '/ReportSaved'
          },
          // {
          //   title: 'Saved Reports',
          //   href: '/SavedKpiReport',
          //   icon: AssessmentOutlinedIcon
          // },
          {
            title: 'KPI Advisor',
            href: '/KpiReport',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Advisor Comparative',
            href: '/KPIReportComparative',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Tech Comparative',
            href: '/KPIReportTechComparative',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Store Comparative',
            href: '/KPIReportStoreComparative',
            icon: AssessmentOutlinedIcon
          },

          // },
          // {
          //   title: 'KPI Report 2 ',
          //   href: '/KpiReportAll',
          //   icon: AssessmentOutlinedIcon
          // },

          {
            title: 'Trend Report',
            href: '/TrendReport',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'Job Count Grid',
            href: '/JobCountGrid',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Graphics',
            href: '/KpiGraphics',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'MPI Stats',
            href: '/MPIStats',
            icon: AssessmentOutlinedIcon
          }
        ]
      },
      {
        title: 'Advisor Metrics',
        href: '/ServiceAdvisorPerformance',
        icon: PeopleIcon,
        params: {
          drillDownType: 'ServiceAdvisorEfficiency'
        }
      },
      {
        title: 'Tech Metrics',
        href: '/TechnicianPerformance',
        icon: HowToRegIcon,
        params: {
          drillDownType: 'TechnicianEfficiency'
        }
      },
      {
        title: 'Discount Metrics',
        icon: TrendingDownIcon,
        href: '/Discounts'
      },
      {
        title: 'Special Metrics',
        icon: EqualizerIcon,
        href: '/SpecialMetrics'
      },

      {
        title: 'Reference / Setups',
        //icon: AttachMoneyIcon,
        items: [
          {
            title: 'Glossary',
            icon: HelpIcon,
            href: '/Glossary'
          },
          {
            title: 'Service Advisors',
            href: '/ServiceAdvisors',
            icon: PeopleIcon
          },
          {
            title: 'Technicians',
            href: '/Technicians',
            icon: HowToRegIcon
          },
          {
            title: 'Makes',
            href: '/Makes',
            icon: DirectionsCarIcon
          },
          {
            title: 'Pay Types',
            href: '/PayTypeMaster',
            icon: PaymentIcon
          },
          {
            title: 'Opcode Categorizations',
            href: '/OPcodes',
            icon: BallotIcon
          },
          {
            title: 'Fixed Rate History',
            href: '/FixedRates',
            icon: BallotIcon
          },
          {
            title: 'Opcode Mapping with Statistics',
            href: '/FixedopsMapperOpcodes',
            icon: BallotIcon
          },
          {
            title: 'Fleet Accounts',
            href: '/FleetAccounts',
            icon: AccountTreeIcon
          },
          {
            title: 'Email Setups',
            href: '/Email',
            icon: Email
          },
          {
            title: 'RO Count by Make - Warranty',
            href: '/NewCarWarranty',
            icon: ListAltIcon
          },
          {
            title: 'RO Count by Make - Total',
            href: '/SharedSequencesByMake',
            icon: ListAltIcon
          },

          {
            title: 'Opcodes - MPI',
            href: '/MPIOpcode',
            icon: BallotIcon
          },
          {
            title: 'Opcodes - Menu',
            href: '/MenuOpcode',
            icon: BallotIcon
          },
          {
            title: 'Store Settings',
            href: '/StoreSettings',
            icon: SettingsIcon
          },
          {
            title: 'Goal Settings \n KPI Report',
            icon: SettingsIcon,
            href: '/ScoreCardGoalSetting'
          },
          {
            title: 'Labor Grid(s)',
            icon: AddBoxIcon,
            href: '/LaborGridPricing'
          },
          {
            title: 'Parts Matrix(s)',
            icon: AddBoxIcon,
            href: '/PartsMatrix'
          },
          {
            title: 'Parts Matrix(s) ',
            icon: TableViewIcon,
            href: '/PartsMatrixPricings'
          },

          {
            title: 'Edit History',
            href: '/EditHistory',
            icon: EventNoteOutlinedIcon
          },
          {
            title: 'Labor - Wty Jobs',
            href: '/WarrantyRatesLabor',
            icon: AccountBoxIcon
          },
          {
            title: 'Parts - Wty Jobs',
            href: '/WarrantyMarkupParts',
            icon: DirectionsCarIcon
          },
          {
            title: 'Customer History',
            icon: PersonIcon,
            href: '/CustomerHistory'
          }
        ]
      },
      {
        title: 'Armatus Admin',
        //icon: AssignmentTurnedInIcon,
        items: [
          {
            title: 'Credentials',
            icon: PersonAddIcon,
            href: '/KeycloakUserCreation'
          },
          {
            title: 'Chart Master',
            href: '/ChartMaster',
            icon: PieChartIcon
          },

          {
            title: 'RO Count - Calendar',
            href: '/ROCalendar',
            icon: CalendarTodayIcon
          },
          {
            title: 'Store Assignments',
            href: '/StoreAssignments',
            icon: SettingsIcon
          },
          {
            title: 'Client Report Card',
            icon: LibraryBooksIcon,
            items: [
              {
                title: 'Saved Reports',
                icon: LibraryBooksIcon,
                href: '/SavedReports'
              },
              {
                title: '1 Month',
                icon: LibraryBooksIcon,
                href: '/OneMonthReport'
              },
              {
                title: '3 Month Total',
                icon: LibraryBooksIcon,
                href: '/ThreeMonthReport'
              }
            ]
          },
          {
            title: 'Menu / MPI Setups',
            icon: HowToRegIcon,
            href: '/Mpi'
          },

          {
            title: 'Model Mapping - \n Menu',
            href: '/MenuModelMapping',
            icon: TableViewIcon
          },
          {
            title: 'Model Mapping - \n Grid',
            href: '/GridModelMapping',
            icon: TableViewIcon
          },
          {
            title: 'User Login History',
            href: '/UserLoginHistory',
            icon: EventNoteOutlinedIcon
          },
          {
            title: 'Edit History All',
            href: '/EditHistoryAll',
            icon: EventNoteOutlinedIcon
          },
          {
            title: 'Hide RO - Rules',
            href: '/RoShowHide',
            icon: BallotIcon
          },
          {
            title: 'Store Settings',
            href: '/StoreSettings',
            icon: SettingsIcon
          },

          {
            title: 'Daily Data Imports',
            href: '/DailyDataImports',
            icon: ListAltIcon
          },
          {
            title: 'Labor - Wty Model',
            href: '/WarrantyReferenceLabor',
            icon: BallotIcon
          },
          {
            title: 'Parts - Wty Model',
            href: '/WarrantyReferenceParts',
            icon: CompassCalibrationIcon
          },

          {
            title: 'Detail Summary',
            href: '/RevenueSummary',
            icon: PostAddIcon
          }
          // {
          //   title: 'Labor Grid(s)',
          //   href: '/NewLaborGrid',
          //   icon: AddBoxIcon
          // },
          // {
          //   title: 'Parts Matrix(s)',
          //   href: '/NewPartsMatrix',
          //   icon: AddBoxIcon
          // }
        ]
      }
    ]
  }
];
