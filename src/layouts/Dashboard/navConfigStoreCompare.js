/* eslint-disable react/no-multi-comp */
/* eslint-disable react/display-name */
import BarChartIcon from '@material-ui/icons/Bar<PERSON><PERSON>';
import ShowChartIcon from '@material-ui/icons/ShowChart';
import AttachMoneyIcon from '@material-ui/icons/AttachMoney';

export default [
  {
    // subheader: 'Pages',
    items: [
      {
        title: 'Customer Pay',
        //icon: AttachMoneyIcon,
        items: [
          {
            title: 'Summary Overview',
            href: '/CPOverview',
            icon: ShowChartIcon
          }
        ]
      }
    ]
  }
];
