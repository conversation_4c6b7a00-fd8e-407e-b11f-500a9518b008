import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import Paper from '@material-ui/core/Paper';

import { Link as RouterLink } from 'react-router-dom';
import Typography from '@material-ui/core/Typography';

import {
  Box,
  Container,
  Row,
  Column,
  FooterLink,
  Heading
} from './FooterStyles';
import { version, versionDemo } from 'src/appversion.json';

import { getLaunchDate } from 'src/utils/hasuraServices';
import moment from 'moment';
const useStyles = makeStyles(theme => ({
  root: {
    ...theme.mixins.gutters(),
    // paddingTop: theme.spacing.unit * 2,
    // paddingBottom: theme.spacing.unit * 2,
    // backgroundColor: theme.palette.primary.main,
    borderRadius: '1px',
    boxShadow: 'none'
  },
  listRoot: {
    width: '100%',
    maxWidth: 360
  },
  footer: {
    height: 70,
    marginTop: 'auto',
    width: '100%',
    bottom: '0%',
    position: 'relative',
    // borderTop: 'solid #f08b27 4px',
    // borderBottom: 'solid #f08b27 4px',
    zIndex: '1201',
    // position: 'fixed',
    alignItems: 'center',
    justifyContent: 'space-between',
    display: 'flex',
    alignItems: 'center',
    paddingTop: '0px',
    // backgroundColor: theme.palette.primary.main,
    boxShadow: 'none',
    background: theme.palette.primary.footerGradient
  },
  logo: {
    width: '247px'
  },
  footerLogo: {
    height: '100%',
    width: '90%'
  },
  copyInfo: {
    color: '#fff',
    fontSize: 8,
    fontWeight: 395
  },
  listItems: {
    marginRight: 40,

    lineHeight: 0,
    color: '#fff',
    '@media (max-width: 1920px)': {
      fontSize: 11
    },
    '@media (max-width: 1280px)': {
      fontSize: 9
    },
    '@media (min-width: 2304px)': {
      fontSize: 15
    }
  },
  listItemsText: {
    lineHeight: 0,
    color: '#fff',
    color: '#fff',
    '@media (max-width: 1920px)': {
      fontSize: 11
    },
    '@media (max-width: 1280px)': {
      fontSize: 9
    },
    '@media (min-width: 2304px)': {
      fontSize: 12
    }
  },
  column: {
    display: 'flex',
    flexDirection: 'column',
    textAlign: 'left',
    '@media (max-width: 2304px)': {
      width: '285px',
      marginLeft: '120px'
    },
    '@media (max-width: 1920px)': {
      width: '215px',
      marginLeft: '60px'
    },
    '@media (max-width: 1280px)': {
      width: '200px',
      marginLeft: '60px'
    }
  },
  subcolumn: {
    float: 'left',
    width: '45%'
    // padding: '10px'
    // paddingTop: '0px',
  },
  h: {
    color: 'rgb(0, 61, 107)',
    fontSize: '10px',
    marginTop: '-4px'
  },
  footerHeading: {
    marginBottom: 15,
    color: '#fff',
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 12
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      fontSize: 11
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      fontSize: 15
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  logoBox: {
    textAlign: 'center',
    display: 'grid'
  },
  box: {
    '@media (max-width: 1920px)': {
      width: '40%'
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: '45%'
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: '35%'
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  row: {
    gridTemplateColumns: 'repeat(auto-fill, minmax(185px, 1fr))',

    alignItems: 'center',

    '@media (max-width: 1920px)': {
      display: 'grid',
      gridGap: '20px'
    },
    '@media (max-width: 1280px)': {
      display: 'grid',
      gridGap: '20px'
    },
    '@media (max-width: 2304px)': {
      display: 'flex'
      //gridGap: '200px'
    }
  }
}));

function Footer() {
  const classes = useStyles();
  const [launchDate, setLaunchDate] = useState('');
  var Dealer = process.env.REACT_APP_DEALER;
  useEffect(() => {
    getLaunchDate(result => {
      if (
        result.data.statelessCcPhysicalRwGetLaunchdate.nodes &&
        result.data.statelessCcPhysicalRwGetLaunchdate.nodes[0] != 'null'
      ) {
        setLaunchDate(result.data.statelessCcPhysicalRwGetLaunchdate.nodes[0]);
        localStorage.setItem(
          'launchDate',
          result.data.statelessCcPhysicalRwGetLaunchdate.nodes[0]
        );
      }
    });
  }, []);

  return (
    <Paper className={clsx(classes.root, classes.footer, 'footer-section')}>
      {/* <div className={classes.box}> */}

      <div className={classes.column}>
        {/* <Typography className={classes.footerHeading}>
          Jankowski Ferrari-Maserati
        </Typography>

        <FooterLink>
          <Typography className={classes.listItems}>General Manager</Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink>
        <FooterLink>
          <Typography className={classes.listItems}>
            Fixed Ops Director
          </Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink>
        <FooterLink>
          <Typography className={classes.listItems}>Service Manager</Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink>
        <FooterLink>
          <Typography className={classes.listItems}>Parts Manager</Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink> */}
      </div>
      <div className={classes.logoBox}>
        {/* <RouterLink style={{ cursor: 'default' }}> */}
        <div style={{ cursor: 'default' }}>
          {Dealer === 'Armatus' ? (
            <img
              className={classes.footerLogo}
              alt="Fixed Ops Logo"
              src="/images/logos/armatus-logo-footer.png"
            />
          ) : (
            <img
              alt="Fixed Ops Logo"
              src="/images/logos/logo_netspective.png"
            />
          )}
        </div>
        {/* </RouterLink> */}

        <Typography className={classes.copyInfo}>
          Copyright @ {new Date().getFullYear()} /Armatus Dealer Uplift / All
          Rights Reserved.
          {localStorage.getItem('versionFlag') &&
          localStorage.getItem('versionFlag') == 'TRUE' &&
          version
            ? ' Version ' + version
            : ' Version ' + versionDemo}
          {/* &nbsp;/&nbsp;
          {launchDate && launchDate !== '' && (
            <span>
              Launched On&nbsp;&nbsp;&nbsp;
              {moment(launchDate).format('MM/DD/YY')}
            </span>
          )} */}
        </Typography>
      </div>
      <div className={classes.column}>
        {/* <Typography className={classes.footerHeading}>
          Armatus Fixed Ops Performance Center
        </Typography>

        <FooterLink>
          <Typography className={classes.listItems}>Account</Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink>
        <FooterLink>
          <Typography className={classes.listItems}>Manager</Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink>
        <FooterLink>
          <Typography className={classes.listItems}>Phone</Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink>
        <FooterLink>
          <Typography className={classes.listItems}>Email</Typography>
          <Typography className={classes.listItemsText}>*****</Typography>
        </FooterLink> */}
      </div>

      {/* </div> */}
    </Paper>
  );
}

Footer.propTypes = {
  classes: PropTypes.object.isRequired
};

export default Footer;
