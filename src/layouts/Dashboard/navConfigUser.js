/* eslint-disable react/no-multi-comp */
/* eslint-disable react/display-name */
import React, { Component } from 'react';
import BarChartIcon from '@material-ui/icons/BarChart';
import ReceiptIcon from '@material-ui/icons/ReceiptOutlined';
import ShowChartIcon from '@material-ui/icons/ShowChart';
import LocalShippingIcon from '@material-ui/icons/LocalShipping';
import GraphicEqIcon from '@material-ui/icons/GraphicEq';
import FolderSpecialIcon from '@material-ui/icons/FolderSpecial';
import AttachMoneyIcon from '@material-ui/icons/AttachMoney';
import NoteIcon from '@material-ui/icons/Note';
import LocalPlayIcon from '@material-ui/icons/LocalPlay';
import PaymentIcon from '@material-ui/icons/Payment';
import AspectRatioIcon from '@material-ui/icons/AspectRatio';
import AllInboxIcon from '@material-ui/icons/AllInbox';
import ListAltIcon from '@material-ui/icons/ListAlt';
import AccountTreeIcon from '@material-ui/icons/AccountTree';
import FilterNoneIcon from '@material-ui/icons/FilterNone';
import AddBoxIcon from '@material-ui/icons/AddBox';
import SupervisorAccountIcon from '@material-ui/icons/SupervisorAccount';
import EmojiPeopleIcon from '@material-ui/icons/EmojiPeople';
import StoreIcon from '@material-ui/icons/Store';
import AssignmentIndIcon from '@material-ui/icons/AssignmentInd';
import HowToRegIcon from '@material-ui/icons/HowToReg';
import AssignmentTurnedInIcon from '@material-ui/icons/AssignmentTurnedIn';
import StarIcon from '@material-ui/icons/Star';
import SearchIcon from '@material-ui/icons/Search';
import SettingsIcon from '@material-ui/icons/Settings';
import PeopleAltOutlinedIcon from '@material-ui/icons/PeopleAltOutlined';
import PermDataSettingOutlinedIcon from '@material-ui/icons/PermDataSettingOutlined';
import MenuBookIcon from '@material-ui/icons/MenuBook';
import TableChartIcon from '@material-ui/icons/TableChart';
import ScreenShareIcon from '@material-ui/icons/ScreenShare';
import NewReleasesIcon from '@material-ui/icons/NewReleases';
import PeopleIcon from '@material-ui/icons/People';
import TrendingDownIcon from '@material-ui/icons/TrendingDown';
import EqualizerIcon from '@material-ui/icons/Equalizer';
import EmojiSymbolsIcon from '@material-ui/icons/EmojiSymbols';
import ImportExportIcon from '@material-ui/icons/ImportExport';
import HelpIcon from '@material-ui/icons/Help';
import AssessmentOutlinedIcon from '@material-ui/icons/AssessmentOutlined';
import EventNoteOutlinedIcon from '@material-ui/icons/EventNoteOutlined';
import BallotIcon from '@material-ui/icons/Ballot';
import DirectionsCarIcon from '@material-ui/icons/DirectionsCar';
import PersonIcon from '@material-ui/icons/Person';
import BuildIcon from '@material-ui/icons/Build';
import LibraryBooksIcon from '@material-ui/icons/LibraryBooks';
import AccountBoxIcon from '@material-ui/icons/AccountBox';
import PostAddIcon from '@material-ui/icons/PostAdd';
import CompassCalibrationIcon from '@material-ui/icons/CompassCalibration';
import createSvgIcon from '@material-ui/icons/utils/createSvgIcon';
import Email from '@material-ui/icons/Email';
const PartsIcon = createSvgIcon(
  <svg
    height="22px"
    width="22px"
    version="1.1"
    id="Capa_1"
    xmlns="http://www.w3.org/2000/svg"
    viewBox="0 0 231.233 231.233"
  >
    <path
      d="M230.505,102.78c-0.365-3.25-4.156-5.695-7.434-5.695c-10.594,0-19.996-6.218-23.939-15.842
      c-4.025-9.855-1.428-21.346,6.465-28.587c2.486-2.273,2.789-6.079,0.705-8.721c-5.424-6.886-11.586-13.107-18.316-18.498
      c-2.633-2.112-6.502-1.818-8.787,0.711c-6.891,7.632-19.27,10.468-28.836,6.477c-9.951-4.187-16.232-14.274-15.615-25.101
      c0.203-3.403-2.285-6.36-5.676-6.755c-8.637-1-17.35-1.029-26.012-0.068c-3.348,0.37-5.834,3.257-5.723,6.617
      c0.375,10.721-5.977,20.63-15.832,24.667c-9.451,3.861-21.744,1.046-28.621-6.519c-2.273-2.492-6.074-2.798-8.725-0.731
      c-6.928,5.437-13.229,11.662-18.703,18.492c-2.133,2.655-1.818,6.503,0.689,8.784c8.049,7.289,10.644,18.879,6.465,28.849
      c-3.99,9.505-13.859,15.628-25.156,15.628c-3.666-0.118-6.275,2.345-6.68,5.679c-1.016,8.683-1.027,17.535-0.049,26.289
      c0.365,3.264,4.268,5.688,7.582,5.688c10.07-0.256,19.732,5.974,23.791,15.841c4.039,9.855,1.439,21.341-6.467,28.592
      c-2.473,2.273-2.789,6.07-0.701,8.709c5.369,6.843,11.537,13.068,18.287,18.505c2.65,2.134,6.504,1.835,8.801-0.697
      c6.918-7.65,19.295-10.481,28.822-6.482c9.98,4.176,16.258,14.262,15.645,25.092c-0.201,3.403,2.293,6.369,5.672,6.755
      c4.42,0.517,8.863,0.773,13.32,0.773c4.23,0,8.461-0.231,12.692-0.702c3.352-0.37,5.834-3.26,5.721-6.621
      c-0.387-10.716,5.979-20.626,15.822-24.655c9.514-3.886,21.752-1.042,28.633,6.512c2.285,2.487,6.063,2.789,8.725,0.73
      c6.916-5.423,13.205-11.645,18.703-18.493c2.135-2.65,1.832-6.503-0.689-8.788c-8.047-7.284-10.656-18.879-6.477-28.839
      c3.928-9.377,13.43-15.673,23.65-15.673l1.43,0.038c3.318,0.269,6.367-2.286,6.768-5.671
      C231.476,120.379,231.487,111.537,230.505,102.78z M115.616,182.27c-36.813,0-66.654-29.841-66.654-66.653
      s29.842-66.653,66.654-66.653s66.654,29.841,66.654,66.653c0,12.495-3.445,24.182-9.428,34.176l-29.186-29.187
      c2.113-4.982,3.229-10.383,3.228-15.957c0-10.915-4.251-21.176-11.97-28.893c-7.717-7.717-17.978-11.967-28.891-11.967
      c-3.642,0-7.267,0.484-10.774,1.439c-1.536,0.419-2.792,1.685-3.201,3.224c-0.418,1.574,0.053,3.187,1.283,4.418
      c0,0,14.409,14.52,19.23,19.34c0.505,0.505,0.504,1.71,0.433,2.144l-0.045,0.317c-0.486,5.3-1.423,11.662-2.196,14.107
      c-0.104,0.103-0.202,0.19-0.308,0.296c-0.111,0.111-0.213,0.218-0.32,0.328c-2.477,0.795-8.937,1.743-14.321,2.225l0.001-0.029
      l-0.242,0.061c-0.043,0.005-0.123,0.011-0.229,0.011c-0.582,0-1.438-0.163-2.216-0.94c-5.018-5.018-18.862-18.763-18.862-18.763
      c-1.242-1.238-2.516-1.498-3.365-1.498c-1.979,0-3.751,1.43-4.309,3.481c-3.811,14.103,0.229,29.273,10.546,39.591
      c7.719,7.718,17.981,11.968,28.896,11.968c5.574,0,10.975-1.115,15.956-3.228l29.503,29.503
      C141.125,178.412,128.825,182.27,115.616,182.27z"
    />
  </svg>
);

const OpportunityIcon = createSvgIcon(
  <svg
    xmlns="http://www.w3.org/2000/svg"
    height="25"
    viewBox="0 96 960 960"
    width="25"
  >
    <path d="M240 976V804q-57-52-88.5-121.5T120 536q0-150 105-255t255-105q125 0 221.5 73.5T827 441l55 218q4 14-5 25.5T853 696h-93v140q0 24.75-17.625 42.375T700 896H600v80h-60V836h160V636h114l-45-180q-24-97-105-158.5T480 236q-125 0-212.5 86.5T180 533.54q0 64.417 26.324 122.392Q232.649 713.908 281 759l19 18v199h-60Zm257-370Zm-17 130q17 0 28.5-11.5T520 696q0-17-11.5-28.5T480 656q-17 0-28.5 11.5T440 696q0 17 11.5 28.5T480 736Zm-30-128h61q0-25 6.5-40.5T544 530q18-20 35-40.5t17-53.5q0-42-32.5-71T483 336q-40 0-72.5 23T365 419l55 23q7-22 24.5-35.5T483 393q22 0 36.5 12t14.5 31q0 21-12.5 37.5T492 507q-20 21-31 42t-11 59Z" />
  </svg>
);
const TableViewIcon = createSvgIcon(
  <path d="M19,7H9C7.9,7,7,7.9,7,9v10c0,1.1,0.9,2,2,2h10c1.1,0,2-0.9,2-2V9C21,7.9,20.1,7,19,7z M19,9v2H9V9H19z M13,15v-2h2v2H13z M15,17v2h-2v-2H15z M11,15H9v-2h2V15z M17,13h2v2h-2V13z M9,17h2v2H9V17z M17,19v-2h2v2H17z M6,17H5c-1.1,0-2-0.9-2-2V5 c0-1.1,0.9-2,2-2h10c1.1,0,2,0.9,2,2v1h-2V5H5v10h1V17z" />,
  'ContentCut'
);
export default [
  {
    // subheader: 'Pages',
    items: [
      // {
      //   title: 'Home',
      //   href: '/Home'
      // },
      // {
      //   title: 'Favorites',
      //   href: '/MyFavorites',
      //   icon: StarIcon
      // },
      {
        title: 'Expand/Collapse All',
        icon: ImportExportIcon,
        href: '#'
      },
      // {
      //   title: 'Customer Pay',
      //   //icon: AttachMoneyIcon,
      //   items: [
      //     {
      //       title: 'Summary Overview',
      //       href: '/CPOverview',
      //       icon: AspectRatioIcon
      //     },
      //     {
      //       title: 'KPI Report 1  \n Individual Advisor',
      //       href: '/KpiReport',
      //       icon: AssessmentOutlinedIcon
      //     }
      //   ]
      // },
      {
        title: 'CP Summary Overview',
        href: '/CPOverview',
        icon: AspectRatioIcon
      },
      {
        title: 'Labor',
        //icon: AttachMoneyIcon,
        icon: PersonIcon,
        items: [
          {
            title: 'Labor Overview',
            href: '/CPLaborOverview',
            icon: AspectRatioIcon
          },
          {
            title: 'Labor Work Mix',
            href: '/LaborWorkMixAnalysis',
            icon: PeopleAltOutlinedIcon
          },
          {
            title: 'Labor Work Mix \n Other',
            href: '/WorkMixVolume',
            icon: PeopleAltOutlinedIcon,
            params: {
              category: 'labor'
            }
          },
          {
            title: 'Labor Work Mix \n Other ',
            href: '/2.4.0/WorkMixVolume',
            icon: PeopleAltOutlinedIcon,
            params: {
              category: 'labor'
            }
          },
          {
            title: 'Scatter Plot - Labor \n  Jobs / Hours / ELR',
            href: '/LaborItemization',
            icon: AllInboxIcon
          },
          {
            title: '“What If” Opportunity \n Hrs Per RO & Labor GP%',
            href: '/LaborGrossAndVolumeOpportunity',
            icon: OpportunityIcon
          },
          {
            title: '“What If” Opportunity \n Effective Labor Rate',
            href: '/CPELROpportunity',
            icon: OpportunityIcon
          },
          {
            title: 'Repair Labor \n Target Misses',
            href: '/LaborMisses',
            icon: ListAltIcon
          },
          {
            title: 'Repair Labor \n Target Misses ',
            href: '/LaborGridMisses',
            icon: ListAltIcon
          }
        ]
      },
      {
        title: 'Parts',
        //icon: AttachMoneyIcon,
        icon: PartsIcon,
        items: [
          {
            title: 'Parts Overview',
            href: '/CPPartsOverview',
            icon: AspectRatioIcon
          },
          {
            title: 'Parts Work Mix',
            href: '/PartsWorkMixAnalysis',
            icon: PermDataSettingOutlinedIcon
          },
          {
            title: 'Parts Work Mix \n Other',
            href: '/WorkMixVolumeParts',
            icon: PermDataSettingOutlinedIcon,
            params: {
              category: 'parts'
            }
          },
          {
            title: 'Scatter Plot - Parts \n Cost / Jobs / Markup',
            href: '/PartsItemization',
            icon: AllInboxIcon
          },
          {
            title: '“What If” Opportunity \n Hrs Per RO & Parts GP%',
            href: '/PartsGrossAndVolumeOpportunity',
            icon: OpportunityIcon
          },
          {
            title: 'Repair Parts \n Target Misses',
            href: '/PartsMisses',
            icon: ListAltIcon
          },
          {
            title: 'Repair Parts \n Target Misses ',
            href: '/PartsTargetMisses',
            icon: ListAltIcon
          }
        ]
      },
      {
        title: 'Reports',
        //icon: AttachMoneyIcon,
        icon: LibraryBooksIcon,
        items: [
          {
            title: 'Reports Saved',
            icon: AssessmentOutlinedIcon,
            href: '/ReportSaved'
          },
          {
            title: 'KPI Advisor',
            href: '/KpiReport',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Advisor Comparative',
            href: '/KPIReportComparative',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Tech Comparative',
            href: '/KPIReportTechComparative',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Store Comparative',
            href: '/KPIReportStoreComparative',
            icon: AssessmentOutlinedIcon
          },

          {
            title: 'Trend Report',
            href: '/TrendReport',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'Job Count Grid',
            href: '/JobCountGrid',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'KPI Graphics',
            href: '/KpiGraphics',
            icon: AssessmentOutlinedIcon
          },
          {
            title: 'MPI Stats',
            href: '/MPIStats',
            icon: AssessmentOutlinedIcon
          }
        ]
      },
      {
        title: 'Advisor Metrics',
        href: '/ServiceAdvisorPerformance',
        icon: PeopleIcon,
        params: {
          drillDownType: 'ServiceAdvisorEfficiency'
        }
      },
      {
        title: 'Tech Metrics',
        href: '/TechnicianPerformance',
        icon: HowToRegIcon,
        params: {
          drillDownType: 'TechnicianEfficiency'
        }
      },

      {
        title: 'Discount Metrics',
        icon: TrendingDownIcon,
        href: '/Discounts'
      },
      {
        title: 'Special Metrics',
        icon: EqualizerIcon,
        href: '/SpecialMetrics'
      },

      {
        title: 'Reference / Setups',
        //icon: AttachMoneyIcon,
        items: [
          {
            title: 'Glossary',
            icon: HelpIcon,
            href: '/Glossary'
          },
          {
            title: 'Service Advisors',
            href: '/ServiceAdvisors',
            icon: PeopleIcon
          },
          {
            title: 'Technicians',
            href: '/Technicians',
            icon: HowToRegIcon
          },
          {
            title: 'Makes',
            href: '/Makes',
            icon: DirectionsCarIcon
          },
          {
            title: 'Pay Types',
            href: '/PayTypeMaster',
            icon: PaymentIcon
          },
          {
            title: 'Opcode Categorizations',
            href: '/OPcodes',
            icon: BallotIcon
          },
          {
            title: 'Fixed Rate History',
            href: '/FixedRates',
            icon: BallotIcon
          },
          {
            title: 'Opcode Mapping with Statistics',
            href: '/FixedopsMapperOpcodes',
            icon: BallotIcon
          },
          {
            title: 'Fleet Accounts',
            href: '/FleetAccounts',
            icon: AccountTreeIcon
          },
          {
            title: 'Email Setups',
            href: '/Email',
            icon: Email
          },
          {
            title: 'RO Count by Make - Warranty',
            href: '/NewCarWarranty',
            icon: ListAltIcon
          },
          {
            title: 'RO Count by Make - Total',
            href: '/SharedSequencesByMake',
            icon: ListAltIcon
          },

          // {
          //   title: 'Labor Work Mix - Other Category',
          //   href: '/WorkMixVolume',
          //   icon: EmojiPeopleIcon,
          //   params: {
          //     category: 'labor'
          //   }
          // },
          // {
          //   title: 'Parts Work Mix - Other Category',
          //   href: '/WorkMixVolumeParts',
          //   icon: SettingsIcon,
          //   params: {
          //     category: 'parts'
          //   }
          // },

          {
            title: 'Opcodes - MPI',
            href: '/MPIOpcode',
            icon: BallotIcon
          },
          {
            title: 'Opcodes - Menu',
            href: '/MenuOpcode',
            icon: BallotIcon
          },
          {
            title: 'Store Settings',
            href: '/StoreSettings',
            icon: SettingsIcon
          },
          {
            title: 'Goal Settings \n KPI Report',
            icon: SettingsIcon,
            href: '/ScoreCardGoalSetting'
          },
          {
            title: 'Labor Grid(s)',
            icon: TableViewIcon,
            href: '/LaborGridPricing'
          },
          {
            title: 'Parts Matrix(s)',
            icon: TableViewIcon,
            href: '/PartsMatrix'
          },
          {
            title: 'Parts Matrix(s) ',
            icon: TableViewIcon,
            href: '/PartsMatrixPricings'
          },
          {
            title: 'User Login History',
            href: '/UserLoginHistory',
            icon: EventNoteOutlinedIcon
          },
          // {
          //   title: 'Daily Data Imports',
          //   href: '/DailyDataImports',
          //   icon: ListAltIcon
          // },
          {
            title: 'Edit History',
            href: '/EditHistory',
            icon: EventNoteOutlinedIcon
          },
          {
            title: 'Labor - Wty Jobs',
            href: '/WarrantyRatesLabor',
            icon: AccountBoxIcon
          },
          {
            title: 'Parts - Wty Jobs',
            href: '/WarrantyMarkupParts',
            icon: DirectionsCarIcon
          },
          {
            title: 'Customer History',
            icon: PersonIcon,
            href: '/CustomerHistory'
          }
        ]
      }
    ]
  }
];
