#navBarDiv nav::-webkit-scrollbar-button:single-button {
  background-color: #cdd2d6;
  display: block;
  background-size: 10px;
  background-repeat: no-repeat;
}
#navBarDiv nav::-webkit-scrollbar-button:single-button:vertical:decrement {
  height: 12px;
  width: 12px;
  background-position: center 4px;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='rgb(96, 96, 96)'><polygon points='50,00 0,50 100,50'/></svg>");
}

#navBarDiv
  nav::-webkit-scrollbar-button:single-button:vertical:decrement:hover {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='rgb(112, 112, 112)'><polygon points='50,00 0,50 100,50'/></svg>");
}

#navBarDiv
  nav::-webkit-scrollbar-button:single-button:vertical:decrement:active {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='rgb(128, 128, 128)'><polygon points='50,00 0,50 100,50'/></svg>");
}

/* Down */
#navBarDiv nav::-webkit-scrollbar-button:single-button:vertical:increment {
  height: 12px;
  width: 12px;
  background-position: center 2px;
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='rgb(96, 96, 96)'><polygon points='0,0 100,0 50,50'/></svg>");
}

#navBarDiv
  nav::-webkit-scrollbar-button:single-button:vertical:increment:hover {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='rgb(112, 112, 112)'><polygon points='0,0 100,0 50,50'/></svg>");
}

#navBarDiv
  nav::-webkit-scrollbar-button:single-button:vertical:increment:active {
  background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='rgb(128, 128, 128)'><polygon points='0,0 100,0 50,50'/></svg>");
}

#navBarDiv ul.MuiList-root {
  width: 100%;
}
#navBarDiv li.MuiListItem-root {
  /* width: 97%; */
  width: 98%;
}
#mpi-stats-icons #export-to-pdf i,
#mpi-stats-icons #export-to-excel i {
  font-size: 17px !important;
}
/*Main title*/
h4.main-title {
  margin-top: 0;
  font-size: 18px;
  color: #242f48;
}

/*Field Label*/
h6.field-label {
  color: #031b4e;
  font-size: 14px;
  font-weight: 600;
}

/*Date as of*/
p.date-asof {
  color: #7987a1;
  font-size: 12px;
}

/*Date as of icon*/
button.reset-layout-icon {
  color: #013e6c;
}

/*Annual Opportunity*/
.annual-opportunity-block,
.kpi-soldhrs-block,
.kpi-soldhrs-block-extended {
  margin-top: 10px !important;
  justify-content: left !important;
}
.annual-opportunity-block button {
  width: 80px;
  height: 48px;
}
.kpi-soldhrs-block button,
.kpi-soldhrs-block-extended button {
  width: 113px;
  height: 63px;
  margin-left: -17px;
}
.annual-opportunity-block .MuiPaper-rounded {
  padding: 5px;
  width: 190px;
  margin-right: 10px;
  max-width: 190px;
}
.annual-opportunity-block h6 {
  font-size: 13px;
  color: #003d6b;
  margin-bottom: 3px;
  line-height: 19px;
}
.annual-opportunity-block h5 {
  font-size: 18px;
  line-height: 14px;
}
.annual-opportunity-block .MuiGrid-item,
.kpi-soldhrs-block .MuiGrid-item,
.kpi-soldhrs-block-extended .MuiGrid-item {
  padding-top: 1px !important;
}
.annual-opportunity-block,
.kpi-soldhrs-block,
.kpi-soldhrs-block-extended {
  margin-bottom: 10px !important;
}
.diagram-section.MuiGrid-container {
  margin-top: 0 !important;
}
/*Diagram*/
.diagram-section .MuiCardHeader-content span {
  display: inline-block;
  color: #031b4e;
  font-size: 14px;
  font-weight: 600;
}
.diagram-section .MuiCardHeader-content span:nth-child(2) {
  margin-left: 20px;
  color: #7987a1 !important;
}
.diagram-section .MuiCardHeader-content span:nth-child(2) span {
  color: #7987a1 !important;
}
.diagram-section .MuiCardHeader-content {
  text-align: left;
}
.diagram-section .MuiCardHeader-action svg {
  width: 21px !important;
}

button.orange-btn {
  min-height: 37px;
  box-shadow: none;
}
button.orange-btn:hover {
  background-color: #efab68;
  box-shadow: none;
}
.annual-opportunity-block .MuiPaper-root {
  box-shadow: none;
  border: 1px solid #003d6b;
}
.diagram-section.custom-diagram-section .MuiCard-root {
  position: relative !important;
  width: auto !important;
  height: 350px !important;
  min-height: 100% !important;
}
.diagram-section .MuiCardHeader-root {
  border-bottom: 1px solid #003d6b !important;
  box-shadow: none;
}
.diagram-section.custom-diagram-section .MuiCard-root {
  position: relative !important;
  width: auto !important;
  height: 350px !important;
  min-height: 100% !important;
}
.diagram-section hr {
  display: none;
}
.diagram-section .MuiCardHeader-root {
  padding: 7px 15px !important;
}
.diagram-section .MuiCardHeader-action button {
  position: relative;
  top: 4px;
}
.diagram-section .MuiCardContent-root {
  padding: 10px !important;
}
.footer-section img {
  width: 55% !important;
}
.sub-title {
  font-size: 16px;
  color: #242f48;
  font-weight: 600;
}

/*repair-order-details*/
.repair-order-details h6 {
  font-size: 13px !important;
  color: #031b4e !important;
  font-weight: 600 !important;
}
.repair-order-details p,
.repair-order-details-advisor p {
  color: #7987a1;
  font-size: 13px;
  /* text-transform: capitalize; */
  font-weight: 500;
  text-align: left !important;
}
.repair-order-details-vin p {
  color: #7987a1;
  font-size: 13px;
  text-transform: capitalize;
  font-weight: 500;
  text-align: left !important;
}

.repair-order-details-mileage p {
  color: #7987a1;
  font-size: 13px;
  text-transform: capitalize;
  font-weight: 500;
  text-align: left !important;
  margin-left: -3px !important;
}

.repair-order-details-vin .MuiGrid-grid-xs-3 {
  /* max-width: 9% !important; */
  max-width: fit-content;
}
.repair-order-details-advisor .MuiGrid-grid-xs-3 {
  /* max-width: 16% !important; */
  max-width: fit-content;
}
/*Repair order details table*/
.repair-order-details-table thead tr {
  background: #dde2ef !important;
}
.repair-order-details-table thead tr th {
  font-size: 13px;
  color: #37374e;
  font-weight: 600;
  line-height: 15px;
  padding: 15px 10px;
}
.repair-order-details-table thead tr th:first-child {
  border-left: 0 !important;
}
.repair-order-details-table tbody tr {
  background-color: #dde2ef !important;
}
.repair-order-details-table tbody tr td {
  padding: 5px 10px;
  font-size: 13px;
  color: #686a7e;
}
.repair-order-details-table tbody tr td p {
  font-size: 13px;
  color: #686a7e;
}
.back-btn {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 8px;
  font-size: 11px;
  border-radius: 3px;
  background: #ee7600 !important;
  box-shadow: none;
  height: 23px;
  position: relative;
  top: -3px;
  min-width: auto !important;
  text-transform: none !important;
}
.back-btn:hover {
  background: #0e5184 !important;
  color: #fff;
  box-shadow: none !important;
}
.repair-order-details-table tbody h6 {
  color: #55576b;
  font-size: 13px;
  margin-bottom: 0 !important;
  line-height: 30px;
}
.repair-order-details-table tr table thead tr {
  background: #c9cdd1 !important;
}
.repair-order-details-table tr table thead th {
  padding: 12px 15px;
  color: #4b4c57;
  font-weight: 500;
}
.repair-order-details-table tr table tbody tr {
  background: #e5eaef !important;
}
.repair-order-details-table tr table tbody tr td,
.repair-order-details-table tr table tbody tr th {
  color: #535460;
  font-size: 13px;
}

.repair-order-details-table button:hover {
  background: #d36b05 !important;
}
.repair-order-details-table button {
  width: auto !important;
  height: 29px !important;
  background: #ee7600 !important;
}
/*Labor Opcode tr title*/
.labor-opcode-title-section td {
  color: #37374e !important;
  font-weight: 600;
}
.last-qtr {
  position: relative;
}
.set-goal {
  margin: 0px 0 15px 0 !important;
}
.last-qtr .MuiGrid-grid-xs-2:first-child,
.set-goal .MuiGrid-grid-xs-2:first-child {
  max-width: 118px;
  flex-basis: 118px;
}
.last-qtr .MuiGrid-grid-xs-2:nth-child(3) {
  max-width: 21% !important;
  flex-basis: 21% !important;
  text-align: right;
  position: absolute;
  right: 0;
}
.last-qtr .MuiGrid-grid-xs-2:nth-child(3) button {
  color: #013e6c;
  font-size: 13px;
  font-weight: 600;
}
.last-qtr .MuiGrid-grid-xs-2:nth-child(3) button svg {
  width: 17px;
  margin-right: 5px;
}
.last-qtr .MuiGrid-grid-xs-2:nth-child(3) button:hover {
  background: none;
  color: #ee7600;
}
.repair-order-details-table tr.MuiTableRow-root.jobline-heading-tr {
  background-color: #003d6b !important;
}
.repair-order-details-table tr.MuiTableRow-root.jobline-heading-tr td h6 {
  color: #fff !important;
  font-size: 14px;
  line-height: 17px;
}
.repair-order-details-table
  tr.MuiTableRow-root.jobline-heading-tr
  td:first-child
  h6 {
  line-height: 30px;
}

/*Job Description*/
.MuiDialog-paper h6 {
  color: #003d6b !important;
  font-size: 17px;
}
ul.job-details {
  padding-left: 15px;
}
ul.job-details li {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-weight: 400;
  line-height: 20px;
  font-size: 14px;
}
ul.job-details li::marker {
  color: #ee7600;
  font-size: 15px;
}
.MuiDialog-paper button:hover,
.MuiDialog-paper button:focus {
  background: none;
}

/*Left nav*/
.leftNav span.MuiButton-label {
  font-size: 14px;
}
.repair-order-details .MuiGrid-grid-xs-2 .MuiGrid-grid-xs-3 {
  width: 210px !important;
  max-width: 100% !important;
  flex-basis: 33%;
}
.repair-order-details .MuiGrid-grid-xs-2 {
  max-width: 190px;
}
a#showXmlButton {
  right: 40px;
  top: 190px;
  z-index: 9;
}
.kpi-summary-block,
.kpi-summary-block-target {
  margin-top: -13px !important;
  padding: 15px !important;
}
.kpi-summary-block h6 {
  /* font-size: 13px; */
  font-size: 13.5px;
  font-size: 13.5px;
  color: #ee7600;
  margin-bottom: 3px;
  font-weight: 600;
  font-weight: 600;
  line-height: 16px;
  text-align: left;
  position: relative;
  /* width: 85% */
  padding-right: 50px;
}
.kpi-summary-block h5 {
  font-size: 16px;
  /* font-size: 14px; */
  margin-bottom: 2px;
  font-weight: 600;
}
.kpi-summary-block .MuiPaper-root,
.kpi-summary-block-target .MuiPaper-root {
  box-shadow: none;
  border: 1px solid #adafb3;
  margin-bottom: 5px;
  height: 84px;
  /* resize: both;
    overflow: hidden; */
}
/* .card_1335 {
  background: #fff;
  border-radius: 3px;
  display: inline-block;
  height: 300px;
  margin: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: none !important;
}

.card_1335 .top {
  display: inline-flex;
  width: 100%;
  height: 220px;
  overflow: hidden;
  justify-content: center;
}

.card_1335 .top img {
  height: 220px;
}

.card_1335 .bottom {
  height: 80px;
  width: 100%;
}

.card_1335 .bottom p {
  text-align: left;
  height: 80px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  padding-left: 20px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #444;
  font-size: 18px;
  font-family: Roboto, sans-serif;
} */

.card-1 {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 32% !important;
}

.card-1:hover {
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  /* cursor: pointer; */
}

/* .card_1336 {
  background: #fff;
  border-radius: 3px;
  display: inline-block;
  height: 300px;
  margin: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: none !important;
}

.card_1336 .top {
  display: inline-flex;
  width: 100%;
  height: 220px;
  overflow: hidden;
  justify-content: center;
}

.card_1336 .top img {
  height: 220px;
}

.card_1336 .bottom {
  height: 80px;
  width: 100%;
}

.card_1336 .bottom p {
  text-align: left;
  height: 80px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  padding-left: 20px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #444;
  font-size: 18px;
  font-family: Roboto, sans-serif;
}

.card_1337 {
  background: #fff;
  border-radius: 3px;
  display: inline-block;
  height: 300px;
  margin: 1rem;
  position: relative;
  overflow: hidden;
  box-shadow: none !important;
}

.card_1337 .top {
  display: inline-flex;
  width: 100%;
  height: 220px;
  overflow: hidden;
  justify-content: center;
}

.card_1337 .top img {
  height: 220px;
}

.card_1337 .bottom {
  height: 80px;
  width: 100%;
}

.card_1337 .bottom p {
  text-align: left;
  height: 80px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  padding-left: 20px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #444;
  font-size: 18px;
  font-family: Roboto, sans-serif;
} */

.card {
  background: #7575752b;
  border-radius: 3px;
  display: inline-block;
  height: 340px;
  margin: 6px;
  position: relative;
  width: 331px;
  overflow: hidden;
  box-shadow: none !important;
}

.card .top,
.card-target .top {
  display: inline-flex;
  width: 100%;
  height: 220px;
  overflow: hidden;
  justify-content: center;
}

.card .top img {
  height: 220px;
}

.card .bottom,
.card-target .bottom {
  height: 80px;
  width: 100%;
}

.card .bottom p,
.card-target .bottom p {
  text-align: left;
  height: 80px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  padding-left: 20px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #444;
  font-size: 18px;
  font-family: Roboto, sans-serif;
}

.daterangepicker .drp-calendar.left .calendar-table {
  margin-top: -7px !important;
}

.kpi-summary-block-target h6 {
  font-size: 12.5px;
  color: #003d6b;
  margin-bottom: 2px;
  font-weight: 500;
  line-height: 16px;
  text-align: left;
  margin-top: 2px;
  position: relative;
  padding-right: 35px;
  /* margin-top: 5px; */
}

.kpi-summary-block-target h5 {
  font-size: 16px;
  margin-bottom: 2px;
  color: #fff;
  font-weight: 500;
}

.card-target {
  background: #ee7600;
  /* background: #ff9000; */
  /* background: #ffa812; */
  border-radius: 3px;
  display: inline-block;
  height: 340px;
  margin: 6px;
  position: relative;
  width: 331px;
  overflow: hidden;
  box-shadow: none !important;
}
@media (max-width: 2304px) {
  .card,
  .card-target {
    width: 331px;
  }
  .chart-footer h6 {
    font-size: 14px;
    margin-right: 10px;
  }
}
@media (max-width: 1920px) {
  .card,
  .card-target {
    width: 380px;
  }
  .chart-footer h6 {
    font-size: 14px;
    margin-right: 10px;
  }
}
@media (max-width: 1280px) {
  .card,
  .card-target {
    width: 435px;
  }
  .chart-footer h6 {
    font-size: 14px;
    margin-right: 10px;
  }
  .workmix-tabs .MuiTab-root {
    min-width: 125px;
    font-size: 12px;
  }
}
@media (max-width: 1440px) {
  .card,
  .card-target {
    width: 385px;
  }
  .chart-footer h6 {
    font-size: 11px;
    margin-right: 10px;
  }
  .workmix-tabs .MuiTab-root {
    min-width: 125px;
    font-size: 12px;
  }
}
h4.main-title-kpi {
  /* margin-top: 10px; */
  margin-top: 15px;
  /* font-size: 18px; */
  font-size: 23px;
  color: #242f48;
  margin-left: 10px;
}
.card-kpi {
  width: 47% !important;
  /* width: 18rem; */
  float: left;
  height: 250px;
}
/* .LineROLtSixtyK1,
.LineROLtSixtyK2,
.LineROLtSixtyK3,
.LaborSale1341 {
  width: 30% !important;
}
.LaborGP1341 {
  width: 15%;
  margin-left: -518px !important;
  margin-top: -288px !important;
  padding-top: 65px !important;
}
.LineROLtSixtyK1 canvas,
.LineROLtSixtyK2 canvas,
.LineROLtSixtyK3 canvas,
.LaborGP1341 canvas,
.LaborSale1341 canvas {
  width: 100% !important;
} */
.iconTarget {
  max-width: 100%;
  max-height: 100%;
  margin-top: -4px;
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
}
/* .card-kpi-1 {
  width: 27% !important;
  float: left;
}
.card-kpi-2 {
  width: 45% !important;
  float: left;
} */
.main-title-kpi .MuiInputBase-formControl {
  height: 30px !important;
  border: 1px solid #97aebd !important;
  border-radius: 3px !important;
  top: 2px;
}
.main-title-kpi .MuiInputBase-formControl fieldset {
  border: 0 !important;
}
.main-title-kpi .MuiSelect-select:focus {
  background-color: transparent !important;
}
/* .kpi-summary-block button,
.kpi-summary-block-target button {
  padding: 0;
  position: relative;
  top: 12px;
  left: 5px;
} */
.kpi-summary-block button svg,
.kpi-summary-block-target button svg {
  width: 15px !important;
  height: 15px !important;
}
.data-div button svg {
  width: 15px !important;
  height: 15px !important;
}
.MuiCardContent-root.kpi-summary-block-target {
  padding: 17px !important;
}
.apply-btn {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 4px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #ee7600 !important;
  box-shadow: none;
  height: 25px;
  width: 45px;
  position: relative;
  /* margin: 0 5px !important; */
  min-width: auto !important;
  text-transform: none !important;
  font-weight: 400 !important;
}
.apply-btn:hover {
  background: #0e5184 !important;
  color: #fff;
  box-shadow: none !important;
}
.apply-btn:disabled {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #f7ba6b !important;
  box-shadow: none;
  height: 25px;
  position: relative;
  /* margin: 0 5px !important; */
  width: 45px;
  min-width: auto !important;
  text-transform: none !important;
  font-weight: 400 !important;
}
.reset-btn,
.job-count-grid-btn,
.reset-btn-fixed-rate {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #ee7600 !important;
  box-shadow: none;
  /* height: 30px; */
  position: relative;
  /* margin: 0 5px !important; */
  min-width: auto !important;
  text-transform: none !important;
  height: 24px;
}
.reset-btn:hover,
.job-count-grid-btn:hover,
.reset-btn-fixed-rate:hover {
  background: #0e5184 !important;
  color: #fff;
  box-shadow: none !important;
}
.reset-btn:disabled {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #f7ba6b !important;
  box-shadow: none;
  height: 30px;
  position: relative;
  /* margin: 0 5px !important; */
  min-width: auto !important;
  text-transform: none !important;
}
.close-btn {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #e53935 !important;
  box-shadow: none;
  height: 35px;
  position: relative;
  /* margin: 0 5px !important; */
  width: 70px;
  min-width: auto !important;
  text-transform: none !important;
}
.close-btn:disabled {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #f7bdbc !important;
  box-shadow: none;
  height: 35px;
  position: relative;
  /* margin: 0 5px !important; */
  width: 70px;
  min-width: auto !important;
  text-transform: none !important;
}
/* .close-btn:hover { 
  background: #e38886 !important;
  color: #fff;
  box-shadow: none !important;
} */
.send-btn {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #43a047 !important;
  box-shadow: none;
  height: 35px;
  position: relative;
  /* margin: 0 5px !important; */
  width: 70px;
  min-width: auto !important;
  text-transform: none !important;
}
.send-btn:disabled {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;
  background: #b2e2b4 !important;
  box-shadow: none;
  height: 35px;
  position: relative;
  /* margin: 0 5px !important; */
  width: 70px;
  min-width: auto !important;
  text-transform: none !important;
}
/* .send-btn:hover {
  background: #91d293 !important;
  color: #fff;
  box-shadow: none !important;
} */
.reset-btn p,
.job-count-grid-btn p {
  color: #fff !important;
  margin-left: 2px;
  font-size: 12px !important;
}
@media (max-width: 1540px) {
  .reset-btn p {
    font-size: 10px !important;
    line-height: 10px !important;
    margin-right: 6px;
  }
  #reset-layout span .MuiSvgIcon-root {
    height: 0.6em !important;
    width: 0.6em !important;
  }
}
.notification-icon {
  top: 7px !important;
  padding: 0 2px !important;
  width: 17px !important;
  height: 17px !important;
  left: 2px;
  min-width: 17px !important;
}
.menuadvi .MuiMenu-paper {
  top: 54px !important;
  /* width: auto !important; */
}

.menuadvi .MuiMenu-paper hr {
  display: none;
}
.menuadvi .MuiMenu-paper p {
  padding: 0 10px;
}
.repair-order-details .single-ro {
  max-width: 15%;
}

.child-div {
  height: 100%;

  /* margin: 0 20px; */
}

.kpi-summary-block img {
  width: 40px !important;
  max-width: 40px !important;
  /* height: 40px; */
}

@media (max-width: 1280px) {
  .container {
    height: 65vh;

    display: flex;
  }
  .container-popup {
    height: 65vh;

    display: flex;
  }
  .child-div-popup {
    height: 100%;
  }
}
@media (max-width: 2304px) {
  .container {
    height: 85vh;

    display: flex;
  }
  .container-popup {
    height: 85vh;

    display: flex;
  }
  .child-div-popup {
    height: 100%;
  }
}
@media (max-width: 2560px) {
  .container {
    height: 85vh;

    display: flex;
  }
  .container-popup {
    height: 85vh;

    display: flex;
  }
  .child-div-popup {
    height: 100%;
  }
}

@media (max-width: 1920px) {
  .container {
    height: 80vh;

    display: flex;
  }
  .container-popup {
    height: 80vh;

    display: flex;
  }
  .child-div-popup {
    height: 100%;
    margin-top: -15%;
  }
}

@media (min-width: 1400px) {
  .container {
    height: 80vh;

    display: flex;
  }
  .container-popup {
    height: 80vh;

    display: flex;
  }
  .child-div-popup {
    height: 100%;
    margin-top: 0px;
  }
}

@media (max-width: 1440px) {
  .container {
    /* height: 75vh; */
    height: 67vh;
    display: flex;
  }
  .container-popup {
    height: 75vh;

    display: flex;
  }
  .child-div-popup {
    height: 112%;
  }
  .kpi-summary-block img {
    width: 37px !important;
    max-width: 37px !important;
    height: 37px;
  }
  .kpi-summary-block h5 {
    font-size: 13.5px;
    margin-bottom: 2px;
    font-weight: 600;
  }
}

/* #canvasMileage {
  width: 230px !important;
  height: 115px !important;
  width: 318px !important;
  height: 134px !important;
} */
/* #canvasMileage {
  width: 300px !important;
  height: 116px !important;
} */
.funnel-pipeline-chart {
  font-size: 12px;
  max-width: 90%;
  /* max-height: 83%; */
  margin-top: 11px;
}
.funnel-pipeline-chart .funnel-pipeline-chart-row {
  padding: 8px 0 !important;
}
.funnel-pipeline-chart .funnel-pipeline-chart-row .funnel-pipeline-chart-title {
  font-size: 12px !important;
}
.highcharts-credits {
  display: none;
}
li.parentListKpi::marker {
  font-size: 1.1rem;
  font-weight: bolder;
}
li.childListKpi {
  margin-left: 10px;
}
ul.tooltipUL {
  margin-left: 7px;
}
li.childListKpi {
  margin-left: 10px;
}
ul.tooltipUL {
  margin-left: 7px;
}
/* li::marker {
  font-size: 1.4rem;
  font-weight: bolder;
} */
.bck-btn,
.bck-btn-kpi {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;

  background: #ee7600 !important;
  box-shadow: none;
  /* height: 30px; */
  height: 24px;
  position: relative;
  margin: 0 5px !important;
  min-width: auto !important;
  text-transform: none !important;
}
.bck-btn:hover,
.bck-btn-kpi:hover {
  background: #0e5184 !important;
  color: #fff;
  box-shadow: none !important;
}
.bck-btn p,
.bck-btn-kpi p {
  color: #fff !important;
  margin-left: 3px;
  font-size: 12px;
}
.bck-btn-kpi {
  margin-top: 10px !important;
}
.tblbody tr:last-child td {
  border-top: 2px solid #5a5a5a !important;
  z-index: 999;
  border-bottom: 5px solid #5a5a5a !important;
  border-bottom-style: double !important;
}
.tblbody tr:last-child td:first-child {
  border: 0 !important;
}
.containerMain {
  overflow-x: scroll;
  overflow-y: hidden;
  width: 100%;
}
.containersub {
  width: 1760px;
}

/*KPI Sold Hours Chart*/
.kpi-soldhrs-block .MuiPaper-rounded,
.kpi-soldhrs-block-extended .MuiPaper-rounded {
  padding: 5px;
  width: 100% !important;
  margin-right: 10px;
  height: 73px;
  margin-bottom: 10px;
  /* width: 275px;
  max-width: 275px; */
}
.kpi-soldhrs-block h6,
.kpi-soldhrs-block-extended h6 {
  font-size: 16px;
  color: #003d6b;
  margin-bottom: -24px;
  /* margin-left: -45px; */
  /* margin-top: 27px; */
  /* padding-top: 9px; */
  display: flex;
  justify-content: start;
}
.kpi-soldhrs-block h5,
.kpi-soldhrs-block-extended h5 {
  font-size: 32px;
  line-height: 14px;
}
.diagram-section textarea {
  font-size: 12px;
  font-family: Roboto;
  padding-left: 10px;
  padding-right: 10px;
}
.sales-table thead th:first-child,
.sales-table tr td:first-child {
  width: 21% !important;
  max-width: 21% !important;
  min-width: 21% !important;
}

/* 
.card_kpi-1335 {
  background: #fff;
  border-radius: 3px;
  display: inline-block;
  height: 300px;
  margin: 1rem;
  position: relative;
  width: 290px;
  overflow: hidden;
  margin-top: 17px;
}
.card_kpi-1337 {
  background: #fff;
  border-radius: 3px;
  display: inline-block;
  margin: 1rem;
  position: relative;
  overflow: hidden;
  margin-top: 17px;
}
.card_kpi-1335 .top,
.card_kpi-1337 .top {
  display: inline-flex;
  width: 100%;
  height: 220px;
  overflow: hidden;
  justify-content: center;
}

.card_kpi-1335 .top img {
  height: 220px;
}

.card_kpi-1335 .bottom,
.card_kpi-1337 .bottom {
  height: 80px;
  width: 100%;
}

.card_kpi-1335 .bottom p,
.card_kpi-1337 .bottom p {
  text-align: left;
  height: 80px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  padding-left: 20px;
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #444;
  font-size: 18px;
  font-family: Roboto, sans-serif;
}
.card-kpi-1335.card-1-kpi {
  margin-top: 17px;
}
.card-kpi-1337.card-1-kpi {
  height: 400px !important;
  margin-top: 17px;
} */
/* .card-kpis {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
} */
.KpiCharts .react-grid-item:not(.react-grid-placeholder) {
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.KpiCharts
  .react-grid-item.react-draggable.cssTransforms.react-resizable.selected {
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  /* cursor: pointer; */

  /* color: #003d6b !important;
  font-weight: 500 !important; */
  /* border: 4px solid #ee7600 !important; */
  border: 4px solid #ee7600 !important;

  /* background-color: #ddeaf4 !important; */
}

@media (max-width: 2304px) {
  .lineChartContainer .chartjs-render-monitor {
    height: 175px !important;
  }
  .barChartContainer .chartjs-render-monitor {
    height: 350px !important;
  }
  .kpi-labor-grid_extended,
  .kpi-parts-grid_extended {
    height: 280px !important;
    margin-left: 17% !important;
  }
}
@media (max-width: 1920px) {
  .lineChartContainer .chartjs-render-monitor {
    height: 175px !important;
  }
  .barChartContainer .chartjs-render-monitor {
    height: 220px !important;
  }
}
.diagram-section-scatter {
  width: 100% !important;
  box-shadow: none !important;
}
.diagram-section-scatter-details {
  width: 100% !important;
}
.active-menu {
  color: #003d6b !important;
  font-weight: 500 !important;
  /* border: 1px solid #003d6b !important; */
  background-color: #ddeaf4 !important;
}
.active-menu svg {
  color: #003d6b !important;
}

.kpiHeader {
  position: absolute;
  right: 0px;
  top: 0px;
}
.kpiHeader button {
  padding: 0px;
}
/* .highcharts-container {
  padding: 10px 0px;
} */
#chartContainer_1346,
#chartContainer_1353 {
  height: 326px !important;
  /* width: 350px !important; */
  /* width: 545px !important; */
  /* height: 326px !important; */
}
.kpi-labor-grid,
.kpi-parts-grid {
  height: 326px !important;
  width: 545px !important;
}
.kpi-labor-grid_extended,
.kpi-parts-grid_extended {
  height: 256px !important;
  width: 545px !important;
  margin-left: 17% !important;
}
/* .LaborSale1341 {
  padding: 0px 10px;
} */
.dashboard-grid-items .react-grid-item {
  border-radius: 4px;
}
/* .layout {
  height: 2041px !important;
} */
.dashboard.dashboard-grid-items {
  margin-left: -4px;
}
.gridDrilldown button {
  padding: 0px;
}
.oneLineDrilldown button {
  padding: 0px;
}
.gridDrilldown {
  position: absolute !important;
  right: 0px !important;
  /* margin-right: 20px !important; */
}
.oneLineDrilldown {
  position: absolute !important;
  right: 0px !important;
}
.gridInternal button {
  padding: 0px;
}
.container1340 {
  margin-top: -126px;
  text-align: '-moz-center';
}
.container1340 div:first-child,
.container1340 .highcharts-container,
.container1340 svg {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 100%;
}
.container1340Linear {
  margin-top: -126px;
  text-align: '-moz-center';
}

.favgrid .react-grid-item {
  box-shadow: none;
}
/* .react-grid-item {
  box-shadow: none !important;
} */

.flatRateHrsBlock {
  width: 100%;
  text-align: center;
}
.ktooltip {
  display: inline-block;
  text-indent: 0em;
}

.ktooltiptext {
  display: none;
  width: 580px;
  background: #ddeaf4;
  border-radius: 6px;
  padding: 5px 5px;
  /* //right: 50px; */
  color: #003d6b;
  border: 2px solid #003d6b;
  line-height: normal;
  text-decoration: none;
  position: absolute;
  z-index: 1300;
  text-align: left;
  font-size: 15px;
  pointer-events: none;
}

p {
  display: inline-block;
}

.ktooltip:hover + div {
  display: grid;
  grid-gap: 8px;
}
.flatRateHrsBlock span {
  color: white;
  display: block;
}
span.flatRateHrsBlockValue {
  font-family: Roboto;
  color: #fff;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 10px;
}
span.flatRateHrsBlockTitle {
  font-family: Roboto;
  font-size: 13px;
}
.kpi-soldhrs-block img,
.kpi-soldhrs-block-extended img {
  position: absolute;
  left: 5px;
  top: 15px;
}
.flatRateLeftBlock {
  width: 50%;
  padding: 0 10px;
}
.hrsPerRoGrid {
  width: 50%;
  padding: 0 10px 0 0;
}

/* #chart-area-1353 {
  height: "30vh";
  width: "50vw";
  margin-left:'-26%';
} */
.kpi-labor-grid-bottom {
  margin-right: 11%;
  /* margin-top: -7%; */
  margin-top: -8%;
  text-align: right;
}

.kpi-labor-grid-top {
  display: flex;
  justify-content: left;
  margin-left: 10%;
}
@media (min-width: 2560px) {
  .kpi-soldhrs-block-extended .MuiPaper-rounded {
    height: 115px !important;
    max-width: 613px !important;
    margin-left: 10px !important;
  }
  .kpi-soldhrs-block-extended img {
    top: 7px !important;
    width: 66% !important;
  }
}
@media (min-width: 1920px) {
  .kpi-soldhrs-block-extended .MuiPaper-rounded {
    height: 115px !important;
  }
  .kpi-soldhrs-block-extended img {
    top: 7px !important;
    width: 66% !important;
  }
}

@media (min-width: 1920px) {
  .kpi-soldhrs-block .MuiPaper-rounded {
    height: 115px !important;
  }
  .hrsPerRoGrid .MuiPaper-rounded {
    height: 240px !important;
  }
  .kpi-soldhrs-block img {
    top: 7px !important;
    width: 66% !important;
  }
  span.flatRateHrsBlockValue {
    font-size: 35px !important;
    margin-bottom: 20px !important;
  }
  span.flatRateHrsBlockTitle {
    font-size: 18px !important;
  }
  .flatRateHrsBlock {
    padding-top: 20px !important;
  }
  .kpi-labor-grid,
  .kpi-parts-grid {
    height: 265px !important;
  }
}

@media (min-width: 1400px) {
  /* .kpi-soldhrs-block .MuiPaper-rounded {
    height: 115px !important;
  }
  .hrsPerRoGrid .MuiPaper-rounded {
    height: 240px !important;
  }
  .kpi-soldhrs-block img {
    top: 7px !important;
    width: 66% !important;
  }
  span.flatRateHrsBlockValue {
    font-size: 35px !important;
    margin-bottom: 20px !important;
  }
  span.flatRateHrsBlockTitle {
    font-size: 18px !important;
  }
  .flatRateHrsBlock {
    padding-top: 20px !important;
  } */
  .kpi-labor-grid,
  .kpi-parts-grid {
    height: 280px !important;
  }
  .kpi-labor-grid_extended,
  .kpi-parts-grid_extended {
    height: 280px !important;
  }
}
#detailSummary h4.main-title {
  margin-top: 0;
  font-size: 18px;
  color: #242f48;
  width: 100%;
}

.highcharts-legend-item-hidden {
  text-decoration: line-through;
}
#tooltipItem {
  font-weight: bold;
  font-size: 13px;
}

.highcharts-reset-zoom {
  display: none;
}
#returnRateDrilldown .ag-overlay {
  margin-top: 35px;
}

#navBarDiv nav::-webkit-scrollbar-track,
.makeStyles-modalPopup-95 .MuiDialog-paperWidthSm::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f5f5f5;
  background-clip: content-box;
}

#navBarDiv nav::-webkit-scrollbar,
.makeStyles-modalPopup-95 .MuiDialog-paperWidthSm::-webkit-scrollbar {
  width: 7px;
  background-color: #f5f5f5;
}
#navBarDiv nav {
  scrollbar-color: #ee7600 #dddddd;
  scrollbar-width: thin;
}
#navBarDiv nav::-webkit-scrollbar-thumb,
.makeStyles-modalPopup-95 .MuiDialog-paperWidthSm::-webkit-scrollbar-thumb {
  background-color: #ee7600;
  min-height: 20px;
}
.makeStyles-dropdown-56 svg {
  fill: #fff !important;
}
.makeStyles-dropdown-56:before {
  border-bottom: 0 !important;
}
.ps__rail-y {
  display: none !important;
}
.ps__rail-x {
  display: none !important;
}
/* .makeStyles-storeDropdown-57 {
  margin-top: -28% !important;
  margin-left: 390px !important;
  position: fixed  !important;
} */
@media (max-width: 2733px) {
  .makeStyles-storeDropdown-57 {
    margin-top: -36.3% !important;
    margin-left: 390px !important;
    position: fixed !important;
  }
}
@media (max-width: 2304px) {
  .makeStyles-storeDropdown-57 {
    margin-top: -35% !important;
    margin-left: 380px !important;
    position: fixed !important;
  }
}
@media (max-width: 1920px) {
  .makeStyles-storeDropdown-57 {
    margin-top: -33.5% !important;
    margin-left: 390px !important;
    position: fixed !important;
  }
  .gridDrilldown {
    position: absolute !important;
    right: 0px !important;
    margin-right: 20px !important;
  }
  .gridDrilldownRo {
    margin-right: 0px !important;
  }
  .oneLineDrilldown {
    position: absolute !important;
    margin-right: 0px !important;
    margin-top: 20px !important;
  }
}
@media (max-width: 1708px) {
  .makeStyles-storeDropdown-57 {
    margin-top: -32.5% !important;
    margin-left: 393px !important;
    position: fixed !important;
  }
  .gridDrilldown {
    position: absolute !important;
    right: 0px !important;
    margin-right: 20px !important;
  }
  .gridDrilldownRo {
    margin-right: 0px !important;
  }
  .oneLineDrilldown {
    position: absolute !important;
    margin-right: 0px !important;
    margin-top: 20px !important;
  }
}
@media (max-width: 1518px) {
  .makeStyles-storeDropdown-57 {
    margin-top: -30.2% !important;
    margin-left: 393px !important;
    position: fixed !important;
  }
}
@media (max-width: 1280px) {
  .makeStyles-storeDropdown-57 {
    margin-top: -30% !important;
    margin-left: 390px !important;
    position: fixed !important;
  }
}
@media (max-width: 1440px) {
  .makeStyles-storeDropdown-57 {
    margin-top: -390px !important;
    margin-left: 390px !important;
    position: fixed !important;
  }
}
.likes__relavance {
  position: relative;
  /* padding:0 80px; */
  float: left;
  margin-top: 15px;
  left: 11%;
}
.likes__list {
  position: absolute;
  box-sizing: border-box;
  overflow-y: hidden;
  max-height: 150px;
  left: 0%;
  top: 90%;
  z-index: 999;
  background: white;
  padding: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
}
#scroll .MuiDialog-paperScrollPaper::-webkit-scrollbar {
  width: 6px !important;
}
#scroll .MuiDialog-paperScrollPaper::-webkit-scrollbar-thumb {
  background: #ee7600 !important;
}
#scroll {
  scrollbar-color: #ee7600 #dddddd;
  scrollbar-width: thin;
}
.diagram-section.diagram-section-custom textarea {
  padding: 10px;
  min-height: 35px;
  margin: 10px 0;
}
.diagram-section.diagram-section-custom {
  margin-bottom: 10px;
  margin-top: 10px;
}
.diagram-section.diagram-section-custom .cardRoot {
  border-radius: 5px !important;
}
.buttonnotify {
  padding: 0 !important;
  max-width: 71px;
}
.buttonnotify img {
  width: 22px !important;
}
.buttonnotify svg {
  width: 22px !important;
  margin-top: -28px;
  margin-left: 42px;
}
.likes__list.contactPopup {
  left: -20px;
}
.likes__relavance.like_relavance_custom {
  /* padding: 0 80px 0 30px; */
}
.orderComponent {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 10px;
  /* background-color: #185b8e; */
  background-color: #5ba6df;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  /* color: #fff; */
  color: #3e3838;
  text-align: center;
  padding-top: 3px;
  min-width: 20px;
  margin-right: 5px;

  margin-left: -12px;
}
.orderComponent1-old {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 10px;
  /* background-colo#185b8eb8e; */
  background-color: #5ba6df;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  /* color: #fff; */
  color: #3e3838;
  text-align: center;
  padding-top: 2px;
  min-width: 20px;
  margin-right: 90px;
  margin-left: -11px;
  /* margin-right: 5px; */
}

.orderComponent8-old {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 10px;
  /* background-color: #185b8e; */
  background-color: #5ba6df;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  /* color: #fff; */
  color: #3e3838;
  text-align: center;
  padding-top: 2px;
  min-width: 20px;
  /* margin-right: 81px; */
  margin-right: 90px;
  margin-left: -111px;

  margin-top: 2px;
}
.orderComponent1 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  text-align: center;
  padding-top: 3px;
  min-width: 20px;
  margin-right: 81px;
  margin-left: -11px;
}
.orderComponent7 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  padding-top: 2px;
  margin-left: -86px;
  margin-right: 92px;
}
.orderComponent2 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  text-align: center;
  padding-top: 3px;
  min-width: 20px;
  margin-right: 5px;
  margin-left: -7px;
  color: #4f3e3e;
}
.orderComponent3 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  margin-top: 2px;
  margin-left: -10px;
  margin-right: 5px;
  color: #4f3e3e;
}
.orderComponent3a {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  margin-top: 2px;
  margin-left: -4px;
  margin-right: 5px;
  color: #4f3e3e;
}
.orderComponent4 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  margin-top: 2px;
  margin-left: -7px;
  margin-right: 5px;
  color: #4f3e3e;
}
/* .orderComponent5a {
  font-weight: bold;
  font-family: "Roboto", "Helvetica", "Arial", sans-serif;
  font-size: 12px;
  margin-top: 2px;
  margin-left: -7px;
  margin-right: 5px;
  color: #003d6b;
} */
.orderComponent5 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  text-align: center;
  padding-top: 3px;
  min-width: 20px;
  margin-right: 5px;
  margin-top: -3px;
  margin-left: -11px;
}
.orderComponent6 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  margin-left: -10px;
  margin-right: 5px;
  margin-top: -1px;
}
.orderComponent8 {
  font-weight: bold;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-size: 12px;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50%;
  text-align: center;
  padding-top: 3px;
  min-width: 20px;
  margin-right: 81px;
  margin-left: -111px;
}

.searchByRoOpcode {
  color: #574949 !important;
  font-weight: bold;
  cursor: pointer;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.searchByRoVariance2 {
  border-left: 1px solid #cfd4d8;
  border-bottom: 1px solid #eeeeee !important;
  border-right: 1px solid #cfd4d8;
  border-top: 1px solid #eeeeee;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.opcodeDetails button .MuiSvgIcon-root {
  height: 0.6em !important;
}

.opcodeDetails button:hover {
  height: 19px !important;
  width: 22px !important;
  background: #dde2ef !important;
}
.allOpcode {
  display: flex;
  justify-content: center;
}

@media (min-width: 2560px) {
  .gridDrilldown {
    position: absolute !important;
    right: 17px !important;
    margin-top: -1px !important;
  }
  .gridDrilldownRo {
    margin-right: 0px !important;
  }
  .oneLineDrilldown {
    position: absolute !important;
    margin-right: 0px !important;
    margin-top: 20px !important;
  }

  .LaborMissesList {
    width: 14px !important;
    height: 14px !important;
    margin-top: 0px !important;
    font-size: 0.6rem !important;
    background-color: #09478a !important;
  }
  .iconTurnUp {
    margin-top: -30px !important;
    position: absolute !important;
    right: 6.5% !important;
  }
  #data-tab-parts-misses {
    /* width: 1219px; */
    /* width: 1551px; */
    width: 1763px !important;
  }
  #data-tab-labor-misses {
    /* width: 1325px; */
    width: 1847px !important;
    /* width: 1722px !important; */
  }
  #data-tab-labor-pricing {
    width: 1128px !important;
  }
  #data-tab-parts-pricing {
    width: 339px !important;
  }

  #data-tab_opcodes_client {
    width: 982px !important;
  }
  .container1340Linear div:first-child,
  .container1340Linear .highcharts-container,
  .container1340Linear svg {
    width: 90% !important;
    margin-left: 5% !important;
  }
  .payTypeListPartsMatrix {
    margin-right: 9px !important;
    margin-top: 9px !important;
  }
  #data-tab-axcessa-report {
    width: 1623px !important;
  }
  .opportunityGrid .MuiAlert-message {
    margin-left: 7px !important;
  }
  #data-tab-job-count {
    width: 1306px !important;
  }
  .opportunityGrid {
    height: 37px !important;
    padding: 1px 16px !important;
  }
  #advisor_summary {
    width: 1450px !important;
  }
}

@media (max-width: 2304px) {
  #data-tab-parts-misses {
    /* width: 1219px; */
    /* width: 1549px; */
    width: 1782px !important;
  }
  #data-tab-labor-misses {
    width: 1783px !important;
    /* width: 1721px !important; */
  }
  #data-tab-labor-pricing {
    width: 1122px !important;
  }
  #data-tab-tech-summary {
    width: 470px !important;
  }
  #data-tab-tech-summary-drilldown {
    width: 706px !important;
  }
  .gridInternal {
    margin-left: 7px;
    margin-top: 1.8px;
    display: flex;
    height: 10px;
  }
  /* .gridDrilldown {
    margin-right: 17px !important;
  } */
  .iconTurnUp {
    margin-top: -30px !important;
    position: absolute !important;
    right: 9% !important;
  }

  #data-tab_opcodes_client {
    width: 970px !important;
  }
  .container1340Linear div:first-child,
  .container1340Linear .highcharts-container,
  .container1340Linear svg {
    width: 90% !important;
    margin-left: 5% !important;
  }
  .payTypeListPartsMatrix {
    margin-right: 9px !important;
    margin-top: 9px !important;
  }
  #data-tab-axcessa-report {
    width: 1429px !important;
  }
  .opportunityGrid .MuiAlert-message {
    margin-left: 7px !important;
  }
  #data-tab-job-count {
    width: 1321px !important;
  }
  .opportunityGrid {
    height: 38px !important;
    padding: 2px 16px !important;
  }
  #advisor_summary {
    width: 1419px !important;
  }
}

@media (min-width: 1920px) and (max-width: 2560px) {
  /* #data-tab-parts-misses {
    width: 1446px !important;
  }
  #data-tab-labor-misses {
    width: 1446px !important;
  } */
  #data-tab-labor-pricing {
    width: 1112px !important;
  }
  #data-tab-tech-summary {
    width: 460px !important;
  }
  #data-tab-tech-summary-drilldown {
    width: 698px !important;
  }
  .kpi-summary-block-target h6 {
    width: 100% !important;
  }
  .gridInternal {
    margin-left: 7px;
    margin-top: 2px;
    display: flex;
    height: 10px;
  }

  .iconTurnUp {
    margin-top: -30px !important;
    position: absolute !important;
    right: 12% !important;
  }

  #data-tab_opcodes_client {
    width: 967px !important;
  }
  .kpi-labor-grid_extended,
  .kpi-parts-grid_extended {
    height: 260px !important;
    margin-left: 0% !important;
  }
  .container1340Linear div:first-child,
  .container1340Linear .highcharts-container,
  .container1340Linear svg {
    width: 100% !important;
    max-width: 100% !important;
    min-width: 100%;
    margin-left: 0% !important;
  }
  .payTypeListPartsMatrix {
    margin-right: 9px !important;
    margin-top: 9px !important;
  }
  #data-tab-job-count {
    width: 1602px !important;
  }
  #data-tab-axcessa-report {
    width: 1623px !important;
  }
  #advisor_summary {
    width: 1430px !important;
  }
}
/*chrome 75%*/
@media (max-width: 1920px) {
  #data-tab-parts-misses {
    /* width: 1219px; */
    width: 1660px !important;
  }
  #data-tab-labor-misses {
    /* width: 1324px !important; */
    width: 1660px !important;
  }
  .opportunityGrid .MuiAlert-message {
    margin-left: 6px !important;
  }
  .opportunityGrid {
    height: 37px !important;
    padding: 1px 16px !important;
  }
}
/*chrome 80%*/
@media (max-width: 1820px) {
  #data-tab-parts-misses {
    /* width: 1219px; */
    width: 1446px !important;
  }
  #data-tab-labor-misses {
    /* width: 1324px !important; */
    width: 1446px !important;
  }
}
@media (min-width: 1920px) and (max-width: 2560px) {
  .kpi-labor-grid_extended,
  .kpi-parts-grid_extended {
    height: 260px !important;
    margin-left: 20% !important;
    height: 277px !important;
  }
  .opportunityGrid .MuiAlert-message {
    margin-left: 7px !important;
  }
  .opportunityGrid {
    height: 38px !important;
    padding: 2px 16px !important;
  }
}
@media (max-width: 1680px) {
  #data-tab-parts-misses {
    /* width: 1219px; */
    width: 1265px !important;
  }
  #data-tab-labor-misses {
    width: 1265px !important;
  }
  #data-tab-labor-pricing {
    width: 1109px !important;
  }
  #data-tab-tech-summary {
    width: 460px !important;
  }
  #data-tab-tech-summary-drilldown {
    width: 693px !important;
  }
  .kpi-summary-block-target h6 {
    width: 100% !important;
  }
  .gridInternal {
    margin-left: -1px;
    margin-top: 2px;
    display: flex;
    height: 10px;
  }
  .gridDrilldown {
    /* margin-right: 17px !important; */

    position: absolute !important;
    right: 0px !important;
    margin-top: 0px !important;
  }
  .gridDrilldownRo {
    margin-right: 0px !important;
  }
  .oneLineDrilldown {
    position: absolute !important;
    margin-right: 0px !important;
    margin-top: 18px !important;
  }
  .iconTurnUp {
    margin-top: -30px !important;
    position: absolute !important;
    right: 14% !important;
  }

  #data-tab_opcodes_client {
    width: 964px !important;
  }
  .payTypeListPartsMatrix {
    margin-right: 9px !important;
    margin-top: 9px !important;
  }
  #data-tab-job-count {
    width: 1263px !important;
  }
  #data-tab-axcessa-report {
    width: 1246px !important;
  }
  .opportunityGrid {
    height: 38px !important;
    padding: 2px 16px !important;
  }
  #advisor_summary {
    width: 1419px !important;
  }
}

@media (max-width: 1440px) {
  #data-tab-parts-misses {
    width: 1110px !important;
  }
  #data-tab-labor-misses {
    width: 1109px !important;
  }
  #data-tab-labor-pricing {
    width: 1104px !important;
  }
  #data-tab-tech-summary {
    width: 471px !important;
  }
  #data-tab-tech-summary-drilldown {
    width: 690px !important;
  }
  .kpi-summary-block-target h6 {
    width: 99% !important;
  }
  /* .gridDrilldown button {
    margin-left: -5px !important;
  } */
  .kpi-summary-block-target h5 {
    font-size: 13.5px !important;
  }
  /* .gridInternal {
    margin-left: 6px !important;
    margin-top: 0px !important;
  } */
  .gridInternal {
    /* margin-left: 4px; */
    /* margin-left: 0px; */
    margin-left: -5px;
    /* margin-top: 2px; */
    margin-top: 2px;
    display: flex;
    height: 10px;
    /*margin-top: -4px !important; */
  }
  .missesGrid {
    display: flex;
    margin-top: -1px;
    margin-bottom: -24px;
    margin-left: -3px;
  }
  .gridDrilldown {
    /* margin-right: 20px !important;
    margin-top: -2px !important; */

    position: absolute !important;
    right: -4px !important;
    margin-top: -1px !important;
  }
  .gridDrilldownRo {
    margin-right: 4px !important;
  }
  .oneLineDrilldown {
    position: absolute !important;
    margin-right: 0px !important;
    margin-top: 20px !important;
  }
  .kpiHeader {
    right: -7px !important;
  }
  .iconTurnUp {
    margin-top: -30px !important;
    /* margin-left: 8% !important; */
    position: absolute !important;
    right: 16% !important;
  }

  #data-tab_opcodes_client {
    width: 963px !important;
  }
  .payTypeListPartsMatrix {
    margin-right: 0px !important;
    margin-top: 9px !important;
  }
  #data-tab-axcessa-report {
    width: 1088px !important;
  }
  #axcessa_report {
    width: 587px !important;
  }
  .opportunityGrid .MuiAlert-message {
    margin-left: 6px !important;
  }
  #data-tab-job-count {
    width: 1104px !important;
  }
  .opportunityGrid {
    height: 38px !important;
    padding: 2px 16px !important;
  }
  #btnRecipient {
    left: 70px;
  }
  #advisor_summary {
    width: 1419px !important;
  }
}

@media (max-width: 1280px) {
  #data-tab-labor-misses {
    width: 1021px !important;
  }
  #data-tab-parts-misses {
    width: 1021px !important;
  }
}

.opcodeDetails button {
  height: 19px !important;
  width: 22px !important;
  background: #dde2ef !important;
  position: absolute;
  top: 3px;
  right: 5px;
}
.repair-order-details-table tbody tr td {
  position: relative;
}
#returnRateDrilldown .ag-header-cell-label .ag-header-cell-text {
  white-space: pre-wrap !important;
}
.allOpcode {
  display: flex;
  justify-content: center;
}
.markdown {
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.custom-table table {
  font-family: arial, sans-serif;

  width: 100%;
}
.custom-table td,
.custom-table th {
  /* text-align: right; */
  padding: 0 1px;
  margin-bottom: -1px;
}
tr.custom-rows td {
  border: 3px solid #000;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  color: #000;
}
tr.custom-rows-kpi td {
  border-left: 3px solid #000;
  border-right: 3px solid #000;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  color: #000;
}
tr.custom-rows-all td {
  border: 1px solid #000;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  color: #000;
}
.dot {
  height: 5px;
  width: 5px;
  background-color: #000;
  border-radius: 50%;
  display: inline-block;
  margin-bottom: 2.5px;
  margin-left: 2px;
  margin-right: 2px;
}
/* .scrollbar-container {
  overflow: auto !important
 
}

} */

.missesGrid {
  display: flex;
  margin-top: -1px;
  margin-bottom: -24px;
}
.kpiReportAlert .MuiAlert-standardInfo .MuiAlert-icon {
  display: none;
}
/* .kpiReportAlert .MuiAlert-standardInfo .MuiAlert-message {
  margin-top: -11px;
}
.kpiReportAlert .MuiAlert-standardInfo {
  height: 10%
} */

.ag-header-cell-label {
  justify-content: left;
}
.goaltitle {
  display: flex;
  padding: 15px;
  width: 972px;
  padding-right: 0px !important;
}
/* .goaltitlesub {
  flex-grow: 1;
  flex-basis: 0;
} */
.goaltitlesub {
  display: flex;
  flex-direction: row;
  gap: 20px;
  justify-content: space-between;
  align-items: center;
}
.goaltitlesub h5 {
  border-bottom: 2px solid #fff;
  padding-bottom: 4px;
}
.active-tab-goal {
  border-bottom: 2px solid #003d6b !important; /* Underline color */
  padding-bottom: 4px; /* Space between text and underline */
}
.goaleditbutton {
  text-align: right;
  margin-top: 1%;
}
.sep {
  margin-bottom: 15px !important;
}
.sep1 {
  margin-bottom: 55px !important;
  visibility: hidden;
}

.gridgoalsettings {
  margin: 15px;
}
.btngoal {
  width: 60px;
  height: 25px;
}

#data-tab-goal .ag-header-cell-label .ag-header-cell-text {
  white-space: pre-wrap !important;
}

.tag-item {
  background-color: #d4d5d6;
  display: inline-block;
  font-size: 14px;
  border-radius: 30px;
  height: 30px;
  padding: 0 4px 0 1rem;
  display: inline-flex;
  align-items: center;
  margin: 0 0.3rem 0.3rem 0;
}

.tag-item > .button {
  background-color: white;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  font: inherit;
  margin-left: 10px;
  font-weight: bold;
  padding: 0;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.error {
  margin: 0;
  font-size: 90%;
  color: tomato;
}

.gridadvgoal {
  float: left;
  height: 421px;
  width: 67%;
}
.emailsave {
  float: left;
  width: 27%;
  margin-top: -48px;
}
.emailtitlesec .emailcustomcb,
.emailtitlesec h6 {
  float: left;
}
.tagoutersec {
  background-color: rgb(234, 234, 241);
  padding: 15px;
  display: inline-block;
  width: 98%;
}
.emailinputbox {
  width: 100%;
  background-color: #fff;
  margin-top: 10px;
}
.emailinputbox input {
  font-size: 10px;
  padding: 10px;
}
.tag-item button {
  position: absolute;
  right: 3px;
}
.tag-item {
  font-size: 11px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-weight: 400;
  width: 100%;
  position: relative;
}
.btnSaveEmail {
  margin-top: 12px !important;
  display: block !important;
  float: right;
  /* right: 10px; */
  height: 25px;
  font-size: 12px !important;
  border: 1px solid black !important;
}
.emailtitlesec {
  display: inline-block;
  width: 100%;
  margin-top: 5px;
  margin-left: 50px;
}
.mainouterdiv {
  float: left;
  width: 850px;
}
#service-advisor-list ul.MuiList-padding {
  padding-top: 0;
}

.data-div ul {
  margin-left: 10px;
  line-height: 20px;
}
.allItems {
  display: flex;
  margin-top: -54px;
  justify-content: end;
}
.allItemsPricing {
  display: flex;
  margin-top: -51px;
  justify-content: start;
  margin-left: 130px;
}
.payTypeList {
  margin-right: 9px;
  margin-top: 9px;
}

/* .toggleValue {
  font-weight: bold;
  margin-left: 7px;
} */
.laborPartsGrid {
  height: 30px;
}
.laborPartsGrid.MuiInputBase-formControl {
  font-size: 12px !important;
}
.toggleLabel {
  /* width: 45%; */
  width: 112px;
  display: inline-block;
  font-size: 12px;
  font-weight: bold;
}
.toggleSeparator {
  /* width: 3%; */
  width: -2%;
  display: inline-block;
}
.toggleValue {
  font-weight: bold;
  /* width: 54%; */
  width: 123px;
  display: inline-block;
  word-break: break-word;
  font-size: 12px;
  margin-left: 4px;
}
.selectedToggle .toggleLabel,
.selectedToggle .toggleValue,
.selectedToggle .toggleSeparator,
.laborPartsGrid .toggleLabel,
.laborPartsGrid .toggleValue,
.laborPartsGrid .toggleSeparator {
  display: inline-block;
  width: auto;
  /* margin-right: 3px; */
}

/*.MuiPaper-elevation1.MuiPaper-root.MuiMenu-paper.MuiPopover-paper.MuiPaper-elevation8.MuiPaper-rounded{
  width: 260px;
  left: 1083px;
} */
.toggleValueKpi {
  font-weight: bold;
  /* width: 54%;
  display: inline-block;
  word-break: break-word; */
  font-size: 11.5px;
  margin-left: 4px;
}
.settingssave {
  text-align: center;
  margin-left: 155px;
}
.select-wrapper {
  width: 324px;
  padding: 7px 0px 0px;
}
.select-wrapperselect {
  /* z-index: 9999 !important; */
  font-family: 'Roboto';
  font-size: 14px;
}
.storesettingssave {
  text-align: left;
  margin-left: 46px;
  margin-top: 237px;
}
.btnSettings {
  width: 70px;
  height: 25px;
  float: right;
}
.grid-selected {
  box-shadow: 0 14px 28px rgba(0, 0, 0, 0.25), 0 10px 10px rgba(0, 0, 0, 0.22);
  /* cursor: pointer; */

  /* color: #003d6b !important;
  font-weight: 500 !important; */
  /* border: 4px solid #ee7600 !important; */
  border: 4px solid #ee7600 !important;

  /* background-color: #ddeaf4 !important; */
}
.button-selected {
  background: rgb(221, 234, 244) !important  ;
  color: rgb(0, 61, 107) !important ;
}

.KpiReportAlert .MuiSvgIcon-root.MuiSvgIcon-fontSizeInherit {
  display: none;
}
.KpiReportAlert .MuiAlert-message {
  /* margin-top: -13px !important; */
  display: flex;
  /* margin-left: -216px; */
}
.iconTargetReport {
  /* max-width: 100%; */
  max-height: 95%;
  transform: scaleX(-1);
}
.alertMsgReport {
  margin-left: 6px;
}
.KpiReportAlert .MuiAlert-root {
  background-color: white;
  position: relative;
}
.iconTargetReportIcon {
  /* width: 1.4%; */
  width: 2%;
}
.goaleditbuttonAdv {
  text-align: right;
  width: 655px;
}
.btngoalSettings {
  width: 83px;
  height: 25px;
  font-size: 12px !important;
  border: 1px solid black !important;
}
.advgoalcontainer {
  /*justify-content: space-between; */
  align-items: end;
  display: flex;
  margin-left: 15px;
  width: 72%;
  margin-top: -10px;
}
.errorOpp {
  margin-left: 12px;
  margin-top: 16px;
  font-size: 90%;
  color: tomato;
  width: 168px;
}
.noncategorized-rows {
  background-color: red;
}
.categorized-rows {
  background-color: #ccc;
}
#chartContainerExtended_1341,
#chartContainerExtended_1342,
#chartContainerExtended_1343,
#chartContainerExtended_1339,
#chartContainerExtended_1336,
#chartContainerExtended_1335,
#chartContainerExtended_1340,
#chartContainerExtended_1344 {
  background: #7575752b;
  border: 1px solid #003d6b;
}
#chartContainerExtended_1337,
#chartContainerExtended_1346,
#chartContainerExtended_1353,
#chartContainerExtended_1338,
#chartContainerExtended_1351 {
  /* , #chartContainerExtended_1346 { */
  background: #ee7600;
  border: 1px solid #003d6b;
}
.show-cell {
  background: white;
  border-left: 1px solid lightgrey !important;
  border-right: 1px solid lightgrey !important;
  border-bottom: 1px solid lightgrey !important;
}
#data-tab-goal .ag-icon.ag-icon-tree-open {
  display: none;
}
#data-tab-goal.ag-theme-balham
  .ag-ltr
  .ag-row-level-1
  .ag-row-group-leaf-indent {
  margin-left: 0px !important;
}
#data-tab-goal.ag-theme-balham
  .ag-cell-wrapper
  > :not(.ag-cell-value):not(.ag-group-value) {
  display: none !important;
}
/* #data-tab-goal.ag-cell-wrapper.ag-row-group-leaf-indent.ag-row-group-indent-1 {
  display: none !important;
} */
/* #data-tab-goal .ag-cell-wrapper.ag-row-group-leaf-indent.ag-row-group-indent-1 {
    display: none !important;
} */

#data-tab-goal-adv.ag-icon.ag-icon-tree-open {
  display: none;
}
#data-tab-goal-adv.ag-theme-balham
  .ag-ltr
  .ag-row-level-1
  .ag-row-group-leaf-indent {
  margin-left: 0px !important;
}
#data-tab-goal-adv.ag-theme-balham
  .ag-cell-wrapper
  > :not(.ag-cell-value):not(.ag-group-value) {
  display: none !important;
}

.dot-pulse {
  position: relative;
  left: -9999px;
  width: 6px;
  height: 6px;
  border-radius: 5px;
  background-color: #f7eeee;
  color: #f7eeee;
  box-shadow: 9999px 0 0 -5px #f7eeee;
  animation: dotPulse 1.5s infinite linear;
  animation-delay: 0.25s;
}

.dot-pulse::before,
.dot-pulse::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
  width: 6px;
  height: 6px;
  border-radius: 5px;
  background-color: #f7eeee;
  color: #f7eeee;
}

.dot-pulse::before {
  box-shadow: 9984px 0 0 -5px #f7eeee;
  animation: dotPulseBefore 1.5s infinite linear;
  animation-delay: 0s;
}

.dot-pulse::after {
  box-shadow: 10014px 0 0 -5px #f7eeee;
  animation: dotPulseAfter 1.5s infinite linear;
  animation-delay: 0.5s;
}

@keyframes dotPulseBefore {
  0% {
    box-shadow: 9984px 0 0 -5px #f7eeee;
  }
  30% {
    box-shadow: 9984px 0 0 2px #f7eeee;
  }
  60%,
  100% {
    box-shadow: 9984px 0 0 -5px #f7eeee;
  }
}

@keyframes dotPulse {
  0% {
    box-shadow: 9999px 0 0 -5px #f7eeee;
  }
  30% {
    box-shadow: 9999px 0 0 2px #f7eeee;
  }
  60%,
  100% {
    box-shadow: 9999px 0 0 -5px #f7eeee;
  }
}

@keyframes dotPulseAfter {
  0% {
    box-shadow: 10014px 0 0 -5px #f7eeee;
  }
  30% {
    box-shadow: 10014px 0 0 2px #f7eeee;
  }
  60%,
  100% {
    box-shadow: 10014px 0 0 -5px #f7eeee;
  }
}

.dot-pulse-kpi {
  position: relative;
  left: -9999px;
  width: 6px;
  height: 6px;
  border-radius: 5px;
  background-color: #546e7a;
  color: #546e7a;
  box-shadow: 9999px 0 0 -5px #546e7a;
  animation: dotPulseKpi 1.5s infinite linear;
  animation-delay: 0.25s;
}
.dot-pulse-kpi::before,
.dot-pulse-kpi::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
  width: 6px;
  height: 6px;
  border-radius: 5px;
  background-color: #546e7a;
  color: #546e7a;
}

.dot-pulse-kpi::before {
  box-shadow: 9984px 0 0 -5px #546e7a;
  animation: dotPulseKpiBefore 1.5s infinite linear;
  animation-delay: 0s;
}

.dot-pulse-kpi::after {
  box-shadow: 10014px 0 0 -5px #546e7a;
  animation: dotPulseKpiAfter 1.5s infinite linear;
  animation-delay: 0.5s;
}

@keyframes dotPulseKpiBefore {
  0% {
    box-shadow: 9984px 0 0 -5px #546e7a;
  }
  30% {
    box-shadow: 9984px 0 0 2px #546e7a;
  }
  60%,
  100% {
    box-shadow: 9984px 0 0 -5px #546e7a;
  }
}

@keyframes dotPulseKpi {
  0% {
    box-shadow: 9999px 0 0 -5px #546e7a;
  }
  30% {
    box-shadow: 9999px 0 0 2px #546e7a;
  }
  60%,
  100% {
    box-shadow: 9999px 0 0 -5px #546e7a;
  }
}

@keyframes dotPulseKpiAfter {
  0% {
    box-shadow: 10014px 0 0 -5px #546e7a;
  }
  30% {
    box-shadow: 10014px 0 0 2px #546e7a;
  }
  60%,
  100% {
    box-shadow: 10014px 0 0 -5px #546e7a;
  }
}
#data-tab-user-login.ag-theme-balham .ag-ltr {
  min-height: 300px !important;
}
#data-tab-opcode.ag-theme-balham .ag-ltr {
  min-height: 200px !important;
}
#data-tab-opcode-zero.ag-theme-balham .ag-ltr {
  min-height: 180px !important;
}
#discountsOpcategoryContainer .highcharts-title {
  display: none;
}

@media (min-width: 2560px) {
  .dataLabels1341.highcharts-data-label text {
    font-size: 18px !important;
    font-weight: 900 !important;
    font-family: 'Roboto';
  }
  .expandedXaxis text {
    font-size: 18px !important;
  }
  .expandedYaxis text {
    font-size: 13px !important;
  }
  .searchByRoVariance {
    border-left: 3px solid red;
    border-bottom: 3px solid red !important;
    border-right: 3px solid red;
    border-top: 3px solid red;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  }
}
@media (min-width: 2304px) {
  .searchByRoVariance {
    border-left: 3px solid red;
    border-bottom: 3px solid red !important;
    border-right: 3px solid red;
    border-top: 3px solid red;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  }
}
@media (max-width: 2304px) {
  .dataLabels1341.highcharts-data-label text {
    font-size: 18px !important;
    font-weight: 900 !important;
    font-family: 'Roboto';
  }
  .expandedXaxis text {
    font-size: 18px !important;
  }
  .expandedYaxis text {
    font-size: 13px !important;
  }
  .searchByRoVariance {
    border-left: 3px solid red;
    border-bottom: 3px solid red !important;
    border-right: 3px solid red;
    border-top: 3px solid red;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  }
  #btnRecipient {
    left: 148px;
  }
  #btnRecipient {
    left: 148px;
  }
}
@media (max-width: 1920px) {
  .dataLabels1341.highcharts-data-label text {
    font-size: 13px !important;
    font-weight: 900 !important;
    font-family: 'Roboto';
  }
  .expandedXaxis text {
    font-size: 13px !important;
  }
  .expandedYaxis text {
    font-size: 11px !important;
  }
  .searchByRoVariance {
    border-left: 2.3px solid red;
    border-bottom: 2.3px solid red !important;
    border-right: 2.3px solid red;
    border-top: 2.3px solid red;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  }
  #btnRecipient {
    left: 79px;
  }
  #btnRecipient {
    left: 79px;
  }
}
@media (max-width: 1440px) {
  .dataLabels1341.highcharts-data-label text {
    font-size: 13px !important;
    font-weight: 900 !important;
    font-family: 'Roboto';
  }
  .expandedXaxis text {
    font-size: 12px !important;
  }
  .expandedYaxis text {
    font-size: 11px !important;
  }
  .searchByRoVariance {
    border-left: 2px solid red;
    border-bottom: 2px solid red !important;
    border-right: 2px solid red;
    border-top: 2px solid red;
    font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  }
}

.title1 {
  position: absolute;
  background-color: white;
  color: #003d6b;
  font-weight: bold;
  font-size: 13px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  top: -10px;
  left: 12px;
  padding: 1px;
}

.dot-pulse-login {
  position: relative;
  left: -9999px;
  width: 6px;
  height: 6px;
  border-radius: 5px;
  background-color: #084573;
  color: #084573;
  box-shadow: 9999px 0 0 -5px #084573;
  animation: dotPulseLogin 1.5s infinite linear;
  animation-delay: 0.25s;
}

.dot-pulse-login::before,
.dot-pulse-login::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
  width: 6px;
  height: 6px;
  border-radius: 5px;
  background-color: #084573;
  color: #084573;
}

.dot-pulse-login::before {
  box-shadow: 9984px 0 0 -5px #084573;
  animation: dotPulseBeforeLogin 1.5s infinite linear;
  animation-delay: 0s;
}

.dot-pulse-login::after {
  box-shadow: 10014px 0 0 -5px #084573;
  animation: dotPulseAfterLogin 1.5s infinite linear;
  animation-delay: 0.5s;
}

@keyframes dotPulseBeforeLogin {
  0% {
    box-shadow: 9984px 0 0 -5px #084573;
  }
  30% {
    box-shadow: 9984px 0 0 2px #084573;
  }
  60%,
  100% {
    box-shadow: 9984px 0 0 -5px #084573;
  }
}

@keyframes dotPulseLogin {
  0% {
    box-shadow: 9999px 0 0 -5px #084573;
  }
  30% {
    box-shadow: 9999px 0 0 2px #084573;
  }
  60%,
  100% {
    box-shadow: 9999px 0 0 -5px #084573;
  }
}

@keyframes dotPulseAfterLogin {
  0% {
    box-shadow: 10014px 0 0 -5px #084573;
  }
  30% {
    box-shadow: 10014px 0 0 2px #084573;
  }
  60%,
  100% {
    box-shadow: 10014px 0 0 -5px #084573;
  }
}
.divNoData {
  margin-top: 102px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  font-weight: bold;
  font-size: 14px;
  text-align: center;
}
.editGoals-Btn {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px;

  background: #ee7600 !important;
  box-shadow: none;
  height: 29px;
  position: relative;
  margin: 0 5px !important;
  min-width: auto !important;
  text-transform: none !important;
  width: 91px;
}
.editGoals-Btn:hover {
  background: #0e5184 !important;
  color: #fff;
  box-shadow: none !important;
}
.editGoals-Btn p {
  color: #fff !important;
  margin-left: 3px;
}
.hastip {
  cursor: pointer;
}
.highcharts-axis-labels span {
  /* width:50px!important; */
  display: inline-block;
}
.ulsettings {
  margin-top: 9px;
  list-style: none;
  font-family: Roboto;
  justify-content: center;
  display: flex;
  color: rgb(90, 88, 88);
  font-size: small;
  margin-right: 59px;
}
.lisettings {
  color: rgb(0, 61, 107);
  font-size: 11px;
  font-family: Roboto;
  font-weight: bold;
  float: left;
  margin-right: 6px;
  line-height: 16px;
}
.spancls {
  margin-right: 5px;
  float: left;
  width: 12px;
  height: 12px;
  margin: 2px;
}
.errorselect {
  float: right;
  margin-right: 268px;
  font-size: 90%;
  color: tomato;
}
.MuiFormHelperText-root {
  color: tomato !important;
}
.matrix-value .MuiInputBase-formControl {
  width: 100%;
}
#schedule.MuiSelect-select.MuiSelect-select {
  padding-right: 24px;
  font-size: 12px;
}
#selectweekly.MuiSelect-select.MuiSelect-select {
  padding-right: 24px;
  font-size: 12px;
}
#selectmonthly.MuiSelect-select.MuiSelect-select {
  padding-right: 24px;
  font-size: 12px;
}
.errorChk {
  margin-left: 362px;
  font-size: 90%;
  color: tomato;
}
.MuiChip-labelSmall {
  font-size: 10px;
}

#data-tab-axcessa-report .ag-icon.ag-icon-tree-open {
  display: none;
}
#selectweekly.MuiSelect-select.MuiSelect-select {
  padding-right: 24px;
  font-size: 12px;
}
#selectmonthly.MuiSelect-select.MuiSelect-select {
  padding-right: 24px;
  font-size: 12px;
}
.errorChk {
  margin-left: 362px;
  font-size: 90%;
  color: tomato;
}
.MuiChip-labelSmall {
  font-size: 10px;
}
.select-wrapperselect-others {
  pointer-events: none;
  opacity: 0.6;
}
div.picker .react-daterange-picker__wrapper {
  border-radius: 4px;
  border: thin solid #d9d9d9;
}
.opportunityGrid .MuiSvgIcon-root.MuiSvgIcon-fontSizeInherit {
  width: 0.9em !important;
  height: 0.9em !important;
  margin-left: -6px !important;
}
.opportunityGrid .MuiAlert-icon {
  margin-right: 0px !important;
}

.kpi-body table {
  table-layout: fixed;

  border-collapse: collapse;
}

.fix {
  position: sticky;
  background: white;
}
.fix:first-child {
  left: 0;
}

.fix:nth-child(2) {
  left: 0;
}
.grid-btn {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 11px;
  border-radius: 3px !important;
  background: #ee7600 !important;
  box-shadow: none;
  height: 16px;
  width: 16px;
  position: initial !important;
  margin: 0 5px !important;
  min-width: auto !important;
  text-transform: none !important;
}
.grid-btn:hover {
  background: #0e5184 !important;
  color: #fff;
  box-shadow: none !important;
}

.MuiTreeItem-iconContainer {
  display: none !important;
}
#data-tab-axcessa-report .ag-icon.ag-icon-none {
  padding-right: 0px !important;
}
#data-tab-labor-misses .ag-icon.ag-icon-none,
#data-tab-labor-misses .ag-icon.ag-icon-asc,
#data-tab-labor-misses .ag-icon.ag-icon-desc,
#data-tab-parts-misses .ag-icon.ag-icon-none,
#data-tab-parts-misses .ag-icon.ag-icon-asc,
#data-tab-parts-misses .ag-icon.ag-icon-desc {
  padding-right: 0px !important;
  margin-right: -5px !important;
}
#reset-layout span .MuiSvgIcon-root {
  height: 0.8em !important;
  width: 0.8em !important;
}

#data-tab .ag-icon.ag-icon-none {
  padding-right: 0px !important;
}
#chartConteiner-1090 .highcharts-loading-inner {
  position: initial !important;
}
.btnSaveFixedRate {
  margin-top: 12px !important;
  display: block !important;
  float: right;
  /* right: 10px; */
  height: 25px;
  font-size: 12px !important;
  border: 1px !important;
}
#fixedRate .react-datepicker-popper {
  position: fixed !important;
  transform: translate(-7px, -2px) !important;
  inset: auto !important;
}
#fixedrate .MuiPickersDatePickerRoot-toolbar {
  display: none !important;
}
.fixedRates .MuiInputBase-input {
  margin-left: -1px !important;
  margin-top: -21px !important;
}
#fixedRate-helper-text {
  width: 100px !important;
}
/* #btnSaveFixedRatePaytype {
  margin-right: 24% !important;
  width: 20% !important;
} */
.fixedRatesPaytypes .MuiInputBase-input {
  margin-left: -1px !important;
  margin-top: -12px !important;
}

#contact-popper .MuiTooltip-tooltip {
  background-color: #fff !important;
}
#contact-popper-login .MuiTooltip-tooltip {
  background-color: #fff !important;
}
.details-tag[title]:hover::after {
  content: attr(title);
  position: absolute;
  top: -100%;
  left: 0;
  background-color: red !important;
}

#itemListeffective {
  pointer-events: none; /* Disable clicking on list items */
  background-color: transparent;
}

.reset-btn-fixed-rate:disabled {
  color: #fff !important;
  border: 0 !important;
  cursor: pointer;
  padding: 0 6px !important;
  font-size: 14px;
  border-radius: 3px;
  background: #f7ba6b !important;
  box-shadow: none;
  position: relative;
  /* margin: 0 5px !important; */
  min-width: auto !important;
  text-transform: none !important;
}
.side-bar {
  position: relative;
}
.side-bar:after {
  width: 110%;
  background-image: url('/images/logos/separator-2.png');
  height: 4px;
  position: absolute;
  content: '';
  left: -10px;
  z-index: 9;
  background-size: 100%;
}
.strikethrough {
  text-decoration: line-through;
}
[op-title]:hover:after {
  opacity: 1;
  transition: all 0.1s ease 0.5s;
  visibility: visible;
}
[op-title]:after {
  content: attr(op-title);
  font-size: 14px;
  font-weight: normal;
  /* color: #003d6b !important; */
  border: 1px solid #003d6b;
  background-color: #ddeaf4 !important;
  position: absolute;

  bottom: -1.6em;
  left: 100%;
  white-space: nowrap;
  box-shadow: 1px 1px 3px #222222;
  opacity: 0;
  border: 1px solid #111111;
  z-index: 99999;
  visibility: hidden;
}
[op-title] {
  position: relative;
}
.opcode_table .ag-row .ag-cell:nth-child(1) {
  overflow: visible;
}
.opcode_table .ag-row [op-title]::after {
  line-height: 13px;
  font-size: 11px;
  padding: 6px 3px 6px 3px !important;
  bottom: -5px !important;
}

[span-title]:hover:after {
  opacity: 1;
  transition: all 0.1s ease 0.5s;
  visibility: visible;
}
[span-title]:after {
  content: attr(span-title);
  font-size: 14px;
  font-weight: normal;
  /* color: #003d6b !important; */
  border: 1px solid #003d6b;
  background-color: #ddeaf4 !important;
  position: absolute;

  bottom: -1.6em;
  left: 100%;
  white-space: nowrap;
  box-shadow: 1px 1px 3px #222222;
  opacity: 0;
  border: 1px solid #111111;
  z-index: 99999;
  visibility: hidden;
}
[span-title] {
  position: relative;
}
.summary_table .ag-row .ag-cell:nth-child(2) {
  overflow: visible;
}
.summary_table .ag-row [span-title]::after {
  line-height: 13px;
  font-size: 11px;
  padding: 6px 3px 6px 3px !important;
  bottom: -5px !important;
}
.opcode-summary-table .ag-row .ag-cell:nth-child(2) {
  overflow: visible;
}
.opcode-summary-table .ag-row [span-title]::after {
  line-height: 13px;
  font-size: 11px;
  padding: 6px 3px 6px 3px !important;
  bottom: 5px !important;
  border-radius: 4px;
  background-color: #d9d9d9;
  border: 1px solid #003d6b;
  color: #003d6b !important;
}
#kpi-report-2 .ag-column-select-indent-2 .ag-column-select-indent-0 {
  display: none !important;
}
.dot_kpi {
  height: 4px;
  width: 4px;
  background-color: #000000bd;
  border-radius: 50%;
  display: inline-block;
  /* margin-bottom: 2.5px; */
  margin-left: 4px;
  margin-right: 4px;
}
.repair-order-details-grid {
  font-size: 13px !important;
  color: #031b4e !important;
  font-weight: 600 !important;
}
.repair-order-details-value {
  font-size: 13px !important;
  color: #7987a1 !important;
  font-weight: 600 !important;
  font-family: 'roboto';
  margin-top: 23px !important;
  /* margin-right: 5.7% !important; */
  /* margin-right: 87px !important; */
  /* width: 32px !important; */
  margin-right: 45px !important;
}
.repair-order-details-value-customer {
  font-size: 13px !important;
  color: #7987a1 !important;
  font-weight: 600 !important;
  font-family: 'roboto';
  margin-top: 23px !important;
  /* margin-right: 5.7% !important; */
  /* margin-right: 87px !important; */
  /* width: 32px !important; */
  margin-right: 45px !important;
  min-width: 100px !important;
  max-width: 120px !important;
  text-align: initial !important;
}
.repair-order-details-value-make,
.repair-order-details-value-make-Desc {
  font-size: 13px !important;
  color: #7987a1 !important;
  font-weight: 600 !important;
  font-family: 'roboto';
  margin-top: 23px !important;
  /* margin-right: 5.7% !important; */
  /* margin-right: 87px !important; */
  /* width: 32px !important; */
  margin-right: 45px !important;
  max-width: 100px !important;
  min-width: 40px !important;
  text-align: initial !important;
}
@media (max-width: 1440px) {
  .repair-order-details-value,
  .repair-order-details-value-customer,
  .repair-order-details-value-make {
    /* margin-right: 25px !important; */

    margin-right: 15px !important;
    display: flex;
    flex-direction: column;
  }
  .repair-order-details-value-make-Desc {
    width: 85px !important;
    /* margin-right: 25px !important; */
    margin-right: 0px !important;
  }
  .repair-order-details-advisor .MuiGrid-grid-xs-3 {
    max-width: 22% !important;
  }
}
.repair-order-details-separator {
  margin-right: 5px !important;
  /* margin-left: 20px !important; */
  /* margin-left: 26px !important; */
  margin-left: 10px !important;
  color: #7987a1 !important;
}
.repair-order-details-separator-cls {
  margin-right: 5px !important;
  /* margin-left: 20px !important; */
  /* margin-left: 20px !important; */
  margin-left: 10px !important;
  color: #7987a1 !important;
}
.MuiAccordionSummary-content {
  margin-top: -10px !important;
  margin-bottom: -6px !important;
  margin-left: 10px !important;
}
/* .MuiAccordionSummary-expandIcon.Mui-expanded {
  transform: rotate(180deg);
} */
.MuiAccordionDetails-root {
  margin-top: -30px !important;
}
#panel1a-header {
  flex-direction: row-reverse !important;
  padding: 0px !important;
}
.rodetailsMsg {
  font-size: 12px !important;
  color: green !important;
  margin-left: 10% !important;
  font-family: 'roboto';
}

.dot-pulse-ro {
  position: relative;
  left: -9999px;
  width: 3px;
  height: 3px;
  border-radius: 2px;
  background-color: #546e7a;
  color: #546e7a;
  box-shadow: 9999px 0 0 -5px #546e7a;
  animation: dotPulseKpi 1.5s infinite linear;
  animation-delay: 0.25s;
}
.dot-pulse-ro::before,
.dot-pulse-ro::after {
  content: '';
  display: inline-block;
  position: absolute;
  top: 0;
  width: 3px;
  height: 3px;
  border-radius: 2px;
  background-color: #546e7a;
  color: #546e7a;
}

.dot-pulse-ro::before {
  box-shadow: 9984px 0 0 -5px #546e7a;
  animation: dotPulseKpiBefore 1.5s infinite linear;
  animation-delay: 0s;
}

.dot-pulse-ro::after {
  box-shadow: 10014px 0 0 -5px #546e7a;
  animation: dotPulseKpiAfter 1.5s infinite linear;
  animation-delay: 0.5s;
}

.kpi-custom-table .ag-group-value .kpi-header-cell {
  justify-content: normal;
}
.kpi-custom-table .ag-group-value .kpi-header-cell .customHeaderOpt {
  width: 20px;
}
.kpi-custom-table .ag-group-value .kpi-header-cell .MuiSvgIcon-root {
  width: 20px;
}
.kpi-custom-table .ag-group-value {
  position: relative;
}
.kpi-custom-table .ag-group-value .pagination {
  position: absolute;
  right: 5px;
  cursor: pointer;
}
.op-detailview div[col-id='lbropcode'] {
  overflow: visible !important;
}
.op-detailview.ag-row [span-title]::after {
  line-height: 13px;
  font-size: 11px;
  padding: 6px 3px 6px 3px !important;
  bottom: 5px !important;
  border-radius: 4px;
  background-color: #d9d9d9;
  border: 1px solid #003d6b;
  color: #003d6b !important;
}

/******************************************************/

.mainSecondaryDiv {
  padding-top: 3px;
  margin: 0px 0;
}

.gridMain {
  padding-bottom: 0px;
}

.dividerKPIMiddle {
  background-color: #fff;
  font-weight: 'bold';
  width: 95%;
  margin-left: 'auto';
  margin-right: 'auto';
}

.kpiDivider {
  background-color: #ee7600 !important;
  height: 4px !important;
  width: 97.5%;
  margin-left: auto !important;
  margin-right: auto !important;
  margin-bottom: 3px !important;
  margin-top: 0px !important;
}

.stylishSpan {
  display: block;
  text-align: right;
}

.stylishSpanValues {
  display: block;
  text-align: left;
}

.dividerKPI {
  background-color: #ee7600 !important;
  font-weight: bold;
  width: 95%;
  margin-left: auto;
  margin-right: auto;
}

.mainSecondarySubDiv {
  text-align: right;
}

.mainSecondarySubDivRight {
  text-align: left;
}

.mainSecondarySubDivRightMiddle {
  text-align: left;
  padding-top: 10px;
}

.kpiIcons {
  position: absolute;
  right: 20px !important;
  top: auto;
}

.valSubHeadMiddle {
  color: #fff;
  /* line-height: 21px; */
  font-weight: 500;
}

@media (max-width: 1920px) {
  .valSubHeadMiddle {
    font-size: 12px !important;
  }
}

@media (max-width: 1440px) {
  .valSubHeadMiddle {
    font-size: 11.25px !important;
  }
  .spaced-text-val {
    width: 33px !important;
  }
  .spaced-text-item {
    width: 6px !important;
    margin-right: 3px !important;
    margin-left: 3px !important;
  }
  .spaced-text .spaced-text-val:nth-child(3) {
    width: 50px !important;
  }
}

@media (max-width: 1280px) {
  .valSubHeadMiddle {
    line-height: 12px !important;
  }
}

@media (min-width: 2304px) {
  .valSubHeadMiddle {
    line-height: 12.5px !important;
  }
}

.targetIcon {
  float: left;
  width: 50px !important;
}

.valSubHead {
  color: #757575;
  font-weight: 500;
}

@media (max-width: 1920px) {
  .valSubHead {
    font-size: 12px !important;
    color: #48616d;
  }
}

@media (max-width: 1440px) {
  .valSubHead {
    font-size: 11.25px !important;
    color: #48616d;
  }
}

@media (max-width: 1280px) {
  .valSubHead {
    line-height: 12px !important;
    color: #48616d;
  }
}

@media (min-width: 2304px) {
  .valSubHead {
    line-height: 12.5px !important;
    color: #48616d;
  }
}

.titleMain {
  color: #003d6b;
}

.card-2 {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 29% !important;
}
.card-3 {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  width: 38% !important;
}
.kpi-grid-B-1 .stylishSpan {
  /* line-height: 20px !important; */
}
.kpi-grid-B-1 .stylishSpanValues {
  /* line-height: 20px !important; */
}
.kpi-grid-B-1 {
  min-height: 142px;
  padding-top: 10px;
}
.kpi-grid-E-0 {
  /* min-height: 147px; */
  margin-bottom: 13px !important;
  margin-top: 3px !important;
}

.kpi-grid-B-0 img,
.kpi-grid-B-2 img,
.kpi-grid-B-1 img,
.kpi-grid-B-3 img {
  max-width: 37px !important;
}
.kpi-grid-C-1 img,
.kpi-grid-C-2 img,
.kpi-grid-A-1 img,
.kpi-grid-E-1 img,
.kpi-grid-A-3 img {
  max-width: 35px !important;
}

@media (min-width: 2300px) {
  .kpi-grid-B-1 {
    min-height: 148px;
  }
}
.scorecard-items {
  display: flex;
  justify-content: space-evenly;
}

.spaced-text {
  white-space: nowrap; /* Prevent line breaks */
}

.spaced-text-item {
  display: inline-block;
  width: 10px; /* Specify the width for each item */
  text-align: center; /* Optional: Center the text within each item */
  margin-right: 3px; /* Optional: Add spacing between items */
  margin-left: 3px;
}
.spaced-text-val {
  display: inline-block;
  width: 38px;
}
.spaced-text .spaced-text-val:nth-child(3) {
  width: 55px !important;
}
.spaced-text-val-work-mix {
  display: inline-block;
  width: 22px !important;
}
.spaced-text-val-misses {
  display: inline-block;
  width: 36px !important;
}

/* Riju 08-01-2024*/
/* .main-cnt-custom-padd {
  padding: 8px 2px !important;
} */

.kpi-grid-A-2 img {
  width: 33px !important;
}
.kpi-grid-E-0 img {
  width: 30px !important;
}
.additionalClassMiddleTop {
  max-inline-size: fit-content !important;
  margin-left: 25px !important;
}
.spaced-text-item-values {
  padding-left: 35px;
}
.spaced-text-val0 {
  display: inline-block;
  width: 23px;
}
.boldValues {
  padding-left: 25px;
}
.spaced-text-val2 {
  padding-left: 50px;
}

.maintRepair {
  padding-left: 55px;
}

@media (max-width: 1920px) {
  .spaced-text-item-values {
    padding-left: 20px;
  }
}

@media (max-width: 1440px) {
  .valSubHeadMiddle {
    font-size: 9.5px !important;
  }
  .additionalClassMiddleTop {
    max-inline-size: fit-content !important;
    margin-left: 1px !important;
  }
  .spaced-text-item-values {
    padding-left: 15px;
  }
  .valSubHead {
    font-size: 10px !important;
    color: #48616d;
  }
}

@media (max-width: 1280px) {
  .spaced-text-item-values {
    padding-left: 20px;
  }
}

@media (min-width: 2304px) {
  .spaced-text-item-values {
    padding-left: 20px;
  }
  .additionalClassMiddleTop {
    max-inline-size: fit-content !important;
    margin-left: 100px !important;
  }
}

@media (min-width: 2560px) {
  .additionalClassMiddleTop {
    max-inline-size: fit-content !important;
    margin-left: 155px !important;
  }
}

@media (max-width: 1280px) {
  .valSubHeadMiddle {
    font-size: 8px !important;
    line-height: 18px !important;
  }
  .valSubHead {
    font-size: 8px !important;
  }
  .maintRepair {
    padding-left: 45px;
  }
  .spaced-text-val2 {
    padding-left: 35px;
  }
  .boldValues {
    padding-left: 12px;
  }
  .spaced-text-val {
    width: 30px !important;
  }
  .spaced-text .spaced-text-val:nth-child(3) {
    width: 42px !important;
  }
  .kpi-summary-block h6 {
    font-size: 11px;
  }
}

@media (min-width: 2304px) {
  .spaced-text-item-values {
    padding-left: 20px;
  }
  .additionalClassMiddleTop {
    max-inline-size: fit-content !important;
    margin-left: 100px !important;
  }
  .kpi-summary-block h6 {
    font-size: 11px;
  }
}

@media (min-width: 2560px) {
  .additionalClassMiddleTop {
    max-inline-size: fit-content !important;
    margin-left: 155px !important;
  }
  .kpi-summary-block h6 {
    font-size: 11px;
  }
}
.side-bar .MuiButton-root .Mui-disabled {
  color: #fff !important;
}
.kpi_expand {
  font-size: 20px !important;
}
.kpi_sort {
  font-size: 24px !important;
}
.kpi-data-div {
  display: flex;
  align-items: center;
  width: 225px;
}

.blocks-outer-first {
  display: inline-flex;
  border-bottom: 5px solid #f4722d;
  padding-bottom: 10px;
  margin-bottom: 10px;
}
.blocks-outer-second {
  display: inline-flex;
}
.second-block {
  border-bottom: 0;
}
.second-block .ash-block {
  width: 33.3%;
  float: left;
  padding: 0px 0px;
}
.second-block .orange-block {
  width: 33.3%;
  float: left;
  padding: 0 6px;
}
.ash-block {
  width: 30%;
  float: left;
  padding: 0 0px;
}
.orange-block {
  width: 40%;
  float: left;
  padding: 0 5px;
}
.ash-block-content {
  background: #e8e8e8;
  padding: 10px;
  float: left;
  width: 100%;
  height: 100%;
}
.orange-block-content {
  background: #f1741f;
  padding: 10px;
  float: left;
  width: 100%;
  height: 100%;
}
.content-row-title h2 {
  font-size: 13px;
  font-weight: bold;
}
.blocks-outer-first .content-label {
  width: 60%;
  float: left;
  color: #48616d;
  font-size: 12px;
  font-weight: bold;
  text-align: right;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.blocks-outer-first .content-value {
  width: 40%;
  float: left;
  color: #48616d;
  font-size: 12px;
  font-weight: bold;
  text-align: left;
  padding-left: 10px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.blocks-outer-first .ash-block-content .content-icon {
  width: 8%;
  float: left;
}
.blocks-outer-first .ash-block-content .content-right {
  width: 92%;
  float: left;
}
.blocks-outer-first .orange-block-content .content-label {
  width: 56%;
  float: left;
  color: #48616d;
  font-size: 12px;
  text-align: right;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.blocks-outer-first .orange-block-content .content-value {
  width: 44%;
  float: left;
  color: #48616d;
  font-size: 12px;
  text-align: left;
  padding-left: 10px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}

.blocks-outer-first .content-icon img {
  width: 30px;
}
.ash-block-content .content-row {
  border-bottom: 2px solid #f1741f;
  float: left;
  width: 100%;
  padding: 7px 0;
}
.content-col {
  padding: 3px 0;
  float: left;
  width: 100%;
  /* display: flex; */
  position: relative;
}
.ash-block-content .content-row:last-child,
.orange-block-content .content-row:last-child {
  border-bottom: 0;
}
.ash-block-content h2 {
  color: #ee7600;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.orange-block-content h2 {
  color: #003d6b;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}

.blocks-outer-first .orange-block-content .content-icon {
  width: 8%;
  float: left;
  padding-top: 6px;
}
.blocks-outer-first .orange-block-content .content-right {
  width: 92%;
  float: left;
}
.blocks-outer-first .orange-block-content .content-label,
.blocks-outer-first .orange-block-content .content-value {
  color: #fff;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
span.inner-data {
  margin-left: 15px;
}
.content-value-inner {
  float: left;
}
.content-value-block-1 {
  /* width: 45px; */
  min-height: 10px;
}
.content-value-block-2 {
  /* width: 60px; */
  min-height: 10px;
}
.content-value-block-3 {
  /* width: 45px; */
  min-height: 10px;
}
.slash {
  width: 12px;
  text-align: center;
  min-height: 10px;
}
.orange-block-content .content-row {
  border-bottom: 2px solid #fff;
  float: left;
  width: 100%;
  padding: 7px 0;
}
.orange-first-mid-row .content-label {
  max-width: 45%;
}
.orange-first-mid-row .content-value {
  min-width: 55%;
}
.middle-title {
  margin-top: 10px;
  display: inline-block;
  margin-left: auto;
  width: 100%;
}
.content-row {
  position: relative;
}
.info-icon {
  position: absolute;
  right: 0;
  width: 12px;
}
.info-icon img {
  width: 100%;
  cursor: pointer;
}
.content-row-title {
  text-align: left;
  margin-bottom: 10px;
}
.content-row-title b {
  color: #003d6b;
  text-decoration: underline;
}
.customGrpHeaderLabel p {
  font-size: 10px;
}
.customGrpHeaderLabelSingle p {
  font-size: 10px;
}
.blocks-outer-first
  .orange-block-content
  .content-row:nth-child(3)
  .content-col {
  margin: 7.5px 0;
  padding: 0;
}
.single-content-col {
  margin: 8px 0;
}
.ash-block-content .content-label b {
  color: #003d6b;
  text-decoration: underline;
}
.content-label-b-block b {
  color: #003d6b;
}
.e-first-block {
  padding: 4px 0px;
}
@media (max-width: 1920px) {
  .content-label {
    font-size: 11px !important;
  }
  .content-value {
    font-size: 11px !important;
  }
  .content-label-b-block {
    width: 62% !important;
  }
  .content-value-b-block {
    width: 38% !important;
    display: flex;
    padding-right: 20px;
    gap: 4px;
  }
  .content-label-b-m-block {
    width: 50% !important;
  }
  .content-value-b-m-block {
    width: 50% !important;
    display: flex;
    padding-right: 20px;
    gap: 4px;
  }
  .orange-block-content .content-row {
    padding: 6.65px 0px !important;
  }
  .content-col-e-block {
    padding: 6px 0px;
  }
  .info-icon {
    width: 15px !important;
    height: 15px !important;
  }
  .ash-block.a-block .inner-data {
    margin-left: 56px !important;
  }
  .ash-block.g-block span.inner-data {
    margin-left: 0px !important;
  }
}

@media (max-width: 1440px) {
  .content-label {
    font-size: 10px !important;
  }
  .content-value {
    font-size: 10px !important;
  }
  .content-label-b-block {
    width: 63% !important;
  }
  .content-value-b-block {
    width: 33% !important;
    display: flex;
    padding-right: 20px;
    gap: 4px;
  }
  .content-label-b-m-block {
    width: 50% !important;
  }
  .content-value-b-m-block {
    width: 50% !important;
    display: flex;
    padding-right: 20px;
    gap: 4px;
  }
  .orange-block-content .content-row {
    padding: 6px 0px !important;
  }
  .ash-block.a-block .inner-data {
    margin-left: 23px !important;
  }
  .content-col.content-right-c-block {
    padding: 4px 0px !important;
  }
  span.inner-data {
    margin-left: 10px;
  }
  .datepicker-block .datepicker {
    width: 164px !important;
  }
  .picker-report {
    width: 54% !important;
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .picker-report-one-month {
    /* width: 54% !important; */
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .info-icon {
    width: 15px !important;
    height: 15px !important;
  }
  /* .blocks-outer-first
    .orange-block-content
    .content-row:nth-child(3)
    .content-col {
    padding: 8px 0;
  } */
}

@media (max-width: 1280px) {
  .content-label {
    font-size: 11px !important;
  }
  .content-value {
    font-size: 11px !important;
  }
  .content-label-b-block {
    width: 62% !important;
  }
  .content-value-b-block {
    width: 38% !important;
    display: flex;
    padding-right: 20px;
  }
  .content-label-b-m-block {
    width: 50% !important;
  }
  .content-value-b-m-block {
    width: 50% !important;
    display: flex;
    padding-right: 20px;
    gap: 4px;
  }
  .ash-block.a-block .inner-data {
    margin-left: 23px !important;
  }
  .info-icon {
    width: 15px !important;
    height: 15px !important;
  }
}

@media (min-width: 2304px) {
  .content-label {
    font-size: 15px !important;
  }
  .content-value {
    font-size: 15px !important;
  }
  .content-label-b-block {
    width: 62% !important;
  }
  .content-value-b-block {
    width: 38% !important;
    display: flex;
    padding-right: 20px;
  }
  .content-value-b-m-block {
    display: flex;
    gap: 4px;
  }
  .b-block-1 {
    width: 62px;
  }
  .b-block-2 {
    width: 82px;
  }
  .b-block-3 {
    width: 62px;
  }
  .info-icon {
    padding: 3px 0;
    width: 20px !important;
    height: 20px !important;
  }
  .orange-block-content .content-row {
    padding: 8.5px 0px !important;
  }
  .blocks-outer-first
    .orange-block-content
    .content-row:nth-child(3)
    .content-col {
    margin: 8px 0;
    padding: 0;
  }
  .e-first-block {
    padding: 5px 0px;
  }
  .picker-report {
    width: 20% !important;
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .picker-report-one-month {
    width: 20% !important;
    display: flex;
    align-items: center;
    gap: 5px;
  }
  .gridDrilldown {
    margin-right: 20px;
  }
  .gridDrilldownRo {
    margin-right: 0px !important;
  }
  .oneLineDrilldown {
    margin-right: 3px !important;
    margin-top: 24px !important;
  }
  .ash-block.a-block .inner-data {
    margin-left: 80px !important;
  }
}

@media (min-width: 2560px) {
  .content-label {
    font-size: 12px !important;
  }
  .content-value {
    font-size: 12px !important;
  }
  .content-label-b-block {
    width: 62% !important;
  }
  .content-value-b-block {
    width: 38% !important;
    display: flex;
    padding-right: 20px;
  }
  .info-icon {
    width: 20px !important;
    height: 20px !important;
  }
}

.no-img {
  height: 10px;
}
/* .content-value-b-block .content-value-block-3{
  color:#003d6b !important
} */

/* a#Favorites {
  color: #fff !important
} */
#kpi-report-2 .ag-header-viewport {
  background-color: #c65911 !important;
  height: 30px;
}
.inner-data-label {
  width: 50px;
  display: inline-block;
}

.content-value-perc {
  color: #003d6b;
}

.value-content {
  display: flex;
  justify-content: space-evenly;
}
.content-row-title p {
  font-size: 11px !important;
}
.ash-block.a-block .inner-data {
  margin-left: 35px;
}
.datepicker-block {
  display: flex;
  padding-right: 5px;
  align-items: center;
  gap: 5px;
}

.datepicker-block .datepicker {
  width: 200px;
}

.date-asofrouting {
  padding-top: 10px;
}

.infoIcon {
  position: absolute !important;
  top: -3px;
  right: 0;
}
.infoIcon:hover {
  background: none !important;
}
.content-right.content-right-d-block .content-label {
  width: 53%;
}
.flex-grid-m-block {
  display: flex;
}
.flex-grid-2-block {
  display: flex;
  /* justify-content: space-around; */
}
.d-first-block {
  width: 50%;
}
.e-block-title {
  margin-bottom: 4px;
}

.ash-block.g-block .content-label {
  width: 65%;
}
.ash-block.g-block .content-value {
  width: 35%;
}
.content-popper > ul {
  padding: 4px 16px;
}
.home-tooltip {
  padding: 4px 16px;
}
.content-popper > p {
  padding-left: 4px !important;
}
.content-col.content-right-c-block {
  padding: 3.5px 0px;
}

.drilldown-icon {
  width: 15px !important;
  height: 15px !important;
}

.gridDrilldown button svg {
  width: 15px !important;
  position: relative;
  top: 1px !important;
}
.oneLineDrilldown button svg {
  width: 15px !important;
  position: relative;
  top: 1px !important;
}
.adv-report {
  display: flex;
  width: 100%;
  justify-content: space-between;
  padding: 10px 4px;
}
.picker-report {
  /* width: 32%; */
  padding-right: 8px;
  display: flex;
  align-items: center;
  gap: 5px;
}
.picker-report-one-month {
  /* width: 32%; */
  padding-right: 8px;
  display: flex;
  align-items: center;
  gap: 5px;
}
.customGrpHeaderLabel p {
  margin-left: 2px;
}
.kpi-line {
  margin: 0px 2px;
}
.kpi-section {
  margin-right: 2px;
}
.mandatorySign {
  color: red;
}
#model-mapping-menu_li {
  white-space: nowrap;
}
#model-mapping-grid_li {
  white-space: nowrap;
}

#selectPartsSource.MuiSelect-select:focus {
  border-radius: 0;
  background-color: transparent !important;
}
.header-disabled {
  pointer-events: none !important;
}

.matrix-upload .MuiDropzonePreviewList-removeButton {
  top: 20px !important;
}
.matrix-upload .MuiDropzoneArea-textContainer {
  display: flex;
  gap: 8px;
}
.goal-data-cell {
  text-align: right !important;
  padding-right: 30px !important;
}
.goal-title-cell {
  font-weight: 600;
}

.goal-title-cell b {
  font-weight: 600;
  text-decoration: underline;
}
.goal-title-cell p {
  font-size: 11px;
}
.kpi-report-2-name {
  display: flex;
  align-items: center;
  padding-left: 8px;
}
.copy-dlg {
  min-width: 100% !important;
}
.saveicon {
  height: 0.9em !important;
  margin-bottom: -2px;
  margin-right: -6px;
}
.select-date-mpistats {
  margin: 5px 0 5px 5px !important;
  padding-right: 0 !important;
}
.kpic-first-top {
  margin-top: 75px !important;
}
.kpic-second-top {
  margin-top: 45px !important;
}
.kpic-third-top {
  margin-top: 99px !important;
}
.kpig-first-top.MuiTooltip-tooltipPlacementTop {
  margin-top: -35px !important;
}
.kpig-popper {
  top: -20px !important;
}
.zero-top {
  margin-top: 0px !important;
}
@media (max-width: 1920px) {
  .middle-grid-tooltip {
    max-width: 400px !important;
    min-width: 370px !important;
  }
}
@media (max-width: 1440px) {
  .middle-grid-tooltip {
    max-width: 320px !important;
    min-width: 300px !important;
  }
}

@media (max-width: 1280px) {
  .middle-grid-tooltip {
    max-width: 290px !important;
    min-width: 250px !important;
  }
}

@media (min-width: 2304px) {
  .middle-grid-tooltip {
    max-width: 490px !important;
    min-width: 370px !important;
  }
}

@media (min-width: 2560px) {
  .middle-grid-tooltip {
    max-width: 490px !important;
    min-width: 370px !important;
  }
}
.MuiSwitch-colorSecondary.Mui-checked {
  color: #084573 !important;
}

.MuiSwitch-colorSecondary.Mui-checked + .MuiSwitch-track {
  background-color: #82a1b9 !important;
}
.card-selectors {
  width: 250px;
}

.grid-wrapper {
  width: 400px;
}

@media (max-width: 1366px) {
  .grid-wrapper {
    width: 350px;
  }
}
.makeStyles-content-2 .ag-center-cols-viewport {
  overflow-x: hidden;
}
