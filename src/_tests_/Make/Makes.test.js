import React from 'react';
import Makes from '../../views/Makes/Makes';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
jest.mock("../../views/Makes/ExcludedMakes.js", () => () => {
  return <mock-ExcludedMakes data-testid="ExcludedMakes"/>;
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Box: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Toolbar: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tab', () => ({
    Tab: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Link', () => ({
    Link: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/GetApp', () => ({
    ExportIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/lab/Alert', () => ({
    Alert: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/IconButton', () => ({
    IconButton: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Collapse', () => ({
    Collapse: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Close', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
  }));
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('Makes  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <Makes {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
