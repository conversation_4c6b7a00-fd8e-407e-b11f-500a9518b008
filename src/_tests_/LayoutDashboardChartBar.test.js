import React from 'react';
import ChatBar from '../layouts/Dashboard/ChatBar';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Avatar: () => Component => props => <Component classes="" {...props} />,
    Drawer: () => Component => props => <Component classes="" {...props} />,
    List: () => Component => props => <Component classes="" {...props} />,
    ListItem: () => Component => props => <Component classes="" {...props} />,
    ListItemAvatar: () => Component => props => <Component classes="" {...props} />,
    ListItemText: () => Component => props => <Component classes="" {...props} />,
    ListSubheader: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Send', () => ({
  SendIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AddPhotoAlternate', () => ({
  AddPhotoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AttachFile', () => ({
  AttachFileIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("src/utils/axios", () => () => {
  return <mock-axios data-testid="axios"/>;
});
jest.mock("src/components/StatusBullet", () => () => {
  return <mock-StatusBullet data-testid="StatusBullet"/>;
});
describe('ChatBar rendering ', () => {
 
  it('render ChatBar', () => {
    const wrapper = shallow(
      <ChatBar  open={true} onclose={jest.fn()} className={"chartbar"}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
