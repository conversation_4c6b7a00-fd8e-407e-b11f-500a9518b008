import React from 'react';
import WarrantyReference from '../views/WarrantyRatesVolumes/WarrantyReference';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
jest.mock("@react-keycloak/web");
jest.mock("src/components/Page", () => () => {
  return <mock-Page data-testid="Page"/>;
});
jest.mock("src/utils/apolloRootClientPostgres", () => () => {
  return <mock-makeApolloClient data-testid="makeApolloClient"/>;
});
jest.mock("src/utils/hasuraServices", () => () => {
  return <mock-hasuraServices data-testid="hasuraServices"/>;
});
jest.mock("src/graphql/queries", () => () => {
  return <mock-queries data-testid="queries"/>;
});

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Box: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  withStyles: () => Component => props => <Component classes="" {...props} />,
  }));
  jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));

  jest.mock('@material-ui/core/Tab', () => ({
    Tab: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Link', () => ({
    Link: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/GetApp', () => ({
    ExportIcon: () => Component => props => <Component classes="" {...props} />
  }));

  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));


  // jest.mock('@react-keycloak/web', () => ({
  //   withKeycloak: () => Component => props => <Component classes="" {...props} />
  // }));
  const props = {
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak: { 
      realmAccess:{
        roles: []
      }
    }
  };
describe('WorkMixVolume index rendering ', () => {
  // it('Renders correctly', () => {
  //   const component = renderer.create(
  //     <WarrantyReference {...props} 
  //     />
  //   );
  //   let tree = component.toJSON();
  //   expect(tree).toMatchSnapshot();
  // });
  it('render CustomHeaderGroup ', () => {
    const wrapper = shallow(
      <WarrantyReference {...props}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
