import React from 'react';
import Glossary from '../views/Glossary/Glossary';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/core', () => ({
  Box: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  fade: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/lab/Alert', () => ({
  Alert: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/IconButton', () => ({
  IconButton: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Collapse', () => ({
  Collapse: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Close', () => ({
  CloseIcon: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
describe('Glossary rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <Glossary
        keycloak={{
          realmAccess: { roles: ['admin'] }
        }}
        onGridReady={jest.fn()}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
