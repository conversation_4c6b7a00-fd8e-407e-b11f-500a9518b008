import React from 'react';
import Enzyme from 'enzyme';
import { shallow } from 'enzyme';
import Adapter from 'enzyme-adapter-react-16';
import Changelog from '../../views/ChangelogInternal/ChangelogInternal';
import toJson from 'enzyme-to-json';
Enzyme.configure({ adapter: new Adapter() });
jest.mock('@material-ui/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Box: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  fade: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock("../../components/PageHeader.js", () => () => {
  return <mock-PageHeader data-testid="PageHeader"/>;
});

describe('Test case for Changelog Component', () => {
  test('Validate no records in grid', () => {
    const wrapper = shallow(
      <Changelog  />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
    // const labelValue = wrapper.find('div');
    // expect(labelValue.length).toBe(3);
    // console.log("label=",labelValue);
  });
});