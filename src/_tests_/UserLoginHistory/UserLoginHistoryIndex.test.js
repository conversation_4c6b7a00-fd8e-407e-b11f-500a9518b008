import React from 'react';
import Index from '../../views/UserLoginHistory/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    useHistory: () => ({
      location: {
        search: '/help',
     },
   }),
  }));
jest.mock("../../views/UserLoginHistory/UserLoginHistory.js", () => () => {
    return <mock-index data-testid="index"/>;
  });
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    CircularProgress: () => Component => props => <Component classes="" {...props} />
}));
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('UserLoginHistory  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <Index {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});

