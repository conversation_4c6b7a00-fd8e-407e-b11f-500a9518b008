import React from 'react';
import UserLoginHistory from '../../views/UserLoginHistory/UserLoginHistory';
import Enzyme, { shallow, mount } from "enzyme";
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
// import Adapter from "enzyme-adapter-react-16";

// Enzyme.configure({ adapter: new Adapter() });
const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
jest.mock('@material-ui/core', () => ({
    Grid: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    LinearProgress: () => Component => props => <Component classes="" {...props} />,
    Box: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    FormControl: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
    Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
}));
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('UserLoginHistory  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <UserLoginHistory {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
describe("Search component", () => {
  test("renders", () => {
    const wrapper = shallow(<UserLoginHistory />);

    expect(wrapper.exists()).toBe(true);
  });
});
