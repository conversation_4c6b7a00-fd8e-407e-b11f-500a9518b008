import React from 'react';
import PricingModal from '../components/PricingModal';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Button: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
  InputLabel: () => Component => props => <Component classes="" {...props} />,
  TextareaAutosize: () => Component => props => (
    <Component classes="" {...props} />
  ),
  Typography: () => Component => props => <Component classes="" {...props} />,
  TextField: () => Component => props => <Component classes="" {...props} />,
  Dialog: () => Component => props => <Component classes="" {...props} />,
  FormControlLabel: () => Component => props => <Component classes="" {...props} />,
  Checkbox: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-phone-input-2', () => ({
  PhoneInput: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('../components/StylesProvider');
describe('Paytype index rendering ', () => {
 
  const keycloak = {
    tokenParsed:{
      given_name: '',
      family_name:''
    }
  }
  it('render Paytype master ', () => {
    const wrapper = shallow(
      <PricingModal  keycloak={keycloak}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
