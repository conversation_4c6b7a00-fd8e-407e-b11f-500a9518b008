import React from 'react';
import Login from '../views/Login/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
const mockStore = configureMockStore();
const store = mockStore({});

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardMedia: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Link: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Lock', () => ({
  LockIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('CPOverview rendering ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  it('render CPOverview ', () => {
    const wrapper = shallow(<Login />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
