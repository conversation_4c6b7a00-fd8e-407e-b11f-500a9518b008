import React from 'react';
import ExcludedMakes from '../views/Makes/ExcludedMakes';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import renderer from 'react-test-renderer';

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Box: () => Component => props => <Component classes="" {...props} />,
    LinearProgress: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    Toolbar: () => Component => props => <Component classes="" {...props} />,
    Grid: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Link', () => ({
    Link: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tab', () => ({
    Tab: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/GetApp', () => ({
    ExportIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Button', () => ({
    Button: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContentText', () => ({
    DialogContentText: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogTitle', () => ({
    DialogTitle: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/lab/Alert', () => ({
    Alert: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/IconButton', () => ({
    IconButton: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Collapse', () => ({
    Collapse: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Close', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
  }));
  
  jest.mock("src/actions", () => () => {
    return <mock-SET_REFRESH_STATUS data-testid="SET_REFRESH_STATUS"/>;
  });
  jest.mock("src/graphql/queries", () => () => {
    return <mock-queries data-testid="queries"/>;
  });
  jest.mock("src/utils/apolloRootClientPostgres", () => () => {
    return <mock-apolloRootClientPostgres data-testid="apolloRootClientPostgres"/>;
  });
  jest.mock("src/utils/hasuraServices", () => () => {
    return <mock-hasuraServices data-testid="hasuraServices"/>;
  });
  const props = { classes : ''}
describe('ExcludedMakes rendering ', () => {
  it('render ExcludedMakes ', () => {
    const component = renderer.create(
        <ExcludedMakes {...props} 
        />
      );
      let tree = component.toJSON();
      expect(tree).toMatchSnapshot();
    // const wrapper = shallow(
    //   <ExcludedMakes />
    // );
    // expect(toJson(wrapper)).toMatchSnapshot();
  });
});
