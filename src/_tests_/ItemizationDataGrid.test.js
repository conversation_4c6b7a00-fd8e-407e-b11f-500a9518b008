import React from 'react';
import ItemizationDataGrid from '../components/charts/ItemizationDataGrid';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("../components/charts/AlertDialog", () => () => {
  return <mock-AlertDialog data-testid="AlertDialog"/>;
});
jest.mock("src/components/CustomHeaderGroup", () => () => {
  return <mock-CustomHeaderGroup data-testid="CustomHeaderGroup"/>;
});
jest.mock("src/components/CustomHeaderGroup", () => () => {
  return <mock-CustomTooltip data-testid="CustomTooltip"/>;
});
jest.mock('@material-ui/lab', () => ({
  Alert: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Snackbar: () => Component => props => <Component classes="" {...props} />
}));

  const props = {
    zoomData: {
      labels: '',
    },
    dataArrSelected: [],
    rowData: []
  };
describe('ItemizationDataGrid  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <ItemizationDataGrid {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
