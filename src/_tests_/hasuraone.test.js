import { Apollo<PERSON>rovider } from '@apollo/client';
import { mount, ReactWrapper } from 'enzyme';
import { createMockClient } from 'mock-apollo-client';
import * as React from 'react';

// import { GET_DOG_QUERY, Dog } from './dog';
import { REFRESH_VIEWS_STATUS } from '../graphql/queries';
import { getRefreshViewsStatus } from '../views/RevenueStatement/index';

let wrapper: ReactWrapper;

beforeEach(() => {
  const mockClient = createMockClient();
  mockClient.setRequestHandler(
    REFRESH_VIEWS_STATUS,
    () => Promise.resolve({ data: { statelessCcPhysicalRwGetDataRefreshStatusLogs: { status: 'COMPLETE'} } }));

  wrapper = mount(
    <ApolloProvider client={mockClient}>
      <getRefreshViewsStatus  />
    </ApolloProvider>
  );
});

it('renders the dog name and breed', () => {
    console.log("mockClient=",wrapper);
  expect(wrapper.text()).toContain('Rufus is a Poodle');
});