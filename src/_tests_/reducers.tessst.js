import reducer from '../reducers/sessionReducer';
import * as actions from '../actions/sessionActions';

describe('todos reducer', () => {
  let todo = {
    id: '7ae5bfa3-f0d4-4fd3-8a9b-61676d67a3c8',
    title: 'Todo',
    project: 'Project',
    done: false,
    image:
      'https://raw.githubusercontent.com/andela-jkithome/image_files/master/images/image5.jpg',
    createdAt: '2017-03-02T23:04:38.003Z',
    modifiedAt: '2017-03-22T16:44:29.034Z'
  };
  let todos = [
    {
      id: '7ae5bfa3-f0d4-4fd3-8a9b-61676d67a3c8',
      title: 'Todo',
      project: 'Project',
      done: false,
      image:
        'https://raw.githubusercontent.com/andela-jkithome/image_files/master/images/image5.jpg',
      createdAt: '2017-03-02T23:04:38.003Z',
      modifiedAt: '2017-03-22T16:44:29.034Z'
    },
    {
      id: 'a9e01001-3750-4c88-9c67-ba3e68255317',
      title: 'New Todo',
      project: 'New Project',
      done: true,
      image:
        'https://raw.githubusercontent.com/andela-jkithome/image_files/master/images/image3.jpg',
      createdAt: '2017-03-18T06:45:57.337Z',
      modifiedAt: '2017-03-18T17:15:23.996Z'
    }
  ];
  it('should return the initial state', () => {
    expect(reducer(undefined, {})).toMatchSnapshot();
  });

  it('should handle SESSION_LOGIN', () => {
    expect(
      reducer(
        {
          serviceAdvisor: ['All'],
          loggedIn: true,
          prevPath: '',
          currentPath: '',
          token: '',
          favouriteCharts: [],
          userName: '',
          payTypeInitialTab: 'one',
          storeSelected: '',
          storeName: '',
          storeId: '',
          payTypeErrors: false,
          user: {
            first_name: 'Shen',
            last_name: 'Zhi',
            email: '<EMAIL>',
            avatar: '/images/avatars/avatar_11.png',
            bio: 'Brain Director',
            role: 'ADMIN'
          }
        },
        {
          type: 'SESSION_LOGIN'
        }
      )
    ).toMatchSnapshot();
  });

  it('should handle SESSION_LOGOUT', () => {
    expect(
      reducer(
        {
          loggedIn: false,
          user: {
            role: 'GUEST'
          }
        },
        {
          type: 'SESSION_LOGOUT',
          todos
        }
      )
    ).toMatchSnapshot();
  });

  it('should handle SET_SERVICE_ADVISOR', () => {
    expect(
      reducer(
        {
          serviceAdvisor: []
        },
        {
          type: 'SET_SERVICE_ADVISOR'
        }
      )
    ).toMatchSnapshot();
  });
});
