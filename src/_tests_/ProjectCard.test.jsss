import React from 'react';
import ProjectCard from '../components/ProjectCard';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';

const mockStore = configureMockStore();
const store = mockStore({});

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
// jest.mock('@material-ui/icons/AccessTime', () => ({
//   AccessTimeIcon: () => Component => props => (
//     <Component classes="" {...props} />
//   )
// }));
jest.mock('@material-ui/icons/Favorite', () => ({
  FavoriteIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/FavoriteBorder', () => ({
  FavoriteBorderIcon: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/icons/Share', () => ({
  ShareIcon: () => Component => props => <Component classes="" {...props} />
}));
// jest.mock('@material-ui/icons/Send', () => ({
//   SendIcon: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/icons/AddPhotoAlternate', () => ({
//   AddPhotoIcon: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/icons/AttachFile', () => ({
//   AttachFileIcon: () => Component => props => (
//     <Component classes="" {...props} />
//   )
// }));
jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardActionArea: () => Component => props => (
    <Component classes="" {...props} />
  ),
  CardMedia: () => Component => props => <Component classes="" {...props} />,
  Avatar: () => Component => props => <Component classes="" {...props} />,
  Link: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  colors: () => Component => props => <Component classes="" {...props} />
}));
describe('ProjectCard render ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  const colors = { grey: [600] };
  const title = 'CP Overview';
  const project = {
      liked:'',
      author:{
          name:'',
          avatar:''
      },
      updated_at:'',
      title:'',
      tags:'',
      price:'',
      location:'',
      type:''
  }
  it('render ProjectCard ', () => {
    const wrapper = shallow(<ProjectCard project={project} colors={colors}/>);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
