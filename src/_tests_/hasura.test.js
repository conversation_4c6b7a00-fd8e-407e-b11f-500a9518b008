
import React from 'react';
import TestRenderer from 'react-test-renderer';
import { MockedProvider } from '@apollo/client/testing';
import { REFRESH_VIEWS_STATUS } from '../graphql/queries';
import { hasuraServices } from '../utils/hasuraServices';
import { renderHook } from '@testing-library/react-hooks';
import { gql, useQuery } from '@apollo/client';

const mocks = [
    {
      
      request: {
        query: REFRESH_VIEWS_STATUS,
        // variables: {
        //   name: "<PERSON>"
        // }
      },
      result: {
        data: {
          statelessCcPhysicalRwGetDataRefreshStatusLogs: { 
            node:{
              refreshBy : '',
              refreshEnd:'',
              refreshStart:'',
              refreshDate:'',
              status:'',
              storeId:''
            }
           }
        }
      }
    }
  ];
  it('should handle a simple query', async () => {
    const query = gql`{ statelessCcPhysicalRwGetDataRefreshStatusLogs }`;
    // const mocks = [
    //   {
    //     request: { query },
    //     result: { data: { hello: "world" } },
    //   },
    // ];

    const wrapper = ({ children }) => (
      <MockedProvider mocks={mocks} addTypename={false} >{children}</MockedProvider>
    );

    const { result, waitForNextUpdate } = renderHook(
      () => useQuery(REFRESH_VIEWS_STATUS),
      { wrapper },
    );

    expect(result.current.loading).toBe(true);
    expect(result.current.data).toBe(undefined);
    await waitForNextUpdate();
    expect(result.current.loading).toBe(false);
    // expect(result.current).toEqual({ hello: "world" });
    console.log("result=",result.current);
  });
// it('displays a loading message when fetching', () => {
//     const data = {result: {
//         data: {
//           dog: { id: "1", name: "Buck", breed: "bulldog" }
//         }
//       }};
//   const component = TestRenderer.create(
//     <MockedProvider mocks={mocks} addTypename={false}>
//       <hasuraServices name="Buck" getRefreshViewsStatus={mocks} />
//     </MockedProvider>,
//   );

//   const tree = component.toJSON();
//   expect(tree.children).toContain(data);
//   console.log("data value=",tree);
// });



// import React from 'react';
// // import MockedProvider from '@apollo/react-testing';
// // import renderer from 'react-test-renderer';
// // import { REFRESH_VIEWS_STATUS } from '../graphql/queries';
// // import { getRefreshViewsStatus } from '../utils/hasuraServices';

// import "@testing-library/jest-dom";
// import { render, screen } from "@testing-library/react";
// // // import { MockedProvider } from "@apollo/client/testing";
// // import { REFRESH_VIEWS_STATUS } from '../graphql/queries';
// // import { getRefreshViewsStatus } from '../utils/hasuraServices';

// import TestRenderer from 'react-test-renderer';
// import { MockedProvider } from '@apollo/client/testing';
// import { REFRESH_VIEWS_STATUS } from '../graphql/queries';
// import { getRefreshViewsStatus } from '../utils/hasuraServices';
// const mocks = [
//   {
//     request: {
//       query: REFRESH_VIEWS_STATUS,
//       variables: {
//         name: "Buck"
//       }
//     },
//     result: {
//       data: {
//         dog: { id: "1", name: "Buck", breed: "bulldog" }
//       }
//     }
//   }
// ];

// it("renders without error", async () => {
//   render(
//     <MockedProvider mocks={mocks} addTypename={false}>
//       <getRefreshViewsStatus/>
//     </MockedProvider>
//   );
// //   const items = await screen.findAllByRole('listitem');
// //   expect(await screen.findAllByRole('listitem')).toBeInTheDocument();

//   const items = await screen.findAllByRole('listitem');

//     expect(items).toHaveLength(2);
// });




// // it('displays a loading message when fetching', () => {
// //   const component = TestRenderer.create(
// //     <MockedProvider mocks={mocks} addTypename={false}>
// //       <getRefreshViewsStatus name="Buck" />
// //     </MockedProvider>,
// //   );

// //   const tree = component.toJSON();
// //   expect(tree).toContain('Loading...');
// // });