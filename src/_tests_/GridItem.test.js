import React from 'react';
import GridItem from '../views/Home/GridItem';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import renderer from 'react-test-renderer';
jest.mock("src/components/charts/KPISummaryCharts", () => () => {
  return <mock-KPISummaryCharts data-testid="KPISummaryCharts"/>;
});
describe('Home render ', () => {
  it('render Dashboard ', () => {
    const wrapper = shallow(<GridItem chartId={942} data={[]}/>);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render Dashboard ', () => {
    
    const toggleInstance = shallow(<GridItem chartId={942} data={[]}/>);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(2);
    expect(toggleInstance.find('div').children().length).toBe(1);
  });
});
