import React from 'react';
import Auth from '../layouts/Auth/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock("../layouts/Auth/Topbar.js", () => () => {
  return <mock-Topbar data-testid="Topbar"/>;
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  LinearProgress: () => Component => props => <Component classes="" {...props} />
}));
describe('Layout', () => {
  const route = {
    routes: ''
  };
  it('Auth render properly', () => {
    const wrapper = shallow(<Auth route={route} />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render Header ', () => {
    const toggleInstance = shallow(<Auth route={route}/>);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(2);
    const element=toggleInstance.find('Suspense');
    expect(element.length).toBe(1);
    expect(toggleInstance.find('div').children().length).toBe(2);
    expect(toggleInstance.find('Suspense').children().length).toBe(0);
  });
});
