import React from 'react';
import Dropdown from '../components/Dropdown';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/icons/ArrowDropDown', () => ({
    ArrowDropDownIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Done', () => ({
    DoneIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/EditOutlined', () => ({
    EditOutlinedIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-perfect-scrollbar', () => ({
    ScrollBar: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/SwapHorizontalCircleOutlined', () => ({
    SwapHorizontalCircleOutlinedIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Button: () => Component => props => <Component classes="" {...props} />,
  Checkbox: () => Component => props => <Component classes="" {...props} />,
  FormControlLabel: () => Component => props => <Component classes="" {...props} />,
  Menu: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardActions: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  IconButton: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  }));
  
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('Dropdown  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <Dropdown storeName={["selected"]} selected={'abc'}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
