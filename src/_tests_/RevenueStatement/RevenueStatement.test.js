import React from 'react';
import RevenueStatementGrid from '../../views/RevenueStatement/RevenueStatementGrid';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import configureMockStore from 'redux-mock-store';
import enzyme, {shallow} from 'enzyme';
import renderer from 'react-test-renderer';
const mockStore = configureMockStore();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
const mockDispatch = jest.fn();
jest.mock('@material-ui/core/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />,
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-router', () => ({
  ...jest.requireActual('react-router'),
  useHistory: () => ({
    location: {
      search: '/help',
   },
 }),
}));
jest.mock('@material-ui/core', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Box: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  FormControl: () => Component => props => <Component classes="" {...props} />,
  InputLabel: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  Select: () => Component => props => <Component classes="" {...props} />,
  Toolbar: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  TableBody: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/core/FormControlLabel', () => ({
  FormControlLabel: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Table', () => ({
  Table: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/pickers', () => ({
  DatePicker: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TableCell', () => ({
  TableCell: () => Component => props => <Component classes="" {...props} />
}));  
jest.mock('@material-ui/core/TableHead', () => ({
  TableHead: () => Component => props => <Component classes="" {...props} />
})); 
jest.mock('@material-ui/core/TableRow', () => ({
  TableRow: () => Component => props => <Component classes="" {...props} />
}));  
jest.mock('@material-ui/core/TextField', () => ({
  TextField: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
  Tab: () => Component => props => <Component classes="" {...props} />
})); 
jest.mock('@material-ui/core/Tabs', () => ({
  Tabs: () => Component => props => <Component classes="" {...props} />
}));  
jest.mock('@material-ui/lab/Alert', () => ({
  Alert: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Chip', () => ({
  Chip: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ArrowBack', () => ({
  ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("../../components/Page.js", () => () => {
  return <mock-Page data-testid="Page"/>;
});  
jest.mock("../../components/LoaderSkeleton.js", () => () => {
  return <mock-LoaderSkeleton data-testid="LoaderSkeleton"/>;
}); 
jest.mock("../../views/RevenueStatement/SummaryDataHeader", () => () => {
  return <mock-SummaryDataHeader data-testid="SummaryDataHeader"/>;
});
jest.mock("../../views/RevenueStatement/SummaryDataGrid", () => () => {
  return <mock-SummaryDataGrid data-testid="SummaryDataGrid"/>;
});
jest.mock("../../views/RevenueStatement/RevenueByComponentGrid", () => () => {
  return <mock-RevenueByComponentGrid data-testid="RevenueByComponentGrid"/>;
});
jest.mock("../../views/RevenueStatement/RevenueByCategoryGrid", () => () => {
  return <mock-RevenueByCategoryGrid data-testid="RevenueByCategoryGrid"/>;
});
jest.mock("../../views/RevenueStatement/JobLevelBreakDownGrid", () => () => {
  return <mock-JobLevelBreakDownGrid data-testid="JobLevelBreakDownGrid"/>;
});
const props = {
  history : {
    location:
      {
        state: ''
      }
  }      
}
describe('Revenue Statement rendering ', () => {
  it('render RevenueStatementGrid ', () => {
    const wrapper = shallow(
      <RevenueStatementGrid {...props} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('Renders correctly', () => {
    const component = renderer.create(
      <RevenueStatementGrid  {...props} />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
  const handleChange = jest.fn();
  it('render Index ', () => {
    const toggleInstance = shallow(<RevenueStatementGrid {...props} />);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(1);
    const elem=toggleInstance.find('Paper');
    expect(elem.length).toBe(2);
    expect(toggleInstance.find('div').children().length).toBe(2);
    expect(toggleInstance.find('Paper').children().length).toBe(3);
    // expect(toggleInstance.find('Input').placeholder()).toBe("What's on your mind");
    // const items = toggleInstance.find('Tooltip[title="Send"]')
    // expect(items).toHaveLength(itemsArray.length)
    // expect(items.first().text()).toEqual('Shopping')
    // expect(wrapper.find('button').text()).toEqual('Toggle');
    // console.log("items=",items);
    // const result=toggleInstance.find('IconButton').simulate('click')
    // expect(result).toBe('some action here');
    const event = {
      preventDefault() {},
      target: { value: 'the-value' }
    };
    const fileInputRef = {
      preventDefault() {},
      current: { click() {} }
    };
    const handleAttach = jest.fn();
    const component = shallow(<Index onClick={handleAttach} />);
    component.find('#IconButton').simulate('click', fileInputRef);
    expect(handleChange).toBe(event);
  });
});
