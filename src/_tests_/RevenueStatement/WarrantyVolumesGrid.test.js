import React from 'react';
import WarrantyVolumesGrid from '../../views/RevenueStatement/WarrantyVolumesGrid';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import configureMockStore from 'redux-mock-store';
import enzyme, {shallow} from 'enzyme';
import renderer from 'react-test-renderer';
const mockStore = configureMockStore();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />,
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("../../components/Page.js", () => () => {
    return <mock-Page data-testid="Page"/>;
  });  
  jest.mock("../../views/RevenueStatement/RevenueStatementGrid", () => () => {
    return <mock-RevenueStatementGrid data-testid="RevenueStatementGrid"/>;
  });
jest.mock('@material-ui/core', () => ({
  TableBody: () => Component => props => <Component classes="" {...props} />,
  Table: () => Component => props => <Component classes="" {...props} />,
  TableHead: () => Component => props => <Component classes="" {...props} />,
})); 
jest.mock('@material-ui/core/TableCell', () => ({
    TableCell: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TableRow', () => ({
    TableRow: () => Component => props => <Component classes="" {...props} />
})); 
jest.mock("../../views/RevenueStatement/SummaryDataGrid", () => () => {
  return <mock-SummaryDataGrid data-testid="SummaryDataGrid"/>;
});
jest.mock("../../views/RevenueStatement/SummaryDataHeader", () => () => {
  return <mock-SummaryDataHeader data-testid="SummaryDataHeader"/>;
});
const initialState = true;
React.useState = jest.fn().mockReturnValue([initialState , {}]);
const props = {
  type:'labor',
  currMonth:'02',
  optMonth:'03'   
}
describe('Revenue Statement rendering ', () => {
  it('render WarrantyVolumesGrid ', () => {
    const wrapper = shallow(
      <WarrantyVolumesGrid {...props} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('Renders correctly', () => {
    const component = renderer.create(
      <WarrantyVolumesGrid  {...props} />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
  const handleChange = jest.fn();
  it('render Index ', () => {
    const toggleInstance = shallow(<WarrantyVolumesGrid {...props} />);
    const elements=toggleInstance.find('Table');
    expect(elements.length).toBe(1);
    const elem=toggleInstance.find('TableHead');
    expect(elem.length).toBe(1);
    expect(toggleInstance.find('Table').children().length).toBe(2);
    expect(toggleInstance.find('TableHead').children().length).toBe(1);
  });
});
