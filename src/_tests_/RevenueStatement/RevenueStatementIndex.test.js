import React from 'react';
import Index from '../../views/RevenueStatement/index';
// import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import configureMockStore from 'redux-mock-store';
import enzyme, {shallow} from 'enzyme';
import renderer from 'react-test-renderer';
const mockStore = configureMockStore();

const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Card: () => Component => props => <Component classes="" {...props} />,
    CardContent: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => (
    <Component classes="" {...props} />
    ),
    Input: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Send', () => ({
  SendIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AddPhotoAlternate', () => ({
  AddPhotoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AttachFile', () => ({
    AttachFileIcon: () => Component => props => <Component classes="" {...props} />
  }));
// jest.mock('@react-keycloak/web', () => ({
//   withKeycloak: () => Component => props => <Component classes="" {...props} />
// }));
jest.mock("../../components/Page.js", () => () => {
  return <mock-Page data-testid="Page"/>;
});  
jest.mock("../../components/LoaderSkeleton.js", () => () => {
  return <mock-LoaderSkeleton data-testid="LoaderSkeleton"/>;
});  
jest.mock("../../views/RevenueStatement/RevenueStatementGrid.js", () => () => {
  return <mock-RevenueStatementGrid data-testid="RevenueStatementGrid"/>;
});
const props = {
  history : {
    location:
      {
        state: ''
      }
  }      
}
describe('Revenue Statement rendering ', () => {
  it('render Index ', () => {
    const wrapper = shallow(
      <Index {...props} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('Renders correctly', () => {
    const component = renderer.create(
      <Index  {...props} />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
  const handleChange = jest.fn();
  // it('render Index ', () => {
  //   const toggleInstance = shallow(<Index onChange={handleChange} />);
  //   const elements=toggleInstance.find('Tooltip');
  //   expect(elements.length).toBe(3);
  //   const elem=toggleInstance.find('Card');
  //   expect(elem.length).toBe(1);
  //   const eleme=toggleInstance.find('Input');
  //   expect(eleme.length).toBe(1);
  //   const elemen=toggleInstance.find('CardContent');
  //   expect(elemen.length).toBe(1);
  //   const element1=toggleInstance.find('Paper');
  //   expect(element1.length).toBe(1);
  //   const element2=toggleInstance.find('Divider');
  //   expect(element2.length).toBe(1);
  //   expect(toggleInstance.find('CardContent').children().length).toBe(6);
  //   expect(toggleInstance.find('Card').children().length).toBe(1);
  //   expect(toggleInstance.find('input').children().length).toBe(0);
  //   expect(toggleInstance.find('Paper').children().length).toBe(1);
  //   expect(toggleInstance.find('Input').children().length).toBe(0);
  //   // expect(toggleInstance.find('IconButton').children().length).toBe(3);
  //   expect(toggleInstance.find('SendIcon').children().length).toBe(0);
  //   expect(toggleInstance.find('Divider').children().length).toBe(0);
  //   expect(toggleInstance.find('AddPhotoIcon').children().length).toBe(0);
  //   expect(toggleInstance.find('AttachFileIcon').children().length).toBe(0);
  //   // expect(toggleInstance.find('Input').placeholder()).toBe("What's on your mind");
  //   // const items = toggleInstance.find('Tooltip[title="Send"]')
  //   // expect(items).toHaveLength(itemsArray.length)
  //   // expect(items.first().text()).toEqual('Shopping')
  //   // expect(wrapper.find('button').text()).toEqual('Toggle');
  //   // console.log("items=",items);
  //   // const result=toggleInstance.find('IconButton').simulate('click')
  //   // expect(result).toBe('some action here');
  //   const event = {
  //     preventDefault() {},
  //     target: { value: 'the-value' }
  //   };
  //   const fileInputRef = {
  //     preventDefault() {},
  //     current: { click() {} }
  //   };
  //   const handleAttach = jest.fn();
  //   const component = shallow(<Index onClick={handleAttach} />);
  //   component.find('#IconButton').simulate('click', fileInputRef);
  //   expect(handleChange).toBe(event);
  // });
});
