// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Revenue Statement rendering  Renders correctly 1`] = `<div />`;

exports[`Revenue Statement rendering  render RevenueStatementGrid  1`] = `
<div>
  <Paper
    square={true}
    style={
      Object {
        "borderColor": "#000",
      }
    }
  >
    <[object Object]
      aria-label="icon label tabs example"
      indicatorColor="secondary"
      showrefresh={true}
      textColor="secondary"
      variant="fullWidth"
    >
      <[object Object]
        label={
          <div>
            Detail Summary
          </div>
        }
        style={
          Object {
            "backgroundColor": "#F4E1E7",
            "border": "1px solid #C2185B",
            "color": "#c2185b",
            "paddingRight": 182,
            "textTransform": "none",
          }
        }
        value="one"
      />
    </[object Object]>
  </Paper>
  <Paper
    square={true}
    style={
      Object {
        "margin": 8,
        "padding": 20,
      }
    }
  >
    <br />
    <Component />
  </Paper>
</div>
`;
