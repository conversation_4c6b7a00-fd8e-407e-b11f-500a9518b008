// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Revenue Statement rendering  Renders correctly 1`] = `null`;

exports[`Revenue Statement rendering  render WarrantyVolumesGrid  1`] = `
<Table
  aria-label="purchases"
  style={
    Object {
      "marginBottom": 10,
      "marginTop": 10,
    }
  }
>
  <TableHead>
    <Component
      className="headerTop"
    >
      <Component
        className="title"
        colSpan={1}
      >
        RO Count
      </Component>
      <Component
        className="title"
      >
        Sold Hours
      </Component>
      <Component
        className="title"
      >
        Actual Hours
      </Component>
      <Component
        className="title"
        colSpan={1}
      >
        Cost
      </Component>
      <Component
        className="title"
      >
        Sales
      </Component>
      <Component
        className="title"
        colSpan={1}
      >
        Rate
      </Component>
      <Component
        className="title"
        colSpan={4}
        style={
          Object {
            "backgroundColor": "#ddeaf4",
            "width": "20%",
          }
        }
      >
        Labor
      </Component>
      <Component
        className="title"
        colSpan={1}
      >
        Rate
      </Component>
      <Component
        className="title"
      >
        Sales
      </Component>
      <Component
        className="title"
        colSpan={1}
      >
        Cost
      </Component>
      <Component
        className="title"
      >
        Actual Hours
      </Component>
      <Component
        className="title"
      >
        Sold Hours
      </Component>
      <Component
        className="title"
      >
        RO Count
      </Component>
    </Component>
  </TableHead>
  <TableBody />
</Table>
`;
