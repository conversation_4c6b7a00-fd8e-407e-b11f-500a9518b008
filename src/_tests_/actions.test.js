import React from 'react';
import sessionActions from '../actions/sessionActions';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

const mockStore = configureMockStore();
const store = mockStore({});
describe("PostCard Error ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });
    it("render Error ", () => {
        const wrapper =  shallow(
                    <sessionActions
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
});   