import React from 'react';
import AlertDialog from '../components/charts/AlertDialog';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';

jest.mock("src/views/SearchByRo", () => () => {
  return <mock-SearchByRo data-testid="SearchByRo"/>;
});
jest.mock('@material-ui/core/Box', () => ({
  Box: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Dialog', () => ({
  Dialog: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContent', () => ({
  DialogContent: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/IconButton', () => ({
  IconButton: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Close', () => ({
  CloseIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Slide', () => ({
  Slide: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />
}))

jest.mock('@material-ui/core/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
// jest.mock('@material-ui/core', () => ({
//     Box: () => Component => props => <Component classes="" {...props} />,
//     Dialog: () => Component => props => <Component classes="" {...props} />,
//     DialogContent: () => Component => props => <Component classes="" {...props} />,
//     IconButton: () => Component => props => (
//     <Component classes="" {...props} />
//   ),
//   CloseIcon: () => Component => props => <Component classes="" {...props} />,
//   Slide: () => Component => props => <Component classes="" {...props} />,
//   Grid: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock(SearchByRo);
// jest.mock('material-ui/lab/Alert', () => ({
//   Alert: () => Component => props => <Component classes="" {...props} />
// }));
describe('Alert Dialog rendering ', () => {
  it('render Alert Dialog', () => {
    const wrapper = shallow(
      <AlertDialog  />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
