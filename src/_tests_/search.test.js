import React from 'react';
import Search from '../components/SearchBar/Search';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Paper: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  Input: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Search', () => ({
  SearchIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('Paytype index rendering ', () => {
  it('render Paytype master ', () => {
    const wrapper = shallow(
      <Search />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
