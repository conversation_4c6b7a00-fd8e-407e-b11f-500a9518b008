import React from 'react';
import HeaderComponent from '../components/HeaderComponent';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Grid: () => Component => props => <Component classes="" {...props} />
}));
  
describe('HeaderComponent  rendering ', () => {
  it('Renders correctly', () => {
        const wrapper = shallow(
          <HeaderComponent  />
        );
        expect(toJson(wrapper)).toMatchSnapshot();
      });
    // const component = renderer.create(
    //   <HeaderComponent 
    //   />
    // );
    // let tree = component.toJSON();
    // expect(tree).toMatchSnapshot();
});
