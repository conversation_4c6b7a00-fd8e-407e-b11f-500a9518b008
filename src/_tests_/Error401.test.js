import React from 'react';
import Error401 from '../views/Error401';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Typography: () => Component => props => <Component classes="" {...props} />,
    Button: () => Component => props => <Component classes="" {...props} />,
    useTheme: () => Component => props => <Component classes="" {...props} />,
    useMediaQuery: () => Component => props => <Component classes="" {...props} />,
}));

describe('Error401 rendering ', () => {
  it('render Error401 ', () => {
    const wrapper = shallow(
      <Error401  useMediaQuery={jest.fn()} theme={jest.fn()} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render Error401 ', () => {
    const toggleInstance = shallow(<Error401  useMediaQuery={jest.fn()} theme={jest.fn()}  />);
    const elements=toggleInstance.find('Page');
    expect(elements.length).toBe(4);
    const ele=toggleInstance.find('input');
    expect(ele.length).toBe(1);
    const elem=toggleInstance.find('img');
    expect(elem.length).toBe(1);
    const eleme=toggleInstance.find('Typography');
    expect(eleme.length).toBe(2);
    const elemen=toggleInstance.find('Link');
    expect(elemen.length).toBe(1);
    const element=toggleInstance.find('Button');
    expect(element.length).toBe(0);
    expect(toggleInstance.find('div').children().length).toBe(7);
    expect(toggleInstance.find('input').children().length).toBe(0);
    expect(toggleInstance.find('img').children().length).toBe(0);
    expect(toggleInstance.find('Typography').children().length).toBe(6);
    expect(toggleInstance.find('Link').children().length).toBe(1);
    expect(toggleInstance.find('Button').children().length).toBe(0);
  });
});
