import React from 'react';
import LaborMisses from '../views/LaborMisses/LaborMisses';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''},
  selectedMonthYear:'',
  selectedToggle:'',
  selectedFilter:'duration',
  toggleOptions:{
      yesterday:['1010/2021'],
      lastweekstartdate:['1010/2021'],
      lastweekenddate:['1010/2021'],
      mtdstartdate:['1010/2021'],
      mtdenddate:['1010/2021'],
      ytdstartdate:['1010/2021'],
      ytdenddate:['1010/2021'],

      lastquarterstartdate:['1010/2021'],
      lastquarterenddate:['1010/2021'],
      lastyearstartdate:['1010/2021'],
      lastyearenddate:['1010/2021'],
      dayBeforeYesterday:['1010/2021'],
    }
});
jest.mock('@material-ui/core/Grid', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core', () => ({
    Typography: () => Component => props => <Component classes="" {...props} />,
    FormControl: () => Component => props => <Component classes="" {...props} />,
    InputLabel: () => Component => props => <Component classes="" {...props} />,
    LinearProgress: () => Component => props => (
    <Component classes="" {...props} />
    ),
    Box: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    MenuItem: () => Component => props => <Component classes="" {...props} />,
    Select: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    FormControlLabel: () => Component => props => <Component classes="" {...props} />,
    RadioGroup: () => Component => props => <Component classes="" {...props} />,
    Radio: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
  Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core', () => ({
    Button: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock("../components/Page.js", () => () => {
    return <mock-page data-testid="Page"/>;
  });
  jest.mock('../utils/hasuraServices.js', () => () => {
    return <mock-hasuraServices data-testid="hasuraServices"/>;
  });
  jest.mock('../utils/Utils.js', () => () => {
    return <mock-Utils data-testid="Utils"/>;
  });
  const props = {
    selectedFilter:'',
    selectedToggle:'',
    parent:'',
    mainLabel:'',
    selectedMonthYear:'10/2021',
    titleContainer:'',
      history:{
          location:{
              state:{
                selectedFilter:'',
                selectedToggle:'',
                selectedMonthYear:'10/2021'
              }
          }
        }
    };
describe('LaborMisses rendering ', () => {
 
  it('render LaborMisses ', () => {
    // const wrapper = shallow(
    //   <LaborMisses  {...props}/>
    // );
    // expect(toJson(wrapper)).toMatchSnapshot();

    const component = renderer.create(
        <LaborMisses {...props} 
        />
      );
      let tree = component.toJSON();
      expect(tree).toMatchSnapshot();
  });
});
