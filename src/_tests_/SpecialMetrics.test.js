import React from 'react';
import Charts from '../views/SpecialMetrics/Charts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ArrowBack', () => ({
    ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
  }));
  
  
  jest.mock("src/components/charts/DashboardBarRenderer", () => () => {
    return <mock-DashboardBarRenderer data-testid="DashboardBarRenderer"/>;
  });
  jest.mock("src/components/DashboardActions", () => () => {
    return <mock-DashboardActions data-testid="DashboardActions"/>;
  });
  jest.mock("src/components/PageHeader", () => () => {
    return <mock-PageHeader data-testid="PageHeader"/>;
  });

  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('WarrantyRatesGrid  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <Charts {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
