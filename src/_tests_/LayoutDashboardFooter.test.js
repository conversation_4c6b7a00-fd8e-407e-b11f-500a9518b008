import React from 'react';
import ChatBar from '../layouts/Dashboard/Footer';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
jest.mock('@material-ui/core/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Typography', () => ({
    Typography: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Paper', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />
}));
describe('ChatBar rendering ', () => {
 
  it('render ChatBar', () => {
    const wrapper = shallow(
      <ChatBar  open={true} onclose={jest.fn()} className={"chartbar"}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
