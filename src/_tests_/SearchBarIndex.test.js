import React from 'react';
import Index from '../components/SearchBar/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/icons/FilterList', () => ({
  FilterListIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
  jest.mock('@material-ui/lab', () => ({
    ToggleButton: () => Component => props => <Component classes="" {...props} />,
    ToggleButtonGroup: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Add', () => ({
    AddIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/DeleteOutlined', () => ({
    DeleteIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Close', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandMore', () => ({
    ExpandMoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandLess', () => ({
    ExpandLessIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/icons/Search', () => ({
  SearchIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('SearchBar index rendering ', () => {
  it('render SearchBar index ', () => {
    const wrapper = shallow(
      <Index onFilter={jest.fn()} onSearch={jest.fn()} className={''}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render SearchBar index ', () => {
    const mockHandleFilterOpen = jest.fn();
    const wrapper = shallow(<Index handleFilterOpen={mockHandleFilterOpen}/>);
    const event = {openFilter: true};
    wrapper.find('Button').at(0).simulate('click', event);
  });
});
