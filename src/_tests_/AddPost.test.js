import React from 'react';
import AddPost from '../components/AddPost';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import configureMockStore from 'redux-mock-store';
const mockStore = configureMockStore();

const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Card: () => Component => props => <Component classes="" {...props} />,
    CardContent: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => (
    <Component classes="" {...props} />
    ),
    Input: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Send', () => ({
  SendIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AddPhotoAlternate', () => ({
  AddPhotoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AttachFile', () => ({
    AttachFileIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('../components/StylesProvider');
describe('Addpost rendering ', () => {
 
  it('render AddPost ', () => {
    
    const wrapper = shallow(
      <AddPost  />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  const handleChange = jest.fn();
  it('render AddPost ', () => {
    
    const toggleInstance = shallow(<AddPost onChange={handleChange} />);
    const elements=toggleInstance.find('Tooltip');
    expect(elements.length).toBe(3);
    const element=toggleInstance.find('IconButton');
    expect(element.length).toBe(3);
    const elem=toggleInstance.find('Card');
    expect(elem.length).toBe(1);
    const eleme=toggleInstance.find('Input');
    expect(eleme.length).toBe(1);
    expect(toggleInstance.find('CardContent').children().length).toBe(6);
    expect(toggleInstance.find('Card').children().length).toBe(1);
    expect(toggleInstance.find('input').children().length).toBe(0);
    expect(toggleInstance.find('Paper').children().length).toBe(1);
    expect(toggleInstance.find('Input').children().length).toBe(0);
    expect(toggleInstance.find('IconButton').children().length).toBe(3);
    expect(toggleInstance.find('SendIcon').children().length).toBe(0);
    expect(toggleInstance.find('Divider').children().length).toBe(0);
    expect(toggleInstance.find('AddPhotoIcon').children().length).toBe(0);
    expect(toggleInstance.find('AttachFileIcon').children().length).toBe(0);
    // expect(toggleInstance.find('Input').placeholder()).toBe("What's on your mind");
    // const items = toggleInstance.find('Tooltip[title="Send"]')
    // expect(items).toHaveLength(itemsArray.length)
    // expect(items.first().text()).toEqual('Shopping')
    // expect(wrapper.find('button').text()).toEqual('Toggle');
    // console.log("items=",items);
  });
});
