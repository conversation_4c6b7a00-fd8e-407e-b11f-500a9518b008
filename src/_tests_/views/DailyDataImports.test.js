// import React from 'react';
// import { shallow } from 'enzyme';
// import DailyDataImports from '../../views/DailyDataImports/DailyDataImports';
// import { createTheme, ThemeProvider } from '@material-ui/core/styles';
// const theme = createTheme();

// describe('DailyDataImports', () => {
//   it('renders correctly', () => {
//     const wrapper = shallow(
//         <ThemeProvider theme={theme}>
//           <DailyDataImports classes={{}} />
//         </ThemeProvider>
//       );
//     // const wrapper = shallow(<DailyDataImports classes={{}} />);
//     console.log("wrapper=",wrapper.debug());
//     expect(wrapper).toMatchSnapshot();
//   });
// });

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
// import React from 'react';
// import { mount } from 'enzyme';
import DailyDataImports from '../../views/DailyDataImports/DailyDataImports';
import { createTheme, ThemeProvider } from '@material-ui/core/styles';
const theme = createTheme();
const props = {
  history: { location: { state: { tabSelection: 'one' } } },
  months: [{ monthYear: '02/22' }],
  location: {
    pathname: '/WarrantyReferenceLabor'
  },
  type: 'ss',
  category: 'aa',
  reporttype: 'rr',
  keycloak: {
    realmAccess: {
      roles: []
    }
  },
  selectedDates: []
};
describe('MyComponent', () => {
  // it('should render correctly', () => {
  //   const wrapper = mount(<DailyDataImports />);
  //   expect(wrapper.html()).toMatchSnapshot();
  // });
  test('renders without error', () => {
    render(
      <ThemeProvider theme={theme}>
        <DailyDataImports {...props} />
      </ThemeProvider>
    );
    // You can add assertions here to check if the component renders correctly
  });

  test('displays the correct title', () => {
    render(<DailyDataImports />);
    const title = screen.getByText('Special Metrics');
    expect(title).toBeInTheDocument();
  });

  test('clicking a button triggers the correct action', () => {
    const mockAction = jest.fn();
    render(<DailyDataImports onButtonClick={mockAction} />);
    const button = screen.getByRole('button');

    // Simulate a button click
    fireEvent.click(button);

    // Check if the action was triggered
    expect(mockAction).toHaveBeenCalled();
  });
});
