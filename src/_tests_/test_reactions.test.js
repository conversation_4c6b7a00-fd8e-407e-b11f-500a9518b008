import React from 'react';
import Reactions from '../components/PostCard/Reactions';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  liked : true,
  likes : 10
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/core', () => ({
    Typography: () => Component => props => <Component classes="" {...props} />,
    Button: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    colors: () => Component => props => <Component classes="" {...props} />
    }));

jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Favorite', () => ({
    FavoriteIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/FavoriteBorder', () => ({
    FavoriteBorderIcon: () => Component => props => <Component classes="" {...props} />
}));

  jest.mock('@material-ui/icons/Share', () => ({
    ShareIcon: () => Component => props => <Component classes="" {...props} />
  }));

  const post = { 
        liked : true,
        likes : 10
    };
describe('Reactions  rendering ', () => {
  it('Renders correctly', () => {
    const wrapper = shallow(
      <Reactions post={post}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
