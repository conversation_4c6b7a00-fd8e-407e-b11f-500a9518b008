import React from 'react';
import Charts from '../views/LaborTranches/Charts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
    Tab: () => Component => props => <Component classes="" {...props} />,
    Tabs: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    CircularProgress: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    Grid: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock("src/components/charts/CPMovingELR", () => () => {
 return <mock-CPMovingELR data-testid="CPMovingELR"/>;
  });   
  jest.mock("src/utils/hasuraServices", () => () => {
    return <mock-hasuraServices data-testid="hasuraServices"/>;
  });
  jest.mock("src/components/PageHeader", () => () => {
    return <mock-PageHeader data-testid="PageHeader"/>;
  });
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('Labor Tranches  rendering ', () => {
  it('Renders Charts correctly', () => {
    const component = renderer.create(
      <Charts {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
//   it('render Paytype master ', () => {
//     const wrapper = shallow(
//       <Charts  />
//     );
//     expect(toJson(wrapper)).toMatchSnapshot();
//   });
});
