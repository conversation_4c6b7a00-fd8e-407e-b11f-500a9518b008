// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FilesDropzone rendering  render FilesDropzone  1`] = `
<div
  className=""
>
  <div
    className=""
    onBlur={[Function]}
    onClick={[Function]}
    onDragEnter={[Function]}
    onDragLeave={[Function]}
    onDragOver={[Function]}
    onDrop={[Function]}
    onFocus={[Function]}
    onKeyDown={[Function]}
    tabIndex={0}
  >
    <input
      autoComplete="off"
      multiple={true}
      onChange={[Function]}
      onClick={[Function]}
      style={
        Object {
          "display": "none",
        }
      }
      tabIndex={-1}
      type="file"
    />
    <div>
      <img
        alt="Select file"
        src="/images/undraw_add_file2_gvbb.svg"
      />
    </div>
    <div>
      <Typography
        gutterBottom={true}
        variant="h3"
      >
        Select files
      </Typography>
      <Typography
        color="textSecondary"
        variant="body1"
      >
        Drop files here or click
         
        <Link
          underline="always"
        >
          browse
        </Link>
         
        thorough your machine
      </Typography>
    </div>
  </div>
</div>
`;
