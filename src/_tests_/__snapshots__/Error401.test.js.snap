// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Error401 rendering  render Error401  1`] = `
<Page
  title="Error 401"
>
  <Typography
    align="center"
  >
    401: We are sorry but we are not able to authenticate you.
  </Typography>
  <Typography
    align="center"
    variant="subtitle2"
  >
    You either tried some shady route or you came here by mistake. Whichever it is, try using the navigation
  </Typography>
  <div>
    <img
      alt="Under development"
      src="/images/undraw_authentication_fsn5.svg"
    />
  </div>
  <div>
    <Button
      color="primary"
      component={
        Object {
          "$$typeof": Symbol(react.forward_ref),
          "propTypes": Object {
            "innerRef": [Function],
            "onClick": [Function],
            "replace": [Function],
            "target": [Function],
            "to": [Function],
          },
          "render": [Function],
        }
      }
      to="/"
      variant="outlined"
    >
      Back to home
    </Button>
  </div>
</Page>
`;
