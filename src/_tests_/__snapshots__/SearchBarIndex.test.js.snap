// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SearchBar index rendering  render SearchBar index  1`] = `
<Grid
  className=""
  container={true}
  spacing={3}
>
  <Grid
    item={true}
  >
    <Search
      onSearch={[MockFunction]}
    />
  </Grid>
  <Grid
    item={true}
  >
    <Button
      color="primary"
      onClick={[Function]}
      size="small"
      variant="outlined"
    >
      <[object Object] />
       
      Show filters
    </Button>
  </Grid>
  <Filter
    onClose={[Function]}
    onFilter={[MockFunction]}
    open={false}
  />
</Grid>
`;
