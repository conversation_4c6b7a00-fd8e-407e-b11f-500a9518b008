// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NotificationList render  render NotificationList  1`] = `
<List
  className=""
  disablePadding={true}
>
  <ListItem
    classes={
      Object {
        "root": undefined,
      }
    }
    divider={false}
    key="1"
  >
    <ListItemAvatar />
    <ListItemText
      primaryTypographyProps={
        Object {
          "className": undefined,
          "variant": "body1",
        }
      }
    />
    <ListItemAvatar>
      <Button
        className="reset-btn"
        onClick={[Function]}
        size="small"
      >
        Fix Now
      </Button>
    </ListItemAvatar>
  </ListItem>
</List>
`;
