// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PartListGrid rendering  render PartListGrid 1`] = `
<[object Object]>
  <Component>
    <Component
      colspan="2"
      padding="checkbox"
      style={
        Object {
          "width": "18%",
        }
      }
    >
      <span
        style={
          Object {
            "float": "left",
            "marginRight": "10px",
          }
        }
      >
        <b>
          2
          .
        </b>
      </span>
      <label
        style={
          Object {
            "fontWeight": "bold",
          }
        }
      >
        Parts Description: 
      </label>
      <br />
      <span />
    </Component>
    <Component
      style={
        Object {
          "width": "11%",
        }
      }
    >
      <label
        style={
          Object {
            "fontWeight": "bold",
          }
        }
      >
         Parts Pay Type: 
      </label>
      <br />
      1.44
       - 
      Warranty
       
    </Component>
    <Component
      style={
        Object {
          "width": "13%",
        }
      }
    >
      <label
        style={
          Object {
            "fontWeight": "bold",
          }
        }
      >
         
        Parts Sale: 
        <br />
      </label>
      $1.23
    </Component>
    <Component
      style={
        Object {
          "width": "10%",
        }
      }
    >
      <label
        style={
          Object {
            "fontWeight": "bold",
          }
        }
      >
         
        Parts Cost: 
        <br />
      </label>
      $2.65
    </Component>
    <Component
      style={
        Object {
          "width": "10%",
        }
      }
    >
      <label
        style={
          Object {
            "fontWeight": "bold",
          }
        }
      >
         Quantity: 
      </label>
      322
    </Component>
    <Component
      style={
        Object {
          "width": "13.5%",
        }
      }
    >
      <label
        style={
          Object {
            "fontWeight": "bold",
          }
        }
      >
         
        Parts Extended Sale:
        <br />
      </label>
      $8.09
    </Component>
    <Component
      style={
        Object {
          "width": "8%",
        }
      }
    >
      <label
        style={
          Object {
            "fontWeight": "bold",
          }
        }
      >
         Parts Extended Cost:
      </label>
      <br />
      $5.54
    </Component>
    <Component
      colspan="2"
      rowspan="2"
      style={
        Object {
          "textAlign": "center",
        }
      }
    >
      <Component
        title={
          <React.Fragment>
            <UNDEFINED
              color="inherit"
            >
               
              <label>
                Parts Sale 
                &lt;&gt;
                 Parts Cost and Parts Sale 
                &gt;
                 0 and Parts Cost 
                &gt;
                 0
              </label>
              <hr />
               
              Part Pay Type  =  CWIBEM
              <hr />
              <label>
                OP Category :
                <br />
                REPAIR (or) MAINTENANCE (or) COMPETITIVE
              </label>
            </UNDEFINED>
          </React.Fragment>
        }
      >
        <Component>
          <img
            src="./images/verifyimages/checked.png"
            style={
              Object {
                "height": "20px",
                "marginLeft": "5px",
                "width": "20px",
              }
            }
          />
          <img
            src="./images/verifyimages/info.png"
            style={
              Object {
                "height": "20px",
                "marginLeft": "5px",
                "width": "20px",
              }
            }
          />
        </Component>
      </Component>
      <br />
      Sale and cost
      <br />
      <p
        style={
          Object {
            "fontWeight": "200 !important",
          }
        }
      >
        Known Category 
        <br
          key="1"
        />
         Allocated
      </p>
    </Component>
  </Component>
</[object Object]>
`;
