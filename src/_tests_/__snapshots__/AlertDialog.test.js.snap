// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Alert Dialog rendering  render Alert Dialog 1`] = `
<[object Object]
  classes={
    Object {
      "paper": undefined,
    }
  }
  fullWidth={true}
>
  <[object Object]
    style={
      Object {
        "marginBottom": 20,
      }
    }
  >
    <[object Object]
      component="form"
      noValidate={true}
      sx={
        Object {
          "display": "flex",
          "flexDirection": "column",
          "m": "auto",
          "width": "100%",
        }
      }
    >
      <[object Object]
        aria-label="close"
        onClick={[Function]}
      >
        <[object Object] />
      </[object Object]>
      <Grid
        item={true}
        justify="flex-start"
        style={
          Object {
            "padding": "5px",
          }
        }
        xs={12}
      >
        <Component
          isFrom="itemization"
        />
      </Grid>
    </[object Object]>
  </[object Object]>
</[object Object]>
`;
