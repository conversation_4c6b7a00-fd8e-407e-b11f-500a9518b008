// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Home render  render Dashboard  1`] = `
<div
  className="dashboard dashboard-grid-items"
>
  <Component
    data={
      Object {
        "graph1": Object {
          "chartId": 1341,
          "data": Array [
            Object {
              "LaborGpRo": undefined,
            },
          ],
        },
        "graph10": Object {
          "chartId": 1351,
          "data": Array [
            Object {
              "LineRO": undefined,
            },
          ],
        },
        "graph11": Object {
          "chartId": 1344,
          "data": Array [
            Object {
              "WorkMix": undefined,
            },
          ],
        },
        "graph12": Object {
          "chartId": 1346,
          "data": Array [
            Object {
              "LaborGrid": undefined,
            },
          ],
        },
        "graph13": Object {
          "chartId": 1353,
          "data": Array [
            Object {
              "PartsGrid": undefined,
            },
          ],
        },
        "graph2": Object {
          "chartId": 1342,
          "data": Array [
            Object {
              "PartsGpRo": undefined,
            },
          ],
        },
        "graph3": Object {
          "chartId": 1343,
          "data": Array [
            Object {
              "TotalGpRo": undefined,
            },
          ],
        },
        "graph4": Object {
          "chartId": 1335,
          "data": Array [
            Object {
              "allCWITData": undefined,
            },
          ],
        },
        "graph5": Object {
          "chartId": 1340,
          "data": Array [
            Object {
              "AvgAgeMiles": undefined,
            },
          ],
        },
        "graph6": Object {
          "chartId": 1339,
          "data": Array [
            Object {
              "FlatRateHrs": undefined,
            },
          ],
        },
        "graph7": Object {
          "chartId": 1336,
          "data": Array [
            Object {
              "ROShareData": undefined,
            },
          ],
        },
        "graph8": Object {
          "chartId": 1337,
          "data": Array [
            Object {
              "LineROLtSixtyK": undefined,
            },
          ],
        },
        "graph9": Object {
          "chartId": 1338,
          "data": Array [
            Object {
              "LineROGtSixtyK": undefined,
            },
          ],
        },
      }
    }
    initialState={
      Object {
        "breakpoint": "lg",
        "data": Object {
          "graph1": Object {
            "chartId": 1341,
            "data": Array [
              Object {
                "LaborGpRo": undefined,
              },
            ],
          },
          "graph10": Object {
            "chartId": 1351,
            "data": Array [
              Object {
                "LineRO": undefined,
              },
            ],
          },
          "graph11": Object {
            "chartId": 1344,
            "data": Array [
              Object {
                "WorkMix": undefined,
              },
            ],
          },
          "graph12": Object {
            "chartId": 1346,
            "data": Array [
              Object {
                "LaborGrid": undefined,
              },
            ],
          },
          "graph13": Object {
            "chartId": 1353,
            "data": Array [
              Object {
                "PartsGrid": undefined,
              },
            ],
          },
          "graph2": Object {
            "chartId": 1342,
            "data": Array [
              Object {
                "PartsGpRo": undefined,
              },
            ],
          },
          "graph3": Object {
            "chartId": 1343,
            "data": Array [
              Object {
                "TotalGpRo": undefined,
              },
            ],
          },
          "graph4": Object {
            "chartId": 1335,
            "data": Array [
              Object {
                "allCWITData": undefined,
              },
            ],
          },
          "graph5": Object {
            "chartId": 1340,
            "data": Array [
              Object {
                "AvgAgeMiles": undefined,
              },
            ],
          },
          "graph6": Object {
            "chartId": 1339,
            "data": Array [
              Object {
                "FlatRateHrs": undefined,
              },
            ],
          },
          "graph7": Object {
            "chartId": 1336,
            "data": Array [
              Object {
                "ROShareData": undefined,
              },
            ],
          },
          "graph8": Object {
            "chartId": 1337,
            "data": Array [
              Object {
                "LineROLtSixtyK": undefined,
              },
            ],
          },
          "graph9": Object {
            "chartId": 1338,
            "data": Array [
              Object {
                "LineROGtSixtyK": undefined,
              },
            ],
          },
        },
        "layouts": [Function],
      }
    }
    layouts={[Function]}
    props={Object {}}
  />
</div>
`;
