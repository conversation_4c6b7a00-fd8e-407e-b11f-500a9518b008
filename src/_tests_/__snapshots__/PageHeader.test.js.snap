// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Paytype index rendering  render Paytype master  1`] = `
<WithStyles(ForwardRef(Grid))
  className="reset-dashboard"
  container={true}
>
  <WithStyles(ForwardRef(Grid))
    item={true}
    style={
      Object {
        "alignItems": "center",
        "display": "flex",
      }
    }
    xs={6}
  >
    <WithStyles(ForwardRef(Typography))
      className="main-title"
      color="primary"
      variant="h4"
    />
  </WithStyles(ForwardRef(Grid))>
  <WithStyles(ForwardRef(Grid))
    item={true}
    xs={4}
  >
    <Button
      className="reset-btn"
      id="reset-layout"
      onClick={[Function]}
      variant="contained"
    >
      <Memo(ForwardRef(RestoreIcon)) />
      <WithStyles(ForwardRef(Typography))
        align="left"
        variant="body1"
      >
        Reset Layout
      </WithStyles(ForwardRef(Typography))>
    </Button>
  </WithStyles(ForwardRef(Grid))>
</WithStyles(ForwardRef(Grid))>
`;
