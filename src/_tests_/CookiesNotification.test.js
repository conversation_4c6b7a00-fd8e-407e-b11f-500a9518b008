import React from 'react';
import CookiesNotification from '../components/CookiesNotification';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('js-cookie', () => ({
    Cookies: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    Link: () => Component => props => <Component classes="" {...props} />,
    Button: () => Component => props => <Component classes="" {...props} />
}));

describe('CookiesNotification rendering ', () => {
  it('render CookiesNotification ', () => {
    const wrapper = shallow(
      <CookiesNotification />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
