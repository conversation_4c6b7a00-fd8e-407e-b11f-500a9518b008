import { 
          getTimeZone,
          getChartConfigurationItemization,
          getOptionsForPie1346,
          getOptionsForHighchartsLinearGauge,
          getOptionsForColumnHighcharts,
          getOptionsForHighchartsPie,
          getKpiChartTitle,
          getResultSetforKPIs,
          getLabelforKPIs,
          getChartConfigurationKPIs,
          getTooltipForDuplicateDashboards,
          formatCellValueCount,
          formatCellValuePerc,
          formatCellValue,
          onBtExportExcludedMakes,
          titleCase,
          applyPartialMonthFilterLineCharts,
          checkClosedDateInCurrentMonth,
          getIndexForOpportunityCharts,
          getComparisonChartConfiguration,
          getDataforComparisonChart,
          getHasuraRenderedCharts,
          isPartsComparisonChart,
          isDiscountComparisonApexChart,
          isAddonsComparisonChart,
          isMonthComparisonChart,
          getStepSizexAxis,
          getHasuraChartMeasures,
          isAdvisorenabledCharts,
          getChartLegendsforHasuraCharts,
          getColorScheme,
          getAgGridChartOptions,
          getDataGridConfigurationKPI,
          getDataGridConfiguration,
          getDataGridConfigurationFav,
          getDataGridConfigurationFavOneRow,
          getDataGridConfigurationCommon,
          getDataGridConfigurationTwoRow,
          getDataGridConfigurationSpecialMetrics,
          getDataGridConfigTotalOpportunity,
          getDataGridConfigurationForDetails,
          getDataforLineChart
       } from 'src/utils/Utils';
import * as redux from 'react-redux';
import { Category } from '@material-ui/icons';
const spy = jest.spyOn(redux, 'useSelector');
  spy.mockReturnValue({
    series:{symbol:'circle'}
  });       
const allData = { datasets : { label : 1 } };
const data = { data:[]};
const resultSet = [{
  categories:["c"],
  data: ["1556.56","1479.00","1253.26","1575.13","1511.47","1069.00","1145.22","1583.66","1741.86","1485.36","1396.80","1488.00","1624.95"
  ],
  name: "Koeppel Ford ",
  dimension: [
    "2021-07-01T00:00:00.000",
    "2021-08-01T00:00:00.000",
    "2021-09-01T00:00:00.000",
    "2021-10-01T00:00:00.000",
    "2021-11-01T00:00:00.000",
    "2021-12-01T00:00:00.000",
    "2022-01-01T00:00:00.000",
    "2022-02-01T00:00:00.000", 
    "2022-03-01T00:00:00.000",
    "2022-04-01T00:00:00.000",
    "2022-05-01T00:00:00.000",
    "2022-06-01T00:00:00.000",
    "2022-07-01T00:00:00.000",
  ]
}];
const hasuraData = ["940", "946", "1072", "967", "1097", "937", "988", "987", "986", "1066", "1073", "1127", "1098", "966", "916", "1238", "1128", "1129", "1130", "1131", "1132", "949", "1076", "1077", "1078", "1083", "1079", "953", "1012", "1015", "1014", "1013", "1091", "1092", "1092", "1094", "955", "1084", "1085", "1086", "1087", "1088", "1044", "1318", "923", "1355", "930", "935", "936", "938", "1174", "1175", "1176", "1177", "1178", "1179", "1180", "1181", "1182", "1183", "1184", "1185", "1186", "1187", "1188", "1189", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1211", "1212", "1213", "1214", "1215", "1216", "1217", "1218", "1219", "1220", "1221", "1222", "1223", "1224", "1225", "1226", "1227", "1228", "1229", "1230", "1231", "1316", "1317", "1319", "1320", "1321", "1322", "1323", "1324", "1325", "1334"];
const excludeMake = {"columnKeys": ["makesId", "manufacturer", "excludedMakes"], "columnWidth": 150, "customHeader": [[], [{"data": {"type": "String", "value": "Excluded Makes"}, "mergeAcross": 3, "styleId": "bigHeader"}]], "fileName": "Excluded Makes", "sheetName": "Report"};
// const receivedData = {"animation": {"animateScale": true}, "layout": {"padding": {"bottom": 0, "left": 0, "right": 0, "top": 30}}, "legend": {"align": "center", "display": true, "labels": {"boxWidth": 12, "padding": 5, "usePointStyle": false}, "position": "right"}, "maintainAspectRatio": false, "scales": {"xAxes": [{"ticks": {"fontSize": 14, "fontStyle": "bold"}}], "yAxes": [{"ticks": {"callback": [Function callback], "maxTicksLimit": 5}}]}, "tooltips": {"axis": "y", "backgroundColor": "#ddeaf4", "bodyFontColor": "#003d6b", "borderColor": "#003d6b", "borderWidth": 1, "callbacks": {"label": [Function label]}, "enabled": false, "mode": "index", "position": "nearest", "titleFontColor": "#003d6b", "yPadding": 10}};
// const pieData = {"accessibility": {"point": {"valueSuffix": "%"}}, "chart": {"plotBackgroundColor": null, "plotBorderWidth": null, "plotShadow": false, "type": "pie"}, "colors": ["#FF9900", "#109618", "#3366CC", "#DD4477"], "plotOptions": {"pie": {"allowPointSelect": true, "dataLabels": {"align": "right", "color": "#757575", "crop": false, "distance": 5, "enabled": true, "formatter": [Function formatter], "style": {"fontFamily": "Roboto", "fontSize": "15px", "fontWeight": "600"}, "y": -12}}, "series": {"enableMouseTracking": false}}, "series": [{"colorByPoint": true, "data": {"datasets": {"label": 1}}, "name": "Brands"}], "title": {"text": ""}, "tooltip": {"enabled": false}};
// const dataRe = {"chart": {"style": {"color": "#757575", "fontFamily": "Roboto"}}, "colors": ["#ee7600", "#109618"], "legend": {"align": "left", "backgroundColor": "rgba(255,255,255,0.25)", "floating": true, "layout": "vertical", "style": {"fontSize": "16px", "fontWeight": "bold"}, "verticalAlign": "bottom", "x": 120, "y": 100}, "plotOptions": {"column": {"dataLabels": {"color": "#757575", "crop": false, "enabled": true, "formatter": [Function formatter], "overflow": "none", "style": {"fontFamily": "Roboto", "fontSize": "12px", "fontWeight": "900"}}}, "series": {"enableMouseTracking": false, "pointWidth": 30}}, "series": [{"data": [NaN, 0], "name": "Sales / RO", "type": "column"}, {"data": [0, NaN], "name": "GP / RO", "type": "column"}], "title": {"text": ""}, "tooltip": {"enabled": false}, "xAxis": [{"categories": ["Sales / RO", "GP / RO"], "labels": {"style": {"fontSize": "12px", "fontWeight": "bold"}}}], "yAxis": [{"labels": {"formatter": [Function formatter], "style": {"color": ["#FF9900", "#109618", "#3366CC", "#DD4477"]}}, "title": {"text": ""}}]};
const dataResult = {"chart": {"backgroundColor": "white","height": 60, "inverted": true, "type": "bullet"}, "credits": {"enabled": false}, "exporting": {"enabled": false}, "legend": {"enabled": false}, "plotOptions": {"series": {"borderWidth": 0, "color": "#FFF", "dataLabels": {"backgroundColor": "rgba(252, 255, 197, 0.7)", "borderColor": "#AAA", "borderRadius": 5, "borderWidth": 1, "enabled": true, "format": "{point.y} Yrs", "style": {"fontFamily": "Roboto", "fontSize": "16px", "fontWeight": "bold"}, "x": 5}, "pointPadding": 0.25, "pointWidth": 4}}, "series": [{"data": [{"target": NaN, "y": NaN}]}], "title": {"text": null}, "tooltip": {"enabled": false}, "xAxis": {"categories": [""]}, "yAxis": {"max": 10, "plotBands": [{"color": "#008000", "from": 0, "to": 2.5}, {"color": "#7ab55c", "from": 2.5, "to": 5}, {"color": "#ec5555", "from": 5, "to": 7.5}, {"color": "#b81414", "from": 7.5, "to": 10}], "style": {"fontSize": "14px", "fontWeight": "bold"}, "title": null}};
// const result = {"boost": {"useGPUTranslations": true, "usePreAllocated": true}, "legend": {"enabled": true, "itemHoverStyle": {"color": "#757575"}, "itemStyle": {"color": "#757575", "fontFamily": "Roboto", "fontWeight": 500}}, "series": [{"allowPointSelect": true, "data": undefined, "legendIndex": 2, "marker": {"fillColor": "#35af557a", "lineColor": "rgba(4, 147, 114, 1)", "lineWidth": 0.5, "symbol": "diamond"}, "name": "ELR Repair"}, {"allowPointSelect": true, "data": undefined, "legendIndex": 1, "marker": {"fillColor": "#f17f7fa8", "lineColor": "rgba(220,57,18,1)", "lineWidth": 0.5, "radius": 4, "symbol": "square"}, "name": "ELR Maintenance"}, {"allowPointSelect": true, "data": undefined, "legendIndex": 0, "marker": {"fillColor": "#0389fc61", "lineColor": "rgba(75,192,192,1)", "lineWidth": 0.5}, "name": "ELR Competitive"}], "title": {"text": ""}, "tooltip": {"backgroundColor": "#ddeaf4", "borderColor": "#003d6b", "borderWidth": 1, "color": "#003d6b", "formatter":[Function formatter], "style": {"color": "#003d6b"}, "useHTML": true}};
// const results =  {
//                   "hover": {
//                     "mode": null
//                   },
//                   "layout": {
//                     "padding": {
//                       "bottom": 0, "left": 80, "top": 0
//                     }
//                   }, "legend": {
//                         "align": "center", "labels": {
//                           "boxWidth": 12, "fontFamily": "Roboto", "fontSize": 14, "fontStyle": "500", "padding": 10, "usePointStyle": false
//                         }, "position": "right"
//                       }, "maintainAspectRatio": false, "plugins": {
//                         "datalabels": {
//                           "align": "end", "anchor": "end", "backgroundColor": [Function backgroundColor], "borderColor": "white", "borderRadius": 25, "borderWidth": 2, "color": "#FFF", "display": true, 
//                           "font": {
//                             "color": "white", "size": 14, "weight": "bold"
//                           }, "formatter": [Function formatter], "offset": -28, "padding": 6
//                         }
//                       }, "title": {
//                         "display": true, "lineHeight": 1, "padding": 0, "position": "top"
//                       }, "tooltips": {
//                         "enabled": false
//                       }
//                     };
// const chartOption = {"cellRange": {"columns": []}, "chartContainer": "+05:30", "chartPalette": "bright", "chartType": "groupedColumn", "processChartOptions": [Function processChartOptions]};
const gridConfig = {"h": 2, "isResizable": true, "minH": 1, "minW": 5, "w": 6, "x": 6, "y": 126};
const gridConfigs = {"h": 7, "minH": 7, "minW": 5, "preventCollision": true, "w": 6, "x": 6, "y": 126};
const gridConfigFav = {"h": 5, "minH": 5, "minW": 5, "preventCollision": true, "w": 6, "x": 6, "y": 126};
const gridConfigFavOne = {"h": 7, "minH": 7, "minW": 12, "preventCollision": true, "w": 12, "x": 6, "y": 126};
const gridConfigCom = {"h": 7, "minH": 5, "minW": 3, "preventCollision": true, "w": 4, "x": 6, "y": 126};
const gridConfigTwoRow = {"h": 7, "minH": 5, "minW": 3, "preventCollision": true, "w": 6, "x": 6, "y": 126};
const gridSpecial = {"h": 10, "isResizable": true, "minH": 9, "minW": 5, "w": 6, "x": 6, "y": 126};
const gridOpportunity = {"h": 8, "isResizable": true, "minH": 8, "minW": 6, "w": 6, "x": 3, "y": 0};
const gridDetails = {"h": 7, "isBounded": true, "isResizable": true, "minH": 7, "minW": 5, "preventCollision": true, "w": 6, "x": 6, "y": 162};
// describe("getAgGridChartOptions ", () => {
//   test('render getAgGridChartOptions properly', () => {
//     expect(getAgGridChartOptions(930,"+05:30")).toStrictEqual(chartOption);
//   });
// });

describe("getYearLegend ", () => {
  test('render getTimeZone properly', () => {
    expect(getTimeZone("+05:30")).toStrictEqual("+05:30");
  });
});
describe("getDataGridConfigurationKPI ", () => {
  test('render getDataGridConfigurationKPI properly', () => {
    expect(getDataGridConfigurationKPI(55)).toStrictEqual(gridConfig);
  });
});
describe("getDataGridConfiguration ", () => {
  test('render getDataGridConfiguration properly', () => {
    expect(getDataGridConfiguration(55)).toStrictEqual(gridConfigs);
  });
});
describe("getDataGridConfigurationFav ", () => {
  test('render getDataGridConfigurationFav properly', () => {
    expect(getDataGridConfigurationFav(55)).toStrictEqual(gridConfigFav);
  });
});
describe("getDataGridConfigurationFavOneRow ", () => {
  test('render getDataGridConfigurationFavOneRow properly', () => {
    expect(getDataGridConfigurationFavOneRow(55)).toStrictEqual(gridConfigFavOne);
  });
});
describe("getDataGridConfigurationCommon ", () => {
  test('render getDataGridConfigurationCommon properly', () => {
    expect(getDataGridConfigurationCommon(55)).toStrictEqual(gridConfigCom);
  });
});
describe("getDataGridConfigurationTwoRow ", () => {
  test('render getDataGridConfigurationTwoRow properly', () => {
    expect(getDataGridConfigurationTwoRow(55)).toStrictEqual(gridConfigTwoRow);
  });
});
describe("getDataGridConfigurationSpecialMetrics ", () => {
  test('render getDataGridConfigurationSpecialMetrics properly', () => {
    expect(getDataGridConfigurationSpecialMetrics(55)).toStrictEqual(gridSpecial);
  });
});
describe("getDataGridConfigTotalOpportunity ", () => {
  test('render getDataGridConfigTotalOpportunity properly', () => {
    expect(getDataGridConfigTotalOpportunity(55)).toStrictEqual(gridOpportunity);
  });
});
describe("getDataGridConfigurationForDetails ", () => {
  test('render getDataGridConfigurationForDetails properly', () => {
    expect(getDataGridConfigurationForDetails(55)).toStrictEqual(gridDetails);
  });
});
// describe("getDataforLineChart ", () => {
//   test('render getDataforLineChart properly', () => {
//     expect(getDataforLineChart(resultSet,55)).toStrictEqual();
//   });
// });
// describe("getChartConfigurationItemization ", () => {
//   test('render getChartConfigurationItemization properly', () => {
//     expect(getChartConfigurationItemization("",allData,18)).toStrictEqual(result);
//   });
// });
// describe("getOptionsForPie1346 ", () => {
//   test('render getOptionsForPie1346 properly', () => {
//     expect(getOptionsForPie1346()).toStrictEqual(results);
//   });
// });
describe("getOptionsForHighchartsLinearGauge ", () => {
  test('render getOptionsForHighchartsLinearGauge properly', () => {
    expect(getOptionsForHighchartsLinearGauge()).toStrictEqual(dataResult);
  });
});
// describe("getOptionsForColumnHighcharts ", () => {
//   test('render getOptionsForColumnHighcharts properly', () => {
//     expect(getOptionsForColumnHighcharts(data,'','')).toStrictEqual(dataRe);
//   });
// });
// describe("getOptionsForHighchartsPie ", () => {
//   test('render getOptionsForHighchartsPie properly', () => {
//     expect(getOptionsForHighchartsPie(pieData)).toStrictEqual();
//   });
// });
describe("getKpiChartTitle ", () => {
  test('render getKpiChartTitle properly', () => {
    expect(getKpiChartTitle("1335")).toStrictEqual("CP / Wty / Int / All Unique ROs");
  });
});
// describe("getResultSetforKPIs ", () => {
//   const aa = [
//     {
//       name:"Deepak"
//     }];
//   test('render getResultSetforKPIs properly', () => {
//     expect(getResultSetforKPIs(aa)).toStrictEqual("CP / Wty / Int / All Unique RO’s");
//   });
// });
describe("getLabelforKPIs ", () => {
  const aa = [
    {
      name:"Deepak"
    }];
    const data =[ {label:"ctotal"}];
    let label = [];
  data.map(val => {
    if (val.label == 'ctotal') {
      label.push('CP');
    } else if (val.label == 'wtotal') {
      label.push('Wty');
    } else if (val.label == 'itotal') {
      label.push('Int');
    } else if (val.label == 'ttotal') {
      label.push('All');
    } else if (val.label == 'totalro' || val.label == 'totalRos') {
      label.push('Total RO');
    } else if (val.label == 'totalshare') {
      label.push('RO Share');
    } else if (val.label == 'totalroDuration') {
      label.push('Advisor RO');
    } else if (val.label == 'totalshareDuration') {
      label.push('Advisor Share');
    } else if (val.label == 'onelineRos') {
      label.push('1 Line RO');
    } else if (val.label == 'onelinePercentage') {
      label.push('1 Line RO%');
    } else if (val.label == 'multilineRos') {
      label.push('Multi-Line RO');
    } else if (val.label == 'multilinePercentage') {
      label.push('Multi-Line RO%');
    } else if (val.label == 'onelineLbrsale') {
      label.push('1 Line Lbr Sale');
    } else if (val.label == 'multilineLbrsale') {
      label.push('Multi-Line Lbr Sale');
    } else if (val.label == 'onelinePrtsale') {
      label.push('1 Line Prt Sale');
    } else if (val.label == 'multilinePrtsale') {
      label.push('Multi-Line Prt Sale');
    } else if (val.label == 'onelineTotalsale') {
      label.push('1 Line Total Sale');
    } else if (val.label == 'multilineTotalsale') {
      label.push('Multi-Line Total Sale');
    } else {
      label.push(val.label);
    }
  });
  test('render getLabelforKPIs properly', () => {
    expect(getLabelforKPIs(data)).toStrictEqual(["CP"]);
  });
});
// describe("getChartConfigurationKPIs ", () => {
//   test('render getChartConfigurationKPIs properly', () => {
//     expect(getChartConfigurationKPIs("1335")).toStrictEqual(receivedData);
//   });
// });
describe("getTooltipForDuplicateDashboards ", () => {
  test('render getTooltipForDuplicateDashboards properly', () => {
    expect(getTooltipForDuplicateDashboards(1335)).toStrictEqual([]);
  });
});
describe("formatCellValueCount ", () => {
  test('render formatCellValueCount properly', () => {
    expect(formatCellValueCount(1335)).toStrictEqual("1,335");
  });
});
describe("formatCellValuePerc ", () => {
  test('render formatCellValuePerc properly', () => {
    expect(formatCellValuePerc("",33)).toStrictEqual("$33.00");
  });
});
describe("formatCellValue ", () => {
  test('render formatCellValue properly', () => {
    expect(formatCellValue(-33)).toStrictEqual("-$33.00");
  });
});
describe("onBtExportExcludedMakes ", () => {
  test('render onBtExportExcludedMakes properly', () => {
    expect(onBtExportExcludedMakes()).toStrictEqual(excludeMake);
  });
});
describe("titleCase ", () => {
  test('render titleCase properly', () => {
    expect(titleCase('TITLE')).toStrictEqual('Title');
  });
});
// describe("applyPartialMonthFilterLineCharts ", () => {
//   test('render applyPartialMonthFilterLineCharts properly', () => {
//     expect(applyPartialMonthFilterLineCharts('TITLE')).toStrictEqual('Title');
//   });
// });
describe("checkClosedDateInCurrentMonth ", () => {
  test('render checkClosedDateInCurrentMonth properly', () => {
    expect(checkClosedDateInCurrentMonth("10-10-2022","15-10-2022")).toStrictEqual(true);
  });
});
describe("getIndexForOpportunityCharts ", () => {
  test('render getIndexForOpportunityCharts properly', () => {
    expect(getIndexForOpportunityCharts(929)).toStrictEqual(4);
  });
});
// describe("getComparisonChartConfiguration ", () => {
//   test('render getComparisonChartConfiguration properly', () => {
//     expect(getComparisonChartConfiguration()).toStrictEqual();
//   });
// });

// describe("getDataforComparisonChart ", () => {
//   test('render getDataforComparisonChart properly', () => {
//     expect(getDataforComparisonChart()).toStrictEqual();
//   });
// });

// describe("getHasuraRenderedCharts ", () => {
//   test('render getHasuraRenderedCharts properly', () => {
//     expect(getAllChartDetails()).toStrictEqual(hasuraData);
//   });
// });
describe("isPartsComparisonChart ", () => {
  test('render isPartsComparisonChart properly', () => {
    expect(isPartsComparisonChart('1309')).toStrictEqual(true);
  });
});

describe("isDiscountComparisonApexChart ", () => {
  test('render isDiscountComparisonApexChart properly', () => {
    expect(isDiscountComparisonApexChart('1124')).toStrictEqual(true);
  });
});
describe("isAddonsComparisonChart ", () => {
  test('render isAddonsComparisonChart properly', () => {
    expect(isAddonsComparisonChart('1118')).toStrictEqual(true);
  });
});
describe("isMonthComparisonChart ", () => {
  test('render isMonthComparisonChart properly', () => {
    expect(isMonthComparisonChart('1114')).toStrictEqual(true);
  });
});
const hasuraDatas = ["laborgrossprofitpercentage", "partsgrossprofitpercentage", "combinedgrossprofitpercentage"];
describe("getHasuraChartMeasures ", () => {
  test('render getHasuraChartMeasures properly', () => {
    expect(getHasuraChartMeasures('940')).toStrictEqual(hasuraDatas);
  });
});
describe("isAdvisorenabledCharts ", () => {
  test('render isAdvisorenabledCharts properly', () => {
    expect(isAdvisorenabledCharts()).toStrictEqual(false);
  });
});
describe("getChartLegendsforHasuraCharts ", () => {
  const chartLegend = ["Labor Gross Profit %", "Parts Gross Profit %", "Combined Gross Profit %"];
  test('render getChartLegendsforHasuraCharts properly', () => {
    expect(getChartLegendsforHasuraCharts('940')).toStrictEqual(chartLegend);
  });
});
describe("getColorScheme ", () => {
  const color = ["#109618", "#DC3912", "#3366CC"];
  test('render getColorScheme properly', () => {
    expect(getColorScheme(0)).toStrictEqual(color);
  });
});