import React from 'react';
import TopBar from '../layouts/Dashboard/TopBar';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';

const mockStore = configureMockStore();
const store = mockStore({});
// jest.mock('jquery-sparkline', () => ({
//   makeStyles: () => Component => props => <Component classes="" {...props} />
// }));
JSON.parse = jest.fn().mockImplementationOnce(() => {
  // return your what your code is returning.
});
jest.mock('jquery-sparkline', () => {});
jest.mock('react-ga');
describe('Dashboard Render ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  it('render TopBar ', () => {
    const wrapper = shallow(<TopBar />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
