import React from 'react';
import NotificationsPopover from '../components/NotificationsPopover/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';

const mockStore = configureMockStore();
const store = mockStore({});

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Popover: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  CardActions: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  colors: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ArrowForward', () => ({
  ArrowForwardIcon: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/icons/Payment', () => ({
  PaymentIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/PeopleOutlined', () => ({
  PeopleIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Error', () => ({
  ErrorIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Code', () => ({
  CodeIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Store', () => ({
  StoreIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('NotificationsPopover render ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  it('render NotificationsPopover ', () => {
    const wrapper = shallow(<NotificationsPopover />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
