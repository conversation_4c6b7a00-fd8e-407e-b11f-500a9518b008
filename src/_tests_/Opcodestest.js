import React from 'react';
import Opcodes from '../views/OPcodes/Opcodes';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  showRefresh: 'all'
});
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));
// jest.mock('ag-grid-react/lib/agGridReact', () => ({
//   AgGridReact: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('jquery', () => ({
//   $: () => Component => props => <Component classes="" {...props} />
// }));

// jest.mock('@react-keycloak/web', () => ({
//   withKeycloak: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/core/Tabs', () => ({
//   Tab: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/core/Link', () => ({
//   Link: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/icons/GetApp', () => ({
//   ExportIcon: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@ag-grid-enterprise/all-modules', () => ({
//   AllModules: () => Component => props => <Component classes="" {...props} />
// }));

jest.mock('@material-ui/core', () => ({
  LinearProgress: () => Component => props => (
    <Component classes="" {...props} />
  ),
  Box: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />
}));

describe('Opcodes rendering ', () => {
  // JSON.parse = jest.fn().mockImplementationOnce(() => {
  //   return 75627643;
  // });
  // it('render Opcodes ', () => {
  //   const wrapper = shallow(
  //     <Opcodes
  //       keycloak={{
  //         realmAccess: { roles: ['admin'] }
  //       }}
  //       onBtClose={jest.fn()}
  //       handleClose={jest.fn()}
  //       handleCloseRow={jest.fn()}
  //       onBtExport={jest.fn()}
  //       onGridReady={jest.fn()}
  //       onBtClose={jest.fn()}
  //     />
  //   );
  //   expect(toJson(wrapper)).toMatchSnapshot();

  // });
  const realmAccess = {
    roles: ['admin']
  };
  const propss = {
    keycloak: {
      realmAccess: {
        roles: ['admin']
      }
    }
  };
  const props = {
    todo: {
      requesting: true,
      todos: [],
      error: null
    }
  };
  it('Renders correctly', () => {
    const component = renderer.create(
      <Opcodes
        {...props}
        key={1}
        // keycloak={{
        //   realmAccess: {
        //     roles: ['admin']
        //   }
        // }}
        // onBtClose={jest.fn()}
        // handleClose={jest.fn()}
        // handleCloseRow={jest.fn()}
        // onBtExport={jest.fn()}
        // onGridReady={jest.fn()}
        // onBtClose={jest.fn()}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
