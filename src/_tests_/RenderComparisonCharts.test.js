import React from 'react';
import RenderComparisonCharts from '../components/charts/RenderComparisonCharts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('react-chartjs-2', () => ({
  Line: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  CircularProgress: () => Component => props => (
    <Component classes="" {...props} />
  ),
  Divider: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-phone-input-2', () => ({
  PhoneInput: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('../components/StylesProvider');
describe('RenderComparisonCharts rendering ', () => {
 
  const resultSet = [];
  const resultSets = [{
    data: ["1556.56","1479.00","1253.26","1575.13","1511.47","1069.00","1145.22","1583.66","1741.86","1485.36","1396.80","1488.00","1624.95"
    ],
    name: "Koeppel Ford ",
    dimension: [
      "2021-07-01T00:00:00.000",
      "2021-08-01T00:00:00.000",
      "2021-09-01T00:00:00.000",
      "2021-10-01T00:00:00.000",
      "2021-11-01T00:00:00.000",
      "2021-12-01T00:00:00.000",
      "2022-01-01T00:00:00.000",
      "2022-02-01T00:00:00.000", 
      "2022-03-01T00:00:00.000",
      "2022-04-01T00:00:00.000",
      "2022-05-01T00:00:00.000",
      "2022-06-01T00:00:00.000",
      "2022-07-01T00:00:00.000",
    ]
  }];
  it('render RenderComparisonCharts with no data ', () => {
    const wrapper = shallow(
      <RenderComparisonCharts  resultSet={resultSet}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render RenderComparisonCharts with data ', () => {
    const wrapper = shallow(
      <RenderComparisonCharts  resultSet={resultSets}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render ReviewStars', () => {
    const toggleInstance = shallow(<RenderComparisonCharts resultSet={resultSets}/>);
    const elements=toggleInstance.find('Card');
    expect(elements.length).toBe(1);
    const element1=toggleInstance.find('CardHeader');
    expect(element1.length).toBe(1);
    const element2=toggleInstance.find('CardContent');
    expect(element2.length).toBe(1);
    expect(toggleInstance.find('Card').children().length).toBe(3);
    expect(toggleInstance.find('CardHeader').children().length).toBe(0);
    expect(toggleInstance.find('CardContent').children().length).toBe(1);
  });
});
