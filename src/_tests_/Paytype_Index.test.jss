import React from 'react';
import Index from '../views/PayTypeMaster/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';

const mockStore = configureMockStore();
const store = mockStore({});
jest.mock('@material-ui/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/pickers', () => ({
  MuiPickersUtilsProvider: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/core/styles', () => ({
  StylesProvider: () => Component => props => (
    <Component classes="" {...props} />
  ),
  jssPreset: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Fade: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
  Box: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => (
    <Component classes="" {...props} />
  ),
  Typography: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@react-keycloak/web', () => ({
  withKeycloak: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
  Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
  Tabs: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Link', () => ({
  Link: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/GetApp', () => ({
  ExportIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Refresh', () => ({
  RefreshIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Button', () => ({
  Button: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Dialog', () => ({
  Dialog: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogActions', () => ({
  DialogActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContent', () => ({
  DialogContent: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContentText', () => ({
  DialogContentText: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/icons/CallMerge', () => ({
  CallMergeIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Error', () => ({
  ErrorIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/GetApp', () => ({
  ExportIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/InsertDriveFile', () => ({
  InsertDriveFileIcon: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/core/DialogTitle', () => ({
  DialogTitle: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/lab/Alert', () => ({
  Alert: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/IconButton', () => ({
  IconButton: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Collapse', () => ({
  Collapse: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Close', () => ({
  CloseIcon: () => Component => props => <Component classes="" {...props} />
}));
//   jest.mock('@ag-grid-enterprise/all-modules', () => ({
//     AllModules: () => Component => props => <Component classes="" {...props} />
//   }));

describe('Opcodes index rendering ', () => {
  it('render Opcodes ', () => {
    const wrapper = shallow(
      <Index handleRefresh={jest.fn()} handleReload={jest.fn()} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
