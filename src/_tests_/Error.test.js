import React from 'react';
import Error from '../layouts/Error';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

const mockStore = configureMockStore();
const store = mockStore({});
describe("PostCard Error ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });
    const route = {
        routes:""
    }  
    it("render Error ", () => {
        const wrapper =  shallow(
                    <Error
                      direction={"rtl"}
                      route={route}
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render Error ', () => {
        const toggleInstance = shallow(<Error direction={"rtl"} route={route} />);
        const elements=toggleInstance.find('div');
        expect(elements.length).toBe(2);
        const element=toggleInstance.find('Suspense');
        expect(element.length).toBe(1);
        expect(toggleInstance.find('div').children().length).toBe(2);
      });
});   