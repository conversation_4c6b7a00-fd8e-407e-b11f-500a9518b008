import React from 'react';
import DashboardActions from '../components/DashboardActions';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';
const mockStore = configureMockStore();
const store = mockStore({});
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  selectedStoreId: 75627643
});
describe('DashboardActions render ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });

  const title = 'CP Overview';
  it('render DashboardActions ', () => {
    // const wrapper = shallow(
    //   <DashboardActions
    //     setTitle={'CP Overview'}
    //     filterCharts={jest.fn()}
    //     resetDashboard={jest.fn()}
    //   />
    // );
    // expect(toJson(wrapper)).toMatchSnapshot();
    const component = renderer.create(
      <DashboardActions
        setTitle={'CP Overview'}
        filterCharts={jest.fn()}
        resetDashboard={jest.fn()}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
  // it("render DashboardActions with Title", () => {
  //     const wrapper =  shallow(
  //                 <DashboardActions
  //                    setTitle = {title}
  //                  />
  //         );
  //     expect(toJson(wrapper)).toMatchSnapshot();
  // });
});
