import React from 'react';
import Placeholder from '../components/NotificationsPopover/Placeholder';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

const mockStore = configureMockStore();
const store = mockStore({});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => (
      <Component
        classes=""
        {...props}
      />
    ),
}));  
jest.mock('@material-ui/core', () => ({
    Typography: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));

describe("Placeholder render ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });  
    it("render Placeholder ", () => {
        const wrapper =  shallow(
          <Placeholder />
        );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render Placeholder ', () => {
      const toggleInstance = shallow(<Placeholder />);
      const elements=toggleInstance.find('div');
      expect(elements.length).toBe(1);
      const element=toggleInstance.find('Typography');
      expect(element.length).toBe(1);
      expect(toggleInstance.find('div').children().length).toBe(1);
      expect(toggleInstance.find('Typography').children().length).toBe(1);
    });
    
});   