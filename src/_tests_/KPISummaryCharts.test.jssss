import React from 'react';
import KPISummaryCharts from '../components/charts/KPISummaryCharts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
// jest.mock("../components/DataCardKpi.js", () => () => {
//   return <mock-DataCard data-testid="DataCard"/>;
// });
// jest.mock('@material-ui/icons/AssignmentReturnOutlined', () => ({
//     AssignmentReturnOutlinedIcon: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/icons/ArrowBackOutlined', () => ({
//     ArrowBackOutlinedIcon: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/core/Paper', () => ({
//     Paper: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/styles', () => ({
//     withStyles: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/styles', () => ({
//     makeStyles: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/icons/ArrowBack', () => ({
//     ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/core', () => ({
//     Card: () => Component => props => <Component classes="" {...props} />,
//     Grid: () => Component => props => <Component classes="" {...props} />,
//     CardHeader: () => Component => props => <Component classes="" {...props} />,
//     CardContent: () => Component => props => <Component classes="" {...props} />,
//     Divider: () => Component => props => <Component classes="" {...props} />,
//     Tooltip: () => Component => props => <Component classes="" {...props} />,
//     Typography: () => Component => props => <Component classes="" {...props} />,
//     IconButton: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/icons/Restore', () => ({
//     RestoreIcon: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/core/Tab', () => ({
//     Tab: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/core/Tabs', () => ({
//     Tabs: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/core/Link', () => ({
//     Link: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/icons/GetApp', () => ({
//     ExportIcon: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/core/Dialog', () => ({
//     Dialog: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/core/DialogContent', () => ({
//     DialogContent: () => Component => props => <Component classes="" {...props} />
//   }));
//   jest.mock('@material-ui/core/DialogActions', () => ({
//     DialogActions: () => Component => props => <Component classes="" {...props} />
//   }));
  // jest.mock('@react-keycloak/web', () => ({
  //   // withKeycloak: () => Component => props => <Component classes="" {...props} />
  // }));
  const props = {
    chartId: 1336,
    ROShareData: []
  };
describe('KPISummaryCharts  rendering ', () => {
  it('KPISummaryCharts Renders correctly', () => {
    const component = renderer.create(
      <KPISummaryCharts {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
//   it('Renders correctly', () => {
//     const wrapper = shallow(
//       <KPISummaryCharts {...props} />
//     );
//     expect(toJson(wrapper)).toMatchSnapshot();
//   });
});
