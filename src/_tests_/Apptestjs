import React from 'react';
import App from '../App';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
const mockStore = configureMockStore();
const store = mockStore({});
jest.mock('jquery-sparkline', () => {});
jest.mock('react-router-config', () => ({
  renderRoutes: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-ga');
describe('App render ', () => {
  it('render app properly', () => {
    const wrapper = shallow(
      <App onKeycloakEvent={jest.fn()} onKeycloakTokens={jest.fn()} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
