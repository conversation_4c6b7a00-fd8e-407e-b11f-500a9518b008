import React from 'react';
import Index from '../views/PayTypeMaster/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';

const mockStore = configureMockStore();
const store = mockStore({});
const mockJss = jest.fn();
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-router', () => ({
  useHistory: () => ({
    push: jest.fn()
  })
}));
jest.mock('react-router-dom', () => ({
  withRouter: () => ({
    push: jest.fn()
  })
}));
jest.mock('jquery-sparkline', () => {});
jest.mock('react-ga');
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Fade: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
  Box: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => (
    <Component classes="" {...props} />
  ),
  Typography: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
  Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
  Tabs: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Link', () => ({
  Link: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/CallMerge', () => ({
  CallMergeIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Error', () => ({
  ErrorIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/GetApp', () => ({
  ExportIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/InsertDriveFile', () => ({
  InsertDriveFileIcon: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/pickers', () => ({
  MuiPickersUtilsProvider: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
// jest.mock('@material-ui/core/styles', () => ({
//   StylesProvider: () => Component => props => (
//     <Component classes="" {...props} />
//   ),
//   jssPreset: () => Component => props => <Component classes="" {...props} />
// }));

jest.mock('../components/StylesProvider');
describe('Paytype index rendering ', () => {
  const route = {
    routes: ''
  };
  it('render Paytype master ', () => {
    const wrapper = shallow(
      <Index handleRefresh={jest.fn()} handleReload={jest.fn()} route={route} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
