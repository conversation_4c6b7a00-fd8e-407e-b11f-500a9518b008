import React from 'react';
import Charts from '../views/AddOns/Charts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock("src/components/charts/DashboardBarRenderer", () => () => {
  return <mock-DashboardBarRenderer data-testid="DashboardBarRenderer"/>;
});
jest.mock("src/components/DashboardActions", () => () => {
  return <mock-DashboardActions data-testid="DashboardActions"/>;
});
jest.mock("../views/AddOns/ServiceAdvisor/ComparisonChartsGrid.js", () => () => {
  return <mock-ComparisonChartsGrid data-testid="ComparisonChartsGrid"/>;
});
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
    Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
}));
  const props = {
    history:{location:{state:{tabSelection:"one"}}},
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      },
    },
    selectedDates:[]
  };
  const mockState  = 
  {chartList:[
            { active :  "1",
              chartId : "1271",
              chartName : "Job Count % - No Flat Rate Hours and No Tech Hours",
              clientId : null,
              createdAt : null,
              createdBy:null,
              dbdId: "22",
              dbdName:null,
              description:"This graph shows the job counts percentage for no tech hours with no flat rate hours on a 2 month comparison.",
              duplicateDbd:null,
              hasGoal: null,
              id: 5019,
              markdown:null,
              matViewName:null,
              parentId:  null,
              slug:null,
              sort: 8,
              updatedAt: null,
              updatedBy:  null,
              viewDetails:  null,
              __typename:"StatefulCcPhysicalRwChartMaster"
            }
          ]};
describe('WarrantyRatesGrid  rendering ', () => {
  let wrapper;
  beforeEach(() => {
    wrapper = shallow(<Charts {...props} 
      />);
  });
  it('Renders correctly', () => {
    const myComponent = shallow(<Charts {...props} />);
    myComponent.setState({ ...mockState });
    const instance = myComponent.instance();
    expect(instance.state).toEqual(mockState);

    // let tree = instance.toJSON();
    // expect(tree).toMatchSnapshot();

    // const wrapper = shallow(
    //   <AddPost  />
    // );
    expect(toJson(instance)).toMatchSnapshot();

  });
});
