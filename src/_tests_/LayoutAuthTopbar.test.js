import React from 'react';
import Topbar from '../layouts/Auth/Topbar';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/core/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />,
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogTitle', () => ({
    DialogTitle: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Popover', () => ({
    Popover: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tooltip', () => ({
    Tooltip: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ListItemIcon', () => ({
    ListItemIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ListItemText', () => ({
    ListItemText: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ListSubheader', () => ({
    ListSubheader: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AccountCircle', () => ({
    AccountCircle: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Divider', () => ({
    Divider: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/List', () => ({
    List: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ListItem', () => ({
    ListItem: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  AppBar: () => Component => props => <Component classes="" {...props} />,
  Toolbar: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  IconButton: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  MenuList: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tab', () => ({
    Tab: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Link', () => ({
    Link: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/GetApp', () => ({
    ExportIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('Topbar  rendering ', () => {
  it('Topbar Renders correctly', () => {
    const component = renderer.create(
      <Topbar {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
