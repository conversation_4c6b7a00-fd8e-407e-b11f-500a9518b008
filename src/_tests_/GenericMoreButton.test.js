import React from 'react';
import GenericMoreButton from '../components/GenericMoreButton';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    ListItemIcon: () => Component => props => <Component classes="" {...props} />,
    ListItemText: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Menu: () => Component => props => <Component classes="" {...props} />,
    MenuItem: () => Component => props => <Component classes="" {...props} />
  }));

  jest.mock('@material-ui/icons/MoreVert', () => ({
    MoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/GetApp', () => ({
    GetAppIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/FileCopy', () => ({
    FileCopyIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Print', () => ({
    PrintIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/PictureAsPdf', () => ({
    PictureAsPdfIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ArchiveOutlined', () => ({
    AchiveIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock("src/utils/bytesToSize", () => () => {
    return <mock-bytesToSize data-testid="bytesToSize"/>;
  });

describe('GenericMoreButton rendering ', () => {
 
    it('render GenericMoreButton ', () => {
      const wrapper = shallow(
        <GenericMoreButton  />
      );
      expect(toJson(wrapper)).toMatchSnapshot();
    });
  });