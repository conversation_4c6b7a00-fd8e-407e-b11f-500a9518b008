import React from 'react';
import SharedSequencesByMakeOverall from '../../views/SharedSequencesByMake/SharedSequencesByMakeOverall';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ArrowBack', () => ({
    ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Box: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  FormControl: () => Component => props => <Component classes="" {...props} />,
  InputLabel: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  Select: () => Component => props => <Component classes="" {...props} />,
  Toolbar: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  }));
  jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tab', () => ({
    Tab: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tabs', () => ({
    Tabs: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Link', () => ({
    Link: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/GetApp', () => ({
    ExportIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));
  // jest.mock('@react-keycloak/web', () => ({
  //   // withKeycloak: () => Component => props => <Component classes="" {...props} />
  // }));
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('SharedSequencesByMakeOverall  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <SharedSequencesByMakeOverall {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
