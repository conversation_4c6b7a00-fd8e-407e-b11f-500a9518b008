// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`SharedSequencesByMakeMonthly  rendering  Renders correctly 1`] = `
<div>
  <div />
  <div
    className="ag-theme-balham"
    id="data-tab"
    style={
      Object {
        "alignContent": "center",
        "display": "block",
        "height": "518px",
        "marginLeft": "8px",
        "width": "60%",
      }
    }
  >
    <div
      style={
        Object {
          "height": "100%",
        }
      }
    />
  </div>
</div>
`;

exports[`SharedSequencesByMakeMonthly  rendering  render AddPost  1`] = `
<SharedSequencesByMakeMonthly
  category="aa"
  className="layout"
  classes=""
  cols={12}
  keycloak={
    Object {
      "realmAccess": Object {
        "roles": Array [],
      },
    }
  }
  location={
    Object {
      "pathname": "/WarrantyReferenceLabor",
    }
  }
  months={
    Array [
      Object {
        "monthYear": "02/22",
      },
    ]
  }
  reportType="workmix"
  reporttype="rr"
  rowHeight={50}
  selectedDates={Array []}
  type="ss"
/>
`;
