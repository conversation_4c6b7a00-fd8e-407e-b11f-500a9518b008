import React from 'react';
import Header from '../views/Home/Header';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import renderer from 'react-test-renderer';
jest.mock("../views/Home/Dashboard.js", () => () => {
  return <mock-Dashboard data-testid="Dashboard"/>;
});
jest.mock("../views/Home/KpiSummary.js", () => () => {
  return <mock-KpiSummary data-testid="KpiSummary"/>;
});
jest.mock('@material-ui/core/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Typography', () => ({
  Typography: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  Select: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  FormControl: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  CircularProgress: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
}));
describe('Header render ', () => {
  it('render Header ', () => {
    const wrapper = shallow(<Header chartId={942} data={[]} item={''}/>);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render Header ', () => {
    
    const toggleInstance = shallow(<Header chartId={942} data={[]} item={''}/>);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(0);
    expect(toggleInstance.find('div').children().length).toBe(0);
  });
});
