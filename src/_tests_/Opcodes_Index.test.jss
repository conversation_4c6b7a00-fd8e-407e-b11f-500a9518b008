import React from 'react';
import Index from '../views/OPcodes/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch,
  connect: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />,
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  LinearProgress: () => Component => props => (
    <Component classes="" {...props} />
  ),
  Box: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@react-keycloak/web', () => ({
  withKeycloak: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
  Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
  Tabs: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Link', () => ({
  Link: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/GetApp', () => ({
  ExportIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Refresh', () => ({
  RefreshIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Button', () => ({
  Button: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Dialog', () => ({
  Dialog: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogActions', () => ({
  DialogActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContent', () => ({
  DialogContent: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Info', () => ({
  InfoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogTitle', () => ({
  DialogTitle: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Star', () => ({
  StarIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/StarBorder', () => ({
  StarBorderIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/lab', () => ({
  Alert: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Assignment', () => ({
  ReportIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ZoomOutMap', () => ({
  ZoomOutMapIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Switch', () => ({
  Switch: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/FormGroup', () => ({
  FormGroup: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/FormControlLabel', () => ({
  FormControlLabel: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Box', () => ({
  Box: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Paper', () => ({
  Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContentText', () => ({
  DialogContentText: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/core/DialogTitle', () => ({
  DialogTitle: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/lab/Alert', () => ({
  Alert: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/IconButton', () => ({
  IconButton: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Collapse', () => ({
  Collapse: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Close', () => ({
  CloseIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Restore', () => ({
  RestoreIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ExitToApp', () => ({
  ExitToAppIcon: () => Component => props => <Component classes="" {...props} />
}));
//   jest.mock('@ag-grid-enterprise/all-modules', () => ({
//     AllModules: () => Component => props => <Component classes="" {...props} />
//   }));

describe('Opcodes index rendering ', () => {
  it('render Opcodes ', () => {
    const wrapper = shallow(
      <Index handleRefresh={jest.fn()} handleReload={jest.fn()} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
