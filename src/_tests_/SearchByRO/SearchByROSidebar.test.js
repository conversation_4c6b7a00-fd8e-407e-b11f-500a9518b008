import React from 'react';
import Sidebar from '../../views/SearchByRo/Sidebar';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});

// jest.mock('@material-ui/core/styles', () => ({
//     makeStyles: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/core/AppBar', () => ({
//     AppBar: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('@material-ui/core/Tabs', () => ({
//     Tabs: () => Component => props => <Component classes="" {...props} />
// }));
//   jest.mock('@material-ui/core/Tab', () => ({
//     Tab: () => Component => props => <Component classes="" {...props} />
//   }));
  // jest.mock('@material-ui/core/Typography', () => ({
  //   TabTypography: () => Component => props => <Component classes="" {...props} />
  // }));
  // jest.mock('@material-ui/core/Box', () => ({
  //   Box: () => Component => props => <Component classes="" {...props} />
  // }));
  const props = {
    realm : 'haleyag',
    xml: '',
    json:''
  };
describe('WarrantyRatesGrid  rendering ', () => {
  it('Renders correctly', () => {
    const wrapper = shallow(
      <Sidebar   children="" value="" index="" props={props}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
    // const component = renderer.create(
    //   <Sidebar   children="" value="" index="" props={props}
    //   />
    // );
    // let tree = component.toJSON();
    // expect(tree).toMatchSnapshot();
  });
  it('render Sidebar ', () => {
    const toggleInstance = shallow(<Sidebar   children="" value="" index="" props={props}/>);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(1);
    const eleme=toggleInstance.find('XMLViewer');
    expect(eleme.length).toBe(2);
    const elemem=toggleInstance.find('TabPanel');
    expect(elemem.length).toBe(2);
    expect(toggleInstance.find('div').children().length).toBe(3);
    expect(toggleInstance.find('TabPanel').children().length).toBe(2);
    expect(toggleInstance.find('XMLViewer').children().length).toBe(0);
  });
});
