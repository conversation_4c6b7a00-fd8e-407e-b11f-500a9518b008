import React from 'react';
import PartListGrid from '../../views/SearchByRo/PartListGrid';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
// import * as redux from 'react-redux';
import { useSelector, useDispatch } from 'react-redux'; 

const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch
}));
// const spy = jest.spyOn(redux, 'useSelector');
// spy.mockReturnValue({
//   isLoading: true,
//   storeSelected: '',
//   storeName: '',
//   storeId: '',
//   user:{first_name:''}
// });


jest.mock('@material-ui/core', () => ({
    Button: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />
  }));
    jest.mock('@material-ui/core/TableBody', () => ({
      TableBody: () => Component => props => <Component classes="" {...props} />
    }));
    jest.mock('@material-ui/core/TableCell', () => ({
      TableCell: () => Component => props => <Component classes="" {...props} />
    }));
    jest.mock('@material-ui/core/TableRow', () => ({
      TableRow: () => Component => props => <Component classes="" {...props} />
    }));
    jest.mock('@material-ui/core/Tooltip', () => ({
      Tooltip: () => Component => props => <Component classes="" {...props} />
    }));

jest.mock('@material-ui/core', () => ({
    Card: () => Component => props => <Component classes="" {...props} />,
    CardContent: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => (
    <Component classes="" {...props} />
    ),
    Input: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Send', () => ({
  SendIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AddPhotoAlternate', () => ({
  AddPhotoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AttachFile', () => ({
    AttachFileIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />,
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
// jest.mock('@material-ui/core/styles', () => ({
//   withStyles: () => Component => props => <Component classes="" {...props} />
// }));
// jest.mock('../components/StylesProvider');
const tempart ={
    prtpaytypegroup : 'W',
    filterByPrt : 'sale_cost',
    filterByCategoryPrt : 'prt_sale_cost_known_category_allocated',
    prtunitsale: 1.23,
    prtunitcost : 2.65,
    prtextendedsale: 8.09,
    prtextendedcost: 5.544,
    prtdesc:'',
    prtpaytype:1.44,
    quantity: 322,
    filterByPrt: 'sale_cost'
 };
describe('PartListGrid rendering ', () => {
 
  it('render PartListGrid', () => {
    const wrapper = shallow(
      <PartListGrid  tempPart={tempart} keyPart={1}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
