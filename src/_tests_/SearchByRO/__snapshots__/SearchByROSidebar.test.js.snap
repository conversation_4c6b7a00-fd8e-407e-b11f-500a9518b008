// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`WarrantyRatesGrid  rendering  Renders correctly 1`] = `
<div
  className="makeStyles-root-1"
>
  <WithStyles(ForwardRef(AppBar))
    position="sticky"
  >
    <WithStyles(ForwardRef(Tabs))
      aria-label="simple tabs example"
      onChange={[Function]}
      value={0}
    >
      <WithStyles(ForwardRef(Tab))
        aria-controls="simple-tabpanel-0"
        id="simple-tab-0"
        label="Relevant XML Nodes"
        wrapped={true}
      />
      <WithStyles(ForwardRef(Tab))
        aria-controls="simple-tabpanel-1"
        id="simple-tab-1"
        label="Complete XML"
        wrapped={true}
      />
    </WithStyles(ForwardRef(Tabs))>
  </WithStyles(ForwardRef(AppBar))>
  <TabPanel
    index={0}
    value={0}
  >
    <XMLViewer
      collapsible={true}
      indentSize={2}
      invalidXml={
        <div>
          Invalid XML!
        </div>
      }
      theme={
        Object {
          "attributeKeyColor": "#FF0000",
          "attributeValueColor": "#000FF",
        }
      }
      xml=""
    />
  </TabPanel>
  <TabPanel
    index={1}
    value={0}
  >
    <XMLViewer
      collapsible={true}
      indentSize={2}
      invalidXml={
        <div>
          Invalid XML!
        </div>
      }
      theme={
        Object {
          "attributeKeyColor": "#FF0000",
          "attributeValueColor": "#000FF",
        }
      }
    />
  </TabPanel>
</div>
`;
