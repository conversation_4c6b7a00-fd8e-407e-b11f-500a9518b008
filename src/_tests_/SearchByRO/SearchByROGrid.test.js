import React from 'react';
import SearchByROGrid from '../../views/SearchByRo/SearchByROGrid';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
jest.mock('react-router', () => ({
    ...jest.requireActual('react-router'),
    useHistory: () => ({
      location: {
        search: '/help',
     },
   }),
  }));
jest.mock('react-redux', () => ({
    useSelector: jest.fn(),
    useDispatch: () => mockDispatch,
    connect: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />,
    withStyles: () => Component => props => <Component classes="" {...props} />    
}));
jest.mock('@material-ui/core', () => ({
  ExpansionPanel: () => Component => props => <Component classes="" {...props} />,
  ExpansionPanelSummary: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ExpansionPanel', () => ({
  MuiExpansionPanel: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ExpansionPanelSummary', () => ({
  MuiExpansionPanelSummary: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ExpansionPanelDetails', () => ({
  MuiExpansionPanelDetails: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ExpandMore', () => ({
  ExpandMoreIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Table', () => ({
  Table: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TableBody', () => ({
  TableBody: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TableCell', () => ({
  TableCell: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tooltip', () => ({
  Tooltip: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TableRow', () => ({
  TableRow: () => Component => props => <Component classes="" {...props} />
}));

jest.mock("../../views/SearchByRo/PartListGrid.js", () => () => {
  return <mock-PartListGrid data-testid="PartListGrid"/>;
});
  // const props = {
  //     params: '',
  //     isFrom: 'help',
  //     Keycloak:{
  //         realmAccess:{
  //             roles:['admin']
  //         }
  //     },
  //     previousToggle:"",
  //     parent:'',
  //     tabType:'',
  //     PrevPayType:'',
  //     PrevGridType:''
  // };
  const individualRow = {
    lbrlinecode:''
  }
describe('SearchByROGrid  rendering ', () => {
  it('Renders SearchByROGrid correctly', () => {
    const wrapper = shallow(
      <SearchByROGrid individualRow={individualRow} roNumberData={[]}
        dataByJob={[]}
        partList={[]}
        partByLbr={[]}
      />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
    // const component = renderer.create(
    //   <Sidebar   children="" value="" index="" props={props}
    //   />
    // );
    // let tree = component.toJSON();
    // expect(tree).toMatchSnapshot();
  });
  it('Renders SearchByROGrid correctly', () => {
    const component = renderer.create(
      <SearchByROGrid   children="" value="" index="" 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
  it('render SearchByROGrid ', () => {
    const toggleInstance = shallow(<SearchByROGrid   children="" value="" index="" />);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(1);
    const eleme=toggleInstance.find('XMLViewer');
    expect(eleme.length).toBe(2);
    const elemem=toggleInstance.find('TabPanel');
    expect(elemem.length).toBe(2);
    expect(toggleInstance.find('div').children().length).toBe(3);
    expect(toggleInstance.find('TabPanel').children().length).toBe(2);
    expect(toggleInstance.find('XMLViewer').children().length).toBe(0);
  });
});
