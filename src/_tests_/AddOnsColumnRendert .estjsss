import React from 'react';
import ColumnRenderer from '../views/AddOns/ServiceAdvisor/ColumnRenderer';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
import { getColorScheme, getComparisonMonths } from 'src/utils/Utils';

jest.mock('src/utils/Utils.js', () => ({ getColorScheme: jest.fn(),getComparisonMonths: jest.fn() }));
getColorScheme.mockImplementation(() => {
  return ["2019-12-01", "2020-12-31"];
});
getComparisonMonths.mockImplementation(() => {
  return [{
    "member": "CPGrossProfitPercentage.store_id",
    "operator": "contains",
    "values": [
      "75627608",
      "75627643",
    ]
  }];
});

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ArrowBack', () => ({
    ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-router', () => ({
  ...jest.requireActual('react-router'),
  useHistory: () => ({
    location: { search: 'localhost:3000/' }
  })
}));
jest.mock('@material-ui/core', () => ({
    CircularProgress: () => Component => props => <Component classes="" {...props} />,
    Grid: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    Card: () => Component => props => <Component classes="" {...props} />,
    CardContent: () => Component => props => <Component classes="" {...props} />,
    CardHeader: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock("src/components/MoreActions", () => () => {
  return <mock-MoreActions data-testid="MoreActions"/>;
});
jest.mock("src/components/Dialog", () => () => {
  return <mock-ChartDialog data-testid="ChartDialog"/>;
});
jest.mock("src/utils/hasuraServices", () => () => {
    return <mock-hasuraServices data-testid="hasuraServices"/>;
  });
  jest.mock("src/components/ViewGraphDetailsAction", () => () => {
    return <mock-ViewGraphDetailsAction data-testid="ViewGraphDetailsAction"/>;
  });
  jest.mock("src/utils/Utils", () => () => {
    return <mock-Utils data-testid="Utils"/>;
  });


  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('WarrantyRatesGrid  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <ColumnRenderer {...props} 
      datatype={''}
      removeFav={''}
      month1={''}
      type={''}
      month2={''}
      chartId={''}
      isFrom={''}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
