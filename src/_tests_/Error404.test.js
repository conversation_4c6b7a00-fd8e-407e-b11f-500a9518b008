import React from 'react';
import Error404 from '../views/Error404';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Typography: () => Component => props => <Component classes="" {...props} />,
    Button: () => Component => props => <Component classes="" {...props} />,
    useTheme: () => Component => props => <Component classes="" {...props} />,
    useMediaQuery: () => Component => props => <Component classes="" {...props} />,
}));

describe('Error404 rendering ', () => {
  it('render Error404 ', () => {
    const wrapper = shallow(
      <Error404  useMediaQuery={jest.fn()} theme={jest.fn()} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
