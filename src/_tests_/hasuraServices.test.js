import { 
    getRefreshViewsStatus,
 } from '../utils/hasuraServices';
import * as redux from 'react-redux';
import { Category } from '@material-ui/icons';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
series:{symbol:'circle'}
});
describe("getRefreshViewsStatus ", () => {
const color = ["#3366CC", "#DC3912", "#109618"];
test('render getRefreshViewsStatus properly', () => {
expect(getRefreshViewsStatus(0)).toStrictEqual(0);
});
});