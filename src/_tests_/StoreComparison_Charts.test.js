import React from 'react';
import Charts from '../views/StoreComparison/Charts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
jest.mock("src/components/DashboardActions", () => () => {
  return <mock-DashboardActions data-testid="DashboardActions"/>;
});
jest.mock("src/components/charts/DashboardLineRenderer", () => () => {
  return <mock-DashboardLineRenderer data-testid="DashboardLineRenderer"/>;
});
jest.mock("src/components/charts/DashboardCompare", () => () => {
  return <mock-DashboardCompare data-testid="DashboardCompare"/>;
});
jest.mock("src/components/DashboardDetailsActions", () => () => {
  return <mock-DashboardDetailsActions data-testid="DashboardDetailsActions"/>;
});
jest.mock("src/components/PageHeader", () => () => {
  return <mock-PageHeader data-testid="PageHeader"/>;
});

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  chartList: [{ dbdName:'CP Overview' }]
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ArrowBack', () => ({
    ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Paper: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  }));
  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/styles', () => ({
    styled: () => Component => props => <Component classes="" {...props} />
  }));
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak: { 
      realm: "hayleg"
    },
    selectedDates:[]
  };
describe('StoreComparison  rendering ', () => {
  it('Renders Charts correctly', () => {
    const component = renderer.create(
      <Charts {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
