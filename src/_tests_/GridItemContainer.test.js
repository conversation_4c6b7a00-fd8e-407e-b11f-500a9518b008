import React from 'react';
import GridItemContainer from '../views/Home/GridItemContainer';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import renderer from 'react-test-renderer';
jest.mock("../views/Home/GridItem.js", () => () => {
  return <mock-GridItem data-testid="GridItem"/>;
});
describe('GridItemContainer render ', () => {
  it('render GridItemContainer ', () => {
    const wrapper = shallow(<GridItemContainer chartId={942} data={[]} item={''}/>);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render GridItemContainer ', () => {
    
    const toggleInstance = shallow(<GridItemContainer chartId={942} data={[]} item={''}/>);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(0);
    expect(toggleInstance.find('div').children().length).toBe(0);
  });
});
