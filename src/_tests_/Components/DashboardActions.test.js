import React from 'react';
import DashboardActions from '../../components/DashboardActions';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

const mockStore = configureMockStore();
const store = mockStore({});
describe("DashboardActions render ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });
   
      const title= "CP Overview";  
    it("render DashboardActions ", () => {
        const wrapper =  shallow(
                    <DashboardActions
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    // it("render DashboardActions with Title", () => {
    //     const wrapper =  shallow(
    //                 <DashboardActions
    //                    setTitle = {title}
    //                  />
    //         );
    //     expect(toJson(wrapper)).toMatchSnapshot();
    // });
});   