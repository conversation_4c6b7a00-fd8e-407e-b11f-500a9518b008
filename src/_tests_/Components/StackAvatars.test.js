import React from 'react';
import StackAvatars from '../../components/StackAvatars';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Avatar: () => Component => props => <Component classes="" {...props} />
}));
describe('StackAvatars ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  const title = 'CP Overview';
  const avatars = ['10','200','9833']
  it('render StackAvatars ', () => {
    const wrapper = shallow(
      <StackAvatars value={1} star={1} className={"ReviewStars"} avatars={avatars}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render AddPost ', () => {
    
    const toggleInstance = shallow(<StackAvatars value={1} star={1} className={"ReviewStars"} avatars={avatars}/>);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(1);
  });
});
