import React from 'react';
import Page from '../../components/Page';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';

jest.mock('@material-ui/core/styles');
jest.mock('react-router', () => ({
  useLocation: jest.fn()
}));
describe("Page render ", () => {
  it("render Page ", () => {
    const wrapper =  shallow(
      <Page
        title = {"Overview"}
      />
    );
    expect(to<PERSON><PERSON>(wrapper)).toMatchSnapshot();
  });
});   