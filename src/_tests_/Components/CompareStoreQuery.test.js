import { getCompareStoreQuery } from '../../components/CompareStoreQuery';
import { getDetailedGraphDateRange } from '../../components/ViewGraphDetailsAction';

jest.mock('../../components/ViewGraphDetailsAction', () => ({ getDetailedGraphDateRange: jest.fn() }));
getDetailedGraphDateRange.mockImplementation(() => {
  return ["2019-12-01", "2020-12-31"];
});

describe("getCompareStoreQuery Component", () => {
const chartId=942;  
const storeId = ["75627608","75627643"];
const result = {
        'measures': [
          'CPRevenue.combined'
        ],
        'dimensions': ['CPRevenue.closeddate.month','CPRevenue.store_id'],
        'timeDimensions': [
          {
            'dimension': 'CPRevenue.closeddate',
            'dateRange': ["2019-12-01", "2020-12-31"],
            'granularity': 'month'
          }
        ]
      }
const results = {
  'measures': ['CPGrossProfit.combinedgrossprofit'],
  'dimensions': ['CPGrossProfit.ro_date.month','CPGrossProfit.store_id'],
  'timeDimensions': [
    {
      'dimension': 'CPGrossProfit.ro_date',
      'dateRange': ["2019-12-01", "2020-12-31"],
      'granularity': 'month',
    }
  ],
      }      
  test('render getCompareStoreQuery properly 942', () => {
    expect(getCompareStoreQuery(942)).toStrictEqual(result);
  });
  test('render getCompareStoreQuery properly 939', () => {
    expect(getCompareStoreQuery(939)).toStrictEqual(results);
  });
});