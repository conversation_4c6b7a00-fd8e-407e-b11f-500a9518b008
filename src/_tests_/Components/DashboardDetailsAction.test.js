
import React from 'react';
import DashboardDetailsActions from '../../components/DashboardDetailsActions';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";
import renderer from 'react-test-renderer';
jest.mock('@material-ui/core/Chip', () => ({
    Chip: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core/Switch', () => ({
    Switch: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/FormGroup', () => ({
    FormGroup: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/FormControlLabel', () => ({
    FormControlLabel: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core', () => ({
    Divider: () => Component => props => <Component classes="" {...props} />,
    Grid: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    MenuItem: () => Component => props => <Component classes="" {...props} />,
    Select: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
  }));
  jest.mock('@material-ui/icons/FilterList', () => ({
    FilterListIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ArrowBack', () => ({
    ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
  }));
const mockStore = configureMockStore();
const store = mockStore({});
describe("DashboardDetailsActions render ", () => {
    const title = "CP Revenue";
    it("render DashboardDetailsActions ", () => {
        const wrapper =  shallow(
                    <DashboardDetailsActions
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    it("render DashboardDetailsActions with Title", () => {
        const wrapper =  shallow(
            <DashboardDetailsActions
              setTitle = {title}
              resetDashboard={jest.fn()}
              redirectHome={jest.fn()}
              showCurrentMonth={''}
            />
        );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render DashboardDetailsActions', () => {
        const toggleInstance = shallow(
          <DashboardDetailsActions
            setTitle = {title}
            resetDashboard={jest.fn()}
            redirectHome={jest.fn()}
            showCurrentMonth={''} />);
        const elements=toggleInstance.find('div');
        expect(elements.length).toBe(1);
        const elem=toggleInstance.find('Grid');
        expect(elem.length).toBe(3);
        const eleme=toggleInstance.find('Tooltip');
        expect(eleme.length).toBe(2);
        const elemen=toggleInstance.find('IconButton');
        expect(elemen.length).toBe(2);
        const element=toggleInstance.find('Typography');
        expect(element.length).toBe(1);
        const element1=toggleInstance.find('Divider');
        expect(element1.length).toBe(1);
        
        expect(toggleInstance.find('div').children().length).toBe(2);
        expect(toggleInstance.find('Grid').children().length).toBe(5);
        expect(toggleInstance.find('Tooltip').children().length).toBe(2);
        expect(toggleInstance.find('IconButton').children().length).toBe(2);
        expect(toggleInstance.find('Typography').children().length).toBe(1);
        expect(toggleInstance.find('Divider').children().length).toBe(0);
      });
});   