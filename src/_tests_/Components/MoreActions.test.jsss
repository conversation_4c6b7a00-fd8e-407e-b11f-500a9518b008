import React from 'react';
import MoreActions from '../components/MoreActions';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";
const mockDispatch = jest.fn();
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => mockDispatch
}));
JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
const mockStore = configureMockStore();
const store = mockStore({});
describe("MoreActions render ", () => {
      const title= "CP Overview";  
    it("render MoreActions ", () => {
        const wrapper =  shallow(
                    <MoreActions
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
});   