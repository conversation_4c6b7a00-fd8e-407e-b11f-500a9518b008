import React from 'react';
import Dialog from '../components/Dialog';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
jest.mock("src/components/charts/DashboardBarRenderer.js", () => () => {
  return <mock-DashboardBarRenderer data-testid="DashboardBarRenderer"/>;
});
jest.mock("src/components/charts/DashboardLineRenderer.js", () => () => {
  return <mock-DashboardLineRenderer data-testid="DashboardLineRenderer"/>;
});
jest.mock("src/views/AnalyzeData/TechEfficiency/BarChartRenderer", () => () => {
  return <mock-TechBarChartRenderer data-testid="TechBarChartRenderer"/>;
});
jest.mock("src/views/AnalyzeData/TechEfficiency/ColumnRenderer", () => () => {
  return <mock-TechBarColumnRenderer data-testid="TechBarColumnRenderer"/>;
});
jest.mock("src/views/Discounts/DiscountColumnRenderer", () => () => {
  return <mock-TechBarColumnRenderer data-testid="TechBarColumnRenderer"/>;
});
jest.mock("src/views/AnalyzeData/ServiceAdvisorEfficiency/OpCategoryChartRenderer", () => () => {
  return <mock-OpCategoryChartRenderer data-testid="OpCategoryChartRenderer"/>;
});
jest.mock("src/views/AnalyzeData/ServiceAdvisorEfficiency/ColumnRenderer", () => () => {
  return <mock-AdvisorColumnRenderer data-testid="AdvisorColumnRenderer"/>;
});
jest.mock("src/views/AnalyzeData/ServiceAdvisorEfficiency/BarChartRenderer", () => () => {
  return <mock-AdvisorBarChartRenderer data-testid="AdvisorBarChartRenderer"/>;
});
jest.mock("src/views/AnalyzeData/WorkMixCharts/BarChartRenderer", () => () => {
  return <mock-BarChartRenderer data-testid="BarChartRenderer"/>;
});
jest.mock("src/components/charts/CPPartsMarkupVsPartsCost", () => () => {
  return <mock-CPPartsMarkupVsPartsCost data-testid="CPPartsMarkupVsPartsCost"/>;
});
jest.mock("src/components/charts/CPMovingPartsMarkup", () => () => {
  return <mock-CPMovingPartsMarkup data-testid="CPMovingPartsMarkup"/>;
});
jest.mock("src/components/charts/CPELRVsLaborSoldHours", () => () => {
  return <mock-CPELRVsLaborSoldHours data-testid="CPELRVsLaborSoldHours"/>;
});
jest.mock("src/views/AnalyzeData/ComparisonCharts/ColumnRenderer", () => () => {
  return <mock-ColumnRenderer data-testid="ColumnRenderer"/>;
});
jest.mock("src/views/AddOns/ServiceAdvisor/ColumnRenderer", () => () => {
  return <mock-AddonSAColumnRenderer data-testid="AddonSAColumnRenderer"/>;
});
jest.mock("src/views/AnalyzeData/ComparisonCharts/ColumnRenderer", () => () => {
  return <mock-AddonColumnRenderer data-testid="AddonColumnRenderer"/>;
});
jest.mock("src/components/charts/OpportunityCharts", () => () => {
  return <mock-OpportunityCharts data-testid="OpportunityCharts"/>;
});
jest.mock("src/components/charts/CPMovingELR", () => () => {
  return <mock-CPMovingELR data-testid="CPMovingELR"/>;
});

// import { getChartName } from 'src/components/ViewGraphDetailsAction';
// import { getDataGridConfiguration } from 'src/utils/Utils';
// import { getDashboardGraphQuery } from 'src/components/DashboardGraphQuery';




JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/core/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/core/Box', () => ({
    Box: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContentText', () => ({
    DialogContentText: () => Component => props => <Component classes="" {...props} />
}));

  const props = {
    title:'Data Card',
    value:' Data value',
    icon:'data.png'
  };
describe('Dialog  rendering ', () => {
  it('Renders correctly', () => {
    // const component = renderer.create(
    //   <DataCard {...props} 
    //   />
    // );
    // let tree = component.toJSON();
    // expect(tree).toMatchSnapshot();
    const wrapper = shallow(
        <Dialog {...props} />
      );
      expect(toJson(wrapper)).toMatchSnapshot();
  });
});
