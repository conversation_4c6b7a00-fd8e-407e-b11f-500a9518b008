import React from 'react';
import {
  getLineChartType,
  GetyAxisRange,
  getChartType,
  getChartName,
  getChartDrillDown,
  getChartDataIndex,
  getChartText,
  getDetailedGraphDateRange,
  getChartPayType,
  getDataforHasuraBarChart,
  getBarChartConfiguration,
  getYAxisLabel,
  getChartOpCategory,
  getTimeDimension,
  getDateRange,
  getQueryFilter,
  getDataforBarChart
} from '../../components/ViewGraphDetailsAction.js';

Storage.prototype.getItem = jest.fn(() => '2021-07-01,2022-07-31');
const resultSet = [
  {
    __typename: 'dbd_cp_overview_ux_gross_profit_percentage_service_advisor',
    combinedgrossprofitpercentage: '0.662',
    laborgrossprofitpercentage: '0.781',
    month_year: '2019-07',
    partsgrossprofitpercentage: '0.485',
    rodate: '2019-07-01'
  },
  {
    __typename: 'dbd_cp_overview_ux_gross_profit_percentage_service_advisor',
    combinedgrossprofitpercentage: '0.351',
    laborgrossprofitpercentage: '0.351',
    month_year: '2019-07',
    partsgrossprofitpercentage: null,
    rodate: '2019-07-01'
  },
  {
    __typename: 'dbd_cp_overview_ux_gross_profit_percentage_service_advisor',
    combinedgrossprofitpercentage: '0.623',
    laborgrossprofitpercentage: '0.738',
    month_year: '2019-08',
    partsgrossprofitpercentage: '0.481',
    rodate: '2019-08-01'
  }
];
const result = {
  datasets: [
    {
      backgroundColor: '#3366CC',
      borderColor: '#3366CC',
      borderWidth: 1.5,
      data: ['0.781', '0.351', '0.738'],
      fill: false,
      label: 'Labor Gross Profit %',
      type: 'bar'
    }
  ],
  labels: [
    '2019-07-01T00:00:00.000Z',
    '2019-07-01T00:00:00.000Z',
    '2019-08-01T00:00:00.000Z',
    '2019-08-01T00:00:00.000Z',
    '2019-09-01T00:00:00.000Z',
    '2019-09-01T00:00:00.000Z',
    '2019-10-01T00:00:00.000Z',
    '2019-11-01T00:00:00.000Z',
    '2019-12-01T00:00:00.000Z',
    '2020-01-01T00:00:00.000Z',
    '2020-02-01T00:00:00.000Z',
    '2020-03-01T00:00:00.000Z',
    '2020-04-01T00:00:00.000Z'
  ],
  datasets: [
    {
      backgroundColor: '#3366CC',
      borderColor: '#3366CC',
      borderWidth: 1.5,
      data: ['0.781', '0.351', '0.738'],
      fill: false,
      label: 'Parts Gross Profit %',
      type: 'bar'
    }
  ],
  labels: [
    '2019-07-01T00:00:00.000Z',
    '2019-07-01T00:00:00.000Z',
    '2019-08-01T00:00:00.000Z',
    '2019-08-01T00:00:00.000Z',
    '2019-09-01T00:00:00.000Z',
    '2019-09-01T00:00:00.000Z',
    '2019-10-01T00:00:00.000Z',
    '2019-11-01T00:00:00.000Z',
    '2019-12-01T00:00:00.000Z',
    '2020-01-01T00:00:00.000Z',
    '2020-02-01T00:00:00.000Z',
    '2020-03-01T00:00:00.000Z',
    '2020-04-01T00:00:00.000Z'
  ]
};
const filter = [
  {
    __typename: 'dms_physical_rw_chart_master',

    chart_id: 1150,

    chart_name: 'RO Count - Maintenance Plan',

    created_at: null,

    created_by: null,

    dbd_id: 1,

    dbd_name: 'CP Overview',

    description: null,

    has_goal: 0,

    id: 640,

    mat_view_name: '',

    parent_id: 925,

    slug: 'ro-count-maintenance-plan',

    sort: 5,

    updated_at: null,

    updated_by: null,

    view_details: 'dbd_cp_overview.vw_detail_repair_order_count'
  },
  {
    __typename: 'dms_physical_rw_chart_master',

    chart_id: 940,

    chart_name: 'CP Gross Profit Percentage',

    created_at: null,

    created_by: null,

    dbd_id: 1,

    dbd_name: 'CP Overview',

    description:
      'This Graph shows the Gross Profit Percentage of Customer Pay for the Parts, Labor and Combined',

    has_goal: 0,

    id: 862,

    mat_view_name: null,

    parent_id: null,

    slug: 'cp-gross-profit-percentage',

    sort: 3,

    updated_at: null,

    updated_by: null,

    view_details: 'dbd_cp_overview.ux_gross_profit_percentage_all'
  }
];
const resultData = {
  legend: {
    align: 'center',
    labels: {
      boxWidth: 12
    },
    position: 'bottom'
  },
  scales: {
    xAxes: [
      {
        display: true,
        ticks: {
          autoSkip: false,
          beginAtZero: true
        }
      }
    ],
    yAxes: [
      {
        display: true,
        ticks: {
          min: null,
          stepSize: null
        }
      }
    ]
  },
  tooltips: {
    backgroundColor: '#2121216e',
    mode: 'index'
  }
};
const queryfilter = [
  {
    member: 'CPRevenue.serviceadvisor',
    operator: 'equals',
    values: [
      {
        __typename: 'dms_physical_rw_chart_master',
        chart_id: 1150,
        chart_name: 'RO Count - Maintenance Plan',
        created_at: null,
        created_by: null,
        dbd_id: 1,
        dbd_name: 'CP Overview',
        description: null,
        has_goal: 0,
        id: 640,
        mat_view_name: '',
        parent_id: 925,
        slug: 'ro-count-maintenance-plan',
        sort: 5,
        updated_at: null,
        updated_by: null,
        view_details: 'dbd_cp_overview.vw_detail_repair_order_count'
      },
      {
        __typename: 'dms_physical_rw_chart_master',
        chart_id: 940,
        chart_name: 'CP Gross Profit Percentage',
        created_at: null,
        created_by: null,
        dbd_id: 1,
        dbd_name: 'CP Overview',
        description:
          'This Graph shows the Gross Profit Percentage of Customer Pay for the Parts, Labor and Combined',
        has_goal: 0,
        id: 862,
        mat_view_name: null,
        parent_id: null,
        slug: 'cp-gross-profit-percentage',
        sort: 3,
        updated_at: null,
        updated_by: null,
        view_details: 'dbd_cp_overview.ux_gross_profit_percentage_all'
      }
    ]
  },
  {
    member: 'CPRevenue.store_id',
    operator: 'contains',
    values: undefined
  }
];
describe('ViewGraphDetailsAction Component', () => {
  test('render getLineChartType properly', () => {
    expect(getLineChartType(942)).toBe(1);
  });
  test('render GetyAxisRange properly', () => {
    expect(GetyAxisRange(941)).toStrictEqual([0, 50000]);
  });
  test('render GetyAxisRange properly with isFrom', () => {
    expect(GetyAxisRange(920, 'retail')).toStrictEqual([0, 250]);
  });
  test('render getChartType properly', () => {
    expect(getChartType(940)).toStrictEqual(5);
  });

   // Storage Mock
   function storageMock() {
    let storage = {};

    return {
      setItem: function(key, value) {
        storage[key] = value || '';
      },
      getItem: function(key) {
        return key in storage ? storage[key] : null;
      },
      removeItem: function(key) {
        delete storage[key];
      },
      get length() {
        return Object.keys(storage).length;
      },
      key: function(i) {
        const keys = Object.keys(storage);
        return keys[i] || null;
      }
    };
  }
  window.localStorage = storageMock();
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    // return your what your code is returning.
  });
  // test('render getChartName properly', () => {
  //   expect(getChartName(940)).toStrictEqual('CP Gross Profit Percentage');
  // });
  test('render getChartDrillDown properly', () => {
    expect(getChartDrillDown(1069)).toStrictEqual(3);
  });
  test('render getChartDrillDown properly with search', () => {
    expect(getChartDrillDown(1141, 'CPPRC')).toStrictEqual(12);
  });
  test('render getChartDataIndex properly', () => {
    expect(getChartDataIndex(1068)).toBe(2);
  });
  test('render getChartDataIndex properly with dataIndex', () => {
    expect(getChartDataIndex(1068, 2)).toBe(2);
  });
  test('render getChartDataIndex properly with label', () => {
    expect(getChartDataIndex(1111, 'Labor Discount')).toBe(2);
  });
  // test('render getChartText properly', () => {
  //   expect(getChartText(942)).toBe('CPR');
  // });
  // test('render getDataforHasuraBarChart properly bar', () => {
  //   expect(getDataforHasuraBarChart(resultSet,940)).toMatchObject(result);
  // });
  test('render getBarChartConfiguration properly bar', () => {
    expect(getBarChartConfiguration(940, undefined, undefined)).toMatchObject(
      resultData
    );
  });
  // test('render getYAxisLabel properly', () => {
  //   expect(getYAxisLabel(938,"CPR")).toBe('CPR');
  // });
  test('render getChartOpCategory properly', () => {
    expect(getChartOpCategory(988)).toBe('R');
  });
  test('render getChartPayType properly', () => {
    expect(getChartPayType(969)).toBe('W');
  });
  test('render getTimeDimension properly', () => {
    expect(getTimeDimension(942)).toStrictEqual('CPRevenue.closeddate');
  });
  test('render getDateRange properly', () => {
    expect(getDateRange(942)).toStrictEqual('Last Year');
  });
  test('render getDetailedGraphDateRange properly', () => {
    expect(getDetailedGraphDateRange('2021-07-01,2022-07-31')).toStrictEqual([
      '2021-07-01','2022-07-31'
    ]);
  });
  test('render getQueryFilter properly', () => {
    expect(getQueryFilter(filter, 'CPRevenue')).toStrictEqual(queryfilter);
  });
  const resultSetData = {
    backwardCompatibleData:[{CPRevenue : {closeddate: "2021-08-01T00:00:00.000",combined: "157269.87"} }],
    loadResponse:[{CPRevenue : {closeddate: "2021-08-01T00:00:00.000",combined: "157269.87"} }],
    CPRevenue : { parts_revenue: 70700.05 },
    categories:({}),
    category: "2021-08-01T00:00:00.000",
    x: "2021-08-01T00:00:00.000"
    };
    
  // test('render getDataforBarChart properly bar', () => {
  //   expect(getDataforBarChart(resultSetData, 1067 , undefined)).toMatchObject(
  //     resultSetData
  //   );
  // });
});

