import { getDashboardAggrigationQuery } from '../../components/DashboardAggrigationQuery';
import { getDetailedGraphDateRange,getStoreIdFilter } from '../../components/ViewGraphDetailsAction';

jest.mock('../../components/ViewGraphDetailsAction', () => ({ getDetailedGraphDateRange: jest.fn(),getStoreIdFilter: jest.fn() }));
getDetailedGraphDateRange.mockImplementation(() => {
  return ["2019-12-01", "2020-12-31"];
});
getStoreIdFilter.mockImplementation(() => {
  return [{
    "member": "CPGrossProfitPercentage.store_id",
    "operator": "contains",
    "values": [
      "75627608",
      "75627643",
    ]
  }];
});
describe("getDashboardAggrigationQuery Component", () => {
  const chartId=940;  
  const storeId = ["75627608","75627643"];
  const result = {
        'measures': [
          'CPGrossProfitPercentage.lbraggrigateprofitpercent',
          'CPGrossProfitPercentage.partsaggrigateprofitpercent',
          'CPGrossProfitPercentage.combinedaggrigateprofitpercent'
        ],
        'timeDimensions': [
          {
            'dimension': 'CPGrossProfitPercentage.ro_date',
            'dateRange': ["2019-12-01", "2020-12-31"]
          }
        ],
        'dimensions': ['CPGrossProfitPercentage.ro_date'],
        'filters':[
            {
              'member': 'CPGrossProfitPercentage.store_id',
              'operator': 'contains',
              'values': [ '75627608', '75627643' ]
            }
          ],
        'order': {
          'CPGrossProfitPercentage.ro_date': 'asc'
        }
      }
// const chartId=942;  
// const storeId = ["75627608","75627643"];
// const result = {
//         'measures': [
//           'CPRevenue.combined'
//         ],
//         'dimensions': ['CPRevenue.closeddate.month','CPRevenue.store_id'],
//         'timeDimensions': [
//           {
//             'dimension': 'CPRevenue.closeddate',
//             'dateRange': ["2019-12-01", "2020-12-31"],
//             'granularity': 'month'
//           }
//         ]
//       }
      test('render getDashboardAggrigationQuery properly', () => {
        expect(getDashboardAggrigationQuery(940)).toStrictEqual(result);
      });
    });