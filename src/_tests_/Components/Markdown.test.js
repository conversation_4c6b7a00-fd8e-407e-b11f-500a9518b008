import React from 'react';
import Markdown from '../../components/Markdown';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
// jest.mock('react-markdown/with-html', () => ({
//     ReactMarkdown: () => Component => props => <Component classes="" {...props} />
// }));
jest.mock('prismjs', () => ({
    Prism: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  colors: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("react-markdown/with-html", () => () => {
    return <mock-ReactMarkdown data-testid="ReactMarkdown"/>;
  });
describe('Markdown  rendering ', () => {
  it('Renders correctly', () => {
    const wrapper = shallow(
      <Markdown  className={"Markdown"}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
