// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Paytype index rendering  render Paytype master  1`] = `
<Dialog
  id="scroll"
  maxWidth="sm"
  style={
    Object {
      "height": "107%",
      "marginTop": "-30px",
    }
  }
>
  <div
    className=""
  >
    <div>
      <Typography
        align="center"
        gutterBottom={true}
        variant="h5"
      >
        Support & Feedback
      </Typography>
    </div>
    <div>
      <Grid
        container={true}
        spacing={4}
      >
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "marginTop": "-5px",
              "padding": "0px 16px",
            }
          }
          xs={12}
        >
          <TextField
            InputLabelProps={
              Object {
                "style": Object {
                  "color": "#212121",
                  "fontSize": 12,
                },
              }
            }
            disabled={true}
            fullWidth={true}
            label="First Name"
            name="name"
            onBlur={[Function]}
            onChange={[Function]}
            required={true}
            style={
              Object {
                "fontSize": "1px",
              }
            }
            value=""
            variant="standard"
          />
        </Grid>
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "marginTop": "3px",
              "padding": "0px 16px",
            }
          }
          xs={12}
        >
          <TextField
            InputLabelProps={
              Object {
                "style": Object {
                  "color": "#212121",
                  "fontSize": 12,
                },
              }
            }
            disabled={true}
            error={false}
            fullWidth={true}
            helperText=""
            label="Last Name"
            name="lastName"
            onBlur={[Function]}
            onChange={[Function]}
            required={true}
            style={
              Object {
                "fontSize": "1px",
              }
            }
            value=""
            variant="standard"
          />
        </Grid>
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "marginTop": "-5px",
              "padding": "10px 16px",
              "width": "100%",
            }
          }
          xs={12}
        >
          <InputLabel
            style={
              Object {
                "fontSize": 10.1,
                "paddingBottom": 4,
              }
            }
          >
            Phone *
          </InputLabel>
          <[object Object]
            containerStyle={
              Object {
                "borderColor": "#fff",
                "width": "100% !important",
              }
            }
            country="us"
            countryCodeEditable={false}
            error={false}
            helperText=""
            label="Phone"
            name="phone"
            onBlur={[Function]}
            onChange={[Function]}
            placeholder="Enter phone number"
            required={true}
            rules={
              Object {
                "required": true,
              }
            }
            value=""
          />
          <label
            style={
              Object {
                "color": "#e53935",
                "fontSize": "11px",
                "visibility": "hidden",
              }
            }
          >
            Enter valid phone number
          </label>
        </Grid>
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "marginTop": "-5px",
              "padding": "0px 16px",
            }
          }
          xs={12}
        >
          <TextField
            InputLabelProps={
              Object {
                "style": Object {
                  "color": "#212121",
                  "fontSize": 12,
                },
              }
            }
            disabled={true}
            error={false}
            fullWidth={true}
            helperText=""
            label="Email"
            name="email"
            onBlur={[Function]}
            onChange={[Function]}
            required={true}
            style={
              Object {
                "fontSize": "14px",
              }
            }
            type="email"
            variant="standard"
          />
        </Grid>
      </Grid>
      <Grid
        item={true}
        md={12}
        style={
          Object {
            "marginTop": "-5px",
            "padding": "8px 0px 0px",
          }
        }
        xs={12}
      >
        <FormControlLabel
          control={
            <Checkbox
              color="primary"
              disabled={false}
            />
          }
          label="Click to automatically attach a screenshot of this page."
          name="attachScreenShot"
          onChange={[Function]}
          value={false}
        />
      </Grid>
      <Grid
        item={true}
        md={12}
        style={
          Object {
            "marginTop": "-5px",
            "padding": "0px 0px 0px",
          }
        }
        xs={12}
      >
        <FormControlLabel
          control={
            <Checkbox
              color="primary"
            />
          }
          label="Technical or functional issue."
          name="technical"
          onChange={[Function]}
          value={false}
        />
      </Grid>
      <Grid
        item={true}
        md={12}
        style={
          Object {
            "marginTop": "-5px",
            "padding": "0px 0px 0px",
          }
        }
        xs={12}
      >
        <FormControlLabel
          control={
            <Checkbox
              color="primary"
            />
          }
          label="Website improvement idea."
          name="improvement"
          onChange={[Function]}
          value={false}
        />
      </Grid>
      <Grid
        item={true}
        md={12}
        style={
          Object {
            "marginTop": "-5px",
            "padding": "0px 0px 0px",
          }
        }
        xs={12}
      >
        <FormControlLabel
          control={
            <Checkbox
              color="primary"
            />
          }
          label="Something else."
          name="something"
          onChange={[Function]}
          value={false}
        />
      </Grid>
      <Grid
        item={true}
        md={12}
        style={
          Object {
            "marginTop": "-5px",
            "padding": "0px 0px 0px",
          }
        }
        xs={12}
      >
        <FormControlLabel
          control={
            <Checkbox
              color="primary"
            />
          }
          label="General comment."
          name="comment"
          onChange={[Function]}
          value={false}
        />
      </Grid>
      <Grid
        item={true}
        md={12}
        style={
          Object {
            "marginTop": "-5px",
            "padding": "0px 0px",
          }
        }
        xs={12}
      >
        <InputLabel
          style={
            Object {
              "fontSize": 10.1,
              "paddingBottom": 4,
            }
          }
        >
          Description *
        </InputLabel>
        <TextareaAutosize
          error={false}
          fullWidth={true}
          helperText=""
          maxLength={257}
          name="description"
          onChange={[Function]}
          onKeyUp={[Function]}
          placeholder="Maximum 256 letters"
          required={true}
          rowsMax={3}
          rowsMin={3}
          style={
            Object {
              "fontSize": "14px",
              "padding": "4px",
              "resize": "vertical",
              "width": "100%",
            }
          }
          value=""
          variant="standard"
        />
        <label
          style={
            Object {
              "color": "#e53935",
              "fontSize": "11px",
              "visibility": "hidden",
            }
          }
        >
          Enter valid description
        </label>
      </Grid>
    </div>
    <div>
      <Button
        className="close-btn"
        disabled={false}
        onClick={[Function]}
        style={
          Object {
            "marginLeft": 15,
          }
        }
        variant="contained"
      >
        CLOSE
      </Button>
    </div>
    <div
      style={
        Object {
          "marginTop": -35,
          "paddingLeft": 450,
          "width": "100%",
        }
      }
    >
      <Button
        className="send-btn"
        disabled={true}
        onClick={[Function]}
        style={
          Object {
            "backgroundColor": "#fff",
          }
        }
        variant="contained"
      >
        SEND
      </Button>
    </div>
  </div>
</Dialog>
`;
