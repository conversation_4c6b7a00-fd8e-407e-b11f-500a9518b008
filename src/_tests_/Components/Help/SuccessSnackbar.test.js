import React from 'react';
import SuccessSnackbar from '../../../components/FeedBackForm/SuccessSnackbar';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import MockTheme from '../../MockTheme';

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/CheckCircleOutlined', () => ({
  CheckCircleIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Snackbar: () => Component => props => <Component classes="" {...props} />,
  SnackbarContent: () => Component => props => <Component classes="" {...props} />,
  colors: () => Component => props => <Component classes="" {...props} />,
}));
const mockStore = configureMockStore();
const store = mockStore({});
describe('DashboardActions render ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });

  const title = 'CP Overview';
  it('render DashboardActions ', () => {
    const wrapper = shallow(
      // <MockTheme>
        <SuccessSnackbar
          open = {false}
          onClose = {jest.fn()}
        />
      // </MockTheme>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render AddPost ', () => {
    
    const toggleInstance = shallow(<SuccessSnackbar open = {false} onClose = {jest.fn()}/>);
    const elements=toggleInstance.find('Snackbar');
    expect(elements.length).toBe(1);
    const element=toggleInstance.find('SnackbarContent');
    expect(element.length).toBe(1);
    expect(toggleInstance.find('Snackbar').children().length).toBe(1);
    expect(toggleInstance.find('SnackbarContent').children().length).toBe(0);
  });
});
