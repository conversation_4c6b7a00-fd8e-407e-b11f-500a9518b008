import {
  myCustomSumAggregate,
  distinctROCount,
  myCustomSumAggregateForSummary
} from '../../components/DrillDownCalculations';

describe('DrillDownCalculations Component', () => {
  const chartId = 942;
  const result = {
    ElrCombined: '$135.00',
    ElrCustomer: '$135.00',
    ElrExtended: '$NaN',
    ElrFactory: '$NaN',
    ElrInternal: '$NaN',
    ElrMaintenance: '$NaN',
    ElrWarranty: '$NaN',
    JobCntCombined: '1',
    JobCntCustomer: '1',
    JobCntExtended: '0',
    JobCntFactory: '0',
    JobCntInteranl: '0',
    JobCntMaintain: '0',
    JobCntWarranty: '0',
    LbrSale: '',
    LcInternal: '$0',
    LcWarranty: '$0',
    LsCombined: '1',
    LsInternal: '$0',
    LsWarranty: '$0',
    MarkupC: '',
    MarkupM: '',
    MarkupR: '',
    MarkupS: '',
    PcExtended: '$0',
    PcFactory: '$0',
    PcInternal: '$0',
    PcMaintenanceP: '$0',
    PcWarranty: '$0',
    PrtCostRC: "",
    PrtSaleRC: "",
    PsExtended: '$0',
    PsFactory: '$0',
    PsInternal: '$0',
    PsMaintenanceP: '$0',
    PsWarranty: '$0',
    ROCountPercAddOn: '',
    RoCountAll: '',
    SHByCategory: '1',
    SHExtended: '',
    ShInternal: '0.00',
    ShWarranty: '0',
    addOnRevenueperc: '',
    availableHours: 0,
    avgHrsTech: NaN,
    avgSalesPerTech: '$-NaN',
    costCompetitive: '$0',
    costExtended: '$0',
    costFactory: '$0',
    costMaintenance: '$0',
    costMaintenances: '$0',
    costRepair: '$0',
    costShop: '$0',
    deltaShFh: 'NaN',
    deltaThFh: 'NaN',
    discountROPerc: '',
    distinctROCompS: '0',
    distinctROCountAddOn: '',
    distinctROCountNonAddOn: '',
    distinctROMS: '0',
    distinctRORS: '0',
    distinctROSS: '0',
    elr: '$135',
    elrAllCategories: '$-NaN',
    elrCompetitive: '$-NaN',
    elrCompetitiveAddOns: '',
    elrMaintenance: '$-NaN',
    elrMaintenanceAddOn: '',
    elrRepair: '$-NaN',
    elrRepairAddOn: '',
    elrRepairCompet: '$135',
    elrRepairCompetitive: '$-NaN',
    elrShopAddOn: '',
    flatrateHours: 'NaN',
    hoursAllCategories: 0,
    hoursCompetitive: 0,
    hoursMaintenance: 0,
    hoursRepair: '0.00',
    hoursRepairCompetitive: 0,
    jobCountAddOn: '',
    jobCountAddOnC: '',
    jobCountAddOnM: '',
    jobCountAddOnPerRo: '',
    jobCountAddOnR: '',
    jobCountAddOnS: '',
    jobCountAll: '',
    jobCountAllC: 'NaN%',
    jobCountAllM: 'NaN%',
    jobCountAllR: 'NaN%',
    jobCountAllS: 'NaN%',
    jobCountCO: '0',
    jobCountM: '0',
    jobCountNonAddOn: '',
    jobCountR: '0',
    jobCountS: '0',
    laborCostCombined: '$45',
    laborCostSum: '$45',
    laborGP: '$90',
    laborGPCombined: '$90',
    laborGPE: '$0',
    laborGPF: '$0',
    laborGPI: '$0',
    laborGPMP: '$0',
    laborGPPerc: '66.7%',
    laborGPW: '$0',
    laborHoursSum: '1',
    laborPercetSumCombined: '63.6%',
    laborSale: 135,
    laborSaleCombined: '$135',
    laborSaleSum: '$135',
    laborSaleSumCombined: '$211.84',
    lbrCostC: '$45',
    lbrDiscount: '$NaN',
    lbrHoursAll: '0',
    lbrHoursPerJob: NaN,
    lbrHoursPerRO: '-NaN',
    lbrHrsPerRoAll: NaN,
    lbrHrsPerRoC: NaN,
    lbrHrsPerRoCPrcnt: 'NaN%',
    lbrHrsPerRoM: NaN,
    lbrHrsPerRoMPrcnt: 'NaN%',
    lbrHrsPerRoR: NaN,
    lbrHrsPerRoRPrcnt: 'NaN%',
    lbrHrsRepairCompet: '1',
    lbrSaleAddOn: '',
    lbrSaleC: '$135',
    lbrSaleCAddOn: '',
    lbrSaleMAddOn: '',
    lbrSaleRAddOn: '',
    lbrSaleRepairCompet: '$135',
    lbrSaleSAddOn: '',
    lbrSaleperRoAll: '$NaN',
    lbrSaleperRoC: '$-NaN',
    lbrSaleperRoM: '$-NaN',
    lbrSaleperRoR: '$-NaN',
    lbrSaleperRoS: '$-NaN',
    lbrSoldhrsNonAddon: '',
    lbrTechHr: '0.02',
    lsGrossSumCombined: '$134.68',
    menuCount: "",
    menuPercentage: "",
    mpiCount: "",
    mpiPercentage: "",
    nonAddOnRevenueperc: '',
    partsCostSum: '$32.16',
    partsGP: '$44.68',
    partsGPPerc: '58.1%',
    partsMarkup: '2.3893',
    partsMarkupC: 'NaN',
    partsMarkupCmb: '2.3900',
    partsMarkupM: 'NaN',
    partsMarkupR: '2.3900',
    partsMarkupRC: "2.3893",
    partsSale: NaN,
    partsSaleSum: '$76.84',
    partsToLaborRatio: 0.57,
    pcSumComp: '$32.16',
    prtGpCombin: '$44.68',
    prtGpE: '$0',
    prtGpF: '$0',
    prtGpInt: '$0',
    prtGpMP: '$0',
    prtGpWar: '$0',
    prtSaleAddOn: '',
    prtSaleCAddOn: '',
    prtSaleMAddOn: '',
    prtSaleRAddOn: '',
    prtSaleSAddOn: '',
    prtcostComp: '$0',
    prtcostM: '$0',
    prtcostR: '$32.16',
    prtsDiscount: '$NaN',
    prtsHours: "0",
    prtsHoursPerRO: "-NaN",
    prtsHrsRoCount: "0",
    prtsMarkupCombined: '2.3893',
    prtsMarkupCustomer: '2.3893',
    prtsMarkupExtended: 'NaN',
    prtsMarkupFactory: 'NaN',
    prtsMarkupInteranl: 'NaN',
    prtsMarkupMaintain: 'NaN',
    prtsMarkupWarranty: 'NaN',
    prtsToLbrRatioC: '-NaN',
    prtsToLbrRatioM: '-NaN',
    prtsToLbrRatioR: '∞',
    prtsaleComp: '$0',
    prtsaleM: '$0',
    prtsaleR: '$76.84',
    psSumComp: '$76.84',
    roCountAll: '0',
    roCountAllCat: '0',
    roCountR: '0',
    roCountShop: '0',
    ronumberArrAll: '',
    ronumberArrMPVI: "",
    sHAll: '',
    sHByCategoryC: '100%',
    sHByCategoryE: '0%',
    sHByCategoryF: '0%',
    sHByCategoryI: '0%',
    sHByCategoryM: '0%',
    sHByCategoryW: '0%',
    sHMaintenance: '',
    saleAllCategories: '$0',
    saleCompetitive: '$0',
    saleExtended: '$0',
    saleFactory: '$0',
    saleMaintenance: '$0',
    saleMaintenance1: '$0',
    saleRepair: '$0',
    saleRepairCompetitive: '$0',
    saleShopSupplies: '$0',
    shExtended: '0',
    shFactory: '0',
    shMaintenance1: '0',
    shopSuppliesC: '$0.00',
    shopSuppliesCombined: '$0.00',
    shopSuppliesI: '$0.00',
    soldHoursTech: 0,
    techCount: '0',
    techHours: 'NaN',
    totalLbrSaleP: '',
    totalPrtSaleP: '',
    totalROCount: "",
    totalSaleTech: '$0',
    totallbrDiscount: "0.00",
    totalprtsDiscount: NaN
  };
  const results = {
    LHROCombined: '-NaN',
    LHROCustomer: '0',
    LHROExtended: '-NaN',
    LHROFactory: '-NaN',
    LHROInteranl: '-NaN',
    LHROMaintain: '-NaN',
    LHROWarranty: '-NaN',
    LSInternal: '0',
    LSWarranty: '0',
    LSaleROCombined: '$NaN',
    LSaleROCustomer: '$0',
    LSaleROExtended: '$NaN',
    LSaleROFactory: '$NaN',
    LSaleROInteranl: '$NaN',
    LSaleROMaintain: '$NaN',
    LSaleROWarranty: '$NaN',
    LsCombined: '0',
    PsExtended: '$0',
    PsFactory: '$0',
    PsInternal: '$0',
    PsWarranty: '$0',
    SaleExtended: '0',
    SaleFactory: '0',
    SaleMaintenance1: '0',
    ShInternal: '0',
    ShWarranty: '0',
    discountJob: '0',
    discountJobcount: 0,
    discountROPerc: 1,
    distinctCPPROComp: '0',
    distinctCPPROM: '0',
    distinctCPPROR: '1',
    distinctRO: '1',
    distinctROC: '0',
    distinctROCmb: '0',
    distinctROCmbPerRo: '0',
    distinctROComp: '0',
    distinctROCompS: '',
    distinctROI: '0',
    distinctROM: '0',
    distinctROMS: '',
    distinctROPartsOnly: "1",
    distinctROPercent: 1,
    distinctROR: '1',
    distinctRORS: '',
    distinctROW: '0',
    jobCount: 1,
    jobCountAddOn: '',
    jobCountNonAddOn: '',
    laborHoursSum: '0',
    laborSaleSum: '0',
    lbrCostC: '$0',
    lbrHrsPerRoCPrcnt: 'NaN%',
    lbrHrsPerRoMPrcnt: 'NaN%',
    lbrHrsPerRoRPrcnt: '100.00%',
    lbrSaleC: '0',
    lbrSaleperRo: '$135',
    lineDiscount: '0',
    lopDiscount: "0",
    partsSaleSum: '$0',
    prtRoByCmb: '$NaN',
    prtRoByComp: '$NaN',
    prtRoByM: '$NaN',
    prtRoByR: '$76.84',
    prtSaleperRo: '$76.84',
    prtcostAll: '$0',
    prtcostCmb: '$0',
    prtsRevenueRoCombined: '$NaN',
    prtsRevenueRoCustomer: '$0',
    prtsRevenueRoExtended: '$NaN',
    prtsRevenueRoFactory: '$NaN',
    prtsRevenueRoInteranl: '$NaN',
    prtsRevenueRoMaintain: '$NaN',
    prtsRevenueRoWarranty: '$NaN',
    prtsaleAll: '$0',
    prtsaleCmb: '$0',
    prtsaleComp: '$0',
    prtsaleM: '$0',
    prtsaleR: '$76.84',
    psSumComp: '$0',
    roCountR: '',
    roDiscount: '0',
    roExtended: '0',
    roFactory: '0',
    roMaintenance: '0',
    ronumberArr: 1,
    ronumberArrJobs: 1,
    shExtended: '0',
    shFactory: '0',
    shMaintenance1: '0'
  };
  const values = [
    {
      __typename: 'dms_drilldown_vw_drill_down_total_revenue_details',
      closeddate: '2020-05-12',
      elr: 135,
      filter_by_laborparts: 'labor and parts',
      filter_by_revenue: 'revenue_with_cost_and_hours',
      lbr_grossprofitpercentage: 66,
      lbrcost: 45,
      lbrgrossprofit: 90,
      lbrlinecode: 'A',
      lbropcode: '30',
      lbropcodedesc: 'GENERAL/LITE DUTY',
      lbrsale: 135,
      lbrsequenceno: 1,
      lbrsoldhours: 1,
      lbrtechhours: 0.02,
      lbrtechno: '25864',
      markup: 2.3893,
      month_year: '2020-05',
      opcategory: 'REPAIR',
      opsubcategory: 'GENERAL/LITE DUTY',
      paytype: 'CP',
      paytypegroup: 'C',
      prtextendedcost: 32.16,
      prtextendedsale: 76.84,
      prts_grossprofitpercentage: 35,
      prtsgrossprofit: 44.68,
      ronumber: '748500',
      serviceadvisor: 183323,
      vin: '1GC0KVCG8CZ351898'
    }
  ];
  const output = {
    elr: '$135.00',
    jobCount: 'NaN',
    lbrCost: '$45.00',
    lbrGP: '$90.00',
    lbrGPPerct: '66.7%',
    lbrHours: '1.00',
    lbrSale: '$135.00',
    partsMarkup: "NaN",
    prtsCost: '$NaN',
    prtsGP: '$NaN',
    prtsGPPerct: 'NaN%',
    prtsSale: '$NaN',
    roCount: 'NaN',
    totalSale: '$NaN'
  };

  test('render myCustomSumAggregate properly', () => {
    expect(myCustomSumAggregate(values, chartId)).toStrictEqual(result);
  });
  test('render distinctROCount properly', () => {
    expect(distinctROCount(values, chartId)).toStrictEqual(results);
  });
  test('render myCustomSumAggregateForSummary properly', () => {
    expect(myCustomSumAggregateForSummary(values, chartId)).toStrictEqual(
      output
    );
  });
});
