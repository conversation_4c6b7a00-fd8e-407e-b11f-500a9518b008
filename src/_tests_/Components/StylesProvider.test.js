import React from 'react';
import CustomStylesProvider from '../../components/StylesProvider';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";
const mockStore = configureMockStore();
const store = mockStore({});
describe("PostCard StylesProvider ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });
    it("render StylesProvider ", () => {
        const wrapper =  shallow(
            <CustomStylesProvider
              direction={"rtl"}
            />
        );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render AddPost ', () => {
    
      const toggleInstance = shallow(<CustomStylesProvider  direction={"rtl"}/>);
      const elements=toggleInstance.find('StylesProvider');
      expect(elements.length).toBe(1);
      const element=toggleInstance.find('div');
      expect(element.length).toBe(1);
    });
});   