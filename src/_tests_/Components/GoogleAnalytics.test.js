import React from 'react';
import GoogleAnalytics from '../../components/GoogleAnalytics';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
describe("GoogleAnalytics render ", () => {
    it("render GoogleAnalytics ", () => {
        const wrapper =  shallow(
                    <GoogleAnalytics
                     />
            );
        expect(to<PERSON><PERSON>(wrapper)).toMatchSnapshot();
    });
    it('render GoogleAnalytics ', () => {
        const toggleInstance = shallow(<GoogleAnalytics />);
        const elements=toggleInstance.find('HelmetWrapper');
        expect(elements.length).toBe(1);
        const ele=toggleInstance.find('script');
        expect(ele.length).toBe(1);
        expect(toggleInstance.find('HelmetWrapper').children().length).toBe(1);
        expect(toggleInstance.find('script').children().length).toBe(0);
    });
});   