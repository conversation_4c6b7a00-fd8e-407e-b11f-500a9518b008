import React from 'react';
import CookiesNotification from '../../components/CookiesNotification';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
// jest.mock('js-cookie', () => ({
//     Cookies: () => Component => props => <Component classes="" {...props} />
//   }));
jest.mock('@material-ui/core', () => ({
  Paper: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Link: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />
}));
const initialState = true;
React.useState = jest.fn().mockReturnValue([initialState , {}]);
describe('CookiesNotification rendering ', () => {
  it('render CookiesNotification ', () => {
    const wrapper = shallow(
      <CookiesNotification />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  const handleChange = jest.fn();
  it('render CookiesNotification ', () => {
    
    const toggleInstance = shallow(<CookiesNotification/>);
    const elements=toggleInstance.find('Paper');
    expect(elements.length).toBe(1);
    const element=toggleInstance.find('div');
    expect(element.length).toBe(3);
    const elem=toggleInstance.find('img');
    expect(elem.length).toBe(1);
    const eleme=toggleInstance.find('Typography');
    expect(eleme.length).toBe(1);
    const elemen=toggleInstance.find('Link');
    expect(elemen.length).toBe(1);
    const element1=toggleInstance.find('Button');
    expect(element1.length).toBe(1);
    expect(toggleInstance.find('Paper').children().length).toBe(3);
    expect(toggleInstance.find('div').children().length).toBe(3);
    expect(toggleInstance.find('img').children().length).toBe(0);
    expect(toggleInstance.find('Typography').children().length).toBe(4);
    expect(toggleInstance.find('Link').children().length).toBe(1);
    expect(toggleInstance.find('Button').children().length).toBe(1);
  });
  
});
