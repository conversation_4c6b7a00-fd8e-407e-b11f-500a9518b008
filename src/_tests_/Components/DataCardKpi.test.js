import React from 'react';
import DataCardKpi from '../../components/DataCardKpi';
import MockTheme from '../MockTheme';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import renderer from 'react-test-renderer';
jest.mock('@material-ui/core/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Grid', () => ({
    Grid: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Paper', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Typography', () => ({
    Typography: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ButtonBase', () => ({
    ButtonBase: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/LocationCity', () => ({
    LocationCityIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Build', () => ({
    BuildIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('DirectionToggle render ', () => {
    const props = {
        color: '#fff',
        height: '100px',
        marginTopIcon: "cc",
        value: "ld",
        marginToph6: 'rd'
    }
  it('render DirectionToggle ', () => {
    const wrapper = shallow(<DataCardKpi {...props} />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render DirectionToggle ', () => {
    const toggleInstance = shallow(<DataCardKpi {...props} />);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(2);
    const elem=toggleInstance.find('img');
    expect(elem.length).toBe(1);
    const eleme=toggleInstance.find('span');
    expect(eleme.length).toBe(2);
    expect(toggleInstance.find('div').children().length).toBe(3);
    expect(toggleInstance.find('img').children().length).toBe(0);
    expect(toggleInstance.find('span').children().length).toBe(1);
  });
});
