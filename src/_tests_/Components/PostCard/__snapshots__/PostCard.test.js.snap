// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PostCard render  render PostCard  1`] = `
<Card
  className=""
>
  <CardHeader
    avatar={
      <Avatar
        alt="Person"
        component={
          Object {
            "$$typeof": Symbol(react.forward_ref),
            "propTypes": Object {
              "innerRef": [Function],
              "onClick": [Function],
              "replace": [Function],
              "target": [Function],
              "to": [Function],
            },
            "render": [Function],
          }
        }
        src="post"
        to="/profile/1/timeline"
      />
    }
    disableTypography={true}
    subheader={
      <div>
        <UNDEFINED />
        <Typography
          variant="body2"
        >
          Invalid date
        </Typography>
      </div>
    }
    title={
      <Link
        color="textPrimary"
        component={
          Object {
            "$$typeof": Symbol(react.forward_ref),
            "propTypes": Object {
              "innerRef": [Function],
              "onClick": [Function],
              "replace": [Function],
              "target": [Function],
              "to": [Function],
            },
            "render": [Function],
          }
        }
        to="/profile/1/timeline"
        variant="h6"
      >
        Post Name
      </Link>
    }
  />
  <CardContent>
    <Typography
      variant="body1"
    >
      Post Data
    </Typography>
    <Reactions
      post={
        Object {
          "author": Object {
            "avatar": "post",
            "name": "Post Name",
          },
          "comments": Array [
            Object {
              "id": 1,
            },
          ],
          "created_at": "",
          "media": "",
          "message": "Post Data",
        }
      }
    />
    <Divider />
    <div>
      <CommentBubble
        comment={
          Object {
            "id": 1,
          }
        }
        key="1"
      />
    </div>
    <Divider />
    <CommentForm />
  </CardContent>
</Card>
`;
