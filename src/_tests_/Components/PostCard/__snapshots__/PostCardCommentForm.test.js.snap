// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommentForm  rendering  Renders correctly 1`] = `
<div
  category="aa"
  className=""
  keycloak={
    Object {
      "realmAccess": Object {
        "roles": Array [],
      },
    }
  }
  location={
    Object {
      "pathname": "/WarrantyReferenceLabor",
    }
  }
  months={
    Array [
      Object {
        "monthYear": "02/22",
      },
    ]
  }
  reporttype="rr"
  selectedDates={Array []}
  type="ss"
>
  <input
    type="file"
  />
</div>
`;
