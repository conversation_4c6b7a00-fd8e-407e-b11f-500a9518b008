import React from 'react';
import CommentBubble from '../../../components/PostCard/CommentBubble';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Avatar: () => Component => props => <Component classes="" {...props} />,
    Link: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />
}));
  const comment = {
      author:{
          avatar:'',
          name:'Bubble',
          created_at: '10/10/2021',
          message: 'Bubble data'
      }
    }
describe('CommentBubble  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <CommentBubble comment={comment}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
