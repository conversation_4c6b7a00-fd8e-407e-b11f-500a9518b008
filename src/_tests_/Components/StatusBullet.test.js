import React from 'react';
import StatusBullet from '../../components/StatusBullet';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  colors: () => Component => props => <Component classes="" {...props} />
}));
describe('StatusBullet ', () => {
  it('render StatusBullet ', () => {
    const component = renderer.create(<StatusBullet />);

    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
