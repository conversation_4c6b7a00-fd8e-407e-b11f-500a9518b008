import React from 'react';
import Label from '../../components/Label.js';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';
const mockStore = configureMockStore();
const store = mockStore({});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
const colors = { grey:[600] };
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Typography: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  colors: () => Component => props => <Component classes="" {...props} />
}));

describe('Label ', () => {
  it('render Label ', () => {
    const component = shallow(<Label colors={colors} />);

    expect(toJson(component)).toMatchSnapshot();
  });
});
