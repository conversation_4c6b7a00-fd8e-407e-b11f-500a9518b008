import React from 'react';
import CodeBlock from '../../components/CodeBlock';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/core', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
describe('Paytype index rendering ', () => {
 const input = [];
  it('render Paytype master ', () => {
    const wrapper = shallow(
      <CodeBlock  input={input}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  
});
