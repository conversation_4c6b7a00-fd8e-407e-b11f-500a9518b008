import { getDashboardGraphQuery } from '../../components/DashboardGraphQuery.js';
import { getDetailedGraphDateRange,getQueryFilter } from '../../components/ViewGraphDetailsAction';

jest.mock('../../components/ViewGraphDetailsAction', () => ({ getDetailedGraphDateRange: jest.fn(),getQueryFilter: jest.fn() }));
getDetailedGraphDateRange.mockImplementation(() => {
  return ["2019-12-01", "2020-12-31"];
});
getQueryFilter.mockImplementation(() => {
  return [{
    "member": "CPRevenue.store_id",
    "operator": "contains",
    "values":[
      "75627608",
      "75627643",
    ]
  }];
});
describe('getDashboardGraphQuery Component', () => {
  const chartId = 942;
  const filters = [942];
  const storeId = ['75627608',"75627643"];
  const result = {
    dimensions: ['CPRevenue.closeddate'],
    filters: [
      {
        member: 'CPRevenue.store_id',
        operator: 'contains',
        values: ['75627608',"75627643"]
      }
    ],
    measures: [
      'CPRevenue.labor_revenue',
      'CPRevenue.parts_revenue',
      'CPRevenue.combined'
    ],
    order: {
      'CPRevenue.closeddate': 'asc'
    },
    timeDimensions: [
      {
        dateRange: ['2019-12-01', '2020-12-31'],
        dimension: 'CPRevenue.closeddate'
      }
    ]
  };
  test('render getDashboardGraphQuery properly', () => {
    expect(getDashboardGraphQuery(chartId, filters, storeId)).toStrictEqual(result);
  });
});
