import React from 'react';
import CustomHeaderGroup from '../../components/CustomHeaderGroup';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
jest.mock('@material-ui/icons/DeleteOutline', () => ({
    DeleteIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core', () => ({
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />
}));
const props = { 
    frameworkComponentWrapper : {
      agGridReact : {
        props : {
            frameworkComponents :''
        },
        gridOptions : {
          rowData :''
        }
    },
}};

describe('CustomHeaderGroup rendering ', () => {
  it('render CustomHeaderGroup ', () => {
    const wrapper = shallow(
      <CustomHeaderGroup {...props}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render DirectionToggle ', () => {
    const toggleInstance = shallow(<CustomHeaderGroup {...props}/>);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(3);
    const elem=toggleInstance.find('Tooltip');
    expect(elem.length).toBe(2);
    const eleme=toggleInstance.find('Tooltip');
    expect(eleme.length).toBe(2);
    expect(toggleInstance.find('div').children().length).toBe(4);
    expect(toggleInstance.find('Tooltip').children().length).toBe(2);
  });
});