import React from 'react';
import FeedBackForm from '../../../components/FeedBackForm/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Popover: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  CardActions: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  colors: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("src/utils/bytesToSize", () => () => {
  return <mock-bytesToSize data-testid="bytesToSize"/>;
});
jest.mock("../../../components/FeedBackForm/FeedBack.js", () => () => {
  return <mock-FeedBackForm data-testid="FeedBackForm"/>;
});
jest.mock("../../../components/FeedBackForm/SuccessSnackbar.js", () => () => {
  return <mock-FeedBack data-testid="FeedBack"/>;
});
jest.mock('@material-ui/icons/HelpOutline', () => ({
  HelpOutlineIcon: () => Component => props => <Component classes="" {...props} />
}));

const mockStore = configureMockStore();
const store = mockStore({});
describe("FeedBackForm render ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });
   
      const title= "CP Overview";  
    it("render FeedBackForm ", () => {
        const wrapper =  shallow(
                    <FeedBackForm
                      page = {"other"}
                      handleSnackbarClose={ jest.fn()}
                      onClose={ jest.fn()}
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render FeedBackForm ', () => {
      const toggleInstance = shallow(<FeedBackForm />);
      const elements=toggleInstance.find('div');
      expect(elements.length).toBe(1);
      const ele=toggleInstance.find('Button');
      expect(ele.length).toBe(1);
      expect(toggleInstance.find('div').children().length).toBe(3);
      expect(toggleInstance.find('Button').children().length).toBe(2);
  });
});   