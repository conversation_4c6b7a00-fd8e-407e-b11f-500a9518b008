// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Matching snapshot 1`] = `
<Dialog
  maxWidth="sm"
  onClose={[MockFunction]}
  open={true}
>
  <div
    className="undefined"
  >
    <div>
      <Typography
        align="center"
        gutterBottom={true}
        variant="h5"
      >
        Feedback
      </Typography>
      <Typography
        align="center"
        variant="subtitle2"
      >
        Please provide your feedback.
      </Typography>
    </div>
    <div>
      <Grid
        container={true}
        spacing={4}
      >
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "padding": "25px 16px 5px",
            }
          }
          xs={12}
        >
          <TextField
            error={false}
            fullWidth={true}
            helperText=""
            label="Email"
            name="email"
            onChange={[Function]}
            required={true}
            style={
              Object {
                "fontSize": "14px",
              }
            }
            type="email"
            value=""
            variant="standard"
          />
        </Grid>
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "padding": "5px 16px",
            }
          }
          xs={12}
        >
          <TextField
            error={false}
            fullWidth={true}
            helperText=""
            label="Name"
            name="name"
            onChange={[Function]}
            required={true}
            style={
              Object {
                "fontSize": "14px",
              }
            }
            value=""
            variant="standard"
          />
        </Grid>
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "padding": "5px 16px",
            }
          }
          xs={12}
        >
          <TextField
            error={false}
            fullWidth={true}
            helperText=""
            label="Phone"
            name="phone"
            onChange={[Function]}
            required={true}
            style={
              Object {
                "fontSize": "14px",
              }
            }
            value=""
            variant="standard"
          />
        </Grid>
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "padding": "5px 16px",
            }
          }
          xs={12}
        />
        <Grid
          item={true}
          md={12}
          style={
            Object {
              "padding": "5px 16px",
            }
          }
          xs={12}
        >
          <TextField
            fullWidth={true}
            helperText={false}
            label="Description"
            name="description"
            onChange={[Function]}
            required={true}
            style={
              Object {
                "fontSize": "14px",
              }
            }
            value=""
            variant="standard"
          />
        </Grid>
      </Grid>
      <Grid
        item={true}
        md={12}
        style={
          Object {
            "padding": "16px 0px 0px",
          }
        }
        xs={12}
      >
        <FormControlLabel
          control={
            <Checkbox
              color="primary"
              defaultChecked={true}
            />
          }
          label="Click to automatically attach a screenshot of this page."
          name="attachScreenShot"
          onChange={[Function]}
          value={true}
        />
      </Grid>
      <div
        id="canvaseeee"
        style={
          Object {
            "height": "150px",
            "position": "relative",
          }
        }
      >
        <img
          src="undefined"
          style={
            Object {
              "border": " 1px solid #333",
              "borderRadius": "5px",
              "height": "125px",
              "left": "50%",
              "position": "absolute",
              "top": "50%",
              "transform": "translate(-50%,-50%)",
            }
          }
        />
      </div>
    </div>
    <div>
      <Button
        onClick={[MockFunction]}
        variant="contained"
      >
        Close
      </Button>
    </div>
    <div
      style={
        Object {
          "marginLeft": 378,
          "marginTop": -36,
          "width": "100%",
        }
      }
    >
      <Button
        onClick={[Function]}
        variant="contained"
      >
        Send Feedback
      </Button>
    </div>
  </div>
</Dialog>
`;
