// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FeedBackForm render  render FeedBackForm  1`] = `
<div>
  <Button
    color="secondary"
    onClick={[Function]}
    style={
      Object {
        "background": "#5eb562",
        "bottom": 52,
        "padding": "2px 6px",
        "position": "fixed",
        "right": -48,
        "transform": "rotate(-90deg)",
      }
    }
    variant="contained"
  >
    FeedBack 
    <[object Object]
      style={
        Object {
          "marginLeft": "10px",
        }
      }
    />
  </Button>
  <Component
    canvased={Object {}}
    onClose={[Function]}
    open={false}
    page="other"
  />
  <Component
    onClose={[Function]}
    open={false}
  />
</div>
`;
