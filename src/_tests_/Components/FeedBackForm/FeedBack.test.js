import React from 'react'
import FeedBackForm from '../../../components/FeedBackForm/FeedBack';
// import { createMuiTheme, MuiThemeProvider } from '@material-ui/core/styles';
import { ThemeProvider } from '@material-ui/styles';
import renderer from 'react-test-renderer'
import { cleanup, render } from '@testing-library/react';
import { Provider } from 'react-redux';
import toJson from 'enzyme-to-json';
import { shallow } from 'enzyme';
import configureMockStore from "redux-mock-store";
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardActions: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  Dialog: () => Component => props => <Component classes="" {...props} />,
  TextField: () => Component => props => <Component classes="" {...props} />,
  InputLabel: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
  Link: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  colors: () => Component => props => <Component classes="" {...props} />,
  FormControlLabel: () => Component => props => <Component classes="" {...props} />,
  Checkbox: () => Component => props => <Component classes="" {...props} />,
}));

const mockStore = configureMockStore();
const store = mockStore({});
// const theme = createMuiTheme({});
test('Matching snapshot', () => {
  const tree = shallow(
    <FeedBackForm
            open = {true}
            page = {"other"}
            className = {"undefined"}
      
            onClose = {jest.fn()}
            />
  );
  expect(toJson(tree)).toMatchSnapshot();
});
it('render FeedBackForm ', () => {
  const toggleInstance = shallow(
    <FeedBackForm
      open = {true}
      page = {"other"}
      className = {"undefined"}
      onClose = {jest.fn()}
    />
  );
  const elements=toggleInstance.find('Dialog');
  expect(elements.length).toBe(1);
  const ele=toggleInstance.find('div');
  expect(ele.length).toBe(6);
  const elements1=toggleInstance.find('Typography');
  expect(elements1.length).toBe(2);
  const ele1=toggleInstance.find('Grid');
  expect(ele1.length).toBe(7);
  const elements2=toggleInstance.find('TextField');
  expect(elements2.length).toBe(4);
  const ele2=toggleInstance.find('FormControlLabel');
  expect(ele2.length).toBe(1);
  const elements3=toggleInstance.find('img');
  expect(elements3.length).toBe(1);
  const ele3=toggleInstance.find('Button');
  expect(ele3.length).toBe(2);
  expect(toggleInstance.find('Dialog').children().length).toBe(1);
  expect(toggleInstance.find('div').children().length).toBe(12);
  expect(toggleInstance.find('Typography').children().length).toBe(2);
  expect(toggleInstance.find('Grid').children().length).toBe(10);
  expect(toggleInstance.find('TextField').children().length).toBe(0);
  expect(toggleInstance.find('FormControlLabel').children().length).toBe(0);
  expect(toggleInstance.find('img').children().length).toBe(0);
  expect(toggleInstance.find('Button').children().length).toBe(2);
});