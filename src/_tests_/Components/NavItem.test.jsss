import React from 'react';
import NavItem from '../components/NavItem';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

const mockStore = configureMockStore();
const store = mockStore({});
jest.mock('@material-ui/icons', () => ({
  TramOutlined: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => (
      <Component
        classes=""
        {...props}
      />
    ),
}));  
jest.mock('@material-ui/core', () => ({
    ListItem: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    Button: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    Collapse: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/ExpandMore', () => ({
    ExpandMoreIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/ExpandLess', () => ({
    ExpandLessIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock("src/utils/OTTTracing.js", () => () => {
  return <mock-traceSpan data-testid="traceSpan"/>;
});

jest.mock('@material-ui/icons', () => ({
  TramOutlined: () => Component => props => <Component classes="" {...props} />
}));
describe("DashboardActions render ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });
      const className = "undefined";  
      const depth = 2;
      const href ="/WarrantyRatesLabor";
      const label ="undefined";
      const open = false;
      const title = "Customer Pay";
    it("render DashboardActions ", () => {
        const wrapper =  shallow(
                    <NavItem
                        className={className}
                        depth={depth}
                        href={href}
                        label={label}
                        open={open}
                        title={title}
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
});   