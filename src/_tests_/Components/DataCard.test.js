import React from 'react';
import DataCard from '../../components/DataCard';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/core/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Grid', () => ({
    Grid: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Paper', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Typography', () => ({
    Typography: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/ButtonBase', () => ({
    ButtonBase: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/LocationCity', () => ({
    LocationCityIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Build', () => ({
    BuildIcon: () => Component => props => <Component classes="" {...props} />
}));

  const props = {
    title:'Data Card',
    value:' Data value',
    icon:'data.png'
  };
describe('DataCard  rendering ', () => {
  it('Renders correctly', () => {
    // const component = renderer.create(
    //   <DataCard {...props} 
    //   />
    // );
    // let tree = component.toJSON();
    // expect(tree).toMatchSnapshot();
    const wrapper = shallow(
        <DataCard {...props} />
      );
      expect(toJson(wrapper)).toMatchSnapshot();
  });
});
