import React from 'react';
import LoaderSkeleton from '../../components/LoaderSkeleton';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';

describe("LoaderSkeleton render ", () => {

    it("render LoaderSkeleton ", () => {
        const wrapper =  shallow(
                    <LoaderSkeleton
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
});   