import React from 'react';
import GenericMoreButton from '../../components/GenericMoreButton';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    ListItemIcon: () => Component => props => <Component classes="" {...props} />,
    ListItemText: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Menu: () => Component => props => <Component classes="" {...props} />,
    MenuItem: () => Component => props => <Component classes="" {...props} />
  }));

  jest.mock('@material-ui/icons/MoreVert', () => ({
    MoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/GetApp', () => ({
    GetAppIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/FileCopy', () => ({
    FileCopyIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Print', () => ({
    PrintIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/PictureAsPdf', () => ({
    PictureAsPdfIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ArchiveOutlined', () => ({
    AchiveIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock("src/utils/bytesToSize", () => () => {
    return <mock-bytesToSize data-testid="bytesToSize"/>;
  });

describe('GenericMoreButton rendering ', () => {
 
    it('render GenericMoreButton ', () => {
      const wrapper = shallow(
        <GenericMoreButton  />
      );
      expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render GenericMoreButton ', () => {
      const toggleInstance = shallow(<GenericMoreButton />);
      const elements=toggleInstance.find('Tooltip');
      expect(elements.length).toBe(1);
      const ele=toggleInstance.find('IconButton');
      expect(ele.length).toBe(1);
      const elem=toggleInstance.find('Menu');
      expect(elem.length).toBe(1);
      const eleme=toggleInstance.find('MenuItem');
      expect(eleme.length).toBe(5);
      const elemen=toggleInstance.find('ListItemIcon');
      expect(elemen.length).toBe(5);
      const element=toggleInstance.find('GetAppIcon');
      expect(element.length).toBe(0);
      const element1=toggleInstance.find('ListItemText');
      expect(element1.length).toBe(5);
      const element2=toggleInstance.find('FileCopyIcon');
      expect(element2.length).toBe(0);
      const element3=toggleInstance.find('PictureAsPdfIcon');
      expect(element3.length).toBe(0);
      const element4=toggleInstance.find('PrintIcon');
      expect(element4.length).toBe(0);
      const element5=toggleInstance.find('AchiveIcon');
      expect(element5.length).toBe(0);
      expect(toggleInstance.find('Tooltip').children().length).toBe(1);
      expect(toggleInstance.find('IconButton').children().length).toBe(1);
      expect(toggleInstance.find('Menu').children().length).toBe(5);
      expect(toggleInstance.find('MenuItem').children().length).toBe(10);
      expect(toggleInstance.find('ListItemIcon').children().length).toBe(5);
      expect(toggleInstance.find('GetAppIcon').children().length).toBe(0);
      expect(toggleInstance.find('ListItemText').children().length).toBe(0);
      expect(toggleInstance.find('FileCopyIcon').children().length).toBe(0);
      expect(toggleInstance.find('PictureAsPdfIcon').children().length).toBe(0);
      expect(toggleInstance.find('PrintIcon').children().length).toBe(0);
      expect(toggleInstance.find('AchiveIcon').children().length).toBe(0);
    });
  });