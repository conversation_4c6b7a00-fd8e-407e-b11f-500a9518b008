import React from 'react';
import TableEditBar from '../../components/TableEditBar';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Drawer: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  Hidden: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Check', () => ({
  CheckIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Close', () => ({
  CloseIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/DeleteOutline', () => ({
  DeleteIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('TableEditBar ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  const title = 'CP Overview';
  const avatars = ['10','200','9833']
  it('render TableEditBar ', () => {
    const wrapper = shallow(
      <TableEditBar selected={true}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render AddPost ', () => {
    const toggleInstance = shallow(<TableEditBar selected={true} />);
    const elements=toggleInstance.find('Drawer');
    expect(elements.length).toBe(1);
    const element=toggleInstance.find('div');
    expect(element.length).toBe(2);
    const elem=toggleInstance.find('Grid');
    expect(elem.length).toBe(3);
    const eleme=toggleInstance.find('Hidden');
    expect(eleme.length).toBe(1);
    const elemem=toggleInstance.find('Button');
    expect(elemem.length).toBe(3);
    expect(toggleInstance.find('Drawer').children().length).toBe(1);
    expect(toggleInstance.find('div').children().length).toBe(4);
    expect(toggleInstance.find('Grid').children().length).toBe(4);
    expect(toggleInstance.find('Hidden').children().length).toBe(1);
    expect(toggleInstance.find('Button').children().length).toBe(6);
  });
});
