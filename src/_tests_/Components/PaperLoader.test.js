import React from 'react';
import PaperLoader from '../../components/PaperLoader';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';

const mockStore = configureMockStore();
const store = mockStore({});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});

describe('PaperLoader ', () => {
  it('render PaperLoader ', () => {
    const component = renderer.create(<PaperLoader />);

    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
