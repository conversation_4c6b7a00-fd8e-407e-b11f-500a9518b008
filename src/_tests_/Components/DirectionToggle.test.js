import React from 'react';
import DirectionToggle from '../../components/DirectionToggle';
import MockTheme from '../MockTheme';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Fab: () => Component => props => <Component classes="" {...props} />
}));
describe('DirectionToggle render ', () => {
  it('render DirectionToggle ', () => {
    const wrapper = shallow(<DirectionToggle onToggle={jest.fn()} />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render DirectionToggle ', () => {
    
    const toggleInstance = shallow(<DirectionToggle onToggle={jest.fn()} />);
    const elements=toggleInstance.find('Fab');
    expect(elements.length).toBe(1);
    expect(toggleInstance.find('CardContent').children().length).toBe(0);
  });
});
