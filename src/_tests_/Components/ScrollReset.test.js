import React from 'react';
import ScrollReset from '../../components/ScrollReset';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

// jest.mock('react-redux', () => ({
//     useSelector: jest.fn(),
//     useDispatch: () => mockDispatch
//   }));
//   const mockStore = configureMockStore();
//   const store = mockStore({})
jest.mock("react-router", () => ({
    ...jest.requireActual("react-router"),
    useLocation: () => ({
      pathname: "localhost:3000/"
    })
  }));
describe("Redirect render ", () => {
    it("render Redirect ", () => {
        const wrapper =  shallow(
                    <ScrollReset
                     />
        );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
});   