import React from 'react';
import Paginate from '../../components/Paginate';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import ReactPaginate from 'react-paginate';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  serviceAdvisor: ['All'],
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  colors: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('react-paginate', () => ({
  ReactPaginate: () => Component => props => <Component classes="" {...props} />
}));
describe('Paytype index rendering ', () => {
  it('render Paginate ', () => {
    // const wrapper = shallow(
    //   <Paginate pageCount ={jest.fn()} pageRangeDisplayed={jest.fn()} onPageChange={jest.fn()} className=''/>
    // );
    // expect(toJson(wrapper)).toMatchSnapshot();

    const component = renderer.create(
        <Paginate pageCount ={jest.fn()} pageRangeDisplayed={jest.fn()} onPageChange={jest.fn()} className=''/>
      );
      let tree = component.toJSON();
      expect(tree).toMatchSnapshot();

  });
});
