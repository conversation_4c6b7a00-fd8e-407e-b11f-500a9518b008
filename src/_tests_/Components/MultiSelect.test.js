
import React from 'react';
import MultiSelect from '../../components/MultiSelect.js';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";
import * as redux from 'react-redux';
import MockTheme from '../MockTheme';
const spy = jest.spyOn(redux, 'useSelector')
spy.mockReturnValue({ serviceAdvisor: ["All"], storeSelected: '',storeName:'',storeId:'' })
const mockStore = configureMockStore();
const store = mockStore({});
jest.mock('jquery-sparkline', () => {
 
});
jest.mock("react-ga")

// jest.mock('react-router', () => ({
//   useHistory: () => ({
//     push: jest.fn(),
//   }),
// }));
jest.mock('@material-ui/core', () => ({
  Button: () => Component => props => <Component classes="" {...props} />,
  Checkbox: () => Component => props => <Component classes="" {...props} />,
  FormControlLabel: () => Component => props => <Component classes="" {...props} />,
  Menu: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardActions: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  IconButton: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ArrowDropDown', () => ({
  ArrowDropDownIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Done', () => ({
  DoneIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/EditOutlined', () => ({
  EditOutlinedIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
const options =  [
    "All","166169", "182881", "25837","25830", "183323", "25916","734","25914", "169775","188196", "176898","166170", "25904", "164409", "188394", "159053","191046","25174","25919","27027", "25803","177462","165854", "174120", "181808","159174","161229","27269","171350", "153113", "162137", "161599", "152722","25848", "26537","153112", "161598", "27144", "165855","27161", "25873","25840", "171677", "166946", "151887", "182823","159643", "156095", "27116","150335","26717","27129","27141", "183804", "25911", "186696" ,"179023", "27185","25862", "26764","178190","160927","165259","25906","181844","26824", "159172","177149","26702", "25799", "25716","25528", "166948", "999","27222","27222","26933", "26932", "26932", "177158","27295","155452","25642","25598", "27241"
];
const value =["All"];
const advisorNames =[ "All", "ANDERSON,JASON","AULWES,JAMIE A", "BECKWITH,CHRISTOPHER J","BIJOU,CHAD A", "BROWN,JACOB C","BUFFALOHEAD,ELI A"
, "CHEA,VANNAK","CHEA,VANNAK", "DAVIDSON,SEAN", "DORSEY,RYAN", "Ebert,Mitchell","FINNEMORE,TYLER", "FISHER,BRANDON J", "GANGESTAD,MICHAEL"
,"GEPP,RICHARD", "GOERISCH,PATRICK HERBERT","GRUBER,JEREMY", "GUTIERREZ,ARCADIO", "Hale,Ella","HANLEY,CHAD","HARDING,JEREMIE","HARTIG,MICHAEL"
,"HAWKINS-EASLEY,CINSERE","HAYNIE,MATTHEW","HELLERMAN,TAYA MONIQUE", "HILL,DAVID", "JACOBSON,KERRY", "JOHNSON,RONALD R", "JONES,MARCUS"
, "KELLY,PATRICK", "KELLY,PATRICK","KIELEY,MICHAEL J", "KLEIST,JASON", "larsen,joseph", "LEMON,CAROL ANN", "LOOS,JOHN AARON", "MADSON,ROBERT"
,"MARSH,PATRICK","MCALLISTER,ANTOINE", "MCDANIEL,BRADLEY GENE","MEIXNER,CHRISTIAN A", "MOLDAN,DANIEL J","NELSON,BRADLEY","NEU,LORELLE M"
, "NOLL,HAYLEY","OLSON,RYAN JAMES", "OLSON,STEVEN", "O'MASTA,JOSEPH M","PARENTEAU,ANTHONY", "PELISKA,JASON", "PELOQUIN,PETER", "PLATT,DOUGLAS I"
,"POBUDA,SCOTT","PRATCHER SR,ELVIN","QUINN,PETER F", "REED,JACE","REGEP,JUSTIN","REID,DAVID JOEL", "REYES,ERIC A", "REYNOLDS,BRANDON"
, "REYNOLDS,ROBERT","ROBINSON,CAITLIN A","ROZYCKI,CALEB J", "SANTIAGO ALVAREZ,JAYC A","SCHAFFHAUSEN,ANDREW", "SCHREIER,BRIAN","SCHUELEIN,ROLF"
, "SIERCKS,CHRISTOPHER", "SINGH,NARINE C", "SOWERS,DALTON", "steffens,jeremiah", "STINE,MARLIN","STRASSER,TROY", "TECH,SRV HOUSE", "THOMPSON,CAMERON M"
, "THOMPSON,CAMERON M", "THOMPSON,ERIK","THOMPSON,MICHAEL","THOMPSON,MICHAEL", "TIEDENS,ADAM","TILLER,ERIK","TREDWELL,CHRISTOPHER E"
, "WILSON,CHAD","WILSON,TODD", null
];
const history = {
  location:{
    pathname:"Discounts",
      state:{
          ronumber: '102211'
      }
  }
};

jest.mock('react-router', () => ({
  ...jest.requireActual('react-router'),
  useHistory: () => ({
    location: {
      pathname: '/help',
   },
 }),
}));
const keycloak = { 
  realmAccess:{
    roles: []
  }
};
describe("MultiSelect render ", () => {
    it("render MultiSelect ", () => {
        const wrapper =  shallow(
          <MultiSelect
            label={"Service Advisor"}
            options={options}
            value={value}
            advisorNames={advisorNames}
            onChange ={jest.fn()}
            selected= {value}
            // history={history}
            keycloak={keycloak}
          />
        );
      expect(toJson(wrapper)).toMatchSnapshot();
    });
  });   