import React from 'react';
import GraphDetailsAction from '../../components/GraphDetailsAction';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
jest.mock('react-redux', () => ({
  useSelector: jest.fn().mockImplementation(selector => selector()),
}));
jest.mock('react-redux', () => ({
  useSelector: jest.fn().mockImplementation(selector => selector()),
}));
jest.mock("src/components/charts/CPELRVsLaborSoldHours", () => () => {
  return <mock-CPELRVsLaborSoldHours data-testid="CPELRVsLaborSoldHours"/>;
});
jest.mock("src/components/charts/CPPartsMarkupVsPartsCost", () => () => {
  return <mock-CPPartsMarkupVsPartsCost data-testid="CPPartsMarkupVsPartsCost"/>;
});
jest.mock("src/components/charts/DashboardBarRenderer", () => () => {
  return <mock-DashboardBarRenderer data-testid="DashboardBarRenderer"/>;
});
jest.mock("src/components/charts/DeltaSoldHoursAndFlatRateHours", () => () => {
  return <mock-DeltaSoldHoursAndFlatRateHours data-testid="DeltaSoldHoursAndFlatRateHours"/>;
});
jest.mock("src/components/charts/DeltaTechFlatHours", () => () => {
  return <mock-DeltaTechFlatHours data-testid="DeltaTechFlatHours"/>;
});
jest.mock("src/components/charts/GraphDetails", () => () => {
  return <mock-GraphDetails data-testid="GraphDetails"/>;
});
jest.mock("src/components/charts/GraphDetailsView", () => () => {
  return <mock-GraphDetailsView data-testid="GraphDetailsView"/>;
});
jest.mock("src/components/ViewGraphDetailsAction", () => () => {
  return <mock-GetyAxisRange data-testid="GetyAxisRange"/>;
});
jest.mock("src/views/AnalyzeData/WorkMixCharts/BarChartRenderer", () => () => {
  return <mock-BarChartRenderer data-testid="BarChartRenderer"/>;
});
jest.mock("../../views/AddOns/ServiceAdvisor/ColumnRenderer", () => () => {
  return <mock-ApexCharts data-testid="ApexCharts"/>;
});
jest.mock("../../views/AnalyzeData/ComparisonCharts/ColumnRenderer", () => () => {
  return <mock-HighCharts data-testid="HighCharts"/>;
});
jest.mock("../../views/Discounts/DiscountColumnRenderer", () => () => {
  return <mock-ApexChartsDiscount data-testid="ApexChartsDiscount"/>;
});
jest.mock("../../components/charts/DashboardLineRenderer", () => () => {
  return <mock-DashboardLineRenderer data-testid="DashboardLineRenderer"/>;
});
jest.mock("src/utils/apolloRootClientPostgres", () => () => {
  return <mock-makeApolloClient data-testid="makeApolloClient"/>;
});
jest.mock("src/views/AnalyzeData/ServiceAdvisorEfficiency/BarChartRenderer", () => () => {
  return <mock-AdvisorPerformanceRenderer data-testid="AdvisorPerformanceRenderer"/>;
});
jest.mock("src/views/AnalyzeData/ServiceAdvisorEfficiency/ColumnRenderer", () => () => {
  return <mock-AdvisorComparisonCharts data-testid="AdvisorComparisonCharts"/>;
});
jest.mock("src/views/AnalyzeData/ServiceAdvisorEfficiency/OpCategoryChartRenderer", () => () => {
  return <mock-AdvisorOpcategoryCharts data-testid="AdvisorOpcategoryCharts"/>;
});
jest.mock('@material-ui/core', () => ({
  FormControl: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
  InputLabel: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  Select: () => Component => props => <Component classes="" {...props} />,
}));
// jest.mock('jquery-sparkline', () => ({
//   makeStyles: () => Component => props => <Component classes="" {...props} />
// }));
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
  }));
jest.mock('jquery-sparkline', () => {});
jest.mock('react-ga');
// const spy = jest.spyOn(redux, 'useSelector');
// spy.mockReturnValue({
//   isLoading: true,
//   storeSelected: '',
//   storeName: '',
//   storeId: '',
//   user:{first_name:''}
// });
describe('GraphDetailsAction render ', () => {
  it('render GraphDetailsAction ', () => {
    const wrapper = shallow(
      <GraphDetailsAction
        className={'layout'}
        cols={12}
        datatype={undefined}
        length={4}
        month1={undefined}
        month2={undefined}
        parentId={942}
      />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
