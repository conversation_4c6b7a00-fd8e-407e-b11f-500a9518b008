// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`GenericMoreButton rendering  render GenericMoreButton  1`] = `
<Fragment>
  <Tooltip
    title="More options"
  >
    <IconButton
      onClick={[Function]}
      size="small"
    >
      <[object Object] />
    </IconButton>
  </Tooltip>
  <Menu
    anchorEl={null}
    anchorOrigin={
      Object {
        "horizontal": "left",
        "vertical": "top",
      }
    }
    elevation={1}
    onClose={[Function]}
    open={false}
    transformOrigin={
      Object {
        "horizontal": "left",
        "vertical": "top",
      }
    }
  >
    <MenuItem>
      <ListItemIcon>
        <[object Object] />
      </ListItemIcon>
      <ListItemText
        primary="Import"
      />
    </MenuItem>
    <MenuItem>
      <ListItemIcon>
        <[object Object] />
      </ListItemIcon>
      <ListItemText
        primary="Copy to clipboard"
      />
    </MenuItem>
    <MenuItem>
      <ListItemIcon>
        <[object Object] />
      </ListItemIcon>
      <ListItemText
        primary="Export as PDF"
      />
    </MenuItem>
    <MenuItem>
      <ListItemIcon>
        <[object Object] />
      </ListItemIcon>
      <ListItemText
        primary="Print"
      />
    </MenuItem>
    <MenuItem>
      <ListItemIcon>
        <[object Object] />
      </ListItemIcon>
      <ListItemText
        primary="Achive"
      />
    </MenuItem>
  </Menu>
</Fragment>
`;
