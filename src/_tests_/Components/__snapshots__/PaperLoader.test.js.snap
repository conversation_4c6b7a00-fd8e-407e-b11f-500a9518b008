// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PaperLoader  render PaperLoader  1`] = `
<div
  className="MuiPaper-root MuiPaper-elevation1"
  style={
    Object {
      "margin": 8,
    }
  }
>
  <div
    className="MuiGrid-root MuiGrid-justify-content-xs-center"
    style={
      Object {
        "alignItems": "center",
        "display": "flex",
        "height": 250,
        "justifyContent": "center",
      }
    }
  >
    <div
      className="MuiCircularProgress-root MuiCircularProgress-colorPrimary MuiCircularProgress-indeterminate"
      role="progressbar"
      style={
        Object {
          "height": 60,
          "width": 60,
        }
      }
    >
      <svg
        className="MuiCircularProgress-svg"
        viewBox="22 22 44 44"
      >
        <circle
          className="MuiCircularProgress-circle MuiCircularProgress-circleIndeterminate"
          cx={44}
          cy={44}
          fill="none"
          r={20.2}
          strokeWidth={3.6}
          style={Object {}}
        />
      </svg>
    </div>
  </div>
</div>
`;
