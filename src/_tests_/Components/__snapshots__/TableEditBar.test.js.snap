// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`TableEditBar  render TableEditBar  1`] = `
<Drawer
  PaperProps={
    Object {
      "elevation": 1,
    }
  }
  anchor="bottom"
  open={false}
  variant="persistent"
>
  <div
    className=""
  >
    <Grid
      alignItems="center"
      container={true}
      spacing={2}
    >
      <Hidden
        smDown={true}
      >
        <Grid
          item={true}
          md={3}
        >
          <Typography
            color="textSecondary"
            variant="subtitle1"
          >
             selected
          </Typography>
        </Grid>
      </Hidden>
      <Grid
        item={true}
        md={6}
        xs={12}
      >
        <div>
          <Button>
            <[object Object] />
            Mark Paid
          </Button>
          <Button>
            <[object Object] />
            Mark Unpaid
          </Button>
          <Button>
            <[object Object] />
            Delete
          </Button>
        </div>
      </Grid>
    </Grid>
  </div>
</Drawer>
`;
