// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Paytype index rendering  render Paytype master  1`] = `
<WithStyles(ForwardRef(Grid))
  className="reset-dashboard"
  container={true}
>
  <WithStyles(ForwardRef(Grid))
    item={true}
    style={
      Object {
        "alignItems": "center",
        "display": "flex",
      }
    }
    xs={6}
  >
    <Button
      className="bck-btn"
      onClick={[Function]}
      variant="contained"
    >
      <WithStyles(ForwardRef(Typography))
        align="left"
        variant="body1"
      >
        Back
      </WithStyles(ForwardRef(Typography))>
    </Button>
    <WithStyles(ForwardRef(Typography))
      className="main-title"
      color="primary"
      variant="h4"
    >
      CP Labor GP %
    </WithStyles(ForwardRef(Typography))>
  </WithStyles(ForwardRef(Grid))>
  <WithStyles(ForwardRef(Grid))
    item={true}
    xs={4}
  >
    <Button
      className="reset-btn"
      id="reset-layout"
      onClick={[Function]}
      variant="contained"
    >
      <Memo(ForwardRef(RestoreIcon)) />
      <WithStyles(ForwardRef(Typography))
        align="left"
        variant="body1"
      >
        Reset Layout
      </WithStyles(ForwardRef(Typography))>
    </Button>
    <Component
      title="Export To Excel"
    >
      <WithStyles(ForwardRef(Link))
        id="export-to-excel"
        onClick={[Function]}
      >
        <Memo(ForwardRef(GetAppIcon)) />
      </WithStyles(ForwardRef(Link))>
    </Component>
  </WithStyles(ForwardRef(Grid))>
</WithStyles(ForwardRef(Grid))>
`;
