// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DataCard  rendering  Renders correctly 1`] = `
<[object Object]>
  <[object Object]
    container={true}
    id="Data Cardgrid"
    xs={12}
  >
    <[object Object]
      item={true}
      xs={4}
    >
      <[object Object]>
        <img
          alt="Fixed Ops"
          src="/images/img/data.png.png"
        />
      </[object Object]>
    </[object Object]>
    <[object Object]
      item={true}
      sm={true}
      xs={8}
    >
      <[object Object]
        container={true}
        direction="column"
        item={true}
        spacing={2}
        xs={true}
      >
        <[object Object]
          item={true}
          xs={true}
        >
          <[object Object]
            color="primary"
            gutterBottom={true}
            variant="subtitle1"
          >
            Data Card
          </[object Object]>
          <[object Object]
            color="primary"
            variant="h5"
          >
             Data value
          </[object Object]>
        </[object Object]>
      </[object Object]>
    </[object Object]>
  </[object Object]>
</[object Object]>
`;
