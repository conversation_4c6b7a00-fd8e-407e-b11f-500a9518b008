// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Addpost rendering  render AddPost  1`] = `
<Card
  className=""
>
  <CardContent>
    <Paper
      elevation={1}
    >
      <Input
        disableUnderline={true}
        onChange={[Function]}
        placeholder="What's on your mind, "
      />
    </Paper>
    <Tooltip
      title="Send"
    >
      <IconButton
        color="default"
      >
        <[object Object] />
      </IconButton>
    </Tooltip>
    <Divider />
    <Tooltip
      title="Attach image"
    >
      <IconButton
        edge="end"
        onClick={[Function]}
      >
        <[object Object] />
      </IconButton>
    </Tooltip>
    <Tooltip
      title="Attach file"
    >
      <IconButton
        edge="end"
        onClick={[Function]}
      >
        <[object Object] />
      </IconButton>
    </Tooltip>
    <input
      type="file"
    />
  </CardContent>
</Card>
`;
