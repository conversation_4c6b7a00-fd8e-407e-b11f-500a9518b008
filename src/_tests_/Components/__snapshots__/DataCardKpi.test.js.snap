// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DirectionToggle render  render DirectionToggle  1`] = `
<[object Object]
  style={
    Object {
      "backgroundColor": "#fff",
      "border": "0px",
      "height": "100px",
      "position": "relative",
    }
  }
>
  <[object Object]
    container={true}
    style={
      Object {
        "marginTop": "cc",
      }
    }
  >
    <div>
      <img
        alt="Fixed Ops"
        src="/images/kpis/undefined.png"
      />
    </div>
    <div
      className="flatRateHrsBlock"
      item={true}
      style={
        Object {
          "marginTop": "rd",
        }
      }
    >
      <span
        className="flatRateHrsBlockValue"
      >
        ld
      </span>
      <span
        className="flatRateHrsBlockTitle"
      />
    </div>
  </[object Object]>
</[object Object]>
`;
