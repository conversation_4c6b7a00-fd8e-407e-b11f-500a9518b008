// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Paytype index rendering  render Paytype master  1`] = `
<Dialog
  maxWidth="lg"
>
  <div
    className=""
    keycloak={
      Object {
        "tokenParsed": Object {
          "family_name": "",
          "given_name": "",
        },
      }
    }
  >
    <div>
      <Typography
        align="center"
        gutterBottom={true}
        variant="h3"
      >
        Start with Freelancer today. Boost up your services!
      </Typography>
      <Typography
        align="center"
        variant="subtitle2"
      >
        Welcome to the first platform created for freelancers and agencies for showcasing and finding the best clients in the market. 30% of our income goes into Whale Charity
      </Typography>
    </div>
    <div>
      <Grid
        container={true}
        spacing={4}
      >
        <Grid
          item={true}
          md={4}
          xs={12}
        >
          <Component
            elevation={1}
          >
            <img
              alt="Product"
              src="/images/products/product_freelancer.svg"
            />
            <Typography
              component="h3"
              gutterBottom={true}
              variant="overline"
            >
              Freelancer
            </Typography>
            <div>
              <Typography
                component="span"
                display="inline"
                variant="h3"
              >
                $5
              </Typography>
              <Typography
                component="span"
                display="inline"
                variant="subtitle2"
              >
                /month
              </Typography>
            </div>
            <Typography
              variant="overline"
            >
              Max 1 user
            </Typography>
            <Component />
            <Typography
              variant="subtitle2"
            >
              <b>
                20
              </b>
               
              proposals/month
               
              <br />
              <b>
                10
              </b>
               
              templates
               
              <br />
              Analytics
               
              <b>
                dashboard
              </b>
               
              <br />
              <b>
                Email
              </b>
               
              alerts
            </Typography>
          </Component>
        </Grid>
        <Grid
          item={true}
          md={4}
          xs={12}
        >
          <Component
            className=""
            elevation={1}
          >
            <img
              alt="Product"
              src="/images/products/product_agency--outlined.svg"
            />
            <Typography
              component="h3"
              gutterBottom={true}
              variant="overline"
            >
              Agency
            </Typography>
            <div>
              <Typography
                component="span"
                display="inline"
                variant="h3"
              >
                $29
              </Typography>
              <Typography
                component="span"
                display="inline"
                variant="subtitle2"
              >
                /month
              </Typography>
            </div>
            <Typography
              variant="overline"
            >
              Max 3 user
            </Typography>
            <Component />
            <Typography
              variant="subtitle2"
            >
              <b>
                20
              </b>
               
              proposals/month
               
              <br />
              <b>
                10
              </b>
               
              templates
               
              <br />
              Analytics
               
              <b>
                dashboard
              </b>
               
              <br />
              <b>
                Email
              </b>
               
              alerts
            </Typography>
          </Component>
        </Grid>
        <Grid
          item={true}
          md={4}
          xs={12}
        >
          <Component
            elevation={1}
          >
            <img
              alt="Product"
              src="/images/products/product_enterprise.svg"
            />
            <Typography
              component="h3"
              gutterBottom={true}
              variant="overline"
            >
              Enterprise
            </Typography>
            <div>
              <Typography
                component="span"
                display="inline"
                variant="h3"
              >
                $259
              </Typography>
              <Typography
                component="span"
                display="inline"
                variant="subtitle2"
              >
                /month
              </Typography>
            </div>
            <Typography
              variant="overline"
            >
              Unlimited
            </Typography>
            <Component />
            <Typography
              variant="subtitle2"
            >
              All from above
               
              <br />
              <b>
                Unlimited
              </b>
               
              24/7 support
               
              <br />
              Personalised
               
              <b>
                Page
              </b>
               
              <br />
              <b>
                Advertise
              </b>
               
              your profile
            </Typography>
          </Component>
        </Grid>
      </Grid>
      <Typography
        align="center"
        variant="subtitle2"
      >
        Have a larger team?
         
        <Component
          color="secondary"
          component={
            Object {
              "$$typeof": Symbol(react.forward_ref),
              "propTypes": Object {
                "innerRef": [Function],
                "onClick": [Function],
                "replace": [Function],
                "target": [Function],
                "to": [Function],
              },
              "render": [Function],
            }
          }
          to="#"
        >
          Contact us
        </Component>
         
        for information about more enterprise options.
      </Typography>
    </div>
    <div>
      <Button
        variant="contained"
      >
        Start with freelancer
      </Button>
    </div>
  </div>
</Dialog>
`;
