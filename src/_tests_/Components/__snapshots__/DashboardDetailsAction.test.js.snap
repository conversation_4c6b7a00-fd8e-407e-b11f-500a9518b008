// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DashboardDetailsActions render  render DashboardDetailsActions  1`] = `
<Fragment>
  <div>
    <Grid
      container={true}
      spacing={3}
      style={
        Object {
          "paddingLeft": "16px",
          "paddingRight": "16px",
        }
      }
    >
      <Grid
        item={true}
        md={6}
        style={
          Object {
            "textAlign": "left",
          }
        }
        xs={12}
      >
        <Tooltip
          onClick={[Function]}
          style={
            Object {
              "marginLeft": 16,
              "marginRight": 15,
            }
          }
          title="Go Back"
        >
          <IconButton
            size="small"
          >
            <[object Object] />
          </IconButton>
        </Tooltip>
        <Typography
          style={
            Object {
              "display": "inline-block",
            }
          }
          variant="h5"
        />
      </Grid>
      <Grid
        item={true}
        md={6}
        style={
          Object {
            "textAlign": "right",
          }
        }
        xs={12}
      >
        <Tooltip
          onClick={[Function]}
          style={
            Object {
              "marginRight": 8,
              "marginTop": -44,
            }
          }
          title="Reset Layout"
        >
          <IconButton
            size="small"
          >
            <[object Object] />
          </IconButton>
        </Tooltip>
      </Grid>
    </Grid>
    <Divider />
  </div>
</Fragment>
`;

exports[`DashboardDetailsActions render  render DashboardDetailsActions with Title 1`] = `
<Fragment>
  <div>
    <Grid
      container={true}
      spacing={3}
      style={
        Object {
          "paddingLeft": "16px",
          "paddingRight": "16px",
        }
      }
    >
      <Grid
        item={true}
        md={6}
        style={
          Object {
            "textAlign": "left",
          }
        }
        xs={12}
      >
        <Tooltip
          onClick={[Function]}
          style={
            Object {
              "marginLeft": 16,
              "marginRight": 15,
            }
          }
          title="Go Back"
        >
          <IconButton
            size="small"
          >
            <[object Object] />
          </IconButton>
        </Tooltip>
        <Typography
          style={
            Object {
              "display": "inline-block",
            }
          }
          variant="h5"
        >
          CP Revenue
        </Typography>
      </Grid>
      <Grid
        item={true}
        md={6}
        style={
          Object {
            "textAlign": "right",
          }
        }
        xs={12}
      >
        <Tooltip
          onClick={[Function]}
          style={
            Object {
              "marginRight": 8,
              "marginTop": -44,
            }
          }
          title="Reset Layout"
        >
          <IconButton
            size="small"
          >
            <[object Object] />
          </IconButton>
        </Tooltip>
      </Grid>
    </Grid>
    <Divider />
  </div>
</Fragment>
`;
