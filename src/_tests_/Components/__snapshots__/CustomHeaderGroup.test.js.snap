// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CustomHeaderGroup rendering  render CustomHeaderGroup  1`] = `
<div>
  <div
    className="customHeaderLabel"
  />
  <div
    style={
      Object {
        "display": "inline-block",
      }
    }
  >
    <Tooltip
      onClick={[Function]}
      style={
        Object {
          "marginLeft": -7,
          "marginTop": -5,
        }
      }
      title="Exclude"
    >
      <[object Object]
        style={
          Object {
            "marginLeft": -4,
            "width": 20,
          }
        }
      />
    </Tooltip>
    <Tooltip
      onClick={[Function]}
      style={
        Object {
          "marginLeft": -7,
          "marginTop": -5,
        }
      }
      title="Reset"
    >
      <[object Object]
        style={
          Object {
            "width": 20,
          }
        }
      />
    </Tooltip>
  </div>
</div>
`;
