import React from 'react';
import OpportunityGrid from '../../components/OpportunityGrid';
// import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import configureMockStore from 'redux-mock-store';
import enzyme, {shallow} from 'enzyme';
import renderer from 'react-test-renderer';
const mockStore = configureMockStore();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
Storage.prototype.getItem = jest.fn(() => 'selectedStoreId');
jest.mock('@material-ui/icons/Done', () => ({
  DoneIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Edit', () => ({
  EditIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/FilterList', () => ({
  FilterListIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Restore', () => ({
  RestoreIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Compare', () => ({
  Compare: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Divider: () => Component => props => <Component classes="" {...props} />,
  Grid: () => Component => props => <Component classes="" {...props} />,
  IconButton: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => (<Component classes="" {...props} />),
  Select: () => Component => props => <Component classes="" {...props} />,
  TextField: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Box: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/core/Checkbox', () => ({
    Checkbox: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Switch', () => ({
    Switch: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/FormGroup', () => ({
    FormGroup: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/FormControlLabel', () => ({
    FormControlLabel: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/colors', () => ({
    red: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/InputBase', () => ({
    InputBase: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Chip', () => ({
    Chip: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/styles', () => ({
    styled: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Paper', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Save', () => ({
    SaveIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("../../components/DataCard.js", () => () => {
  return <mock-DataCard data-testid="DataCard"/>;
});
// jest.mock("../../components/LoaderSkeleton.js", () => () => {
//   return <mock-LoaderSkeleton data-testid="LoaderSkeleton"/>;
// });
// jest.mock("../../utils/Utils.js", () => () => {
//   return <mock-Utils data-testid="Utils"/>;
// });
// jest.mock("../../graphql/queries.js", () => () => {
//   return <mock-queries data-testid="queries"/>;
// });
// jest.mock("../../utils/hasuraServices.js", () => () => {
//   return <mock-hasuraServices data-testid="hasuraServices"/>;
// });
// jest.mock("../../utils/apolloRootClientPostgres.js", () => () => {
//   return <mock-apolloRootClientPostgres data-testid="apolloRootClientPostgres"/>;
// });
const props = {
  className:"",
  filterCharts:"",
  resetDashboard:jest.fn(),
  saveClick:jest.fn(),
  zoomed:'',
  setTitle:'',
  checkSuccess:'',
  showCurrentMonth:'',
  keycloak:''
}
describe('Addpost rendering ', () => {
//   it('render AddPost ', () => {
//     const wrapper = shallow(
//       <OpportunityGrid  {...props}/>
//     );
//     expect(toJson(wrapper)).toMatchSnapshot();
//   });
it('Renders correctly', () => {
    const component = renderer.create(
      <OpportunityGrid {...props} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
  const handleChange = jest.fn();
//   it('render AddPost ', () => {
//     const toggleInstance = shallow(<OpportunityGrid onChange={handleChange} />);
//     const elements=toggleInstance.find('Tooltip');
//     expect(elements.length).toBe(3);
//     const elem=toggleInstance.find('Card');
//     expect(elem.length).toBe(1);
//     const eleme=toggleInstance.find('Input');
//     expect(eleme.length).toBe(1);
//     const elemen=toggleInstance.find('CardContent');
//     expect(elemen.length).toBe(1);
//     const element1=toggleInstance.find('Paper');
//     expect(element1.length).toBe(1);
//     const element2=toggleInstance.find('Divider');
//     expect(element2.length).toBe(1);
//     expect(toggleInstance.find('CardContent').children().length).toBe(6);
//     expect(toggleInstance.find('Card').children().length).toBe(1);
//     expect(toggleInstance.find('input').children().length).toBe(0);
//     expect(toggleInstance.find('Paper').children().length).toBe(1);
//     expect(toggleInstance.find('Input').children().length).toBe(0);
//     // expect(toggleInstance.find('IconButton').children().length).toBe(3);
//     expect(toggleInstance.find('SendIcon').children().length).toBe(0);
//     expect(toggleInstance.find('Divider').children().length).toBe(0);
//     expect(toggleInstance.find('AddPhotoIcon').children().length).toBe(0);
//     expect(toggleInstance.find('AttachFileIcon').children().length).toBe(0);
//     // expect(toggleInstance.find('Input').placeholder()).toBe("What's on your mind");
//     // const items = toggleInstance.find('Tooltip[title="Send"]')
//     // expect(items).toHaveLength(itemsArray.length)
//     // expect(items.first().text()).toEqual('Shopping')
//     // expect(wrapper.find('button').text()).toEqual('Toggle');
//     // console.log("items=",items);
//     // const result=toggleInstance.find('IconButton').simulate('click')
//     // expect(result).toBe('some action here');
//     const event = {
//       preventDefault() {},
//       target: { value: 'the-value' }
//     };
//     const fileInputRef = {
//       preventDefault() {},
//       current: { click() {} }
//     };
//     const handleAttach = jest.fn();
//     const component = shallow(<OpportunityGrid onClick={handleAttach} />);
//     component.find('#IconButton').simulate('click', fileInputRef);
//     expect(handleChange).toBe(event);
//   });
});
