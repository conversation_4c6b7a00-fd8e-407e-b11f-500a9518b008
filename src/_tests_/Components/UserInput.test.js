import React from 'react';
import FormDialog from '../../components/UserInput';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';
const initialState = true;
React.useState = jest.fn().mockReturnValue([initialState , {}]);
const mockStore = configureMockStore();
const store = mockStore({});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
const chartData = {
  description: '',
  goal_id:189,
  goal_value: '121',
  goal_name: 'Form'
};
jest.mock('@material-ui/core/Button', () => ({
  Button: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Dialog', () => ({
  Dialog: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogActions', () => ({
  DialogActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContent', () => ({
  DialogContent: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContentText', () => ({
  DialogContentText: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogTitle', () => ({
  DialogTitle: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TextField', () => ({
  TextField: () => Component => props => <Component classes="" {...props} />
}));


describe('FormDialog ', () => {
  // it('render FormDialog ', () => {
  //   const component = renderer.create(<FormDialog chartData={chartData} openForm={true}/>);

  //   let tree = component.toJSON();
  //   expect(tree).toMatchSnapshot();
  // });
  it("render FormDialog ", () => {
    const wrapper =  shallow(
      <FormDialog chartData={chartData} openForm={true} closeClick={jest.fn()}  saveClick={jest.fn()}
      />
    );
  expect(toJson(wrapper)).toMatchSnapshot();
});
});
