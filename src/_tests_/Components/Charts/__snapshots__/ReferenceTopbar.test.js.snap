// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Render Comparison Charts rendering  render with data 1`] = `
<ReferenceTopbar
  chartId={960}
  chartName="CP Overview"
  classes=""
  resultSet={
    Array [
      Object {
        "categories": Array [
          "c",
        ],
        "data": Array [
          "1556.56",
          "1479.00",
          "1253.26",
          "1575.13",
          "1511.47",
          "1069.00",
          "1145.22",
          "1583.66",
          "1741.86",
          "1485.36",
          "1396.80",
          "1488.00",
          "1624.95",
        ],
        "dimension": Array [
          "2021-07-01T00:00:00.000",
          "2021-08-01T00:00:00.000",
          "2021-09-01T00:00:00.000",
          "2021-10-01T00:00:00.000",
          "2021-11-01T00:00:00.000",
          "2021-12-01T00:00:00.000",
          "2022-01-01T00:00:00.000",
          "2022-02-01T00:00:00.000",
          "2022-03-01T00:00:00.000",
          "2022-04-01T00:00:00.000",
          "2022-05-01T00:00:00.000",
          "2022-06-01T00:00:00.000",
          "2022-07-01T00:00:00.000",
        ],
        "name": "Koeppel Ford ",
      },
    ]
  }
/>
`;

exports[`Render Comparison Charts rendering  render without data 1`] = `
<ReferenceTopbar
  chartId={960}
  chartName="CP Overview"
  classes=""
  resultSet={Array []}
/>
`;
