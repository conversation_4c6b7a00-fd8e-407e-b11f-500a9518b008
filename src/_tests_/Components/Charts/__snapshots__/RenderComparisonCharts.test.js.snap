// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Render Comparison Charts rendering  render with data 1`] = `
<Card
  style={
    Object {
      "height": "100%",
      "width": "100%",
    }
  }
>
  <CardHeader
    title="CP Overview"
  />
  <Divider />
  <CardContent
    style={
      Object {
        "cursor": "pointer",
        "height": "85%",
      }
    }
  >
    <Line
      data={
        Object {
          "datasets": Array [
            Object {
              "backgroundColor": "#3366CC",
              "borderColor": "#3366CC",
              "borderWidth": 1.5,
              "data": Array [
                "1556.56",
                "1479.00",
                "1253.26",
                "1575.13",
                "1511.47",
                "1069.00",
                "1145.22",
                "1583.66",
                "1741.86",
                "1485.36",
                "1396.80",
                "1488.00",
                "1624.95",
              ],
              "fill": false,
              "label": "Koeppel Ford ",
              "lineTension": 0,
              "pointRadius": 1.5,
              "type": "line",
            },
          ],
          "labels": Array [
            "Jul 21",
            "Aug 21",
            "Sep 21",
            "Oct 21",
            "Nov 21",
            "Dec 21",
            "Jan 22",
            "Feb 22",
            "Mar 22",
            "Apr 22",
            "May 22",
            "Jun 22",
            "Jul 22",
          ],
        }
      }
      options={
        Object {
          "legend": Object {
            "align": "center",
            "labels": Object {
              "boxWidth": 12,
              "usePointStyle": false,
            },
            "position": "bottom",
          },
          "maintainAspectRatio": false,
          "plugins": Object {
            "datalabels": Object {
              "display": false,
            },
          },
          "scales": Object {
            "xAxes": Array [
              Object {
                "display": true,
                "scaleLabel": Object {
                  "display": false,
                  "labelString": "",
                },
                "ticks": Object {
                  "autoSkip": true,
                  "beginAtZero": true,
                  "callback": [Function],
                  "maxRotation": 45,
                  "minRotation": 45,
                },
              },
            ],
            "yAxes": Array [
              Object {
                "display": true,
                "scaleLabel": Object {
                  "display": false,
                  "labelString": "",
                },
                "ticks": Object {
                  "callback": [Function],
                  "maxTicksLimit": 5,
                },
              },
            ],
          },
          "tooltips": Object {
            "axis": "y",
            "backgroundColor": "#2121216e",
            "callbacks": Object {
              "label": [Function],
              "title": [Function],
            },
            "mode": "index",
            "position": "nearest",
          },
        }
      }
    />
  </CardContent>
</Card>
`;

exports[`Render Comparison Charts rendering  render without data 1`] = `<LoaderSkeleton />`;
