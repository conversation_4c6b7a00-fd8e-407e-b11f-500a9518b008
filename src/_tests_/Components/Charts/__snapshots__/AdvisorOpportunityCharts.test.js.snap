// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Addpost rendering  render AdvisorOpportunityCharts  1`] = `
<Card
  bordered={false}
  style={
    Object {
      "border": "1px solid #003d6b",
      "borderRadius": 0,
      "height": "100%",
    }
  }
>
  <CardHeader
    action={null}
    style={
      Object {
        "borderBottom": "1px solid #003d6b",
      }
    }
    subheader="Test"
  />
  <Divider />
  <CardContent
    style={
      Object {
        "cursor": "pointer",
      }
    }
  >
    <div
      style={
        Object {
          "backgroundColor": "#FFF",
          "height": "300px",
          "margin": "auto",
          "position": "relative",
          "width": "100%",
        }
      }
    >
      <Bar
        data={
          Array [
            "2019-12-01",
            "2020-12-31",
          ]
        }
        getElementAtEvent={[Function]}
        options={
          Array [
            "2019-12-01",
            "2020-12-31",
          ]
        }
      />
    </div>
  </CardContent>
  <Component
    chartType="opportunityCharts"
    handlePopupClose={[Function]}
    open={false}
  />
</Card>
`;
