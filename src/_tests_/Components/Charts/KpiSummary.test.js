import React from 'react';
import KpiSummary from '../../../views/Home/KpiSummary';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import renderer from 'react-test-renderer';
jest.mock('react-redux', () => ({
  useSelector: jest.fn(),
  useDispatch: () => jest.fn(),
  connect: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />,
  makeStyles: () => Component => props => <Component classes="" {...props} />,
  styled: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Grid', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/core/Grid', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/InfoOutlined', () => ({
  InfoOutlinedIcon: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/InsertChartOutlined', () => ({
  InsertChartOutlined: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/OpenInNewOutlined', () => ({
  OpenInNewOutlinedIcon: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Forward', () => ({
  ForwardIcon: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/core/Switch', () => ({
  Switch: () => Component => props => <Component classes="" {...props} />,
}));
// jest.mock('@material-ui/styles', () => ({
//   styled: () => Component => props => <Component classes="" {...props} />,
// }));
jest.mock('@material-ui/core/FormGroup', () => ({
  FormGroup: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/core/FormControlLabel', () => ({
  FormControlLabel: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/CheckCircle', () => ({
  CheckCircleIcon: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Brightness1Rounded', () => ({
  CircleIcon: () => Component => props => <Component classes="" {...props} />,
}));

jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  IconButton: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  Radio: () => Component => props => <Component classes="" {...props} />
}));
const props = {
  internalMisses : true,
  toggleOption: true,
  LaborGpRo:[1,2,3,4,5,6],
  PartsGpRo:[],
  TotalGpRo:[],
  FlatRateHrs:[],
  LineROLtSixtyK:[],
  LaborGrid:[],
  PartsGrid:[],
  LineROGtSixtyK:[],
  ROShareData:[],
  allCWITData:[],
  WorkMix:[],
  AvgAgeMiles:[],
}
describe('KpiSummary render ', () => {
  it('render KpiSummary ', () => {
    const wrapper = shallow(<KpiSummary {...props}/>);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render KpiSummary ', () => {
    
    const toggleInstance = shallow(<KpiSummary {...props}/>);
    const elements=toggleInstance.find('Card');
    expect(elements.length).toBe(0);
    const elements1=toggleInstance.find('CardContent');
    expect(elements1.length).toBe(0);
    const elements2=toggleInstance.find('div');
    expect(elements2.length).toBe(0);
    const elements3=toggleInstance.find('span');
    expect(elements3.length).toBe(76);
    const elements4=toggleInstance.find('Typography');
    expect(elements4.length).toBe(38);
    const elements5=toggleInstance.find('img');
    expect(elements5.length).toBe(9);
    const elements6=toggleInstance.find('Tooltip');
    expect(elements6.length).toBe(14);
    const elements7=toggleInstance.find('IconButton');
    expect(elements7.length).toBe(26);
    const elements8=toggleInstance.find('Divider');
    expect(elements8.length).toBe(9);
    expect(toggleInstance.find('Card').children().length).toBe(3);
    expect(toggleInstance.find('CardContent').children().length).toBe(19);
    expect(toggleInstance.find('div').children().length).toBe(85);
    expect(toggleInstance.find('span').children().length).toBe(88);
    expect(toggleInstance.find('Typography').children().length).toBe(52);
    expect(toggleInstance.find('img').children().length).toBe(0);
    expect(toggleInstance.find('Tooltip').children().length).toBe(14);
    expect(toggleInstance.find('IconButton').children().length).toBe(26);
    expect(toggleInstance.find('Divider').children().length).toBe(0);
  });
});
