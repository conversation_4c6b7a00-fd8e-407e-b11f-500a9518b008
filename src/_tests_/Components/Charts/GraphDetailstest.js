import GraphDetails from '../../../components/charts/GraphDetails';
import * as redux from 'react-redux';
import configureMockStore from 'redux-mock-store';

const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  serviceAdvisor: ['All'],
  storeSelected: '',
  storeName: '',
  storeId: ''
});
jest.mock('react-router', () => ({
  ...jest.requireActual('react-router'),
  useHistory: () => ({
    location: { search: 'localhost:3000/' }
  })
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />
}));
describe('GraphDetails Component', () => {
  const title = 'CPR';
  const flag = 1;
  const realm = 'haleyag';
  const result = {
    dimensions: ['CPRevenue.closeddate'],
    filters: [
      {
        member: 'CPRevenue.serviceadvisor',
        operator: 'equals',
        values: [942]
      }
    ],
    measures: [
      'CPRevenue.labor_revenue',
      'CPRevenue.parts_revenue',
      'CPRevenue.combined'
    ],
    order: {
      'CPRevenue.closeddate': 'asc'
    },
    timeDimensions: [
      {
        dateRange: ['2019-11-01', '2020-11-30'],
        dimension: 'CPRevenue.closeddate'
      }
    ]
  };

  test('render getChartDetails properly', () => {
    expect(GraphDetails(title, flag, realm)).toStrictEqual(result);
  });
});
