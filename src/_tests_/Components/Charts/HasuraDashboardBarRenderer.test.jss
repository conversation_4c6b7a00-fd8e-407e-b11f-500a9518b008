import React from 'react';
import HasuraDashboardBarRenderer from '../components/charts/HasuraDashboardBarRenderer';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';

const mockStore = configureMockStore();
const store = mockStore({});
describe('HasuraDashboardBarRenderer ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  const route = {
    routes: ''
  };
  const chartId = 940;
  const storeId = ['75627608', '75627643'];
  const resultSet = {
    measures: [
      'CPGrossProfitPercentage.lbraggrigateprofitpercent',
      'CPGrossProfitPercentage.partsaggrigateprofitpercent',
      'CPGrossProfitPercentage.combinedaggrigateprofitpercent'
    ],
    timeDimensions: [
      {
        dimension: 'CPGrossProfitPercentage.ro_date',
        dateRange: ['2019-12-01', '2020-12-31']
      }
    ],
    dimensions: ['CPGrossProfitPercentage.ro_date'],
    filters: [
      {
        member: 'CPGrossProfitPercentage.store_id',
        operator: 'contains',
        values: ['75627608', '75627643']
      }
    ],
    order: {
      'CPGrossProfitPercentage.ro_date': 'asc'
    }
  };
  it('render HasuraDashboardBarRenderer ', () => {
    const component = renderer.create(
      <HasuraDashboardBarRenderer
        getDataforHasuraBarChart={jest.fn()}
        getHasuraChartMeasures={jest.fn()}
        chartId={chartId}
        resultSet={resultSet}
      />
    );

    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
