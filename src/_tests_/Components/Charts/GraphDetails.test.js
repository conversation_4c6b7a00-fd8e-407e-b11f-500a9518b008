import React from 'react';
import GraphDetails from '../../../components/charts/GraphDetails';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';
import reactRouterDom from 'react-router-dom';
jest.mock('react-router-dom');

const pushMock = jest.fn();
reactRouterDom.useHistory = jest.fn().mockReturnValue({push: pushMock});
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  serviceAdvisor:123
});
jest.mock('react-router', () => ({
  useHistory: () => ({
    push: jest.fn(),
  }),
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
})); 
jest.mock('@material-ui/icons/Edit', () => ({
    EditIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  Divider: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
  IconButton: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/CardActions', () => ({
    CardActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Input', () => ({
    Input: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Collapse', () => ({
    Collapse: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TextField', () => ({
    TextField: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/lab/Alert', () => ({
    Alert: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Close', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons', () => ({
    TrendingUpTwoTone: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/TextareaAutosize', () => ({
  TextareaAutosize: () => Component => props => <Component classes="" {...props} />
}));
jest.mock("src/utils/hasuraServices", () => () => {
  return <mock-getChartMarkdown data-testid="getChartMarkdown"/>;
});
jest.mock("src/graphql/queries", () => () => {
  return <mock-queries data-testid="queries"/>;
});
jest.mock("src/utils/apolloRootClientPostgres", () => () => {
  return <mock-apolloRootClientPostgres data-testid="apolloRootClientPostgres"/>;
});
jest.mock('react-router', () => ({
  ...jest.requireActual('react-router'),
  useHistory: () => ({
    location: {
      search: '/help',
   },
 }),
}));
describe('GraphDetails rendering ', () => {
 
  const keycloak = {
    realmAccess:{
      roles: ['user']
    }
  }
  it('render GraphDetails  ', () => {

    const component = renderer.create(
      <GraphDetails
        keycloak={keycloak} realm={'haleyag'} chartId={944} 
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
