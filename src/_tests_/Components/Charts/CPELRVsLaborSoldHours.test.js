import React from 'react';
import CPELRVsLaborSoldHours from '../../../components/charts/CPELRVsLaborSoldHours';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';
import { getChartConfigurationItemization, getChartTitle } from '../../../utils/Utils';
jest.mock('../../../utils/Utils.js', () => ({ getChartConfigurationItemization: jest.fn() },{ getChartTitle: jest.fn() }));
getChartTitle.mockImplementation(() => {
  return "CPELR";
});
getChartConfigurationItemization.mockImplementation(() => {
  return "CPELR";
});

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/core', () => ({
    Card: () => Component => props => <Component classes="" {...props} />,
    CardContent: () => Component => props => <Component classes="" {...props} />,
    CardHeader: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
  }));
  jest.mock("../../../components/MoreActions.js", () => () => {
    return <mock-MoreActions data-testid="MoreActions"/>;
  });
  jest.mock("../../../components/charts/ItemizationDataGrid.js", () => () => {
    return <mock-ItemizationDataGrid data-testid="ItemizationDataGrid"/>;
  });
  jest.mock("../../../components/Dialog.js", () => () => {
    return <mock-ChartDialog data-testid="ChartDialog"/>;
  });
  jest.mock("../../../utils/Utils.js", () => () => {
    // return <mock-ChartDialog data-testid="ChartDialog"/>;
  });
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[],
    handleResetClicked:jest.fn()
  };
describe('CPELRVsLaborSoldHours  rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <CPELRVsLaborSoldHours {...props} 
      getChartTitle={jest.fn()}
      />
    );
    component.instance().getChartTitle = jest.fn();
    component.update();
    component.instance().getChartTitle(1090);
   expect(component.instance().getChartTitle).toBeCalledWith('BoB');
    // let tree = component.toJSON();
    // expect(tree).toMatchSnapshot();
  });
});
