import React from 'react';
import AdvisorOpportunityCharts from '../../../components/charts/AdvisorOpportunityCharts';
// import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
import configureMockStore from 'redux-mock-store';
import enzyme, {shallow} from 'enzyme';
import {
    getDataforHasuraBarChart,
    getBarChartConfiguration,
    getSubHeader
} from '../../../components/ViewGraphDetailsAction';
import {
    getColorScheme
} from '../../../utils/Utils';
const mockStore = configureMockStore();

const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Card: () => Component => props => <Component classes="" {...props} />,
    CardContent: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
    CardHeader: () => Component => props => (
    <Component classes="" {...props} />
    ),
}));
jest.mock('@material-ui/icons/Send', () => ({
  SendIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AddPhotoAlternate', () => ({
  AddPhotoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/AttachFile', () => ({
    AttachFileIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock("../../../components/MoreActions.js", () => () => {
    return <mock-MoreActions data-testid="MoreActions"/>;
  });
  jest.mock("../../../components/Dialog.js", () => () => {
    return <mock-Dialog data-testid="Dialog"/>;
  });
  jest.mock("../../../components/LoaderSkeleton.js", () => () => {
    return <mock-LoaderSkeleton data-testid="LoaderSkeleton"/>;
  });
  jest.mock('../../../components/ViewGraphDetailsAction.js', () => ({ getDataforHasuraBarChart: jest.fn(),getBarChartConfiguration: jest.fn() , getSubHeader: jest.fn()}));
  getDataforHasuraBarChart.mockImplementation(() => {
    return ["2019-12-01", "2020-12-31"];
  });
  getBarChartConfiguration.mockImplementation(() => {
    return ["2019-12-01", "2020-12-31"];
  });
  getSubHeader.mockImplementation(() => {
    return "Test";
  });
describe('Addpost rendering ', () => {
 
  it('render AdvisorOpportunityCharts ', () => {
    const wrapper = shallow(
      <AdvisorOpportunityCharts  />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  const handleChange = jest.fn();
  it('render AdvisorOpportunityCharts ', () => {
    const toggleInstance = shallow(<AdvisorOpportunityCharts onChange={handleChange} />);
    const elements=toggleInstance.find('Card');
    expect(elements.length).toBe(1);
    const elem=toggleInstance.find('CardHeader');
    expect(elem.length).toBe(1);
    const eleme=toggleInstance.find('Divider');
    expect(eleme.length).toBe(1);
    const elemen=toggleInstance.find('CardContent');
    expect(elemen.length).toBe(1);
    const element1=toggleInstance.find('div');
    expect(element1.length).toBe(1);
    const element2=toggleInstance.find('Bar');
    expect(element2.length).toBe(1);
    expect(toggleInstance.find('Card').children().length).toBe(4);
    expect(toggleInstance.find('CardHeader').children().length).toBe(0);
    expect(toggleInstance.find('Divider').children().length).toBe(0);
    expect(toggleInstance.find('CardContent').children().length).toBe(1);
    expect(toggleInstance.find('div').children().length).toBe(1);
    expect(toggleInstance.find('Bar').children().length).toBe(0);
  });
});
