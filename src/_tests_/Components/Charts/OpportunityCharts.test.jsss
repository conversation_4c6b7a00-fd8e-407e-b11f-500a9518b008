import React from 'react';
import OpportunityCharts from '../components/charts/OpportunityCharts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('react-chartjs-2', () => ({
  Line: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Card: () => Component => props => <Component classes="" {...props} />,
  CardContent: () => Component => props => <Component classes="" {...props} />,
  CardHeader: () => Component => props => <Component classes="" {...props} />,
  CircularProgress: () => Component => props => (
    <Component classes="" {...props} />
  ),
  Divider: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-phone-input-2', () => ({
  PhoneInput: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('../components/MoreActions.js', () => ({
    MoreActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('src/components/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
}));
describe('OpportunityCharts rendering ', () => {
 
  const resultSet = [];
  const resultSets = [{
    data: ["1556.56","1479.00","1253.26","1575.13","1511.47","1069.00","1145.22","1583.66","1741.86","1485.36","1396.80","1488.00","1624.95"
    ],
    name: "Koeppel Ford ",
    dimension: [
      "2021-07-01T00:00:00.000",
      "2021-08-01T00:00:00.000",
      "2021-09-01T00:00:00.000",
      "2021-10-01T00:00:00.000",
      "2021-11-01T00:00:00.000",
      "2021-12-01T00:00:00.000",
      "2022-01-01T00:00:00.000",
      "2022-02-01T00:00:00.000", 
      "2022-03-01T00:00:00.000",
      "2022-04-01T00:00:00.000",
      "2022-05-01T00:00:00.000",
      "2022-06-01T00:00:00.000",
      "2022-07-01T00:00:00.000",
    ]
  }];
  it('render OpportunityCharts with no data ', () => {
    const wrapper = shallow(
      <OpportunityCharts  resultSet={resultSet}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render OpportunityCharts with data ', () => {
    const wrapper = shallow(
      <OpportunityCharts renderChart={jest.fn()} chartId={930} removeFav={jest.fn()} realm={''} handleClosePopup={jest.fn()}  isFrom={'details'} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
