import React from 'react';
import HasuraDashboardLineRenderer from '../../../components/charts/HasuraDashboardLineRenderer';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';
// import {
//   getChartType,
//   getLineChartType,
//   GetyAxisRange,
//   getChartName,
//   getChartDrillDown,
//   getChartDataIndex,
//   getSubHeader
// } from '../../../components/ViewGraphDetailsAction';
// import {
//   getChartConfiguration,
//   getDataforLineChart,
//   getDataforLineChartFromHasura,
//   applyPartialMonthFilterLineCharts
// } from '../../../utils/Utils';
// jest.mock('../../../components/ViewGraphDetailsAction', () => ({ getChartType: jest.fn() },{ getLineChartType: jest.fn() },{ GetyAxisRange: jest.fn() },{ getSubHeader: jest.fn() },
//   { getChartName: jest.fn() },{ getChartDrillDown: jest.fn() },{ getChartDataIndex: jest.fn() }
// ));
// jest.mock('../../../utils/Utils.js', () => ({ getChartConfiguration: jest.fn() },{ getDataforLineChart: jest.fn() },{ getDataforLineChartFromHasura: jest.fn() },
//   { applyPartialMonthFilterLineCharts: jest.fn() },{ getTimeZone: jest.fn() }
// ));
// GetyAxisRange.mockImplementation(() => {
//   return "";
// });
const mockStore = configureMockStore();
const store = mockStore({});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
const route = {
  routes: ''
};
const chartId = 940;
const storeId = ['75627608', '75627643'];
jest.mock("../../../components/ViewGraphDetailsAction", () => () => {
  return <mock-ViewGraphDetailsAction data-testid="ViewGraphDetailsAction"/>;
});
const resultSet = {
  rodate:[],
  measures: [
    'CPGrossProfitPercentage.lbraggrigateprofitpercent',
    'CPGrossProfitPercentage.partsaggrigateprofitpercent',
    'CPGrossProfitPercentage.combinedaggrigateprofitpercent'
  ],
  timeDimensions: [
    {
      dimension: 'CPGrossProfitPercentage.ro_date',
      dateRange: ['2019-12-01', '2020-12-31']
    }
  ],
  dimensions: ['CPGrossProfitPercentage.ro_date'],
  filters: [
    {
      member: 'CPGrossProfitPercentage.store_id',
      operator: 'contains',
      values: ['75627608', '75627643']
    }
  ],
  order: {
    'CPGrossProfitPercentage.ro_date': 'asc'
  }
};
describe('HasuraDashboardLineRenderer ', () => {
  it('render HasuraDashboardLineRenderer ', () => {
    const component = renderer.create(
      <HasuraDashboardLineRenderer
        getDataforHasuraBarChart={jest.fn()}
        getHasuraChartMeasures={jest.fn()}
        chartId={chartId}
        resultSet={resultSet}
      />
    );

    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
