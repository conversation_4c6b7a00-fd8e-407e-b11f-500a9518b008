import React from 'react';
import Search from '../../../components/SearchBar/Search';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Paper: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />,
  Input: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Search', () => ({
  SearchIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('Paytype index rendering ', () => {
  it('render Paytype master ', () => {
    const wrapper = shallow(
      <Search />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render Sidebar Index', () => {
    const toggleInstance = shallow(<Search />);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(1);
    const eleme=toggleInstance.find('Paper');
    expect(eleme.length).toBe(1);
    const elemem=toggleInstance.find('Input');
    expect(elemem.length).toBe(1);
    const elemen=toggleInstance.find('Button');
    expect(elemen.length).toBe(1);
    expect(toggleInstance.find('div').children().length).toBe(2);
    expect(toggleInstance.find('Paper').children().length).toBe(2);
    expect(toggleInstance.find('Input').children().length).toBe(0);
    expect(toggleInstance.find('Button').children().length).toBe(1);
  });
});
