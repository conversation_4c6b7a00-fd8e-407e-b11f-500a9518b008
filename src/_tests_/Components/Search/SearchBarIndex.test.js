import React from 'react';
import Index from '../../../components/SearchBar/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/icons/FilterList', () => ({
  FilterListIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
  jest.mock('@material-ui/lab', () => ({
    ToggleButton: () => Component => props => <Component classes="" {...props} />,
    ToggleButtonGroup: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Add', () => ({
    AddIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/DeleteOutlined', () => ({
    DeleteIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Close', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandMore', () => ({
    ExpandMoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandLess', () => ({
    ExpandLessIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core', () => ({
  Grid: () => Component => props => <Component classes="" {...props} />,
  Button: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/icons/Search', () => ({
  SearchIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('SearchBar index rendering ', () => {
  const mockHandleFilterOpen = jest.fn();
  it('render SearchBar index ', () => {
    const wrapper = shallow(
      <Index onFilter={jest.fn()} onSearch={jest.fn()} className={''}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render SearchBar index ', () => {
    
    const wrapper = shallow(<Index handleFilterOpen={mockHandleFilterOpen}/>);
    const event = {openFilter: true};
    wrapper.find('Button').at(0).simulate('click', event);
  });
  it('render Sidebar Index', () => {
    const toggleInstance = shallow(<Index handleFilterOpen={mockHandleFilterOpen}/>);
    const elements=toggleInstance.find('Grid');
    expect(elements.length).toBe(3);
    const eleme=toggleInstance.find('Search');
    expect(eleme.length).toBe(1);
    const elemem=toggleInstance.find('TabPanel');
    expect(elemem.length).toBe(0);
    expect(toggleInstance.find('Grid').children().length).toBe(5);
    expect(toggleInstance.find('Search').children().length).toBe(0);
    expect(toggleInstance.find('TabPanel').children().length).toBe(0);
  });
});
