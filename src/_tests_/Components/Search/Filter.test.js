import React from 'react';
import Filter from '../../../components/SearchBar/Filter';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
  jest.mock('@material-ui/lab', () => ({
    ToggleButton: () => Component => props => <Component classes="" {...props} />,
    ToggleButtonGroup: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Add', () => ({
    AddIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/DeleteOutlined', () => ({
    DeleteIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Close', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandMore', () => ({
    ExpandMoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandLess', () => ({
    ExpandLessIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core', () => ({
    Button: () => Component => props => <Component classes="" {...props} />,
    Chip: () => Component => props => <Component classes="" {...props} />,
    Collapse: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
    Drawer: () => Component => props => <Component classes="" {...props} />,
    FormControlLabel: () => Component => props => <Component classes="" {...props} />,
    Radio: () => Component => props => <Component classes="" {...props} />,
    RadioGroup: () => Component => props => <Component classes="" {...props} />,
    Slider: () => Component => props => <Component classes="" {...props} />,
    TextField: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
}));

jest.mock('@material-ui/icons/Search', () => ({
  SearchIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('Filter rendering ', () => {
  it('render Filter ', () => {
    const wrapper = shallow(
      <Filter />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render Filter ', () => {
    const toggleInstance = shallow(<Filter />);
    const elements=toggleInstance.find('Drawer');
    expect(elements.length).toBe(1);
    const ele=toggleInstance.find('form');
    expect(ele.length).toBe(1);
    const eleme=toggleInstance.find('div');
    expect(eleme.length).toBe(23);
    const elements1=toggleInstance.find('Button');
    expect(elements1.length).toBe(4);
    const ele1=toggleInstance.find('Typography');
    expect(ele1.length).toBe(6);
    const eleme1=toggleInstance.find('ExpandLessIcon');
    expect(eleme1.length).toBe(0);

    const elements2=toggleInstance.find('ExpandMoreIcon');
    expect(elements2.length).toBe(0);
    const ele2=toggleInstance.find('Divider');
    expect(ele2.length).toBe(2);
    const eleme3=toggleInstance.find('Collapse');
    expect(eleme3.length).toBe(2);
    const elements3=toggleInstance.find('TextField');
    expect(elements3.length).toBe(6);
    const ele3=toggleInstance.find('option');
    expect(ele3.length).toBe(10);
    const eleme4=toggleInstance.find('AddIcon');
    expect(eleme4.length).toBe(0);
    const eleme5=toggleInstance.find('Slider');
    expect(eleme5.length).toBe(1);

    const ele6=toggleInstance.find('RadioGroup');
    expect(ele6.length).toBe(1);
    const eleme6=toggleInstance.find('FormControlLabel');
    expect(eleme6.length).toBe(4);
    const eleme7=toggleInstance.find('ToggleButtonGroup');
    expect(eleme7.length).toBe(1);
    const eleme8=toggleInstance.find('ToggleButton');
    expect(eleme8.length).toBe(2);
    const eleme9=toggleInstance.find('DeleteIcon');
    expect(eleme9.length).toBe(0);
});
});
