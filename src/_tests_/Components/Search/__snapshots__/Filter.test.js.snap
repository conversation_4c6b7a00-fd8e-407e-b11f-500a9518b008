// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Filter rendering  render Filter  1`] = `
<Drawer
  anchor="right"
  classes={
    Object {
      "paper": undefined,
    }
  }
  variant="temporary"
>
  <form
    className=""
    onSubmit={[Function]}
  >
    <div>
      <Button
        size="small"
      >
        <[object Object] />
        Close
      </Button>
    </div>
    <div>
      <div>
        <div
          onClick={[Function]}
        >
          <Typography
            variant="h5"
          >
            Project
          </Typography>
          <[object Object] />
        </div>
        <Divider />
        <Collapse
          in={true}
        >
          <div>
            <div>
              <TextField
                SelectProps={
                  Object {
                    "native": true,
                  }
                }
                fullWidth={true}
                label="Payment status"
                margin="dense"
                name="paymentStatus"
                onChange={[Function]}
                select={true}
                value=""
                variant="outlined"
              >
                <option
                  disabled={true}
                  value=""
                />
                <option
                  key="Pending"
                  value="Pending"
                >
                  Pending
                </option>
                <option
                  key="Canceled"
                  value="Canceled"
                >
                  Canceled
                </option>
                <option
                  key="Completed"
                  value="Completed"
                >
                  Completed
                </option>
                <option
                  key="Rejected"
                  value="Rejected"
                >
                  Rejected
                </option>
              </TextField>
            </div>
            <div>
              <div>
                <TextField
                  className=""
                  label="Filter Tags"
                  margin="dense"
                  name="tag"
                  onChange={[Function]}
                  value=""
                  variant="outlined"
                />
                <Button
                  onClick={[Function]}
                  size="small"
                >
                  <[object Object] />
                  Add
                </Button>
              </div>
              <div>
                <Chip
                  deleteIcon={<UNDEFINED />}
                  key="Full-Time"
                  label="Full-Time"
                  onDelete={[Function]}
                />
              </div>
            </div>
            <div>
              <Typography
                component="p"
                gutterBottom={true}
                variant="overline"
              >
                Project amount
              </Typography>
              <div>
                <Typography
                  variant="body1"
                >
                  $
                  1
                  K
                </Typography>
                <Slider
                  max={20}
                  min={1}
                  onChange={[Function]}
                  value={
                    Array [
                      1,
                      7,
                    ]
                  }
                  valueLabelDisplay="auto"
                />
                <Typography
                  variant="body1"
                >
                  $
                  7
                  K
                </Typography>
              </div>
            </div>
            <div>
              <Typography
                component="p"
                gutterBottom={true}
                variant="overline"
              >
                Project status
              </Typography>
              <div>
                <RadioGroup
                  name="projectStatus"
                  onChange={[Function]}
                  value="ended"
                >
                  <FormControlLabel
                    control={
                      <Radio
                        color="primary"
                      />
                    }
                    label="Ended"
                    value="ended"
                  />
                  <FormControlLabel
                    control={
                      <Radio
                        color="primary"
                      />
                    }
                    label="On-Going"
                    value="onGoing"
                  />
                  <FormControlLabel
                    control={
                      <Radio
                        color="primary"
                      />
                    }
                    label="In Review"
                    value="inReview"
                  />
                  <FormControlLabel
                    control={
                      <Radio
                        color="primary"
                      />
                    }
                    label="Competed"
                    value="completed"
                  />
                </RadioGroup>
              </div>
            </div>
          </div>
        </Collapse>
      </div>
      <div>
        <div
          onClick={[Function]}
        >
          <Typography
            variant="h5"
          >
            Customer
          </Typography>
          <[object Object] />
        </div>
        <Divider />
        <Collapse
          in={false}
        >
          <div>
            <div>
              <div>
                <TextField
                  fullWidth={true}
                  label="Customer name"
                  margin="dense"
                  name="customerName"
                  onChange={[Function]}
                  value=""
                  variant="outlined"
                />
              </div>
              <div>
                <ToggleButtonGroup
                  exclusive={true}
                  onChange={[Function]}
                  size="small"
                  value="freelancer"
                  variant="outlined"
                >
                  <ToggleButton
                    color="primary"
                    value="projectOwner"
                  >
                    Project owner
                  </ToggleButton>
                  <ToggleButton
                    value="freelancer"
                  >
                    Freelancer
                  </ToggleButton>
                </ToggleButtonGroup>
              </div>
              <div>
                <TextField
                  fullWidth={true}
                  label="Email address"
                  margin="dense"
                  name="customerEmail"
                  onChange={[Function]}
                  value=""
                  variant="outlined"
                />
              </div>
              <div>
                <TextField
                  fullWidth={true}
                  label="Phone number"
                  margin="dense"
                  name="customerPhone"
                  onChange={[Function]}
                  value=""
                  variant="outlined"
                />
              </div>
              <div>
                <TextField
                  SelectProps={
                    Object {
                      "native": true,
                    }
                  }
                  fullWidth={true}
                  label="Age"
                  margin="dense"
                  name="customerAge"
                  onChange={[Function]}
                  select={true}
                  value=""
                  variant="outlined"
                >
                  <option
                    disabled={true}
                    value=""
                  />
                  <option
                    key="18 - 30"
                    value="18 - 30"
                  >
                    18 - 30
                  </option>
                  <option
                    key="30 - 45"
                    value="30 - 45"
                  >
                    30 - 45
                  </option>
                  <option
                    key="50 - 60"
                    value="50 - 60"
                  >
                    50 - 60
                  </option>
                  <option
                    key="60+"
                    value="60+"
                  >
                    60+
                  </option>
                </TextField>
              </div>
            </div>
          </div>
        </Collapse>
      </div>
    </div>
    <div>
      <Button
        fullWidth={true}
        onClick={[Function]}
        variant="contained"
      >
        <[object Object] />
        Clear
      </Button>
      <Button
        color="primary"
        fullWidth={true}
        type="submit"
        variant="contained"
      >
        Apply filters
      </Button>
    </div>
  </form>
</Drawer>
`;
