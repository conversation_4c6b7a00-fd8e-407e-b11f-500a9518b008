import React from 'react';
import FilesDropzone from '../../components/FilesDropzone';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/core', () => ({
    Button: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Link: () => Component => props => <Component classes="" {...props} />,
    List: () => Component => props => <Component classes="" {...props} />,
    ListItem: () => Component => props => <Component classes="" {...props} />,
    ListItemIcon: () => Component => props => <Component classes="" {...props} />,
    ListItemText: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    colors: () => Component => props => <Component classes="" {...props} />
  }));

  jest.mock('@material-ui/icons/FileCopy', () => ({
    FileCopyIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/MoreVert', () => ({
    MoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock("src/utils/bytesToSize", () => () => {
    return <mock-bytesToSize data-testid="bytesToSize"/>;
  });

describe('FilesDropzone rendering ', () => {
 
    it('render FilesDropzone ', () => {
      const wrapper = shallow(
        <FilesDropzone  />
      );
      expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render FilesDropzone ', () => {
      const toggleInstance = shallow(<FilesDropzone />);
      const elements=toggleInstance.find('div');
      expect(elements.length).toBe(4);
      const ele=toggleInstance.find('input');
      expect(ele.length).toBe(1);
      const elem=toggleInstance.find('img');
      expect(elem.length).toBe(1);
      const eleme=toggleInstance.find('Typography');
      expect(eleme.length).toBe(2);
      const elemen=toggleInstance.find('Link');
      expect(elemen.length).toBe(1);
      const element=toggleInstance.find('Button');
      expect(element.length).toBe(0);
      expect(toggleInstance.find('div').children().length).toBe(7);
      expect(toggleInstance.find('input').children().length).toBe(0);
      expect(toggleInstance.find('img').children().length).toBe(0);
      expect(toggleInstance.find('Typography').children().length).toBe(6);
      expect(toggleInstance.find('Link').children().length).toBe(1);
      expect(toggleInstance.find('Button').children().length).toBe(0);
    });
  });