import React from 'react';
import Alert from '../../components/Alert';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    colors: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/CheckCircleOutlined', () => ({
    CheckCircleIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/CloseOutlined', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ErrorOutlined', () => ({
    ErrorIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/InfoOutlined', () => ({
    InfoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/WarningOutlined', () => ({
  WarningIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('Alert rendering ', () => {
 const props = {
    className:"alert",
    icon: '',
    variant:'', 
    message:"Alert Message",
    onClose:jest.fn()
 }
  it('render Alert  ', () => {
    const wrapper = shallow(
      <Alert  {...props}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render AddPost ', () => {
    const toggleInstance = shallow(<Alert  {...props}/>);
    const elements=toggleInstance.find('Paper');
    expect(elements.length).toBe(1);
    const element=toggleInstance.find('span');
    expect(element.length).toBe(1);
    const elem=toggleInstance.find('div');
    expect(elem.length).toBe(1);
    const eleme=toggleInstance.find('IconButton');
    expect(eleme.length).toBe(1);
    expect(toggleInstance.find('Paper').children().length).toBe(3);
    expect(toggleInstance.find('span').children().length).toBe(0);
    expect(toggleInstance.find('div').children().length).toBe(1);
    expect(toggleInstance.find('IconButton').children().length).toBe(1);
    expect(toggleInstance.find('Input').children().length).toBe(0);
    expect(toggleInstance.find('CloseIcon').children().length).toBe(0);
  });
});
