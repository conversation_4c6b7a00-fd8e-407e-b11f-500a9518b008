import React from 'react';
import ReviewStars from '../../components/ReviewStars';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockSetState = jest.fn();
jest.mock('react', () => ({
  ...jest.requireActual('react'),
  useState: initial => [initial, mockSetState]
}));
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  colors: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/Star', () => ({
  StarIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/StarBorder', () => ({
  StarBorderIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('PostCard Redirect ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  const title = 'CP Overview';
  it('render ReviewStars ', () => {
    const wrapper = shallow(
      <ReviewStars value={1} star={1} className={"ReviewStars"} />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render ReviewStars', () => {
    const toggleInstance = shallow(<ReviewStars />);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(1);
    expect(toggleInstance.find('div').children().length).toBe(5);
  });
});
