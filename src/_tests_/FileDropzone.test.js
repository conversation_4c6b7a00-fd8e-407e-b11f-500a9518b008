import React from 'react';
import FilesDropzone from '../components/FilesDropzone';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/core', () => ({
    Button: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Link: () => Component => props => <Component classes="" {...props} />,
    List: () => Component => props => <Component classes="" {...props} />,
    ListItem: () => Component => props => <Component classes="" {...props} />,
    ListItemIcon: () => Component => props => <Component classes="" {...props} />,
    ListItemText: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    Tooltip: () => Component => props => <Component classes="" {...props} />,
    colors: () => Component => props => <Component classes="" {...props} />
  }));

  jest.mock('@material-ui/icons/FileCopy', () => ({
    FileCopyIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/MoreVert', () => ({
    MoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock("src/utils/bytesToSize", () => () => {
    return <mock-bytesToSize data-testid="bytesToSize"/>;
  });

describe('FilesDropzone rendering ', () => {
 
    it('render FilesDropzone ', () => {
      const wrapper = shallow(
        <FilesDropzone  />
      );
      expect(toJson(wrapper)).toMatchSnapshot();
    });
  });