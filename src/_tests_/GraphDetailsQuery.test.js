import { getQueryForDetails } from '../components/GraphDetailsQuery.js';
import { getDetailedGraphDateRange,getQueryFilter } from '../components/ViewGraphDetailsAction';

jest.mock('../components/ViewGraphDetailsAction', () => ({ getDetailedGraphDateRange: jest.fn(),getQueryFilter: jest.fn() }));
getDetailedGraphDateRange.mockImplementation(() => {
  return ["2019-12-01", "2020-12-31"];
});
getQueryFilter.mockImplementation(() => {
  return [{
    "member": "CPRevenue.store_id",
    "operator": "contains",
    "values":[
      "75627608",
      "75627643",
    ]
  }];
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
describe('getQueryForDetails Component', () => {
  const chartId = 1068;
  const filters = [1068];
  const storeId = ['75627608',"75627643"];
  const result = {
    dimensions: ['CPRevenue.closeddate'],
    filters: [
      {
        member: 'CPRevenue.store_id',
        operator: 'contains',
        values: ['75627608',"75627643"]
      }
    ],
    measures: [
      'CPRevenue.combined'
    ],
    order: {
      "CPRevenue.closeddate": "desc",
    },
    timeDimensions: [
      {
        dateRange: ['2019-12-01', '2020-12-31'],
        dimension: 'CPRevenue.closeddate'
      }
    ]
  };
  test('render getQueryForDetails properly', () => {
    expect(getQueryForDetails(chartId, filters, storeId)).toStrictEqual(result);
  });
});
