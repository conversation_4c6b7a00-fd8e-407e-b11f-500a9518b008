import React from 'react';
import Dashboard from '../views/Home/Dashboard';
import { shallow } from 'enzyme';
import to<PERSON>son from 'enzyme-to-json';
import renderer from 'react-test-renderer';
jest.mock("../views/Home/GridLayout.js", () => () => {
  return <mock-GridLayout data-testid="GridLayout"/>;
});
jest.mock("../views/Home/layoutConfig.js", () => () => {
  return <mock-layoutConfig data-testid="layoutConfig"/>;
});

describe('Home render ', () => {
  it('render Dashboard ', () => {
    const wrapper = shallow(<Dashboard />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
  it('render Dashboard ', () => {
    
    const toggleInstance = shallow(<Dashboard />);
    const elements=toggleInstance.find('div');
    expect(elements.length).toBe(1);
    expect(toggleInstance.find('div').children().length).toBe(1);
  });
});
