import React from 'react';
import PayTypeErrors from '../views/PayTypeMaster/PayTypeErrors';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import renderer from 'react-test-renderer';
import * as redux from 'react-redux';

const mockStore = configureMockStore();
const store = mockStore({});
// const spy = jest.spyOn(redux, 'useSelector');
// spy.mockReturnValue({
//   isLoading: true,
//   storeSelected: '',
//   storeName: '',
//   storeId: ''
// });
jest.mock('@material-ui/core/Button', () => ({
  Button: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Dialog', () => ({
  Dialog: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogActions', () => ({
  DialogActions: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContent', () => ({
  DialogContent: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/DialogContentText', () => ({
  DialogContentText: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
jest.mock('@material-ui/core/DialogTitle', () => ({
  DialogTitle: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@react-keycloak/web', () => ({
  withKeycloak: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('react-redux', () => ({
  connect: () => Component => props => <Component classes="" {...props} />
}));
JSON.parse = jest.fn().mockImplementationOnce(() => {
  // return your what your code is returning.
});
describe('Paytype master rendering ', () => {
  // it('render paytype ', () => {
  //   const wrapper = shallow(
  //     <PayTypeErrors
  //       keycloak={{
  //         realmAccess: { roles: ['admin'] }
  //       }}
  //       onBtClose={jest.fn()}
  //       handleClose={jest.fn()}
  //       handleCloseRow={jest.fn()}
  //       onBtExport={jest.fn()}
  //       onGridReady={jest.fn()}
  //       onBtClose={jest.fn()}
  //     />
  //   );
  //   expect(toJson(wrapper)).toMatchSnapshot();
  // });
  it('Renders correctly', () => {
    const component = renderer.create(
      <PayTypeErrors
        keycloak={{
          realmAccess: { roles: ['admin'] }
        }}
        handleClose={jest.fn()}
        onGridReady={jest.fn()}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
