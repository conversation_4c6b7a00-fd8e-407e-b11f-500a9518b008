import React from 'react';
import "@testing-library/jest-dom";
import { render, screen } from "@testing-library/react";
import { MockedProvider } from "@apollo/client/testing";
import { REFRESH_VIEWS_STATUS } from '../graphql/queries';
import { hasuraServices } from '../utils/hasuraServices';
import { Charts } from '../views/Home/Charts';
const mocks = [
  {
    request: {
      query: REFRESH_VIEWS_STATUS,
      variables: {
        name: "<PERSON>"
      }
    },
    result: {
      data: {
        dog: { id: "1", name: "<PERSON>", breed: "bulldog" }
      }
    }
  }
];

it("renders without error", async () => {
  const props = {history:{
      location:{
          state:{
              ronumber: '102211'
          }
      }
  }};
  const { findByText, getByText } = render(
    <MockedProvider mocks={mocks} addTypename={false}>
      <Charts />
    </MockedProvider>
  );
  expect(getByText("Loading products...")).toBeInTheDocument();
  const productTag = await findByText("Nike Shoes");
  expect(productTag).toBeInTheDocument();
});

// it("should render dog", async () => {
//   const dogMock = {
//     request: {
//       query: GET_DOG_QUERY,
//       variables: { name: "Buck" }
//     },
//     result: {
//       data: { dog: { id: 1, name: "Buck", breed: "poodle" } }
//     }
//   };
//   render(
//     <MockedProvider mocks={[dogMock]} addTypename={false}>
//       <Dog name="Buck" />
//     </MockedProvider>
//   );
//   expect(await screen.findByText("Loading...")).toBeInTheDocument();
//   expect(await screen.findByText("Buck is a poodle")).toBeInTheDocument();
// });

// it("should show error UI", async () => {
//   const dogMock = {
//     request: {
//       query: GET_DOG_QUERY,
//       variables: { name: "Buck" }
//     },
//     error: new Error("An error occurred")
//   };
//   render(
//     <MockedProvider mocks={[dogMock]} addTypename={false}>
//       <Dog name="Buck" />
//     </MockedProvider>
//   );
//   expect(await screen.findByText("An error occurred")).toBeInTheDocument();
// });