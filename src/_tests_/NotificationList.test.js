import React from 'react';
import NotificationList from '../components/NotificationsPopover/NotificationList';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";
const mockStore = configureMockStore();
const store = mockStore({});
jest.mock("../utils/gradients");
jest.mock('@material-ui/styles', () => ({
    makeStyles: () => Component => props => (
      <Component
        classes=""
        {...props}
      />
    ),
})); 
jest.mock('@material-ui/core', () => ({
    Avatar: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    List: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    ListItem: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    ListItemAvatar: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    ListItemText: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    colors: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    Button: () => Component => props => (
      <Component
        classes=""
        {...props}
      />
  ),
}));
jest.mock('@material-ui/icons/ArrowForward', () => ({
    ArrowForwardIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/Payment', () => ({
    PaymentIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/PeopleOutlined', () => ({
    PeopleIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/Error', () => ({
    ErrorIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/Code', () => ({
    CodeIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/Store', () => ({
    StoreIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/ArrowForward', () => ({
    ArrowForwardIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PaymentIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PeopleIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    ErrorIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    CodeIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    StoreIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/ArrowForward', () => ({
    ArrowForwardIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PaymentIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PeopleIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    ErrorIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    CodeIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    StoreIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/ArrowForward', () => ({
    ArrowForwardIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PaymentIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PeopleIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    ErrorIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    CodeIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    StoreIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock('@material-ui/icons/ArrowForward', () => ({
    ArrowForwardIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PaymentIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    PeopleIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    ErrorIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    CodeIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
    StoreIcon: () => Component => props => (
        <Component
          classes=""
          {...props}
        />
    ),
}));
jest.mock("src/utils/gradients", () => () => {
  return <mock-gradients data-testid="gradients"/>;
});
jest.mock('react-redux', () => ({
  useDispatch: jest.fn()
  }));
describe("NotificationList render ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });  
    const notifications = [{
      id:1,
      Title: 'Notification',
      Type: 'Noti',
      value: 'payType'
    }]  
    it("render NotificationList ", () => {
        const wrapper =  shallow(
          <NotificationList
            notifications={notifications}
            className=''
            handleNotificationsClose={jest.fn()}
           />
        );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    it('render NotificationList ', () => {
      const toggleInstance = shallow(<NotificationList  notifications={notifications} className='' handleNotificationsClose={jest.fn()}/>);
      const elements=toggleInstance.find('List');
      expect(elements.length).toBe(1);
      const elements1=toggleInstance.find('ListItem');
      expect(elements1.length).toBe(1);
      const elements2=toggleInstance.find('ListItemAvatar');
      expect(elements2.length).toBe(2);
      const elements3=toggleInstance.find('ListItemText');
      expect(elements3.length).toBe(1);
      const elements4=toggleInstance.find('Button');
      expect(elements4.length).toBe(1);
      expect(toggleInstance.find('List').children().length).toBe(1);
      expect(toggleInstance.find('ListItem').children().length).toBe(3);
      expect(toggleInstance.find('ListItemAvatar').children().length).toBe(1);
      expect(toggleInstance.find('ListItemText').children().length).toBe(0);
      expect(toggleInstance.find('Button').children().length).toBe(1);
    });
});   