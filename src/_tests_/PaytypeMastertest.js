import React from 'react';
import PaytypeMaster from '../views/PayTypeMaster/PaytypeMaster';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
jest.mock('jquery-sparkline', () => {});
jest.mock('@material-ui/core/styles', () => ({
  StylesProvider: () => Component => props => (
    <Component classes="" {...props} />
  ),
  jssPreset: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/pickers', () => ({
  MuiPickersUtilsProvider: () => Component => props => (
    <Component classes="" {...props} />
  )
}));
describe('Paytype master rendering ', () => {
  JSON.parse = jest.fn().mockImplementationOnce(() => {
    return 75627643;
  });
  // it('render paytype ', () => {
  //   const wrapper = shallow(
  //     <PaytypeMaster
  //     //   keycloak={{
  //     //     realmAccess: { roles: ['admin'] }
  //     //   }}
  //     //   onBtClose={jest.fn()}
  //     //   handleClose={jest.fn()}
  //     //   handleCloseRow={jest.fn()}
  //     //   onBtExport={jest.fn()}
  //     //   onGridReady={jest.fn()}
  //     //   onBtClose={jest.fn()}
  //     />
  //   );
  //   expect(toJson(wrapper)).toMatchSnapshot();
  // });
  it('Renders correctly', () => {
    const component = renderer.create(
      <PayTypeDrilldown
        keycloak={{
          realmAccess: { roles: ['admin'] }
        }}
        onGridReady={jest.fn()}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
