import React from 'react';
import WorkMixVolume from '../views/WorkMixVolume/WorkMixVolume';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});
jest.mock('jquery-sparkline', () => {});

jest.mock("../components/Page", () => () => {
  return <mock-page data-testid="Page"/>;
});
jest.mock('../components/Page.js');
jest.mock('@material-ui/core', () => ({
  Button: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  FormControl: () => Component => props => <Component classes="" {...props} />,
  InputLabel: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Box: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  MenuItem: () => Component => props => <Component classes="" {...props} />,
  Select: () => Component => props => <Component classes="" {...props} />,
  Tooltip: () => Component => props => <Component classes="" {...props} />,
}));
jest.mock('@material-ui/icons/Restore', () => ({
  RestoreIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
  Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
  Tabs: () => Component => props => <Component classes="" {...props} />
}));

jest.mock('@material-ui/icons/ArrowBack', () => ({
  ArrowBackIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/GetApp', () => ({
  ExportIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Link', () => ({
  Link: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/styles', () => ({
  withStyles: () => Component => props => <Component classes="" {...props} />
}));
describe('PayTypeDrilldown rendering ', () => {
  it('Renders correctly', () => {
    const component = renderer.create(
      <WorkMixVolume
        keycloak={{
          realmAccess: { roles: ['admin'] }
        }}
        onGridReady={jest.fn()}
      />
    );
    let tree = component.toJSON();
    expect(tree).toMatchSnapshot();
  });
});
