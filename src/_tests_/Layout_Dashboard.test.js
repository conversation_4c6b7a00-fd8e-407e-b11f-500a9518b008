import React from 'react';
import Dashboard from '../layouts/Dashboard/index';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
// jest.mock('jquery-sparkline', () => ({
//   makeStyles: () => Component => props => <Component classes="" {...props} />
// }));
jest.mock('jquery-sparkline', () => {});
jest.mock('react-ga');
describe('Layout Render ', () => {
  it('render Dashboard ', () => {
    const wrapper = shallow(<Dashboard />);
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
