import cubejs from "@cubejs-client/core";
import { getDashboardGraphQuery } from '../components/DashboardGraphQuery';
const token = "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJnYk1SbFJkUnpSdzNVTjVsVFF1a2ZvT3huWnhfamVUQXFHbGZfUjhLVWRjIn0.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Zu8U6Wkc50NRTPE3VkhKfZSolUbozH8qJkhCf4F7azYr9Slju6x-kW5yJdkfMrE71Pyd8q0XFpsowq44my_SuKxQZplSgOtO5ad5O4GxkBs88YslbOoEUpTn_2rTiQxbQNvUpblmfL9yEv4Zb-WAMO1YCJDnx_jTgNQXm1JpgyBqUxFUR0IUFgiDY8Hg49qX1WYV9qq5XLmW_jUPb7Z2pwIlbqSk6AtK5ydeeTmjmpXPJK0jKAj3Dmb_EoMy_RhcrhgNpU-xF39RJC-ipbRmg2JCMGVa1XrIAemU64ThjvklP7WwMQSgkHcXzEi4Yi0ytU5xAt4qAuqSBQQRMm26pQ"
const tokens =  token ? `Bearer ${token}` : '' ;
const cubejsApi = cubejs(tokens, {
  apiUrl: 'https://api-koeppelag.fixedopspc.com/cubejs-api/v1'
});
import { getDetailedGraphDateRange,getQueryFilter } from '../components/ViewGraphDetailsAction';

jest.mock('../components/ViewGraphDetailsAction', () => ({ getDetailedGraphDateRange: jest.fn(),getQueryFilter: jest.fn() }));
getDetailedGraphDateRange.mockImplementation(() => {
  return ["2019-12-01", "2020-12-31"];
});
getQueryFilter.mockImplementation(() => {
  return [{
    "member": "CPRevenue.store_id",
    "operator": "contains",
    "values":[
      "75627608",
      "75627643",
    ]
  }];
});
const query = getDashboardGraphQuery(
  942,
  ['All'],
  76184939,
  'koeppelag'
);

// console.log("query=",query);
it("Returns CPRevenue combined data", async () => {
  const resultSet = await cubejsApi.load({
    measures: ['CPRevenue.combined'],
    timeDimensions: [
      {
        dimension: 'CPRevenue.closeddate',
        dateRange: ["2019-12-01", "2020-12-31"]
      }
    ],
    dimensions: ['CPRevenue.closeddate'],
    filters: [{
        "member": "CPRevenue.store_id",
        "operator": "contains",
        "values":[
          "76184939",
        ]
      }],
    order: {
      'CPRevenue.closeddate': 'asc'
    }
  });
  const data = await resultSet.serialize();
  const dataResult = data.loadResponse.data;
  // console.log("datas=",data.loadResponse.data);
  const result = [
    {
      'CPRevenue.closeddate': '2020-01-01T00:00:00.000',
      'CPRevenue.combined': '181077.95'
    },
    {
      'CPRevenue.closeddate': '2020-02-01T00:00:00.000',
      'CPRevenue.combined': '211121.53'
    },
    {
      'CPRevenue.closeddate': '2020-03-01T00:00:00.000',
      'CPRevenue.combined': '102456.07'
    },
    {
      'CPRevenue.closeddate': '2020-05-01T00:00:00.000',
      'CPRevenue.combined': '102943.35'
    },
    {
      'CPRevenue.closeddate': '2020-06-01T00:00:00.000',
      'CPRevenue.combined': '175907.10'
    },
    {
      'CPRevenue.closeddate': '2020-07-01T00:00:00.000',
      'CPRevenue.combined': '174043.32'
    },
    {
      'CPRevenue.closeddate': '2020-08-01T00:00:00.000',
      'CPRevenue.combined': '177094.96'
    },
    {
      'CPRevenue.closeddate': '2020-09-01T00:00:00.000',
      'CPRevenue.combined': '159451.36'
    },
    {
      'CPRevenue.closeddate': '2020-10-01T00:00:00.000',
      'CPRevenue.combined': '192141.94'
    },
    {
      'CPRevenue.closeddate': '2020-11-01T00:00:00.000',
      'CPRevenue.combined': '162172.13'
    },
    {
      'CPRevenue.closeddate': '2020-12-01T00:00:00.000',
      'CPRevenue.combined': '200691.53'
    }
  ];
  expect(JSON.stringify(dataResult)).toEqual(JSON.stringify(result));
  console.log("result value=",data);
});

it("Returns CPGrossProfit Labor Gross Profit,PartsGrossGrofit,CombinedGrossProfit data", async () => {
  const resultSet = await cubejsApi.load({
    measures: [
      'CPGrossProfit.laborgrossprofit',
      'CPGrossProfit.prt_gross_profit',
      'CPGrossProfit.combinedgrossprofit'
    ],
    timeDimensions: [
      {
        dimension: 'CPGrossProfit.ro_date',
        dateRange: ["2019-12-01", "2020-12-31"]
      }
    ],
    dimensions: ['CPGrossProfit.ro_date'],
    filters: [{
      "member": "CPGrossProfit.store_id",
      "operator": "contains",
      "values":[
        "76184939",
      ]
    }],
    order: {
      'CPGrossProfit.ro_date': 'asc'
    }
  });
  const data = await resultSet.serialize();
  const dataResult = data.loadResponse.data;
  console.log("datas=",localStorage.getItem('keycloakToken'));
  const result = [
    {
      'CPGrossProfit.ro_date': '2020-01-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '79167.79',
      'CPGrossProfit.prt_gross_profit': '32997.89',
      'CPGrossProfit.combinedgrossprofit': '112165.68'
    },
    {
      'CPGrossProfit.ro_date': '2020-02-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '89138.00',
      'CPGrossProfit.prt_gross_profit': '38260.20',
      'CPGrossProfit.combinedgrossprofit': '127398.20'
    },
    {
      'CPGrossProfit.ro_date': '2020-03-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '42864.57',
      'CPGrossProfit.prt_gross_profit': '18542.06',
      'CPGrossProfit.combinedgrossprofit': '61406.63'
    },
    {
      'CPGrossProfit.ro_date': '2020-05-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '43509.58',
      'CPGrossProfit.prt_gross_profit': '19228.44',
      'CPGrossProfit.combinedgrossprofit': '62738.02'
    },
    {
      'CPGrossProfit.ro_date': '2020-06-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '74147.02',
      'CPGrossProfit.prt_gross_profit': '30624.30',
      'CPGrossProfit.combinedgrossprofit': '104771.32'
    },
    {
      'CPGrossProfit.ro_date': '2020-07-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '74351.51',
      'CPGrossProfit.prt_gross_profit': '31313.41',
      'CPGrossProfit.combinedgrossprofit': '105664.92'
    },
    {
      'CPGrossProfit.ro_date': '2020-08-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '75368.73',
      'CPGrossProfit.prt_gross_profit': '28956.43',
      'CPGrossProfit.combinedgrossprofit': '104325.16'
    },
    {
      'CPGrossProfit.ro_date': '2020-09-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '65612.60',
      'CPGrossProfit.prt_gross_profit': '29956.00',
      'CPGrossProfit.combinedgrossprofit': '95568.60'
    },
    {
      'CPGrossProfit.ro_date': '2020-10-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '83306.44',
      'CPGrossProfit.prt_gross_profit': '34356.76',
      'CPGrossProfit.combinedgrossprofit': '117663.20'
    },
    {
      'CPGrossProfit.ro_date': '2020-11-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '70772.11',
      'CPGrossProfit.prt_gross_profit': '27971.25',
      'CPGrossProfit.combinedgrossprofit': '98743.36'
    },
    {
      'CPGrossProfit.ro_date': '2020-12-01T00:00:00.000',
      'CPGrossProfit.laborgrossprofit': '88000.07',
      'CPGrossProfit.prt_gross_profit': '36801.53',
      'CPGrossProfit.combinedgrossprofit': '124801.60'
    }
  ];
  expect(JSON.stringify(dataResult)).toEqual(JSON.stringify(result));
});


it("Returns CPLabourhours ro_date,soldhours data", async () => {
  const resultSet = await cubejsApi.load({
    measures: ['CPLabourhours.soldhours'],
    timeDimensions: [
      {
        dimension: 'CPLabourhours.ro_date',
        dateRange: ["2019-12-01", "2020-12-31"]
      }
    ],
    dimensions: ['CPLabourhours.ro_date'],
    filters: [{
      "member": "CPLabourhours.store_id",
      "operator": "contains",
      "values":[
        "76184939",
      ]
    }],
    order: {
      'CPLabourhours.ro_date': 'asc'
    }
  });
  const data = await resultSet.serialize();
  const dataResult = data.loadResponse.data;
  console.log("datas=",dataResult);
  const result =[
    {
      'CPLabourhours.ro_date': '2020-01-01T00:00:00.000',
      'CPLabourhours.soldhours': '734.78'
    },
    {
      'CPLabourhours.ro_date': '2020-02-01T00:00:00.000',
      'CPLabourhours.soldhours': '817.81'
    },
    {
      'CPLabourhours.ro_date': '2020-03-01T00:00:00.000',
      'CPLabourhours.soldhours': '435.40'
    },
    {
      'CPLabourhours.ro_date': '2020-05-01T00:00:00.000',
      'CPLabourhours.soldhours': '410.61'
    },
    {
      'CPLabourhours.ro_date': '2020-06-01T00:00:00.000',
      'CPLabourhours.soldhours': '715.30'
    },
    {
      'CPLabourhours.ro_date': '2020-07-01T00:00:00.000',
      'CPLabourhours.soldhours': '691.55'
    },
    {
      'CPLabourhours.ro_date': '2020-08-01T00:00:00.000',
      'CPLabourhours.soldhours': '743.70'
    },
    {
      'CPLabourhours.ro_date': '2020-09-01T00:00:00.000',
      'CPLabourhours.soldhours': '637.40'
    },
    {
      'CPLabourhours.ro_date': '2020-10-01T00:00:00.000',
      'CPLabourhours.soldhours': '750.37'
    },
    {
      'CPLabourhours.ro_date': '2020-11-01T00:00:00.000',
      'CPLabourhours.soldhours': '642.43'
    },
    {
      'CPLabourhours.ro_date': '2020-12-01T00:00:00.000',
      'CPLabourhours.soldhours': '805.80'
    }
  ];
  expect(JSON.stringify(dataResult)).toEqual(JSON.stringify(result));
});