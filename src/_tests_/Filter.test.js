import React from 'react';
import Filter from '../components/SearchBar/Filter';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
  jest.mock('@material-ui/lab', () => ({
    ToggleButton: () => Component => props => <Component classes="" {...props} />,
    ToggleButtonGroup: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Add', () => ({
    AddIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/DeleteOutlined', () => ({
    DeleteIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Close', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandMore', () => ({
    ExpandMoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ExpandLess', () => ({
    ExpandLessIcon: () => Component => props => <Component classes="" {...props} />
  }));
jest.mock('@material-ui/core', () => ({
    Button: () => Component => props => <Component classes="" {...props} />,
    Chip: () => Component => props => <Component classes="" {...props} />,
    Collapse: () => Component => props => <Component classes="" {...props} />,
    Divider: () => Component => props => <Component classes="" {...props} />,
    Drawer: () => Component => props => <Component classes="" {...props} />,
    FormControlLabel: () => Component => props => <Component classes="" {...props} />,
    Radio: () => Component => props => <Component classes="" {...props} />,
    RadioGroup: () => Component => props => <Component classes="" {...props} />,
    Slider: () => Component => props => <Component classes="" {...props} />,
    TextField: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
}));

jest.mock('@material-ui/icons/Search', () => ({
  SearchIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('Paytype index rendering ', () => {
  it('render Paytype master ', () => {
    const wrapper = shallow(
      <Filter />
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
