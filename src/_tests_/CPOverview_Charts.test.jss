import React from 'react';
import Charts from '../views/CPOverview/Charts';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from "redux-mock-store";

const mockStore = configureMockStore();
const store = mockStore({});
describe("CPOverview rendering ", () => {
    JSON.parse = jest.fn().mockImplementationOnce(() => {
        return 75627643;
      });
    it("render CPOverview ", () => {
        const wrapper =  shallow(
                    <Charts
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
});   
