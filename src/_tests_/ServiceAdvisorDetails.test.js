import React from 'react';
import ServiceAdvisor from '../views/ServiceAdvisorDetails/ServiceAdvisor';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import configureMockStore from 'redux-mock-store';
import * as redux from 'react-redux';
import renderer from 'react-test-renderer';

const mockStore = configureMockStore();
const store = mockStore({});
const mockDispatch = jest.fn();
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: ''
});
JSON.parse = jest.fn().mockImplementationOnce(() => {
  return 75627643;
});

jest.mock('@material-ui/core/Button', () => ({
  Button: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tab', () => ({
  Tab: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core/Tabs', () => ({
  Tabs: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
  Box: () => Component => props => <Component classes="" {...props} />,
  LinearProgress: () => Component => props => <Component classes="" {...props} />,
  Paper: () => Component => props => <Component classes="" {...props} />,
  Typography: () => Component => props => <Component classes="" {...props} />,
  withStyles: () => Component => props => <Component classes="" {...props} />
  }));

  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Restore', () => ({
    RestoreIcon: () => Component => props => <Component classes="" {...props} />
  }));
  const props = {
    months:[{monthYear: '02/22'}],
    location: {
      pathname: '/WarrantyReferenceLabor',
    },
    type:'ss',
    category:'aa',
    reporttype:'rr',
    keycloak:{ 
      realmAccess:{
        roles: []
      }
    },
    selectedDates:[]
  };
describe('ServiceAdvisor  rendering ', () => {
  it('Renders correctly', () => {
    // const component = renderer.create(
    //   <ServiceAdvisor {...props} 
    //   />
    // );
    // let tree = component.toJSON();
    // expect(tree).toMatchSnapshot();
    const wrapper = shallow(
      <ServiceAdvisor {...props}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
