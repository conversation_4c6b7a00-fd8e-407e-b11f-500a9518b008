import React from 'react';
import Topbar from '../layouts/Auth/Topbar';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
jest.mock('@material-ui/core/styles', () => ({
    withStyles: () => Component => props => <Component classes="" {...props} />,
    makeStyles: () => Component => props => <Component classes="" {...props} />,
  }));
  jest.mock('@material-ui/core/DialogTitle', () => ({
    DialogTitle: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogContent', () => ({
    DialogContent: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/DialogActions', () => ({
    DialogActions: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Dialog', () => ({
    Dialog: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Popover', () => ({
    Popover: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Tooltip', () => ({
    Tooltip: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/ListItemIcon', () => ({
    ListItemIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/ListItemText', () => ({
    ListItemText: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/ListSubheader', () => ({
    ListSubheader: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/AccountCircle', () => ({
    AccountCircle: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/Divider', () => ({
    Divider: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/List', () => ({
    List: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core/ListItem', () => ({
    ListItem: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core', () => ({
    AppBar: () => Component => props => <Component classes="" {...props} />,
    Toolbar: () => Component => props => <Component classes="" {...props} />,
    Button: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Paper: () => Component => props => <Component classes="" {...props} />,
    MenuItem: () => Component => props => <Component classes="" {...props} />,
    MenuList: () => Component => props => <Component classes="" {...props} />,
  }));
  jest.mock('@material-ui/icons/Input', () => ({
    InputIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/core', () => ({
    Grid: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/ContactSupport', () => ({
    ContactSupportIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Phone', () => ({
    PhoneIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/Email', () => ({
    EmailIcon: () => Component => props => <Component classes="" {...props} />
  }));

describe("Topbar Render ", () => {
    it("render Topbar ", () => {
        const wrapper =  shallow(
                    <Topbar
                     />
            );
        expect(toJson(wrapper)).toMatchSnapshot();
    });
    
});   