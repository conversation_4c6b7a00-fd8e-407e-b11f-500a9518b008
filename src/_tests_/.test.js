import React from 'react';
import AuthGuard from '../components/AuthGuard';
import { shallow } from 'enzyme';
import toJson from 'enzyme-to-json';
import * as redux from 'react-redux';
const spy = jest.spyOn(redux, 'useSelector');
spy.mockReturnValue({
  isLoading: true,
  storeSelected: '',
  storeName: '',
  storeId: '',
  user:{first_name:''}
});
jest.mock('@material-ui/styles', () => ({
  makeStyles: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/core', () => ({
    Paper: () => Component => props => <Component classes="" {...props} />,
    IconButton: () => Component => props => <Component classes="" {...props} />,
    Typography: () => Component => props => <Component classes="" {...props} />,
    colors: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/CheckCircleOutlined', () => ({
    CheckCircleIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/CloseOutlined', () => ({
    CloseIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/ErrorOutlined', () => ({
    ErrorIcon: () => Component => props => <Component classes="" {...props} />
  }));
  jest.mock('@material-ui/icons/InfoOutlined', () => ({
    InfoIcon: () => Component => props => <Component classes="" {...props} />
}));
jest.mock('@material-ui/icons/WarningOutlined', () => ({
  WarningIcon: () => Component => props => <Component classes="" {...props} />
}));
describe('AuthGuard rendering ', () => {
 const props = {
    className:"alert",
    icon: '',
    variant:'', 
    message:"Alert Message",
    onClose:jest.fn()
 }
  it('render AuthGuard  ', () => {
    const wrapper = shallow(
      <AuthGuard  roles={"Admin"} children={<div>Hello,</div>}/>
    );
    expect(toJson(wrapper)).toMatchSnapshot();
  });
});
