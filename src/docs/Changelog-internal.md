<div>Please specify changes as following:</div>

<p>

- Enhancements - add new feature as Enhancements
- Functionality Improvements - bug fixes and other small changes

</p>

## <h1>1.3.0 (10/27/22)</h1>

- Enhancements - Add multiple opcode save functionality before reloading data.
- Enhancements - Make op code refresh store specific.
- Functionality Improvements - Add op code refresh page and refresh status improvements
- KPI Score Card - Dynamic Radio Buttons for Labor and Parts compliance.
- Rename Grid Period and Matrix Period Drop Down as Current, prior 1 , prior 2 and so on - Labor Grids and Matrix
- Add all grid types - In KPI reports, KPI goal settings
- Drill down to the grid with corresponding door rate from Labor Misses.
- Search By RO - Rename Service Request to “Request / Story / Cause” and adjust columns to fit, Show data on
  "Request / Story / Cause” column if any of tooltip data present
- KPI Goal Settings - Order KPI Nos, Date format, KPI Number column renaming
- RO Search for Parts Misses Cliff Harris - Using corresponding model
- Opcode Edit - Reduce Table Width, Change Alert Message Format, Rename Column Menu Sales to Menu Item and CP Repair Grid Item to Repair Grid Item

## <h1>Completed tasks till version - 1.2.1 (10/18/22)</h1>

- Grid computation for higher hours. Check matrix as well.
- Check PgDCP - grant permissions especially for physical rw
- Delay in Sawyer daily data load setup - dependency with Praveen
- Door Rate & Grid Type fix and Drill down to the grid with corresponding door rate
- Order grids with sold hours
- Different icons for makes , ro counts, same icon for all op code menus, same icon for RO counts , tic tac toe icon for Labor and Parts Grids, car icon for makes
- Opcodes - MPI and Opcodes - Menu
- Get Models for Warrensburg out to David
- 7650952 - Op code description gets trimmed in tooltip - Ford of Tulsa - Search RO page - Fix DB
- Split Disc & Special Metrics and Advisor & Tech Metrics
- Other Labor Work Mix - fit it on one line - rname to Labor WorkMix - Other
- Nav menu - increase font size to two points for submenus
- See Whats New - Orange - Change Log
- Warrensburg CDJR-Fiat
- Stivers and all other except Bill Knight - Only Noncategorized CP Opcodes to be pulled on the Dashboard for edits
- Stivers - Rename store to Hyundai-Genesis
- Set up users for Sawyer, Stivers and Warrensburg
- KPI Score Card - Dynamic Radio Buttons for Labor and Parts compliance
- Repair Labor <br> Target Misses and Repair Parts <br> Target Misses - On Nav menu
- Rename Nav Menu - KPI Score Card Goal Settings to KPI Reports <br> Goal Settings
- Unmapped Pay Type bug for Lincoln Volvo store
- Op Code descriptions - not found in Op Codes Categorization
- Save Opcodes and Reload Data - Rename buttons. Rename Save Opcodes to Save Changes and reduce size of the buttons - same as reset layout.Add messages as per David's email. Window.reload icon to be orange - tooltip with orange background and white font
- Op Code Categorized - Increase font size for the info messages
- Bell Icon positioning for UI - Discuss with David
- Add Grid Type column to Labor Grid and Matrix Type to Parts Matrix
- Warrensburg - Parts Matrix Source 100 with List Price + 10%
- Labor Grid - Parts Matrix - same size drop downs
- "Choose Grid Period/ Choose Grid Type - Labor Grid
- Choose Matrix Period/Choose Matrix Type - Parts Matrix"
- Drill down from Missed to grids or matrix - menu to move to the current selection
- Op Code Detailed View Page, Tech detailed View and Service Advisor Detailed View - Enable Service Advisor/Tech as dropdowns - totals at the top - Job Counts, Labor Dollars and Profits
- Drill down filters not working as expected
- Parts Matrix - Do we need to exclude for N/A job parts?
- Non Categorized Op Code mapping for Bill Knight
- Advisor /Tech Issue fix- deadlock
- Cliff Harris - Opcode mappings received
- Op Code Categorization - Check Email from David
- Matrix Install date - rename
- Part Source column missing in the Parts Misses page
- Parts Cost and Parts List on separate columns on the Parts Matrix misses
- Set N/A's to exclude by default in all bill knight stores and fix the grid
- Labor Grid misses - Add tech names too after the advisor column
- Send release log - before build - to David - to approve
- Change log not updated
- Retail, Internal Matrix - Current and Prior1 -Effective date
- No export on grids and matrices - no select capability
- Rename Labor Grid(s), Parts Matrix(s)
- Labor Grid Pricing - Drop Down - Current, Prior, Prior 1 etc with dates appended
- Other Work Mix - Remove Parts and Labor Drop Down
- Text to be left aligned everywhere in grids
- Additional column grid or do not grid - Opcode screen
- Notifications for additional opcodes that are not available in the opcode table
- Advisors mapped in correctly - BillKnight we should reload. Pls refer RO 7653484 - Tulsa - Advior Wesley Thomas is inactive as per data but we show this person as active.Take active flag from data. Also add name into index of advisor and tech tables . Also check RO 7652122. We need to check if we have loaded the data for the incorrect store. For this RO , the advisor is supposed to be Robert Potter with id - 598 but it shows Hennip. Tulsa store says they have no Hennip as an advisor. Something not right here. Pls check. Also wherever we pull service advisor names, and there are 2 ids with same name for same store , then pick only the one thats active. Always compare with Enrollment sheet that I have shared.
- Additional/new opcodes to be added to opcode table as we load daily data and set to REPAIR/NA
- Correct the Grids and Matrix - Spelling and Presentation.
- Add Part Numbers and description to Matrix misses
- Fix Non responsive page in Op Code Screen when Mat view refresh fails
- Do not edit department for Bill Knight or make Pay Type page non editable for dealer trak stores
- Opcode Counts by Store, Advisor or Tech
- Search RO - Scroll bar to be floating
- Add tenant for daily load and login emails in FCR, AWS and PgDCP integration. Take client name from Client master table
- KPI report email functionality - DB fix for html template
- Parts matrix screen - List or Cost to be mentioned based on store and source
- Cliff Harris - Sources
- Discuss with David - Sawyer Parts Matrix - MSRP and sources, Grid - Cars 1500 Trucks and 2500 Trucks
- Reply to David's emails.
- Discuss with Praveen on the Dealer Track Op Code descriptions and Comments. Take opcode description from source raw op code desc table and add comments to the service request
- Listen to Video recording of Joe,David Johnston and David Stonham in drop box
- Role alias on UI
- Listen to drop box video recording for Fisher Honda
- Fix Dealer track comments for labor lines and map to Service Request
- Check all grids and matrices - add in rates and verify effective dates - pending to add prior grid dates
- KPI Score Card rest of the changes - snapshot from Riju - completed. Pending implementation
- WorkMix Opcode tooltip - Pending
- "Stivers Pending
  - Parts Matrix Load - Pending
  - Revenue Summary not loaded - completed. Pending data mismatch fixes
  - Daily Logins and Support Feedback and Daily Loads status emails - Pending"
- "Cliff Harris -
  - Run load in AWS - Pending
  - Keycloak configurations in FCR and AWS for Cliff Harris - pending
  - Grid load - pending
  - Matrix Load - pending
  - Daily Load Setup - pending
  - Daily Logins and Support Feedback and Daily Loads status emails - pending
  - Infra setup AWS - Pending"
- Store and advisor goals changes - Pending
- Hyperlink door rate and take to grid screen with a tool tip - click to view grid - Pending UI . DB Completed. PgDCP integration pending
- Discuss deployment architecture with Shahid and Rinshad
- Verify all update screens and release
- Pay Type opcode update issue fixes
- Add table for email configurations and KPI Report email recipients
- Nation wide data load and pull opcodes
- Grid & Matrix View Screen - DB Pgdcp integration
- Load to aggregate - total revenue details function - make model year mileage and customer name and integrate to PgDCP - Sawyer
- hyperlink to store site.
- Refer email for Bill Knight store name changes
- Hyperlink door rate and take to grid screen
- Grids and matrices on screen - Grid or door Install date and 1 hour door rate
- Store grouping - Lincoln-Volvo update on UI, Add Phone Number
- Ford Stillwater, Lincoln-Volvo - rename everywhere
- Service Advisor /Technician Edit screens - nickname edit not working
- Parts sources for Cliff Harris - Send email to David
- Excluded Op codes and Parts for Grid and Matrix and Markups
- Grid Install Date - Rename column - Grid misses
- "Grid Misses - Op Code with tool tip of desc after RO column. Add door rate
- DB - for labor grid drill down - add Op code, opcode desc(most frequent desc) , door rate"
- Export option for Labor Grid Misses and Parts Misses
- KPI report - add goals from goal settings table
- "Advisor and Store Goals -
  1. Default Empty Grid with Headings for Advisor Goals - populate when advisor selected
     2.Email report - Have one check box near store goals as well - add appropriate messages and pop up after save recipients with close button
     3.Move edit button below - for Advisor Goals
  2. Current Goal - Assigned Date
  3. Edit button tool tip /message"
- Op Code Update page not working as it should - Bill Knight
- Parts - Core Exchange Program - Pull Parts with -C appended for Bill Knight
- search ro page - Parts column - rename to Part
- Cliff Harris - Parts Matrix - No sources - Ask David
- Notification and change store toggle to the left of BillKnight Auto Group
- Put the address back on the Top Bar - add ph no
- Login and data load mails for koeppel and login not received - last received was on 9/6
- Logins & Downloads - Screen. Downloads for rolling 10 days
- Sawyer Scripts
- Cliff Harris - FCR
- WorkMix Opcode tooltip
- Store Settings - Updates
- Search RO changes
- Misses Page changes
- Bill Knight \$135 grid load for Tulsa
- Daily load - mail trigger - cron setup - bill knight, stivers, cliff harris
- Stivers AWS - Data Load
- Stivers Daily loads - integrate with Scheduler_invoke script
- Stivers Grid and Matrix load
- Stivers - techs without name - compare with closed date of RO - Send email to David
- Stivers - advisors without name - compare with closed date of RO - Send email to David
- Check with David - KPI Snapshots
- Home Page - Store group changes
- remove the current matrix/grid as of column
- add door rate column to the misses
- check all grids , door rates and dates for all tenants
- Update manufacturer in pgdcp script
- Follow up with Praveen for remaining data for Stivers/CliffHarris/Nationwide/Sawyer
- Cliff Harris Parts and Labor Grid
- Stivers Parts and Labor Grid Load
- Group name on Top Bar
- KPI Expanded view - all KPIs - Snapshots
- "Store Settings - Only US Timezones
- Working Days - Add Three Quarter day (0.75)"
- KPI Report - Take Store and Advisor goals from respective tables
- Labor- Parts Misses -parts cost/list to be within 2 lines on the column
- Get rid of Current Grid as of from Misses screen
- Follow up with Rinshad on the Advisor Report mail trigger during load time
- KPI Score Card - DB Html Template
- Display List Price & Parts Source in RO Search Page, Part No
- WorkMix - 2 month Comparison - Display Op Code description as tooltip when hovered over Op Code
- David's emails - fixes
- KPI Score card - No of working days affecting counts
- Active Techs and Advisors - Check with David
- Changelog - Reduce font size of Date
- Discuss change log updates with David
- Labor /Parts Misses - Reduce font size of duration and misses dropdowns.Reduce box size
- Date formats on the toggle - KPI Score card
- Get list of sources for Bill Knight l]Lincoln Volvo
- KPI Score card expanded and standard/condensed- Send design snapshots to David
- KPI numbers not seen on store 1
- Icons on score card get distorted for different resolutions
- Discount for Bill Knoght
- Matrix lincoln volvo
- Misses and KPI score card dropdown different grids and matrices
- Toggle duration
- Timezone for Store
- Triggering emails after import process for KPI report
- Set cron jobs to email reports per advisor at Store timezone
- KPI Setups - Remove email trigger. Move Email report check box to right of grid. Add recipients and have option to delete.
- Armatus Admin - Set store timezone - send reports at that time between 6am-7am
- Email Trigger to be removed. Have a time zone option
- Client logins under Armatus Admin
- David email fixes
- Data download status page under Reference Page
- Update report for David
- Bill Knight - Grids and Matrix load
- Bill Knight Op Code load and Dashboard refresh
- FCR - Support and Feedback image issue
- Labor Grid /Parts Matrix improvements - Diff to be renamed to Net Difference - Grid width adjustments - Refer David's email dated 16-08-2022
- Change Log improvements as per David's Email
- KPI Score Card - Last 3 months , Last 12 months toggle
- Setups Page - KPI Report - Store Goal Grid - Columns KPI No, KPI Name ,Store Goal, Prior 12 month actual, Prior 3 month actual and Current Goal
- KPI Report#1 - Goal Settings as title
- Setups Page - KPI Report - Advisor Grid - Columns KPI No, KPI Name ,Store Goal, Prior 12 month actual, Prior 3 month actual and Current Goal
- Darken grey shade for KPI Report
- Cron Job to trigger KPI report PDF emails
- Setups Page for KPI Report
- Change Log - Site Enhancements and Site Functionality Improvements
- RO - Search Remove Job Description. Change Description column to Service Request - the tool tip should have Service Request, Customer Story, Cause.
- Op code description as tooltip on the op code -search RO
- Functionality Improvements in changelog
- Service Advisor Top Bar Drop Down - Move Edit and Apply filter to the top.
- Grafana - Add date of login to login status and login location dashboards
- KPI Scorecard - Internal Grids toggle
- Op Code Detailed View - Color Code the variance
- RO Search - Parts to Part 1, Part 2
- Setups Page for KPI Report- Design to be sent by David
- KPI Report 1 - Create PDF and Email PDF icons
- KPI Report - new tab on the nav bar - Customer Pay - Under Summary Overview - add a new tab - KPI Report 1 - Individual Advisor
- "Labor and Parts Misses -(Please refer David's email)
  1. Top Bar - mail icon for Contact Us - This need to be a hyperlink for an email.
  2. Please add Job Count then Sold hours.
  3. Please add “Diff \$: “
  4. Please further left justify and even spacing in between. "
- Part Matrix Misses - Parts Extended Sale to be considered for variance calculation
- Demo AG Anonymization
- KPI Report #1 Design to be finalized
- KPI Report #2 – Advisor Rollup - to be discussed with David
- Return Rate Drill Down fix - With Service Advisor Filter
- "Special Metrics - Return Rate Drill Down
  1. Headings need to be the same’
     a. Line 1 on the top = Vin Count
     b. Line 2 on top = Last 6 Months (wrap)
  2. When I opened this it was too wide, and I condensed it to make it easier to read (like we need to please have on all such pages please).
  3. No tab highlight (and we need it).
  4. “RO Close Date” + columns for Tech and Advisor, please. "
- Discount Metrics - Last two charts - Add a space between service advisor bars
- Customer Name - Set to XXXXX
- Email formats for Armatus Branding
- Toggle duration fix based on browser timezone
- Number the kpi's - Upper left corner as a bold number
- Support and Feedback - Issue not fixed with the corrupt file - Send button gets enabled before the attachment is created, and if for some reason the call to the api fails, and user closes the support and feedback window, the next time he clicks on Support and Feedback, a fresh window should open instead of showing the previous window with the overlay of the previous call.
- Scatter Plot - Have both tooltips if possible when a point in the data table is hovered.
- Glossary - Left Justify the Abbreviation Column values and reduce column width
- "Labor Grid and Parts Matrix Misses -
  1. Add back the Grid Date Column - Rename to Active Grid Used and Active Matrix Dated
  2. Add Labor Sold Hour column for labor misses and Parts cost for Parts misses
  3. Nav bar name to be Cust. Pay Repair - Labor Target Misses and Cust. Pay Repair - Parts Target Misses. Add Cust to Glossary
  4. RO Closed Date column to be renamed to RO Cls Date - Add Cls to Glossary
  5. Top Right - Left Justify the Data As of, Rename Grid Dated to Current Grid As of and Target Rate in 3 rows
  6. Variance calculation to be Actual Sale - Target Price."
- Pay Type Master - Non Categorized Pay Types - Left justify tabs, split the Action column to Edit and View Ros, Pay Type group drop down on edit gets cut off(needs to be fixed)
- When drilling down from KPI Score card to grid misses , nav bar selection to be made available.
- Discount Metrics - By Service Advisor tab - Have Advisor names on the charts
- Navigating back to the Home Page from Special metrics with the users original SA filtering selections but values get reset.
- Special Metrics - drill down on charts - tab selection on nav bar disappears - fix everywhere applicable
- 1 Line Ros - KPI - Link to Special Metrics page
- Error Handling - React JS
- Koeppel FCR[Mazda]: When advisor is selected, x axis value orders are changed
- Koeppel Ford - Compliance showing 'TRUE', even if there is variance
- Advisor Metrics - Category By Month - When any month is selected on the drop down and chart expanded - June data always seen on the expanded chart
- OTHER - Add ToolTip - OTHER = All Opcodes Used Less than 2% of the Time
- Tech Metrics - Go to detailed page - selection of nav menu goes away - to be fixed.
- Labor Workmix - Demo Store1 - 2 month Comparison - Labor Work Mix % chart - edit markdown is not selectable - resizable - scroll bar missing
- Add Percentage symbol for all GP% column values in drill downs and reports
- Scatter Plot - both tooltips if possible , scales to be bigger
- Enable Service Advisor Filter for Misses tabs - maintain state between KPI Score card and these pages
- Filters - only toggle required.
- Labor & Parts Grids - when back button clicked from Search RO - state not set and labor grid misses is reloaded - this needs to be fixed.
- KPI Score Card - Labor & Parts Misses - View Misses for drill down tooltip.
- Labor & Parts Grids - Show only non compliant Ros
- Search RO - Labor - Target Price, Labor Sale, Variance, Labor Cost, GP% - Colour the variance box
- Mails dated 6/20 from David - to discuss
- Special Metrics - Multi Line RO Count charts and Link from KPI dashboard to special metrics for 1 Line RO KPI
- Revenue Summary graphql fix
- 911 discussion with Mathew and Shahid
- Provide Davids Office Number on UI for Contact
- UI Enhancement mails from David dated - 6/16 & 6/17
- Missing Reset Layout in some of the pages
- Feedback - Scroll & Corrupt jpg screenshot - Add Last Name, Expandable description, enable send button only after all mandatory fields /or screenshot check box and pic is loaded
- Back Button on the Labor Grid Misses and Parts Grid misses pages
- Revert Store Swap changes
- Disable Telemetry and see load time
- RO Search Enable Customer Name
- All Grid - Fix the unresponsive issues
- Parts Misses - Ro Search not working
- Parts Misses - Slow Loading for Subaru
- Scatter Plot - RO Seacrh loading slow
- Op Code Detailed View - Issues
- Subaru Grid loads
- "To Discuss with David - Pay Type Notifications
- Work mix Op Code detailed view - Op Code AF"
- Blank page issue for Client and User role logins
- Ford and Nissan Labor Grids - Latest - June 2022 - Load to DB
- Mazda Labor Grid - Latest - June 2022 - Load to DB
- Fix AWS Server issue and send an email to David
- Labor Grid Misses and Parts Misses - Add variance column
- Labor Grid misses and Parts misses - RO search page Grid price, variance, list price - color code
- Postgres 14 IMMV
- Add Store filter for Service Advisor based functions
- Git Semver and Change Log generator
- User Login Telemetry emails
- Email to CDK to go through David Johnston for approval and then forward to CDK post approval.
- Duration toggle on KPI dashboard - Update code for Yesterday and Day Before Yesterday
- "Version 1.0.1 Build release - <Date> on the footer
- Add Current Version and Release Date
- Add Prior Version and Release Date"
- Data As of on the KPI dashboard
- Check with Rinshad if he has updated the latest daily load script to PgDCP
- 13 months - Fill in the missing months with zero for Discount Metrics - 13 month trends - Ford/Hyundai and any other stores that shows this behaviour on the chart
- Optimize slow queries on data load
- Release notes and Release Versioning
- Add David Stonham and David Johnston to the setup and feedback armatus group
- LOP Discounts for Koeppel - Discuss with David Johnston - Check for CDK contacts
- Have a contact us link on the login page
- FCR build following bt Demo AG builds to be released , reviewed by David Stonham and then rest of the stores released.
- Scheduler fix to get the env as a prefix to decide on the folder in the S3 bucket where the files are to be placed
- Keycloak FDW setup
- Mail gun fdw setup
- Unleash Integration
- scatter plot issues
- op code description tool tip now working on work mix reports - both labor and parts
- Nissan release
- Grafana - add all relevant telemetry including DB calls
- Status.fixedopspc.com - Add service urls for react ui pages and postgraphile
- Check if we have a 404 page created for incorrect routes - if not we need this for every page
- Support and Feedback - <NAME_EMAIL> - Configure mail group
- Version for Application - Major-Minor-Patch releases
- Nissan discount fix
- "Observability - Need to achieve the following-
- Never happen events - Data loads daily - triggering emails to business users if load did not happen before a schedule time on a daily basis
- Logins per day per store - Trigger emails"
- What if - check if there is a mismatch in data
- Mail Setup for Feed Back- <EMAIL>
- Setup Users for 3 stores
- Disable remember me feature
- Support and feedback - clicking anywhere outside the box closes it
- Support & Feedback Setup - Remove Scroll bar
- Op Code Categorization - Align Op Code desc to left justified
- KPI Issues mentioned by David
- site reliability group setup - <EMAIL>
- Rest Password - Add validation for rejecting same password
- Login location on a map
- Pay Type Master - Remove filters from grid
- Keycloak - Forgot Password setup
- Allow client and user role to have the what if edit capability
- Op Code Edits to have a drop down for Op Category selection
- Op Code Summary Filter issue
- Armatus Master Login for all tenants
- Data Validations - Nissan
- Discount Issue for Nissan
- Data Validations - Subaru
- Data Validatons - Ford
- Data Validations - Mazda
- Data Validations - Hyundai
- Op Code & Pay Type Refreshes
- Mail Gun Setup for Loads, Refreshes , Support and Feedback - Talk to Deol
- Support & Feedback Setup - Add to Configuration - <EMAIL>
- Bulk Data Load for all 5 Stores
- Postgraphile Shield
- Postgraphile Anonymous
- Group store configurations keycloak
- Realm configuration Keycloak
- User Id Configuration in Keycloak
- Daily Loads Setup - Nissan
- Nissan Loads
- Subaru & Nissan Op Codes
- Config - Labor & Parts Matrix - Margin +/- 99 cents
- Scatter Plot - Do we need to display N/As - Not required
- Remove Null techs from table
- Labor Warranty Rates and Parts Warranty rates
- Labor Rate Model and Parts Rate Model - Nissan & Subaru - FCR and AWS
- Parts Matrices
- QA Issues
- Google Analytics with Microsoft Clarity
- Client role - what if - check with David Stonham
- Daily Data Loads
- MPI and Menu Penetration
- Return Rate charts
- Set WMV% for all 5 stores
- Scatter Plot Issues
- What if Reports - Set appropriate Goals for all 5 stores
- Active Techs
- Active Advisors
- Grafana Observabiliy
