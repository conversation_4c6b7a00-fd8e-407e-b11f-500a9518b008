<p>All Notable Enhancements.  Functionality Improvements Not Listed Here</p>

## <h1>Version 2.4.0 Release Date-11/23/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Tech Metrics – NEW CHART – 1 Line RO Count Under 60K, Over 60K, and Total Shop.<i goTo="TechnicianPerformance" tab="seven">
- Tech Metrics – Tech Productivity Chart – All Sold Hours – When drilling down from a weekly point in the chart you will see hours displayed by pay type (with additional enhancements coming to this drill down soon).<i goTo="TechnicianPerformance" tab="seven">
- Reference / Setups – NEW TAB – Customer History tab added to display Customer details by date.<i goTo="CustomerHistory">
- New Login Page – There is now a FOPC Corporate Login to be used by Armatus – FOPC personnel.
- Search RO Box – Functionality enhanced now displays recycled repair orders (ROs with the same number, but different open/close dates).

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 2.3.0 Release Date-10/09/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Freeze Pane Feature added on the navigation bar to lock the Home and Favourites tabs at the top.<i goTo="Home">
- Tech Metrics – Tech Productivity Chart now begins each week on a Sunday, and because this newer chart now includes all features that were in the prior chart below, we’ve removed that chart below (all-in-one now).<i goTo="TechnicianPerformance"tab="seven" >
- Performance-Speed optimizations have been released for the following pages:
  - CP Summary Overview<i goTo="CPOverview">
  - Labor Overview<i goTo="CPLaborOverview">
  - Labor Work Mix – Work Mix tab<i goTo="LaborWorkMixAnalysis">
  - "What If” Opportunity – Hrs Per RO & Labor GP%<i goTo="LaborGrossAndVolumeOpportunity">
  - “What If” Opportunity – Effective Labor Rate<i goTo="CPELROpportunity">
  - Parts Overview<i goTo="CPPartsOverview">
  - Parts Work Mix – Work Mix tab<i goTo="PartsWorkMixAnalysis">
  - “What If” Opportunity – Hrs Per RO & Parts GP%<i goTo="PartsGrossAndVolumeOpportunity">
  - Tech Metrics – 13 Month trend charts<i goTo="TechnicianPerformance" tab="seven">
  - Discount Metrics – 13 Month charts<i goTo="Discounts" tab="three">
  - Special Metrics – 13 Month charts<i goTo="SpecialMetrics">
- Removed the “Site Refresh” button in Reference/Setups – Opcode Categorizations<i goTo="OPcodes"> and Pay Types<i goTo="PayTypeMaster"> tab. Now with all opcode edits and others “Save Changes” button does the save and site refresh.

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 2.2.0 Release Date-09/01/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Home Page Dropdown Added – Technician filtering is now available next to Advisor filtering (individually or grouping selectable).<i goTo="Home"></i>
- Tech Metrics Tab – NEW CHART added for Tech Weekly Productivity, representing All Sold Hours, Customer Pay Hours, Warranty, and Internal Hours.<i goTo="TechnicianPerformance" ></i>
- Reference/Setups – Technicians Tab – Added two additional columns (Team Tech and Team Assigned To) giving the user Tech team assignment functionality. (Currently available for Dealer Track only, but with other DMS functionality coming soon.)<i goTo="Technicians"></i>
- FOPC Site Speed Optimizations Continue, such as with CP Summary Overview, Labor Overview, Parts Overview, Labor Work Mix> Opcode Summary, and Opcode Detailed View. (Many more optimizations coming in the next release.)

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 2.1.0 Release Date-07/07/23</h1>

<Grid>
<div>

<span style="font-weight: bold; font-size: 16px">Enhancements</span> <br><br>

<span class="dot"></span> Sitewide Speed Optimizations.

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 2.0.0 Release Date-06/06/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Site Release Log – A Hyperlink feature has been added to redirect the user to the appropriate dashboard where a feature or enhancement has been added.<i goTo="ChangeLog"></i>
- Home Page – KPI Score Card – “CP Repair Grid Targets” renamed to “CP Repair Labor Price Targets”.<i goTo="Home"></i>
- Home Page – KPI Score Card – “CP Parts Matrix Targets” renamed to “CP Repair Parts Price Targets”.<i goTo="Home"></i>
- Reference / Setups – Opcode Categorizations Tab – “Reprocess Data History” button renamed to “Site Refresh Now”.<i goTo="OPcodes"></i>
- Reference / Setups – Pay Types – Added columns – “Labor Fixed Rate” and “Parts Fixed Rate”.<i goTo="PayTypeMaster"></i>
- Reference / Setups – Pay Types – Renamed column “Excl. from Compliance” to “Compliance Excluded”.<i goTo="PayTypeMaster"></i>
- Reference / Setups – “Menu Item” tab was renamed to Fixed Rate History”, which now displays an “Opcode Fixed Rate” tab and a “Pay Type Fixed Rate” tab, with historical tracking and other functions.<i goTo="FixedRates"></i>
- Labor Work Mix – “Opcode - Summary” and “Opcode - Detailed View” – Added feature to Edit Opcodes.<i goTo="LaborWorkMixAnalysis"></i>
- Parts Tab – Repair Parts Target Misses – Added column “Opcode”.<i goTo="PartsMisses"></i>
- Reports – Job Count Grid – User can now select or deselect (filter) Competitive, Maintenance, and Repair.<i goTo="JobCountGrid"></i>
- Reports – Trend Report – User can now select at the store level or Advisor level (Advisor Filter Added).<i goTo="TrendReport"></i>
- Labor Overview and Parts Overview Tabs – Partial Month checkbox has been improved.<i goTo="CPLaborOverview"></i>
- Labor Target Misses and Parts Target Misses – Added column “% Variance” and added “RO Count” to the summary at the top.<i goTo="LaborMisses"></i>
- Parts Overview – Parts Target Misses – Added columns “Extended Sale”, “Extended Cost”, “Quantity”, and “Target Extended Price”.<i goTo="PartsMisses"></i>
- Scatter Plot – On the drill-down box a delete icon has been added, and a View Opcode feature has been added to redirect the user to the Opcode Categorizations page when clicked.<i goTo="PartsItemization"></i>

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 1.9.0 Release Date-04/12/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- User Profile – Role descriptions updated.<i goTo="Home"></i>
- Reference / Setups – Pay Types – Additional column added “Exclude from Compliance” identifies specific pay types that will not be utilised for compliance for Labor and Parts compliance calculations.<i goTo="PayTypeMaster"></i>
- Opcode Categorization – Additional columns added for “Date Last Used” (the last date Opcode was used on an RO), “Used Last 12 Mths” (number of times Opcode was used in last 12 months), “Fixed Rate” (checkbox), “Fixed Rate (\$)” and “Fixed Rate Install Date”.<i goTo="OPcodes"></i>
- Reference / Setups – NEW TAB ADDED – “Opcode Fixed Rates”, listing all Opcodes with a fixed labor rate.<i goTo="FixedRates"></i>
- Settings Edit History – Added additional History items to the dropdown.<i goTo="EditHistory"></i>
- Parts Target Misses – Added Opcode column.<i goTo="PartsMisses"></i>
- Reference / Setups – KPI Goal Settings – Added additional email functionality options.<i goTo="ScoreCardGoalSetting"></i>
- Reference / Setups – NEW TABS ADDED – “Labor - Wty Jobs” and “Parts - Wty Jobs” giving the user additional visibility for warranty jobs sold.<i goTo="WarrantyRatesLabor"></i>
- Reports – NEW TAB ADDED – “Job Count Grid” – This new report shows Job counts for Competitive, Maintenance and Repair that mirror scatter plot job counts for the same periods of time.<i goTo="JobCountGrid"></i>
- Scatter Plot – Labor and Parts – Added a new date range selector (Last 7 Days), in addition to a hyperlink for the new Job Count Grid report.<i goTo="LaborItemization"></i>
- Reports – NEW TAB ADDED – “Trend Report – 13 Mth” – This report represents a 13 month trend of overall Sales, Cost and Gross Profit for Service Labor Pay Types (Customer Pay, Warranty, Internal , Maintenance, Service Contract and Factory Service Contract).<i goTo="TrendReport"></i>

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 1.8.0 Release Date-03/28/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Reference / Setups – New Menus added for Store Settings, Daily Data Imports, Settings Edit History, Labor – Wty Jobs and Parts – Wty Jobs.<i goTo="Glossary"></i>
- Labor Work Mix and Parts Work Mix – Op Code Summary – Added an additional column – NA that categorizes all Opcodes that are NA and defaulted to REPAIR.<i goTo="LaborWorkMixAnalysis"></i>
- KPI Report #1 enhancements - Rephrased message to say 'Please choose an advisor from the drop down.'. Title to reflect KPI Report #1 - Individual Advisors.<i goTo="KpiReport"></i>
- KPI Report Store/Advisor Goal Settings - Enhancements to add recipients for receiving Store only, Advisor Only or both reports.<i goTo="KpiReport"></i>
- What-Ifs – When a selected advisor does not have a last quarter baseline , this page would still load with an empty graph and a message to alert the user.<i goTo="LaborGrossAndVolumeOpportunity"></i>
- KPI Score Card and elsewhere – Duration toggle format updates.<i goTo="Home"></i>
- Reference and Setups - Opcode Categorization - Renamed column Repair Grid Item to Repair Compliance.<i goTo="OPcodes"></i>
- Drill Downs – All drill downs with an RO hyper link would have a tooltip that says, “View RO”.<i goTo="CPOverview"></i>
- Work Mix – Labor and Parts – Opcode - Detailed View – Added Job Count to the Summary.<i goTo="LaborWorkMixAnalysis"></i>
- Work Mix – Labor and Parts – Opcode Summary – Added RO Count to the Summary.<i goTo="LaborWorkMixAnalysis"></i>
- Armatus Admin – Labor – Wty Jobs and Parts – Wty Jobs – Added totals to Summary.<i goTo="WarrantyRatesLabor"></i>
- Settings Edit History, Labor – Wty Jobs and Parts – Wty Jobs – Enhanced Date Range Picker added.<i goTo="EditHistory"></i>
- Labor and Parts Target Misses – Font color for Over Sold Jobs updated to blue.<i goTo="LaborMisses"></i>
- Labor and Parts Overview – Show/Hide Partial Month toggle button – color reversed to dark blue with white font.<i goTo="CPLaborOverview"></i>
- Navigation Menu – Renamed Expand/Collapse to Expand/Collapse All, icon for Parts Menu made 25% smaller.<i goTo="Home"></i>

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 1.7.0 Release Date-02/20/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Reference / Setups – Service Advisor and Technicians’ setup and edit functions have been modified with a dropdown functionality when selecting between non-active and active (the checkbox is gone).<i goTo="ServiceAdvisors"></i>
- Enhancement to switch stores from the home page without going back to the store selection page.<i goTo="Home"></i>
- Opcode Categorization Page – Improvements, such as a yellow border highlight for Opcodes in process of change, a selection box drop-down now defaults at blank until edited, a Department column has been added which allows the user to filter between Service and Body Shop Opcodes, etc.<i goTo="OPcodes"></i>
- Notification Bell – In addition to new Opcode notifications, users will be notified when new Advisors and Techs are found.<i goTo="Home"></i>
- Discount Metrics – Service Advisor monthly comparative have been added horizontally (more Discount Metrics enhancements are coming soon).<i goTo="Discounts"></i>
- “What-If’s” – Added capabilities to view the selected Advisor and their corresponding opportunity on a full 13-month trend + All user credentials now have the ability to utilize “What-If” functionality.<i goTo="LaborGrossAndVolumeOpportunity"></i>
- All Dashboards – All charts will display as current plus last 12 months, instead of the earlier display of last 12 months to current metrics.<i goTo="CPOverview"></i>
- Navigation Menu – New reports tab added under CP Summary Overview.<i goTo="KpiReport"></i>
- Labor Grid Misses – Dealer pay types and FOPC Pay Type Groups added to the drill down.<i goTo="LaborMisses"></i>
- Labor Overview – New chart added – CP ELR – Competitive & Maintenance.<i goTo="CPLaborOverview"></i>
- Opcode Summary (under Work Mix) – Filtering buttons added to look at 3 months, 6 months, and Current + 12 months of opcode history.<i goTo="LaborWorkMixAnalysis"></i>
- All Drill Downs – Open Date column added for every Repair Order.<i goTo="CPOverview"></i>

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 1.6.0 Release Date-01/20/23</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Reference / Setups – Op Code Categorization – Additional columns allowing for the selection of Menu and MPI Items.<i goTo="OPcodes"></i>
- Reference / Setups – Op Code Categorization – Functionality improvement allowing the user to review changes prior to reprocessing the data history.<i goTo="EditHistory"></i>
- KPI Report #1 - Hyperlink feature added directing the user to the setup section under Reference & Setups.<i goTo="KpiReport"></i>
- Reference / Setups - KPI Goal Setting – Significant improvements allowing the user to select an email schedule specific to individual recipient requirements such as, daily, weekly, bi-weekly, monthly, etc.<i goTo="ScoreCardGoalSetting"></i>
- Special Metrics – Enhanced 1 Line RO and Multi-Line RO charts to better display all mileage bands used.<i goTo="SpecialMetrics"></i>
- Work Mix – Advisor filters are now enabled to drill down from the ‘What If’ Opportunity tabs, which also allow individual advisor work mix chart viewing.<i goTo="LaborGrossAndVolumeOpportunity"></i>
- ‘What If’ Opportunity – “All What If Tabs” – Advisor Filters Enabled allowing the selection of individual advisors.<i goTo="LaborGrossAndVolumeOpportunity"></i>
- Non-Compliance “Target Misses” Pages – Toggle selection improvements.<i goTo="LaborMisses"></i>

<body>

<h4>Please Note:</h4>

<span>These enhancements listed above are a small sample of what changed in this new build. Thank you, and we hope you enjoy this new version.</span>

</body>

</div>
</Grid>

## <h1>Version 1.5.0 Release Date-12/23/22</h1>

<Grid>
<div>

### <span>Enhancements</span>

- Pay Type Tab (in Reference / Setups) – CDK clients now have multiple edit capabilities.<i goTo="PayTypeMaster"></i>
- Labor/Parts Workmix - Op Code Summary & Op Code Detailed View to display all Opcodes irrespective of payment method.<i goTo="LaborWorkMixAnalysis"></i>
- Labor Grid Misses and Parts Misses - Toggle feature to view all jobs for a given period or non-compliant jobs only for a given period.<i goTo="LaborMisses"></i>
- Special Metrics - CP 1-Line RO Count and CP Multiline RO Count charts - Feature to look at RO Counts by mileage.<i goTo="SpecialMetrics"></i>

</div>
</Grid>

## <h1>Version 1.4.0 Release Date-12/08/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Migration to SIMT Interface (not client-facing).<i goTo="Home"></i>
- Opcode Categorization – Updates to Verbiage.<i goTo="OPcodes"></i>
- KPI Score Card – Order of Grid Types displayed on Toggle Tooltip and within Reference & Set-Ups.<i goTo="Home"></i>
- Chart Master – Instant Updates to Chart Titles post edit.<i goTo="ChartMaster"></i>
- Reference & Setups - Labor Grid & Parts Matrix - Added Store Install Date and FOPC Calculated Date From.<i goTo="LaborGridPricing"></i>
- Opcode Categorization Tab – All Opcodes are now hyperlinked to the Opcode Summary.<i goTo="OPcodes"></i>
- Opcode Summary & Opcode Detailed View – Complete Opcode history now matches the Opcode Categorization tab.<i goTo="OPcodes"></i>
- Rename chart 1194 - Pre-paid Maintenance Plans.<i goTo="CPOverview"></i>
- Edit Service Advisor – Alert message on save changes removed.<i goTo="ServiceAdvisors"></i>
- Reference & Setups – Pay Types – Rename columns to Dealer Pay Type and FOPC Pay Type Group.<i goTo="PayTypeMaster"></i>
- Opcode Categorization – When an Opcode is clicked on it Hyperlinks to the Opcode Summary page.<i goTo="OPcodes"></i>

</div>

</Grid>

## <h1>Version 1.3.0 Release Date-10/28/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- KPI Score Card - Dynamic Radio Buttons for Labor and Parts compliance.<i goTo="Home"></i>
- In KPI reports, KPI goal settings - Add all grid types.<i goTo="KpiReport"></i>
- Grid non-compliance drilldown – Page improvements.<i goTo="LaborMisses"></i>
- Search By RO - Rename Service Request to “Request / Story / Cause” and adjust column sizing.<i goTo="SearchByRO"></i>
- "Request / Story / Cause” - Tooltip display if any data present.<i goTo="CPOverview"></i>
- KPI Goal Settings – Multiple columns renamed.<i goTo="ScoreCardGoalSetting"></i>
- Add multiple opcode save functionality before reloading data.<i goTo="OPcodes"></i>
- Make op code refresh store specific.<i goTo="OPcodes"></i>
- Add op code refresh page UI improvements.<i goTo="OPcodes"></i>

</div>

</Grid>

## <h1>Version 1.2.1 Release Date-10/18/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Grid filter updates in all drill-downs.<i goTo="CPOverview"></i>
- Display totals on the top - Work Mix - Op Code Detailed View , Tech detailed View, and Service Advisor Detailed View.<i goTo="LaborWorkMixAnalysis"></i>
- Abbreviated Work Mix 2 Month Comparison charts Op Category.<i goTo="LaborWorkMixAnalysis"></i>
- Icon updates for Nav Menu items.<i goTo="CPOverview"></i>
- Rename MPI Opcodes and Menu Opcodes to Opcodes - MPI and Opcodes - Menu.<i goTo="MPIOpcode"></i>
- Nav Menu improvements for Advisor Metrics, Tech Metrics, Discount & Special Metrics.<i goTo="ServiceAdvisorPerformance"></i>

</div>

</Grid>

## <h1>Version 1.2.0 Release Date-10/06/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Added Multiple Opcode Update Capabilities with Full Database Recalculation Functionality.<i goTo="OPcodes"></i>
- Labor Grid/Parts Matrix - Grid Period and Matrix Period Drop Down (Current, Prior, Prior 1, etc.) and Grid Type / Matrix Type Dropdown (Customer, Internal, etc.).<i goTo="LaborGridPricing"></i>
- Repair – Labor Grid Misses: Added Tech Names as an additional column.<i goTo="LaborMisses"></i>
- Repair – Parts Matrix Misses: Added Matrix Install Date, Parts, and Cost Additional Columns.<i goTo="PartsMisses"></i>

</div>

</Grid>

## <h1>Version 1.1.3 Release Date-09/30/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Several Labor Grid(s) and Parts Matrix(s) screen improvements.<i goTo="LaborGridPricing"></i>
- Hyperlink Added to Door Rate and Matrix Install date that Redirects the user to the appropriate tabs in the “Reference /Setups” section for viewing.<i goTo="Glossary"></i>
- Add Part Numbers to Parts Target Misses and to the Search By RO page.<i goTo="PartsMisses"></i>

</div>

</Grid>

## <h1>Version 1.1.2 Release Date-09/23/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Added Tooltips to Opcode Descriptions on 2-Month Work Mix comparison charts.<i goTo="LaborWorkMixAnalysis"></i>
- Other Labor/Parts Work Mix - Remove Parts/Labor Drop Down as this was redundant.<i goTo="WorkMixVolume"></i>
- Repair – Labor Target Misses: Add Grid Door Install Date, 1 Hour Door Rate, Matrix Install Date, and Opcodes with Descriptions as additional columns.<i goTo="LaborMisses"></i>
- Export Option has been Added for Labor Grid Misses and Parts Matrix Misses.<i goTo="LaborMisses"></i>

</div>

</Grid>

## <h1>Version 1.1.1 Release Date-09/14/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Add store group name to topbar.<i goTo="CPOverview"></i>
- Add search by RO improvements.<i goTo="CPOverview"></i>
- Add labor and parts misses improvements.<i goTo="LaborMisses"></i>

</div>

</Grid>

## <h1>Version 1.1.0 Release Date-09/07/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Add store settings feature.<i goTo="StoreSettings"></i>
- Add KPI score card goal settings page.<i goTo="ScoreCardGoalSetting"></i>
- Add KPI report UI improvements based on All advisor selection from the dropdown.<i goTo="KpiReport"></i>

</div>

</Grid>

## <h1>Version 1.0.7 Release Date-08/29/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Add KPI Duration Dropdown design Changes.<i goTo="KpiReport"></i>
- Add Labor /Parts Misses - Reduce font size of duration and misses dropdowns.Reduce box size.<i goTo="LaborMisses"></i>
- Add Date formats on the toggle - KPI Score card.<i goTo="Home"></i>
- Add CP and Internal Misses Toggle Dropdown in Labor and Parts Misses.<i goTo="LaborMisses"></i>
- Icons on score card get distorted for different resolutions.<i goTo="Home"></i>

</div>

</Grid>

## <h1>Version 1.0.6 Release Date-08/24/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Add Changelog improvements.<i goTo="ChangeLog"></i>
- Add navbar changes.<i goTo="CPOverview"></i>
- Add KPI internal toggle updates.<i goTo="Home"></i>

</div>

</Grid>

## <h1>Version 1.0.5 Release Date-08/17/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Add KPI Scorecard - Internal Grids toggle.<i goTo="Home"></i>
- Add Last 3 months and Last 12 months toggle in KPI Score Card, KPI Report , Labor and Parts Misses.<i goTo="Home"></i>
- Add Change Log improvements.<i goTo="ChangeLog"></i>
- Error Handling - React JS.<i goTo="CPOverview"></i>
- Relabel \$Diff as “Net Difference” and change its position in Labor and Parts Misses.<i goTo="LaborMisses"></i>
- At 100% Chrome page view site, Compliance should be fully visible without needing to scroll to the right in Labor and Parts Misses.<i goTo="LaborMisses"></i>

</div>

</Grid>

## <h1>Version 1.0.4 Release Date-08/05/22</h1>

<Grid >

<div>

### <span>Enhancements</span>

- Single select Service Advisor Dropdown in KPI Report.<i goTo="ScoreCardGoalSetting"></i>
- Remove Multiple advisor selection option in KPI report.<i goTo="ScoreCardGoalSetting"></i>
- Search RO Updates : Opcode description as tooltip on the Opcode , Remove Job Description. Change Description column to Service Request - the tool tip should have Service Request, Customer Story, Cause.<i goTo="SearchByRO"></i>
- Add - Multiple dots shown for same advisor selection.<i goTo="CPOverview"></i>
- Add - 'All' shown as dot in advisor drop down.<i goTo="CPOverview"></i>
- Add KPI report page responsive issues.<i goTo="KpiReport"></i>

</div>

</Grid>

## <h1>Version 1.0.3 Release Date-08/02/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Support & Feedback updates.<i goTo="CPOverview"></i>
- Menu Opcodes & MPI Opcodes UI.<i goTo="MPIOpcode"></i>
- Changelog report.<i goTo="ChangeLog"></i>
- KPI report dashboard and PDF.<i goTo="KpiReport"></i>
- Labor Grid and Parts Matrix Misses - Add Active Grid Used, Active Matrix Dated,Labor Sold Hour column for labor misses and Parts cost for Parts misses and Variance column.<i goTo="LaborMisses"></i>
- Opcode Detailed View - Color Code the variance.<i goTo="LaborWorkMixAnalysis"></i>
- Add Sum Totals on the top of Labor and Parts Misses.<i goTo="LaborMisses"></i>
- Search By RO - Add Variance column and Highlight them and add drilldown to Opcode summary for Opcode click.<i goTo="SearchByRO"></i>
- Return Rate Drill Down updates.<i goTo="SpecialMetrics"></i>
- Improve service advisor dropdown height.<i goTo="CPOverview"></i>
- Navbar improvements.<i goTo="CPOverview"></i>
- Expanded view of the charts are not working properly.<i goTo="CPOverview"></i>
- Data in Filter box is not visible.<i goTo="CPOverview"></i>
- Technician list is showing blank.<i goTo="TechnicianPerformance"></i>
- Edit and Save functionality in the goal under opportunity is not functioning properly.<i goTo="LaborGrossAndVolumeOpportunity"></i>
- (Security Issue) - Client and user having access to modules having admin privilege.<i goTo="CPOverview"></i>
- 'Data as of' is missing in Discount metrics.<i goTo="Discounts"></i>
- When we remove ELR chart from favorite, CP revenue chart displays the data of ELR in it.<i goTo="MyFavorites"></i>

</div>

</Grid>

## <h1>Version 1.0.2 Release Date-07/18/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- RO Search - Add Variance to REPAIR jobs - Labor & Parts. Highlight in red for non- compliant jobs.<i goTo="SearchByRO"></i>
- Toggle duration - Consider UTC Time for Cron Jobs & Emails.<i goTo="CPOverview"></i>
- Parts Matrix - Remove Target Rate Label.<i goTo="PartsMatrixPricing"></i>
- Master Login - Keycloak configurations.<i goTo="CPOverview"></i>
- Advisor metrics - Month issue for Category By Month tab.<i goTo="ServiceAdvisorPerformance"></i>
- Multi-Line RO Count and Percentage Charts.<i goTo="SpecialMetrics"></i>
- Special Metrics - Return Rate Drill down updates.<i goTo="SpecialMetrics"></i>
- Parts Matrix drilldown correction and comma seperation in opportunity.<i goTo="PartsMatrixPricing"></i>
- KPI numbering.<i goTo="Home"></i>

</div>

</Grid>

## <h1>Version 1.0.1 Release Date-07/04/22</h1>

<Grid >
<div>

### <span>Enhancements</span>

- Add app version in login page and footer.<i goTo="Home"></i>
- Add variance column in Labor and Parts Grid Misses.<i goTo="LaborMisses"></i>
- Add UI Enhancement mail changes dated - 6/16 & 6/17.<i goTo="Home"></i>
- Add only non compliant ROs in Labor & Parts Grids.<i goTo="LaborMisses"></i>
- Change KPI Score Card - Labor & Parts Misses - View Misses for drill down tooltip.<i goTo="Home"></i>
- Remove Filters and Show only toggles in Labor and Parts Misses.<i goTo="LaborMisses"></i>
- Add Service Advisor Filter for Misses tabs - maintain state between KPI Score card and these pages.<i goTo="Home"></i>
- Add Labor and Parts Misses changes As per Mail dated - 6/27 - 6/28.<i goTo="LaborMisses"></i>
- Add Login Details Email.<i goTo="Home"></i>
- Add Advisor names on the charts-Discount Metrics - By Service Advisor tab.<i goTo="Discounts"></i>
- Add KPI to Special Metrics Link.<i goTo="SpecialMetrics"></i>
- Add filter state maintenance for all Drilldowns, Detailed Views, Summary and Labor and Parts Misses.<i goTo="LaborMisses"></i>
- Add Target Price and Variance in Search By RO.<i goTo="SearchByRO"></i>
- Add Back Button on the Labor Grid Misses and Parts Grid misses pages.<i goTo="LaborMisses"></i>
- Add Sorting issue in Labor and Parts Misses improvements.<i goTo="LaborMisses"></i>
- Add Support & Feedback Improvements.<i goTo="CPOverview"></i>

</div>

</Grid>

## <h1>Version 1.0.0 Release Date-06/18/22</h1>

<Grid >
<div>

### <span>Features</span>

- KPI Score Card.<i goTo="Home"></i>
- CP Summary Overview.<i goTo="CPOverview"></i>
- CP Labor Overview.<i goTo="CPLaborOverview"></i>
- Labor WorkMix.<i goTo="LaborWorkMixAnalysis"></i>
- Scatter Plot - Labor - Jobs / Hours / ELR.<i goTo="LaborItemization"></i>
- “What If” Opportunity - Hrs Per RO & Labor GP%.<i goTo="LaborGrossAndVolumeOpportunity"></i>
- “What If” Opportunity - Effective Labor Rate.<i goTo="CPELROpportunity"></i>
- CP Parts Overview.<i goTo="CPPartsOverview"></i>
- Parts WorkMix.<i goTo="PartsWorkMixAnalysis"></i>
- Scatter Plot - Parts - Cost / Jobs / Markup.<i goTo="PartsItemization"></i>
- “What If” Opportunity - Hrs Per RO & Parts GP%.<i goTo="PartsGrossAndVolumeOpportunity"></i>
- Advisor Metrics.<i goTo="ServiceAdvisorPerformance"></i>
- Tech Metrics.<i goTo="TechnicianPerformance"></i>
- Discount Metrics.<i goTo="Discounts"></i>
- Special Metrics.<i goTo="SpecialMetrics"></i>
- Reference & Setup.<i goTo="Glossary"></i>
- Armatus Admin.<i goTo="KeycloakUserCreation"></i>

</div>

</Grid>
