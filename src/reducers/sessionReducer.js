import * as actionTypes from 'src/actions';

const initialState = {
  stoveValue: [localStorage.getItem('selectedStoreId')],
  dashboardReloadStatus: false,
  serviceAdvisor: ['All'],
  loggedIn: true,
  prevPath: '',
  currentPath: '',
  token: '',
  menuSelected: '',
  favouriteCharts: [],
  favouriteChartsComparisonMonth: [],
  userName: '',
  payTypeInitialTab: '',
  storeSelected: '',
  storeName: '',
  storeId: '',
  userId: '',
  navItemStatus: false,
  navItems: ['Home'],
  payTypeErrors: false,
  advisorErrors: false,
  payTypeErrorsCount: 0,
  searchByRo: false,
  drillDownCount: false,
  refreshStatus: false,
  reloadStatus: false,
  itemizationReload: false,
  advisorWithId: [],
  editedOpcodes: [],
  hiddenRowsKPI: [],
  selAdvs: [],
  selStores: [],
  sortOrder: {
    row: '',
    order: 0
  },
  hiddenRowsKPIStore: [],
  sortOrderStore: {
    row: '',
    order: 0
  },
  hiddenKpisStore: [],
  refreshErrorStatus: false,
  kpiAdvisor: ['All'],
  toggleStatus: false,
  selectedTech: 'All[All]',
  kpiToggle: localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH',
  allErrorsCount: 0,
  internalKpiToggle: 'C',
  historyStatus: 'Advisor',
  technician: ['All'],
  allAdvisorNames: [],
  allAdvisorIds: [],
  allLaborGridTypes: ['Customer'],
  allPartsMatrixTypes: ['Customer'],
  activeAdvisors: [],
  hiddenKpis: [],
  hiddenAdvisors: [],
  selectedAdvisors: [],
  selectedStores: [],
  dateData: [],
  kpiDataAll: [],
  activeTechs: [],
  retailFlag: [],
  kpiHomeToggle: localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH',
  kpiToggleStartDate: localStorage.getItem('kpiStartDate'),
  kpiToggleEndDate: localStorage.getItem('kpiFilterEndDate'),
  kpiGraphicsToggleStartDate: localStorage.getItem('kpiStartDate'),
  kpiGraphicsToggleEndDate: localStorage.getItem('kpiFilterEndDate'),
  technicanSelected: '',
  versionFlag: localStorage.getItem('versionFlag')
    ? localStorage.getItem('versionFlag')
    : 'FALSE',
  mpiFlag: false,
  menuFlag: false,
  user: {
    first_name: 'Shen',
    last_name: 'Zhi',
    email: '<EMAIL>',
    avatar: '/images/avatars/avatar_11.png',
    bio: 'Brain Director',
    role: 'ADMIN' // ['GUEST', 'USER', 'ADMIN']
  },
  currentUser: ''
};
const sessionReducer = (state = initialState, action) => {
  switch (action.type) {
    case actionTypes.SESSION_LOGIN: {
      return {
        ...initialState
      };
    }

    case actionTypes.SESSION_LOGOUT: {
      return {
        ...state,
        loggedIn: false,
        user: {
          role: 'GUEST'
        }
      };
    }
    case actionTypes.SESSION_LOGOUT: {
      return {
        ...state,
        loggedIn: false,
        user: {
          role: 'GUEST'
        }
      };
    }
    case actionTypes.SET_NAV_ITEMS: {
      return {
        ...state,
        navItems: action.payload
      };
    }
    case actionTypes.SET_NAV_ITEM_STATUS: {
      return {
        ...state,
        navItemStatus: action.payload
      };
    }
    case actionTypes.SET_SERVICE_ADVISOR: {
      return {
        ...state,
        serviceAdvisor: action.payload
      };
    }
    case actionTypes.SET_STORE_VALUE: {
      return {
        ...state,
        storeValue: action.payload
      };
    }
    case actionTypes.SET_RONUMBER: {
      return {
        ...state,
        roNumber: action.payload
      };
    }
    case actionTypes.SET_PREV_PATH: {
      return {
        ...state,
        prevPath: action.payload
      };
    }
    case actionTypes.SET_CURRENT_PATH: {
      return {
        ...state,
        currentPath: action.payload
      };
    }
    case actionTypes.SET_ALL_FAVOURITES: {
      return {
        ...state,
        favouriteCharts: action.payload
      };
    }
    case actionTypes.SET_ALL_FAVOURITES_COMPARISON_MONTHS: {
      return {
        ...state,
        favouriteChartsComparisonMonth: action.payload
      };
    }
    case actionTypes.SET_TOKEN: {
      return {
        ...state,
        token: action.payload
      };
    }
    case actionTypes.SET_USERNAME: {
      return {
        ...state,
        userName: action.payload
      };
    }
    case actionTypes.SET_STOREID: {
      return {
        ...state,
        storeSelected: action.payload
      };
    }
    case actionTypes.SET_PAY_TYPE_TAB: {
      return {
        ...state,
        payTypeInitialTab: action.payload
      };
    }
    case actionTypes.SET_DATE_DATA: {
      return {
        ...state,
        dateData: action.payload
      };
    }
    case actionTypes.SET_PAY_TYPE_ERROR: {
      return {
        ...state,
        payTypeErrors: action.payload
      };
    }
    case actionTypes.SET_PAY_TYPE_ERROR_COUNT: {
      return {
        ...state,
        payTypeErrorsCount: action.payload
      };
    }
    case actionTypes.SET_OPCODE_ERROR: {
      return {
        ...state,
        opcodeErrors: action.payload
      };
    }
    case actionTypes.SET_OPCODE_ERROR_COUNT: {
      return {
        ...state,
        opcodeErrorsCount: action.payload
      };
    }
    case actionTypes.SET_SEARCH: {
      return {
        ...state,
        searchByRo: action.payload
      };
    }
    case actionTypes.SET_DRILL_DOWN_COUNT: {
      return {
        ...state,
        drillDownCount: action.payload
      };
    }
    case actionTypes.SET_REFRESH_STATUS: {
      return {
        ...state,
        refreshStatus: action.payload
      };
    }
    case actionTypes.SET_RELOAD_STATUS: {
      return {
        ...state,
        reloadStatus: action.payload
      };
    }
    case actionTypes.SET_DASHBOARD_RELOAD_STATUS: {
      return {
        ...state,
        dashboardReloadStatus: action.payload
      };
    }
    case actionTypes.SET_REFRESH_ERROR_STATUS: {
      return {
        ...state,
        refreshErrorStatus: action.payload
      };
    }
    case actionTypes.SET_ADVISOR_STATUS: {
      return {
        ...state,
        advisorStatusChanged: action.payload
      };
    }
    case actionTypes.SET_ADVISOR_WITHID: {
      return {
        ...state,
        advisorWithId: action.payload
      };
    }
    case actionTypes.SET_MENU_SELECTED: {
      return {
        ...state,
        menuSelected: action.payload
      };
    }
    case actionTypes.SET_KPI_REPORT_ADVISOR: {
      return {
        ...state,
        kpiAdvisor: action.payload
      };
    }
    case actionTypes.SET_KPI_TOGGLE: {
      return {
        ...state,
        kpiToggle: action.payload
      };
    }
    case actionTypes.SET_INTERNAL_KPI_TOGGLE: {
      return {
        ...state,
        internalKpiToggle: action.payload
      };
    }
    case actionTypes.SET_HISTORY_STATUS: {
      return {
        ...state,
        historyStatus: action.payload
      };
    }
    case actionTypes.SET_ADVISOR_NICK_NAME: {
      return {
        ...state,
        advisorNickNameChanged: action.payload
      };
    }

    case actionTypes.SET_ADVISOR_ERRORS: {
      return {
        ...state,
        advisorErrors: action.payload
      };
    }
    case actionTypes.SET_ADVISOR_ERROR_COUNT: {
      return {
        ...state,
        advisorErrorsCount: action.payload
      };
    }
    case actionTypes.SET_TECH_ERRORS: {
      return {
        ...state,
        techErrors: action.payload
      };
    }
    case actionTypes.SET_TECH_ERROR_COUNT: {
      return {
        ...state,
        techErrorsCount: action.payload
      };
    }
    case actionTypes.SET_DAILY_IMPORT_ERROR: {
      return {
        ...state,
        dailyImportErrors: action.payload
      };
    }
    case actionTypes.SET_DAILY_IMPORT_ERROR_COUNT: {
      return {
        ...state,
        dailyImportErrorsCount: action.payload
      };
    }
    case actionTypes.SET_ALL_ERROR_COUNT: {
      return {
        ...state,
        allErrorsCount: action.payload
      };
    }
    case actionTypes.SET_TECHNICIAN: {
      return {
        ...state,
        technician: action.payload
      };
    }
    case actionTypes.SET_ALL_ADVISOR_NAMES: {
      return {
        ...state,
        allAdvisorNames: action.payload
      };
    }
    case actionTypes.SET_ACTIVE_ADVISORS: {
      return {
        ...state,
        activeAdvisors: action.payload
      };
    }
    case actionTypes.SET_ALL_ADVISOR_IDS: {
      return {
        ...state,
        allAdvisorIds: action.payload
      };
    }
    case actionTypes.SET_LABOR_GRID_TYPES: {
      return {
        ...state,
        allLaborGridTypes: action.payload
      };
    }
    case actionTypes.SET_PARTS_MATRIX_TYPES: {
      return {
        ...state,
        allPartsMatrixTypes: action.payload
      };
    }
    case actionTypes.SET_USER_DETAILS: {
      return {
        ...state,
        userId: action.payload
      };
    }
    case actionTypes.SET_KPI_HOME_TOGGLE: {
      return {
        ...state,
        kpiHomeToggle: action.payload
      };
    }
    case actionTypes.SET_NEW_ADVISOR_ERROR: {
      return {
        ...state,
        newAdvisorErrors: action.payload
      };
    }
    case actionTypes.SET_NEW_ADVISOR_ERROR_COUNT: {
      return {
        ...state,
        newAdvisorCount: action.payload
      };
    }
    case actionTypes.SET_KPI_HOME_TOGGLE: {
      return {
        ...state,
        kpiHomeToggle: action.payload
      };
    }
    case actionTypes.SET_NEW_PAYTYPE_ERROR: {
      return {
        ...state,
        newPaytypeErrors: action.payload
      };
    }
    case actionTypes.SET_NEW_PAYTYPE_ERROR_COUNT: {
      return {
        ...state,
        newPaytypeCount: action.payload
      };
    }
    case actionTypes.SET_TOGGLE_STATUS: {
      return {
        ...state,
        toggleStatus: action.payload
      };
    }
    case actionTypes.SET_TECHNICIAN_STATUS: {
      return {
        ...state,
        techStatusChanged: action.payload
      };
    }
    case actionTypes.SET_ACTIVE_TECHS: {
      return {
        ...state,
        activeTechs: action.payload
      };
    }
    case actionTypes.SET_TECHNICIAN_SUMMARY: {
      return {
        ...state,
        technicanSelected: action.payload
      };
    }
    case actionTypes.SET_SELECTED_TECH: {
      return {
        ...state,
        selectedTech: action.payload
      };
    }
    case actionTypes.SET_SORT_ORDER: {
      return {
        ...state,
        sortOrder: action.payload
      };
    }
    case actionTypes.SET_HIDDEN_KPI_ROWS: {
      return {
        ...state,
        hiddenKpis: action.payload
      };
    }
    case actionTypes.SET_SORT_ORDER_STORE: {
      return {
        ...state,
        sortOrderStore: action.payload
      };
    }
    case actionTypes.SET_HIDDEN_KPI_ROWS_STORE: {
      return {
        ...state,
        hiddenKpisStore: action.payload
      };
    }
    case actionTypes.SET_KPI_TOGGLE_START_DATE: {
      return {
        ...state,
        kpiToggleStartDate: action.payload
      };
    }
    case actionTypes.SET_KPI_TOGGLE_END_DATE: {
      return {
        ...state,
        kpiToggleEndDate: action.payload
      };
    }
    case actionTypes.SET_KPIGRAPHICS_TOGGLE_START_DATE: {
      return {
        ...state,
        kpiGraphicsToggleStartDate: action.payload
      };
    }
    case actionTypes.SET_KPIGRAPHICS_TOGGLE_END_DATE: {
      return {
        ...state,
        kpiGraphicsToggleEndDate: action.payload
      };
    }
    case actionTypes.SET_SEL_ADVISORS: {
      return {
        ...state,
        selectedAdvisors: action.payload
      };
    }
    case actionTypes.SET_HID_ADVISORS: {
      return {
        ...state,
        hiddenAdvisors: action.payload
      };
    }
    case actionTypes.SET_SEL_STORES: {
      return {
        ...state,
        selectedStores: action.payload
      };
    }

    case actionTypes.SET_VERSION_FLAG: {
      return {
        ...state,
        versionFlag: action.payload
      };
    }
    case actionTypes.SET_MPI_FLAG: {
      return {
        ...state,
        mpiFlag: action.payload
      };
    }
    case actionTypes.SET_MENU_FLAG: {
      return {
        ...state,
        menuFlag: action.payload
      };
    }
    case actionTypes.SET_NEW_MODEL_ERROR_COUNT: {
      return {
        ...state,
        modelErrorCount: action.payload
      };
    }
    case actionTypes.SET_NEW_MODEL: {
      return {
        ...state,
        modelError: action.payload
      };
    }
    case actionTypes.SET_SEL_ADV: {
      return {
        ...state,
        selAdvs: action.payload
      };
    }
    case actionTypes.SET_SEL_STORE: {
      return {
        ...state,
        selStores: action.payload
      };
    }
    case actionTypes.KPI_DATA: {
      return {
        ...state,
        kpiDataAll: action.payload
      };
    }
    case actionTypes.CURRENT_USER: {
      return {
        ...state,
        currentUser: action.payload
      };
    }
    case actionTypes.RETAIL_FLAG: {
      return {
        ...state,
        retailFlag: action.payload
      };
    }
    default: {
      return state;
    }
  }
};

export default sessionReducer;
