import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  Button,
  Tooltip
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { getNotificationsUserLoginHistoryStatus } from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import { getLast13Months } from 'src/utils/Utils';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import { addDays } from 'date-fns';
import { withKeycloak } from '@react-keycloak/web';
import { ReactSession } from 'react-client-session';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import Link from '@material-ui/core/Link';
import ExportIcon from '@material-ui/icons/GetApp';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class UserLoginHistory extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData( this.state.value ? this.state.value : new Date());
    //     this.setState({ value: this.state.value ? this.state.value : new Date() });
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData(this.state.value ? this.state.value : new Date());
        this.setState({
          value: this.state.value ? this.state.value : new Date()
        });
      }
    }
  }
  constructor(props) {
    super(props);

    var initialQueryMonth = this.props.months[0].monthYear;
    var location =
      getVerificationDashboardBaseURL() + '/FOC3_Searchbyro/ag-grid.html';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      queryMonth: initialQueryMonth,
      selectedMonthYear: initialQueryMonth,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: ['All'],
      isLoading: true,
      isRefresh: false,
      rawGridApi: {},
      gridApi: {},
      tabSelection: 0,
      headerHeight: 45,
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      monthYear: this.props.months,
      startDate: '',
      endDate: '',
      value: new Date(),
      columnDefs: [
        {
          headerName: 'User Name',
          chartDataType: 'series',
          // width: 140,
          minWidth: 140,
          field: 'userName',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          flex: 1,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          comparator: (valueA, valueB) => {
            // Case-insensitive sorting logic
            if (valueA && valueB) {
              return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
            }
            return 0;
          }
        },
        {
          headerName: 'Role',
          chartDataType: 'series',
          // width: 150,
          minWidth: 150,
          field: 'roleName',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          flex: 1,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Login Date',
          // chartDataType: 'category',
          field: 'logindate',
          // width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          editable: false,
          flex: 1,
          // aggFunc: this.nulValue,
          // onCellClicked: this.handleSearchByRo,
          cellStyle() {
            return {
              border: ' 0px white',
              textAlign: 'left'
            };
          }
        },
        {
          headerName: 'Login Time(EST)',
          // chartDataType: 'category',
          field: 'logintime',
          // width: 110,
          minWidth: 110,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          editable: false,
          flex: 1,
          // aggFunc: this.nulValue,
          // onCellClicked: this.handleSearchByRo,
          cellStyle() {
            return {
              border: ' 0px white',
              textAlign: 'left'
            };
          }
        },
        {
          headerName: 'Logout Time(EST)',
          // chartDataType: 'category',
          field: 'logouttime',
          // width: 110,
          minWidth: 110,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          editable: false,
          flex: 1,
          // aggFunc: this.nulValue,
          // onCellClicked: this.handleSearchByRo,
          cellStyle() {
            return {
              border: ' 0px white',
              textAlign: 'left'
            };
          }
        },
        {
          headerName: 'Duration',
          chartDataType: 'series',
          // width: 150,
          minWidth: 150,
          field: 'loginDuration',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          flex: 1,
          // tooltipField: 'loginDuration',
          cellStyle() {
            // return { border: ' 0px white', textAlign: 'left' };
          }
        }
      ],
      groupIncludeTotalFooter: true,
      rowData: [],
      overlayNoRowsTemplate:
        '<span style="padding: 10px;margin-top:50px; ">No rows to show</span>',
      defaultColDef: {
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
        // editable: true
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/DD/YY');
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/YY');
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        (
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString()
      );
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        Math.abs(
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
      );
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };

  onGridReady = params => {
    // params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridcolumnApi: params.columnApi });

    // this.gridApi.sizeColumnsToFit();
    this.getAgGridData(this.state.value);
  };

  getAgGridData(statusDate) {
    var startDate = moment(statusDate).format('YYYY-MM-DD');
    let url = window.location.href;
    var arr = url.split('/');
    const settings = { ...process.env };

    if (process.env.REACT_APP_PRODUCTION == 'false') {
      var env =
        'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('-')[0].toUpperCase();
    } else {
      var env =
        'REACT_APP_KEYCLOAK_REALM_' + arr[2].split('.')[0].toUpperCase();
    }
    this.setState({ isLoading: true });
    const realmid = settings[env];
    const timezoneOffset = '+05:30';
    var enddate = moment(statusDate).format('YYYY-MM-DD');
    var startdate = moment(Date.now() - 29 * 24 * 3600 * 1000).format(
      'YYYY-MM-DD'
    );
    getNotificationsUserLoginHistoryStatus(
      enddate,
      startdate,
      realmid,
      timezoneOffset,
      result => {
        this.setState({ isLoading: false });
        console.log('result=', result);
        if (
          result.data
            .statelessCcSupportAndNotificationsGetUserLoginHistoryStatusDaterange
            .statelessCcSupportAndNotificationsUserLoginHistoryStatuses
        ) {
          var resultArr =
            result.data
              .statelessCcSupportAndNotificationsGetUserLoginHistoryStatusDaterange
              .statelessCcSupportAndNotificationsUserLoginHistoryStatuses;
          this.setState({
            rowData: resultArr
          });
        }
      }
    );
  }

  nulValue = () => {
    return '';
  };

  setDrillDownValuesToState = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      LsWarranty: Values.LsWarranty
    });
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
    this.setState({ category: newValue });
    let type = '';
    if (newValue == 0) {
      type = 'discount_drilldown';
    } else {
      type = 'discounted_parts_drilldown';
    }
    this.setState({
      month_year: this.state.queryMonth,
      chartId: this.props.chartId,
      type: this.props.type
    });
    this.getAgGridData(this.state.value);
    let data = {
      month_year: this.state.queryMonth,
      type: type,
      chartId: this.props.chartId
    };
    this.props.parentCallback(data);
  };
  getMonthYear = () => {
    var table = [];
    this.setState({
      discountMonthYear: getLast13Months()
    });
  };
  renderBackButton = () => {
    {
      let chartId = this.props.chartId
        ? this.props.chartId
        : this.props.history.location.search.split('=')[1];
      let data = {
        month_year: this.state.queryMonth,
        type: '',
        chartId: this.props.chartId,
        history: this.props.history,
        prevPath: this.state.previousLocation,
        drillDown:
          chartId == 1111
            ? 41
            : chartId == 1115
            ? 42
            : chartId == 1232
            ? 45
            : chartId == 1165
            ? 44
            : 43
      };
      this.props.parentCallback(data);
    }
  };
  handleSearchByRo = params => {
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        queryMonth: this.state.queryMonth,
        pageType: 'WarrantyRates_' + this.props.drillDownType
      }
    });
  };
  externalFilterChanged = () => {
    //ageType = 'test';

    this.getAgGridData(this.state.value);
    this.state.rawGridApi.onFilterChanged();
  };
  dateChanage = e => {
    this.setState({ value: e });
    this.getAgGridData(e);
    this.state.rawGridApi.onFilterChanged();
  };
  handleMonthChange = async event => {
    await this.setState({
      queryMonth: ''
    });
    this.setState({
      queryMonth: event.target.value
    });
    var startDate = moment(new Date(event.target.value + '-01')).format(
      'YYYY-MM-DD'
    );
    var endDate = moment(new Date(event.target.value + '-01'))
      .clone()
      .endOf('month')
      .format('YYYY-MM-DD');
    await this.setState({
      value: [startDate, endDate]
    });
    this.externalFilterChanged();
  };
  isExternalFilterPresent = () => {
    return this.state.value;
  };

  doesExternalFilterPass = node => {
    if (node.alreadyRendered == false) {
      if (this.state.value != '') {
        let filteredUsers = this.state.rowData.filter(
          user =>
            moment(this.state.value).format('YYYY-MM-DD') == user.logindate
        );
        this.setState({
          rowData: filteredUsers
        });

        return (
          node.data.logindate == moment(this.state.value).format('YYYY-MM-DD')
        );
      }
    }
  };
  resetReportGrid = () => {
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
    this.setState({
      queryMonth: this.state.queryMonth
    });
    window.sortState = {};
    this.state.gridcolumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'UserLoginHistory',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'User Login History' },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: params => {
        if (params.column.getColId() === 'logindate') {
          if (params.value != null && params.value != 0) {
            return moment(params.value).format('MM/DD/YY');
          }
        }
        return params.value;
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: { size: 18, bold: true }
        },
        {
          id: 'dateFormat',
          dataType: 'DateTime',
          numberFormat: { format: 'mm/dd/yy' }
        }
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  render() {
    const { classes } = this.props;

    return (
      <div>
        <Paper square style={{ margin: 8 }}>
          <Tabs
            value={this.state.tabSelection}
            //onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
            style={{ pointerEvents: 'none' }}
          >
            <Tab
              style={{
                textTransform: 'none',
                paddingRight: 182,
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
              label={<div>User Login History</div>}
              value="one"
            />
          </Tabs>
        </Paper>

        <Grid container spacing={12}>
          <Grid item xs={12} style={{ padding: '8px' }}>
            <Paper square justify="center">
              <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <Typography
                  style={{
                    fontWeight: 'bold',
                    color: '#003d6b',
                    fontSize: 13,
                    marginTop: 4
                  }}
                >
                  This displays results from the last 30 days only.{' '}
                </Typography>
                {/* <Typography style={{ fontSize: 12 }} sx={{ mt: 2, mb: 1 }}>
                  Select Date
                  <Typography
                    style={{
                      // fontWeight: 'bold',
                      color: '#003d6b',
                      fontSize: 10
                    }}
                  >
                    &nbsp;*Results available for dates within the last 10 days only.
                    {/* This displays the results of the last 10 data downloads.{' '} */}
                {/* </Typography>
                </Typography> */}
                {/* <DatePicker
                  selected={this.state.value}
                  className={classes.reactDaterangePicker}
                  onChange={this.dateChanage}
                  maxDate={new Date()}
                  minDate={addDays(new Date(), -9)}
                  dateFormat="MM/dd/yy"
                /> */}
              </FormControl>

              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.back, 'reset-btn')}
                onClick={this.resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{
                    marginRight: 10,
                    float: 'right',
                    marginTop: 8,
                    cursor: 'pointer'
                  }}
                  onClick={this.onBtExport}
                >
                  <ExportIcon />
                </Link>
              </Tooltip>
            </Paper>
          </Grid>
        </Grid>
        {this.state.isLoading && (
          <div
          // style={{
          //   display: this.state.tabSelection != 1 ? 'none' : 'block'
          // }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          // id="data-tab-user-login"
          className="ag-theme-balham"
          style={{
            height: window.innerHeight - 218 + 'px',
            width: '800px',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              width: '100%',
              height: '410px'
            }}
            // domLayout="autoHeight"
            // isExternalFilterPresent={this.isExternalFilterPresent}
            // doesExternalFilterPass={this.doesExternalFilterPass}
            floatingFilter={true}
            defaultColDef={this.state.defaultColDef}
            headerHeight={this.state.headerHeight}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            // suppressRowClickSelection={true}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
            excelStyles={this.state.excelStyles}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    border: 'thin solid #968989 !important',
    height: '35px !important'
  },
  reactDaterangePicker: {
    width: '80px !important',
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px'
  },
  back: {
    marginRight: 30,
    float: 'right',
    marginTop: 5
  }
});
export default withKeycloak(withStyles(styles)(UserLoginHistory));
