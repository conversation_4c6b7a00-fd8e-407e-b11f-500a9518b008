import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { withStyles, makeStyles } from '@material-ui/core/styles';

import Collapse from '@material-ui/core/Collapse';
import clsx from 'clsx';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import KeyboardArrowDownIcon from '@material-ui/icons/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@material-ui/icons/KeyboardArrowUp';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  InputLabel,
  MenuItem,
  Select,
  Divider,
  Button,
  IconButton,
  Tooltip
} from '@material-ui/core';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import 'src/styles.css';
import $ from 'jquery';
import CloseIcon from '@material-ui/icons/Close';
import ReactHtmlParser from 'react-html-parser';
import { useHistory } from 'react-router';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import { i } from 'react-dom-factories';

const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 500,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b',
    display: 'grid',
    gridGap: 10,
    backgroundColor: '#ddeaf4'
  }
}))(Tooltip);

const useRowStyles = makeStyles(theme => ({
  root: {
    '& > *': {
      borderBottom: 'unset'
    }
  },
  table: {
    '& .MuiTableCell-root': {
      borderLeft: '1px solid #cfd4d8',
      '@media (max-width: 1280px)': {
        fontSize: 12
      }
      //borderBottom: '1px solid #cfd4d8'
    },
    '&:last-child': { borderBottom: 'none' }
  },
  mainLabelWithMargin: {
    marginTop: 10
  },
  headerItem: {
    display: 'flex'
    // justifyContent: 'space-between'
  },
  tableRowRoot: {
    backgroundColor: '#dde2ef !important',
    '&:last-child': { borderBottom: 'none' }
  },
  tableRowRootParts: {
    backgroundColor: '#e5eaef !important',
    '&:last-child': { borderBottom: 'none' }
  },
  tableRowMain: {
    backgroundColor: '#bac1d4 !important'
  },
  tableRowSub: {
    backgroundColor: '#cbd6df !important'
  },
  tableRowSubHeader: {
    backgroundColor: '#c9cdd1 !important'
  },
  tableContainter: {
    borderRadius: 0,
    boxShadow: 'none'
  },
  descriptionButton: {
    backgroundColor: theme.palette.primary.main,
    color: '#fff',
    fontSize: 12,
    textTransform: 'none'
  },
  edit: {
    background: theme.palette.button.primary,
    color: '#fff',
    marginLeft: 10,
    fontSize: 12,
    alignSelf: 'self-end',
    textTransform: 'none'
  },
  // laborCell: {
  //   display: 'grid'
  // },
  closeButton: {
    position: 'absolute',
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  jobDescGrid: {
    marginBottom: 10
  },
  square: {
    height: 10,
    width: 10,
    backgroundColor: '#ee7600',
    marginRight: 10,
    marginTop: 4
  },
  jobDescText: {
    display: 'flex'
  },
  tableRowCommon: {
    margin: '0px 12px',
    '@media (max-width: 1280px)': {
      fontSize: 12
    }
  },
  jobLineHeading: {
    color: '#7987a1',
    fontSize: '14px !important',
    textTransform: 'capitalize',
    fontWeight: 600,
    textSlign: 'left !important'
  },
  techLineHeading: {
    color: '#7987a1',
    fontSize: '14px !important',
    // textTransform: 'capitalize',
    fontWeight: 600,
    textSlign: 'left !important'
  },
  paperWidthSm: {
    maxWidth: '70%'
  }
}));

function createData(name, calories, fat, carbs, protein, price) {
  return {
    name,
    calories,
    fat,
    carbs,
    protein,
    price,
    history: [
      { date: '2020-01-05', customerId: '11091700', amount: 3 },
      { date: '2020-01-02', customerId: 'Anonymous', amount: 1 }
    ]
  };
}

function Row({
  individualRow1,
  roNumberData,
  dataByJob,
  partList,
  partByLbr,
  showDescription,
  varianceData,
  varianceDataParts,
  props
}) {
  const { row } = individualRow1;
  const [open, setOpen] = React.useState(true);
  const [isOpened, setIsOpened] = useState(true);
  const [openDialog, setOpenDialog] = React.useState(false);
  const hideRow = id => {
    setIsOpened(wasOpened => !wasOpened);
  };
  const classes = useRowStyles();
  const history = useHistory();

  const dataLoaderRo = () => {
    return (
      <div class="stage" style={{ marginLeft: '30%' }}>
        <div class="dot-pulse-ro"></div>
      </div>
    );
  };
  // const individualRow = dataByJob[individualRow1.lbrlinecode][0];
  let individualRow = individualRow1;
  let linservicerequest;
  let lincause;
  let linstorytext;
  if (dataByJob[individualRow.lbrlinecode]) {
    if (
      dataByJob[individualRow.lbrlinecode][0].linservicerequest == '' ||
      dataByJob[individualRow.lbrlinecode][0].linservicerequest == null ||
      dataByJob[individualRow.lbrlinecode][0].linservicerequest == 'null'
    ) {
      linservicerequest = 'No Service Request Provided';
    } else {
      linservicerequest =
        dataByJob[individualRow.lbrlinecode][0].linservicerequest;
    }
    if (
      dataByJob[individualRow.lbrlinecode][0].lincause == '' ||
      dataByJob[individualRow.lbrlinecode][0].lincause == null ||
      dataByJob[individualRow.lbrlinecode][0].lincause == 'null'
    ) {
      lincause = 'No Cause Provided';
    } else {
      lincause = dataByJob[individualRow.lbrlinecode][0].lincause;
    }
    if (
      dataByJob[individualRow.lbrlinecode][0].linstorytext == '' ||
      dataByJob[individualRow.lbrlinecode][0].linstorytext == null ||
      dataByJob[individualRow.lbrlinecode][0].linstorytext == 'null'
    ) {
      linstorytext = 'No Story Text Provided';
    } else {
      linstorytext = dataByJob[individualRow.lbrlinecode][0].linstorytext;
    }
  }
  let jobDesc = {
    linservicerequest: linservicerequest,
    lincause: lincause,
    linstorytext: linstorytext
  };
  //setJobDesc(jobDesc);
  individualRow['partList'] = [];
  if (
    partList.includes(individualRow.lbrlinecode + individualRow.lbrsequenceno)
  ) {
    partByLbr[individualRow.lbrlinecode + individualRow.lbrsequenceno].map(
      item => {
        if (
          item.lbrpaytype == individualRow.lbrpaytype &&
          item.prtpaytype == individualRow.prtpaytype
        ) {
          individualRow['partList'].push(item);
        }
      }
    );
    // individualRow['partList'].push(
    //   partByLbr[individualRow.lbrlinecode + individualRow.lbrsequenceno]
    // );
  }
  var arr = Object.values(partByLbr);
  var resultArr = flatten(arr);
  function flatten(arr) {
    return arr.reduce(function(flat, toFlatten) {
      return flat.concat(
        Array.isArray(toFlatten) ? flatten(toFlatten) : toFlatten
      );
    }, []);
  }
  if (
    roNumberData != undefined &&
    roNumberData.length > 0 &&
    individualRow['partList'].length > 0
  ) {
    individualRow['partList'].forEach(function(item) {
      roNumberData.map(item1 => {
        if (
          item1.lbrsequenceno == item.lbrsequenceno &&
          item1.prtsequenceno == item.prtsequenceno &&
          item1.prtlaborsequenceno == item.prtlaborsequenceno &&
          item1.prtpaytypegroup == item.prtpaytypegroup &&
          item1.prtpaytype == item.prtpaytype
        ) {
          item.prtextendedcost = item1.prtextendedcost;
          item.prtextendedsale = item1.prtextendedsale;
        }
      });
    });
  }
  if (
    varianceDataParts != undefined &&
    varianceDataParts.length > 0 &&
    individualRow['partList'].length > 0
  ) {
    individualRow['partList'].forEach(function(item) {
      varianceDataParts.map(item1 => {
        if (
          item1.lbrsequenceno == item.lbrsequenceno &&
          item1.prtsequenceno == item.prtsequenceno &&
          item1.prtlaborsequenceno == item.prtlaborsequenceno &&
          item1.paytypegroup == item.prtpaytypegroup
        ) {
          item.varianceParts = item1.variance;
          item.targetPriceParts = item1.targetPrice;
          item.targetExtendedPrice = item1.targetExtendedPrice;
          item.complianceParts = item1.compliance;
          // item.prtextendedcost = item1.prtextendedcost;
          // item.prtextendedsale = item1.prtextendedsale;
          item.partsGpPercentage = item1.grossprofitpercentage;
          item.markup = item1.markup;
        }
      });
    });
  }

  // console.log(
  //   'check',
  //   individualRow,
  //   individualRow['partList'],
  //   varianceDataParts,
  //   partByLbr,
  //   resultArr
  // );
  var lbrPayTypeName;
  if (individualRow.lbrpaytypegroup == 'C') {
    lbrPayTypeName = 'Customer Pay';
  } else if (individualRow.lbrpaytypegroup == 'W') {
    lbrPayTypeName = 'Warranty';
  } else if (individualRow.lbrpaytypegroup == 'I') {
    lbrPayTypeName = 'Internal';
  } else if (individualRow.lbrpaytypegroup == 'B') {
    lbrPayTypeName = 'Body Shop';
  } else if (individualRow.lbrpaytypegroup == 'E') {
    lbrPayTypeName = 'Extended Service Plan';
  } else if (individualRow.lbrpaytypegroup == 'M') {
    lbrPayTypeName = 'Maintenance Plan';
  } else if (individualRow.lbrpaytypegroup == 'U') {
    lbrPayTypeName = 'Unallocated';
  } else if (individualRow.lbrpaytypegroup == 'F') {
    lbrPayTypeName = 'Factory Service Contract';
  }
  if (individualRow.lbrsale != null) {
    var lbrsale = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(individualRow.lbrsale).toFixed(2));
  } else {
    var lbrsale = 0;
  }
  if (individualRow.lbrcost != null) {
    var lbrcost = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(individualRow.lbrcost).toFixed(2));
  } else {
    var lbrcost = 0;
  }
  var pageType =
    history.location &&
    history.location.state &&
    history.location.state.pageType
      ? history.location.state && history.location.state.pageType
      : '';
  var targetPrice = 0;
  var targetPriceParts = 0;
  var varianceParts = 0;
  var varianceLabor = 0;
  var targetExtendedPrice = 0;
  // if(pageType == "labormisses") {
  if (varianceData != undefined && varianceData.gridPricing != null) {
    targetPrice = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(varianceData.gridPricing).toFixed(2));
  } else {
    targetPrice = '$0.00';
  }
  if (varianceData != undefined && varianceData.variance != null) {
    varianceLabor = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(varianceData.variance).toFixed(2));
  } else {
    varianceLabor = '$0.00';
  }
  //} else if(pageType == "partsmisses") {
  if (varianceDataParts != undefined && varianceDataParts.targetPrice != null) {
    targetPriceParts = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(varianceDataParts.targetPrice).toFixed(2));
  } else {
    targetPriceParts = '$0.00';
  }
  if (
    varianceDataParts != undefined &&
    varianceDataParts.targetExtendedPrice != null
  ) {
    targetExtendedPrice = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(varianceDataParts.targetExtendedPrice).toFixed(2));
  } else {
    targetExtendedPrice = '$0.00';
  }

  if (varianceDataParts != undefined && varianceDataParts.variance != null) {
    varianceParts = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(varianceDataParts.variance).toFixed(2));
  } else {
    varianceParts = '$0.00';
  }
  //}

  const handleOpcodeClick = (opcode, expanded, lbrpaytypegroup) => {
    var pageType =
      history.location &&
      history.location.state &&
      history.location.state.pageType
        ? history.location.state && history.location.state.pageType
        : '';
    var payTypes = lbrpaytypegroup;
    console.log('payTypes', payTypes);
    history.push({
      pathname: '/LaborWorkMixAnalysis',
      state: {
        tabselection: 'four',
        opcode: opcode,
        pageType: 'searchByRo',
        isFrom: 'searchByRo',
        page: props.pageType == 'Topbar' ? props.pageType : 'searchByRo',
        parent:
          props.parent != ''
            ? props.parent
            : props.tabType != ''
            ? props.propType + '' + props.tabType
            : props.parent,
        roNumber: roNumberData[0].ronumber,
        selectedFilter: props.selectedFilter,
        //selectedToggle: props.selectedToggle,
        selectedToggle: props.userHistory,
        previousToggle: props.previousToggle,
        // payTypeRo: props.userHistory.payType,
        payTypeRo: lbrpaytypegroup,
        PrevPayType: props.userHistory.PrevPayType,
        PrevGridType: props.userHistory.PrevGridType,
        showAllJobs: props.userHistory.showAllJobs,
        itemizationTab: props.itemizationTab,
        itemizationData: props.itemizationData,
        expanded: expanded,
        filterStart: props.filterStart,
        filterEnd: props.filterEnd,
        storeId: props.storeId,
        selectedGridType: props.userHistory.selectedGridType
      }
    });
  };
  const getVarianceData = (variance, type, targetPrice) => {
    let varianceData;
    if (
      type == 'parts' &&
      (targetPrice == '' || targetPrice == 0 || targetPrice == null)
    ) {
      varianceData = 'N/A';
    } else {
      varianceData = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(Number(variance).toFixed(2));
      varianceData = variance > 0 ? '+' + varianceData : varianceData;
    }
    return varianceData;
  };

  const getTargetPrice = (targetPrice, type, data) => {
    let targetPriceData;
    if (
      (targetPrice == 0 || targetPrice == '' || targetPrice == null) &&
      type == 'parts' &&
      (data.targetPriceParts == 0 ||
        data.targetPriceParts == '' ||
        data.targetPriceParts == null)
    ) {
      targetPriceData = 'N/A';
    } else {
      targetPriceData = new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(Number(targetPrice).toFixed(2));
    }
    return targetPriceData;
  };
  const showJobDescription = joblinecode => {
    showDescription(joblinecode);
  };
  let desc = individualRow.lbropcodedesc + '<br>' + jobDesc.linservicerequest;

  const getDisplayValue = jobDesc => {
    if (
      jobDesc.linservicerequest &&
      jobDesc.linservicerequest.trim() !== '' &&
      jobDesc.linservicerequest !== 'No Service Request Provided'
    ) {
      return jobDesc.linservicerequest;
    } else if (jobDesc.linstorytext && jobDesc.linstorytext.trim() !== '') {
      return jobDesc.linstorytext;
    } else if (jobDesc.lincause && jobDesc.lincause !== 'No Cause Provided') {
      return jobDesc.lincause;
    } else {
      return '';
    }
  };
  return (
    <React.Fragment>
      <TableRow
        className={clsx(
          classes.root,
          classes.tableRowSub,
          'jobline-heading-tr'
        )}
      >
        <TableCell colSpan={8}>
          {/* <TableCell colSpan={( pageType == "labormisses" || pageType == "partsmisses" ) ? 8 : 6}> */}
          <div className={clsx(classes.headerItem, 'main-title')}>
            <Typography
              variant="h6"
              color="secondary"
              gutterBottom
              className={clsx(classes.tableRowCommon, classes.jobLineHeading)}
            >
              {/* Job Line : {individualRow.lbrlinecode} */}
              Job Line : {individualRow.linlinecode}
            </Typography>
            <Divider orientation="vertical" flexItem />
            <Typography
              variant="h6"
              color="secondary"
              gutterBottom
              className={clsx(classes.tableRowCommon, classes.techLineHeading)}
            >
              Tech :{' '}
              {individualRow.techNickname
                ? individualRow.techNickname.replace(/\[/g, ' [')
                : individualRow.lbrtechname &&
                  individualRow.lbrtechname.replace(/\[/g, ' [')}
              {/* {individualRow.lbrtechname
                ? individualRow.lbrtechname.replace(/\[/g, ' [')
                : individualRow.techNickname &&
                  individualRow.techNickname.replace(/\[/g, ' [')} */}
              {/* /{individualRow.lbrtechno} */}
            </Typography>
          </div>
        </TableCell>

        <TableCell colSpan={3} align="right">
          {/* <Button
            className={classes.descriptionButton}
            onClick={() => showJobDescription(individualRow.lbrlinecode)}
          >
            Job Description
          </Button> */}
        </TableCell>
      </TableRow>
      {open ? (
        <>
          <TableRow
            className={clsx(
              classes.table,
              classes.tableRowMain,
              'labor-opcode-title-section'
            )}
          >
            {/* <TableCell width="10%" align="center"> */}
            <TableCell width="8%" align="center">
              Labor
            </TableCell>
            <TableCell width="8%" align="center">
              Opcode
            </TableCell>
            {/* <TableCell width="10%" align="center"> */}
            <TableCell width="8%" align="center">
              Op Category
            </TableCell>
            {/* <TableCell width="12%" align="center"> */}
            <TableCell width="10%" align="center">
              Pay Type
            </TableCell>
            {/* <TableCell width="8%" align="center"> */}
            <TableCell width="6%" align="center">
              Sold Hours
            </TableCell>
            {/* {( pageType == "labormisses" || pageType == "partsmisses" ) ? (
              <React.Fragment> */}
            <TableCell width="8%" align="center">
              Target Price
            </TableCell>
            <TableCell width="8%" align="center">
              Labor Sale
            </TableCell>
            {/* <TableCell width="8%" align="center"> */}
            <TableCell width="7%" align="center">
              $ Variance (+/-)
            </TableCell>
            <TableCell width="8%" align="center">
              Labor Cost
            </TableCell>
            <TableCell width="6%" align="center">
              GP %
            </TableCell>

            {/* <TableCell width="8%" align="center"> */}
            <TableCell width="16%" align="center">
              Request / Story / Cause
            </TableCell>
            {/* </React.Fragment>
            ) : (
              <React.Fragment>
                <TableCell width="8%" align="center">
                  Labor Cost
                </TableCell>
                <TableCell width="8%" align="center">
                  Labor Sale
                </TableCell>
                <TableCell width="6%" align="center">
                  GP %
                </TableCell>

                <TableCell width="30%" align="center">
                  Description
                </TableCell>
              </React.Fragment>
            )} */}
          </TableRow>
          <TableRow
            className={clsx(classes.root, classes.tableRowRoot, classes.table)}
          >
            <TableCell align="center" className={classes.laborCell}>
              <Typography
                variant="body1"
                color="primary"
                gutterBottom
                className={
                  individualRow['partList'].length <= 0 &&
                  classes.mainLabelWithMargin
                }
              >
                Labor {individualRow.lbrsequencenoIdx}
              </Typography>
              {/* {individualRow['partList'].length > 0 && (
              <Button
                color="primary"
                className={classes.edit}
                onClick={event => hideRow(individualRow.lbrlinecode)}
              >
                Hide Parts
              </Button>
            )} */}
            </TableCell>
            {/* <TableCell align="center" style={{cursor: 'pointer',color: '#41424d !important', fontWeight: 'bold'}} onClick={() => handleOpcodeClick(individualRow.lbropcode)}>{individualRow.lbropcode}</TableCell> */}
            <TableCell>
              <span class="allOpcode">
                <HtmlTooltip
                  interactive={true}
                  title={
                    <React.Fragment>
                      {/* <span>
                          <label style={{ fontWeight: 'bold' }}>
                            Job Description :{' '}
                          </label>{' '}
                          {individualRow.lbropcodedesc}{' '}
                        </span>{' '} */}
                      <span>
                        {individualRow.lbropcodedesc
                          ? individualRow.lbropcodedesc
                          : 'No Opcode Description Provided'}
                      </span>{' '}
                    </React.Fragment>
                  }
                  //style={{ display: searchText ? 'none' : 'inline-block' }}
                >
                  <span>{individualRow.lbropcode}</span>
                </HtmlTooltip>
                {/* <span class="opcodeText"></span> */}
                <span class="opcodeDetails">
                  {JSON.parse(localStorage.getItem('selectedStoreId')).length ==
                    1 && (
                    <Tooltip
                      title={
                        <span style={{ fontSize: 11.5 }}>View Details</span>
                      }
                    >
                      <IconButton
                        size="small"
                        classes="infoIcon"
                        onClick={() =>
                          handleOpcodeClick(
                            individualRow.lbropcode,
                            props.expanded,
                            individualRow.lbrpaytypegroup
                          )
                        }
                        id="opcode"
                        style={{ top: '1px', right: '0px' }}
                      >
                        <OpenInNewOutlinedIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                </span>
              </span>
            </TableCell>
            {/* <TableCell align="center" class="searchByRoOpcode" onClick={() => handleOpcodeClick(individualRow.lbropcode)}>{individualRow.lbropcode}</TableCell> */}
            <TableCell align="center">{individualRow.opcategory}</TableCell>
            <TableCell align="center">
              {individualRow.lbrpaytype} - {lbrPayTypeName}
            </TableCell>
            <TableCell align="center">
              {individualRow.laborSoldHours != null
                ? Number(individualRow.laborSoldHours).toFixed(2)
                : individualRow.laborSoldHours == null
                ? '0.00'
                : individualRow.laborSoldHours}
            </TableCell>

            <TableCell align="center">
              {individualRow.opcategory != 'REPAIR'
                ? 'N/A'
                : individualRow.targetPrice != undefined
                ? getTargetPrice(
                    individualRow.targetPrice,
                    'labor',
                    individualRow
                  )
                : 'N/A'}
            </TableCell>

            <TableCell align="center">{lbrsale}</TableCell>
            <TableCell
              align="center"
              class={
                individualRow.opcategory != 'REPAIR' ||
                (individualRow.variance != undefined &&
                  individualRow.opcategory == 'REPAIR' &&
                  (individualRow.variance == 0 ||
                    individualRow.compliance == 'TRUE')) ||
                individualRow.variance == undefined
                  ? 'searchByRoVariance2'
                  : 'searchByRoVariance'
              }
            >
              {individualRow.opcategory != 'REPAIR'
                ? 'N/A'
                : individualRow.variance != undefined
                ? getVarianceData(individualRow.variance, 'labor')
                : 'N/A'}
            </TableCell>

            <TableCell align="center">{lbrcost}</TableCell>

            <TableCell align="center">
              {individualRow.laborGpPercentage
                ? individualRow.laborGpPercentage + '%'
                : '0%'}
            </TableCell>
            <TableCell align="left">
              <HtmlTooltip
                interactive={true}
                title={
                  <React.Fragment>
                    {/* <span>
                          <label style={{ fontWeight: 'bold' }}>
                            Job Description :{' '}
                          </label>{' '}
                          {individualRow.lbropcodedesc}{' '}
                        </span>{' '} */}
                    <span>
                      <label style={{ fontWeight: 'bold' }}>Request :</label>{' '}
                      {/* {jobDesc.linservicerequest}{' '} */}
                      <pre
                        style={{ fontFamily: 'Roboto', whiteSpace: 'pre-wrap' }}
                      >
                        {jobDesc.linservicerequest}{' '}
                      </pre>
                    </span>{' '}
                    <span>
                      <label style={{ fontWeight: 'bold' }}>Story</label>{' '}
                      <label style={{ fontWeight: 'bold', marginLeft: 6 }}>
                        :
                      </label>{' '}
                      {jobDesc.linstorytext}
                    </span>
                    <span>
                      <label style={{ fontWeight: 'bold' }}> Cause :</label>{' '}
                      {jobDesc.lincause}{' '}
                    </span>{' '}
                  </React.Fragment>
                }
                //style={{ display: searchText ? 'none' : 'inline-block' }}
              >
                <span>
                  {getDisplayValue(jobDesc)}
                  {/* {jobDesc.linservicerequest &&
                  jobDesc.linservicerequest != 'No Service Request Provided' &&
                  jobDesc.linservicerequest != ''
                    ? jobDesc.linservicerequest.substring(0, 40)
                    : jobDesc.linstorytext
                    ? jobDesc.linstorytext.substring(0, 40)
                    : jobDesc.lincause
                    ? jobDesc.lincause.substring(0, 40)
                    : jobDesc.linservicerequest
                    ? jobDesc.linservicerequest.substring(0, 40)
                    : ''} */}
                </span>
              </HtmlTooltip>
            </TableCell>
            {/* </React.Fragment>
            ) :(
            <React.Fragment>
              <TableCell align="center">{lbrcost}</TableCell>
              <TableCell align="center">{lbrsale}</TableCell>

              <TableCell align="center">
                {individualRow.laborGpPercentage
                  ? individualRow.laborGpPercentage + '%'
                  : '0%'}
              </TableCell>
              <TableCell align="left">
                <HtmlTooltip
                  title={
                    <React.Fragment>
                      <span>
                        <label style={{ fontWeight: 'bold' }}>
                          Job Description :{' '}
                        </label>{' '}
                        {individualRow.lbropcodedesc}{' '}
                      </span>{' '}
                      <span>
                        <label style={{ fontWeight: 'bold' }}>
                          Service Request :
                        </label>{' '}
                        {jobDesc.linservicerequest}{' '}
                      </span>{' '}
                      <span>
                        <label style={{ fontWeight: 'bold' }}> Cause :</label>{' '}
                        {jobDesc.lincause}{' '}
                      </span>{' '}
                      <span>
                        <label style={{ fontWeight: 'bold' }}>Story Text :</label>{' '}
                        {jobDesc.linstorytext}
                      </span>
                    </React.Fragment>
                  }
                  //style={{ display: searchText ? 'none' : 'inline-block' }}
                >
                  <span>{individualRow.lbropcodedesc.substring(0, 60)}</span>
                </HtmlTooltip>
                {/* <span>
                  <span id="reference-to-edtxt-explnote2" className="ktooltip">
                    {individualRow.lbropcodedesc.substring(0, 60)}
                  </span>
                  <div
                    className="ktooltiptext"
                    id="ro-details"
                    onclick="return false;"
                  >
                    <span>
                      <label style={{ fontWeight: 'bold' }}>
                        Job Description :{' '}
                      </label>{' '}
                      {individualRow.lbropcodedesc}{' '}
                    </span>{' '}
                    <span>
                      <label style={{ fontWeight: 'bold' }}>
                        Service Request :
                      </label>{' '}
                      {jobDesc.linservicerequest}{' '}
                    </span>{' '}
                    <span>
                      <label style={{ fontWeight: 'bold' }}> Cause :</label>{' '}
                      {jobDesc.lincause}{' '}
                    </span>{' '}
                    <span>
                      <label style={{ fontWeight: 'bold' }}>Story Text :</label>{' '}
                      {jobDesc.linstorytext}
                    </span>
                  </div>
                </span> */}
            {/* </TableCell>
            </React.Fragment>
            )} */}
          </TableRow>
        </>
      ) : (
        ''
      )}
      <TableRow>
        <TableCell
          style={{ paddingBottom: 0, paddingTop: 0 }}
          colSpan={11}
          //</TableRow>colSpan={( pageType == "labormisses" || pageType == "partsmisses" ) ? 11 : 9}
          align="center"
        >
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box>
              {isOpened && individualRow['partList'].length > 0 && (
                <TableContainer
                  component={Paper}
                  className={classes.tableContainter}
                >
                  <Table
                    size="small"
                    aria-label="purchases"
                    className={classes.table}
                  >
                    <TableHead>
                      <TableRow className={classes.tableRowSubHeader}>
                        <TableCell width="8%" align="center">
                          Part
                        </TableCell>
                        <TableCell width="10%" align="center">
                          {' '}
                          Description
                        </TableCell>
                        <TableCell width="8%" align="center">
                          {' '}
                          Pay Type
                        </TableCell>
                        <TableCell width="8%" align="center">
                          {' '}
                          Part Source
                        </TableCell>
                        <TableCell width="6%" align="center">
                          {' '}
                          Unit Cost
                        </TableCell>
                        <TableCell width="8%" align="center">
                          {' '}
                          List Price
                        </TableCell>
                        <TableCell width="6%" align="center">
                          Quantity
                        </TableCell>
                        <TableCell width="6%" align="center">
                          Extended Cost
                        </TableCell>
                        <TableCell width="6%" align="center">
                          {' '}
                          Unit Sale
                        </TableCell>
                        <TableCell width="6%" align="center">
                          Target Unit Price
                        </TableCell>
                        <TableCell width="8%" align="center">
                          Target Extended Price
                        </TableCell>
                        <TableCell width="6%" align="center">
                          Extended Sale
                        </TableCell>

                        <TableCell width="6%" align="center">
                          Variance (+/-)
                        </TableCell>

                        <TableCell width="6%" align="center">
                          Markup
                        </TableCell>
                        <TableCell width="6%" align="center">
                          GP %
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {individualRow['partList'].length > 0 &&
                        individualRow['partList'].map((item, index) => (
                          <TableRow
                            key={index}
                            className={classes.tableRowRootParts}
                          >
                            <TableCell
                              align="center"
                              className={classes.laborCell}
                            >
                              <Typography
                                variant="body1"
                                color="primary"
                                gutterBottom
                                className={
                                  individualRow['partList'].length <= 0 &&
                                  classes.mainLabelWithMargin
                                }
                              >
                                {/* Part {index + 1} */}
                                {item.prtpartno}
                              </Typography>
                            </TableCell>
                            <TableCell component="th" scope="row" align="left">
                              {item.prtdesc}
                            </TableCell>
                            <TableCell align="center">
                              {item.prtpaytype}
                            </TableCell>
                            <TableCell align="center">
                              {item.prtsource}
                            </TableCell>
                            <TableCell align="center">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD'
                              }).format(Number(item.prtunitcost).toFixed(2))}
                            </TableCell>
                            <TableCell align="center">
                              ${item.prtlist ? item.prtlist : 0}
                            </TableCell>
                            <TableCell align="center">
                              {item.quantity}
                            </TableCell>
                            <TableCell align="center">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD'
                              }).format(
                                Number(item.prtextendedcost).toFixed(2)
                              )}
                            </TableCell>
                            <TableCell align="center">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD'
                              }).format(Number(item.prtunitsale).toFixed(2))}
                            </TableCell>

                            <TableCell align="center">
                              {individualRow.opcategory != 'REPAIR'
                                ? 'N/A'
                                : item.targetPriceParts != undefined &&
                                  varianceDataParts.length > 0
                                ? getTargetPrice(
                                    item.targetPriceParts,
                                    'parts',
                                    item
                                  )
                                : item.targetPriceParts == undefined &&
                                  varianceDataParts.length > 0
                                ? 'N/A'
                                : varianceDataParts.length == 0 &&
                                  props.dataLoaded == true &&
                                  item.targetPriceParts == undefined
                                ? 'N/A'
                                : dataLoaderRo()}
                            </TableCell>
                            <TableCell align="center">
                              {individualRow.opcategory != 'REPAIR'
                                ? 'N/A'
                                : item.targetExtendedPrice != undefined
                                ? getTargetPrice(
                                    item.targetExtendedPrice,
                                    'parts',
                                    item
                                  )
                                : item.targetExtendedPrice == undefined &&
                                  varianceDataParts.length > 0
                                ? 'N/A'
                                : varianceDataParts.length == 0 &&
                                  props.dataLoaded == true &&
                                  item.targetExtendedPrice == undefined
                                ? 'N/A'
                                : dataLoaderRo()}
                            </TableCell>
                            <TableCell align="center">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: 'USD'
                              }).format(
                                Number(item.prtextendedsale).toFixed(2)
                              )}
                            </TableCell>

                            <TableCell
                              align="center"
                              class={
                                individualRow.opcategory != 'REPAIR' ||
                                (item.varianceParts != undefined &&
                                  individualRow.opcategory == 'REPAIR' &&
                                  (item.varianceParts == 0 ||
                                    item.complianceParts == 'TRUE')) ||
                                item.varianceParts == undefined
                                  ? 'searchByRoVariance2'
                                  : 'searchByRoVariance'
                              }
                            >
                              {individualRow.opcategory != 'REPAIR'
                                ? 'N/A'
                                : item.varianceParts != undefined
                                ? getVarianceData(
                                    item.varianceParts,
                                    'parts',
                                    item.targetPriceParts
                                  )
                                : item.varianceParts == undefined &&
                                  varianceDataParts.length > 0
                                ? 'N/A'
                                : varianceDataParts.length == 0 &&
                                  props.dataLoaded == true &&
                                  item.varianceParts == undefined
                                ? 'N/A'
                                : dataLoaderRo()}
                            </TableCell>

                            <TableCell align="center">
                              {item.markup ? item.markup : '0.0000'}
                            </TableCell>
                            <TableCell align="center">
                              {item.partsGpPercentage
                                ? item.partsGpPercentage + '%'
                                : '0%'}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}

Row.propTypes = {
  row: PropTypes.shape({
    calories: PropTypes.number.isRequired,
    carbs: PropTypes.number.isRequired,
    fat: PropTypes.number.isRequired,
    history: PropTypes.arrayOf(
      PropTypes.shape({
        amount: PropTypes.number.isRequired,
        customerId: PropTypes.string.isRequired,
        date: PropTypes.string.isRequired
      })
    ).isRequired,
    name: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    protein: PropTypes.number.isRequired
  }).isRequired
};

const rows = [
  createData('Frozen yoghurt', 159, 6.0, 24, 4.0, 3.99),
  createData('Ice cream sandwich', 237, 9.0, 37, 4.3, 4.99),
  createData('Eclair', 262, 16.0, 24, 6.0, 3.79),
  createData('Cupcake', 305, 3.7, 67, 4.3, 2.5),
  createData('Gingerbread', 356, 16.0, 49, 3.9, 1.5)
];
// const JobDescription = props => {
//   const handleClose = () => {
//     //setOpen(false);
//   };
//   console.log('props===', props);
// };
export default function SearchGrid(props) {
  const classes = useRowStyles();
  const [open, setOpen] = React.useState(false);
  const [jobDesc, setJobDesc] = React.useState({});

  const showDescription = val => {
    let linservicerequest;
    let lincause;
    let linstorytext;
    if (
      props.dataByJob[val][0].linservicerequest == '' ||
      props.dataByJob[val][0].linservicerequest == null ||
      props.dataByJob[val][0].linservicerequest == 'null'
    ) {
      linservicerequest = 'No Service Request Provided';
    } else {
      linservicerequest = props.dataByJob[val][0].linservicerequest;
    }
    if (
      props.dataByJob[val][0].lincause == '' ||
      props.dataByJob[val][0].lincause == null ||
      props.dataByJob[val][0].lincause == 'null'
    ) {
      lincause = 'No Cause Provided';
    } else {
      lincause = props.dataByJob[val][0].lincause;
    }
    if (
      props.dataByJob[val][0].linstorytext == '' ||
      props.dataByJob[val][0].linstorytext == null ||
      props.dataByJob[val][0].linstorytext == 'null'
    ) {
      linstorytext = 'No Customer Story Provided';
    } else {
      linstorytext = props.dataByJob[val][0].linstorytext;
    }
    let jobDesc = {
      linservicerequest: linservicerequest,
      lincause: lincause,
      linstorytext: linstorytext
    };
    setJobDesc(jobDesc);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  return (
    <>
      <TableContainer
        component={Paper}
        className={clsx(classes.tableContainter, 'repair-order-details-table')}
        style={{ maxHeight: window.innerHeight - 292 }}
      >
        <Table stickyHeader aria-label="collapsible table">
          <TableBody>
            {/* {rows.map(row => (
            <Row key={row.name} row={row} />
          ))} */}
            {props.uniqueList.map(item => (
              <Row
                roNumberData={props.roNumberData}
                individualRow1={item}
                partList={props.partList}
                dataByJob={props.dataByJob}
                partByLbr={props.partByLbr}
                showDescription={showDescription}
                varianceData={props.varianceData}
                varianceDataParts={props.varianceDataParts}
                props={props}
              />
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <Dialog
        open={open}
        // fullWidth
        // maxWidth="lg"
        classes={{
          paperWidthSm: classes.paperWidthSm
        }}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          <Typography variant="h6">Job Description</Typography>

          <IconButton
            aria-label="close"
            onClick={handleClose}
            className={classes.closeButton}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <Typography variant="h6">Service Request</Typography>
          <ul className={'job-details'}>
            <li>{jobDesc.linservicerequest}</li>
          </ul>
          <Typography variant="h6">Cause</Typography>
          <ul className={'job-details'}>
            <li>{jobDesc.lincause}</li>{' '}
          </ul>
          <Typography variant="h6">Story Text</Typography>

          <ul className={'job-details'}>
            <li>{jobDesc.linstorytext}</li>
          </ul>

          {/* <div className={classes.jobDescGrid}>
            <Typography gutterBottom variant="h6">
              Service Request
            </Typography>
            <Typography
              variant="body2"
              color="textSecondary"
              component="p"
              className={classes.jobDescText}
            >
              <div className={classes.square}></div>
              {jobDesc.linservicerequest}
            </Typography>
          </div>
          <div className={classes.jobDescGrid}>
            <Typography gutterBottom variant="h6">
              Cause
            </Typography>
            <Typography
              variant="body2"
              color="textSecondary"
              component="p"
              className={classes.jobDescText}
            >
              <div className={classes.square}></div>
              {jobDesc.lincause}
            </Typography>
          </div>
          <div className={classes.jobDescGrid}>
            <Typography gutterBottom variant="h6">
              Story Text
            </Typography>
            <div>
              <div className={classes.square}></div>
              <div>
                <Typography
                  variant="body2"
                  color="textSecondary"
                  className={classes.jobDescText}
                >
                  {jobDesc.linstorytext}
                </Typography>
              </div>
            </div>
          </div> */}
        </DialogContent>
      </Dialog>
    </>
  );
}
