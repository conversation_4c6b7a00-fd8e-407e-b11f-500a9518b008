import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/core/styles';
import AppBar from '@material-ui/core/AppBar';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import Typography from '@material-ui/core/Typography';
import Box from '@material-ui/core/Box';
import XMLViewer from 'react-xml-viewer';
import ReactJson from 'react-json-view';

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={3}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  };
}

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1,
    backgroundColor: theme.palette.background.paper
  }
}));
const Sidebar = props => {
  const classes = useStyles();
  const [value, setValue] = React.useState(0);
  const [xmlData, setXmlData] = useState('');
  const [jsonData, setJSONData] = useState('');
  const customTheme = {
    attributeKeyColor: '#FF0000',
    attributeValueColor: '#000FF'
  };
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  useEffect(() => {
    if (
      localStorage.getItem('dms') != 'dtk' &&
      localStorage.getItem('dms') != 'atm'
    ) {
      var parser = new DOMParser();
      var xmldoc = parser.parseFromString(props.xml, 'text/xml');

      var x =
        props.realm == 'fisherhonda'
          ? xmldoc.querySelector('RoRecord')
          : xmldoc.querySelector('service-repair-order-history');
      var deletedNodes = x.children;
      var nodesToDelete = [];
      var nodeArray =
        props.realm == 'fisherhonda'
          ? new Array('Rolabor', 'Ropart', 'Romisc')
          : new Array(
              'service-repair-order-history',
              '#text',
              'V',
              'RONumber',
              'VIN',
              'ClosedDate',
              'linLineCode',
              'lbrOpCode',
              'lbrOpCodeDesc',
              'lbrLineCode',
              'lbrSequenceNo',
              'lbrLaborType',
              'lbrCost',
              'lbrSale',
              'hrsSoldHours',
              'hrsActualHours',
              'hrsCost',
              'hrsActualHours',
              'prtLineCode',
              'prtDesc',
              'prtLaborSequenceNo',
              'prtLaborType',
              'prtSequenceNo',
              'prtExtendedSale',
              'prtQtySold',
              'prtExtendedCost'
            );
      for (var index = 0; index < deletedNodes.length; index += 1) {
        var node = deletedNodes[index];
        if (!nodeArray.includes(node.localName)) {
          nodesToDelete.push(node); //delete the node from the parent
          //  x.removeChild(node);
        }
      }
      nodesToDelete.forEach(function(val) {
        x.removeChild(val);
      });

      var s = new XMLSerializer();
      var str = s.serializeToString(x);
      setXmlData(str);
    } else {
      setJSONData(props.json);
    }
  }, [props.xml, props.realm, props.json]);
  return (
    <div className={classes.root}>
      <AppBar position="sticky">
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="simple tabs example"
        >
          <Tab
            wrapped
            label={
              localStorage.getItem('dms') != 'dtk' &&
              localStorage.getItem('dms') != 'atm'
                ? 'Relevant XML Nodes'
                : ' Relevant JSON Nodes'
            }
            {...a11yProps(0)}
          />
          <Tab
            wrapped
            label={
              localStorage.getItem('dms') != 'dtk' &&
              localStorage.getItem('dms') != 'atm'
                ? 'Complete XML'
                : ' Complete JSON'
            }
            {...a11yProps(1)}
          />
        </Tabs>
      </AppBar>
      <TabPanel value={value} index={0}>
        {localStorage.getItem('dms') != 'dtk' &&
        localStorage.getItem('dms') != 'atm' ? (
          <XMLViewer xml={xmlData} collapsible={true} theme={customTheme} />
        ) : (
          jsonData != '' && (
            <ReactJson
              name={null}
              displayObjectSize={false}
              displayDataTypes={false}
              quotesOnKeys={false}
              onAdd={false}
              onEdit={false}
              onDelete={false}
              collapsed={2}
              enableClipboard={false}
              displayArrayKey={false}
              indentWidth={0}
              src={
                localStorage.getItem('dms') != 'atm'
                  ? JSON.parse(jsonData).RODetails
                  : JSON.parse(jsonData).repairOrderLines
              }
            />
          )
        )}
      </TabPanel>
      <TabPanel value={value} index={1}>
        {localStorage.getItem('dms') != 'dtk' &&
        localStorage.getItem('dms') != 'atm' ? (
          <XMLViewer xml={props.xml} collapsible={true} theme={customTheme} />
        ) : (
          jsonData != '' && (
            <ReactJson
              name={null}
              displayObjectSize={false}
              displayDataTypes={false}
              quotesOnKeys={false}
              onAdd={false}
              onEdit={false}
              onDelete={false}
              collapsed={1}
              enableClipboard={false}
              displayArrayKey={false}
              indentWidth={0}
              src={JSON.parse(jsonData)}
            />
          )
        )}
      </TabPanel>
    </div>
  );
};
export default Sidebar;
