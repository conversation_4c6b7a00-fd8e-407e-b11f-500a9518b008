import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import SearchByRoGrid from './SearchByRo';
import { useDispatch } from 'react-redux';

import PropTypes from 'prop-types';
import { withKeycloak } from '@react-keycloak/web';
//import queryString from 'query-string';

const useStyles = makeStyles(theme => ({
  root: {
    padding: 12
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

const SearchByRO = props => {
  const classes = useStyles();
  const dispatch = useDispatch();

  let params = '';
  if (
    typeof props.history != 'undefined' &&
    typeof props.history.location.state != 'undefined' &&
    props.history.location.state != null
  ) {
    params = props.history.location.state.ronumber;
  }
  //  else if (
  //   typeof props.history != 'undefined' &&
  //   typeof props.history.location != 'undefined' &&
  //   props.history.location.ronumber != undefined &&
  //   props.history.location.ronumber != null
  // ) {
  //   params = props.history.location.ronumber;
  // }
  else {
    params = props.selectedRo ? props.selectedRo : '';
  }
  let parent = props.isFrom ? props.isFrom : '';
  // let pageType = typeof props.history != 'undefined' &&
  //   typeof props.history.location != 'undefined' &&
  //   props.history.location.pageType != undefined ? props.history.location.pageType :'';
  return (
    <Page className={classes.root} title="Search by RO">
      <SearchByRoGrid
        params={params && params.trim() == '' ? null : params}
        parent={parent}
        keycloak={props.keycloak}
        isFrom={props.isFrom}
        tabType={props.type ? props.type : ''}
        otherProps={props}
        //  pageType={pageType}
      />
    </Page>
  );
};
SearchByRO.propTypes = {
  keycloak: PropTypes.any
};
export default withKeycloak(SearchByRO);
