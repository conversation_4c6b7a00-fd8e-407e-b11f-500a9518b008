import React, { useEffect, useState } from 'react';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { useDispatch, useSelector } from 'react-redux';
import {
  Grid,
  Typography,
  CircularProgress,
  Box,
  Paper,
  TextField,
  InputLabel,
  MenuItem,
  Select,
  Divider,
  Button,
  IconButton,
  Tooltip
} from '@material-ui/core';

import FormControlLabel from '@material-ui/core/FormControlLabel';
import Table from '@material-ui/core/Table';
import Drawer from '@material-ui/core/Drawer';
import TableCell from '@material-ui/core/TableCell';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';

import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import Alert from '@material-ui/lab/Alert';
import {
  getSearchByRODetails,
  getSearchByROXML,
  getSearchByROJSON,
  getSearchByROJSONFerrario,
  getSearchByROXMLReynolds,
  getDataForLaborMissesWithRO,
  getDataForPartsMissesWithRO,
  getSearchByROJSONData
} from 'src/utils/hasuraServices';
import SearchByROGrid from './SearchByROGrid';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import Link from '@material-ui/core/Link';
import { useHistory } from 'react-router';
import moment from 'moment';
// import Sidebar from './Sidebar';
import { connect } from 'react-redux';
import { SET_SEARCH } from 'src/actions';
import { setSearch } from 'src/actions';
import clsx from 'clsx';
import 'src/styles.css';
import SearchGrid from './SearchGrid';
import { traceSpan } from 'src/utils/OTTTracing';
import { getTimeZone } from 'src/utils/Utils';

import Accordion from '@material-ui/core/Accordion';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';

var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1,
    width: '99%'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  mainLabelAlert: {
    marginTop: 10,
    display: 'flex'
  },
  container: {
    alignItems: 'center',
    margin: '20px 0px'
  },
  containerGridItem: {
    // margin: '10px 0px';
    display: 'flex',
    justifyContent: 'space-between'
    //gridGap: 20
  },
  containerGrid: {
    display: 'flex',
    marginLeft: '19px !important'
  },
  headerItem: {
    display: 'flex',
    //justifyContent: 'space-between'
    alignItems: 'center'
  },
  sublLabel: {
    display: 'flex',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: '12px !important'
    },
    '@media (min-width: 2304px)': {
      fontSize: 15
    }
  },
  textContainer: {
    alignItems: 'start',
    display: 'flex'
  },
  edit: {
    background: theme.palette.button.primary,
    color: '#fff',
    marginLeft: 10,
    alignSelf: 'self-end'
  },
  back: {
    fontSize: 12,

    color: '#7987a1',
    border: '1px solid #7987a1',
    padding: '0 8px',
    borderRadius: 3,
    cursor: 'pointer',
    '&:hover': {
      background: '#7987a1',
      color: '#fff'
    }
  },
  input: {
    margin: '0px 5px',
    width: '28%'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  },
  tableTheme: {
    border: '1px solid #adbab9',
    fontSize: '14px',
    marginLeft: '7px'
  },
  drawer: {
    width: '350px',
    flexShrink: 0
  },
  drawerPaper: {
    width: '350px',
    height: '600px',
    top: '140px'
  },
  headValue: {
    fontWeight: 'normal'
  },
  roDetailsGridFirst: {
    paddingLeft: 0,
    boxShadow: 'none',
    borderRight: '1px solid #e5e5e5',
    borderRadius: 'inherit'
  },
  roDetailsGrid: {
    paddingLeft: 10,
    boxShadow: 'none',
    borderRight: '1px solid #e5e5e5',
    borderRadius: 'inherit'
  },
  roDetailsGridLast: {
    paddingLeft: 10,
    boxShadow: 'none'
  },
  listItems: {
    marginRight: 40,

    lineHeight: 0,

    '@media (max-width: 1920px)': {
      fontSize: 11
    },
    '@media (max-width: 1280px)': {
      fontSize: 9
    },
    '@media (min-width: 2304px)': {
      fontSize: 15
    }
  },
  listItemsText: {
    lineHeight: 0,

    '@media (max-width: 1920px)': {
      fontSize: 11
    },
    '@media (max-width: 1280px)': {
      fontSize: 9
    },
    '@media (min-width: 2304px)': {
      fontSize: 12
    }
  },
  column: {
    display: 'flex',
    flexDirection: 'column',
    textAlign: 'left',
    '@media (max-width: 2304px)': {
      width: '285px',
      marginLeft: '120px'
    },
    '@media (max-width: 1920px)': {
      width: '215px',
      marginLeft: '60px'
    },
    '@media (max-width: 1280px)': {
      width: '200px',
      marginLeft: '60px'
    }
  },
  drawerGrid: {
    //position: 'fixed',
    right: 10,
    top: '210px',
    //zIndex: 1300,
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'flex-end',
    maxWidth: '100% !important'
  },
  drawerIcon: {
    height: '60px',
    width: '60px',
    // zIndex: 1300,
    marginTop: '-16px',
    right: '9px',
    // position: 'absolute',
    boxShadow: '0 0 5px #333',
    borderRadius: '50%',
    background: '#003d6b',
    justifyContent: 'center',
    alignItems: 'center',
    color: '#fff !important',
    fontWeight: 'bold',
    display: 'flex'
  },
  loaderGrid: {
    height: 250,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  dataLabel: {
    display: 'flex',
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      marginLeft: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: '12px !important',
      marginLeft: 10
    }
  },
  roDetailsGridStore: {
    display: 'none'
  },
  roDetailsGridStoreLast: {
    paddingLeft: 10,
    boxShadow: 'none',
    borderLeft: '1px solid #e5e5e5'
  }
}));

const SearchByRO = props => {
  let roExpanded =
    props.otherProps &&
    props.otherProps.location &&
    props.otherProps.location.state &&
    props.otherProps.location.state.roExpanded
      ? props.otherProps.location.state.roExpanded
      : '';

  const classes = useStyles();
  const dispatch = useDispatch();
  let textInput = React.createRef();
  //const [roNumber, setRONumber] = useState(props.params);
  const [roNumber, setRONumber] = useState(props.params && props.params.trim());
  const [roInput, setROInput] = useState(props.params && props.params.trim());
  const [roNumberExists, setRONumberExists] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [roNumberData, setRoNumberData] = useState([]);
  const [roNumberDataNonFiltered, setRoNumberNonDataFiltered] = useState([]);
  const [roNumberNoData, setRoNumberNoData] = useState(false);
  const [roNumberXML, setRoNumberXML] = useState([]);
  const [roNumberJSON, setRoNumberJSON] = useState([]);
  const [varianceData, setVarianceData] = useState([]);
  const [varianceDataParts, setVarianceDataParts] = useState([]);
  const [pageType, setPageType] = useState();
  const [selectedFilters, setSelectedFilter] = useState();
  const [selectedToggles, setSelectedToggle] = useState();
  const [parent, setParent] = useState();
  const [userHistory, setHistory] = useState([]);
  const history = useHistory();
  const session = useSelector(state => state.session);
  const [expandedRo, setExpandedRo] = useState(roExpanded);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [filterStart, setFilterStart] = useState();
  const [filterEnd, setFilterEnd] = useState();
  const [storeChanged, setStoreChange] = useState(false);
  const [storeIdSelected, setStoreIdSelected] = useState('');

  const [state, setState] = useState({
    right: false
  });
  let storeId =
    JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
    history.location &&
    history.location.state &&
    history.location.state.storeId
      ? history.location.state.storeId
      : JSON.parse(localStorage.getItem('selectedStoreId'))[0];

  const [selectedStoreId, setStoreId] = useState(storeId);

  const toggleDrawer = (anchor, open) => event => {
    if (
      event.type === 'keydown' &&
      (event.key === 'Tab' || event.key === 'Shift')
    ) {
      return;
    }

    setState({ ...state, [anchor]: open });
  };
  useEffect(() => {
    var checkSt = lodash.isEqual(storeIdSelected, session.storeSelected);

    if (checkSt == false) {
      setStoreIdSelected(localStorage.getItem('selectedStoreId'));
      if (storeIdSelected == '') {
        setStoreChange(false);
      } else {
        setStoreChange(true);
      }
    } else {
      setStoreIdSelected(localStorage.getItem('selectedStoreId'));
      setStoreChange(false);
    }
  }, [session.storeSelected]);

  useEffect(() => {
    if (props.params && props.params.trim() != null) {
      setRONumber(props.params && props.params.trim());
      setROInput(props.params && props.params.trim());
      setIsLoading(true);
    }
  }, [props.params && props.params.trim(), , session.serviceAdvisor]);
  const getFilters = advisors => {
    let combiner = `{`;
    advisors.map((each, index) => {
      combiner = combiner + '' + each + ',';
    });
    combiner = combiner.substring(0, combiner.length - 1) + `}`;
    return combiner;
  };
  useEffect(() => {
    dispatch(setSearch(false));
    let roclickFrom = '';
    if (history.location && history.location.state) {
      const spanAttribute = {
        pageUrl: '',
        origin: '',
        event: 'RO Click',
        ronumber: roNumber ? roNumber : '',
        fromurl: getPrevPath(),
        pagetype: history.location.state.pageType
          ? history.location.state.pageType
          : '',
        chart: history.location.state.chartName
          ? history.location.state.chartName
          : '',
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('ROClick', spanAttribute);
    }

    if (roNumber != null) {
      // setIsLoading(true);
      // console.log('searchRo',history,history.location.state,history.location.state.payType)
      if (history && history.location.state) {
        setHistory(history.location.state);
      }
      let toggle = null;
      let timeZone =
        history.location &&
        history.location.state &&
        history.location.state.timeZone
          ? history.location.state.timeZone
          : getTimeZone();
      let closeddate = null;
      let pageType =
        history.location &&
        history.location.state &&
        history.location.state.pageType
          ? history.location.state.pageType
          : '';
      let selectedFilter =
        history.location &&
        history.location.state &&
        history.location.state.selectedFilter
          ? history.location.state.selectedFilter
          : '';
      let selectedToggle =
        history.location &&
        history.location.state &&
        history.location.state.selectedToggle
          ? history.location.state.selectedToggle
          : '';
      let parent =
        history.location &&
        history.location.state &&
        history.location.state.parent
          ? history.location.state.parent
          : '';

      let payType =
        history.location &&
        history.location.state &&
        history.location.state.payType
          ? history.location.state.payType
          : 'C';
      let gridType =
        history.location &&
        history.location.state &&
        history.location.state.gridType
          ? history.location.state.gridType
          : '';
      let PrevPayType =
        history.location &&
        history.location.state &&
        history.location.state.PrevPayType
          ? history.location.state.PrevPayType
          : '';
      let PrevGridType =
        history.location &&
        history.location.state &&
        history.location.state.PrevGridType
          ? history.location.state.PrevGridType
          : '';
      let showAllJobs =
        history.location &&
        history.location.state &&
        history.location.state.showAllJobs
          ? history.location.state.showAllJobs
          : false;

      let openDate =
        history.location &&
        history.location.state &&
        history.location.state.openDate
          ? history.location.state.openDate
          : history.location &&
            history.location.state &&
            history.location.state.userHistory &&
            history.location.state.userHistory.openDate
          ? history.location.state.userHistory.openDate
          : '';
      let filterStart =
        history.location &&
        history.location.state &&
        history.location.state.filterStart
          ? history.location.state.filterStart
          : '';
      let filterEnd =
        history.location &&
        history.location.state &&
        history.location.state.filterEnd
          ? history.location.state.filterEnd
          : '';
      let selectedGridType =
        history.location &&
        history.location.state &&
        history.location.state.selectedGridType
          ? history.location.state.selectedGridType
          : '';
      let closedDate =
        history.location &&
        history.location.state &&
        history.location.state.closedDate
          ? history.location.state.closedDate
          : history.location &&
            history.location.state &&
            history.location.state.userHistory &&
            history.location.state.userHistory.closedDate
          ? history.location.state.userHistory.closedDate
          : '';
      let storeSelected =
        JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
        history.location &&
        history.location.state &&
        history.location.state.storeId
          ? `{` + selectedStoreId + `}`
          : getFilters(JSON.parse(localStorage.getItem('selectedStoreId')));
      setPageType(pageType);
      setSelectedFilter(selectedFilter);
      setSelectedToggle(selectedToggle);
      setParent(parent);
      setRONumberExists(true);
      setRoNumberData([]);
      setVarianceDataParts([]);
      setVarianceData([]);
      setDataLoaded(false);
      setFilterStart(filterStart);
      setFilterEnd(filterEnd);
      getSearchByRODetails(roNumber, storeSelected, result => {
        if (
          result.data.statelessDbdSearchRoGetUxRepairOrderDetails
            .uxRepairOrderDetails
        ) {
          const roData =
            result.data.statelessDbdSearchRoGetUxRepairOrderDetails
              .uxRepairOrderDetails;
          if (roData.length <= 0) {
            setRoNumberNoData(true);
          } else {
            setRoNumberNoData(false);
          }
          setRoNumberData(roData);
          setRoNumberNonDataFiltered(roData);
          let distinctClosedDates = lodash
            .uniqBy(roData, 'closeddate')
            .map(item => item.closeddate);

          if (
            roData.length > 0 &&
            closedDate != '' &&
            openDate != '' &&
            roExpanded == ''
          ) {
            let distinctClosedDatesArr = lodash
              .uniqBy(roData, 'closeddate')
              .map(item => item);
            distinctClosedDatesArr = distinctClosedDatesArr.sort(
              (a, b) => new Date(b.closeddate) - new Date(a.closeddate)
            );

            const resultIndex = lodash.findIndex(distinctClosedDatesArr, {
              opendate: openDate,
              closeddate: closedDate
            });

            setExpandedRo('panel' + resultIndex);
          } else if (distinctClosedDates.length > 1 && roExpanded == '') {
            setExpandedRo('');
          } else if (roExpanded != '') {
            setExpandedRo(roExpanded);
          } else {
            setExpandedRo('panel0');
          }
          setIsLoading(false);

          if (
            history.location &&
            history.location.state &&
            history.location.state.pageType &&
            (history.location.state.pageType == 'labormisses' ||
              history.location.state.pageType == 'partsmisses')
          ) {
            toggle = history.location.state.selectedToggle
              ? history.location.state.selectedToggle
              : 'MTD';
          } else {
            toggle = null;
            closeddate = roData.length > 0 ? roData[0].closeddate : null;
          }
          let storeId = JSON.parse(localStorage.getItem('selectedStoreId'));
          if (
            storeId[0] == '221641258' &&
            roData.length > 0 &&
            gridType == ''
          ) {
            let models = [
              '2500CC',
              '2500PU',
              '2500T',
              '3500PU',
              'E2500',
              'F250',
              'F350',
              'F4',
              'F550',
              'G2500',
              'GM3500',
              'PROM3500',
              'R2500',
              'R3500',
              'R5500',
              'RAM4500',
              'RAMT25',
              'RAMT35',
              'RAMT45',
              'RAMT55',
              'E350',
              'E450'
            ];
            let selectedModel = roData[0].model;
            let status = models.includes(selectedModel);
            if (status == true) {
              gridType = 'Heavy Duty';
            } else {
              gridType = 'Light Duty';
            }
          } else if (
            storeId[0] == '214272044' &&
            roData.length > 0 &&
            gridType == ''
          ) {
            let selectedModel = roData[0].model;
            let status =
              '2500'.includes(selectedModel) ||
              '3500'.includes(selectedModel) ||
              '4500'.includes(selectedModel) ||
              '5500'.includes(selectedModel) ||
              'F250'.includes(selectedModel) ||
              'F350'.includes(selectedModel) ||
              'F450'.includes(selectedModel) ||
              'F550'.includes(selectedModel);
            if (status == true) {
              gridType = 'Heavy Duty';
            } else {
              gridType = 'Light Duty';
            }
          }
          let laborGridTypes = JSON.parse(
            localStorage.getItem('laborGridTypes')
          );
          let partsMatrixTypes = JSON.parse(
            localStorage.getItem('partsMatrixTypes')
          );

          laborGridTypes.map(item => {
            if (item == 'Internal') {
              payType = 'I';
            } else if (item == 'Warranty') {
              payType = 'W';
            } else {
              payType = 'C';
            }
            getDataForLaborMissesWithRO(
              toggle,
              timeZone,
              roNumber,
              closeddate,
              payType,
              item,
              filterStart,
              filterEnd,
              result => {
                if (
                  result.data
                    .statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown
                    .statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns
                ) {
                  const varianceData =
                    result.data
                      .statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown
                      .statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns;
                  setVarianceData(varianceData);
                }
              }
            );
          });
          partsMatrixTypes.map(item => {
            if (item == 'Internal') {
              payType = 'I';
            } else {
              payType = 'C';
            }
            getDataForPartsMissesWithRO(
              toggle,
              timeZone,
              roNumber,
              closeddate,
              payType,
              filterStart,
              filterEnd,
              result => {
                if (
                  result.data
                    .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                    .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns
                ) {
                  const varianceDataParts =
                    result.data
                      .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                      .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns;
                  setDataLoaded(true);
                  setVarianceDataParts(varianceDataParts);
                }
              }
            );
          });
        } else {
          setIsLoading(false);
        }
      });
      // if (localStorage.getItem('dms') == 'cdk') {
      //   getSearchByROXML(roNumber, storeId, result => {
      //     if (result.data.statefulCdkSourceRawCdkDataDetails.nodes) {
      //       const roData =
      //         result.data.statefulCdkSourceRawCdkDataDetails.nodes.length > 0
      //           ? result.data.statefulCdkSourceRawCdkDataDetails.nodes[0]
      //           : '';

      //       setRoNumberXML(roData);
      //     }
      //   });
      // } else if (localStorage.getItem('dms') == 'rey') {
      //   getSearchByROXMLReynolds(roNumber, result => {
      //     if (result.data.statefulRldsSourceRawRldsDataDetails.nodes) {
      //       const roData =
      //         result.data.statefulRldsSourceRawRldsDataDetails.nodes.length > 0
      //           ? result.data.statefulRldsSourceRawRldsDataDetails.nodes[0]
      //           : '';

      //       setRoNumberXML(roData);
      //     }
      //   });
      // } else if (localStorage.getItem('dms') == 'atm') {
      //   getSearchByROJSONFerrario(roNumber, result => {
      //     if (result.data.atmDataDetails.nodes) {
      //       const roData = result.data.atmDataDetails.nodes[0];

      //       setRoNumberJSON(roData.roDataJson.replace(/\\/g, ''));
      //     }
      //   });
      // } else if (localStorage.getItem('dms') == 'dtk') {
      //   getSearchByROJSONData(roNumber, result => {
      //     if (result.data.statefulDtkSourceRawDtkDataDetails.nodes) {
      //       const roData =
      //         result.data.statefulDtkSourceRawDtkDataDetails.nodes[0];

      //       setRoNumberJSON(roData.roDataJson.replace(/\"\\/g, ''));
      //     }
      //   });
      // } else {
      //   getSearchByROJSON(roNumber, result => {
      //     if (result.data.dmsSourceRawDtkDataDetailS.nodes) {
      //       const roData = result.data.dmsSourceRawDtkDataDetailS.nodes[0];

      //       setRoNumberJSON(roData.roDataJson.replace(/\\/g, ''));
      //     }
      //   });
      // }
    } else {
      setRONumberExists(false);
      setIsLoading(false);
    }
  }, [roNumber, session.serviceAdvisor]);

  useEffect(() => {
    if (roNumberDataNonFiltered.length == 0) {
      setExpandedRo('');
    }
    if (expandedRo != '' && roNumberDataNonFiltered.length > 0) {
      let distinctClosedDates = lodash
        .uniqBy(roNumberDataNonFiltered, 'closeddate')
        .map(item => item);
      const dateObjects = distinctClosedDates.map(
        dateString => new Date(dateString.closeddate)
      );
      distinctClosedDates = distinctClosedDates.sort(
        (a, b) => new Date(b.closeddate) - new Date(a.closeddate)
      );
      let filteredData = [];
      if (
        distinctClosedDates.length > 0 &&
        roNumberDataNonFiltered.length > 0
      ) {
        console.log('ro data', expandedRo, distinctClosedDates);

        let index = expandedRo.split('panel')[1];
        console.log('ro data index', index);
        if (index >= 0) {
          filteredData = roNumberDataNonFiltered.filter(
            item => item.closeddate == distinctClosedDates[index].closeddate
          );
        }
        setRoNumberData(filteredData);
        setExpandedRo(expandedRo);
        setIsLoading(false);
      }
    }
  }, [expandedRo]);

  function handleSearch(event) {
    if (event.key === 'Enter' || event.key == undefined) {
      if (roInput != null && roInput != '') {
        setRONumber(roInput);
        roNumberData.length > 0 && roInput == roNumber
          ? setIsLoading(false)
          : setIsLoading(true);
      } else {
        setRONumberExists(false);
        setIsLoading(false);
        setRONumber(null);
      }
    }
  }
  const imagePath =
    localStorage.getItem('dms') != 'cdk' &&
    localStorage.getItem('dms') != 'rld' &&
    localStorage.getItem('dms') != 'atm'
      ? '/images/verifyimages/showXml1.png'
      : '/images/verifyimages/json.jpg';
  const job = [];
  const dataByJob = {};
  const partByLbr = {};
  const partList = [];
  var lbrRepeat = [];
  var partListData = [];
  if (roNumberExists == true && roNumberData.length > 0) {
    if (varianceData != undefined && varianceData.length > 0) {
      roNumberData.forEach(function(individualRow) {
        varianceData.map(item => {
          if (
            item.lbrsequenceno == individualRow.lbrsequenceno &&
            item.paytype == individualRow.lbrpaytype
          ) {
            individualRow.variance = item.variance;
            individualRow.targetPrice = item.gridPricing;
            individualRow.compliance = item.compliance;
          }
        });
      });
    }

    var uniqueList = lodash.uniqBy(roNumberData, function(p) {
      return [
        p.lbrlinecode,
        p.lbrsale,
        p.lbrcost,
        p.lbrpaytype,
        p.lbropcode,
        p.lbrsequenceno,
        p.lbrtechno
      ].join();
    });

    uniqueList = lodash.sortBy(uniqueList, ['linlinecode', 'lbrsequenceno']);
    //console.log('uniqueList===', uniqueList);
    roNumberData.forEach(function(individualRow) {
      const partAppend =
        individualRow.prtlinecode + individualRow.prtlaborsequenceno;

      if (partAppend != 0) {
        if (partList.includes(partAppend)) {
          partByLbr[partAppend].push(individualRow);
        } else {
          partList.push(partAppend);
          partByLbr[partAppend] = new Array();
          partByLbr[partAppend].push(individualRow);
        }
      }
      if (individualRow.lbrpaytype != null) {
        if (job.includes(individualRow.lbrlinecode)) {
          dataByJob[individualRow.lbrlinecode].push(individualRow);
        } else {
          job.push(individualRow.lbrlinecode);

          dataByJob[individualRow.lbrlinecode] = new Array();
          dataByJob[individualRow.lbrlinecode].push(individualRow);
        }
      }
    });
  }

  const toggleAcordion = (panel, closeddate) => (event, newExpanded) => {
    let filteredData = [];
    if (closeddate) {
      setExpandedRo(newExpanded ? panel : false);
      filteredData = roNumberDataNonFiltered.filter(
        item => item.closeddate == closeddate
      );
      setRoNumberData(filteredData);
    }
  };

  function renderBackButton() {
    // let newHistory =
    //   history.location &&
    //   history.location.state &&
    //   history.location.state != 'undefined' &&
    //   (history.location.state.userHistory == 'undefined' ||
    //     history.location.state.userHistory == undefined ||
    //     history.location.state.userHistory == '') &&
    //   userHistory &&
    //   userHistory.parent &&
    //   userHistory.parent != 'searchByRo'
    //     ? history.location.state
    //     : userHistory && userHistory.parent == 'searchByRo'
    //     ? userHistory
    //     : userHistory.userHistory &&
    //       userHistory.userHistory.pageType != 'searchByRo'
    //     ? userHistory.userHistory
    //     : userHistory.userHistory && userHistory.userHistory.userHistory
    //     ? userHistory.userHistory.userHistory
    //     : history;
    let newHistory =
      history.location &&
      history.location.state &&
      history.location.state != 'undefined' &&
      (history.location.state.userHistory == 'undefined' ||
        history.location.state.userHistory == undefined ||
        history.location.state.userHistory == '')
        ? history.location.state
        : userHistory.userHistory &&
          userHistory.userHistory.pageType != 'searchByRo'
        ? userHistory.userHistory
        : userHistory.userHistory && userHistory.userHistory.userHistory
        ? userHistory.userHistory.userHistory
        : history;
    console.log('SearchhByROoo', newHistory);
    if (newHistory) {
      newHistory.pageType == 'opcodes'
        ? // (newHistory.pageType == 'drilldown' && newHistory.parent == 'opcodes')
          history.push({
            pathname: newHistory.isLabor
              ? '/LaborWorkMixAnalysis'
              : '/PartsWorkMixAnalysis',
            // history: history,
            state: {
              tabselection: 'five',
              opcode: newHistory.opcode,
              y: newHistory.month_year,
              pageType: 'searchByRo',
              isFrom: newHistory.isFrom,
              parent: newHistory.parent,
              reportType: newHistory.reportType,
              workmixParent: newHistory.workmixParent,
              workmixTab: newHistory.workmixTab,
              selectedReportType: newHistory.selectedReportType,
              opCategory: newHistory.opCategory,
              prevSearch: newHistory.prevSearch,
              prevPage: newHistory.prevPage,
              userHistory: newHistory.userHistory,
              payTypeRo: newHistory.payType,
              opcodePayType: newHistory.opcodePayType,
              itemizationData: newHistory.itemizationData,
              itemizationTab: newHistory.itemizationTab,
              isLabor: newHistory.isLabor,
              isExpanded: newHistory.isExpanded,
              prevPath: newHistory.prevPath,
              prevMenu: newHistory.prevMenu,
              selectedButton: newHistory.selectedButtonType,
              comparisonMonth1: newHistory.comparisonMonth1,
              comparisonMonth2: newHistory.comparisonMonth2,
              selectValue: newHistory.selectValue,
              originData: newHistory.originData
            }
          })
        : newHistory.pageType == 'technician'
        ? history.push({
            pathname: newHistory.isFromTechnician
              ? '/TechnicianPerformance'
              : '/ServiceAdvisorPerformance',
            state: {
              tabselection: newHistory.isFromTechnician ? 'ten' : 'twelve',
              techNo: newHistory.techNo,
              y: newHistory.month_year,
              pageType: 'searchByRo',
              isFrom: newHistory.isFrom,
              parent: newHistory.parent,
              type: newHistory.type,
              techName: newHistory.techName,
              advisorName: newHistory.serviceAdvisor,
              selectedReportType: newHistory.selectedReportType,
              reportTabSelection: newHistory.reportTabSelection,
              comparisonMonth1: newHistory.comparisonMonth1,
              comparisonMonth2: newHistory.comparisonMonth2,
              chartLoc: newHistory.chartLoc,
              prevPath: newHistory.prevPath,
              prevMenu: newHistory.prevMenu,
              payType:
                props.otherProps &&
                props.otherProps.history.location &&
                props.otherProps.history.location.state.payType,
              monthSelected: newHistory.monthSelected,
              month_year: newHistory.month_year,
              chartType: newHistory.chartType,
              drilldownVal: newHistory.drilldownVal,
              weekStart: newHistory.weekStart,
              weekEnd: newHistory.weekEnd
            }
          })
        : newHistory.pageType == 'elrReport'
        ? history.push({
            pathname: '/TrancheReport',
            state: {
              currentRate: newHistory.currentRate
            }
          })
        : newHistory.pageType == 'Discounts'
        ? history.push({
            pathname: '/AnalyzeData',
            search: '?chartId=' + newHistory.chartId,
            state: {
              chartId: newHistory.chartId,
              month_year: newHistory.discountMonth ? newHistory.discountMonth : newHistory.month_year,
              prevPath: newHistory.prevPath,
              isFrom: 'searchByRoDiscount',
              type: newHistory.type,
              serviceAdvisor: 'All',
              drillDown: newHistory.drillDown
            },
            handleHighlight: history.location.handleHighlight,
            selectedGrid: history.location.selectedGrid
          })
        : newHistory.pageType == 'revenueSummary'
        ? history.push({
            pathname: '/RevenueSummaryDrilldown',
            state: {
              queryMonth: newHistory.queryMonth,
              drillDownType: newHistory.drillDownType,
              department: newHistory.department
            }
          })
        : newHistory.pageType == 'WarrantyRates_labor'
        ? history.push({
            pathname: '/WarrantyRatesLabor',
            state: {
              drillDownType: newHistory.drillDownType,
              ronumber: newHistory.queryMonth,
              pageType: newHistory.pageType,
              startDate: newHistory.startDate,
              endDate: newHistory.endDate
            }
          })
        : newHistory.pageType == 'WarrantyRates_parts'
        ? history.push({
            pathname: '/WarrantyMarkupParts',
            state: {
              ronumber: newHistory.queryMonth,
              pageType: newHistory.pageType,
              startDate: newHistory.startDate,
              endDate: newHistory.endDate,
              drillDownType: newHistory.drillDownType
            }
          })
        : newHistory.pageType == 'CustomerHistory'
        ? history.push({
            pathname: '/CustomerHistory',
            state: {
              ronumber: newHistory.ronumber,
              pageType: newHistory.pageType,
              startDate: newHistory.startDate,
              endDate: newHistory.endDate
            }
          })
        : newHistory.pageType == 'labormisses'
        ? history.push({
            pathname:
              session.versionFlag == 'TRUE' || session.versionFlag == true
                ? '/LaborMisses'
                : '/LaborGridMisses',
            state: {
              selectedFilter: newHistory.selectedFilter,
              selectedToggle: newHistory.selectedToggle,
              selectedMonthYear: newHistory.selectedMonthYear,
              parent: newHistory.parent,
              previousToggle: newHistory.previousToggle,
              payType: newHistory.payType,
              gridType: newHistory.gridType,
              previousPayType: newHistory.PrevPayType,
              previousGridType: newHistory.PrevGridType,
              showAllJobs: newHistory.showAllJobs,
              filterStart: newHistory.filterStart,
              filterEnd: newHistory.filterEnd,
              storeId: newHistory.storeId,
              selectedGridType: newHistory.selectedGridType,
              report_name: newHistory.reportName,
              filterText: newHistory.reportName,
              visibility: newHistory.visibility,
              checkedColumns: newHistory.checkedColumns,
              draggedColumn: newHistory.draggedColumn,
              selectedAdvisor: newHistory.selectedAdvisor,
              selectedTech: newHistory.selectedTech,
              filterColumns: newHistory.filterColumns,
              sortColumns: newHistory.sortColumns
            }
          })
        : newHistory.pageType == 'partsmisses'
        ? history.push({
            pathname:
              session.versionFlag == 'TRUE' || session.versionFlag == true
                ? '/PartsMisses'
                : '/PartsTargetMisses',
            state: {
              selectedFilter: newHistory.selectedFilter,
              selectedToggle: newHistory.selectedToggle,
              selectedMonthYear: newHistory.selectedMonthYear,
              parent: newHistory.parent,
              previousToggle: newHistory.previousToggle,
              payType: newHistory.payType,
              previousPayType: newHistory.PrevPayType,
              previousGridType: newHistory.PrevGridType,
              showAllJobs: newHistory.showAllJobs,
              filterStart: newHistory.filterStart,
              filterEnd: newHistory.filterEnd,
              storeId: newHistory.storeId,
              gridType: newHistory.gridType,
              selectedGridType: newHistory.selectedGridType,
              report_name: newHistory.reportName,
              filterText: newHistory.reportName,
              visibility: newHistory.visibility,
              checkedColumns: newHistory.checkedColumns,
              draggedColumn: newHistory.draggedColumn,
              selectedAdvisor: newHistory.selectedAdvisor,
              selectedTech: newHistory.selectedTech,
              filterColumns: newHistory.filterColumns,
              sortColumns: newHistory.sortColumns
            }
          })
        : newHistory.pageType == 'Specialmetrics'
        ? history.push({
            pathname: '/SpecialMetrics',
            state: {
              parent: newHistory.parent,
              drillDownType: newHistory.drillDownType
            },
            handleHighlight: history.location.handleHighlight,
            selectedGrid: history.location.selectedGrid
          })
        : userHistory &&
          userHistory.pageType == 'searchByRo' &&
          userHistory.parent == 'Laboritemization'
        ? history.push({
            pathname: '/LaborItemization',
            state: {
              tabSelection: newHistory.itemizationTab,
              data: newHistory.itemizationData,
              isFrom: 'searchRo'
            }
          })
        : userHistory &&
          userHistory.pageType == 'searchByRo' &&
          userHistory.parent == 'Partsitemization'
        ? history.push({
            pathname: '/PartsItemization',
            state: {
              tabSelection: newHistory.itemizationTab,
              data: newHistory.itemizationData,
              isFrom: 'searchRo'
            }
          })
        : newHistory.pageType == 'onelineDrilldown'
        ? history.push({
            pathname: '/AnalyzeData',
            search: '?OneLine',
            state: {
              ronumber: newHistory.ronumber,
              pageType: newHistory.pageType,
              filterStart: newHistory.filterStart,
              filterEnd: newHistory.filterEnd,
              type: newHistory.type,
              gridType: newHistory.oneLineType,
              report_name: newHistory.reportName,
              parent: newHistory.parent,
              filterText: newHistory.reportName,
              visibility: newHistory.visibility,
              checkedColumns: newHistory.checkedColumns,
              draggedColumn: newHistory.draggedColumn,
              selectedAdvisor: newHistory.selectedAdvisor,
              selectedTech: newHistory.selectedTech,
              filterColumns: newHistory.filterColumns,
              sortColumns: newHistory.sortColumns,
              jobType: newHistory.jobType
            }
          })
        : newHistory.pageType == 'MPIStats'
        ? history.push({
            pathname: '/MPIStats',
            state: {
              ronumber: newHistory.ronumber,
              pageType: newHistory.pageType,
              filterStart: newHistory.filterStart,
              filterEnd: newHistory.filterEnd,
              type: newHistory.type,
              parent: newHistory.parent,
              report_name: newHistory.reportName,
              filterText: newHistory.reportName,
              visibility: newHistory.visibility,
              checkedColumns: newHistory.checkedColumns,
              draggedColumn: newHistory.draggedColumn,
              selectedAdvisor: newHistory.selectedAdvisor,
              selectedTech: newHistory.selectedTech,
              filterColumns: newHistory.filterColumns,
              sortColumns: newHistory.sortColumns,
              jobType: newHistory.jobType
            }
          })
        : history.push({
            pathname: '/AnalyzeData',
            search: '?chartId=' + 'drillDown',
            prevPath: newHistory.prevPath,
            state: {
              chartId: newHistory.chartId,
              x: newHistory.x,
              y: newHistory.y,
              drillDown: newHistory.drillDown,
              chartName: newHistory.chartName,
              category: newHistory.category,
              prevPath: newHistory.prevPath,
              //isFrom: 'searchByRo',
              discountMonth: newHistory.discountMonth,
              discountId: newHistory.discountId,
              discountServiceAdvisor: newHistory.discountServiceAdvisor,
              discountServiceAdvisorName: newHistory.discountServiceAdvisorName,
              tabSelection: newHistory.tabSelection,
              elr: newHistory.elr,
              slodhours: newHistory.slodhours,
              markup: newHistory.markup,
              partscost: newHistory.partscost,
              label: newHistory.label,
              parent: newHistory.parent,
              selectedToggle: newHistory.selectedToggle,
              monthYearComparison: newHistory.monthYearComparison,
              datatype: newHistory.datatype,
              techNo: newHistory.techNo,
              techName: newHistory.techName,
              isFrom: newHistory.isFrom
            },
            handleHighlight: history.location.handleHighlight,
            selectedGrid: history.location.selectedGrid
          });
    }
    /*  else {
          if(pageType == 'labormisses' || pageType == 'partsmisses') {
            history.push({
              pathname: pageType == 'labormisses' ? '/LaborMisses' : '/PartsMisses',
              state: {
                selectedFilter: selectedFilters,
                selectedToggle: selectedToggles,
                parent: parent
              }
            })
          }
        }*/
  }

  function getPrevPath() {
    let prevPath = '';
    if (history.location.state.pageType == 'opcodes') {
      prevPath = history.location.state.isLabor
        ? '/LaborWorkMixAnalysis'
        : '/PartsWorkMixAnalysis';
    } else if (history.location.state.pageType == 'technician') {
      prevPath = history.location.state.isFromTechnician
        ? '/TechnicianPerformance'
        : '/ServiceAdvisorPerformance';
    } else if (history.location.state.pageType == 'elrReport')
      prevPath = '/TrancheReport';
    else if (history.location.state.pageType == 'Discounts')
      prevPath = '/AnalyzeData' + '?chartId=' + history.location.state.chartId;
    else if (history.location.state.pageType == 'revenueSummary')
      prevPath = '/RevenueSummaryDrilldown';
    else if (history.location.state.pageType == 'WarrantyRates_labor')
      prevPath = '/WarrantyRatesLabor';
    else if (history.location.state.pageType == 'WarrantyRates_parts')
      prevPath = '/WarrantyMarkupParts';
    else if (history.location.state.pageType == 'labormisses')
      prevPath = '/LaborMisses';
    else if (history.location.state.pageType == 'partsmisses')
      prevPath = '/PartsMisses';
    else if (history.location.state.pageType == 'CustomerHistory')
      prevPath = '/CustomerHistory';
    else prevPath = '/AnalyzeData' + '?chartId=' + 'drillDown';
    return prevPath;
  }

  const windowWidth = window.innerWidth;
  const counter = 1;

  var tabType = '';
  const GridItem = ({ title, value }) => {
    return (
      <Grid container xs={12} className={classes.containerGridItem}>
        <Grid item xs={3}>
          <Typography
            variant="h6"
            gutterBottom
            color="primary"
            className={clsx(classes.sublLabel, 'sub-title')}
          >
            {title}
          </Typography>
        </Grid>
        <Grid item xs={4} sm>
          <Typography
            variant="body1"
            gutterBottom
            color="primary"
            className={classes.dataLabel}
          >
            {value ? ': ' + value : title == 'Customer' ? ':' : ''}
          </Typography>
        </Grid>
      </Grid>
    );
  };

  let roItem = [1, 2];
  let distinctClosedDates = [];
  if (roNumberDataNonFiltered.length > 0) {
    distinctClosedDates = lodash
      .uniqBy(roNumberDataNonFiltered, 'closeddate')
      .map(item => item);
    const dateObjects = distinctClosedDates.map(
      dateString => new Date(dateString.closeddate)
    );
    distinctClosedDates = distinctClosedDates.sort(
      (a, b) => new Date(b.closeddate) - new Date(a.closeddate)
    );

    // dateObjects.sort((a, b) => b - a);
    // distinctClosedDates = dateObjects.map(
    //   date => date.toISOString().split('T')[0]
    // );
  }
  return (
    <div className={classes.root}>
      <Paper className={classes.paper}>
        {/* <div className={classes.headerItem}>
          <Typography
            variant="h4"
            color="primary"
            gutterBottom
            className={clsx(classes.mainLabel, 'main-title')}
          >
            Search By RO
          </Typography>
          <Button
            variant="contained"
            className={clsx(classes.back, 'back-btn')}
            onClick={renderBackButton}
          >
            Back
          </Button>
        </div>
        <Divider />
        <Grid container className={classes.container}>
          <Grid item xs={2}>
            <Typography
              variant="h6"
              gutterBottom
              color="primary"
              className={clsx(classes.sublLabel, 'field-label')}
            >
              RO # :
            </Typography>
          </Grid>
          <Grid item xs={6} className={classes.textContainer}>
            <TextField
              value={roInput}
              onChange={e => setROInput(e.target.value)}
              classes={{ root: classes.TextField }}
              size="small"
              label={'RO Number'}
              autoFocus={true}
              id={'ronumber'}
              variant="outlined"
              className={classes.input}
              onKeyDown={handleSearch}
            />

            <Button
              variant="contained"
              className={clsx(classes.edit, 'orange-btn')}
              onClick={handleSearch}
            >
              Go
            </Button>
          </Grid>
        </Grid>
        <Divider /> */}
        {/* || pageType == 'drillDown' */}
        <div className={classes.headerItem}>
          {props.isFrom != 'itemization' &&
            ((history &&
              history.location.state &&
              history.location.state.pageType &&
              history.location.state.pageType != 'Topbar' &&
              JSON.parse(localStorage.getItem('selectedStoreId')).length == 1 &&
              storeChanged == false) ||
              (history &&
                history.location.state &&
                history.location.state.pageType &&
                history.location.state.pageType != 'Topbar' &&
                JSON.parse(localStorage.getItem('selectedStoreId')).length >
                  1) ||
              (history &&
                history.location.state == undefined &&
                pageType == 'searchByRo')) && (
              <Button
                variant="contained"
                className={clsx(classes.back, 'back-btn')}
                onClick={renderBackButton}
              >
                Back
              </Button>
            )}
          <Typography
            variant="h4"
            color="primary"
            gutterBottom
            className={clsx(
              props.isFrom != 'itemization'
                ? classes.mainLabel
                : classes.mainLabelAlert,
              'main-title'
            )}
          >
            Repair Order Details
          </Typography>
          {distinctClosedDates.length > 1 ? (
            <Typography
              variant="h6"
              color="green"
              gutterBottom
              class={'rodetailsMsg'}
            >
              {distinctClosedDates.length} Repair orders found.
            </Typography>
          ) : null}
        </div>

        <Divider />
        {isLoading === true ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : roNumberExists === true && roNumberData.length > 0 ? (
          <>
            <div>
              {distinctClosedDates.map((item, index) => {
                var advisorName = roNumberData[0].advisorNickname
                  ? roNumberData[0].advisorNickname
                  : roNumberData[0].advisorName;

                var serviceAdvisor =
                  roNumberData[0].serviceadvisor !== null
                    ? '/' + roNumberData[0].serviceadvisor
                    : '';
                return (
                  <Accordion
                    expanded={expandedRo === 'panel' + index}
                    onChange={toggleAcordion('panel' + index, item.closeddate)}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls="panel1a-content"
                      id="panel1a-header"
                    >
                      <React.Fragment>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-grid'
                          )}
                        >
                          RO
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-separator'
                          )}
                        >
                          :
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          //  color="primary"
                          class={clsx(
                            classes.container,
                            'repair-order-details-value'
                          )}
                        >
                          {roNumberData[0].ronumber}
                        </Typography>

                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-grid'
                          )}
                        >
                          Opened
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-separator-cls'
                          )}
                        >
                          :
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          //  color="primary"
                          class={clsx(
                            classes.container,
                            'repair-order-details-value'
                          )}
                        >
                          {moment(item.opendate).format('MM/DD/YY')}
                        </Typography>

                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-grid'
                          )}
                        >
                          Closed
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-separator-cls'
                          )}
                        >
                          :
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          //  color="primary"
                          class={clsx(
                            classes.container,
                            'repair-order-details-value'
                          )}
                        >
                          {moment(item.closeddate).format('MM/DD/YY')}
                        </Typography>

                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-grid'
                          )}
                        >
                          Customer
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-separator-cls'
                          )}
                        >
                          :
                        </Typography>
                        <Tooltip title={item.customername || '-'}>
                          <Typography
                            variant="h6"
                            gutterBottom
                            //  color="primary"
                            class={clsx(
                              classes.container,
                              'repair-order-details-value-customer'
                            )}
                          >
                            {/* {item.customername != null ? item.customername : '-'} */}
                            {item.customername
                              ? item.customername.length > 10
                                ? `${item.customername.substring(0, 10)}...`
                                : item.customername
                              : '-'}
                          </Typography>
                        </Tooltip>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-grid'
                          )}
                        >
                          Year
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-separator-cls'
                          )}
                        >
                          :
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          //  color="primary"
                          class={clsx(
                            classes.container,
                            'repair-order-details-value'
                          )}
                        >
                          {item.year}
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-grid'
                          )}
                        >
                          Make
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-separator-cls'
                          )}
                        >
                          :
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          //  color="primary"
                          class={clsx(
                            classes.container,
                            'repair-order-details-value-make'
                          )}
                        >
                          {item.make}
                        </Typography>
                        {item.makeDesc && (
                          <React.Fragment>
                            <Typography
                              variant="h6"
                              gutterBottom
                              color="primary"
                              className={clsx(
                                classes.container,
                                'repair-order-details-grid'
                              )}
                            >
                              Make Description
                            </Typography>
                            <Typography
                              variant="h6"
                              gutterBottom
                              color="primary"
                              className={clsx(
                                classes.container,
                                'repair-order-details-separator-cls'
                              )}
                            >
                              :
                            </Typography>
                            <Typography
                              variant="h6"
                              gutterBottom
                              //  color="primary"
                              class={clsx(
                                classes.container,
                                'repair-order-details-value-make-Desc'
                              )}
                            >
                              {item.makeDesc}
                            </Typography>
                          </React.Fragment>
                        )}
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-grid'
                          )}
                        >
                          Model
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          color="primary"
                          className={clsx(
                            classes.container,
                            'repair-order-details-separator-cls'
                          )}
                        >
                          :
                        </Typography>
                        <Typography
                          variant="h6"
                          gutterBottom
                          //  color="primary"
                          class={clsx(
                            classes.container,
                            'repair-order-details-value'
                          )}
                        >
                          {item.model}
                        </Typography>
                      </React.Fragment>
                    </AccordionSummary>
                    <AccordionDetails style={{ display: 'block' }}>
                      <Grid
                        container
                        className={clsx(
                          classes.container,
                          'repair-order-details'
                        )}
                      >
                        <Grid item xs={10} className={classes.containerGrid}>
                          <Grid item xs={3}>
                            <Paper
                              classes={{
                                root: classes.roDetailsGrid
                              }}
                              className={'repair-order-details-advisor'}
                            >
                              <GridItem
                                title="Advisor"
                                value={advisorName + serviceAdvisor}
                                // value={
                                //   (roNumberData[0].advisorNickname
                                //     ? roNumberData[0].advisorNickname
                                //     : roNumberData[0].advisorName) +
                                //   '/' +
                                //   roNumberData[0].serviceadvisor
                                // }
                              />
                            </Paper>
                          </Grid>

                          <Grid item xs={2}>
                            <Paper
                              classes={{
                                root: classes.roDetailsGrid
                              }}
                              className={'repair-order-details-mileage'}
                            >
                              <GridItem
                                title="Mileage"
                                value={roNumberData[0].mileage}
                              />

                              <GridItem />
                            </Paper>
                          </Grid>
                          <Grid item xs={3}>
                            <Paper
                              classes={{
                                root:
                                  history.location &&
                                  history.location.state &&
                                  history.location.state.storeId == undefined &&
                                  JSON.parse(
                                    localStorage.getItem('selectedStoreId')
                                  ).length > 1
                                    ? classes.roDetailsGrid
                                    : classes.roDetailsGridLast
                              }}
                              className={'repair-order-details-vin'}
                            >
                              <GridItem
                                title="Vin"
                                value={roNumberData[0].vin}
                              />

                              <GridItem />
                            </Paper>
                          </Grid>
                          <Grid item xs={3}>
                            <Paper
                              classes={{
                                root:
                                  ((history.location &&
                                    history.location.state &&
                                    history.location.state.storeId !=
                                      undefined) ||
                                    roNumberData[0].storeName) &&
                                  JSON.parse(
                                    localStorage.getItem('selectedStoreId')
                                  ).length > 1
                                    ? classes.roDetailsGridStoreLast
                                    : classes.roDetailsGridStore
                              }}
                              className={'repair-order-details-vin'}
                            >
                              <GridItem
                                title="Store"
                                value={roNumberData[0].storeName}
                              />

                              <GridItem />
                            </Paper>
                          </Grid>
                        </Grid>
                        {/* {props.isFrom != 'itemization' &&
                (props.keycloak.realmAccess.roles.includes('admin') == true ||
                  props.keycloak.realmAccess.roles.includes('superadmin') ==
                    true) && (
                  <Grid
                    item
                    xs={2}
                    className={classes.drawerGrid}
                    onClick={toggleDrawer('right', true)}
                  >
                    <a id="showXmlButton">
                      <Typography
                        variant="body1"
                        className={classes.drawerIcon}
                      >
                        {localStorage.getItem('dms') == 'dtk' ||
                        localStorage.getItem('dms') == 'atm'
                          ? 'JSON'
                          : 'XML'}
                      </Typography>
                    </a>
                  </Grid>
                )} */}
                      </Grid>
                      <Grid container className={classes.container}>
                        <SearchGrid
                          uniqueList={uniqueList}
                          roNumberData={roNumberData}
                          partList={partList}
                          dataByJob={dataByJob}
                          partByLbr={partByLbr}
                          varianceData={varianceData}
                          varianceDataParts={varianceDataParts}
                          selectedFilter={selectedFilters}
                          selectedToggle={selectedToggles}
                          parent={parent}
                          userHistory={userHistory}
                          pageType={pageType}
                          previousToggle={props.previousToggle}
                          tabType={props.parent ? props.parent : ''}
                          propType={props.tabType ? props.tabType : ''}
                          PrevPayType={
                            props.PrevPayType ? props.PrevPayType : ''
                          }
                          PrevGridType={
                            props.PrevGridType ? props.PrevGridType : ''
                          }
                          showAllJobs={
                            props.showAllJobs ? props.showAllJobs : false
                          }
                          itemizationTab={
                            props.otherProps && props.otherProps.tab
                              ? props.otherProps.tab
                              : 'one'
                          }
                          itemizationData={
                            props.otherProps && props.otherProps.data
                              ? props.otherProps.data
                              : []
                          }
                          dataLoaded={dataLoaded}
                          expanded={expandedRo}
                          filterStart={filterStart}
                          filterEnd={filterEnd}
                          storeId={selectedStoreId}
                          selectedGridType={
                            props.selectedGridType ? props.selectedGridType : ''
                          }
                        />
                      </Grid>
                    </AccordionDetails>
                  </Accordion>
                );
              })}
            </div>
          </>
        ) : roNumberExists === true &&
          roNumberData.length == 0 &&
          isLoading === false &&
          roNumberNoData == false ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : (
          // <Alert
          //   variant="outlined"
          //   severity="info"
          //   style={{ border: '1px solid #c2185b' }}
          // >
          //   No Data Found For Selected RO Number
          // </Alert>
          <Alert
            variant="outlined"
            severity="info"
            style={{
              marginTop: 10,
              border:
                process.env.REACT_APP_DEALER == 'Armatus'
                  ? '1px solid  #003d6b'
                  : '1px solid #c2185b'
            }}
            //  style={{ border: '1px solid #c2185b' }}
          >
            {roNumberNoData == true
              ? 'No Data Found For Selected RO Number'
              : ' No RO Number Selected !!!'}
          </Alert>
        )}
      </Paper>
      {/* <Drawer
        anchor="right"
        open={state['right']}
        onClose={toggleDrawer('right', false)}
        className={classes.drawer}
        classes={{
          paper: classes.drawerPaper
        }}
      >
        {roNumberJSON.length > 0 ? (
          <Sidebar
            xml={roNumberXML}
            json={roNumberJSON}
            realm={localStorage.getItem('realm')}
          />
        ) : (
          <Sidebar
            xml={'<?xml version="1.0"?>' + roNumberXML.roDataXml}
            realm={localStorage.getItem('realm')}
            json={roNumberJSON}
          />
        )}
      </Drawer> */}
    </div>
  );
};

export default connect(mapStateToProps, mapDispatchToProps)(SearchByRO);

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setSearch: data => dispatch({ type: SET_SEARCH, payload: data })
  };
}

// export default SearchByRO;
