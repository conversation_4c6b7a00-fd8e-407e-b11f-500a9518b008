import React, { useState } from 'react';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import { useDispatch } from 'react-redux';
import {
  ExpansionPanel,
  ExpansionPanelSummary,
  Typography,
  Button,
  Grid
} from '@material-ui/core';
import MuiExpansionPanel from '@material-ui/core/ExpansionPanel';
import MuiExpansionPanelSummary from '@material-ui/core/ExpansionPanelSummary';
import MuiExpansionPanelDetails from '@material-ui/core/ExpansionPanelDetails';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import Tooltip from '@material-ui/core/Tooltip';
import TableRow from '@material-ui/core/TableRow';
import ReactHtmlParser from 'react-html-parser';
import PartListGrid from './PartListGrid';

const MExpansionPanel = withStyles({
  root: {
    backgroundColor: '#adbab930 !important',
    border: '1px solid rgba(0, 0, 0, .125)',
    boxShadow: 'none',
    '&:not(:last-child)': {
      borderBottom: 0
    },
    '&:before': {
      display: 'none'
    },
    '&$expanded': {
      margin: 'auto'
    }
  },
  expanded: {}
})(MuiExpansionPanel);

const MExpansionPanelSummary = withStyles({
  root: {
    backgroundColor: '#adbab930 !important',
    borderBottom: '1px solid rgba(0, 0, 0, .125)',
    marginBottom: -1,
    minHeight: 56,
    '&$expanded': {
      minHeight: 56
    }
  },
  content: {
    '&$expanded': {
      margin: '12px 0'
      // backgroundColor: '#adbab930 !important'
    }
  },
  expanded: {}
})(MuiExpansionPanelSummary);

const MExpansionPanelDetails = withStyles(theme => ({
  root: {
    // backgroundColor: '#adbab930 !important',

    padding: theme.spacing(2)
  }
}))(MuiExpansionPanelDetails);

const ExpansionPanelDetails = withStyles(theme => ({
  root: {
    backgroundColor: '#adbab930 !important',

    padding: theme.spacing(2),
    display: 'block !important'
  }
}))(MuiExpansionPanelDetails);

const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      backgroundColor: '#adbab930 !important'
      //color: '#ccc !important'
    }
  }
}))(TableRow);
const StyledTableRows = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      backgroundColor: '#adbab9 !important'
      //color: '#ccc !important'
    }
  }
}))(TableRow);
const StyledTableCell = withStyles(theme => ({
  head: {},
  body: {
    fontSize: 14,
    textAlign: 'center',
    borderBottom: '1px solid #eee  ',
    borderRight: '1px solid #eee !important',
    borderCollapse: 'unset !important',
    lineHeight: '0.5rem',
    width: '100px'
  }
}))(TableCell);

const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 300,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b',

    backgroundColor: '#ddeaf4'
  }
}))(Tooltip);

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3)
  },
  table: {
    minWidth: 700,
    borderCollapse: 'unset'
  },
  tableRowcss: {
    minWidth: 700,
    borderCollapse: 'unset'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  },
  tableTheme: {
    border: '1px solid #adbab9',
    fontSize: '14px',
    marginLeft: '7px'
  },
  stickyRow: {
    position: 'sticky',
    left: 0,
    top: '23px',
    /* background: #1C4464; */
    color: 'white',
    zIndex: 3
  }
}));
const DetailsGrid = ({
  setLbrRepeat,
  lbrRepeat,
  dataByJobDetails,
  countrLbr,
  partList,
  partByLbr,
  setPartsArr,
  partListVal
}) => {
  var job = [];
  const rows = dataByJobDetails;
  const [isOpened, setIsOpened] = useState(true);
  rows['partList'] = [];
  if (partList.includes(rows.lbrlinecode + rows.lbrsequenceno)) {
    rows['partList'].push(partByLbr[rows.lbrlinecode + rows.lbrsequenceno]);
  }

  var lbrPayTypeName;
  if (rows.lbrpaytypegroup == 'C') {
    lbrPayTypeName = 'Customer Pay';
  } else if (rows.lbrpaytypegroup == 'W') {
    lbrPayTypeName = 'Warranty';
  } else if (rows.lbrpaytypegroup == 'I') {
    lbrPayTypeName = 'Internal';
  } else if (rows.lbrpaytypegroup == 'B') {
    lbrPayTypeName = 'Body Shop';
  } else if (rows.lbrpaytypegroup == 'E') {
    lbrPayTypeName = 'Extended Service Plan';
  } else if (rows.lbrpaytypegroup == 'M') {
    lbrPayTypeName = 'Maintenance Plan';
  } else if (rows.lbrpaytypegroup == 'U') {
    lbrPayTypeName = 'Unallocated';
  }
  var checkImage;
  var filterByLbr;
  var filterByLbrCatDes = '';
  if (rows.filterByLbr == 'sale_cost') {
    filterByLbr = 'Sale and cost';

    var filterByLbrCategory;
    var opCat;

    if (rows.filterByCategoryLbr == 'lbr_sale_cost_known_category_allocated') {
      filterByLbrCategory = 'Labor Pay Type = CWIBEM';
      opCat = 'REPAIR (or) MAINTENANCE (or) COMPETITIVE';
      filterByLbrCatDes = '<br/>Known Category <br/><br/>Allocated';
    } else if (
      rows.filterByCategoryLbr == 'lbr_sale_cost_not_categorized_or_unallocated'
    ) {
      filterByLbrCategory = ' Labor Pay Type  = U or Null';
      opCat = 'Null';
      filterByLbrCatDes = '<br/>Not Category Unallocated';
    } else if (
      rows.filterByCategoryLbr == 'lbr_sale_cost_other_category_allocated'
    ) {
      filterByLbrCategory = 'Labor Pay Type  = CWIBEM';
      opCat = 'N/A';
      filterByLbrCatDes = '<br/>Other Category Allocated';
    } else {
      filterByLbrCategory = '';
      opCat = '';
      filterByLbrCatDes = '';
    }
  } else if (rows.filterByLbr == 'no_sale_no_cost') {
    filterByLbr = 'No sale and No cost';
  } else if (rows.filterByLbr == 'sale_but_no_cost') {
    filterByLbr = 'Sale but no cost';
  } else if (rows.filterByLbr == 'cost_but_no_sale') {
    filterByLbr = 'Cost but no Sale';
  } else if (rows.filterByLbr == 'sale_equal_cost') {
    filterByLbr = 'Sale equal cost';
  }
  if (rows.lbrsale != null) {
    var lbrsale = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(rows.lbrsale).toFixed(2));
  } else {
    var lbrsale = 0;
  }
  if (rows.lbrcost != null) {
    var lbrcost = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(Number(rows.lbrcost).toFixed(2));
  } else {
    var lbrcost = 0;
  }

  const hideRow = id => {
    setIsOpened(wasOpened => !wasOpened);
  };
  return (
    <StyledTableRow
      style={{
        display: 'block',
        width: '100%',
        borderBottom: '1px solid #eee',
        backgroundColor: '#adbab9'
      }}
    >
      <StyledTableCell
        style={ { width: '8%', padding: '14px', lineHeight: '1.0rem' }}
      >
        <Grid>Labor {countrLbr}</Grid>
        <br />
        {rows['partList'].length > 0? (
          <Button
            size="small"
            variant="outlined"
            style={{
              minWidth: '50px',
              textTransform: 'none',
              background: 'rgba(0, 0, 0, 0.23)'
            }}
            onClick={event => hideRow(rows.lbrlinecode)}
          >
            {isOpened ? 'Hide Parts' : 'Show Parts'}
          </Button>
        ):(
        <Button
            size="small"
            variant="outlined"
            style={{
              visibility: 'hidden',
              minWidth: '50px',
              textTransform: 'none',
              background: 'rgba(0, 0, 0, 0.23)'
            }}
          >
           {isOpened ? 'Hide Parts' : 'Show Parts'}
          </Button>
        )}
      </StyledTableCell>
      <StyledTableCell style={ { width: '100%', padding: '0px', lineHeight: '1.0rem' }}
      >
      <StyledTableRow
      style={{
        display: 'block',
        width: '100%',
        // borderBottom: '1px solid #eee',
        backgroundColor: '#adbab9',
        // marginLeft: '-14px'
      }}
      >
      <StyledTableCell
        // colspan={2}
        style={{
          wordBreak: 'break-word',
          width: '1%',
          fontSize: '12px',
          lineHeight: '1.0rem'
        }}
      >
        <span style={{fontSize: '14px',fontWeight: 'bold',float: 'left',marginLeft: '20px'}}>Tech ID: {rows.lbrtechno}</span>
        <span  style={{fontSize: '14px',fontWeight: 'bold',float: 'left',marginLeft: '150px'}}>Tech Name: {rows.lbrtechname}</span>
      </StyledTableCell>
      
      </StyledTableRow>
      <StyledTableRow
       style={{
        display: 'block',
        width: '100%',
        // borderBottom: '1px solid #eee',
        backgroundColor: '#adbab9',
        // marginLeft: '-14px'
      }}
      >
      <StyledTableCell
        // colspan={2}
        style={{
          wordBreak: 'break-word',
          width: '8%',
          fontSize: '12px',
          lineHeight: '1.0rem'
        }}
      >
        {rows.lbropcode}
      </StyledTableCell>
      <StyledTableCell
        colspan={2}
        hidden
        style={{
          wordBreak: 'break-word',
          width: '11%',
          fontSize: '12px',
          lineHeight: '1.0rem'
        }}
      >
        {rows.opcategory}
      </StyledTableCell>
      <StyledTableCell
        style={{ width: '13%', fontSize: '12px', lineHeight: '1.0rem' }}
      >
        {rows.lbrpaytype} - {lbrPayTypeName}
      </StyledTableCell>
      <StyledTableCell
        style={{ width: '10%', fontSize: '12px', lineHeight: '1.0rem' }}
      >
        {lbrsale}{' '}
      </StyledTableCell>
      <StyledTableCell
        style={{ width: '10%', fontSize: '12px', lineHeight: '1.0rem' }}
      >
        {lbrcost}{' '}
      </StyledTableCell>
      <StyledTableCell
        style={{ width: '9.5%', fontSize: '12px', lineHeight: '1.0rem' }}
      >
        {rows.laborSoldHours != null
          ? Number(rows.laborSoldHours).toFixed(2)
          : rows.laborSoldHours}
      </StyledTableCell>
      <StyledTableCell
        align="center"
        className="tooltd"
        style={{
          width: '0%',
          fontSize: '12px',
          lineHeight: '1.0rem',
          padding: '13px'
        }}
      >
        <HtmlTooltip
          title={
            <React.Fragment>
              <Typography color="inherit">
                {' '}
                <label>{rows.lbropcodedesc}</label>
              </Typography>
            </React.Fragment>
          }
        >
          <Button style={{ minWidth: '53px' }}>
            <img
              style={{ marginLeft: '5px', width: '17px', height: '17px' }}
              src="./images/verifyimages/info.png"
            />
          </Button>
        </HtmlTooltip>
      </StyledTableCell>
      <StyledTableCell
        align="center"
        style={{ width: '17%', lineHeight: 1 }}
        colspan="2"
      >
        {rows.filterByLbr == 'sale_cost' ? (
          <HtmlTooltip
            title={
              <React.Fragment>
                <Typography color="inherit">
                  {' '}
                  <label>
                    Labor Sale {'<>'} Labor Cost and Labor Sale {'>'} 0 and
                    Labor cost {'>'}0{' '}
                  </label>
                </Typography>
                <hr
                  style={{
                    borderTop: '1px solid #ffffff !important',
                    margin: '10px'
                  }}
                />{' '}
                {filterByLbrCategory}
                <hr
                  style={{
                    borderTop: '1px solid #ffffff !important',
                    margin: '10px'
                  }}
                />
                <label>OP Category : </label>
                <br />
                {opCat}
              </React.Fragment>
            }
          >
            <Button>
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/checked.png"
              />
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/info.png"
              />
            </Button>
          </HtmlTooltip>
        ) : rows.filterByLbr == 'no_sale_no_cost' ? (
          <HtmlTooltip
            title={
              <React.Fragment>
                <Typography color="inherit">
                  {' '}
                  <label>Labor Sale = 0 and Labor Cost =0</label>
                </Typography>
              </React.Fragment>
            }
          >
            <Button>
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/cross.png"
              />
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/info.png"
              />
            </Button>
          </HtmlTooltip>
        ) : rows.filterByLbr == 'sale_but_no_cost' ? (
          <HtmlTooltip
            title={
              <React.Fragment>
                <Typography color="inherit">
                  {' '}
                  <label>
                    Labor Sale {'>'}0 and Labor Cost {'<='} 0{' '}
                  </label>
                </Typography>
              </React.Fragment>
            }
          >
            <Button>
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/cross.png"
              />
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/info.png"
              />
            </Button>
          </HtmlTooltip>
        ) : rows.filterByLbr == 'cost_but_no_sale' ? (
          <HtmlTooltip
            title={
              <React.Fragment>
                <Typography color="inherit">
                  {' '}
                  <label>
                    Labor Sale {'< ='} 0 and Labor Cost {'>'} 0{' '}
                  </label>
                </Typography>
              </React.Fragment>
            }
          >
            <Button>
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/cross.png"
              />
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/info.png"
              />
            </Button>
          </HtmlTooltip>
        ) : rows.filterByLbr == 'sale_equal_cost' ? (
          <HtmlTooltip
            title={
              <React.Fragment>
                <Typography color="inherit">
                  {' '}
                  <label>
                    Labor Sale {'>'} 0 and Labor Cost {'>'} 0 and Labor Sale =
                    Labor Cost
                  </label>
                </Typography>
              </React.Fragment>
            }
          >
            <Button>
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/cross.png"
              />
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/verifyimages/info.png"
              />
            </Button>
          </HtmlTooltip>
        ) : (
          <HtmlTooltip
            title={
              <React.Fragment>
                <Typography color="inherit">
                  {' '}
                  <label>
                    Labor Sale {'>'} 0 and Labor Cost {'>'} 0 and Labor Sale =
                    Labor Cost
                  </label>
                </Typography>
              </React.Fragment>
            }
          >
            <Button>
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/cross.png"
              />
              <img
                style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                src="./images/info.png"
              />
            </Button>
          </HtmlTooltip>
        )}
        <br />
        {ReactHtmlParser(filterByLbr)}
        <br />
        <p style={{ fontWeight: '200 !important', wordBreak: 'break-word' }}>
          {ReactHtmlParser(filterByLbrCatDes)}
        </p>
      </StyledTableCell>
      </StyledTableRow>
      </StyledTableCell>
      {isOpened &&
        rows['partList'].length > 0 &&
        rows['partList'][0].map((item, index) => (
          <StyledTableRows
            id="partToShow"
            style={{
              backgroundColor: '#adbab9 !important'
            }}
          >
            <StyledTableCell colspan="11" style={{ padding: 0 }}>
              <Table
                className="partsPortion"
                style={{
                  textAlign: 'center',
                  color: 'white',
                  borderRadius: '15px'
                }}
              >
                <PartListGrid tempPart={item} keyPart={index} />
              </Table>
            </StyledTableCell>
          </StyledTableRows>
        ))}
    </StyledTableRow>
  );
};
const SearchByROGrid = ({
  individualRow,
  roNumberData,
  dataByJob,
  partList,
  partByLbr
}) => {
  const classes = useStyles();
  const [selectedJob, toggleJob] = useState(-1);
  const [partListVal, setPartListVal] = useState([]);
  const [lbrRepeatData, setlbrRepeatData] = useState([]);

  const [expanded, setExpanded] = React.useState('');

  const handleChange = panel => (event, newExpanded) => {
    setExpanded(newExpanded ? panel : false);
  };

  const setPartsArr = id => {
    const val = partListVal;
    if (!partListVal.includes(id)) {
      val.push(id);
      setPartListVal([...partListVal, id]);
    }
  };
  var countrLbr = 1;

  let linservicerequest;
  let lincause;
  let linstorytext;
  function openJob(index) {
    toggleJob(selectedJob === index ? -1 : index);
  }
  let lbrRepeat = lbrRepeatData;
  const setLbrRepeat = value => {
    lbrRepeat.push(value);
    setlbrRepeatData(lbrRepeat);
  };

  if (
    dataByJob[individualRow.lbrlinecode][0].linservicerequest == '' ||
    dataByJob[individualRow.lbrlinecode][0].linservicerequest == null ||
    dataByJob[individualRow.lbrlinecode][0].linservicerequest == 'null'
  ) {
    linservicerequest = 'No Service Request Provided';
  } else {
    linservicerequest =
      dataByJob[individualRow.lbrlinecode][0].linservicerequest;
  }
  if (
    dataByJob[individualRow.lbrlinecode][0].lincause == '' ||
    dataByJob[individualRow.lbrlinecode][0].lincause == null ||
    dataByJob[individualRow.lbrlinecode][0].lincause == 'null'
  ) {
    lincause = 'No Cause Provided';
  } else {
    lincause = dataByJob[individualRow.lbrlinecode][0].lincause;
  }
  if (
    dataByJob[individualRow.lbrlinecode][0].linstorytext == '' ||
    dataByJob[individualRow.lbrlinecode][0].linstorytext == null ||
    dataByJob[individualRow.lbrlinecode][0].linstorytext == 'null'
  ) {
    linstorytext = 'No Story Text Provided';
  } else {
    linstorytext = dataByJob[individualRow.lbrlinecode][0].linstorytext;
  }
  const counter = 1;

  return (
    <>
      <Table
        className={classes.table}
        style={{
          border: '1px solid #eee'
        }}
      >
        <TableBody>
          <StyledTableRow>
            <StyledTableCell
              className={classes.stickyRow}
              style={{
                color: 'black',
                borderRight: '1px solid #eee',
                width: '120px'
              }}
              stickyHeader
              rowspan={dataByJob[individualRow.lbrlinecode].length + 1}
            >
              <label style={{ position: 'sticky' }}>
                Job Line : {individualRow.lbrlinecode}
              </label>
            </StyledTableCell>

            <StyledTableRow
              style={{
                display: 'block',
                width: '100%'
              }}
            >
              <StyledTableCell
                colspan="10"
                style={{
                  //  background: '#adbab9',
                  display: 'block',
                  width: '100%',
                  padding: '15px',
                  backgroundColor: '#adbab930'
                }}
              >
                <ExpansionPanel>
                  <ExpansionPanelSummary
                    style={{ backgroundColor: '#adbab930' }}
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                  >
                    <Typography className={classes.heading}>
                      Job Description
                    </Typography>
                  </ExpansionPanelSummary>
                  <ExpansionPanelDetails
                    style={{ display: 'inline-block !important' }}
                  >
                    <MExpansionPanel
                      square
                      expanded={expanded === 'panel1'}
                      onChange={handleChange('panel1')}
                    >
                      <MExpansionPanelSummary
                        aria-controls="panel1d-content"
                        id="panel1d-header"
                        expandIcon={<ExpandMoreIcon />}
                      >
                        <Typography> Service Request</Typography>
                      </MExpansionPanelSummary>
                      <MExpansionPanelDetails>
                        <Typography>{linservicerequest}</Typography>
                      </MExpansionPanelDetails>
                    </MExpansionPanel>
                    <MExpansionPanel
                      square
                      expanded={expanded === 'panel2'}
                      onChange={handleChange('panel2')}
                    >
                      <MExpansionPanelSummary
                        aria-controls="panel2d-content"
                        id="panel2d-header"
                        expandIcon={<ExpandMoreIcon />}
                      >
                        <Typography>Cause</Typography>
                      </MExpansionPanelSummary>
                      <MExpansionPanelDetails>
                        <Typography>{lincause}</Typography>
                      </MExpansionPanelDetails>
                    </MExpansionPanel>
                    <MExpansionPanel
                      square
                      expanded={expanded === 'panel3'}
                      onChange={handleChange('panel3')}
                    >
                      <MExpansionPanelSummary
                        aria-controls="panel3d-content"
                        id="panel3d-header"
                        expandIcon={<ExpandMoreIcon />}
                      >
                        <Typography>Story Text</Typography>
                      </MExpansionPanelSummary>
                      <MExpansionPanelDetails>
                        <Typography>{linstorytext}</Typography>
                      </MExpansionPanelDetails>
                    </MExpansionPanel>
                  </ExpansionPanelDetails>
                </ExpansionPanel>
              </StyledTableCell>
            </StyledTableRow>

            <DetailsGrid
              setLbrRepeat={setLbrRepeat}
              lbrRepeat={lbrRepeatData}
              dataByJobDetails={dataByJob[individualRow.lbrlinecode][0]}
              countrLbr={countrLbr}
              partList={partList}
              setPartsArr={setPartsArr}
              partListVal={partListVal}
              partByLbr={partByLbr}
            />
          </StyledTableRow>
        </TableBody>
      </Table>
    </>
  );
};

export default SearchByROGrid;
