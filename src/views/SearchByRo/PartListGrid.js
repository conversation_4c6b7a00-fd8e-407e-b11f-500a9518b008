import React from 'react';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import { useDispatch } from 'react-redux';
import { Typography, Button } from '@material-ui/core';
import ReactHtmlParser from 'react-html-parser';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableRow from '@material-ui/core/TableRow';
import Tooltip from '@material-ui/core/Tooltip';

const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      backgroundColor: '#adbab9 !important'
      //color: '#ccc !important'
    }
  }
}))(TableRow);
const StyledTableCell = withStyles(theme => ({
  head: {},
  body: {
    fontSize: 14,
    width: '140px',
    borderBottom: 'none',
    borderRight: '1px solid #eee !important',
    borderCollapse: 'unset !important',
    lineHeight: '1.5rem',
    padding: '10px'
  }
}))(TableCell);

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 300,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b',

    backgroundColor: '#ddeaf4'
  }
}))(Tooltip);
const PartListGrid = ({ tempPart, keyPart }) => {
  const classes = useStyles();
  const dispatch = useDispatch();
  var prtPayTypeName;
  if (tempPart.prtpaytypegroup == 'C') {
    prtPayTypeName = 'Customer Pay';
  } else if (tempPart.prtpaytypegroup == 'W') {
    prtPayTypeName = 'Warranty';
  } else if (tempPart.prtpaytypegroup == 'I') {
    prtPayTypeName = 'Internal';
  } else if (tempPart.prtpaytypegroup == 'B') {
    prtPayTypeName = 'Body Shop';
  } else if (tempPart.prtpaytypegroup == 'E') {
    prtPayTypeName = 'Extended Service Plan';
  } else if (tempPart.prtpaytypegroup == 'M') {
    prtPayTypeName = 'Maintenance Plan';
  } else if (tempPart.prtpaytypegroup == 'U') {
    prtPayTypeName = 'Unallocated';
  }
  var filterByPrt;

  var filterByPrtCatDes = '';
  if (tempPart.filterByPrt == 'sale_cost') {
    filterByPrt = 'Sale and cost';

    var filterByPrtCategory;
    var opCat;
    if (
      tempPart.filterByCategoryPrt == 'prt_sale_cost_known_category_allocated'
    ) {
      filterByPrtCategory = 'Part Pay Type  =  CWIBEM';
      opCat = 'REPAIR (or) MAINTENANCE (or) COMPETITIVE';
      filterByPrtCatDes = 'Known Category <br/> Allocated';
    } else if (
      tempPart.filterByCategoryPrt ==
      'prt_sale_cost_not_categorized_or_unallocated'
    ) {
      filterByPrtCategory = 'Part Pay Type =  U or Null';
      opCat = 'Null';
      filterByPrtCatDes = 'Not Category <br/> Unallocated';
    } else if (
      tempPart.filterByCategoryPrt == 'prt_sale_cost_other_category_allocated'
    ) {
      filterByPrtCategory = 'Part Pay Type =  CWIBEM';
      opCat = 'N/A';
      filterByPrtCatDes = 'Other Category <br/> Allocated';
    } else {
      filterByPrtCategory = '';
      opCat = '';
      filterByPrtCatDes = '';
    }
  } else if (tempPart.filterByPrt == 'no_sale_no_cost') {
    filterByPrt = 'No sale and No cost';
  } else if (tempPart.filterByPrt == 'sale_but_no_cost') {
    filterByPrt = 'Sale but no cost';
  } else if (tempPart.filterByPrt == 'cost_but_no_sale') {
    filterByPrt = 'Cost but no Sale';
  } else if (tempPart.filterByPrt == 'sale_equal_cost') {
    filterByPrt = 'Sale equal cost';
  }
  var prtunitsale = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(Number(tempPart.prtunitsale).toFixed(2));
  var prtunitcost = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(Number(tempPart.prtunitcost).toFixed(2));
  var prtextendedsale = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(Number(tempPart.prtextendedsale).toFixed(2));
  var prtextendedcost = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(Number(tempPart.prtextendedcost).toFixed(2));
  const windowWidth = window.innerWidth;
  return (
    <TableBody>
      <StyledTableRow>
        <StyledTableCell
          padding="checkbox"
          colspan="2"
          style={{ width: windowWidth <= 1408 ? '18%' : '17%' }}
        >
          <span style={{ float: 'left', marginRight: '10px' }}>
            <b>{keyPart + 1}.</b>
          </span>
          <label style={{ fontWeight: 'bold' }}>Parts Description: </label>
          <br />
          <span>{tempPart.prtdesc}</span>
        </StyledTableCell>
        <StyledTableCell style={{ width: '11%' }}>
          <label style={{ fontWeight: 'bold' }}> Parts Pay Type: </label>
          <br />
          {tempPart.prtpaytype} - {prtPayTypeName}{' '}
        </StyledTableCell>
        <StyledTableCell style={{ width: '13%' }}>
          <label style={{ fontWeight: 'bold' }}>
            {' '}
            Parts Sale: <br />
          </label>
          {prtunitsale}
        </StyledTableCell>
        <StyledTableCell style={{ width: '10%' }}>
          <label style={{ fontWeight: 'bold' }}>
            {' '}
            Parts Cost: <br />
          </label>
          {prtunitcost}
        </StyledTableCell>
        <StyledTableCell style={{ width: '10%' }}>
          <label style={{ fontWeight: 'bold' }}> Quantity: </label>
          {tempPart.quantity}
        </StyledTableCell>
        <StyledTableCell
          style={{ width: windowWidth <= 1408 ? '13.5%' : '9.5%' }}
        >
          <label style={{ fontWeight: 'bold' }}>
            {' '}
            Parts Extended Sale:
            <br />
          </label>
          {prtextendedsale}
        </StyledTableCell>
        <StyledTableCell
          style={{ width: windowWidth <= 1408 ? '8%' : '12.5%' }}
        >
          <label style={{ fontWeight: 'bold' }}> Parts Extended Cost:</label>
          <br />
          {prtextendedcost}
        </StyledTableCell>
        <StyledTableCell
          colspan="2"
          rowspan="2"
          style={{ textAlign: 'center' }}
        >
          {tempPart.filterByPrt == 'sale_cost' ? (
            <HtmlTooltip
              title={
                <React.Fragment>
                  <Typography color="inherit">
                    {' '}
                    <label>
                      Parts Sale {'<>'} Parts Cost and Parts Sale {'>'} 0 and
                      Parts Cost {'>'} 0
                    </label>
                    <hr /> {filterByPrtCategory}
                    <hr />
                    <label>
                      OP Category :<br />
                      {opCat}
                    </label>
                  </Typography>
                </React.Fragment>
              }
            >
              <Button>
                <img
                  style={{ marginLeft: '5px', width: '20px', height: '20px' }}
                  src="./images/verifyimages/checked.png"
                />
                <img
                  style={{ marginLeft: '5px', width: '20px', height: '20px' }}
                  src="./images/verifyimages/info.png"
                />
              </Button>
            </HtmlTooltip>
          ) : tempPart.filterByPrt == 'no_sale_no_cost' ? (
            <HtmlTooltip
              title={
                <React.Fragment>
                  <Typography color="inherit">
                    {' '}
                    <label>Parts Sale = 0 and Parts Cost = 0</label>
                  </Typography>
                </React.Fragment>
              }
            >
              <Button>
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/cross.png"
                />
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/info.png"
                />
              </Button>
            </HtmlTooltip>
          ) : tempPart.filterByPrt == 'sale_but_no_cost' ? (
            <HtmlTooltip
              title={
                <React.Fragment>
                  <Typography color="inherit">
                    {' '}
                    <label>
                      Parts Sale {'>'}0 and Parts Cost {'<='} 0
                    </label>
                  </Typography>
                </React.Fragment>
              }
            >
              <Button>
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/cross.png"
                />
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/info.png"
                />
              </Button>
            </HtmlTooltip>
          ) : tempPart.filterByPrt == 'cost_but_no_sale' ? (
            <HtmlTooltip
              title={
                <React.Fragment>
                  <Typography color="inherit">
                    {' '}
                    <label>
                      Parts Sale {'<='} 0 and Parts Cost {'>'} 0{' '}
                    </label>
                  </Typography>
                </React.Fragment>
              }
            >
              <Button>
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/cross.png"
                />
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/info.png"
                />
              </Button>
            </HtmlTooltip>
          ) : tempPart.filterByPrt == 'sale_equal_cost' ? (
            <HtmlTooltip
              title={
                <React.Fragment>
                  <Typography color="inherit">
                    {' '}
                    <label>
                      Labor Sale {'>'} 0 and Labor Cost {'>'} 0 and Labor Sale =
                      Labor Cost
                    </label>
                  </Typography>
                </React.Fragment>
              }
            >
              <Button>
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/cross.png"
                />
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/info.png"
                />
              </Button>
            </HtmlTooltip>
          ) : (
            <HtmlTooltip
              title={
                <React.Fragment>
                  <Typography color="inherit">
                    {' '}
                    <label>
                      Parts Sale {'>'} 0 and Parts Cost {'<='} 0 and Parts Sale
                      = Parts Cost
                    </label>
                  </Typography>
                </React.Fragment>
              }
            >
              <Button>
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/cross.png"
                />
                <img
                  style={{ marginLeft: '5px', width: '17px', height: '17px' }}
                  src="./images/verifyimages/info.png"
                />
              </Button>
            </HtmlTooltip>
          )}
          <br />
          {ReactHtmlParser(filterByPrt)}

          <br />
          <p style={{ fontWeight: '200 !important' }}>
            {ReactHtmlParser(filterByPrtCatDes)}
          </p>
        </StyledTableCell>
      </StyledTableRow>
    </TableBody>
  );
};

export default PartListGrid;
