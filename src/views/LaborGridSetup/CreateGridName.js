import * as React from 'react';

import AddIcon from '@material-ui/icons/Add';
import styled from 'styled-components';
import * as Yup from 'yup';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import CheckCircleIcon from '@material-ui/icons/CheckCircleOutlined';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/core/styles';
import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Checkbox,
  Typography,
  FormControlLabel,
  DialogActions,
  CircularProgress,
  Backdrop
} from '@material-ui/core';
import { createOrUpdateGridName } from 'src/utils/hasuraServices';
import useLaborGrid from './useLaborGrid';
import EditIcon from '@material-ui/icons/Edit';
const FormErrorMsg = styled.div`
  color: red;
  font-size: 12px;
  text-overflow: ellipsis;
  max-width: 100%;
  padding-top: 10px;
`;
export default function CreateGridName(props) {
  const [open, setOpen] = React.useState(false);
  const [statusMessage, setStatusMessage] = React.useState('');
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [errorMsg, setErrorMsg] = React.useState('');
  const [isSuccessful, setIsSuccessful] = React.useState(true);
  const [openDefault, setOpenDefault] = React.useState(false);
  const [defaultConfirmed, setDefaultConfirmed] = React.useState(false);
  const [loading, setLoading] = React.useState(false);

  const {
    fetchGridOptions,
    onGridNameSubmit,
    showSnackbar,
    isGridModal,
    hasDefaultType,
    defaultGridType,
    gridDetails
  } = props;
  const classes = makeStyles();

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setDefaultConfirmed(false);
    setOpen(false);
    setErrorMsg('');
  };

  const handleDialogClose = () => {
    setOpenDefault(false);
    setDefaultConfirmed(true);
  };

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setSubmitting(true);
    try {
      const gridName = values.gridName.trim();
      if (!gridName || /\s{2,}/.test(gridName)) {
        setErrorMsg('Invalid Grid Name');
      } else {
        submitGridName(gridName, values.isDefaultGrid);
        setErrorMsg('');
      }
    } catch (error) {
      console.error('Failed to submit form:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const submitGridName = async (gridName, isDefaultGrid) => {
    setLoading(true);
    const input = {
      inOldGridName: props.isEdit ? props.gridName : gridName,
      inNewGridName: gridName,
      // inIsDefaultGridName: props.isEdit ? defaultGridType : isDefaultGrid, // Pass checkbox value isDefaultGrid
      inIsDefaultGridName: props.isEdit
        ? gridDetails &&
          !gridDetails.isDefaultGridType &&
          gridDetails.gridCount > 0
          ? isDefaultGrid
          : defaultGridType
        : isDefaultGrid,
      inActivity: props.isEdit ? 'Update' : 'Insert'
    };
    createOrUpdateGridName(input, result => {
      if (result && result[0] && result[0].status === 1) {
        if (props.isEdit) {
          showSnackbar(result[0].msg, true);
        } else {
          setIsSuccessful(true);
          if (onGridNameSubmit) {
            onGridNameSubmit(gridName);
          }
        }
        handleClose();
        fetchGridOptions();
      } else {
        if (props.isEdit) {
          showSnackbar(result[0].msg, false);
        } else {
          setIsSuccessful(false);
        }
      }
      setLoading(false);
      if (!props.isEdit) {
        setStatusMessage(result[0].msg);
        setOpenSnackbar(true);
      }
    });
  };

  const validateGridName = value => {
    if (!value) {
      return 'Please enter Grid Name';
    }

    const trimmedValue = value.trim();
    if (trimmedValue !== value) {
      return 'Grid Name cannot start or end with spaces';
    }

    if (/\s{2,}/.test(value)) {
      return 'Grid Name cannot contain multiple consecutive spaces';
    }

    // if (/[^a-zA-Z0-9 ]/.test(value)) {
    //   return 'Grid Name cannot contain special characters';
    // }

    if (trimmedValue.length > 30) {
      return 'Grid Name cannot be longer than 30 characters';
    }

    return undefined;
  };
  const validationSchema = Yup.object({
    gridName: Yup.string().test('gridName', function(value) {
      const error = validateGridName(value);
      return error ? this.createError({ message: error }) : true;
    })
  });

  return (
    <>
      {isGridModal ? (
        ''
      ) : //removed the Add new grid name button from here as we are not using it now.
      // <Button
      //   onClick={handleClickOpen}
      //   size="small"
      //   sx={{ borderRadius: 0, mt: 2 }}
      //   type="submit"
      //   variant="contained"
      //   color="primary"
      //   className={clsx('reset-btn', classes.btnDiv)}
      // >
      //   Add new grid name
      // </Button>
      props.isEdit ? (
        <Tooltip title="Edit">
          <IconButton onClick={handleClickOpen} size="small" color="primary">
            <EditIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Add Grid Name">
          <IconButton onClick={handleClickOpen}>
            <AddIcon />
          </IconButton>
        </Tooltip>
      )}
      <Dialog
        fullWidth
        maxWidth="sm"
        open={open}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick') {
            handleClose();
          }
        }}
      >
        <DialogTitle>
          {props.isEdit ? 'Edit Grid Name' : 'Add Grid Name'}
        </DialogTitle>
        <DialogContent style={{ overflow: 'hidden' }}>
          <Formik
            initialValues={{
              gridName: props.isEdit ? props.gridName : '',
              showField: '',
              isDefaultGrid: false
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting, values, setFieldValue, initialValues }) => (
              <>
                <Form>
                  <Grid container spacing={2} direction="column">
                    <Grid item>
                      <Field
                        name="gridName"
                        as={TextField}
                        variant="outlined"
                        size="small"
                        fullWidth
                        style={{ height: '40px' }}
                        onChange={e => {
                          let value = e.target.value;
                          value = value.replace(/\s{2,}/g, ' ');
                          if (value[0] === ' ') {
                            value = value.substring(1);
                          }
                          setFieldValue('gridName', value);
                        }}
                      />
                      <ErrorMessage name="gridName" component={FormErrorMsg} />
                    </Grid>
                    {props.isEdit &&
                      gridDetails &&
                      !gridDetails.isDefaultGridType &&
                      gridDetails.gridCount > 0 && (
                        <Grid item>
                          <FormControlLabel
                            control={
                              <Field
                                type="checkbox"
                                name="isDefaultGrid"
                                as={Checkbox}
                                color="primary"
                              />
                            }
                            label="Set as Default Grid"
                          />
                        </Grid>
                      )}

                    <Grid item container justifyContent="flex-end" spacing={2}>
                      <Grid item>
                        <Button
                          onClick={handleClose}
                          disabled={isSubmitting}
                          variant="contained"
                          color="primary"
                          className={clsx('reset-btn', classes.btnDiv)}
                        >
                          Cancel
                        </Button>
                      </Grid>
                      <Grid item>
                        <Button
                          type="submit"
                          disabled={
                            isSubmitting ||
                            (values.gridName === initialValues.gridName &&
                              values.isDefaultGrid ===
                                initialValues.isDefaultGrid)
                          }
                          variant="contained"
                          color="primary"
                          className={clsx('reset-btn', classes.btnDiv)}
                          style={{
                            width: 'fit-content',
                            minWidth: 0,
                            fontSize: '14px',
                            height: '24px' // Set a fixed height for the button
                          }}
                        >
                          Submit
                        </Button>
                      </Grid>
                    </Grid>
                  </Grid>
                </Form>
              </>
            )}
          </Formik>
          {loading && (
            <Backdrop
              open={loading}
              style={{
                zIndex: 1301,
                color: '#fff',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%'
              }}
            >
              <CircularProgress color="inherit" />
            </Backdrop>
          )}
        </DialogContent>
      </Dialog>
      <SuccessSnackbar
        open={openSnackbar}
        onClose={() => setOpenSnackbar(false)}
        msg={statusMessage}
        goalFail={!isSuccessful}
        autoHideDuration={6000}
      />
    </>
  );
}
