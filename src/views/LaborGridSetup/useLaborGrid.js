import moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { patterns } from 'src/utils/constants';
import {
  GetLaborGridList,
  getGridorMatrixDetails,
  GetGridTypeOptions,
  GetOpcodesList,
  InsertOrUpdatePartsMatrix,
  fetchKpiScorecardPartsMatrix,
  fetchKpiScorecardPartsMatrixDetails,
  getGridorMatrixPayTypeDetailsReport,
  getGridorMatrixPayTypesByGridType,
  getMatrixType,
  partsMatrixFileUploadNew,
  GetGridTypes,
  gridTypeFileUpload,
  CreateGridWithDoorRate,
  InsertUpdateLaborGrid,
  createOrUpdateGridName
} from 'src/utils/hasuraServices';

const useLaborGrid = props => {
  const getAutoExpandState = () => {
    if (
      props &&
      props.props &&
      props.props.history &&
      props.props.history.location &&
      props.props.history.location.state !== undefined &&
      props.props.history.location.state !== null
    ) {
      return true;
    }
    return false;
  };

  const [tempData, setTempData] = useState([]);
  const [rowData, setRowData] = useState(null);
  const [addGrid, setAddGrid] = useState('');
  const [gridTypeList, setGridTypeList] = useState([]);
  const [saveClickedFlag, setSaveClickedFlag] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');
  const [enableSave, setEnableSave] = useState(false);
  const [incompleteError, setIncompleteError] = useState(false);
  const [invalidError, setInvalidError] = useState(false);
  const [installDate, setInstallDate] = useState();
  const [gridType, setGridType] = useState();
  const [listLoading, setListLoading] = useState(true);
  const [installDateValue, setInstallDateValue] = useState();
  const [updateDate, setUpdateDate] = useState(false);
  const [disableSubmitButton, setDisableSubmitButton] = useState(false);
  const [isSuccessful, setIsSuccessful] = useState(true);
  const [msgType, setMsgType] = useState('success');
  const [statusMessageType, setStatusMessageType] = useState('');
  const [opcodeListChoice, setOpcodeListChoice] = useState([]);
  const [gridTypeChoice, setGridTypeChoice] = useState([]);
  const [edit, setEdit] = useState(false);
  const [gridValidationMsg, setGridValidationMsg] = useState('');
  const [invalidLength, setInvalidLength] = useState(false);
  const [expandedAccordionIndex, setExpandedAccordionIndex] = useState(null);
  const [autoExpand, setAutoExpand] = useState(getAutoExpandState());
  const [isEditValueChanged, setIsEditValueChanged] = useState(false);
  const [gridTypeDetails, setGridTypeDetails] = useState([]);
  const [hasDefaultType, setHasDefaultType] = useState(false);
  const [defaultGridType, setDefaultGridType] = useState('');
  const [openDlg, setOpenDlg] = useState(false);
  const [updatedDefaultGridType, setUpdatedDefaultGridType] = useState('');
  const [gridTypeChanged, setGridTypeChanged] = useState(false);
  const [isLoadingGridType, setIsLoadingGridType] = useState(true);
  const [newDefaultGridType, setNewDefaultGridType] = useState(null);
  const [snackbarMessages, setSnackbarMessages] = useState();
  const [noOfRows, setNoOfRows] = useState(16);
  const [reassignGridType, setReassignGridType] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const onRowEditingStopped = params => {
    let newArray = [...tempData];
    tempData.map((obj, index) => {
      if (params.data.hours === obj.hours) newArray[index] = params.data;
    });
    setTempData(newArray);
  };

  const getLaborGridList = getInput => {
    setEnableSave(false);
    setEdit(false);
    GetLaborGridList(getInput, result => {
      if (result) {
        if (getInput.callType === 'Grid_Type') {
          setGridTypeList(result);
          if (autoExpand) {
            //code  for auto expand accordion
            const index = result.findIndex(
              item =>
                item.createdDate ===
                  props.props.history.location.state.gridDate &&
                item.gridType === props.props.history.location.state.gridType &&
                item.gridFor ===
                  props.props.history.location.state.selectedGridFor
            );
            const item = result[index];
            if (index !== -1) {
              getLaborGridList({
                callType: 'Grid',
                gridType: item.gridType,
                gridFor: item.gridFor,
                createdDate: item.createdDate
              });

              setExpandedAccordionIndex(index);
              setAutoExpand(false);
            }
          }
        } else {
          setRowData(result);
          setNoOfRows(result.length);
        }
        setListLoading(false);
      }
    });
  };
  const getSnackbarForGrid = results => {
    results.forEach(result => {
      if (result.code == 1) {
        setIsSuccessful(true);
        setMsgType('success');

        setStatusMessage(`${result.message}`);
      } else {
        setIsSuccessful(false);
        setMsgType('error');
        setStatusMessage(result.message || 'Operation completed');
      }
    });
    return true;
  };

  const handleSubmitLaborGrid = async (values, pCall) => {
    let isSuccess = false;
    let response = [];
    setReassignGridType(false);
    if (pCall === 'fixed_rate') {
      setDisableSubmitButton(true);
      const pValData = {
        inCreatedDate: moment(values.installDate).format('MM-DD-YYYY'),
        inGridType:
          values.gridFor === 'Opcode' ? values.opcode : values.gridType,
        inDoorRate: values.fixedRate,
        inGridFor: values.gridFor === 'Opcode' ? 'opcode' : 'model'
      };

      CreateGridWithDoorRate(pValData, res => {
        if (res && Array.isArray(res)) {
          setSnackbarMessages(res);
          setAddGrid('');
          getLaborGridList({
            callType: 'Grid_Type'
          });
        } else {
          // Handle unexpected response structure
          setIsSuccessful(false);
          setMsgType('error');
          setStatusMessage('Unexpected response format');
        }
        setDisableSubmitButton(false);
      });
    } else if (pCall === 'upload') {
      setDisableSubmitButton(true);
      const file = values.csvFile[0];
      const base64Content = await convertFileToBase64(file);
      const pValData = {
        base64Data: base64Content,
        inFileName: values.csvFile[0].name,
        inGridType:
          values.gridFor === 'Opcode' ? values.opcode : values.gridType,
        inInstallationDate: moment(values.installDate).format('MM-DD-YYYY'),
        inTenantId: localStorage.getItem('realm'),
        inGridOrFleet: values.gridFor === 'Opcode' ? 'opcode' : 'model'
      };
      gridTypeFileUpload(
        pValData.base64Data,
        pValData.inFileName,
        pValData.inGridType,
        pValData.inInstallationDate,
        pValData.inGridOrFleet,
        result => {
          const res = JSON.parse(
            result.data
              .statelessCcPhysicalRwInsertGriddataDtlFileUploadLogMultiple.json
          );
          if (res && Array.isArray(res)) {
            // Refresh grid list after processing all results
            setSnackbarMessages(res);
            setAddGrid('');
            getLaborGridList({
              callType: 'Grid_Type'
            });
          } else {
            // Handle unexpected response structure
            setIsSuccessful(false);
            setMsgType('error');
            setStatusMessage('Unexpected response format');
          }
          setDisableSubmitButton(false);
        }
      );
    } else {
      setIncompleteError(false);
      setInvalidLength(false);
      setInvalidError(false);
      setSaveClickedFlag(true);
      const getGridType = (call, values) => {
        return call === 'delete' || call === 'update'
          ? values.gridType
          : values.gridFor === 'Opcode'
          ? values.opcode
          : values.gridType;
      };

      const getStoreInstallDate = (call, values, installDateValue) => {
        if (call === 'delete') {
          return values.storeInstallDate;
        }
        const date =
          call === 'update' ? values.storeInstallDate : values.installDate;
        return moment(date).format('MM-DD-YYYY');
      };

      const getCreatedDate = (values, installDateValue) => {
        return values.createdDate
          ? values.createdDate
          : moment(values.installDate).format('MM-DD-YYYY');
      };

      const tempInput = {
        pGridFor: values.gridFor.toLowerCase(),
        pOldGridType: getGridType(pCall, values),
        pNewGridType: getGridType(pCall, values),
        storeInstallDate: getStoreInstallDate(pCall, values, installDateValue),
        gridData:
          pCall === 'delete'
            ? rowData.map(({ __typename, ...rest }) => rest)
            : tempData,
        createdDate: getCreatedDate(values, installDateValue),
        pLaborMissType: pCall === 'insert' ? addGrid : 'grid',
        pCall: pCall,
        pConfirmFlag: pCall === 'delete' && newDefaultGridType ? 'Y' : 'N',
        pIsDefault:
          pCall === 'delete' && newDefaultGridType ? newDefaultGridType : ''
      };
      let dataForValidationCheck = tempData.map(obj => {
        delete obj.__typename;
        return obj;
      });

      let incomplete = false;
      let inValid = false;
      let gridValueLength = false;
      incomplete = dataForValidationCheck.some(obj =>
        Object.values(obj).some(value => {
          return (
            pCall !== 'delete' &&
            (value === '' || value === null || value === undefined)
          );
        })
      );

      inValid = dataForValidationCheck.some(obj =>
        Object.values(obj).some(value => {
          if (!patterns.numericWithOptionalDecimalRegex.test(value))
            return (
              value !== '' &&
              value !== null &&
              value !== undefined &&
              !patterns.numericWithOptionalDecimalRegex.test(value)
            );
        })
      );

      const regex = /^\d{1,7}(\.\d{1,2})?$/;
      gridValueLength = dataForValidationCheck.some(obj =>
        Object.values(obj).some(value => {
          return (
            value !== '' &&
            value !== null &&
            value !== undefined &&
            !regex.test(value)
          );
        })
      );

      if (gridValueLength) {
        setInvalidLength(true);
      }
      if (incomplete) {
        setIncompleteError(true);
      }
      if (inValid) {
        setInvalidError(true);
      }

      if (!incomplete && !inValid && !gridValueLength) {
        if (pCall === 'update') {
          if (isEditValueChanged) {
            setDisableSubmitButton(true);
            InsertUpdateLaborGrid(tempInput, res => {
              if (res && res[0] && res[0].rStatus == 1) {
                getLaborGridList({
                  callType: 'Grid_Type'
                });
                setIsSuccessful(true);
                setMsgType('success');
                setAddGrid('');
                setEnableSave(false);
                setEdit(false);
              } else {
                setIsSuccessful(false);
                setMsgType('error');
              }
              setOpenSnackbar(true);
              setStatusMessage(res && res[0] ? res[0].msg : 'Error');
              setDisableSubmitButton(false);
            });
          } else {
            setOpenSnackbar(true);
            setStatusMessage('Please make some changes to update');
            console.log('Please make some changes to update');
            setMsgType('warning');
            setDisableSubmitButton(false);
          }
          setIsEditValueChanged(false);
        } else if (pCall === 'insert') {
          setDisableSubmitButton(true);
          InsertUpdateLaborGrid(tempInput, res => {
            if (res && Array.isArray(res)) {
              setSnackbarMessages(res);
              setAddGrid('');
              getLaborGridList({
                callType: 'Grid_Type'
              });
            } else {
              // Handle unexpected response structure
              setIsSuccessful(false);
              setMsgType('error');
              setStatusMessage('Unexpected response format');
            }
            // setOpenSnackbar(true);
            setDisableSubmitButton(false);
          });
        } else {
          setDisableSubmitButton(true);
          await new Promise(resolve => {
            InsertUpdateLaborGrid(tempInput, res => {
              if (res && Array.isArray(res)) {
                response = res;
                if (res && res[0] && res[0].rStatus == 1) {
                  getLaborGridList({ callType: 'Grid_Type' });
                  setMsgType('success');
                  setAddGrid('');
                  setEnableSave(false);
                  setEdit(false);
                  setIsSuccessful(true);
                  setReassignGridType(false);
                  setStatusMessage(res[0].msg);
                  cancel();
                } else if (res && res[0] && res[0].rStatus == 2) {
                  fetchGridOrMatrixTypes('delete');
                  setReassignGridType(true);
                  setStatusMessage(res[0].msg);
                } else {
                  setIsSuccessful(false);
                  setMsgType('error');
                  setStatusMessage(res[0].msg || 'Unexpected response format');
                  setReassignGridType(false);
                }
              } else {
                setIsSuccessful(false);
                setMsgType('error');
                setStatusMessage(res[0].msg || 'Unexpected response format');
              }
              if (res && res[0] && res[0].rStatus != 2) {
                setOpenSnackbar(true);
                isSuccess = true;
              }
              setDisableSubmitButton(false);
              resolve();
            });
          });
        }
      }
    }
    return isSuccess, response;
  };
  const cancel = () => {
    setEnableSave(false);
    setEdit(false);
    setExpandedAccordionIndex(null);
  };
  const validateGridData = (data, pCall) => {
    const numericWithOptionalDecimalRegex =
      patterns.numericWithOptionalDecimalRegex;
    const maxLengthRegex = /^\d{1,4}(\.\d{1,2})?$/;

    const isIncomplete = data.some(obj =>
      Object.values(obj).some(value => pCall !== 'delete' && value === '')
    );
    const isInvalid = data.some(obj =>
      Object.values(obj).some(
        value => value !== '' && !numericWithOptionalDecimalRegex.test(value)
      )
    );

    const hasInvalidLength = data.some(obj =>
      Object.values(obj).some(
        value => value !== '' && !maxLengthRegex.test(value)
      )
    );

    let validationMessage = '';

    if (isIncomplete) {
      validationMessage = 'Please fill in all fields..';
    } else if (isInvalid) {
      validationMessage = '*Enter numeric values only.';
    } else if (hasInvalidLength) {
      validationMessage =
        '* Value should contain only four digits with up to two decimal places, e.g. 1234.56';
    }

    return validationMessage;
  };

  const convertFileToBase64 = file =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result.split(',')[1]);
      reader.onerror = error => reject(error);
    });

  const getGridTypeOptionsPromise = () =>
    new Promise((resolve, reject) => {
      GetGridTypes(result => {
        if (result) resolve(result);
        else reject(new Error('Failed to fetch Grid Type Options'));
      });
    });

  const fetchGridOptions = async () => {
    const fetchOpcodeListPromise = () =>
      new Promise((resolve, reject) => {
        GetOpcodesList(result => {
          if (result) resolve(result);
          else reject(new Error('Failed to fetch Grid Type Options'));
        });
      });

    try {
      const [result, opcodes] = await Promise.all([
        getGridTypeOptionsPromise(),
        fetchOpcodeListPromise()
      ]);

      const defaultGridType = result.find(item => item.isDefaultGridType);
      setHasDefaultType(defaultGridType?.isDefaultGridType);
      setDefaultGridType(defaultGridType?.value);
      const typeChoice = result.map(item => ({
        id: item.value,
        name: item.value,
        isDefaultGridType: item.isDefaultGridType
      }));
      setGridTypeChoice(typeChoice);
      const opcodeChoice = opcodes.map(item => ({
        id: item.opcode,
        name: item.opcode
      }));
      setOpcodeListChoice(opcodeChoice);
    } catch (error) {
      console.log(error);
    } finally {
      setListLoading(false);
      // setSourceLoading(false);
    }
  };

  const fetchGridOrMatrixTypes = async operation => {
    try {
      if (operation != 'delete') {
        setOpenDlg(true);
        setIsLoadingGridType(true);
      }

      const result = await getGridTypeOptionsPromise();
      setGridTypeDetails(result);
      const typeChoice = result.map(item => ({
        id: item.value,
        name: item.value,
        isDefaultGridType: item.isDefaultGridType
      }));
      setGridTypeChoice(typeChoice);
      if (result.length !== 0) {
        const defaultGridType = result.find(item => item.isDefaultGridType);
        setHasDefaultType(defaultGridType?.isDefaultGridType || false);
        setDefaultGridType(defaultGridType?.value || null);
      }
    } catch (error) {
      console.error('Error fetching grid types:', error);
      setIsLoadingGridType(false);
    } finally {
      setIsLoadingGridType(false);
    }
  };

  const saveDefaultGridType = async handleShowSnackbar => {
    const input = {
      inNewGridName: updatedDefaultGridType,
      inOldGridName: updatedDefaultGridType,
      inIsDefaultGridName: true,
      inActivity: 'Update'
    };
    try {
      setIsLoadingGridType(true);
      createOrUpdateGridName(input, result => {
        if (result && result[0] && result[0].status === 1) {
          handleShowSnackbar(result[0]?.msg, true);
          fetchGridOrMatrixTypes();
        } else {
          handleShowSnackbar(result[0]?.msg, false);
        }
        setIsLoadingGridType(false); // Reset the flag here
      });
    } catch (error) {
      setOpenSnackbar(true);
      handleShowSnackbar('Something went wrong, Please try again later', false);
      console.log(error);
      setIsLoadingGridType(false); // Reset the flag here
    }
  };

  return {
    setInstallDate,
    setGridType,
    setEnableSave,
    setSaveClickedFlag,
    getLaborGridList,
    onRowEditingStopped,
    setTempData,
    handleSubmitLaborGrid,
    setAddGrid,
    setRowData,
    setOpenSnackbar,
    setIncompleteError,
    setInvalidError,
    isSuccessful,
    invalidError,
    incompleteError,
    tempData,
    rowData,
    addGrid,
    gridTypeList,
    saveClickedFlag,
    openSnackbar,
    statusMessage,
    enableSave,
    installDate,
    listLoading,
    gridType,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    setStatusMessage,
    statusMessageType,
    setStatusMessageType,
    fetchGridOptions,
    getGridTypeOptionsPromise,
    gridTypeChoice,
    opcodeListChoice,
    setEdit,
    edit,
    setGridValidationMsg,
    gridValidationMsg,
    invalidLength,
    setInvalidLength,
    expandedAccordionIndex,
    setExpandedAccordionIndex,
    statusMessage,
    setStatusMessage,
    isSuccessful,
    setIsSuccessful,
    disableSubmitButton,
    isEditValueChanged,
    setIsEditValueChanged,
    saveDefaultGridType,
    msgType,
    updatedDefaultGridType,
    setUpdatedDefaultGridType,
    setGridTypeChanged,
    gridTypeChanged,
    gridTypeDetails,
    setGridTypeDetails,
    hasDefaultType,
    setHasDefaultType,
    defaultGridType,
    setDefaultGridType,
    openDlg,
    setOpenDlg,
    fetchGridOrMatrixTypes,
    isLoadingGridType,
    setIsLoadingGridType,
    newDefaultGridType,
    setNewDefaultGridType,
    noOfRows,
    setNoOfRows,
    setReassignGridType,
    reassignGridType,
    cancel,
    snackbarMessages,
    setSnackbarMessages
  };
};

export default useLaborGrid;
