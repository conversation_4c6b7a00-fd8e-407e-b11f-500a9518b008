import React, { useRef } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { Formik, Form, Field, ErrorMessage, useFormikContext } from 'formik';
import * as Yup from 'yup';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import { DropzoneArea } from 'material-ui-dropzone';
import Autocomplete from '@material-ui/lab/Autocomplete';
import {
  Box,
  Button,
  Grid,
  LinearProgress,
  Paper,
  Tooltip,
  Divider,
  Typography,
  MenuItem,
  Select,
  ListItemText
} from '@material-ui/core';
import { TextField, CardContent } from '@material-ui/core';
import { useState } from 'react';
import clsx from 'clsx';
import styled from 'styled-components';
import { CheckBox } from '@material-ui/icons';
import CreateGridName from './CreateGridName';
import CSVDownload from 'src/components/CSVDownload';
import moment from 'moment';
import { forwardRef, useImperativeHandle } from 'react';

const useStyles = makeStyles(theme => ({
  customDropzone: {},
  dropzoneImage: {
    // Your custom styles for the image preview
    width: '30px', // Example width
    height: '30px', // Example height
    objectFit: 'cover', // Example object-fit property
    borderRadius: '50%' // Example border-radius
    // Add more styles as needed
  },
  snackbarContainer: {
    '& .MuiSnackbar-root': {
      top: '30px',
      bottom: 'unset',
      left: '50%',
      transform: 'translateX(-50%)'
    }
  },
  root: {
    // Add your custom styles here
    border: '2px dashed #ccc',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center'
    // width: '90% !important'
  },

  fileError: {
    // Add your custom styles here
    border: '2px dashed #e53935',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center'
    // width: '90% !important'
  },
  icon: {
    fontSize: '48px',
    // color: 'red !important',
    marginBottom: '-8px !important',
    marginTop: '15px !important',
    height: 30
  },
  gridButtonWrapper: {
    '@media (min-width: 2305px)': {
      marginLeft: 8
    },
    '@media (min-width: 2560px)': {
      marginLeft: -15
    },
    '@media (max-width: 2304px)': {
      marginLeft: 20
    },

    '@media (min-width: 1601px) and (max-width: 1919px)': {
      marginLeft: 30
    },
    '@media (max-width: 1600px)': {
      marginLeft: 45
    },
    '@media (max-width: 1440px)': {
      marginLeft: 22
    }
  },
  editBtn: {
    marginTop: '400px',
    marginLeft: '-423px !important',
    '@media (max-width: 1280px)': {
      marginLeft: '-242px !important'
    },
    '@media (min-width: 1281px) and (max-width: 1366px)': {
      marginLeft: '-273px !important'
    },
    '@media (min-width: 1367px) and (max-width: 1440px)': {
      marginLeft: '-297px !important'
    },
    '@media (min-width: 1920px)': {
      marginLeft: '-454px !important'
    },
    '@media (min-width: 1921px) and (max-width: 2303px)': {
      marginLeft: '-580px !important'
    },
    '@media (min-width: 2048px)': { marginLeft: '-497px !important' },
    '@media (min-width: 2240px)': {
      marginLeft: '-560px !important'
    },
    '@media (min-width: 2304px)': {
      marginLeft: '-580px !important'
    },
    '@media (min-width: 2560px)': { marginLeft: '-667px !important' },
    '@media (min-width: 3200px)': { marginLeft: '-878px !important' },
    '@media (min-width: 3840px)': { marginLeft: '-1090px !important' }
  },
  gridButton: {
    '@media (max-width: 1600px)': {
      width: '75%',
      height: '70%',
      fontSize: '13px',
      marginLeft: '-19px'
    }
  }
}));

const FormErrorMsg = styled.div`
  color: red;
  font-size: 12px;
  text-overflow: ellipsis;
  max-width: 100%;
  padding-top: 10px;
`;

const GridForm = forwardRef((props, ref) => {
  const {
    handleSubmitForm,
    setAddGrid,
    addGrid,
    component,
    matrixSourceList,
    gridTypeList,
    fetchGridOptions,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    action,
    enableSave,
    cancel,
    handleAlert,
    rowData,
    gridTypeChoice,
    getGridTypesList,
    defaultGridType,
    hasDefaultType,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    opcodeListChoice,
    item,
    gridTypeData,
    disableSubmitButton
  } = props;

  const [showCustomSourceInput, setShowCustomSourceInput] = useState(false);
  const [invalidDateMsg, setInvalidDateMsg] = useState('');
  const [selectedGridType, setSelectedGridType] = useState('');
  const [submitFlag, setsubmitFlag] = useState(false);
  const classes = useStyles();
  const formikRef = useRef(null);
  const gridChoices = [
    { id: 'opcode', name: 'Opcode' },
    { id: 'model', name: 'Model' }
  ];
  const [gridName, setGridName] = useState([]);
  useImperativeHandle(ref, () => ({
    refresh: () => {
      if (formikRef.current) {
        setsubmitFlag(false);
        formikRef.current.resetForm();
        formikRef.current.setFieldValue('gridType', []);
        setGridName([]);
        setSelectedGridType('');
      }
    }
  }));

  const handleGridName = name => {
    setGridName(name);
  };
  const handleMultiSelectChange = (event, setFieldValue) => {
    const { value } = event.target;
    setGridName(value);
    setFieldValue('gridType', value);
  };

  const validationSchema = Yup.object({
    // gridFor: Yup.string().required('This is required!'),
    gridFor: Yup.mixed().test('gridFor', 'This is required!', value => {
      return action == 'edit' || (value && value.length > 0);
    }),
    opcode: Yup.mixed().test('opcode', 'This is required!', value => {
      return selectedGridType !== 'Opcode' || (value && value.length > 0);
    }),
    gridType: Yup.array()
      .of(Yup.string())
      .test('gridType', 'This is required!', value => {
        return selectedGridType !== 'Model' || (value && value.length > 0);
      }),
    installDate: Yup.date().required('This is required!'),
    csvFile: Yup.mixed().test('csvFile', 'This is required!', value => {
      return addGrid !== 'upload' || (value && value.length > 0);
    }),
    fixedRate: Yup.mixed()
      .test('fixedRate', 'This is required!', value => {
        return addGrid !== 'fixed_rate' || (value && value.length > 0);
      })
      .test('minValue', 'Value should be greater than 0', value => {
        return addGrid !== 'fixed_rate' || (value && value > 0);
      })
      .test('maxDigits', 'Value should contain only three digits with up to two decimal places (e.g. 123.45)', value => {
        return addGrid !== 'fixed_rate' || /^[0-9]{1,3}(\.\d{1,2})?$/.test(value);
      })
  });

  const handleFileChange = async (files, setFieldValue) => {
    setFieldValue('csvFile', files);
  };

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setSubmitting(true);

    try {
      if (action == 'edit') {
        const modifiedGridObject = {
          ...item,
          storeInstallDate: values.installDate
        };
        await handleSubmitForm(modifiedGridObject);
      } else {
        await handleSubmitForm(values);
      }
    } catch (error) {
      console.error('Failed to submit form:', error);
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <>
      <CardContent>
        <Formik
          innerRef={formikRef}
          initialValues={{
            // gridFor: item && item.gridType && item.gridType,
            gridFor: '',
            opcode: '',
            gridType: [],
            installDate: item && item.storeInstallDate && item.storeInstallDate,
            csvFile: '',
            fixedRate: ''
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => (
            <Form>
              <Grid container spacing={2}>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Grid Type <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <Field
                    name="gridFor"
                    as={Select}
                    variant="outlined"
                    size="small"
                    fullWidth
                    onChange={e => {
                      setFieldValue('gridFor', e.target.value);
                      setSelectedGridType(e.target.value);
                    }}
                    style={{ height: '40px' }}
                  >
                    <MenuItem disabled value="">
                      Select Grid Type
                    </MenuItem>
                    {gridChoices.map(type => (
                      <MenuItem key={type.id} value={type.name}>
                        {type.name}
                      </MenuItem>
                    ))}
                  </Field>
                  {submitFlag && (
                    <ErrorMessage name="gridFor" component={FormErrorMsg} />
                  )}
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: selectedGridType === 'Opcode' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Opcodes <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: selectedGridType === 'Opcode' ? 'block' : 'none'
                  }}
                >
                  <Field
                    name="opcode"
                    render={({ field, form }) => (
                      <Autocomplete
                        options={opcodeListChoice}
                        getOptionLabel={option => option.name}
                        onChange={(event, value) => {
                          form.setFieldValue(
                            field.name,
                            value ? value.name : ''
                          );
                        }}
                        value={
                          (opcodeListChoice &&
                            opcodeListChoice.find(
                              option => option.name === field.value
                            )) ||
                          null
                        }
                        renderInput={params => (
                          <TextField
                            {...params}
                            placeholder="Select Opcode"
                            variant="outlined"
                            size="small"
                            fullWidth
                            style={{ height: '40px' }}
                            MenuProps={{
                              PaperProps: {
                                style: {
                                  maxHeight: '350px'
                                }
                              }
                            }}
                          />
                        )}
                      />
                    )}
                  />
                  {submitFlag && (
                    <ErrorMessage name="opcode" component={FormErrorMsg} />
                  )}
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: selectedGridType === 'Model' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Grid Name <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: selectedGridType === 'Model' ? 'block' : 'none'
                  }}
                >
                  <Field
                    style={{ height: '40px' }}
                    name="gridType"
                    as={Select}
                    multiple
                    value={gridName}
                    onChange={e => handleMultiSelectChange(e, setFieldValue)}
                    variant="outlined"
                    size="small"
                    fullWidth
                    renderValue={selected => selected.join(', ')}
                    MenuProps={{
                      getContentAnchorEl: null, // Ensures the menu is aligned based on `anchorOrigin`
                      anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      PaperProps: {
                        style: {
                          marginTop: '50px', // Adjust the top margin
                          maxHeight: '350px'
                        }
                      }
                    }}
                  >
                    <MenuItem disabled value="">
                      Select Grid Name
                    </MenuItem>
                    {gridTypeChoice.map(type => (
                      <MenuItem key={type.id} value={type.name}>
                        <ListItemText primary={type.name} />
                      </MenuItem>
                    ))}
                  </Field>
                  {submitFlag && (
                    <ErrorMessage name="gridType" component={FormErrorMsg} />
                  )}
                </Grid>
                <Grid
                  xs={12}
                  sm={1}
                  style={{
                    display: selectedGridType === 'Model' ? 'block' : 'none'
                  }}
                >
                  <CreateGridName
                    isEdit={false}
                    isGridModal={false}
                    fetchGridOptions={fetchGridOptions}
                    onGridNameSubmit={name => {
                      handleGridName([name]);
                      setFieldValue('gridType', [name]);
                    }}
                    hasDefaultType={hasDefaultType}
                  />
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Store Install Date <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <DateRangePicker
                    key={
                      values.installDate
                        ? moment(values.installDate).format('MM/DD/YY')
                        : moment().format('MM/DD/YY')
                    }
                    onApply={(e, picker) => {
                      setFieldValue(
                        'installDate',
                        picker.startDate.format('MM/DD/YY')
                      );
                    }}
                    initialSettings={{
                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },
                      autoUpdateInput: true,
                      showDropdowns: true,
                      autoApply: true,
                      singleDatePicker: true,
                      startDate: values.installDate
                        ? moment(values.installDate).format('MM/DD/YY')
                        : moment().format('MM/DD/YY')
                    }}
                  >
                    <div className="custom-date-container">
                      <Field
                        name="installDate"
                        as={TextField}
                        variant="outlined"
                        size="small"
                        fullWidth
                        value={
                          values.installDate
                            ? moment(values.installDate).format('MM/DD/YY')
                            : ''
                        }
                        inputProps={{ readOnly: true }}
                      />
                    </div>
                  </DateRangePicker>
                  {submitFlag && (
                    <ErrorMessage name="installDate" component={FormErrorMsg} />
                  )}
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{ display: addGrid === 'upload' ? 'block' : 'none' }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Upload CSV File <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{ display: addGrid === 'upload' ? 'block' : 'none' }}
                  className={classes.snackbarContainer}
                >
                  <DropzoneArea
                    name="csvFile"
                    acceptedFiles={['.csv']}
                    maxFiles={1}
                    filesLimit={1}
                    dropzoneText={'Drag and drop file or click to upload'}
                    showAlerts={['error']}
                    dropzoneClass="matrix-upload"
                    showFileNames
                    classes={{
                      root: classes.root,
                      icon: classes.icon
                    }}
                    onChange={files => handleFileChange(files, setFieldValue)}
                    onAlert={handleAlert}
                  />
                  <CSVDownload typeFor={'grid'} />
                  {submitFlag && (
                    <ErrorMessage name="csvFile" component={FormErrorMsg} />
                  )}
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: addGrid === 'fixed_rate' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Door Rate <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: addGrid === 'fixed_rate' ? 'block' : 'none'
                  }}
                >
                  <Field
                    name="fixedRate"
                    as={TextField}
                    variant="outlined"
                    size="small"
                    fullWidth
                    style={{
                      '& .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input': {
                        height: '1em'
                      },
                      height: '40px'
                    }}
                    onChange={e => {
                      let value = e.target.value;
                      // Allow only numbers
                      if (!/^\d*\.?\d*$/.test(value)) return;
                      // Replace multiple spaces with a single space
                      value = value.replace(/\s{2,}/g, ' ');
                      // Trim leading spaces
                      if (value[0] === ' ') {
                        value = value.substring(1);
                      }
                      setFieldValue('fixedRate', value);
                    }}
                  />
                  {submitFlag && (
                    <ErrorMessage name="fixedRate" component={FormErrorMsg} />
                  )}
                </Grid>
              </Grid>

              <Grid
                container
                spacing={1}
                justifyContent="flex-end"
                style={{ padding: '10px' }}
                className={
                  addGrid == undefined
                    ? classes.gridButtonWrapper
                    : action == 'edit'
                    ? classes.editBtn
                    : undefined
                }
              >
                <Grid item style={{ paddingRight: '10px' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    className={clsx('reset-btn', classes.btnDiv)}
                    fullWidth
                    onClick={() => {
                      if (action !== 'edit') {
                        setAddGrid('');
                      } else {
                        cancel();
                        console.log('Call Cancel for edit');
                      }
                    }}
                    disabled={disableSubmitButton}
                  >
                    Cancel
                  </Button>
                </Grid>
                <Grid item style={{ paddingRight: '66px' }}>
                  <Button
                    variant="contained"
                    type="submit"
                    color="primary"
                    disabled={disableSubmitButton}
                    className={clsx('reset-btn', classes.btnDiv)}
                    fullWidth
                    onClick={() => setsubmitFlag(true)}
                  >
                    Submit
                  </Button>
                </Grid>
              </Grid>
            </Form>
          )}
        </Formik>
      </CardContent>
    </>
  );
});

export default GridForm;
