import { useMemo } from 'react';
import { patterns } from 'src/utils/constants';
import 'src/grid.css';

const GridDefinitions = (saveClickedFlag, noOfRows) => {
  const FormatCellValueRate = params => {
    const regex = /^\d{1,4}(\.\d{1,2})?$/;

    if (
      params.value &&
      regex.test(params.value) &&
      patterns.numericWithOptionalDecimalRegex.test(params.value)
    ) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return params.value === '' || params.value === undefined
        ? '$'
        : '$' + params.value;
    }
  };

  const SetCellStyle = () => {
    return { textAlign: 'right', border: '0px white', fontSize: '12px' };
  };

  const SetCellBorderStyle = {
    'rag-red-outer': params => {
      const regex = /^\d{1,4}(\.\d{1,2})?$/;
      console.log('saveClickedFlag', saveClickedFlag);
      return (
        (saveClickedFlag &&
          (params.data[params.colDef.field] === '' ||
            params.data[params.colDef.field] === undefined)) ||
        !patterns.numericWithOptionalDecimalRegex.test(params.value) ||
        !regex.test(params.value)
      );
    }
  };

  const columnDefs = [
    {
      headerName: 'Hours Sold',
      minWidth: 100,
      width: 100,
      field: 'hours',
      editable: false
    },
    {
      headerName: '0',
      width: 80,
      field: 'col0',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate,
      editable: params => params.node.rowIndex !== 0 //disable first row first cell
    },
    {
      headerName: '0.1',
      width: 80,
      field: 'col1',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.2',
      width: 80,
      field: 'col2',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.3',
      width: 80,
      field: 'col3',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.4',
      width: 80,
      field: 'col4',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.5',
      width: 80,
      field: 'col5',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.6',
      width: 80,
      field: 'col6',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.7',
      width: 80,
      field: 'col7',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.8',
      width: 80,
      field: 'col8',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    },
    {
      headerName: '0.9',
      width: 80,
      field: 'col9',
      cellEditor: 'agTextCellEditor',
      valueFormatter: FormatCellValueRate
    }
  ];

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      minWidth: 80,
      resizable: true,
      suppressMenu: true,
      unSortIcon: true,
      enableValue: true,
      editable: true,
      cellStyle: SetCellStyle
    };
  }, []);

  const LaborGridCols = {
    col0: '',
    col1: '',
    col2: '',
    col3: '',
    col4: '',
    col5: '',
    col6: '',
    col7: '',
    col8: '',
    col9: ''
  };

  const gridData = useMemo(() => {
    const data = Array.from({ length: 16 }, (_, index) => ({
      hours: index,
      ...LaborGridCols
    }));
    // Add 0 in the field first row first cell
    if (data.length > 0) {
      data[0].col0 = '0';
    }
    return data;
  }, [LaborGridCols]);

  return {
    columnDefs,
    defaultColDef,
    gridData,
    LaborGridCols
  };
};

export default GridDefinitions;
