import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { useDispatch, useSelector } from 'react-redux';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { setNavItems, setMenuSelected } from 'src/actions';
import AddLaborGrid from './AddLaborGrid';
import { useHistory } from 'react-router';
import {
  getGridorMatrixPayTypeDetails,
  getAllGridForDetails,
  getGridorMatrixPayTypesByGridType
} from 'src/utils/hasuraServices';
var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));
const LaborGridModule = () => {
  
  const session = useSelector(state => state.session);
  const history = useHistory();
  const dispatch = useDispatch();
  
  // useEffect(() => {
  //   dispatch(setMenuSelected('Parts Matrix(s)'));
  //   dispatch(setNavItems(['Armatus Admin']));
  // }, []);
  
  return (
    
    <Page title="Labor Grid">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <AddLaborGrid
          history={history}
        />
      )}
    </Page>
  );
};

export default withKeycloak(LaborGridModule);
