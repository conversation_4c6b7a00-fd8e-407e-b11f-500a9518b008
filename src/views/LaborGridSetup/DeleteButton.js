import React, { useEffect, useState } from 'react';
import {
  CircularProgress,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography,
  Select,
  MenuItem
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import DeleteIcon from '@material-ui/icons/DeleteOutline';

const useStyles = makeStyles({
  iconButton: {
    '&:hover': {
      backgroundColor: 'transparent'
    },
    '&:focus': {
      boxShadow: 'none'
    }
  }
});

const DeleteButton = props => {
  const {
    handleDelete,
    gridTypeData,
    cancel,
    setNewDefaultGridType,
    newDefaultGridType,
    fetchGridOrMatrixTypes,
    setReassignGridType,
    reassignGridType,
    gridOrMatrixDetails,
    getLaborGridList
  } = props;
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [gridTypes, setGridTypes] = useState();
  const [confirmLoading, setConfirmLoading] = useState(true);
  const handleClick = event => {
    setNewDefaultGridType('');

    setReassignGridType(false);
    event.preventDefault();
    event.stopPropagation();
    getLaborGridList({
      callType: 'Grid',
      gridType: gridTypeData.gridType,
      gridFor: gridTypeData.gridFor,
      createdDate: gridTypeData.createdDate
    });
    setOpen(true);
  };

  const handleDialogClose = event => {
    event.preventDefault();
    event.stopPropagation();
    setOpen(false);
  };

  const handleConfirm = async event => {
    event.stopPropagation();
    setOpen(false);
    setLoading(true);
    const success = await handleDelete(gridTypeData, 'delete');
    if (
      Array.isArray(success) &&
      success.length > 0 &&
      success[0].rStatus != 2
    ) {
      setReassignGridType(false);
      setNewDefaultGridType('');
    } else {
      setOpen(true);
    }
  };

  useEffect(() => {
    if (gridOrMatrixDetails) {
      let gridType = gridOrMatrixDetails.filter(
        item => !item.isDefaultGridType && item.gridCount != 0
      );
      setGridTypes(gridType);
    }
  }, [gridOrMatrixDetails]);

  return (
    <>
      <Tooltip title="Delete">
        <IconButton
          aria-label="delete"
          onClick={handleClick}
          className={classes.iconButton}
        >
          {/* {loading ? <CircularProgress size={24} /> : <DeleteIcon />} */}
          <DeleteIcon />
        </IconButton>
      </Tooltip>

      <Dialog
        open={open}
        onClose={handleDialogClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        onClick={event => {
          event.preventDefault();
          event.stopPropagation();
        }}
      >
        <DialogTitle id="alert-dialog-title">Delete Grid</DialogTitle>
        <DialogContent>
          {!reassignGridType && (
            <DialogContentText id="alert-dialog-description">
              Are you sure you want to delete this Grid?
            </DialogContentText>
          )}
          {reassignGridType && (
            <>
              <Typography
                variant="body2"
                color="textSecondary"
                style={{ marginBottom: 10 }}
              >
                Assign a default grid type other than{' '}
                {`'${gridTypeData.gridType}'`} before deleting the last grid.
              </Typography>
              <Select
                fullWidth
                variant="outlined"
                value={newDefaultGridType}
                onChange={e => setNewDefaultGridType(e.target.value)}
                displayEmpty
                disabled={gridTypes.length == 0}
              >
                <MenuItem value="" disabled>
                  Grid Type
                </MenuItem>

                {gridTypes &&
                  gridTypes.map(item => {
                    return (
                      <MenuItem sx={{ fontSize: '12px' }} value={item.value}>
                        {' '}
                        {item.value}{' '}
                      </MenuItem>
                    );
                  })}
              </Select>
              {gridTypes.length == 0 && (
                <Typography
                  variant="body2"
                  color="error"
                  style={{ marginTop: 10 }}
                >
                  Please add a labor grid to switch the default grid type.
                </Typography>
              )}
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>Cancel</Button>
          <Button
            onClick={handleConfirm}
            autoFocus
            disabled={reassignGridType && !newDefaultGridType}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DeleteButton;
