import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';

import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getGridorMatrixDetails,
  getGridorMatrixPeriods,
  getGridorMatrixPayTypesByGridType
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import moment from 'moment';
import { ReactSession } from 'react-client-session';
import { color } from 'highcharts';

import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { makeStyles } from '@material-ui/core/styles';
import Accordion from '@material-ui/core/Accordion';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';

import GridDefinitions from './GridDefinitions';
import EditIcon from '@material-ui/icons/Edit';
import EditGridOrMatrixNameDialog from 'src/components/EditGridOrMatrixNameDialog';
import {
  Typography,
  Paper,
  FormControl,
  Tooltip,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  FormControlLabel,
  Radio,
  RadioGroup,
  Box,
  IconButton,
  LinearProgress
} from '@material-ui/core';
import { getMatrixType, getMatrixDetail } from 'src/utils/hasuraServices';
import { setMenuSelected, setNavItems } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { i } from 'react-dom-factories';
import useLaborGrid from './useLaborGrid';
import NewLaborGrid from './NewLaborGrid';
import { set } from 'date-fns';
import DeleteButton from './DeleteButton';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import { withKeycloak } from '@react-keycloak/web';
import GridForm from './GridForm';
import MultipleSnackbars from 'src/components/MultipleSnackbars';
var lodash = require('lodash');

const Dealer = process.env.REACT_APP_DEALER;
const useStyles = makeStyles(theme => ({
  root: {
    width: '99%',
    marginLeft: 8
  },
  text: {
    fontSize: 13
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  matrixType: {
    marginTop: -42,
    marginLeft: 150
  },
  back: {
    marginRight: 13,
    float: 'right',
    width: 'auto',
    marginTop: -40
  },
  loaderGrid: {
    marginLeft: 450,
    marginTop: 120
  },
  header: {
    textTransform: 'none',
    fontSize: 13,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  headerValue: {
    textTransform: 'none',
    fontSize: 13,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#7987a1',
    fontSize: 13
  },
  iconButton: {
    '&:hover': {
      backgroundColor: 'transparent'
    },
    '&:focus': {
      boxShadow: 'none'
    }
  },
  laborGrids: {
    '@media (min-width: 2560px)': {
      paddingLeft: '4% !important'
    },
    '@media (min-width: 2305px) and (max-width: 2559px)': {
      paddingLeft: '6% !important'
    },
    '@media (min-width: 1920px) and (max-width: 2304px)': {
      paddingLeft: '4% !important'
    },
    '@media (min-width: 1601px) and (max-width: 1919px)': {
      paddingLeft: '6% !important'
    },
    '@media (max-width: 1600px)': {
      paddingLeft: '3% !important'
    }
  }
}));

const LaborGridPricing = props => {
  const gridRef = useRef();
  const classes = useStyles();
  const [initialRowData, setInitialRowData] = useState();
  const session = useSelector(state => state.session);
  const ref = useRef(null);
  const {
    onRowEditingStopped,
    setTempData,
    tempData,
    getLaborGridList,
    setOpenSnackbar,
    setEnableSave,
    setIncompleteError,
    handleSubmitLaborGrid,
    setAddGrid,
    setSaveClickedFlag,
    setInvalidError,
    setInstallDate,
    setRowData,
    setGridType,
    gridType,
    invalidError,
    statusMessage,
    openSnackbar,
    rowData,
    addGrid,
    gridTypeList,
    saveClickedFlag,
    enableSave,
    incompleteError,
    installDate,
    listLoading,
    installDateValue,
    setInstallDateValue,
    setStatusMessage,
    updateDate,
    setUpdateDate,
    statusMessageType,
    setStatusMessageType,
    getGridTypesList,
    // defaultGridType,
    // hasDefaultType,
    saveDefaultGridType,
    setUpdatedDefaultGridType,
    updatedDefaultGridType,
    setOpenGridTypeModal,
    openGridTypeModal,
    setGridTypeChanged,
    gridTypeChanged,
    fetchGridOptions,
    getGridTypeOptionsPromise,
    gridTypeChoice,
    opcodeListChoice,
    setEdit,
    invalidLength,
    edit,
    setGridValidationMsg,
    gridValidationMsg,
    validationErrors,
    expandedAccordionIndex,
    setExpandedAccordionIndex,
    isSuccessful,
    msgType,
    setIsSuccessful,
    showAllJobs,
    setShowAllJobs,
    setInvalidLength,
    disableSubmitButton,
    isEditValueChanged,
    setIsEditValueChanged,
    gridTypeDetails,
    setGridTypeDetails,
    hasDefaultType,
    setHasDefaultType,
    defaultGridType,
    setDefaultGridType,
    openDlg,
    setOpenDlg,
    fetchGridOrMatrixTypes,
    isLoadingGridType,
    setIsLoadingGridType,
    newDefaultGridType,
    setNewDefaultGridType,
    noOfRows,
    setNoOfRows,
    setReassignGridType,
    reassignGridType,
    cancel,
    snackbarMessages,
    setSnackbarMessages
  } = useLaborGrid({ props });
  const {
    columnDefs,
    defaultColDef,
    gridData,
    LaborGridCols
  } = GridDefinitions(saveClickedFlag, noOfRows);
  const [showBackBtn, setShowBackBtn] = useState(true);
  const accordionRefs = useRef([]);
  const firstRender = useRef(true);
  const [parent, setParent] = useState('grid');

  const handleCancelDlg = () => {
    setOpenDlg(false);
    setGridTypeChanged(false);
    setUpdatedDefaultGridType('');
  };

  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
    } else {
      setShowBackBtn(false);
      setExpandedAccordionIndex(null);
    }
    getLaborGridList({
      callType: 'Grid_Type'
    });
    setAddGrid('');
  }, [session.storeSelected]);

  const handleRadioChange = async event => {
    fetchGridOptions();
    setExpandedAccordionIndex(null);
    setAddGrid(event.target.value);
    setInstallDateValue('');
    setUpdateDate(false);
    setNoOfRows(16);
    ref && ref.current && ref.current.refresh();
    if (event.target.value === 'grid') {
      setSaveClickedFlag(false);
      setRowData(null);
      setIncompleteError(false);
      setInvalidError(false);
      setInvalidLength(false);
    } else if (event.target.value === 'upload') {
    } else if (event.target.value === 'fixed_rate') {
    }
  };
  const onGridReady = params => {
    gridRef.current = params.api;
    setInitialRowData(gridData);
    setTempData(gridData);
    !rowData &&
      setTimeout(() => {
        let rowIndex = 0;
        gridRef.current.setFocusedCell(rowIndex, 'col1');
        gridRef.current.startEditingCell({
          rowIndex,
          colKey: 'col1'
        });
      }, 100);
  };
  const handleEditButton = () => {
    setEdit(true);
    setSaveClickedFlag(true);
    gridRef.current.refreshCells({ force: true });
    let rowIndex = 0;
    gridRef.current.setFocusedCell(0, 'col1');
    gridRef.current.startEditingCell({
      rowIndex,
      colKey: 'col1'
    });
    setTempData(rowData);
  };

  // const cancel = () => {
  //   setEnableSave(false);
  //   setEdit(false);
  //   setExpandedAccordionIndex(null);
  // };

  const handleAlert = alert => {
    if (alert.includes('removed')) {
      setOpenSnackbar(true);
      setStatusMessage(alert);
    }
  };
  const hidesnackbar = () => {
    setOpenSnackbar(false);
  };
  const getGridWrapper = (action, RowData) => {
    return (
      <>
        <div
          className="ag-theme-balham"
          style={{
            height: '483px',
            width: '91% !important',
            alignContent: 'center',
            marginLeft: '8px'
          }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            onGridReady={onGridReady}
            rowData={action === 'add' ? initialRowData : RowData}
            defaultColDef={defaultColDef}
            rowSelection="single"
            animateRows={true}
            ref={gridRef}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            onRowEditingStopped={onRowEditingStopped}
            stopEditingWhenGridLosesFocus={true}
            onRowEditingStarted={() => setEnableSave(true)}
            suppressClickEdit={enableSave ? false : true}
            suppressDragLeaveHidesColumns={true}
            suppressMovableColumns={true}
            suppressKeyboardEvent={params => {
              return true;
            }}
            onCellValueChanged={params => {
              if (action !== 'add') {
                setIsEditValueChanged(true);
              }
            }}
          />
        </div>
      </>
    );
  };

  const formatCellValueDescription = gridTypeData => {
    return gridTypeData.gridType.length > 150
      ? gridTypeData.gridType.substring(0, 150) + `...`
      : gridTypeData.gridType;
  };
  const formatCellValueDate = params => {
    if (params != null && params != 0) {
      return moment(params).format('MM/DD/YY');
    }
  };
  const addOrRemoveGridRow = (action, rowCount) => {
    let dummy;
    if (action === 'add') {
      dummy = [...tempData, { hours: rowCount - 1, ...LaborGridCols }];
      setInitialRowData(dummy);
      setTempData(dummy);
      setRowData(dummy);
    } else if (action === 'remove') {
      dummy = tempData.filter(item => item.hours !== noOfRows - 1);
      setInitialRowData(dummy);
      setTempData(dummy);
      setRowData(dummy);
      setIsEditValueChanged(true);
    }
  };

  const cancelGridAddition = () => {
    setAddGrid('');
    setExpandedAccordionIndex(null);
    setInstallDateValue('');
    setUpdateDate(false);
    setNoOfRows(16);
    setRowData(null);
  };

  const handleclick = params => {
    props.history.push({
      pathname:
        props.history.location.state &&
        props.history.location.state.pageType == 'FleetAccounts'
          ? '/FleetAccounts'
          : localStorage.getItem('versionFlag') == 'TRUE'
          ? '/LaborMisses'
          : '/LaborGridMisses',
      state: {
        selectedFilter: props.history.location.state.selectedFilter,
        selectedToggle: props.history.location.state.selectedToggle,
        selectedMonthYear: props.history.location.state.selectedMonthYear,
        parent: props.history.location.state.parent,
        previousToggle: props.history.location.state.previousToggle,
        payType: props.history.location.state.payType,
        gridType:
          props.history.location.state.pageType != 'FleetAccounts'
            ? props.history.location.state.gridType
            : props.history.location.state.gridRateValueStart,
        previousPayType: props.history.location.state.PrevPayType,
        previousGridType: props.history.location.state.PrevGridType,
        showAllJobs: props.history.location.state.showAllJobs
          ? props.history.location.state.showAllJobs
          : false,
        filterStart: props.history.location.state.filterStart,
        filterEnd: props.history.location.state.filterEnd,
        selectedGridType: props.history.location.state.selectedGridType,
        customerName: props.history.location.state.customerName,
        // ? props.history.location.state.customerName
        // : props.history.location.state.fleetName,
        selectedPayType: props.history.location.state.selectedPayType,
        selectedOpcode: props.history.location.state.selectedOpcode
      }
    });
  };
  function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }
  useEffect(() => {
    // Scroll the expanded accordion into view when the data list or loading state changes
    if (accordionRefs.current[expandedAccordionIndex]) {
      setTimeout(() => {
        accordionRefs.current[expandedAccordionIndex] &&
          accordionRefs.current[expandedAccordionIndex].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
      }, 1000);
    }
  }, [gridTypeList, listLoading]);

  const getName = gridFor => {
    switch (gridFor) {
      case 'opcode':
        return 'Opcode Name';
      case 'model':
        return 'Grid Name';
      case 'opcode_fleet':
        return 'Fleet Opcode';
      case 'paytype_fleet':
        return 'Fleet Pay Type';
      case 'cust_fleet':
        return 'Fleet Customer';
      default:
        return '';
    }
  };

  const getPadding = gridFor => {
    switch (gridFor) {
      case 'opcode':
        return '7px';
      case 'model':
        return '27px';
      case 'opcode_fleet':
        return '12px';
      case 'paytype_fleet':
        return '5px';
      default:
        return '0px'; // Default padding if none of the cases match
    }
  };

  return (
    <>
      <SuccessSnackbar
        open={openSnackbar}
        onClose={() => hidesnackbar()}
        msg={statusMessage}
        goalFail={isSuccessful ? false : true}
        autoHideDuration={6000}
        type={msgType}
      />
      {snackbarMessages?.length > 0 && (
        <MultipleSnackbars
          messages={snackbarMessages}
          setSnackbarMessages={setSnackbarMessages}
        />
      )}
      {disableSubmitButton && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(255, 255, 255, 0.91)',
            zIndex: '1000',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </div>
      )}
      <Page title="Labor Grid(s)">
        <Paper
          square
          style={{
            // margin: 8,
            marginTop: '40px',
            paddingTop: '6px',
            height: '40px',
            margin: '8px 8px 8px',
            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Grid
            container
            className={clsx(props.titleContainer, 'reset-dashboard')}
          >
            <Grid item xs={5} style={{ display: 'flex', alignItems: 'center' }}>
              {showBackBtn &&
                props.history &&
                props.history.location.state &&
                (props.history.location.state.pageType == 'LaborMisses' ||
                  props.history.location.state.pageType == 'FleetAccounts') && (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={handleclick}
                    style={{ width: 'auto' }}
                    fullWidth={false}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                )}
            </Grid>
            <Grid item xs={5} style={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="h4"
                color="primary"
                className={clsx(props.mainLabel, 'main-title')}
              >
                Labor Grid(s)
              </Typography>
            </Grid>
          </Grid>
        </Paper>
        <div>
          {props.keycloak.realmAccess.roles.includes('superadmin') === true && (
            <Paper
              square
              style={{
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
                cursor: 'default',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                fontSize: 14,
                padding: '5px',
                margin: 8,
                marginLeft: 8,
                paddingTop: 4,
                paddingLeft: 4
              }}
            >
              <Typography
                style={{
                  fontSize: 14,
                  color: '#084588',
                  textTransform: 'none',
                  pointerEvents: 'none',
                  borderColor: '#e7eef3',
                  marginLeft: '10px'
                }}
                variant="h6"
                color="primary"
              >
                Add a New Labor Grid
              </Typography>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <RadioGroup
                  style={{ fontWeight: 'bold', marginLeft: '10px' }}
                  row
                  aria-labelledby="demo-row-radio-buttons-group-label"
                  name="row-radio-buttons-group"
                  value={addGrid}
                  onChange={handleRadioChange}
                >
                  <FormControlLabel
                    value={'grid'}
                    control={
                      <Radio
                        size="small"
                        style={{
                          color: '#003d6b',
                          '&.Mui-checked': {
                            color: '#003d6b'
                          }
                        }}
                      />
                    }
                    label={'New Grid'}
                  />
                  <FormControlLabel
                    value="upload"
                    control={
                      <Radio
                        size="small"
                        style={{
                          color: '#003d6b',
                          '&.Mui-checked': {
                            color: '#003d6b'
                          }
                        }}
                      />
                    }
                    label="Upload File"
                  />

                  <FormControlLabel
                    value="fixed_rate"
                    control={
                      <Radio
                        size="small"
                        style={{
                          color: '#003d6b',
                          '&.Mui-checked': {
                            color: '#003d6b'
                          }
                        }}
                      />
                    }
                    label="Door Rate"
                  />
                </RadioGroup>
                <Button
                  onClick={fetchGridOrMatrixTypes}
                  variant="outlined"
                  style={{
                    textTransform: 'none',
                    color: '#003d6b',
                    borderColor: '#003d6b',
                    marginLeft: 'auto',
                    marginTop: '-18px'
                  }}
                >
                  All Model Grid
                </Button>
              </div>

              {addGrid && (
                <NewLaborGrid
                  addGrid={addGrid}
                  incompleteError={incompleteError}
                  invalidError={invalidError}
                  getGridWrapper={getGridWrapper}
                  setAddGrid={setAddGrid}
                  component="laborGrid"
                  gridTypeChoice={gridTypeChoice}
                  opcodeListChoice={opcodeListChoice}
                  handleSubmitLaborGrid={handleSubmitLaborGrid}
                  fetchGridOptions={fetchGridOptions}
                  cancel={cancel}
                  action={'insert'}
                  invalidLength={invalidLength}
                  disableSubmitButton={disableSubmitButton}
                  handleAlert={handleAlert}
                  ref={ref}
                  hasDefaultType={hasDefaultType}
                  setNoOfRows={setNoOfRows}
                  noOfRows={noOfRows}
                  addOrRemoveGridRow={addOrRemoveGridRow}
                  cancelGridAddition={cancelGridAddition}
                />
              )}
              <EditGridOrMatrixNameDialog
                openDlg={openDlg}
                parent={parent}
                handleCancelDlg={handleCancelDlg}
                gridOrMatrixDetails={gridTypeDetails}
                fetchGridOrMatrixTypes={fetchGridOrMatrixTypes}
                hasDefaultType={hasDefaultType}
                setUpdatedDefaultGridType={setUpdatedDefaultGridType}
                updatedDefaultGridType={updatedDefaultGridType}
                saveDefaultGridType={saveDefaultGridType}
                setGridTypeChanged={setGridTypeChanged}
                gridTypeChanged={gridTypeChanged}
                defaultGridType={defaultGridType}
                isLoadingGridType={isLoadingGridType}
                setIsLoadingGridType={setIsLoadingGridType}
                getGridOrMatrixList={getLaborGridList}
              />
            </Paper>
          )}

          <Paper className={classes.root}>
            {listLoading ? (
              <div>
                <Box style={{ padding: 25 }}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    style={{ padding: 25 }}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : gridTypeList.length === 0 ? (
              <Box style={{ padding: 25 }}>
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  No Data To Display
                </Typography>
              </Box>
            ) : (
              <Paper>
                {gridTypeList &&
                  gridTypeList.map((gridTypeData, index) => (
                    <Accordion
                      key={index}
                      expanded={index === expandedAccordionIndex}
                      onChange={(event, expanded) => {
                        setAddGrid('');
                        setRowData(null);
                        setEnableSave(false);
                        setIncompleteError(false);
                        setInvalidError(false);
                        setInvalidLength(false);
                        setEdit(false);
                        if (expanded) {
                          setSaveClickedFlag(false);
                          setExpandedAccordionIndex(index);
                          setInstallDate(
                            new Date(gridTypeData.storeInstallDate)
                          );
                          setGridType(gridTypeData.gridType);
                          getLaborGridList({
                            callType: 'Grid',
                            gridType: gridTypeData.gridType,
                            gridFor: gridTypeData.gridFor,
                            createdDate: gridTypeData.createdDate
                          });
                        } else {
                          setExpandedAccordionIndex(null);
                        }
                      }}
                      ref={el => (accordionRefs.current[index] = el)}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        aria-controls="panel1bh-content"
                        id="panel1bh-header"
                        style={{ height: '90px' }}
                      >
                        <Grid
                          container
                          spacing={1}
                          alignItems="center"
                          justifyContent="space-between"
                        >
                          <Grid item xs="auto">
                            <Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.header}
                              >
                                Store Install Date&nbsp;&nbsp;&nbsp;
                              </Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.header}
                              >
                                FOPC Calculated Date From&nbsp;&nbsp;&nbsp;
                              </Typography>
                            </Typography>
                            <Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.headerValue}
                              >
                                :{' '}
                                {formatCellValueDate(
                                  gridTypeData.storeInstallDate
                                )}
                              </Typography>
                              <Tooltip title="This may be different from store install date if no prior pricing provided">
                                <Typography
                                  variant="subtitle1"
                                  className={classes.headerValue}
                                >
                                  :{' '}
                                  {formatCellValueDate(
                                    gridTypeData.createdDate
                                  )}
                                </Typography>
                              </Tooltip>
                            </Typography>
                          </Grid>
                          <Grid item xs={3} className={classes.laborGrids}>
                            <Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.header}
                              >
                                Grid Type&nbsp;&nbsp;&nbsp;
                              </Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.header}
                              >
                                Grid Period&nbsp;&nbsp;&nbsp;
                              </Typography>
                            </Typography>
                            <Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.headerValue}
                              >
                                :{' '}
                                {capitalizeFirstLetter(
                                  gridTypeData.gridForDisplay
                                )}
                              </Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.headerValue}
                              >
                                {gridTypeData.gridOrder == 1
                                  ? ': Current'
                                  : `: Prior ${gridTypeData.gridOrder - 1}`}
                              </Typography>
                            </Typography>
                          </Grid>
                          <Grid item xs="auto" style={{ height: '100%' }}>
                            <Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.header}
                              >
                                {getName(gridTypeData.gridFor)}
                                &nbsp;&nbsp;&nbsp;
                              </Typography>
                              <Typography
                                variant="subtitle1"
                                className={classes.header}
                              ></Typography>
                            </Typography>
                            <Typography>
                              <Tooltip title={gridTypeData.gridType}>
                                <Typography
                                  variant="subtitle1"
                                  className={classes.headerValue}
                                  style={{
                                    paddingLeft: getPadding(
                                      gridTypeData.gridFor
                                    )
                                  }}
                                >
                                  : {formatCellValueDescription(gridTypeData)}
                                </Typography>
                              </Tooltip>
                              <Typography
                                variant="subtitle1"
                                className={classes.headerValue}
                              ></Typography>
                            </Typography>
                          </Grid>
                          <Grid item xs></Grid>
                          {props.keycloak.realmAccess.roles.includes(
                            'superadmin'
                          ) === true && (
                            <Grid item>
                              {index === expandedAccordionIndex && (
                                <Tooltip title="Edit">
                                  <IconButton
                                    className={classes.iconButton}
                                    aria-label="edit"
                                    disabled={enableSave}
                                    onClick={params => {
                                      handleEditButton(params);
                                      params.preventDefault();
                                      params.stopPropagation();
                                    }}
                                  >
                                    <EditIcon />
                                  </IconButton>
                                </Tooltip>
                              )}
                              <DeleteButton
                                handleDelete={handleSubmitLaborGrid}
                                gridTypeData={gridTypeData}
                                cancel={cancel}
                                setNewDefaultGridType={setNewDefaultGridType}
                                newDefaultGridType={newDefaultGridType}
                                fetchGridOrMatrixTypes={fetchGridOrMatrixTypes}
                                setReassignGridType={setReassignGridType}
                                reassignGridType={reassignGridType}
                                gridOrMatrixDetails={gridTypeDetails}
                                getLaborGridList={getLaborGridList}
                                setEnableSave={setEnableSave}
                              />
                            </Grid>
                          )}
                        </Grid>
                      </AccordionSummary>
                      {rowData && index === expandedAccordionIndex ? (
                        <AccordionDetails>
                          <Grid container display={'contents'}>
                            <>
                              <Grid item xs={8}>
                                <Typography
                                  fontSize={'small'}
                                  alignContent={'left'}
                                  marginBottom={'5px'}
                                  color="error"
                                  style={{
                                    textTransform: 'none',
                                    fontSize: 12
                                  }}
                                >
                                  {incompleteError &&
                                    '*Please fill in all fields.. '}
                                  {invalidError &&
                                    '*Enter numeric values only.'}{' '}
                                  {invalidLength &&
                                    '* Value should contain only seven digits with up to two decimal places, e.g. 1234567.89'}
                                </Typography>

                                {getGridWrapper('view', rowData)}
                                {enableSave && (
                                  <Box
                                    display="flex"
                                    justifyContent="flex-start"
                                  >
                                    <Button
                                      style={{
                                        textTransform: 'none',
                                        fontSize: '12px'
                                      }}
                                      color="primary"
                                      onClick={() => {
                                        setNoOfRows(prev => {
                                          const updatedRows = prev + 1;
                                          addOrRemoveGridRow(
                                            'add',
                                            updatedRows
                                          );
                                          return updatedRows;
                                        });
                                      }}
                                    >
                                      + Add new row
                                    </Button>
                                    <Button
                                      style={{
                                        textTransform: 'none',
                                        fontSize: '12px'
                                      }}
                                      color="primary"
                                      disabled={noOfRows <= 16}
                                      onClick={() => {
                                        setNoOfRows(prev => {
                                          const updatedRows = prev - 1;
                                          addOrRemoveGridRow(
                                            'remove',
                                            updatedRows
                                          );
                                          return updatedRows;
                                        });
                                      }}
                                    >
                                      - Remove last row
                                    </Button>
                                  </Box>
                                )}
                              </Grid>

                              {edit && index === expandedAccordionIndex && (
                                <Grid
                                  item
                                  xs={4}
                                  style={{ marginTop: '90px', float: 'right' }}
                                >
                                  <GridForm
                                    setAddGrid={setAddGrid}
                                    gridTypeChoice={gridTypeChoice}
                                    opcodeListChoice={opcodeListChoice}
                                    handleSubmitForm={values =>
                                      handleSubmitLaborGrid(values, 'update')
                                    }
                                    action={'edit'}
                                    cancel={cancel}
                                    enableSave={enableSave}
                                    item={gridTypeData}
                                    addGrid={'grid'}
                                    disableSubmitButton={disableSubmitButton}
                                    ref={ref}
                                    hasDefaultType={hasDefaultType}
                                  />
                                </Grid>
                              )}
                            </>
                          </Grid>
                        </AccordionDetails>
                      ) : !rowData && index === expandedAccordionIndex ? (
                        <Box style={{ padding: 25 }}>
                          <LinearProgress color="secondary" />
                          <Typography
                            variant="h6"
                            align="center"
                            style={{ padding: 25 }}
                            color="primary"
                          >
                            Processing...
                          </Typography>
                        </Box>
                      ) : (
                        <></>
                      )}
                    </Accordion>
                  ))}
              </Paper>
            )}
          </Paper>
        </div>
      </Page>
    </>
  );
};

export default withKeycloak(LaborGridPricing);
