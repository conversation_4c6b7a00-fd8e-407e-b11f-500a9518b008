import React from 'react';
import GridForm from './GridForm';
import { forwardRef, useImperativeHandle } from 'react';
import {
  Paper,
  Typography,
  Button,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  CircularProgress,
  Tooltip,
  Box,
  IconButton,
  LinearProgress,
  makeStyles
} from '@material-ui/core';
const useStyles = makeStyles(theme => ({
  paper: {
    margin: theme.spacing(2),
    padding: theme.spacing(2),
    boxShadow: props => (props.action === 'edit' ? 'none' : '0px 0px 0px 0px')
  },
  gridContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  tableContainer: {
    flex: 8,
    overflow: 'auto',
    padding: theme.spacing(2)
  },
  formContainer: {
    flex: 4,
    // overflow: 'auto',
    padding: theme.spacing(2)
  }
}));
const NewLaborGrid = forwardRef((props, ref) => {
  const classes = useStyles(props);
  const {
    addGrid,
    handleRadioChange,
    incompleteError,
    invalidError,
    getGridWrapper,
    handleSubmitLaborGrid,
    handleAlert,
    setAddGrid,
    component,
    addEmptyRow,
    handleRemoveLastRow,
    initialRowData,
    matrixSourceList,
    matrixTypeList,
    fetchPartsMatrixRowData,
    listLoading,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    gridTypeChoice,
    getGridTypesList,
    hasDefaultType,
    defaultGridType,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    action,
    opcodeListChoice,
    fetchGridOptions,
    cancel,
    invalidLength,
    disableSubmitButton,
    cancelGridAddition,
    noOfRows,
    setNoOfRows,
    addOrRemoveGridRow
  } = props;
  return (
    <Paper className={classes.paper}>
      <Grid container className={classes.gridContainer}>
        {addGrid == 'grid' ? (
          <>
            <Grid item xs={8}>
              <Typography
                fontSize={'small'}
                alignContent={'left'}
                marginBottom={'5px'}
                color="error"
                style={{
                  textTransform: 'none',
                  fontSize: 12
                }}
              >
                {incompleteError && '*Please fill in all fields.. '}
                {invalidError && '*Enter numeric values only.'}{' '}
                {invalidLength &&
                  '* Value should contain only seven digits with up to two decimal places, e.g. 1234567.89'}
              </Typography>

              {getGridWrapper('add')}
              <Box display="flex"  justifyContent="flex-start">
                <Button
                  style={{
                    textTransform: 'none',
                    fontSize: '12px'
                  }}
                  color="primary"
                  onClick={() => {
                    setNoOfRows(prev => {
                      const updatedRows = prev + 1;
                      addOrRemoveGridRow('add', updatedRows);
                      return updatedRows;
                    });
                  }}
                >
                  + Add new row
                </Button>
                <Button
                  style={{
                    textTransform: 'none',
                    fontSize: '12px'
                  }}
                  color="primary"
                  disabled={noOfRows <= 16}
                  onClick={() => {
                    setNoOfRows(prev => {
                      const updatedRows = prev - 1;
                      addOrRemoveGridRow('remove', updatedRows);
                      return updatedRows;
                    });
                  }}
                >
                  - Remove last row
                </Button>
              </Box>
            </Grid>
            <Grid item className={classes.formContainer}>
              <GridForm
                hasDefaultType={hasDefaultType}
                setAddGrid={setAddGrid}
                gridTypeChoice={gridTypeChoice}
                opcodeListChoice={opcodeListChoice}
                handleSubmitForm={values =>
                  handleSubmitLaborGrid(values, 'insert')
                }
                fetchGridOptions={fetchGridOptions}
                disableSubmitButton={disableSubmitButton}
                ref={ref}
              />
            </Grid>
          </>
        ) : addGrid === 'upload' || addGrid === 'fixed_rate' ? (
          <Grid
            item
            xs={action === 'edit' ? 12 : 6}
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
          >
            <GridForm
              cancelGridAddition={cancelGridAddition}
              handleSubmitForm={values =>
                handleSubmitLaborGrid(values, addGrid)
              }
              setAddGrid={setAddGrid}
              addGrid={addGrid}
              component={component}
              gridTypeChoice={gridTypeChoice}
              opcodeListChoice={opcodeListChoice}
              getGridTypesList={getGridTypesList}
              defaultGridType={defaultGridType}
              hasDefaultType={hasDefaultType}
              matrixSourceList={matrixSourceList}
              matrixTypeList={matrixTypeList}
              fetchGridOptions={fetchGridOptions}
              listLoading={listLoading}
              installDateValue={installDateValue}
              setInstallDateValue={setInstallDateValue}
              updateDate={updateDate}
              setUpdateDate={setUpdateDate}
              setStatusMessage={setStatusMessage}
              setOpenSnackbar={setOpenSnackbar}
              setStatusMessageType={setStatusMessageType}
              cancel={cancel}
              disableSubmitButton={disableSubmitButton}
              handleAlert={handleAlert}
              ref={ref}
            />
          </Grid>
        ) : (
          ''
        )}
      </Grid>
    </Paper>
  );
});

export default NewLaborGrid;
