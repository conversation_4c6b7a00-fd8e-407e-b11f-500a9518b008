import React, { Component } from 'react';

export default class extends Component {
  constructor(props) {
    super(props);
    this.checkedHandler = this.checkedHandler.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;
    this.props.node.setDataValue(colId, checked == true ? 1 : 0);
  }
  render() {
    return (
      <input
        type="checkbox"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : false
        }
        onClick={this.checkedHandler}
        checked={this.props.value == 1 ? true : false}
      />
    );
  }
}
