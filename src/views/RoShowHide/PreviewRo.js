import React, { useState, useEffect } from 'react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import {
  Button,
  Typography,
  Paper,
  Tooltip,
  LinearProgress
} from '@material-ui/core';
import ExportIcon from '@material-ui/icons/GetApp';
import { useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import { AgGridReact } from '@ag-grid-community/react';
import Slide from '@material-ui/core/Slide';
import { makeStyles } from '@material-ui/styles';
import clsx from 'clsx';
import 'src/styles.css';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import _ from 'lodash';
import RestoreIcon from '@material-ui/icons/Restore';
import moment from 'moment';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { getRoHideRules } from 'src/utils/hasuraServices';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {},
  smallRadioButton: {
    '& svg': {
      width: '0.7em',
      height: '0.7em'
    },
    paddingLeft: 0,
    paddingRight: 0,
    height: 18,
    marginLeft: -2,
    backgroundColor: 'transparent !important',
    width: 19,
    '@media (max-width: 1440px)': {
      width: '16px !important',
      marginLeft: -2
    }
  },
  listItemText: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    padding: '7px 10px !important',
    marginBottom: -8,
    fontWeight: 500,
    maxWidth: 220
  },
  loaderStore: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: '20% !important'
  },
  customTabIndicator: {
    '& .MuiTabs-indicator': {
      backgroundColor: '#ddeaf4 !important'
    }
  },
  back: { width: 100, marginTop: 12, marginRight: 8 },
  buttonContainer: { float: 'right', marginTop: 8, marginRight: 8 }
}));
function PreviewRo({
  openPopup,
  handlePopupClose,
  handleSubmitForm,
  previewRowData,
  previewLoad,
  ruleSelected
}) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(openPopup);
  const [rowData, setRowData] = React.useState(previewRowData);
  const [serviceAdvisors, setServiceAdvisors] = useState(['All']);
  const [store, setStore] = useState(localStorage.getItem('selectedStoreId'));
  const [isLoading, setIsLoading] = useState(true);
  const [gridApi, setGridApi] = useState(null);
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const [tabSelection, setTabSelection] = useState('one');
  const [pageSize, setPageSize] = useState(10);
  const context = { componentParent: this };
  const excelStyles = [
    {
      id: 'bigHeader',
      font: {
        size: 25,
        color: 'primary'
      },
      alignment: {
        horizontal: 'Center',
        vertical: 'Center'
      }
    },
    {
      id: 'textAlign',
      alignment: {
        horizontal: 'Left'
      }
    },
    {
      id: 'greenBackground',
      interior: {
        color: '#d9f2d9',
        pattern: 'Solid'
      }
    },
    {
      id: 'header',
      interior: {
        color: '#009900',
        pattern: 'Solid'
      },
      font: {
        bold: true,
        color: '#ffffff'
      }
    }
  ];
  const columnDefsRO = [
    { headerName: 'Id', field: 'id', hide: true },

    {
      headerName: 'RO Number',
      field: 'rvalueOne',
      suppressMenu: true,
      suppressMovable: true,
      unSortIcon: true,
      editable: false,
      width: 150,
      flex: 1,
      cellClass: 'textAlign',
      tooltipField: 'rvalueOne'
    },
    {
      headerName: 'Closed Date',
      field: 'rvalueTwo',
      suppressMenu: true,
      suppressMovable: true,
      unSortIcon: true,
      editable: false,
      width: 150,
      flex: 1,
      cellClass: 'textAlign',
      valueFormatter: params => moment(params.value).format('MM/DD/YY'),
      filterParams: {
        valueFormatter: params => moment(params.value).format('MM/DD/YY')
      }
    }
  ];
  const columnDefs = [
    { headerName: 'Id', field: 'id', hide: true },
    {
      headerName: 'Opcode',
      field: 'rvalueOne',
      suppressMenu: true,
      suppressMovable: true,
      unSortIcon: true,
      editable: false,
      width: 150,
      flex: 1,
      cellClass: 'textAlign',
      tooltipField: 'rvalueOne'
      // valueGetter: params => {
      //   switch (params.data.columnName) {
      //     case 'opcategory':
      //       return 'Op Category';
      //     case 'lbropcode':
      //       return 'Opcode';
      //     case 'ronumber':
      //       return 'RO Number';
      //     default:
      //       return params.data.columnName;
      //   }
      // }
    }
    // {
    //   headerName: 'Closed Date',
    //   field: 'rvalueTwo',
    //   suppressMenu: true,
    //   suppressMovable: true,
    //   unSortIcon: true,
    //   editable: false,
    //   width: 150,
    //   flex: 1,
    //   cellClass: 'textAlign',
    //   valueFormatter: formatCellValueDate,
    //   filterParams: {
    //     valueFormatter: formatCellValueDate
    //   }
    // }
    // {
    //   headerName: 'RO Number',
    //   field: 'rvalueThree',
    //   suppressMenu: true,
    //   suppressMovable: true,
    //   unSortIcon: true,
    //   editable: false,
    //   width: 150,
    //   flex: 1,
    //   cellClass: 'textAlign',
    //   tooltipField: 'rvalueThree'
    // }
  ];

  const defaultColDef = {
    cellClassRules: {
      greenBackground: params => params.rowIndex % 2 === 0
    },
    enableValue: true,
    sortable: true,
    filter: true,
    resizable: false,
    editable: true,
    suppressMovable: false,
    filter: 'agSetColumnFilter',
    filterParams: {
      applyMiniFilterWhileTyping: true
    }
  };
  useEffect(() => {
    setOpenDialog(openPopup);
  }, [openPopup]);
  const handleClose = () => {
    setOpenDialog(false);
    handlePopupClose();
  };

  useEffect(() => {
    // Fetch initial data when the component mounts
    // getRoOperand();
    // getDbdData();
    getAgGridData();
    console.log('previewRowData=rrrr=', previewRowData, rowData);
  }, [rowData]);

  useEffect(() => {
    // Fetch grid data when the store changes
    // if (store) {
    // getAgGridData();
    // }
  }, [session.storeSelected]);

  const getAgGridData = () => {
    setIsLoading(true);
    setRowData(previewRowData);
    setIsLoading(false);
    // getRoHideRules(result => {
    //   if (
    //     result.data &&
    //     result.data.statelessCcPhysicalRwGetRoHideRules &&
    //     result.data.statelessCcPhysicalRwGetRoHideRules.showHideRoRules
    //   ) {
    //     setRowData(
    //       result.data.statelessCcPhysicalRwGetRoHideRules.showHideRoRules
    //     );
    //   }
    //   setIsLoading(false);
    // });
  };

  const onGridReady = params => {
    params.api.closeToolPanel();
    setGridApi(params.api);
    setGridColumnApi(params.columnApi);
  };

  const resetReportGrid = () => {
    gridApi.setColumnDefs(columnDefs);
    gridColumnApi.resetColumnState();
    gridApi.setSortModel(null);
    gridApi.setFilterModel(null);
    gridApi.redrawRows();
    getAgGridData('reset');
  };

  const onBtExport = () => {
    const params = {
      sheetName: 'RoShowHide',
      fileName: 'RoShowHide',
      // processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Show/Hide RO - Rules' },
            mergeAcross: 3
          }
        ]
      ]
    };
    gridApi.exportDataAsExcel(params);
  };
  const handleApply = () => {
    setOpenDialog(false);
    // Handle apply logic here
    console.log('Apply clicked');
    handleSubmitForm();
    // setOpenDialog(false);
  };

  const handleCancel = () => {
    setOpenDialog(false);
    handlePopupClose();
  };
  console.log(
    'rowData===1111====',
    rowData,
    'previewRowData==',
    previewRowData,
    'pppp==',
    isLoading,
    '==',
    previewLoad,
    'openDialog==',
    openDialog
  );
  return (
    <div>
      <Dialog
        open={openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          style: {
            maxWidth: 1000,
            width: '450',
            marginLeft: '20px',
            marginTop: '28px',
            height: 575
          }
        }}
      >
        {/* <IconButton
          onClick={handleClose}
          aria-label="close"
          style={{ float: 'inline-end', justifyContent: 'right' }}
        >
          <Tooltip title="Close">
            <HighlightOffIcon style={{ fontSize: 30 }} />
          </Tooltip>
        </IconButton> */}
        <DialogContent style={{ flexDirection: 'row' }}>
          <div>
            <Paper
              square
              style={{
                margin: 8,
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
            >
              <Tabs
                value={tabSelection}
                onChange={(e, value) => setTabSelection(value)}
                variant="fullWidth"
                indicatorColor="secondary"
                textColor="secondary"
                className={classes.customTabIndicator}
              >
                <Tab
                  label={<div>Preview Data</div>}
                  value="one"
                  style={{ pointerEvents: 'none', textTransform: 'none' }}
                />
                {/* <Link
                  id="export-to-excel"
                  style={{ paddingRight: 12, cursor: 'pointer', marginTop: 11 }}
                  onClick={onBtExport}
                >
                  <Tooltip title="Export To Excel">
                    <ExportIcon />
                  </Tooltip>
                </Link>
                <Button
                  variant="contained"
                  id="reset-layout"
                  className={clsx(classes.back, 'reset-btn')}
                  onClick={resetReportGrid}
                >
                  <RestoreIcon />
                  <Typography variant="body1" align="left">
                    Reset Layout
                  </Typography>
                </Button> */}
              </Tabs>
            </Paper>
            {isLoading && previewLoad ? (
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            ) : (
              <div
                id="data-tab"
                className="ag-theme-balham"
                style={{
                  height: window.innerHeight - 545 + 'px',
                  width: '310px',
                  margin: 8,
                  display: tabSelection === 'two' ? 'none' : 'block'
                }}
              >
                <AgGridReact
                  className="ag-theme-balham"
                  style={{ height: '300px', width: '100%' }}
                  modules={AllModules}
                  columnDefs={
                    ruleSelected == 'RoNumber' ? columnDefsRO : columnDefs
                  }
                  defaultColDef={defaultColDef}
                  onGridReady={onGridReady}
                  rowData={previewRowData}
                  excelStyles={excelStyles}
                  tooltipShowDelay={0}
                  container="body"
                  floatingFilter
                  enableRangeSelection
                  animateRows
                  enableCharts
                  suppressRowClickSelection
                  suppressDragLeaveHidesColumns
                  suppressContextMenu
                  context={context}
                  pagination={true} // Enable pagination
                  paginationPageSize={pageSize} // Set the page size
                  enableBrowserTooltips
                />
              </div>
            )}
            <Box className={classes.buttonContainer}>
              {isLoading ? (
                ''
              ) : (
                <>
                  <Button
                    type="submit" // Make sure to set the type to submit
                    variant="contained"
                    color="primary"
                    onClick={handleApply}
                    className={clsx('reset-btn', classes.btnDiv)}
                    classes={{ label: classes.btnLabel }}
                    style={{
                      marginLeft: 10
                    }}
                  >
                    Apply
                  </Button>
                  <Button
                    type="submit" // Make sure to set the type to submit
                    variant="contained"
                    color="primary"
                    onClick={handleCancel}
                    className={clsx('reset-btn', classes.btnDiv)}
                    classes={{ label: classes.btnLabel }}
                    style={{
                      marginLeft: 7
                    }}
                  >
                    Close
                  </Button>
                </>
              )}
            </Box>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default PreviewRo;
