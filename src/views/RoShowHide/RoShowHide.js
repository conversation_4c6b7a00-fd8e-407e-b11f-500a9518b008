import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Button,
  Grid,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio
} from '@material-ui/core';
import { Formik, Form, Field, ErrorMessage, useFormikContext } from 'formik';
import { withStyles } from '@material-ui/styles';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React, { createRef } from 'react';
import 'react-grid-layout/css/styles.css';
import { UPDATE_CHART_MASTER } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import {
  getAllChartDetailsForDisplay,
  getDbdDetails,
  getViewDetails,
  getRoHideRules,
  insertRoHideRules,
  getRoHideRulesList,
  editRoHideRules,
  deleteRoHideRules,
  viewRoHide
} from 'src/utils/hasuraServices';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import RestoreIcon from '@material-ui/icons/Restore';
import { ReactSession } from 'react-client-session';
import clsx from 'clsx';
import ActiveStatusRenderer from './ActiveStatusRenderer';
import { getAllChartDetails } from '../../utils/Utils';
import { traceSpan } from 'src/utils/OTTTracing';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import moment from 'moment';
import * as Yup from 'yup';
import { getRoHideColumns, getRoHideOprators } from 'src/utils/hasuraServices';
import styled from 'styled-components';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import PreviewRo from './PreviewRo';
import ViewRo from './ViewRo';
import { ConsoleSpanExporter } from '@opentelemetry/sdk-trace-web';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import IconButton from '@material-ui/core/IconButton';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
const FormErrorMsg = ({ children }) => (
  <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>
    {children}
  </div>
);
class RoShowHide extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({
          openView: false,
          deleteInType: '',
          openDeleteDialog: false,
          save: false,
          previewLoad: false,
          roVal: true,
          inBetween: false,
          addCal: true,
          disabledCalender: false,
          disabledException: false,
          openException: false,
          openCalender: false,
          openRO: false,
          openOpcat: false,
          openOpcode: false,
          openSnackbar: false,
          goalFail: false,
          previewRo: false,
          cancel: false,
          showAddRule: true,
          ruleValue: ''
        });
        this.setState({
          editedRowId: null
        });
        // this.setState({ openOpcat: false });
        // this.setState({ openRO: false });
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData();
        this.resetFormOutside();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };
  componentDidMount() {
    this.getRoOperand();
    this.getDbdData();
  }

  formikRef = createRef();
  constructor(props) {
    super(props);
    let startEdit = this;
    this.state = {
      ruleSelected: '',
      inputData: {
        argRoleType: '',
        argActionName: '',
        argActionValue: '',
        argExcepActionName: '',
        argExcepActionValue: '',
        argExcepDateName: '',
        argExcepDateValueFrom: '',
        argExcepDateValueTo: '',
        inType: '',
        inTypeView: 0,
        argId: '',
        inActive: 1,
        preview: 1
      },
      ruleValue: '',
      openView: false,
      deleteId: null,
      deleteInType: '',
      openDeleteDialog: false,
      save: false,
      previewLoad: false,
      viewRowData: [],
      previewRowData: [],
      roVal: true,
      inBetween: false,
      addCal: true,
      disabledCalender: false,
      disabledException: false,
      openException: false,
      openCalender: false,
      openRO: false,
      openOpcat: false,
      openOpcode: false,
      openSnackbar: false,
      goalFail: false,
      previewRo: false,
      cancel: false,
      showCharts: false,
      selectValue: 'other',
      selectValueFor: this.props.category,
      selectValueType: this.props.reportType,
      ddLabel: '< 2',
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      isEdited: false,
      roInsertMsg: '',
      dbdId: [],
      viewdata: [],
      success: false,
      oldDataArr: [],
      newDataArr: [],
      prevDataArr: [],
      editedRowId: null,
      operator: [],
      field: [],
      showAddRule: true,
      // startDate: moment()
      //   .startOf('week')
      //   .toDate(),
      // endDate: moment()
      //   .endOf('week')
      //   .toDate(),
      startDate: moment().toDate(),
      endDate: moment().toDate(),
      columnDefs: [
        { headerName: 'Id', field: 'id', hide: true },
        {
          headerName: 'Type',
          field: 'roleType',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          flex: 1,
          cellClass: 'textAlign',
          tooltipField: 'roleType',
          valueGetter: params => {
            switch (params.data.roleType) {
              case 'Opcategory':
                return 'Op Category';
              case 'Opcode':
                return 'Opcode';
              case 'RoNumber':
                return 'RO Number';
              default:
                return params.data.roleType; // Fallback to the original value
            }
          }
        },
        // {
        //   headerName: 'Created Date',
        //   field: 'createdTime',
        //   suppressMenu: true,
        //   suppressMovable: true,
        //   unSortIcon: true,
        //   editable: false,
        //   width: 150,
        //   flex: 1,
        //   cellClass: 'textAlign',
        //   valueFormatter: this.formatCellValueDate,
        //   filterParams: {
        //     valueFormatter: this.formatCellValueDate
        //   }
        // },
        {
          headerName: 'Action',
          field: 'actionName',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          cellClass: 'textAlign',
          flex: 1,
          valueGetter: params => {
            switch (params.data.actionName) {
              case 'BeginsWith':
                return 'Begins With';
              case 'Contains':
                return 'Contains';
              case 'EndsWith':
                return 'Ends With';
              case 'EqualTo':
                return 'Equal To';
              default:
                return params.data.actionName; // Fallback to the original value
            }
          }
        },
        {
          headerName: 'Value',
          field: 'actionValue',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          cellClass: 'textAlign',
          filter: 'agSetColumnFilter',
          flex: 1,
          cellStyle: function(params) {
            return {
              textAlign: 'center'
            };
          }
        },
        {
          headerName: 'Exception Action',
          field: 'excepActionName',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          cellClass: 'textAlign',
          flex: 1,
          valueGetter: params => {
            switch (params.data.excepActionName) {
              case 'NotEqualTo':
                return 'Not Equal To';
              default:
                return params.data.excepActionName; // Fallback to the original value
            }
          }
        },
        {
          headerName: 'Exception Value',
          field: 'excepActionValue',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          cellClass: 'textAlign',
          filter: 'agSetColumnFilter',
          flex: 1,
          cellStyle: function(params) {
            return {
              textAlign: 'center'
            };
          }
        },
        {
          headerName: 'Closed Date Action',
          field: 'excepDateName',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          cellClass: 'textAlign',
          flex: 1,
          valueGetter: params => {
            switch (params.data.excepDateName) {
              case 'InBetween':
                return 'In Between';
              case 'IsEqualTo':
                return 'Is Equal To';
              case 'IsLessThanOrEqualTo':
                return 'Is Less Than Or Equal To';
              default:
                return params.data.excepDateName; // Fallback to the original value
            }
          }
        },
        {
          headerName: 'Close Date From',
          field: 'excepDateValueFrom',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          flex: 1,
          cellClass: 'textAlign',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Close Date To',
          field: 'excepDateValueTo',
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          editable: false,
          width: 150,
          flex: 1,
          cellClass: 'textAlign',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        // {
        //   headerName: 'Hide',
        //   chartDataType: 'series',
        //   width: 150,
        //   field: 'active',
        //   editable: false,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   flex: 1,
        //   cellStyle() {
        //     return { textAlign: 'center', border: ' 0px white' };
        //   },
        //   cellRenderer: 'activeStatusRenderer'
        // },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          width: 170,
          filter: false,
          sortable: false,
          flex: 1,
          suppressMenu: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false,
          cellRenderer: function(params) {
            var index = params.rowIndex;
            var eDiv = document.createElement('div');

            eDiv.innerHTML =
              // '<button title="Edit" id="btnedit' +
              // index +
              // '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel' +
              // index +
              // '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate' +
              // index +
              // '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>' +
              '<button title="Delete" id="btndelete' +
              index +
              '"style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>' +
              '<button   title="View" id="btnview' +
              index +
              '"  style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px; margin-left: 6px;" class="view-button"><i class="fas fa-eye"></i></button>';

            // eDiv.innerHTML =
            //   '<button title="Edit" id="btnedit' +
            //   index +
            //   '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel' +
            //   index +
            //   '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate' +
            //   index +
            //   '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>' +
            //   '<button title="Delete" id="btndelete' +
            //   index +
            //   '"style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>' +
            //   '<button   title="View" id="btnview' +
            //   index +
            //   '"  style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px; margin-left: 6px;" class="view-button"><i class="fas fa-eye"></i></button>';
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') == false
            ) {
              $(document).ready(function() {
                $('.edit-button').attr('disabled', 'disabled');
                $('.edit-button').css('background', '#38416373');
              });
            }
            if (index != undefined) {
              // var eButton = eDiv.querySelectorAll('.edit-button')[0];
              // var uButton = eDiv.querySelectorAll('.update-button')[0];
              // var cButton = eDiv.querySelectorAll('.cancel-button')[0];
              var dButton = eDiv.querySelectorAll('.delete-button')[0];
              var vButton = eDiv.querySelectorAll('.view-button')[0];
              // eButton.addEventListener('click', () => {
              //   startEdit.gridApi.redrawRows();
              //   localStorage.setItem('index', index);
              //   startEdit.setState({ cancel: false });
              //   startEdit.onBtStartEditing(index);
              //   localStorage.setItem('chartId', params.data.id);
              //   startEdit.setState({
              //     editedRowId: index
              //   });
              //   let oldArr = {
              //     index: index,
              //     fieldName: params.data.fieldName,
              //     active: params.data.active,
              //     operatorValue: params.data.operatorValue,
              //     operator: params.data.operator
              //   };

              //   var rowPrevArray = [];
              //   let indexArr = rowPrevArray.findIndex(
              //     ({ fieldName }) => fieldName == params.data.fieldName
              //   );
              //   if (indexArr === -1) {
              //     rowPrevArray.push(oldArr);
              //   }
              //   startEdit.setState({
              //     oldDataArr: rowPrevArray
              //   });
              //   $('#btnedit' + index).hide();
              //   $('#btncancel' + index).show();
              //   $('#btnupdate' + index).show();
              //   $('#btndelete' + index).hide();
              //   $('#btnview' + index).hide();
              // });

              // uButton.addEventListener('click', () => {
              //   startEdit.onBtStopEditing(index);
              //   startEdit.setState({ cancel: false });
              //   startEdit.setState({
              //     editedRowId: null
              //   });
              //   let arr = startEdit.state.oldDataArr;
              //   if (arr[0].active != params.data.active) {
              //     if (arr[0].active == params.data.active) {
              //       $('#btnedit' + index).show();
              //       $('#btncancel' + index).hide();
              //       $('#btnupdate' + index).hide();
              //       $('#btndelete' + index).show();
              //       $('#btnview' + index).show();
              //     } else {
              //       // var selectedRoId = params.data.roId;
              //       // var selectedActive = params.data.active;
              //       // var selectedFieldName = params.data.roleType;
              //       // var selectedOperatorValue = params.data.operandValue;
              //       // var selectedOperator = params.data.operator;
              //       // const input = {
              //       //   fieldName: selectedFieldName,
              //       //   status: selectedActive,
              //       //   operatorValue: selectedOperatorValue,
              //       //   operator: selectedOperator
              //       // };

              //       var selectedId = params.data.id;
              //       var selectedActionName = params.data.actionName;
              //       var selectedActionValue = params.data.actionValue;
              //       var selectedActive = params.data.active;
              //       var selectedExcepActionName = params.data.excepActionName;
              //       var selectedExcepActionValue = params.data.excepActionValue;
              //       var selectedExcepDateName = params.data.excepDateName;
              //       var selectedExcepDateValueFrom =
              //         params.data.excepDateValueFrom;
              //       var selectedExcepDateValueTo = params.data.excepDateValueTo;
              //       var selectedRoleType = params.data.roleType;

              //       const input = {
              //         argRoleType: selectedRoleType,
              //         argActionName: selectedActionName,
              //         argActionValue: selectedActionValue,
              //         argExcepActionName: selectedExcepActionName,
              //         argExcepActionValue: selectedExcepActionValue,
              //         argExcepDateName: selectedExcepDateName,
              //         argExcepDateValueFrom: selectedExcepDateValueFrom,
              //         argExcepDateValueTo: selectedExcepDateValueTo,
              //         inType: 'rule_status_change',
              //         inTypeView: 0,
              //         argId: selectedId,
              //         inActive: selectedActive,
              //         preview: 1
              //       };

              //       startEdit.insertRoHide(input);

              //       let rowData = startEdit.state.oldDataArr;
              //       rowData.map((object, i) => {
              //         if (index != object.index) {
              //           let rowNode = params.api.getDisplayedRowAtIndex(
              //             object.index
              //           );
              //           rowNode.setDataValue('active', rowData[i].active);

              //           $('#btnedit' + object.index).show();
              //           $('#btncancel' + object.index).hide();
              //           $('#btnupdate' + object.index).hide();
              //           $('#btndelete' + object.index).show();
              //           $('#btnview' + object.index).show();
              //         }
              //       });
              //       startEdit.setState({
              //         oldDataArr: []
              //       });
              //     }
              //   } else {
              //     $('#btnedit' + index).show();
              //     $('#btncancel' + index).hide();
              //     $('#btnupdate' + index).hide();
              //     $('#btnedit' + index).show();
              //   }
              // });
              // cButton.addEventListener('click', function() {
              //   startEdit.setState({ cancel: true });
              //   startEdit.onBtStopEditing(index);
              //   startEdit.setState({
              //     editedRowId: null
              //   });
              //   let rowData = startEdit.state.oldDataArr;
              //   rowData.map((object, i) => {
              //     let rowNode = params.api.getDisplayedRowAtIndex(object.index);
              //     rowNode.setDataValue('active', rowData[i].active);
              //     $('#btnedit' + object.index).show();
              //     $('#btncancel' + object.index).hide();
              //     $('#btnupdate' + object.index).hide();
              //     $('#btndelete' + object.index).show();
              //     $('#btnview' + object.index).show();
              //   });
              // });
              dButton.addEventListener('click', function() {
                var selectedId = params.data.id;
                if (selectedId != '') {
                  startEdit.openDeleteRoData(selectedId, 'rule_delete');
                }
                // $('#btnedit' + index).show();
                // $('#btncancel' + index).hide();
                // $('#btnupdate' + index).hide();
                $('#btndelete' + index).show();
                $('#btnview' + index).show();
              });
              vButton.addEventListener('click', function() {
                var selectedId = params.data.id;
                var selectedField = params.data.roleType;
                if (selectedId != '') {
                  startEdit.deleteRoData(
                    selectedId,
                    'rule_details',
                    selectedField
                  );
                }
                // $('#btnedit' + index).show();
                // $('#btncancel' + index).hide();
                // $('#btnupdate' + index).hide();
                $('#btndelete' + index).show();
                $('#btnview' + index).show();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],

      editType: 'fullRow',
      chartName: null,
      context: { componentParent: this },
      frameworkComponents: {
        activeStatusRenderer: ActiveStatusRenderer
      },
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        suppressMovable: false,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        }
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },

        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
    this.handleSubmit = this.handleSubmit.bind(this);
  }
  viewRoData = id => {
    const inputArr = {
      argRoleType: '',
      argActionName: '',
      argActionValue: '',
      argExcepActionName: '',
      argExcepActionValue: '',
      argExcepDateName: '',
      argExcepDateValueFrom: null,
      argExcepDateValueTo: null,
      inType: 'rule_details',
      inTypeView: 0,
      argId: id,
      inActive: 1,
      preview: 1
    };
    this.insertRoHide(inputArr);
  };
  openDeleteRoData = (selectedId, inType) => {
    this.setState({
      openDeleteDialog: true,
      deleteId: selectedId,
      deleteInType: 'rule_delete'
    });
  };
  handleCancelDelete = () => {
    this.setState({
      openDeleteDialog: false,
      deleteId: null
    });
  };
  handleDelete = () => {
    this.deleteRoData(this.state.deleteId, 'rule_delete', '');
    this.setState({
      openDeleteDialog: false
    });
  };
  deleteRoData = (selectedId, inType, selectedField) => {
    if (inType == 'rule_delete') {
      this.setState({ isLoading: true });
    }
    deleteRoHideRules(selectedId, inType, result => {
      console.log('enter=1');
      if (
        result.data.statelessCcPhysicalRwCrudRoHideRules.results &&
        inType == 'rule_details'
      ) {
        this.setState({ ruleSelected: selectedField });
        this.setState({ openView: true });
        console.log(
          'statelessCcPhysicalRwCrudRoHideRules====',
          result.data.statelessCcPhysicalRwCrudRoHideRules.results
        );
        this.setState({
          viewRowData: result.data.statelessCcPhysicalRwCrudRoHideRules.results
        });
        this.setState({ previewLoad: false });
      } else if (
        result.data.statelessCcPhysicalRwCrudRoHideRules.results &&
        // result.data.statelessCcPhysicalRwCrudRoHideRules.results.length > 0 &&
        result.data.statelessCcPhysicalRwCrudRoHideRules.results[0].status == 1
      ) {
        console.log('enter=12');
        if (inType == 'rule_delete') {
          console.log('enter=123');
          this.setState({ isLoading: true });
          this.setState({ success: true });

          this.gridApi.setColumnDefs(this.state.columnDefs);
          this.setState({
            editedRowId: null
          });
          this.state.gridColumnApi.resetColumnState();
          this.gridApi.setSortModel(null);
          this.gridApi.setFilterModel(null);
          this.gridApi.redrawRows();
          this.setState({
            roInsertMsg:
              result.data.statelessCcPhysicalRwCrudRoHideRules.results[0].msg
          });
          this.getAgGridData('reset');
          this.setState({ previewRo: false });
          this.setState({ previewLoad: false });

          this.setState({ openSnackbar: true });
          this.setState({ goalFail: false });
          // this.closeEmail();
        }
      } else {
        console.log('enter=1234');
        // this.setState({ success: true });
        this.setState({
          roInsertMsg:
            result.data.statelessCcPhysicalRwCrudRoHideRules.results[0].msg
        });

        this.gridApi.setColumnDefs(this.state.columnDefs);
        this.setState({
          editedRowId: null
        });
        this.state.gridColumnApi.resetColumnState();
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
        this.gridApi.redrawRows();
        this.getAgGridData('reset');

        this.setState({ openSnackbar: true });
        this.setState({ goalFail: true });
        this.closeEmail();
      }
    });
  };
  resetFormOutside = () => {
    if (this.formikRef.current) {
      this.formikRef.current.resetForm();
    }
  };
  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
  };
  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[1]['editable'] = false;
    groupColumn[4]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  insertRoHide = input => {
    this.setState({ previewRowData: [] });
    this.setState({ viewRowData: [] });
    console.log('insertRoHide==', input);
    if (input.inType == 'rule_details') {
    } else if (input.inTypeView != 1) {
      this.setState({ isLoading: true });
    } else if (input.inTypeView == 1) {
      // this.setState({ previewRo: true });
      this.setState({ previewLoad: true });
    }

    // insertRoHideRules(input, result => {
    //   if (
    //     result.data.statelessCcPhysicalRwInsertRoHideRules.results &&
    //     result.data.statelessCcPhysicalRwInsertRoHideRules.results.length > 0 &&
    //     result.data.statelessCcPhysicalRwInsertRoHideRules.results[0].status !==
    //       0
    //   ) {
    //     this.setState({ isLoading: true });
    //     this.setState({ success: true });

    //     this.gridApi.setColumnDefs(this.state.columnDefs);
    //     this.setState({
    //       editedRowId: null
    //     });
    //     this.state.gridColumnApi.resetColumnState();
    //     this.gridApi.setSortModel(null);
    //     this.gridApi.setFilterModel(null);
    //     this.gridApi.redrawRows();
    //     this.getAgGridData('reset');
    //     this.setState({ previewRo: false });
    //     this.setState({
    //       roInsertMsg:
    //         result.data.statelessCcPhysicalRwInsertRoHideRules.results[0].msg
    //     });
    //     this.setState({ openSnackbar: true });
    //     this.setState({ goalFail: false });
    //   } else {
    //     this.setState({ success: true });
    //     this.setState({ previewRo: false });
    //     this.setState({
    //       roInsertMsg:
    //         result.data.statelessCcPhysicalRwInsertRoHideRules.results[0].msg
    //     });

    //     this.gridApi.setColumnDefs(this.state.columnDefs);
    //     this.setState({
    //       editedRowId: null
    //     });
    //     this.state.gridColumnApi.resetColumnState();
    //     this.gridApi.setSortModel(null);
    //     this.gridApi.setFilterModel(null);
    //     this.gridApi.redrawRows();
    //     this.getAgGridData('reset');

    //     this.setState({ openSnackbar: true });
    //     this.setState({ goalFail: true });
    //   }
    // });
    editRoHideRules(input, result => {
      if (
        result.data.statelessCcPhysicalRwCrudRoHideRules.results &&
        result.data.statelessCcPhysicalRwCrudRoHideRules.results.length > 0 &&
        result.data.statelessCcPhysicalRwCrudRoHideRules.results[0].status !== 0
      ) {
        if (input.inTypeView != 1) {
          this.setState({ isLoading: true });
          this.setState({ success: true });

          this.gridApi.setColumnDefs(this.state.columnDefs);
          this.setState({
            editedRowId: null
          });
          this.state.gridColumnApi.resetColumnState();
          this.gridApi.setSortModel(null);
          this.gridApi.setFilterModel(null);
          this.gridApi.redrawRows();
        }
        if (input.inTypeView == 1) {
          this.setState({ previewRo: true });
          console.log(
            'statelessCcPhysicalRwCrudRoHideRules====',
            result.data.statelessCcPhysicalRwCrudRoHideRules.results
          );
          this.setState({
            previewRowData:
              result.data.statelessCcPhysicalRwCrudRoHideRules.results
          });
          this.setState({ previewLoad: false });
        } else {
          this.setState({
            roInsertMsg:
              result.data.statelessCcPhysicalRwCrudRoHideRules.results[0].msg
          });
          this.getAgGridData('reset');
          this.setState({ previewRo: false });
          this.setState({ previewLoad: false });

          this.setState({
            openView: false,
            deleteInType: '',
            openDeleteDialog: false,
            save: false,
            previewLoad: false,
            roVal: true,
            inBetween: false,
            addCal: true,
            disabledCalender: false,
            disabledException: false,
            openException: false,
            openCalender: false,
            openRO: false,
            openOpcat: false,
            openOpcode: false,
            openSnackbar: false,
            goalFail: false,
            previewRo: false,
            cancel: false,
            showAddRule: true
          });
          this.resetFormOutside();

          // this.resetFormOutside();
          // this.setState({
          //   openView: false,
          //   deleteInType: '',
          //   openDeleteDialog: false,
          //   save: false,
          //   previewLoad: false,
          //   roVal: true,
          //   inBetween: false,
          //   addCal: true,
          //   disabledCalender: false,
          //   disabledException: false,
          //   openException: false,
          //   openCalender: false,
          //   openRO: false,
          //   openOpcat: false,
          //   openOpcode: false,
          //   openSnackbar: false,
          //   goalFail: false,
          //   previewRo: false,
          //   cancel: false,
          //   showAddRule: true
          // });
        }

        if (input.inTypeView != 1) {
          this.setState({ openSnackbar: true });
          this.setState({ goalFail: false });
          this.closeEmail();
        }
      } else if (
        result.data.statelessCcPhysicalRwCrudRoHideRules.results &&
        result.data.statelessCcPhysicalRwCrudRoHideRules.results.length == 0
      ) {
        // this.setState({ success: true });
        this.setState({ previewRo: true });
        this.setState({ previewLoad: false });
        // this.setState({
        //   roInsertMsg:
        //     result.data.statelessCcPhysicalRwCrudRoHideRules.results[0].msg
        // });

        // this.gridApi.setColumnDefs(this.state.columnDefs);
        // this.setState({
        //   editedRowId: null
        // });
        // this.state.gridColumnApi.resetColumnState();
        // this.gridApi.setSortModel(null);
        // this.gridApi.setFilterModel(null);
        // this.gridApi.redrawRows();
        // this.getAgGridData('reset');

        // this.setState({ openSnackbar: true });
        // this.setState({ goalFail: true });
        // this.closeEmail();
      } else {
        this.setState({ success: true });
        this.setState({ previewRo: clearInterval });
        this.setState({ previewLoad: false });
        this.setState({
          roInsertMsg:
            result.data.statelessCcPhysicalRwCrudRoHideRules.results[0].msg
        });

        this.gridApi.setColumnDefs(this.state.columnDefs);
        this.setState({
          editedRowId: null
        });
        this.state.gridColumnApi.resetColumnState();
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
        this.gridApi.redrawRows();
        this.getAgGridData('reset');

        this.setState({ openSnackbar: true });
        this.setState({ goalFail: true });
        this.closeEmail();

        this.setState({
          openView: false,
          deleteInType: '',
          openDeleteDialog: false,
          save: false,
          previewLoad: false,
          roVal: true,
          inBetween: false,
          addCal: true,
          disabledCalender: false,
          disabledException: false,
          openException: false,
          openCalender: false,
          openRO: false,
          openOpcat: false,
          openOpcode: false,
          previewRo: false,
          cancel: false,
          showAddRule: true
        });
        this.resetFormOutside();
      }
    });
    this.setState({ isEdited: false });
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['active'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['active'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    this.gridApi.redrawRows();
  };

  onSortChanged = e => {
    this.gridApi.redrawRows();
  };

  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    this.state.rawGridApi.setColumnDefs(groupColumn);
    this.state.rawGridApi.setFocusedCell(index, 'active', pinned);
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'active',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridColumnApi: params.columnApi });
  };
  onCellValueChanged = params => {
    if (this.state.cancel == false) {
      if (params.newValue != params.oldValue) {
        this.setState({ isEdited: true });
        var selectedChartId = params.data.chartId;
        var selectedChart = params.data.chartName;
        var selectedChartDescription = params.data.description;
        var selectedDbd = params.data.dbdName;
        var selectedView = params.data.viewDetails;
      }
    }
  };
  resetReportGrid = () => {
    this.gridApi.setColumnDefs(this.state.columnDefs);
    this.setState({
      editedRowId: null
    });
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
    this.resetFormOutside();
    this.setState({
      openView: false,
      deleteInType: '',
      openDeleteDialog: false,
      save: false,
      previewLoad: false,
      roVal: true,
      inBetween: false,
      addCal: true,
      disabledCalender: false,
      disabledException: false,
      openException: false,
      openCalender: false,
      openRO: false,
      openOpcat: false,
      openOpcode: false,
      openSnackbar: false,
      goalFail: false,
      previewRo: false,
      cancel: false,
      showAddRule: true,
      ruleValue: ''
    });
    // this.getAgGridData('reset');
  };
  getAgGridData(reset) {
    console.log('results=65=ssssssss', reset);
    if (reset != 'reset') {
      this.setState({ isLoading: true });
    }
    // this.setState({
    //   rowData: []
    // });
    let inType = 'rule_list';
    getRoHideRulesList(inType, result => {
      console.log('results=65=', result);
      let resultArr = [];
      if (
        result.data &&
        result.data.statelessCcPhysicalRwCrudRoHideRules &&
        result.data.statelessCcPhysicalRwCrudRoHideRules.results
      ) {
        this.setState({
          rowData: result.data.statelessCcPhysicalRwCrudRoHideRules.results
        });
        this.setState({ isLoading: false });
      }
    });
  }
  getDbdData() {
    let inType = 'rule_list';
    getRoHideRulesList(inType, result => {
      console.log(
        'results=65888=',
        result.data.statelessCcPhysicalRwCrudRoHideRules.results
      );
      let resultArr = [];
      if (
        result &&
        result.data &&
        result.data.statelessCcPhysicalRwCrudRoHideRules &&
        result.data.statelessCcPhysicalRwCrudRoHideRules.results
      ) {
        this.setState({
          rowData: result.data.statelessCcPhysicalRwCrudRoHideRules.results
        });
        this.setState({ isLoading: false });
        console.log(
          'results=65888=====',
          result.data.statelessCcPhysicalRwCrudRoHideRules.results
        );
      }
    });
  }
  // onBtExport = () => {
  //   var params = {
  //     sheetName: 'RoShowHide',
  //     fileName: 'RoShowHide',
  //     processCellCallback: params => processCells(params),
  //     customHeader: [
  //       [],
  //       [
  //         {
  //           styleId: 'bigHeader',
  //           data: { type: 'String', value: 'Show/Hide RO - Rules' },
  //           mergeAcross: 3
  //         }
  //       ]
  //     ]
  //   };

  //   this.state.rawGridApi.exportDataAsExcel(params);
  // };

  onBtExport = () => {
    const params = {
      sheetName: 'Hide RO - Rules',
      fileName: 'Hide RO - Rules',
      processCellCallback: params => processCells(params),
      columnWidth: 150,
      columnKeys: [
        'roleType',
        'Action',
        'actionName',
        'actionValue',
        'excepActionName',
        'excepActionValue',
        'excepDateName',
        'excepDateValueFrom',
        'excepDateValueTo'
      ],

      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Hide RO - Rules' },
            mergeAcross: 3
          }
        ]
      ]
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  getDashboardIds = selectedDbd => {
    var schemaName = [];
  };
  handleOk = () => {
    this.setState({ success: false });
  };
  onCellClicked = params => {
    let index = localStorage.getItem('index');
    const id = localStorage.getItem('roId');
    if (params.data.id != id) {
      $(`.edit-button`).show();
      $(`.update-button`).hide();
      $(`.cancel-button`).hide();

      this.setState({ cancel: true });
      this.onBtStopEditing(index);
      this.setState({ editedRowId: null });
      let rowData = this.state.oldDataArr;
      rowData.map((object, i) => {
        let rowNode = params.api.getDisplayedRowAtIndex(object.index);
        rowNode.setDataValue('active', rowData[i].active);
        $('#btnedit' + object.index).show();
        $('#btncancel' + object.index).hide();
        $('#btnupdate' + object.index).hide();
        $('#btndelete' + object.index).hide();
        $('#btnview' + object.index).hide();
        $('#advisorStatus' + object.index).prop(
          'checked',
          rowData.active == 1 ? true : false
        );
        $('#advisorStatus' + object.index).prop('disabled', 'disabled');
      });
      this.gridApi.redrawRows();
    }
  };
  closePreview = () => {
    this.setState({ previewRo: false });
    //////////////////////////////////////////////////
    // this.setState({ showAddRule: false });
    // this.setState({ disabledException: false });
    // this.setState({ disabledCalender: false });
    // this.setState({ openCalender: false });
    // this.setState({ openException: false });
    // this.setState({ openRO: false });
    // this.setState({ openOpcode: false });
    // this.setState({ addCal: false });
    // this.setState({ roVal: false });
  };
  closeView = () => {
    this.setState({ openView: false });
    //////////////////////////////////////////////////
    // this.setState({ showAddRule: false });
    // this.setState({ disabledException: false });
    // this.setState({ disabledCalender: false });
    // this.setState({ openCalender: false });
    // this.setState({ openException: false });
    // this.setState({ openRO: false });
    // this.setState({ openOpcode: false });
    // this.setState({ addCal: false });
    // this.setState({ roVal: false });
  };
  insertPreviewRowData = () => {
    console.log('this.state.inputArr===', this.state.inputData);
    this.insertRoHide(this.state.inputData);
  };
  addEmail = () => {
    this.setState({ showAddRule: true });
  };
  closeEmail = () => {
    // this.setState({ previewRo: true });
    //////////////////////////////////////////////////
    // this.setState({ showAddRule: false });
    this.setState({ disabledException: false });
    this.setState({ disabledCalender: false });
    this.setState({ openCalender: false });
    this.setState({ openException: false });
    this.setState({ openRO: false });
    this.setState({ openOpcode: false });
    this.setState({ ruleValue: '' });
    // this.setState({ addCal: false });
    // this.setState({ roVal: false });
  };
  addException = setTouched => {
    setTouched({
      fieldName: false,
      operator: false,
      operatorValue: false,
      operatorTwo: false,
      operatorValueOne: false
    });
    this.setState({ openException: true });
    this.setState({ disabledException: true });
  };
  deleteException = (setTouched, setFieldValue) => {
    setTouched({
      fieldName: false,
      operator: false,
      operatorValue: false,
      operatorTwo: false,
      operatorValueOne: false
    });
    this.setState({ openException: false });
    this.setState({ disabledException: false });
    setFieldValue('operatorValueOne', '');
  };
  deleteCalender = (setTouched, setFieldValue) => {
    setTouched({
      fieldName: false,
      operator: false,
      operatorValue: false,
      operatorTwo: false,
      operatorValueOne: false,
      singleDate: false,
      picker: false
    });
    setFieldValue('operatorTwo', 'IsEqualTo');
    this.setState({
      inBetween: false
    });
    this.setState({ openCalender: false });
    this.setState({ disabledCalender: false });
    setFieldValue('singleDate', moment().format('MM/DD/YY'));
    setFieldValue(
      'picker',
      { startDate: moment(this.state.startDate).format('MM/DD/YY') },
      { endDate: moment(this.state.endDate).format('MM/DD/YY') }
    );
  };
  addCalender = setTouched => {
    setTouched({
      fieldName: false,
      operator: false,
      operatorValue: false,
      operatorTwo: false,
      operatorValueOne: false
    });
    // this.setState({ openRO: true });
    this.setState({ openCalender: true });
    this.setState({ disabledCalender: true });
  };

  formatCellValueDate = params => {
    console.log('wwww===', params.value, '===', params.value != null);
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return params.value;
    }
  };
  validationSchema = context =>
    Yup.object().shape({
      fieldName: Yup.string().required('Please select the Rule'),
      operator: Yup.string().required('Please select the Action'),
      operatorValue: Yup.string().when('fieldName', (fieldName, schema) => {
        if (fieldName == 'Opcategory') {
          return schema.required('Please select the Value');
        } else if (fieldName == 'Opcode' || fieldName == 'RoNumber') {
          return schema
            .required('Value is required')
            .max(20, 'Value cannot exceed 20 characters')
            .matches(/^[^%]*$/, 'Value cannot contain %');
        }
        return schema; // Default case, returns the schema as-is
      }),
      operatorValueOne: Yup.string().when([], {
        is: () => context.openException && context.openRO,
        then: schema =>
          schema
            .required('Value is required')
            .max(20, 'Value cannot exceed 20 characters'),
        otherwise: schema => schema.optional() // If conditions are not met, make it optional
      }),
      operatorTwo: Yup.string().when([], {
        is: () => context.openCalender,
        then: schema => schema.required('Please select the Action'),
        otherwise: schema => schema.optional() // If conditions are not met, make it optional
      }),
      singleDate: Yup.string()
        .test(
          'isValidDate',
          'Invalid date format. Use MM/DD/YY',
          value => value && moment(value, 'MM/DD/YY', true).isValid()
        )
        .required('Date is required')
    });

  async handleSubmit(values, { setSubmitting, resetForm }) {
    setSubmitting(true); // Disable submit button while submitting
    try {
      let {
        fieldName,
        operator,
        operatorValue,
        operatorOne,
        operatorValueOne,
        operatorTwo,
        singleDate,
        inTypeView,
        id,
        picker
      } = values;

      fieldName = fieldName ? fieldName.trim() : '';
      operatorValue = operatorValue ? operatorValue.trim() : '';
      operatorValueOne = operatorValueOne ? operatorValueOne.trim() : '';
      let startDate = moment(picker.startDate, 'MM/DD/YY');
      let endDate = moment(picker.endDate, 'MM/DD/YY');
      startDate =
        operatorTwo == 'InBetween'
          ? startDate.format('YYYY-MM-DD')
          : singleDate;
      endDate =
        operatorTwo == 'InBetween' ? endDate.format('YYYY-MM-DD') : null;
      let inType = 'rule_creation';
      let operatorOneVal = '';
      let operatorValueOneVal = '';
      let operatorTwoVal = '';
      let startDateVal = null;
      let endDateVal = null;
      if (this.state.openException == true && this.state.openRO == true) {
        operatorOneVal = operatorOne;
        operatorValueOneVal = operatorValueOne;
      }
      if (this.state.openCalender == true) {
        operatorTwoVal = operatorTwo;
        startDateVal = startDate;
        endDateVal = endDate;
      }
      const input = {
        argRoleType: fieldName,
        argActionName: operator,
        argActionValue: operatorValue,
        argExcepActionName: operatorOneVal,
        argExcepActionValue: operatorValueOneVal,
        argExcepDateName: operatorTwoVal,
        argExcepDateValueFrom: startDateVal,
        argExcepDateValueTo: endDateVal,
        inType: inType,
        inTypeView: 1,
        argId: 0,
        inActive: 1,
        preview: 1
      };

      const inputArr = {
        argRoleType: fieldName,
        argActionName: operator,
        argActionValue: operatorValue,
        argExcepActionName: operatorOneVal,
        argExcepActionValue: operatorValueOneVal,
        argExcepDateName: operatorTwoVal,
        argExcepDateValueFrom: startDateVal,
        argExcepDateValueTo: endDateVal,
        inType: inType,
        inTypeView: 0,
        argId: 0,
        inActive: 1,
        preview: 1
      };
      this.setState({ inputData: inputArr });
      this.setState({ ruleSelected: fieldName });
      // Simulate API or form handler call
      await this.insertRoHide(this.state.save == false ? input : inputArr); // You can define handleSubmitForm as per your logic
      // Optionally reset form
      // resetForm();
    } catch (error) {
    } finally {
      setSubmitting(false); // Re-enable submit button after submission
    }
  }
  // Dummy function to simulate API/form submission
  async handleSubmitForm(input) {
    return new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
  }
  getRoOperand = () => {
    getRoHideColumns(result => {
      if (result.data.statelessCcPhysicalRwGetRoHideColumns.fieldNames) {
        this.setState({
          field: result.data.statelessCcPhysicalRwGetRoHideColumns.fieldNames
        });
      }
    });
    getRoHideOprators(result => {
      if (result.data.statelessCcPhysicalRwGetRoHideOperators.operatorVals) {
        this.setState({
          operator:
            result.data.statelessCcPhysicalRwGetRoHideOperators.operatorVals
        });
      }
    });
  };
  handleApply = (e, picker, setFieldValue) => {
    console.log('picker==', picker);
    setFieldValue('startDate', picker.startDate.format('MM/DD/YY'));
    setFieldValue('endDate', picker.endDate.format('MM/DD/YY'));
  };
  handleCallback = (event, picker) => {
    console.log('picker==', picker);
    this.setState({ filterDisabled: false });
    this.setState({
      value:
        picker.startDate.format('MM/DD/YY') +
        ' - ' +
        picker.endDate.format('MM/DD/YY')
    });
  };
  handleEventCallback = (event, picker) => {};
  render() {
    console.log(
      'openRO===',
      this.state.openRO,
      'disabledCalender===',
      this.state.disabledCalender,
      'disabledException===',
      this.state.disabledException,
      'openOpcode==',
      this.state.openOpcode,
      'addCal==',
      this.state.addCal,
      'openCalender==',
      this.state.openCalender,
      'openException==',
      this.state.openException,
      'roval==',
      this.state.roVal,
      '===',
      this.state.openDeleteDialog
    );
    console.log(
      'previewRowData====class========',
      this.state.addCal,
      this.state.roVal,
      'viewRo==',
      this.state.viewRo
    );
    const { classes } = this.props;
    const { openException, openRO, openCalender } = this.state;
    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,
            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Tab
              label={<div>Hide RO - Rules</div>}
              value="one"
              style={{ pointerEvents: 'none', textTransform: 'none' }}
            />
            <Link
              id="export-to-excel"
              style={{ paddingRight: 12, cursor: 'pointer', marginTop: 11 }}
              onClick={this.onBtExport}
            >
              <Tooltip title="Export To Excel">
                <ExportIcon />
              </Tooltip>
            </Link>
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>
        {/* <Paper
          square
          style={{
            marginLeft: 8,
            marginRight: 8,
            paddingTop: 9,
            display: 'flex',
            height: '54px',
            justifyContent: 'space-between'
          }}
        >
          <div className={classes.btnContainer}>
            <Button
              variant="contained"
              className={clsx('reset-btn', classes.btnUser)}
              onClick={this.addEmail}
              disabled={
                this.props.keycloak &&
                this.props.keycloak.realmAccess.roles.includes('admin') ==
                  false &&
                this.props.keycloak.realmAccess.roles.includes('superadmin') ==
                  false
                  ? true
                  : false
              }
            >
              Add Rule
            </Button>
          </div>
        </Paper> */}
        {this.state.showAddRule && (
          <Paper
            square
            style={{
              marginLeft: 8,
              marginRight: 8,
              paddingTop: 9,
              paddingBottom: 9,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              minHeight: '54px',
              overflow: 'visible'
            }}
          >
            <Formik
              innerRef={this.formikRef}
              initialValues={{
                fieldName: 'RoNumber',
                operator: 'EqualTo',
                operatorValue: '',
                operatorOne: 'NotEqualTo',
                operatorTwo: 'IsEqualTo',
                startDate: '',
                endDate: '',
                singleDate: moment().format('MM/DD/YY'),
                operatorValueOne: '',
                id: '',
                picker: {
                  startDate: moment(this.state.startDate).format('MM/DD/YY'),
                  endDate: moment(this.state.endDate).format('MM/DD/YY')
                }
              }}
              validationSchema={this.validationSchema({
                openException,
                openRO,
                openCalender
              })}
              validateOnMount
              onSubmit={this.handleSubmit} // Your submit handler
              validateOnChange={true}
            >
              {({
                isSubmitting,
                values,
                setFieldValue,
                setFieldError,
                setTouched
              }) => (
                <Form style={{ width: '100%' }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} sm={2} style={{ marginLeft: '15px' }}>
                      <Field
                        name="fieldName"
                        as={TextField}
                        select
                        label="Type"
                        variant="outlined"
                        size="small"
                        fullWidth
                        value={values.fieldName}
                        onChange={e => {
                          let fieldName = e.target.value.trim();
                          setFieldValue('fieldName', fieldName);
                          setFieldValue('operator', '');
                          setFieldValue('operatorValue', '');
                          setFieldValue('operatorTwo', '');
                          setFieldValue('operatorValueOne', '');

                          setFieldError('fieldName', '');
                          setFieldError('operator', '');
                          setFieldError('operatorValue', '');
                          setFieldError('operatorTwo', '');
                          setFieldError('operatorValueOne', '');

                          setTouched({
                            fieldName: false,
                            operator: false,
                            operatorValue: false,
                            operatorTwo: false,
                            operatorValueOne: false
                          });
                          this.setState({ ruleValue: '' });
                          this.setState({
                            openRO: false
                            //  roVal: false
                          });
                          this.setState({
                            openCalender: false,
                            disabledException: false,
                            disabledCalender: false,
                            openException: false
                          });

                          fieldName === 'Opcategory'
                            ? setFieldValue('operator', 'EqualTo')
                            : setFieldValue('operator', 'EqualTo');
                          fieldName === 'Opcategory'
                            ? this.setState({ openOpcat: true })
                            : this.setState({ openOpcat: false });
                          fieldName === 'Opcode' || fieldName === 'RepairNumber'
                            ? this.setState({ openOpcode: true })
                            : this.setState({ openOpcode: false });
                          this.setState({
                            addCal: fieldName === 'RoNumber',
                            roVal: fieldName === 'RoNumber'
                          });
                        }}
                        SelectProps={{
                          MenuProps: {
                            anchorOrigin: {
                              vertical: 'bottom',
                              horizontal: 'left'
                            },
                            transformOrigin: {
                              vertical: 'top',
                              horizontal: 'left'
                            },
                            getContentAnchorEl: null // This ensures the dropdown opens below the field
                          }
                        }}
                      >
                        {/* <MenuItem value={'Opcategory'}>
                          {'Op Category'}
                        </MenuItem>
                        <MenuItem value={'Opcode'}>{'Opcode'}</MenuItem> */}
                        <MenuItem value={'RoNumber'}>{'RO Number'}</MenuItem>
                        {/* <MenuItem value={'RepairNumber'}>
                          {'Repair Number'}
                        </MenuItem> */}
                      </Field>
                    </Grid>
                    <ErrorMessage name="fieldName" component={FormErrorMsg} />

                    <Grid item xs={12} sm={2}>
                      {this.state.openOpcat == false ? (
                        <Field
                          name="operator"
                          as={TextField}
                          select
                          label="Action"
                          variant="outlined"
                          size="small"
                          fullWidth
                          value={values.operator}
                          onChange={e => {
                            let operator = e.target.value.trim();
                            setFieldValue('operator', operator);
                            // Access the current fieldName value
                            const fieldName = values.fieldName;
                            // Update state based on the fieldName and operator conditions
                            this.setState({
                              openRO:
                                (fieldName === 'Opcode' &&
                                  operator !== 'EqualTo') ||
                                (fieldName === 'RepairNumber' &&
                                  operator !== 'EqualTo') ||
                                (fieldName === 'RoNumber' &&
                                  operator !== 'EqualTo')
                            });
                            this.setState({
                              openException: false,
                              disabledException: false,
                              openOpcode:
                                fieldName === 'Opcode' ||
                                fieldName === 'RepairNumber'
                                  ? true
                                  : false
                            });

                            // this.setState({
                            //   disabledException:
                            //     fieldName === 'RoNumber' &&
                            //     operator == 'EqualTo'
                            // });
                            this.setState({
                              addCal: fieldName === 'RoNumber',
                              roVal: fieldName === 'RoNumber'
                            });
                          }}
                          SelectProps={{
                            MenuProps: {
                              anchorOrigin: {
                                vertical: 'bottom',
                                horizontal: 'left'
                              },
                              transformOrigin: {
                                vertical: 'top',
                                horizontal: 'left'
                              },
                              getContentAnchorEl: null // This ensures the dropdown opens below the field
                            }
                          }}
                        >
                          <MenuItem value={'BeginsWith'}>
                            {'Begins With'}
                          </MenuItem>
                          <MenuItem value={'Contains'}>{'Contains'}</MenuItem>
                          <MenuItem value={'EndsWith'}>{'Ends With'}</MenuItem>
                          <MenuItem value={'EqualTo'}>{'Equal To'}</MenuItem>
                        </Field>
                      ) : (
                        <Field
                          name="operator"
                          as={TextField}
                          select
                          label="Action"
                          variant="outlined"
                          size="small"
                          fullWidth
                          value={values.operator}
                          onChange={e => {
                            let operator = e.target.value.trim(); // Use trim to remove spaces
                            setFieldValue('operator', operator);
                          }}
                          SelectProps={{
                            MenuProps: {
                              anchorOrigin: {
                                vertical: 'bottom',
                                horizontal: 'left'
                              },
                              transformOrigin: {
                                vertical: 'top',
                                horizontal: 'left'
                              },
                              getContentAnchorEl: null // This ensures the dropdown opens below the field
                            }
                          }}
                        >
                          <MenuItem value={'EqualTo'}>{'Equal To'}</MenuItem>
                        </Field>
                      )}
                    </Grid>
                    <ErrorMessage name="operator" component={FormErrorMsg} />
                    <Grid item xs={12} sm={2}>
                      {this.state.openOpcat == false ? (
                        <Field
                          name="operatorValue"
                          as={TextField}
                          label="Value"
                          variant="outlined"
                          size="small"
                          fullWidth
                          value={values.operatorValue}
                          // onChange={e => {
                          //   let operatorValue = e.target.value
                          //     .trim()
                          //     .replace(/\s{2,}/g, ' ')
                          //     .replace(/\s+/g, '');
                          //   setFieldValue('operatorValue', operatorValue);
                          //   this.setState({ ruleValue: operatorValue });
                          // }}
                          onChange={e => {
                            let operatorValue = e.target.value
                              .replace(/^\s+/, '')
                              .replace(/^\_/, '')
                              .replace(/\s{2,}/g, ' ');
                            setFieldValue('operatorValue', operatorValue);
                            this.setState({
                              ruleValue: operatorValue
                            });
                          }}
                          // onChange={e => {
                          //   const fieldName = values.fieldName;
                          //   let operatorValue = e.target.value
                          //     .replace(/\s{2,}/g, ' ');
                          //   setFieldValue('operatorValue', operatorValue);
                          //   this.setState({
                          //     ruleValue: operatorValue
                          //   });
                          // }}
                          onKeyPress={e => {
                            if (e.key === 'Enter') {
                              e.preventDefault();
                            }
                          }}
                        />
                      ) : (
                        <Field
                          name="operatorValue"
                          as={TextField}
                          select
                          label="Value"
                          variant="outlined"
                          size="small"
                          fullWidth
                          value={values.operatorValue}
                          onChange={e => {
                            let operatorValue = e.target.value.trim(); // Use trim to remove spaces
                            setFieldValue('operatorValue', operatorValue);
                            this.setState({ ruleValue: operatorValue });
                          }}
                          SelectProps={{
                            MenuProps: {
                              anchorOrigin: {
                                vertical: 'bottom',
                                horizontal: 'left'
                              },
                              transformOrigin: {
                                vertical: 'top',
                                horizontal: 'left'
                              },
                              getContentAnchorEl: null // This ensures the dropdown opens below the field
                            }
                          }}
                        >
                          <MenuItem value={'COMPETITIVE'}>
                            {'COMPETITIVE'}
                          </MenuItem>
                          <MenuItem value={'MAINTENANCE'}>
                            {'MAINTENANCE '}
                          </MenuItem>
                          <MenuItem value={'N/A'}>{'N/A'}</MenuItem>
                          <MenuItem value={'REPAIR'}>{'REPAIR'}</MenuItem>
                        </Field>
                      )}
                    </Grid>
                    <ErrorMessage
                      name="operatorValue"
                      component={FormErrorMsg}
                    />

                    <Grid item xs={12} sm={4} style={{ marginLeft: 15 }}>
                      {this.state.openRO == true && (
                        <Button
                          type="submit" // Make sure to set the type to submit
                          variant="contained"
                          color="primary"
                          // onClick={this.addException}
                          onClick={() => this.addException(setTouched)}
                          className={clsx('reset-btn', classes.btnDiv)}
                          classes={{ label: classes.btnLabel }}
                          disabled={this.state.disabledException}
                        >
                          Add Exception
                        </Button>
                      )}
                      {((this.state.openRO == true &&
                        this.state.openOpcode == false) ||
                        (this.state.addCal == true &&
                          this.state.roVal == true)) && (
                        <Button
                          type="submit" // Make sure to set the type to submit
                          variant="contained"
                          color="primary"
                          // onClick={this.addCalender}
                          onClick={() => this.addCalender(setTouched)}
                          className={clsx('reset-btn', classes.btnDiv)}
                          classes={{ label: classes.btnLabel }}
                          disabled={
                            this.state.ruleValue == ''
                              ? true
                              : this.state.disabledCalender
                          }
                          style={{
                            marginLeft: 10
                          }}
                        >
                          Add Closed Date
                        </Button>
                      )}

                      {/* {this.state.disabledCalender == false &&
                        this.state.disabledException == false && ( */}
                      {/* ||
                          this.state.addCal == false */}
                      {/* <> */}
                      <Button
                        type="submit" // Make sure to set the type to submit
                        variant="contained"
                        color="primary"
                        disabled={isSubmitting}
                        className={clsx('reset-btn', classes.btnDiv)}
                        classes={{ label: classes.btnLabel }}
                        style={{
                          marginLeft: 10
                        }}
                        disabled={this.state.ruleValue == '' ? true : false}
                        // onClick={() => this.saveChanges(setTouched)}
                        onClick={e => {
                          this.setState({
                            save: true
                          });
                        }}
                      >
                        {'Save'}
                      </Button>
                      <Button
                        type="submit" // Make sure to set the type to submit
                        variant="contained"
                        color="primary"
                        disabled={isSubmitting}
                        className={clsx('reset-btn', classes.btnDiv)}
                        classes={{ label: classes.btnLabel }}
                        style={{
                          marginLeft: 10
                        }}
                        disabled={this.state.ruleValue == '' ? true : false}
                        onClick={e => {
                          this.setState({
                            save: false
                          });
                        }}
                      >
                        {'Preview And Save'}
                      </Button>
                      {/* <Button
                              type="submit" // Make sure to set the type to submit
                              variant="contained"
                              color="primary"
                              onClick={this.closeEmail}
                              className={clsx('reset-btn', classes.btnDiv)}
                              classes={{ label: classes.btnLabel }}
                              style={{
                                marginLeft: 7
                              }}
                            >
                              Close
                            </Button>{' '} */}
                      {/* </>
                        )} */}
                    </Grid>
                    {this.state.disabledException == true && (
                      <Grid item xs={12} sm={2} className={classes.labelPos}>
                        <Typography variant="body1" className={classes.label}>
                          Add Exception<span className={classes.star}>*</span>
                        </Typography>
                      </Grid>
                    )}
                    {this.state.openException == true &&
                      this.state.openRO == true && (
                        <>
                          <Grid item xs={12} sm={2} style={{ marginLeft: 15 }}>
                            <Field
                              name="operatorOne"
                              as={TextField}
                              select
                              label="Action"
                              variant="outlined"
                              size="small"
                              fullWidth
                              value={values.operatorOne}
                              onChange={e => {
                                let operatorOne = e.target.value.trim(); // Use trim to remove spaces
                                setFieldValue('operatorOne', operatorOne);
                              }}
                              SelectProps={{
                                MenuProps: {
                                  anchorOrigin: {
                                    vertical: 'bottom',
                                    horizontal: 'left'
                                  },
                                  transformOrigin: {
                                    vertical: 'top',
                                    horizontal: 'left'
                                  },
                                  getContentAnchorEl: null // This ensures the dropdown opens below the field
                                }
                              }}
                            >
                              <MenuItem value={'NotEqualTo'}>
                                {'Not Equal To'}
                              </MenuItem>
                            </Field>
                          </Grid>
                          <ErrorMessage
                            name="operatorOne"
                            component={FormErrorMsg}
                          />
                          <Grid item xs={12} sm={2}>
                            <Field
                              name="operatorValueOne"
                              as={TextField}
                              label="Value"
                              variant="outlined"
                              size="small"
                              fullWidth
                              value={values.operatorValueOne}
                              onChange={e => {
                                let operatorValueOne = e.target.value
                                  .trim()
                                  .replace(/\s{2,}/g, ' ');
                                setFieldValue(
                                  'operatorValueOne',
                                  operatorValueOne
                                );
                              }}
                              onKeyPress={e => {
                                if (e.key === 'Enter') {
                                  e.preventDefault(); // Prevent form submission on Enter key
                                }
                              }}
                            />
                          </Grid>
                          <ErrorMessage
                            name="operatorValueOne"
                            component={FormErrorMsg}
                          />
                        </>
                      )}
                    {this.state.openException == true &&
                      this.state.openRO == true && (
                        <IconButton
                          onClick={() =>
                            this.deleteException(setTouched, setFieldValue)
                          }
                          aria-label="close"
                          style={{
                            float: 'inline-end',
                            justifyContent: 'right',
                            marginLeft: 9
                          }}
                        >
                          <Tooltip title="Close">
                            <HighlightOffIcon style={{ fontSize: 30 }} />
                          </Tooltip>
                        </IconButton>
                        // <Button
                        //   type="submit" // Make sure to set the type to submit
                        //   variant="contained"
                        //   color="primary"
                        //   // onClick={this.deleteException}
                        //   onClick={() => this.deleteException(setTouched)}
                        //   className={clsx('reset-btn', classes.btnDiv)}
                        //   classes={{ label: classes.btnLabel }}
                        //   style={{ marginLeft: 22 }}
                        // >
                        //   Delete
                        // </Button>
                      )}
                    {this.state.disabledCalender == true &&
                      this.state.disabledException == true && (
                        <Grid item xs={12} sm={2}></Grid>
                      )}
                    {/* {this.state.disabledCalender == true &&
                      this.state.disabledException == true && (
                        <Grid item xs={12} sm={6}></Grid>
                      )} */}
                    {this.state.disabledCalender == true &&
                      (this.state.disabledCalender == true &&
                      this.state.disabledException == true ? (
                        <Grid item xs={12} sm={2}></Grid>
                      ) : (
                        <Grid item xs={12} sm={2} className={classes.labelPos}>
                          <Typography
                            variant="body1"
                            className={classes.labelDate}
                          >
                            Closed Date
                            <span className={classes.star}>*</span>
                          </Typography>
                        </Grid>
                      ))}
                    {this.state.disabledCalender == true &&
                      this.state.disabledException == true && (
                        <Grid item xs={12} sm={2} className={classes.labelPos}>
                          <Typography
                            variant="body1"
                            className={classes.labelDate}
                          >
                            Closed Date
                            <span className={classes.star}>*</span>
                          </Typography>
                        </Grid>
                      )}
                    {this.state.openCalender == true && (
                      <>
                        <Grid item xs={12} sm={2} style={{ marginLeft: 15 }}>
                          <Field
                            name="operatorTwo"
                            as={TextField}
                            select
                            label="Action"
                            variant="outlined"
                            size="small"
                            fullWidth
                            value={values.operatorTwo}
                            onChange={e => {
                              let operatorTwo = e.target.value.trim(); // Use trim to remove spaces
                              setFieldValue('operatorTwo', operatorTwo);
                              setFieldValue('picker', {
                                startDate: moment().format('MM/DD/YY'),
                                endDate: moment().format('MM/DD/YY')
                              });
                              this.setState({
                                inBetween: operatorTwo === 'InBetween'
                              });
                            }}
                            SelectProps={{
                              MenuProps: {
                                anchorOrigin: {
                                  vertical: 'bottom',
                                  horizontal: 'left'
                                },
                                transformOrigin: {
                                  vertical: 'top',
                                  horizontal: 'left'
                                },
                                getContentAnchorEl: null // This ensures the dropdown opens below the field
                              }
                            }}
                          >
                            <MenuItem value={'InBetween'}>{'Between'}</MenuItem>
                            <MenuItem value={'IsEqualTo'}>
                              {'Equal To'}
                            </MenuItem>
                            <MenuItem value={'IsLessThanOrEqualTo'}>
                              {'Less Than Or Equal To'}
                            </MenuItem>
                          </Field>
                        </Grid>
                        <ErrorMessage
                          name="operatorTwo"
                          component={FormErrorMsg}
                        />
                      </>
                    )}
                    {this.state.openCalender == true &&
                      this.state.inBetween == false && (
                        <>
                          <Grid item xs={12} sm={2} style={{ width: '100%' }}>
                            <FormControl
                              variant="outlined"
                              margin="dense"
                              className={clsx(
                                classes.formControl,
                                'input-container'
                              )}
                              style={{ width: '100%' }}
                            >
                              <DateRangePicker
                                initialSettings={{
                                  locale: {
                                    format: 'MM/DD/YY'
                                  },
                                  singleDatePicker: true,
                                  startDate: moment().toDate(),
                                  // minDate: moment()
                                  //   .subtract(1, 'years')
                                  //   .toDate(),
                                  maxDate: moment().toDate(),
                                  alwaysShowCalendars: false,
                                  // applyClass: clsx('apply-btn'),
                                  // cancelClass: clsx('cancel-btn'),
                                  applyClass: clsx(
                                    classes.calButton,
                                    'apply-btn'
                                  ),
                                  cancelClass: clsx(
                                    classes.calButton,
                                    'apply-btn'
                                  )
                                }}
                                onApply={(e, picker) => {
                                  // Update Formik value when date is selected
                                  setFieldValue(
                                    'singleDate',
                                    picker.startDate.format('MM/DD/YY')
                                  );
                                }}
                                style={{ width: '100%' }}
                              >
                                <div>
                                  <input
                                    type="text"
                                    className="rodatepicker"
                                    id="picker"
                                    name="singleDate"
                                    value={values.singleDate}
                                    aria-labelledby="label-picker"
                                    style={{
                                      // width: 264,
                                      marginLeft: -9,
                                      marginTop: -1,
                                      height: 37,
                                      width: '100%'
                                    }}
                                    // onChange={e => {
                                    //   const inputDate = e.target.value.trim();
                                    //   setFieldValue('singleDate', inputDate);
                                    //   if (
                                    //     inputDate &&
                                    //     !moment(
                                    //       inputDate,
                                    //       'MM/DD/YY',
                                    //       true
                                    //     ).isValid()
                                    //   ) {
                                    //     setTouched({
                                    //       singleDate: true
                                    //     });
                                    //   } else {
                                    //     setFieldError('singleDate', ''); // Clear error when valid
                                    //   }
                                    // }}
                                    // onBlur={e => {
                                    //   const inputDate = e.target.value.trim();
                                    //   if (
                                    //     !moment(
                                    //       inputDate,
                                    //       'MM/DD/YY',
                                    //       true
                                    //     ).isValid()
                                    //   ) {
                                    //     setTouched({ singleDate: true });
                                    //     setFieldError(
                                    //       'singleDate',
                                    //       'Invalid date format. Use MM/DD/YY'
                                    //     );
                                    //   } else if (inputDate.length < 8) {
                                    //     setTouched({ singleDate: true });
                                    //     setFieldError(
                                    //       'singleDate',
                                    //       'Incomplete date. Use MM/DD/YY'
                                    //     );
                                    //   } else {
                                    //     setTouched({ singleDate: false });
                                    //     setFieldError('singleDate', '');
                                    //   }
                                    // }}
                                    // onChange={e => {
                                    //   const inputDate = e.target.value.trim();
                                    //   setFieldValue('singleDate', inputDate);
                                    //   if (
                                    //     !moment(
                                    //       inputDate,
                                    //       'MM/DD/YY',
                                    //       true
                                    //     ).isValid()
                                    //   ) {
                                    //     setTouched({ singleDate: true });
                                    //     setFieldError(
                                    //       'singleDate',
                                    //       'Invalid date format. Use MM/DD/YY'
                                    //     );
                                    //   } else if (inputDate.length < 8) {
                                    //     setTouched({ singleDate: true });
                                    //     setFieldError(
                                    //       'singleDate',
                                    //       'Incomplete date. Use MM/DD/YY'
                                    //     );
                                    //   } else {
                                    //     setTouched({ singleDate: false });
                                    //     setFieldError('singleDate', '');
                                    //   }
                                    // }}
                                  />
                                  <label
                                    className="rolabelpicker"
                                    htmlFor="picker"
                                    id="label-picker"
                                  >
                                    <div className="textpicker">
                                      Select Date
                                    </div>
                                  </label>
                                </div>
                              </DateRangePicker>
                            </FormControl>
                          </Grid>
                          <ErrorMessage
                            name="singleDate"
                            component={FormErrorMsg}
                          />
                        </>
                      )}
                    {this.state.openCalender == true &&
                      this.state.inBetween == true && (
                        <>
                          <Grid item xs={12} sm={2} style={{ width: '100%' }}>
                            <FormControl
                              variant="outlined"
                              margin="dense"
                              className={clsx(
                                classes.formControl,
                                'input-container'
                              )}
                              style={{ width: '100%' }}
                            >
                              <DateRangePicker
                                initialSettings={{
                                  // maxDate: {
                                  //   date: new Date(),
                                  // },
                                  // minDate:{
                                  //   date: (this.props.selectedDates[1])
                                  // },
                                  locale: {
                                    format: 'MM/DD/YY',
                                    separator: ' - '
                                  },
                                  startDate: moment().toDate(),
                                  endDate: moment().toDate(),
                                  ranges: {
                                    ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.today]: [
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].today
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].today
                                      ).toDate()
                                    ],
                                    ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.yesterDay]: [
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].yesterday
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].yesterday
                                      ).toDate()
                                    ],
                                    ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.dayBfYest]: [
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].dayBeforeYesterday
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].dayBeforeYesterday
                                      ).toDate()
                                    ],
                                    ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.lastWeek]: [
                                      moment(
                                        this.props.dates[0].lastweekstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0].lastweekenddate
                                      ).toDate()
                                    ],
                                    // 'Last 7 Days': [
                                    //   moment()
                                    //     .subtract(6, 'days')
                                    //     .toDate(),
                                    //   moment().toDate()
                                    // ],
                                    // 'Last 30 Days': [
                                    //   moment()
                                    //     .subtract(29, 'days')
                                    //     .toDate(),
                                    //   moment().toDate()
                                    // ],
                                    ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.mtd]: [
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].mtdstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].mtdenddate
                                      ).toDate()
                                    ],
                                    ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.lastMonth]: [
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].lastmonthstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0] &&
                                          this.props.dates[0].lastmonthenddate
                                      ).toDate()
                                    ],
                                    ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.lastThreeMonths]: [
                                      moment(
                                        this.props.dates[0]
                                          .lastthreemonthstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0]
                                          .lastthreemonthenddate
                                      ).toDate()
                                    ],
                                    ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.lastQtr]: [
                                      moment(
                                        this.props.dates[0].lastquarterstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0].lastquarterenddate
                                      ).toDate()
                                    ],
                                    ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.ytd]: [
                                      moment(
                                        this.props.dates[0].ytdstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0].ytdenddate
                                      ).toDate()
                                    ],
                                    ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.lastTwelveMonths]: [
                                      moment(
                                        this.props.dates[0]
                                          .lasttwelvemonthstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0]
                                          .lasttwelvemonthenddate
                                      ).toDate()
                                    ],
                                    ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                                    this.props.lastYear]: [
                                      moment(
                                        this.props.dates[0].lastyearstartdate
                                      ).toDate(),
                                      moment(
                                        this.props.dates[0].lastyearenddate
                                      ).toDate()
                                    ]
                                  },
                                  // maxDate: moment(this.state.closedDate).toDate(),
                                  //   maxDate: moment().toDate(),
                                  maxDate: moment(
                                    this.props.dates[0] &&
                                      this.props.dates[0].today
                                  ).toDate(),
                                  alwaysShowCalendars: false,
                                  applyClass: clsx(
                                    classes.calButton,
                                    'apply-btn'
                                  ),
                                  cancelClass: clsx(
                                    classes.calButton,
                                    'apply-btn'
                                  )
                                }}
                                onEvent={this.handleEventCallback}
                                // onApply={this.handleCallback}

                                onApply={(e, picker) => {
                                  // Update Formik's picker values on date selection
                                  setFieldValue('picker', {
                                    startDate: picker.startDate.format(
                                      'MM/DD/YY'
                                    ),
                                    endDate: picker.endDate.format('MM/DD/YY')
                                  });
                                }}
                                style={{ width: '100%' }}
                              >
                                {/* <input
                                  type="text"
                                  className="datepicker"
                                  id="picker"
                                  name="picker"
                                  aria-labelledby="label-picker"
                                /> */}
                                <input
                                  type="text"
                                  className="datepicker"
                                  id="picker"
                                  name="picker"
                                  value={`${values.picker.startDate} - ${values.picker.endDate}`}
                                  aria-labelledby="label-picker"
                                  readOnly
                                  style={{
                                    // width: 264,
                                    marginLeft: -9,
                                    marginTop: -1,
                                    height: 37,
                                    width: '100%',
                                    fontWeight: ''
                                  }}
                                />
                              </DateRangePicker>
                              <label
                                class="labelpicker"
                                for="picker"
                                id="label-picker"
                              >
                                <div class="textpicker">Select Date</div>
                              </label>
                            </FormControl>
                          </Grid>
                          <ErrorMessage
                            name="picker"
                            component={FormErrorMsg}
                          />
                        </>
                      )}
                    {this.state.openCalender == true && (
                      <IconButton
                        onClick={() =>
                          this.deleteCalender(setTouched, setFieldValue)
                        }
                        aria-label="close"
                        style={{
                          float: 'inline-end',
                          justifyContent: 'right',
                          marginLeft: 9
                        }}
                      >
                        <Tooltip title="Close">
                          <HighlightOffIcon style={{ fontSize: 30 }} />
                        </Tooltip>
                      </IconButton>
                      // <Button
                      //   type="submit" // Make sure to set the type to submit
                      //   variant="contained"
                      //   color="primary"
                      //   // onClick={this.deleteCalender}
                      //   onClick={() => this.deleteCalender(setTouched)}
                      //   className={clsx('reset-btn', classes.btnDiv)}
                      //   classes={{ label: classes.btnLabel }}
                      //   style={{ marginLeft: 25 }}
                      // >
                      //   Delete
                      // </Button>
                    )}
                    {/* {(this.state.disabledCalender == true ||
                      this.state.disabledException == true) &&
                      (this.state.openRO == true ||
                        this.state.addCal == true) && (
                        <Grid item xs={12} sm={2} style={{ marginLeft: 0 }}>
                          <Button
                            type="submit" // Make sure to set the type to submit
                            variant="contained"
                            color="primary"
                            disabled={isSubmitting}
                            className={clsx('reset-btn', classes.btnDiv)}
                            classes={{ label: classes.btnLabel }}
                            
                          >
                            {'Preview And Save'}
                          </Button> */}
                    {/* <Button
                            type="submit" // Make sure to set the type to submit
                            variant="contained"
                            color="primary"
                            onClick={this.closeEmail}
                            className={clsx('reset-btn', classes.btnDiv)}
                            classes={{ label: classes.btnLabel }}
                            style={{
                              marginLeft: 7
                            }}
                          >
                            Close
                          </Button> */}
                    {/* </Grid>
                      )} */}
                  </Grid>
                </Form>
              )}
            </Formik>
          </Paper>
        )}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : (
          <div
            id="data-tab"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 170 + 'px',
              width: '1370px',
              margin: 8,
              display: this.state.tabSelection == 'two' ? 'none' : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader={true}
              rowData={this.state.rowData}
              editType={this.state.editType}
              excelStyles={this.state.excelStyles}
              tooltipShowDelay={0}
              container="body"
              suppressClickEdit={true}
              onCellClicked={this.onCellClicked}
              floatingFilter={true}
              enableRangeSelection={true}
              animateRows={true}
              enableCharts={true}
              suppressRowClickSelection={true}
              context={this.state.context}
              components={this.state.components}
              onRowEditingStarted={this.onRowEditingStarted}
              onRowEditingStopped={this.onRowEditingStopped}
              onFilterChanged={this.onFilterChanged}
              onSortChanged={this.onSortChanged}
              frameworkComponents={this.state.frameworkComponents}
              suppressDragLeaveHidesColumns={true}
              suppressContextMenu={true}
            />
          </div>
        )}
        {this.state.previewRo && (
          <PreviewRo
            openPopup={this.state.previewRo}
            handlePopupClose={this.closePreview}
            handleSubmitForm={this.insertPreviewRowData}
            previewRowData={this.state.previewRowData}
            previewLoad={this.state.previewLoad}
            ruleSelected={this.state.ruleSelected}
          />
        )}
        {this.state.openView && (
          <ViewRo
            openPopup={this.state.openView}
            handlePopupClose={this.closeView}
            previewRowData={this.state.viewRowData}
            previewLoad={this.state.previewLoad}
            ruleSelected={this.state.ruleSelected}
          />
        )}
        {this.state.roInsertMsg && (
          <SuccessSnackbar
            open={this.state.openSnackbar}
            onClose={this.hidesnackbar}
            msg={this.state.roInsertMsg}
            goalFail={this.state.goalFail}
          />
        )}
        <Dialog
          //fullWidth
          //maxWidth="sm"
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openDeleteDialog}
        >
          <DialogContent>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to delete the rule?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={this.handleCancelDelete}>
              Cancel
            </Button>
            <Button onClick={this.handleDelete} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}

const styles = theme => ({
  star: {
    color: 'red',
    padding: '5px'
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 13,
    marginTop: '6px',
    width: '176px',
    // paddingRight: 68,
    marginLeft: -19
  },
  labelPos: {
    textAlign: 'right'
  },
  labelDate: {
    color: 'rgb(0, 61, 107)',
    fontSize: 13,
    marginTop: '6px',
    width: '176px',
    // paddingRight: 68,
    marginLeft: 25
  },
  btnDiv: {
    minWidth: 150,
    // width: 60,
    height: '27px !important'
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: 8,
    width: 'auto'
  },
  btnContainer: {
    display: 'flex',
    width: '845px',
    justifyContent: 'space-between',
    width: '100%',
    marginRight: 25
  },
  btnUser: {
    height: '27px',
    fontSize: '12px !important',
    marginTop: '4px',
    width: 110,
    paddingRight: -29,
    paddingLeft: -18,
    left: 15
  },
  TextFieldValue: {
    '& .MuiOutlinedInput-root': {
      height: '40px' // Custom padding for the input field
    }
  },
  btnLabel: {
    fontSize: '11px'
  }
});

export default withStyles(styles)(withKeycloak(RoShowHide));
