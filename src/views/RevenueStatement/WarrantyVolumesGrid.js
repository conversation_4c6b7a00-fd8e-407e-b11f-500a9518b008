import React, { useEffect, useState } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import RevenueStatementGrid from './RevenueStatementGrid';
import { useDispatch } from 'react-redux';
import { TableBody, Table, TableHead } from '@material-ui/core';
import TableCell from '@material-ui/core/TableCell';

import TableRow from '@material-ui/core/TableRow';
//import queryString from 'query-string';
import { formatCellValue, formatCellValueCount } from 'src/utils/Utils';
import {
  getRevenueSummaryWarrantyVolumesLaborNew,
  getRevenueSummaryWarrantyVolumesPartsNew
} from 'src/utils/hasuraServices';
import SummaryDataGrid from './SummaryDataGrid';
import SummaryDataHeader from './SummaryDataHeader';
var lodash = require('lodash');
const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      //color: '#ccc !important'
    }
    // padding: 10
  }
}))(TableRow);
const StyledTableCellHeader = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    fontSize: 10,
    maxWidth: 4,
    paddingLeft: '1px'
  },
  body: {
    fontSize: 10,
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    maxWidth: 4,
    paddingLeft: '7px'
  }
}))(TableCell);
const StyledTableCellSubHeader = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',

    fontWeight: 'bold',
    padding: 10,
    maxWidth: 4
  },
  body: {
    fontSize: 10,
    padding: 10,

    border: '1px solid #100101 !important',
    fontWeight: 'bold',
    maxWidth: 4
  }
}))(TableCell);
const StyledTableCellHeaderMain = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    fontSize: 12,
    maxWidth: 5,
    backgroundColor: '#ddeaf4'
  },
  body: {
    fontSize: 12,
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    maxWidth: 5
  }
}))(TableCell);

const StyledTableCell = withStyles(theme => ({
  head: {
    color: '#100101 !important',
    border: '1px solid #ccc !important',
    lineHeight: '0.2rem',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: '1px solid #ccc !important'
  }
}))(TableCell);
const WarrantyVolumesGrid = props => {
  const [summaryData, setSummaryData] = useState([]);
  const [summaryDataParts, setSummaryDataParts] = useState([]);
  useEffect(() => {
    if (props.type == 'labor') {
      getRevenueSummaryWarrantyVolumesLaborNew(
        props.currMonth,
        props.optMonth,
        result => {
          if (
            result.data
              .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesLaborByMonth
              .detailSummaryDetails
          ) {
            //setIsLoading(false);

            const roData =
              result.data
                .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesLaborByMonth
                .detailSummaryDetails;
            let data = JSON.parse(roData[0].jsonData);
            const sorted = lodash.sortBy(
              data.analysis_fs_warranty_volumes_labor_by_month[0].details,
              o => o.heading
            );

            // const sorted = lodash.sortBy(roData, o => o.heading);

            setSummaryData(sorted);
          }
        }
      );
    } else {
      getRevenueSummaryWarrantyVolumesPartsNew(
        props.currMonth,
        props.optMonth,
        result => {
          if (
            result.data
              .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesPartsByMonth
              .detailSummaryDetails
          ) {
            //setIsLoading(false);

            const roData =
              result.data
                .statelessDbdRevenueSummaryGetAnalysisFsWarrantyVolumesPartsByMonth
                .detailSummaryDetails;

            let data = JSON.parse(roData[0].jsonData);

            //console.log('lll', props.componentType);
            const sorted = lodash.sortBy(
              data.analysis_fs_warranty_volumes_parts_by_month[0].details,
              o => o.heading
            );

            setSummaryData(sorted);
          }
        }
      );
    }
    //setIsLoading(false);,props.department
  }, []);
  return (
    <Table style={{ marginBottom: 10, marginTop: 10 }} aria-label="purchases">
      <TableHead>
        <StyledTableRow className="headerTop">
          <StyledTableCellHeaderMain className="title" colSpan={1}>
            RO Count
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Sold Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Actual Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title" colSpan={1}>
            Cost
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Sales
          </StyledTableCellHeaderMain>
          {props.type == 'labor' ? (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Rate
            </StyledTableCellHeaderMain>
          ) : (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Markup
            </StyledTableCellHeaderMain>
          )}
          <StyledTableCellSubHeader
            className="title"
            colSpan={4}
            style={{ width: '20%', backgroundColor: '#ddeaf4' }}
          >
            {props.type == 'labor' ? 'Labor' : 'Parts'}
          </StyledTableCellSubHeader>
          {props.type == 'labor' ? (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Rate
            </StyledTableCellHeaderMain>
          ) : (
            <StyledTableCellHeaderMain className="title" colSpan={1}>
              Markup
            </StyledTableCellHeaderMain>
          )}
          <StyledTableCellHeaderMain className="title">
            Sales
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title" colSpan={1}>
            Cost
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Actual Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            Sold Hours
          </StyledTableCellHeaderMain>
          <StyledTableCellHeaderMain className="title">
            RO Count
          </StyledTableCellHeaderMain>
        </StyledTableRow>
      </TableHead>
      <TableBody>
        {summaryData.length > 0 &&
          summaryData.map((summary, index) => (
            <StyledTableRow className="headerTop">
              {/* {console.log('roData1212345', summary)} */}
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValueCount(summary.sel_jobcount)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.sel_hours)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.actual_hours)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValue(summary.sel_cost)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValue(summary.sel_sale)}
              </StyledTableCellHeader>
              {props.type == 'labor' ? (
                <StyledTableCellHeader className="title">
                  {formatCellValue(summary.sel_elr)}
                </StyledTableCellHeader>
              ) : (
                <StyledTableCellHeader className="title" colSpan={1}>
                  {formatCellValue(summary.sel_markup)}
                </StyledTableCellHeader>
              )}

              <StyledTableCellSubHeader
                className="title"
                colSpan={4}
                style={{ width: '20%', backgroundColor: '#d1d1d1' }}
              >
                {summary.heading}
              </StyledTableCellSubHeader>
              {props.type == 'labor' ? (
                <StyledTableCellHeader className="title">
                  {formatCellValue(summary.user_elr)}
                </StyledTableCellHeader>
              ) : (
                <StyledTableCellHeader className="title" colSpan={1}>
                  {formatCellValue(summary.user_markup)}
                </StyledTableCellHeader>
              )}
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValue(summary.user_sel_cost)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title" colSpan={1}>
                {formatCellValue(summary.user_sel_cost)}
              </StyledTableCellHeader>

              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.user_actual_hours)}
              </StyledTableCellHeader>
              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.user_sel_hours)}
              </StyledTableCellHeader>

              <StyledTableCellHeader className="title">
                {formatCellValueCount(summary.user_sel_jobcount)}
              </StyledTableCellHeader>
            </StyledTableRow>
          ))}
      </TableBody>
    </Table>
  );
};

export default WarrantyVolumesGrid;
