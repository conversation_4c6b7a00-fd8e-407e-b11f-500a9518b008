import React, { useEffect, useState } from 'react';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { useDispatch, useSelector } from 'react-redux';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button,
  TableBody,
  Tooltip
} from '@material-ui/core';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Table from '@material-ui/core/Table';
import { DatePicker } from '@material-ui/pickers';
import TableCell from '@material-ui/core/TableCell';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import TextField from '@material-ui/core/TextField';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import Alert from '@material-ui/lab/Alert';
import {
  getRevenueSummaryRevenueByPaytype,
  getRevenueSummaryRevenueByPaytypeNew,
  getMonthYears
} from 'src/utils/hasuraServices';
import { getLast13Months } from 'src/utils/Utils';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import moment from 'moment';
import { useHistory } from 'react-router';
import SummaryDataGrid from './SummaryDataGrid';
import SummaryDataHeader from './SummaryDataHeader';
import RevenueByComponentGrid from './RevenueByComponentGrid';
import RevenueByCategoryGrid from './RevenueByCategoryGrid';
import JobLevelBreakDownGrid from './JobLevelBreakDownGrid';
import Chip from '@material-ui/core/Chip';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      //color: '#ccc !important'
    },
    padding: 10
  }
}))(TableRow);
const StyledTableCellHeader = withStyles(theme => ({
  head: {
    color: '#fff !important',
    // border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor:
      Dealer == 'Armatus' ? '#003d6b !important' : '#c2185b !important'
  },
  body: {
    fontSize: 14
  }
}))(TableCell);
const StyledTableCellSubHeader = withStyles(theme => ({
  head: {
    border: '1px solid #d1d1d1 !important',
    lineHeight: '1.0rem',
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold'
  }
}))(TableCell);
const StyledTableCell = withStyles(theme => ({
  head: {
    color: '#100101 !important',
    // border: '#ccc !important',
    lineHeight: '0.2rem',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10
    // border: '1px solid #ccc !important'
  }
}))(TableCell);
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  },
  tableTheme: {
    border: '1px solid #adbab9',
    fontSize: '14px',
    marginLeft: '7px'
  },
  drawer: {
    width: '350px',
    flexShrink: 0
  },
  drawerPaper: {
    width: '350px',
    height: '600px',
    top: '140px'
  },

  optBgSelected: {
    background: Dealer == 'Armatus' ? '#003d6b' : '#c2185b',
    textAlign: 'center',
    border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
    borderRadius: '10px',
    margin: '2px'
  },

  optTagSelected: {
    textDecoration: 'none',
    color: '#ffffff'
  },

  optBg: {
    margin: '2px',
    textAlign: 'center',
    border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
    borderRadius: '10px'
  },

  optTag: {
    textDecoration: 'none',
    color: '#000',
    '&:hover, &:focus': {
      background: Dealer == 'Armatus' ? '#003d6b' : '#c2185b',
      border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
      borderRadius: '10px',
      cursor: 'pointer',
      color: '#fff'
    }
  },

  optBgSelected: {
    '&:hover, &:focus': {
      background: '#ffffff',
      border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
      borderRadius: '10px',
      cursor: 'pointer',
      color: '#fff'
    },

    backgroundColor: Dealer == 'Armatus' ? '#003d6b' : '#c2185b',
    textAlign: 'center',
    border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
    borderRadius: '10px',
    margin: '2px',
    color: '#fff !important'
  },

  optTagSelected: {
    color: '#646464',
    textDecoration: 'none',
    '&:hover, &:focus': {
      color: '#646464',
      textDecoration: 'none'
    }
  }
}));

const RevenueStatementGrid = props => {
  const classes = useStyles();
  const dispatch = useDispatch();
  let textInput = React.createRef();

  const { queryMonth, optQueryMonthYear, endMonth } = props;
  const [summaryDataNoFilters, setSummaryDataNoFilters] = useState([]);
  const [summaryData, setSummaryData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [optQueryMonth, setOptQueryMonth] = useState(optQueryMonthYear);
  const [componentType, setComponentType] = useState(props.componentType);
  const [department, setDepartment] = useState(props.department);
  const [payType, setPayType] = useState(null);
  const [monthYear, setMonthYear] = useState('');
  const [componentTypes, setComponentTypes] = useState(props.department);
  const session = useSelector(state => state.session);
  const serviceAdvisor = props && props.session && props.session.serviceAdvisor;
  const [revenuBypayType, setRevenuBypayType] = useState();
  const [partsRevenueData, setPartsRevenueData] = useState();
  const [laborRevenueData, setlaborRevenueData] = useState();

  const [lbrRevenueByCategory, setLbrRevenueByCategory] = useState();
  const [partsRevenueByCategory, setPartsRevenueByCategory] = useState();
  const [jobLevelbrkDown, setJobLevelbrkDown] = useState();
  const [jobLevelbrkDownPerc, setJobLevelbrkDownPerc] = useState();

  const changeRevenueComponentType = params => {
    setComponentType(params);
  };
  const handleDateAccept = params => {
    // console.log('ppp', );
    //console.log('ppp', moment(params).format('YYYY-MM'));
    setOptQueryMonth(moment(params).format('YYYY-MM'));
  };
  const handleDateChange = params => {
    setMonthYear(moment(params).format('YYYY-MM'));
    //setOptQueryMonth(moment(params).format('YYYY-MM'));
  };
  const handleDeptChange = params => {
    setComponentTypes(params);
    setDepartment(params);
  };
  const history = useHistory();

  const [state, setState] = useState({
    right: false
  });
  const loadDrilldown = params => {
    history.push({
      pathname: '/RevenueSummaryDrilldown',
      state: {
        queryMonth:
          typeof queryMonth != 'undefined' ? queryMonth : optQueryMonth,
        drillDownType: params,
        department: department,
        payType: payType,
        serviceAdvisor: serviceAdvisor,
        session: session
        //showCurrentMonth: showCurrentMonth
      }
    });
  };
  const toggleDrawer = (anchor, open) => event => {
    if (
      event.type === 'keydown' &&
      (event.key === 'Tab' || event.key === 'Shift')
    ) {
      return;
    }

    setState({ ...state, [anchor]: open });
  };

  useEffect(() => {
    //console.log('ddddd', queryMonth, optQueryMonth, endMonth);
    setIsLoading(true);

    getRevenueSummaryRevenueByPaytype(
      queryMonth,
      optQueryMonth,
      department,
      serviceAdvisor,

      result => {
        if (
          result.data.statelessDbdRevenueSummaryGetDetailSummary
            .detailSummaryDetails
        ) {
          setIsLoading(false);
          var resultdata =
            result.data.statelessDbdRevenueSummaryGetDetailSummary
              .detailSummaryDetails;

          let data = JSON.parse(resultdata[0].jsonData);
          let rowData = data.detail_summary;
          for (let i = 0; i < rowData.length; i++) {
            if (
              data.detail_summary[i].group_title ==
              'Customer Labor Revenue By Category'
            ) {
              const laborRevenueCategory = data.detail_summary[i].details;
              setLbrRevenueByCategory(laborRevenueCategory);
            } else if (
              data.detail_summary[i].group_title ==
              'Customer Labor Revenue By Components'
            ) {
              const laborRevenue = data.detail_summary[i].details;
              setlaborRevenueData(laborRevenue);
            } else if (
              data.detail_summary[i].group_title == 'Revenue By PayType'
            ) {
              const paytypeArr = data.detail_summary[i].details;
              const FiltersData = lodash.filter(paytypeArr, function(o) {
                return o.heading != 'revenue - no filters - labor and parts';
              });
              const sorted = lodash.sortBy(FiltersData, o => o.id);
              setSummaryData(sorted);
              const noFiltersData = lodash.filter(paytypeArr, function(o) {
                return o.heading == 'revenue - no filters - labor and parts';
              });
              setSummaryDataNoFilters(noFiltersData);
            } else if (
              data.detail_summary[i].group_title ==
              'Customer Parts Revenue By Category'
            ) {
              const prtsRevenueByCategory = data.detail_summary[i].details;
              setPartsRevenueByCategory(prtsRevenueByCategory);
            } else if (
              data.detail_summary[i].group_title ==
              'Customer Parts Revenue By Components'
            ) {
              const partsRevenue = data.detail_summary[i].details;
              setPartsRevenueData(partsRevenue);
            } else if (
              data.detail_summary[i].group_title ==
              'Job Level Break Down - Percentages'
            ) {
              const jobBrkDwnPerc = data.detail_summary[i].details;
              setJobLevelbrkDownPerc(jobBrkDwnPerc);
            } else if (
              data.detail_summary[i].group_title == 'Job Level Break Down'
            ) {
              const jobBrkDwn = data.detail_summary[i].details;
              setJobLevelbrkDown(jobBrkDwn);
            }
          }

          setIsLoading(false);
        }
      }
    );
    setIsLoading(false);
  }, [queryMonth, optQueryMonth, department, serviceAdvisor, session]);

  const windowWidth = window.innerWidth;
  const counter = 1;

  return (
    <div>
      <Paper
        square
        style={{ borderColor: '#000', marginLeft: '8px', marginRight: '8px' }}
      >
        <Tabs
          variant="fullWidth"
          indicatorColor="secondary"
          textColor="secondary"
          showrefresh
          aria-label="icon label tabs example"
          style={{ pointerEvents: 'none' }}
        >
          {/* {history.location.state && history.location.state.pageType ? (
            <Toolbar style={{ minHeight: 30 }}>
              <Link href="#" onClick={renderBackButton} title="Go Back">
                <ArrowBackIcon />
              </Link>
            </Toolbar>
          ) : (
            ''
          )} */}
          <Tab
            style={{
              textTransform: 'none',
              paddingRight: 182,
              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
            }}
            label={<div>Detail Summary</div>}
            value="one"
          />
        </Tabs>
      </Paper>

      <Paper square style={{ margin: 8, padding: 20 }}>
        <br />
        {isLoading == true ? (
          <LoaderSkeleton></LoaderSkeleton>
        ) : (
          <>
            <Grid xs="14" id="showTable" style={{ top: '194px' }}>
              <Table
                className="headPortion"
                // style={{ border: '1px solid #d8d8d8' }}
              >
                <TableHead>
                  <StyledTableRow>
                    <StyledTableCell
                      className="title"
                      colspan="5"
                      style={{
                        minWidth: '50px',
                        textAlign: 'center !important'
                      }}
                      id="currentMonth"
                    >
                      Current Month - {moment(queryMonth).format('MMM-YY')}
                    </StyledTableCell>
                    {/* <StyledTableCell style={{ minWidth: '50px' }}> */}
                    {/* <FormControl
                        variant="outlined"
                        margin="dense"
                        className={classes.formControl}

                        //  style={{marginTop:25}}
                      >
                        <Typography sx={{ mt: 2, mb: 1 }}>
                          Service Department{' '}
                        </Typography>
                        <Select
                          variant="outlined"
                          label="Group By"
                          name="group-by-type"
                          value="all"
                          style={{ width: '100%' }}
                          onChange={handleDeptChange}
                          value={department}
                          // className={classes.monthSelector}
                        >
                          <MenuItem value="all" selected>
                            All
                          </MenuItem>
                          <MenuItem value="service">Service</MenuItem>
                          <MenuItem value="bodyshop">Body Shop</MenuItem>
                        </Select>
                      </FormControl> */}
                    {/* </StyledTableCell> */}
                    <StyledTableCellSubHeader
                      className="title"
                      style={{ minWidth: '50px' }}
                    >
                      {componentTypes === 'Body Shop'
                        ? 'Body Shop Department'
                        : 'Service Department'}

                      <br></br>
                      <Grid
                        direction="row"
                        spacing={1}
                        style={{ display: 'inline-flex', marginTop: '2%' }}
                      >
                        {/* <Chip
                          label="All"
                          className={
                            classes.optBg +
                            ' ' +
                            classes.optTag +
                            ' ' +
                            (componentTypes == 'all'
                              ? classes.optBgSelected
                              : '')
                          }
                          onClick={() => handleDeptChange('all')}
                          style={{ marginRight: 5 }}
                          size="small"
                        /> */}
                        <Chip
                          label="Service"
                          style={{ marginRight: 5 }}
                          className={
                            classes.optBg +
                            ' ' +
                            classes.optTag +
                            ' ' +
                            (componentTypes == 'Service'
                              ? classes.optBgSelected
                              : '')
                          }
                          onClick={() => handleDeptChange('Service')}
                          size="small"
                          //variant="outlined"
                        />
                        <Chip
                          label="Body Shop"
                          style={{ marginRight: 5 }}
                          className={
                            classes.optBg +
                            ' ' +
                            classes.optTag +
                            ' ' +
                            (componentTypes == 'Body Shop'
                              ? classes.optBgSelected
                              : '')
                          }
                          size="small"
                          onClick={() => handleDeptChange('Body Shop')}
                          //  variant="outlined"
                        />
                      </Grid>
                    </StyledTableCellSubHeader>
                    <StyledTableCell
                      className="title"
                      colspan="5"
                      style={{
                        minWidth: '50px',
                        textAlign: 'center !important'
                      }}
                    >
                      <FormControl
                        variant="outlined"
                        margin="dense"
                        className={classes.formControl}
                        //  style={{marginTop:25}}
                      >
                        <DatePicker
                          autoOk
                          variant="inline"
                          inputVariant="outlined"
                          views={['year', 'month']}
                          label="User Selection"
                          minDate={new Date(endMonth + '-01')}
                          maxDate={new Date(queryMonth + '-01')}
                          //format="yyyy-MM"
                          //format="MM-yyyy"
                          format="MMM-YY"
                          value={optQueryMonth}
                          onChange={handleDateChange}
                          onMonthChange={handleDateAccept}
                        />
                        {/* <Select
                          variant="outlined"
                          label="Group By"
                          name="group-by-type"
                          value={monthYear.length > 0 ? monthYear[11] : ''}

                          // className={classes.monthSelector}
                        >
                          {monthYear.length > 0 &&
                            monthYear.map(month => (
                              <MenuItem value={month}>{month}</MenuItem>
                            ))}
                        </Select> */}
                      </FormControl>
                    </StyledTableCell>
                  </StyledTableRow>
                  <SummaryDataHeader type="all" />
                </TableHead>

                <TableBody>
                  {summaryDataNoFilters.length > 0 && (
                    <SummaryDataGrid
                      summaryData={summaryDataNoFilters}
                      isFirstRow={true}
                      type="all"
                      currMonth={queryMonth}
                      optMonth={optQueryMonth}
                    />
                  )}

                  <StyledTableRow>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                    <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                  </StyledTableRow>
                  <StyledTableRow>
                    <StyledTableCell colspan="5"></StyledTableCell>
                    <StyledTableCellSubHeader>
                      Revenue By Pay Type {/* <abbr title="Drill Down"> */}
                      <span>
                        <a
                          class="viewDetail"
                          id="laborparts"
                          style={{ cursor: 'pointer' }}
                          onClick={() => loadDrilldown('laborandparts')}
                        >
                          <Tooltip title="Drill Down">
                            <img
                              style={{
                                marginLeft: '5px',
                                width: '17px',
                                height: '17px',
                                float: 'right'
                              }}
                              src="./images/verifyimages/external.png"
                            />
                          </Tooltip>
                        </a>
                      </span>
                      {/* </abbr> */}
                    </StyledTableCellSubHeader>
                    <StyledTableCell colspan="5"></StyledTableCell>
                  </StyledTableRow>
                  {summaryData.length > 0 && (
                    <SummaryDataGrid
                      summaryData={summaryData}
                      type="all"
                      currMonth={queryMonth}
                      optMonth={optQueryMonth}
                      department={department}
                    />
                  )}
                </TableBody>
                <TableBody>
                  <StyledTableRow>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                    <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                  </StyledTableRow>
                  <SummaryDataHeader
                    type="laborRevenueByComponent"
                    changeRevenueComponentType={changeRevenueComponentType}
                    department={department}
                    componentType={componentType}
                    currMonth={queryMonth}
                  />

                  <RevenueByComponentGrid
                    currMonth={queryMonth}
                    componentType={componentType}
                    optMonth={optQueryMonth}
                    department={department}
                    laborRevenueData={laborRevenueData}
                    partsRevenueData={partsRevenueData}
                  />
                </TableBody>
                <TableBody>
                  <StyledTableRow>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                    <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                  </StyledTableRow>

                  <RevenueByCategoryGrid
                    currMonth={queryMonth}
                    componentType={componentType}
                    optMonth={optQueryMonth}
                    department={department}
                    partsRevenueByCategory={partsRevenueByCategory}
                    lbrRevenueByCategory={lbrRevenueByCategory}
                  />
                </TableBody>
                <TableBody>
                  <StyledTableRow>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                    <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
                    <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
                  </StyledTableRow>

                  <JobLevelBreakDownGrid
                    currMonth={queryMonth}
                    componentType={componentType}
                    optMonth={optQueryMonth}
                    department={department}
                    jobLevelbrkDown={jobLevelbrkDown}
                    jobLevelbrkDownPerc={jobLevelbrkDownPerc}
                  />
                </TableBody>
              </Table>
            </Grid>
          </>
        )}
      </Paper>
    </div>
  );
};

export default RevenueStatementGrid;
