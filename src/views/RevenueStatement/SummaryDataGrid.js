import React, { useEffect, useState } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import RevenueStatementGrid from './RevenueStatementGrid';
import { useDispatch } from 'react-redux';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button,
  TableBody
} from '@material-ui/core';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Table from '@material-ui/core/Table';
import Drawer from '@material-ui/core/Drawer';
import TableCell from '@material-ui/core/TableCell';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Tooltip from '@material-ui/core/Tooltip';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import KeyboardArrowDownIcon from '@material-ui/icons/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@material-ui/icons/KeyboardArrowUp';
import ReactHtmlParser from 'react-html-parser';

//import queryString from 'query-string';
import {
  formatCellValue,
  formatCellValueCount,
  formatCellValuePerc
} from 'src/utils/Utils';
import WarrantyVolumesGrid from './WarrantyVolumesGrid';

const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      //color: '#ccc !important'
    }
  }
}))(TableRow);
const StyledTableCellSubHeader = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: 'rgb(209, 209, 209)'
    //fontWeight: 'bold'
  }
}))(TableCell);
const StyledTableCellSubHeaderRevenue = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold'
  }
}))(TableCell);
const StyledTableCell = withStyles(theme => ({
  head: {
    color: '#100101 !important',
    border: '1px solid #ccc !important',
    lineHeight: '0.2rem',
    padding: 10
  },
  body: {
    fontSize: 12,
    textAlign: 'right',
    padding: 10,
    border: '1px solid #ccc !important'
  }
}))(TableCell);
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 300,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b',

    backgroundColor: '#ddeaf4'
  }
}))(Tooltip);
const SummaryDataGrid = props => {
  const { summaryData } = props;

  const [isExpanded, setIsExpanded] = useState(false);
  const [open, setOpen] = React.useState(false);
  const changeRevenueComponentType = params => {
    setIsExpanded(true);
  };
  const formatToUpperCase = str =>
    str.replace(/^(.)|\s+(.)/g, c => c.toUpperCase());

  const formatHeading = heading => {
    if (heading == 'extended warranty') {
      return '+++Extended';
    } else if (heading.startsWith('-')) {
      // return '- ' + heading.charAt(1).toUpperCase() + heading.slice(2);
      return '- ' + formatToUpperCase(heading.slice('1'));
    } else if (heading == 'Sale No Cost') {
      return 'Sale, No Cost';
    } else if (heading == 'Cost No Sale') {
      return 'Cost, No Sale';
    } else if (heading == 'Sale Equals Cost Core') {
      return 'Sale = Cost; Core Related';
    } else if (heading == 'Sale Equals Cost Not Core') {
      return 'Sale = Cost; Not Core Related';
    } else if (heading == 'sale costGT50 markup GT3') {
      return 'Sale & Cost; Mark-Up > 3.00 & Cost > $50';
    } else if (heading == 'sale cost markup LT1.03') {
      return 'Sale & Cost; Mark-Up < 1.03';
    } else if (heading == 'sale cost markup GT1.03') {
      return '<b>Charts: Sale & Cost; Mark-Up >= 1.03</b>';
    } else if (heading == 'extended warranty - labor') {
      return ' - labor';
    } else if (heading == 'extended warranty - parts') {
      return ' - parts';
    } else if (heading == 'revenue with cost and hours') {
      // return '<b>(A)</b> Revenue With Cost And Hours';
      return 'Revenue With Cost And Hours';
    } else if (heading == 'revenue with cost but no hours') {
      // return '<b>(B)</b> Revenue With Cost But No Hours';
      return 'Revenue With Cost But No Hours';
    } else if (heading == 'revenue no cost but hours') {
      // return '<b>(C)</b> Revenue No Cost But Hours';
      return 'Revenue No Cost But Hours';
    } else if (heading == 'revenue no cost no hours') {
      // return '<b>(D)</b> Revenue No Cost No Hours';
      return 'Revenue No Cost No Hours';
    } else if (heading == 'revenue - no filters - labor and parts') {
      return '<b>Revenue - No Filters (L & P)</b>';
    } else {
      //return heading.charAt(0).toUpperCase() + heading.slice(1);
      return formatToUpperCase(heading);
    }
  };

  return props.type == 'all' ? (
    <>
      {summaryData.map(data => (
        <>
          <StyledTableRow>
            <StyledTableCell id="currTotalJobCount">
              {formatCellValueCount(data.sel_jobcount)}
            </StyledTableCell>
            <StyledTableCell id="currTotalHours">
              {formatCellValueCount(data.sel_hours)}
            </StyledTableCell>
            <StyledTableCell id="currActualHours">
              {formatCellValueCount(data.actual_hours)}
            </StyledTableCell>
            <StyledTableCell id="currTotalCost">
              {formatCellValue(data.sel_cost)}
            </StyledTableCell>
            <StyledTableCell id="currTotalSales">
              {formatCellValue(data.sel_sale)}
            </StyledTableCell>
            <StyledTableCellSubHeader id="currTotalSales">
              <label> {ReactHtmlParser(formatHeading(data.heading))} </label>
              {props.isChartSubTotal ? (
                <div></div>
              ) : (
                // <HtmlTooltip
                //   arrow
                //   placement="top"
                //   title={
                //     <React.Fragment>
                //       <Typography color="inherit">
                //         {' '}
                //         <label>
                //           {data.heading.toLowerCase() ==
                //           'Customer Labor Revenue'.toLowerCase()
                //             ? 'A+B+C+D'
                //             : data.heading.trim().toLowerCase() ==
                //               'Effective Labor Rate Inputs'.toLowerCase()
                //             ? 'A+C'
                //             : data.heading.trim().toLowerCase() ==
                //               'Labor Gross Profit Inputs'.toLowerCase()
                //             ? 'A'
                //             : data.heading.toLowerCase() ==
                //               'Labor Gross Profit Per Hour Inputs'.toLowerCase()
                //             ? 'A'
                //             : ''}
                //         </label>
                //       </Typography>
                //     </React.Fragment>
                //   }
                // >
                //   <Button style={{ float: 'right' }}>
                //     <img
                //       style={{
                //         marginLeft: '5px',
                //         width: '20px',
                //         height: '20px'
                //       }}
                //       src="./images/verifyimages/info.png"
                //     />
                //   </Button>
                // </HtmlTooltip>
                props.department == 'Service' &&
                data.heading.toLowerCase() == 'warranty' && (
                  <Button
                    style={{ float: 'right', fontSize: 10 }}
                    onClick={() => setOpen(!open)}
                  >
                    Rates & Markups
                    <IconButton
                      aria-label="expand row"
                      size="small"
                      onClick={() => setOpen(!open)}
                    >
                      {open ? (
                        <KeyboardArrowUpIcon />
                      ) : (
                        <KeyboardArrowDownIcon />
                      )}
                    </IconButton>
                  </Button>
                )
              )}
            </StyledTableCellSubHeader>

            <StyledTableCell id="optTotalSales">
              {formatCellValue(data.user_sel_sale)}
            </StyledTableCell>
            <StyledTableCell id="optTotalCost">
              {formatCellValue(data.user_sel_cost)}
            </StyledTableCell>
            <StyledTableCell id="optActualHours">
              {formatCellValueCount(data.user_actual_hours)}
            </StyledTableCell>
            <StyledTableCell id="optTotalHours">
              {formatCellValueCount(data.user_sel_hours)}
            </StyledTableCell>
            <StyledTableCell id="optTotalJobCount">
              {formatCellValueCount(data.user_sel_jobcount)}
            </StyledTableCell>
          </StyledTableRow>
          {props.department == 'Service' &&
            data.heading.toLowerCase() == 'warranty' && (
              <TableRow>
                <TableCell
                  style={{ paddingBottom: 0, paddingTop: 0, padding: 0 }}
                  colSpan={12}
                >
                  <Collapse in={open} timeout="auto" unmountOnExit>
                    <Box sx={{ margin: 1 }}>
                      <WarrantyVolumesGrid
                        type="labor"
                        currMonth={props.currMonth}
                        optMonth={props.optMonth}
                      />
                      <WarrantyVolumesGrid
                        type="parts"
                        currMonth={props.currMonth}
                        optMonth={props.optMonth}
                      />
                    </Box>
                  </Collapse>
                </TableCell>
              </TableRow>
            )}
        </>
      ))}
    </>
  ) : props.type == 'laborRevenueByCategory' ? (
    summaryData.map(data => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours">
          {formatCellValueCount(data.elr_rev)}
        </StyledTableCell>
        <StyledTableCell id="currTotalCost">
          {formatCellValuePerc('-' + props.type, data.gp_per_rev)}
        </StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValue(data.labor_rev)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValue(data.user_sel_labor_rev)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost">
          {formatCellValuePerc('-' + props.type, data.user_sel_gp_per_rev)}
        </StyledTableCell>
        <StyledTableCell id="optActualHours">
          {formatCellValueCount(data.user_sel_elr_rev)}
        </StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount"></StyledTableCell>
      </StyledTableRow>
    ))
  ) : props.type == 'partsRevenueByCategory' ? (
    summaryData.map(data => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours"></StyledTableCell>
        <StyledTableCell id="currTotalCost">
          {formatCellValue(data.sel_cost)}
        </StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValue(data.sel_sale)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValue(data.user_sel_sale)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost">
          {formatCellValue(data.user_sel_cost)}
        </StyledTableCell>
        <StyledTableCell id="optActualHours"></StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount"></StyledTableCell>
      </StyledTableRow>
    ))
  ) : props.type == 'jobLevelBrkDownPerc' ? (
    summaryData.map(data => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours"></StyledTableCell>
        <StyledTableCell id="currTotalCost"></StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValuePerc(data.heading, data.sel_sale)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValuePerc(data.heading, data.user_sel_sale)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost"></StyledTableCell>
        <StyledTableCell id="optActualHours"></StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount"></StyledTableCell>
      </StyledTableRow>
    ))
  ) : (
    props.type == 'jobLevelBrkDown' &&
    summaryData.map(data => (
      <StyledTableRow>
        <StyledTableCell id="currTotalJobCount"></StyledTableCell>
        <StyledTableCell id="currTotalHours"></StyledTableCell>
        <StyledTableCell id="currActualHours">
          {data.sel_jobcount}
        </StyledTableCell>
        <StyledTableCell id="currTotalCost">
          {formatCellValue(data.sel_cost)}
        </StyledTableCell>
        <StyledTableCell id="currTotalSales">
          {formatCellValue(data.sel_sale)}
        </StyledTableCell>
        <StyledTableCellSubHeader id="currTotalSales">
          {formatHeading(data.heading)}
        </StyledTableCellSubHeader>
        <StyledTableCell id="optTotalSales">
          {formatCellValue(data.user_sel_sale)}
        </StyledTableCell>
        <StyledTableCell id="optTotalCost">
          {formatCellValue(data.user_sel_cost)}
        </StyledTableCell>
        <StyledTableCell id="optActualHours">
          {data.user_sel_jobcount}
        </StyledTableCell>
        <StyledTableCell id="optTotalHours"></StyledTableCell>
        <StyledTableCell id="optTotalJobCount" />
      </StyledTableRow>
    ))
  );
};

export default SummaryDataGrid;
