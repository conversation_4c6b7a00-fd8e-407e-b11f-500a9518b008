import React, { useEffect, useState } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import RevenueStatementGrid from './RevenueStatementGrid';
import { useDispatch } from 'react-redux';
import { Grid, Tooltip } from '@material-ui/core';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Table from '@material-ui/core/Table';
import Drawer from '@material-ui/core/Drawer';
import TableCell from '@material-ui/core/TableCell';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Chip from '@material-ui/core/Chip';
import { useHistory } from 'react-router';

//import queryString from 'query-string';
import { formatCellValue, formatCellValueCount } from 'src/utils/Utils';
var Dealer = process.env.REACT_APP_DEALER;

const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      //color: '#ccc !important'
    }
  }
}))(TableRow);
const StyledTableCellSubHeader = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold'
  }
}))(TableCell);
const StyledTableCellHeader = withStyles(theme => ({
  head: {
    color: '#fff !important',
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor:
      Dealer == 'Armatus' ? '#003d6b !important' : '#c2185b !important',
    padding: 10,
    fontSize: 12
  },
  body: {
    fontSize: 12,
    fontWeight: 'bold',
    padding: 10,
    color: '#fff !important',
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor:
      Dealer == 'Armatus' ? '#003d6b !important' : '#c2185b !important'
  }
}))(TableCell);
const useStyles = makeStyles(theme => ({
  optBgSelected: {
    background: Dealer == 'Armatus' ? '#003d6b' : '#c2185b',
    textAlign: 'center',
    border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
    borderRadius: '10px',
    margin: '2px'
  },

  optTagSelected: {
    textDecoration: 'none',
    color: '#ffffff'
  },

  optBg: {
    margin: '2px',
    textAlign: 'center',
    border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
    borderRadius: '10px'
  },

  optTag: {
    textDecoration: 'none',
    color: '#000',
    '&:hover, &:focus': {
      background: Dealer == 'Armatus' ? '#003d6b' : '#c2185b',
      border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
      borderRadius: '10px',
      cursor: 'pointer',
      color: '#fff'
    }
  },

  optBgSelected: {
    '&:hover, &:focus': {
      background: '#ffffff',
      border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
      borderRadius: '10px',
      cursor: 'pointer',
      color: '#fff'
    },

    backgroundColor: Dealer == 'Armatus' ? '#003d6b' : '#c2185b',
    textAlign: 'center',
    border: Dealer == 'Armatus' ? '1px solid #003d6b' : '1px solid #c2185b',
    borderRadius: '10px',
    margin: '2px',
    color: '#fff !important'
  },

  optTagSelected: {
    color: '#646464',
    textDecoration: 'none',
    '&:hover, &:focus': {
      color: '#646464',
      textDecoration: 'none'
    }
  }
}));
const SummaryDataHeader = props => {
  const classes = useStyles();
  const history = useHistory();
  const [componentType, setComponentType] = useState(props.componentType);
  const changeComponentType = params => {
    setComponentType(params);
    props.changeRevenueComponentType(params);
  };
  const loadDrilldown = params => {
    localStorage.setItem('scrollpos', window.scrollY);

    history.push({
      pathname: '/RevenueSummaryDrilldown',
      state: {
        // queryMonth: localStorage.getItem('queryMonth'),
        queryMonth: props.currMonth,
        drillDownType: params,
        department: props && props.department,
        componentType: componentType
        //showCurrentMonth: showCurrentMonth
      }
    });
  };
  return props.type == 'all' ||
    props.type == 'laborRevenueByComponent' ||
    props.type == 'PartsRevenueByComponent' ? (
    <StyledTableRow className="headerTop">
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        RO Count
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Sold Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Actual Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Sales
      </StyledTableCellHeader>
      {props.type == 'all' ? (
        <StyledTableCellHeader
          className="title"
          style={{ minWidth: '50px' }}
        ></StyledTableCellHeader>
      ) : props.type == 'laborRevenueByComponent' ? (
        <StyledTableCellSubHeader
          className="title"
          style={{ minWidth: '50px' }}
        >
          Customer Labor Revenue By Components
          {/* <abbr title="Drill Down"> */}
          <span>
            <a
              class="viewDetail"
              id="laborparts"
              style={{ cursor: 'pointer' }}
              onClick={() => loadDrilldown('labor')}
            >
              <Tooltip title="Drill Down">
                <img
                  style={{
                    marginLeft: '5px',
                    width: '17px',
                    height: '17px',
                    float: 'right'
                  }}
                  src="./images/verifyimages/external.png"
                />
              </Tooltip>
            </a>
          </span>
          {/* </abbr> */}
          <Grid direction="row" spacing={1} style={{ display: 'inline-flex' }}>
            <Chip
              label="All"
              className={
                classes.optBg +
                ' ' +
                classes.optTag +
                ' ' +
                (componentType == 'all' ? classes.optBgSelected : '')
              }
              onClick={() => changeComponentType('all')}
              style={{ marginRight: 5 }}
              size="small"
            />
            <Chip
              label="With Parts"
              style={{ marginRight: 5 }}
              className={
                classes.optBg +
                ' ' +
                classes.optTag +
                ' ' +
                (componentType == 'labor with parts'
                  ? classes.optBgSelected
                  : '')
              }
              onClick={() => changeComponentType('labor with parts')}
              size="small"
              //variant="outlined"
            />
            <Chip
              label="Without Parts"
              style={{ marginRight: 5 }}
              className={
                classes.optBg +
                ' ' +
                classes.optTag +
                ' ' +
                (componentType == 'labor without parts'
                  ? classes.optBgSelected
                  : '')
              }
              size="small"
              onClick={() => changeComponentType('labor without parts')}
              //  variant="outlined"
            />
          </Grid>
        </StyledTableCellSubHeader>
      ) : (
        <StyledTableCellSubHeader
          className="title"
          style={{ minWidth: '50px' }}
        >
          Customer Parts Revenue By Components
          {/* <abbr title="Drill Down"> */}
          <span>
            <a
              class="viewDetail"
              id="laborparts"
              style={{ cursor: 'pointer' }}
              onClick={() => loadDrilldown('parts')}
            >
              <Tooltip title="Drill Down">
                <img
                  style={{
                    marginLeft: '5px',
                    width: '17px',
                    height: '17px',
                    float: 'right'
                  }}
                  src="./images/verifyimages/external.png"
                />
              </Tooltip>
            </a>
          </span>
          {/* </abbr> */}
        </StyledTableCellSubHeader>
      )}

      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Sales
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Actual Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Sold Hours
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        RO Count
      </StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == 'laborRevenueByCategory' ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        ELR Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        GP% Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Labor Rev.
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: '50px' }}>
        {' '}
        Customer Labor Revenue By Category
        {/* <abbr title="Drill Down"> */}
        <span>
          <a
            class="viewDetail"
            id="laborparts"
            style={{ cursor: 'pointer' }}
            onClick={() => loadDrilldown('labor')}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: '5px',
                  width: '17px',
                  height: '17px',
                  float: 'right'
                }}
                src="./images/verifyimages/external.png"
              />
            </Tooltip>
          </a>
        </span>
        {/* </abbr> */}
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Labor Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        GP% Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        ELR Rev.
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == 'partsRevenueByCategory' ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Sale
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: '50px' }}>
        {' '}
        Customer Parts Revenue By Category
        {/* <abbr title="Drill Down"> */}
        <span>
          <a
            class="viewDetail"
            id="laborparts"
            style={{ cursor: 'pointer' }}
            onClick={() => loadDrilldown('parts')}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: '5px',
                  width: '17px',
                  height: '17px',
                  float: 'right'
                }}
                src="./images/verifyimages/external.png"
              />
            </Tooltip>
          </a>
        </span>
        {/* </abbr> */}
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Sale
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Cost
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == 'jobLevelBrkDown' ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        RO Count
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Parts
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: '50px' }}>
        {' '}
        Job Level Break Down
        {/* <abbr title="Drill Down"> */}
        <span>
          <a
            class="viewDetail"
            id="laborparts"
            style={{ cursor: 'pointer' }}
            onClick={() => loadDrilldown('labor')}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: '5px',
                  width: '17px',
                  height: '17px',
                  float: 'right'
                }}
                src="./images/verifyimages/external.png"
              />
            </Tooltip>
          </a>
        </span>
        {/* </abbr> */}
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Parts
      </StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        RO Count
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : props.type == 'jobLevelBrkDownPerc' ? (
    <StyledTableRow>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellSubHeader className="title" style={{ minWidth: '50px' }}>
        {' '}
        Job Level Break Down
        {/* <abbr title="Drill Down"> */}
        <span>
          <a
            class="viewDetail"
            id="laborparts"
            style={{ cursor: 'pointer' }}
            onClick={() => loadDrilldown('labor')}
          >
            <Tooltip title="Drill Down">
              <img
                style={{
                  marginLeft: '5px',
                  width: '17px',
                  height: '17px',
                  float: 'right'
                }}
                src="./images/verifyimages/external.png"
              />
            </Tooltip>
          </a>
        </span>
        {/* </abbr> */}
      </StyledTableCellSubHeader>

      <StyledTableCellHeader className="title" style={{ minWidth: '50px' }}>
        Labor
      </StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
      <StyledTableCellHeader
        className="title"
        style={{ minWidth: '50px' }}
      ></StyledTableCellHeader>
    </StyledTableRow>
  ) : (
    ''
  );
};

export default SummaryDataHeader;
