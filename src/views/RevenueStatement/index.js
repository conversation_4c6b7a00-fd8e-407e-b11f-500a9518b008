import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import RevenueStatementGrid from './RevenueStatementGrid';
import { getMonthYears } from 'src/utils/hasuraServices';
import { useDispatch, useSelector } from 'react-redux';
//import queryString from 'query-string';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { setNavItems, setMenuSelected } from 'src/actions';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

const RevenueStatement = props => {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const dispatch = useDispatch();
  const [queryMonth, setQueryMonth] = useState('');
  const [optQueryMonth, setOptQueryMonth] = useState('');
  const [endQueryMonth, setEndQueryMonth] = useState('');
  let params = '';
  let department =
    props &&
    props.history &&
    props.history.location &&
    props.history.location.department;
  let componentType =
    props &&
    props.history &&
    props.history.location &&
    props.history.location.componentType;
  if (
    typeof props.history.location.state != 'undefined' &&
    props.history.location.state != null
  ) {
    params = props.history.location.state.ronumber;
  }
  useEffect(() => {
    getMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        //setIsLoading(false);

        const monthYear = result.data.statelessCcDrilldownGetMonthYears.nodes;
        setQueryMonth(monthYear[0].monthYear);
        setOptQueryMonth(monthYear[1].monthYear);
        setEndQueryMonth(monthYear[monthYear.length - 1].monthYear);
        localStorage.setItem('queryMonth', monthYear[1].monthYear);
      }
    });
  }, [session]);
  useEffect(() => {
    dispatch(setMenuSelected('Detail Summary'));
    dispatch(setNavItems(['Armatus Admin']));
  }, []);
  if (queryMonth != '' && optQueryMonth != '' && endQueryMonth != '') {
    return (
      <Page className={classes.root} title="Detail Summary">
        {props.keycloak.realmAccess.roles.includes('client') ||
        props.keycloak.realmAccess.roles.includes('user') ||
        JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <RevenueStatementGrid
            queryMonth={queryMonth}
            optQueryMonthYear={optQueryMonth}
            endMonth={endQueryMonth}
            params={params.trim() == '' ? null : params}
            session={session}
            componentType={componentType ? componentType : 'all'}
            department={department ? department : 'Service'}
          />
        )}
      </Page>
    );
  } else {
    return <LoaderSkeleton></LoaderSkeleton>;
  }
};

export default withKeycloak(RevenueStatement);
