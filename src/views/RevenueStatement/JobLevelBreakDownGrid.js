import React, { useEffect, useState } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import RevenueStatementGrid from './RevenueStatementGrid';
import { useDispatch } from 'react-redux';

import TableCell from '@material-ui/core/TableCell';

import TableRow from '@material-ui/core/TableRow';
//import queryString from 'query-string';
import { formatCellValue, formatCellValueCount } from 'src/utils/Utils';
import SummaryDataGrid from './SummaryDataGrid';
import SummaryDataHeader from './SummaryDataHeader';
var lodash = require('lodash');
const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      //color: '#ccc !important'
    },
    padding: 10
  }
}))(TableRow);
const StyledTableCellHeader = withStyles(theme => ({
  head: {
    color: '#fff !important',
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: '#c2185b !important'
  },
  body: {
    fontSize: 14
  }
}))(TableCell);
const StyledTableCellSubHeader = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold'
  }
}))(TableCell);
const StyledTableCell = withStyles(theme => ({
  head: {
    color: '#100101 !important',
    border: '1px solid #ccc !important',
    lineHeight: '0.2rem',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: '1px solid #ccc !important'
  }
}))(TableCell);
const JobLevelBreakDownGrid = props => {
  const [summaryDataLabor, setSummaryDataLabor] = useState([]);
  const [summaryDataParts, setSummaryDataParts] = useState([]);
  useEffect(() => {
    const roDatabrkDown =
      props && props.jobLevelbrkDown && props.jobLevelbrkDown;
    const roDatabrkDownPerc =
      props && props.jobLevelbrkDownPerc && props.jobLevelbrkDownPerc;
    if (roDatabrkDown !== undefined) {
      const sorted = lodash.sortBy(roDatabrkDown, o => o.id);

      setSummaryDataLabor(sorted);
    }
    if (roDatabrkDownPerc !== undefined) {
      const sorted = lodash.sortBy(roDatabrkDownPerc, o => Number(o.id));
      console.log('sorted', sorted);
      setSummaryDataParts(sorted);
    }
    //setIsLoading(false);
  }, [
    props.optMonth,
    props.department,
    props.jobLevelbrkDown,
    props.jobLevelbrkDownPerc
  ]);
  return (
    <>
      <SummaryDataHeader
        type="jobLevelBrkDown"
        department={props.department}
        componentType={props.componentType}
        currMonth={props.currMonth}
      />
      {summaryDataLabor.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataLabor}
          isChartSubTotal={false}
          type="jobLevelBrkDown"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
      </StyledTableRow>
      <SummaryDataHeader
        type="jobLevelBrkDownPerc"
        department={props.department}
        componentType={props.componentType}
        currMonth={props.currMonth}
      />
      {summaryDataParts.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataParts}
          isChartSubTotal={false}
          type="jobLevelBrkDownPerc"
        />
      )}
    </>
  );
};

export default JobLevelBreakDownGrid;
