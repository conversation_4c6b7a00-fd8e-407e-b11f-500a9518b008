import React, { useEffect, useState } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import RevenueStatementGrid from './RevenueStatementGrid';
import { useDispatch } from 'react-redux';

import TableCell from '@material-ui/core/TableCell';

import TableRow from '@material-ui/core/TableRow';
//import queryString from 'query-string';
import { formatCellValue, formatCellValueCount } from 'src/utils/Utils';
import SummaryDataGrid from './SummaryDataGrid';
import SummaryDataHeader from './SummaryDataHeader';
import { i } from 'react-dom-factories';
var lodash = require('lodash');
const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      //color: '#ccc !important'
    }
  }
}))(TableRow);
const StyledTableCellSubHeader = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold'
  }
}))(TableCell);
const StyledTableCell = withStyles(theme => ({
  head: {
    color: '#100101 !important',
    border: '1px solid #ccc !important',
    lineHeight: '0.2rem',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: '1px solid #ccc !important'
  }
}))(TableCell);

const RevenueByComponentGrid = props => {
  const [summaryDataAll, setSummaryDataAll] = useState([]);
  const [summaryData, setSummaryData] = useState([]);
  const [chartedSubTotalData, setChartedSubTotalData] = useState([]);
  const [summaryDataParts, setSummaryDataParts] = useState([]);
  useEffect(() => {
    const roData = props && props.laborRevenueData && props.laborRevenueData;
    if (roData !== undefined) {
      const noFiltersData = lodash.filter(roData, function(o) {
        return (
          o.filterby == props.componentType &&
          o.heading.toLowerCase() != 'Customer Labor Revenue'.toLowerCase() &&
          o.heading.trim().toLowerCase() !=
            'Effective Labor Rate Inputs'.toLowerCase() &&
          o.heading.trim().toLowerCase() !=
            'Labor Gross Profit Inputs'.toLowerCase() &&
          o.heading.toLowerCase() !=
            'Labor Gross Profit Per Hour Inputs'.toLowerCase()
        );
      });
      const subTotalsData = lodash.filter(roData, function(o) {
        return (
          o.filterby == props.componentType &&
          (o.heading.toLowerCase() == 'Customer Labor Revenue'.toLowerCase() ||
            o.heading.trim().toLowerCase() ==
              'Effective Labor Rate Inputs'.toLowerCase() ||
            o.heading.trim().toLowerCase() ==
              'Labor Gross Profit Inputs'.toLowerCase() ||
            o.heading.toLowerCase() ==
              'Labor Gross Profit Per Hour Inputs'.toLowerCase())
        );
      });
      setChartedSubTotalData(subTotalsData);
      setSummaryData(noFiltersData);
    }
  }, [
    props.componentType,
    props.optMonth,
    props.department,
    props.laborRevenueData
  ]);
  useEffect(() => {
    const roDataParts =
      props && props.partsRevenueData && props.partsRevenueData;
    if (roDataParts !== undefined) {
      setSummaryDataParts(roDataParts);
    }

    //setIsLoading(false);
  }, [props.optMonth, props.department, props.partsRevenueData]);

  return (
    <>
      {summaryData.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryData}
          isChartSubTotal={false}
          type="all"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>Charted Sub-Totals</StyledTableCellSubHeader>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
      </StyledTableRow>
      {chartedSubTotalData.length > 0 && (
        <SummaryDataGrid
          summaryData={chartedSubTotalData}
          isChartSubTotal={true}
          type="all"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
      </StyledTableRow>
      <SummaryDataHeader
        type="PartsRevenueByComponent"
        department={props.department}
        componentType={props.componentType}
        currMonth={props.currMonth}
        //changeRevenueComponentType={changeRevenueComponentType}
      />
      {summaryDataParts.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataParts}
          isChartSubTotal={false}
          type="all"
        />
      )}
    </>
  );
};

export default RevenueByComponentGrid;
