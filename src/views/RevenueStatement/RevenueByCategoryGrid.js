import React, { useEffect, useState } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import RevenueStatementGrid from './RevenueStatementGrid';
import { useDispatch } from 'react-redux';

import TableCell from '@material-ui/core/TableCell';

import TableRow from '@material-ui/core/TableRow';
//import queryString from 'query-string';
import { formatCellValue, formatCellValueCount } from 'src/utils/Utils';
import SummaryDataGrid from './SummaryDataGrid';
import SummaryDataHeader from './SummaryDataHeader';
var lodash = require('lodash');
const StyledTableRow = withStyles(theme => ({
  root: {
    '&:nth-of-type(odd)': {
      //color: '#ccc !important'
    },
    padding: 10
  }
}))(TableRow);
const StyledTableCellHeader = withStyles(theme => ({
  head: {
    color: '#fff !important',
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: '#c2185b !important'
  },
  body: {
    fontSize: 14
  }
}))(TableCell);
const StyledTableCellSubHeader = withStyles(theme => ({
  head: {
    border: '1px solid #100101 !important',
    lineHeight: '1.0rem',
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    backgroundColor: 'rgb(209, 209, 209)',
    fontWeight: 'bold'
  }
}))(TableCell);
const StyledTableCell = withStyles(theme => ({
  head: {
    color: '#100101 !important',
    border: '1px solid #ccc !important',
    lineHeight: '0.2rem',
    padding: 10
  },
  body: {
    fontSize: 12,
    padding: 10,
    border: '1px solid #ccc !important'
  }
}))(TableCell);
const RevenueByCategoryGrid = props => {
  const [summaryDataLabor, setSummaryDataLabor] = useState([]);
  const [summaryDataParts, setSummaryDataParts] = useState([]);
  useEffect(() => {
    const roData =
      props && props.lbrRevenueByCategory && props.lbrRevenueByCategory;
    const roDataParts =
      props && props.partsRevenueByCategory && props.partsRevenueByCategory;
    if (roData !== undefined) {
      const sorted = lodash.sortBy(roData, o => o.heading);
      sorted.unshift({
        id: '720',
        elrRev: '  ',
        gpPercLbrRev: '  ',
        heading: 'Repair',
        lbrRevenue: '  ',
        optElrRev: '  ',
        optGpPercLbrRev: '  ',
        optLbrRevenue: '  ',
        storeId: '217486972'
      });
      setSummaryDataLabor(sorted);
    }
    if (roDataParts !== undefined) {
      const sorted = lodash.sortBy(roDataParts, o => o.heading);
      sorted.unshift({
        selCost: '  ',
        selSale: '  ',
        optSelSale: '  ',
        optSelCost: '  ',
        heading: 'Repair',
        id: '700'
      });
      setSummaryDataParts(sorted);
    }
  }, [
    props.optMonth,
    props.department,
    props.lbrRevenueByCategory,
    props.partsRevenueByCategory
  ]);
  return (
    <>
      <SummaryDataHeader
        type="laborRevenueByCategory"
        department={props.department}
        componentType={props.componentType}
        currMonth={props.currMonth}
      />
      {summaryDataLabor.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataLabor}
          isChartSubTotal={false}
          type="laborRevenueByCategory"
        />
      )}
      <StyledTableRow>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
        <StyledTableCellSubHeader>&nbsp;</StyledTableCellSubHeader>
        <StyledTableCell colspan="5">&nbsp;</StyledTableCell>
      </StyledTableRow>
      <SummaryDataHeader
        type="partsRevenueByCategory"
        department={props.department}
        componentType={props.componentType}
        currMonth={props.currMonth}
      />
      {summaryDataParts.length > 0 && (
        <SummaryDataGrid
          summaryData={summaryDataParts}
          isChartSubTotal={false}
          type="partsRevenueByCategory"
        />
      )}
    </>
  );
};

export default RevenueByCategoryGrid;
