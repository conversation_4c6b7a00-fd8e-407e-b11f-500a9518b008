import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Tooltip,
  Button
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import moment from 'moment';
import React from 'react';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import ReactHtmlParser from 'react-html-parser';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Link from '@material-ui/core/Link';
import {
  getDrillDownDataForLaborDiscount,
  getDrillDownDataForPartsDiscount,
  getDrillDownDataForWarrantyRatesLabor,
  getDrillDownMonthYears,
  getDrillDownDataForTotalRevenueDateRange,
  getDrillDownDataForRevenueSummary
} from 'src/utils/hasuraServices';
import {
  distinctROCount,
  myCustomSumAggregate
} from 'src/components/DrillDownCalculations';
import { renderBackButtonForDrillDown } from 'src/components/DrillDownCalculations';
import { DateRangePicker } from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import { withStyles, styled } from '@material-ui/styles';
import $ from 'jquery';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getLast13Months, getTimeZone } from 'src/utils/Utils';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { getVerificationDashboardBaseURL, getYearValue } from 'src/utils/Utils';
import CheckboxRenderer from 'src/components/CheckboxRenderer';
import clsx from 'clsx';
import { ReactSession } from 'react-client-session';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
import { store } from '../../App';
import { isValidDate } from 'src/components/ViewGraphDetailsAction';
var lodash = require('lodash');
const Item = styled(Paper)(({ theme }) => ({
  ...theme.typography.body2,
  padding: theme.spacing(1),
  textAlign: 'center',
  color: theme.palette.text.secondary
}));
class RevenueSummaryDrilldown extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (ReactSession.get('serviceAdvisors') != undefined) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisor,
        ReactSession.get('serviceAdvisors')
      );
      if (checkStatus == false) {
        this.getAgGridData(
          this.state.queryMonth,
          ReactSession.get('serviceAdvisors'),
          this.props.type
        );
        this.setState({
          serviceAdvisor: ReactSession.get('serviceAdvisors')
        });
      }
    }

    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      console.log(
        'selected=',
        ReactSession.get('selectedStoreId'),
        checkSt,
        localStorage.getItem('selectedStoreId'),
        this.state.store
      );
      if (checkSt == false) {
        // this.getAgGridData(
        //   this.state.queryMonth,
        //   this.state.serviceAdvisor,
        //   this.props.type
        // );
        this.getAgGridData();
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth =
      typeof this.props.location.state != 'undefined' &&
      typeof this.props.location.state.queryMonth != 'undefined'
        ? this.props.location.state.queryMonth
        : localStorage.getItem('queryMonth')
        ? localStorage.getItem('queryMonth')
        : '2021-06';
    const dateData1 = store.getState().session;
    var location =
      getVerificationDashboardBaseURL() + '/FOC3_Searchbyro/ag-grid.html';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    var department =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.department
        ? this.props &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.department
        : 'Service';
    var payType =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.payType;
    var componentType =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.componentType;
    this.state = {
      queryMonth: initialQueryMonth,
      selectedMonthYear: initialQueryMonth,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      // serviceAdvisor: ['All'],
      serviceAdvisor:
        this.props &&
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.serviceAdvisor
          ? this.props.history.location.state.serviceAdvisor
          : ['All'],

      isLoading: true,
      isRefresh: false,
      rawGridApi: {},
      monthYearArr: [],
      selectedDate: [],
      gridApi: {},
      tabSelection: 0,
      headerHeight: 45,
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      filterDisabled: true,
      department: department,
      componentType: componentType,
      payType: payType,
      selectedMonth: moment(localStorage.getItem('closedDate')).startOf(
        'month'
      ),
      closedDate: localStorage.getItem('closedDate'),
      // selectedDateRange: {
      //   startDate: moment(localStorage.getItem('closedDate')).startOf('month'),
      //   endDate: moment(localStorage.getItem('closedDate'))
      // },
      startDate:
        localStorage.getItem('kpiDataStatus') == 0
          ? dateData1 &&
            dateData1.dateData[0] &&
            dateData1.dateData[0].lastmonthstartdate
          : dateData1.dateData[0] &&
            dateData1.dateData[0].mtdstartdate &&
            dateData1.dateData[0].mtdstartdate,
      endDate:
        localStorage.getItem('kpiDataStatus') == 0
          ? dateData1 &&
            dateData1.dateData[0] &&
            dateData1.dateData[0].lastmonthenddate
          : dateData1.dateData[0] &&
            dateData1.dateData[0].mtdenddate &&
            dateData1.dateData[0].mtdenddate,

      drillDownType:
        typeof this.props.location.state != 'undefined' &&
        typeof this.props.location.state.drillDownType != 'undefined'
          ? this.props.location.state.drillDownType
          : 'laborandparts',
      //drillDownType: 'laborandparts',
      monthYear: this.props.months,
      // startDate: '',
      // endDate: '',
      // modules: [
      //   ClientSideRowModelModule,
      //   RowGroupingModule,
      //   MenuModule,
      //   ColumnsToolPanelModule
      // ],
      isSumChanged: false,
      isSRowDataChanged: false,
      selectedRowData: [],
      isChecked: false,
      // department: 'Service',
      columnDefs: [
        {
          headerName: 'RO',
          filter: 'agSetColumnFilter',

          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'textAlign',
          field: 'ronumber',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          // cellRenderer: 'agGroupCellRenderer',
          unSortIcon: true,
          suppressMenu: true,
          // hide :drillDown == 33 ? true : false,
          onCellClicked: this.handleSearchByRo,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              //left: '-28px',
              cursor: 'pointer'
            };
          }
        },
        // {
        //   headerName: 'Exclude',
        //   //field: 'lbropcode',
        //   // unSortIcon: true,

        //   sortable: false,
        //   suppressMenu: true,
        //   width: 90,
        //   chartDataType: 'category',
        //   cellRenderer: 'checkboxRenderer',
        //   cellStyle() {
        //     return { textAlign: 'center' };
        //   }
        // },
        {
          headerName: 'Month',
          field: 'monthYear',
          chartDataType: 'category',
          calculate: false,

          hide: false,
          numeric: false,
          width: 85,
          minWidth: 85,
          flex: 1,
          unSortIcon: true,
          suppressMenu: true,
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          }
        },
        {
          headerName: 'Open Date',
          field: 'opendate',
          chartDataType: 'series',
          calculate: false,

          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 88,
          minWidth: 88,
          flex: 1
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          chartDataType: 'series',
          calculate: false,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 88,
          minWidth: 88,
          flex: 1
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,

          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // },

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,

          width: 78,
          minWidth: 78,
          flex: 1
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,

          width: 80,
          minWidth: 80,
          flex: 1
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 90,
          minWidth: 90,

          flex: 1
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 90,
          minWidth: 90,

          flex: 1
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          filter: 'agSetColumnFilter',

          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          calculate: false,
          hide: true,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 150,
          minWidth: 150,
          flex: 1
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,

          width: 110,
          minWidth: 110,
          flex: 1
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 55,
        //   sortable: false,
        //   // filter: false,
        //   suppressMenu: true,

        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          calculate: false,
          hide: true,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 68,

          minWidth: 68,
          flex: 1
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          calculate: true,
          hide: false,
          numeric: true,
          width: 83,
          minWidth: 83,
          flex: 1,
          unSortIcon: true,
          suppressMenu: true,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          calculate: true,
          hide: false,
          numeric: true,
          width: 80,
          minWidth: 80,
          flex: 1,
          unSortIcon: true,
          suppressMenu: true,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          calculate: true,
          hide: false,
          numeric: true,
          width: 78,
          minWidth: 78,
          flex: 1,
          unSortIcon: true,
          suppressMenu: true,
          valueFormatter: this.formatCellValueWithOut$,
          filterParams: {
            valueFormatter: this.formatCellValueWithOut$
          },
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          calculate: true,
          hide: false,
          numeric: true,
          unSortIcon: true,
          suppressMenu: true,
          width: 80,
          minWidth: 80,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          calculate: true,
          hide: false,
          numeric: true,
          unSortIcon: true,
          suppressMenu: true,
          width: 80,
          minWidth: 80,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },

        {
          headerName: 'Labor GP',
          field: 'lbrgrossprofit',
          calculate: false,
          hide: false,
          numeric: true,
          unSortIcon: true,
          suppressMenu: true,
          width: 78,
          minWidth: 78,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor GP %',
          field: 'lbrGrossprofitpercentage',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 78,
          minWidth: 78,
          flex: 1,

          valueFormatter: this.formatCellValueGP,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 75,
          minWidth: 75,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts GP %',
          field: 'prtsGrossprofitpercentage',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 75,
          minWidth: 75,
          flex: 1,

          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP
        },
        {
          headerName: 'ELR',
          field: 'elr',
          calculate: false,
          hide: false,
          numeric: false,
          width: 75,
          minWidth: 75,
          flex: 1,
          unSortIcon: true,
          suppressMenu: true,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Markup',
          field: 'markup',
          calculate: false,
          hide: false,
          numeric: true,
          unSortIcon: true,
          suppressMenu: true,
          width: 88,
          minWidth: 88,
          flex: 1,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            valueFormatter: this.formatCellValueMarkup
          },
          cellStyle: this.cellStyles
        }
      ],
      columnDefsLabor: [
        {
          headerName: 'RO',
          // filter: 'agTextColumnFilter',
          field: 'ronumber',
          width: 90,
          minWidth: 90,
          flex: 1,

          chartDataType: 'category',
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          // cellRenderer: 'agGroupCellRenderer',
          unSortIcon: true,
          suppressMenu: true,
          // hide: drillDown == 33 ? true : false,
          onCellClicked: this.handleSearchByRo,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              //left: '-28px',
              cursor: 'pointer'
            };
          }
        },
        // {
        //   headerName: 'Exclude',
        //   //field: 'lbropcode',
        //   // unSortIcon: true,

        //   sortable: false,
        //   suppressMenu: true,
        //   width: 90,
        //   chartDataType: 'category',
        //   cellRenderer: 'checkboxRenderer',
        //   cellStyle() {
        //     return { textAlign: 'center' };
        //   }
        // },
        {
          headerName: 'Month',
          field: 'monthYear',
          calculate: false,
          hide: false,
          numeric: false,
          width: 85,
          minWidth: 85,
          flex: 1,
          unSortIcon: true,
          suppressMenu: true,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          valueFormatter: this.formatCellValueMonthYear
        },
        {
          headerName: 'Open Date',
          field: 'opendate',
          chartDataType: 'series',
          calculate: false,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 88,
          minWidth: 88,

          flex: 1
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          calculate: false,
          hide: false,
          numeric: false,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          width: 90,
          minWidth: 90,
          flex: 1,
          unSortIcon: true,

          suppressMenu: true
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 78,
          minWidth: 78,

          flex: 1
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 80,
          minWidth: 80,

          flex: 1
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 90,
          minWidth: 90,

          flex: 1
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 90,
          minWidth: 90,

          flex: 1
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          calculate: false,
          hide: true,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 150,
          minWidth: 150,

          flex: 1
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 110,
          minWidth: 110,

          flex: 1
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 55,
        //   sortable: false,
        //   // filter: false,
        //   suppressMenu: true,

        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          calculate: false,
          hide: true,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 68,
          minWidth: 68,

          flex: 1
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          calculate: true,
          hide: false,
          numeric: true,
          width: 80,
          minWidth: 80,
          flex: 1,
          unSortIcon: true,
          suppressMenu: true,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          calculate: true,
          hide: false,
          numeric: true,
          unSortIcon: true,
          suppressMenu: true,
          width: 80,
          minWidth: 80,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          calculate: true,
          hide: false,
          numeric: true,
          unSortIcon: true,
          suppressMenu: true,
          width: 80,
          minWidth: 80,
          flex: 1,
          valueFormatter: this.formatCellValueWithOut$,
          filterParams: {
            valueFormatter: this.formatCellValueWithOut$
          },
          cellStyle: this.cellStyles
        },

        {
          headerName: 'Labor GP',
          field: 'lbrgrossprofit',
          calculate: false,
          hide: false,
          numeric: true,
          unSortIcon: true,
          suppressMenu: true,
          width: 78,
          minWidth: 78,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor GP %',
          field: 'lbrGrossprofitpercentage',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 78,
          minWidth: 78,
          flex: 1,

          valueFormatter: this.formatCellValueGP,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'ELR',
          field: 'elr',
          calculate: false,
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 90,
          minWidth: 90,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        }
      ],
      columnDefsParts: [
        {
          headerName: 'RO',
          // filter: 'agTextColumnFilter',
          field: 'ronumber',
          unSortIcon: true,
          suppressMenu: true,
          width: 90,
          minWidth: 90,
          flex: 1,

          chartDataType: 'category',
          // cellRenderer: 'agGroupCellRenderer',

          // hide: drillDown == 33 ? true : false,
          onCellClicked: this.handleSearchByRo,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              // left: '-28px',
              cursor: 'pointer'
            };
          }
        },
        // {
        //   headerName: 'Exclude',
        //   //field: 'lbropcode',
        //   // unSortIcon: true,
        //   sortable: false,
        //   suppressMenu: true,
        //   width: 90,

        //   chartDataType: 'category',
        //   cellRenderer: 'checkboxRenderer',
        //   cellStyle() {
        //     return { textAlign: 'center' };
        //   }
        // },
        {
          headerName: 'Month',
          field: 'monthYear',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 85,
          minWidth: 85,
          flex: 1,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          valueFormatter: this.formatCellValueMonthYear
        },
        {
          headerName: 'Open Date',
          field: 'opendate',
          chartDataType: 'series',
          calculate: false,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          hide: false,
          numeric: false,
          unSortIcon: true,
          suppressMenu: true,
          width: 88,
          minWidth: 88,

          flex: 1
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 88,
          minWidth: 88,
          flex: 1,

          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },

        {
          headerName: 'Pay Type',
          field: 'paytype',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 80,
          minWidth: 80,

          flex: 1
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 90,
          minWidth: 90,

          flex: 1
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 90,
          minWidth: 90,

          flex: 1
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: true,
          numeric: false,
          width: 150,
          minWidth: 150,

          flex: 1
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 110,
          minWidth: 110,

          flex: 1
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 55,
        //   sortable: false,
        //   // filter: false,
        //   suppressMenu: true,

        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          unSortIcon: true,
          suppressMenu: true,
          calculate: true,
          hide: false,
          numeric: true,
          width: 90,
          minWidth: 90,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          unSortIcon: true,
          suppressMenu: true,
          calculate: true,
          hide: false,
          numeric: true,
          width: 90,
          minWidth: 90,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Markup',
          field: 'markup',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: true,
          width: 90,
          minWidth: 90,
          flex: 1,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            valueFormatter: this.formatCellValueMarkup
          },
          cellStyle: this.cellStyles
          //valueFormatter: params => params.data.number.toFixed(4)
        },

        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 90,
          minWidth: 90,
          flex: 1,

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts GP %',
          field: 'prtsGrossprofitpercentage',
          unSortIcon: true,
          suppressMenu: true,
          calculate: false,
          hide: false,
          numeric: false,
          width: 90,
          minWidth: 90,
          flex: 1,

          valueFormatter: this.formatCellValueGP,
          cellStyle: this.cellStyles
        }
      ],
      context: { componentParent: this },
      frameworkComponents: {
        checkboxRenderer: CheckboxRenderer
      },
      //autoGroupColumnDef: { minWidth: 200 },

      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: null
      },
      headerHeight: 45,
      autoGroupColumnDef: { minWidth: 200 },
      // sideBar: 'columns',
      pivotMode: true,
      rowData: [],
      statusBar: { statusPanels: [{ statusPanel: 'agAggregationComponent' }] },
      rowSelection: 'multiple',
      defaultColDef: {
        headerClass: function(params) {
          return 'header_theme';
        },
        cellClassRules: {
          border: 'solid 1px #003d6b',
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        floatingFilter: true,
        enableFilter: false,
        // filterParams: { newRowsAction: 'keep' },
        enableValue: true,
        sortable: true,
        resizable: false,
        enablePivot: false,
        suppressMovable: false
        //sort:'asc'
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  handleRowSelection = (val, checked, rowId) => {
    val.rowId = rowId;
    console.log('val', val);
    if (checked) {
      this.setState(prevState => ({
        selectedRowData: [...prevState.selectedRowData, val]
        //isChecked: checked
      }));
    } else {
      let rowVal;

      rowVal = lodash.filter(this.state.selectedRowData, function(o) {
        return o.rowId != rowId;
      });

      console.log('rowVal', rowVal);
      this.setState(prevState => ({
        selectedRowData: rowVal
        //isChecked: checked
      }));
    }

    this.setState({ isSumChanged: true });
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    console.log('Filter values changed:', filterValues);
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    if (!Object.keys(filterValues).length == 0) {
      this.setState({ isSRowDataChanged: true });
      this.handleSumChanged(rowData);
    }
  };
  handleSumChanged = data => {
    var selectedRowData = data ? data : this.state.selectedRowData;
    let LbrSale = 0;
    let LbrCost = 0;
    let PartsSale = 0;
    let PartsCost = 0;
    let LbrSoldHours = 0;
    if (selectedRowData.length > 0) {
      selectedRowData.forEach(function(value) {
        LbrSale += parseFloat(value.lbrsale);
        LbrCost += parseFloat(value.lbrcost);
        PartsSale += parseFloat(value.prtextendedsale);
        PartsCost += parseFloat(value.prtextendedcost);
        LbrSoldHours += parseFloat(value.lbrsoldhours);
      });
      console.log('totalLaborSaleSum1111', Number(LbrSale).toFixed(2), LbrSale);
      let totalLaborSaleSum =
        // Number(this.state.laborSaleSum.replace('$', '').replace(/,/g, '')) -
        Number(LbrSale).toFixed(2);

      let totalLaborCostSum =
        // Number(this.state.laborCostSum.replace('$', '').replace(/,/g, '')) -
        Number(LbrCost).toFixed(2);
      let totallaborHoursSum =
        // Number(this.state.laborHoursSum.replace('$', '').replace(/,/g, '')) -
        Number(LbrSoldHours).toFixed(2);
      let totalpartsSaleSum =
        // Number(this.state.partsSaleSum.replace('$', '').replace(/,/g, '')) -
        Number(PartsSale).toFixed(2);
      let totalpartsCostSum =
        // Number(this.state.partsCostSum.replace('$', '').replace(/,/g, '')) -
        Number(PartsCost).toFixed(2);
      console.log(
        'totalLaborSaleSum',

        totalLaborSaleSum
      );
      this.setState({
        revlaborSaleSum: '$' + totalLaborSaleSum
        // Math.round(totalLaborSaleSum + Number.EPSILON).toLocaleString('en-GB')
      });
      this.setState({
        revlaborCostSum: '$' + totalLaborCostSum
        // Math.round(totalLaborCostSum + Number.EPSILON).toLocaleString('en-GB')
      });
      this.setState({
        revpartsSaleSum: '$' + totalpartsSaleSum
        // Math.round(totalpartsSaleSum + Number.EPSILON).toLocaleString('en-GB')
      });
      this.setState({
        revpartsCostSum: '$' + totalpartsCostSum
        // Math.round(totalpartsCostSum + Number.EPSILON).toLocaleString('en-GB')
      });
      this.setState({
        revlaborHoursSum: totallaborHoursSum
        //   Math.round(
        //   totallaborHoursSum + Number.EPSILON
        // ).toLocaleString('en-GB')
      });

      this.setState({ isSumChanged: false });

      // this.setState({ isSRowDataChanged: true });
    } else {
      this.setState({ isSumChanged: false });
      this.setState({ isSRowDataChanged: false });
    }
  };
  cellStyles = () => {
    return {
      textAlign: 'right'
    };
  };

  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/DD/YY');
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value).toFixed(4);
    } else {
      return '0.0000';
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        Math.abs(
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
      );
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  componentDidMount() {
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        this.setState({ monthYearArr: monthArr });
        //setMonths(monthArr);

        // const newMonthStart = moment(this.state.queryMonth).startOf("month");
        // const newMonthEnd = moment(this.state.queryMonth).endOf("month");

        // this.setState({
        //   selectedMonth: this.state.queryMonth.startOf("month"),
        //   selectedDateRange: {
        //     startDate: newMonthStart,
        //     endDate: newMonthEnd,
        //   },
        // });

        // var startDate = moment(new Date(this.state.queryMonth + '-01')).format(
        //   'YYYY-MM-DD'
        // );

        // var endDate = moment(new Date(this.state.queryMonth + '-01'))
        //   .clone()
        //   .endOf('month')
        //   .format('YYYY-MM-DD');
        // console.log([startDate, endDate]);
        // this.setState({ selectedDate: [startDate, endDate] });
      }
    });
  }
  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      fileName:
        this.state.drillDownType == 'laborandparts'
          ? 'Revenue Details - Labor & Parts'
          : this.state.drillDownType == 'labor'
          ? 'Revenue Details - Labor'
          : 'Revenue Details - Parts',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value:
                this.state.drillDownType == 'laborandparts'
                  ? 'Revenue Details - Labor & Parts'
                  : this.state.drillDownType == 'labor'
                  ? 'Revenue Details - Labor'
                  : 'Revenue Details - Parts'
            },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: cell => {
        if (cell.column.getColId().includes('opendate')) {
          return isValidDate(cell.value)
            ? moment(cell.value).format('MM/DD/YY')
            : cell.value;
        }
        if (cell.column.getColId().includes('closeddate')) {
          return isValidDate(cell.value)
            ? moment(cell.value).format('MM/DD/YY')
            : cell.value;
        }
        if (cell.column.getColId().includes('monthYear')) {
          return moment(cell.value).format('MM/YY');
        }
        return cell.value; // for other fields, return the original value
      }
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onGridReady = params => {
    // params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    // this.gridApi.sizeColumnsToFit();
    console.log('window====', window, this.props);

    this.gridApi.setSortModel(window.sortState);
    this.gridApi.setFilterModel(window.filterStateSummary);
    if (window.colState && this.gridColumnApi) {
      this.gridColumnApi.setColumnState(window.colState);
    }
    if (
      this.props.history.location.state == undefined ||
      (this.props.history.location.state &&
        this.props.history.location.state.isFrom &&
        this.props.history.location.state.isFrom == 'revenueSummary')
    ) {
      window.sortState = {};
      window.filterStateSummary = {};
      this.state.gridColumnApi.resetColumnState();
    }
    // this.gridApi.setSortModel(window.sortState);
    // this.gridApi.setFilterModel(window.filterState);

    this.getAgGridData(
      this.state.queryMonth,
      this.state.serviceAdvisor,
      this.props.type
    );
  };
  parseArray(filtererdArray) {
    let intArray = [];
    var res = filtererdArray.map(v => {
      intArray.push({
        closeddate: v.closeddate,
        advisorName: v.advisorName,
        filterByLaborparts: v.filterByLaborparts,
        filterByRevenue: v.filterByRevenue,
        issinglejob: v.issinglejob,
        serviceadvisor: parseFloat(v.serviceadvisor) || 0,
        lbrGrossprofitpercentage: v.lbrGrossprofitpercentage,
        lbrlinecode: v.lbrlinecode,
        lbropcode: v.lbropcode,
        lbropcodedesc: v.lbropcodedesc,
        lbrsequenceno: v.lbrsequenceno,
        monthYear: v.monthYear,
        opcategory: v.opcategory,
        opsubcategory: v.opsubcategory,
        paytype: v.paytype,
        paytypegroup: v.paytypegroup,
        ronumber: v.ronumber,
        lbrtechno: v.lbrtechno,
        elr: parseFloat(v['elr']) || 0,
        lbrcost: parseFloat(v['lbrcost']) || 0,
        lbrgrossprofit: parseFloat(v['lbrgrossprofit']) || 0,
        lbrsale: parseFloat(v['lbrsale']) || 0,
        lbrsoldhours: parseFloat(v['lbrsoldhours']) || 0,
        lbrtechhours: parseFloat(v['lbrtechhours']) || 0,
        markup: parseFloat(v['markup']) || 0,
        prtextendedcost: parseFloat(v['prtextendedcost']) || 0,
        prtextendedsale: parseFloat(v['prtextendedsale']) || 0,
        prtsGrossprofitpercentage:
          parseFloat(v['prtsGrossprofitpercentage']) || 0,
        prtsgrossprofit: parseFloat(v['prtsgrossprofit'] || 0),
        serviceadvisor: v.serviceadvisor,
        vin: v.vin,
        linaddonflag: v.linaddonflag,
        openMonth: v.openMonth,
        opendate: v.opendate,
        lbrPaytype: v.lbrPaytype,
        jobcount: v.jobcount,
        laborsale: parseFloat(v['laborsale']) || 0,
        customerpayshopsup: parseFloat(v['customerpayshopsup']) || 0,
        internalshopsup: parseFloat(v['internalshopsup']) || 0,
        disdiscountid: v.disdiscountid,
        dislevel: v.dislevel,
        salepercentage: parseFloat(v['salepercentage']) || 0,
        apportionedlbrdiscount: parseFloat(v['apportionedlbrdiscount']) || 0,
        laborpaytypegroup: v.laborpaytypegroup,
        prtlinecode: v.prtlinecode,
        prtcost: parseFloat(v.prtcost) || 0,
        prtssale: parseFloat(v.prtssale) || 0,
        totaldiscount: parseFloat(v.totaldiscount) || 0,
        totalpartsdiscount: parseFloat(v.totalpartsdiscount) || 0,
        totallabordiscount: parseFloat(v.totallabordiscount) || 0,
        prtsale: parseFloat(v.prtsale) || 0,
        partsdiscount: parseFloat(v.partsdiscount) || 0,
        labordiscount: parseFloat(v.labordiscount) || 0,
        discountedlabor: parseFloat(v.discountedlabor) || 0,
        discountedparts: parseFloat(v.discountedparts) || 0,
        partsdiscountdollars: parseFloat(v.partsdiscountdollars) || 0,
        labordiscountdollars: parseFloat(v.labordiscountdollars) || 0,
        partssale: parseFloat(v.partssale) || 0,
        percentagepertotalcpsale: parseFloat(v.percentagepertotalcpsale) || 0,
        discountpercentageperdiscountcpsale:
          parseFloat(v.discountpercentageperdiscountcpsale) || 0,
        discountedlaborpercpro: parseFloat(v.discountedlaborpercpro) || 0,
        discountedlaborperdiscountedro:
          parseFloat(v.discountedlaborperdiscountedro) || 0,
        discountedpartspercpro: parseFloat(v.discountedpartspercpro) || 0,
        discountedpartsrperdiscountedro:
          parseFloat(v.discountedpartsrperdiscountedro) || 0,
        totaldiscountspercpro: parseFloat(v.totaldiscountspercpro) || 0,
        totaldiscountsperdiscountedcpro:
          parseFloat(v.totaldiscountsperdiscountedcpro) || 0,
        disdesc: v.disdesc,
        laborpaytype: v.laborpaytype,
        prtlaborsequenceno: v.prtlaborsequenceno,
        lbrlabortype: v.lbrlabortype,
        partspaytype: v.partspaytype,
        partspaytypegroup: v.partspaytypegroup
      });
      // return res;
    });
    return intArray;
  }
  filterByPayTypeGroup = () => {
    var payTypeFilterComponent = this.state.rawGridApi.getFilterInstance(
      'paytypegroup'
    )
      ? this.state.rawGridApi.getFilterInstance('paytypegroup')
      : this.state.rawGridApi.getFilterInstance('lbrPaytype')
      ? this.state.rawGridApi.getFilterInstance('lbrPaytype')
      : this.state.rawGridApi.getFilterInstance('partspaytypegroup');

    payTypeFilterComponent.setModel({
      condition1: {
        type: 'startsWith',
        filter: 'C'
      },
      condition2: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter2
      },
      condition3: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter3
      },
      operator: 'OR'
    });
    this.state.rawGridApi.onFilterChanged();
  };
  getAgGridData(queryMonth, serviceAdvisor, type) {
    // let startDate = moment(this.state.startDate).format('YYYY-MM-DD');
    // let endDate = moment(this.state.endDate).format('YYYY-MM-DD');

    this.setState({ isLoading: true });
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    let DrilldownValues = 0;
    let RoCountValues = 0;
    let LbrSale = 0;
    let LbrCost = 0;
    let PartsSale = 0;
    let PartsCost = 0;
    let LbrSoldHours = 0;
    // let department =
    //   this.props &&
    //   this.props.history &&
    //   this.props.history.location &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.department;
    // let payType =
    //   this.props &&
    //   this.props.history &&
    //   this.props.history.location &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.payType;
    let start = this.state.startDate
      ? moment(this.state.startDate).format('YYYY-MM-DD')
      : moment(localStorage.getItem('closedDate'))
          .startOf('month')
          .format('YYYY-MM-DD');

    let end = this.state.endDate
      ? moment(this.state.endDate).format('YYYY-MM-DD')
      : moment(localStorage.getItem('closedDate'))
          .endOf('month')
          .format('YYYY-MM-DD');
    getDrillDownDataForRevenueSummary(
      null,
      this.state.department,
      this.state.payType,
      start,
      end,
      serviceAdvisor,
      result => {
        this.setState({ isLoading: false });
        if (
          result.data.statelessCcDrilldownGetDrillDownRevenuSummaryDetails
            .drillDownRevenuSummaryDetails
        ) {
          let filteredArr = [];
          var resultArr = this.parseArray(
            result.data.statelessCcDrilldownGetDrillDownRevenuSummaryDetails
              .drillDownRevenuSummaryDetails
          );
          const sorted = lodash.sortBy(resultArr, o => o.closeddate);
          if (this.state.drillDownType == 'laborandparts') {
            filteredArr = resultArr;
          } else {
            filteredArr = lodash.filter(sorted, function(o) {
              return o.paytypegroup == 'C';
            });
          }
          filteredArr.map(item => {
            if (item.opsubcategory && item.opsubcategory != 'N/A') {
              return (item.opsubcategory = '');
            }
          });
          //console.log('this.state', resultArr);
          this.setState({
            rowData: filteredArr
          });

          filteredArr.forEach(function(value) {
            LbrSale += parseFloat(value.lbrsale);
            LbrCost += parseFloat(value.lbrcost);
            PartsSale += parseFloat(value.prtextendedsale);
            PartsCost += parseFloat(value.prtextendedcost);
            LbrSoldHours += parseFloat(value.lbrsoldhours);
          });
          this.setState({
            laborSaleSum:
              '$' +
              Math.round(LbrSale + Number.EPSILON).toLocaleString('en-GB'),
            laborCostSum:
              '$' +
              Math.round(LbrCost + Number.EPSILON).toLocaleString('en-GB'),
            partsSaleSum:
              '$' +
              Math.round(PartsSale + Number.EPSILON).toLocaleString('en-GB'),
            partsCostSum:
              '$' +
              Math.round(PartsCost + Number.EPSILON).toLocaleString('en-GB'),
            laborHoursSum: Math.round(
              LbrSoldHours + Number.EPSILON
            ).toLocaleString('en-GB')
          });
          this.filterByPayTypeGroup();
          if (window.filterStateSummary != undefined) {
            this.filterByValue();
          }
          // DrilldownValues = myCustomSumAggregate(resultArr, '942');
          // this.setDrillDownValuesToState(DrilldownValues);

          // if (
          //   this.state.drillDownServiceAdvisor.includes('All') == false &&
          //   this.state.title != 'GP-CPLR' &&
          //   this.state.title != 'GP-LCPSH' &&
          //   this.state.title != 'GP-CPRO' &&
          //   this.state.title != 'GP-CPLGP' &&
          //   this.state.title != 'GP-CPPR' &&
          //   this.state.title != 'GP-CPPGP'
          // ) {
          //   RoCountValues = distinctROCount(filteredArr, this.state.chartId);
          // } else {
          //   if (this.state.chartId >= 1318 && this.state.chartId <= 1325) {
          //     var filteredResultForPrtsHours = resultArr.filter(
          //       item => item.prtextendedsale != 0
          //     );
          //     RoCountValues = distinctROCount(
          //       filteredResultForPrtsHours,
          //       this.state.chartId
          //     );
          //   } else {
          //     RoCountValues = distinctROCount(resultArr, this.state.chartId);
          //   }
          // }

          // var RoAndDrillDownCombined = Object.assign(
          //   RoCountValues,
          //   DrilldownValues
          // );
          // this.setDrillDownValuesToState(RoAndDrillDownCombined);
        }
      }
    );
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterState);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  nulValue = () => {
    return '';
  };

  setDrillDownValuesToState = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      LsWarranty: Values.LsWarranty
    });
  };

  // handleTabChange = (event, newValue) => {
  //   this.setState({ tabSelection: newValue });
  //   this.setState({ category: newValue });
  //   let type = '';
  //   if (newValue == 0) {
  //     type = 'discount_drilldown';
  //   } else {
  //     type = 'discounted_parts_drilldown';
  //   }
  //   this.setState({
  //     month_year: this.state.queryMonth,
  //     chartId: this.props.chartId,
  //     type: this.props.type
  //   });
  //   this.getAgGridData(this.state.queryMonth, this.state.serviceAdvisor, type);
  //   let data = {
  //     month_year: this.state.queryMonth,
  //     type: type,
  //     chartId: this.props.chartId
  //   };
  //   this.props.parentCallback(data);
  // };
  getMonthYear = () => {
    var table = [];
    this.setState({
      discountMonthYear: getLast13Months()
    });
  };
  renderBackButton = () => {
    // const department =
    //   this.props &&
    //   this.props.history &&
    //   this.props.history.location &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.department;
    // const componentType =
    //   this.props &&
    //   this.props.history &&
    //   this.props.history.location &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.componentType;
    window.sortState = {};
    window.filterStateSummary = {};
    this.state.gridColumnApi.resetColumnState();

    this.props.history.push({
      pathname: '/RevenueSummary',
      department: this.state.department,
      componentType: this.state.componentType
    });
  };
  handleSearchByRo = params => {
    console.log('tparams', params);
    window.sortState = this.gridApi.getSortModel();
    window.colState = this.state.gridColumnApi.getColumnState();
    window.filterStateSummary = this.gridApi.getFilterModel();
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,

        pageType: 'revenueSummary',
        queryMonth: this.state.queryMonth,
        drillDownType: this.state.drillDownType,
        department: this.state.department
      }
    });
  };
  isExternalFilterPresent = () => {
    return this.state.value;
  };

  handleDateChange = (event, picker) => {
    window.sortState = {};
    window.filterStateSummary = {};
    this.setState({ isSRowDataChanged: false });
    this.setState({ filterDisabled: false });
    this.setState({ startDate: picker.startDate });
    this.setState({ endDate: picker.endDate });
    // this.setState({
    //   selectedDateRange: {
    //     startDate: picker.startDate,
    //     endDate: picker.endDate
    //   }
    // });
    let date = new Date(picker.startDate);
    this.setState({
      queryMonth: date.getFullYear() + '-' + moment(new Date(date)).format('MM')
    });

    this.getAgGridData(
      // this.state.selectedDateRange.endDate,
      // this.state.selectedDateRange.startDate,
      this.state.queryMonth,
      this.state.serviceAdvisor,
      this.props.type
    );
  };
  resetReportGrid = () => {
    // this.setState({ resetLayout: true });
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
    this.setState({ isSRowDataChanged: false });
  };

  render() {
    const { classes } = this.props;
    const dateData = store.getState().session;
    let today;
    let yesterday;
    if (dateData.dateData[0]) {
      today = dateData.dateData[0].today;
      yesterday = dateData.dateData[0].yesterday;
      console.log('dateData===', dateData.dateData[0]);
    }

    console.log('getState===', today, yesterday);
    const { selectedMonth, selectedDateRange, startDate, endDate } = this.state;
    console.log('startDate1', selectedDateRange);
    console.log('sell==', this.state.selectedRowData);
    if (this.state.isSumChanged) {
      this.handleSumChanged();
    }
    let topPadding;
    if (this.state.isSRowDataChanged) {
      topPadding = 'padding-top: 8px';
    } else {
      topPadding = '';
    }
    const labelTitle =
      this.state.drillDownType == 'laborandparts'
        ? 'Revenue Details - Labor & Parts'
        : this.state.drillDownType == 'labor'
        ? 'Revenue Details - Labor'
        : 'Revenue Details - Parts';
    let label =
      '<div style="width: 100%;padding:0 20px;"><div><label style="float:left;font-size:16px;padding-left:50px;' +
      topPadding +
      '">' +
      labelTitle +
      (process.env.REACT_APP_DEALER == 'Armatus' && window.innerWidth == 1366
        ? '</label><div style="margin-left:70px;margin-top:1px;text-align: right;">'
        : process.env.REACT_APP_DEALER == 'Armatus'
        ? '</label></div><div style="margin-left:90px;margin-top:1px;text-align: right;">'
        : '</label><div style="margin-left:90px;">');
    if (!this.state.isLoading && this.state.rowData.length > 0) {
      if (this.state.drillDownType == 'labor') {
        label +=
          '<div ><label style="font-size: 13px;padding-top: 4px;"><span style="color:#C2185B">Totals:</span> Labor Sale: <span style="color:#000">' +
          this.state.laborSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Labor Cost: <span style="color:#000">' +
          this.state.laborCostSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Sold Hours: <span style="color:#000">' +
          this.state.laborHoursSum +
          '</span></label></div>';
      } else if (this.state.drillDownType == 'parts') {
        label +=
          '<div ><label style="font-size: 13px;padding-top: 4px;"><span style="color:#C2185B">Totals:</span> Parts Sale: <span style="color:#000">' +
          this.state.partsSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Parts Cost: <span style="color:#000">' +
          this.state.partsCostSum +
          '</span></label></div>';
      } else {
        label +=
          '<div style="margin-top:-5px;"><label style="font-size: 13px;padding-top: 4px;"><span style="color:#C2185B">Totals:</span> Labor Sale: <span style="color:#000">' +
          this.state.laborSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Labor Cost: <span style="color:#000">' +
          this.state.laborCostSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Sold Hours: <span style="color:#000">' +
          this.state.laborHoursSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Parts Sale: <span style="color:#000">' +
          this.state.partsSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Parts Cost: <span style="color:#000">' +
          this.state.partsCostSum +
          '</span></label></div>';
      }
    }
    if (this.state.isSRowDataChanged) {
      if (this.state.drillDownType == 'labor') {
        label +=
          '<div style="margin-top: -13px;"><label style="font-size: 13px;padding-top: 4px;margin-left:45px"><span style="color:#C2185B">Revised Totals:</span> Labor Sale: <span style="color:#000">' +
          this.state.revlaborSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Labor Cost: <span style="color:#000">' +
          this.state.revlaborCostSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Sold Hours: <span style="color:#000">' +
          this.state.revlaborHoursSum +
          '</span></label></div>';
      } else if (this.state.drillDownType == 'parts') {
        label +=
          '<div style="margin-top: -13px;"><label style="font-size: 13px;padding-top: 4px;margin-left:45px"><span style="color:#C2185B">Revised Totals:</span> Parts Sale: <span style="color:#000">' +
          this.state.revpartsSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Parts Cost: <span style="color:#000">' +
          this.state.revpartsCostSum +
          '</span></label></div>';
      } else {
        label +=
          '<div style="margin-top: -13px"><label style="font-size: 13px;padding-top: 4px;margin-left:45px"><span style="color:#C2185B">Revised Totals:</span> Labor Sale: <span style="color:#000">' +
          this.state.revlaborSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Labor Cost: <span style="color:#000">' +
          this.state.revlaborCostSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Sold Hours: <span style="color:#000">' +
          this.state.revlaborHoursSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Parts Sale: <span style="color:#000">' +
          this.state.revpartsSaleSum +
          '</span>&nbsp;&nbsp;&nbsp;&nbsp; Parts Cost: <span style="color:#000">' +
          this.state.revpartsCostSum +
          '</span></label></div>';
      }
    }

    label += '</div></div>';
    //this.handleSumChanged();
    console.log('drillDownType=', this.state.drillDownType);
    return (
      <div>
        {/* <Paper square style={{ margin: 8 , marginTop: '20px'}}>
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Link
              href="#"
              style={{ paddingTop: 12, paddingLeft: 16 }}
              title="Go Back"
              onClick={this.renderBackButton}
            >
              <ArrowBackIcon />
            </Link>
            <Tab
              style={{ textTransform: 'none', paddingRight: 0 }}
              label={ReactHtmlParser(label)}
              value="one"
            />
          </Tabs>
        </Paper> */}

        <Paper square className={classes.headerContainer}>
          <Grid
            container
            className={clsx(this.props.titleContainer, 'reset-dashboard')}
          >
            <Grid
              item
              xs={1}
              style={{
                display: 'flex',
                alignItems: 'center',
                position: 'absolute'
              }}
            >
              {this.props &&
                this.props.history &&
                this.props.history.location &&
                this.props.history.location.state != undefined && (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.renderBackButton}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                )}
              {/* <Button
                variant="contained"
                className={'bck-btn'}
                onClick={this.renderBackButton}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button> */}
            </Grid>
            <Grid
              item
              xs={11}
              id="detailSummary"
              style={{
                display: 'flex',
                alignItems: 'center',
                whiteSpace: 'nowrap',
                justifyContent: 'space-between'
              }}
            >
              <Typography
                variant="h4"
                color="primary"
                className={clsx(this.props.mainLabel, 'main-title')}
              >
                {ReactHtmlParser(label)}
              </Typography>
            </Grid>
          </Grid>
        </Paper>
        {/* {(this.props.keycloak.realmAccess.roles.includes('client') ||
          this.props.keycloak.realmAccess.roles.includes('user')) ?
            <Redirect to="/errors/error-404" /> 
          : */}
        <Grid container spacing={12}>
          <Grid item xs={12} style={{ padding: '5px' }}>
            <Paper square justify="center">
              <Grid
                container
                className={clsx(classes.titleContainer, 'reset-dashboard')}
              >
                <Grid
                  item
                  xs={9}
                  style={{ display: 'flex', alignItems: 'right' }}
                >
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={clsx(classes.formControl, 'input-container')}
                  >
                    {dateData.dateData[0] && (
                      <DateRangePicker
                        key={selectedMonth.format()}
                        initialSettings={{
                          startDate,
                          endDate,
                          locale: {
                            format: 'MM/DD/YY',
                            separator: ' - '
                          },
                          ranges: {
                            ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(today).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].today
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].today
                              ).toDate()
                            ],
                            ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0] &&
                                dateData.dateData[0].yesterday
                            ).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].yesterday
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].yesterday
                              ).toDate()
                            ],
                            ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].dayBeforeYesterday
                            ).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].dayBeforeYesterday
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].dayBeforeYesterday
                              ).toDate()
                            ],
                            ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].thisweekstartdate
                            ).format('MMM DD') +
                            ' to ' +
                            moment(dateData.dateData[0].thisweekenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0].thisweekstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].thisweekenddate
                              ).toDate()
                            ],
                            ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastweekstartdate
                            ).format('MMM DD') +
                            ' to ' +
                            moment(dateData.dateData[0].lastweekenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0].lastweekstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastweekenddate
                              ).toDate()
                            ],
                            ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lasttwoweekstartdate
                            ).format('MMM DD') +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lasttwoweekenddate
                            ).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0].lasttwoweekstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lasttwoweekenddate
                              ).toDate()
                            ],
                            ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(dateData.dateData[0].mtdstartdate).format(
                              'MMM DD'
                            ) +
                            ' to ' +
                            moment(dateData.dateData[0].mtdenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].mtdstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].mtdenddate
                              ).toDate()
                            ],
                            ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastmonthstartdate
                            ).format('MMM')]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].lastmonthstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].lastmonthenddate
                              ).toDate()
                            ],
                            ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastthreemonthstartdate
                            ).format('MMM') +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lastthreemonthenddate
                            ).format('MMM')]: [
                              moment(
                                dateData.dateData[0].lastthreemonthstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastthreemonthenddate
                              ).toDate()
                            ],
                            ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastquarterstartdate
                            ).format('MMM') +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lastquarterenddate
                            ).format('MMM')]: [
                              moment(
                                dateData.dateData[0].lastquarterstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastquarterenddate
                              ).toDate()
                            ],
                            ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(dateData.dateData[0].ytdstartdate).format(
                              'MMM DD'
                            ) +
                            ' to ' +
                            moment(dateData.dateData[0].ytdenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0].ytdstartdate
                              ).toDate(),
                              moment(dateData.dateData[0].ytdenddate).toDate()
                            ],
                            ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lasttwelvemonthstartdate
                            ).format("MMM ' YY") +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lasttwelvemonthenddate
                            ).format("MMM ' YY")]: [
                              moment(
                                dateData.dateData[0].lasttwelvemonthstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lasttwelvemonthenddate
                              ).toDate()
                            ],
                            ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastyearstartdate
                            ).format('MMM') +
                            ' to ' +
                            moment(dateData.dateData[0].lastyearenddate).format(
                              'MMM'
                            ) +
                            " ' " +
                            getYearValue(
                              dateData.dateData[0].lastyearenddate
                            )]: [
                              moment(
                                dateData.dateData[0].lastyearstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastyearenddate
                              ).toDate()
                            ]

                            // Today: [moment().toDate(), moment().toDate()],
                            // Yesterday: [
                            //   moment()
                            //     .subtract(1, 'days')
                            //     .toDate(),
                            //   moment()
                            //     .subtract(1, 'days')
                            //     .toDate()
                            // ],
                            // 'Last 7 Days': [
                            //   moment()
                            //     .subtract(6, 'days')
                            //     .toDate(),
                            //   moment().toDate()
                            // ],
                            // 'Last 30 Days': [
                            //   moment()
                            //     .subtract(29, 'days')
                            //     .toDate(),
                            //   moment().toDate()
                            // ],
                            // 'This Month': [
                            //   moment()
                            //     .startOf('month')
                            //     .toDate(),
                            //   moment()
                            //     .endOf('month')
                            //     .toDate()
                            // ],
                            // 'Last Month': [
                            //   moment()
                            //     .subtract(1, 'month')
                            //     .startOf('month')
                            //     .toDate(),
                            //   moment()
                            //     .subtract(1, 'month')
                            //     .endOf('month')
                            //     .toDate()
                            // ]
                          },

                          maxDate: moment(
                            dateData.dateData[0] && dateData.dateData[0].today
                          ).toDate(),
                          alwaysShowCalendars: false,
                          applyClass: clsx(classes.calButton, 'apply-btn'),
                          cancelClass: clsx(classes.calButton, 'apply-btn'),
                          // startDate: moment(startDate1).toDate(),

                          // endDate: moment(endDate1).toDate()
                          startDate:
                            startDate && startDate != ''
                              ? moment(startDate).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  dateData.dateData[0] &&
                                    dateData.dateData[0].mtdstartdate
                                ).toDate()
                              : moment(
                                  dateData.dateData[0] &&
                                    dateData.dateData[0].lastmonthstartdate
                                ).toDate(),
                          endDate:
                            endDate && endDate != ''
                              ? moment(endDate).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  dateData.dateData[0] &&
                                    dateData.dateData[0].mtdenddate
                                ).toDate()
                              : moment(
                                  dateData.dateData[0] &&
                                    dateData.dateData[0].lastmonthenddate
                                ).toDate()
                        }}
                        onApply={this.handleDateChange}
                      >
                        <input
                          type="text"
                          className="datepicker"
                          id="picker"
                          name="picker"
                          aria-labelledby="label-picker"
                          // value={`${startDate.format(
                          //   'MM/DD/YY'
                          // )} - ${endDate.format('MM/DD/YY')}`}
                        />
                      </DateRangePicker>
                    )}
                    <label class="labelpicker" for="picker" id="label-picker">
                      <div class="textpicker">Select Date (EST)</div>
                    </label>
                  </FormControl>
                </Grid>
                <Grid
                  item
                  xs={3}
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    paddingLeft: '10%',
                    marginTop: 7,
                    marginBottom: 6
                  }}
                >
                  <Tooltip title="Export To Excel">
                    <Link
                      id="export-to-excel"
                      style={{
                        paddingRight: 8,
                        cursor: 'pointer',
                        marginTop: 3
                      }}
                      onClick={this.onBtExport}
                    >
                      <ExportIcon />
                    </Link>
                  </Tooltip>
                  <Button
                    variant="contained"
                    id="reset-layout"
                    className={clsx(classes.back, 'reset-btn')}
                    onClick={this.resetReportGrid}
                  >
                    <RestoreIcon />
                    <Typography variant="body1" align="left">
                      Reset Layout
                    </Typography>
                  </Button>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
        {/* } */}
        {this.state.isLoading && (
          <div
          // style={{
          //   display: this.state.tabSelection != 1 ? 'none' : 'block'
          // }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              (this.state.rowData.length > 0
                ? window.innerHeight - 250
                : window.innerHeight - 160) + 'px',
            width: '98.8%',
            margin: 8,
            marginBottom: 0,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            isExternalFilterPresent={this.isExternalFilterPresent}
            // doesExternalFilterPass={this.doesExternalFilterPass}
            frameworkComponents={this.state.frameworkComponents}
            defaultColDef={this.state.defaultColDef}
            enableRangeSelection={true}
            headerHeight={this.state.headerHeight}
            sideBar={this.state.sideBar}
            animateRows={true}
            enableCharts={true}
            context={this.state.context}
            rowSelection={this.state.rowSelection}
            modules={AllModules}
            // modules={this.state.modules}
            columnDefs={
              this.state.drillDownType == 'laborandparts'
                ? this.state.columnDefs
                : this.state.drillDownType == 'labor'
                ? this.state.columnDefsLabor
                : this.state.columnDefsParts
            }
            statusBar={this.state.statusBar}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            suppressDragLeaveHidesColumns={true}
            onFilterChanged={this.onFilterChanged}
            suppressContextMenu={true}
            excelStyles={this.state.excelStyles}
          />
        </div>
        {/* {!this.state.isLoading && this.state.rowData.length > 0 && (
          <Paper square style={{ margin: 8, marginRight: 0, marginTop: 0 }}>
            <div className={classes.root}>
              <Grid
                container
                style={{
                  marginRight: -8,
                  display: 'flow-root list-item',
                  paddingTop: 5
                }}
              >
                {this.state.drillDownType == 'labor' ? (
                  <>
                    <SummaryTitle
                      title={'Labor Sold Hours'}
                      value={this.state.laborHoursSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Cost'}
                      value={this.state.laborCostSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.laborSaleSum}
                      drillDown={this.state.drillDown}
                      category={1}
                    />
                  </>
                ) : this.state.drillDownType == 'parts' ? (
                  <>
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.partsCostSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.partsSaleSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </>
                ) : (
                  <>
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.partsCostSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.partsSaleSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours'}
                      value={this.state.laborHoursSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Cost'}
                      value={this.state.laborCostSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.laborSaleSum}
                      drillDown={this.state.drillDown}
                      category={1}
                    />
                    <SummaryTitle
                      title={'Total'}
                      value={''}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </>
                )}
              </Grid>
              {this.state.isSRowDataChanged && (
                <Grid
                  container
                  style={{
                    marginRight: -8,
                    display: 'flow-root list-item',
                    paddingTop: 5
                  }}
                >
                  {this.state.drillDownType == 'labor' ? (
                    <>
                      <SummaryTitle
                        title={'Labor Sold Hours'}
                        value={this.state.revlaborHoursSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                      <SummaryTitle
                        title={'Total Labor Cost'}
                        value={this.state.revlaborCostSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                      <SummaryTitle
                        title={'Total Labor Sale'}
                        value={this.state.revlaborSaleSum}
                        drillDown={this.state.drillDown}
                        category={1}
                      />
                    </>
                  ) : this.state.drillDownType == 'parts' ? (
                    <>
                      <SummaryTitle
                        title={'Total Parts Cost'}
                        value={this.state.revpartsCostSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                      <SummaryTitle
                        title={'Total Parts Sale'}
                        value={this.state.revpartsSaleSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                    </>
                  ) : (
                    <>
                      <SummaryTitle
                        title={'Total Parts Cost'}
                        value={this.state.revpartsCostSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                      <SummaryTitle
                        title={'Total Parts Sale'}
                        value={this.state.revpartsSaleSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                      <SummaryTitle
                        title={'Labor Sold Hours'}
                        value={this.state.revlaborHoursSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                      <SummaryTitle
                        title={'Total Labor Cost'}
                        value={this.state.revlaborCostSum}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                      <SummaryTitle
                        title={'Total Labor Sale'}
                        value={this.state.revlaborSaleSum}
                        drillDown={this.state.drillDown}
                        category={1}
                      />
                      <SummaryTitle
                        title={'Revised Total'}
                        value={''}
                        drillDown={this.state.drillDown}
                        category={this.state.category}
                      />
                    </>
                  )}
                </Grid>
              )}
            </div>
          </Paper>
        )} */}
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <>
      <Grid
        item
        xs
        style={{ display: 'inline-flex', float: 'right', paddingRight: 5 }}
      >
        <Typography
          style={{
            fontSize: '13px',
            fontWeight:
              title == 'Total' || title == 'Revised Total' ? 'bold' : '',
            minHeight: '40px',
            padding: 3
          }}
          color={title == 'Total' || title == 'Revised Total' ? 'primary' : ''}
        >
          {title}:
        </Typography>
        <div>
          <Typography
            style={{ fontSize: '13px', fontWeight: 'bold' }}
            variant="subtitle1"
          >
            {' '}
            {value}
          </Typography>
        </div>
      </Grid>
    </>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(2),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    // border: 'thin solid #968989 !important'
    height: '32px !important'
  },
  reactDaterangePicker: {
    //padding: '4px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    color: '#ccc',
    height: '40px !important',
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif'
  },
  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  headerContainer: {
    marginTop: '10px',
    height: '50px',
    paddingTop: '10px',
    width: '99%',
    marginLeft: '5px',
    display: 'flex'
  },
  apply: {
    marginTop: 10
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  }
});

export default withStyles(styles)(withKeycloak(RevenueSummaryDrilldown));
