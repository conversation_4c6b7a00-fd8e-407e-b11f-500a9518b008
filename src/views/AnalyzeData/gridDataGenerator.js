export function getTablesToDisplay(
  groupBy,
  fullData,
  gridType,
  PersonFilterBy,
  OpCodeFilterBy
) {
 
  let columnsArray = [];
  // 'elr','lbrprofit','lbrprftpercentage','prtsmarkup','prtssale','prtscost','prtsprofit','partsprofitpercentage'];
  //  columnsDef=columnsDef.filter(element => element !== gridType)
  // Remove filter types
  if (gridType != 'labor') {
    columnsArray = [
      'prtscost',
      'prtssale',
      'prtsmarkup',
      'lbrsoldhours',
      'prtsprofit',
      'jobcount',
      'jobpercentage',
      'partsprofitpercentage'
    ];
  } else {
    columnsArray = [
      'lbrcost',
      'lbrsale',
      'elr',
      'lbrsoldhours',
      'lbrprofit',
      'jobcount',
      'jobpercentage',
      'lbrprftpercentage'
    ];
  }

  if (groupBy == 'PERSON') {
    columnsArray.unshift('personname');
    if (PersonFilterBy.includes('ServiceAdvisor')) {
      columnsArray.push('singlejobrocount', 'rocount');
    }
    if (PersonFilterBy.includes('Technician')) {
      columnsArray.push(
        'jobcount_repair',
        'jobcount_maintenance',
        'jobcount_competitive'
      );
    }
    columnsArray.push('prtstolaborratio');
  }
  if (groupBy == 'OP CODE') {
    columnsArray.unshift('lbropcode');
  }
  if (groupBy == 'OP CATEGORY') {
    columnsArray.unshift('opcategory');
  }

  let dataSeries = [];
  let xAxis = [];
  fullData.map((data, index) => {
    xAxis.push(fullData[index][columnsArray[0]]);
  });

  for (let i = 1; i < columnsArray.length; i++) {
    let chartName = getChartName(
      columnsArray[0],
      columnsArray[i],
      PersonFilterBy,
      OpCodeFilterBy
    );
    let series = [];
    let seiresObj = { name: columnsArray[i], dataSeries: [] };

    for (let j = 0; j < fullData.length; j++) {
      seiresObj.dataSeries.push(
        fullData[j][columnsArray[i]] == null ? 0 : fullData[j][columnsArray[i]]
      );
    }
    series.push(seiresObj);

    let resultObj = {
      categories: [],
      seriesData: [],
      chartName: '',
      chartType: ''
    };
    resultObj.categories = xAxis;
    resultObj.chartType = getChartType(groupBy, chartName);
    resultObj.chartName = chartName;
    resultObj.categories = xAxis;
    resultObj.seriesData = series;
    dataSeries.push(resultObj);
  }
  return dataSeries;
}

function getChartType(groupBy, chartName) {
  let chartType = 'line';

  if (groupBy == 'PERSON') {
    chartType = 'line';
  }
  if (groupBy == 'OP CODE') {
    chartType = 'line';
  }
  if (groupBy == 'OP CATEGORY') {
    chartType = 'line';
    if (chartName.includes('Job %')) {
      chartType = 'pie';
    }
  }
  return chartType;
}

function getChartName(chartType, key, PersonFilterBy, OpCodeFilterBy) {
  let chartName = '';
  if (chartType == 'personname') {
    chartName = PersonFilterBy + ' Vs ';
  }
  if (chartType == 'lbropcode') {
    if (OpCodeFilterBy == 'ALL') {
      chartName = 'OP Code Vs ';
    } else {
      chartName = OpCodeFilterBy + ' Vs ';
    }
  }
  if (chartType == 'opcategory') {
    chartName = 'OP Category Vs ';
  }

  switch (key) {
    case 'prtstolaborratio':
      chartName = chartName + 'Parts To Labor Ratio';
      break;
    case 'lbrsale':
      chartName = chartName + 'Labor Sale';
      break;

    case 'lbrcost':
      chartName = chartName + 'Labor Cost';
      break;
    case 'lbrsoldhours':
      chartName = chartName + 'Labor Sold Hours';
      break;
    case 'jobcount':
      chartName = chartName + 'Job Count';
      break;
    case 'elr':
      chartName = chartName + 'ELR';
      break;
    case 'lbrprofit':
      chartName = chartName + 'Labor Profit';
      break;
    case 'lbrprftpercentage':
      chartName = chartName + 'Labor Profit %';
      break;
    case 'prtsmarkup':
      chartName = chartName + 'Parts Markup';
      break;
    case 'prtssale':
      chartName = chartName + 'Parts Sale';
      break;
    case 'rocount':
      chartName = chartName + 'RO Count';
      break;
    case 'singlejobrocount':
      chartName = chartName + 'Single Job RO Count';
      break;

    case 'prtscost':
      chartName = chartName + 'Parts Cost';
      break;
    case 'prtsprofit':
      chartName = chartName + 'Parts Profit';
      break;
    case 'partsprofitpercentage':
      chartName = chartName + 'Parts Profit %';
      break;
    case 'jobpercentage':
      chartName = chartName + 'Job %';
      break;
    case 'jobcount_repair':
      chartName = chartName + 'Job Count Repair';
      break;
    case 'jobcount_maintenance':
      chartName = chartName + 'Job Count Maintenance';
      break;
    case 'jobcount_competitive':
      chartName = chartName + 'Job Count Competitive';
      break;
    default:
      chartName = '';
      break;
  }

  return chartName;
}

export function getYAxisSuffix(keyValue) {
  let yAxisFormatter = '';
  switch (keyValue) {
    case 'lbrsale':
      yAxisFormatter = '$';
      break;
    case 'prtstolaborratio':
      yAxisFormatter = 'D';
      break;

    case 'lbrcost':
      yAxisFormatter = '$';
      break;
    case 'prtsmarkup':
      yAxisFormatter = 'D';
      break;

    case 'elr':
      yAxisFormatter = '$';
      break;
    case 'lbrprofit':
      yAxisFormatter = '$';
      break;
    case 'lbrprftpercentage':
      yAxisFormatter = '%';
      break;

    case 'prtssale':
      yAxisFormatter = '$';
      break;

    case 'prtscost':
      yAxisFormatter = '$';
      break;
    case 'prtsprofit':
      yAxisFormatter = '$';
      break;
    case 'partsprofitpercentage':
      yAxisFormatter = '%';
      break;
    case 'jobpercentage':
      yAxisFormatter = '%';
      break;
    default:
      yAxisFormatter = '';
      break;
  }

  return yAxisFormatter;
}
