import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import { withStyles } from '@material-ui/styles';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import {
  Box,
  LinearProgress,
  Paper,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Divider
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'src/views/AnalyzeData/Component/Aggrid.css';

// import { AgGridReact } from 'ag-grid-react';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import moment from 'moment';
import sparkline from 'jquery-sparkline';
import {
  getWorkMixReportForLabor,
  getWorkMixReportForParts,
  getServiceAdvisorReport,
  getServiceAdvisorRevenues,
  getLastThirteenMonths,
  getWorkMixReportForTechEfficiency,
  getAllTechnicians,
  getAllSADetails
} from 'src/utils/hasuraServices';
import DrilldownTechDetailView from '../AnalyzeData/DrilldownTechDetailView';
import { withRouter } from 'react-router-dom';
import { getLast13Months } from 'src/utils/Utils';
import ToggleButton from '@material-ui/lab/ToggleButton';
import FormatAlignLeftIcon from '@material-ui/icons/FormatAlignLeft';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import HeaderComponent from 'src/components/HeaderComponent';
import CustomHeaderGroup from 'src/components/CustomHeaderGroup';
import clsx from 'clsx';
import { logDOM } from '@testing-library/react';
import { ReactSession } from 'react-client-session';
import { FALSE } from 'sass';

var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;
class Reports extends React.Component {
  componentWillMount() {
    console.log('enter=000');
    this.setState({ store: localStorage.getItem('selectedStoreId') });
    this.setState({ serviceAdvisors: ['All'] });
  }
  componentDidMount() {
    if (this.state.reportType == 'serviceAdvisor') {
      this.setState({ columnDefs: this.state.columnsForServAdv });
    }
    this.getAgGridData(this.state.reportType);
  }
  componentDidUpdate(prevProps) {
    if (
      this.state.reportType != this.props.reportType &&
      !lodash.isEmpty(this.state.gridApi)
    ) {
      {
        this.handleReportsChange(this.props.reportType);
      }
    }
    if (
      this.state.resetReport != this.props.resetReport &&
      !lodash.isEmpty(this.state.gridApi)
    ) {
      {
        this.handleReportsChange1();
      }
    }
    if (ReactSession.get('selectedReport') != undefined) {
      var checkStatusRpt = lodash.isEqual(
        this.state.reportType,
        ReactSession.get('selectedReport')
      );

      if (checkStatusRpt == false && this.state.gridColumnApi) {
        setTimeout(() => {
          this.state.gridColumnApi.resetColumnState();

          window.filterStateWorkMix = {};
          window.sortStateWorkMix = {};
        }, 500);
      }
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      console.log('enter=222');
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        this.getAgGridData(this.state.reportType);
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ isLoading: true });
        this.resetRowData();
      }
    }
    if (ReactSession.get('serviceAdvisors') != undefined) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisors,
        ReactSession.get('serviceAdvisors')
      );

      if (checkStatus == false && $('#advisorSelection').hasClass('selected')) {
        this.getAgGridData(
          this.state.reportType,
          ReactSession.get('serviceAdvisors')
        );
        this.setState({ isLoading: true });
        this.resetRowData();
      }
    }
    if (this.state.resetReport != this.props.resetReport) {
      this.getAgGridData(this.state.reportType);
    }
  }

  // componentDidUpdate(prevProps) {
  //   if (this.state.reportType != this.props.reportType) {
  //     {
  //       this.handleReportsChange(this.props.reportType);
  //     }
  //   }
  //   if (this.state.resetReport != this.props.resetReport) {
  //     {
  //       console.log("fffff=",JSON.parse(localStorage.getItem('selectedStoreId'))[0],JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.handleReportsChange1();this.getAgGridData(this.state.reportType);
  //     }
  //   }

  // }
  // componentWillMount(props) {
  //   if (this.state.isAdvisorCharts || this.state.isTechnicianCharts) {
  //     this.getAllActiveTechniciansAndAdvisors();
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    isParts: false,
    reportType: 'Sales',
    isEfficiencyCharts: false
  };

  constructor(props) {
    super(props);
    if (this.props.resetReport == true) {
      window.filterStateWorkMix = {};
      this.setState({ expandedItems: [] });
      if (this.gridApi) {
        this.gridApi.forEachNode(node => {
          node.expanded = false;
        });
      }
    }
    this.state = {
      originData: this.props.originData ? this.props.originData : '',
      showCharts: false,
      reportType: this.props.reportType,
      resetReport: this.props.resetReport,
      isLoading: true,
      gridApi: {},
      gridParams: {},
      eGui: {},
      opcatdata: [],
      expandedItems: [],
      expanded: 0,
      funCount: 0,
      yearMonthArr: [],
      selected: true,
      opcatSum: [],
      opcatSumTotal: [],
      dollarRequired: '',
      subtotalLabel: '',
      isTechnicianCharts: this.props.isTechnicianCharts,
      isAdvisorCharts: this.props.isAdvisorCharts,
      selectedRowData: [],
      columnDefs: this.getDefaultColumnDefs(),
      columnsForServAdv: this.getColumnDefsSA(),
      rowData: [],
      chartName: null,
      reportName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        //filter: true,
        resizable: true,
        filter: true,
        editable: false,
        suppressMovable: false
      },
      postSort: rowNodes => {
        let nextInsertPos = 0;
        for (let i = 0; i < rowNodes.length; i++) {
          const isActive = rowNodes[i].data ? rowNodes[i].data.isActive : '';
          if (isActive == 1) {
            rowNodes.splice(nextInsertPos, 0, rowNodes.splice(i, 1)[0]);
            nextInsertPos++;
          }
        }
      },
      // autoGroupColumnDef: { maxWidth: 150 },
      rowStyle: { background: 'white' },
      context: { componentParent: this },
      frameworkComponents: {
        customHeaderComponent: HeaderComponent,
        customHeaderComponentReports: CustomHeaderGroup
      },
      rowSelection: 'multiple',
      suppressRowClickSelection: true,
      // autoGroupColumnDef: { maxWidth: 150 },
      autoGroupColumnDef: {
        headerName: 'Opcode Category',
        field: 'opcode',
        // width: 90,
        maxWidth: 150,
        chartDataType: 'category',
        // cellRenderer: function(params) {
        //   if (params.value != undefined)
        //     return `<a style="cursor: pointer, padding-left: 70px">${params.value}</a>`;
        // },
        cellRendererParams: {
          suppressCount: true
        },
        tooltipField: 'opcodedesc',
        suppressMenu: true,
        unSortIcon: true,
        onCellClicked: this.handleCellClicked,

        cellClass: 'textAlign',
        cellStyle: function() {
          return {
            color: '#000000',
            fontWeight: 'bold',
            // paddingLeft: '70px',
            // left: '8px'
            cursor: 'pointer'
          };
        },
        hide:
          this.props.reportType == 'techefficiency' ||
          this.props.reportType == 'flatratehrs' ||
          this.props.reportType == 'allsoldhrs' ||
          this.props.reportType == 'actualhrs' ||
          this.props.reportType == 'SA_lbrsale' ||
          this.props.reportType == 'SA_soldhours' ||
          this.props.reportType == 'SA_jobcount' ||
          this.props.reportType == 'SA_profit' ||
          this.props.reportType == 'SA_elr' ||
          this.props.reportType == 'SA_rocount' ||
          this.props.reportType == 'SA_prtssale' ||
          this.props.reportType == 'SA_prtscost' ||
          this.props.reportType == 'SA_prtsprofit' ||
          this.props.reportType == 'SA_markup'
            ? true
            : false

        //  headerName: 'Opcode',
        //  maxWidth: 150,
        //   field: 'opcode',
        //   cellRendererParams: {
        //       suppressCount: true,
        //   }
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 20,
            color: 'primary'
          }
          // alignment: {
          //   horizontal: 'Right',
          //   vertical: 'Center'
          // }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0' }
        },
        {
          id: 'fourDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.0000' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0' }
        },
        {
          id: 'fourDecimalPlace',
          numberFormat: { format: '###0.0000' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  getDefaultColumnDefs = () => {
    return [
      // {
      //   field: '',
      //   width: 36,
      //   suppressCellFlash: true,
      //   lockPosition: true,
      //   chartDataType: 'category',
      //   headerCheckboxSelection: true,
      //   checkboxSelection: true,
      //   floatingFilterComponent: 'customHeaderComponentReports',
      //   suppressMenu: true,
      //   filter: false,
      //   cellStyle: function() {
      //     return {
      //       cursor: 'pointer'
      //     };
      //   }
      // },
      {
        // headerName: 'Op Category',
        field: 'opcategory',
        dataType: 'string',

        width: 110,
        rowGroup:
          this.props.reportType == 'techefficiency' ||
          this.props.reportType == 'flatratehrs' ||
          this.props.reportType == 'actualhrs' ||
          this.props.reportType == 'allsoldhrs' ||
          this.props.reportType == 'SA_lbrsale' ||
          this.props.reportType == 'SA_soldhours' ||
          this.props.reportType == 'SA_jobcount' ||
          this.props.reportType == 'SA_profit' ||
          this.props.reportType == 'SA_elr' ||
          this.props.reportType == 'SA_rocount' ||
          this.props.reportType == 'SA_prtssale' ||
          this.props.reportType == 'SA_prtscost' ||
          this.props.reportType == 'SA_prtsprofit' ||
          this.props.reportType == 'SA_markup'
            ? false
            : true,
        suppressCount: true,
        suppressColumnsToolPanel: true,
        hide: true,
        // cellRenderer: 'agGroupCellRenderer',
        cellStyle: function(params) {
          return { textAlign: 'left' };
        }
      },
      {
        headerName: '',
        field: 'opcodedesc',
        width: 0,
        chartDataType: 'category',
        suppressMenu: true,

        cellStyle: function() {
          return {
            color: '#000000',
            fontWeight: 'bold'
            // paddingLeft: '70px',
            // left: '8px'
          };
        },
        hide: true
      },
      {
        headerName: 'Tech No',
        field: 'lbrtechname',
        width: 160,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        tooltipField: 'lbrtechname',

        // cellStyle: function() {
        //   return {
        //     color: '#000000',
        //     fontWeight: 'bold'
        //   };
        // },
        suppressColumnsToolPanel: true,
        cellStyle: this.setCellStyle,
        cellRenderer: function(params) {
          if (params.value != undefined)
            return `<a style="cursor: pointer">${params.value}</a>`;
        },
        onCellClicked: this.handleCellClickedTechno,

        hide:
          this.props.reportType == 'techefficiency' ||
          this.props.reportType == 'flatratehrs' ||
          this.props.reportType == 'actualhrs' ||
          this.props.reportType == 'allsoldhrs'
            ? false
            : true
      },
      {
        headerName: 'Advisor',
        field: 'advisorName',
        width: 155,
        suppressMenu: true,
        unSortIcon: true,
        suppressColumnsToolPanel: true,
        suppressMenu: true,
        tooltip: params => params.value,
        comparator: (valueA, valueB) => {
          // Extract advisor names and numeric codes
          const nameA = (valueA || '').split('[')[0].trim();
          const nameB = (valueB || '').split('[')[0].trim();

          const numberA = parseInt(
            ((valueA && valueA.match(/\[(\d+)]/)) || [])[1] || 0,
            10
          );
          const numberB = parseInt(
            ((valueB && valueB.match(/\[(\d+)]/)) || [])[1] || 0,
            10
          );

          // Handle null values
          if (valueA === null && valueB === null) {
            return 0; // Both values are null, so they're considered equal
          } else if (valueA === null) {
            return 1; // valueA is null, so it should come after valueB
          } else if (valueB === null) {
            return -1; // valueB is null, so it should come before valueA
          }

          // Compare advisor names alphabetically
          const nameComparison = nameA.localeCompare(nameB);
          if (nameComparison !== 0) {
            return nameComparison;
          }

          // If names are the same, compare numeric codes
          return numberA - numberB;
        },
        cellStyle: this.setCellStyle,
        cellRenderer: function(params) {
          if (params.value != undefined)
            return `<a style="cursor: pointer">${params.value}</a>`;
        },
        cellClass: 'textAlign',
        onCellClicked: this.handleCellClickedSA,
        hide:
          this.props.reportType == 'SA_lbrsale' ||
          this.props.reportType == 'SA_soldhours' ||
          this.props.reportType == 'SA_jobcount' ||
          this.props.reportType == 'SA_profit' ||
          this.props.reportType == 'SA_elr' ||
          this.props.reportType == 'SA_rocount' ||
          this.props.reportType == 'SA_prtssale' ||
          this.props.reportType == 'SA_prtscost' ||
          this.props.reportType == 'SA_prtsprofit' ||
          this.props.reportType == 'SA_markup'
            ? false
            : true
      },
      {
        headerName: '13 Month Average',
        field: '13_months',
        width: 115,
        suppressMenu: true,
        unSortIcon: true,

        // cellStyle: function(params) {
        //   return { textAlign: 'center' };
        // },
        filter: 'agSetColumnFilter',
        chartDataType: 'category',
        suppressMenu: true,
        // valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass,

        valueGetter: this.calculateAvg,
        valueFormatter: this.formatCellValue,
        cellStyle: function(params) {
          return { textAlign: 'right' };
        }
        // cellStyle: this.getCellStyle,
      },
      {
        headerName: "Apr'20",
        field: 'mon36',
        width: 100,
        suppressMenu: true,
        unSortIcon: true,
        chartDataType: 'category',
        editable: false,

        // aggFunc: this.sumFunction,
        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
        // cellClass: 'greenBackground'
      },
      {
        headerName: "Mar'20",
        field: 'mon35',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        editable: false,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
        // cellClass: 'greenBackground'
      },
      {
        headerName: "Feb'20",
        field: 'mon34',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Jan'20",
        field: 'mon33',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Dec'19",
        field: 'mon32',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Nov'19",
        field: 'mon31',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Oct'19",
        field: 'mon30',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Sep'19",
        field: 'mon29',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Aug'19",
        field: 'mon28',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },

      {
        headerName: "Jul'19",
        field: 'mon27',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Jun'19",
        field: 'mon26',
        width: 100,
        chartDataType: 'category',
        suppressMenu: true,
        unSortIcon: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "May'19",
        field: 'mon25',
        width: 100,
        suppressMenu: true,
        unSortIcon: true,
        enableValue: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },
      {
        headerName: "Apr'19",
        field: 'mon24',
        width: 100,
        suppressMenu: true,
        unSortIcon: true,
        enableValue: true,

        cellStyle: this.getCellStyle,
        valueFormatter: this.formatCellValue,
        cellClass: this.getCellClass
      },

      {
        headerName: 'Trend',
        width: 350,
        // field: 'opcategory',
        // cellRenderer: 'agGroupCellRenderer',
        field: 'allData',
        cellRenderer: this.sparklineRenderer,

        cellRendererParams: {
          innerRenderer: this.sparklineRenderer
        },
        filter: false,
        // valueGetter: function(params) {
        //   const formattedData = [];

        //   if (typeof params.data != 'undefined') {
        //     const allData = params.data.alldata;
        //     const { monthnames, values } = allData;
        //     monthnames.map((xVal, i) =>
        //       formattedData.push([xVal, values[i]])
        //     );
        //   }

        //   return formattedData;
        // },

        headerComponent: 'customHeaderComponent',
        sortable: false,
        cellStyle: function(params) {
          return { textAlign: 'center', border: 'none', paddingRight: '70px' };
        },
        hide:
          this.props.reportType == 'techefficiency' ||
          this.props.reportType == 'flatratehrs' ||
          this.props.reportType == 'actualhrs' ||
          this.props.reportType == 'allsoldhrs'
            ? true
            : false
      },
      {
        headerName: 'Trend',
        width: 300,
        field: 'serviceadvisor1',
        headerComponent: 'customHeaderComponent',
        cellRenderer: this.sparklineRenderer,

        sortable: false,
        cellStyle: function(params) {
          return { textAlign: 'center', paddingRight: '70px' };
        },
        filter: false,
        hide:
          this.props.reportType == 'techefficiency' ||
          this.props.reportType == 'flatratehrs' ||
          this.props.reportType == 'actualhrs' ||
          this.props.reportType == 'allsoldhrs'
            ? false
            : true
      }
    ];
  };

  getColumnDefsSA = () => {
    return [
      {
        headerName: 'Advisor',
        field: 'serviceadvisor',
        width: 75,
        suppressMenu: true,

        cellStyle: function() {
          return {
            color: '#000000',
            fontWeight: 'bold',
            textAlign: 'left'
          };
        },
        cellRenderer: function(params) {
          if (params.value != undefined)
            return `<a style="cursor: pointer">${params.value}</a>`;
        },
        comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
          // Extract advisor names from advisorName field
          const nameA = (valueA || '').split('[')[0].trim();
          const nameB = (valueB || '').split('[')[0].trim();

          // Handle null values
          if (valueA === null && valueB === null) {
            return 0; // Both values are null, so they're considered equal
          } else if (valueA === null) {
            return 1; // valueA is null, so it should come after valueB
          } else if (valueB === null) {
            return -1; // valueB is null, so it should come before valueA
          }

          // Compare advisor names alphabetically
          return nameA.localeCompare(nameB);
        },
        cellClass: 'textAlign',
        onCellClicked: this.handleCellClickedSA
      },
      {
        headerName: 'Op Category',
        field: 'opcategory',
        width: 110,
        cellClass: 'textAlign',

        cellStyle: function(params) {
          return { textAlign: 'left' };
        }
      },
      {
        headerName: 'Month',
        field: 'month_year',
        width: 72,

        cellStyle: function(params) {
          return { textAlign: 'left' };
        }
      },
      {
        headerName: 'RO Count',
        field: 'rocount',
        width: 70,

        cellStyle: function(params) {
          return { textAlign: 'right' };
        }
      },
      {
        headerName: 'Job Count',
        field: 'jobcount',
        width: 70,

        cellStyle: function(params) {
          return { textAlign: 'right' };
        }
      },
      {
        headerName: 'Labor Sale',
        field: 'lbrsale',
        width: 78,
        cellClass: 'twoDecimalPlacesWith$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueSA
      },
      {
        headerName: 'Labor Cost',
        field: 'lbrcost',
        width: 78,

        cellClass: 'twoDecimalPlacesWith$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueSA
      },
      {
        headerName: 'Labor Profit',
        field: 'lbrprofit',
        width: 78,

        cellClass: 'twoDecimalPlacesWith$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueSA
      },
      {
        headerName: 'Sold Hours',
        field: 'lbrsoldhours',
        width: 68,

        cellClass: 'twoDecimalPlacesWithOut$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        }
      },
      {
        headerName: 'Parts Sale',
        field: 'prtssale',
        width: 78,

        cellClass: 'twoDecimalPlacesWith$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueSA
      },
      {
        headerName: 'Parts Cost',
        field: 'prtscost',
        width: 78,

        cellClass: 'twoDecimalPlacesWith$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueSA
      },
      {
        headerName: 'Parts Profit',
        field: 'prtsprofit',
        width: 78,

        cellClass: 'twoDecimalPlacesWith$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueSA
      },

      {
        headerName: 'Avg ELR',
        field: 'avgelr',
        width: 68,

        cellClass: 'twoDecimalPlacesWith$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueSA
      },
      {
        headerName: 'Avg Markup',
        field: 'avgmarkup',
        width: 75,

        cellClass: 'fourDecimalPlacesWithOut$',
        cellStyle: function(params) {
          return { textAlign: 'right' };
        },
        valueFormatter: this.formatCellValueMarkup
      }
    ];
  };

  setCellStyle = params => {
    if (this.state.isTechnicianCharts) {
      let activeTechs = [];
      this.state.rowData.filter(el => {
        if (el.isActive != 1) {
          activeTechs.push(el.lbrtechno);
        }
      });
      return {
        color: '#000000',
        fontWeight: 'bold',
        backgroundColor: activeTechs.includes(params.data.lbrtechno)
          ? Dealer === 'Armatus'
            ? '#ddeaf4'
            : '#F4E1E7'
          : ''
      };
    } else {
      //this.state.activeAdvisors
      let activeAdvisors = [];
      this.state.rowData.filter(el => {
        if (el.isActive != 1) {
          activeAdvisors.push(el.serviceadvisor);
        }
      });
      return {
        color: '#000000',
        fontWeight: 'bold',

        backgroundColor:
          params.data.serviceadvisor != '' &&
          activeAdvisors.includes(params.data.serviceadvisor)
            ? Dealer === 'Armatus'
              ? '#ddeaf4'
              : '#F4E1E7'
            : ''
      };
    }
  };
  getCellClass = params => {
    if (
      this.state.reportType == 'Sales' ||
      this.state.reportType == 'Cost' ||
      this.state.reportType == 'elr' ||
      this.state.reportType == 'SA_lbrsale' ||
      this.state.reportType == 'SA_profit' ||
      this.state.reportType == 'SA_elr' ||
      this.state.reportType == 'SA_prtssale' ||
      this.state.reportType == 'SA_prtscost' ||
      this.state.reportType == 'SA_prtsprofit'
    ) {
      return 'twoDecimalPlacesWith$';
    } else if (this.state.reportType == 'grossprofit') {
      return 'oneDecimalPlace';
    } else if (
      this.state.reportType == 'markup' ||
      this.state.reportType == 'SA_markup'
    ) {
      return 'fourDecimalPlace';
    } else {
      return 'twoDecimalPlacesWithOut$';
    }
  };
  formatCellValueSA = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    }
  };

  formatCellValue = params => {
    if (params.value != null) {
      this.setState({ reportType: this.state.reportType });
      if (
        this.state.reportType == 'Sales' ||
        this.state.reportType == 'Cost' ||
        this.state.reportType == 'elr' ||
        this.state.reportType == 'SA_lbrsale' ||
        this.state.reportType == 'SA_profit' ||
        this.state.reportType == 'SA_elr' ||
        this.state.reportType == 'SA_prtssale' ||
        this.state.reportType == 'SA_prtscost' ||
        this.state.reportType == 'SA_prtsprofit'
      ) {
        return params.value == 0
          ? ''
          : Math.sign(params.value) > -1
          ? '$' + Math.round(Number(params.value)).toLocaleString()
          : '-$' + Math.abs(Math.round(Number(params.value))).toLocaleString();
        // : '$' + parseFloat(params.value.toFixed(2)).toLocaleString();
      }
      if (this.state.reportType == 'grossprofit') {
        return params.value == 0
          ? ''
          : Math.round(Number(params.value)).toLocaleString();
        //: parseFloat(params.value.toFixed(1)).toLocaleString();
      } else if (
        this.state.reportType == 'markup' ||
        this.state.reportType == 'SA_markup'
      ) {
        return params.value == 0
          ? ''
          : parseFloat(params.value)
              .toFixed(4)
              .toString();
      } else {
        return params.value == 0
          ? ''
          : Math.round(params.value).toLocaleString();
        // : parseFloat(params.value.toFixed(2)).toLocaleString();
      }
    }
  };

  calculateAvg = params => {
    if (params.data != undefined) {
      let arr = [];
      arr = params.data;

      var result = Object.keys(arr).map(function(key) {
        return arr[key];
      });

      var resultArr = [];
      if (this.props.isAdvisorCharts) {
        //  resultArr = result.slice(1, 14);
        resultArr = result;
      }
      if (this.props.isTechnicianCharts) {
        //resultArr = result.slice(2, 14);
        resultArr = result;
      } else if (
        !this.props.isTechnicianCharts &&
        !this.props.isAdvisorCharts
      ) {
        resultArr = result.slice(8, 21);
      }

      var filteredArr = resultArr.filter(function(el) {
        return (
          el != 0 &&
          el != undefined &&
          typeof el != 'string' &&
          typeof el != 'object'
        );
      });

      var noNullArr = resultArr.filter(el => el != null);

      if (
        filteredArr.length != 0 &&
        this.state.reportType != 'markup' &&
        this.state.reportType != 'SA_markup'
      ) {
        var arrAvg =
          Math.round(
            (filteredArr.reduce((a, b) => parseFloat(a) + parseFloat(b), 0) /
              filteredArr.length +
              Number.EPSILON) *
              100
          ) / 100;
        arrAvg = Math.round(arrAvg);
      } else if (
        filteredArr.length != 0 &&
        (this.state.reportType == 'markup' ||
          this.state.reportType == 'SA_markup')
      ) {
        var arrAvg =
          ((filteredArr.reduce((a, b) => parseFloat(a) + parseFloat(b), 0) /
            filteredArr.length +
            Number.EPSILON) *
            100) /
          100;
        arrAvg = arrAvg.toFixed(4);
      } else {
        var arrAvg = 0;
      }

      return arrAvg;
    }
  };
  handleCellClicked = params => {
    window.sortStateWorkMix = this.gridApi.getSortModel();
    window.filterStateWorkMix = this.gridApi.getFilterModel();
    if (this.props.isParts) {
      window.colStatePrts = this.state.gridColumnApi.getColumnState();
    } else {
      window.colStateLbrReport = this.state.gridColumnApi.getColumnState();
    }

    let data;
    if (params.data) {
      if (
        params.value != 'COMPETITIVE' &&
        params.value != 'REPAIR' &&
        params.value != 'SHOP SUPPLIES' &&
        params.value != 'MAINTENANCE'
      ) {
        if (params.value != 'OTHER') {
          data = {
            type: 'reports',
            isFrom: 'workmix',
            opcode: params.value,
            reportType: this.props.reportType,
            isExpanded: this.state.expanded,
            originData: this.state.originData
          };
        } else {
          data = {
            type: 'comparisonchart',
            isFrom: 'reports',
            opcode: 'OTHER',
            opcategory: params.data.opcategory,
            isExpanded: this.state.expanded,
            workmixreportType: this.state.reportType
              ? this.state.reportType == 'Cost'
                ? 'partscost'
                : this.state.reportType
              : 'soldhours',
            originData: this.state.originData
          };
        }
        this.props.parentCallback(data);
      }
    }
  };
  handleCellClickedTechno = params => {
    window.sortStateTechMonthly = this.gridApi.getSortModel();
    window.colStateTechMonthly = this.state.gridColumnApi.getColumnState();
    window.filterStateTechMonthly = this.gridApi.getFilterModel();

    this.props.handleReportSelected();
    let techNo = params.value.split('[')[1];

    let data = {
      type: 'reports',
      isFrom: 'technician',
      opcode: techNo.split(']')[0],
      reportType: this.props.reportType,
      reportTabSelection: this.props.reportTabSelection,
      techName: params.value,
      payType: this.props.reportType == 'allsoldhrs' ? 'All' : 'C',
      pageType: 'technician'
    };

    this.props.parentCallback(data);
  };
  handleCellClickedSA = params => {
    window.sortStateAdvLbr = this.gridApi.getSortModel();
    window.colStateAdvLbr = this.state.gridColumnApi.getColumnState();
    window.filterStateAdvLbr = this.gridApi.getFilterModel();

    this.props.handleReportSelected();
    let data = {
      type: 'reports',
      isFrom: 'serviceadvisor',
      isReportSelected: false,
      opcode: params.value
        .split('[')[1]
        .toString()
        .split(']')[0],
      reportType: this.props.reportType,
      reportTabSelection: this.props.reportTabSelection
    };
    this.props.parentCallback(data);
  };
  getHeaderStyle = params => {
    return { background: 'red' };
  };
  getRowStyle = params => {
    if (params.node.allChildrenCount != null) {
      return { background: '#F3F1F0' };
    }
  };
  getCellStyle = params => {
    if (params.data != undefined) {
      let colorArr = [
        '#003d6b',
        '#054372',
        '#064677',
        '#0f5285',
        '#165a8e',
        '#1e6296',
        '#2772ac',
        '#347fb8',
        '#468cc1',
        '#72aeda',
        '#6baad8',
        '#88bce3',
        '#b5daf6'
      ];
      var bgColor = null;
      let dataArr = [];

      dataArr.push(
        params.data.mon24,
        params.data.mon25,
        params.data.mon26,
        params.data.mon27,
        params.data.mon28,
        params.data.mon29,
        params.data.mon30,
        params.data.mon31,
        params.data.mon32,
        params.data.mon33,
        params.data.mon34,
        params.data.mon35,
        params.data.mon36
      );
      var filtered = dataArr.filter(function(el) {
        return el != null;
      });

      filtered.sort((a, b) => b - a);

      if (params.value != 0) {
        var valueIndex = filtered.indexOf(params.value);

        bgColor = colorArr[valueIndex];
      } else {
        bgColor = null;
      }
    }
    return {
      'background-color': bgColor,
      color: '#fff',
      textAlign: 'right'
    };
  };
  onFirstDataRendered = params => {
    if (this.props.isFrom == '') {
      this.state.gridColumnApi.resetColumnState();
    }
  };
  handleReportsChange1 = () => {
    this.setState({ selected: true });
    this.setState({ resetReport: this.props.resetReport });
    // this.setState({ reportType: event.target.value }, value => {
    window.filterState = {};
    window.sortState = {};
    // this.state.gridApi.setColumnDefs([]);
    // this.state.gridApi.setColumnDefs(this.state.columnDefs);
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.state.gridColumnApi.resetColumnState();

    const groupColumn = this.state.gridApi.columnController.columnDefs;
    this.state.gridApi.columnController.columnApi.setColumnsVisible(
      ['serviceadvisor1'],
      false
    );
    this.state.gridApi.columnController.columnApi.setColumnsVisible(
      ['opcode'],
      true
    );

    if (
      this.state.reportType == 'techefficiency' ||
      this.state.reportType == 'flatratehrs' ||
      this.state.reportType == 'actualhrs' ||
      this.state.reportType == 'allsoldhrs'
    ) {
      groupColumn[0]['rowGroup'] = false;
      this.state.gridApi.setColumnDefs([]);
      this.state.gridApi.setColumnDefs(groupColumn);
      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['opcode', 'serviceadvisor'],
        false
      );
      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['lbrtechno'],
        true
      );
      //  groupColumn[18]['field'] = 'opcategory';
    } else if (this.state.reportType == 'serviceAdvisor') {
      this.state.gridApi.setColumnDefs([]);
      this.state.gridApi.setColumnDefs(this.state.columnsForServAdv);
    } else if (
      this.state.reportType == 'SA_lbrsale' ||
      this.state.reportType == 'SA_soldhours' ||
      this.state.reportType == 'SA_jobcount' ||
      this.state.reportType == 'SA_profit' ||
      this.state.reportType == 'SA_elr' ||
      this.state.reportType == 'SA_rocount' ||
      this.state.reportType == 'SA_prtssale' ||
      this.state.reportType == 'SA_prtscost' ||
      this.state.reportType == 'SA_prtsprofit' ||
      this.state.reportType == 'SA_markup'
    ) {
      groupColumn[0]['rowGroup'] = false;
      // groupColumn[18]['field'] = 'opcategory1';
      // this.state.gridApi.setColumnDefs([]);
      this.state.gridApi.setColumnDefs(groupColumn);

      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['opcode', 'lbrtechno', 'opcategory', 'opcategory1', 'allData'],
        false
      );
      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['serviceadvisor', 'serviceadvisor1'],
        true
      );
    } else {
      // groupColumn[1]['rowGroup'] = true;
      // groupColumn[18]['field'] = 'opcategory';
      this.state.gridApi.setColumnDefs([]);

      this.state.gridApi.setColumnDefs(groupColumn);
      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['opcode'],
        true
      );
      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['lbrtechno'],
        false
      );
    }

    var monthYear = this.getMonthyear();
    //var monthYear = monthYear.reverse();
    var monthnames = [
      'mon36',
      'mon35',
      'mon34',
      'mon33',
      'mon32',
      'mon31',
      'mon30',
      'mon29',
      'mon28',
      'mon27',
      'mon26',
      'mon25',
      'mon24'
    ];

    for (let index = 0; index < monthnames.length; index++) {
      const headerName = monthnames[index];
      var makeCol = this.state.gridApi.columnController.columnApi.getColumn(
        headerName
      );

      if (makeCol != null || makeCol.colDef != null) {
        makeCol.colDef.headerName = monthYear[index];
      }
      this.state.gridApi.refreshHeader();
    }
    this.state.gridApi.forEachNode(node => {
      if (this.state.expandedItems.includes(node.key)) {
        node.setExpanded(true);
      }
    });

    var defaultSortModel = [
      {
        colId: 'mon24'
      },
      {
        colId: 'mon25'
      },
      {
        colId: 'mon26'
      },
      {
        colId: 'mon27'
      },
      {
        colId: 'mon28'
      },
      {
        colId: 'mon29'
      },
      {
        colId: 'mon30'
      },
      {
        colId: 'mon31'
      },
      {
        colId: 'mon32'
      },
      {
        colId: 'mon33'
      },
      {
        colId: 'mon34'
      },
      {
        colId: 'mon35'
      },
      {
        colId: 'mon36'
      }
    ];
    this.state.gridApi.setSortModel(defaultSortModel);
    this.props.handleResetReport();
  };
  handleReportsChange = event => {
    this.setState({ selected: true });
    this.setState({ reportType: event }, value => {
      // this.setState({ reportType: event.target.value }, value => {
      this.state.gridApi.setColumnDefs([]);
      this.state.gridApi.setColumnDefs(this.state.columnDefs);

      const groupColumn = this.state.gridApi.columnController.columnDefs;
      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['serviceadvisor1'],
        false
      );
      this.state.gridApi.columnController.columnApi.setColumnsVisible(
        ['opcode'],
        false
      );

      if (
        this.state.reportType == 'techefficiency' ||
        this.state.reportType == 'flatratehrs' ||
        this.state.reportType == 'actualhrs' ||
        this.state.reportType == 'allsoldhrs'
      ) {
        groupColumn[0]['rowGroup'] = false;
        this.state.gridApi.setColumnDefs([]);
        this.state.gridApi.setColumnDefs(groupColumn);
        this.state.gridApi.columnController.columnApi.setColumnsVisible(
          ['opcode', 'serviceadvisor'],
          false
        );
        this.state.gridApi.columnController.columnApi.setColumnsVisible(
          ['lbrtechno'],
          true
        );
        // groupColumn[18]['field'] = 'opcategory';
      } else if (this.state.reportType == 'serviceAdvisor') {
        this.state.gridApi.setColumnDefs([]);
        this.state.gridApi.setColumnDefs(this.state.columnsForServAdv);
      } else if (
        this.state.reportType == 'SA_lbrsale' ||
        this.state.reportType == 'SA_soldhours' ||
        this.state.reportType == 'SA_jobcount' ||
        this.state.reportType == 'SA_profit' ||
        this.state.reportType == 'SA_elr' ||
        this.state.reportType == 'SA_rocount' ||
        this.state.reportType == 'SA_prtssale' ||
        this.state.reportType == 'SA_prtscost' ||
        this.state.reportType == 'SA_prtsprofit' ||
        this.state.reportType == 'SA_markup'
      ) {
        groupColumn[0]['rowGroup'] = false;
        // groupColumn[18]['field'] = 'opcategory1';
        this.state.gridApi.setColumnDefs([]);
        this.state.gridApi.setColumnDefs(groupColumn);

        this.state.gridApi.columnController.columnApi.setColumnsVisible(
          ['opcode', 'lbrtechno', 'opcategory', 'opcategory1'],
          false
        );
        this.state.gridApi.columnController.columnApi.setColumnsVisible(
          ['serviceadvisor', 'allData'],
          true
        );
      } else {
        // groupColumn[1]['rowGroup'] = true;
        // groupColumn[18]['field'] = 'opcategory';
        this.state.gridApi.setColumnDefs([]);
        this.state.gridApi.setColumnDefs(groupColumn);
        this.state.gridApi.columnController.columnApi.setColumnsVisible(
          ['opcode'],
          false
        );
        this.state.gridApi.columnController.columnApi.setColumnsVisible(
          ['lbrtechno'],
          false
        );
      }
      var monthYear = this.getMonthyear();
      // var monthYear = monthYear1.reverse();

      var monthnames = [
        'mon36',
        'mon35',
        'mon34',
        'mon33',
        'mon32',
        'mon31',
        'mon30',
        'mon29',
        'mon28',
        'mon27',
        'mon26',
        'mon25',
        'mon24'
      ];

      for (let index = 0; index < monthnames.length; index++) {
        const headerName = monthnames[index];
        var makeCol = this.state.gridApi.columnController.columnApi.getColumn(
          headerName
        );

        if (makeCol != null) {
          makeCol.colDef.headerName = monthYear[index];
        }
        this.state.gridApi.refreshHeader();
      }

      this.getAgGridData(this.state.reportType);
      var defaultSortModel = [
        {
          colId: 'mon24'
        },
        {
          colId: 'mon25'
        },
        {
          colId: 'mon26'
        },
        {
          colId: 'mon27'
        },
        {
          colId: 'mon28'
        },
        {
          colId: 'mon29'
        },
        {
          colId: 'mon30'
        },
        {
          colId: 'mon31'
        },
        {
          colId: 'mon32'
        },
        {
          colId: 'mon33'
        },
        {
          colId: 'mon34'
        },
        {
          colId: 'mon35'
        },
        {
          colId: 'mon36'
        }
      ];
      this.state.gridApi.setSortModel(defaultSortModel);
      // const emptyRow = {
      //   advisorName: '',
      //   serviceadvisor: '',
      //   mon1: '',
      //   mon2: '',
      //   mon3: '',
      //   mon27: '',
      //   mon28: '',
      //   mon29: '',
      //   mon30: '',
      //   mon31: '',
      //   mon32: '',
      //   mon33: '',
      //   mon34: '',
      //   mon35: '',
      //   mon36: '',
      //   alldata: { monthnames: [], values: [] }
      // };
      // this.state.gridApi.updateRowData({ add: [emptyRow], addIndex: 0 });
      // }
    });
  };
  getMonthyear = () => {
    var monthValues = getLast13Months();
    var yearMonth = [];
    var months = [
      'none',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    for (let index = 0; index < monthValues.length; index++) {
      var mon = monthValues[index];
      var res = mon.split('-');
      var month1 = res[1];
      month1 = month1.replace(/^0+/, '');

      yearMonth.push(months[month1] + " '" + res[0].slice(2));
    }
    yearMonth.reverse();
    this.setState({ yearMonthArr: yearMonth });

    return yearMonth;
  };
  onBtExport = () => {
    const columnsToExclude = ['allData', 'serviceadvisor1'];

    if (!this.state.gridApi || !this.state.gridColumnApi) {
      console.error('Grid API or Column API is not available!');
      return;
    }

    // Get all displayed columns
    const allColumnKeys = this.state.gridColumnApi
      .getAllDisplayedColumns()
      .map(column => column.getColId());

    // Exclude unwanted columns
    const columnKeys = allColumnKeys.filter(
      key => !columnsToExclude.includes(key)
    );

    // Ensure grouped columns are included
    const groupColumns = this.state.gridColumnApi
      .getColumnGroupState()
      .map(group => group.groupId)
      .filter(groupId => columnKeys.some(key => key.startsWith(groupId)));

    // Combine normal and grouped columns
    const exportColumns = [...new Set([...columnKeys, ...groupColumns])];

    console.log('Export Columns:', exportColumns);

    // Define export parameters
    const params = {
      sheetName: 'Report',
      fileName:
        this.props.isAdvisorCharts || this.props.isTechnicianCharts
          ? this.state.reportName
          : this.state.chartName,
      columnKeys: exportColumns,
      columnGroups: true, // Ensure group headers are included
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value:
                this.props.isAdvisorCharts || this.props.isTechnicianCharts
                  ? this.state.reportName
                  : this.state.chartName
            },
            mergeAcross: 6
          }
        ]
      ]
    };

    console.log('Export Params:', params);

    // Export the data
    this.state.gridApi.exportDataAsExcel(params);
  };
  // onBtExport = () => {
  //   const columnsToExclude = ['allData', 'serviceadvisor1'];

  //   var params = {
  //     sheetName: 'Report',
  //     fileName:
  //       this.props.isAdvisorCharts || this.props.isTechnicianCharts
  //         ? this.state.reportName
  //         : this.state.chartName,
  //     columnKeys: this.getColumnKeys(columnsToExclude),
  //     customHeader: [
  //       [],
  //       [
  //         {
  //           styleId: 'bigHeader',
  //           data: {
  //             type: 'String',
  //             value:
  //               this.props.isAdvisorCharts || this.props.isTechnicianCharts
  //                 ? this.state.reportName
  //                 : this.state.chartName
  //           },
  //           mergeAcross: 6
  //         }
  //       ]
  //     ]
  //   };

  //   this.state.rawGridApi.exportDataAsExcel(params);
  // };

  getColumnKeys = columnsToExclude => {
    const conditionForIncludingColumn = column =>
      !column.hide || column.field === 'opcategory'; // Ensure "Opcode category" is included
    console.log('Columns to Exclude:', columnsToExclude);
    // Get all column keys
    const allColumnKeys = this.getDefaultColumnDefs()
      .filter(conditionForIncludingColumn)
      .map(column => column.field);
    console.log('All Column Keys:', allColumnKeys);

    // Exclude the specified column keys
    const filteredColumnKeys = allColumnKeys.filter(
      columnKey => !columnsToExclude.includes(columnKey)
    );
    console.log('Filtered Column Keys:', filteredColumnKeys);

    return filteredColumnKeys;
  };

  sparklineRenderer = () => {
    var prefix,
      value = '';
    var type = '0';
    if (
      this.state.reportType == 'Sales' ||
      this.state.reportType == 'Cost' ||
      this.state.reportType == 'elr' ||
      this.state.reportType == 'SA_lbrsale' ||
      this.state.reportType == 'SA_profit' ||
      this.state.reportType == 'SA_elr' ||
      this.state.reportType == 'SA_prtssale'
    ) {
      prefix = '$';
      type = '0';
    } else if (
      this.state.reportType == 'grossprofit' ||
      this.state.reportType == 'workmix'
    ) {
      prefix = '%';
      type = '1';
    } else if (
      this.state.reportType == 'markup' ||
      this.state.reportType == 'SA_markup'
    ) {
      prefix = '';
      type = '2';
    } else {
      prefix = '';
      type = '0';
    }

    this.sparklineRenderer.prototype.init = function(params) {
      // create the cell
      let dataNode;

      if (typeof params.data != 'undefined') {
        dataNode = params.data.alldata.values;
      } else {
        dataNode = [];
      }
      dataNode = dataNode;
      dataNode = dataNode.map(function(val, i) {
        return val === null ? 0 : val;
      });
      var eGui = document.createElement('div');
      this.eGui = eGui;
      const formattedData = [];
      if (typeof params.data != 'undefined') {
        const allData = params.data.alldata;
        const { monthnames, values } = allData;
        // const  values1=values.reverse();
        // const monthnames1=monthnames.reverse();

        monthnames.map((xVal, i) =>
          formattedData.push({ mon: xVal, val: values[i] })
        );
      }

      setTimeout(() => {
        $(eGui).sparkline(dataNode, {
          type: 'line',
          width: '200px',
          lineColor: '#333333',

          fillColor: '#ffffff00',
          spotColor: '#ffffff00',
          minSpotColor: '#e60000',
          maxSpotColor: '#00b33c',
          spotRadius: 3,
          highlightSpotColor: '#000000',
          drawNormalOnTop: false,

          tooltipFormatter: function(sp, options, field) {
            const data = formattedData.filter((obj, index) => {
              // return obj.val == field.y;
              return index == field.x;
            });

            if (type == 0) {
              value = prefix + data[0].val.toLocaleString();
            } else if (type == 1) {
              value = data[0].val.toLocaleString() + prefix;
            } else if (type == 2) {
              value = data[0].val.toFixed(4).toLocaleString() + prefix;
            }
            return data[0].mon + ' - ' + value;
          }
        });
      }, 0);
      // }
    };
    this.sparklineRenderer.prototype.getGui = function() {
      return this.eGui;
    };
  };
  onRowClicked = params => {
    let title = params.node.key;
    let items = this.state.expandedItems;
    var rowNodeIndex = params.node.childIndex;
    this.setState({ expanded: rowNodeIndex });

    if (params.node.expanded == false) {
      items = items.filter(function(item) {
        return item !== title;
      });

      this.setState({ expandedItems: items });
    } else {
      items.unshift(title);
      this.setState({ expandedItems: items });
    }
  };
  onGridReady = params => {
    getLastThirteenMonths(result => {
      const startMonth = result[0];
      const endMonth = result[12];
      const datestr = endMonth.split('-');
      const month = datestr[1];
      const year = datestr[0];
      const date = new Date(year, month, 0).getDate();
      var expanded;

      let dateRange = [startMonth + '-01', endMonth + '-' + date];
      let dateRange12Months = [result[1] + '-01', endMonth + '-' + date];
      localStorage.setItem('12Months', dateRange12Months);
      localStorage.setItem('13Months', dateRange);
    });
    this.gridApi = params.api;
    params.api.closeToolPanel();
    // var monthValues = this.getLastThirteenMonthsValues();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });
    this.setState({ gridParams: params });
    this.setState({
      groupColumn: params.api.columnController.columnDefs
    });
    // if (this.props.history.location.state == undefined) {
    //   window.sortState = {};
    //   window.filterState = {};
    // }
    // this.getAllActiveTechniciansAndAdvisors();
    const groupColumn = this.state.groupColumn;
    if (window.colStateLbrReport && !this.props.isParts) {
      this.state.gridColumnApi.setColumnState(window.colStateLbrReport);
    }
    if (window.colStatePrts && this.props.isParts) {
      this.state.gridColumnApi.setColumnState(window.colStatePrts);
    }
    if (window.colStateAdvLbr) {
      this.state.gridColumnApi.setColumnState(window.colStateAdvLbr);
    }
    if (window.colStateTech) {
      this.state.gridColumnApi.setColumnState(window.colStateTech);
    }
    this.gridApi.setSortModel(
      this.state.isAdvisorCharts
        ? window.sortStateAdvLbr
        : this.state.isTechnicianCharts
        ? //? window.sortStateTech
          window.sortStateTechMonthly
        : this.props.isWorkMixRouting == true
        ? window.sortStateWorkMix
        : window.sortState
    );
    this.gridApi.setFilterModel(
      this.state.isAdvisorCharts
        ? window.filterStateAdvLbr
        : this.state.isTechnicianCharts
        ? window.filterStateTechMonthly
        : this.props.isWorkMixRouting == true
        ? window.filterStateWorkMix
        : window.filterState
    );
    //this.props.api.updateRowData({ add: [emptyRow], addIndex: 0 });

    // if (
    //   this.state.reportType == 'techefficiency' ||
    //   this.state.reportType == 'flatratehrs' ||
    //   this.state.reportType == 'actualhrs' ||
    //   this.state.reportType == 'allsoldhrs'
    // ) {
    //   const groupColumn = this.state.gridApi.columnController.columnDefs;
    //   groupColumn[0]['rowGroup'] = false;
    //   this.state.gridApi.setColumnDefs([]);
    //   this.state.gridApi.setColumnDefs(groupColumn);
    // }
    params.api.expandAll();

    if (
      this.props.reportType == 'techefficiency' ||
      this.props.reportType == 'flatratehrs' ||
      this.props.reportType == 'actualhrs' ||
      this.props.reportType == 'allsoldhrs'
    ) {
      groupColumn[3]['hide'] = true;
      groupColumn[0]['rowGroup'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      // if (window.sortStateTech && this.gridApi) {
      //   this.gridApi.setSortModel(window.sortStateTech);
      // }
      if (window.sortStateTechMonthly && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateTechMonthly);
      }
      if (window.colStateTech && this.state.gridColumnApi) {
        this.state.gridColumnApi.setColumnState(window.colStateTech);
      }
    } else if (
      this.props.reportType == 'SA_lbrsale' ||
      this.props.reportType == 'SA_soldhours' ||
      this.props.reportType == 'SA_jobcount' ||
      this.props.reportType == 'SA_profit' ||
      this.props.reportType == 'SA_elr' ||
      this.props.reportType == 'SA_rocount' ||
      this.props.reportType == 'SA_prtssale' ||
      this.props.reportType == 'SA_prtscost' ||
      this.props.reportType == 'SA_prtsprofit' ||
      this.props.reportType == 'SA_markup'
    ) {
      groupColumn[2]['hide'] = true;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateAdvLbr && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateAdvLbr);
      }
      if (window.colStateAdvLbr && this.state.gridColumnApi) {
        this.state.gridColumnApi.setColumnState(window.colStateAdvLbr);
      }
    } else {
      groupColumn[3]['hide'] = true;
      // groupColumn[0]['rowGroup'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);

      if (window.colStateLbrReport && !this.props.isParts) {
        this.state.gridColumnApi.setColumnState(window.colStateLbrReport);
      }
      if (window.colStatePrts && this.props.isParts) {
        this.state.gridColumnApi.setColumnState(window.colStatePrts);
      }
    }

    this.gridApi.setFilterModel(
      this.state.isAdvisorCharts
        ? window.filterStateAdvLbr
        : this.state.isTechnicianCharts
        ? window.filterStateTech
        : this.props.isWorkMixRouting == true
        ? window.filterStateWorkMix
        : window.filterState
    );
    var monthYear = this.getMonthyear();

    var monthnames = [
      'mon36',
      'mon35',
      'mon34',
      'mon33',
      'mon32',
      'mon31',
      'mon30',
      'mon29',
      'mon28',
      'mon27',
      'mon26',
      'mon25',
      'mon24'
    ];

    for (let index = 0; index < monthnames.length; index++) {
      const headerName = monthnames[index];
      var makeCol = params.columnApi.getColumn(headerName);
      makeCol.colDef.headerName = monthYear[index];

      params.api.refreshHeader();
    }
    var defaultSortModel = [
      {
        colId: 'mon24'
      },
      {
        colId: 'mon25'
      },
      {
        colId: 'mon26'
      },
      {
        colId: 'mon27'
      },
      {
        colId: 'mon28'
      },
      {
        colId: 'mon29'
      },
      {
        colId: 'mon30'
      },
      {
        colId: 'mon31'
      },
      {
        colId: 'mon32'
      },
      {
        colId: 'mon33'
      },
      {
        colId: 'mon34'
      },
      {
        colId: 'mon35'
      },
      {
        colId: 'mon36'
      }
    ];
    params.api.setSortModel(defaultSortModel);
    this.gridApi.setSortModel(
      this.state.isAdvisorCharts
        ? window.sortStateAdvLbr
        : this.state.isTechnicianCharts
        ? //? window.sortStateTech
          window.sortStateTechMonthly
        : this.props.isWorkMixRouting == true
        ? window.sortStateWorkMix
        : window.sortState
    );
  };

  onGroupExpandedOrCollapsed = params => {
    params.api.forEachNode(node => {
      node.expanded = true;
    });
  };
  onFirstDataRendered = params => {
    console.log('this.props.isFrom', this.props.isFrom);
    if (this.props.isFrom == 'opcodelevel') {
      setTimeout(() => {
        params.api.forEachNode((node, b) => {
          if (node.group == true && node.rowIndex == this.props.isExpanded) {
            node.setExpanded(true);
          }
        });
      }, 1000);
    } else {
      if (this.props.isFrom == 'workmixVolume') {
        setTimeout(() => {
          params.api.forEachNode((node, b) => {
            if (
              node.group == true &&
              node.rowIndex == this.props.history.location.state.isExpanded
            ) {
              node.setExpanded(true);
            }
          });
        }, 1000);
      }
    }
  };
  sumFunction = values => {
    var result = 0;

    if (
      this.state.reportType == 'grossprofit' ||
      this.state.reportType == 'elr' ||
      this.state.reportType == 'markup'
      // this.state.reportType == 'workmix'
    ) {
      var sum = 0;
      var count = 0;
      var opcatDataArray = this.state.opcatdata;
      var monthValues = getLast13Months();
      values.forEach(function(value) {
        var groupNode =
          value !== null && value !== undefined && typeof value === 'object';

        if (groupNode) {
          sum += value.avg * value.count;
          count += value.count;
        } else {
          if (typeof value === 'number') {
            sum += value;
            count++;
          }
        }
      });
      if (count !== 0) {
        var avg = sum / count;
      } else {
        avg = null;
      }
      result = avg;
    } else {
      values.forEach(function(value) {
        if (typeof value === 'number') {
          result += value;
        }
      });
    }
    return result;
  };
  onRowClickedGrp = params => {
    // Check if the clicked row is a group row
    if (params.node.group) {
      // Toggle the expansion state of the group row
      if (params.node.expanded) {
        params.node.setExpanded(false);
      } else {
        params.node.setExpanded(true);
      }
    }
  };
  resetRowData = () => {
    if (!lodash.isEmpty(this.state.gridApi)) {
      this.state.gridApi.setRowData(this.state.rowData);
      this.handleReportsChange();
    }
  };
  getDataForSparkLines(data) {
    var resArr = [];
    var intArray = this.parseArray(data);
    var monthYear = this.getMonthyear();

    intArray.map((item, index) => {
      resArr = [
        item.mon36,
        item.mon35,
        item.mon34,
        item.mon33,
        item.mon32,
        item.mon31,
        item.mon30,
        item.mon29,
        item.mon28,
        item.mon27,
        item.mon26,
        item.mon25,
        item.mon24
      ];
      intArray[index].alldata = {
        monthnames: monthYear,
        values: resArr
      };
    });

    return intArray;
  }

  /* getAllActiveTechniciansAndAdvisors() {
    if (this.state.isTechnicianCharts) {
      getAllTechnicians(result => {
        let resultArr = result.data.dmsPhysicalRwGetTblTechnicians.nodes;
        let filteredArr = resultArr.filter(item => item.active == 1);
        const filteredArrTech = [];
        filteredArr.map((item, index) => {
          filteredArrTech.push(item.lbrtechno);
        });
        this.setState({ activeTechs: filteredArrTech });
      });
    } else {
      getAllSADetails(result => {
        let resultArr = result.data.dmsPhysicalRwGetTblServiceAdvisors.nodes;
        let filteredArr = resultArr.filter(item => item.active == 1);
        const filteredArrAdvisors = [];
        filteredArr.map((item, index) => {
          filteredArrAdvisors.push(item.serviceadvisor);
        });
        this.setState({ activeAdvisors: filteredArrAdvisors });
      });
    }
  }*/

  parseArray(filtererdArray) {
    let intArray = [];

    var res = filtererdArray.map(v => {
      intArray.push({
        opcategory: v.opcategory,
        opcode: v.opcode,
        opcodedesc: v.opcodedesc,
        lbrtechno: v.lbrtechno,
        advisorName: v.advisorName,
        serviceadvisor: v.serviceadvisor,
        lbrtechname: v.lbrtechname,
        isActive: v.isActive ? v.isActive : 0,
        mon24: parseFloat(v['mon24']) || 0,
        mon25: parseFloat(v['mon25']) || 0,
        mon26: parseFloat(v['mon26']) || 0,
        mon27: parseFloat(v['mon27']) || 0,
        mon28: parseFloat(v['mon28']) || 0,
        mon29: parseFloat(v['mon29']) || 0,
        mon30: parseFloat(v['mon30']) || 0,
        mon31: parseFloat(v['mon31']) || 0,
        mon32: parseFloat(v['mon32']) || 0,
        mon33: parseFloat(v['mon33'] || 0),
        mon34: parseFloat(v['mon34'] || 0),
        mon35: parseFloat(v['mon35'] || 0),
        mon36: parseFloat(v['mon36'] || 0)
      });
      // return res;
    });
    return intArray;
  }

  getAgGridData(viewFor, advisor) {
    this.setState({ isLoading: true });
    this.setState({ rowData: null });
    if (advisor) {
      this.setState({ serviceAdvisors: advisor });
    }

    var name = null;
    var reportName = null;
    var type =
      this.state.reportType == 'grossprofit'
        ? 'Gross Profit %'
        : this.state.reportType == 'techefficiency'
        ? 'Tech Productivity'
        : this.state.reportType == 'allsoldhrs'
        ? 'Tech Sold Hours - All'
        : this.state.reportType == 'flatratehrs'
        ? 'Tech Sold Hours - CP'
        : this.state.reportType == 'actualhrs'
        ? 'Actual Hours'
        : this.state.reportType == 'markup'
        ? 'Markup'
        : this.state.reportType == 'workmix'
        ? 'Work Mix %'
        : this.state.reportType == 'elr'
        ? 'ELR'
        : this.state.reportType == 'Sales'
        ? 'Sales'
        : this.state.reportType == 'Cost'
        ? 'Cost'
        : this.state.reportType == 'soldhours'
        ? 'Sold Hours'
        : this.state.reportType == 'jobcount'
        ? 'Job Count'
        : this.state.reportType == 'serviceAdvisor'
        ? 'Service Advisor'
        : this.state.reportType == 'SA_lbrsale'
        ? 'Labor Sale'
        : this.state.reportType == 'SA_soldhours'
        ? 'Sold Hours'
        : this.state.reportType == 'SA_jobcount'
        ? 'Job Count'
        : this.state.reportType == 'SA_profit'
        ? 'Profit'
        : this.state.reportType == 'SA_elr'
        ? 'ELR'
        : this.state.reportType == 'SA_rocount'
        ? 'RO Count'
        : this.state.reportType == 'SA_prtssale'
        ? 'Parts Sale'
        : this.state.reportType == 'SA_prtscost'
        ? 'Parts Cost'
        : this.state.reportType == 'SA_prtsprofit'
        ? 'Parts Profit'
        : this.state.reportType == 'SA_markup'
        ? 'Parts Markup'
        : null;
    var subtotalLabel =
      this.state.reportType == 'grossprofit' ||
      this.state.reportType == 'elr' ||
      this.state.reportType == 'markup'
        ? 'Categories'
        : 'Categories';
    this.setState({ subtotalLabel: subtotalLabel });
    var dollarRequiredTypes =
      this.state.reportType == 'elr' ||
      this.state.reportType == 'Sales' ||
      this.state.reportType == 'Cost'
        ? '$'
        : '';
    this.setState({ dollarRequired: dollarRequiredTypes });
    if (this.props.isParts) {
      name = type + ' Report';
      if (this.state.reportType == 'serviceAdvisor') {
        getServiceAdvisorReport(result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport
              .statelessDbdPeopleMetricsServiceAdvisorDrillDownServiceAdvisorRevenueSummaryReports
          ) {
            this.setState({
              rowData:
                result.data
                  .statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport
                  .statelessDbdPeopleMetricsServiceAdvisorDrillDownServiceAdvisorRevenueSummaryReports
            });
            if (window.filterState != undefined) {
              this.filterByValue();
            }
          }
        });
      } else if (
        this.state.reportType == 'SA_lbrsale' ||
        this.state.reportType == 'SA_soldhours' ||
        this.state.reportType == 'SA_jobcount' ||
        this.state.reportType == 'SA_profit' ||
        this.state.reportType == 'SA_elr' ||
        this.state.reportType == 'SA_rocount' ||
        this.state.reportType == 'SA_prtssale' ||
        this.state.reportType == 'SA_prtscost' ||
        this.state.reportType == 'SA_prtsprofit' ||
        this.state.reportType == 'SA_markup'
      ) {
        name = type + ' Report';

        getServiceAdvisorRevenues(viewFor, result => {
          this.setState({ isLoading: false });
          let filteredArr = [];

          if (
            result.data.statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrend
              .statelessDbdPeopleMetricsServiceAdvisorRevenueTrends
          ) {
            var intArray = this.getDataForSparkLines(
              result.data.statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrend
                .statelessDbdPeopleMetricsServiceAdvisorRevenueTrends
            );

            if (
              this.props.session.serviceAdvisor != undefined &&
              this.props.session.serviceAdvisor.includes('All') == false
            ) {
              intArray.map(obj => {
                if (
                  // JSON.parse(this.props.session.serviceAdvisor)
                  //   .toString()
                  //   .indexOf(obj.serviceadvisor) != -1
                  this.props.session.serviceAdvisor.indexOf(
                    obj.serviceadvisor
                  ) != -1
                ) {
                  filteredArr.push(obj);
                }
              });
              this.setState({
                rowData: filteredArr
              });
            } else {
              this.setState({
                rowData: intArray
              });
            }
            if (window.filterStateAdvLbr != undefined) {
              this.filterByValue();
            }
          }
        });
      } else if (
        !this.props.isAdvisorCharts &&
        !this.props.isTechnicianCharts
      ) {
        getWorkMixReportForParts(viewFor, result => {
          this.setState({ isLoading: false, rowData: null });
          if (
            result.data.statelessDbdPartsWorkmixGetWorkmixTrendReportParts
              .statelessDbdPartsWorkmixWorkmixTrendReportParts
          ) {
            var resultData =
              result.data.statelessDbdPartsWorkmixGetWorkmixTrendReportParts
                .statelessDbdPartsWorkmixWorkmixTrendReportParts;
            // var filteredResult = resultData.filter(
            //   item => item.opcode == 'subtotal' || item.opcode == 'TOTALS'
            // );
            var filteredResult = resultData.filter(
              item =>
                (item.opcode == 'subtotal' || item.opcode == 'TOTALS') &&
                item.opcategory !== 'N/A'
            );

            var filteredResultWithoutSubtotal = resultData.filter(
              item => item.opcode != 'subtotal' && item.opcode != 'TOTALS'
            );
            filteredResultWithoutSubtotal = lodash.sortBy(
              filteredResultWithoutSubtotal,
              'opcategory'
            );
            var filteredResultWithoutSubtotal = resultData.filter(
              item => item.opcode != 'subtotal' && item.opcode != 'TOTALS'
            );
            filteredResultWithoutSubtotal = lodash.filter(
              filteredResultWithoutSubtotal,
              function(item) {
                return item.opcategory !== 'N/A';
              }
            );

            var intArray = this.getDataForSparkLines(
              filteredResultWithoutSubtotal
            );

            this.setState({ opcatSum: filteredResult });

            this.setState({
              rowData: intArray
            });
            if (
              window.filterStateWorkMix != undefined ||
              window.filterStateTechMonthly != undefined
            ) {
              this.filterByValue();
            }
            if (window.sortStateWorkMix) {
              this.gridApi.setSortModel(window.sortStateWorkMix);
            }
            // if (window.filterStateTech != undefined) {
            //   this.filterByValue();
            // }
          }
        });
      }
    } else {
      name = type + ' Report';

      // if (this.state.reportType == 'serviceAdvisor') {
      //   getServiceAdvisorReport(result => {
      //     this.setState({ isLoading: false });
      //     if (
      //       result.data
      //         .statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport
      //         .nodes
      //     ) {
      //       this.setState({
      //         rowData:
      //           result.data
      //             .statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport
      //             .nodes
      //       });
      //     }
      //   });
      // } else
      if (
        this.state.reportType == 'SA_lbrsale' ||
        this.state.reportType == 'SA_soldhours' ||
        this.state.reportType == 'SA_jobcount' ||
        this.state.reportType == 'SA_profit' ||
        this.state.reportType == 'SA_elr' ||
        this.state.reportType == 'SA_rocount' ||
        this.state.reportType == 'SA_prtssale' ||
        this.state.reportType == 'SA_prtscost' ||
        this.state.reportType == 'SA_prtsprofit' ||
        this.state.reportType == 'SA_markup'
      ) {
        getServiceAdvisorRevenues(viewFor, result => {
          this.setState({ isLoading: false });
          let filteredArr = [];
          if (
            result.data.statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrend
              .statelessDbdPeopleMetricsServiceAdvisorRevenueTrends
          ) {
            var intArray = this.getDataForSparkLines(
              result.data.statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrend
                .statelessDbdPeopleMetricsServiceAdvisorRevenueTrends
            );
            // var intArray = this.parseArray(
            //   result.data.statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrend
            //     .nodes
            // );

            if (
              this.props.session.serviceAdvisor != undefined &&
              this.props.session.serviceAdvisor.includes('All') == false
            ) {
              intArray.map(obj => {
                if (
                  this.props.session.serviceAdvisor.indexOf(
                    obj.serviceadvisor
                  ) != -1
                ) {
                  filteredArr.push(obj);
                }
              });
              this.setState({
                rowData: filteredArr
              });
            } else {
              this.setState({
                rowData: intArray
              });
            }

            if (window.filterState != undefined) {
              this.filterByValue();
            }
          }
        });
      } else if (
        !this.props.isAdvisorCharts ||
        !this.props.isTechnicianCharts
      ) {
        getWorkMixReportForLabor(viewFor, result => {
          this.setState({ isLoading: false });
          var resArr = [];
          if (
            result.data.statelessDbdLaborWorkmixGetWorkmixTrendReportLabor
              .statelessDbdLaborWorkmixTrendReportLabors
          ) {
            var resultData =
              result.data.statelessDbdLaborWorkmixGetWorkmixTrendReportLabor
                .statelessDbdLaborWorkmixTrendReportLabors;

            var filteredResult = resultData.filter(
              item =>
                (item.opcode == 'subtotal' || item.opcode == 'TOTALS') &&
                item.opcategory !== 'N/A'
            );

            var filteredResultWithoutSubtotal = resultData.filter(
              item =>
                item.opcode != 'subtotal' &&
                item.opcode != 'TOTALS' &&
                ![
                  item.mon24,
                  item.mon25,
                  item.mon26,
                  item.mon27,
                  item.mon28,
                  item.mon29,
                  item.mon30,
                  item.mon31,
                  item.mon32,
                  item.mon33,
                  item.mon34,
                  item.mon35,
                  item.mon36
                ].every(value => value == null)
            );
            filteredResultWithoutSubtotal = lodash.filter(
              filteredResultWithoutSubtotal,
              function(item) {
                return item.opcategory !== 'N/A';
              }
            );
            filteredResultWithoutSubtotal = lodash.sortBy(
              filteredResultWithoutSubtotal,
              'opcategory'
            );
            var intArray = this.getDataForSparkLines(
              filteredResultWithoutSubtotal
            );

            var arrayWithDollar = [];

            this.setState({ opcatSum: filteredResult });
            this.setState({
              rowData: intArray
            });
            if (window.filterStateWorkMix != undefined) {
              this.filterByValue();
            }
            if (window.sortStateWorkMix) {
              this.gridApi.setSortModel(window.sortStateWorkMix);
            }
          }
        });
      }
    }
    if (this.props.isEfficiencyCharts) {
      if (this.props.isAdvisorCharts) {
        name = type + ' Report';
        reportName = type + ' Report';
      } else if (this.props.isWorkMixRouting) {
      } else {
        getWorkMixReportForTechEfficiency(viewFor, result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessDbdPeopleMetricsTechnicianGetTrendReportTechnicians
              .statelessDbdPeopleMetricsTechnicianTrendReportTechnicians
          ) {
            var resultData =
              result.data
                .statelessDbdPeopleMetricsTechnicianGetTrendReportTechnicians
                .statelessDbdPeopleMetricsTechnicianTrendReportTechnicians;
            var filteredResultWithoutSubtotal = resultData.filter(
              item =>
                (item.lbrtechno != null &&
                  item.lbrtechname != null &&
                  item.mon24 != null &&
                  item.mon24 != '0') ||
                (item.mon25 != null && item.mon25 != '0') ||
                (item.mon26 != null && item.mon26 != '0') ||
                (item.mon27 != null && item.mon27 != '0') ||
                (item.mon28 != null && item.mon28 != '0') ||
                (item.mon29 != null && item.mon29 != '0') ||
                (item.mon30 != null && item.mon30 != '0') ||
                (item.mon31 != null && item.mon31 != '0') ||
                (item.mon32 != null && item.mon32 != '0') ||
                (item.mon33 != null && item.mon33 != '0') ||
                (item.mon34 != null && item.mon34 != '0') ||
                (item.mon35 != null && item.mon35 != '0') ||
                (item.mon36 != null && item.mon36 != '0')
            );

            var intArray = this.getDataForSparkLines(
              filteredResultWithoutSubtotal
            );

            this.setState({
              rowData: intArray
            });
            if (window.filterStateTechMonthly != undefined) {
              this.filterByValue();
            }
          }
        });
        name = type + ' Report';
        reportName = type + ' Report';
      }
    }
    this.setState({ chartName: name });
    this.setState({ reportName: reportName });
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(
      this.state.isAdvisorCharts
        ? window.filterStateAdvLbr
        : this.state.isTechnicianCharts
        ? window.filterStateTechMonthly
        : this.props.isWorkMixRouting == true
        ? window.filterStateWorkMix
        : window.filterState
    );
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  renderBackButton = () => {
    {
      let data = {};
      //if (this.props.isFrom == 'workmixcharts') {
      data = {
        tabSelection:
          this.props.reportTabSelection &&
          this.props.reportTabSelection != 'three'
            ? this.props.reportTabSelection
            : this.props.isTechnicianCharts
            ? 'seven'
            : this.props.isAdvisorCharts
            ? 'eight'
            : 'one',
        isFrom: 'drillDown-workmix',
        isReportSelected: false,
        reportType:
          this.props.isFrom == 'opcodelevel' ||
          this.props.isFrom == 'workmix' ||
          this.props.isFrom == 'workmixcharts'
            ? this.props.selectedReportType
              ? this.props.selectedReportType
              : this.props.reportType
            : this.state.reportType
      };
      // }

      this.props.parentCallback(data);
    }
  };

  onSelectionChanged = event => {
    this.setState({ selectedRowData: event.api.getSelectedRows() });
  };
  onRowSelected = e => {
    this.setState({ rowChecked: e.node.selected });
  };
  handleRowSelection = () => {
    if (this.state.selectedRowData.length > 0) {
      this.gridApi.updateRowData({
        remove: this.state.selectedRowData
      });
      this.setState({ previousSelectedRows: this.state.selectedRowData });
    }
  };

  handleResetRowSelection = () => {
    this.gridApi.updateRowData({
      add: this.state.previousSelectedRows
    });
    this.setState({ previousSelectedRows: [] });
  };

  render() {
    console.log('window===', window);
    let selectedReportType = this.props.selectedReportType
      ? this.props.selectedReportType
      : this.props.history.location.state &&
        this.props.history.location.state.selectedReportType
      ? this.props.history.location.state.selectedReportType
      : '';
    if (
      selectedReportType &&
      document.getElementById(selectedReportType + '_report')
    ) {
      document.getElementById(selectedReportType + '_report').style.background =
        Dealer == 'Armatus' ? '#ddeaf4' : '#F4E1E7';
      document.getElementById(selectedReportType + '_report').style.color =
        Dealer == 'Armatus' ? '#003d6b' : '#C2185B';

      // document.getElementById(selectedReportType+'_report').style.color= (Dealer == 'Armatus') ? '#003d6b' : '#C2185B' ;
    } else if (
      this.props.reportType &&
      document.getElementById(this.props.reportType + '_report')
    ) {
      document.getElementById(
        this.props.reportType + '_report'
      ).style.background = Dealer == 'Armatus' ? '#ddeaf4' : '#F4E1E7';
      document.getElementById(this.props.reportType + '_report').style.color =
        Dealer == 'Armatus' ? '#003d6b' : '#C2185B';
    }
    if (this.props.exportReport) {
      this.onBtExport();
      this.props.handleExportReport();
    }
    const { classes } = this.props;
    // $('.ag-header-select-all.ag-labeled.ag-label-align-right.ag-checkbox.ag-input-field').attr('id','reportsHeader')

    return (
      <div className={this.state.isLoading != true ? 'containerMain' : ''}>
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div className="containersub">
          <Grid container spacing={12}>
            {this.state.selected &&
            !this.props.isTechnicianCharts &&
            !this.props.isAdvisorCharts ? (
              <TableContainer
                component={Paper}
                style={{
                  margin: 8,
                  padding: 1,
                  display: this.state.isLoading ? 'none' : 'block',
                  width: '82.5%'
                }}
              >
                <Table
                  className="sales-table"
                  // style={{ minWidth: 300 }}
                  size="small"
                  aria-label="a dense table"
                >
                  <TableHead
                    style={{
                      textAlign: 'center',
                      backgroundColor: '#003d6b'
                    }}
                  >
                    <TableRow size="small">
                      <TableCell
                        align="center"
                        size="small"
                        style={{
                          fontSize: 10,
                          color: Dealer == 'Armatus' ? '#ffffff' : '#C2185B',
                          lineHeight: '15px !important',
                          textAlign: 'center'
                        }}
                      >
                        {this.state.subtotalLabel}
                      </TableCell>
                      {this.state.yearMonthArr.map((item, index) => (
                        <TableCell
                          align="center"
                          size="small"
                          style={{
                            fontSize: 10,
                            color: Dealer == 'Armatus' ? '#ffffff' : '#C2185B'
                            // width: '20px',
                          }}
                        >
                          {item}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody
                    className={
                      this.state.reportType == 'Sales' ||
                      this.state.reportType == 'Cost' ||
                      this.state.reportType == 'soldhours' ||
                      this.state.reportType == 'jobcount'
                        ? 'tblbody'
                        : ''
                    }
                    size="small"
                  >
                    {this.state.opcatSum.map((row, index) => (
                      <TableRow size="small">
                        <TableCell
                          align="center"
                          size="small"
                          style={{
                            fontSize: 10,
                            fontWeight: 'bold',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {row.opcategory}
                        </TableCell>

                        <TableCell
                          align="right"
                          style={{
                            fontSize: 10,
                            whiteSpace: 'nowrap',
                            width: '50'
                          }}
                        >
                          {this.state.dollarRequired}
                          {row.mon36
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon36).toFixed(4)
                              : Math.round(row.mon36).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon35
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon35).toFixed(4)
                              : Math.round(row.mon35).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon34
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon34).toFixed(4)
                              : Math.round(row.mon34).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon33
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon33).toFixed(4)
                              : Math.round(row.mon33).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon32
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon32).toFixed(4)
                              : Math.round(row.mon32).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon31
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon31).toFixed(4)
                              : Math.round(row.mon31).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon30
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon30).toFixed(4)
                              : Math.round(row.mon30).toLocaleString()
                            : 0}
                        </TableCell>

                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon29
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon29).toFixed(4)
                              : Math.round(row.mon29).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon28
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon28).toFixed(4)
                              : Math.round(row.mon28).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon27
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon27).toFixed(4)
                              : Math.round(row.mon27).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          align="center"
                          style={{ fontSize: 10, whiteSpace: 'nowrap' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon26
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon26).toFixed(4)
                              : Math.round(row.mon26).toLocaleString()
                            : 0}
                        </TableCell>
                        <TableCell
                          height="5"
                          size="small"
                          align="center"
                          style={{ fontSize: 10 }}
                        >
                          {this.state.dollarRequired}
                          {row.mon25
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon25).toFixed(4)
                              : Math.round(row.mon25).toLocaleString()
                            : 0}
                        </TableCell>

                        <TableCell
                          align="center"
                          size="small"
                          height="5"
                          style={{ fontSize: 10, width: '10' }}
                        >
                          {this.state.dollarRequired}
                          {row.mon24
                            ? this.state.reportType == 'markup'
                              ? Number(row.mon24).toFixed(4)
                              : Math.round(row.mon24).toLocaleString()
                            : 0}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : null}
          </Grid>
          <div
            id="data-tab-reports"
            className="ag-theme-balham"
            style={{
              height:
                this.state.isAdvisorCharts || this.state.isTechnicianCharts
                  ? window.innerHeight - 300 + 'px'
                  : window.innerHeight - 400 + 'px',
              width: '1915px',
              margin: 8,
              display: this.state.isLoading ? 'none' : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '410px',
                width: '100%'
              }}
              // enableRangeSelection={true} 410
              suppressHorizontalScroll={true}
              suppressDragLeaveHidesColumns={true}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              // getChartToolbarItems={this.getChartToolbarItems}
              animateRows={true}
              //  enableCharts={true}
              headerHeight={this.state.headerHeight}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              enableRangeSelection={true}
              onGridReady={this.onGridReady}
              onRowGroupOpened={this.onRowClicked}
              groupDefaultExpanded={0}
              suppressAggFuncInHeader={true}
              enableSorting={true}
              rowData={this.state.rowData}
              sortingOrder={this.sortingOrder}
              groupMultiAutoColumn={true}
              getRowStyle={this.getRowStyle}
              excelStyles={this.state.excelStyles}
              frameworkComponents={this.state.frameworkComponents}
              postSort={this.state.postSort}
              rowSelection={this.state.rowSelection}
              onSelectionChanged={this.onSelectionChanged}
              suppressRowClickSelection={this.state.suppressRowClickSelection}
              rowStyle={this.state.rowStyle}
              onRowSelected={this.onRowSelected.bind(this)}
              context={this.state.context}
              groupSelectsChildren={true}
              suppressCellSelection={true}
              floatingFilter={true}
              enableCharts={true}
              tooltipShowDelay={0}
              onRowClicked={this.onRowClickedGrp}
              onFirstDataRendered={this.onFirstDataRendered}
              suppressContextMenu={true}
            />
          </div>
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1)
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  dividerRoot: {
    backgroundColor: '#4a4646',
    width: '100%'
  }
});
export default withStyles(styles)(withRouter(Reports));
