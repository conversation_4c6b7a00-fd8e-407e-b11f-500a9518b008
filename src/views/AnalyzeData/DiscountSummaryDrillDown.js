import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'src/grid.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button
} from '@material-ui/core';
import { AgGridReact } from '@ag-grid-community/react';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Link from '@material-ui/core/Link';
import {
  getDrillDownDataForLaborDiscount,
  getDrillDownDataForPartsDiscount
} from 'src/utils/hasuraServices';
import { renderBackButtonForDrillDown } from 'src/components/DrillDownCalculations';
import 'src/grid.css';
import { withStyles } from '@material-ui/styles';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { MasterDetailModule } from '@ag-grid-enterprise/master-detail';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getLast13Months } from 'src/utils/Utils';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import RestoreIcon from '@material-ui/icons/Restore';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import clsx from 'clsx';
import { ReactSession } from 'react-client-session';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
var lodash = require('lodash');

class DiscountSummaryDrillDown extends React.Component {
  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        const dateFormat = item => moment(item).format('YYYY-MM');
        var initialQueryMonth = dateFormat(this.props.month_year);
        var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
          ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
          : this.props.serviceAdvisor;
        this.getAgGridData(
          initialQueryMonth,
          initialServiceAdvisor,
          this.props.type
        );
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = dateFormat(this.props.month_year);
    var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
      ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
      : this.props.serviceAdvisor;
    var location =
      getVerificationDashboardBaseURL() + '/FOC3_Searchbyro/ag-grid.html';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      previousLocation: this.props.history.location.prevPath
        ? this.props.history.location.prevPath
        : this.props.history.location.state
        ? this.props.history.location.state.prevPath
        : '/Discounts',
      queryMonth: initialQueryMonth,
      selectedMonthYear: initialQueryMonth,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: initialServiceAdvisor,
      isLoading: true,
      rawGridApi: {},
      gridApi: {},
      tabSelection: this.props.type == 'discounted_parts_drilldown' ? 1 : 0,
      headerHeight: 45,
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      columnDefsDisSummaryLbr: [
        {
          headerName: 'RO',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          // filter: "agNumberColumnFilter",
          field: 'ronumber',
          width: 88,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          // cellRendererParams: {
          //   innerRenderer: function (params) {
          //     return `<a style="color: #000000" href="${location}?ronumber=${params.value}&store=${storeId}" target="_blank"  >${params.value}</a>`
          //   },

          // },
          // cellRendererFramework:TooltipRenderer,
          onCellClicked: this.handleSearchByRo,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 95,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: "agNumberColumnFilter",
          width: 145,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          // filter: "agTextColumnFilter",
          width: 75,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          // filter: "agTextColumnFilter",
          width: 68,
          chartDataType: 'category',
          hide: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 90,
          // filter: "agTextColumnFilter",
          dataType: 'string',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: "agTextColumnFilter",
          width: 80,
          chartDataType: 'category',
          hide: true,
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          resizable: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          // filter: "agTextColumnFilter",
          dataType: 'string',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          // filter: "agNumberColumnFilter",
          width: 100,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          width: 100,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          hide: true,
          minWidth: 100,
          resizable: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Discount Level',
          field: 'dislevel',
          // filter: "agTextColumnFilter",
          width: 97,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          // filter: "agNumberColumnFilter",
          width: 58,
          chartDataType: 'category',
          hide: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          // filter: "agNumberColumnFilter",
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          // filter: "agNumberColumnFilter",
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Discount',
          field: 'labordiscount',
          // filter: "agNumberColumnFilter",
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          cellClass: 'twoDecimalPlacesWith$',
          hide: true,
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Sale %',
          field: 'salepercentage',
          // filter: "agNumberColumnFilter",
          valueFormatter: this.formatCellValuePercentage,
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
          // valueFormatter: this.formatCellValueGP
        },
        {
          headerName: 'Apportioned Labor Discount',
          field: 'apportionedlbrdiscount',
          // filter: "agNumberColumnFilter",
          width: 124,
          minWidth: 124,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueDiscount,
          cellClass: 'twoDecimalPlacesDiscount',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        }
      ],
      columnDefsDisSummaryPrts: [
        {
          headerName: 'RO',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          colId: 'ronumber_1',
          // filter: "agNumberColumnFilter",
          field: 'ronumber',
          width: 83,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          // cellRendererParams: {
          //   innerRenderer: function (params) {
          //     return `<a style="color: #000000" href="${location}?ronumber=${params.value}&store=${storeId}" target="_blank"  >${params.value}</a>`
          //   },

          // },
          //cellRendererFramework:TooltipRenderer,
          tooltip: params => 'View RO',
          onCellClicked: this.handleSearchByRo,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          colId: 'monthYear_1',
          width: 95,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          colId: 'advisorName_1',
          // filter: "agNumberColumnFilter",
          width: 145,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Pay Type',
          field: 'laborpaytype',
          // filter: "agTextColumnFilter",
          width: 66,
          chartDataType: 'category',
          hide: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'laborpaytypegroup',
          width: 90,
          // filter: "agTextColumnFilter",
          dataType: 'string',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          colId: 'lbropcode_1',
          // filter: "agTextColumnFilter",
          width: 80,
          chartDataType: 'category',
          hide: true,
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          colId: 'lbropcodedesc_1',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          resizable: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          colId: 'opcategory_1',
          width: 110,
          // filter: "agTextColumnFilter",
          dataType: 'string',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          colId: 'disdiscountid_1',
          // filter: "agNumberColumnFilter",
          width: 100,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          colId: 'disdesc_1',
          tooltipField: 'disdesc',
          width: 100,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          hide: true,
          minWidth: 100,
          resizable: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts Code',
          field: 'prtlinecode',
          // filter: "agTextColumnFilter",
          width: 77,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Discount Level',
          field: 'dislevel',
          colId: 'dislevel_1',
          // filter: "agTextColumnFilter",
          width: 97,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Seq No.',
          field: 'prtlaborsequenceno',
          // filter: "agNumberColumnFilter",
          width: 65,
          chartDataType: 'category',
          hide: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          // filter: "agNumberColumnFilter",
          width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Parts Cost',
          field: 'prtcost',
          // filter: "agNumberColumnFilter",
          width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Parts Discount',
          field: 'partsdiscount',
          // filter: "agNumberColumnFilter",
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          hide: true,
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Sale %',
          field: 'salepercentage',
          colId: 'salepercentage_1',
          // filter: "agNumberColumnFilter",
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          unSortIcon: true,
          suppressMenu: true,
          valueFormatter: this.formatCellValuePercentage,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
          //  valueFormatter: this.formatCellValueGP
        },
        {
          headerName: 'Apportioned Parts Discount',
          field: 'apportionedlbrdiscount',
          colId: 'apportionedlbrdiscount_1',
          // filter: "agNumberColumnFilter",
          width: 124,
          minWidth: 124,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueDiscount,
          cellClass: 'twoDecimalPlacesDiscount',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        }
      ],

      autoGroupColumnDef: { minWidth: 200 },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      rowData: [],
      defaultColDef: {
        filter: true,
        filterParams: { newRowsAction: 'keep' },
        enableValue: true,
        sortable: true,
        resizable: false,
        enablePivot: false,
        floatingFilter: true
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  // formatCellValue = params => {
  //   if (params.value != null && params.value != 0) {
  //     return (
  //       '$' +
  //       (
  //         Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
  //       ).toLocaleString()
  //     );
  //   }
  // };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      const roundedValue =
        Math.round((Number(params.value) + Number.EPSILON) * 100) / 100;
      const formattedValue = Math.abs(roundedValue).toLocaleString();

      if (roundedValue < 0) {
        return `-$${formattedValue}`;
      } else {
        return `$${formattedValue}`;
      }
    }
    return null;
  };

  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        Math.abs(
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
      );
    }
  };
  formatCellValuePercentage = params => {
    const value = params.value ?? 0;
    return value + '%';
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };

  onGridReady = params => {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridcolumnApi: params.columnApi });
    this.gridApi.setSortModel(window.sortState);
    this.gridApi.setFilterModel(window.filterState);
    this.getAgGridData(
      this.state.queryMonth,
      this.state.serviceAdvisor,
      this.props.type
    );
  };
  getAgGridData(queryMonth, serviceAdvisor, type) {
    if (type == 'discount_drilldown') {
      this.setState({ isLoading: true });
      getDrillDownDataForLaborDiscount(queryMonth, result => {
        this.setState({ isLoading: false });
        if (
          result.data.statelessCcDrilldownGetDrillDownDiscountedLaborDetails
            .nodes
        ) {
          var resultArr =
            result.data.statelessCcDrilldownGetDrillDownDiscountedLaborDetails
              .nodes;
          this.setState({
            rowData: resultArr
          });
        }
        if (window.filterState != undefined) {
          this.filterByValues();
        }
      });
    } else {
      this.setState({ isLoading: true });
      this.setState({
        rowData: []
      });
      getDrillDownDataForPartsDiscount(queryMonth, result => {
        this.setState({ isLoading: false });
        if (
          result.data.statelessCcDrilldownGetDrillDownDiscountedPartsDetails
            .nodes
        ) {
          var resultArr =
            result.data.statelessCcDrilldownGetDrillDownDiscountedPartsDetails
              .nodes;
          this.setState({
            rowData: resultArr
          });
        }
        if (window.filterState != undefined) {
          this.filterByValues();
        }
      });
    }
  }

  filterByValues = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterState);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  setDrillDownValuesToState = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      LsWarranty: Values.LsWarranty
    });
  };

  handleTabChange = (event, newValue) => {
    window.sortState = this.gridApi.getSortModel();
    window.filterState = this.gridApi.getFilterModel();
    this.setState({ tabSelection: newValue });
    this.setState({ category: newValue });
    let type = '';
    if (newValue == 0) {
      type = 'discount_drilldown';
    } else {
      type = 'discounted_parts_drilldown';
    }
    this.setState({
      month_year: this.state.queryMonth,
      chartId: this.props.chartId,
      type: this.props.type
    });
    this.getAgGridData(this.state.queryMonth, this.state.serviceAdvisor, type);
    let data = {
      month_year: this.state.queryMonth,
      type: type,
      chartId: this.props.chartId
    };
    this.props.parentCallback(data);
  };
  getMonthYear = () => {
    var table = [];
    this.setState({
      discountMonthYear: getLast13Months()
    });
  };
  renderBackButton = () => {
    {
      let chartId = this.props.chartId
        ? this.props.chartId
        : this.props.history.location.search.split('=')[1];
      let data = {
        month_year: this.state.queryMonth,
        type: '',
        //chartId: this.props.chartId ? this.props.chartId : this.props.history.location.search.split('=')[1],
        chartId: this.props.chartId,
        history: this.props.history,
        prevPath: this.state.previousLocation,
        drillDown:
          chartId == 1111
            ? 41
            : chartId == 1115
            ? 42
            : chartId == 1232
            ? 45
            : chartId == 1165
            ? 44
            : chartId == 1234
            ? 30
            : 43,
        category: this.props.category
          ? this.props.category
          : this.props.history.location.state.category
      };
      this.props.parentCallback(data);
    }
  };
  handleSearchByRo = params => {
    window.sortState = this.gridApi.getSortModel();
    window.filterState = this.gridApi.getFilterModel();
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        parentCallback: this.props.callbackFunction,
        month_year: this.state.queryMonth,
        serviceAdvisor: 'All',
        type: this.props.type,
        chartId: this.props.chartId
          ? this.props.chartId
          : this.props.history.location.search.split('?chartId=').pop(),
        drillDown: this.props.drillDown,
        pageType: 'Discounts',
        prevPath: this.state.previousLocation,
        tab: this.state.tabSelection
      }
    });
  };
  resetReportGrid = () => {
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        <Paper square style={{ margin: 8, paddingTop: '6px', height: '40px' }}>
          <Grid
            container
            className={clsx(this.props.titleContainer, 'reset-dashboard')}
          >
            {/* Back Button */}
            <Grid item xs={4} style={{ display: 'flex', alignItems: 'center' }}>
              <Button
                variant="contained"
                className="bck-btn"
                onClick={this.renderBackButton}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button>
            </Grid>

            {/* Title */}
            <Grid
              item
              xs={4}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <Typography
                variant="h4"
                color="primary"
                className={clsx(this.props.mainLabel, 'main-title')}
              >
                Discount Summary
              </Typography>
            </Grid>

            {/* Reset Layout Button */}
            <Grid
              item
              xs={4}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'flex-end',
                paddingRight: '8px'
              }}
            >
              <Button
                variant="contained"
                id="reset-layout"
                className="reset-btn"
                onClick={this.resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </Grid>
          </Grid>
        </Paper>
        {this.state.isLoading && (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}

        <Paper
          square
          style={{ margin: 8, backgroundColor: '#FFF', marginTop: '20px' }}
        >
          <Tabs
            variant="standard"
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            indicatorColor="secondary"
            textColor="secondary"
          >
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>Labor</div>}
              value={0}
            />
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>Parts</div>}
              value={1}
            />
          </Tabs>
        </Paper>

        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              (this.state.rowData.length > 0
                ? window.innerHeight - 230
                : window.innerHeight - 160) + 'px',
            width: '100%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            floatingFilter={true}
            suppressRowClickSelection={true}
            enableRangeSelection={true}
            headerHeight={this.state.headerHeight}
            sideBar={this.state.sideBar}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={
              this.props.type == 'discount_drilldown'
                ? this.state.columnDefsDisSummaryLbr
                : this.state.columnDefsDisSummaryPrts
            }
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  }
});

export default withStyles(styles)(DiscountSummaryDrillDown);
