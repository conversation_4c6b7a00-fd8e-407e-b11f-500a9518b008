import {
  Grid,
  Paper,
  FormControl,
  MenuItem,
  Select,
  InputLabel
} from '@material-ui/core';
import React, { useState, useEffect } from 'react';
import ColumnRenderer from './ColumnRenderer';
import { makeStyles } from '@material-ui/core/styles';
import {
  getLastThreeYears,
  getComparisonMonths,
  getLast13Months
} from 'src/utils/Utils';
import moment from 'moment';
import { useSelector } from 'react-redux';
import Alert from '@material-ui/lab/Alert';
import clsx from 'clsx';
import 'src/styles.css';
import { getServiceEfficiencyByMonthsAll } from 'src/utils/hasuraServices';

var lodash = require('lodash');

const useStyles = makeStyles({
  formControl: {
    margin: 8
  },
  gridContainer: {
    padding: 8
  },
  alertGrid: {
    margin: 4,
    padding: 4
  },
  alert: {
    fontSize: 14,
    fontWeight: 'bold'
  },
  chartContainer: {
    padding: '0px 4px'
  }
});
const ComparisonChartsGrid = ({
  mon1,
  mon2,
  props,
  parentCallback,
  removeFav
}) => {
  const classes = useStyles();
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  /* const [queryMonth, setQueryMonth] = useState(
    moment(new Date().setMonth(new Date().getMonth() - 1)).format('YYYY-MM')
  );
  const [queryMonth2, setQueryMonth2] = useState(
    moment(new Date().setMonth(new Date().getMonth() - 2)).format('YYYY-MM')
  );*/
  let month2 = mon1 ? mon1 : getComparisonMonths()[0];
  let month1 = mon2 ? mon2 : getComparisonMonths()[1];
  const [queryMonth, setQueryMonth] = useState(month2);
  const [queryMonth2, setQueryMonth2] = useState(month1);
  const [advisorChartData, setAdvisorChartData] = useState([]);
  const [advisor, setAdvisor] = useState(0);
  const handleClosePopup = value => {
    console.log('state===handleClosePopup');
  };
  const session = useSelector(state => state.session);
  const [checkEmpty, setCheckEmpty] = useState(false);
  useEffect(() => {
    if (
      typeof props.comparisonMonth1 != 'undefined' &&
      props.comparisonMonth1 != ''
    ) {
      setQueryMonth(props.comparisonMonth1);
    }
    if (
      typeof props.comparisonMonth2 != 'undefined' &&
      props.comparisonMonth2 != ''
    ) {
      setQueryMonth2(props.comparisonMonth2);
    }
  }, [session.serviceAdvisor]);

  useEffect(() => {
    getServiceEfficiencyByMonthsAll(queryMonth, queryMonth2, 'All', result => {
      if (
        result.data
          .statelessDbdPeopleMetricsServiceAdvisorRevenueMonthlyComparisonAll
          .statelessDbdPeopleMetricsServiceAdvisorRevenueComparisonAlls
      ) {
        var data =
          result.data
            .statelessDbdPeopleMetricsServiceAdvisorRevenueMonthlyComparisonAll
            .statelessDbdPeopleMetricsServiceAdvisorRevenueComparisonAlls;
        setAdvisorChartData(JSON.parse(data[0].jsonData));
      }
    });
  }, [queryMonth2, queryMonth, session.storeSelected]);
  const handleChange = val => {
    setCheckEmpty(val);
  };
  const [charts] = useState([
    'totalrevenue',
    'lbrsale',
    'prtssale',
    'lbrsoldhours',
    'rocount',
    'jobcount',
    'lbrprofit',
    'lbrprftpercentage',
    'prtsprofit',
    'prtprfpercentage',
    'elr',
    'markup'
  ]);
  const handleMonthchange = event => {
    setQueryMonth(event.target.value);
  };
  const handleMonthchange2 = event => {
    setQueryMonth2(event.target.value);
  };
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Service Advisor-Month Comparison' &&
      item.parentId == null
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');

  return (
    <Grid container spacing={12}>
      <Grid container xs={12}>
        <Grid
          item
          xs={6}
          justify="flex-start"
          className={classes.gridContainer}
        >
          <Paper square>
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                Month
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth}
                onChange={handleMonthchange}
              >
                {localStorage.getItem('versionFlag') == 'TRUE'
                  ? getLastThreeYears().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))
                  : getLast13Months().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))}
              </Select>
            </FormControl>
          </Paper>
          {checkEmpty ? (
            <Alert severity="info" style={{ fontSize: 14, fontWeight: 'bold' }}>
              Selected advisor have no data found during the time period{' '}
            </Alert>
          ) : null}
        </Grid>
        <Grid item xs={6} className={classes.gridContainer}>
          <Paper square justify="center">
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                Month
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth2}
                onChange={handleMonthchange2}
              >
                {localStorage.getItem('versionFlag') == 'TRUE'
                  ? getLastThreeYears().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))
                  : getLast13Months().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))}
              </Select>
            </FormControl>
          </Paper>
        </Grid>
      </Grid>

      <Grid container spacing={12} className={classes.chartContainer}>
        {charts.map((val, index) => {
          return (
            <Grid
              item
              xs={6}
              justify="flex-start"
              className={clsx('diagram-section')}
            >
              <ColumnRenderer
                month1={queryMonth}
                month2={queryMonth2}
                parentCallback={parentCallback}
                datatype={val}
                handleClosePopup={handleClosePopup}
                chartId={orderedData[index].chartId}
                removeFav={removeFav}
                checkEmpty={handleChange}
                advisorChartData={advisorChartData}
                isFrom={'ServiceAdvisorEfficiency'}
              />
            </Grid>
          );
        })}
      </Grid>
    </Grid>
  );
};

export default ComparisonChartsGrid;
