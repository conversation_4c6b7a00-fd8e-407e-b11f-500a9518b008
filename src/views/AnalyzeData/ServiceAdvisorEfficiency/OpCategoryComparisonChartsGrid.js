import {
  Grid,
  Paper,
  FormControl,
  MenuItem,
  Select,
  InputLabel,
  Button,
  Typography
} from '@material-ui/core';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import { useSelector } from 'react-redux';
import clsx from 'clsx';
import 'src/styles.css';
import { isArray } from 'validate.js';

import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import OpCategoryChartRenderer from './OpCategoryChartRenderer';
import {
  getLastThreeYears,
  getComparisonMonths,
  getLast13Months
} from 'src/utils/Utils';
import { getServiceAdvisorEfficiencyByOpCategoryAll } from 'src/utils/hasuraServices';

var lodash = require('lodash');

const useStyles = makeStyles({
  formControl: {
    margin: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  opcategoryBtn: {
    marginLeft: 10,
    marginRight: 5
  }
});

const OpCategoryComparisonChartsGrid = ({
  props,
  parentCallback,
  realm,
  removeFav
}) => {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  // const [queryMonth, setQueryMonth] = useState(
  //   moment(new Date().setMonth(new Date().getMonth() - 1)).format('YYYY-MM')
  // );
  let month1 = getComparisonMonths()[1];
  const [advisorChartData, setAdvisorChartData] = useState([]);
  const [queryMonth, setQueryMonth] = useState(month1);
  const [checkEmpty, setCheckEmpty] = useState(false);

  useEffect(() => {
    if (typeof props.month_year != 'undefined' && realm != 'ferrarioat_store') {
      if (Array.isArray(props.month_year) && props.month_year.length > 1) {
        setQueryMonth(props.month_year[props.month_year.length - 1]);
      } else {
        setQueryMonth(props.month_year);
      }
    }
  }, []);
  useEffect(() => {
    setAdvisorChartData([]);
    getServiceAdvisorEfficiencyByOpCategoryAll(
      queryMonth,

      'All',
      result => {
        if (
          result.data
            .statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategoryByMonthAll
            .statelessDbdPeopleMetricsServiceAdvisorRevenueComparisonAlls
        ) {
          var data =
            result.data
              .statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategoryByMonthAll
              .statelessDbdPeopleMetricsServiceAdvisorRevenueComparisonAlls;
          setAdvisorChartData(JSON.parse(data[0].jsonData));
        }
      }
    );
  }, [queryMonth, session.storeSelected]);
  const handleChange = val => {
    setCheckEmpty(val);
  };
  const [charts] = useState([
    'totalrevenue',
    'lbrsale',
    'prtssale',
    'lbrsoldhours',
    'rocount',
    'jobcount',
    'lbrprofit',
    // 'lbrprftpercentage',
    'prtsprofit',
    //'prtprfpercentage',
    'elr',
    'markup'
  ]);
  const handleMonthchange = event => {
    setQueryMonth(event.target.value);
  };
  const handleClosePopup = value => {
    console.log('state===handleClosePopup');
  };
  const renderBackButton = () => {
    let prevPath =
      props.history.location.state && props.history.location.state.prevPath
        ? props.history.location.state.prevPath
        : '';
    let chartId =
      props.history.location.state && props.history.location.state.chartId
        ? props.history.location.state.chartId
        : '';
    if (
      props.history.location.state &&
      props.history.location.state.currentPath &&
      props.history.location.state.currentPath == '/GraphDetailsView'
    ) {
      props.history.push({
        //pathname: '/GraphDetailsView?chartId=1243',
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        isFrom: prevPath ? 'opportunity' : 'workmixAdvisor',
        prevPath: prevPath,
        state: {
          tabSelection: 'one'
        }
      });
    } else if (
      props.history.location.state &&
      props.history.location.state.chartLoc &&
      props.history.location.state.chartLoc == 'PartsWorkMixAnalysis'
    ) {
      props.history.push({
        pathname: '/PartsWorkMixAnalysis',
        isFrom: prevPath ? 'opportunity' : 'workmixAdvisor',
        state: {
          tabSelection: 'one'
        }
      });
    } else {
      props.history.push({
        pathname: '/LaborWorkMixAnalysis',
        isFrom: prevPath ? 'opportunity' : 'workmixAdvisor',
        prevPath: props.history.location.state.prevPath,
        state: {
          tabSelection: 'one'
        }
      });
    }
  };
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Service Advisor-Opcategory By Month' &&
      item.parentId == null
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  return (
    <Grid container className={classes.container} spacing={12}>
      <Grid container spacing={12}>
        <Grid item xs={12} justify="flex-start" className={classes.container}>
          <Paper square>
            {props.history != undefined &&
            props.history.location.state != undefined &&
            props.history.location.state.parent == 'workmixAdvisor' ? (
              <span className={classes.opcategoryBtn}>
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={renderBackButton}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              </span>
            ) : null}
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
              style={{ verticalAlign: 'baseline' }}
            >
              <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                Month
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth}
                onChange={handleMonthchange}
              >
                {localStorage.getItem('versionFlag') == 'TRUE'
                  ? getLastThreeYears().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))
                  : getLast13Months().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))}
              </Select>
            </FormControl>
          </Paper>
          {checkEmpty ? (
            <Alert severity="info" style={{ fontSize: 14, fontWeight: 'bold' }}>
              Selected advisor have no data found during the time period{' '}
            </Alert>
          ) : null}
        </Grid>
      </Grid>

      <Grid container className={classes.container} spacing={12}>
        {charts.map((val, index) => {
          return (
            <Grid
              item
              xs={6}
              justify="flex-start"
              className={clsx(classes.container, 'diagram-section')}
            >
              <OpCategoryChartRenderer
                month1={queryMonth}
                datatype={val}
                handleClosePopup={handleClosePopup}
                chartId={orderedData[index].chartId}
                parentCallback={parentCallback}
                removeFav={removeFav}
                checkEmpty={handleChange}
                propsData={props}
                advisorChartData={advisorChartData}
                isFrom={'ServiceAdvisorEfficiency'}
              />
            </Grid>
          );
        })}
      </Grid>
    </Grid>
  );
};

export default OpCategoryComparisonChartsGrid;
