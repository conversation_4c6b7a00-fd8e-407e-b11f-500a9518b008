import { QueryRenderer } from '@cubejs-client/react';
import {
  CircularProgress,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import moment from 'moment';
import React, { useState } from 'react';
import ReactApexChart from 'react-apexcharts';
import cubejsApi from 'src/utils/cubeUtils';
import { makeStyles } from '@material-ui/styles';
import { getDashboardGraphQuery } from 'src/components/DashboardGraphQuery';
import MoreActions from 'src/components/MoreActions';
import {
  getChartName,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';

import ChartDialog from 'src/components/Dialog';
import { useHistory } from 'react-router';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';

const useStyles = makeStyles(theme => ({
  cardContainer: {
    borderRadius: 0,
    border: '1px solid #003d6b'
  }
}));

const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || (
    <Paper square style={{ margin: 8 }}>
      <Grid
        justify="center"
        style={{
          height: 250,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress size={60} />
      </Grid>
    </Paper>
  );

const BarChartRenderer = ({
  chartId,
  title,
  removeFav,
  advisor,
  advisorName,
  checkEmpty,
  handleClosePopup,
  isFrom,
  type,
  parentCallback,

  tabSelection,
  mainTabSelection
}) => {
  const session = useSelector(state => state.session);
  const history = useHistory();
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [queryState, setQuery] = [
    getDashboardGraphQuery(
      chartId,
      session.serviceAdvisor,
      JSON.parse(localStorage.getItem('selectedStoreId'))[0]
    )
  ];
  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const barRender = ({ resultSet }) => {
    if (resultSet.seriesNames()[0] && resultSet.seriesNames()[0].title) {
      checkEmpty(false);
    } else {
      checkEmpty(true);
    }
    const customData = resultSet.series().map((s, index) => ({
      name: s.title,
      data: s.series.map(r => r.value)
    }));
    console.log(
      'customData',
      resultSet.categories().map(c => moment(c.category).format('MMM ’YY'))
    );
    const chartName = 'cp overview';
    const options = {
      chart: {
        zoom: {
          enabled: false
        },

        events: {
          // click: function(event, chartContext, config) {
          dataPointSelection: function(event, chartContext, config) {
            console.log(
              'vvv==123',
              resultSet.loadResponse.data[config.dataPointIndex][
                'ServiceAdvisorDrillDown.ro_date'
              ]
                ? resultSet.loadResponse.data[config.dataPointIndex][
                    'ServiceAdvisorDrillDown.ro_date'
                  ].substring(0, 7)
                : resultSet.loadResponse.data[config.dataPointIndex][
                    'ServiceAdvisorDrillDownAll.ro_date'
                  ].substring(0, 7)
            );
            let data = {
              type: 'advisortrendingcharts',
              reportType: title,
              month: resultSet.loadResponse.data[config.dataPointIndex][
                'ServiceAdvisorDrillDown.ro_date'
              ]
                ? resultSet.loadResponse.data[config.dataPointIndex][
                    'ServiceAdvisorDrillDown.ro_date'
                  ].substring(0, 7)
                : resultSet.loadResponse.data[config.dataPointIndex][
                    'ServiceAdvisorDrillDownAll.ro_date'
                  ].substring(0, 7),
              chartName: config.w.globals.seriesNames[0]
            };
            if (typeof parentCallback == 'undefined') {
              // history.push({
              //   pathname: '/ServiceAdvisorPerformance',
              //   state: {
              //     tabselection: 'eleven',
              //     isAdvisorCharts: true,
              //     isEfficiencyCharts: false,
              //     isTechnicianCharts: false,
              //     month_year: data.month,
              //     type: 'advisortrendingcharts',
              //     isFrom: 'advisortrendingcharts',
              //     advisor: advisor,
              //     advisorName: advisorName,
              //     chartType: 1,
              //     chartName: chartName,
              //     parent: 'advisortrendingcharts'
              //   }
              // });
            } else {
              //parentCallback(data);
            }
          }
        },
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        }
      },
      dataLabels: {
        enabled: false
      },
      // title: {
      //   //text: isFrom != 'detail_page' ? resultSet.seriesNames()[0] ? resultSet.seriesNames()[0].title : getChartNames(resultSet.loadResponse.query.measures[0]) : '',
      //   align: 'left'
      // },
      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      markers: {
        size: 4,
        strokeWidth: 1,
        strokeOpacity: 0.7
      },
      stroke: {
        curve: 'straight',
        width: 2.5
      },
      theme: {
        palette: 'palette8' // upto palette10
      },
      yaxis: {
        tickAmount: chartName.includes('Markup') ? 5 : 0,
        title: {
          text: chartName.includes('Revenue')
            ? 'Revenue'
            : chartName.includes('%')
            ? 'Percentages'
            : chartName.includes('Hours')
            ? 'Hours'
            : chartName.includes('Count')
            ? 'Count'
            : chartName.includes('Gross')
            ? 'Profit '
            : chartName.includes('Markup')
            ? 'Markup'
            : chartName.includes('Rate')
            ? 'Rate'
            : ''
        },
        labels: {
          formatter: function(value) {
            if (resultSet.seriesNames()[0]) {
              if (resultSet.seriesNames()[0].title.includes('Markup')) {
                return (
                  Math.round(value * 100 + Number.EPSILON) / 100
                ).toLocaleString();
              } else if (resultSet.seriesNames()[0].title.includes('%')) {
                return (
                  (
                    Math.round(value * 100 + Number.EPSILON) / 100
                  ).toLocaleString() + ' %'
                );
              } else if (
                resultSet.seriesNames()[0].title.includes('Revenue') ||
                resultSet.seriesNames()[0].title.includes('Profit') ||
                resultSet.seriesNames()[0].title.includes('Rate')
              ) {
                return Number.isInteger(value)
                  ? ' $' +
                      value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  : ' $' +
                      value
                        .toFixed(2)
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              } else if (
                resultSet.seriesNames()[0].title.includes('Count') ||
                resultSet.seriesNames()[0].title.includes('Hours')
              ) {
                return Math.round(value)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              } else
                return (
                  Math.round(value * 100 + Number.EPSILON) / 100
                ).toLocaleString();
            } else {
              if (
                chartName.includes('Rate') ||
                chartName.includes('Revenue') ||
                chartName == 'Labor Gross Profit' ||
                chartName == 'Parts Gross Profit'
              ) {
                return '$' + value;
              } else {
                return value;
              }
            }
          }
        }
      },
      xaxis: {
        tooltip: {
          enabled: false
        },
        title: {
          text: 'Month'
        },
        categories: resultSet
          .categories()
          .map(c => moment(c.category).format('MMM ’YY'))
      },
      tooltip: {
        shared: false,
        intersect: true,
        x: {
          formatter: function(y, index) {
            return resultSet
              .categories()
              .map(c => moment(c.category).format('MMM-YY'))[y - 1];
          }
        },
        y: {
          formatter: function(y) {
            if (
              typeof y != 'undefined' &&
              (resultSet.seriesNames()[0].title == '  Labor Gross Profit %' ||
                resultSet.seriesNames()[0].title == '  Parts Gross Profit %')
            ) {
              return y.toFixed(1) + '%';
            } else if (
              (typeof y != 'undefined' &&
                resultSet.seriesNames()[0].title.includes('Revenue')) ||
              resultSet.seriesNames()[0].title.includes('Profit') ||
              resultSet.seriesNames()[0].title.includes('Rate')
            ) {
              return '$' + y.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else if (
              typeof y != 'undefined' &&
              resultSet.seriesNames()[0].title.includes('Count')
            ) {
              return Math.round(y)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else if (
              typeof y != 'undefined' &&
              resultSet.seriesNames()[0].title.includes('Markup')
            ) {
              return y.toFixed(4);
            }
            return y.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          }
        }
      }
    };

    const result = (
      // <Paper square style={{ margin: 4, padding: 8, cursor: 'pointer' }}>
      <Card
        bordered={false}
        className={classes.cardContainer}
        style={{
          // height: '85%',
          margin:
            window.location.pathname == '/MyFavorites'
              ? 0
              : isFrom == 'detail_page'
              ? 0
              : 4
        }}
      >
        <CardHeader
          title={getChartName(chartId)}
          action={
            <MoreActions
              removeFavourite={removeFavourite}
              setActions={setactions}
              chartId={chartId}
              type={type}
              chartPopup={chartPopup}
              handleClose={handleClose}
              //favoritesDisabled={true}
            ></MoreActions>
          }
          subheader={getSubHeader(chartId)}
        ></CardHeader>

        <Divider />
        <CardContent style={{ cursor: 'pointer', height: '85%' }}>
          <ReactApexChart
            options={options}
            series={customData}
            type="line"
            height={
              window.location.pathname == '/MyFavorites'
                ? 190
                : isFrom == 'detail_page'
                ? 200
                : type == 'popup'
                ? 500
                : '125%'
            }
          />
        </CardContent>
      </Card>
      // </Paper>
    );

    return result;
  };

  const getChartNames = id => {
    var name = 'total';

    switch (id) {
      case 'ServiceAdvisorDrillDown.totalrevenue':
        return 'Total Revenue';
      case 'ServiceAdvisorDrillDown.lbrsale':
        return 'Labor Revenue';
      case 'ServiceAdvisorDrillDown.prtssale':
        return 'Parts Revenue';
      case 'ServiceAdvisorDrillDown.lbrsoldhours':
        return 'Labor Sold Hours';
      case 'ServiceAdvisorDrillDown.rocount':
        return 'RO Count';
      case 'ServiceAdvisorDrillDown.jobcount':
        return 'Job Count';
      case 'ServiceAdvisorDrillDown.lbrprofit':
        return 'Labor Gross Profit';
      case 'ServiceAdvisorDrillDown.lbrprftpercentage':
        return 'Labor Gross Profit %';
      case 'ServiceAdvisorDrillDown.prtsprofit':
        return 'Parts Gross Profit';
      case 'ServiceAdvisorDrillDown.prtprfpercentage':
        return 'Parts Gross Profit %';
      case 'ServiceAdvisorDrillDown.avgelr':
        return 'Effective Labor Rate';
      case 'ServiceAdvisorDrillDown.avgmarkup':
        return 'Parts Markup';
    }
  };
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        SelectedLocation: window.location.pathname,
        state: {
          tabSelection: 'eight',
          selectedSubTab: 'one',
          SelectedLocation: window.location.pathname
        }
      });
    }
  };
  return (
    <>
      <QueryRenderer
        query={queryState}
        cubejsApi={cubejsApi()}
        render={renderChart(barRender)}
      />
      <ChartDialog
        open={open}
        chartId={chartId}
        chartType="advisorCharts"
        realm={localStorage.getItem('realm')}
        parentCallback={parentCallback}
        advisor={advisor}
        advisorName={advisorName}
        checkEmpty={checkEmpty}
        handlePopupClose={handleClose}
      />
    </>
  );
};
BarChartRenderer.propTypes = {
  removeFav: PropTypes.func
};

export default BarChartRenderer;
