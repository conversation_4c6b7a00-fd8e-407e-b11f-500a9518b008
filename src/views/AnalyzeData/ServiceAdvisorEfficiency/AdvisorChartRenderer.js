import {
  CircularProgress,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import MoreActions from 'src/components/MoreActions';
import React, { useEffect, useState } from 'react';

import { makeStyles } from '@material-ui/core/styles';
import ReactApexChart from 'react-apexcharts';
import {
  getServiceAdvisorData,
  getTechnicianRevenue
} from 'src/utils/hasuraServices';
import { useHistory } from 'react-router';
import PropTypes from 'prop-types';
import { getComparisonMonths } from 'src/utils/Utils';
import { getSubHeader } from 'src/components/ViewGraphDetailsAction';
import ChartDialog from 'src/components/Dialog';
import moment from 'moment';
import clsx from 'clsx';
import 'src/styles.css';

const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});

var lodash = require('lodash');
const AdvisorChartRenderer = ({
  chartId,
  title,
  removeFav,
  advisor,
  advisorName,
  checkEmpty,
  handleClosePopup,
  isFrom,
  type,
  parentCallback,
  session,
  tabSelection,
  mainTabSelection,
  serviceAdvisor
}) => {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  const [techno, setTechno] = useState([]);
  const [charttype, setDataType] = useState([]);
  //const [isFrom, setCallFrom] = useState([]);
  const [chartName, setChartName] = useState('');
  useEffect(() => {
    setLoading(true);
    var chart = getGraphName(chartId);

    getDataforAdvisorCharts(chart);
  }, [session.serviceAdvisor]);
  const history = useHistory();
  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const getDataforAdvisorCharts = chartName => {
    getServiceAdvisorData(serviceAdvisor, result => {
      console.log('customData', result.length);
      if (result.length > 0) {
        checkEmpty(false);
      } else {
        checkEmpty(true);
      }
      if (result) {
        let oderedData;

        let data = result;

        var arr = [];
        var monthArr = [];
        //if (chartId == '1264' || chartId == '1265')
        //oderedData = lodash.orderBy(data, 'mon1', 'desc');
        if (chartName == 'Total Revenue ') {
          data.map((s, index) => {
            arr.push(s.totalrevenue);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Labor Revenue') {
          data.map((s, index) => {
            arr.push(s.lbrsale);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Parts Revenue') {
          data.map((s, index) => {
            arr.push(s.prtssale);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Labor Sold Hours') {
          data.map((s, index) => {
            arr.push(s.lbrsoldhours);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'RO Count ') {
          data.map((s, index) => {
            arr.push(s.rocount);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Job Count ') {
          data.map((s, index) => {
            arr.push(s.jobcount);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Labor Gross Profit') {
          data.map((s, index) => {
            arr.push(s.lbrprofit);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Labor Gross Profit %') {
          data.map((s, index) => {
            arr.push(s.lbrprftpercentage);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Parts Gross Profit') {
          data.map((s, index) => {
            arr.push(s.prtsprofit);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Parts Gross Profit %') {
          data.map((s, index) => {
            arr.push(s.prtprfpercentage);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Effective Labor Rate') {
          data.map((s, index) => {
            arr.push(s.avgelr);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        } else if (chartName == 'Parts Markup') {
          data.map((s, index) => {
            arr.push(s.avgmarkup);
            monthArr.push(moment(s.monthYear).format('MMM ’YY'));
          });
        }
        const filteredArr = arr;
        //const filteredArr = arr.filter(value => parseFloat(value) != 0);
        oderedData = [{ name: chartName, data: filteredArr, month: monthArr }];
        // oderedData = [{ name: chartName, data: arr, month: monthArr }];

        // var nullRemoved = oderedData.map(val => val.techname.replace('[', ' ['));

        // var techNameArray = nullRemoved.filter(el => el != null);

        // var lbrtechnoArray = oderedData.map(val => val.lbrtechno);

        const seriesData = oderedData;
        setSeries(seriesData);

        const options = {
          chart: {
            zoom: {
              enabled: false
            },

            events: {
              // click: function(event, chartContext, config) {
              dataPointSelection: function(event, chartContext, config) {
                let data = {
                  type: 'advisortrendingcharts',
                  reportType: title,
                  month: result[config.dataPointIndex]
                    ? result[config.dataPointIndex].monthYear
                    : result[config.dataPointIndex].monthYear,
                  chartName: chartName
                };
                if (typeof parentCallback == 'undefined') {
                  if (
                    session.serviceAdvisor[0] == 'All' &&
                    (isFrom == 'detail_page' || isFrom == 'source_page')
                  ) {
                    //if (technicianSelected == '' || technicianSelected == 'All') {
                    alert('Choose any Service Advisor ');
                  } else {
                    history.push({
                      pathname: '/ServiceAdvisorPerformance',
                      state: {
                        tabselection: 'eleven',
                        isAdvisorCharts: true,
                        isEfficiencyCharts: false,
                        isTechnicianCharts: false,
                        month_year: data.month,
                        type: 'advisortrendingcharts',
                        isFrom: 'advisortrendingcharts',
                        advisor: advisor,
                        advisorName: advisorName,
                        chartType: 1,
                        chartName: chartName,
                        parent: 'advisortrendingcharts'
                      }
                    });
                  }
                } else {
                  parentCallback(data);
                }
              }
            },
            fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
            toolbar: {
              show: false,
              autoSelected: 'zoom'
            }
          },
          dataLabels: {
            enabled: false
          },

          // title: {
          //   //text: isFrom != 'detail_page' ? resultSet.seriesNames()[0] ? resultSet.seriesNames()[0].title : getChartNames(resultSet.loadResponse.query.measures[0]) : '',
          //   align: 'left'
          // },
          grid: {
            row: {
              colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
              opacity: 0.5
            }
          },
          markers: {
            size: 4,
            strokeWidth: 1,
            strokeOpacity: 0.7
          },
          stroke: {
            curve: 'straight',
            width: 2.5
          },
          theme: {
            palette: 'palette8' // upto palette10
          },
          yaxis: {
            tickAmount: chartName.includes('Markup') ? 5 : 0,
            title: {
              text: chartName.includes('Revenue')
                ? 'Revenue'
                : chartName.includes('%')
                ? 'Percentages'
                : chartName.includes('Hours')
                ? 'Hours'
                : chartName.includes('Count')
                ? 'Count'
                : chartName.includes('Gross')
                ? 'Profit '
                : chartName.includes('Markup')
                ? 'Markup'
                : chartName.includes('Rate')
                ? 'Rate'
                : ''
            },
            labels: {
              formatter: function(value) {
                // console.log('vvv==', value, resultSet.seriesNames());
                if (chartName) {
                  if (chartName.includes('Markup')) {
                    return (
                      Math.round(value * 100 + Number.EPSILON) / 100
                    ).toLocaleString();
                  } else if (chartName.includes('%')) {
                    return (
                      (
                        Math.round(value * 100 + Number.EPSILON) / 100
                      ).toLocaleString() + ' %'
                    );
                  } else if (
                    chartName.includes('Revenue') ||
                    chartName.includes('Profit') ||
                    chartName.includes('Rate')
                  ) {
                    return Number.isInteger(value)
                      ? ' $' +
                          value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                      : ' $' +
                          value
                            .toFixed(2)
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  } else if (
                    // chartName.includes('Count') ||
                    chartName.includes('Hours')
                  ) {
                    return Math.round(value)
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                  } else
                    return (
                      Math.round(value * 100 + Number.EPSILON) / 100
                    ).toLocaleString();
                } else {
                  if (
                    chartName.includes('Rate') ||
                    chartName.includes('Revenue') ||
                    chartName == 'Labor Gross Profit' ||
                    chartName == 'Parts Gross Profit'
                  ) {
                    return '$' + value;
                  } else {
                    return value;
                  }
                }
              }
            }
          },
          xaxis: {
            tooltip: {
              enabled: false
            },
            title: {
              text: 'Month'
            },
            categories: seriesData[0].month
          },
          tooltip: {
            shared: false,
            intersect: true,
            x: {
              formatter: function(y, index) {
                return data.map(c => moment(c.monthYear).format('MMM-YY'))[
                  y - 1
                ];
              }
            },
            y: {
              formatter: function(y) {
                if (
                  typeof y != 'undefined' &&
                  (chartName == 'Labor Gross Profit %' ||
                    chartName == 'Parts Gross Profit %')
                ) {
                  return y.toFixed(1) + '%';
                } else if (
                  (typeof y != 'undefined' && chartName.includes('Revenue')) ||
                  chartName.includes('Profit') ||
                  chartName.includes('Rate')
                ) {
                  return (
                    '$' + y.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  );
                } else if (
                  typeof y != 'undefined' &&
                  chartName.includes('Count')
                ) {
                  return Math.round(y)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                } else if (
                  typeof y != 'undefined' &&
                  chartName.includes('Markup')
                ) {
                  return y.toFixed(4);
                }
                return y.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
              }
            }
          }
        };

        setchartOptions(options);
        setLoading(false);
      } else {
        setLoading(false);
      }
    });
  };

  const getGraphName = chartId => {
    var chartList = JSON.parse(global.localStorage.getItem('chart-master'));

    let chart = chartList.filter(item => item.chartId == chartId);

    return chart[0].chartName;
  };
  const getDataType = chartId => {
    var datatype = '';
    switch (chartId) {
      case 1264:
        datatype = 'revenue';
        break;
      case 1265:
        datatype = 'soldhours';
        break;
      case 1349:
        datatype = 'jobcount';
        break;
      case 1350:
        datatype = 'jobperc';
        break;
      case 1266:
        datatype = 'techefficiencyoverall';
        break;
      case 1267:
        datatype = 'estimatedtechefficiency';
        break;
      case 1268:
        datatype = 'nonzeroflatratehrsandnonzeroactualhrs';
        break;
      case 1269:
        datatype = 'nonzeroflatratehrsandzeroactualhrs';
        break;
      case 1270:
        datatype = 'zeroflatratehrsandnonzeroactualhrs';
        break;
      case 1271:
        datatype = 'zeroflatratehrsandzeroactualhrs';
        break;

      case 1272:
        datatype = 'jobcountflathrsandactualhrs';
        break;
      case 1273:
        datatype = 'jobcountflatratehrsandzeroactualhrs';
        break;
      case 1274:
        datatype = 'jobcountzeroflatratehrsandnonzeroactualhrs';
        break;
      case 1275:
        datatype = 'jobcountzeroflatratehrsandzeroactualhrs';
        break;
    }
    return datatype;
  };
  const processData = data => {};
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=' + chartId + '?chartId=' + chartId,
        state: {
          tabSelection: 'eight',
          selectedSubTab: 'one',
          SelectedLocation: window.location.pathname
        },
        SelectedLocation: window.location.pathname
      });
    }
  };

  return (
    <>
      {isLoading == true ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <Card
          bordered={false}
          style={{
            border: '1px solid #003d6b',
            margin: 4,
            borderRadius: window.location.pathname === '/MyFavorites' ? 0 : 4
          }}
          id={'chart-' + chartId}
        >
          <CardHeader
            style={
              typeof parentCallback == 'undefined'
                ? { padding: 18, paddingLeft: 20 }
                : { padding: 15 }
            }
            title={getGraphName(chartId)}
            action={
              <MoreActions
                removeFavourite={removeFavourite}
                setActions={setactions}
                chartId={chartId}
                type={type}
                chartPopup={chartPopup}
                handleClose={handleClose}
                //favoritesDisabled={true}
              ></MoreActions>
            }
            subheader={getSubHeader(chartId)}
            //style={{ borderBottom: '1px solid #003d6b' }}
          ></CardHeader>
          <Divider />
          <CardContent style={{ cursor: 'pointer', height: '85%' }}>
            <ReactApexChart
              options={chartoptions}
              series={series}
              type="line"
              height={
                window.location.pathname == '/MyFavorites'
                  ? 190
                  : isFrom == 'detail_page'
                  ? 200
                  : type == 'popup'
                  ? 500
                  : '125%'
              }
            />
          </CardContent>
        </Card>
      )}
      <ChartDialog
        open={open}
        chartId={chartId}
        chartType="advisorCharts"
        realm={localStorage.getItem('realm')}
        parentCallback={parentCallback}
        advisor={advisor}
        advisorName={advisorName}
        checkEmpty={checkEmpty}
        handlePopupClose={handleClose}
      />
    </>
  );
};

function getGroupName(index) {
  let name = '';
  switch (index) {
    case 0:
      name = 'REPAIR';
      break;
    case 1:
      name = 'MAINTENANCE';
      break;
    case 2:
      name = 'COMPETITIVE';
      break;
    default:
      name = '';
      break;
  }
  return name;
}
AdvisorChartRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default AdvisorChartRenderer;
