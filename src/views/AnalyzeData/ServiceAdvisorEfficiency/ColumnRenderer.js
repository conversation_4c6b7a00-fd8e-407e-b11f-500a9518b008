import {
  CircularProgress,
  Grid,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import React, { useEffect, useState } from 'react';
import ReactApexChart from 'react-apexcharts';
import {
  getServiceEfficiencyByMonths,
  getAllServiceAdvisors
} from 'src/utils/hasuraServices';
import MoreActions from 'src/components/MoreActions';

import { makeStyles } from '@material-ui/core/styles';
import { useHistory } from 'react-router';
import {
  getChartName,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import PropTypes from 'prop-types';
import { getComparisonMonths } from 'src/utils/Utils';
import ChartDialog from 'src/components/Dialog';
import moment from 'moment';
import { useSelector } from 'react-redux';
import { event } from 'react-ga';
import clsx from 'clsx';
import 'src/styles.css';

var lodash = require('lodash');
const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  }
});
const ColumnRenderer = ({
  datatype,
  removeFav,
  month1,
  month2,
  chartId,
  parentCallback,
  handleClosePopup,
  type,
  isFrom,
  checkEmpty,

  tabSelection,
  mainTabSelection,
  advisorChartData
}) => {
  const [open, setOpen] = useState(false);
  const classes = useStyles();
  const [isLoading, setLoading] = useState(true);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  const history = useHistory();
  const session = useSelector(state => state.session);
  var ServiceAdvisorArr = JSON.stringify(session.serviceAdvisor);
  var ServiceAdvisorWithId = JSON.stringify(session.advisorWithId);
  const [dataSeries, setDataseries] = useState([]);
  const [advisorNames, setAdvisornames] = useState([]);
  const [advisor, setAdvisor] = useState(session.serviceAdvisor);
  const [selectedAdv, setSelectedAdv] = useState([]);
  useEffect(() => {
    setLoading(true);
    if (isFrom == 'ServiceAdvisorEfficiency') {
      if (advisorChartData.length > 0) {
        const transformedData = transformComparisonData(advisorChartData);

        const chartData = transformedData[chartId];

        const chartType = getDataType(Number(chartId)); // 'workmix', 'elr', or 'jobcount'

        setCHartData(chartData);
      }
    } else {
      getDataforColumnChart();
    }

    // getAllServiceAdvisors();
    setAdvisor(session.serviceAdvisor);
    setAdvisornames(session.advisorWithId);
  }, [datatype, ServiceAdvisorArr, session.serviceAdvisor, advisorChartData]);
  const removeFavourite = val => {
    removeFav(val);
  };
  const transformComparisonData = workmixData => {
    const result = {};

    if (workmixData.length > 0) {
      const datasets = workmixData[0].datasets;

      // Find base datasets
      const advisorIds = datasets.find(d => d.label === 'serviceadvisor');
      const advisorName = datasets.find(d => d.label === 'advisor_name');

      // Group datasets by chartId
      const chartGroups = {};
      datasets.forEach(dataset => {
        if (dataset.chartId) {
          if (!chartGroups[dataset.chartId]) {
            chartGroups[dataset.chartId] = {};
          }
          chartGroups[dataset.chartId][dataset.label] = dataset;
        }
      });

      // Transform each chartId group
      Object.keys(chartGroups).forEach(chartId => {
        const group = chartGroups[chartId];
        const mon1Dataset = Object.values(group).find(d =>
          d.label.includes('_mon1')
        );
        const mon2Dataset = Object.values(group).find(d =>
          d.label.includes('_mon2')
        );

        if (mon1Dataset && mon2Dataset) {
          result[chartId] = advisorIds.data.map((code, index) => ({
            serviceadvisor: code,
            serviceadvisorName: advisorName.data[index],
            mon1: mon1Dataset.data[index],
            mon2: mon2Dataset.data[index]
          }));
        }
      });
    }

    return result;
  };
  const setCHartData = resultArr => {
    let filteredArr = [];
    let selectedAdvisor = [];

    let dataAdvisors = [];
    if (ServiceAdvisorArr.includes('All') == false) {
      dataAdvisors = lodash.map(resultArr, 'serviceadvisor');
      let checkAdvisor = lodash.difference(
        JSON.parse(ServiceAdvisorArr),
        dataAdvisors
      ).length;
      if (checkAdvisor != 0) {
        filteredArr = [
          {
            mon1: '0.00',
            mon2: '0.00',
            serviceadvisor: '',
            serviceadvisorName: ''
          }
        ];
      }

      resultArr.map(obj => {
        if (JSON.parse(ServiceAdvisorArr).indexOf(obj.serviceadvisor) != -1) {
          filteredArr.push(obj);

          if (obj.serviceadvisor != '')
            selectedAdvisor.push(obj.serviceadvisor);
        }
      });
      processData(filteredArr);
      filteredArr.forEach(item => {
        if (item.mon1 == '0.00' && item.mon2 == '0.00') {
          checkEmpty(true);
        } else {
          checkEmpty(false);
        }
      });

      // if (filteredArr.length < 1 || filteredArr[0].mon1 == '0.00') {
      //   checkEmpty(true);
      // } else {
      //   checkEmpty(false);
      // }
    } else {
      processData(resultArr);
      if (isFrom != 'detail_page' && type != 'popup') {
        checkEmpty(false);
      }
    }
  };

  const getDataforColumnChart = () => {
    if (datatype == '') {
      month1 = month1 ? month1 : getComparisonMonths()[0];
      month2 = month2 ? month2 : getComparisonMonths()[1];
      datatype = getDataType(Number(chartId));
    }
    let advisorName = [];
    getServiceEfficiencyByMonths(month1, month2, datatype, result => {
      if (
        result.data
          .statelessDbdPeopleMetricsServiceAdvisorRevenueMonthlyComparison.nodes
      ) {
        var resultArr =
          result.data
            .statelessDbdPeopleMetricsServiceAdvisorRevenueMonthlyComparison
            .nodes;

        setCHartData(resultArr);
      } else {
        setLoading(false);
      }
    });

    for (let index = 0; index < advisor.length; index++) {
      advisorNames.find(el => {
        if (el.includes(advisor[index])) {
          advisorName.push(el);

          return el;
        }
      });
    }
  };
  const getDataType = chartId => {
    var datatype = '';
    switch (chartId) {
      case 1287:
        datatype = 'totalrevenue';
        break;
      case 1288:
        datatype = 'lbrsale';
        break;
      case 1289:
        datatype = 'prtssale';
        break;
      case 1290:
        datatype = 'lbrsoldhours';
        break;
      case 1291:
        datatype = 'rocount';
        break;
      case 1292:
        datatype = 'jobcount';
        break;
      case 1293:
        datatype = 'lbrprofit';
        break;
      case 1294:
        datatype = 'lbrprftpercentage';
        break;
      case 1295:
        datatype = 'prtsprofit';
        break;
      case 1296:
        datatype = 'prtprfpercentage';
        break;
      case 1297:
        datatype = 'elr';
        break;
      case 1298:
        datatype = 'markup';
        break;
    }
    return datatype;
  };

  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=' + chartId.toString() + '?chartId=' + chartId,
        state: {
          month1: month1,
          month2: month2,
          datatype: datatype,
          tabSelection: 'eight',
          selectedSubTab: 'two',
          SelectedLocation: window.location.pathname
        },
        isFrom: isFrom,
        SelectedLocation: window.location.pathname
      });
    }
  };
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const clickLegend = e => {
    let filteredAdv = [];
    let advList = [];
    var input = e.target.className;
    var active;
    var lbl = document.getElementsByClassName(input);

    for (var i = 0, len = lbl.length; i < len; i++) {
      if (lbl[i].tagName == 'SPAN') {
        if (lbl[i].style.backgroundColor == 'rgb(185, 19, 19)') {
          lbl[i].style.backgroundColor = '#999';
          active = 0;
          advisor.filter(el => {
            if (el == e.target.id && !selectedAdv.includes(e.target.id)) {
              selectedAdv.push(el);
            }
          });
        } else {
          lbl[i].style.backgroundColor = 'rgb(185, 19, 19)';
          active = 1;
          selectedAdv.splice(selectedAdv.indexOf(e.target.id), 1); //deleting
        }
      }
      if (lbl[i].tagName == 'LABEL') {
        if (active == 1) {
          lbl[i].style.textDecoration = 'none';
        } else {
          lbl[i].style.textDecoration = 'line-through';
        }
      }
    }

    dataSeries.map(obj => {
      if (selectedAdv.indexOf(obj.serviceadvisor) == -1) {
        filteredAdv.push(obj);
      }
    });

    processData(filteredAdv);
  };
  const processData = data => {
    let orderedData = lodash.orderBy(
      data,
      ['serviceadvisor', 'mon1'],
      ['asc', 'desc']
    );
    orderedData = lodash.filter(
      orderedData,
      item => item.serviceadvisor != null && item.serviceadvisorName != null
    );

    // Remove negative values
    orderedData.forEach(item => {
      if (item.mon1 < 0) item.mon1 = 0;
      if (item.mon2 < 0) item.mon2 = 0;
      // Add similar checks for other 'mon' properties if necessary
    });
    var lbrserviceadvisorArray = orderedData.map(val => val.serviceadvisor);
    const seriesData = [
      {
        name: moment(month1).format('MMM-YY'),
        data: orderedData.map(e => e.mon1)
      },
      {
        name: moment(month2).format('MMM-YY'),
        data: orderedData.map(e => e.mon2)
      }
    ];

    const chartName = getChartName(chartId);
    const options = {
      chart: {
        events: {
          click: function(event, chartContext, config) {
            let data = {
              type: 'advisorcomparison',
              isType: 'monthComparison',
              person: lbrserviceadvisorArray[config.dataPointIndex],
              month: config.globals.seriesNames[config.seriesIndex],
              comparisonMonth1: month1,
              comparisonMonth2: month2
            };
            if (
              typeof parentCallback == 'undefined' &&
              config.dataPointIndex >= 0
            ) {
              history.push({
                pathname: '/ServiceAdvisorPerformance',
                state: {
                  tabselection: 'eleven',
                  month_year: config.seriesIndex == 0 ? month1 : month2,
                  type: 'monthComparison',
                  isFrom: 'advisorcomparison',
                  advisorName: data.person,
                  comparisonMonth1: data.comparisonMonth1,
                  comparisonMonth2: data.comparisonMonth2,
                  parent: 'advisorcomparison'
                }
              });
            } else {
              if (data.person) {
                let monthYear = data.month.split('-');
                let date = moment(monthYear[1], 'YY');
                let year = date.format('YYYY');
                let month = moment()
                  .month(monthYear[0])
                  .format('MM');
                data.month = year + '-' + month;
                parentCallback(data);
              }
            }
          }
        },
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        }
      },
      dataLabels: {
        enabled: false
      },
      // title: {
      //   text: getGraphName(datatype),
      //   align: 'left'
      // },
      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      colors: ['rgb(51,102,204)', 'rgb(220,57,18)'],
      plotOptions: {
        bar: {
          horizontal: true
        }
      },
      theme: {
        palette: 'palette8' // upto palette10
      },
      yaxis: {
        // labels: {
        //   formatter: function(value) {
        //     if (getChartName(chartId).includes('Markup')) {
        //       return (
        //         Math.round(value * 100 + Number.EPSILON) / 100
        //       ).toLocaleString();
        //     } else if (getChartName(chartId).includes('%')) {
        //       return (
        //         (
        //           Math.round(value * 100 + Number.EPSILON) / 100
        //         ).toLocaleString() + ' %'
        //       );
        //     } else if (
        //       getChartName(chartId).includes('Revenue') ||
        //       getChartName(chartId).includes('Profit') ||
        //       getChartName(chartId).includes('Rate')
        //     ) {
        //       return (
        //         ' $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        //       );
        //     }
        //     if (value == 'Infinity') {
        //       value = 100;
        //     } else return value;
        //   }
        // }
      },
      xaxis: {
        title: {
          text: chartName.includes('Revenue')
            ? 'Revenue'
            : chartName.includes('%')
            ? 'Percentage'
            : chartName.includes('Hours')
            ? 'Hours'
            : chartName.includes('hours')
            ? 'Hours'
            : chartName.includes('Count')
            ? 'Count'
            : chartName.includes('Gross')
            ? 'Profit '
            : chartName.includes('Markup')
            ? 'Markup'
            : chartName.includes('Rate')
            ? 'Rate'
            : ''
        },
        categories: orderedData.map(val =>
          val.serviceadvisorName.replace('[', ' [')
        ),
        labels: {
          formatter: function(value) {
            if (getChartName(chartId).includes('Markup')) {
              return (
                Math.round(value * 100 + Number.EPSILON) / 100
              ).toLocaleString();
            } else if (getChartName(chartId).includes('%')) {
              return (
                (
                  Math.round(value * 100 + Number.EPSILON) / 100
                ).toLocaleString() + '%'
              );
            } else if (
              getChartName(chartId).includes('Revenue') ||
              getChartName(chartId).includes('Profit') ||
              getChartName(chartId).includes('Rate')
            ) {
              if (value && Number.isInteger(value)) {
                return (
                  ' $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                );
              } else {
                return (
                  ' $' +
                  value
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                );
              }
            } else if (
              getChartName(chartId).includes('Hours') ||
              getChartName(chartId).includes('Count')
            ) {
              return Number.isInteger(value)
                ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : value
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            }
            if (value == 'Infinity') {
              value = 100;
            } else return value;
          }
        }
      },
      tooltip: {
        shared: false,
        intersect: true,
        y: {
          formatter: function(y) {
            if (
              typeof y != 'undefined' &&
              getChartName(chartId).includes('Profit %')
            ) {
              return y.toFixed(1) + '%';
            }
            if (
              (typeof y != 'undefined' && datatype == 'lbrprofit') ||
              datatype == 'lbrsale' ||
              datatype == 'prtscost' ||
              datatype == 'lbrsale' ||
              datatype == 'prtsprofit' ||
              datatype == 'prtssale' ||
              datatype == 'elr' ||
              datatype == 'partscost' ||
              datatype == 'totalrevenue'
            ) {
              // return (
              //   '$' +
              //   (Math.round(y * 100 + Number.EPSILON) / 100).toLocaleString()
              // );
              return (
                ' $' +
                y
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              );
            }
            if (getChartName(chartId).includes('Markup')) {
              return y.toFixed(4);
            } else if (getChartName(chartId).includes('%')) {
              return (
                (Math.round(y * 100 + Number.EPSILON) / 100)
                  .toFixed(1)
                  .toLocaleString() + '%'
              );
            } else if (
              getChartName(chartId).includes('Revenue') ||
              getChartName(chartId).includes('Profit') ||
              getChartName(chartId).includes('Rate')
            ) {
              return ' $' + y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else if (datatype == 'lbrsoldhours') {
              return y
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            } else return y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          }
        }
      },
      legend: {
        show: true,
        showForSingleSeries: false,
        showForNullSeries: true,
        showForZeroSeries: true,
        position: 'bottom',
        horizontalAlign: 'center',
        floating: false,
        fontSize: '14px',
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        fontWeight: 400,
        formatter: undefined,
        inverseOrder: false,
        width: undefined,
        height: undefined,
        tooltipHoverFormatter: undefined,
        customLegendItems: [],
        offsetX: 45,
        offsetY: 5
      }
    };

    setchartOptions(options);
    setSeries(seriesData);
    setTimeout(() => {
      setLoading(false);
    }, 200);
  };
  return (
    <Paper
      square
      className={classes.paperContainer}
      style={
        window.location.pathname == '/MyFavorites'
          ? { height: 380, margin: 0 }
          : isFrom == 'detail_page'
          ? { margin: 0 }
          : { margin: 4 }
      }
    >
      {isLoading == true ? (
        <Grid
          justify="center"
          style={{
            height: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <Card
          bordered={false}
          style={{
            height: '100%',
            textAlign: 'left',
            cursor: 'pointer',
            borderRadius: 0,
            border: '1px solid #003d6b'
          }}
          id={'chart-' + chartId}
        >
          <CardHeader
            style={
              typeof parentCallback == 'undefined'
                ? { padding: 18, paddingLeft: 20 }
                : { padding: 15 }
            }
            title={getChartName(chartId)}
            action={
              <MoreActions
                removeFavourite={removeFavourite}
                setActions={setactions}
                chartId={chartId}
                month1={month1}
                month2={month2}
                type={type}
                chartPopup={chartPopup}
                handleClose={handleClose}
                // favoritesDisabled={true}
              ></MoreActions>
            }
            subheader={getSubHeader(chartId)}
            // style={{ borderBottom: '1px solid #003d6b' }}
          ></CardHeader>

          <Divider />
          <CardContent>
            <ReactApexChart
              options={chartoptions}
              series={series}
              type="bar"
              height={
                window.location.pathname == '/MyFavorites'
                  ? 270
                  : isFrom == 'source_page'
                  ? '100%'
                  : type == 'popup'
                  ? 500
                  : window.location.pathname == '/GraphDetailsView'
                  ? 300
                  : 280
              }
            />
            {/* <div style={{ display: 'flex', flexWrap: 'wrap' }}>
              {advisor.map((value, index) => {
                var valueIndex = index + chartId;
                if (value != 'All' && value != ';')
                  return (
                    <div
                      className="spanC"
                      style={{
                        whiteSpace: 'nowrap',
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <span
                        id={value}
                        className={valueIndex}
                        style={{
                          marginRight: '5px',
                          width: '12px',
                          height: '12px',
                          margin: '3px',
                          backgroundColor: '#b91313',
                          display: 'block',
                          fontSize: '14px',
                          fontWeight: '400',
                          fontFamily: 'Roboto, Helvetica, Arial, sans-serif'
                        }}
                        onClick={clickLegend}
                      ></span>
                      <label className={valueIndex}>{value}</label>
                    </div>
                  );
              })}
              ;
            </div> */}
          </CardContent>
        </Card>
      )}
      <ChartDialog
        open={open}
        chartId={chartId}
        mon1={month1}
        mon2={month2}
        chartType="serviceAdvisorComparison"
        realm={localStorage.getItem('realm')}
        parentCallback={parentCallback}
        handlePopupClose={handleClose}
        checkEmpty={checkEmpty}
        chartData={advisorChartData}
      />
    </Paper>
  );
};
ColumnRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default ColumnRenderer;
