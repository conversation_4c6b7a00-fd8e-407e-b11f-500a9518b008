import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { Paper } from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import BarChartIcon from '@material-ui/icons/BarChart';
import CompareArrowsIcon from '@material-ui/icons/CompareArrows';
import { makeStyles } from '@material-ui/styles';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import ComparisonChartsGrid from './ComparisonChartsGrid';
import ServiceEfficiencyTrend from './ServiceEfficiencyTrend';
import OpCategoryComparisonChartsGrid from './OpCategoryComparisonChartsGrid';

import { traceSpan } from 'src/utils/OTTTracing';

const useStyles = makeStyles(theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
}));
const ServiceAdvisorEfficiency = props => {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const getInitialTabSelection = () => {
    if (props.tabSelection) return props.tabSelection;
    if (
      props.history &&
      props.history.location.state &&
      props.history.location.state.tabSelection
    )
      return props.history.location.state.tabSelection;
    return 'one';
  };

  const [tabSelection, setTabSelection] = useState(getInitialTabSelection());
  const [mainTabSelection] = useState(
    props.mainTabSelection ? props.mainTabSelection : 'seven'
  );

  const handleTabChange = (event, newValue) => {
    setTabSelection(newValue);
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'AdvisorMetrics TabClick',
      value: newValue,
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('AdvisorMetrics TabClick', spanAttribute);
  };

  return (
    <div>
      <Paper square style={{ margin: 8, backgroundColor: '#FFF' }}>
        <Tabs
          variant="standard"
          value={tabSelection}
          onChange={handleTabChange}
          indicatorColor="secondary"
          textColor="secondary"
          TabIndicatorProps={{ style: { display: 'none' } }}
        >
          <Tab
            style={{ textTransform: 'none' }}
            label={
              <div>
                <BarChartIcon style={{ verticalAlign: 'middle' }} />
                13 Month Trend
              </div>
            }
            id={'13 Month Trend'}
            value="one"
            className={tabSelection === 'one' ? classes.tabSelected : null}
          />
          <Tab
            style={{ textTransform: 'none' }}
            label={
              <div>
                <CompareArrowsIcon style={{ verticalAlign: 'middle' }} />
                Comparison by Month
              </div>
            }
            value="two"
            id={'Comparison by Month'}
            className={tabSelection === 'two' ? classes.tabSelected : null}
          />
          <Tab
            style={{ textTransform: 'none' }}
            label={
              <div>
                <CompareArrowsIcon style={{ verticalAlign: 'middle' }} />
                Category by Month
              </div>
            }
            value="three"
            id={'Category by Month'}
            className={tabSelection === 'three' ? classes.tabSelected : null}
          />
        </Tabs>
      </Paper>

      {tabSelection === 'one' && (
        <ServiceEfficiencyTrend
          props={props}
          parentCallback={props.parentCallback}
          removeFav={props.removeFav}
          tabSelection={tabSelection}
          mainTabSelection={mainTabSelection}
          session={props.session}
        />
      )}
      {tabSelection === 'two' && (
        <ComparisonChartsGrid
          mon1={
            props.history && props.comparisonMonth1 === undefined
              ? props.mon2
              : props.mon1
          }
          mon2={
            props.history && props.comparisonMonth2 === undefined
              ? props.mon1
              : props.mon2
          }
          props={props}
          parentCallback={props.parentCallback}
          realm={props.realm}
          removeFav={props.removeFav}
          tabSelection={tabSelection}
          mainTabSelection={mainTabSelection}
          session={props.session}
        />
      )}
      {tabSelection === 'three' && (
        <OpCategoryComparisonChartsGrid
          props={props}
          mon1={props.mon1}
          parentCallback={props.parentCallback}
          realm={props.realm}
          removeFav={props.removeFav}
          tabSelection={tabSelection}
          mainTabSelection={mainTabSelection}
        />
      )}
    </div>
  );
};

export default ServiceAdvisorEfficiency;
