import {
  Grid,
  Paper,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@material-ui/core';
import React, { useEffect, useState } from 'react';
import AdvisorChartRenderer from './AdvisorChartRenderer';
import { makeStyles } from '@material-ui/core/styles';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import Alert from '@material-ui/lab/Alert';
import { getStoreIdFilter } from 'src/components/ViewGraphDetailsAction';
import { useSelector, useDispatch } from 'react-redux';
import clsx from 'clsx';
import 'src/styles.css';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

const ServiceEfficiencyTrend = ({
  props,
  removeFav,
  parentCallback,
  resultSet,
  tabSelection,
  mainTabSelection
}) => {
  const classes = useStyles();
  const [checkEmpty, setCheckEmpty] = useState(false);
  const session = useSelector(state => state.session);
  const [technician, settechnician] = useState([]);
  const [technicianSelected, settechnicianSelected] = useState([]);

  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));

  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Service Advisor Perfomance' && item.parentId == null
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  useEffect(() => {
    if (
      typeof props != 'undefined' &&
      typeof props.technicianSelected != 'undefined'
    ) {
      settechnicianSelected([props.technicianSelected]);
    }
  }, [session.serviceAdvisor]);
  const handleChange = val => {
    setCheckEmpty(val);
  };
  const handleCallback = val => {
    if (val) {
      if (session.serviceAdvisor[0] == 'All') {
        //if (technicianSelected == '' || technicianSelected == 'All') {
        alert('Choose any Service Advisor ');
      } else {
        let data = {
          type: 'advisortrendingcharts',
          person: session.serviceAdvisor,
          month: val.month,
          chartName: val.chartName
        };
        parentCallback(data);
      }
    }
  };
  const handleTechChange = event => {
    settechnicianSelected(event.target.value);
  };
  const handleClosePopup = value => {
    console.log('state===handleClosePopup');
  };
  useEffect(() => {
    // getAllServiceAdvisors(result => {
    //   if (result.data.statelessCcPhysicalRwGetServiceAdvisors.nodes) {
    //     settechnician(
    //       result.data.dmsPhysicalRwGetTblServiceAdvisors.nodes.map(e =>
    //         e
    //           ? e.name +
    //             ' [' +
    //             e.serviceadvisor.toString() +
    //             ']' +
    //             '-status-' +
    //             e.active
    //           : ''
    //       )
    //     );
    //   }
    // });
  }, [session.serviceAdvisor]);
  localStorage.setItem('selectedAdvisorName', technicianSelected);
  localStorage.setItem(
    'selectedAdvisor',
    technicianSelected != '' && technicianSelected != 'All'
      ? technicianSelected
          .split('[')[1]
          .split(']')[0]
          .toString()
      : 'All'
  );
  return (
    <Grid container spacing={12} className={classes.gridContainer}>
      {checkEmpty ? (
        <Grid container spacing={12}>
          <Grid item xs={12}>
            <Paper square className={classes.alertGrid}>
              {/* {checkEmpty ? ( */}
              <Alert severity="info" className={classes.alert}>
                Selected advisor have no data found during the time period{' '}
              </Alert>
              {/* ) : null} */}

              {/* <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                Service Advisor
              </InputLabel>
              <Select
                margin="dense"
                variant="outlined"
                label="Service Advisor"
                name="Service Advisor"
                value={technicianSelected == '' ? 'All' : technicianSelected}
                onChange={handleTechChange}
              >
                <MenuItem value="All"> All </MenuItem>
                {technician.length
                  ? technician.map(val => (
                      <MenuItem
                        value={val.split('-status-')[0]}
                        style={{
                          backgroundColor:
                            val.split('-status-')[1] == 1
                              ? Dealer === 'Armatus'
                                ? '#ddeaf4'
                                : '#F4E1E7'
                              : ''
                        }}
                      >
                        {val.split('-status-')[0]}
                      </MenuItem>
                    ))
                  : null}
                Í
              </Select>
            </FormControl> */}
            </Paper>
          </Grid>
        </Grid>
      ) : null}
      {orderedData.map((item, index) => (
        <Grid
          item
          xs={6}
          id={'chart-' + item.chartId}
          className={clsx('diagram-section')}
        >
          <AdvisorChartRenderer
            chartId={parseInt(item.chartId)}
            title={index + 1}
            removeFav={removeFav}
            // advisor={
            //   technicianSelected != '' && technicianSelected != 'All'
            //     ? technicianSelected
            //         .split('[')[1]
            //         .split(']')[0]
            //         .toString()
            //     : 'All'
            // }
            tabSelection={tabSelection}
            mainTabSelection={mainTabSelection}
            serviceAdvisor={session.serviceAdvisor}
            advisorName={technicianSelected}
            checkEmpty={handleChange}
            isFrom={'source_page'}
            handleClosePopup={handleClosePopup}
            parentCallback={handleCallback}
            session={session}
          />
        </Grid>
      ))}
    </Grid>
  );
};
const useStyles = makeStyles({
  formControl: {
    padding: 4,
    minWidth: 250
  },
  gridContainer: {
    padding: 4
  },
  alertGrid: {
    margin: 4,
    padding: 4
  },
  alert: {
    fontSize: 14,
    fontWeight: 'bold'
  }
});
export default ServiceEfficiencyTrend;
