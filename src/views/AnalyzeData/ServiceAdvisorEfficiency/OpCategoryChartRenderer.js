import {
  CircularProgress,
  Grid,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import React, { useEffect, useState } from 'react';
import ReactApexChart from 'react-apexcharts';
import { getServiceAdvisorEfficiencyByOpCategory } from 'src/utils/hasuraServices';
import MoreActions from 'src/components/MoreActions';
import { useHistory } from 'react-router';
import {
  getChartName,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import PropTypes from 'prop-types';
import { getComparisonMonths } from 'src/utils/Utils';
import ChartDialog from 'src/components/Dialog';
import moment from 'moment';
import { makeStyles } from '@material-ui/core/styles';
import { useSelector } from 'react-redux';
import { logDOM } from '@testing-library/react';

import clsx from 'clsx';
import 'src/styles.css';
var lodash = require('lodash');
const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  cardContainer: {
    height: '100%',
    textAlign: 'left',
    cursor: 'pointer',
    borderRadius: 0,
    border: '1px solid #003d6b'
  }
});
const OpCategoryChartRenderer = ({
  datatype,
  removeFav,
  month1,
  month2,
  type,
  chartId,
  handleClosePopup,
  isFrom,
  parentCallback,
  checkEmpty,
  propsData,
  advisorChartData
}) => {
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  const [mon, setMonth] = useState(month1);
  const history = useHistory();
  const session = useSelector(state => state.session);
  var ServiceAdvisorArr = session.serviceAdvisor;
  useEffect(() => {
    setLoading(true);

    if (isFrom == 'ServiceAdvisorEfficiency') {
      if (advisorChartData.length > 0) {
        const chartType = getDataType(Number(chartId)); // 'workmix', 'elr', or 'jobcount'

        const transformedData = transformComparisonData(advisorChartData);

        const chartData = transformedData[chartId];
        setCHartData(chartData);
      }
    } else {
      getDataforColumnChart();
    }
  }, [datatype, ServiceAdvisorArr, advisorChartData]);
  const removeFavourite = val => {
    removeFav(val);
  };
  const setCHartData = resultArr => {
    let filteredArr = [];
    if (ServiceAdvisorArr.includes('All') == false) {
      resultArr.map(obj => {
        if (ServiceAdvisorArr.indexOf(obj.serviceadvisor) != -1) {
          filteredArr.push(obj);
          // checkEmpty(false);
        }
      });

      processData(filteredArr);
      if (filteredArr.length < 1 || filteredArr[0].mon1 == '0.00') {
        checkEmpty(true);
      } else {
        checkEmpty(false);
      }
      setLoading(false);
    } else {
      processData(resultArr);
      if (isFrom != 'detail_page' && type != 'popup') {
        checkEmpty(false);
      }
      setLoading(false);
    }
  };
  const transformComparisonData = workmixData => {
    const result = {};

    if (workmixData.length > 0) {
      const datasets = workmixData[0].datasets;

      // Find base datasets
      const advisorIds = datasets.find(d => d.label === 'serviceadvisor');
      const advisorName = datasets.find(d => d.label === 'serviceadvisor_name');

      // Group datasets by chartId
      const chartGroups = {};
      datasets.forEach(dataset => {
        if (dataset.chartId) {
          if (!chartGroups[dataset.chartId]) {
            chartGroups[dataset.chartId] = {};
          }
          chartGroups[dataset.chartId][dataset.label] = dataset;
        }
      });

      // Transform each chartId group
      Object.keys(chartGroups).forEach(chartId => {
        const group = chartGroups[chartId];
        const compDataset = Object.values(group).find(d =>
          d.label.includes('_competitive')
        );
        const maintDataset = Object.values(group).find(d =>
          d.label.includes('_maintenance')
        );
        const repairDataset = Object.values(group).find(d =>
          d.label.includes('_repair')
        );

        if (compDataset && maintDataset && repairDataset) {
          result[chartId] = advisorIds.data.map((code, index) => ({
            serviceadvisor: code,
            serviceadvisorName: advisorName.data[index],
            competitive: compDataset.data[index],
            maintenance: maintDataset.data[index],
            repair: repairDataset.data[index]
          }));
        }
      });
    }

    return result;
  };
  const getDataforColumnChart = () => {
    if (datatype == '') {
      month1 = month1 ? month1 : getComparisonMonths()[1];

      datatype = getDataType(Number(chartId));
    }

    getServiceAdvisorEfficiencyByOpCategory(month1, datatype, result => {
      if (
        result.data
          .statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategoryByMonth
          .statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategories
      ) {
        var resultArr =
          result.data
            .statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategoryByMonth
            .statelessDbdPeopleMetricsServiceAdvisorRevenueOpcategories;

        setCHartData(resultArr);
      } else {
        setLoading(false);
      }
    });
  };

  const getDataType = chartId => {
    var datatype = '';
    switch (chartId) {
      case 1299:
        datatype = 'totalrevenue';
        break;
      case 1300:
        datatype = 'lbrsale';
        break;
      case 1301:
        datatype = 'prtssale';
        break;
      case 1302:
        datatype = 'lbrsoldhours';
        break;
      case 1303:
        datatype = 'rocount';
        break;
      case 1304:
        datatype = 'jobcount';
        break;
      case 1305:
        datatype = 'lbrprofit';
        break;
      case 1306:
        datatype = 'prtsprofit';
        break;
      case 1307:
        datatype = 'elr';
        break;
      case 1308:
        datatype = 'markup';
        break;
    }
    return datatype;
  };
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=' + chartId.toString() + '?chartId=' + chartId,
        state: {
          month1: month1,
          month2: month2,
          datatype: datatype,
          tabSelection: 'eight',
          selectedSubTab: 'three',
          SelectedLocation: window.location.pathname,
          parent:
            propsData &&
            propsData.history &&
            propsData.history.location &&
            propsData.history.location.state &&
            propsData.history.location.state.parent
              ? propsData.history.location.state.parent
              : ''
        },
        SelectedLocation: window.location.pathname
      });
    }
  };

  const processData = data => {
    let orderedData = lodash.orderBy(
      data,
      ['serviceadvisor', 'repair'],
      ['asc', 'desc']
    );

    var lbrserviceadvisorArray = orderedData.map(val => val.serviceadvisor);
    const seriesData = [
      {
        name: 'Competitive',
        data: orderedData.map(e => Math.max(0, e.competitive))
      },
      // { name: 'Repair', data: orderedData.map(e => Math.max(0, e.repair)) },
      {
        name: 'Maintenance',
        data: orderedData.map(e => Math.max(0, e.maintenance))
      },
      { name: 'Repair', data: orderedData.map(e => Math.max(0, e.repair)) }
    ];
    const chartName = getChartName(chartId);
    const options = {
      chart: {
        events: {
          dataPointSelection: function(event, chartContext, config) {
            let data = {
              type: 'advisorcomparison',
              isType: 'opcategory',
              serviceadvisor: lbrserviceadvisorArray[config.dataPointIndex],
              month: month1,
              chartLoc:
                history.location &&
                history.location.state &&
                history.location.state.chartLoc
                  ? history.location.state.chartLoc
                  : '',
              prevPath:
                history.location &&
                history.location.state &&
                history.location.state.prevPath
                  ? history.location.state.prevPath
                  : ''
            };
            if (typeof parentCallback == 'undefined') {
              history.push({
                pathname: '/ServiceAdvisorPerformance',
                state: {
                  tabselection: 'eleven',
                  month_year: data.month,
                  type: 'opcategory',
                  isFrom: 'advisorcomparison',
                  advisorName: data.serviceadvisor,
                  parent: 'advisorcomparison',
                  chartLoc:
                    history.location &&
                    history.location.state &&
                    history.location.state.chartLoc
                      ? history.location.state.chartLoc
                      : '',
                  prevPath:
                    history.location &&
                    history.location.state &&
                    history.location.state.prevPath
                      ? history.location.state.prevPath
                      : ''
                }
              });
            } else {
              parentCallback(data);
            }
          }
        },
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        }
      },
      dataLabels: {
        enabled: false
      },
      // title: {
      //   text: getGraphName(datatype),
      //   align: 'left'
      // },
      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      theme: {
        palette: 'palette8' // upto palette10
      },
      plotOptions: {
        bar: {
          horizontal: true
        }
      },
      xaxis: {
        title: {
          text: chartName.includes('Revenue')
            ? 'Revenue'
            : chartName.includes('%')
            ? 'Percentage %'
            : chartName.includes('Hours')
            ? 'Hours'
            : chartName.includes('Count')
            ? 'Count'
            : chartName.includes('Gross')
            ? 'Profit '
            : chartName.includes('Markup')
            ? 'Markup'
            : chartName.includes('Rate')
            ? 'Rate'
            : ''
        },
        categories: orderedData.map(val =>
          val.serviceadvisorName.replace('[', ' [')
        ),
        labels: {
          formatter: function(value) {
            if (getChartName(chartId).includes('Markup')) {
              return (
                Math.round(value * 100 + Number.EPSILON) / 100
              ).toLocaleString();
            } else if (getChartName(chartId).includes('%')) {
              return (
                (
                  Math.round(value * 100 + Number.EPSILON) / 100
                ).toLocaleString() + ' %'
              );
            } else if (
              getChartName(chartId).includes('Revenue') ||
              getChartName(chartId).includes('Profit') ||
              getChartName(chartId).includes('Rate')
            ) {
              if (value && Number.isInteger(value)) {
                return (
                  ' $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                );
              } else {
                return (
                  ' $' +
                  value
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                );
              }
            } else
              return (
                Math.round(value * 100 + Number.EPSILON) / 100
              ).toLocaleString();
          }
        }
      },
      tooltip: {
        shared: false,
        intersect: true,
        y: {
          formatter: function(y) {
            if (getChartName(chartId).includes('Markup')) {
              return y.toFixed(4);
            } else if (getChartName(chartId).includes('%')) {
              return (
                (Math.round(y * 100 + Number.EPSILON) / 100)
                  .toFixed(1)
                  .toLocaleString() + ' %'
              );
            } else if (
              getChartName(chartId).includes('Revenue') ||
              getChartName(chartId).includes('Profit') ||
              getChartName(chartId).includes('Rate')
            ) {
              return (
                ' $' +
                y
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              );
            } else
              return (
                Math.round(y * 100 + Number.EPSILON) / 100
              ).toLocaleString();
          }
        }
      }
    };

    setchartOptions(options);
    setSeries(seriesData);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  return (
    <Paper square className={classes.paperContainer}>
      {isLoading == true ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <div>
          <Card
            bordered={false}
            className={classes.cardContainer}
            id={'chart-' + chartId}
          >
            <CardHeader
              style={
                typeof parentCallback == 'undefined'
                  ? {
                      padding: 18,
                      paddingLeft: 20,
                      borderBottom: '1px solid #003d6b'
                    }
                  : { padding: 15, borderBottom: '1px solid #003d6b' }
              }
              title={getChartName(chartId)}
              action={
                <MoreActions
                  removeFavourite={removeFavourite}
                  setActions={setactions}
                  chartId={chartId}
                  month1={month1}
                  type={type}
                  chartPopup={chartPopup}
                  handleClose={handleClose}
                  //favoritesDisabled={true}
                ></MoreActions>
              }
              subheader={getSubHeader(chartId)}
            ></CardHeader>

            <Divider />
            <CardContent>
              <ReactApexChart
                options={chartoptions}
                series={series}
                type="bar"
                height={
                  window.location.pathname == '/MyFavorites'
                    ? 190
                    : isFrom == 'source_page'
                    ? '100%'
                    : type == 'popup'
                    ? 500
                    : window.location.pathname == '/GraphDetailsView'
                    ? 300
                    : 280
                }
              />
              <ChartDialog
                open={open}
                chartId={chartId}
                chartType="opCategoryChartRenderer"
                mon1={month1}
                realm={localStorage.getItem('realm')}
                parentCallback={parentCallback}
                handlePopupClose={handleClose}
                checkEmpty={checkEmpty}
                chartData={advisorChartData}
              />
            </CardContent>
          </Card>
        </div>
      )}
    </Paper>
  );
};
OpCategoryChartRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default OpCategoryChartRenderer;
