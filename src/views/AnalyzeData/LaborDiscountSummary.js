import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import clsx from 'clsx';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { getDrillDownDataForDiscountSummary } from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { MasterDetailModule } from '@ag-grid-enterprise/master-detail';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getLastThreeYears, getLast13Months } from 'src/utils/Utils';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import { ReactSession } from 'react-client-session';
import { getComparisonMonths } from 'src/utils/Utils';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

class LaborDiscountSummary extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }

  // componentDidUpdate(prevProps) { console.log("resetReport=",localStorage.getItem('selectedStoreId'),prevProps.session.storeSelected,this.props.session.storeSelected);
  // if(prevProps.session.storeSelected && this.props.session.storeSelected){
  //   if (prevProps.session.storeSelected != this.props.session.storeSelected) {
  //     const dateFormat = item => moment(item).format('YYYY-MM');
  //     var initialQueryMonth = dateFormat(this.props.monthYear);
  //     var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
  //       ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
  //       : this.props.serviceAdvisor;
  //       // this.resetRawData();
  //       this.getAgGridData(initialQueryMonth, initialServiceAdvisor);
  //   }
  // }
  // }

  // componentWillMount() {
  //   this.setState({ serviceAdvisors: ['All'] });
  //   this.setState({ store: localStorage.getItem('selectedStoreId') });
  // }
  // componentDidUpdate() {
  //   const dateFormat = item => moment(item).format('YYYY-MM');
  //   var initialQueryMonth = dateFormat(this.props.monthYear);
  //   var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
  //     ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
  //     : this.props.serviceAdvisor;
  //   if(ReactSession.get("serviceAdvisors") != undefined) {
  //     var checkStatus = lodash.isEqual(
  //       this.state.serviceAdvisors,
  //       ReactSession.get("serviceAdvisors")
  //     );
  //     if(checkStatus == false) {
  //       this.getAgGridData(initialQueryMonth, initialServiceAdvisor);
  //     }
  //   }
  //   if(ReactSession.get("selectedStoreId") != undefined) {
  //     var checkSt = lodash.isEqual(
  //       this.state.store,
  //       ReactSession.get("selectedStoreId")
  //     );
  //     if(checkSt == false ) {
  //       this.setState({ isLoading: true });
  //       this.setState({ store: localStorage.getItem('selectedStoreId') });
  //       this.getAgGridData(initialQueryMonth, initialServiceAdvisor);
  //     }
  //   }
  // }

  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       const dateFormats = item => moment(item).format('YYYY-MM');
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData(dateFormats(this.props.monthYear),this.props.serviceAdvisor.split('[')[1]);
  //     }
  componentDidUpdate() {
    if (this.state.resetReport != this.props.resetReport) {
      this.setState({ isLoading: true });
      this.handleResetData();
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        let data = {
          type: 'laborDiscount',
          month_year: this.props.monthYear,
          serviceAdvisor: 'All'
        };
        this.props.parentCallback(data);
        this.setState({ isLoading: true });

        this.setState({ selectedServiceAdvisor: 'All' });
        const dateFormats = item => moment(item).format('YYYY-MM');
        this.getAgGridData(dateFormats(this.props.monthYear), 'All');
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');

    var initialQueryMonth =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.discountMonthYear
        ? this.props.history.location.state.discountMonthYear
        : this.props.monthYear
        ? this.props.monthYear
        : this.state.selectedMonthYear;
    var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
      ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
      : this.props.serviceAdvisor;
    this.state = {
      queryMonth: initialQueryMonth,
      resetReport: this.props.resetReport,
      selectedMonthYear: initialQueryMonth,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: initialServiceAdvisor,
      isLoading: true,
      rawGridApi: {},
      gridApi: {},
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      isColumnFiltered: false,
      columnDefs: [
        {
          headerName: 'Month',
          field: 'monthYear',
          chartDataType: 'category',
          width: 100,
          // onCellClicked: this.handleCellClicked,
          suppressMenu: true,

          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          valueFormatter: this.formatCellValueMonthYear
          // cellRenderer: function(params) {
          //   if (params.value != undefined)
          //     return `<a style="cursor: pointer">${moment(params.value).format(
          //       'MM-YYYY'
          //     )}</a>`;
          // }
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          // chartDataType: 'category',
          width: 180,
          minWidth: 80,
          flex: 1,
          suppressMenu: true,
          sortable: true,
          unSortIcon: true,
          onCellClicked: this.handleCellClicked,

          cellRenderer: function(params) {
            if (params.value != undefined)
              return `<a style="cursor: pointer">${params.value}</a>`;
          },
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              border: ' 0px white'
            };
          }
          // comparator: function(valueA, valueB, nodeA, nodeB, isInverted) {
          //   if (valueA == null && valueB == null) {
          //     return 0;
          //   }
          //   if (valueA == null) {
          //     return -1;
          //   }
          //   if (valueB == null) {
          //     return 1;
          //   }

          //   // Parse as floats if they are numbers, otherwise compare as strings
          //   let numberA = parseFloat(valueA);
          //   let numberB = parseFloat(valueB);

          //   if (!isNaN(numberA) && !isNaN(numberB)) {
          //     return numberA - numberB;
          //   }

          //   // Compare as case-insensitive strings
          //   return valueA
          //     .toString()
          //     .toLowerCase()
          //     .localeCompare(valueB.toString().toLowerCase());
          // }
          // filter: 'agTextColumnFilter'
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          chartDataType: 'category',
          // filter: 'agTextColumnFilter',
          width: 300,
          minWidth: 200,
          flex: 1,
          suppressMenu: true,

          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },
          resizable: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 180,
          minWidth: 150,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          // filter: 'agNumberColumnFilter',
          suppressMenu: true,
          suppressToolPanel: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // }
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsaleforeachdiscountedjobs',
          chartDataType: 'category',
          width: 110,
          minWidth: 60,
          flex: 1,
          cellStyle: this.cellStyles(),
          valueFormatter: this.formatCellValue,

          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Labor Discount',
          field: 'eachlabordiscount',
          chartDataType: 'category',
          width: 110,
          minWidth: 60,
          flex: 1,
          cellStyle: this.cellStyles(),
          valueFormatter: this.formatCellValueDiscount,
          // filter: 'agNumberColumnFilter',
          suppressMenu: true,

          unSortIcon: true,
          comparator: function(valueA, valueB) {
            // Compare the values directly to account for negative signs
            return valueA - valueB;
          }
          // comparator: function(valueA, valueB) {
          //   return Math.abs(valueA) - Math.abs(valueB);
          // }
        },
        {
          headerName: 'Net Sale',
          field: 'netlaborsale',
          cellStyle: this.cellStyles(),
          chartDataType: 'category',
          width: 110,
          minWidth: 60,
          flex: 1,
          valueFormatter: this.formatCellValue,
          suppressMenu: true,

          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Coupon Margin',
          field: 'singlecouponmargin',
          width: 110,
          minWidth: 60,
          flex: 1,
          cellStyle: this.cellStyles(),
          chartDataType: 'category',
          suppressMenu: true,

          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      rowData: [],
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        filter: true,
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        resizable: false,
        suppressMovable: false
      }
    };
  }
  componentDidMount() {
    window.sortState = {};
    window.filterState = {};
  }
  resetRawData = () => {
    this.setState({ resetReport: this.props.resetReport });
    this.props.handleResetReport();
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    window.filterStateDisLbr = {};
    window.sortStateDisLbr = {};
  };
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

      // return (
      //   '$' + params.value.toLocaleString()
      //   // //  Math.round(
      //   //     (params.value + Number.EPSILON) * 100 / 100
      //   // ).toLocaleString()
      // );
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '($' +
            params.value.toLocaleString() +
            // Math.abs(
            //   Math.round((params.value + Number.EPSILON) * 100) / 100
            // ).toLocaleString() +
            ')'
        : '(-$' +
            Math.abs(params.value).toLocaleString() +
            // Math.abs(
            //   Math.round((params.value + Number.EPSILON) * 100) / 100
            // ).toLocaleString() +
            ')';
    }
  };

  handleCellClicked = params => {
    window.sortStateDisLbr = this.gridApi.getSortModel();
    window.colStateDisLbr = this.state.gridcolumnApi.getColumnState();
    window.filterStateDisLbr = this.gridApi.getFilterModel();
    if (this.state.selectedServiceAdvisor == 'All') {
      let data = {
        type: 'labordiscount_dilldown',
        month_year: params.data.monthYear,
        discount_id: params.data.disdiscountid,
        isPartsCharts: false
      };
      this.props.parentCallback(data);
      console.log('sd', this.props.parentCallback(data));
    } else {
      let data = {
        type: 'labordiscount_dilldown_sa',
        month_year: params.data.monthYear,
        discount_id: params.data.disdiscountid,
        serviceAdvisor: params.data.serviceadvisor,
        advisorName: this.state.selectedServiceAdvisor,
        isPartsCharts: false
      };
      this.props.parentCallback(data);
    }
  };

  onColumnVisible = event => {
    if (this.state.selectedServiceAdvisor == 'All') {
      const groupColumnNamed = this.state.groupColumn;

      const fieldToRemove = 'advisorName';
      var columnDefs = groupColumnNamed.filter(
        column => column.field !== fieldToRemove
      );

      this.state.rawGridApi.setColumnDefs(columnDefs);
    }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.gridApi = params.api;

    this.gridColumnApi = params.columnApi;

    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridcolumnApi: params.columnApi });
    this.setState({
      groupColumn: this.state.rawGridApi.columnController.columnDefs
    });
    if (this.props.history.location.state == undefined) {
      window.sortStateDisLbr = {};
      window.filterStateDisLbr = {};
    }
    this.gridApi.setSortModel(window.sortStateDisLbr);
    this.gridApi.setFilterModel(window.filterStateDisLbr);

    params.api.addEventListener('columnVisible', this.onColumnVisible);

    // this.gridApi.sizeColumnsToFit();
    this.getAgGridData(this.state.queryMonth, this.state.serviceAdvisor);
  };
  getAgGridData(queryMonth, serviceAdvisor) {
    //if (this.state.selectedServiceAdvisor != 'All') {
    const groupColumn = this.state.groupColumn;
    if (serviceAdvisor != 'All') {
      groupColumn[3]['hide'] = false;
      groupColumn[3]['suppressToolPanel'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateDisLbr && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateDisLbr);
      }

      //  this.state.rawGridApi.sizeColumnsToFit();
    } else {
      groupColumn[3]['hide'] = true;
      groupColumn[3]['suppressToolPanel'] = true;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateDisLbr && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateDisLbr);
      }
      //  this.state.rawGridApi.sizeColumnsToFit();
    }
    serviceAdvisor = serviceAdvisor
      ? serviceAdvisor
      : this.state.serviceAdvisor;
    var SAAll = [];
    let DrilldownValues = 0;
    let resultSet = [];
    getDrillDownDataForDiscountSummary(queryMonth, serviceAdvisor, result => {
      this.setState({ isLoading: false });
      if (serviceAdvisor == 'All') {
        if (result.data.statelessDbdDiscountGetLaborDiscountSummary.nodes) {
          resultSet = this.parseArray(
            result.data.statelessDbdDiscountGetLaborDiscountSummary.nodes
          );
          this.setState({
            rowData: resultSet
          });
          DrilldownValues = this.getSummaryCalculation(resultSet);
          if (window.filterStateDisLbr != undefined) {
            this.filterByValue();
          }
        }

        const groupColumnNamed = this.state.groupColumn;

        const fieldToRemove = 'advisorName';
        var columnDefs = groupColumnNamed.filter(
          column => column.field !== fieldToRemove
        );

        this.state.rawGridApi.setColumnDefs(columnDefs);
      } else {
        if (
          result.data
            .statelessDbdDiscountGetLaborDiscountSummaryByServiceAdvisor
            .laborDiscountSummaryByServiceAdvisorDetails
        ) {
          resultSet = this.parseArray(
            result.data
              .statelessDbdDiscountGetLaborDiscountSummaryByServiceAdvisor
              .laborDiscountSummaryByServiceAdvisorDetails
          );
          // console.log("service",this.state.selectedServiceAdvisor.split('[')[1].split(']')[0])
          let filteredResult = resultSet.filter(
            item =>
              item.serviceadvisor ==
                this.state.selectedServiceAdvisor.split('[')[1].split(']')[0] &&
              item.monthYear == this.state.selectedMonthYear
          );
          console.log('filteredResult', filteredResult);
          this.setState({
            rowData: filteredResult
          });
          DrilldownValues = this.getSummaryCalculation(filteredResult);
          if (window.filterStateDisLbr != undefined) {
            this.filterByValue();
          }
        }
      }
      this.getMonthYear();
      getAllServiceAdvisors(result => {
        if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
          var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
            e => e.active == 1
          );
          const updatedAdvisors = advArr.map(advisor => ({
            ...advisor,
            nickname: advisor.nickname || advisor.name
          }));
          const sortedAdvisors = updatedAdvisors.sort((a, b) =>
            a.nickname.localeCompare(b.nickname)
          );

          SAAll = ['All'].concat(
            sortedAdvisors.map(e =>
              e
                ? (e.nickname ? e.nickname : e.name) +
                  ' [' +
                  e.serviceadvisor.toString() +
                  ']' +
                  '-status-' +
                  e.active
                : ''
            )
          );
          this.setState({ serviceAdvisors: SAAll });
        }
      });
    });
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateDisLbr);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  parseArray(filtererdArray) {
    let intArray = [];
    var res = filtererdArray.map(v => {
      intArray.push({
        discountedlbrsale: v.discountedlbrsale,
        discountedvolume: v.discountedvolume,
        discountjobcount: v.discountjobcount,
        disdesc: v.disdesc,
        disdiscountid: v.disdiscountid,
        eachlabordiscount: v.eachlabordiscount
          ? parseFloat(v.eachlabordiscount)
          : 0,
        lbrsaleforeachdiscountedjobs: parseFloat(
          v.lbrsaleforeachdiscountedjobs
        ),
        monthYear: v.monthYear,
        netlaborsale: v.netlaborsale ? parseFloat(v.netlaborsale) : 0,
        overallbrcost: v.overallbrcost,
        overallbrsale: v.overallbrsale,
        overallcouponmargin: v.overallcouponmargin,
        overalldiscountinmonth: v.overalldiscountinmonth,
        overallrocount: v.overallrocount,
        singlecouponmargin: v.singlecouponmargin
          ? parseFloat(v.singlecouponmargin)
          : 0,
        cpdiscountedrocount: v.cpdiscountedrocount,
        serviceadvisor: v.serviceadvisor,
        advisorName: v.advisorName
      });
      // return res;
    });
    return intArray;
  }

  getSummaryCalculation = values => {
    var overallbrcost = [];
    var overallbrsale = [];
    var overallrocount = [];
    var cpdiscountedrocount = [];
    var discountedlbrsale = [];
    var discountedvolume = [];
    var discountjobcount = [];
    var overallcouponmargin = [];
    var overalldiscountinmonth = [];
    var sumDiscountedlabordiscount = 0;
    values.forEach(function(value) {
      overallbrcost.push(value.overallbrcost);
      overallbrsale.push(value.overallbrsale);
      overallrocount.push(value.overallrocount);
      cpdiscountedrocount.push(value.cpdiscountedrocount);
      discountedlbrsale.push(value.discountedlbrsale);
      discountedvolume.push(value.discountedvolume);
      discountjobcount.push(value.discountjobcount);
      overallcouponmargin.push(value.overallcouponmargin);
      overalldiscountinmonth.push(value.overalldiscountinmonth);
      if (value.eachlabordiscount) {
        sumDiscountedlabordiscount += value.eachlabordiscount;
      }
    });
    var overallbrcostmaxValue = Math.max.apply(null, overallbrcost);
    var overallbrsalemaxValue = Math.max.apply(null, overallbrsale);
    var discountedlbrsalemaxValue = Math.max.apply(null, discountedlbrsale);
    var overallcouponmarginMaxValue = Math.max.apply(null, overallcouponmargin);
    var overalldiscountinmonthMax = Math.max.apply(
      null,
      overalldiscountinmonth
    );
    this.setState({
      overallbrcostmaxValue:
        '$' +
        (
          Math.round((overallbrcostmaxValue + Number.EPSILON) * 100) / 100
        ).toLocaleString(),
      overallbrsalemaxValue:
        '$' +
        (
          Math.round((overallbrsalemaxValue + Number.EPSILON) * 100) / 100
        ).toLocaleString(),
      discountedlbrsalemaxValue:
        '$' +
        (
          Math.round((discountedlbrsalemaxValue + Number.EPSILON) * 100) / 100
        ).toLocaleString(),
      overallcouponmarginMaxValue: (
        Math.round((overallcouponmarginMaxValue + Number.EPSILON) * 100) / 100
      ).toLocaleString(),
      // overallbrsalemaxValue: overallbrsale.length > 0 ? Math.max.apply(null, overallbrsale): 0,
      overallrocountmaxValue:
        overallrocount.length > 0 ? Math.max.apply(null, overallrocount) : 0,
      cpdiscountedrocountmaxValue:
        cpdiscountedrocount.length > 0
          ? Math.max.apply(null, cpdiscountedrocount)
          : 0,
      //discountedlbrsalemaxValue:  discountedlbrsale.length > 0 ? '$' +Math.max.apply(null, discountedlbrsale): 0,
      discountedvolumemaxValue:
        discountedvolume.length > 0
          ? Math.max.apply(null, discountedvolume).toFixed(2) + '%'
          : 0,
      discountjobcountmaxValue:
        discountjobcount.length > 0
          ? Math.max.apply(null, discountjobcount)
          : 0,
      overalldiscountinmonthMax:
        '($' +
        Math.abs(
          Math.round((overalldiscountinmonthMax + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
    });
    var GPPBeforeDiscount =
      ((overallbrsalemaxValue - overallbrcostmaxValue) /
        overallbrsalemaxValue) *
      100;

    var GPPAfterDiscount = parseFloat(
      ((overallbrsalemaxValue -
        Math.abs(sumDiscountedlabordiscount) -
        overallbrcostmaxValue) /
        overallbrsalemaxValue) *
        100
    );

    this.setState({
      GppBeforeDiscount:
        overallbrsale.length > 0
          ? (
              Math.round((GPPBeforeDiscount + Number.EPSILON) * 100) / 100
            ).toFixed(1) + '%'
          : 0,
      GppAfterDiscount:
        overallbrsale.length > 0
          ? (
              Math.round((GPPAfterDiscount + Number.EPSILON) * 100) / 100
            ).toFixed(1) + '%'
          : 0
    });
  };

  setDrillDownValuesToState = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      LsWarranty: Values.LsWarranty
    });
  };
  handleMonthYearChange = event => {
    let monthYear = event.target.value;
    this.setState({
      selectedMonthYear: monthYear,
      serviceAdvisor: this.state.selectedServiceAdvisor
    });
    this.setState({ isLoading: true });
    setTimeout(
      function() {
        let data = {
          type: 'laborDiscount',
          month_year: monthYear,
          serviceAdvisor: this.state.selectedServiceAdvisor
        };
        this.props.parentCallback(data);
        this.getAgGridData(
          monthYear,
          this.state.selectedServiceAdvisor.split('[')[1]
            ? this.state.selectedServiceAdvisor.split('[')[1].split(']')[0]
            : this.state.selectedServiceAdvisor
        );
      }.bind(this),
      50
    );
  };

  handleServiceAdvisorChange = event => {
    let serviceAdvisor = event.target.value.split('[')[1]
      ? event.target.value.split('[')[1].split(']')[0]
      : event.target.value;
    this.setState({ selectedServiceAdvisor: event.target.value });
    this.setState({ isLoading: true });
    setTimeout(
      function() {
        let data = {
          type: 'laborDiscount',
          month_year: this.state.selectedMonthYear,
          serviceAdvisor: event.target.value
        };

        this.props.parentCallback(data);
        this.getAgGridData(this.state.selectedMonthYear, serviceAdvisor);
      }.bind(this),
      50
    );
  };
  handleResetData = () => {
    var monthValues =
      localStorage.getItem('versionFlag') == 'TRUE'
        ? getLastThreeYears()
        : getLast13Months();
    var monthVal = getComparisonMonths()[1];
    console.log('monthvalues', moment(monthValues[12]).format('MMM-YY'));
    this.setState({ resetReport: this.props.resetReport });
    this.props.setResetDashboard();
    // this.setState({ selectedMonthYear: monthValues[12] });
    this.setState({ selectedMonthYear: monthVal });
    this.setState({ selectedServiceAdvisor: 'All' });

    this.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.getAgGridData(
      this.state.selectedMonthYear,
      this.state.selectedServiceAdvisor
    );

    window.sortStateDisLbr = {};
    window.filterStateDisLbr = {};

    let data = {
      type: 'laborDiscount',
      month_year: monthVal,
      serviceAdvisor: 'All'
    };
    this.props.parentCallback(data);
  };

  getMonthYear = () => {
    var table = [];
    this.setState({
      discountMonthYear:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? getLastThreeYears()
          : getLast13Months()
    });
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    if (!lodash.isEmpty(filterValues)) {
      this.setState({ isColumnFiltered: true });
    } else {
      this.setState({ isColumnFiltered: false });
    }
  };
  render() {
    const { classes } = this.props;
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonths = dateFormat(this.props.monthYear);

    var monthVal = getComparisonMonths()[1];
    // monthVal = moment(monthVal).format('MMM-YY');

    return (
      <div>
        <Grid container spacing={12}>
          <Grid item xs={6} justify="flex-start" style={{ padding: '2.5px' }}>
            <Paper square classes={{ root: classes.gridContainer }}>
              <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                  Month
                </InputLabel>
                <Select
                  variant="outlined"
                  label="Group By"
                  name="group-by-type"
                  value={this.state.selectedMonthYear}
                  onChange={this.handleMonthYearChange}
                >
                  {/* <MenuItem value={this.state.selectedMonthYear}>
                    {moment(this.state.selectedMonthYear).format('MMM-YY')}
                  </MenuItem> */}
                  {typeof this.state.discountMonthYear != 'undefined' &&
                    Object.values(this.state.discountMonthYear).map(item => (
                      <MenuItem value={item}>
                        {moment(item).format('MMM-YY')}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Paper>
          </Grid>
          <Grid item xs={6} style={{ padding: '2.5px' }}>
            <Paper
              square
              justify="center"
              classes={{ root: classes.gridContainer }}
            >
              <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                  Service Advisor
                </InputLabel>
                <Select
                  variant="outlined"
                  label="Service Advisor"
                  name="group-by-type"
                  value={this.state.selectedServiceAdvisor}
                  onChange={this.handleServiceAdvisorChange}
                  style={
                    this.state.selectedServiceAdvisor == 'All'
                      ? { width: '100px' }
                      : { width: '' }
                  }
                >
                  {/* <MenuItem value={this.state.selectedServiceAdvisor}>
                      {this.state.selectedServiceAdvisor}
                  </MenuItem> */}
                  {typeof this.state.serviceAdvisors != 'undefined' &&
                    Object.values(this.state.serviceAdvisors).map(item => (
                      <MenuItem
                        value={item.split('-status-')[0]}
                        style={{
                          backgroundColor:
                            item.split('-status-')[1] == 0
                              ? Dealer === 'Armatus'
                                ? '#ddeaf4'
                                : '#F4E1E7'
                              : '',
                          color:
                            item.split('-status-')[1] == 0
                              ? Dealer === 'Armatus'
                                ? '#969592'
                                : '#F4E1E7'
                              : ''
                        }}
                      >
                        {item.split('-status-')[0]}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Paper>
          </Grid>
        </Grid>

        {this.state.isLoading && (
          <div
            style={{
              display: this.state.tabSelection != 'one' ? 'block' : 'block'
            }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}

        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              (this.state.rowData.length > 0
                ? window.innerHeight - 450
                : window.innerHeight - 380) + 'px',
            width: '1065px',
            margin: 8,
            display:
              this.state.isLoading || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            sideBar={this.state.sideBar}
            floatingFilter={true}
            enableRangeSelection={true}
            onFilterChanged={this.onFilterChanged}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressHorizontalScroll={false}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
        {!this.state.isLoading && this.state.rowData.length > 0 && (
          <Paper square style={{ margin: 6 }}>
            <div
              className={clsx(classes.root, {
                [classes.hidesummary]: this.state.isColumnFiltered
              })}
            >
              <Grid container item spacing={1} style={{ width: '1050px' }}>
                <SummaryTitle
                  title={'Overall Labor Sale'}
                  value={this.state.overallbrsalemaxValue}
                  //value={this.state.overallbrsalemaxValue != 0 ? '$' +this.state.overallbrsalemaxValue : this.state.overallbrsalemaxValue}
                />
                <SummaryTitle
                  title={'Overall Labor Cost'}
                  value={this.state.overallbrcostmaxValue}
                  //value={this.state.overallbrcostmaxValue != 0 ? '$' +this.state.overallbrcostmaxValue : this.state.overallbrcostmaxValue}
                />
                <SummaryTitle
                  style={{ margin: '20px 0' }}
                  title={'Overall Discount'}
                  value={this.state.overalldiscountinmonthMax}
                />
                <SummaryTitle
                  title={'Overall Coupon Margin'}
                  value={this.state.overallcouponmarginMaxValue}
                />
                <SummaryTitle
                  title={'Overall RO Count'}
                  value={this.state.overallrocountmaxValue}
                />
                <SummaryTitle
                  title={'Discounted RO Count'}
                  value={this.state.cpdiscountedrocountmaxValue}
                />
                <SummaryTitle
                  title={'Discounted Labor Sale'}
                  value={this.state.discountedlbrsalemaxValue}
                />

                <SummaryTitle
                  title={'Discounted Volume'}
                  value={this.state.discountedvolumemaxValue}
                />
                <SummaryTitle
                  title={'Discounted Job Count'}
                  value={this.state.discountjobcountmaxValue}
                />
                <SummaryTitle
                  title={'GP % Before Discount'}
                  value={this.state.GppBeforeDiscount}
                />
                <SummaryTitle
                  title={'GP % After Discount'}
                  value={this.state.GppAfterDiscount}
                />
              </Grid>
            </div>
          </Paper>
        )}
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    // margin: theme.spacing(1),
    // minWidth: 120
    padding: 8
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  hidesummary: {
    display: 'none'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  gridContainer: {
    display: 'flex',
    marginTop: '10px'
  }
});

export default withStyles(styles)(LaborDiscountSummary);
