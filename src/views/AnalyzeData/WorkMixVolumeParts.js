import React from 'react';
import WorkMixVolume from './WorkMixVolume';
import WorkMixVolumeVersion2 from './WorkMixVolume2.4';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
function WorkMixVolumeParts() {
  const history = useHistory();
  let category = history
    ? history.location.state
      ? history.location.state.category
      : 'parts'
    : 'parts';
  let reportType = history
    ? history.location.state
      ? history.location.state.reportType
      : 'workmix'
    : 'workmix';
  let isFrom = history
    ? history.location.state
      ? history.location.state.isFrom
      : ''
    : '';
  let comparisonMonth1 = history
    ? history.location.state
      ? history.location.state.comparisonMonth1
      : ''
    : '';
  let comparisonMonth2 = history
    ? history.location.state
      ? history.location.state.comparisonMonth2
      : ''
    : '';
  let type = history
    ? history.location.state
      ? history.location.state.type
      : ''
    : '';
  let opCategory = history
    ? history.location.state
      ? history.location.state.opCategory
      : ''
    : '';
  let isExpanded = history
    ? history.location.state
      ? history.location.state.isExpanded
      : ''
    : '';
  const session = useSelector(state => state.session);

  return (
    <Page title="Work Mix Volumes">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : localStorage.getItem('versionFlag') == 'TRUE' ? (
        <WorkMixVolume
          session={session}
          category={category}
          reportType={reportType}
          isFrom={isFrom}
          comparisonMonth1={comparisonMonth1}
          comparisonMonth2={comparisonMonth2}
          type={type}
          history={history}
          opCategory={opCategory}
          isExpanded={isExpanded}
        />
      ) : (
        <WorkMixVolumeVersion2
          session={session}
          category={category}
          reportType={reportType}
          isFrom={isFrom}
          comparisonMonth1={comparisonMonth1}
          comparisonMonth2={comparisonMonth2}
          type={type}
          history={history}
          opCategory={opCategory}
          isExpanded={isExpanded}
        />
      )}
    </Page>
  );
}

export default WorkMixVolumeParts;
