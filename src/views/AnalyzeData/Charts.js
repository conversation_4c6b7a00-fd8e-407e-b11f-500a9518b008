import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import {
  Paper,
  Typography,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Grid,
  Button,
  Checkbox
} from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import BarChartIcon from '@material-ui/icons/BarChart';
import CompareArrowsIcon from '@material-ui/icons/CompareArrows';
import SubjectIcon from '@material-ui/icons/Subject';
import { withStyles } from '@material-ui/styles';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import DiscountSummaryDrillDown from './DiscountSummaryDrillDown';
import ComparisonChartsGrid from './ComparisonCharts/ComparisonChartsGrid';
import RawDataOpcodeGrid from './Component/RawDataOpcodeGrid';
import ROLevelRawDataGrid from './Component/ROLevelRawDataGrid';
import Drilldown from './Drilldown';

import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import 'src/styles.css';
import Reports from './Reports';
import WorkMixCharts from './WorkMixCharts/WorkMixCharts';
import WorkMixVolume from './WorkMixVolume';
import DescriptionIcon from '@material-ui/icons/Description';
import TableChartIcon from '@material-ui/icons/TableChart';
import DetailsIcon from '@material-ui/icons/Details';
import ShowChartIcon from '@material-ui/icons/ShowChart';
import TechEfficiency from './TechEfficiency/TechEfficiency';
import ServiceAdvisorEfficiency from './ServiceAdvisorEfficiency/ServiceAdvisorEfficiency';
import {
  getLast13Months,
  checkClosedDateInCurrentMonth,
  getComparisonMonths
} from 'src/utils/Utils';
import TechnicianSummary from './Component/TechnicianSummary';
import TechnicianDetailedView from './Component/TechnicianDetailedView';
import ServiceAdvisorSummary from './Component/ServiceAdvisorSummary';
import { isArray } from 'validate.js';
import { withKeycloak } from '@react-keycloak/web';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import Chip from '@material-ui/core/Chip';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import $ from 'jquery';
import { faFileExcel } from '@fortawesome/free-solid-svg-icons';
import { nonExecutableDefinitionMessage } from 'graphql/validation/rules/ExecutableDefinitions';
import { traceSpan } from 'src/utils/OTTTracing';
import { TrainOutlined } from '@material-ui/icons';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import { ReactSession } from 'react-client-session';
import { connect } from 'react-redux';
import { setToggleStatus, setSelectedTech } from 'src/actions';
import ReportsVersion2 from './Reports_2.4';
import DrilldownOneLineRo from './DrilldownOneLineRo';
import { Alert } from '@material-ui/lab';

var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

class Charts extends React.Component {
  componentDidUpdate(prevProps) {
    console.log('enter technician');
    if (
      prevProps.session.storeSelected &&
      JSON.parse(localStorage.getItem('selectedStoreId'))
    ) {
      if (
        JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
        JSON.parse(prevProps.session.storeSelected)[0]
      ) {
        console.log(
          'stores=00==',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
            JSON.parse(prevProps.session.storeSelected)[0]
        );
        localStorage.getItem('showCurrentMonth', false);
        this.getDrillDownParams();
        this.resetReportGrid();
      }
    }
  }
  componentDidMount = () => {
    console.log(
      'issssfrom',
      this.props.history.location.isFrom,
      this.state.tabSelection
    );
    // getAllServiceAdvisors(result => {
    //   if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
    //     var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
    //       e => e.active == 1
    //     );
    //     this.setState({
    //       names: ['All'].concat(
    //         advArr.map(e => (e ? e.serviceadvisor.toString() : ''))
    //       )
    //     });

    //     this.setState({
    //       advisorNames: ['All'].concat(
    //         advArr.map(e =>
    //           e
    //             ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active
    //             : ''
    //         )
    //       )
    //     });
    //   }
    // });
  };

  componentDidUpdate(prevProps) {
    const { dispatch } = this.props;

    if (
      this.state.tabSelection != 'one' &&
      (this.props.history.location.pathname == '/LaborWorkMixAnalysis' ||
        this.props.history.location.pathname == '/PartsWorkMixAnalysis')
    ) {
      $('#advisorSelection').css('display', 'none');
    } else if (
      this.state.tabSelection == 'one' &&
      (this.props.history.location.pathname == '/LaborWorkMixAnalysis' ||
        this.props.history.location.pathname == '/PartsWorkMixAnalysis')
    ) {
      $('#advisorSelection').css('display', 'block');
    }
    // if (
    //   this.state.checked != this.props.session.toggleStatus &&
    //   checkClosedDateInCurrentMonth(
    //     localStorage.getItem('closedDate'),
    //     localStorage.getItem('openDate')
    //   )
    // ) {
    //   this.setState({
    //     checked: this.props.session.toggleStatus
    //   });
    //   dispatch(setToggleStatus(this.props.session.toggleStatus));
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ drilldownVal: '' });
        // if (
        //   checkClosedDateInCurrentMonth(
        //     localStorage.getItem('closedDate'),
        //     localStorage.getItem('openDate')
        //   )
        // ) {
        //   this.setState({ checked: true });
        //   dispatch(setToggleStatus(true));
        // } else {
        //   this.setState({ checked: false });
        //   dispatch(setToggleStatus(false));
        // }

        let status = checkClosedDateInCurrentMonth(
          localStorage.getItem('closedDate'),
          localStorage.getItem('openDate')
        );
        if (this.props.session.toggleStatus != undefined) {
          this.setState({ checked: this.props.session.toggleStatus });
          dispatch(setToggleStatus(this.props.session.toggleStatus));
        } else {
          this.setState({ checked: status });
          dispatch(setToggleStatus(status));
          dispatch(setSelectedTech('All[All]'));
        }
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }

  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var defaultDate = new Date();
    var selectedTab, selectedSubTab;
    if (this.props.history.location.state != undefined) {
      selectedTab = this.props.history.location.state.tabSelection;
      selectedSubTab = this.props.history.location.state.selectedSubTab;
    }
    // var lastDefMonth =
    //   this.props.keycloak.realm == 'ferrarioat_store' ? '2021-05' : '2020-12';
    var lastDefMonth = getComparisonMonths()[1];
    var initialQueryMonth = dateFormat(
      this.props.history.location.state == undefined
        ? lastDefMonth
        : this.props.history.location.state &&
          (this.props.history.location.state.isFrom == 'opcodes' ||
            this.props.history.location.state.isFrom == 'noncpopcodes')
        ? this.props.history.location.state.monthYear
        : this.props.history.location.state.y
        ? this.props.history.location.state.y
        : this.props.history.location.state.month_year
        ? Array.isArray(this.props.history.location.state.month_year)
          ? this.props.history.location.state.month
            ? this.props.history.location.state.month
            : this.props.history.location.state.month_year
          : this.props.history.location.state.month_year
        : lastDefMonth
    );
    var date = new Date(
      this.props.history.location.state == undefined
        ? new Date()
        : this.props.history.location.state.y
    );
    var type = this.props.history.location.state
      ? this.props.history.location.state.type
      : '';
    var isFrom = this.props.history.location.state
      ? this.props.history.location.state.isFrom
      : '';
    var technician = this.props.history.location.state
      ? this.props.history.location.state.techNo
      : '';
    var techName = this.props.history.location.state
      ? this.props.history.location.state.techName
      : '';
    var prevMonth = this.props.history.location.state
      ? this.props.history.location.state.prevMonth
        ? this.props.history.location.state.prevMonth
        : getComparisonMonths()[0]
      : getComparisonMonths()[0];
    if (this.props.history.location.state) {
      var drillDown = this.props.history.location.state.drillDown;
      var category = this.props.history.location.state.category;
      var chartName = this.props.history.location.state.chartName;
      var comparisonMonth1 = this.props.history.location.state
        ? this.props.history.location.state.comparisonMonth1
        : '';
      var comparisonMonth2 = this.props.history.location.state
        ? this.props.history.location.state.comparisonMonth2
        : '';
      if (
        this.props.history.location.state.drillDownType == 'workmix' &&
        this.props.history.location.state.tabSelection == 'two'
      ) {
        initialQueryMonth = this.props.history.location.state.comparisonMonth1;
        prevMonth = this.props.history.location.state.comparisonMonth2;
        comparisonMonth1 = this.props.history.location.state.comparisonMonth1;
        comparisonMonth2 = this.props.history.location.state.comparisonMonth2;
      }
    }
    //var prevMonth = dateFormat(new Date(date.setMonth(date.getMonth() - 1)));
    var serviceadvisor = this.props.history.location.state
      ? this.props.history.location.state.advisor
      : '';
    var advisorName = this.props.history.location.state
      ? this.props.history.location.state.advisorName
      : '';
    var isFrom = this.props.history.location.state
      ? this.props.history.location.state.isFrom
      : '';
    var type = this.props.history.location.state
      ? this.props.history.location.state.type
      : '';
    var parent = this.props.history.location.state
      ? this.props.history.location.state.parent
      : '';
    var serviceAdvisor =
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'searchByRo'
        ? this.props.history.location.state.advisorName
        : '';
    var techNo =
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'searchByRo'
        ? this.props.history.location.state.techNo
        : '';

    var pageType =
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'searchByRo'
        ? this.props.history.location.state.pageType
        : '';
    var reportTypeWorkmix =
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'searchByRo'
        ? this.props.history.location.state.reportType
        : 'workmix';
    var workmixParent =
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'searchByRo'
        ? this.props.history.location.state.workmixParent
        : '';
    var workmixTab =
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'searchByRo'
        ? this.props.history.location.state.workmixTab
        : '';
    var roNumber =
      this.props.history.location.state &&
      this.props.history.location.state.roNumber
        ? this.props.history.location.state.roNumber
        : '';
    var selectedFilter =
      this.props.history.location.state &&
      this.props.history.location.state.selectedFilter
        ? this.props.history.location.state.selectedFilter
        : '';
    var selectedToggle =
      this.props.history.location.state &&
      this.props.history.location.state.selectedToggle
        ? this.props.history.location.state.selectedToggle
        : this.props.history.location.selectedToggle
        ? this.props.history.location.selectedToggle
        : '';
    var page =
      this.props.history.location.state &&
      this.props.history.location.state.page
        ? this.props.history.location.state.page
        : '';
    var payTypeRo =
      this.props.history.location.state &&
      this.props.history.location.state.payTypeRo
        ? this.props.history.location.state.payTypeRo
        : 'C';
    var opcodePayType =
      this.props.history.location.state &&
      this.props.history.location.state.opcodePayType
        ? this.props.history.location.state.opcodePayType
        : 'C';
    var itemizationData =
      this.props.history.location.state &&
      this.props.history.location.state.itemizationData
        ? this.props.history.location.state.itemizationData
        : '';
    var itemizationTab =
      this.props.history.location.state &&
      this.props.history.location.state.itemizationTab
        ? this.props.history.location.state.itemizationTab
        : '';
    var isLabor =
      this.props.history.location.state &&
      this.props.history.location.state.isLabor
        ? this.props.history.location.state.isLabor
        : '';
    var roExpanded =
      this.props.history.location.state &&
      this.props.history.location.state.expanded
        ? this.props.history.location.state.expanded
        : '';
    var isReportSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.isReportSelected === true
        ? this.props.history.location.state.isReportSelected
        : false;
    this.state = {
      reloadKey: 0,
      originData: this.props.originData
        ? this.props.originData
        : this.props &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.originData
        ? this.props.history.location.state.originData
        : '',
      checked: this.props.session.toggleStatus
        ? this.props.session.toggleStatus
        : checkClosedDateInCurrentMonth(
            localStorage.getItem('closedDate'),
            localStorage.getItem('openDate')
          ),
      // this.props.session.toggleStatus &&
      // checkClosedDateInCurrentMonth(
      //   localStorage.getItem('closedDate'),
      //   localStorage.getItem('openDate')
      // )
      //   ? this.props.session.toggleStatus
      //   : false,
      chartRenderType: 'line',
      selectedTypeValue: 'labor',
      firstFilterState: false,
      secondFilterState: false,
      isLoading: true,
      chartZoomed: false,
      showCharts: false,
      tabSelection: this.handleInitialtabSelection(),
      queryMonth: initialQueryMonth,
      previousMonth: prevMonth,
      rawGridColumnApi: {},
      drillDown: drillDown,
      category: category,
      chartName: chartName,
      resetLayout: false,
      exportReport: false,
      opcode: this.handleInitialOpcodeValue(),
      reportType: this.getReportType(),
      month_year: initialQueryMonth,
      monthSelected:
        this.props &&
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.weekStart,
      isPartsCharts: this.getPartsorLaborType(),
      serviceadvisor: advisorName,
      workmixreportType: this.handleInitialWorkmixType(reportTypeWorkmix),
      technician: technician,
      isFrom: isFrom,
      chart_name: chartName,
      comparisonMonth1: comparisonMonth1,
      comparisonMonth2: comparisonMonth2,
      type: type,
      techName: techName,
      selectedSubTab: selectedSubTab,
      parent: parent,
      serviceAdvisor: serviceAdvisor,
      techNo: techNo,
      setClosedDate: localStorage.getItem('closedDate'),
      pageType: pageType,
      workmixParent: workmixParent,
      opcodePayType: opcodePayType,
      workmixTab: workmixTab,
      isReportSelected: isReportSelected,
      roNumber: roNumber,
      selectedFilter: selectedFilter,
      selectedToggle: selectedToggle,
      page: page,
      payTypeRo: payTypeRo,
      itemizationData: itemizationData,
      itemizationTab: itemizationTab,
      isLabor: isLabor,
      isExpanded: 0,
      prevMenu: this.props.session && this.props.session.menuSelected,
      roExpanded: roExpanded,
      techPageType: 'techSummary'
    };

    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'three'
    ) {
      this.setState({
        isFrom: 'workmixcharts',
        reportType: this.props.history.location.state.reportType
      });
    }
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'four'
    ) {
      this.setState({
        isFrom: 'comparisonchart',
        type: 'comparisonchart',
        workmixreportType: this.props.history.location.state.workmixreportType,
        opcode: this.props.history.location.state.opcode
      });
    }

    // this.handleInitialtabSelection();
  }
  getPartsorLaborType() {
    if (
      this.props.route.state &&
      typeof this.props.route.state.drillDownType != 'undefined'
    ) {
      if (this.props.route.state.drillDownType == 'PartsWorkMixAnalysis') {
        return true;
      }
      if (this.props.route.state.drillDownType == 'LaborWorkMixAnalysis') {
        return false;
      }
    }

    let isParts =
      this.props.history.location.state == null
        ? true
        : this.props.history.location.state.isPartsCharts;
    return isParts;
  }

  getReportType = () => {
    if (this.checkAdvisorCharts()) {
      //  return 'SA_lbrsale';
      return this.state != undefined &&
        this.state.selectedReportType != undefined &&
        this.state.selectedReportType != ''
        ? this.state.selectedReportType
        : 'SA_lbrsale';
    } else if (this.checkTechnicianCharts()) {
      // return 'techefficiency';
      return this.state != undefined &&
        this.state.selectedReportType != undefined &&
        this.state.selectedReportType != ''
        ? this.state.selectedReportType
        : 'allsoldhrs';
    } else {
      if (
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.reportType
      ) {
        return this.props.history.location.state.reportType;
      } else {
        /// return 'Sales';
        return 'Select';
      }
    }
  };
  handleInitialOpcodeValue = () => {
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'four'
    ) {
      return this.props.history.location.state.opcode;
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'five' &&
      this.props.history.location.state.pageType == 'searchByRo'
    ) {
      return this.props.history.location.state.opcode;
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'five' &&
      this.props.history.location.state.isFrom == 'opcodes'
    ) {
      return this.props.history.location.state.opcode;
    } else {
      return '';
    }
  };
  handleInitialWorkmixType = workmixreportType => {
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.opcode == 'OTHER'
    ) {
      return this.props.history.location.state.workmixreportType;
    } else {
      //return 'workmix';
      return workmixreportType;
    }
  };
  handleInitialtabSelection = () => {
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'eleven'
    ) {
      return 'eleven';
    } else if (
      this.props.route.state &&
      this.props.route.state.drillDownType &&
      this.props.route.state.drillDownType == 'ServiceAdvisorEfficiency' &&
      this.props.history.location.state == undefined
      // &&
      // localStorage.getItem("advisorTabSelection") == 'eight'
    ) {
      if (
        localStorage.getItem('advisorTabSelection') == 'eight' ||
        localStorage.getItem('advisorTabSelection') == 'eleven' ||
        localStorage.getItem('advisorTabSelection') == 'twelve'
      ) {
        return localStorage.getItem('advisorTabSelection');
      } else {
        return 'eight';
      }
      // return 'eight';
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'nine'
    ) {
      return 'nine';
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'eight' &&
      this.props.history.location.state.parent == 'workmixAdvisor'
    ) {
      return 'eight';
    } else if (
      this.props.route.state &&
      this.props.route.state.drillDownType &&
      this.props.route.state.drillDownType == 'TechnicianEfficiency' &&
      this.props.history.location.state == undefined
      // &&
      // localStorage.getItem("technicianTabSelection") == 'seven'
    ) {
      if (
        localStorage.getItem('technicianTabSelection') == 'seven' ||
        localStorage.getItem('technicianTabSelection') == 'nine' ||
        localStorage.getItem('technicianTabSelection') == 'ten'
      ) {
        return localStorage.getItem('technicianTabSelection');
      } else {
        return 'seven';
      }
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'three'
    ) {
      return 'three';
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.dbdType == 'workmix'
    ) {
      return this.props.history.location.state.tabselection;
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.drillDownType == 'workmix'
    ) {
      return 'two';
    }
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'searchByRo'
    ) {
      return this.props.history.location.state.tabselection;
    }
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'opcodes'
    ) {
      return this.props.history.location.state.tabselection;
    }
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.pageType == 'noncpopcodes'
    ) {
      return this.props.history.location.state.tabselection;
    }
    if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabSelection
    ) {
      return this.props.history.location.state.tabSelection;
    } else if (localStorage.getItem('laborTabSelection')) {
      return localStorage.getItem('laborTabSelection');
    } else if (localStorage.getItem('technicianTabSelection')) {
      return localStorage.getItem('technicianTabSelection');
    } else if (localStorage.getItem('advisorTabSelection')) {
      return localStorage.getItem('advisorTabSelection');
    } else if (localStorage.getItem('partsTabSelection')) {
      return localStorage.getItem('partsTabSelection');
    } else {
      return 'one';
    }
  };

  removeFav = value => {
    this.setState({
      isLoading: false
    });
  };
  hideGrids = value => {
    this.setState({
      chartZoomed: value
    });
  };

  callbackFunction = params => {
    // this.setState({
    //   chartZoomed: false
    // });
    console.log('params====__---====', params);
    if (params) {
      const spanAttribute = {
        pageUrl: '',
        origin: '',
        event: 'Drilldown Click',
        value: params.chartId
          ? params.chartId.toString()
          : params.reportType
          ? params.reportType.toString()
          : '',

        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Drilldown Click', spanAttribute);
      if (params.isFrom == 'Favorites') {
        this.props.history.push('/MyFavorites');
      }
      if (params.type == '') {
        this.setState({
          y: '2020-12-01T00:00:00',

          serviceadvisor: 'All',
          chartId: params.chartId,

          history: params.history,
          category: 0,
          type: '',
          prevPath: params.prevPath,
          discountMonth: params.discountMonth,
          discountId: params.discountId,
          discountServiceAdvisor: params.discountServiceAdvisor,
          drillDown: params.drillDown,
          originData: params.originData
        });
      }
      if (params.type == 'discount_drilldown') {
        this.setState({
          month_year: params.month_year,
          discountId: 'COMPETE',
          serviceadvisor: 'All',
          chartId: params.chartId,
          category: 0,
          type: params.type,
          isParts: true
        });
      }
      if (params.type == 'discounted_parts_drilldown') {
        this.setState({
          month_year: params.month_year,
          discountId: 'COMPETE',
          chartId: params.chartId,
          serviceadvisor: 'All',
          drillDown: 41,

          category: 0,
          type: params.type,
          isParts: true
        });
      }
      if (params.tabSelection && params.isFrom == 'drillDown') {
        this.setState({
          tabSelection: params.tabSelection,
          selectedSubTab: params.selectedSubTab,
          selectedTech: params.selectedTech,
          comparisonMonth1: params.comparisonMonth1,
          comparisonMonth2: params.comparisonMonth2,
          parent: params.parent,
          selectedTechName: params.selectedTechName,
          type: params.parent,
          selectedReportType: params.selectedReportType,
          reportTabSelection: params.reportTabSelection,
          reportType: params.selectedReportType,
          prevMenu: params.prevMenu,
          payType: params.payType,
          monthSelected: params.monthSelected,
          chartType: params.chartType,
          techPageType: params.techPageType,
          isReportSelected: true
        });
        if (params.parent == '') {
          this.setState({
            techNo: params.techNo
          });
        }
      }
      if (params.tabSelection && params.isFrom == 'drillDown-sa') {
        this.setState({
          tabSelection: params.tabSelection,
          selectedSubTab: params.selectedSubTab,
          selectedAdvisor: params.selectedAdvisor
            ? params.selectedAdvisor
            : params.serviceAdvisor,
          comparisonMonth1: params.comparisonMonth1,
          comparisonMonth2: params.comparisonMonth2,
          parent: params.parent,
          month_year: params.month_year,
          selectedReportType: params.selectedReportType,
          reportTabSelection: params.reportTabSelection,
          reportType: params.selectedReportType,
          chartLoc: params.chartLoc,
          prevPath: params.prevPath,
          prevMenu: params.prevMenu,
          isReportSelected: params.isReportSelected
        });
        if (params.parent == '') {
          this.setState({
            serviceadvisor: params.serviceAdvisor
          });
        }
      }
      if (params.tabSelection && params.isFrom == 'drillDown-workmix') {
        this.setState({
          tabSelection: params.tabSelection,
          //selectedSubTab: params.selectedSubTab,
          // selectedAdvisor: params.selectedAdvisor,
          comparisonMonth1: params.comparisonMonth1,
          comparisonMonth2: params.comparisonMonth2,
          parent: params.parent,
          workmixreportType: params.reportType,
          workmixParent: params.workmixParent,
          workmixTab: params.workmixTab,
          isReportSelected: true,
          reportType: params.reportType,
          selectedReportType: params.selectedReportType,
          opCategory: params.opCategory,
          roNumber: params.roNumber,
          page: params.pageType,
          selectedToggle: params.userHistory,
          payTypeRo: params.payTypeRo,
          //isFrom: params.selected == 'opcodelevel' ? params.selected : '',
          isFrom: 'opcodelevel',
          selectedButton: params.selectedButton,
          isExpanded: params.isExpanded,
          selectedOpcodeAll: params.selectedOpcodeAll,
          prevPath: params.prevPath,
          prevMenu: params.prevMenu,
          originData: params.originData
        });
        if (params.parent == '') {
          this.setState({
            opcode: ''
          });
        }
        if (
          (params.parent == 'opcodelevel' ||
            params.parent == 'comparisonchart') &&
          params.opcode != ''
        ) {
          this.setState({
            opcode: params.opcode
          });
        }
      }
      if (params.type == 'techniciantrendingcharts') {
        this.setState({
          technician: params.person == undefined ? '' : params.person,
          type: 'techniciantrendingcharts',
          chartType: params.reportType,
          isFrom: params.isFrom ? 'techniciantrendingcharts' : '',
          techName: params.techName,
          month_year: params.month_year,
          payType: params.payType,
          monthSelected: params.monthSelected,
          isFrom: params.isFrom,
          selectedToggle: params.selectedToggle,
          drilldownVal: params.drilldownVal,
          weekStart: params.weekStart,
          weekEnd: params.weekEnd,
          techPageType: params.techPageType
        });
        this.setState({ tabSelection: 'nine' });
      } else if (params.type == 'techcomparison') {
        this.setState({
          technician: params.person == undefined ? '' : params.person,
          month_year: params.month,
          isFrom: 'techcomparison',
          type: 'techcomparison',
          comparisonMonth1: params.fstMonth,
          comparisonMonth2: params.sndMonth,
          techName: params.techName,
          chartId: params.chartId
        });
        this.setState({ tabSelection: 'nine' });
      } else if (params.type == 'advisortrendingcharts') {
        this.setState({
          serviceadvisor: params.person == undefined ? '' : params.person,
          month_year: params.month ? params.month : '',
          chart_name: params.chartName ? params.chartName : '',
          isFrom: 'advisortrendingcharts'
        });
        this.setState({ tabSelection: 'eleven' });
      } else if (params.type == 'advisorcomparison') {
        this.setState({
          serviceadvisor:
            params.person == undefined
              ? params.serviceadvisor
                ? params.serviceadvisor
                : ''
              : params.person,
          month_year: params.month,
          isFrom: 'advisorcomparison',
          type: params.isType,
          comparisonMonth1: params.comparisonMonth1,
          comparisonMonth2: params.comparisonMonth2,
          chartLoc: params.chartLoc,
          prevPath: params.prevPath
        });
        this.setState({ tabSelection: 'eleven' });
      } else if (params.type == 'opcodelevel') {
        this.setState({
          opcode: params.opcode,
          month_year: params.month_year,
          isFrom: 'opcodelevel',
          parent: params.parent,
          isFromWorkmix: params.isFromWorkmix,
          workmixreportType: params.reportType,
          workmixParent: params.workmixParent,
          workmixTab: params.workmixTab,
          reportType: params.reportType,
          selectedReportType: params.selectedReportType,
          opcodePayType: params.opcodePayType,
          selectedButtonType: params.selectedButtonType,
          isExpanded: params.isExpanded,
          selectedOpcodeAll: params.selectedOpcodeAll,
          prevPath: params.prevPath,
          prevMenu: params.prevMenu,
          originData: this.state.originData
        });

        this.setState({ tabSelection: 'five' });
      } else if (params.type == 'reports') {
        params.isFrom == 'technician'
          ? this.setState({
              technician: params.opcode,
              isFrom: params.isFrom,
              type: 'reports',
              techName: params.techName,
              //month_year: this.getmonthYearforReportsLevelDrillDown(),
              selectedReportType: params.reportType,
              reportTabSelection: params.reportTabSelection,
              payType: params.payType,
              pageType: params.pageType,
              originData: params.originData
            })
          : params.isFrom == 'serviceadvisor'
          ? this.setState({
              serviceadvisor: params.opcode,
              isFrom: params.isFrom,
              type: 'reports',
              month_year: this.getmonthYearforReportsLevelDrillDown(),
              selectedReportType: params.reportType,
              reportTabSelection: params.reportTabSelection
            })
          : this.setState({
              opcode: params.opcode,
              isFrom: params.isFrom,
              month_year: this.getmonthYearforReportsLevelDrillDown(),
              selectedReportType: params.reportType,
              workmixreportType: params.reportType,
              isExpanded: params.isExpanded
            });
        params.isFrom == 'technician'
          ? this.setState({ tabSelection: 'nine' })
          : params.isFrom == 'serviceadvisor'
          ? this.setState({ tabSelection: 'eleven' })
          : this.setState({ tabSelection: 'four' });
      } else if (params.type == 'workmixvolume') {
        this.setState({
          opcode: params.opcode,
          month_year: this.getmonthYearforReportsLevelDrillDown()
        });
        this.setState({ tabSelection: 'four' });
      } else if (params.type == 'comparisonchart') {
        if (params.opcode == 'OTHER') {
          this.setState({
            isFrom: 'comparisonchart',
            type: params.isFrom,
            comparisonMonth1: params.comparisonMonth1,
            comparisonMonth2: params.comparisonMonth2,
            workmixreportType: params.workmixreportType,
            opCategory: params.opCategory,
            isExpanded: params.isExpanded,
            originData: params.originData
          });
          this.props.history.push({
            pathname:
              this.state.isPartsCharts == true
                ? 'WorkMixVolumeParts'
                : '/WorkMixVolume',
            parentCallback: this.callbackFunction,
            state: {
              category: this.state.isPartsCharts == true ? 'parts' : 'labor',
              isFrom: 'comparisonchart',
              type: this.state.type,
              comparisonMonth1: this.state.comparisonMonth1,
              comparisonMonth2: this.state.comparisonMonth2,
              reportType:
                this.state.isPartsCharts == false &&
                this.state.workmixreportType == 'Sales'
                  ? 'soldhours'
                  : this.state.isPartsCharts &&
                    (this.state.workmixreportType == 'Sales' ||
                      this.state.workmixreportType == 'Cost')
                  ? 'partscost'
                  : this.state.isPartsCharts &&
                    this.state.workmixreportType == 'grossprofit'
                  ? 'grossprofitpercentage'
                  : this.state.workmixreportType,
              selectedReportType: this.state.workmixreportType,
              opCategory: params.opcategory,
              // opCategory: this.state.opCategory,
              isExpanded: this.state.isExpanded,
              originData: params.originData
            }
          });
          // <WorkMixVolume
          //   category={this.state.isPartsCharts == true ? 'parts' : 'labor'}
          //   isFrom={this.state.isFrom}
          //   type={this.state.type}
          //   comparisonMonth1={this.state.comparisonMonth1}
          //   comparisonMonth2={this.state.comparisonMonth2}
          //   reportType={this.state.workmixreportType}
          //   parentCallback={this.callbackFunction}
          // />
          //this.setState({ tabSelection: 'six' });
        } else {
          this.setState({
            opcode: params.opcode,
            isFrom: 'comparisonchart',
            comparisonMonth1: params.comparisonMonth1,
            comparisonMonth2: params.comparisonMonth2,
            month_year: params.month ? params.month : params.month_year
          });
          this.setState({ tabSelection: 'four' });
        }
      } else if (params.type == 'workmixcharts') {
        this.setState({
          isFrom: 'workmixcharts',
          reportType:
            this.state.isPartsCharts == true
              ? params.reportType
              : params.reportType.includes('Cost') == true
              ? 'soldhours'
              : params.reportType,
          isReportSelected: true,
          originData: 'workmixcharts'
        });
        this.setState({ tabSelection: 'three' });
      } else if (params.type == 'workmixchartsAdvisor') {
        this.setState({
          isFrom: 'workmixchartsAdvisor',
          selectedSubTab: 'three'
        });
        this.setState({ tabSelection: 'eight' });
      } else if (params.type == 'workmixdrilldown') {
        this.setState({
          drillDown: params.drillDown,
          type: params.type,
          chartId: params.chartId,
          x: params.x,
          y: params.y,
          category: params.category,
          chartName: params.chartName,
          title: params.title,
          isParts: params.isParts,
          isFrom: params.isFrom
        });
      } else if (params.type == 'technicianlevel') {
        this.setState({
          techNo: params.techNo,
          month_year: params.month_year,
          type: params.type,
          parent: params.parent,
          comparisonMonth1: params.comparisonMonth1,
          comparisonMonth2: params.comparisonMonth2,
          techName: params.selectedTechName,
          monthSelected: params.monthSelected,
          payType: params.payType,
          prevMenu: params.prevMenu,
          chartType: params.chartType,
          drilldownVal: params.drilldownVal,
          weekStart: params.weekStart,
          weekEnd: params.weekEnd,
          chartId: params.chartId
        });
        this.setState({ tabSelection: 'ten' });
      } else if (params.type == 'serviceadvisorlevel') {
        this.setState({
          serviceAdvisor: params.serviceAdvisor,
          month_year: params.month_year,
          type: params.type,
          parent: params.parent,
          chartLoc: params.chartLoc,
          prevPath: params.prevPath,
          prevMenu: params.prevMenu
        });
        this.setState({ tabSelection: 'twelve' });
      } else if (params.tabSelection && params.isFrom == 'report-drillDown') {
        this.setState({
          tabSelection: params.tabSelection,
          isReportSelected: true
          // reportType: params.reportType
        });
      }
    }
  };

  getmonthYearforReportsLevelDrillDown = () => {
    return getLast13Months();
  };

  handleTabChange = (event, newValue) => {
    this.setState({
      serviceadvisor: ['All'],
      isFrom: '',
      parent: '',
      technician: '',
      techName: '',
      opcode: '',
      changeTab: true,
      techPageType: 'techSummary',
      comparisonMonth1: '',
      comparisonMonth2: ''
    });
    setTimeout(() => {
      ReactSession.set('selectedReport', '');
    }, 500);
    window.sortStateTechMonthly = {};
    window.filterStateTechMonthly = {};
    window.sortStateTech = {};
    window.sortStatesTech = {};
    window.filterStateTech = {};
    window.filterStateAdv = {};
    window.sortStateAdv = {};
    window.filterStatesAdv = {};
    window.sortStatesAdv = {};
    window.sortState = {};
    window.filterState = {};
    var title = localStorage.getItem('title');
    if (newValue == 'seven' || newValue == 'nine' || newValue == 'ten') {
      localStorage.setItem('technicianTabSelection', newValue);
      localStorage.setItem('laborTabSelection', '');
      localStorage.setItem('advisorTabSelection', '');
    } else if (
      newValue == 'eight' ||
      newValue == 'eleven' ||
      newValue == 'twelve'
    ) {
      localStorage.setItem('advisorTabSelection', newValue);
      localStorage.setItem('technicianTabSelection', '');
      localStorage.setItem('laborTabSelection', '');
    } else if (
      (newValue == 'one' ||
        newValue == 'two' ||
        newValue == 'four' ||
        newValue == 'five') &&
      title == 'Parts Work Mix'
    ) {
      localStorage.setItem('partsTabSelection', newValue);
      localStorage.setItem('laborTabSelection', '');
      localStorage.setItem('technicianTabSelection', '');
      localStorage.setItem('advisorTabSelection', '');
    } else if (
      (newValue == 'one' ||
        newValue == 'two' ||
        newValue == 'four' ||
        newValue == 'five') &&
      title == 'Labor Work Mix'
    ) {
      localStorage.setItem('laborTabSelection', newValue);

      localStorage.setItem('technicianTabSelection', '');
      localStorage.setItem('advisorTabSelection', '');
      localStorage.setItem('partsTabSelection', '');
    }
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'ReportTabs Click',
      value:
        newValue +
        '-' +
        (this.props.route.state && this.props.route.state.drillDownType
          ? this.props.route.state.drillDownType
          : ''),
      provenance: localStorage.getItem('provenance')
    };
    const { history } = this.props;

    if (
      history &&
      history.location &&
      history.location.state &&
      history.location.state.drilldownVal
    ) {
      // Clone the current state
      const newState = { ...history.location.state };

      // Remove the drilldownVal
      delete newState.drilldownVal;

      // Replace the history state with the new state without drilldownVal
      history.replace({ ...history.location, state: newState });
    }
    traceSpan('ReportTabs', spanAttribute);
    this.setState({ tabSelection: newValue });
    this.setState({ drilldownVal: '' });
    this.setState({ weekStart: '' });
    this.setState({ weekEnd: '' });
    this.setState({ isReportSelected: false });
    this.setState({ selectedReportType: '' });
    this.setState({ serviceAdvisor: '' });
    this.setState({ month_year: getComparisonMonths()[1] });
    this.setState({ type: '' });
    if (newValue == 'two' || newValue == 'one') {
      this.setState({ isExpanded: '' });
    }
    if (newValue == 'four') {
      this.setState({ isFrom: '' });
    }
    if (this.state.pageType == 'searchByRo') {
      this.setState({ pageType: '' });
    }
  };

  handleChangeCurrentMonth = event => {
    const { dispatch } = this.props;

    //   if (event.target.innerText == 'Show Partial Month') {
    if (this.state.checked == false || this.state.checked == 'false') {
      this.setState({ checked: true });
      localStorage.setItem('showCurrentMonth', true);
      dispatch(setToggleStatus(true));
    } else {
      this.setState({ checked: false });
      localStorage.setItem('showCurrentMonth', false);
      dispatch(setToggleStatus(false));
    }

    // this.setState({ checked: event.target.checked });
    // localStorage.setItem('showCurrentMonth', event.target.checked);
  };

  renderBackButton = prevPath => {
    console.log('prevPath===', prevPath, '===', this.props, '====', this.state);
    let data = {};
    if (this.state.prevMenu == 'Favorites') {
      this.props.history.push('/MyFavorites');
    } else {
      if (
        this.props.history &&
        this.props.history.location &&
        ((this.props.history.location.isFrom &&
          this.props.history.location.isFrom == 'opportunity') ||
          (this.props.history.location.state &&
            this.props.history.location.state.isFrom &&
            this.props.history.location.state.isFrom == 'opportunity')) &&
        this.state.tabSelection != 'three'
      ) {
        if (this.state.isPartsCharts == true) {
          this.props.history.push('/PartsGrossAndVolumeOpportunity');
        } else if (
          (prevPath && prevPath == '/CPELROpportunity') ||
          (this.props.history.location.state &&
            this.props.history.location.state.prevPaths &&
            this.props.history.location.state.prevPaths == '/CPELROpportunity')
        ) {
          this.props.history.push('/CPELROpportunity');
        } else {
          this.props.history.push('/LaborGrossAndVolumeOpportunity');
        }
      } else {
        data = {
          tabSelection: 'one',
          isFrom: 'report-drillDown',
          isReportSelected: false,
          reportType: 'Select'
        };
        if (document.getElementById(this.state.reportType + '_report')) {
          document.getElementById(
            this.state.reportType + '_report'
          ).style.background = '#FFFFFF';
          document.getElementById(
            this.state.reportType + '_report'
          ).style.color = '#757575';
        }

        this.setState({ reportType: '' });
        this.setState({ selectedReportType: '' });
        this.callbackFunction(data);
      }
    }
  };

  handleReportsChanges = type => {
    this.setState({ changeTab: false });
    ReactSession.set('selectedReport', '');
    setTimeout(() => {
      ReactSession.set('selectedReport', type);
    }, 1000);

    window.sortState = {};
    window.filterState = {};
    window.sortStateWorkMix = {};
    window.filterStateWorkMix = {};
    window.sortStateTech = {};
    window.filterStateTech = {};
    window.sortStateAdvLbr = {};
    window.filterStateAdvLbr = {};
    window.filterStatesAdv = {};
    window.sortStatesAdv = {};
    window.filterStatesTech = {};
    window.sortStatesTech = {};
    window.filterStateTechMonthly = {};
    window.sortStatesTechMonthly = {};
    localStorage.setItem('laborTabSelection', '');
    localStorage.setItem('partsTabSelection', '');
    localStorage.setItem('technicianTabSelection', '');
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'MonthlyTables Click',
      value:
        type +
        '-' +
        (this.props.route.state && this.props.route.state.drillDownType
          ? this.props.route.state.drillDownType
          : ''),
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('MonthlyTables', spanAttribute);
    if (document.getElementById(this.state.reportType + '_report')) {
      document.getElementById(
        this.state.reportType + '_report'
      ).style.background = '#FFFFFF';
      document.getElementById(this.state.reportType + '_report').style.color =
        '#757575';
    }
    if (document.getElementById(this.state.selectedReportType + '_report')) {
      document.getElementById(
        this.state.selectedReportType + '_report'
      ).style.background = '#FFFFFF';
      document.getElementById(
        this.state.selectedReportType + '_report'
      ).style.color = '#757575';
    }
    this.setState({ isFrom: '' });
    this.setState({ reportTabSelection: this.state.tabSelection });
    this.setState({ reportType: type });
    this.setState({ selectedReportType: type });
    this.setState({ tabSelection: 'three' });
    this.setState({ isReportSelected: true });

    // document.getElementById(type+'_report').style.background='#9c9c9c';2F4F4F;D3D3D3
    document.getElementById(type + '_report').style.background = '#DCDCDC';
    document.getElementById(type + '_report').style.color = '#C2185B';
    if (this.state.pageType == 'searchByRo') {
      this.setState({ pageType: '' });
    }
  };
  getDrillDownParams = () => {
    let data = {};
    if (
      typeof this.state.type != 'undefined' &&
      (this.state.type == '' ||
        this.state.type == 'workmixdrilldown' ||
        this.state.type == 'discount_drilldown' ||
        this.state.type == 'discounted_parts_drilldown')
    ) {
      data = {
        type: this.state.type,
        chartId: this.state.chartId,
        x: this.state.x,
        y: this.state.y,
        drillDown: this.state.drillDown,
        category: this.state.category,
        chartName: this.state.chartName,
        title: this.state.title,
        isParts: this.state.isParts,
        isFrom: this.state.isFrom
      };
      return data;
    }
  };
  resetReportGrid = () => {
    this.setState(prevState => ({
      reloadKey: prevState.reloadKey + 1
    }));

    console.log('ccc===resetReportGrid');
    this.setState({ resetLayout: true });
  };
  handleResetReport = () => {
    this.setState({ resetLayout: false });
  };
  exportReportGrid = () => {
    this.setState({ exportReport: true });
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title: this.state.reportType ? this.state.reportType : '',
      from:
        this.props.route.state && this.props.route.state.drillDownType
          ? this.props.route.state.drillDownType
          : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to Excel', spanAttribute);
  };
  handleExportReport = () => {
    this.setState({ exportReport: false });
  };
  handleReportSelected = () => {
    this.setState({ isReportSelected: false });
  };
  checkEfficiencyCharts = () => {
    if (this.props.route.state && this.props.route.state.drillDownType) {
      return true;
    } else if (
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabselection == 'three'
    ) {
      return true;
    } else {
      return false;
    }
  };

  checkisWorkMixRouting = () => {
    if (
      this.props.route.state &&
      typeof this.props.route.state.drillDownType != 'undefined'
    ) {
      if (
        this.props.route.state.drillDownType == 'LaborWorkMixAnalysis' ||
        this.props.route.state.drillDownType == 'PartsWorkMixAnalysis'
      ) {
        return true;
      } else if (
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.tabselection == 'three'
      ) {
        return true;
      } else {
        return false;
      }
    } else return false;
  };
  checkTechnicianCharts = () => {
    if (
      this.props.route.state &&
      this.props.route.state.drillDownType &&
      this.props.route.state.drillDownType == 'TechnicianEfficiency'
    ) {
      return true;
    } else {
      return false;
    }
  };

  checkAdvisorCharts = () => {
    if (
      this.props.route.state &&
      this.props.route.state.drillDownType &&
      this.props.route.state.drillDownType == 'ServiceAdvisorEfficiency'
    ) {
      return true;
    } else {
      return false;
    }
  };
  render() {
    const { classes } = this.props;
    let data = this.getDrillDownParams();
    let isEfficiencyCharts = this.checkEfficiencyCharts();
    let isWorkMixRouting = this.checkisWorkMixRouting();
    let isTechnicianCharts = this.checkTechnicianCharts();
    let isAdvisorCharts = this.checkAdvisorCharts();
    let pageType = this.props.history.location.state
      ? this.props.history.location.state.pageType
        ? this.props.history.location.state.pageType
        : ''
      : '';

    //console.log('chartjsprops', this.props, this.state);

    if (
      typeof this.state.isFrom == 'undefined' &&
      this.props.history.location.pathname != '/TechnicianPerformance'
    ) {
      this.setState({ isFrom: 'workmixcharts' });
    }
    let history;
    if (typeof this.state.type != 'undefined' && this.state.type == '') {
      history = this.state.history;
    } else {
      history = this.props.history;
    }
    if (
      document.getElementById(this.state.reportType + '_report') &&
      (this.state.isReportSelected == false ||
        (this.state.isReportSelected == true &&
          this.state.tabSelection != this.state.reportTabSelection))
    ) {
      document.getElementById(this.state.reportType + '_report').style.color =
        '#757575';
      document.getElementById(
        this.state.reportType + '_report'
      ).style.background = '#FFFFFF';
    } else if (
      (typeof this.state.isReportSelected == 'undefined' ||
        this.state.isReportSelected == false) &&
      this.state.selectedReportType
    ) {
      if (this.props.history.location.pathname == '/WorkMixVolume') {
        document.getElementById(
          this.state.selectedReportType + '_report'
        ).style.background = '#FFFFFF';
        document.getElementById(
          this.state.selectedReportType + '_report'
        ).style.color = '#757575';
      }
    }
    let prevPath =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.prevPath
        ? this.props.history.location.prevPath
        : '';
    // console.log(
    //   'month_year==',
    //   this.state.names,
    //   this.state.advisorNames,
    //   this.props.session.kpiAdvisor
    // );

    return this.state.drillDown != 'undefined' &&
      this.state.drillDown >= 0 &&
      this.state.type != 'discount_drilldown' &&
      this.state.type != 'discounted_parts_drilldown' ? (
      <Drilldown
        history={history}
        params={data}
        session={this.props.session}
        parentCallback={this.callbackFunction}
        prevPath={this.state.prevPath}
        handleExportReport={this.handleExportReport}
        techNo={
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state
            ? this.props.history.location.state.techNo
            : this.state.selectedTech
            ? this.state.selectedTech
            : this.state.technician
        }
        techName={
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state
            ? this.props.history.location.state.techName
            : this.state.selectedTechName
            ? this.state.selectedTechName
            : this.state.techName
        }
      />
    ) : this.state.type == 'discount_drilldown' ||
      this.state.type == 'discounted_parts_drilldown' ? (
      <DiscountSummaryDrillDown
        parentCallback={this.callbackFunction}
        month_year={this.state.month_year}
        serviceAdvisor="All"
        type={this.state.type}
        history={this.props.history}
        chartId={this.state.chartId}
        drillDown={this.state.drillDown}
      />
    ) : this.state.type == 'one_line_drilldown' ? (
      <DrilldownOneLineRo
        parentCallback={this.callbackFunction}
        // month_year={this.state.month_year}
        // serviceAdvisor="All"
        // type={this.state.type}
        history={this.props.history}
        session={this.props.session}
        oneLineType={
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.oneLineType
            ? this.props.history.location.state.oneLineType
            : 'Under 60K'
        }
        filterStart={
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.filterStart
            ? this.props.history.location.state.filterStart
            : ''
        }
        filterEnd={
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.filterEnd
            ? this.props.history.location.state.filterEnd
            : ''
        }
      />
    ) : (
      <div>
        <Paper
          square
          style={{
            margin: 8,
            //marginTop: '20px',
            border: '2px solid #0b497a'
            // display: this.state.chartZoomed ? 'none' : 'block'
          }}
        >
          <Grid container xs={12} className={'workmix-tabs'}>
            <Grid xs={8}>
              <Tabs
                //variant="scrollable"
                scrollButtons="auto"
                value={this.state.tabSelection}
                onChange={this.handleTabChange}
                indicatorColor="secondary"
                textColor="secondary"
                TabIndicatorProps={{ style: { display: 'none' } }}
              >
                {isEfficiencyCharts != true || isWorkMixRouting == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'one'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <BarChartIcon
                          style={{ verticalAlign: 'middle', marginTop: -5 }}
                        />
                        Work Mix
                      </div>
                    }
                    id={'Work mix tab'}
                    color="primary"
                    value="one"
                  />
                ) : null}

                {isEfficiencyCharts != true || isWorkMixRouting == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'two'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <CompareArrowsIcon
                          style={{ verticalAlign: 'middle' }}
                        />{' '}
                        2 Month Work Mix Comparison
                      </div>
                    }
                    id={'2 Month Work Mix Comparison'}
                    value="two"
                  />
                ) : null}

                {isTechnicianCharts == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'seven'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <ShowChartIcon style={{ verticalAlign: 'middle' }} />
                        Technician Productivity
                      </div>
                    }
                    id={'Technician Productivity'}
                    value="seven"
                  />
                ) : null}

                {isAdvisorCharts == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'eight'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <ShowChartIcon style={{ verticalAlign: 'middle' }} />{' '}
                        Service Advisor Performance
                      </div>
                    }
                    id={'Service Advisor Performance'}
                    value="eight"
                  />
                ) : null}

                {/* <Tab
              style={{ textTransform: 'none' }}
              label={
                <div>
                  <DescriptionIcon style={{ verticalAlign: 'middle' }} />{' '}
                  Monthly Ranked Tables
                </div>
              }
              value="three"
            /> */}

                {isTechnicianCharts == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'nine'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <ShowChartIcon style={{ verticalAlign: 'middle' }} />{' '}
                        Technician Summary
                      </div>
                    }
                    id={'Technician Summary'}
                    value="nine"
                  />
                ) : null}

                {isTechnicianCharts == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'ten'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <ShowChartIcon style={{ verticalAlign: 'middle' }} />{' '}
                        Technician Detailed View
                      </div>
                    }
                    id={'Technician Detailed View'}
                    value="ten"
                  />
                ) : null}
                {isAdvisorCharts == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'eleven'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <ShowChartIcon style={{ verticalAlign: 'middle' }} />{' '}
                        Service Advisor Summary
                      </div>
                    }
                    id={'Service Advisor Summary'}
                    value="eleven"
                  />
                ) : null}

                {isAdvisorCharts == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'twelve'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <ShowChartIcon style={{ verticalAlign: 'middle' }} />{' '}
                        Service Advisor Detailed View
                      </div>
                    }
                    id={'Service Advisor Detailed View'}
                    value="twelve"
                  />
                ) : null}

                {isEfficiencyCharts != true || isWorkMixRouting == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'four'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <TableChartIcon style={{ verticalAlign: 'middle' }} />{' '}
                        Opcode - Summary
                      </div>
                    }
                    id={'Opcode - Summary'}
                    value="four"
                  />
                ) : null}

                {isEfficiencyCharts != true || isWorkMixRouting == true ? (
                  <Tab
                    style={{ textTransform: 'none' }}
                    className={
                      this.state.tabSelection == 'five'
                        ? classes.tabSelected
                        : null
                    }
                    label={
                      <div>
                        <DetailsIcon style={{ verticalAlign: 'middle' }} />{' '}
                        Opcode - Detailed View
                      </div>
                    }
                    id={'Opcode - Detailed View'}
                    value="five"
                  />
                ) : null}

                {/* {isEfficiencyCharts != true || isWorkMixRouting == true ? (
              <Tab
                style={{ textTransform: 'none' }}
                label={
                  <div>
                    <ShowChartIcon style={{ verticalAlign: 'middle' }} /> Work
                    Mix Volumes
                  </div>
                }
                value="six"
              />
            ) : null} */}
              </Tabs>
            </Grid>
            {/* <Paper square style={{ margin: 8}}> */}
            <Grid xs={4} className={classes.dataAsofContainer2}>
              {this.props.history &&
              this.props.history.location &&
              ((this.props.history.location.isFrom &&
                this.props.history.location.isFrom == 'opportunity') ||
                (this.props.history.location.state &&
                  this.props.history.location.state.isFrom &&
                  this.props.history.location.state.isFrom == 'opportunity')) &&
              this.state.tabSelection == 'one' &&
              this.props.session.kpiAdvisor.length == 1 ? (
                // <div style={{width: 195}}>
                <div style={{ marginRight: 10 }}>
                  <Typography
                    variant="h6"
                    align="right"
                    style={{
                      fontSize: 12,
                      color: '#7987a1',
                      fontWeight: 'bold'
                    }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between'
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between'
                        }}
                      >
                        <div>{'Advisor:' + '\xa0'}</div>
                        {/* <div style={{ width: 13 }}>{':'}</div> */}
                        <div style={{ marginLeft: 3 }}>
                          {/* {'Chrystal Casey [236]'} */}
                          {console.log(
                            'ppp====',
                            this.props.session.kpiAdvisor,
                            this.state.advisorNames
                          )}
                          {this.state.names &&
                          this.state.names.length > 0 &&
                          this.state.advisorNames &&
                          this.state.advisorNames.length > 0
                            ? this.props.session.kpiAdvisor
                                .map(data => {
                                  if (
                                    this.state.names.indexOf(data) > -1 ===
                                    true
                                  ) {
                                    const index = this.state.names.indexOf(
                                      data
                                    );
                                    const advisorName = this.state.advisorNames[
                                      index
                                    ].split('-status-')[0];
                                    console.log(advisorName);
                                    let formattedData = `${advisorName}[${data}]`;
                                    if (advisorName == 'All') {
                                      formattedData = advisorName;
                                    }
                                    return formattedData;
                                  } else {
                                    return data;
                                  }
                                })
                                .join(', ')
                            : //     this.props.session.kpiAdvisor.map(
                              //   data =>
                              //     // <span style={{fontWeight: 400, marginLeft: 5}}>

                              //     this.state.names.indexOf(data) > -1 === true
                              //       ? this.state.advisorNames[
                              //           this.state.names.indexOf(data)
                              //         ].split('-status-')[0] +
                              //         '[' +
                              //         data +
                              //         (this.state.names.indexOf(data) == 1 ||
                              //         data == 'All'
                              //           ? ']'
                              //           : '], ')
                              //       : data

                              //   // </span>
                              // )
                              ''}
                        </div>
                      </div>
                    </div>
                  </Typography>
                  <Grid
                    xs={
                      //checkClosedDateInCurrentMonth() &&
                      isTechnicianCharts != true &&
                      isAdvisorCharts != true &&
                      this.state.tabSelection == 'one'
                        ? 1.5
                        : 4
                    }
                    className={classes.dataAsofContainer}
                  >
                    <Typography
                      variant="body1"
                      color="secondary"
                      className={clsx(classes.dataLabel, 'date-asof')}
                    >
                      Data as of :{' '}
                      <span style={{ marginLeft: 1 }}>
                        {moment(this.state.setClosedDate).format('MM/DD/YY')}
                      </span>
                    </Typography>
                  </Grid>
                </div>
              ) : (
                <Grid
                  xs={
                    //checkClosedDateInCurrentMonth() &&
                    isTechnicianCharts != true &&
                    isAdvisorCharts != true &&
                    this.state.tabSelection == 'one'
                      ? 1.5
                      : 4
                  }
                  className={classes.dataAsofContainer}
                >
                  <Typography
                    variant="body1"
                    color="secondary"
                    className={clsx(classes.dataLabel, 'date-asof')}
                  >
                    Data as of :{' '}
                    {moment(this.state.setClosedDate).format('MM/DD/YY')}
                  </Typography>
                </Grid>
              )}
              {//checkClosedDateInCurrentMonth() &&
              isTechnicianCharts != true &&
              isAdvisorCharts != true &&
              this.state.tabSelection == 'one' ? (
                /*<Button
                  className={
                    this.state.checked
                      ? classes.partialToggleSelect
                      : classes.partialToggle
                  }
                  id="partialMonthToggle"
                  variant="outlined"
                  style={{ textTransform: 'none' }}
                  onClick={this.handleChangeCurrentMonth}
                >
                  {this.state.checked
                    ? 'Hide Partial Month'
                    : 'Show Partial Month'}
                </Button>*/
                <span
                  style={{
                    opacity: this.state.checked === 1 ? '0.5' : 'initial'
                  }}
                >
                  <FormControlLabel
                    control={
                      <Checkbox
                        disabled={this.state.checked === 1 ? true : false}
                        checked={this.state.checked == true ? true : false}
                        onClick={this.handleChangeCurrentMonth}
                        style={{
                          transform: 'scale(0.8)',
                          color: '#003d6b'
                        }}
                      />
                    }
                    label={
                      <Typography
                        variant="h6"
                        style={{
                          color: '#003d6b',
                          fontWeight: 'bold',
                          marginLeft: -8,
                          fontSize: 12,
                          marginRight: -8
                        }}
                      >
                        {/* {JSON.parse(this.state.checked) ? 'Hide Partial Month' : 'Show Partial Month'}</Typography>} */}
                        {this.state.checked ? 'Partial Month' : 'Partial Month'}
                      </Typography>
                    }
                  />
                </span>
              ) : (
                ''
              )}
              {(this.state.isReportSelected &&
                this.state.tabSelection != 'one') ||
              this.state.tabSelection == 'four' ||
              this.state.tabSelection == 'three' ||
              this.state.tabSelection == 'two' ||
              this.state.tabSelection == 'five' ||
              this.state.tabSelection == 'nine' ||
              this.state.tabSelection == 'ten' ||
              this.state.tabSelection == 'eleven' ||
              this.state.tabSelection == 'twelve' ? (
                <Button
                  variant="contained"
                  id="reset-layout"
                  className={clsx(classes.back, 'reset-btn')}
                  onClick={this.resetReportGrid}
                >
                  <RestoreIcon />
                  <Typography variant="body1" align="left">
                    Reset Layout
                  </Typography>
                </Button>
              ) : (
                ''
              )}
            </Grid>
          </Grid>
          {/* </Paper> */}
        </Paper>
        <Grid
          container
          spacing={12}
          id="monthlyRankedTables"
          //style={{ display: this.state.chartZoomed ? 'none' : 'block' }}
          tab="one"
        >
          <Grid item xs={12}>
            {isAdvisorCharts == false ? (
              <Paper square className={classes.reportTopBar}>
                <Grid
                  container
                  xs={12}
                  className={classes.rankedTableContainer}
                  //className={clsx(classes.titleContainer, 'reset-dashboard')}
                >
                  <Grid xs={8} className={classes.rankButtonGrid}>
                    {(((this.state.isFrom == 'workmixcharts' &&
                      this.state.selectedReportType == '') ||
                      (this.state.isFrom == 'workmixcharts' &&
                        this.state.reportType !== '' &&
                        (this.state.selectedReportType == '' ||
                          this.state.selectedReportType == undefined)) ||
                      this.state.isFrom == 'workmix' ||
                      (this.state.isFrom == 'opcodelevel' &&
                        this.state.selectedReportType == '')) &&
                      this.state.tabSelection == 'three') ||
                    (this.props.history &&
                      this.props.history.location &&
                      ((this.props.history.location.isFrom &&
                        this.props.history.location.isFrom == 'opportunity') ||
                        (this.props.history.location.state &&
                          this.props.history.location.state.isFrom &&
                          this.props.history.location.state.isFrom ==
                            'opportunity')) &&
                      this.state.tabSelection == 'one') ||
                    (this.state.originData == 'workmixcharts' &&
                      this.state.tabSelection == 'three') ? (
                      <Button
                        variant="contained"
                        className={'bck-btn'}
                        onClick={() => this.renderBackButton(prevPath)}
                      >
                        <Typography variant="body1" align="left">
                          Back
                        </Typography>
                      </Button>
                    ) : null}
                    <Typography
                      variant="h6"
                      color="primary"
                      className={classes.rankLabel}
                    >
                      Monthly Tables
                    </Typography>
                    <div>
                      {isEfficiencyCharts != true ||
                      isWorkMixRouting == true ? (
                        <Button
                          id="Sales_report"
                          className={
                            this.state.reportType === 'Sales' ||
                            this.state.changeTab != true
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() => this.handleReportsChanges('Sales')}
                        >
                          Sales
                        </Button>
                      ) : null}
                      {isEfficiencyCharts != true ||
                      isWorkMixRouting == true ? (
                        <Button
                          id={
                            this.state.isPartsCharts == true
                              ? 'Cost_report'
                              : 'soldhours_report'
                          }
                          variant="outlined"
                          className={
                            this.state.reportType == 'Cost' ||
                            this.state.reportType == 'soldhours' ||
                            this.state.changeTab != true
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          onClick={() =>
                            this.handleReportsChanges(
                              this.state.isPartsCharts == true
                                ? 'Cost'
                                : 'soldhours'
                            )
                          }
                        >
                          {this.state.isPartsCharts == true
                            ? 'Cost'
                            : 'Sold Hours'}
                        </Button>
                      ) : null}
                      {isEfficiencyCharts != true ||
                      isWorkMixRouting == true ? (
                        <Button
                          id="grossprofit_report"
                          className={
                            this.state.reportType == 'grossprofit' ||
                            this.state.changeTab != true
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() =>
                            this.handleReportsChanges('grossprofit')
                          }
                        >
                          {this.state.isPartsCharts == true
                            ? 'Gross Profit %'
                            : 'Gross Profit %'}
                        </Button>
                      ) : null}
                      {isEfficiencyCharts != true ||
                      isWorkMixRouting == true ? (
                        <Button
                          id={
                            this.state.isPartsCharts == true
                              ? 'markup_report'
                              : 'elr_report'
                          }
                          className={
                            this.state.reportType == 'markup' ||
                            this.state.reportType == 'elr' ||
                            this.state.changeTab != true
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() =>
                            this.handleReportsChanges(
                              this.state.isPartsCharts == true
                                ? 'markup'
                                : 'elr'
                            )
                          }
                        >
                          {this.state.isPartsCharts == true ? 'Markup' : 'ELR'}
                        </Button>
                      ) : null}
                      {isEfficiencyCharts != true ||
                      isWorkMixRouting == true ? (
                        <Button
                          id="jobcount_report"
                          className={
                            this.state.reportType == 'jobcount' ||
                            this.state.changeTab != true
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() => this.handleReportsChanges('jobcount')}
                        >
                          {this.state.isPartsCharts == true
                            ? 'Job Count '
                            : 'Job Count'}
                        </Button>
                      ) : null}

                      {isEfficiencyCharts != true ||
                      isWorkMixRouting == true ? (
                        <Button
                          id="workmix_report"
                          className={
                            this.state.reportType == 'workmix' ||
                            this.state.changeTab != true
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() => this.handleReportsChanges('workmix')}
                        >
                          {this.state.isPartsCharts == true
                            ? 'Work Mix % '
                            : 'Work Mix %'}
                        </Button>
                      ) : null}

                      {/* {isTechnicianCharts == true ? (
                        <Button
                          id="techefficiency_report"
                          className={
                            this.state.reportType == 'techefficiency'
                              ? classes.reportButtonSelect
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() =>
                            this.handleReportsChanges('techefficiency')
                          }
                        >
                          Tech Productivity
                        </Button>
                      ) : null} */}

                      {isTechnicianCharts == true ? (
                        <Button
                          id="allsoldhrs_report"
                          className={
                            this.state.reportType == 'allsoldhrs' &&
                            this.state.changeTab != true
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() =>
                            this.handleReportsChanges('allsoldhrs')
                          }
                        >
                          Tech Sold Hours - All
                        </Button>
                      ) : null}
                      {isTechnicianCharts == true ? (
                        <Button
                          id="flatratehrs_report"
                          className={
                            this.state.reportType == 'flatratehrs'
                              ? classes.reportButtonSelect
                              : this.state.changeTab == true
                              ? classes.anotherClass
                              : classes.reportButton
                          }
                          variant="outlined"
                          onClick={() =>
                            this.handleReportsChanges('flatratehrs')
                          }
                        >
                          Tech Sold Hours - CP
                        </Button>
                      ) : null}

                      {/* {isTechnicianCharts == true ? (
                    <Button
                      id="actualhrs_report"
                      className={
                        this.state.reportType == 'actualhrs'
                          ? classes.reportButtonSelect
                          : classes.reportButton
                      }
                      variant="outlined"
                      onClick={() => this.handleReportsChanges('actualhrs')}
                    >
                      Actual Hours
                    </Button>
                  ) : null} */}
                    </div>
                  </Grid>

                  {this.state.isReportSelected &&
                    this.state.tabSelection == 'three' && (
                      <Grid xs={4} className={classes.gridContainer}>
                        <div style={{ display: 'grid' }}>
                          <Typography
                            variant="h14"
                            color="primary"
                            style={{
                              textTransform: 'none',
                              float: 'right',

                              marginRight:
                                !this.props.isAdvisorCharts &&
                                !this.props.isTechnicianCharts
                                  ? 160
                                  : 166,
                              fontSize: 12
                            }}
                          >
                            Ranking Per Row
                          </Typography>
                          <div
                            // '#003d6b',
                            // '#054372',
                            // '#064677',
                            // '#0f5285',
                            // '#165a8e',
                            // '#1e6296',
                            // '#2772ac',
                            // '#347fb8',
                            // '#468cc1',
                            // '#5797c7',
                            // '#6baad8',
                            // '#88bce3',
                            // '#b5daf6'
                            style={{
                              height: 10,
                              width: 200,
                              background:
                                'linear-gradient(to right, #003d6b 0%, #054372, #064677,#0f5285,#165a8e,#1e6296,#2772ac,#347fb8,#468cc1,#5797c7,#6baad8,#88bce3,#b5daf6)',
                              float: 'right',
                              marginTop: 2,
                              marginRight:
                                !this.props.isAdvisorCharts &&
                                !this.props.isTechnicianCharts
                                  ? 45
                                  : 51
                            }}
                          ></div>
                          <div>
                            <Typography
                              variant="h18"
                              color="primary"
                              style={{
                                textTransform: 'none',
                                //float: 'right',
                                fontSize: 10,
                                //marginRight: -20,
                                color: 'black'
                                //paddingTop: 7
                              }}
                            >
                              High
                            </Typography>
                            <Typography
                              variant="h18"
                              color="primary"
                              style={{
                                textTransform: 'none',
                                float: 'right',
                                fontSize: 10,
                                marginRight: 46,
                                color: 'black',
                                paddingTop: 7
                              }}
                            >
                              Low
                            </Typography>
                          </div>
                        </div>
                        <Tooltip
                          title="Export To Excel"
                          style={{
                            // marginLeft: 404,
                            verticalAlign: 'bottom',
                            cursor: 'pointer',
                            float: 'right',
                            display: 'flex',
                            alignSelf: 'center'
                          }}
                        >
                          <Link
                            id="export-to-excel"
                            // style={{cursor: 'pointer' ,
                            //   paddingLeft: ((this.state.reportType).includes('SA_'))  == true ? 499 : 525
                            // }}
                            onClick={this.exportReportGrid}
                          >
                            <ExportIcon />
                          </Link>
                        </Tooltip>
                      </Grid>
                    )}
                  {this.state.tabSelection == 'four' && (
                    // this.state.opcode == '' &&
                    <Grid xs={4} className={classes.gridContainer}>
                      <Tooltip title="Export To Excel">
                        <Link
                          id="export-to-excel"
                          onClick={this.exportReportGrid}
                        >
                          <ExportIcon />
                        </Link>
                      </Tooltip>
                    </Grid>
                  )}
                  {this.state.tabSelection == 'nine' &&
                    this.state.technician == '' && (
                      <Grid xs={4} className={classes.gridContainer}>
                        <Tooltip title="Export To Excel">
                          <Link
                            id="export-to-excel"
                            onClick={this.exportReportGrid}
                          >
                            <ExportIcon />
                          </Link>
                        </Tooltip>
                      </Grid>
                    )}
                  {this.state.drillDown >= 0 &&
                    this.state.drillDown != 'undefined' && (
                      <Grid xs={4} className={classes.gridContainer}>
                        <Tooltip title="Export To Excel">
                          <Link
                            id="export-to-excel"
                            onClick={this.exportReportGrid}
                          >
                            <ExportIcon />
                          </Link>
                        </Tooltip>
                      </Grid>
                    )}
                </Grid>
              </Paper>
            ) : (
              <React.Fragment>
                <Paper square className={classes.reportTopBar}>
                  <Grid
                    container
                    xs={12}
                    className={classes.rankedTableContainer}

                    //className={clsx(classes.titleContainer, 'reset-dashboard')}
                  >
                    <Grid xs={8} className={classes.rankButtonGrid}>
                      <Typography
                        variant="h6"
                        color="primary"
                        style={{
                          textTransform: 'none'
                        }}
                      >
                        Labor Monthly Tables
                      </Typography>
                      <div>
                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_lbrsale_report"
                            className={
                              this.state.reportType == 'SA_lbrsale' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_lbrsale')
                            }
                          >
                            Labor Sale
                          </Button>
                        ) : null}

                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_soldhours_report"
                            className={
                              this.state.reportType == 'SA_soldhours' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_soldhours')
                            }
                          >
                            Sold Hours
                          </Button>
                        ) : null}
                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_jobcount_report"
                            className={
                              this.state.reportType == 'SA_jobcount' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_jobcount')
                            }
                          >
                            Job Count
                          </Button>
                        ) : null}
                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_profit_report"
                            className={
                              this.state.reportType == 'SA_profit' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_profit')
                            }
                          >
                            Profit
                          </Button>
                        ) : null}

                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_elr_report"
                            className={
                              this.state.reportType == 'SA_elr' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() => this.handleReportsChanges('SA_elr')}
                          >
                            ELR
                          </Button>
                        ) : null}

                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_rocount_report"
                            className={
                              this.state.reportType == 'SA_rocount' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_rocount')
                            }
                          >
                            RO Count
                          </Button>
                        ) : null}
                      </div>
                    </Grid>
                  </Grid>
                </Paper>
                <Paper square className={classes.reportTopBar}>
                  <Grid
                    container
                    xs={12}
                    className={classes.rankedTableContainer}

                    //className={clsx(classes.titleContainer, 'reset-dashboard')}
                  >
                    <Grid xs={8} className={classes.rankButtonGrid}>
                      <Typography
                        variant="h6"
                        color="primary"
                        style={{
                          textTransform: 'none'
                        }}
                      >
                        Parts Monthly Tables
                      </Typography>
                      <div>
                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_prtssale_report"
                            className={
                              this.state.reportType == 'SA_prtssale' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_prtssale')
                            }
                          >
                            Parts Sale
                          </Button>
                        ) : null}

                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_prtscost_report"
                            className={
                              this.state.reportType == 'SA_prtscost' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_prtscost')
                            }
                          >
                            Parts Cost
                          </Button>
                        ) : null}

                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_prtsprofit_report"
                            className={
                              this.state.reportType == 'SA_prtsprofit' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_prtsprofit')
                            }
                          >
                            Parts Profit
                          </Button>
                        ) : null}
                        {isAdvisorCharts == true ? (
                          <Button
                            id="SA_markup_report"
                            className={
                              this.state.reportType == 'SA_markup' &&
                              this.state.changeTab != true
                                ? classes.reportButtonSelect
                                : this.state.changeTab == true
                                ? classes.anotherClass
                                : classes.reportButton
                            }
                            variant="outlined"
                            onClick={() =>
                              this.handleReportsChanges('SA_markup')
                            }
                          >
                            Markup
                          </Button>
                        ) : null}
                      </div>
                    </Grid>

                    {this.state.isReportSelected && (
                      <Grid xs={4} className={classes.gridContainer}>
                        <div style={{ display: 'grid' }}>
                          <Typography
                            variant="h14"
                            color="primary"
                            style={{
                              textTransform: 'none',
                              float: 'right',

                              marginRight:
                                !this.props.isAdvisorCharts &&
                                !this.props.isTechnicianCharts
                                  ? 160
                                  : 166,
                              fontSize: 12
                            }}
                          >
                            Ranking Per Row
                          </Typography>
                          <div
                            // '#003d6b',
                            // '#054372',
                            // '#064677',
                            // '#0f5285',
                            // '#165a8e',
                            // '#1e6296',
                            // '#2772ac',
                            // '#347fb8',
                            // '#468cc1',
                            // '#5797c7',
                            // '#6baad8',
                            // '#88bce3',
                            // '#b5daf6'
                            style={{
                              height: 10,
                              width: 200,
                              background:
                                'linear-gradient(to right, #003d6b 0%, #054372, #064677,#0f5285,#165a8e,#1e6296,#2772ac,#347fb8,#468cc1,#5797c7,#6baad8,#88bce3,#b5daf6)',
                              float: 'right',
                              marginTop: 2,
                              marginRight:
                                !this.props.isAdvisorCharts &&
                                !this.props.isTechnicianCharts
                                  ? 45
                                  : 51
                            }}
                          ></div>
                          <div>
                            <Typography
                              variant="h18"
                              color="primary"
                              style={{
                                textTransform: 'none',
                                //float: 'right',
                                fontSize: 10,
                                //marginRight: -20,
                                color: 'black'
                                //paddingTop: 7
                              }}
                            >
                              High
                            </Typography>
                            <Typography
                              variant="h18"
                              color="primary"
                              style={{
                                textTransform: 'none',
                                float: 'right',
                                fontSize: 10,
                                marginRight: 46,
                                color: 'black',
                                paddingTop: 7
                              }}
                            >
                              Low
                            </Typography>
                          </div>
                        </div>
                        <Tooltip
                          title="Export To Excel"
                          style={{
                            // marginLeft: 404,
                            verticalAlign: 'bottom',
                            cursor: 'pointer',
                            float: 'right',
                            display: 'flex',
                            alignSelf: 'center'
                          }}
                        >
                          <Link
                            id="export-to-excel"
                            // style={{cursor: 'pointer' ,
                            //   paddingLeft: ((this.state.reportType).includes('SA_'))  == true ? 499 : 525
                            // }}
                            onClick={this.exportReportGrid}
                          >
                            <ExportIcon />
                          </Link>
                        </Tooltip>
                      </Grid>
                    )}

                    {/* {this.state.tabSelection == 'eleven' &&
                      this.state.serviceadvisor == '' && (
                        <Grid xs={4} className={classes.gridContainer}>
                          <Tooltip title="Export To Excel">
                            <Link onClick={this.exportReportGrid}>
                              <ExportIcon />
                            </Link>
                          </Tooltip>
                        </Grid>
                      )} */}
                  </Grid>
                </Paper>
              </React.Fragment>
            )}
          </Grid>
        </Grid>
        {/* {isEfficiencyCharts == true &&
        isWorkMixRouting == true &&
        this.state.tabSelection == 'one' ? (
          <Grid
            item
            xs={12}
            style={{
              marginTop: -11
              //display: this.state.chartZoomed ? 'none' : 'initial'
            }}
          >
            <Paper square className={classes.reportTopBar}>
              <Typography
                variant="h6"
                color="primary"
                style={{
                  textTransform: 'none'
                }}
              >
                These 4 base charts represent the data in the larger Dashletts
                below
              </Typography>
            </Paper>
          </Grid>
        ) : (
          ''
        )} */}
        {this.state.tabSelection == 'five' ? (
          // && (this.state.isReportSelected == false || ( this.state.isReportSelected == true &&
          //   (this.state.tabSelection != this.state.reportTab))) ? (
          <ROLevelRawDataGrid
            //key={this.state.reloadKey}
            isLabor={!this.state.isPartsCharts}
            isFrom={this.state.isFrom}
            parentCallback={this.callbackFunction}
            parent={this.state.parent}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            month_year={
              // this.state.month_year.length > 1
              //   ? this.state.queryMonth
              //   : this.state.month_year
              this.state.month_year.length > 1 && this.state.opcode
                ? this.state.month_year
                : this.state.queryMonth
            }
            opcode={this.state.opcode}
            history={this.props.history}
            realm={this.props.keycloak.realm}
            pageType={this.state.pageType}
            reportTypeWorkmix={this.state.workmixreportType}
            workmixParent={this.state.workmixParent}
            workmixTab={this.state.workmixTab}
            selectedReportType={this.state.selectedReportType}
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            selectedFilter={this.state.selectedFilter}
            selectedToggle={this.state.selectedToggle}
            page={this.state.page}
            roNumber={this.state.roNumber}
            payTypeRo={this.state.payTypeRo}
            opcodePayType={this.state.opcodePayType}
            itemizationData={this.state.itemizationData}
            itemizationTab={this.state.itemizationTab}
            //isLabor={this.state.isLabor}
            selectedButtonType={this.state.selectedButtonType}
            isExpanded={this.state.isExpanded}
            selectedOpcodeAll={this.state.selectedOpcodeAll}
            prevPath={this.state.prevPath}
            prevMenu={this.state.prevMenu}
            selectValue={this.props.selectValue}
            originData={this.state.originData}
          />
        ) : null}

        {this.state.tabSelection == 'one' ? (
          // && (this.state.isReportSelected == false || ( this.state.isReportSelected == true &&
          // (this.state.tabSelection != this.state.reportTab))) ? (
          //  dispatch(setCurrentPath(location.search))
          <div id="work-mix">
            <WorkMixCharts
              key={this.state.reloadKey}
              removeFav={this.removeFav}
              hideGrids={this.hideGrids}
              partialMonth={this.state.checked}
              parentCallback={this.callbackFunction}
              isPartsCharts={this.state.isPartsCharts}
              realm={this.props.keycloak.realm}
              session={this.props.session}
              history={this.props.history}
            ></WorkMixCharts>
          </div>
        ) : null}
        {this.state.tabSelection == 'two' ? (
          // && (this.state.isReportSelected == false || ( this.state.isReportSelected == true &&
          //   (this.state.tabSelection != this.state.reportTab))) ? (
          <ComparisonChartsGrid
            key={this.state.reloadKey}
            parentCallback={this.callbackFunction}
            removeFav={this.removeFav}
            mon1={this.state.queryMonth}
            mon2={this.state.previousMonth}
            hideGrids={this.hideGrids}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            isPartsCharts={this.state.isPartsCharts}
            realm={this.props.keycloak.realm}
            handleWorkmixChartPopup={this.handleChartPopup}
          />
        ) : null}
        {/* {this.state.tabSelection == 'three' ? ( */}
        {this.state.tabSelection == 'three' ? (
          // || (this.state.isReportSelected && (this.state.tabSelection == this.state.reportTab) )? (
          localStorage.getItem('versionFlag') == 'TRUE' ? (
            <Reports
              // key={this.state.reloadKey}
              isFrom={this.state.isFrom}
              isEfficiencyCharts={isEfficiencyCharts}
              isTechnicianCharts={isTechnicianCharts}
              isWorkMixRouting={isWorkMixRouting}
              isAdvisorCharts={isAdvisorCharts}
              parentCallback={this.callbackFunction}
              isParts={this.state.isPartsCharts}
              reportType={this.state.reportType}
              reportTabSelection={this.state.reportTabSelection}
              selectedReportType={this.state.selectedReportType}
              resetReport={this.state.resetLayout}
              handleResetReport={this.handleResetReport}
              handleResetLayout={this.resetReportGrid}
              exportReport={this.state.exportReport}
              handleExportReport={this.handleExportReport}
              handleReportSelected={this.handleReportSelected}
              session={this.props.session}
              isExpanded={this.state.isExpanded}
              prevMenu={this.state.prevMenu}
              originData={this.state.originData}
            />
          ) : (
            <ReportsVersion2
              key={this.state.reloadKey}
              isFrom={this.state.isFrom}
              isEfficiencyCharts={isEfficiencyCharts}
              isTechnicianCharts={isTechnicianCharts}
              isWorkMixRouting={isWorkMixRouting}
              isAdvisorCharts={isAdvisorCharts}
              parentCallback={this.callbackFunction}
              isParts={this.state.isPartsCharts}
              reportType={this.state.reportType}
              reportTabSelection={this.state.reportTabSelection}
              selectedReportType={this.state.selectedReportType}
              resetReport={this.state.resetLayout}
              handleResetReport={this.handleResetReport}
              handleResetLayout={this.resetReportGrid}
              exportReport={this.state.exportReport}
              handleExportReport={this.handleExportReport}
              handleReportSelected={this.handleReportSelected}
              session={this.props.session}
              isExpanded={this.state.isExpanded}
              prevMenu={this.state.prevMenu}
            />
          )
        ) : null}

        {this.state.tabSelection == 'four' ? (
          // && (this.state.isReportSelected == false || ( this.state.isReportSelected == true &&
          //   (this.state.tabSelection != this.state.reportTab))) ? (
          <RawDataOpcodeGrid
            key={this.state.reloadKey}
            month={this.state.month_year}
            isFrom={this.state.isFrom}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            parent={this.state.parent}
            // monthData={this.state.previousMonth}
            opcode={this.state.opcode}
            isLabor={!this.state.isPartsCharts}
            parentCallback={this.callbackFunction}
            history={history}
            reportType={this.state.workmixreportType}
            workmixParent={this.state.workmixParent}
            workmixTab={this.state.workmixTab}
            selectedReportType={this.state.selectedReportType}
            opCategory={this.state.opCategory}
            exportReport={this.state.exportReport}
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            session={this.props.session}
            roNumber={this.state.roNumber}
            selectedFilter={this.state.selectedFilter}
            selectedToggle={this.state.selectedToggle}
            page={this.state.page}
            payTypeRo={this.state.payTypeRo}
            handleExportReport={this.handleExportReport}
            itemizationData={this.state.itemizationData}
            itemizationTab={this.state.itemizationTab}
            // isLabor={this.state.isLabor}
            selectedButton={this.state.selectedButton}
            itemsRef={this.props.itemsRef}
            isExpanded={this.state.isExpanded}
            selectedOpcodeAll={this.state.selectedOpcodeAll}
            prevPath={this.state.prevPath}
            roExpanded={this.state.roExpanded}
            workmix={
              history && history.location && history.location.workmix
                ? history.location.workmix
                : ''
            }
            selectValue={
              history && history.location && history.location.state
                ? history.location.state.selectValue
                : ''
            }
            originData={this.state.originData}
          />
        ) : null}

        {/* {this.state.tabSelection == 'six' ? (
          <WorkMixVolume
            category={this.state.isPartsCharts == true ? 'parts' : 'labor'}
            isFrom={this.state.isFrom}
            type={this.state.type}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            reportType={this.state.workmixreportType}
            parentCallback={this.callbackFunction}
          />
        ) : null} */}
        {this.state.tabSelection == 'seven' ? (
          <TechEfficiency
            key={this.state.reloadKey}
            mon1={this.state.queryMonth}
            mon2={this.state.previousMonth}
            tabSelection={this.state.selectedSubTab}
            mainTabSelection={this.state.tabSelection}
            parentCallback={this.callbackFunction}
            technicianSelected={
              this.props.history &&
              this.props.history.location &&
              this.props.history.location &&
              this.props.history.location.tech
                ? this.props.history.location.tech
                : this.state.parent == 'Home' && this.state.selectedTechName
                ? this.state.selectedTechName
                : this.state.parent == 'Home' && this.state.techName
                ? this.state.techName
                : this.state.selectedTech
                ? this.state.selectedTech
                : this.state.technician
            }
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            techName={
              this.props.history &&
              this.props.history.location &&
              this.props.history.location &&
              this.props.history.location.techName
                ? this.props.history.location.techName
                : this.state.selectedTechName
                ? this.state.selectedTechName
                : this.state.techName
            }
            removeFav={this.removeFav}
            session={this.props.session}
            parent={
              this.props.history &&
              this.props.history.location.parent &&
              this.props.history.location.parent == 'Home'
                ? this.props.history.location.parent
                : this.state.parent
            }
            selectedToggle={this.state.selectedToggle}
          />
        ) : null}

        {this.state.tabSelection == 'eight' ? (
          <ServiceAdvisorEfficiency
            key={this.state.reloadKey}
            mon1={this.state.queryMonth}
            mon2={this.state.previousMonth}
            month_year={this.state.month_year}
            tabSelection={this.state.selectedSubTab}
            mainTabSelection={this.state.tabSelection}
            technicianSelected={this.state.selectedAdvisor}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            parentCallback={this.callbackFunction}
            history={history}
            realm={this.props.keycloak.realm}
            removeFav={this.removeFav}
          />
        ) : null}

        {this.state.tabSelection == 'nine' ? (
          <TechnicianSummary
            key={this.state.reloadKey}
            month_year={this.state.month_year}
            type={this.state.type}
            history={this.props.history}
            // month_year={this.state.previousMonth}
            techNo={this.state.technician}
            techName={this.state.techName}
            isFrom={this.state.isFrom}
            chartType={this.state.chartType}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            parent={
              this.props.history &&
              this.props.history.location.parent &&
              this.props.history.location.parent == 'Home'
                ? this.props.history.location.parent
                : this.state.parent
            }
            parentCallback={this.callbackFunction}
            selectedReportType={this.state.selectedReportType}
            reportTabSelection={this.state.reportTabSelection}
            exportReport={this.state.exportReport}
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            handleExportReport={this.handleExportReport}
            session={this.props.session}
            payType={this.state.payType}
            monthSelected={this.state.monthSelected}
            prevMenu={this.state.prevMenu}
            selectedToggle={this.state.selectedToggle}
            drilldownVal={this.state.drilldownVal}
            weekStart={this.state.weekStart}
            weekEnd={this.state.weekEnd}
            techPageType={this.state.techPageType}
            chartId={this.state.chartId}
          />
        ) : null}

        {this.state.tabSelection == 'ten' ? (
          <TechnicianDetailedView
            key={this.state.reloadKey}
            month_year={
              this.state.techNo ? this.state.month_year : this.state.queryMonth
            }
            session={this.props.session}
            //month_year={this.state.previousMonth}
            techNo={this.state.techNo}
            type={this.state.type}
            parentCallback={this.callbackFunction}
            isFromTechnician={true}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            parent={this.state.parent}
            techName={this.state.techName}
            history={this.props.history}
            realm={this.props.keycloak.realm}
            pageType={this.state.pageType}
            selectedReportType={this.state.selectedReportType}
            reportTabSelection={this.state.reportTabSelection}
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            handleExportReport={this.handleExportReport}
            monthSelected={this.state.monthSelected}
            payType={this.state.payType}
            prevMenu={this.state.prevMenu}
            chartType={this.state.chartType}
            drilldownVal={this.state.drilldownVal}
            weekStart={this.state.weekStart}
            weekEnd={this.state.weekEnd}
            chartId={this.state.chartId}
          />
        ) : null}

        {this.state.tabSelection == 'eleven' ? (
          <ServiceAdvisorSummary
            key={this.state.reloadKey}
            month_year={this.state.month_year}
            chart_name={this.state.chart_name}
            session={this.props.session}
            serviceAdvisor={this.state.serviceadvisor}
            isFrom={this.state.isFrom}
            type={this.state.type}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            parentCallback={this.callbackFunction}
            parent={this.state.parent}
            selectedReportType={this.state.selectedReportType}
            reportTabSelection={this.state.reportTabSelection}
            exportReport={this.state.exportReport}
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            handleExportReport={this.handleExportReport}
            chartLoc={this.state.chartLoc}
            prevPath={this.state.prevPath}
            prevMenu={this.state.prevMenu}
          />
        ) : null}

        {this.state.tabSelection == 'twelve' ? (
          <TechnicianDetailedView
            key={this.state.reloadKey}
            session={this.props.session}
            month_year={
              this.state.serviceAdvisor
                ? this.state.month_year
                : this.state.queryMonth
            }
            serviceAdvisor={this.state.serviceAdvisor}
            type={this.state.type}
            parentCallback={this.callbackFunction}
            isFromTechnician={false}
            comparisonMonth1={this.state.comparisonMonth1}
            comparisonMonth2={this.state.comparisonMonth2}
            parent={this.state.parent}
            history={this.props.history}
            realm={this.props.keycloak.realm}
            pageType={this.state.pageType}
            selectedReportType={this.state.selectedReportType}
            reportTabSelection={this.state.reportTabSelection}
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            handleExportReport={this.handleExportReport}
            chartLoc={this.state.chartLoc}
            prevPath={this.state.prevPath}
            prevMenu={this.state.prevMenu}
          />
        ) : null}
      </div>
    );
  }
}

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  back: {
    marginRight: 5
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  reportButton: {
    height: 24,
    marginLeft: 4,
    color: '#757575',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  anotherClass: {
    color: '#757575 !important',
    background: 'none !important',
    height: '24px !important',
    marginLeft: '4px !important',
    '@media (max-width: 1920px)': {
      fontSize: '14px !important'
    },
    '@media (max-width: 1440px)': {
      fontSize: '10px !important'
    },
    '@media (max-width: 1280px)': {
      fontSize: '10px !important'
    },
    '@media (min-width: 2304px)': {
      fontSize: '17px !important'
    },
    borderColor: '1px solid rgba(0, 0, 0, 0.23) !important'
  },
  reportButtonSelect: {
    height: 24,
    marginLeft: 4,
    color: '#757575',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    borderColor: theme.palette.primary.main
  },

  reportTopBar: {
    //margin: 8,
    padding: 8,
    marginRight: 8,
    marginLeft: 8
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  dataAsofContainer: {
    display: 'flex',
    // justifyContent: 'flex-end',
    alignItems: 'center',
    justifyContent: 'flex-end',
    maxWidth: '100%'
  },
  dataAsofContainer1: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    marginRight: '1%'
  },
  dataAsofContainer2: {
    display: 'flex',
    justifyContent: 'flex-end',
    // alignItems: 'last baseline'
    alignItems: 'center'
  },
  dataLabel: {
    marginRight: 5,
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  rankedTableContainer: {
    display: 'flex',
    alignItems: 'center'
  },
  gridContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    width: '100%'
  },
  rankLabel: {
    textTransform: 'none',

    '@media (max-width: 1920px)': {
      fontSize: 14,
      marginRight: 30
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      marginRight: 0
    },
    '@media (max-width: 1280px)': {
      fontSize: 12,
      marginRight: 0
    },
    '@media (min-width: 2304px)': {
      fontSize: 17,
      marginRight: 30
    }
  },
  rankButtonGrid: {
    display: 'flex',
    alignItems: 'center'
    // justifyContent: 'space-between'
  },
  partialToggle: {
    height: 21,
    marginLeft: 4,
    marginRight: 7,
    //color: '#757575',
    border: '2px solid #7d97aa',
    height: 30,
    borderColor: theme.palette.primary.main,
    background: '#003d6b',
    color: '#fff',
    '@media (min-width: 2560px)': {
      fontSize: 12,
      width: 176
    },
    '@media (max-width: 2304px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 1920px)': {
      fontSize: 12,
      width: 176
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 178
    },
    '&:hover': {
      background: '#003d6b',
      color: '#fff',
      cursor: 'pointer'
    }
  },
  partialToggleSelect: {
    height: 21,
    marginLeft: 4,
    marginRight: 7,
    border: '2px solid #7d97aa',
    height: 30,
    //color: '#757575',
    borderColor: theme.palette.primary.main,
    // background: 'rgb(221, 234, 244)',
    // color: 'rgb(0, 61, 107)',
    background: '#003d6b',
    color: '#fff',
    '@media (min-width: 2560px)': {
      fontSize: 12,
      width: 176
    },
    '@media (max-width: 2304px)': {
      fontSize: 12,
      width: 175
    },
    '@media (max-width: 1920px)': {
      fontSize: 12,
      width: 172
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 172
    },
    '&:hover': {
      background: '#003d6b',
      color: '#fff',
      cursor: 'pointer'
    }
  }
});

export default connect()(withKeycloak(withStyles(styles)(Charts)));
