import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import ExportIcon from '@material-ui/icons/GetApp';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  Button,
  Tooltip
} from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import moment from 'moment';
import React from 'react';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { getAgGridChartOptions } from 'src/utils/Utils';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import {
  getDrillDownDataForReturnRate,
  getDataReturnedForCurrentMonth,
  getDataReturnedForLastMonths,
  getDrillDownDataForReturnRateSA
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Link from '@material-ui/core/Link';
import {
  myCustomSumAggregate,
  distinctROCount
} from 'src/components/DrillDownCalculations';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import clsx from 'clsx';
import { ReactSession } from 'react-client-session';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
var lodash = require('lodash');

class DrilldownReturnRate extends React.Component {
  componentWillMount() {
    console.log('enter=000');
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps, prevState) {
    if (ReactSession.get('selectedStoreId') != undefined) {
      console.log('enter=222');
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        this.getAgGridData(this.state.queryMonth);
        // this.getAgGridData(this.state.queryMonth,this.props.session.serviceAdvisor);
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ isLoading: true });
      }
    }
  }
  componentDidUpdate(prevProps, prevState) {
    const dateFormat = item => moment(item).format('YYYY-MM');

    var initialQueryMonth =
      typeof this.props.params != 'undefined' &&
      typeof this.props.params.y != 'undefined'
        ? dateFormat(this.props.params.y)
        : typeof this.props.history.location.state != 'undefined' &&
          this.props.history.location.state != null
        ? dateFormat(this.props.history.location.state.y)
        : '';

    var drillDownServiceAdvisor = JSON.stringify(
      this.props.session.serviceAdvisor
    );
    var previousServiceAdvisor = JSON.stringify(
      prevProps.session.serviceAdvisor
    );

    var isContain = lodash.includes(
      previousServiceAdvisor,
      drillDownServiceAdvisor
    );
    if (isContain == false) {
      this.setState({ isLoading: true });
      this.setState({
        drillDownServiceAdvisor: this.props.session.serviceAdvisor
      });
      this.getAgGridData(initialQueryMonth, this.props.session.serviceAdvisor);
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = this.props.params
      ? dateFormat(this.props.params.y)
      : dateFormat(this.props.history.location.state.y);
    var category = this.props.params
      ? this.props.params.category
      : this.props.history.location.state.category;
    var chartName = this.props.params
      ? this.props.params.chartName
      : this.props.history.location.state.chartName;
    var drillDown = this.props.params
      ? this.props.params.drillDown
      : this.props.history.location.state.drillDown;
    var chartId = this.props.params
      ? this.props.params.chartId
      : this.props.history.location.state.chartId;
    var drillDownServiceAdvisor = JSON.stringify(
      this.props.session.serviceAdvisor
    );
    var isFromMetrices = this.props.history.location.state.isFromMetrices
      ? this.props.history.location.state.isFromMetrices
      : false;
    this.state = {
      previousLocation: this.props.history.location.prevPath,
      showCharts: false,
      tabSelection: 'one',
      queryMonth: initialQueryMonth,
      isLoading: true,
      category: category,
      chartName: chartName,
      chartId: chartId,
      drillDown: drillDown,
      rawGridApi: {},
      gridApi: {},
      drillDownServiceAdvisor: drillDownServiceAdvisor,
      isFromMetrices: isFromMetrices,
      modules: [
        ClientSideRowModelModule,
        RowGroupingModule,
        MenuModule,
        ColumnsToolPanelModule,
        SetFilterModule
      ],
      columnDefs: [
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 100,
          minWidth: 100,
          // flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'RO Closed Date',
          field: 'rodate',
          width: 120,
          minWidth: 120,
          // flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          cellClass: 'dateUS',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Advisor',
          field: 'serviceadvisor',
          width: 150,
          minWidth: 150,
          flex: 1,
          chartDataType: 'category',
          hide: false,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Vin Count\n6 Months',
          field: 'totalVinsInLastSixMonths',
          width: 120,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          minWidth: 120,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Vin Count\n12 Months',
          field: 'totalVinsInLastTwelveMonths',
          width: 120,
          minWidth: 120,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Vin Count\nLast 6 Months',
          field: 'lastSixMonVinsRevInCurrMon',
          width: 150,
          minWidth: 150,

          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Vin Count\nLast 12 Months',
          field: 'lastTwelveMonVinsRevInCurrMon',
          width: 150,
          minWidth: 150,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '6 Month Return Rate',
          field: 'sixMonthReturnrate',
          width: 130,
          minWidth: 130,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '12 Month Return Rate',
          field: 'twelveMonthReturnrate',
          width: 130,
          minWidth: 130,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefs1: [
        {
          headerName: 'Month',
          field: 'monthYear',
          chartDataType: 'category',
          width: 130,
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'RO Closed Date',
          field: 'roDate',
          chartDataType: 'category',
          width: 160,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Return Rate',
          field: 'returnrate',
          chartDataType: 'category',
          width: 120,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Vin',
          field: 'vin',
          chartDataType: 'category',
          width: 200,
          minWidth: 200,
          valueFormatter: this.formatCellValueVin,
          filterParams: {
            valueFormatter: this.formatCellValueVin
          },
          filter:
            window.location.hostname == 'demoenterprise-simt.fixedops.cc'
              ? false
              : true,
          // flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Vin Count\nLast 6 Months',
          field: 'vincountLastSixMonths',
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          width: 220,
          minWidth: 220,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Vin Count\nLast 12 Months',
          field: 'vincountLastTwelveMonths',
          width: 240,
          minWidth: 240,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,

          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefs2: [
        {
          headerName: 'Month',
          rowGroup: true,
          field: 'month_year',
          chartDataType: 'category',
          width: 100,
          minWidth: 100,
          flex: 1,
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Vin',
          field: 'vin',
          chartDataType: 'category',
          width: 150,
          minWidth: 150,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Open Date',
          field: 'opendate',
          chartDataType: 'category',
          width: 150,
          minWidth: 150,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          chartDataType: 'category',
          width: 150,
          minWidth: 150,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'RO ',
          field: 'ronumber',
          chartDataType: 'category',
          cellClass: 'textAlign',
          width: 150,
          minWidth: 150,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          onCellClicked: this.handleSearchByRo,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        }
      ],
      autoGroupColumnDef: { minWidth: 200 },
      rowData: [],
      headerHeight: 50,

      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        //  wrapText: true,
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: true
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 20,
            color: 'primary'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'dateUS',
          dataType: 'dateTime',
          numberFormat: {
            format: 'yyyy/MM/dd;@'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'center',
      border: ' 0px white',
      'white-space': 'normal !important'
    };
  };

  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValueVin = params => {
    if (
      params.value != null &&
      params.value != '' &&
      window.location.hostname == 'demoenterprise-simt.fixedops.cc'
    ) {
      return 'xxxxxxxx';
    } else {
      return params.value;
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  handleSearchByRo = params => {
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        parentCallback: this.props.callbackFunction,
        month_year: this.state.queryMonth,
        serviceAdvisor: 'All',
        type: this.props.type,
        chartId: this.props.chartId
          ? this.props.chartId
          : this.props.history.location.search.split('?chartId=').pop(),
        drillDown: this.props.drillDown,
        pageType: 'Specialmetrics',
        prevPath: this.state.previousLocation
      }
    });
  };
  onBtExport = () => {
    var params = {
      sheetName: this.state.chartName,
      fileName: this.state.chartName,
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: this.state.chartName },
            mergeAcross: 6
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  onBtExport1 = () => {
    var params = {
      sheetName: this.state.chartName + ' - VINS RETURNED IN CURRENT MONTH',
      fileName: this.state.chartName + ' - VINS RETURNED IN CURRENT MONTH',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: this.state.chartName + ' - VINS RETURNED IN CURRENT MONTH'
            },
            mergeAcross: 6
          }
        ]
      ]
    };

    this.state.rawGridApi1.exportDataAsExcel(params);
  };
  onBtExport2 = () => {
    var params = {
      sheetName: this.state.chartName + ' - VINS IN LAST MONTHS',
      fileName: this.state.chartName + ' - VINS IN LAST MONTHS',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: this.state.chartName + ' - VINS IN LAST MONTHS'
            },
            mergeAcross: 6
          }
        ]
      ]
    };

    this.state.rawGridApi2.exportDataAsExcel(params);
  };
  onGridReady = params => {
    // params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;
    this.setState({ gridcolumnApi: params.columnApi });

    this.getAgGridData(this.state.queryMonth);
  };
  onGridReady1 = params => {
    //  params.api.closeToolPanel();
    this.setState({ rawGridApi1: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    //this.gridApi.sizeColumnsToFit();
    this.gridColumnApi = params.columnApi;

    this.setState({ gridcolumnApi: params.columnApi });

    this.getAgGridData(this.state.queryMonth);
  };
  onGridReady2 = params => {
    // params.api.closeToolPanel();
    this.setState({ rawGridApi2: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    //this.gridApi.sizeColumnsToFit();
    this.setState({ gridcolumnApi: params.columnApi });

    this.getAgGridData(this.state.queryMonth);
  };
  getAgGridData(queryMonth, advisor) {
    var formData = {};
    var drillDownServiceAdvisor =
      advisor && advisor.includes('All')
        ? 'All'
        : advisor && advisor.includes('All') == false
        ? advisor
        : this.state.drillDownServiceAdvisor.includes('All')
        ? 'All'
        : this.props.session.serviceAdvisor.join().split(',');

    drillDownServiceAdvisor.includes('All')
      ? this.state.rawGridApi.columnController.columnApi.setColumnsVisible(
          ['serviceadvisor'],
          false
        )
      : this.state.rawGridApi.columnController.columnApi.setColumnsVisible(
          ['serviceadvisor'],
          true
        );
    if (drillDownServiceAdvisor.includes('All') == false) {
      getDrillDownDataForReturnRateSA(
        queryMonth,
        drillDownServiceAdvisor,
        result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessCcDrilldownGetDrillDownReturnRateByServiceAdvisor.nodes
          ) {
            this.setState({
              rowData:
                result.data
                  .statelessCcDrilldownGetDrillDownReturnRateByServiceAdvisor
                  .nodes
            });
          }
        }
      );
    } else {
      getDrillDownDataForReturnRate(queryMonth, result => {
        this.setState({ isLoading: false });
        if (result.data.statelessDbdSpecialMetricsGetReturnRate.nodes) {
          this.setState({
            rowData: result.data.statelessDbdSpecialMetricsGetReturnRate.nodes
          });
        }
      });
    }
    getDataReturnedForCurrentMonth(queryMonth, result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessCcDrilldownGetDrillDownReturnRateCountDetail.nodes
      ) {
        this.setState({
          rowData1:
            result.data.statelessCcDrilldownGetDrillDownReturnRateCountDetail
              .nodes
        });
      }
    });
    getDataReturnedForLastMonths(queryMonth, result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessCcDrilldownGetDrillDownReturnRateVinDetails.nodes
      ) {
        var roData = this.fetchData(
          result.data.statelessCcDrilldownGetDrillDownReturnRateVinDetails.nodes
        );
        this.setState({
          rowData2: roData
        });
      }
    });
  }
  fetchData = rodata => {
    var json_data = new Array();
    rodata.map(roItem => {
      if (roItem.roDetails) {
        var ro_data = JSON.parse(roItem.roDetails);
        ro_data.map(details => {
          json_data.push(details);
        });
      }
    });
    return json_data;
  };

  renderBackButton = () => {
    {
      const historyLocation = this.state.previousLocation;
      if (
        this.props.history.location.selectedGrid === undefined ||
        this.props.history.location.selectedGrid === false
      ) {
        this.props.history.push(historyLocation);
      }
      if (
        this.props.history.location.selectedGrid === true &&
        !historyLocation.includes('/GraphDetailsView')
      ) {
        this.props.history.push({
          pathname: historyLocation,
          handleHighlight: this.props.history.location.handleHighlight,
          selectedGrid: this.props.history.location.selectedGrid
        });
      }
      if (
        this.props.history.location.selectedGrid === true &&
        historyLocation.includes('/GraphDetailsView')
      ) {
        this.props.history.push({
          pathname: '/GraphDetailsView',
          search: '?chartId=' + historyLocation.split('?chartId=').pop(),
          handleHighlight: this.props.history.location.handleHighlight,
          selectedGrid: this.props.history.location.selectedGrid
        });
      }

      // renderBackButtonForDrillDown(
      //   this.state.title,
      //   this.state.chartId,
      //   this.state.titleCategory,
      //   this.props.history,
      //   this.state.isParts
      // );
    }
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        {/* <Paper square style={{ margin: 8, marginTop: '20px' }}>
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Link
              href="#"
              style={{ paddingTop: 12, paddingLeft: 16, cursor: 'pointer' }}
              title="Go Back"
              onClick={this.renderBackButton}
            >
              <ArrowBackIcon />
            </Link>
            <Tab
              style={{ textTransform: 'none', paddingRight: 182 }}
              label={<div>{this.state.chartName}</div>}
              value="one"
            />
            <Tooltip title="Export To Excel">
              <Link
                style={{ paddingTop: 12, paddingRight: 27, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
          </Tabs>
        </Paper> */}
        <Paper
          square
          style={{
            margin: 8,
            paddingTop: '6px',
            height: '40px',
            width: '1060px'
          }}
        >
          <Grid
            container
            className={clsx(this.props.titleContainer, 'reset-dashboard')}
          >
            <Grid item xs={4} style={{ display: 'flex', alignItems: 'left' }}>
              <Button
                variant="contained"
                className={'bck-btn'}
                onClick={this.renderBackButton}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button>
            </Grid>
            <Grid item xs={7} style={{ display: 'flex', alignItems: 'right' }}>
              <Typography
                variant="h4"
                color="primary"
                className={clsx(this.props.mainLabel, 'main-title')}
              >
                {this.state.chartName}
              </Typography>
            </Grid>
            {this.state.rowData && this.state.rowData.length > 0 && (
              <Grid
                item
                xs={1}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      paddingTop: 4,
                      paddingLeft: 42,
                      cursor: 'pointer'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon
                      style={{
                        display:
                          !this.state.rowData || this.state.rowData.length === 0
                            ? 'none'
                            : 'block'
                      }}
                    />
                  </Link>
                </Tooltip>
              </Grid>
            )}
          </Grid>
        </Paper>
        {this.state.isLoading && (
          <div
            style={{
              display: this.state.tabSelection != 'one' ? 'none' : 'block'
            }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}

        <div
          id="returnRateDrilldown"
          className="ag-theme-balham"
          style={{
            height:
              this.state.rowData.length == 1
                ? '129px'
                : 129 + 20 * this.state.rowData.length + 'px',
            width: '1060px',
            margin: 8,
            display: this.state.tabSelection == 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            enableRangeSelection={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            headerHeight={this.state.headerHeight}
            excelStyles={this.state.excelStyles}
            floatingFilter={true}
            suppressRowClickSelection={true}
            suppressContextMenu={true}
          />
        </div>

        <Paper square style={{ margin: 8, width: '1060px' }}>
          <Tabs variant="fullWidth" aria-label="icon label tabs example">
            <Tab
              style={{ textTransform: 'none', paddingRight: 182 }}
              label={<div>VINS RETURNED IN CURRENT MONTH</div>}
              value="one"
            />
            {this.state.rowData1 && this.state.rowData1.length > 0 && (
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{
                    paddingTop: 12,
                    paddingRight: 27,
                    cursor: 'pointer'
                  }}
                  onClick={this.onBtExport1}
                >
                  <ExportIcon
                    style={{
                      display:
                        !this.state.rowData1 || this.state.rowData1.length === 0
                          ? 'none'
                          : 'block'
                    }}
                  />
                </Link>
              </Tooltip>
            )}
          </Tabs>
        </Paper>
        <div
          id="returnRateDrilldown"
          className="ag-theme-balham"
          style={{
            height: '600px',
            width: '1060px',
            margin: 8,
            display: this.state.tabSelection == 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            enableRangeSelection={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs1}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady1}
            rowData={this.state.rowData1}
            excelStyles={this.state.excelStyles}
            floatingFilter={true}
            suppressRowClickSelection={true}
            suppressContextMenu={true}
          />
        </div>

        <Paper square style={{ margin: 8, width: '1060px' }}>
          <Tabs variant="fullWidth" aria-label="icon label tabs example">
            <Tab
              style={{ textTransform: 'none', paddingRight: 182 }}
              label={<div>VINS IN LAST MONTHS</div>}
              value="one"
            />
            {this.state.rowData2 && this.state.rowData2.length > 0 && (
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{
                    paddingTop: 12,
                    paddingRight: 27,
                    cursor: 'pointer'
                  }}
                  onClick={this.onBtExport2}
                >
                  <ExportIcon
                    style={{
                      display:
                        !this.state.rowData2 || this.state.rowData2.length === 0
                          ? 'none'
                          : 'block'
                    }}
                  />
                </Link>
              </Tooltip>
            )}
          </Tabs>
        </Paper>
        <div
          id="returnRateDrilldown"
          className="ag-theme-balham"
          style={{
            height: '600px',
            width: '1060px',
            margin: 8,
            display: this.state.tabSelection == 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            enableRangeSelection={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            enableCharts={true}
            modules={this.state.modules}
            columnDefs={this.state.columnDefs2}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady2}
            rowData={this.state.rowData2}
            excelStyles={this.state.excelStyles}
            floatingFilter={true}
            suppressRowClickSelection={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  }
});

export default withStyles(styles)(DrilldownReturnRate);
