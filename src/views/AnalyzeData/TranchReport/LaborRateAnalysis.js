import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import { Typography, LinearProgress, Box, Paper } from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import {
  getElrWarrantyPaytypes,
  getTrancheReporForLaborRateAnalysis,
  getTopFiveOpportunities,
  getValidMakes
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Link from '@material-ui/core/Link';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import PDFExportPanel from 'src/pdfExport/PDFExportPanel.js';
import CustomPinnedRowRenderer from 'src/customPinnedRowRenderer.jsx';
import IncludedOpcodesReport from './IncludedOpcodesReport';
import ExcludedOpcodesReport from './ExcludedOpcodesReport';
import ExitToAppIcon from '@material-ui/icons/ExitToApp';
import { IconButton, Tooltip } from '@material-ui/core';
import { getAggregateForTrancheReports } from 'src/components/DrillDownCalculations';
import {
  createDataForTrancheReports,
  getLastSixMonths,
  titleCase
} from 'src/utils/Utils';
import { withKeycloak } from '@react-keycloak/web';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
class LaborRateAnalysis extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    var currentRate = this.props.history.location.state
      ? this.props.history.location.state.currentRate
      : localStorage.getItem('currentWarrantyRate');
    var realm = this.props.keycloak.realm;
    this.state = {
      previousLocation: this.props.history.location.SelectedLocation
        ? this.props.history.location.SelectedLocation
        : '/LaborTranches',
      storeId: storeId,
      showCharts: false,
      tabSelection: 'one',
      isLoading: true,
      rawGridApi: {},
      gridApi: {},
      columnApi: {},
      currentRate: currentRate,
      AnalysisElr: 0,
      MonthlyOpportunity: 0,
      AnnualOpportunity: 0,
      startDate: '',
      endDate: '',
      ElrWarrantyPaytypes: [],
      showModal: false,
      labrSale: 0,
      soldHours: 0,
      elr: 0,
      topFiveOpportunities: [],
      themeName: '',
      topFiveOpcodeArr: [],
      opcodeArr: [],
      reportName: 'Best 100 ROs',
      opportunityFlag: false,
      validMakes: '',
      modules: [
        ClientSideRowModelModule,
        RowGroupingModule,
        MenuModule,
        ColumnsToolPanelModule,
        SetFilterModule
      ],
      columnDefs: [
        {
          headerName: 'RO',
          filter: 'agTextColumnFilter',
          field: 'ronumber',
          width: realm == 'ferrarioat_store' ? 80 : 95,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          onCellClicked: this.handleSearchByRo,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-25px',
              cursor: 'pointer'
            };
          },
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { float: 'right', fontSize: 15 }
          }
        },
        {
          headerName: 'Open Date',
          field: 'opendate',
          width: 88,
          chartDataType: 'category',
          filter: 'agDateColumnFilter',
          filterParams: {
            browserDatePicker: true,
            comparator: function(filterLocalDateAtMidnight, cellValue) {
              var dateAsString = moment(cellValue).format('YYYY-MM-DD');
              if (dateAsString == null) return -1;

              var dateParts = dateAsString.split('-');
              var cellDate = new Date(
                Number(dateParts[0]),
                Number(dateParts[1]) - 1,
                Number(dateParts[2])
              );

              if (filterLocalDateAtMidnight.getTime() == cellDate.getTime()) {
                return 0;
              }

              if (cellDate < filterLocalDateAtMidnight) {
                return -1;
              }

              if (cellDate > filterLocalDateAtMidnight) {
                return 1;
              }
            }
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filter: 'agDateColumnFilter',
          filterParams: {
            browserDatePicker: true,
            comparator: function(filterLocalDateAtMidnight, cellValue) {
              var dateAsString = moment(cellValue).format('YYYY-MM-DD');
              if (dateAsString == null) return -1;

              var dateParts = dateAsString.split('-');
              var cellDate = new Date(
                Number(dateParts[0]),
                Number(dateParts[1]) - 1,
                Number(dateParts[2])
              );

              if (filterLocalDateAtMidnight.getTime() == cellDate.getTime()) {
                return 0;
              }

              if (cellDate < filterLocalDateAtMidnight) {
                return -1;
              }

              if (cellDate > filterLocalDateAtMidnight) {
                return 1;
              }
            }
          }
        },
        {
          headerName: 'Pay Type',
          filter: 'agTextColumnFilter',
          field: 'paytype',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category'
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: realm == 'ferrarioat_store' ? 70 : 85,
          minWidth: realm == 'ferrarioat_store' ? 70 : 85,
          flex: 1,
          filter: 'agTextColumnFilter',
          dataType: 'string'
        },
        {
          headerName: 'Opcode',
          filter: 'agTextColumnFilter',
          field: 'lbropcode',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          flex: 1,
          chartDataType: 'category'
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: realm == 'ferrarioat_store' ? 100 : 120,
          minWidth: realm == 'ferrarioat_store' ? 100 : 120,
          flex: 1,
          chartDataType: 'category',
          minWidth: 150,
          resizable: true
        },
        {
          headerName: 'Op Category',
          filter: 'agTextColumnFilter',
          field: 'opcategory',
          width: realm == 'ferrarioat_store' ? 90 : 110,
          minWidth: realm == 'ferrarioat_store' ? 90 : 110,
          flex: 1,
          filter: 'agTextColumnFilter',
          dataType: 'string'
        },
        {
          headerName: 'Labor Sale',
          filter: 'agNumberColumnFilter',
          field: 'lbrsale',
          width: realm == 'ferrarioat_store' ? 90 : 100,
          minWidth: realm == 'ferrarioat_store' ? 90 : 100,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'Sold Hours',
          filter: 'agNumberColumnFilter',
          field: 'lbrsoldhours',
          width: realm == 'ferrarioat_store' ? 70 : 80,
          minWidth: realm == 'ferrarioat_store' ? 70 : 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithout$,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'ELR',
          filter: 'agNumberColumnFilter',
          field: 'elr',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        }
      ],
      autoGroupColumnDef: { minWidth: 200 },
      rowData: [],
      headerHeight: 50,
      //   sideBar: 'columns',

      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right'
    };
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return 0;
    }
  };

  formatCellValueWithout$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return 0;
    }
  };

  onGridReady = params => {
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ columnApi: params.columnApi });
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.getAgGridData();
    this.getTopOpportunity();
  };
  onFirstDataRendered = params => {
    this.setState({ themeName: 'yellow-theme' });
    let opcodeArr = [];
    let topFiveOpcodeArr = [];
    this.state.rowData.map(item => {
      opcodeArr.push(item.lbropcode);
    });
    this.state.topFiveOpportunities.map(item => {
      topFiveOpcodeArr.push(item.lbropcode);
    });
    this.setState({
      topFiveOpcodeArr: topFiveOpcodeArr,
      opcodeArr: opcodeArr
    });
    opcodeArr.some((r, i) => {
      if (topFiveOpcodeArr.includes(r)) {
        params.api.getRowNode(i).setSelected(true);
      }
    });
  };
  getAgGridData() {
    this.getTotalOpportunity();
    this.getValidMakesLabor();
    getTrancheReporForLaborRateAnalysis(result => {
      this.setState({ isLoading: true });
      let DrilldownValues = [];
      let filteredArr = [];
      if (result.data.statelessDbdCpLaborRatesGetElrMovingTrancheReport.nodes) {
        this.setState({ isLoading: false });
        var resultArr =
          result.data.statelessDbdCpLaborRatesGetElrMovingTrancheReport.nodes;
        resultArr.map(obj => {
          if (obj.paytypegroup == 'C' && obj.lbrsale != 0) {
            filteredArr.push(obj);
          }
        });
        this.setState({
          rowData: filteredArr
        });
        DrilldownValues = getAggregateForTrancheReports(
          filteredArr,
          this.state.reportName
        );
        this.setDrillDownValuesToState(DrilldownValues);
      }
    });
  }

  setDrillDownValuesToState = Values => {
    this.setState({
      labrSale: Values.labrSale,
      soldHours: Values.soldHours,
      elr: Values.elr,
      startDate: Values.startDate,
      endDate: Values.endDate,
      AnalysisElr: Values.analysisElr
    });
  };
  getValidMakesLabor = () => {
    let validMakesArr = [];
    getValidMakes(result => {
      if (result.data.statelessCcPhysicalRwGetValidMake) {
        result.data.statelessCcPhysicalRwGetValidMake.map(item => {
          validMakesArr.push(titleCase(item));
        });
        this.setState({ validMakes: validMakesArr.join(', ') });
        // this.setState({validMakes: result.data.statelessCcPhysicalRwGetValidMake})
      }
    });
  };
  getTotalOpportunity = () => {
    let monthlyImpact = 0;
    let annualImpact = 0;
    getElrWarrantyPaytypes(result => {
      if (result.data.statelessDbdCpLaborRatesGetElrWarrantyPaytype.nodes) {
        var resultArr =
          result.data.statelessDbdCpLaborRatesGetElrWarrantyPaytype.nodes;
        resultArr.map(obj => {
          if (obj.monthlyImpact > 0) {
            monthlyImpact += Number(obj.monthlyImpact);
          }
          if (obj.annualImpact > 0) {
            annualImpact += Number(obj.annualImpact);
          }
        });
        this.setState({
          opportunityFlag: true,
          ElrWarrantyPaytypes: resultArr,
          MonthlyOpportunity: monthlyImpact,
          AnnualOpportunity: annualImpact
        });
      }
    });
  };

  getTopOpportunity = () => {
    getTopFiveOpportunities(result => {
      if (result.data.statelessDbdCpLaborRatesGetTopFiveOpcodeDetail.nodes) {
        this.setState({
          topFiveOpportunities:
            result.data.statelessDbdCpLaborRatesGetTopFiveOpcodeDetail.nodes
        });
      }
    });
  };

  getOpportunityDetails = resultArr => {
    this.props.history.push({
      pathname: '/TrancheReport/OpportunityDetailsReport',
      SelectedLocation: window.location.pathname,
      state: {
        resultSet: resultArr
      }
    });
  };

  renderBackButton = () => {
    {
      const historyLocation = this.state.previousLocation;
      this.props.history.push(historyLocation);
    }
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };

  handleSearchByRo = params => {
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        currentRate: this.state.currentRate,
        pageType: 'elrReport'
      }
    });
  };

  render() {
    const { classes } = this.props;
    let manufacturer = localStorage.getItem('storeManufacturer');
    const label =
      'Labor Rate Analysis \n Dealership: ' +
      localStorage.getItem('storeNames') +
      '\n Date Range: ' +
      (this.state.startDate
        ? moment(this.state.startDate).format('MM/DD/YY')
        : '') +
      ' to ' +
      (this.state.endDate
        ? moment(this.state.endDate).format('MM/DD/YY')
        : '') +
      '\n Labor > 0; Customer Pay; Exclude ELR < 0.00 ' +
      '\n Included Makes: ' +
      (manufacturer == 'GM' ? 'All Makes' : this.state.validMakes) +
      '\n' +
      '\n Current Warranty Rate: $' +
      this.state.currentRate +
      '\n Monthly Warranty Opportunity: $' +
      Number(this.state.MonthlyOpportunity)
        .toFixed(2)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
      '\n Analysis: $' +
      this.state.AnalysisElr +
      '\n Annual Warranty Opportunity: $' +
      Number(this.state.AnnualOpportunity)
        .toFixed(2)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
      '\n Increase/(Decrease): $' +
      Math.round(this.state.AnalysisElr - this.state.currentRate) +
      '\n Note: Your increase is not guaranteed. Due to discounts, opcode usage, etc, the actual increase received may differ by an average of 15%.' +
      '\n Note: An RO# may be displayed multiple times if it includes multiple qualifying opcodes.' +
      '\n Note: Annual Opportunity based off 12 months of Warranty Information.' +
      '\n Highlighted Services Indicate Top 5 Opportunities\n';

    const footer =
      this.state.reportName +
      ' \n ' +
      this.state.labrSale +
      '\n ' +
      this.state.soldHours +
      '\n ' +
      this.state.elr;
    var dateRange =
      (getLastSixMonths()[0]
        ? moment(getLastSixMonths()[0]).format('MM/DD/YY')
        : '') +
      ' to ' +
      (getLastSixMonths()[1]
        ? moment(getLastSixMonths()[1]).format('MM/DD/YY')
        : '');
    let Dealer = process.env.REACT_APP_DEALER;

    return (
      <div>
        <Paper square style={{ margin: 8, marginTop: '20px' }}>
          <Tabs
            value={this.state.tabSelection}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Link
              href="#"
              style={{ paddingTop: 12, paddingLeft: 16, cursor: 'pointer' }}
              onClick={this.renderBackButton}
              title="Go Back"
            >
              <ArrowBackIcon />
            </Link>
            <Tab
              style={{ textTransform: 'none', paddingRight: 55 }}
              label={
                <div style={{ lineHeight: 1.25, width: '100%' }}>
                  <span
                    style={{
                      color: Dealer == 'Armatus' ? '#003d6b' : '#cc0052',
                      fontWeight: 'bold'
                    }}
                  >
                    {label.split('\n')[0]}
                  </span>
                  <br />
                  <span
                    style={{
                      fontSize: 12,
                      color: '#000000',
                      fontWeight: 'bold'
                    }}
                  >
                    {label.split('\n')[1]}
                  </span>
                  <br />
                  <span style={{ fontSize: 11, color: '#000000' }}>
                    {label.split('\n')[2]}
                  </span>
                  <br />
                  <span style={{ fontSize: 11, color: '#000000' }}>
                    {label.split('\n')[3]}
                  </span>
                  <br />
                  <span style={{ fontSize: 11, color: '#000000' }}>
                    {label.split('\n')[4]}
                  </span>
                  <br />
                  <br />
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'left' }}
                  >
                    {label.split('\n')[6]}
                  </span>
                  {this.state.opportunityFlag ? (
                    <span>
                      <Tooltip
                        title="View Details"
                        style={{
                          float: 'right',
                          paddingTop: 0,
                          color: Dealer == 'Armatus' ? '#003d6b' : '#C2185B',
                          fontWeight: 'bold',
                          margin: -6,
                          transform: 'scale(0.8)'
                        }}
                        onClick={() =>
                          this.getOpportunityDetails(
                            this.state.ElrWarrantyPaytypes
                          )
                        }
                      >
                        <IconButton size="medium">
                          <ExitToAppIcon />
                        </IconButton>
                      </Tooltip>
                    </span>
                  ) : (
                    ''
                  )}
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'right' }}
                  >
                    {label.split('\n')[7]}
                  </span>
                  <br />
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'left' }}
                  >
                    {label.split('\n')[8]}
                  </span>
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'right' }}
                  >
                    {label.split('\n')[9]}
                  </span>
                  <br />
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'left' }}
                  >
                    {label.split('\n')[10]}
                  </span>
                  <br />
                  <span
                    style={{
                      fontSize: 12,
                      color: '#000000',
                      float: 'left',
                      paddingRight: 23
                    }}
                  >
                    {label.split('\n')[11]}
                  </span>
                  <br />
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'left' }}
                  >
                    {label.split('\n')[12]}
                  </span>
                  <br />
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'left' }}
                  >
                    {label.split('\n')[13]}
                  </span>
                  <br />
                  <span
                    style={{ fontSize: 12, color: '#000000', float: 'left' }}
                  >
                    {label.split('\n')[14]}
                  </span>
                </div>
              }
              value="one"
            />
          </Tabs>
        </Paper>
        <Paper square style={{ margin: 8, marginTop: '20px' }}>
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
            TabIndicatorProps={{ style: { display: 'none' } }}
          >
            <Tab
              style={{ textTransform: 'none' }}
              label={<div> {this.state.reportName}</div>}
              value="one"
              className={
                this.state.tabSelection == 'one' ? classes.tabSelected : null
              }
            />
            {/* <Tab
              style={{ textTransform: 'none' }}
              label={
                <div>
                  {' '}
                  {'Included Op codes '}
                  <span style={{ fontSize: 12, whiteSpace: 'pre' }}>
                    {'(Date Range: ' + dateRange + ')'}
                  </span>
                </div>
              }
              value="one"
              className={
                this.state.tabSelection == 'one' ? classes.tabSelected : null
              }
            /> */}
            <Tab
              style={{ textTransform: 'none' }}
              label={
                <div>
                  {' '}
                  {'Included Op codes '}
                  <span style={{ fontSize: 12, whiteSpace: 'pre' }}>
                    {'(Date Range: ' + dateRange + ')'}
                  </span>
                </div>
              }
              value="two"
              className={
                this.state.tabSelection == 'two' ? classes.tabSelected : null
              }
            />

            <Tab
              style={{ textTransform: 'none' }}
              label={
                <div>
                  {' '}
                  {'Excluded Op Codes '}
                  <span style={{ fontSize: 12, whiteSpace: 'pre' }}>
                    {'(Date Range: ' + dateRange + ')'}
                  </span>
                </div>
              }
              value="three"
              className={
                this.state.tabSelection == 'three' ? classes.tabSelected : null
              }
            />
          </Tabs>
        </Paper>
        {this.state.tabSelection == 'one' &&
        (this.state.MonthlyOpportunity ||
          JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
            '232297966') ? (
          <PDFExportPanel
            gridApi={this.state.gridApi}
            columnApi={this.state.columnApi}
            label={label}
            footer={footer}
            fileName={this.state.reportName}
            topFiveOpcodeArr={this.state.topFiveOpcodeArr}
            opcodeArr={this.state.opcodeArr}
          />
        ) : (
          ''
        )}
        {this.state.tabSelection == 'two' ? <IncludedOpcodesReport /> : null}
        {this.state.tabSelection == 'three' ? <ExcludedOpcodesReport /> : null}

        {this.state.isLoading && (
          <div
            style={{
              display: this.state.tabSelection != 'one' ? 'none' : 'block'
            }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div id="grid-theme-wrapper" className={this.state.themeName}>
          <div
            id="data-tab"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 410 + 'px',
              width: '98.8%',
              margin: 8,
              display:
                this.state.isLoading == true ||
                this.state.tabSelection == 'two' ||
                this.state.tabSelection == 'three'
                  ? 'none'
                  : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              enableRangeSelection={true}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              animateRows={true}
              enableCharts={true}
              modules={AllModules}
              sideBar={this.state.sideBar}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              rowData={this.state.rowData}
              headerHeight={this.state.headerHeight}
              pinnedBottomRowData={createDataForTrancheReports(this.state)}
              frameworkComponents={{
                customPinnedRowRenderer: CustomPinnedRowRenderer
              }}
              tooltipShowDelay={0}
              rowSelection="multiple"
              onFirstDataRendered={this.onFirstDataRendered}
              suppressContextMenu={true}
            />
          </div>
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
});

export default withKeycloak(withStyles(styles)(LaborRateAnalysis));
