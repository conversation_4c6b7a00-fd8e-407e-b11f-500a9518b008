import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import LaborRateAnalysis from './LaborRateAnalysis';
import { useHistory } from 'react-router';
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function TranchReport({ route }) {
  const classes = useStyles();
  const history = useHistory();
  return (
    <Page className={classes.root} title="Analysis">
      <LaborRateAnalysis
        route={route}
        history={history}
      />
    </Page>
  );
}

export default TranchReport;
