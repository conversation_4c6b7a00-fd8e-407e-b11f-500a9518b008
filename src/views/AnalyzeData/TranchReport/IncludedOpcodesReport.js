import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import { Typography, LinearProgress, Box } from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { withStyles } from '@material-ui/styles';
import PDFExportPanel from 'src/pdfExport/PDFExportPanel.js';
import CustomPinnedRowRenderer from 'src/customPinnedRowRenderer.jsx';
import { getTrancheReportForIncludedOpcodes } from 'src/utils/hasuraServices';
import { getAggregateForTrancheReports } from 'src/components/DrillDownCalculations';
import { createDataForTrancheReports, getLastSixMonths } from 'src/utils/Utils';

class IncludedOpcodesReport extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      storeId: storeId,
      showCharts: false,
      tabSelection: 'two',
      isLoading: true,
      rawGridApi: {},
      gridApi: {},
      columnApi: {},
      AnalysisElr: 0,
      monthlyOpportunity: 0,
      laborsale: 0,
      frh: 0,
      reveImpact: 0,
      monthlyImpact: 0,
      annualImpact: 0,
      elr: 0,
      themeName: '',
      reportName: 'Included Op Codes',
      modules: [
        ClientSideRowModelModule,
        RowGroupingModule,
        MenuModule,
        ColumnsToolPanelModule,
        SetFilterModule
      ],
      columnDefs: [
        {
          headerName: 'Opcode',
          filter: 'agTextColumnFilter',
          field: 'lbropcode',
          width: 120,
          minWidth: 120,
          flex: 1,
          chartDataType: 'category',
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 15, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          maxWidth: 250,
          resizable: true
        },
        {
          headerName: 'Op Category',
          filter: 'agTextColumnFilter',
          field: 'opcategory',
          width: 110,
          filter: 'agTextColumnFilter',
          dataType: 'string'
        },
        {
          headerName: 'Labor Sale',
          filter: 'agNumberColumnFilter',
          field: 'laborsale',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'Flat Rate Hours',
          filter: 'agNumberColumnFilter',
          field: 'frh',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithout$,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'ELR',
          filter: 'agNumberColumnFilter',
          field: 'elr',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'Analysis ELR',
          filter: 'agNumberColumnFilter',
          field: 'analysisElr',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueELR,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'Variance ELR',
          filter: 'agNumberColumnFilter',
          field: 'varianceElr',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'Monthly Impact',
          filter: 'agNumberColumnFilter',
          field: 'monthlyImpact',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        },
        {
          headerName: 'Annual Impact',
          filter: 'agNumberColumnFilter',
          field: 'annualImpact',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          pinnedRowCellRenderer: 'customPinnedRowRenderer',
          pinnedRowCellRendererParams: {
            style: { fontSize: 12, fontWeight: 'bold' }
          }
        }
      ],
      autoGroupColumnDef: { minWidth: 200 },
      rowData: [],
      headerHeight: 50,
      //sideBar: 'columns',

      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right'
    };
  };

  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return 0;
    }
  };

  formatCellValueELR = params => {
    if (params.value != null && params.value != 0) {
      return '$' + Math.round(params.value);
    } else {
      return 0;
    }
  };
  formatCellValueWithout$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return 0;
    }
  };

  onGridReady = params => {
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ columnApi: params.columnApi });
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    // this.gridApi.sizeColumnsToFit()
    this.getAgGridData();
  };

  onFirstDataRendered = params => {
    this.setState({ themeName: 'yellow-theme' });
    for (var i = 0; i < 5; i++) {
      if (params.api.getRowNode(i)) {
        params.api.getRowNode(i).setSelected(true);
      }
    }
  };
  getAgGridData() {
    let DrilldownValues = [];
    getTrancheReportForIncludedOpcodes(result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessDbdCpLaborRatesGetTrancheReportIncludedOpCodes
          .nodes
      ) {
        this.setState({
          rowData:
            result.data.statelessDbdCpLaborRatesGetTrancheReportIncludedOpCodes
              .nodes
        });
        DrilldownValues = getAggregateForTrancheReports(
          result.data.statelessDbdCpLaborRatesGetTrancheReportIncludedOpCodes
            .nodes,
          this.state.reportName
        );
        this.setDrillDownValuesToState(DrilldownValues);
      }
    });
  }

  setDrillDownValuesToState = Values => {
    this.setState({
      laborsale: Values.laborsale,
      frh: Values.frh,
      elr: Values.elr,
      monthlyImpact: Values.monthlyImpact,
      annualImpact: Values.annualImpact
    });
  };

  render() {
    var dateRange = getLastSixMonths()[0] + ' to ' + getLastSixMonths()[1];
    const { classes } = this.props;
    const label = this.state.reportName + ' (Date Range: ' + dateRange + ')';
    const footer =
      this.state.reportName +
      ' \n' +
      this.state.laborsale +
      '\n ' +
      this.state.frh +
      '\n ' +
      this.state.elr +
      '\n ' +
      this.state.monthlyImpact +
      '\n ' +
      this.state.annualImpact;
    return (
      <div>
        {this.state.rowData.length > 0 ? (
          <PDFExportPanel
            gridApi={this.state.gridApi}
            columnApi={this.state.columnApi}
            label={label}
            footer={footer}
            fileName={this.state.reportName}
          />
        ) : (
          ''
        )}
        {this.state.isLoading && (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div id="grid-theme-wrapper" className={this.state.themeName}>
          <div
            id="data-tab"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 410 + 'px',
              width: '98.8%',
              margin: 8,
              display: this.state.isLoading == true ? 'none' : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              enableRangeSelection={true}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              animateRows={true}
              enableCharts={true}
              modules={AllModules}
              sideBar={this.state.sideBar}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              rowData={this.state.rowData}
              headerHeight={this.state.headerHeight}
              pinnedBottomRowData={createDataForTrancheReports(this.state)}
              frameworkComponents={{
                customPinnedRowRenderer: CustomPinnedRowRenderer
              }}
              tooltipShowDelay={0}
              rowSelection="multiple"
              onFirstDataRendered={this.onFirstDataRendered}
              suppressContextMenu={true}
            />
          </div>
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  }
});

export default withStyles(styles)(IncludedOpcodesReport);
