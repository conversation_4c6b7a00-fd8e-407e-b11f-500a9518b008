import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import {
  getChartType,
  getLineChartType,
  GetyAxisRange
} from 'src/components/ViewGraphDetailsAction';
import { applyPartialMonthFilterLineCharts } from '../../utils/Utils';
import { Paper } from '@material-ui/core';
import { useHistory } from 'react-router';
import ReactApexChart from 'react-apexcharts';
import moment from 'moment';
import { makeStyles } from '@material-ui/core/styles';
import { applyPartialMonthFilterWorkMix } from 'src/utils/Utils';
import getYearLegend from 'src/utils/Utils';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';
import { useSelector, useDispatch } from 'react-redux';

const useStyles = makeStyles({
  formControl: {
    padding: 4,
    minWidth: 250
  },
  gridContainer: {
    padding: '8px 0px'
  },
  container: {
    cursor: 'pointer',
    borderRadius: 5,
    border: '1px solid #003d6b'
  }
});
const CPPartsROCount = ({
  filterCharts,
  parentCallback,
  isPartsCharts,
  realm,
  partialMonth,
  userhistory
}) => {
  const history = useHistory();
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [hasuraData, setHasuraData] = useState([]);
  const [loader, setLoader] = useState(false);

  let data = [];

  useEffect(() => {
    getChartsDataFromViews(session.serviceAdvisor, 1143, callback => {
      if (callback) {
        setHasuraData(JSON.parse(callback[0].jsonData));
        // console.log('ccc===customData', JSON.parse(callback[0].jsonData));

        setLoader(false);
      }
    });
  }, [session.serviceAdvisor]);

  const options = {
    chart: {
      id: 'chart' + 925,
      events: {
        dataPointSelection: function(event, chartContext, config) {
          var xAxis =
            config.w.globals.seriesLog[config.seriesIndex][
              config.dataPointIndex
            ];
          var month = moment()
            .month(config.w.globals.categoryLabels[config.dataPointIndex])
            .format('MM');
          var year = config.w.globals.seriesNames[config.seriesIndex];
          let yAxis = year + '-' + month;
          var isFrom =
            userhistory && userhistory.location && userhistory.location.isFrom
              ? userhistory.location.isFrom
              : '';
          var prevPaths =
            userhistory && userhistory.location && userhistory.location.prevPath
              ? userhistory.location.prevPath
              : '';
          history.push({
            pathname: '/AnalyzeData',
            prevPath: window.location.pathname,
            state: {
              chartId: 925,
              x: xAxis,
              y: yAxis,
              drillDown: 1,
              category: 0,
              chartName: 'CP RO Count',
              title: 'GP-CPRO',
              isParts: isPartsCharts,
              isFrom: isFrom,
              prevPaths: prevPaths
            }
          });
          let data = {
            type: 'workmixdrilldown',
            chartId: 925,
            x: xAxis,
            y: yAxis,
            drillDown: 1,
            category: 0,
            chartName: 'CP RO Count',
            title: 'GP-CPRO',
            isParts: isPartsCharts,
            isFrom: isFrom
          };
          parentCallback(data);
        }
      },
      fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
      toolbar: {
        show: false,
        offsetX: 5,
        offsetY: 0,
        tools: {
          download: true,
          selection: true,
          zoomin: true,
          zoomout: true,
          pan: true
        },
        autoSelected: 'zoom'
      },
      type: 'line',
      zoom: {
        enabled: false
      }
    },
    // colors: ['#3366CC', '#DC3912', '#109618'],
    colors: ['#109618', '#DC3912', '#3366CC'],
    dataLabels: {
      enabled: false
    },
    markers: {
      size: 4,
      strokeWidth: 1,
      strokeOpacity: 0.7
    },
    stroke: {
      curve: 'straight',
      width: 2.5
    },
    title: {
      text: 'CP RO Count',
      align: 'left'
    },
    grid: {
      row: {
        colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
        opacity: 0.5
      }
    },
    theme: {
      palette: 'palette8' // upto palette10
    },
    yaxis: {
      labels: {
        formatter: function(value) {
          return value
            ? value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            : value;
        }
      }
    },
    xaxis: {
      categories:
        hasuraData &&
        hasuraData.length > 0 &&
        hasuraData[0].labels &&
        hasuraData[0].labels.map(
          c =>
            new Date(new Date(c).toISOString().replace('Z', ''))
              .toString()
              .split(' ')[1]
        ),
      tooltip: {
        enabled: false
      }
    },
    tooltip: {
      shared: false,
      intersect: true
    }
  };
  if (hasuraData && hasuraData.length > 0) {
    data = [
      {
        name: getYearLegend('present', realm),
        data:
          hasuraData[0].datasets && hasuraData[0].datasets[0].data
            ? hasuraData[0].datasets[0].data.map(e => e)
            : [],
        labels: hasuraData[0].labels && hasuraData[0].labels.map(e => e)
      },
      {
        name: getYearLegend('past', realm),
        data:
          hasuraData[0].datasets && hasuraData[0].datasets[1].data
            ? hasuraData[0].datasets[1].data.map(e => e)
            : [],
        labels: hasuraData[0].labels && hasuraData[0].labels.map(e => e)
      },
      {
        name: getYearLegend('previous', realm),
        data:
          hasuraData[0].datasets && hasuraData[0].datasets[2].data
            ? hasuraData[0].datasets[2].data.map(e => e)
            : [],
        labels: hasuraData[0].labels && hasuraData[0].labels.map(e => e)
      }
    ];
  }

  if (partialMonth == false && data && data.length > 0) {
    applyPartialMonthFilterWorkMix(data);
  }

  return (
    <Paper square className={classes.container}>
      <ReactApexChart
        options={options}
        series={data}
        type="line"
        height={250}
      />
    </Paper>
  );
};

CPPartsROCount.propTypes = {
  className: PropTypes.string,
  chartId: PropTypes.number,
  yAxisRanges: PropTypes.array,
  filterCharts: PropTypes.func
};

export default CPPartsROCount;
