import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { Paper } from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';

import BarChartIcon from '@material-ui/icons/BarChart';
import CompareArrowsIcon from '@material-ui/icons/CompareArrows';
import { withStyles } from '@material-ui/styles';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import LaborDiscountSummary from '../AnalyzeData/LaborDiscountSummary';
import PartsDiscountSummary from '../AnalyzeData/PartsDiscountSummary';
import { getComparisonMonths } from 'src/utils/Utils';
import moment from 'moment';
class DiscountSummary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      resetReport: this.props.resetReport,
      tabSelection: props.history.location.state
        ? props.history.location.state.innerTab
          ? props.history.location.state.innerTab
          : 'one'
        : 'one',
      month_year: props.history.location.state
        ? props.history.location.state.discountMonthYear
        : '',
      serviceAdvisor:
        props.history.location.state &&
        props.history.location.state.discountAdvisorName != ''
          ? props.history.location.state.discountAdvisorName
          : props.history.location.state &&
            props.history.location.state.discountAdvisor
          ? props.history.location.state.discountAdvisor
          : ''
    };
  }

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
    window.sortState = {};
    window.filterState = {};
    window.sortStateDisLbr = {};
    window.filterStateDisLbr = {};
    window.sortStateDisPrts = {};
    window.filterStateDisPrts = {};
  };
  callbackFunction = params => {
    if (params) {
      if (params.type == 'laborDiscount') {
        this.setState({
          month_year: params.month_year,
          serviceAdvisor: params.serviceAdvisor,
          type: params.type
        });
      } else if (params.type == 'partsDiscount') {
        this.setState({
          month_year: params.month_year,
          serviceAdvisor: params.serviceAdvisor,
          type: params.type
        });
      } else if (
        params.type == 'labordiscount_dilldown' ||
        params.type == 'partsdiscount_dilldown'
      ) {
        this.props.history.push({
          pathname: '/AnalyzeData',
          prevPath: window.location.pathname,
          state: {
            month_year: params.month_year,
            discountId: params.discount_id,
            serviceAdvisor: 'All',
            drillDown: params.type == 'labordiscount_dilldown' ? 34 : 35,
            chartName: 'Discount Summary',
            category: 0,
            title: params.type == 'labordiscount_dilldown' ? 'DSL' : 'DSP',
            isParts: params.isPartsCharts,
            tabSelection: 'one',
            innerTab: params.type == 'labordiscount_dilldown' ? 'one' : 'two'
          }
        });
      } else if (
        params.type == 'labordiscount_dilldown_sa' ||
        params.type == 'partsdiscount_dilldown_sa'
      ) {
        this.props.history.push({
          pathname: '/AnalyzeData',
          prevPath: window.location.pathname,
          state: {
            month_year: params.month_year,
            discountId: params.discount_id,
            serviceAdvisor: params.serviceAdvisor,
            drillDown: params.type == 'partsdiscount_dilldown_sa' ? 35 : 34,
            chartName: 'Discount Summary',
            category: 0,
            title: params.type == 'labordiscount_dilldown_sa' ? 'DSL' : 'DSP',
            isParts: params.isPartsCharts,
            tabSelection: 'one',
            innerTab:
              params.type == 'labordiscount_dilldown_sa' ? 'one' : 'two',
            advisorName: params.advisorName
          }
        });
      }
    }
  };
  render() {
    const { classes } = this.props;

    return (
      <div>
        <Paper square style={{ backgroundColor: '#FFF', marginTop: '20px' }}>
          <Tabs
            variant="standard"
            // scrollButtons="auto"
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            indicatorColor="secondary"
            textColor="secondary"
            TabIndicatorProps={{ style: { display: 'none' } }}
          >
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>Labor</div>}
              value="one"
              className={
                this.state.tabSelection == 'one' ? classes.tabSelected : null
              }
            />
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>Parts</div>}
              value="two"
              className={
                this.state.tabSelection == 'two' ? classes.tabSelected : null
              }
            />
          </Tabs>
        </Paper>

        {this.state.tabSelection == 'one' ? (
          <LaborDiscountSummary
            monthYear={
              this.state.month_year
                ? this.state.month_year
                : getComparisonMonths()[1]
            }
            serviceAdvisor={
              this.state.serviceAdvisor ? this.state.serviceAdvisor : 'All'
            }
            parentCallback={this.callbackFunction}
            setResetDashboard={this.props.setResetDashboard}
            resetReport={this.props.resetReport}
            session={this.props.session}
            history={this.props.history}
          />
        ) : null}

        {this.state.tabSelection == 'two' ? (
          <PartsDiscountSummary
            monthYear={
              this.state.month_year
                ? this.state.month_year
                : getComparisonMonths()[1]
            }
            serviceAdvisor={
              this.state.serviceAdvisor ? this.state.serviceAdvisor : 'All'
            }
            parentCallback={this.callbackFunction}
            setResetDashboard={this.props.setResetDashboard}
            resetReport={this.props.resetReport}
            session={this.props.session}
            history={this.props.history}
          />
        ) : null}
      </div>
    );
  }
}

const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
});

export default withStyles(styles)(DiscountSummary);
