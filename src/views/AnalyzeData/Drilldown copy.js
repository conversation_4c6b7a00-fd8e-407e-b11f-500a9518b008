import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Box,
  Button,
  Grid,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import ReactHtmlParser from 'react-html-parser';
import 'src/grid.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import moment from 'moment';
import React from 'react';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { getAgGridChartOptions, getComparisonMonths } from 'src/utils/Utils';
import {
  getChartName,
  processCells,
  isValidDate,
  isValidMonthYear
} from 'src/components/ViewGraphDetailsAction';
import {
  getAverageHoursSoldPerTechData,
  getDiscountedSalePercentage,
  getDiscountedTotalPerTotalDiscountedCPROs,
  getDiscountsPerCPROs,
  getDiscountsPerTotalDiscountedCPROs,
  getDrillDownDataForAddOns,
  getDrillDownDataForDiscount,
  getDrillDownDataForDiscountedRo,
  getDrillDownDataForDiscountJobCount,
  getDrillDownDataForDiscountJobCountOpcategory,
  getDrillDownDataForDiscountSummary,
  getDrillDownDataForDiscountSummaryParts,
  getDrillDownDataForGrossProfit,
  getDrillDownDataForLaborDiscount,
  getDrillDownDataForLaborItemization,
  getDrillDownDataForMovingElr,
  getDrillDownDataForMovingPartsMarkup,
  getDrillDownDataForPartsDiscount,
  getDrillDownDataForPartsItemization,
  getDrillDownDataForPartsMarkup,
  getDrillDownDataForSoldHours,
  getDrillDownDataForTotalRevenue,
  getDrillDownDatForSingleJob,
  getDrillDownDiscountByRO,
  getDrillDownDiscountSummaryLabor,
  getDrillDownDiscountSummaryParts,
  getDrillDownForAverageHoursSoldPerTech,
  getLaborPartsDiscountByMonth,
  getROCountForTotalRevenue,
  getShopSuppliesDrilldown,
  getDrillDownDataForTotalCustomerPayRevenue,
  getDrillDownDataForMPIPenetration,
  getDrillDownDataForPartsMarkupCharts,
  getDrillDownDataForAllRevenue
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Link from '@material-ui/core/Link';
import {
  distinctROCount,
  myCustomSumAggregate,
  myCustomSumAggregateRevisedTotals
} from 'src/components/DrillDownCalculations';
import DrilldownReturnRate from './DrilldownReturnRate';
import ExportIcon from '@material-ui/icons/GetApp';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import { withKeycloak } from '@react-keycloak/web';
import Select from '@material-ui/core/Select';
import MenuItem from '@material-ui/core/MenuItem';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { RowGroupingModule } from '@ag-grid-enterprise/row-grouping';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { FiltersToolPanelModule } from '@ag-grid-enterprise/filter-tool-panel';
import { SetFilterModule } from '@ag-grid-enterprise/set-filter';
import { MenuModule } from '@ag-grid-enterprise/menu';

import CheckboxRenderer from 'src/components/CheckboxRenderer';
import { i } from 'react-dom-factories';
import { connect } from 'react-redux';
import { SET_DRILL_DOWN_COUNT } from 'src/actions';
// import {
//   setDrillDownCount
// } from 'src/actions';

var lodash = require('lodash');

class Drilldown extends React.Component {
  componentDidMount() {
    this.props.setDrillDownCount(false);
  }
  componentDidUpdate(prevProps) {
    const dateFormat = item => moment(item).format('YYYY-MM');

    var initialQueryMonth =
      typeof this.props.params != 'undefined' &&
      typeof this.props.params.y != 'undefined'
        ? dateFormat(this.props.params.y)
        : typeof this.props.history.location.state != 'undefined' &&
          this.props.history.location.state != null
        ? dateFormat(this.props.history.location.state.y)
        : '';

    var drillDownServiceAdvisor = JSON.stringify(
      this.props.session.serviceAdvisor
    );
    var previousServiceAdvisor = JSON.stringify(
      prevProps.session.serviceAdvisor
    );

    var isContain = lodash.includes(
      previousServiceAdvisor,
      drillDownServiceAdvisor
    );
    if (isContain == false) {
      this.setState({ isLoading: true });
      this.getAgGridData(initialQueryMonth, '', drillDownServiceAdvisor);
    }
  }

  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var isParts =
      typeof this.props.params != 'undefined'
        ? typeof this.props.params.isParts != 'undefined'
          ? this.props.params.isParts
          : false
        : this.props.history.location.state.isParts;

    var initialQueryMonth =
      typeof this.props.params != 'undefined' &&
      typeof this.props.params.y != 'undefined'
        ? dateFormat(this.props.params.y)
        : dateFormat(this.props.history.location.state.y);
    //var date = new Date(this.props.history.location.state.y);
    var serviceAdvisor =
      typeof this.props.params != 'undefined'
        ? this.props.params.x
        : this.props.history.location.state.x;
    var drillDownServiceAdvisor = JSON.stringify(
      this.props.session.serviceAdvisor
    );
    var category = this.props.params
      ? this.props.params.category
      : this.props.history.location.state.category;
    var chartName =
      this.props.params && this.props.params.chartName != undefined
        ? this.props.params.chartName
        : this.props.history.location.state &&
          this.props.history.location.state.chartName
        ? this.props.history.location.state.chartName
        : getChartName(
            this.props.history.location.search.split('?chartId=').pop()
          );
    var drillDown = this.props.params
      ? this.props.params.drillDown
      : this.props.history.location.state.drillDown;

    var chartId = this.props.params
      ? this.props.params.chartId
      : this.props.history.location.state.chartId;
    var title = this.props.params
      ? this.props.params.title
      : this.props.history.location.state.title;
    var label = this.props.history.location.state
      ? this.props.history.location.state.label
      : '';
    var titleCategory = this.props.history.location.state
      ? this.props.history.location.state.titleCategory
      : '';
    var payType = this.props.history.location.state
      ? this.props.history.location.state.payType
        ? this.props.history.location.state.payType
        : 'C'
      : 'C';

    var selectedpayTypeFilter1 = this.props.history.location.state
      ? this.props.history.location.state.payType
        ? this.props.history.location.state.payType.split(',')[0]
          ? this.props.history.location.state.payType.split(',')[0]
          : this.props.history.location.state.payType
        : 'C'
      : 'C';
    var selectedpayTypeFilter2 = this.props.history.location.state
      ? this.props.history.location.state.payType
        ? this.props.history.location.state.payType.split(',')[1]
          ? this.props.history.location.state.payType.split(',')[1]
          : ''
        : 'C'
      : 'C';
    var selectedpayTypeFilter3 = this.props.history.location.state
      ? this.props.history.location.state.payType
        ? this.props.history.location.state.payType.split(',')[2]
          ? this.props.history.location.state.payType.split(',')[2]
          : ''
        : 'C'
      : 'C';
    var selectedOpCategoryFilter1 = this.props.history.location.state
      ? this.props.history.location.state.opCategory
        ? this.props.history.location.state.opCategory.split(',')[0]
          ? this.props.history.location.state.opCategory.split(',')[0]
          : this.props.history.location.state.opCategory
        : ''
      : '';
    var selectedOpCategoryFilter2 = this.props.history.location.state
      ? this.props.history.location.state.opCategory
        ? this.props.history.location.state.opCategory.split(',')[1]
          ? this.props.history.location.state.opCategory.split(',')[1]
          : ''
        : ''
      : '';
    var selectedOpCategoryFilter3 = this.props.history.location.state
      ? this.props.history.location.state.opCategory
        ? this.props.history.location.state.opCategory.split(',')[2]
          ? this.props.history.location.state.opCategory.split(',')[2]
          : ''
        : ''
      : '';
    var isFromMetrices = this.props.history.location.state
      ? this.props.history.location.state.isFromMetrices
        ? this.props.history.location.state.isFromMetrices
        : false
      : false;
    var discountMonth = this.props.history.location.state
      ? this.props.history.location.state.month_year
        ? this.props.history.location.state.month_year
        : this.props.history.location.state.discountMonth
        ? this.props.history.location.state.discountMonth
        : getComparisonMonths()[0]
      : getComparisonMonths()[0];
    var discountId = this.props.history.location.state
      ? this.props.history.location.state.discountId
      : '';
    var discountServiceAdvisor = this.props.history.location.state
      ? this.props.history.location.state.serviceAdvisor
        ? this.props.history.location.state.serviceAdvisor
        : this.props.history.location.state.discountServiceAdvisor
        ? this.props.history.location.state.discountServiceAdvisor
        : 'All'
      : 'All';

    var location = '/SearchByRO';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    var realm = this.props.keycloak.realm;
    var isFrom = this.props.history.location.state
      ? this.props.history.location.state.isFrom
        ? this.props.history.location.state.isFrom
        : ''
      : '';
    var tabSelection = this.props.history.location.state
      ? this.props.history.location.state.tabSelection
      : '';
    localStorage.setItem('chartName', chartName);
    localStorage.removeItem('storeIdSelected');
    console.log('drillDowns', drillDown, chartId, category);
    this.state = {
      previousLocation: this.props.history.location.prevPath,
      isFrom: isFrom,
      showCharts: false,
      stateChanged: false,
      columnFilteredArr: [],
      tabSelection: 'one',
      drillDownTabSelection: tabSelection,
      queryMonth: initialQueryMonth,
      serviceAdvisor: serviceAdvisor,
      selectedGroups: [],
      selectedFilters: [],
      isLoading: true,
      isSumChanged: false,
      isSRowDataChanged: false,
      selectedRowData: [],
      isChecked: false,
      category: category,
      chartName: chartName,
      chartId: chartId,
      autoGroupColumnDef: [],
      drillDown: drillDown,
      drillDownServiceAdvisor: drillDownServiceAdvisor,
      rawGridApi: {},
      gridApi: {},
      selectedFilterValue: payType,
      selectedOpCategoryFilter1: selectedOpCategoryFilter1,
      selectedOpCategoryFilter2: selectedOpCategoryFilter2,
      selectedOpCategoryFilter3: selectedOpCategoryFilter3,
      selectedpayTypeFilter1: selectedpayTypeFilter1,
      selectedpayTypeFilter2: selectedpayTypeFilter2,
      selectedpayTypeFilter3: selectedpayTypeFilter3,
      title: title,
      titleCategory: titleCategory,
      label: label,
      isParts: isParts,
      isFromMetrices: isFromMetrices,
      height: window.innerHeight,
      discountMonth: discountMonth,
      discountId: discountId,
      discountServiceAdvisor: discountServiceAdvisor,
      storeSelected: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
      columnDefsRevenue: [
        {
          headerName: 'RO',
          unSortIcon: true,
          suppressMenu: true,
          // filter: 'agTextColumnFilter',
          field: 'ronumber',
          width:
            realm != 'haleyag'
              ? drillDown == 7 ||
                drillDown == 0 ||
                drillDown == 5 ||
                (drillDown == 2 && category == 9)
                ? 95
                : 85
              : 110,
          minWidth:
            realm != 'haleyag'
              ? drillDown == 7 ||
                drillDown == 0 ||
                drillDown == 5 ||
                (drillDown == 2 && category == 9)
                ? 95
                : 85
              : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          // hide: drillDown == 33 ? true : false,
          onCellClicked: this.handleSearchByRo,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          hide: true,
          width: drillDown == 7 || drillDown == 5 ? 90 : 100,
          minWidth: drillDown == 7 || drillDown == 5 ? 90 : 100,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agDateColumnFilter',
          // filterParams: {
          //   browserDatePicker: true,
          //   comparator: function(filterLocalDateAtMidnight, cellValue) {
          //     var dateAsString = moment(cellValue).format('YYYY-MM-DD');
          //     if (dateAsString == null) return -1;

          //     var dateParts = dateAsString.split('-');
          //     var cellDate = new Date(
          //       Number(dateParts[0]),
          //       Number(dateParts[1]) - 1,
          //       Number(dateParts[2])
          //     );

          //     if (filterLocalDateAtMidnight.getTime() == cellDate.getTime()) {
          //       return 0;
          //     }

          //     if (cellDate < filterLocalDateAtMidnight) {
          //       return -1;
          //     }

          //     if (cellDate > filterLocalDateAtMidnight) {
          //       return 1;
          //     }
          //   }
          // }
        },

        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: drillDown == 2 || drillDown == 7 ? 145 : 145,
          minWidth: drillDown == 2 || drillDown == 7 ? 145 : 145,
          flex: 1,
          chartDataType: 'string',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // }
        },
        /*   {
          headerName: 'Tech No.',
          filter: 'agNumberColumnFilter',
          field: 'lbrtechno',
          width: 72,
          chartDataType: 'series',
          hide:
            (drillDown == 7 && category == 0) ||
            (drillDown == 37 && category == 0)
              ? false
              : true,
          cellStyle: this.cellStyles
        },*/
        {
          headerName: 'Labor Code',
          // filter: 'agTextColumnFilter',
          field: 'lbrlinecode',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
          // hide:
          //   (drillDown == 1 ||
          //     drillDown == 3 ||
          //     drillDown == 4 ||
          //     drillDown == 5 ||
          //     drillDown == 47) &&
          //   (category == 0 || category == 2 || category == 11)
          //     ? false
          //     : true
        },
        {
          headerName: 'Pay Type',
          // filter: 'agTextColumnFilter',
          field: 'paytype',
          width: realm == 'ferrarioat_store' ? 100 : 85,
          minWidth: realm == 'ferrarioat_store' ? 100 : 85,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: drillDown == 2 || drillDown == 7 ? 93 : 90,
          minWidth: drillDown == 2 || drillDown == 7 ? 93 : 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          // filter: 'agTextColumnFilter',
          field: 'lbropcode',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 100,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 100,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          // filter: 'agTextColumnFilter',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Seq No.',
          // filter: 'agNumberColumnFilter',
          field: 'lbrsequenceno',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Tech Hours',
          field: 'lbrtechhours',
          // filter: 'agNumberColumnFilter',
          width: 84,
          minWidth: 84,
          flex: 1,
          chartDataType: 'series',
          hide: drillDown == 7 && category == 0 ? false : true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR',
          // filter: 'agNumberColumnFilter',
          field: 'elr',
          width: drillDown == 5 ? 72 : 68,
          minWidth: drillDown == 5 ? 72 : 68,
          flex: 1,
          // flex:(chartId == 946 || chartId == 1127|| chartId == 1098|| drillDown == 38) ? 1 : '',
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          suppressMenu: true,
          cellClass: 'twoDecimalPlacesWith$',
          suppressToolPanel:
            drillDown == 38 ||
            chartId == 946 ||
            chartId == 937 ||
            chartId == 988 ||
            chartId == 987 ||
            chartId == 986 ||
            chartId == 1066 ||
            chartId == 1127 ||
            chartId == 1128 ||
            chartId == 1129 ||
            chartId == 1130 ||
            chartId == 1131 ||
            chartId == 1132 ||
            chartId == 1098
              ? false
              : true,
          pivot: false,
          suppressToolPanel:
            chartId == 1068 || chartId == 1067 || chartId == 941 ? true : false,
          hide:
            drillDown == 38 ||
            chartId == 946 ||
            chartId == 937 ||
            chartId == 988 ||
            chartId == 987 ||
            chartId == 986 ||
            chartId == 1066 ||
            chartId == 1127 ||
            chartId == 1128 ||
            chartId == 1129 ||
            chartId == 1130 ||
            chartId == 1131 ||
            chartId == 1132 ||
            chartId == 1098
              ? false
              : true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          // filter: 'agNumberColumnFilter',
          field: 'lbrsale',
          width: drillDown == 5 ? 80 : 80,
          minWidth: 80,
          flex: 1,
          // flex: (chartId == 946 || chartId == 1127 || chartId == 1044 || chartId == 1098|| chartId == 918
          //   || chartId == 955 || drillDown == 38) ? 1 : '',
          chartDataType: 'series',
          hide:
            ((drillDown == 1 ||
              (drillDown == 0 &&
                chartId != 1318 &&
                chartId != 1319 &&
                chartId != 1320 &&
                chartId != 1321 &&
                chartId != 1322 &&
                chartId != 1323 &&
                chartId != 1324 &&
                chartId != 1325) ||
              drillDown == 6 ||
              drillDown == 7 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5 ||
              drillDown == 2 ||
              drillDown == 20 ||
              drillDown == 19 ||
              drillDown == 16) &&
              (category == 0 ||
                category == 2 ||
                category == 3 ||
                category == 4 ||
                category == 5 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9 ||
                category == 10 ||
                category == 12 ||
                category == 11 ||
                category == 19 ||
                category == 20) &&
              chartId != 1327 &&
              chartId != 1328 &&
              chartId != 1329 &&
              chartId != 1330 &&
              chartId != 1331 &&
              chartId != 1332 &&
              chartId != 1333 &&
              chartId != 1144 &&
              chartId != 1145 &&
              chartId != 1146 &&
              chartId != 1147 &&
              chartId != 1162 &&
              chartId != 1163) ||
            (drillDown == 0 && category == 18) ||
            (drillDown == 37 && category == 0) ||
            (drillDown == 21 && category == 8) ||
            drillDown == 38 ||
            drillDown == 47 ||
            (drillDown == 39 &&
              chartId != 1218 &&
              chartId != 1219 &&
              chartId != 1220 &&
              chartId != 1221 &&
              chartId != 1222 &&
              chartId != 1223 &&
              chartId != 1224 &&
              chartId != 1225 &&
              chartId != 1226 &&
              chartId != 1227 &&
              chartId != 1228 &&
              chartId != 1229 &&
              chartId != 1230 &&
              chartId != 1231 &&
              chartId != 1238)
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          //aggFunc: 'sum',
        },
        {
          headerName: 'Labor Cost',
          // filter: 'agNumberColumnFilter',
          field: 'lbrcost',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // aggFunc: 'sum',
        },
        {
          headerName: 'Sold Hours',
          // filter: 'agNumberColumnFilter',
          field: 'lbrsoldhours',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          hide:
            ((drillDown == 1 ||
              drillDown == 0 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5 ||
              drillDown == 2 ||
              drillDown == 20 ||
              drillDown == 19 ||
              drillDown == 47 ||
              drillDown == 16) &&
              (category == 0 ||
                category == 2 ||
                category == 3 ||
                category == 4 ||
                category == 5 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9 ||
                category == 10 ||
                category == 12 ||
                category == 11 ||
                category == 19 ||
                category == 20) &&
              chartId != 1327 &&
              chartId != 1328 &&
              chartId != 1329 &&
              chartId != 1330 &&
              chartId != 1331 &&
              chartId != 1332 &&
              chartId != 1333 &&
              chartId != 1144 &&
              chartId != 1145 &&
              chartId != 1146 &&
              chartId != 1147 &&
              chartId != 1162 &&
              chartId != 1163) ||
            (drillDown == 0 && category == 18) ||
            (drillDown == 37 && category == 0) ||
            (drillDown == 21 && category == 8) ||
            drillDown == 38 ||
            (drillDown == 39 &&
              chartId != 1218 &&
              chartId != 1219 &&
              chartId != 1220 &&
              chartId != 1221 &&
              chartId != 1222 &&
              chartId != 1223 &&
              chartId != 1224 &&
              chartId != 1225 &&
              chartId != 1226 &&
              chartId != 1227 &&
              chartId != 1228 &&
              chartId != 1229 &&
              chartId != 1230 &&
              chartId != 1231 &&
              chartId != 1238)
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          suppressMenu: true,
          unSortIcon: true
          // aggFunc: 'sum',
        },
        {
          headerName: 'Parts Sale',
          // filter: 'agNumberColumnFilter',
          field: 'prtextendedsale',
          width: 80,
          minWidth: 80,
          flex: 1,
          // flex:( chartId == 1049 || chartId == 952|| chartId == 966|| chartId == 1002
          //   || drillDown == 10|| drillDown == 11|| chartId == 953|| drillDown == 39 ||
          //   (chartId>=1143 && chartId<= 1150)|| (chartId>=1318 && chartId<= 1325)
          //   || (chartId>=1326 && chartId<= 1334)) ? 1 : '',
          chartDataType: 'series',
          hide:
            ((drillDown == 1 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 9) &&
              (category == 1 || category == 2 || category == 20)) ||
            drillDown == 10 ||
            drillDown == 11 ||
            drillDown == 47 ||
            (drillDown == 0 && chartId == 1318) ||
            chartId == 1319 ||
            chartId == 1320 ||
            chartId == 1321 ||
            chartId == 1322 ||
            chartId == 1323 ||
            chartId == 1324 ||
            chartId == 1325 ||
            (drillDown == 12 &&
              (category == 3 ||
                category == 4 ||
                category == 5 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            drillDown == 12 ||
            (drillDown == 20 && category == 0) ||
            category == 10 ||
            (drillDown == 9 &&
              (category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 10)) ||
            chartId == 953 ||
            chartId == 916 ||
            (drillDown == 37 && category == 0) ||
            (drillDown == 21 && category == 8) ||
            (drillDown == 39 &&
              (chartId == 1218 ||
                chartId == 1219 ||
                chartId == 1220 ||
                chartId == 1221 ||
                chartId == 1222 ||
                chartId == 1223 ||
                chartId == 1224 ||
                chartId == 1225 ||
                chartId == 1226 ||
                chartId == 1227 ||
                chartId == 1228 ||
                chartId == 1229 ||
                chartId == 1230 ||
                chartId == 1231 ||
                chartId == 1238 ||
                chartId == 1318)) ||
            chartId == 1327 ||
            chartId == 1329 ||
            chartId == 1330 ||
            chartId == 1331 ||
            chartId == 1332 ||
            chartId == 1144 ||
            chartId == 1145 ||
            chartId == 1146 ||
            chartId == 1147 ||
            chartId == 1162 ||
            chartId == 1163
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // aggFunc: 'sum',
        },
        {
          headerName: 'Parts Cost',
          // filter: 'agNumberColumnFilter',
          field: 'prtextendedcost',
          width: 80,
          minWidth: 80,
          flex: 1,
          // flex:( chartId == 1049 || chartId == 952|| chartId == 966|| chartId == 1002
          //   || drillDown == 10|| drillDown == 11|| chartId == 953) ? 1 : '',
          chartDataType: 'series',
          hide:
            chartId == 1049 ||
            chartId == 952 ||
            chartId == 966 ||
            chartId == 953 ||
            chartId == 1143 ||
            chartId == 916 ||
            category == 1 ||
            chartId == 1238 ||
            chartId == 1326 ||
            chartId == 1334
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          //  aggFunc: 'sum',
        },
        {
          headerName: 'Labor GP',
          // filter: 'agNumberColumnFilter',
          field: 'lbrgrossprofit',
          width: drillDown == 5 ? 80 : 80,
          minWidth: 80,
          flex: 1,
          // flex:(chartId == 946 || chartId == 1127|| chartId == 1044 || chartId == 1098|| chartId == 918
          //   || chartId == 955|| drillDown == 38) ? 1 : '',
          chartDataType: 'series',
          hide:
            ((drillDown == 1 ||
              drillDown == 2 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5 ||
              (drillDown == 0 && chartId != 1318) ||
              drillDown == 6 ||
              drillDown == 7 ||
              drillDown == 47) &&
              (category == 0 || category == 2 || category == 9) &&
              chartId != 1145) ||
            (drillDown == 1 &&
              (category == 5 ||
                category == 3 ||
                category == 4 ||
                category == 12 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 19 ||
                category == 20) &&
              chartId != 1327 &&
              chartId != 1328 &&
              chartId != 1329 &&
              chartId != 1330 &&
              chartId != 1331 &&
              chartId != 1332 &&
              chartId != 1333 &&
              chartId != 1144 &&
              chartId != 1145 &&
              chartId != 1146 &&
              chartId != 1147 &&
              chartId != 1162 &&
              chartId != 1163) ||
            (drillDown == 2 &&
              (category == 10 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9)) ||
            (drillDown == 3 &&
              (category == 5 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 0 &&
              (category == 10 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 18)) ||
            (drillDown == 6 &&
              (category == 12 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9 ||
                category == 17)) ||
            (drillDown == 5 &&
              (category == 12 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9 ||
                category == 11)) ||
            drillDown == 19 ||
            (drillDown == 20 && category == 0) ||
            (drillDown == 21 &&
              (category == 8 || category == 7 || category == 6)) ||
            (drillDown == 37 && category == 0) ||
            drillDown == 38 ||
            (drillDown == 39 &&
              chartId != 1218 &&
              chartId != 1219 &&
              chartId != 1220 &&
              chartId != 1221 &&
              chartId != 1222 &&
              chartId != 1223 &&
              chartId != 1224 &&
              chartId != 1225 &&
              chartId != 1226 &&
              chartId != 1227 &&
              chartId != 1228 &&
              chartId != 1229 &&
              chartId != 1230 &&
              chartId != 1231 &&
              chartId != 1238 &&
              chartId != 1318)
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          //  aggFunc: 'sum',
        },
        {
          headerName: 'Labor GP %',
          // filter: 'agNumberColumnFilter',
          field: 'lbrGrossprofitpercentage',
          width: 80,
          minWidth: 80,
          flex: 1,
          // flex: (chartId == 918|| chartId == 955) ? 1 : '',
          chartDataType: 'series',
          hide:
            ((drillDown == 1 ||
              drillDown == 2 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5 ||
              (drillDown == 0 && chartId != 1318) ||
              drillDown == 6 ||
              drillDown == 7) &&
              (category == 0 || category == 2 || category == 9) &&
              chartId != 1145) ||
            (drillDown == 1 &&
              (category == 5 ||
                category == 3 ||
                category == 4 ||
                category == 12 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 19 ||
                category == 20) &&
              chartId != 1327 &&
              chartId != 1328 &&
              chartId != 1329 &&
              chartId != 1330 &&
              chartId != 1331 &&
              chartId != 1332 &&
              chartId != 1333 &&
              chartId != 1144 &&
              chartId != 1146 &&
              chartId != 1147 &&
              chartId != 1162 &&
              chartId != 1163) ||
            (drillDown == 2 &&
              (category == 10 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9)) ||
            (drillDown == 3 &&
              (category == 5 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 0 &&
              (category == 10 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 18)) ||
            (drillDown == 6 &&
              (category == 12 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9 ||
                category == 17)) ||
            (drillDown == 5 &&
              (category == 12 ||
                category == 6 ||
                category == 7 ||
                category == 8 ||
                category == 9 ||
                category == 11)) ||
            drillDown == 19 ||
            (drillDown == 20 && category == 0) ||
            (drillDown == 21 &&
              (category == 8 || category == 7 || category == 6)) ||
            (drillDown == 37 && category == 0) ||
            drillDown == 38 ||
            drillDown == 47 ||
            (drillDown == 39 &&
              chartId != 1218 &&
              chartId != 1219 &&
              chartId != 1220 &&
              chartId != 1221 &&
              chartId != 1222 &&
              chartId != 1223 &&
              chartId != 1224 &&
              chartId != 1225 &&
              chartId != 1226 &&
              chartId != 1227 &&
              chartId != 1228 &&
              chartId != 1229 &&
              chartId != 1230 &&
              chartId != 1231 &&
              chartId != 1238)
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP',
          // filter: 'agNumberColumnFilter',
          field: 'prtsgrossprofit',
          width: 80,
          minWidth: 80,
          flex: 1,
          // flex:( chartId == 1049 || chartId == 952|| chartId == 966|| chartId == 1002
          //   || drillDown == 10|| drillDown == 11|| chartId == 953|| drillDown == 39||
          //   (chartId>=1143 && chartId<= 1150)|| (chartId>=1318 && chartId<= 1325)
          //   || (chartId>=1326 && chartId<= 1334)) ? 1 : '',
          chartDataType: 'series',
          hide:
            ((drillDown == 1 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5) &&
              (category == 1 || category == 2 || category == 20)) ||
            (drillDown == 10 &&
              (category == 5 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 11 &&
              (category == 5 ||
                category == 1 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 12 &&
              (category == 10 ||
                category == 5 ||
                category == 0 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 23 &&
              (category == 6 || category == 7 || category == 8)) ||
            (drillDown == 9 &&
              (category == 10 ||
                category == 6 ||
                category == 7 ||
                category == 8)) ||
            (drillDown == 20 && category == 0) ||
            (drillDown == 21 &&
              (category == 8 || category == 7 || category == 6)) ||
            (drillDown == 37 && category == 0) ||
            (drillDown == 39 && drillDown == 47) ||
            chartId == 1218 ||
            chartId == 1219 ||
            chartId == 1220 ||
            chartId == 1221 ||
            chartId == 1222 ||
            chartId == 1223 ||
            chartId == 1224 ||
            chartId == 1225 ||
            chartId == 1226 ||
            chartId == 1227 ||
            chartId == 1228 ||
            chartId == 1229 ||
            chartId == 1230 ||
            chartId == 1231 ||
            chartId == 1238 ||
            chartId == 1318 ||
            chartId == 1319 ||
            chartId == 1320 ||
            chartId == 1321 ||
            chartId == 1322 ||
            chartId == 1323 ||
            chartId == 1324 ||
            chartId == 1325 ||
            chartId == 1327 ||
            chartId == 1329 ||
            chartId == 1330 ||
            chartId == 1331 ||
            chartId == 1332 ||
            chartId == 1144 ||
            chartId == 1145 ||
            chartId == 1146 ||
            chartId == 1147 ||
            chartId == 1162 ||
            chartId == 1163
              ? false
              : true,

          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          //   aggFunc: 'sum',
        },
        {
          headerName: 'Parts GP %',
          // filter: 'agNumberColumnFilter',
          field: 'prtsGrossprofitpercentage',
          width: 80,
          minWidth: 80,
          flex: 1,
          // flex:(drillDown == 10|| drillDown == 39 || (chartId>=1143 && chartId<= 1150)
          // || (chartId>=1318 && chartId<= 1325)|| (chartId>=1326 && chartId<= 1334)) ? 1 : '',
          chartDataType: 'series',
          hide:
            ((drillDown == 1 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5) &&
              (category == 1 ||
                category == 2 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 10 &&
              (category == 5 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 11 &&
              (category == 5 ||
                category == 1 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 12 &&
              (category == 10 ||
                category == 5 ||
                category == 0 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 23 &&
              (category == 6 || category == 7 || category == 8)) ||
            (drillDown == 0 && chartId == 1318) ||
            chartId == 1319 ||
            chartId == 1320 ||
            chartId == 1321 ||
            chartId == 1322 ||
            chartId == 1323 ||
            chartId == 1324 ||
            chartId == 1325 ||
            (drillDown == 9 &&
              (category == 10 ||
                category == 6 ||
                category == 7 ||
                category == 8)) ||
            (drillDown == 20 && category == 0) ||
            (drillDown == 21 &&
              (category == 8 || category == 7 || category == 6)) ||
            (drillDown == 37 && category == 0) ||
            drillDown == 39 ||
            (drillDown == 47 &&
              (chartId == 1218 ||
                chartId == 1219 ||
                chartId == 1220 ||
                chartId == 1221 ||
                chartId == 1222 ||
                chartId == 1223 ||
                chartId == 1224 ||
                chartId == 1225 ||
                chartId == 1226 ||
                chartId == 1227 ||
                chartId == 1228 ||
                chartId == 1229 ||
                chartId == 1230 ||
                chartId == 1231 ||
                chartId == 1238 ||
                chartId == 1318)) ||
            chartId == 1327 ||
            chartId == 1329 ||
            chartId == 1330 ||
            chartId == 1331 ||
            chartId == 1332 ||
            chartId == 1144 ||
            chartId == 1145 ||
            chartId == 1146 ||
            chartId == 1147 ||
            chartId == 1162 ||
            chartId == 1163
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        // {
        //   headerName: 'ELR',
        //   // filter: 'agNumberColumnFilter',
        //   field: 'elr',
        //   width: drillDown == 5 ? 72 : 68,
        //   minWidth: drillDown == 5 ? 72 : 68,
        //   flex: 1,
        //   //flex:( chartId == 1044 || chartId == 918|| chartId == 955) ? 1 : '',
        //   chartDataType: 'series',
        //   cellStyle: this.cellStyles,
        //   valueFormatter: this.formatCellValue,
        //   cellClass: 'twoDecimalPlacesWith$',
        //   suppressToolPanel:
        //     ((drillDown == 1 ||
        //       drillDown == 2 ||
        //       drillDown == 3 ||
        //       drillDown == 4 ||
        //       drillDown == 5 ||
        //       drillDown == 6 ||
        //       drillDown == 7) &&
        //       (category == 0 || category == 2 || category == 9) &&
        //       chartId != 946 &&
        //       chartId != 937 &&
        //       chartId != 988 &&
        //       chartId != 987 &&
        //       chartId != 986 &&
        //       chartId != 1066 &&
        //       chartId != 1127 &&
        //       chartId != 1128 &&
        //       chartId != 1129 &&
        //       chartId != 1130 &&
        //       chartId != 1131 &&
        //       chartId != 1132 &&
        //       chartId != 1098 &&
        //       chartId != 1145) ||
        //     (drillDown == 1 &&
        //       (category == 5 ||
        //         category == 3 ||
        //         category == 4 ||
        //         category == 12 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 19 ||
        //         category == 20) &&
        //       chartId != 1327 &&
        //       chartId != 1328 &&
        //       chartId != 1329 &&
        //       chartId != 1330 &&
        //       chartId != 1331 &&
        //       chartId != 1332 &&
        //       chartId != 1333 &&
        //       chartId != 1144 &&
        //       chartId != 1145 &&
        //       chartId != 1146 &&
        //       chartId != 1147 &&
        //       chartId != 1162 &&
        //       chartId != 1163) ||
        //     (drillDown == 2 &&
        //       (category == 10 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 9) &&
        //       chartId != 946 &&
        //       chartId != 937 &&
        //       chartId != 988 &&
        //       chartId != 987 &&
        //       chartId != 986 &&
        //       chartId != 1066 &&
        //       chartId != 1127 &&
        //       chartId != 1128 &&
        //       chartId != 1129 &&
        //       chartId != 1130 &&
        //       chartId != 1131 &&
        //       chartId != 1132 &&
        //       chartId != 1098) ||
        //     (drillDown == 3 &&
        //       (category == 5 ||
        //         category == 3 ||
        //         category == 4 ||
        //         category == 7 ||
        //         category == 19 ||
        //         category == 20)) ||
        //     (drillDown == 0 &&
        //       (category == 10 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 18)) ||
        //     (drillDown == 6 &&
        //       (category == 12 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 9 ||
        //         category == 17)) ||
        //     (drillDown == 5 &&
        //       (category == 12 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 9 ||
        //         category == 11)) ||
        //     drillDown == 19 ||
        //     (drillDown == 20 && category == 0) ||
        //     (drillDown == 21 &&
        //       (category == 8 || category == 7 || category == 6)) ||
        //     (drillDown == 37 && category == 0) ||
        //     ((drillDown == 39 || drillDown == 47) &&
        //       chartId != 1218 &&
        //       chartId != 1219 &&
        //       chartId != 1220 &&
        //       chartId != 1221 &&
        //       chartId != 1222 &&
        //       chartId != 1223 &&
        //       chartId != 1224 &&
        //       chartId != 1225 &&
        //       chartId != 1226 &&
        //       chartId != 1227 &&
        //       chartId != 1228 &&
        //       chartId != 1229 &&
        //       chartId != 1230 &&
        //       chartId != 1231 &&
        //       chartId != 1238) ||
        //     (drillDown == 0 &&
        //       chartId != 1318 &&
        //       chartId != 1319 &&
        //       chartId != 1320 &&
        //       chartId != 1321 &&
        //       chartId != 1322 &&
        //       chartId != 1323 &&
        //       chartId != 1324 &&
        //       chartId != 1325)
        //       ? false
        //       : true,
        //   hide:
        //     ((drillDown == 1 ||
        //       drillDown == 2 ||
        //       drillDown == 3 ||
        //       drillDown == 4 ||
        //       drillDown == 5 ||
        //       drillDown == 6 ||
        //       drillDown == 7) &&
        //       (category == 0 || category == 2 || category == 9) &&
        //       chartId != 946 &&
        //       chartId != 937 &&
        //       chartId != 988 &&
        //       chartId != 987 &&
        //       chartId != 986 &&
        //       chartId != 1066 &&
        //       chartId != 1127 &&
        //       chartId != 1128 &&
        //       chartId != 1129 &&
        //       chartId != 1130 &&
        //       chartId != 1131 &&
        //       chartId != 1132 &&
        //       chartId != 1098 &&
        //       chartId != 1145) ||
        //     (drillDown == 1 &&
        //       (category == 5 ||
        //         category == 3 ||
        //         category == 4 ||
        //         category == 12 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 19 ||
        //         category == 20) &&
        //       chartId != 1327 &&
        //       chartId != 1328 &&
        //       chartId != 1329 &&
        //       chartId != 1330 &&
        //       chartId != 1331 &&
        //       chartId != 1332 &&
        //       chartId != 1333 &&
        //       chartId != 1144 &&
        //       chartId != 1145 &&
        //       chartId != 1146 &&
        //       chartId != 1147 &&
        //       chartId != 1162 &&
        //       chartId != 1163) ||
        //     (drillDown == 2 &&
        //       (category == 10 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 9) &&
        //       chartId != 946 &&
        //       chartId != 937 &&
        //       chartId != 988 &&
        //       chartId != 987 &&
        //       chartId != 986 &&
        //       chartId != 1066 &&
        //       chartId != 1127 &&
        //       chartId != 1128 &&
        //       chartId != 1129 &&
        //       chartId != 1130 &&
        //       chartId != 1131 &&
        //       chartId != 1132 &&
        //       chartId != 1098) ||
        //     (drillDown == 3 &&
        //       (category == 5 ||
        //         category == 3 ||
        //         category == 4 ||
        //         category == 7 ||
        //         category == 19 ||
        //         category == 20)) ||
        //     (drillDown == 0 &&
        //       (category == 10 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 18)) ||
        //     (drillDown == 6 &&
        //       (category == 12 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 9 ||
        //         category == 17)) ||
        //     (drillDown == 5 &&
        //       (category == 12 ||
        //         category == 6 ||
        //         category == 7 ||
        //         category == 8 ||
        //         category == 9 ||
        //         category == 11)) ||
        //     drillDown == 19 ||
        //     (drillDown == 20 && category == 0) ||
        //     (drillDown == 21 &&
        //       (category == 8 || category == 7 || category == 6)) ||
        //     (drillDown == 37 && category == 0) ||
        //     ((drillDown == 39 || drillDown == 47) &&
        //       chartId != 1218 &&
        //       chartId != 1219 &&
        //       chartId != 1220 &&
        //       chartId != 1221 &&
        //       chartId != 1222 &&
        //       chartId != 1223 &&
        //       chartId != 1224 &&
        //       chartId != 1225 &&
        //       chartId != 1226 &&
        //       chartId != 1227 &&
        //       chartId != 1228 &&
        //       chartId != 1229 &&
        //       chartId != 1230 &&
        //       chartId != 1231 &&
        //       chartId != 1238) ||
        //     (drillDown == 0 &&
        //       chartId != 1318 &&
        //       chartId != 1319 &&
        //       chartId != 1320 &&
        //       chartId != 1321 &&
        //       chartId != 1322 &&
        //       chartId != 1323 &&
        //       chartId != 1324 &&
        //       chartId != 1325)
        //       ? false
        //       : true,
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        {
          // filter: 'agNumberColumnFilter',
          field: 'markup',
          width: drillDown != 2 ? 90 : 100,
          minWidth: drillDown != 2 ? 90 : 100,
          flex: 1,
          // flex:(drillDown == 10|| drillDown == 39||(chartId>=1143 && chartId<= 1150)
          // || (chartId>=1318 && chartId<= 1325)|| (chartId>=1326 && chartId<= 1334)) ? 1 : '',
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          hide:
            ((drillDown == 1 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5) &&
              (category == 1 ||
                category == 2 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 10 &&
              (category == 5 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 11 &&
              (category == 5 ||
                category == 1 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 12 &&
              (category == 10 ||
                category == 5 ||
                category == 0 ||
                category == 3 ||
                category == 4 ||
                category == 7 ||
                category == 19 ||
                category == 20)) ||
            (drillDown == 23 &&
              (category == 6 || category == 7 || category == 8)) ||
            (drillDown == 0 && chartId == 1318) ||
            chartId == 1319 ||
            chartId == 1320 ||
            chartId == 1321 ||
            chartId == 1322 ||
            chartId == 1323 ||
            chartId == 1324 ||
            chartId == 1325 ||
            (drillDown == 9 &&
              (category == 10 ||
                category == 6 ||
                category == 7 ||
                category == 8)) ||
            (drillDown == 20 && category == 0) ||
            (drillDown == 21 &&
              (category == 8 || category == 7 || category == 6)) ||
            (drillDown == 37 && category == 0) ||
            (drillDown == 39 &&
              (chartId == 1218 ||
                chartId == 1219 ||
                chartId == 1220 ||
                chartId == 1221 ||
                chartId == 1222 ||
                chartId == 1223 ||
                chartId == 1224 ||
                chartId == 1225 ||
                chartId == 1226 ||
                chartId == 1227 ||
                chartId == 1228 ||
                chartId == 1229 ||
                chartId == 1230 ||
                chartId == 1231 ||
                chartId == 1238)) ||
            chartId == 1327 ||
            chartId == 1329 ||
            chartId == 1330 ||
            chartId == 1331 ||
            chartId == 1332 ||
            chartId == 1144 ||
            chartId == 1145 ||
            chartId == 1146 ||
            chartId == 1147 ||
            chartId == 1162 ||
            chartId == 1163
              ? false
              : true,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsAddOns: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          hide: true,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          hide: true,
          // filter: 'agTextColumnFilter',
          width: 67,
          minWidth: 67,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 70,
          minWidth: 70,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          // filter: 'agNumberColumnFilter',
          width: 58,
          minWidth: 58,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Add on flag',
          field: 'linaddonflag',
          width: 70,
          minWidth: 70,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          hide: drillDown == 25 || drillDown == 26 ? false : true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1107 || chartId == 1110 || chartId == 1117 ? true : false
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          aggFunc: 'sum',
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1107 || chartId == 1110 || chartId == 1117 ? true : false
        },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1106 ||
            chartId == 1108 ||
            chartId == 1109 ||
            chartId == 1116
              ? true
              : false
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP',
          field: 'lbrgrossprofit',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1107 || chartId == 1110 || chartId == 1117 ? true : false
        },
        {
          headerName: 'Labor GP %',
          field: 'lbrGrossprofitpercentage',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1107 || chartId == 1110 || chartId == 1117 ? true : false
        },
        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1106 ||
            chartId == 1108 ||
            chartId == 1109 ||
            chartId == 1116
              ? true
              : false
        },
        {
          headerName: 'Parts GP %',
          field: 'prtsGrossprofitpercentage',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true,
          hide:
            chartId == 1106 ||
            chartId == 1108 ||
            chartId == 1109 ||
            chartId == 1116
              ? true
              : false
        },

        {
          headerName: 'ELR',
          field: 'elr',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1107 || chartId == 1110 || chartId == 1117
              ? true
              : false,
          // hide:
          //   (drillDown == 1 || drillDown == 3 || drillDown == 4) &&
          //   (category == 0 || category == 2)
          //     ? false
          //     : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$'
        },
        {
          headerName: 'Markup',
          field: 'markup',
          // filter: 'agNumberColumnFilter',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'series',
          cellClass: 'fourDecimalPlaces',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            chartId == 1106 ||
            chartId == 1108 ||
            chartId == 1109 ||
            chartId == 1116
              ? true
              : false,
          // hide:
          //   (drillDown == 1 || drillDown == 3 || drillDown == 4) &&
          //   (category == 1 || category == 2)
          //     ? false
          //     : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup
        }
      ],
      columnDefsPrtsMarkup: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          // filter: 'agTextColumnFilter',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 90,
          minWidth: 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          // filter: 'agNumberColumnFilter',
          width: 65,
          minWidth: 65,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP %',
          field: 'prtsGrossprofitpercentage',
          // filter: 'agNumberColumnFilter',
          width: 65,
          minWidth: 65,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup',
          field: 'markup',
          // filter: 'agNumberColumnFilter',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          cellClass: 'fourDecimalPlaces',
          valueFormatter: this.formatCellValueMarkup,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsSingleJob: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          suppressMenu: true,
          //   hide: drillDown == 33 ? true : false,
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          // filter: 'agTextColumnFilter',
          width: 77,
          minWidth: 77,
          flex: 1,
          hide: true,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          // filter: 'agTextColumnFilter',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 90,
          minWidth: 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 90,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 90,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          // filter: 'agNumberColumnFilter',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          aggFunc: 'sum',
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP',
          field: 'lbrgrossprofit',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP %',
          field: 'lbrGrossprofitpercentage',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP %',
          field: 'prtsGrossprofitpercentage',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Is Single Job',
          field: 'issinglejob',
          width: 78,
          minWidth: 78,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR',
          field: 'elr',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup',
          field: 'markup',
          // filter: 'agNumberColumnFilter',
          width: 85,
          minWidth: 85,
          flex: 1,
          chartDataType: 'series',
          cellClass: 'fourDecimalPlaces',
          hide:
            (drillDown == 1 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 18) &&
            (category == 1 || category == 2)
              ? false
              : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsActualHrs: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'month_year',
          width: 72,
          chartDataType: 'category',
          hide: true,
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'serviceadvisor',
          // filter: 'agNumberColumnFilter',
          width: 75,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          hide: true,
          // filter: 'agTextColumnFilter',
          width: 67,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          // filter: 'agTextColumnFilter',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 90,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          minWidth: 150,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          // filter: 'agNumberColumnFilter',
          width: 58,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          // filter: 'agNumberColumnFilter',
          width: 80,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Flat Rate Hours',
          field: 'flatratehours',
          // filter: 'agNumberColumnFilter',
          width: 65,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          // filter: 'agNumberColumnFilter',
          width: 80,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Tech Hours',
          field: 'techhours',
          // filter: 'agNumberColumnFilter',
          width: 68,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsMovingElr: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Open Date',
          field: 'startopendate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'endopendate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Tranche',
          field: 'currentGroup',
          // filter: 'agNumberColumnFilter',
          width: 70,
          minWidth: 70,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Moving ELR',
          field: 'movingElr',
          // filter: 'agNumberColumnFilter',
          width: '75',
          minWidth: 75,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueElr,
          cellClass: 'DecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Moving ELR Median',
          field: 'movingElrMedian',
          // filter: 'agNumberColumnFilter',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueElr,
          cellClass: 'DecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Total Labor Sale',
          field: 'totalLbrsale',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueElr,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Total Labor Soldhours',
          field: 'totalLbrsoldhours',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'RO Count - Current Group',
          field: 'roCountInCurrentGroup',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Moving ELR after Discount',
          field: 'elrAfterDiscount',
          // filter: 'agNumberColumnFilter',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueElr,
          cellClass: 'DecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Make',
          field: 'make',
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsMovingMarkup: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          hide: true,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Open Date',
          field: 'startopendate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'endopendate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Current Group',
          field: 'currentGroup',
          // filter: 'agNumberColumnFilter',
          width: 73,
          minWidth: 73,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Moving Markup',
          field: 'movingMarkup',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Moving Markup Median',
          field: 'movingMarkupMedian',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Total Parts Cost',
          field: 'totalPartsCost',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Total Parts Sale',
          field: 'totalPartsSale',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Markup after Discount',
          field: 'movingMarkupAfterDiscount',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsItemization: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          width: 85,
          minWidth: 85,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          // filter: 'agNumberColumnFilter',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR Repair',
          field: 'elrRepair',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          //hide: drillDown == 16 && label == 0 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR Competitive',
          field: 'elrCompetitive',
          // filter: 'agNumberColumnFilter',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          //hide: drillDown == 16 && label == 1 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR Maintenance',
          field: 'elrMaintenance',
          // filter: 'agNumberColumnFilter',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          //  hide: drillDown == 16 && label == 2 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsPrtsItemization: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          width: 85,
          minWidth: 85,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          // filter: 'agNumberColumnFilter',
          width: 85,
          minWidth: 85,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtscost',
          // filter: 'agNumberColumnFilter',
          width: 85,
          minWidth: 85,
          flex: 1,
          chartDataType: 'category',
          hide: drillDown == 17 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup Repair',
          field: 'markupRepair',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          //hide: drillDown == 17 && label == 0 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup Competitive',
          field: 'markupCompetitive',
          // filter: 'agNumberColumnFilter',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          //hide: drillDown == 17 && label == 1 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup Maintenance',
          field: 'markupMaintenance',
          // filter: 'agNumberColumnFilter',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          //hide: drillDown == 17 && label == 2 ? false : true,
          //hide: chartId == 1096 ? true : false,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsDiscount: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'serviceadvisor',
          // filter: 'agNumberColumnFilter',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 90,
          minWidth: 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Class Sort Type',
          field: 'disclassortype',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          width: 100,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          minWidth: 100,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          // filter: 'agNumberColumnFilter',
          width: 100,
          chartDataType: 'category',
          minWidth: 100,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Discount',
          field: 'dislabordiscount',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          hide: drillDown == 31 && category == ' Labor Discount' ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Level',
          field: 'dislevel',
          // filter: 'agNumberColumnFilter',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Line Code',
          field: 'dislinecode',
          // filter: 'agNumberColumnFilter',
          width: 85,
          minWidth: 85,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount LopSeq No.',
          field: 'dislopseqno',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Discount',
          field: 'dispartsdiscount',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          hide: drillDown == 31 && category == ' Parts Discount' ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Seq No.',
          field: 'dissequenceno',
          // filter: 'agNumberColumnFilter',
          width: 83,
          minWidth: 83,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Sequence Number_id',
          field: 'dissequenceno_idx',
          // filter: 'agNumberColumnFilter',
          width: 95,
          minWidth: 95,
          flex: 1,
          hide: true,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsDiscountSA: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          // flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          hide: true,
          // filter: 'agTextColumnFilter',
          width: 67,
          minWidth: 67,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'lbrPaytype',
          // filter: 'agTextColumnFilter',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          width: 100,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          minWidth: 100,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Discount',
          field: 'labordiscount',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Discount',
          field: 'partsdiscount',
          // filter: 'agNumberColumnFilter',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          // filter: 'agNumberColumnFilter',
          width: 58,
          minWidth: 58,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsDiscountJobRo: [
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discounted Jobs Per RO',
          field: 'discountedjobsperro',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          //  hide: drillDown == 33 ? false : true,
          cellStyle: this.cellStyles,
          aggFunc: 'count',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Total Discounted Jobs',
          field: 'totaldiscountedjobs',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          // hide: drillDown == 33 ? false : true,
          cellStyle: this.cellStyles,
          aggFunc: 'count',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Total Discounted RO Count',
          field: 'totaldiscountedrocount',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          //hide: drillDown == 33 ? false : true,
          cellStyle: this.cellStyles,
          aggFunc: 'count',
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsDisSummaryLbr: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Code',
          field: 'lbrlinecode',
          hide: true,
          // filter: 'agTextColumnFilter',
          width: 67,
          minWidth: 67,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          // filter: 'agTextColumnFilter',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 90,
          minWidth: 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          width: 100,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          hide: true,
          minWidth: 100,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Level',
          field: 'dislevel',
          // filter: 'agTextColumnFilter',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          // filter: 'agNumberColumnFilter',
          width: 58,
          minWidth: 58,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Discount',
          field: 'labordiscount',
          // filter: 'agNumberColumnFilter',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sale %',
          field: 'salepercentage',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
          // valueFormatter: this.formatCellValueGP
        },
        {
          headerName: 'Apportioned Labor Discount',
          field: 'apportionedlbrdiscount',
          width: 104,
          minWidth: 104,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueDiscount,
          cellClass: 'twoDecimalPlacesDiscount',
          unSortIcon: true,
          suppressMenu: true,
          // filter: 'agNumberColumnFilter',
          comparator: function(valueA, valueB) {
            return Math.abs(valueA) - Math.abs(valueB);
          }
        }
      ],
      columnDefsDisSummaryPrts: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Pay Type',
          field: 'laborpaytype',
          // filter: 'agTextColumnFilter',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'laborpaytypegroup',
          width: 90,
          minWidth: 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          width: 100,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          hide: true,
          minWidth: 100,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Code',
          field: 'prtlinecode',
          // filter: 'agTextColumnFilter',
          width: 67,
          minWidth: 67,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Level',
          field: 'dislevel',
          // filter: 'agTextColumnFilter',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'prtlaborsequenceno',
          // filter: 'agNumberColumnFilter',
          width: 65,
          minWidth: 65,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtcost',
          // filter: 'agNumberColumnFilter',
          width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Discount',
          field: 'partsdiscount',
          // filter: 'agNumberColumnFilter',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sale %',
          field: 'salepercentage',
          // filter: 'agNumberColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
          //  valueFormatter: this.formatCellValueGP
        },
        {
          headerName: 'Apportioned Parts Discount',
          field: 'apportionedlbrdiscount',
          // filter: 'agNumberColumnFilter',
          width: 104,
          minWidth: 104,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueDiscount,
          cellClass: 'twoDecimalPlacesDiscount',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return Math.abs(valueA) - Math.abs(valueB);
          }
        }
      ],
      columnDefsDiscountByRO: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 72,
          minWidth: 72,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Line Code',
          field: 'lbrlinecode',
          // filter: 'agTextColumnFilter',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'lbrlabortype',
          // filter: 'agTextColumnFilter',
          width: realm == 'ferrarioat_store' ? 100 : 66,
          minWidth: realm == 'ferrarioat_store' ? 100 : 66,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'lbrPaytype',
          width: 90,
          minWidth: 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // filter: 'agTextColumnFilter',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 80,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          width: 100,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          hide: true,
          minWidth: 100,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          // filter: 'agNumberColumnFilter',
          width: 67,
          minWidth: 67,
          flex: 1,
          hide: true,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'category',
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Discount',
          field: 'labordiscount',
          // filter: 'agNumberColumnFilter',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtsale',
          // filter: 'agNumberColumnFilter',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Discount',
          field: 'partsdiscount',
          // filter: 'agNumberColumnFilter',
          width: 82,
          minWidth: 82,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsAverageHrs: [
        {
          headerName: 'Month',
          field: 'month_year',
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Distinct Techs',
          field: 'techlist',
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Sold Hours',
          field: 'soldhours',
          chartDataType: 'category',
          // filter: 'agNumberColumnFilter',
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Available Hours',
          field: 'availablehrs',
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      columnDefsAverageHrsSa: [
        {
          headerName: 'Month',
          field: 'month_year',
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'RO Date',
          field: 'rodate',
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'totalsoldhours',
          chartDataType: 'category',
          // filter: 'agNumberColumnFilter',
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Available Hours',
          field: 'totalavailablehrs',
          chartDataType: 'category',
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Aeverage Hours Per Tech',
          field: 'averagehrspertech',
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      columnDefsLaborPartsByMonth: [
        {
          headerName: 'Month',
          field: 'monthYear',
          onCellClicked: this.handleCellClicked,
          unSortIcon: true,
          suppressMenu: true,
          cellRenderer: function(params) {
            if (params.value != undefined)
              return `<a style="cursor: pointer">${moment(params.value).format(
                'MM-YYYY'
              )}</a>`;
          },
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold'
            };
          },
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Total Labor Discount',
          field: 'totallabordiscount',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Total Parts Discount',
          field: 'totalpartsdiscount',
          chartDataType: 'category',
          width: 110,
          minWidth: 110,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Overall Discount',
          field: 'totaldiscount',
          chartDataType: 'category',
          width: 110,
          minWidth: 110,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      columnDefsDiscountedSalePercentage: [
        {
          headerName: 'Month',
          field: 'monthYear',
          onCellClicked: this.handleCellClicked,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold'
            };
          },
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          chartDataType: 'category',
          width: 110,
          minWidth: 110,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          suppressMenu: true,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true
        },
        {
          headerName: 'Labor Discount',
          field: 'labordiscountdollars',
          chartDataType: 'category',
          width: 110,
          minWidth: 110,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          suppressMenu: true,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'partssale',
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true
        },

        {
          headerName: 'Parts Discount',
          field: 'partsdiscountdollars',
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true
        },

        {
          headerName: 'Discounted Labor Sale %',
          field: 'discountedlabor',
          chartDataType: 'category',
          width: 90,
          minWidth: 90,
          flex: 1,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Discounted Parts Sale %',
          field: 'discountedparts',
          chartDataType: 'category',
          width: 90,
          minWidth: 90,
          flex: 1,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Overall Discount Sale %',
          field: 'percentagepertotalcpsale',
          chartDataType: 'category',
          width: 90,
          minWidth: 90,
          flex: 1,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      columnDefsDiscountsPerTotalCPROs: [
        {
          headerName: 'Month',
          field: 'monthYear',
          onCellClicked: this.handleCellClicked,
          unSortIcon: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold'
            };
          },
          width: 90,
          minWidth: 90,
          suppressMenu: true,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discounted Labor per CP RO',
          field: 'discountedlaborpercpro',
          chartDataType: 'category',
          width: 120,
          minWidth: 120,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Discounted Parts per CP RO',
          field: 'discountedpartspercpro',
          chartDataType: 'category',
          width: 120,
          minWidth: 120,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Overall Discount per CP RO',
          field: 'totaldiscountspercpro',
          chartDataType: 'category',
          width: 120,
          minWidth: 120,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      columnDefsDiscountsPerTotalDiscountedCPROs: [
        {
          headerName: 'Month',
          onCellClicked: this.handleCellClicked,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold'
            };
          },
          field: 'monthYear',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Discounted Labor per CP RO',
          field: 'discountedlaborperdiscountedro',
          chartDataType: 'category',
          width: 120,
          minWidth: 120,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Discounted Parts per CP RO',
          field: 'discountedpartsrperdiscountedro',
          chartDataType: 'category',
          width: 120,
          minWidth: 120,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Overall Discount per CP RO',
          field: 'totaldiscountsperdiscountedcpro',
          chartDataType: 'category',
          width: 120,
          minWidth: 120,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      columnDefsDiscountedCPPerTotalDiscountedCPROs: [
        {
          headerName: 'Month',
          onCellClicked: this.handleCellClicked,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold'
            };
          },
          field: 'monthYear',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Discount',
          field: 'labordiscountdollars',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts Discount',
          field: 'partsdiscountdollars',
          width: 110,
          minWidth: 110,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true,
          suppressMenu: true
        },
        {
          headerName: 'Discounted Labor %',
          field: 'discountedlabor',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          unSortIcon: true,
          suppressMenu: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Discounted Parts %',
          field: 'discountedparts',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          unSortIcon: true,
          suppressMenu: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Overall Discount %',
          field: 'discountpercentageperdiscountcpsale',
          chartDataType: 'category',
          width: 80,
          minWidth: 80,
          flex: 1,
          cellStyle: this.cellStyles,
          unSortIcon: true,
          suppressMenu: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      columnDefsShopSupplies: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Pay Type',
          // filter: 'agTextColumnFilter',
          field: 'lbrPaytype',
          width: realm == 'ferrarioat_store' ? 100 : 70,
          minWidth: realm == 'ferrarioat_store' ? 100 : 70,
          flex: 1,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Job Count',
          // filter: 'agNumberColumnFilter',
          field: 'jobcount',
          width: 70,
          minWidth: 70,
          flex: 1,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Labor Sale',
          // filter: 'agNumberColumnFilter',
          field: 'laborsale',
          width: 90,
          minWidth: 90,
          flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          chartDataType: 'category',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName:
            category == 5 ? 'Shop Supplies - Customer Pay' : 'Shop Supplies',
          field: 'customerpayshopsup',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          suppressToolPanel: category == 0 || category == 5 ? false : true,
          hide: category == 0 || category == 5 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName:
            category == 5 ? 'Shop Supplies - Internal' : 'Shop Supplies',
          field: 'internalshopsup',
          width: 100,
          minWidth: 100,
          flex: 1,
          chartDataType: 'category',
          suppressToolPanel: category == 4 || category == 5 ? false : true,
          hide: category == 4 || category == 5 ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          // filter: 'agNumberColumnFilter',
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true
        }
      ],
      columnDefsPartsMarkups: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          width: realm != 'haleyag' ? 83 : 110,
          minWidth: realm != 'haleyag' ? 83 : 110,
          flex: 1,
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer',
          cellClass: 'textAlign',
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          suppressMenu: true,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              left: '-35px',
              cursor: 'pointer'
            };
          }
        },
        // {
        //   headerName: 'Exclude',
        //   //field: 'lbropcode',
        //   width: 95,
        //   suppressCellFlash: true,
        //   chartDataType: 'category',
        //   cellRenderer: 'checkboxRenderer',
        //   unSortIcon: true,
        //   suppressMenu:true
        // },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 85,
          minWidth: 85,
          hide: true,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          unSortIcon: true,
          suppressMenu: true
          // filter: 'agDateColumnFilter',
          // filter: 'agDateColumnFilter',
          // filterParams: {
          //   browserDatePicker: true,
          //   comparator: function(filterLocalDateAtMidnight, cellValue) {
          //     var dateAsString = moment(cellValue).format('YYYY-MM-DD');
          //     if (dateAsString == null) return -1;

          //     var dateParts = dateAsString.split('-');
          //     var cellDate = new Date(
          //       Number(dateParts[0]),
          //       Number(dateParts[1]) - 1,
          //       Number(dateParts[2])
          //     );

          //     if (filterLocalDateAtMidnight.getTime() == cellDate.getTime()) {
          //       return 0;
          //     }

          //     if (cellDate < filterLocalDateAtMidnight) {
          //       return -1;
          //     }

          //     if (cellDate > filterLocalDateAtMidnight) {
          //       return 1;
          //     }
          //   }
          // }
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          // filter: 'agNumberColumnFilter',
          width: 145,
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts Code',
          // filter: 'agTextColumnFilter',
          field: 'prtlinecode',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true,
          hide:
            (drillDown == 1 ||
              drillDown == 3 ||
              drillDown == 4 ||
              drillDown == 5 ||
              drillDown == 47) &&
            (category == 0 || category == 2 || category == 11)
              ? false
              : true
        },
        {
          headerName: 'Pay Types',
          // filter: 'agTextColumnFilter',
          field: 'partspaytype',
          width: realm == 'ferrarioat_store' ? 100 : 90,
          minWidth: realm == 'ferrarioat_store' ? 100 : 90,
          flex: 1,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Pay Type Groups',
          field: 'partspaytypegroup',
          width: drillDown == 2 || drillDown == 7 ? 93 : 90,
          minWidth: drillDown == 2 || drillDown == 7 ? 93 : 90,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Opcode',
          // filter: 'agTextColumnFilter',
          field: 'lbropcode',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 97,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 97,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          flex: 1,
          resizable: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Op Category',
          // filter: 'agTextColumnFilter',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          flex: 1,
          // filter: 'agTextColumnFilter',
          dataType: 'string',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Seq No.',
          // filter: 'agNumberColumnFilter',
          field: 'prtsequenceno',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,
          hide: true,
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Markup',
          // filter: 'agNumberColumnFilter',
          field: 'markup',
          width: 95,
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts Sale',
          // filter: 'agNumberColumnFilter',
          field: 'prtextendedsale',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts Cost',
          // filter: 'agNumberColumnFilter',
          field: 'prtextendedcost',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true
        },
        {
          headerName: 'Parts GP',
          // filter: 'agNumberColumnFilter',
          field: 'prtsgrossprofit',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          unSortIcon: true,
          suppressMenu: true
          //   aggFunc: 'sum',
        },
        {
          headerName: 'Parts GP %',
          // filter: 'agNumberColumnFilter',
          field: 'prtsGrossprofitpercentage',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          unSortIcon: true,
          suppressMenu: true
        }
      ],
      context: { componentParent: this },
      frameworkComponents: {
        checkboxRenderer: CheckboxRenderer
      },
      rowData: [],
      filteredRowData: [],
      headerHeight: drillDown == 46 ? 60 : drillDown == 15 ? 62 : 45,
      autoGroupColumnDef: { minWidth: 200 },
      // onFilterChanged: function(e) {
      //   console.log('onFilterChanged', e);
      //   console.log('gridApi.getFilterModel() =>', e.api.getFilterModel());
      // },

      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        filter: true,
        // suppressMenu: true,
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filterParams: { newRowsAction: 'keep' },
        enableValue: true,
        //enableRowGroup: true,
        sortable: true,
        resizable: false,
        //enableFilter: true,
        enablePivot: false
        // filter: true
        //sort:'asc'
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 20,
            color: 'primary'
          }
          // alignment: {
          //   horizontal: 'Right',
          //   vertical: 'Center'
          // }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'DecimalPlacesWith$',
          numberFormat: { format: '$#,##0' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0.0' }
        },
        {
          id: 'twoDecimalPlacesDiscount',
          numberFormat: { format: '($#,##0.00)' }
        },
        {
          id: 'fourDecimalPlaces',
          numberFormat: { format: '###0.0000' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right'
    };
  };

  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    let filterArr;
    let rowData = this.state.rowData;

    let DrilldownValues;
    let pointer = this;
    if (!lodash.isEmpty(filterValues)) {
      Object.keys(filterValues).forEach(function(key) {
        if (filterValues[key].values.length > 0) {
          console.log('this==filterArr', rowData);
          filterArr = lodash.filter(rowData, function(o) {
            return filterValues[key].values.includes(o[key]);
          });
          rowData = filterArr;

          pointer.setState({ filteredRowData: rowData });
          DrilldownValues = myCustomSumAggregateRevisedTotals(
            rowData,
            pointer.state.chartId
          );
          pointer.setDrillDownValuesToStateRevisedTotals(DrilldownValues);

          pointer.setState({
            isColumnFiltered: true
          });
        }
      });
      // let DrilldownValues = myCustomSumAggregate(rowData, this.state.chartId);
      // this.setDrillDownValuesToState(DrilldownValues);
    }
    /* else {
      this.setState({ filteredRowData: rowData });
      let DrilldownValues = myCustomSumAggregate(rowData, this.state.chartId);
      this.setDrillDownValuesToState(DrilldownValues);
      this.setState({
        isColumnFiltered: false
      });
    }*/
  };

  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };

  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  formatCellValueElr = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' + parseFloat(params.value)
        : '-$' + Math.abs(parseFloat(params.value));
    } else {
      return '$0';
    }
  };

  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        parseFloat(Math.abs(params.value))
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        ')'
      );
    } else {
      return '($0.00)';
    }
  };

  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };

  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };

  handleSearchByRo = params => {
    window.sortState = this.gridApi.getSortModel();
    window.colState = this.state.gridColumnApi.getColumnState();
    window.filterState = this.gridApi.getFilterModel();
    const selectedTab = this.props.history.location.state
      ? this.props.history.location.state.tabSelection
      : '';

    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        drillDown: this.state.drillDown,
        prevPath: this.state.previousLocation,
        chartId: this.state.chartId,
        x: this.state.serviceAdvisor,
        y: this.state.queryMonth,
        chartName: this.state.chartName,
        category: this.state.category,
        pageType: 'drillDown',
        discountMonth: this.state.discountMonth,
        discountId: this.state.discountId,
        discountServiceAdvisor: this.state.discountServiceAdvisor,
        tabSelection: selectedTab,
        storeId: localStorage.getItem('storeIdSelected')
          ? localStorage.getItem('storeIdSelected')
          : this.state.storeId
      }
    });
  };

  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(1)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.0';
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };

  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: this.state.chartName.trim(),
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: this.state.chartName },
            mergeAcross: 6
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  onGridReady = params => {
    // params.api.closeToolPanel();
    this.gridApi = params.api;
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    if (this.state.isFrom == '') {
      window.sortState = {};
      window.filterState = {};
    }
    this.gridApi.setSortModel(window.sortState);
    this.gridApi.setFilterModel(window.filterState);
    if (window.colState) {
      this.state.gridColumnApi.setColumnState(window.colState);
    }
    if (window.colState && this.state.isFrom == '') {
      this.state.gridColumnApi.resetColumnState();
    }
    /* if (
      this.state.chartId == 1318 ||
      this.state.chartId == 918 ||
      this.state.chartId == 953 ||
      this.state.chartId == 916 ||
      this.state.chartId == 1125 ||
      this.state.chartId == 1123 ||
      this.state.chartId == 1124 ||
      this.state.chartId == 1126 ||
      this.state.chartId == 1132 ||
      this.state.chartId == 753 ||
      this.state.chartId == 1080 ||
      this.state.chartId == 1081 ||
      this.state.chartId == 1082 ||
      this.state.chartId == 998 ||
      this.state.chartId == 1083 ||
      this.state.chartId == 1084 ||
      this.state.chartId == 1085 ||
      this.state.chartId == 1086 ||
      this.state.chartId == 1087 ||
      this.state.chartId == 1088 ||
      this.state.chartId == 1001 ||
      this.state.chartId == 457 ||
      this.state.chartId == 456 ||
      this.state.chartId == 1028 ||
      this.state.chartId == 1029 ||
      this.state.chartId == 1030 ||
      this.state.chartId == 1012 ||
      this.state.chartId == 1015 ||
      this.state.chartId == 1014 ||
      this.state.chartId == 1013 ||
      this.state.chartId == 1091 ||
      this.state.chartId == 1092 ||
      this.state.chartId == 1093 ||
      this.state.chartId == 1094 ||
      this.state.chartId == 1174 ||
      this.state.drillDown == 43 ||
      this.state.drillDown == 44
    ) {
      this.gridApi.sizeColumnsToFit();
    }*/

    this.getAgGridData(
      this.state.queryMonth,
      this.state.serviceAdvisor,
      this.state.drillDownServiceAdvisor
    );
  };
  parseArray(filtererdArray) {
    let intArray = [];
    var res = filtererdArray.map(v => {
      intArray.push({
        closeddate: v.closeddate,
        filterByLaborparts: v.filterByLaborparts,
        filterByRevenue: v.filterByRevenue,
        issinglejob: v.issinglejob,
        serviceadvisor: parseFloat(v.serviceadvisor) || 0,
        lbrGrossprofitpercentage: v.lbrGrossprofitpercentage,
        lbrlinecode: v.lbrlinecode,
        lbropcode: v.lbropcode,
        lbropcodedesc: v.lbropcodedesc,
        lbrsequenceno: v.lbrsequenceno,
        monthYear: v.monthYear,
        opcategory: v.opcategory,
        opsubcategory: v.opsubcategory,
        paytype: v.paytype,
        paytypegroup: v.paytypegroup,
        ronumber: v.ronumber,
        lbrtechno: v.lbrtechno,
        elr: parseFloat(v['elr']) || 0,
        lbrcost: parseFloat(v['lbrcost']) || 0,
        lbrgrossprofit: parseFloat(v['lbrgrossprofit']) || 0,
        lbrsale: parseFloat(v['lbrsale']) || 0,
        lbrsoldhours: parseFloat(v['lbrsoldhours']) || 0,
        lbrtechhours: parseFloat(v['lbrtechhours']) || 0,
        markup: parseFloat(v['markup']) || 0,
        prtextendedcost: parseFloat(v['prtextendedcost']) || 0,
        prtextendedsale: parseFloat(v['prtextendedsale']) || 0,
        prtsGrossprofitpercentage:
          parseFloat(v['prtsGrossprofitpercentage']) || 0,
        prtsgrossprofit: parseFloat(v['prtsgrossprofit'] || 0),
        serviceadvisor: v.serviceadvisor,
        vin: v.vin,
        linaddonflag: v.linaddonflag,
        openMonth: v.openMonth,
        opendate: v.opendate,
        lbrPaytype: v.lbrPaytype,
        jobcount: v.jobcount,
        laborsale: parseFloat(v['laborsale']) || 0,
        customerpayshopsup: parseFloat(v['customerpayshopsup']) || 0,
        internalshopsup: parseFloat(v['internalshopsup']) || 0,
        disdiscountid: v.disdiscountid,
        dislevel: v.dislevel,
        salepercentage: parseFloat(v['salepercentage']) || 0,
        apportionedlbrdiscount: parseFloat(v['apportionedlbrdiscount']) || 0,
        laborpaytypegroup: v.laborpaytypegroup,
        prtlinecode: v.prtlinecode,
        prtcost: parseFloat(v.prtcost) || 0,
        prtssale: parseFloat(v.prtssale) || 0,
        totaldiscount: parseFloat(v.totaldiscount) || 0,
        totalpartsdiscount: parseFloat(v.totalpartsdiscount) || 0,
        totallabordiscount: parseFloat(v.totallabordiscount) || 0,
        prtsale: parseFloat(v.prtsale) || 0,
        partsdiscount: parseFloat(v.partsdiscount) || 0,
        labordiscount: parseFloat(v.labordiscount) || 0,
        discountedlabor: parseFloat(v.discountedlabor) || 0,
        discountedparts: parseFloat(v.discountedparts) || 0,
        partsdiscountdollars: parseFloat(v.partsdiscountdollars) || 0,
        labordiscountdollars: parseFloat(v.labordiscountdollars) || 0,
        partssale: parseFloat(v.partssale) || 0,
        percentagepertotalcpsale: parseFloat(v.percentagepertotalcpsale) || 0,
        discountpercentageperdiscountcpsale:
          parseFloat(v.discountpercentageperdiscountcpsale) || 0,
        discountedlaborpercpro: parseFloat(v.discountedlaborpercpro) || 0,
        discountedlaborperdiscountedro:
          parseFloat(v.discountedlaborperdiscountedro) || 0,
        discountedpartspercpro: parseFloat(v.discountedpartspercpro) || 0,
        discountedpartsrperdiscountedro:
          parseFloat(v.discountedpartsrperdiscountedro) || 0,
        totaldiscountspercpro: parseFloat(v.totaldiscountspercpro) || 0,
        totaldiscountsperdiscountedcpro:
          parseFloat(v.totaldiscountsperdiscountedcpro) || 0,
        disdesc: v.disdesc,
        laborpaytype: v.laborpaytype,
        prtlaborsequenceno: v.prtlaborsequenceno,
        lbrlabortype: v.lbrlabortype,
        partspaytype: v.partspaytype,
        partspaytypegroup: v.partspaytypegroup,
        prtsequenceno: v.prtsequenceno,
        advisorName: v.advisorName
      });
      // return res;
    });
    return intArray;
  }

  getFilters = advisors => {
    let combiner = `'{`;
    advisors.map((each, index) => {
      combiner = combiner + each + ',';
    });
    combiner = combiner.substring(0, combiner.length - 1) + `}'`;

    return combiner;
  };
  // autoSizeColumns = params => {
  //   const colIds = params.columnApi
  //     .getAllDisplayedColumns()
  //     .map(col => col.getColId());

  //   params.columnApi.autoSizeColumns(colIds);
  // }
  filterByPayTypeGroup = () => {
    var payTypeFilterComponent = this.state.rawGridApi.getFilterInstance(
      'paytypegroup'
    )
      ? this.state.rawGridApi.getFilterInstance('paytypegroup')
      : this.state.rawGridApi.getFilterInstance('lbrPaytype')
      ? this.state.rawGridApi.getFilterInstance('lbrPaytype')
      : this.state.rawGridApi.getFilterInstance('partspaytypegroup');

    if (
      this.state.chartId == 1175 ||
      this.state.chartId == 968 ||
      this.state.chartId == 1007 ||
      this.state.chartId == 1074 ||
      this.state.chartId == 1134 ||
      this.state.chartId == 1139 ||
      this.state.chartId == 1001 ||
      this.state.chartId == 1028 ||
      this.state.chartId == 1144 ||
      this.state.chartId == 1319 ||
      (this.state.drillDown == 38 && this.state.category == 5) ||
      (this.state.drillDown == 39 && this.state.category == 5) ||
      this.state.chartId == 1327 ||
      this.state.chartId == 1225
    ) {
      this.setState({
        selectedpayTypeFilter1: '',
        selectedpayTypeFilter2: '',
        selectedpayTypeFilter3: ''
      });
    }
    payTypeFilterComponent.setModel({
      condition1: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter1
      },
      condition2: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter2
      },
      condition3: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter3
      },
      operator: 'OR'
    });
    this.state.rawGridApi.onFilterChanged();
  };

  filterByOpCategory = () => {
    var opCatFilterComponent = this.state.rawGridApi.getFilterInstance(
      'opcategory'
    );
    opCatFilterComponent.setModel({
      condition1: {
        type: 'startsWith',
        filter: this.state.selectedOpCategoryFilter1
      },
      condition2: {
        type: 'startsWith',
        filter: this.state.selectedOpCategoryFilter2
      },
      condition3: {
        type: 'startsWith',
        filter: this.state.selectedOpCategoryFilter3
      },
      operator: 'OR'
    });
    this.state.rawGridApi.onFilterChanged();
  };

  filterByOpCode = () => {
    var opcodeFilterComponent = this.state.rawGridApi.getFilterInstance(
      'lbropcode'
    );

    opcodeFilterComponent.setModel({
      filterType: 'text',
      type: 'Equals',
      filter: 'MPVI'
    });

    this.state.rawGridApi.onFilterChanged();
  };

  filterByAddOns = () => {
    var AddonFilterComponent = this.state.rawGridApi.getFilterInstance(
      'linaddonflag'
    );

    AddonFilterComponent.setModel({
      condition1: {
        type: 'startsWith',
        filter: 'Y'
      },
      condition2: {
        type: 'startsWith',
        filter: 'Y'
      },

      operator: 'OR'
    });

    this.state.rawGridApi.onFilterChanged();
  };
  filterByNonAddOns = () => {
    var AddonFilterComponent = this.state.rawGridApi.getFilterInstance(
      'linaddonflag'
    );

    AddonFilterComponent.setModel({
      condition1: {
        type: 'startsWith',
        filter: 'N'
      },
      condition2: {
        type: 'startsWith',
        filter: 'N'
      },

      operator: 'OR'
    });

    this.state.rawGridApi.onFilterChanged();
  };

  intersection = (first, second) => {
    const filtered = second.filter(key => {
      if (first.includes(key.ronumber)) {
        return key;
      }
    });
    return filtered;
  };
  filterMenuOpcodes = second => {
    var menuOpcodesArr = [
      '60K',
      '100KT8',
      '100KC4',
      '100K6',
      '45KT',
      '100K8',
      '100K4',
      '15K',
      '30K',
      '45K',
      '30KA',
      '90K',
      '100KT6',
      '100KT4'
    ];
    const filtered = second.filter(key => {
      if (menuOpcodesArr.includes(key.lbropcode)) {
        return key;
      }
    });
    return filtered;
  };

  setDrillDownValuesToState = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      laborSale: Values.laborSale,
      LsWarranty: Values.LsWarranty,
      LsInternal: Values.LsInternal,
      saleRepair: Values.saleRepair,
      saleMaintenance: Values.saleMaintenance,
      saleCompetitive: Values.saleCompetitive,
      saleRepairCompetitive: Values.saleRepairCompetitive,
      saleAllCategories: Values.saleAllCategories,
      saleShopSupplies: Values.saleShopSupplies,
      lbrSaleperRoR: Values.lbrSaleperRoR,
      lbrSaleperRoM: Values.lbrSaleperRoM,
      lbrSaleperRoC: Values.lbrSaleperRoC,
      lbrSaleperRoS: Values.lbrSaleperRoS,
      lbrSaleperRoAll: Values.lbrSaleperRoAll,
      lbrHoursPerRO: Values.lbrHoursPerRO,
      laborCostSum: Values.laborCostSum,
      LcWarranty: Values.LcWarranty,
      LcInternal: Values.LcInternal,
      elrRepair: Values.elrRepair,
      elrMaintenance: Values.elrMaintenance,
      elrCompetitive: Values.elrCompetitive,
      elrRepairCompetitive: Values.elrRepairCompetitive,
      elrAllCategories: Values.elrAllCategories,
      laborHoursSum: Values.laborHoursSum,
      ShWarranty: Values.ShWarranty,
      ShInternal: Values.ShInternal,
      lbrTechHr: Values.lbrTechHr,
      hoursRepair: Values.hoursRepair,
      hoursMaintenance: Values.hoursMaintenance,
      hoursCompetitive: Values.hoursCompetitive,
      hoursRepairCompetitive: Values.hoursRepairCompetitive,
      hoursAllCategories: Values.hoursAllCategories,
      jobCountR: Values.jobCountR,
      jobCountM: Values.jobCountM,
      jobCountCO: Values.jobCountCO,
      jobCountS: Values.jobCountS,
      lbrHrsPerRoR: Values.lbrHrsPerRoR,
      lbrHrsPerRoM: Values.lbrHrsPerRoM,
      lbrHrsPerRoC: Values.lbrHrsPerRoC,
      lbrHrsPerRoAll: Values.lbrHrsPerRoAll,
      lbrHoursPerJob: Values.lbrHoursPerJob,
      partsSaleSum: Values.partsSaleSum,
      partsSale: Values.partsSale,
      PsWarranty: Values.PsWarranty,
      PsInternal: Values.PsInternal,
      partsCostSum: Values.partsCostSum,
      PcWarranty: Values.PcWarranty,
      PcInternal: Values.PcInternal,
      laborGP: Values.laborGP,
      laborGPW: Values.laborGPW,
      laborGPI: Values.laborGPI,
      laborGPCombined: Values.laborGPCombined,
      laborSaleCombined: Values.laborSaleCombined,
      laborCostCombined: Values.laborCostCombined,
      partsMarkup: Values.partsMarkup,
      partsGP: Values.partsGP,
      laborGPPerc: Values.laborGPPerc,
      partsGPPerc: Values.partsGPPerc,
      laborSaleSumCombined: Values.laborSaleSumCombined,
      laborPercetSumCombined: Values.laborPercetSumCombined,
      lsGrossSumCombined: Values.lsGrossSumCombined,
      elr: Values.elr,
      LsCombined: Values.LsCombined,
      lbrSaleRepairCompet: Values.lbrSaleRepairCompet,
      lbrHrsRepairCompet: Values.lbrHrsRepairCompet,
      elrRepairCompet: Values.elrRepairCompet,
      psSumComp: Values.psSumComp,
      pcSumComp: Values.pcSumComp,
      prtGpWar: Values.prtGpWar,
      prtGpInt: Values.prtGpInt,
      prtGpCombin: Values.prtGpCombin,
      prtsaleR: Values.prtsaleR,
      prtsaleM: Values.prtsaleM,
      prtsaleComp: Values.prtsaleComp,
      prtcostR: Values.prtcostR,
      prtcostM: Values.prtcostM,
      prtcostComp: Values.prtcostComp,
      partsMarkupR: Values.partsMarkupR,
      partsMarkupM: Values.partsMarkupM,
      partsMarkupC: Values.partsMarkupC,
      partsMarkupCmb: Values.partsMarkupCmb,
      prtsaleM: Values.prtsaleM,
      prtsaleR: Values.prtsaleR,
      prtsaleCmb: Values.prtsaleCmb,
      prtsaleAll: Values.prtsaleAll,
      prtcostAll: Values.prtcostAll,
      prtcostCmb: Values.prtcostCmb,
      lbrSaleC: Values.lbrSaleC,
      lbrCostC: Values.lbrCostC,
      distinctRO: Values.distinctRO,
      distinctROW: Values.distinctROW,
      distinctROI: Values.distinctROI,
      distinctROC: Values.distinctROC,
      distinctROR: Values.distinctROR,
      distinctROM: Values.distinctROM,
      distinctROComp: Values.distinctROComp,
      distinctROCmb: Values.distinctROCmb,
      lbrSaleperRo: Values.lbrSaleperRo,
      prtSaleperRo: Values.prtSaleperRo,
      jobCount: Values.jobCount,
      prtRoByM: Values.prtRoByM,
      prtRoByR: Values.prtRoByR,
      prtRoByComp: Values.prtRoByComp,
      prtRoByCmb: Values.prtRoByCmb,
      partsToLaborRatio: Values.partsToLaborRatio,
      distinctROPercent: Values.distinctROPercent,
      prtsToLbrRatioR: Values.prtsToLbrRatioR,
      prtsToLbrRatioM: Values.prtsToLbrRatioM,
      prtsToLbrRatioC: Values.prtsToLbrRatioC,
      sHByCategoryC: Values.sHByCategoryC,
      sHByCategoryE: Values.sHByCategoryE,
      sHByCategoryW: Values.sHByCategoryW,
      sHByCategoryI: Values.sHByCategoryI,
      sHByCategoryM: Values.sHByCategoryM,
      deltaThFh: Values.deltaThFh,
      deltaShFh: Values.deltaShFh,
      techHours: Values.techHours,
      flatrateHours: Values.flatrateHours,
      distinctCPPROR: Values.distinctCPPROR,
      distinctCPPROM: Values.distinctCPPROM,
      distinctCPPROComp: Values.distinctCPPROComp,
      roCountR: Values.roCountR,
      RoCountAll: Values.RoCountAll,
      jobCountAddOn: Values.jobCountAddOn,
      jobCountNonAddOn: Values.jobCountNonAddOn,
      jobCountAddOnPerRo: Values.jobCountAddOnPerRo,
      ROCountPercAddOn: Values.ROCountPercAddOn,
      distinctROCountNonAddOn: Values.distinctROCountNonAddOn,
      distinctROCountAddOn: Values.distinctROCountAddOn,
      lbrSoldhrsAddon: Values.lbrSoldhrsAddon,
      lbrSoldhrsNonAddon: Values.lbrSoldhrsNonAddon,
      lbrSaleAddOn: Values.lbrSaleAddOn,
      prtSaleAddOn: Values.prtSaleAddOn,
      totalRevAddon: Values.totalSaleAddOn,
      jobCountAddOnR: Values.jobCountAddOnR,
      jobCountAddOnM: Values.jobCountAddOnM,
      jobCountAddOnC: Values.jobCountAddOnC,
      jobCountAddOnS: Values.jobCountAddOnS,
      lbrSaleRAddon: Values.lbrSaleRAddOn,
      lbrSaleCAddon: Values.lbrSaleCAddOn,
      lbrSaleMAddon: Values.lbrSaleMAddOn,
      lbrSaleSAddOn: Values.lbrSaleSAddOn,
      prtSaleRAddon: Values.prtSaleRAddOn,
      prtSaleCAddon: Values.prtSaleCAddOn,
      prtSaleMAddon: Values.prtSaleMAddOn,
      prtSaleSAddOn: Values.prtSaleSAddOn,
      elrRepairAddOn: Values.elrRepairAddOn,
      elrMaintenanceAddOn: Values.elrMaintenanceAddOn,
      elrCompetitiveAddOn: Values.elrCompetitiveAddOns,
      elrShopAddOn: Values.elrShopAddOn,
      MarkupR: Values.MarkupR,
      MarkupS: Values.MarkupS,
      MarkupC: Values.MarkupC,
      MarkupM: Values.MarkupM,
      addOnRevenueperc: Values.addOnRevenueperc,
      nonAddOnRevenueperc: Values.nonAddOnRevenueperc,
      lbrHoursAll: Values.lbrHoursAll,
      roCountAllCat: Values.roCountAllCat,
      roCountAll: Values.roCountAll,
      roCountShop: Values.roCountShop,
      lbrDiscount: Values.lbrDiscount,
      prtsDiscount: Values.prtsDiscount,
      roDiscount: Values.roDiscount,
      lineDiscount: Values.lineDiscount,
      discountJob: Values.discountJob,
      discountJobcount: Values.discountJobcount,
      discountROPerc: Values.discountROPerc,
      jobCountAllR: Values.jobCountAllR,
      jobCountAllM: Values.jobCountAllM,
      jobCountAllC: Values.jobCountAllC,
      jobCountAllS: Values.jobCountAllS,
      lbrHrsPerRoRPrcnt: Values.lbrHrsPerRoRPrcnt,
      lbrHrsPerRoMPrcnt: Values.lbrHrsPerRoMPrcnt,
      lbrHrsPerRoCPrcnt: Values.lbrHrsPerRoCPrcnt,
      distinctRORS: Values.distinctRORS,
      distinctROMS: Values.distinctROMS,
      distinctROCompS: Values.distinctROCompS,
      distinctROSS: Values.distinctROSS,
      distinctROCmbPerRo: Values.distinctROCmbPerRo,
      totallbrDiscount: Values.totallbrDiscount,
      totalprtsDiscount: Values.totalprtsDiscount,
      percLbrDiscount: Values.percLbrDiscount,
      totalPercDiscount: Values.totalPercDiscount,
      percPartsDiscount: Values.percPartsDiscount,
      ronumberArrJobs: Values.ronumberArrJobs,
      costMaintenance: Values.costMaintenance,
      saleMaintenance1: Values.saleMaintenance1,
      shMaintenance1: Values.shMaintenance1,
      saleExtended: Values.saleExtended,
      costExtended: Values.costExtended,
      shExtended: Values.shExtended,
      roMaintenance: Values.roMaintenance,
      roExtended: Values.roExtended,
      laborGPMP: Values.laborGPMP,
      laborGPE: Values.laborGPE,
      PsMaintenanceP: Values.PsMaintenanceP,
      PcMaintenanceP: Values.PcMaintenanceP,
      PsExtended: Values.PsExtended,
      PcExtended: Values.PcExtended,
      prtGpMP: Values.prtGpMP,
      prtGpE: Values.prtGpE,
      sHByCategoryF: Values.sHByCategoryF,
      saleFactory: Values.saleFactory,
      costFactory: Values.costFactory,
      shFactory: Values.shFactory,
      roFactory: Values.roFactory,
      laborGPF: Values.laborGPF,
      PsFactory: Values.PsFactory,
      PcFactory: Values.PcFactory,
      prtGpF: Values.prtGpF,
      costRepair: Values.costRepair,
      costMaintenances: Values.costMaintenances,
      costCompetitive: Values.costCompetitive,
      costShop: Values.costShop,
      SHByCategory: Values.SHByCategory,
      soldHoursTech: Values.soldHoursTech,
      availableHours: Values.availableHours,
      avgHrsTech: Values.avgHrsTech,
      techCount: Values.techCount,
      avgSalesPerTech: Values.avgSalesPerTech,
      totalSaleTech: Values.totalSaleTech,
      totalSale: Values.totalSale,
      ElrCombined: Values.ElrCombined,
      ElrCustomer: Values.ElrCustomer,
      ElrWarranty: Values.ElrWarranty,
      ElrInternal: Values.ElrInternal,
      ElrMaintenance: Values.ElrMaintenance,
      ElrExtended: Values.ElrExtended,
      ElrFactory: Values.ElrFactory,
      LHROCombined: Values.LHROCombined,
      LHROCustomer: Values.LHROCustomer,
      LHROWarranty: Values.LHROWarranty,
      LHROInteranl: Values.LHROInteranl,
      LHROMaintain: Values.LHROMaintain,
      LHROExtended: Values.LHROExtended,
      LHROFactory: Values.LHROFactory,
      LSaleROCombined: Values.LSaleROCombined,
      LSaleROCustomer: Values.LSaleROCustomer,
      LSaleROWarranty: Values.LSaleROWarranty,
      LSaleROInteranl: Values.LSaleROInteranl,
      LSaleROMaintain: Values.LSaleROMaintain,
      LSaleROExtended: Values.LSaleROExtended,
      LSaleROFactory: Values.LSaleROFactory,
      JobCntCombined: Values.JobCntCombined,
      JobCntCustomer: Values.JobCntCustomer,
      JobCntWarranty: Values.JobCntWarranty,
      JobCntInteranl: Values.JobCntInteranl,
      JobCntMaintain: Values.JobCntMaintain,
      JobCntExtended: Values.JobCntExtended,
      JobCntFactory: Values.JobCntFactory,
      prtsRevenueRoCombined: Values.prtsRevenueRoCombined,
      prtsRevenueRoCustomer: Values.prtsRevenueRoCustomer,
      prtsRevenueRoWarranty: Values.prtsRevenueRoWarranty,
      prtsRevenueRoInteranl: Values.prtsRevenueRoInteranl,
      prtsRevenueRoMaintain: Values.prtsRevenueRoMaintain,
      prtsRevenueRoExtended: Values.prtsRevenueRoExtended,
      prtsRevenueRoFactory: Values.prtsRevenueRoFactory,
      prtsMarkupCombined: Values.prtsMarkupCombined,
      prtsMarkupCustomer: Values.prtsMarkupCustomer,
      prtsMarkupWarranty: Values.prtsMarkupWarranty,
      prtsMarkupInteranl: Values.prtsMarkupInteranl,
      prtsMarkupMaintain: Values.prtsMarkupMaintain,
      prtsMarkupExtended: Values.prtsMarkupExtended,
      prtsMarkupFactory: Values.prtsMarkupFactory,
      shopSuppliesC: Values.shopSuppliesC,
      shopSuppliesCombined: Values.shopSuppliesCombined,
      shopSuppliesI: Values.shopSuppliesI,
      mpiCount: Values.mpiCount,
      totalROCount: Values.totalROCount,
      mpiPercentage: Values.mpiPercentage,
      menuCount: Values.menuCount,
      menuPercentage: Values.menuPercentage,
      prtsHoursPerRO: Values.prtsHoursPerRO,
      prtsHours: Values.prtsHours,
      prtsHrsRoCount: Values.prtsHrsRoCount,
      distinctROPartsOnly: Values.distinctROPartsOnly,
      partsMarkupRC: Values.partsMarkupRC
    });
  };

  setDrillDownValuesToStateRevisedTotals = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      laborSale: Values.laborSale,
      LsWarranty: Values.LsWarranty,
      LsInternal: Values.LsInternal,
      saleRepair: Values.saleRepair,
      saleMaintenance: Values.saleMaintenance,
      saleCompetitive: Values.saleCompetitive,
      saleRepairCompetitive: Values.saleRepairCompetitive,
      saleAllCategories: Values.saleAllCategories,
      saleShopSupplies: Values.saleShopSupplies,
      lbrSaleperRoR: Values.lbrSaleperRoR,
      lbrSaleperRoM: Values.lbrSaleperRoM,
      lbrSaleperRoC: Values.lbrSaleperRoC,
      lbrSaleperRoS: Values.lbrSaleperRoS,
      lbrSaleperRoAll: Values.lbrSaleperRoAll,
      lbrHoursPerRO: Values.lbrHoursPerRO,
      laborCostSum: Values.laborCostSum,
      LcWarranty: Values.LcWarranty,
      LcInternal: Values.LcInternal,
      elrRepair: Values.elrRepair,
      elrMaintenance: Values.elrMaintenance,
      elrCompetitive: Values.elrCompetitive,
      elrRepairCompetitive: Values.elrRepairCompetitive,
      elrAllCategories: Values.elrAllCategories,
      laborHoursSum: Values.laborHoursSum,
      ShWarranty: Values.ShWarranty,
      ShInternal: Values.ShInternal,
      lbrTechHr: Values.lbrTechHr,
      hoursRepair: Values.hoursRepair,
      hoursMaintenance: Values.hoursMaintenance,
      hoursCompetitive: Values.hoursCompetitive,
      hoursRepairCompetitive: Values.hoursRepairCompetitive,
      hoursAllCategories: Values.hoursAllCategories,
      jobCountR: Values.jobCountR,
      jobCountM: Values.jobCountM,
      jobCountCO: Values.jobCountCO,
      jobCountS: Values.jobCountS,
      lbrHrsPerRoR: Values.lbrHrsPerRoR,
      lbrHrsPerRoM: Values.lbrHrsPerRoM,
      lbrHrsPerRoC: Values.lbrHrsPerRoC,
      lbrHrsPerRoAll: Values.lbrHrsPerRoAll,
      lbrHoursPerJob: Values.lbrHoursPerJob,
      revPartsSaleSum: Values.revPartsSaleSum,
      partsSale: Values.partsSale,
      PsWarranty: Values.PsWarranty,
      PsInternal: Values.PsInternal,
      revPartsCostSum: Values.revPartsCostSum,
      PcWarranty: Values.PcWarranty,
      PcInternal: Values.PcInternal,
      laborGP: Values.laborGP,
      laborGPW: Values.laborGPW,
      laborGPI: Values.laborGPI,
      laborGPCombined: Values.laborGPCombined,
      laborSaleCombined: Values.laborSaleCombined,
      laborCostCombined: Values.laborCostCombined,
      partsMarkup: Values.partsMarkup,
      partsGP: Values.partsGP,
      laborGPPerc: Values.laborGPPerc,
      partsGPPerc: Values.partsGPPerc,
      laborSaleSumCombined: Values.laborSaleSumCombined,
      laborPercetSumCombined: Values.laborPercetSumCombined,
      lsGrossSumCombined: Values.lsGrossSumCombined,
      elr: Values.elr,
      LsCombined: Values.LsCombined,
      lbrSaleRepairCompet: Values.lbrSaleRepairCompet,
      lbrHrsRepairCompet: Values.lbrHrsRepairCompet,
      elrRepairCompet: Values.elrRepairCompet,
      psSumComp: Values.psSumComp,
      pcSumComp: Values.pcSumComp,
      prtGpWar: Values.prtGpWar,
      prtGpInt: Values.prtGpInt,
      prtGpCombin: Values.prtGpCombin,
      prtsaleR: Values.prtsaleR,
      prtsaleM: Values.prtsaleM,
      prtsaleComp: Values.prtsaleComp,
      prtcostR: Values.prtcostR,
      prtcostM: Values.prtcostM,
      prtcostComp: Values.prtcostComp,
      partsMarkupR: Values.partsMarkupR,
      partsMarkupM: Values.partsMarkupM,
      partsMarkupC: Values.partsMarkupC,
      partsMarkupCmb: Values.partsMarkupCmb,
      prtsaleM: Values.prtsaleM,
      prtsaleR: Values.prtsaleR,
      prtsaleCmb: Values.prtsaleCmb,
      prtsaleAll: Values.prtsaleAll,
      prtcostAll: Values.prtcostAll,
      prtcostCmb: Values.prtcostCmb,
      lbrSaleC: Values.lbrSaleC,
      lbrCostC: Values.lbrCostC,
      distinctRO: Values.distinctRO,
      distinctROW: Values.distinctROW,
      distinctROI: Values.distinctROI,
      distinctROC: Values.distinctROC,
      distinctROR: Values.distinctROR,
      distinctROM: Values.distinctROM,
      distinctROComp: Values.distinctROComp,
      distinctROCmb: Values.distinctROCmb,
      lbrSaleperRo: Values.lbrSaleperRo,
      prtSaleperRo: Values.prtSaleperRo,
      jobCount: Values.jobCount,
      prtRoByM: Values.prtRoByM,
      prtRoByR: Values.prtRoByR,
      prtRoByComp: Values.prtRoByComp,
      prtRoByCmb: Values.prtRoByCmb,
      partsToLaborRatio: Values.partsToLaborRatio,
      distinctROPercent: Values.distinctROPercent,
      prtsToLbrRatioR: Values.prtsToLbrRatioR,
      prtsToLbrRatioM: Values.prtsToLbrRatioM,
      prtsToLbrRatioC: Values.prtsToLbrRatioC,
      sHByCategoryC: Values.sHByCategoryC,
      sHByCategoryE: Values.sHByCategoryE,
      sHByCategoryW: Values.sHByCategoryW,
      sHByCategoryI: Values.sHByCategoryI,
      sHByCategoryM: Values.sHByCategoryM,
      deltaThFh: Values.deltaThFh,
      deltaShFh: Values.deltaShFh,
      techHours: Values.techHours,
      flatrateHours: Values.flatrateHours,
      distinctCPPROR: Values.distinctCPPROR,
      distinctCPPROM: Values.distinctCPPROM,
      distinctCPPROComp: Values.distinctCPPROComp,
      roCountR: Values.roCountR,
      RoCountAll: Values.RoCountAll,
      jobCountAddOn: Values.jobCountAddOn,
      jobCountNonAddOn: Values.jobCountNonAddOn,
      jobCountAddOnPerRo: Values.jobCountAddOnPerRo,
      ROCountPercAddOn: Values.ROCountPercAddOn,
      distinctROCountNonAddOn: Values.distinctROCountNonAddOn,
      distinctROCountAddOn: Values.distinctROCountAddOn,
      lbrSoldhrsAddon: Values.lbrSoldhrsAddon,
      lbrSoldhrsNonAddon: Values.lbrSoldhrsNonAddon,
      lbrSaleAddOn: Values.lbrSaleAddOn,
      prtSaleAddOn: Values.prtSaleAddOn,
      totalRevAddon: Values.totalSaleAddOn,
      jobCountAddOnR: Values.jobCountAddOnR,
      jobCountAddOnM: Values.jobCountAddOnM,
      jobCountAddOnC: Values.jobCountAddOnC,
      jobCountAddOnS: Values.jobCountAddOnS,
      lbrSaleRAddon: Values.lbrSaleRAddOn,
      lbrSaleCAddon: Values.lbrSaleCAddOn,
      lbrSaleMAddon: Values.lbrSaleMAddOn,
      lbrSaleSAddOn: Values.lbrSaleSAddOn,
      prtSaleRAddon: Values.prtSaleRAddOn,
      prtSaleCAddon: Values.prtSaleCAddOn,
      prtSaleMAddon: Values.prtSaleMAddOn,
      prtSaleSAddOn: Values.prtSaleSAddOn,
      elrRepairAddOn: Values.elrRepairAddOn,
      elrMaintenanceAddOn: Values.elrMaintenanceAddOn,
      elrCompetitiveAddOn: Values.elrCompetitiveAddOns,
      elrShopAddOn: Values.elrShopAddOn,
      MarkupR: Values.MarkupR,
      MarkupS: Values.MarkupS,
      MarkupC: Values.MarkupC,
      MarkupM: Values.MarkupM,
      addOnRevenueperc: Values.addOnRevenueperc,
      nonAddOnRevenueperc: Values.nonAddOnRevenueperc,
      lbrHoursAll: Values.lbrHoursAll,
      roCountAllCat: Values.roCountAllCat,
      roCountAll: Values.roCountAll,
      roCountShop: Values.roCountShop,
      lbrDiscount: Values.lbrDiscount,
      prtsDiscount: Values.prtsDiscount,
      roDiscount: Values.roDiscount,
      lineDiscount: Values.lineDiscount,
      discountJob: Values.discountJob,
      discountJobcount: Values.discountJobcount,
      discountROPerc: Values.discountROPerc,
      jobCountAllR: Values.jobCountAllR,
      jobCountAllM: Values.jobCountAllM,
      jobCountAllC: Values.jobCountAllC,
      jobCountAllS: Values.jobCountAllS,
      lbrHrsPerRoRPrcnt: Values.lbrHrsPerRoRPrcnt,
      lbrHrsPerRoMPrcnt: Values.lbrHrsPerRoMPrcnt,
      lbrHrsPerRoCPrcnt: Values.lbrHrsPerRoCPrcnt,
      distinctRORS: Values.distinctRORS,
      distinctROMS: Values.distinctROMS,
      distinctROCompS: Values.distinctROCompS,
      distinctROSS: Values.distinctROSS,
      distinctROCmbPerRo: Values.distinctROCmbPerRo,
      totallbrDiscount: Values.totallbrDiscount,
      totalprtsDiscount: Values.totalprtsDiscount,
      percLbrDiscount: Values.percLbrDiscount,
      totalPercDiscount: Values.totalPercDiscount,
      percPartsDiscount: Values.percPartsDiscount,
      ronumberArrJobs: Values.ronumberArrJobs,
      costMaintenance: Values.costMaintenance,
      saleMaintenance1: Values.saleMaintenance1,
      shMaintenance1: Values.shMaintenance1,
      saleExtended: Values.saleExtended,
      costExtended: Values.costExtended,
      shExtended: Values.shExtended,
      roMaintenance: Values.roMaintenance,
      roExtended: Values.roExtended,
      laborGPMP: Values.laborGPMP,
      laborGPE: Values.laborGPE,
      PsMaintenanceP: Values.PsMaintenanceP,
      PcMaintenanceP: Values.PcMaintenanceP,
      PsExtended: Values.PsExtended,
      PcExtended: Values.PcExtended,
      prtGpMP: Values.prtGpMP,
      prtGpE: Values.prtGpE,
      sHByCategoryF: Values.sHByCategoryF,
      saleFactory: Values.saleFactory,
      costFactory: Values.costFactory,
      shFactory: Values.shFactory,
      roFactory: Values.roFactory,
      laborGPF: Values.laborGPF,
      PsFactory: Values.PsFactory,
      PcFactory: Values.PcFactory,
      prtGpF: Values.prtGpF,
      costRepair: Values.costRepair,
      costMaintenances: Values.costMaintenances,
      costCompetitive: Values.costCompetitive,
      costShop: Values.costShop,
      SHByCategory: Values.SHByCategory,
      soldHoursTech: Values.soldHoursTech,
      availableHours: Values.availableHours,
      avgHrsTech: Values.avgHrsTech,
      techCount: Values.techCount,
      avgSalesPerTech: Values.avgSalesPerTech,
      totalSaleTech: Values.totalSaleTech,
      totalSale: Values.totalSale,
      ElrCombined: Values.ElrCombined,
      ElrCustomer: Values.ElrCustomer,
      ElrWarranty: Values.ElrWarranty,
      ElrInternal: Values.ElrInternal,
      ElrMaintenance: Values.ElrMaintenance,
      ElrExtended: Values.ElrExtended,
      ElrFactory: Values.ElrFactory,
      LHROCombined: Values.LHROCombined,
      LHROCustomer: Values.LHROCustomer,
      LHROWarranty: Values.LHROWarranty,
      LHROInteranl: Values.LHROInteranl,
      LHROMaintain: Values.LHROMaintain,
      LHROExtended: Values.LHROExtended,
      LHROFactory: Values.LHROFactory,
      LSaleROCombined: Values.LSaleROCombined,
      LSaleROCustomer: Values.LSaleROCustomer,
      LSaleROWarranty: Values.LSaleROWarranty,
      LSaleROInteranl: Values.LSaleROInteranl,
      LSaleROMaintain: Values.LSaleROMaintain,
      LSaleROExtended: Values.LSaleROExtended,
      LSaleROFactory: Values.LSaleROFactory,
      JobCntCombined: Values.JobCntCombined,
      JobCntCustomer: Values.JobCntCustomer,
      JobCntWarranty: Values.JobCntWarranty,
      JobCntInteranl: Values.JobCntInteranl,
      JobCntMaintain: Values.JobCntMaintain,
      JobCntExtended: Values.JobCntExtended,
      JobCntFactory: Values.JobCntFactory,
      prtsRevenueRoCombined: Values.prtsRevenueRoCombined,
      prtsRevenueRoCustomer: Values.prtsRevenueRoCustomer,
      prtsRevenueRoWarranty: Values.prtsRevenueRoWarranty,
      prtsRevenueRoInteranl: Values.prtsRevenueRoInteranl,
      prtsRevenueRoMaintain: Values.prtsRevenueRoMaintain,
      prtsRevenueRoExtended: Values.prtsRevenueRoExtended,
      prtsRevenueRoFactory: Values.prtsRevenueRoFactory,
      prtsMarkupCombined: Values.prtsMarkupCombined,
      revPrtsMarkupCustomer: Values.revPrtsMarkupCustomer,
      prtsMarkupWarranty: Values.prtsMarkupWarranty,
      prtsMarkupInteranl: Values.prtsMarkupInteranl,
      prtsMarkupMaintain: Values.prtsMarkupMaintain,
      prtsMarkupExtended: Values.prtsMarkupExtended,
      prtsMarkupFactory: Values.prtsMarkupFactory,
      shopSuppliesC: Values.shopSuppliesC,
      shopSuppliesCombined: Values.shopSuppliesCombined,
      shopSuppliesI: Values.shopSuppliesI,
      mpiCount: Values.mpiCount,
      totalROCount: Values.totalROCount,
      mpiPercentage: Values.mpiPercentage,
      menuCount: Values.menuCount,
      menuPercentage: Values.menuPercentage,
      prtsHoursPerRO: Values.prtsHoursPerRO,
      prtsHours: Values.prtsHours,
      prtsHrsRoCount: Values.prtsHrsRoCount,
      distinctROPartsOnly: Values.distinctROPartsOnly,
      partsMarkupRC: Values.partsMarkupRC
    });
  };
  handleCellClicked = params => {
    let data = {
      type: 'discount_drilldown',
      month_year: params.value,
      chartId: this.state.chartId
    };
    this.setState({
      month_year: params.value,
      type: 'discount_drilldown'
    });
    this.props.parentCallback(data);
  };

  getAgGridData(queryMonth, serviceAdvisor, drillDownServiceAdvisor, storeId) {
    this.setState({ drillDownServiceAdvisor: drillDownServiceAdvisor });
    if (storeId) {
      localStorage.setItem('storeIdSelected', storeId);
    }
    let DrilldownValues = 0;
    let RoCountValues = 0;
    if (
      this.state.drillDown != 3 &&
      this.state.drillDown != 4 &&
      this.state.drillDown != 18 &&
      this.state.drillDown != 22 &&
      this.state.drillDown != 14 &&
      this.state.drillDown != 15 &&
      this.state.drillDown != 16 &&
      this.state.drillDown != 17 &&
      this.state.drillDown != 25 &&
      this.state.drillDown != 26 &&
      this.state.drillDown != 30 &&
      this.state.drillDown != 31 &&
      this.state.drillDown != 32 &&
      this.state.drillDown != 33 &&
      this.state.drillDown != 34 &&
      this.state.drillDown != 35 &&
      this.state.drillDown != 36 &&
      this.state.drillDown != 40 &&
      this.state.drillDown != 41 &&
      this.state.drillDown != 42 &&
      this.state.drillDown != 43 &&
      this.state.drillDown != 44 &&
      this.state.drillDown != 45 &&
      this.state.drillDown != 46 &&
      this.state.drillDown != 47 &&
      this.state.drillDown != 48 &&
      this.state.chartId != 1174
    ) {
      console.log('enter row=', queryMonth);
      getDrillDownDataForTotalRevenue(queryMonth, storeId, result => {
        this.setState({ isLoading: false });
        if (result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes) {
          let filteredArr = [];
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes
          );

          if (
            this.state.drillDownServiceAdvisor.includes('All') == false &&
            this.state.chartId != 1318 &&
            this.state.chartId != 1319 &&
            this.state.chartId != 1320 &&
            this.state.chartId != 1321 &&
            this.state.chartId != 1322 &&
            this.state.chartId != 1323 &&
            this.state.chartId != 1324 &&
            this.state.chartId != 1325 &&
            this.state.chartId != 1326 &&
            this.state.chartId != 1327 &&
            this.state.chartId != 1328 &&
            this.state.chartId != 1329 &&
            this.state.chartId != 1330 &&
            this.state.chartId != 1331 &&
            this.state.chartId != 1332 &&
            this.state.chartId != 1333
          ) {
            resultArr.map(obj => {
              if (
                this.state.drillDownServiceAdvisor.indexOf(
                  obj.serviceadvisor
                ) != -1
              ) {
                filteredArr.push(obj);
              }
            });
            this.setState({
              rowData: filteredArr
            });
            this.props.setDrillDownCount(true);
          }
          if (
            (this.state.chartId >= 1318 && this.state.chartId <= 1325) ||
            (this.state.chartId >= 1326 && this.state.chartId <= 1333)
          ) {
            var filteredResultForPrtsHours = resultArr.filter(
              item => item.prtextendedsale != 0
            );
            resultArr = filteredResultForPrtsHours;
            if (this.state.drillDownServiceAdvisor.includes('All') == false) {
              filteredResultForPrtsHours.map(obj => {
                if (
                  this.state.drillDownServiceAdvisor.indexOf(
                    obj.serviceadvisor
                  ) != -1
                ) {
                  filteredArr.push(obj);
                }
              });
              this.setState({
                rowData: filteredArr
              });
              resultArr = filteredArr;
              this.props.setDrillDownCount(true);
            } else {
              this.setState({ rowData: filteredResultForPrtsHours });
              this.props.setDrillDownCount(true);
            }
          } else if (this.state.drillDownServiceAdvisor.includes('All')) {
            this.setState({
              rowData: resultArr
            });
            this.props.setDrillDownCount(true);
          }
          if (
            this.state.drillDownServiceAdvisor.includes('All') == false &&
            this.state.title != 'GP-CPLR' &&
            this.state.title != 'GP-LCPSH' &&
            this.state.title != 'GP-CPRO' &&
            this.state.title != 'GP-CPLGP' &&
            this.state.title != 'GP-CPPR' &&
            this.state.title != 'GP-CPPGP'
          ) {
            DrilldownValues = myCustomSumAggregate(
              filteredArr,
              this.state.chartId
            );
          } else {
            if (this.state.chartId >= 1318 && this.state.chartId <= 1325) {
              var filteredResultForPrtsHours = resultArr.filter(
                item => item.prtextendedsale != 0
              );

              DrilldownValues = myCustomSumAggregate(
                filteredResultForPrtsHours,
                this.state.chartId
              );
            } else {
              DrilldownValues = myCustomSumAggregate(
                resultArr,
                this.state.chartId
              );
            }
          }
          this.setDrillDownValuesToState(DrilldownValues);
          if (
            this.state.chartId == 935 ||
            this.state.drillDown == 38 ||
            this.state.drillDown == 39 ||
            this.state.chartId >= 1318 ||
            (this.state.chartId >= 1326 && this.state.chartId <= 1333)
          ) {
            if (this.state.category == 19 || this.state.chartId == 1324) {
              this.setState({ selectedpayTypeFilter1: 'E' });
              this.setState({ selectedpayTypeFilter2: 'E' });
              this.setState({ selectedpayTypeFilter3: 'E' });
            } else if (this.state.category == 4 || this.state.chartId == 1322) {
              this.setState({ selectedpayTypeFilter1: 'I' });
              this.setState({ selectedpayTypeFilter2: 'I' });
              this.setState({ selectedpayTypeFilter3: 'I' });
            } else if (this.state.category == 7 || this.state.chartId == 1323) {
              this.setState({ selectedpayTypeFilter1: 'M' });
              this.setState({ selectedpayTypeFilter2: 'M' });
              this.setState({ selectedpayTypeFilter3: 'M' });
            } else if (this.state.category == 3 || this.state.chartId == 1321) {
              this.setState({ selectedpayTypeFilter1: 'W' });
              this.setState({ selectedpayTypeFilter2: 'W' });
              this.setState({ selectedpayTypeFilter3: 'W' });
            } else if (
              this.state.category == 20 ||
              this.state.chartId == 1325
            ) {
              this.setState({ selectedpayTypeFilter1: 'F' });
              this.setState({ selectedpayTypeFilter2: 'F' });
              this.setState({ selectedpayTypeFilter3: 'F' });
            }
            this.filterByPayTypeGroup();
          }

          if (
            this.state.chartId != 1007 ||
            this.state.chartId != 968 ||
            this.state.chartId != 1134
          ) {
            this.filterByPayTypeGroup();
          }

          if (this.state.drillDown == 8 || this.state.chartId == 1334) {
            this.setState({
              selectedOpCategoryFilter1: 'R',
              selectedOpCategoryFilter2: 'C'
            });
            this.filterByOpCategory();
          }
          if (
            this.state.drillDown == 0 &&
            this.state.category == 6 &&
            this.state.chartId == 1079
          ) {
            this.setState({
              selectedOpCategoryFilter1: 'R'
            });
            this.filterByOpCategory();
          }
          if (
            this.state.drillDown == 0 &&
            this.state.category == 7 &&
            this.state.chartId == 1079
          ) {
            this.setState({
              selectedOpCategoryFilter1: 'M'
            });
            this.filterByOpCategory();
          }
          if (
            this.state.drillDown == 0 &&
            this.state.category == 8 &&
            this.state.chartId == 1079
          ) {
            this.setState({
              selectedOpCategoryFilter1: 'C'
            });
            this.filterByOpCategory();
          }
          if (this.state.chartId == 1098) {
            this.setState({
              selectedOpCategoryFilter1: 'R',
              selectedOpCategoryFilter2: 'C'
            });
          }
          if (this.state.chartId == 936) {
            this.setState({
              selectedOpCategoryFilter1:
                this.state.category == 8
                  ? 'C'
                  : this.state.category == 6
                  ? 'R'
                  : 'M'
            });
          }

          this.filterByOpCategory();
          if (
            this.state.drillDownServiceAdvisor.includes('All') == false &&
            this.state.title != 'GP-CPLR' &&
            this.state.title != 'GP-LCPSH' &&
            this.state.title != 'GP-CPRO' &&
            this.state.title != 'GP-CPLGP' &&
            this.state.title != 'GP-CPPR' &&
            this.state.title != 'GP-CPPGP'
          ) {
            RoCountValues = distinctROCount(filteredArr, this.state.chartId);
          } else {
            if (this.state.chartId >= 1318 && this.state.chartId <= 1325) {
              var filteredResultForPrtsHours = resultArr.filter(
                item => item.prtextendedsale != 0
              );
              RoCountValues = distinctROCount(
                filteredResultForPrtsHours,
                this.state.chartId
              );
            } else {
              RoCountValues = distinctROCount(resultArr, this.state.chartId);
            }
          }

          var RoAndDrillDownCombined = Object.assign(
            RoCountValues,
            DrilldownValues
          );
          this.setDrillDownValuesToState(RoAndDrillDownCombined);
        }
      });
    } else if (
      this.state.drillDown == 3 ||
      this.state.drillDown == 4 ||
      (this.state.drillDown == 47 && this.state.chartId != 1316)
    ) {
      getDrillDownDataForTotalRevenue(queryMonth, storeId, result => {
        this.setState({ isLoading: false });
        var resultArrForMPI;
        var resultArrForMPIs;

        if (result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes) {
          let filteredArr = [];
          let filteredArrForCustomerPay = [];
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes
          );

          if (
            this.state.drillDownServiceAdvisor.includes('All') == false &&
            this.state.drillDown != 47 &&
            this.state.title != 'GP-CPLR' &&
            this.state.title != 'GP-LCPSH' &&
            this.state.title != 'GP-CPRO' &&
            this.state.title != 'GP-CPLGP' &&
            this.state.title != 'GP-CPPR' &&
            this.state.title != 'GP-CPPGP'
          ) {
            resultArr.map(obj => {
              if (
                this.state.drillDownServiceAdvisor.indexOf(
                  obj.serviceadvisor
                ) != -1
              ) {
                filteredArr.push(obj);
              }
            });
            this.setState({
              rowData: filteredArr
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(
              filteredArr,
              this.state.chartId
            );
          } else if (this.state.drillDown == 47) {
            this.setState({ isLoading: true });

            getDrillDownDataForTotalCustomerPayRevenue(
              queryMonth,
              storeId,
              result => {
                this.setState({ isLoading: false });
                var roArray = [];
                if (
                  result.data.dmsAggregateTotalCustomerPayRevenueDetails.nodes
                ) {
                  var resultArrCustomerPayRevenue = this.parseArray(
                    result.data.dmsAggregateTotalCustomerPayRevenueDetails.nodes
                  );
                  if (
                    this.state.drillDownServiceAdvisor.includes('All') == false
                  ) {
                    resultArr.map(obj => {
                      if (
                        this.state.drillDownServiceAdvisor.indexOf(
                          obj.serviceadvisor
                        ) != -1
                      ) {
                        filteredArr.push(obj);
                      }
                    });
                    resultArrCustomerPayRevenue.map(obj => {
                      if (
                        this.state.drillDownServiceAdvisor.indexOf(
                          obj.serviceadvisor
                        ) != -1
                      ) {
                        filteredArrForCustomerPay.push(obj);
                      }
                    });
                    filteredArrForCustomerPay.map(obj => {
                      roArray.push(obj.ronumber);
                    });
                    resultArrForMPI = this.intersection(roArray, filteredArr);

                    DrilldownValues = myCustomSumAggregate(
                      resultArrForMPI,
                      this.state.chartId
                    );
                  } else {
                    resultArrCustomerPayRevenue.map(obj => {
                      roArray.push(obj.ronumber);
                    });
                    resultArrForMPI = this.intersection(roArray, resultArr);

                    DrilldownValues = myCustomSumAggregate(
                      resultArrForMPI,
                      this.state.chartId
                    );
                  }
                  if (this.state.chartId == 1317) {
                    var opcodeFiltered = this.filterMenuOpcodes(
                      resultArrForMPI
                    );
                    this.setState({
                      rowData: opcodeFiltered
                    });
                    this.props.setDrillDownCount(true);
                  } else {
                    this.setState({
                      rowData: resultArrForMPI
                    });
                    this.props.setDrillDownCount(true);
                  }
                  this.setDrillDownValuesToState(DrilldownValues);
                  // if (this.state.chartId == 1316) {
                  //   this.filterByOpCode();
                  // }
                }
              }
            );
          } else {
            this.setState({
              rowData: resultArr
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(
              resultArr,
              this.state.chartId
            );
          }

          this.setDrillDownValuesToState(DrilldownValues);
          //   if (this.state.chartId != 1316) {
          this.filterByPayTypeGroup();
          // }

          //  this.filterByOpCategory();
        }
      });
    } else if (this.state.drillDown == 47 && this.state.chartId == 1316) {
      getDrillDownDataForAllRevenue(queryMonth, result => {
        this.setState({ isLoading: false });
        if (result.data.dmsDrilldownGetDrillDownAllRevenueDetails.nodes) {
          let filteredArr = [];
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownAllRevenueDetails.nodes
          );
          if (this.state.drillDownServiceAdvisor.includes('All') == false) {
            resultArr.map(obj => {
              if (
                this.state.drillDownServiceAdvisor.indexOf(
                  obj.serviceadvisor
                ) != -1
              ) {
                filteredArr.push(obj);
              }
            });
            this.setState({
              rowData: filteredArr
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(
              filteredArr,
              this.state.chartId
            );
          } else {
            this.setState({
              rowData: resultArr
            });
            this.props.setDrillDownCount(true);
            this.filterByOpCode();
            DrilldownValues = myCustomSumAggregate(
              resultArr,
              this.state.chartId
            );
          }
        }
        this.setDrillDownValuesToState(DrilldownValues);
      });
    } else if (this.state.drillDown == 48) {
      getDrillDownDataForPartsMarkupCharts(queryMonth, result => {
        this.setState({ isLoading: false });
        if (result.data.dmsDrilldownGetDrillDownPartsRevenueDetails.nodes) {
          let filteredArr = [];
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownPartsRevenueDetails.nodes
          );
          if (this.state.drillDownServiceAdvisor.includes('All') == false) {
            resultArr.map(obj => {
              if (
                this.state.drillDownServiceAdvisor.indexOf(
                  obj.serviceadvisor
                ) != -1
              ) {
                filteredArr.push(obj);
              }
            });
            this.setState({
              rowData: filteredArr
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(
              filteredArr,
              this.state.chartId
            );
          } else {
            this.filterByPayTypeGroup();
            this.setState({
              rowData: resultArr
            });
            this.props.setDrillDownCount(true);
            this.filterByPayTypeGroup();
            DrilldownValues = myCustomSumAggregate(
              resultArr,
              this.state.chartId
            );
          }
        }
        this.setDrillDownValuesToState(DrilldownValues);
      });
    } else if (this.state.drillDown == 25) {
      getDrillDownDataForAddOns(queryMonth, result => {
        this.setState({ isLoading: false });
        if (
          result.data.dmsDrilldownGetDrillDownTotalRevenueDetailsAddons.nodes
        ) {
          this.setState({
            rowData: this.parseArray(
              result.data.dmsDrilldownGetDrillDownTotalRevenueDetailsAddons
                .nodes
            )
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(
            this.parseArray(
              result.data.dmsDrilldownGetDrillDownTotalRevenueDetailsAddons
                .nodes
            ),
            this.state.chartId
          );

          this.setDrillDownValuesToState(DrilldownValues);

          this.filterByPayTypeGroup();
          if (
            this.state.category == 13 ||
            (this.state.chartId == 1100 && this.state.category == 1) ||
            this.state.chartId == 1101 ||
            this.state.chartId == 1102 ||
            this.state.chartId == 1105 ||
            this.state.chartId == 1106 ||
            this.state.chartId == 1107 ||
            this.state.chartId == 1103 ||
            this.state.chartId == 1117 ||
            this.state.chartId == 1109 ||
            this.state.chartId == 1110 ||
            this.state.chartId == 1116 ||
            this.state.chartId == 1099
          ) {
            this.filterByAddOns();
          } else if (this.state.chartId == 1100 && this.state.category == 0) {
            this.filterByNonAddOns();
          }
        }
      });
    } else if (this.state.drillDown == 26) {
      getDrillDownDataForAddOns(queryMonth, result => {
        this.setState({ isLoading: false });
        if (
          result.data.dmsDrilldownGetDrillDownTotalRevenueDetailsAddons.nodes
        ) {
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownTotalRevenueDetailsAddons.nodes
          );
          var filteredArr = resultArr.filter(function(el) {
            return el.serviceadvisor == serviceAdvisor;
          });
          this.setState({
            rowData: filteredArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(
            filteredArr,
            this.state.chartId,
            this.state.category
          );

          this.setDrillDownValuesToState(DrilldownValues);

          this.filterByPayTypeGroup();
          if (
            this.state.chartId == 1118 ||
            this.state.chartId == 1122 ||
            this.state.chartId == 1119 ||
            this.state.chartId == 1120
          ) {
            this.filterByAddOns();
          }
          if (this.state.category == 'Non-Add Ons Revenue%') {
            this.filterByNonAddOns();
          } else if (this.state.category == 'Add Ons Revenue%') {
            this.filterByAddOns();
          }
        }
      });
    } else if (this.state.drillDown == 9) {
      getDrillDownDataForPartsMarkup(queryMonth, result => {
        this.setState({ isLoading: false });
        if (result.data.dmsDrilldownGetDrillDownCpPartsMarkup) {
          let filteredArr = [];
          var resultArr = result.data.dmsDrilldownGetDrillDownCpPartsMarkup;
          if (
            this.state.drillDownServiceAdvisor.includes('All') == false &&
            this.state.title != 'GP-CPLR' &&
            this.state.title != 'GP-LCPSH' &&
            this.state.title != 'GP-CPRO' &&
            this.state.title != 'GP-CPLGP' &&
            this.state.title != 'GP-CPPR' &&
            this.state.title != 'GP-CPPGP'
          ) {
            resultArr.map(obj => {
              if (
                this.state.drillDownServiceAdvisor.indexOf(
                  obj.serviceadvisor
                ) != -1
              ) {
                filteredArr.push(obj);
              }
            });
            this.setState({
              rowData: filteredArr
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(filteredArr, this.props);
          } else {
            this.setState({
              rowData: result.data.dmsDrilldownGetDrillDownCpPartsMarkup
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(
              result.data.dmsDrilldownGetDrillDownCpPartsMarkup,
              this.props
            );
          }
          this.setDrillDownValuesToState(DrilldownValues);
          this.filterByPayTypeGroup();
          this.filterByOpCategory();
        }
      });
    } else if (this.state.drillDown == 18) {
      var drillDownServiceAdvisor = this.state.drillDownServiceAdvisor.includes(
        'All'
      )
        ? 'All'
        : this.props.session.serviceAdvisor.join().split(',');
      if (this.state.category == 1) {
        getROCountForTotalRevenue(
          queryMonth,
          drillDownServiceAdvisor,
          result => {
            //  this.setState({ isLoading: false });
            if (
              result.data
                .dbdPeopleMetricsTechnicianFnRonumberDrillDownTechRevenue.nodes
            ) {
              this.setState({
                rowDataLength:
                  result.data
                    .dbdPeopleMetricsTechnicianFnRonumberDrillDownTechRevenue
                    .totalCount
              });
              getDrillDownDatForSingleJob(queryMonth, result => {
                this.setState({ isLoading: false });
                if (result.data.dmsDrilldownGetDrillDownSingleJobRo.nodes) {
                  let filteredArr = [];

                  var resultArr = this.parseArray(
                    result.data.dmsDrilldownGetDrillDownSingleJobRo.nodes
                  );

                  if (
                    this.state.drillDownServiceAdvisor.includes('All') == false
                  ) {
                    resultArr.map(obj => {
                      if (
                        this.state.drillDownServiceAdvisor.indexOf(
                          obj.serviceadvisor
                        ) != -1
                      ) {
                        filteredArr.push(obj);
                      }
                    });

                    this.setState({
                      rowData: filteredArr
                    });
                    this.props.setDrillDownCount(true);
                    DrilldownValues = myCustomSumAggregate(
                      filteredArr,
                      this.props
                    );
                    this.setDrillDownValuesToState(DrilldownValues);
                    RoCountValues = distinctROCount(
                      filteredArr,
                      this.state.chartId
                    );
                  } else {
                    this.setState({
                      rowData: resultArr
                    });
                    this.props.setDrillDownCount(true);
                    DrilldownValues = myCustomSumAggregate(
                      resultArr,
                      this.props
                    );

                    this.setDrillDownValuesToState(DrilldownValues);
                    RoCountValues = distinctROCount(
                      resultArr,
                      this.state.chartId
                    );
                  }
                  var RoAndDrillDownCombined = Object.assign(
                    RoCountValues,
                    DrilldownValues
                  );
                  this.setDrillDownValuesToState(RoAndDrillDownCombined);
                  var RoPercent =
                    (this.state.distinctROPercent / this.state.rowDataLength) *
                    100;
                  this.setState({
                    ROPercent:
                      (
                        Math.round((RoPercent + Number.EPSILON) * 100) / 100
                      ).toLocaleString() + '%'
                  });
                }
              });
            }
          }
        );
      } else {
        getDrillDownDatForSingleJob(queryMonth, result => {
          this.setState({ isLoading: false });
          if (result.data.dmsDrilldownGetDrillDownSingleJobRo.nodes) {
            let filteredArr = [];
            var resultArr = this.parseArray(
              result.data.dmsDrilldownGetDrillDownSingleJobRo.nodes
            );

            if (this.state.drillDownServiceAdvisor.includes('All') == false) {
              resultArr.map(obj => {
                if (
                  this.state.drillDownServiceAdvisor.indexOf(
                    obj.serviceadvisor
                  ) != -1
                ) {
                  filteredArr.push(obj);
                }
              });

              this.setState({
                rowData: filteredArr
              });
              this.props.setDrillDownCount(true);
              DrilldownValues = myCustomSumAggregate(filteredArr, this.props);
              this.setDrillDownValuesToState(DrilldownValues);
              RoCountValues = distinctROCount(filteredArr, this.state.chartId);
            } else {
              this.setState({
                rowData: resultArr
              });
              this.props.setDrillDownCount(true);
              DrilldownValues = myCustomSumAggregate(resultArr, this.props);
              this.setDrillDownValuesToState(DrilldownValues);
              RoCountValues = distinctROCount(resultArr, this.state.chartId);
            }
            var RoAndDrillDownCombined = Object.assign(
              RoCountValues,
              DrilldownValues
            );
            this.setDrillDownValuesToState(RoAndDrillDownCombined);
          }
        });
      }
    } else if (this.state.drillDown == 22) {
      getDrillDownDataForSoldHours(queryMonth, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .dms_drilldown_vw_drill_down_master_revenue_with_actualhours
        ) {
          this.setState({
            rowData:
              result.data
                .dms_drilldown_vw_drill_down_master_revenue_with_actualhours
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(
            result.data
              .dms_drilldown_vw_drill_down_master_revenue_with_actualhours,
            this.props
          );
          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (this.state.drillDown == 14) {
      getDrillDownDataForMovingElr(result => {
        this.setState({ isLoading: false, rowData: [] });
        if (result.data.dbdCpLaborRatesGetElrMovingAverage.nodes) {
          this.setState({
            rowData: result.data.dbdCpLaborRatesGetElrMovingAverage.nodes
          });
          this.props.setDrillDownCount(true);
        }
      });
    } else if (this.state.drillDown == 15) {
      getDrillDownDataForMovingPartsMarkup(result => {
        this.setState({ isLoading: false });
        if (result.data.dbdCpPartsMarkupGetPartsMovingMarkups.nodes) {
          //data = lodash.sortBy(data, 'opcategory');

          this.setState({
            rowData: lodash.sortBy(
              result.data.dbdCpPartsMarkupGetPartsMovingMarkups.nodes,
              'currentGroup'
            )
          });
          this.props.setDrillDownCount(true);
        }
      });
    } else if (this.state.drillDown == 16) {
      let Elr = this.props.history.location.state.elr;
      let soldHours = this.props.history.location.state.slodhours;
      let tabSelection = this.props.history.location.state.tabSelection;
      let filteredResult = [];
      getDrillDownDataForLaborItemization(tabSelection, result => {
        this.setState({ isLoading: false });
        if (
          result.data.dbdLaborItemizationGetChartsLaborElrSoldhoursItemization
            .nodes
        ) {
          let data =
            result.data.dbdLaborItemizationGetChartsLaborElrSoldhoursItemization
              .nodes;
          if (this.props.history.location.state.label == 0) {
            filteredResult = data.filter(
              item =>
                item.lbrsoldhours == soldHours &&
                item.elrRepair == Elr &&
                item.opcategory == 'REPAIR'
            );
          } else if (this.props.history.location.state.label == 1) {
            filteredResult = data.filter(
              item =>
                item.lbrsoldhours == soldHours &&
                item.elrCompetitive == Elr &&
                item.opcategory == 'COMPETITIVE'
            );
          } else {
            filteredResult = data.filter(
              item =>
                item.lbrsoldhours == soldHours &&
                item.elrMaintenance == Elr &&
                item.opcategory == 'MAINTENANCE'
            );
          }
          this.setState({
            rowData: filteredResult
          });
          this.props.setDrillDownCount(true);
          // this.setState({
          //   rowData:
          //     result.data
          //       .dbdLaborItemizationGetChartsLaborElrSoldhoursItemization.nodes
          // });
        }
      });
    } else if (this.state.drillDown == 17) {
      let markup = this.props.history.location.state.markup;
      let partsCost = this.props.history.location.state.partscost;
      let tabSelection = this.props.history.location.state.tabSelection;
      let filteredResult = [];
      getDrillDownDataForPartsItemization(tabSelection, result => {
        this.setState({ isLoading: false });
        if (
          result.data.dbdPartsItemizationGetChartsPartsMarkupCostItemization
            .nodes
        ) {
          let data =
            result.data.dbdPartsItemizationGetChartsPartsMarkupCostItemization
              .nodes;
          if (this.props.history.location.state.label == 0) {
            filteredResult = data.filter(
              item =>
                item.prtscost == partsCost &&
                item.markupRepair == markup &&
                item.opcategory == 'REPAIR'
            );
          } else if (this.props.history.location.state.label == 1) {
            filteredResult = data.filter(
              item =>
                item.prtscost == partsCost &&
                item.markupCompetitive == markup &&
                item.opcategory == 'COMPETITIVE'
            );
          } else {
            filteredResult = data.filter(
              item =>
                item.prtscost == partsCost &&
                item.markupMaintenance == markup &&
                item.opcategory == 'MAINTENANCE'
            );
          }
          this.setState({
            rowData: filteredResult
          });
          this.props.setDrillDownCount(true);
          // this.setState({
          //   rowData:
          //     result.data.dbdPartsItemizationGetChartsPartsMarkupCostItemization
          //       .nodes
          // });
        }
      });
    } else if (
      (this.state.drillDown == 30 &&
        (this.state.category == 0 ||
          this.state.category == 2 ||
          this.state.category == 3)) ||
      (this.state.drillDown == 36 && this.state.category == 0)
      //  (this.state.drillDown == 40 && this.state.category == 0)
    ) {
      var overallLbrSaleDiscount = 0;
      var overallROCount = 0;
      var discountedLaborRo = 0;
      var overallDiscount = 0;

      getDrillDownDataForDiscountSummary(queryMonth, 'All', result => {
        // this.setState({ isLoading: false });
        if (result.data.dbdDiscountsGetLaborDiscountSummary.nodes) {
          var datas = result.data.dbdDiscountsGetLaborDiscountSummary.nodes;

          datas.forEach(function(value) {
            overallLbrSaleDiscount = value.overallbrsale;
            overallROCount = value.overallrocount;
            discountedLaborRo = value.cpdiscountedrocount;
            overallDiscount = value.overalldiscountinmonth;
          });
          getDrillDownDataForLaborDiscount(queryMonth, result => {
            this.setState({ isLoading: false });
            if (
              result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes
            ) {
              var resultArr = this.parseArray(
                result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes
              );
              if (this.state.chartId == 1113 && this.state.category == 3) {
                resultArr = resultArr.filter(function(el) {
                  return el.dislevel == 'LINE';
                });
              } else if (
                this.state.chartId == 1113 &&
                this.state.category == 2
              ) {
                resultArr = resultArr.filter(function(el) {
                  return el.dislevel == 'RO';
                });
              }
              this.setState({
                rowData: resultArr
              });
              this.props.setDrillDownCount(true);
              DrilldownValues = myCustomSumAggregate(
                result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails
                  .nodes,
                this.state.chartId
              );
              this.setDrillDownValuesToState(DrilldownValues);

              var TotalLbrSaleP =
                (this.state.totallbrDiscount / overallLbrSaleDiscount) * 100;
              this.setState({
                totalLbrSaleP:
                  parseFloat(TotalLbrSaleP)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              });
              this.setState({
                overallLbrSaleDiscount:
                  '$' +
                  parseFloat(overallLbrSaleDiscount)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                totallbrDiscount:
                  '$' +
                  parseFloat(this.state.totallbrDiscount)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });

              var DiscountedTotalCPRos = Math.abs(
                overallDiscount / overallROCount
              );
              var DiscountedTotalDiscountedCPRos = Math.abs(
                overallDiscount / discountedLaborRo
              );
              this.setState({
                discountedTotalCPRos:
                  '$' +
                  parseFloat(DiscountedTotalCPRos)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                discountedTotalDiscountedCPRos:
                  '$' +
                  parseFloat(DiscountedTotalDiscountedCPRos)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                discountedLaborRoCount: parseFloat(discountedLaborRo)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                overallROCount: parseFloat(overallROCount)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });

              if (this.state.chartId == 1113 || this.state.chartId == 1123) {
                RoCountValues = distinctROCount(
                  result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails
                    .nodes,
                  this.state.chartId
                );
                var RoAndDrillDownCombined = Object.assign(
                  RoCountValues,
                  DrilldownValues
                );
                this.setDrillDownValuesToState(RoAndDrillDownCombined);
              }
            }
          });
        }
      });
    } else if (
      (this.state.drillDown == 30 || this.state.drillDown == 36) &&
      this.state.category == 1
    ) {
      var overallPrtsSaleDiscount = 0;
      var overallROCount = 0;
      var discountedLaborRo = 0;
      var overallDiscount = 0;
      getDrillDownDataForDiscountSummaryParts(queryMonth, 'All', result => {
        // this.setState({ isLoading: false });
        if (result.data.dbdDiscountsGetPartsDiscountSummary.nodes) {
          var datas = result.data.dbdDiscountsGetPartsDiscountSummary.nodes;

          datas.forEach(function(value) {
            overallPrtsSaleDiscount = value.overallprtsale;
            overallROCount = value.overallrocount;
            discountedLaborRo = value.cpdiscountedrocount;
            overallDiscount = value.overalldiscountinmonth;
          });
          getDrillDownDataForPartsDiscount(queryMonth, result => {
            this.setState({ isLoading: false });
            if (
              result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails.nodes
            ) {
              var resultArr =
                result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails
                  .nodes;
              this.setState({
                rowData: resultArr
              });
              this.props.setDrillDownCount(true);
              DrilldownValues = myCustomSumAggregate(
                this.parseArray(
                  result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails
                    .nodes
                ),
                this.state.chartId
              );

              this.setDrillDownValuesToState(DrilldownValues);

              var TotalPrtsDiscountP =
                (this.state.totalprtsDiscount / this.state.partsSale) * 100;

              this.setState({
                totalDiscountP:
                  parseFloat(TotalPrtsDiscountP)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              });
              this.setState({
                overAllDiscountP:
                  '$' +
                  parseFloat(this.state.partsSale)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });

              var TotalPrtsSaleP =
                (this.state.totalprtsDiscount / overallPrtsSaleDiscount) * 100;
              this.setState({
                totalPrtSaleP:
                  parseFloat(TotalPrtsSaleP)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              });
              this.setState({
                overallPrtsSaleDiscount:
                  '$' +
                  parseFloat(overallPrtsSaleDiscount)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                totalprtsDiscount:
                  '$' +
                  parseFloat(this.state.totalprtsDiscount)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              var DiscountedTotalCPRos = Math.abs(
                overallDiscount / overallROCount
              );
              var DiscountedTotalDiscountedCPRos = Math.abs(
                overallDiscount / discountedLaborRo
              );
              this.setState({
                discountedTotalCPRos:
                  '$' +
                  parseFloat(DiscountedTotalCPRos)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                discountedTotalDiscountedCPRos:
                  '$' +
                  parseFloat(DiscountedTotalDiscountedCPRos)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                discountedLaborRoCount: parseFloat(discountedLaborRo)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                overallROCount: parseFloat(overallROCount)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
            }
          });
        }
      });
    } else if (
      this.state.drillDown == 33 ||
      (this.state.drillDown == 30 &&
        (this.state.chartId == 1123 || this.state.chartId == 1124))
    ) {
      getDrillDownDataForTotalRevenue(queryMonth, storeId, result => {
        // this.setState({ isLoading: false });
        if (result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes) {
          var resultArrs = this.parseArray(
            result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes
          );
          var filteredArr = resultArrs.filter(function(el) {
            return Number(el.serviceadvisor) == serviceAdvisor;
          });
          var RoCountValuess = distinctROCount(filteredArr, this.state.chartId);

          getDrillDownDiscountByRO(queryMonth, result => {
            this.setState({ isLoading: false });
            if (
              result.data
                .dmsDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor
                .nodes
            ) {
              var resultArr = this.parseArray(
                result.data
                  .dmsDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor
                  .nodes
              );
              var filteredArr1 = resultArr.filter(function(el) {
                return Number(el.serviceadvisor) == serviceAdvisor;
              });
              if (this.state.chartId == 1123) {
                this.setState({
                  rowData: resultArr
                });
                this.props.setDrillDownCount(true);
              } else {
                this.setState({
                  rowData: filteredArr1
                });
                this.props.setDrillDownCount(true);
              }
              DrilldownValues = myCustomSumAggregate(
                result.data
                  .dmsDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor
                  .nodes,
                this.state.chartId
              );
              this.setDrillDownValuesToState(DrilldownValues);
              RoCountValues = distinctROCount(
                result.data
                  .dmsDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor
                  .nodes,
                this.state.chartId
              );
              var RoCountValuesMaster = distinctROCount(
                resultArrs,
                this.state.chartId
              );

              if (this.state.chartId == 1124) {
                RoCountValues = distinctROCount(
                  filteredArr1,
                  this.state.chartId
                );
              }
              var ronumberArrJobs = RoCountValues.ronumberArrJobs;
              var roArrayCount = RoCountValues.ronumberArr;
              this.setState({ roArrayCountDis: roArrayCount });
              var roArrayCountMaster = RoCountValuesMaster.ronumberArr;
              this.setState({ overallRoCountDis: roArrayCountMaster });
              var discountROPerc = (roArrayCount / roArrayCountMaster) * 100;
              var discountJobs = ronumberArrJobs / roArrayCount;
              var DiscountROPercSa =
                (roArrayCount / RoCountValuess.ronumberArr) * 100;
              this.setState({
                discountRO: roArrayCount
              });
              this.setState({
                overallRO: RoCountValuess.ronumberArr
              });
              this.setState({
                discountROPerc: discountROPerc.toFixed(2) + '%'
              });
              this.setState({
                discountJob: discountJobs.toFixed(2)
              });
              this.setState({
                discountROPercSa: DiscountROPercSa.toFixed(2) + '%'
              });
            }
          });
        }
      });
    } else if (
      this.state.drillDown == 31 &&
      this.state.category == ' Labor Discount'
    ) {
      getDrillDownDataForLaborDiscount(queryMonth, result => {
        this.setState({ isLoading: false });

        if (result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes) {
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes
          );
          var filteredArr = resultArr.filter(function(el) {
            return el.serviceadvisor == serviceAdvisor;
          });
          this.setState({
            rowData: filteredArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(
            filteredArr,
            this.state.chartId
          );
          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (
      this.state.drillDown == 31 &&
      this.state.category == ' Parts Discount'
    ) {
      getDrillDownDataForPartsDiscount(queryMonth, result => {
        this.setState({ isLoading: false });
        if (result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails.nodes) {
          var resultArr =
            result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails.nodes;
          var filteredArr = resultArr.filter(function(el) {
            return el.serviceadvisor == serviceAdvisor;
          });
          this.setState({
            rowData: filteredArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(
            filteredArr,
            this.state.chartId
          );
          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (this.state.drillDown == 32) {
      let jobCountAll = 0;
      var jobCount = 0;

      getDrillDownDataForDiscountJobCount(queryMonth, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .dmsDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor.nodes
        ) {
          var resultArr = this.parseArray(
            result.data
              .dmsDrilldownGetDrillDownDiscountsJobDetailsByServiceAdvisor.nodes
          );

          var filteredArr = resultArr.filter(function(el) {
            return el.serviceadvisor == serviceAdvisor;
          });
          jobCount = filteredArr.length;
          var jobArrayRepair = [];
          var jobArrayCompet = [];
          var jobArrayMaint = [];
          filteredArr.forEach(element => {
            if (element.opcategory == 'REPAIR') {
              jobArrayRepair.push(element.opcategory);
            }
            if (element.opcategory == 'MAINTENANCE') {
              jobArrayMaint.push(element.opcategory);
            }
            if (element.opcategory == 'COMPETITIVE') {
              jobArrayCompet.push(element.opcategory);
            }
          });

          var jobCountRep = (jobArrayRepair.length / filteredArr.length) * 100;
          var jobCountMain = (jobArrayMaint.length / filteredArr.length) * 100;
          var jobCountComp = (jobArrayCompet.length / filteredArr.length) * 100;
          this.setState({
            rowData: filteredArr,
            // discountJobCountPerc: jobCountPerc,
            jobCountRep: jobCountRep.toFixed(2) + '%',
            jobCountMain: jobCountMain.toFixed(2) + '%',
            jobCountComp: jobCountComp.toFixed(2) + '%'
          });
          this.props.setDrillDownCount(true);
        }
        if (this.state.chartId == 1126) {
          this.setState({ isLoading: true });
          getDrillDownDataForTotalRevenue(queryMonth, storeId, result => {
            this.setState({ isLoading: false });
            if (result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes) {
              var resultArr = this.parseArray(
                result.data.dmsDrilldownGetDrillDownTotalRevenueDetails.nodes
              );
              var filteredArr = resultArr.filter(function(el) {
                return (
                  el.serviceadvisor == serviceAdvisor && el.paytypegroup == 'C'
                );
              });

              jobCountAll = filteredArr.length;

              this.setState({ discountJob: jobCount });
              this.setState({ overallJob: jobCountAll });
              var jobCountPerc = (jobCount / jobCountAll) * 100;
              this.setState({
                discountJobCountPerc: jobCountPerc.toFixed(2) + '%'
              });
            }
          });
        }
      });
      // }
    } else if (this.state.drillDown == 34) {
      getDrillDownDiscountSummaryLabor(
        this.state.discountMonth,
        this.state.discountId,
        this.state.discountServiceAdvisor,
        result => {
          this.setState({ isLoading: false });
          if (
            result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes
          ) {
            this.setState({
              rowData: this.parseArray(
                result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes
              )
            });
            this.props.setDrillDownCount(true);
          }
        }
      );
    } else if (this.state.drillDown == 35) {
      getDrillDownDiscountSummaryParts(
        this.state.discountMonth,
        this.state.discountId,
        this.state.discountServiceAdvisor,
        result => {
          this.setState({ isLoading: false });
          if (
            result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails.nodes
          ) {
            this.setState({
              rowData: this.parseArray(
                result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails.nodes
              )
            });
            this.props.setDrillDownCount(true);
          }
        }
      );
    } else if (this.state.drillDown == 37 && this.state.chartId != 1175) {
      let intArray = [];
      if (this.state.drillDownServiceAdvisor.includes('All')) {
        getDrillDownForAverageHoursSoldPerTech(queryMonth, 'All', result => {
          this.setState({ isLoading: false });
          let filteredArr = [];
          var resultArr =
            result.data.dmsDrilldownVwDrillDownAverageHoursPerTechnicians.nodes;
          this.setState({
            rowData: resultArr
          });
          this.props.setDrillDownCount(true);
          var res = resultArr.map(v => {
            intArray.push({
              availablehrs: parseFloat(v['availablehrs']) || 0,
              closeddate: v.closeddate,
              monthYear: v.monthYear,
              soldhours: parseFloat(v['soldhours']) || 0,
              techlist: parseFloat(v['techlist']) || 0
            });
          });
          DrilldownValues = myCustomSumAggregate(intArray, this.state.chartId);
          this.setDrillDownValuesToState(DrilldownValues);
        });
      } else {
        getDrillDownForAverageHoursSoldPerTech(
          queryMonth,
          this.getFilters(this.props.session.serviceAdvisor),
          result => {
            this.setState({ isLoading: false });
            let filteredArr = [];
            var resultArr =
              result.data
                .dmsDrilldownFnDrilldownAverageHoursPerTechnicianServiceAdvisor
                .nodes;
            this.setState({
              rowData: resultArr
            });
            this.props.setDrillDownCount(true);
            var res = resultArr.map(v => {
              intArray.push({
                averagehrspertech: parseFloat(v['averagehrspertech']) || 0,
                rodate: v.rodate,
                monthYear: v.monthYear,
                totalavailablehrs: parseFloat(v['totalavailablehrs']) || 0,
                totalsoldhours: parseFloat(v['totalsoldhours']) || 0
              });
            });
            DrilldownValues = myCustomSumAggregate(
              intArray,
              this.state.chartId
            );
            this.setDrillDownValuesToState(DrilldownValues);
          }
        );
      }
    } else if (this.state.drillDown == 40) {
      getDrillDownDataForLaborDiscount(queryMonth, result => {
        this.setState({ isLoading: false });

        if (result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes) {
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownDiscountedLaborDetails.nodes
          );

          this.setState({
            rowData: resultArr
          });
          this.props.setDrillDownCount(true);
          var LbrSale = 0;
          var LbrDiscount = 0;
          resultArr.forEach(function(value) {
            if (value.paytypegroup == 'C') {
              LbrSale += Number(value.lbrsale);
              LbrDiscount += Number(value.apportionedlbrdiscount);
            }
          });
          if (
            this.state.chartId == 1233 &&
            this.state.drillDown == 40 &&
            this.state.category == 0
          ) {
            DrilldownValues = myCustomSumAggregate(
              resultArr,
              this.state.chartId
            );
            this.setDrillDownValuesToState(DrilldownValues);
          }

          console.log(
            'datas 123== ',
            this.state.chartId,
            this.state.drillDown,
            this.state.category
          );
          getDrillDownDataForPartsDiscount(queryMonth, result => {
            this.setState({ isLoading: false });
            if (
              result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails.nodes
            ) {
              var resultArr = this.parseArray(
                result.data.dmsDrilldownGetDrillDownDiscountedPartsDetails.nodes
              );
              if (
                this.state.category == 1
                //  typeof this.state.category == "undefined"
              ) {
                this.setState({
                  rowData: resultArr
                });
                this.props.setDrillDownCount(true);
              }

              DrilldownValues = myCustomSumAggregate(
                resultArr,
                this.state.chartId
              );
              var totallbrDiscount = this.state.totallbrDiscount;
              this.setDrillDownValuesToState(DrilldownValues);

              var total = LbrSale + this.state.partsSale;
              var TotalLbrDiscountP = (totallbrDiscount / total) * 100;

              this.setState({
                totalDiscountP:
                  parseFloat(TotalLbrDiscountP)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              });

              this.setState({
                totallbrDiscount:
                  '$' +
                  parseFloat(totallbrDiscount)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });

              this.setState({
                overAllDiscountP:
                  '$' +
                  parseFloat(total)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              var totalDiscount =
                Math.abs(LbrDiscount) + this.state.totalprtsDiscount;
              var totalDiscountPercentage = (totalDiscount / total) * 100;
              this.setState({
                totalDiscountPercentage:
                  parseFloat(totalDiscountPercentage)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              });

              var percPrtsDiscount =
                (this.state.totalprtsDiscount / total) * 100;

              this.setState({
                percPartsDiscount:
                  parseFloat(percPrtsDiscount)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              });

              this.setState({
                totalDiscountLP:
                  '$' +
                  parseFloat(totalDiscount)
                    .toFixed(2)
                    .toString()
                    .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              });
              this.setState({
                LbrDiscount:
                  '$ ' +
                  (
                    Math.round((Math.abs(LbrDiscount) + Number.EPSILON) * 100) /
                    100
                  ).toLocaleString()
              });
              this.setState({
                totalprtsDiscount:
                  '$' +
                  (
                    Math.round(
                      (Math.abs(this.state.totalprtsDiscount) +
                        Number.EPSILON) *
                        100
                    ) / 100
                  ).toLocaleString()
              });
            }
          });
        }
      });
    } else if (this.state.drillDown == 41) {
      getLaborPartsDiscountByMonth(queryMonth, result => {
        this.setState({ isLoading: false });
        if (result.data.dbdDiscountsGetVwLaborAndPartsDiscountByMonth.nodes) {
          var resultArr = this.parseArray(
            result.data.dbdDiscountsGetVwLaborAndPartsDiscountByMonth.nodes
          );

          this.setState({
            rowData: resultArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(resultArr, this.state.chartId);

          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (this.state.drillDown == 42) {
      getDiscountedSalePercentage(queryMonth, result => {
        this.setState({ isLoading: false });

        if (result.data.dbdDiscountsGetVwDiscountedSalePercentage.nodes) {
          var resultArr = this.parseArray(
            result.data.dbdDiscountsGetVwDiscountedSalePercentage.nodes
          );

          this.setState({
            rowData: resultArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(resultArr, this.state.chartId);

          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (this.state.drillDown == 43) {
      getDiscountsPerCPROs(queryMonth, result => {
        this.setState({ isLoading: false });

        if (result.data.dbdDiscountsGetVwDiscountsPerRo.nodes) {
          var resultArr = this.parseArray(
            result.data.dbdDiscountsGetVwDiscountsPerRo.nodes
          );

          this.setState({
            rowData: resultArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(resultArr, this.state.chartId);

          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (this.state.drillDown == 44) {
      getDiscountsPerTotalDiscountedCPROs(queryMonth, result => {
        this.setState({ isLoading: false });

        if (result.data.dbdDiscountsGetVwDiscountsPerRo.nodes) {
          var resultArr = result.data.dbdDiscountsGetVwDiscountsPerRo.nodes;

          this.setState({
            rowData: resultArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(resultArr, this.state.chartId);

          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (this.state.drillDown == 45) {
      getDiscountedTotalPerTotalDiscountedCPROs(queryMonth, result => {
        this.setState({ isLoading: false });

        if (
          result.data.dbdDiscountsGetVwDiscountPercDiscountedPerTotalCpDiscounts
            .nodes
        ) {
          var resultArr = this.parseArray(
            result.data
              .dbdDiscountsGetVwDiscountPercDiscountedPerTotalCpDiscounts.nodes
          );

          this.setState({
            rowData: resultArr
          });
          this.props.setDrillDownCount(true);
          DrilldownValues = myCustomSumAggregate(resultArr, this.state.chartId);

          this.setDrillDownValuesToState(DrilldownValues);
        }
      });
    } else if (this.state.drillDown == 46) {
      let filteredArr = [];
      getShopSuppliesDrilldown(queryMonth, result => {
        this.setState({ isLoading: false });
        if (result.data.dmsDrilldownGetDrillDownShopSupplies.nodes) {
          var resultArr = this.parseArray(
            result.data.dmsDrilldownGetDrillDownShopSupplies.nodes
          );
          if (this.state.drillDownServiceAdvisor.includes('All') == false) {
            resultArr.map(obj => {
              if (
                this.state.drillDownServiceAdvisor.indexOf(
                  obj.serviceadvisor
                ) != -1
              ) {
                filteredArr.push(obj);
              }
            });
            this.setState({
              rowData: filteredArr
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(
              filteredArr,
              this.state.chartId
            );
          } else {
            this.setState({
              rowData: resultArr
            });
            this.props.setDrillDownCount(true);
            DrilldownValues = myCustomSumAggregate(
              resultArr,
              this.state.chartId
            );
          }

          // var resultArr = this.parseArray(
          //   result.data.dmsDrilldownGetDrillDownShopSupplies.nodes
          // );
          // this.setState({
          //   rowData: resultArr
          // });
          // DrilldownValues = myCustomSumAggregate(resultArr, this.state.chartId);
          this.setDrillDownValuesToState(DrilldownValues);
          this.filterByPayTypeGroup();
        }
      });
    }
  }
  renderBackButton = () => {
    {
      window.sortState = {};
      window.filterState = {};
      localStorage.removeItem('storeIdSelected');
      const historyLocation = this.state.previousLocation
        ? this.state.previousLocation
        : this.props.prevPath
        ? this.props.prevPath
        : '';
      const selectedTab = this.props.history.location.state
        ? this.props.history.location.state.tabSelection
        : this.state.drillDownTabSelection
        ? this.state.drillDownTabSelection
        : '';
      const innerTabNumber = this.props.history.location.state
        ? this.props.history.location.state.innerTab
        : '';
      const showCurrentMonth = this.props.history.location.state
        ? this.props.history.location.state.showCurrentMonth
        : '';

      if (
        selectedTab &&
        this.state.chartId != 1090 &&
        this.state.chartId != 1096
      ) {
        this.props.history.push({
          pathname: historyLocation,
          state: {
            tabSelection: selectedTab,
            innerTab: innerTabNumber
          }
        });
      } else if (
        showCurrentMonth != undefined &&
        showCurrentMonth != '' &&
        showCurrentMonth == 'undefined'
      ) {
        this.props.history.push({
          pathname: historyLocation,
          state: {
            showCurrentMonth: showCurrentMonth
          }
        });
      } else if (historyLocation == window.location.pathname) {
        this.props.history.push('/PartsWorkMixAnalysis');
      } else {
        this.props.history.push(historyLocation);
      }
      // renderBackButtonForDrillDown(
      //   this.state.title,
      //   this.state.chartId,
      //   this.state.titleCategory,
      //   this.props.history,
      //   this.state.isParts
      // );
    }
  };

  handleStoreChange = event => {
    let storeId = event.target.dataset
      ? event.target.dataset['value']
      : event.target.value;
    if (storeId) {
      this.setState({ storeSelected: storeId });
      this.setState({ rowData: [] });
      this.setState({ isLoading: true });
      localStorage.setItem('storeIdSelected', storeId);
      this.getAgGridData(
        this.state.queryMonth,
        '',
        this.state.drillDownServiceAdvisor,
        storeId
      );
    }
  };
  handleRowSelection = (val, checked, rowId) => {
    val.rowId = rowId;
    console.log('val', val);
    if (checked) {
      this.setState(prevState => ({
        selectedRowData: [...prevState.selectedRowData, val]
        //isChecked: checked
      }));
    } else {
      let rowVal;

      rowVal = lodash.filter(this.state.selectedRowData, function(o) {
        return o.rowId != rowId;
      });

      console.log('rowVal', rowVal);
      this.setState(prevState => ({
        selectedRowData: rowVal
        //isChecked: checked
      }));
    }

    this.setState({ isSumChanged: true });
  };
  handleSumChanged = () => {
    let PartsSale = 0;
    let PartsCost = 0;
    let totalpartsSaleSum;
    let totalpartsCostSum;
    if (this.state.selectedRowData.length > 0) {
      this.state.selectedRowData.forEach(function(value) {
        PartsSale += parseFloat(value.prtextendedsale);
        PartsCost += parseFloat(value.prtextendedcost);
      });
      if (this.state.isColumnFiltered) {
        totalpartsSaleSum =
          Number(
            this.state.revPartsSaleSum.replace('$', '').replace(/,/g, '')
          ) - Number(PartsSale);
        totalpartsCostSum =
          Number(
            this.state.revPartsCostSum.replace('$', '').replace(/,/g, '')
          ) - Number(PartsCost);
      } else {
        totalpartsSaleSum =
          Number(this.state.partsSaleSum.replace('$', '').replace(/,/g, '')) -
          Number(PartsSale);
        totalpartsCostSum =
          Number(this.state.partsCostSum.replace('$', '').replace(/,/g, '')) -
          Number(PartsCost);
      }

      let totalPartsMarkup = totalpartsSaleSum / totalpartsCostSum;
      this.setState({
        revPartsSaleSum:
          '$' +
          (
            Math.round((totalpartsSaleSum + Number.EPSILON) * 100) / 100
          ).toLocaleString()
      });
      this.setState({
        revPartsCostSum:
          '$' +
          (
            Math.round((totalpartsCostSum + Number.EPSILON) * 100) / 100
          ).toLocaleString()
      });
      this.setState({
        revPrtsMarkupCustomer: totalPartsMarkup.toFixed(4)
      });

      this.setState({ isSumChanged: false });

      this.setState({ isSRowDataChanged: true });
    } else {
      let DrilldownValues = myCustomSumAggregateRevisedTotals(
        this.state.filteredRowData,
        this.state.chartId
      );
      this.setDrillDownValuesToStateRevisedTotals(DrilldownValues);
      this.setState({ isSumChanged: false });
      this.setState({ isSRowDataChanged: false });
    }
  };
  render() {
    let Dealer = process.env.REACT_APP_DEALER;
    let storeArr = [];
    storeArr = localStorage.getItem('storeData');
    const { classes } = this.props;
    console.log('chartId=', this.state.drillDown, this.state.chartId);
    if (this.state.isSumChanged) {
      this.handleSumChanged();
    }
    let topPadding;
    let labelColor;

    if (Dealer == 'Armatus') {
      labelColor = '#003d6b';
    } else {
      labelColor = '#C2185B';
    }
    if (this.state.isSRowDataChanged) {
      topPadding = 'padding-top: 8px';
    } else {
      topPadding = '';
    }
    let label = '';

    // const labelTitle =
    //   '<h5  style=" text-transform: uppercase;color: ' +
    //   labelColor +
    //   '; font-size:16px;margin-left:5px;">' +
    //   this.state.chartName +
    //   '</h5>';

    // if (this.state.drillDown == 48 && this.state.chartId == 1238) {
    //   label = '<div style="margin-left:217px;">';
    //   if (!this.state.isLoading && this.state.rowData.length > 0) {
    //     label +=
    //       '<div style="float: right;margin-right:13px"><label style="font-size: 16px;padding-top: 4px;"><span style="color:' +
    //       labelColor +
    //       '">Totals:</span> <span style="color:#757575;"> Parts Sale:</span> <span style="color:#000;margin-right:5px">' +
    //       this.state.partsSaleSum +
    //       '</span> <span style="color:#757575;">Parts Cost:</span> <span style="color:#000;margin-right:5px">' +
    //       this.state.partsCostSum +
    //       '</span> <span style="color:#757575;">Parts Markup - Customer Pay:</span> <span style="color:#000;margin-right:5px">' +
    //       this.state.prtsMarkupCustomer +
    //       '</span></label></div><br>';
    //   }
    //   if (this.state.isSRowDataChanged || this.state.isColumnFiltered) {
    //     label +=
    //       '<div style="float: right; margin-bottom: 5px"><label style="font-size: 16px;padding-top: 4px;margin-left:10px"><span style="color:' +
    //       labelColor +
    //       '">Revised Totals:</span> <span style="color:#757575;">Parts Sale:</span> <span style="color:#000;margin-right:5px">' +
    //       this.state.revPartsSaleSum +
    //       '</span> <span style="color:#757575;">Parts Cost:</span> <span style="color:#000;margin-right:5px">' +
    //       this.state.revPartsCostSum +
    //       '</span> <span style="color:#757575;">Parts Markup - Customer Pay:</span> <span style="color:#000;margin-right:5px">' +
    //       this.state.revPrtsMarkupCustomer +
    //       '</span></label></div>';
    //   }

    //   label += '</div></div>';
    // }
    console.log(
      'datavalues=',
      this.state.drillDown,
      this.state.type,
      this.state.category
    );
    // return (
    return this.state.isFromMetrices == true ? (
      <DrilldownReturnRate
        history={this.props.history}
        session={this.props.session}
      />
    ) : (
      <div className={classes.root}>
        <Paper
          square
          className={classes.paper}
          style={{
            margin: 8,
            marginTop: '20px',
            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
            style={{ height: 60 }}
          >
            <Link
              href="#"
              style={{ paddingTop: 20, paddingLeft: 16 }}
              title="Go Back"
              onClick={this.renderBackButton}
            >
              <ArrowBackIcon />
            </Link>
            {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
              <Select
                style={{ width: 150, fontWeight: 450, marginTop: 10 }}
                disableUnderline
                className={classes.select}
                inputProps={{
                  classes: {
                    icon: classes.icon
                  }
                }}
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                value={this.state.storeSelected}
                onClick={this.handleStoreChange}
              >
                {JSON.parse(localStorage.getItem('storeData')).map((val, i) => (
                  <MenuItem value={val['storeId']}>{val['storeName']}</MenuItem>
                ))}
              </Select>
            ) : (
              ''
            )}
            {/* <Tab
              //style={{ textTransform: 'none', paddingRight: 182 }}
              label={
                this.state.drillDown == 48 && this.state.chartId == 1238 ? (
                  ReactHtmlParser(label)
                ) : (
                  <div>{this.state.chartName}</div>
                )
              }
              value="one"
            /> */}
            <Grid color="primary" style={{ width: '95%', float: 'left' }}>
              {/* {this.state.drillDown == 48 && this.state.chartId == 1238 ? (
                // ReactHtmlParser(label)
                <div style={{ width: '100%', display: 'inline-flex' }}>
                  <Box
                    component="div"
                    display="inline"
                    flexGrow={1}
                    p={1}
                    m={1}
                    style={{ paddingTop: '15px' }}
                  >
                    <Typography
                      variant="h5"
                      component="h5"
                      color="primary"
                      style={{
                        paddingLeft: 5,
                        textTransform: 'uppercase'
                      }}
                    >
                      {this.state.chartName}
                    </Typography>
                  </Box>
                  <Box
                    component="div"
                    display="block"
                    p={1}
                    m={1}
                    bgcolor="background.paper"
                  >
                    <Table>
                      <TableRow>
                        <Typography
                          color="primary"
                          style={{
                            paddingLeft: 5,
                            fontSize: 12,
                            textTransform: 'uppercase'
                            //width: 103
                          }}
                        >
                          Totals
                          <span style={{ float: 'right' }}>:</span>
                        </Typography>

                        <SummaryTitleTotals
                          title={'Parts Sale'}
                          value={this.state.partsSaleSum}
                        />
                        <SummaryTitleTotals
                          title={'Parts Cost'}
                          value={this.state.partsCostSum}
                        />
                        <SummaryTitleTotals
                          title={'Parts Markup'}
                          value={this.state.prtsMarkupCustomer}
                        />
                      </TableRow>
                      {this.state.isSRowDataChanged ||
                      this.state.isColumnFiltered ? (
                        <TableRow>
                          <Typography
                            color="primary"
                            style={{
                              paddingLeft: 5,
                              fontSize: 12,
                              textTransform: 'uppercase'
                            }}
                          >
                            Revised Totals :{' '}
                          </Typography>
                          <SummaryTitleTotals
                            title={'Parts Sale'}
                            value={this.state.revPartsSaleSum}
                          />
                          <SummaryTitleTotals
                            title={'Parts Cost'}
                            value={this.state.revPartsCostSum}
                          />
                          <SummaryTitleTotals
                            title={'Parts Markup'}
                            value={this.state.revPrtsMarkupCustomer}
                          />
                        </TableRow>
                      ) : null}
                    </Table>
                  </Box>
                </div>
              ) : ( */}
              <Typography
                variant="h5"
                component="h5"
                color="primary"
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  textTransform: 'uppercase',
                  marginTop: 20
                }}
              >
                {this.state.chartName}
              </Typography>
              {/* )} */}
            </Grid>
            <Tooltip title="Export To Excel">
              <Link
                style={{
                  paddingTop: 12,
                  paddingRight: 27,
                  cursor: 'pointer',
                  float: 'right'
                }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
          </Tabs>
        </Paper>
        {this.state.isLoading && (
          <div
            style={{
              height:
                this.state.chartId == 1089 ||
                this.state.chartId == 1090 ||
                this.state.chartId == 1095 ||
                this.state.chartId == 1096 ||
                this.state.drillDown == 35 ||
                this.state.drillDown == 34 ||
                (this.state.drillDown == 41 &&
                  this.state.type != 'discount_drilldown') ||
                this.state.drillDown == 42 ||
                this.state.drillDown == 45 ||
                this.state.drillDown == 43 ||
                this.state.drillDown == 44
                  ? window.innerHeight - 150 + 'px'
                  : window.innerHeight - 210 + 'px',
              width: '100%',
              margin: 8,
              display: this.state.tabSelection == 'two' ? 'none' : 'block'
            }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              this.state.chartId == 1089 ||
              this.state.chartId == 1090 ||
              this.state.chartId == 1095 ||
              this.state.chartId == 1096 ||
              this.state.drillDown == 35 ||
              this.state.drillDown == 34
                ? window.innerHeight - 150 + 'px'
                : window.innerHeight - 250 + 'px',
            width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            floatingFilter={true}
            enableRangeSelection={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            suppressRowClickSelection={true}
            onFilterChanged={this.onFilterChanged}
            onFilterModified={this.onFilterModified}
            onSelectionChanged={this.onSelectionChanged}
            sideBar={this.state.sideBar}
            frameworkComponents={this.state.frameworkComponents}
            context={this.state.context}
            rowSelection="multiple"
            columnDefs={
              this.state.drillDown == 25 || this.state.drillDown == 26
                ? this.state.columnDefsAddOns
                : this.state.drillDown == 9
                ? this.state.columnDefsPrtsMarkup
                : this.state.drillDown == 18
                ? this.state.columnDefsSingleJob
                : this.state.drillDown == 22
                ? this.state.columnDefsActualHrs
                : this.state.drillDown == 14
                ? this.state.columnDefsMovingElr
                : this.state.drillDown == 15
                ? this.state.columnDefsMovingMarkup
                : this.state.drillDown == 16
                ? this.state.columnDefsItemization
                : this.state.drillDown == 17
                ? this.state.columnDefsPrtsItemization
                : this.state.drillDown == 41 &&
                  this.state.type != 'discount_drilldown'
                ? this.state.columnDefsLaborPartsByMonth
                : this.state.drillDown == 42
                ? this.state.columnDefsDiscountedSalePercentage
                : this.state.drillDown == 43
                ? this.state.columnDefsDiscountsPerTotalCPROs
                : this.state.drillDown == 44
                ? this.state.columnDefsDiscountsPerTotalDiscountedCPROs
                : this.state.drillDown == 45
                ? this.state.columnDefsDiscountedCPPerTotalDiscountedCPROs
                : this.state.drillDown == 32
                ? this.state.columnDefsDiscountSA
                : (this.state.drillDown == 30 &&
                    (this.state.category == 0 ||
                      this.state.category == 2 ||
                      this.state.category == 3)) ||
                  this.state.drillDown == 34 ||
                  (this.state.drillDown == 31 &&
                    this.state.category == ' Labor Discount') ||
                  (this.state.drillDown == 36 && this.state.category == 0) ||
                  (this.state.drillDown == 40 && this.state.category == 0)
                ? this.state.columnDefsDisSummaryLbr
                : (this.state.drillDown == 30 && this.state.category == 1) ||
                  this.state.drillDown == 35 ||
                  (this.state.drillDown == 31 &&
                    this.state.category == ' Parts Discount') ||
                  (this.state.drillDown == 36 && this.state.category == 1) ||
                  (this.state.drillDown == 40 && this.state.category == 1)
                ? this.state.columnDefsDisSummaryPrts
                : (this.state.drillDown == 33 && this.state.category == 16) ||
                  (this.state.drillDown == 30 &&
                    (this.state.chartId == 1123 || this.state.chartId == 1124))
                ? this.state.columnDefsDiscountByRO
                : this.state.drillDown == 37 &&
                  this.state.chartId != 1175 &&
                  this.state.drillDownServiceAdvisor.includes('All') == true
                ? this.state.columnDefsAverageHrs
                : this.state.drillDown == 37 &&
                  this.state.chartId != 1175 &&
                  this.state.drillDownServiceAdvisor.includes('All') == false
                ? this.state.columnDefsAverageHrsSa
                : this.state.drillDown == 46
                ? this.state.columnDefsShopSupplies
                : this.state.drillDown == 48
                ? this.state.columnDefsPartsMarkups
                : this.state.columnDefsRevenue
            }
            // : this.state.drillDown == 33
            // ? this.state.columnDefsDiscountJobRo
            // : this.state.drillDown == 31
            // ? this.state.columnDefsDiscount
            defaultColDef={this.state.defaultColDef}
            // sideBar={true}
            excelStyles={this.state.excelStyles}
            enableRangeSelection={true}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            headerHeight={this.state.headerHeight}
            tooltipShowDelay={0}
            suppressContextMenu={true}
          />
        </div>
        {!this.state.isLoading && this.state.rowData.length > 0 && (
          <Paper square style={{ margin: 8 }}>
            <div className={classes.root}>
              <Grid container spacing={3} style={{ margin: -8 }}>
                {(this.state.drillDown == 1 ||
                  this.state.drillDown == 5 ||
                  this.state.drillDown == 0) &&
                (this.state.category == 0 || this.state.category == 1) &&
                this.state.chartId != 970 &&
                this.state.chartId != 968 &&
                this.state.chartId != 1002 &&
                this.state.chartId != 942 &&
                this.state.chartId != 920 &&
                this.state.chartId != 941 &&
                this.state.chartId != 1067 &&
                this.state.chartId != 1133 &&
                this.state.chartId != 1134 &&
                this.state.chartId != 1135 &&
                this.state.chartId != 1137 &&
                this.state.chartId != 971 &&
                this.state.chartId != 1318 &&
                this.state.chartId != 1326 &&
                this.state.chartId != 1334 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctRO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.chartId == 1326 ? (
                  <SummaryTitle
                    title={'RO Count - Parts Only'}
                    value={this.state.distinctROPartsOnly}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 18 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Total RO Count'}
                    value={this.state.rowDataLength}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 18 &&
                (this.state.category == 0 || this.state.category == 1) ? (
                  <SummaryTitle
                    title={'CP 1-Line-RO Count'}
                    value={this.state.distinctRO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {(this.state.drillDown == 30 && this.state.category == 16) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 0 &&
                  (this.state.chartId == 1198 ||
                    this.state.chartId == 1212 ||
                    this.state.chartId == 1219 ||
                    this.state.chartId == 1320)) ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctRO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 18 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'CP 1-Line-RO Count %'}
                    value={this.state.ROPercent}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 12 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctRO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 0 && this.state.chartId == 1322 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.distinctROI}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours - Internal'}
                      value={this.state.ShInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO - Internal'}
                      value={this.state.LHROInteranl}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {(this.state.drillDown == 1 &&
                  this.state.category == 3 &&
                  this.state.chartId != 969 &&
                  this.state.chartId != 1136) ||
                (this.state.drillDown == 12 && this.state.category == 3) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 3 &&
                  (this.state.chartId == 1199 ||
                    this.state.chartId == 1213 ||
                    this.state.chartId == 1220)) ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROW}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 1 &&
                  this.state.category == 4 &&
                  this.state.chartId != 970 &&
                  this.state.chartId != 1137) ||
                (this.state.drillDown == 12 && this.state.category == 4) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 4 &&
                  (this.state.chartId == 1200 ||
                    this.state.chartId == 1214 ||
                    this.state.chartId == 1221)) ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROI}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 1 &&
                  (this.state.category == 5 ||
                    (this.state.category == 4 && this.state.chartId != 1330)) &&
                  this.state.chartId != 970 &&
                  this.state.chartId != 968 &&
                  this.state.chartId != 1134 &&
                  this.state.chartId != 1137 &&
                  this.state.chartId != 1035 &&
                  this.state.chartId != 1147 &&
                  this.state.chartId != 1142) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 5 &&
                  (this.state.chartId == 1197 ||
                    this.state.chartId == 1211 ||
                    this.state.chartId == 1218)) ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROC}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 12 && this.state.category == 5 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROC}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 && this.state.chartId == 1321 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.distinctROW}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours - Warranty'}
                      value={this.state.ShWarranty}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO - Warranty'}
                      value={this.state.LHROWarranty}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {/* {this.state.drillDown == 12 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROR}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null} */}
                {this.state.drillDown == 23 && this.state.category == 6 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctCPPROR}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 12 &&
                this.state.category == 7 &&
                this.state.chartId != 1162 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROM}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 23 && this.state.category == 7 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctCPPROM}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {/* {this.state.drillDown == 12 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROComp}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null} */}
                {this.state.drillDown == 23 && this.state.category == 8 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctCPPROComp}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 12 && this.state.category == 10 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.distinctROCmbPerRo}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {/*  {this.state.drillDown == 5 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.lbrSaleperRo}
                  />
                ) : null} */}
                {/* {this.state.drillDown == 5 && this.state.category == 12 ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.RoCountAll}
                  />
                ) : null} */}
                {this.state.drillDown == 6 &&
                (this.state.category == 0 || this.state.category == 12) ? (
                  <SummaryTitle
                    title={'Job Count'}
                    value={this.state.jobCount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 ||
                  this.state.drillDown == 2 ||
                  this.state.drillDown == 3 ||
                  this.state.drillDown == 4 ||
                  this.state.drillDown == 5 ||
                  this.state.drillDown == 20 ||
                  this.state.drillDown == 18) &&
                  (this.state.category == 0 ||
                    this.state.category == 2 ||
                    this.state.category == 12) &&
                  this.state.chartId != 1145) ||
                (this.state.drillDown == 38 && this.state.category == 0) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 0 &&
                  (this.state.chartId == 1191 ||
                    this.state.chartId == 1212)) ? (
                  <SummaryTitle
                    title={'Total Labor Sale'}
                    value={this.state.laborSaleSum}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 ||
                  this.state.drillDown == 3 ||
                  this.state.drillDown == 2 ||
                  this.state.drillDown == 4) &&
                  (this.state.category == 0 ||
                    this.state.category == 2 ||
                    this.state.category == 12) &&
                  this.state.chartId != 1145) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 0 &&
                  this.state.chartId == 1191) ? (
                  <SummaryTitle
                    title={'Total Labor Cost'}
                    value={this.state.laborCostSum}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 ||
                  this.state.drillDown == 3 ||
                  this.state.drillDown == 4 ||
                  this.state.drillDown == 5 ||
                  this.state.drillDown == 48 ||
                  this.state.drillDown == 9) &&
                  (this.state.category == 1 || this.state.category == 2) &&
                  this.state.chartId != 1145) ||
                this.state.drillDown == 20 ? (
                  <SummaryTitle
                    title={
                      this.state.chartId == 1334
                        ? 'Total Parts Sale - Repair and Competitive'
                        : 'Total Parts Sale'
                    }
                    value={this.state.partsSaleSum}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 1 ||
                  this.state.drillDown == 3 ||
                  this.state.drillDown == 4 ||
                  this.state.drillDown == 48 ||
                  this.state.drillDown == 9) &&
                (this.state.category == 1 || this.state.category == 2) &&
                this.state.chartId != 1145 ? (
                  <SummaryTitle
                    title={
                      this.state.chartId == 1334
                        ? 'Total Parts Cost - Repair and Competitive'
                        : 'Total Parts Cost'
                    }
                    value={this.state.partsCostSum}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 5 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO'}
                    value={this.state.lbrSaleperRo}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 5 && this.state.category == 6 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale/RO-Repair'}
                      value={this.state.lbrSaleperRoR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'RO Count-Repair'}
                      value={this.state.distinctRORS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 5 && this.state.category == 7 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale/RO-Maintenance'}
                      value={this.state.lbrSaleperRoM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'RO Count-Maintenance'}
                      value={this.state.distinctROMS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 5 && this.state.category == 8 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count-Competitive'}
                      value={this.state.distinctROCompS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sale/RO-Competitive'}
                      value={this.state.lbrSaleperRoC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 5 && this.state.category == 11 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale-Shop Supplies'}
                      value={this.state.saleShopSupplies}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'RO Count-Shop Supplies'}
                      value={this.state.distinctROSS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />

                    <SummaryTitle
                      title={'Labor Sale/RO-Shop Supplies'}
                      value={this.state.lbrSaleperRoS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 5 && this.state.category == 12 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count-All Categories'}
                      value={this.state.roCountAll}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sale/RO-All Categories'}
                      value={this.state.lbrSaleperRoAll}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 0 &&
                this.state.category == 0 &&
                this.state.chartId != 1318 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO'}
                    value={this.state.lbrHoursPerRO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 0 && this.state.chartId == 1318 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.prtsHrsRoCount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours'}
                      value={this.state.prtsHours}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO'}
                      value={this.state.prtsHoursPerRO}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 0 && this.state.category == 10 ? (
                  <SummaryTitle
                    title={'Labor Hours-All Categories'}
                    value={this.state.lbrHoursAll}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 && this.state.category == 10 ? (
                  <SummaryTitle
                    title={'RO Count-All Categories'}
                    value={this.state.roCountAllCat}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 &&
                this.state.category == 6 &&
                this.state.chartId != 1079 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Repair'}
                      value={this.state.roCountR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - Repair'}
                      value={this.state.hoursRepair}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Hours/RO-Repair'}
                      value={this.state.lbrHrsPerRoR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 0 &&
                this.state.category == 7 &&
                this.state.chartId != 1079 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO-Maintenance'}
                    value={this.state.lbrHrsPerRoM}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 &&
                this.state.category == 7 &&
                this.state.chartId != 1079 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours-Maintenance'}
                    value={this.state.hoursMaintenance}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 &&
                this.state.category == 7 &&
                this.state.chartId != 1079 ? (
                  <SummaryTitle
                    title={'RO Count-Maintenance'}
                    value={this.state.distinctROM}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 &&
                this.state.category == 8 &&
                this.state.chartId != 1079 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Hours/RO-Competitive'}
                      value={this.state.lbrHrsPerRoC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - Competitive'}
                      value={this.state.hoursCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />{' '}
                    <SummaryTitle
                      title={'RO Count-Competitive'}
                      value={this.state.distinctROComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {this.state.drillDown == 0 &&
                this.state.category == 6 &&
                this.state.chartId == 1079 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count'}
                      value={this.state.distinctROR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours-Repair'}
                      value={this.state.hoursRepair}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Hours/RO % By-Repair'}
                      value={this.state.lbrHrsPerRoRPrcnt}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 0 &&
                this.state.category == 7 &&
                this.state.chartId == 1079 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count'}
                      value={this.state.distinctROM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours-Maintenance '}
                      value={this.state.hoursMaintenance}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Hours/RO % By-Maintenance '}
                      value={this.state.lbrHrsPerRoMPrcnt}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {this.state.drillDown == 0 &&
                this.state.category == 8 &&
                this.state.chartId == 1079 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count'}
                      value={this.state.distinctROComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours-Competitive'}
                      value={this.state.hoursCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Hours/RO % By-Competitive'}
                      value={this.state.lbrHrsPerRoCPrcnt}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {this.state.drillDown == 0 && this.state.category == 10 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO-All Categories'}
                    value={this.state.lbrHrsPerRoAll}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 5 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Parts Sale/RO'}
                    value={this.state.prtSaleperRo}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Parts Markup'}
                    value={this.state.partsMarkup}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.chartId == 1334 ? (
                  <SummaryTitle
                    title={'Parts Markup - Repair and Competitive'}
                    value={this.state.partsMarkupRC}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 ||
                  (this.state.drillDown == 0 && this.state.chartId != 1318) ||
                  this.state.drillDown == 3 ||
                  this.state.drillDown == 2 ||
                  this.state.drillDown == 4) &&
                  (this.state.category == 0 ||
                    this.state.category == 2 ||
                    this.state.category == 12) &&
                  this.state.chartId != 1145) ||
                (this.state.drillDown == 38 && this.state.category == 0) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 0 &&
                  (this.state.chartId == 1191 ||
                    this.state.chartId == 1198)) ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Customer Pay'}
                    value={this.state.laborHoursSum}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 2 &&
                (this.state.category == 0 || this.state.category == 2) ? (
                  <SummaryTitle
                    title={'ELR'}
                    value={this.state.elr}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 7 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Labor Tech Hours'}
                    value={this.state.lbrTechHr}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 &&
                (this.state.category == 0 || this.state.category == 2) ? (
                  <SummaryTitle
                    title={'Labor Gross Profit'}
                    value={this.state.laborGP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 && this.state.category == 3 ? (
                  <SummaryTitle
                    title={'Labor Gross Profit-Warranty'}
                    value={this.state.laborGPW}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 && this.state.category == 4 ? (
                  <SummaryTitle
                    title={'Labor Gross Profit-Internal'}
                    value={this.state.laborGPI}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 && this.state.category == 5 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale-Combined'}
                      value={this.state.laborSaleCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Cost-Combined'}
                      value={this.state.laborCostCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Gross Profit-Combined'}
                      value={this.state.laborGPCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 3 &&
                (this.state.category == 1 || this.state.category == 2) ? (
                  <SummaryTitle
                    title={'Parts Gross Profit'}
                    value={this.state.partsGP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 4 &&
                (this.state.category == 0 || this.state.category == 2) ? (
                  <SummaryTitle
                    title={'Labor Gross Profit %'}
                    value={this.state.laborGPPerc}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 4 &&
                (this.state.category == 1 || this.state.category == 2) ? (
                  <SummaryTitle
                    title={'Parts Gross Profit %'}
                    value={this.state.partsGPPerc}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 || this.state.drillDown == 3) &&
                  this.state.category == 3 &&
                  this.state.chartId != 1329 &&
                  this.state.chartId != 1146) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 3 &&
                  this.state.chartId == 1192) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.LsWarranty}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Cost'}
                      value={this.state.LcWarranty}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 38 && this.state.category == 3) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 3 &&
                  this.state.chartId == 1213) ? (
                  <SummaryTitle
                    title={'Total Labor Sale'}
                    value={this.state.LsWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 1 &&
                  this.state.category == 3 &&
                  this.state.chartId != 1074 &&
                  this.state.chartId != 1139 &&
                  this.state.chartId != 1144 &&
                  this.state.chartId != 1327 &&
                  this.state.chartId != 1329 &&
                  this.state.chartId != 1146) ||
                (this.state.drillDown == 38 && this.state.category == 3) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 3 &&
                  (this.state.chartId == 1192 ||
                    this.state.chartId == 1199)) ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Warranty'}
                    value={this.state.ShWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 &&
                this.state.category == 3 &&
                this.state.chartId != 1007 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours Warranty'}
                    value={this.state.ShWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 1 &&
                this.state.category == 2 &&
                (this.state.chartId == 942 || this.state.chartId == 1068) ? (
                  <SummaryTitle
                    title={'Combined Revenue'}
                    value={this.state.laborSaleSumCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 1 &&
                this.state.category == 2 &&
                this.state.chartId != 942 &&
                this.state.chartId != 960 &&
                this.state.chartId != 1068 ? (
                  <SummaryTitle
                    title={'Combined Gross Profit'}
                    value={this.state.laborSaleSumCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 1 &&
                this.state.category == 2 &&
                this.state.chartId != 942 &&
                this.state.chartId != 960 &&
                this.state.chartId != 1068 ? (
                  <SummaryTitle
                    title={'Combined Gross Profit %'}
                    value={this.state.laborPercetSumCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 4 &&
                this.state.category == 2 &&
                this.state.chartId != 942 ? (
                  <SummaryTitle
                    title={'Combined Gross Profit %'}
                    value={this.state.laborPercetSumCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 && this.state.category == 2 ? (
                  <SummaryTitle
                    title={'Combined Gross Profit'}
                    value={this.state.lsGrossSumCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 || this.state.drillDown == 3) &&
                  this.state.category == 4 &&
                  this.state.chartId != 1330 &&
                  this.state.chartId != 1147) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 4 &&
                  this.state.chartId == 1193) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.LsInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Cost'}
                      value={this.state.LcInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 38 && this.state.category == 4) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 4 &&
                  this.state.chartId == 1214) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.LsInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours Internal'}
                      value={this.state.ShInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {(this.state.drillDown == 3 && this.state.category == 4) ||
                (this.state.drillDown == 1 &&
                  this.state.category == 4 &&
                  this.state.chartId != 1007 &&
                  this.state.chartId != 1074 &&
                  this.state.chartId != 1139 &&
                  this.state.chartId != 1144 &&
                  this.state.chartId != 1330 &&
                  this.state.chartId != 1147 &&
                  this.state.chartId != 1327) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 4 &&
                  this.state.chartId == 1193) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 4 &&
                  this.state.chartId == 1200) ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Internal'}
                    value={this.state.ShInternal}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 1 &&
                this.state.category == 5 &&
                this.state.chartId != 1074 &&
                this.state.chartId != 1139 &&
                this.state.chartId != 1144 &&
                this.state.chartId != 1327 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Combined'}
                    value={this.state.LsCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 2 ||
                  this.state.drillDown == 5 ||
                  this.state.drillDown == 1 ||
                  this.state.drillDown == 21) &&
                this.state.category == 6 ? (
                  <SummaryTitle
                    title={'Labor Sale - Repair'}
                    value={this.state.saleRepair}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 1 && this.state.category == 6 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Cost - Repair'}
                      value={this.state.costRepair}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - Repair'}
                      value={this.state.hoursRepair}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 1 || this.state.drillDown == 2) &&
                this.state.category == 6 &&
                this.state.chartId != 1020 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Repair'}
                      value={this.state.hoursRepair}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Effective Labor Rate - Repair'}
                      value={this.state.elrRepair}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 2 ||
                  this.state.drillDown == 5 ||
                  this.state.drillDown == 1 ||
                  this.state.drillDown == 21) &&
                this.state.category == 7 &&
                this.state.chartId != 1148 &&
                this.state.chartId != 1150 &&
                this.state.chartId != 1154 &&
                this.state.chartId != 1155 &&
                this.state.chartId != 1156 &&
                this.state.chartId != 1157 &&
                this.state.chartId != 1331 ? (
                  <SummaryTitle
                    title={'Labor Sale - Maintenance'}
                    value={this.state.saleMaintenance}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 1 &&
                this.state.category == 7 &&
                this.state.chartId != 1331 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Cost - Maintenance'}
                      value={this.state.costMaintenance}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* <SummaryTitle
                      title={'Labor Sold Hours - Maintenance'}
                      value={this.state.hoursMaintenance}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    /> */}
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 1 || this.state.drillDown == 2) &&
                this.state.category == 7 &&
                this.state.chartId != 1148 &&
                this.state.chartId != 1150 &&
                this.state.chartId != 1154 &&
                this.state.chartId != 1155 &&
                this.state.chartId != 1156 &&
                this.state.chartId != 1157 &&
                this.state.chartId != 1019 &&
                this.state.chartId != 1331 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Maintenance'}
                      value={this.state.hoursMaintenance}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Effective Labor Rate - Maintenance'}
                      value={this.state.elrMaintenance}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 2 ||
                  this.state.drillDown == 5 ||
                  this.state.drillDown == 1 ||
                  this.state.drillDown == 21) &&
                this.state.category == 8 ? (
                  <SummaryTitle
                    title={'Labor Sale - Competitive'}
                    value={this.state.saleCompetitive}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 1 && this.state.category == 8 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Cost - Competitive'}
                      value={this.state.costCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - Competitive'}
                      value={this.state.hoursCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 1 || this.state.drillDown == 2) &&
                this.state.category == 8 &&
                this.state.chartId != 1018 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Competitive'}
                      value={this.state.hoursCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Effective Labor Rate - Competitive'}
                      value={this.state.elrCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 1 || this.state.drillDown == 2) &&
                this.state.category == 9 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale - Repair & Competitive'}
                      value={this.state.saleRepairCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - Repair & Competitive'}
                      value={this.state.hoursRepairCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Effective Labor Rate - Repair & Competitive'}
                      value={this.state.elrRepairCompetitive}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 1 || this.state.drillDown == 2) &&
                this.state.category == 10 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale - All Categories'}
                      value={this.state.saleAllCategories}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - All Categories'}
                      value={this.state.hoursAllCategories}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 6 && this.state.category == 6 ? (
                  <SummaryTitle
                    title={'Job Count -Repair'}
                    value={this.state.jobCountR}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 6 && this.state.category == 7 ? (
                  <SummaryTitle
                    title={'Job Count -Maintenance'}
                    value={this.state.jobCountM}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 6 && this.state.category == 8 ? (
                  <SummaryTitle
                    title={'Job Count -Competitive'}
                    value={this.state.jobCountCO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 6 && this.state.category == 11 ? (
                  <SummaryTitle
                    title={'Job Count -Shop Supplies'}
                    value={this.state.jobCountS}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 6 && this.state.category == 17 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Job Count % - Repair'}
                      value={this.state.jobCountAllR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Job Count % - Maintenance'}
                      value={this.state.jobCountAllM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Job Count % - Competitive'}
                      value={this.state.jobCountAllC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Job Count % - Shop Supplies'}
                      value={this.state.jobCountAllS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 1 || this.state.drillDown == 2) &&
                this.state.category == 10 ? (
                  <SummaryTitle
                    title={'Effective Labor Rate - All Categories'}
                    value={this.state.elrAllCategories}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 8 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.lbrSaleRepairCompet}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Hours'}
                      value={this.state.lbrHrsRepairCompet}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'ELR Repair & Competitive'}
                      value={this.state.elrRepairCompet}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {((this.state.drillDown == 10 || this.state.drillDown == 11) &&
                  this.state.category == 5) ||
                (this.state.drillDown == 48 &&
                  this.state.category == 5 &&
                  this.state.chartId == 1225) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.psSumComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.pcSumComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1218 ? (
                  <SummaryTitle
                    title={'Parts Sale - Combined'}
                    value={this.state.psSumComp}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 10 ||
                  this.state.drillDown == 11 ||
                  this.state.drillDown == 12) &&
                  this.state.category == 3) ||
                (this.state.drillDown == 48 &&
                  this.state.category == 3 &&
                  this.state.chartId == 1227) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.PsWarranty}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.PcWarranty}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 3 &&
                this.state.chartId == 1220 ? (
                  <SummaryTitle
                    title={'Parts Sale - Warranty'}
                    value={this.state.PsWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 10 ||
                  this.state.drillDown == 11 ||
                  this.state.drillDown == 12) &&
                  this.state.category == 4) ||
                (this.state.drillDown == 48 &&
                  this.state.category == 4 &&
                  this.state.chartId == 1228) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.PsInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.PcInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 4 &&
                this.state.chartId == 1221 ? (
                  <SummaryTitle
                    title={'Parts Sale - Internal'}
                    value={this.state.PsInternal}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 11 && this.state.category == 1) ||
                (this.state.drillDown == 12 && this.state.category == 0) ||
                (this.state.drillDown == 48 &&
                  this.state.category == 0 &&
                  this.state.chartId == 1226) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.partsSaleSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.partsCostSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 0 &&
                this.state.chartId == 1219 ? (
                  <SummaryTitle
                    title={'Parts Sale - Customer Pay'}
                    value={this.state.partsSaleSum}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 11 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Parts Gross Profit - Customer Pay'}
                    value={this.state.partsGP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 11 && this.state.category == 3 ? (
                  <SummaryTitle
                    title={'Parts Gross Profit - Warranty'}
                    value={this.state.prtGpWar}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 11 && this.state.category == 4 ? (
                  <SummaryTitle
                    title={'Parts Gross Profit - Internal'}
                    value={this.state.prtGpInt}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 11 && this.state.category == 5 ? (
                  <SummaryTitle
                    title={'Parts Gross Profit - Combined'}
                    value={this.state.prtGpCombin}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 48 || this.state.drillDown == 12) &&
                this.state.category == 10 ? (
                  <SummaryTitle
                    title={'Total Parts Sale'}
                    value={this.state.prtsaleCmb}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 9 && this.state.category == 10 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.prtcostCmb}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'CP Parts Markup - All Categories'}
                      value={this.state.partsMarkupCmb}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 23 && this.state.category == 8 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.prtsaleComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'CP Parts Revenue Per RO by Competitive'}
                      value={this.state.prtRoByComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 21 && this.state.category == 8 ? (
                  <SummaryTitle
                    title={'Parts Sale - Competitive'}
                    value={this.state.prtsaleComp}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 23 && this.state.category == 7 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.prtsaleM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'CP Parts Revenue Per RO by Maintenance'}
                      value={this.state.prtRoByM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 21 && this.state.category == 7 ? (
                  <SummaryTitle
                    title={'Parts Sale - Maintenance Plan'}
                    value={this.state.prtsaleM}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 23 && this.state.category == 6 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.prtsaleR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'CP Parts Revenue Per RO by Repair'}
                      value={this.state.prtRoByR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 21 && this.state.category == 6 ? (
                  <SummaryTitle
                    title={'Parts Sale - Repair'}
                    value={this.state.prtsaleR}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 12 && this.state.category == 10 ? (
                  <SummaryTitle
                    title={'CP Parts Revenue Per RO - All Categories'}
                    value={this.state.prtRoByCmb}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 9 && this.state.category == 6 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Parts Sale - Repair'}
                      value={this.state.prtsaleR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Cost - Repair'}
                      value={this.state.prtcostR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Markup - Repair'}
                      value={this.state.partsMarkupR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 9 && this.state.category == 7 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Parts Sale - Maintenance'}
                      value={this.state.prtsaleM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Cost - Maintenance'}
                      value={this.state.prtcostM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Markup - Maintenance'}
                      value={this.state.partsMarkupM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 9 && this.state.category == 8 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Parts Sale - Competitive'}
                      value={this.state.prtsaleComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Cost - Competitive'}
                      value={this.state.prtcostComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Markup - Competitive'}
                      value={this.state.partsMarkupC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 12 && this.state.category == 5 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Parts Sale - Combined'}
                      value={this.state.prtsaleAll}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Cost - Combined'}
                      value={this.state.prtcostAll}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 1 &&
                this.state.category == 5 &&
                this.state.chartId != 1144 &&
                this.state.chartId != 1327 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale - Combined'}
                      value={this.state.lbrSaleC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Cost - Combined'}
                      value={this.state.lbrCostC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 20 ? (
                  <SummaryTitle
                    title={'Parts To Labor Ratio'}
                    value={this.state.partsToLaborRatio}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 21 && this.state.category == 7 ? (
                  <SummaryTitle
                    title={'Parts To Labor Ratio - Maintenance'}
                    value={this.state.prtsToLbrRatioM}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 21 && this.state.category == 8 ? (
                  <SummaryTitle
                    title={'Parts To Labor Ratio - Competitive'}
                    value={this.state.prtsToLbrRatioC}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 21 && this.state.category == 6 ? (
                  <SummaryTitle
                    title={'Parts To Labor Ratio - Repair'}
                    value={this.state.prtsToLbrRatioR}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 19 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - All Categories'}
                    value={this.state.SHByCategory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 19 && this.state.category == 0 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Customer Pay'}
                      value={this.state.laborHoursSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours % - Customer Pay'}
                      value={this.state.sHByCategoryC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 19 && this.state.category == 19 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Extended Service'}
                      value={this.state.shExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours % - Extended Service'}
                      value={this.state.sHByCategoryE}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 19 && this.state.category == 4 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Internal'}
                      value={this.state.ShInternal}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours % - Internal'}
                      value={this.state.sHByCategoryI}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 19 && this.state.category == 7 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Maintenance'}
                      value={this.state.shMaintenance1}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours % - Maintenance'}
                      value={this.state.sHByCategoryM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 19 && this.state.category == 3 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours - Warranty'}
                      value={this.state.ShWarranty}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours % - Warranty'}
                      value={this.state.sHByCategoryW}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 22 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours'}
                    value={this.state.flatrateHours}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 22 ? (
                  <SummaryTitle
                    title={'Flat Rate Hours'}
                    value={this.state.flatrateHours}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 22 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Tech Hours'}
                    value={this.state.techHours}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 22 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Delta - Sold Hours And Flat Rate Hours'}
                    value={this.state.deltaShFh}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 22 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Delta - Labor Tech Hours and Flat Rate Hours'}
                    value={this.state.deltaThFh}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 25 && this.state.chartId == 1099) ||
                (this.state.drillDown == 26 && this.state.chartId == 1118) ? (
                  <SummaryTitle
                    title={'Add On Job Count'}
                    value={this.state.jobCountAddOn}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 25 &&
                this.state.chartId == 1100 &&
                this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Non Add On Job Count'}
                    value={this.state.jobCountNonAddOn}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 25 &&
                this.state.chartId == 1100 &&
                this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Add On Job Count'}
                    value={this.state.jobCountAddOn}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1101 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On Job Count/RO'}
                      value={this.state.jobCountAddOnPerRo}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1102 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On RO Count %'}
                      value={this.state.ROCountPercAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1104 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On RO Count'}
                      value={this.state.distinctROCountAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Non Add On RO Count'}
                      value={this.state.distinctROCountNonAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1103 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On Job Count-Repair'}
                      value={this.state.jobCountAddOnR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Job Count-Competitive'}
                      value={this.state.jobCountAddOnC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Job Count-Maintenance'}
                      value={this.state.jobCountAddOnM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Job Count-Shop Supplies'}
                      value={this.state.jobCountAddOnS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 25 && this.state.chartId == 1106) ||
                (this.state.drillDown == 26 && this.state.chartId == 1119) ? (
                  <SummaryTitle
                    title={'Labor Revenue-Add On'}
                    value={this.state.lbrSaleAddOn}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 25 && this.state.chartId == 1107) ||
                (this.state.drillDown == 26 && this.state.chartId == 1120) ? (
                  <SummaryTitle
                    title={'Parts Revenue-Add On'}
                    value={this.state.prtSaleAddOn}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 26 && this.state.chartId == 1122 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Revenue-Add On'}
                      value={this.state.lbrSaleAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Revenue-Add On'}
                      value={this.state.prtSaleAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 26 && this.state.chartId == 1121 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Revenue % - Add On'}
                      value={this.state.addOnRevenueperc}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Revenue % - Non Add On'}
                      value={this.state.nonAddOnRevenueperc}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {this.state.drillDown == 25 && this.state.chartId == 1105 ? (
                  <SummaryTitle
                    title={'Total Revenue-Add On'}
                    value={this.state.totalRevAddon}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1108 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sold Hours-Add On'}
                      value={this.state.lbrSoldhrsAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours-Non Add On'}
                      value={this.state.lbrSoldhrsNonAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1109 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On Labor Sale-Repair'}
                      value={this.state.lbrSaleRAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Labor Sale-Competitive'}
                      value={this.state.lbrSaleCAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Labor Sale-Maintenance'}
                      value={this.state.lbrSaleMAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* {this.state.lbrSaleSAddon != null ? ( */}
                    <SummaryTitle
                      title={'Add On Labor Sale-Shop Supplies'}
                      value={this.state.lbrSaleSAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* ) : null} */}
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1110 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On Parts Sale-Repair'}
                      value={this.state.prtSaleRAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Parts Sale-Competitive'}
                      value={this.state.prtSaleCAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Parts Sale-Maintenance'}
                      value={this.state.prtSaleMAddon}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* {this.state.prtSaleSAddon != null ? ( */}
                    <SummaryTitle
                      title={'Add On Parts Sale-Shop Supplies'}
                      value={this.state.prtSaleSAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* ) : null} */}
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1116 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On ELR-Repair'}
                      value={this.state.elrRepairAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On ELR-Competitive'}
                      value={this.state.elrCompetitiveAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On ELR-Maintenance'}
                      value={this.state.elrMaintenanceAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* {this.state.elrShopAddOn != '' ? ( */}
                    <SummaryTitle
                      title={'Add On ELR-Shop Supplies'}
                      value={this.state.elrShopAddOn}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* ) : null} */}
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 25 && this.state.chartId == 1117 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Add On Markup-Repair'}
                      value={this.state.MarkupR}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Markup-Competitive'}
                      value={this.state.MarkupC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Add On Markup-Maintenance'}
                      value={this.state.MarkupM}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* {this.state.MarkupS != 0 ? ( */}
                    <SummaryTitle
                      title={'Add On Markup-Shop Supplies'}
                      value={this.state.MarkupS}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    {/* ) : null} */}
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 31 &&
                this.state.category == ' Labor Discount' ? (
                  <SummaryTitle
                    title={'Total Labor Discount'}
                    value={this.state.lbrDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 31 &&
                this.state.category == ' Parts Discount' ? (
                  <SummaryTitle
                    title={'Total Parts Discount'}
                    value={this.state.prtsDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 30 &&
                (this.state.category == 0 || this.state.category == 1) &&
                (this.state.chartId == 1165 || this.state.chartId == 1237) ? (
                  <SummaryTitle
                    title={'Discounted RO Count'}
                    value={this.state.discountedLaborRoCount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 &&
                (this.state.category == 0 || this.state.category == 1) &&
                (this.state.chartId == 1164 || this.state.chartId == 1236) ? (
                  <SummaryTitle
                    title={'Overall RO Count'}
                    value={this.state.overallROCount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 30 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Total Labor Discount'}
                    value={this.state.totallbrDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 40 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Total Labor Discount'}
                    value={this.state.totallbrDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Total Parts Discount'}
                    value={this.state.totalprtsDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 40 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Total Parts Discount'}
                    value={this.state.totalprtsDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 40 ? (
                  <SummaryTitle
                    title={'Total Discount'}
                    value={this.state.overAllDiscountP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 40 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'Total Percentage Labor Discount'}
                    value={this.state.totalDiscountP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 40 && this.state.category == 1 ? (
                  <SummaryTitle
                    title={'Total Percentage Parts Discount'}
                    value={this.state.percPartsDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.category == 2 ? (
                  <SummaryTitle
                    title={'RO - Discounts'}
                    value={this.state.roDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.category == 3 ? (
                  <SummaryTitle
                    title={'Line - Discounts'}
                    value={this.state.lineDiscount}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {/* {this.state.drillDown == 30 && this.state.category == 16 ? (
                  <SummaryTitle
                    title={'Discounted Jobs Count'}
                    value={this.state.discountJobcount}
                  />
                ) : null} */}
                {/* {this.state.drillDown == 30 && this.state.category == 16 ? ( */}
                {this.state.drillDown == 33 ? (
                  <SummaryTitle
                    title={'Discounted Jobs/RO'}
                    value={this.state.discountJob}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.chartId == 1123 ? (
                  <SummaryTitle
                    title={'Discounted RO Count'}
                    value={this.state.roArrayCountDis}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.chartId == 1123 ? (
                  <SummaryTitle
                    title={'Overall RO Count'}
                    value={this.state.overallRoCountDis}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.chartId == 1123 ? (
                  <SummaryTitle
                    title={'Discounted RO %'}
                    value={this.state.discountROPerc}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.chartId == 1124 ? (
                  <SummaryTitle
                    title={'Discounted RO Count'}
                    value={this.state.discountRO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.chartId == 1124 ? (
                  <SummaryTitle
                    title={'Overall RO Count'}
                    value={this.state.overallRO}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 && this.state.chartId == 1124 ? (
                  <SummaryTitle
                    title={'Discounted RO %'}
                    value={this.state.discountROPercSa}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 32 && this.state.chartId == 1126 ? (
                  <SummaryTitle
                    title={'Discounted Job Count'}
                    value={this.state.discountJob}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 32 && this.state.chartId == 1126 ? (
                  <SummaryTitle
                    title={'Overall Job Count'}
                    value={this.state.overallJob}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 32 && this.state.chartId == 1126 ? (
                  <SummaryTitle
                    title={'Discounted Job Count %'}
                    value={this.state.discountJobCountPerc}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 32 && this.state.chartId == 1125 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Discounted Job Count % -Repair'}
                      value={this.state.jobCountRep}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Discounted Job Count % -Maintenance'}
                      value={this.state.jobCountMain}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Discounted Job Count % -Competitive'}
                      value={this.state.jobCountComp}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 36 && this.state.category == 0 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.overallLbrSaleDiscount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Discounted Labor Sale'}
                      value={this.state.totallbrDiscount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Discounted Labor Sale %'}
                      value={this.state.totalLbrSaleP}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 36 && this.state.category == 1 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.overallPrtsSaleDiscount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Discounted Parts Sale'}
                      value={this.state.totalprtsDiscount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Discounted Parts Sale %'}
                      value={this.state.totalPrtSaleP}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {(this.state.drillDown == 1 &&
                  this.state.category == 7 &&
                  (this.state.chartId == 1150 ||
                    this.state.chartId == 1156 ||
                    this.state.chartId == 1331)) ||
                (this.state.drillDown == 12 && this.state.category == 7) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 7 &&
                  (this.state.chartId == 1201 ||
                    this.state.chartId == 1215 ||
                    this.state.chartId == 1222)) ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.roMaintenance}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 && this.state.category == 7 ? (
                  <SummaryTitle
                    title={'Labor Gross Profit-Maintenance Plan'}
                    value={this.state.laborGPMP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 || this.state.drillDown == 3) &&
                  this.state.category == 7 &&
                  this.state.chartId != 1331) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 7 &&
                  this.state.chartId == 1194 &&
                  this.state.chartId != 1331 &&
                  this.state.chartId != 1163) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.saleMaintenance1}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Cost'}
                      value={this.state.costMaintenance}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sold Hours'}
                      value={this.state.shMaintenance1}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 7 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.saleMaintenance1}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sold Hours'}
                      value={this.state.shMaintenance1}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 7 &&
                this.state.chartId == 1215 ? (
                  <SummaryTitle
                    title={'Total Labor Sale'}
                    value={this.state.saleMaintenance1}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 7 &&
                this.state.chartId == 1201 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Maintenance Plan'}
                    value={this.state.shMaintenance1}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {(this.state.drillDown == 1 &&
                  this.state.category == 19 &&
                  (this.state.chartId == 1151 ||
                    this.state.chartId == 1157 ||
                    this.state.chartId == 1332)) ||
                (this.state.drillDown == 12 && this.state.category == 19) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 19 &&
                  (this.state.chartId == 1202 ||
                    this.state.chartId == 1216 ||
                    this.state.chartId == 1223)) ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.roExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 3 && this.state.category == 19 ? (
                  <SummaryTitle
                    title={'Labor Gross Profit-Extended Service Contract'}
                    value={this.state.laborGPE}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 || this.state.drillDown == 3) &&
                  this.state.category == 19 &&
                  this.state.chartId != 1332) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 19 &&
                  this.state.chartId == 1195) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.saleExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Cost'}
                      value={this.state.costExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sold Hours'}
                      value={this.state.shExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 19 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.saleExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sold Hours'}
                      value={this.state.shExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 19 &&
                this.state.chartId == 1216 ? (
                  <SummaryTitle
                    title={'Total Labor Sale'}
                    value={this.state.saleExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 19 &&
                this.state.chartId == 1202 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Extended Service Contract'}
                    value={this.state.shExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 10 ||
                  this.state.drillDown == 11 ||
                  this.state.drillDown == 12) &&
                  this.state.category == 7) ||
                (this.state.drillDown == 48 &&
                  this.state.category == 7 &&
                  this.state.chartId == 1229) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.PsMaintenanceP}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.PcMaintenanceP}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 7 &&
                this.state.chartId == 1222 ? (
                  <SummaryTitle
                    title={'Total Parts Sale'}
                    value={this.state.PsMaintenanceP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 10 ||
                  this.state.drillDown == 11 ||
                  this.state.drillDown == 12) &&
                  this.state.category == 19) ||
                (this.state.drillDown == 48 &&
                  this.state.category == 19 &&
                  this.state.chartId == 1230) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.PsExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.PcExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 19 &&
                this.state.chartId == 1223 ? (
                  <SummaryTitle
                    title={'Parts Sale - Extended Service'}
                    value={this.state.PsExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 11 && this.state.category == 7 ? (
                  <SummaryTitle
                    title={'Parts Gross Profit - Maintenance Plan'}
                    value={this.state.prtGpMP}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 11 && this.state.category == 19 ? (
                  <SummaryTitle
                    title={'Parts Gross Profit - Extended Service Contract'}
                    value={this.state.prtGpE}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 &&
                (this.state.category == 0 || this.state.category == 1) &&
                (this.state.chartId == 1164 || this.state.chartId == 1236) ? (
                  <SummaryTitle
                    title={'$Discounted per Total CP Ros'}
                    value={this.state.discountedTotalCPRos}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 30 &&
                (this.state.category == 0 || this.state.category == 1) &&
                (this.state.chartId == 1165 || this.state.chartId == 1237) ? (
                  <SummaryTitle
                    title={'$Discounted per Total Discounted CP Ros'}
                    value={this.state.discountedTotalDiscountedCPRos}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 19 && this.state.category == 20 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Factory Service Contract'}
                    value={this.state.sHByCategoryF}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 || this.state.drillDown == 12) &&
                  this.state.category == 20 &&
                  (this.state.chartId == 1167 ||
                    this.state.chartId == 1173 ||
                    this.state.chartId == 1170 ||
                    this.state.chartId == 1333)) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 20 &&
                  (this.state.chartId == 1203 ||
                    this.state.chartId == 1217 ||
                    this.state.chartId == 1224)) ? (
                  <SummaryTitle
                    title={'RO Count'}
                    value={this.state.roFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 && this.state.chartId == 1325 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.roFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours - Factory Service Contract'}
                      value={this.state.shFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO - Factory Service Contract'}
                      value={this.state.LHROFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {this.state.drillDown == 3 && this.state.category == 20 ? (
                  <SummaryTitle
                    title={'Labor Gross Profit- Factory Service Contract'}
                    value={this.state.laborGPF}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {((this.state.drillDown == 1 || this.state.drillDown == 3) &&
                  this.state.category == 20 &&
                  this.state.chartId != 1068 &&
                  this.state.chartId != 1028 &&
                  this.state.chartId != 1170 &&
                  this.state.chartId != 1333) ||
                (this.state.drillDown == 39 &&
                  this.state.category == 20 &&
                  this.state.chartId == 1196) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.saleFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Cost'}
                      value={this.state.costFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sold Hours'}
                      value={this.state.shFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 20 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Labor Sale'}
                      value={this.state.saleFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Labor Sold Hours'}
                      value={this.state.shFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 20 &&
                this.state.chartId == 1217 ? (
                  <SummaryTitle
                    title={'Total Labor Sale'}
                    value={this.state.saleFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 20 &&
                this.state.chartId == 1203 ? (
                  <SummaryTitle
                    title={'Total Labor Sold Hours - Factory Service Contract'}
                    value={this.state.shFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {((this.state.drillDown == 10 ||
                  this.state.drillDown == 11 ||
                  this.state.drillDown == 12) &&
                  this.state.category == 20 &&
                  this.state.chartId != 1170) ||
                (this.state.drillDown == 48 &&
                  this.state.category == 20 &&
                  this.state.chartId == 1231) ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Parts Sale'}
                      value={this.state.PsFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Parts Cost'}
                      value={this.state.PcFactory}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 20 &&
                this.state.chartId == 1224 ? (
                  <SummaryTitle
                    title={'Parts Sale - Factory Service Contract'}
                    value={this.state.PsFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 11 && this.state.category == 20 ? (
                  <SummaryTitle
                    title={'Parts Gross Profit - Factory Service Contract'}
                    value={this.state.prtGpF}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 37 && this.state.chartId != 1175 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Total Sold Hours'}
                      value={this.state.soldHoursTech}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Available Hours'}
                      value={this.state.availableHours}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Average Hours Sold Per Technician'}
                      value={this.state.avgHrsTech}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.chartId == 1175 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Distinct Tech Count'}
                      value={this.state.techCount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total Sale'}
                      value={this.state.totalSaleTech}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Average Sales Per Technician'}
                      value={this.state.avgSalesPerTech}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 5 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale - Combined'}
                      value={this.state.lbrSaleC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - Combined'}
                      value={this.state.LsCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'ELR - Combined'}
                      value={this.state.ElrCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1211 ? (
                  <SummaryTitle
                    title={'Total Labor Sale'}
                    value={this.state.lbrSaleC}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 0 ? (
                  <SummaryTitle
                    title={'ELR - Customer Pay'}
                    value={this.state.ElrCustomer}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 3 ? (
                  <SummaryTitle
                    title={'ELR - Warranty'}
                    value={this.state.ElrWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 4 ? (
                  <SummaryTitle
                    title={'ELR - Internal'}
                    value={this.state.ElrInternal}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 7 ? (
                  <SummaryTitle
                    title={'ELR - Maintenance Plan'}
                    value={this.state.ElrMaintenance}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 19 ? (
                  <SummaryTitle
                    title={'ELR - Extended Service Contract'}
                    value={this.state.ElrExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 38 && this.state.category == 20 ? (
                  <SummaryTitle
                    title={'ELR - Factory Service Contract'}
                    value={this.state.ElrFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1190 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Labor Sale - Combined'}
                      value={this.state.lbrSaleC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Cost - Combined'}
                      value={this.state.lbrCostC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Labor Sold Hours - Combined'}
                      value={this.state.LsCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1197 ? (
                  <SummaryTitle
                    title={'Labor Sold Hours - Combined'}
                    value={this.state.LsCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1197 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO - Combined'}
                    value={this.state.LHROCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 0 && this.state.chartId == 1319 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.distinctROC}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours - Combined'}
                      value={this.state.LsCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO - Combined'}
                      value={this.state.LHROCombined}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 0 && this.state.chartId == 1320 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.distinctRO}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours - Customer Pay'}
                      value={this.state.laborHoursSum}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO - Customer Pay'}
                      value={this.state.LHROCustomer}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 0 &&
                this.state.chartId == 1198 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO - Customer Pay'}
                    value={this.state.LHROCustomer}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 3 &&
                this.state.chartId == 1199 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO - Warranty'}
                    value={this.state.LHROWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 39 &&
                this.state.category == 4 &&
                this.state.chartId == 1200 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO - Internal'}
                    value={this.state.LHROInteranl}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 39 &&
                this.state.category == 7 &&
                this.state.chartId == 1201 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO - Maintenance Plan'}
                    value={this.state.LHROMaintain}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 0 && this.state.chartId == 1323 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.roMaintenance}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours - Maintenance Plan'}
                      value={this.state.shMaintenance1}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO - Maintenance Plan'}
                      value={this.state.LHROMaintain}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 19 &&
                this.state.chartId == 1202 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO - Extended Service Contract'}
                    value={this.state.LHROExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 20 &&
                this.state.chartId == 1203 ? (
                  <SummaryTitle
                    title={'Labor Hours/RO - Factory Service Contract'}
                    value={this.state.LHROFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}

                {this.state.drillDown == 0 && this.state.chartId == 1324 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'RO Count - Parts Only'}
                      value={this.state.roExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours - Extended Service Contract'}
                      value={this.state.shExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Parts Hours/RO - Extended Service Contract'}
                      value={this.state.LHROExtended}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1211 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO - Combined'}
                    value={this.state.LSaleROCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 0 &&
                this.state.chartId == 1212 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO - Customer Pay'}
                    value={this.state.LSaleROCustomer}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 3 &&
                this.state.chartId == 1213 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO - Warranty'}
                    value={this.state.LSaleROWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 4 &&
                this.state.chartId == 1214 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO - Internal'}
                    value={this.state.LSaleROInteranl}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 7 &&
                this.state.chartId == 1215 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO - Maintenance Plan'}
                    value={this.state.LSaleROMaintain}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 19 &&
                this.state.chartId == 1216 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO - Extended Service Contract'}
                    value={this.state.LSaleROExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 20 &&
                this.state.chartId == 1217 ? (
                  <SummaryTitle
                    title={'Labor Sale/RO - Factory Service Contract'}
                    value={this.state.LSaleROFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1204 ? (
                  <SummaryTitle
                    title={'Job Count - Combined'}
                    value={this.state.JobCntCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 0 &&
                this.state.chartId == 1205 ? (
                  <SummaryTitle
                    title={'Job Count - Customer Pay'}
                    value={this.state.JobCntCustomer}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 3 &&
                this.state.chartId == 1206 ? (
                  <SummaryTitle
                    title={'Job Count - Warranty'}
                    value={this.state.JobCntWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 4 &&
                this.state.chartId == 1207 ? (
                  <SummaryTitle
                    title={'Job Count - Internal'}
                    value={this.state.JobCntInteranl}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 7 &&
                this.state.chartId == 1208 ? (
                  <SummaryTitle
                    title={'Job Count - Maintenance Plan'}
                    value={this.state.JobCntMaintain}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 19 &&
                this.state.chartId == 1209 ? (
                  <SummaryTitle
                    title={'Job Count - Extended Service Contract'}
                    value={this.state.JobCntExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 20 &&
                this.state.chartId == 1210 ? (
                  <SummaryTitle
                    title={'Job Count - Factory Service Contract'}
                    value={this.state.JobCntFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 5 &&
                this.state.chartId == 1218 ? (
                  <SummaryTitle
                    title={'Parts Revenue/RO - Combined'}
                    value={this.state.prtsRevenueRoCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 0 &&
                this.state.chartId == 1219 ? (
                  <SummaryTitle
                    title={'Parts Revenue/RO - Customer Pay'}
                    value={this.state.prtsRevenueRoCustomer}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 3 &&
                this.state.chartId == 1220 ? (
                  <SummaryTitle
                    title={'Parts Revenue/RO - Warranty'}
                    value={this.state.prtsRevenueRoWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 4 &&
                this.state.chartId == 1221 ? (
                  <SummaryTitle
                    title={'Parts Revenue/RO - Internal'}
                    value={this.state.prtsRevenueRoInteranl}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 7 &&
                this.state.chartId == 1222 ? (
                  <SummaryTitle
                    title={'Parts Revenue/RO - Maintenance Plan'}
                    value={this.state.prtsRevenueRoMaintain}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 19 &&
                this.state.chartId == 1223 ? (
                  <SummaryTitle
                    title={'Parts Revenue/RO - Extended Service Contract'}
                    value={this.state.prtsRevenueRoExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 39 &&
                this.state.category == 20 &&
                this.state.chartId == 1224 ? (
                  <SummaryTitle
                    title={'Parts Revenue/RO - Factory Service Contract'}
                    value={this.state.prtsRevenueRoFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 &&
                this.state.category == 5 &&
                this.state.chartId == 1225 ? (
                  <SummaryTitle
                    title={'Parts Markup - Combined'}
                    value={this.state.prtsMarkupCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 &&
                this.state.category == 0 &&
                this.state.chartId == 1226 ? (
                  <SummaryTitle
                    title={'Parts Markup - Customer Pay'}
                    value={this.state.prtsMarkupCustomer}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 &&
                this.state.category == 3 &&
                this.state.chartId == 1227 ? (
                  <SummaryTitle
                    title={'Parts Markup - Warranty'}
                    value={this.state.prtsMarkupWarranty}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 &&
                this.state.category == 4 &&
                this.state.chartId == 1228 ? (
                  <SummaryTitle
                    title={'Parts Markup - Internal'}
                    value={this.state.prtsMarkupInteranl}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 &&
                this.state.category == 7 &&
                this.state.chartId == 1229 ? (
                  <SummaryTitle
                    title={'Parts Markup - Maintenance Plan'}
                    value={this.state.prtsMarkupMaintain}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 &&
                this.state.category == 19 &&
                this.state.chartId == 1230 ? (
                  <SummaryTitle
                    title={'Parts Markup - Extended Service Contract'}
                    value={this.state.prtsMarkupExtended}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 48 &&
                this.state.category == 20 &&
                this.state.chartId == 1231 ? (
                  <SummaryTitle
                    title={'Parts Markup - Factory Service Contract'}
                    value={this.state.prtsMarkupFactory}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.chartId == 1239 || this.state.chartId == 1241 ? (
                  <SummaryTitle
                    title={'Shop Supplies - Customer Pay'}
                    value={this.state.shopSuppliesC}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.chartId == 1240 ? (
                  <SummaryTitle
                    title={'Shop Supplies - Combined'}
                    value={this.state.shopSuppliesCombined}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.chartId == 1242 ? (
                  <SummaryTitle
                    title={'Shop Supplies - Internal'}
                    value={this.state.shopSuppliesI}
                    drillDown={this.state.drillDown}
                    category={this.state.category}
                  />
                ) : null}
                {this.state.drillDown == 47 && this.state.chartId == 1316 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'MPI Count'}
                      value={this.state.mpiCount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total RO Count'}
                      value={this.state.totalROCount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'MPI Penetration %'}
                      value={this.state.mpiPercentage}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}

                {this.state.drillDown == 47 && this.state.chartId == 1317 ? (
                  <React.Fragment>
                    <SummaryTitle
                      title={'Menu Count'}
                      value={this.state.menuCount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Total RO Count'}
                      value={this.state.totalROCount}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                    <SummaryTitle
                      title={'Menu Penetration %'}
                      value={this.state.menuPercentage}
                      drillDown={this.state.drillDown}
                      category={this.state.category}
                    />
                  </React.Fragment>
                ) : null}
              </Grid>
            </div>
          </Paper>
        )}
      </div>
    );
  }
}
const SummaryTitleTotals = ({ title, value, drillDown, category }) => {
  return (
    <>
      <TableCell
        component="th"
        scope="row"
        style={{
          padding: 10,
          fontSize: '12px',
          borderBottom: 'none',
          lineHeight: 0
        }}
      >
        {title} :
      </TableCell>
      <TableCell
        align="right"
        style={{
          padding: 10,
          fontSize: '12px',
          borderBottom: 'none',
          lineHeight: 0
        }}
      >
        {value}
      </TableCell>
    </>
  );
};

const SummaryTitle = ({ title, value, drillDown, category }) => {
  return (drillDown == 3 || drillDown == 4 || drillDown == 1) &&
    category == 2 ? (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '40px' }}
        color="primary"
      >
        {title}
      </Typography>
      <Typography variant="subtitle1" style={{ fontSize: '14px' }}>
        {' '}
        {value}
      </Typography>
    </Grid>
  ) : (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '30px' }}
        color="primary"
      >
        {title}
      </Typography>
      <Typography variant="subtitle1" style={{ fontSize: '14px' }}>
        {' '}
        {value}
      </Typography>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  select: {
    color: theme.palette.primary.main
  },
  root: {
    flexGrow: 1
  },
  // paper: {
  //   padding: theme.spacing(2),
  //   textAlign: 'center',
  //   color: theme.palette.text.secondary
  // },
  root: {
    flexGrow: 1,
    width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    //textAlign: 'center',
    color: theme.palette.text.secondary
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    //justifyContent: 'space-between',
    width: '100%'
  }
});

// export default withKeycloak(withStyles(styles)(Drilldown));

export default connect(mapStateToProps, mapDispatchToProps)(
  withKeycloak(withStyles(styles)(Drilldown)),
  Drilldown
);

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setDrillDownCount: data =>
      dispatch({ type: SET_DRILL_DOWN_COUNT, payload: data })
  };
}
