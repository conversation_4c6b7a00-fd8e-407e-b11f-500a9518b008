import PropTypes from 'prop-types';
import React, { useEffect } from 'react';
import ReactApexChart from 'react-apexcharts';
import { Paper } from '@material-ui/core';

const GraphGenerator = ({ ...rest }) => {
  const customData =
    rest.chartData.chartType == 'pie'
      ? rest.chartData.seriesData[0].dataSeries
      : rest.chartData.seriesData.map((s, index) => ({
          name: s.name,
          data: s.dataSeries.map(value => value)
        }));

  const options = {
    chart: {
      fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
      toolbar: {
        show: true,
        offsetX: 0,
        offsetY: 0,
        tools: {
          download: false,
          selection: true,
          zoomin: true,
          zoomout: true,
          pan: true
        }
        // autoSelected: 'pan'
      }
    },
    labels: rest.chartData.categories,
    dataLabels: {
      enabled: false
    },
    stroke: {
      curve: 'straight',
      width: 3
    },
    markers: {
      size: 4,
      strokeWidth: 1,
      strokeOpacity: 0.7,
      showNullDataPoints: false
    },
    title: {
      text: rest.chartData.chartName,
      align: 'left'
    },
    grid: {
      show: true,
      row: {
        colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
        opacity: 0.5
      }
    },
    legend: {
      show: true,
      showForSingleSeries: false
    },
    theme: {
      palette: 'palette8' // upto palette10
    },
    yaxis: {
      labels: {
        formatter: function(value) {
          if (rest.yAxisSuffix) {
            if (rest.yAxisSuffix == '%') return value + ' %';
            if (rest.yAxisSuffix == 'D')
              return Math.round((value + Number.EPSILON) * 100) / 100;
            if (rest.yAxisSuffix == '$')
              return (
                ' $' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              );
          } else {
            return value;
          }
        }
      }
    },
    xaxis: {
      // tickPlacement: 'on',
      show: false,
      categories: rest.chartData.categories.map(c => c),
      labels: {
        show: true
        // rotate: 90,
      }
      // .map(c => moment(c.category).format('MMM'))
    }
  };

  useEffect(() => {}, []);

  return (
    <Paper square>
      <ReactApexChart
        options={options}
        series={customData}
        height={250}
        type={rest.chartData.chartType}
      />
    </Paper>
  );
};
GraphGenerator.propTypes = {
  className: PropTypes.string
};

export default GraphGenerator;
