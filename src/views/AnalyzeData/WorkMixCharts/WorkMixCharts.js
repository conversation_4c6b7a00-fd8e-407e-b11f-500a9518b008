import { Grid, CircularProgress, Card } from '@material-ui/core';
import React, { useEffect, useState } from 'react';

import { makeStyles } from '@material-ui/core/styles';
import { useSelector } from 'react-redux';
import Bar<PERSON><PERSON><PERSON>enderer from './BarChartRenderer';
import { getDateRange } from 'src/components/ViewGraphDetailsAction';
import { getStoreIdFilter } from 'src/components/ViewGraphDetailsAction';
import OverViewGraphGrid from '../OverviewGraphsGrid';
import clsx from 'clsx';
import 'src/styles.css';
import $ from 'jquery';
import { getChartsDataFromViews } from 'src/utils/hasuraServices';

var lodash = require('lodash');
const useStyles = makeStyles({
  formControl: {
    padding: 4,
    minWidth: 250
  },
  gridContainer: {
    padding: 4
  },
  container: {
    padding: '0px 5px'
  }
});
const WorkMixCharts = ({
  isPartsCharts,
  chartList,
  removeFav,
  partialMonth,
  parentCallback,
  hideGrids,
  realm,
  session,
  history
}) => {
  const [checked, setChecked] = useState(false);
  const [popupChartId, setPopupChartId] = useState('');
  const [workmixChartData, setWorkmixChartData] = useState(null);
  const [isLoading, setLoading] = useState(true);

  const classes = useStyles();
  const handleChartPopup = value => {
    setPopupChartId(value);
    hideGrids(true);
    setChecked(true);
    // window.scrollTo(0, 0);
  };
  const handleClosePopup = value => {
    console.log('state===handleClosePopup');
    setPopupChartId('');
    setChecked(false);
    hideGrids(false);
  };

  useEffect(() => {
    const chartId = orderedData[0]?.chartId; // or a specific one you want
    if (chartId) {
      setWorkmixChartData(null);
      getDataforWorkmixCharts(chartId);
    }
  }, [session.serviceAdvisor, session.storeSelected]);

  useEffect(() => {}, [isPartsCharts]);
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult;
  if (isPartsCharts) {
    filteredResult =
      chartList &&
      chartList.filter(
        item => item.dbdName == 'Parts Workmix' && item.parentId == null
      );
  } else {
    filteredResult =
      chartList &&
      chartList.filter(
        item => item.dbdName == 'Labor Workmix' && item.parentId == null
      );
  }

  let orderedData = lodash.orderBy(
    filteredResult && filteredResult.slice(0, 6),
    'sort',
    'asc'
  );

  const getDataforWorkmixCharts = chartId => {
    setLoading(true);
    getChartsDataFromViews(session.serviceAdvisor, chartId, callback => {
      if (callback) {
        setLoading(false);
        setWorkmixChartData(callback);
      }
    });
  };

  return (
    <>
      <OverViewGraphGrid
        isPartsCharts={isPartsCharts}
        parentCallback={parentCallback}
        realm={realm}
        partialMonth={partialMonth}
        session={session}
        history={history}
        // chartZoomed={checked}
      />
      <Grid container spacing={12} className={classes.container}>
        {orderedData.map((item, index) => (
          <Grid
            item
            xs={6}
            className={clsx(classes.gridContainer, 'diagram-section')}
          >
            {workmixChartData ? (
              <BarChartRenderer
                title={item.chartName}
                removeFav={removeFav}
                chartId={parseInt(item.chartId)}
                parentCallback={parentCallback}
                handleChartPopup={handleChartPopup}
                handleClosePopup={handleClosePopup}
                realm={realm}
                session={session}
                userhistory={history}
                chartData={workmixChartData}
              />
            ) : (
              <Card
                bordered={false}
                style={{
                  height: 280,
                  margin: 0,
                  borderRadius: 0,
                  border: '1px solid #003d6b',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                <CircularProgress size={60} />
              </Card>
            )}
          </Grid>
        ))}
      </Grid>

      {/* {popupChartId != '' && (
        <Collapse
          in={checked}
          style={{
        height: 620,
        maxWidth: '100%',
        margin: 20,
        padding: 10,
        display: checked ? 'block' : 'none'
          }}
        >
          <Paper elevation={4}>
        <BarChartRenderer
          title={
            typeof chartId != 'undefined' ? getChartName(popupChartId) : ''
          }
          removeFav={removeFav}
          handleChartPopup={handleChartPopup}
          chartId={parseInt(popupChartId)}
          parentCallback={parentCallback}
          type="popup"
          handleClosePopup={handleClosePopup}
          realm={realm}
        />
          </Paper>
        </Collapse>
      )} */}
    </>
  );
};

export default React.memo(WorkMixCharts);
