import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import { Button } from '@material-ui/core';
import {
  Typography,
  FormControl,
  InputLabel,
  LinearProgress,
  Box,
  Paper,
  MenuItem,
  Select,
  Tooltip,
  FormControlLabel,
  RadioGroup,
  Radio,
  Link
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import RestoreIcon from '@material-ui/icons/Restore';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getKpiToggleOptionsWithTimeZone,
  getDataForPartsMisses,
  getTargetGridRate,
  getGridorMatrixMissesDetails,
  getDataForAdvisorAndTechPartsMisses,
  getEmail,
  insertPartsMissesSavededReport
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import css from 'src/assets/scss/main.scss';
import { getLast13Months, getTimeZone } from 'src/utils/Utils';
import Page from 'src/components/Page';
import { ReactSession } from 'react-client-session';
import $ from 'jquery';
import { getLatestClosedDate } from 'src/utils/hasuraServices';
import { getNextMonth, getYearValue } from 'src/utils/Utils';
import ExportIcon from '@material-ui/icons/GetApp';
import PictureAsPdfIcon from '@material-ui/icons/PictureAsPdf';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import TooltipRenderer from './Component/TooltipRenderer';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import { Redirect } from 'react-router-dom';
import { withKeycloak } from '@react-keycloak/web';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import { isValidDate } from 'src/components/ViewGraphDetailsAction';
import MailIcon from '@material-ui/icons/Mail';
import EmailDialogKpi from 'src/components/EmailDialogKpi';
import SaveIcon from '@material-ui/icons/Save';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import AlertSnackbar from 'src/components/AlertSnackbar';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import SaveReportDialog from 'src/components/SaveReportDialog';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;
var defaultFilterParams = { readOnly: false };

class PartsMisses extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ Technicians: ['All'] });

    this.setState({ store: localStorage.getItem('selectedStoreId') });
    this.setState({ payTypeList: [] });
    this.getKpiToggleOptions();
  }
  componentDidUpdate() {
    const newAdvisors = ReactSession.get('serviceAdvisors');
    const newTech = ReactSession.get('technicians');
    if (
      ReactSession.get('serviceAdvisors') != undefined ||
      ReactSession.get('technicians') != undefined ||
      this.state.filterChanged
    ) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisors,
        ReactSession.get('serviceAdvisors')
      );
      var checkStatusTech = lodash.isEqual(
        this.state.Technicians,
        ReactSession.get('technicians')
      );
      if (
        (checkStatus == false && $('#advisorSelection').hasClass('selected')) ||
        (checkStatusTech == false &&
          $('#techSelection').hasClass('selected')) ||
        this.state.filterChanged == true
      ) {
        /* this.setState({ payType: this.state.payType });
        this.setState({ selectedFilter: 'duration' });
        this.setState({ selectedToggle: this.state.selectedToggle });
        this.setState({ selectedMonthYear: '' });
        this.setState({ filterChanged: false });
        //this.setState({ checked: false });
        this.setState({ advisors: ReactSession.get('serviceAdvisors') });
        this.setState({ tech: ReactSession.get('technicians') });
        this.getAgGridData(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          this.state.filterStart,
          this.state.filterEnd
        );
*/
        this.setState(
          {
            payType: this.state.payType,
            selectedFilter: 'duration',
            selectedToggle: this.state.selectedToggle,
            selectedMonthYear: '',
            filterChanged: false,
            advisors: newAdvisors,
            tech: newTech
          },
          () => {
            this.getAgGridData(
              this.state.selectedToggle,
              newAdvisors,
              newTech,
              this.state.filterStart,
              this.state.filterEnd
            );
          }
        );
      }
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        // if (this.state.store != undefined) {
        window.sortStatePrtMiss = {};
        window.filterStatePrtMiss = {};
        if (this.state.gridColumnApi) {
          const groupColumns = this.state.groupColumn;
          this.state.rawGridApi.setColumnDefs(groupColumns);
          setTimeout(() => {
            this.state.gridColumnApi.resetColumnState();
          }, 50);
        }
        // }
        if (this.state.gridColumnApi) {
          const groupColumns = this.state.groupColumn;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumns);
          setTimeout(() => {
            this.state.gridColumnApi.resetColumnState();
          }, 50);
        }
        this.setState({ storeSwitch: true, parent: '' });
        this.setState({ payType: this.state.payType });
        this.setState({ selectedFilter: 'duration' });
        this.setState({
          selectedToggle: this.state.selectedToggle
        });
        this.setState({ selectedMonthYear: '' });

        //  window.filterStateParts = {};
        getLatestClosedDate(result => {
          if (result) {
            var openDate = '';
            var Date1 = result[0].value;
            localStorage.setItem('closedDate', Date1);
            this.setState({ closedDate: Date1 });
          }
        });
        this.setState({
          checked: false,
          gridType: 'All'
        });

        this.getAgGridData(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          this.state.filterStart,
          this.state.filterEnd
        );
        const groupColumn = this.state.groupColumn;
        if (
          JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
          this.state.rawGridApi
        ) {
          groupColumn[2]['hide'] = false;
          groupColumn[2]['suppressToolPanel'] = false;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          if (window.sortStatePrtMiss && this.gridApi) {
            this.gridApi.setSortModel(window.sortStatePrtMiss);
          }
          if (window.colStatePrtMisses && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStatePrtMisses);
          }
        } else if (this.state.rawGridApi) {
          groupColumn[2]['hide'] = true;
          groupColumn[2]['suppressToolPanel'] = true;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          if (window.sortStatePrtMiss && this.gridApi) {
            this.gridApi.setSortModel(window.sortStatePrtMiss);
          }
          if (window.colStatePrtMisses && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStatePrtMisses);
          }
        }
        this.resetRawData();
        this.setState({
          store: localStorage.getItem('selectedStoreId')
        });
      }
    }
  }

  constructor(props) {
    super(props);
    let created_by =
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.created_by
        ? props.history.location.state.created_by
        : '';
    let isDisabled =
      created_by != localStorage.getItem('userID') &&
      props.keycloak.realmAccess.roles.includes('superadmin');
    let selFilter = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedFilter
        : this.props.selectedFilter
      : this.props.selectedFilter;
    let selToggle = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedToggle
        : this.props.selectedToggle
      : this.props.selectedToggle;
    let selMonthyr = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedMonthYear
        : this.props.selectedMonthYear
      : this.props.selectedMonthYear;
    let closedDate = localStorage.getItem('closedDate');
    let parent =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.parent
        ? this.props.history.location.state.parent
        : '';
    var timeZone = getTimeZone();
    let PayType =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.payType
        ? this.props.history.location.state.payType
        : 'C';
    let previousPayType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.previousPayType
        : 'C';
    let previousGridType =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousGridType
        ? this.props.history.location.state.previousGridType
        : '';
    let showAllJobs =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.jobType &&
          this.props.history.location.state.jobType == 'All'
        ? true
        : false;
    let kpiDataToggle =
      localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';

    let startDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterStart
        ? this.props.history.location.state.filterStart
        : ReactSession.get('kpiToggleStartDate') != ''
        ? ReactSession.get('kpiToggleStartDate')
        : '';

    let endDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterEnd
        ? this.props.history.location.state.filterEnd
        : ReactSession.get('kpiToggleEndDate') != ''
        ? ReactSession.get('kpiToggleEndDate')
        : '';

    let gridType =
      this.props.location &&
      this.props.location.state &&
      // this.props.location.state.parent &&
      // this.props.location.state.parent == 'Home' &&
      this.props.location.state.selectedGridType
        ? this.props.location.state.selectedGridType
        : this.props.location &&
          this.props.location.state &&
          this.props.location.state.parent &&
          this.props.location.state.parent == 'Home'
        ? 'All'
        : this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : 'All';
    // let filterText = ReactSession.get('kpiHomeToggle');
    let filterText =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterText
        ? this.props.history.location.state.filterText
        : ReactSession.get('kpiHomeToggle')
        ? ReactSession.get('kpiHomeToggle')
        : localStorage.getItem('kpiHomeToggle');

    let filterColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterColumns
        ? this.props.history.location.state.filterColumns
        : {};
    let sortColumn =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.sortColumns
        ? this.props.history.location.state.sortColumns
        : {};
    let advisor =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedAdvisor
        ? this.props.history.location.state.selectedAdvisor
        : ReactSession.get('serviceAdvisors')
        ? ReactSession.get('serviceAdvisors')
        : null;
    let technicians =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedTech
        ? this.props.history.location.state.selectedTech
        : ReactSession.get('technicians')
        ? ReactSession.get('technicians')
        : null;
    let checkedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.checkedColumns
        ? this.props.history.location.state.checkedColumns
        : 'All';
    let draggedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.draggedColumn
        ? this.props.history.location.state.draggedColumn
        : [];

    let visibility =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.visibility
        ? this.props.history.location.state.visibility
        : 'private';
    let reportName =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.report_name
        ? this.props.history.location.state.report_name
        : '';

    this.state = {
      selectedStoreIds: JSON.parse(localStorage.getItem('selectedStoreId')),
      headerData: [],
      rowDataPdf: [],
      iKpiReportType: 'Parts_Target_Misses',
      mailUsers: [],
      openDialogue: false,
      createdBy: created_by,
      storeSwitch: false,
      openStoreDlg: false,
      selectedFilter: selFilter ? selFilter : 'duration',
      selectedToggle: selToggle ? selToggle : kpiDataToggle,
      kpiDataToggle: kpiDataToggle,
      selectedMonthYear: selMonthyr ? selMonthyr : '',
      monthYear: {},
      toggleOptions: [],

      dayBeforeYesterday: '',
      closedDate: closedDate,
      parent: parent,
      timeZone: timeZone,
      partsCost: 0,
      targetPrice: 0,
      actualSale: 0,
      overSell: 0,
      underSell: 0,
      diffSale: 0,
      jobCount: 0,
      roCount: 0,
      payType: PayType,
      PrevPayType: previousPayType,
      lastThreeMonths: '',
      lastTwelveMonths: '',
      payTypeList: [],
      PrevGridType: previousGridType,
      checked: showAllJobs,
      gridType: gridType,
      isLoading: true,
      dates: [],
      lastWeek: '',
      thisWeek: '',
      lastTwoWeek: '',
      yesterDay: '',
      today: '',
      dayBfYest: '',
      mtd: '',
      setMtd: '',
      lastMonth: '',
      lastThreeMonths: '',
      lastQtr: '',
      ytd: '',
      lastTwelveMonths: '',
      lastYear: '',
      filterStart: startDate,
      filterEnd: endDate,
      filterChanged: false,

      filterText: filterText,
      openSaveDlg: false,
      reportName: reportName,
      errorReport: '',
      selectedType: visibility,
      openSusSnackbar: false,
      requiredText: false,
      displayCol: checkedColumns,
      draggedColumn: draggedColumns,
      advisors: advisor,
      tech: technicians,
      parent: parent,
      filterCol: filterColumns,
      sortCol: sortColumn,
      // filterTxt: filterTxt,
      copy: false,
      copyFile: false,
      reportNameCopy: '',
      openDilog: false,
      openAlert: false,
      openDilogAll: false,
      zeroFilter: false,
      columnDefs: [
        {
          headerName: 'RO Open Date',
          field: 'opendate',
          cellClass: 'dateFormatter',
          tooltipValueGetter: function(params) {
            return params.valueFormatted ? params.valueFormatted : params.value;
          },
          width: 92,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate,
            applyMiniFilterWhileTyping: true
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          headerClass: 'ag-header-cell-text'
        },
        {
          headerName: 'RO Cls. Date',
          field: 'closeddate',
          width: 92,
          cellClass: 'dateFormatter',
          tooltipValueGetter: function(params) {
            return params.valueFormatted ? params.valueFormatted : params.value;
          },
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate,
            applyMiniFilterWhileTyping: true
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          headerClass: 'ag-header-cell-text'
        },
        {
          headerName: 'Store',
          field: 'storeName',
          width: 140,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'storeName',
          hide: true,
          suppressToolPanel: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        // {
        //   headerName: 'Matrix For',
        //   field: 'partsFor',
        //   width: 100,
        //   dataType: 'string',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'left', border: ' 0px white' };
        //   }
        // },
        // {
        //   headerName: 'Type',
        //   field: 'fleetOrPaytypeOrOpcodeFixedRate',
        //   width: 100,
        //   dataType: 'string',
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        {
          headerName: 'RO#',
          field: 'ronumber',
          width: 85,
          suppressMenu: true,
          unSortIcon: true,
          onCellClicked: this.handleSearchByRo,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellClass: 'textAlign',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              //left: '-35px',
              cursor: 'pointer',
              textAlign: 'center',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 140,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'advisorName',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
            let nameA = (valueA && valueA.split('[')[0].trim()) || ''; // Handle null or undefined valueA
            let nameB = (valueB && valueB.split('[')[0].trim()) || ''; // Handle null or undefined valueB
            return nameA.localeCompare(nameB); // Alphabetic sort based on the name only
          }
        },
        {
          headerName: 'Technician',
          field: 'techName',
          width: 120,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'techName',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
            let nameA = (valueA && valueA.split('[')[0].trim()) || ''; // Handle null or undefined valueA
            let nameB = (valueB && valueB.split('[')[0].trim()) || ''; // Handle null or undefined valueB
            return nameA.localeCompare(nameB); // Alphabetic sort based on the name only
          }
        },
        {
          headerName: 'Customer',
          field: 'customerName',
          dataType: 'string',
          tooltipField: 'customerName',
          width: 140,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Desc',
          field: 'prtdesc',
          width: 115,
          tooltipField: 'prtdesc',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Part #',
          field: 'prtpartno',
          width: 115,
          suppressMenu: true,
          cellClass: 'textAlignCentercenter',
          unSortIcon: true,
          tooltipField: 'prtpartno'
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // }
        },
        {
          headerName: 'Parts Source',
          field: 'prtsource',
          width: 75,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          width: 90,
          chartDataType: 'category',
          cellClass: 'textAlignUiLeft',
          tooltipField: 'opcodedescription',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: (valueA, valueB) => {
            // Helper function to extract numeric part from string
            const extractNumericPart = str => {
              const matches = str.match(/(\d+)/);
              return matches ? parseInt(matches[0], 10) : NaN;
            };

            // Helper function to extract non-numeric part from string
            const extractNonNumericPart = str => {
              return str.replace(/(\d+)/, '').toLowerCase();
            };

            // Extract numeric and non-numeric parts from values
            const numericPartA = extractNumericPart(valueA);
            const numericPartB = extractNumericPart(valueB);
            const nonNumericPartA = extractNonNumericPart(valueA);
            const nonNumericPartB = extractNonNumericPart(valueB);

            // Compare non-numeric parts
            if (nonNumericPartA !== nonNumericPartB) {
              return nonNumericPartA.localeCompare(nonNumericPartB);
            }

            // Compare numeric parts if both are numeric
            if (!isNaN(numericPartA) && !isNaN(numericPartB)) {
              return numericPartA - numericPartB;
            }

            // If one is numeric and the other is not, numeric comes first
            if (!isNaN(numericPartA)) {
              return -1;
            } else if (!isNaN(numericPartB)) {
              return 1;
            }

            // Fallback to comparing original values if both parts are non-numeric
            return valueA.localeCompare(valueB);
          }
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: 60,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        // {
        //   headerName: 'Pay Type Group',
        //   field: 'paytypegroup',
        //   width: 68,
        //   dataType: 'string',
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        {
          headerName: 'Qty',
          field: 'prtqtysold',
          width: 50,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Part List Price',
          field: 'prtlist',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Part Unit Cost',
          field: 'prtcost',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          filterParams: {
            decimalPlaces: 2,
            valueFormatter: function(params) {
              const roundedValue =
                Math.round(parseFloat(params.value) * 100) / 100;
              return '$' + roundedValue.toFixed(2);
            },
            applyMiniFilterWhileTyping: true
          }
        },
        {
          headerName: 'Part Unit Sale',
          field: 'lbrsale',
          width: 85,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Extended Cost',
          field: 'prtextendedcost',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Extended Sale',
          field: 'prtextendedsale',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Parts GP%',
          field: 'grossprofitpercentage',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Target Unit Price',
          field: 'targetPrice',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueTargetPrice,
          filterParams: {
            valueGetter: this.formatCellValueTargetPriceFilter,
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,

          comparator: function(valueA, valueB) {
            // Handle "N/A" values
            if (valueA === 'N/A' && valueB === 'N/A') {
              return 0;
            } else if (valueA === 'N/A') {
              return -1; // "N/A" comes first
            } else if (valueB === 'N/A') {
              return 1; // "N/A" comes first
            }

            // Handle numeric values by parsing strings to numbers
            const numA = parseFloat(valueA.replace(/[^0-9.-]+/g, ''));
            const numB = parseFloat(valueB.replace(/[^0-9.-]+/g, ''));

            return numA - numB;
          }
        },
        {
          headerName: 'Target Extended Price',
          field: 'targetExtendedPrice',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueTargetPrice,
          filterParams: {
            valueGetter: this.formatCellValueTargetPriceFilter,
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            // Handle "N/A" values
            if (valueA === 'N/A' && valueB === 'N/A') {
              return 0;
            } else if (valueA === 'N/A') {
              return -1; // "N/A" comes first
            } else if (valueB === 'N/A') {
              return 1; // "N/A" comes first
            }

            // Handle numeric values by parsing strings to numbers
            const numA = parseFloat(valueA.replace(/[^0-9.-]+/g, ''));
            const numB = parseFloat(valueB.replace(/[^0-9.-]+/g, ''));

            return numA - numB;
          }
        },
        {
          headerName: '$ Variance',
          field: 'variance',
          width: 95,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueVariance,
          filterParams: {
            valueGetter: this.formatCellValueVarianceFilter,
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWith$',
          cellClassRules: {
            greenBackground: function(params) {
              return params.rowIndex % 2 == 0;
            },
            greenFont: function(params) {
              return (
                params.data.compliance == 'TRUE' &&
                params.value != 0 &&
                params.value != null
              );
            },
            blueFont: function(params) {
              return params.value >= 0 && params.data.compliance == 'FALSE';
            },
            redFont: function(params) {
              return params.value < 0 && params.data.compliance == 'FALSE';
            }
          },
          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              fontWeight: 'bold',
              color:
                params.value >= 0 && params.data.compliance == 'FALSE'
                  ? '#0000FF'
                  : params.value < 0 && params.data.compliance == 'FALSE'
                  ? 'red'
                  : params.data.compliance == 'TRUE' &&
                    params.value != 0 &&
                    params.value != null
                  ? 'green'
                  : ''
            };
          },
          comparator: function(valueA, valueB) {
            // Handle "N/A" values
            if (valueA === 'N/A' && valueB === 'N/A') {
              return 0;
            } else if (valueA === 'N/A') {
              return -1; // "N/A" comes first
            } else if (valueB === 'N/A') {
              return 1; // "N/A" comes first
            }

            // Handle numeric values
            if (valueA != 'N/A' && valueB != 'N/A') {
              return valueA - valueB;
            }
            return valueA.localeCompare(valueB);
          }
        },
        {
          headerName: '% Variance',
          field: 'variancePerc',
          width: 99,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValuePercent,
          filterParams: {
            valueGetter: this.formatCellValuePercentFilter,
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWithOut$',
          cellClassRules: {
            greenBackground: function(params) {
              return params.rowIndex % 2 == 0;
            },
            greenFont: function(params) {
              return (
                params.data.compliance == 'TRUE' &&
                params.data.variancePerc != 0 &&
                params.data.variancePerc != null
              );
            },
            blueFont: function(params) {
              return (
                params.data.variancePerc >= 0 &&
                params.data.compliance == 'FALSE'
              );
            },
            redFont: function(params) {
              return (
                params.data.variancePerc < 0 &&
                params.data.compliance == 'FALSE'
              );
            }
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            const rawValue = params.data.variancePerc;
            return {
              textAlign: 'right',
              border: '0px white',
              fontWeight: 'bold',
              color:
                rawValue >= 0 && params.data.compliance == 'FALSE'
                  ? '#0000FF'
                  : rawValue < 0 && params.data.compliance == 'FALSE'
                  ? 'red'
                  : params.data.compliance == 'TRUE' &&
                    rawValue != 0 &&
                    rawValue != null
                  ? 'green'
                  : ''
            };
          },
          comparator: function(valueA, valueB) {
            // Handle "N/A" values
            if (valueA === 'N/A' && valueB === 'N/A') {
              return 0;
            } else if (valueA === 'N/A') {
              return -1; // "N/A" comes first
            } else if (valueB === 'N/A') {
              return 1; // "N/A" comes first
            }

            // Handle numeric values
            if (valueA != 'N/A' && valueB != 'N/A') {
              return valueA - valueB;
            }
            return valueA.localeCompare(valueB);
          }
        },
        {
          headerName: 'Matrix Install Date',
          field: 'gridDate',
          width: 90,
          cellClass: 'dateFormatter',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate,
            applyMiniFilterWhileTyping: true
          },
          onCellClicked: this.handleDoorRate,
          suppressMenu: true,
          unSortIcon: true,
          // tooltip: params => 'View Matrix',
          tooltip: params => {
            const matrixType = params.data.matrixType;

            if (matrixType.includes('Fixed Rate')) {
              return 'View Fixed Rate';
            } else {
              return 'View Matrix';
            }
          },
          cellStyle: function(params) {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'left',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Matrix Type',
          field: 'matrixType',
          width: 100,
          dataType: 'string',
          tooltipField: 'matrixType',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Compliance',
          field: 'compliance',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'textAlignCenter',
          cellStyle: function(params) {
            return {
              textAlign: 'center'
            };
          }
          //cellClass: this.getCellClass
        },
        {
          headerName: 'Vin',
          field: 'vin',
          hide: true,
          width: 110,
          tooltipField: 'vin',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcategory',
          field: 'opcategory',
          hide: true,
          width: 95,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'center'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Hours Sold',
          field: 'lbrsoldhours',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP',
          field: 'lbrgrossprofit',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP%',
          field: 'lbrGrossprofitpercentage',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR',
          field: 'elr',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup',
          field: 'markup',
          hide: true,
          width: 80,
          cellClass: 'fourDecimalPlaces',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Make',
          field: 'make',
          hide: true,
          width: 80,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'textAlignUiRight'
        },
        {
          headerName: 'Model',
          field: 'model',
          width: 100,
          hide: true,
          dataType: 'string',
          tooltipField: 'model',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'textAlignUiRight'
        },
        {
          headerName: 'Year',
          field: 'year',
          hide: true,
          width: 80,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'center'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Mileage',
          field: 'mileage',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMilage,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMilage,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'commaSeparatedNumber'
          // cellClass: 'textAlignUiRight'
        },
        {
          headerName: 'Seq No.',
          field: 'prtsequenceno',
          hide: true,
          width: 85,
          cellStyle: function(params) {
            return {
              textAlign: 'right'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        }
      ],

      rowData: [],
      overlayNoRowsTemplate:
        '<span style="padding: 10px; margin-top:50px;">No Rows To Show</span>',
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 48,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true,
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              suppressColumnExpandAll: false
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        suppressMovable: false,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false
      },
      excelStyles: [
        {
          id: 'summaryValue',
          font: {
            size: 11,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'subHeader',
          font: {
            size: 12,
            color: 'primary',
            bold: true
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          }
        },
        {
          id: 'summaryRed',
          font: {
            size: 11,
            color: '#ff0000'
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'summaryBlue',
          font: {
            size: 11,
            color: '0000FF'
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'summaryLabel',
          font: {
            size: 11,
            color: 'primary',
            bold: true
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'bigHeader',
          font: {
            size: 16,
            color: 'primary',
            bold: true
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          }
        },
        {
          id: 'fourDecimalPlaces',
          numberFormat: { format: '###0.0000' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'oneDecimalPlace',
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000',
            bold: true
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'blueFont',
          font: {
            color: '0000FF',
            bold: true
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#c65911', // Orange
            pattern: 'Solid'
          },
          font: {
            bold: true,
            size: 12,
            color: '#ffffffff' // White text
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center',
            wrapText: true
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'dateFormatter',
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlignCentercenter',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'textAlignCenter',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'textAlignUiLeft',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'textAlignUiRight',
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'commaSeparatedNumber',
          numberFormat: {
            format: '#,##0' // Comma formatting without decimal places
          },
          alignment: {
            horizontal: 'Right'
          }
        }
      ]
    };
  }
  cancelDilog = () => {
    this.setState({ openDilog: false });
  };
  CancelDilogAll = () => {
    this.setState({ openDilogAll: false });
  };
  handleSaveReport = () => {
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.parent != 'savedReports'
    ) {
      this.setState({ openDilogAll: true });
    } else if (this.state.filterText == 'CRANGE') {
      this.setState({
        openDilog: true
      });
    } else if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({
        openSaveDlg: true
      });
    }
  };
  handleCancelSaveReport = () => {
    this.setState({
      openSaveDlg: false
    });
    this.setState({
      copyFile: false
    });
    this.setState({
      copy: false
    });
    if (this.state.parent != 'savedReports') {
      this.setState({
        reportName: ''
      });
      this.setState({
        selectedType: 'private'
      });
    } else {
      this.setState({
        selectedType:
          this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.visibility
      });
    }
  };
  handleCopyReport = () => {
    if (this.state.filterText == 'CRANGE') {
      this.setState({
        openDilog: true
      });
    } else if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({
        openSaveDlg: true
      });
    }

    this.setState({
      copy: true
    });
    this.setState({
      copyFile: true
    });
    if (this.state.reportName != '') {
      this.setState({ reportNameCopy: this.state.reportName });
    }
    this.setState({
      reportName: ''
    });
  };
  onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;
    if (!nameRegex.test(e.target.value) && e.target.value) {
      this.setState({ requiredText: '' });
    } else {
      this.setState({
        errorReport: ''
      });
      this.setState({
        reportName: e.target.value
      });

      this.setState({ requiredText: false });
    }
  };
  handleCheckboxChange = e => {
    this.setState({
      selectedType: e.target.value
    });
  };
  handleSnackbarClose = () => {
    this.setState({ openSusSnackbar: false });
  };
  CancelAlertDilog = () => {
    this.setState({ openAlert: false });
  };
  transformData = data => {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, value.values])
    );
  };
  transformSortData = data => {
    return data.flatMap(({ colId, sort }) => [colId, sort]);
  };
  handleOkSaveReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    window.sortStatePrtMiss = this.gridApi.getSortModel();
    window.filterStatePrtMiss = this.gridApi.getFilterModel();

    window.colStatePrtMisses = this.gridColumnApi.getColumnState();

    if (
      window.filterStatePrtMiss != undefined ||
      window.sortStatePrtMiss != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStatePrtMiss)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStatePrtMiss)
      );
    } else {
      filterColumn = null;
      sortColumns = null;
    }

    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    if (this.state.parent == 'savedReports') {
      insertPartsMissesSavededReport(
        'update',
        iStoreId,
        this.state.reportName,
        this.state.filterText,
        userId,
        'Parts_Target_Misses',
        this.state.selectedType,
        userRole,
        sortColumns,
        filterColumn,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        this.state.displayCol,
        this.state.gridType,
        this.state.checked ? 'All' : 'Non_Compliance',
        this.state.PrevPayType,
        this.state.draggedColumn,
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,

        result => {
          if (
            result.data
              .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
              .results[0].status == 1
          ) {
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data
                  .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
                  .results[0].msg
            });
          }
        }
      );
    } else {
      insertPartsMissesSavededReport(
        'insert',
        iStoreId,
        this.state.reportName,
        this.state.filterText,
        userId,
        'Parts_Target_Misses',
        this.state.selectedType,
        userRole,
        sortColumns,
        filterColumn,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        this.state.displayCol,
        this.state.gridType,
        this.state.checked ? 'All' : 'Non_Compliance',
        this.state.PrevPayType,
        this.state.draggedColumn,
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,
        result => {
          if (
            result.data
              .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
              .results[0].status == 1
          ) {
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ reportName: '' });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data
                  .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
                  .results[0].msg
            });
          }
        }
      );
    }
  };
  handleSaveAsReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    window.sortStatePrtMiss = this.gridApi.getSortModel();
    window.filterStatePrtMiss = this.gridApi.getFilterModel();

    window.colStatePrtMisses = this.gridColumnApi.getColumnState();

    if (
      window.filterStatePrtMiss != undefined ||
      window.sortStatePrtMiss != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStatePrtMiss)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStatePrtMiss)
      );
    } else {
      filterColumn = null;
      sortColumns = null;
    }

    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    insertPartsMissesSavededReport(
      'insert',
      iStoreId,
      this.state.reportName,
      this.state.filterText,
      userId,
      'Parts_Target_Misses',
      this.state.selectedType,
      userRole,
      sortColumns,
      filterColumn,
      this.state.tech == undefined || this.state.tech.includes('All')
        ? null
        : this.state.tech,
      this.state.displayCol,
      this.state.gridType,
      this.state.checked ? 'All' : 'Non_Compliance',
      this.state.PrevPayType,
      this.state.draggedColumn,
      this.state.advisors == undefined || this.state.advisors.includes('All')
        ? null
        : this.state.advisors,
      result => {
        if (
          result.data
            .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
            .results[0].status == 1
        ) {
          this.setState({
            openSaveDlg: false
          });
          this.setState({
            errorReport: ''
          });
          this.setState({
            reportName: ''
          });
          this.setState({ openSusSnackbar: true });
          this.setState({ requiredText: false });
        } else {
          this.setState({ requiredText: true });
          this.setState({
            errorReport:
              result.data
                .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
                .results[0].msg
          });
        }
      }
    );
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValueMilage = params => {
    if (params && params.value != null && params.value != 0) {
      return params.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0';
    }
  };
  formatSheetName = sheetName => {
    // Maximum length for Excel sheet names is 31 characters
    const maxLength = 31;

    // Check if the sheet name exceeds the maximum length
    if (sheetName.length > maxLength) {
      // Truncate to 28 characters and add "..."
      return sheetName.substring(0, 28) + '...';
    }

    // If it's within the limit, return the original name
    return sheetName;
  };
  exportToPDF = () => {
    const { payType } = this.state;

    const sheetTitle =
      this.state.parent == 'savedReports'
        ? `Customer Pay Repair - Parts Target Misses - Saved Reports - ${this
            .props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.report_name &&
            this.props.history.location.state.report_name}`
        : payType === 'I'
        ? 'Internal Repair - Parts Target Misses'
        : 'Customer Pay Repair - Parts Target Misses';
    if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }
      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };

      const doc = new jsPDF('l', 'mm', columns.length <= 30 ? 'a2' : 'a1');
      const marginLeft = 5;
      const marginTop = 10;

      doc.setFontSize(16);
      doc.text(sheetTitle, marginLeft, marginTop);
      const summaryLabels = [
        'RO Count:',
        'Parts Count:',
        'Parts Cost:',
        'Target Price:',
        'Actual Sale:',
        'Over Sold:',
        'Under Sold:',
        'Net Difference:'
      ];
      const summaryValues = [
        String(
          this.state.roCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        ),
        String(
          this.state.jobCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        ),
        String(this.formatTotals(this.state.partsCost, 'partsCost')),
        String(this.formatTotals(this.state.targetPrice, 'targetPrice')),
        String(this.formatTotals(this.state.actualSale, 'actualSale')),
        String(this.formatTotals(this.state.overSell, 'overSold')),
        String(this.formatTotals(this.state.underSell, 'underSold')),
        String(this.formatTotals(this.state.diffSale, 'netDifference'))
      ];

      // doc.setFontSize(11);
      // let xPosition = marginLeft;
      // const yPosition = 20;
      // Add "Summary" heading
      doc.setFontSize(12);
      doc.setFont(undefined, 'bold');
      doc.setTextColor('black');
      doc.text('Summary', marginLeft, 16);

      // Continue with summary line
      doc.setFontSize(11);
      let xPosition = marginLeft;
      const yPosition = 22;

      summaryLabels.forEach((label, index) => {
        doc.setFont(undefined, 'normal');
        doc.setTextColor('black');
        doc.text(label, xPosition, yPosition);
        xPosition += doc.getTextWidth(label) + 1;

        doc.setFont(undefined, 'bold');
        let valueNormal = String(summaryValues[index]).replace(/[$,]/g, '');
        let value = summaryValues[index];

        if (
          label === 'Over Sold:' ||
          //label === 'Under Sold:' ||
          label === 'Net Difference:'
        ) {
          if (valueNormal < 0) {
            doc.setTextColor('red');
          } else if (valueNormal >= 0) {
            doc.setTextColor('blue');
          } else {
            doc.setTextColor('black');
          }
        } else if (label === 'Under Sold:') {
          doc.setTextColor('red');
        } else {
          doc.setTextColor('black');
        }

        doc.text(String(value), xPosition, yPosition);
        xPosition += doc.getTextWidth(String(value)) + 8;
      });

      doc.setFont(undefined, 'normal');

      const headers = columns.map(col => col.header);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];

          if (
            [
              'prtlist',
              'prtcost',
              'lbrsale',
              'prtextendedcost',
              'prtextendedsale'
            ].includes(col.field)
          ) {
            return this.formatCellValue({ value });
          } else if (
            ['targetPrice', 'targetExtendedPrice'].includes(col.field)
          ) {
            return this.formatCellValueTargetPrice({ value });
          } else if (col.field === 'grossprofitpercentage') {
            return this.formatCellValueGP({ value });
          } else if (col.field === 'variance') {
            const formattedValue = this.formatCellValueVariance({ value });

            return {
              content: formattedValue,
              color:
                value < 0 ? [255, 0, 0] : value > 0 ? [0, 0, 255] : [0, 0, 255]
            };
          } else if (col.field === 'variancePerc') {
            const formattedValue = this.formatCellValuePercent({ value });

            return {
              content: formattedValue,
              color:
                value < 0 ? [255, 0, 0] : value > 0 ? [0, 0, 255] : [0, 0, 255]
            };
          } else if (col.field === 'doorRate') {
            return this.formatCellValueRate({ value });
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }
        })
      );

      autoTable(doc, {
        startY: 24,
        head: [headers],
        body: data,
        didParseCell: function(data) {
          if (
            columns[data.column.index]?.field === 'variance' ||
            columns[data.column.index]?.field === 'variancePerc'
          ) {
            if (data.cell.raw && data.cell.raw.color) {
              data.cell.styles.textColor = data.cell.raw.color;
            }
          }
        },
        theme: 'grid',
        styles: {
          fontSize: 9,
          cellPadding: 1.5,
          halign: 'left'
        },
        headStyles: {
          fillColor: '#c65911',
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 10,
          halign: 'left'
        },
        tableWidth: 'auto',
        margin: { top: 3, left: 3, right: 3 },
        horizontalPageBreak: false,
        scaleFactor: 1.2
      });

      doc.save(sheetTitle + '.pdf');
    }
  };
  onBtExport = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      const { payType, rawGridApi } = this.state;

      const sheetTitle =
        this.state.parent == 'savedReports'
          ? `Customer Pay Repair - Parts Target Misses - Saved Reports - ${this
              .props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.report_name &&
              this.props.history.location.state.report_name}`
          : payType === 'I'
          ? 'Internal Repair - Parts Target Misses'
          : 'Customer Pay Repair - Parts Target Misses';
      const columnCount = this.state.columnDefs.length;
      const summaryHeader = [
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: sheetTitle },
            mergeAcross: columnCount - 1
          }
        ],
        [
          {
            styleId: 'subHeader',
            data: { type: 'String', value: 'Summary' },
            mergeAcross: columnCount - 1
          }
        ],
        [
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Ro Count:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.state.roCount
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Parts Count:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.state.jobCount
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Parts Cost:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.partsCost, 'partsCost')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Target Price:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.targetPrice, 'targetPrice')
            }
          }
        ],
        [
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Actual Sale:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.actualSale, 'actualSale')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Over Sold:' }
          },
          {
            styleId: this.state.overSell >= 0 ? 'summaryBlue' : 'summaryRed',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.overSell, 'overSold')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Under Sold:' }
          },
          {
            // styleId: this.state.underSell > 0 ? 'summaryBlue' : 'summaryRed',
            styleId: 'summaryRed',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.underSell, 'underSold')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Net Difference:' }
          },
          {
            styleId: this.state.diffSale >= 0 ? 'summaryBlue' : 'summaryRed',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.diffSale, 'netDifference')
            }
          }
        ],
        []
      ];

      const params = {
        sheetName: this.formatSheetName(sheetTitle),
        fileName: sheetTitle,
        headerRowHeight: 30,
        customHeader: summaryHeader,
        rowHeight: 18,

        processCellCallback: params => {
          processCells(params, 'parts');
          const colId = params.column.getColId();

          // if (
          //   // colId === 'lbrsale' ||
          //   // colId === 'lbrcost' ||
          //   // colId === 'prtextendedsale' ||
          //   // colId === 'prtextendedcost' ||
          //   // colId === 'lbrgrossprofit' ||
          //   // colId === 'prtsgrossprofit' ||
          //   // colId === 'elr'
          // ) {
          //   //
          //   return this.formatCellValue(params);
          // }

          if (colId === 'lbrsoldhours') {
            return this.formatCellValueWithOut$(params);
          }
          if (
            params.column.getColId() === 'variancePerc' ||
            params.column.getColId() === 'grossprofitpercentage' ||
            params.column.getColId() === 'lbrGrossprofitpercentage'
          ) {
            return this.formatCellValueGP(params);
            // let val = params.value;
            // if (val != null) {
            //   return val.endsWith('.00') ? val.slice(0, -3) + '%' : val + '%';
            // }
            // return '0%';
          }
          // if (colId === 'mileage') {
          //   return this.formatCellValueMilage(params);
          // }
          if (colId === 'markup') {
            return this.formatCellValueMarkup(params);
          }
          if (
            ['closeddate', 'opendate', 'gridDate'].some(id =>
              params.column.getColId().includes(id)
            )
          ) {
            return isValidDate(params.value)
              ? moment(params.value).format('MM/DD/YY')
              : params.value;
          }

          if (params.column.getColId() === 'prtpartno') {
            return '\u200C' + params.value;
          }

          return params.value;
        }
      };

      rawGridApi.exportDataAsExcel(params);
    }
  };

  componentDidMount() {
    if (this.state.selectedFilter == '')
      this.setState({ selectedFilter: 'duration' });
    if (this.state.selectedToggle == '')
      this.setState({ selectedToggle: 'MTD' });
    //this.getToggleOptions();
    this.getMonthYearForSelect();
    this.getTargetGridRates();
    this.getKpiToggleOptions();
    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousToggle &&
      this.props.history.location.state.previousToggle != 'undefined'
    ) {
      this.setState({
        previousToggle: this.props.history.location.state.previousToggle
      });
    }
  }

  getTargetGridRates = () => {
    getTargetGridRate(result => {
      if (result.data.statelessCcPhysicalRwGetDoorRate.nodes) {
        let target =
          result.data.statelessCcPhysicalRwGetDoorRate.nodes[0].value;
        this.setState({ targetGridRate: target });
      }
    });
  };
  getMatrixDetailsOld = (
    toggle,
    advisor,
    technician,
    filterStart,
    filterEnd
  ) => {
    this.getAgGridData(toggle, advisor, technician, filterStart, filterEnd);
  };
  getMatrixDetails = (toggle, advisor, technician, filterStart, filterEnd) => {
    getGridorMatrixMissesDetails('matrix_misses_type', '', result => {
      if (result) {
        let data = result;
        this.setState({
          payTypeList: lodash.uniq(data)
        });
        let selectedAdv =
          this.props?.history?.location?.state?.selectedAdvisor ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('serviceAdvisors'));
        // ReactSession.get('serviceAdvisors');

        let selectedTech =
          this.props?.history?.location?.state?.selectedTech ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('technicians'));
        //ReactSession.get('technicians');
        this.setState({ advisors: selectedAdv });
        this.setState({ tech: selectedTech });
        //  this.setState({ gridType: data[0] });
        this.getAgGridData(toggle, advisor, technician, filterStart, filterEnd);
      }
    });
  };

  getKpiToggleOptions = () => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        this.setState({
          dates: dataArr,
          thisWeek:
            moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY"),

          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          lastWeek:
            moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY"),

          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          lastTwoWeek:
            moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY"),

          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          mtd:
            moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY"),
          lastMonth: moment(dataArr[0].lastmonthstartdate).format('MMM'),
          lastThreeMonths:
            moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM'),
          lastQtr:
            moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM'),

          ytd:
            moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY"),
          lastTwelveMonths:
            moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY"),
          lastYear:
            moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        });

        if (this.state.filterStart == '' && this.state.filterEnd == '') {
          if (localStorage.getItem('kpiDataStatus') == 1) {
            this.setState({
              filterStart: moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
              filterEnd: moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'),
              toggleOption: 'MTD'
            });
          } else {
            this.setState({
              filterStart: moment(dataArr[0].lastmonthstartdate).format(
                'YYYY-MM-DD'
              ),
              filterEnd: moment(dataArr[0].lastmonthenddate).format(
                'YYYY-MM-DD'
              ),
              toggleOption: 'LMONTH'
            });
          }
        }
        this.getMatrixDetails(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          this.state.filterStart
            ? this.state.filterStart
            : localStorage.getItem('kpiDataStatus') == 1
            ? moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            : moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD'),

          this.state.filterEnd
            ? this.state.filterEnd
            : localStorage.getItem('kpiDataStatus') == 1
            ? moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
            : moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
        );
      }
    });
  };

  getMonthYearForSelect = () => {
    this.setState({
      monthYear: getLast13Months()
    });
    if (
      (this.state.monthYear && this.state.selectedMonthYear == '') ||
      this.state.selectedMonthYear == undefined ||
      !this.state.selectedMonthYear
    )
      this.setState({
        selectedMonthYear: this.state.monthYear[this.state.monthYear.length - 1]
      });
  };
  handleChange = event => {
    window.sortStatePrtMiss = {};
    window.filterStatePrtMiss = {};
    this.state.gridColumnApi.resetColumnState();
    this.state.rawGridApi.setSortModel(null);
    this.state.rawGridApi.setFilterModel(null);
    if (event.target.name == 'duration') {
      this.setState({ selectedToggle: event.target.value }, function() {
        this.getAgGridData(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          this.state.filterStart,
          this.state.filterEnd
        );
      });
    } else {
      this.setState({ selectedMonthYear: event.target.value }, function() {
        this.getAgGridData(this.state.selectedMonthYear);
      });
    }
  };

  handlerdChange = event => {
    if (event.target.value == 'duration') {
      this.setState({ selectedFilter: event.target.value }, function() {
        this.getAgGridData(this.state.selectedToggle);
      });
    } else {
      this.setState({ selectedFilter: event.target.value }, function() {
        this.getAgGridData(this.state.selectedMonthYear);
      });
    }
  };
  handleclick = params => {
    //  ReactSession.set('kpiHomeToggle', params);
    if (this.state.parent == 'savedReports') {
      this.props.history.push({
        pathname: '/ReportSaved',
        state: {
          toggleOptions: this.state.previousToggle
            ? this.state.previousToggle
            : 'MTD',
          payType: this.state.PrevPayType,
          gridType: this.state.PrevGridType,
          parent: this.state.parent,
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd,
          created_by: this.props.history.location.state.created_by
        }
      });
    } else {
      this.props.history.push({
        pathname: '/Home',
        state: {
          toggleOptions: this.state.previousToggle
            ? this.state.previousToggle
            : 'MTD',
          payType: this.state.PrevPayType,
          gridType: this.state.PrevGridType,
          parent: this.state.parent,
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd
        }
      });
    }
  };

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };

  formatCellValueTargetPrice = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  // formatCellValueTargetPrice = params => {
  //   if (
  //     params.value != null &&
  //     params.value != 0 &&
  //     params.data &&
  //     params.data.targetPrice != '' &&
  //     params.data.targetPrice != 0
  //   ) {
  //     return Math.sign(params.value) > -1
  //       ? '$' +
  //           parseFloat(params.value)
  //             .toFixed(2)
  //             .toString()
  //             .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  //       : '-$' +
  //           Math.abs(parseFloat(params.value))
  //             .toFixed(2)
  //             .toString()
  //             .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  //   } else if (
  //     (params.value == '' || params.value == null || params.value == 0) &&
  //     params.data &&
  //     params.data.targetPrice != '' &&
  //     params.data.targetPrice != 0
  //   ) {
  //     return '$0.00';
  //   } else {
  //     return 'N/A';
  //   }
  // };
  formatCellValueTargetPriceFilter = params => {
    if (
      params.data &&
      params.data.targetPrice != '' &&
      params.data.targetPrice != null &&
      params.data.targetPrice != 0
    ) {
      return Math.sign(params.data.targetPrice) > -1
        ? '$' +
            parseFloat(params.data.targetPrice)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.data.targetPrice))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return 'N/A';
    }
  };
  formatCellValueWithOut$ = params => {
    return params && params.value != null && params.value != 0
      ? parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : '0.00';
  };
  formatCellValueMarkup = params => {
    if (params && params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0%';
    }
  };

  // formatCellValueVariance = params => {
  //   if (
  //     params.value != null &&
  //     params.value != 0 &&
  //     params.data &&
  //     params.data.targetPrice != '' &&
  //     params.data.targetPrice != 0
  //   ) {
  //     return Math.sign(params.value) > -1
  //       ? '+$' +
  //           parseFloat(params.value)
  //             .toFixed(2)
  //             .toString()
  //             .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  //       : '-$' +
  //           Math.abs(parseFloat(params.value))
  //             .toFixed(2)
  //             .toString()
  //             .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  //   } else if (
  //     (params.value == '' || params.value == null || params.value == 0) &&
  //     params.data &&
  //     params.data.targetPrice != '' &&
  //     params.data.targetPrice != 0
  //   ) {
  //     return '$0.00';
  //   } else {
  //     return 'N/A';
  //   }
  // };
  formatCellValueVariance = params => {
    console.log('params==', params.value);
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '+$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueVarianceFilter = params => {
    if (
      params.data &&
      params.data.targetPrice != '' &&
      params.data.targetPrice != 0 &&
      params.data.variance != null &&
      params.data.variance != 0
    ) {
      return Math.sign(params.data.variance) > -1
        ? '+$' +
            parseFloat(params.data.variance)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.data.variance))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else if (
      params.data &&
      (params.data.variance == '' ||
        params.data.variance == null ||
        params.data.variance == 0) &&
      params.data.targetPrice != '' &&
      params.data.targetPrice != 0
    ) {
      return '$0.00';
    } else {
      return 'N/A';
    }
  };

  // formatCellValuePercent = params => {
  //   if (
  //     params.value != null &&
  //     params.value != 0 &&
  //     params.data &&
  //     params.data.targetPrice != '' &&
  //     params.data.targetPrice != 0
  //   ) {
  //     return (
  //       parseFloat(params.value)
  //         .toFixed(2)
  //         .toString()
  //         .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
  //     );
  //   } else if (
  //     (params.value == '' || params.value == null || params.value == 0) &&
  //     params.data &&
  //     params.data.targetPrice != '' &&
  //     params.data.targetPrice != 0
  //   ) {
  //     return '0.00%';
  //   } else {
  //     return 'N/A';
  //   }
  // };

  formatCellValuePercent = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.00%';
    }
  };

  formatCellValuePercentFilter = params => {
    if (
      params.data &&
      params.data.targetPrice != '' &&
      params.data.targetPrice != 0 &&
      params.data.variancePerc != null &&
      params.data.variancePerc != 0
    ) {
      return (
        parseFloat(params.data.variancePerc)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else if (
      params.data &&
      (params.data.variancePerc == '' ||
        params.data.variancePerc == null ||
        params.data.variancePerc == 0) &&
      params.data.targetPrice != '' &&
      params.data.targetPrice != 0
    ) {
      return '0.00%';
    } else {
      return 'N/A';
    }
  };
  onColumnMoved = params => {
    const columnOrder = params.columnApi
      .getAllGridColumns()
      .map(col => col.getColId());
    this.setState({ draggedColumn: columnOrder });
  };
  onGridReady = params => {
    const transformedFilterState = Object.fromEntries(
      Object.entries(this.state.filterCol).map(([key, values]) => [
        key,
        { values, filterType: 'set' }
      ])
    );
    const transformedSortState = [
      {
        colId: this.state.sortCol[0],
        sort: this.state.sortCol[1]
      }
    ];
    params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    if (
      this.gridApi &&
      (!this.state.rowData || this.state.rowData.length === 0)
    ) {
      this.gridApi.showNoRowsOverlay();
    }

    this.setState({ gridApi: params.api });
    this.setState({ rawGridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });
    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null ||
      lodash.isEmpty(this.props.history.location.state)
    ) {
      window.sortStatePrtMiss = {};
      window.filterStatePrtMiss = {};
    }
    if (
      this.gridColumnApi &&
      window.colStatePrtMisses &&
      (this.props.history == undefined ||
        (this.props.history &&
          this.props.history.location &&
          this.props.history.location.state == undefined) ||
        this.props.history.location.state.selectedGridType == undefined)
    ) {
      window.colStatePrtMisses = null;
      this.state.gridColumnApi.resetColumnState();
    }
    if (window.colStatePrtMisses && this.gridColumnApi) {
      this.gridColumnApi.setColumnState(window.colStatePrtMisses);
    }
    this.getKpiToggleOptions();
    this.gridApi.setSortModel(window.sortStatePrtMiss);
    this.gridApi.setFilterModel(window.filterStatePrtMiss);
    // this.gridColumnApi.setColumnState(window.colStatePrtMisses);
    this.setState({
      groupColumn: params.api.columnController.columnDefs
    });
    const groupColumn = this.state.groupColumn;
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.rawGridApi
    ) {
      groupColumn[2]['hide'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStatePrtMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStatePrtMiss);
      }
      if (window.colStatePrtMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStatePrtMisses);
      }
    } else if (this.state.rawGridApi) {
      groupColumn[2]['hide'] = true;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStatePrtMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStatePrtMiss);
      }
      if (window.colStatePrtMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStatePrtMisses);
      }
    }
    if (this.state.parent == 'savedReports') {
      window.filterStatePrtMiss = transformedFilterState;
      window.sortStatePrtMiss = transformedSortState;

      if (this.state.draggedColumn.length > 0) {
        window.colStatePrtMisses = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();

        // Reorder columns based on desiredOrder
        const orderedState = this.state.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col
              ? {
                  ...col,
                  order: index
                }
              : null;
          })
          .filter(Boolean);
        // Apply reordered column state
        window.colStatePrtMisses = orderedState;
        this.gridColumnApi.setColumnState(window.colStatePrtMisses);
        // this.gridColumnApi.setColumnState(orderedState);
      }

      if (this.state.displayCol.length > 0) {
        if (this.state.displayCol.includes('All')) {
        } else {
          window.colStatePrtMisses = this.gridColumnApi.getColumnState();
          const visibleColumns = this.state.columnDefs.filter(column =>
            this.state.displayCol.includes(column.field)
          );
          const visibleFields = visibleColumns.map(col => col.field);

          const updatedColState = window.colStatePrtMisses.map(col => ({
            ...col,
            hide: !visibleFields.includes(col.colId) // hide true for matched columns
          }));
          // this.gridColumnApi.setColumnState(updatedColState);
          window.colStatePrtMisses = updatedColState;
          this.gridColumnApi.setColumnState(window.colStatePrtMisses);
        }
      }

      this.gridApi.setFilterModel(window.filterStatePrtMiss);
      this.gridApi.setSortModel(window.sortStatePrtMiss);
    }
  };

  handleDoorRate = params => {
    const selectedStoreId = JSON.parse(localStorage.getItem('selectedStoreId'));
    if (selectedStoreId.length > 1) {
      this.setState({
        openStoreDlg: true
      });
    } else {
      window.sortStatePrtMiss = this.gridApi.getSortModel();
      window.filterStatePrtMiss = this.gridApi.getFilterModel();
      window.filterState = this.gridApi.getFilterModel();
      window.colStatePrtMisses = this.gridColumnApi.getColumnState();

      if (
        params.data.fleetOrPaytypeOrOpcodeFixedRate == 'matrix_calc' ||
        params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_matrix_calc' ||
        params.data.fleetOrPaytypeOrOpcodeFixedRate ==
          'cust_fleet_matrix_calc' ||
        params.data.fleetOrPaytypeOrOpcodeFixedRate ==
          'paytype_fleet_matrix_calc' ||
        params.data.fleetOrPaytypeOrOpcodeFixedRate ==
          'opcode_fleet_matrix_calc'
      ) {
        this.props.history.push({
          pathname: '/PartsMatrix',
          // pathname: '/PartsMatrixPricing',
          state: {
            pageType: 'PartsMisses',
            selectedFilter: this.state.selectedFilter,
            selectedToggle: this.state.selectedToggle,
            selectedMonthYear: this.state.selectedMonthYear,
            parent: this.state.parent,
            timeZone: this.state.timeZone,
            previousToggle: this.state.previousToggle,
            payType: this.state.payType,
            PrevPayType: this.state.PrevPayType,
            PrevGridType: this.state.PrevGridType,
            matrixDate: params.value,
            selectedGrid: '',
            showAllJobs: this.state.checked,
            partsSource: params.data.prtsource,
            storeId: params.data.storeId,
            filterStart: this.state.filterStart,
            filterEnd: this.state.filterEnd,
            selectedPartsFor: params.data.partsFor,
            gridType: this.state.gridType,
            selectedGridType: this.state.gridType,
            reportName: this.state.reportName,

            matrixType:
              params.data.fleetOrPaytypeOrOpcodeFixedRate ==
              'opcode_matrix_calc'
                ? params.data.lbropcode
                : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                  'cust_fleet_matrix_calc'
                ? params.data.customerName
                : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                  'paytype_fleet_matrix_calc'
                ? params.data.paytype
                : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                  'opcode_fleet_matrix_calc'
                ? params.data.lbropcode
                : params.data.matrixType.split('- ')[1]
          }
        });
      } else if (
        params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_calc' ||
        params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_fix_calc'
      ) {
        this.props.history.push({
          pathname: '/FixedRates',
          state: {
            pageType: 'PartsMisses',
            selectedFilter: this.state.selectedFilter,
            selectedToggle: this.state.selectedToggle,
            selectedMonthYear: this.state.selectedMonthYear,
            parent: this.state.parent,
            timeZone: this.state.timeZone,
            previousToggle: this.state.previousToggle,
            payType: this.state.payType,
            gridType: this.state.gridType,
            gridDoorRate: params.value,
            PrevPayType: this.state.PrevPayType,
            selectedGrid: '',
            PrevGridType: this.state.PrevGridType,
            showAllJobs: this.state.checked,
            fixedRate: params.value,
            tabSelection: 'one',
            opcode: params.data.lbropcode,
            filterStart: this.state.filterStart,
            filterEnd: this.state.filterEnd,
            selectedPartsFor: params.data.partsFor,
            selectedGridType: this.state.gridType
          }
        });
      } else if (
        params.data.fleetOrPaytypeOrOpcodeFixedRate == 'cust_fleet_calc' ||
        params.data.fleetOrPaytypeOrOpcodeFixedRate == 'paytype_fleet_calc' ||
        params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_fleet_calc'
      ) {
        this.props.history.push({
          pathname: '/FleetAccounts',
          state: {
            pageType: 'PartsMisses',
            selectedFilter: this.state.selectedFilter,
            selectedToggle: this.state.selectedToggle,
            selectedMonthYear: this.state.selectedMonthYear,
            parent: this.state.parent,
            timeZone: this.state.timeZone,
            previousToggle: this.state.previousToggle,
            payType: this.state.payType,
            // gridType: this.state.gridType
            //   ? this.state.gridType
            gridType: params.data.matrixType,
            gridDoorRate: params.value,
            PrevPayType: this.state.PrevPayType,
            selectedGrid: '',
            PrevGridType: this.state.PrevGridType,
            showAllJobs: this.state.checked,
            fixedRate: params.value,
            tabSelection: 'one',
            opcode: params.data.lbropcode,
            selectedOpcode: params.data.lbropcode,
            filterStart: this.state.filterStart,
            filterEnd: this.state.filterEnd,
            selectedPartsFor: params.data.partsFor,
            customerName: params.data.customerName,
            selectedPayType: params.data.paytype,
            fleetType: params.data.fleetOrPaytypeOrOpcodeFixedRate,
            selectedGridType: this.state.gridType
          }
        });
        return;
      } else {
        window.sortStatePrtMiss = this.gridApi.getSortModel();
        window.filterStatePrtMiss = this.gridApi.getFilterModel();
        window.filterState = this.gridApi.getFilterModel();
        window.colStatePrtMisses = this.gridColumnApi.getColumnState();

        if (
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'matrix_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_matrix_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate ==
            'cust_fleet_matrix_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate ==
            'paytype_fleet_matrix_calc'
        ) {
          this.props.history.push({
            pathname: '/PartsMatrix',
            // pathname: '/PartsMatrixPricing',
            state: {
              pageType: 'PartsMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              PrevPayType: this.state.PrevPayType,
              PrevGridType: this.state.PrevGridType,
              matrixDate: params.value,
              selectedGrid: '',
              showAllJobs: this.state.checked,
              partsSource: params.data.prtsource,
              storeId: params.data.storeId,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedPartsFor: params.data.partsFor,
              gridType: this.state.gridType,
              selectedGridType: this.state.gridType,

              matrixType:
                params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                'opcode_matrix_calc'
                  ? params.data.lbropcode
                  : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                    'cust_fleet_matrix_calc'
                  ? params.data.customerName
                  : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                    'paytype_fleet_matrix_calc'
                  ? params.data.paytype
                  : params.data.matrixType.split('- ')[1]
            }
          });
        } else if (
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_fix_calc'
        ) {
          this.props.history.push({
            pathname: '/FixedRates',
            state: {
              pageType: 'PartsMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              gridType: this.state.gridType,
              gridDoorRate: params.value,
              PrevPayType: this.state.PrevPayType,
              selectedGrid: '',
              PrevGridType: this.state.PrevGridType,
              showAllJobs: this.state.checked,
              fixedRate: params.value,
              tabSelection: 'one',
              opcode: params.data.lbropcode,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedPartsFor: params.data.partsFor,
              selectedGridType: this.state.gridType
            }
          });
        } else if (
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'cust_fleet_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'paytype_fleet_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_fleet_calc'
        ) {
          this.props.history.push({
            pathname: '/FleetAccounts',
            state: {
              pageType: 'PartsMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              // gridType: this.state.gridType
              //   ? this.state.gridType
              gridType: params.data.matrixType,
              gridDoorRate: params.value,
              PrevPayType: this.state.PrevPayType,
              selectedGrid: '',
              PrevGridType: this.state.PrevGridType,
              showAllJobs: this.state.checked,
              fixedRate: params.value,
              tabSelection: 'one',
              opcode: params.data.lbropcode,
              selectedOpcode: params.data.lbropcode,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedPartsFor: params.data.partsFor,
              customerName: params.data.customerName,
              selectedPayType: params.data.paytype,
              fleetType: params.data.fleetOrPaytypeOrOpcodeFixedRate,
              selectedGridType: this.state.gridType
            }
          });
        } else {
          this.props.history.push({
            pathname: '/FixedRates',
            state: {
              pageType: 'PartsMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              gridType: this.state.gridType,
              gridDoorRate: params.value,
              PrevPayType: this.state.PrevPayType,
              selectedGrid: '',
              PrevGridType: this.state.PrevGridType,
              showAllJobs: this.state.checked,
              fixedRate: params.value,
              tabSelection: 'two',
              paytype: params.data.paytype,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedPartsFor: params.data.partsFor,
              selectedGridType: this.state.gridType
            }
          });
        }
      }
    }
  };

  handleSearchByRo = params => {
    let filterColumn = {};
    let sortColumns = {};
    window.sortStatePrtMiss = this.gridApi.getSortModel();
    window.filterStatePrtMiss = this.gridApi.getFilterModel();
    if (
      window.filterStatePrtMiss != undefined ||
      window.sortStatePrtMiss != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStatePrtMiss)
      );
      sortColumns = this.transformSortData(window.sortStatePrtMiss);
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    window.filterState = this.gridApi.getFilterModel();
    window.colStatePrtMisses = this.gridColumnApi.getColumnState();
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        pageType: 'partsmisses',
        parent: this.props.parent,
        selectedFilter: this.state.selectedFilter,
        selectedToggle: this.state.selectedToggle,
        selectedMonthYear: this.state.selectedMonthYear,
        parent: this.state.parent,
        timeZone: this.state.timeZone,
        previousToggle: this.state.previousToggle,
        payType: this.state.payType,
        PrevPayType: this.state.PrevPayType,
        PrevGridType: this.state.PrevGridType,
        showAllJobs: this.state.checked,
        filterStart: this.state.filterStart,
        filterEnd: this.state.filterEnd,
        storeId: params.data.storeId,
        gridType: this.state.gridType,
        selectedGridType: this.state.gridType,
        reportName: this.state.reportName,
        filterText: this.state.filterText,
        visibility: this.state.selectedType,
        checkedColumns: this.state.displayCol,
        draggedColumn: this.state.draggedColumn,
        selectedAdvisor: this.state.advisors,
        selectedTech: this.state.tech,

        filterColumns: this.state.filterCol,
        sortColumns: sortColumns,
        jobType: this.state.jobType
      }
    });
  };

  getAgGridData(
    selectedVal,
    advisor,
    technician,
    filterStart,
    filterEnd,
    gridType
  ) {
    advisor = advisor ? advisor : ['All'];
    this.setState({ serviceAdvisors: advisor });
    this.setState({ Technicians: technician });
    // Show loading overlay before data fetch
    if (this.gridApi) {
      this.gridApi.showLoadingOverlay();
    }
    this.setState({ isLoading: true });

    const groupColumn = this.state.groupColumn;
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.rawGridApi
    ) {
      groupColumn[2]['hide'] = false;
      groupColumn[2]['suppressToolPanel'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStatePrtMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStatePrtMiss);
      }
      if (window.colStatePrtMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStatePrtMisses);
      }
    } else if (this.state.rawGridApi) {
      groupColumn[2]['hide'] = true;
      groupColumn[2]['suppressToolPanel'] = true;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStatePrtMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStatePrtMiss);
      }
      if (window.colStatePrtMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStatePrtMisses);
      }
    }

    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousGridType
    ) {
      this.setState({
        PrevGridType: this.props.history.location.state.previousGridType
      });
    }
    // this.setState({ isLoading: true });
    this.getMonthYearForSelect();
    let filterBy = selectedVal;
    let filterType = this.state.selectedFilter;
    if (
      filterStart != undefined &&
      filterEnd != undefined &&
      filterStart != '' &&
      filterEnd != ''
    ) {
      this.setState({ filterStart: filterStart, filterEnd: filterEnd });
    }
    this.gridApi && this.gridApi.setFilterModel(null);
    if (
      (this.state.advisors == null ||
        this.state.advisors == undefined ||
        (this.state.advisors && this.state.advisors.includes('All'))) &&
      (ReactSession.get('serviceAdvisors') == undefined ||
        (ReactSession.get('serviceAdvisors') &&
          ReactSession.get('serviceAdvisors').includes('All') == true)) &&
      (this.state.tech == null ||
        this.state.tech == undefined ||
        (this.state.tech && this.state.tech.includes('All'))) &&
      (ReactSession.get('technicians') == undefined ||
        (ReactSession.get('technicians') &&
          ReactSession.get('technicians').includes('All') == true))
    ) {
      if (
        this.state.filterStart != undefined &&
        this.state.filterEnd != undefined &&
        this.state.filterStart != '' &&
        this.state.filterEnd != ''
      ) {
        getDataForPartsMisses(
          filterBy,
          filterType,
          this.state.timeZone,
          this.state.payType,
          this.state.filterStart,
          this.state.filterEnd,
          result => {
            this.setState({ isLoading: false });
            if (
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns
            ) {
              let resultArr =
                result.data
                  .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                  .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns;

              resultArr = resultArr.map(item => {
                if (
                  item.customerName &&
                  typeof item.customerName === 'string'
                ) {
                  item.customerName =
                    item.customerName.charAt(0).toUpperCase() +
                    item.customerName.slice(1);
                }
                return item;
              });
              // let matrixType = resultArr.map(item => item.matrixType);
              // if (matrixType.length > 0) {
              //   this.setState({
              //     payTypeList: lodash.uniq(matrixType)
              //   });
              // }

              if (this.state.gridType != 'All') {
                resultArr = resultArr.filter(
                  item => item.matrixType == this.state.gridType
                );
              }

              this.setState({ completeData: resultArr });
              this.showAllLaborMissesData(resultArr, this.state.checked);
              if (window.filterStatePrtMiss != undefined) {
                this.filterByValue();
              }
              this.setState({ isLoading: false });
              if (this.gridApi) {
                if (this.state.rowData.length > 0) {
                  this.gridApi.hideOverlay();
                } else {
                  this.gridApi.showNoRowsOverlay();
                }
              }
            }
          }
        );
      }
    } else {
      if (
        this.state.filterStart != undefined &&
        this.state.filterEnd != undefined &&
        this.state.filterStart != '' &&
        this.state.filterEnd != ''
      ) {
        getDataForAdvisorAndTechPartsMisses(
          filterBy,
          filterType,
          this.state.timeZone,
          this.state.payType,
          this.state.advisors && this.state.advisors != undefined
            ? //  &&
              // !this.state.advisors.includes('All')
              this.state.advisors
            : ReactSession.get('serviceAdvisors'),
          this.state.tech && this.state.tech != undefined
            ? // &&
              // !this.state.tech.includes('All')
              this.state.tech
            : ReactSession.get('technicians'),
          this.state.filterStart,
          this.state.filterEnd,
          result => {
            this.setState({ isLoading: false });
            if (
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns
            ) {
              let resultArr =
                result.data
                  .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                  .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns;

              this.setState({ completeData: resultArr });
              this.showAllLaborMissesData(resultArr, this.state.checked);
              this.setState({ isLoading: false });

              if (this.gridApi) {
                this.gridApi.hideOverlay();
              }
              if (window.filterStatePrtMiss != undefined) {
                this.filterByValue();
              }
            }
          }
        );
      }
    }
  }
  showAllLaborMissesData = (resultArr, checked) => {
    if (
      resultArr.length > 0 ||
      (this.state.completeData && this.state.completeData.length > 0)
    ) {
      resultArr = resultArr.length > 0 ? resultArr : this.state.completeData;
      let filteredArr = resultArr.filter(item => item.compliance == 'FALSE');
      this.setState({
        rowData: checked ? resultArr : filteredArr
      });
      this.setState({ isLoading: false });
      this.getTotalsForDisplay(checked ? resultArr : filteredArr);
    } else {
      this.setState({ isLoading: false });
      this.setState({
        rowData: resultArr
      });
      this.getTotalsForDisplay(resultArr);
    }
  };
  getTotalsForDisplay = data => {
    var rowData = data ? data : this.state.rowData;
    var partsCost = 0;
    var targetPrice = 0;
    var actualSale = 0;
    var overSell = 0;
    var underSell = 0;
    var diffSale = 0;
    var jobCount = 0;
    var ronumberArr = [];
    if (rowData.length > 0) {
      jobCount = rowData.length;
      rowData.map(item => {
        partsCost += Number(item.prtextendedcost);
        targetPrice += Number(item.targetExtendedPrice);
        if (
          Number(item.variance) != 0 ||
          (item.targetPrice != '' && item.targetPrice != 0)
        ) {
          actualSale += Number(item.prtextendedsale);
        }
        if (Number(item.variance) >= 0) {
          overSell += Number(item.variance);
        } else {
          underSell += Number(item.variance);
        }
      });
      diffSale = actualSale - targetPrice;
      ronumberArr = [...new Set(rowData.map(obj => obj.ronumber))];
    }
    this.setState({
      jobCount: jobCount,
      partsCost: partsCost,
      targetPrice: targetPrice,
      actualSale: actualSale,
      overSell: overSell,
      underSell: underSell,
      diffSale: diffSale,
      roCount: ronumberArr.length
    });
  };
  handleAllJobs = event => {
    this.setState({
      checked: event.target.checked
    });
    this.showAllLaborMissesData([], event.target.checked);
  };

  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach(field => {
      const filter = filterValues[field];
      if (filter?.filterType === 'set') {
        const selectedValues = filter.values || [];
        if (selectedValues.length === 0) {
          this.setState({ zeroFilter: true });
        } else {
          this.setState({ zeroFilter: false });
        }
      }
    });
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    this.getTotalsForDisplay(rowData);
  };
  onColumnVisible = params => {
    const columnApi = params.columnApi || this.gridColumnApi;
    const gridApi = this.gridApi;
    const { column, visible } = params;
    if (column == null && visible == false) {
      this.getTotalsForDisplay([]);
    } else {
      let rowData = [];
      this.gridApi.forEachNodeAfterFilter(node => {
        rowData.push(node.data);
      });
      this.getTotalsForDisplay(rowData);
    }
    const visibleColumns = columnApi.getAllDisplayedColumns();
    const rowCount = gridApi.getDisplayedRowCount();

    const colIds = visibleColumns.map(col => col.colId);
    this.setState({ displayCol: colIds });
  };
  resetRawData = () => {
    if (this.state.parent == 'savedReports') {
      const propsData = this.props.history && this.props.history.location.state;

      this.setState({
        isLoading: true
      });
      const transformedFilterState = Object.fromEntries(
        Object.entries(propsData.filterColumns).map(([key, values]) => [
          key,
          { values, filterType: 'set' }
        ])
      );
      const transformedSortState = [
        {
          colId: propsData.sortColumns[0],
          sort: propsData.sortColumns[1]
        }
      ];
      window.filterStatePrtMiss = transformedFilterState;
      window.sortStatePrtMiss = transformedSortState;
      this.gridApi.setFilterModel(window.filterStatePrtMiss);
      this.gridApi.setSortModel(window.sortStatePrtMiss);
      if (propsData.draggedColumn.length > 0) {
        window.colStatePrtMisses = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();
        const orderedState = propsData.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col ? { ...col, order: index } : null;
          })
          .filter(Boolean);
        window.colStatePrtMisses = orderedState;
        this.gridColumnApi.setColumnState(window.colStatePrtMisses);
      }

      if (
        propsData.checkedColumns.length > 0 &&
        propsData.checkedColumns[0] != 'All'
      ) {
        window.colStatePrtMisses = this.gridColumnApi.getColumnState();
        const visibleColumns = this.state.columnDefs.filter(column =>
          propsData.checkedColumns.includes(column.field)
        );
        const visibleFields = visibleColumns.map(col => col.field);
        const updatedColState = window.colStatePrtMisses.map(col => ({
          ...col,
          hide: !visibleFields.includes(col.colId)
        }));
        window.colStatePrtMisses = updatedColState;
        this.gridColumnApi.setColumnState(window.colStatePrtMisses);
      }
      this.setState({ gridType: propsData.gridType });
      this.setState({ payType: propsData.payType });
      this.setState({ tech: propsData.selectedTech });
      this.setState({ advisors: propsData.selectedAdvisor });
      this.setState({ selectedType: propsData.visibility });
      this.setState({ filterText: propsData.filterText });
      this.setState({ checked: propsData.jobType == 'All' ? true : false });
      this.setState({ zeroFilter: false });
      setTimeout(() => {
        this.setState({ filterStart: propsData.filterStart });
        this.setState({ filterEnd: propsData.filterEnd });
        this.setState({
          isLoading: false
        });
      }, 500);
    } else {
      window.sortStatePrtMiss = {};
      window.filterStatePrtMiss = {};
      this.state.gridColumnApi.resetColumnState();
      this.setState({ zeroFilter: false });
      if (this.gridApi) {
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
      }
    }
  };

  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStatePrtMiss);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  formatTotals = (value, type) => {
    if (value != null && value != 0) {
      if (
        type == 'targetPrice' ||
        type == 'actualSale' ||
        type == 'partsCost'
      ) {
        return Math.sign(value) > -1
          ? '$' +
              parseFloat(value)
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : '-$' +
              Math.abs(parseFloat(value))
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        return Math.sign(value) > -1
          ? '+$' +
              parseFloat(value)
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : '-$' +
              Math.abs(parseFloat(value))
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
    } else {
      if (type == 'overSold' || type == 'netDifference') {
        return '$0';
      } else if (type == 'underSold') {
        return '$0';
      } else {
        return '$0';
      }
    }
  };

  handleJobChange = type => {
    if (type == 'all') {
      this.setState({ checked: true });
    } else if (type == 'non-compliant') {
      this.setState({ checked: false });
    }
    this.showAllLaborMissesData([], type == 'all' ? true : false);
    // window.sortStatePrtMiss = {};
    // window.filterStatePrtMiss = {};
    this.state.gridColumnApi.resetColumnState();
    this.state.rawGridApi.setSortModel(null);
    this.state.rawGridApi.setFilterModel(null);
  };
  handleCallback = (event, picker) => {
    this.setState({ filterStart: picker.startDate.format('YYYY-MM-DD') });
    this.setState({ filterEnd: picker.endDate.format('YYYY-MM-DD') });
    this.setState({ filterChanged: true });
    // if (
    //   this.props.history &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.selectedToggle &&
    //   this.state.parent == 'Home'
    // ) {
    this.getFilterText(picker.chosenLabel);
    // }
  };
  getFilterText = label => {
    var filterText;

    if (label.includes('Yesterday')) {
      filterText = 'YESDT';
    } else if (label.includes('Day Before Yest')) {
      filterText = 'DBYESDT';
    } else if (label.includes('Last Week')) {
      filterText = 'LWEEK';
    } else if (label.includes('This Month')) {
      filterText = 'MTD';
    } else if (label.includes('Last Month')) {
      filterText = 'LMONTH';
    } else if (label.includes('Last 3 Mths')) {
      filterText = 'PLMTHREE';
    } else if (label.includes('Last Qtr')) {
      filterText = 'LQRTR';
    } else if (label.includes('YTD')) {
      filterText = 'YTD';
    } else if (label.includes('Last 12 Mths')) {
      filterText = 'PLYONE';
    } else if (label.includes('Last Year')) {
      filterText = 'LYEAR';
    } else if (label.includes('Custom Range')) {
      filterText = 'CRANGE';
    } else if (label.includes('This Week')) {
      filterText = 'THISWEEK';
    } else if (label.includes('Last 2 Weeks')) {
      filterText = 'LTWOWEEK';
    }
    this.setState({ filterText: filterText });
    ReactSession.set('kpiHomeToggle', filterText);
    return filterText;
  };
  handlePayTypeChange = event => {
    this.setState({
      rowData: []
    });

    this.setState({ gridType: event.target.value }, function() {
      this.getAgGridData(
        this.state.selectedToggle,
        ReactSession.get('serviceAdvisors'),
        ReactSession.get('technicians'),
        this.state.filterStart,
        this.state.filterEnd,
        event.target.value
      );
    });
  };
  handleCancelAllStores = () => {
    this.setState({
      openStoreDlg: false
    });
  };

  linkStyle = () => {
    return {
      pointerEvents:
        this.state.createdBy !== '' &&
        this.state.createdBy === localStorage.getItem('userID')
          ? 'auto'
          : this.state.createdBy !== '' &&
            this.state.createdBy !== localStorage.getItem('userID') &&
            !this.props.keycloak.realmAccess.roles.includes('superadmin')
          ? 'none'
          : 'auto',
      color:
        this.state.createdBy !== '' &&
        this.state.createdBy === localStorage.getItem('userID')
          ? '#003d6b'
          : this.state.createdBy !== '' &&
            this.state.createdBy !== localStorage.getItem('userID') &&
            !this.props.keycloak.realmAccess.roles.includes('superadmin')
          ? 'grey'
          : '#003d6b'
    };
  };

  handleMailClick = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({ openDialogue: true });

      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }
      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };

      // const doc = new jsPDF('l', 'mm', 'a3');
      const marginLeft = 5;
      const marginTop = 10;

      // doc.setFontSize(16);
      // doc.text(
      //   this.state.payType == 'I'
      //     ? 'Internal Repair - Parts Target Misses'
      //     : 'Customer Pay Repair - Parts Target Misses',
      //   marginLeft,
      //   marginTop
      // );
      const summaryLabels = [
        'RO Count:',
        'Parts Count:',
        'Parts Cost:',
        'Target Price:',
        'Actual Sale:',
        'Over Sold:',
        'Under Sold:',
        'Net Difference:'
      ];
      const summaryValues = [
        String(
          this.state.roCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        ),
        String(
          this.state.jobCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        ),
        String(this.formatTotals(this.state.partsCost, 'partsCost')),
        String(this.formatTotals(this.state.targetPrice, 'targetPrice')),
        String(this.formatTotals(this.state.actualSale, 'actualSale')),
        String(this.formatTotals(this.state.overSell, 'overSold')),
        String(this.formatTotals(this.state.underSell, 'underSold')),
        String(this.formatTotals(this.state.diffSale, 'netDifference'))
      ];

      // doc.setFontSize(11);
      let xPosition = marginLeft;
      const yPosition = 20;

      summaryLabels.forEach((label, index) => {
        // doc.setFont(undefined, 'normal');
        // doc.setTextColor('black');
        // doc.text(label, xPosition, yPosition);
        // xPosition += doc.getTextWidth(label) + 1;
        // doc.setFont(undefined, 'bold');
        // let valueNormal = String(summaryValues[index]).replace(/[$,]/g, '');
        // let value = summaryValues[index];
        // if (
        //   label === 'Over Sold:' ||
        //   label === 'Under Sold:' ||
        //   label === 'Net Difference:'
        // ) {
        //   if (valueNormal < 0) {
        //     doc.setTextColor('red');
        //   } else if (valueNormal >= 0) {
        //     doc.setTextColor('blue');
        //   } else {
        //     doc.setTextColor('black');
        //   }
        // } else {
        //   doc.setTextColor('black');
        // }
        // doc.text(String(value), xPosition, yPosition);
        // xPosition += doc.getTextWidth(String(value)) + 8;
      });

      // doc.setFont(undefined, 'normal');

      const headers = columns.map(col => col.header);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];

          if (
            [
              'prtlist',
              'prtcost',
              'lbrsale',
              'prtextendedcost',
              'prtextendedsale'
            ].includes(col.field)
          ) {
            return this.formatCellValue({ value });
          } else if (
            ['targetPrice', 'targetExtendedPrice'].includes(col.field)
          ) {
            return this.formatCellValueTargetPrice({ value });
          } else if (col.field === 'grossprofitpercentage') {
            return this.formatCellValueGP({ value });
          } else if (col.field === 'variance') {
            const formattedValue = this.formatCellValueVariance({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'variancePerc') {
            const formattedValue = this.formatCellValuePercent({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'doorRate') {
            return this.formatCellValueRate({ value });
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }
        })
      );
      this.setState({ rowDataTotal: rowData });
      this.setState({ headerData: headers });
      this.setState({ rowDataPdf: data });
      let userList = [];
      getEmail('NULL', 'NULL', 'view', 'Client_Report_Card_1_Month', result => {
        if (
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
            0
        ) {
          userList.push(
            result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
              item => item.value
            )
          );
          this.setState({ mailUsers: userList });
        } else {
          this.setState({ mailUsers: [] });
        }
      });
    }
  };
  handleCloseEmail = () => {
    this.setState({ openDialogue: false });
  };

  render() {
    var gridDate = '';
    var gridDateArr1 = [];
    var gridDateArr = [];

    if (this.state.rowData.length > 0) {
      gridDateArr1 = lodash.uniqBy(this.state.rowData, obj => obj.gridDate);
      gridDateArr = gridDateArr1.map(item => item.gridDate);
      if (gridDateArr.length > 0) {
        gridDate = moment(lodash.max(gridDateArr)).format('MM/DD/YY');
      }
    }
    const { classes } = this.props;
    //   $('#parts-matrics-misses').addClass('active-menu');
    let Title = 'Customer Pay Repair - Parts Target Misses';
    if (this.state.payType == 'I') {
      Title = 'Internal Repair - Parts Target Misses';
    }
    const titleName =
      this.state.parent == 'savedReports'
        ? `${Title} - Saved Reports - ${this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.report_name &&
            this.props.history.location.state.report_name}  `
        : Title;
    return (
      <div>
        <Page title={Title}></Page>
        {// JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
        // ((this.props.location &&
        //   this.props.location.state &&
        //   this.props.location.state.parent &&
        //   this.props.location.state.parent != 'Home') ||
        //   (this.props.location && this.props.location.state == undefined)) ? (
        //   <Redirect to="/errors/error-404" />
        // ) :
        localStorage.getItem('versionFlag') == 'FALSE' ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <React.Fragment>
            <Paper
              square
              style={{
                margin: '10px',
                paddingTop: '6px',
                height: '40px',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
            >
              <Grid
                container
                className={clsx(this.props.titleContainer, 'reset-dashboard')}
              >
                <Grid
                  item
                  xs={4}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  {(this.state.storeSwitch == false &&
                    this.state.parent == 'savedReports') ||
                  (this.state.storeSwitch == false &&
                    this.props.history &&
                    this.props.history.location.state &&
                    this.props.history.location.state.selectedToggle &&
                    (this.state.parent == 'Home' ||
                      this.state.parent == 'Extended_View')) ? (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      onClick={this.handleclick}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  ) : (
                    ''
                  )}
                </Grid>
                <Grid
                  item
                  xs={8}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(this.props.mainLabel, 'main-title')}
                    style={{ width: '100%' }}
                  >
                    {/* Parts Matrix Misses */}
                    {titleName}
                    {/* Customer Pay Repair - Parts Target Misses */}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
            <Paper
              square
              style={{
                margin: 8,
                marginLeft: 8,
                height: 50,
                paddingTop: 4,
                paddingLeft: 4
              }}
            >
              {this.state.dates && this.state.dates[0] && (
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={clsx(classes.formControl, 'input-container')}
                  style={{ margin: 5 }}
                >
                  <DateRangePicker
                    initialSettings={{
                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },
                      ranges: {
                        ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.yesterDay]: [
                          moment(
                            this.state.dates[0] && this.state.dates[0].yesterday
                          ).toDate(),
                          moment(
                            this.state.dates[0] && this.state.dates[0].yesterday
                          ).toDate()
                        ],
                        ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.dayBfYest]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].dayBeforeYesterday
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].dayBeforeYesterday
                          ).toDate()
                        ],
                        ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.thisWeek]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].thisweekstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].thisweekenddate
                          ).toDate()
                        ],
                        ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastWeek]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastweekstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastweekenddate
                          ).toDate()
                        ],
                        ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastTwoWeek]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwoweekstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwoweekenddate
                          ).toDate()
                        ],
                        ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.mtd]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].mtdstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].mtdenddate
                          ).toDate()
                        ],
                        ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastMonth]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastmonthstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastmonthenddate
                          ).toDate()
                        ],
                        ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastThreeMonths]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastthreemonthstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastthreemonthenddate
                          ).toDate()
                        ],
                        ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastQtr]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastquarterstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastquarterenddate
                          ).toDate()
                        ],
                        ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.ytd]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].ytdstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].ytdenddate
                          ).toDate()
                        ],
                        ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastTwelveMonths]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwelvemonthstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwelvemonthenddate
                          ).toDate()
                        ],
                        ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastYear]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastyearstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastyearenddate
                          ).toDate()
                        ]
                      },
                      //  maxDate: moment().toDate(),
                      //maxDate: moment(this.state.closedDate).toDate(),
                      maxDate: moment(
                        this.state.dates[0] && this.state.dates[0].today
                      ).toDate(),
                      alwaysShowCalendars: false,
                      applyClass: clsx(classes.calButton, 'apply-btn'),
                      cancelClass: clsx(classes.calButton, 'apply-btn'),
                      startDate:
                        this.state.filterStart && this.state.filterStart != ''
                          ? moment(this.state.filterStart).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(
                              this.state.dates[0] &&
                                this.state.dates[0].mtdstartdate
                            ).toDate()
                          : moment(
                              this.state.dates[0] &&
                                this.state.dates[0].lastmonthstartdate
                            ).toDate(),

                      endDate:
                        this.state.filterEnd && this.state.filterEnd != ''
                          ? moment(this.state.filterEnd).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(
                              this.state.dates[0] &&
                                this.state.dates[0].mtdenddate
                            ).toDate()
                          : moment(
                              this.state.dates[0] &&
                                this.state.dates[0].lastmonthenddate
                            ).toDate()

                      // startDate:
                      //   this.state.filterStart && this.state.filterStart != ''
                      //     ? moment(this.state.filterStart).toDate()
                      //     : moment(
                      //         this.state.dates[0] &&
                      //           this.state.dates[0].mtdstartdate
                      //       ).toDate(),
                      // endDate:
                      //   this.state.filterEnd && this.state.filterEnd != ''
                      //     ? moment(this.state.filterEnd).toDate()
                      //     : moment(
                      //         this.state.dates[0] &&
                      //           this.state.dates[0].mtdenddate
                      //       ).toDate()
                      //showDropdowns: true
                    }}
                    onApply={this.handleCallback}
                  >
                    <input
                      type="text"
                      className="datepicker"
                      id="picker"
                      name="picker"
                      aria-labelledby="label-picker"
                    />
                    {/* <TextField
                      id="outlined-basic"
                      label="Select Date"
                      size="small"
                      //onChange={}
                      value={this.state.value}
                      variant="outlined"
                    /> */}
                  </DateRangePicker>
                  <label class="labelpicker" for="picker" id="label-picker">
                    <div class="textpicker">Select Date</div>
                  </label>
                </FormControl>
              )}
              {this.state.payTypeList.length > 1 ? (
                <span className="payTypeList">
                  <FormControl margin="dense" variant="outlined">
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ marginTop: -4 }}
                    >
                      Misses
                    </InputLabel>
                    <Select
                      margin="dense"
                      variant="outlined"
                      label="Filter By"
                      name="duration"
                      className={'laborPartsGrid'}
                      // value={this.state.payType}
                      value={this.state.gridType}
                      onChange={this.handlePayTypeChange}
                      disabled={
                        this.state.selectedFilter == 'duration' ? false : true
                      }
                    >
                      <MenuItem value="All">All</MenuItem>
                      {this.state.payTypeList.map(item => (
                        <MenuItem value={item}>{item}</MenuItem>
                        // <MenuItem value={item.substring(0, 1)}>{item}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </span>
              ) : null}

              <span className={classes.ButtonSelect} id="ButtonSelectHome">
                <Button
                  className={
                    this.state.checked == false
                      ? classes.reportButtonSelect
                      : classes.reportButton
                  }
                  id="nonCompliantjobs"
                  variant="outlined"
                  style={{ textTransform: 'none' }}
                  onClick={() => this.handleJobChange('non-compliant')}
                >
                  Non-Compliant&nbsp;Jobs
                </Button>
                <Button
                  className={
                    this.state.checked
                      ? classes.reportButtonSelect
                      : classes.reportButton
                  }
                  id="allJobs"
                  variant="outlined"
                  style={{ textTransform: 'none' }}
                  onClick={() => this.handleJobChange('all')}
                >
                  All Jobs
                </Button>
              </span>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.resetBtn, 'reset-btn')}
                onClick={this.resetRawData}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
              <div className="allItems">
                <div className={clsx(classes.dataAsOf)}>
                  {this.state.closedDate ? (
                    <Typography
                      variant="h6"
                      align="right"
                      style={{
                        fontSize: 12,
                        color: '#7987a1',
                        fontWeight: 'bold'
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginTop: 8
                        }}
                      >
                        <div>Data&nbsp;as&nbsp;of&nbsp;:</div>
                        <div className={classes.dataAsOfValue}>
                          {moment(this.state.closedDate).format('MM/DD/YY')}
                        </div>
                      </div>
                    </Typography>
                  ) : (
                    ''
                  )}
                </div>
                <Tooltip title="Save Report">
                  <Link
                    className={classes.linkItem}
                    style={{
                      //   ...linkStyle
                      paddingTop: 20,
                      paddingRight: 5,

                      cursor: 'pointer',
                      float: 'right',
                      pointerEvents: 'auto',
                      cursor: 'pointer'
                    }}
                    onClick={this.handleSaveReport}
                  >
                    <SaveIcon className="saveicon" />
                  </Link>
                </Tooltip>
                {this.state.parent == 'savedReports' && (
                  <Tooltip title="Rename and Copy">
                    <Link
                      className={classes.linkItem}
                      // style={linkStyleCopy}
                      style={{
                        display: 'block',
                        marginLeft: -3
                        // display:
                        //   (createdBy != '' &&
                        //     createdBy == localStorage.getItem('userID')) ||
                        //   selectedType == 'public' ||
                        //   props.keycloak.realmAccess.roles.includes('superadmin')
                        //     ? 'block'
                        //     : 'none'
                      }}
                      onClick={this.handleCopyReport}
                    >
                      <FileCopyOutlinedIcon
                        style={{
                          height: '0.73em',
                          weight: '0.8em',
                          marginRight: '-4px',
                          marginTop: '23px'

                          //                         height: 0.76em;
                          // margin-right: -8px;
                          // margin-top: 2px;
                          // display: allStore ? 'none' : 'block'
                        }}
                      />
                    </Link>
                  </Tooltip>
                )}

                <Tooltip title="Email Now">
                  <Link
                    className={classes.linkItem}
                    // style={this.linkStyle}
                    onClick={() => this.handleMailClick()}
                    style={{
                      cursor: 'pointer',
                      marginRight: -2
                    }}
                  >
                    <MailIcon
                      style={{
                        height: '1.15em',
                        weight: '1.0em',
                        marginTop: '17px'
                      }}
                    />
                  </Link>
                </Tooltip>
                <Tooltip title="Export To PDF">
                  <Link
                    id="export-to-pdf"
                    style={{
                      paddingRight: 5,
                      marginLeft: 5,
                      marginTop: 23,
                      cursor: 'pointer',
                      float: 'right'
                    }}
                    onClick={this.exportToPDF}
                  >
                    <i
                      className="fas fa-file-pdf"
                      style={{
                        color: 'red'
                      }}
                    ></i>
                  </Link>
                </Tooltip>
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      paddingTop: 23,
                      paddingRight: 10,
                      cursor: 'pointer',
                      float: 'right'
                    }}
                    onClick={this.onBtExport}
                  >
                    <i
                      className="fas fa-file-excel"
                      style={{ color: 'green' }}
                    ></i>
                  </Link>
                </Tooltip>
              </div>
            </Paper>
          </React.Fragment>
        )}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : (
          <Paper
            square
            style={{
              margin: 8,
              marginLeft: 8,
              height: 35,
              paddingTop: 4,
              paddingLeft: 4
            }}
          >
            <Grid container xs={12} className={classes.rankedTableContainer}>
              <Grid xs={12}>
                <div>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      RO&nbsp;Count&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Parts&nbsp;Count&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.state.jobCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Parts&nbsp;Cost&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(this.state.partsCost, 'partsCost')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Target&nbsp;Price&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(
                          this.state.targetPrice,
                          'targetPrice'
                        )}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Actual&nbsp;Sale&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(this.state.actualSale, 'actualSale')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Over&nbsp;Sold&nbsp;:&nbsp;
                      <span style={{ color: '#0000FF', marginLeft: 3 }}>
                        {this.formatTotals(this.state.overSell, 'overSold')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Under&nbsp;Sold&nbsp;:&nbsp;
                      <span style={{ color: 'red', marginLeft: 3 }}>
                        {this.formatTotals(this.state.underSell, 'underSold')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Net&nbsp;Difference&nbsp;:&nbsp;
                      <span
                        style={{
                          color:
                            this.state.diffSale >= 0
                              ? '#0000FF'
                              : this.state.diffSale < 0
                              ? 'red'
                              : '#003d6b'
                        }}
                      >
                        {this.formatTotals(
                          this.state.diffSale,
                          'netDifference'
                        )}
                      </span>
                    </Typography>
                  </Button>
                </div>
              </Grid>
            </Grid>
          </Paper>
        )}
        <div
          id="data-tab-parts-misses"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            maxWidth: '99%',
            height: window.innerHeight - 260 + 'px',
            // height:(window.innerHeight-215)+'px',
            //  width: '1050px',
            //  width: '1110px',
            alignContent: 'center',

            marginLeft: '8px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            excelStyles={this.state.excelStyles}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            floatingFilter={true}
            suppressRowClickSelection={true}
            headerHeight={this.state.headerHeight}
            onFilterChanged={this.onFilterChanged}
            onColumnVisible={this.onColumnVisible}
            suppressDragLeaveHidesColumns={true}
            sideBar={this.state.sideBar}
            suppressContextMenu={true}
            onColumnMoved={this.onColumnMoved}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
          />
        </div>
        <EmailDialogKpi
          open={this.state.openDialogue}
          handlePopupClose={this.handleCloseEmail}
          mailUsers={this.state.mailUsers}
          // clientReportCardDetails={clientReportCardDetails}
          // image={image}
          // selectedWorkmixOptions={selectedWorkmixOptions}
          iKpiReportType={this.state.iKpiReportType}
          // measuredMTH={measuredMTH}
          // priorMTH={priorMTH}
          // selectedOptions={selectedOptions}
          selectedStoreIds={this.state.selectedStoreIds}
          reportName={this.state.reportName}
          reportNameCopy={this.state.reportNameCopy}
          // selectStoreDetails={selectStoreDetails}
          selectedStoreName={localStorage.getItem('storeSelected')}
          headerData={this.state.headerData}
          rowDataPdf={this.state.rowDataPdf}
          filterStart={this.state.filterStart}
          filterEnd={this.state.filterEnd}
          rowDataTotal={this.state.rowDataTotal}
          selectedServiceAdvisors={ReactSession.get('serviceAdvisors')}
          selectedTechnicians={ReactSession.get('technicians')}
        />
        <SaveReportDialog
          openSaveDlg={this.state.openSaveDlg}
          parent={this.state.parent}
          copyFile={this.state.copyFile}
          copy={this.state.copy}
          reportName={this.state.reportName}
          errorReport={this.state.errorReport}
          requiredText={this.state.requiredText}
          selectedType={this.state.selectedType}
          filterText={this.state.filterText}
          onChangeReportName={this.onChangeReportName}
          handleCancelSaveReport={this.handleCancelSaveReport}
          handleOkSaveReport={this.handleOkSaveReport}
          handleSaveAsReport={this.handleSaveAsReport}
          handleCheckboxChange={this.handleCheckboxChange}
          reportNameCopy={this.state.reportNameCopy}
        />
        <SuccessSnackbar
          onClose={this.handleSnackbarClose}
          open={this.state.openSusSnackbar}
          msg={'Report saved successfully!'}
          autoHideDuration={2000}
          //goalFail={this.state.goalFail}
        />
        <AlertSnackbar
          onClose={this.CancelAlertDilog}
          open={this.state.openAlert}
          msg={'No data available'}
        />
        {this.state.openDilog ? (
          <Dialog
            open={this.state.openDilog}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available for custom date range.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.cancelDilog} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
        <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openStoreDlg}
        >
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              This option is not available from this page.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCancelAllStores} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
        {this.state.openDilogAll ? (
          <Dialog
            open={this.state.openDilogAll}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available at all stores.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.CancelDilogAll} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
      </div>
    );
  }
}

const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },
  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    // marginTop: 9
    marginTop: 6
  },
  dataAsOf: {
    marginRight: 6,
    float: 'right',
    // marginTop: 5
    marginTop: 14
  },
  dataAsOfText: {
    marginLeft: 55
  },
  dataAsOfValue: {
    marginLeft: 3
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    '@media (max-width: 1440px)': {
      marginLeft: '0px !important'
    },
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  allJobs: {
    marginTop: 12,
    marginRight: 10
  },
  ButtonSelect: {
    lineHeight: 1.5,
    position: 'absolute',
    '@media (min-width: 2560px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 1920px)': {
      width: 183,
      marginTop: '-5px !important',
      marginBottom: '3px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '-5px !important',
      width: 154,
      marginBottom: '2x !important'
    }
  },
  reportButton: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    }
  },
  reportButtonSelect: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  resetBtn: {
    marginRight: 10,
    float: 'right',
    marginTop: 10
  }
});

export default withKeycloak(withStyles(styles)(PartsMisses));
