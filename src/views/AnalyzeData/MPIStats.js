import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import * as ExcelJS from 'exceljs';
import 'src/input-style.css';
import {
  Typography,
  FormControl,
  InputLabel,
  LinearProgress,
  Box,
  Paper,
  MenuItem,
  Select,
  Tooltip,
  FormControlLabel,
  RadioGroup,
  Radio,
  Link
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import RestoreIcon from '@material-ui/icons/Restore';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import SaveIcon from '@material-ui/icons/Save';
import MailIcon from '@material-ui/icons/Mail';

import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import {
  getKpiToggleOptionsWithTimeZone,
  getDataForOneLineRoDrilldown,
  getLatestClosedDate,
  getDataForMPIStatsDrilldown,
  getEmail,
  insertMpiStatsReportName
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import { getLast13Months, getTimeZone } from 'src/utils/Utils';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import { ReactSession } from 'react-client-session';
import { getYearValue } from 'src/utils/Utils';
import ExportIcon from '@material-ui/icons/GetApp';
import $ from 'jquery';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import 'bootstrap-daterangepicker/daterangepicker.css';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import EmailDialogKpi from 'src/components/EmailDialogKpi';
import SaveReportDialog from 'src/components/SaveReportDialog';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import AlertSnackbar from 'src/components/AlertSnackbar';
import { async } from 'validate.js';
var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

class DrilldownMPIStats extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ Technicians: ['All'] });
    //this.getKpiToggleOptions();
  }
  componentDidUpdate() {
    const newAdvisors = ReactSession.get('serviceAdvisors');
    const newTech = ReactSession.get('technicians');
    if (
      (ReactSession && ReactSession.get('serviceAdvisors') != undefined) ||
      (ReactSession && ReactSession.get('technicians') != undefined) ||
      this.state.filterChanged
    ) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisors,
        ReactSession.get('serviceAdvisors')
      );
      var checkStatusTech = lodash.isEqual(
        this.state.Technicians,
        ReactSession.get('technicians')
      );

      if (
        (checkStatus == false && $('#advisorSelection').hasClass('selected')) ||
        (checkStatusTech == false &&
          $('#techSelection').hasClass('selected')) ||
        this.state.filterChanged == true
      ) {
        // this.setState({ filterChanged: false });
        // this.setState({ advisors: ReactSession.get('serviceAdvisors') });
        // this.setState({ tech: ReactSession.get('technicians') });

        // this.getAgGridData(
        //   ReactSession.get('serviceAdvisors'),
        //   ReactSession.get('technicians'),
        //   this.state.filterStart,
        //   this.state.filterEnd
        // );
        this.setState(
          {
            filterChanged: false,
            advisors: newAdvisors,
            tech: newTech
          },
          () => {
            this.getAgGridData(
              newAdvisors,
              newTech,
              this.state.filterStart,
              this.state.filterEnd
            );
          }
        );
      }
    }

    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ showBackButton: false });
        if (this.state.store != undefined) {
          window.sortStateMpiDrilldown = {};
          window.filterStateMpiDrilldown = {};
          if (this.gridApi) {
            this.gridApi.setSortModel(null);
            this.gridApi.setFilterModel(null);
          }
          if (this.state.gridColumnApi) {
            const groupColumns = this.state.groupColumn;
            this.state.rawGridApi.setColumnDefs([]);
            this.state.rawGridApi.setColumnDefs(groupColumns);
            setTimeout(() => {
              this.state.gridColumnApi.resetColumnState();
            }, 50);
          }
        }

        if (this.state.store != undefined) {
          this.setState({ checked: false, parent: '' });
        }
        getLatestClosedDate(result => {
          if (result) {
            var openDate = '';
            var Date1 = result[0].value;
            localStorage.setItem('closedDate', Date1);
            this.setState({ closedDate: Date1 });
          }
        });
        // this.getAgGridData(
        //   ReactSession.get('serviceAdvisors'),
        //   ReactSession.get('technicians'),
        //   this.state.filterStart,
        //   this.state.filterEnd
        // );
        this.setState(
          {
            advisors: ['All'],
            tech: ['All']
          },
          () => {
            this.getAgGridData(
              ReactSession.get('serviceAdvisors'),
              ReactSession.get('technicians'),
              this.state.filterStart,
              this.state.filterEnd
            );
          }
        );
        this.setState({
          store: localStorage.getItem('selectedStoreId')
        });
      }
    }
  }
  constructor(props) {
    super(props);
    let startDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterStart
        ? this.props.history.location.state.filterStart
        : ReactSession.get('kpiToggleStartDate')
        ? ReactSession.get('kpiToggleStartDate')
        : localStorage.getItem('kpiStartDate')
        ? localStorage.getItem('kpiStartDate')
        : '';

    let endDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterEnd
        ? this.props.history.location.state.filterEnd
        : ReactSession.get('kpiToggleEndDate')
        ? ReactSession.get('kpiToggleEndDate')
        : localStorage.getItem('kpiFilterEndDate')
        ? localStorage.getItem('kpiFilterEndDate')
        : '';
    let oneLineType =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.oneLineType
        ? this.props.history.location.state.oneLineType
        : this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : 'Under 60K';
    let closedDate = localStorage.getItem('closedDate');
    // let filterText = ReactSession.get('kpiHomeToggle');
    let created_by =
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.created_by
        ? props.history.location.state.created_by
        : '';
    let filterText =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterText
        ? this.props.history.location.state.filterText
        : ReactSession.get('kpiHomeToggle')
        ? ReactSession.get('kpiHomeToggle')
        : localStorage.getItem('kpiHomeToggle');
    let filterColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterColumns
        ? this.props.history.location.state.filterColumns
        : {};
    let sortColumn =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.sortColumns
        ? this.props.history.location.state.sortColumns
        : {};
    let advisor =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedAdvisor
        ? this.props.history.location.state.selectedAdvisor
        : ReactSession.get('serviceAdvisors')
        ? ReactSession.get('serviceAdvisors')
        : null;
    let technicians =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedTech
        ? this.props.history.location.state.selectedTech
        : ReactSession.get('technicians')
        ? ReactSession.get('technicians')
        : null;
    let checkedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.checkedColumns
        ? this.props.history.location.state.checkedColumns
        : 'All';
    let draggedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.draggedColumn
        ? this.props.history.location.state.draggedColumn
        : [];
    let parent =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.parent
        ? this.props.history.location.state.parent
        : '';
    let visibility =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.visibility
        ? this.props.history.location.state.visibility
        : 'private';
    let reportName =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.report_name
        ? this.props.history.location.state.report_name
        : '';
    this.state = {
      sortModel: [],
      filterModelValue: '',
      selectedStoreIds: JSON.parse(localStorage.getItem('selectedStoreId')),
      headerData: [],
      rowDataPdf: [],
      field: [],
      iKpiReportType: 'MPI_Stats',
      mailUsers: [],
      openDialogue: false,
      createdBy: created_by,
      openStoreDlg: false,
      isExportDisabled: false,
      isExcelExportDisabled: false,
      store: localStorage.getItem('selectedStoreId')
        ? localStorage.getItem('selectedStoreId')
        : undefined,
      showBackButton:
        this.props.history &&
        this.props.history.location.state &&
        this.props.history.location.state.parent == 'Home'
          ? true
          : this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.parent == 'savedReports'
          ? true
          : false,
      showSummaryValues: true,
      isLoading: true,
      monthYear: {},
      dates: [],
      lastWeek: '',
      thisWeek: '',
      lastTwoweek: '',
      yesterDay: '',
      today: '',
      dayBfYest: '',
      mtd: '',
      setMtd: '',
      lastMonth: '',
      lastThreeMonths: '',
      lastQtr: '',
      ytd: '',
      lastTwelveMonths: '',
      lastYear: '',
      filterStart: startDate,
      filterEnd: endDate,
      filterChanged: false,
      oneLineType: oneLineType,
      closedDate: closedDate,
      milageBelow60K: 0,
      milageAbove60K: 0,
      lbrSale: 0,
      roCount: 0,
      lbrSoldHrs: 0,
      soldHours: 0,
      filterText: filterText,
      avgMileage: 0,
      prtsSale: 0,
      mpiCount: 0,
      ttlSales: 0,
      openSaveDlg: false,
      reportName: reportName,
      errorReport: '',
      selectedType: visibility,
      openSusSnackbar: false,
      requiredText: false,
      displayCol: checkedColumns,
      draggedColumn: draggedColumns,
      advisors: advisor,
      tech: technicians,
      parent: parent,
      filterCol: filterColumns,
      sortCol: sortColumn,
      copy: false,
      copyFile: false,
      reportNameCopy: '',
      openDilog: false,
      openDilogAll: false,
      openAlert: false,
      zeroFilter: false,
      columnDefs: [
        {
          headerName: 'Closed Date',
          field: 'Closed Date',
          // width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'RO',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          // filter: 'agNumberColumnFilter',
          field: 'RO',

          // width: realm != 'haleyag' ? 83 : 110,
          minWidth: 85,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          //   hide: drillDown == 33 ? true : false,
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Advisor',
          field: 'Advisor',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          tooltipField: 'Advisor',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          suppressMenu: true
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // }
        },
        {
          headerName: 'Tech',
          //filter: 'agSetColumnFilter',
          filter: false,
          field: 'Technician',
          minWidth: 150,
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          flex: 1,
          sortable: true,
          comparator: this.customComparator,
          chartDataType: 'category',
          tooltipField: 'Technician',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Customer',
          field: 'Customer',
          dataType: 'string',
          tooltipField: 'Customer',
          minWidth: 165,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'MPI Done',
          field: 'MPI Done',
          cellStyle: { textAlign: 'center' },
          flex: 1,
          // dataType: 'string',
          //tooltipField: 'MPI Done',
          minWidth: 60,
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'textAlign',
          hartDataType: 'category'
        },
        {
          headerName: 'MPI Opcode',
          field: 'MPI Opcode',
          minWidth: 80,
          flex: 1,
          tooltipField: 'MPI Opcode',
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          }
        },
        {
          headerName: 'Mileage',
          field: 'Mileage',
          minWidth: 88,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueMilage,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMilage,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'commaSeparatedNumber'
        },
        {
          headerName: 'MPI Sold Hours',
          field: 'MPI Sold Hours',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          aggFunc: 'sum',
          valueFormatter: this.formatCellValueWithOut$,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueWithOut$,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'MPI Labor Sale',
          field: 'MPI Labor Sale',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,

          filterParams: {
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            },
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'MPI Parts Sale',
          field: 'MPI Parts Sale',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'MPI Labor GP %',
          field: 'MPI Labor GP %',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueGP,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'MPI Parts GP %',
          field: 'MPI Parts GP %',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueGP,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'MPI Sales',
          field: 'MPI Total Sales',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      popupParent: document.body,
      headerHeight: 48,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true,
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              suppressColumnExpandAll: false
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        suppressMovable: false,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'doorRate$',
          numberFormat: { format: '$#,##0' },
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'variance%',
          //  numberFormat: { format: '#0.00%' },
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'blueFont',
          font: {
            color: '0000FF'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          dataType: 'DateTime',
          numberFormat: {
            format: 'mm/dd/yy'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        },
        {
          id: 'commaSeparatedNumber',
          numberFormat: {
            format: '#,##0' // Comma formatting without decimal places
          },
          alignment: {
            horizontal: 'Right'
          }
        }
      ]
    };
  }

  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };

  formatCellValuePercent = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.00%';
    }
  };

  formatCellValueRate = params => {
    if (params.value != null && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };

  exportToPDF = () => {
    const sheetName =
      this.state.parent == 'savedReports'
        ? `MPI Stats - Saved Reports - ${this.props.history.location?.state?.report_name}`
        : 'MPI Stats';
    if (
      this.state.displayCol.length == 0 ||
      this.state.rowData == null ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }
      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };

      const doc = new jsPDF('l', 'mm', columns.length <= 30 ? 'a3' : 'a2');
      const marginLeft = 5;
      const marginTop = 10;

      doc.setFontSize(16);
      doc.text(sheetName, marginLeft, marginTop);
      const summaryLabels = [
        'RO Count:',
        'MPI Count:',
        'MPI Hours Sold:',
        'MPI Labor Sale:',
        'MPI Parts Sale:',
        'Avg Mileage:',
        'MPI Total Sales:'
      ];
      const summaryValues = [
        String(this.state.roCount),
        String(this.state.mpiCount),
        String(this.state.lbrSoldHrs),
        String(this.state.lbrSale),
        String(this.state.prtsSale),
        String(this.state.avgMileage),
        String(this.state.ttlSales)
      ];

      // doc.setFontSize(11);
      // let xPosition = marginLeft;
      // const yPosition = 20;
      doc.setFontSize(12);
      doc.setFont(undefined, 'bold');
      doc.setTextColor('black');
      doc.text('Summary', marginLeft, 18);

      // Continue with summary line
      doc.setFontSize(11);
      let xPosition = marginLeft;
      const yPosition = 22;

      summaryLabels.forEach((label, index) => {
        doc.setFont(undefined, 'normal');
        doc.setTextColor('black');
        doc.text(label, xPosition, yPosition);
        xPosition += doc.getTextWidth(label) + 1;

        doc.setFont(undefined, 'bold');
        let valueNormal = String(summaryValues[index]).replace(/[$,]/g, '');
        let value = summaryValues[index];
        doc.text(String(value), xPosition, yPosition);
        xPosition += doc.getTextWidth(String(value)) + 8;
      });

      doc.setFont(undefined, 'normal');

      const headers = columns.map(col => col.header);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];

          if (col.field === 'Mileage') {
            return this.formatCellValueMilage({ value });
          } else if (col.field === 'MPI Sold Hours') {
            return this.formatCellValueWithOut$({ value });
          } else if (
            ['MPI Labor Sale', 'MPI Parts Sale', 'MPI Total Sales'].includes(
              col.field
            )
          ) {
            return this.formatCellValue({ value });
          } else if (['MPI Parts GP %', 'MPI Labor GP %'].includes(col.field)) {
            return this.formatCellValueGP({ value });
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }
        })
      );

      autoTable(doc, {
        startY: 24,
        head: [headers],
        body: data,
        didParseCell: function(data) {
          if (
            columns[data.column.index]?.field === 'variance' ||
            columns[data.column.index]?.field === 'variancePerc'
          ) {
            if (data.cell.raw && data.cell.raw.color) {
              data.cell.styles.textColor = data.cell.raw.color;
            }
          }
        },
        theme: 'grid',
        styles: {
          fontSize: 9,
          cellPadding: 1.5,
          halign: 'left'
        },
        headStyles: {
          fillColor: '#c65911',
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 10,
          halign: 'left'
        },
        tableWidth: 'auto',
        margin: { top: 3, left: 3, right: 3 },
        horizontalPageBreak: false,
        scaleFactor: 1.2
      });

      doc.save(sheetName + '.pdf');
    }
  };

  onBtExport = async () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.rowData == null ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      const sheetTitle =
        this.state.parent === 'savedReports'
          ? `MPI Stats - Saved Reports - ${this.props.history.location?.state?.report_name}`
          : 'MPI Stats';

      const workbook = new ExcelJS.Workbook();
      const sheet = workbook.addWorksheet(sheetTitle);
      const gridApi = this.gridApi;
      const columnApi = this.gridColumnApi;

      const filteredAndSortedRows = gridApi
        .getModel()
        .rowsToDisplay.map(rowNode => rowNode.data);

      const summaryData = [
        ['Ro Count:', this.state.roCount],
        ['MPI Count:', this.state.mpiCount],
        ['MPI Hours Sold:', this.state.lbrSoldHrs],
        ['MPI Labor Sale:', this.state.lbrSale],
        ['MPI Parts Sale:', this.state.prtsSale],
        ['Avg Mileage:', this.state.avgMileage],
        ['MPI Total Sales:', this.state.ttlSales]
      ];

      const startRow = 3;
      summaryData.forEach(([label, value], index) => {
        const rowIndex = index < 4 ? startRow : startRow + 1;
        const position = index < 4 ? index : index - 4;

        const labelCell = sheet.getRow(rowIndex).getCell(1 + position * 2);
        const valueCell = sheet.getRow(rowIndex).getCell(2 + position * 2);

        labelCell.value = label;
        labelCell.alignment = { horizontal: 'right', vertical: 'middle' };
        labelCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'f8dbc9' }
        };
        labelCell.font = { bold: true };

        valueCell.value = value;
        valueCell.alignment = { horizontal: 'left', vertical: 'middle' };
        valueCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'f8dbc9' }
        };

        sheet.getRow(rowIndex).commit();
      });

      const visibleColumns = columnApi.getAllDisplayedColumns();
      const columnMapping = visibleColumns.reduce((map, col) => {
        map[col.getColDef().headerName] = col.getColDef().field;
        return map;
      }, {});
      const dynamicHeaders = Object.keys(columnMapping);
      const numberFormatter = new Intl.NumberFormat();

      sheet.mergeCells('A1:' + sheet.getCell(1, dynamicHeaders.length).address);
      sheet.getCell('A1').value = sheetTitle;
      sheet.getCell('A1').alignment = {
        horizontal: 'left',
        vertical: 'middle'
      };
      sheet.getCell('A1').font = { bold: true, size: 14 };

      const columnLengths = Array(dynamicHeaders.length).fill(0);

      dynamicHeaders.forEach((header, index) => {
        const cell = sheet.getCell(6, index + 1);
        cell.value = header;
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'c65911' }
        };
        columnLengths[index] = header.length;
      });

      sheet.getCell(`A2`).value = 'Summary';
      sheet.getCell(`A2`).font = { bold: true, size: 13 };
      sheet.getCell(`A2`).alignment = {
        horizontal: 'left',
        vertical: 'middle'
      };
      sheet.mergeCells(`A2:${sheet.getCell(2, dynamicHeaders.length).address}`);

      filteredAndSortedRows.forEach((row, rowIndex) => {
        const rowNumber = rowIndex + 4;
        const values = dynamicHeaders.map((header, colIndex) => {
          const field = columnMapping[header];
          let value = row[field];

          let formatted = value;
          if (field === 'Closed Date') {
            const date = new Date(value);
            formatted = `${(date.getMonth() + 1)
              .toString()
              .padStart(2, '0')}/${date
              .getDate()
              .toString()
              .padStart(2, '0')}/${date
              .getFullYear()
              .toString()
              .slice(-2)}`;
          } else if (
            ['MPI Labor Sale', 'MPI Parts Sale', 'MPI Total Sales'].includes(
              field
            )
          ) {
            formatted = value;
            // formatted =
            //   value < 0
            //     ? `-$${Math.abs(value).toLocaleString(undefined, {
            //         minimumFractionDigits: 2,
            //         maximumFractionDigits: 2
            //       })}`
            //     : `$${value.toLocaleString(undefined, {
            //         minimumFractionDigits: 2,
            //         maximumFractionDigits: 2
            //       })}`;
          } else if (['MPI Parts GP %', 'MPI Labor GP %'].includes(field)) {
            // formatted = `${value.toFixed(1)}%`;
            formatted = value / 100; // Convert percentage string (25.0) to decimal (0.25)
          } else if (field === 'MPI Sold Hours') {
            formatted = value; // Keep as number
            // formatted = value.toLocaleString(undefined, {
            //   minimumFractionDigits: 1,
            //   maximumFractionDigits: 1
            // });
          } else if (typeof value === 'number') {
            // formatted = numberFormatter.format(value);
            formatted = value;
          }

          if (
            formatted &&
            formatted.toString().length > columnLengths[colIndex]
          ) {
            columnLengths[colIndex] = formatted.toString().length;
          }

          return formatted;
        });

        const excelRow = sheet.addRow(values);

        dynamicHeaders.forEach((header, colIndex) => {
          const cell = excelRow.getCell(colIndex + 1);
          const field = columnMapping[header];

          if (field === 'RO') {
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          } else if (['MPI Sold Hours', 'Mileage'].includes(field)) {
            cell.numFmt = '#,##0'; // comma-separated format in Excel
            cell.alignment = { horizontal: 'right', vertical: 'middle' };
          }
          if (['MPI Parts GP %', 'MPI Labor GP %'].includes(field)) {
            cell.numFmt = '0.0%'; // One decimal place percent format
          }
          if (
            ['MPI Labor Sale', 'MPI Parts Sale', 'MPI Total Sales'].includes(
              field
            )
          ) {
            cell.numFmt = '"$"#,##0.00'; // Dollar sign with two decimal places
          }
          if (field === 'MPI Sold Hours') {
            cell.numFmt = '0.0'; // One decimal place
          }
          if ((rowIndex + 1) % 2 === 1) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'f8dbc9' }
            };
          }
        });
      });

      // Set final column widths based on content length
      columnLengths.forEach((len, i) => {
        sheet.getColumn(i + 1).width = len + 2; // +2 for padding
      });

      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download =
        this.state.parent == 'savedReports'
          ? `MPI Stats - Saved Reports - ${this.props.history.location?.state?.report_name}.xlsx`
          : 'MPI Stats.xlsx';
      // a.download = `${sheetTitle}.xlsx`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  componentDidMount() {
    if (this.state.selectedFilter == '')
      this.setState({ selectedFilter: 'duration' });
    if (this.state.selectedToggle == '')
      this.setState({ selectedToggle: 'MTD' });
    this.getMonthYearForSelect();
    this.getKpiToggleOptions();
    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousToggle &&
      this.props.history.location.state.previousToggle != 'undefined'
    ) {
      this.setState({
        previousToggle: this.props.history.location.state.previousToggle
      });
    }
  }

  getKpiToggleOptions = () => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        this.setState({
          dates: dataArr,
          thisWeek:
            moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY"),

          lastWeek:
            moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY"),
          lastTwoWeek:
            moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY"),
          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          mtd:
            moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY"),
          lastMonth: moment(dataArr[0].lastmonthstartdate).format('MMM'),
          lastThreeMonths:
            moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM'),
          lastQtr:
            moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM'),

          ytd:
            moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY"),
          lastTwelveMonths:
            moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY"),
          lastYear:
            moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        });
        if (this.state.filterStart == '' && this.state.filterEnd == '') {
          this.setState({
            filterStart: moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
            filterEnd: moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'),
            toggleOption: 'MTD'
          });

          if (localStorage.getItem('kpiDataStatus') == 1) {
            this.setState({
              filterStart: moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
              filterEnd: moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'),
              toggleOption: 'MTD'
            });
          } else {
            this.setState({
              filterStart: moment(dataArr[0].lastmonthstartdate).format(
                'YYYY-MM-DD'
              ),
              filterEnd: moment(dataArr[0].lastmonthenddate).format(
                'YYYY-MM-DD'
              ),
              toggleOption: 'LMONTH'
            });
          }
        }
        this.setState({ columnDefs: this.state.columnDefs });

        //this.gridApi.showLoadingOverlay();
        let selectedAdv =
          this.props?.history?.location?.state?.selectedAdvisor ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('serviceAdvisors'));
        //  ReactSession.get('serviceAdvisors');

        let selectedTech =
          this.props?.history?.location?.state?.selectedTech ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('technicians'));
        this.setState({ advisors: selectedAdv });
        this.setState({ tech: selectedTech });
        this.getAgGridData(
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians')
          // this.state.advisors,
          // this.state.tech
        );
      }
    });
  };

  getMonthYearForSelect = () => {
    this.setState({
      monthYear: getLast13Months()
    });
    if (
      (this.state.monthYear && this.state.selectedMonthYear == '') ||
      this.state.selectedMonthYear == undefined ||
      !this.state.selectedMonthYear
    )
      this.setState({
        selectedMonthYear: this.state.monthYear[this.state.monthYear.length - 1]
      });
  };

  handleclick = params => {
    this.props.history.push({
      pathname: '/Home',
      state: {
        toggleOptions: this.state.previousToggle
          ? this.state.previousToggle
          : 'MTD',
        payType: this.state.PrevPayType,
        gridType: this.state.PrevGridType,
        parent: this.state.parent,
        filterStart: this.state.filterStart,
        filterEnd: this.state.filterEnd
      }
    });
  };

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };

  customComparator = (valueA, valueB) => {
    // Handling null values
    if (valueA === null && valueB === null) {
      return 0;
    }
    if (valueA === null) {
      return -1;
    }
    if (valueB === null) {
      return 1;
    }

    const nameA = valueA.split('[')[0].trim();
    const nameB = valueB.split('[')[0].trim();

    return nameA.localeCompare(nameB);
  };

  isExternalFilterPresent = () => {
    return (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.parent === 'Home'
    );
  };

  doesExternalFilterPass = node => {
    return node.data['MPI Done'] === 'Y';
  };

  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };

  formatCellValueMilage = params => {
    if (params.value != null && params.value != 0) {
      return params.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0' + '%';
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };

  // onFirstDataRendered = params => {
  //   const rowCount = params.api.getDisplayedRowCount();
  //   if (rowCount == 0) {
  //     this.setState({
  //       showSummaryValues: false,
  //       lbrSale: 0,
  //       lbrSoldHrs: 0,
  //       prtsSale: 0,
  //       ttlSales: 0,
  //       avgMileage: 0,
  //       roCount: 0,
  //       mpiCount: 0
  //     });
  //   } else {
  //     this.setState({ showSummaryValues: true });
  //   }
  // };

  // onFirstDataRendered = (params) => {
  //   if (!this.state.isDataRendered) {  // Prevent overwriting
  //     this.gridApi = params.api;
  //     this.setState({ isDataRendered: true }); // Track initialization
  //     this.getTotalsForDisplay(this.state.filteredRowData || this.state.rowData);
  //   }
  // };

  onModelUpdated = params => {
    const rowCount = params.api.getDisplayedRowCount();
    const visibleColumns = params.columnApi.getAllDisplayedColumns();

    if (visibleColumns.length === 0) {
      this.setState({
        showSummaryValues: false,
        lbrSale: 0,
        lbrSoldHrs: 0,
        prtsSale: 0,
        ttlSales: 0,
        avgMileage: 0,
        roCount: 0,
        mpiCount: 0
      });
    } else {
      this.setState({ showSummaryValues: true });
    }
  };

  onColumnVisible = params => {
    const columnApi = params.columnApi || this.gridColumnApi;
    const gridApi = this.gridApi;

    if (!columnApi || !gridApi) {
      console.error('columnApi or gridApi is undefined');
      return;
    }

    const displayedPivotColumns = columnApi
      .getAllColumns()
      .filter(col => col.getColDef().pivot && columnApi.isColumnDisplayed(col));

    const visibleColumns = columnApi.getAllDisplayedColumns();
    const rowCount = gridApi.getDisplayedRowCount();
    const colIds = visibleColumns.map(col => col.colId);
    this.setState({ displayCol: colIds });
    const rowData = [];
    gridApi.forEachNodeAfterFilter(node => {
      if (node.data) rowData.push(node.data);
    });

    if (
      visibleColumns.length === 0 ||
      rowCount === 0 ||
      columnApi.getAllColumns().every(col => col.getColDef().hide)
    ) {
      this.setState({
        lbrSale: 0,
        lbrSoldHrs: 0,
        prtsSale: 0,
        ttlSales: 0,
        avgMileage: 0,
        roCount: 0,
        mpiCount: 0,
        showSummaryValues: false,
        isExcelExportDisabled: true
      });
    } else {
      this.setState({ showSummaryValues: true });
      this.setState({ isExcelExportDisabled: false });

      if (rowData.length > 0) {
        //this.getAgGridData();
        this.getTotalsForDisplay(rowData);
      }
    }
  };
  onColumnMoved = params => {
    const columnOrder = params.columnApi
      .getAllGridColumns()
      .map(col => col.getColId());
    this.setState({ draggedColumn: columnOrder });
  };
  onGridReady = params => {
    const transformedFilterState = Object.fromEntries(
      Object.entries(this.state.filterCol).map(([key, values]) => [
        key,
        { values, filterType: 'set' }
      ])
    );
    const transformedSortState = [
      {
        colId: this.state.sortCol[0],
        sort: this.state.sortCol[1]
      }
    ];
    params.api.closeToolPanel();
    //  this.getKpiToggleOptions();
    //params.api.openToolPanel("columns");
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.sizeColumnsToFit();
    this.setState({ gridApi: params.api });
    this.setState({ rawGridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });

    this.setState({
      groupColumn: params.api.columnController.columnDefs
    });

    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null ||
      this.props.history.location.state.ronumber == undefined
    ) {
      window.sortStateMpiDrilldown = {};
      window.filterStateMpiDrilldown = {};
    }
    if (
      this.gridColumnApi &&
      window.colStateMpiDrilldown &&
      (this.props.history == undefined ||
        (this.props.history &&
          this.props.history.location &&
          this.props.history.location.state == undefined) ||
        this.props.history.location.state.ronumber == undefined)
    ) {
      window.colStateMpiDrilldown = null;
      this.state.gridColumnApi.resetColumnState();
    }

    if (window.colStateMpiDrilldown && this.gridColumnApi) {
      this.gridColumnApi.setColumnState(window.colStateMpiDrilldown);
    }
    this.gridApi.setSortModel(window.sortStateMpiDrilldown);
    this.gridApi.setFilterModel(window.filterStateMpiDrilldown);
    // this.getAgGridData(
    //   ReactSession.get('serviceAdvisors'),
    //   ReactSession.get('technicians')
    // );

    this.gridApi.setSortModel(window.sortStateMpiDrilldown);
    this.gridApi.setFilterModel(window.filterStateMpiDrilldown);

    if (this.state.parent == 'savedReports') {
      window.filterStateMpiDrilldown = transformedFilterState;
      window.sortStateMpiDrilldown = transformedSortState;

      if (this.state.draggedColumn.length > 0) {
        window.colStateMpiDrilldown = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();

        // Reorder columns based on desiredOrder
        const orderedState = this.state.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col ? { ...col, order: index } : null;
          })
          .filter(Boolean);
        // Apply reordered column state
        this.gridColumnApi.setColumnState(orderedState);
      }

      if (this.state.displayCol.length > 0) {
        if (this.state.displayCol.includes('All')) {
        } else {
          window.colStateMpiDrilldown = this.gridColumnApi.getColumnState();
          const visibleColumns = this.state.columnDefs.filter(column =>
            this.state.displayCol.includes(column.field)
          );
          const visibleFields = visibleColumns.map(col => col.field);
          const updatedColState = window.colStateMpiDrilldown.map(col => ({
            ...col,
            hide: !visibleFields.includes(col.colId) // hide true for matched columns
          }));
          this.gridColumnApi.setColumnState(updatedColState);
        }
      }
      // window.colStateMpiDrilldown = this.gridColumnApi.getColumnState();
      // const visibleColumns = this.state.columnDefs.filter(column =>
      //   this.state.displayCol.includes(column.field)
      // );
      // const visibleFields = visibleColumns.map(col => col.field);
      // const updatedColState = window.colStateMpiDrilldown.map(col => ({
      //   ...col,
      //   hide: !visibleFields.includes(col.colId) // hide true for matched columns
      // }));
      // this.gridColumnApi.setColumnState(updatedColState);

      this.gridApi.setFilterModel(window.filterStateMpiDrilldown);
      this.gridApi.setSortModel(window.sortStateMpiDrilldown);
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(1)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.0';
    }
  };

  getAgGridData(advisor, tech) {
    let resultArr = [];
    this.setState({ isLoading: true });
    advisor = advisor ? advisor : ['All'];
    this.setState({ serviceAdvisors: advisor });
    this.setState({ Technicians: tech });
    let laborSale = 0;
    let laborSoldHrs = 0;

    getDataForMPIStatsDrilldown(
      this.state.filterStart,
      this.state.filterEnd,
      this.state.tech
        ? // && !this.state.tech.includes('All')
          this.state.tech
        : tech,
      this.state.advisors
        ? //&& !this.state.advisors.includes('All')
          this.state.advisors
        : advisor,
      result => {
        if (
          result.data
            .statelessDbdKpiScorecardGetKpiScorecardMpiStatsDrillDownReport
            .mpiStatsDrillDownReports
        ) {
          let data1 =
            result.data
              .statelessDbdKpiScorecardGetKpiScorecardMpiStatsDrillDownReport
              .mpiStatsDrillDownReports;
          // this.setState({ isLoading: false });

          //this.getTotalsForDisplay(data1)
          if (
            result.data
              .statelessDbdKpiScorecardGetKpiScorecardMpiStatsDrillDownReport
              .mpiStatsDrillDownReports[0] == null
          ) {
            this.setState({
              rowData: [],
              lbrSale: 0,
              lbrSoldHrs: 0
            });
            this.setState({ filterChanged: false });
          } else {
            resultArr =
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardMpiStatsDrillDownReport
                .mpiStatsDrillDownReports[0].jsonData;
            const rowData = JSON.parse(resultArr)[0].result_data;
            this.setState({ rowData }, () => {
              this.getTotalsForDisplay(rowData); // Calculate initial page summaries
            });

            // this.getTotalsForDisplay(JSON.parse(resultArr)[0].result_data);
            this.setState({ filterChanged: false });
            if (
              (this.props.history?.location?.state?.filterColumns &&
                Object.keys(this.props.history.location.state.filterColumns)
                  .length > 0) ||
              (this.state.parent != 'savedReports' &&
                (this.props.history.location.state == undefined ||
                  this.props.history.location.state.pageType == undefined ||
                  (this.props.history.location.state.pageType &&
                    this.props.history.location.state.pageType == 'MPIStats')))
            ) {
              this.filterByMPI();
            }
            if (window.filterStateMpiDrilldown != undefined) {
              this.filterByValue();
            }
            if (window.filterStateMpiDrilldown != undefined) {
              this.filterByValue();
            }
          }
        } else {
          this.setState({
            rowData: [],
            // milageBelow60K: 0,
            // milageAbove60K: 0,
            lbrSale: 0,
            lbrSoldHrs: 0
          });
          this.setState({ filterChanged: false });
        }
      }
    );
  }

  resetRawData = () => {
    if (this.state.parent == 'savedReports') {
      const propsData = this.props.history && this.props.history.location.state;
      console.log('propsData>>>>>>>>', propsData, this.state.displayCol);
      this.setState({
        isLoading: true
      });
      const transformedFilterState = Object.fromEntries(
        Object.entries(propsData.filterColumns).map(([key, values]) => [
          key,
          { values, filterType: 'set' }
        ])
      );
      const transformedSortState = [
        {
          colId: propsData.sortColumns[0],
          sort: propsData.sortColumns[1]
        }
      ];
      window.filterStateMpiDrilldown = transformedFilterState;
      window.sortStateMpiDrilldown = transformedSortState;
      this.gridApi.setFilterModel(window.filterStateMpiDrilldown);
      this.gridApi.setSortModel(window.sortStateMpiDrilldown);
      if (propsData.draggedColumn.length > 0) {
        window.colStateMpiDrilldown = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();
        const orderedState = propsData.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col ? { ...col, order: index } : null;
          })
          .filter(Boolean);
        window.colStateMpiDrilldown = orderedState;
        this.gridColumnApi.setColumnState(window.colStateMpiDrilldown);
      }

      if (
        propsData.checkedColumns.length > 0 &&
        propsData.checkedColumns[0] != 'All'
      ) {
        window.colStateMpiDrilldown = this.gridColumnApi.getColumnState();
        const visibleColumns = this.state.columnDefs.filter(column =>
          propsData.checkedColumns.includes(column.field)
        );
        const visibleFields = visibleColumns.map(col => col.field);
        const updatedColState = window.colStateMpiDrilldown.map(col => ({
          ...col,
          hide: !visibleFields.includes(col.colId)
        }));
        window.colStateMpiDrilldown = updatedColState;
        this.gridColumnApi.setColumnState(window.colStateMpiDrilldown);
      }

      this.setState({ tech: propsData.selectedTech });
      this.setState({ advisors: propsData.selectedAdvisor });
      this.setState({ selectedType: propsData.visibility });
      this.setState({ filterText: propsData.filterText });
      this.setState({ zeroFilter: false });
      setTimeout(() => {
        this.setState({ filterStart: propsData.filterStart });
        this.setState({ filterEnd: propsData.filterEnd });
        this.setState({
          isLoading: false
        });
      }, 500);
    } else {
      window.sortStateMpiDrilldown = {};
      window.filterStateMpiDrilldown = {};
      this.state.gridColumnApi && this.state.gridColumnApi.resetColumnState();
      this.setState({ zeroFilter: false });
      if (this.gridApi) {
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
      }
      this.filterByMPI();
    }
  };
  // filterByMPI = () => {
  //   var mpiFilterComponent = this.gridApi.getFilterInstance('MPI Done');
  //   mpiFilterComponent.setModel({ values: ['Y'] });
  //   this.gridApi.onFilterChanged();
  // };
  // filterByMPI = () => {
  //   var mpiFilterComponent = this.gridApi.getFilterInstance('MPI Done');
  //   mpiFilterComponent.setModel({ values: ['Y'] });
  //   this.gridApi.onFilterChanged();
  // };
  filterByMPI = () => {
    // Set a flag to indicate programmatic filtering
    this.isMPIProgrammatic = true;

    // Get the filter instance for "MPI Done"
    var mpiFilterComponent = this.gridApi.getFilterInstance('MPI Done');

    // Apply the filter

    // Apply the filter
    mpiFilterComponent.setModel({ values: ['Y'] });

    // Trigger filter change event

    // Trigger filter change event
    this.gridApi.onFilterChanged();

    // Reset the flag after a short delay to avoid conflicts
    setTimeout(() => {
      this.isMPIProgrammatic = false;
    }, 0);
  };
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateMpiDrilldown);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  // onFilterChanged = e => {
  //   const filterValues = e.api.getFilterModel();
  //   let rowData = [];
  //   this.gridApi.forEachNodeAfterFilter(node => {
  //     rowData.push(node.data);
  //   });
  //   console.log('onfilterchange', rowData);
  //   this.getTotalsForDisplay(rowData);
  // };
  convertFilterModel = filterModelValue => {
    return Object.entries(filterModelValue).reduce((acc, [key, val]) => {
      acc[key] = val.values;
      return acc;
    }, {});
  };
  onFilterChanged = e => {
    // Get the current filter model from the grid
    const filterValues = e.api.getFilterModel();

    if (Object.keys(filterValues).length > 0) {
      Object.keys(filterValues).forEach(field => {
        const filter = filterValues[field];
        if (filter?.filterType === 'set') {
          const selectedValues = filter.values || [];
          if (selectedValues.length === 0) {
            this.setState({ zeroFilter: true });
          } else {
            this.setState({ zeroFilter: false });
          }
        }
      });
    } else {
      this.setState({ zeroFilter: false });
    }

    // Extract the filtered rows after the filter is applied
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });

    // Update state with filter model and filtered rows
    const filterVal = this.convertFilterModel(filterValues);
    this.setState({
      appliedFilters: filterVal, // Store filter model in state
      filteredRowData: rowData // Store filtered rows for additional use
    });

    // Trigger summary calculation with the filtered rows
    this.getTotalsForDisplay(rowData);
  };

  getTotalsForDisplay = data => {
    const filteredRowData = Array.isArray(data)
      ? data
      : Array.isArray(this.state.rowData)
      ? this.state.rowData
      : [];

    const unfilteredRowData = Array.isArray(this.state.rowData)
      ? this.state.rowData
      : [];

    const filterModel = this.gridApi.getFilterModel();

    // Remove "MPI Done" filter from affecting RO Count & Avg Mileage calculations
    const filterModelWithoutMPI = { ...filterModel };
    delete filterModelWithoutMPI['MPI Done'];

    // Filtered data for everything except "MPI Done" (for RO Count & Avg Mileage)
    const filteredWithoutMPI = unfilteredRowData.filter(item => {
      return Object.keys(filterModelWithoutMPI).every(column => {
        const filter = filterModelWithoutMPI[column];
        if (!filter) return true;
        const value = item[column];

        if (filter.filterType === 'set') {
          return filter.values.includes(value != null && value.toString());
        }

        // if (filter.filterType === "number") {

        //   const numValue = Number(value);
        //   if (filter.type === "greaterThan") return numValue > filter.filter;
        //   if (filter.type === "lessThan") return numValue < filter.filter;
        //   if (filter.type === "equals") return numValue === filter.filter;
        //   return true;
        // }

        // if (filter.filterType === "number") {

        //   const numValue = Number(value);
        //   if (filter.type === "greaterThan") return numValue > filter.filter;
        //   if (filter.type === "lessThan") return numValue < filter.filter;
        //   if (filter.type === "equals") return numValue === filter.filter;
        //   return true;
        // }
        if (filter.filterType === 'number') {
          const numValue = Number(value);
          const filterNum = Number(filter.filter);
          if (isNaN(numValue) || isNaN(filterNum)) return false;

          if (filter.type === 'greaterThan') return numValue > filterNum;
          if (filter.type === 'lessThan') return numValue < filterNum;
          if (filter.type === 'equals') return numValue === filterNum;
        }

        if (filter.filterType === 'text') {
          const textValue = String(value).toLowerCase();
          const filterText = filter.filter.toLowerCase();
          if (filter.type === 'contains') return textValue.includes(filterText);
          if (filter.type === 'startsWith')
            return textValue.startsWith(filterText);
          if (filter.type === 'endsWith') return textValue.endsWith(filterText);
        }

        return true;
      });
    });

    let laborSoldHrs = 0;
    let laborSale = 0;
    let partsSale = 0;
    let totalSales = 0;
    let mileageTotal = 0;
    let mileageCount = 0;
    let roCount = 0;
    let mpiCount = 0;
    let avgMileage = 0;

    const closedDateMap = new Map();

    // Use full filteredRowData for Sold Hours, Labor Sale, and MPI Counts
    if (filteredRowData.length > 0) {
      filteredRowData.forEach(item => {
        laborSoldHrs += Number(item['MPI Sold Hours']); // Sold Hours should update
        laborSale += Number(item['MPI Labor Sale']);
        partsSale += Number(item['MPI Parts Sale']);
        totalSales += Number(item['MPI Total Sales']);
        if (item['MPI Done'] === 'Y') {
          mpiCount++;
        }
      });
    }

    // Use filteredWithoutMPI (excluding MPI Done filter) for RO Count & Avg Mileage
    if (filteredWithoutMPI.length > 0) {
      filteredWithoutMPI.forEach(item => {
        if (item['Mileage'] !== undefined && item['Mileage'] !== null) {
          mileageTotal += Number(item['Mileage']);
          mileageCount++;
        }
      });
      avgMileage = mileageCount ? mileageTotal / mileageCount : 0;
    }

    // Calculate RO Count using **data without MPI Done filter**
    filteredWithoutMPI.forEach(item => {
      const closeddate = item['Closed Date'];
      const ronumber = item['RO'];
      if (!closedDateMap.has(closeddate)) {
        closedDateMap.set(closeddate, new Set());
      }
      closedDateMap.get(closeddate).add(ronumber);
    });

    closedDateMap.forEach(set => {
      roCount += set.size;
    });

    this.setState({
      lbrSale:
        '$' +
        laborSale
          .toFixed(0)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      lbrSoldHrs: laborSoldHrs.toFixed(1).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      prtsSale:
        '$' +
        partsSale
          .toFixed(0)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      ttlSales:
        '$' +
        totalSales
          .toFixed(0)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      avgMileage: avgMileage.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      roCount: roCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      mpiCount: mpiCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    });

    this.setState({ isLoading: false });
  };

  handleCallback = (event, picker) => {
    this.setState({ filterStart: picker.startDate.format('YYYY-MM-DD') });
    this.setState({ filterEnd: picker.endDate.format('YYYY-MM-DD') });
    this.setState({ filterChanged: true });
    this.getFilterText(picker.chosenLabel);
    // setFilterChanged(true);
  };
  getFilterText = label => {
    var filterText;

    if (label.includes('Yesterday')) {
      filterText = 'YESDT';
    } else if (label.includes('Day Before Yest')) {
      filterText = 'DBYESDT';
    } else if (label.includes('Last Week')) {
      filterText = 'LWEEK';
    } else if (label.includes('This Month')) {
      filterText = 'MTD';
    } else if (label.includes('Last Month')) {
      filterText = 'LMONTH';
    } else if (label.includes('Last 3 Mths')) {
      filterText = 'PLMTHREE';
    } else if (label.includes('Last Qtr')) {
      filterText = 'LQRTR';
    } else if (label.includes('YTD')) {
      filterText = 'YTD';
    } else if (label.includes('Last 12 Mths')) {
      filterText = 'PLYONE';
    } else if (label.includes('Last Year')) {
      filterText = 'LYEAR';
    } else if (label.includes('Custom Range')) {
      filterText = 'CRANGE';
    } else if (label.includes('This Week')) {
      filterText = 'THISWEEK';
    } else if (label.includes('Last 2 Weeks')) {
      filterText = 'LTWOWEEK';
    }
    this.setState({ filterText: filterText });
    ReactSession.set('kpiHomeToggle', filterText);
    return filterText;
  };
  handleSearchByRo = params => {
    let filterColumn = {};
    let sortColumns = {};
    window.sortStateMpiDrilldown = this.gridApi.getSortModel();
    window.filterStateMpiDrilldown = this.gridApi.getFilterModel();
    if (
      window.filterStateMpiDrilldown != undefined ||
      window.sortStateMpiDrilldown != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateMpiDrilldown)
      );
      sortColumns = this.transformSortData(window.sortStateMpiDrilldown);
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    window.colStateMpiDrilldown = this.gridColumnApi.getColumnState();
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        filterStart: this.state.filterStart,
        filterEnd: this.state.filterEnd,
        pageType: 'MPIStats',
        parent:
          this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.parent
            ? this.props.history.location.state.parent
            : '',
        reportName: this.state.reportName,
        filterText: this.state.filterText,
        visibility: this.state.selectedType,
        checkedColumns: this.state.displayCol,
        draggedColumn: this.state.draggedColumn,
        selectedAdvisor: this.state.advisors,
        selectedTech: this.state.tech,

        filterColumns: this.state.filterCol,
        sortColumns: sortColumns,
        reportNameCopy: this.state.reportNameCopy

        //type: 'one_line_drilldown',
        //oneLineType: this.state.oneLineType
      }
    });
  };

  renderBackButton = event => {
    ReactSession.set('kpiHomeToggle', this.state.filterText);
    if (
      this.state.showBackButton == true &&
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.parent == 'Home'
    ) {
      this.props.history.push({
        pathname: '/Home',
        state: {
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd
        }
      });
    } else {
      this.props.history.push({
        pathname: '/ReportSaved',
        state: {
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd,
          created_by: this.props.history.location.state.created_by
        }
      });
    }
  };

  handleCancelAllStores = () => {
    this.setState({
      openStoreDlg: false
    });
  };

  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0%';
    }
  };
  handleMailClick = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.rowData == null ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({
        openDialogue: true
      });
      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }
      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };

      // const visibleCols = this.gridColumnApi&&this.gridColumnApi.getAllDisplayedColumns();
      // const colIds = visibleCols.map(col => col.getColId());

      // const filterModel =  this.gridApi&&this.gridApi.getFilterModel();
      // this.setState({ filterModelValue: filterModel });
      // console.log('visibleCols==', this.state.appliedFilters);
      let sortModel = this.gridApi.getSortModel();
      sortModel =
        sortModel.length > 0 ? [sortModel[0].colId, sortModel[0].sort] : [];
      this.setState({
        sortModel: sortModel
      });
      const headers = columns.map(col => col.header);
      const field = columns.map(col => col.field);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];
          // 'MPI Parts Sale','MPI Labor Sale',
          if (
            [
              'prtlist',
              'prtcost',
              'lbrsale',
              'prtextendedcost',
              'prtextendedsale',
              'MPI Total Sales',
              'MPI Parts Sale',
              'MPI Labor Sale'
            ].includes(col.field)
          ) {
            return this.formatCellValue({ value });
          } else if (['MPI Parts GP %', 'MPI Labor GP %'].includes(col.field)) {
            return this.formatCellValueGP({ value });
          } else if (col.field === 'grossprofitpercentage') {
            return this.formatCellValueGP({ value });
          } else if (col.field === 'variance') {
            const formattedValue = this.formatCellValueVariance({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'variancePerc') {
            const formattedValue = this.formatCellValuePercent({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'doorRate') {
            return this.formatCellValueRate({ value });
          } else if (col.field === 'Mileage') {
            return this.formatCellValueMilage({ value });
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }

          // 'MPI Total Sales', 'MPI Parts GP %','MPI Labor GP %','MPI Parts Sale','MPI Labor Sale',
        })
      );
      this.setState({
        field: field
      });
      this.setState({
        rowDataTotal: rowData
      });
      this.setState({
        headerData: headers
      });
      this.setState({
        rowDataPdf: data
      });
      let userList = [];
      getEmail('NULL', 'NULL', 'view', 'Client_Report_Card_1_Month', result => {
        if (
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
            0
        ) {
          userList.push(
            result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
              item => item.value
            )
          );
          this.setState({
            mailUsers: userList
          });
        } else {
          this.setState({
            mailUsers: []
          });
        }
      });
    }
  };
  handleCloseEmail = () => {
    this.setState({ openDialogue: false });
  };
  cancelDilog = () => {
    this.setState({ openDilog: false });
  };
  CancelDilogAll = () => {
    this.setState({ openDilogAll: false });
  };
  handleSaveReport = () => {
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.parent != 'savedReports'
    ) {
      this.setState({ openDilogAll: true });
    } else {
      if (this.state.filterText == 'CRANGE') {
        this.setState({
          openDilog: true
        });
      } else {
        if (
          this.state.displayCol.length == 0 ||
          this.state.rowData == null ||
          this.state.zeroFilter
        ) {
          this.setState({
            openAlert: true
          });
        } else {
          this.setState({
            openSaveDlg: true
          });
        }
      }
    }
  };
  handleCancelSaveReport = () => {
    console.log(
      'handleCancelSaveReport',
      this.state.selectedType,
      this.state.parent,
      this.props
    );
    this.setState({
      openSaveDlg: false
    });
    this.setState({
      copyFile: false
    });
    this.setState({
      copy: false
    });
    if (this.state.parent != 'savedReports') {
      this.setState({
        reportName: ''
      });
      this.setState({
        selectedType: 'private'
      });
    } else {
      this.setState({
        selectedType:
          this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.visibility
      });
    }
  };
  onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;
    if (!nameRegex.test(e.target.value) && e.target.value) {
      this.setState({ requiredText: '' });
    } else {
      this.setState({
        errorReport: ''
      });
      this.setState({
        reportName: e.target.value
      });

      this.setState({ requiredText: false });
    }
  };
  handleCheckboxChange = e => {
    this.setState({
      selectedType: e.target.value
    });
  };
  transformData = data => {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, value.values])
    );
  };
  transformSortData = data => {
    return data.flatMap(({ colId, sort }) => [colId, sort]);
  };
  handleOkSaveReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    window.filterStateMpiDrilldown = this.gridApi.getFilterModel();
    window.sortStateMpiDrilldown = this.gridApi.getSortModel();

    if (
      window.filterStateMpiDrilldown != undefined ||
      window.sortStateMpiDrilldown != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateMpiDrilldown)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStateMpiDrilldown)
      );
      // this.setState({ filterCol: filterColumn });
      // this.setState({ sortCol: sortColumns });
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    if (this.state.parent == 'savedReports') {
      insertMpiStatsReportName(
        'update',
        iStoreId,
        this.state.reportName,
        userId,
        'MPI_Stats',
        this.state.selectedType,
        userRole,
        sortColumns,
        filterColumn,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,
        this.state.displayCol,
        this.state.draggedColumn,
        this.state.filterText,
        result => {
          if (
            result.data
              .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
              .results[0].status == 1
          ) {
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data
                  .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
                  .results[0].msg
            });
          }
        }
      );
    } else {
      insertMpiStatsReportName(
        'insert',
        iStoreId,
        this.state.reportName,
        userId,
        'MPI_Stats',
        this.state.selectedType,
        userRole,
        sortColumns,
        filterColumn,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,
        this.state.displayCol,
        this.state.draggedColumn,
        this.state.filterText,
        result => {
          if (
            result.data
              .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
              .results[0].status == 1
          ) {
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
            this.setState({ reportName: '' });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data
                  .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
                  .results[0].msg
            });
          }
        }
      );
    }
  };
  handleSaveAsReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    window.filterStateMpiDrilldown = this.gridApi.getFilterModel();
    window.sortStateMpiDrilldown = this.gridApi.getSortModel();

    if (
      window.filterStateMpiDrilldown != undefined ||
      window.sortStateMpiDrilldown != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateMpiDrilldown)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStateMpiDrilldown)
      );
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    insertMpiStatsReportName(
      'insert',
      iStoreId,
      this.state.reportName,
      userId,
      'MPI_Stats',
      this.state.selectedType,
      userRole,
      sortColumns,
      filterColumn,
      this.state.tech == undefined || this.state.tech.includes('All')
        ? null
        : this.state.tech,
      this.state.advisors == undefined || this.state.advisors.includes('All')
        ? null
        : this.state.advisors,
      this.state.displayCol,
      this.state.draggedColumn,
      this.state.filterText,
      result => {
        if (
          result.data
            .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
            .results[0].status == 1
        ) {
          this.setState({
            openSaveDlg: false
          });
          this.setState({
            copy: false
          });
          this.setState({
            errorReport: ''
          });
          this.setState({
            reportName: ''
          });
          this.setState({ openSusSnackbar: true });
          this.setState({ requiredText: false, reportName: '' });
        } else {
          this.setState({ requiredText: true });
          this.setState({
            errorReport:
              result.data
                .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
                .results[0].msg
          });
        }
      }
    );
  };
  handleSnackbarClose = () => {
    this.setState({ openSusSnackbar: false });
  };
  CancelAlertDilog = () => {
    this.setState({ openAlert: false });
  };
  handleCopyReport = () => {
    if (this.state.filterText == 'CRANGE') {
      this.setState({
        openDilog: true
      });
    } else if (
      this.state.displayCol.length == 0 ||
      this.state.rowData == null ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({
        openSaveDlg: true
      });
    }

    this.setState({
      copy: true
    });
    this.setState({
      copyFile: true
    });
    if (this.state.reportName != '') {
      this.setState({ reportNameCopy: this.state.reportName });
    }
    this.setState({
      reportName: ''
    });
  };
  render() {
    const { classes } = this.props;
    const {
      isExcelExportDisabled,
      isExportDisabled,
      parent,
      reportName
    } = this.state;

    const titleName =
      parent == 'savedReports'
        ? `MPI Stats - Saved Reports - ${this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.report_name &&
            this.props.history.location.state.report_name}  `
        : 'MPI Stats';

    return (
      <div>
        <Page title="MPI Stats"></Page>
        {localStorage.getItem('versionFlag') == 'FALSE' ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <React.Fragment>
            <Paper
              square
              style={{
                margin: 10,
                paddingTop: '6px',
                height: '40px',
                //backgroundColor: Dealer === 'Armatus' ? '#003d6b': '#F4E1E7',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
                display: 'flex', // Enables Flexbox
                justifyContent: 'center', // Centers horizontally
                alignItems: 'center' // Centers vertically
              }}
            >
              {/* <Grid
                container
                className={clsx(this.props.titleContainer, 'reset-dashboard')}
              > */}
              <Grid
                item
                xs={0}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  marginBottom: '6px'
                }}
              >
                {(this.state.showBackButton == true &&
                  this.props.history &&
                  this.props.history.location.state &&
                  this.props.history.location.state.parent == 'Home') ||
                (this.props.history &&
                  this.props.history.location.state &&
                  this.props.history.location.state.parent ==
                    'savedReports') ? (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.renderBackButton}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                ) : (
                  ''
                )}
              </Grid>
              <Grid
                item
                xs={12}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <Typography
                  variant="h4"
                  color="primary"
                  className={clsx(this.props.mainLabel, 'main-title')}
                  style={{ width: '100%', textAlign: 'center' }}
                  // style={{ width: '100%', color:'#fff' }}
                >
                  {/* MPI Stats */}
                  {titleName}
                </Typography>
              </Grid>
            </Paper>
            <Paper
              square
              style={{
                margin: 8,
                marginLeft: 8,
                height: 50,
                paddingTop: 4,
                paddingLeft: 4
              }}
            >
              <Grid
                container
                spacing={2}
                className={clsx(this.props.titleContainer, 'reset-dashboard')}
              >
                {' '}
                {/* <Grid
                  item
                  xs={0.5}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <Button
                    variant="contained"
                    className="bck-btn"
                    onClick={this.renderBackButton}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                </Grid>*/}
                {/* <Grid
                  item
                  xs={4}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                </Grid> */}
                {/* <Grid
                  item
                  xs={5}
                  style={{ display: 'flex', justifyContent: 'flex-start' }}
                >
                  {this.state.dates && this.state.dates[0] && (
                    <FormControl
                      variant="outlined"
                      margin="dense"
                      className={clsx(classes.formControl, 'input-container')}
                      style={{ margin: 5 }}
                    >
                      <DateRangePicker
                        initialSettings={{
                          locale: {
                            format: 'MM/DD/YY',
                            separator: ' - '
                          },
                          ranges: {
                            ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.yesterDay]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].yesterday
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].yesterday
                              ).toDate()
                            ],
                            ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.dayBfYest]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].dayBeforeYesterday
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].dayBeforeYesterday
                              ).toDate()
                            ],
                            ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastWeek]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastweekstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastweekenddate
                              ).toDate()
                            ],

                            ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.mtd]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].mtdstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].mtdenddate
                              ).toDate()
                            ],
                            ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastMonth]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastmonthstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastmonthenddate
                              ).toDate()
                            ],
                            ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastThreeMonths]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastthreemonthstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastthreemonthenddate
                              ).toDate()
                            ],
                            ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastQtr]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastquarterstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastquarterenddate
                              ).toDate()
                            ],
                            ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.ytd]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].ytdstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].ytdenddate
                              ).toDate()
                            ],
                            ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastTwelveMonths]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lasttwelvemonthstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lasttwelvemonthenddate
                              ).toDate()
                            ],
                            ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastYear]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastyearstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastyearenddate
                              ).toDate()
                            ]
                          },
                          maxDate: moment(
                            this.state.dates[0] && this.state.dates[0].today
                          ).toDate(),
                          alwaysShowCalendars: false,
                          applyClass: clsx(classes.calButton, 'apply-btn'),
                          cancelClass: clsx(classes.calButton, 'apply-btn'),
                          startDate:
                            this.state.filterStart &&
                            this.state.filterStart != ''
                              ? moment(this.state.filterStart).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].mtdstartdate
                                ).toDate()
                              : moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastmonthstartdate
                                ).toDate(),
                          endDate:
                            this.state.filterEnd && this.state.filterEnd != ''
                              ? moment(this.state.filterEnd).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].mtdenddate
                                ).toDate()
                              : moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastmonthenddate
                                ).toDate()
                          //showDropdowns: true
                        }}
                        onApply={this.handleCallback}
                      >
                        <input
                          type="text"
                          className="datepicker"
                          id="picker"
                          name="picker"
                          aria-labelledby="label-picker"
                        />
                      </DateRangePicker>
                      <label class="labelpicker" for="picker" id="label-picker">
                        <div class="textpicker">Select Date</div>
                      </label>
                    </FormControl>
                  )}
                </Grid> */}
                <Grid
                  item
                  xs={12}
                  style={{ display: 'flex', justifyContent: 'flex-end' }}
                >
                  <div className={classes.allItems}>
                    <div className={clsx(classes.dataAsOf)}>
                      {this.state.closedDate ? (
                        <Typography
                          variant="h6"
                          align="right"
                          style={{
                            fontSize: 12,
                            color: '#7987a1',
                            fontWeight: 'bold'
                          }}
                        >
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between',
                              lineHeight: '13px'
                            }}
                          >
                            <div>Data&nbsp;as&nbsp;of:</div>
                            <div className={classes.dataAsOfValue}>
                              {moment(this.state.closedDate).format('MM/DD/YY')}
                            </div>
                          </div>
                        </Typography>
                      ) : (
                        ''
                      )}
                    </div>

                    {this.state.dates && this.state.dates[0] && (
                      <FormControl
                        variant="outlined"
                        margin="dense"
                        className={clsx(
                          classes.formControl,
                          'input-container select-date-mpistats'
                        )}
                      >
                        <DateRangePicker
                          initialSettings={{
                            locale: {
                              format: 'MM/DD/YY',
                              separator: ' - '
                            },
                            ranges: {
                              ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.yesterDay]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].yesterday
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].yesterday
                                ).toDate()
                              ],
                              ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.dayBfYest]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].dayBeforeYesterday
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].dayBeforeYesterday
                                ).toDate()
                              ],

                              ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.thisWeek]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].thisweekstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].thisweekenddate
                                ).toDate()
                              ],
                              ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.lastWeek]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastweekstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastweekenddate
                                ).toDate()
                              ],
                              ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.lastTwoWeek]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lasttwoweekstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lasttwoweekenddate
                                ).toDate()
                              ],

                              ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.mtd]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].mtdstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].mtdenddate
                                ).toDate()
                              ],
                              ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.lastMonth]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastmonthstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastmonthenddate
                                ).toDate()
                              ],
                              ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.lastThreeMonths]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastthreemonthstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastthreemonthenddate
                                ).toDate()
                              ],
                              ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.lastQtr]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastquarterstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastquarterenddate
                                ).toDate()
                              ],
                              ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.ytd]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].ytdstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].ytdenddate
                                ).toDate()
                              ],
                              ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.lastTwelveMonths]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lasttwelvemonthstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lasttwelvemonthenddate
                                ).toDate()
                              ],
                              ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                              this.state.lastYear]: [
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastyearstartdate
                                ).toDate(),
                                moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastyearenddate
                                ).toDate()
                              ]
                            },
                            maxDate: moment(
                              this.state.dates[0] && this.state.dates[0].today
                            ).toDate(),
                            alwaysShowCalendars: false,
                            applyClass: clsx(classes.calButton, 'apply-btn'),
                            cancelClass: clsx(classes.calButton, 'apply-btn'),
                            startDate:
                              this.state.filterStart &&
                              this.state.filterStart != ''
                                ? moment(this.state.filterStart).toDate()
                                : localStorage.getItem('kpiDataStatus') == 1
                                ? moment(
                                    this.state.dates[0] &&
                                      this.state.dates[0].mtdstartdate
                                  ).toDate()
                                : moment(
                                    this.state.dates[0] &&
                                      this.state.dates[0].lastmonthstartdate
                                  ).toDate(),
                            endDate:
                              this.state.filterEnd && this.state.filterEnd != ''
                                ? moment(this.state.filterEnd).toDate()
                                : localStorage.getItem('kpiDataStatus') == 1
                                ? moment(
                                    this.state.dates[0] &&
                                      this.state.dates[0].mtdenddate
                                  ).toDate()
                                : moment(
                                    this.state.dates[0] &&
                                      this.state.dates[0].lastmonthenddate
                                  ).toDate()
                            //showDropdowns: true
                          }}
                          onApply={this.handleCallback}
                        >
                          <input
                            type="text"
                            className="datepicker"
                            id="picker"
                            name="picker"
                            aria-labelledby="label-picker"
                          />
                        </DateRangePicker>
                        <label
                          class="labelpicker"
                          for="picker"
                          id="label-picker"
                        >
                          <div class="textpicker">Select Date</div>
                        </label>
                      </FormControl>
                    )}
                    <div
                      style={{
                        marginTop: '2px',
                        display: 'flex',
                        justifyContent: 'flex-end', // Align icons to the right
                        alignItems: 'center', // Center icons vertically
                        //gap: "15px", // Space between icons
                        padding: '5px'
                      }}
                      id="mpi-stats-icons"
                    >
                      {/* Save Report */}
                      <Tooltip title="Save Report">
                        <Link
                          className={classes.linkItem}
                          style={{
                            pointerEvents: 'auto',
                            cursor: 'pointer'
                            // opacity: 0.5
                          }}
                          onClick={this.handleSaveReport}
                        >
                          <SaveIcon className="saveicon" />
                        </Link>
                      </Tooltip>
                      {this.state.parent == 'savedReports' && (
                        <Tooltip title="Rename and Copy">
                          <Link
                            className={classes.linkItem}
                            // style={linkStyleCopy}
                            style={{
                              display: 'block',
                              marginLeft: 2,
                              marginRight: -3
                              // display:
                              //   (createdBy != '' &&
                              //     createdBy == localStorage.getItem('userID')) ||
                              //   selectedType == 'public' ||
                              //   props.keycloak.realmAccess.roles.includes('superadmin')
                              //     ? 'block'
                              //     : 'none'
                            }}
                            onClick={this.handleCopyReport}
                          >
                            <FileCopyOutlinedIcon
                              style={{
                                height: '0.73em',
                                weight: '0.8em',
                                marginRight: '-4px',
                                marginTop: '2px'

                                //                         height: 0.76em;
                                // margin-right: -8px;
                                // margin-top: 2px;
                                // display: allStore ? 'none' : 'block'
                              }}
                            />
                          </Link>
                        </Tooltip>
                      )}

                      {/* Email Now */}
                      <Tooltip title="Email Now">
                        <Link
                          id="email-now"
                          onClick={this.handleMailClick}
                          style={{
                            cursor: isExportDisabled
                              ? 'not-allowed'
                              : 'pointer',
                            opacity: isExportDisabled ? 0.5 : 1,
                            pointerEvents: isExportDisabled ? 'none' : 'auto',
                            marginRight: '6px'
                            //fontSize: "20px"
                          }}
                        >
                          <MailIcon
                            style={{
                              height: '1.15em',
                              weight: '1.0em',
                              paddingTop: '2px',
                              marginRight: '-2px',
                              marginLeft: '4px'
                            }}
                          />
                        </Link>
                      </Tooltip>

                      {/* Export to PDF */}
                      <Tooltip title="Export To PDF">
                        <Link
                          id="export-to-pdf"
                          style={{
                            marginRight: '6px',
                            // cursor: isExportDisabled
                            //   ? 'not-allowed'
                            //   : 'pointer',
                            //opacity: isExportDisabled ? 0.5 : 1,
                            //pointerEvents: isExportDisabled ? 'none' : 'auto'
                            cursor: 'pointer'
                          }}
                          onClick={this.exportToPDF}
                        >
                          <i
                            className="fas fa-file-pdf"
                            style={{
                              color: 'red'
                            }}
                          ></i>
                        </Link>
                      </Tooltip>

                      {/* Export to Excel */}
                      <Tooltip title="Export To Excel">
                        <Link
                          id="export-to-excel"
                          style={{
                            //marginRight: "6px" ,
                            cursor: 'pointer',
                            opacity: 1,
                            pointerEvents: 'auto',
                            // cursor: isExcelExportDisabled
                            //   ? 'not-allowed'
                            //   : 'pointer',
                            // opacity: isExcelExportDisabled ? 0.5 : 1,
                            // pointerEvents: isExcelExportDisabled
                            //   ? 'none'
                            //   : 'auto',
                            float: 'right',
                            marginRight: '6px'
                          }}
                          onClick={this.onBtExport}
                        >
                          <i
                            className="fas fa-file-excel"
                            style={{ color: 'green' }}
                          ></i>
                        </Link>
                      </Tooltip>
                    </div>
                    <Button
                      variant="contained"
                      id="reset-layout"
                      className={clsx(classes.resetBtn, 'reset-btn')}
                      onClick={this.resetRawData}
                    >
                      <RestoreIcon />
                      <Typography variant="body1" align="left">
                        Reset Layout
                      </Typography>
                    </Button>
                  </div>{' '}
                </Grid>
              </Grid>
            </Paper>
            {this.state.isLoading == true ? (
              <div>
                <Box style={{ padding: 25 }}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    style={{ padding: 25 }}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : (
              <Paper
                square
                style={{
                  margin: 8,
                  marginLeft: 8,
                  height: 35,
                  paddingTop: 4,
                  paddingLeft: 4
                }}
              >
                <Grid
                  container
                  xs={12}
                  className={classes.rankedTableContainer}

                  //className={clsx(classes.titleContainer, 'reset-dashboard')}
                >
                  <Grid xs={12}>
                    <div>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          RO&nbsp;Count:&nbsp;
                          <span style={{ color: '#003d6b' }}>
                            {this.state.roCount}
                          </span>
                          {/* <span style={{ color: '#003d6b' }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span> */}
                        </Typography>
                      </Button>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          MPI&nbsp;Count:&nbsp;
                          <span style={{ color: '#003d6b' }}>
                            {this.state.mpiCount}
                          </span>
                        </Typography>
                      </Button>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          MPI&nbsp;Hours&nbsp;Sold:&nbsp;
                          <span style={{ color: '#003d6b' }}>
                            {this.state.lbrSoldHrs}
                          </span>
                          {/* <span style={{ color: '#003d6b' }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span> */}
                        </Typography>
                      </Button>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          MPI&nbsp;Labor&nbsp;Sale:&nbsp;
                          <span style={{ color: '#003d6b' }}>
                            {this.state.lbrSale}
                          </span>
                          {/* <span style={{ color: '#003d6b' }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span> */}
                        </Typography>
                      </Button>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          MPI&nbsp;Parts&nbsp;Sale:&nbsp;
                          <span style={{ color: '#003d6b' }}>
                            {this.state.prtsSale}
                          </span>
                          {/* <span style={{ color: '#003d6b' }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span> */}
                        </Typography>
                      </Button>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          Avg&nbsp;Mileage:&nbsp;
                          <span style={{ color: '#003d6b' }}>
                            {this.state.avgMileage}
                          </span>
                          {/* <span style={{ color: '#003d6b' }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span> */}
                        </Typography>
                      </Button>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          MPI&nbsp;Total&nbsp;Sales:&nbsp;
                          <span style={{ color: '#003d6b' }}>
                            {this.state.ttlSales}
                          </span>
                          {/* <span style={{ color: '#003d6b' }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span> */}
                        </Typography>
                      </Button>
                    </div>
                  </Grid>
                </Grid>
              </Paper>
            )}
          </React.Fragment>
        )}

        <div
          id="data-tab-labor-misses"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            height: window.innerHeight - 250 + 'px',
            // height:(window.innerHeight-215)+'px',
            // width: '100%',
            alignContent: 'center',
            marginLeft: '8px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            excelStyles={this.state.excelStyles}
            defaultColDef={this.state.defaultColDef}
            popupParent={this.state.popupParent}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            floatingFilter={true}
            suppressRowClickSelection={true}
            headerHeight={this.state.headerHeight}
            onFilterChanged={this.onFilterChanged}
            suppressDragLeaveHidesColumns={true}
            sideBar={this.state.sideBar}
            suppressContextMenu={true}
            onColumnVisible={this.onColumnVisible}
            //onFirstDataRendered={this.onFirstDataRendered}
            onModelUpdated={this.onModelUpdated}
            onColumnMoved={this.onColumnMoved}
          />
        </div>
        <EmailDialogKpi
          open={this.state.openDialogue}
          handlePopupClose={this.handleCloseEmail}
          mailUsers={this.state.mailUsers}
          // clientReportCardDetails={clientReportCardDetails}
          // image={image}
          // selectedWorkmixOptions={selectedWorkmixOptions}
          iKpiReportType={this.state.iKpiReportType}
          // measuredMTH={measuredMTH}
          // priorMTH={priorMTH}
          // selectedOptions={selectedOptions}
          selectedStoreIds={this.state.selectedStoreIds}
          reportName={this.state.reportName}
          reportNameCopy={this.state.reportNameCopy}
          // selectStoreDetails={selectStoreDetails}
          selectedStoreName={localStorage.getItem('storeSelected')}
          headerData={this.state.headerData}
          rowDataPdf={[]}
          filterStart={this.state.filterStart}
          filterEnd={this.state.filterEnd}
          rowDataTotal={[]}
          selectedServiceAdvisors={ReactSession.get('serviceAdvisors')}
          selectedTechnicians={ReactSession.get('technicians')}
          filterModelValue={this.state.appliedFilters}
          field={this.state.field}
          roValue={this.state.roCount}
          mileageValue={this.state.avgMileage}
          draggedColumn={this.state.draggedColumn}
          sortModel={this.state.sortModel}
        ></EmailDialogKpi>
        <SaveReportDialog
          openSaveDlg={this.state.openSaveDlg}
          parent={this.state.parent}
          copyFile={this.state.copyFile}
          copy={this.state.copy}
          reportName={this.state.reportName}
          errorReport={this.state.errorReport}
          requiredText={this.state.requiredText}
          selectedType={this.state.selectedType}
          filterText={this.state.filterText}
          onChangeReportName={this.onChangeReportName}
          handleCancelSaveReport={this.handleCancelSaveReport}
          handleOkSaveReport={this.handleOkSaveReport}
          handleSaveAsReport={this.handleSaveAsReport}
          handleCheckboxChange={this.handleCheckboxChange}
          reportNameCopy={this.state.reportNameCopy}
        />
        <SuccessSnackbar
          onClose={this.handleSnackbarClose}
          open={this.state.openSusSnackbar}
          msg={'Report saved successfully!'}
          autoHideDuration={2000}
          //goalFail={this.state.goalFail}
        />
        <AlertSnackbar
          onClose={this.CancelAlertDilog}
          open={this.state.openAlert}
          msg={'No data available'}
        />
        <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openStoreDlg}
        >
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              This option is not available from this page.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCancelAllStores} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
        {this.state.openDilog ? (
          <Dialog
            open={this.state.openDilog}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available for custom date range.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.cancelDilog} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
        {this.state.openDilogAll ? (
          <Dialog
            open={this.state.openDilogAll}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available at all stores.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.CancelDilogAll} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
      </div>
    );
  }
}

const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '30px' }}
        color="primary"
      >
        {title}
      </Typography>
      <Typography variant="subtitle1" style={{ fontSize: '14px' }}>
        {' '}
        {value}
      </Typography>
    </Grid>
  );
};
const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    marginTop: 7
  },
  dataAsOf: {
    float: 'right',
    marginTop: 16
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '5px !important'
    }
  },

  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  containerTotal: {
    marginTop: '7px',
    border: '1px solid rgba(0, 0, 0, 0.23)',
    position: 'relative',
    padding: '6px 0px 13px',
    height: '40px',
    borderRadius: '4px'
    // width:'934px'
  },
  spancls: {
    color: '#7987a1',
    marginLeft: 3,
    fontSize: 10
  },
  // summaryBlockText: {
  //   fontSize: 15,
  //   color: '#7987a1',
  //   fontWeight: 'bold'
  // },
  allJobs: {
    marginTop: 12,
    marginRight: 10
  },
  ButtonSelect: {
    lineHeight: 1.5,
    position: 'absolute',
    '@media (min-width: 2560px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 1920px)': {
      width: 183,
      marginTop: '-5px !important',
      marginBottom: '3px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '-5px !important',
      width: 154,
      marginBottom: '2x !important'
    }
  },
  reportButton: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    }
  },
  reportButtonSelect: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  resetBtn: {
    marginRight: 10,
    float: 'right',
    marginTop: 10
  },
  allItems: {
    display: 'flex',
    justifyContent: 'end'
  }
});

export default withKeycloak(withStyles(styles)(DrilldownMPIStats));
