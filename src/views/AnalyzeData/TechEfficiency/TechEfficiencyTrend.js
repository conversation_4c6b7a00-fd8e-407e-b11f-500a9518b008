import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Button,
  Typography,
  CircularProgress
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import React, { useEffect, useState } from 'react';

import clsx from 'clsx';
import 'src/styles.css';
import {
  getAllTechnicians,
  gettechMetricsTechJob
} from 'src/utils/hasuraServices';
import BarChartRenderer from './BarChartRenderer';
import BarChartRendererPostgraphile from './BarChartRendererPostgraphile';
import Alert from '@material-ui/lab/Alert';
import { getDashboardGraphQuery } from 'src/components/DashboardGraphQuery';
import { useSelector, useDispatch } from 'react-redux';
import { setTechnicianSummary, setSelectedTech } from 'src/actions';
import { useHistory } from 'react-router';

var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;
const useStyles = makeStyles({
  formControl: {
    padding: 4,
    minWidth: 250
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  alertGrid: {
    margin: 4,
    padding: 4
  },
  alertInfo: {
    fontSize: 14,
    fontWeight: 'bold'
  },
  backButton: {
    marginTop: '18px !important'
  }
});
const TechEfficiencyTrend = ({
  props,
  removeFav,
  parentCallback,
  selectedTechnician,
  tabSelection,
  mainTabSelection,
  parent,
  techniciansArr
}) => {
  const session = useSelector(state => state.session);
  const dispatch = useDispatch();
  const history = useHistory();
  const realm = props.keycloak.realm;
  const classes = useStyles();
  const [technician, setTechnician] = useState([]);
  const [technicianData, setTechnicianData] = useState([]);
  const [dropdownTechnician, setDropdownTechnician] = useState([]);
  const [checkEmpty, setCheckEmpty] = useState(false);
  const [techChanged, setTechChanged] = useState(false);
  const [alert, setAlert] = useState(true);
  const [parentCheck, setParentCheck] = useState(parent);
  const [techMetricChartData, setTechMetricChartData] = useState(null);
  const [technicianSelected, settechnicianSelected] = useState(
    typeof props.technicianSelected != 'undefined' &&
      props.technicianSelected != '' &&
      props.tabSelection !== 'two'
      ? props.techName
      : selectedTechnician &&
        parent == 'Home' &&
        session.selectedTech != 'All[All]'
      ? selectedTechnician
      : 'All[All]'
  );
  const [loader, setLoader] = useState(false);

  useEffect(() => {
    setTimeout(() => {
      setAlert(false);
    }, 10000);
  }, []);
  useEffect(() => {
    window.filterStatesTech = {};
    window.sortStatesTech = {};
    //settechnicianSelected('All[All]');
    //dispatch(setSelectedTech('All[All]'));

    if (techniciansArr) {
      const dataArr = techniciansArr;
      const serviceDepartment = dataArr.filter(
        item => item.department === 'Service'
      );
      var techArr = serviceDepartment.filter(e => e.active == 1);
      techArr = techArr.map(
        e =>
          (e.nickname ? e.nickname : e.name) +
          ' [' +
          e.lbrtechno.toString() +
          ']' +
          '-status-' +
          e.active
      );
      const sortedArr = [...techArr].sort((a, b) => a.localeCompare(b));
      setTechnician(sortedArr);
    }
  }, [session.storeSelected]);
  const [technicianSelectedDetail, settechnicianSelectedDetail] = useState(
    typeof props.technicianSelected != 'undefined' &&
      props.technicianSelected != ''
      ? props.techName
      : 'All[All]'
  );
  //const [meauresforQuery] = useState([1, 2, 3, 4, 5, 6, 7, 8]);
  const [meauresforQuery] = useState([1, 2, 3, 4]);

  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));

  let filteredResult = chartList.filter(
    item => item.dbdName == 'Technician Efficiency' && item.parentId == null
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');

  useEffect(() => {
    const chartId = orderedData[4]?.chartId;
    if (chartId) {
      getDataforTechCharts(chartId);
    }
  }, [session.storeSelected]);

  useEffect(() => {
    if (parent == 'Home' && session.selectedTech != 'All[All]') {
      settechnicianSelected(selectedTechnician);
      dispatch(setSelectedTech(selectedTechnician));
      dispatch(setTechnicianSummary(selectedTechnician));
    } else if (
      typeof props.technicianSelected != 'undefined' &&
      props.technicianSelected != '' &&
      props.tabSelection !== 'two'
    ) {
      settechnicianSelected(props.techName);
    } else {
      settechnicianSelected('All[All]');
    }
  }, [session.serviceAdvisor]);
  const handleChange = val => {
    setCheckEmpty(val);
  };

  const getDataforTechCharts = chartId => {
    let tech =
      session.selectedTech &&
      session.selectedTech !== 'All[All]' &&
      typeof session.selectedTech === 'string'
        ? session.selectedTech.split('[')[1] &&
          session.selectedTech
            .split('[')[1]
            .split(']')[0]
            .toString()
        : parent == 'Home' && selectedTechnician
        ? getTechId(selectedTechnician)
        : 'All';

    tech = tech == '' ? 'All' : tech;
    gettechMetricsTechJob(tech, callback => {
      if (callback) {
        setTechMetricChartData(callback);
      }
    });
  };

  const handleTechChange = event => {
    setLoader(true);
    setTechChanged(true);
    settechnicianSelected(event.target.value);
    dispatch(setSelectedTech(event.target.value));
    dispatch(setTechnicianSummary(event.target.value));
    setTimeout(() => {
      setLoader(false);
    }, 5000);
  };
  const [queryFormat, setQuery] = useState();
  const renderBackButton = event => {
    history.push({
      pathname:
        localStorage.getItem('versionFlag') == 'TRUE' ? '/Home' : '/2.4.0/Home',
      state: {
        toggleOptions: props.selectedToggle ? props.selectedToggle : ''
      }
    });
  };
  const handleCallback = val => {
    if (val) {
      if (technicianSelected == '' || technicianSelected == 'All') {
        alert('Choose any Technician ');
      } else {
        let data = {
          type: 'techniciantrendingcharts',
          person:
            typeof val.techName != 'undefined'
              ? val.techName
                  .split('[')[1]
                  .split(']')[0]
                  .toString()
              : props.technicianSelected,
          // person:
          //   typeof props.technicianSelected != 'undefined' &&
          //   props.technicianSelected ==
          //     (Array.isArray(technicianSelected)
          //       ? technicianSelected[0]
          //           .split('[')[1]
          //           .split(']')[0]
          //           .toString()
          //       : technicianSelected
          //           .split('[')[1]
          //           .split(']')[0]
          //           .toString())
          //     ? props.technicianSelected
          //     : technicianSelected
          //         .split('[')[1]
          //         .split(']')[0]
          //         .toString(),
          reportType: val.reportType,
          selectedSubTab: 'one',
          tabSelection: 'one',
          techName:
            typeof val.techName != 'undefined' ? val.techName : props.techName,
          //: technicianSelected,
          month_year: val.month_year !== 'undefined' ? val.month_year : '',
          payType: val.payType,
          monthSelected: val.monthSelected,
          isFrom: val.isFrom,
          selectedToggle: val.selectedToggle,
          drilldownVal: val.drilldownVal,
          weekEnd: val.weekEnd,
          weekStart: val.weekStart,
          techPageType: 'techSummary'
        };

        parentCallback(data);
      }
    }
  };
  const getTechId = tech => {
    const regex = /\[(.*?)\]/; // Matches anything inside square brackets

    const match = tech.match(regex);
    if (match) {
      const valueInsideBrackets = match[1];
      return valueInsideBrackets;
    } else {
      return 'All';
    }
  };
  const handleClosePopup = value => {};

  if (technicianSelected == '[null]') {
    settechnicianSelected('All[All]');
  }
  localStorage.setItem(
    'selectedTechName',
    Array.isArray(session.selectedTech)
      ? session.selectedTech[0]
      : session.selectedTech
  );

  localStorage.setItem(
    'selectedTech',
    session.selectedTech != '' && typeof session.selectedTech == 'string'
      ? session.selectedTech.split('[')[1] &&
          session.selectedTech
            .split('[')[1]
            .split(']')[0]
            .toString()
      : 'All'
  );
  // localStorage.setItem(
  //   'selectedTech',
  //   technicianSelected != '' && technicianSelected != 'All'
  //     ? technicianSelected[0]
  //         .split('[')[1]
  //         .split(']')[0]
  //         .toString()
  //     :   technicianSelected
  //         .split('[')[1]
  //         .split(']')[0]
  //         .toString()
  // );

  return (
    <>
      {techMetricChartData ? (
        <Grid container spacing={12} className={classes.gridContainer}>
          <Grid container spacing={12}>
            <Grid item xs={12}>
              <Paper square className={classes.alertGrid}>
                {checkEmpty ? (
                  <Alert severity="info" className={classes.alertInfo}>
                    Selected Technician have no data found during the time
                    period{' '}
                  </Alert>
                ) : null}
                {!checkEmpty && realm == 'haleyag' ? (
                  <Alert severity="info" className={classes.alertInfo}>
                    No data found for actual hours punched by Technicians{' '}
                  </Alert>
                ) : null}
                {alert &&
                Array.isArray(props.technicianSelected) &&
                props.technicianSelected.length > 1 ? (
                  <Alert severity="warning" className={classes.alertInfo}>
                    Multiple Technician selection is yet to be implemented{' '}
                  </Alert>
                ) : null}
                {parent == 'Home' ||
                parentCheck == 'Home' ||
                (parent == '' &&
                  props.selectedToggle != '' &&
                  props.selectedToggle != undefined) ? (
                  <Button
                    variant="contained"
                    className={clsx(classes.backButton, 'bck-btn')}
                    onClick={renderBackButton}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                ) : null}

                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={classes.formControl}
                >
                  <InputLabel
                    htmlFor="outlined-age-native-simple"
                    margin="dense"
                  >
                    Technician
                  </InputLabel>
                  <Select
                    margin="dense"
                    variant="outlined"
                    label="Technician"
                    name="Select Technician"
                    value={
                      (typeof props.techName != 'undefined' &&
                        props.techName == session.selectedTech) ||
                      props.technicianSelected == session.selectedTech[0]
                        ? props.techName
                        : session.selectedTech &&
                          session.selectedTech.split(/\[|\]/)[1] != undefined &&
                          session.selectedTech.split(/\[|\]/)[1] ===
                            props.technicianSelected
                        ? props.techName.replace(/\[/, ' [')
                        : parent == 'Home' &&
                          selectedTechnician &&
                          techChanged == false
                        ? selectedTechnician
                        : session.selectedTech
                    }
                    onChange={handleTechChange}
                  >
                    <MenuItem value="All[All]" style={{}}>
                      {' '}
                      All Technicians{' '}
                    </MenuItem>
                    {technician.length
                      ? technician.map(e => (
                          <MenuItem
                            data-active={
                              e.split('-status-')[1] == 0 ? false : true
                            }
                            value={e.split('-status-')[0]}
                            style={{
                              backgroundColor:
                                e.split('-status-')[1] == 0
                                  ? Dealer === 'Armatus'
                                    ? '#ddeaf4'
                                    : '#F4E1E7'
                                  : '',
                              color:
                                e.split('-status-')[1] == 0
                                  ? Dealer === 'Armatus'
                                    ? '#969592'
                                    : '#F4E1E7'
                                  : ''
                            }}
                          >
                            {' '}
                            {e.split('-status-')[0]}{' '}
                          </MenuItem>
                        ))
                      : null}
                  </Select>
                </FormControl>
              </Paper>
            </Grid>
          </Grid>

          {loader && (
            <div
              style={{
                position: 'fixed',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
                background: 'rgba(255, 255, 255, 255)',
                zIndex: '1000',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center'
              }}
            >
              <CircularProgress size={60} />
            </div>
          )}

          {orderedData.map((item, index) => (
            <Grid
              item
              xs={
                parseInt(item.chartId) === 1345 ||
                parseInt(item.chartId) === 1352 ||
                parseInt(item.chartId) === 1363 ||
                parseInt(item.chartId) === 1358
                  ? 12
                  : 6
              }
              id={'chart-' + item.chartId}
              className={clsx('diagram-section')}
            >
              <BarChartRendererPostgraphile
                chartId={parseInt(item.chartId)}
                title={index + 1}
                tabSelection={tabSelection}
                mainTabSelection={mainTabSelection}
                tech={
                  session.selectedTech &&
                  session.selectedTech !== 'All[All]' &&
                  typeof session.selectedTech === 'string'
                    ? session.selectedTech.split('[')[1] &&
                      session.selectedTech
                        .split('[')[1]
                        .split(']')[0]
                        .toString()
                    : parent == 'Home' && selectedTechnician
                    ? getTechId(selectedTechnician)
                    : 'All'
                }
                techName={
                  typeof props.techName != 'undefined' &&
                  technicianSelected &&
                  (props.techName == technicianSelected ||
                    props.technicianSelected == technicianSelected[0])
                    ? props.techName
                    : parent == 'Home' && selectedTechnician
                    ? selectedTechnician
                    : technicianSelected
                }
                handleClosePopup={handleClosePopup}
                checkEmpty={handleChange}
                parentCallback={handleCallback}
                realm={realm}
                chartTitle={item.chartName}
                callFrom={0}
                removeFav={removeFav}
                isFrom={parent == 'Home' ? parent : ''}
                selectedToggle={props.selectedToggle}
                ChartData={techMetricChartData}
              />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Grid
          justify="center"
          style={{
            height: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </Grid>
      )}
    </>
  );
};

export default TechEfficiencyTrend;
