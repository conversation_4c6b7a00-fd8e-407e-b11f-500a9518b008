import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { Paper } from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { CircularProgress, Grid } from '@material-ui/core';
import BarChartIcon from '@material-ui/icons/BarChart';
import CompareArrowsIcon from '@material-ui/icons/CompareArrows';
import { withStyles } from '@material-ui/styles';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import ComparisonChartsGrid from './ComparisonChartsGrid';
import TechEfficiencyTrend from './TechEfficiencyTrend';
import {
  getMostFrequentTechnician,
  getAllTechnicians
} from 'src/utils/hasuraServices';
import { withKeycloak } from '@react-keycloak/web';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { traceSpan } from 'src/utils/OTTTracing';
import { ReactSession } from 'react-client-session';

var lodash = require('lodash');

class TechEfficiency extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      tabSelection: this.props.tabSelection ? this.props.tabSelection : 'one',
      mainTabSelection: this.props.mainTabSelection
        ? this.props.mainTabSelection
        : 'seven',
      technicianSelected: '',
      technicians: [],
      isLoading: true,
      parent: this.props.parent ? this.props.parent : '',
      store: localStorage.getItem('selectedStoreId')
    };
  }
  componentWillMount() {
    if (
      this.props.parent != 'Home' ||
      (Array.isArray(this.props.technicianSelected) == false &&
        this.props.technicianSelected != '') ||
      this.props.technicianSelected.length > 1
    ) {
      // getMostFrequentTechnician(result => {
      //   this.setState({ isLoading: true });
      //   if (
      //     result.data
      //       .statelessDbdPeopleMetricsTechnicianGetMostFrequentTechnician.nodes
      //   ) {
      //     let tech =
      //       result.data
      //         .statelessDbdPeopleMetricsTechnicianGetMostFrequentTechnician
      //         .nodes[0].techname +
      //       ' [' +
      //       result.data.statelessDbdPeopleMetricsTechnicianGetMostFrequentTechnician.nodes[0].lbrtechno.toString() +
      //       ']';
      //
      //     if (
      //       this.props.parent == 'Home' &&
      //       Array.isArray(this.props.technicianSelected) == false &&
      //       this.props.technicianSelected != ''
      //     ) {
      //       this.setState({
      //         technicianSelected: this.props.technicianSelected
      //       });
      //     } else if (
      //       this.props.parent == 'Home' &&
      //       Array.isArray(this.props.technicianSelected) == true &&
      //       this.props.technicianSelected.length > 1
      //     ) {
      //
      //     } else {
      //       this.setState({ technicianSelected: tech });
      //     }
      //   }
      // });
      //
      this.setState({ technicianSelected: 'All[All]' });
      this.setState({ isLoading: false });
    }
    getAllTechnicians(result => {
      this.setState({ isLoading: true });

      var techniacianData = [];
      if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
        this.setState({
          technicians: result.data.statelessCcPhysicalRwGetTechnicians.nodes
        });

        if (
          this.props.parent == 'Home' &&
          this.props.technicianSelected &&
          this.props.technicianSelected.length == 1
        ) {
          let tech = '';
          if (
            result.data.statelessCcPhysicalRwGetTechnicians.nodes.length > 0
          ) {
            // if (this.props.technicianSelected[0].split('[')[0] != '') {
            //   alert(1.1);
            //   let techSelected = this.props.technicianSelected[0]
            //     .split('[')[1]
            //     .split(']')[0];
            //   tech = result.data.statelessCcPhysicalRwGetTechnicians.nodes.filter(
            //     item => item.lbrtechno == techSelected
            //   );
            // } else {
            //   alert(1.2);
            tech = result.data.statelessCcPhysicalRwGetTechnicians.nodes.filter(
              item => item.lbrtechno == this.props.technicianSelected[0]
            );
            // }
            this.setState({
              technicianSelected:
                tech[0].nickname && tech[0].nickname != ''
                  ? tech[0].nickname + ' [' + tech[0].lbrtechno.toString() + ']'
                  : tech[0].name + ' [' + tech[0].lbrtechno.toString() + ']'
            });
          }
        }
        if (this.props.parent == 'Home') {
          this.setState({ isLoading: false });
        }
      }
    });
  }

  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ technicianSelected: 'All[All]' });
        this.setState({ parent: '' });
      }
    }
  }
  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'TechMetrics TabClick',
      value: newValue,
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('TechMetrics TabClick', spanAttribute);
  };

  render() {
    const { classes } = this.props;

    if (
      this.state.technicianSelected != '' &&
      this.state.technicians.length != 0
    ) {
      return (
        <div>
          <Paper
            square
            style={{ margin: 8, backgroundColor: '#FFF', marginTop: '20px' }}
          >
            <Tabs
              variant="standard"
              // scrollButtons="auto"
              value={this.state.tabSelection}
              onChange={this.handleTabChange}
              indicatorColor="secondary"
              textColor="secondary"
              TabIndicatorProps={{ style: { display: 'none' } }}
            >
              <Tab
                style={{ textTransform: 'none' }}
                label={
                  <div>
                    <BarChartIcon style={{ verticalAlign: 'middle' }} />
                    13 Month Trend by Technician
                  </div>
                }
                value="one"
                className={
                  this.state.tabSelection == 'one' ? classes.tabSelected : null
                }
              />
              <Tab
                style={{ textTransform: 'none' }}
                label={
                  <div>
                    <CompareArrowsIcon style={{ verticalAlign: 'middle' }} />
                    Comparison by Month
                  </div>
                }
                value="two"
                className={
                  this.state.tabSelection == 'two' ? classes.tabSelected : null
                }
              />
            </Tabs>
          </Paper>

          {this.state.tabSelection == 'one' &&
          this.state.store == localStorage.getItem('selectedStoreId') ? (
            <TechEfficiencyTrend
              props={this.props}
              parentCallback={this.props.parentCallback}
              selectedTechnician={this.state.technicianSelected}
              techniciansArr={this.state.technicians}
              removeFav={this.props.removeFav}
              tabSelection={this.state.tabSelection}
              mainTabSelection={this.state.mainTabSelection}
              session={this.props.session}
              parent={this.state.parent}
            ></TechEfficiencyTrend>
          ) : null}

          {this.state.tabSelection == 'two' ? (
            <ComparisonChartsGrid
              props={this.props}
              parentCallback={this.props.parentCallback}
              removeFav={this.props.removeFav}
              tabSelection={this.state.tabSelection}
              mainTabSelection={this.state.mainTabSelection}
              session={this.props.session}
            />
          ) : null}
        </div>
      );
    } else {
      return this.state.isLoading == true ? (
        <Grid
          justify="center"
          style={{
            height: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </Grid>
      ) : null;
    }
  }
}

const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
});

export default withKeycloak(withStyles(styles)(TechEfficiency));
