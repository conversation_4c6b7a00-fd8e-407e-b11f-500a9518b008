import { QueryRenderer } from '@cubejs-client/react';
import {
  CircularProgress,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import moment from 'moment';
import React, { useState } from 'react';
import ReactApexChart from 'react-apexcharts';
import { makeStyles } from '@material-ui/core/styles';
import cubejsApi from 'src/utils/cubeUtils';
import { useHistory } from 'react-router';
import { getDashboardGraphQuery } from 'src/components/DashboardGraphQuery';
import MoreActions from 'src/components/MoreActions';
import { getSubHeader } from 'src/components/ViewGraphDetailsAction';
import ChartDialog from 'src/components/Dialog';
import PropTypes from 'prop-types';
const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  }
});
const dateFormatter = item => moment(item).format('MMM ');
const renderChart = Component => ({ resultSet, error }) =>
  (resultSet && <Component resultSet={resultSet} />) ||
  (error && error.toString()) || (
    <Paper square style={{ margin: 8 }}>
      <Grid
        justify="center"
        style={{
          height: 250,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <CircularProgress size={60} />
      </Grid>
    </Paper>
  );

const BarChartRenderer = ({
  chartId,
  removeFav,
  title,
  tech,
  techName,
  checkEmpty,
  parentCallback,
  handleClosePopup,
  realm,
  type,
  chartTitle,
  callFrom,
  isFrom,
  tabSelection,
  mainTabSelection
}) => {
  const classes = useStyles();
  const history = useHistory();
  const [open, setOpen] = useState(false);
  const [queryState, setQuery] = [
    getDashboardGraphQuery(
      chartId,
      tech,
      JSON.parse(localStorage.getItem('selectedStoreId'))[0],
      callFrom
    )
  ];

  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const barRender = ({ resultSet }) => {
    if (resultSet.seriesNames()[0] && resultSet.seriesNames()[0].title) {
      checkEmpty(false);
    } else {
      checkEmpty(true);
    }
    console.log('resultSetCube', resultSet);
    const customData = resultSet.series().map((s, index) => ({
      name: s.title,
      data:
        s.title == '  Actual Technician Efficiency ' && realm == 'haleyag'
          ? s.series.map(r => null)
          : s.series.map(r => r.value)
    }));

    const chartName = resultSet.seriesNames()[0]
      ? resultSet.seriesNames()[0].title
      : chartTitle;
    const options = {
      //colors: ['#5e2598', '#ff4f1a', '#388e3c', '#ffc40c'],
      colors:
        chartId == 1345
          ? ['#5e2598']
          : chartId == 1347
          ? ['#5e2598']
          : ['#dd4477'],
      chart: {
        zoom: {
          enabled: false
        },
        stacked: false,
        events: {
          dataPointSelection: function(event, chartContext, config) {
            var roDate = resultSet.loadResponse.data[config.dataPointIndex][
              'TechEfficiency.ro_date'
            ]
              ? resultSet.loadResponse.data[config.dataPointIndex][
                  'TechEfficiency.ro_date'
                ]
              : resultSet.loadResponse.data[config.dataPointIndex][
                  'EstimatedTechEfficiency.ro_date'
                ];
            if (chartId == '1345') {
              tech.includes('All')
                ? (roDate =
                    resultSet.loadResponse.data[config.dataPointIndex][
                      'TotalEstimatedTechEfficiencyWeekly.weekstartdate'
                    ])
                : (roDate =
                    resultSet.loadResponse.data[config.dataPointIndex][
                      'EstimatedTechEfficiencyWeekly.weekstartdate'
                    ]);
            }
            if (chartId == '1352') {
              tech.includes('All')
                ? (roDate =
                    resultSet.loadResponse.data[config.dataPointIndex][
                      'TotalEstimatedTechEfficiencySoldHrsWeekly.weekstartdate'
                    ])
                : (roDate =
                    resultSet.loadResponse.data[config.dataPointIndex][
                      'EstimatedTechEfficiencySoldHrsWeekly.weekstartdate'
                    ]);
            }
            if (chartId == '1347' || chartId == '1348') {
              tech.includes('All')
                ? (roDate =
                    resultSet.loadResponse.data[config.dataPointIndex][
                      'TotalTechEfficiency.month_year'
                    ])
                : (roDate =
                    resultSet.loadResponse.data[config.dataPointIndex][
                      'TechsEfficiency.month_year'
                    ]);
            }
            let data = {
              type: 'techniciantrendingcharts',
              reportType: chartId,
              month_year: roDate.substring(0, 7),
              chartName: chartTitle
            };

            if (typeof parentCallback == 'undefined') {
              history.push({
                pathname: '/TechnicianPerformance',
                state: {
                  tabselection: 'nine',
                  isAdvisorCharts: false,
                  isEfficiencyCharts: false,
                  isTechnicianCharts: true,
                  month_year: data.month_year,
                  type: 'techniciantrendingcharts',
                  isFrom: 'techniciantrendingcharts',
                  techNo: tech,
                  techName: techName,
                  chartType: 1,
                  parent: 'techniciantrendingcharts'
                }
              });
            } else {
              parentCallback(data);
            }
          }
        },
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        }
      },
      dataLabels: {
        enabled: false
      },

      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      markers: {
        size: 4,
        strokeWidth: 1,
        strokeOpacity: 0.7
      },
      stroke: {
        curve: 'straight',
        width: 2.5
      },
      // theme: {
      //   palette: 'palette8' // upto palette10
      // },
      yaxis: {
        title: {
          text: getYaxixLabels(chartName, chartId)
        },
        labels: {
          formatter: function(value) {
            if (chartId == 1250) {
              return (
                (
                  Math.round((value + Number.EPSILON) * 100) / 100
                ).toLocaleString() + ' %'
              );
            } else if (chartId == 1251 || chartId == 1249 || chartId == 1347) {
              return (
                (
                  Math.round((value + Number.EPSILON) * 100) / 100
                ).toLocaleString() + ' %'
              );
            } else
              return (
                Math.round((value + Number.EPSILON) * 100) / 100
              ).toLocaleString();
          }
        }
      },
      xaxis: {
        tooltip: {
          enabled: false
        },
        title: {
          text: chartId == '1345' || chartId == '1352' ? 'Week' : 'Month'
        },
        labels: {
          rotate: -45,
          rotateAlways: chartId == '1345' || chartId == '1352' ? true : false
        },
        categories: resultSet
          .categories()
          .map(c =>
            chartId == '1345' || chartId == '1352'
              ? moment(c.category).format('MMM DD')
              : moment(c.category).format('MMM YY')
          )
      },
      tooltip: {
        shared: false,
        intersect: true,
        x: {
          formatter: function(y, index) {
            return chartName.includes('  Flat Rate Hours With Tech Hours') ||
              chartId == '1345' ||
              chartId == '1347' ||
              chartId == '1348' ||
              chartId == '1352'
              ? resultSet
                  .categories()
                  .map(c =>
                    chartId == '1345' || chartId == '1352'
                      ? moment(c.category).format('MMM DD, YY')
                      : moment(c.category).format('MMM ’YY')
                  )[index.dataPointIndex]
              : resultSet
                  .categories()
                  .map(c => moment(c.category).format('MMM-YY'))[y - 1];
          }
        }
      }
    };
    if (chartId == 1251) {
      options.yaxis['max'] = 100;
      options.yaxis['tickAmount'] = 5;
    }
    if (chartId == 1252) {
      options.yaxis['max'] = 100;
      options.yaxis['tickAmount'] = 4;
    }
    const result = (
      // <Paper square style={{ margin: 4, padding: 8, cursor: 'pointer' }}>
      <Card
        bordered={false}
        style={{
          margin: isFrom == 'detail_page' ? 0 : 4,
          border: '1px solid #003d6b',
          borderRadius: window.location.pathname === '/MyFavorites' ? 0 : 4
        }}
      >
        <CardHeader
          title={chartTitle}
          style={
            typeof parentCallback == 'undefined'
              ? { padding: 10, paddingLeft: 20 }
              : { padding: 15 }
          }
          action={
            <MoreActions
              removeFavourite={removeFavourite}
              setActions={setactions}
              chartId={chartId}
              type={type}
              chartPopup={chartPopup}
              handleClose={handleClose}
              // favoritesDisabled={true}
            ></MoreActions>
          }
          subheader={getSubHeader(chartId)}
        ></CardHeader>

        <Divider />
        <CardContent className={classes.paperContainer}>
          <ReactApexChart
            options={options}
            series={customData}
            type={
              chartId == 1251 || chartId == 1347 || chartId == 1348
                ? 'bar'
                : 'line'
            }
            height={
              window.location.pathname == '/MyFavorites'
                ? 180
                : isFrom == 'source_page'
                ? 230
                : callFrom == 0 && (chartId == 1251 || chartId == 1252)
                ? 250
                : callFrom == 0 && (chartId == 1249 || chartId == 1250)
                ? 200
                : type == 'popup'
                ? 500
                : window.location.pathname == '/GraphDetailsView'
                ? 200
                : 285
            }
          />
        </CardContent>
      </Card>
      // </Paper>
    );

    return result;
  };
  const getYaxixLabels = (label, title) => {
    var yAxisLabel = '';
    switch (title) {
      case 1251:
      case 1347:
        yAxisLabel = 'Job Count%';
        break;
      case 1252:
      case 1348:
        yAxisLabel = 'Job Count';
        break;

      case 1249:
      case 1250:
        yAxisLabel = 'Percentage';
        break;

      case 1345:
      case 1352:
        yAxisLabel = 'Hours';
        break;
    }
    return yAxisLabel;
  };
  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?title=' + chartId + '?chartId=' + chartId,
        state: {
          tabSelection: mainTabSelection,
          selectedSubTab: tabSelection,
          SelectedLocation: window.location.pathname,
          tech: tech,
          techName: techName,
          chartType: chartId
        }
      });
    }
  };
  return (
    <>
      <QueryRenderer
        query={queryState}
        cubejsApi={cubejsApi()}
        render={renderChart(barRender)}
      />
      <ChartDialog
        open={open}
        chartId={chartId}
        chartType="techBarChartRenderer"
        tech={tech}
        techName={techName}
        checkEmpty={checkEmpty}
        realm={realm}
        parentCallback={parentCallback}
        handlePopupClose={handleClose}
      />
    </>
  );
};
BarChartRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default BarChartRenderer;
