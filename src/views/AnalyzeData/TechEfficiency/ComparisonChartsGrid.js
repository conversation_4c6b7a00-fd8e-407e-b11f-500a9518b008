import {
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import React, { useEffect, useState } from 'react';
import ColumnRenderer from './ColumnRenderer';
import {
  getLastThreeYears,
  getComparisonMonths,
  getLast13Months
} from 'src/utils/Utils';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import clsx from 'clsx';
import 'src/styles.css';
import { useSelector } from 'react-redux';
import { getTechnicianRevenueAll } from 'src/utils/hasuraServices';

var lodash = require('lodash');
const useStyles = makeStyles({
  formControl: {
    margin: 8
  },
  gridContainer: {
    padding: 8
  },
  paperContainer: {
    cursor: 'pointer',
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    padding: 5
  },
  alertInfo: {
    fontSize: 14,
    fontWeight: 'bold',
    width: 'inherit'
  }
});
const ComparisonChartsGrid = ({ props, removeFav, parentCallback }) => {
  const classes = useStyles();
  /* const [queryMonth, setQueryMonth] = useState(
    moment(new Date().setMonth(new Date().getMonth() - 1)).format('YYYY-MM')
  );
  const [queryMonth2, setQueryMonth2] = useState(
    moment(new Date().setMonth(new Date().getMonth() - 2)).format('YYYY-MM')
  );*/
  const realm = props.keycloak.realm;
  let mon1 =
    typeof props.comparisonMonth2 != 'undefined' && props.comparisonMonth2 != ''
      ? props.comparisonMonth2
      : getComparisonMonths()[0];
  let mon2 =
    typeof props.comparisonMonth1 != 'undefined' && props.comparisonMonth1 != ''
      ? props.comparisonMonth1
      : getComparisonMonths()[1];
  const [techChartData, setTechChartData] = useState([]);
  const [queryMonth, setQueryMonth] = useState(mon2);
  const [queryMonth2, setQueryMonth2] = useState(mon1);
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  const session = useSelector(state => state.session);
  useEffect(() => {
    if (
      typeof props.comparisonMonth1 != 'undefined' &&
      props.comparisonMonth1 != ''
    ) {
      setQueryMonth(props.comparisonMonth1);
    }
    if (
      typeof props.comparisonMonth2 != 'undefined' &&
      props.comparisonMonth2 != ''
    ) {
      setQueryMonth2(props.comparisonMonth2);
    }
  }, [session.serviceAdvisor, props.comparisonMonth1, props.comparisonMonth2]);
  useEffect(() => {
    setTechChartData([]);
    getTechnicianRevenueAll(queryMonth, queryMonth2, 'All', result => {
      if (
        result.data
          .statelessDbdPeopleMetricsTechnicianGetTechRevenueAndHoursByNameAll
          .techRevenueAndHoursByNameAlls
      ) {
        var data =
          result.data
            .statelessDbdPeopleMetricsTechnicianGetTechRevenueAndHoursByNameAll
            .techRevenueAndHoursByNameAlls;
        setTechChartData(JSON.parse(data[0].jsonData));
      }
    });
  }, [queryMonth, queryMonth2, session.storeSelected]);
  const [charts] = useState([
    'revenue',
    'soldhours',
    'jobcount',
    'jobperc'
    // 'techefficiencyoverall',
    // 'estimatedtechefficiency',

    // 'nonzeroflatratehrsandnonzeroactualhrs',
    // 'nonzeroflatratehrsandzeroactualhrs',
    // 'zeroflatratehrsandnonzeroactualhrs',
    // 'zeroflatratehrsandzeroactualhrs',

    // 'jobcountflathrsandactualhrs',
    // 'jobcountflatratehrsandzeroactualhrs',
    // 'jobcountzeroflatratehrsandnonzeroactualhrs',
    // 'jobcountzeroflatratehrsandzeroactualhrs'

    // 'actualhrszeroflatratehrs',
    // 'flatratehrsnonzeroactualhrs'

    // 'flatratehrsnoactualhrs',

    // 'actualhoursnonzero'
  ]);
  const handleMonthchange = event => {
    setQueryMonth(event.target.value);
  };
  const handleClosePopup = value => {
    console.log('state===handleClosePopup');
  };
  const handleMonthchange2 = event => {
    setQueryMonth2(event.target.value);
  };
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'Technician Efficiency-Month Comparison' &&
      item.parentId == null
  );

  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');

  return (
    <Grid container className={classes.container} spacing={12}>
      {realm == 'haleyag' ? (
        <Alert severity="info" className={classes.alertInfo}>
          No data found for actual hours punched by Technicians{' '}
        </Alert>
      ) : null}
      <Grid container spacing={12}>
        <Grid item xs={6} justify="flex-start" className={classes.container}>
          <Paper square>
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel
                htmlFor="outlined-age-native-simple"
                margin="dense"
                color="primary"
              >
                Month
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth}
                onChange={handleMonthchange}
              >
                {/* {getLast13Months().map(val => (
                  <MenuItem value={val}>{val}</MenuItem>
                ))} */}
                {localStorage.getItem('versionFlag') == 'TRUE'
                  ? getLastThreeYears().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))
                  : getLast13Months().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))}
              </Select>
            </FormControl>
          </Paper>
        </Grid>
        <Grid item xs={6} className={classes.container}>
          <Paper square justify="center">
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                Month
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth2}
                onChange={handleMonthchange2}
              >
                {localStorage.getItem('versionFlag') == 'TRUE'
                  ? getLastThreeYears().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))
                  : getLast13Months().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))}
              </Select>
            </FormControl>
          </Paper>
        </Grid>
      </Grid>

      {charts.map((val, index) => {
        if (
          val === 'revenue' ||
          val === 'soldhours' ||
          val == 'jobperc' ||
          val == 'jobcount'
        ) {
          return (
            <Grid
              item
              xs={12}
              className={clsx(classes.container, 'diagram-section')}
            >
              <ColumnRenderer
                month1={queryMonth}
                month2={queryMonth2}
                parentCallback={parentCallback}
                handleClosePopup={handleClosePopup}
                datatype={val}
                callFrom={0}
                chartId={orderedData[index].chartId}
                removeFav={removeFav}
                session={session}
                techChartData={techChartData}
                parent={'techefficiency'}
              />
            </Grid>
          );
        } else {
          return (
            <Grid
              item
              xs={6}
              justify="flex-start"
              className={clsx(classes.container, 'diagram-section')}
            >
              <ColumnRenderer
                // month1={(typeof props.comparisonMonth1 != 'undefined' && props.comparisonMonth1 != 'undefined') ? props.comparisonMonth1 : queryMonth}
                // month2={(typeof props.comparisonMonth2 != 'undefined' && props.comparisonMonth2 != 'undefined') ? props.comparisonMonth2 :queryMonth2}
                month1={queryMonth}
                month2={queryMonth2}
                handleClosePopup={handleClosePopup}
                parentCallback={parentCallback}
                datatype={val}
                chartId={orderedData[index].chartId}
                chartName={orderedData[index].chartName}
                removeFav={removeFav}
                session={session}
                techChartData={techChartData}
                parent={'techefficiency'}
              />
            </Grid>
          );
        }
      })}
    </Grid>
  );
};

export default ComparisonChartsGrid;
