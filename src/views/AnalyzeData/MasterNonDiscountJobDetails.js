import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { getDrillDownDataForMasterDiscountJobs } from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { MasterDetailModule } from '@ag-grid-enterprise/master-detail';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getLast13Months } from 'src/utils/Utils';

ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  MasterDetailModule,
  MenuModule,
  ColumnsToolPanelModule,
  AllModules
]);
class MasterNonDiscountJobDetails extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = dateFormat(this.props.monthYear);
    var flag = 1;
    var noDataFlag = 0;
    this.state = {
      showCharts: false,
      tabSelection: 'one',
      queryMonth: initialQueryMonth,
      selectedMonthYear: initialQueryMonth,
      isLoading: true,
      flag: flag,
      noDataFlag: noDataFlag,
      rawGridApi: {},
      gridApi: {},
      modules: [
        ClientSideRowModelModule,
        MasterDetailModule,
        MenuModule,
        ColumnsToolPanelModule,
        AllModules
      ],
      columnDefs: [
        {
          headerName: 'Month-Year',
          field: 'month_year',
          chartDataType: 'category',
          cellRenderer: 'agGroupCellRenderer'
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          chartDataType: 'category'
        },
        {
          headerName: 'Discount Description',
          field: 'disdesc',
          chartDataType: 'category'
        },
        {
          headerName: 'Discount Level',
          field: 'dislevel',
          chartDataType: 'category'
        },
        {
          headerName: 'Labor Discount',
          field: 'labordiscount',
          chartDataType: 'category',
          cellStyle: this.cellStyles(),
          valueFormatter: this.formatCellValue
        },
        {
          headerName: 'Parts Discount',
          field: 'partsdiscount',
          chartDataType: 'category',
          cellStyle: this.cellStyles(),
          valueFormatter: this.formatCellValue
        }
      ],
      defaultColDef: { flex: 1 },
      detailCellRendererParams: {
        detailGridOptions: {
          columnDefs: [
            {
              headerName: 'RO Number',
              filter: 'agNumberColumnFilter',
              field: 'ronumber',
              chartDataType: 'category',
              showRowGroup: false,

              cellRenderer: 'agGroupCellRenderer',
              cellRendererParams: {
                innerRenderer: function(params) {
                  return `<a style="color: #000000" href="https://verify.fixedops.cc/FOC3_Searchbyro/ag-grid.html?ronumber=${params.value}" target="_blank"  >${params.value}</a>`;
                }
              },
              cellStyle: function() {
                return {
                  color: '#000000',
                  fontWeight: 'bold'
                };
              }
            },
            { headerName: 'Discount Id', field: 'disdiscountid' },
            { headerName: 'Discount Level', field: 'dislevel' },
            {
              headerName: 'Labor Sale',
              field: 'lbrsale',
              cellStyle: this.cellStyles(),
              valueFormatter: this.formatCellValue
            },
            { headerName: 'Discount Description', field: 'disdesc' },
            {
              headerName: 'Labor Discount',
              field: 'labordiscount',
              cellStyle: this.cellStyles(),
              valueFormatter: this.formatCellValue
            },
            {
              headerName: 'Parts Discount',
              field: 'partsdiscount',
              cellStyle: this.cellStyles(),
              valueFormatter: this.formatCellValue
            },
            {
              headerName: 'Labor Sequence Number',
              field: 'lbrsequenceno',
              cellStyle: this.cellStyles()
            },
            { headerName: 'Discount Description', field: 'disdesc' },
            {
              headerName: 'Labor Discount',
              field: 'labordiscount',
              cellStyle: this.cellStyles(),
              valueFormatter: this.formatCellValue
            },
            {
              headerName: 'Parts Discount',
              field: 'partsdiscount',
              cellStyle: this.cellStyles(),
              valueFormatter: this.formatCellValue
            },
            {
              headerName: 'Labor Sequence Number',
              field: 'lbrsequenceno',
              cellStyle: this.cellStyles()
            },
            { headerName: 'Labor Line Code', field: 'lbrlinecode' },
            { headerName: 'Opcategory', field: 'opcategory' },
            { headerName: 'Opsubcategory', field: 'opsubcategory' },
            { headerName: 'Labor Type', field: 'lbrlabortype' },
            { headerName: 'Labor Pay Type', field: 'lbr_paytype' },
            { headerName: 'Labor Opcode', field: 'lbropcode' },
            { headerName: 'Labor Opcode Description', field: 'lbropcodedesc' }
          ],
          defaultColDef: { flex: 1 },
          onGridReady: function(params) {
            if (noDataFlag == 1) {
              params.api.showNoRowsOverlay();
            }
            params.api.setDomLayout('autoHeight');
            params.api.sizeColumnsToFit();
            var allColumnIds = [];
            params.columnApi.getAllColumns().forEach(function(column) {
              allColumnIds.push(column.colId);
            });
            params.columnApi.autoSizeColumns(allColumnIds);
          }
        },
        getDetailRowData: function(params) {
          if (params.data.non_discount_job_details.length != 1) {
            params.successCallback(params.data.non_discount_job_details);
            noDataFlag = 0;
          } else {
            params.successCallback(params.data.non_discount_job_details);
            noDataFlag = 1;
          }
        },
        template:
          '<div style=" background-color: #edf6ff; padding: 20px; box-sizing: border-box;">' +
          '  <div style="height: 10%; padding: 2px; font-weight: bold;">Non Discounted Jobs </div>' +
          '  <div ref="eDetailGrid" style="height: 90%;"></div>' +
          '</div>'
      },
      getRowHeight: function(params) {
        if (params.node && params.node.detail) {
          var offset = 80;
          var allDetailRowHeight =
            params.data.non_discount_job_details.length *
            params.api.getSizesForCurrentTheme().rowHeight;
          var gridSizes = params.api.getSizesForCurrentTheme();
          return allDetailRowHeight + gridSizes.headerHeight + offset;
        }
      },
      getRowHeight: function(params) {
        if (params.node && params.node.detail) {
          var offset = 80;
          var allDetailRowHeight =
            params.data.non_discount_job_details.length *
            params.api.getSizesForCurrentTheme().rowHeight;
          var gridSizes = params.api.getSizesForCurrentTheme();
          return allDetailRowHeight + gridSizes.headerHeight + offset;
        }
      },
      rowData: [],
      defaultColDef: {
        enableValue: true,
        enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: true
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right'
    };
  };
  formatCellValue = params => {
    if (params.value != null) {
      return '$' + parseFloat(params.value.toFixed(2)).toLocaleString();
    }
  };
  onFirstDataRendered = params => {
    setTimeout(function() {
      params.api.getDisplayedRowAtIndex(0).setExpanded(true);
    }, 0);
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData(this.state.queryMonth);
  };

  getAgGridData(queryMonth) {
    getDrillDownDataForMasterDiscountJobs(queryMonth, result => {
      this.setState({ isLoading: false });
      if (result.data.foc3_vw_master_discounts_job_details_by_discount_id) {
        this.setState({
          rowData:
            result.data.foc3_vw_master_discounts_job_details_by_discount_id
        });
      }
      this.getMonthYear();
    });
  }

  viewNonDiscountJobDetails = flag => {
    let data = {
      type: 'discountjoblevel',
      month_year: this.state.queryMonth
    };
    this.props.parentCallback(data);
  };

  handleMonthYearChange = event => {
    let monthYear = event.target.value;
    this.setState({ selectedMonthYear: monthYear });
    setTimeout(
      function() {
        this.getAgGridData(monthYear);
      }.bind(this),
      100
    );
  };

  getMonthYear = () => {
    var table = [];
    this.setState({
      discountMonthYear: getLast13Months()
    });
  };

  render() {
    const { classes } = this.props;
    return (
      <div>
        <div>
          <Paper square style={{ marginLeft: 8, marginRight: 8 }}>
            <Toolbar>
              <Grid container justify="flex-start" style={{ padding: '5px' }}>
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={classes.formControl}
                >
                  <InputLabel
                    htmlFor="outlined-age-native-simple"
                    margin="dense"
                  >
                    Month
                  </InputLabel>
                  <Select
                    variant="outlined"
                    label="Group By"
                    name="group-by-type"
                    value={this.state.selectedMonthYear}
                    onChange={this.handleMonthYearChange}
                  >
                    <MenuItem value={this.state.selectedMonthYear}>
                      {this.state.selectedMonthYear}
                    </MenuItem>
                    {typeof this.state.discountMonthYear != 'undefined' &&
                      Object.values(this.state.discountMonthYear).map(item => (
                        <MenuItem value={item}>{item}</MenuItem>
                      ))}
                  </Select>
                </FormControl>
                <Button
                  variant="contained"
                  style={{ margin: 10, marginLeft: 'auto' }}
                  icon="fullscreen-exit"
                  onClick={() => this.viewNonDiscountJobDetails()}
                  size="default"
                  color="primary"
                >
                  View Discounted Jobs
                </Button>
              </Grid>
            </Toolbar>
          </Paper>
        </div>
        {this.state.isLoading && (
          <div
            style={{
              display: this.state.tabSelection != 'one' ? 'none' : 'block'
            }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Fetching raw data...
              </Typography>
            </Box>
          </div>
        )}

        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: '370px',
            width: '100%',
            margin: 8,
            display: this.state.tabSelection == 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            enableRangeSelection={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            enableCharts={true}
            modules={this.state.modules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            masterDetail={true}
            embedFullWidthRows={true}
            detailCellRendererParams={this.state.detailCellRendererParams}
            onFirstDataRendered={this.onFirstDataRendered.bind(this)}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            getRowHeight={this.state.getRowHeight}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  }
});

export default withStyles(styles)(MasterNonDiscountJobDetails);
