import {
  Card,
  CardContent,
  CardHeader,
  CircularProgress,
  Divider,
  Grid,
  Paper
} from '@material-ui/core';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import React, { useEffect, useState } from 'react';
import ReactHtmlParser from 'react-html-parser';

import { makeStyles } from '@material-ui/styles';
import { useHistory } from 'react-router';
import {
  getChartDrillDown,
  getChartName,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';

import ChartDialog from 'src/components/Dialog';
import {
  getAddOnVsNonAddOnRevenuePercent,
  getDiscountByServiceAdvisor,
  getDiscountByServiceAdvisorOpcategory,
  getDrillGraphDataForMonthWiseComparisonLabor,
  getDrillGraphDataForMonthWiseComparisonParts
} from 'src/utils/hasuraServices';
import { getColorScheme, getComparisonMonths } from 'src/utils/Utils';
import MoreActions from '../../../components/MoreActions';
import PropTypes from 'prop-types';

import moment from 'moment';
import NoDataToDisplay from 'highcharts/modules/no-data-to-display';
import { useDispatch, useSelector } from 'react-redux';
import { SettingsRemoteOutlined } from '@material-ui/icons';
NoDataToDisplay(Highcharts);
require('highcharts-grouped-categories')(Highcharts);

var lodash = require('lodash');
const $ = require('jquery');
Highcharts.setOptions({
  lang: {
    thousandsSep: ','
  }
});
const useStyles = makeStyles(theme => ({
  active: {
    color: '#fff',
    fontWeight: theme.typography.fontWeightMedium,
    border: '1px solid ' + theme.palette.primary.main,
    backgroundColor: theme.palette.primary.main,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  title: {
    lineHeight: 1.5
  },
  cardRoot: {
    boxShadow: 'none',
    borderRadius: 5
  },
  container: {
    cursor: 'pointer',
    borderRadius: 5,
    border: '1px solid #003d6b'
  }
}));
const ColumnRenderer = ({
  isPartsCharts,
  removeFav,
  datatype,
  month1,
  month2,
  type,
  handleClosePopup,
  parentCallback,
  chartId,
  isFrom,
  tabSelection,
  workmixChartData
}) => {
  // console.log('month1', isFrom, datatype, chartId, month1, month2);
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [isLoading, setLoading] = useState(true);
  const [optionsHighChart, setOptionsHighChart] = useState({});

  const [seriesArray, setSeriesArray] = useState([]);
  const session = useSelector(state => state.session);
  useEffect(() => {
    setLoading(true);

    if (isFrom == 'comparisonChartsGrid') {
      if (workmixChartData.length > 0) {
        const transformedData = transformWorkmixData(workmixChartData);
        //console.log('transformedData===', transformedData);
        const chartData = transformedData[chartId];

        if (chartData) {
          var data = chartData.filter(
            column =>
              column.opcategory == 'REPAIR' ||
              column.opcategory == 'MAINTENANCE' ||
              column.opcategory == 'COMPETITIVE'
          );
          console.log('laborWorkmixChartData', data);
          processData(data, datatype);
        }
      }
    } else {
      console.log('laborWorkmixChartData');
      setLoading(true);
      getDataforColumnChart();
    }
  }, [datatype, workmixChartData]);
  const removeFavourite = val => {
    console.log('val', val);
    removeFav(val);
  };
  const history = useHistory();
  let chartLoc;
  if (isPartsCharts) {
    chartLoc = 'PartsWorkMixAnalysis';
  } else {
    chartLoc = 'LaborWorkMixAnalysis';
  }
  if (typeof chartId == 'undefined') {
    chartId = history.location.search
      .split('?chartId=')
      .pop()
      .split('?')[0];
  }
  if (datatype == '') {
    month1 = month1 ? month1 : getComparisonMonths()[1];
    month2 = month2 ? month2 : getComparisonMonths()[0];
    datatype = getDataType(Number(chartId));
  }
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  const getDataforColumnChart = () => {
    if (isPartsCharts) {
      getDrillGraphDataForMonthWiseComparisonParts(
        [month2, month1, datatype],
        result => {
          if (
            result.data
              .statelessDbdPartsWorkmixGetWorkmixChartPartsMonthlyComparison
              .nodes
          ) {
            processData(
              result.data
                .statelessDbdPartsWorkmixGetWorkmixChartPartsMonthlyComparison
                .nodes,
              datatype
            );
          } else {
            setLoading(false);
          }
        }
      );
    } else if (
      datatype == 'addonvsnonaddonrevenue%' ||
      datatype == 'addonrevenue'
    ) {
      var queryMonth = [month1, month2];
      getAddOnVsNonAddOnRevenuePercent(queryMonth, result => {
        if (
          result.data
            .statelessDbdAddonsChartsAddonsGetChartsAddonsServiceadvisors.nodes
        ) {
          processData(
            result.data
              .statelessDbdAddonsChartsAddonsGetChartsAddonsServiceadvisors
              .nodes,
            datatype
          );
        } else {
          setLoading(false);
        }
      });
    } else if (datatype == 'discountserviceadv') {
      var queryMonth = [month1, month2];
      getDiscountByServiceAdvisor(queryMonth, result => {
        if (
          result.data.statelessDbdDiscountsGetDiscountsByServiceAdvisors.nodes
        ) {
          var intArray = parsedArray(
            result.data.statelessDbdDiscountsGetDiscountsByServiceAdvisors
              .nodes,
            datatype
          );
          processData(intArray, datatype);
        } else {
          setLoading(false);
        }
      });
    } else if (datatype == 'discountserviceadvOpcat') {
      var queryMonth = [month1, month2];

      getDiscountByServiceAdvisorOpcategory(month1, month2, result => {
        if (
          result.data
            .statelessDbdDiscountsGetDiscountJobcountPercentageServiceAdvisorOpcategory
            .discountJobcountPercentageServiceAdvisorOpcategories
        ) {
          var intArray = parsedArray(
            result.data
              .statelessDbdDiscountsGetDiscountJobcountPercentageServiceAdvisorOpcategory
              .discountJobcountPercentageServiceAdvisorOpcategories,
            datatype
          );

          processData(intArray, datatype);
        } else {
          setLoading(false);

          var intArray = parsedArray(
            result.data
              .statelessDbdDiscountsGetDiscountJobcountPercentageServiceAdvisorOpcategory
              .discountJobcountPercentageServiceAdvisorOpcategories,
            datatype
          );

          processData(intArray, datatype);
        }
        // } else {
        //   setLoading(false);
        // }
      });
    } else {
      console.log('datatype', datatype);
      getDrillGraphDataForMonthWiseComparisonLabor(
        [month1, month2, datatype],
        result => {
          if (
            result.data
              .statelessDbdLaborWorkmixGetWorkmixChartsMonthlyComparison.nodes
          ) {
            var data = result.data.statelessDbdLaborWorkmixGetWorkmixChartsMonthlyComparison.nodes.filter(
              column =>
                column.opcategory == 'REPAIR' ||
                column.opcategory == 'MAINTENANCE' ||
                column.opcategory == 'COMPETITIVE'
            );
            processData(data, datatype);
            // processData(
            //   result.data
            //     .statelessDbdLaborWorkmixGetWorkmixChartsMonthlyComparison
            //     .nodes,
            //   datatype
            // );
          } else {
            setLoading(false);
          }
        }
      );
    }
  };

  const parsedArray = filtererdArray => {
    let intArray = [];
    if (datatype == 'discountserviceadv') {
      var res = filtererdArray.map(v => {
        intArray.push({
          closeddate: v.closeddate,
          monthYear: v.monthYear,
          serviceadvisor: v.serviceadvisor,
          totallabordiscount: parseFloat(v['totallabordiscount']) || 0,
          totalpartsdiscount: parseFloat(v['totalpartsdiscount']) || 0,
          advisorName: v.advisorName ? v.advisorName : ''
        });
      });
    } else if (datatype == 'discountserviceadvOpcat') {
      var res = filtererdArray.map(v => {
        intArray.push({
          monthYear: v.monthYear,
          serviceadvisor: v.serviceadvisor,
          jobcountpercentagecompetitive:
            parseFloat(v['jobcountpercentagecompetitive']) || 0,
          jobcountpercentagemaintenance:
            parseFloat(v['jobcountpercentagemaintenance']) || 0,
          jobcountpercentagerepair:
            parseFloat(v['jobcountpercentagerepair']) || 0,
          advisorName: v.advisorName ? v.advisorName : ''
        });
      });
    }
    return intArray;
  };

  function getDataType(chartId) {
    var datatype = '';
    switch (chartId) {
      case 1121:
        datatype = 'addonvsnonaddonrevenue%';
        break;
      case 1122:
        datatype = 'addonrevenue';
        break;
      case 1114:
        datatype = 'discountserviceadv';
        break;
      case 1125:
        datatype = 'discountserviceadvOpcat';
        break;
      case 1259:
        datatype = 'soldhours';
        break;
      case 1310:
      case 1260:
        datatype = 'workmix';
        break;
      case 1311:
      case 1261:
        datatype = 'jobcount';
        break;
      case 1262:
        datatype = 'elr';
        break;
      case 1313:
      case 1263:
        datatype = 'grossprofitpercentage';
        break;
      case 1309:
        datatype = 'partscost';
        break;
      case 1312:
        datatype = 'markup';
        break;
    }
    return datatype;
  }
  const getTickInterval = chartId => {
    var label;
    if (chartId == 1114) {
      label = 500;
    }
    if (chartId == 1125) {
      label = 10;
    }
    return label;
  };
  const YaxisLabels = (value, dataType) => {
    var label = '';

    if (
      dataType == 'addonrevenue' ||
      dataType == 'discountserviceadv' ||
      dataType == 'partscost'
    ) {
      label = '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else if (
      dataType == 'addonvsnonaddonrevenue%' ||
      dataType == 'discountserviceadvOpcat' ||
      dataType == 'grossprofitpercentage'
    ) {
      label = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%';
    } else if (dataType == 'elr') {
      label = '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      label = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
    return label;
  };
  const YaxisTitle = datatype => {
    var text = '';
    switch (datatype) {
      case 'addonrevenue':
        text = 'Add On Revenue';
        break;
      case 'addonvsnonaddonrevenue%':
        text = 'Revenue %';
        break;
      case 'discountserviceadv':
        text = 'Discounts';
        break;
      case 'discountserviceadvOpcat':
        text = 'Job Count %';
        break;
    }
    return text;
  };
  const fnParseArray = filtererdArray => {
    let integerArray = [];

    var res = filtererdArray.map(value => {
      integerArray.push({
        lbropcode: value.lbropcode,
        mon1: parseFloat(value['mon1']) || 0,
        mon2: parseFloat(value['mon2']) || 0,
        opcategory: value.opcategory,
        lbropcodedesc: value.lbropcodedesc
      });
    });
    return integerArray;
  };
  const formatChartValue = (dataType, value) => {
    if (
      dataType == 'addonrevenue' ||
      dataType == 'discountserviceadv' ||
      dataType == 'partscost' ||
      dataType == 'elr'
    ) {
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return value;
    }
  };
  const processData = (data, dataType) => {
    var data = data;

    data = lodash.sortBy(data, 'opcategory');
    if (
      dataType != 'discountserviceadv' &&
      dataType != 'discountserviceadvOpcat'
    ) {
      data = data.filter(item =>
        ['REPAIR', 'MAINTENANCE', 'COMPETITIVE'].includes(item.opcategory)
      );
    }
    if (dataType == 'addonvsnonaddonrevenue%') {
      var resultentData = formatDataWithOutZero(data, dataType, month1, month2);
      var orderedData1 = lodash
        .chain(resultentData)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => parseFloat(data.addonrevenuepercentage));
        })
        .value();

      var orderedData2 = lodash
        .chain(resultentData)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => parseFloat(data.nonaddonrevenuepercentage));
        })
        .value();

      var opcodesArray = lodash
        .chain(resultentData)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.serviceadvisor);
        })
        .map(val => lodash.uniqBy(val))
        .value();
    } else if (dataType == 'addonrevenue') {
      var resultentData = formatDataWithOutZero(data, dataType, month1, month2);
      var orderedData1 = lodash
        .chain(resultentData)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => parseFloat(data.addonlbrsale));
        })
        .value();

      var orderedData2 = lodash
        .chain(resultentData)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => parseFloat(data.addonpartsrevenue));
        })
        .value();

      var opcodesArray = lodash
        .chain(resultentData)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.serviceadvisor);
        })
        .map(val => lodash.uniqBy(val))
        .value();
    } else if (dataType == 'discountserviceadv') {
      data = lodash.orderBy(data, ['serviceadvisor'], ['desc']);
      var monthYearArr = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.monthYear);
        })
        .map(val => lodash.uniqBy(val))
        .value();

      var orderedData1 = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.totallabordiscount);
        })
        .value();

      var orderedData2 = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.totalpartsdiscount);
        })
        .value();

      var opcodesArray = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.advisorName);
        })
        .map(val => lodash.uniqBy(val))
        .value();
      var greaterMonth =
        data.length > 0 ? checkForMonthYear(month1, month2, data) : false;

      if (greaterMonth) {
        if (opcodesArray.length > 0 && opcodesArray[1] != undefined) {
          var key = opcodesArray[0];
          opcodesArray[0] = opcodesArray[1];
          opcodesArray[1] = key;
        }
        if (orderedData1.length > 0 && orderedData1[1] != undefined) {
          var key1 = orderedData1[0];
          orderedData1[0] = orderedData1[1];
          orderedData1[1] = key1;
        }
        if (orderedData2.length > 0 && orderedData2[1] != undefined) {
          var key2 = orderedData2[0];
          orderedData2[0] = orderedData2[1];
          orderedData2[1] = key2;
        }
      }
    } else if (dataType == 'discountserviceadvOpcat') {
      var monthYearArr = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.monthYear);
        })
        .map(val => lodash.uniqBy(val))
        .value();
      data = lodash.orderBy(data, ['serviceadvisor'], ['desc']);
      var lodashMonth = data.length > 0 ? data[0].monthYear : null;
      var orderedData1 = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.jobcountpercentagecompetitive);
        })
        .value();

      var orderedData2 = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.jobcountpercentagemaintenance);
        })
        .value();
      var orderedData3 = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.jobcountpercentagerepair);
        })
        .value();
      var opcodesArray = lodash
        .chain(data)
        .groupBy('monthYear')
        .map(value => {
          return value.map(data => data.advisorName);
        })
        .map(val => lodash.uniqBy(val))
        .value();

      // var greaterMonth1 = checkForMonthYear(month1, month2, data);
      var greaterMonth =
        data.length > 0 ? checkForMonthYear(month1, month2, data) : false;
      if (lodashMonth != null && lodashMonth != month2 && greaterMonth) {
        if (opcodesArray.length > 0 && opcodesArray[1] != undefined) {
          var key = opcodesArray[0];
          opcodesArray[0] = opcodesArray[1];
          opcodesArray[1] = key;
        }
        if (opcodesArray.length > 0 && orderedData1[1] != undefined) {
          var key1 = orderedData1[0];
          orderedData1[0] = orderedData1[1];
          orderedData1[1] = key1;
        }
        if (opcodesArray.length > 0 && orderedData2[1] != undefined) {
          var key2 = orderedData2[0];
          orderedData2[0] = orderedData2[1];
          orderedData2[1] = key2;
        }
        if (opcodesArray.length > 0 && orderedData3[1] != undefined) {
          var key3 = orderedData3[0];
          orderedData3[0] = orderedData3[1];
          orderedData3[1] = key3;
        }
      }
    } else {
      var data = fnParseArray(data);
      data = data.filter(function(el) {
        return el.lbropcode != '';
      });
      var orderedData1 = lodash
        .chain(data)
        .groupBy('opcategory')
        .map(value => {
          return value.map(data =>
            isPartsCharts ? Number(data.mon1) : Number(data.mon2)
          );
        })
        .value();
      var orderedData2 = lodash
        .chain(data)
        .groupBy('opcategory')
        .map(value => {
          return value.map(data =>
            isPartsCharts ? Number(data.mon2) : Number(data.mon1)
          );
        })
        .value();

      var opcodesArray = lodash
        .chain(data)
        .groupBy('opcategory')
        .map(value => {
          return value.map(data => data.lbropcode);
        })
        .map(val => lodash.uniqBy(val))
        .value();

      var opCategory = lodash
        .chain(data)
        .groupBy('opcategory')
        .map(value => {
          return value.map(data => data.opcategory);
        })
        .map(val => lodash.uniqBy(val))
        .value();
      var opcodeDesc = lodash
        .chain(data)
        .groupBy('lbropcode')
        .map(value => {
          return value.map(data => data.lbropcodedesc);
        })
        .map(val => lodash.uniqBy(val))
        .value();
    }
    if (datatype == 'discountserviceadvOpcat') {
      //var greaterMonth = checkForMonthYear(month1, month2);
      var series = lodash.concat(
        [lodash.concat(orderedData1[0], orderedData1[1])],
        [lodash.concat(orderedData2[0], orderedData2[1])],
        [lodash.concat(orderedData3[0], orderedData3[1])]
      );

      setSeriesArray(series);
    } else if (datatype == 'discountserviceadv') {
      var series = lodash.concat(
        [lodash.concat(orderedData1[0], orderedData1[1])],
        [lodash.concat(orderedData2[0], orderedData2[1])]
      );

      setSeriesArray(series);
    } else {
      var series = '';
      orderedData1[2]
        ? (series = lodash.concat(
            [lodash.concat(orderedData1[0], orderedData1[1], orderedData1[2])],
            [lodash.concat(orderedData2[0], orderedData2[1], orderedData2[2])]
          ))
        : (series = lodash.concat(
            [lodash.concat(orderedData1[0], orderedData1[1])],
            [lodash.concat(orderedData2[0], orderedData2[1])]
          ));

      setSeriesArray(series);
    }
    var month3 = month2;
    if (monthYearArr && monthYearArr.length == 1) {
      month2 = monthYearArr[0][0];
    }

    const chartOptions = {
      colors:
        datatype == 'discountserviceadvOpcat'
          ? getColorScheme(21)
          : getColorScheme(1),
      chart: {
        // events: {
        //   load: function() {

        //     const width = this.xAxis[0].width,
        //       y = this.xAxis[0].height + this.xAxis[0].top,
        //       x = this.xAxis[0].left;
        //       console.log("chart1",x,y,width);
        //     this.renderer.rect(x, y+70, width , 25).attr({
        //       fill: '#003d6b'
        //     }).add()
        //   }
        // render: function() {
        //   console.log("chart1",this);
        //   const chart = this,

        //     xAxisBox = (chart.axes[0].gridGroup.getBBox());

        //   chart.renderer.rect(xAxisBox.y+28.5/*position on X-axis*/ , chart.plotHeight + xAxisBox.y+23 /*position on Y-axis*/ , chart.plotWidth /*width*/ , 25 /*height*/)
        //     .attr({
        //       'stroke-width': 0,
        //       stroke: '#003d6b',
        //       fill: '#003d6b',
        //       zIndex: 3
        //     })
        //     .add();
        // }
        // },
        style: {
          fontFamily: 'Roboto'
        },
        className: 'chartid-' + chartId,
        type:
          datatype == 'discountserviceadv' ||
          datatype == 'discountserviceadvOpcat'
            ? 'bar'
            : 'column'
      },
      title: {
        text: ''
      },
      lang: {
        noData: 'No data to display'
      },
      noData: {
        style: {
          fontWeight: 'bold',
          fontSize: '13px'
          // color: '#303030'
        }
      },

      plotOptions: {
        series: {
          states: {
            inactive: {
              enabled: false // Disable the inactive state to prevent opacity changes
            }
          },
          cursor: 'pointer',

          pointWidth:
            datatype == 'discountserviceadv' ||
            datatype == 'discountserviceadvOpcat'
              ? 6
              : undefined,
          point: {
            events: {
              click: function() {
                if (this.category.name && this.series.name) {
                  setTab();
                  let data = {
                    type: 'comparisonchart',
                    opcode: this.category.name,
                    month: this.series.name,
                    comparisonMonth1: isFrom == 'details' ? month2 : month1,
                    comparisonMonth2: isFrom == 'details' ? month1 : month2,
                    month_year: [month1, month2],
                    workmixreportType: datatype,
                    opCategory: this.category.parent.name[0]
                  };
                  if (
                    datatype == 'addonvsnonaddonrevenue%' ||
                    datatype == 'addonrevenue' ||
                    datatype == 'discountserviceadv' ||
                    datatype == 'discountserviceadvOpcat'
                  ) {
                    let monthYear = this.category.parent.name.split('-');
                    let date = moment(monthYear[1], 'YY');
                    let year = date.format('YYYY');
                    let month = moment()
                      .month(monthYear[0])
                      .format('MM');
                    let targetMonthYear = year + '-' + month;

                    history.push({
                      pathname: '/AnalyzeData',
                      prevPath:
                        window.location.pathname == '/GraphDetailsView'
                          ? window.location.pathname + '?chartId=' + chartId
                          : window.location.pathname,
                      state: {
                        chartId: chartId,
                        x: this.category.name,
                        y: targetMonthYear,
                        category: this.series.name,
                        drillDown: getChartDrillDown(Number(chartId), ''),
                        chartName: getChartName(chartId),
                        monthYearComparison: [month1, month3],
                        tabSelection: 'two',
                        datatype: datatype
                        // window.location.pathname == '/GraphDetailsView'
                        //   ? ''
                        //   : 'two'
                      }
                    });
                  } else {
                    if (typeof parentCallback == 'undefined') {
                      let tab;
                      let reports = '';
                      if (this.category.name == 'OTHER') {
                        //tab = 'six';
                        chartLoc =
                          isPartsCharts == false
                            ? 'WorkMixVolume'
                            : 'WorkMixVolumeParts';
                        reports =
                          isPartsCharts == false && datatype == 'Sales'
                            ? 'soldhours'
                            : isPartsCharts &&
                              (datatype == 'Sales' || datatype == 'Cost')
                            ? 'partscost'
                            : isPartsCharts && datatype == 'grossprofit'
                            ? 'grossprofitpercentage'
                            : datatype;
                      } else {
                        tab = 'four';
                      }

                      let monthYear = this.series.name.split('-');
                      let date = moment(monthYear[1], 'YY');
                      let year = date.format('YYYY');
                      let month = moment()
                        .month(monthYear[0])
                        .format('MM');
                      let targetMonth = year + '-' + month;
                      history.push({
                        pathname: '/' + chartLoc,
                        prevPath: window.location.pathname,

                        state: {
                          tabselection: tab,
                          type: 'comparisonchart',
                          isFrom: 'comparisonchart',
                          dbdType: 'workmix',
                          chartId: chartId,
                          opcode: this.category.name,
                          comparisonMonth1:
                            isFrom == 'details' ? month1 : month2,
                          comparisonMonth2:
                            isFrom == 'details' ? month2 : month1,
                          month: targetMonth,
                          month_year: [month1, month2],
                          workmixreportType: datatype,
                          selectedTypeValue: 'labor',
                          category: isPartsCharts ? 'parts' : 'labor',
                          reportType: reports
                        }
                      });
                    } else {
                      let monthYear = data.month.split('-');
                      let date = moment(monthYear[1], 'YY');
                      let year = date.format('YYYY');
                      let month = moment()
                        .month(monthYear[0])
                        .format('MM');
                      data.month = year + '-' + month;
                      parentCallback(data);
                    }
                  }
                }
              }
            }
          }
        }
      },
      legend: {
        onItemHover: {
          highlightDataSeries: false // Disable hover effect on legend items
        },
        events: {
          legendItemClick: function(
            event,
            chartContext,
            { seriesIndex, config }
          ) {
            event.preventDefault(); // Prevent the default behavior of toggling series
          }
        },
        itemStyle: {
          font: '10pt Trebuchet MS, Verdana, sans-serif',
          fontSize: '30px'
        },
        symbolHeight: 15,
        symbolWidth: 32,
        symbolRadius: 32
      },
      tooltip: {
        shape: 'rect',
        formatter: function() {
          var getprefix =
            dataType == 'addonrevenue' ||
            dataType == 'discountserviceadv' ||
            dataType == 'partscost' ||
            dataType == 'elr'
              ? '$'
              : '';
          var getSuffix =
            dataType == 'grossprofitpercentage' || dataType == 'workmix'
              ? '%'
              : '';
          var descTooltip =
            data.filter(item => item.lbropcode == this.points[0].x.name)
              .length > 0
              ? data.filter(item => item.lbropcode == this.points[0].x.name)[0]
                  .lbropcodedesc
              : '';
          const tooltipTemp =
            '<span style="font-size: 10px">' +
            this.points[0].x.name +
            ',' +
            this.points[0].x.parent.name +
            '</span><br/><span style="color:' +
            this.points[0].color +
            '">●</span> ' +
            this.points[0].series.name +
            ': <b>' +
            getprefix +
            this.points[0].y +
            getSuffix +
            '</b><br/><span style="color:' +
            this.points[1].color +
            '">●</span> ' +
            this.points[1].series.name +
            ': <b>' +
            getprefix +
            this.points[1].y +
            getSuffix +
            '</b><br/><span style="font-size: 10px">' +
            descTooltip +
            '</span><br/>';
          return tooltipTemp;
        },
        crosshairs: false,

        positioner: function(labelWidth, labelHeight, point) {
          // if (point.plotX > 700) {
          //   if (point.plotY < 250) {
          //     console.log('enter=1', chartId, point);
          //     return {
          //       x: point.plotX + this.chart.container.offsetLeft - 200,
          //       y: point.plotY + this.chart.container.offsetTop - 40
          //     };
          //   } else {
          //     console.log('enter=12', chartId, point);
          //     return {
          //       x: point.plotX + this.chart.container.offsetLeft - 200,
          //       y: point.plotY + this.chart.container.offsetTop - 200
          //     };
          //   }
          // } else if (point.plotY <= 300) {
          //   console.log('enter=123', chartId, point);
          //   return {
          //     x: point.plotX + this.chart.container.offsetLeft + 80,
          //     y: point.plotY + this.chart.container.offsetTop - 85
          //   };
          // } else {
          //   console.log('enter=1234', chartId, point);
          //   return {
          //     x: point.plotX + this.chart.container.offsetLeft,
          //     y: point.plotY + this.chart.container.offsetTop - 200
          //   };
          // }
          if (point.plotX > 700) {
            if (point.plotY < 250) {
              return {
                x: point.plotX + this.chart.container.offsetLeft,
                y: point.plotY + this.chart.container.offsetTop
              };
            } else {
              return {
                x: point.plotX + this.chart.container.offsetLeft,
                y: point.plotY + this.chart.container.offsetTop
              };
            }
          } else if (point.plotY <= 300) {
            //console.log('enter=123', chartId, point);
            return {
              x: point.plotX + this.chart.container.offsetLeft,
              y: point.plotY + this.chart.container.offsetTop
            };
          } else {
            return {
              x: point.plotX + this.chart.container.offsetLeft,
              y: point.plotY + this.chart.container.offsetTop
            };
          }
        },

        formatter: function() {
          var getprefix =
            dataType == 'addonrevenue' ||
            dataType == 'discountserviceadv' ||
            dataType == 'partscost' ||
            dataType == 'elr'
              ? '$'
              : '';
          var getSuffix =
            dataType == 'grossprofitpercentage' || dataType == 'workmix'
              ? '%'
              : '';
          var descTooltip =
            data.filter(item => item.lbropcode == this.points[0].x.name)
              .length > 0
              ? data.filter(item => item.lbropcode == this.points[0].x.name)[0]
                  .lbropcodedesc
              : '';
          var result = descTooltip.split(',');
          var splitted = [];

          // let tooltipTemp =
          //   '<span style="font-size: 10px">' +
          //   this.points[0].x.name +
          //   ',' +
          //   this.points[0].x.parent.name +
          //   '</span><br/><span style="color:' +
          //   this.points[0].color +
          //   '">●</span> ' +
          //   this.points[0].series.name +
          //   ': <b>' +
          //   getprefix +
          //   //this.points[0].y +
          //   formatChartValue(dataType, this.points[0].y) +
          //   getSuffix +
          //   '</b><br/><span style="color:' +
          //   this.points[1].color +
          //   '">●</span> ' +
          //   this.points[1].series.name +
          //   ': <b>' +
          //   getprefix +
          //   //this.points[1].y +
          //   formatChartValue(dataType, this.points[1].y) +
          //   getSuffix +
          //   '</b><br/>';
          let tooltipTemp = '';
          if (this.points[0]) {
            tooltipTemp +=
              '<span style="font-size: 10px">' +
              this.points[0].x.name +
              ',' +
              this.points[0].x.parent.name +
              '</span><br/><span style="color:' +
              this.points[0].color +
              '">●</span> ' +
              this.points[0].series.name +
              ': <b>' +
              getprefix +
              //this.points[0].y +
              formatChartValue(dataType, this.points[0].y) +
              getSuffix +
              '</b><br/>';
          }
          if (this.points[1]) {
            tooltipTemp +=
              '<span style="color:' +
              this.points[1].color +
              '">●</span> ' +
              this.points[1].series.name +
              ': <b>' +
              getprefix +
              //this.points[1].y +
              formatChartValue(dataType, this.points[1].y) +
              getSuffix +
              '</b><br/>';
          }

          if (this.points[2]) {
            tooltipTemp +=
              '<span style="color:' +
              this.points[2].color +
              '">●</span> ' +
              this.points[2].series.name +
              ': <b>' +
              getprefix +
              //this.points[1].y +
              formatChartValue(dataType, this.points[2].y) +
              getSuffix +
              '</b><br/>';
          }
          for (var i = 0; i < result.length; i++) {
            tooltipTemp +=
              '<span style="font-size: 10px">' + result[i] + '</span><br/>';
          }

          return tooltipTemp;
        },

        shared: true,
        useHTML: true,
        outside: type != 'popup' ? true : false,
        valueDecimals:
          dataType == 'grossprofitpercentage'
            ? 1
            : dataType == 'markup'
            ? 4
            : dataType == 'jobcount'
            ? ''
            : 2,
        valueSuffix:
          dataType == 'grossprofitpercentage' || dataType == 'workmix'
            ? '%'
            : '',
        valuePrefix:
          dataType == 'addonrevenue' ||
          dataType == 'discountserviceadv' ||
          dataType == 'partscost' ||
          dataType == 'elr'
            ? '$'
            : // : dataType == 'grossprofitpercentage' || dataType == 'workmix'
              // ? '%'
              '',
        borderColor: '#651FFF',
        backgroundColor: 'rgba(255,255,255,1)',
        shadow: false
      },
      series: series.map((val, index) => {
        return {
          name: getSeriesName(index, datatype, month1, month2),
          // name: index == 0 ? month2 : month1,
          // data: val.map(val => val),
          data: val.map(value => (value < 0 ? 0 : value)),
          id: 'row-' + index
        };
      }),
      xAxis: {
        categories: opcodesArray.map((val, index) => {
          const uniqueCategories = [...new Set(val)];

          return {
            name: getGroupName(index, datatype, month2, month1, opCategory),
            categories: uniqueCategories
          };
        }),

        labels: {
          groupedOptions: [
            {
              style: {
                color: '#19215d',
                fontWeight: 'bold'
              }
            }
          ],
          padding: 0,
          useHTML: true,
          rotation: 0,
          style: {
            color: 'black'
          },
          formatter: function() {
            const maxLength = 6;
            const value = this.value;
            const truncatedValue =
              value.length > maxLength
                ? value.substring(0, maxLength) + '..'
                : value;

            return `<span title="${value}">${truncatedValue}</span>`;
          }
        }
      },
      yAxis: {
        title: {
          text: YaxisTitle(dataType)
        },

        labels: {
          formatter: function(value) {
            return YaxisLabels(value.value, dataType);
          }
        },
        tickInterval: getTickInterval(chartId)
        //  tickAmount:8
      },

      credits: {
        enabled: false
      }
    };

    setOptionsHighChart(chartOptions);

    setTimeout(() => {
      $('.highcharts-xaxis-labels span')
        .toArray()
        .forEach(function(item, i) {
          var originalWidth = item.style.width;
          if (data.length > 10) {
            item.style.width =
              $('.highcharts-xaxis-labels span').width() + 3 + 'px';
          } else {
            item.style.width =
              $('.highcharts-xaxis-labels span').width() + 6 + 'px';
          }
        });
      setLoading(false);
    }, 200);
  };

  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        state: {
          month1: month1,
          month2: month2,
          datatype: datatype,
          isFrom: isFrom,
          SelectedLocation: window.location.pathname,
          tabSelection:
            (datatype == 'discountserviceadv' ||
              datatype == 'discountserviceadvOpcat' ||
              datatype == 'addonvsnonaddonrevenue%' ||
              datatype == 'addonrevenue') &&
            window.location.pathname != '/MyFavorites'
              ? 'two'
              : ''
        },
        SelectedLocation: window.location.pathname
      });
    }
  };
  const setTab = () => {
    localStorage.setItem('laborTabSelection', 'four');
  };
  let title =
    window.location.pathname == '/MyFavorites'
      ? getChartName(chartId) ==
        'Add On vs Non Add on Revenue % by Service Advisor'
        ? '<label style="font-size: 14px;">' +
          getChartName(chartId) +
          '</label> <br>' +
          '<label style="font-size: 12px;">' +
          moment(month1).format('MMM-YY') +
          ' vs ' +
          moment(month2).format('MMM-YY') +
          '</label>'
        : getChartName(chartId) == 'Add On Revenues by Service Advisor'
        ? '<label style="font-size: 14px;"; float:"left">' +
          getChartName(chartId) +
          '</label> <br>' +
          '<label style="font-size: 12px;">' +
          moment(month1).format('MMM-YY') +
          ' vs ' +
          moment(month2).format('MMM-YY') +
          '</label>'
        : '<label style="font-size: 14px;">' +
          getChartName(chartId) +
          '</label> <br>' +
          '<label style="font-size: 12px;">' +
          (moment(month2).format('MMM-YY')
            ? moment(month2).format('MMM-YY')
            : getComparisonMonths()[0]) +
          ' vs ' +
          (moment(month1).format('MMM-YY')
            ? moment(month1).format('MMM-YY')
            : getComparisonMonths()[1]) +
          '</label>' +
          '</label>' +
          '<label style="font-size: 14px;margin-left: 10px; margin-top: -5px;color: #7987a1;">' +
          chartId +
          '</label> '
      : getChartName(chartId) ==
        'Add On vs Non Add on Revenue % by Service Advisor'
      ? '<label style="font-size: 18px;">' +
        getChartName(chartId) +
        '</label> <br>' +
        '<label style="font-size: 14px;">' +
        moment(month1).format('MMM-YY') +
        ' vs ' +
        moment(month2).format('MMM-YY') +
        '</label>'
      : getChartName(chartId) == 'Add On Revenues by Service Advisor'
      ? '<label style="font-size: 18px;">' +
        getChartName(chartId) +
        '</label> <br>' +
        '<label style="font-size: 14px;">' +
        moment(month1).format('MMM-YY') +
        ' vs ' +
        moment(month2).format('MMM-YY') +
        '</label>'
      : '<label style="font-size: 14px;color:#031b4e; float:left">' +
        getChartName(chartId) +
        ' - </label> ' +
        '<label style="font-size: 14px; float:left;color:#031b4e;">&nbsp;' +
        (moment(month2).format('MMM-YY')
          ? moment(month2).format('MMM-YY')
          : getComparisonMonths()[0]) +
        ' vs ' +
        (moment(month1).format('MMM-YY')
          ? moment(month1).format('MMM-YY')
          : getComparisonMonths()[1]) +
        '</label>' +
        '<label style="font-size: 14px;margin-left: 10px; float:left;color: #7987a1;">' +
        chartId +
        '</label> ';

  return (
    <Paper
      square
      classes={{ root: classes.cardRoot }}
      style={
        // isFrom == 'source_page' ? { margin: 4, height: 355 } : { margin: 4 }
        isFrom == 'source_page'
          ? window.location.pathname == '/MyFavorites'
            ? { margin: 0 }
            : { margin: 0, height: 355 }
          : { margin: 0 }
      }
    >
      {isLoading == true ? (
        <Grid
          justify="center"
          style={{
            height: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <Card
          className={classes.container}
          id={
            datatype == 'discountserviceadvOpcat'
              ? 'discountsOpcategoryContainer'
              : 'highchartDataContainer'
          }
          style={{
            borderRadius: window.location.pathname === '/MyFavorites' ? 0 : 4
          }}
        >
          <CardHeader
            style={
              isFrom == 'source_page'
                ? { height: 90, borderBottom: '1px solid #003d6b' }
                : { height: '100%', borderBottom: '1px solid #003d6b' }
            }
            // titleTypographyProps={
            //   isFrom == 'source_page' ? { lineHeight: 25 } : { lineHeight: 25 }
            // }

            // titleTypographyProps={{variant:'h6' }}
            title={ReactHtmlParser(title)}
            action={
              datatype == 'addonvsnonaddonrevenue%' ||
              datatype == 'addonrevenue' ||
              datatype == 'discountserviceadv' ||
              datatype == 'discountserviceadvOpcat' ||
              datatype == 'soldhours' ||
              datatype == 'workmix' ||
              datatype == 'jobcount' ||
              datatype == 'partscost' ||
              datatype == 'markup' ||
              datatype == 'elr' ||
              datatype == 'grossprofitpercentage' ||
              isFrom == 'source_page' ? (
                <MoreActions
                  removeFavourite={removeFavourite}
                  setActions={setactions}
                  chartId={chartId}
                  month1={month1}
                  month2={month2}
                  type={type}
                  chartPopup={chartPopup}
                  handleClose={handleClose}
                  // favoritesDisabled={true}
                ></MoreActions>
              ) : null
            }
            // subheader={getSubHeader(chartId)}
          ></CardHeader>

          <Divider />
          <CardContent>
            <HighchartsReact
              className="test"
              containerProps={
                isFrom == 'source_page'
                  ? {
                      id: 'test',
                      style:
                        window.location.pathname == '/MyFavorites'
                          ? { height: 250 }
                          : { height: 250, width: 300 }
                    }
                  : type == 'popup'
                  ? { id: 'test', style: { height: 500 } }
                  : localStorage.getItem('realm') == 'koonsofsilverspring'
                  ? { id: 'test', style: { height: 600 } }
                  : ''
              }
              highcharts={Highcharts}
              options={optionsHighChart}
            />
          </CardContent>
        </Card>
      )}

      <ChartDialog
        open={open}
        chartId={chartId}
        isPartsCharts={isPartsCharts}
        mon1={month1}
        mon2={month2}
        chartType="comparison"
        realm={localStorage.getItem('realm')}
        handlePopupClose={handleClose}
        parentCallback={parentCallback}
        chartData={workmixChartData}
      />
    </Paper>
  );
};

function checkForMonthYear1(month1, month2) {
  return Date.parse(month2) > Date.parse(month1);
}
function checkForMonthYear(month1, month2, data) {
  if (month2 != data[0].monthYear) {
    return true;
  } else {
    return false;
  }
  //  return Date.parse(month2) > Date.parse(month1);
}

function getGroupName(index, dataType, month1, month2, opCategory) {
  let name = '';
  switch (index) {
    case 0:
      dataType == 'addonvsnonaddonrevenue%' || dataType == 'addonrevenue'
        ? (name = moment(month1).format('MMM-YY'))
        : dataType == 'discountserviceadv'
        ? (name = moment(month1).format('MMM-YY'))
        : dataType == 'discountserviceadvOpcat'
        ? (name = moment(month1).format('MMM-YY'))
        : (name =
            opCategory[0] == 'MAINTENANCE'
              ? 'MAINT'
              : opCategory[0] == 'COMPETITIVE'
              ? 'COMP'
              : 'REPAIR');
      // (name = 'COMP');
      break;
    case 1:
      dataType == 'addonvsnonaddonrevenue%' || dataType == 'addonrevenue'
        ? (name = moment(month2).format('MMM-YY'))
        : dataType == 'discountserviceadv'
        ? (name = moment(month2).format('MMM-YY'))
        : dataType == 'discountserviceadvOpcat'
        ? (name = moment(month2).format('MMM-YY'))
        : (name =
            opCategory[1] == 'MAINTENANCE'
              ? 'MAINT'
              : opCategory[1] == 'COMPETITIVE'
              ? 'COMP'
              : 'REPAIR');
      // (name = 'MAINT');
      break;
    case 2:
      // name = opCategory[2];
      name = 'REPAIR';
      break;
    default:
      name = '';
      break;
  }
  return name;
}
function getSeriesName(index, dataType, month1, month2) {
  let name = '';
  switch (index) {
    case 0:
      dataType == 'addonvsnonaddonrevenue%'
        ? (name = 'Add Ons Revenue% ')
        : dataType == 'addonrevenue'
        ? (name = 'Add Ons Labor Revenue')
        : dataType == 'discountserviceadv'
        ? (name = ' Labor Discount')
        : dataType == 'discountserviceadvOpcat'
        ? // ? (name = ' Repair Job Count %')
          (name = ' Competitive Job Count % ')
        : (name = moment(month2).format('MMM-YY'));
      break;
    case 1:
      dataType == 'addonvsnonaddonrevenue%'
        ? (name = 'Non-Add Ons Revenue% ')
        : dataType == 'addonrevenue'
        ? (name = 'Add Ons Parts Revenue')
        : dataType == 'discountserviceadv'
        ? (name = ' Parts Discount')
        : dataType == 'discountserviceadvOpcat'
        ? (name = ' Maintenance Job Count % ')
        : (name = moment(month1).format('MMM-YY'));
      break;
    case 2:
      dataType == 'discountserviceadvOpcat'
        ? (name = 'Repair Job Count %')
        : (name = moment(month1).format('MMM-YY'));
      break;
  }
  return name;
}
function formatDataWithOutZero(data, dataType, month1, month2) {
  var dataNonZeroArr = [];
  var dataZeroArr = [];
  var commonArr = [];
  if (dataType == 'addonvsnonaddonrevenue%') {
    data.map(item => {
      if (
        item.addonrevenuepercentage == 0 &&
        item.nonaddonrevenuepercentage == 0 &&
        (item.monthYear == month1 || item.monthYear == month2)
      ) {
        dataZeroArr.push(item);
      } else {
        dataNonZeroArr.push(item);
      }
    });
    dataNonZeroArr.map(item => {
      dataZeroArr.map(item1 => {
        if (item.serviceadvisor == item1.serviceadvisor) {
          commonArr.push(item1);
        }
      });
    });
    var data1 = dataNonZeroArr.concat(commonArr);

    return data1;
  }
  if (dataType == 'addonrevenue') {
    data.map(item => {
      if (
        item.addonlbrsale == 0 &&
        item.addonpartsrevenue == 0 &&
        (item.monthYear == month1 || item.monthYear == month2)
      ) {
        dataZeroArr.push(item);
      } else {
        dataNonZeroArr.push(item);
      }
    });
    dataNonZeroArr.map(item => {
      dataZeroArr.map(item1 => {
        if (item.serviceadvisor == item1.serviceadvisor) {
          commonArr.push(item1);
        }
      });
    });
    var data1 = dataNonZeroArr.concat(commonArr);
    return data1;
  }
}
const transformWorkmixData = workmixData => {
  const result = {};

  if (workmixData.length > 0) {
    const datasets = workmixData[0].datasets;

    // Find base datasets
    const lbropcode = datasets.find(d => d.label === 'lbropcode');
    const lbropcodedesc = datasets.find(d => d.label === 'lbropcodedesc');
    const opcategory = datasets.find(d => d.label === 'opcategory');

    // Group datasets by chartId
    const chartGroups = {};
    datasets.forEach(dataset => {
      if (dataset.chartId) {
        if (!chartGroups[dataset.chartId]) {
          chartGroups[dataset.chartId] = {};
        }
        chartGroups[dataset.chartId][dataset.label] = dataset;
      }
    });

    // Transform each chartId group
    Object.keys(chartGroups).forEach(chartId => {
      const group = chartGroups[chartId];
      const mon1Dataset = Object.values(group).find(d =>
        d.label.includes('_mon1')
      );
      const mon2Dataset = Object.values(group).find(d =>
        d.label.includes('_mon2')
      );

      if (mon1Dataset && mon2Dataset) {
        result[chartId] = lbropcode.data.map((code, index) => ({
          lbropcode: code,
          lbropcodedesc: lbropcodedesc.data[index],
          opcategory: opcategory.data[index],
          mon1: mon1Dataset.data[index],
          mon2: mon2Dataset.data[index]
        }));
      }
    });
  }

  return result;
};

const getDataType = chartId => {
  const chartMap = {
    1260: 'workmix',
    1259: 'soldhours',
    1263: 'grossprofitpercentage',
    1261: 'jobcount',
    1262: 'elr'
  };
  return chartMap[chartId] || 'workmix';
};
ColumnRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default ColumnRenderer;
