import {
  Grid,
  Paper,
  FormControl,
  MenuItem,
  Select,
  InputLabel
} from '@material-ui/core';
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import ColumnRenderer from './ColumnRenderer';
import { makeStyles } from '@material-ui/core/styles';
import moment from 'moment';
import 'src/styles.css';
import clsx from 'clsx';
import $ from 'jquery';
import {
  getLast13Months,
  getLastThreeYears,
  getComparisonMonths
} from 'src/utils/Utils';
import {
  getDrillGraphDataForMonthWiseComparisonLaborAll,
  getDrillGraphDataForMonthWiseComparisonPartsAll
} from 'src/utils/hasuraServices';
var lodash = require('lodash');
const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  container: {
    padding: 5
  }
});
const ComparisonChartsGrid = ({
  mon1,
  mon2,
  comparisonMonth1,
  comparisonMonth2,
  isPartsCharts,
  parentCallback,
  realm,
  removeFav
}) => {
  const session = useSelector(state => state.session);
  const [workmixChartData, setWorkmixChartData] = useState([]);
  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult;
  if (isPartsCharts) {
    filteredResult = chartList.filter(
      item =>
        item.dbdName == 'Parts Workmix-comparison' && item.parentId == null
    );
  } else {
    filteredResult = chartList.filter(
      item =>
        item.dbdName == 'Labor Workmix-comparison' && item.parentId == null
    );
  }
  const handleClosePopup = value => {
    console.log('state===handleClosePopup');
  };

  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  const classes = useStyles();
  mon2 =
    realm == 'ferrarioat_store'
      ? getComparisonMonths()[0]
      : typeof comparisonMonth2 != 'undefined' && comparisonMonth2 != ''
      ? comparisonMonth2
      : typeof mon2 != 'undefined' && mon2 != ''
      ? mon2
      : getComparisonMonths()[0];
  mon1 =
    realm == 'ferrarioat_store'
      ? getComparisonMonths()[1]
      : typeof comparisonMonth1 != 'undefined' && comparisonMonth1 != ''
      ? comparisonMonth1
      : typeof mon1 != 'undefined' && mon1 != ''
      ? mon1
      : getComparisonMonths()[1];

  let monthsArray = [];
  if (localStorage.getItem('versionFlag') === 'TRUE') {
    monthsArray = getLastThreeYears();
  } else {
    monthsArray = getLast13Months();
  }

  if (monthsArray.includes(mon1)) {
    mon1 = mon1;
  } else {
    mon1 = monthsArray[monthsArray.length - 1];
  }

  const [queryMonth, setQueryMonth] = useState(mon1);
  const [queryMonth2, setQueryMonth2] = useState(mon2);
  useEffect(() => {
    if (typeof comparisonMonth1 != 'undefined' && comparisonMonth1 != '') {
      setQueryMonth(comparisonMonth1);
    }
    if (typeof comparisonMonth2 != 'undefined' && comparisonMonth2 != '') {
      setQueryMonth2(comparisonMonth2);
    }
  }, []);
  useEffect(() => {
    setWorkmixChartData([]);
    console.log('isPartsCharts', isPartsCharts);
    if (isPartsCharts) {
      getDrillGraphDataForMonthWiseComparisonPartsAll(
        [queryMonth, queryMonth2, 'All'],
        result => {
          if (
            result.data
              .statelessDbdPartsWorkmixGetWorkmixChartPartsMonthlyComparisonAll
              .nodes
          ) {
            var data =
              result.data
                .statelessDbdPartsWorkmixGetWorkmixChartPartsMonthlyComparisonAll
                .nodes;

            setWorkmixChartData(JSON.parse(data[0].jsonData));
          }
        }
      );
    } else {
      getDrillGraphDataForMonthWiseComparisonLaborAll(
        [queryMonth2, queryMonth, 'All'],
        result => {
          if (
            result.data
              .statelessDbdLaborWorkmixGetWorkmixChartsMonthlyComparisonAll
              .statelessDbdLaborWorkmixChartLaborMonthlyComparisonTests
          ) {
            var data =
              result.data
                .statelessDbdLaborWorkmixGetWorkmixChartsMonthlyComparisonAll
                .statelessDbdLaborWorkmixChartLaborMonthlyComparisonTests;

            setWorkmixChartData(JSON.parse(data[0].jsonData));
          }
        }
      );
    }
  }, [queryMonth2, queryMonth, session.storeSelected]);
  const handleMonthchange = event => {
    setQueryMonth(event.target.value);
  };
  const handleMonthchange2 = event => {
    setQueryMonth2(event.target.value);
  };
  return (
    <Grid container className={classes.container} spacing={12}>
      <Grid container spacing={12}>
        <Grid item xs={6} justify="flex-start" className={classes.container}>
          <Paper square>
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel
                htmlFor="outlined-age-native-simple"
                margin="dense"
                style={{ padding: '12px' }}
              >
                Month 1
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth}
                onChange={handleMonthchange}
                MenuProps={{
                  getContentAnchorEl: null,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  },
                  PaperProps: {
                    style: {
                      maxHeight: 300
                    }
                  }
                }}
              >
                {localStorage.getItem('versionFlag') == 'TRUE'
                  ? getLastThreeYears().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))
                  : getLast13Months().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))}
              </Select>
            </FormControl>
          </Paper>
        </Grid>
        <Grid item xs={6} className={classes.container}>
          <Paper square justify="center">
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel
                htmlFor="outlined-age-native-simple"
                margin="dense"
                style={{ padding: '12px' }}
              >
                {/* Drill Down Month */}
                Month 2
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth2}
                onChange={handleMonthchange2}
                MenuProps={{
                  getContentAnchorEl: null,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  },
                  PaperProps: {
                    style: {
                      maxHeight: 300
                    }
                  }
                }}
              >
                {localStorage.getItem('versionFlag') == 'TRUE'
                  ? getLastThreeYears().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))
                  : getLast13Months().map(val => (
                      <MenuItem value={val}>
                        {moment(val).format('MMM-YY')}
                      </MenuItem>
                    ))}
              </Select>
            </FormControl>
          </Paper>
        </Grid>
      </Grid>

      {orderedData.map((item, index) => (
        <Grid
          item
          xs={12}
          justify="flex-start"
          className={clsx(classes.container, 'diagram-section')}
        >
          <ColumnRenderer
            month1={queryMonth2}
            month2={queryMonth}
            parentCallback={parentCallback}
            datatype={''}
            // datatype={item.chartName}
            handleClosePopup={handleClosePopup}
            isPartsCharts={isPartsCharts}
            chartId={orderedData[index].chartId}
            removeFav={removeFav}
            workmixChartData={workmixChartData}
            isFrom={'comparisonChartsGrid'}
          />
        </Grid>
      ))}
    </Grid>
  );
};

export default ComparisonChartsGrid;
