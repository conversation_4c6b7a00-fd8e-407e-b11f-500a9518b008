import { makeStyles } from '@material-ui/styles';
import React, { useEffect, useState, useRef } from 'react';
import { useHistory } from 'react-router';
import Page from 'src/components/Page';
import Charts from './Charts';
import { useSelector, useDispatch } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { getLatestClosedDate } from 'src/utils/hasuraServices';
import { checkClosedDateInCurrentMonth } from 'src/utils/Utils';
import { setToggleStatus } from 'src/actions';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));
const getBrowserTitle = (pathName, chartName) => {
  switch (pathName) {
    case '/LaborWorkMixAnalysis':
      return 'Labor Work Mix';
    case '/PartsWorkMixAnalysis':
      return 'Parts Work Mix';
    case '/TechnicianPerformance':
      return 'Technician Performance';
    case '/ServiceAdvisorPerformance':
      return 'Service Advisor Performance';
    case '/AnalyzeData':
      return chartName
        ? 'Drill Down - ' + chartName
        : localStorage.getItem('chartName')
        ? 'Drill Down - ' + localStorage.getItem('chartName')
        : 'Drill Down';
    default:
      return 'Labor';
  }
};
function AnalyzeData({ route, ...rest }) {
  const classes = useStyles();
  const history = useHistory();
  const itemsRef = useRef([]);
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  var pathName = history.location.pathname;
  var chartName = history.location.state
    ? history.location.state.chartName
      ? history.location.state.chartName
      : ''
    : localStorage.getItem('chartName');
  const titles = getBrowserTitle(pathName, chartName);
  const [closedDate, setClosedDate] = useState('');
  useEffect(() => {
    console.log(
      'closedDate===',
      closedDate,
      localStorage.getItem('closedDate')
    );
    if (
      localStorage.getItem('closedDate') == null &&
      localStorage.getItem('closedDate') == ''
    ) {
      getLatestClosedDate(result => {
        if (result) {
          var openDate = '';
          var Date1 = result[0].value;

          setClosedDate(Date1);

          localStorage.setItem('closedDate', Date1);
        }
      });
    } else {
      setClosedDate(localStorage.getItem('closedDate'));
    }
  }, [session.serviceAdvisor]);
  useEffect(() => {
    let status = checkClosedDateInCurrentMonth(
      localStorage.getItem('closedDate'),
      localStorage.getItem('openDate')
    );
    if (session.toggleStatus != undefined) {
      dispatch(setToggleStatus(session.toggleStatus));
    } else {
      dispatch(setToggleStatus(status));
    }
  }, []);
  return (
    <Page className={classes.root} title={titles}>
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      ((rest.location &&
        rest.location.state &&
        rest.location.state.isFrom &&
        rest.location.state.isFrom != 'searchByRo') ||
        (rest.location && rest.location.state == undefined)) ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts
          history={history}
          route={route}
          session={session}
          closedDate={closedDate}
          itemsRef={itemsRef}
        />
      )}
    </Page>
  );
}

export default AnalyzeData;
