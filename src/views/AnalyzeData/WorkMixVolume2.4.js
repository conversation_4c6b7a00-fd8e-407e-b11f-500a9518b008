import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import DateFnsUtils from '@date-io/date-fns';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import {
  Card,
  CardContent,
  Divider,
  Grid,
  Button,
  Typography,
  FormControl,
  InputLabel,
  LinearProgress,
  Box,
  Paper,
  MenuItem,
  Select,
  Tooltip
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';

import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import ApolloClient from 'apollo-boost';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getWorkMixReportTotalVolume,
  getWorkMixReportTotalVolumeParts
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';

import RestoreIcon from '@material-ui/icons/Restore';
import Tab from '@material-ui/core/Tab';
import sparkline from 'jquery-sparkline';
import Tabs from '@material-ui/core/Tabs';
import css from 'src/assets/scss/main.scss';
import { getLast13Months } from 'src/utils/Utils';
import { type } from 'jquery';
import Page from 'src/components/Page';
import Chip from '@material-ui/core/Chip';
import clsx from 'clsx';
import 'src/styles.css';
import { ReactSession } from 'react-client-session';
import { Redirect } from 'react-router-dom';

var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

class WorkMixVolume extends React.Component {
  componentWillMount() {
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    let category = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.category
        : this.props.category
      : this.props.category;
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      console.log(
        'selected=',
        ReactSession.get('selectedStoreId'),
        checkSt,
        localStorage.getItem('selectedStoreId'),
        this.state.store
      );
      if (checkSt == false) {
        this.setState({ selectValue: 'other' });
        this.setState({ selectValueFor: category });
        this.setState({ selectValueType: 'workmix' });
        this.getAgGridData(this.state.selectValueFor, 'workmix');
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    let category = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.category
        : this.props.category
      : this.props.category;
    let reportType = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedReportType &&
          this.props.history.location.state.selectedReportType != ''
          ? this.props.history.location.state.selectedReportType
          : this.props.reportType
        : this.props.reportType
      : this.props.reportType;

    let isFrom = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.isFrom
        : this.props.isFrom
      : this.props.isFrom;
    let comparisonMonth1 = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.comparisonMonth1
        : this.props.comparisonMonth1
      : this.props.comparisonMonth1;
    let comparisonMonth2 = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.comparisonMonth2
        : this.props.comparisonMonth2
      : this.props.comparisonMonth2;
    let type = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.type
        : this.props.type
      : this.props.type;
    let parent = this.props.history
      ? this.props.history.location.state &&
        this.props.history.location.state.workmixParent
        ? this.props.history.location.state.workmixParent
        : ''
      : '';
    let workmixTab = this.props.history
      ? this.props.history.location.state &&
        this.props.history.location.state.workmixTab
        ? this.props.history.location.state.workmixTab
        : ''
      : '';
    let selectedReportType =
      this.props.location && this.props.location.state
        ? this.props.location.state.selectedReportType
        : '';
    let opCategory = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.opCategory
        : this.props.opCategory
      : this.props.opCategory;
    let isExpanded = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.isExpanded
        : this.props.isExpanded
      : this.props.isExpanded;
    let prevPath =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.prevPath;
    let prepathSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.prevPath;
    this.state = {
      showCharts: false,
      selectValue: 'other',
      selectValueFor: category,
      selectValueType:
        reportType == 'grossprofit' ? 'grossprofitpercentage' : reportType,
      // ddLabel: '< 2',
      ddLabel: '-2%',
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      isExpanded: isExpanded,
      isFrom: isFrom,
      comparisonMonth1: comparisonMonth1,
      comparisonMonth2: comparisonMonth2,
      type: type,
      category: category,
      parent: parent,
      workmixTab: workmixTab,
      selectedReportType: selectedReportType,
      opCategory: opCategory,
      prevMenu: this.props.session && this.props.session.menuSelected,
      prevPath: prevPath,
      prepathSelected: prepathSelected,
      columnDefs: [
        {
          headerName: 'Opcode',
          field: 'opcode',
          width: 90,
          minWidth: 90,
          flex: 1,
          suppressMenu: true,
          tooltipField: 'opcode',
          unSortIcon: true,
          cellRenderer: function(params) {
            return `<a style="cursor: pointer"  >${params.value}</a>`;
          },
          onCellClicked: this.handleCellClicked,
          cellClass: 'textAlign',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'opcodedescription',
          tooltipField: 'opcodedescription',
          width: 100,
          minWidth: 100,
          resizable: true,
          suppressMenu: true,

          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          headerClass: 'ag-header-cell-text'
        },
        {
          headerName: 'Op Category',
          // filter: 'agTextColumnFilter',
          field: 'opcategory',
          dataType: 'string',
          width: 120,
          suppressMenu: true,
          unSortIcon: true,

          // cellRenderer: 'agGroupCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Tbl Category',
          field: 'tblcategory',
          dataType: 'string',
          width: 120,
          hide: true,
          suppressMenu: true,
          unSortIcon: true,

          cellRenderer: 'agGroupCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: '13 Month Average',
          width: 115,
          suppressMenu: true,
          unSortIcon: true,
          filter: 'agSetColumnFilter',
          chartDataType: 'category',
          suppressMenu: true,
          cellClass: this.getCellClass,
          valueGetter: this.calculateAvg,
          valueFormatter: this.formatCellValue,

          cellStyle: function(params) {
            return { textAlign: 'right' };
          }
        },
        {
          headerName: "Apr'20",
          field: 'mon13',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Mar'20",
          field: 'mon12',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Feb'20",
          field: 'mon11',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Jan'20",
          field: 'mon10',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Dec'19",
          field: 'mon9',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Nov'19",
          field: 'mon8',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },

        {
          headerName: "Oct'19",
          field: 'mon7',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },

        {
          headerName: "Sep'19",
          field: 'mon6',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },

        {
          headerName: "Aug'19",
          field: 'mon5',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Jul'19",
          field: 'mon4',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Jun'19",
          field: 'mon3',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "May'19",
          field: 'mon2',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        },
        {
          headerName: "Apr'19",
          field: 'mon1',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.getCellStyle,
          valueFormatter: this.formatCellValue,

          cellClass: this.getCellClass
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0' }
        },
        {
          id: 'fourDecimalPlace',
          numberFormat: { format: '#,##0.0000' }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  getCellClass = params => {
    if (
      this.state.selectValueType == 'Sales' ||
      this.state.selectValueType == 'partscost' ||
      this.state.selectValueType == 'elr'
    ) {
      return 'twoDecimalPlacesWith$';
    } else if (this.state.selectValueType == 'markup') {
      return 'fourDecimalPlace';
    } else {
      return 'twoDecimalPlacesWithOut$';
    }
  };
  resetRawData = () => {
    //this.setState({ resetReport: this.props.resetReport });
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);

    var monthYear = this.getMonthyear();
    var monthnames = [
      'mon13',
      'mon12',
      'mon11',
      'mon10',
      'mon9',
      'mon8',
      'mon7',
      'mon6',
      'mon5',
      'mon4',
      'mon3',
      'mon2',
      'mon1'
    ];

    for (let index = 0; index < monthnames.length; index++) {
      const headerName = monthnames[index];
      var makeCol = this.gridApi.columnController.columnApi.getColumn(
        headerName
      );

      if (makeCol != null) {
        makeCol.colDef.headerName = monthYear[index];
      }
      this.gridApi.refreshHeader();
    }
    var defaultSortModel = [
      {
        colId: 'mon1',
        sort: 'desc'
      },
      {
        colId: 'mon2',
        sort: 'desc'
      },
      {
        colId: 'mon3',
        sort: 'desc'
      },
      {
        colId: 'mon4',
        sort: 'desc'
      },
      {
        colId: 'mon5',
        sort: 'desc'
      },
      {
        colId: 'mon6',
        sort: 'desc'
      },
      {
        colId: 'mon7',
        sort: 'desc'
      },
      {
        colId: 'mon8',
        sort: 'desc'
      },
      {
        colId: 'mon9',
        sort: 'desc'
      },
      {
        colId: 'mon10',
        sort: 'desc'
      },
      {
        colId: 'mon11',
        sort: 'desc'
      },
      {
        colId: 'mon12',
        sort: 'desc'
      },
      {
        colId: 'mon13',
        sort: 'desc'
      }
    ];
    this.gridApi.setSortModel(defaultSortModel);
    setTimeout(
      function() {
        let filterComponent = this.state.rawGridApi.getFilterInstance(
          'tblcategory'
        );
        filterComponent.selectNothing();
        if (this.state.selectValue != 'None') {
          filterComponent.selectValue(this.state.selectValue);
          filterComponent.applyModel();
          this.state.rawGridApi.onFilterChanged();
        } else {
          this.state.rawGridApi.setRowData(this.state.rowData);
        }
      }.bind(this),
      100
    );
  };
  calculateAvg = params => {
    if (params.data != undefined) {
      let arr = [];
      arr = params.data;

      var result = Object.keys(arr).map(function(key) {
        return arr[key];
      });

      var resultArr = [];

      resultArr = result.slice(4, 17);

      var filteredArr = resultArr.filter(function(el) {
        return (
          el != 0 &&
          el != undefined &&
          typeof el != 'string' &&
          typeof el != 'object'
        );
      });

      var noNullArr = resultArr.filter(el => el != null);

      if (filteredArr.length != 0) {
        var arrAvg =
          Math.round(
            (filteredArr.reduce((a, b) => parseFloat(a) + parseFloat(b), 0) /
              filteredArr.length +
              Number.EPSILON) *
              100
          ) / 100;
        arrAvg = Math.round(arrAvg);
      } else if (filteredArr.length != 0) {
        var arrAvg =
          ((filteredArr.reduce((a, b) => parseFloat(a) + parseFloat(b), 0) /
            filteredArr.length +
            Number.EPSILON) *
            100) /
          100;
        arrAvg = arrAvg.toFixed(4);
      } else {
        var arrAvg = 0;
      }

      return arrAvg;
    }
  };
  formatCellValue = params => {
    if (params.value != null) {
      this.setState({ reportType: this.state.reportType });
      if (
        this.state.selectValueType == 'Sales' ||
        this.state.selectValueType == 'partscost' ||
        this.state.selectValueType == 'elr'
      ) {
        return params.value == 0
          ? ''
          : Math.sign(params.value) > -1
          ? '$' + parseFloat(params.value).toLocaleString()
          : '-$' + Math.abs(parseFloat(params.value)).toLocaleString();
      } else if (this.state.selectValueType == 'markup') {
        return params.value == 0 ? '' : Number(params.value);
      } else if (this.state.selectValueType == 'grossprofitpercentage') {
        return params.value == 0
          ? ''
          : parseFloat(params.value).toLocaleString();
      } else {
        return params.value == 0 ? '' : params.value;
      }
    }
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName:
        this.state.selectValueFor == 'parts'
          ? 'Parts Work Mix \n Other'
          : 'Labor Work Mix \n Other',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value:
                this.state.selectValueFor == 'parts'
                  ? 'Parts Work Mix \n Other'
                  : 'Labor Work Mix \n Other'
            },
            mergeAcross: 3
          }
        ]
      ]
    };
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });

    var monthYear = this.getMonthyear();
    var monthnames = [
      'mon13',
      'mon12',
      'mon11',
      'mon10',
      'mon9',
      'mon8',
      'mon7',
      'mon6',
      'mon5',
      'mon4',
      'mon3',
      'mon2',
      'mon1'
    ];

    for (let index = 0; index < monthnames.length; index++) {
      const headerName = monthnames[index];
      var makeCol = params.columnApi.getColumn(headerName);
      makeCol.colDef.headerName = monthYear[index];
      params.api.refreshHeader();
    }
    this.setState({ rawGridApi: params.api });
    this.gridApi = params.api;
    //this.setState({ gridApi: params });
    if (this.props.history.location.state == undefined) {
      window.sortState = {};
      window.filterState = {};
    }
    this.gridApi.setSortModel(window.sortState);
    this.gridApi.setFilterModel(window.filterState);

    this.getAgGridData(this.state.selectValueFor, this.state.selectValueType);
    var defaultSortModel = [
      {
        colId: 'mon1',
        sort: 'desc'
      },
      {
        colId: 'mon2',
        sort: 'desc'
      },
      {
        colId: 'mon3',
        sort: 'desc'
      },
      {
        colId: 'mon4',
        sort: 'desc'
      },
      {
        colId: 'mon5',
        sort: 'desc'
      },
      {
        colId: 'mon6',
        sort: 'desc'
      },
      {
        colId: 'mon7',
        sort: 'desc'
      },
      {
        colId: 'mon8',
        sort: 'desc'
      },
      {
        colId: 'mon9',
        sort: 'desc'
      },
      {
        colId: 'mon10',
        sort: 'desc'
      },
      {
        colId: 'mon11',
        sort: 'desc'
      },
      {
        colId: 'mon12',
        sort: 'desc'
      },
      {
        colId: 'mon13',
        sort: 'desc'
      }
    ];
    params.api.setSortModel(defaultSortModel);
  };
  handleCellClicked = params => {
    window.sortState = this.gridApi.getSortModel();
    window.colStateWrkMixVlm = this.state.gridColumnApi.getColumnState();
    window.filterState = this.gridApi.getFilterModel();
    // let data = {
    //   type: 'workmixvolume',
    //   opcode: params.value
    // };
    // this.props.parentCallback(data);
    this.state.category == 'labor'
      ? this.props.history.push({
          pathname: '/LaborWorkMixAnalysis',
          state: {
            tabSelection: 'four',
            opcode: params.value,
            isFrom: 'workMixVolume',
            reportType: this.state.selectValueType,
            workmixParent: this.state.isFrom,
            workmixTab: this.state.type,
            selectedReportType: this.props.location.state
              ? this.props.location.state.selectedReportType
              : this.state.selectValueType,
            opCategory: this.props.location.state
              ? this.props.location.state.opCategory
              : this.state.opCategory,
            parent: '',
            prevPath: this.state.prevPath
          }
        })
      : this.props.history.push({
          pathname: '/PartsWorkMixAnalysis',
          state: {
            tabSelection: 'four',
            opcode: params.value,
            isFrom: 'workMixVolume',
            reportType: this.state.selectValueType,
            workmixParent: this.state.isFrom,
            workmixTab: this.state.type,
            opCategory: this.state.opCategory,
            prevPath: this.state.prevPath
          }
        });
  };
  getMonthyear = () => {
    var monthValues = getLast13Months();
    var yearMonth = [];
    var months = [
      'none',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    for (let index = 0; index < monthValues.length; index++) {
      var mon = monthValues[index];
      var res = mon.split('-');
      var month1 = res[1];
      month1 = month1.replace(/^0+/, '');

      yearMonth.push(months[month1] + " '" + res[0].slice(2));
    }
    yearMonth.reverse();
    this.setState({ yearMonthArr: yearMonth });

    return yearMonth;
  };
  getCellStyle = params => {
    if (params.data != undefined) {
      let colorArr = [
        '#003d6b',
        '#054372',
        '#064677',
        '#0f5285',
        '#165a8e',
        '#1e6296',
        '#2772ac',
        '#347fb8',
        '#468cc1',
        '#5797c7',
        '#6baad8',
        '#88bce3',
        '#b5daf6'
      ];
      var bgColor = null;
      let dataArr = [];

      dataArr.push(
        params.data.mon1,
        params.data.mon2,
        params.data.mon3,
        params.data.mon4,
        params.data.mon5,
        params.data.mon6,
        params.data.mon7,
        params.data.mon8,
        params.data.mon9,
        params.data.mon10,
        params.data.mon11,
        params.data.mon12,
        params.data.mon13
      );
      var filtered = dataArr.filter(function(el) {
        return el != null;
      });
      filtered.sort((a, b) => b - a);
      if (params.value != 0) {
        var valueIndex = filtered.indexOf(params.value);
        bgColor = colorArr[valueIndex];
      } else {
        bgColor = null;
      }
    }

    return {
      'background-color': bgColor,
      color: '#fff',
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  parseArray(filtererdArray) {
    let intArray = [];

    var res = filtererdArray.map(v => {
      intArray.push({
        opcategory: v.opcategory,
        opcode: v.opcode,
        opcodedescription: v.opcodedescription,
        tblcategory: v.tblcategory,
        mon1: parseFloat(v['mon1']) || 0,
        mon2: parseFloat(v['mon2']) || 0,
        mon3: parseFloat(v['mon3']) || 0,
        mon4: parseFloat(v['mon4']) || 0,
        mon5: parseFloat(v['mon5']) || 0,
        mon6: parseFloat(v['mon6']) || 0,
        mon7: parseFloat(v['mon7']) || 0,
        mon8: parseFloat(v['mon8']) || 0,
        mon9: parseFloat(v['mon9']) || 0,
        mon10: parseFloat(v['mon10'] || 0),
        mon11: parseFloat(v['mon11'] || 0),
        mon12: parseFloat(v['mon12'] || 0),
        mon13: parseFloat(v['mon13'] || 0)
      });
      // return res;
    });
    return intArray;
  }
  getAgGridData(tblFor, viewFor) {
    this.setState({ isLoading: true });
    if (tblFor == 'labor') {
      getWorkMixReportTotalVolume(tblFor, viewFor, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessDbdLaborWorkmixGetWorkmixLaborOpcodesVolumeGrouping.nodes
        ) {
          var parsedArray = this.parseArray(
            result.data
              .statelessDbdLaborWorkmixGetWorkmixLaborOpcodesVolumeGrouping
              .nodes
          );
          this.setState({
            rowData: parsedArray
          });
          this.filterByOpCategory();
          if (window.filterState != undefined) {
            this.filterByValue();
          }

          // this.onGroupExpandedOrCollapsed();
        }
        setTimeout(
          function() {
            let filterComponent = this.state.rawGridApi.getFilterInstance(
              'tblcategory'
            );
            filterComponent.selectNothing();
            if (this.state.selectValue != 'None') {
              filterComponent.selectValue(this.state.selectValue);
              filterComponent.applyModel();
              this.state.rawGridApi.onFilterChanged();
            } else {
              this.state.rawGridApi.setRowData(this.state.rowData);
            }
          }.bind(this),
          100
        );
      });
    } else {
      getWorkMixReportTotalVolumeParts(tblFor, viewFor, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessDbdPartsWorkmixGetWorkmixPartsOpcodesVolumeGrouping.nodes
        ) {
          var parsedArray = this.parseArray(
            result.data
              .statelessDbdPartsWorkmixGetWorkmixPartsOpcodesVolumeGrouping
              .nodes
          );
          this.setState({
            rowData: parsedArray
          });
          this.filterByOpCategory();
          if (window.filterState != undefined) {
            this.filterByValue();
          }
        }
        setTimeout(
          function() {
            let filterComponent = this.state.rawGridApi.getFilterInstance(
              'tblcategory'
            );
            filterComponent.selectNothing();
            if (this.state.selectValue != 'None') {
              filterComponent.selectValue(this.state.selectValue);
              filterComponent.applyModel();
              this.state.rawGridApi.onFilterChanged();
            } else {
              this.state.rawGridApi.setRowData(this.state.rowData);
            }
          }.bind(this),
          100
        );
      });
    }

    this.handleChange = event => {
      if (event.target.name == 'filter1') {
        this.setState({ selectValue: event.target.value });
        setTimeout(
          function() {
            let filterComponent = this.state.rawGridApi.getFilterInstance(
              'tblcategory'
            );
            filterComponent.selectNothing();
            if (this.state.selectValue != 'None') {
              filterComponent.selectValue(this.state.selectValue);
              filterComponent.applyModel();
              this.state.rawGridApi.onFilterChanged();
            } else {
              this.state.rawGridApi.setRowData(this.state.rowData);
            }
          }.bind(this),
          100
        );
      }

      if (event.target.name == 'filter2') {
        this.setState({ selectValueFor: event.target.value }, () => {
          setTimeout(
            function() {
              this.getAgGridData(
                this.state.selectValueFor,
                this.state.selectValueType
              );
            }.bind(this),
            50
          );
        });
      }
      if (event.target.name == 'filter3') {
        this.setState({ selectValueType: event.target.value }, () => {
          setTimeout(
            function() {
              this.getAgGridData(
                this.state.selectValueFor,
                this.state.selectValueType
              );
            }.bind(this),
            50
          );
        });
      }
    };
    var defaultSortModel = [
      {
        colId: 'mon1',
        sort: 'desc'
      },
      {
        colId: 'mon2',
        sort: 'desc'
      },
      {
        colId: 'mon3',
        sort: 'desc'
      },
      {
        colId: 'mon4',
        sort: 'desc'
      },
      {
        colId: 'mon5',
        sort: 'desc'
      },
      {
        colId: 'mon6',
        sort: 'desc'
      },
      {
        colId: 'mon7',
        sort: 'desc'
      },
      {
        colId: 'mon8',
        sort: 'desc'
      },
      {
        colId: 'mon9',
        sort: 'desc'
      },
      {
        colId: 'mon10',
        sort: 'desc'
      },
      {
        colId: 'mon11',
        sort: 'desc'
      },
      {
        colId: 'mon12',
        sort: 'desc'
      },
      {
        colId: 'mon13',
        sort: 'desc'
      }
    ];
    this.state.rawGridApi.setSortModel(defaultSortModel);
    var monthYear = this.getMonthyear();
    var monthnames = [
      'mon13',
      'mon12',
      'mon11',
      'mon10',
      'mon9',
      'mon8',
      'mon7',
      'mon6',
      'mon5',
      'mon4',
      'mon3',
      'mon2',
      'mon1'
    ];

    for (let index = 0; index < monthnames.length; index++) {
      const headerName = monthnames[index];
      var makeCol = this.state.rawGridApi.columnController.columnApi.getColumn(
        headerName
      );
      makeCol.colDef.headerName = monthYear[index];
      this.state.rawGridApi.refreshHeader();
    }
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterState);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  filterByOpCategory = () => {
    var opCatFilterComponent = this.state.rawGridApi.getFilterInstance(
      'opcategory'
    );
    if (opCatFilterComponent && this.state.opCategory) {
      opCatFilterComponent.setModel({ values: [this.state.opCategory] });
    }

    this.state.rawGridApi.onFilterChanged();
  };

  renderBackButton = () => {
    {
      let data = {};
      window.sortState = {};
      window.filterState = {};
      if (
        this.state.prepathSelected == '/MyFavorites' ||
        this.state.prevPath == '/MyFavorites'
      ) {
        this.props.history.push({
          pathname: '/MyFavorites'
        });
      } else {
        if (
          (this.state.isFrom == 'comparisonchart' &&
            this.state.type == 'reports') ||
          this.state.workmixTab == 'reports'
        ) {
          /*  data = {
            tabSelection: 'three',
            isFrom: 'drillDown-workmix',
           // reportType: this.props.reportType,
          };*/
          this.state.category == 'labor'
            ? this.props.history.push({
                pathname: '/LaborWorkMixAnalysis',
                state: {
                  tabSelection: 'three',
                  isFrom: 'workmixVolume',
                  // selectedReportType: this.props.location.state.selectedReportType,
                  // reportType: this.props.location.state.selectedReportType
                  selectedReportType: this.state.selectedReportType
                    ? this.state.selectedReportType
                    : this.props.selectValueType == 'partscost'
                    ? 'partscost'
                    : this.state.selectValueType == 'grossprofitpercentage'
                    ? 'grossprofit'
                    : this.state.selectValueType,
                  reportType: this.state.selectedReportType
                    ? this.state.selectedReportType
                    : this.state.selectValueType == 'partscost'
                    ? 'partscost'
                    : this.state.selectValueType == 'grossprofitpercentage'
                    ? 'grossprofit'
                    : this.state.selectValueType,
                  isExpanded: this.state.isExpanded,
                  prevPath: this.state.prevPath
                }
              })
            : this.props.history.push({
                pathname: '/PartsWorkMixAnalysis',
                state: {
                  tabSelection: 'three',
                  isFrom: 'workmixVolume',
                  selectedReportType:
                    this.state.selectValueType == 'partscost'
                      ? 'partscost'
                      : this.state.selectValueType == 'grossprofitpercentage'
                      ? 'grossprofit'
                      : this.state.selectValueType,
                  reportType:
                    this.state.selectValueType == 'partscost'
                      ? 'partscost'
                      : this.state.selectValueType == 'grossprofitpercentage'
                      ? 'grossprofit'
                      : this.state.selectValueType,
                  isExpanded: this.state.isExpanded
                }
              });
        } else if (
          this.state.isFrom == 'comparisonchart' ||
          this.state.parent == 'comparisonchart'
        ) {
          /*data = {
            tabSelection: 'two',
            isFrom: 'drillDown-workmix',
            comparisonMonth1: this.state.comparisonMonth1,
            comparisonMonth2: this.state.comparisonMonth2
          };*/
          this.state.category == 'labor'
            ? this.props.history.push({
                pathname: '/LaborWorkMixAnalysis',
                state: {
                  tabSelection: 'two',
                  comparisonMonth2: this.state.comparisonMonth1,
                  comparisonMonth1: this.state.comparisonMonth2
                }
              })
            : this.props.history.push({
                pathname: '/PartsWorkMixAnalysis',
                state: {
                  tabSelection: 'two',
                  comparisonMonth2: this.state.comparisonMonth1,
                  comparisonMonth1: this.state.comparisonMonth2
                }
              });
        }
        //this.props.history.parentCallback(data);
      }
    }
  };

  render() {
    const { classes } = this.props;
    return (
      <div>
        <Page
          title={
            this.state.selectValueFor == 'parts'
              ? 'Parts Work Mix \n Other'
              : 'Labor Work Mix \n Other'
          }
        ></Page>
        {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <React.Fragment>
            <Paper
              square
              style={{
                margin: 8,
                // marginTop: '20px',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
            >
              <Tabs
                value={this.state.tabSelection}
                onChange={this.handleTabChange}
                variant="fullWidth"
                indicatorColor="secondary"
                textColor="secondary"
                aria-label="icon label tabs example"
              >
                {this.props.parent == 'workMixVolume' ||
                this.state.isFrom == 'workMixVolume' ||
                (this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.workmixParent ==
                    'comparisonchart') ||
                (this.props.history &&
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.isFrom ==
                    'comparisonchart' &&
                  (this.props.history.location.state.reportType == 'workmix' ||
                    this.props.history.location.state.selectedReportType !=
                      '')) ? (
                  <div style={{ marginTop: 9 }}>
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      onClick={this.renderBackButton}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  </div>
                ) : (
                  <Link></Link>
                )}
                <Tab
                  label={
                    <div>
                      {this.state.selectValueFor == 'parts'
                        ? 'Parts Work Mix \n Other'
                        : 'Labor Work Mix \n Other'}
                    </div>
                  }
                  value="one"
                  style={{ pointerEvents: 'none', textTransform: 'none' }}
                />
                <span style={{ marginRight: 9, marginTop: 13 }}>
                  <Typography
                    variant="body1"
                    color="secondary"
                    align="right"
                    className={clsx(classes.dataLabel, 'date-asof')}
                  >
                    Data as of :{' '}
                    {moment(localStorage.getItem('closedDate')).format(
                      'MM/DD/YY'
                    )}
                  </Typography>
                </span>
                <Button
                  variant="contained"
                  className={clsx(classes.resetBtn, 'reset-btn')}
                  onClick={this.resetRawData}
                >
                  <RestoreIcon />
                  <Typography variant="body1" align="left">
                    Reset Layout
                  </Typography>
                </Button>
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{ alignSelf: 'center', cursor: 'pointer' }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip>
              </Tabs>
            </Paper>
            <Paper
              square
              style={{ margin: 8, marginRight: 8, height: 55, paddingTop: 4 }}
            >
              {/* <FormControl
            margin="dense"
            variant="outlined"
            style={{
              minWidth: 120,
              marginLeft: 10
            }}
          >
            <InputLabel
              htmlFor="outlined-age-native-simple"
              margin="dense"
              style={{ marginTop: -4 }}
            >
              For
            </InputLabel>
            <Select
              margin="dense"
              variant="outlined"
              label="Filter By"
              name="filter2"
              value={this.state.selectValueFor}
              onChange={this.handleChange}
              disabled="true"
            >
              <MenuItem value="labor">Labor</MenuItem>
              <MenuItem value="parts">Parts</MenuItem>
            </Select>
          </FormControl> */}
              <FormControl
                margin="dense"
                variant="outlined"
                style={{
                  minWidth: 120,
                  marginLeft: 10
                }}
              >
                <InputLabel
                  htmlFor="outlined-age-native-simple"
                  margin="dense"
                  style={{ marginTop: -4 }}
                >
                  Type
                </InputLabel>
                <Select
                  margin="dense"
                  variant="outlined"
                  label="Filter By"
                  name="filter3"
                  value={this.state.selectValueType}
                  onChange={this.handleChange}
                >
                  <MenuItem value="workmix">Work Mix %</MenuItem>
                  <MenuItem value="grossprofitpercentage">
                    Gross Profit%
                  </MenuItem>
                  <MenuItem value="jobcount">Job Count</MenuItem>
                  <MenuItem value="Sales">Sales</MenuItem>
                  {this.state.selectValueFor == 'labor' ? (
                    <MenuItem value="soldhours">Sold Hours</MenuItem>
                  ) : (
                    <MenuItem value="partscost">Cost</MenuItem>
                  )}

                  {this.state.selectValueFor == 'labor' ? (
                    <MenuItem value="elr">ELR</MenuItem>
                  ) : (
                    <MenuItem value="markup">Markup</MenuItem>
                  )}
                </Select>
              </FormControl>
              <FormControl
                margin="dense"
                variant="outlined"
                style={{
                  minWidth: 120,
                  marginLeft: 10
                }}
              >
                <InputLabel
                  htmlFor="outlined-age-native-simple"
                  margin="dense"
                  style={{ marginTop: -4 }}
                >
                  Work Mix %
                </InputLabel>
                <Select
                  margin="dense"
                  variant="outlined"
                  label="Filter By"
                  name="filter1"
                  value={this.state.selectValue}
                  onChange={this.handleChange}
                >
                  {/* <MenuItem value="None">None</MenuItem> */}
                  {/* <MenuItem value="Main">{'> 2'}</MenuItem> */}
                  <MenuItem value="Main">{'+2%'}</MenuItem>
                  <MenuItem value="other">{this.state.ddLabel}</MenuItem>
                </Select>
              </FormControl>
              <Typography
                variant="h14"
                color="primary"
                style={{
                  textTransform: 'none',
                  float: 'right',
                  marginTop: 5,
                  marginRight: 223,
                  fontSize: 12
                }}
              >
                Ranking Per Row
              </Typography>
              <div
                style={{
                  height: 10,
                  width: 200,
                  background:
                    'linear-gradient(to right, #003d6b 0%, #054372, #064677,#0f5285,#165a8e,#1e6296,#2772ac,#347fb8,#468cc1,#5797c7,#6baad8,#88bce3,#b5daf6)',
                  float: 'right',
                  marginTop: 22,
                  marginRight: -200
                }}
              ></div>
              <Typography
                variant="h18"
                color="primary"
                style={{
                  textTransform: 'none',
                  float: 'right',
                  fontSize: 10,
                  color: 'black',
                  marginTop: 33,
                  marginRight: -20,
                  verticalAlign: 'bottom'
                }}
              >
                High
              </Typography>
              <Typography
                variant="h18"
                color="primary"
                style={{
                  textTransform: 'none',
                  float: 'right',
                  fontSize: 10,
                  color: 'black',
                  marginTop: 32,
                  marginRight: -199,
                  verticalAlign: 'bottom'
                }}
              >
                Low
              </Typography>
            </Paper>
          </React.Fragment>
        )}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            height: window.innerHeight - 210 + 'px',
            // height:(window.innerHeight-215)+'px',
            width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  back: {
    marginRight: 5,
    width: '10%',
    marginTop: 8
  },
  formControl: {
    margin: 8
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  gridContainer: {
    display: 'flex'
  },
  reset: {
    width: 'auto',
    alignSelf: 'center'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  },
  workmixBackButton: {
    // width: '5%'
  },
  resetBtn: {
    marginRight: 10,
    float: 'right',
    marginTop: 10,
    width: 'auto'
  }
});

export default withStyles(styles)(WorkMixVolume);
