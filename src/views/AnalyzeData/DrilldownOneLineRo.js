import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import * as ExcelJS from 'exceljs';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import SaveIcon from '@material-ui/icons/Save';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import {
  Typography,
  FormControl,
  InputLabel,
  LinearProgress,
  Box,
  Paper,
  MenuItem,
  Select,
  Tooltip,
  FormControlLabel,
  RadioGroup,
  Radio,
  Link
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import RestoreIcon from '@material-ui/icons/Restore';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getKpiToggleOptionsWithTimeZone,
  getDataForOneLineRoDrilldown,
  getLatestClosedDate,
  getEmail,
  insertKpiOnelineSavedReport
} from 'src/utils/hasuraServices';

import SaveReportDialog from 'src/components/SaveReportDialog';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import { withStyles } from '@material-ui/styles';
import { getLast13Months, getTimeZone } from 'src/utils/Utils';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import { ReactSession } from 'react-client-session';
import { getYearValue } from 'src/utils/Utils';
import ExportIcon from '@material-ui/icons/GetApp';
import PictureAsPdfIcon from '@material-ui/icons/PictureAsPdf';
import $ from 'jquery';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import MailIcon from '@material-ui/icons/Mail';
import EmailDialogKpi from 'src/components/EmailDialogKpi';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import AlertSnackbar from 'src/components/AlertSnackbar';
var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

class DrilldownOneLineRo extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ Technicians: ['All'] });
    this.getKpiToggleOptions();
  }
  componentDidUpdate() {
    const newAdvisors = ReactSession.get('serviceAdvisors');
    const newTech = ReactSession.get('technicians');
    if (
      (ReactSession && ReactSession.get('serviceAdvisors') != undefined) ||
      (ReactSession && ReactSession.get('technicians') != undefined) ||
      this.state.filterChanged
    ) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisors,
        ReactSession.get('serviceAdvisors')
      );
      var checkStatusTech = lodash.isEqual(
        this.state.Technicians,
        ReactSession.get('technicians')
      );

      if (
        (checkStatus == false && $('#advisorSelection').hasClass('selected')) ||
        (checkStatusTech == false &&
          $('#techSelection').hasClass('selected')) ||
        this.state.filterChanged == true
      ) {
        /* this.setState({ filterChanged: false });
        this.setState({ advisors: ReactSession.get('serviceAdvisors') });
        this.setState({ tech: ReactSession.get('technicians') });
        this.getAgGridData(
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          this.state.filterStart,
          this.state.filterEnd
        );*/
        this.setState(
          {
            filterChanged: false,
            advisors: newAdvisors,
            tech: newTech
          },
          () => {
            this.getAgGridData(
              newAdvisors,
              newTech,
              this.state.filterStart,
              this.state.filterEnd
            );
          }
        );
      }
    }

    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ showBackButton: false });
        if (this.state.store != undefined) {
          window.sortStateOneLineRo = {};
          window.filterStateOneLineRo = {};
          this.state.gridColumnApi &&
            this.state.gridColumnApi.resetColumnState();
          if (this.gridApi) {
            this.gridApi.setSortModel(null);
            this.gridApi.setFilterModel(null);
          }
          if (this.state.gridColumnApi) {
            const groupColumns = this.state.groupColumn;
            this.state.rawGridApi.setColumnDefs(groupColumns);
            setTimeout(() => {
              this.state.gridColumnApi.resetColumnState();
            }, 1000);
          }
        }

        if (this.state.store != undefined) {
          this.setState({
            checked: false,
            parent: ''
          });
        }
        getLatestClosedDate(result => {
          if (result) {
            var openDate = '';
            var Date1 = result[0].value;
            localStorage.setItem('closedDate', Date1);
            this.setState({ closedDate: Date1 });
          }
        });
        this.setState(
          {
            advisors: ['All'],
            tech: ['All']
          },
          () => {
            this.getAgGridData(
              ReactSession.get('serviceAdvisors'),
              ReactSession.get('technicians'),
              this.state.filterStart,
              this.state.filterEnd
            );
          }
        );

        this.setState({
          store: localStorage.getItem('selectedStoreId')
        });
      }
    }
  }
  constructor(props) {
    super(props);
    let startDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterStart
        ? this.props.history.location.state.filterStart
        : ReactSession.get('kpiToggleStartDate') != ''
        ? ReactSession.get('kpiToggleStartDate')
        : '';

    let endDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterEnd
        ? this.props.history.location.state.filterEnd
        : ReactSession.get('kpiToggleEndDate') != ''
        ? ReactSession.get('kpiToggleEndDate')
        : '';
    let oneLineType =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.oneLineType
        ? this.props.history.location.state.oneLineType
        : this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : 'Under 60K';
    let closedDate = localStorage.getItem('closedDate');
    // let filterText = ReactSession.get('kpiHomeToggle');

    let created_by =
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.created_by
        ? props.history.location.state.created_by
        : '';
    let filterText =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterText
        ? this.props.history.location.state.filterText
        : ReactSession.get('kpiHomeToggle')
        ? ReactSession.get('kpiHomeToggle')
        : localStorage.getItem('kpiHomeToggle');
    let filterColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterColumns
        ? this.props.history.location.state.filterColumns
        : {};
    let sortColumn =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.sortColumns
        ? this.props.history.location.state.sortColumns
        : {};
    let advisor =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedAdvisor
        ? this.props.history.location.state.selectedAdvisor
        : ReactSession.get('serviceAdvisors')
        ? ReactSession.get('serviceAdvisors')
        : null;
    let technicians =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedTech
        ? this.props.history.location.state.selectedTech
        : ReactSession.get('technicians')
        ? ReactSession.get('technicians')
        : null;
    let checkedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.checkedColumns
        ? this.props.history.location.state.checkedColumns
        : 'All';
    let draggedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.draggedColumn
        ? this.props.history.location.state.draggedColumn
        : [];
    let parent =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.parent
        ? this.props.history.location.state.parent
        : '';
    let visibility =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.visibility
        ? this.props.history.location.state.visibility
        : 'private';
    let reportName =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.report_name
        ? this.props.history.location.state.report_name
        : '';
    let createdBy =
      props && props.kpiReportData && props.kpiReportData.created_by;
    //end of  saved report
    this.state = {
      selectedStoreIds: JSON.parse(localStorage.getItem('selectedStoreId')),
      headerData: [],
      rowDataPdf: [],
      iKpiReportType:
        oneLineType == 'Under 60K'
          ? 'CP_1-Line-RO_Count_Under_60k'
          : 'CP_1-Line-RO_Count_Over_60k',
      mailUsers: [],
      openDialogue: false,
      createdBy: created_by,
      store: localStorage.getItem('selectedStoreId')
        ? localStorage.getItem('selectedStoreId')
        : undefined,
      showBackButton:
        this.props.history &&
        this.props.history.location.state &&
        this.props.history.location.state.parent == 'Home'
          ? true
          : false,
      showSummaryValues: true,
      isLoading: true,
      monthYear: {},
      dates: [],
      lastWeek: '',
      yesterDay: '',
      today: '',
      dayBfYest: '',
      mtd: '',
      setMtd: '',
      lastMonth: '',
      lastThreeMonths: '',
      lastQtr: '',
      ytd: '',
      lastTwelveMonths: '',
      lastYear: '',
      filterStart: startDate,
      filterEnd: endDate,
      filterChanged: false,
      oneLineType: oneLineType,
      closedDate: closedDate,
      milageBelow60K: 0,
      milageAbove60K: 0,
      lbrSale: 0,
      lbrSoldHrs: 0,
      filterText: filterText,
      rowDataTech: [],
      openSaveDlg: false,
      reportName: reportName,
      errorReport: '',
      selectedType: visibility,
      openSusSnackbar: false,
      requiredText: false,
      displayCol: checkedColumns,
      draggedColumn: draggedColumns,
      advisors: advisor,
      tech: technicians,
      parent: parent,
      filterCol: filterColumns,
      sortCol: sortColumn,
      copy: false,
      copyFile: false,
      reportNameCopy: '',
      openDilog: false,
      openDilogAll: false,
      openAlert: false,
      zeroFilter: false,
      columnDefs: [
        {
          headerName: 'RO',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',

          // width: realm != 'haleyag' ? 83 : 110,
          minWidth: 85,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          //   hide: drillDown == 33 ? true : false,
          onCellClicked: this.handleSearchByRo,
          unSortIcon: true,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Month',
          field: 'month_year',
          // width: 85,
          minWidth: 85,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          valueFormatter: this.formatCellValueMonthYear,
          filter: 'agSetColumnFilter',

          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          // width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Advisor',
          field: 'servadvisorname',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          minWidth: 145,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          unSortIcon: true,
          tooltipField: 'servadvisorname',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          suppressMenu: true
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // }
        },
        {
          headerName: 'Tech',
          filter: 'agSetColumnFilter',
          field: 'techname',
          minWidth: 150,
          cellClass: 'textAlign',
          unSortIcon: true,
          suppressMenu: true,
          flex: 1,
          sortable: true,
          comparator: this.customComparator,
          chartDataType: 'category',
          tooltipField: 'techname',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Job Line',
          field: 'labor_code',
          minWidth: 80,
          flex: 1,
          hide: true,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          minWidth: 81,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          }
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          // width: 90,
          minWidth: 90,
          flex: 1,
          cellClass: 'textAlignCenter',
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          minWidth: 95,
          flex: 1,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          // width: 150,
          chartDataType: 'category',
          hide: true,
          minWidth: 150,
          flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          }
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          minWidth: 110,
          flex: 1,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          }
        },

        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          minWidth: 68,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,

          filterParams: {
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            },
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            },
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          aggFunc: 'sum',
          valueFormatter: this.formatCellValueWithOut$,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueWithOut$,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          minWidth: 80,
          flex: 1,
          hide: true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          minWidth: 80,
          flex: 1,
          hide: true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP',
          field: 'lbrgrossprofit',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          hide: true,
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Labor GP %',
          field: 'lbr_grossprofitpercentage',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueGP,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'oneDecimalPlace',
          hide: true,
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWith$',
          aggFunc: 'sum',
          hide: true,
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Parts GP %',
          field: 'prts_grossprofitpercentage',
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueGP,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'oneDecimalPlace',
          hide: true,
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Mileage',
          field: 'mileage',
          minWidth: 88,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueMilage,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMilage,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'commaSeparatedNumber'
        },
        {
          headerName: 'ELR',
          field: 'elr',
          // filter: 'agNumberColumnFilter',
          // width: 68,
          minWidth: 68,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValue,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          cellClass: 'twoDecimalPlacesWith$',
          hide: true,
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Markup',
          field: 'markup',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          cellClass: 'fourDecimalPlaces',

          hide: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMarkup,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Tech Id',
          field: 'lbrtechno',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'lbrtechno'
        },
        {
          headerName: 'Advisor Id',
          field: 'serviceadvisor',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'serviceadvisor'
        },
        {
          headerName: 'Customer Name',
          field: 'customer_name',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'customer_name'
        },
        {
          headerName: 'Vin',
          field: 'vin',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'vin'
        },
        {
          headerName: 'Op Subcategory',
          field: 'opsubcategory',
          minWidth: 120,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Make',
          field: 'make',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Model',
          field: 'model',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Year',
          field: 'year',
          minWidth: 95,
          flex: 1,
          chartDataType: 'series',
          hide: true,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      popupParent: document.body,
      headerHeight: 48,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true,
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              suppressColumnExpandAll: false
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        suppressMovable: false,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'commaSeparatedNumber',
          numberFormat: {
            format: '#,##0' // Comma formatting without decimal places
          },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'doorRate$',
          numberFormat: { format: '$#,##0' },
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'variance%',
          //  numberFormat: { format: '#0.00%' },
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'blueFont',
          font: {
            color: '0000FF'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          dataType: 'DateTime',
          numberFormat: {
            format: 'mm/dd/yy'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };

  formatCellValuePercent = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.00%';
    }
  };

  formatCellValueRate = params => {
    if (params.value != null && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };

  // onBtExport = () => {
  //   var params = {
  //     sheetName:
  //       this.state.oneLineType == 'Under 60K'
  //         ? 'CP 1-Line-RO Count Under 60K'
  //         : 'CP 1-Line-RO Count Over 60K',
  //     fileName:
  //       this.state.oneLineType == 'Under 60K'
  //         ? 'CP 1-Line-RO Count Under 60K'
  //         : 'CP 1-Line-RO Count Over 60K',
  //     processCellCallback: params => processCells(params),
  //     customHeader: [
  //       [],
  //       [
  //         {
  //           styleId: 'bigHeader',
  //           data: {
  //             type: 'String',
  //             value:
  //               this.state.oneLineType == 'Under 60K'
  //                 ? 'CP 1-Line-RO Count Under 60K'
  //                 : 'CP 1-Line-RO Count Over 60K'
  //           },
  //           mergeAcross: 2
  //         }
  //       ]
  //     ]
  //   };

  //   this.state.rawGridApi.exportDataAsExcel(params);
  // };
  exportToPDF = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.rowData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }
      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      console.log('ccccccc---', columns.length);

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };

      const doc = new jsPDF('l', 'mm', columns.length <= 30 ? 'a3' : 'a2');
      const marginLeft = 5;
      const marginTop = 10;

      doc.setFontSize(16);
      doc.text(
        this.state.parent == 'savedReports' &&
          this.state.oneLineType === 'Under 60K'
          ? `CP 1-Line-RO Count Under 60K - Saved Reports - ${this.props.history
              .location &&
              this.props.history.location.state &&
              this.props.history.location.state.report_name &&
              this.props.history.location.state.report_name}`
          : this.state.parent == 'savedReports' &&
            this.state.oneLineType === 'Over 60K'
          ? `CP 1-Line-RO Count Over 60K - Saved Reports - ${this.props.history
              .location &&
              this.props.history.location.state &&
              this.props.history.location.state.report_name &&
              this.props.history.location.state.report_name}`
          : this.state.oneLineType === 'Under 60K'
          ? 'CP 1-Line-RO Count Under 60K'
          : 'CP 1-Line-RO Count Over 60K',
        marginLeft,
        marginTop
      );
      const summaryLabels = [
        this.state.oneLineType == 'Under 60K'
          ? 'Mileage Under 60K:'
          : 'Mileage Over 60K:',
        'Labor Sale:',
        'Labor Sold Hours:'
      ];
      const summaryValues = [
        this.state.oneLineType == 'Under 60K'
          ? columns.length == 0
            ? 0
            : String(
                this.state.milageBelow60K
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              )
          : columns.length == 0
          ? 0
          : String(
              this.state.milageAbove60K
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            ),
        columns.length == 0 ? '$0' : '$' + this.state.lbrSale,
        columns.length == 0 ? '0.0' : this.state.lbrSoldHrs
      ];

      doc.setFontSize(11);
      let xPosition = marginLeft;
      const yPosition = 20;

      summaryLabels.forEach((label, index) => {
        doc.setFont(undefined, 'normal');
        doc.setTextColor('black');
        doc.text(label, xPosition, yPosition);
        xPosition += doc.getTextWidth(label) + 1;

        doc.setFont(undefined, 'bold');
        let valueNormal = String(summaryValues[index]).replace(/[$,]/g, '');
        let value = summaryValues[index];

        if (
          label === 'Over Sold:' ||
          label === 'Under Sold:' ||
          label === 'Net Difference:'
        ) {
          if (valueNormal < 0) {
            doc.setTextColor('red');
          } else if (valueNormal >= 0) {
            doc.setTextColor('blue');
          } else {
            doc.setTextColor('black');
          }
        } else {
          doc.setTextColor('black');
        }

        doc.text(String(value), xPosition, yPosition);
        xPosition += doc.getTextWidth(String(value)) + 8;
      });

      doc.setFont(undefined, 'normal');

      const headers = columns.map(col => col.header);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];

          if (
            [
              'lbrsale',
              'lbrcost',
              'prtextendedcost',
              'prtextendedsale',
              'lbrgrossprofit',
              'prtsgrossprofit',
              'elr'
            ].includes(col.field)
          ) {
            return this.formatCellValue({ value });
          } else if (
            [
              'lbr_grossprofitpercentage',
              'prts_grossprofitpercentage'
            ].includes(col.field)
          ) {
            return this.formatCellValueGP({ value });
          } else if (col.field === 'lbrsoldhours') {
            return this.formatCellValueWithOut$({ value });
          } else if (col.field === 'mileage') {
            return this.formatCellValueMilage({ value });
          } else if (col.field === 'markup') {
            return this.formatCellValueMarkup({ value });
          } else if (col.field === 'month_year') {
            return this.formatCellValueMonthYear({ value });
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }
        })
      );
      autoTable(doc, {
        startY: 24,
        head: [headers],
        body: data,
        theme: 'grid',
        styles: { fontSize: 9, cellPadding: 1.5, halign: 'left' },
        headStyles: {
          fillColor: '#c65911',
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 10,
          halign: 'left'
        },
        tableWidth: 'auto',
        margin: { top: 3, left: 3, right: 3 },
        horizontalPageBreak: false,
        scaleFactor: 1.2
      });

      doc.save(
        this.state.parent == 'savedReports' &&
          this.state.oneLineType === 'Under 60K'
          ? `CP 1-Line-RO Count Under 60K - Saved Reports - ${this.props.history
              .location &&
              this.props.history.location.state &&
              this.props.history.location.state.report_name &&
              this.props.history.location.state.report_name}`
          : this.state.parent == 'savedReports' &&
            this.state.oneLineType === 'Over 60K'
          ? `CP 1-Line-RO Count Over 60K - Saved Reports - ${this.props.history
              .location &&
              this.props.history.location.state &&
              this.props.history.location.state.report_name &&
              this.props.history.location.state.report_name}`
          : this.state.oneLineType === 'Under 60K'
          ? 'CP 1-Line-RO Count Under 60K'
          : 'CP 1-Line-RO Count Over 60K' + '.pdf'
      );
    }
  };

  onBtExport = async () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.rowData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      const workbook = new ExcelJS.Workbook();
      const sheetName =
        this.state.parent == 'savedReports'
          ? `CP 1-Line-RO Count ${
              this.state.oneLineType
            } - Saved Reports - ${this.props.history?.location?.state
              ?.report_name || ''}`
          : `CP 1-Line-RO Count ${this.state.oneLineType}`;

      const sheet = workbook.addWorksheet(sheetName);

      const gridApi = this.gridApi;
      const columnApi = this.gridColumnApi;
      const filteredAndSortedRows = gridApi
        .getModel()
        .rowsToDisplay.map(rowNode => rowNode.data);

      // ------- NEW Summary Section (Merged Cells) -------
      const summaryLabels = [
        this.state.oneLineType === 'Under 60K'
          ? 'Mileage Under 60K:'
          : 'Mileage Over 60K:',
        'Labor Sale:',
        'Sold Hours:'
      ];
      const summaryValues = [
        this.state.oneLineType === 'Under 60K'
          ? this.state.milageBelow60K
          : this.state.milageAbove60K,
        `$${this.state.lbrSale}`,
        this.state.lbrSoldHrs
      ];

      let colPointer = 1;
      summaryLabels.forEach((label, index) => {
        // Merge 2 columns per label block (A3:B3, D3:E3, etc.)
        sheet.mergeCells(3, colPointer, 3, colPointer + 1);
        const labelCell = sheet.getCell(3, colPointer);
        labelCell.value = `${label} ${summaryValues[index]}`;
        labelCell.font = { bold: true };
        labelCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'f8dbc9' }
        };
        labelCell.alignment = { horizontal: 'center', vertical: 'middle' };
        colPointer += 3;
      });
      // --------------------------------------------------

      const visibleColumns = columnApi.getAllDisplayedColumns();
      const columnMapping = visibleColumns.reduce((map, col) => {
        map[col.getColDef().headerName] = col.getColDef().field;
        return map;
      }, {});
      const dynamicHeaders = Object.keys(columnMapping);

      const numberFormatter = new Intl.NumberFormat();
      const maxColWidths = Array(dynamicHeaders.length).fill(0);

      dynamicHeaders.forEach((header, index) => {
        const cell = sheet.getCell(5, index + 1);
        cell.value = header;
        cell.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'c65911' }
        };
        maxColWidths[index] = header.length;
      });

      sheet.getCell(`A2`).value = 'Summary';
      sheet.getCell(`A2`).font = { bold: true, size: 13 };
      sheet.getCell(`A2`).alignment = {
        horizontal: 'left',
        vertical: 'middle'
      };
      sheet.mergeCells(`A2:${sheet.getCell(2, dynamicHeaders.length).address}`);

      filteredAndSortedRows.forEach((row, rowIndex) => {
        const values = dynamicHeaders.map((header, index) => {
          const field = columnMapping[header];
          let value = row[field];
          console.log('fff==f==', typeof value, '===', field);
          if (field === 'closeddate' || field === 'month_year') {
            const date = new Date(value);
            const formatted =
              field === 'closeddate'
                ? `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date
                    .getDate()
                    .toString()
                    .padStart(2, '0')}/${date
                    .getFullYear()
                    .toString()
                    .slice(-2)}`
                : `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date
                    .getFullYear()
                    .toString()
                    .slice(-2)}`;
            const estimatedExcelWidth = Math.ceil(
              value.toString().length * 1.2
            );
            maxColWidths[index] = Math.max(
              maxColWidths[index],
              estimatedExcelWidth
            );
            return formatted;
          }

          if (
            [
              'lbrsale',
              'lbrcost',
              'lbrgrossprofit',
              'elr',
              'prtextendedsale',
              'prtextendedcost',
              'prtsgrossprofit',
              'lbrsoldhours'
            ].includes(field)
          ) {
            value = value;
            // value = `$${numberFormatter.format(value)}`;
          } else if (['mileage'].includes(field)) {
            value = parseInt(value);
            // value = `$${numberFormatter.format(value)}`;
          } else if (
            [
              'lbr_grossprofitpercentage',
              'prts_grossprofitpercentage'
            ].includes(field)
          ) {
            value = value / 100; // Convert percentage string (25.0) to decimal (0.25)
            // value = `${numberFormatter.format(value)}%`;
          } else if (['markup'].includes(field)) {
            // value = value;
            value =
              value == '0' || value == '' || value == null ? '0.0000' : value;
          } else if (
            typeof value === 'number' &&
            !['mileage'].includes(field)
          ) {
            value = numberFormatter.format(value);
          }

          value = value ?? '';
          maxColWidths[index] = Math.max(
            maxColWidths[index],
            value.toString().length
          );
          return value;
        });

        const excelRow = sheet.addRow(values);

        dynamicHeaders.forEach((header, colIndex) => {
          const field = columnMapping[header];
          const cell = excelRow.getCell(colIndex + 1);
          console.log('field===', field);
          if (['ronumber'].includes(field)) {
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
          } else if (field === 'lbrsequenceno') {
            cell.alignment = { horizontal: 'left', vertical: 'middle' };
          } else if (
            [
              'lbr_grossprofitpercentage',
              'prts_grossprofitpercentage'
            ].includes(field)
          ) {
            cell.alignment = { horizontal: 'right', vertical: 'middle' };
            cell.numFmt = '0.0%'; // One decimal place percent format
          }
          if (['lbrsoldhours'].includes(field)) {
            cell.numFmt = '0.00';
            cell.alignment = { horizontal: 'right', vertical: 'middle' };
          }
          if (['mileage'].includes(field)) {
            cell.alignment = { horizontal: 'center', vertical: 'middle' };
            cell.numFmt = '#,##,##0'; // Indian-style grouping
          }

          if (['markup'].includes(field)) {
            cell.numFmt = '0.0000';
            cell.alignment = { horizontal: 'right', vertical: 'middle' };
          }
          if (
            [
              'lbrsale',
              'lbrcost',
              'lbrgrossprofit',
              'elr',
              'prtextendedsale',
              'prtextendedcost',
              'prtsgrossprofit'
            ].includes(field)
          ) {
            cell.numFmt = '"$"#,##0.00'; // Dollar sign with two decimal places
          }

          if ((rowIndex + 1) % 2 !== 0) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'f8dbc9' }
            };
          }
        });
      });

      // Custom width adjustments for specific headers
      dynamicHeaders.forEach((header, index) => {
        const column = sheet.getColumn(index + 1);

        if (header === 'RO') {
          column.width = 20;
        } else if (header === 'Pay Type Group') {
          column.width = 19;
        } else {
          column.width = Math.max(12, maxColWidths[index] + 4);
        }
      });

      sheet.mergeCells('A1:' + sheet.getCell(1, dynamicHeaders.length).address);
      const titleCell = sheet.getCell('A1');
      titleCell.value = sheetName;
      titleCell.font = { bold: true, size: 14 };
      titleCell.alignment = { horizontal: 'left', vertical: 'middle' };

      const buffer = await workbook.xlsx.writeBuffer();
      const blob = new Blob([buffer], { type: 'application/octet-stream' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${sheetName}.xlsx`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  componentDidMount() {
    if (this.state.selectedFilter == '')
      this.setState({ selectedFilter: 'duration' });
    if (this.state.selectedToggle == '')
      this.setState({ selectedToggle: 'MTD' });
    this.getMonthYearForSelect();
    this.getKpiToggleOptions();
    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousToggle &&
      this.props.history.location.state.previousToggle != 'undefined'
    ) {
      this.setState({
        previousToggle: this.props.history.location.state.previousToggle
      });
    }
  }

  getKpiToggleOptions = () => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        this.setState({
          dates: dataArr,
          lastWeek:
            moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY"),
          lastTwoWeek:
            moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY"),
          thisWeek:
            moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY"),
          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          mtd:
            moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY"),
          lastMonth: moment(dataArr[0].lastmonthstartdate).format('MMM'),
          lastThreeMonths:
            moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM'),
          lastQtr:
            moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM'),

          ytd:
            moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY"),
          lastTwelveMonths:
            moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY"),
          lastYear:
            moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        });
        if (this.state.filterStart == '' && this.state.filterEnd == '') {
          this.setState({
            filterStart: moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
            filterEnd: moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'),
            toggleOption: 'MTD'
          });

          if (localStorage.getItem('kpiDataStatus') == 1) {
            this.setState({
              filterStart: moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
              filterEnd: moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'),
              toggleOption: 'MTD'
            });
          } else {
            this.setState({
              filterStart: moment(dataArr[0].lastmonthstartdate).format(
                'YYYY-MM-DD'
              ),
              filterEnd: moment(dataArr[0].lastmonthenddate).format(
                'YYYY-MM-DD'
              ),
              toggleOption: 'LMONTH'
            });
          }
        }
        this.setState({ columnDefs: this.state.columnDefs });
        let selectedAdv =
          this.props?.history?.location?.state?.selectedAdvisor ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('serviceAdvisors'));
        //ReactSession.get('serviceAdvisors');

        let selectedTech =
          this.props?.history?.location?.state?.selectedTech ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('technicians'));
        //  ReactSession.get('technicians');

        this.setState({ advisors: selectedAdv });
        this.setState({ tech: selectedTech });
        this.getAgGridData(
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians')
          // this.state.advisors,
          // this.state.tech
        );
      }
    });
  };

  getMonthYearForSelect = () => {
    this.setState({
      monthYear: getLast13Months()
    });
    if (
      (this.state.monthYear && this.state.selectedMonthYear == '') ||
      this.state.selectedMonthYear == undefined ||
      !this.state.selectedMonthYear
    )
      this.setState({
        selectedMonthYear: this.state.monthYear[this.state.monthYear.length - 1]
      });
  };

  handleclick = params => {
    this.props.history.push({
      pathname: '/Home',
      state: {
        toggleOptions: this.state.previousToggle
          ? this.state.previousToggle
          : 'MTD',
        payType: this.state.PrevPayType,
        gridType: this.state.PrevGridType,
        parent: this.state.parent,
        filterStart: this.state.filterStart,
        filterEnd: this.state.filterEnd
      }
    });
  };

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };

  customComparator = (valueA, valueB) => {
    // Handling null values
    if (valueA === null && valueB === null) {
      return 0;
    }
    if (valueA === null) {
      return -1;
    }
    if (valueB === null) {
      return 1;
    }

    const nameA = valueA.split('[')[0].trim();
    const nameB = valueB.split('[')[0].trim();

    return nameA.localeCompare(nameB);
  };

  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };

  formatCellValueMilage = params => {
    if (params.value != null && params.value != 0) {
      return params.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0' + '%';
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };
  formatCellValueMarkupMail = value => {
    console.log('value===', value.value);
    if (value != null && value != 0) {
      return parseFloat(value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };
  onFirstDataRendered = params => {
    const rowCount = params.api.getDisplayedRowCount();
    if (rowCount == 0) {
      this.setState({ showSummaryValues: false });
    } else {
      this.setState({ showSummaryValues: true });
    }
  };

  onModelUpdated = params => {
    const rowCount = params.api.getDisplayedRowCount();
    if (rowCount == 0) {
      this.setState({ showSummaryValues: false });
    } else {
      this.setState({ showSummaryValues: true });
    }
  };

  onColumnVisible = params => {
    const columnApi = params.columnApi || this.gridColumnApi;

    if (!columnApi) {
      console.error('columnApi is undefined');
      return;
    }

    const allColumnsHidden = columnApi
      .getAllColumns()
      .every(col => !col.isVisible());
    const visibleColumns = columnApi.getAllDisplayedColumns();
    const colIds = visibleColumns.map(col => col.colId);
    this.setState({ displayCol: colIds });
    if (allColumnsHidden) {
      this.setState({ showSummaryValues: false });
    } else {
      this.setState({ showSummaryValues: true });
    }
  };

  onColumnMoved = params => {
    const columnOrder = params.columnApi
      .getAllGridColumns()
      .map(col => col.getColId());
    this.setState({ draggedColumn: columnOrder });
  };

  onGridReady = params => {
    const transformedFilterState = Object.fromEntries(
      Object.entries(this.state.filterCol).map(([key, values]) => [
        key,
        { values, filterType: 'set' }
      ])
    );
    const transformedSortState = [
      {
        colId: this.state.sortCol[0],
        sort: this.state.sortCol[1]
      }
    ];

    params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.setState({ gridApi: params.api });
    this.setState({ rawGridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });

    this.setState({
      groupColumn: params.api.columnController.columnDefs
    });
    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null ||
      this.props.history.location.state.ronumber == undefined
    ) {
      window.sortStateOneLineRo = {};
      window.filterStateOneLineRo = {};
    }
    // this.getAgGridData(this.state.value);
    if (
      this.gridColumnApi &&
      window.colStateOneLineRo &&
      (this.props.history == undefined ||
        (this.props.history &&
          this.props.history.location &&
          this.props.history.location.state == undefined) ||
        this.props.history.location.state.ronumber == undefined)
    ) {
      window.colStateOneLineRo = null;
      this.state.gridColumnApi.resetColumnState();
    }
    if (window.colStateOneLineRo && this.gridColumnApi) {
      this.gridColumnApi.setColumnState(window.colStateOneLineRo);
      // this.gridApi.setSortModel(window.sortStateOneLineRo);
      // this.gridApi.setFilterModel(window.filterStateOneLineRo);
    }
    this.getKpiToggleOptions();
    this.gridApi.setSortModel(window.sortStateOneLineRo);
    this.gridApi.setFilterModel(window.filterStateOneLineRo);

    if (this.state.parent == 'savedReports') {
      window.filterStateOneLineRo = transformedFilterState;
      window.sortStateOneLineRo = transformedSortState;

      if (this.state.draggedColumn.length > 0) {
        window.colStateOneLineRo = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();

        // Reorder columns based on desiredOrder
        const orderedState = this.state.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col ? { ...col, order: index } : null;
          })
          .filter(Boolean);
        // Apply reordered column state
        this.gridColumnApi.setColumnState(orderedState);
      }

      if (this.state.displayCol.length > 0) {
        if (this.state.displayCol.includes('All')) {
        } else {
          window.colStateOneLineRo = this.gridColumnApi.getColumnState();
          const visibleColumns = this.state.columnDefs.filter(column =>
            this.state.displayCol.includes(column.field)
          );
          const visibleFields = visibleColumns.map(col => col.field);
          const updatedColState = window.colStateOneLineRo.map(col => ({
            ...col,
            hide: !visibleFields.includes(col.colId) // hide true for matched columns
          }));
          this.gridColumnApi.setColumnState(updatedColState);
        }
      }
      this.gridApi.setFilterModel(window.filterStateOneLineRo);
      this.gridApi.setSortModel(window.sortStateOneLineRo);
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueMonth = value => {
    if (value != null && value != '') {
      return moment(value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  formatCellValueWithOut$Pdf = params => {
    console.log('params===', params);
    if (params != null && params != 0) {
      return parseFloat(params)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  getAgGridData(advisor, tech) {
    let resultArr = [];
    this.setState({ isLoading: true });
    advisor = advisor ? advisor : ['All'];
    this.setState({ serviceAdvisors: advisor });
    this.setState({ Technicians: tech });
    let laborSale = 0;
    let laborSoldHrs = 0;
    let filtered = [];

    getDataForOneLineRoDrilldown(
      this.state.filterStart,
      this.state.filterEnd,
      this.state.oneLineType == 'Under 60K' ? 'YES' : 'NO',
      //tech,
      // advisor,
      this.state.tech
        ? //&& !this.state.tech.includes('All')
          this.state.tech
        : tech,
      this.state.advisors
        ? //&& !this.state.advisors.includes('All')
          this.state.advisors
        : advisor,
      result => {
        if (
          result.data.statelessDbdKpiScorecardGetKpiOnelineDrilldown
            .kpiScorecardDetails
        ) {
          this.setState({ isLoading: false });

          if (
            result.data.statelessDbdKpiScorecardGetKpiOnelineDrilldown
              .kpiScorecardDetails[0] == null
          ) {
            this.setState({
              rowData: [],
              milageBelow60K: 0,
              milageAbove60K: 0,
              lbrSale: 0,
              lbrSoldHrs: 0
            });
            this.setState({ filterChanged: false });
          } else {
            resultArr =
              result.data.statelessDbdKpiScorecardGetKpiOnelineDrilldown
                .kpiScorecardDetails[0].jsonData;
            this.setState({
              rowData: JSON.parse(resultArr)
            });
            tech = tech ? tech : ['All'];

            if (tech.includes('All') && advisor.includes('All')) {
              filtered = JSON.parse(resultArr);
            } else {
              filtered = JSON.parse(resultArr).filter(
                item =>
                  tech.includes(item.lbrtechno) ||
                  advisor.includes(item.serviceadvisor)
              );
            }

            // let dataArr = JSON.parse(resultArr);
            let dataArr =
              filtered.length > 0 ? filtered : JSON.parse(resultArr);
            dataArr.forEach(function(value) {
              laborSale += value.lbrsale;
              laborSoldHrs += value.lbrsoldhours;
            });

            if (this.state.oneLineType == 'Under 60K') {
              this.setState({ milageBelow60K: JSON.parse(resultArr).length });
            } else {
              this.setState({ milageAbove60K: JSON.parse(resultArr).length });
            }
            this.setState({ filterChanged: false });

            this.setState({
              lbrSale: laborSale
                .toFixed(0)
                .replace(/\B(?=(\d{3})+(?!\d))/g, ','),
              lbrSoldHrs: laborSoldHrs
                .toFixed(1)
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            });
            if (window.filterStateOneLineRo != undefined) {
              this.filterByValue();
            }
          }
        } else {
          this.setState({
            rowData: [],
            milageBelow60K: 0,
            milageAbove60K: 0,
            lbrSale: 0,
            lbrSoldHrs: 0
          });
          this.setState({ filterChanged: false });
        }
      }
    );
  }

  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    this.getTotalsForDisplay(rowData);
  };

  resetRawData = () => {
    if (this.state.parent == 'savedReports') {
      const propsData = this.props.history && this.props.history.location.state;
      this.setState({
        isLoading: true
      });
      const transformedFilterState = Object.fromEntries(
        Object.entries(propsData.filterColumns).map(([key, values]) => [
          key,
          { values, filterType: 'set' }
        ])
      );
      const transformedSortState = [
        {
          colId: propsData.sortColumns[0],
          sort: propsData.sortColumns[1]
        }
      ];
      window.filterStateOneLineRo = transformedFilterState;
      window.sortStateOneLineRo = transformedSortState;
      this.gridApi.setFilterModel(window.filterStateOneLineRo);
      this.gridApi.setSortModel(window.sortStateOneLineRo);
      if (propsData.draggedColumn.length > 0) {
        window.colStateOneLineRo = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();
        const orderedState = propsData.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col ? { ...col, order: index } : null;
          })
          .filter(Boolean);
        window.colStateOneLineRo = orderedState;
        this.gridColumnApi.setColumnState(window.colStateOneLineRo);
      }

      if (propsData.checkedColumns.length > 0) {
        window.colStateOneLineRo = this.gridColumnApi.getColumnState();
        const visibleColumns = this.state.columnDefs.filter(column =>
          propsData.checkedColumns.includes(column.field)
        );
        const visibleFields = visibleColumns.map(col => col.field);
        const updatedColState = window.colStateOneLineRo.map(col => ({
          ...col,
          hide: !visibleFields.includes(col.colId)
        }));
        window.colStateOneLineRo = updatedColState;
        this.gridColumnApi.setColumnState(window.colStateOneLineRo);
      }

      this.setState({ tech: propsData.selectedTech });
      this.setState({ advisors: propsData.selectedAdvisor });
      this.setState({ selectedType: propsData.visibility });
      this.setState({ filterText: propsData.filterText });
      this.setState({ zeroFilter: false });
      setTimeout(() => {
        this.setState({ filterStart: propsData.filterStart });
        this.setState({ filterEnd: propsData.filterEnd });
        this.setState({
          isLoading: false
        });
      }, 500);
    } else {
      window.sortStateOneLineRo = {};
      window.filterStateOneLineRo = {};
      this.state.gridColumnApi && this.state.gridColumnApi.resetColumnState();
      this.setState({ zeroFilter: false });
      if (this.gridApi) {
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
      }
    }
  };
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateOneLineRo);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    console.log('filterrCC', filterValues);
    Object.keys(filterValues).forEach(field => {
      const filter = filterValues[field];
      if (filter?.filterType === 'set') {
        const selectedValues = filter.values || [];
        if (selectedValues.length === 0) {
          this.setState({ zeroFilter: true });
        } else {
          this.setState({ zeroFilter: false });
        }
      }
    });
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    this.getTotalsForDisplay(rowData);
  };

  getTotalsForDisplay = data => {
    var rowData = data ? data : this.state.rowData;
    var laborSoldHrs = 0;
    var laborSale = 0;
    let filteredTech = [];
    let advisorArr = ReactSession.get('serviceAdvisors')
      ? ReactSession.get('serviceAdvisors')
      : ['All'];
    let techArr = ReactSession.get('technicians')
      ? ReactSession.get('technicians')
      : ['All'];
    if (rowData.length > 0) {
      if (this.state.oneLineType == 'Under 60K') {
        this.setState({ milageBelow60K: rowData.length });
      } else {
        this.setState({ milageAbove60K: rowData.length });
      }
      if (techArr.includes('All') && advisorArr.includes('All')) {
        filteredTech = rowData;
      } else {
        filteredTech = rowData.filter(
          item =>
            techArr.includes(item.lbrtechno) ||
            advisorArr.includes(item.serviceadvisor)
        );
      }
      let dataArr = filteredTech.length > 0 ? filteredTech : rowData;
      if (dataArr.length > 0) {
        dataArr.map(item => {
          laborSoldHrs += Number(item.lbrsoldhours);
          laborSale += Number(item.lbrsale);
        });
      }
    } else {
      this.setState({ milageBelow60K: 0, milageAbove60K: 0 });
    }
    this.setState({
      lbrSale: laborSale.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ','),
      lbrSoldHrs: laborSoldHrs.toFixed(1).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    });
  };
  handleCallback = (event, picker) => {
    this.setState({ filterStart: picker.startDate.format('YYYY-MM-DD') });
    this.setState({ filterEnd: picker.endDate.format('YYYY-MM-DD') });
    this.setState({ filterChanged: true });
    this.getFilterText(picker.chosenLabel);
    // setFilterChanged(true);
  };
  getFilterText = label => {
    var filterText;

    if (label.includes('Yesterday')) {
      filterText = 'YESDT';
    } else if (label.includes('Day Before Yest')) {
      filterText = 'DBYESDT';
    } else if (label.includes('Last Week')) {
      filterText = 'LWEEK';
    } else if (label.includes('This Month')) {
      filterText = 'MTD';
    } else if (label.includes('Last Month')) {
      filterText = 'LMONTH';
    } else if (label.includes('Last 3 Mths')) {
      filterText = 'PLMTHREE';
    } else if (label.includes('Last Qtr')) {
      filterText = 'LQRTR';
    } else if (label.includes('YTD')) {
      filterText = 'YTD';
    } else if (label.includes('Last 12 Mths')) {
      filterText = 'PLYONE';
    } else if (label.includes('Last Year')) {
      filterText = 'LYEAR';
    } else if (label.includes('Custom Range')) {
      filterText = 'CRANGE';
    }
    this.setState({ filterText: filterText });
    ReactSession.set('kpiHomeToggle', filterText);
    return filterText;
  };
  handleSearchByRo = params => {
    let filterColumn = {};
    let sortColumns = {};
    window.sortStateOneLineRo = this.gridApi.getSortModel();
    window.filterStateOneLineRo = this.gridApi.getFilterModel();
    if (
      window.filterStateOneLineRo != undefined ||
      window.sortStateOneLineRo != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateOneLineRo)
      );
      sortColumns = this.transformSortData(window.sortStateOneLineRo);
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    window.filterState = this.gridApi.getFilterModel();
    window.colStateOneLineRo = this.gridColumnApi.getColumnState();
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        filterStart: this.state.filterStart,
        filterEnd: this.state.filterEnd,
        pageType: 'onelineDrilldown',
        type: 'one_line_drilldown',
        oneLineType: this.state.oneLineType,
        reportName: this.state.reportName,
        parent:
          this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.parent
            ? this.props.history.location.state.parent
            : '',
        filterText: this.state.filterText,
        visibility: this.state.selectedType,
        checkedColumns: this.state.displayCol,
        draggedColumn: this.state.draggedColumn,
        selectedAdvisor: this.state.advisors,
        selectedTech: this.state.tech,

        filterColumns: this.state.filterCol,
        sortColumns: sortColumns
      }
    });
  };

  renderBackButton = event => {
    ReactSession.set('kpiHomeToggle', this.state.filterText);
    if (
      this.state.showBackButton == true &&
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.parent == 'Home'
    ) {
      this.props.history.push({
        pathname: '/Home',
        state: {
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd
        }
      });
    } else {
      this.props.history.push({
        pathname: '/ReportSaved',
        state: {
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd
        }
      });
    }
  };
  handleSaveReport = () => {
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.props.parentSelected !== 'savedReports'
    ) {
      this.setState({ openDilogAll: true });
    } else if (this.state.filterText === 'CRANGE') {
      this.setState({ openDilog: true });
    } else if (
      this.state.displayCol.length == 0 ||
      this.state.rowData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({ openSaveDlg: true });

      if (this.props.parentSelected === 'savedReports') {
        this.setState({ reportName: this.props.reportName });
      }
    }
  };

  formatMileage = value => {
    if (value != null && value != 0) {
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0';
    }
  };
  handleMailClick = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.rowData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({ openDialogue: true });

      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }
      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };
      const headers = columns.map(col => col.header);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];

          if (
            [
              'prtlist',
              'prtcost',
              'lbrsale',
              'prtextendedcost',
              'prtextendedsale',
              'lbrgrossprofit',
              'elr',
              'lbrcost',
              'prtsgrossprofit'
            ].includes(col.field)
          ) {
            return this.formatCellValue({ value });
          } else if (
            ['targetPrice', 'targetExtendedPrice'].includes(col.field)
          ) {
            return this.formatCellValueTargetPrice({ value });
          } else if (
            col.field === 'grossprofitpercentage' ||
            col.field === 'lbr_grossprofitpercentage' ||
            col.field === 'prts_grossprofitpercentage'
          ) {
            return this.formatCellValueGP({ value });
          } else if (col.field === 'variance') {
            const formattedValue = this.formatCellValueVariance({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'variancePerc') {
            const formattedValue = this.formatCellValuePercent({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'markup') {
            return this.formatCellValueMarkup({ value });
          } else if (col.field === 'doorRate') {
            return this.formatCellValueRate({ value });
          } else if (col.field === 'mileage') {
            return this.formatMileage(value);
          } else if (col.field === 'lbrsoldhours') {
            return this.formatCellValueWithOut$Pdf(value);
          } else if (col.field === 'month_year') {
            return this.formatCellValueMonth(value);
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }
        })
      );
      console.log('data===', data);
      this.setState({ rowDataTotal: rowData });
      this.setState({ headerData: headers });
      this.setState({ rowDataPdf: data });
      let userList = [];
      getEmail('NULL', 'NULL', 'view', 'Client_Report_Card_1_Month', result => {
        if (
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
            0
        ) {
          userList.push(
            result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
              item => item.value
            )
          );
          this.setState({ mailUsers: userList });
        } else {
          this.setState({ mailUsers: [] });
        }
      });
    }
  };

  CancelDilog = () => {
    this.setState({
      openDilog: false
    });
  };

  CancelDilogAll = () => {
    this.setState({
      openDilogAll: false
    });
  };

  handleCloseEmail = () => {
    this.setState({ openDialogue: false });
  };
  handleCancelSaveReport = () => {
    this.setState({
      openSaveDlg: false
    });
    this.setState({
      copyFile: false
    });
    this.setState({
      copy: false
    });
    if (this.state.parent != 'savedReports') {
      this.setState({
        reportName: ''
      });
      this.setState({
        selectedType: 'private'
      });
    } else {
      this.setState({
        selectedType:
          this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.visibility
      });
    }
  };

  onChangeReportName = e => {
    console.log('SaveReportDialog-----e.target.value', e.target.value);
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;
    if (!nameRegex.test(e.target.value) && e.target.value) {
      this.setState({ requiredText: '' });
    } else {
      this.setState({
        errorReport: ''
      });
      this.setState({
        reportName: e.target.value
      });

      this.setState({ requiredText: false });
    }
  };
  handleCheckboxChange = e => {
    this.setState({
      selectedType: e.target.value
    });
  };
  transformData = data => {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, value.values])
    );
  };
  transformSortData = data => {
    return data.flatMap(({ colId, sort }) => [colId, sort]);
  };

  handleOkSaveReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    window.filterStateOneLineRo = this.gridApi.getFilterModel();
    window.sortStateOneLineRo = this.gridApi.getSortModel();

    if (
      window.filterStateOneLineRo != undefined ||
      window.sortStateOneLineRo != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateOneLineRo)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStateOneLineRo)
      );
    } else {
      filterColumn = null;
      sortColumns = null;
    }

    if (this.state.parent == 'savedReports') {
      insertKpiOnelineSavedReport(
        'update',
        iStoreId,
        this.state.reportName,
        this.state.filterText,
        this.state.oneLineType == 'Under 60K'
          ? 'CP_1-Line-RO_Count_Under_60k'
          : 'CP_1-Line-RO_Count_Over_60k',
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        userId,
        userRole,
        this.state.selectedType,
        this.state.displayCol,
        sortColumns,
        filterColumn,
        this.state.draggedColumn,
        this.state.oneLineType == 'Under 60K' ? 'YES' : 'NO',
        'S',
        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpiOnelineSavedReport
              .results[0].status == 1
          ) {
            console.log('successs------1233------------->');
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data.statelessDbdKpiScorecardInsertKpiOnelineSavedReport
                  .results[0].msg
            });
          }
        }
      );
    } else {
      insertKpiOnelineSavedReport(
        'insert',
        iStoreId,
        this.state.reportName,
        this.state.filterText,
        this.state.oneLineType == 'Under 60K'
          ? 'CP_1-Line-RO_Count_Under_60k'
          : 'CP_1-Line-RO_Count_Over_60k',
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        userId,
        userRole,
        this.state.selectedType,
        this.state.displayCol,
        sortColumns,
        filterColumn,
        this.state.draggedColumn,
        this.state.oneLineType == 'Under 60K' ? 'YES' : 'NO',
        'S',
        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpiOnelineSavedReport
              .results[0].status == 1
          ) {
            console.log('successs------1233------------->');
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data.statelessDbdKpiScorecardInsertKpiOnelineSavedReport
                  .results[0].msg
            });
          }
        }
      );
    }
  };
  handleSaveAsReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    window.filterStateOneLineRo = this.gridApi.getFilterModel();
    window.sortStateOneLineRo = this.gridApi.getSortModel();

    if (
      window.filterStateOneLineRo != undefined ||
      window.sortStateOneLineRo != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateOneLineRo)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStateOneLineRo)
      );
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    insertKpiOnelineSavedReport(
      'insert',
      iStoreId,
      this.state.reportName,
      this.state.filterText,
      this.state.oneLineType == 'Under 60K'
        ? 'CP_1-Line-RO_Count_Under_60k'
        : 'CP_1-Line-RO_Count_Over_60k',
      this.state.advisors == undefined || this.state.advisors.includes('All')
        ? null
        : this.state.advisors,
      this.state.tech == undefined || this.state.tech.includes('All')
        ? null
        : this.state.tech,
      userId,
      userRole,
      this.state.selectedType,
      this.state.displayCol,
      sortColumns,
      filterColumn,
      this.state.draggedColumn,
      this.state.oneLineType == 'Under 60K' ? 'YES' : 'NO',
      'S',
      result => {
        if (
          result.data.statelessDbdKpiScorecardInsertKpiOnelineSavedReport
            .results[0].status == 1
        ) {
          console.log('successs------1233------------->');
          this.setState({
            openSaveDlg: false
          });
          this.setState({
            copy: false
          });
          this.setState({
            errorReport: ''
          });
          this.setState({ openSusSnackbar: true });
          this.setState({ requiredText: false });
        } else {
          this.setState({ requiredText: true });
          this.setState({
            errorReport:
              result.data.statelessDbdKpiScorecardInsertKpiOnelineSavedReport
                .results[0].msg
          });
        }
      }
    );
  };
  handleSnackbarClose = () => {
    this.setState({ openSusSnackbar: false });
  };
  CancelAlertDilog = () => {
    this.setState({ openAlert: false });
  };
  handleCopyReport = () => {
    if (this.state.filterText === 'CRANGE') {
      this.setState({ openDilog: true });
    } else if (
      this.state.displayCol.length == 0 ||
      this.state.rowData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({
        openSaveDlg: true
      });
      this.setState({
        copy: true
      });
      this.setState({
        copyFile: true
      });
      if (this.state.reportName != '') {
        this.setState({ reportNameCopy: this.state.reportName });
      }
      this.setState({
        reportName: ''
      });
    }
  };

  render() {
    const { classes } = this.props;
    const { isExportDisabled, parent, reportName } = this.state;
    const reportNam =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.report_name
        ? this.props.history.location.state.report_name
        : '';
    const reportTitle =
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.report_name &&
      this.props.history.location.state.report_name;
    const titleName =
      parent === 'savedReports'
        ? `CP 1-Line-RO Count ${
            this.state.oneLineType === 'Under 60K' ? 'Under 60K' : 'Over 60K'
          } - Saved Reports - ${reportTitle}`
        : `CP 1-Line-RO Count ${
            this.state.oneLineType === 'Under 60K' ? 'Under 60K' : 'Over 60K'
          }`;
    return (
      <div>
        <Page
          title={
            this.state.oneLineType == 'Under 60K'
              ? 'CP 1-Line-RO Count Under 60K'
              : 'CP 1-Line-RO Count Over 60K'
          }
        ></Page>
        {localStorage.getItem('versionFlag') == 'FALSE' ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <React.Fragment>
            <Paper
              square
              style={{
                margin: 8,
                marginLeft: 8,
                height: 50,
                paddingTop: 4,
                paddingLeft: 4
              }}
            >
              <Grid
                container
                className={clsx(this.props.titleContainer, 'reset-dashboard')}
              >
                {' '}
                <Grid
                  item
                  xs={0}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  {(this.state.showBackButton == true &&
                    this.props.history &&
                    this.props.history.location.state &&
                    this.props.history.location.state.parent == 'Home') ||
                  (this.props.history &&
                    this.props.history.location.state &&
                    this.props.history.location.state.parent ==
                      'savedReports') ? (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      onClick={this.renderBackButton}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  ) : (
                    ''
                  )}
                </Grid>
                <Grid
                  item
                  xs={3}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  {this.state.dates && this.state.dates[0] && (
                    <FormControl
                      variant="outlined"
                      margin="dense"
                      className={clsx(classes.formControl, 'input-container')}
                      style={{ margin: 5 }}
                    >
                      <DateRangePicker
                        initialSettings={{
                          locale: {
                            format: 'MM/DD/YY',
                            separator: ' - '
                          },
                          ranges: {
                            ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.yesterDay]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].yesterday
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].yesterday
                              ).toDate()
                            ],
                            ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.dayBfYest]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].dayBeforeYesterday
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].dayBeforeYesterday
                              ).toDate()
                            ],
                            ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.thisWeek]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].thisweekstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].thisweekenddate
                              ).toDate()
                            ],
                            ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastWeek]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastweekstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastweekenddate
                              ).toDate()
                            ],
                            ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastTwoWeek]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lasttwoweekstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lasttwoweekenddate
                              ).toDate()
                            ],
                            ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.mtd]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].mtdstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].mtdenddate
                              ).toDate()
                            ],
                            ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastMonth]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastmonthstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastmonthenddate
                              ).toDate()
                            ],
                            ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastThreeMonths]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastthreemonthstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastthreemonthenddate
                              ).toDate()
                            ],
                            ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastQtr]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastquarterstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastquarterenddate
                              ).toDate()
                            ],
                            ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.ytd]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].ytdstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].ytdenddate
                              ).toDate()
                            ],
                            ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastTwelveMonths]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lasttwelvemonthstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lasttwelvemonthenddate
                              ).toDate()
                            ],
                            ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            this.state.lastYear]: [
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastyearstartdate
                              ).toDate(),
                              moment(
                                this.state.dates[0] &&
                                  this.state.dates[0].lastyearenddate
                              ).toDate()
                            ]
                          },
                          maxDate: moment(
                            this.state.dates[0] && this.state.dates[0].today
                          ).toDate(),
                          alwaysShowCalendars: false,
                          applyClass: clsx(classes.calButton, 'apply-btn'),
                          cancelClass: clsx(classes.calButton, 'apply-btn'),
                          startDate:
                            this.state.filterStart &&
                            this.state.filterStart != ''
                              ? moment(this.state.filterStart).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].mtdstartdate
                                ).toDate()
                              : moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastmonthstartdate
                                ).toDate(),
                          endDate:
                            this.state.filterEnd && this.state.filterEnd != ''
                              ? moment(this.state.filterEnd).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].mtdenddate
                                ).toDate()
                              : moment(
                                  this.state.dates[0] &&
                                    this.state.dates[0].lastmonthenddate
                                ).toDate()
                          //showDropdowns: true
                        }}
                        onApply={this.handleCallback}
                      >
                        <input
                          type="text"
                          className="datepicker"
                          id="picker"
                          name="picker"
                          aria-labelledby="label-picker"
                        />
                      </DateRangePicker>
                      <label class="labelpicker" for="picker" id="label-picker">
                        <div class="textpicker">Select Date</div>
                      </label>
                    </FormControl>
                  )}
                </Grid>
                <Grid
                  item
                  xs={5}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(this.props.mainLabel, 'main-title')}
                    style={{ width: '100%', textAlign: 'center' }}
                  >
                    {titleName}
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={3}
                  style={{ display: 'flex', justifyContent: 'flex-end' }}
                >
                  <div className={classes.allItems}>
                    <div className={clsx(classes.dataAsOf)}>
                      {this.state.closedDate ? (
                        <Typography
                          variant="h6"
                          align="right"
                          style={{
                            fontSize: 12,
                            color: '#7987a1',
                            fontWeight: 'bold'
                          }}
                        >
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-between'
                            }}
                          >
                            <div>Data&nbsp;as&nbsp;of&nbsp;:</div>
                            <div className={classes.dataAsOfValue}>
                              {moment(this.state.closedDate).format('MM/DD/YY')}
                            </div>
                          </div>
                        </Typography>
                      ) : (
                        ''
                      )}
                    </div>
                    <Tooltip title="Save Report">
                      <Link
                        id="saved-report"
                        style={{
                          paddingTop: 11,
                          paddingRight: 5,
                          cursor: 'pointer',
                          float: 'right',
                          pointerEvents: 'auto',
                          cursor: 'pointer'
                        }}
                        onClick={this.handleSaveReport}
                      >
                        <SaveIcon className="saveicon" />
                      </Link>
                    </Tooltip>

                    {this.state.parent == 'savedReports' && (
                      <Tooltip title="Rename and Copy">
                        <Link
                          className={classes.linkItem}
                          id="saved-report-rename-copy"
                          style={{
                            paddingTop: 11,
                            paddingRight: 2,
                            marginLeft: -2,
                            cursor: 'pointer',
                            float: 'right',
                            pointerEvents: 'auto',
                            cursor: 'pointer'
                          }}
                          onClick={this.handleCopyReport}
                        >
                          <FileCopyOutlinedIcon
                            style={{
                              height: '0.73em',
                              weight: '0.8em',
                              marginRight: '-4px',
                              marginTop: '2px'
                            }}
                          />
                        </Link>
                      </Tooltip>
                    )}

                    <Tooltip title="Email Now">
                      <Link
                        className={classes.linkItem}
                        // style={this.linkStyle}
                        onClick={() => this.handleMailClick()}
                        style={{
                          cursor: 'pointer',
                          marginTop: '-2px'
                        }}
                      >
                        <MailIcon
                          style={{
                            height: '1.15em',
                            weight: '1.0em',
                            marginTop: '10px'
                          }}
                        />
                      </Link>
                    </Tooltip>
                    <Tooltip title="Export To PDF">
                      <Link
                        id="export-to-pdf"
                        style={{
                          paddingRight: 5,
                          marginLeft: 5,
                          marginTop: 14,
                          cursor: 'pointer',
                          float: 'right'
                        }}
                        onClick={this.exportToPDF}
                      >
                        <i
                          className="fas fa-file-pdf"
                          style={{
                            color: 'red'
                          }}
                        ></i>
                      </Link>
                    </Tooltip>
                    <Tooltip title="Export To Excel">
                      <Link
                        id="export-to-excel"
                        style={{
                          paddingTop: 14,
                          paddingRight: 10,
                          cursor: 'pointer',
                          float: 'right'
                        }}
                        onClick={this.onBtExport}
                      >
                        <i
                          className="fas fa-file-excel"
                          style={{ color: 'green' }}
                        ></i>
                      </Link>
                    </Tooltip>
                    <Button
                      variant="contained"
                      id="reset-layout"
                      className={clsx(classes.resetBtn, 'reset-btn')}
                      onClick={this.resetRawData}
                    >
                      <RestoreIcon />
                      <Typography variant="body1" align="left">
                        Reset Layout
                      </Typography>
                    </Button>
                  </div>{' '}
                </Grid>
              </Grid>
            </Paper>
          </React.Fragment>
        )}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab-labor-misses"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            height: window.innerHeight - 230 + 'px',
            // height:(window.innerHeight-215)+'px',
            alignContent: 'center',
            marginLeft: '8px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            excelStyles={this.state.excelStyles}
            defaultColDef={this.state.defaultColDef}
            popupParent={this.state.popupParent}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            floatingFilter={true}
            suppressRowClickSelection={true}
            headerHeight={this.state.headerHeight}
            onFilterChanged={this.onFilterChanged}
            suppressDragLeaveHidesColumns={true}
            sideBar={this.state.sideBar}
            suppressContextMenu={true}
            onColumnVisible={this.onColumnVisible}
            onFirstDataRendered={this.onFirstDataRendered}
            onModelUpdated={this.onModelUpdated}
            onColumnMoved={this.onColumnMoved}
          />
        </div>

        <SaveReportDialog
          openSaveDlg={this.state.openSaveDlg}
          parent={this.state.parent}
          reportName={this.state.reportName}
          errorReport={this.state.errorReport}
          requiredText={this.state.requiredText}
          selectedType={this.state.selectedType}
          filterText={this.state.filterText}
          onChangeReportName={this.onChangeReportName}
          handleCancelSaveReport={this.handleCancelSaveReport}
          handleOkSaveReport={this.handleOkSaveReport}
          handleCheckboxChange={this.handleCheckboxChange}
          handleSaveAsReport={this.handleSaveAsReport}
          copyFile={this.state.copyFile}
          copy={this.state.copy}
          reportNameCopy={this.state.reportNameCopy}
        />
        <SuccessSnackbar
          onClose={this.handleSnackbarClose}
          open={this.state.openSusSnackbar}
          msg={'Report saved successfully!'}
          //goalFail={this.state.goalFail}
        />
        <AlertSnackbar
          onClose={this.CancelAlertDilog}
          open={this.state.openAlert}
          msg={'No data available'}
        />
        {this.state.isLoading == false &&
        this.state.showSummaryValues == true ? (
          <Paper square style={{ marginBottom: 12, marginLeft: 5 }}>
            <div
              className={clsx(classes.root, {
                [classes.hidesummary]: this.state.isColumnFiltered
              })}
            >
              <Grid container spacing={3} style={{ margin: 0 }}>
                {this.state.oneLineType == 'Under 60K' ? (
                  <SummaryTitle
                    title={'Mileage Under 60K'}
                    value={this.state.milageBelow60K
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  />
                ) : (
                  <SummaryTitle
                    title={'Mileage Over 60K'}
                    value={this.state.milageAbove60K
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  />
                )}
                <SummaryTitle
                  title={'Labor Sale'}
                  value={'$' + this.state.lbrSale}
                />
                <SummaryTitle
                  title={'Labor Sold Hours'}
                  //value={this.state.lbrSoldHrs}
                  value={this.state.lbrSoldHrs}
                />
              </Grid>
            </div>
          </Paper>
        ) : null}

        <EmailDialogKpi
          open={this.state.openDialogue}
          handlePopupClose={this.handleCloseEmail}
          mailUsers={this.state.mailUsers}
          // clientReportCardDetails={clientReportCardDetails}
          // image={image}
          // selectedWorkmixOptions={selectedWorkmixOptions}
          iKpiReportType={this.state.iKpiReportType}
          // measuredMTH={measuredMTH}
          // priorMTH={priorMTH}
          // selectedOptions={selectedOptions}
          selectedStoreIds={this.state.selectedStoreIds}
          reportName={reportNam}
          // reportName={this.state.reportName}
          reportNameCopy={this.state.reportNameCopy}
          // selectStoreDetails={selectStoreDetails}
          selectedStoreName={localStorage.getItem('storeSelected')}
          headerData={this.state.headerData}
          rowDataPdf={this.state.rowDataPdf}
          filterStart={this.state.filterStart}
          filterEnd={this.state.filterEnd}
          rowDataTotal={this.state.rowDataTotal}
          oneLineType={this.state.oneLineType}
          // Technicians={this.state.Technicians}
          // serviceAdvisors={this.state.serviceAdvisors}
          selectedServiceAdvisors={ReactSession.get('serviceAdvisors')}
          selectedTechnicians={ReactSession.get('technicians')}
        ></EmailDialogKpi>

        {this.state.openDilog ? (
          <Dialog
            open={this.state.openDilog}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available for custom date range.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.CancelDilog} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
        {this.state.openDilogAll ? (
          <Dialog
            open={this.state.openDilogAll}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available at all stores.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.CancelDilogAll} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
      </div>
    );
  }
}

const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '30px' }}
        color="primary"
      >
        {title}
      </Typography>
      <Typography variant="subtitle1" style={{ fontSize: '14px' }}>
        {' '}
        {value}
      </Typography>
    </Grid>
  );
};
const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    marginTop: 7
  },
  dataAsOf: {
    marginRight: 7,
    float: 'right',
    marginTop: 14
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '5px !important'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  allJobs: {
    marginTop: 12,
    marginRight: 10
  },
  ButtonSelect: {
    lineHeight: 1.5,
    position: 'absolute',
    '@media (min-width: 2560px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 1920px)': {
      width: 183,
      marginTop: '-5px !important',
      marginBottom: '3px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '-5px !important',
      width: 154,
      marginBottom: '2x !important'
    }
  },
  reportButton: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    }
  },
  reportButtonSelect: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  resetBtn: {
    marginRight: 10,
    float: 'right',
    marginTop: 10
  },
  allItems: {
    display: 'flex',
    justifyContent: 'end'
  }
});

export default withKeycloak(withStyles(styles)(DrilldownOneLineRo));
