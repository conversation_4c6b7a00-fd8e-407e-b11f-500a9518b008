import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import {
  Typography,
  FormControl,
  InputLabel,
  LinearProgress,
  Box,
  Paper,
  MenuItem,
  Select,
  Tooltip,
  FormControlLabel,
  RadioGroup,
  Radio,
  Link
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import RestoreIcon from '@material-ui/icons/Restore';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import SaveReportDialog from 'src/components/SaveReportDialog';
import {
  getKpiToggleOptionsWithTimeZone,
  getDataForLaborMisses,
  getTargetGridRate,
  getGridorMatrixMissesDetails,
  getDataForAdviorAndTechLaborMisses,
  getEmail,
  insertLaborMissesSavededReport
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import css from 'src/assets/scss/main.scss';
import { getLast13Months, getTimeZone } from 'src/utils/Utils';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import { ReactSession } from 'react-client-session';
import TooltipRenderer from './Component/TooltipRenderer';
import { getLatestClosedDate } from 'src/utils/hasuraServices';
import { getNextMonth, getYearValue } from 'src/utils/Utils';
import ExportIcon from '@material-ui/icons/GetApp';
import PictureAsPdfIcon from '@material-ui/icons/PictureAsPdf';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import $ from 'jquery';
import { withKeycloak } from '@react-keycloak/web';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import { Redirect } from 'react-router-dom';
import SaveIcon from '@material-ui/icons/Save';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import { i } from 'react-dom-factories';
import { PanoramaSharp } from '@material-ui/icons';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import { isValidDate } from 'src/components/ViewGraphDetailsAction';
import EmailDialogKpi from 'src/components/EmailDialogKpi';
import MailIcon from '@material-ui/icons/Mail';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import AlertSnackbar from 'src/components/AlertSnackbar';

import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { resultKeyNameFromField } from '@apollo/client/utilities';

var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

class LaborMisses extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ Technicians: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });

    this.getKpiToggleOptions();
  }
  componentDidUpdate() {
    const newAdvisors = ReactSession.get('serviceAdvisors');
    const newTech = ReactSession.get('technicians');
    if (
      ReactSession.get('serviceAdvisors') != undefined ||
      ReactSession.get('technicians') != undefined ||
      this.state.filterChanged
    ) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisors,
        ReactSession.get('serviceAdvisors')
      );
      var checkStatusTech = lodash.isEqual(
        this.state.Technicians,
        ReactSession.get('technicians')
      );
      console.log(
        'selectedStoreId=qq=1',
        ReactSession.get('selectedStoreId'),
        ReactSession.get('serviceAdvisors'),
        checkStatus,
        JSON.parse(localStorage.getItem('selectedStoreId')).length
      );
      if (
        (checkStatus == false && $('#advisorSelection').hasClass('selected')) ||
        (checkStatusTech == false &&
          $('#techSelection').hasClass('selected')) ||
        this.state.filterChanged == true
      ) {
        /* this.setState({ gridType: this.state.gridType });
        this.setState({ selectedFilter: 'duration' });
        this.setState({ selectedToggle: this.state.selectedToggle });
        this.setState({ selectedMonthYear: '' });
        this.setState({ filterChanged: false });
        this.setState({ advisors: ReactSession.get('serviceAdvisors') });
        this.setState({ tech: ReactSession.get('technicians') });
        //this.setState({ checked: false });
        this.getAgGridData(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          '',
          this.state.filterStart,
          this.state.filterEnd
        );*/

        this.setState(
          {
            gridType: this.state.gridType,
            selectedFilter: 'duration',
            selectedToggle: this.state.selectedToggle,
            selectedMonthYear: '',
            filterChanged: false,
            advisors: newAdvisors,
            tech: newTech
          },
          () => {
            this.getAgGridData(
              this.state.selectedToggle,
              newAdvisors,
              newTech,
              '',
              this.state.filterStart,
              this.state.filterEnd
            );
          }
        );
      }
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      //this.resetRawData();

      // this.state.gridColumnApi && this.state.gridColumnApi.resetColumnState();
      // this.gridApi && this.gridApi.setSortModel(null);
      // this.gridApi && this.gridApi.setFilterModel(null);

      // console.log(
      //   'selected=',
      //   ReactSession.get('selectedStoreId'),
      //   checkSt,
      //   localStorage.getItem('selectedStoreId'),
      //   this.state.store,
      //   '======',
      //   this.state.store == undefined
      // );
      if (checkSt == false) {
        if (this.state.store != undefined) {
          window.sortStateLbrMiss = {};
          window.filterStateLbrMiss = {};
          this.state.rawGridApi.setColumnDefs([]);
          if (this.state.gridColumnApi) {
            const groupColumns = this.state.groupColumn;
            this.state.rawGridApi.setColumnDefs(groupColumns);
            setTimeout(() => {
              this.state.gridColumnApi.resetColumnState();
            }, 1000);
          }
        }
        this.setState({ storeSwitch: true, parent: '' });
        console.log('enter=====');
        // this.setState({ gridType: this.state.gridType });
        this.setState({ selectedFilter: 'duration' });
        this.setState({ selectedToggle: this.state.selectedToggle });
        this.setState({ selectedMonthYear: '' });
        if (this.state.store != undefined) {
          this.setState({ checked: false });
        }
        this.setState({ payTypeList: [], gridType: 'All' });

        //    window.filterState = {};
        getLatestClosedDate(result => {
          if (result) {
            var openDate = '';
            var Date1 = result[0].value;
            localStorage.setItem('closedDate', Date1);
            this.setState({ closedDate: Date1 });
          }
        });
        this.getGridDetails(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          this.state.filterStart,
          this.state.filterEnd
        );
        const groupColumn = this.state.groupColumn;
        if (
          JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
          this.state.rawGridApi
        ) {
          groupColumn[2]['hide'] = false;
          groupColumn[2]['suppressToolPanel'] = false;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          if (window.sortStateLbrMiss && this.gridApi) {
            this.gridApi.setSortModel(window.sortStateLbrMiss);
          }
          if (window.colStateLbrMisses && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStateLbrMisses);
          }
        } else if (this.state.rawGridApi) {
          groupColumn[2]['hide'] = true;
          groupColumn[2]['suppressToolPanel'] = true;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          if (window.sortStateLbrMiss && this.gridApi) {
            this.gridApi.setSortModel(window.sortStateLbrMiss);
          }
          if (window.colStateLbrMisses && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStateLbrMisses);
          }
        }

        this.resetRawData();
        this.setState({
          store: localStorage.getItem('selectedStoreId')
        });
      }
    }
  }
  constructor(props) {
    super(props);
    let selFilter = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedFilter
        : this.props.selectedFilter
      : this.props.selectedFilter;
    let selToggle = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedToggle
        : this.props.selectedToggle
      : this.props.selectedToggle;
    let selMonthyr = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedMonthYear
        : this.props.selectedMonthYear
      : this.props.selectedMonthYear;
    let closedDate = localStorage.getItem('closedDate');
    let parent =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.parent
        ? this.props.history.location.state.parent
        : '';
    let PayType =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.payType
        ? this.props.history.location.state.payType
        : 'C';
    let previousPayType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.previousPayType
        : 'C';
    let gridType =
      this.props.location &&
      this.props.location.state &&
      // this.props.location.state.parent &&
      // this.props.location.state.parent == 'Home' &&
      this.props.location.state.selectedGridType
        ? this.props.location.state.selectedGridType
        : this.props.location &&
          this.props.location.state &&
          this.props.location.state.parent &&
          this.props.location.state.parent == 'Home'
        ? 'All'
        : this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : 'All';
    let previousGridType =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousGridType
        ? this.props.history.location.state.previousGridType
        : '';
    let showAllJobs =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.jobType &&
          this.props.history.location.state.jobType == 'All'
        ? true
        : false;
    var timeZone = getTimeZone();
    let kpiDataToggle =
      localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';

    let startDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterStart
        ? this.props.history.location.state.filterStart
        : ReactSession.get('kpiToggleStartDate') != ''
        ? ReactSession.get('kpiToggleStartDate')
        : '';

    let endDate =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterEnd
        ? this.props.history.location.state.filterEnd
        : ReactSession.get('kpiToggleEndDate') != ''
        ? ReactSession.get('kpiToggleEndDate')
        : '';
    // let filterText = ReactSession.get('kpiHomeToggle');
    let created_by =
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.created_by
        ? props.history.location.state.created_by
        : '';
    let filterText =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterText
        ? this.props.history.location.state.filterText
        : ReactSession.get('kpiHomeToggle')
        ? ReactSession.get('kpiHomeToggle')
        : localStorage.getItem('kpiHomeToggle');
    let filterColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.filterColumns
        ? this.props.history.location.state.filterColumns
        : {};
    let sortColumn =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.sortColumns
        ? this.props.history.location.state.sortColumns
        : {};
    let advisor =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedAdvisor
        ? this.props.history.location.state.selectedAdvisor
        : ReactSession.get('serviceAdvisors')
        ? ReactSession.get('serviceAdvisors')
        : null;
    let technicians =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedTech
        ? this.props.history.location.state.selectedTech
        : ReactSession.get('technicians')
        ? ReactSession.get('technicians')
        : null;
    let checkedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.checkedColumns
        ? this.props.history.location.state.checkedColumns
        : 'All';
    let draggedColumns =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.draggedColumn
        ? this.props.history.location.state.draggedColumn
        : [];

    let visibility =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.visibility
        ? this.props.history.location.state.visibility
        : 'private';
    let reportName =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.report_name
        ? this.props.history.location.state.report_name
        : '';
    console.log('TopbarSelection>>>>>>>', advisor, technicians, this.props);
    this.state = {
      selectedStoreIds: JSON.parse(localStorage.getItem('selectedStoreId')),
      headerData: [],
      rowDataPdf: [],
      iKpiReportType: 'Labor_Target_Misses',
      mailUsers: [],
      openDialogue: false,
      createdBy: created_by,
      storeSwitch: false,
      openStoreDlg: false,
      selectedFilter: selFilter ? selFilter : 'duration',
      selectedToggle: selToggle ? selToggle : kpiDataToggle,
      selectedMonthYear: selMonthyr ? selMonthyr : '',
      monthYear: {},
      toggleOptions: [],
      kpiDataToggle: kpiDataToggle,

      dayBeforeYesterday: '',
      closedDate: closedDate,
      parent: parent,
      timeZone: timeZone,
      soldHours: 0,
      targetPrice: 0,
      actualSale: 0,
      overSell: 0,
      underSell: 0,
      diffSale: 0,
      jobCount: 0,
      roCount: 0,
      payType: PayType,
      PrevPayType: previousPayType,
      lastThreeMonths: '',
      //   lastTwelveMonths: '',
      gridType: gridType,
      PrevGridType: previousGridType,
      isLoading: true,
      payTypeList: [],
      checked: showAllJobs,

      dates: [],
      thisWeek: '',
      lastTwoWeek: '',
      lastWeek: '',
      yesterDay: '',
      today: '',
      dayBfYest: '',
      mtd: '',
      setMtd: '',
      lastMonth: '',
      lastThreeMonths: '',
      lastQtr: '',
      ytd: '',
      lastTwelveMonths: '',
      lastYear: '',
      filterStart: startDate,
      filterEnd: endDate,
      filterChanged: false,
      filterText: filterText,
      openSaveDlg: false,
      reportName: reportName,
      errorReport: '',
      selectedType: visibility,
      openSusSnackbar: false,
      requiredText: false,
      displayCol: checkedColumns,
      draggedColumn: draggedColumns,
      advisors: advisor,
      tech: technicians,
      parent: parent,
      filterCol: filterColumns,
      sortCol: sortColumn,
      // filterTxt: filterTxt,
      copy: false,
      copyFile: false,
      reportNameCopy: '',
      openDilog: false,
      openDilogAll: false,
      openAlert: false,
      zeroFilter: false,
      columnDefs: [
        {
          headerName: 'RO Open Date',
          field: 'opendate',
          width: 90,
          valueFormatter: this.formatCellValueDate,
          cellClass: 'dateFormatter',
          tooltipValueGetter: function(params) {
            return params.valueFormatted ? params.valueFormatted : params.value;
          },
          suppressMenu: true,
          unSortIcon: true,
          filterParams: {
            valueFormatter: this.formatCellValueDate,
            applyMiniFilterWhileTyping: true
          },
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          headerClass: 'ag-header-cell-text'
        },
        {
          headerName: 'RO Cls. Date',
          field: 'closeddate',
          width: 90,
          tooltipValueGetter: function(params) {
            return params.valueFormatted ? params.valueFormatted : params.value;
          },
          valueFormatter: this.formatCellValueDate,
          cellClass: 'dateFormatter',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: this.formatCellValueDate,
            applyMiniFilterWhileTyping: true
          },
          headerClass: 'ag-header-cell-text'
        },
        {
          headerName: 'Store',
          field: 'storeName',
          width: 140,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'storeName',
          hide: true,
          pivot: false,
          suppressToolPanel: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        // {
        //   headerName: 'Grid For',
        //   field: 'gridFor',
        //   width: 100,
        //   dataType: 'string',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'left', border: ' 0px white' };
        //   }
        // },
        // {
        //   headerName: 'Type',
        //   field: 'fleetOrPaytypeOrOpcodeFixedRate',
        //   width: 100,
        //   dataType: 'string',
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        {
          headerName: 'RO#',
          field: 'ronumber',
          width: 85,
          suppressMenu: true,
          unSortIcon: true,
          onCellClicked: this.handleSearchByRo,
          tooltip: params => 'View RO',
          //cellRendererFramework: TooltipRenderer,
          cellClass: 'textAlignCenter',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 120,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'advisorName',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
            // Handle null values
            if (valueA === null && valueB === null) {
              return 0;
            }
            if (valueA === null) {
              return isInverted ? 1 : -1; // Place null values at the end if sorting in ascending order
            }
            if (valueB === null) {
              return isInverted ? -1 : 1; // Place null values at the beginning if sorting in ascending order
            }

            // Extract names and perform comparison
            let nameA = valueA.split('[')[0].trim();
            let nameB = valueB.split('[')[0].trim();
            return nameA.localeCompare(nameB); // Alphabetic sort based on the name only
          }
        },
        {
          headerName: 'Technician',
          field: 'techName',
          width: 120,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'techName',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
            // Handle null values
            if (valueA === null && valueB === null) {
              return 0;
            }
            if (valueA === null) {
              return isInverted ? 1 : -1; // Place null values at the end if sorting in ascending order
            }
            if (valueB === null) {
              return isInverted ? -1 : 1; // Place null values at the beginning if sorting in ascending order
            }

            // Extract names and perform comparison
            let nameA = valueA.split('[')[0].trim();
            let nameB = valueB.split('[')[0].trim();
            return nameA.localeCompare(nameB); // Alphabetic sort based on the name only
          }
        },
        // {
        //   headerName: 'Tech',
        //   field: 'techName',
        //   width: 120,
        //   dataType: 'string',
        //   suppressMenu: true,
        //   hide: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'left', border: ' 0px white' };
        //   }
        // },
        {
          headerName: 'Customer',
          field: 'customerName',
          dataType: 'string',
          tooltipField: 'customerName',
          width: 165,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Model',
          field: 'model',
          width: 100,
          dataType: 'string',
          tooltipField: 'model',
          cellClass: 'textAlignUiLeft',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'opcode',
          tooltipField: 'opcodeDesc',
          width: 90,
          chartDataType: 'category',
          cellClass: 'textAlignUiLeft',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: (valueA, valueB) => {
            // Helper function to extract numeric part from string
            const extractNumericPart = str => {
              const matches = str.match(/(\d+)/);
              return matches ? parseInt(matches[0], 10) : NaN;
            };

            // Helper function to extract non-numeric part from string
            const extractNonNumericPart = str => {
              return str.replace(/(\d+)/, '').toLowerCase();
            };

            // Extract numeric and non-numeric parts from values
            const numericPartA = extractNumericPart(valueA);
            const numericPartB = extractNumericPart(valueB);
            const nonNumericPartA = extractNonNumericPart(valueA);
            const nonNumericPartB = extractNonNumericPart(valueB);

            // Compare non-numeric parts
            if (nonNumericPartA !== nonNumericPartB) {
              return nonNumericPartA.localeCompare(nonNumericPartB);
            }

            // Compare numeric parts if both are numeric
            if (!isNaN(numericPartA) && !isNaN(numericPartB)) {
              return numericPartA - numericPartB;
            }

            // If one is numeric and the other is not, numeric comes first
            if (!isNaN(numericPartA)) {
              return -1;
            } else if (!isNaN(numericPartB)) {
              return 1;
            }

            // Fallback to comparing original values if both parts are non-numeric
            return valueA.localeCompare(valueB);
          }
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: 60,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'paytype'
        },
        // {
        //   headerName: 'Pay Type Group',
        //   field: 'paytypegroup',
        //   width: 68,
        //   dataType: 'string',
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        {
          headerName: 'Hours Sold',
          field: 'lbrsoldhours',
          width: 65,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Target Price',
          field: 'gridPricing',
          width: 78,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          tooltipField: 'gridPricing'
        },
        {
          headerName: 'Actual Sale',
          field: 'lbrsale',
          width: 78,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'lbrsale',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '$ Variance',
          field: 'variance',
          width: 95,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueVariance,
          filterParams: {
            valueFormatter: this.formatCellValueVariance,
            applyMiniFilterWhileTyping: true
          },
          cellClass: 'twoDecimalPlacesWith$',
          cellClassRules: {
            greenBackground: function(params) {
              return params.rowIndex % 2 == 0;
            },
            greenFont: function(params) {
              return (
                params.data.compliance == 'TRUE' &&
                params.value != 0 &&
                params.value != null &&
                parseFloat(params.value).toFixed(2) != 0
              );
            },
            blueFont: function(params) {
              return params.value >= 0 && params.data.compliance == 'FALSE';
            },
            redFont: function(params) {
              return params.value < 0 && params.data.compliance == 'FALSE';
            }
          },
          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              fontWeight: 'bold',
              color:
                params.value >= 0 && params.data.compliance == 'FALSE'
                  ? '#0000FF'
                  : params.value < 0 && params.data.compliance == 'FALSE'
                  ? 'red'
                  : params.data.compliance == 'TRUE' &&
                    params.value != 0 &&
                    params.value != null &&
                    parseFloat(params.value).toFixed(2) != 0
                  ? 'green'
                  : ''
            };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '% Variance',
          field: 'variancePerc',
          width: 99,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValuePercent,
          cellClass: 'twoDecimalPlacesWithOut$',
          cellClassRules: {
            greenBackground: function(params) {
              return params.rowIndex % 2 == 0;
            },
            greenFont: function(params) {
              return (
                params.data.compliance == 'TRUE' &&
                params.data.variancePerc != 0 &&
                params.data.variancePerc != null
              );
            },
            blueFont: function(params) {
              return (
                params.data.variancePerc >= 0 &&
                params.data.compliance == 'FALSE'
              );
            },
            redFont: function(params) {
              return (
                params.data.variancePerc < 0 &&
                params.data.compliance == 'FALSE'
              );
            }
          },
          //cellClass: 'variance%',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            const rawValue = params.data.variancePerc;
            return {
              textAlign: 'right',
              border: '0px white',
              fontWeight: 'bold',
              color:
                rawValue >= 0 && params.data.compliance == 'FALSE'
                  ? '#0000FF'
                  : rawValue < 0 && params.data.compliance == 'FALSE'
                  ? 'red'
                  : params.data.compliance == 'TRUE' &&
                    rawValue != 0 &&
                    rawValue != null
                  ? 'green'
                  : ''
            };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Target ELR',
          field: 'targetElr',
          width: 75,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Actual ELR',
          field: 'actualElr',
          width: 75,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '1 Hour Door Rate',
          field: 'doorRate',
          width: 83,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$Center',
          suppressMenu: true,
          unSortIcon: true,
          onCellClicked: this.handleDoorRate,
          // tooltip: params => 'View Grid',
          tooltip: params => {
            const gridType = params.data.gridType;

            if (gridType.includes('Fixed Rate')) {
              return 'View Fixed Rate';
            } else {
              return 'View Grid';
            }
          },

          cellStyle: function(params) {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Grid / Door Install Date',
          field: 'gridDate',
          width: 105,
          valueFormatter: this.formatCellValueDate,
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'textAlignCenter',
          filterParams: {
            valueFormatter: this.formatCellValueDate,
            applyMiniFilterWhileTyping: true
          },
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Grid Name',
          field: 'gridType',
          width: 100,
          dataType: 'string',
          tooltipField: 'gridType',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Compliance',
          field: 'compliance',
          width: 103,
          cellClass: 'textAlignCenter',
          cellStyle: function(params) {
            return {
              textAlign: 'center'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Grid As Of',
          field: 'currentgridasof',
          hide: true,
          width: 95,
          cellClass: 'dateFormatter',
          tooltipValueGetter: function(params) {
            return params.valueFormatted ? params.valueFormatted : params.value;
          },
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate,
            applyMiniFilterWhileTyping: true
          },
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          hide: true,
          width: 80,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'left'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        // {
        //   headerName: 'Type',
        //   field: 'fleetOrPaytypeOrOpcodeFixedRate',
        //   hide: true,
        //   width: 103,
        //   cellClass: 'textAlign',
        //   cellStyle: function(params) {
        //     return {
        //       textAlign: 'center'
        //     };
        //   },
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'StoreId',
        //   field: 'storeId',
        //   hide: true,
        //   width: 103,
        //   cellClass: 'textAlign',
        //   cellStyle: function(params) {
        //     return {
        //       textAlign: 'center'
        //     };
        //   },
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        {
          headerName: 'Grid For',
          field: 'gridFor',
          hide: true,
          width: 85,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'left'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Vin',
          field: 'vin',
          hide: true,
          width: 110,
          tooltipField: 'vin',
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcategory',
          field: 'opcategory',
          hide: true,
          width: 95,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'left'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Actual Hrs',
          field: 'lbractualhours',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Part Extended Sale',
          field: 'prtextendedsale',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Part Extended Cost',
          field: 'prtextendedcost',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP',
          field: 'lbrgrossprofit',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP%',
          field: 'lbrGrossprofitpercentage',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP',
          field: 'prtsgrossprofit',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP%',
          field: 'prtsGrossprofitpercentage',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR',
          field: 'elr',
          hide: true,
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup',
          field: 'markup',
          hide: true,
          width: 80,
          cellClass: 'fourDecimalPlaces',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Make',
          field: 'make',
          hide: true,
          width: 80,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },

          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Year',
          field: 'year',
          hide: true,
          width: 80,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'right'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Mileage',
          field: 'mileage',
          hide: true,
          width: 80,
          // cellClass: 'textAlignUiRight',
          cellClass: 'commaSeparatedNumber',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMilage,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMilage,
            comparator: function(valueA, valueB) {
              return valueA - valueB;
            }
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Seq No.',
          field: 'lbrsequenceno',
          hide: true,
          width: 85,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'right'
            };
          },
          suppressMenu: true,
          unSortIcon: true
        }
      ],

      rowData: [],
      overlayNoRowsTemplate:
        '<span style="padding: 10px; margin-top:50px;">No Rows To Show</span>',
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      popupParent: document.body,
      headerHeight: 48,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true,
              // suppressColumnFilter: true,
              //suppressColumnSelectAll: true,
              suppressColumnExpandAll: false
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        suppressMovable: false,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false
      },
      excelStyles: [
        {
          id: 'summaryValue',
          font: {
            size: 11,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'bigHeader',
          font: {
            size: 16,
            color: 'primary',
            bold: true
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          }
        },
        {
          id: 'currency',
          numberFormat: {
            format: '$#,##0.00'
          },
          font: {
            color: '#000000'
          },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: {
            format: '#,##0.0'
          },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'redFont',
          font: {
            color: '#FF0000',
            bold: true
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '#008000',
            bold: true
          }
        },
        {
          id: 'blueFont',
          font: {
            color: '#0000FF',
            bold: true
          }
        },
        {
          id: 'centerAlign',
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'header',
          interior: {
            pattern: 'Solid',
            //color: '#F4B084',
            color: '#c65911'
          },
          font: {
            bold: true,
            color: '#FFFFFF',
            size: 12
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center',
            wrapText: true
          }
        },
        {
          id: 'subHeader',
          font: {
            size: 12,
            color: 'primary',
            bold: true
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          }
        },
        {
          id: 'summaryRed',
          font: {
            size: 11,
            color: '#ff0000'
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'summaryBlue',
          font: {
            size: 11,
            color: '0000FF'
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'summaryLabel',
          font: {
            size: 11,
            color: 'primary',
            bold: true
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          },
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#f8dbc9',
            pattern: 'Solid'
          }
        },
        {
          id: 'fourDecimalPlaces',
          numberFormat: { format: '###0.0000' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'twoDecimalPlacesWith$Center',
          numberFormat: { format: '$#,##0.00' },
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          alignment: {
            horizontal: 'Left',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlignCenter',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'textAlignUiLeft',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'textAlignUiRight',
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'commaSeparatedNumber',
          numberFormat: {
            format: '#,##0' // Comma formatting without decimal places
          },
          alignment: {
            horizontal: 'Right'
          }
        }
      ]
    };
  }
  cancelDilog = () => {
    this.setState({ openDilog: false });
  };
  CancelDilogAll = () => {
    this.setState({ openDilogAll: false });
  };
  handleSaveReport = () => {
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.parent != 'savedReports'
    ) {
      this.setState({ openDilogAll: true });
    } else if (this.state.filterText == 'CRANGE') {
      this.setState({
        openDilog: true
      });
    } else if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({
        openSaveDlg: true
      });
    }
  };
  handleCancelSaveReport = () => {
    this.setState({
      openSaveDlg: false
    });
    this.setState({
      copyFile: false
    });
    this.setState({
      copy: false
    });
    if (this.state.parent != 'savedReports') {
      this.setState({
        reportName: ''
      });
      this.setState({
        selectedType: 'private'
      });
    } else {
      this.setState({
        selectedType:
          this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.visibility
      });
    }
  };
  handleCopyReport = () => {
    if (this.state.filterText == 'CRANGE') {
      this.setState({
        openDilog: true
      });
    } else if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({
        openSaveDlg: true
      });
    }

    this.setState({
      copy: true
    });
    this.setState({
      copyFile: true
    });
    if (this.state.reportName != '') {
      this.setState({ reportNameCopy: this.state.reportName });
    }
    this.setState({
      reportName: ''
    });
  };
  onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;
    if (!nameRegex.test(e.target.value) && e.target.value) {
      this.setState({ requiredText: '' });
    } else {
      this.setState({
        errorReport: ''
      });
      this.setState({
        reportName: e.target.value
      });

      this.setState({ requiredText: false });
    }
  };
  handleCheckboxChange = e => {
    this.setState({
      selectedType: e.target.value
    });
  };
  handleSnackbarClose = () => {
    this.setState({ openSusSnackbar: false });
  };
  CancelAlertDilog = () => {
    this.setState({ openAlert: false });
  };
  formatCellValueMarkup = params => {
    if (params && params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };
  transformData = data => {
    return Object.fromEntries(
      Object.entries(data).map(([key, value]) => [key, value.values])
    );
  };
  transformSortData = data => {
    return data.flatMap(({ colId, sort }) => [colId, sort]);
  };
  handleOkSaveReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    window.sortStateLbrMiss = this.gridApi.getSortModel();
    window.filterStateLbrMiss = this.gridApi.getFilterModel();

    window.colStateLbrMisses = this.gridColumnApi.getColumnState();

    if (
      window.filterStateLbrMiss != undefined ||
      window.sortStateLbrMissn != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateLbrMiss)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStateLbrMiss)
      );
    } else {
      filterColumn = null;
      sortColumns = null;
    }

    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    if (this.state.parent == 'savedReports') {
      insertLaborMissesSavededReport(
        'update',
        iStoreId,
        this.state.reportName,
        this.state.filterText,
        userId,
        'Labor_Target_Misses',
        this.state.selectedType,
        userRole,
        sortColumns,
        filterColumn,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        this.state.displayCol,
        this.state.gridType,
        this.state.checked ? 'All' : 'Non_Compliance',
        this.state.PrevPayType,
        this.state.draggedColumn,
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,
        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertLabormissesSavedReport
              .results[0].status == 1
          ) {
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data.statelessDbdKpiScorecardInsertLabormissesSavedReport
                  .results[0].msg
            });
          }
        }
      );
    } else {
      insertLaborMissesSavededReport(
        'insert',
        iStoreId,
        this.state.reportName,
        this.state.filterText,
        userId,
        'Labor_Target_Misses',
        this.state.selectedType,
        userRole,
        sortColumns,
        filterColumn,
        this.state.tech == undefined || this.state.tech.includes('All')
          ? null
          : this.state.tech,
        this.state.displayCol,
        this.state.gridType,
        this.state.checked ? 'All' : 'Non_Compliance',
        this.state.PrevPayType,
        this.state.draggedColumn,
        this.state.advisors == undefined || this.state.advisors.includes('All')
          ? null
          : this.state.advisors,
        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertLabormissesSavedReport
              .results[0].status == 1
          ) {
            this.setState({
              openSaveDlg: false
            });
            this.setState({
              errorReport: ''
            });
            this.setState({ reportName: '' });
            this.setState({ openSusSnackbar: true });
            this.setState({ requiredText: false });
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data.statelessDbdKpiScorecardInsertLabormissesSavedReport
                  .results[0].msg
            });
          }
        }
      );
    }
  };
  handleSaveAsReport = () => {
    let filterColumn = {};
    let sortColumns = {};

    window.sortStateLbrMiss = this.gridApi.getSortModel();
    window.filterStateLbrMiss = this.gridApi.getFilterModel();

    window.colStateLbrMisses = this.gridColumnApi.getColumnState();

    if (
      window.filterStateLbrMiss != undefined ||
      window.sortStateLbrMissn != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateLbrMiss)
      );
      sortColumns = JSON.stringify(
        this.transformSortData(window.sortStateLbrMiss)
      );
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    var iStoreId =
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.store_id
        ? this.props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');

    insertLaborMissesSavededReport(
      'insert',
      iStoreId,
      this.state.reportName,
      this.state.filterText,
      userId,
      'Labor_Target_Misses',
      this.state.selectedType,
      userRole,
      sortColumns,
      filterColumn,
      this.state.tech == undefined || this.state.tech.includes('All')
        ? null
        : this.state.tech,
      this.state.displayCol,
      this.state.gridType,
      this.state.checked ? 'All' : 'Non_Compliance',
      this.state.PrevPayType,
      this.state.draggedColumn,
      this.state.advisors == undefined || this.state.advisors.includes('All')
        ? null
        : this.state.advisors,
      result => {
        if (
          result.data.statelessDbdKpiScorecardInsertLabormissesSavedReport
            .results[0].status == 1
        ) {
          this.setState({
            openSaveDlg: false
          });
          this.setState({
            errorReport: ''
          });
          this.setState({ openSusSnackbar: true });
          this.setState({ requiredText: false });
          this.setState({ reportName: '' });
        } else {
          this.setState({ requiredText: true });
          this.setState({
            errorReport:
              result.data.statelessDbdKpiScorecardInsertLabormissesSavedReport
                .results[0].msg
          });
        }
      }
    );
  };
  formatCellValueDate = params => {
    if (params && params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValueMilage = params => {
    if (params && params.value != null && params.value != 0) {
      return params.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0';
    }
  };
  formatCellValuePercent = params => {
    if (params && params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.00%';
    }
  };
  formatCellValueGP = params => {
    if (params && params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0' + '%';
    }
  };
  formatCellValueRate = params => {
    if (params && params.value != null && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };

  formatSheetName = sheetName => {
    // Maximum length for Excel sheet names is 31 characters
    const maxLength = 31;

    // Check if the sheet name exceeds the maximum length
    if (sheetName.length > maxLength) {
      // Truncate to 28 characters and add "..."
      return sheetName.substring(0, 28) + '...';
    }

    // If it's within the limit, return the original name
    return sheetName;
  };

  // exportToPDF = () => {
  //   const { gridApi, gridColumnApi } = this.state;
  //   if (!gridApi) {
  //     console.error('Grid API is not available.');
  //     return;
  //   }
  //   const rowData = [];
  //   gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

  //   const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
  //     header: col.getColDef().headerName,
  //     field: col.getColDef().field
  //   }));

  //   const formatDate = dateStr => {
  //     if (!dateStr) return '';
  //     const date = new Date(dateStr);
  //     if (isNaN(date)) return dateStr;
  //     return new Intl.DateTimeFormat('en-US', {
  //       month: '2-digit',
  //       day: '2-digit',
  //       year: '2-digit'
  //     }).format(date);
  //   };

  //   const doc = new jsPDF('l', 'mm', 'a3');
  //   const marginLeft = 5;
  //   const marginTop = 10;

  //   doc.setFontSize(16);
  //   doc.text(
  //     this.state.gridType == 'Customer' || this.state.gridType == 'All'
  //       ? 'Customer Pay Repair - Labor Target Misses'
  //       : this.state.gridType != 'Customer' && this.state.gridType != 'Internal'
  //       ? 'Customer Pay Repair - Labor Target Misses - ' + this.state.gridType
  //       : this.state.gridType + ' Repair - Labor Target Misses',
  //     marginLeft,
  //     marginTop
  //   );
  //   const summaryLabels = [
  //     'RO Count:',
  //     'Job Count:',
  //     'Hours Sold:',
  //     'Target Price:',
  //     'Actual Sale:',
  //     'Over Sold:',
  //     'Under Sold:',
  //     'Net Difference:'
  //   ];
  //   const summaryValues = [
  //     String(
  //       this.state.roCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  //     ),
  //     String(
  //       this.state.jobCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  //     ),
  //     String(
  //       this.state.soldHours
  //         .toFixed(1)
  //         .toString()
  //         .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  //     ),
  //     String(this.formatTotals(this.state.targetPrice, 'targetPrice')),
  //     String(this.formatTotals(this.state.actualSale, 'actualSale')),
  //     String(this.formatTotals(this.state.overSell, 'overSold')),
  //     String(this.formatTotals(this.state.underSell, 'underSold')),
  //     String(this.formatTotals(this.state.diffSale, 'netDifference'))
  //   ];

  //   doc.setFontSize(11);
  //   let xPosition = marginLeft;
  //   const yPosition = 20;

  //   summaryLabels.forEach((label, index) => {
  //     doc.setFont(undefined, 'normal');
  //     doc.setTextColor('black');
  //     doc.text(label, xPosition, yPosition);
  //     xPosition += doc.getTextWidth(label) + 1;

  //     doc.setFont(undefined, 'bold');
  //     let valueNormal = String(summaryValues[index]).replace(/[$,]/g, '');
  //     let value = summaryValues[index];

  //     if (
  //       label === 'Over Sold:' ||
  //       // label === 'Under Sold:' ||
  //       label === 'Net Difference:'
  //     ) {
  //       if (valueNormal < 0) {
  //         doc.setTextColor('red');
  //       } else if (valueNormal >= 0) {
  //         doc.setTextColor('blue');
  //       } else {
  //         doc.setTextColor('black');
  //       }
  //     } else if (label === 'Under Sold:') {
  //       doc.setTextColor('red');
  //     } else {
  //       doc.setTextColor('black');
  //     }

  //     doc.text(String(value), xPosition, yPosition);
  //     xPosition += doc.getTextWidth(String(value)) + 8;
  //   });

  //   doc.setFont(undefined, 'normal');

  //   const headers = columns.map(col => col.header);
  //   const data = rowData.map(row =>
  //     columns.map(col => {
  //       let value = row[col.field];

  //       if (
  //         ['lbrsale', 'gridPricing', 'targetElr', 'actualElr'].includes(
  //           col.field
  //         )
  //       ) {
  //         return this.formatCellValue({ value });
  //       } else if (col.field === 'variance') {
  //         const formattedValue = this.formatCellValueVariance({ value });

  //         return {
  //           content: formattedValue,
  //           color:
  //             value < 0 ? [255, 0, 0] : value > 0 ? [0, 0, 255] : [0, 0, 255]
  //         };
  //       } else if (col.field === 'variancePerc') {
  //         const formattedValue = this.formatCellValuePercent({ value });

  //         return {
  //           content: formattedValue,
  //           color:
  //             value < 0 ? [255, 0, 0] : value > 0 ? [0, 0, 255] : [0, 0, 255]
  //         };
  //       } else if (col.field === 'doorRate') {
  //         return this.formatCellValueRate({ value });
  //       } else if (
  //         typeof value === 'string' &&
  //         value.match(/^\d{4}-\d{2}-\d{2}/)
  //       ) {
  //         return formatDate(value);
  //       } else {
  //         return value;
  //       }
  //     })
  //   );

  //   autoTable(doc, {
  //     startY: 24,
  //     head: [headers],
  //     body: data,
  //     didParseCell: function(data) {
  //       if (
  //         columns[data.column.index]?.field === 'variance' ||
  //         columns[data.column.index]?.field === 'variancePerc'
  //       ) {
  //         if (data.cell.raw && data.cell.raw.color) {
  //           data.cell.styles.textColor = data.cell.raw.color;
  //         }
  //       }
  //     },
  //     theme: 'grid',
  //     styles: {
  //       fontSize: 9,
  //       cellPadding: 1.5,
  //       halign: 'left'
  //     },
  //     headStyles: {
  //       fillColor: '#c65911',
  //       textColor: [255, 255, 255],
  //       fontStyle: 'bold',
  //       fontSize: 10,
  //       halign: 'left'
  //     },
  //     tableWidth: 'auto',
  //     margin: { top: 3, left: 3, right: 3 },
  //     horizontalPageBreak: false,
  //     scaleFactor: 1.2
  //   });

  //   doc.save(
  //     this.state.gridType == 'Customer' || this.state.gridType == 'All'
  //       ? 'Customer Pay Repair - Labor Target Misses'
  //       : this.state.gridType != 'Customer' && this.state.gridType != 'Internal'
  //       ? 'Customer Pay Repair - Labor Target Misses - ' + this.state.gridType
  //       : this.state.gridType + ' Repair - Labor Target Misses' + '.pdf'
  //   );
  // };

  exportToPDF = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({ openAlert: true });
    } else {
      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }

      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };

      // const doc = new jsPDF('l', 'mm', 'a3');
      const doc = new jsPDF('l', 'mm', columns.length <= 30 ? 'a2' : 'a1');
      const marginLeft = 5;
      let yPosition = 10;

      // Title
      doc.setFontSize(16);
      doc.setFont(undefined, 'bold');
      doc.text(
        this.state.parent == 'savedReports'
          ? `Customer Pay Repair - Labor Target Misses - Saved Reports - ${this
              .props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.report_name &&
              this.props.history.location.state.report_name}`
          : this.state.gridType === 'Customer' || this.state.gridType === 'All'
          ? 'Customer Pay Repair - Labor Target Misses'
          : this.state.gridType !== 'Customer' &&
            this.state.gridType !== 'Internal'
          ? 'Customer Pay Repair - Labor Target Misses - ' + this.state.gridType
          : this.state.gridType + ' Repair - Labor Target Misses',
        marginLeft,
        yPosition
      );

      yPosition += 6;

      // "Summary" Subheader
      doc.setFontSize(12);
      doc.setFont(undefined, 'bold');
      doc.setTextColor(0, 0, 0);
      doc.text('Summary', marginLeft, yPosition);

      yPosition += 4;
      doc.setDrawColor(180);
      // doc.line(marginLeft, yPosition, 410, yPosition); // horizontal line

      yPosition += 3;

      // Summary values
      const summaryLabels = [
        'RO Count:',
        'Job Count:',
        'Hours Sold:',
        'Target Price:',
        'Actual Sale:',
        'Over Sold:',
        'Under Sold:',
        'Net Difference:'
      ];
      const summaryValues = [
        String(
          this.state.roCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        ),
        String(
          this.state.jobCount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        ),
        String(
          this.state.soldHours
            .toFixed(1)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        ),
        String(this.formatTotals(this.state.targetPrice, 'targetPrice')),
        String(this.formatTotals(this.state.actualSale, 'actualSale')),
        String(this.formatTotals(this.state.overSell, 'overSold')),
        String(this.formatTotals(this.state.underSell, 'underSold')),
        String(this.formatTotals(this.state.diffSale, 'netDifference'))
      ];

      doc.setFontSize(11);
      doc.setFont(undefined, 'normal');
      let xPosition = marginLeft;

      summaryLabels.forEach((label, index) => {
        doc.setFont(undefined, 'normal');
        doc.setTextColor('black');
        doc.text(label, xPosition, yPosition);

        xPosition += doc.getTextWidth(label) + 1;

        doc.setFont(undefined, 'bold');
        let valueNormal = String(summaryValues[index]).replace(/[$,]/g, '');
        let value = summaryValues[index];

        if (label === 'Over Sold:' || label === 'Net Difference:') {
          if (valueNormal < 0) {
            doc.setTextColor('red');
          } else {
            doc.setTextColor('blue');
          }
        } else if (label === 'Under Sold:') {
          doc.setTextColor('red');
        } else {
          doc.setTextColor('black');
        }

        doc.text(String(value), xPosition, yPosition);
        xPosition += doc.getTextWidth(String(value)) + 8;
      });

      // Table
      const headers = columns.map(col => col.header);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];

          if (
            ['lbrsale', 'gridPricing', 'targetElr', 'actualElr'].includes(
              col.field
            )
          ) {
            return this.formatCellValue({ value });
          } else if (col.field === 'variance') {
            const formattedValue = this.formatCellValueVariance({ value });

            return {
              content: formattedValue,
              color:
                value < 0 ? [255, 0, 0] : value > 0 ? [0, 0, 255] : [0, 0, 255]
            };
          } else if (col.field === 'variancePerc') {
            const formattedValue = this.formatCellValuePercent({ value });

            return {
              content: formattedValue,
              color:
                value < 0 ? [255, 0, 0] : value > 0 ? [0, 0, 255] : [0, 0, 255]
            };
          } else if (col.field === 'doorRate') {
            return this.formatCellValueRate({ value });
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }
        })
      );

      autoTable(doc, {
        startY: yPosition + 2,
        head: [headers],
        body: data,
        didParseCell: function(data) {
          if (
            columns[data.column.index]?.field === 'variance' ||
            columns[data.column.index]?.field === 'variancePerc'
          ) {
            if (data.cell.raw && data.cell.raw.color) {
              data.cell.styles.textColor = data.cell.raw.color;
            }
          }
        },
        theme: 'grid',
        styles: {
          fontSize: 9,
          cellPadding: 1.5,
          halign: 'left'
        },
        headStyles: {
          fillColor: '#c65911',
          textColor: [255, 255, 255],
          fontStyle: 'bold',
          fontSize: 10,
          halign: 'left'
        },
        tableWidth: 'auto',
        margin: { top: 3, left: 3, right: 3 },
        horizontalPageBreak: false,
        scaleFactor: 1.2
      });

      doc.save(
        this.state.parent == 'savedReports'
          ? `Customer Pay Repair - Labor Target Misses - Saved Reports - ${this
              .props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.report_name &&
              this.props.history.location.state.report_name}`
          : this.state.gridType === 'Customer' || this.state.gridType === 'All'
          ? 'Customer Pay Repair - Labor Target Misses'
          : this.state.gridType !== 'Customer' &&
            this.state.gridType !== 'Internal'
          ? 'Customer Pay Repair - Labor Target Misses - ' + this.state.gridType
          : this.state.gridType + ' Repair - Labor Target Misses' + '.pdf'
      );
    }
  };

  onBtExport = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({ openAlert: true });
    } else {
      const { gridType, rawGridApi, columnDefs, rawGridColumnApi } = this.state;

      const sheetTitle =
        this.state.parent == 'savedReports'
          ? `Customer Pay Repair - Labor Target Misses - Saved Reports - ${this.props.history.location?.state?.report_name}`
          : gridType === 'Customer' || gridType === 'All'
          ? 'Customer Pay Repair - Labor Target Misses'
          : gridType !== 'Customer' && gridType !== 'Internal'
          ? `Customer Pay Repair - Labor Target Misses - ${gridType}`
          : `${gridType} Repair - Labor Target Misses`;

      const columnCount = columnDefs.length;

      const summaryHeader = [
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: sheetTitle },
            mergeAcross: columnCount - 1
          }
        ],
        [
          {
            styleId: 'subHeader',
            data: { type: 'String', value: 'Summary' },
            mergeAcross: columnCount - 1
          }
        ],
        [
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Ro Count:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.state.roCount
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Job Count:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.state.jobCount
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Hours Sold:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.state.soldHours
                .toFixed(1)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Target Price:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.targetPrice, 'targetPrice')
            }
          }
        ],
        [
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Actual Sale:' }
          },
          {
            styleId: 'summaryValue',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.actualSale, 'actualSale')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Over Sold:' }
          },
          {
            styleId: this.state.overSell >= 0 ? 'summaryBlue' : 'summaryRed',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.overSell, 'overSold')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Under Sold:' }
          },
          {
            styleId: 'summaryRed',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.underSell, 'underSold')
            }
          },
          {
            styleId: 'summaryLabel',
            data: { type: 'String', value: 'Net Difference:' }
          },
          {
            styleId: this.state.diffSale >= 0 ? 'summaryBlue' : 'summaryRed',
            data: {
              type: 'String',
              value: this.formatTotals(this.state.diffSale, 'netDifference')
            }
          }
        ],
        []
      ];

      const params = {
        sheetName: this.formatSheetName(sheetTitle),
        fileName: sheetTitle,
        headerRowHeight: 30,
        customHeader: summaryHeader,
        rowHeight: 18,

        processCellCallback: params => {
          let value = params.value;
          const colId = params.column.getColId();

          if (typeof value === 'string') {
            value = value.trim(); // Remove leading/trailing spaces
          }

          processCells(params, 'labor');

          if (colId === 'variancePerc') {
            return value != null
              ? value.endsWith('.00')
                ? value.slice(0, -3) + '%'
                : value + '%'
              : '0%';
          }

          // if (
          //   colId === 'targetElr' ||
          //   colId === 'actualElr' ||
          //   colId === 'gridPricing' ||
          //   colId === 'doorRate' ||
          //   colId === 'lbrsale' ||
          //   colId === 'lbrcost' ||
          //   colId === 'prtextendedsale' ||
          //   colId === 'prtextendedcost' ||
          //   colId === 'lbrgrossprofit' ||
          //   colId === 'prtsgrossprofit' ||
          //   colId === 'elr'
          // ) {
          //   return this.formatCellValue({ ...params, value });
          // }

          if (colId === 'lbrsoldhours' || colId === 'lbractualhours') {
            return this.formatCellValueWithOut$({ ...params, value });
          }

          if (
            colId === 'lbrGrossprofitpercentage' ||
            colId === 'prtsGrossprofitpercentage'
          ) {
            return this.formatCellValueGP({ ...params, value });
          }

          // if (colId === 'mileage') {
          //   return this.formatCellValueMilage({ ...params, value });
          // }

          if (colId === 'markup') {
            return this.formatCellValueMarkup({ ...params, value });
          }

          if (
            ['closeddate', 'opendate', 'gridDate', 'currentgridasof'].some(id =>
              colId.includes(id)
            )
          ) {
            return isValidDate(value)
              ? moment(value).format('MM/DD/YY')
              : value;
          }

          return value;
        }
      };

      if (rawGridColumnApi) {
        const allColumnIds = [];
        rawGridColumnApi.getAllColumns().forEach(column => {
          allColumnIds.push(column.getColId());
        });
        rawGridColumnApi.autoSizeColumns(allColumnIds);
      }

      rawGridApi.exportDataAsExcel(params);
    }
  };

  componentDidMount() {
    if (this.state.selectedFilter == '')
      this.setState({ selectedFilter: 'duration' });
    if (this.state.selectedToggle == '')
      this.setState({ selectedToggle: 'MTD' });
    this.getMonthYearForSelect();
    this.getKpiToggleOptions();
    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousToggle &&
      this.props.history.location.state.previousToggle != 'undefined'
    ) {
      this.setState({
        previousToggle: this.props.history.location.state.previousToggle
      });
    }
  }
  getGridDetails_old = (
    toggle,
    advisor,
    technician,
    filterStart,
    filterEnd
  ) => {
    this.getAgGridData(toggle, advisor, technician, '', filterStart, filterEnd);
  };
  getGridDetails = (toggle, advisor, technician, filterStart, filterEnd) => {
    getGridorMatrixMissesDetails('grid_misses_type', '', result => {
      if (result) {
        let data = result;
        this.setState({
          payTypeList: lodash.uniq(data)
        });
        let selectedAdv =
          this.props?.history?.location?.state?.selectedAdvisor ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('serviceAdvisors'));
        //ReactSession.get('serviceAdvisors');

        let selectedTech =
          this.props?.history?.location?.state?.selectedTech ??
          (this.state.parent === 'savedReports'
            ? ['All']
            : ReactSession.get('technicians'));
        //ReactSession.get('technicians');
        this.setState({ advisors: selectedAdv });
        this.setState({ tech: selectedTech });
        //  this.setState({ gridType: data[0] });
        this.getAgGridData(
          toggle,
          advisor,
          technician,
          data,
          filterStart,
          filterEnd
        );
        const groupColumn = this.state.groupColumn;
        if (
          JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
          this.state.rawGridApi
        ) {
          groupColumn[2]['hide'] = false;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          if (window.sortStateLbrMiss && this.gridApi) {
            this.gridApi.setSortModel(window.sortStateLbrMiss);
          }
          if (window.colStateLbrMisses && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStateLbrMisses);
          }
        } else if (this.state.rawGridApi) {
          groupColumn[2]['hide'] = true;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          if (window.sortStateLbrMiss && this.gridApi) {
            this.gridApi.setSortModel(window.sortStateLbrMiss);
          }
          if (window.colStateLbrMisses && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStateLbrMisses);
          }
        }
      }
    });
  };

  getKpiToggleOptions = () => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        this.setState({
          dates: dataArr,
          thisWeek:
            moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY"),

          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          lastWeek:
            moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY"),

          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          lastTwoWeek:
            moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY"),

          yesterDay: moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          dayBfYest: moment(dataArr[0].dayBeforeYesterday).format(
            "MMM DD ' YY"
          ),
          mtd:
            moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY"),
          lastMonth: moment(dataArr[0].lastmonthstartdate).format('MMM'),
          lastThreeMonths:
            moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM'),
          lastQtr:
            moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM'),

          ytd:
            moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY"),
          lastTwelveMonths:
            moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY"),
          lastYear:
            moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        });
        if (this.state.filterStart == '' && this.state.filterEnd == '') {
          this.setState({
            filterStart: moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
            filterEnd: moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'),
            toggleOption: 'MTD'
          });

          if (localStorage.getItem('kpiDataStatus') == 1) {
            this.setState({
              filterStart: moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
              filterEnd: moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'),
              toggleOption: 'MTD'
            });
          } else {
            this.setState({
              filterStart: moment(dataArr[0].lastmonthstartdate).format(
                'YYYY-MM-DD'
              ),
              filterEnd: moment(dataArr[0].lastmonthenddate).format(
                'YYYY-MM-DD'
              ),
              toggleOption: 'LMONTH'
            });
          }
        }
        this.getGridDetails(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians'),
          this.state.filterStart
            ? this.state.filterStart
            : localStorage.getItem('kpiDataStatus') == 1
            ? moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            : moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD'),
          this.state.filterEnd
            ? this.state.filterEnd
            : localStorage.getItem('kpiDataStatus') == 1
            ? moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
            : moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')

          // ['All'],
          // ['All'],
          // moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'),
          // moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
        );
      }
    });
  };

  getMonthYearForSelect = () => {
    this.setState({
      monthYear: getLast13Months()
    });
    if (
      (this.state.monthYear && this.state.selectedMonthYear == '') ||
      this.state.selectedMonthYear == undefined ||
      !this.state.selectedMonthYear
    )
      this.setState({
        selectedMonthYear: this.state.monthYear[this.state.monthYear.length - 1]
      });
  };

  handlePayTypeChange = event => {
    if (
      this.props.keycloak.realm == 'billknightag' ||
      this.props.keycloak.realm == 'suntrupag'
    ) {
      if (event.target.value == 'Internal') {
        this.setState({ payType: 'I' });
      } else if (event.target.value == 'Warranty') {
        this.setState({ payType: 'W' });
      } else {
        this.setState({ payType: 'C' });
      }
    } else {
      this.setState({ payType: 'C' });
    }
    this.setState({
      rowData: []
    });

    this.setState({ gridType: event.target.value }, function() {
      this.getAgGridData(
        this.state.selectedToggle,
        ReactSession.get('serviceAdvisors'),
        ReactSession.get('technicians'),
        '',
        this.state.filterStart,
        this.state.filterEnd
      );
    });
  };
  handleclick = params => {
    ReactSession.set('kpiHomeToggle', this.state.filterText);
    if (this.state.parent == 'savedReports') {
      this.props.history.push({
        pathname: '/ReportSaved',
        state: {
          toggleOptions: this.state.previousToggle
            ? this.state.previousToggle
            : 'MTD',
          payType: this.state.PrevPayType,
          gridType: this.state.PrevGridType,
          parent: this.state.parent,
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd,
          created_by: this.props.history.location.state.created_by
        }
      });
    } else {
      this.props.history.push({
        pathname: '/Home',
        state: {
          toggleOptions: this.state.previousToggle
            ? this.state.previousToggle
            : 'MTD',
          payType: this.state.PrevPayType,
          gridType: this.state.PrevGridType,
          parent: this.state.parent,
          filterStart: this.state.filterStart,
          filterEnd: this.state.filterEnd
        }
      });
    }
  };

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };

  formatCellValue = params => {
    if (params && params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueWithOut$ = params => {
    return params && params.value != null && params.value != 0
      ? parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      : '0.00';
  };
  formatCellValueVariance = params => {
    if (params && params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '+$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  onColumnMoved = params => {
    const columnOrder = params.columnApi
      .getAllGridColumns()
      .map(col => col.getColId());
    this.setState({ draggedColumn: columnOrder });
  };
  onGridReady = params => {
    const transformedFilterState = Object.fromEntries(
      Object.entries(this.state.filterCol).map(([key, values]) => [
        key,
        { values, filterType: 'set' }
      ])
    );
    const transformedSortState = [
      {
        colId: this.state.sortCol[0],
        sort: this.state.sortCol[1]
      }
    ];
    params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    if (
      this.gridApi &&
      (!this.state.rowData || this.state.rowData.length === 0)
    ) {
      this.gridApi.showNoRowsOverlay();
    }
    this.setState({ gridApi: params.api });
    this.setState({ rawGridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });

    this.setState({
      groupColumn: params.api.columnController.columnDefs
    });
    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null ||
      lodash.isEmpty(this.props.history.location.state)
    ) {
      window.sortStateLbrMiss = {};
      window.filterStateLbrMiss = {};
    }
    // this.getAgGridData(this.state.value);
    if (
      this.gridColumnApi &&
      window.colStateLbrMisses &&
      (this.props.history == undefined ||
        (this.props.history &&
          this.props.history.location &&
          this.props.history.location.state == undefined) ||
        this.props.history.location.state.selectedGridType == undefined)
    ) {
      window.colStateLbrMisses = null;
      this.state.gridColumnApi.resetColumnState();
    }
    if (window.colStateLbrMisses && this.gridColumnApi) {
      this.gridColumnApi.setColumnState(window.colStateLbrMisses);
      // this.gridApi.setSortModel(window.sortStateLbrMiss);
      // this.gridApi.setFilterModel(window.filterStateLbrMiss);
    }
    this.getKpiToggleOptions();
    this.gridApi.setSortModel(window.sortStateLbrMiss);
    this.gridApi.setFilterModel(window.filterStateLbrMiss);

    const groupColumn = this.state.groupColumn;
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.rawGridApi
    ) {
      groupColumn[2]['hide'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateLbrMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateLbrMiss);
      }
      if (window.colStateLbrMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStateLbrMisses);
      }
    } else if (this.state.rawGridApi) {
      groupColumn[2]['hide'] = true;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateLbrMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateLbrMiss);
      }
      if (window.colStateLbrMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStateLbrMisses);
      }
    }
    if (this.state.parent == 'savedReports') {
      window.filterStateLbrMiss = transformedFilterState;
      window.sortStateLbrMiss = transformedSortState;

      if (this.state.draggedColumn.length > 0) {
        window.colStateLbrMisses = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();

        // Reorder columns based on desiredOrder
        const orderedState = this.state.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col ? { ...col, order: index } : null;
          })
          .filter(Boolean);
        // Apply reordered column state
        window.colStateLbrMisses = orderedState;
        this.gridColumnApi.setColumnState(window.colStateLbrMisses);
        // this.gridColumnApi.setColumnState(orderedState);
      }

      if (this.state.displayCol.length > 0) {
        if (this.state.displayCol.includes('All')) {
        } else {
          window.colStateLbrMisses = this.gridColumnApi.getColumnState();
          const visibleColumns = this.state.columnDefs.filter(column =>
            this.state.displayCol.includes(column.field)
          );
          const visibleFields = visibleColumns.map(col => col.field);

          const updatedColState = window.colStateLbrMisses.map(col => ({
            ...col,
            hide: !visibleFields.includes(col.colId) // hide true for matched columns
          }));

          // this.gridColumnApi.setColumnState(updatedColState);
          window.colStateLbrMisses = updatedColState;
          this.gridColumnApi.setColumnState(window.colStateLbrMisses);
        }
      }

      this.gridApi.setFilterModel(window.filterStateLbrMiss);
      this.gridApi.setSortModel(window.sortStateLbrMiss);
    }
  };

  onColumnVisible = params => {
    const columnApi = params.columnApi || this.gridColumnApi;
    const gridApi = this.gridApi;
    const { column, visible } = params;
    // const columnApi = params.columnApi;

    if (column == null && visible == false) {
      this.getTotalsForDisplay([]);
    } else {
      let rowData = [];
      this.gridApi.forEachNodeAfterFilter(node => {
        rowData.push(node.data);
      });
      this.getTotalsForDisplay(rowData);
    }

    const visibleColumns = columnApi.getAllDisplayedColumns();
    const rowCount = gridApi.getDisplayedRowCount();

    const colIds = visibleColumns.map(col => col.colId);

    this.setState({ displayCol: colIds });
    const groupColumnNamed = this.state.groupColumn;

    const fieldToRemove = 'storeName';
    var columnDefs = groupColumnNamed.filter(
      column => column.field !== fieldToRemove
    );

    this.state.rawGridApi.setColumnDefs(columnDefs);
  };

  handleDoorRate = params => {
    const selectedStoreId = JSON.parse(localStorage.getItem('selectedStoreId'));
    if (selectedStoreId.length > 1) {
      this.setState({
        openStoreDlg: true
      });
      return;
    } else {
      window.sortStateLbrMiss = this.gridApi.getSortModel();
      window.filterStateLbrMiss = this.gridApi.getFilterModel();
      window.colStateLbrMisses = this.gridColumnApi.getColumnState();
      if (params.data.doorRate) {
        if (
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'grid_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_grid_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate ==
            'cust_fleet_grid_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate ==
            'paytype_fleet_grid_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate ==
            'opcode_fleet_grid_calc'
        ) {
          this.props.history.push({
            pathname: '/LaborGridPricing',
            state: {
              pageType: 'LaborMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              gridType:
                params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                'opcode_grid_calc'
                  ? params.data.opcode
                  : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                    'cust_fleet_grid_calc'
                  ? params.data.customerName
                  : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                    'paytype_fleet_grid_calc'
                  ? params.data.paytype
                  : params.data.fleetOrPaytypeOrOpcodeFixedRate ==
                    'opcode_fleet_grid_calc'
                  ? params.data.opcode
                  : params.data.gridType.split('- ')[1],
              gridDoorRate: params.value,
              PrevPayType: this.state.PrevPayType,
              selectedGrid: '',
              PrevGridType: this.state.PrevGridType,
              showAllJobs: this.state.checked,
              gridDate: params.data.gridDate,
              storeId: params.data.storeId,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedGridType: this.state.gridType,
              selectedGridFor: params.data.gridFor
            }
          });
        } else if (
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_fix_calc'
        ) {
          this.props.history.push({
            pathname: '/FixedRates',
            state: {
              pageType: 'LaborMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              gridType: params.data.gridType,
              gridDoorRate: params.value,
              PrevPayType: this.state.PrevPayType,
              selectedGrid: '',
              PrevGridType: this.state.PrevGridType,
              showAllJobs: this.state.checked,
              fixedRate: params.value,
              tabSelection: 'one',
              opcode: params.data.opcode,
              storeId: params.data.storeId,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedGridType: this.state.gridType,
              selectedGridFor: params.data.gridFor
            }
          });
        } else if (
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'cust_fleet_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'paytype_fleet_calc' ||
          params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_fleet_calc'
        ) {
          this.props.history.push({
            pathname: '/FleetAccounts',
            state: {
              pageType: 'LaborMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              gridType: params.data.gridType,
              gridDoorRate: params.value,
              PrevPayType: this.state.PrevPayType,
              selectedGrid: '',
              PrevGridType: this.state.PrevGridType,
              showAllJobs: this.state.checked,
              fixedRate: params.value,
              tabSelection: 'one',
              opcode: params.data.opcode,
              selectedOpcode: params.data.opcode,
              storeId: params.data.storeId,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedGridType: this.state.gridType,
              selectedGridFor: params.data.gridFor,
              customerName: params.data.customerName,
              selectedPayType: params.data.paytype,
              fleetType: params.data.fleetOrPaytypeOrOpcodeFixedRate,
              gridDate: params.data.gridDate
            }
          });
        } else {
          this.props.history.push({
            pathname: '/FixedRates',
            state: {
              pageType: 'LaborMisses',
              selectedFilter: this.state.selectedFilter,
              selectedToggle: this.state.selectedToggle,
              selectedMonthYear: this.state.selectedMonthYear,
              parent: this.state.parent,
              timeZone: this.state.timeZone,
              previousToggle: this.state.previousToggle,
              payType: this.state.payType,
              gridType: params.data.gridType,
              gridDoorRate: params.value,
              PrevPayType: this.state.PrevPayType,
              selectedGrid: '',
              PrevGridType: this.state.PrevGridType,
              showAllJobs: this.state.checked,
              fixedRate: params.value,
              tabSelection: 'two',
              paytype: params.data.paytype,
              filterStart: this.state.filterStart,
              filterEnd: this.state.filterEnd,
              selectedGridType: this.state.gridType,
              selectedGridFor: params.data.gridFor
            }
          });
        }
      }
    }
  };

  handleSearchByRo = params => {
    let filterColumn = {};
    let sortColumns = {};
    window.sortStateLbrMiss = this.gridApi.getSortModel();
    window.filterStateLbrMiss = this.gridApi.getFilterModel();
    if (
      window.filterStateLbrMiss != undefined ||
      window.sortStateLbrMiss != undefined
    ) {
      filterColumn = JSON.stringify(
        this.transformData(window.filterStateLbrMiss)
      );
      sortColumns = this.transformSortData(window.sortStateLbrMiss);
    } else {
      filterColumn = null;
      sortColumns = null;
    }
    window.filterState = this.gridApi.getFilterModel();
    window.colStateLbrMisses = this.gridColumnApi.getColumnState();
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        pageType: 'labormisses',
        parent: this.props.parent,
        selectedFilter: this.state.selectedFilter,
        selectedToggle: this.state.selectedToggle,
        selectedMonthYear: this.state.selectedMonthYear,
        parent: this.state.parent,
        timeZone: this.state.timeZone,
        previousToggle: this.state.previousToggle,
        payType: this.state.payType,
        gridType: this.state.gridType,
        PrevPayType: this.state.PrevPayType,
        PrevGridType: this.state.PrevGridType,
        showAllJobs: this.state.checked,
        filterStart: this.state.filterStart,
        filterEnd: this.state.filterEnd,
        storeId: params.data.storeId,
        selectedGridType: this.state.gridType,
        reportName: this.state.reportName,
        filterText: this.state.filterText,
        visibility: this.state.selectedType,
        checkedColumns: this.state.displayCol,
        draggedColumn: this.state.draggedColumn,
        selectedAdvisor: this.state.advisors,
        selectedTech: this.state.tech,

        filterColumns: this.state.filterCol,
        sortColumns: sortColumns,
        jobType: this.state.jobType
      }
    });
  };

  getAgGridData(
    selectedVal,
    advisor,
    technician,
    type,
    filterStart,
    filterEnd
  ) {
    advisor = advisor ? advisor : ['All'];
    this.setState({ serviceAdvisors: advisor });
    this.setState({ Technicians: technician });
    // Show loading overlay before data fetch
    if (this.gridApi) {
      this.gridApi.showLoadingOverlay();
    }
    this.setState({ isLoading: true });
    const groupColumn = this.state.groupColumn;
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.rawGridApi
    ) {
      groupColumn[2]['hide'] = false;
      groupColumn[2]['suppressToolPanel'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateLbrMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateLbrMiss);
      }
      if (window.colStateLbrMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStateLbrMisses);
      }
    } else if (this.state.rawGridApi) {
      groupColumn[2]['hide'] = true;
      groupColumn[2]['suppressToolPanel'] = true;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateLbrMiss && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateLbrMiss);
      }
      if (window.colStateLbrMisses && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStateLbrMisses);
      }
    }
    if (
      this.state.gridType != 'All' &&
      ((type && type.length > 0 && this.state.gridType == '') ||
        (this.state.gridType != '' &&
          type &&
          type.length > 0 &&
          !type.includes(this.state.gridType)))
    ) {
      type = type[0];
      this.setState({ gridType: type });
    }
    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousGridType
    ) {
      this.setState({
        PrevGridType: this.props.history.location.state.previousGridType
      });
    }
    if (
      filterStart != undefined &&
      filterEnd != undefined &&
      filterStart != '' &&
      filterEnd != ''
    ) {
      this.setState({ filterStart: filterStart, filterEnd: filterEnd });
    }
    // let previousGridType =
    // this.props.history && this.props.history.location.state
    //   ? this.props.history.location.state.previousGridType

    //this.setState({ isLoading: true });
    this.getMonthYearForSelect();
    let filterBy = selectedVal;
    let filterType = this.state.selectedFilter;

    if (
      (this.state.advisors == null ||
        this.state.advisors == undefined ||
        (this.state.advisors && this.state.advisors.includes('All'))) &&
      (ReactSession.get('serviceAdvisors') == undefined ||
        (ReactSession.get('serviceAdvisors') &&
          ReactSession.get('serviceAdvisors').includes('All') == true)) &&
      (this.state.tech == null ||
        this.state.tech == undefined ||
        (this.state.tech && this.state.tech.includes('All'))) &&
      (ReactSession.get('technicians') == undefined ||
        (ReactSession.get('technicians') &&
          ReactSession.get('technicians').includes('All') == true))
    ) {
      if (
        this.state.filterStart != undefined &&
        this.state.filterEnd != undefined &&
        this.state.filterStart != '' &&
        this.state.filterEnd != ''
      ) {
        let resultArr = [];
        getDataForLaborMisses(
          filterBy,
          filterType,
          this.state.timeZone,
          this.state.payType,
          this.state.gridType,
          filterStart,
          filterEnd,
          result => {
            if (
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown
                .statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns
            ) {
              resultArr =
                result.data
                  .statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown
                  .statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns;

              resultArr = resultArr.map(item => {
                if (
                  item.customerName &&
                  typeof item.customerName === 'string'
                ) {
                  item.customerName =
                    item.customerName.charAt(0).toUpperCase() +
                    item.customerName.slice(1);
                }
                return item;
              });

              // let gridTypes = resultArr.map(item => item.gridType);
              // if (gridTypes.length > 0) {
              //   this.setState({
              //     payTypeList: lodash.uniq(gridTypes)
              //   });
              // }
              if (this.state.gridType != 'All') {
                resultArr = resultArr.filter(
                  item => item.gridType == this.state.gridType
                );
              }

              this.setState({ completeData: resultArr });
              // setTimeout(() => {
              this.showAllLaborMissesData(resultArr, this.state.checked);
              //  }, 800);
              if (window.filterStateLbrMiss != undefined) {
                this.filterByValue();
              }
              this.setState({ isLoading: false });
              if (this.gridApi) {
                if (this.state.rowData.length > 0) {
                  this.gridApi.hideOverlay();
                } else {
                  this.gridApi.showNoRowsOverlay();
                }
              }
              this.setState({ filterChanged: false });
            }
          }
        );
      }
    } else {
      if (
        this.state.filterStart != undefined &&
        this.state.filterEnd != undefined &&
        this.state.filterStart != '' &&
        this.state.filterEnd != ''
      ) {
        //this.setState({ isLoading: true });
        getDataForAdviorAndTechLaborMisses(
          filterBy,
          filterType,
          this.state.timeZone,
          this.state.payType,
          // this.state.gridType,
          null,
          this.state.advisors &&
            this.state.advisors != undefined &&
            !this.state.advisors.includes('All')
            ? this.state.advisors
            : ReactSession.get('serviceAdvisors'),
          this.state.tech &&
            this.state.tech != undefined &&
            !this.state.tech.includes('All')
            ? this.state.tech
            : ReactSession.get('technicians'),
          this.state.filterStart,
          this.state.filterEnd,
          result => {
            if (
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown
                .statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns
            ) {
              let resultArr =
                result.data
                  .statelessDbdKpiScorecardGetKpiScorecardGriddataDrilldown
                  .statelessDbdKpiScorecardKpiScorecardGriddataDrilldowns;
              if (this.state.gridType != 'All') {
                resultArr = resultArr.filter(
                  item => item.gridType == this.state.gridType
                );
              }
              this.setState({ completeData: resultArr });
              this.showAllLaborMissesData(resultArr, this.state.checked);

              this.setState({ isLoading: false });

              if (this.gridApi) {
                this.gridApi.hideOverlay();
              }
              if (window.filterStateLbrMiss != undefined) {
                this.filterByValue();
              }
            }
          }
        );
      }
    }
  }
  showAllLaborMissesData = (resultArr, checked) => {
    if (this.state.completeData) {
      if (
        resultArr.length > 0 ||
        (this.state.completeData && this.state.completeData.length > 0)
      ) {
        resultArr = resultArr.length > 0 ? resultArr : this.state.completeData;
        let filteredArr = resultArr.filter(item => item.compliance == 'FALSE');
        this.setState({
          rowData: checked ? resultArr : filteredArr
        });
        this.setState({ isLoading: false });
        this.getTotalsForDisplay(checked ? resultArr : filteredArr);
      } else {
        this.setState({
          rowData: resultArr
        });
        this.setState({ isLoading: false });

        this.getTotalsForDisplay(resultArr);
      }
    }
  };
  getTotalsForDisplay = data => {
    var rowData = data ? data : this.state.rowData;
    var soldHours = 0;
    var targetPrice = 0;
    var actualSale = 0;
    var overSell = 0;
    var underSell = 0;
    var diffSale = 0;
    var jobCount = 0;
    var ronumberArr = [];
    if (rowData.length > 0) {
      jobCount = rowData.length;
      rowData.map(item => {
        soldHours += Number(item.lbrsoldhours);
        targetPrice += Number(item.gridPricing);
        // if (Number(item.variance) != 0) {
        actualSale += Number(item.lbrsale);
        // }

        if (Number(item.variance) >= 0) {
          overSell += Number(item.variance);
        } else {
          underSell += Number(item.variance);
        }
      });
      diffSale = actualSale - targetPrice;
      ronumberArr = [...new Set(rowData.map(obj => obj.ronumber))];
    }
    this.setState({
      jobCount: jobCount,
      soldHours: soldHours,
      targetPrice: targetPrice,
      actualSale: actualSale,
      overSell: overSell,
      underSell: underSell,
      diffSale: diffSale,
      roCount: ronumberArr.length
    });
  };

  resetRawData = () => {
    if (this.state.parent == 'savedReports') {
      const propsData = this.props.history && this.props.history.location.state;
      this.setState({
        isLoading: true
      });
      const transformedFilterState = Object.fromEntries(
        Object.entries(propsData.filterColumns).map(([key, values]) => [
          key,
          { values, filterType: 'set' }
        ])
      );
      const transformedSortState = [
        {
          colId: propsData.sortColumns[0],
          sort: propsData.sortColumns[1]
        }
      ];
      window.filterStateLbrMiss = transformedFilterState;
      window.sortStateLbrMiss = transformedSortState;
      this.gridApi.setFilterModel(window.filterStateLbrMiss);
      this.gridApi.setSortModel(window.sortStateLbrMiss);
      if (propsData.draggedColumn.length > 0) {
        window.colStateLbrMisses = this.gridColumnApi.getColumnState();
        const currentState = this.gridColumnApi.getColumnState();
        const orderedState = propsData.draggedColumn
          .map((colId, index) => {
            const col = currentState.find(c => c.colId === colId);
            return col ? { ...col, order: index } : null;
          })
          .filter(Boolean);
        window.colStateLbrMisses = orderedState;
        this.gridColumnApi.setColumnState(window.colStateLbrMisses);
      }

      if (
        propsData.checkedColumns.length > 0 &&
        propsData.checkedColumns[0] != 'All'
      ) {
        window.colStateLbrMisses = this.gridColumnApi.getColumnState();
        const visibleColumns = this.state.columnDefs.filter(column =>
          propsData.checkedColumns.includes(column.field)
        );
        const visibleFields = visibleColumns.map(col => col.field);
        const updatedColState = window.colStateLbrMisses.map(col => ({
          ...col,
          hide: !visibleFields.includes(col.colId)
        }));
        window.colStateLbrMisses = updatedColState;
        this.gridColumnApi.setColumnState(window.colStateLbrMisses);
      }
      this.setState({ gridType: propsData.gridType });
      this.setState({ payType: propsData.payType });
      this.setState({ tech: propsData.selectedTech });
      this.setState({ advisors: propsData.selectedAdvisor });
      this.setState({ selectedType: propsData.visibility });
      this.setState({ filterText: propsData.filterText });
      this.setState({ checked: propsData.jobType == 'All' ? true : false });
      this.setState({ zeroFilter: false });
      setTimeout(() => {
        this.setState({ filterStart: propsData.filterStart });
        this.setState({ filterEnd: propsData.filterEnd });
        this.setState({
          isLoading: false
        });
      }, 500);
    } else {
      this.setState({ zeroFilter: false });
      window.sortStateLbrMiss = {};
      window.filterStateLbrMiss = {};
      this.state.gridColumnApi && this.state.gridColumnApi.resetColumnState();
      if (this.gridApi) {
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
      }
    }
  };
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateLbrMiss);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  handleAllJobs = event => {
    this.setState({
      checked: event.target.checked
    });
    this.showAllLaborMissesData([], event.target.checked);
  };

  formatTotals = (value, type) => {
    if (value != null && value != 0) {
      if (type == 'targetPrice' || type == 'actualSale') {
        return Math.sign(value) > -1
          ? '$' +
              parseFloat(value)
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : '-$' +
              Math.abs(parseFloat(value))
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        return Math.sign(value) > -1
          ? '+$' +
              parseFloat(value)
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : '-$' +
              Math.abs(parseFloat(value))
                .toFixed(0)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
    } else {
      if (type == 'overSold' || type == 'netDifference') {
        return '$0';
      } else if (type == 'underSold') {
        return '$0';
      } else {
        return '$0';
      }
    }
  };

  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    Object.keys(filterValues).forEach(field => {
      const filter = filterValues[field];
      if (filter?.filterType === 'set') {
        const selectedValues = filter.values || [];
        if (selectedValues.length === 0) {
          this.setState({ zeroFilter: true });
        } else {
          this.setState({ zeroFilter: false });
        }
      }
    });
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    this.getTotalsForDisplay(rowData);
  };

  handleJobChange = type => {
    this.setState({ isLoading: true });
    if (type == 'all') {
      this.setState({ checked: true });
    } else if (type == 'non-compliant') {
      this.setState({ checked: false });
    }
    this.showAllLaborMissesData([], type == 'all' ? true : false);
  };

  handleCallback = (event, picker) => {
    this.setState({ filterStart: picker.startDate.format('YYYY-MM-DD') });
    this.setState({ filterEnd: picker.endDate.format('YYYY-MM-DD') });
    this.setState({ filterChanged: true });
    console.log('handleCallback>>>>>>>>', picker.chosenLabel);
    // if (
    //   this.props.history &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.selectedToggle &&
    //   this.state.parent == 'Home'
    // ) {
    this.getFilterText(picker.chosenLabel);
    // }
    // setFilterChanged(true);
  };
  getFilterText = label => {
    var filterText;

    if (label.includes('Yesterday')) {
      filterText = 'YESDT';
    } else if (label.includes('Day Before Yest')) {
      filterText = 'DBYESDT';
    } else if (label.includes('Last Week')) {
      filterText = 'LWEEK';
    } else if (label.includes('This Month')) {
      filterText = 'MTD';
    } else if (label.includes('Last Month')) {
      filterText = 'LMONTH';
    } else if (label.includes('Last 3 Mths')) {
      filterText = 'PLMTHREE';
    } else if (label.includes('Last Qtr')) {
      filterText = 'LQRTR';
    } else if (label.includes('YTD')) {
      filterText = 'YTD';
    } else if (label.includes('Last 12 Mths')) {
      filterText = 'PLYONE';
    } else if (label.includes('Last Year')) {
      filterText = 'LYEAR';
    } else if (label.includes('Custom Range')) {
      filterText = 'CRANGE';
    } else if (label.includes('This Week')) {
      filterText = 'THISWEEK';
    } else if (label.includes('Last 2 Weeks')) {
      filterText = 'LTWOWEEK';
    }
    this.setState({ filterText: filterText });
    ReactSession.set('kpiHomeToggle', filterText);
    return filterText;
  };
  handleCancelAllStores = () => {
    this.setState({
      openStoreDlg: false
    });
  };
  handleMailClick = () => {
    if (
      this.state.displayCol.length == 0 ||
      this.state.completeData.length == 0 ||
      this.state.zeroFilter
    ) {
      this.setState({
        openAlert: true
      });
    } else {
      this.setState({ openDialogue: true });

      const { gridApi, gridColumnApi } = this.state;
      if (!gridApi) {
        console.error('Grid API is not available.');
        return;
      }
      const rowData = [];
      gridApi.forEachNodeAfterFilterAndSort(node => rowData.push(node.data));

      const columns = gridColumnApi.getAllDisplayedColumns().map(col => ({
        header: col.getColDef().headerName,
        field: col.getColDef().field
      }));

      const formatDate = dateStr => {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        if (isNaN(date)) return dateStr;
        return new Intl.DateTimeFormat('en-US', {
          month: '2-digit',
          day: '2-digit',
          year: '2-digit'
        }).format(date);
      };
      const headers = columns.map(col => col.header);
      const data = rowData.map(row =>
        columns.map(col => {
          let value = row[col.field];

          if (
            [
              'prtlist',
              'prtcost',
              'lbrsale',
              'prtextendedcost',
              'prtextendedsale',
              'actualElr',
              'targetElr',
              'gridPricing'
            ].includes(col.field)
          ) {
            return this.formatCellValue({ value });
          } else if (
            ['targetPrice', 'targetExtendedPrice'].includes(col.field)
          ) {
            return this.formatCellValueTargetPrice({ value });
          } else if (col.field === 'grossprofitpercentage') {
            return this.formatCellValueGP({ value });
          } else if (col.field === 'variance') {
            const formattedValue = this.formatCellValueVariance({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'variancePerc') {
            const formattedValue = this.formatCellValuePercent({ value });

            return {
              content: formattedValue,
              color: value < 0 ? '#FF0000' : value > 0 ? '#0000FF' : '#0000FF'
            };
          } else if (col.field === 'doorRate') {
            return this.formatCellValueRate({ value });
          } else if (
            typeof value === 'string' &&
            value.match(/^\d{4}-\d{2}-\d{2}/)
          ) {
            return formatDate(value);
          } else {
            return value;
          }
        })
      );
      this.setState({ rowDataTotal: rowData });
      this.setState({ headerData: headers });
      this.setState({ rowDataPdf: data });
      let userList = [];
      getEmail('NULL', 'NULL', 'view', 'Client_Report_Card_1_Month', result => {
        if (
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
            0
        ) {
          userList.push(
            result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
              item => item.value
            )
          );
          this.setState({ mailUsers: userList });
        } else {
          this.setState({ mailUsers: [] });
        }
      });
    }
  };
  handleCloseEmail = () => {
    this.setState({ openDialogue: false });
  };
  render() {
    var gridDate = '';
    var gridDateArr1 = [];
    var gridDateArr = [];
    if (this.state.rowData.length > 0) {
      gridDateArr1 = lodash.uniqBy(this.state.rowData, obj => obj.gridDate);
      gridDateArr = gridDateArr1.map(item => item.gridDate);
      if (gridDateArr.length > 0) {
        gridDate = moment(lodash.max(gridDateArr)).format('MM/DD/YY');
      }
    }
    const { classes } = this.props;
    let Title = 'Repair - Labor Target Misses';
    if (this.state.gridType) {
      if (this.state.gridType == 'Customer' || this.state.gridType == 'All') {
        Title = 'Customer Pay Repair - Labor Target Misses';
      } else if (
        this.state.gridType != 'Customer' &&
        this.state.gridType != 'Internal' &&
        this.state.gridType != 'Warranty'
      ) {
        Title =
          'Customer Pay Repair - Labor Target Misses - ' + this.state.gridType;
      } else {
        Title = this.state.gridType + ' Repair - Labor Target Misses';
      }
    }

    const titleName =
      this.state.parent == 'savedReports'
        ? `${Title} - Saved Reports - ${this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.report_name &&
            this.props.history.location.state.report_name}  `
        : Title;
    return (
      <div>
        <Page title={Title}></Page>
        {// JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
        // ((this.props.location &&
        //   this.props.location.state &&
        //   this.props.location.state.parent &&
        //   this.props.location.state.parent != 'Home') ||
        //   (this.props.location && this.props.location.state == undefined)) ? (
        //   <Redirect to="/errors/error-404" />
        // ) :
        localStorage.getItem('versionFlag') == 'FALSE' ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <React.Fragment>
            <Paper
              square
              style={{
                margin: 10,
                paddingTop: '6px',
                height: '40px',
                //backgroundColor: Dealer === 'Armatus' ? '#003d6b': '#F4E1E7',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
            >
              <Grid
                container
                className={clsx(this.props.titleContainer, 'reset-dashboard')}
              >
                <Grid
                  item
                  xs={1}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  {(this.state.storeSwitch == false &&
                    this.state.parent == 'savedReports') ||
                  (this.state.storeSwitch == false &&
                    this.props.history &&
                    this.props.history.location.state &&
                    this.props.history.location.state.selectedToggle &&
                    (this.state.parent == 'Home' ||
                      this.state.parent == 'Extended_View')) ? (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      onClick={this.handleclick}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  ) : (
                    ''
                  )}
                </Grid>
                <Grid
                  item
                  xs={10}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(this.props.mainLabel, 'main-title')}
                    style={{
                      width: '100%',
                      display: 'flex',
                      justifyContent: 'center'
                    }} // style={{ width: '100%', color:'#fff' }}
                  >
                    {titleName}
                    {/* {Title} */}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
            <Paper
              square
              style={{
                margin: 8,
                marginLeft: 8,
                height: 50,
                paddingTop: 4,
                paddingLeft: 4
              }}
            >
              {this.state.dates && this.state.dates[0] && (
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={clsx(classes.formControl, 'input-container')}
                  style={{ margin: 5 }}
                >
                  <DateRangePicker
                    initialSettings={{
                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },
                      ranges: {
                        ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.yesterDay]: [
                          moment(
                            this.state.dates[0] && this.state.dates[0].yesterday
                          ).toDate(),
                          moment(
                            this.state.dates[0] && this.state.dates[0].yesterday
                          ).toDate()
                        ],
                        ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.dayBfYest]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].dayBeforeYesterday
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].dayBeforeYesterday
                          ).toDate()
                        ],
                        ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.thisWeek]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].thisweekstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].thisweekenddate
                          ).toDate()
                        ],
                        ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastWeek]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastweekstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastweekenddate
                          ).toDate()
                        ],
                        ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastTwoWeek]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwoweekstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwoweekenddate
                          ).toDate()
                        ],
                        ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.mtd]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].mtdstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].mtdenddate
                          ).toDate()
                        ],
                        ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastMonth]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastmonthstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastmonthenddate
                          ).toDate()
                        ],
                        ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastThreeMonths]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastthreemonthstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastthreemonthenddate
                          ).toDate()
                        ],
                        ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastQtr]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastquarterstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastquarterenddate
                          ).toDate()
                        ],
                        ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.ytd]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].ytdstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].ytdenddate
                          ).toDate()
                        ],
                        ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastTwelveMonths]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwelvemonthstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lasttwelvemonthenddate
                          ).toDate()
                        ],
                        ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        this.state.lastYear]: [
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastyearstartdate
                          ).toDate(),
                          moment(
                            this.state.dates[0] &&
                              this.state.dates[0].lastyearenddate
                          ).toDate()
                        ]
                      },
                      //  maxDate: moment().toDate(),
                      //maxDate: moment(this.state.closedDate).toDate(),
                      maxDate: moment(
                        this.state.dates[0] && this.state.dates[0].today
                      ).toDate(),
                      alwaysShowCalendars: false,
                      applyClass: clsx(classes.calButton, 'apply-btn'),
                      cancelClass: clsx(classes.calButton, 'apply-btn'),
                      startDate:
                        this.state.filterStart && this.state.filterStart != ''
                          ? moment(this.state.filterStart).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(
                              this.state.dates[0] &&
                                this.state.dates[0].mtdstartdate
                            ).toDate()
                          : moment(
                              this.state.dates[0] &&
                                this.state.dates[0].lastmonthstartdate
                            ).toDate(),
                      endDate:
                        this.state.filterEnd && this.state.filterEnd != ''
                          ? moment(this.state.filterEnd).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(
                              this.state.dates[0] &&
                                this.state.dates[0].mtdenddate
                            ).toDate()
                          : moment(
                              this.state.dates[0] &&
                                this.state.dates[0].lastmonthenddate
                            ).toDate()
                      //showDropdowns: true
                    }}
                    onApply={this.handleCallback}
                  >
                    <input
                      type="text"
                      className="datepicker"
                      id="picker"
                      name="picker"
                      aria-labelledby="label-picker"
                    />
                    {/* <TextField
                      id="outlined-basic"
                      label="Select Date"
                      size="small"
                      //onChange={}
                      value={this.state.value}
                      variant="outlined"
                    /> */}
                  </DateRangePicker>
                  <label class="labelpicker" for="picker" id="label-picker">
                    <div class="textpicker">Select Date</div>
                  </label>
                </FormControl>
              )}
              {this.state.payTypeList.length > 1 ? (
                <span className="payTypeList">
                  <FormControl margin="dense" variant="outlined">
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ marginTop: -4 }}
                    >
                      Misses
                    </InputLabel>
                    <Select
                      margin="dense"
                      variant="outlined"
                      label="Filter By"
                      name="duration"
                      className={'laborPartsGrid'}
                      value={this.state.gridType}
                      onChange={this.handlePayTypeChange}
                      disabled={
                        this.state.selectedFilter == 'duration' ? false : true
                      }
                    >
                      {/* {this.props.location &&
                        this.props.location.state &&
                        this.props.location.state.parent &&
                        this.props.location.state.parent == 'Home' && ( */}
                      <MenuItem value="All">All</MenuItem>
                      {/* )} */}
                      {this.state.payTypeList
                        .sort((a, b) => a.localeCompare(b)) // Sort in ascending order
                        .map(item => (
                          <MenuItem value={item} key={item}>
                            {item}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                </span>
              ) : null}
              <span className={classes.ButtonSelect} id="ButtonSelectHome">
                <Button
                  className={
                    this.state.checked == false
                      ? classes.reportButtonSelect
                      : classes.reportButton
                  }
                  id="nonCompliantjobs"
                  variant="outlined"
                  style={{ textTransform: 'none' }}
                  // style={{ textTransform: 'none', backgroundColor: '#003d6b' }}
                  onClick={() => this.handleJobChange('non-compliant')}
                >
                  {/* <span style={{color: '#fff'}}> */}
                  Non-Compliant Jobs
                  {/* </span> */}
                </Button>
                <Button
                  className={
                    this.state.checked
                      ? classes.reportButtonSelect
                      : classes.reportButton
                  }
                  id="allJobs"
                  variant="outlined"
                  style={{ textTransform: 'none' }}
                  onClick={() => this.handleJobChange('all')}
                >
                  All Jobs
                </Button>
              </span>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.resetBtn, 'reset-btn')}
                onClick={this.resetRawData}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
              <div className="allItems">
                <div className={clsx(classes.dataAsOf)}>
                  {this.state.closedDate ? (
                    <Typography
                      variant="h6"
                      align="right"
                      style={{
                        fontSize: 12,
                        color: '#7987a1',
                        fontWeight: 'bold'
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between'
                        }}
                      >
                        <div>Data&nbsp;as&nbsp;of&nbsp;:</div>
                        <div className={classes.dataAsOfValue}>
                          {moment(this.state.closedDate).format('MM/DD/YY')}
                        </div>
                      </div>
                    </Typography>
                  ) : (
                    ''
                  )}
                </div>
                <div className={clsx(classes.linkItemsContainer)}>
                  <Tooltip title="Save Report">
                    <Link
                      className={classes.linkItem}
                      style={{
                        //   ...linkStyle
                        cursor: 'pointer',
                        float: 'right',
                        pointerEvents: 'auto',
                        marginRight: '4px',
                        marginTop: '1px'
                      }}
                      onClick={this.handleSaveReport}
                    >
                      <SaveIcon className="saveicon" />
                    </Link>
                  </Tooltip>
                  {this.state.parent == 'savedReports' && (
                    <Tooltip title="Rename and Copy">
                      <Link
                        className={classes.linkItem}
                        // style={linkStyleCopy}
                        style={{
                          display: 'block',
                          cursor: 'pointer',
                          marginTop: '4px',
                          marginRight: '-3px',
                          marginLeft: '-2px'
                          // display:
                          //   (createdBy != '' &&
                          //     createdBy == localStorage.getItem('userID')) ||
                          //   selectedType == 'public' ||
                          //   props.keycloak.realmAccess.roles.includes('superadmin')
                          //     ? 'block'
                          //     : 'none'
                        }}
                        onClick={this.handleCopyReport}
                      >
                        <FileCopyOutlinedIcon
                          style={{
                            height: '0.73em',
                            weight: '0.8em'
                            // display: allStore ? 'none' : 'block'
                          }}
                        />
                      </Link>
                    </Tooltip>
                  )}

                  <Tooltip title="Email Now">
                    <Link
                      className={classes.linkItem}
                      style={{
                        cursor: 'pointer',
                        marginRight: '2px',
                        marginTop: '3px'
                      }}
                      onClick={() => this.handleMailClick()}
                    >
                      <MailIcon
                        style={{
                          height: '1.15em',
                          weight: '1.0em'
                        }}
                      />
                    </Link>
                  </Tooltip>
                  <Tooltip title="Export To PDF">
                    <Link
                      id="export-to-pdf"
                      style={{
                        cursor: 'pointer',
                        float: 'right',
                        marginRight: '4px'
                      }}
                      onClick={this.exportToPDF}
                    >
                      <i
                        className="fas fa-file-pdf"
                        style={{
                          color: 'red'
                        }}
                      ></i>
                    </Link>
                  </Tooltip>
                  <Tooltip title="Export To Excel">
                    <Link
                      id="export-to-excel"
                      style={{
                        cursor: 'pointer',
                        float: 'right',
                        marginRight: '4px'
                      }}
                      onClick={this.onBtExport}
                    >
                      <i
                        className="fas fa-file-excel"
                        style={{ color: 'green' }}
                      ></i>
                    </Link>
                  </Tooltip>
                </div>
              </div>
            </Paper>
          </React.Fragment>
        )}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : (
          <Paper
            square
            style={{
              margin: 8,
              marginLeft: 8,
              height: 35,
              paddingTop: 4,
              paddingLeft: 4
            }}
          >
            <Grid
              container
              xs={12}
              className={classes.rankedTableContainer}

              //className={clsx(classes.titleContainer, 'reset-dashboard')}
            >
              <Grid xs={12}>
                <div>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      RO&nbsp;Count&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b' }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Job&nbsp;Count&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b' }}>
                        {this.state.jobCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Hours&nbsp;Sold&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.state.soldHours
                          .toFixed(1)
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Target&nbsp;Price&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(
                          this.state.targetPrice,
                          'targetPrice'
                        )}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Actual&nbsp;Sale&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(this.state.actualSale, 'actualSale')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Over&nbsp;Sold&nbsp;:&nbsp;
                      {/* #0000FF */}
                      <span style={{ color: '#0000FF', marginLeft: 3 }}>
                        {this.formatTotals(this.state.overSell, 'overSold')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Under&nbsp;Sold&nbsp;:&nbsp;
                      <span style={{ color: 'red', marginLeft: 3 }}>
                        {this.formatTotals(this.state.underSell, 'underSold')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Net&nbsp;Difference&nbsp;:&nbsp;
                      <span
                        style={{
                          color:
                            this.state.diffSale >= 0
                              ? '#0000FF'
                              : this.state.diffSale < 0
                              ? 'red'
                              : '#003d6b',
                          marginLeft: 3
                        }}
                      >
                        {this.formatTotals(
                          this.state.diffSale,
                          'netDifference'
                        )}
                      </span>
                    </Typography>
                  </Button>
                </div>
              </Grid>
            </Grid>
          </Paper>
        )}
        <div
          id="data-tab-labor-misses"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            maxWidth: '100%',
            height: window.innerHeight - 260 + 'px',
            // height:(window.innerHeight-215)+'px',
            alignContent: 'center',
            marginLeft: '8px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            excelStyles={this.state.excelStyles}
            defaultColDef={this.state.defaultColDef}
            popupParent={this.state.popupParent}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            floatingFilter={true}
            suppressRowClickSelection={true}
            headerHeight={this.state.headerHeight}
            onFilterChanged={this.onFilterChanged}
            suppressDragLeaveHidesColumns={true}
            sideBar={this.state.sideBar}
            suppressContextMenu={true}
            onColumnVisible={this.onColumnVisible}
            onColumnMoved={this.onColumnMoved}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
          />
        </div>
        <EmailDialogKpi
          open={this.state.openDialogue}
          handlePopupClose={this.handleCloseEmail}
          mailUsers={this.state.mailUsers}
          // clientReportCardDetails={clientReportCardDetails}
          // image={image}
          // selectedWorkmixOptions={selectedWorkmixOptions}
          iKpiReportType={this.state.iKpiReportType}
          // measuredMTH={measuredMTH}
          // priorMTH={priorMTH}
          // selectedOptions={selectedOptions}
          selectedStoreIds={this.state.selectedStoreIds}
          reportName={this.state.reportName}
          reportNameCopy={this.state.reportNameCopy}
          // selectStoreDetails={selectStoreDetails}
          selectedStoreName={localStorage.getItem('storeSelected')}
          headerData={this.state.headerData}
          rowDataPdf={this.state.rowDataPdf}
          filterStart={this.state.filterStart}
          filterEnd={this.state.filterEnd}
          rowDataTotal={this.state.rowDataTotal}
          selectedServiceAdvisors={ReactSession.get('serviceAdvisors')}
          selectedTechnicians={ReactSession.get('technicians')}
        ></EmailDialogKpi>
        <SaveReportDialog
          openSaveDlg={this.state.openSaveDlg}
          parent={this.state.parent}
          copyFile={this.state.copyFile}
          copy={this.state.copy}
          reportName={this.state.reportName}
          errorReport={this.state.errorReport}
          requiredText={this.state.requiredText}
          selectedType={this.state.selectedType}
          filterText={this.state.filterText}
          onChangeReportName={this.onChangeReportName}
          handleCancelSaveReport={this.handleCancelSaveReport}
          handleOkSaveReport={this.handleOkSaveReport}
          handleSaveAsReport={this.handleSaveAsReport}
          handleCheckboxChange={this.handleCheckboxChange}
          reportNameCopy={this.state.reportNameCopy}
        />
        <SuccessSnackbar
          onClose={this.handleSnackbarClose}
          open={this.state.openSusSnackbar}
          msg={'Report saved successfully!'}
          autoHideDuration={2000}
          //goalFail={this.state.goalFail}
        />
        <AlertSnackbar
          onClose={this.CancelAlertDilog}
          open={this.state.openAlert}
          msg={'No data available'}
        />
        {this.state.openDilog ? (
          <Dialog
            open={this.state.openDilog}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available for custom date range.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.cancelDilog} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
        <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openStoreDlg}
        >
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              This option is not available from this page.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCancelAllStores} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
        {this.state.openDilogAll ? (
          <Dialog
            open={this.state.openDilogAll}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography variant="h6" style={{ textTransform: 'none' }}>
                This option is not available at all stores.
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.CancelDilogAll} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
      </div>
    );
  }
}

const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    marginTop: 7
  },
  dataAsOf: {
    marginRight: 7,
    float: 'right',
    marginTop: 23
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3
  },
  linkItemsContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    float: 'right',
    margin: '21px 6px 0px 0px',
    height: '20px',
    alignItems: 'center'
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '5px !important'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  allJobs: {
    marginTop: 12,
    marginRight: 10
  },
  ButtonSelect: {
    lineHeight: 1.5,
    position: 'absolute',
    '@media (min-width: 2560px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 1920px)': {
      width: 183,
      marginTop: '-5px !important',
      marginBottom: '3px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '-5px !important',
      width: 154,
      marginBottom: '2x !important'
    }
  },
  reportButton: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    }
  },
  reportButtonSelect: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  resetBtn: {
    marginRight: 10,
    float: 'right',
    marginTop: 10
  }
});

export default withKeycloak(withStyles(styles)(LaborMisses));
