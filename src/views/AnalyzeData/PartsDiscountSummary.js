import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import clsx from 'clsx';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { ColumnsToolPanelModule } from '@ag-grid-enterprise/column-tool-panel';
import { getDrillDownDataForDiscountSummaryParts } from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { MasterDetailModule } from '@ag-grid-enterprise/master-detail';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getLastThreeYears, getLast13Months } from 'src/utils/Utils';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import { withKeycloak } from '@react-keycloak/web';
import Alert from '@material-ui/lab/Alert';
import { ReactSession } from 'react-client-session';
import { getComparisonMonths } from 'src/utils/Utils';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
class PartsDiscountSummary extends React.Component {
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       const dateFormats = item => moment(item).format('YYYY-MM');
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData(dateFormats(this.props.monthYear),this.props.serviceAdvisor.split('[')[1]);
  //     }
  //   }
  // }
  // componentDidUpdate(prevProps) { console.log("resetReport=",localStorage.getItem('selectedStoreId'),prevProps.session.storeSelected,this.props.session.storeSelected);
  //   if(prevProps.session.storeSelected && this.props.session.storeSelected){
  //     if (prevProps.session.storeSelected != this.props.session.storeSelected) {
  //       const dateFormat = item => moment(item).format('YYYY-MM');
  //       var initialQueryMonth = dateFormat(this.props.monthYear);
  //       var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
  //         ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
  //         : this.props.serviceAdvisor;
  //         // this.resetRawData();
  //         this.getAgGridData(initialQueryMonth, initialServiceAdvisor);
  //     }
  //   }
  // }

  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (this.state.resetReport != this.props.resetReport) {
      this.setState({ isLoading: true });
      this.handleResetData();
    }

    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = dateFormat(this.props.monthYear);
    var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
      ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
      : this.props.serviceAdvisor;
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        let data = {
          type: 'partsDiscount',
          month_year: this.props.monthYear,
          serviceAdvisor: 'All'
        };
        this.props.parentCallback(data);
        this.setState({ isLoading: true });
        this.setState({ selectedServiceAdvisor: 'All' });
        const dateFormats = item => moment(item).format('YYYY-MM');
        this.getAgGridData(this.props.monthYear, 'All');
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = dateFormat(this.props.monthYear);
    var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
      ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
      : this.props.serviceAdvisor;
    this.state = {
      queryMonth: initialQueryMonth,
      resetReport: this.props.resetReport,
      realm: this.props.keycloak.realm,
      selectedMonthYear: initialQueryMonth,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: initialServiceAdvisor,
      isLoading: true,
      rawGridApi: {},
      gridApi: {},
      overallprtcostmaxValue: '',
      overallprtsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedprtsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      isColumnFiltered: false,
      columnDefs: [
        {
          headerName: 'Month',
          field: 'monthYear',
          chartDataType: 'category',
          width: 100,
          // onCellClicked: this.handleCellClicked,
          suppressMenu: true,

          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          valueFormatter: this.formatCellValueMonthYear
          // cellRenderer: function(params) {
          //   if (params.value != undefined)
          //     return `<a style="cursor: pointer">${moment(params.value).format(
          //       'MM-YYYY'
          //     )}</a>`;
          // }
        },
        {
          headerName: 'Discount Id',
          field: 'disdiscountid',
          chartDataType: 'category',
          width: 180,
          minWidth: 80,
          flex: 1,
          onCellClicked: this.handleCellClicked,
          suppressMenu: true,
          sortable: true,
          unSortIcon: true,
          cellRenderer: function(params) {
            if (params.value != undefined)
              return `<a style="cursor: pointer">${params.value}</a>`;
          },
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              border: ' 0px white'
            };
          }
          // comparator: function(valueA, valueB, nodeA, nodeB, isInverted) {
          //   if (valueA == null && valueB == null) {
          //     return 0;
          //   }
          //   if (valueA == null) {
          //     return -1;
          //   }
          //   if (valueB == null) {
          //     return 1;
          //   }

          //   // Parse as floats if they are numbers, otherwise compare as strings
          //   let numberA = parseFloat(valueA);
          //   let numberB = parseFloat(valueB);

          //   if (!isNaN(numberA) && !isNaN(numberB)) {
          //     return numberA - numberB;
          //   }

          //   // Compare as case-insensitive strings
          //   return valueA
          //     .toString()
          //     .toLowerCase()
          //     .localeCompare(valueB.toString().toLowerCase());
          // }
          // filter: 'agTextColumnFilter'
        },
        {
          headerName: 'Discount Desc',
          field: 'disdesc',
          tooltipField: 'disdesc',
          chartDataType: 'category',
          // filter: 'agTextColumnFilter',
          width: 300,
          minWidth: 150,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },

          resizable: true
        },

        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 180,
          minWidth: 150,
          flex: 1,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true,
          suppressToolPanel: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          // filter: 'agNumberColumnFilter',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Parts Sale',
          field: 'prtsaleforeachdiscountedjobs',
          chartDataType: 'category',
          width: 110,
          minWidth: 60,
          flex: 1,
          cellStyle: this.cellStyles(),
          valueFormatter: this.formatCellValue,
          suppressMenu: true,

          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Parts Discount',
          field: 'eachpartsdiscount',
          chartDataType: 'category',
          width: 110,
          minWidth: 60,
          flex: 1,
          cellStyle: this.cellStyles(),
          valueFormatter: this.formatCellValueDiscount,
          suppressMenu: true,
          unSortIcon: true,
          // filter: 'agNumberColumnFilter',
          comparator: function(valueA, valueB) {
            // Compare the values directly to account for negative signs
            return valueA - valueB;
          }
          // comparator: function(valueA, valueB) {
          //   return Math.abs(valueA) - Math.abs(valueB);
          // }
        },
        {
          headerName: 'Net Sale',
          field: 'netpartssale',
          cellStyle: this.cellStyles(),
          chartDataType: 'category',
          width: 110,
          minWidth: 60,
          flex: 1,
          valueFormatter: this.formatCellValue,
          suppressMenu: true,

          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        },
        {
          headerName: 'Coupon Margin',
          field: 'singlecouponmargin',
          width: 110,
          minWidth: 60,
          flex: 1,
          cellStyle: this.cellStyles(),
          chartDataType: 'category',
          suppressMenu: true,
          valueFormatter: this.formatCellValueMargin,
          unSortIcon: true
          // filter: 'agNumberColumnFilter'
        }
      ],
      rowData: [],
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        filter: true,
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        resizable: false,
        suppressMovable: false
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      // return (
      //   '$' + params.value.toLocaleString()
      //   //   Math.round((params.value + Number.EPSILON) * 100) / 100
      //   // ).toLocaleString()
      // );
    }
  };
  formatCellValueMargin = params => {
    if (params.value != null && !isNaN(params.value)) {
      return params.value;
    } else {
      return 0;
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '($' + params.value.toLocaleString() + ')'
        : '(-$' + Math.abs(params.value).toLocaleString() + ')';
    } else {
      return 0;
    }
  };
  handleCellClicked = params => {
    window.sortStateDisPrts = this.gridApi.getSortModel();
    window.colStateDisPrts = this.state.gridcolumnApi.getColumnState();
    window.filterStateDisPrts = this.gridApi.getFilterModel();
    if (this.state.selectedServiceAdvisor == 'All') {
      let data = {
        type: 'partsdiscount_dilldown',
        month_year: params.data.monthYear,
        discount_id: params.data.disdiscountid,
        isPartsCharts: false
      };
      this.props.parentCallback(data);
    } else {
      let data = {
        type: 'partsdiscount_dilldown_sa',
        month_year: params.data.monthYear,
        discount_id: params.data.disdiscountid,
        serviceAdvisor: params.data.serviceadvisor,
        advisorName: this.state.selectedServiceAdvisor,
        isPartsCharts: false
      };
      this.props.parentCallback(data);
    }
  };
  onColumnVisible = event => {
    if (this.state.selectedServiceAdvisor == 'All') {
      const groupColumnNamed = this.state.groupColumn;

      const fieldToRemove = 'advisorName';
      var columnDefs = groupColumnNamed.filter(
        column => column.field !== fieldToRemove
      );

      this.state.rawGridApi.setColumnDefs(columnDefs);
    }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridcolumnApi: params.columnApi });
    this.setState({
      groupColumn: this.state.rawGridApi.columnController.columnDefs
    });

    if (this.props.history.location.state == undefined) {
      window.sortStateDisPrts = {};
      window.filterStateDisPrts = {};
    }
    this.gridApi.setSortModel(window.sortStateDisPrts);
    this.gridApi.setFilterModel(window.filterStateDisPrts);
    params.api.addEventListener('columnVisible', this.onColumnVisible);
    this.getAgGridData(this.state.queryMonth, this.state.serviceAdvisor);
  };

  getAgGridData(queryMonth, serviceAdvisor) {
    // if (this.state.selectedServiceAdvisor != 'All') {
    const groupColumn = this.state.groupColumn;
    if (serviceAdvisor != 'All') {
      groupColumn[3]['hide'] = false;
      groupColumn[3]['suppressToolPanel'] = false;
      // groupColumn[4]['hide'] = false;
      // groupColumn[3]['hide'] = true;

      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateDisPrts && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateDisPrts);
      }
    } else {
      groupColumn[3]['hide'] = true;
      groupColumn[3]['suppressToolPanel'] = true;

      // groupColumn[4]['hide'] = true;
      // groupColumn[3]['hide'] = false;

      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      if (window.sortStateDisPrts && this.gridApi) {
        this.gridApi.setSortModel(window.sortStateDisPrts);
      }
      // this.state.rawGridApi.sizeColumnsToFit();
    }
    serviceAdvisor = serviceAdvisor
      ? serviceAdvisor
      : this.state.serviceAdvisor;
    var SAAll = [];
    let DrilldownValues = 0;
    let resultSet = [];
    getDrillDownDataForDiscountSummaryParts(
      queryMonth,
      serviceAdvisor,
      result => {
        this.setState({ isLoading: false });
        if (serviceAdvisor == 'All') {
          if (
            result.data.statelessDbdDiscountGetPartsDiscountSummary
              .partsDiscountSummaryDetails
          ) {
            resultSet = this.parseArray(
              result.data.statelessDbdDiscountGetPartsDiscountSummary
                .partsDiscountSummaryDetails
            );
            this.setState({
              rowData: resultSet
            });
            DrilldownValues = this.getSummaryCalculation(resultSet);
            if (window.filterStateDisPrts != undefined) {
              this.filterByValue();
            }
          }
          const groupColumnNamed = this.state.groupColumn;

          const fieldToRemove = 'advisorName';
          var columnDefs = groupColumnNamed.filter(
            column => column.field !== fieldToRemove
          );

          this.state.rawGridApi.setColumnDefs(columnDefs);
        } else {
          if (
            result.data
              .statelessDbdDiscountGetPartsDiscountSummaryByServiceAdvisor
              .partsDiscountSummaryByServiceAdvisorDetails
          ) {
            resultSet = this.parseArray(
              result.data
                .statelessDbdDiscountGetPartsDiscountSummaryByServiceAdvisor
                .partsDiscountSummaryByServiceAdvisorDetails
            );
            let filteredResult = resultSet.filter(
              item =>
                item.serviceadvisor ==
                  this.state.selectedServiceAdvisor
                    .split('[')[1]
                    .split(']')[0] &&
                item.monthYear == this.state.selectedMonthYear
            );
            this.setState({
              rowData: filteredResult
            });

            DrilldownValues = this.getSummaryCalculation(filteredResult);
            if (window.filterStateDisPrts != undefined) {
              this.filterByValue();
            }
          }
        }
        this.getMonthYear();
        getAllServiceAdvisors(result => {
          if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
            var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
              e => e.active == 1
            );
            SAAll = ['All'].concat(
              advArr.map(e =>
                e
                  ? (e.nickname ? e.nickname : e.name) +
                    ' [' +
                    e.serviceadvisor.toString() +
                    ']' +
                    '-status-' +
                    e.active
                  : ''
              )
            );
            this.setState({ serviceAdvisors: SAAll });
          }
        });
      }
    );
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateDisPrts);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  parseArray(filtererdArray) {
    let intArray = [];
    var res = filtererdArray.map(v => {
      intArray.push({
        discountedprtsale: v.discountedprtsale,
        discountedvolume: v.discountedvolume,
        discountjobcount: v.discountjobcount,
        disdesc: v.disdesc,
        disdiscountid: v.disdiscountid,
        eachpartsdiscount: v.eachpartsdiscount
          ? parseFloat(v.eachpartsdiscount)
          : 0,
        prtsaleforeachdiscountedjobs: parseFloat(
          v.prtsaleforeachdiscountedjobs
        ),
        monthYear: v.monthYear,
        netpartssale: v.netpartssale ? parseFloat(v.netpartssale) : 0,
        overallprtcost: v.overallprtcost,
        overallprtsale: v.overallprtsale,
        overallcouponmargin: v.overallcouponmargin,
        overalldiscountinmonth: v.overalldiscountinmonth,
        overallrocount: v.overallrocount,
        singlecouponmargin: parseFloat(v.singlecouponmargin),
        cpdiscountedrocount: v.cpdiscountedrocount,
        serviceadvisor: v.serviceadvisor,
        advisorName: v.advisorName
      });
      // return res;
    });
    return intArray;
  }

  getSummaryCalculation = values => {
    var overallprtcost = [];
    var overallprtsale = [];
    var overallrocount = [];
    var cpdiscountedrocount = [];
    var discountedprtsale = [];
    var discountedvolume = [];
    var discountjobcount = [];
    var overallcouponmargin = [];
    var overalldiscountinmonth = [];
    // var sumDiscountedpartsdiscountAll = 0;
    var sumDiscountedpartsdiscount = 0;
    values.forEach(function(value) {
      overallprtcost.push(value.overallprtcost);
      overallprtsale.push(value.overallprtsale);
      overallrocount.push(value.overallrocount);
      cpdiscountedrocount.push(value.cpdiscountedrocount);
      discountedprtsale.push(value.discountedprtsale);
      discountedvolume.push(value.discountedvolume);
      discountjobcount.push(value.discountjobcount);
      overallcouponmargin.push(value.overallcouponmargin);
      overalldiscountinmonth.push(value.overalldiscountinmonth);
      //  sumDiscountedpartsdiscountAll += value.discountedpartdiscount;
      sumDiscountedpartsdiscount += value.eachpartsdiscount;
    });
    var overallprtcostmaxValue = Math.max.apply(null, overallprtcost);
    var overallprtsalemaxValue = Math.max.apply(null, overallprtsale);
    var discountedprtsalemaxValue = Math.max.apply(null, discountedprtsale);
    var overallcouponmarginMaxValue = Math.max.apply(null, overallcouponmargin);
    var overalldiscountinmonthMax = Math.max.apply(
      null,
      overalldiscountinmonth
    );
    this.setState({
      overallprtcostmaxValue:
        '$' +
        (
          Math.round((overallprtcostmaxValue + Number.EPSILON) * 100) / 100
        ).toLocaleString(),
      overallprtsalemaxValue:
        '$' +
        (
          Math.round((overallprtsalemaxValue + Number.EPSILON) * 100) / 100
        ).toLocaleString(),
      discountedprtsalemaxValue:
        '$' +
        (
          Math.round((discountedprtsalemaxValue + Number.EPSILON) * 100) / 100
        ).toLocaleString(),
      overallcouponmarginMaxValue: (
        Math.round((overallcouponmarginMaxValue + Number.EPSILON) * 100) / 100
      ).toLocaleString(),

      // overallprtcostmaxValue: overallprtcost.length > 0 ? Math.max.apply(null, overallprtcost) : 0,
      // overallprtsalemaxValue: overallprtsale.length > 0 ? Math.max.apply(null, overallprtsale): 0,
      overallrocountmaxValue:
        overallrocount.length > 0 ? Math.max.apply(null, overallrocount) : 0,
      cpdiscountedrocountmaxValue:
        cpdiscountedrocount.length > 0
          ? Math.max.apply(null, cpdiscountedrocount)
          : 0,
      //discountedprtsalemaxValue:  discountedprtsale.length > 0 ? '$' +Math.max.apply(null, discountedprtsale): 0,
      discountedvolumemaxValue:
        discountedvolume.length > 0
          ? Math.max.apply(null, discountedvolume).toFixed(2) + '%'
          : 0,
      discountjobcountmaxValue:
        discountjobcount.length > 0
          ? Math.max.apply(null, discountjobcount)
          : 0,
      overalldiscountinmonthMax:
        '($' +
        Math.abs(
          Math.round((overalldiscountinmonthMax + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
    });

    var GPPBeforeDiscount =
      ((overallprtsalemaxValue - overallprtcostmaxValue) /
        overallprtsalemaxValue) *
      100;
    // if(this.state.selectedServiceAdvisor == 'All'){
    //   var GPPAfterDiscount = ((overallprtsalemaxValue - Math.abs(sumDiscountedpartsdiscountAll) - overallprtcostmaxValue) / (overallprtsalemaxValue)) * 100;
    // }else{
    var GPPAfterDiscount =
      ((overallprtsalemaxValue -
        Math.abs(sumDiscountedpartsdiscount) -
        overallprtcostmaxValue) /
        overallprtsalemaxValue) *
      100;
    //}

    this.setState({
      GppBeforeDiscount:
        overallprtsale.length > 0
          ? (
              Math.round((GPPBeforeDiscount + Number.EPSILON) * 100) / 100
            ).toFixed(1) + '%'
          : 0,
      GppAfterDiscount:
        overallprtsale.length > 0
          ? (
              Math.round((GPPAfterDiscount + Number.EPSILON) * 100) / 100
            ).toFixed(1) + '%'
          : 0
    });
  };

  setDrillDownValuesToState = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      LsWarranty: Values.LsWarranty
    });
  };
  handleMonthYearChange = event => {
    let monthYear = event.target.value;
    this.setState({
      selectedMonthYear: monthYear,
      serviceAdvisor: this.state.selectedServiceAdvisor
    });
    this.setState({ isLoading: true });
    setTimeout(
      function() {
        let data = {
          type: 'partsDiscount',
          month_year: monthYear,
          serviceAdvisor: this.state.selectedServiceAdvisor
        };
        this.props.parentCallback(data);
        this.getAgGridData(
          monthYear,
          this.state.selectedServiceAdvisor.split('[')[1]
            ? this.state.selectedServiceAdvisor.split('[')[1].split(']')[0]
            : this.state.selectedServiceAdvisor
        );
      }.bind(this),
      50
    );
  };

  handleServiceAdvisorChange = event => {
    let serviceAdvisor = event.target.value.split('[')[1]
      ? event.target.value.split('[')[1].split(']')[0]
      : event.target.value;
    this.setState({ selectedServiceAdvisor: event.target.value });
    this.setState({ isLoading: true });
    setTimeout(
      function() {
        let data = {
          type: 'partsDiscount',
          month_year: this.state.selectedMonthYear,
          serviceAdvisor: event.target.value
        };
        this.props.parentCallback(data);
        this.getAgGridData(this.state.selectedMonthYear, serviceAdvisor);
      }.bind(this),
      50
    );
  };
  handleResetData = () => {
    var monthValues =
      localStorage.getItem('versionFlag') == 'TRUE'
        ? getLastThreeYears()
        : getLast13Months();
    var monthVal = getComparisonMonths()[1];
    console.log('monthvalues', moment(monthValues[12]).format('MMM-YY'));
    this.setState({ resetReport: this.props.resetReport });
    this.props.setResetDashboard();
    this.setState({ selectedMonthYear: monthVal });
    // this.setState({ selectedMonthYear: monthValues[12] });
    this.setState({ selectedServiceAdvisor: 'All' });

    this.getAgGridData(
      this.state.selectedMonthYear,
      this.state.selectedServiceAdvisor
    );

    this.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);

    window.filterStateDisPrts = {};
    window.sortStateDisPrts = {};

    let data = {
      type: 'partsDiscount',
      month_year: monthVal,
      serviceAdvisor: 'All'
    };
    this.props.parentCallback(data);
  };

  getMonthYear = () => {
    var table = [];
    this.setState({
      discountMonthYear:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? getLastThreeYears()
          : getLast13Months()
    });
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    if (!lodash.isEmpty(filterValues)) {
      this.setState({ isColumnFiltered: true });
    } else {
      this.setState({ isColumnFiltered: false });
    }
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        <Grid container spacing={12}>
          <Grid item xs={6} justify="flex-start" style={{ padding: '2.5px' }}>
            <Paper square classes={{ root: classes.gridContainer }}>
              <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                  Month
                </InputLabel>
                <Select
                  variant="outlined"
                  label="Group By"
                  name="group-by-type"
                  value={this.state.selectedMonthYear}
                  onChange={this.handleMonthYearChange}
                >
                  {/* <MenuItem value={this.state.selectedMonthYear}>
                    {moment(this.state.selectedMonthYear).format('MMM-YY')}
                  </MenuItem> */}
                  {typeof this.state.discountMonthYear != 'undefined' &&
                    Object.values(this.state.discountMonthYear).map(item => (
                      <MenuItem value={item}>
                        {moment(item).format('MMM-YY')}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Paper>
          </Grid>
          <Grid item xs={6} style={{ padding: '2.5px' }}>
            <Paper
              square
              justify="center"
              classes={{ root: classes.gridContainer }}
            >
              <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                  Service Advisor
                </InputLabel>
                <Select
                  variant="outlined"
                  label="Service Advisor"
                  name="group-by-type"
                  value={this.state.selectedServiceAdvisor}
                  onChange={this.handleServiceAdvisorChange}
                  style={
                    this.state.selectedServiceAdvisor == 'All'
                      ? { width: '100px' }
                      : { width: '' }
                  }
                >
                  {/* <MenuItem value={this.state.selectedServiceAdvisor}>
                    {this.state.selectedServiceAdvisor}
                </MenuItem> */}
                  {typeof this.state.serviceAdvisors != 'undefined' &&
                    Object.values(this.state.serviceAdvisors).map(item => (
                      <MenuItem
                        value={item.split('-status-')[0]}
                        style={{
                          backgroundColor:
                            item.split('-status-')[1] == 0
                              ? Dealer === 'Armatus'
                                ? '#ddeaf4'
                                : '#F4E1E7'
                              : '',
                          color:
                            item.split('-status-')[1] == 0
                              ? Dealer === 'Armatus'
                                ? '#969592'
                                : '#F4E1E7'
                              : ''
                        }}
                      >
                        {item.split('-status-')[0]}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Paper>
          </Grid>
        </Grid>

        {/* {this.state.realm == 'haleyag' ? 
      <Alert severity="info" style={{fontSize: 17,padding: 26,fontWeight: 'bold'}}>No Parts Discounts found for the selected month </Alert>
      : ''} */}
        {this.state.isLoading && (
          <div
            style={{
              display: this.state.tabSelection != 'one' ? 'block' : 'block'
            }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              (this.state.rowData.length > 0
                ? window.innerHeight - 450
                : window.innerHeight - 380) + 'px',
            width: '1050px',
            margin: 8,
            display:
              this.state.isLoading || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            enableRangeSelection={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            sideBar={this.state.sideBar}
            floatingFilter={true}
            onFilterChanged={this.onFilterChanged}
            suppressRowClickSelection={true}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
        {!this.state.isLoading && this.state.rowData.length > 0 && (
          <Paper square style={{ margin: 8 }}>
            <div
              className={clsx(classes.root, {
                [classes.hidesummary]: this.state.isColumnFiltered
              })}
            >
              <Grid container item spacing={1} style={{ width: '1050px' }}>
                <SummaryTitle
                  title={'Overall Parts Sale'}
                  value={this.state.overallprtsalemaxValue}
                  //value={this.state.overallprtsalemaxValue != 0 ? '$' +this.state.overallprtsalemaxValue : this.state.overallprtsalemaxValue}
                />
                <SummaryTitle
                  title={'Overall Parts Cost'}
                  value={this.state.overallprtcostmaxValue}
                  //value={this.state.overallprtcostmaxValue != 0 ? '$' +this.state.overallprtcostmaxValue : this.state.overallprtcostmaxValue}
                />
                <SummaryTitle
                  title={'Overall Discount'}
                  value={this.state.overalldiscountinmonthMax}
                />
                <SummaryTitle
                  title={'Overall Coupon Margin'}
                  value={this.state.overallcouponmarginMaxValue}
                />
                <SummaryTitle
                  title={'Overall RO Count'}
                  value={this.state.overallrocountmaxValue}
                />
                <SummaryTitle
                  title={'Discounted RO Count'}
                  value={this.state.cpdiscountedrocountmaxValue}
                />
                <SummaryTitle
                  title={'Discounted Parts Sale'}
                  value={this.state.discountedprtsalemaxValue}
                />
                <SummaryTitle
                  title={'Discounted Volume'}
                  value={this.state.discountedvolumemaxValue}
                />
                <SummaryTitle
                  title={'Discounted Job Count'}
                  value={this.state.discountjobcountmaxValue}
                />
                <SummaryTitle
                  title={'GP % Before Discount'}
                  value={this.state.GppBeforeDiscount}
                />
                <SummaryTitle
                  title={'GP % After Discount'}
                  value={this.state.GppAfterDiscount}
                />
              </Grid>
            </div>
          </Paper>
        )}
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    padding: 8
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  hidesummary: {
    display: 'none'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  gridContainer: {
    display: 'flex',
    marginTop: '10px'
  }
});

export default withKeycloak(withStyles(styles)(PartsDiscountSummary));
