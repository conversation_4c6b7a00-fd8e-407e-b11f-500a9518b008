import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import { Button } from '@material-ui/core';
import {
  Typography,
  FormControl,
  InputLabel,
  LinearProgress,
  Box,
  Paper,
  MenuItem,
  Select,
  Tooltip,
  FormControlLabel,
  RadioGroup,
  Radio,
  Link
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import RestoreIcon from '@material-ui/icons/Restore';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getKpiToggleOptionsWithTimeZone,
  getDataForPartsMisses,
  getTargetGridRate,
  getGridorMatrixPayTypeDetails,
  getDataForAdvisorAndTechPartsMisses
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import css from 'src/assets/scss/main.scss';
import { getLast13Months, getTimeZone } from 'src/utils/Utils';
import Page from 'src/components/Page';
import { ReactSession } from 'react-client-session';
import $ from 'jquery';
import { getLatestClosedDate } from 'src/utils/hasuraServices';
import { getNextMonth, getYearValue } from 'src/utils/Utils';
import ExportIcon from '@material-ui/icons/GetApp';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import TooltipRenderer from './Component/TooltipRenderer';
import Switch from '@material-ui/core/Switch';
import FormGroup from '@material-ui/core/FormGroup';
import { Redirect } from 'react-router-dom';

var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;
var defaultFilterParams = { readOnly: false };

class PartsMisses extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ Technicians: ['All'] });

    this.setState({ store: localStorage.getItem('selectedStoreId') });
    this.setState({ payTypeList: [] });
    this.getMatrixDetails(this.state.selectedToggle, ['All'], ['All']);

    /*getGridorMatrixPayTypeDetails('paytype_matrix', result => {
      if (result) {
        let data = result;
        if (result.includes('Heavy Duty') || result.includes('Fleet')) {
          if (result[0] == 'Heavy Duty' || result[0] == 'Fleet') {
            data = result.reverse();
          }
        }
        if (result.includes('Internal')) {
          data = result.sort();
        }
        this.getAgGridData(this.state.selectedToggle, ['All'], data);
        this.setState({
          payTypeList: data
        });
      }
    });*/
  }
  componentDidUpdate() {
    if (
      ReactSession.get('serviceAdvisors') != undefined ||
      ReactSession.get('technicians') != undefined
    ) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisors,
        ReactSession.get('serviceAdvisors')
      );
      var checkStatusTech = lodash.isEqual(
        this.state.Technicians,
        ReactSession.get('technicians')
      );
      if (
        (checkStatus == false && $('#advisorSelection').hasClass('selected')) ||
        (checkStatusTech == false && $('#techSelection').hasClass('selected'))
      ) {
        this.setState({ payType: this.state.payType });
        this.setState({ selectedFilter: 'duration' });
        this.setState({ selectedToggle: this.state.selectedToggle });
        this.setState({ selectedMonthYear: '' });
        //this.setState({ checked: false });
        this.getAgGridData(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians')
        );
      }
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      console.log(
        'selected=',
        ReactSession.get('selectedStoreId'),
        checkSt,
        localStorage.getItem('selectedStoreId'),
        this.state.store
      );
      if (checkSt == false) {
        // if (this.state.store != undefined) {
        window.sortStatePrtMiss = {};
        window.filterStatePrtMiss = {};
        // }
        this.setState({ payType: this.state.payType });
        this.setState({ selectedFilter: 'duration' });
        this.setState({ selectedToggle: this.state.selectedToggle });
        this.setState({ selectedMonthYear: '' });
        //  window.filterStateParts = {};
        getLatestClosedDate(result => {
          if (result) {
            var openDate = '';
            var Date1 = result[0].value;
            localStorage.setItem('closedDate', Date1);
            this.setState({ closedDate: Date1 });
          }
        });
        this.setState({ checked: false });
        // this.getAgGridData(
        //   this.state.selectedToggle,
        //   ReactSession.get('serviceAdvisors')
        // );
        this.getMatrixDetails(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians')
        );
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  constructor(props) {
    super(props);
    let selFilter = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedFilter
        : this.props.selectedFilter
      : this.props.selectedFilter;
    let selToggle = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedToggle
        : this.props.selectedToggle
      : this.props.selectedToggle;
    let selMonthyr = this.props.history
      ? this.props.history.location.state
        ? this.props.history.location.state.selectedMonthYear
        : this.props.selectedMonthYear
      : this.props.selectedMonthYear;
    let closedDate = localStorage.getItem('closedDate');
    let parent =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.parent
        : '';
    var timeZone = getTimeZone();
    let PayType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.payType
        : 'C';
    let previousPayType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.previousPayType
        : 'C';
    let previousGridType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.previousGridType
        : '';
    let showAllJobs =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : false;
    let kpiDataToggle =
      localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';
    this.state = {
      selectedFilter: selFilter ? selFilter : 'duration',
      selectedToggle: selToggle ? selToggle : kpiDataToggle,
      kpiDataToggle: kpiDataToggle,
      selectedMonthYear: selMonthyr ? selMonthyr : '',
      monthYear: {},
      toggleOptions: [],
      yesterDay: '',
      lastWeek: '',
      mtd: '',
      lastMonth: '',
      ytd: '',
      lastQtr: '',
      lastYear: '',
      dayBeforeYesterday: '',
      closedDate: closedDate,
      parent: parent,
      timeZone: timeZone,
      partsCost: 0,
      targetPrice: 0,
      actualSale: 0,
      overSell: 0,
      underSell: 0,
      diffSale: 0,
      jobCount: 0,
      roCount: 0,
      payType: PayType,
      PrevPayType: previousPayType,
      lastThreeMonths: '',
      lastTwelveMonths: '',
      payTypeList: [],
      PrevGridType: previousGridType,
      checked: showAllJobs,
      columnDefs: [
        {
          headerName: 'RO Open Date',
          field: 'opendate',
          width: 72,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          headerClass: 'ag-header-cell-text'
        },
        {
          headerName: 'RO Cls. Date',
          field: 'closeddate',
          width: 72,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          headerClass: 'ag-header-cell-text'
        },
        {
          headerName: 'RO#',
          field: 'ronumber',
          width: 85,
          suppressMenu: true,
          unSortIcon: true,
          onCellClicked: this.handleSearchByRo,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellClass: 'textAlign',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              //left: '-35px',
              cursor: 'pointer',
              textAlign: 'center',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 140,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'advisorName',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Technician',
          field: 'techName',
          width: 120,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'techName',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Customer',
          field: 'customerName',
          dataType: 'string',
          tooltipField: 'customerName',
          width: 140,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Desc',
          field: 'prtdesc',
          width: 115,
          tooltipField: 'prtdesc',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Number',
          field: 'prtpartno',
          width: 115,
          suppressMenu: true,
          unSortIcon: true
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // }
        },
        {
          headerName: 'Parts Source',
          field: 'prtsource',
          width: 75,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          width: 90,
          chartDataType: 'category',
          cellClass: 'textAlign',
          tooltipField: 'opcodedescription',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: 60,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 68,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Quantity',
          field: 'prtqtysold',
          width: 77,
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Part List Price',
          field: 'prtlist',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Part Unit Cost',
          field: 'prtcost',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Part Unit Sale',
          field: 'lbrsale',
          width: 85,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Extended Cost',
          field: 'prtextendedcost',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Extended Sale',
          field: 'prtextendedsale',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Parts GP%',
          field: 'grossprofitpercentage',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Target Unit Price',
          field: 'targetPrice',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Target Extended Price',
          field: 'targetExtendedPrice',
          width: 80,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '$ Variance',
          field: 'variance',
          width: 98,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueVariance,
          filterParams: {
            valueFormatter: this.formatCellValueVariance
          },
          cellClass: 'twoDecimalPlacesWith$',
          cellClassRules: {
            greenFont: function(params) {
              return (
                params.data.compliance == 'TRUE' &&
                params.value != 0 &&
                params.value != null
              );
            },
            blueFont: function(params) {
              return params.value >= 0 && params.data.compliance == 'FALSE';
            },
            redFont: function(params) {
              return params.value < 0 && params.data.compliance == 'FALSE';
            }
          },
          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              fontWeight: 'bold',
              color:
                params.value >= 0 && params.data.compliance == 'FALSE'
                  ? '#0000FF'
                  : params.value < 0 && params.data.compliance == 'FALSE'
                  ? 'red'
                  : params.data.compliance == 'TRUE' &&
                    params.value != 0 &&
                    params.value != null
                  ? 'green'
                  : ''
            };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '% Variance',
          field: 'variancePerc',
          width: 101,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValuePercent,
          cellClass: 'variance%',
          cellClassRules: {
            greenFont: function(params) {
              return (
                params.data.compliance == 'TRUE' &&
                params.value != 0 &&
                params.value != null
              );
            },
            blueFont: function(params) {
              return params.value >= 0 && params.data.compliance == 'FALSE';
            },
            redFont: function(params) {
              return params.value < 0 && params.data.compliance == 'FALSE';
            }
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              fontWeight: 'bold',
              color:
                params.value >= 0 && params.data.compliance == 'FALSE'
                  ? '#0000FF'
                  : params.value < 0 && params.data.compliance == 'FALSE'
                  ? 'red'
                  : params.data.compliance == 'TRUE' &&
                    params.value != 0 &&
                    params.value != null
                  ? 'green'
                  : ''
            };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Matrix Install Date',
          field: 'gridDate',
          width: 90,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          onCellClicked: this.handleDoorRate,
          suppressMenu: true,
          unSortIcon: true,
          tooltip: params => 'View Matrix',
          cellStyle: function(params) {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'left',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Compliance',
          field: 'compliance',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return {
              textAlign: 'center'
            };
          }
          //cellClass: this.getCellClass
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 48,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        suppressMovable: false,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0.0' }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'blueFont',
          font: {
            color: '0000FF'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };

  onBtExport = () => {
    var params = {
      sheetName:
        this.state.payType == 'I'
          ? 'Internal Repair - Parts Target Misses'
          : 'Customer Pay Repair - Parts Target Misses',
      fileName:
        this.state.payType == 'I'
          ? 'Internal Repair - Parts Target Misses'
          : 'Customer Pay Repair - Parts Target Misses',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value:
                this.state.payType == 'I'
                  ? 'Internal Repair - Parts Target Misses'
                  : 'Customer Pay Repair - Parts Target Misses'
            },
            mergeAcross: 6
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  componentDidMount() {
    if (this.state.selectedFilter == '')
      this.setState({ selectedFilter: 'duration' });
    if (this.state.selectedToggle == '')
      this.setState({ selectedToggle: 'MTD' });
    this.getToggleOptions();
    this.getMonthYearForSelect();
    this.getTargetGridRates();
    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousToggle &&
      this.props.history.location.state.previousToggle != 'undefined'
    ) {
      this.setState({
        previousToggle: this.props.history.location.state.previousToggle
      });
    }
  }

  getTargetGridRates = () => {
    getTargetGridRate(result => {
      if (result.data.statelessCcPhysicalRwGetDoorRate.nodes) {
        let target =
          result.data.statelessCcPhysicalRwGetDoorRate.nodes[0].value;
        this.setState({ targetGridRate: target });
      }
    });
  };

  getMatrixDetails = (toggle, advisor, technician) => {
    getGridorMatrixPayTypeDetails(
      'paytype_matrix',
      JSON.parse(localStorage.getItem('selectedStoreId'))[0],
      result => {
        if (result) {
          let data = result;
          if (
            result.includes('Heavy Duty') ||
            result.includes('Fleet') ||
            result.includes('Commercial')
          ) {
            if (
              result[0] == 'Heavy Duty' ||
              result[0] == 'Fleet' ||
              (result[0] == 'Commercial' && !result.includes('RV'))
            ) {
              result.reverse();
            }
          }
          if (
            result.includes('Commercial') &&
            result.includes('RV') &&
            result.includes('Retail')
          ) {
            data = ['Retail', 'Commercial', 'RV'];
          }
          if (
            result.includes('Warranty') &&
            result.includes('Standard') &&
            result.includes('Fleet')
          ) {
            data = ['Standard', 'Warranty', 'Fleet'];
          }
          if (
            result.includes('Warranty') &&
            result.includes('Standard') &&
            result.includes('Fleet')
          ) {
            data = ['Standard', 'Warranty', 'Fleet'];
          }
          if (result.includes('Internal')) {
            data = result.sort();
          }
          if (result.includes('Highline')) {
            data = result.reverse();
          }
          this.getAgGridData(toggle, advisor, technician, data);
          this.setState({
            payTypeList: data
          });
        }
      }
    );
  };
  getDurationForSelectBox() {
    this.setState({
      yesterDay: moment(this.state.toggleOptions[0].yesterday).format(
        "MMM DD ' YY"
      )
    });
    this.setState({
      lastWeek:
        moment(this.state.toggleOptions[0].lastweekstartdate).format('MMM DD') +
        ' to ' +
        moment(this.state.toggleOptions[0].lastweekenddate).format(
          "MMM DD ' YY"
        )
    });
    this.setState({
      mtd:
        moment(this.state.toggleOptions[0].mtdstartdate).format('MMM DD') +
        ' to ' +
        moment(this.state.toggleOptions[0].mtdenddate).format("MMM DD ' YY")
    });
    this.setState({
      lastMonth: moment(this.state.toggleOptions[0].lastmonthstartdate).format(
        'MMM'
      )
    });
    this.setState({
      ytd:
        moment(this.state.toggleOptions[0].ytdstartdate).format('MMM DD') +
        ' to ' +
        moment(this.state.toggleOptions[0].ytdenddate).format("MMM DD ' YY")
    });
    this.setState({
      lastQtr:
        moment(this.state.toggleOptions[0].lastquarterstartdate).format('MMM') +
        ' to ' +
        // getNextMonth(this.state.toggleOptions[0].lastquarterstartdate) + ' - ' +
        moment(this.state.toggleOptions[0].lastquarterenddate).format('MMM')
    });
    this.setState({
      lastYear:
        moment(this.state.toggleOptions[0].lastyearstartdate).format('MMM') +
        ' to ' +
        moment(this.state.toggleOptions[0].lastyearenddate).format('MMM') +
        " ' " +
        getYearValue(this.state.toggleOptions[0].lastyearenddate)
    });
    this.setState({
      dayBeforeYesterday: moment(
        this.state.toggleOptions[0].dayBeforeYesterday
      ).format("MMM DD ' YY")
    });
    this.setState({
      lastThreeMonths:
        moment(this.state.toggleOptions[0].lastthreemonthstartdate).format(
          'MMM'
        ) +
        ' to ' +
        //getNextMonth(this.state.toggleOptions[0].lastthreemonthstartdate) + ' - ' +
        moment(this.state.toggleOptions[0].lastthreemonthenddate).format('MMM')
    });
    this.setState({
      lastTwelveMonths:
        moment(this.state.toggleOptions[0].lasttwelvemonthstartdate).format(
          "MMM ' YY"
        ) +
        ' to ' +
        moment(this.state.toggleOptions[0].lasttwelvemonthenddate).format(
          "MMM ' YY"
        )
    });
  }
  getToggleOptions = () => {
    //  this.setState({ isLoading: true });
    getKpiToggleOptionsWithTimeZone(this.state.timeZone, result => {
      // this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;

        this.setState({ toggleOptions: dataArr });
        this.getDurationForSelectBox();
      }
    });
  };
  getMonthYearForSelect = () => {
    this.setState({
      monthYear: getLast13Months()
    });
    if (
      (this.state.monthYear && this.state.selectedMonthYear == '') ||
      this.state.selectedMonthYear == undefined ||
      !this.state.selectedMonthYear
    )
      this.setState({
        selectedMonthYear: this.state.monthYear[this.state.monthYear.length - 1]
      });
  };
  handleChange = event => {
    window.sortStatePrtMiss = {};
    window.filterStatePrtMiss = {};
    if (event.target.name == 'duration') {
      this.setState({ selectedToggle: event.target.value }, function() {
        this.getAgGridData(
          this.state.selectedToggle,
          ReactSession.get('serviceAdvisors'),
          ReactSession.get('technicians')
        );
      });
    } else {
      this.setState({ selectedMonthYear: event.target.value }, function() {
        this.getAgGridData(this.state.selectedMonthYear);
      });
    }
  };

  handlePayTypeChange = event => {
    this.setState({ payType: event.target.value }, function() {
      this.getAgGridData(
        this.state.selectedToggle,
        ReactSession.get('serviceAdvisors'),
        ReactSession.get('technicians')
      );
    });
  };

  handlerdChange = event => {
    if (event.target.value == 'duration') {
      this.setState({ selectedFilter: event.target.value }, function() {
        this.getAgGridData(this.state.selectedToggle);
      });
    } else {
      this.setState({ selectedFilter: event.target.value }, function() {
        this.getAgGridData(this.state.selectedMonthYear);
      });
    }
  };
  handleclick = params => {
    this.props.history.push({
      pathname:
        localStorage.getItem('versionFlag') == 'TRUE' ? '/Home' : '/2.4.0/Home',
      state: {
        toggleOptions: this.state.previousToggle
          ? this.state.previousToggle
          : 'MTD',
        payType: this.state.PrevPayType,
        gridType: this.state.PrevGridType,
        parent: this.state.parent
      }
    });
  };

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };

  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0';
    }
  };

  formatCellValueVariance = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '+$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };

  formatCellValuePercent = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.00%';
    }
  };
  onGridReady = params => {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.setState({ rawGridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });

    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null
    ) {
      window.sortStatePrtMiss = {};
      window.filterStatePrtMiss = {};
    }
    if (window.colStatePrtMisses) {
      this.gridColumnApi.setColumnState(window.colStatePrtMisses);
    }
    this.gridApi.setSortModel(window.sortStatePrtMiss);
    this.gridApi.setFilterModel(window.filterStatePrtMiss);

    if (this.state.selectedFilter == 'duration')
      this.getAgGridData(this.state.selectedToggle, ['All']);
    else this.getAgGridData(this.state.selectedMonthYear);
  };

  handleDoorRate = params => {
    window.sortStatePrtMiss = this.gridApi.getSortModel();
    window.filterStatePrtMiss = this.gridApi.getFilterModel();
    window.filterState = this.gridApi.getFilterModel();
    window.colStatePrtMisses = this.gridColumnApi.getColumnState();

    if (params.data.fleetOrPaytypeOrOpcodeFixedRate == 'matrix_calc') {
      this.props.history.push({
        pathname: '/PartsMatrixPricings',
        state: {
          pageType: 'PartsMisses',
          selectedFilter: this.state.selectedFilter,
          selectedToggle: this.state.selectedToggle,
          selectedMonthYear: this.state.selectedMonthYear,
          parent: this.state.parent,
          timeZone: this.state.timeZone,
          previousToggle: this.state.previousToggle,
          payType: this.state.payType,
          PrevPayType: this.state.PrevPayType,
          PrevGridType: this.state.PrevGridType,
          matrixDate: params.value,
          selectedGrid: '',
          showAllJobs: this.state.checked,
          partsSource: params.data.prtsource
        }
      });
    } else if (params.data.fleetOrPaytypeOrOpcodeFixedRate == 'opcode_calc') {
      this.props.history.push({
        pathname: '/FixedRates',
        state: {
          pageType: 'PartsMisses',
          selectedFilter: this.state.selectedFilter,
          selectedToggle: this.state.selectedToggle,
          selectedMonthYear: this.state.selectedMonthYear,
          parent: this.state.parent,
          timeZone: this.state.timeZone,
          previousToggle: this.state.previousToggle,
          payType: this.state.payType,
          gridType: this.state.gridType,
          gridDoorRate: params.value,
          PrevPayType: this.state.PrevPayType,
          selectedGrid: '',
          PrevGridType: this.state.PrevGridType,
          showAllJobs: this.state.checked,
          fixedRate: params.value,
          tabSelection: 'one',
          opcode: params.data.opcode
        }
      });
    } else {
      this.props.history.push({
        pathname: '/FixedRates',
        state: {
          pageType: 'PartsMisses',
          selectedFilter: this.state.selectedFilter,
          selectedToggle: this.state.selectedToggle,
          selectedMonthYear: this.state.selectedMonthYear,
          parent: this.state.parent,
          timeZone: this.state.timeZone,
          previousToggle: this.state.previousToggle,
          payType: this.state.payType,
          gridType: this.state.gridType,
          gridDoorRate: params.value,
          PrevPayType: this.state.PrevPayType,
          selectedGrid: '',
          PrevGridType: this.state.PrevGridType,
          showAllJobs: this.state.checked,
          fixedRate: params.value,
          tabSelection: 'two',
          paytype: params.data.paytype
        }
      });
    }
  };

  handleSearchByRo = params => {
    window.sortStatePrtMiss = this.gridApi.getSortModel();
    window.filterStatePrtMiss = this.gridApi.getFilterModel();
    window.filterState = this.gridApi.getFilterModel();
    window.colStatePrtMisses = this.gridColumnApi.getColumnState();
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        pageType: 'partsmisses',
        parent: this.props.parent,
        selectedFilter: this.state.selectedFilter,
        selectedToggle: this.state.selectedToggle,
        selectedMonthYear: this.state.selectedMonthYear,
        parent: this.state.parent,
        timeZone: this.state.timeZone,
        previousToggle: this.state.previousToggle,
        payType: this.state.payType,
        PrevPayType: this.state.PrevPayType,
        PrevGridType: this.state.PrevGridType,
        showAllJobs: this.state.checked
      }
    });
  };

  getAgGridData(selectedVal, advisor, technician) {
    advisor = advisor ? advisor : ['All'];
    this.setState({ serviceAdvisors: advisor });
    this.setState({ Technicians: technician });

    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.previousGridType
    ) {
      this.setState({
        PrevGridType: this.props.history.location.state.previousGridType
      });
    }
    this.setState({ isLoading: true });
    this.getMonthYearForSelect();
    let filterBy = selectedVal;
    let filterType = this.state.selectedFilter;
    if (
      (ReactSession.get('serviceAdvisors') == undefined ||
        (ReactSession.get('serviceAdvisors') &&
          ReactSession.get('serviceAdvisors').includes('All') == true)) &&
      (ReactSession.get('technicians') == undefined ||
        (ReactSession.get('technicians') &&
          ReactSession.get('technicians').includes('All') == true))
    ) {
      getDataForPartsMisses(
        filterBy,
        filterType,
        this.state.timeZone,
        this.state.payType,
        '',
        '',
        result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
              .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns
          ) {
            let resultArr =
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns;

            this.setState({ completeData: resultArr });
            this.showAllLaborMissesData(resultArr, this.state.checked);
            if (window.filterStatePrtMiss != undefined) {
              this.filterByValue();
            }
          }
        }
      );
    } else {
      getDataForAdvisorAndTechPartsMisses(
        filterBy,
        filterType,
        this.state.timeZone,
        this.state.payType,
        ReactSession.get('serviceAdvisors'),
        ReactSession.get('technicians'),
        result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
              .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns
          ) {
            let resultArr =
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardPartsMatrixGriddataDrilldown
                .statelessDbdKpiScorecardKpiScorecardPartsMatrixGriddataDrilldowns;

            this.setState({ completeData: resultArr });
            this.showAllLaborMissesData(resultArr, this.state.checked);
            if (window.filterStatePrtMiss != undefined) {
              this.filterByValue();
            }
          }
        }
      );
    }
  }
  showAllLaborMissesData = (resultArr, checked) => {
    if (
      resultArr.length > 0 ||
      (this.state.completeData && this.state.completeData.length > 0)
    ) {
      resultArr = resultArr.length > 0 ? resultArr : this.state.completeData;
      let filteredArr = resultArr.filter(item => item.compliance == 'FALSE');
      this.setState({
        rowData: checked ? resultArr : filteredArr
      });
      this.setState({ isLoading: false });
      this.getTotalsForDisplay(checked ? resultArr : filteredArr);
    } else {
      this.setState({ isLoading: false });
      this.setState({
        rowData: resultArr
      });
      this.getTotalsForDisplay(resultArr);
    }
  };
  getTotalsForDisplay = data => {
    var rowData = data ? data : this.state.rowData;
    var partsCost = 0;
    var targetPrice = 0;
    var actualSale = 0;
    var overSell = 0;
    var underSell = 0;
    var diffSale = 0;
    var jobCount = 0;
    var ronumberArr = [];
    if (rowData.length > 0) {
      jobCount = rowData.length;
      rowData.map(item => {
        partsCost += Number(item.prtextendedcost);
        targetPrice += Number(item.targetExtendedPrice);
        actualSale += Number(item.prtextendedsale);
        if (Number(item.variance) >= 0) {
          overSell += Number(item.variance);
        } else {
          underSell += Number(item.variance);
        }
      });
      diffSale = actualSale - targetPrice;
      ronumberArr = [...new Set(rowData.map(obj => obj.ronumber))];
    }
    this.setState({
      jobCount: jobCount,
      partsCost: partsCost,
      targetPrice: targetPrice,
      actualSale: actualSale,
      overSell: overSell,
      underSell: underSell,
      diffSale: diffSale,
      roCount: ronumberArr.length
    });
  };
  handleAllJobs = event => {
    this.setState({
      checked: event.target.checked
    });
    this.showAllLaborMissesData([], event.target.checked);
  };

  onFilterChanged = e => {
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    this.getTotalsForDisplay(rowData);
  };
  resetRawData = () => {
    window.sortStatePrtMiss = {};
    window.filterStatePrtMiss = {};
    this.state.gridColumnApi.resetColumnState();
    if (this.gridApi) {
      this.gridApi.setSortModel(null);
      this.gridApi.setFilterModel(null);
    }
  };

  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStatePrtMiss);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  formatTotals = (value, type) => {
    if (value != null && value != 0) {
      if (
        type == 'targetPrice' ||
        type == 'actualSale' ||
        type == 'partsCost'
      ) {
        return Math.sign(value) > -1
          ? '$' +
              parseFloat(value)
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : '-$' +
              Math.abs(parseFloat(value))
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      } else {
        return Math.sign(value) > -1
          ? '+$' +
              parseFloat(value)
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          : '-$' +
              Math.abs(parseFloat(value))
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
    } else {
      if (type == 'overSold' || type == 'netDifference') {
        return '$0.00';
      } else if (type == 'underSold') {
        return '$0.00';
      } else {
        return '$0.00';
      }
    }
  };

  handleJobChange = type => {
    if (type == 'all') {
      this.setState({ checked: true });
    } else if (type == 'non-compliant') {
      this.setState({ checked: false });
    }
    this.showAllLaborMissesData([], type == 'all' ? true : false);
  };

  render() {
    var gridDate = '';
    var gridDateArr1 = [];
    var gridDateArr = [];

    if (this.state.rowData.length > 0) {
      gridDateArr1 = lodash.uniqBy(this.state.rowData, obj => obj.gridDate);
      gridDateArr = gridDateArr1.map(item => item.gridDate);
      if (gridDateArr.length > 0) {
        gridDate = moment(lodash.max(gridDateArr)).format('MM/DD/YY');
      }
    }
    const { classes } = this.props;
    //   $('#parts-matrics-misses').addClass('active-menu');
    let Title = 'Customer Pay Repair - Parts Target Misses';
    if (this.state.payType == 'I') {
      Title = 'Internal Repair - Parts Target Misses';
    }
    return (
      <div>
        <Page title={Title}></Page>
        {localStorage.getItem('versionFlag') == 'TRUE' ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <React.Fragment>
            <Paper
              square
              style={{
                margin: '10px',
                paddingTop: '6px',
                height: '40px',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
            >
              <Grid
                container
                className={clsx(this.props.titleContainer, 'reset-dashboard')}
              >
                <Grid
                  item
                  xs={4}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  {this.props.history &&
                  this.props.history.location.state &&
                  this.props.history.location.state.selectedToggle &&
                  (this.state.parent == 'Home' ||
                    this.state.parent == 'Extended_View') ? (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      onClick={this.handleclick}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  ) : (
                    ''
                  )}
                </Grid>
                <Grid
                  item
                  xs={8}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(this.props.mainLabel, 'main-title')}
                    style={{ width: '100%' }}
                  >
                    {/* Parts Matrix Misses */}
                    {Title}
                    {/* Customer Pay Repair - Parts Target Misses */}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
            <Paper
              square
              style={{
                margin: 8,
                marginLeft: 8,
                height: 50,
                paddingTop: 4,
                paddingLeft: 4
              }}
            >
              <FormControl>
                <RadioGroup
                  row
                  aria-labelledby="demo-radio-buttons-group-label"
                  defaultValue={this.state.selectedFilter}
                  name="radio-buttons-group"
                  value={this.state.selectedFilter}
                  onChange={this.handlerdChange}
                >
                  {/* <FormControlLabel control={<Radio value="duration" />} label="Filter by Duration" /> */}
                  <FormControl
                    margin="dense"
                    variant="outlined"
                    style={{
                      minWidth: 120,
                      marginLeft: 10,
                      paddingRight: 10
                    }}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ marginTop: -4 }}
                    >
                      Duration
                    </InputLabel>
                    <Select
                      margin="dense"
                      variant="outlined"
                      label="Filter By"
                      name="duration"
                      className={'laborPartsGrid'}
                      value={this.state.selectedToggle}
                      onChange={this.handleChange}
                      disabled={
                        this.state.selectedFilter == 'duration' ? false : true
                      }
                    >
                      <MenuItem
                        value={'YESDT'}
                        disabled={
                          localStorage.getItem('kpiDataStatusYesdt') == 1
                            ? false
                            : true
                        }
                      >
                        <span className={'toggleLabel'}>Yesterday</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.yesterDay}
                        </span>
                      </MenuItem>
                      <MenuItem
                        value={'DBYESDT'}
                        disabled={
                          localStorage.getItem('kpiDataStatusDbYesdt') == 1
                            ? false
                            : true
                        }
                      >
                        <span className={'toggleLabel'}>Day Before Yest.</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.dayBeforeYesterday}
                        </span>
                      </MenuItem>
                      <MenuItem value={'LWEEK'}>
                        <span className={'toggleLabel'}>Last Week</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.lastWeek}
                        </span>
                      </MenuItem>
                      <MenuItem
                        value={'MTD'}
                        disabled={
                          this.state.kpiDataToggle == 'MTD' ? false : true
                        }
                      >
                        <span className={'toggleLabel'}>Mth to Date</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{this.state.mtd}</span>
                      </MenuItem>
                      <MenuItem value={'LMONTH'}>
                        <span className={'toggleLabel'}>Last Mth </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.lastMonth}
                        </span>
                      </MenuItem>
                      <MenuItem value={'PLMTHREE'}>
                        <span className={'toggleLabel'}>Last 3 Mths </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.lastThreeMonths}
                        </span>
                      </MenuItem>
                      <MenuItem value={'LQRTR'}>
                        <span className={'toggleLabel'}>Last Qtr </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.lastQtr}
                        </span>
                      </MenuItem>
                      <MenuItem value={'YTD'}>
                        <span className={'toggleLabel'}>YTD </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{this.state.ytd}</span>
                      </MenuItem>
                      <MenuItem value={'PLYONE'}>
                        <span className={'toggleLabel'}>Last 12 Mths </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.lastTwelveMonths}
                        </span>
                      </MenuItem>
                      <MenuItem value={'LYEAR'}>
                        <span className={'toggleLabel'}>Last Year </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {this.state.lastYear}
                        </span>
                      </MenuItem>
                    </Select>
                  </FormControl>
                </RadioGroup>
              </FormControl>
              {this.state.payTypeList.length > 1 ? (
                <span className="payTypeList">
                  <FormControl margin="dense" variant="outlined">
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ marginTop: -4 }}
                    >
                      Misses
                    </InputLabel>
                    <Select
                      margin="dense"
                      variant="outlined"
                      label="Filter By"
                      name="duration"
                      className={'laborPartsGrid'}
                      value={this.state.payType}
                      onChange={this.handlePayTypeChange}
                      disabled={
                        this.state.selectedFilter == 'duration' ? false : true
                      }
                    >
                      {this.state.payTypeList.map(item => (
                        <MenuItem value={item.substring(0, 1)}>{item}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </span>
              ) : null}

              <span className={classes.ButtonSelect} id="ButtonSelectHome">
                <Button
                  className={
                    this.state.checked == false
                      ? classes.reportButtonSelect
                      : classes.reportButton
                  }
                  id="nonCompliantjobs"
                  variant="outlined"
                  style={{ textTransform: 'none' }}
                  onClick={() => this.handleJobChange('non-compliant')}
                >
                  Non-Compliant&nbsp;Jobs
                </Button>
                <Button
                  // className={classes.reportButtonSelect}
                  className={
                    this.state.checked
                      ? classes.reportButtonSelect
                      : classes.reportButton
                  }
                  id="allJobs"
                  variant="outlined"
                  style={{ textTransform: 'none' }}
                  onClick={() => this.handleJobChange('all')}
                >
                  All Jobs
                </Button>
              </span>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.resetBtn, 'reset-btn')}
                onClick={this.resetRawData}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
              <div className="allItems">
                <div className={clsx(classes.dataAsOf)}>
                  {this.state.closedDate ? (
                    <Typography
                      variant="h6"
                      align="right"
                      style={{
                        fontSize: 12,
                        color: '#7987a1',
                        fontWeight: 'bold'
                      }}
                    >
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginTop: 8
                        }}
                      >
                        <div>Data&nbsp;as&nbsp;of&nbsp;:</div>
                        <div className={classes.dataAsOfValue}>
                          {moment(this.state.closedDate).format('MM/DD/YY')}
                        </div>
                      </div>
                    </Typography>
                  ) : (
                    ''
                  )}
                </div>
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      paddingTop: 21,
                      paddingRight: 10,
                      cursor: 'pointer',
                      float: 'right'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip>
              </div>
            </Paper>
          </React.Fragment>
        )}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : (
          <Paper
            square
            style={{
              margin: 8,
              marginLeft: 8,
              height: 35,
              paddingTop: 4,
              paddingLeft: 4
            }}
          >
            <Grid container xs={12} className={classes.rankedTableContainer}>
              <Grid xs={12}>
                <div>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      RO&nbsp;Count&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.state.roCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Parts&nbsp;Count&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.state.jobCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Parts&nbsp;Cost&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(this.state.partsCost, 'partsCost')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Target&nbsp;Price&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(
                          this.state.targetPrice,
                          'targetPrice'
                        )}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Actual&nbsp;Sale&nbsp;:&nbsp;
                      <span style={{ color: '#003d6b', marginLeft: 3 }}>
                        {this.formatTotals(this.state.actualSale, 'actualSale')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Over&nbsp;Sold&nbsp;:&nbsp;
                      <span style={{ color: '#0000FF', marginLeft: 3 }}>
                        {this.formatTotals(this.state.overSell, 'overSold')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Under&nbsp;Sold&nbsp;:&nbsp;
                      <span style={{ color: 'red', marginLeft: 3 }}>
                        {this.formatTotals(this.state.underSell, 'underSold')}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h6"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Net&nbsp;Difference&nbsp;:&nbsp;
                      <span
                        style={{
                          color:
                            this.state.diffSale >= 0
                              ? '#0000FF'
                              : this.state.diffSale < 0
                              ? 'red'
                              : '#003d6b'
                        }}
                      >
                        {this.formatTotals(
                          this.state.diffSale,
                          'netDifference'
                        )}
                      </span>
                    </Typography>
                  </Button>
                </div>
              </Grid>
            </Grid>
          </Paper>
        )}
        <div
          id="data-tab-parts-misses"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            height: window.innerHeight - 260 + 'px',
            // height:(window.innerHeight-215)+'px',
            //  width: '1050px',
            //  width: '1110px',
            alignContent: 'center',

            marginLeft: '5px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            excelStyles={this.state.excelStyles}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            floatingFilter={true}
            suppressRowClickSelection={true}
            headerHeight={this.state.headerHeight}
            onFilterChanged={this.onFilterChanged}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },
  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    //marginTop: 9
    marginTop: 6
  },
  dataAsOf: {
    marginRight: 6,
    float: 'right',
    // marginTop: 5
    marginTop: 14
  },
  dataAsOfText: {
    marginLeft: 55
  },
  dataAsOfValue: {
    marginLeft: 3
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    '@media (max-width: 1440px)': {
      marginLeft: '0px !important'
    },
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  allJobs: {
    marginTop: 12,
    marginRight: 10
  },
  ButtonSelect: {
    lineHeight: 1.5,
    position: 'absolute',
    '@media (min-width: 2560px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-6px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 1920px)': {
      width: 183,
      marginTop: '-5px !important',
      marginBottom: '3px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '-5px !important',
      width: 154,
      marginBottom: '2x !important'
    }
  },
  reportButton: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    }
  },
  reportButtonSelect: {
    height: 21,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 135
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 174
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 150
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  resetBtn: {
    marginRight: 10,
    float: 'right',
    marginTop: 10
  }
});
export default withStyles(styles)(PartsMisses);
