import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import { withStyles } from '@material-ui/styles';
import {
  Box,
  LinearProgress,
  Paper,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import $ from 'jquery';
import React from 'react';
import sparkline from 'jquery-sparkline';
import { getDrillDownDataForTechDetail } from 'src/utils/hasuraServices';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import Link from '@material-ui/core/Link';
import { withRouter } from 'react-router-dom';
import moment from 'moment';

class DrilldownTechDetailView extends React.Component {
  componentDidMount() {
    this.getAgGridData(this.props.location.state.lbrtechno);
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,

    techNo: '25715'
  };

  constructor(props) {
    super(props);
    var techNo = this.props.location.state.lbrtechno
      ? this.props.location.state.lbrtechno
      : '';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      showCharts: false,
      techNo: this.props.location.state.lbrtechno,
      isLoading: true,
      gridApi: {},
      eGui: {},
      columnDefs: [
        {
          headerName: 'RO Number',
          field: 'ronumber',
          width: 110,
          cellStyle: function(params) {
            return { textAlign: 'left', fontWeight: 'bold' };
          },
          cellRenderer: function(params) {
            return `<a style="color: #000000" href="https://verify.fixedops.cc/FOC3_Searchbyro/ag-grid.html?ronumber=${params.value}&store=${storeId}" target="_blank"  >${params.value}</a>`;
          }
        },
        {
          headerName: 'Month Year',
          field: 'month_year',
          width: 110,
          cellStyle: function() {
            return {
              color: '#000000'
            };
          },
          valueFormatter: this.formatCellValueMonthYear
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          width: 110,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          cellStyle: function() {
            return {
              color: '#000000'
            };
          }
        },

        {
          headerName: 'Advisor',
          field: 'serviceadvisor',
          width: 120
        },

        {
          headerName: 'Labor Tech No',
          field: 'lbrtechno',
          width: 120
        },

        {
          headerName: 'Labor Opcode',
          field: 'lbropcode',
          width: 120
        },
        {
          headerName: 'Labor Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 120,
          minWidth: 120,
          resizable: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 120
        },

        {
          headerName: 'Op Sub Category',
          field: 'opsubcategory',
          width: 120
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: 120
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 120
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor Profit',
          field: 'lbrprofit',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Job Count',
          field: 'jobcount',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor Sold Hours',
          field: 'lbrsoldhours',
          width: 120,
          cellStyle: this.cellStyles
        },

        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts Cost',
          field: 'prtscost',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Parts Profit',
          field: 'prtsprofit',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Labor Actual Hours',
          field: 'lbractualhours',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Tech Productivity',
          field: 'techefficiency',
          width: 120,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'ELR',
          field: 'elr',
          width: 100,
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Markup',
          field: 'markup',
          width: 100,
          cellStyle: this.cellStyles
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      defaultColDef: {
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false
      },
      autoGroupColumnDef: { minWidth: 50 }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right'
    };
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ gridApi: params.api });

    params.api.expandAll();
  };
  onGroupExpandedOrCollapsed = params => {
    params.api.forEachNode(node => {
      node.expanded = true;
    });
  };

  getAgGridData(techNo) {
    this.setState({ isLoading: true });
    getDrillDownDataForTechDetail(techNo, result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessCcDrilldownGetDrillDownTechAdvisorsRevenues.nodes
      ) {
        this.setState({
          rowData:
            result.data.statelessCcDrilldownGetDrillDownTechAdvisorsRevenues
              .nodes
        });
      }
    });
  }
  renderBackButton = () => {
    this.props.history.push({
      pathname: '/Reports'
    });
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        <Paper square style={{ margin: 8, marginTop: '20px' }}>
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Link
              href="#"
              style={{ paddingTop: 12, paddingLeft: 16 }}
              title="Go Back"
              onClick={this.renderBackButton}
            >
              <ArrowBackIcon />
            </Link>
            <Tab
              style={{ textTransform: 'none', paddingRight: 182 }}
              label={<div>Tech Detailed View</div>}
              value="one"
            />
          </Tabs>
        </Paper>

        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: '600px',
            width: '100%',
            margin: 8
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            // enableRangeSelection={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            // getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            //  enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            enableRangeSelection={true}
            onGridReady={this.onGridReady}
            groupDefaultExpanded={1}
            suppressAggFuncInHeader={true}
            enableSorting={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            groupMultiAutoColumn={true}
            getRowStyle={this.getRowStyle}
            tooltipShowDelay={0}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

export default withRouter(DrilldownTechDetailView);
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1)
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  }
});
