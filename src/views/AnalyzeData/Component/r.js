import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Toolbar,
  Typography,
  Tooltip,
  Button
} from '@material-ui/core';
import clsx from 'clsx';
import { withStyles } from '@material-ui/styles';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import $ from 'jquery';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  getDrillDownDataForLabor,
  getDrillDownDataForParts,
  getOpcodeDetailedViewMonthYears
} from 'src/utils/hasuraServices';
import { getLast13Months } from 'src/utils/Utils';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import moment from 'moment';
import { ReactSession } from 'react-client-session';
import { param } from 'jquery';
import { isNonNullType } from 'graphql';
import { theme } from 'highcharts';
import { i } from 'react-dom-factories';
import OpcodeCellRenderer from './OpcodeCellRenderer.js';
import { result } from 'validate.js';

var lodash = require('lodash');
//Opcode - Summary
class RawDataOpcodeGrid extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (this.state.resetReport != this.props.resetReport) {
      this.resetRowData();
    }

    // if (ReactSession.get('serviceAdvisors') != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get('serviceAdvisors')
    //   );
    //   if (checkStatus == false) {
    //     this.getAgGridData(
    //       this.state.queryMonth ? this.state.queryMonth : this.props.month,
    //       this.props.opcode ? this.props.opcode : ''
    //     );
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        window.filterStates = {};
        window.sortStates = {};
        window.filterStatesPrts = {};
        window.sortStatesPrts = {};
        this.setState({ opcode: '' });
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ selectedOpcode: '' });
        // this.getAgGridData(
        //   this.state.queryMonth ? this.state.queryMonth : this.props.month,
        //   this.props.opcode ? this.props.opcode : ''
        // );
        this.setState({ storeChanged: true });
        this.setState({ selectedButton: 'button-3months' });
        console.log('opcode=====1=2', this.props.opcode);

        setTimeout(() => {
          this.getAgGridData(
            this.state.queryMonth ? this.state.queryMonth : this.props.month,
            ''
          );
        }, 500);
      }
    }
  }

  componentDidMount(prevProps, prevState) {
    this.getOpcodeDetailedViewMonthYear();

    if (
      (this.props.isFrom != 'opcodes' && this.props.isFrom != 'searchByRo') ||
      (this.props.isFrom == 'opcodes' &&
        this.props.parent == 'workMixVolume') ||
      (this.props.isFrom == 'opcodes' && this.props.parent == 'workmix') ||
      (this.props.isFrom == 'opcodes' && this.props.parent == 'comparisonchart')
    ) {
      this.handleDurationChange('3months');
    }
  }
  static defaultProps = {
    cellClassRules: {
      greenBackground: function(params) {
        return params.rowIndex % 2 == 0;
      }
    },
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    month: '2019-11'
  };
  constructor(props) {
    super(props);
    var opCode = this.props.opcode
      ? this.props.opcode
      : this.props.history && this.props.history.location.state
      ? this.props.history.location.state.opcode
      : '';

    var realm = localStorage.getItem('realm');
    var isFrom =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.isFrom
        : '';
    var reportType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.reportType
        : this.props.reportType;
    var workmixParent =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.workmixParent
        : this.props.workmixParent;
    var workmixTab =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.workmixTab
        : this.props.workmixTab;
    var opcategory =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.opCategory
        : this.props.opCategory
        ? this.props.opCategory
        : '';
    var prevPath =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.prevPath
        ? this.props.history.location.state.prevPath
        : this.props.prevPath;
    let prevMenu =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.prevMenu;
    let prepathSelected = this.props.history
      ? this.props.history.location && this.props.history.location.prevPath
      : '';
    var summaryTab =
      this.props.isFrom &&
      (this.props.isFrom == 'opcodes' || this.props.isFrom == 'comparisonchart')
        ? ''
        : this.props.isFrom != 'comparisonchart' &&
          this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.selectedButton
        ? this.props.history.location.state.selectedButton.split('-')[1] ==
          '3months'
          ? 3
          : this.props.history.location.state.selectedButton.split('-')[1] ==
            '6months'
          ? 6
          : 12
        : this.props.selectedButton
        ? this.props.selectedButton.split('-')[1] == '3months'
          ? 3
          : this.props.selectedButton.split('-')[1] == '6months'
          ? 6
          : 12
        : 3;
    var selectedButton =
      this.props.isFrom != 'comparisonchart' &&
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedButton
        ? this.props.history.location.state.selectedButton
        : this.props.selectedButton
        ? this.props.selectedButton
        : '3months';

    this.state = {
      queryMonth: this.props.month,
      resetReport: this.props.resetReport,
      opcode:
        (this.props.isFrom == 'opcodelevel' &&
          this.props.selectedReportType == '' &&
          this.props.parent != 'workmix' &&
          this.props.parent != 'comparisonchart' &&
          this.props.parent != 'workMixVolume' &&
          this.props.isFrom != 'workMixVolume') ||
        (this.props.selectedReportType == '' &&
          this.props.isFrom != 'comparisonchart' &&
          this.props.parent != 'comparisonchart' &&
          this.props.isFrom != 'workMixVolume') ||
        (this.props.isFrom == 'opcodelevel' &&
          this.props.parent != 'opcodes' &&
          this.props.parent != 'comparisonchart' &&
          this.props.parent != 'workMixVolume' &&
          this.props.parent != 'workmix' &&
          this.props.parent != 'searchByRo')
          ? ''
          : opCode,
      isLoading: true,
      selectedOpcode:
        this.props.isFrom == 'opcodelevel' &&
        this.props.selectedReportType == '' &&
        this.props.parent != 'workmix' &&
        this.props.parent != 'comparisonchart' &&
        this.props.parent != 'workMixVolume'
          ? ''
          : opCode,
      workmixParent: workmixParent,
      isFrom: this.props.isFrom == 'workmix' ? this.props.isFrom : isFrom,
      reportType: reportType,
      workmixTab: workmixTab,
      opcategory: opcategory,
      jobCount: 0,
      roCount: 0,
      soldHours: 0,
      lbrSale: 0,
      lbrCost: 0,
      prtsSale: 0,
      prtsCost: 0,
      lbrGP: 0,
      prtsGP: 0,
      lbrGPPerc: 0,
      prtsGPPerc: 0,
      monthYearsArr: [],
      rowDataNonfiltered: [],
      rowDataNonfilteredAll: [],
      expanded: 0,
      disabled: false,
      parent: this.props.parent ? this.props.parent : '',
      duration: 3,
      summaryTab: summaryTab,
      selectedButton: selectedButton,
      prevMenu: prepathSelected ? prepathSelected : prevMenu,
      // prevMenu: this.props.session.menuSelected,
      prevPath: prevPath,
      storeChanged: false,
      realm: realm,
      columnDefs: [
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 65,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressToolPanel: true,
          suppressColumnsToolPanel: true,
          unSortIcon: true,
          rowGroup: true,
          hide: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 110 : 110,
          minWidth:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 110 : 110,
          flex: 1,
          hide: false,
          chartDataType: 'category',
          onCellClicked: this.handleCellClicked,

          // tooltip: function(params) {
          //   if (params.data) {
          //     return 'Opcode Details';
          //   }
          // },

          // cellRenderer: function(params) {
          //   console.log('params');
          //   if (params.value != undefined)
          //     return `<a style="cursor:pointer" >${params.value}</a>`;
          //   else return null;
          // },
          cellRenderer: 'OpcodeCellRenderer',
          cellRendererParams: {
            rendererType: 'Summary',
            summaryTab: this.handleEditClicked
          },
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'left',
              border: ' 0px white',
              cursor: 'pointer'
            };
          },
          //   cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Description',
          field: 'opcodedescription',
          tooltipField: 'opcodedescription',
          minWidth: 120,
          width: 120,
          chartDataType: 'category',
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type Grp',
          field: 'paytypegroup',
          width: 90,
          minWidth: 90,
          flex: 1,
          cellClass: 'textAlignCenter',
          filter: 'agSetColumnFilter',
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'RO Ct',
          field: 'rocount',
          width: 70,
          minWidth: 70,
          // flex: 1,
          chartDataType: 'series',
          // hide: this.props.isLabor == true ? true : false,
          cellStyle: this.cellStyles,

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Job Ct',
          field: 'jobcount',
          width: 70,
          minWidth: 70,
          // flex: 1,
          chartDataType: 'series',

          // hide: this.props.isLabor == true ? true : false,
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Category',
          field: 'opcategory',
          width: 120,
          dataType: 'category',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'NA',
          field: 'opsubcategory',
          width: 55,
          sortable: false,
          filter: false,
          suppressMenu: true,

          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 'N/A' ? 'checked' : ''
            } disabled=disabled/>`;
          },
          filter: 'agSetColumnFilter',
          filterParams: {
            values: ['N/A', '']
          },
          cellStyle: params => {
            return params.value && params.value == 'N/A'
              ? { border: ' 0px white', textAlign: 'left' }
              : { display: 'none' };
          }
        },
        {
          headerName: 'Main Gp/Other',
          field: 'tblcategory',
          width: 110,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
          //  suppressSizeToFit: true
        },
        {
          headerName: 'ELR Dominant',
          field: 'dominantElr',
          width: 106,
          minWidth: 106,
          // flex: 1,
          chartDataType: 'series',
          hide: this.props.isLabor == true ? false : true,
          suppressToolPanel: this.props.isLabor == true ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR Average',
          field: 'elr',
          width: 95,
          minWidth: 95,
          // flex: 1,
          chartDataType: 'series',
          hide: this.props.isLabor == true ? false : true,
          suppressToolPanel: this.props.isLabor == true ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '$ ELR Variance',
          field: 'elrVariance',
          width: 100,
          minWidth: 100,
          // flex: 1,
          chartDataType: 'series',

          hide: this.props.isLabor == true ? false : true,
          suppressToolPanel: this.props.isLabor == true ? false : true,
          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              color: params.value > 0 ? 'green' : params.value < 0 ? 'red' : ''
            };
          },
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '% ELR Variance',
          field: 'variancePerc',
          width: 100,
          minWidth: 100,
          // flex: 1,
          chartDataType: 'series',
          hide: this.props.isLabor == true ? false : true,
          suppressToolPanel: this.props.isLabor == true ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValuePercent,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '% Work Mix',
          field: 'workmix',
          width: 95,
          minWidth: 95,
          flex: 1,
          chartDataType: 'category',
          cellStyle: this.cellStyles,

          valueFormatter: function(params) {
            if (params.value != null && params.value != 0) {
              return (
                parseFloat(params.value)
                  .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              );
            } else if (params.value == undefined) {
              return null;
            } else {
              return '0.00%';
            }
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Sold Hours',
          field: 'laborsoldhours',
          width: 86,
          minWidth: 86,
          flex: 1,
          chartDataType: 'series',
          hide: this.props.isLabor == true ? false : true,
          suppressToolPanel: this.props.isLabor == true ? false : true,
          cellStyle: this.cellStyles,

          cellClass: 'twoDecimalPlacesWithOut$',
          valueFormatter: this.formatCellValueWithOut$,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: this.props.isLabor == true ? 'Labor Sale' : 'Parts Sale',
          field: this.props.isLabor == true ? 'lbrsale' : 'partssale',
          width: 90,
          minWidth: 90,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: this.props.isLabor == true ? 'Labor Cost' : 'Parts Cost',
          field: this.props.isLabor == true ? 'lbrcost' : 'partscost',
          width: 85,
          minWidth: 85,
          flex: 1,
          chartDataType: 'series',
          hide: false,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,

          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'partsale',
          width: 90,
          minWidth: 90,
          flex: 1,

          hide: this.props.isLabor == true ? false : true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          suppressToolPanel: this.props.isLabor == true ? false : true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'partcost',
          width: 85,
          minWidth: 85,
          flex: 1,
          hide: this.props.isLabor == true ? false : true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          suppressToolPanel: this.props.isLabor == true ? false : true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: this.props.isLabor == true ? 'Labor GP' : 'Parts GP',
          field: 'grossprofit',
          width: 80,
          minWidth: 80,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: this.props.isLabor == true ? 'Labor GP%' : 'Parts GP%',
          field: 'grossprofitpercentage',
          width: 75,
          minWidth: 75,
          flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,

          valueFormatter: function(params) {
            if (params.value != null && params.value != 0) {
              return (
                parseFloat(params.value)
                  .toFixed(1)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              );
            } else if (params.value == undefined) {
              return null;
            } else {
              return '0.0';
            }
          },
          cellClass: 'oneDecimalPlace',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Parts GP',
          field: 'partGrossprofit',
          width: 80,
          minWidth: 80,
          flex: 1,
          hide: this.props.isLabor == true ? false : true,
          suppressToolPanel: this.props.isLabor == true ? false : true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,

          cellClass: 'twoDecimalPlacesWith',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP%',
          field: 'partGrossprofitpercentage',
          width: 75,
          minWidth: 75,
          flex: 1,
          hide: this.props.isLabor == true ? false : true,
          suppressToolPanel: this.props.isLabor == true ? false : true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true,

          valueFormatter: function(params) {
            if (params.value != null && params.value != 0) {
              return (
                parseFloat(params.value)
                  .toFixed(1)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
              );
            } else if (params.value == undefined) {
              return null;
            } else {
              return '0.0';
            }
          },
          cellClass: 'oneDecimalPlace',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Markup Average',
          // filter: 'agNumberColumnFilter',

          field: 'markup',
          width: 93,
          minWidth: 93,
          flex: 1,
          chartDataType: 'series',
          valueFormatter: this.formatCellValueMarkup,
          hide: this.props.isLabor == true ? true : false,
          suppressToolPanel: this.props.isLabor == true ? true : false,
          cellStyle: this.cellStyles,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Markup Dominant',
          // filter: 'agNumberColumnFilter',
          field: 'dominantMarkup',
          width: 115,
          minWidth: 115,
          flex: 1,
          chartDataType: 'series',
          valueFormatter: this.formatCellValueMarkup,
          hide: this.props.isLabor == true ? true : false,
          suppressToolPanel: this.props.isLabor == true ? true : false,
          cellStyle: this.cellStyles,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Markup Variance',
          // filter: 'agNumberColumnFilter',
          field: 'markupVariance',
          width: 98,
          minWidth: 98,
          flex: 1,
          chartDataType: 'series',
          valueFormatter: this.formatCellValueMarkup,
          hide: this.props.isLabor == true ? true : false,
          suppressToolPanel: this.props.isLabor == true ? true : false,
          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              color: params.value > 0 ? 'green' : params.value < 0 ? 'red' : ''
            };
          },
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '% Markup Variance',
          field: 'variancePerc',
          width: 100,
          minWidth: 100,
          // flex: 1,
          chartDataType: 'series',
          hide: this.props.isLabor == true ? true : false,
          suppressToolPanel: this.props.isLabor == true ? true : false,
          valueFormatter: this.formatCellValuePercentMarkup,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellStyle: this.cellStyles,
          suppressMenu: true,

          unSortIcon: true
        }
      ],
      rowData: [],
      headerHeight: 45,
      frameworkComponents: {
        OpcodeCellRenderer: OpcodeCellRenderer
      },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true,
              // suppressColumnFilter: true,
              suppressColumnSelectAll: true
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        suppressMovable: false
        //  resizable: false
      },
      autoGroupColumnDef: {
        headerName: 'Month',
        field: 'monthYear',
        maxWidth: 125,

        // headerTooltip: 'Month',
        chartDataType: 'category',
        cellRendererParams: {
          suppressCount: true
        },
        suppressMenu: true,
        unSortIcon: true,
        valueFormatter: this.formatCellValueMonthYear,
        filterParams: {
          valueFormatter: this.formatCellValueMonthYear
        },
        cellClass: 'textAlign',
        cellStyle() {
          return { border: ' 0px white' };
        },
        tooltipValueGetter: params => {
          if (params.data == undefined) return 'Expand';
        }
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0.0' }
        },
        {
          id: 'fourDecimalPlaces',
          numberFormat: { format: '###0.0000' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  handleEditClicked = params => {
    if (this.props.isLabor) {
      window.sortStates = this.gridApi.getSortModel();
      window.colStates = this.state.gridColumnApi.getColumnState();
      window.filterStates = this.gridApi.getFilterModel();
    } else {
      window.sortStatesPrts = this.gridApi.getSortModel();
      window.colStatesPrts = this.state.gridColumnApi.getColumnState();
      window.filterStatesPrts = this.gridApi.getFilterModel();
    }

    let data = {
      type: 'opcodelevel',
      selectedButtonType: this.state.selectedButton,
      parent:
        this.props.selectedReportType &&
        this.props.selectedReportType != '' &&
        this.props.isFrom == 'opcodelevel'
          ? this.props.parent
          : this.props.isFrom == 'opcodes' &&
            this.props.opcode == '' &&
            this.props.parent != 'comparisonchart' &&
            this.props.parent != 'opcodes'
          ? 'opcodelevel'
          : this.props.parent == '' && this.props.isFrom == ''
          ? 'opcodelevel'
          : this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.parent
          ? this.props.history.location.state.parent
          : this.props.isFrom,
      isExpanded: this.state.expanded,
      roNumber: this.props.roNumber,
      monthYear: this.props.month
    };
    // console.log('rawlevel', data, this.props);
    return data;
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else if (params.value == undefined) {
      return null;
    } else {
      return '0.00';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0.0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else if (params.value == undefined) {
      return null;
    } else {
      return '$0.00';
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else if (params.value == undefined) {
      return null;
    } else {
      return '0.0000';
    }
  };
  formatCellValuePercent = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else if (params.value == undefined) {
      return null;
    } else {
      return '0.00%';
    }
  };
  formatCellValuePercentMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value).toFixed(2) + '%';
    } else if (params.value == undefined) {
      return null;
    } else {
      return '0.00%';
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  handleCellClicked = params => {
    localStorage.setItem('laborTabSelection', 'five');
    if (this.props.isLabor) {
      window.sortStates = this.gridApi.getSortModel();
      window.colStates = this.state.gridColumnApi.getColumnState();
      window.filterStates = this.gridApi.getFilterModel();
    } else {
      window.sortStatesPrts = this.gridApi.getSortModel();
      window.colStatesPrts = this.state.gridColumnApi.getColumnState();
      window.filterStatesPrts = this.gridApi.getFilterModel();
    }

    if (params.data) {
      let data = {
        type: 'opcodelevel',
        opcode: params.value,
        month_year: params.data.monthYear,
        isLabor: this.props.isLabor,
        opcodePayType: params.data.paytypegroup,
        parent:
          this.props.selectedReportType != '' &&
          this.props.isFrom == 'opcodelevel'
            ? //  || this.props.isFrom == 'opcodes'
              this.props.parent
            : this.props.isFrom == 'opcodes' &&
              this.props.parent == 'comparisonchart'
            ? this.props.parent
            : this.props.isFrom,
        comparisonMonth1: this.props.comparisonMonth1,
        comparisonMonth2: this.props.comparisonMonth2,
        reportType: this.state.reportType,
        workmixParent: this.state.workmixParent,
        workmixTab: this.state.workmixTab,
        selectedReportType: this.props.selectedReportType,
        opcategory: this.state.opcategory,
        roNumber: this.props.roNumber,
        page: this.props.page,
        selectedToggle: this.props.selectedToggle,
        previousToggle: this.props.previousToggle,
        payTypeRo: this.props.payTypeRo,
        PrevPayType: this.props.PrevPayType,
        PrevGridType: this.props.PrevGridType,
        showAllJobs: this.props.showAllJobs,
        itemizationData: this.props.itemizationData
          ? this.props.itemizationData
          : '',
        itemizationTab: this.props.itemizationTab
          ? this.props.itemizationTab
          : '',
        isLabor: this.props.isLabor ? this.props.isLabor : '',
        selectedButtonType:
          this.props.isFrom != 'searchByRo'
            ? this.state.selectedButton
            : undefined,
        isExpanded: this.state.expanded,
        selectedOpcodeAll: this.state.selectedOpcodeAll,
        prevPath: this.state.prevPath,
        prevMenu: this.state.prevMenu
        // selectedReportType: (this.props.history.location && this.props.history.location.state )? this.props.history.location.state.selectedReportType: ''
      };

      this.props.parentCallback(data);
    }
  };
  parseArray(filtererdArray) {
    let intArray = [];

    var res = filtererdArray.map(v => {
      intArray.push({
        elr: parseFloat(v.elr) || 0,
        laborcost: parseFloat(v.laborcost) || 0,
        partscost: parseFloat(v.partscost) || 0,
        grossprofit: parseFloat(v.grossprofit) || 0,
        grossprofitpercentage: parseFloat(v.grossprofitpercentage) || 0,
        laborsale: parseFloat(v['laborsale']) || 0,
        laborsoldhours: parseFloat(v['laborsoldhours']) || 0,
        lbropcode: v.lbropcode,
        lbropcodedesc: v.lbropcodedesc,
        monthYear: v.monthYear,
        opcategory: v.opcategory,
        totallaborcost: parseFloat(v.totallaborcost) || 0,
        totallaborsale: parseFloat(v.totallaborsale) || 0,
        partstotalsale: parseFloat(v.partstotalsale) || 0,
        partstotalcost: parseFloat(v.partstotalcost) || 0,
        partssale: parseFloat(v.partssale) || 0,
        totallaborsoldhours: parseFloat(v.totallaborsoldhours) || 0,
        workmix: parseFloat(v.workmix || 0),
        markup: v.markup
      });
      // return res;
    });
    return intArray;
  }

  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'Opcode Summary',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Opcode Summary' },
            mergeAcross: 3
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  resetRowData = () => {
    this.setState({ resetReport: this.props.resetReport });

    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    window.sortStates = {};
    window.filterStates = {};
    window.sortStatesPrts = {};
    window.filterStatesPrts = {};
    this.props.handleResetReport();
    if (this.props.parent == 'opcodelevel') {
      this.setState({ parent: '' });
      this.setState({ selectedOpcode: '' });
    }
    if (
      this.props.isFrom == 'opcodes' ||
      this.props.isFrom == 'searchByRo' ||
      (this.props.isFrom == 'opcodelevel' && this.props.parent == 'opcodes') ||
      this.props.isFrom == 'comparisonchart'
    ) {
      this.setState({ selectedOpcodeAll: 'All' });
      this.setState({ isLoading: true });
      this.getAgGridData(this.state.queryMonth, '');
    }

    this.handleDurationChange('3months');

    this.setState({ disabled: false });
    this.gridApi.collapseAll();
  };
  onFirstDataRendered = params => {
    if (
      this.props.isFrom == 'opcodelevel' ||
      (this.props.isFrom == 'opcodes' &&
        (this.props.parent == 'comparisonchart' ||
          this.props.parent == 'workMixVolume' ||
          this.props.parent == 'workmix'))
    ) {
      setTimeout(() => {
        params.api.forEachNode((node, b) => {
          if (node.group == true && node.rowIndex == this.props.isExpanded) {
            node.setExpanded(true);
          }
        });
      }, 1000);
    }
    if (
      (this.props.parent == 'opcodelevel' ||
        this.props.parent == 'workmix' ||
        this.props.parent == 'searchByRo' ||
        this.props.parent == 'opcodes') &&
      this.props.history.location.state
    ) {
      setTimeout(() => {
        params.api.forEachNode((node, b) => {
          if (
            node.group == true &&
            node.rowIndex == this.props.history.location.state.isExpanded
          ) {
            node.setExpanded(true);
          }
        });
      }, 1000);
    }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    this.setState({ firstLoad: true });
    this.gridApi = params.api;
    var expanded;

    console.log(
      'opcode',
      this.props,
      this.state,
      this.props.isFrom,
      window.filterStates
    );
    if (
      this.props.isFrom == 'opcodelevel' &&
      this.props.selectedOpcodeAll === 'All'
    ) {
      this.setState({ selectedOpcodeAll: 'All' });

      this.getAgGridData(this.state.queryMonth, '');
      //this.handleDurationChange('lastyear');
    }
    if (
      (this.props.isFrom == 'opcodelevel' ||
        this.props.parent == 'opcodelevel' ||
        this.props.parent == 'workmix' ||
        this.props.parent == 'searchByRo') &&
      (window.sortStates || window.filterStates)
    ) {
      if (this.props.isLabor) {
        this.gridApi.setSortModel(window.sortStates);
        this.gridApi.setFilterModel(window.filterStates);
        if (window.colStates) {
          this.state.gridColumnApi.setColumnState(window.colStates);
        }
      } else {
        this.gridApi.setSortModel(window.sortStatesPrts);
        this.gridApi.setFilterModel(window.filterStatesPrts);
        if (window.colStatesPrts) {
          this.state.gridColumnApi.setColumnState(window.colStatesPrts);
        }
      }
    } else if (
      this.props.isFrom != 'opcodes' &&
      this.props.isFrom != 'workMixVolume' &&
      this.props.isFrom != 'workmix' &&
      this.props.isFrom != 'comparisonchart' &&
      this.props.isFrom != 'searchByRo'
    ) {
      this.setState({ opcode: '' });

      this.gridApi.setSortModel(null);
      this.gridApi.setFilterModel(null);
      window.filterStates = {};
      window.sortStates = {};
      window.filterStatesPrts = {};
      window.sortStatesPrts = {};
    }
    if (this.props.isFrom == 'comparisonchart') {
      this.gridApi.setSortModel(null);
      this.gridApi.setFilterModel(null);
      window.filterStates = {};
      window.sortStates = {};
      window.filterStatesPrts = {};
      window.sortStatesPrts = {};
    }
    if (this.props.isFrom == 'searchByRo' && window.filterStates) {
      window.prevFilter = window.filterStates;
      window.prevSort = window.sortStates;
      window.prevFilterParts = window.filterStatesPrts;
      window.prevSortParts = window.sortStatesPrts;
      this.gridApi.setSortModel(null);
      this.gridApi.setFilterModel(null);
      window.filterStates = {};
      window.sortStates = {};
      window.filterStatesPrts = {};
      window.sortStatesPrts = {};
    }
    if (this.props.isFrom == 'opcodelevel' && window.prevFilter) {
      window.filterStates = window.prevFilter;
      window.sortStates = window.prevSort;
      window.filterStatesPrts = window.prevFilterParts;
      window.sortStatesPrts = window.prevSortParts;
    }
    //*click from opcode edit and return back
    if (
      this.props.parent == 'opcodelevel' ||
      (this.props.isFrom == 'opcodelevel' &&
        this.props.parent == 'opcodes' &&
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.pageType != 'searchByRo' &&
        this.props.history.location.state.pageType != 'opcodes')
    ) {
      this.setState({ opcode: '' });
    }
    //this.getAgGridData(this.state.queryMonth, this.state.opcode);
  };
  getAgGridData(queryMonth, opCode) {
    queryMonth =
      opCode &&
      this.state.isFrom != 'workMixVolume' &&
      this.props.parent != 'workMixVolume' &&
      this.props.isFrom != 'opcodelevel' &&
      this.props.isFrom != 'opcodes' &&
      this.props.isFrom != 'workmix' &&
      this.props.isFrom != 'searchByRo'
        ? queryMonth
        : '';
    // : getLast13Months();
    // if (
    //   this.props.isFrom == 'opcodelevel' &&
    //   this.props.parent == 'searchByRo'
    // ) {
    //   console.log('opCode22', this.state.queryMonth);
    //   queryMonth = this.state.queryMonth;
    // }
    let filteredResult = [];
    let duration = this.state.summaryTab;
    console.log(
      'opCode22',
      duration,
      this.state.selectedButton,
      this.props.isFrom,
      this.props.parent
    );
    if (duration == '') {
      let monthFlag = this.state.selectedButton.split('-')[1];
      if (monthFlag) {
        duration = monthFlag == '3months' ? 3 : monthFlag == '6months' ? 6 : 12;
      }
    }

    if (
      this.props.parent == 'opcodes' &&
      this.props.opcode != '' &&
      (this.props.selectedButton == '3months' ||
        this.props.selectedButton == undefined)
    ) {
      duration = 0;
    }

    if (
      this.props.isFrom == 'comparisonchart' ||
      this.props.parent == 'comparisonchart'
    ) {
      if (this.props.parent == 'comparisonchart') {
        queryMonth = this.props.month;
      }

      duration = 0;
      var chkSelectedMonth = this.checkselectedMonthRange(queryMonth);

      this.handleDurationChange(chkSelectedMonth);
    }
    if (duration == '') {
      duration = 0;
    }
    if (
      (this.props.isFrom == 'opcodes' ||
        this.props.isFrom == 'searchByRo' ||
        this.props.isFrom == 'comparisonchart') &&
      this.state.selectedOpcodeAll == 'All'
    ) {
      duration = 12;
    }
    if (
      this.props.isFrom == 'searchByRo' ||
      this.props.parent == 'searchByRo'
    ) {
      duration = 0;
    }

    if (this.state.storeChanged) {
      duration = 3;
    }

    if (this.props.isLabor == true) {
      getDrillDownDataForLabor(queryMonth, opCode, duration, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessDbdLaborWorkmixGetWorkmixOpcodesVolumeGroupingMaster
            .statelessDbdLaborWorkmixOpcodeDetailsVolumeGroupingMasters
        ) {
          let data =
            result.data
              .statelessDbdLaborWorkmixGetWorkmixOpcodesVolumeGroupingMaster
              .statelessDbdLaborWorkmixOpcodeDetailsVolumeGroupingMasters;

          this.handleOpcodeSummaryForNavigation(
            data,
            queryMonth,
            opCode,
            'Labor'
          );
        }
        if (window.filterStates != undefined) {
          this.filterByValues();
        }
        this.filterByPayTypeGroup();
      });
    } else {
      getDrillDownDataForParts(queryMonth, opCode, duration, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessDbdPartsWorkmixGetWorkmixOpcodesVolumeGroupingMaster
            .statelessDbdPartsWorkmixWorkmixOpcodeDetailsVolumeGroupingMasters
        ) {
          let data =
            result.data
              .statelessDbdPartsWorkmixGetWorkmixOpcodesVolumeGroupingMaster
              .statelessDbdPartsWorkmixWorkmixOpcodeDetailsVolumeGroupingMasters;
          this.handleOpcodeSummaryForNavigation(
            data,
            queryMonth,
            opCode,
            'Parts'
          );
        }
        if (window.filterStates != undefined) {
          this.filterByValues();
        }
        this.filterByPayTypeGroup();
      });
    }
  }
  handleOpcodeSummaryForNavigation = (data, queryMonth, opCode, type) => {
    let filteredResult = [];
    if (
      (((this.props.isFrom != 'comparisonchart' &&
        this.state.isFrom != 'opcodes' &&
        this.props.parent != 'opcodelevel' &&
        this.props.isFrom != 'workmix' &&
        this.props.parent != 'workmix' &&
        this.props.isFrom != 'workMixVolume' &&
        this.props.parent != 'workMixVolume' &&
        this.props.isFrom != 'searchByRo' &&
        this.props.parent != 'searchByRo' &&
        this.props.parent != 'comparisonchart') ||
        (this.state.isFrom == 'opcodelevel' &&
          this.props.parent == 'opcodes')) &&
        this.state.opcode != '') ||
      // (this.props.parent == 'opcodes' && this.props.opcode != '') ||
      (this.props.isFrom == 'opcodes' &&
        this.props.opcode != '' &&
        (this.props.parent == '' || this.props.parent == 'opcodes')) ||
      (this.props.parent == 'opcodes' &&
        this.props.opcode != '' &&
        this.props.selectedButton == '3months') ||
      (this.state.isFrom == 'opcodes' && this.state.parent == 'opcodes')
    ) {
      filteredResult = data;
    } else {
      alert(2);
      filteredResult = data.filter(item => item.paytypegroup == 'C');
      //To be remove after  optimization with monthyear param
    }
    filteredResult.map(item => {
      if (item.opsubcategory && item.opsubcategory != 'N/A') {
        return (item.opsubcategory = '');
      }
    });
    filteredResult.sort((a, b) => {
      const dateA = new Date(a.monthYear);
      const dateB = new Date(b.monthYear);
      return dateB - dateA;
    });
    var chkMonth = this.checkDateRange(data);
    this.setState({ rowData: filteredResult });
    this.setState({
      rowDataNonfiltered: filteredResult
    });
    if (
      (this.props.isFrom != 'comparisonchart' &&
        this.props.isFrom != 'opcodes' &&
        this.props.parent != 'opcodes' &&
        this.props.parent != 'comparisonchart') ||
      this.state.storeChanged == true
    ) {
      this.getDrillDownDataForLabor12Months(queryMonth, opCode, type);
      // this.handleDurationChange('3months');
    }

    var chkMonth = this.checkDateRange(filteredResult);
    console.log('arya', chkMonth, this.props, this.state);
    if (
      (this.props.isFrom != 'opcodes' &&
        this.props.parent != 'opcodes' &&
        this.props.parent != 'opcodelevel' &&
        this.props.isFrom != 'searchByRo' &&
        this.props.parent != 'searchByRo' &&
        chkMonth) ||
      (this.props.isFrom == 'opcodes' &&
        this.props.parent == 'workMixVolume') ||
      (this.state.selectedOpcodeAll == 'All' &&
        (this.props.isFrom == 'searchByRo' ||
          this.props.parent == 'searchByRo')) ||
      (this.props.isFrom == 'opcodes' &&
        this.props.parent == 'comparisonchart') ||
      (this.props.isFrom == 'opcodes' && this.props.parent == 'workmix') ||
      this.props.isFrom == 'workmix' ||
      (this.props.isFrom == 'workMixVolume' &&
        (this.props.parent == '' || this.props.parent == undefined)) ||
      (this.props.isFrom == 'opcodes' &&
        this.props.parent == '' &&
        this.state.storeChanged == true)
    ) {
      alert(3);
      this.setState({ storeChanged: false });
      this.handleDurationChange(this.state.selectedButton.split('-')[1]);
    } else {
      this.disablebuttons();
      if (!chkMonth) {
        if (this.state.isFrom != 'opcodes') {
          this.setState({ selectedButton: 'lastyear' });
        }

        console.log(
          '12344',
          this.state.selectedButton,
          this.props.selectedButton,
          this.state.selectedOpcodeAll
        );
        if (this.state.selectedOpcodeAll != 'All') {
          alert(1);
          this.setState({ disabled: true });
          this.disablebuttons();
        }
      }
      this.getTotalsForDisplay(filteredResult);
    }

    if (window.filterStates != undefined) {
      this.filterByValues();
    }

    if (
      ((this.props.isFrom == 'opcodes' ||
        this.props.isFrom == 'searchByRo' ||
        this.props.isFrom == 'comparisonchart') &&
        this.state.selectedOpcodeAll == 'All') ||
      (filteredResult.length < 1 &&
        this.state.selectedButton.split('-')[1] == '3months' &&
        this.state.selectedOpcodeAll != 'All')
    ) {
      this.handleDurationChange('lastyear');
      //this.handleDurationChange(this.state.selectedButton.split('-')[1]);
    }
    if (
      chkMonth &&
      this.props.selectedButton != undefined &&
      this.props.parent != 'searchByRo'
    ) {
      this.handleDurationChange(this.props.selectedButton.split('-')[1]);
    }
    if (
      this.props.history.location.state.selectedButton &&
      this.state.parent == 'opcodelevel'
    ) {
      this.handleDurationChange(
        this.props.history.location.state.selectedButton.split('-')[1]
      );
    }
  };
  getDrillDownDataForLabor12Months = (queryMonth, opCode, type) => {
    let filteredResult = [];
    if (type == 'Labor') {
      getDrillDownDataForLabor(queryMonth, opCode, 12, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessDbdLaborWorkmixGetWorkmixOpcodesVolumeGroupingMaster
            .statelessDbdLaborWorkmixOpcodeDetailsVolumeGroupingMasters
        ) {
          let data =
            result.data
              .statelessDbdLaborWorkmixGetWorkmixOpcodesVolumeGroupingMaster
              .statelessDbdLaborWorkmixOpcodeDetailsVolumeGroupingMasters;

          this.handleLaborAndParts12MonthData(data);
        }
      });
    } else {
      getDrillDownDataForParts(queryMonth, opCode, 12, result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessDbdPartsWorkmixGetWorkmixOpcodesVolumeGroupingMaster
            .statelessDbdPartsWorkmixWorkmixOpcodeDetailsVolumeGroupingMasters
        ) {
          let data =
            result.data
              .statelessDbdPartsWorkmixGetWorkmixOpcodesVolumeGroupingMaster
              .statelessDbdPartsWorkmixWorkmixOpcodeDetailsVolumeGroupingMasters;
          this.handleLaborAndParts12MonthData(data);
        }
      });
    }
  };
  handleLaborAndParts12MonthData = data => {
    let filteredResult = [];
    if (
      ((this.props.isFrom != 'comparisonchart' &&
        this.state.isFrom != 'opcodes' &&
        this.props.parent != 'opcodelevel' &&
        this.props.isFrom != 'workmix' &&
        this.props.parent != 'workmix' &&
        this.props.isFrom != 'workMixVolume' &&
        this.props.parent != 'workMixVolume' &&
        this.props.isFrom != 'searchByRo' &&
        this.props.parent != 'comparisonchart') ||
        (this.state.isFrom == 'opcodelevel' &&
          this.props.parent == 'opcodes') ||
        (this.state.isFrom == 'opcodes' && this.state.parent == 'opcodes')) &&
      this.state.opcode != ''
    ) {
      filteredResult = data;
    } else {
      filteredResult = data.filter(item => item.paytypegroup == 'C');
      //To be remove after  optimization with monthyear param
    }
    // console.log(
    //   'rowDataNonfiltered',
    //   filteredResult,
    //   this.state.selectedButton,
    //   this.state.firstLoad,
    //   duration
    // );
    this.setState({
      rowDataNonfiltered: filteredResult,
      rowDataNonfilteredAll: filteredResult
    });
    let duration = '3months';
    if (this.state.selectedButton.split('-')[1] == 3) {
      duration = '3months';
    } else {
      if (this.state.selectedButton == '3months') {
        duration = '3months';
      } else {
        duration = this.state.selectedButton.split('-')[1];
      }
    }
    if (this.state.firstLoad != true || duration != '3months') {
      this.handleDurationChange(duration);
    }

    this.setState({ firstLoad: false });
  };
  disablebuttons = () => {
    if (document.getElementById(this.state.selectedButton)) {
      document.getElementById(this.state.selectedButton).style.background =
        '#FFFFFF';
      document.getElementById(this.state.selectedButton).style.color =
        '#757575';
      document.getElementById(this.state.selectedButton).style.border =
        '1px solid rgba(0, 0, 0, 0.23)';
    }
  };
  checkDateRange = result => {
    var monthYear = [];
    var uniqueDates = lodash
      .chain(result)
      .map(function(item) {
        return item.monthYear;
      })
      .uniq()
      .value();

    const currentYear = new Date().getFullYear();
    var previousYear = currentYear - 1;
    var beforePreviousYear =
      this.state.monthYearsArr.length > 0 &&
      this.state.monthYearsArr.pop().split('-')[0];
    var CurrentYearShort = currentYear.toString().slice(-2);
    var lastYearShort = previousYear.toString().slice(-2);
    var prevYearShort = beforePreviousYear.toString().slice(-2);
    if (this.state.realm == 'demoag') {
      this.state.monthYearsArr.some((r, i) => {
        if (
          r.includes(lastYearShort) ||
          r.includes(CurrentYearShort) ||
          r.includes(prevYearShort)
        ) {
          monthYear.push(r);
        }
      });
    } else {
      this.state.monthYearsArr.some((r, i) => {
        if (r.includes(lastYearShort) || r.includes(CurrentYearShort)) {
          monthYear.push(r);
        }
      });
    }
    const found = uniqueDates.some(r => monthYear.indexOf(r) >= 0);

    return found;
  };
  checkselectedMonthRange = monthyr => {
    let type;
    var monthyear = monthyr.split('-');
    const withinLast3Months = this.checkWithinLastMonths(
      monthyear[1],
      monthyear[0],
      3
    );
    const withinLast13Months = this.checkWithinLastMonths(
      monthyear[1],
      monthyear[0],
      13
    );
    if (withinLast3Months) {
      type = '3months';
    } else if (withinLast13Months) {
      type = 'lastyear';
    }
    return type;
  };
  getSelectedDuration = () => {
    if (document.getElementById(this.props.selectedButton)) {
      document.getElementById(this.props.selectedButton).style.background =
        'rgb(221, 234, 244)';
      document.getElementById(this.props.selectedButton).style.color =
        'rgb(0, 61, 107)';
    }
    this.setState({ selectedButton: this.props.selectedButton });
  };
  filterByValues = () => {
    var countryFilterComponent = '';
    var filterArr = this.props.isLabor
      ? Object.entries(window.filterStates)
      : Object.entries(window.filterStatesPrts);
    console.log(' filterArr', filterArr);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  autoSizeColumns = params => {
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map(col => col.getColId());

    params.columnApi.autoSizeColumns(colIds);
  };
  handleOpcodeChange = event => {
    let opcode = event.target.value;
    this.setState({ selectedOpcodeAll: opcode });
    if (opcode == 'All') {
      opcode = '';
    }
    this.setState({ isLoading: true });
    setTimeout(
      function() {
        this.handleDurationChange('3months');
        this.setState({ disabled: false });
        this.getAgGridData(this.state.queryMonth, opcode);
      }.bind(this),
      10
    );
  };
  renderBackButton = () => {
    {
      let data = {};
      if (this.state.prevMenu == '/MyFavorites') {
        this.props.history.push({
          pathname: '/MyFavorites'
        });
      } else {
        if (
          this.props.isFrom == 'comparisonchart' ||
          this.props.parent == 'comparisonchart'
        ) {
          data = {
            tabSelection: 'two',
            isFrom: 'drillDown-workmix',
            comparisonMonth1: this.props.comparisonMonth2,
            comparisonMonth2: this.props.comparisonMonth1
          };
        } else if (
          this.props.isFrom == 'workmix' ||
          this.props.parent == 'workmix'
        ) {
          data = {
            tabSelection: 'three',
            isFrom: 'drillDown-workmix',
            opcode: '',
            parent: '',
            reportType: this.state.reportType,
            selectedReportType: this.props.selectedReportType,
            isExpanded: this.props.isExpanded
          };
        }

        /*else if (this.state.type == 'reports'|| this.props.parent == 'reports') {
          data = {
            tabSelection: 'three',
            isFrom: 'drillDown'
          };
        }*/
        this.props.parentCallback(data);

        if (
          this.state.isFrom == 'workMixVolume' ||
          this.props.parent == 'workMixVolume'
        ) {
          this.props.history.push({
            pathname: this.props.isLabor
              ? '/WorkMixVolume'
              : 'WorkMixVolumeParts',
            parentCallback: this.props.parentCallback,
            state: {
              category: this.props.isLabor ? 'labor' : 'parts',
              reportType: this.state.reportType,
              workmixParent: this.state.workmixParent,
              workmixTab: this.state.workmixTab,
              selectedReportType:
                this.props.history.location && this.props.history.location.state
                  ? this.props.history.location.state.selectedReportType
                  : this.props.selectedReportType
                  ? this.props.selectedReportType
                  : '',
              opCategory: this.state.opcategory,
              prevPath: this.state.prevPath
            }
          });
        }
        if (
          this.props.isFrom == 'searchByRo' ||
          this.props.parent == 'searchByRo' ||
          this.props.parent == 'Laboritemization' ||
          this.props.parent == 'Partsitemization' ||
          (this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.isFrom &&
            this.props.history.location.state.isFrom == 'searchByRo')
        ) {
          this.props.history.push({
            pathname: '/SearchByRO',
            state: {
              ronumber: this.props.roNumber,
              pageType: this.props.page,
              userHistory:
                this.props.selectedToggle &&
                this.props.selectedToggle.userHistory
                  ? this.props.selectedToggle.userHistory
                  : this.props.selectedToggle,
              previousToggle: this.props.previousToggle,
              payType: this.props.payTypeRo,
              parent: this.props.parent,
              PrevPayType: this.props.PrevPayType,
              PrevGridType: this.props.PrevGridType,
              showAllJobs: this.props.showAllJobs,
              itemizationTab: this.props.itemizationTab
                ? this.props.itemizationTab
                : '',
              itemizationData: this.props.itemizationData
                ? this.props.itemizationData
                : ''
            }
          });
        }
        if (
          (this.props.isFrom == 'opcodes' &&
            this.props.parent != 'workMixVolume' &&
            this.props.parent != 'searchByRo' &&
            this.props.parent != 'workmix' &&
            this.props.parent != 'comparisonchart') ||
          this.props.parent == 'opcodes'
        ) {
          this.props.history.push({
            pathname: '/OPcodes',
            state: {
              opcode: this.state.opcode,
              parent: 'opcodes',
              itemizationData: this.props.itemizationData
                ? this.props.itemizationData
                : '',
              itemizationTab: this.props.itemizationTab
                ? this.props.itemizationTab
                : '',
              isLabor: this.props.isLabor ? this.props.isLabor : ''
            }
          });
        }
      }
    }
  };
  filterByPayTypeGroup = () => {
    var payTypeFilterComponent = this.state.rawGridApi.getFilterInstance(
      'paytypegroup'
    )
      ? this.state.rawGridApi.getFilterInstance('paytypegroup')
      : this.state.rawGridApi.getFilterInstance('lbrPaytype')
      ? this.state.rawGridApi.getFilterInstance('lbrPaytype')
      : this.state.rawGridApi.getFilterInstance('partspaytypegroup');

    if (
      this.state.chartId == 1175 ||
      this.state.chartId == 968 ||
      this.state.chartId == 1007 ||
      this.state.chartId == 1074 ||
      this.state.chartId == 1134 ||
      this.state.chartId == 1139 ||
      this.state.chartId == 1001 ||
      this.state.chartId == 1028 ||
      this.state.chartId == 1144 ||
      this.state.chartId == 1319 ||
      (this.state.drillDown == 38 && this.state.category == 5) ||
      (this.state.drillDown == 39 && this.state.category == 5) ||
      this.state.chartId == 1327 ||
      this.state.chartId == 1225
    ) {
      this.setState({
        selectedpayTypeFilter1: '',
        selectedpayTypeFilter2: '',
        selectedpayTypeFilter3: ''
      });
    }
    let isFrom =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.isFrom
        ? this.props.history.location.state.isFrom
        : '';
    if (
      isFrom == 'searchByRo' &&
      this.state.chartName.indexOf('Warranty') > -1
    ) {
      this.setState({ selectedpayTypeFilter1: 'W' });
    } else if (
      isFrom == 'searchByRo' &&
      this.state.chartName.indexOf('Internal') > -1
    ) {
      this.setState({ selectedpayTypeFilter1: 'I' });
    } else if (
      isFrom == 'searchByRo' &&
      this.state.chartName.indexOf('Maintenance Plan') > -1
    ) {
      this.setState({ selectedpayTypeFilter1: 'M' });
    } else if (
      isFrom == 'searchByRo' &&
      this.state.chartName.indexOf('Extended Service Contract') > -1
    ) {
      this.setState({ selectedpayTypeFilter1: 'E' });
    } else if (
      isFrom == 'searchByRo' &&
      this.state.chartName.indexOf('Factory Service Contract') > -1
    ) {
      this.setState({ selectedpayTypeFilter1: 'F' });
    }
    if (payTypeFilterComponent) {
      payTypeFilterComponent.setModel({
        condition1: {
          type: 'startsWith',
          filter: this.state.selectedpayTypeFilter1
        }
      });
      if (
        this.state.chartName.indexOf('Combined') == -1 ||
        this.state.chartId == 1069 ||
        this.state.chartId == 1068 ||
        this.state.chartId == 1072
      ) {
        payTypeFilterComponent.setModel({
          values: [this.state.selectedpayTypeFilter1]
        });
        this.gridApi.onFilterChanged();
      }
    }
    this.state.rawGridApi.onFilterChanged();
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    this.getTotalsForDisplay(rowData);
  };
  getTotalsForDisplay = data => {
    var rowData = data ? data : this.state.rowData;
    var soldHours = 0;
    var lbrSale = 0;
    var lbrCost = 0;
    var prtsSale = 0;
    var prtsCost = 0;
    var roCount = 0;
    var lbrGP = 0;
    var prtsGP = 0;
    var lbrGPPerc = 0;
    var prtsGPPerc = 0;
    var ronumberArr = [];
    var jobCountSum = 0;
    var roCountSum = 0;
    if (rowData.length > 0) {
      roCount = rowData.map(item => {
        if (item) {
          ronumberArr.push(item.ronumber);
        }
      });

      rowData.map(item => {
        if (item) {
          soldHours +=
            item.laborsoldhours != null ? Number(item.laborsoldhours) : 0;
          lbrSale += Number(item.lbrsale);
          lbrCost += Number(item.lbrcost);
          prtsSale +=
            item.partssale != null && item.partssale != undefined
              ? Number(item.partssale)
              : 0;
          prtsCost += Number(item.partscost);
          jobCountSum += Number(item.jobcount);
          roCountSum += Number(item.rocount);
        }
      });
      lbrGP = lbrSale - lbrCost;
      prtsGP = prtsSale - prtsCost;
      lbrGPPerc = (lbrGP / lbrSale) * 100;
      prtsGPPerc = (prtsGP / prtsSale) * 100;
    }
    this.setState({
      jobCount: jobCountSum,
      roCount: roCountSum,
      soldHours: soldHours,
      lbrSale: lbrSale,
      lbrCost: lbrCost,
      prtsSale: prtsSale,
      prtsCost: prtsCost,
      lbrGP: lbrGP,
      prtsGP: prtsGP,
      lbrGPPerc: isNaN(lbrGPPerc) ? 0 : lbrGPPerc,
      prtsGPPerc: isNaN(prtsGPPerc) ? 0 : prtsGPPerc
    });
  };
  formatTotals = value => {
    if (value != null && value != 0) {
      return Math.sign(value) > -1
        ? '$' +
            parseFloat(value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueGPPerc = value => {
    if (value != null && value != 0) {
      return (
        parseFloat(value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0';
    }
  };
  getOpcodeDetailedViewMonthYear() {
    getOpcodeDetailedViewMonthYears(result => {
      if (result) {
        this.setState({ monthYearsArr: result });

        if (this.props.selectedOpcodeAll != 'All') {
          this.getAgGridData(this.state.queryMonth, this.state.opcode);
        }
      }
    });
  }

  handleDurationChange = (type, val) => {
    var monthYear = [];
    let resultSet = [];
    var gridData = this.state.rowDataNonfiltered;

    this.setState({ monthYearsArr: getLast13Months() });
    if (this.state.gridApi) {
      this.setState({ expanded: 0 });
    }
    if (val) {
      this.setState({ firstLoad: false });
    }
    console.log('gridData', type, val, this.props.isFrom);
    if (
      val &&
      this.props.isFrom != 'comparisonchart' &&
      this.props.parent != 'comparisonchart' &&
      this.props.isFrom != 'opcodes' &&
      this.props.parent != 'opcodes'
    ) {
      this.setState({ isLoading: true });
    }

    if (type == 'lastyear') {
      const last13 = this.state.monthYearsArr.slice(-13);
      this.setState({ duration: 12 });
      resultSet = gridData.filter(function(e) {
        if (last13.includes(e.monthYear)) return e;
      });
    }
    if (type == '6months') {
      var lastsix = this.state.monthYearsArr.slice(-6);
      this.setState({ duration: 6 });
      resultSet = gridData.filter(function(e) {
        if (lastsix.includes(e.monthYear)) return e;
      });
    }
    if (type == '3months') {
      var lastsix = this.state.monthYearsArr.slice(-3);

      this.setState({ duration: 3 });
      resultSet = gridData.filter(function(e) {
        if (lastsix.includes(e.monthYear)) return e;
      });
    }
    if (
      this.props.parent == 'opcodes' &&
      this.props.opcode != '' &&
      type == undefined
    ) {
      resultSet = this.state.rowDataNonfiltered;
    }
    resultSet.sort((a, b) => {
      const dateA = new Date(a.monthYear);
      const dateB = new Date(b.monthYear);
      return dateB - dateA;
    });

    this.setState({ rowData: resultSet });
    if (this.state.rowDataNonfilteredAll.length > 0) {
      this.setState({ isLoading: false });
    }
    this.getTotalsForDisplay(resultSet);
    if (document.getElementById(this.state.selectedButton)) {
      document.getElementById(this.state.selectedButton).style.background =
        '#FFFFFF';
      document.getElementById(this.state.selectedButton).style.color =
        '#757575';
    }

    if (document.getElementById('button-' + type)) {
      document.getElementById('button-' + type).style.background =
        'rgb(221, 234, 244)';
      document.getElementById('button-' + type).style.color = 'rgb(0, 61, 107)';
      document.getElementById('button-' + type).style.borderColor = '#003d6b';
    }
    this.setState({ selectedButton: 'button-' + type });
  };
  onRowGroupOpened = event => {
    var rowNodeIndex = event.node.rowIndex;
    // factor in child nodes so we can scroll to correct position
    var childCount = event.node.childrenAfterSort
      ? event.node.childrenAfterSort.length
      : 0;
    var newIndex = rowNodeIndex + 10;
    this.gridApi.ensureIndexVisible(newIndex);
    this.setState({ expanded: rowNodeIndex });
  };
  checkWithinLastMonths = (month, year, numMonths) => {
    const currentDate = new Date();
    const targetDate = new Date(year, month - 1);

    const monthDiff =
      (currentDate.getFullYear() - targetDate.getFullYear()) * 12 +
      currentDate.getMonth() -
      targetDate.getMonth();

    return monthDiff < numMonths;
  };
  onRowClicked = params => {
    // Check if the clicked row is a group row
    if (params.node.group) {
      // Toggle the expansion state of the group row
      if (params.node.expanded) {
        params.node.setExpanded(false);
      } else {
        params.node.setExpanded(true);
      }
    }
  };
  render() {
    const { classes } = this.props;
    if (this.props.exportReport) {
      this.onBtExport();
      this.props.handleExportReport();
    }
    {
    }

    return (
      <div>
        {/* {this.state.opcode!='' && ( */}
        {(this.state.opcode == '' &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.isFrom &&
          this.props.history.location.state.isFrom == 'searchByRo') ||
        (this.state.opcode &&
          ((this.state.opcode != '' &&
            this.props.parent != 'opcodelevel' &&
            this.props.isFrom != 'opcodelevel') ||
            (this.state.selectedOpcode != '' &&
              this.props.isFrom !== 'opcodes' &&
              this.props.isFrom != 'opcodelevel' &&
              this.props.parent != 'opcodes') ||
            (this.props.isFrom == 'opcodelevel' &&
              this.props.parent == 'comparisonchart') ||
            (this.props.isFrom == 'opcodelevel' &&
              this.props.parent == 'searchByRo') ||
            (this.props.isFrom == 'opcodelevel' &&
              this.props.parent == 'workMixVolume') ||
            (this.props.isFrom == 'opcodelevel' &&
              this.props.parent == 'workmix') ||
            (this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.pageType &&
              this.props.history.location.state.pageType == 'opcodes'))) ? (
          <div>
            <Paper square style={{ marginLeft: 8, marginRight: 8 }}>
              <Toolbar>
                {this.props.isFrom == 'comparisonchart' ||
                this.props.isFrom == 'workmix' ||
                this.props.parent == 'comparisonchart' ||
                this.props.parent == 'workmix' ||
                this.state.isFrom == 'workMixVolume' ||
                this.props.parent == 'workMixVolume' ||
                (this.props.selectedReportType != '' &&
                  this.props.isFrom !== 'opcodes') ? (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.renderBackButton}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.renderBackButton}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                )}
                <Grid container justify="flex-start" style={{ padding: '5px' }}>
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={classes.formControl}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                    >
                      Opcodes
                    </InputLabel>
                    <Select
                      variant="outlined"
                      label="Group By"
                      name="group-by-type"
                      value={
                        this.state.selectedOpcodeAll
                          ? this.state.selectedOpcodeAll
                          : this.state.selectedOpcode
                      }
                      onChange={this.handleOpcodeChange}
                    >
                      <MenuItem value={this.state.selectedOpcode}>
                        {this.state.selectedOpcode}
                      </MenuItem>
                      <MenuItem value="All"> All Opcodes </MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Toolbar>
            </Paper>
          </div>
        ) : null}
        <Paper
          square
          style={{
            marginLeft: 8,
            marginRight: 8,
            paddingTop: 3,
            display: 'flex'
          }}
        >
          <Grid
            xs={12}
            style={{ display: 'flex', padding: 5, alignItems: 'baseline' }}
          >
            {/* {!this.state.isLoading && ( */}
            <Grid className={classes.buttonlast}>
              <Button
                className={
                  this.state.checked == false
                    ? classes.reportButtonSelect
                    : classes.reportButton
                }
                id="button-3months"
                variant="outlined"
                onClick={() => this.handleDurationChange('3months', 3)}
                disabled={this.state.disabled}
              >
                3 Mths
              </Button>
              <Button
                className={
                  this.state.checked == false
                    ? classes.reportButtonSelect
                    : classes.reportButton
                }
                id="button-6months"
                variant="outlined"
                onClick={() => this.handleDurationChange('6months', 6)}
                disabled={this.state.disabled}
              >
                6 Mths
              </Button>{' '}
              <Button
                className={
                  this.state.checked == false
                    ? classes.reportButtonSelect
                    : classes.reportButton
                }
                id="button-lastyear"
                variant="outlined"
                onClick={() => this.handleDurationChange('lastyear', 12)}
                disabled={this.state.disabled}
              >
                Current +12
              </Button>
            </Grid>
            {/* )} */}
            <Grid
              className={classes.containerTotal}
              title="Page Summary"
              style={{ width: '75%', marginLeft: '8px' }}
            >
              <span class="title1">Page Summary</span>
              <Button className={classes.summaryBlock}>
                <Typography
                  variant="h5"
                  align="left"
                  className={classes.summaryBlockText}
                >
                  RO Count :
                  <span className={classes.spancls}>
                    {' '}
                    {this.state.roCount
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  </span>
                </Typography>
              </Button>
              <Button className={classes.summaryBlock}>
                <Typography
                  variant="h5"
                  align="left"
                  className={classes.summaryBlockText}
                >
                  Job Count :
                  <span className={classes.spancls}>
                    {' '}
                    {this.state.jobCount
                      .toString()
                      .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  </span>
                </Typography>
              </Button>
              {this.props.isLabor == true ? (
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Hours Sold :
                    <span className={classes.spancls}>
                      {' '}
                      {this.state.soldHours
                        .toFixed(2)
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  </Typography>
                </Button>
              ) : (
                ''
              )}
              <Button className={classes.summaryBlock}>
                {this.props.isLabor == true ? (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor Sale :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatTotals(this.state.lbrSale)}
                    </span>
                  </Typography>
                ) : (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts Sale :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatTotals(this.state.prtsSale)}
                    </span>
                  </Typography>
                )}
              </Button>
              <Button className={classes.summaryBlock}>
                {this.props.isLabor == true ? (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor Cost :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatTotals(this.state.lbrCost)}
                    </span>
                  </Typography>
                ) : (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts Cost :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatTotals(this.state.prtsCost)}
                    </span>
                  </Typography>
                )}
              </Button>
              <Button className={classes.summaryBlock}>
                {this.props.isLabor == true ? (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor GP :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatTotals(this.state.lbrGP)}
                    </span>
                  </Typography>
                ) : (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts GP :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatTotals(this.state.prtsGP)}
                    </span>
                  </Typography>
                )}
              </Button>
              <Button className={classes.summaryBlock}>
                {this.props.isLabor == true ? (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor GP% :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatCellValueGPPerc(this.state.lbrGPPerc)}
                    </span>
                  </Typography>
                ) : (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts GP% :
                    <span className={classes.spancls}>
                      {' '}
                      {this.formatCellValueGPPerc(this.state.prtsGPPerc)}
                    </span>
                  </Typography>
                )}
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {this.state.isLoading && (
          <div className={classes.loader}>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id="data-tab"
          className={clsx('ag-theme-balham', 'opcode-summary-table')}
          style={{
            height:
              this.state.opcode != ''
                ? window.innerHeight - 280 + 'px'
                : window.innerHeight - 250 + 'px',
            //  height: this.state.opcode!= '' ? '430px' : '500px',
            width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            // getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            // enableRangeSelection={true}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            onFilterChanged={this.onFilterChanged}
            excelStyles={this.state.excelStyles}
            headerHeight={this.state.headerHeight}
            tooltipShowDelay={0}
            sideBar={this.state.sideBar}
            floatingFilter={true}
            enableRangeSelection={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            onRowGroupOpened={this.onRowGroupOpened.bind(this)}
            groupDefaultExpanded={this.state.expanded}
            frameworkComponents={this.state.frameworkComponents}
            onFirstDataRendered={this.onFirstDataRendered}
            onRowClicked={this.onRowClicked}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    },
    '@media (min-width: 2560px)': {
      fontSize: 10,
      padding: 7
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      padding: 7
    },
    '@media (max-width: 1920px)': {
      fontSize: 12,
      padding: 10
    },
    '@media (max-width: 1440px)': {
      fontSize: 10,
      padding: 2
    }
  },
  summaryBlockText: {
    fontSize: 10,
    color: '#003d6b',
    fontWeight: 'bold'
  },
  containerTotal: {
    marginTop: '7px',
    border: '1px solid rgba(0, 0, 0, 0.23)',
    position: 'relative',
    padding: '6px 0px 13px',
    height: '40px',
    borderRadius: '4px'
    // width:'934px'
  },
  spancls: {
    color: '#7987a1',
    marginLeft: 3,
    fontSize: 10
  },
  reportButtonSelect: {
    border: '2px solid #7d97aa',
    //color: '#757575',
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 120
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 120
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 120
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 113
    },
    '@media (max-width: 1366px)': {
      fontSize: 10,
      width: 110
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)',
    textTransform: 'none',
    margin: '2px',
    marginTop: 6
  },
  reportButton: {
    height: 21,
    marginLeft: 4,
    marginTop: 6,
    textTransform: 'none',
    margin: '2px',
    color: '#757575',

    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 90,
      fontWeight: 'bold'
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 90,
      fontWeight: 'bold'
    },
    '@media (max-width: 1920px)': {
      fontSize: 10,
      width: 90,
      fontWeight: 'bold'
    },
    '@media (max-width: 1440px)': {
      fontSize: 10,
      width: 90,
      fontWeight: 'bold'
    },
    '@media (max-width: 1366px)': {
      fontSize: 10,
      width: 90,
      fontWeight: 'bold'
    }
  },
  buttonlast: {
    height: 33,
    textAlign: 'right',
    display: 'flex'
  }
});

export default withStyles(styles)(RawDataOpcodeGrid);
