import { AllModules } from '@ag-grid-enterprise/all-modules';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';

import 'src/grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Toolbar,
  Typography,
  Tooltip,
  Button
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  getDrillDownDataTechnicianSummary,
  getDrillDownDataForTechnician,
  getAllTechnicians,
  getTechnicianDrilldownTechHoursWeekly,
  getDrillDownDataForReports,
  getTechDrillDownAllRos
} from 'src/utils/hasuraServices';
import { getLast13Months } from 'src/utils/Utils';
import './Aggrid.css';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import moment from 'moment';
import { ReactSession } from 'react-client-session';
import { LeakRemoveTwoTone } from '@material-ui/icons';
var lodash = require('lodash');
class TechnicianSummary extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (this.props.resetReport != this.state.resetReport) {
      this.resetRawData();
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      console.log(
        'selectedStoreId=',
        localStorage.getItem('selectedStoreId'),
        '=',
        this.state.store,
        '==',
        ReactSession.get('selectedStoreId'),
        this.props,
        checkSt
      );
      if (checkSt == false) {
        this.setState({ selectedTechNo: 'All[All]' });
        this.setState({ techNo: '' });
        this.setState({ isLoading: true });
        this.setState({ queryMonth: getLast13Months() });

        //  this.getAgGridData(this.state.queryMonth, '');
        this.setState({ type: '' });
        //   this.getAgGridData(this.state.queryMonth, '');

        getDrillDownDataTechnicianSummary(getLast13Months(), '', result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHrs.nodes
          ) {
            let sortData = lodash.orderBy(
              result.data
                .statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHrs
                .nodes,
              'monthYear',
              'desc'
            );

            this.setState({
              rowData: sortData
            });
          }
          if (window.filterStatesTech != undefined) {
            this.filterByValues();
          }
        });
        this.resetRawData();
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    month: '2019-11'
  };
  // componentDidUpdate() {
  //   if (this.props.resetReport != this.state.resetReport) {
  //     this.resetRawData();
  //   }
  // }
  // componentDidUpdate(prevProps) {
  //   console.log("enter technician");
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       this.setState({ isLoading: true });
  //       console.log("stores=00==",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData(this.state.queryMonth, this.state.techNo)
  //     }
  //   }
  // }
  constructor(props) {
    super(props);
    var techNo = this.props.techNo ? this.props.techNo : '';
    console.log('isfromTech', this.props);
    techNo =
      this.props.type == '' ||
      this.props.type == undefined ||
      this.props.type == 'technicianlevel' ||
      (this.props.parent == '' &&
        this.props.type != 'techniciantrendingcharts' &&
        this.props.type != 'reports' &&
        this.props.type != 'techcomparison')
        ? ''
        : this.props.techName == 'All[All]' && this.props.techNo != 'All'
        ? 'All'
        : techNo.toString();
    var isFrom = this.props.isFrom ? this.props.isFrom : '';
    if (
      (this.props.isFrom == 'techcomparison' &&
        this.props.techName == this.props.techNo) ||
      (this.props.isFrom != 'techcomparison' &&
        (this.props.techName == '' ||
          (this.props.techName &&
            this.props.techName !== 'All[All]' &&
            this.props.techName.split('[')[1] &&
            this.props.techName
              .split('[')[1]
              .split(']')[0]
              .toString() != techNo))) ||
      (this.props.techNo &&
        this.props.techName !== 'All[All]' &&
        this.props.techNo != 'All' &&
        typeof this.props.techNo.split('[')[1] == 'undefined')
    ) {
      this.getTechName(techNo);
    }
    var month_year =
      this.props.type == '' ||
      (this.props.parent == '' &&
        this.props.type != 'techniciantrendingcharts' &&
        this.props.type != 'reports' &&
        this.props.type != 'techcomparison')
        ? ''
        : this.props.month_year;

    var weekStart =
      this.props.weekStart != undefined
        ? this.props.weekStart
        : this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.weekStart;

    this.state = {
      resetReport: this.props.resetReport,

      queryMonth:
        this.props.chartType == 1363 ||
        this.props.chartType == 1352 ||
        (this.props.chartType == undefined &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.chartId &&
          this.props.history.location.state.chartId == 1363)
          ? weekStart
          : month_year,
      techNo: techNo,
      isLoading: true,
      selectedTechNo:
        this.props.techName == null || this.props.techName === 'All[All]'
          ? 'All[All]'
          : this.props.techName,
      reportType: this.props.reportType,
      type: this.props.type,
      isFrom: isFrom,
      chartType: this.props.chartType,
      prevMenu: this.props.session.menuSelected,

      columnDefsSummary: [
        {
          headerName: 'Tech No',
          field: 'lbrtechname',
          chartDataType: 'category',
          //   width: 150,
          //   minWidth: 150,
          // flex: 1,
          minWidth: 120,
          // width: 120,
          onCellClicked: this.handleCellClicked,
          cellRenderer: function(params) {
            return `<a style="cursor:pointer" >${params.value}</a>`;
          },
          comparator: this.customComparator,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              border: ' 0px white'
            };
          },
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'lbrtechname'
        },
        {
          headerName: 'Month',
          field: 'monthYear',

          // flex: 1,
          // width: 60,
          minWidth: 95,
          chartDataType: 'category',
          valueGetter: function(params) {
            return params.data.monthYear;
          },
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Sold Hours',
          field: 'flatratehours',
          minWidth: 95,
          // width: 70,
          // minWidth: 80,
          // flex: 1,
          dataType: 'category',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          // suppressToolPanel: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        // {
        //   headerName: 'Tech Hours',
        //   field: 'techhours',
        //   width: 78,
        //   // minWidth: 78,
        //   //flex: 1,
        //   chartDataType: 'category',
        //   valueFormatter: this.formatCellValueWithOut$,
        //   cellStyle: this.cellStyles,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'Actual Tech Productivity ',
        //   field: 'techefficiency',
        //   width: 120,
        //   // minWidth: 110,
        //   //flex: 1,
        //   chartDataType: 'category',
        //   valueFormatter: this.formatCellValueTechHrs,
        //   cellStyle: this.cellStyles,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'Tech Productivity - Flat Rate Hours With Tech Hours',
        //   field: 'techefficiencynonzero',
        //   width: 133,
        //   minWidth: 133,
        //   flex: 1,
        //   chartDataType: 'series',
        //   valueFormatter: this.formatCellValueTechHrs,
        //   cellStyle: this.cellStyles,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        {
          headerName: 'Total Job Count',
          field: 'totaljobs',
          minWidth: 95,
          // width: 70,
          // minWidth: 73,
          // flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          // suppressToolPanel: true,

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        }
        // {
        //   headerName: 'Job Count - Flat Rate Hours With Tech Hours',
        //   field: 'jobcountflathrsandactualhrs',
        //   width: 132,
        //   minWidth: 132,
        //   flex: 1,
        //   chartDataType: 'series',
        //   cellStyle: this.cellStyles,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'Job Count - Flat Rate Hours No Tech Hours',
        //   field: 'jobcountflatratehrsandzeroactualhrs',
        //   width: 125,
        //   minWidth: 125,
        //   flex: 1,
        //   chartDataType: 'series',
        //   cellStyle: this.cellStyles,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'Job Count - Tech Hours No Flat Rate Hours',
        //   field: 'jobcountzeroflatratehrsandnonzeroactualhrs',
        //   width: 125,
        //   minWidth: 125,
        //   flex: 1,
        //   chartDataType: 'series',
        //   cellStyle: this.cellStyles,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'Job Count - No Flat Rate Hours No Tech Hours',
        //   field: 'jobcountzeroflatratehrsandzeroactualhrs',
        //   width: 125,
        //   minWidth: 125,
        //   flex: 1,
        //   chartDataType: 'category',
        //   cellStyle: this.cellStyles,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu:true,
        //   unSortIcon: true
        // }
      ],
      columnDefsDrilldown: [
        {
          headerName: 'Tech No',
          field: 'lbrtechname',
          chartDataType: 'category',
          // width: 160,
          minWidth: 160,

          //flex: 1,
          onCellClicked: this.handleCellClicked,
          cellRenderer: function(params) {
            return `<a style="cursor:pointer" >${params.value}</a>`;
          },
          comparator: this.customComparator,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              border: ' 0px white'
            };
          },
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'lbrtechname'
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          suppressColumnsToolPanel: false,
          // width: 85,
          minWidth: 85,

          // hide: false,
          chartDataType: 'category',
          valueGetter: function(params) {
            return params.data.monthYear;
          },

          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Sold Hours',
          field: 'flatratehours',
          // width: 80,
          // hide: this.props.chartId == 1265 ? false : true,
          minWidth: 80,
          suppressColumnsToolPanel: this.props.chartId == 1264 ? true : false,
          //flex: 1,
          dataType: 'category',
          valueFormatter: this.formatCellValueWithOut$,
          cellStyle: this.cellStyles,
          // hide:
          //   this.props.chartType != 1251 &&
          //   this.props.chartType != 1252 &&
          //   this.props.isFrom == 'techcomparison'
          //     ? false
          //     : true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Labor Revenue',
          field: 'lbrsale',
          // width: 120,
          minWidth: 120,

          //flex: 1,
          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
          // hide:
          //   (this.props.chartType != 1251 &&
          //     this.props.chartType != 1252 &&
          //     this.props.chartType == 'undefined' &&
          //     this.props.isFrom == 'techcomparison') ||
          //   this.props.chartType == 'techcomparison'
          //     ? false
          //     : true
        },
        {
          headerName: 'Parts Revenue',
          field: 'prtssale',
          // width: 110,
          minWidth: 110,

          // flex: 1,
          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
          // hide:
          //   (this.props.chartType != 1251 && this.props.chartType != 1252) ||
          //   this.props.chartType == 'techcomparison' ||
          //   this.props.isFrom == 'techcomparison'
          //     ? false
          //     : true
        },

        {
          headerName: 'Total Job Count',
          field: 'totaljobs',
          // width: 93,
          minWidth: 93,
          hide: false,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefsDrilldownWeekly: [
        {
          headerName: 'Tech No',
          field: 'lbrtechname',
          chartDataType: 'category',
          // width: 160,
          minWidth: 160,

          onCellClicked: this.handleCellClicked,
          cellRenderer: function(params) {
            return `<a style="cursor:pointer" >${params.value}</a>`;
          },
          comparator: this.customComparator,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              border: ' 0px white'
            };
          },
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'lbrtechname'
        },

        {
          headerName: 'Week Start Date',
          field: 'closeddate',
          // width: 120,
          minWidth: 120,
          valueFormatter: this.formatCellValueFullMonthYear,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueFullMonthYear
          },
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Sold Hours',
          field: 'flatratehours',
          // width: 80,
          minWidth: 80,
          // minWidth: 125,
          //flex: 1,
          dataType: 'category',
          valueFormatter: this.formatCellValueWithOut$,
          cellStyle: this.cellStyles,
          // hide:
          //   this.props.chartType != 1251 &&
          //   this.props.chartType != 1252 &&
          //   this.props.chartType != 1363 &&
          //   this.props.history &&
          //   this.props.history.location &&
          //   ((this.props.history.location.state &&
          //     ((this.props.history.location.state.chartId &&
          //       this.props.history.location.state.chartId != 1363) ||
          //       this.props.history.location.state.chartId == undefined)) ||
          //     this.props.history.location.state == undefined)
          //     ? false
          //     : true,
          hide: this.props.chartId == 1363 ? true : false,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Labor Revenue',
          field: 'lbrsale',
          // width: 110,
          minWidth: 120,
          // width: 120,
          //flex: 1,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true,
          hide:
            this.props.chartType != 1251 && this.props.chartType != 1252
              ? false
              : true
        },
        {
          headerName: 'Parts Revenue',
          field: 'prtssale',
          // width: 110,
          minWidth: 110,

          // flex: 1,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true,
          hide:
            this.props.chartType != 1251 && this.props.chartType != 1252
              ? false
              : true
        },
        {
          headerName: 'Total RO Count',
          field: 'totalros',
          // width: 93,
          minWidth: 93,
          // hide:
          //   this.props.chartType != 1363 &&
          //   this.props.history &&
          //   this.props.history.location &&
          //   ((this.props.history.location.state &&
          //     ((this.props.history.location.state.chartId &&
          //       this.props.history.location.state.chartId != 1363) ||
          //       this.props.history.location.state.chartId == undefined)) ||
          //     this.props.history.location.state == undefined)
          //     ? true
          //     : false,
          hide: this.props.chartId != 1363 ? true : false,
          //  flex: 1,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        }
      ],

      rowData: [],
      headerHeight: 60,
      // sideBar: 'columns',
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          }
          // {
          //   id: 'filters',
          //   labelDefault: 'Filters',
          //   labelKey: 'filters',
          //   iconKey: 'filter',
          //   toolPanel: 'agFiltersToolPanel'
          // }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        filter: true,
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        resizable: false,
        minWidth: 40,
        flex: 1,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0.0' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      whiteSpace: 'normal',
      border: ' 0px white'
    };
  };

  onCheck = params => {
    const groupColumn =
      this.state.groupColumn &&
      this.state.groupColumn.map(col => {
        if (this.props.chartType !== 1363) {
          if (col.field === 'totalros') {
            return {
              ...col,
              hide: this.props.chartType !== 1363,
              lockVisible: this.props.chartType !== 1363 // Prevents it from appearing in the sidebar
            };
          }
        } else if (this.props.chartType == 1363) {
          if (col.field === 'flatratehours') {
            return {
              ...col,
              hide: this.props.chartType == 1363,
              lockVisible: this.props.chartType == 1363 // Prevents it from appearing in the sidebar
            };
          }
        }
        return col;
      });

    // Update the state and set column definitions
    this.setState({ groupColumn }, () => {
      this.state.rawGridApi.setColumnDefs(groupColumn);
    });
  };

  resetRawData = () => {
    this.setState({ resetReport: this.props.resetReport });
    //  this.gridApi.setColumnDefs([]);
    if (
      this.state.chartType != 1352 ||
      this.state.chartType != 1363 ||
      this.state.type == 'techcomparison' ||
      this.state.type == 'reports'
    ) {
      this.state.gridColumnApiTech.resetColumnState();
    } else {
      this.state.gridColumnApi.resetColumnState();
    }
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    // const columnsGrid =
    //   this.state.type == 'techniciantrendingcharts' ||
    //   this.state.type == 'techcomparison'
    //     ? this.state.columnDefsDrilldown
    //     : this.state.columnDefsSummary;
    // this.gridApi.setColumnDefs(columnsGrid);

    this.props.handleResetReport();
    window.filterStatesTech = {};
    window.sortStatesTech = {};
  };
  formatCellValueTechHrs = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value);
    } else {
      return '0.00';
    }
  };
  formatCellValueFullMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };

  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };

  // formatCellValueWithOut$ = params => {
  //   if (params.value != null && params.value != 0) {
  //     return parseFloat(params.value);
  //     // .toFixed(2)
  //     // .toString()
  //     // .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  //   } else {
  //     return '0.00';
  //   }
  // };
  customComparator = (valueA, valueB) => {
    // Check for null values
    if (valueA === null && valueB === null) {
      return 0; // Both values are null, consider them equal
    } else if (valueA === null) {
      return -1; // valueA is null, place it before valueB
    } else if (valueB === null) {
      return 1; // valueB is null, place it before valueA
    }

    // Splitting and trimming the values
    const nameA = valueA.split('[')[0].trim();
    const nameB = valueB.split('[')[0].trim();

    // Comparing the trimmed values
    return nameA.localeCompare(nameB);
  };
  handleCellClicked = params => {
    let techNo = params.value.split('[')[1];
    window.sortStatesTech = this.gridApi.getSortModel();
    if (
      this.state.chartType != 1352 ||
      this.state.chartType != 1363 ||
      this.state.type == 'techcomparison' ||
      this.state.type == 'reports'
    ) {
      window.colStatesTech1348 = this.state.gridColumnApiTech.getColumnState();
    } else {
      window.colStatesTech =
        this.state.gridColumnApi && this.state.gridColumnApi.getColumnState();
    }
    window.filterStatesTech = this.gridApi.getFilterModel();
    let splittedString = params.data.closeddate.split('-');
    let splittedClosedDate = splittedString[0] + '-' + splittedString[1];
    let data = {
      type: 'technicianlevel',
      techNo: techNo.split(']')[0],
      month_year: params.data.monthYear
        ? params.data.monthYear
        : splittedClosedDate,
      parent: this.props.parent
        ? this.props.parent
        : this.props.type
        ? this.props.type
        : '',
      comparisonMonth1: this.props.comparisonMonth1,
      comparisonMonth2: this.props.comparisonMonth2,
      selectedTechName: this.props.techName,
      selectedReportType: this.props.selectedReportType,
      reportTabSelection: this.props.reportTabSelection,
      prevMenu: this.state.prevMenu,
      monthSelected: this.props.monthSelected,
      payType: this.props.payType,
      chartType: this.state.chartType,
      selectedTechNo: this.state.selectedTechNo,
      weekEnd: this.props.weekEnd,
      weekStart: this.props.weekStart,
      drilldownVal: this.props.drilldownVal
    };
    this.props.parentCallback(data);
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Technician Summary',
      fileName: 'Technician Summary',
      processCellCallback: params => this.processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Technician Summary' },
            mergeAcross: 3
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  processCells = params => {
    const columnId = params.column.getColId();
    const value = params.value;

    if (columnId === 'closeddate') {
      // Format date as MM/DD/YYYY
      const date = new Date(value);
      return `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date
        .getDate()
        .toString()
        .padStart(2, '0')}/${date.getFullYear()}`;
    }

    if (columnId === 'lbrsale' || columnId === 'prtssale') {
      // Add $ symbol to labor and parts revenue
      return `$${value}`;
    }

    if (params.value && /^\d{4}-\d{2}$/.test(params.value)) {
      // Split the value by '-' to get year and month
      const [year, month] = params.value.split('-');
      // Return formatted value as MM/YY
      return `${month}/${year.slice(-2)}`;
    }

    return value;
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    if (
      this.state.chartType != 1352 ||
      this.state.chartType != 1363 ||
      this.state.type == 'techcomparison' ||
      this.state.type == 'reports' ||
      ((this.state.chartType == 1352 || this.state.chartType == 1363) &&
        this.state.type == '')
    ) {
      this.setState({ gridColumnApiTech: params.columnApi });
    } else {
      this.setState({ gridColumnApi: params.columnApi });
    }
    this.gridApi = params.api;

    this.setState({
      groupColumn: params.api.columnController.columnDefs
    });

    console.log('window.filterStatesTech----', window.filterStatesTech);

    this.gridApi.setSortModel(window.sortStatesTech);
    this.gridApi.setFilterModel(window.filterStatesTech);
    this.gridApi.setHeaderHeight(50);

    if (
      this.state.chartType != 1352 ||
      this.state.chartType != 1363 ||
      this.state.type == 'techcomparison' ||
      this.state.type == 'reports' ||
      ((this.state.chartType == 1352 || this.state.chartType == 1363) &&
        this.state.type == '')
    ) {
      if (window.colStatesTech1348) {
        this.state.gridColumnApiTech.setColumnState(window.colStatesTech1348);
      }
    } else {
      if (window.colStatesTech) {
        this.state.gridColumnApi.setColumnState(window.colStatesTech);
      }
    }

    this.getAgGridData(this.state.queryMonth, this.state.techNo);
  };
  getAgGridData(queryMonth, techNo) {
    this.setState({ isLoading: true });
    if (
      (this.state.chartType != 1352 && this.state.type == 'techcomparison') ||
      this.state.type == 'reports' ||
      (this.state.chartType == 1352 && this.state.type == '')
    ) {
      this.state.gridColumnApiTech.setColumnsVisible(
        ['totaljobs', 'monthYear'],
        true
      );
    }

    const groupColumn = this.state.groupColumn;
    if (
      this.state.rawGridApi &&
      (this.state.chartType == 1352 ||
        ((this.state.chartType == 1 || this.state.chartType == undefined) &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.chartId &&
          this.props.history.location.state.chartId == 1352))
    ) {
      if (groupColumn.length > 5) {
        groupColumn[2]['hide'] = false;
        groupColumn[5]['hide'] = true;
        groupColumn[2]['suppressToolPanel'] = false;
        groupColumn[5]['suppressToolPanel'] = true;
        this.state.rawGridApi.setColumnDefs([]);
        this.state.rawGridApi.setColumnDefs(groupColumn);
      } else if (groupColumn.length == 4) {
        groupColumn[0]['hide'] = false;
        groupColumn[1]['hide'] = false;
        groupColumn[2]['hide'] = false;
        groupColumn[3]['hide'] = false;
        groupColumn[2]['suppressToolPanel'] = false;
        this.state.rawGridApi.setColumnDefs([]);
        this.state.rawGridApi.setColumnDefs(groupColumn);
      } else if (groupColumn.length == 5) {
        groupColumn[0]['hide'] = false;
        groupColumn[1]['hide'] = false;
        groupColumn[2]['hide'] = false;
        groupColumn[3]['hide'] = false;
        groupColumn[4]['hide'] = false;
        groupColumn[2]['suppressToolPanel'] = false;
        this.state.rawGridApi.setColumnDefs([]);
        this.state.rawGridApi.setColumnDefs(groupColumn);
      }
    } else if (this.state.rawGridApi) {
      // groupColumn[2]['hide'] = true;
      // groupColumn[5]['hide'] = false;

      // this.state.rawGridApi.setColumnDefs([]);
      // this.state.rawGridApi.setColumnDefs(groupColumn);
      if (groupColumn.length > 5) {
        if (this.props.chartId == 1265) {
          groupColumn[2]['hide'] = false;
          groupColumn[5]['hide'] = false;
          groupColumn[2]['suppressToolPanel'] = false;
          groupColumn[5]['suppressToolPanel'] = false;
        } else if (this.props.chartId == 1264) {
          groupColumn[2]['hide'] = true;
          groupColumn[5]['hide'] = false;
          groupColumn[2]['suppressToolPanel'] = false;
          groupColumn[5]['suppressToolPanel'] = false;
        } else {
          groupColumn[2]['hide'] = true;
          groupColumn[5]['hide'] = false;
          groupColumn[2]['suppressToolPanel'] = true;
          groupColumn[5]['suppressToolPanel'] = false;
        }

        this.state.rawGridApi.setColumnDefs([]);
        this.state.rawGridApi.setColumnDefs(groupColumn);
      } else if (groupColumn.length == 4) {
        groupColumn[0]['hide'] = false;
        groupColumn[1]['hide'] = false;
        groupColumn[2]['hide'] = false;
        groupColumn[3]['hide'] = false;
        groupColumn[2]['suppressToolPanel'] = false;
        this.state.rawGridApi.setColumnDefs([]);
        this.state.rawGridApi.setColumnDefs(groupColumn);
      } else if (groupColumn.length == 5) {
        groupColumn[0]['hide'] = false;
        groupColumn[1]['hide'] = false;
        groupColumn[2]['hide'] = false;
        groupColumn[3]['hide'] = false;
        groupColumn[4]['hide'] = false;
        groupColumn[2]['suppressToolPanel'] = false;
        this.state.rawGridApi.setColumnDefs([]);
        this.state.rawGridApi.setColumnDefs(groupColumn);
      }
    }
    this.state.rawGridApi.setColumnDefs([]);
    this.state.rawGridApi.setColumnDefs(groupColumn);
    if (
      this.state.chartType != 1352 ||
      this.state.chartType != 1363 ||
      this.state.type == 'techcomparison' ||
      this.state.type == 'reports' ||
      ((this.state.chartType == 1352 || this.state.chartType == 1363) &&
        this.state.type == '')
    ) {
      if (window.colStatesTech1348) {
        this.state.gridColumnApiTech.setColumnState(window.colStatesTech1348);
      }
    } else {
      if (window.colStatesTech) {
        this.state.gridColumnApi.setColumnState(window.colStatesTech);
      }
    }

    if (this.props.techPageType == 'techSummary') {
      if (window.colStatesTech1348) {
        this.state.gridColumnApiTech.resetColumnState();
      }
      if (window.colStatesTech) {
        this.state.gridColumnApi.resetColumnState();
      }
    }
    // if (this.props.parent == '' && window.colStatesTech1348) {
    //   this.state.gridColumnApiTech.resetColumnState();
    // }
    // this.state.gridColumnApiTech.resetColumnState();

    queryMonth =
      (this.state.isFrom && queryMonth && this.state.isFrom != 'technician') ||
      (this.props.chartType &&
        (this.props.chartType == '1345' ||
          this.props.chartType == '1347' ||
          this.props.chartType == '1348' ||
          this.props.chartType == '1352' ||
          this.props.chartType == '1363'))
        ? queryMonth
        : getLast13Months();

    if (!queryMonth) {
      queryMonth = getLast13Months();
    }
    /*Fix issue 12390*/
    if (this.state.type == 'technicianlevel' && techNo == 'All') {
      queryMonth = getLast13Months();
      techNo = '';
    }
    if (this.state.isFrom == 'technician' && this.state.type == 'reports') {
      queryMonth = getLast13Months();
    }

    if (
      this.state.type != 'techniciantrendingcharts' &&
      this.state.type != 'techcomparison' &&
      this.state.type != 'reports'
      // &&
      // this.props.history &&
      // this.props.history.location.pathname != '/TechnicianPerformance'
    ) {
      getDrillDownDataTechnicianSummary(queryMonth, techNo, result => {
        this.setState({ isLoading: false });
        if (
          result.data.statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHrs
            .nodes
        ) {
          let sortData = lodash.orderBy(
            result.data
              .statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHrs.nodes,
            'monthYear',
            'desc'
          );

          this.setState({
            rowData: sortData
          });
        }
        if (window.filterStatesTech != undefined) {
          this.filterByValues();
        }
      });
    } else {
      if (
        (this.state.chartType == 1352 ||
          // this.state.chartType == 1363 ||
          (this.state.chartType == undefined &&
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.chartId &&
            // (
            this.props.history.location.state.chartId == 1352) ||
          (this.state.chartType == 1 &&
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.chartId &&
            // (
            this.props.history.location.state.chartId == 1352)) &&
        // ||
        // this.props.history.location.state.chartId == 1363
        this.state.type != '' &&
        this.state.type != 'techcomparison' &&
        this.state.type != 'reports'
      ) {
        getTechnicianDrilldownTechHoursWeekly(
          this.props.payType == 'All' ||
            (this.props.payType == undefined &&
              this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.payType &&
              (this.props.history.location.state.payType == undefined ||
                this.props.history.location.state.payType == null ||
                this.props.history.location.state.payType == 'All'))
            ? null
            : this.props.payType == undefined &&
              this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.payType
            ? this.props.history.location.state.payType
            : this.props.payType,
          techNo == 'All' || techNo == '' ? null : techNo,
          this.props.monthSelected
            ? this.props.monthSelected
            : this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.month_year
            ? this.props.history.location.state.month_year
            : this.props.month_year,
          result => {
            this.setState({ isLoading: false });
            if (
              result.data.statelessCcDrilldownGetDrilldownTechnicianHours
                .drillDownTechnicianHrs
            ) {
              this.setState({
                rowData:
                  result.data.statelessCcDrilldownGetDrilldownTechnicianHours
                    .drillDownTechnicianHrs
              });
            }
            if (window.filterStatesTech != undefined) {
              this.filterByValues();
            }
          }
        );
      } else if (
        (this.state.chartType == 1363 ||
          (this.state.chartType == undefined &&
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.chartId &&
            this.props.history.location.state.chartId == 1363 &&
            (this.props.history.location.state.chartId != 1348 ||
              this.props.history.location.state.chartId != 1347)) ||
          (this.state.chartType == 1 &&
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.chartId &&
            this.props.history.location.state.chartId == 1363 &&
            (this.props.history.location.state.chartId != 1348 ||
              this.props.history.location.state.chartId != 1347))) &&
        this.state.type != '' &&
        this.state.type != 'techcomparison' &&
        this.state.type != 'reports'
      ) {
        getTechDrillDownAllRos(
          queryMonth,
          techNo == 'All' || techNo == '' ? 'All' : techNo,
          this.props.payType == 'All' ||
            (this.props.payType == undefined &&
              this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.payType &&
              (this.props.history.location.state.payType == undefined ||
                this.props.history.location.state.payType == null ||
                this.props.history.location.state.payType == 'All'))
            ? 'All'
            : this.props.payType == undefined &&
              this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.payType
            ? this.props.history.location.state.payType
            : this.props.payType,
          result => {
            this.setState({ isLoading: false });
            console.log('result------------>', result);
            if (
              result.data.statelessCcDrilldownGetDrillDownTechnicianRocount
                .statelessCcDrilldownDrillDownTechnicianRocounts
            ) {
              this.setState({
                rowData:
                  result.data.statelessCcDrilldownGetDrillDownTechnicianRocount
                    .statelessCcDrilldownDrillDownTechnicianRocounts
              });
            }
            if (window.filterStatesTech != undefined) {
              this.filterByValues();
            }
          }
        );
      } else if (this.state.type == 'reports') {
        if (
          !window.filterStatesTech ||
          Object.keys(window.filterStatesTech).length === 0
        ) {
          queryMonth = getLast13Months();
        }

        getDrillDownDataForReports(
          this.props.payType && this.props.payType,
          techNo,
          queryMonth,
          result => {
            this.setState({ isLoading: false });

            if (
              result.data
                .statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHours
                .nodes
            ) {
              let sortData = lodash.orderBy(
                result.data
                  .statelessDbdPeopleMetricsTechnicianGetSummaryTechnicianHours
                  .nodes,
                'monthYear',
                'desc'
              );

              this.setState({
                rowData: sortData
              });
            }
            if (window.filterStatesTech != undefined) {
              this.filterByValues();
            }
          }
        );
      } else {
        getDrillDownDataForTechnician(queryMonth, techNo, result => {
          this.setState({ isLoading: false });
          if (
            result.data.statelessCcDrilldownGetDrillDownTechnicianHrs
              .drillDownTechnicianHrs
          ) {
            this.setState({
              rowData:
                result.data.statelessCcDrilldownGetDrillDownTechnicianHrs
                  .drillDownTechnicianHrs
            });
          }
          if (window.filterStatesTech != undefined) {
            this.filterByValues();
          }
        });
      }
    }
    this.gridApi.setSortModel(window.sortStatesTech);
  }

  filterByValues = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStatesTech);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  getTechName = (techNo, flag) => {
    getAllTechnicians(result => {
      if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
        result.data.statelessCcPhysicalRwGetTechnicians.nodes.map(e => {
          if (e.lbrtechno == techNo) {
            this.setState({
              selectedTechNo:
                (e.nickname ? e.nickname : e.name) +
                ' [' +
                e.lbrtechno.toString() +
                ']'
            });
          } else if (e.name.includes(techNo)) {
            this.setState({
              selectedTechNo:
                (e.nickname ? e.nickname : e.name) +
                ' [' +
                e.lbrtechno.toString() +
                ']'
            });
          }
        });
      }
    });
  };

  handleOpcodeChange = event => {
    let techNo = event.target.value.split('[')[1]
      ? event.target.value.split('[')[1].split(']')[0]
      : event.target.value;

    this.setState({ selectedOpcodeAll: event.target.value });
    if (techNo == 'All' || techNo == '') {
      if (this.props.chartType == 1352 || this.props.chartType == 1363) {
        techNo = 'All';
      } else {
        techNo = '';
      }

      this.setState({ type: this.state.type });
      this.setState({ queryMonth: getLast13Months() });
    }

    //this.getAgGridData(this.state.queryMonth, techNo.toString());
    this.getAgGridData(this.props.monthSelected, techNo.toString());
    // setTimeout(
    //   function() {
    //     this.getAgGridData(this.state.queryMonth, techNo.toString());
    //   }.bind(this),
    //   100
    // );
  };
  renderBackButton = () => {
    window.sortStatesTech = {};
    window.filterStatesTech = {};

    this.resetRawData();

    {
      let data = {};
      if (
        this.state.prevMenu == 'Favorites' ||
        this.props.prevMenu == 'Favorites' ||
        (this.props &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.prevPath &&
          this.props.history.location.state.prevPath == '/MyFavorites')
      ) {
        data = {
          isFrom: 'Favorites'
        };
      } else if (
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.prevPath &&
        this.props.history.location.state.prevPath.includes('/GraphDetailsView')
      ) {
        this.props.history.push({
          pathname: '/GraphDetailsView',
          search:
            '?title=' +
            this.props.history.location.state.prevPath
              .split('?chartId=')
              .pop() +
            '?chartId=' +
            this.props.history.location.state.prevPath.split('?chartId=').pop(),
          state: {
            tech: this.state.techNo,
            tabSelection: 'seven',
            selectedSubTab: 'one',
            SelectedLocation: window.location.pathname,
            techName: this.props.techName,
            parent:
              this.props.parent && this.props.parent == 'Home'
                ? this.props.parent
                : ''
          }
        });
      } else {
        if (
          this.state.type == 'techniciantrendingcharts' ||
          this.props.parent == 'techniciantrendingcharts' ||
          (this.props.parent == 'Home' && this.state.type != 'reports') ||
          (this.state.type == 'technicianlevel' &&
            this.state.techNo != '' &&
            this.props.parent != 'reports' &&
            this.props.parent != 'techcomparison')
        ) {
          data = {
            tabSelection: 'seven',
            isFrom: 'drillDown',
            selectedSubTab: 'one',
            // selectedTech: this.state.techNo,
            selectedTech: this.state.techNo,
            selectedTechName: this.props.techName,
            type: '',
            parent:
              this.props.parent && this.props.parent == 'Home'
                ? this.props.parent
                : '',
            selectedReportType: this.props.selectedReportType,
            reportTabSelection: this.props.reportTabSelection,
            selectedToggle: this.props.selectedToggle
          };
        } else if (
          this.state.type == 'techcomparison' ||
          this.props.parent == 'techcomparison'
        ) {
          data = {
            tabSelection: 'seven',
            isFrom: 'drillDown',
            selectedSubTab: 'two',
            comparisonMonth1: this.props.comparisonMonth1,
            comparisonMonth2: this.props.comparisonMonth2,
            type: '',
            parent: '',
            selectedReportType: this.props.selectedReportType,
            reportTabSelection: this.props.reportTabSelection,
            techNo: '',
            techName: ''
          };
        } else if (
          this.state.type == 'reports' ||
          this.props.parent == 'reports'
        ) {
          data = {
            tabSelection: 'three',
            isFrom: 'drillDown',
            type: '',
            isReportSelected: true,
            selectedReportType: this.props.selectedReportType
              ? this.props.selectedReportType
              : 'allsoldhrs',
            reportTabSelection: this.props.reportTabSelection
          };
        }
      }
      this.props.parentCallback(data);
    }
  };
  getRowStyle = params => {
    if (params.data.lbrtechActive == 0) {
      return { background: 'rgb(221, 234, 244)' };
    } else {
      return { background: 'rgb(255, 255, 255)' };
    }
  };
  render() {
    const { classes } = this.props;

    if (this.props.exportReport) {
      this.onBtExport();
      this.props.handleExportReport();
    }

    return (
      <div>
        {/* {this.state.techNo != '' && ( */}
        {this.state.techNo != '' && this.props.techNo != '' && (
          <div>
            <Paper square style={{ marginLeft: 8, marginRight: 8 }}>
              <Toolbar>
                {this.state.type == 'techniciantrendingcharts' ||
                this.props.parent == 'techniciantrendingcharts' ||
                this.state.type == 'techcomparison' ||
                this.props.parent == 'techcomparison' ||
                this.state.type == 'reports' ||
                this.props.parent == 'reports' ||
                this.props.parent == 'Home' ||
                this.state.type == 'technicianlevel' ? (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.renderBackButton}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                ) : (
                  <Link></Link>
                )}
                <Grid container justify="flex-start" style={{ padding: '5px' }}>
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={classes.formControl}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                    >
                      Tech Numbers
                    </InputLabel>

                    <Select
                      variant="outlined"
                      label="Group By"
                      name="group-by-type"
                      value={
                        this.state.selectedOpcodeAll
                          ? this.state.selectedOpcodeAll
                          : this.state.selectedTechNo
                      }
                      onChange={this.handleOpcodeChange}
                    >
                      {this.state.selectedTechNo.includes('All') ? (
                        <MenuItem value={this.state.selectedTechNo}>
                          All Tech Numbers
                        </MenuItem>
                      ) : (
                        <MenuItem value={this.state.selectedTechNo}>
                          {this.state.selectedTechNo}
                        </MenuItem>
                      )}

                      {this.state.selectedTechNo.includes('All') ? null : (
                        <MenuItem value="All"> All Tech Numbers </MenuItem>
                      )}
                    </Select>
                  </FormControl>
                  {(this.state.type == 'reports' ||
                    this.state.type == 'techcomparison' ||
                    this.state.type == 'techniciantrendingcharts') && (
                    <Tooltip title="Export To Excel">
                      <Link
                        id="export-to-excel"
                        style={{
                          paddingTop: 17,
                          marginLeft: 'auto',
                          cursor: 'pointer'
                        }}
                        onClick={this.onBtExport}
                      >
                        <ExportIcon />
                      </Link>
                    </Tooltip>
                  )}
                </Grid>
              </Toolbar>
            </Paper>
          </div>
        )}
        {/* )} */}
        {this.state.isLoading && (
          <div className={classes.loader}>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id={
            (this.state.type == 'techniciantrendingcharts' ||
              this.state.type == 'techcomparison') &&
            this.state.isFrom != 'technician' &&
            this.state.type != 'reports'
              ? 'data-tab-tech-summary-drilldown'
              : 'data-tab-tech-summary'
          }
          className="ag-theme-balham"
          style={{
            height:
              this.state.isFrom || this.state.type
                ? window.innerHeight - 300 + 'px'
                : window.innerHeight - 300 + 'px',
            // height: this.state.isFrom ? '430px' : '500px',
            // width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            modules={AllModules}
            sideBar={this.state.sideBar}
            getRowStyle={this.getRowStyle}
            // sideBar={
            //   this.state.type == 'techniciantrendingcharts' ||
            //   this.state.type == 'techcomparison'
            //     ? this.state.sideBar
            //     : this.state.sideBar
            // }
            columnDefs={
              ((this.props.history &&
                this.props.history.location &&
                this.props.history.location.state &&
                (this.props.history.location.state.chartId == 1352 ||
                  this.props.history.location.state.chartId == 1363)) ||
                // this.state.chartType == undefined ||
                this.state.chartType == 1352 ||
                this.state.chartType == 1363) &&
              this.state.type != 'techcomparison' &&
              this.state.type != 'reports' &&
              this.state.type != ''
                ? this.state.columnDefsDrilldownWeekly
                : this.state.type == 'techniciantrendingcharts' ||
                  this.state.type == 'techcomparison'
                ? //   &&
                  // this.state.chartType != 1352
                  this.state.columnDefsDrilldown
                : this.state.columnDefsSummary
            }
            //  columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            // enableRangeSelection={true}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            headerHeight={this.state.headerHeight}
            enableRangeSelection={true}
            floatingFilter={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            rowSelection="multiple"
            tooltipShowDelay={0}
            suppressContextMenu={true}
            // onFirstDataRendered={this.autoSizeColumns}
            onColumnVisible={this.onCheck}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
    padding: 4
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  }
});

export default withStyles(styles)(TechnicianSummary);
