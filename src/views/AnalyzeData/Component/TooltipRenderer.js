import React from 'react';
import { withStyles } from '@material-ui/core/styles';
import Tooltip from '@material-ui/core/Tooltip';

const styles = theme => ({
  tooltip: {
    backgroundColor: '#c8c8d5',
    color: '#000000',
    fontSize: theme.typography.pxToRem(12),
    height: 25,
    fontFamily: 'Arial, sans-serif',
    textAlign: 'center',
    top: '50%'
  }
});

class TooltipRenderer extends React.Component {
  render() {
    const { classes, value } = this.props;
    return (
      <BlueOnGreenTooltip
        title={<span style={{ marginTop: 10 }}>View RO</span>}
      >
        <span>{value}</span>
      </BlueOnGreenTooltip>
    );
  }
}

const BlueOnGreenTooltip = withStyles({
  tooltip: {
    backgroundColor: '#c8c8d5',
    color: '#000000',
    fontSize: 12,
    height: 20,
    fontFamily: 'Arial, sans-serif',
    textAlign: 'center',
    top: '50%'
  }
})(Tooltip);

export default withStyles(styles)(TooltipRenderer);
