import React from 'react';
import { withStyles } from '@material-ui/core/styles';
import { Tooltip, IconButton } from '@material-ui/core';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
const styles = theme => ({
  tooltip: {
    backgroundColor: '#c8c8d5',
    color: '#000000',
    fontSize: theme.typography.pxToRem(12),
    height: 25,
    fontFamily: 'Arial, sans-serif',
    textAlign: 'center',
    top: '50%'
  }
});
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    maxWidth: 500,
    // fontSize: '25px',
    // border: '1px solid #dadde9',
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b',
    display: 'grid',
    gridGap: 10,
    backgroundColor: '#ddeaf4',
    right: 100
  }
}))(Tooltip);
class TooltipRendererMarkdown extends React.Component {
  render() {
    const { classes, value } = this.props;
    let val;
    console.log('value===', this.props);
    if (value) {
      if (
        this.props &&
        this.props.data &&
        this.props.data.optionName == 'Chart'
      ) {
        val = JSON.parse(value);
      } else {
        val = value;
      }
    } else {
      val = {};
    }
    return (
      <>
        {this.props && this.props.data && this.props.data.optionName == 'Chart'
          ? value && (
              <HtmlTooltip
                interactive={true}
                placement="bottom-start"
                title={
                  <React.Fragment>
                    <span>
                      <label style={{ fontWeight: 'bold' }}>Overview :</label>{' '}
                      <pre
                        style={{ fontFamily: 'Roboto', whiteSpace: 'pre-wrap' }}
                      >
                        {val && val.overview ? val.overview : ''}{' '}
                      </pre>
                    </span>{' '}
                    <span>
                      <label style={{ fontWeight: 'bold' }}>Calculation:</label>{' '}
                      <br />
                      {val && val.overview
                        ? Object.keys(val.calculation).map((value, i) => {
                            return (
                              <>
                                <span>{val.calculation[i]}</span>
                                <br />
                              </>
                            );
                          })
                        : ''}
                      {/* {Object.keys(val.calculation).map((value, i) => {
                return (
                  <>
                    <span>{val.calculation[i]}</span>
                    <br />
                  </>
                );
              })} */}
                    </span>
                  </React.Fragment>
                }
              >
                {/* <span>{value}</span> */}

                <span>
                  {/* <Tooltip> */}
                  <IconButton size="small" classes="infoIcon">
                    <InfoOutlinedIcon
                      style={{
                        width: '12px',
                        height: '12px',
                        gap: '5 !important'
                      }}
                    />
                  </IconButton>
                  {/* </Tooltip> */}
                </span>
              </HtmlTooltip>
            )
          : value && (
              <>
                <HtmlTooltip
                  interactive={true}
                  placement="bottom-start"
                  title={
                    <React.Fragment>
                      {' '}
                      <span>
                        <label style={{ fontWeight: 'bold' }}>
                          {' '}
                          {this.props &&
                          this.props.colDef &&
                          this.props.colDef.field == 'oldMilegeIntervals'
                            ? 'Old Milege Intervals'
                            : this.props &&
                              this.props.colDef &&
                              this.props.colDef.field == 'newMilegeIntervals'
                            ? 'New Milege Intervals'
                            : this.props &&
                              this.props.colDef &&
                              this.props.colDef.field == 'oldMenuOpcodes'
                            ? 'Old Menu Opcodes'
                            : this.props &&
                              this.props.colDef &&
                              this.props.colDef.field == 'menuOpcodes'
                            ? 'New Menu Opcodes'
                            : ''}{' '}
                        </label>{' '}
                        <pre
                          style={{
                            fontFamily: 'Roboto',
                            whiteSpace: 'pre-wrap'
                          }}
                        >
                          {val && val ? val : ''}{' '}
                        </pre>
                      </span>{' '}
                    </React.Fragment>
                  }
                >
                  <span>
                    <IconButton size="small" classes="infoIcon">
                      <InfoOutlinedIcon
                        style={{
                          width: '12px',
                          height: '12px',
                          gap: '5 !important'
                        }}
                      />
                    </IconButton>
                  </span>
                </HtmlTooltip>
              </>
            )}
      </>
    );
  }
}

export default withStyles(styles)(TooltipRendererMarkdown);
