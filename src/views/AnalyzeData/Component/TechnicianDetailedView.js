import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Toolbar,
  Typography,
  Tooltip,
  Button
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  getDrillDownDataByTechNo,
  getDrillDownDataByServiceAdvisor,
  getDrillDownTechAdvisorsweeklyRevenues
} from 'src/utils/hasuraServices';
import {
  getLastThreeYears,
  getComparisonMonths,
  getLast13Months
} from 'src/utils/Utils';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import moment from 'moment';
import { logDOM } from '@testing-library/react';
import { traceSpan } from 'src/utils/OTTTracing';
import { ReactSession } from 'react-client-session';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
var lodash = require('lodash');

class TechnicianDetailedView extends React.Component {
  componentWillMount() {
    console.log('enter=000');
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps, prevState) {
    if (this.props.resetReport != this.state.resetReport) {
      this.resetRawData();
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      if (
        prevProps.session.serviceAdvisor != this.props.session.serviceAdvisor
      ) {
        console.log('enter=123=');
        var monthYear = this.props.month_year;
        var queryMonth =
          this.props.parent != undefined && this.props.month_year
            ? this.props.month_year
            : getComparisonMonths()[1];
        var checkSt = lodash.isEqual(
          this.state.store,
          ReactSession.get('selectedStoreId')
        );
        if (checkSt == false) {
          window.filterStatesTech = {};
          window.sortStatesTech = {};
          const { history } = this.props;
          this.setState({ drilldownVal: '' });
          this.setState({ storeChange: true });
          if (this.state.isFromTechnician) {
            this.setState({ isLoading: true });
            this.getAgGridData('', queryMonth, 'stores');
            this.setState({ store: localStorage.getItem('selectedStoreId') });

            this.setState({ type: '' });
            // this.setState({
            //   selectedMonthYear:
            //     localStorage.getItem('versionFlag') == 'TRUE'
            //       ? getLastThreeYears()[35]
            //       : getLast13Months()[12]
            // });
          } else {
            // this.setState({
            //   selectedMonthYear:
            //     localStorage.getItem('versionFlag') == 'TRUE'
            //       ? getLastThreeYears()[35]
            //       : getLast13Months()[12]
            // });
            this.getAgGridData(this.props.session.serviceAdvisor, queryMonth);
            this.setState({ store: localStorage.getItem('selectedStoreId') });
            this.setState({ isLoading: true });
            this.setState({ type: '' });
          }

          this.resetRawData();
        }
      }
    } else if (
      prevProps.session.serviceAdvisor != this.props.session.serviceAdvisor
    ) {
      this.setState({ storeChange: true });
      var monthYear = this.props.month_year;
      var queryMonth =
        this.props.parent != undefined && this.props.month_year
          ? this.props.month_year
          : getComparisonMonths()[1];
      this.setState({ serviceadvisor: this.props.session.serviceAdvisor });
      if (this.state.isFromTechnician) {
        this.getAgGridData(this.props.techNo, queryMonth);
        this.setState({ isLoading: true });
      } else {
        // this.getAgGridData(this.props.session.serviceAdvisor, queryMonth);
        this.getAgGridData(
          this.props.session.serviceAdvisor,
          this.state.selectedMonthYear
        );
        this.onFirstDataRendered();
        this.setState({ isLoading: true });
      }
    }
  }

  // componentDidUpdate(prevProps) {
  //   if (prevProps.session.serviceAdvisor != this.props.session.serviceAdvisor) {
  //     // var monthYear = this.props.month_year;
  //     // var queryMonth =
  //     //   this.props.parent != undefined && this.props.month_year
  //     //     ? this.props.month_year
  //     //     : getComparisonMonths()[1];

  //     // this.setState({ serviceadvisor: this.props.session.serviceAdvisor });
  //     // if (this.state.isFromTechnician) {
  //     //   this.getAgGridData(this.props.techNo, queryMonth);
  //     //   this.setState({ isLoading: true });
  //     // } else {
  //     //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     //     {
  //     //       this.getAgGridData(this.props.session.serviceAdvisor, queryMonth);
  //     //       this.setState({ isLoading: true });
  //     //     }
  //     //   }
  //     // }
  //   }
  //   if (this.props.resetReport != this.state.resetReport) {
  //     this.resetRawData();
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50
    //month_year: '2020-04',
    // techNo: '159089',
    // serviceAdvisor: '26932'
  };

  componentDidMount() {
    if (this.state.isFromTechnician) {
      this.getAgGridData(this.state.techNo, this.state.queryMonth);
    } else {
      this.getAgGridData(this.state.serviceAdvisor, this.state.queryMonth);
    }
  }

  constructor(props) {
    super(props);
    var monthYear = this.props.month_year;
    var queryMonth =
      this.props.parent != undefined && this.props.month_year
        ? this.props.month_year
        : getComparisonMonths()[1];
    console.log(
      'this.props.history.location.state',
      this.props.history.location.state
    );

    // let techNo = this.props.techNo ? this.props.techNo : this.props.techName ?
    // this.props.techName.split('[')[1]
    // .split(']')[0]
    // .toString() : '';

    let techNo = this.props.techNo ? this.props.techNo : '';
    techNo =
      this.props.parent == undefined || this.props.month_year == ''
        ? ''
        : techNo;
    var isFromTechnician = this.props.isFromTechnician;
    var serviceAdvisor =
      this.props.parent == undefined ||
      this.props.month_year == '' ||
      (this.props.parent == '' &&
        this.props.type == 'serviceadvisorlevel' &&
        (this.props.month_year == undefined || this.props.month_year == ''))
        ? ''
        : this.props.serviceAdvisor;
    // var serviceAdvisor = this.props.parent != undefined ? this.props.serviceAdvisor : '';
    var type = this.props.type ? this.props.type : '';

    var realm = this.props.realm;
    var selectedReportType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.selectedReportType
        : this.props.selectedReportType
        ? this.props.selectedReportType
        : '';
    var reportTabSelection =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.reportTabSelection
        : this.props.reportTabSelection
        ? this.props.reportTabSelection
        : '';
    var prevMenu =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.prevMenu
        ? this.props.history.location.state.prevMenu
        : this.props.prevMenu;

    let payType =
      this.props.payType != undefined
        ? this.props.payType
        : this.props.history.location.state &&
          this.props.history.location.state.payType;
    var chartType =
      this.props.chartType != undefined
        ? this.props.chartType
        : this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.chartId
        ? this.props.history.location.state.chartId
        : this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.chartType;

    var drilldownVal =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.drilldownVal
        ? this.props.history.location.state.drilldownVal
        : this.props.drilldownVal;
    var weekStart =
      this.props.weekStart != undefined
        ? this.props.weekStart
        : this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.weekStart;
    var weekEnd =
      this.props.weekEnd != undefined
        ? this.props.weekEnd
        : this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.weekEnd;

    let storeId = JSON.parse(localStorage.getItem('selectedStoreId'));
    this.state = {
      showSummaryValues: true,
      storeChange: false,
      weekStart: weekStart,
      weekEnd: weekEnd,
      queryMonth: queryMonth,
      resetReport: this.props.resetReport,
      techNo: techNo,
      isLoading: true,
      jobCount: 0,
      roCount: 0,
      soldHours: 0,
      lbrSale: 0,
      lbrCost: 0,
      prtsSale: 0,
      prtsCost: 0,
      lbrGPPerc: 0,
      prtsGPPerc: 0,
      isFromTechnician: isFromTechnician,
      selectedMonthYear: this.props.month_year
        ? this.props.month_year
        : localStorage.getItem('versionFlag') == 'TRUE'
        ? getLastThreeYears()[35]
        : getLast13Months()[12],
      //serviceAdvisor: serviceAdvisor,
      serviceAdvisor:
        this.props.type == 'serviceadvisorlevel'
          ? serviceAdvisor
          : this.props.session
          ? this.props.session.serviceAdvisor
          : '',
      type: type,
      selectedReportType: selectedReportType,
      reportTabSelection: reportTabSelection,
      prevMenu: prevMenu,
      payType: payType,
      chartType: chartType,
      drilldownVal: drilldownVal,
      columnDefs: [
        {
          headerName: 'Tech No',
          field: 'lbrtechname',
          width: 160,
          chartDataType: 'series',
          hide: isFromTechnician ? false : true,
          suppressToolPanel: isFromTechnician ? false : true,
          cellClass: 'textAlign',
          // flex: 1,
          comparator: this.customComparator,
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'lbrtechname',
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 145,
          chartDataType: 'series',
          // flex: 1,
          //  cellStyle: this.setCellStyle,
          hide: isFromTechnician ? true : false,
          suppressToolPanel: isFromTechnician ? true : false,
          cellClass: 'textAlign',
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // },
          tooltipField: 'advisorName',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
            let nameA = valueA.split('[')[0].trim();
            let nameB = valueB.split('[')[0].trim();
            return nameA.localeCompare(nameB);
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 90,
          // flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMonthYear
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          // flex: 1,
          // width: 83,
          width: 85,
          minWidth: 85,
          chartDataType: 'category',
          showRowGroup: false,
          onCellClicked: this.handleSearchByRo,

          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          },
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 93,
          chartDataType: 'category',
          // flex: 1,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          minWidth: 150,
          // flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          filterValueGetter: params => {
            let value = params.data.lbropcodedesc.replace(/(\r\n|\n|\r)/gm, '');
            value = value.trimStart();
            return value?.length > 25 ? value.substring(0, 25) + '...' : value; // Show trimmed text
          }
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          chartDataType: 'category',
          suppressMenu: true,
          // flex: 1,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 55,
        //   sortable: false,
        //   filter: false,
        //   suppressMenu: true,

        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   filter: 'agSetColumnFilter',
        //   filterParams: {
        //     values: ['N/A', '']
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          tooltipField: 'paytype',
          width: realm == 'ferrarioat_store' ? 100 : 75,
          chartDataType: 'category',
          suppressMenu: true,
          // flex: 1,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 83,
          chartDataType: 'string',
          // flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'center' };
          }
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          width: 85,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          width: 85,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          // flex: 1,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Profit',
          field: 'lbrprofit',
          width: 85,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          width: 85,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          width: 80,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtscost',
          width: 82,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Profit',
          field: 'prtsprofit',
          width: 83,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        // {
        //   headerName: 'Actual Hours',
        //   field: 'lbractualhours',
        //   width: 90,
        //   chartDataType: 'series',
        //   cellStyle: this.cellStyles,
        //   valueFormatter: this.formatCellValueWithOut$,
        //   cellClass: 'twoDecimalPlacesWithOut$',
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        {
          headerName: 'Job Count',
          field: 'jobcount',
          width: 88,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          cellClass: 'twoDecimalPlacesWithOut$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'ELR',
          field: 'elr',
          width: 68,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup',
          field: 'markup',
          width: 95,
          chartDataType: 'series',
          // flex: 1,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMarkup
          },
          cellStyle: this.cellStyles,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      columnDefWeekly: [
        {
          headerName: 'Tech No',
          field: 'lbrtechname',
          width: 160,
          chartDataType: 'series',
          hide: isFromTechnician ? false : true,
          suppressToolPanel: isFromTechnician ? false : true,
          cellClass: 'textAlign',
          // flex: 1,
          comparator: this.customComparator,
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'lbrtechname',
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 145,
          chartDataType: 'series',
          // flex: 1,
          //  cellStyle: this.setCellStyle,
          hide: isFromTechnician ? true : false,
          suppressToolPanel: isFromTechnician ? true : false,
          cellClass: 'textAlign',
          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // },
          tooltipField: 'advisorName',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: (valueA, valueB, nodeA, nodeB, isInverted) => {
            let nameA = valueA.split('[')[0].trim();
            let nameB = valueB.split('[')[0].trim();
            return nameA.localeCompare(nameB);
          }
        },
        // {
        //   headerName: 'Month',
        //   field: 'monthYear',
        //   width: 90,
        //   flex: 1,
        //   chartDataType: 'category',
        //   valueFormatter: this.formatCellValueMonthYear,
        //   filterParams: {
        //     valueFormatter: this.formatCellValueMonthYear
        //   },
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle() {
        //     return { border: ' 0px white' };
        //   }
        // },
        {
          headerName: 'Closed Date',
          field: 'closeddate',

          width: 90,
          valueFormatter: this.formatCellValueFullMonthYear,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueFullMonthYear
          },
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          // width: 83,
          width: 85,
          // flex: 1,
          minWidth: 85,
          chartDataType: 'category',
          showRowGroup: false,
          onCellClicked: this.handleSearchByRo,

          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          },
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          // flex: 1,
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 93,
          chartDataType: 'category',

          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 150,
          chartDataType: 'category',
          minWidth: 150,
          // flex: 1,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          chartDataType: 'category',
          suppressMenu: true,
          // flex: 1,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          tooltipField: 'paytype',
          width: realm == 'ferrarioat_store' ? 100 : 75,
          chartDataType: 'category',
          suppressMenu: true,
          // flex: 1,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 83,
          chartDataType: 'string',
          // flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'center' };
          }
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          width: 85,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          width: 85,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          // flex: 1,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Profit',
          field: 'lbrprofit',
          width: 85,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          width: 85,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          width: 80,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtscost',
          width: 82,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Profit',
          field: 'prtsprofit',
          width: 83,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Job Count',
          field: 'jobcount',
          width: 88,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          cellClass: 'twoDecimalPlacesWithOut$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'ELR',
          field: 'elr',
          width: 68,
          chartDataType: 'series',
          // flex: 1,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Markup',
          field: 'markup',
          width: 95,
          chartDataType: 'series',
          // flex: 1,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            applyMiniFilterWhileTyping: true,
            valueFormatter: this.formatCellValueMarkup
          },
          cellStyle: this.cellStyles,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      rowData: [],

      headerHeight: 45,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0.0' }
        },
        {
          id: 'fourDecimalPlaces',
          numberFormat: { format: '###0.0000' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  setCellStyle = params => {
    let activeAdv = [];
    this.state.rowData.filter(e => {
      if (e.advisorActive != 1) {
        activeAdv.push(e.advisorName);
      }
    });
    return {
      color: '#000000',
      fontWeight: 'bold',
      backgroundColor: activeAdv.includes(params.data.advisorName)
        ? '#ddeaf4'
        : ''
    };
  };
  resetRawData = () => {
    this.setState({ resetReport: this.props.resetReport });
    this.setState({ payType: 'All' });
    // this.setState({
    //   selectedMonthYear:
    //     localStorage.getItem('versionFlag') == 'TRUE'
    //       ? getLastThreeYears()[35]
    //       : getLast13Months()[12]
    // });
    this.state.gridColumnApi.resetColumnState();
    // this.getAgGridData(
    //   this.state.techNo,
    //   localStorage.getItem('versionFlag') == 'TRUE'
    //     ? getLastThreeYears()[35]
    //     : getLast13Months()[12]
    // );
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.props.handleResetReport();
    if (this.state.isFromTechnician) {
      window.filterStateTech = {};
      window.sortStateTech = {};
    } else {
      window.filterStateAdv = {};
      window.sortStateAdv = {};
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  customComparator = (valueA, valueB) => {
    const nameA = valueA.split('[')[0].trim();
    const nameB = valueB.split('[')[0].trim();

    return nameA.localeCompare(nameB);
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueFullMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };
  onBtExport = () => {
    var params = {
      sheetName: this.state.isFromTechnician
        ? 'Technician Detailed View'
        : 'Service Advisor Detailed View',
      fileName: this.state.isFromTechnician
        ? 'Technician Detailed View'
        : 'Service Advisor Detailed View',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: this.state.isFromTechnician
                ? 'Technician Detailed View'
                : 'Service Advisor Detailed View'
            },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: params => {
        // Check if the cell value matches the YYYY-MM format
        if (params.value && /^\d{4}-\d{2}$/.test(params.value)) {
          // Split the value by '-' to get year and month
          const [year, month] = params.value.split('-');
          // Return formatted value as MM/YY
          return `${month}/${year.slice(-2)}`;
        }
        // Return the original value if it doesn't match the format
        return params.value;
      }
    };

    this.state.rawGridApi.exportDataAsExcel(params);
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title: this.state.isFromTechnician
        ? 'Technician Detailed View'
        : 'Service Advisor Detailed View',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to Excel', spanAttribute);
  };

  onFirstDataRendered = params => {
    if (this.gridApi) {
      this.gridApi.getFilterInstance('opcategory', filterInstance => {
        if (filterInstance) {
          const values = filterInstance.getValues();
          if (values && values.includes('N/A')) {
            filterInstance.setModel({
              filterType: 'set',
              values: values.filter(value => value !== 'N/A')
            });
            this.gridApi.onFilterChanged();
          }
        }
      });
    }
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    this.gridApi = params.api;

    if (this.state.isFromTechnician) {
      if (this.props.pageType == '' || this.state.resetReport == true) {
        window.sortStateTech = {};
        window.filterStateTech = {};
      }
      this.gridApi.setSortModel(window.sortStateTech);
      this.gridApi.setFilterModel(window.filterStateTech);
      if (window.colStateTechDetail) {
        this.state.gridColumnApi.setColumnState(window.colStateTechDetail);
      }
      if (
        window.colStateTechDetail &&
        (this.props.pageType == '' || this.state.resetReport == true)
      ) {
        this.state.gridColumnApi.resetColumnState();
      }
    } else {
      if (this.props.pageType == '' || this.state.resetReport == true) {
        window.sortStateAdv = {};
        window.filterStateAdv = {};
      }
      this.gridApi.setSortModel(window.sortStateAdv);
      this.gridApi.setFilterModel(window.filterStateAdv);
      if (window.colStateAdv) {
        this.state.gridColumnApi.setColumnState(window.colStateAdv);
      }
      if (
        window.colStateAdv &&
        (this.props.pageType == '' || this.state.resetReport == true)
      ) {
        this.state.gridColumnApi.resetColumnState();
      }
    }
    this.setState({
      groupColumn: this.state.rawGridApi.columnController.columnDefs
    });
    if (this.state.isFromTechnician) {
      const groupColumnNamed = this.state.groupColumn;

      const fieldToRemove = 'advisorName';
      var columnDefs = groupColumnNamed.filter(
        column => column.field !== fieldToRemove
      );

      this.state.rawGridApi.setColumnDefs(columnDefs);
    } else {
      const groupColumnNamed = this.state.groupColumn;

      const fieldToRemove = 'lbrtechname';
      var columnDefs = groupColumnNamed.filter(
        column => column.field !== fieldToRemove
      );

      this.state.rawGridApi.setColumnDefs(columnDefs);
    }
  };
  parseArray(filtererdArray) {
    let intArray = [];

    var res = filtererdArray.map(v => {
      intArray.push({
        closeddate: v.closeddate,
        elr: parseFloat(v.elr) || 0,
        jobcount: v.jobcount,
        lbractualhours: v.lbractualhours,
        lbrcost: parseFloat(v.lbrcost) || 0,
        lbrlinecode: v.lbrlinecode,
        lbropcode: v.lbropcode,
        lbropcodedesc: v.lbropcodedesc,
        lbrprofit: parseFloat(v.lbrprofit) || 0,
        lbrtechActive: v.lbrtechActive,
        lbrsale: parseFloat(v['lbrsale']) || 0,
        lbrsoldhours: parseFloat(v['lbrsoldhours']) || 0,
        lbrsequenceno: v.lbrsequenceno,
        lbrtechno: v.lbrtechno,
        opcategory: v.opcategory,
        markup: parseFloat(v.markup) || 0,
        monthYear: v.monthYear,

        opsubcategory: v.opsubcategory,
        paytype: v.paytype,
        paytypegroup: v.paytypegroup,
        prtscost: parseFloat(v.prtscost) || 0,
        prtsprofit: parseFloat(v.prtsprofit) || 0,
        prtssale: parseFloat(v.prtssale) || 0,
        ronumber: v.ronumber,
        serviceadvisor: v.serviceadvisor,
        techefficiency: v.techefficiency,
        advisorName: v.advisorName,
        lbrtechname: v.lbrtechname,
        advisorActive: v.advisorActive
      });
      // return res;
    });
    return intArray;
  }
  getAgGridData(params, queryMonth, stores) {
    //     if(this.props.session.serviceAdvisor !='All'){
    // this.setState({isFromTechnician:false})
    //     }
    if (this.state.isFromTechnician) {
      var techNo = [];
      if (this.state.type) {
        techNo = [params];
      } else {
        techNo = [];
        var d = new Date();
        var m = d.getMonth();
        m.toString().length == 1 ? (m = '0' + m) : (m = m);

        var y = d.getFullYear();
        var queryMonth1 = y + '-' + m;

        // if (queryMonth1 == queryMonth || queryMonth > queryMonth1) {
        //   queryMonth = queryMonth1;
        // } else {
        //   queryMonth = queryMonth;
        // }
        //this.setState({ selectedMonthYear: queryMonth });
      }
      if (
        this.state.drilldownVal == 'techProd' &&
        this.state.storeChange == false &&
        this.state.chartType == 1363 &&
        stores != 'stores'
      ) {
        const wstartdate = this.state.weekStart;
        const wenddate = this.state.weekEnd;
        const lbrTechno = techNo;
        const serviceAdvisor = null;
        const monthyear = null;

        getDrillDownTechAdvisorsweeklyRevenues(
          wstartdate,
          wenddate,
          lbrTechno,
          serviceAdvisor,
          monthyear,
          this.state.payType != 'All' ? this.state.payType : null,
          this.state.chartType,
          result => {
            this.setState({ isLoading: false });
            if (
              result.data.statelessCcDrilldownGetDrillDownTechWeeklyDetails
                .drillDownTechWeeklyDetails
            ) {
              var intArray = this.parseArray(
                result.data.statelessCcDrilldownGetDrillDownTechWeeklyDetails
                  .drillDownTechWeeklyDetails
              );

              // if (
              //   this.props.parent == 'techniciantrendingcharts' &&
              //   this.state.payType != 'All'
              // ) {
              // intArray = intArray.filter(
              //   item => this.state.payType.includes(item.paytypegroup)
              //   //item.paytypegroup == this.state.payType
              // );
              //}
              intArray.map(item => {
                if (item.opsubcategory && item.opsubcategory != 'N/A') {
                  return (item.opsubcategory = '');
                }
              });
              this.setState({
                rowData: intArray
              });
              this.getTotalsForDisplay(intArray);
            }
            this.getMonthYear();
            if (window.filterStateTech != undefined) {
              this.filterByValues();
            }
          }
        );
      }
      if (
        this.state.drilldownVal == 'techProd' &&
        this.state.storeChange == false &&
        this.state.chartType == 1352 &&
        stores != 'stores'
      ) {
        const wstartdate = this.state.weekStart;
        const wenddate = this.state.weekEnd;
        const lbrTechno = techNo;
        const serviceAdvisor = null;
        const monthyear = null;

        getDrillDownTechAdvisorsweeklyRevenues(
          wstartdate,
          wenddate,
          lbrTechno,
          serviceAdvisor,
          monthyear,
          this.state.payType != 'All' ? this.state.payType : null,
          this.state.chartType,
          result => {
            this.setState({ isLoading: false });
            if (
              result.data
                .statelessCcDrilldownGetDrillDownTechAdvisorsweeklyRevenues
                .nodes
            ) {
              var intArray = this.parseArray(
                result.data
                  .statelessCcDrilldownGetDrillDownTechAdvisorsweeklyRevenues
                  .nodes
              );

              if (
                this.props.parent == 'techniciantrendingcharts' &&
                this.state.payType != 'All'
              ) {
                intArray = intArray.filter(
                  item => this.state.payType.includes(item.paytypegroup)
                  //item.paytypegroup == this.state.payType
                );
              }
              intArray.map(item => {
                if (item.opsubcategory && item.opsubcategory != 'N/A') {
                  return (item.opsubcategory = '');
                }
              });
              this.setState({
                rowData: intArray
              });
              this.getTotalsForDisplay(intArray);
            }
            this.getMonthYear();
            if (window.filterStateTech != undefined) {
              this.filterByValues();
            }
          }
        );
      } else {
        getDrillDownDataByTechNo(techNo, queryMonth, result => {
          if (
            result.data.statelessCcDrilldownGetDrillDownTechAdvisorsRevenues
              .nodes
          ) {
            var intArray = this.parseArray(
              result.data.statelessCcDrilldownGetDrillDownTechAdvisorsRevenues
                .nodes
            );

            // const payType =
            //   this.props.payType != undefined
            //     ? this.props.payType
            //     : this.props.history.location.state &&
            //       this.props.history.location.state.payType;
            if (
              this.props.parent == 'techniciantrendingcharts' &&
              this.state.payType != 'All'
            ) {
              intArray = intArray.filter(
                item => this.state.payType.includes(item.paytypegroup)
                //item.paytypegroup == this.state.payType
              );
            }
            if (this.props.parent == 'reports' && this.state.payType == 'C') {
              intArray = intArray.filter(item => item.paytypegroup == 'C');
            }
            intArray.map(item => {
              if (item.opsubcategory && item.opsubcategory != 'N/A') {
                return (item.opsubcategory = '');
              }
            });
            this.setState({
              rowData: intArray
            });
            this.getTotalsForDisplay(intArray);
            this.setState({ isLoading: false });
          }
          this.getMonthYear();
          if (window.filterStateTech != undefined) {
            this.filterByValues();
          }
        });
      }
    } else {
      var serviceAdvisor = [];

      if (this.state.type) {
        if (params.includes('All')) {
          serviceAdvisor = [];
        } else {
          serviceAdvisor = params;
        }
      } else {
        serviceAdvisor = [];
        var d = new Date();
        var m = d.getMonth();
        m.toString().length == 1 ? (m = '0' + m) : (m = m);

        var y = d.getFullYear();
        var queryMonth1 = y + '-' + m;
        // if (queryMonth1 == queryMonth || queryMonth > queryMonth1) {
        //   queryMonth = queryMonth1;
        // } else {
        //   queryMonth = queryMonth;
        // }
        //this.setState({ selectedMonthYear: queryMonth });
        if (this.props.session.serviceAdvisor != 'All' && !this.state.type) {
          serviceAdvisor = this.props.session.serviceAdvisor;
        }
      }
      console.log(
        'ccc===ccc',
        this.state.type,
        serviceAdvisor,
        this.props.session.serviceAdvisor
      );
      getDrillDownDataByServiceAdvisor(serviceAdvisor, queryMonth, result => {
        this.setState({ isLoading: false });
        if (
          result.data.statelessCcDrilldownGetDrillDownTechAdvisorsRevenues.nodes
        ) {
          var intArray = this.parseArray(
            result.data.statelessCcDrilldownGetDrillDownTechAdvisorsRevenues
              .nodes
          );
          this.setState({
            rowData: intArray
          });
          this.getTotalsForDisplay(intArray);
        }
        this.getMonthYear();
        if (window.filterStateAdv != undefined) {
          this.filterByValues();
        }
      });
    }
  }

  filterByValues = () => {
    if (this.gridApi) {
      this.gridApi.getFilterInstance('opcategory', filterInstance => {
        if (filterInstance) {
          const values = filterInstance.getValues();
          if (values && values.includes('N/A')) {
            filterInstance.setModel({
              filterType: 'set',
              values: values.filter(value => value !== 'N/A')
            });
            this.gridApi.onFilterChanged();
          }
        }
      });
    }
    var countryFilterComponent = '';
    if (this.state.isFromTechnician) {
      var filterArr = Object.entries(window.filterStateTech);
    } else {
      var filterArr = Object.entries(window.filterStateAdv);
    }

    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  handleMonthYearChange = event => {
    let monthYear = event.target.value;
    this.setState({ selectedMonthYear: monthYear });
    // this.gridApi.setFilterModel(null); // reset filter

    if (
      this.props.parent != 'reports' &&
      this.props.parent != 'advisortrendingcharts'
    ) {
      this.setState({ parent: '' });
    } else if (
      this.props.parent == 'advisortrendingcharts' &&
      this.props.type != 'serviceadvisorlevel'
    ) {
      this.setState({ parent: 'advisortrendingcharts' });
    }
    setTimeout(
      function() {
        if (this.state.isFromTechnician) {
          this.setState({ isLoading: true });
          this.getAgGridData(this.state.techNo, monthYear);
        } else {
          this.setState({ isLoading: true });
          this.getAgGridData(this.state.serviceAdvisor, monthYear);
        }
      }.bind(this),
      100
    );
  };

  getMonthYear = () => {
    var table = [];
    this.setState({
      opcodeMonthYear:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? getLastThreeYears()
          : getLast13Months()
    });
  };
  renderBackButton = () => {
    {
      window.filterStateTech = {};
      window.sortStateTech = {};
      window.filterStateAdv = {};
      window.sortStateAdv = {};
      let data = {};
      if (this.state.type == 'technicianlevel') {
        data = {
          tabSelection: 'nine',
          isFrom: 'drillDown',
          parent: this.state.parent
            ? this.state.parent
            : this.props.type == 'technicianlevel' &&
              this.props.parent == 'technicianlevel'
            ? ''
            : this.props.parent,
          comparisonMonth1: this.props.comparisonMonth1,
          comparisonMonth2: this.props.comparisonMonth2,
          selectedTechName: this.props.techName,
          techNo: '',
          month_year: '',
          selectedReportType: this.state.selectedReportType,
          reportTabSelection: this.state.reportTabSelection,
          prevMenu: this.state.prevMenu,
          // monthSelected: this.props.monthSelected,
          //monthSelected: this.state.queryMonth,
          monthSelected: this.props.monthSelected
            ? this.props.monthSelected
            : this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.monthSelected
            ? this.props.history.location.state.monthSelected
            : this.state.queryMonth,
          payType:
            this.props.payType != undefined
              ? this.props.payType
              : this.props.history.location.state &&
                this.props.history.location.state.payType,

          // payType: this.state.selectedReportType == 'flatratehrs' ? 'C' : 'All',
          chartType: this.state.chartType,
          techPageType: ''
        };
      } else if (this.state.type == 'serviceadvisorlevel') {
        data = {
          tabSelection: 'eleven',
          isFrom: 'drillDown-sa',
          parent:
            this.state.parent == ''
              ? this.state.parent
              : this.props.type == 'serviceadvisorlevel' &&
                this.props.parent == 'serviceadvisorlevel'
              ? ''
              : this.props.parent,
          comparisonMonth1: this.props.comparisonMonth1,
          comparisonMonth2: this.props.comparisonMonth2,
          month_year:
            //          this.props.parent == 'reports' ||
            // this.state.type == 'serviceadvisorlevel'
            //   ? this.props.month_year
            this.props.parent == 'reports'
              ? getLast13Months()
              : this.state.type == 'serviceadvisorlevel'
              ? this.props.month_year
              : '',
          serviceAdvisor: this.props.serviceAdvisor
            ? this.props.serviceAdvisor
            : '',
          selectedReportType: this.state.selectedReportType,
          reportTabSelection: this.state.reportTabSelection,
          chartLoc:
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.chartLoc
              ? this.props.history.location.state.chartLoc
              : this.props.chartLoc,
          prevPath:
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.prevPath
              ? this.props.history.location.state.prevPath
              : this.props.prevPath,
          prevMenu: this.state.prevMenu
        };
      }
      this.props.parentCallback(data);
    }
  };
  handleSearchByRo = params => {
    let monthY = this.props.monthSelected
      ? this.props.monthSelected
      : this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.monthSelected
      ? this.props.history.location.state.monthSelected
      : this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.month_year
      ? this.props.history.location.state.month_year
      : this.state.selectedMonthYear;
    if (this.state.isFromTechnician) {
      window.sortStateTech = this.gridApi.getSortModel();
      window.colStateTechDetail = this.state.gridColumnApi.getColumnState();
      window.filterStateTech = this.gridApi.getFilterModel();
    } else {
      window.sortStateAdv = this.gridApi.getSortModel();
      window.colStateAdv = this.state.gridColumnApi.getColumnState();
      window.filterStateAdv = this.gridApi.getFilterModel();
    }
    if (
      this.state.drilldownVal == 'techProd' &&
      (this.state.chartType == 1352 || this.state.chartType == 1363)
    ) {
      const wstartdate = this.state.weekStart;
      const wenddate = this.state.weekEnd;

      this.props.history.push({
        pathname: '/SearchByRO',
        state: {
          ronumber: params.value,
          pageType: 'technician',
          // month_year: this.state.selectedMonthYear,
          month_year: this.props.month_year
            ? this.props.month_year
            : this.props.monthSelected
            ? this.props.monthSelected
            : this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.monthSelected
            ? this.props.history.location.state.monthSelected
            : this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.month_year
            ? this.props.history.location.state.month_year
            : this.state.selectedMonthYear,

          techNo: this.state.techNo,
          isFromTechnician: this.state.isFromTechnician,
          serviceAdvisor: this.state.serviceAdvisor,
          type: this.state.type,
          techName: this.props.techName,
          parent: this.props.parent,
          selectedReportType: this.props.selectedReportType,
          reportTabSelection: this.props.reportTabSelection,
          comparisonMonth1: this.props.comparisonMonth1,
          comparisonMonth2: this.props.comparisonMonth2,
          // monthSelected: this.props.monthSelected,
          monthSelected: this.props.monthSelected
            ? this.props.monthSelected
            : this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.monthSelected
            ? this.props.history.location.state.monthSelected
            : this.props.monthSelected,
          chartLoc:
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.chartLoc
              ? this.props.history.location.state.chartLoc
              : this.props.chartLoc,
          prevPath:
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.prevPath
              ? this.props.history.location.state.prevPath
              : this.props.prevPath,
          payType: this.state.payType,
          prevMenu: this.state.prevMenu,
          chartType: this.state.chartType,
          isFrom: this.state.isFromTechnician ? 'techcomparison' : '',
          drilldownVal: this.state.drilldownVal,
          weekStart: this.state.weekStart,
          weekEnd: this.state.weekEnd
        }
      });
    } else {
      this.props.history.push({
        pathname: '/SearchByRO',
        state: {
          ronumber: params.value,
          pageType: 'technician',
          // month_year: this.state.selectedMonthYear,
          month_year: this.props.month_year
            ? this.props.month_year
            : this.props.monthSelected
            ? this.props.monthSelected
            : // : this.props.month_year
            // ? this.props.month_year
            this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.monthSelected
            ? this.props.history.location.state.monthSelected
            : this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.month_year
            ? this.props.history.location.state.month_year
            : this.state.selectedMonthYear,

          techNo: this.state.techNo,
          isFromTechnician: this.state.isFromTechnician,
          serviceAdvisor: this.state.serviceAdvisor,
          type: this.state.type,
          techName: this.props.techName,
          parent: this.props.parent,
          selectedReportType: this.props.selectedReportType,
          reportTabSelection: this.props.reportTabSelection,
          comparisonMonth1: this.props.comparisonMonth1,
          comparisonMonth2: this.props.comparisonMonth2,
          // monthSelected: this.props.monthSelected,
          monthSelected: this.props.month_year
            ? this.props.month_year
            : this.props.monthSelected
            ? this.props.monthSelected
            : this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.monthSelected
            ? this.props.history.location.state.monthSelected
            : this.props.monthSelected,
          chartLoc:
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.chartLoc
              ? this.props.history.location.state.chartLoc
              : this.props.chartLoc,
          prevPath:
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.prevPath
              ? this.props.history.location.state.prevPath
              : this.props.prevPath,
          payType: this.state.payType,
          prevMenu: this.state.prevMenu,
          chartType: this.state.chartType,
          isFrom: this.state.isFromTechnician ? 'techcomparison' : ''
        }
      });
    }
  };
  getTotalsForDisplay = data => {
    var rowData = data ? data : this.state.rowData;
    var soldHours = 0;
    var lbrSale = 0;
    var lbrCost = 0;
    var prtsSale = 0;
    var prtsCost = 0;
    var jobCount = 0;
    var lbrGPPerc = 0;
    var prtsGPPerc = 0;
    var roCount = 0;
    var ronumberArr = [];
    const closedDateMap = new Map();

    const ronumberSet = new Set();
    if (rowData.length > 0) {
      jobCount = rowData.length;
      rowData.map(item => {
        soldHours += Number(item.lbrsoldhours);
        lbrSale += Number(item.lbrsale);
        lbrCost += Number(item.lbrcost);
        prtsSale += Number(item.prtssale);
        prtsCost += Number(item.prtscost);
        ronumberArr.push(item.ronumber);
      });
      ronumberArr.map(obj => {
        if (obj) {
          ronumberSet.add(obj);
        }
      });
      rowData.forEach(item => {
        const { closeddate, ronumber } = item;
        if (!closedDateMap.has(closeddate)) {
          closedDateMap.set(closeddate, new Set());
        }
        closedDateMap.get(closeddate).add(ronumber);
      });
      let totalDistinctRonumberCount = 0;
      closedDateMap.forEach(set => {
        totalDistinctRonumberCount += set.size; // Add the size of the Set (distinct ronumbers)
      });
      //var roCount = ronumberSet.size;
      var roCount = totalDistinctRonumberCount;
      lbrGPPerc = lbrSale - lbrCost;
      prtsGPPerc = prtsSale - prtsCost;
    }

    this.setState({
      roCount: roCount,
      jobCount: jobCount,
      soldHours: soldHours,
      lbrSale: lbrSale,
      lbrCost: lbrCost,
      prtsSale: prtsSale,
      prtsCost: prtsCost,
      lbrGPPerc: lbrGPPerc,
      prtsGPPerc: prtsGPPerc
    });
  };
  onColumnVisible = params => {
    const columnApi = params.columnApi || this.gridColumnApi;

    if (!columnApi) {
      console.error('columnApi is undefined');
      return;
    }

    const allColumnsHidden = columnApi
      .getAllColumns()
      .every(col => !col.isVisible());

    if (allColumnsHidden) {
      this.setState({ showSummaryValues: false });
    } else {
      this.setState({ showSummaryValues: true });
    }
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });

    this.getTotalsForDisplay(rowData);
  };
  formatTotals = value => {
    if (value != null && value != 0) {
      return Math.sign(value) > -1
        ? '$' +
            parseFloat(value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  getRowStyle = params => {
    if (
      this.state.type == 'serviceadvisorlevel' &&
      params.data.advisorActive == 0
    ) {
      return { background: 'rgb(221, 234, 244)' };
    } else if (
      this.state.type == 'technicianlevel' &&
      params.data.lbrtechActive == 0
    ) {
      return { background: 'rgb(221, 234, 244)' };
    } else {
      return { background: 'rgb(255, 255, 255)' };
    }
  };
  render() {
    const { classes } = this.props;

    return (
      <div>
        <div>
          <Paper square style={{ marginLeft: 8, marginRight: 8 }}>
            <Toolbar>
              {(this.state.type == 'serviceadvisorlevel' ||
                this.state.type == 'technicianlevel') &&
              this.state.serviceAdvisor != '' &&
              (this.props.month_year != undefined ||
                this.props.month_year == '') &&
              this.props.techNo != '' &&
              this.props.parent != undefined ? (
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={this.renderBackButton}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              ) : (
                <Link></Link>
              )}
              <Grid container justify="flex-start" style={{ padding: '5px' }}>
                {/* <div style={{display:'flex'}}> */}
                {this.state.drilldownVal == 'techProd' &&
                (this.state.chartType == 1352 ||
                  this.state.chartType == 1363) ? (
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={classes.formControl}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ paddingRight: '5px', backgroundColor: '#fff' }}
                    >
                      Week Start Date
                    </InputLabel>

                    <Select
                      variant="outlined"
                      label="Group By"
                      name="group-by-type"
                      value={this.state.weekStart}
                      // onChange={this.handleMonthYearChange}
                    >
                      {/* <MenuItem value={this.state.selectedMonthYear}>
                      {this.state.selectedMonthYear}
                    </MenuItem> */}
                      {/* {typeof this.state.opcodeMonthYear != 'undefined' &&
                        Object.values(this.state.opcodeMonthYear).map(item => ( */}
                      <MenuItem value={this.state.weekStart}>
                        {moment(this.state.weekStart).format('MM-DD-YY')}
                      </MenuItem>
                      {/* ))} */}
                    </Select>
                  </FormControl>
                ) : (
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={classes.formControl}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                    >
                      Month
                    </InputLabel>

                    <Select
                      variant="outlined"
                      label="Group By"
                      name="group-by-type"
                      value={this.state.selectedMonthYear}
                      onChange={this.handleMonthYearChange}
                    >
                      {/* <MenuItem value={this.state.selectedMonthYear}>
                      {this.state.selectedMonthYear}
                    </MenuItem> */}
                      {typeof this.state.opcodeMonthYear != 'undefined' &&
                        Object.values(this.state.opcodeMonthYear).map(item => (
                          <MenuItem value={item}>
                            {moment(item).format('MMM-YY')}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                )}
                {/* </div> */}
                {}
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      paddingTop: 17,
                      marginLeft: 'auto',
                      cursor: 'pointer'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip>
              </Grid>
            </Toolbar>
          </Paper>
          {this.state.showSummaryValues == true && (
            <Paper className={classes.paperTotal}>
              {' '}
              <div className={classes.containerTotal}>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    RO Count :
                    <span style={{ color: '#003d6b', marginLeft: 3 }}>
                      {this.state.roCount
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  </Typography>
                </Button>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Job Count :
                    <span style={{ color: '#003d6b', marginLeft: 3 }}>
                      {this.state.jobCount
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  </Typography>
                </Button>

                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Hours Sold :
                    <span style={{ color: '#003d6b', marginLeft: 3 }}>
                      {this.state.soldHours
                        .toFixed(2)
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  </Typography>
                </Button>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor Sale :
                    <span style={{ color: '#003d6b', marginLeft: 3 }}>
                      {this.formatTotals(this.state.lbrSale)}
                    </span>
                  </Typography>
                </Button>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts Sale :
                    <span style={{ color: '#003d6b', marginLeft: 6 }}>
                      {this.formatTotals(this.state.prtsSale)}
                    </span>
                  </Typography>
                </Button>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor Cost :
                    <span style={{ color: '#003d6b', marginLeft: 6 }}>
                      {this.formatTotals(this.state.lbrCost)}
                    </span>
                  </Typography>
                </Button>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts Cost :
                    <span style={{ color: '#003d6b', marginLeft: 6 }}>
                      {this.formatTotals(this.state.prtsCost)}
                    </span>
                  </Typography>
                </Button>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor GP :
                    <span style={{ color: '#003d6b', marginLeft: 6 }}>
                      {this.formatTotals(this.state.lbrGPPerc)}
                    </span>
                  </Typography>
                </Button>
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts GP :
                    <span style={{ color: '#003d6b', marginLeft: 6 }}>
                      {this.formatTotals(this.state.prtsGPPerc)}
                    </span>
                  </Typography>
                </Button>
              </div>
            </Paper>
          )}
        </div>

        {this.state.isLoading && (
          <div className={classes.loader}>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              this.state.isFromTechnician == false
                ? window.innerHeight - 330 + 'px'
                : window.innerHeight - 280 + 'px',
            //height: '430px',
            // width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            getRowStyle={this.getRowStyle}
            suppressDragLeaveHidesColumns={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={
              this.state.drilldownVal == 'techProd' &&
              (this.state.chartType == 1352 || this.state.chartType == 1363)
                ? this.state.columnDefWeekly
                : this.state.columnDefs
            }
            defaultColDef={this.state.defaultColDef}
            enableRangeSelection={true}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            headerHeight={this.state.headerHeight}
            tooltipShowDelay={0}
            sideBar={this.state.sideBar}
            floatingFilter={true}
            suppressRowClickSelection={true}
            onFilterChanged={this.onFilterChanged}
            suppressContextMenu={true}
            onColumnVisible={this.onColumnVisible}
            onFirstDataRendered={this.onFirstDataRendered}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    marginLeft: '7px',
    minWidth: 106
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  containerTotal: {
    marginTop: '6px'
  },
  paperTotal: {
    marginLeft: '8px',
    marginRight: '8px'
  }
});

export default withStyles(styles)(TechnicianDetailedView);
