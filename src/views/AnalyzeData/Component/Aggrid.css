#data-tab-tech-summary .ag-header-cell-label {
    text-overflow: clip;
    overflow: visible;
    white-space: normal;
}
#data-tab-tech-summary .ag-header-cell-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}
.ag-header-cell-label {
    text-overflow: clip;
    overflow: visible;
    white-space: normal;
    justify-content: center;
}
.ag-header-cell-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}

.ag-theme-balham .ag-side-buttons {
    padding-top: 16px;
    width: 31px;
    position: relative;
    color: #000;
    overflow: hidden;
}
/* .ag-theme-balham .ag-cell {
    min-width: 110px;
} */
.ag-header-cell-label .ag-header-icon.ag-sort-order {
    display: none
}
span.ag-header-icon.ag-header-label-icon.ag-filter-icon {
   display: none !important;
}

.ag-theme-balham .ag-icon {
    margin-right: 3px;
}
.grid-cell-centered {
    text-align: center;
}
#grid-theme-wrapper.yellow-theme .ag-theme-balham .ag-row-selected {
    /* background-color: rgba(220, 53, 69, 0.3); */
    /* background-color: rgba(238, 238, 0, 1); */
    background-color: rgba(255,255,102);
}
.ag-cell-wrapper.ag-row-group {
    align-items: flex-start;
    font-weight: normal;
}
/* #reportsHeader.ag-header-select-all.ag-labeled.ag-label-align-right.ag-checkbox.ag-input-field{
    margin-top: -24px;
} */
#itemizationHeader {
    /* writing-mode: vertical-rl; */
    word-break: break-all;
    /* font-size: 9px; */
    font-size: 11px;
    font-weight: bold;
}
#itemizationRows {
    font-size: 11px;
}
#competitiveCircle {
    background-color: #4ec1ee;
    opacity: 0.7;
    color: #4ec1ee;
    line-height: 0px;
}

#maintenanceSquare {
    background-color: #f40c22;;
    opacity: 0.4;
    color: #f40c22;;
    line-height: 0px;
}
#repairSquare {
    background-color: #049372;
    opacity: 0.6;
    color: #30bf6f;;
    transform: rotate(-45deg);
    line-height: 0px;
}
#itemizationSelectHeader {
    font-size: 11px;
}
#ItemizationGrid .ag-row-selected,#data-tab-reports .ag-row-selected {
    background-color: #D3D3D3 !important;
}
#ItemizationGrid .ag-row-selected .ag-cell.ag-cell-not-inline-editing.ag-cell-auto-height, 
#data-tab-reports .ag-row-selected .ag-cell.ag-cell-not-inline-editing.ag-cell-auto-height {
    text-decoration-line: line-through;
}
/* #Itemization .ag-ltr .ag-cell{
    font-size: 11px;
} */
#ItemizationGrid .ag-ltr .ag-cell{
    font-size: 9px;
}
#ItemizationGrid .ag-checkbox .ag-input-wrapper,#data-tab-reports .ag-checkbox .ag-input-wrapper {
    font-size: 11px;
    width: 10px;
    height: 12px;
    line-height: 12px;
}
#ItemizationGrid .ag-body-viewport {
    overflow-x: overlay;
}
#ItemizationGrid .ag-header-icon.ag-header-cell-menu-button {
    display: none;
}
.ag-tool-panel-wrapper {
    width: 153px;
}
