import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import clsx from 'clsx';
import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Toolbar,
  Typography,
  Tooltip,
  Button
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import { processCells } from 'src/components/ViewGraphDetailsAction';

import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  getDrillDownDataByOpcode,
  getDrillDownMonthYearByOpcode,
  getOpcodeDetailedViewMonthYears
} from 'src/utils/hasuraServices';
import { getLast13Months } from 'src/utils/Utils';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import moment from 'moment';
import { traceSpan } from 'src/utils/OTTTracing';
import { ReactSession } from 'react-client-session';
import TooltipRenderer from './TooltipRenderer';
import OpcodeCellRenderer from './OpcodeCellRenderer.js';
import $ from 'jquery';

var lodash = require('lodash');
class ROLevelRawDataGrid extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    month_year: '2019-10',
    opcode: '30',
    isLabor: false
  };
  componentWillMount() {
    console.log('enter=000');
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  // componentDidUpdate(prevProps,prevState) {
  //   console.log("selected=dd=1=");
  //   if(ReactSession.get("selectedStoreId") != undefined) { console.log("selected=dd=12345=");
  //     var checkSt = lodash.isEqual(
  //       this.state.store,
  //       ReactSession.get("selectedStoreId")
  //     );
  //     console.log("selected=dd=",ReactSession.get("selectedStoreId"),checkSt,localStorage.getItem('selectedStoreId'),this.state.store);
  //     if(checkSt == false ) {  console.log("selected=dd=123456=");
  //       this.getAgGridData(this.state.opcode, this.state.queryMonth);
  //       this.setState({ store: localStorage.getItem('selectedStoreId') });
  //       this.setState({ isLoading: true });
  //     }
  //   }
  // }
  componentDidMount() {
    this.getOpcodeDetailedViewMonthYear();
  }
  componentDidUpdate() {
    var opcode =
      this.props.isFrom == 'workmix' || this.props.parent == undefined
        ? ''
        : this.props.opcode;
    var queryMonth =
      typeof this.props.month_year === 'object' ||
      this.props.isFrom == 'workmix' ||
      this.props.parent == undefined
        ? getLast13Months()[12]
        : this.props.month_year;

    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        var opcode =
          this.props.isFrom == 'workmix' || this.props.parent == undefined
            ? ''
            : this.props.opcode;
        var queryMonth = getLast13Months()[12];
        window.filterStateDetail = {};
        window.sortState = {};
        if (this.state.gridColumnApi) {
          this.state.gridColumnApi.resetColumnState();
        }
        // typeof this.props.month_year === 'object' ||
        // this.props.isFrom == 'workmix' ||
        // this.props.parent == undefined
        //   ? getLast13Months()[12]
        //   : this.props.month_year;
        this.getAgGridData('', queryMonth);
        console.log(
          'selected=dd=123456=',
          opcode,
          this.state.opcode,
          this.state.queryMonth,
          queryMonth
        );
        $('.bck-btn').css('display', 'none');
        this.setState({ selectedMonthYear: queryMonth });

        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ isLoading: true });
      }
    }
    if (this.state.resetReport != this.props.resetReport) {
      this.resetRawData();
      this.getAgGridData(this.state.opcode, this.state.queryMonth);
    }
  }

  constructor(props) {
    super(props);
    var monthYear =
      this.props.month_year != 'undefined'
        ? this.props.month_year
        : this.props.history &&
          this.props.history.location.state &&
          this.props.parent != 'workmix' &&
          this.props.history.location.state.y &&
          this.props.isFrom != '' &&
          this.props.parent != '' &&
          this.props.pageType != ''
        ? this.props.history.location.state.y
        : typeof this.props.month_year === 'object' ||
          this.props.isFrom == 'workmix' ||
          this.props.parent == undefined ||
          (this.props.isFrom == '' &&
            this.props.parent == '' &&
            this.props.pageType == '')
        ? getLast13Months()[12]
        : this.props.month_year;

    var queryMonth =
      this.props.month_year != 'undefined'
        ? this.props.month_year
        : this.props.history &&
          this.props.history.location.state &&
          this.props.parent != 'workmix' &&
          this.props.history.location.state.y &&
          this.props.isFrom != '' &&
          this.props.parent != '' &&
          this.props.pageType != '' &&
          this.props.history.location.state.pageType &&
          this.props.history.location.state.pageType != 'searchByRo'
        ? this.props.history.location.state.y
        : typeof this.props.month_year === 'object' ||
          this.props.isFrom == 'workmix' ||
          this.props.parent == undefined ||
          (this.props.isFrom == '' &&
            this.props.parent == '' &&
            this.props.pageType == '')
        ? getLast13Months()[12]
        : this.props.month_year;
    // var opcode =
    //   this.props.isFrom == 'workmix' || this.props.parent == undefined
    //     ? ''
    //     : (this.props.isFrom == 'opcodes' ||
    //         (this.props.isFrom == 'opcodelevel' &&
    //           this.props.parent != 'comparisonchart' &&
    //           this.props.parent != 'workmix')) &&
    //       this.props.history &&
    //       this.props.history.location.state &&
    //       this.props.parent != '' &&
    //       this.props.selectedOpcodeAll != 'All' &&
    //       this.props.selectedOpcodeAll != undefined
    //     ? this.props.history.location.state &&
    //       this.props.history.location.state.opcode
    //     : this.props.isFrom == 'opcodes' &&
    //       this.props.history &&
    //       this.props.history.location.state &&
    //       this.props.parent != '' &&
    //       this.props.selectedOpcodeAll != 'All'
    //     ? this.props.history.location.state &&
    //       this.props.history.location.state.opcode
    //         : this.props.opcode;

    var opcode =
      this.props.isFrom == 'workmix' || this.props.parent == undefined
        ? ''
        : (this.props.isFrom == 'opcodes' ||
            this.props.isFrom == 'noncpopcodes' ||
            (this.props.isFrom == 'opcodelevel' &&
              this.props.parent != 'comparisonchart' &&
              this.props.parent != 'workmix')) &&
          this.props.history &&
          this.props.history.location.state &&
          this.props.parent != '' &&
          this.props.selectedOpcodeAll != 'All' &&
          this.props.selectedOpcodeAll != undefined
        ? this.props.history.location.state.opcode
        : this.props.opcode;

    var isLabor = this.props.isLabor;
    var realm = this.props.realm;
    var reportTypeWorkmix = this.props.reportTypeWorkmix
      ? this.props.reportTypeWorkmix
      : '';
    var workmixParent =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.workmixParent
        : this.props.workmixParent
        ? this.props.workmixParent
        : '';
    var workmixTab =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.workmixTab
        : this.props.workmixTab
        ? this.props.workmixTab
        : '';
    var selectedReportType =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.selectedReportType
        : this.props.selectedReportType
        ? this.props.selectedReportType
        : '';
    var opCategory =
      this.props.history && this.props.history.location.state
        ? this.props.history.location.state.opCategory
        : '';
    var prevMenu =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.prevMenu
        ? this.props.history.location.state.prevMenu
        : this.props.prevMenu;
    let storeId = JSON.parse(localStorage.getItem('selectedStoreId'));
    console.log('storeId', storeId);

    this.state = {
      originData: this.props.originData
        ? this.props.originData
        : this.props &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.originData
        ? this.props.history.location.state.originData
        : '',
      queryMonth: queryMonth,
      resetReport: this.props.resetReport,
      opcode: opcode,
      isLoading: true,
      isLabor: isLabor,
      selectedMonthYear: monthYear,
      reportTypeWorkmix: reportTypeWorkmix,
      workmixParent: workmixParent,
      workmixTab: workmixTab,
      selectedReportType: selectedReportType,
      opCategory: opCategory,
      ROCount: 0,
      jobCount: 0,
      soldHours: 0,
      lbrSale: 0,
      lbrCost: 0,
      prtsSale: 0,
      prtsCost: 0,
      lbrGP: 0,
      prtsGP: 0,
      lbrGPPerc: 0,
      prtsGPPerc: 0,
      monthYearsArr: [],
      prevPath: this.props.prevPath,
      prevMenu: prevMenu,
      columnDefs: [
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 90,
          flex: 1,
          minWidth: 90,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true,
          hide: false,

          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Advisor',
          field: 'advisorName',
          minWidth: 140,
          width: 140,
          flex: 1,
          chartDataType: 'string',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'advisorName',
          valueFormatter: this.formatCellValueDescription,
          cellRenderer: function(params) {
            return `<div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${
              params.value ? params.value : ''
            }</div>`;
          },
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Tech',
          field: 'techName',
          minWidth: 140,
          width: 140,
          flex: 1,
          chartDataType: 'string',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'techName',
          cellRenderer: function(params) {
            return `<div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${
              params.value ? params.value : ''
            }</div>`;
          },
          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: (valueA, valueB) => {
            const trimmedA = valueA ? valueA.trimStart() : '';
            const trimmedB = valueB ? valueB.trimStart() : '';
            if (trimmedA < trimmedB) return -1;
            if (trimmedA > trimmedB) return 1;
            return 0;
          }
        },
        {
          headerName: 'Opcode',
          field: 'lbropcode',
          width:
            realm == 'ferrarioat_store' || realm == 'fisherhonda' ? 100 : 89,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          width: 97,
          minWidth: 97,
          filter: 'agSetColumnFilter',

          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          cellRenderer: 'OpcodeCellRenderer',
          cellRendererParams: {
            rendererType: 'DetailedView',
            summaryTab: this.handleEditClicked
          },
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Description',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          width: 200,
          minWidth: 200,
          flex: 1,
          chartDataType: 'category',
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', overflow: 'hidden' };
          },
          filterValueGetter: params => {
            const value = params.data.lbropcodedesc;
            return value?.length > 25 ? value.substring(0, 25) + '...' : value; // Show trimmed text
          }
        },
        // {
        //   headerName: 'RO',
        //   // filter: 'agNumberColumnFilter',
        //   field: 'ronumber',
        //   width: 83,
        //   chartDataType: 'category',
        //   showRowGroup: false,

        //   cellRenderer: 'agGroupCellRenderer',
        //   onCellClicked: this.handleSearchByRo,
        //   cellStyle: function () {
        //     return {
        //       color: '#000000',
        //       fontWeight: 'bold',
        //       cursor: 'pointer',
        //       border: ' 0px white'
        //     };
        //   },
        //   cellClass: 'textAlign',
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        {
          headerName: 'RO',
          field: 'ronumber',
          // width: 83,
          flex: 1,
          // minWidth: 83,
          width: 85,
          minWidth: 85,
          chartDataType: 'category',
          cellClass: 'textAlign',
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          onCellClicked: this.handleSearchByRo,
          suppressMenu: true,
          unSortIcon: true,

          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              cursor: 'pointer',
              // marginRight: '25px',
              // paddingLeft: '0px',
              textAlign: 'center',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Open Date',
          field: 'roOpendate',
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Category',
          field: 'opcategory',
          width: 110,
          flex: 1,
          minWidth: 110,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 55,
        //   sortable: false,
        //   filter: false,
        //   suppressMenu: true,

        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   filter: 'agSetColumnFilter',
        //   filterParams: {
        //     values: ['N/A', '']
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'ELR Dominant',
          // filter: 'agNumberColumnFilter',
          field: 'dominantElr',
          width: 107,
          minWidth: 107,
          flex: 1,
          hide: isLabor == true ? false : true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressToolPanel: isLabor == true ? false : true,
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'ELR Average',
          // filter: 'agNumberColumnFilter',
          field: 'elr',
          minWidth: 97,
          width: 97,
          flex: 1,
          hide: isLabor == true ? false : true,
          chartDataType: 'series',
          suppressToolPanel: isLabor == true ? false : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '$ ELR Variance',
          // filter: 'agNumberColumnFilter',
          field: 'elrVariance',
          flex: 1,
          width: 102,
          minWidth: 102,
          hide: isLabor == true ? false : true,
          suppressToolPanel: isLabor == true ? false : true,
          chartDataType: 'series',

          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              color: params.value > 0 ? 'green' : params.value < 0 ? 'red' : ''
            };
          },
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '% ELR Variance',
          field: 'elrVariancePerc',
          width: 90,
          minWidth: 90,
          // flex: 1,

          chartDataType: 'series',
          hide: isLabor == true ? false : true,
          suppressToolPanel: isLabor == true ? false : true,
          valueFormatter: this.formatCellValuePercent,
          cellStyle: this.cellStyles,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: 80,
          flex: 1,
          minWidth: 80,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type Group',
          field: 'paytypegroup',
          width: 80,
          minWidth: 80,
          chartDataType: 'string',
          suppressMenu: true,
          flex: 1,

          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'center' };
          }
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          width: 80,
          flex: 1,
          minWidth: 80,
          chartDataType: 'series',
          hide: isLabor == true ? false : true,

          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          suppressToolPanel: isLabor == true ? false : true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Tech Hours',
          field: 'lbrtechhours',
          width: 80,
          flex: 1,
          minWidth: 80,
          chartDataType: 'series',
          hide: isLabor == true ? true : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,

          cellClass: 'twoDecimalPlacesWithOut$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          suppressMenu: true,
          unSortIcon: true
        },
        {
          //headerName: isLabor == true ? 'Labor Sale' : 'Parts Sale',
          headerName: 'Labor Sale',
          field: 'lbrsale',
          width: 80,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          flex: 1,
          minWidth: 80,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          width: 80,
          flex: 1,
          minWidth: 80,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtextendedsale',
          width: 80,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          flex: 1,
          minWidth: 80,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtextendedcost',
          width: 80,
          flex: 1,
          minWidth: 80,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        // {
        //   headerName: this.props.isLabor == true ? 'Labor GP' : 'Parts GP',
        //   field: 'grossprofit',
        //   width: 80,
        //   minWidth: 80,
        //   flex: 1,
        //   chartDataType: 'series',
        //   cellStyle: this.cellStyles,
        //   valueFormatter: this.formatCellValue,
        //   cellClass: 'twoDecimalPlacesWith',
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   suppressMenu:true,
        //   unSortIcon: true
        // },
        {
          headerName: 'Labor GP',
          // filter: 'agNumberColumnFilter',
          field: 'lbrgrossprofit',
          width: 80,
          flex: 1,
          minWidth: 80,
          //hide: isLabor == true ? false : true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          //suppressToolPanel: isLabor == true ? false : true,
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor GP%',
          // filter: 'agNumberColumnFilter',
          field: 'lbrGrossprofitpercentage',
          width: 80,
          flex: 1,
          minWidth: 80,
          //hide: isLabor == true ? false : true,
          // suppressToolPanel: isLabor == true ? false : true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Parts GP',
          // filter: 'agNumberColumnFilter',
          field: 'prtsgrossprofit',
          width: 80,
          flex: 1,
          minWidth: 80,
          //hide: isLabor == true ? true : false,
          //  suppressToolPanel: isLabor == true ? true : false,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },

          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts GP%',
          // filter: 'agNumberColumnFilter',
          field: 'prtsGrossprofitpercentage',
          width: 80,
          flex: 1,
          minWidth: 80,
          // hide: isLabor == true ? true : false,
          //  suppressToolPanel: isLabor == true ? true : false,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          pivot: this.props.isLabor == true ? false : true,
          valueFormatter: this.formatCellValueGP,
          cellClass: 'oneDecimalPlace',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Markup Average',
          // filter: 'agNumberColumnFilter',
          field: 'markup',
          width: 95,
          flex: 1,
          minWidth: 95,
          suppressToolPanel: isLabor == true ? true : false,
          hide: isLabor == true ? true : false,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,

          unSortIcon: true
        },

        {
          headerName: 'Markup Dominant',
          // filter: 'agNumberColumnFilter',
          field: 'dominantMarkup',
          width: 107,
          minWidth: 107,
          flex: 1,
          suppressToolPanel: isLabor == true ? true : false,
          hide: isLabor == true ? true : false,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,

          unSortIcon: true
        },
        {
          headerName: 'Markup Variance',
          // filter: 'agNumberColumnFilter',
          field: 'markupVariance',
          width: 100,
          minWidth: 100,
          flex: 1,
          suppressToolPanel: isLabor == true ? true : false,
          hide: isLabor == true ? true : false,
          chartDataType: 'series',

          cellStyle: function(params) {
            return {
              textAlign: 'right',
              border: ' 0px white',
              color: params.value > 0 ? 'green' : params.value < 0 ? 'red' : ''
            };
          },
          valueFormatter: this.formatCellValueMarkup,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: '% Markup Variance',
          field: 'markupVariancePerc',
          width: 103,
          minWidth: 103,
          // flex: 1,
          chartDataType: 'series',
          hide: isLabor == true ? true : false,
          suppressToolPanel: isLabor == true ? true : false,
          valueFormatter: this.formatCellValuePercentMarkup,
          cellClass: 'twoDecimalPlacesWith$',

          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellStyle: this.cellStyles,
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      rowData: [],
      headerHeight: 45,
      frameworkComponents: {
        OpcodeCellRenderer: OpcodeCellRenderer
      },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      // pivotMode: true,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        suppressMovable: false
        // resizable: true
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' }
        },
        {
          id: 'oneDecimalPlace',
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'fourDecimalPlaces',
          numberFormat: { format: '###0.0000' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  filterByOpCategory = () => {
    var opCatFilterComponent = this.state.rawGridApi.getFilterInstance(
      'opcategory'
    );

    opCatFilterComponent.setModel({
      type: 'set',
      values: ['COMPETITIVE', 'MAINTENANCE', 'REPAIR']
    });
    this.state.rawGridApi.onFilterChanged();
  };

  getTotalsForDisplay = (data, isFiltered) => {
    var rowData = data ? data : this.state.rowData;
    var soldHours = 0;
    var lbrSale = 0;
    var lbrCost = 0;
    var prtsSale = 0;
    var prtsCost = 0;
    var roCount = 0;
    var ronumberArr = [];
    const ronumberSet = new Set();
    var lbrGP = 0;
    var prtsGP = 0;
    var lbrGPPerc = 0;
    var prtsGPPerc = 0;
    var totalRo = 0;
    var totalRoCust = 0;
    const closedDateMap = new Map();
    if (rowData.length > 0) {
      rowData.map(item => {
        soldHours += Number(item.lbrsoldhours);
        lbrSale += Number(item.lbrsale);
        lbrCost += Number(item.lbrcost);
        prtsSale += Number(item.prtextendedsale);
        prtsCost += Number(item.prtextendedcost);

        ronumberArr.push(item.ronumber);
      });

      rowData.forEach(item => {
        if (this.state.opcode == '') {
          const { closeddate, ronumber } = item;
          //  Check if closeddate already exists in the Map
          if (!closedDateMap.has(closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(closeddate).add(ronumber);
        } else {
          // if (
          //   !(
          //     this.props.workmixParent != 'comparisonchart' &&
          //     item.lbrsale == 0 &&
          //     item.prtextendedsale == 0 &&
          //     item.lbrsoldhours == 0 &&
          //     item.prtextendedcost == 0
          //   )
          // ) {
          const { closeddate, ronumber } = item;
          //  Check if closeddate already exists in the Map
          if (!closedDateMap.has(closeddate)) {
            // If not, initialize a new Set for this closeddate
            closedDateMap.set(closeddate, new Set());
          }
          // Add the ronumber to the Set corresponding to this closeddate
          closedDateMap.get(closeddate).add(ronumber);
        }
        //}
      });

      // Calculate total distinct ronumber count across all unique closeddates
      let totalDistinctRonumberCount = 0;

      closedDateMap.forEach(set => {
        totalDistinctRonumberCount += set.size; // Add the size of the Set (distinct ronumbers)
      });
      // totalRo = Number(rowData[0].totRoCount);
      // ronumberArr.map(obj => {
      //   if (obj) {
      //     ronumberSet.add(obj);
      //   }
      // });
      if (this.state.rowData.length == data.length) {
        totalRoCust = Number(rowData[0].custRoCount);
      } else {
        // totalRoCust = ronumberSet.size;
        totalRoCust = totalDistinctRonumberCount;
      }
      // var roCount1 = ronumberSet.size;
      var roCount1 = totalDistinctRonumberCount;
      lbrGP = lbrSale - lbrCost;
      prtsGP = prtsSale - prtsCost;

      // if (lbrSale !== 0) {
      //   lbrGPPerc = (lbrGP / lbrSale) * 100;
      // } else {
      //   lbrGPPerc = 0;
      // }
      // prtsGPPerc = (prtsGP / prtsSale) * 100;
      lbrGPPerc = this.calculateGPPerc(lbrGP, lbrSale);
      prtsGPPerc = this.calculateGPPerc(prtsGP, prtsSale);
    }
    this.setState({
      // ROCount: roCount1,
      soldHours: soldHours,
      lbrSale: lbrSale,
      lbrCost: lbrCost,
      prtsSale: prtsSale,
      prtsCost: prtsCost,
      lbrGP: lbrGP,
      prtsGP: prtsGP,
      lbrGPPerc: isNaN(lbrGPPerc) ? 0 : lbrGPPerc,
      prtsGPPerc: isNaN(prtsGPPerc) ? 0 : prtsGPPerc,
      jobCount: ronumberArr.length
    });

    if (lodash.isEmpty(this.state.filterArr) || this.state.opcode) {
      this.setState({
        ROCount: roCount1
      });
    } else {
      this.setState({
        ROCount: totalRoCust
      });
    }
  };
  calculateGPPerc = (GP, sale) => {
    if (sale !== 0) {
      return (GP / sale) * 100;
    } else {
      return 0;
    }
  };
  formatCellValueDescription = params => {
    if (params.value != null && params.value != 0) {
      if (params.value.length > 20) {
        const data = params.value.substring(0, 20) + `...`;
        return data;
      }
    }
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    this.setState({ filterArr: filterValues });
    let filterArr;
    let rowData = this.state.rowData;
    // console.log('filterValues==', rowData);
    let pointer = this;

    if (!lodash.isEmpty(filterValues)) {
      Object.keys(filterValues).forEach(function(key) {
        if (filterValues[key].values.length > 0) {
          filterArr = lodash.filter(rowData, function(o) {
            return filterValues[key].values.includes(o[key]);
          });
          rowData = filterArr;
          ///  console.log('filterValues==11', rowData);
          pointer.setState({ filteredRowData: rowData });
          pointer.getTotalsForDisplay(rowData, true);
        }
      });
    } else {
      // console.log('filterValues==22', rowData);
      pointer.getTotalsForDisplay(rowData, true);
    }
    //this.setDrillDownValuesToState(DrilldownValues);
    // this.setDrillDownValuesToState(DrilldownValues);
  };
  // onFilterChanged = e => {
  //   const filterValues = e.api.getFilterModel();
  //   let rowData = [];
  //   this.gridApi.forEachNodeAfterFilter(node => {
  //     rowData.push(node.data);
  //   });
  //   this.getTotalsForDisplay(rowData, true);
  // };
  formatTotals = value => {
    if (value != null && value != 0) {
      return Math.sign(value) > -1
        ? '$' +
            parseFloat(value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueGPPerc = value => {
    if (value != null && value != 0) {
      return (
        parseFloat(value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0%';
    }
  };
  resetRawData = () => {
    this.setState({ resetReport: this.props.resetReport });
    this.props.handleResetReport();
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    window.filterStateDetail = {};
    window.sortState = {};
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0%';
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    } else {
      return '0.0000';
    }
  };
  formatCellValuePercent = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else if (params.value == undefined) {
      return null;
    } else {
      return '0.00%';
    }
  };
  formatCellValuePercentMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value).toFixed(2) + '%';
    } else if (params.value == undefined) {
      return null;
    } else {
      return '0.00%';
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'Opcode Detailed View',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Opcode Detailed View' },
            mergeAcross: 3
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title:
        'Opcode Detailed View -' +
        (this.state.reportTypeWorkmix ? this.state.reportTypeWorkmix : ''),
      from:
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.pathname
          ? this.props.history.location.pathname
          : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to Excel', spanAttribute);
  };
  onFirstDataRendered = params => {
    if (this.gridApi) {
      this.gridApi.getFilterInstance('opcategory', filterInstance => {
        if (filterInstance) {
          const values = filterInstance.getValues();
          if (values && values.includes('N/A')) {
            filterInstance.setModel({
              filterType: 'set',
              values: values.filter(value => value !== 'N/A')
            });
            this.gridApi.onFilterChanged();
          }
        }
      });
    }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    this.gridApi = params.api;
    if (
      (this.props.parent == '' &&
        this.props.isFrom == '' &&
        this.props.pageType != 'searchByRo') ||
      (this.props.pageType == '' &&
        this.props.parent != 'searchByRo' &&
        (this.props.isFrom != 'opcodes' ||
          this.props.isFrom != 'noncpopcodes') &&
        this.props.pageType != 'searchByRo')
    ) {
      window.sortState = {};
      window.filterStateDetail = {};
    }
    if (this.props.parent != 'searchByRo') {
      this.gridApi.setSortModel(window.sortState);
      this.gridApi.setFilterModel(window.filterStateDetail);
      if (window.colStateOpcodeDetail) {
        this.state.gridColumnApi.setColumnState(window.colStateOpcodeDetail);
      }
      console.log('ropage', this.props, window.colStateOpcodeDetail);

      if (
        window.colStateOpcodeDetail &&
        ((this.props.pageType == '' &&
          this.props.prevMenu != 'Opcode Categorizations') ||
          (this.props.parent == '' &&
            this.props.isFrom == '' &&
            this.props.pageType != 'searchByRo'))
      ) {
        this.state.gridColumnApi.resetColumnState();
      }
    }
  };

  getOpcodeDetailedViewMonthYear() {
    getOpcodeDetailedViewMonthYears(result => {
      if (result) {
        // alert(this.state.queryMonth);
        this.setState({ monthYearsArr: result });
        // alert(this.state.monthYearsArr.includes(this.state.queryMonth));
        if (this.state.monthYearsArr.includes(this.state.queryMonth) == true) {
          this.setState({ loadedMonth: this.state.queryMonth });
        } else {
          this.setState({
            loadedMonth: this.state.monthYearsArr[
              this.state.monthYearsArr.length - 1
            ]
          });
        }
        this.setState({ selectedMonthYear: this.state.loadedMonth });
        this.getAgGridData(this.state.opcode, this.state.loadedMonth);
      }
    });
  }

  getAgGridData(opcode, queryMonth) {
    this.setState({ isLoading: true });
    if (this.props.isFrom == 'searchByRo') {
      opcode = '';
    }
    if (
      (this.props.isFrom == 'opcodes' || this.props.isFrom == 'noncpopcodes') &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.opcode
    ) {
      opcode = this.props.history.location.state.opcode;
    }
    let filteredResult = [];
    var payTypeSaved = localStorage.getItem('retailFlags').split(',');
    getDrillDownDataByOpcode(opcode, queryMonth, result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessCcDrilldownGetDrillDownAllRevenueOpcodeDetails
          .drillDownAllRevenueOpcodeDetailedViews
      ) {
        let data =
          result.data.statelessCcDrilldownGetDrillDownAllRevenueOpcodeDetails
            .drillDownAllRevenueOpcodeDetailedViews;
        if (
          this.props.isFrom == 'opcodelevel' &&
          this.props.parent == 'opcodes'
        ) {
          console.log('eee===1122');
          if (this.props.opcodePayType && this.props.opcodePayType != '') {
            if (
              this.props.opcodePayType == 'C' ||
              payTypeSaved.includes(this.props.opcodePayType)
            ) {
              filteredResult = data.filter(item =>
                payTypeSaved.includes(item.paytypegroup)
              );
            } else if (
              this.props.opcodePayType &&
              !payTypeSaved.includes(this.props.opcodePayType) &&
              this.props.opcodePayType != 'C' &&
              this.props.opcodePayType != 'I'
            ) {
              //if retail flag contain only C then ['E', 'M', 'W', 'F']paytypes shown as 'W' in opcode summary
              filteredResult = data.filter(item =>
                ['E', 'M', 'W', 'F'].includes(item.paytypegroup)
              );
            } else {
              filteredResult = data.filter(
                item => item.paytypegroup == this.props.opcodePayType
              );
            }
          } else {
            filteredResult = data;
          }
        } else if (
          this.props.parent == 'opcodelevel' &&
          (this.props.isFrom == 'opcodes' ||
            this.props.isFrom == 'noncpopcodes') &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.opcodePayType &&
          this.props.history.location.state.opcodePayType != ''
        ) {
          console.log('eee===222');
          if (this.props.history.location.state.opcodePayType == 'C') {
            filteredResult = data.filter(item =>
              payTypeSaved.includes(item.paytypegroup)
            );
          } else {
            filteredResult = data.filter(
              item =>
                item.paytypegroup ==
                this.props.history.location.state.opcodePayType
            );
          }
        } else if (
          this.props.parent == 'searchByRo' &&
          this.props.isFrom == 'opcodelevel' &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.payTypeRo &&
          this.props.history.location.state.payTypeRo != ''
        ) {
          console.log('eee===333');
          if (this.props.history.location.state.payTypeRo == 'C') {
            filteredResult = data.filter(item =>
              payTypeSaved.includes(item.paytypegroup)
            );
          } else {
            filteredResult = data.filter(
              item =>
                item.paytypegroup == this.props.history.location.state.payTypeRo
            );
          }
        } else {
          //check

          // filteredResult = data.filter(item => item.paytypegroup == 'C');
          if (this.state.opcode) {
            if (this.props.opcodePayType == 'C') {
              filteredResult = data.filter(item =>
                payTypeSaved.includes(item.paytypegroup)
              );
            } else {
              filteredResult = data.filter(
                item => item.paytypegroup == this.props.opcodePayType
              );
            }
            //filteredResult = data;
          } else {
            filteredResult = data.filter(item =>
              payTypeSaved.includes(item.paytypegroup)
            );
          }
        }
        // if (this.state.opcode) {
        //   filteredResult.forEach(item => {
        //     if (item.lbrsale === '0.00') {
        //       item.paytypegroup = 'I';
        //     }
        //   });
        // }

        // filteredResult.map(item => {
        //   if (item.opsubcategory && item.opsubcategory != 'N/A') {
        //     return (item.opsubcategory = '');
        //   }
        // });
        this.setState({ rowData: filteredResult });
        this.filterByOpCategory();
        // this.setState({
        //   rowData:
        //     result.data.statelessCcDrilldownGetDrillDownAllRevenueOpcodeDetails
        //       .nodes
        // });
      }

      this.getMonthYear();

      if (
        this.props.parent == 'searchByRo' &&
        this.props.page == 'searchByRo' &&
        this.props.pageType == 'searchByRo' &&
        this.props.payTypeRo == 'C'
      ) {
        window.filterStateDetail = {};
      }
      if (Object.keys(window.filterStateDetail).length >= 0) {
        this.filterByValues();
      }
    });
  }
  handleEditClicked = params => {
    if (this.props.isLabor) {
      window.sortState = this.gridApi.getSortModel();
      window.colStateOpcodeDetail = this.state.gridColumnApi.getColumnState();
      window.filterStateDetail = this.gridApi.getFilterModel();
    } else {
      // window.sortStatesPrts = this.gridApi.getSortModel();
      // window.colStateOpcodeDetail = this.state.gridColumnApi.getColumnState();
      // window.filterStateDetailsPrts = this.gridApi.getFilterModel();
      window.sortState = this.gridApi.getSortModel();
      window.colStateOpcodeDetail = this.state.gridColumnApi.getColumnState();
      window.filterStateDetail = this.gridApi.getFilterModel();
    }
    let data = {
      type: 'opcodelevel',
      selectedButtonType: this.props.selectedButtonType
        ? this.props.selectedButtonType
        : this.props.history &&
          this.props.history.location.state &&
          this.props.history.location.state.selectedButton
        ? this.props.history.location.state.selectedButton
        : this.props.selectedButtonType,
      parent:
        this.props.parent == 'comparisonchart'
          ? this.props.parent
          : this.props.isFrom,
      superParent:
        this.props.parent == 'comparisonchart' ||
        this.props.parent == 'noncpopcodes'
          ? this.props.parent
          : this.props.isFrom,
      monthYear: this.state.queryMonth,
      isExpanded: this.props.isExpanded,
      opcodePayType: this.props.opcodePayType,
      comparisonMonth1: this.props.comparisonMonth1,
      comparisonMonth2: this.props.comparisonMonth2,
      userHistory:
        this.props.history &&
        this.props.history.location.state &&
        this.props.history.location.state.userHistory
          ? this.props.history.location.state.userHistory
          : this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.selectedToggle
          ? this.props.history.location.state.selectedToggle
          : '',
      page:
        this.props.history &&
        this.props.history.location.state &&
        this.props.history.location.state.page
          ? this.props.history.location.state.page
          : '',
      roNumber: this.props.roNumber ? this.props.roNumber : ''
    };

    return data;
  };
  filterByValues = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateDetail);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  handleMonthYearChange = event => {
    let monthYear = event.target.value;
    this.setState({ selectedMonthYear: monthYear });
    this.setState({ queryMonth: monthYear });
    // if (this.props.parent != 'workmix' && this.props.parent != 'opcodes' )  {
    //   this.setState({ parent: '' });
    // }
    setTimeout(
      function() {
        this.setState({ isLoading: true });
        this.getAgGridData(this.state.opcode, monthYear);
      }.bind(this),
      100
    );
  };

  getMonthYear = () => {
    var table = [];
    /* if (this.state.opcode != '') {
       setTimeout(
         function() {
           getDrillDownMonthYearByOpcode(this.state.opcode, result => {
             if (result.data.dms_drilldown_vw_drill_down_total_revenue_details) {
               var formData = [];
               result.data.dms_drilldown_vw_drill_down_total_revenue_details.map(
                 item => {
                   formData.push(item.month_year);
                   this.setState({
                     opcodeMonthYear: formData
                   });
                 }
               );
             }
           });
         }.bind(this),
         100
       );
     } else {*/
    var table = [];
    this.setState({
      opcodeMonthYear: getLast13Months()
    });
    // }
  };
  renderBackButton = () => {
    {
      // window.filterStateDetail = {};
      // window.sortState = {};
      // window.filterStateDetailsPrts = {};
      // window.sortStatesPrts = {};
      let data = {};
      if (
        this.props.isFrom == 'opcodelevel' ||
        this.props.isFrom == 'searchByRo' ||
        ((this.props.isFrom == 'opcodes' ||
          this.props.isFrom == 'noncpopcodes') &&
          this.props.parent == 'opcodelevel') ||
        (this.props.isFrom == 'opcodes' &&
          this.props.parent == 'comparisonchart')
      ) {
        data = {
          tabSelection: 'four',
          isFrom: 'drillDown-workmix',
          comparisonMonth1: this.props.comparisonMonth1,
          comparisonMonth2: this.props.comparisonMonth2,
          parent:
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.parent &&
            (this.props.history.location.state.parent == 'Laboritemization' ||
              this.props.history.location.state.parent == 'Partsitemization')
              ? this.props.history.location.state.parent
              : this.state.parent == ''
              ? this.state.parent
              : this.props.isFrom == 'opcodelevel' &&
                this.props.parent == 'opcodelevel' &&
                this.state.selectedReportType == '' &&
                this.props.comparisonMonth1 != '' &&
                this.props.comparisonMonth2 != '' &&
                this.props.comparisonMonth1 != undefined &&
                this.props.comparisonMonth2 != undefined
              ? 'comparisonchart'
              : (this.props.isFrom == 'opcodelevel' &&
                  this.props.parent == 'opcodelevel' &&
                  this.state.selectedReportType == '') ||
                ((this.props.isFrom == 'opcodes' ||
                  this.props.isFrom == 'noncpopcodes') &&
                  this.props.parent == 'opcodelevel') ||
                (this.props.isFrom == 'opcodelevel' &&
                  this.props.parent == 'opcodes' &&
                  this.props.selectedButtonType != '3months')
              ? ''
              : this.props.parent,

          opcode: this.state.selectedReportType
            ? this.state.opcode
            : (this.props.isFrom == 'opcodes' ||
                this.props.isFrom == 'noncpopcodes') &&
              (this.props.parent == 'opcodelevel' ||
                this.props.parent == 'comparisonchart' ||
                this.props.parent == 'searchByRo')
            ? this.props.history.location.state &&
              this.props.history.location.state.opcode
            : '',
          selectedButton:
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.selectedButton
              ? this.props.history.location.state.selectedButton
              : this.props.selectedButtonType,
          selected: this.props.isFrom == 'opcodelevel' ? this.props.isFrom : '',
          type: '',
          history: this.props.history,
          reportType: this.state.reportTypeWorkmix,
          workmixParent: this.state.workmixParent,
          workmixTab: this.state.workmixTab,
          selectedReportType: this.state.selectedReportType,
          opCategory: this.state.opCategory,
          roNumber:
            this.props.roNumber != ''
              ? this.props.roNumber
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.prevSearch,
          pageType:
            this.props.page != ''
              ? this.props.page
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.prevPage,

          userHistory:
            this.props.selectedToggle != ''
              ? this.props.selectedToggle
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.userHistory,
          previousToggle:
            this.props.previousToggle != ''
              ? this.props.previousToggle
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.previousToggle,
          // payTypeRo:
          //   this.props.payTypeRo != ''
          //     ? this.props.payTypeRo
          //     : this.props.history &&
          //       this.props.history.location.state &&
          //       this.props.history.location.state.payTypeRo,
          payTypeRo:
            this.props.opcodePayType != ''
              ? this.props.opcodePayType
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.opcodePayType,
          // opcodePayType
          PrevPayType:
            this.props.PrevPayType != ''
              ? this.props.PrevPayType
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.PrevPayType,
          PrevGridType:
            this.props.PrevGridType != ''
              ? this.props.PrevGridType
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.PrevGridType,
          showAllJobs:
            this.props.showAllJobs != ''
              ? this.props.showAllJobs
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.showAllJobs,
          itemizationData: this.props.itemizationData
            ? this.props.itemizationData
            : '',
          itemizationTab: this.props.itemizationTab
            ? this.props.itemizationTab
            : '',
          isLabor: this.props.isLabor ? this.props.isLabor : '',
          isExpanded:
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.isExpanded
              ? this.props.history.location.state.isExpanded
              : this.props.isExpanded,
          selectedOpcodeAll: this.props.selectedOpcodeAll,
          prevPath: this.state.prevPath,
          prevMenu: this.state.prevMenu,
          filterStart:
            this.props.selectedToggle &&
            //  this.props.selectedToggle.userHistory &&
            this.props.selectedToggle.filterStart
              ? this.props.selectedToggle.filterStart
              : '',
          filterEnd:
            this.props.selectedToggle &&
            //  this.props.selectedToggle.userHistory &&
            this.props.selectedToggle.filterEnd
              ? this.props.selectedToggle.filterEnd
              : '',
          storeId:
            this.props.selectedToggle &&
            //this.props.selectedToggle.userHistory &&
            this.props.selectedToggle.storeId
              ? this.props.selectedToggle.storeId
              : '',
          selectedGridType:
            this.props.selectedGridType != ''
              ? this.props.selectedGridType
              : this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.selectedGridType,
          originData: this.state.originData
        };
      }
      this.props.parentCallback(data);
    }
  };

  handleSearchByRo = params => {
    window.sortState = this.gridApi.getSortModel();
    window.colStateOpcodeDetail = this.state.gridColumnApi.getColumnState();
    window.filterStateDetail = this.gridApi.getFilterModel();
    this.props.history.push({
      pathname: '/SearchByRO',
      history: this.props.history,
      state: {
        ronumber: params.value,
        pageType: 'opcodes',
        month_year: this.state.queryMonth,
        opcode: this.state.opcode,
        isLabor: this.state.isLabor,
        isFrom: this.props.isFrom,
        parent: this.props.parent,
        // reportType: this.props.history.location.state.reportType
        reportType: this.state.reportTypeWorkmix,
        workmixParent: this.state.workmixParent,
        workmixTab: this.state.workmixTab,
        selectedReportType: this.props.selectedReportType
          ? this.props.selectedReportType
          : this.state.selectedReportType,
        opCategory: this.state.opCategory,
        prevSearch: this.props.roNumber ? this.props.roNumber : '',
        prevPage: this.props.page ? this.props.page : '',
        userHistory: this.props.selectedToggle ? this.props.selectedToggle : '',
        previousToggle: this.props.previousToggle
          ? this.props.previousToggle
          : '',
        payType: this.props.payTypeRo ? this.props.payTypeRo : '',
        PrevPayType: this.props.PrevPayType ? this.props.PrevPayType : '',
        PrevGridType: this.props.PrevGridType ? this.props.PrevGridType : '',
        showAllJobs: this.props.showAllJobs ? this.props.showAllJobs : false,
        opcodePayType: this.props.opcodePayType,
        itemizationData: this.props.itemizationData
          ? this.props.itemizationData
          : '',
        itemizationTab: this.props.itemizationTab
          ? this.props.itemizationTab
          : '',
        isLabor: this.props.isLabor ? this.props.isLabor : '',
        isExpanded: this.props.isExpanded,
        prevPath: this.state.prevPath,
        prevMenu: this.state.prevMenu,
        closedDate: params.data.closeddate,
        openDate: params.data.roOpendate,
        selectedGridType: this.props.selectedGridType
          ? this.props.selectedGridType
          : '',
        selectedButtonType: this.props.selectedButtonType
          ? this.props.selectedButtonType
          : this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.selectedButton
          ? this.props.history.location.state.selectedButton
          : this.props.selectedButtonType,
        comparisonMonth1: this.props.comparisonMonth1,
        comparisonMonth2: this.props.comparisonMonth2,
        selectValue:
          this.props &&
          this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.selectValue
            ? this.props.history.location.state.selectValue
            : '',
        originData: this.state.originData
      }
    });
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        <div>
          <Paper square style={{ marginLeft: 8, marginRight: 8 }}>
            <Toolbar>
              {(this.props.isFrom == 'opcodelevel' &&
                this.props.parent != undefined &&
                this.state.opcode != '') ||
              (this.props.parent == 'opcodelevel' &&
                (this.props.isFrom == 'opcodes' ||
                  this.props.isFrom == 'noncpopcodes')) ||
              (this.props.parent == 'comparisonchart' &&
                this.props.isFrom == 'opcodes') ? (
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={this.renderBackButton}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              ) : (
                <Link></Link>
              )}
              <Grid container justify="flex-start" style={{ padding: '5px' }}>
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={classes.formControl}
                >
                  <InputLabel
                    htmlFor="outlined-age-native-simple"
                    margin="dense"
                  >
                    Month
                  </InputLabel>
                  <Select
                    variant="outlined"
                    label="Group By"
                    name="group-by-type"
                    value={this.state.selectedMonthYear}
                    //value={this.state.loadedMonth}
                    onChange={this.handleMonthYearChange}
                    MenuProps={{
                      getContentAnchorEl: null,
                      anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'left'
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      PaperProps: {
                        style: {
                          maxHeight: 300
                        }
                      }
                    }}
                  >
                    {/* <MenuItem value={this.state.selectedMonthYear}>
                      {this.state.selectedMonthYear}
                    </MenuItem> */}
                    {typeof this.state.monthYearsArr != 'undefined' &&
                      this.state.monthYearsArr.length > 0 &&
                      Object.values(this.state.monthYearsArr).map(item => (
                        <MenuItem value={item}>
                          {moment(item).format('MMM-YY')}
                        </MenuItem>
                      ))}
                  </Select>
                </FormControl>
                <div className={classes.containerTotal} title="Page Summary">
                  <span class="title1">Page Summary</span>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h5"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      RO Count :
                      <span className={classes.spancls}>
                        {this.state.ROCount
                          ? this.state.ROCount.toString().replace(
                              /\B(?=(\d{3})+(?!\d))/g,
                              ','
                            )
                          : '0'}
                      </span>
                    </Typography>
                  </Button>
                  <Button className={classes.summaryBlock}>
                    <Typography
                      variant="h5"
                      align="left"
                      className={classes.summaryBlockText}
                    >
                      Job Count :
                      <span className={classes.spancls}>
                        {this.state.jobCount
                          ? this.state.jobCount
                              .toString()
                              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                          : '0'}
                      </span>
                    </Typography>
                  </Button>
                  {this.state.isLabor == true ? (
                    <Button className={classes.summaryBlock}>
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Hours Sold :
                        <span className={classes.spancls}>
                          {this.state.soldHours
                            .toFixed(2)
                            .toString()
                            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        </span>
                      </Typography>
                    </Button>
                  ) : (
                    ''
                  )}
                  <Button className={classes.summaryBlock}>
                    {this.state.isLabor == true ? (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Labor Sale :
                        <span className={classes.spancls}>
                          {this.formatTotals(this.state.lbrSale)}
                        </span>
                      </Typography>
                    ) : (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Parts Sale :
                        <span className={classes.spancls}>
                          {this.formatTotals(this.state.prtsSale)}
                        </span>
                      </Typography>
                    )}
                  </Button>
                  <Button className={classes.summaryBlock}>
                    {this.state.isLabor == true ? (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Labor Cost :
                        <span className={classes.spancls}>
                          {this.formatTotals(this.state.lbrCost)}
                        </span>
                      </Typography>
                    ) : (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Parts Cost :
                        <span className={classes.spancls}>
                          {this.formatTotals(this.state.prtsCost)}
                        </span>
                      </Typography>
                    )}
                  </Button>
                  <Button className={classes.summaryBlock}>
                    {this.state.isLabor == true ? (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Labor GP :
                        <span className={classes.spancls}>
                          {this.formatTotals(this.state.lbrGP)}
                        </span>
                      </Typography>
                    ) : (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Parts GP :
                        <span className={classes.spancls}>
                          {this.formatTotals(this.state.prtsGP)}
                        </span>
                      </Typography>
                    )}
                  </Button>
                  <Button className={classes.summaryBlock}>
                    {this.state.isLabor == true ? (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Labor GP% :
                        <span className={classes.spancls}>
                          {this.formatCellValueGPPerc(this.state.lbrGPPerc)}
                        </span>
                      </Typography>
                    ) : (
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        Parts GP% :
                        <span className={classes.spancls}>
                          {this.formatCellValueGPPerc(this.state.prtsGPPerc)}
                        </span>
                      </Typography>
                    )}
                  </Button>
                </div>
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      paddingTop: 17,
                      marginLeft: 'auto',
                      cursor: 'pointer'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip>
              </Grid>
            </Toolbar>
          </Paper>
        </div>

        {this.state.isLoading && (
          <div className={classes.loader}>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id="data-tab"
          className={clsx(
            'ag-theme-balham',
            'opcode-summary-table',
            'op-detailview'
          )}
          style={{
            //height: '430px',
            height: window.innerHeight - 280 + 'px',
            width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            headerHeight={this.state.headerHeight}
            tooltipShowDelay={0}
            sideBar={this.state.sideBar}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            onFilterChanged={this.onFilterChanged}
            frameworkComponents={this.state.frameworkComponents}
            suppressContextMenu={true}
            //onFirstDataRendered={this.onFirstDataRendered}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
    '@media (max-width: 1440px)': {
      minWidth: '110px !important'
    }
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '0px !important'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#003d6b',
    fontWeight: 'bold'
  },
  containerTotal: {
    marginTop: '7px',
    border: '1px solid rgba(0, 0, 0, 0.23)',
    position: 'relative',
    padding: '6px 0px 13px',
    height: '40px',
    borderRadius: '4px'
  },
  spancls: {
    color: '#7987a1',
    marginLeft: 3,
    fontSize: '12px'
  }
});

export default withStyles(styles)(ROLevelRawDataGrid);
