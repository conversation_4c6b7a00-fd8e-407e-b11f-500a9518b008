import React, { Component } from 'react';
import EditIcon from '@material-ui/icons/Edit';
import { Tooltip } from '@material-ui/core';
import { withRouter } from 'react-router-dom';
import { withStyles } from '@material-ui/styles';
import { withKey<PERSON>loak } from '@react-keycloak/web';
import { connect } from 'react-redux';
class OpcodeCellRenderer extends Component {
  render() {
    const { classes } = this.props;
    const formatCellValueDescription = params => {
      return params && params.length > 8
        ? params.substring(0, 8) + `...`
        : params;
    };

    return (
      <span className={classes.summaryText}>
        {this.props.value != undefined ? (
          <OpcodeTooltip
            title={
              this.props.keycloak &&
              this.props.keycloak.realmAccess.roles.includes('client') == true
                ? ''
                : 'Edit'
            }
          >
            <button
              style={{
                height: '32px',
                backgroundColor: 'Transparent',
                border: 'none',
                textAlign: 'center',
                cursor:
                  this.props.keycloak &&
                  this.props.keycloak.realmAccess.roles.includes('client') ==
                    true
                    ? 'default'
                    : 'pointer'
              }}
              onClick={() => this.onButtonClicked(this.props.value)}
              disabled={
                this.props.keycloak &&
                this.props.keycloak.realmAccess.roles.includes('client') == true
                  ? true
                  : false
              }
            >
              <EditIcon
                htmlColor={
                  this.props.keycloak &&
                  this.props.keycloak.realmAccess.roles.includes('client') ==
                    true
                    ? '#38416373'
                    : 'rgb(0, 61, 107)'
                }
                style={{ width: 14, left: '9', top: '70%' }}
              ></EditIcon>
            </button>
          </OpcodeTooltip>
        ) : null}
        <a span-title={this.props.value} className={classes.summaryTag}>
          {formatCellValueDescription(this.props.value)}
        </a>
      </span>
    );
  }

  onButtonClicked(value) {
    // console.log(
    //   'renderer',
    //   this.props.summaryTab().parent,
    //   this.props.summaryTab()
    // );
    this.props.history.push({
      pathname:
        this.props.summaryTab().parent == 'noncpopcodes' ||
        this.props.summaryTab().superParent == 'noncpopcodes'
          ? '/NonCPOpcodes'
          : '/OPcodes',
      state: {
        opcode: value,
        isFrom: this.props.history.location.pathname,
        tabSelection:
          this.props.rendererType == 'DetailedView' ? 'five' : 'four',
        summaryTab: this.props.summaryTab().selectedButtonType,
        parent: this.props.summaryTab().parent,
        monthYear: this.props.summaryTab().monthYear
          ? this.props.summaryTab().monthYear
          : '',
        isExpanded: this.props.summaryTab().isExpanded,
        roNumber: this.props.summaryTab().roNumber,
        roExpanded: this.props.summaryTab().roExpanded,
        opcodePayType: this.props.summaryTab().opcodePayType,
        comparisonMonth1: this.props.summaryTab().comparisonMonth1,
        comparisonMonth2: this.props.summaryTab().comparisonMonth2,
        userHistory: this.props.summaryTab().userHistory,
        page: this.props.summaryTab().page
      }
    });
  }
}
const OpcodeTooltip = withStyles({
  tooltip: {
    lineHeight: '13px',
    fontSize: '11px',
    padding: '6px 3px 6px 3px !important',
    bottom: '5px !important',
    borderRadius: '4px',
    backgroundColor: '#d9d9d9'
  }
})(Tooltip);

const styles = theme => ({
  summaryText: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  summaryTag: {
    display: 'inline-block',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis'
  }
});
// export default connect()(withKeycloak(withStyles(styles)(Charts)));
export default connect()(
  withRouter(withKeycloak(withStyles(styles)(OpcodeCellRenderer)))
);
