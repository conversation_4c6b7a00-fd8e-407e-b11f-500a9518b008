import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import {
  Box,
  FormControl,
  Grid,
  InputLabel,
  LinearProgress,
  MenuItem,
  Paper,
  Select,
  Toolbar,
  Typography,
  Tooltip,
  Button
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  getDrillDownDataServiceAdvisorSummary,
  getDrillDownCalculationDataSASummary,
  getAllServiceAdvisors
} from 'src/utils/hasuraServices';
import clsx from 'clsx';
import { getLast13Months, getComparisonMonths } from 'src/utils/Utils';
import { myCustomSumAggregateForSummary } from 'src/components/DrillDownCalculations';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import moment from 'moment';
import { el } from 'date-fns/locale';
import { traceSpan } from 'src/utils/OTTTracing';
import { ReactSession } from 'react-client-session';
var lodash = require('lodash');
class ServiceAdvisorSummary extends React.Component {
  componentWillMount() {
    // this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps) {
    if (this.props.resetReport != this.state.resetReport) {
      this.resetRawData();
    }
    var serviceAdvisors =
      this.props.isFrom && this.props.isFrom == 'advisortrendingcharts'
        ? this.props.serviceAdvisor
          ? this.props.serviceAdvisor
          : ''
        : this.props.serviceAdvisor
        ? typeof this.props.serviceAdvisor == 'string' &&
          this.props.serviceAdvisor.split('[')[1]
          ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
          : this.props.serviceAdvisor
        : '';
    var month_years =
      this.props.month_year && serviceAdvisors != ''
        ? this.props.month_year
        : serviceAdvisors == ''
        ? ''
        : getComparisonMonths()[1];

    if (prevProps.session.serviceAdvisor != this.props.session.serviceAdvisor) {
      const dateFormat = item => moment(item).format('YYYY-MM');
      var initialQueryMonth =
        this.props.monthYear !== undefined
          ? dateFormat(this.props.monthYear)
          : getComparisonMonths()[1];
      this.getAgGridData(initialQueryMonth, this.props.session.serviceAdvisor);
      this.setState({ serviceadvisor: this.props.session.serviceAdvisor });
    }

    if (ReactSession.get('selectedStoreId') != undefined) {
      console.log('enter=3=');
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        console.log(
          'enter=4=',
          this.state.queryMonth ? this.state.queryMonth : month_years,
          this.state.serviceadvisor,
          this.props.session.serviceAdvisor
        );
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData(
          this.state.queryMonth ? this.state.queryMonth : month_years,
          ['All']
        );
        this.resetRawData();
      }
    }
  }

  // componentWillMount() {
  //   this.setState({ store: localStorage.getItem('selectedStoreId') });
  // }
  // componentDidUpdate() {
  //   if(ReactSession.get("selectedStoreId") != undefined) {
  //     var checkSt = lodash.isEqual(
  //       this.state.store,
  //       ReactSession.get("selectedStoreId")
  //     );
  //     console.log("selected=",ReactSession.get("selectedStoreId"),checkSt,localStorage.getItem('selectedStoreId'),this.state.store);
  //     if(checkSt == false ) {
  //       const dateFormat = item => moment(item).format('YYYY-MM');
  //       var initialQueryMonth =
  //         this.props.monthYear !== undefined
  //           ? dateFormat(this.props.monthYear)
  //           : getComparisonMonths()[1];
  //       this.getAgGridData(initialQueryMonth, this.props.session.serviceAdvisor);
  //       this.setState({ serviceadvisor: this.props.session.serviceAdvisor });
  //       this.setState({ store: localStorage.getItem('selectedStoreId') });
  //       this.setState({ isLoading: true });
  //     }
  //   }
  // }
  // componentDidUpdate(prevProps, prevState) {
  //   if (prevProps.session.serviceAdvisor != this.props.session.serviceAdvisor) {
  //     const dateFormat = item => moment(item).format('YYYY-MM');
  //     var initialQueryMonth =
  //       this.props.monthYear !== undefined
  //         ? dateFormat(this.props.monthYear)
  //         : getComparisonMonths()[1];
  //     this.getAgGridData(initialQueryMonth, this.props.session.serviceAdvisor);
  //     this.setState({ serviceadvisor: this.props.session.serviceAdvisor });
  //   }
  //   if (this.props.resetReport != this.state.resetReport) {
  //     this.resetRawData();
  //   }

  // }
  // componentDidUpdate(prevProps) {
  //   if (prevProps.session.serviceAdvisor != this.props.session.serviceAdvisor) {
  //     const dateFormat = item => moment(item).format('YYYY-MM');
  //     var initialQueryMonth =
  //       this.props.monthYear !== undefined
  //         ? dateFormat(this.props.monthYear)
  //         : getComparisonMonths()[1];
  //     this.getAgGridData(initialQueryMonth, this.props.session.serviceAdvisor);
  //     this.setState({ serviceadvisor: this.props.session.serviceAdvisor });
  //   }
  //   if (this.props.resetReport != this.state.resetReport) {
  //     this.resetRawData();
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    month: '2019-11'
  };
  constructor(props) {
    super(props);

    var serviceAdvisor =
      this.props.isFrom && this.props.isFrom == 'advisortrendingcharts'
        ? this.props.serviceAdvisor
          ? this.props.serviceAdvisor
          : ''
        : this.props.serviceAdvisor
        ? typeof this.props.serviceAdvisor == 'string' &&
          this.props.serviceAdvisor.split('[')[1]
          ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
          : this.props.serviceAdvisor
        : '';
    if (this.props.isFrom && this.props.isFrom != 'advisortrendingcharts') {
      serviceAdvisor = serviceAdvisor.toString();
    }
    var isFrom = this.props.isFrom ? this.props.isFrom : '';
    var month_year =
      this.props.month_year && serviceAdvisor != ''
        ? this.props.month_year
        : serviceAdvisor == ''
        ? ''
        : getComparisonMonths()[1];
    if (
      this.props.serviceAdvisor &&
      this.props.isFrom &&
      this.props.isFrom != 'advisortrendingcharts' &&
      typeof this.props.serviceAdvisor == 'string' &&
      this.props.serviceAdvisor.split('[')[1] == undefined
    ) {
      this.getAdvisorName(this.props.serviceAdvisor);
    }

    //var selectedServiceAdvisor =
    this.state = {
      queryMonth: month_year,
      resetReport: this.props.resetReport,
      serviceadvisor:
        this.props.isFrom == 'advisorcomparison' ||
        this.props.isFrom == 'workmixcharts' ||
        this.props.parent == 'monthComparison' ||
        (this.props.isFrom == 'serviceadvisor' && this.props.serviceAdvisor) ||
        (this.props.type == 'serviceadvisorlevel' && this.props.serviceAdvisor)
          ? this.props.serviceAdvisor
          : this.props.session.serviceAdvisor,
      isLoading: true,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      reportType: this.props.reportType,
      isFrom: isFrom,
      chartName: this.props.chart_name,
      lbrSale: '',
      totalSale: '',
      prtsSale: '',
      lbrHours: '',
      lbrGP: '',
      lbrGPPerct: '',
      prtsGP: '',
      prtsGPPerct: '',
      lbrCost: '',
      prtsCost: '',
      roCount: '',
      jobCount: '',
      elr: '',
      prevMenu: this.props.session.menuSelected,
      isColumnFiltered: false,
      postSort: rowNodes => {
        let nextInsertPos = 0;
        for (let i = 0; i < rowNodes.length; i++) {
          const advisorActive = rowNodes[i].data
            ? rowNodes[i].data.advisorActive
            : '';
          if (advisorActive == 1) {
            rowNodes.splice(nextInsertPos, 0, rowNodes.splice(i, 1)[0]);
            nextInsertPos++;
          }
        }
      },
      columnDefs: [
        {
          headerName: 'Advisor',
          field: 'advisorName',
          width: 160,
          minWidth: 160,
          //  flex: 2,

          chartDataType: 'category',
          cellStyle: this.setCellStyle,
          onCellClicked: this.handleCellClicked,
          cellRenderer: function(params) {
            return `<a style="cursor:pointer" >${params.value}</a>`;
          },
          // cellStyle: function() {
          //   return {
          //     color: '#000000',
          //     fontWeight: 'bold',
          //     border: ' 0px white'
          //   };
          // },
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'advisorName',
          comparator: (valueA, valueB) => {
            // Extract advisor names and numeric codes
            const nameA = (valueA || '').split('[')[0].trim();
            const nameB = (valueB || '').split('[')[0].trim();

            const numberA = parseInt(
              ((valueA && valueA.match(/\[(\d+)]/)) || [])[1] || 0,
              10
            );
            const numberB = parseInt(
              ((valueB && valueB.match(/\[(\d+)]/)) || [])[1] || 0,
              10
            );

            // Handle null values
            if (valueA === null && valueB === null) {
              return 0; // Both values are null, so they're considered equal
            } else if (valueA === null) {
              return 1; // valueA is null, so it should come after valueB
            } else if (valueB === null) {
              return -1; // valueB is null, so it should come before valueA
            }

            // Compare advisor names alphabetically
            const nameComparison = nameA.localeCompare(nameB);
            if (nameComparison !== 0) {
              return nameComparison;
            }

            // If names are the same, compare numeric codes
            return numberA - numberB;
          }
        },
        {
          headerName: 'Month',
          field: 'monthYear',
          width: 115,
          minWidth: 115,
          //flex: 1,

          chartDataType: 'category',
          valueGetter: function(params) {
            return params.data.monthYear;
          },
          valueFormatter: this.formatCellValueMonthYear,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          }
        },
        {
          headerName: 'RO Count',
          field: 'rocount',
          width: 115,
          minWidth: 115,
          //  flex: 1,

          dataType: 'series',
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Op Category',
          field: 'opcategory',
          width: 110,
          minWidth: 110,
          chartDataType: 'series',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 55,

        //   sortable: false,
        //   filter: false,
        //   suppressMenu: true,
        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   filter: 'agSetColumnFilter',
        //   filterParams: {
        //     values: ['N/A', '']
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'Job Count',
          field: 'jobcount',
          width: 115,
          minWidth: 115,
          //  flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          cellClass: 'twoDecimalPlacesWithOut$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Sold Hours',
          field: 'lbrsoldhours',
          width: 105,
          minWidth: 105,
          // flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithOut$,
          cellClass: 'twoDecimalPlacesWithOut$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Sale',
          field: 'lbrsale',
          width: 110,
          minWidth: 110,
          // flex: 1,

          dataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Cost',
          field: 'lbrcost',
          width: 120,
          minWidth: 120,
          //   flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Labor Profit ',
          field: 'lbrprofit',
          width: 120,
          minWidth: 120,
          //  flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Average ELR',
          field: 'avgelr',
          width: 120,
          minWidth: 120,
          //  flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Sale',
          field: 'prtssale',
          width: 120,
          minWidth: 120,
          //   flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Cost',
          field: 'prtscost',
          width: 120,
          minWidth: 120,
          //   flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Parts Profit',
          field: 'prtsprofit',
          width: 120,
          minWidth: 120,
          // flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Average Markup',
          field: 'avgmarkup',
          width: 120,
          minWidth: 120,
          //  flex: 1,

          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueWithFour,
          cellClass: 'fourDecimalPlaces',
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          suppressMenu: true,
          unSortIcon: true
        }
      ],
      rowData: [],

      headerHeight: 45,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: true,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' }
        },
        {
          id: 'fourDecimalPlaces',
          numberFormat: { format: '###0.0000' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0.0' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  setCellStyle = params => {
    let activeTechs = [];
    this.state.rowData.filter(e => {
      if (e.advisorActive != 1) {
        activeTechs.push(e.advisorName);
      }
    });

    return {
      color: '#000000',
      fontWeight: 'bold',
      backgroundColor: activeTechs.includes(params.data.advisorName)
        ? '#ddeaf4'
        : ''
    };
  };
  resetRawData = () => {
    this.setState({ resetReport: this.props.resetReport });
    this.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.props.handleResetReport();

    window.filterStatesAdv = {};
    window.sortStatesAdv = {};
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };

  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };

  formatCellValueWithFour = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value).toFixed(4);
    } else {
      return '0.0000';
    }
  };

  handleCellClicked = params => {
    window.sortStatesAdv = this.gridApi.getSortModel();
    window.colStatesAdv = this.state.gridColumnApi.getColumnState();
    window.filterStatesAdv = this.gridApi.getFilterModel();
    let advisor = params.value.split('[')[1];
    let data = {
      type: 'serviceadvisorlevel',
      serviceAdvisor: advisor.split(']')[0],
      month_year: params.data.monthYear,
      parent:
        this.props.isFrom == 'advisortrendingcharts'
          ? this.props.isFrom
          : this.props.parent
          ? this.props.parent
          : this.props.type,
      comparisonMonth1: this.props.comparisonMonth1,
      comparisonMonth2: this.props.comparisonMonth2,
      selectedReportType: this.props.selectedReportType,
      chartLoc:
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.chartLoc
          ? this.props.history.location.state.chartLoc
          : this.props.chartLoc,
      prevPath:
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.prevPath
          ? this.props.history.location.state.prevPath
          : this.props.prevPath,
      prevMenu: this.state.prevMenu
    };
    this.props.parentCallback(data);
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Service Advisor Summary',
      fileName: 'Service Advisor Summary',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Service Advisor Summary' },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: params => {
        // Check if the cell value matches the YYYY-MM format
        if (params.value && /^\d{4}-\d{2}$/.test(params.value)) {
          // Split the value by '-' to get year and month
          const [year, month] = params.value.split('-');
          // Return formatted value as MM/YY
          return `${month}/${year.slice(-2)}`;
        } else if (
          params.column.colId == 'avgelr' ||
          params.column.colId == 'avgmarkup'
        ) {
          if (
            params.value == '' ||
            params.value == '0.00' ||
            params.value == null
          ) {
            params.value = '0.00';
          }
        }
        // Return the original value if it doesn't match the format
        return params.value;
      }
    };

    this.state.rawGridApi.exportDataAsExcel(params);
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title: 'Service Advisor Summary',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to Excel', spanAttribute);
  };

  onFirstDataRendered = params => {
    if (this.gridApi) {
      this.gridApi.getFilterInstance('opcategory', filterInstance => {
        if (filterInstance) {
          const values = filterInstance.getValues();
          if (values && values.includes('N/A')) {
            filterInstance.setModel({
              filterType: 'set',
              values: values.filter(value => value !== 'N/A')
            });
            this.gridApi.onFilterChanged();
          }
        }
      });
    }
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    this.gridApi.setSortModel(window.sortStatesAdv);
    this.gridApi.setFilterModel(window.filterStatesAdv);

    if (window.colStatesAdv) {
      this.state.gridColumnApi.setColumnState(window.colStatesAdv);
    }
    if (
      window.colStatesAdv &&
      ((this.props.isFrom == '' && this.props.type == '') ||
        (this.props.isFrom == 'advisortrendingcharts' &&
          this.props.type == '') ||
        (this.props.type == 'reports' && this.props.parent == ''))
    ) {
      this.state.gridColumnApi.resetColumnState();
    }

    this.getAgGridData(
      this.state.queryMonth,
      this.state.serviceadvisor
        ? this.state.serviceadvisor
        : this.props.session.serviceAdvisor
    );
  };
  getAgGridData(queryMonth, serviceAdvisor) {
    var DrilldownValues = 0;

    // queryMonth =
    //   this.state.isFrom && queryMonth && this.state.isFrom != 'serviceadvisor'
    //     ? queryMonth
    //     : getLast13Months();
    queryMonth =
      this.state.isFrom && queryMonth && this.state.isFrom != 'serviceadvisor'
        ? queryMonth
        : this.props.month_year;
    if (serviceAdvisor && serviceAdvisor.includes('All')) {
      if (
        this.props.parent == 'opcategory' ||
        this.props.parent == 'monthComparison'
      ) {
        this.setState({
          queryMonth: this.props.month_year
        });
      } else {
        this.setState({
          queryMonth: getLast13Months()
        });
        this.setState({ serviceAdvisor: '' });
        queryMonth = getLast13Months();
      }
    }
    if (
      serviceAdvisor &&
      !serviceAdvisor.includes('All') &&
      this.props.isFrom == '' &&
      this.props.parent == ''
    ) {
      queryMonth = null;
    }
    // serviceAdvisor = this.props.serviceAdvisor
    //   ? this.props.serviceAdvisor
    //   : this.props.session.serviceAdvisor
    //   ? this.props.session.serviceAdvisor
    //   : serviceAdvisor;

    getDrillDownDataServiceAdvisorSummary(
      // this.props.month_year ? this.props.month_year : queryMonth,
      queryMonth,
      serviceAdvisor,
      result => {
        this.state.isFrom == 'advisortrendingcharts' && serviceAdvisor != ''
          ? this.setState({ isLoading: true })
          : this.setState({ isLoading: false });

        if (
          result.data
            .statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport
            .statelessDbdPeopleMetricsServiceAdvisorDrillDownServiceAdvisorRevenueSummaryReports
        ) {
          let resultData =
            result.data
              .statelessDbdPeopleMetricsServiceAdvisorGetDrillDownServiceAdvisorRevenueReport
              .statelessDbdPeopleMetricsServiceAdvisorDrillDownServiceAdvisorRevenueSummaryReports;
          resultData.map(item => {
            if (item.opsubcategory && item.opsubcategory != 'N/A') {
              return (item.opsubcategory = '');
            }
          });
          this.setState({
            rowData: resultData
          });
          if (this.state.isFrom == 'advisortrendingcharts') {
            getDrillDownCalculationDataSASummary(
              queryMonth,
              serviceAdvisor,
              result => {
                this.setState({ isLoading: false });
                if (
                  result.data
                    .statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrendCharts
                    .nodes
                ) {
                  if (this.state.isFrom) {
                    DrilldownValues = myCustomSumAggregateForSummary(
                      result.data
                        .statelessDbdPeopleMetricsServiceAdvisorGetRevenueTrendCharts
                        .nodes,
                      this.state.chartName
                    );

                    this.setDrillDownValuesToState(DrilldownValues);
                  }
                }
                if (window.filterStatesAdv != undefined) {
                  this.filterByValues();
                }
              }
            );
          }
        }
        if (window.filterStatesAdv != undefined) {
          this.filterByValues();
        }
      }
    );
  }

  filterByValues = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStatesAdv);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  autoSizeColumns = params => {
    const colIds = params.columnApi
      .getAllDisplayedColumns()
      .map(col => col.getColId());

    params.columnApi.autoSizeColumns(colIds);
  };
  handleOpcodeChange = event => {
    let serviceAdvisor = event.target.value.split('[')[1]
      ? event.target.value.split('[')[1].split(']')[0]
      : event.target.value;

    this.setState({ selectedOpcodeAll: event.target.value });
    if (serviceAdvisor == 'All') {
      serviceAdvisor = '';
      this.setState({ isFrom: this.state.isFrom });
      this.setState({ queryMonth: getLast13Months() });
      this.setState({ serviceAdvisor: serviceAdvisor });
    }
    setTimeout(
      function() {
        this.getAgGridData(this.state.queryMonth, serviceAdvisor.toString());
      }.bind(this),
      100
    );
  };
  setDrillDownValuesToState = Values => {
    this.setState({
      totalSale: Values.totalSale,
      lbrSale: Values.lbrSale,
      prtsSale: Values.prtsSale,
      lbrHours: Values.lbrHours,
      lbrGP: Values.lbrGP,
      lbrGPPerct: Values.lbrGPPerct,
      prtsGP: Values.prtsGP,
      prtsGPPerct: Values.prtsGPPerct,
      lbrCost: Values.lbrCost,
      prtsCost: Values.prtsCost,
      roCount: Values.roCount,
      jobCount: Values.jobCount,
      elr: Values.elr,
      partsMarkup: Values.partsMarkup
    });
  };
  renderBackButton = () => {
    {
      let data = {};
      window.filterStatesAdv = {};
      window.sortStatesAdv = {};
      if (
        this.state.prevMenu == 'Favorites' ||
        this.props.prevMenu == 'Favorites'
      ) {
        data = {
          isFrom: 'Favorites'
        };
      } else {
        if (
          this.state.isFrom == 'advisortrendingcharts' ||
          this.props.parent == 'advisortrendingcharts'
        ) {
          console.log('enter==1');
          data = {
            tabSelection: 'eight',
            isFrom: 'drillDown-sa',
            selectedSubTab: 'one',
            parent: '',
            selectedReportType: this.props.selectedReportType,
            reportTabSelection: this.props.reportTabSelection
            //selectedAdvisor: this.state.serviceAdvisor
            //selectedAdvisor: this.state.selectedServiceAdvisor
          };
          this.setState({ isFrom: '' });
        } else if (
          (this.state.isFrom == 'advisorcomparison' &&
            (this.props.type == 'monthComparison' ||
              (this.props.type == 'serviceadvisorlevel' &&
                this.props.parent == 'monthComparison'))) ||
          this.props.parent == 'monthComparison'
        ) {
          data = {
            tabSelection: 'eight',
            isFrom: 'drillDown-sa',
            selectedSubTab: 'two',
            comparisonMonth1: this.props.comparisonMonth1,
            comparisonMonth2: this.props.comparisonMonth2,
            parent: '',
            selectedReportType: this.props.selectedReportType,
            reportTabSelection: this.props.reportTabSelection
          };
        } else if (
          (this.state.isFrom == 'advisorcomparison' &&
            this.props.type == 'opcategory') ||
          this.props.parent == 'opcategory' ||
          (this.state.isFrom == 'advisorcomparison' &&
            this.props.parent == 'workmixAdvisor') ||
          (this.props.isFrom == 'workmixcharts' &&
            this.props.parent == 'workmixAdvisor')
        ) {
          data = {
            tabSelection: 'eight',
            isFrom: 'drillDown-sa',
            selectedSubTab: 'three',
            month_year: this.state.queryMonth,
            comparisonMonth2: this.props.comparisonMonth2,
            parent: '',
            selectedReportType: this.props.selectedReportType,
            reportTabSelection: this.props.reportTabSelection,
            chartLoc:
              this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.chartLoc
                ? this.props.history.location.state.chartLoc
                : this.props.chartLoc,
            prevPath:
              this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.prevPath
                ? this.props.history.location.state.prevPath
                : this.props.prevPath
          };
        } else if (
          this.props.type == 'reports' ||
          this.props.parent == 'reports'
        ) {
          data = {
            tabSelection: 'three',
            isFrom: 'drillDown-sa',
            serviceAdvisor: '',
            parent: '',
            selectedReportType: this.props.selectedReportType
              ? this.props.selectedReportType
              : 'SA_lbrsale',
            reportTabSelection: this.props.reportTabSelection,
            isReportSelected: true
          };
        }
      }
      this.props.parentCallback(data);
    }
  };

  getAdvisorName = (advisor, flag) => {
    getAllServiceAdvisors(result => {
      if (result.data.dmsPhysicalRwGetTblServiceAdvisors.nodes) {
        result.data.dmsPhysicalRwGetTblServiceAdvisors.nodes.map(e => {
          if (e.serviceadvisor == advisor) {
            this.setState({
              selectedServiceAdvisor:
                e.name + ' [' + e.serviceadvisor.toString() + ']'
            });
          } else if (e.name.includes(advisor)) {
            this.setState({
              selectedServiceAdvisor:
                e.name + ' [' + e.serviceadvisor.toString() + ']'
            });
          }
        });
      }
    });
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    if (!lodash.isEmpty(filterValues)) {
      this.setState({ isColumnFiltered: true });
    } else {
      this.setState({ isColumnFiltered: false });
    }
  };
  getRowStyle = params => {
    if (params.data.advisorActive == 0) {
      return { background: 'rgb(221, 234, 244)' };
    } else {
      return { background: 'rgb(255, 255, 255)' };
    }
  };
  render() {
    const { classes } = this.props;
    if (this.props.exportReport) {
      this.onBtExport();
      this.props.handleExportReport();
    }
    return (
      <div>
        {/* {this.state.serviceAdvisor!='' && ( */}
        {/* {(window.location.pathname != '/ServiceAdvisorPerformance') && ((this.state.serviceAdvisor != '' && */}
        {/* this.state.selectedServiceAdvisor != '' &&
          this.state.selectedServiceAdvisor != undefined) ||
        (this.state.selectedServiceAdvisor != '' &&
          this.state.selectedServiceAdvisor != undefined &&
          (this.props.isFrom == 'advisortrendingcharts' ||
            this.props.selectedReportType != ''))) ? ( */}
        <div>
          <Paper square style={{ marginLeft: 8, marginRight: 8 }}>
            <Toolbar>
              {/* {this.state.type == 'techniciantrendingcharts' ||
                this.props.parent == 'techniciantrendingcharts' ||
                this.state.type == 'techcomparison' ||
                this.props.parent == 'techcomparison' ||
                this.state.type == 'reports' ||
                this.props.parent == 'reports' ||
                this.state.type == 'technicianlevel' ?  */}

              {((this.state.type != 'serviceadvisorlevel' ||
                this.state.isFrom != 'serviceadvisor') &&
                this.state.isFrom == 'advisortrendingcharts') ||
              this.props.parent == 'advisortrendingcharts' ||
              (this.state.isFrom == 'advisorcomparison' &&
                (this.props.type == 'monthComparison' ||
                  this.props.type == 'serviceadvisorlevel')) ||
              this.props.parent == 'monthComparison' ||
              (this.state.isFrom == 'advisorcomparison' &&
                this.props.type == 'opcategory') ||
              this.props.parent == 'opcategory' ||
              this.props.type == 'reports' ||
              this.props.parent == 'reports' ||
              this.props.isFrom == 'advisortrendingcharts' ||
              (this.state.isFrom == 'advisorcomparison' &&
                this.props.parent == 'workmixAdvisor') ||
              (this.props.isFrom == 'workmixcharts' &&
                this.props.parent == 'workmixAdvisor') ? (
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={this.renderBackButton}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              ) : (
                <Link></Link>
              )}
              <Grid container justify="flex-start" style={{ padding: '5px' }}>
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={classes.formControl}
                ></FormControl>
                {/* { 
                 this.props.type == 'reports'  && */}
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      paddingTop: 17,
                      marginLeft: 'auto',
                      cursor: 'pointer'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip>
              </Grid>
            </Toolbar>
          </Paper>
        </div>
        {/* ) : (
          '' */}
        {/* )} */}
        {/*  )} */}
        {this.state.isLoading && (
          <div className={classes.loader}>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          // id="data-tab"
          id="advisor_summary_old"
          className="ag-theme-balham"
          style={{
            height:
              this.state.isFrom && this.state.isFrom != 'advisortrendingcharts'
                ? window.innerHeight - 330 + 'px'
                : this.state.isFrom &&
                  this.state.isFrom == 'advisortrendingcharts' &&
                  this.state.serviceAdvisor != ''
                ? window.innerHeight - 400 + 'px'
                : this.state.isFrom &&
                  this.state.isFrom == 'advisortrendingcharts' &&
                  this.state.serviceAdvisor == ''
                ? window.innerHeight - 330 + 'px'
                : window.innerHeight - 290 + 'px',
            //height: this.state.isFrom ? '430px' : '500px',
            // width: '98.8%',
            width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            // getChartToolbarItems={this.getChartToolbarItems}
            animateRows={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            // enableRangeSelection={true}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            //onFirstDataRendered={this.autoSizeColumns}
            postSort={this.state.postSort}
            headerHeight={this.state.headerHeight}
            sideBar={this.state.sideBar}
            floatingFilter={true}
            enableRangeSelection={true}
            onFilterChanged={this.onFilterChanged}
            enableCharts={true}
            suppressRowClickSelection={true}
            tooltipShowDelay={0}
            getRowStyle={this.getRowStyle}
            suppressContextMenu={true}
            onFirstDataRendered={this.onFirstDataRendered}
          />
        </div>
        {!this.state.isLoading &&
          this.state.rowData.length > 0 &&
          this.state.queryMonth &&
          this.state.isFrom == 'advisortrendingcharts' &&
          this.state.serviceAdvisor != '' && (
            <Paper square style={{ margin: 8 }}>
              <div
                className={clsx(classes.root, {
                  [classes.hidesummary]: this.state.isColumnFiltered
                })}
              >
                <Grid container spacing={3} style={{ margin: -8 }}>
                  {this.state.chartName == '  Total Revenue' ||
                  this.state.chartName == '  Labor Revenue' ? (
                    <SummaryTitle
                      title={'Labor Revenue'}
                      value={this.state.lbrSale}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Total Revenue' ||
                  this.state.chartName == '  Parts Revenue' ? (
                    <SummaryTitle
                      title={'Parts Revenue'}
                      value={this.state.prtsSale}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Total Revenue' ? (
                    <SummaryTitle
                      title={'Total Revenue'}
                      value={this.state.totalSale}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Effective Labor Rate' ||
                  this.state.chartName == '  Labor Gross Profit' ||
                  this.state.chartName == '  Labor Gross Profit %' ? (
                    <SummaryTitle
                      title={'Labor Sale'}
                      value={this.state.lbrSale}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Parts Gross Profit' ||
                  this.state.chartName == '  Parts Gross Profit %' ||
                  this.state.chartName == '  Parts Markup' ? (
                    <SummaryTitle
                      title={'Parts Sale'}
                      value={this.state.prtsSale}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Labor Sold Hours' ||
                  this.state.chartName == '  Effective Labor Rate' ? (
                    <SummaryTitle
                      title={'Labor Sold Hours'}
                      value={this.state.lbrHours}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Labor Gross Profit' ? (
                    <React.Fragment>
                      <SummaryTitle
                        title={'Labor Cost'}
                        value={this.state.lbrCost}
                        chart={this.state.chartName}
                      />
                      <SummaryTitle
                        title={'Labor Gross Profit'}
                        value={this.state.lbrGP}
                        chart={this.state.chartName}
                      />
                    </React.Fragment>
                  ) : null}
                  {this.state.chartName == '  Labor Gross Profit %' ? (
                    <React.Fragment>
                      <SummaryTitle
                        title={'Labor Gross Profit'}
                        value={this.state.lbrGP}
                        chart={this.state.chartName}
                      />
                      <SummaryTitle
                        title={'Labor Gross Profit %'}
                        value={this.state.lbrGPPerct}
                        chart={this.state.chartName}
                      />
                    </React.Fragment>
                  ) : null}
                  {this.state.chartName == '  Parts Gross Profit' ? (
                    <React.Fragment>
                      <SummaryTitle
                        title={'Parts Cost'}
                        value={this.state.prtsCost}
                        chart={this.state.chartName}
                      />
                      <SummaryTitle
                        title={'Parts Gross Profit'}
                        value={this.state.prtsGP}
                        chart={this.state.chartName}
                      />
                    </React.Fragment>
                  ) : null}
                  {this.state.chartName == '  Parts Gross Profit %' ? (
                    <React.Fragment>
                      <SummaryTitle
                        title={'Parts Gross Profit'}
                        value={this.state.prtsGP}
                        chart={this.state.chartName}
                      />
                      <SummaryTitle
                        title={'Parts Gross Profit %'}
                        value={this.state.prtsGPPerct}
                        chart={this.state.chartName}
                      />
                    </React.Fragment>
                  ) : null}
                  {this.state.chartName == '  RO Count' ? (
                    <SummaryTitle
                      title={'RO Count'}
                      value={this.state.roCount}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Job Count' ? (
                    <SummaryTitle
                      title={'Job Count'}
                      value={this.state.jobCount}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Effective Labor Rate' ? (
                    <SummaryTitle
                      title={'Effective Labor Rate'}
                      value={this.state.elr}
                      chart={this.state.chartName}
                    />
                  ) : null}
                  {this.state.chartName == '  Parts Markup' ? (
                    <React.Fragment>
                      <SummaryTitle
                        title={'Parts Cost'}
                        value={this.state.prtsCost}
                        chart={this.state.chartName}
                      />
                      <SummaryTitle
                        title={'Parts Markup'}
                        value={this.state.partsMarkup}
                        chart={this.state.chartName}
                      />
                    </React.Fragment>
                  ) : null}
                </Grid>
              </div>
            </Paper>
          )}
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '30px' }}
        color="primary"
      >
        {title}
      </Typography>
      <Typography variant="subtitle1" style={{ fontSize: '14px' }}>
        {' '}
        {value}
      </Typography>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
    padding: 4
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  hidesummary: {
    display: 'none'
  }
});

export default withStyles(styles)(ServiceAdvisorSummary);
