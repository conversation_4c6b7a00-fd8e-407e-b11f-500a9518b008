import { Grid } from '@material-ui/core';
import React from 'react';
import CPPartsROCount from './CPRoCount';
import { makeStyles } from '@material-ui/core/styles';
import CPLaborSoldHours from './LaborSoldhours';
import CPPartaGrossProfitPercentage from './PartsGrossProfitPercent';
import CPPartsRevenue from './PartsRevenue';
import CPLaborRevenue from './LaborRevenue';
import CPLaborGrossProfitPercentage from './LaborGrossProfitPercent';
import { useEffect } from 'react';
import clsx from 'clsx';
import 'src/styles.css';

const useStyles = makeStyles({
  formControl: {
    padding: 4,
    minWidth: 250
  },
  gridContainer: {
    padding: '8px 5px'
  },
  container: {
    padding: '0px 5px'
  }
});
const OverViewGraphGrid = ({
  isPartsCharts,
  parentCallback,
  chartZoomed,
  realm,
  partialMonth,
  session,
  history
}) => {
  const classes = useStyles();
  useEffect(() => {}, [isPartsCharts, session.serviceAdvisor]);
  const handleCallback = value => {
    if (value) {
      parentCallback(value);
    }
  };
  return (
    <Grid
      container
      className={classes.gridContainer}
      style={{ display: chartZoomed ? 'none' : 'flex' }}
      spacing={12}
    >
      <Grid item xs={3} justify="flex-start" className={classes.container}>
        {isPartsCharts == true ? (
          <CPPartsRevenue
            parentCallback={handleCallback}
            realm={realm}
            partialMonth={partialMonth}
            session={session}
            userhistory={history}
          />
        ) : (
          <CPLaborRevenue
            parentCallback={handleCallback}
            realm={realm}
            partialMonth={partialMonth}
            session={session}
            userhistory={history}
          />
        )}
      </Grid>
      <Grid item xs={3} justify="flex-start" className={classes.container}>
        {isPartsCharts == true ? (
          <CPPartaGrossProfitPercentage
            parentCallback={handleCallback}
            partialMonth={partialMonth}
            session={session}
            userhistory={history}
          />
        ) : (
          <CPLaborGrossProfitPercentage
            parentCallback={handleCallback}
            realm={realm}
            partialMonth={partialMonth}
            session={session}
            userhistory={history}
          />
        )}
      </Grid>
      <Grid item xs={3} justify="flex-start" className={classes.container}>
        <CPLaborSoldHours
          parentCallback={handleCallback}
          isPartsCharts={isPartsCharts}
          realm={realm}
          partialMonth={partialMonth}
          session={session}
          userhistory={history}
        />
      </Grid>
      <Grid item xs={3} justify="flex-start" className={classes.container}>
        <CPPartsROCount
          parentCallback={handleCallback}
          isPartsCharts={isPartsCharts}
          realm={realm}
          partialMonth={partialMonth}
          session={session}
          userhistory={history}
        />
      </Grid>
    </Grid>
  );
};

export default OverViewGraphGrid;
