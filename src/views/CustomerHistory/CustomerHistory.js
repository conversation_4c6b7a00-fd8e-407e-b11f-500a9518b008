import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  Divider,
  fade,
  LinearProgress,
  Paper,
  Tooltip,
  Button,
  Typography,
  FormControl
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import moment from 'moment';
import 'react-grid-layout/css/styles.css';
import { connect } from 'react-redux';
import DateRangePicker from 'react-bootstrap-daterangepicker';
// import 'bootstrap/dist/css/bootstrap.css';
import { withStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import Page from 'src/components/Page';
import { SET_REFRESH_STATUS } from 'src/actions';
import { setRefreshStatus } from '../../actions';
import { store } from '../../App';
import { ReactSession } from 'react-client-session';
import { getVerificationDashboardBaseURL, getYearValue } from 'src/utils/Utils';
import { getCustomerHistoryData } from 'src/utils/hasuraServices';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
class CustomerHistory extends React.Component {
  componentWillMount() {
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps) {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.getAgGridData(this.state.queryMonth);
        // this.getAgGridData(this.state.queryMonth,this.props.session.serviceAdvisor);
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ isLoading: true });
      }
    }
  }

  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);

    // var startDate =
    //   props.session.kpiToggleStartDate != ''
    //     ? props.session.kpiToggleStartDate
    //     : '';
    // var endDate =
    //   props.session.kpiToggleEndDate != ''
    //     ? props.session.kpiToggleEndDate
    //     : '';

    // var startDate =
    //   this.props.history &&
    //   this.props.history.location &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.startDate
    //     ? moment(new Date(this.props.history.location.state.startDate)).format(
    //         'MM/DD/YY'
    //       )
    //     : moment(localStorage.getItem('closedDate'))
    //         .startOf('month')
    //         .format('MM/DD/YY');

    // var endDate =
    //   this.props.history &&
    //   this.props.history.location &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.endDate
    //     ? moment(new Date(this.props.history.location.state.endDate)).format(
    //         'MM/DD/YY'
    //       )
    //     : moment(localStorage.getItem('closedDate')).format('MM/DD/YY');
    let storeId = JSON.parse(localStorage.getItem('selectedStoreId'));

    this.state = {
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      selectedMonth: moment(localStorage.getItem('closedDate')).startOf(
        'month'
      ),

      closedDate: localStorage.getItem('closedDate'),
      startDate: this.props.history
        ? this.props.history.location.state
          ? this.props.history.location.state.startDate
          : moment(new Date(this.props.selectedDates[0])).format('MM/DD/YY')
        : moment(new Date(this.props.selectedDates[0])).format('MM/DD/YY'),
      endDate: this.props.history
        ? this.props.history.location.state
          ? this.props.history.location.state.endDate
          : moment(new Date(this.props.selectedDates[1])).format('MM/DD/YY')
        : moment(new Date(this.props.selectedDates[1])).format('MM/DD/YY'),
      value: [this.props.selectedDates[0], this.props.selectedDates[1]],
      columnDefs: [
        {
          headerName: 'RO',
          field: 'ronumber',
          // width: 83,
          // minWidth: 83,

          width: 85,
          minWidth: 85,
          flex: 1,
          editable: false,
          onCellClicked: this.handleSearchByRo,
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,

          unSortIcon: true,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              textAlign: 'center',
              cursor: 'pointer',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          // width: 88,
          minWidth: 88,
          flex: 1,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          suppressMenu: true,
          unSortIcon: true,

          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Customer Name',
          field: 'customerName',
          tooltipField: 'customerName',
          minWidth: 110,
          width: 110,

          editable: false,
          flex: 1,
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          chartDataType: 'series',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Make',
          chartDataType: 'series',
          field: 'make',
          width: 90,
          minWidth: 90,
          editable: false,
          flex: 1,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Make Description',
          chartDataType: 'series',
          field: 'makedesc',
          flex: 1,
          minWidth: 100,
          editable: false,
          Width: 100,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Model',
          chartDataType: 'series',
          field: 'model',
          flex: 1,
          minWidth: 100,
          editable: false,
          Width: 100,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Model Description ',
          field: 'modeldesc',
          minWidth: 100,
          Width: 100,
          editable: false,
          flex: 1,
          cellStyle: this.cellStyles,
          chartDataType: 'category',
          unSortIcon: true,
          suppressMenu: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Year',
          field: 'year',
          minWidth: 60,
          Width: 60,
          editable: false,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          }
        },
        {
          headerName: 'Vehicle Color',
          field: 'vehiclecolor',
          minWidth: 120,
          Width: 120,
          editable: false,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Vin',
          field: 'vin',
          minWidth: 130,
          Width: 130,
          flex: 1,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },

        {
          headerName: 'Mileage',
          field: 'mileage',
          minWidth: 80,
          flex: 1,
          Width: 80,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Customer No',
          field: 'custno',
          minWidth: 90,
          Width: 90,
          flex: 1,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          }
        },
        {
          headerName: 'Address',
          field: 'address',
          minWidth: 120,
          Width: 120,
          flex: 1,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'City State Zip',
          field: 'citystatezip',
          minWidth: 110,
          Width: 110,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Contact Phone No',
          field: 'contactphonenumber',
          minWidth: 110,
          Width: 110,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          }
        },
        {
          headerName: 'Zip',
          field: 'zip',
          minWidth: 80,
          Width: 80,
          flex: 1,
          tooltipField: 'zip',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          }
        },
        {
          headerName: 'License No',
          field: 'licensenumber',
          minWidth: 80,
          Width: 80,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          }
        },
        {
          headerName: 'Booked Date',
          field: 'bookeddate',
          minWidth: 80,
          Width: 80,
          flex: 1,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          }
        },
        {
          headerName: 'Booked Time',
          field: 'bookedtime',
          editable: false,
          minWidth: 80,
          Width: 80,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category',
          cellStyle() {
            return { border: ' 0px white', textAlign: 'right' };
          }
        },
        {
          headerName: 'Status Code',
          field: 'statuscode',
          minWidth: 80,
          editable: false,
          Width: 80,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'category'
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        enableFilter: false,
        sortable: true,
        filter: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        floatingFilter: true,
        editable: true,
        suppressMovable: false
      },
      editType: 'fullRow',
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  handleSearchByRo = params => {
    window.sortState = this.gridApi.getSortModel();
    window.filterState = this.gridApi.getFilterModel();

    let start = moment(this.state.startDate).format('YYYY-MM-DD');
    let end = moment(this.state.endDate).format('YYYY-MM-DD');

    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        pageType: 'CustomerHistory',
        startDate: start,
        endDate: end
      }
    });
  };

  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/DD/YY');
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return Math.sign(params.value) > -1
        ? '$' +
            parseFloat(params.value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(params.value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0' + '%';
    }
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });
    this.setState({ gridApi: params });
    this.gridApi = params.api;

    if (this.props.history.location.state == undefined) {
      window.sortState = {};
      window.filterState = {};
    }
    this.gridApi.setSortModel(window.sortState);
    this.gridApi.setFilterModel(window.filterState);
    this.getAgGridData(this.state.value);
  };

  getAgGridData(statusDate) {
    this.setState({ isLoading: true });
    let start = moment(this.state.startDate).format('YYYY-MM-DD');
    let end = moment(this.state.endDate).format('YYYY-MM-DD');
    getCustomerHistoryData(end, start, result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessCcAggregateGetCustomerHistoryDetails
          .customerHistories
      ) {
        this.setState({
          rowData:
            result.data.statelessCcAggregateGetCustomerHistoryDetails
              .customerHistories
        });
      }
      if (window.filterState != undefined) {
        this.filterByValue();
      }
    });
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterState);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
  };
  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      fileName: 'Customer History',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Customer History'
            },
            mergeAcross: 3
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  resetDashboard = () => {
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };

  handleDateChange = (event, picker) => {
    window.sortState = {};
    window.filterState = {};
    this.setState({ isSRowDataChanged: false });
    this.setState({ filterDisabled: false });
    this.setState({
      // selectedDateRange: {
      startDate: picker.startDate,
      endDate: picker.endDate
      // }
    });
    let date = new Date(picker.startDate);
    this.setState({
      queryMonth: date.getFullYear() + '-' + moment(new Date(date)).format('MM')
    });

    this.getAgGridData(
      this.state.endDate,
      this.state.startDate,
      this.state.serviceAdvisor,
      this.props.type
    );
  };

  render() {
    const { classes } = this.props;
    const dateData = store.getState().session;
    const { selectedMonth, endDate, startDate } = this.state;
    let today;
    let yesterday;
    if (dateData.dateData[0]) {
      today = dateData.dateData[0].today;
      yesterday = dateData.dateData[0].yesterday;
    }

    return (
      <div>
        {/* <Paper square style={{margin: 8,paddingTop: '6px' }}> */}
        {/* <Grid item xs={12} style={{ justifyContent: 'left' }}> */}
        <Paper square style={{ margin: 8 }}>
          <Grid
            container
            className={clsx(classes.titleContainer, 'reset-dashboard')}
            style={{
              textTransform: 'none',
              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B'
            }}
          >
            <Grid item xs={9} style={{ display: 'flex', alignItems: 'right' }}>
              <Typography
                variant="h6"
                color="secondary"
                className={clsx(classes.mainTitle, 'main-title')}
              >
                Customer History
              </Typography>
            </Grid>
            <Grid
              item
              xs={3}
              style={{
                display: 'flex',
                justifyContent: 'center',
                paddingLeft: '10%',
                marginTop: 7,
                marginBottom: 6
              }}
            >
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{ paddingRight: 8, cursor: 'pointer', marginTop: 3 }}
                  onClick={this.onBtExport}
                >
                  <ExportIcon />
                </Link>
              </Tooltip>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.back, 'reset-btn')}
                onClick={this.resetDashboard}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </Grid>
          </Grid>
        </Paper>
        {/* /////////////////////////////////////////////////////// */}

        <Grid container spacing={12}>
          <Grid item xs={12} style={{ padding: '5px' }}>
            <Paper square justify="center">
              <Grid
                container
                className={clsx(classes.titleContainer, 'reset-dashboard')}
              >
                <Grid
                  item
                  xs={9}
                  style={{ display: 'flex', alignItems: 'right' }}
                >
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={clsx(classes.formControl, 'input-container')}
                  >
                    {dateData.dateData[0] && (
                      <DateRangePicker
                        key={selectedMonth.format()}
                        initialSettings={{
                          startDate,
                          endDate,
                          locale: {
                            format: 'MM/DD/YY',
                            separator: ' - '
                          },
                          ranges: {
                            ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(today).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].today
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].today
                              ).toDate()
                            ],
                            ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0] &&
                                dateData.dateData[0].yesterday
                            ).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].yesterday
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].yesterday
                              ).toDate()
                            ],
                            ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].dayBeforeYesterday
                            ).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].dayBeforeYesterday
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].dayBeforeYesterday
                              ).toDate()
                            ],
                            ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].thisweekstartdate
                            ).format('MMM DD') +
                            ' to ' +
                            moment(dateData.dateData[0].thisweekenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0].thisweekstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].thisweekenddate
                              ).toDate()
                            ],
                            ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastweekstartdate
                            ).format('MMM DD') +
                            ' to ' +
                            moment(dateData.dateData[0].lastweekenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0].lastweekstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastweekenddate
                              ).toDate()
                            ],
                            ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lasttwoweekstartdate
                            ).format('MMM DD') +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lasttwoweekenddate
                            ).format("MMM DD ' YY")]: [
                              moment(
                                dateData.dateData[0].lasttwoweekstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lasttwoweekenddate
                              ).toDate()
                            ],
                            ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(dateData.dateData[0].mtdstartdate).format(
                              'MMM DD'
                            ) +
                            ' to ' +
                            moment(dateData.dateData[0].mtdenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].mtdstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].mtdenddate
                              ).toDate()
                            ],
                            ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastmonthstartdate
                            ).format('MMM')]: [
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].lastmonthstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0] &&
                                  dateData.dateData[0].lastmonthenddate
                              ).toDate()
                            ],
                            ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastthreemonthstartdate
                            ).format('MMM') +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lastthreemonthenddate
                            ).format('MMM')]: [
                              moment(
                                dateData.dateData[0].lastthreemonthstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastthreemonthenddate
                              ).toDate()
                            ],
                            ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastquarterstartdate
                            ).format('MMM') +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lastquarterenddate
                            ).format('MMM')]: [
                              moment(
                                dateData.dateData[0].lastquarterstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastquarterenddate
                              ).toDate()
                            ],
                            ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(dateData.dateData[0].ytdstartdate).format(
                              'MMM DD'
                            ) +
                            ' to ' +
                            moment(dateData.dateData[0].ytdenddate).format(
                              "MMM DD ' YY"
                            )]: [
                              moment(
                                dateData.dateData[0].ytdstartdate
                              ).toDate(),
                              moment(dateData.dateData[0].ytdenddate).toDate()
                            ],
                            ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lasttwelvemonthstartdate
                            ).format("MMM ' YY") +
                            ' to ' +
                            moment(
                              dateData.dateData[0].lasttwelvemonthenddate
                            ).format("MMM ' YY")]: [
                              moment(
                                dateData.dateData[0].lasttwelvemonthstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lasttwelvemonthenddate
                              ).toDate()
                            ],
                            ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            moment(
                              dateData.dateData[0].lastyearstartdate
                            ).format('MMM') +
                            ' to ' +
                            moment(dateData.dateData[0].lastyearenddate).format(
                              'MMM'
                            ) +
                            " ' " +
                            getYearValue(
                              dateData.dateData[0].lastyearenddate
                            )]: [
                              moment(
                                dateData.dateData[0].lastyearstartdate
                              ).toDate(),
                              moment(
                                dateData.dateData[0].lastyearenddate
                              ).toDate()
                            ]
                          },
                          maxDate: moment(
                            dateData.dateData[0] && dateData.dateData[0].today
                          ).toDate(),
                          alwaysShowCalendars: false,
                          applyClass: clsx(classes.calButton, 'apply-btn'),
                          cancelClass: clsx(classes.calButton, 'apply-btn'),
                          startDate:
                            this.state.startDate && this.state.startDate != ''
                              ? moment(this.state.startDate).toDate()
                              : moment(
                                  this.props.dates[0].mtdstartdate
                                ).toDate(),
                          endDate:
                            this.state.endDate && this.state.endDate != ''
                              ? moment(this.state.endDate).toDate()
                              : moment(this.props.dates[0].mtdenddate).toDate()
                        }}
                        onApply={this.handleDateChange}
                      >
                        <input
                          type="text"
                          className="datepicker"
                          id="picker"
                          name="picker"
                          style={{ margin: '6px' }}
                          aria-labelledby="label-picker"
                          // value={`${startDate.format(
                          //   'MM/DD/YY'
                          // )} - ${endDate.format('MM/DD/YY')}`}
                        />
                      </DateRangePicker>
                    )}
                    <label class="labelpicker" for="picker" id="label-picker">
                      <div class="textpicker">Select Date Range</div>
                    </label>
                  </FormControl>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
        {/* /////////////////////////////////////////////////////// */}
        {/* <Divider></Divider> */}

        {this.state.isLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: `${window.innerHeight - 200}px`,
            width: '98.8%',
            margin: '6px',
            display:
              this.state.isLoading || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '400px',
              width: '100%'
            }}
            suppressDragLeaveHidesColumns={true}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            editType={this.state.editType}
            suppressClickEdit={true}
            // onCellClicked={this.onCellClicked}
            floatingFilter={false}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressHorizontalScroll={false}
            suppressContextMenu={true}
          />
        </div>
        {/* </Grid> */}
        {/* </Paper> */}
      </div>
    );
  }
}

CustomerHistory.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};
const styles = theme => ({
  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  back: {
    marginRight: 10,
    float: 'right'
  },
  mainTitle: {
    marginLeft: '51% !important'
    // marginTop: '10px !important'
  },
  apply: {
    marginTop: 10
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  }
});
export default withStyles(styles)(CustomerHistory);
