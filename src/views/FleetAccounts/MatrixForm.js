import React, { useState, useEffect } from 'react';
import {
  Paper,
  Typography,
  Button,
  Grid,
  FormControl,
  Radio,
  RadioGroup,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  InputLabel,
  InputAdornment,
  IconButton,
  OutlinedInput,
  Chip,
  Autocomplete,
  Checkbox,
  ListItemText
} from '@material-ui/core';

import CheckIcon from '@material-ui/icons/Check';
import CancelIcon from '@material-ui/icons/Cancel';
import { FileUploadOutlined } from '@material-ui/icons';
import { DatePicker } from '@material-ui/pickers';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  partsMatrixFileUpload,
  partsMatrixType,
  getPartsSource,
  gridTypeFileUpload
} from 'src/utils/hasuraServices';
import { DropzoneArea } from 'material-ui-dropzone';
import {
  MuiPickersUtilsProvider,
  KeyboardTimePicker,
  KeyboardDatePicker
} from '@material-ui/pickers';
import DateFnsUtils from '@date-io/date-fns';
import { makeStyles } from '@material-ui/core/styles';
import clsx from 'clsx';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import SuccessSnackbar from '../MenuModelMapping/SuccessSnackbar';
const useStyles = makeStyles(theme => ({
  customDropzone: {},
  dropzoneImage: {
    // Your custom styles for the image preview
    width: '30px', // Example width
    height: '30px', // Example height
    objectFit: 'cover', // Example object-fit property
    borderRadius: '50%' // Example border-radius
    // Add more styles as needed
  },
  snackbarContainer: {
    '& .MuiSnackbar-root': {
      top: '30px',
      bottom: 'unset',
      left: '50%',
      transform: 'translateX(-50%)'
    }
  },
  root: {
    // Add your custom styles here
    border: '2px dashed #ccc',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center'
  },
  fileError: {
    // Add your custom styles here
    border: '2px dashed #e53935',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center'
  },
  customFileIcon: {
    width: 32,
    height: 32,
    marginRight: 8
  },
  customFileName: {
    fontSize: 16
  },
  star: {
    color: 'red',
    padding: '2px'
  }
}));
const AddMatrixForm = props => {
  const [matrixType, setMatrixType] = useState([]);
  const [selectedMatrixType, setSelectedMatrixType] = useState('');
  const [partsSource, setPartsSource] = useState([]);
  const [selectedPartsSource, setSelectedPartsSource] = useState([]);
  const [error, setError] = useState('');
  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const [rowData, setRowData] = useState([
    { from: 0.01, to: '', base: '', breakField: '', percentage: '' }
  ]);

  const handleAddRow = () => {
    const lastRow = rowData[rowData.length - 1];
    const newRow = {
      from: parseFloat(lastRow.to) + 0.01,
      to: '',
      base: '',
      breakField: '',
      percentage: ''
    };
    setRowData([...rowData, newRow]);
  };

  const handleRemoveLastRow = () => {
    if (rowData.length > 1) {
      setRowData(rowData.slice(0, -1));
    }
  };
  const handleDateChange = date => {
    setSelectedDate(date);
  };
  useEffect(() => {
    getPartsSource('prtsource_list', result => {
      if (
        result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix.nodes
      ) {
        setPartsSource(
          result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix
            .nodes
        );
      }
    });

    partsMatrixType(result => {
      if (result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes) {
        setMatrixType(
          result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes
        );
      }
    });
  }, []);
  const handleMatrixTypeChange = event => {
    setSelectedMatrixType(event.target.value);
    // setMatrixType(event.target.value);
  };

  const handleSourceChange = event => {
    // setSelectedPartsSource(event.target.value);
  };
  const handleCancel = () => {
    props.handleCancel();
  };
  const columnDefs = [
    { headerName: 'From', field: 'from', editable: false },
    { headerName: 'To', field: 'to', editable: true },
    { headerName: 'Base', field: 'base', editable: true },
    { headerName: 'Break Field', field: 'breakField', editable: true },
    { headerName: 'Percentage', field: 'percentage', editable: true }
  ];

  return (
    <Paper elevation={3} style={{ padding: '20px', marginBottom: '20px' }}>
      <Typography variant="h5" gutterBottom>
        Add Matrix
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <div
            className="ag-theme-material"
            style={{ height: '300px', width: '100%', marginBottom: '20px' }}
          >
            <AgGridReact
              columnDefs={columnDefs}
              rowData={rowData}
              suppressContextMenu={true}
            />
          </div>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4}></Grid>
            <Grid item xs={12} sm={4}>
              <Grid item xs={12} sm={12}>
                <FormControl style={{ width: 235, marginLeft: 55 }}>
                  <InputLabel>Matrix Type</InputLabel>
                  <Select
                    value={selectedMatrixType}
                    onChange={handleMatrixTypeChange}
                  >
                    {matrixType.map(item => (
                      <MenuItem value={item.value}>{item.value}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={12}>
                {/* <FormControl style={{ width: 235, marginLeft: 55 }}>
                  <InputLabel>Source</InputLabel>
                  <Select
                    value={selectedPartsSource}
                    onChange={handleSourceChange}
                  >
                    {partsSource.map(item => (
                      <MenuItem value={item.prtsourceList}>
                        {item.prtsourceList}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl> */}
                <FormControl style={{ width: 235, marginLeft: 55 }}>
                  <InputLabel>Source</InputLabel>
                  <Select
                    multiple
                    value={selectedPartsSource}
                    onChange={e => setSelectedPartsSource(e.target.value)}
                    input={<OutlinedInput label="Multiple Select" />}
                    renderValue={selected => (
                      <div gap={1} direction="row" flexWrap="wrap">
                        {selectedPartsSource.map(value => (
                          <Chip
                            key={value}
                            label={value}
                            onDelete={() =>
                              setSelectedPartsSource(
                                selectedPartsSource.filter(
                                  item => item !== value
                                )
                              )
                            }
                            deleteIcon={
                              <CancelIcon
                                onMouseDown={event => event.stopPropagation()}
                              />
                            }
                          />
                        ))}
                      </div>
                    )}
                  >
                    {partsSource.map(item => (
                      <MenuItem
                        key={item.prtsourceList}
                        value={item.prtsourceList}
                        sx={{ justifyContent: 'space-between' }}
                      >
                        {item.prtsourceList}
                        {selectedPartsSource.includes(item.prtsourceList) ? (
                          <CheckIcon color="info" />
                        ) : null}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={12}>
                <MuiPickersUtilsProvider
                  utils={DateFnsUtils}
                  style={{ width: 200 }}
                >
                  <Grid container justifyContent="space-around">
                    <KeyboardDatePicker
                      margin="normal"
                      id="date-picker-dialog"
                      label="Date picker dialog"
                      format="MM/dd/yy"
                      value={selectedDate}
                      onChange={handleDateChange}
                      KeyboardButtonProps={{
                        'aria-label': 'change date'
                      }}
                      // style={{ paddingTop: '8px !important' }}
                    />
                  </Grid>
                </MuiPickersUtilsProvider>
              </Grid>

              <Grid container spacing={3} justify="flex-end">
                <Grid item>
                  <Button
                    variant="contained"
                    color="secondary"
                    onClick={handleCancel}
                  >
                    Cancel
                  </Button>
                </Grid>
                <Grid item>
                  <Button
                    variant="contained"
                    color="primary"
                    // onClick={handleSubmit}
                  >
                    Submit
                  </Button>
                </Grid>
              </Grid>
            </Grid>
            <Grid item xs={12} sm={4}></Grid>
          </Grid>
        </Grid>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <Button variant="contained" color="primary" onClick={handleAddRow}>
              Add Row
            </Button>
            <Button
              variant="contained"
              color="secondary"
              onClick={handleRemoveLastRow}
            >
              Remove Last Row
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};

const UploadMatrixForm = props => {
  const names = [
    'Humaira Sims',
    'Santiago Solis',
    'Dawid Floyd',
    'Mateo Barlow',
    'Samia Navarro',
    'Kaden Fields',
    'Genevieve Watkins',
    'Mariah Hickman',
    'Rocco Richardson',
    'Harris Glenn'
  ];
  const [selectedNames, setSelectedNames] = useState([]);

  const classes = useStyles();
  const [files, setFiles] = useState('');
  const [fileName, setFileName] = useState('');
  const [matrixType, setMatrixType] = useState([]);
  const [selectedMatrixType, setSelectedMatrixType] = useState('');
  const [partsSource, setPartsSource] = useState([]);
  const [selectedPartsSource, setSelectedPartsSource] = useState([]);
  const [error, setError] = useState('');
  const [fileError, setFileError] = useState('');
  const [sourceError, setSourceError] = useState('');
  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const [fileUploaded, setFileUploaded] = useState([]);
  const [successMsg, setSuccessMsg] = useState('');
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [allSelected, setAllSelected] = useState(false);
  const [selectedGridType, setSelectedGridType] = useState('');
  const [gridTypeError, setGridTypeError] = useState('');
  const [goalFail, setGoalFail] = useState('');
  const handleDateChange = date => {
    setSelectedDate(date);
  };
  useEffect(() => {
    getPartsSource('prtsource_list', result => {
      if (
        result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix.nodes
      ) {
        setPartsSource(
          result.data.statelessDbdKpiScorecardBzoGetKpiScorecardPartsMatrix
            .nodes
        );
      }
    });

    partsMatrixType(result => {
      if (result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes) {
        setMatrixType(
          result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes
        );
        setSelectedMatrixType(
          result.data.statelessCcPhysicalRwPartsMatrixTypeMasterDetails.nodes[0]
            .value
        );
      }
    });
  }, []);
  const handleMatrixTypeChange = event => {
    setSelectedMatrixType(event.target.value);
    // setMatrixType(event.target.value);
  };

  const handleSourceChange = event => {
    setSelectedPartsSource(event.target.value);
  };

  const handleFileChange = fileData => {
    setFileUploaded([...fileUploaded, ...fileData]);
    const file = fileData[0]; // Assuming only one file is uploaded
    if (file) {
      setFileName(file.name);
      console.log('file===', file);
      const reader = new FileReader();
      reader.onload = () => {
        const base64Data = reader.result;
        const base64Content = base64Data.split(',')[1];
        setFiles(base64Content);
        console.log('Base64 data:', base64Content, '==', files);
        // You can now use the base64Data as needed
      };
      reader.readAsDataURL(file);
    }
  };
  const handleDeleteFile = deletedFile => {
    // Remove the deleted file from the files state
    const filteredFiles = fileUploaded.filter(file => file !== deletedFile);
    setFiles('');
  };
  const handleCancel = () => {
    props.handleCancel();
  };
  const handleSubmit = () => {
    setopenSnackbar(false);
    setGoalFail('');
    setSuccessMsg('');
    console.log('props.typeFor==', props.typeFor);
    if (props.typeFor == 'Matrix') {
      if (selectedPartsSource.length > 0) {
        console.log('enter=1=====1111====');
        setSourceError('');
      } else {
        console.log('enter=1=====22222');
        setSourceError('Please select at least one parts source');
      }
      if (files) {
        setFileError('');
      } else {
        setFileError('Please upload the parts matrix file.');
      }

      if (!selectedMatrixType || !selectedPartsSource.length > 0 || !files) {
        setError('Please fill in all fields');
        return;
      } else {
        setError('');
        var source = [];
        source.push(selectedPartsSource);
        partsMatrixFileUpload(
          files,
          fileName,
          selectedMatrixType,
          selectedDate,
          selectedPartsSource,
          result => {
            if (
              result.data
                .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                .results[0]
            ) {
              if (
                result.data
                  .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                  .results[0].status == 1
              ) {
                setopenSnackbar(true);
                setGoalFail(false);
                console.log('enter=1');
                setSuccessMsg(
                  'Parts matrix file has been successfully uploaded'
                );
              }
              if (
                result.data
                  .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                  .results[0].status == 0
              ) {
                setopenSnackbar(true);
                console.log('enter=1234');
                setGoalFail(true);
                setSuccessMsg(
                  result.data
                    .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog
                    .results[0].msg
                );
              }
            }
          }
        );
      }
    } else if (props.typeFor == 'Grid') {
      if (selectedGridType) {
        setGridTypeError('');
      } else {
        setGridTypeError('Please Enter Grid Type.');
      }

      if (files) {
        setFileError('');
      } else {
        setFileError('Please upload the parts matrix file.');
      }

      if (!selectedGridType || !files) {
        setError('Please fill in all fields');
        return;
      } else {
        setError('');
        // var source = [];
        // source.push(selectedPartsSource);
        gridTypeFileUpload(
          files,
          fileName,
          selectedGridType,
          selectedDate,
          result => {
            console.log('result==1=============', result);
            if (
              result.data.statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
                .results[0]
            ) {
              if (
                result.data.statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
                  .results[0].status == 1
              ) {
                setopenSnackbar(true);
                setGoalFail(false);
                console.log('enter=1');
                setSuccessMsg('Labor grid file has been successfully uploaded');
              }
              if (
                result.data.statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
                  .results[0].status == 0
              ) {
                setopenSnackbar(true);
                console.log('enter=1234');
                setGoalFail(true);
                setSuccessMsg(
                  result.data
                    .statelessCcPhysicalRwInsertGriddataDtlFileUploadLog
                    .results[0].msg
                );
              }
            }
          }
        );
      }
    }
  };

  const hidesnackbar = () => {
    setopenSnackbar(false);
  };

  const handleSelectChange = event => {
    const { value } = event.target;

    if (value.includes('all')) {
      if (allSelected) {
        setSelectedPartsSource([]);
        setAllSelected(false);
      } else {
        setSelectedPartsSource(partsSource.map(option => option.prtsourceList));
        setAllSelected(true);
      }
    } else {
      setSelectedPartsSource(value);
      setAllSelected(false);
    }
  };
  const handleGridTypeChange = event => {
    setSelectedGridType(event.target.value);
  };

  const handleCallback = (start, end, label) => {
    console.log('start===', start);
    setSelectedDate(start.format('MM/DD/YY'));
  };
  console.log('sourceError==', sourceError);
  return (
    <Paper elevation={3} style={{ padding: '20px', marginBottom: '20px' }}>
      <Typography variant="h5" gutterBottom>
        {props.typeFor == 'Matrix' ? 'Upload Matrix' : 'Upload Grid'}
      </Typography>
      <Grid container spacing={6}>
        <Grid item xs={12} sm={2}>
          {props.typeFor == 'Matrix' ? (
            <TextField
              id="outlined-select-matrix-value"
              select
              size="small"
              style={{
                marginRight: 5,
                width: '100%'
                //display: this.state.partsMatrix ? 'block' : 'none'
              }}
              label="Matrix Type"
              SelectProps={{
                native: true
              }}
              classes={{
                root: 'matrix-value'
              }}
              placeholder={'Select'}
              value={selectedMatrixType}
              onChange={handleMatrixTypeChange}
              //placeholder=" select your currency"
              variant="outlined"
            >
              {matrixType.map(item => (
                <option key={item.value} value={item.value}>
                  {item.value}
                </option>
              ))}
            </TextField>
          ) : (
            <>
              <TextField
                id="outlined-select-matrix-value"
                size="small"
                style={{
                  marginRight: 5,
                  width: '100%'
                  //display: this.state.partsMatrix ? 'block' : 'none'
                }}
                label={
                  <span>
                    Grid Type <span style={{ color: 'red' }}>*</span>
                  </span>
                }
                SelectProps={{
                  native: true
                }}
                classes={{
                  root: 'matrix-value'
                }}
                placeholder={'Grid Type'}
                value={selectedGridType}
                onChange={handleGridTypeChange}
                //placeholder=" select your currency"
                variant="outlined"
              />
              {gridTypeError && (
                <p
                  style={{
                    marginTop: 5,
                    color: 'tomato',
                    marginLeft: 6,
                    fontSize: '90'
                  }}
                >
                  {gridTypeError}
                </p>
              )}
            </>
          )}
        </Grid>
        {props.typeFor == 'Matrix' && (
          <Grid item xs={12} sm={2}>
            <FormControl fullWidth>
              <InputLabel style={{ marginLeft: 16, top: '-5px' }}>
                Parts Source <span className={classes.star}>*</span>
              </InputLabel>
              <Select
                multiple
                value={selectedPartsSource}
                style={{ height: 40 }}
                onChange={handleSelectChange}
                input={<OutlinedInput label="Parts Source" />}
                startAdornment={
                  <InputAdornment position="start"></InputAdornment>
                }
                id="selectPartsSource"
                renderValue={selected => (
                  <div gap={1} direction="row" flexWrap="wrap">
                    {selectedPartsSource.map(value => (
                      <Chip
                        key={value}
                        label={value}
                        onDelete={() =>
                          setSelectedPartsSource(
                            selectedPartsSource.filter(item => item !== value)
                          )
                        }
                        deleteIcon={
                          <CancelIcon
                            onMouseDown={event => event.stopPropagation()}
                          />
                        }
                      />
                    ))}
                  </div>
                )}
                MenuProps={{
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  },
                  getContentAnchorEl: null // This ensures the menu is not anchored to the text field
                }}
                renderValue={selected => (
                  <div>
                    {selected.map(value => (
                      <Chip key={value} label={value} />
                    ))}
                  </div>
                )}
              >
                <MenuItem value="all" selected={allSelected}>
                  <Checkbox checked={allSelected} />
                  <ListItemText primary="Select All" />
                </MenuItem>
                {partsSource.map(item => (
                  <MenuItem key={item.prtsourceList} value={item.prtsourceList}>
                    <Checkbox
                      checked={
                        selectedPartsSource.indexOf(item.prtsourceList) > -1
                      }
                    />
                    <ListItemText primary={item.prtsourceList} />
                  </MenuItem>
                ))}
              </Select>
              {sourceError && (
                <p
                  style={{
                    marginTop: 5,
                    color: 'tomato',
                    marginLeft: 6,
                    fontSize: '90'
                  }}
                >
                  {sourceError}
                </p>
              )}
            </FormControl>
          </Grid>
        )}
        <Grid item xs={12} sm={2}>
          <FormControl
            variant="outlined"
            margin="dense"
            className={clsx(classes.formControl, 'input-container')}
            style={{ width: '100%' }}
          >
            <DateRangePicker
              initialSettings={{
                locale: {
                  format: 'MM/DD/YY',
                  separator: ' - '
                },
                autoUpdateInput: true,
                showDropdowns: true,
                autoApply: true,
                singleDatePicker: true,

                applyClass: clsx(classes.calButton, 'apply-btn'),
                cancelClass: clsx(classes.calButton, 'apply-btn')
              }}
              value={selectedDate}
              onCallback={handleCallback}
            >
              <input
                type="text"
                className="datepicker fleet-picker fleet-picker-cls"
                id="fleet-picker"
                name="picker"
                aria-labelledby="label-picker"
                style={{ marginTop: -8, height: 40, width: '100%' }}
              />
            </DateRangePicker>
            <label
              class="labelpicker fleet-picker"
              for="picker"
              id="label-picker"
            >
              <div class="textpicker fleet-picker-label">
                Install Date <span className={classes.star}>*</span>
              </div>
            </label>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}></Grid>
        {props.typeFor == 'Grid' && <Grid item xs={12} sm={2}></Grid>}
      </Grid>
      <Grid container spacing={3}>
        <Grid item xs={12} sm={3} className={classes.snackbarContainer}>
          <DropzoneArea
            acceptedFiles={['.csv']}
            maxFiles={1}
            filesLimit={1}
            dropzoneText={'Drag and drop a CSV file here or click'}
            showAlerts={['error', 'info']}
            onChange={handleFileChange}
            onDelete={handleDeleteFile}
            maxHeight={2}
            classes={{
              root: fileError ? classes.fileError : classes.root
            }}
            iconStyle={{
              fontSize: '48px', // Example icon size
              color: 'blue'
            }}
            showFileNames
            dropzoneProps={{
              style: {
                // Custom style for the dropzone area
                border: '2px dashed #e53935 !important',
                borderRadius: '5px',
                backgroundColor: '#fafafa',
                maxHeight: '10px',
                padding: '20px',
                textAlign: 'center'
              }
            }}
          />
          {fileError && (
            <p
              style={{
                marginTop: 5,
                color: 'tomato',
                marginLeft: 6,
                fontSize: '90'
              }}
            >
              {fileError}
            </p>
          )}
        </Grid>
        <Grid item xs={12} sm={3}></Grid>
        <Grid item xs={12} sm={3}></Grid>
      </Grid>

      <Grid item xs={2} style={{ marginTop: 25 }}>
        <div>
          <Button
            title="Save"
            className={clsx('reset-btn', 'btnClass')}
            variant="contained"
            color="primary"
            style={{
              marginLeft: 5,
              width: '55px',
              height: '24px',
              fontSize: '15px'
            }}
            onClick={handleSubmit}
          >
            Save
          </Button>
          <Button
            title="Reset"
            className={clsx('reset-btn')}
            style={{ marginLeft: 5, width: '55px' }}
            variant="contained"
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </div>
        {successMsg && (
          <SuccessSnackbar
            open={openSnackbar}
            onClose={hidesnackbar}
            msg={successMsg}
            goalFail={goalFail}
          />
        )}
      </Grid>
    </Paper>
  );
};

const App = props => {
  const [selectedOption, setSelectedOption] = useState('');
  const handleCancel = () => {
    setSelectedOption('');
  };
  const handleChange = event => {
    setSelectedOption(event.target.value);
    props.showPartsMatrix(event.target.value);
  };
  return (
    <div style={{ padding: '20px' }}>
      <FormControl component="fieldset">
        <RadioGroup
          aria-label="matrixOption"
          name="matrixOption"
          value={selectedOption}
          onChange={handleChange}
          row // Add the row prop to the RadioGroup component
        >
          <Grid container>
            <Grid item>
              <FormControlLabel
                value="add"
                control={<Radio />}
                label={props.typeFor == 'Matrix' ? 'Add Matrix' : 'Add Grid'}
              />
            </Grid>
            <Grid item>
              <FormControlLabel
                value="upload"
                control={<Radio />}
                label={
                  props.typeFor == 'Matrix' ? 'Upload Matrix' : 'Upload Grid'
                }
              />
            </Grid>
          </Grid>
        </RadioGroup>
      </FormControl>
      {selectedOption === 'upload' && (
        <UploadMatrixForm handleCancel={handleCancel} typeFor={props.typeFor} />
      )}
      {/* {selectedOption === 'add' && (
        <AddMatrixForm handleCancel={handleCancel} />
      )} */}
    </div>
  );
};

export default App;
