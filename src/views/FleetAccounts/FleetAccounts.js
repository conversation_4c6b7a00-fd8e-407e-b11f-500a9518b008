import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import PartsMatrixModel from './PartMatrixModel';
import LaborGrid from './LaborGrid';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tooltip,
  Link,
  FormControl,
  FormHelperText,
  TextField,
  InputLabel,
  Select,
  MenuItem,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import AddIcon from '@material-ui/icons/Add';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import Checkbox from '@material-ui/core/Checkbox';
import Autocomplete from '@material-ui/lab/Autocomplete';
import InputAdornment from '@material-ui/core/InputAdornment';
import SwapHorizIcon from '@material-ui/icons/SwapHoriz';
import SearchIcon from '@material-ui/icons/Search';
import FixedRateRenderer from './FixedRateRenderer';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';

import { AgGridReact } from '@ag-grid-community/react';

import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getFleetCustomerNames,
  getFleetListItems,
  insertFleetDetails,
  insertFleetNameToList,
  getFleetMasterData,
  getFleetPayTypeNames
} from 'src/utils/hasuraServices';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import { withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import Datepicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import moment from 'moment';

import GridCheckboxRenderer from './GridCheckboxRenderer.js';

import { withKeycloak } from '@react-keycloak/web';
var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

class FleetAccounts extends React.Component {
  componentDidMount() {
    // to get list of customer

    this.fetchFleetCustomerNames();

    // to get list of paytype names
    this.fetchFleetPayTypeNames('paytype_fleet');

    // to get list of already exist paytype names
    this.fetchFleetPayTypeNames('paytype_fleet_exists');

    // to get list of already exist customer names
    this.fetchFleetPayTypeNames('cust_fleet');

    getFleetMasterData('fleet_name', result => {
      let data = result;

      if (
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
          .statelessCcPhysicalRwKpiFleetMasters.length > 0
      ) {
        this.setState({
          isFleetLoaded: true
        });

        let fleetNames = [{ customerName: 'All' }];
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster.statelessCcPhysicalRwKpiFleetMasters.map(
          (val, i) => {
            fleetNames.push({
              customerName: val.fleetName
            });
          }
        );
        this.setState({
          selectedFleetNames: fleetNames
        });
        console.log('ffff', fleetNames);
        // this.setState({
        //   customerNames: result.data.statelessCcAggregateGetCustomers.nodes
        // });
      }
    });
    getFleetMasterData('part_source', result => {
      let data = result;

      if (
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
          .statelessCcPhysicalRwKpiFleetMasters.length > 0
      ) {
        this.setState({
          isFleetLoaded: true
        });

        let partsSource = [];
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster.statelessCcPhysicalRwKpiFleetMasters.map(
          (val, i) => {
            partsSource.push(val.partSource);
          }
        );

        this.setState({
          partsSourceArray: partsSource
        });
        this.setState({
          partsSource: partsSource[0]
        });
        console.log('ffff', partsSource[0]);
        // this.setState({
        //   customerNames: result.data.statelessCcAggregateGetCustomers.nodes
        // });
      }
    });
  }

  // Define a function to fetch fleet pay type names
  fetchFleetPayTypeNames = variable => {
    getFleetPayTypeNames(variable, result => {
      console.log('resultsssss-----', result);
      let data = result;

      if (
        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
          .statelessCcPhysicalRwKpiFleetMasters.length > 0
      ) {
        this.setState({
          isFleetLoaded: true
        });

        let fleetNames = [];

        if (variable == 'paytype_fleet' || variable == 'paytype_fleet_exists') {
          fleetNames = [{ payTypeName: 'All' }];
        } else {
          fleetNames = [{ customerName: 'All' }];
        }

        result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster.statelessCcPhysicalRwKpiFleetMasters.map(
          (val, i) => {
            if (
              variable == 'paytype_fleet' ||
              variable == 'paytype_fleet_exists'
            ) {
              fleetNames.push({
                payTypeName: val.fleetName
              });
            } else {
              fleetNames.push({
                customerName: val.fleetName
              });
            }
          }
        );
        if (variable == 'paytype_fleet') {
          this.setState({
            selectedPayTypeNames: fleetNames
          });
        } else if (variable == 'paytype_fleet_exists') {
          this.setState({
            selectedPayTypeNamesExist: fleetNames
          });
        } else {
          this.setState({
            selectedCustomerNamesExist: fleetNames
          });
        }

        console.log('paytypenmaes-----------', fleetNames);
      }
    });
  };

  fetchFleetCustomerNames = () => {
    getFleetCustomerNames(result => {
      let data = result;
      // let fleetNames = [{ customerName: 'All' }];
      if (result.data.statelessCcAggregateGetCustomers.nodes.length > 0) {
        this.setState({
          isFleetLoaded: true
        });
        let fleetNames = result.data.statelessCcAggregateGetCustomers.nodes;

        this.setState({
          customerNames: fleetNames
        });
        this.setState({
          isFleetNamesLoaded: true
        });
        //console.log('ccc===', fleetNames);
      }
    });
  };

  constructor(props) {
    super(props);
    let payTypeSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : this.props.selectedPayType;
    let gridDoorRate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridDoorRate
        ? this.props.history.location.state.gridDoorRate
        : '';
    let selectedGrid =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedGrid == ''
        ? this.props.history.location.state.selectedGrid
        : 1;
    if (payTypeSelected == 'C') {
      payTypeSelected = 'Customer';
    } else if (payTypeSelected == 'I') {
      payTypeSelected = 'Internal';
    }
    let showAllJobs =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : false;
    const startEdit = this;
    const { keycloak } = this.props;
    this.textInput = React.createRef();
    this.state = {
      dataDeleteConfirm: false,
      openSnackbar: false,
      openSnackbarEdit: false,
      openSnackbarError: false,
      openSnackbarDelete: false,
      showPartsMatrix: false,
      showLaborGrid: false,
      typeStatus: true,
      searchStatus: 'customer',
      responseStatus: false,
      gridRateValueStart: 'customer',
      rawGridApi: {},
      rawGridApiLabor: {},
      rawGridApiParts: {},
      gridApiLabor: {},
      gridApiParts: {},
      isLoading: true,
      oldRowArray: [],
      newRowArray: [],
      oldCodeArray: [],
      prevCodeArray: [],
      newCodeArray: [],
      selectedGrid: selectedGrid,
      selectedPayType: payTypeSelected,
      payTypes: this.props.payTypes,
      gridPeriods: [],
      gridDoorRate: gridDoorRate,
      addNewRow: true,
      addNewFleet: false,
      isFleetLoaded: false,
      isFleetNamesLoaded: false,
      customerNames: { customerName: 'All' },
      selectedCustomer: '',
      selectedPayTypeOption: '',
      selectedFleet: 'All',
      selectedPayTypeOptionType: 'All',
      selectedCustomerNamesExist: [],
      selectedPayTypeNamesExist: [],
      showAllJobs: showAllJobs,
      gridRateValue: 'fixedRate',
      matrixRateValue: 'partsFixedRate',
      partsSourceArray: [],
      laborGrid: false,
      fixedRate: true,
      doorRate: '',
      fixedDoorRate: '',
      fixedLaborRate: '',
      partsFixedRate: true,
      partsMatrix: false,
      doorRateChanged: false,
      fixedDoorRateChanged: false,
      fixedRateChanged: false,
      markupChanged: false,
      markupValueChanged: false,
      pSourceChanged: false,
      matrixChanged: false,
      markupValue: '',
      partsMarkup: 'Cost',
      partsSource: '',
      gridInstalledDate: new Date(),
      matrixInstalledDate: new Date(),
      selectedFleetError: false,
      setFleetOpen: false,
      fleetNameError: false,
      doorRateError: false,
      fixedLaborRateError: false,
      gridInstalledDateError: false,
      markupError: false,
      partsourceError: false,
      matrixInstalledDateError: false,
      laborFixedRateNew: null,
      doorRateNew: null,
      partsFixedRateOld: '',
      partsFixedRateNew: null,
      errorItems: [],
      columnDefs: [
        // {
        //   headerName: 'Door Rate',
        //   field: 'doorRate',
        //   width: 90,njan
        //   },
        // },
        {
          headerName: 'Fleet Name',
          field: 'fleetName',
          width: 130,
          tooltipField: 'fleetName',
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },

        {
          headerName: 'Labor Grid',
          field: 'fleetFlag',
          tooltipField: 'fleetFlag',
          width: 120,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },

        {
          headerName: 'Labor Grid',
          hide: true,
          field: 'laborgridOrNot',
          width: 70,
          unSortIcon: false,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'center', border: ' 0px white' };
          },
          cellRenderer: params => {
            var index = params.rowIndex;

            if (typeof params.value != 'undefined') {
              return `<input type='checkbox' value=${
                params.value
              } disabled="disabled"
              id="laborgridOrNot${index}"
              ${params.value == 1 ? 'checked' : ''} />`;
            } else {
              return '';
            }
          }
        },
        {
          headerName: 'Fixed Rt',
          field: 'fleetRate',
          tooltipField: 'fleetRate',
          width: 105,
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          valueSetter: function(params) {
            var newValue = parseFloat(params.newValue); // Parse the new value as a float
            if (isNaN(newValue) || newValue < 0) {
              // Validate the new value
              // Validation failed, return false to reject the new value

              return false;
            }

            // Validation passed, update the data with the new value
            params.data.fleetRate = newValue;
            return true;
          },
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '1 Hr Door Rt',
          hide: true,
          field: 'doorRate',
          width: 105,
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          //cellRenderer: 'fixedRateRenderer',
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Install Date',
          field: 'installDate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          width: 110,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Period',
          field: 'gridOrder',
          valueFormatter: this.formatCellValuePeriod,
          width: 110,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },

        {
          headerName: 'Action',
          width: 120,
          filter: false,
          sortable: false,
          editable: false,

          suppressMenu: true,
          // hide: true,
          flex: 1,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          cellRenderer(params) {
            let index = params.rowIndex;
            const eDiv = document.createElement('div');
            eDiv.innerHTML = `<button   title="Edit" id="btnedit${index}" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button  title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-check"></i></button>
            <button title="Delete" id="btndelete${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>`;
            // if (
            //   props.keycloak &&
            //   props.keycloak.realmAccess.roles.includes('admin') == false &&
            //   props.keycloak.realmAccess.roles.includes('superadmin') ==
            //     false &&
            //   props.keycloak.realmAccess.roles.includes('user') == false
            // ) {
            // $(document).ready(function() {
            //   $('.edit-button').attr('disabled', 'disabled');
            //   $('.edit-button').css('background', '#38416373');
            //   $('.edit-button').css('cursor', 'default');
            // });
            // }
            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll('.edit-button')[0];
              const uButton = eDiv.querySelectorAll('.update-button')[0];
              const cButton = eDiv.querySelectorAll('.cancel-button')[0];
              const dButton = eDiv.querySelectorAll('.delete-button')[0];

              // eButton.addEventListener('click', () => {
              //   alert(44);
              //   startEdit.gridApiParts.redrawRows();
              //   $(`.cancel-buttonp`).hide();
              //   $(`.edit-buttonp`).show();
              //   $(`.update-buttonp`).hide();
              //   $(`.delete-buttonp`).hide();
              //   let rowData = startEdit.state.oldDataArr;

              //   // startEdit.setState({
              //   //   editedRowId: null
              //   // });
              //   localStorage.setItem('index', index);
              //   startEdit.setState({ cancel: false });
              //   startEdit.onBtStartEditingParts(index);
              //   localStorage.setItem('fleetIdParts', params.data.id);
              //   let editingCells = params.api.getEditingCells();
              //   startEdit.setState({
              //     editedRowId: index
              //   });
              //   let oldArr = {
              //     index: index,
              //     partsmarkup: params.data.partsmarkup,
              //     id: params.data.id
              //   };
              //   var rowPrevArray = [];
              //   let indexArr = rowPrevArray.findIndex(
              //     ({ id }) => id == params.data.id
              //   );
              //   if (indexArr === -1) {
              //     rowPrevArray.push(oldArr);
              //   }params
              //   startEdit.setState({
              //     oldDataArr: rowPrevArray
              //   });
              //   $('#btneditp' + index).hide();
              //   $('#btndeletep' + index).hide();
              //   $('#btncancelp' + index).show();
              //   $('#btnupdatep' + index).show();
              // });

              eButton.addEventListener('click', () => {
                //alert(1);
                console.log('data value=', params.data);

                $(`.cancel-button`).hide();
                $(`.update-button`).hide();
                $(`.edit-button`).show();
                $(`.delete-button`).show();
                $('.laborFixedRateValue').hide();
                $('.doorRateValues').hide();
                $('.doorRateValue').hide();
                $('.fixedRateValue').hide();
                localStorage.setItem('oldId', params.data.id);
                localStorage.setItem('oldDoorRate', params.data.doorRate);
                localStorage.setItem(
                  'oldlaborFixedRate',
                  params.data.fleetRate
                );
                var oldRow = [];
                oldRow.push(params.data.id);
                oldRow.push(params.data.doorRate);
                oldRow.push(params.data.fleetRate);
                JSON.stringify(oldRow);
                localStorage.setItem('oldRow', oldRow);
                startEdit.setState({
                  editedRowId: index
                });
                startEdit.setState({
                  isCodeEdited: true
                });
                var rowPrev = {
                  id: params.data.id,
                  //doorRate: params.data.doorRate,
                  laborFixedRate: params.data.fleetRate,
                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                };

                startEdit.setState({
                  laborFixedRateNew: params.data.fleetRate
                  //doorRateNew: params.data.doorRate
                });
                var rowPrevArray = startEdit.state.prevCodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ id, laborFixedRate, doorRate }) =>
                    id === rowPrev.id &&
                    laborFixedRate === rowPrev.laborFixedRate
                  //&& doorRate === rowPrev.doorRate
                );
                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }
                startEdit.setState({
                  prevCodeArray: rowPrevArray
                });
                var prevRow = [];
                prevRow.push({
                  id: params.data.id,
                  laborFixedRate: params.data.fleetRate,
                  //doorRate: params.data.doorRate,
                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                });
                startEdit.setState({
                  oldCodeArray: prevRow
                });
                startEdit.setState({
                  laborfixedRateError: null
                  //doorRateError: null
                });
                localStorage.setItem('fleetId', params.data.id);
                //startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);
                $(`#btndelete${index}`).hide();
                $(`#btnedit${index}`).hide();
                $(`#btncancel${index}`).show();
                $(`#btnupdate${index}`).show();
              });
              uButton.addEventListener('click', () => {
                //alert(2);
                console.log(
                  'enter===1',
                  startEdit.state.laborFixedRateNew,
                  '==',
                  startEdit.state.doorRateNew
                );
                if (
                  startEdit.state.laborFixedRateNew &&
                  startEdit.state.laborFixedRateNew != 0 &&
                  startEdit.state.laborFixedRateNew != '' &&
                  startEdit.state.laborFixedRateNew != '0'
                  //&& startEdit.state.doorRateNew &&
                  // startEdit.state.doorRateNew != 0 &&
                  //startEdit.state.doorRateNew != '' &&
                  // startEdit.state.doorRateNew != '0'
                ) {
                  console.log('enter===2');
                  startEdit.onBtStopEditing(index);
                  //alert(111);
                  localStorage.setItem('newId', params.data.id);
                  localStorage.setItem(
                    'newLaborFixedRate',
                    params.data.fleetRate
                  );
                  const storeId = JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0];
                  startEdit.setState({
                    isCodeEdited: false
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(
                    params.rowIndex
                  );
                  rowNode.data['laborFixedRate'] =
                    startEdit.state.laborFixedRateNew;
                  rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                  startEdit.setState({
                    prevIndex: ''
                  });
                  var newRow = [];
                  newRow.push(
                    params.data.id,
                    params.data.fleetRate,
                    //params.data.doorRate,
                    storeId
                  );
                  let oldDataArr = lodash.filter(
                    startEdit.state.prevCodeArray,
                    item => {
                      return (
                        item.laborFixedRate == params.data.fleetRate &&
                        // item.doorRate == params.data.doorRate &&
                        item.id == params.data.id
                      );
                    }
                  );
                  var oldData = [];
                  oldData = startEdit.state.oldCodeArray;
                  let lbr = params.data.fleetRate;
                  lbr = parseFloat(lbr).toFixed(2);
                  // let dr = params.data.doorRate;
                  // dr = parseFloat(dr).toFixed(2);
                  if (
                    oldData[0].id == params.data.id &&
                    oldData[0].laborFixedRate != lbr
                  ) {
                    localStorage.setItem('newRow', newRow);
                    startEdit.deleteFleet(params.data, 'labor', 'update');
                  }
                  startEdit.setState({
                    laborfixedRateError: null,
                    doorRateError: null
                  });

                  startEdit.setState({
                    editedRowId: null
                  });
                  startEdit.gridApiLabor.redrawRows();
                  $(`#btnedit${index}`).show();
                  $(`#btncancel${index}`).hide();
                  $(`#btnupdate${index}`).hide();
                } else {
                  var rowNode = params.api.getDisplayedRowAtIndex(
                    params.rowIndex
                  );
                  rowNode.data['laborFixedRate'] =
                    startEdit.state.laborFixedRateNew;
                  rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                  startEdit.setState({
                    laborfixedRateError: null,
                    doorRateError: null
                  });
                  console.log('enter===33');
                  if (
                    startEdit.state.laborFixedRateNew == null ||
                    startEdit.state.laborFixedRateNew == '' ||
                    startEdit.state.laborFixedRateNew == '0' ||
                    startEdit.state.laborFixedRateNew == 0
                  ) {
                    if (
                      startEdit.state.doorRateNew == null ||
                      startEdit.state.doorRateNew == '' ||
                      startEdit.state.doorRateNew == '0' ||
                      startEdit.state.doorRateNew == 0
                    ) {
                      console.log('enter===63');
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      rowNode.data['doorRate'] = null;
                      rowNode.data['laborFixedRate'] = null;
                      startEdit.setState({
                        laborfixedRateError: index,
                        doorRateError: index
                      });
                      if (params.data.laborgridOrNot == 1) {
                        params.api.refreshCells({
                          columns: ['doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      } else {
                        params.api.refreshCells({
                          columns: ['doorRate', 'laborFixedRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      }
                    } else {
                      console.log('enter===73', params.data.laborgridOrNot);
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      if (params.data.laborgridOrNot == 1) {
                        console.log('enter===733');
                        // rowNode.data['laborFixedRate'] = startEdit.state.laborFixedRateNew;
                        rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                        startEdit.setState({
                          laborfixedRateError: null,
                          doorRateError: null
                        });

                        params.api.refreshCells({
                          columns: ['doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                        startEdit.deleteFleet(params.data, 'labor', 'update');

                        // startEdit.setState({
                        //   laborfixedRateError: null,
                        //   doorRateError: null
                        // });
                        startEdit.setState({
                          editedRowId: null
                        });
                        startEdit.gridApiLabor.redrawRows();
                        $(`#btnedit${index}`).show();
                        $(`#btncancel${index}`).hide();
                        $(`#btnupdate${index}`).hide();
                      } else {
                        console.log('enter===7333');
                        rowNode.data['laborFixedRate'] = null;
                        rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                        startEdit.setState({
                          laborfixedRateError: index,
                          doorRateError: null
                        });
                        params.api.refreshCells({
                          columns: ['laborFixedRate', 'doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      }
                    }
                  } else {
                    if (
                      startEdit.state.doorRateNew == null ||
                      startEdit.state.doorRateNew == '' ||
                      startEdit.state.doorRateNew == '0' ||
                      startEdit.state.doorRateNew == 0
                    ) {
                      console.log(
                        'enter===65',
                        startEdit.state.laborFixedRateNew
                      );
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      rowNode.data['doorRate'] = null;
                      rowNode.data['laborFixedRate'] =
                        startEdit.state.laborFixedRateNew;
                      startEdit.setState({
                        laborfixedRateError: null,
                        doorRateError: index
                      });
                      if (params.data.laborgridOrNot == 1) {
                        params.api.refreshCells({
                          columns: ['doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      } else {
                        params.api.refreshCells({
                          columns: ['laborFixedRate', 'doorRate'],
                          rowNodes: [params.node],
                          force: true
                        });
                      }
                    } else {
                      console.log('enter===75');
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      rowNode.data['laborFixedRate'] =
                        startEdit.state.laborFixedRateNew;
                      rowNode.data['doorRate'] = startEdit.state.doorRateNew;
                      startEdit.setState({
                        laborfixedRateError: null,
                        doorRateError: null
                      });
                      // params.api.refreshCells({
                      //   columns: ['doorRate'],
                      //   rowNodes: [params.node],
                      //   force: true
                      // });
                    }
                  }
                }
              });
              cButton.addEventListener('click', () => {
                // alert(3);
                startEdit.onBtStopEditing(index);
                //alert(77);
                startEdit.setState({
                  isCodeEdited: true
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return o.id == params.data.id;
                });
                if (valArr.length > 0) {
                  rowNode.setDataValue('fleetRate', valArr[0].laborFixedRate);
                  //rowNode.setDataValue('doorRate', valArr[0].doorRate);
                  rowNode.data['fleetRate'] = valArr[0].laborFixedRate;
                  // rowNode.data['doorRate'] = valArr[0].doorRate;
                }
                startEdit.setState({
                  editedRowId: null
                });
                params.api.refreshCells({
                  // columns: ['laborFixedRate', 'doorRate'],
                  columns: ['fleetRate'],
                  rowNodes: [rowNode],
                  force: true
                });
                let filteredArray = startEdit.state.newCodeArray.filter(
                  function(obj) {
                    return obj.id != params.data.id;
                  }
                );
                startEdit.setState({
                  newCodeArray: filteredArray
                });
                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                }
                startEdit.setState({
                  laborfixedRateError: null,
                  doorRateError: null
                });
                startEdit.gridApiLabor.redrawRows();
                $(`#btnedit${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
                $(`#btndelete${index}`).show();
              });
              dButton.addEventListener('click', function() {
                var selectedId = params.data.id;
                if (selectedId != '') {
                  startEdit.deleteFleet(params.data, 'labor', 'delete');
                }
                $('#btnedit' + index).show();
                $('#btncancel' + index).hide();
                $('#btnupdate' + index).hide();
                $('#btndelete' + index).show();
              });
            }
            return eDiv;
          }
        }
        // {
        //   headerName: 'Action',
        //   //cellRenderer: 'buttonRenderer',
        //   filter: false,
        //   // tooltip(params) {
        //   //   return 'Edit';
        //   // },
        //   width: 90,
        //   editable: false,
        //   sortable: false,
        //
        //   suppressMenu: true,
        //   // hide:
        //   //   // eslint-disable-next-line react/destructuring-assignment
        //   //   !!(
        //   //     typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
        //   //     this.props.keycloak.realmAccess.roles.includes('client') === true
        //   //   ),
        //   cellStyle: params => {
        //     return startEdit.state.isReloaded
        //       ? {
        //           'pointer-events': 'none',
        //           opacity: '0.4',
        //           textAlign: 'center'
        //         }
        //       : { textAlign: 'center' };
        //   },
        //   // cellStyle() {
        //   //   return { textAlign: 'center', border: ' 0px white' };
        //   // },
        //   // eslint-disable-next-line no-dupe-keys
        //   cellRenderer(params) {
        //     const index = params.rowIndex;
        //     const eDiv = document.createElement('div');
        //     // let context = this;
        //     let bgcolor;
        //     if (!startEdit.state.isOpcodeUpdated) {
        //       bgcolor = '#384163';
        //     } else {
        //       bgcolor = '#384163';
        //     }

        //     if (
        //       typeof params.value == 'undefined' &&
        //       typeof params.data != 'undefined'
        //     ) {
        //       if (!params.data) {
        //         eDiv.innerHTML = `<button title="Edit" id="btnedit${index}" style="background: ${bgcolor}; color: #fff; display:none;disabled; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Confirm" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-check"></i></button>`;
        //       } else {
        //         eDiv.innerHTML = `<button  title="Edit" id="btnedit${index}" style="background: ${bgcolor}; color: #fff; border-radius: 3px; width: 30px;disabled; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Confirm" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-check"></i></button>`;
        //       }
        //     }

        //     if (
        //       props.keycloak &&
        //       props.keycloak.realmAccess.roles.includes('admin') == false &&
        //       props.keycloak.realmAccess.roles.includes('user') == false
        //     ) {
        //       $(document).ready(function() {
        //         $('.edit-button').attr('disabled', 'disabled');
        //         $('.edit-button').css('background', '#38416373');
        //       });
        //     }

        //     return eDiv;
        //   }
        // }
      ],
      columnDefsParts: [
        // {
        //   headerName: 'Door Rate',
        //   field: 'doorRate',
        //   width: 90,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'right', border: ' 0px white' };
        //   },
        // },

        {
          headerName: 'Fleet Name',
          field: 'fleetName',
          width: 130,
          tooltipField: 'fleetName',
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },

        {
          headerName: 'Parts Matrix',
          field: 'fleetFlag',
          width: 130,
          tooltipField: 'fleetFlag',
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Parts Matrix',
          hide: true,
          field: 'laborgridOrNot',
          width: 80,
          suppressMenu: false,
          unSortIcon: false,
          flex: 1,
          cellStyle: function(params) {
            return { textAlign: 'center', border: ' 0px white' };
          },
          cellRenderer: params => {
            var index = params.rowIndex;

            if (typeof params.value != 'undefined') {
              return `<input type='checkbox' value=${
                params.value
              } disabled="disabled"
              id="laborgridOrNot${index}"
              ${params.value == 1 ? 'checked' : ''} />`;
            } else {
              return '';
            }
          }
        },
        {
          headerName: 'Markup',
          width: 240,
          flex: 1,
          field: 'partsmarkup',

          // cellEditorFramework: FixedRateRenderer,
          cellRenderer: 'fixedRateRenderer',

          suppressMenu: true,
          unSortIcon: false,
          sortable: false,
          hide: false,

          editable: !(
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') === true
          ),
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Source',
          hide: true,
          field: 'partsource',
          width: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Install Date',
          field: 'installDate',
          valueFormatter: this.formatCellValueDate,
          width: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },

        {
          headerName: 'Period',
          field: 'gridOrder',
          valueFormatter: this.formatCellValuePeriod,
          width: 100,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          cellStyle: this.cellStyles,
          width: 100,
          flex: 1,
          // hide: true,
          filter: false,

          suppressMenu: true,
          sortable: false,
          editable: false,
          // tooltip: function(params) {
          //   return 'Edit';
          // },
          cellRenderer(params) {
            console.log('paramssssss0000', params);
            var index = params.rowIndex;
            var eDiv = document.createElement('div');
            console.log('params===', params);
            if (params.data.id == '' && params.data.name == '') {
              eDiv.innerHTML =
                '<button  title="Edit44" id="btneditp' +
                index +
                '" style="background: #384163; color: #fff; display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-buttonp"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancelp' +
                index +
                '" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-buttonp" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatep' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-buttonp" ><i class="fas fa-save"></i></button>&nbsp;<button title="Delete" id="btndeletep' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-buttonp" ><i class="fas fa-trash-alt"></i></button>';
            } else {
              eDiv.innerHTML =
                '<button  title="Edit44" id="btneditp' +
                index +
                '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-buttonp"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancelp' +
                index +
                '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-buttonp" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatep' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-buttonp" ><i class="fas fa-save"></i></button>&nbsp;<button title="Delete" id="btndeletep' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-buttonp" ><i class="fas fa-trash-alt"></i></button>';
            }
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-buttonp').attr('disabled', 'disabled');
                $('.edit-buttonp').css('background', '#38416373');
                $('.edit-buttonp').css('cursor', 'default');
              });
              // $('.editNickName').attr('disabled', true);
            }
            if (index != undefined) {
              var eButton = eDiv.querySelectorAll('.edit-buttonp')[0];
              var uButton = eDiv.querySelectorAll('.update-buttonp')[0];
              var cButton = eDiv.querySelectorAll('.cancel-buttonp')[0];
              var dButton = eDiv.querySelectorAll('.delete-buttonp')[0];

              eButton.addEventListener('click', () => {
                startEdit.gridApiParts.redrawRows();
                $(`.cancel-buttonp`).hide();
                $(`.edit-buttonp`).show();
                $(`.update-buttonp`).hide();
                $(`.delete-buttonp`).hide();
                let rowData = startEdit.state.oldDataArr;

                // startEdit.setState({
                //   editedRowId: null
                // });
                localStorage.setItem('index', index);
                startEdit.setState({ cancel: false });
                startEdit.onBtStartEditingParts(index);
                localStorage.setItem('fleetIdParts', params.data.id);
                let editingCells = params.api.getEditingCells();
                startEdit.setState({
                  editedRowId: index
                });
                let oldArr = {
                  index: index,
                  partsmarkup: params.data.partsmarkup,
                  id: params.data.id
                };
                var rowPrevArray = [];
                let indexArr = rowPrevArray.findIndex(
                  ({ id }) => id == params.data.id
                );
                if (indexArr === -1) {
                  rowPrevArray.push(oldArr);
                }
                startEdit.setState({
                  oldDataArr: rowPrevArray
                });
                $('#btneditp' + index).hide();
                $('#btndeletep' + index).hide();
                $('#btncancelp' + index).show();
                $('#btnupdatep' + index).show();
              });

              uButton.addEventListener('click', () => {
                startEdit.onBtStopEditingParts(index);

                var rowNode = params.api.getDisplayedRowAtIndex(
                  params.rowIndex
                );
                //alert(startEdit.state.partsFixedRateNew);

                params.data.partsmarkup = startEdit.state.partsFixedRateNew;

                if (
                  startEdit.state.partsFixedRateNew == null ||
                  startEdit.state.partsFixedRateNew == '' ||
                  startEdit.state.partsFixedRateNew == '0'
                ) {
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.data['partsmarkup'] = null;
                  startEdit.setState({
                    partsfixedRateError: index
                  });
                  // var rowNode = params.api.getDisplayedRowAtIndex(index);
                  // rowNode.data['laborFixedratevalue'] = null;
                  params.api.refreshCells({
                    columns: ['partsmarkup'],
                    rowNodes: [params.node],
                    force: true
                  });
                }

                if (
                  startEdit.state.partsFixedRateNew == null ||
                  startEdit.state.partsFixedRateNew == '' ||
                  startEdit.state.partsFixedRateNew == 'N/A' ||
                  (startEdit.state.partsFixedRateNew != null &&
                    startEdit.state.partsFixedRateNew.split(' ')[1] == '')
                ) {
                  startEdit.setState({
                    partsfixedRateError: index
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.data['partsmarkup'] =
                    startEdit.state.partsFixedRateNew.split(' ')[0] + ' ';
                  params.api.refreshCells({
                    columns: ['partsmarkup'],
                    rowNodes: [params.node],
                    force: true
                  });

                  return false;
                }

                console.log('ccccc1====', params.data);
                var selectedId = params.data.id;
                console.log('selectedId=', selectedId);
                var selectedName = params.data.name;
                var selectedAdvisor = params.data.serviceadvisor;
                var nickName = params.data.nickname;
                nickName = nickName ? nickName.trim() : '';
                var advisorStatus = params.data.active;
                startEdit.setState({ cancel: false });
                startEdit.setState({
                  editedRowId: null
                });
                let arr = startEdit.state.oldDataArr;
                startEdit.deleteFleet(params.data, 'parts', 'update');
                if (
                  arr[0].nickname != params.data.nickname ||
                  arr[0].active != params.data.active
                ) {
                  let arrNickname = params.data.nickname
                    ? params.data.nickname.trim()
                    : '';
                  let arrNickname1 = arr[0].nickname
                    ? arr[0].nickname.trim()
                    : '';
                  console.log('nick==', arrNickname, '===', arrNickname1);

                  // if (
                  //   arrNickname != arrNickname1 ||
                  //   arr[0].active != params.data.active
                  // ) {
                  //   startEdit.updateServiceAdvisor(
                  //     selectedId,
                  //     selectedName,
                  //     selectedAdvisor,
                  //     nickName ? nickName.trim() : '',
                  //     advisorStatus
                  //   );
                  //   startEdit.setState({
                  //     oldDataArr: []
                  //   });
                  // }
                }
                startEdit.gridApiParts.redrawRows();
                $('#btneditp' + index).show();
                $('#btncancelp' + index).hide();
                $('#btnupdatep' + index).show();
                $('#btndeletep' + index).hide();

                startEdit.setState({
                  partsfixedRateError: null
                });
              });
              cButton.addEventListener('click', function() {
                startEdit.setState({ cancel: true });
                startEdit.onBtStopEditingParts(index);
                //alert(4444);
                startEdit.setState({
                  isCodeEdited: true
                });
                startEdit.setState({
                  editedRowId: null
                });

                let rowData = startEdit.state.oldDataArr;

                var rowNode = params.api.getDisplayedRowAtIndex(index);
                console.log('rowNode======', rowNode);
                rowNode.setDataValue('partsmarkup', rowData[0].partsmarkup);
                params.api.refreshCells({
                  columns: ['partsmarkup'],
                  rowNodes: [rowNode],
                  force: true
                });

                $('#btneditp' + index).show();
                $('#btncancelp' + index).hide();
                $('#btnupdatep' + index).hide();
                $('#btndeletep' + index).show();
              });
              dButton.addEventListener('click', function() {
                var selectedId = params.data.id;
                if (selectedId != '') {
                  startEdit.deleteFleet(params.data, 'parts', 'delete');
                }
                $('#btneditp' + index).show();
                $('#btncancelp' + index).hide();
                $('#btnupdatep' + index).hide();
                $('#btndeletep' + index).show();
              });
            }
            return eDiv;
          }
        }
      ],

      filter: 'agSetColumnFilter',
      editType: 'fullRow',
      frameworkComponents: {
        gridCheckboxRenderer: GridCheckboxRenderer,
        fixedRateRenderer: FixedRateRenderer
      },
      context: { componentParent: this },
      rowData: [],
      fleetLaborArr: [],
      fleetPartsArr: [],
      selectedFleetNames: [{ customerName: 'All' }],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      overlayNoRowsTemplateLabor:
        '<span style="padding: 10px; font-size: 12.5px;">No Data Found</span>',
      overlayNoRowsTemplate:
        '<span style="padding: 10px; font-size: 12.5px;">No Data Found</span>',
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          dataType: 'DateTime',
          numberFormat: {
            format: 'mm/dd/yy'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  deleteFleetConfirmed = () => {
    this.deleteFleet(
      this.state.dataForDelete,
      this.state.dataTypeDelete,
      'delete'
    );
  };

  deleteFleetConfirmedCancel = () => {
    this.setState({ dataDeleteConfirm: false });
  };

  deleteFleet = (data, type, process) => {
    this.setState({ dataForDelete: data });
    this.setState({ dataTypeDelete: type });

    console.log('data value==', data, type, process);
    let laborArr = [];
    let partsArr = [];

    let fleet_rate_grid = [];
    let fleet_rate_parts = [];
    let fleet_exists_labor_grid = [];
    let fleet_exists_parts_matrix = [];

    let markup = '';
    if (this.state.partsMatrix == true) {
      markup = this.state.partsMarkup;
    } else {
      markup = this.state.partsMarkup + '' + this.state.markupValue;
    }

    let fleettype =
      this.state.searchStatus == 'customer' ? 'customer' : 'paytype';
    let fleetname =
      this.state.searchStatus == 'customer'
        ? this.state.selectedFleet
        : this.state.selectedPayTypeOptionType;

    if (type == 'labor') {
      fleet_rate_grid.push({
        process: process,
        fleettype: fleettype,
        fleetname: data.fleetName,
        fleetorgridflag: 'Fixed Rate',
        laborfleetrate: data.fleetRate,
        gridinstalldate: data.installDate
      });
    }

    if (type == 'parts') {
      fleet_rate_parts.push({
        process: process,
        fleettype: fleettype,
        fleetname: data.fleetName,
        fleetormatrixflag: 'Fixed Rate',
        partsmarkup: data.partsmarkup,
        partsource: null,
        matrixinstalldate: data.installDate
      });
    }

    // Open Material-UI Dialog for confirmation if process is delete
    if (process === 'delete' && this.state.dataDeleteConfirm == false) {
      this.setState({ openConfirmationDialog: true });
      this.setState({ dataDeleteConfirm: true });
      return; // Wait for user confirmation
    }

    insertFleetDetails(
      JSON.stringify(fleet_rate_grid),
      JSON.stringify(fleet_rate_parts),
      JSON.stringify(fleet_exists_labor_grid),
      JSON.stringify(fleet_exists_parts_matrix),
      result => {
        console.log('success=====', result);
        if (
          result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount.string ==
          'Success'
        ) {
          this.getAgGridData();
          this.gridApiLabor.redrawRows();
          this.gridApiParts.redrawRows();
          this.clearLaborGrid();
          this.clearPartsGrid();
          this.setState({ dataDeleteConfirm: false });
          if (process == 'update') {
            this.setState({ openSnackbarEdit: true });
          } else {
            this.setState({ openSnackbarDelete: true });
          }
        } else {
          this.setState({ openSnackbarError: true });
        }
      }
    );
  };
  geDeptForDrilldown = () => {
    let data = ['Cost', 'Cost+', 'List', 'List-', 'List+'];
    return data;
  };
  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
    console.log('groupColumn', groupColumn);
    // if (
    //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
    //   this.props.keycloak.realmAccess.roles.length >= 1 &&
    //   this.props.keycloak.realmAccess.roles.includes('client') === false
    // ) {
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = true;
    groupColumn[4]['editable'] = false;
    groupColumn[5]['editable'] = false;
    // }
    this.state.rawGridApiLabor.setColumnDefs(groupColumn);

    this.state.rawGridApiLabor.columnController.columnDefs[1].editable = true;
    const { rawGridApiLabor } = this.state;
    this.state.rawGridApiLabor.setFocusedCell(
      index,
      // 'laborFixedRate',
      'fleetRate',
      pinned
    );
    this.state.rawGridApiLabor.startEditingCell({
      rowIndex: index,
      // colKey: 'laborFixedRate',
      colKey: 'fleetRate',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  // onBtStartEditing = (index, key, char, pinned) => {
  //   // columnController
  //   const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
  //   console.log('groupColumn===', groupColumn);
  //   groupColumn[0]['editable'] = false;
  //   groupColumn[1]['editable'] = false;
  //   groupColumn[2]['editable'] = true;
  //   groupColumn[3]['editable'] = false;
  //   this.state.rawGridApiLabor.setColumnDefs(groupColumn);
  //   this.state.rawGridApiLabor.columnController.columnDefs[1].editable = true;
  //   this.state.rawGridApiLabor.setFocusedCell(
  //     index,
  //     'laborFixedRate',
  //     // 'doorRate',
  //     pinned
  //   );
  //   this.state.rawGridApiLabor.startEditingCell({
  //     rowIndex: index,
  //     colKey: 'laborFixedRate',
  //     // colKey: 'doorRate',
  //     rowPinned: pinned,
  //     keyPress: key,
  //     charPress: char
  //   });
  // };
  onBtStopEditing = () => {
    //alert(4);
    this.gridApiLabor.stopEditing();
    const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;
    groupColumn[5]['editable'] = false;
    this.state.rawGridApiLabor.setColumnDefs(groupColumn);
  };
  onBtStartEditingParts = (index, key, char, pinned) => {
    // columnController
    const groupColumn = this.state.rawGridApiParts.columnController.columnDefs;
    console.log('groupColumn===', groupColumn);
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = true;
    groupColumn[3]['editable'] = false;
    this.state.rawGridApiParts.setColumnDefs(groupColumn);
    this.state.rawGridApiParts.columnController.columnDefs[1].editable = true;
    this.state.rawGridApiParts.setFocusedCell(index, 'partsmarkup', pinned);
    this.state.rawGridApiParts.startEditingCell({
      rowIndex: index,
      colKey: 'partsmarkup',
      // colKey: 'doorRate',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onBtStopEditingParts = () => {
    this.gridApiParts.stopEditing();
    const groupColumn = this.state.rawGridApiParts.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;
    groupColumn[5]['editable'] = false;
    this.state.rawGridApiParts.setColumnDefs(groupColumn);
  };
  formatCellValueRate = params => {
    if (params.value && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValuePeriod = params => {
    if (params.value && params.value != '') {
      return params.value == 1
        ? 'Current'
        : 'Prior' + (Number(params.value) - 1);
    } else {
      return '';
    }
  };
  resetRawData = () => {
    this.gridApiLabor.setColumnDefs([]);
    this.gridApiLabor.setColumnDefs(this.state.columnDefs);
    this.gridApiParts.setColumnDefs([]);
    this.gridApiParts.setColumnDefs(this.state.columnDefsParts);
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Labor Grid(s)',
      fileName: 'Labor Grid(s)',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Labor Grid(s)'
            },
            mergeAcross: 3
          }
        ],
        [
          {
            data: {
              type: 'String',
              value:
                this.state.selectedGrid == 1 ? 'Current Grid' : 'Prior Grid'
            },
            mergeAcross: 1
          },
          {
            data: {
              type: 'String',
              value: 'Grid / Door Install Date  : ' + this.state.gridDate
            },
            mergeAcross: 2
          },
          {
            data: {
              type: 'String',
              value: '1 Hour Door Rate  : ' + this.state.doorRate
            }
          }
        ],
        []
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onGridReadyLabor = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApiLabor: params.api });
    this.setState({ gridApiLabor: params });
    this.gridApiLabor = params.api;
    this.setState({ gridColumnApiLabor: params.columnApi });
    this.gridApiLabor.sizeColumnsToFit();
    this.getAgGridData();
  };
  onGridReadyParts = params => {
    this.setState({ rawGridApiParts: params.api });
    this.setState({ gridApiParts: params });
    this.gridApiParts = params.api;

    this.setState({ gridColumnApiParts: params.columnApi });
    this.getAgGridData();
  };
  getAgGridData() {
    this.setState({ isLoading: true });
    let fleetArr = [];

    let fleettype = '';
    if (this.state.searchStatus == 'customer') {
      fleettype = 'customer';
      if (this.state.selectedFleet == '') {
        fleetArr.push('All');
      } else {
        fleetArr.push(this.state.selectedFleet);
      }
    } else {
      fleettype = 'paytype';
      if (this.state.selectedPayTypeOptionType == '') {
        fleetArr.push('All');
      } else {
        fleetArr.push(this.state.selectedPayTypeOptionType);
      }
    }

    this.getFleetListItemsList(fleetArr, fleettype);
  }

  getFleetListItemsList = (fleetArr, fleettype) => {
    getFleetListItems(fleetArr, fleettype, result => {
      let data = result;

      let resultArr = [];
      let laborArr = [];
      let partsArr = [];
      if (
        result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
          .length > 0
      ) {
        this.setState({ isLoading: false });
        result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes.map(
          (obj, i) => {
            if (obj.laborOrParts == 'labor') {
              laborArr.push(obj);
            } else {
              partsArr.push(obj);
            }
          }
        );
        this.setState({
          fleetLaborArr: laborArr
        });
        // se
        this.setState({
          fleetPartsArr: partsArr
        });
        console.log('partsArr===', partsArr);
        // setFleetsParts(partsArr);

        // setFleets(
        //   result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
        // );
      } else {
        this.setState({
          fleetLaborArr: []
        });
        this.setState({
          fleetPartsArr: []
        });
        this.setState({ isLoading: false });
      }
    });
  };
  handleclick = params => {
    this.props.history.push({
      pathname: '/LaborMisses',
      state: {
        selectedFilter: this.props.history.location.state.selectedFilter,
        selectedToggle: this.props.history.location.state.selectedToggle,
        selectedMonthYear: this.props.history.location.state.selectedMonthYear,
        parent: this.props.history.location.state.parent,
        previousToggle: this.props.history.location.state.previousToggle,
        payType: this.props.history.location.state.payType,
        gridType: this.props.history.location.state.gridType,
        previousPayType: this.props.history.location.state.PrevPayType,
        previousGridType: this.props.history.location.state.PrevGridType,
        showAllJobs: this.state.showAllJobs
      }
    });
  };

  onPartsFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedRateOld: oldVal
    });

    this.setState({
      partsFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };

  handleAddRecord = event => {
    if (
      this.state.selectedCustomer !== '' &&
      this.state.searchStatus == 'customer'
    ) {
      var rowArray = this.state.selectedCustomerNamesExist;
      let found =
        rowArray &&
        rowArray.some(el => el.customerName == this.state.selectedCustomer);
      let rowArr = {
        customerName: this.state.selectedCustomer
      };
      if (!found) {
        rowArray && rowArray.push(rowArr);
        insertFleetNameToList(
          this.state.selectedCustomer,
          'insert',
          'cust_fleet',
          result => {
            console.log(
              'cccaaa',
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName
            );
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
            ) {
              this.setState({
                setFleetOpen: true
              });
              $('#select-fleet-account').click();
            } else {
              this.setState({
                setFleetOpen: false
              });
            }
          }
        );
        //this.fetchFleetPayTypeNames('cust_fleet');
        this.setState({
          selectedCustomerNamesExist: rowArray
        });

        this.setState({
          selectedFleetError: false
        });
      } else {
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    } else if (
      this.state.selectedPayTypeOption !== '' &&
      this.state.searchStatus == 'paytype'
    ) {
      var rowArray = this.state.selectedPayTypeNamesExist;
      let found = rowArray.some(
        el => el.payTypeName == this.state.selectedPayTypeOption
      );
      let rowArr = {
        payTypeName: this.state.selectedPayTypeOption
      };

      if (!found) {
        rowArray.push(rowArr);

        console.log('rowArray', rowArray);

        insertFleetNameToList(
          this.state.selectedPayTypeOption,
          'insert',
          'paytype_fleet',
          result => {
            console.log(
              'cccaaa',
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName
            );
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
                .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
            ) {
              this.setState({
                setFleetOpen: true
              });
              $('#select-fleet-account').click();
            } else {
              this.setState({
                setFleetOpen: false
              });
            }
          }
        );
        this.setState({
          selectedPayTypeNamesExist: rowArray
        });

        this.setState({
          selectedFleetError: false
        });
      } else {
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    } else if (
      this.state.selectedFleet !== 'All' ||
      this.state.selectedPayTypeOptionType !== 'All'
    ) {
      var rowArray = [];
      let found;
      let rowArr;
      if (this.state.searchStatus == 'customer') {
        rowArray = this.state.selectedCustomerNamesExist;
        found = rowArray.some(
          el => el.customerName == this.state.selectedFleet
        );

        rowArr = {
          customerName: this.state.selectedFleet
        };
      } else {
        rowArray = this.state.selectedPayTypeNamesExist;
        console.log('rowArray', rowArray);
        found = rowArray.some(
          el => el.payTypeName == this.state.selectedPayTypeOptionType
        );

        rowArr = {
          payTypeName: this.state.selectedPayTypeOptionType
        };
      }

      if (found) {
        let items = [];
        if (this.state.searchStatus == 'customer') {
          items = rowArray.filter(
            item => item.customerName !== this.state.selectedFleet
          );
          this.setState({ selectedCustomerNamesExist: items });
          this.setState({ selectedFleet: 'All' });
        } else {
          items = rowArray.filter(
            item => item.payTypeName !== this.state.selectedPayTypeOptionType
          );
          this.setState({ selectedPayTypeNamesExist: items });
          this.setState({ selectedPayTypeOptionType: 'All' });
        }

        let pTypeRemove = '';
        if (this.state.searchStatus == 'customer') {
          pTypeRemove = 'cust_fleet';
        } else {
          pTypeRemove = 'paytype_fleet';
        }

        let fleetRemove = '';
        if (this.state.searchStatus == 'customer') {
          fleetRemove = this.state.selectedFleet;
        } else {
          fleetRemove = this.state.selectedPayTypeOptionType;
        }

        insertFleetNameToList(fleetRemove, 'delete', pTypeRemove, result => {
          console.log(
            'cccaaa===',
            result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
              .statelessCcPhysicalRwKpiFleetMasters[0].fleetName,
            '===',
            items
          );
          if (
            result.data.statelessCcPhysicalRwInsertOrUpdateFleetMaster
              .statelessCcPhysicalRwKpiFleetMasters[0].fleetName == 'Success'
          ) {
            if (this.state.searchStatus == 'customer') {
              this.setState({ selectedFleet: 'All' });
            } else {
              this.setState({ selectedPayTypeOptionType: 'All' });
              //this.setState({ selectedPayTypeNames: 'All' });

              this.fetchFleetPayTypeNames('paytype_fleet');
              //this.fetchFleetPayTypeNames('paytype_fleet_exists');
              //this.setState({ selectedPayTypeNames: 'All' });
            }
            this.setState({
              setFleetOpen: true
            });
            $('#select-fleet-account').click();
          } else {
            this.setState({
              setFleetOpen: false
            });
          }
        });
        // this.setState({
        //   selectedFleetNames: rowArray
        // });
        this.setState({
          selectedFleetError: false
        });
      } else {
        $('#select-fleet-account').click();
        this.setState({
          selectedFleetError: true
        });
      }
    }
  };
  createFleetAccount = event => {
    if (
      this.state.selectedFleet != 'All' ||
      this.state.selectedPayTypeOption != 'All'
    ) {
      this.setState({ addNewFleet: true });
    } else {
      this.setState({ fleetNameError: true });
    }
  };
  handleRadioChange = event => {
    this.setState({ gridRateValue: event.target.value });
    if (event.target.value == 'fixedRate') {
      this.setState({ fixedRate: true });
      this.setState({ laborGrid: false });
    } else {
      this.setState({ laborGrid: true });
      this.setState({ fixedRate: false });
    }
  };
  handleRadioChangeStart = event => {
    this.setState({ radioCheckedDefault: false });
    this.setState({ gridRateValueStart: event.target.value });
    this.setState({ searchStatus: event.target.value });
    this.setState({ selectedFleet: 'All' });
    this.setState({ selectedPayTypeOptionType: 'All' });
    this.setState({ selectedCustomer: '' });
    this.setState({ selectedPayTypeOption: '' });

    this.getFleetListItemsList(['All'], event.target.value);
    //this.setState({ gridRateValue: event.target.value });
    // if (event.target.value == 'customerType') {
    //   this.setState({ fixedRate: true });
    //   this.setState({ laborGrid: false });
    // } else {
    //   this.setState({ laborGrid: true });
    //   this.setState({ fixedRate: false });
    // }
  };
  handleMatrixChange = event => {
    this.setState({ matrixRateValue: event.target.value, matrixChanged: true });

    if (event.target.value == 'partsFixedRate') {
      this.setState({ partsFixedRate: true });
      this.setState({ partsMatrix: false });
    } else {
      this.setState({ partsMatrix: true });
      this.setState({ partsFixedRate: false });
    }
  };
  handleCancel = event => {
    this.setState({ addNewRow: false });
  };
  handleChange = (event, newValue) => {
    if (this.state.searchStatus == 'customer') {
      this.setState({ selectedCustomer: newValue });
    } else {
      this.setState({ selectedPayTypeOption: newValue });
    }
    this.setState({ selectedPayTypeOptionType: false });
    this.setState({ selectedFleetError: false });
    this.setState({ selectedFleet: 'All' });
  };

  resetFleet = () => {
    // this.setState({ typeStatus: true });
    //this.setState({ gridRateValueStart: 'customer' });
    this.getFleetListItemsList(['All'], 'customer');
    this.setState({ searchStatus: 'customer' });
    this.setState({ gridRateValueStart: 'customer' });

    this.setState({
      selectedCustomer: ''
    });
    this.setState({
      selectedPayTypeOption: ''
    });
    this.setState({ selectedFleet: 'All' });
    this.setState({ selectedPayTypeOptionType: 'All' });

    this.gridApiLabor.setSortModel(null);
    this.gridApiParts.setSortModel(null);
    this.gridApiLabor.setFilterModel(null);
    this.gridApiParts.setFilterModel(null);
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApiLabor.redrawRows();
    this.gridApiParts.redrawRows();
  };
  clearFirstRow = () => {
    this.setState({ typeStatus: false });
    this.setState({ searchStatus: 'customer' });
    this.setState({ responseStatus: false });
    this.setState({ gridRateValueStart: false });
    this.setState({ selectedFleet: '' });
  };
  clearLaborGrid = () => {
    // this.setState({ laborGrid: false });
    this.setState({ fixedRate: true });
    this.setState({ gridRateValue: 'fixedRate' });
    this.setState({ laborGrid: false });
    this.setState({ fixedLaborRate: '' });
    this.setState({ fixedDoorRate: '' });
    this.setState({ doorRate: '' });
    this.setState({ gridInstalledDate: new Date() });
    this.setState({ doorRateChanged: false });
    this.setState({ fixedDoorRateChanged: false });
    this.setState({ fixedRateChanged: false });
  };
  clearPartsGrid = () => {
    this.setState({ matrixRateValue: 'partsFixedRate' });
    this.setState({ partsFixedRate: true });
    this.setState({ partsMatrix: false });
    this.setState({ markupValue: '' });
    this.setState({ partsMarkup: 'Cost' });
    this.setState({ partsSource: this.state.partsSourceArray[0] });
    this.setState({ matrixInstalledDate: new Date() });
    this.setState({ markupChanged: false });
    this.setState({ matrixChanged: false });
    this.setState({ markupValueChanged: false });
  };
  handleChangeFleet = (event, newValue) => {
    console.log('newValue', newValue);
    if (newValue != null) {
      this.setState({
        setFleetOpen: false
      });
      this.setState({
        selectedFleetError: false
      });
      this.setState({
        selectedCustomer: ''
      });
      this.setState({
        selectedPayTypeOption: ''
      });

      // this.setState({ selectedFleet: newValue });
      this.gridApiLabor.showLoadingOverlay();
      this.gridApiParts.showLoadingOverlay();

      let selected = [];
      if (newValue != 'All') {
        selected.push(newValue);
      } else {
        selected.push('All');
      }

      let fleettype = '';
      if (this.state.searchStatus == 'customer') {
        fleettype = 'customer';
        this.setState({ selectedFleet: false });
        this.setState({ selectedFleet: newValue });
      } else {
        fleettype = 'paytype';

        this.setState({ selectedPayTypeOptionType: false });
        this.setState({ selectedPayTypeOptionType: newValue });
        // this.setState({ selectedPayTypeOption: false });
        // this.setState({ selectedPayTypeOptionType: false });
        // this.setState({ selectedPayTypeOption: newValue });
        // this.setState({ selectedPayTypeOptionType: newValue });
      }
      getFleetListItems(selected, fleettype, result => {
        let data = result;

        this.setState({ isLoading: false });
        let resultArr = [];
        let laborArr = [];
        let partsArr = [];
        if (
          result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
            .length > 0
        ) {
          result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes.map(
            (obj, i) => {
              if (obj.laborOrParts == 'labor') {
                laborArr.push(obj);
              } else {
                partsArr.push(obj);
              }
            }
          );
          this.setState({
            fleetLaborArr: laborArr
          });
          // se
          this.setState({
            fleetPartsArr: partsArr
          });
          this.setState({ isLoading: false });
          this.gridApiLabor.hideOverlay();
          this.gridApiParts.hideOverlay();
          this.gridApiLabor.redrawRows();
          this.gridApiParts.redrawRows();
          // setFleetsParts(partsArr);

          // setFleets(
          //   result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
          // );
        } else {
          this.gridApiLabor.hideOverlay();
          this.gridApiParts.hideOverlay();
          this.setState({
            fleetLaborArr: []
          });
          this.setState({
            fleetPartsArr: []
          });
          this.setState({ isLoading: false });
        }
      });
    }
  };
  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
  };

  hidesnackbarEdit = () => {
    this.setState({ openSnackbarEdit: false });
  };
  hidesnackbarDelete = () => {
    this.setState({ openSnackbarDelete: false });
  };
  hidesnackbarError = () => {
    this.setState({ openSnackbarError: false });
  };
  handleSave = () => {
    let laborArr = [];
    let errorArr = [];
    let typeChecking = '';
    if (this.state.searchStatus == 'customer') {
      typeChecking = this.state.selectedFleet;
    } else {
      typeChecking = this.state.selectedPayTypeOptionType;
    }
    if (typeChecking == '' || typeChecking == 'All') {
      errorArr.push('fleetNameError');
    }

    this.setState({
      errorItems: errorArr
    });
    let doorVal = '';
    if (this.state.laborGrid == true) {
      doorVal = this.state.doorRate;
    } else {
      doorVal = this.state.fixedDoorRate;
    }

    // both side fixed rate taken

    let fleet_rate_grid = [];
    let fleet_rate_parts = [];
    let fleet_exists_labor_grid = [];
    let fleet_exists_parts_matrix = [];

    let markup = '';
    if (this.state.partsMatrix == true) {
      markup = this.state.partsMarkup;
    } else {
      markup = this.state.partsMarkup + ' ' + this.state.markupValue;
    }

    let process = 'insert';
    let fleettype =
      this.state.searchStatus == 'customer' ? 'customer' : 'paytype';
    let fleetname =
      this.state.searchStatus == 'customer'
        ? this.state.selectedFleet
        : this.state.selectedPayTypeOptionType;

    if (this.state.fixedRate == true) {
      fleet_rate_grid.push({
        process: process,
        fleettype: fleettype,
        fleetname: fleetname,
        fleetorgridflag: 'Fixed Rate',
        laborfleetrate:
          this.state.fixedLaborRate == '' ? 0 : this.state.fixedLaborRate,
        gridinstalldate:
          this.state.gridInstalledDate == ''
            ? null
            : moment(this.state.gridInstalledDate).format('YYYY-MM-DD')
      });
    }
    if (this.state.laborGrid == true) {
      // alert('laborgrid');
    }
    if (this.state.partsFixedRate == true) {
      fleet_rate_parts.push({
        process: process,
        fleettype: fleettype,
        fleetname: fleetname,
        fleetormatrixflag: 'Fixed Rate',
        partsmarkup: markup,
        partsource: null,
        matrixinstalldate:
          this.state.matrixInstalledDate == ''
            ? null
            : moment(this.state.matrixInstalledDate).format('YYYY-MM-DD')
      });
    }
    if (this.state.partsMatrix == true) {
      //alert('matrix');
    }

    // process: insert/update/delete
    // 							fleettype: customer/paytype
    // 							fleetname: <Fleet Name/Pay Type Name>
    // 							fleetorgridflag: fleet/grid
    // 							laborfleetrate: <Fleet Rate>
    // 							gridinstalldate: <Fleet Install Date></Fleet>

    // if (
    //   this.state.gridInstalledDate != '' &&
    //   (this.state.fixedLaborRate != '' || this.state.doorRate != '')
    // ) {
    //   laborArr.push({
    //     process: 'insert',
    //     fleetname: this.state.selectedFleet,
    //     laborgridornot: this.state.laborGrid == true ? 1 : 0,
    //     laborfixedrate:
    //       this.state.fixedLaborRate == '' ? 0 : this.state.fixedLaborRate,
    //     doorrate: doorVal == '' ? 0 : doorVal,
    //     gridinstalldate:
    //       this.state.gridInstalledDate == ''
    //         ? null
    //         : moment(this.state.gridInstalledDate).format('YYYY-MM-DD')
    //   });
    // }

    //let partsArr = [];
    // let markup = '';
    // if (this.state.partsMatrix == true) {
    //   markup = this.state.partsMarkup;
    // } else {
    //   markup = this.state.partsMarkup + '' + this.state.markupValue;
    // }

    // if (
    //   this.state.matrixInstalledDate != '' &&
    //   (this.state.markupChanged ||
    //     this.state.matrixChanged ||
    //     this.state.markupValueChanged)
    // ) {
    //   partsArr.push({
    //     process: 'insert',
    //     fleetname: this.state.selectedFleet,
    //     partsmatrixornot: this.state.partsMatrix == true ? 1 : 0,
    //     partsmarkup: markup,
    //     partsource: this.state.partsSource,
    //     matrixinstalldate:
    //       this.state.matrixInstalledDate == ''
    //         ? null
    //         : moment(this.state.matrixInstalledDate).format('YYYY-MM-DD')
    //   });
    // }

    if (errorArr.length <= 0) {
      insertFleetDetails(
        JSON.stringify(fleet_rate_grid),
        JSON.stringify(fleet_rate_parts),
        JSON.stringify(fleet_exists_labor_grid),
        JSON.stringify(fleet_exists_parts_matrix),
        result => {
          console.log('success data====', result);
          if (
            result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
              .string == 'Success'
          ) {
            this.getAgGridData();
            this.gridApiLabor.redrawRows();
            this.gridApiParts.redrawRows();
            this.clearLaborGrid();
            this.clearPartsGrid();
            this.setState({ openSnackbar: true });
          } else {
            this.setState({ openSnackbarError: true });
          }
        }
      );
    }

    // insertFleetDetails(
    //   JSON.stringify(laborArr),
    //   JSON.stringify(partsArr),
    //   result => {
    //     console.log('fff--', result);
    //   }
    // );
  };
  handleChangePayType = event => {
    this.setState({ selectedPayType: event.target.value }, function() {
      this.getAgGridData();
    });
  };
  handleCallback = (start, end, label) => {
    this.setState({
      gridInstalledDate: start.format('MM/DD/YY')
    });
  };
  handleCallbackParts = (start, end, label) => {
    this.setState({
      matrixInstalledDate: start.format('MM/DD/YY')
    });
  };
  onCellClicked = params => {
    const id = localStorage.getItem('oldId');
    let rowId = this.state.editedRowId;
    console.log(
      'rowId===',
      rowId,
      '====',
      id,
      '==',
      params.data.id,
      '=',
      params.data.id != id
    );
    if (params.data.id != id) {
      console.log('enter=1');
      $(`.cancel-button`).hide();
      $(`.edit-button`).show();
      $(`.update-button`).hide();
      $(`#btncancel` + rowId).click();
      $('.laborFixedRateValue').hide();
      $('.doorRateValues').hide();
      $('.doorRateValue').hide();
      $('.fixedRateValue').hide();
      $('.laborFixedRate').attr('disabled', 'disabled');
    } else {
      console.log('enter=12');
      $(`#btncancel${rowId}`).show();
      $(`#btnupdate${rowId}`).show();
      $(`#btnedit${rowId}`).hide();
    }
  };
  onRowEditingStarted(params) {
    console.log('pa=pa=', params.data.laborgridOrNot);
    if (params.data.laborgridOrNot == 1) {
      params.api.refreshCells({
        columns: ['doorRate'],
        rowNodes: [params.node],
        force: true
      });
    } else {
      params.api.refreshCells({
        columns: ['laborFixedRate', 'doorRate'],
        rowNodes: [params.node],
        force: true
      });
    }
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['laborFixedRate', 'doorRate'],
      rowNodes: [params.node],
      force: true
    });
  }
  onLaborFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedRateOld: oldVal
    });
    this.setState({
      laborFixedRateNew: newVal
    });
  };
  onDoorRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      doorRateOld: oldVal
    });
    this.setState({
      doorRateNew: newVal
    });
  };
  partsMatrixPopup = event => {
    this.setState({
      showPartsMatrix: !this.state.showPartsMatrix
    });
  };
  laborGridPopup = event => {
    this.setState({
      showLaborGrid: !this.state.showLaborGrid
    });
  };

  render() {
    const { classes } = this.props;
    var gridDate = '';
    var doorRate = '';
    var storeDate = '';
    var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    // if (this.state.rowData.length > 0) {
    //   gridDate = moment(this.state.rowData[0].createdDate).format('MM/DD/YY');
    //   doorRate = '$' + this.state.rowData[0].doorRate;
    //   storeDate = moment(this.state.rowData[0].storeInstallDate).format(
    //     'MM/DD/YY'
    //   );
    // }
    console.log('sss', this.state.isFleetNamesLoaded);
    return this.state.isFleetNamesLoaded == false ? (
      <div>
        <Box style={{ padding: 25 }}>
          <LinearProgress color="secondary" />
          <Typography
            variant="h6"
            align="center"
            style={{ padding: 25 }}
            color="primary"
          >
            Processing...
          </Typography>
        </Box>
      </div>
    ) : (
      <>
        <div>
          <Page title={'Fleet Accounts'}></Page>
          <Paper
            square
            style={{
              // margin: 8,
              marginTop: '40px',
              paddingTop: '6px',
              height: '40px',
              margin: '8px 8px 8px',
              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
            }}
          >
            <Grid
              container
              className={clsx(this.props.titleContainer, 'reset-dashboard')}
            >
              <Grid
                item
                xs={5}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                {this.props.history &&
                this.props.history.location.state &&
                this.props.history.location.state.pageType == 'LaborMisses' ? (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.handleclick}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                ) : (
                  ''
                )}
              </Grid>
              <Grid
                item
                xs={5}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <Typography
                  variant="h4"
                  color="primary"
                  className={clsx(this.props.mainLabel, 'main-title')}
                >
                  Fleet Accounts
                </Typography>
              </Grid>
              <Grid
                item
                xs={2}
                style={{ display: 'flex', justifyContent: 'end' }}
              ></Grid>
            </Grid>
          </Paper>

          <div className="fleetClass">
            <Paper
              square
              style={{
                margin: 8,
                height: 65,
                marginLeft: 8,
                alignItems: 'center',
                paddingLeft: 4,
                display: 'flex',
                gap: 10
              }}
            >
              <Grid container spacing={2}>
                <Grid item xs={12} sm={3}>
                  <Grid container spacing={2} style={{ marginLeft: '0px' }}>
                    {this.state.typeStatus == true && (
                      <Grid item xs={6}>
                        <RadioGroup
                          aria-label="quiz1"
                          name="quiz1"
                          value={this.state.gridRateValueStart}
                          onChange={this.handleRadioChangeStart}
                        >
                          <div
                            style={{
                              display: 'flex',
                              justifyContent: 'space-around'
                            }}
                          >
                            <FormControlLabel
                              value="customer"
                              classes={{
                                root: 'radioLabel'
                              }}
                              style={{ height: 25, width: 125 }}
                              control={<Radio />}
                              label="By Customer"
                            />
                            <FormControlLabel
                              value="paytype"
                              classes={{
                                root: 'radioLabel'
                              }}
                              style={{ height: 25, width: 120 }}
                              control={<Radio />}
                              label="By PayType"
                            />
                          </div>
                        </RadioGroup>
                      </Grid>
                    )}
                  </Grid>
                </Grid>
                <Grid item xs={12} sm={1}></Grid>

                {this.state.gridRateValueStart != false && (
                  <Grid item xs={12} sm={8}>
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <div
                          style={{
                            display: 'flex',
                            justifyContent: 'flex-end'
                          }}
                        >
                          <CustomAutocomplete
                            id="select-all-account"
                            freeSolo
                            size="small"
                            style={{
                              width: '38%',

                              fontSize: 12
                            }}
                            // {...console.log('sss', this.state.setFleetOpen)}
                            // open={this.state.setFleetOpen}
                            value={
                              this.state.searchStatus == 'customer'
                                ? this.state.selectedCustomer
                                : this.state.selectedPayTypeOption
                            }
                            openOnFocus={true}
                            //disabled={this.state.selectedFleetNames.length > 0 ? false : true}
                            onChange={this.handleChange}
                            options={
                              typeof this.state.customerNames != 'undefined' &&
                              this.state.customerNames != '' &&
                              this.state.customerNames.length > 0 &&
                              this.state.searchStatus == 'customer'
                                ? this.state.customerNames.map(
                                    (customerName, i) =>
                                      customerName != null &&
                                      customerName.customerName != null
                                        ? customerName.customerName
                                        : ''
                                  )
                                : typeof this.state.selectedPayTypeNames !=
                                    'undefined' &&
                                  this.state.selectedPayTypeNames != '' &&
                                  this.state.selectedPayTypeNames.length > 0 &&
                                  this.state.searchStatus == 'paytype'
                                ? this.state.selectedPayTypeNames.map(
                                    (selectedPayTypeNames, i) =>
                                      selectedPayTypeNames != null &&
                                      selectedPayTypeNames.payTypeName != null
                                        ? selectedPayTypeNames.payTypeName
                                        : '**********'
                                  )
                                : []
                            }
                            renderInput={params => (
                              <TextField
                                {...params}
                                label={
                                  this.state.searchStatus == 'customer'
                                    ? 'Search All Account'
                                    : 'Search All Paytype'
                                }
                                variant="outlined"
                                error={
                                  this.state.selectedFleetError ? true : false
                                }
                                helperText={
                                  this.state.selectedFleetError
                                    ? 'Selected Fleet already exists'
                                    : ''
                                }
                                // autoComplete={true}
                                // placeholder={
                                //   this.state.fleetNameError ? 'Select one fleet account' : ''
                                // }
                                InputProps={{
                                  ...params.InputProps,

                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <SearchIcon />
                                    </InputAdornment>
                                  ),
                                  disableUnderline: true
                                }}
                              />
                            )}
                          />

                          <Button
                            //className={clsx('reset-btn')}
                            title="Add Fleet Account"
                            variant="contained"
                            color="primary"
                            className={clsx(classes.back, 'reset-btn')}
                            onClick={this.handleAddRecord}
                            style={{ top: '5px', marginLeft: '7px' }}
                          >
                            <SwapHorizIcon style={{ top: '5px' }} />
                            {/* <Typography variant="body1" align="left">
              Add
            </Typography> */}
                          </Button>
                          <CustomAutocomplete
                            id="select-fleet-account"
                            freeSolo
                            size="small"
                            style={{
                              width: '38%',
                              alignContent: 'center',
                              fontSize: 12
                            }}
                            // {...console.log('sss', this.state.setFleetOpen)}
                            // open={this.state.setFleetOpen}
                            value={
                              this.state.searchStatus == 'customer'
                                ? this.state.selectedFleet
                                : this.state.selectedPayTypeOptionType
                            }
                            openOnFocus={true}
                            //disabled={this.state.selectedFleetNames.length > 0 ? false : true}
                            onChange={this.handleChangeFleet}
                            options={
                              this.state.selectedCustomerNamesExist &&
                              this.state.selectedCustomerNamesExist.length >
                                0 &&
                              typeof this.state.selectedCustomerNamesExist !=
                                'undefined' &&
                              this.state.selectedCustomerNamesExist != '' &&
                              this.state.searchStatus == 'customer'
                                ? this.state.selectedCustomerNamesExist.map(
                                    (customerName, i) =>
                                      customerName != null &&
                                      customerName.customerName != null
                                        ? customerName.customerName
                                        : ''
                                  )
                                : typeof this.state.selectedPayTypeNamesExist !=
                                    'undefined' &&
                                  this.state.searchStatus == 'paytype' &&
                                  this.state.selectedPayTypeNamesExist.length >
                                    0
                                ? this.state.selectedPayTypeNamesExist.map(
                                    (customerName, i) =>
                                      customerName != null &&
                                      customerName.payTypeName != null
                                        ? customerName.payTypeName
                                        : ''
                                  )
                                : []
                              //: [{ customerName: 'Loading', id: 0 }]  selectedPayTypeNames
                            }
                            renderInput={params => (
                              <TextField
                                {...params}
                                label="Fleet Accounts"
                                variant="outlined"
                                error={this.state.fleetNameError ? true : false}
                                // autoComplete={true}
                                // placeholder={
                                //   this.state.fleetNameError ? 'Select one fleet account' : ''
                                // }
                                InputProps={{
                                  ...params.InputProps,

                                  endAdornment: (
                                    <InputAdornment position="end">
                                      <SearchIcon />
                                    </InputAdornment>
                                  ),
                                  disableUnderline: true
                                }}
                              />
                            )}
                          />
                          <Button
                            //className={clsx('reset-btn')}
                            title="Select Fleet Account"
                            variant="contained"
                            color="primary"
                            className={clsx(classes.back, 'reset-btn')}
                            onClick={this.createFleetAccount}
                            style={{ top: '5px', marginLeft: '7px' }}
                          >
                            <AddIcon />
                            {/* <Typography variant="body1" align="left">
              Add
            </Typography> */}
                          </Button>
                        </div>
                      </Grid>
                      <Grid item xs={4}>
                        <Grid xs={12} className={classes.flexGrid}>
                          <Typography
                            variant="subtitle2"
                            color="primary"
                            className={clsx(this.props.mainLabel, 'sub-title')}
                          >
                            Selected:{' '}
                          </Typography>

                          <Typography
                            variant="subtitle1"
                            color="primary"
                            className={clsx(this.props.mainLabel, 'sub-title')}
                          >
                            {this.state.searchStatus == 'customer'
                              ? this.state.selectedFleet
                              : this.state.selectedPayTypeOptionType}
                          </Typography>
                        </Grid>
                      </Grid>

                      {/* New Grid item with width of 1 */}
                      <Grid item xs={2}>
                        <div>
                          <Button
                            title="Save"
                            className={clsx('reset-btn', 'btnClass')}
                            variant="contained"
                            color="primary"
                            style={{
                              marginLeft: 5,
                              width: '55px',
                              height: '24px',
                              fontSize: '15px'
                            }}
                            disabled={
                              this.state.doorRateChanged ||
                              this.state.fixedDoorRateChanged ||
                              this.state.fixedRateChanged ||
                              this.state.markupChanged ||
                              this.state.matrixChanged ||
                              this.state.markupValueChanged
                                ? false
                                : true
                            }
                            onClick={this.handleSave}
                          >
                            Save
                          </Button>
                          <Button
                            title="Reset"
                            className={clsx('reset-btn')}
                            style={{ marginLeft: 5, width: '55px' }}
                            variant="contained"
                            onClick={this.resetFleet}
                          >
                            Reset
                          </Button>
                        </div>
                      </Grid>
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </div>

          {this.state.addNewRow &&
          this.state.addNewFleet &&
          this.state.searchStatus != false &&
          (this.state.selectedFleet != 'All' ||
            this.state.selectedPayTypeOptionType != 'All') ? (
            <Paper
              square
              style={{
                margin: 8,
                height: 65,
                marginLeft: 8,
                alignItems: 'center',
                paddingLeft: 4,
                display: 'flex',
                pointerEvents: this.state.addNewFleet ? 'all' : 'none'
              }}
            >
              <Grid item xs={12} className={classes.textContainer}>
                <Grid
                  xs={6}
                  className={'fleet-grid'}
                  style={{ display: 'flex', alignItems: 'center' }}
                >
                  <RadioGroup
                    aria-label="quiz"
                    name="quiz"
                    value={this.state.gridRateValue}
                    onChange={this.handleRadioChange}
                  >
                    <FormControlLabel
                      value="fixedRate"
                      classes={{
                        root: 'radioLabel'
                      }}
                      style={{ height: 25, width: 110 }}
                      control={<Radio />}
                      label="Fixed Rate"
                    />
                    <FormControlLabel
                      value="laborGrid"
                      classes={{
                        root: 'radioLabel'
                      }}
                      style={{ height: 25, width: 120 }}
                      control={<Radio />}
                      label="Labor Grid"
                      onClick={this.laborGridPopup}
                    />
                  </RadioGroup>
                  {/* <Typography
                  variant="subtitle1"
                  color="primary"
                  className={clsx(this.props.mainLabel, 'sub-title')}
                >
                  Labor Grid
                </Typography> */}
                  <FormControl
                    size="small"
                    className={classes.margin}
                    variant="outlined"
                    style={{ display: this.state.laborGrid ? 'block' : 'none' }}
                  >
                    <InputLabel htmlFor="outlined-adornment-amount">
                      Door Rate
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-amount"
                      // value={values.amount}

                      error={
                        this.state.errorItems.includes('doorRateError')
                          ? true
                          : false
                      }
                      value={this.state.doorRate}
                      onChange={e =>
                        (e.target.value === '' || regEx.test(e.target.value)) &&
                        this.setState({
                          doorRate: e.target.value,
                          doorRateChanged: true
                        })
                      }
                      //onChange={handleChange('amount')}
                      startAdornment={
                        <InputAdornment position="start">$</InputAdornment>
                      }
                      labelWidth={120}
                    />
                    {/* <FormHelperText
                  style={{
                    color: 'red',
                    display: this.state.doorRateError ? 'block' : 'none'
                  }}
                  id="my-helper-text"
                >
                  Enter door rate
                </FormHelperText> */}
                  </FormControl>
                  <FormControl
                    size="small"
                    className={classes.margin}
                    variant="outlined"
                    style={{ display: this.state.fixedRate ? 'block' : 'none' }}
                  >
                    <InputLabel htmlFor="outlined-adornment-amount">
                      Fixed Labor Rate
                    </InputLabel>
                    <OutlinedInput
                      id="outlined-adornment-amount"
                      // value={values.amount}
                      error={
                        this.state.errorItems.includes('fixedLaborRateError')
                          ? true
                          : false
                      }
                      value={this.state.fixedLaborRate}
                      onChange={e =>
                        (e.target.value === '' || regEx.test(e.target.value)) &&
                        this.setState({
                          fixedLaborRate: e.target.value,
                          fixedRateChanged: true
                        })
                      }
                      disabled={this.state.laborGrid ? true : false}
                      //onChange={handleChange('amount')}
                      startAdornment={
                        <InputAdornment position="start">$</InputAdornment>
                      }
                      labelWidth={120}
                    />
                  </FormControl>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={clsx(classes.formControl, 'input-container')}
                  >
                    <DateRangePicker
                      initialSettings={{
                        // maxDate: {
                        //   date: new Date(),
                        // },
                        // minDate:{
                        //   date: (this.props.selectedDates[1])
                        // },
                        locale: {
                          format: 'MM/DD/YY',
                          separator: ' - '
                        },
                        autoUpdateInput: true,
                        showDropdowns: true,
                        autoApply: true,
                        singleDatePicker: true,
                        //maxDate: moment().toDate(),
                        // alwaysShowCalendars: true,
                        applyClass: clsx(classes.calButton, 'apply-btn'),
                        cancelClass: clsx(classes.calButton, 'apply-btn'),
                        startDate: new Date(this.state.gridInstalledDate)
                        //showDropdowns: true
                      }}
                      value={this.state.gridInstalledDate}
                      onCallback={this.handleCallback}
                    >
                      <input
                        type="text"
                        className="datepicker fleet-picker"
                        style={{
                          border: this.state.errorItems.includes(
                            'gridInstalledDateError'
                          )
                            ? '1px solid red;'
                            : '1px solid #c0c0c0;'
                        }}
                        id="picker"
                        name="picker"
                        aria-labelledby="label-picker"
                      />
                    </DateRangePicker>
                    <label
                      class="labelpicker fleet-picker"
                      for="picker"
                      id="label-picker"
                    >
                      <div class="textpicker fleet-picker">Install Date</div>
                    </label>
                  </FormControl>
                  {/* <FormControl
                  size="small"
                  className={classes.margin}
                  variant="outlined"
                >
                  <Typography style={{ fontSize: 10 }} sx={{ mt: 2, mb: 1 }}>
                    Install Date
                  </Typography>
                  <Datepicker
                    selected={this.state.gridInstalledDate}
                    onChange={date =>
                      this.setState({ gridInstalledDate: date })
                    }
                    error={
                      this.state.errorItems.includes('gridInstalledDateError')
                        ? true
                        : false
                    }
                    className={classes.datePickerInput}
                    dateFormat="MM/dd/yy"
                    // format={'MM-DD-YYYY'}
                    peekNextMonth
                    showMonthDropdown
                    showYearDropdown
                    dropdownMode="select"
                  />
                </FormControl> */}
                  <Button
                    title="Clear Labor Grid"
                    className={clsx('reset-btn')}
                    style={{ marginLeft: 5 }}
                    variant="contained"
                    onClick={this.clearLaborGrid}
                  >
                    Clear
                  </Button>
                </Grid>
                <Grid
                  xs={6}
                  className={'fleet-grid'}
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    //justifyContent: 'end',
                    paddingRight: '10px'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <RadioGroup
                      aria-label="quiz"
                      name="quiz"
                      value={this.state.matrixRateValue}
                      onChange={this.handleMatrixChange}
                    >
                      <FormControlLabel
                        value="partsFixedRate"
                        classes={{
                          root: 'radioLabel'
                        }}
                        style={{ height: 25, width: 110 }}
                        control={<Radio />}
                        label="Fixed Rate"
                      />
                      <FormControlLabel
                        value="partsMatrix"
                        classes={{
                          root: 'radioLabel'
                        }}
                        style={{ height: 25, width: 120 }}
                        control={<Radio />}
                        label="Parts Matrix"
                        onClick={this.partsMatrixPopup}
                      />
                    </RadioGroup>
                    <TextField
                      id="outlined-select-matrix-value"
                      select
                      size="small"
                      style={{
                        marginRight: 5,
                        width: '17%'
                        //display: this.state.partsMatrix ? 'block' : 'none'
                      }}
                      label="Parts Markup"
                      SelectProps={{
                        native: true
                      }}
                      classes={{
                        root: 'matrix-value'
                      }}
                      placeholder={'Select'}
                      value={this.state.partsMarkup}
                      onChange={e =>
                        this.setState({
                          partsMarkup: e.target.value,
                          markupChanged: true
                        })
                      }
                      //placeholder=" select your currency"
                      variant="outlined"
                    >
                      <option key={1} value={'Cost'}>
                        {'Cost'}
                      </option>
                      <option key={2} value={'Cost+'}>
                        {'Cost+'}
                      </option>
                      <option key={3} value={'List'}>
                        {'List'}
                      </option>
                      <option key={4} value={'List-'}>
                        {'List-'}
                      </option>
                      <option key={5} value={'List+'}>
                        {'List+'}
                      </option>
                    </TextField>
                    &nbsp;&nbsp;&nbsp;
                    <TextField
                      style={{
                        display: this.state.partsFixedRate ? 'block' : 'none',
                        marginRight: 5,
                        width: '10%'
                      }}
                      size="small"
                      InputLabelProps={{ shrink: true }}
                      className={classes.margin}
                      id="outlined-password-input"
                      label="%"
                      disabled={
                        this.state.partsMarkup == 'Cost' ||
                        this.state.partsMarkup == 'List'
                          ? true
                          : false
                      }
                      type="text"
                      variant="outlined"
                      error={
                        this.state.errorItems.includes('markupError')
                          ? true
                          : false
                      }
                      value={this.state.markupValue}
                      onChange={e =>
                        (e.target.value === '' || regEx.test(e.target.value)) &&
                        this.setState({
                          markupValue: e.target.value,
                          markupValueChanged: true
                        })
                      }
                    />
                    &nbsp;&nbsp;&nbsp;
                    {/* <TextField
                    id="outlined-select-matrix-value"
                    select
                    size="small"
                    style={{
                      marginRight: 5,
                      width: '17%'
                    }}
                    label="Parts Source"
                    SelectProps={{
                      native: true
                    }}
                    classes={{
                      root: 'matrix-value'
                    }}
                    error={
                      this.state.errorItems.includes('partsourceError')
                        ? true
                        : false
                    }
                    value={this.state.partsSource}
                    InputLabelProps={{ shrink: true }}
                    onChange={e =>
                      (e.target.value === '' || regEx.test(e.target.value)) &&
                      this.setState({ partsSource: e.target.value })
                    }
                    //placeholder=" select your currency"
                    variant="outlined"
                  >
                    {this.state.partsSourceArray.map((source, i) => (
                      <option key={source} value={source}>
                        {source}
                      </option>
                    ))}
                  </TextField> */}
                    <FormControl
                      variant="outlined"
                      margin="dense"
                      className={clsx(classes.formControl, 'input-container')}
                    >
                      <DateRangePicker
                        initialSettings={{
                          // maxDate: {
                          //   date: new Date(),
                          // },
                          // minDate:{
                          //   date: (this.props.selectedDates[1])
                          // },
                          locale: {
                            format: 'MM/DD/YY',
                            separator: ' - '
                          },
                          autoUpdateInput: true,
                          showDropdowns: true,
                          autoApply: true,
                          singleDatePicker: true,
                          //maxDate: moment().toDate(),
                          // alwaysShowCalendars: true,
                          applyClass: clsx(classes.calButton, 'apply-btn'),
                          cancelClass: clsx(classes.calButton, 'apply-btn'),
                          startDate: new Date(this.state.matrixInstalledDate)
                          //showDropdowns: true
                        }}
                        value={this.state.matrixInstalledDate}
                        onCallback={this.handleCallbackParts}
                      >
                        <input
                          type="text"
                          className="datepicker fleet-picker"
                          style={{
                            border: this.state.errorItems.includes(
                              'matrixInstalledDateError'
                            )
                              ? '1px solid red;'
                              : '1px solid #c0c0c0;'
                          }}
                          id="picker"
                          name="picker"
                          aria-labelledby="label-picker"
                        />
                      </DateRangePicker>
                      <label
                        class="labelpicker fleet-picker"
                        for="picker"
                        id="label-picker"
                      >
                        <div class="textpicker fleet-picker">Install Date</div>
                      </label>
                    </FormControl>
                    {/* <FormControl
                    size="small"
                    className={classes.margin}
                    variant="outlined"
                    style={{ marginRight: 10, width: '17%' }}
                  >
                    <Typography style={{ fontSize: 10 }} sx={{ mt: 2, mb: 1 }}>
                      Install Date
                    </Typography>
                    <Datepicker
                      selected={this.state.matrixInstalledDate}
                      error={
                        this.state.errorItems.includes(
                          'matrixInstalledDateError'
                        )
                          ? true
                          : false
                      }
                      onChange={date =>
                        this.setState({ matrixInstalledDate: date })
                      }
                      className={classes.datePickerInput}
                      dateFormat="MM/dd/yy"
                      // format={'MM-DD-YYYY'}
                      peekNextMonth
                      showMonthDropdown
                      showYearDropdown
                      dropdownMode="select"
                    />
                  </FormControl> */}
                    <Button
                      title="Clear Parts Grid"
                      className={clsx('reset-btn', 'btnClass')}
                      style={{ marginRight: 5 }}
                      variant="contained"
                      onClick={this.clearPartsGrid}
                    >
                      Clear
                    </Button>
                  </div>
                </Grid>
                {/* <Grid
                xs={1}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'end',
                  paddingRight: '10px'
                }}
              >
                
              </Grid> */}
              </Grid>
            </Paper>
          ) : (
            ''
          )}
          {this.state.isLoading == true ? (
            <div>
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            </div>
          ) : null}
          <Paper
            square
            style={{
              margin: 8,
              height: window.innerHeight - 250 + 'px',
              marginLeft: 8,
              display: this.state.isLoading != true ? 'block' : 'none',
              paddingLeft: 4,
              paddingRight: 4,
              display: 'flex'
            }}
          >
            <Grid xs={12} className={classes.textContainerGrid}>
              <Grid xs={6}>
                <div
                  className="ag-theme-balham fleet-container"
                  style={{
                    //height: '410px',//
                    // width: '700px',
                    height: window.innerHeight - 305 + 'px',
                    // height:(window.innerHeight-215)+'px',
                    alignContent: 'center',
                    marginLeft: '8px',
                    marginTop: 10,
                    display: this.state.isLoading == true ? 'none' : 'block'
                  }}
                >
                  <Typography
                    variant="h5"
                    color="primary"
                    className={clsx(this.props.mainLabel, classes.subTitle)}
                  >
                    Labor
                  </Typography>
                  <AgGridReact
                    className="ag-theme-balham grid-cell-centered "
                    style={{
                      height: '410px'
                    }}
                    onRowEditingStarted={this.onRowEditingStarted}
                    onRowEditingStopped={this.onRowEditingStopped}
                    onFilterChanged={this.onFilterChanged}
                    onSortChanged={this.onSortChanged}
                    getRowStyle={this.getRowStyle}
                    getChartToolbarItems={this.getChartToolbarItems}
                    modules={AllModules}
                    columnDefs={this.state.columnDefs}
                    defaultColDef={this.state.defaultColDef}
                    onGridReady={this.onGridReadyLabor}
                    suppressAggFuncInHeader
                    rowData={this.state.fleetLaborArr}
                    excelStyles={this.state.excelStyles}
                    editType={this.state.editType}
                    suppressClickEdit={true}
                    onCellClicked={this.onCellClicked}
                    floatingFilter={true}
                    //context={this.state.context}
                    //frameworkComponents={this.state.frameworkComponents}
                    suppressDragLeaveHidesColumns={true}
                    // context={this.state.context}
                    // frameworkComponents={this.state.frameworkComponents}
                    // animateRows={true}
                    // enableRangeSelection={false}
                    // enableCharts={true}
                    // modules={AllModules}
                    // columnDefs={this.state.columnDefs}
                    // excelStyles={this.state.excelStyles}
                    // defaultColDef={this.state.defaultColDef}
                    // onGridReady={this.onGridReadyLabor}
                    // suppressAggFuncInHeader={true}
                    // rowData={this.state.fleetLaborArr}
                    // sortingOrder={this.sortingOrder}
                    // tooltipShowDelay={0}
                    // floatingFilter={true}
                    // suppressRowClickSelection={true}
                    // headerHeight={this.state.headerHeight}
                    // onFilterChanged={this.onFilterChanged}
                    // suppressContextMenu={true}
                    // overlayNoRowsTemplate={this.state.overlayNoRowsTemplateLabor}
                    // editType={this.state.editType}
                    // onCellClicked={this.onCellClicked}
                    // onRowEditingStarted={this.onRowEditingStarted}
                    // onRowEditingStopped={this.onRowEditingStopped}
                    // suppressClickEdit={true}
                    //suppressHorizontalScroll={true}
                    suppressContextMenu={true}
                  />
                </div>
              </Grid>
              <Grid xs={6}>
                <div
                  className="ag-theme-balham fleet-container"
                  style={{
                    //height: '410px',//
                    // width: '100%',
                    // width: '715px',
                    height: window.innerHeight - 305 + 'px',
                    // height:(window.innerHeight-215)+'px',
                    alignContent: 'center',
                    marginLeft: '8px',
                    marginTop: 10,
                    display: this.state.isLoading == true ? 'none' : 'block'
                  }}
                >
                  <Typography
                    variant="h5"
                    color="primary"
                    className={clsx(this.props.mainLabel, classes.subTitle)}
                  >
                    Parts
                  </Typography>
                  <AgGridReact
                    className="ag-theme-balham"
                    style={{
                      height: '410px'
                    }}
                    animateRows={true}
                    enableRangeSelection={false}
                    enableCharts={true}
                    modules={AllModules}
                    columnDefs={this.state.columnDefsParts}
                    excelStyles={this.state.excelStyles}
                    defaultColDef={this.state.defaultColDef}
                    onGridReady={this.onGridReadyParts}
                    suppressAggFuncInHeader={true}
                    rowData={this.state.fleetPartsArr}
                    sortingOrder={this.sortingOrder}
                    tooltipShowDelay={0}
                    floatingFilter={true}
                    suppressRowClickSelection={true}
                    // headerHeight={this.state.headerHeight}
                    onFilterChanged={this.onFilterChanged}
                    suppressContextMenu={true}
                    overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
                    editType={this.state.editType}
                    suppressDragLeaveHidesColumns={true}
                    context={this.state.context}
                    frameworkComponents={this.state.frameworkComponents}
                    //suppressHorizontalScroll={true}
                  />
                </div>
              </Grid>
            </Grid>
          </Paper>
          {this.state.showPartsMatrix && (
            <PartsMatrixModel
              openDialog={this.state.showPartsMatrix}
              handleClose={this.partsMatrixPopup}
              gridRateValueStart={
                this.state.gridRateValueStart == 'customer'
                  ? 'cust_fleet'
                  : 'paytype_fleet'
              }
              fleetName={
                this.state.searchStatus == 'customer'
                  ? this.state.selectedFleet
                  : this.state.selectedPayTypeOptionType
              }
              typeFor={'Matrix'}
            />
          )}
          {this.state.showLaborGrid && (
            <PartsMatrixModel
              openDialog={this.state.showLaborGrid}
              handleClose={this.laborGridPopup}
              gridRateValueStart={
                this.state.gridRateValueStart == 'customer'
                  ? 'cust_fleet'
                  : 'paytype_fleet'
              }
              fleetName={
                this.state.searchStatus == 'customer'
                  ? this.state.selectedFleet
                  : this.state.selectedPayTypeOptionType
              }
              typeFor={'Grid'}
            />
          )}
          {/* {this.state.showLaborGrid && (
            <LaborGrid
              openDialog={this.state.showLaborGrid}
              handleClose={this.laborGridPopup}
              gridRateValueStart={
                this.state.gridRateValueStart == 'customer'
                  ? 'cust_fleet'
                  : 'paytype_fleet'
              }
              fleetName={
                this.state.searchStatus == 'customer'
                  ? this.state.selectedFleet
                  : this.state.selectedPayTypeOptionType
              }
              typeFor={'grid'}
            />
          )} */}
        </div>
        <Snackbar
          open={this.state.openSnackbar}
          autoHideDuration={6000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbar} severity="success">
            Fleet Details Added Successfully!
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarEdit}
          autoHideDuration={6000}
          onClose={this.hidesnackbarEdit}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarEdit} severity="success">
            Fleet Details Updated Successfully!
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarError}
          autoHideDuration={6000}
          onClose={this.hidesnackbarError}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarError} severity="error">
            Something Went Wrong!
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarDelete}
          autoHideDuration={6000}
          onClose={this.hidesnackbarDelete}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbarDelete} severity="success">
            Fleet Details Deleted Successfully!
          </Alert>
        </Snackbar>
        <Dialog
          open={this.state.openConfirmationDialog}
          onClose={() => {
            this.setState({ openConfirmationDialog: false });
            this.deleteFleetConfirmedCancel();
          }}
        >
          <DialogTitle>Confirm Deletion</DialogTitle>
          <DialogContent>
            Are you sure you want to delete this fleet?
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialog: false });
                this.deleteFleetConfirmedCancel();
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                this.setState({ openConfirmationDialog: false });
                // Call your function to delete the fleet
                this.deleteFleetConfirmed();
              }}
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </>
    );
  }
}
const CustomAutocomplete = withStyles({
  inputRoot: {
    paddingRight: '5px !important',
    '&[class*="MuiFilledInput-root"]': {}
  }
})(Autocomplete);
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 210,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right'
    // marginTop: 7
  },
  dataAsOf: {
    marginRight: 32,
    float: 'left',
    marginTop: 1,
    marginLeft: '1%'
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3,
    marginRight: 8
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  inputText: {
    width: 125,
    textAlign: 'left'
  },
  inputTextRate: {
    width: 97,
    textAlign: 'left'
  },
  inputValue: {
    marginRight: 11
  },
  input: {
    margin: '0px 5px',
    width: '200px'
  },
  margin: {
    width: '22%',
    marginLeft: 5
  },
  dataContainer: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  textContainer: {
    alignItems: 'center',
    display: 'flex',
    gap: 10,
    paddingLeft: 10
  },
  textContainerGrid: {
    display: 'flex',
    gap: 10
    //paddingLeft: 10
  },
  flexGrid: {
    display: 'flex',
    alignItems: 'center',
    gap: 5
  },
  inputRoot: {
    paddingRight: 2
  },
  subTitle: {
    paddingRight: 5,
    paddingBottom: 5
  },
  datePickerInput: {
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px',
    width: '100%',
    marginBottom: 20
  }
});

export default withStyles(styles)(withKeycloak(FleetAccounts));
