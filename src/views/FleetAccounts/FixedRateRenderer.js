import React, { Component } from 'react';
import { FormControl, TextField } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputAdornment from '@material-ui/core/InputAdornment';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import clsx from 'clsx';
import moment from 'moment';
import 'src/input-style.css';
import { TransferWithinAStationOutlined } from '@material-ui/icons';

class FixedRateRenderer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      laborFixedRate: this.props.data.laborFixedRate,
      laborfixedValue: this.props.data.laborFixedRate,
      doorRate: this.props.data.doorRate,
      doorRateValue: this.props.data.doorRate,
      isValid:
        this.props.data.partsFixedratevalue != null &&
        this.props.data.partsFixedratevalue != 'N/A'
          ? this.props.data.partsFixedratevalue.split(' ')[0] == 'Cost' ||
            this.props.data.partsFixedratevalue.split(' ')[0] == 'List'
            ? true
            : false
          : true
    };
    this.checkedHandler = this.checkedHandler.bind(this);
    this.partsCheckedHandler = this.partsCheckedHandler.bind(this);
    this.handleFixedRateChange = this.handleFixedRateChange.bind(this);
    this.handleDoorRateChange = this.handleDoorRateChange.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;
    // if (this.props.column.colId == 'laborFixedRate') {
    //   this.props.node.setDataValue(colId, checked == true ? 1 : 0);
    // }
    if (this.props.data.laborFixedRate == 1) {
      this.props.api.refreshCells({
        columns: ['laborFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
    } else {
      this.props.api.refreshCells({
        columns: ['laborFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  partsCheckedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'partsFixedRate') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
    if (this.props.data.partsFixedRate == 1 && checked == true) {
      this.props.api.refreshCells({
        columns: ['partsFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
      this.props.context.componentParent.onPartsFixedDateChanged(
        this.props.data.fixedRateDate,
        this.props.data.fixedRateDate == null || this.props.fixedRateDate == ''
          ? moment(new Date()).format('MM/DD/YY')
          : this.props.data.fixedRateDate
      );
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.fixedRateValue,
        'Cost'
      );
    } else {
      this.props.api.refreshCells({
        columns: ['partsFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  handleFixedRateChange(event) {
    let colId = this.props.column.colId;
    this.setState({
      fixedValue: event.target.value
    });
    if (this.props.column.colId == 'laborFixedRate') {
      this.props.context.componentParent.onLaborFixedRateChanged(
        this.props.data.laborFixedRate,
        event.target.value
      );
    }
  }
  handleDoorRateChange(event) {
    let colId = this.props.column.colId;
    // this.setState({
    //   fixedValue: event.target.value
    // });
    if (this.props.column.colId == 'doorRate') {
      this.props.context.componentParent.onDoorRateChanged(
        this.props.data.doorRate,
        event.target.value
      );
    }
  }
  render() {
    const { classes } = this.props;
    console.log(
      'data=====',
      this.props.context.componentParent.state.editedRowId,
      '==',
      this.props.rowIndex,
      '====',
      this.props.context.componentParent.state.laborfixedRateError,
      '=11=',
      this.props.context.componentParent.state.doorRateError
    );
    var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    var regExParts = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    return this.props.colDef.field == 'laborFixedRate' ? (
      <div
        style={{
          display:
            this.props.context.componentParent.state.editedRowId != null
              ? 'block'
              : 'inline-flex',
          gap: 3,
          marginTop:
            this.props.context.componentParent.state.editedRowId != null
              ? '-2px'
              : '1px',
          alignItems: 'center'
        }}
      >
        <FormControl
          variant="outlined"
          className={'laborFixedRateValue'}
          disabled={
            this.props.context.componentParent.state.editedRowId != null
              ? false
              : 'true'
          }
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? 'block'
                : 'none'
          }}
        >
          <OutlinedInput
            className={clsx(classes.formControl)}
            id="outlined-adornment-weight"
            title="Labor Fixed Rate($)"
            value={this.state.laborfixedValue}
            error={
              this.props.context.componentParent.state.laborfixedRateError ==
              null
                ? false
                : this.props.context.componentParent.state
                    .laborfixedRateError == this.props.rowIndex
                ? true
                : false
            }
            onBlur={this.handleFixedRateChange}
            onChange={e =>
              (e.target.value === '' || regEx.test(e.target.value)) &&
              this.setState({
                laborfixedValue: e.target.value
              })
            }
            // onChange={this.handleFixedRateChange}
            startAdornment={
              <InputAdornment
                classes={{ root: classes.adorment }}
                position="start"
                disableTypography={true}
              >
                $
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
        // style={{
        //   display:
        //     this.props.context.componentParent.state.editedRowId != null
        //       ? 'block'
        //       : 'none'
        // }}
        >
          {this.props.data.laborFixedRate != null &&
            this.props.data.laborFixedRate != '' &&
            // this.props.data.laborFixedRate != 0 &&
            '$' + this.props.data.laborFixedRate}
        </span>
      </div>
    ) : this.props.colDef.field == 'doorRate' ? (
      <div
        style={{
          display:
            this.props.context.componentParent.state.editedRowId != null
              ? 'block'
              : 'inline-flex',
          gap: 3,
          marginTop:
            this.props.context.componentParent.state.editedRowId != null
              ? '-2px'
              : '1px',
          alignItems: 'center'
        }}
      >
        <FormControl
          variant="outlined"
          className={'doorRateValues'}
          disabled={
            this.props.context.componentParent.state.editedRowId != null
              ? false
              : 'true'
          }
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? 'block'
                : 'none'
          }}
        >
          <OutlinedInput
            className={clsx(classes.formControl, 'doorRateValue')}
            id="outlined-adornment-weight"
            title="Door Rate($)"
            value={this.state.doorRateValue}
            error={
              this.props.context.componentParent.state.doorRateError == null
                ? false
                : this.props.context.componentParent.state.doorRateError ==
                  this.props.rowIndex
                ? true
                : false
            }
            onBlur={this.handleDoorRateChange}
            onChange={e =>
              (e.target.value === '' || regEx.test(e.target.value)) &&
              this.setState({
                doorRateValue: e.target.value
              })
            }
            // onChange={this.handleFixedRateChange}
            startAdornment={
              <InputAdornment
                classes={{ root: classes.adorment }}
                position="start"
                disableTypography={true}
              >
                $
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Door Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
        // style={{
        //   display:
        //     this.props.context.componentParent.state.editedRowId != null
        //       ? 'block'
        //       : 'none'
        // }}
        >
          {this.props.data.doorRate != null &&
            this.props.data.doorRate != '' &&
            // this.props.data.laborFixedRate != 0 &&
            '$' + this.props.data.doorRate}
        </span>
      </div>
    ) : (
      //   <div
      //     style={{
      //       display: 'inline-flex',
      //       gap: 3,
      //       marginTop:
      //         this.props.context.componentParent.state.editedRowId != null
      //           ? '-2px'
      //           : '1px',
      //       alignItems: 'center'
      //     }}
      //   >
      //     <input
      //       type="checkbox"
      //       title="Parts Fixed Rate"
      //       className="partsFixedRate"
      //       disabled={
      //         this.props.context.componentParent.state.editedRowId == null
      //           ? true
      //           : this.props.context.componentParent.state.editedRowId !=
      //             this.props.rowIndex
      //           ? true
      //           : this.props.data.gridExcluded == 1
      //           ? true
      //           : false
      //       }
      //       onClick={this.partsCheckedHandler}
      //       checked={this.props.data.partsFixedRate == 0 ? false : true}
      //     />
      //     <select
      //       disabled={
      //         this.props.context.componentParent.state.editedRowId != null &&
      //         this.props.data.partsFixedRate == 1
      //           ? false
      //           : 'true'
      //       }
      //       style={{
      //         display:
      //           this.props.context.componentParent.state.editedRowId != null &&
      //           this.props.data.gridExcluded != 1
      //             ? 'block'
      //             : 'none'
      //       }}
      //       onChange={e => this.handleMarkupChange(e)}
      //       value={this.state.partsMarkup}
      //       className={clsx(classes.formControlSelect, 'partsFixedRateSelect')}
      //       name="parts-markups"
      //       id="parts-markups"
      //     >
      //       <option value="Cost">Cost</option>
      //       <option value="Cost+">Cost+</option>
      //       <option value="Cost%">Cost%</option>
      //       <option value="List">List</option>
      //       <option value="List-">List-</option>
      //       <option value="List+">List+</option>
      //       <option value="List%">List%</option>
      //     </select>

      //     <FormControl
      //       variant="outlined"
      //       disabled={
      //         this.props.context.componentParent.state.editedRowId != null &&
      //         this.props.data.partsFixedRate == 1 &&
      //         !this.state.isValid
      //           ? false
      //           : 'true'
      //       }
      //       className="partsFixedRateValue"
      //       style={{
      //         display:
      //           this.props.context.componentParent.state.editedRowId != null &&
      //           this.props.data.gridExcluded != 1
      //             ? 'block'
      //             : 'none'
      //       }}
      //     >
      //       <OutlinedInput
      //         className={clsx(classes.formControl, 'fixedRateValue')}
      //         classes={{ input: classes.adorment }}
      //         title="Parts Fixed Rate(%)"
      //         id="outlined-adornment-weight"
      //         value={this.state.partsfixedValue}
      //         error={
      //           this.props.context.componentParent.state.partsfixedRateError ==
      //           null
      //             ? false
      //             : this.props.context.componentParent.state
      //                 .partsfixedRateError == this.props.rowIndex
      //             ? true
      //             : false
      //         }
      //         onBlur={this.handleFixedRateChange}
      //         onChange={e =>
      //           (e.target.value === '' || regExParts.test(e.target.value)) &&
      //           this.setState({
      //             partsfixedValue: e.target.value
      //           })
      //         }
      //         // onChange={this.handleFixedRateChange}
      //         endAdornment={
      //           <InputAdornment
      //             classes={{ input: classes.adorment }}
      //             position="end"
      //             disableTypography={true}
      //           >
      //             %
      //           </InputAdornment>
      //         }
      //         aria-describedby="outlined-weight-helper-text"
      //         inputProps={{
      //           'aria-label': 'Fixed Rate Value'
      //         }}
      //         labelWidth={0}
      //       />
      //     </FormControl>
      //     <span
      //       style={{
      //         display:
      //           this.props.context.componentParent.state.editedRowId == null &&
      //           this.props.data.partsFixedRate == 1
      //             ? 'block'
      //             : 'none'
      //       }}
      //     >
      //       {this.props.data.partsFixedratevalue != null &&
      //       this.props.data.partsFixedratevalue != '' &&
      //       this.props.data.partsFixedratevalue != 'N/A' &&
      //       this.props.data.partsFixedratevalue != 'Cost' &&
      //       this.props.data.partsFixedratevalue != 'List'
      //         ? this.props.data.partsFixedratevalue + '%'
      //         : this.props.data.partsFixedratevalue}
      //     </span>

      //     <FormControl
      //       variant="outlined"
      //       style={{
      //         display:
      //           this.props.context.componentParent.state.editedRowId != null &&
      //           this.props.data.gridExcluded != 1
      //             ? 'block'
      //             : 'none'
      //       }}
      //       margin="dense"
      //       title="Effective From"
      //       className={clsx(
      //         classes.formControlDate,
      //         'input-container',
      //         'partsFixedRateDate'
      //       )}
      //     >
      //       <DateRangePicker
      //         initialSettings={{
      //           // maxDate: {
      //           //   date: new Date(),
      //           // },
      //           // minDate:{
      //           //   date: (this.props.selectedDates[1])
      //           // },
      //           locale: {
      //             format: 'MM/DD/YY',
      //             separator: ' - '
      //           },
      //           autoUpdateInput: true,
      //           showDropdowns: true,
      //           autoApply: true,
      //           singleDatePicker: true,
      //           // minDate: moment().toDate(),
      //           //maxDate: moment().toDate(),
      //           // alwaysShowCalendars: true,
      //           applyClass: clsx(classes.calButton, 'apply-btn'),
      //           cancelClass: clsx(classes.calButton, 'apply-btn'),
      //           startDate: new Date(this.state.partsfixedDate)
      //           //showDropdowns: true
      //         }}
      //         value={this.state.partsfixedDate}
      //         onCallback={this.handleCallback}
      //       >
      //         <input
      //           type="text"
      //           disabled={
      //             this.props.context.componentParent.state.editedRowId != null &&
      //             this.props.data.partsFixedRate == 1
      //               ? false
      //               : 'true'
      //           }
      //           className={clsx(classes.fixeddatepicker, 'fixedRateDate')}
      //           id="picker"
      //           name="picker"
      //           aria-labelledby="label-picker"
      //         />
      //         {/* <TextField
      //     id="outlined-basic"
      //     label="Select Date"
      //     size="small"
      //     //onChange={}
      //     value={this.state.value}
      //     variant="outlined"
      //   /> */}
      //       </DateRangePicker>
      //       {/* <label class="labelpicker" for="picker" id="label-picker">
      //     <div class="textpicker">Select Date</div>
      //   </label> */}
      //     </FormControl>
      //     <span
      //       style={{
      //         display:
      //           this.props.context.componentParent.state.editedRowId == null &&
      //           this.props.data.partsFixedRate == 1
      //             ? 'block'
      //             : 'none'
      //       }}
      //     >
      //       {this.props.data.partsFixedratedate != null &&
      //         '[' +
      //           moment(new Date(this.props.data.partsFixedratedate)).format(
      //             'MM/DD/YY'
      //           ) +
      //           ']'}
      //     </span>
      //   </div>
      ''
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    width: 60,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: '8px !important',
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '75px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,
    width: 80,
    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 4
  }
});
export default withStyles(styles)(FixedRateRenderer);
