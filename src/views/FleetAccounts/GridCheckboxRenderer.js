import React, { Component } from 'react';

export default class extends Component {
  constructor(props) {
    super(props);
    this.checkedHandler = this.checkedHandler.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'gridExcluded') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);
      if (checked == true) {
        this.props.node.setDataValue('laborFixedRate', 0);
        this.props.node.setDataValue('partsFixedRate', 0);
        this.props.context.componentParent.onLaborFixedDateChanged(
          this.props.data.laborFixedratedate,
          null
        );
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedratedate,
          null
        );
        this.props.context.componentParent.onLaborFixedRateChanged(
          this.props.data.laborFixedratevalue,
          '0'
        );
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedratevalue,
          'N/A'
        );
      }

      this.props.api.refreshCells({
        columns: ['laborFixedRate', 'partsFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  render() {
    return this.props.colDef.field == 'gridExcluded' ? (
      <input
        type="checkbox"
        className="grid-excluded"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : false
        }
        onClick={this.checkedHandler}
        checked={this.props.value == 1 ? true : false}
      />
    ) : (
      ''
    );
  }
}
