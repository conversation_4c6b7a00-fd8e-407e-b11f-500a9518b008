import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import EditHistoryAll from './EditHistoryAll';
import EditHistoryOld from './EditHistoryOld';
import {
  getDrillDownMonthYears,
  getEditHistoryToggleOptionsWithTimeZone
} from 'src/utils/hasuraServices';
import moment from 'moment';
import { useHistory } from 'react-router';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { setHistoryStatus } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { getTimeZone, getYearValue } from 'src/utils/Utils';
//import queryString from 'query-string';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

const EditHistoryStatus = props => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const [dates, setDates] = useState([]);
  const [lastWeek, setLastWeek] = useState('');

  const [thisWeek, setThisWeek] = useState('');
  const [lastTwoWeek, setLastTwoWeek] = useState('');

  const [yesterDay, setYesterDay] = useState('');
  const [today, setToday] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');
  const session = useSelector(state => state.session);
  useEffect(() => {
    getEditHistoryToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (
        result.data.statelessCcPhysicalRoGetToggleDurationsEdithistory.nodes
      ) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsEdithistory.nodes;
        console.log('dataArr===', dataArr);

        setThisWeek(
          moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY")
        );
        setLastTwoWeek(
          moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY")
        );
        setDates(dataArr);
        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
      }
    });
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date())
          .clone()
          .format('YYYY-MM-DD');
        setSelectedDates([endDate, endDate]);
      }
    });
  }, []);

  if (months.length > 0 && selectedDates.length > 0 && dates.length > 0) {
    return (
      <Page className={classes.root} title="Edit History All">
        {props.keycloak.realmAccess.roles.includes('client') ||
        props.keycloak.realmAccess.roles.includes('user') ||
        JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
          <Redirect to="/errors/error-404" />
        ) : localStorage.getItem('versionFlag') == 'FALSE' ? (
          <EditHistoryOld
            history={history}
            months={months}
            selectedDates={selectedDates}
            session={session}
            dates={dates}
            today={today}
            yesterDay={yesterDay}
            dayBfYest={dayBfYest}
            lastWeek={lastWeek}
            mtd={mtd}
            lastMonth={lastMonth}
            lastThreeMonths={lastThreeMonths}
            lastQtr={lastQtr}
            ytd={ytd}
            lastYear={lastYear}
            lastTwelveMonths={lastTwelveMonths}
            thisWeek={thisWeek}
            lastTwoWeek={lastTwoWeek}
          />
        ) : (
          <EditHistoryAll
            history={history}
            months={months}
            selectedDates={selectedDates}
            session={session}
            dates={dates}
            today={today}
            yesterDay={yesterDay}
            dayBfYest={dayBfYest}
            lastWeek={lastWeek}
            mtd={mtd}
            lastMonth={lastMonth}
            lastThreeMonths={lastThreeMonths}
            lastQtr={lastQtr}
            ytd={ytd}
            lastYear={lastYear}
            lastTwelveMonths={lastTwelveMonths}
            thisWeek={thisWeek}
            lastTwoWeek={lastTwoWeek}
          />
        )}
      </Page>
    );
  } else {
    return <LoaderSkeleton></LoaderSkeleton>;
  }
};

export default withKeycloak(EditHistoryStatus);
