import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  Select,
  TextField,
  Button,
  MenuItem,
  Tooltip,
  InputLabel
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import 'src/input-style.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { GetDailyUpdateStatusAll } from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import { addDays } from 'date-fns';
import RestoreIcon from '@material-ui/icons/Restore';
import { ReactSession } from 'react-client-session';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import TooltipRendererMarkdown from 'src/views/AnalyzeData/Component/TooltipRendererMarkdown';
import clsx from 'clsx';

var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class EditHistoryAll extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData( this.state.statusDate ? moment(this.state.statusDate[0]).format('YYYY-MM-DD') : new Date(),this.state.statusDate ? moment(this.state.statusDate[1]).format('YYYY-MM-DD') : new Date(), this.state.optionName ? this.state.optionName : 'Advisor');
    //     this.setState({ statusDate: this.state.statusDate ? this.state.statusDate : [this.props.selectedDates[0], this.props.selectedDates[1]] });
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData(
          this.state.statusDate
            ? moment(this.state.statusDate[0]).format('YYYY-MM-DD')
            : new Date(),
          this.state.statusDate
            ? moment(this.state.statusDate[1]).format('YYYY-MM-DD')
            : new Date(),
          this.state.optionName ? this.state.optionName : 'Advisor'
        );
        this.setState({
          statusDate: this.state.statusDate
            ? this.state.statusDate
            : [this.props.selectedDates[0], this.props.selectedDates[1]]
        });
        //   this.setState({ value: this.state.value ? this.state.value : new Date() });
        //   this.setState({ value: this.state.optionName ? this.state.optionName : 'Advisor' });
        // }
      }
    }
  }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = this.props.months[0].monthYear;
    var location =
      getVerificationDashboardBaseURL() + '/FOC3_Searchbyro/ag-grid.html';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      filterDisabled: true,
      optionName: 'Advisor',
      columnName: 'Advisor',
      statusDate: [this.props.selectedDates[0], this.props.selectedDates[1]],
      queryMonth: initialQueryMonth,
      selectedMonthYear: initialQueryMonth,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: ['All'],
      isLoading: true,
      isRefresh: false,
      rawGridApi: {},
      gridApi: {},
      tabSelection: 0,
      headerHeight: 45,
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      monthYear: this.props.months,
      startDate: '',
      endDate: '',

      value:
        moment(new Date(this.props.selectedDates[0])).format('MM/DD/YY') +
        ' - ' +
        moment(new Date(this.props.selectedDates[1])).format('MM/DD/YY'),
      columnDefsAdvisor: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 200,
          minWidth: 20,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1,
          valueFormatter: this.formatCellValueDate
          // filterParams: {
          //   valueFormatter: this.formatCellValueDate
          // }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          tooltipField: 'userName',
          // tooltip: params => 'View Matrix',
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          // width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true
        },
        {
          headerName: 'Service Advisor Name',
          chartDataType: 'category',
          field: 'advisorName',
          // width: 150,
          minWidth: 150,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1,
          tooltipField: 'advisorName'
        },
        {
          headerName: 'Old Nickname',
          chartDataType: 'category',
          field: 'oldNickname',
          // width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'New Nickname',
          chartDataType: 'category',
          field: 'newNickname',
          // width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'Old Status',
          chartDataType: 'category',
          field: 'oldStatus',
          // width: 120,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'New Status',
          chartDataType: 'category',
          field: 'newStatus',
          // width: 120,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1
        }
      ],
      columnDefsPaytype: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 210,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 180,
          minWidth: 180,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 130,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'category',
          field: 'paytype',
          // width: 110,
          minWidth: 110,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Old Pay Type Group',
          chartDataType: 'category',
          field: 'oldPaytypecode',
          // width: 115,
          minWidth: 115,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'New Pay Type Group',
          chartDataType: 'category',
          field: 'newPaytypecode',
          // width: 115,
          minWidth: 115,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Old Department',
          chartDataType: 'category',
          field: 'oldPaytypedept',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Department',
          chartDataType: 'category',
          field: 'newPaytypedept',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Excl.from Comp',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldgridexcluded',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Excl.from Comp',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newgridexcluded',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'Old Lbr Fixed Rate',
          chartDataType: 'category',
          field: 'oldlaborFixedRate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Lbr Fixed Rate',
          chartDataType: 'category',
          field: 'newlaborFixedRate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'Old Prts Fixed Rate',
          chartDataType: 'category',
          field: 'oldpartsFixedRate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Prts Fixed Rate',
          chartDataType: 'category',
          field: 'newpartsFixedRate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'Old Lbr Fixed Rate Date',
          chartDataType: 'category',
          field: 'oldlaborFixedratedate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'New Lbr Fixed Rate Date',
          chartDataType: 'category',
          field: 'newlaborFixedratedate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Old Prts Fixed Rate Date',
          chartDataType: 'category',
          field: 'oldpartsFixedratedate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'New Prts Fixed Rate Date',
          chartDataType: 'category',
          field: 'newpartsFixedratedate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Old Lbr Fixed Rate Value $',
          chartDataType: 'category',
          field: 'oldlaborFixedratevalue',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          valueFormatter: this.formatCellValue,
          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Lbr Fixed Rate Value $',
          chartDataType: 'category',
          field: 'newlaborFixedratevalue',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          valueFormatter: this.formatCellValue,
          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Prts Fixed Rate Value',
          chartDataType: 'category',
          field: 'oldpartsFixedratevalue',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Prts Fixed Rate Value',
          chartDataType: 'category',
          field: 'newpartsFixedratevalue',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        }
      ],
      columnDefsTechnician: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 220,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Technician Name',
          chartDataType: 'category',
          field: 'techname',
          // width: 150,
          minWidth: 180,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          tooltipField: 'techname'
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Nickname',
          chartDataType: 'category',
          field: 'oldNickname',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Nickname',
          chartDataType: 'category',
          field: 'newNickname',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },

        {
          headerName: 'Old Status',
          chartDataType: 'category',
          field: 'oldStatus',
          // width: 120,
          minWidth: 80,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Status',
          chartDataType: 'category',
          field: 'newStatus',
          // width: 120,
          minWidth: 80,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        }
      ],
      columnDefsOpcode: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // filter: false
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Opcode',
          chartDataType: 'category',
          field: 'laboropcode',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Old Opcategory',
          chartDataType: 'category',
          field: 'oldOpcategory',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Opcategory',
          chartDataType: 'category',
          field: 'newOpcategory',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Log Status',
          chartDataType: 'category',
          field: 'logStatus',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Fixed Rate Value',
          chartDataType: 'category',
          field: 'oldfrvalue',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Fixed Rate Value',
          chartDataType: 'category',
          field: 'newfrvalue',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Fixed Rate Date',
          chartDataType: 'category',
          field: 'oldfrdate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Fixed Rate Date',
          chartDataType: 'category',
          field: 'newfrdate',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Department',
          chartDataType: 'category',
          field: 'oldPaytypedept',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Department',
          chartDataType: 'category',
          field: 'newPaytypedept',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Category',
          chartDataType: 'series',
          // width: 120,
          minWidth: 120,
          field: 'oldcategorized',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Category',
          chartDataType: 'series',
          // width: 120,
          minWidth: 120,
          field: 'newcategorized',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled />`;
          }
        },
        {
          headerName: 'Old MPI Item',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldmpiItem',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled />`;
          }
        },
        {
          headerName: 'New MPI Item',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newmpiItem',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'Old Menu Sales',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldmenuSales',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 'Y' ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Menu Sales',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newmenuSales',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 'Y' ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'Old Maint Plan',
          chartDataType: 'series',
          // width: 120,
          minWidth: 120,
          field: 'oldmaintenancePlan',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Maint Plan',
          chartDataType: 'series',
          // width: 120,
          minWidth: 120,
          field: 'newmaintenancePlan',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'Old Excl.from Comp',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldgridexcluded',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Excl.from Comp',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newgridexcluded',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'Old Fixed Rate Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 120,
          field: 'oldfrstatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Fixed Rate Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 120,
          field: 'newfrstatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        }
      ],
      columnDefsChartMaster: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 120,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // filter: false
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Chart Name ',
          chartDataType: 'category',
          field: 'oldChartname',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          tooltipField: 'oldChartname'
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'New Chart Name',
          chartDataType: 'category',
          field: 'newChartname',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          tooltipField: 'newChartname'
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Chart Description',
          chartDataType: 'category',
          field: 'oldChartdescription',
          // width: 100,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          tooltipField: 'oldChartdescription'
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Chart Description',
          chartDataType: 'category',
          field: 'newChartdescription',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          tooltipField: 'newChartdescription'
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Markdown Desc',
          chartDataType: 'category',
          field: 'oldmarkdowndesc',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          // tooltipField: 'oldmarkdowndesc',
          cellRendererFramework: TooltipRendererMarkdown
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Markdown Desc',
          chartDataType: 'category',
          field: 'newmarkdowndesc',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          // tooltipField: 'newmarkdowndesc',
          cellRendererFramework: TooltipRendererMarkdown
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldStatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newStatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        }
      ],
      columnDefsStoreSettings: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 120,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // filter: false
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Key Name',
          chartDataType: 'category',
          field: 'keyName',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Old Keyvalue',
          chartDataType: 'category',
          field: 'oldKeyvalue',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Keyvalue',
          chartDataType: 'category',
          field: 'newKeyvalue',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldStatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newStatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        }
      ],
      columnDefsStoreGoal: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'KPI Number',
          chartDataType: 'category',
          field: 'kpiNo',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Goal Name',
          chartDataType: 'category',
          field: 'goalName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          tooltipField: 'goalName',
          flex: 1
        },
        {
          headerName: 'Old Goal Value',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldGoalvalue',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1
        },
        {
          headerName: 'New Goal Value',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newGoalvalue',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1
        }
      ],
      columnDefsAdvisorGoal: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 120,
          minWidth: 60,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 120,
          minWidth: 180,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'KPI Number',
          chartDataType: 'category',
          field: 'kpiNo',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Goal Name',
          chartDataType: 'category',
          field: 'goalName',
          // width: 120,
          minWidth: 150,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          tooltipField: 'goalName',
          valueFormatter: this.formatCellGoalName,
          flex: 1
        },
        {
          headerName: 'Advisor',
          chartDataType: 'category',
          field: 'advisorName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Old Goal Value',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldGoalvalue',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1
        },
        {
          headerName: 'New Goal Value',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newGoalvalue',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1
        }
      ],
      columnDefsGoalSettings: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 120,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // filter: false
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Goal Name',
          chartDataType: 'category',
          field: 'goalName',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          tooltipField: 'goalName'
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Old Goal Value',
          chartDataType: 'category',
          field: 'oldGoalvalue',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Goal Value',
          chartDataType: 'category',
          field: 'newGoalvalue',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        }
      ],
      columnDefsGoalSettingsAdvisor: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 120,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          hide: false,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // filter: false
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },

        {
          headerName: 'Goal Name',
          chartDataType: 'category',
          field: 'goalName',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          tooltipField: 'goalName'
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Advisor Name',
          chartDataType: 'category',
          field: 'advisorName',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true,
          // filter: false
        },
        {
          headerName: 'Old Goal Value',
          chartDataType: 'category',
          field: 'oldGoalvalue',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Goal Value',
          chartDataType: 'category',
          field: 'newGoalvalue',
          // width: 120,
          minWidth: 150,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        }
      ],
      columnDefsFROpcode: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 200,
          minWidth: 20,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'Labor Opcode',
          chartDataType: 'category',
          field: 'laboropcode',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Fixed Rate Value',
          chartDataType: 'category',
          field: 'oldfrvalue',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Fixed Rate Value',
          chartDataType: 'category',
          field: 'newfrvalue',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Fixed Rate Date',
          chartDataType: 'category',
          field: 'oldfrdate',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Fixed Rate Date',
          chartDataType: 'category',
          field: 'newfrdate',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Fixed Rate Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'oldfrstatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        },
        {
          headerName: 'New Fixed Rate Status',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          field: 'newfrstatus',

          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          flex: 1,
          cellRenderer: params => {
            return `<input type='checkbox' ${
              params.value == 1 ? 'checked' : ''
            } disabled/>`;
          }
        }
      ],
      columnDefsFRPaytype: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 200,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          tooltipField: 'userName',
          // width: 180,
          minWidth: 180,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'category',
          field: 'paytype',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Type',
          chartDataType: 'category',
          field: 'ticketId',
          // width: 180,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
        },
        // {
        //   headerName: 'Old Lbr FR',
        //   chartDataType: 'category',
        //   field: 'oldlaborFixedRate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        // },
        // {
        //   headerName: 'New Lbr FR',
        //   chartDataType: 'category',
        //   field: 'newlaborFixedRate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        // },
        // {
        //   headerName: 'Old Prts FR',
        //   chartDataType: 'category',
        //   field: 'oldpartsFixedRate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        //   // hide: session.historyStatus == 'Advisor' ? false : true
        // },
        // {
        //   headerName: 'New Prts FR',
        //   chartDataType: 'category',
        //   field: 'newpartsFixedRate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        //   // hide: session.historyStatus == 'Advisor' ? false : true
        // },
        // {
        //   headerName: 'Old Lbr FR Date',
        //   chartDataType: 'category',
        //   field: 'oldlaborFixedratedate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        //   // hide: session.historyStatus == 'Advisor' ? false : true
        // },
        // {
        //   headerName: 'New Lbr FR Date',
        //   chartDataType: 'category',
        //   field: 'newlaborFixedratedate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        //   // hide: session.historyStatus == 'Advisor' ? false : true
        // },
        // {
        //   headerName: 'Old Prts FR Date',
        //   chartDataType: 'category',
        //   field: 'oldpartsFixedratedate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        //   // hide: session.historyStatus == 'Advisor' ? false : true
        // },
        // {
        //   headerName: 'New Prts FR Date',
        //   chartDataType: 'category',
        //   field: 'newpartsFixedratedate',
        //   // width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   flex: 1
        //   // hide: session.historyStatus == 'Advisor' ? false : true
        // },
        {
          headerName: 'Old Lbr Fixed Rate Value $',
          chartDataType: 'category',
          field: 'oldlaborFixedratevalue',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          valueFormatter: this.formatCellValue,
          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Lbr  Fixed Rate Value $',
          chartDataType: 'category',
          field: 'newlaborFixedratevalue',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          valueFormatter: this.formatCellValue,
          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'Old Prts  Fixed Rate Value',
          chartDataType: 'category',
          field: 'oldpartsFixedratevalue',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        },
        {
          headerName: 'New Prts  Fixed Rate Value',
          chartDataType: 'category',
          field: 'newpartsFixedratevalue',
          // width: 100,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1
          // hide: session.historyStatus == 'Advisor' ? false : true
        }
      ],
      columnDefsStoreAssignAdvisor: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          tooltipField: 'userName',
          // tooltip: params => 'View Matrix',
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          // width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true
        },
        {
          headerName: 'Service Advisor Name',
          chartDataType: 'category',
          field: 'advisorName',
          // width: 150,
          minWidth: 150,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1,
          tooltipField: 'advisorName'
        },
        // {
        //   headerName: 'Old Nickname',
        //   chartDataType: 'category',
        //   field: 'oldNickname',
        //   // width: 130,
        //   minWidth: 130,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   filter: true,
        //   flex: 1
        // },
        // {
        //   headerName: 'New Nickname',
        //   chartDataType: 'category',
        //   field: 'newNickname',
        //   // width: 130,
        //   minWidth: 130,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   filter: true,
        //   flex: 1
        // },
        {
          headerName: 'Old Status',
          chartDataType: 'category',
          field: 'oldStatus',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1,
          tooltipField: 'oldStatus'
        },
        {
          headerName: 'New Status',
          chartDataType: 'category',
          field: 'newStatus',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1,
          tooltipField: 'newStatus'
        }
      ],
      columnDefsStoreAssignPaytype: [
        {
          headerName: 'Date',
          chartDataType: 'category',
          field: 'statusdate',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Time(EST)',
          // chartDataType: 'category',
          field: 'statustime',
          // width: 200,
          minWidth: 100,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
          filter: true,
          flex: 1
        },
        {
          tooltipField: 'userName',
          // tooltip: params => 'View Matrix',
          headerName: 'User Name',
          chartDataType: 'category',
          field: 'userName',
          // width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1
        },
        {
          headerName: 'Role',
          chartDataType: 'category',
          field: 'userRole',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          flex: 1,
          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'category',
          field: 'paytype',
          // width: 150,
          minWidth: 150,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1,
          tooltipField: 'paytype'
        },
        // {
        //   headerName: 'Old Nickname',
        //   chartDataType: 'category',
        //   field: 'oldNickname',
        //   // width: 130,
        //   minWidth: 130,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   filter: true,
        //   flex: 1
        // },
        // {
        //   headerName: 'New Nickname',
        //   chartDataType: 'category',
        //   field: 'newNickname',
        //   // width: 130,
        //   minWidth: 130,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   editable: false,
        //
        //   filter: true,
        //   flex: 1
        // },
        {
          headerName: 'Old Status',
          chartDataType: 'category',
          field: 'oldStatus',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1,
          tooltipField: 'oldStatus'
        },
        {
          headerName: 'New Status',
          chartDataType: 'category',
          field: 'newStatus',
          // width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,

          // hide: session.historyStatus == 'Advisor' ? false : true,
          filter: true,
          flex: 1,
          tooltipField: 'newStatus'
        }
      ],
      groupIncludeTotalFooter: true,
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      rowData: [],
      overlayNoRowsTemplate:
        '<span style="padding: 10px; margin-top:50px;">No Rows To Show</span>',
      defaultColDef: {
        enableValue: true,
        // enableRowGroup: true,
        enablePivot: false,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  formatCellGoalName = params => {
    return params.value.replace(/^\s*(?:[\dA-Z]+\.|[a-z]\)|•)\s+/gm, '');
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/DD/YY');
    }
  };
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    }
  };

  onGridReady = params => {
    // params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.setState({ gridColumnApi: params.columnApi });
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridcolumnApi: params.columnApi });

    var startDate = moment(this.state.statusDate[0]).format('YYYY-MM-DD');
    var endDate = moment(this.state.statusDate[1]).format('YYYY-MM-DD');
    console.log('startdate=', startDate, endDate);
    this.getAgGridData(startDate, endDate, this.state.optionName);
  };

  getAgGridData(startDate, endDate, optionName) {
    // var startDate = moment(statusDate).format('YYYY-MM-DD');
    // var endDate = moment(statusDate).format('YYYY-MM-DD');
    this.setState({ isLoading: true });
    var timezoneOffset = '+05:30';
    console.log('start===', startDate, endDate);
    GetDailyUpdateStatusAll(
      startDate,
      endDate,
      optionName,
      timezoneOffset,
      result => {
        this.setState({ isLoading: false });
        if (
          result.data.statelessCcPhysicalRwGetDailyUpdateStatusWithoutfilter
            .nodes
        ) {
          console.log(
            'data==',
            result.data.statelessCcPhysicalRwGetDailyUpdateStatusWithoutfilter
              .nodes
          );
          var resultArr =
            result.data.statelessCcPhysicalRwGetDailyUpdateStatusWithoutfilter
              .nodes;
          this.setState({
            rowData: resultArr
          });
        }
      }
    );
  }

  nulValue = () => {
    return '';
  };
  renderBackButton = () => {
    {
      let chartId = this.props.chartId
        ? this.props.chartId
        : this.props.history.location.search.split('=')[1];
      let data = {
        month_year: this.state.queryMonth,
        type: '',
        chartId: this.props.chartId,
        history: this.props.history,
        prevPath: this.state.previousLocation,
        drillDown:
          chartId == 1111
            ? 41
            : chartId == 1115
            ? 42
            : chartId == 1232
            ? 45
            : chartId == 1165
            ? 44
            : 43
      };
      this.props.parentCallback(data);
    }
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'EditHistory',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Edit History' },
            mergeAcross: 3
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  dateChange = e => {
    // this.setState({ filterDisabled: false });
    console.log('value===', moment(e[0]).format('YYYY-MM-DD'), e.target.value);
    //     this.setState({ value: e });
    //     var startDate = moment(e).format('YYYY-MM-DD');
    //     this.setState({ statusDate: startDate });
    // console.log("startDate=",startDate,this.state.statusDate);
  };
  optionChange = e => {
    this.setState({ filterDisabled: false });
    console.log('dateeeeeeee=', e.target.value);
    this.setState({ optionName: e.target.value });
  };
  filterData = () => {
    // if (this.state.optionName == 'Opcode') {
    //   this.setState({ headerHeight: 75 });
    // } else {
    //   this.setState({ headerHeight: 45 });
    // }
    this.setState({ columnName: this.state.optionName });
    console.log('value=', this.state.value[0], this.state.value[1]);
    var startDate = moment(this.state.value.split('-')[0]).format('YYYY-MM-DD');
    var endDate = moment(this.state.value.split('-')[1]).format('YYYY-MM-DD');
    console.log(
      'filterData=',
      this.state.value[0],
      this.state.value[1],
      startDate,
      endDate,
      this.state.value.split('-')[0],
      this.state.value.split('-')[1]
    );

    if (startDate <= endDate) {
      this.getAgGridData(startDate, endDate, this.state.optionName);
    } else {
      var startDate = moment(this.state.statusDate[0]).format('YYYY-MM-DD');
      var endDate = moment(this.state.statusDate[1]).format('YYYY-MM-DD');
      this.setState({
        value: [this.props.selectedDates[0], this.props.selectedDates[1]]
      });
      this.getAgGridData(startDate, endDate, this.state.optionName);
    }
  };
  resetReportGrid = () => {
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
  };
  handleCallback = (event, picker) => {
    this.setState({ filterDisabled: false });
    this.setState({
      value:
        picker.startDate.format('MM/DD/YY') +
        ' - ' +
        picker.endDate.format('MM/DD/YY')
    });
  };
  handleEventCallback = (event, picker) => {};
  render() {
    const { classes } = this.props;

    return (
      <div>
        <Paper square style={{ margin: 8, marginTop: '20px' }}>
          <Tabs
            value={this.state.tabSelection}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
            style={{ pointerEvents: 'none' }}
          >
            <Tab
              style={{
                textTransform: 'none',
                paddingRight: 182,
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
              label={<div>Edit History</div>}
              value="one"
            />
          </Tabs>
        </Paper>
        <Paper square style={{ margin: '0px 8px' }}>
          <Grid container spacing={12} style={{ alignItems: 'center' }}>
            <Grid
              item
              xs={8}
              style={{ padding: '5px', display: 'flex', alignItems: 'center' }}
            >
              <FormControl
                variant="outlined"
                margin="dense"
                className={clsx(classes.formControl, 'input-container')}
              >
                <DateRangePicker
                  initialSettings={{
                    // maxDate: {
                    //   date: new Date(),
                    // },
                    // minDate:{
                    //   date: (this.props.selectedDates[1])
                    // },
                    locale: {
                      format: 'MM/DD/YY',
                      separator: ' - '
                    },
                    ranges: {
                      ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.today]: [moment().toDate(), moment().toDate()],
                      ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.yesterDay]: [
                        moment()
                          .subtract(1, 'days')
                          .toDate(),
                        moment()
                          .subtract(1, 'days')
                          .toDate()
                      ],
                      ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.dayBfYest]: [
                        moment()
                          .subtract(2, 'days')
                          .toDate(),
                        moment()
                          .subtract(2, 'days')
                          .toDate()
                      ],
                      ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.lastWeek]: [
                        moment(this.props.dates[0].lastweekstartdate).toDate(),
                        moment(this.props.dates[0].lastweekenddate).toDate()
                      ],
                      // 'Last 7 Days': [
                      //   moment()
                      //     .subtract(6, 'days')
                      //     .toDate(),
                      //   moment().toDate()
                      // ],
                      // 'Last 30 Days': [
                      //   moment()
                      //     .subtract(29, 'days')
                      //     .toDate(),
                      //   moment().toDate()
                      // ],
                      ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.mtd]: [
                        moment()
                          .startOf('month')
                          .toDate(),
                        moment()
                          .endOf('month')
                          .toDate()
                      ],
                      ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.lastMonth]: [
                        moment()
                          .subtract(1, 'month')
                          .startOf('month')
                          .toDate(),
                        moment()
                          .subtract(1, 'month')
                          .endOf('month')
                          .toDate()
                      ],
                      ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.lastThreeMonths]: [
                        moment(
                          this.props.dates[0].lastthreemonthstartdate
                        ).toDate(),
                        moment(
                          this.props.dates[0].lastthreemonthenddate
                        ).toDate()
                      ],
                      ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.lastQtr]: [
                        moment(
                          this.props.dates[0].lastquarterstartdate
                        ).toDate(),
                        moment(this.props.dates[0].lastquarterenddate).toDate()
                      ],
                      ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.ytd]: [
                        moment(this.props.dates[0].ytdstartdate).toDate(),
                        moment(this.props.dates[0].ytdenddate).toDate()
                      ],
                      ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.lastTwelveMonths]: [
                        moment(
                          this.props.dates[0].lasttwelvemonthstartdate
                        ).toDate(),
                        moment(
                          this.props.dates[0].lasttwelvemonthenddate
                        ).toDate()
                      ],
                      ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                      this.props.lastYear]: [
                        moment(this.props.dates[0].lastyearstartdate).toDate(),
                        moment(this.props.dates[0].lastyearenddate).toDate()
                      ]
                    },
                    maxDate: moment().toDate(),
                    alwaysShowCalendars: true,
                    applyClass: clsx(classes.calButton, 'apply-btn'),
                    cancelClass: clsx(classes.calButton, 'apply-btn')

                    // startDate: '01/01/23',
                    // endDate: '03/01/23',
                    //showDropdowns: true
                  }}
                  onEvent={this.handleEventCallback}
                  onApply={this.handleCallback}
                >
                  <input
                    type="text"
                    className="datepicker"
                    id="picker"
                    name="picker"
                    aria-labelledby="label-picker"
                  />
                  {/* <TextField
                    id="outlined-basic"
                    label="Select Date"
                    size="small"
                    //onChange={}
                    value={this.state.value}
                    variant="outlined"
                  /> */}
                </DateRangePicker>
                <label class="labelpicker" for="picker" id="label-picker">
                  <div class="textpicker">Select Date (EST)</div>
                </label>
              </FormControl>
              <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <InputLabel
                  htmlFor="outlined-age-native-simple"
                  margin="dense"
                  style={{
                    paddingRight: 4,
                    paddingLeft: 4,
                    marginTop: 1,
                    marginLeft: -4,
                    fontWeight: 'bold',
                    backgroundColor: '#fff'
                  }}
                >
                  History For
                </InputLabel>
                {/* <Typography style={{ fontSize: 12 }} sx={{ mt: 2, mb: 1 }}>
                  History For
                </Typography> */}
                <Select
                  MenuProps={{
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left'
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left'
                    },
                    getContentAnchorEl: null
                  }}
                  variant="outlined"
                  label="Group By"
                  name="group-by-type"
                  value={this.state.optionName}
                  className={classes.monthSelector}
                  onChange={this.optionChange}
                  id="advisor-dropdown"
                  // sx={{
                  //   width: 100,
                  //   height: 40,
                  //   marginRight: 15,
                  //   border: "1px solid yellow",
                  //   color: "#fff",
                  //   "& .MuiSvgIcon-root MuiSelect-icon MuiSelect-iconOutlined": {
                  //       color: "red",
                  //   },
                  // }}
                >
                  <MenuItem value={'Chart'}>Chart Master</MenuItem>
                  {/* <MenuItem value={'fixedrate_opcode'}>
                    Fixed Rate - Opcodes
                  </MenuItem>
                  <MenuItem value={'fixedrate_paytype'}>
                    Fixed Rate - Pay Types
                  </MenuItem> */}
                  <MenuItem value={'advisor_goal'}>
                    KPI Goals - Advisor
                  </MenuItem>
                  <MenuItem value={'store_goal'}>KPI Goals - Store</MenuItem>
                  <MenuItem value={'GoalSettingsAdvisor'}>
                    Opportunity Goals - Advisor
                  </MenuItem>
                  <MenuItem value={'GoalSettings'}>
                    Opportunity Goals - Store
                  </MenuItem>
                  <MenuItem value={'Opcode'}>Opcodes</MenuItem>
                  <MenuItem value={'Paytype'}>Pay Types</MenuItem>
                  <MenuItem value={'Advisor'}>Service Advisor</MenuItem>
                  <MenuItem value={'Store_settings'}>Store Settings</MenuItem>
                  {localStorage.getItem('realm') == 'mullerag' && (
                    <MenuItem value={'store_assignment_advisor'}>
                      Store Assignment - Advisor
                    </MenuItem>
                  )}
                  {localStorage.getItem('realm') == 'royalag' && (
                    <MenuItem value={'store_assignment_advisor'}>
                      Store Assignment - Advisor
                    </MenuItem>
                  )}
                  {localStorage.getItem('realm') == 'royalag' && (
                    <MenuItem value={'store_assignment_paytype'}>
                      Store Assignment - Pay Type
                    </MenuItem>
                  )}
                  <MenuItem value={'Technician'}>Technician</MenuItem>
                </Select>
              </FormControl>
              <Button
                title="Apply"
                className={clsx(classes.apply, 'apply-btn')}
                variant="contained"
                onClick={this.filterData}
                disabled={this.state.filterDisabled}
                color="primary"
                style={{ marginLeft: 8, width: 50, height: 30 }}
              >
                {/* <Typography
                  variant="body1"
                  align="left"
                  style={{ color: '#fff !important'  }}
                > */}
                Apply
                {/* </Typography> */}
              </Button>
            </Grid>
            <Grid
              item
              xs={4}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'end'
              }}
            >
              {/* <Button
                variant="contained"
                onClick={this.filterData}
                color="primary"
                style={{ marginTop: 30, height: 35, width: 20 }}
                disabled={this.state.filterDisabled} 
              >
                Apply
              </Button> */}
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{
                    marginRight: 10,
                    marginTop: 10,

                    cursor: 'pointer'
                  }}
                  onClick={this.onBtExport}
                >
                  <ExportIcon />
                </Link>
              </Tooltip>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.back, 'reset-btn')}
                onClick={this.resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </Grid>
          </Grid>
        </Paper>
        {this.state.isLoading && (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          // id={
          //   // this.state.columnName == 'Opcode'
          //   //   ? this.state.rowData.length == 0
          //   //     ? 'data-tab-opcode-zero'
          //   //     : 'data-tab-opcode'
          //   //   : ''
          // }
          className="ag-theme-balham"
          style={{
            height: window.innerHeight - 210 + 'px',
            // width: '1106px',
            width:
              this.state.columnName == 'Opcode' ||
              this.state.columnName == 'Paytype'
                ? '98.8%'
                : // : this.state.columnName ==  'Paytype'
                // ? '1280px'
                this.state.columnName == 'Chart'
                ? '98.8%'
                : this.state.columnName == 'store_assignment_advisor' ||
                  this.state.columnName == 'store_assignment_paytype'
                ? '890px'
                : '1110px',
            margin: 8,
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              width: '100%',
              height: '410px'
              // minHeight: 500
            }}
            // domLayout="autoHeight"
            // isExternalFilterPresent={this.isExternalFilterPresent}
            // doesExternalFilterPass={this.doesExternalFilterPass}
            floatingFilter={true}
            defaultColDef={this.state.defaultColDef}
            headerHeight={this.state.headerHeight}
            // sideBar={
            //   this.state.columnName == 'Opcode' ? this.state.sideBar : ''
            // }
            modules={AllModules}
            columnDefs={
              this.state.columnName == 'Advisor'
                ? this.state.columnDefsAdvisor
                : this.state.columnName == 'Paytype'
                ? this.state.columnDefsPaytype
                : this.state.columnName == 'Opcode'
                ? this.state.columnDefsOpcode
                : this.state.columnName == 'Technician'
                ? this.state.columnDefsTechnician
                : this.state.columnName == 'Chart'
                ? this.state.columnDefsChartMaster
                : this.state.columnName == 'Store_settings'
                ? this.state.columnDefsStoreSettings
                : this.state.columnName == 'store_goal'
                ? this.state.columnDefsStoreGoal
                : this.state.columnName == 'advisor_goal'
                ? this.state.columnDefsAdvisorGoal
                : this.state.columnName == 'GoalSettings'
                ? this.state.columnDefsGoalSettings
                : this.state.columnName == 'GoalSettingsAdvisor'
                ? this.state.columnDefsGoalSettingsAdvisor
                : this.state.columnName == 'fixedrate_opcode'
                ? this.state.columnDefsFROpcode
                : this.state.columnName == 'fixedrate_paytype'
                ? this.state.columnDefsFRPaytype
                : this.state.columnName == 'store_assignment_advisor'
                ? this.state.columnDefsStoreAssignAdvisor
                : this.state.columnName == 'store_assignment_paytype'
                ? this.state.columnDefsStoreAssignPaytype
                : ''
            }
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
            excelStyles={this.state.excelStyles}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    // border: 'thin solid #968989 !important',
    height: '35px !important',
    fontWeight: 'bold !important',
    fontSize: '12px !important'
  },
  reactDaterangePicker: {
    border: 'thin solid #ccc !important',
    borderRadius: '4px !important',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px'

    // padding: '4px',
    // color: '#003d6b  !important',
    // border: 'thin solid #ccc !important',
    // borderRadius: '4px',
    // height: '36px !important',
    // fontFamily: 'sans-serif',
    // fontSize: '14px',
    // '&:hover': {
    //   color: '#003d6b  !important',
    //   border: 'thin solid #ccc !important',
    // }
  },
  back: {
    marginRight: 10,
    marginTop: 4
    //  float: 'right'
    //marginTop: 31
  },
  apply: {
    marginRight: 30,
    float: 'right',
    marginLeft: 0,
    color: '#fff !important'

    // marginTop: 31
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  }
});

export default withStyles(styles)(EditHistoryAll);
