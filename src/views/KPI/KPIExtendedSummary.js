import React, { useState } from 'react';

import { Typography, IconButton, Radio, Tooltip } from '@material-ui/core';
import { useHistory } from 'react-router';
import 'chartjs-plugin-doughnutlabel';
import 'chartjs-plugin-datalabels';
import { useSelector, useDispatch } from 'react-redux';
import { makeStyles, withStyles } from '@material-ui/styles';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import getTooltipConentKPI from '../../utils/KpiTooltips';
import clsx from 'clsx';

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  cardContentAll: {
    cursor: 'pointer',
    paddingBottom: 6
  },
  pieContainerRo1: {
    float: 'none',
    marginRight: 0
  },
  pieContainerRo2: {
    float: 'right',
    marginRight: 97
  },
  pieContainerRo3: {
    float: 'left',
    marginLeft: 75,
    position: 'fixed',
    marginTop: -5,
    width: '88%'
  },
  pieContainerRo4: {
    display: 'none'
  },
  pieContainerLine1: {
    float: 'right',
    marginRight: 83,
    width: '100%'
  },
  pieContainerLine2: {
    float: 'left',
    marginLeft: 66,
    position: 'fixed',
    marginTop: 12,
    width: '88%'
  },
  pie13363: {
    display: 'none'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  chartRoot: {
    maxWidth: '30%',
    marginRight: 20
  },
  chartRoot1336: {
    maxWidth: '40%'
  },

  chartRoot1340: {
    maxWidth: '26%'
  },
  chartRoot1344: {
    maxWidth: '80%'
  },
  chartRoot1341: {
    maxWidth: '49%'
  },
  chartRoot1343: {
    maxWidth: '40%'
  },
  chartRoot1339: {
    maxWidth: '30%'
  },
  lineROChart: {
    display: 'flex',
    justifyContent: 'center'
  },
  roShareChart: {
    display: 'flex',
    justifyContent: 'center'
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: 600,
    fontFamily: 'Roboto',
    marginBottom: 25,
    textAlign: 'left',
    marginTop: 15,
    marginLeft: 13,
    color: '#003d6b'
  },
  mileageChart: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 7
  },
  mileageChart1343: {
    display: 'flex',
    justifyContent: 'center',
    marginTop: 8
  },
  PaperLaborSale1341: {
    height: '22%',
    width: 296,
    display: 'flex',
    justifyContent: 'center',
    marginLeft: 88
  },
  PaperLaborSale13411: {
    height: '22%',
    width: 296,
    display: 'flex',
    justifyContent: 'center'
    //marginLeft: 88
  },
  HeaderComponent1341: {
    paddingTop: 2,
    marginBottom: 5,
    paddingBottom: 9
  },
  imageComponent1341: {
    display: 'flex',
    height: 70,
    width: 70,
    margin: 'auto',
    marginTop: 20
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    width: '100%',
    filter: 'brightness(80%) !important'
  },
  PaperLaborSale1343: {
    display: 'none'
  },
  highChartContainer1340: {
    marginTop: -17,
    '@media (max-width: 1920px)': {
      width: '300px !important'
      //marginLeft: -13,
    },
    '@media (max-width: 1280px)': {
      width: '300px !important'
      //marginLeft: -37,
    },
    '@media (min-width: 2304px)': {
      width: '500px !important'
      //marginLeft: 20,
    }
  },
  visualization: {
    display: 'block'
  },
  chartHeader: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  PaperSale1343: {
    width: '47%',
    marginLeft: 4,
    border: '1px solid #75757575'
  },
  PaperSale1341: {
    width: '38%',
    marginLeft: 4,
    border: '1px solid #75757575'
  },
  PaperGpp1343: {
    dispaly: 'none',
    width: 0
  },
  PaperGpp1341: {
    width: '20%',
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#109618',
    border: '1px solid #75757575',
    '@media (max-width: 1920px)': {
      marginTop: '11% !important'
    },
    '@media screen and (min-width: 1900px)': {
      marginTop: '10% !important'
    },
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '10% !important',
    // },
    height: '20%'
  },
  PaperGpp1342: {
    width: '20%',
    marginLeft: 5,
    marginRight: 5,
    backgroundColor: '#109618',
    border: '1px solid #75757575',
    '@media (max-width: 1920px)': {
      marginTop: '11% !important'
    },
    '@media screen and (min-width: 1900px)': {
      marginTop: '10% !important'
    },
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '10% !important',
    // },
    height: '20%'
  },
  PaperGp1343: {
    width: '47%',
    border: '1px solid #75757575'
  },
  PaperGp1341: {
    width: '37%',
    marginRight: 4,
    border: '1px solid #75757575'
  },
  centerText1341: {
    fontWeight: 600,
    fontSize: 17,
    marginTop: 10,
    marginBottom: 10,
    color: '#FFF',
    textAlign: 'center'
    //marginLeft: 39
  },
  centerValue1341: {
    fontWeight: 600,
    fontSize: 30,
    color: '#FFF',
    textAlign: 'center'
    //marginLeft: 22
  },
  container1340_1: {
    textAlign: 'center',
    '@media (max-width: 1920px)': {
      marginTop: '-19px !important'
    },
    '@media screen and (min-width: 1900px)': {
      marginTop: '10% !important'
    }
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '10% !important',
    // }
  },

  PaperGpp1351: {
    display: 'none'
  },
  PaperGpp1337: {
    width: 130,
    height: '20%',
    marginTop: '8%',
    marginLeft: -65,
    marginRight: 43,
    paddingBottom: 10,
    backgroundColor: '#FF9900'
    //e1ad01
  },
  centerText1337: {
    color: '#FFF',
    marginTop: 10,
    textAlign: 'center',
    fontWeight: 600,
    marginBottom: 10,
    '@media (max-width: 1920px)': {
      fontSize: '14px !important'
    },
    '@media screen and (min-width: 1900px)': {
      fontSize: '16px !important'
    }
    // '@media screen and (min-width: 1400px)': {
    //   fontSize: '16px !important',
    // },
  },
  chartContainer1339: {
    '@media (max-width: 1920px)': {
      marginTop: '15px !important'
    }
  },
  chartContainer1335: {
    '@media (max-width: 1920px)': {
      marginTop: '25px !important'
    },
    '@media screen and (min-width: 1900px)': {
      marginTop: '1px !important'
    }
    // '@media screen and (min-width: 1400px)': {
    //   marginTop: '1px !important',
    // }
  },
  imageComponent: {
    display: 'flex',
    height: 100,
    // marginTop: 10
    marginTop: 33,
    marginLeft: 10
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    //width: '40%'
    width: '100%',
    filter: 'brightness(80%) !important'
  },
  HeaderComponent: {
    display: 'flex',
    marginBottom: 2,
    justifyContent: 'stretch',
    paddingTop: 2,
    paddingBottom: 2,
    height: 80,
    marginLeft: 4
  },
  dataValue: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  dataComponent: {
    // width: '62%',
    marginLeft: 15,
    marginTop: 10,
    // width: '75%',
    width: '78%'
  },
  dataComponentGrid: {
    marginLeft: 15,
    marginTop: 10,
    width: '83%'
  },
  dataSubValue: {
    display: 'flex',
    marginTop: 9,
    justifyContent: 'space-between',
    '@media (min-width: 2560px)': {
      width: '300px !important'
    },
    '@media (max-width: 2304px)': {
      width: '350px !important'
    },
    '@media (max-width: 1920px)': {
      width: '230px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '350px !important'
    }
    /*'@media (min-width: 2560px)': {
      width: '300px !important'
    },
    '@media (max-width: 2304px)': {
      width: '300px !important'
    },
    '@media (max-width: 1920px)': {
      width: '200px !important',
    },*/
  },
  valSubHead: {
    width: '100%',
    marginTop: 10,
    fontSize: 15,
    color: '#757575',
    '@media (max-width: 2304px)': {
      fontSize: '22px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '15px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '22px !important'
    }
  },
  titleLabel: {
    color: '#ee7600',
    textAlign: 'left !important',
    '@media (max-width: 2304px)': {
      fontSize: '24px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '16px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '24px !important'
    }
  },
  titleLabelWorkMix: {
    color: '#ee7600',
    display: 'flex',
    '@media (max-width: 2304px)': {
      fontSize: '22px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '16px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '22px !important'
    }
  },
  HeaderComponent1: {
    paddingTop: 2,
    paddingBottom: 2,
    display: 'flex',
    // justifyContent: 'flex-start',
    marginTop: 10,
    marginLeft: 9,
    justifyContent: 'space-between',
    marginRight: '3%'
  },
  lineRoTitle: {
    // marginLeft: 4,
    color: '#003d6b',
    fontWeight: 500,
    '@media (min-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 21,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (max-width: 2304px)': {
      fontSize: '26.5px !important',
      marginLeft: 23,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (max-width: 1920px)': {
      fontSize: '18px !important',
      marginLeft: 13,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 23,
      lineHeight: '1.5 !important',
      display: 'flex'
    }
  },
  lineRoTitle1337: {
    color: '#003d6b',
    fontWeight: 500,
    '@media (min-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 40,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (max-width: 2304px)': {
      fontSize: '26.5px !important',
      marginLeft: 40,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (max-width: 1920px)': {
      fontSize: '18px !important',
      marginLeft: 26,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 40,
      lineHeight: '1.5 !important',
      display: 'flex'
    }
  },
  lineRoTitle1338: {
    color: '#003d6b',
    fontWeight: 500,
    '@media (min-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 29,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (max-width: 2304px)': {
      fontSize: '26.5px !important',
      marginLeft: 30,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (max-width: 1920px)': {
      fontSize: '18px !important',
      marginLeft: 17,
      lineHeight: '1.5 !important',
      display: 'flex'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 30,
      lineHeight: '1.5 !important',
      display: 'flex'
    }
  },
  lineRoTitleAll: {
    color: '#003d6b',
    fontWeight: 500,
    '@media (min-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 39,
      lineHeight: '1.5 !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '26.5px !important',
      marginLeft: 39,
      lineHeight: '1.5 !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '18px !important',
      marginLeft: 25,
      lineHeight: '1.5 !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '26.5px !important',
      marginLeft: 39,
      lineHeight: '1.5 !important'
    }
  },
  LtLineRo: {
    marginTop: 20,
    display: 'flex',
    //marginLeft: 22,
    color: '#fff',
    '@media (min-width: 2560px)': {
      marginLeft: 39
    },
    '@media (max-width: 2304px)': {
      marginLeft: 39
    },
    '@media (max-width: 1920px)': {
      marginLeft: 25
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginLeft: 39
    }
  },
  LineRoLtValue: {
    display: 'flex',
    marginTop: 30,
    marginLeft: 22
  },
  MultiLineRoLtValue: {
    display: 'flex',
    marginLeft: 22,
    marginTop: 25
  },
  AvgJobLtValue: {
    display: 'flex',
    marginTop: 25,
    marginLeft: 22,
    justifyContent: 'space-between',
    marginRight: '3%'
  },
  targetTitle: {
    color: '#003d6b',
    fontWeight: 500
  },
  kpiTargetImage: {
    width: '24%'
  },
  HeaderComponent1346: {
    paddingTop: 2,
    paddingBottom: 2,
    display: 'flex',
    justifyContent: 'space-around',
    marginTop: 10,
    marginLeft: 9
  },
  valSubHeadTarget: {
    fontSize: 14,
    fontWeight: 500,
    color: '#003d6b',
    lineHeight: 1.5,
    '@media (min-width: 2560px)': {
      fontSize: '20px !important',
      marginLeft: '19px !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '20px !important',
      marginLeft: '18px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '14px !important',
      marginLeft: '5px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '20px !important',
      marginLeft: '18px !important'
    }
  },
  oneLineRo: {
    '@media (min-width: 2560px)': {
      marginRight: '79px !important'
    },
    '@media (max-width: 2304px)': {
      marginRight: '79px !important'
    },
    '@media (max-width: 1920px)': {
      marginRight: '59px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginRight: '79px !important'
    }
  },
  multiLineRo: {
    '@media (min-width: 2560px)': {
      marginRight: '43px !important'
    },
    '@media (max-width: 2304px)': {
      marginRight: '43px !important'
    },
    '@media (max-width: 1920px)': {
      marginRight: '34px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginRight: '43px !important'
    }
  },
  kpiHeader: {
    position: 'relative',
    top: 0
    // right: '-20px !important'
  },
  tooltipContent: {
    fontSize: 12,
    marginLeft: 7,
    width: 470,
    color: '#003d6b',
    lineHeight: '18px',
    marginTop: 10,
    marginBottom: 10
  },
  valTooltip: {
    marginLeft: 20,
    textAlign: 'left',
    display: 'block'
  },
  dotSeparationTooltip: {
    fontSize: '1.6em',
    verticalAlign: 'middle'
  },
  tooltipContentBlock3: {
    fontSize: 12,
    marginLeft: 7,
    width: 580,
    color: '#003d6b',
    lineHeight: '18px',
    marginTop: 10,
    marginBottom: 10
  },
  parentComponent: {
    display: 'flex',
    justifyContent: 'space-around',
    width: '100%'
  },
  tooltipIconMiddle: {
    transform: 'scale(0.75)',
    //  marginTop: -4,
    marginTop: -13.5,
    left: '4%'
  },
  tooltipIconGrid: {
    transform: 'scale(0.75)',
    marginTop: -4.5,
    left: '0%',
    '@media (min-width: 2560px)': {
      marginTop: '-5.5px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-5.5px !important'
    },
    '@media (max-width: 1920px)': {
      marginTop: '-6px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginTop: '-0.5px !important'
    },
    '@media (max-width: 1440)': {
      marginTop: '-4.5px !important'
    }
  },
  tooltipIconMatrix: {
    transform: 'scale(0.75)',
    marginTop: -5.5,
    left: '0%',
    '@media (min-width: 2560px)': {
      marginTop: '-5.5px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-5.5px !important'
    },
    '@media (max-width: 1920px)': {
      marginTop: '-7.5px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginTop: '-2px !important'
    },
    '@media (max-width: 1440)': {
      marginTop: '-5.5px !important'
    }
  },
  tooltipIcon: {
    transform: 'scale(0.8)',
    marginTop: -4
  },
  valLabel: {
    lineHeight: 1.5,
    '@media (min-width: 2560px)': {
      fontSize: '24px !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '24px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '16px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '24px !important'
    }
  },
  dataValueComponent: {
    fontWeight: 500,
    '@media (min-width: 2560px)': {
      lineHeight: '1.5 !important'
    },
    '@media (max-width: 2304px)': {
      lineHeight: '1.5 !important'
    }
  },
  dataValueComponentGrid: {
    '@media (min-width: 2560px)': {
      lineHeight: '1.5 !important',
      marginLeft: '22px !important'
    },
    '@media (max-width: 2304px)': {
      lineHeight: '1.5 !important',
      marginLeft: '23px !important'
    },
    '@media (max-width: 1960px)': {
      lineHeight: '1.5 !important',
      marginLeft: '13px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      lineHeight: '1.5 !important',
      marginLeft: '23px !important'
    },
    '@media (max-width: 1440px)': {
      lineHeight: '1.5 !important',
      marginLeft: '13px !important'
    }
  },
  gridDrilldownExtended: {
    position: 'absolute !important',
    right: '0px !important',
    '@media (min-width: 2560px)': {
      left: '-8.8% !important',
      marginTop: '-11px !important'
    },
    '@media (max-width: 2304px)': {
      left: '-11% !important',
      marginTop: '-11px !important'
    },
    '@media (max-width: 1920px)': {
      left: '-10.3% !important',
      marginTop: '-14px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      left: '-11% !important',
      marginTop: '-11px !important'
    },
    '@media (max-width: 1680px)': {
      left: '-11% !important',
      marginTop: '-15px !important'
    }
  },
  infoContainerMisses: {
    marginRight: 1
  },
  infoContainer1341: {
    '@media (min-width: 2560px)': {
      marginRight: '-29px !important'
    },
    '@media (max-width: 2304px)': {
      marginRight: '0px !important'
    },
    '@media (max-width: 1920px)': {
      marginRight: '4px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginRight: '0px !important'
    }
  },
  infoContainer1343: {
    '@media (min-width: 2560px)': {
      marginRight: '-29px !important'
    },
    '@media (max-width: 2304px)': {
      marginRight: '0px !important'
    },
    '@media (max-width: 1920px)': {
      marginRight: '5px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginRight: '0px !important'
    }
  },
  infoContainer1339: {
    '@media (min-width: 2560px)': {
      marginRight: '-56px !important'
    },
    '@media (max-width: 2304px)': {
      marginRight: '-2px !important'
    },
    '@media (max-width: 1920px)': {
      marginRight: '-2px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginRight: '-2px !important'
    }
  },
  infoContainer1336: {
    '@media (min-width: 2560px)': {
      marginRight: '-28px !important'
    },
    '@media (max-width: 2304px)': {
      marginRight: '-6px !important'
    },
    '@media (max-width: 1920px)': {
      marginRight: '9px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginRight: '-6px !important'
    }
  },
  smallRadioButton: {
    paddingLeft: 0,
    paddingRight: 0,
    height: 18,
    marginLeft: -2,
    backgroundColor: 'transparent !important',
    width: 22,
    '@media (min-width: 2560px)': {
      width: '31px !important',
      '& svg': {
        width: '1em',
        height: '1em'
      }
    },
    '@media (max-width: 2304px)': {
      width: '29px !important',
      '& svg': {
        width: '0.8em',
        height: '0.8em'
      }
    },
    '@media (max-width: 1920px)': {
      width: '22px !important',
      '& svg': {
        width: '0.6em',
        height: '0.6em'
      }
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '29px !important',
      '& svg': {
        width: '0.8em',
        height: '0.8em'
      }
    },
    '@media (max-width: 1440px)': {
      width: '22px !important',
      marginLeft: -2,
      '& svg': {
        width: '0.6em',
        height: '0.6em'
      }
    }
  },
  smallRadioButtonI: {
    paddingLeft: 0,
    paddingRight: 0,
    width: 19,
    height: 18,
    '& svg': {
      width: '0.5em',
      height: '0.5em'
    },
    // marginLeft: -14,
    '@media (min-width: 2560px)': {
      marginLeft: '1px !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '1px !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '1px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      marginLeft: '1px !important'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '-4px !important'
    },
    backgroundColor: 'transparent !important'
  },
  gridInternal: {
    position: 'absolute !important',
    right: '0px !important',
    '@media (min-width: 2560px)': {
      left: '-19% !important',
      marginTop: '2px !important'
    },
    '@media (max-width: 2304px)': {
      left: '42% !important',
      marginTop: '11px !important'
    },
    '@media (max-width: 1920px)': {
      left: '42.2% !important',
      marginTop: '1px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      left: '45% !important',
      marginTop: '11px !important'
    },
    '@media (max-width: 1680px)': {
      left: '42% !important',
      marginTop: '1px !important'
    },
    '@media (max-width: 1440px)': {
      left: '42.2% !important',
      marginTop: '1px !important'
    }
  },
  lbrSale1341: {
    /*'@media (min-width: 2560px)': {
      marginRight: '33px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '40px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '34px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '160px !important'
    },
    '@media (max-width: 2304px)': {
      // width: '150px !important',
      width: '160px !important'
    },
    '@media (max-width: 1920px)': {
      width: '109px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '160px !important',
      marginLeft: '2px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrSale1342: {
    /* '@media (min-width: 2560px)': {
      marginRight: '36px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '44px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '34px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '160px !important'
    },
    '@media (max-width: 2304px)': {
      // width: '150px !important',
      width: '160px !important'
    },
    '@media (max-width: 1920px)': {
      width: '109px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '160px !important',
      marginLeft: '2px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrGP1341: {
    /*'@media (min-width: 2560px)': {
      marginRight: '27px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '27px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '24px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '160px !important'
    },
    '@media (max-width: 2304px)': {
      // width: '150px !important',
      width: '160px !important'
    },
    '@media (max-width: 1920px)': {
      width: '109px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '160px !important',
      marginLeft: '2px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrSaleValue1341: {
    /*'@media (min-width: 2560px)': {
      marginRight: '-7px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '-10px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '-10px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '100px !important'
    },
    '@media (max-width: 2304px)': {
      // width: '100px !important',
      width: '100px !important'
    },
    '@media (max-width: 1920px)': {
      width: '80px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '100px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrGPValue1341: {
    /*'@media (min-width: 2560px)': {
      marginRight: '1px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '1px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '-1px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '100px !important'
    },
    '@media (max-width: 2304px)': {
      width: '100px !important'
    },
    '@media (max-width: 1920px)': {
      width: '80px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '100px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrSale1343: {
    /*'@media (min-width: 2560px)': {
      marginRight: '-7px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '-7px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '-4px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '160px !important'
    },
    '@media (max-width: 2304px)': {
      // width: '150px !important',
      width: '160px !important'
    },
    '@media (max-width: 1920px)': {
      width: '109px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '160px !important',
      marginLeft: '2px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrGP1343: {
    /*'@media (min-width: 2560px)': {
      marginRight: '22px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '31px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '24px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '160px !important'
    },
    '@media (max-width: 2304px)': {
      // width: '150px !important',
      width: '160px !important'
    },
    '@media (max-width: 1920px)': {
      width: '109px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '160px !important',
      marginLeft: '2px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrSaleValue1343: {
    // marginRight: -9
    '@media (min-width: 2560px)': {
      width: '100px !important'
    },
    '@media (max-width: 2304px)': {
      width: '100px !important'
    },
    '@media (max-width: 1920px)': {
      width: '80px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '100px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  lbrGPValue1343: {
    // marginRight: -9
    '@media (min-width: 2560px)': {
      width: '100px !important'
    },
    '@media (max-width: 2304px)': {
      width: '100px !important'
    },
    '@media (max-width: 1920px)': {
      width: '80px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '100px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  compData1344: {
    // marginRight: 22
    '@media (min-width: 2560px)': {
      width: '160px !important'
    },
    '@media (max-width: 2304px)': {
      width: '150px !important'
    },
    '@media (max-width: 1920px)': {
      width: '109px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '150px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  compValue1344: {
    /* '@media (min-width: 2560px)': {
      marginRight: '13px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '13px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '9px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '100px !important'
    },
    '@media (max-width: 2304px)': {
      width: '100px !important'
    },
    '@media (max-width: 1920px)': {
      width: '80px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '100px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  repairData1344: {
    /* '@media (min-width: 2560px)': {
      marginRight: '59px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '74px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '59px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '150px !important'
    },
    '@media (max-width: 2304px)': {
      width: '150px !important'
    },
    '@media (max-width: 1920px)': {
      width: '108px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '150px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  repairValue1344: {
    /* '@media (min-width: 2560px)': {
      marginRight: '-4px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '-9px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '-8px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '100px !important'
    },
    '@media (max-width: 2304px)': {
      width: '100px !important'
    },
    '@media (max-width: 1920px)': {
      width: '80px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '100px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  data1344: {
    // marginRight: 15
    '@media (min-width: 2560px)': {
      width: '150px !important'
    },
    '@media (max-width: 2304px)': {
      width: '150px !important'
    },
    '@media (max-width: 1920px)': {
      width: '108px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '150px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  value1344: {
    /*'@media (min-width: 2560px)': {
      marginRight: '-4px !important',
    },
    '@media (max-width: 2304px)': {
      marginRight: '-9px !important',
    },
    '@media (max-width: 1920px)': {
      marginRight: '-8px !important',
    },*/
    '@media (min-width: 2560px)': {
      width: '100px !important'
    },
    '@media (max-width: 2304px)': {
      width: '100px !important'
    },
    '@media (max-width: 1920px)': {
      width: '80px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '100px !important'
    },
    textAlign: 'left',
    fontWeight: 410,
    lineHeight: 1.2
  },
  targetSubLabel: {
    color: '#FFF'
  },
  targetSubValue: {
    color: '#FFF',
    display: 'inline-block',
    textAlign: 'left',
    '@media (min-width: 2560px)': {
      width: '45px !important'
    },
    '@media (max-width: 2304px)': {
      width: '45px !important'
    },
    '@media (max-width: 1920px)': {
      width: '30px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      width: '45px !important'
    }
  },
  orderComponent5a: {
    fontWeight: 'bold',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    marginTop: 2,
    marginLeft: -7,
    marginRight: 5,
    color: '#003d6b',
    '@media (min-width: 2560px)': {
      fontSize: '18px !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '18px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '12px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '18px !important'
    }
  },
  orderComponent3: {
    fontWeight: 'bold',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    marginTop: 2,
    marginRight: 5,
    color: '#4f3e3e',
    '@media (min-width: 2560px)': {
      fontSize: '18px !important',
      marginLeft: '-8px !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '18px !important',
      marginLeft: '-8px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '12px !important',
      marginLeft: '-10px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '18px !important',
      marginLeft: '-8px !important'
    }
  },
  orderComponent2: {
    fontWeight: 'bold',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    // marginTop: 0,
    marginLeft: -7,
    marginRight: 5,
    width: '20px !important',
    height: '20px !important',
    borderRadius: '50%',
    textAlign: 'center',
    paddingTop: '3px !important',
    minWidth: 20,
    color: '#4f3e3e',
    '@media (min-width: 2560px)': {
      fontSize: '18px !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '18px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '12px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '18px !important'
    }
  },
  orderComponent3a: {
    fontWeight: 'bold',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    marginTop: 2,
    marginRight: 5,
    color: '#4f3e3e',
    '@media (min-width: 2560px)': {
      fontSize: '18px !important',
      marginLeft: '-3px !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '18px !important',
      marginLeft: '-3px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '12px !important',
      marginLeft: '-3px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '18px !important',
      marginLeft: '-3px !important'
    }
  },
  orderComponent4: {
    fontWeight: 'bold',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    marginTop: 2,
    marginRight: 5,
    color: '#4f3e3e',
    '@media (min-width: 2560px)': {
      fontSize: '18px !important',
      marginLeft: '-4px !important'
    },
    '@media (max-width: 2304px)': {
      fontSize: '18px !important',
      marginLeft: '-4px !important'
    },
    '@media (max-width: 1920px)': {
      fontSize: '12px !important',
      marginLeft: '-7px !important'
    },
    '@media (min-width: 1920px) and (max-width: 2560px)': {
      fontSize: '18px !important',
      marginLeft: '-4px !important'
    }
  }
}));
const KPIExtendedSummary = dataSet => {
  let data = dataSet.data;
  let props = dataSet.props;
  let toggleSelection = props.gridType
    ? props.gridType
    : props.payTypeList && props.payTypeList.length > 0
    ? props.payTypeList[0]
    : '';
  let internalMissesData = '';
  if (props.internalMisses) {
    internalMissesData = props.internalMisses;
  }
  const history = useHistory();
  const session = useSelector(state => state.session);
  const [selectedValue, setSelectedValue] = useState(toggleSelection);
  const [selectedValueParts, setSelectedValueParts] = useState(toggleSelection);
  const [checked, setToggle] = useState(internalMissesData);
  const classes = useStyles();
  let data4 = {};
  const gotoSpecialMetrics = () => {
    history.push({
      pathname: '/SpecialMetrics',
      state: {
        selectedToggle: props.toggleOption,
        parent: 'Extended_View',
        payType:
          typeof toggleSelection != undefined &&
          toggleSelection != '' &&
          toggleSelection == 'Internal'
            ? 'I'
            : 'C',
        gridType: toggleSelection
      }
    });
  };
  const viewInternalMisses = event => {
    if (props.gridType != event.target.value) {
      setSelectedValue(event.target.value);
      setToggle(true);
      if (event.target.value == 'Internal') {
        props.handleInternalMisses(true);
      } else {
        props.handleInternalMisses(false);
      }
      props.handleGridType(event.target.value);
    } else {
      setSelectedValue(event.target.value);
      setToggle(false);
      if (event.target.value == 'Internal') {
        props.handleInternalMisses(true);
      } else {
        props.handleInternalMisses(false);
      }
      props.handleGridType(event.target.value);
    }
  };

  const gotoLaborGridMisses = type => {
    type == 'Labor'
      ? history.push({
          pathname: '/LaborMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Extended_View',
            previousToggle: props.toggleOption,
            payType:
              localStorage.getItem('realm') == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            previousPayType:
              localStorage.getItem('realm') == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            gridType: selectedValue,
            previousGridType: selectedValue
          }
        })
      : history.push({
          pathname: '/PartsMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Extended_View',
            previousToggle: props.toggleOption,
            payType:
              localStorage.getItem('realm') == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            previousPayType:
              localStorage.getItem('realm') == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            gridType: selectedValue,
            previousGridType: selectedValue
          }
        });
  };

  const getKpiTooltips = (chartId, gridTitle) => {
    return (
      <HtmlTooltip
        title={getTooltipConentKPI(chartId, classes, gridTitle)}
        style={{ height: 10 }}
      >
        <IconButton
          size="medium"
          className={
            chartId == 1337 || chartId == 1338
              ? classes.tooltipIconMiddle
              : chartId == 1346
              ? classes.tooltipIconGrid
              : chartId == 1353
              ? classes.tooltipIconMatrix
              : classes.tooltipIcon
          }
          classes="infoIcon"
        >
          <InfoOutlinedIcon />
        </IconButton>
      </HtmlTooltip>
    );
  };
  const HtmlTooltip = withStyles(theme => ({
    arrow: {
      color: theme.palette.common.black
    },
    tooltip: {
      minWidth: 470,
      maxWidth: 600,
      zIndex: '99',
      textAlign: 'left',
      fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
      fontSize: '14px',
      fontWeight: 'normal',
      color: '#003d6b',
      border: '1px solid #003d6b'
    }
  }))(Tooltip);
  const formatSummaryValues = (value, status) => {
    return value == null
      ? 0
      : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };
  let orderItem = props.chartId == 1341 ? 1 : 2;
  var gridTitle =
    props.gridType.substring(0, 1) == 'I' ? 'Internal' : 'Customer Pay';
  var gridTitleParts =
    props.payTypeListParts &&
    props.payTypeListParts.length > 1 &&
    props.gridType.substring(0, 1) == 'I'
      ? 'Internal'
      : 'Customer Pay';

  return (
    <React.Fragment>
      {(props.chartId == 1341 || props.chartId == 1342) && (
        <div className={classes.HeaderComponent}>
          <span className={classes.orderComponent2}>{orderItem}</span>
          <span className={classes.orderComponent3}>)</span>
          <div className={classes.imageComponent}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={
                props.chartId == 1341
                  ? `/images/kpis/lbr-gp-ro.png`
                  : `/images/kpis/prts-gp-ro.png`
              }
            />
          </div>
          <span className={classes.parentComponent}>
            <div className={classes.dataComponent}>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    {props.chartId == 1341
                      ? 'Customer Pay Labor Sales Per RO'
                      : 'Customer Pay Parts Sales Per RO'}
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>$</span>
                      <span className={classes.dataValueComponent}>
                        {formatSummaryValues(
                          !lodash.isEmpty(data) &&
                            data.labels.length > 0 &&
                            data.datasets[0].data[0] != null
                            ? data.datasets[0].data[0]
                            : 0
                        )}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Gross Profit $
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>$</span>
                      {formatSummaryValues(
                        !lodash.isEmpty(data) &&
                          data.labels.length > 0 &&
                          data.datasets[0].data[1] != null
                          ? data.datasets[0].data[1]
                          : 0
                      )}
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Gross Profit &nbsp;
                    <span className={classes.targetSubHeadPerct}>%</span>
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>
                        {!lodash.isEmpty(data) &&
                        data.labels.length > 0 &&
                        data.datasets[0].data[2] != null
                          ? data.datasets[0].data[2]
                          : 0}
                      </span>
                      <span className={classes.targetSubHeadPerct}>%</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>

              <span>
                <Typography className={classes.valSubHead}>
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataSubValue}>
                        <span
                          className={
                            props.chartId == 1341
                              ? classes.lbrSale1341
                              : classes.lbrSale1342
                          }
                        >
                          {props.chartId == 1341 ? 'Labor Sale' : 'Parts Sale'}
                        </span>
                        <span>=</span>
                        <span className={classes.lbrSaleValue1341}>
                          ${' '}
                          {formatSummaryValues(
                            !lodash.isEmpty(data) &&
                              data.labels.length > 0 &&
                              data.datasets[0].data[3] != null
                              ? data.datasets[0].data[3]
                              : 0
                          )}
                        </span>
                      </span>
                      <span className={classes.dataSubValue}>
                        <span className={classes.lbrGP1341}>Gross Profit</span>
                        <span>=</span>
                        <span className={classes.lbrGPValue1341}>
                          ${' '}
                          {formatSummaryValues(
                            !lodash.isEmpty(data) &&
                              data.labels.length > 0 &&
                              data.datasets[0].data[4] != null
                              ? data.datasets[0].data[4]
                              : 0
                          )}
                        </span>
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
            </div>
            <div className={classes.kpiHeader}>
              <span className={classes.infoContainer1341}>
                {getKpiTooltips(props.chartId)}
              </span>
            </div>
          </span>
        </div>
      )}

      {props.chartId == 1343 && (
        <div className={classes.HeaderComponent}>
          <span className={classes.orderComponent2}>3</span>
          <span className={classes.orderComponent3}>)</span>
          <div className={classes.imageComponent}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={`/images/kpis/total-gp-ro.png`}
            />
          </div>
          <span className={classes.parentComponent}>
            <div className={classes.dataComponent}>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Customer Pay Labor & Parts Sales $ Per RO
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>$</span>
                      <span>
                        {formatSummaryValues(
                          !lodash.isEmpty(data) &&
                            data.labels.length > 0 &&
                            data.datasets[0].data[0] != null
                            ? data.datasets[0].data[0]
                            : 0
                        )}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Total Gross Profit $
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>$</span>
                      {formatSummaryValues(
                        !lodash.isEmpty(data) &&
                          data.labels.length > 0 &&
                          data.datasets[0].data[1] != null
                          ? data.datasets[0].data[1]
                          : 0
                      )}
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>

              <span>
                <Typography className={classes.valSubHead}>
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataSubValue}>
                        <span className={classes.lbrSale1343}>
                          Combined Sales
                        </span>
                        <span>=</span>
                        <span className={classes.lbrSaleValue1343}>
                          ${' '}
                          {formatSummaryValues(
                            !lodash.isEmpty(data) &&
                              data.labels.length > 0 &&
                              data.datasets[0].data[2] != null
                              ? data.datasets[0].data[2]
                              : 0
                          )}
                        </span>
                      </span>
                      <span className={classes.dataSubValue}>
                        <span className={classes.lbrGP1343}>Gross Profit</span>
                        <span>=</span>
                        <span className={classes.lbrGPValue1343}>
                          ${' '}
                          {formatSummaryValues(
                            !lodash.isEmpty(data) &&
                              data.labels.length > 0 &&
                              data.datasets[0].data[3] != null
                              ? data.datasets[0].data[3]
                              : 0
                          )}
                        </span>
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
            </div>
            <span className={classes.kpiHeader}>
              <span className={classes.infoContainer1343}>
                {getKpiTooltips(1343)}
              </span>
            </span>
          </span>
        </div>
      )}

      {props.chartId == 1339 && (
        <div className={classes.HeaderComponent}>
          <span className={classes.orderComponent2}>4</span>
          <span className={classes.orderComponent3}>)</span>
          <div className={classes.imageComponent}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={`/images/kpis/hrs-per-ro.png`}
            />
          </div>
          <span className={classes.parentComponent}>
            <div className={classes.dataComponent}>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    All Sold Hours
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.FlatRateHrs.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>
                        {formatSummaryValues(props.FlatRateHrs[0].val, 1)}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Average Per Day
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.FlatRateHrs.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>
                        {props.FlatRateHrs[1].val
                          ? props.FlatRateHrs[1].val
                          : 0}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Customer Pay Average Hours Per RO
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.FlatRateHrs.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataValueComponent}>
                        {props.FlatRateHrs[2].val
                          ? props.FlatRateHrs[2].val
                          : 0}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <Typography className={classes.valSubHeadNone}>{''}</Typography>
            </div>
            <span className={classes.kpiHeader}>
              <span className={classes.infoContainer1339}>
                {getKpiTooltips(1339)}
              </span>
            </span>
          </span>
        </div>
      )}
      {props.chartId == 1337 && (
        <div>
          <div className={classes.HeaderComponent1}>
            {/* <span className={classes.orderComponent5a}>5</span>
          <span className={classes.orderComponent3} style={{marginLeft: -3}}>)</span> */}
            {/* <Typography
            gutterBottom
            color="primary"
            variant="subtitle1"
            className={classes.titleLabel}
          > */}
            <span>
              <span className={classes.orderComponent5a}>5</span>
              <span className={classes.orderComponent3a}>)</span>
            </span>
            {/* <span className={classes.lineRoTitle}>
          Customer Pay 1 Line ROs Under 60k Miles
          </span> */}
            {/* </Typography> */}

            <span className={classes.kpiHeader}>
              <span className={classes.oneLineDrilldown}>
                <Tooltip
                  title={<span style={{ fontSize: 11.5 }}>Detailed View</span>}
                >
                  <IconButton
                    size="medium"
                    classes="infoIcon"
                    style={{
                      transform: 'scale(0.75)',
                      marginTop: -14,
                      left: '24%'
                    }}
                    onClick={() => gotoSpecialMetrics()}
                  >
                    <OpenInNewOutlinedIcon />
                  </IconButton>
                </Tooltip>
              </span>
              <span className={classes.infoContainer}>
                {getKpiTooltips(1337)}
              </span>
            </span>
          </div>
          <Typography
            gutterBottom
            color="primary"
            variant="subtitle1"
            className={classes.titleLabel}
          >
            <span className={classes.lineRoTitle1337}>
              Customer Pay 1 Line ROs Under 60k Miles
            </span>
          </Typography>
          <Typography variant="h5" color="primary" className={classes.valLabel}>
            {props.LineROLtSixtyK && props.LineROLtSixtyK.length > 0 ? (
              <React.Fragment>
                <span className={classes.LtLineRo}>
                  <span>1 Line ROs =</span>&nbsp;
                  <span>{props.LineROLtSixtyK[1].val}</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;
                  <span>
                    {props.LineROLtSixtyK[3].val == '100.00'
                      ? '100'
                      : props.LineROLtSixtyK[3].val == null
                      ? 0
                      : props.LineROLtSixtyK[3].val}
                  </span>
                  <span className={classes.targetSubHeadPerct}>%</span>
                </span>
              </React.Fragment>
            ) : (
              ''
            )}
          </Typography>
          <div className={classes.LineRoLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROLtSixtyK && props.LineROLtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span className={classes.oneLineRo}>1 Line RO Value</span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>Labor</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[5].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Parts</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[6].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Total</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[7].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
          </div>
          <div className={classes.MultiLineRoLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROLtSixtyK && props.LineROLtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span className={classes.multiLineRo}>
                    Multi-Line RO Value
                  </span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>Labor</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[8].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Parts</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[9].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Total</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[10].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
          </div>
          <div className={classes.AvgJobLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROLtSixtyK && props.LineROLtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span style={{ marginRight: 12 }}>
                    Average Jobs Per Multi
                  </span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.targetSubValue}>
                    {props.LineROLtSixtyK[11].val
                      ? props.LineROLtSixtyK[11].val
                      : 0}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            {/* <span className={classes.oneLineDrilldown}>
              <Tooltip
                title={<span style={{ fontSize: 11.5 }}>Detailed View</span>}
              >
                <IconButton
                  size="medium"
                  classes="infoIcon"
                  style={{transform: 'scale(0.8)', marginTop: -14, left: '4%'}}
                  onClick={()=> gotoSpecialMetrics()}
                >
                  <OpenInNewOutlinedIcon/>
                </IconButton>
              </Tooltip>
            </span> */}
          </div>
        </div>
      )}

      {props.chartId == 1346 && (
        <React.Fragment>
          <div>
            <div className={classes.HeaderComponent1} style={{ marginTop: 0 }}>
              {/* <div className={classes.kpiTargetData1}> */}
              <span>
                <span className={classes.orderComponent5a}>6</span>
                <span className={classes.orderComponent3a}>)</span>
              </span>
              {/* <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={clsx(classes.titleLabel, classes.targetTitle)}
                >
                  <span className={classes.lineRoTitle}>
                  {gridTitle} Repair Grid Targets / Misses / % of Non-Compliance
                  </span>
                </Typography> */}
              <span className={classes.kpiHeader}>
                <span className={classes.gridDrilldownExtended}>
                  <Tooltip
                    title={<span style={{ fontSize: 11.5 }}>View Misses</span>}
                  >
                    <IconButton
                      size="medium"
                      classes="infoIcon"
                      style={{
                        transform: 'scale(0.75)',
                        left: '-48%',
                        marginTop: -1
                      }}
                      onClick={() => gotoLaborGridMisses('Labor')}
                    >
                      <OpenInNewOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </span>
                <span className={classes.infoContainerMisses}>
                  {getKpiTooltips(1346, gridTitle)}
                </span>
              </span>
            </div>
            <div className={classes.dataComponentGrid}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={clsx(classes.titleLabel, classes.targetTitle)}
              >
                <span className={classes.lineRoTitle}>
                  {gridTitle} Repair Grid Targets / Misses / % of Non-Compliance
                </span>
              </Typography>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={clsx(classes.titleLabel, classes.targetTitle)}
                >
                  <span className={classes.dataValueComponentGrid}>
                    {gridTitle} Repair Grid Targets
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  style={{ color: '#FFF' }}
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span>{formatSummaryValues(props.LaborGrid[0].val)}</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={clsx(classes.titleLabel, classes.targetTitle)}
                >
                  <span className={classes.dataValueComponentGrid}>Misses</span>
                </Typography>
                <Typography
                  variant="h5"
                  style={{ color: '#FFF' }}
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span>{formatSummaryValues(props.LaborGrid[1].val)}</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span>
                <span className={classes.dataValue}>
                  <span className={classes.gridInternal}>
                    {props.payTypeList.length > 1 ? (
                      <span className={'gridInternal'}>
                        {props.payTypeList.map((item, index) => (
                          <Tooltip
                            title={
                              <span style={{ fontSize: 11.5, marginTop: 100 }}>
                                {'View ' + item + ' Misses'}
                              </span>
                            }
                          >
                            <Radio
                              checked={props.gridType == item}
                              onChange={viewInternalMisses}
                              value={item}
                              name="radio-buttons"
                              inputProps={{ 'aria-label': 'A' }}
                              className={classes.smallRadioButton}
                              id={index}
                            />
                          </Tooltip>
                        ))}
                      </span>
                    ) : null}
                  </span>
                  {/* <span className={classes.gridDrilldownExtended} >
                    <Tooltip
                      title={<span style={{ fontSize: 11.5 }}>View Misses</span>}
                    >
                      <IconButton
                        size="medium"
                        classes="infoIcon"
                        style={{transform: 'scale(0.8)'}}
                        onClick={()=> gotoLaborGridMisses('Labor')}
                      >
                        <OpenInNewOutlinedIcon/>
                      </IconButton>
                    </Tooltip>
                  </span> */}
                  <Typography
                    gutterBottom
                    color="primary"
                    variant="subtitle1"
                    className={clsx(classes.titleLabel, classes.targetTitle)}
                  >
                    <span className={classes.dataValueComponentGrid}>
                      % of Non-Compliance
                    </span>
                  </Typography>
                  <Typography
                    variant="h5"
                    style={{ color: '#FFF' }}
                    className={classes.valLabel}
                  >
                    {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                      <React.Fragment>
                        <span>
                          {formatSummaryValues(props.LaborGrid[3].val)}
                        </span>
                        <span className={classes.targetSubHeadPerct}>%</span>
                      </React.Fragment>
                    ) : (
                      ''
                    )}
                  </Typography>
                </span>
              </span>
            </div>
          </div>
        </React.Fragment>
      )}

      {props.chartId == 1353 && (
        <React.Fragment>
          <div>
            <div className={classes.HeaderComponent1} style={{ marginTop: 0 }}>
              <span>
                <span className={classes.orderComponent5a}>7</span>
                <span className={classes.orderComponent3a}>)</span>
              </span>
              <span className={classes.kpiHeader}>
                <span className={classes.gridDrilldownExtended}>
                  <Tooltip
                    title={<span style={{ fontSize: 11.5 }}>View Misses</span>}
                  >
                    <IconButton
                      size="medium"
                      classes="infoIcon"
                      style={{
                        transform: 'scale(0.75)',
                        left: '-48%',
                        marginTop: -2
                      }}
                      onClick={() => gotoLaborGridMisses('Parts')}
                    >
                      <OpenInNewOutlinedIcon />
                    </IconButton>
                  </Tooltip>
                </span>
                <span className={classes.infoContainerMisses}>
                  {getKpiTooltips(1353, gridTitleParts)}
                </span>
              </span>
            </div>
            <div className={classes.dataComponentGrid}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={clsx(classes.titleLabel, classes.targetTitle)}
              >
                <span className={classes.lineRoTitle}>
                  {gridTitle} Parts Matrix Targets / Misses / % of
                  Non-Compliance
                </span>
              </Typography>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={clsx(classes.titleLabel, classes.targetTitle)}
                >
                  <span className={classes.dataValueComponentGrid}>
                    {gridTitleParts} Parts Matrix Targets
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  style={{ color: '#FFF' }}
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span>{formatSummaryValues(props.PartsGrid[0].val)}</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={clsx(classes.titleLabel, classes.targetTitle)}
                >
                  <span className={classes.dataValueComponentGrid}>Misses</span>
                </Typography>
                <Typography
                  variant="h5"
                  style={{ color: '#FFF' }}
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span>{formatSummaryValues(props.PartsGrid[1].val)}</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <span className={classes.gridInternal}>
                  {props.payTypeListParts.length > 1 ? (
                    <span className={'gridInternal'}>
                      {props.payTypeListParts.map((item, index) => (
                        <Tooltip
                          title={
                            <span style={{ fontSize: 11.5, marginTop: 100 }}>
                              {'View ' + item + ' Misses'}
                            </span>
                          }
                        >
                          <Radio
                            checked={props.gridType == item}
                            onChange={viewInternalMisses}
                            value={item}
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'A' }}
                            className={classes.smallRadioButton}
                            id={index}
                          />
                        </Tooltip>
                      ))}
                    </span>
                  ) : null}
                </span>
                {/* <span className={classes.gridDrilldownExtended}>
                    <Tooltip
                      title={<span style={{ fontSize: 11.5 }}>View Misses</span>}
                    >
                      <IconButton
                        size="medium"
                        classes="infoIcon"
                        style={{transform: 'scale(0.8)'}}
                        onClick={()=> gotoLaborGridMisses('Parts')}
                      >
                        <OpenInNewOutlinedIcon/>
                      </IconButton>
                    </Tooltip>
                  </span> */}
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={clsx(classes.titleLabel, classes.targetTitle)}
                >
                  <span className={classes.dataValueComponentGrid}>
                    % of Non-Compliance
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  style={{ color: '#FFF' }}
                  className={classes.valLabel}
                >
                  {!lodash.isEmpty(data) && data.labels.length > 0 ? (
                    <React.Fragment>
                      <span>{formatSummaryValues(props.PartsGrid[3].val)}</span>
                      <span className={classes.targetSubHeadPerct}>%</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>

              {/* <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel1}
                >
                  {props.PartsGrid.length > 0 ? (
                    <React.Fragment>
                    <span className={'missesGrid'}>
                  {props.internalToggleStatus == 'TRUE' ? 
                    <span className={'gridInternal'}>
                      <Tooltip
                      title={
                          <span style={{ fontSize: 11.5 , marginTop: 100}}>
                            View CP Misses
                          </span>
                        }
                      >
                        <Radio
                          checked="C"
                          value="C"
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'A' }}
                          className={classes.smallRadioButton}
                        />
                      </Tooltip>
                      <Tooltip
                      title={
                          <span style={{ fontSize: 11.5 }}>
                            View Internal Misses
                          </span>
                        }
                      >
                        <Radio
                          checked={"I"}
                          value="I"
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'A' }}
                          className={classes.smallRadioButtonI}
                        />
                      </Tooltip>
                    </span>
                  : null }
                  </span>
                  </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
               */}
            </div>
          </div>
        </React.Fragment>
      )}

      {props.chartId == 1338 && (
        <div>
          <div className={classes.HeaderComponent1}>
            <Typography
              gutterBottom
              color="primary"
              variant="subtitle1"
              className={classes.titleLabel}
            >
              <span className={classes.orderComponent5a}>8</span>
              <span className={classes.orderComponent3a}>)</span>
              <span className={classes.lineRoTitle1338}>
                Customer Pay 1 Line ROs Over 60k Miles
              </span>
            </Typography>
            <span className={classes.kpiHeader}>
              <span className={classes.oneLineDrilldown}>
                <Tooltip
                  title={<span style={{ fontSize: 11.5 }}>Detailed View</span>}
                >
                  <IconButton
                    size="medium"
                    classes="infoIcon"
                    style={{
                      transform: 'scale(0.75)',
                      marginTop: -14,
                      left: '24%'
                    }}
                    onClick={() => gotoSpecialMetrics()}
                  >
                    <OpenInNewOutlinedIcon />
                  </IconButton>
                </Tooltip>
              </span>
              <span className={classes.infoContainer}>
                {getKpiTooltips(1338)}
              </span>
            </span>
          </div>
          <Typography variant="h5" color="primary" className={classes.valLabel}>
            {props.LineROGtSixtyK && props.LineROGtSixtyK.length > 0 ? (
              <React.Fragment>
                <span className={classes.LtLineRo}>
                  <span>1 Line ROs =</span>&nbsp;
                  <span>{props.LineROGtSixtyK[1].val}</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;
                  <span>
                    {props.LineROGtSixtyK[3].val == '100.00'
                      ? '100'
                      : props.LineROGtSixtyK[3].val == null
                      ? 0
                      : props.LineROGtSixtyK[3].val}
                  </span>
                  <span className={classes.targetSubHeadPerct}>%</span>
                </span>
              </React.Fragment>
            ) : (
              ''
            )}
          </Typography>
          <div className={classes.LineRoLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROGtSixtyK && props.LineROGtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span className={classes.oneLineRo}>1 Line RO Value</span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>Labor</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[5].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Parts</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[6].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Total</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[7].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
          </div>
          <div className={classes.MultiLineRoLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROGtSixtyK && props.LineROGtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span className={classes.multiLineRo}>
                    Multi-Line RO Value
                  </span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>Labor</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[8].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Parts</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[9].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Total</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[10].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
          </div>
          <div className={classes.AvgJobLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROGtSixtyK && props.LineROGtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span style={{ marginRight: 12 }}>
                    Average Jobs Per Multi
                  </span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.targetSubValue}>
                    {props.LineROGtSixtyK[11].val
                      ? props.LineROGtSixtyK[11].val
                      : 0}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            {/* <span className={classes.oneLineDrilldown}>
            <Tooltip
              title={<span style={{ fontSize: 11.5 }}>Detailed View</span>}
            >
              <IconButton
                size="medium"
                classes="infoIcon"
                style={{transform: 'scale(0.8)', marginTop: -14}}
                onClick={()=> gotoSpecialMetrics()}
              >
                <OpenInNewOutlinedIcon/>
              </IconButton>
            </Tooltip>
          </span> */}
          </div>
        </div>
      )}

      {props.chartId == 1351 && (
        <div>
          <div className={classes.HeaderComponent1}>
            <span>
              <span className={classes.orderComponent5a}>8.1</span>
              <span className={classes.orderComponent3a}>)</span>
            </span>
            <span className={classes.kpiHeader}>
              <span className={classes.infoContainer}>
                {getKpiTooltips(1351)}
              </span>
            </span>
          </div>
          <Typography
            gutterBottom
            color="primary"
            variant="subtitle1"
            className={classes.titleLabel}
          >
            <span className={classes.lineRoTitleAll}>
              1 Line ROs for All Miles
            </span>
          </Typography>
          <Typography variant="h5" color="primary" className={classes.valLabel}>
            {props.LineRO && props.LineRO.length > 0 ? (
              <React.Fragment>
                <span className={classes.LtLineRo}>
                  <span>1 Line ROs =</span>&nbsp;
                  <span>{props.LineRO[1].val}</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;
                  <span>
                    {props.LineRO[3].val == '100.00'
                      ? '100'
                      : props.LineRO[3].val == null
                      ? 0
                      : props.LineRO[3].val}
                  </span>
                  <span className={classes.targetSubHeadPerct}>%</span>
                </span>
              </React.Fragment>
            ) : (
              ''
            )}
          </Typography>
          <div className={classes.LineRoLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineRO && props.LineRO.length > 0 ? (
                <React.Fragment>
                  <span className={classes.oneLineRo}>1 Line RO Value</span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>Labor</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineRO[5].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Parts</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineRO[6].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Total</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineRO[7].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
          </div>
          <div className={classes.MultiLineRoLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineRO && props.LineRO.length > 0 ? (
                <React.Fragment>
                  <span className={classes.multiLineRo}>
                    Multi-Line RO Value
                  </span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>Labor</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineRO[8].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Parts</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineRO[9].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>Total</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineRO[10].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
          </div>
          <div className={classes.AvgJobLtValue}>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineRO && props.LineRO.length > 0 ? (
                <React.Fragment>
                  <span style={{ marginRight: 12 }}>
                    Average Jobs Per Multi
                  </span>
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.targetSubValue}>
                    {props.LineRO[11].val ? props.LineRO[11].val : 0}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
          </div>
        </div>
      )}

      {props.chartId == 1336 && (
        <div className={classes.HeaderComponent}>
          <span className={classes.orderComponent2}>9</span>
          <span className={classes.orderComponent3}>)</span>
          <div className={classes.imageComponent}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={`/images/kpis/ro-count-share.png`}
            />
          </div>
          <span className={classes.parentComponent}>
            <div className={classes.dataComponent}>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    All ROs Per Day Average
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.ROShareData.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {props.ROShareData[1].val
                          ? props.ROShareData[1].val
                          : 0}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    % of Total Share
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.ROShareData.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {props.ROShareData[3].val &&
                        props.ROShareData[3].val == 100.0
                          ? '100'
                          : props.ROShareData[3].val &&
                            props.ROShareData[3].val != 100.0
                          ? props.ROShareData[3].val
                          : 0}
                      </span>
                      <span className={classes.targetSubHeadPerct}>%</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <Typography className={classes.valSubHeadNone}>{''}</Typography>
            </div>
            <span className={classes.kpiHeader}>
              <span className={classes.infoContainer1336}>
                {getKpiTooltips(1336)}
              </span>
            </span>
          </span>
        </div>
      )}

      {props.chartId == 1335 && (
        <div className={classes.HeaderComponent}>
          <span className={classes.orderComponent2}>10</span>
          <span className={classes.orderComponent4}>)</span>
          <div className={classes.imageComponent}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={`/images/kpis/c-w-i-total.png`}
            />
          </div>
          <span className={classes.parentComponent}>
            <div className={classes.dataComponent} style={{ width: '75%' }}>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Customer Pay
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.allCWITData.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {formatSummaryValues(props.allCWITData[0].val, 1)}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>Warranty</span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.allCWITData.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {formatSummaryValues(props.allCWITData[1].val, 1)}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>Internal</span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.allCWITData.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {formatSummaryValues(props.allCWITData[2].val, 1)}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    All Unique ROs
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.allCWITData.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {formatSummaryValues(props.allCWITData[3].val, 1)}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
            </div>
            <span className={classes.kpiHeader}>
              <span className={classes.infoContainer1336}>
                {getKpiTooltips(1335)}
              </span>
            </span>
          </span>
        </div>
      )}

      {props.chartId == 1344 && (
        <div className={classes.HeaderComponent}>
          <span className={classes.orderComponent2}>11</span>
          <span className={classes.orderComponent4}>)</span>
          <div className={classes.imageComponent}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={`/images/kpis/work-mix.png`}
            />
          </div>
          <span className={classes.parentComponent}>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabelWorkMix}
              >
                <span className={classes.dataValueComponent}>
                  Customer Pay Work Mix
                </span>
              </Typography>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Competitive
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.WorkMix.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {props.WorkMix[0].val ? props.WorkMix[0].val : 0}
                      </span>
                      <span className={classes.targetSubHeadPerct}>%</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Maintenance
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.WorkMix.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {props.WorkMix[1].val ? props.WorkMix[1].val : 0}
                      </span>
                      <span className={classes.targetSubHeadPerct}>%</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>Repair</span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.WorkMix.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {props.WorkMix[2].val ? props.WorkMix[2].val : 0}
                      </span>
                      <span className={classes.targetSubHeadPerct}>%</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span>
                <Typography className={classes.valSubHead}>
                  {props.WorkMix.length > 0 ? (
                    <React.Fragment>
                      <span className={classes.dataSubValue}>
                        <span className={classes.compData1344}>
                          Competitive
                        </span>
                        <span>=</span>
                        <span className={classes.compValue1344}>
                          $ {formatSummaryValues(props.WorkMix[3].val)}
                        </span>
                      </span>
                      <span className={classes.dataSubValue}>
                        <span className={classes.data1344}>Maintenance</span>
                        <span>=</span>
                        <span className={classes.value1344}>
                          $ {formatSummaryValues(props.WorkMix[4].val)}
                        </span>
                      </span>
                      <span className={classes.dataSubValue}>
                        <span className={classes.repairData1344}>Repair</span>
                        <span>=</span>
                        <span className={classes.repairValue1344}>
                          $ {formatSummaryValues(props.WorkMix[5].val)}
                        </span>
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
            </div>
            <span className={classes.kpiHeader}>
              <span className={classes.infoContainer1336}>
                {getKpiTooltips(1344)}
              </span>
            </span>
          </span>
        </div>
      )}

      {props.chartId == 1340 && (
        <div className={classes.HeaderComponent}>
          <span className={classes.orderComponent2}>12</span>
          <span className={classes.orderComponent4}>)</span>
          <div className={classes.imageComponent}>
            <img
              className={classes.icon}
              alt="Fixed Ops"
              src={`/images/kpis/avg-age.png`}
            />
          </div>
          <span className={classes.parentComponent}>
            <div className={classes.dataComponent}>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Average Age
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.AvgAgeMiles.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {Math.round(props.AvgAgeMiles[0].val * 2) / 2}
                      </span>
                      <span> Years</span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
              <span className={classes.dataValue}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={classes.dataValueComponent}>
                    Miles for Customer Pay & Warranty Vehicles
                  </span>
                </Typography>
                <Typography
                  variant="h5"
                  color="primary"
                  className={classes.valLabel}
                >
                  {props.AvgAgeMiles.length > 0 ? (
                    <React.Fragment>
                      <span>
                        {formatSummaryValues(props.AvgAgeMiles[1].val)}
                      </span>
                    </React.Fragment>
                  ) : (
                    ''
                  )}
                </Typography>
              </span>
            </div>
            <span className={classes.kpiHeader}>
              <span className={classes.infoContainer1336}>
                {getKpiTooltips(1340)}
              </span>
            </span>
          </span>
        </div>
      )}
    </React.Fragment>
  );
};
export default KPIExtendedSummary;
