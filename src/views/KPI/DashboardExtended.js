import React from 'react';
import GridLayout from '../Home/GridLayout';
import layoutConfig from '../Home/layoutConfig';
import layoutConfigExtended from './layoutConfigExtended';

const getInitialState = data => {
  let initialState1 = {
    data: {
      graph1: {
        chartId: 1341,
        data: [{ LaborGpRo: data.LaborGpRo }]
      },
      graph2: {
        chartId: 1342,
        data: [{ PartsGpRo: data.PartsGpRo }]
      },
      graph3: {
        chartId: 1343,
        data: [{ TotalGpRo: data.TotalGpRo }]
      },
      graph6: {
        chartId: 1339,
        data: [{ FlatRateHrs: data.FlatRateHrs }]
      },
      graph8: {
        chartId: 1337,
        data: [{ LineROLtSixtyK: data.LineROLtSixtyK }]
      },
      graph12: {
        chartId: 1346,
        data: [{ LaborGrid: data.LaborGrid }]
      },
      graph13: {
        chartId: 1353,
        data: [{ PartsGrid: data.PartsGrid }]
      },
      graph9: {
        chartId: 1338,
        data: [{ LineROGtSixtyK: data.LineROGtSixtyK }]
      },
      graph10: {
        chartId: 1351,
        data: [{ LineRO: data.LineRO }]
      },
      graph7: {
        chartId: 1336,
        data: [{ ROShareData: data.ROShareData }]
      },
      graph4: {
        chartId: 1335,
        data: [{ allCWITData: data.allCWITData }]
      },
      graph11: {
        chartId: 1344,
        data: [{ WorkMix: data.WorkMix }]
      },
      graph5: {
        chartId: 1340,
        data: [{ AvgAgeMiles: data.AvgAgeMiles }]
      }
    },

    layouts:  layoutConfigExtended,
    breakpoint: 'lg'
  };
  return initialState1;
};

const DashboardExtended = props => {
  const initialState = getInitialState(props);

  return (
    <div className="dashboard dashboard-grid-items">
      <GridLayout
        data={initialState.data}
        layouts={initialState.layouts}
        props={props}
        initialState={initialState}
        parent={props.parent}
      />
    </div>
  );
};
export default DashboardExtended;
