import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import $ from 'jquery';
import Page from 'src/components/Page';
import Charts from '../Home/Charts';
import { useHistory } from 'react-router';
import moment from 'moment';
import { getTimeZone } from '../../utils/Utils';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(3),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function Home() {
  const classes = useStyles();
  const history = useHistory();
  $('#navBarDiv .active-menu').removeClass('active-menu');
  $('#Home').addClass('active-menu');
  var timezone = getTimeZone();
  return (
    <Page className={classes.root} title="Home Extended View">
      <Charts history={history} timezone={timezone} parent={'Extended_View'}/>
    </Page>
  );
}

export default Home;
