import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect
} from 'react';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Divider,
  FormControl,
  Grid
} from '@material-ui/core';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { AgGridReact } from '@ag-grid-community/react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import moment from 'moment';
import './styles.css';
import PageHeader from 'src/components/PageHeader';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import {
  getKpiComparativeReport,
  getDrillDownMonthYears,
  getKpiToggleOptionsWithTimeZone
} from 'src/utils/hasuraServices';
import CustomHeaderGroup from '../KPIReportComparative/CustomHeaderGroup';
import KPIHeaderGroup from '../KPIReportComparative/KPIHeaderGroup';
// import KPIColumnRenderer from './KPIColumnRenderer';
// import TableCellRenderer from './TableCellRenderer';
import { getTimeZone, getYearValue } from 'src/utils/Utils';
import { TRUE } from 'sass';
import Dashboard from '../Home/Dashboard';
import { i } from 'react-dom-factories';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'

    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  }
}));
const Kpi = () => {
  const gridRef = useRef();
  const classes = useStyles();
  const containerStyle = useMemo(() => ({ height: '100%', marginTop: 8 }), []);
  const gridStyle = useMemo(() => ({ height: '100%', width: '100%' }), []);
  const [visibleCol, setVisibleCol] = useState([]);
  const [rowData, setRowData] = useState([]);
  const [filterStart, setFilterStart] = useState();
  const [filterEnd, setFilterEnd] = useState();
  const [filterChanged, setFilterChanged] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [hiddenRowId, setHiddenRowId] = useState([]);
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const [dates, setDates] = useState([]);
  const [lastWeek, setLastWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [today, setToday] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');

  const [LaborGpRo, setLaborGpRo] = useState([]);

  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  useEffect(() => {
    console.log('usess');
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);
        console.log('dataArr===', dataArr);

        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        setFilterStart(
          moment(dataArr[0].lastquarterstartdate).format('YYYY-MM-DD')
        );
        setFilterEnd(
          moment(dataArr[0].lastquarterenddate).format('YYYY-MM-DD')
        );
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
        setIsPageLoading(true);
      }
    });
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date())
          .clone()
          .format('YYYY-MM-DD');
        setSelectedDates([endDate, endDate]);
      }
    });
  }, []);
  useEffect(() => {
    console.log('ffff', filterStart, filterEnd);
    if (typeof filterStart != 'undefined' && typeof filterEnd != 'undefined') {
      getKpiComparativeReport(
        filterStart,
        filterEnd,
        1,
        2,
        null,
        '',
        '+05:30',
        null,
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
              .kpiScorecardDetails
          ) {
            // setRowData(
            //   JSON.parse(
            //     result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
            //       .kpiScorecardDetails[0].jsonData
            //   )
            // );
            let data = [
              {
                kpi_type: 'Financial',
                kpi_type_code: 'A',
                kpi_name: 'CP Labor Sales  / GP $ / GP %',
                kpi_no: '1',
                kpi_slno: '1',
                data: [
                  {
                    labor_gp_ro_sale: '1498736.28',
                    labor_gp_ro_gp: '1128233.31',
                    labor_gp_ro_gpperc: '75.27897503088401916800',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Financial',
                kpi_type_code: 'A',
                kpi_name: 'CP Parts Sales  / GP $ / GP %',
                kpi_no: '2',
                kpi_slno: '2',
                data: [
                  {
                    parts_gp_ro_sale: '1260714.90',
                    parts_gp_ro_gp: '502159.25',
                    parts_gp_ro_gpperc: '39.83130920400797991700',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Financial',
                kpi_type_code: 'A',
                kpi_name: 'CP Labor & Parts Sales / Total GP $',
                kpi_no: '3',
                kpi_slno: '3',
                data: [
                  {
                    total_gp_ro_sale: '2759451.18',
                    total_gp_ro_gp: '1630392.56',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Financial',
                kpi_type_code: 'A',
                kpi_name: 'CP Labor Sales Per/RO  / GP $ Per/RO',
                kpi_no: '4',
                kpi_slno: '4',
                data: [
                  {
                    labor_gp_ro_avgsales: '2540.5690839121314070',
                    labor_gp_ro_avggp: '1950.3954269616198140',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Financial',
                kpi_type_code: 'A',
                kpi_name: 'CP Parts Sales Per/RO  / GP $ Per/RO',
                kpi_no: '5',
                kpi_slno: '5',
                data: [
                  {
                    parts_gp_ro_avgsales: '3397.8242258518801796',
                    parts_gp_ro_avggp: '1212.5501535417325617',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Financial',
                kpi_type_code: 'A',
                kpi_name: 'Parts To Labor Ratio',
                kpi_no: '6',
                kpi_slno: '6',
                data: [
                  {
                    PartsRatio: '0.84118528177619080523',
                    LaborRatio: '1',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Volume',
                kpi_type_code: 'B',
                kpi_name: 'CP / Wty / Int / All Unique ROs',
                kpi_no: '1',
                kpi_slno: '7',
                data: [
                  {
                    cwitotal_cp: '8502',
                    cwi_w_total: '5028',
                    cwi_i_total: '7114',
                    cwi_total: '20644',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Volume',
                kpi_type_code: 'B',
                kpi_name: 'ROs Per Day Avg /  % of Total Share',
                kpi_no: '2',
                kpi_slno: '8',
                data: [
                  {
                    roshare_totalshare_afterfilter: '115.3296089385474860',
                    roshare_value_afterfilter: '100.00000000000000000000',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Volume',
                kpi_type_code: 'B',
                kpi_name: '    Avg Days Open',
                kpi_no: '3',
                kpi_slno: '9',
                data: [
                  {
                    avgdays_ro_open: '2.977717496609184266617326',
                    avgdays_totalro: '20644',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Volume',
                kpi_type_code: 'B',
                kpi_name: 'Avg Age / Miles for Customer Owned Vehicles',
                kpi_no: '4',
                kpi_slno: '10',
                data: [
                  {
                    avgage_vechicleage: '6.7082779009608278',
                    avgage_vechiclemileage: '80001.363784183296',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Volume',
                kpi_type_code: 'B',
                kpi_name: 'All Sold Hrs / Avg Per Day / CP Avg Hrs Per RO',
                kpi_no: '5',
                kpi_slno: '11',
                data: [
                  {
                    flatrate_soldhours: '31512.28',
                    flatrate_avgdaysoldhoursperro: '176.0462569832402235',
                    flatrate_cp_avgsoldhoursperro: '1.6701834862385321',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Pricing',
                kpi_type_code: 'C',
                kpi_name: 'Repair Grid Targets / Misses / % of Non-Compliance ',
                kpi_no: '1',
                kpi_slno: '12',
                data: [
                  {
                    lbrm_totalcount: null,
                    lbrm_totalfalse: null,
                    lbrm_noncompliance: null,
                    lbrm_totaltrue: null,
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Pricing',
                kpi_type_code: 'C',
                kpi_name: 'CP  C (LOF) Hrs/ Sales / ELR ',
                kpi_no: '2',
                kpi_slno: '13',
                data: [
                  {
                    cp_lof_soldhours: '2556.10',
                    cp_lof_sales: '215424.64',
                    cp_lof_elr: '84.2786432455694222',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Pricing',
                kpi_type_code: 'C',
                kpi_name: 'CP  Maint Hrs/ Sales / ELR ',
                kpi_no: '3',
                kpi_slno: '14',
                data: [
                  {
                    cp_maint_soldhours: '4289.30',
                    cp_maint_sales: '333683.49',
                    cp_maint_elr: '77.7943930245028326',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Pricing',
                kpi_type_code: 'C',
                kpi_name: 'CP  Repair Hrs/ Sales / ELR',
                kpi_no: '4',
                kpi_slno: '15',
                data: [
                  {
                    cp_repair_soldhours: '7353.50',
                    cp_repair_sales: '949590.25',
                    cp_repair_elr: '129.1344597810566397',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Pricing',
                kpi_type_code: 'C',
                kpi_name: 'CP  Total Hrs/ Sales / ELR',
                kpi_no: '6',
                kpi_slno: '17',
                data: [
                  {
                    cp_total_soldhours: '14199.90',
                    cp_total_sales: '1498736.28',
                    cp_total_elr: '105.5455517292375298',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Pricing',
                kpi_type_code: 'C',
                kpi_name: 'CP Sales  Maint. / Repair Mix',
                kpi_no: '8',
                kpi_slno: '19',
                data: [
                  {
                    workmix_maintenance_perc: '28.61555511651061725900',
                    workmix_repair_perc: '71.38444488348938274100',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Pricing',
                kpi_type_code: 'C',
                kpi_name:
                  ' CP Parts Matrix Targets / Misses / % Non-Compliance',
                kpi_no: '9',
                kpi_slno: '20',
                data: [
                  {
                    prtm_totalcount: null,
                    prtm_totalfalse: null,
                    prtm_noncompliance: null,
                    prtm_totaltrue: null,
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Under 60K Miles',
                kpi_type_code: 'D',
                kpi_name: 'CP  ROs Under 60k Miles  # Count / % of all CP',
                kpi_no: '1',
                kpi_slno: '21',
                data: [
                  {
                    linero_lt_totalcount: '2666',
                    linero_lt_totalperc: '100',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Under 60K Miles',
                kpi_type_code: 'D',
                kpi_name: 'CP 1 Line ROs Under 60k Miles  # Count / %',
                kpi_no: '2',
                kpi_slno: '22',
                data: [
                  {
                    linero_lt_oneline_rocount: '1558',
                    linero_lt_oneline_percentage: '58.43960990247561890500',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Under 60K Miles',
                kpi_type_code: 'D',
                kpi_name: ' 1 Line - Value Labor/ Parts/ Total',
                kpi_no: '3',
                kpi_slno: '23',
                data: [
                  {
                    linero_lt_oneline_avgsale: '1874.9939552837021475',
                    linero_lt_oneline_avgprtsale: '1602.8488940762824182',
                    linero_lt_oneline_totalsale: '209384.02',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Under 60K Miles',
                kpi_type_code: 'D',
                kpi_name: 'Multi-Line - Value Labor/ Parts/ Total',
                kpi_no: '4',
                kpi_slno: '24',
                data: [
                  {
                    linero_lt_multiline_avgsale: '4126.6123504651265607',
                    linero_lt_multiline_avgprtsale: '3792.0690452942813666',
                    linero_lt_multiline_totalsale: '393574.75',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Under 60K Miles',
                kpi_type_code: 'D',
                kpi_name: 'Avg Jobs on Multi-Line Ros',
                kpi_no: '5',
                kpi_slno: '25',
                data: [
                  {
                    linero_lt_Multiline_avg_job_ro: '2.4792418772563177',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Over 60K Miles',
                kpi_type_code: 'E',
                kpi_name: 'CP  ROs Over 60k Miles  # Count / % of all CP',
                kpi_no: '1',
                kpi_slno: '26',
                data: [
                  {
                    linero_gt_totalcount: '5484',
                    linero_gt_totalperc: '100',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Over 60K Miles',
                kpi_type_code: 'E',
                kpi_name: 'CP 1 Line ROs over 60k Miles  # Count / %',
                kpi_no: '2',
                kpi_slno: '27',
                data: [
                  {
                    linero_gt_oneline_rocount: '2851',
                    linero_gt_oneline_percentage: '51.98760029175784099200',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Over 60K Miles',
                kpi_type_code: 'E',
                kpi_name: ' 1 Line - Value Labor/ Parts/ Total',
                kpi_no: '3',
                kpi_slno: '28',
                data: [
                  {
                    linero_gt_oneline_sale: '369325.47',
                    linero_gt_oneline_prtsale: '264446.92',
                    linero_gt_oneline_totalsale: '633772.39',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Over 60K Miles',
                kpi_type_code: 'E',
                kpi_name: 'Multi-Line - Value Labor/ Parts/ Total',
                kpi_no: '4',
                kpi_slno: '29',
                data: [
                  {
                    linero_gt_multiline_sale: '792865.99',
                    linero_gt_multiline_prtsale: '645482.07',
                    linero_gt_multiline_totalsale: '1438348.06',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              },
              {
                kpi_type: 'Opportunities - Over 60K Miles',
                kpi_type_code: 'E',
                kpi_name: 'Avg Jobs on Multi-Line Ros',
                kpi_no: '5',
                kpi_slno: '30',
                data: [
                  {
                    linero_gt_Multiline_avg_job_ro: '2.7117356627421193',
                    start_date: null,
                    end_date: null,
                    adv_or_tech_name: 'KPI_Values',
                    adv_or_tech_id: null,
                    adv_or_tech: null
                  }
                ]
              }
            ];
            setRowData(data);

            let orderedData;
            if (chartList) {
              let filteredResult = chartList.filter(
                item => item.dbdName == 'KPI' && item.parentId == null
              );
              orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
            }
            // setCWITData([]);
            // setLineROLtSixtyK([]);
            // setLineROGtSixtyK([]);
            // setAvgAgeMiles([]);
            // setFlatRateHrs([]);
            setLaborGpRo([]);
            // setTotalGpRo([]);
            // setWorkMix([]);
            // setLaborGrid([]);
            // setLineRO([]);
            // setPartsGrid([]);
            // setROShareData([]);
            // setCWITAllDataAll([]);
            // setPartsGpRo([]);
            if (orderedData && orderedData.length > 0) {
              orderedData.map(item => {
                if (item.chartId == 1341) {
                  var resultArr = Object.entries(data[0].data[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setIsLoading(true);

                  console.log('arya33', filteredOutput);
                  setLaborGpRo(filteredOutput);
                }
              });
            }
          }
        }
      );
    }
  }, [filterStart, filterEnd]);

  const handleCallback = (event, picker) => {
    console.log('picker==', picker);
    setFilterStart(picker.startDate.format('YYYY-MM-DD'));
    setFilterEnd(picker.endDate.format('YYYY-MM-DD'));
    setFilterChanged(true);
  };
  const setColDef = val => {
    var col = visibleCol;
    col.push(val);

    setVisibleCol(col);
  };

  return (
    <React.Fragment>
      {/* <Kpi history={history} /> */}
      {isPageLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <Grid
              xs={12}
              className={clsx(classes.headerItem, 'main-title-kpi')}
            >
              <Grid xs={10}>
                <PageHeader
                  title={'KPI Report #2 - Advisor Comparative'}
                  // title={session.kpiAdvisor[0] != 'All' ? 'KPI Report 1 - Individual Advisor' : 'KPI Report 1 - All Advisors'}
                  hideTitle={false}
                  isFrom={'details'}
                  // exportPDF={true}
                  // exportKpiReportGrid={exportKpiReportGrid}
                  //setResetDashboard={this.setResetDashboard}
                />
              </Grid>
              {/* <div>
                      <Button
                        variant="contained"
                        className={'editGoals-Btn'}
                        onClick={handleclick}
                      >
                        <Typography variant="body1" align="left">
                          Edit Goals
                        </Typography>
                      </Button>
                    </div> */}
              <Grid xs={2}>
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={clsx(classes.formControl, 'input-container')}
                >
                  <DateRangePicker
                    initialSettings={{
                      // maxDate: {
                      //   date: new Date(),
                      // },
                      // minDate:{
                      //   date: (selectedDates[1])
                      // },

                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },
                      ranges: {
                        ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        today]: [moment().toDate(), moment().toDate()],
                        ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        yesterDay]: [
                          moment()
                            .subtract(1, 'days')
                            .toDate(),
                          moment()
                            .subtract(1, 'days')
                            .toDate()
                        ],
                        ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;' +
                        dayBfYest]: [
                          moment()
                            .subtract(2, 'days')
                            .toDate(),
                          moment()
                            .subtract(2, 'days')
                            .toDate()
                        ],
                        ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastWeek]: [
                          moment(dates[0].lastweekstartdate).toDate(),
                          moment(dates[0].lastweekenddate).toDate()
                        ],
                        // 'Last 7 Days': [
                        //   moment()
                        //     .subtract(6, 'days')
                        //     .toDate(),
                        //   moment().toDate()
                        // ],
                        // 'Last 30 Days': [
                        //   moment()
                        //     .subtract(29, 'days')
                        //     .toDate(),
                        //   moment().toDate()
                        // ],
                        ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        mtd]: [
                          moment()
                            .startOf('month')
                            .toDate(),
                          moment()
                            .endOf('month')
                            .toDate()
                        ],
                        ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastMonth]: [
                          moment()
                            .subtract(1, 'month')
                            .startOf('month')
                            .toDate(),
                          moment()
                            .subtract(1, 'month')
                            .endOf('month')
                            .toDate()
                        ],
                        ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastThreeMonths]: [
                          moment(dates[0].lastthreemonthstartdate).toDate(),
                          moment(dates[0].lastthreemonthenddate).toDate()
                        ],
                        ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastQtr]: [
                          moment(dates[0].lastquarterstartdate).toDate(),
                          moment(dates[0].lastquarterenddate).toDate()
                        ],
                        ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        ytd]: [
                          moment(dates[0].ytdstartdate).toDate(),
                          moment(dates[0].ytdenddate).toDate()
                        ],
                        ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastTwelveMonths]: [
                          moment(dates[0].lasttwelvemonthstartdate).toDate(),
                          moment(dates[0].lasttwelvemonthenddate).toDate()
                        ],
                        ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastYear]: [
                          moment(dates[0].lastyearstartdate).toDate(),
                          moment(dates[0].lastyearenddate).toDate()
                        ]
                      },
                      maxDate: moment().toDate(),
                      alwaysShowCalendars: true,
                      applyClass: clsx(classes.calButton, 'apply-btn'),
                      cancelClass: clsx(classes.calButton, 'apply-btn'),

                      startDate: moment(dates[0].lastquarterstartdate).toDate(),
                      endDate: moment(dates[0].lastquarterenddate).toDate()
                      //showDropdowns: true
                    }}
                    // onEvent={this.handleEventCallback}
                    onApply={handleCallback}
                  >
                    <input
                      type="text"
                      className="datepicker"
                      id="picker"
                      name="picker"
                      aria-labelledby="label-picker"
                    />
                    {/* <TextField
                        id="outlined-basic"
                        label="Select Date"
                        size="small"
                        //onChange={}
                        value={this.state.value}
                        variant="outlined"
                      /> */}
                  </DateRangePicker>
                  <label class="labelpicker" for="picker" id="label-picker">
                    <div class="textpicker">Select Date</div>
                  </label>
                </FormControl>
              </Grid>
            </Grid>
            <Divider />

            {isLoading == false ? (
              <div>
                <Box className={classes.boxClass}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    className={classes.boxClass}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : LaborGpRo.length > 0 ? (
              <div id={'kpi-report-2'} style={containerStyle}>
                <>
                  <Dashboard
                    // toggleOption={toggleOption}
                    LaborGpRo={LaborGpRo}
                    // PartsGpRo={PartsGpRo}
                    // TotalGpRo={TotalGpRo}
                    // FlatRateHrs={FlatRateHrs}
                    // allCWITData={CWITAllData}
                    // ROShareData={ROShareData}
                    // AvgAgeMiles={AvgAgeMiles}
                    // LineROLtSixtyK={LineROLtSixtyK}
                    // LineROGtSixtyK={LineROGtSixtyK}
                    // WorkMix={WorkMix}
                    // LineRO={LineRO}
                    // LaborGrid={LaborGrid}
                    // PartsGrid={PartsGrid}
                    // internalMisses={internalMisses}
                    parent={'Home'}
                  />
                </>
              </div>
            ) : null}
          </Paper>
        </div>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </React.Fragment>
  );
};
export default Kpi;
