import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect
} from 'react';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Divider,
  FormControl,
  Grid,
  Button
} from '@material-ui/core';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { AgGridReact } from '@ag-grid-community/react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import moment from 'moment';
import PageHeader from 'src/components/PageHeader';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import {
  getHomeKpis,
  getDrillDownMonthYears,
  getKpiToggleOptionsWithTimeZone
} from 'src/utils/hasuraServices';
import CustomHeaderGroup from '../KPIReportComparative/CustomHeaderGroup';
import KPIHeaderGroup from '../KPIReportComparative/KPIHeaderGroup';
// import KPIColumnRenderer from './KPIColumnRenderer';
// import TableCellRenderer from './TableCellRenderer';
import { getTimeZone, getYearValue } from 'src/utils/Utils';
import { TRUE } from 'sass';
import Dashboard from '../Home/Dashboard';
import { i } from 'react-dom-factories';
import $ from 'jquery';
import { useSelector, useDispatch } from 'react-redux';
import { useHistory } from 'react-router';
import {
  setKpiHomeToggle,
  setKpiGraphicsToggleStartDate,
  setKpiGraphicsToggleEndDate
} from 'src/actions';
import { ReactSession } from 'react-client-session';

var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'

    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  loaderGrid: {
    // height: 150,
    height: window.innerHeight + 'px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b',
    marginTop: '-8%'
  }
}));
const Kpi = props => {
  const session = useSelector(state => state.session);
  const dispatch = useDispatch();

  if (
    localStorage.getItem('storeChange') == 'true' ||
    localStorage.getItem('storeChange') == true
  ) {
    if (
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.filterStart
    ) {
      props.history.location.state.filterStart = '';
    }
    if (
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.filterEnd
    ) {
      props.history.location.state.filterEnd = '';
    }
    localStorage.removeItem('storeChange');
  }

  let startDate =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.filterStart
      ? props.history.location.state.filterStart
      : session.kpiGraphicsToggleStartDate != ''
      ? session.kpiGraphicsToggleStartDate
      : session.kpiToggleStartDate != ''
      ? session.kpiToggleStartDate
      : '';
  let endDate =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.filterEnd
      ? props.history.location.state.filterEnd
      : session.kpiGraphicsToggleEndDate != ''
      ? session.kpiGraphicsToggleEndDate
      : session.kpiToggleEndDate != ''
      ? session.kpiToggleEndDate
      : '';

  const gridRef = useRef();
  const classes = useStyles();
  const containerStyle = useMemo(() => ({ height: '100%', marginTop: 8 }), []);
  const gridStyle = useMemo(() => ({ height: '100%', width: '100%' }), []);
  const [visibleCol, setVisibleCol] = useState([]);
  const [rowData, setRowData] = useState([]);
  const [filterStart, setFilterStart] = useState(startDate);
  const [filterEnd, setFilterEnd] = useState(endDate);
  const [filterChanged, setFilterChanged] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [hiddenRowId, setHiddenRowId] = useState([]);
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const [dates, setDates] = useState([]);
  const [thisWeek, setThisWeek] = useState('');
  const [lastWeek, setLastWeek] = useState('');
  const [lastTwoWeek, setLastTwoWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [today, setToday] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');
  const history = useHistory();

  const [LaborGpRo, setLaborGpRo] = useState([]);
  const [PartsGpRo, setPartsGpRo] = useState([]);
  const [TotalGpRo, setTotalGpRo] = useState([]);
  const [CWITAllData, setCWITAllDataAll] = useState([]);
  const [AvgAgeMiles, setAvgAgeMiles] = useState([]);
  const [FlatRateHrs, setFlatRateHrs] = useState([]);
  const [ROShareData, setROShareData] = useState([]);
  const [LaborGrid, setLaborGrid] = useState([]);
  const [PartsGrid, setPartsGrid] = useState([]);
  const [LineROLtSixtyK, setLineROLtSixtyK] = useState([]);
  const [LineROGtSixtyK, setLineROGtSixtyK] = useState([]);
  const [WorkMix, setWorkMix] = useState([]);
  const [LineRO, setLineRO] = useState([]);
  const [pageLoader, setPageLoader] = useState(false);
  const [noData, setNoData] = useState(false);
  const [filterText, setFilterText] = useState(session.kpiHomeToggle);

  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );

  let closedDate = localStorage.getItem('closedDate');

  useEffect(() => {
    console.log('usess');
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);
        console.log('dataArr===', dataArr);

        setThisWeek(
          moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY")
        );
        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setLastTwoWeek(
          moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        if (filterStart == '' && filterEnd == '') {
          if (localStorage.getItem('kpiDataStatus') == 1) {
            setFilterStart(
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'));
          } else {
            setFilterStart(
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
          }
        }
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
        setIsPageLoading(true);
        setFilterChanged(true);
      }
    });
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date())
          .clone()
          .format('YYYY-MM-DD');
        setSelectedDates([endDate, endDate]);
      }
    });
    dispatch(setKpiGraphicsToggleStartDate(filterStart));
    dispatch(setKpiGraphicsToggleEndDate(filterEnd));
  }, []);
  useEffect(() => {
    if (filterStart != '' && filterEnd != '') {
      setFilterChanged(true);
    }
  }, [session.serviceAdvisor, session.technician]);
  useEffect(() => {
    console.log('ffff', filterStart, filterEnd, filterChanged);
    // if (typeof filterStart != 'undefined' && typeof filterEnd != 'undefined') {
    if (filterChanged) {
      setPageLoader(true);

      getHomeKpis(
        filterStart,
        filterEnd,
        session.serviceAdvisor.includes('All') ? null : session.serviceAdvisor,
        session.technician.includes('All') ? null : session.technician,
        2,
        'kpi_scorecard',
        getTimeZone(),
        1,
        null,
        'C',
        'Customer',
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
              .kpiScorecardDetails
          ) {
            let data = JSON.parse(
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails[0].jsonData
            );

            setPageLoader(false);
            setRowData(data);
            setFilterChanged(false);
            // const mostallValuesNull = lodash.every(
            //   data.slice(0, 20),
            //   item =>
            //     lodash.isNull(item.kpi_value) ||
            //     item.kpi_value == 0 ||
            //     item.kpi_value == '0/0/0' ||
            //     item.kpi_value == '0.0/0/0'
            // );
            const allValuesNull = lodash.every(
              data,
              item =>
                lodash.isNull(item.kpi_value) ||
                isAllZeroValue(item.kpi_value) ||
                // item.kpi_value == 0 ||
                // item.kpi_value == '0/0/0' ||
                // item.kpi_value == '0.0/0/0' ||
                // item.kpi_value == '0/0.0/0' ||
                // item.kpi_value == '0/0/0/0/0/0/0/0/0/0/0/0/0/0.0/0/0/0/0/0/0' ||
                // item.kpi_value == '0.0' ||
                // item.kpi_value == '0/0' ||
                (item.kpi_name == 'Parts to Labor Ratio' &&
                  item.kpi_value == '0.0/1.0')
            );
            if (allValuesNull) {
              setNoData(true);
              setIsLoading(true);
            } else {
              setNoData(false);
              getChartsData(data);
            }
          }
        }
      );
    }
  }, [filterChanged, session.serviceAdvisor]);

  const isAllZeroValue = value => {
    if (value === 0 || value === '0.0' || value === '0/0') return true;

    if (typeof value === 'string') {
      return value.split('/').every(part => parseFloat(part) === 0);
    }

    return false;
  };

  const getChartsData = data => {
    // const groupedData = lodash.groupBy(data, 'kpi_no');
    const groupedData = lodash.groupBy(
      data,
      item => `${item.kpi_no}-${item.kpi_type_code}`
    );
    const groupedArray = Object.values(groupedData);
    let orderedData;
    if (chartList) {
      let filteredResult = chartList.filter(
        item => item.dbdName == 'KPI' && item.parentId == null
      );
      orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    }
    setLineROLtSixtyK([]);
    setLineROGtSixtyK([]);
    setAvgAgeMiles([]);
    setFlatRateHrs([]);
    setLaborGpRo([]);
    setTotalGpRo([]);
    setWorkMix([]);
    setLaborGrid([]);
    setLineRO([]);
    setPartsGrid([]);
    setROShareData([]);
    setCWITAllDataAll([]);
    setPartsGpRo([]);
    if (orderedData && orderedData.length > 0) {
      orderedData.map(item => {
        if (item.chartId == 1341) {
          var filteredOutput = groupedArray[0].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));

          if (filteredOutput[1].label == 'Labor Gross Profit') {
            let newItem = {
              label: 'Labor Gross Profit%',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[1]
                : 0
            };
            filteredOutput.push(newItem);
            filteredOutput[1].val = filteredOutput[1].val
              ? filteredOutput[1].val.split('/')[0]
              : 0;

            filteredOutput = [
              filteredOutput[2],
              filteredOutput[3],
              filteredOutput[4],
              filteredOutput[0],
              filteredOutput[1]
            ];
          }

          setIsLoading(true);
          setLaborGpRo(filteredOutput);
        }
        if (item.chartId == 1342) {
          var filteredOutput = groupedArray[1].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          if (filteredOutput[1].label == 'Parts Gross Profit') {
            const newItem = {
              label: 'Parts Gross Profit%',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[1]
                : 0
            };
            filteredOutput.push(newItem);
            filteredOutput[1].val = filteredOutput[1].val
              ? filteredOutput[1].val.split('/')[0]
              : 0;

            filteredOutput = [
              filteredOutput[2],
              filteredOutput[3],
              filteredOutput[4],
              filteredOutput[0],
              filteredOutput[1]
            ];
          }
          setIsLoading(true);
          setPartsGpRo(filteredOutput);
        }
        if (item.chartId == 1343) {
          var filteredOutput = groupedArray[2].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          var filteredOutputNew = [];
          filteredOutputNew.push(
            {
              label: 'Labor + Parts Sales Per Ro',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[1]
                : 0
            },
            {
              label: 'Labor + Parts GP Per Ro',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[2]
                : 0
            },
            {
              label: 'Labor + Parts Sales',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[0]
                : 0
            },
            {
              label: 'Labor + Parts GP',
              val: filteredOutput[1].val
            }
          );

          setIsLoading(true);
          setTotalGpRo(filteredOutputNew);
        }
        if (item.chartId == 1335) {
          var filteredOutput = groupedArray[8].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          setIsLoading(true);
          setCWITAllDataAll(filteredOutput);
        }
        if (item.chartId == 1340) {
          var filteredOutput = groupedArray[10].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          setIsLoading(true);
          setAvgAgeMiles(filteredOutput);
        }

        if (item.chartId == 1336) {
          var filteredOutput = groupedArray[9].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          let filteredOutputNew = [];
          //  totalvalue/totalro/workingdays/totalshare_duration/totalvalue_duration/||totalro_duration
          filteredOutputNew.push(
            {
              label: 'totalro',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[1]
                : 0
            },
            {
              label: 'totalshare',
              //val: filteredOutput[0].val
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[6]
                : 0
            },
            {
              label: 'workingdays',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[2]
                : 0
            },
            {
              label: 'totalvalue',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[0]
                : 0
            },

            {
              label: 'totalroDuration',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[5]
                : 0
            },
            {
              label: 'totalshareDuration',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[3]
                : 0
            },
            {
              label: 'totalvalueDuration',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[4]
                : 0
            }
          );
          setIsLoading(true);
          setROShareData(filteredOutputNew);
        }
        if (item.chartId == 1346) {
          var filteredOutput = groupedArray[4].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          setIsLoading(true);
          let filteredOutputNew = [];
          var resultArray = [];
          if (filteredOutput[0].val != null && filteredOutput[0].val != '') {
            resultArray = filteredOutput[0].val
              .split(/\/|<b>|<\/b>/)
              .filter(function(element) {
                return element.trim() !== '';
              });
          }

          filteredOutputNew.push(
            {
              label: 'totalcount',
              val: resultArray.length > 0 ? resultArray[0] : 0
            },
            {
              label: 'totalfalse',
              val: resultArray.length > 0 ? resultArray[1] : 0
            },
            {
              label: 'totaltrue',
              val: resultArray.length > 0 ? resultArray[3] : 0
            },
            {
              label: 'noncompliance',
              val: resultArray.length > 0 ? resultArray[2] : 0
            }
          );

          setLaborGrid(filteredOutputNew);
        }
        if (item.chartId == 1353) {
          var filteredOutput = groupedArray[4].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          setIsLoading(true);
          let filteredOutputNew = [];
          var resultArray = [];
          if (filteredOutput[1].val != null && filteredOutput[1].val != '') {
            resultArray = filteredOutput[1].val
              .split(/\/|<b>|<\/b>/)
              .filter(function(element) {
                return element.trim() !== '';
              });
          }

          filteredOutputNew.push(
            {
              label: 'totalcount',
              val: resultArray.length > 0 ? resultArray[0] : 0
            },
            {
              label: 'totalfalse',
              val: resultArray.length > 0 ? resultArray[1] : 0
            },
            {
              label: 'totaltrue',
              val: resultArray.length > 0 ? resultArray[3] : 0
            },
            {
              label: 'noncompliance',
              val: resultArray.length > 0 ? resultArray[2] : 0
            }
          );

          setPartsGrid(filteredOutputNew);
        }
        if (item.chartId == 1337) {
          var filteredOutput = groupedArray[11].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          var filteredOutput1 = groupedArray[12].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          var filteredOutput2 = groupedArray[13].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          let filteredOutputNew = [];
          filteredOutputNew.push(
            {
              label: 'totalRos',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[0]
                : 0
            },
            {
              label: 'onelineRos',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[0]
                : 0
            },
            {
              label: 'multilineRos',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[2]
                : 0
            },
            {
              label: 'onelinePercentage',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[1]
                : 0
            },
            {
              label: 'multilinePercentage',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[3]
                : 0
            },
            {
              label: 'onelineLbrsale',
              val: filteredOutput1[0].val
            },
            {
              label: 'onelinePrtsale',
              val: filteredOutput1[1].val
            },
            {
              label: 'onelineTotalsale',
              val: filteredOutput1[2].val
            },
            {
              label: 'multilineLbrsale',
              val: filteredOutput2[0].val
            },
            {
              label: 'multilinePrtsale',
              val: filteredOutput2[1].val
            },
            {
              label: 'multilineTotalsale',
              val: filteredOutput2[2].val
            },
            {
              label: 'multilineJobcount',
              val: filteredOutput2[3].val
            }
          );
          setIsLoading(true);
          setLineROLtSixtyK(filteredOutputNew);
        }
        if (item.chartId == 1338) {
          var filteredOutput = groupedArray[16].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          var filteredOutput1 = groupedArray[17].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          var filteredOutput2 = groupedArray[18].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          let filteredOutputNew = [];
          filteredOutputNew.push(
            {
              label: 'totalRos',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[0]
                : 0
            },
            {
              label: 'onelineRos',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[0]
                : 0
            },
            {
              label: 'multilineRos',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[2]
                : 0
            },
            {
              label: 'onelinePercentage',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[1]
                : 0
            },
            {
              label: 'multilinePercentage',
              val: filteredOutput[1].val
                ? filteredOutput[1].val.split('/')[3]
                : 0
            },
            {
              label: 'onelineLbrsale',
              val: filteredOutput1[0].val
            },
            {
              label: 'onelinePrtsale',
              val: filteredOutput1[1].val
            },
            {
              label: 'onelineTotalsale',
              val: filteredOutput1[2].val
            },
            {
              label: 'multilineLbrsale',
              val: filteredOutput2[0].val
            },
            {
              label: 'multilinePrtsale',
              val: filteredOutput2[1].val
            },
            {
              label: 'multilineTotalsale',
              val: filteredOutput2[2].val
            },
            {
              label: 'multilineJobcount',
              val: filteredOutput2[3].val
            }
          );
          setIsLoading(true);
          setLineROGtSixtyK(filteredOutputNew);
        }
        if (item.chartId == 1339) {
          var filteredOutput = groupedArray[10].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));
          setIsLoading(true);
          let filteredOutputNew = [];
          filteredOutputNew.push(
            {
              label: 'totalsoldhours',
              val: filteredOutput[2].val
            },
            {
              label: 'avgsoldhoursperdays',
              val: filteredOutput[3].val
            },
            {
              label: 'avgsoldhoursperro',
              val: filteredOutput[4].val
            }
          );
          setFlatRateHrs(filteredOutputNew);
        }
        if (item.chartId == 1344) {
          var filteredOutput = groupedArray[7].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));

          //maintenance sale,competitive sale, repair sale, competitiveperc
          let resultArray = [];
          if (filteredOutput[0].val != null && filteredOutput[0].val != '') {
            resultArray = filteredOutput[0].val.split('/');
          }
          setIsLoading(true);
          let filteredOutputNew = [];
          filteredOutputNew.push(
            {
              label: 'competitive',
              val: resultArray.length > 0 ? resultArray[5] : 0
            },
            {
              label: 'maintenance',
              val: resultArray.length > 0 ? resultArray[6] : 0
            },
            {
              label: 'repair',
              val: resultArray.length > 0 ? resultArray[7] : 0
            },
            {
              label: 'competitive Sale',
              val: resultArray.length > 0 ? resultArray[3] : 0
            },
            {
              label: 'maintenance Sale',
              val: resultArray.length > 0 ? resultArray[2] : 0
            },
            {
              label: 'repair Sale',
              val: resultArray.length > 0 ? resultArray[4] : 0
            }
          );
          setWorkMix(filteredOutputNew);
        }
        if (item.chartId == 1351) {
          var filteredOutput = groupedArray[11].map(item => ({
            label: item.kpi_name,
            val: item.kpi_value
          }));

          // oneline_lbrsale, oneline_prtsale, oneline_totalsale, multiline_lbrsale, multiline_prtsale, multiline_totalsale,	total_ros, oneline_percentage, oneline_ros,  multiline_percentage,multiline_ros, Multilibe_avg_job_ro
          let filteredOutputNew = [];
          filteredOutputNew.push(
            {
              label: 'totalRos',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[8]
                : 0
            },
            {
              label: 'onelineRos',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[10]
                : 0
            },
            {
              label: 'multilineRos',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[12]
                : 0
            },
            {
              label: 'onelinePercentage',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[9]
                : 0
            },
            {
              label: 'multilinePercentage',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[11]
                : 0
            },
            {
              label: 'onelineLbrsale',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[14]
                : 0
            },
            {
              label: 'onelinePrtsale',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[15]
                : 0
            },
            {
              label: 'onelineTotalsale',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[16]
                : 0
            },
            {
              label: 'multilineLbrsale',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[17]
                : 0
            },
            {
              label: 'multilinePrtsale',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[18]
                : 0
            },
            {
              label: 'multilineTotalsale',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[19]
                : 0
            },
            {
              label: 'multilineJobcount',
              val: filteredOutput[0].val
                ? filteredOutput[0].val.split('/')[13]
                : 0
            }
          );
          setLineRO(filteredOutputNew);
        }
      });
    }
  };

  const gotoVisualization = chartId => {
    let selectedId = $(
      '.react-grid-item.react-draggable.cssTransforms.react-resizable.selected'
    ).attr('id');
    const container = document.querySelector('.makeStyles-container-3');
    if (selectedId) {
      $('#' + selectedId).removeClass('selected');
    }
    var element = document.getElementById('chartContainer_' + chartId);

    if (element) {
      element.scrollIntoView({ block: 'center' });
      $('#chartContainer_' + chartId).addClass('selected');
      $('#visualization_' + chartId).css('display', 'block');
    }
  };

  const handleCallback = (event, picker) => {
    console.log('picker==', picker);
    setFilterStart(picker.startDate.format('YYYY-MM-DD'));
    setFilterEnd(picker.endDate.format('YYYY-MM-DD'));
    setFilterChanged(true);
    dispatch(
      setKpiGraphicsToggleStartDate(picker.startDate.format('YYYY-MM-DD'))
    );
    dispatch(setKpiGraphicsToggleEndDate(picker.endDate.format('YYYY-MM-DD')));
    localStorage.setItem(
      'kpiGraphicsStartDate',
      picker.startDate.format('YYYY-MM-DD')
    );
    localStorage.setItem(
      'kpiGraphicsFilterEndDate',
      picker.endDate.format('YYYY-MM-DD')
    );
    if (
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.parent &&
      props.history.location.state.parent == 'Home'
    ) {
      getFilterText(picker.chosenLabel);
    }
  };

  const getFilterText = label => {
    var filterText;

    if (label.includes('Yesterday')) {
      filterText = 'YESDT';
    } else if (label.includes('Day Before Yest')) {
      filterText = 'DBYESDT';
    } else if (label.includes('Last Week')) {
      filterText = 'LWEEK';
    } else if (label.includes('This Month')) {
      filterText = 'MTD';
    } else if (label.includes('Last Month')) {
      filterText = 'LMONTH';
    } else if (label.includes('Last 3 Mths')) {
      filterText = 'PLMTHREE';
    } else if (label.includes('Last Qtr')) {
      filterText = 'LQRTR';
    } else if (label.includes('YTD')) {
      filterText = 'YTD';
    } else if (label.includes('Last 12 Mths')) {
      filterText = 'PLYONE';
    } else if (label.includes('Last Year')) {
      filterText = 'LYEAR';
    }
    setFilterText(filterText);
    dispatch(setKpiHomeToggle(filterText));
    ReactSession.set('kpiHomeToggle', filterText);

    return filterText;
  };
  const setColDef = val => {
    var col = visibleCol;
    col.push(val);

    setVisibleCol(col);
  };

  const renderBackButton = () => {
    dispatch(setKpiHomeToggle(filterText));
    ReactSession.set('kpiHomeToggle', filterText);

    history.push({
      pathname: '/Home',
      state: {
        filterStart: filterStart,
        // props.history &&
        // props.history.location &&
        // props.history.location.state &&
        // props.history.location.state.filterStart
        //   ? props.history.location.state.filterStart
        //   : filterStart,
        filterEnd: filterEnd
        // props.history &&
        // props.history.location &&
        // props.history.location.state &&
        // props.history.location.state.filterEnd
        //   ? props.history.location.state.filterEnd
        //   : filterEnd
      }
    });
  };
  return (
    <React.Fragment>
      {/* <Kpi history={history} /> */}
      {isPageLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <Grid
              xs={12}
              className={clsx(classes.headerItem, 'main-title-kpi')}
            >
              {props.history.location &&
                props.history.location.state &&
                props.history.location.state.parent &&
                props.history.location.state.parent == 'Home' && (
                  <Grid>
                    <Button
                      variant="contained"
                      className={'bck-btn-kpi'}
                      onClick={renderBackButton}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  </Grid>
                )}
              <Grid xs={10}>
                <PageHeader
                  title={'KPI Graphics'}
                  hideTitle={false}
                  isFrom={'details'}
                />
              </Grid>

              <Grid xs={2}>
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={clsx(classes.formControl, 'input-container')}
                >
                  <DateRangePicker
                    initialSettings={{
                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },

                      ranges: {
                        // ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        // today]: [
                        //   moment(closedDate).toDate(),
                        //   moment(closedDate).toDate()
                        // ],
                        ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        yesterDay]: [
                          moment(dates[0] && dates[0].yesterday).toDate(),
                          moment(dates[0] && dates[0].yesterday).toDate()
                        ],
                        ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        dayBfYest]: [
                          moment(
                            dates[0] && dates[0].dayBeforeYesterday
                          ).toDate(),
                          moment(
                            dates[0] && dates[0].dayBeforeYesterday
                          ).toDate()
                        ],
                        ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        thisWeek]: [
                          moment(dates[0].thisweekstartdate).toDate(),
                          moment(dates[0].thisweekenddate).toDate()
                        ],
                        ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastWeek]: [
                          moment(dates[0].lastweekstartdate).toDate(),
                          moment(dates[0].lastweekenddate).toDate()
                        ],
                        ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastTwoWeek]: [
                          moment(dates[0].lasttwoweekstartdate).toDate(),
                          moment(dates[0].lasttwoweekenddate).toDate()
                        ],

                        ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        mtd]: [
                          moment(dates[0] && dates[0].mtdstartdate).toDate(),
                          moment(dates[0] && dates[0].mtdenddate).toDate()
                        ],
                        ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastMonth]: [
                          moment(
                            dates[0] && dates[0].lastmonthstartdate
                          ).toDate(),
                          moment(dates[0] && dates[0].lastmonthenddate).toDate()
                        ],
                        ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastThreeMonths]: [
                          moment(dates[0].lastthreemonthstartdate).toDate(),
                          moment(dates[0].lastthreemonthenddate).toDate()
                        ],
                        ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastQtr]: [
                          moment(dates[0].lastquarterstartdate).toDate(),
                          moment(dates[0].lastquarterenddate).toDate()
                        ],
                        ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        ytd]: [
                          moment(dates[0].ytdstartdate).toDate(),
                          moment(dates[0].ytdenddate).toDate()
                        ],
                        ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastTwelveMonths]: [
                          moment(dates[0].lasttwelvemonthstartdate).toDate(),
                          moment(dates[0].lasttwelvemonthenddate).toDate()
                        ],
                        ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastYear]: [
                          moment(dates[0].lastyearstartdate).toDate(),
                          moment(dates[0].lastyearenddate).toDate()
                        ]
                      },
                      maxDate: moment(dates[0] && dates[0].today).toDate(),
                      alwaysShowCalendars: false,
                      applyClass: clsx(classes.calButton, 'apply-btn'),
                      cancelClass: clsx(classes.calButton, 'apply-btn'),

                      startDate:
                        filterStart && filterStart != ''
                          ? moment(filterStart).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(dates[0].mtdstartdate).toDate()
                          : moment(dates[0].lastmonthstartdate).toDate(),
                      endDate:
                        filterEnd && filterEnd != ''
                          ? moment(filterEnd).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(dates[0].mtdenddate).toDate()
                          : moment(dates[0].lastmonthenddate).toDate()
                      //showDropdowns: true
                    }}
                    onApply={handleCallback}
                  >
                    <input
                      type="text"
                      className="datepicker"
                      id="picker"
                      name="picker"
                      aria-labelledby="label-picker"
                    />
                  </DateRangePicker>
                  <label class="labelpicker" for="picker" id="label-picker">
                    <div class="textpicker">Select Date</div>
                  </label>
                </FormControl>
              </Grid>
            </Grid>
            <Divider />
            {isLoading == false || pageLoader == true ? (
              <div>
                <Box className={classes.boxClass}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    className={classes.boxClass}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : noData ? (
              <Grid justify="center" className={classes.loaderGrid}>
                No Data to Display
              </Grid>
            ) : (
              <div id={'kpi-report-2'} style={containerStyle}>
                <>
                  <Dashboard
                    LaborGpRo={LaborGpRo}
                    PartsGpRo={PartsGpRo}
                    TotalGpRo={TotalGpRo}
                    FlatRateHrs={FlatRateHrs}
                    allCWITData={CWITAllData}
                    ROShareData={ROShareData}
                    AvgAgeMiles={AvgAgeMiles}
                    LineROLtSixtyK={LineROLtSixtyK}
                    LineROGtSixtyK={LineROGtSixtyK}
                    WorkMix={WorkMix}
                    LineRO={LineRO}
                    LaborGrid={LaborGrid}
                    PartsGrid={PartsGrid}
                    parent={'Home'}
                  />
                </>
              </div>
            )}
          </Paper>
        </div>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </React.Fragment>
  );
};
export default Kpi;
