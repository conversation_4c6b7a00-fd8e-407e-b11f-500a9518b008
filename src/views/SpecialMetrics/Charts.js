import React, { useState, useEffect, useCallback } from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { makeStyles } from '@material-ui/core/styles';
import { useSelector } from 'react-redux';
import { Paper, Divider, Button } from '@material-ui/core';
import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import DashboardActions from 'src/components/DashboardActions';
import {
  getDataGridConfigurationSpecialMetrics,
  getLayoutConfiguration,
  saveLayoutConfiguration
} from '../../utils/Utils';
import { withStyles } from '@material-ui/styles';
import { withKeycloak } from '@react-keycloak/web';
import PageHeader from 'src/components/PageHeader';
import {
  getDataForSpecialMetricsCharts,
  getDataForSpecialMetricsChartsSummary
} from 'src/utils/hasuraServices';
import HasuraDashboardBarRenderer from 'src/components/charts/HasuraDashboardBarRenderer';
import ChartDialog from 'src/components/Dialog';
import clsx from 'clsx';
import 'src/styles.css';
import Grid from '@material-ui/core/Grid';
import $ from 'jquery';
import { chart } from 'highcharts';
const ReactGridLayout = WidthProvider(RGL);
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  dividerStyle: {
    marginLeft: 0,
    marginRight: 0
  },
  root: {
    flexGrow: 1
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  paperSub: {
    boxShadow: '0 3px 2px -2px #8080802b'
  },
  container: {
    alignItems: 'center',
    margin: '5px 0px'
  },
  tabs: {
    '& button': {
      padding: 5
    },
    "& button[aria-selected='true']": {
      color: theme.palette.primary.main,
      textTransform: 'none',
      border: 'solid 1px',
      borderColor: theme.palette.primary.main,
      backgroundColor: theme.palette.primary.active
    }
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  sublLabel: {
    display: 'flex'
  },
  resetButton: {
    float: 'right'
  },
  reportButton: {
    height: 24,
    marginLeft: 8,
    marginTop: 5,
    padding: '10px 20px',
    borderRadius: '0px',
    border: '2px solid #7d97aa',
    textTransform: 'none',
    color: '#757575',
    borderRadius: '4px',
    '@media (max-width: 1920px)': {
      fontSize: 12
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (min-width: 2304px)': {
      fontSize: 16
    }
  },
  rankedTableContainer: {
    margin: 'auto'
  },
  rankButtonGrid: {},
  reportTopBar: {
    padding: 8,
    display: 'flex',
    alignItems: 'center'
  }
}));
const Charts = props => {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const { keycloak, history, onLayoutChange } = props;
  const realm = keycloak.realm;
  const [filters, setFilters] = useState(2);
  const [laborChartsData, setLaborChartsData] = useState([]);
  const [orderedData, setOrderedData] = useState([]);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [selected, setSelected] = useState(history.location.selectedGrid);
  const [selectedChartId, setSelectedChartId] = useState(
    history.location.handleHighlight
  );

  const [layout, setLayout] = useState(
    JSON.parse(
      JSON.stringify(
        getLayoutConfiguration('layout', 'fixed-ops-layout-30') || {}
      )
    )
  );
  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  const [chartData, setChartData] = useState([]);
  const [reloadCounter, setReloadCounter] = useState(0);
  const [popupChartId, setPopupChartId] = useState('');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    localStorage.setItem('popup', false);
    if (history && history.location) {
      if (history.location.selectedGrid) {
        setTimeout(() => {
          handleHighlightContainer(history.location.handleHighlight);
        }, 100);
      }
    }
    let realm = keycloak.realm;
    let filteredResult = chartList.filter(
      item => item.dbdName === 'Special Metrics' && item.parentId == null
    );
    let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');

    setOrderedData(orderedData);
    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    setIsDataLoaded(false);
    setLaborChartsData([]);
    setChartData([]); // Reset chartData

    const processData = (callback, isSecondCall = false) => {
      const datasets = JSON.parse(callback[0].jsonData)[0].datasets;
      const labels = JSON.parse(callback[0].jsonData)[0].labels;

      // Group datasets by chartId
      const grouped = {};
      datasets.forEach(ds => {
        if (!grouped[ds.chartId]) grouped[ds.chartId] = [];
        grouped[ds.chartId].push({
          chartId: parseInt(ds.chartId, 10),
          data: ds.data,
          label: ds.label
        });
      });

      // Form the final array
      return Object.entries(grouped).map(([chartId, datasets]) => ({
        datasets,
        labels,
        chartId: parseInt(chartId, 10)
      }));
    };

    Promise.all([
      new Promise(resolve => {
        getDataForSpecialMetricsCharts(session.serviceAdvisor, 'All', resolve);
      }),
      new Promise(resolve => {
        getDataForSpecialMetricsChartsSummary(
          session.serviceAdvisor,
          'All',
          resolve
        );
      })
    ]).then(([firstCallback, secondCallback]) => {
      const firstDataset = processData(firstCallback);
      const secondDataset = processData(secondCallback);

      const combinedData = [...firstDataset, ...secondDataset];
      setChartData(combinedData);

      if (!session.serviceAdvisor.includes('All')) {
        setIsDataLoaded(true);
      }
    });
  }, [session.serviceAdvisor]);

  useEffect(() => {
    console.log('ccc==chartData', chartData);
  }, [chartData]);

  const setResetDashboard = value => {
    setReloadCounter(prev => prev + 1);
    if (value) {
      setLayout([]);
    }
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn) {
      $('#' + prevBtn).removeClass('button-selected');
    }
    if (selectedId) {
      $('#' + selectedId).removeClass('grid-selected');
    }
    setSelected('');
    setSelectedChartId('');
    return layout;
  };

  const handleLayoutChange = newLayout => {
    saveLayoutConfiguration('layout', newLayout, 'fixed-ops-layout-30');
    setLayout(newLayout);
    if (onLayoutChange) onLayoutChange(newLayout);
  };

  const removeFav = () => {};

  const handleClosePopup = () => {
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn) {
      let selectedChart = prevBtn.split('-')[1];
      $('#chartContainterId-' + selectedChart).addClass('grid-selected');
      setSelected(true);
      var element = document.getElementById(
        'chartContainterId-' + selectedChart
      );
      if (element) element.scrollIntoView({ block: 'center' });
    }
    setPopupChartId('');
    setOpen(false);
  };

  const redirectToHome = () => {
    history.push({
      pathname:
        localStorage.getItem('versionFlag') === 'TRUE'
          ? '/Home'
          : '/2.4.0/Home',
      state: {
        toggleOptions: history.location.state.selectedToggle,
        payType: history.location.state.payType,
        gridType: history.location.state.gridType,
        parent: history.location.state.parent
      }
    });
  };

  const gotoVisualization = chartId => {
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn) {
      $('#' + prevBtn).removeClass('button-selected');
    }
    if (selectedId) {
      $('#' + selectedId).removeClass('grid-selected');
    }
    var element = document.getElementById('chartContainterId-' + chartId);
    if (element) element.scrollIntoView({ block: 'center' });

    $('#chartContainterId-' + chartId).addClass('grid-selected');
    setSelected(true);
    setSelectedChartId(chartId);
    $('#chartid-' + chartId).addClass('button-selected');
  };

  const handleHighlightContainer = chartId => {
    $('#chartid-' + chartId).addClass('button-selected');
    $('#chartContainterId-' + chartId).addClass('grid-selected');
    var element = document.getElementById('chartContainterId-' + chartId);
    if (element)
      setTimeout(() => {
        element.scrollIntoView({ block: 'center' });
      }, 100);
  };

  const backTobutton = chartId => {
    let selectedId = $('.grid-selected').attr('id');
    var element = document.getElementById('chartContainterId-' + chartId);
    let prevBtn = $('.button-selected').attr('id');
    setSelected(false);
    setSelectedChartId('');
    if (
      typeof prevBtn !== 'undefined' &&
      prevBtn &&
      prevBtn.split('-')[1] === String(chartId)
    ) {
      if (prevBtn) {
        window.scrollTo(0, 0);
        $('#' + prevBtn).removeClass('button-selected');
      }
      if (selectedId) {
        $('#' + selectedId).removeClass('grid-selected');
      }
    } else {
      window.scrollTo(0, 0);
    }
  };

  const getChartname = str => {
    var newStr = str.replace('CP', '');
    var newString = 'Percentage';
    if (newStr.includes(newString)) {
      var newStr2 = newStr.replace('Percentage', '%');
      return newStr2;
    }
    return newStr;
  };

  const getHighlighedDiv = chartId => {
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn && prevBtn.split('-')[1] === String(chartId)) {
      if (prevBtn) {
        $('#' + prevBtn).removeClass('button-selected');
      }
      if (selectedId) {
        $('#' + selectedId).removeClass('grid-selected');
      }
      var element = document.getElementById('chartContainterId-' + chartId);
      if (element) element.scrollIntoView({ block: 'center' });

      $('#chartContainterId-' + chartId).addClass('grid-selected');
      setSelected(true);
      $('#chartid-' + chartId).addClass('button-selected');
    }
  };

  return (
    <div className={classes.root}>
      <Paper className={classes.paper}>
        <PageHeader
          title={'Special Metrics'}
          closedDate={null}
          setResetDashboard={setResetDashboard}
          redirectHome={redirectToHome}
          parent={
            history.location &&
            history.location.state &&
            history.location.state.parent
              ? history.location.state.parent
              : ''
          }
        />
        <Divider />
        <Grid item xs={9} xl={7} className={classes.rankedTableContainer}>
          <Paper className={classes.reportTopBar}>
            <div>
              <Grid container style={{ margin: 0 }} justifyContent="center">
                {orderedData.map((item, index) => (
                  <Button
                    className={classes.reportButton}
                    id={'chartid-' + item.chartId}
                    variant="outlined"
                    key={item.chartId}
                    onClick={() => gotoVisualization(item.chartId)}
                  >
                    {getChartname(item.chartName)}
                  </Button>
                ))}
              </Grid>
            </div>
          </Paper>
        </Grid>
        {console.log('cccc===', chartData)}
        <ReactGridLayout
          {...props}
          layout={layout}
          onLayoutChange={handleLayoutChange}
          isDraggable={false}
          isResizable={false}
          cols={12}
          rowHeight={30}
        >
          {orderedData.map((item, index) => (
            <div
              className={clsx('diagram-section')}
              id={'chartContainterId-' + item.chartId}
              key={index}
              data-grid={getDataGridConfigurationSpecialMetrics(index)}
            >
              <DashboardBarRenderer
                handleClosePopup={handleClosePopup}
                chartId={parseInt(item.chartId)}
                chartData={chartData.find(
                  chart => chart.chartId === parseInt(item.chartId)
                )}
                allData={chartData}
                isFrom={item.dbdName}
                removeFav={removeFav}
                tabSelection={null}
                backTobutton={backTobutton}
                handleHighlightContainer={handleHighlightContainer}
                selected={selected}
                selectedChartId={selectedChartId}
                headerClick={getHighlighedDiv}
                session={session}
                parentFrom="source_page"
                key={reloadCounter}
              />
              {/* <HasuraDashboardBarRenderer ... /> */}
            </div>
          ))}
          {/* <ChartDialog
            open={open}
            chartId={popupChartId}
            chartType="barChartRenderer"
            realm={realm}
            filter={filters}
            handlePopupClose={handleClosePopup}
            // headerClick={headerClick}
            // selected={selected}
            //selectedChartId={selectedChartId}
            //  handleHighlight={handleHighlight}
            chartData={chartData}
          /> */}
        </ReactGridLayout>
      </Paper>
    </div>
  );
};

export default withKeycloak(Charts);
