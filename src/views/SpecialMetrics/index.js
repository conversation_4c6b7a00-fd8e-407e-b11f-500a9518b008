import React, { useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import $ from 'jquery';
import { useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import { setNavItems, setMenuSelected } from 'src/actions';
import Charts from './Charts';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function SpecialMetrics() {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setNavItems(['Special Metrics']));
    dispatch(setMenuSelected('Special Metrics'));
  }, []);

  const selectedStoreId = localStorage.getItem('selectedStoreId');
  const storeIds = selectedStoreId ? JSON.parse(selectedStoreId) : [];

  return (
    <Page className={classes.root} title="Special Metrics">
      {storeIds.length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts history={history} />
      )}
    </Page>
  );
}

export default SpecialMetrics;
