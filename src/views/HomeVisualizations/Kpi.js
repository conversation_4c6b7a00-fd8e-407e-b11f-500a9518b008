import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect
} from 'react';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Divider,
  FormControl,
  Grid
} from '@material-ui/core';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { AgGridReact } from '@ag-grid-community/react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import moment from 'moment';
import './styles.css';
import PageHeader from 'src/components/PageHeader';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import {
  getHomeKpis,
  getDrillDownMonthYears,
  getKpiToggleOptionsWithTimeZone
} from 'src/utils/hasuraServices';
import CustomHeaderGroup from '../KPIReportComparative/CustomHeaderGroup';
import KPIHeaderGroup from '../KPIReportComparative/KPIHeaderGroup';
// import KPIColumnRenderer from './KPIColumnRenderer';
// import TableCellRenderer from './TableCellRenderer';
import { getTimeZone, getYearValue } from 'src/utils/Utils';
import { TRUE } from 'sass';
import Dashboard from '../Home/Dashboard';
import { i } from 'react-dom-factories';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'

    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  }
}));
const Kpi = () => {
  const gridRef = useRef();
  const classes = useStyles();
  const containerStyle = useMemo(() => ({ height: '100%', marginTop: 8 }), []);
  const gridStyle = useMemo(() => ({ height: '100%', width: '100%' }), []);
  const [visibleCol, setVisibleCol] = useState([]);
  const [rowData, setRowData] = useState([]);
  const [filterStart, setFilterStart] = useState();
  const [filterEnd, setFilterEnd] = useState();
  const [filterChanged, setFilterChanged] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [hiddenRowId, setHiddenRowId] = useState([]);
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const [dates, setDates] = useState([]);
  const [lastWeek, setLastWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [today, setToday] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');

  const [LaborGpRo, setLaborGpRo] = useState([]);
  const [PartsGpRo, setPartsGpRo] = useState([]);
  const [TotalGpRo, setTotalGpRo] = useState([]);
  const [CWITAllData, setCWITAllDataAll] = useState([]);
  const [AvgAgeMiles, setAvgAgeMiles] = useState([]);
  const [FlatRateHrs, setFlatRateHrs] = useState([]);
  const [ROShareData, setROShareData] = useState([]);
  const [LaborGrid, setLaborGrid] = useState([]);
  const [PartsGrid, setPartsGrid] = useState([]);
  const [LineROLtSixtyK, setLineROLtSixtyK] = useState([]);

  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  useEffect(() => {
    console.log('usess');
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);
        console.log('dataArr===', dataArr);

        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        setFilterStart(
          moment(dataArr[0].lastquarterstartdate).format('YYYY-MM-DD')
        );
        setFilterEnd(
          moment(dataArr[0].lastquarterenddate).format('YYYY-MM-DD')
        );
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
        setIsPageLoading(true);
      }
    });
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date())
          .clone()
          .format('YYYY-MM-DD');
        setSelectedDates([endDate, endDate]);
      }
    });
  }, []);
  useEffect(() => {
    if (typeof filterStart != 'undefined' && typeof filterEnd != 'undefined') {
      getHomeKpis(
        0,
        'kpi_scorecard',
        '+05:30',
        'YTD',
        'C',
        'Customer',
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetKpiScorecardDetailsHomepage
              .kpiScorecardDetailsHomepages
          ) {
            let data = JSON.parse(
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetailsHomepage
                .kpiScorecardDetailsHomepages[0].kpiScorecardJson
            );
            // setRowData(
            //   JSON.parse(
            //     result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
            //       .kpiScorecardDetails[0].jsonData
            //   )
            // );

            setRowData(data);
            // const groupedData = lodash.groupBy(data, 'kpi_no');
            const groupedData = lodash.groupBy(
              data,
              item => `${item.kpi_no}-${item.kpi_type_code}`
            );
            // Convert the grouped data to an array
            const groupedArray = Object.values(groupedData);
            // let result = lodash.groupBy(data, 'kpi_no');
            let orderedData;
            if (chartList) {
              let filteredResult = chartList.filter(
                item => item.dbdName == 'KPI' && item.parentId == null
              );
              orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
            }
            // setCWITData([]);
            setLineROLtSixtyK([]);
            // setLineROGtSixtyK([]);
            setAvgAgeMiles([]);
            setFlatRateHrs([]);
            setLaborGpRo([]);
            setTotalGpRo([]);
            // setWorkMix([]);
            setLaborGrid([]);
            // setLineRO([]);
            setPartsGrid([]);
            setROShareData([]);
            setCWITAllDataAll([]);
            setPartsGpRo([]);
            if (orderedData && orderedData.length > 0) {
              orderedData.map(item => {
                if (item.chartId == 1341) {
                  var filteredOutput = groupedArray[0].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  if (
                    filteredOutput[1].label == 'Labor Gross Profit' &&
                    filteredOutput[1].val != null
                  ) {
                    const newItem = {
                      label: 'Labor Gross Profit%',
                      val: filteredOutput[1].val.split('/')[1]
                    };
                    filteredOutput.push(newItem);
                    filteredOutput[1].val = filteredOutput[1].val.split('/')[0];

                    filteredOutput = [
                      filteredOutput[2],
                      filteredOutput[3],
                      filteredOutput[4],
                      filteredOutput[0],
                      filteredOutput[1]
                    ];
                  }
                  setIsLoading(true);
                  setLaborGpRo(filteredOutput);
                }
                if (item.chartId == 1342) {
                  var filteredOutput = groupedArray[1].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  if (
                    filteredOutput[1].label == 'Parts Gross Profit' &&
                    filteredOutput[1].val != null
                  ) {
                    const newItem = {
                      label: 'Parts Gross Profit%',
                      val: filteredOutput[1].val.split('/')[1]
                    };
                    filteredOutput.push(newItem);
                    filteredOutput[1].val = filteredOutput[1].val.split('/')[0];

                    filteredOutput = [
                      filteredOutput[2],
                      filteredOutput[3],
                      filteredOutput[4],
                      filteredOutput[0],
                      filteredOutput[1]
                    ];
                  }
                  setIsLoading(true);
                  setPartsGpRo(filteredOutput);
                }
                if (item.chartId == 1343) {
                  var filteredOutput = groupedArray[2].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  setIsLoading(true);
                  setTotalGpRo(filteredOutput);
                }
                if (item.chartId == 1335) {
                  var filteredOutput = groupedArray[7].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  setIsLoading(true);
                  setCWITAllDataAll(filteredOutput);
                }
                if (item.chartId == 1340) {
                  var filteredOutput = groupedArray[9].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  setIsLoading(true);
                  setAvgAgeMiles(filteredOutput);
                }
                //  if(item.chartId == 1339){
                //   var filteredOutput = groupedArray[9].map(item => ({
                //     label: item.kpi_name,
                //     val: item.kpi_value
                //   }));
                //   setIsLoading(true);

                //   setFlatRateHrs(filteredOutput);
                //  }
                if (item.chartId == 1336) {
                  var filteredOutput = groupedArray[8].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  setIsLoading(true);
                  setROShareData(filteredOutput);
                }
                if (item.chartId == 1346) {
                  var filteredOutput = groupedArray[3].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  setIsLoading(true);
                  if (filteredOutput[0].val == null) {
                    filteredOutput[0].label = 'Repair Grid Targets';
                    filteredOutput[0].val = 0;
                    filteredOutput.push(
                      {
                        label: 'Misses',
                        val: 0
                      },
                      {
                        label: '% of Non-Compliance',
                        val: 0
                      }
                    );
                  }
                  setLaborGrid(filteredOutput);
                }
                if (item.chartId == 1353) {
                  var filteredOutput = groupedArray[6].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  setIsLoading(true);
                  if (filteredOutput[0].val == null) {
                    filteredOutput[0].label = 'Parts Price Targets';
                    filteredOutput[0].val = 0;
                    filteredOutput.push(
                      {
                        label: 'Misses',
                        val: 0
                      },
                      {
                        label: '% of Non-Compliance',
                        val: 0
                      }
                    );
                  }

                  setPartsGrid(filteredOutput);
                }
                if (item.chartId == 1337) {
                  var filteredOutput = groupedArray[10].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  var filteredOutput1 = groupedArray[11].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  var filteredOutput2 = groupedArray[12].map(item => ({
                    label: item.kpi_name,
                    val: item.kpi_value
                  }));
                  filteredOutput[0].label = 'onelineRos';
                  filteredOutput[0].val = filteredOutput[1].val.split('/')[0];
                  filteredOutput[1].label = 'onelinePercentage';
                  filteredOutput[1].val = filteredOutput[1].val.split('/')[1];
                  filteredOutput.push(
                    {
                      label: 'onelineLbrsale',
                      val: filteredOutput1[0].val
                    },
                    {
                      label: 'onelinePrtsale',
                      val: filteredOutput1[1].val
                    },
                    {
                      label: 'onelineTotalsale',
                      val: filteredOutput1[2].val
                    },
                    {
                      label: 'multilineLbrsale',
                      val: filteredOutput2[0].val
                    },
                    {
                      label: 'multilinePrtsale',
                      val: filteredOutput2[1].val
                    },
                    {
                      label: 'multilineTotalsale',
                      val: filteredOutput2[2].val
                    },
                    {
                      label: 'multilineJobcount',
                      val: filteredOutput2[3].val
                    }
                  );

                  setIsLoading(true);
                  setLineROLtSixtyK(filteredOutput);
                }
              });
            }
          }
        }
      );
    }
  }, [filterStart, filterEnd]);

  const handleCallback = (event, picker) => {
    console.log('picker==', picker);
    setFilterStart(picker.startDate.format('YYYY-MM-DD'));
    setFilterEnd(picker.endDate.format('YYYY-MM-DD'));
    setFilterChanged(true);
  };
  const setColDef = val => {
    var col = visibleCol;
    col.push(val);

    setVisibleCol(col);
  };

  return (
    <React.Fragment>
      {/* <Kpi history={history} /> */}
      {isPageLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <Grid
              xs={12}
              className={clsx(classes.headerItem, 'main-title-kpi')}
            >
              <Grid xs={10}>
                <PageHeader
                  title={'KPI Report #2 - Advisor Comparative'}
                  // title={session.kpiAdvisor[0] != 'All' ? 'KPI Report 1 - Individual Advisor' : 'KPI Report 1 - All Advisors'}
                  hideTitle={false}
                  isFrom={'details'}
                  // exportPDF={true}
                  // exportKpiReportGrid={exportKpiReportGrid}
                  //setResetDashboard={this.setResetDashboard}
                />
              </Grid>
              {/* <div>
                    <Button
                      variant="contained"
                      className={'editGoals-Btn'}
                      onClick={handleclick}
                    >
                      <Typography variant="body1" align="left">
                        Edit Goals
                      </Typography>
                    </Button>
                  </div> */}
              <Grid xs={2}>
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={clsx(classes.formControl, 'input-container')}
                >
                  <DateRangePicker
                    initialSettings={{
                      // maxDate: {
                      //   date: new Date(),
                      // },
                      // minDate:{
                      //   date: (selectedDates[1])
                      // },

                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },
                      ranges: {
                        ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        today]: [moment().toDate(), moment().toDate()],
                        ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        yesterDay]: [
                          moment(dates[0] && dates[0].yesterday).toDate(),
                          moment(dates[0] && dates[0].yesterday).toDate()
                        ],
                        ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;' +
                        dayBfYest]: [
                          moment(
                            dates[0] && dates[0].dayBeforeYesterday
                          ).toDate(),
                          moment(
                            dates[0] && dates[0].dayBeforeYesterday
                          ).toDate()
                        ],
                        ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastWeek]: [
                          moment(dates[0].lastweekstartdate).toDate(),
                          moment(dates[0].lastweekenddate).toDate()
                        ],
                        // 'Last 7 Days': [
                        //   moment()
                        //     .subtract(6, 'days')
                        //     .toDate(),
                        //   moment().toDate()
                        // ],
                        // 'Last 30 Days': [
                        //   moment()
                        //     .subtract(29, 'days')
                        //     .toDate(),
                        //   moment().toDate()
                        // ],
                        ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        mtd]: [
                          moment(dates[0] && dates[0].mtdstartdate).toDate(),
                          moment(dates[0] && dates[0].mtdenddate).toDate()
                        ],
                        ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastMonth]: [
                          moment(
                            dates[0] && dates[0].lastmonthstartdate
                          ).toDate(),
                          moment(dates[0] && dates[0].lastmonthenddate).toDate()
                        ],
                        ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastThreeMonths]: [
                          moment(dates[0].lastthreemonthstartdate).toDate(),
                          moment(dates[0].lastthreemonthenddate).toDate()
                        ],
                        ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastQtr]: [
                          moment(dates[0].lastquarterstartdate).toDate(),
                          moment(dates[0].lastquarterenddate).toDate()
                        ],
                        ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        ytd]: [
                          moment(dates[0].ytdstartdate).toDate(),
                          moment(dates[0].ytdenddate).toDate()
                        ],
                        ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastTwelveMonths]: [
                          moment(dates[0].lasttwelvemonthstartdate).toDate(),
                          moment(dates[0].lasttwelvemonthenddate).toDate()
                        ],
                        ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastYear]: [
                          moment(dates[0].lastyearstartdate).toDate(),
                          moment(dates[0].lastyearenddate).toDate()
                        ]
                      },
                      maxDate: moment(dates[0] && dates[0].today).toDate(),
                      alwaysShowCalendars: false,
                      applyClass: clsx(classes.calButton, 'apply-btn'),
                      cancelClass: clsx(classes.calButton, 'apply-btn'),

                      startDate: moment(dates[0].mtdstartdate).toDate(),
                      endDate: moment(dates[0].mtdenddate).toDate()
                      //showDropdowns: true
                    }}
                    // onEvent={this.handleEventCallback}
                    onApply={handleCallback}
                  >
                    <input
                      type="text"
                      className="datepicker"
                      id="picker"
                      name="picker"
                      aria-labelledby="label-picker"
                    />
                    {/* <TextField
                      id="outlined-basic"
                      label="Select Date"
                      size="small"
                      //onChange={}
                      value={this.state.value}
                      variant="outlined"
                    /> */}
                  </DateRangePicker>
                  <label class="labelpicker" for="picker" id="label-picker">
                    <div class="textpicker">Select Date</div>
                  </label>
                </FormControl>
              </Grid>
            </Grid>
            <Divider />

            {isLoading == false ? (
              <div>
                <Box className={classes.boxClass}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    className={classes.boxClass}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : LaborGpRo.length > 0 ? (
              <div id={'kpi-report-2'} style={containerStyle}>
                <>
                  <Dashboard
                    // toggleOption={toggleOption}
                    LaborGpRo={LaborGpRo}
                    PartsGpRo={PartsGpRo}
                    TotalGpRo={TotalGpRo}
                    // FlatRateHrs={FlatRateHrs}
                    allCWITData={CWITAllData}
                    ROShareData={ROShareData}
                    AvgAgeMiles={AvgAgeMiles}
                    LineROLtSixtyK={LineROLtSixtyK}
                    // LineROGtSixtyK={LineROGtSixtyK}
                    // WorkMix={WorkMix}
                    // LineRO={LineRO}
                    LaborGrid={LaborGrid}
                    PartsGrid={PartsGrid}
                    // internalMisses={internalMisses}
                    parent={'Home'}
                  />
                </>
              </div>
            ) : null}
          </Paper>
        </div>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </React.Fragment>
  );
};
export default Kpi;
