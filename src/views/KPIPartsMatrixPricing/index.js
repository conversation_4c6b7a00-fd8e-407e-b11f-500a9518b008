import React, { useState, useEffect } from 'react';
import Page from 'src/components/Page';
import PartsMatrixPricing from './PartsMatrixPricing';
import { useHistory } from 'react-router';
import { getGridorMatrixPayTypeDetails } from 'src/utils/hasuraServices';
import { CircularProgress, Grid } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));

function KPIPartsMatrixPricing() {
  const classes = useStyles();
  const history = useHistory();
  const [payTypes, setPayType] = useState([]);
  const [selectedPayType, setSelectedPayType] = useState('');
  const [isLoading, setLoading] = useState(true);
  const [isStoreLoading, setStoreLoading] = useState(false);
  const session = useSelector(state => state.session);
  let storeId =
    history.location && history.location.state && history.location.state.storeId
      ? history.location.state.storeId
      : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  useEffect(() => {
    setStoreLoading(false);
    getGridorMatrixPayTypeDetails('paytype_matrix', storeId, result => {
      let data = result;
      if (
        result.includes('Heavy Duty') ||
        result.includes('Fleet') ||
        result.includes('Commercial')
      ) {
        if (
          result[0] == 'Heavy Duty' ||
          result[0] == 'Fleet' ||
          (result[0] == 'Commercial' && !result.includes('RV'))
        ) {
          data = result.reverse();
        }
      }
      if (
        result.includes('Commercial') &&
        result.includes('RV') &&
        result.includes('Retail')
      ) {
        data = ['Retail', 'Commercial', 'RV'];
      }
      if (
        result.includes('Warranty') &&
        result.includes('Standard') &&
        result.includes('Fleet')
      ) {
        data = ['Standard', 'Warranty', 'Fleet'];
      }
      if (result.includes('Internal')) {
        data = result.sort();
      }
      if (result.includes('Highline')) {
        data = result.reverse();
      }
      if (result.includes('Diesel')) {
        data = result.reverse();
      }
      setPayType(data);
      setSelectedPayType(data[0]);
      setLoading(false);
      setStoreLoading(true);
    });
  }, [selectedPayType, session.storeSelected]);

  return (
    <Page title="Parts Matrix(s)">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      ((history &&
        history.location.state &&
        history.location.state.pageType != 'PartsMisses') ||
        history.location.state == undefined) ? (
        <Redirect to="/errors/error-404" />
      ) : isLoading == true && selectedPayType == '' ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : !isStoreLoading ? null : (
        <PartsMatrixPricing
          history={history}
          payTypes={payTypes}
          selectedPayType={selectedPayType}
          session={session}
        />
      )}
    </Page>
  );
}

export default KPIPartsMatrixPricing;
