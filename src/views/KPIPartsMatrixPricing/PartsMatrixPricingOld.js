import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tooltip,
  Link,
  FormControl,
  RadioGroup,
  MenuItem,
  Select,
  InputLabel,
  Divider
} from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getGridorMatrixDetails,
  getMatrixPartSource,
  getGridorMatrixPeriods
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import moment from 'moment';
import { ReactSession } from 'react-client-session';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class PartsMatrixPricingOld extends React.Component {
  componentWillMount() {
    console.log('enter=1');
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) { console.log("enter=123");
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) { console.log("enter=1234");
    //     this.setState({ serviceAdvisors: ReactSession.get("serviceAdvisors") });
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      console.log('enter=12345');
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        console.log('enter=123456');
        this.setState({ isLoading: true });
        this.setState({ storeChanged: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });

        if (this.props.history.location.state) {
          this.props.history.location.state.partsSource = this.state.partSource[0];
          this.props.history.location.state.pageType = '';
        }
        this.setState({ selectedSource: this.state.partSource[0] });
        // this.getAgGridData();
        this.getMatrixDetails();
      }
    }
  }
  componentDidMount() {
    getMatrixPartSource('part_source', this.state.selectedPayType, result => {
      if (result) {
        if (!result.includes(this.state.selectedSource)) {
          this.setState({
            selectedSource: result.sort(function(a, b) {
              return a - b;
            })[0]
          });
          this.getMatrixTypes(
            result.sort(function(a, b) {
              return a - b;
            })[0]
          );
        }
        this.setState({
          partSource: result.sort(function(a, b) {
            return a - b;
          })
        });
        this.getMatrixTypes(this.state.selectedSource);
        var prtSource = result.sort(function(a, b) {
          return a - b;
        });
        if (prtSource) {
          if (prtSource[0]) {
            let partSource = prtSource[0];
            partSource = partSource.slice(1, -1);
            if (partSource) {
              var partSourceData = [];
              console.log('partSource==', partSource);
              partSource.split(',').map(item => partSourceData.push(item));
              this.setState({ partSourceData });
              console.log('arr=====', partSourceData);
            }
          }
        }
      }
    });
  }

  getMatrixTypes(selectedSource) {
    getGridorMatrixPeriods(
      'matrix',
      this.state.selectedPayType,
      selectedSource,
      result => {
        if (result) {
          let resultArr = lodash.uniq(result);

          this.setState({
            gridPeriods: resultArr.sort(function(a, b) {
              return a - b;
            })
          });
        }
        this.getMatrixDetails();
      }
    );
  }
  constructor(props) {
    super(props);

    let payTypeSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.payType
        ? this.props.history.location.state.payType
        : this.props.selectedPayType;
    if (payTypeSelected == 'C') {
      payTypeSelected = 'Customer';
    } else if (payTypeSelected == 'I') {
      payTypeSelected = 'Internal';
    }
    let matrixDate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.matrixDate
        ? this.props.history.location.state.matrixDate
        : '';
    let selectedGrid =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      (this.props.history.location.state.selectedGrid ||
        this.props.history.location.state.selectedGrid == '')
        ? this.props.history.location.state.selectedGrid
        : 1;
    let showAllJobs =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : false;
    let selectedSource =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.partsSource != ''
        ? this.props.history.location.state.partsSource
        : '';
    this.state = {
      partSourceData: [],
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      selectedGrid: selectedGrid,
      partSource: [],
      selectedSource: selectedSource != '' ? selectedSource : '',
      selectedPayType: payTypeSelected,
      payTypes: this.props.payTypes,
      gridPeriods: [],
      matrixDate: matrixDate,
      showAllJobs: showAllJobs,
      storeChanged: false,
      columnDefs: [
        {
          headerName: 'From',
          field: 'col0OrpriceStartRange',
          width: 110,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },

        {
          headerName: 'To',
          field: 'col1OrpriceEndRange',
          width: 110,
          valueFormatter: this.formatCellValueRate,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Percentage',
          field: 'col2OraddPercentage',
          valueFormatter: this.formatCellValue,
          cellClass: 'addPercentage',
          width: 110,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      overlayNoRowsTemplate:
        '<span style="padding: 10px; font-size: 12.5px;">No data found for the selected Matrix Period , Matrix Type and Parts Source combination</span>',
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          dataType: 'DateTime',
          numberFormat: {
            format: 'mm/dd/yy'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  formatCellValueRate = params => {
    if (params.value && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };

  formatCellValue = params => {
    if (params.value && params.value != '') {
      return params.value + '%';
    } else {
      return '';
    }
  };

  resetRawData = () => {
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
    window.sortState = {};
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };

  onBtExport = () => {
    var params = {
      sheetName: 'Parts Matrix(s)',
      fileName: 'Parts Matrix(s)',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Parts Matrix(s)' },
            mergeAcross: 3
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  onGridReady = params => {
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridColumnApi: params.columnApi });
    //  this.getAgGridData();
  };

  getMatrixDetails() {
    this.setState({ isLoading: true });
    let resultArr = [];
    let dataArr = [];
    let partSource = this.state.selectedSource
      ? this.state.selectedSource
      : this.state.partSource
      ? this.state.partSource[0]
      : '';
    let dataArrDoorRate = [];
    getGridorMatrixDetails(
      'matrix',
      this.state.selectedPayType,
      partSource,
      result => {
        this.setState({ isLoading: false });
        if (
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        ) {
          resultArr =
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes;
          if (this.state.matrixDate != '') {
            dataArrDoorRate = resultArr.filter(
              item => item.createdDate == this.state.matrixDate
            );
            if (
              dataArrDoorRate.length > 0 &&
              this.state.selectedGrid == '' &&
              this.state.selectedSource == ''
            ) {
              const unique = [
                ...new Set(dataArrDoorRate.map(item => item.gridOrder))
              ];
              var min = Math.min(...unique);
              this.setState({ selectedGrid: min });
              var minSource = Math.min(...this.state.partSource);
              this.setState({ selectedSource: minSource });
              dataArr = dataArrDoorRate.filter(
                item => item.gridOrder == min && item.partSource == minSource
              );
            } else if (resultArr.length > 0 && this.state.selectedGrid) {
              if (this.state.selectedSource == '') {
                var minSource = Math.min(...this.state.partSource);
                this.setState({ selectedSource: minSource });
              }
              partSource =
                this.state.selectedSource != ''
                  ? this.state.selectedSource
                  : minSource;
              if (this.state.gridPeriods.includes(this.state.selectedGrid)) {
                dataArr = resultArr.filter(
                  item =>
                    item.gridOrder == this.state.selectedGrid &&
                    item.partSource == partSource
                );
              } else {
                this.setState({ selectedGrid: this.state.gridPeriods[0] });
                dataArr = resultArr.filter(
                  item =>
                    item.gridOrder == this.state.gridPeriods[0] &&
                    item.partSource == partSource
                );
              }
              // dataArr = resultArr.filter(
              //   item =>
              //     item.gridOrder == this.state.selectedGrid &&
              //     item.partSource == partSource
              // );
            } else if (
              dataArrDoorRate.length > 0 &&
              this.state.selectedGrid == '' &&
              this.state.selectedSource != ''
            ) {
              const unique = [
                ...new Set(dataArrDoorRate.map(item => item.gridOrder))
              ];
              var min = Math.min(...unique);
              this.setState({ selectedGrid: min });
              dataArr = dataArrDoorRate.filter(
                item =>
                  item.gridOrder == min &&
                  item.partSource == this.state.selectedSource
              );
            }
          } else {
            if (this.state.selectedGrid) {
              if (this.state.gridPeriods.includes(this.state.selectedGrid)) {
                dataArr = resultArr.filter(
                  item =>
                    item.gridOrder == this.state.selectedGrid &&
                    item.partSource == partSource
                );
              } else {
                this.setState({ selectedGrid: this.state.gridPeriods[0] });
                dataArr = resultArr.filter(
                  item =>
                    item.gridOrder == this.state.gridPeriods[0] &&
                    item.partSource == partSource
                );
              }
            }
          }
          this.setState({
            rowData: dataArr
          });
          this.getMatrixPartSources();
        }
      }
    );
  }

  handleclick = params => {
    this.props.history.push({
      pathname: '/PartsMisses',
      state: {
        selectedFilter: this.props.history.location.state.selectedFilter,
        selectedToggle: this.props.history.location.state.selectedToggle,
        selectedMonthYear: this.props.history.location.state.selectedMonthYear,
        parent: this.props.history.location.state.parent,
        previousToggle: this.props.history.location.state.previousToggle,
        payType: this.props.history.location.state.payType,
        previousPayType: this.props.history.location.state.PrevPayType,
        previousGridType: this.props.history.location.state.PrevGridType,
        showAllJobs: this.state.showAllJobs
      }
    });
  };

  handleChange = event => {
    this.setState({ selectedGrid: event.target.value }, function() {
      // this.getAgGridData();
      this.getMatrixDetails();
    });
  };

  handleChangeSource = event => {
    this.setState({ selectedSource: event.target.value }, function() {
      this.getMatrixTypes(event.target.value);
      // this.getMatrixDetails();
    });
  };

  handleChangePayType = event => {
    this.setState({ selectedPayType: event.target.value }, function() {
      getGridorMatrixPeriods('matrix', this.state.selectedPayType, result => {
        if (result) {
          let resultArr = lodash.uniq(result);
          let resultArrSorted = resultArr.sort(function(a, b) {
            return a - b;
          });
          this.setState({
            gridPeriods: resultArrSorted,
            selectedGrid: resultArrSorted.includes(3)
              ? this.state.selectedGrid
              : resultArrSorted[0]
          });
        }
      });
      // this.getAgGridData();
      this.getMatrixDetails();
    });
  };

  render() {
    const { classes } = this.props;
    var matrixDated = '';
    var storeDate = '';
    if (this.state.rowData.length > 0) {
      matrixDated = moment(this.state.rowData[0].createdDate).format(
        'MM/DD/YY'
      );
      storeDate = moment(this.state.rowData[0].storeInstallDate).format(
        'MM/DD/YY'
      );
    }
    return (
      <div>
        <Page title={'Parts Matrix(s)'}></Page>
        <Paper
          square
          style={{
            margin: 8,

            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
            cursor: 'default'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            TabIndicatorProps={{
              style: {
                backgroundColor: '#e7eef3'
              }
            }}
            id={'lineTab'}
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
            style={{ cursor: 'default' }}
          >
            {this.props.history &&
              this.props.history.location.state &&
              this.props.history.location.state.pageType == 'PartsMisses' && (
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={this.handleclick}
                  style={{ width: 'auto', top: '9px' }}
                  fullWidth={false}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              )}

            <Tab
              label={<div style={{ color: '#084588' }}> Parts Matrix(s)</div>}
              value="one"
              style={{
                textTransform: 'none',
                pointerEvents: 'none',
                borderColor: '#e7eef3'
              }}
            />

            <Button
              variant="contained"
              className={clsx(classes.back, 'reset-btn')}
              id="reset-layout"
              onClick={this.resetRawData}
              // style={{ width: '10%'  }}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>

        <Paper
          square
          style={{
            margin: 8,
            marginLeft: 8,
            height: 50,
            paddingTop: 4,
            paddingLeft: 4
          }}
        >
          {this.state.partSource.length > 0 ? (
            <div>
              <FormControl
                margin="dense"
                variant="outlined"
                style={{
                  minWidth: 120,
                  marginLeft: 10,
                  paddingRight: 10
                }}
              >
                <InputLabel
                  htmlFor="outlined-age-native-simple"
                  margin="dense"
                  style={{ marginTop: -4 }}
                >
                  Matrix Period
                </InputLabel>
                <Select
                  margin="dense"
                  variant="outlined"
                  label="Filter By"
                  name="duration"
                  className={'laborPartsGrid'}
                  value={this.state.selectedGrid}
                  onChange={this.handleChange}
                >
                  {this.state.gridPeriods.length > 0 ? (
                    this.state.gridPeriods.map(item => (
                      <MenuItem value={item}>
                        {item == 1 ? 'Current' : 'Prior ' + Number(item - 1)}
                      </MenuItem>
                    ))
                  ) : (
                    <MenuItem value={1}>Current</MenuItem>
                  )}
                </Select>
              </FormControl>
            </div>
          ) : null}
          <div className="allItemsPricing">
            {this.state.partSource.length > 0 ? (
              <div className="payTypeListPartsMatrix">
                <FormControl
                  margin="dense"
                  variant="outlined"
                  style={{
                    minWidth: 120,
                    marginLeft: 10,
                    paddingRight: 10
                  }}
                >
                  <InputLabel
                    htmlFor="outlined-age-native-simple"
                    margin="dense"
                    style={{ marginTop: -4 }}
                  >
                    Matrix Type
                  </InputLabel>
                  <Select
                    margin="dense"
                    variant="outlined"
                    label="Filter By"
                    name="duration"
                    className={'laborPartsGrid'}
                    value={this.state.selectedPayType}
                    onChange={this.handleChangePayType}
                  >
                    {this.state.payTypes.map(item => (
                      <MenuItem value={item}>{item}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
                <FormControl
                  margin="dense"
                  variant="outlined"
                  style={{
                    minWidth: 120,
                    marginLeft: 10,
                    paddingRight: 10,
                    maxWidth: 250
                  }}
                >
                  <InputLabel
                    htmlFor="outlined-age-native-simple"
                    margin="dense"
                    style={{ marginTop: -4 }}
                  >
                    Part Source
                  </InputLabel>
                  <Select
                    margin="dense"
                    variant="outlined"
                    label="Filter By"
                    name="duration"
                    className={'laborPartsGrid'}
                    value={
                      this.state.selectedSource
                        ? this.state.selectedSource
                        : this.state.partSource.length > 0
                        ? this.state.partSource[0]
                        : ''
                    }
                    onChange={this.handleChangeSource}
                  >
                    {this.state.partSource.map(item => (
                      <MenuItem value={item}>{item}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </div>
            ) : (
              ''
            )}
            <div className={clsx(classes.dataAsOf)}>
              {storeDate ? (
                <Typography
                  variant="h6"
                  align="right"
                  style={{ fontSize: 12, color: '#7987a1', fontWeight: 'bold' }}
                >
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between'
                      }}
                    >
                      <div>{'Store Install Date' + '\xa0\xa0'}</div>
                      <div style={{ width: 62 }}>{':'}</div>
                      <div className={classes.dataAsOfValue}>{storeDate}</div>
                    </div>
                  </div>
                </Typography>
              ) : null}
              {matrixDated ? (
                <Typography
                  variant="h6"
                  align="right"
                  style={{ fontSize: 12, color: '#7987a1', fontWeight: 'bold' }}
                >
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <div>{'FOPC Calculated Date From' + '\xa0\xa0' + ' :'}</div>
                    <div className={classes.dataAsOfValue}>{matrixDated}</div>
                    <div>
                      {
                        '(This may be different from store install date if no prior pricing provided).'
                      }
                    </div>
                  </div>
                </Typography>
              ) : (
                ''
              )}
            </div>
          </div>
        </Paper>
        {/* <Divider /> */}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <span
          style={{
            display: 'flex'
          }}
        >
          <div
            id="data-tab-parts-pricing"
            className="ag-theme-balham"
            style={{
              // height: '410px',
              // height: window.innerHeight - 170 + 'px',
              height: window.innerHeight - 303 + 'px',
              width: '442px',
              // height:(window.innerHeight-215)+'px',
              alignContent: 'center',
              marginLeft: '8px',
              display: this.state.isLoading == true ? 'none' : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '350px',
                width: '100%'
              }}
              enableRangeSelection={false}
              animateRows={true}
              enableCharts={true}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              excelStyles={this.state.excelStyles}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader={true}
              rowData={this.state.rowData}
              sortingOrder={this.sortingOrder}
              tooltipShowDelay={0}
              floatingFilter={true}
              suppressRowClickSelection={true}
              headerHeight={this.state.headerHeight}
              onFilterChanged={this.onFilterChanged}
              suppressContextMenu={true}
              overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
              suppressDragLeaveHidesColumns={true}
            />
          </div>
          {/* <Grid container spacing={12}>
            <Grid item xs={12} style={{ padding: '4px' }}>
              <Paper square justify="center">
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={classes.formControl}
                >
                  <Typography
                    style={{
                      fontWeight: 'bold',
                      color: '#003d6b',
                      fontSize: 13,
                      height: 'auto'
                      // marginTop: 4
                    }}
                  >
                    <span
                      style={{
                        paddingRight: 10,
                        height: 'auto',
                        wordBreak: 'break-word'
                      }}
                    >
                      {'Included Part Sources  :'}{' '}
                      {this.state.partSourceData.map((item, index) => (
                        <>
                          {item}{' '}
                          {this.state.partSourceData.length == index + 1
                            ? ''
                            : ','}
                        </>
                      ))}
                    </span>
                  </Typography>
                </FormControl>
              </Paper>
            </Grid>
          </Grid> */}

          {/* <Grid
            item
            xs={12}
            className={classes.rankedTableContainerSource}
            style={{
              display: this.state.isLoading == true ? 'none' : 'block'
            }}
          >
            <Paper className={classes.reportTopBar} style={{ width: '99%' }}>
              <div style={{ width: '100%' }}>
                <div>
                  <Grid
                    container
                    style={{
                      margin: 0,
                      width: '100%'
                    }}
                  >
                    <Typography
                      style={{
                        display:
                          this.state.isLoading == true ? 'none' : 'block',
                        fontWeight: 'bold',
                        color: '#003d6b',
                        fontSize: 13,
                        width: '100%',
                        display: 'flex'
                        // justifyContent: 'left',
                        // alignItems: 'center'
                      }}
                    >
                      <span style={{ paddingRight: 10 }}>
                        {' '}
                        {'Included Part Sources  :'}
                      </span>
                      {this.state.partSourceData.map((item, index) => (
                        <>
                          <span style={{ paddingRight: 10 }}>
                            {item}
                            {this.state.partSourceData.length == index + 1
                              ? ''
                              : ','}
                          </span>
                        </>
                      ))}
                    </Typography>
                  </Grid>
                </div>
              </div>
            </Paper>
          </Grid> */}
        </span>
      </div>
    );
  }
}

const styles = theme => ({
  rankedTableContainerSource: {
    // display: 'flex',
    // alignItems: 'center',
    marginLeft: 20,
    marginTop: 5,
    maxWidth: '100%'
  },
  rankedTableContainer: {
    // display: 'flex',
    // alignItems: 'center',
    marginLeft: 20,
    marginTop: 5,
    maxWidth: '20%'
  },
  reportTopBar: {
    display: 'contents',
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'space-around',

    boxShadow: '0 0 0 1px rgba(63,63,68,0.05), 0 1px 3px 0 rgba(63,63,68,0.15)'
  },
  reportTopBar: {
    //margin: 8,
    padding: 8,
    width: '100%',
    justifyContent: 'center',
    display: 'flex',
    alignItems: 'center'
  },
  reportButton: {
    height: 24,
    marginLeft: 5,
    color: '#757575',
    borderRadius: '4px',
    padding: '10px 20px',
    marginTop: '5px',
    border: '2px solid #7d97aa',
    textTransform: 'none',
    '@media (max-width: 1920px)': {
      fontSize: 12
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (min-width: 2304px)': {
      fontSize: 16
    }
  },
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    marginTop: 7,
    width: 'auto'
  },
  dataAsOf: {
    marginRight: 7,
    float: 'right',
    marginTop: 11,
    marginLeft: '1%'
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3,
    marginRight: 8
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  }
});
export default withStyles(styles)(PartsMatrixPricingOld);
