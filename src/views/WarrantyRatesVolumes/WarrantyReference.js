import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Button,
  withStyles
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import {
  UPDATE_WARRANTY_REFERENCE_LABOR,
  UPDATE_WARRANTY_REFERENCE_PARTS
} from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import {
  getWarrantyReferenceLabor,
  getWarrantyReferenceParts
} from 'src/utils/hasuraServices';
import moment from 'moment';
import Page from 'src/components/Page';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import { Redirect } from 'react-router-dom';
import { ReactSession } from 'react-client-session';
import { traceSpan } from 'src/utils/OTTTracing';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class WarrantyReference extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        this.getAgGridData(
          this.state.queryMonth,
          this.state.serviceAdvisor,
          this.props.type
        );
        // this.getAgGridData(this.state.selectedToggle, ReactSession.get("serviceAdvisors"));
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    let startEdit = this;
    let drillDownType = '';
    if (props.location.pathname == '/WarrantyReferenceLabor') {
      drillDownType = 'labor';
    } else {
      drillDownType = 'parts';
    }

    this.state = {
      rowData: [],
      rowDataOld: [],
      showCharts: false,
      selectValue: 'other',
      selectValueFor: this.props.category,
      selectValueType: this.props.reportType,
      ddLabel: '< 2',
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      isEdited: false,
      isEditData: false,
      drillDownType: drillDownType,
      dbdId: [],
      viewdata: [],
      success: false,
      columnDefs: [
        {
          headerName: 'Min Date',
          chartDataType: 'series',
          width: 150,
          minWidth: 150,
          field: 'minDate',
          hide: false,
          suppressMenu: true,
          flex: 1,
          unSortIcon: true,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          editable: false,
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white'
            };
          }
          // typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          // this.props.keycloak.realmAccess.roles.length == 1 &&
          // this.props.keycloak.realmAccess.roles.includes('client') === true
          //   ? false
          //   : true
        },
        {
          headerName: 'Max Date',
          chartDataType: 'series',
          width: 150,
          minWidth: 150,
          field: 'maxDate',
          suppressMenu: true,
          flex: 1,
          unSortIcon: true,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          editable: false,
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white'
            };
          }
          // typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          // this.props.keycloak.realmAccess.roles.length == 1 &&
          // this.props.keycloak.realmAccess.roles.includes('client') === true
          //   ? false
          //   : true
        },

        {
          headerName: 'Min Warranty Rate',
          chartDataType: 'series',
          width: 110,
          field: 'minWarrantyRate',
          flex: 1,
          // tooltipField: 'minWarrantyRate',
          minWidth: 110,
          resizable: true,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          // typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          // this.props.keycloak.realmAccess.roles.length >= 1 &&
          // this.props.keycloak.realmAccess.roles.includes('client') === true
          //   ? false
          //   : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          }
        },
        {
          headerName: 'Max Warranty Rate',
          chartDataType: 'series',
          width: 150,
          minWidth: 150,
          field: 'maxWarranrtyRate',
          flex: 1,
          editable: false,
          resizable: true,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          // typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          // this.props.keycloak.realmAccess.roles.length == 1 &&
          // this.props.keycloak.realmAccess.roles.includes('client') === true
          //   ? false
          //   : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          }

          //   cellEditorParams: function(params) {
          //     return {
          //       values: startEdit.getDbdNames()
          //     };
          //   }
        },
        {
          headerName: 'Warranty Rate',
          chartDataType: 'series',
          width: 150,
          minWidth: 150,
          field: 'warrantyRate',
          flex: 1,
          hide: false,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          cellStyle: this.cellStyles
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'series',
          width: 210,
          minWidth: 210,
          field: 'paytype',
          hide: false,
          editable: false,
          suppressMenu: true,
          flex: 1,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        }

        // {
        //   headerName: 'Action',
        //   cellRenderer: 'buttonRenderer',
        //   filter: false,
        //   width: 200,
        //   sortable: false,
        //   suppressMenu: true,
        //
        //   // tooltip: function(params) {
        //   //   return 'Edit';
        //   // },
        //   cellStyle() {
        //     return { textAlign: 'center', border: ' 0px white' };
        //   },
        //   hide:
        //     typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
        //     this.props.keycloak.realmAccess.roles.length >= 1 &&
        //     this.props.keycloak.realmAccess.roles.includes('client') === true
        //       ? true
        //       : false,
        //   editable: false,
        //   cellRenderer: function(params) {
        //     var index = params.rowIndex;
        //     var eDiv = document.createElement('div');
        //     eDiv.innerHTML =
        //       '<button title="Edit" id="btnedit' +
        //       index +
        //       '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel' +
        //       index +
        //       '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate' +
        //       index +
        //       '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>';
        //     if (index != undefined) {
        //       var eButton = eDiv.querySelectorAll('.edit-button')[0];
        //       var uButton = eDiv.querySelectorAll('.update-button')[0];
        //       var cButton = eDiv.querySelectorAll('.cancel-button')[0];

        //       eButton.addEventListener('click', () => {
        //         localStorage.setItem('warrantyId', params.data.id);
        //         console.log('data=', params.data);
        //         localStorage.setItem('maxDate', params.data.maxDate);
        //         localStorage.setItem('minDate', params.data.minDate);
        //         localStorage.setItem(
        //           'maxWarrantyRate',
        //           params.data.maxWarranrtyRate
        //         );
        //         localStorage.setItem(
        //           'minWarrantyRate',
        //           params.data.minWarrantyRate
        //         );
        //         startEdit.onBtStartEditing(index);
        //         $('#btnedit' + index).hide();
        //         $('#btncancel' + index).show();
        //         $('#btnupdate' + index).show();
        //       });

        //       uButton.addEventListener('click', () => {
        //         startEdit.onBtStopEditing(index);
        //         var minDate = params.data.minDate;
        //         var maxDate = params.data.maxDate;
        //         var minWarrantyRate = params.data.minWarrantyRate;
        //         var maxWarranrtyRate = params.data.maxWarranrtyRate;
        //         var payType = params.data.paytype;
        //         console.log('minWarrantyRate=', params.data);
        //         startEdit.setState({ isEditData: true });
        //         // startEdit.updateWarrantyReferenceLabor(
        //         //   minDate,
        //         //   maxDate,
        //         //   minWarrantyRate,
        //         //   maxWarranrtyRate,
        //         //   payType
        //         // );

        //         $('#btnedit' + index).show();
        //         $('#btncancel' + index).hide();
        //         $('#btnupdate' + index).hide();
        //       });
        //       cButton.addEventListener('click', function() {
        //         startEdit.onBtStopEditing(index);
        //         $('#btnedit' + index).show();
        //         $('#btncancel' + index).hide();
        //         $('#btnupdate' + index).hide();
        //       });
        //     }
        //     return eDiv;
        //   }
        // }
      ],
      columnDefsParts: [
        {
          headerName: 'Min Date',
          chartDataType: 'series',
          width: 128,
          minWidth: 128,
          field: 'minDate',
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          editable: false,
          hide: false,
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white'
            };
          }
          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.length == 1 &&
          //   this.props.keycloak.realmAccess.roles.includes('client') === true
          //     ? false
          //     : true
        },
        {
          headerName: 'Max Date',
          chartDataType: 'series',
          width: 129,
          minWidth: 129,
          field: 'maxDate',
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white',
              marginLeft: '-10px'
            };
          },
          editable: false
          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.length == 1 &&
          //   this.props.keycloak.realmAccess.roles.includes('client') === true
          //     ? false
          //     : true
        },

        {
          headerName: 'Min Markup',
          chartDataType: 'series',
          width: 120,
          field: 'minMarkup',
          // tooltipField: 'minMarkup',
          minWidth: 120,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.length >= 1 &&
          //   this.props.keycloak.realmAccess.roles.includes('client') === true
          //     ? false
          //     : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            valueFormatter: this.formatCellValueMarkup
          }
        },
        {
          headerName: 'Max Markup',
          chartDataType: 'series',
          width: 157,
          minWidth: 157,
          field: 'maxMarkup',
          editable: false,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,
          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.length == 1 &&
          //   this.props.keycloak.realmAccess.roles.includes('client') === true
          //     ? false
          //     : true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            valueFormatter: this.formatCellValueMarkup
          }
          //   cellEditorParams: function(params) {
          //     return {
          //       values: startEdit.getDbdNames()
          //     };
          //   }
        },
        {
          headerName: 'Warranty Markup',
          chartDataType: 'series',
          width: 171,
          minWidth: 171,
          field: 'warrantyMarkup',
          hide: false,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValueMarkup,
          filterParams: {
            valueFormatter: this.formatCellValueMarkup
          }
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'series',
          width: 225,
          minWidth: 225,
          field: 'paytype',
          hide: false,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        }

        // {
        //   headerName: 'Action',
        //   cellRenderer: 'buttonRenderer',
        //   width: 143,
        //   sortable: false,
        //   suppressMenu: true,
        //   filter: false,
        //   // tooltip: function(params) {
        //   //   return 'Edit';
        //   // },
        //   cellStyle() {
        //     return { textAlign: 'center', border: ' 0px white' };
        //   },
        //   hide:
        //     typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
        //     this.props.keycloak.realmAccess.roles.length >= 1 &&
        //     this.props.keycloak.realmAccess.roles.includes('client') === true
        //       ? true
        //       : false,
        //   editable: false,
        //   cellRenderer: function(params) {
        //     var index = params.rowIndex;
        //     var eDiv = document.createElement('div');
        //     eDiv.innerHTML =
        //       '<button title="Edit" id="btnedit' +
        //       index +
        //       '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel' +
        //       index +
        //       '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate' +
        //       index +
        //       '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>';
        //     if (index != undefined) {
        //       var eButton = eDiv.querySelectorAll('.edit-button')[0];
        //       var uButton = eDiv.querySelectorAll('.update-button')[0];
        //       var cButton = eDiv.querySelectorAll('.cancel-button')[0];

        //       eButton.addEventListener('click', () => {
        //         console.log('warranty=', params.data);
        //         localStorage.setItem('warrantyId', params.data.id);
        //         localStorage.setItem('maxDate3', params.data.maxDate);
        //         localStorage.setItem('minDate3', params.data.minDate);
        //         localStorage.setItem('minMarkup3', params.data.minMarkup);
        //         localStorage.setItem('maxMarkup3', params.data.maxMarkup);
        //         startEdit.onBtStartEditing(index);
        //         $('#btnedit' + index).hide();
        //         $('#btncancel' + index).show();
        //         $('#btnupdate' + index).show();
        //       });

        //       uButton.addEventListener('click', () => {
        //         startEdit.onBtStopEditing(index);
        //         console.log('params.data', params.data);
        //         var minDate = params.data.minDate;
        //         var maxDate = params.data.maxDate;
        //         var minmarkup = params.data.minMarkup;
        //         var maxmarkup = params.data.maxMarkup;
        //         var payType = params.data.paytype;
        //         // startEdit.updateWarrantyReferenceParts(
        //         //   minDate,
        //         //   maxDate,
        //         //   minmarkup,
        //         //   maxmarkup,
        //         //   payType
        //         // );
        //         startEdit.setState({ isEditData: true });
        //         $('#btnedit' + index).show();
        //         $('#btncancel' + index).hide();
        //         $('#btnupdate' + index).hide();
        //       });
        //       cButton.addEventListener('click', function() {
        //         startEdit.onBtStopEditing(index);
        //         $('#btnedit' + index).show();
        //         $('#btncancel' + index).hide();
        //         $('#btnupdate' + index).hide();
        //       });
        //     }
        //     return eDiv;
        //   }
        // }
      ],
      headerHeight: 45,
      editType: 'fullRow',
      chartName: null,
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },

        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/DD/YY');
    }
  };
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValue = params => {
    if (Math.sign(params.value) > -1) {
      return (
        '$' +
        (
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString()
      );
    } else {
      return (
        '-$' +
        (
          Math.round((Number(Math.abs(params.value)) + Number.EPSILON) * 100) /
          100
        ).toLocaleString()
      );
    }
  };
  formatCellValueMarkup = params => {
    if (Math.sign(params.value) > -1 && params.value !== null) {
      return params.value;
    } else if (params.value == null) {
      return 0;
    } else {
      return (
        ((Number(params.value) + Number.EPSILON) * 100) /
        100
      ).toLocaleString();
    }
  };
  updateWarrantyReferenceLabor = (
    minDate,
    maxDate,
    minWarrantyRate,
    maxWarranrtyRate,
    payType
  ) => {
    let maxDates = localStorage.getItem('maxDate');
    let minDates = localStorage.getItem('minDate');
    let maxWarrantyRates = localStorage.getItem('maxWarrantyRate');
    let minWarrantyRates = localStorage.getItem('minWarrantyRate');
    if (this.state.isEdited && this.state.isEditData) {
      // console.log("type=",
      // (minDate != minDates) || (maxDate != maxDates) || (minWarrantyRate != minWarrantyRates) || (maxWarranrtyRate != maxWarrantyRates));
      // if ((minDate != minDates) || (maxDate != maxDates) || (minWarrantyRate != minWarrantyRates) || (maxWarranrtyRate != maxWarrantyRates) &&
      //    ((minWarrantyRate != undefined && minWarrantyRates != 'null')||(maxWarranrtyRate != undefined && maxWarrantyRates != 'null'))
      // ) {
      let filteredResult = this.state.rowDataOld.filter(
        item => item.paytype == 'Dominant Warranty'
      );
      const client = makeApolloClient;
      if (minWarrantyRate == '') {
        var minWarrantyRate1 = null;
      } else {
        var minWarrantyRate1 = minWarrantyRate;
      }
      if (maxWarranrtyRate == '') {
        var maxWarrantyRate1 = null;
      } else {
        var maxWarrantyRate1 = maxWarranrtyRate;
      }
      const start = new Date();
      client
        .mutate({
          mutation: UPDATE_WARRANTY_REFERENCE_LABOR,
          variables: {
            mindate: minDate,
            maxdate: maxDate,
            minwarrantyrate: minWarrantyRate1,
            maxwarrantyrate: maxWarrantyRate1,
            payType: payType,
            storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/WarrantyReferenceLabor',
            origin: '',
            event: 'Menu Load',
            is_from: 'UPDATE_WARRANTY_REFERENCE_LABOR',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          //alert('Row updated');
          this.setState({ success: true });
          //  window.location.reload(false);
        })
        .catch(error => {
          this.resetReportGrid();
          var arrSelecte = [];
          this.state.rawGridApi.forEachNode(node => {
            arrSelecte.push(node.data);
          });
          this.state.rowData.push(arrSelecte[0]); // Add new Item
          this.getAgGridData();
          this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
          if (error.graphQLErrors) {
            error.graphQLErrors.map(({ message, locations, path }) =>
              console.log(
                `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
              )
            );
          }
          return error;
        });
    }
    this.setState({ isEdited: false });
  };

  updateWarrantyReferenceParts = (
    minDate,
    maxDate,
    minmarkup,
    maxmarkup,
    payType
  ) => {
    if (this.state.isEdited && this.state.isEditData) {
      let filteredResult = this.state.rowDataOld.filter(
        item => item.paytype == 'Dominant Warranty'
      );

      var maxDate3 = localStorage.getItem('maxDate3');
      var minDate3 = localStorage.getItem('minDate3');
      var minMarkup3 = localStorage.getItem('minMarkup3');
      var maxMarkup3 = localStorage.getItem('maxMarkup3');
      if (minmarkup == '' || minmarkup == undefined) {
        var minmarkup1 = null;
      } else {
        var minmarkup1 = minmarkup;
      }
      if (maxmarkup == '' || maxmarkup == undefined) {
        var maxmarkup1 = null;
      } else {
        var maxmarkup1 = maxmarkup;
      }
      // console.log("data valll=",minDate,minDate3,maxDate,maxDate3,minmarkup1,minMarkup3,maxmarkup1,maxMarkup3);
      // if ((minDate != minDate3) || (maxDate != maxDate3) || (minmarkup1 != minMarkup3) || (maxmarkup1 != maxMarkup3) &&
      // ((minmarkup1 != undefined && minMarkup3 != 'null')||(maxmarkup1 != undefined && maxMarkup3 != 'null'))
      // ) {
      const start = new Date();
      const client = makeApolloClient;

      client
        .mutate({
          mutation: UPDATE_WARRANTY_REFERENCE_PARTS,
          variables: {
            mindate: minDate,
            maxdate: maxDate,
            minmarkup: minmarkup1,
            maxmarkup: maxmarkup1,
            payType: payType,
            storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/WarrantyReferenceParts',
            origin: '',
            event: 'Menu Load',
            is_from: 'UPDATE_WARRANTY_REFERENCE_PARTS',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          this.setState({ success: true });
          // alert('Row updated');
          //  window.location.reload(false);
        })
        .catch(error => {
          this.resetReportGrid();
          var arrSelecte = [];
          this.state.rawGridApi.forEachNode(node => {
            arrSelecte.push(node.data);
          });
          this.state.rowData.push(arrSelecte[0]); // Add new Item
          this.getAgGridData();
          this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
          if (error.graphQLErrors) {
            error.graphQLErrors.map(({ message, locations, path }) =>
              console.log(
                `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
              )
            );
          }
          return error;
        });
    }
    this.setState({ isEdited: false });
  };
  onBtStartEditing = (index, key, char, pinned) => {
    // columnController
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      groupColumn[0]['editable'] = true;
      groupColumn[1]['editable'] = true;
      groupColumn[2]['editable'] = true;
      groupColumn[3]['editable'] = true;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    this.state.rawGridApi.setFocusedCell(index, 'minDate', pinned);
    this.state.drillDownType == 'labor'
      ? this.state.rawGridApi.startEditingCell({
          rowIndex: index,
          colKey: 'minDate',
          colKey: 'minWarrantyRate',
          colKey: 'maxWarranrtyRate',

          rowPinned: pinned,
          keyPress: key,
          charPress: char
        })
      : this.state.rawGridApi.startEditingCell({
          rowIndex: index,
          colKey: 'minDate',
          colKey: 'minMarkup',
          colKey: 'maxMarkup',

          rowPinned: pinned,
          keyPress: key,
          charPress: char
        });
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();

    this.getAgGridData();
  };
  onCellValueChanged = params => {
    if (params.newValue != params.oldValue) {
      this.setState({ isEdited: true });
      var minDate = params.data.minDate;
      var maxDate = params.data.maxDate;
      var minWarrantyRate = params.data.minWarrantyRate;
      var maxWarranrtyRate = params.data.maxWarranrtyRate;
      var payType = params.data.paytype;
      if (this.state.drillDownType == 'labor') {
        this.updateWarrantyReferenceLabor(
          minDate,
          maxDate,
          minWarrantyRate,
          maxWarranrtyRate,
          payType
        );
      } else {
        var minDate = params.data.minDate;
        var maxDate = params.data.maxDate;
        var minMarkup = params.data.minMarkup;
        var maxMarkup = params.data.maxMarkup;
        var payType = params.data.paytype;
        this.updateWarrantyReferenceParts(
          minDate,
          maxDate,
          minMarkup,
          maxMarkup,
          payType
        );
      }
    }
  };
  getAgGridData(reset) {
    if (reset != 'reset') {
      this.setState({ isLoading: true });
    }
    if (this.state.drillDownType == 'labor') {
      getWarrantyReferenceLabor(result => {
        this.setState({ isLoading: false });
        if (result.data.statefulCcPhysicalRwWarrantyLaborRateModels.nodes) {
          this.setState({
            rowData:
              result.data.statefulCcPhysicalRwWarrantyLaborRateModels.nodes
          });
          this.setState({
            rowDataOld:
              result.data.statefulCcPhysicalRwWarrantyLaborRateModels.nodes
          });
          // this.onGroupExpandedOrCollapsed();
        }
      });
    } else {
      getWarrantyReferenceParts(result => {
        this.setState({ isLoading: false });
        if (
          result.data.statefulCcPhysicalRwWarrantyPartsMarkupModelOmits.nodes
        ) {
          this.setState({
            rowData:
              result.data.statefulCcPhysicalRwWarrantyPartsMarkupModelOmits
                .nodes
          });
        }
      });
    }
  }
  //         // this.onGroupExpandedOrCollapsed();
  //       }
  //     });
  //   }
  // }

  //   onBtExport = () => {
  //     var params = {
  //       sheetName: 'Report',
  //       fileName: 'Chartmaster',
  //       customHeader: [
  //         [],
  //         [
  //           {
  //             styleId: 'bigHeader',
  //             data: { type: 'String', value: 'Chart Master' },
  //             mergeAcross: 3
  //           }
  //         ]
  //       ]
  //     };

  //     this.state.rawGridApi.exportDataAsExcel(params);
  //   };
  getDashboardIds = selectedDbd => {
    var schemaName = [];
  };
  handleOk = () => {
    this.setState({ success: false });
  };
  onCellClicked = params => {
    const id = localStorage.getItem('warrantyId');
    if (params.data.id != id) {
      $(`.edit-button`).show();
      $(`.update-button`).hide();
      $(`.cancel-button`).hide();
    }
  };
  resetReportGrid = () => {
    this.onBtStopEditing();
    $(`.edit-button`).show();
    $(`.update-button`).hide();
    $(`.cancel-button`).hide();
    this.gridApi.setColumnDefs([]);
    this.state.drillDownType == 'labor'
      ? this.gridApi.setColumnDefs(this.state.columnDefs)
      : this.gridApi.setColumnDefs(this.state.columnDefsParts);
    this.gridApi.setRowData(this.state.rowData);
    this.getAgGridData('reset');
  };

  render() {
    const { classes } = this.props;

    return (
      <div>
        <Page
          title={
            this.state.drillDownType == 'labor'
              ? 'Warranty Reference Labor'
              : 'Warranty Reference Parts'
          }
        ></Page>
        {this.props.keycloak.realmAccess.roles.includes('client') ||
        this.props.keycloak.realmAccess.roles.includes('user') ||
        JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <Paper square style={{ margin: 8, marginTop: '20px' }}>
            <Tabs
              value={this.state.tabSelection}
              onChange={this.handleTabChange}
              variant="fullWidth"
              indicatorColor="secondary"
              textColor="secondary"
              aria-label="icon label tabs example"
            >
              <Tab
                style={{
                  pointerEvents: 'none',
                  textTransform: 'none',
                  backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                  border:
                    Dealer === 'Armatus'
                      ? '1px solid #003d6b'
                      : '1px solid #C2185B',
                  color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
                label={
                  <div>
                    {this.state.drillDownType == 'labor'
                      ? 'Warranty Reference Labor'
                      : 'Warranty Reference Parts'}
                  </div>
                }
                value="one"
              />
              {/* <Tooltip title="Export To Excel">
              <Link
                style={{ paddingTop: 17, paddingRight: 19, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip> */}
            </Tabs>
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Paper>
        )}
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.success}
        >
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              {this.state.drillDownType == 'labor'
                ? 'Warranty Reference Labor'
                : 'Warranty Reference Parts'}{' '}
              details updated!
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleOk} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: window.innerHeight - 160 + 'px',
            //height: '600px',
            width: this.state.drillDownType == 'labor' ? '922px' : '932px',
            alignContent: 'center',
            marginLeft: '8px',
            display: this.state.tabSelection == 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={
              this.state.drillDownType == 'labor'
                ? this.state.columnDefs
                : this.state.columnDefsParts
            }
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            editType={this.state.editType}
            onCellValueChanged={this.onCellValueChanged.bind(this)}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            headerHeight={this.state.headerHeight}
            suppressClickEdit={true}
            onCellClicked={this.onCellClicked}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: -40
  }
});
export default withStyles(styles)(withKeycloak(WarrantyReference));
