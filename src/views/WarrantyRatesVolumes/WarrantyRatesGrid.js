import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button,
  Tooltip
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Link from '@material-ui/core/Link';
import {
  getDrillDownDataForLaborDiscount,
  getDrillDownDataForPartsDiscount,
  getDrillDownDataForWarrantyRatesLabor,
  getDrillDownDataForWarrantyRatesParts,
  getWarrantyReimbursementsGetAnalysisWarrantyVolumesDaterange
} from 'src/utils/hasuraServices';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import { renderBackButtonForDrillDown } from 'src/components/DrillDownCalculations';
// import DateRangePicker from '@wojtekmaj/react-daterange-picker';
import { withStyles } from '@material-ui/styles';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { MasterDetailModule } from '@ag-grid-enterprise/master-detail';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getLast13Months } from 'src/utils/Utils';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import { ReactSession } from 'react-client-session';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import ExportIcon from '@material-ui/icons/GetApp';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;

class WarrantyRatesGrid extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      console.log(
        'selected=',
        ReactSession.get('selectedStoreId'),
        checkSt,
        localStorage.getItem('selectedStoreId'),
        this.state.store
      );
      if (checkSt == false) {
        window.sortState = {};
        window.filterState = {};
        this.setState({ isLoading: true });
        this.getAgGridData(this.state.queryMonth);
        // this.getAgGridData(this.state.selectedToggle, ReactSession.get("serviceAdvisors"));
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData(
  //         this.state.queryMonth,
  //         this.state.serviceAdvisor,
  //         this.props.type
  //       );
  //     }
  //   }
  // }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = this.props.months[0].monthYear;

    // var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
    //   ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
    //   : this.props.serviceAdvisor;
    var location =
      getVerificationDashboardBaseURL() + '/FOC3_Searchbyro/ag-grid.html';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      // previousLocation: this.props.history.location.prevPath ? this.props.history.location.prevPath :
      //   (this.props.history.location.state ? this.props.history.location.state.prevPath :  '/Discounts'),
      queryMonth: initialQueryMonth,
      selectedMonthYear: initialQueryMonth,
      selectedMonth: moment(localStorage.getItem('closedDate')).startOf(
        'month'
      ),
      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: ['All'],
      isLoading: true,
      isRefresh: false,
      rawGridApi: {},
      gridApi: {},
      tabSelection: 0,
      headerHeight: 45,
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      monthYear: this.props.months,
      ROCount: 0,
      jobCount: 0,
      soldHours: 0,
      lbrSale: 0,
      lbrCost: 0,
      prtsSale: 0,
      prtsCost: 0,
      closedDate: localStorage.getItem('closedDate'),
      startDate: this.props.history
        ? this.props.history.location.state
          ? this.props.history.location.state.startDate
          : moment(new Date(this.props.selectedDates[0])).format('MM/DD/YY')
        : moment(new Date(this.props.selectedDates[0])).format('MM/DD/YY'),
      endDate: this.props.history
        ? this.props.history.location.state
          ? this.props.history.location.state.endDate
          : moment(new Date(this.props.selectedDates[1])).format('MM/DD/YY')
        : moment(new Date(this.props.selectedDates[1])).format('MM/DD/YY'),
      value: [this.props.selectedDates[0], this.props.selectedDates[1]],
      columnDefs: [
        {
          headerName: 'Month',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,
          field: 'monthYear',
          suppressMenu: true,

          unSortIcon: true,
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          cellStyle() {
            return {
              // marginLeft: '-20px',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Closed Date',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          cellStyle: function() {
            return {
              //marginLeft: '-30px',
              border: ' 0px white',
              textAlign: 'left'
            };
          },
          // cellRenderer: function (result) {
          //   var closeddate = JSON.stringify(result.value);
          //   return (
          //     "<a href='#' onclick='navigateToRoDetails(" +
          //     closeddate +
          //     ")'>" +
          //     result.value +
          //     "</a>"
          //   );
          // },
          field: 'closeddate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Open Date',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          cellStyle: function() {
            return {
              border: ' 0px white',
              textAlign: 'left'
            };
          },
          field: 'opendate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'RO',
          chartDataType: 'category',
          field: 'ronumber',
          // width: 100,
          minWidth: 100,
          flex: 1,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          aggFunc: this.nulValue,
          onCellClicked: this.handleSearchByRo,
          // tooltip: function(params) {
          //   return 'View RO';
          // },
          cellRendererFramework: TooltipRenderer,
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              // left: '0px',
              cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'center'
            };
          }
          // rowGroup: true
        },
        {
          headerName: 'Make',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'brand',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          //comparator: this.sortAllTextColumns
          // rowGroup: true
        },

        {
          headerName: 'Labor Line Code',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,
          field: 'lbrlinecode',
          suppressMenu: true,

          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          comparator: this.sortAllTextColumns
          // rowGroup: true
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'paytype',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          // rowGroup: true
        },
        {
          headerName: 'Pay Type Group',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'paytypegroup',
          suppressMenu: true,

          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          // rowGroup: true
        },
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          // width: 100,
          minWidth: 100,
          flex: 1,
          field: 'lbropcode',
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          // comparator: this.sortAllTextColumns
          // rowGroup: true
        },
        {
          headerName: 'Op Category',
          chartDataType: 'category',
          // width: 105,
          minWidth: 105,
          flex: 1,
          field: 'opcategory',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          // rowGroup: true
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 85,
        //   minWidth: 85,
        //   sortable: false,
        //   filter: false,
        //   flex: 1,
        //   suppressMenu: true,
        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   filter: 'agSetColumnFilter',
        //   filterParams: {
        //     values: ['N/A', '']
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'Op Category Description',
          chartDataType: 'series',
          // width: 210,
          minWidth: 210,
          flex: 1,
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          comparator: this.sortAllTextColumns_desc
          // rowGroup: true
        },
        {
          headerName: 'Labor Sale',
          chartDataType: 'series',
          field: 'lbrsale',
          // type: 'numericColumn',
          // width: 90,
          minWidth: 90,
          flex: 1,
          aggFunc: 'sum',

          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Cost',
          chartDataType: 'series',
          // type: 'numericColumn',
          // width: 90,
          minWidth: 90,
          flex: 1,
          cellStyle: { 'text-align': 'right' },
          field: 'lbrcost',
          aggFunc: 'sum',

          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Labor Sold Hours',
          chartDataType: 'series',
          cellStyle: { 'text-align': 'right' },
          // width: 90,
          minWidth: 90,
          flex: 1,
          aggFunc: 'sum',

          valueFormatter: this.formatCellValueWithOut$,
          filterParams: {
            valueFormatter: this.formatCellValueWithOut$
          },
          suppressMenu: true,
          unSortIcon: true,
          field: 'lbrsoldhours',
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },

        {
          headerName: 'Warranty Rate',
          chartDataType: 'series',
          // type: 'numericColumn',
          // width: 110,
          minWidth: 110,
          flex: 1,
          field: 'warrantyrate',

          suppressMenu: true,
          unSortIcon: true,
          aggFunc: 'sum',

          valueFormatter: this.formatCellValue,
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        }
      ],
      columnDefsParts: [
        {
          headerName: 'Month',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,
          field: 'monthYear',

          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },

          cellStyle() {
            return {
              border: ' 0px white',
              textAlign: 'left'
              // marginLeft: '-20px'
            };
          }
        },
        {
          headerName: 'Closed Date',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function() {
            return {
              //  marginLeft: '-30px',
              border: ' 0px white',
              textAlign: 'left'
            };
          },
          // cellRenderer: function (result) {
          //   var closeddate = JSON.stringify(result.value);
          //   return (
          //     "<a href='#' onclick='navigateToRoDetails(" +
          //     closeddate +
          //     ")'>" +
          //     result.value +
          //     "</a>"
          //   );
          // },
          field: 'closeddate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },
        {
          headerName: 'Open Date',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          cellStyle: function() {
            return {
              border: ' 0px white',
              textAlign: 'left'
            };
          },
          field: 'opendate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          }
        },

        {
          headerName: 'RO',
          chartDataType: 'series',
          field: 'ronumber',
          // width: 100,
          minWidth: 100,
          flex: 1,

          cellClass: 'textAlign',
          aggFunc: this.nulValue,
          onCellClicked: this.handleSearchByRo,
          suppressMenu: true,
          unSortIcon: true,
          // tooltip: function(params) {
          //   return 'View RO';
          // },
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          cellStyle: function() {
            return {
              color: '#000000',
              fontWeight: 'bold',
              // left: '0px',
              cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'center'
            };
          }
          // rowGroup: true
        },
        {
          headerName: 'Make',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'brand',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          //comparator: this.sortAllTextColumns
          // rowGroup: true
        },

        {
          headerName: 'Labor Line Code',
          chartDataType: 'series',
          // width: 110,
          minWidth: 110,
          flex: 1,
          field: 'lbrlinecode',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          comparator: this.sortAllTextColumns
          // rowGroup: true
        },
        {
          headerName: 'Lin Cause',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          hide: true,
          field: 'lincause',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Lin Story Text',
          chartDataType: 'series',
          hide: true,
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'linstorytext',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'partspaytype',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          // rowGroup: true
        },
        {
          headerName: 'Pay Type Group',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'partspaytypegroup',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          // rowGroup: true
        },
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'lbropcode',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          //  comparator: this.sortAllTextColumns

          // rowGroup: true
        },
        {
          headerName: 'Op Category',
          chartDataType: 'series',
          // width: 105,
          minWidth: 105,
          flex: 1,
          field: 'opcategory',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          }
          // rowGroup: true
        },
        // {
        //   headerName: 'NA',
        //   field: 'opsubcategory',
        //   width: 80,
        //   minWidth: 80,
        //   flex: 1,
        //   sortable: false,
        //   filter: false,
        //   suppressMenu: true,
        //   cellRenderer: params => {
        //     return `<input type='checkbox' ${
        //       params.value == 'N/A' ? 'checked' : ''
        //     } disabled=disabled/>`;
        //   },
        //   filter: 'agSetColumnFilter',
        //   filterParams: {
        //     values: ['N/A', '']
        //   },
        //   cellStyle: params => {
        //     return params.value && params.value == 'N/A'
        //       ? { border: ' 0px white', textAlign: 'left' }
        //       : { display: 'none' };
        //   }
        // },
        {
          headerName: 'Op Category Description',
          chartDataType: 'series',
          // width: 220,
          minWidth: 220,
          flex: 1,
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          comparator: this.sortAllTextColumns_desc
        },
        {
          headerName: 'Parts Sale',
          chartDataType: 'series',
          // type: 'numericColumn',
          // width: 95,
          minWidth: 95,
          flex: 1,
          field: 'prtextendedsale',

          aggFunc: 'sum',
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Parts Cost',
          chartDataType: 'series',
          // type: 'numericColumn',
          // width: 95,
          minWidth: 95,
          flex: 1,
          field: 'prtextendedcost',

          aggFunc: 'sum',
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Mark Up',
          chartDataType: 'series',
          // type: 'numericColumn',

          // width: 95,
          minWidth: 95,
          flex: 1,
          field: 'markup',
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: params => {
            if (params.value == 0) {
              return '0.0000';
            }
          },
          filterParams: {
            valueFormatter: params => {
              if (params.value == 0) {
                return '0.0000';
              }
            }
          },
          cellStyle: this.cellStyles,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Part Description',
          chartDataType: 'series',
          // width: 150,
          minWidth: 150,
          flex: 1,
          field: 'prtdesc',

          tooltipField: 'prtdesc',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', textAlign: 'left' };
          },
          comparator: this.sortAllTextColumns_desc
        }
      ],
      groupIncludeTotalFooter: true,
      //autoGroupColumnDef: { minWidth: 200 },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        hiddenByDefault: true,
        defaultToolPanel: 'columns'
      },
      pivotMode: true,

      rowData: [],
      overlayNoRowsTemplate:
        '<span style="padding: 10px;">No rows to show</span>',
      defaultColDef: {
        // enableValue: true,
        // enableRowGroup: true,
        // enablePivot: true,
        editable: false,
        sortable: true,
        resizable: true,
        filter: true,
        animateRows: true,
        // filter: 'agTextColumnFilter',
        floatingFilter: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          cellRenderer: params => {
            const span = document.createElement('span');
            span.style.whiteSpace = 'nowrap'; // Prevent line breaks
            span.style.overflow = 'hidden'; // Hide overflowing text
            span.style.textOverflow = 'ellipsis'; // Show ellipsis for overflowing text
            span.style.display = 'block'; // Use block display
            span.style.width = '100%'; // Set width to 100% of container
            span.innerHTML = params.value;
            // span.title = params.value;             // Set tooltip with full text
            return span;
          },
          applyMiniFilterWhileTyping: true,
          debounceMs: 200,
          suppressRemoveEntries: false
        },
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  sortAllTextColumns_desc = (a, b) => {
    // Check for blank values
    if (a === '' || a === null || a === undefined) {
      return -1; // Blank values come first
    }
    if (b === '' || b === null || b === undefined) {
      return 1; // Blank values come first
    }

    // Parse numeric values
    var numA = parseFloat(a);
    var numB = parseFloat(b);

    // Check if both values are numeric
    var isNumA = !isNaN(numA) && isFinite(a);
    var isNumB = !isNaN(numB) && isFinite(b);

    // If both are numeric, sort numerically
    if (isNumA && isNumB) {
      return numA - numB;
    }

    // If only one is numeric, numeric goes before non-numeric
    if (isNumA && !isNumB) {
      return -1;
    }
    if (!isNumA && isNumB) {
      return 1;
    }

    // If both are non-numeric, or both are NaN, sort alphabetically
    return a.localeCompare(b);
  };
  sortAllTextColumns = (a, b) => {
    // Check for blank values
    if (!a) return !b ? 0 : -1;
    if (!b) return 1;

    // Define regular expressions for special characters and numbers
    const specialCharsRegex = /^[^a-zA-Z0-9]+/;
    const numbersRegex = /^[0-9]+/;
    const alphanumericRegex = /^[a-zA-Z0-9]+$/;

    // Check if both values are blank
    const isBlankA = a.trim() === '';
    const isBlankB = b.trim() === '';
    if (isBlankA && isBlankB) return 0;
    if (isBlankA) return -1;
    if (isBlankB) return 1;

    // Check if both values start with special characters
    const isSpecialA = specialCharsRegex.test(a);
    const isSpecialB = specialCharsRegex.test(b);
    if (isSpecialA && isSpecialB) return a.localeCompare(b);
    if (isSpecialA) return -1;
    if (isSpecialB) return 1;

    // Check if both values start with numbers
    const isNumberA = numbersRegex.test(a);
    const isNumberB = numbersRegex.test(b);
    if (isNumberA && isNumberB) return parseFloat(a) - parseFloat(b);
    if (isNumberA) return -1;
    if (isNumberB) return 1;

    // Check if both values are alphanumeric
    const isAlphanumericA = alphanumericRegex.test(a);
    const isAlphanumericB = alphanumericRegex.test(b);
    if (isAlphanumericA && isAlphanumericB) return a.localeCompare(b);
    if (isAlphanumericA) return -1;
    if (isAlphanumericB) return 1;

    // Sort alphabetically for all other cases
    return a.localeCompare(b);
  };

  sortAllTextColumns_old = (a, b) => {
    // Check for blank values
    if (a === '' || a === null || a === undefined) {
      return -1; // Blank values come first
    }
    if (b === '' || b === null || b === undefined) {
      return 1; // Blank values come first
    }

    // Parse numeric values
    var numA = parseFloat(a);
    var numB = parseFloat(b);

    // Check if both values are numeric
    var isNumA = !isNaN(numA) && isFinite(a);
    var isNumB = !isNaN(numB) && isFinite(b);

    // If both are numeric, sort numerically
    if (isNumA && isNumB) {
      return numA - numB;
    }

    // If only one is numeric, numeric goes before non-numeric
    if (isNumA && !isNumB) {
      return -1;
    }
    if (!isNumA && isNumB) {
      return 1;
    }

    // If both are non-numeric, or both are NaN, sort alphabetically
    return a.localeCompare(b);
  };
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/DD/YY');
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/YY');
    }
  };
  formatCellValue = params => {
    // let formatValue = (
    //   Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
    // ).toFixed(2);

    // if (params.value != null && params.value != 0) {
    //   if (Math.sign(params.value) > -1) {
    //     return (
    //       '$' +
    //       (
    //         Math.round(
    //           (Math.abs(Number(params.value)) + Number.EPSILON) * 100
    //         ) / 100
    //       ).toLocaleString()
    //     );
    //   } else {
    //     return (
    //       '-$' +
    //       (
    //         Math.round(
    //           (Math.abs(Number(params.value)) + Number.EPSILON) * 100
    //         ) / 100
    //       ).toLocaleString()
    //     );
    //   }
    // }

    if (params.value != null && params.value !== 0) {
      const value = Math.abs(Number(params.value));
      const roundedValue = Math.round(value * 100) / 100;
      const formattedValue = roundedValue.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      if (params.value < 0) {
        return `-$${formattedValue}`;
      } else {
        return `$${formattedValue}`;
      }
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        Math.abs(
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
      );
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  formatTotals = value => {
    if (value != null && value != 0) {
      return Math.sign(value) > -1
        ? '$' +
            parseFloat(value)
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : '-$' +
            Math.abs(parseFloat(value))
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };

  onFirstDataRendered = params => {
    if (this.gridApi) {
      this.gridApi.getFilterInstance('opcategory', filterInstance => {
        if (filterInstance) {
          const values = filterInstance.getValues();
          if (values && values.includes('N/A')) {
            filterInstance.setModel({
              filterType: 'set',
              values: values.filter(value => value !== 'N/A')
            });
            this.gridApi.onFilterChanged();
          }
        }
      });
    }
  };

  onGridReady = params => {
    // params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    // this.gridApi.sizeColumnsToFit();
    if (this.props.history.location.state == undefined) {
      window.sortState = {};
      window.filterState = {};
    }

    this.gridApi.setSortModel(window.sortState);
    this.gridApi.setFilterModel(window.filterState);
    this.getAgGridData(this.state.queryMonth);
  };

  getAgGridData(queryMonth) {
    let startDate = moment(this.state.startDate).format('YYYY-MM-DD');
    let endDate = moment(this.state.endDate).format('YYYY-MM-DD');
    if (this.props.drillDownType == 'labor') {
      this.setState({ isLoading: true });
      getWarrantyReimbursementsGetAnalysisWarrantyVolumesDaterange(
        startDate,
        endDate,
        result => {
          console.log(
            'rsults=',
            result.data
              .statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumesDaterange
          );
          var resultArr =
            result.data
              .statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumesDaterange
              .nodes;
          resultArr.map(item => {
            if (item.opsubcategory && item.opsubcategory != 'N/A') {
              return (item.opsubcategory = '');
            }
          });
          this.setState({
            rowData: resultArr
          });
          this.getTotalsForDisplay(resultArr);
          this.setState({ isLoading: false });
          console.log('data==', resultArr);
          if (window.filterState != undefined) {
            this.filterByValue();
          }
        }
      );
      // getDrillDownDataForWarrantyRatesLabor(queryMonth, result => {
      //   this.setState({ isLoading: false });

      //   if (
      //     result.data
      //       .statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumes.nodes
      //   ) {
      //     var resultArr =
      //       result.data
      //         .statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumes
      //         .nodes;
      //     resultArr.map(item => {
      //        if(item.opsubcategory && item.opsubcategory != 'N/A') {
      //          return item.opsubcategory = '';
      //        }
      //     });
      //     this.setState({
      //       rowData: resultArr
      //     });
      //     var startDate = moment(new Date(queryMonth + '-01')).format(
      //       'YYYY-MM-DD'
      //     );
      //     var endDate = moment(new Date(queryMonth + '-01'))
      //       .clone()
      //       .endOf('month')
      //       .format('YYYY-MM-DD');
      //     this.setState({
      //       value: [startDate, endDate]
      //     });
      //   }
      // });
    } else {
      this.setState({ isLoading: true });
      getDrillDownDataForWarrantyRatesParts(
        startDate,
        endDate,
        queryMonth,
        result => {
          console.log('data===', result);
          if (
            result.data
              .statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumesPartsDaterange
              .nodes
          ) {
            var resultArr =
              result.data
                .statelessDbdWarrantyReimbursementsGetAnalysisWarrantyVolumesPartsDaterange
                .nodes;
            resultArr.map(item => {
              if (item.opsubcategory && item.opsubcategory != 'N/A') {
                return (item.opsubcategory = '');
              }
            });
            this.setState({
              rowData: resultArr
            });
            this.getTotalsForDisplay(resultArr);
            this.setState({ isLoading: false });
            var startDate = moment(new Date(queryMonth + '-01')).format(
              'YYYY-MM-DD'
            );
            var endDate = moment(new Date(queryMonth + '-01'))
              .clone()
              .endOf('month')
              .format('YYYY-MM-DD');
            this.setState({
              value: [startDate, endDate]
            });
            if (window.filterState != undefined) {
              this.filterByValue();
            }
          }
        }
      );
    }
  }

  nulValue = () => {
    return '';
  };

  setDrillDownValuesToState = Values => {
    this.setState({
      laborSaleSum: Values.laborSaleSum,
      LsWarranty: Values.LsWarranty
    });
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
    this.setState({ category: newValue });
    let type = '';
    if (newValue == 0) {
      type = 'discount_drilldown';
    } else {
      type = 'discounted_parts_drilldown';
    }
    this.setState({
      month_year: this.state.queryMonth,
      chartId: this.props.chartId,
      type: this.props.type
    });
    this.getAgGridData(this.state.queryMonth);
    let data = {
      month_year: this.state.queryMonth,
      type: type,
      chartId: this.props.chartId
    };
    this.props.parentCallback(data);
  };
  getMonthYear = () => {
    var table = [];
    this.setState({
      discountMonthYear: getLast13Months()
    });
  };
  renderBackButton = () => {
    {
      let chartId = this.props.chartId
        ? this.props.chartId
        : this.props.history.location.search.split('=')[1];
      let data = {
        month_year: this.state.queryMonth,
        type: '',
        //chartId: this.props.chartId ? this.props.chartId : this.props.history.location.search.split('=')[1],
        chartId: this.props.chartId,
        history: this.props.history,
        prevPath: this.state.previousLocation,
        drillDown:
          chartId == 1111
            ? 41
            : chartId == 1115
            ? 42
            : chartId == 1232
            ? 45
            : chartId == 1165
            ? 44
            : 43
      };
      this.props.parentCallback(data);
    }
  };
  handleSearchByRo = params => {
    window.sortState = this.gridApi.getSortModel();
    // window.colState = this.state.gridColumnApi.getColumnState();
    window.filterState = this.gridApi.getFilterModel();
    console.log('window.sortState=', window.filterState);
    this.props.history.push({
      pathname: '/SearchByRO',
      state: {
        ronumber: params.value,
        pageType: 'WarrantyRates_' + this.props.drillDownType,
        startDate: this.state.startDate,
        endDate: this.state.endDate
      }
    });
  };
  // handleSearchByRo = params => {
  //   this.props.history.push({
  //     pathname: '/SearchByRO',
  //     state: {
  //       ronumber: params.value,
  //       queryMonth: this.state.queryMonth,
  //       pageType: 'WarrantyRates_' + this.props.drillDownType
  //     }
  //   });
  // };
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterState);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    let rowData = [];
    this.gridApi.forEachNodeAfterFilter(node => {
      rowData.push(node.data);
    });
    this.getTotalsForDisplay(rowData);
  };
  externalFilterChanged = () => {
    //ageType = 'test';
    console.log(
      'query=',
      this.state.queryMonth,
      this.state.serviceAdvisor,
      this.props.type
    );
    this.getAgGridData(this.state.queryMonth);
    // this.state.rawGridApi.onFilterChanged();
  };
  handleMonthChange = async event => {
    await this.setState({
      queryMonth: ''
    });
    this.setState({
      queryMonth: event.target.value
    });
    var startDate = moment(new Date(event.target.value + '-01')).format(
      'YYYY-MM-DD'
    );
    var endDate = moment(new Date(event.target.value + '-01'))
      .clone()
      .endOf('month')
      .format('YYYY-MM-DD');
    var startDate1 = moment(new Date(event.target.value + '-01')).format(
      'MM/DD/YY'
    );
    var endDate1 = moment(new Date(event.target.value + '-01'))
      .clone()
      .endOf('month')
      .format('MM/DD/YY');
    console.log('VALUE==', startDate, endDate, startDate1, endDate1);
    const newMonth = moment(event.target.value, 'YYYY-MM');
    await this.setState({
      value: [startDate, endDate],
      selectedMonth: newMonth.startOf('month'),
      startDate: startDate1,
      endDate: endDate1
    });
    // this.getAgGridData(
    //   event.target.value,
    //   this.state.serviceAdvisor,
    //   this.props.type
    // );
    //ageType = 'test';
    // this.state.rawGridApi.onFilterChanged();
    this.externalFilterChanged();
  };
  isExternalFilterPresent = () => {
    return this.state.value;
  };

  doesExternalFilterPass = node => {
    console.log('node', node);

    if (node.alreadyRendered == false) {
      let inputValue0 = `${this.state.startDate} `;
      let inputValue1 = `${this.state.endDate}`;
      let formattedDate0 = moment(inputValue0, 'MM/DD/YY').format('MM/DD/YYYY');
      let formattedDate1 = moment(inputValue1, 'MM/DD/YY').format('MM/DD/YYYY');
      console.log('node20', formattedDate0);
      console.log('node21', formattedDate1);
      if (formattedDate0 != '') {
        let filteredUsers = this.state.rowData.filter(
          user =>
            formattedDate0 <= user.closeddate &&
            user.closeddate <= formattedDate1
        );
        filteredUsers.map(item => {
          if (item.opsubcategory && item.opsubcategory != 'N/A') {
            return (item.opsubcategory = '');
          }
        });
        this.setState({
          rowData: filteredUsers
        });
        return (
          node.data.closeddate >= formattedDate0 &&
          node.data.closeddate <= formattedDate1
        );
      }
    }
  };
  // doesExternalFilterPass = node => {
  //   if (node.alreadyRendered == false) {
  //     if (this.state.value[0] != '') {
  //       let filteredUsers = this.state.rowData.filter(
  //         user =>
  //           moment(this.state.value[0]).format('MM/DD/YY') <= user.closeddate &&
  //           user.closeddate <= moment(this.state.value[1]).format('MM/DD/YY')
  //       );
  //       filteredUsers.map(item => {
  //          if(item.opsubcategory && item.opsubcategory != 'N/A') {
  //            return item.opsubcategory = '';
  //          }
  //       });
  //       this.setState({
  //         rowData: filteredUsers
  //       });
  //       return (
  //         node.data.closeddate >=
  //           moment(this.state.value[0]).format('MM/DD/YY') &&
  //         node.data.closeddate <= moment(this.state.value[1]).format('MM/DD/YY')
  //       );
  //     }
  //   }
  // };
  resetReportGrid = () => {
    window.sortState = {};
    window.filterState = {};
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs( this.props.drillDownType == 'labor'
    // ? this.state.columnDefs
    // : this.state.columnDefsParts);

    // this.setState({
    //   queryMonth: this.state.queryMonth
    // });
  };
  handleCallback = (event, picker) => {
    window.sortState = {};
    window.filterState = {};
    this.setState({ filterDisabled: false });
    this.setState({
      value:
        picker.startDate.format('MM/DD/YY') +
        ' - ' +
        picker.endDate.format('MM/DD/YY'),
      startDate: picker.startDate.format('MM/DD/YY'),
      endDate: picker.endDate.format('MM/DD/YY')
    });
    let date = new Date(picker.startDate);
    this.setState({
      queryMonth: date.getFullYear() + '-' + moment(new Date(date)).format('MM')
    });
    this.externalFilterChanged();
    console.log(
      'querymonth=',
      date.getFullYear() + '-' + moment(new Date(date)).format('MM')
    );
  };
  getTotalsForDisplay = data => {
    var rowData = data ? data : this.state.rowData;
    var soldHours = 0;
    var lbrSale = 0;
    var lbrCost = 0;
    var prtsSale = 0;
    var prtsCost = 0;
    var roCount = 0;
    var ronumberArr = [];
    const ronumberSet = new Set();
    var lbrGP = 0;
    var prtsGP = 0;
    var lbrGPPerc = 0;
    var prtsGPPerc = 0;
    const closedDateMap = new Map();

    if (rowData.length > 0) {
      rowData.map(item => {
        soldHours += Number(item.lbrsoldhours);
        lbrSale += Number(item.lbrsale);
        lbrCost += Number(item.lbrcost);
        prtsSale += Number(item.prtextendedsale);
        prtsCost += Number(item.prtextendedcost);
        ronumberArr.push(item.ronumber);
      });
      rowData.forEach(item => {
        const { closeddate, ronumber } = item;
        if (!closedDateMap.has(closeddate)) {
          closedDateMap.set(closeddate, new Set());
        }
        closedDateMap.get(closeddate).add(ronumber);
      });
      let totalDistinctRonumberCount = 0;
      closedDateMap.forEach(set => {
        totalDistinctRonumberCount += set.size; // Add the size of the Set (distinct ronumbers)
      });
      var roCount1 = totalDistinctRonumberCount;
      // ronumberArr.map(obj => {
      //   if (obj) {
      //     ronumberSet.add(obj);
      //   }
      // });
      // var roCount1 = ronumberSet.size;
    }
    this.setState({
      ROCount: roCount1,
      soldHours: soldHours,
      lbrSale: lbrSale,
      lbrCost: lbrCost,
      prtsSale: prtsSale,
      prtsCost: prtsCost,
      lbrGP: lbrGP,
      prtsGP: prtsGP,
      lbrGPPerc: isNaN(lbrGPPerc) ? 0 : lbrGPPerc,
      prtsGPPerc: isNaN(prtsGPPerc) ? 0 : prtsGPPerc,
      jobCount: ronumberArr.length
    });
    // this.externalFilterChanged();
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName:
        this.props.drillDownType == 'labor'
          ? 'Labor - Warranty Jobs'
          : 'Parts - Warranty Jobs',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value:
                this.props.drillDownType == 'labor'
                  ? 'Labor - Warranty Jobs'
                  : 'Parts - Warranty Jobs'
            },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: params => {
        const fieldsToFormat = [
          'lbrsale',
          'lbrcost',
          'lbrsoldhours',
          'warrantyrate',
          'prtextendedsale',
          'prtextendedcost',
          'markup'
        ];

        if (params.column.getColId() === 'monthYear' && params.value) {
          const [year, month] = params.value.split('-') || [];
          if (year && month) {
            return `${month}/${year.slice(2)}`;
          }
          return params.value;
        }

        if (params.column.getColId() === 'closeddate' && params.value) {
          const date = new Date(params.value);

          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date
            .getDate()
            .toString()
            .padStart(2, '0');
          const year = date
            .getFullYear()
            .toString()
            .slice(2);

          return `${month}/${day}/${year}`;
        }
        if (params.column.getColId() === 'opendate' && params.value) {
          const date = new Date(params.value);

          const month = (date.getMonth() + 1).toString().padStart(2, '0');
          const day = date
            .getDate()
            .toString()
            .padStart(2, '0');
          const year = date
            .getFullYear()
            .toString()
            .slice(2);

          return `${month}/${day}/${year}`;
        }

        if (fieldsToFormat.includes(params.column.getColId())) {
          if (params.value != null && params.value !== 0) {
            const value = Math.abs(Number(params.value));
            const roundedValue = Math.round(value * 100) / 100;
            const formattedValue = roundedValue.toLocaleString(undefined, {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2
            });

            // Explicitly set four decimal places for 'markup' field
            if (params.column.getColId() === 'markup') {
              const formattedValueMarkup = value.toLocaleString(undefined, {
                minimumFractionDigits: 4,
                maximumFractionDigits: 4
              });
              return params.value > 0
                ? formattedValueMarkup
                : `-${formattedValueMarkup}`;
              console.log('formattedValueMarkup====', formattedValueMarkup);
            }
            if (params.value > 0) {
              if (params.column.getColId() === 'lbrsoldhours') {
                return formattedValue;
              } else {
                return `$${formattedValue}`;
              }
            } else {
              if (params.column.getColId() === 'lbrsoldhours') {
                return `-${formattedValue}`;
              } else {
                return `-$${formattedValue}`;
              }
            }
          }
        }
        return params.value;
      }
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  render() {
    const { classes } = this.props;
    const { selectedMonth, startDate, endDate } = this.state;

    return (
      <div>
        <Paper square style={{ margin: 8, marginTop: '20px' }}>
          <Tabs
            value={this.state.tabSelection}
            //onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
            style={{ pointerEvents: 'none' }}
          >
            <Tab
              style={{
                textTransform: 'none',
                paddingRight: 182,
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
              label={
                <div>
                  {this.props.drillDownType == 'labor'
                    ? 'Labor - Warranty Jobs'
                    : 'Parts - Warranty Jobs'}
                </div>
              }
              value="one"
            />
          </Tabs>
        </Paper>

        <Paper square justify="center">
          <Grid container justify="flex-start" style={{ padding: '5px' }}>
            {/* <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
                //  style={{marginTop:25}}
              >
                <InputLabel
                  htmlFor="outlined-age-native-simple"
                  margin="dense"
                  style={{ marginTop: 0, marginLeft: -4 }}
                >
                  Select Month
                </InputLabel>
                <Select
                  MenuProps={{
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left'
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left'
                    },
                    getContentAnchorEl: null
                  }}
                  variant="outlined"
                  label="Group By"
                  name="group-by-type"
                  value={
                    this.state.monthYear.length > 0 ? this.state.queryMonth : ''
                  }
                  onChange={this.handleMonthChange}
                  style={{ height: 35}}
                  // className={classes.monthSelector}
                >
                  {this.state.monthYear.length > 0 &&
                    this.state.monthYear.map(month => (
                      <MenuItem value={month.monthYear}>
                        {moment(month.monthYear).format('MMM-YY')}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl> */}

            {/* <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <InputLabel
                  htmlFor="outlined-age-native-simple"
                  margin="dense"
                  style={{ marginTop: 0, marginLeft: -4 }}
                >
                  History For
                </InputLabel>
                <Select
                  MenuProps={{
                    anchorOrigin: {
                      vertical: 'bottom',
                      horizontal: 'left'
                    },
                    transformOrigin: {
                      vertical: 'top',
                      horizontal: 'left'
                    },
                    getContentAnchorEl: null
                  }}
                  variant="outlined"
                  label="Group By"
                  name="group-by-type"
                  value={this.state.optionName}
                  className={classes.monthSelector}
                  onChange={this.optionChange}
                  id="advisor-dropdown"
                >
                  <MenuItem value={'advisor_goal'}>Advisor Goal</MenuItem>
                  <MenuItem value={'Chart'}>Chart Master</MenuItem>
                  <MenuItem value={'Opcode'}>Opcode</MenuItem>
                  <MenuItem value={'Paytype'}>Paytype</MenuItem>
                  <MenuItem value={'Advisor'}>Service Advisor</MenuItem>
                  <MenuItem value={'store_goal'}>Store Goal</MenuItem>
                  <MenuItem value={'Store_settings'}>Store Settings</MenuItem>
                  <MenuItem value={'Technician'}>Technician</MenuItem>
                </Select>
              </FormControl> */}

            {/* <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                <Typography style={{ fontSize: 12 }} sx={{ mt: 2, mb: 1 }}>
                  Month Range{' '}
                </Typography>
                <DateRangePicker
                  onChange={newValue => {
                    this.setState({
                      value: newValue
                    });
                    let date = new Date(newValue[0]);
                    this.setState({
                      queryMonth:
                        date.getFullYear() +
                        '-' +
                        moment(new Date(date)).format('MM')
                    });
                  }}
                  value={this.state.value}
                  className={classes.reactDaterangePicker}
                  format={"MM/dd/yy"}
                />
              </FormControl> */}
            <FormControl
              variant="outlined"
              margin="dense"
              className={clsx(classes.formControl, 'input-container')}
            >
              <DateRangePicker
                key={selectedMonth.format()}
                initialSettings={{
                  startDate,
                  endDate,
                  locale: {
                    format: 'MM/DD/YY',
                    separator: ' - '
                  },
                  ranges: {
                    ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.today]: [
                      moment(
                        this.props.dates[0] && this.props.dates[0].today
                      ).toDate(),
                      moment(
                        this.props.dates[0] && this.props.dates[0].today
                      ).toDate()
                    ],
                    ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.yesterDay]: [
                      moment(
                        this.props.dates[0] && this.props.dates[0].yesterday
                      ).toDate(),
                      moment(
                        this.props.dates[0] && this.props.dates[0].yesterday
                      ).toDate()
                    ],
                    ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.dayBfYest]: [
                      moment(
                        this.props.dates[0] &&
                          this.props.dates[0].dayBeforeYesterday
                      ).toDate(),
                      moment(
                        this.props.dates[0] &&
                          this.props.dates[0].dayBeforeYesterday
                      ).toDate()
                    ],
                    ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.thisWeek]: [
                      moment(this.props.dates[0].thisweekstartdate).toDate(),
                      moment(this.props.dates[0].thisweekenddate).toDate()
                    ],
                    ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.lastWeek]: [
                      moment(this.props.dates[0].lastweekstartdate).toDate(),
                      moment(this.props.dates[0].lastweekenddate).toDate()
                    ],
                    ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.lastTwoWeek]: [
                      moment(this.props.dates[0].lasttwoweekstartdate).toDate(),
                      moment(this.props.dates[0].lasttwoweekenddate).toDate()
                    ],
                    ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.mtd]: [
                      moment(
                        this.props.dates[0] && this.props.dates[0].mtdstartdate
                      ).toDate(),
                      moment(
                        this.props.dates[0] && this.props.dates[0].mtdenddate
                      ).toDate()
                    ],
                    ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.lastMonth]: [
                      moment(
                        this.props.dates[0] &&
                          this.props.dates[0].lastmonthstartdate
                      ).toDate(),
                      moment(
                        this.props.dates[0] &&
                          this.props.dates[0].lastmonthenddate
                      ).toDate()
                    ],
                    ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.lastThreeMonths]: [
                      moment(
                        this.props.dates[0].lastthreemonthstartdate
                      ).toDate(),
                      moment(this.props.dates[0].lastthreemonthenddate).toDate()
                    ],
                    ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.lastQtr]: [
                      moment(this.props.dates[0].lastquarterstartdate).toDate(),
                      moment(this.props.dates[0].lastquarterenddate).toDate()
                    ],
                    ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.ytd]: [
                      moment(this.props.dates[0].ytdstartdate).toDate(),
                      moment(this.props.dates[0].ytdenddate).toDate()
                    ],
                    ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.lastTwelveMonths]: [
                      moment(
                        this.props.dates[0].lasttwelvemonthstartdate
                      ).toDate(),
                      moment(
                        this.props.dates[0].lasttwelvemonthenddate
                      ).toDate()
                    ],
                    ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                    this.props.lastYear]: [
                      moment(this.props.dates[0].lastyearstartdate).toDate(),
                      moment(this.props.dates[0].lastyearenddate).toDate()
                    ]
                  },
                  // ranges: {
                  //   Today: [moment().toDate(), moment().toDate()],
                  //   Yesterday: [
                  //     moment()
                  //       .subtract(1, 'days')
                  //       .toDate(),
                  //     moment()
                  //       .subtract(1, 'days')
                  //       .toDate()
                  //   ],
                  //   'Last 7 Days': [
                  //     moment()
                  //       .subtract(6, 'days')
                  //       .toDate(),
                  //     moment().toDate()
                  //   ],
                  //   'Last 30 Days': [
                  //     moment()
                  //       .subtract(29, 'days')
                  //       .toDate(),
                  //     moment().toDate()
                  //   ],
                  //   'This Month': [
                  //     moment()
                  //       .startOf('month')
                  //       .toDate(),
                  //     moment()
                  //       .endOf('month')
                  //       .toDate()
                  //   ],
                  //   'Last Month': [
                  //     moment()
                  //       .subtract(1, 'month')
                  //       .startOf('month')
                  //       .toDate(),
                  //     moment()
                  //       .subtract(1, 'month')
                  //       .endOf('month')
                  //       .toDate()
                  //   ]
                  // },
                  //  maxDate: moment(this.state.closedDate).toDate(),
                  maxDate: moment(
                    this.props.dates[0] && this.props.dates[0].today
                  ).toDate(),
                  alwaysShowCalendars: false,
                  applyClass: clsx(classes.calButton, 'apply-btn'),
                  cancelClass: clsx(classes.calButton, 'apply-btn'),
                  startDate:
                    this.state.startDate && this.state.startDate != ''
                      ? moment(this.state.startDate).toDate()
                      : moment(this.props.dates[0].mtdstartdate).toDate(),
                  endDate:
                    this.state.endDate && this.state.endDate != ''
                      ? moment(this.state.endDate).toDate()
                      : moment(this.props.dates[0].mtdenddate).toDate()
                }}
                onApply={this.handleCallback}
              >
                <input
                  type="text"
                  className="datepicker"
                  id="picker"
                  name="picker"
                  aria-labelledby="label-picker"
                  // value={`${startDate} - ${endDate}`}
                />
              </DateRangePicker>
              <label class="labelpicker" for="picker" id="label-picker">
                <div class="textpicker">Select Date (EST)</div>
              </label>
            </FormControl>

            {/* <Button
                variant="contained"
                onClick={this.externalFilterChanged}
                color="primary"
                style={{ marginLeft: 8, width: 50, height: 30 }}
                className={clsx(classes.apply, 'apply-btn')}
              >
                Apply
              </Button> */}

            {/* <input
                  type="text"
                  className="datepicker"
                  id="picker"
                  name="picker"
                  aria-labelledby="label-picker"
                  value={`${startDate} - ${endDate}`}
                />
              </DateRangePicker>
              <label class="labelpicker" for="picker" id="label-picker">
                <div class="textpicker">Select Date</div>
              </label>
            </FormControl> */}

            {/* <Button
              variant="contained"
              onClick={this.externalFilterChanged}
              color="primary"
              style={{ marginLeft: 8, width: 50, height: 30 }}
              className={clsx(classes.apply, 'apply-btn')}
            >
              Apply
            </Button> */}
            <div className={classes.containerTotal} title="Page Summary">
              <span class="title1">Page Summary</span>
              <Button className={classes.summaryBlock}>
                <Typography
                  variant="h5"
                  align="left"
                  className={classes.summaryBlockText}
                >
                  RO Count :
                  <span className={classes.spancls}>
                    {this.state.ROCount
                      ? this.state.ROCount.toString().replace(
                          /\B(?=(\d{3})+(?!\d))/g,
                          ','
                        )
                      : '0'}
                  </span>
                </Typography>
              </Button>
              <Button className={classes.summaryBlock}>
                <Typography
                  variant="h5"
                  align="left"
                  className={classes.summaryBlockText}
                >
                  Job Count :
                  <span className={classes.spancls}>
                    {this.state.jobCount
                      ? this.state.jobCount
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                      : '0'}
                  </span>
                </Typography>
              </Button>
              {this.props.drillDownType == 'labor' ? (
                <Button className={classes.summaryBlock}>
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Hours Sold :
                    <span className={classes.spancls}>
                      {this.state.soldHours
                        .toFixed(2)
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  </Typography>
                </Button>
              ) : (
                ''
              )}
              <Button className={classes.summaryBlock}>
                {this.props.drillDownType == 'labor' ? (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor Sale :
                    <span className={classes.spancls}>
                      {this.formatTotals(this.state.lbrSale)}
                    </span>
                  </Typography>
                ) : (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts Sale :
                    <span className={classes.spancls}>
                      {this.formatTotals(this.state.prtsSale)}
                    </span>
                  </Typography>
                )}
              </Button>
              <Button className={classes.summaryBlock}>
                {this.props.drillDownType == 'labor' ? (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Labor Cost :
                    <span className={classes.spancls}>
                      {this.formatTotals(this.state.lbrCost)}
                    </span>
                  </Typography>
                ) : (
                  <Typography
                    variant="h6"
                    align="left"
                    className={classes.summaryBlockText}
                  >
                    Parts Cost :
                    <span className={classes.spancls}>
                      {this.formatTotals(this.state.prtsCost)}
                    </span>
                  </Typography>
                )}
              </Button>
            </div>
            <div>
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{
                    marginLeft: 42,
                    float: 'left',
                    marginTop: 9,
                    padding: 7,
                    cursor: 'pointer'
                  }}
                  onClick={this.onBtExport}
                >
                  <ExportIcon />
                </Link>
              </Tooltip>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.back, 'reset-btn')}
                onClick={this.resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </div>
          </Grid>
        </Paper>

        {this.state.isLoading && (
          <div
          // style={{
          //   display: this.state.tabSelection != 1 ? 'none' : 'block'
          // }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              (this.state.rowData.length > 0
                ? window.innerHeight - 260
                : window.innerHeight - 160) + 'px',
            width: '98.8%',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            // isExternalFilterPresent={this.isExternalFilterPresent}
            // doesExternalFilterPass={this.doesExternalFilterPass}
            floatingFilter={true}
            defaultColDef={this.state.defaultColDef}
            headerHeight={this.state.headerHeight}
            sideBar={this.state.sideBar}
            modules={AllModules}
            columnDefs={
              this.props.drillDownType == 'labor'
                ? this.state.columnDefs
                : this.state.columnDefsParts
            }
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
            onFilterChanged={this.onFilterChanged}
            excelStyles={this.state.excelStyles}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
            onFirstDataRendered={this.onFirstDataRendered}
          />
        </div>
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    border: 'thin solid #968989 !important',
    height: '35px !important'
  },
  reactDaterangePicker: {
    //padding: '4px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px'
  },
  back: {
    marginRight: 30,
    float: 'left',
    marginTop: 15
  },
  apply: {
    marginTop: 10
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#003d6b',
    fontWeight: 'bold'
  },
  containerTotal: {
    marginTop: '7px',
    border: '1px solid rgba(0, 0, 0, 0.23)',
    position: 'relative',
    padding: '6px 0px 13px',
    height: '40px',
    borderRadius: '4px',
    marginLeft: 20,
    width: '56%'
  },
  spancls: {
    color: '#7987a1',
    marginLeft: 3,
    fontSize: '12px'
  }
});

export default withStyles(styles)(WarrantyRatesGrid);
