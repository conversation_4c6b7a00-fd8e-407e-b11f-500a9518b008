import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import WarrantyRatesGrid from './WarrantyRatesGrid';
import {
  getDrillDownMonthYears,
  getKpiToggleOptionsWithTimeZone
} from 'src/utils/hasuraServices';
import moment from 'moment';
import { useHistory } from 'react-router';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { useDispatch, useSelector } from 'react-redux';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { getTimeZone, getYearValue } from 'src/utils/Utils';
//import queryString from 'query-string';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

const WarrantyRatesVolumes = props => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const [dates, setDates] = useState([]);
  const [thisWeek, setThisWeek] = useState('');
  const [lastWeek, setLastWeek] = useState('');
  const [lastTwoWeek, setLastTwoWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [today, setToday] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');
  const session = useSelector(state => state.session);
  let drillDownType = '';
  console.log('this.props', props);
  if (props.location.pathname == '/WarrantyRatesLabor') {
    drillDownType = 'labor';
  } else {
    drillDownType = 'parts';
  }
  useEffect(() => {
    // var date = new Date();
    // var startDate = moment(
    //   new Date(date.getFullYear(), date.getMonth(), 1)
    // ).format('YYYY-MM-DD');
    // var endDate = moment(new Date())
    //   .clone()
    //   .format('YYYY-MM-DD');
    // var startDate = moment(localStorage.getItem('closedDate'))
    //   .startOf('month')
    //   .format('YYYY-MM-DD');

    // var endDate = moment(localStorage.getItem('closedDate'))
    //   .clone()
    //   .format('YYYY-MM-DD');

    let startDate =
      session.kpiToggleStartDate != '' ? session.kpiToggleStartDate : '';
    let endDate =
      session.kpiToggleEndDate != '' ? session.kpiToggleEndDate : '';
    setSelectedDates([startDate, endDate]);
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;
        setMonths(monthArr);
      }
    });

    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);
        setThisWeek(
          moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY")
        );
        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setLastTwoWeek(
          moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );

        if (startDate == '' && endDate == '') {
          startDate = moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD');

          endDate = moment(dataArr[0].mtdenddate).format('YYYY-MM-DD');

          setSelectedDates([startDate, endDate]);
        }
        console.log(
          'yesterDay===',
          yesterDay,
          '===',
          moment(dataArr[0].yesterday).format("MMM DD ' YY"),
          '=-=',
          dataArr[0]
        );
      }
    });
  }, []);

  if (months.length > 0 && selectedDates.length > 0 && dates.length > 0) {
    return (
      <Page
        className={classes.root}
        title={
          drillDownType == 'labor'
            ? 'Labor - Warranty Jobs'
            : 'Parts - Warranty Jobs'
        }
      >
        {/* {props.keycloak.realmAccess.roles.includes('client') ||
        props.keycloak.realmAccess.roles.includes('user') ? (
          <Redirect to="/errors/error-404" />
        ) : ( */}
        {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <WarrantyRatesGrid
            history={history}
            drillDownType={drillDownType}
            months={months}
            selectedDates={selectedDates}
            session={session}
            dates={dates}
            today={today}
            yesterDay={yesterDay}
            dayBfYest={dayBfYest}
            lastWeek={lastWeek}
            thisWeek={thisWeek}
            lastTwoWeek={lastTwoWeek}
            mtd={mtd}
            lastMonth={lastMonth}
            lastThreeMonths={lastThreeMonths}
            lastQtr={lastQtr}
            ytd={ytd}
            lastTwelveMonths={lastTwelveMonths}
            lastYear={lastYear}
          />
        )}
        {/* )} */}
      </Page>
    );
  } else {
    return <LoaderSkeleton></LoaderSkeleton>;
  }
};

export default withKeycloak(WarrantyRatesVolumes);
