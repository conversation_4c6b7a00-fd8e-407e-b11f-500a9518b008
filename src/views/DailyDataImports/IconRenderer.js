import React, { Component } from 'react';
import { FormControl, TextField, Tooltip, IconButton } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputAdornment from '@material-ui/core/InputAdornment';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import clsx from 'clsx';
import moment from 'moment';
import 'src/input-style.css';
import { TransferWithinAStationOutlined } from '@material-ui/icons';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';

class IconRenderer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { classes, value } = this.props;
    console.log('value====', value);
    return (
      <div
        style={{
          display: 'inline-flex',
          gap: 3,
          marginTop: '1px',
          alignItems: 'center'
        }}
      >
        {value}
        {value != 'SUCCESS' && (
          <Tooltip
            title={
              <span style={{ fontSize: 10 }}>
                Daily Imports have failed. We are working on it. Your data
                should be refreshed in 24hrs.{' '}
              </span>
            }
          >
            <IconButton size="small" classes="infoIcon">
              <InfoOutlinedIcon
                style={{ width: '12px', height: '12px', gap: '5 !important' }}
              />
            </IconButton>
          </Tooltip>
        )}
      </div>
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    width: 60,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: '8px !important',
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '75px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,
    width: 80,
    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 4
  }
});
export default withStyles(styles)(IconRenderer);
