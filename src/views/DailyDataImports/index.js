import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import DailyDataImports from './DailyDataImports';
import { getDrillDownMonthYears } from 'src/utils/hasuraServices';
import moment from 'moment';
import { useHistory } from 'react-router';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
//import queryString from 'query-string';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

const DailyDataImportsIndex = props => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const session = useSelector(state => state.session);
  let drillDownType = '';

  if (props.location.pathname == '/WarrantyRatesLabor') {
    drillDownType = 'labor';
  } else {
    drillDownType = 'parts';
  }
  useEffect(() => {
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date(monthArr[0].monthYear + '-01'))
          .clone()
          .endOf('month')
          .format('YYYY-MM-DD');

        setSelectedDates([startDate, endDate]);
      }
    });
  }, []);
  if (months.length > 0 && selectedDates.length > 0) {
    return (
      <Page className={classes.root} title="Daily Data Imports">
        {props.keycloak.realmAccess.roles.includes('client') ||
        props.keycloak.realmAccess.roles.includes('user') ||
        JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <DailyDataImports
            history={history}
            drillDownType={drillDownType}
            months={months}
            selectedDates={selectedDates}
            session={session}
          />
        )}
      </Page>
    );
  } else {
    return <LoaderSkeleton></LoaderSkeleton>;
  }
};

export default withKeycloak(DailyDataImportsIndex);
