import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button,
  Tooltip
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Link from '@material-ui/core/Link';
import { GetDailyDataloadStatus } from 'src/utils/hasuraServices';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import { renderBackButtonForDrillDown } from 'src/components/DrillDownCalculations';
import DateRangePicker from '@wojtekmaj/react-daterange-picker';
import { withStyles } from '@material-ui/styles';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { MasterDetailModule } from '@ag-grid-enterprise/master-detail';
import { MenuModule } from '@ag-grid-enterprise/menu';
import { ModuleRegistry } from '@ag-grid-community/core';
import { getLast13Months } from 'src/utils/Utils';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import { addDays } from 'date-fns';
import { ReactSession } from 'react-client-session';
import ExportIcon from '@material-ui/icons/GetApp';
import {
  GET_DAILY_DATA_LOAD,
  GET_DAILY_DATA_LOAD_OLD
} from 'src/graphql/queries';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import IconRenderer from './IconRenderer';
import { traceSpan } from 'src/utils/OTTTracing';
import { isValidDate } from 'src/components/ViewGraphDetailsAction';

var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class DailyDataImports extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData( this.state.value ? this.state.value : new Date());
    //     this.setState({ value: this.state.value ? this.state.value : new Date() });
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData(this.state.value ? this.state.value : new Date());
        this.setState({
          value: this.state.value ? this.state.value : new Date()
        });
      }
    }
  }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    var initialQueryMonth = this.props.months[0].monthYear;

    // var initialServiceAdvisor = this.props.serviceAdvisor.split('[')[1]
    //   ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
    //   : this.props.serviceAdvisor;
    var location =
      getVerificationDashboardBaseURL() + '/FOC3_Searchbyro/ag-grid.html';
    var storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      // previousLocation: this.props.history.location.prevPath ? this.props.history.location.prevPath :
      //   (this.props.history.location.state ? this.props.history.location.state.prevPath :  '/Discounts'),
      queryMonth: initialQueryMonth,
      selectedMonthYear: initialQueryMonth,
      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: ['All'],
      isLoading: true,
      isRefresh: false,
      rawGridApi: {},
      gridApi: {},
      tabSelection: 0,
      headerHeight: 45,
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      monthYear: this.props.months,
      startDate: '',
      endDate: '',
      value: new Date(),
      columnDefs: [
        {
          headerName: 'Date From',
          chartDataType: 'category',
          field: 'startdate',
          width: 130,
          minWidth: 130,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          editable: false,
          // aggFunc: this.nulValue,
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // rowGroup: true
        },
        {
          headerName: 'Date To',
          chartDataType: 'series',
          width: 130,
          minWidth: 130,
          field: 'enddate',
          suppressMenu: true,

          editable: false,
          unSortIcon: true,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              // left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Load Date',
          chartDataType: 'series',
          width: 128,
          minWidth: 128,
          field: 'loaddate',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              // left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Load Time(EST)',
          chartDataType: 'series',
          width: 130,
          minWidth: 130,
          field: 'loadtime',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          // valueFormatter: this.formatCellValueMonthYear,
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              // left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Import Status',
          chartDataType: 'series',
          width: 130,
          minWidth: 130,
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          cellRendererFramework: IconRenderer,
          // cellRenderer: 'IconRenderer',
          cellStyle: function(params) {
            return params.value.toLowerCase() == 'success'
              ? {
                  color: '#008000',
                  border: ' 0px white',
                  fontWeight: 'bold',
                  textAlign: 'left'
                }
              : {
                  color: '#ff0000',
                  border: ' 0px white',
                  fontWeight: 'bold',
                  textAlign: 'left'
                };
          },
          field: 'status'
        },
        {
          headerName: 'FOPC Refresh',
          chartDataType: 'series',
          width: 130,
          minWidth: 130,
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          // cellRendererFramework: IconRenderer,
          cellStyle: function(params) {
            return params.value.toLowerCase() == 'success'
              ? {
                  color: '#008000',
                  border: ' 0px white',
                  fontWeight: 'bold',
                  textAlign: 'left'
                }
              : {
                  color: '#ff0000',
                  border: ' 0px white',
                  fontWeight: 'bold',
                  textAlign: 'left'
                };
          },
          // cellRenderer: function (result) {
          //   var closeddate = JSON.stringify(result.value);
          //   return (
          //     "<a href='#' onclick='navigateToRoDetails(" +
          //     closeddate +
          //     ")'>" +
          //     result.value +
          //     "</a>"
          //   );
          // },
          field: 'dashboardStatus'
          // valueFormatter: this.formatCellValueDate,
          // filter: 'agDateColumnFilter',
          // filterParams: {
          //   browserDatePicker: true,
          //   comparator: function(filterLocalDateAtMidnight, cellValue) {
          //     var dateAsString = moment(cellValue).format('YYYY-MM-DD');

          //     if (dateAsString == null) return -1;

          //     var dateParts = dateAsString.split('-');
          //     var cellDate = new Date(
          //       Number(dateParts[0]),
          //       Number(dateParts[1]) - 1,
          //       Number(dateParts[2])
          //     );

          //     if (filterLocalDateAtMidnight.getTime() == cellDate.getTime()) {
          //       return 0;
          //     }

          //     if (cellDate < filterLocalDateAtMidnight) {
          //       return -1;
          //     }

          //     if (cellDate > filterLocalDateAtMidnight) {
          //       return 1;
          //     }
          //   }
          // }
        },
        {
          headerName: 'Attempt',
          chartDataType: 'series',
          width: 128,
          minWidth: 128,
          field: 'attempt',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          // valueFormatter: this.formatCellValueDate,
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              // left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Reason',
          chartDataType: 'series',
          width: 178,
          minWidth: 178,
          field: 'logError',
          suppressMenu: true,
          tooltipField: 'logError',
          unSortIcon: true,
          editable: false,
          cellStyle: function() {
            return {
              color: '#000000',
              border: ' 0px white',
              textAlign: 'left'
            };
          },
          tooltipField: 'logError'
          // filterParams: {
          //   popupParent: document.body,
          //   suppressFilterButton: true,
          //   debounceMs: 300,
          //   valueFormatter: params => {
          //     const value = params.value || '';
          //     return value.length > 20 ? `${value.substring(0, 20)}...` : value;
          //   },
          //   cellRenderer: params => {
          //     const value = params.value || '';
          //     const div = document.createElement('div');
          //     div.textContent =
          //       value.length > 20 ? `${value.substring(0, 20)}...` : value;
          //     return div;
          //   }
          // }
        }
        // {
        //   headerName: 'Attempt Time(EST)',
        //   chartDataType: 'series',
        //   width: 130,
        //   minWidth: 130,
        //   field: 'attempttime',
        //   suppressMenu: true,
        //
        //   unSortIcon: true,
        //   editable: false,
        //   // valueFormatter: this.formatCellValueMonthYear,
        //   cellStyle: function() {
        //     return {
        //       color: '#000000',
        //       // fontWeight: 'bold',
        //       // left: '0px',
        //       // cursor: 'pointer',
        //       border: ' 0px white',
        //       textAlign: 'left'
        //     };
        //   }
        //   // hide: true
        // },
        // {
        //   headerName: 'Store Id',
        //   chartDataType: 'series',
        //   width: 130,
        //   minWidth: 130,
        //   field: 'storeId',
        //   suppressMenu: true,
        //
        //   unSortIcon: true,
        //   cellStyle() {
        //     return { border: ' 0px white', textAlign: 'center' };
        //   }
        //   // rowGroup: true
        // },
        // {
        //   headerName: 'Store Name',
        //   chartDataType: 'series',
        //   width: 250,
        //   minWidth: 250,
        //   field: 'storeName',
        //
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle() {
        //     return { border: ' 0px white' };
        //   }
        //   // rowGroup: true
        // },
      ],
      groupIncludeTotalFooter: true,
      //autoGroupColumnDef: { minWidth: 200 },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,
      // frameworkComponents: {
      //   IconRenderer: IconRenderer
      // },
      rowData: [],
      overlayNoRowsTemplate:
        '<span style="padding: 10px; margin-top:50px;">No rows to show</span>',
      defaultColDef: {
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
        // editable: true
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/DD/YY');
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != 0) {
      return moment(params.value).format('MM/YY');
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        (
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString()
      );
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        Math.abs(
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
      );
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };

  onGridReady = params => {
    // params.api.closeToolPanel();
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;

    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridcolumnApi: params.columnApi });

    // this.gridApi.sizeColumnsToFit();
    this.getAgGridData(this.state.value);
  };

  getAgGridData(statusDate) {
    var startDate = moment(statusDate).format('YYYY-MM-DD');
    var fromdate = moment(Date.now() - 9 * 24 * 3600 * 1000).format(
      'YYYY-MM-DD'
    );
    this.setState({ isLoading: true });
    var timezoneOffset = '+05:30';
    const start = new Date();
    const client = makeApolloClientPostgres;
    console.log('result data=111');
    client
      .mutate({
        mutation:
          localStorage.getItem('versionFlag') == 'TRUE'
            ? GET_DAILY_DATA_LOAD
            : GET_DAILY_DATA_LOAD_OLD,
        variables: {
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          timezoneOffset: timezoneOffset,
          fromdate: fromdate,
          todate: startDate
        }
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/DailyDataImports',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_DAILY_DATA_LOAD',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        console.log(
          'result data=',
          result.data
            .statelessCcSupportAndNotificationsGetDailyDataloadStatusWithDaterange
            .dailyDataloadStatuses
        );
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessCcSupportAndNotificationsGetDailyDataloadStatusWithDaterange
            .dailyDataloadStatuses
        ) {
          var resultArr =
            result.data
              .statelessCcSupportAndNotificationsGetDailyDataloadStatusWithDaterange
              .dailyDataloadStatuses;
          this.setState({
            rowData: resultArr
          });

          // this.setState({
          //   value: [startDate, endDate]
          // });
        }
      })
      .catch(error => {});
  }

  nulValue = () => {
    return '';
  };
  renderBackButton = () => {
    {
      let chartId = this.props.chartId
        ? this.props.chartId
        : this.props.history.location.search.split('=')[1];
      let data = {
        month_year: this.state.queryMonth,
        type: '',
        //chartId: this.props.chartId ? this.props.chartId : this.props.history.location.search.split('=')[1],
        chartId: this.props.chartId,
        history: this.props.history,
        prevPath: this.state.previousLocation,
        drillDown:
          chartId == 1111
            ? 41
            : chartId == 1115
            ? 42
            : chartId == 1232
            ? 45
            : chartId == 1165
            ? 44
            : 43
      };
      this.props.parentCallback(data);
    }
  };
  externalFilterChanged = () => {
    //ageType = 'test';

    this.getAgGridData(this.state.value);
    this.state.rawGridApi.onFilterChanged();
  };
  isExternalFilterPresent = () => {
    return this.state.value;
  };

  doesExternalFilterPass = node => {
    if (node.alreadyRendered == false) {
      if (this.state.value != '') {
        let filteredUsers = this.state.rowData.filter(
          user =>
            moment(this.state.value).format('YYYY/MM/DD') <= user.logindate
        );
        this.setState({
          rowData: filteredUsers
        });
        return (
          node.data.closeddate >= moment(this.state.value).format('YYYY/MM/DD')
        );
      }
    }
  };
  dateChanage = e => {
    this.setState({ value: e });
    this.getAgGridData(e);
    this.state.rawGridApi.onFilterChanged();
  };
  resetReportGrid = () => {
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
    this.setState({
      queryMonth: this.state.queryMonth
    });
    window.sortState = {};
    window.filterState = {};
    this.state.gridcolumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'DailyDataImports',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Daily Data Imports' },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: cell => {
        if (
          cell.column.getColId().includes('startdate') ||
          cell.column.getColId().includes('enddate') ||
          cell.column.getColId().includes('loaddate')
        ) {
          return isValidDate(cell.value)
            ? moment(cell.value).format('MM/DD/YY')
            : cell.value;
        }
        return cell.value; // for other fields, return the original value
      }
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  render() {
    const { classes } = this.props;

    return (
      <div>
        <Paper square style={{ margin: 8 }}>
          <Tabs
            value={this.state.tabSelection}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
            style={{ pointerEvents: 'none' }}
          >
            <Tab
              style={{
                textTransform: 'none',
                paddingRight: 182,
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
              label={<div>Daily Data Imports</div>}
              value="one"
            />
          </Tabs>
        </Paper>

        <Grid container spacing={12}>
          <Grid item xs={12} style={{ padding: '8px' }}>
            <Paper square justify="center">
              <FormControl
                variant="outlined"
                margin="dense"
                className={classes.formControl}
              >
                {/* <Typography style={{ fontSize: 12 }} sx={{ mt: 2, mb: 1 }}>
                  Select Date */}
                <Typography
                  style={{
                    fontWeight: 'bold',
                    color: '#003d6b',
                    fontSize: 13,
                    marginTop: 4
                  }}
                >
                  This displays the results of the last 10 data downloads.{' '}
                </Typography>
                {/* </Typography> */}
                {/* <DatePicker
                  selected={this.state.value}
                  className={classes.reactDaterangePicker}
                  onChange={this.dateChanage}
                  maxDate={new Date()}
                  minDate={addDays(new Date(), -9)}
                  dateFormat="MM/dd/yy"
                /> */}
              </FormControl>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.back, 'reset-btn')}
                onClick={this.resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{
                    marginRight: 10,
                    float: 'right',
                    marginTop: 8,
                    cursor: 'pointer'
                  }}
                  onClick={this.onBtExport}
                >
                  <ExportIcon />
                </Link>
              </Tooltip>
            </Paper>
          </Grid>
        </Grid>
        {this.state.isLoading && (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div
          // id="data-tab-user-login"
          className="ag-theme-balham"
          style={{
            height: window.innerHeight - 210 + 'px',
            // height: '100%',
            // height:
            // (this.state.rowData.length > 0
            //   ? window.innerHeight - ((this.state.rowData.length*-20)+540)
            //   : window.innerHeight - 500) + 'px',
            width: '1089px',
            margin: 8,
            display:
              this.state.isLoading == true || this.state.tabSelection == 'two'
                ? 'none'
                : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              // height: '100%',
              width: '100%',
              height: '410px'
            }}
            // domLayout="autoHeight"
            // isExternalFilterPresent={this.isExternalFilterPresent}
            // doesExternalFilterPass={this.doesExternalFilterPass}
            floatingFilter={true}
            defaultColDef={this.state.defaultColDef}
            headerHeight={this.state.headerHeight}
            // sideBar={this.state.sideBar}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            tooltipShowDelay={0}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
            excelStyles={this.state.excelStyles}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
            // frameworkComponents={this.state.frameworkComponents}
          />
        </div>
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
    height: 30
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    border: 'thin solid #968989 !important',
    height: '35px !important'
  },
  reactDaterangePicker: {
    width: '80px !important',
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px'
  },
  back: {
    marginRight: 30,
    float: 'right',
    marginTop: 8
  }
});

export default withStyles(styles)(DailyDataImports);
