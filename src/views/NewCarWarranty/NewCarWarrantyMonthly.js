import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Toolbar,
  Grid,
  Tabs,
  Tab,
  Button
} from '@material-ui/core';
import clsx from 'clsx';
import RestoreIcon from '@material-ui/icons/Restore';
import Link from '@material-ui/core/Link';
import ExportIcon from '@material-ui/icons/GetApp';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { getNewCarWarrantyMonthly } from 'src/utils/hasuraServices';
import moment from 'moment';
import ReferenceTopbar from 'src/components/charts/ReferenceTopbar';
import NewCarWarrantyOverall from './NewCarWarrantyOverall';
import NewCarWarrantySixMonths from './NewCarWarrantySixMonths';
import { withStyles } from '@material-ui/styles';
import { ReactSession } from 'react-client-session';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
class NewCarWarrantyMonthly extends React.Component {
  // componentDidUpdate(prevProps) {
  //   console.log("datas=",prevProps.session.storeSelected ,JSON.parse(localStorage.getItem('selectedStoreId')));
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData();
  //     }
  //   }
  // }
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.setState({ serviceAdvisors: ReactSession.get("serviceAdvisors") });
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    let realm = localStorage.getItem('realm');
    this.state = {
      exportGrid: false,
      setReload: false,
      resetLayout: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      showRefresh: 'none',
      disableTable: 'all',
      rawGridApi: {},
      tabSelection: 'one',
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      columnDefs: [
        {
          headerName: 'Month Year',
          chartDataType: 'series',
          field: 'monthYear',
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          width: 90,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          }
        },
        {
          headerName: 'Make',
          chartDataType: 'series',
          field: 'make',
          cellClass: 'textAlign',
          width: realm === 'chevyland' ? 160 : 120,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type Group',
          chartDataType: 'series',
          field: 'paytypegroup',
          cellClass: 'textAlign',
          width: 130,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'RO Count',
          chartDataType: 'series',
          width: 110,
          field: 'rocount',
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueCount,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Total RO Count',
          chartDataType: 'series',
          width: 130,
          field: 'totalCount',
          cellClass: 'textAlign',
          suppressMenu: true,

          unSortIcon: true,
          valueFormatter: this.formatCellValueCount,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Percentage',
          chartDataType: 'series',
          width: 102,
          field: 'percentage',
          cellClass: 'textAlign',
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          valueFormatter: this.formatCellValueGP,
          cellStyle() {
            return {
              textAlign: 'right',
              border: ' 0px white',
              left: '560px !important'
            };
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
        // editable: true
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueCount = params => {
    if (params.value != null && params.value != '') {
      return params.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      if (params.value == '100.00') {
        return '100%';
      } else
        return (
          parseFloat(params.value)
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
        );
    } else {
      return '0.0';
    }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.setState({ gridColumnApi: params.columnApi });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    getNewCarWarrantyMonthly(result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessDbdReferencesGetWarrantyCountPercentageMonthly
          .nodes
      ) {
        this.setState({
          rowData:
            result.data.statelessDbdReferencesGetWarrantyCountPercentageMonthly
              .nodes
        });
      }
    });
  }
  onBtExportMonthly = () => {
    let params = {
      sheetName: 'Report',
      columnWidth: 150,
      fileName: 'New Car Warranty - Monthly',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'New Car Warranty - Monthly' },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: params => {
        if (params.column.getColId() === 'percentage') {
          let formattedValue = params.value;
          // Check if the formatted value ends with .00
          if (formattedValue.endsWith('.00')) {
            return formattedValue.slice(0, -3) + '%'; // Remove .00 and add %
          } else {
            return formattedValue + '%'; // Add % without removing anything
          }
        }
        if (params.column.getColId() === 'monthYear') {
          if (params.value != null && params.value != 0) {
            return moment(params.value).format('MM/YY');
          }
        }
        // For other columns, return the value as is
        return params.value;
      }
    };
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onBtExportTwo = () => {
    this.setState({ exportGrid: true });
    // let params = {
    //   sheetName: 'Report',
    //   columnWidth: 150,
    //   fileName: 'New Car Warranty - Over Six Months',
    //   customHeader: [
    //     [],
    //     [
    //       {
    //         styleId: 'bigHeader',
    //         data: {
    //           type: 'String',
    //           value: 'New Car Warranty - Over Six Months'
    //         },
    //         mergeAcross: 3
    //       }
    //     ]
    //   ]
    // };

    // eslint-disable-next-line react/destructuring-assignment
    // this.state.rawGridApi.exportDataAsExcel(params);
  };
  onBtExportThree = () => {
    this.setState({ exportGrid: true });
    // const params = {
    //   sheetName: 'Report',
    //   columnWidth: 150,
    //   fileName: 'New Car Warranty - Overall',
    //   customHeader: [
    //     [],
    //     [
    //       {
    //         styleId: 'bigHeader',
    //         data: { type: 'String', value: 'New Car Warranty - Overall' },
    //         mergeAcross: 3
    //       }
    //     ]
    //   ]
    // };
    // this.state.rawGridApi.exportDataAsExcel(params);
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  resetReportGrid = () => {
    this.setState({ resetLayout: true });
  };
  resetReportGridMonth = () => {
    //this.setState({ resetLayout: true });
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.sizeColumnsToFit();
  };
  handleResetReport = () => {
    this.setState({ resetLayout: false });
  };
  handleExportGrid = () => {
    this.setState({ exportGrid: false });
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        {/* <div>
            <Paper square style={{ marginLeft: 8, marginRight: 8, height: 29 }}>
              <Toolbar>
                <Grid container justify="flex-start" style={{ padding: '5px' }}>
                  <Tooltip title="Export To Excel">
                    <Link
                      style={{
                        marginTop: -28,
                        marginLeft: 'auto',
                        cursor: 'pointer'
                      }}
                      onClick={this.onBtExport}
                    >
                      <ExportIcon />
                    </Link>
                  </Tooltip>
                </Grid>
              </Toolbar>
            </Paper>
          </div> */}

        {/* {this.state.tabSelection == 'one' ? (
          <Paper square style={{ margin: '0px 8px' }}>
            <ReferenceTopbar
              props={this.props}
              title={'New Car Warranty - Monthly'}
              exportReport={this.onBtExportMonthly}
              setResetDashboard={this.resetReportGrid}
            />
          </Paper>
        ) : null}
        {this.state.tabSelection == 'two' ? (
          <Paper square style={{ margin: '0px 8px' }}>
            <ReferenceTopbar
              props={this.props}
              title={'New Car Warranty - Over Six Months'}
              exportReport={this.onBtExportTwo}
              setResetDashboard={this.resetReportGrid}
            />
          </Paper>
        ) : null}
        {this.state.tabSelection == 'three' ? (
          <Paper square style={{ margin: '0px 8px' }}>
            <ReferenceTopbar
              props={this.props}
              title={'New Car Warranty - Overall'}
              exportReport={this.onBtExportThree}
              setResetDashboard={this.resetReportGrid}
            />
          </Paper>
        ) : null} */}
        <Paper square style={{ margin: 8 }}>
          <Grid
            container
            className={clsx(classes.titleContainer, 'reset-dashboard')}
            style={{
              textTransform: 'none',
              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B'
            }}
          >
            <Grid item xs={9} style={{ display: 'flex', alignItems: 'right' }}>
              <Typography
                variant="h6"
                color="primary"
                className={clsx(classes.mainTitle, 'main-title')}
              >
                {this.state.tabSelection == 'one'
                  ? 'New Car Warranty - Monthly'
                  : this.state.tabSelection == 'two'
                  ? 'New Car Warranty - Over Six Months'
                  : this.state.tabSelection == 'three'
                  ? 'New Car Warranty - Overall'
                  : ''}
              </Typography>
            </Grid>
            <Grid
              item
              xs={3}
              style={{
                display: 'flex',
                justifyContent: 'end',
                // paddingLeft: '10%',
                marginTop: 7,
                marginLeft: -9
              }}
            >
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{ paddingRight: 8, cursor: 'pointer', marginTop: 3 }}
                  onClick={
                    this.state.tabSelection == 'one'
                      ? this.onBtExportMonthly
                      : this.state.tabSelection == 'two'
                      ? this.onBtExportTwo
                      : this.state.tabSelection == 'three'
                      ? this.onBtExportThree
                      : ''
                  }
                >
                  <ExportIcon />
                </Link>
              </Tooltip>
              <Button
                variant="contained"
                id="reset-layout"
                className={clsx(classes.back, 'reset-btn')}
                // onClick={this.resetReportGrid}
                onClick={
                  this.state.tabSelection == 'one'
                    ? this.resetReportGridMonth
                    : this.resetReportGrid
                }
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </Grid>
          </Grid>
        </Paper>
        <Paper
          square
          style={
            this.state.tabSelection == 'one'
              ? { margin: 8 }
              : {
                  margin: 8
                }
          }
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
            TabIndicatorProps={{ style: { display: 'none' } }}
          >
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>Monthly</div>}
              value="one"
              className={
                this.state.tabSelection == 'one' ? classes.tabSelected : null
              }
            />
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>Over Six Months</div>}
              value="two"
              className={
                this.state.tabSelection == 'two' ? classes.tabSelected : null
              }
            />
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>Overall</div>}
              value="three"
              className={
                this.state.tabSelection == 'three' ? classes.tabSelected : null
              }
            />
          </Tabs>
        </Paper>
        {this.state.tabSelection == 'three' ? (
          <NewCarWarrantyOverall
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            session={this.props.session}
            exportReport={this.state.exportGrid}
            handleExportGrid={this.handleExportGrid}
          />
        ) : null}
        {this.state.tabSelection == 'two' ? ( //
          <NewCarWarrantySixMonths
            resetReport={this.state.resetLayout}
            handleResetReport={this.handleResetReport}
            exportReport={this.state.exportGrid}
            handleExportGrid={this.handleExportGrid}
          />
        ) : null}
        {this.state.isLoading === true ? (
          <Box style={{ padding: 25 }}>
            <LinearProgress color="secondary" />
            <Typography
              variant="h6"
              align="center"
              style={{ padding: 25 }}
              color="primary"
            >
              Processing...
            </Typography>
          </Box>
        ) : null}
        {this.state.tabSelection == 'one' ? (
          <div
            id="data-tab"
            className="ag-theme-balham"
            style={{
              height: `${window.innerHeight - 250}px`,
              width: '670px',
              alignContent: 'center',
              marginLeft: '8px',
              display:
                this.state.tabSelection === 'two' ||
                this.state.tabSelection === 'three'
                  ? 'none'
                  : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader
              rowData={this.state.rowData}
              excelStyles={this.state.excelStyles}
              tooltipShowDelay={0}
              floatingFilter={true}
              enableRangeSelection={true}
              animateRows={true}
              enableCharts={true}
              suppressRowClickSelection={true}
              suppressHorizontalScroll={true}
              suppressDragLeaveHidesColumns={true}
              suppressContextMenu={true}
            />
          </div>
        ) : null}
      </div>
    );
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  mainTitle: {
    marginLeft: '51% !important',
    marginTop: '10px !important'
  },
  back: {
    marginTop: 2
  }
});
export default withStyles(styles)(NewCarWarrantyMonthly);
