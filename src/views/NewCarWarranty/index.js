import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useDispatch, useSelector } from 'react-redux';
import NewCarWarrantyMonthly from './NewCarWarrantyMonthly';
import { Redirect } from 'react-router-dom';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function NewCarWarranty() {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  return (
    <Page className={classes.root} title="New Car Warranty">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <NewCarWarrantyMonthly session={session} />
      )}
    </Page>
  );
}

export default NewCarWarranty;
