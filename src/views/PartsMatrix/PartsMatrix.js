import React, { useState, useEffect, useCallback } from 'react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { makeStyles } from '@material-ui/core/styles';
import Accordion from '@material-ui/core/Accordion';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import moment from 'moment';
import RestoreIcon from '@material-ui/icons/Restore';
import {
  Grid,
  Typography,
  Paper,
  FormControl,
  Button,
  Tooltip,
  Tab,
  Tabs,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress
} from '@material-ui/core';
import {
  getMatrixType,
  getMatrixDetail,
  getGridorMatrixPeriods,
  getGridorMatrixPayTypesByGridType
} from 'src/utils/hasuraServices';
import { setMenuSelected, setNavItems } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import Page from 'src/components/Page';
import { i } from 'react-dom-factories';
var lodash = require('lodash');

const Dealer = process.env.REACT_APP_DEALER;
const useStyles = makeStyles(theme => ({
  root: {
    width: '99%',
    marginLeft: 8,
    maxHeight: 700,
    overflowY: 'auto'
  },
  text: {
    fontSize: 13
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  matrixType: {
    marginTop: -42,
    marginLeft: 150
  },
  back: {
    marginRight: 5,
    float: 'right',
    width: '7%'
  },
  loaderGrid: {
    marginLeft: 450,
    marginTop: 120
  },
  header: {
    textTransform: 'none',
    fontSize: 13,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  partsMatrixS: {
    '@media (min-width: 2305px)': {
      height: 'auto'
    },
    '@media (min-width: 2560px)': {
      height: 'auto'
    },
    '@media (max-width: 2304px)': {
      height: 'auto'
    },
    '@media (max-width: 1920px)': {
      height: 'auto'
    },
    '@media (max-width: 1440px)': {
      height: 'auto'
    }
  },
  partSources: {
    fontSize: 13,
    '@media (min-width: 2305px)': {
      width: '53%'
    },
    '@media (min-width: 2560px)': {
      width: '80%'
    },
    '@media (max-width: 2304px)': {
      width: '60%'
    },
    '@media (max-width: 1920px)': {
      width: '70%'
    },
    '@media (max-width: 1440px)': {
      width: '70%'
    }
  },
  partSourcesMain: {
    marginTop: -25,
    width: '90%',
    '@media (min-width: 2305px)': {
      marginLeft: 15
    },
    '@media (min-width: 2560px)': {
      marginLeft: 22
    },
    '@media (max-width: 2304px)': {
      marginLeft: 22
    },
    '@media (max-width: 1920px)': {
      marginLeft: 12
    },
    '@media (max-width: 1440px)': {
      marginLeft: 22
    }
  },
  partSourcesMainList: {
    marginTop: -25,
    width: '90%',
    '@media (min-width: 2305px)': {
      marginLeft: 90
    },
    '@media (min-width: 2560px)': {
      marginLeft: 22
    },
    '@media (max-width: 2304px)': {
      marginLeft: 90
    },
    '@media (max-width: 1920px)': {
      marginLeft: 87
    },
    '@media (max-width: 1440px)': {
      marginLeft: 22
    }
  }
}));

export default function PartsMatrix(props) {
  let showAllJob =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.showAllJobs
      ? props.history.location.state.showAllJobs
      : false;
  let payTypeSelected =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.payType
      ? props.history.location.state.payType
      : props.selectedPayType;
  if (payTypeSelected == 'C') {
    payTypeSelected = 'Customer';
  } else if (payTypeSelected == 'I') {
    payTypeSelected = 'Internal';
  }
  let selectedSource =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.partsSource != ''
      ? props.history.location.state.partsSource
      : '';
  let selectedGridValue =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    (props.history.location.state.selectedGrid ||
      props.history.location.state.selectedGrid == '')
      ? props.history.location.state.selectedGrid
      : 1;
  let selectedPageType =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.pageType != ''
      ? props.history.location.state.pageType
      : '';
  let matrixDat =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.matrixDate
      ? props.history.location.state.matrixDate
      : '';
  let matrixTypeSelected =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.matrixType
      ? props.history.location.state.matrixType
      : '';

  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const [matrixDate, setMatrixDate] = React.useState(
    matrixDat ? matrixDat : ''
  );
  const [showAllJobs, setShowAllJobs] = React.useState(
    showAllJob ? showAllJob : false
  );
  const [expanded, setExpanded] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [type, setType] = React.useState('paytype_matrix');
  const [partSrc, setPartSrc] = React.useState(
    selectedSource ? selectedSource : null
  );
  const [payType, setPayType] = React.useState(
    selectedMatrixType ? selectedMatrixType : null
  );
  const [matrixTypeData, setMatrixTypeData] = React.useState([]);
  const [selectedMatrixType, setSelectedMatrixType] = React.useState(
    matrixTypeSelected
  );
  const [matrixData, setMatrixData] = React.useState([]);
  const [gridPeriods, setGridPeriods] = React.useState([]);
  const [selectedGrid, setSelectedGrid] = React.useState(
    selectedGridValue ? selectedGridValue : ''
  );
  const [partsDetail, setPartsDetail] = React.useState([]);
  const [rowData, setRowData] = useState([]);
  const [matrixDataAll, setMatrixDataAll] = React.useState([]);
  const [partsForData, setPartsForData] = useState([]);
  const [selectedPartsFor, setSelectedPartsFor] = useState('');

  const [overlayNoRowsTemplate, setOverlayNoRowsTemplate] = useState(
    '<span style="padding: 10px;margin-top:50px; ">No rows to show</span>'
  );
  const [columnDefs, setColumnDefs] = useState([
    {
      headerName: 'From',
      chartDataType: 'series',
      minWidth: 80,
      field: 'col0OrpriceStartRange',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      valueFormatter: params => formatCellValue(params),
      flex: 1,
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'To',
      chartDataType: 'series',
      minWidth: 80,
      field: 'col1OrpriceEndRange',
      valueFormatter: params => formatCellValue(params),
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Base',
      chartDataType: 'series',
      minWidth: 80,
      field: 'calcBase',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Break Field',
      chartDataType: 'series',
      minWidth: 80,
      field: 'breakField',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Percentage',
      field: 'col2OraddPercentage',
      minWidth: 80,
      valueFormatter: params => formatCellValueGP(params),
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      cellClass: 'textAlign',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return {
          border: ' 0px white',
          textAlign: 'left'
        };
      }
    }
  ]);

  const [defaultColDef, setDefaultColDef] = useState({
    enableValue: true,
    sortable: true,
    filter: true,
    filter: 'agSetColumnFilter',
    filterParams: {
      applyMiniFilterWhileTyping: true
    },
    resizable: false,
    suppressMovable: false
  });

  useEffect(() => {
    dispatch(setMenuSelected('Parts Matrix(s)'));
    dispatch(setNavItems(['Reference / Setups']));
  }, []);

  useEffect(() => {
    setIsLoading(true);
    setMatrixTypeData([]);
    setMatrixData([]);
    matrixType(type, partSrc, payType);
  }, [session.storeSelected]);
  const matrixType = () => {
    setIsLoading(true);
    getMatrixType(type, partSrc, payType, null, result => {
      let resultArr = [];
      let partsForData = '';
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val.gridormatrixtype);
        }
      );
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes &&
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
          .length > 0
      ) {
        let partsFor = lodash
          .uniqBy(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes,
            'gridorpartsfor'
          )
          .map(obj => obj);
        setPartsForData(partsFor);
        if (
          props.history &&
          props.history.location &&
          props.history.location.state &&
          props.history.location.state.selectedPartsFor
        ) {
          partsForData = props.history.location.state.selectedPartsFor;
        } else {
          partsForData = partsFor[0].gridorpartsfor;
        }
        setSelectedPartsFor(partsForData);
        if (partsForData != '') {
          getGridorMatrixPayTypesByGridType(
            'paytype_matrix',
            partsForData,
            '',
            result => {
              let data = result;
              if (selectedMatrixType == '') {
                setSelectedMatrixType(data[0]);
              }
              if (
                props.history &&
                props.history.location &&
                props.history.location.state &&
                props.history.location.state.pageType &&
                props.history.location.state.pageType == 'FleetAccounts' &&
                selectedMatrixType != ''
              ) {
                let data1 = data.filter(item => item == selectedMatrixType);
                setPartsForData(
                  partsFor.filter(item => item.gridorpartsfor == partsForData)
                );
                setMatrixTypeData(lodash.uniq(data1));
              } else {
                setMatrixTypeData(lodash.uniq(data));
              }
              matrixDetails(
                'matrix',
                null,
                selectedMatrixType != '' ? selectedMatrixType : data[0],
                partsForData
              );
            }
          );
        }
        // setSelectedMatrixType(
        //   result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
        //     .nodes[0].gridormatrixtype
        // );
        // const uniqueDataArray = [...new Set(resultArr)];
        // console.log('resultArr===', uniqueDataArray);
        // setMatrixTypeData(uniqueDataArray);
        // matrixDetails(
        //   'matrix',
        //   null,
        //   result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
        //     .nodes[0].gridormatrixtype
        // );
      } else {
        setIsLoading(false);
      }
    });
  };
  const matrixDetails = (type, partSource, payTypes, partsFor) => {
    let dataArr = [];
    let dataArrDoorRate = [];
    let resultArr = [];
    getMatrixType(type, payTypes, partSource, partsFor, result => {
      setIsLoading(false);
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
      ) {
        setMatrixDataAll(
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        );
        const data =
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes;
        const distinctGridOrders = [
          ...new Set(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
              item => item.gridOrder
            )
          )
        ];
        setGridPeriods(distinctGridOrders);
        if (matrixDate != '') {
          dataArrDoorRate = data.filter(item => item.createdDate == matrixDate);
          if (
            dataArrDoorRate.length > 0 &&
            selectedGrid == '' &&
            selectedSource == ''
          ) {
            console.log('enter=12');
            // const unique = [
            //   ...new Set(dataArrDoorRate.map(item => item.gridOrder))
            // ];
            // var min = Math.min(...unique);
            // this.setState({ selectedGrid: min });
            // var minSource = Math.min(...partSource);
            // this.setState({ selectedSource: minSource });
            // dataArr = dataArrDoorRate.filter(
            //   item => item.gridOrder == min && item.partSource == minSource
            // );
          } else if (data.length > 0 && selectedGrid) {
            console.log('enter=123', selectedGrid);
            setSelectedGrid(selectedGrid);

            // if (selectedSource == '') {
            //   var minSource = Math.min(...partSource);
            //   this.setState({ selectedSource: minSource });
            // }
            // partSource =
            //   selectedSource != ''
            //     ? selectedSource
            //     : minSource;
            // if (gridPeriods.includes(selectedGrid)) {
            //   dataArr = data.filter(
            //     item =>
            //       item.gridOrder == selectedGrid &&
            //       item.partSource == partSource
            //   );
            // } else {
            //   this.setState({ selectedGrid: gridPeriods[0] });
            //   dataArr = data.filter(
            //     item =>
            //       item.gridOrder == gridPeriods[0] &&
            //       item.partSource == partSource
            //   );
            // }
          } else if (
            dataArrDoorRate.length > 0 &&
            selectedGrid == '' &&
            selectedSource != ''
          ) {
            console.log('enter=1234', selectedGrid, dataArrDoorRate);

            dataArr = dataArrDoorRate.filter(
              item => item.createdDate == matrixDate
            );
            const unique = [...new Set(dataArr.map(item => item.gridOrder))];
            var min = Math.min(...unique);
            setSelectedGrid(min);
          }
          const resultArr =
            dataArr.length > 0
              ? filterUniqueArrays(dataArr, 'partSource')
              : data;
          if (selectedPageType == 'PartsMisses') {
            let filteredData = [];
            if (props.history.location.state.selectedPartsFor == partsFor) {
              filteredData = resultArr.filter(
                item =>
                  item.partSource.includes(selectedSource) ||
                  item.partSource.includes('All')
              );

              setMatrixData(filteredData);
            } else {
              filteredData = resultArr;
            }
            const unique = [
              ...new Set(filteredData.map(item => item.gridOrder))
            ];

            if (unique.length > 0) {
              var min = Math.min(...unique);
              setSelectedGrid(min);

              // if (props.history.location.state.selectedPartsFor != partsFor) {
              filteredData = filteredData.filter(item => item.gridOrder == min);
              let resultArr1 = filterUniqueArrays(filteredData, 'partSource');
              if (props.history.location.state.selectedPartsFor == partsFor) {
                resultArr1 = resultArr1.filter(
                  item =>
                    item.partSource.includes(selectedSource) ||
                    item.partSource.includes('All')
                );
              }
              setMatrixData(resultArr1);
              //  }
            }
          } else {
            if (
              props.history &&
              props.history.location.state &&
              props.history.location.state.pageType == 'FleetAccounts' &&
              matrixDate
            ) {
              let resultArr1 = resultArr.filter(
                item => item.createdDate == matrixDate
              );

              resultArr1 = filterUniqueArrays(resultArr1, 'partSource');
              const unique = [
                ...new Set(resultArr1.map(item => item.gridOrder))
              ];

              if (unique.length > 0) {
                var min = Math.min(...unique);
                setSelectedGrid(min);
                setGridPeriods(distinctGridOrders.filter(item => item == min));
              }

              setMatrixData(resultArr1);
            } else {
              const unique = [
                ...new Set(resultArr.map(item => item.gridOrder))
              ];
              var min = Math.min(...unique);
              setSelectedGrid(min);
              setMatrixData(resultArr);
            }
          }
        } else {
          setSelectedGrid(distinctGridOrders[0]);
          setMatrixData(
            data.filter(item => item.gridOrder === distinctGridOrders[0])
          );
          const resultArrary = data.filter(
            item => item.gridOrder === distinctGridOrders[0]
          );
          const resultArr = filterUniqueArrays(resultArrary, 'partSource');
          if (selectedPageType == 'PartsMisses') {
            const filteredData = resultArr.filter(
              item =>
                item.partSource.includes(selectedSource) ||
                item.partSource.includes('All')
            );
            setMatrixData(filteredData);
          } else {
            setMatrixData(resultArr);
          }
        }
        setIsLoading(false);
      }
    });
  };
  const resetReportGrid = () => {
    setExpanded(null);
    setSelectedGrid(1);
    setSelectedGrid(1);
  };
  const filterUniqueArrays = (arr, prop) => {
    const uniqueArrays = [];
    return arr.filter(obj => {
      const arrStr = JSON.stringify(obj[prop]);
      if (!uniqueArrays.includes(arrStr)) {
        uniqueArrays.push(arrStr);
        return true;
      }
      return false;
    });
  };
  const handleChange = panel => (event, isExpanded) => {
    setExpanded(null);
    setPartsDetail([]);
    getMatrixDetail(
      'matrix',
      panel.partSource,
      selectedMatrixType,
      panel.createdDate,
      panel.gridorpartsfor,
      result => {
        let resultArr = [];
        if (
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        ) {
          setPartsDetail(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes
          );
        }
      }
    );
    setExpanded(isExpanded ? panel : false);
  };
  const handleMatrixChange = e => {
    console.log('matrix==', e.target.value, matrixDataAll);
    setSelectedMatrixType(e.target.value);
    setIsLoading(true);
    getMatrixType(type, e.target.value, partSrc, selectedPartsFor, result => {
      let resultArr = [];
      result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
        val => {
          resultArr.push(val.gridormatrixtype);
        }
      );
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes &&
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
          .length > 0
      ) {
        const uniqueDataArray = [...new Set(resultArr)];
        console.log('resultArr===', uniqueDataArray);
        setMatrixTypeData(uniqueDataArray);
        matrixDetails(
          'matrix',
          null,
          e.target.value
          //result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
          // .nodes[0].gridormatrixtype
        );
      } else {
        setIsLoading(false);
      }
    });
    // const resultArrary = matrixDataAll.filter(
    //   item => item.gridOrder === event.target.value
    // );
    // const resultArr = filterUniqueArrays(resultArrary, 'partSource');
    // setMatrixData(resultArr);
  };
  const handleChangePeriod = event => {
    setSelectedGrid(event.target.value);

    const resultArrary = matrixDataAll.filter(
      item => item.gridOrder === event.target.value
    );
    const resultArr = filterUniqueArrays(resultArrary, 'partSource');
    setMatrixData(resultArr);
  };
  const formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '$0.00';
    }
  };
  const formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0%';
    }
  };
  const formatCellValueDate = params => {
    if (params != null && params != 0) {
      return moment(params).format('MM/DD/YY');
    }
  };

  const handleclickBackBtn = params => {
    props.history.push({
      pathname: '/FleetAccounts',
      state: {}
    });
  };
  const handleclick = params => {
    props.history.push({
      pathname:
        props.history.location.state &&
        props.history.location.state.pageType == 'FleetAccounts'
          ? '/FleetAccounts'
          : localStorage.getItem('versionFlag') == 'TRUE'
          ? '/PartsMisses'
          : '/PartsTargetMisses',
      state: {
        selectedFilter: props.history.location.state.selectedFilter,
        selectedToggle: props.history.location.state.selectedToggle,
        selectedMonthYear: props.history.location.state.selectedMonthYear,
        parent: props.history.location.state.parent,
        previousToggle: props.history.location.state.previousToggle,
        payType: props.history.location.state.payType,
        previousPayType: props.history.location.state.PrevPayType,
        previousGridType: props.history.location.state.PrevGridType,
        // showAllJobs: showAllJobs
        showAllJobs: props.history.location.state.showAllJobs
          ? props.history.location.state.showAllJobs
          : false,
        filterStart: props.history.location.state.filterStart,
        filterEnd: props.history.location.state.filterEnd,
        gridType:
          props.history.location.state.pageType != 'FleetAccounts'
            ? props.history.location.state.gridType
            : props.history.location.state.gridRateValueStart,
        selectedPayType: props.history.location.state.selectedPayType
          ? props.history.location.state.selectedPayType
          : '',
        customerName: props.history.location.state.customerName
          ? props.history.location.state.customerName
          : props.history.location.state.fleetName,
        selectedGridType: props.history.location.state.selectedGridType
      }
    });
  };
  const NumberFormat = number => {
    var nf = new Intl.NumberFormat();
    var value = nf.format(number);
    return value;
  };
  const handleChangeMatrixFor = event => {
    setSelectedPartsFor(event.target.value);
    getGridorMatrixPayTypesByGridType(
      'paytype_matrix',
      event.target.value,
      '',
      result => {
        if (result) {
          let data = result;
          setSelectedMatrixType(data[0]);
          setMatrixTypeData(lodash.uniq(data));
          matrixDetails('matrix', null, data[0], event.target.value);
        }
      }
    );
  };
  const toPascalCase = str => {
    return str
      .replace(/(\w)(\w*)/g, (match, p1, p2) => {
        return p1.toUpperCase() + p2.toLowerCase();
      })
      .replace(/\s+/g, '');
  };
  return (
    <>
      <Page title="Parts Matrix(s)">
        <div>
          <Paper
            square
            style={{
              margin: 8,

              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
            }}
          >
            <Tabs
              variant="fullWidth"
              indicatorColor="secondary"
              textColor="secondary"
              aria-label="icon label tabs example"
            >
              {props.history &&
                props.history.location.state &&
                (props.history.location.state.pageType == 'PartsMisses' ||
                  props.history.location.state.pageType == 'FleetAccounts') && (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={handleclick}
                    style={{ width: 'auto', top: '9px' }}
                    fullWidth={false}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                )}

              <Tab
                label={<div>Parts Matrix(s)</div>}
                value="one"
                style={{ pointerEvents: 'none', textTransform: 'none' }}
              />
              <Button
                variant="contained"
                id="reset-layout"
                style={{ paddingRight: 12, cursor: 'pointer', marginTop: 11 }}
                className={clsx(classes.back, 'reset-btn')}
                onClick={resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </Tabs>
          </Paper>
          <Paper
            square
            style={{
              margin: 8,
              marginLeft: 8,
              height: 50,
              paddingTop: 4,
              paddingLeft: 4
            }}
          >
            {matrixTypeData.length > 0 ? (
              <div>
                {/* {props.history &&
                  props.history.location.state &&
                  props.history.location.state.fleetName && (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      onClick={handleclickBackBtn}
                      style={{ width: 'auto', top: '9px' }}
                      fullWidth={false}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  )} */}
                <FormControl
                  margin="dense"
                  variant="outlined"
                  style={{
                    minWidth: 120,
                    marginLeft: 10,
                    paddingRight: 10
                  }}
                >
                  <InputLabel
                    htmlFor="outlined-age-native-simple"
                    margin="dense"
                    style={{ marginTop: -4 }}
                  >
                    Matrix Period
                  </InputLabel>
                  <Select
                    margin="dense"
                    variant="outlined"
                    label="Filter By"
                    name="duration"
                    className={'laborPartsGrid'}
                    value={selectedGrid}
                    onChange={handleChangePeriod}
                    MenuProps={{
                      getContentAnchorEl: null,
                      anchorOrigin: {
                        vertical: 'bottom',
                        horizontal: 'left'
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      }
                    }}
                  >
                    {gridPeriods.length > 0 ? (
                      gridPeriods.map(item => (
                        <MenuItem value={item}>
                          {item == 1 ? 'Current' : 'Prior ' + Number(item - 1)}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem value={1}>Current</MenuItem>
                    )}
                  </Select>
                </FormControl>
              </div>
            ) : null}

            <div className={classes.matrixType}>
              {partsForData.length > 0 && matrixTypeData.length > 0 ? (
                <div className="">
                  <FormControl
                    margin="dense"
                    variant="outlined"
                    style={{
                      minWidth: 120,
                      marginLeft: 10,
                      paddingRight: 10
                      // display:
                      //   this.state.payTypes.length > 0 ? 'inline-block' : 'none'
                    }}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ marginTop: -4 }}
                    >
                      Matrix Type
                    </InputLabel>
                    <Select
                      margin="dense"
                      variant="outlined"
                      label="Filter By"
                      name="duration"
                      className={'laborPartsGrid'}
                      value={selectedPartsFor}
                      onChange={handleChangeMatrixFor}
                      MenuProps={{
                        getContentAnchorEl: null,
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'left'
                        },
                        transformOrigin: {
                          vertical: 'top',
                          horizontal: 'left'
                        }
                      }}
                    >
                      {partsForData.map(item => (
                        <MenuItem value={item.gridorpartsfor}>
                          {item.dipsGridorpartsfor}
                          {/* {item.includes('cust_fleet')
                            ? 'Customer Fleet'
                            : item.includes('paytype_fleet')
                            ? 'Pay type Fleet'
                            : toPascalCase(item)} */}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                  <FormControl
                    margin="dense"
                    variant="outlined"
                    style={{
                      minWidth: 120,
                      marginLeft: 10,
                      paddingRight: 10
                    }}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ marginTop: -4 }}
                    >
                      Matrix For
                    </InputLabel>
                    <Select
                      margin="dense"
                      variant="outlined"
                      label="Filter By"
                      name="duration"
                      className={'laborPartsGrid'}
                      value={selectedMatrixType}
                      onChange={handleMatrixChange}
                      MenuProps={{
                        getContentAnchorEl: null,
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'left'
                        },
                        transformOrigin: {
                          vertical: 'top',
                          horizontal: 'left'
                        }
                      }}
                    >
                      {matrixTypeData.map(item => (
                        <MenuItem value={item}>{item}</MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </div>
              ) : (
                ''
              )}
            </div>
          </Paper>
          <div className={classes.root}>
            {isLoading ? (
              <Grid justify="center" className={classes.loaderGrid}>
                <CircularProgress size={60} />
              </Grid>
            ) : matrixData.length > 0 ? (
              matrixData.map(
                (item, index) => (
                  // index < 1 && (
                  <Accordion
                    key={item}
                    expanded={expanded === item}
                    onChange={handleChange(item)}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon />}
                      aria-controls="panel1bh-content"
                      id="panel1bh-header"
                      style={{ height: '90px' }}
                    >
                      <Grid container spacing={2}>
                        <Grid item xs={3}>
                          <Typography>
                            {' '}
                            <Typography
                              variant="subtitle1"
                              className={classes.header}
                            >
                              Store Install
                              Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            </Typography>
                            <Typography
                              variant="subtitle1"
                              className={classes.header}
                            >
                              FOPC Calculated Date
                              From&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            </Typography>
                          </Typography>
                          <Typography>
                            {' '}
                            <Typography
                              variant="subtitle1"
                              className={classes.text}
                            >
                              : {formatCellValueDate(item.storeInstallDate)}
                            </Typography>
                            <Tooltip title="This may be different from store install date if no prior pricing provided">
                              <Typography
                                variant="subtitle1"
                                className={classes.text}
                              >
                                : {formatCellValueDate(item.createdDate)}
                              </Typography>
                            </Tooltip>
                          </Typography>
                        </Grid>
                        <Grid item xs={9}>
                          <Typography>
                            {' '}
                            <Typography
                              variant="subtitle1"
                              className={classes.header}
                            >
                              Parts Source
                            </Typography>
                          </Typography>
                          {item.partSource.length > 21 ? (
                            <Typography className={classes.partSourcesMainList}>
                              {' '}
                              <Typography
                                variant="subtitle1"
                                className={classes.partSources}
                              >
                                {' '}
                                {item.partSource.length > 0 &&
                                  item.partSource.map((items, indexs) => (
                                    <>
                                      {indexs == 0 ? ':       ' : ''}
                                      {indexs < 21 ? (
                                        <Typography className={classes.text}>
                                          {items}
                                          {item.partSource.length == indexs + 1
                                            ? ' '
                                            : ',\u00A0'}
                                        </Typography>
                                      ) : (
                                        <>
                                          {indexs % 21 == 0 ? (
                                            <>
                                              <Typography
                                                className={classes.text}
                                              >
                                                {items}
                                                {item.partSource.length ==
                                                indexs + 1
                                                  ? ' '
                                                  : ',\u00A0'}
                                              </Typography>
                                            </>
                                          ) : (
                                            <Typography
                                              className={classes.text}
                                            >
                                              {items}
                                              {item.partSource.length ==
                                              indexs + 1
                                                ? ' '
                                                : ',\u00A0'}
                                            </Typography>
                                          )}
                                        </>
                                      )}
                                    </>
                                  ))}
                              </Typography>
                            </Typography>
                          ) : (
                            <Typography className={classes.partSourcesMain}>
                              {' '}
                              <Typography
                                variant="subtitle1"
                                className={classes.partSources}
                              >
                                {' '}
                                {item.partSource.length > 0 &&
                                  item.partSource.map((items, indexs) => (
                                    <>
                                      {indexs == 0 ? ':       ' : ''}
                                      {indexs < 21 ? (
                                        <Typography className={classes.text}>
                                          {items}
                                          {item.partSource.length == indexs + 1
                                            ? ' '
                                            : ',\u00A0'}
                                        </Typography>
                                      ) : (
                                        <>
                                          <Typography className={classes.text}>
                                            {items}
                                            {item.partSource.length ==
                                            indexs + 1
                                              ? ' '
                                              : ',\u00A0'}
                                          </Typography>
                                        </>
                                      )}
                                    </>
                                  ))}
                              </Typography>
                            </Typography>
                          )}
                        </Grid>
                      </Grid>
                    </AccordionSummary>
                    <AccordionDetails>
                      {partsDetail.length > 0 ? (
                        <div
                          className={clsx(
                            classes.dataGrid,
                            'ag-theme-balham fleet-container',
                            classes.partsMatrixS
                          )}
                          style={{
                            width: 800,
                            height: window.innerHeight - 400 + 'px',
                            alignContent: 'center',
                            marginLeft: '8px',
                            marginTop: 20
                          }}
                        >
                          <AgGridReact
                            className="ag-theme-balham"
                            style={{
                              // height: 500,
                              width: '60%'
                            }}
                            // domLayout="autoHeight"
                            floatingFilter={true}
                            defaultColDef={defaultColDef}
                            modules={AllModules}
                            columnDefs={columnDefs}
                            rowData={partsDetail}
                            tooltipShowDelay={0}
                            enableRangeSelection={true}
                            animateRows={true}
                            enableCharts={true}
                            overlayNoRowsTemplate={overlayNoRowsTemplate}
                            suppressDragLeaveHidesColumns={true}
                            suppressNoRowsOverlay={false}
                            suppressContextMenu={true}
                          />
                        </div>
                      ) : (
                        <div
                          className={clsx(
                            classes.dataGrid,
                            'ag-theme-balham fleet-container'
                          )}
                          style={{
                            width: 600,
                            height: window.innerHeight - 505 + 'px',
                            alignContent: 'center',
                            marginLeft: '8px',
                            marginTop: 10
                          }}
                        >
                          <Grid justify="center" className={classes.loaderGrid}>
                            <CircularProgress size={50} />
                          </Grid>
                        </div>
                      )}
                    </AccordionDetails>
                  </Accordion>
                )
                // )
              )
            ) : (
              <Accordion>
                <AccordionSummary style={{ height: '90px' }}>
                  <Grid container spacing={1}>
                    <Grid item xs={12}>
                      {' '}
                      <Typography>
                        Parts matrix data is not available.
                      </Typography>
                    </Grid>
                  </Grid>
                </AccordionSummary>
              </Accordion>
            )}
          </div>
        </div>
      </Page>
    </>
  );
}
