import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  Paper,
  Divider,
  Tab,
  Tabs,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
  Grid
} from '@material-ui/core';
import clsx from 'clsx';
import 'src/styles.css';
import { withStyles } from '@material-ui/styles';
import DashboardActions from 'src/components/DashboardActions';
import DashboardLineRenderer from 'src/components/charts/DashboardLineRenderer';
import DashboardCompare from 'src/components/charts/DashboardCompare';
import {
  getDataGridConfiguration,
  getLayoutConfiguration,
  saveLayoutConfiguration,
  getAllChartDetails
} from '../../utils/Utils';
import { withKeycloak } from '@react-keycloak/web';
import { CubeProvider } from '@cubejs-client/react';
import cubejsApi from 'src/utils/cubeUtils';
import DashboardDetailsActions from 'src/components/DashboardDetailsActions';
import PageHeader from 'src/components/PageHeader';
var lodash = require('lodash');

const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 35,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      filters: 2,
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-4') || {}
        )
      ),
      chartList: JSON.parse(global.localStorage.getItem('chart-master'))
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }

  redirectToHome = () => {
    const { history } = this.props;
    let searchText = history.location.search
      .split('?title=')
      .pop()
      .split('?')[0];
    {
      //history.push(this.props.history.location.SelectedLocation)
      // history.push(localStorage.getItem('selectedLocation'));
      history.push('/CPOverview');
    }
  };

  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }
    return this.state.layout;
  };
  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-4');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }

  render() {
    // let realm = this.props.keycloak.realm;
    const { classes } = this.props;
    let filteredResult = this.state.chartList.filter(
      item => item.dbdName == 'CP Overview' && item.parentId == null
    );
    let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');

    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          <PageHeader
            title={'CP Overview'}
            setResetDashboard={this.setResetDashboard}
            redirectHome={this.redirectToHome}
            isFrom={'compare_store'}
          />
          <Divider />
          <ReactGridLayout
            {...this.props}
            layout={this.state.layout}
            onLayoutChange={this.onLayoutChange}
            isResizable={false}
          >
            {orderedData.map((item, index) => {
              return (
                <div
                 // className={clsx(classes.container, 'diagram-section')}
                  className={clsx('diagram-section')}
                  key={index}
                  data-grid={getDataGridConfiguration(index)}
                >
                  <CubeProvider cubejsApi={cubejsApi()}>
                    <DashboardCompare
                      chartId={Number(item.chartId)}
                      chartName={item.chartName}
                    />
                  </CubeProvider>
                </div>
              );
            })}
          </ReactGridLayout>
        </Paper>
      </div>
    );
  }
}
const styles = theme => ({
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  root: {
    flexGrow: 1,
    width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    //justifyContent: 'space-between',
    width: '100%',
    boxShadow: 'none !important'
  }
});

export default withStyles(styles)(withKeycloak(Charts));
