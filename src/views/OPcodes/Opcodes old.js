/* eslint-disable react/default-props-match-prop-types */
/* eslint-disable react/jsx-no-duplicate-props */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-shadow */
/* eslint-disable react/static-property-placement */
/* eslint-disable no-unused-vars */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';
import clsx from 'clsx';
import RestoreIcon from '@material-ui/icons/Restore';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
// eslint-disable-next-line import/no-extraneous-dependencies
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  UPDATE_OPCODE,
  ADD_REFRESH_STATUS,
  UPDATE_REFRESH_STATUS,
  CLIENT_AUDITS,
  MV_REFRESH_STATUS
} from 'src/graphql/queries';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import {
  getOpcodeDetails,
  updateRefreshViewsData,
  getRefreshViewsStatus,
  updateRefreshStatus
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import PropTypes from 'prop-types';
import { SET_REFRESH_STATUS } from 'src/actions';
import { connect } from 'react-redux';
import { withStyles } from '@material-ui/styles';
import { setRefreshStatus } from '../../actions';
import OpportunityCharts from 'src/components/charts/OpportunityCharts';
var Dealer = process.env.REACT_APP_DEALER;

class OPCodes extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    this.addNewRow = this.addNewRow.bind(this);
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      showRefresh: 'none',
      disableTable: 'all',
      rawGridApi: {},
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      isOpcodeLoading: false,
      category: '',
      columnDefs: [
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          width: 100,
          field: 'opcode',
          editable: false,
          suppressMovable: true,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'opcodedescription',
          tooltipField: 'opcodedescription',
          width: 300,
          chartDataType: 'category',
          //  minWidth: 440,
          // flex: 1,
          suppressMovable: true,
          resizable: true,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Op Category',
          chartDataType: 'series',
          width: 150,
          field: 'opcategory',
          editable: false,
          suppressMovable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellEditor: 'agSelectCellEditor',
          cellEditorParams: {
            values: startEdit.getOpCategoryForDrilldown()
          },
          filterParams: {
            valueFormatter: function(params) {
              return startEdit.lookupValue(
                startEdit.getOpCategoryForDrilldown(),
                params.value
              );
            }
          },
          // valueFormatter: function(params) {
          //   return this.lookupValue(this.opCategories, params.value);
          // },
          // editable:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !(typeof this.props.keycloak.realmAccess.roles !== 'undefined'
          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true),

          cellClass: 'textAlign',
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: 150,
          chartDataType: 'series',
          editable: false,
          suppressMovable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Menu Sales',
          chartDataType: 'series',
          width: 150,

          field: 'menuSales',
          editable: false,
          // editable:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !(typeof this.props.keycloak.realmAccess.roles !== 'undefined'
          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true),
          suppressMenu: true,
          suppressMovable: true,
          unSortIcon: true,
          cellClass: 'textAlign',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'SLno',
          chartDataType: 'series',
          width: 200,
          field: 'slno',
          editable: false,
          hide: true,
          suppressMovable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          filter: false,
          // tooltip(params) {
          //   return 'Edit';
          // },
          width: 220,
          editable: false,
          sortable: false,
          suppressMovable: true,
          suppressMenu: true,
          hide:
            // eslint-disable-next-line react/destructuring-assignment
            !!(
              typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
              this.props.keycloak.realmAccess.roles.includes('client') === true
            ),

          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            const index = params.rowIndex;
            const eDiv = document.createElement('div');

            if (params.data.id === '' && params.data.name === '') {
              eDiv.innerHTML = `<button title="Edit" id="btnedit${index}" style="background: #384163; color: #fff; display:none;disabled; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>`;
            } else {
              eDiv.innerHTML = `<button  title="Edit" id="btnedit${index}" style="background: #384163; color: #fff; border-radius: 3px; width: 30px;disabled; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>`;
            }
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button').attr('disabled', 'disabled');
                $('.edit-button').css('background', '#38416373');
              });
            }
            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll('.edit-button')[0];
              const uButton = eDiv.querySelectorAll('.update-button')[0];
              const cButton = eDiv.querySelectorAll('.cancel-button')[0];
              const dButton = eDiv.querySelectorAll('.delete-button')[0];
              eButton.addEventListener('click', () => {
                localStorage.setItem('opcodeId', params.data.slno);
                var oldRow = [];
                localStorage.setItem('oldOpcode', params.data.opcode);
                localStorage.setItem('oldOpcategory', params.data.opcategory);
                oldRow.push(params.data.slno);
                oldRow.push(params.data.opcode);
                oldRow.push(params.data.paytype);
                oldRow.push(params.data.opcategory);
                oldRow.push(params.data.menuSales);
                oldRow.push(
                  JSON.parse(localStorage.getItem('selectedStoreId'))[0]
                );
                JSON.stringify(oldRow);
                localStorage.setItem('oldRow', oldRow);
                startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);
                $(`#btnedit${index}`).hide();
                $(`#btncancel${index}`).show();
                $(`#btnupdate${index}`).show();
              });
              uButton.addEventListener('click', () => {
                startEdit.onBtStopEditing(index);
                localStorage.setItem('newOpcode', params.data.opcode);
                localStorage.setItem('newOpcategory', params.data.opcategory);
                const selectedId = params.data.slno;
                const selectedOpcat = params.data.opcategory;
                const selectedOpcode = params.data.opcode;
                const selectedPayType = params.data.paytype;
                const storeId = JSON.parse(
                  localStorage.getItem('selectedStoreId')
                )[0];

                var newRow = [];
                newRow.push(
                  params.data.slno,
                  params.data.opcode,
                  params.data.paytype,
                  params.data.opcategory,
                  params.data.menuSales,
                  storeId
                );
                localStorage.setItem('newRow', newRow);
                if (selectedId !== '') {
                  startEdit.updateOpcodes(
                    selectedOpcat,
                    selectedOpcode,
                    selectedPayType,
                    storeId
                  );
                }
                $(`#btnedit${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
              });
              cButton.addEventListener('click', () => {
                startEdit.getAgGridData();
                startEdit.onBtStopEditing(index);
                $(`#btnedit${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true
      },
      editType: 'fullRow',
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  componentDidMount() {
    const client = makeApolloClientPostgres;
    const store_id = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userID = localStorage.getItem('userID');
    const status = getRefreshViewsStatus(userID);
    const hookUrl = process.env.REACT_APP_WEBHOOK_URL;
    const key = process.env.REACT_APP_WEBHOOK_KEY;
    const value = process.env.REACT_APP_WEBHOOK_VALUE;
    this.getOpCategory();
    if (
      this.props.keycloak.realmAccess.roles.includes('admin') === true ||
      this.props.keycloak.realmAccess.roles.includes('user') === true
    ) {
      getRefreshViewsStatus(result => {
        if (
          result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
            .status === 'FAILED'
        ) {
          const coll = document.getElementsByClassName('edit-button');
          localStorage.setItem('setRunningStatus', 1);
          setTimeout(() => {
            for (let i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#ccc';
            }
          }, 500);
          this.setState({ disableTable: 'none' });
          this.setState({ showRefresh: 'none' });
          this.setState({ setOpenAlertError: true });
          this.setState({ setOpenAlert: false });
          localStorage.setItem('setRunningStatus', 0);
        } else if (
          result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
            .status !== 'COMPLETE'
        ) {
          const coll = document.getElementsByClassName('edit-button');
          localStorage.setItem('setRunningStatus', 1);
          setTimeout(() => {
            for (let i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#ccc';
            }
          }, 500);
          this.setState({ disableTable: 'none' });
          this.setState({ showRefresh: 'none' });
          this.setState({ setOpenAlert: true });
          this.setState({ setOpenAlertError: false });
          this.handleRefresh(true);

          this.CheckRefreshStatus(result);
          this.setState({ setReload: true });
          // client
          // .mutate({
          //   mutation: MV_REFRESH_STATUS,
          //   variables: {
          //     reqSource: 'PayType',
          //     storeid: store_id
          //   }
          // })
          // .then(result => {
          //   client
          //   .mutate({
          //     mutation: MV_REFRESH_STATUS,
          //     variables: {
          //       reqSource: 'PayType',
          //       storeid: store_id
          //     }
          //   })
          //   .then(result => {
          //     if(result.data.statelessCcPhysicalRwGetMvRefreshStatus.string == 'TRUE'){
          //       const coll = document.getElementsByClassName('edit-button');
          //       for (let i = 0, len = coll.length; i < len; i++) {
          //         coll[i].style['background-color'] = '#ccc';
          //       }
          //       this.setState({ showRefresh: 'all' });
          //       this.setState({ disableTable: 'none' });
          //     }
          //   });
          // });
        } else if (
          result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
            .status == 'COMPLETE'
        ) {
          console.log('enter 1');
          this.setState({ setReload: false });
          client
            .mutate({
              mutation: MV_REFRESH_STATUS,
              variables: {
                reqSource: 'PayType',
                storeid: store_id
              }
            })
            .then(result => {
              console.log('enter 1==', result);
              if (
                result.data.statelessCcPhysicalRwGetMvRefreshStatus.string ==
                'TRUE'
              ) {
                const coll = document.getElementsByClassName('edit-button');
                for (let i = 0, len = coll.length; i < len; i++) {
                  coll[i].style['background-color'] = '#ccc';
                }
                this.setState({ showRefresh: 'all' });
                this.setState({ disableTable: 'none' });
              }
            });
        }
      });
      const interval = setInterval(() => {
        if (this.state.reload === true) {
          getRefreshViewsStatus(result => {
            if (
              result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
                .status == 'COMPLETE'
            ) {
              console.log('enter 2');
              const coll = document.getElementsByClassName('edit-button');
              localStorage.setItem('setRunningStatus', 0);
              this.setState({ disableTable: 'all' });
              this.setState({ showRefresh: 'none' });
              $('.edit-button').show();
              this.setState({ setOpenAlert: false });
              this.setState({ setOpenAlertError: false });
              this.props.handleReload(true);
              this.props.handleRefresh(false);
              setTimeout(() => {
                for (let i = 0, len = coll.length; i < len; i++) {
                  coll[i].style['background-color'] = '#384163';
                }
              }, 500);
              clearInterval(interval);
            } else if (
              result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
                .status === 'FAILED'
            ) {
              this.setState({ setOpenAlertError: true });
              this.setState({ setOpenAlert: false });
              localStorage.setItem('setRunningStatus', 0);
              clearInterval(interval);
            } else {
              const rstatus = this.CheckRefreshStatus(result);
              if (rstatus == 'COMPLETE') {
                console.log('enter 3');
                clearInterval(interval);
              }
            }
          });
        }
      }, 30000);
    }
  }

  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  setData = () => {
    const coll = document.getElementsByClassName('edit-button');
    this.setState({ disableTable: 'all' });
    this.setState({ showRefresh: 'none' });
    this.setState({ setOpen: true });
    $('.edit-button').show();
    this.setState({ setOpenAlertError: false });
    this.setState({ setOpenAlert: false });
    for (let i = 0, len = coll.length; i < len; i++) {
      coll[i].style['background-color'] = '#384163';
    }
    // eslint-disable-next-line react/destructuring-assignment
    this.props.handleReload(true);
    // eslint-disable-next-line react/destructuring-assignment
    this.props.handleRefresh(false);
  };

  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[2]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      // groupColumn[0]['editable'] = true;
      // groupColumn[0]['editable'] = true;
      // groupColumn[2]['editable'] = true;

      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = true;
      groupColumn[3]['editable'] = false;
      groupColumn[4]['editable'] = false;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    this.state.rawGridApi.setFocusedCell(index, 'opcategory', pinned);
    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'opcategory',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
    // eslint-disable-next-line react/destructuring-assignment
    // this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    // // eslint-disable-next-line react/destructuring-assignment
    // this.state.rawGridApi.setFocusedCell(index, 'opcategory', pinned);
    // // eslint-disable-next-line react/destructuring-assignment
    // this.state.rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'opcategory',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    // this.getOpCategory();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    getOpcodeDetails(result => {
      this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRwRoOpcodesDetails.nodes) {
        this.setState({
          rowData: result.data.statelessCcPhysicalRwRoOpcodesDetails.nodes
        });
      }
    });
  }
  getOpCategory = () => {
    const filteredOpcategoryArry = [];
    getOpcodeDetails(result => {
      if (result.data.statelessCcPhysicalRwRoOpcodesDetails.nodes) {
        result.data.statelessCcPhysicalRwRoOpcodesDetails.nodes.map(
          (item, i) => {
            filteredOpcategoryArry.push(
              result.data.statelessCcPhysicalRwRoOpcodesDetails.nodes[i]
                .opcategory
            );
          }
        );
      }
      let unique = filteredOpcategoryArry.filter(
        (item, i, ar) => ar.indexOf(item) === i
      );

      localStorage.setItem('category', unique);
    });
  };
  getOpCategoryForDrilldown = () => {
    let data = localStorage.getItem('category');
    if (data) {
      console.log('data=', data.split(','));
      const split_string = data.split(',');
      return split_string;
    }
  };

  opCategories = {
    COMPETITIVE: 'COMPETITIVE',
    REPAIR: 'REPAIR',
    MAINTENANCE: 'MAINTENANCE',
    'N/A': 'N/A'
  };
  lookupValue = (mappings, key) => {
    return mappings[key];
  };
  extractValues = mappings => {
    return Object.keys(mappings);
  };
  opcategory = this.extractValues(this.opCategories);
  CheckRefreshStatus = result => {
    const userID = localStorage.getItem('userID');
    let status = getRefreshViewsStatus(userID);
    const hookUrl = process.env.REACT_APP_WEBHOOK_URL;
    const key = process.env.REACT_APP_WEBHOOK_KEY;
    const value = process.env.REACT_APP_WEBHOOK_VALUE;
    if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status == 'COMPLETE'
    ) {
      console.log('enter 4');
      const coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 0);
      this.setState({ disableTable: 'all' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpen: true });
      $('.edit-button').show();
      this.setState({ setOpenAlert: false });
      this.setState({ setOpenAlertError: false });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
      this.props.handleReload(true);
      this.props.handleRefresh(false);
    } else if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status === 'RUNNING'
    ) {
      let coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 1);
      setTimeout(() => {
        for (let i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
      }, 500);
      this.setState({ disableTable: 'none' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpenAlertError: false });
      this.setState({ setOpenAlert: true });
      this.props.handleRefresh(true);
    } else if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status === 'REFRESHED'
    ) {
      const coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 1);
      setTimeout(() => {
        for (let i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
      }, 500);
      this.setState({ disableTable: 'none' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpenAlertError: false });
      this.setState({ setOpenAlert: true });
      this.props.handleRefresh(true);
      const storeId =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .store_id;
      const userID =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .refresh_by;
      fetch(hookUrl, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'text/plain',
          Pragma: 'no-cache',
          'Cache-Control': 'no-cache',
          'Hook-Auth': value,
          'Access-Control-Allow-Origin': '*',
          'Control-Allow-Methods': 'GET, PUT, POST, DELETE, HEAD, OPTIONS'
        }
      }).then(async response => {
        const data = response.json();
        if (response.statusText === 'OK') {
          const status = 'SERVERSTARTED';
          updateRefreshStatus(status, async result => {
            if (result) {
              this.cubeDataFetch(userID, storeId);
            }
          });
        }
      });
    } else if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status === 'SERVERSTARTED'
    ) {
      const coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 1);
      setTimeout(() => {
        for (let i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
      }, 500);
      this.setState({ disableTable: 'none' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpenAlertError: false });
      this.setState({ setOpenAlert: true });
      this.props.handleRefresh(true);

      const storeId =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .store_id;
      const userID =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .refresh_by;
      fetch(hookUrl, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'text/plain',
          Pragma: 'no-cache',
          'Cache-Control': 'no-cache',
          'Hook-Auth': value,
          'Access-Control-Allow-Origin': '*',
          'Control-Allow-Methods': 'GET, PUT, POST, DELETE, HEAD, OPTIONS'
        }
      }).then(async response => {
        const data = response.json();
        if (response.statusText === 'OK') {
          const status = 'SERVERSTARTED';
          updateRefreshStatus(status, async result => {
            if (result) {
              await this.cubeDataFetch(userID, storeId);
            }
          });
        }
      });
    } else if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status === 'FAILED'
    ) {
      this.props.handleRefresh(false);
      const coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 0);
      setTimeout(() => {
        for (var i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
      }, 500);
      this.setState({ disableTable: 'none' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpenAlertError: true });
      this.setState({ setOpenAlert: false });
    }
  };

  updateOpcodes = (opcategory, opcode, paytype, storeId) => {
    const coll = document.getElementsByClassName('edit-button');
    this.setState({ showRefresh: 'none' });
    this.setState({ disableTable: 'none' });
    this.setState({ isOpcodeLoading: true });
    const client = makeApolloClientPostgres;

    const oldOpcode = localStorage.getItem('oldOpcode');
    const oldOpcategory = localStorage.getItem('oldOpcategory');
    const newOpcode = localStorage.getItem('newOpcode');
    const newOpcategory = localStorage.getItem('newOpcategory');
    const userID = localStorage.getItem('userID');
    if (oldOpcode != newOpcode || oldOpcategory != newOpcategory) {
      client
        .mutate({
          mutation: UPDATE_OPCODE,
          variables: {
            opcat: opcategory,
            opcode,
            pay_type: paytype,
            store_id: storeId,
            user_id: userID
          }
        })
        .then(result => {
          if (
            result.data.statelessCcPhysicalRwUpdateRoopcodeMasterByOpcode
              .updateRoopcodeMasterStatuses[0].status == '1'
          ) {
            this.setState({ isOpcodeLoading: false });
            this.setState({ isLoading: false });
            this.setState({ showRefresh: 'all' });
            this.setState({ disableTable: 'all' });
            this.setState({ setMessage: `${opcode} updated.` });
            this.setState({ setOpenRow: true });
            for (var i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#384163';
            }
            const userIDs = localStorage.getItem('userID');
            const storeIds = JSON.parse(
              localStorage.getItem('selectedStoreId')
            )[0];
            const userRole = this.props.session.user.role;
            client
              .mutate({
                mutation: CLIENT_AUDITS,
                variables: {
                  dmlaction: 'Update opcode',
                  newdata: localStorage.getItem('newRow'),
                  olddata: localStorage.getItem('oldRow'),
                  schemaname: 'stateless_cc_physical_rw',
                  storeId: storeIds,
                  tablename: 'ro_opcodes',
                  username: userIDs,
                  userrole: userRole
                }
              })
              .then(result => {
                // console.log("data result=",result);
              });
          } else {
            this.setState({ showRefresh: 'none' });
            this.setState({ disableTable: 'none' });
            for (var i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#ccc';
            }
          }
        });
    } else {
      this.setState({ disableTable: 'all' });
      for (var i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    }
  };

  addRereshStatus = async (storeId, userID) => {
    const client = makeApolloClientPostgres;
    client
      .mutate({
        mutation: ADD_REFRESH_STATUS,
        variables: {
          status: 'RUNNING',
          refresh_by: userID,
          store_id: storeId
        }
      })
      .then(result => {
        const coll = document.getElementsByClassName('edit-button');
        const hookUrl = process.env.REACT_APP_WEBHOOK_URL;
        const key = process.env.REACT_APP_WEBHOOK_KEY;
        const value = process.env.REACT_APP_WEBHOOK_VALUE;
        getRefreshViewsStatus(result => {
          this.props.opcodeReloaded(result);
        });
        updateRefreshViewsData(userID, result => {
          if (result.data) {
            if (
              result.data.statelessCcPhysicalRwRetrunRefreshMviews.nodes[0]
                .status == 1
            ) {
              const client = makeApolloClientPostgres;
              client
                .query({
                  query: UPDATE_REFRESH_STATUS,
                  variables: {
                    status: 'REFRESHED'
                  }
                })
                .then(result => {
                  fetch(hookUrl, {
                    method: 'POST',
                    headers: {
                      Accept: 'application/json',
                      'Content-Type': 'text/plain',
                      Pragma: 'no-cache',
                      'Cache-Control': 'no-cache',
                      'Hook-Auth': value,
                      'Access-Control-Allow-Origin': '*',
                      'Control-Allow-Methods':
                        'GET, PUT, POST, DELETE, HEAD, OPTIONS'
                    }
                  }).then(async response => {
                    const data = response.json();
                    if (response.statusText === 'OK') {
                      const client = makeApolloClientPostgres;
                      client
                        .query({
                          query: UPDATE_REFRESH_STATUS,
                          variables: {
                            status: 'SERVERSTARTED'
                          }
                        })
                        // .mutate({
                        //   mutation: ADD_REFRESH_STATUS,
                        //   variables: {
                        //     status: 'SERVERSTARTED',
                        //     refresh_by: userID,
                        //     store_id: storeId
                        //   }
                        // })
                        .then(async result => {
                          await this.cubeDataFetch(userID, storeId);
                        });
                    }
                  });
                });
              // Hook call
              Promise.resolve();
            } else {
              this.props.handleRefresh(false);
              this.props.setRefreshErrorStatus(true);
              this.setState({ disableTable: 'none' });
              this.setState({ showRefresh: 'none' });
              this.setState({ setOpenAlertError: true });
              this.setState({ setOpenAlert: false });
              // Promise.reject();
            }
          } else {
            //this.setState({ setOpenAlert: false });
            // eslint-disable-next-line react/no-unused-state
            //this.setState({ setOpenAlertError: true });
          }
        });
      });
  };

  refreshDisable = () => {
    this.setState({ showRefresh: 'none' });
  };

  // CheckRefreshStatus = (result) => {
  //   let userID = localStorage.getItem('userID');
  //   const status = getRefreshViewsStatus(userID);
  //   const hookUrl = process.env.REACT_APP_WEBHOOK_URL;
  //   const key = process.env.REACT_APP_WEBHOOK_KEY;
  //   const value = process.env.REACT_APP_WEBHOOK_VALUE;
  //   const coll = document.getElementsByClassName('edit-button');
  //   if (
  //     result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].status
  //     === 'COMPLETE'
  //   ) {
  //     localStorage.setItem('setRunningStatus', 0);
  //     this.setState({ disableTable: 'all' });
  //     this.setState({ showRefresh: 'none' });
  //     this.setState({ setOpen: true });
  //     $('.edit-button').show();
  //     this.setState({ setOpenAlert: false });
  //     for (let i = 0, len = coll.length; i < len; i++) {
  //       coll[i].style['background-color'] = '#384163';
  //     }
  //     // eslint-disable-next-line react/destructuring-assignment
  //     this.props.handleReload(true);
  //     // eslint-disable-next-line react/destructuring-assignment
  //     this.props.handleRefresh(false);
  //   } else if (
  //     result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].status
  //     === 'RUNNING'
  //   ) {
  //     localStorage.setItem('setRunningStatus', 1);
  //     setTimeout(() => {
  //       for (let i = 0, len = coll.length; i < len; i++) {
  //         coll[i].style['background-color'] = '#ccc';
  //       }
  //     }, 500);
  //     this.setState({ disableTable: 'none' });
  //     this.setState({ showRefresh: 'none' });
  //     this.setState({ setOpenAlert: true });
  //     // eslint-disable-next-line react/destructuring-assignment
  //     this.props.handleRefresh(true);
  //   } else if (
  //     result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].status
  //     === 'REFRESHED'
  //   ) {
  //     localStorage.setItem('setRunningStatus', 1);
  //     setTimeout(() => {
  //       for (let i = 0, len = coll.length; i < len; i++) {
  //         coll[i].style['background-color'] = '#ccc';
  //       }
  //     }, 500);
  //     this.setState({ disableTable: 'none' });
  //     this.setState({ showRefresh: 'none' });
  //     this.setState({ setOpenAlert: true });
  //     // eslint-disable-next-line react/destructuring-assignment
  //     this.props.handleRefresh(true);
  //     const storeId = result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].store_id;
  //     userID = result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].refresh_by;
  //     fetch(hookUrl, {
  //       method: 'POST',
  //       headers: {
  //         Accept: 'application/json',
  //         'Content-Type': 'text/plain',
  //         Pragma: 'no-cache',
  //         'Cache-Control': 'no-cache',
  //         key: value,
  //         'Access-Control-Allow-Origin': '*',
  //         'Control-Allow-Methods': 'GET, PUT, POST, DELETE, HEAD, OPTIONS'
  //       }
  //     }).then(async (response) => {
  //       const data = response.json();
  //       if (response.statusText === 'OK') {
  //         // eslint-disable-next-line no-shadow
  //         const status = 'SERVERSTARTED';
  //         updateRefreshStatus(status, async (result) => {
  //           if (result) {
  //             this.cubeDataFetch(userID, storeId);
  //           }
  //         });
  //       }
  //     });
  //   } else if (
  //     result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].status
  //     === 'SERVERSTARTED'
  //   ) {
  //     localStorage.setItem('setRunningStatus', 1);
  //     setTimeout(() => {
  //       for (let i = 0, len = coll.length; i < len; i++) {
  //         coll[i].style['background-color'] = '#ccc';
  //       }
  //     }, 500);
  //     this.setState({ disableTable: 'none' });
  //     this.setState({ showRefresh: 'none' });
  //     this.setState({ setOpenAlert: true });
  //     // eslint-disable-next-line react/destructuring-assignment
  //     this.props.handleRefresh(true);
  //     const storeId = result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].store_id;
  //     const userID = result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0].refresh_by;
  //     fetch(hookUrl, {
  //       method: 'POST',
  //       headers: {
  //         Accept: 'application/json',
  //         'Content-Type': 'text/plain',
  //         Pragma: 'no-cache',
  //         'Cache-Control': 'no-cache',
  //         key: value,
  //         'Access-Control-Allow-Origin': '*',
  //         'Control-Allow-Methods': 'GET, PUT, POST, DELETE, HEAD, OPTIONS'
  //       }
  //     }).then(async (response) => {
  //       if (response.statusText === 'OK') {
  //         // eslint-disable-next-line no-shadow
  //         updateRefreshStatus(status, async (result) => {
  //           if (result) {
  //             await this.cubeDataFetch(userID, storeId);
  //           }
  //         });
  //       }
  //     });
  //   }
  // };

  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      columnKeys: ['opcode', 'opcategory', 'menu_sales', 'client_id'],
      fileName: 'Opcodes',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Op Codes' },
            mergeAcross: 3
          }
        ]
      ]
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };

  handleClose = () => {
    this.setState({ setOpen: false });
  };

  handleCloseRow = () => {
    this.setState({ setOpenRow: false });
  };

  onBtClose = () => {
    this.setState({ setOpenAlert: false });
  };

  onBtCloseError = () => {
    this.setState({ setOpenAlertError: false });
  };

  onBtDisable = () => {
    // eslint-disable-next-line react/destructuring-assignment
    this.props.setRefreshStatus(true);
    // eslint-disable-next-line react/destructuring-assignment
    this.props.handleReload(false);
    const { rowData } = this.state;
    const lengths = rowData.length;
    const userID = localStorage.getItem('userID');
    const coll = document.getElementsByClassName('edit-button');
    for (let i = 0, len = coll.length; i < len; i++) {
      coll[i].style['background-color'] = '#ccc';
    }
    const storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const status = 'started';

    this.setState({ setOpenAlertError: false });
    this.setState({ setOpenAlert: true });
    this.setState({ showRefresh: 'none' });
    this.setState({ disableTable: 'none' });
    // eslint-disable-next-line react/destructuring-assignment
    this.props.handleRefresh(true);
    this.addRereshStatus(storeId, userID);
  };

  cubeDataFetch = async (userID, storeId) => {
    const token = localStorage.getItem('keycloakToken');
    const cubeReloadUrl = process.env.REACT_APP_CUBE_RELOAD_URL;
    fetch(cubeReloadUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : ''
      },
      body: JSON.stringify({ query: { measures: ['CPRevenue.labor_revenue'] } })
    }).then(async response => {
      if (response) {
        const data = await response.json();
        const coll = document.getElementsByClassName('edit-button');
        if (data.data[0]) {
          const client = makeApolloClientPostgres;
          client

            .query({
              query: UPDATE_REFRESH_STATUS,
              variables: {
                status: 'COMPLETE'
              }
            })
            // .mutate({
            //   mutation: ADD_REFRESH_STATUS,
            //   variables: {
            //     status: 'COMPLETE',
            //     refresh_by: userID,
            //     store_id: storeId
            //   }
            // })
            .then(async result => {
              console.log('reee===');
              localStorage.setItem('setRunningStatus', 0);
              this.setState({ disableTable: 'all' });
              this.setState({ showRefresh: 'none' });
              this.setState({ setOpen: true });
              const eDiv = document.createElement('div');
              const eButton = eDiv.querySelectorAll('.edit-button')[0];
              $('.edit-button').show();
              this.setState({ setOpenAlertError: false });
              this.setState({ setOpenAlert: false });
              for (let i = 0, len = coll.length; i < len; i++) {
                coll[i].style['background-color'] = '#384163';
              }
              // eslint-disable-next-line react/destructuring-assignment
              this.props.handleReload(true);
              // eslint-disable-next-line react/destructuring-assignment
              this.props.handleRefresh(false);
              Promise.resolve();
            });
        } else {
          Promise.reject();
        }
      }
    });
  };

  addNewRow() {
    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.updateRowData({
      addIndex: 0,
      add: [{ id: '', name: '', serviceadvisor: '' }]
    });
    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.setFocusedCell(0, 'name', '');
  }
  onCellClicked = params => {
    const id = localStorage.getItem('opcodeId');
    if (params.data.slno != id) {
      $(`.edit-button`).show();
      $(`.update-button`).hide();
      $(`.cancel-button`).hide();
    }
  };
  resetReportGrid = () => {
    this.gridApi.setColumnDefs([]);
    this.gridApi.setColumnDefs(this.state.columnDefs);
  };
  render() {
    const { classes } = this.props;
    const {
      showRefresh,
      disableTable,
      setOpen,
      setOpenRow,
      setOpenAlert,
      setOpenAlertError,
      setMessage
    } = this.state;
    console.log('thissss', this.state);
    const coll = document.getElementsByClassName('edit-button');
    if (disableTable === 'none') {
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#ccc';
      }
    }
    return (
      <div>
        {this.props.keycloak.realmAccess.roles.includes('admin') === true ||
        this.props.keycloak.realmAccess.roles.includes('user') === true ? (
          <>
            <Paper
              square
              style={
                setOpenAlert == true || setOpenAlertError == true
                  ? { margin: 8, padding: 10, height: 80 }
                  : { margin: 8, padding: 10, height: 80, display: 'none' }
              }
            >
              {/* <Button
                variant="outlined"
                style={
                  showRefresh === 'all'
                    ? {
                      margin: 8,
                      padding: 10,
                      pointerEvents: 'all',
                      color: '#fff',
                      float: 'right',
                      background: process.env.REACT_APP_DEALER == 'Armatus' ? '#003d6b' :'#C2185B'
                    }
                    : {
                        margin: 8,
                        padding: 10,
                        pointerEvents: 'none',
                        color: '#ccc',
                        float: 'right',
                        background: '#fff'
                      }
                }
                onClick={this.onBtDisable}
              >
                Refresh Data
              </Button> */}
              <Collapse in={setOpenAlert} style={{ marginTop: 8 }}>
                <Alert
                  action={
                    <IconButton
                      aria-label="close"
                      color="inherit"
                      size="small"
                      onClick={this.onBtClose}
                    >
                      <CloseIcon fontSize="inherit" />
                    </IconButton>
                  }
                >
                  Dashboard refresh in progress. You will be able to update data
                  after this process is complete.
                </Alert>
              </Collapse>
              <Collapse in={setOpenAlertError}>
                <Alert
                  severity="error"
                  action={
                    <IconButton
                      aria-label="close"
                      color="inherit"
                      size="small"
                      onClick={this.onBtCloseError}
                    >
                      <CloseIcon fontSize="inherit" />
                    </IconButton>
                  }
                >
                  {' '}
                  Dashboard refresh has failed. Please contact support team to
                  resolve.
                </Alert>
              </Collapse>
            </Paper>
          </>
        ) : null}
        <Dialog
          open={setOpen}
          onClose={this.handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle id="alert-dialog-title" style={{ paddingLeft: 108 }}>
            OpCodes
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="alert-dialog-description">
              Dashboard refresh completed please reload the dashboard.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleClose} color="primary" autoFocus>
              OK
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={setOpenRow}
          onClose={this.handleCloseRow}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          // style={{ width: 350,height: 350 }}paddingLeft: '20px'
        >
          <DialogTitle
            id="alert-dialog-title"
            style={{ paddingLeft: 108, paddingRight: 85 }}
          >
            OpCodes
          </DialogTitle>
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              style={{ paddingLeft: 85, paddingRight: 85 }}
            >
              {setMessage}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCloseRow} color="primary" autoFocus>
              OK
            </Button>
          </DialogActions>
        </Dialog>
        <Paper
          square
          style={{
            margin: 8,
            marginTop: '20px'
            // backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            // border: Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            // color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
            TabIndicatorProps={{ style: { display: 'none' } }}
          >
            <Tab
              label={<div>Op Codes</div>}
              value="one"
              style={{
                textTransform: 'none',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
            />
            <Button
              variant="outlined"
              style={
                showRefresh == 'all'
                  ? {
                      margin: 8,
                      padding: 10,
                      pointerEvents: 'all',
                      color: '#fff',
                      float: 'right',
                      background:
                        process.env.REACT_APP_DEALER == 'Armatus'
                          ? '#003d6b'
                          : '#C2185B',
                      width: '10%'
                    }
                  : {
                      margin: 8,
                      padding: 10,
                      pointerEvents: 'none',
                      color: '#ccc',
                      float: 'right',
                      background: '#fff',
                      width: '10%'
                    }
              }
              onClick={this.onBtDisable}
            >
              Reload Data
            </Button>
            <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{ paddingTop: 17, paddingRight: 19, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, classes.reset, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>
        {this.state.isLoading === true ||
        this.state.isOpcodeLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: `${window.innerHeight - 200}px`,
            width: '1054px',
            margin: 8,
            display: this.state.isLoading == true ? 'none' : 'block',
            pointerEvents: disableTable
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            editType={this.state.editType}
            suppressClickEdit={true}
            onCellClicked={this.onCellClicked}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressHorizontalScroll={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setRefreshStatus: data =>
      dispatch({ type: SET_REFRESH_STATUS, payload: data })
  };
}
OPCodes.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};
const styles = theme => ({
  back: {
    float: 'right',
    marginTop: '14px',
    marginRight: '17px'
  },
  reset: {
    width: '130px'
  }
});
export default connect(mapStateToProps, mapDispatchToProps)(
  withStyles(styles)(withKeycloak(OPCodes)),
  OPCodes
);
