import React, { Component } from 'react';
var lodash = require('lodash');
export default class extends Component {
  constructor(props) {
    super(props);

    this.checkedHandler = this.checkedHandler.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;
    this.props.node.setDataValue(
      colId,
      checked == true ? 'Categorized' : 'Non-Categorized'
    );
  }

  render() {
    let oldDataArr;

    if (
      this.props.context.componentParent.state.oldOpcodeArray.length > 0 &&
      this.props.context.componentParent.state.prevOpcodeArray.length > 0
    ) {
      oldDataArr = lodash.filter(
        this.props.context.componentParent.state.prevOpcodeArray,
        item => {
          return (
            item.paytype ==
              this.props.context.componentParent.state.oldOpcodeArray[0]
                .paytype &&
            item.opcode ==
              this.props.context.componentParent.state.oldOpcodeArray[0].opcode
          );
        }
      );
    }

    return (
      <input
        type="checkbox"
        className="categorized"
        id={'categorized-' + this.props.rowIndex}
        disabled={true}
        onClick={this.checkedHandler}
        checked={
          this.props.context.componentParent.state.categorizedIds.includes(
            this.props.rowIndex
          )
            ? true
            : this.props.value == 'Categorized'
            ? true
            : false
        }
      />
    );
  }
}
