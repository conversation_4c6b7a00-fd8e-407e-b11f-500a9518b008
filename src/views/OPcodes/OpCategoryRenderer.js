import React, { Component } from 'react';
import { FormControl, TextField } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import clsx from 'clsx';

class OpCategoryRenderer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isFixedRateChecked: false,
      setMessage: '',
      opcategory: this.props.data.opcategory
    };
    this.handleOpCategoryChange = this.handleOpCategoryChange.bind(this);
  }
  componentDidMount() {
    if (this.props.data.opcategory == '-----') {
      this.props.context.componentParent.onOpCategoryChanged(
        this.props.data.opcategory,
        this.props.data.opcategory == '-----'
          ? 'N/A'
          : this.props.data.opcategory
      );
    }
  }
  handleOpCategoryChange(event) {
    let colId = this.props.column.colId;
    this.setState({
      opcategory: event.target.value
    });
    this.props.context.componentParent.onOpCategoryChanged(
      this.props.data.opcategory,
      event.target.value == '-----' ? 'N/A' : event.target.value
    );

    if (event.target.value == 'REPAIR') {
      if (
        typeof this.props.context.componentParent.state.deptNew ==
          'undefined' ||
        this.props.context.componentParent.state.deptNew == 'Service'
      ) {
        this.props.node.setDataValue('gridExcluded', 0);
      } else {
        this.props.node.setDataValue('gridExcluded', 1);
      }

      //this.props.node.setDataValue('fixedRate', 1);
      // this.props.api.refreshCells({
      //   columns: [
      //     'gridExcluded',
      //     'fixedRate',
      //     'fixedRateValue',
      //     'fixedRateDate'
      //   ],
      //   rowNodes: [this.props.node],
      //   force: true
      // });
      this.props.api.refreshCells({
        columns: ['gridExcluded', 'fixedRate', 'fixedRateParts'],
        rowNodes: [this.props.node],
        force: true
      });
    } else {
      var prevOpcode = this.props.data.opcode;
      var prevPaytype = this.props.data.paytype;
      let valArr = this.props.context.componentParent.state.prevOpcodeArray.filter(
        function(o) {
          return o.opcode == prevOpcode && o.paytype == prevPaytype;
        }
      );

      this.props.node.setDataValue('gridExcluded', 1);
      //this.props.node.setDataValue('gridExcluded', valArr[0].gridExcluded);
      this.props.node.setDataValue('fixedRate', 0);
      // this.props.api.refreshCells({
      //   columns: [
      //     'gridExcluded',
      //     'fixedRate',
      //     'fixedRateValue',
      //     'fixedRateDate'
      //   ],
      //   rowNodes: [this.props.node],
      //   force: true
      // });
      this.props.api.refreshCells({
        columns: ['gridExcluded', 'fixedRate', 'fixedRateParts'],
        rowNodes: [this.props.node],
        force: true
      });
    }

    // if (this.props.data.fixedRate == 1 && checked == true) {
    //   this.setState({
    //     isFixedRateChecked: true
    //   });
    // }
  }
  render() {
    const { classes } = this.props;
    let data = localStorage.getItem('category');

    const split_string = data.split(',');
    return (
      <>
        <select
          style={{
            width: 115,
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          onBlur={e => this.handleOpCategoryChange(e)}
          onChange={e => this.handleOpCategoryChange(e)}
          title={this.state.opcategory}
          value={this.state.opcategory}
          className={clsx(
            classes.formControlSelect,

            'opcategoryselect'
          )}
          name="opcategoryselect"
          id={'opcategoryselect-' + this.props.rowIndex}
        >
          {/* <option disabled value={'-----'}>
            {'-----'}
          </option> */}
          {split_string.map((obj, i) => (
            <option value={obj}>{obj}</option>
          ))}
        </select>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                  this.props.rowIndex
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.opcategory}
        </span>
      </>
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    width: 60,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: '8px !important',
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '75px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,
    width: 80,
    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 3
  }
});
export default withStyles(styles)(OpCategoryRenderer);
