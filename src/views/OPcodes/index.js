import React, { useEffect, useRef, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import Opcodes from './Opcodes';
import {
  setRefreshStatus,
  setReloadStatus,
  setPreviousPath,
  setCurrentPath,
  setPayTypeError,
  setRefreshErrorStatus,
  setOpcodeError,
  setOpcodeErrorCount,
  setAllErrorsCount
} from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import {
  getRefreshViewsStatus,
  updateRefreshStatus,
  getOpcodeDetails,
  getAllOpcodeErrors
} from 'src/utils/hasuraServices';
import { Redirect } from 'react-router-dom';

import { withKeycloak } from '@react-keycloak/web';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function Opcode({ keycloak, ...rest }) {
  const classes = useStyles();

  const history = useHistory();
  const session = useSelector(state => state.session);

  const [refreshStatusVal, setRefreshStatusVal] = useState();
  const [path, setPath] = useState('');
  const [opcategoryArr, setOpcategoryArr] = useState([]);
  const [reload, setReload] = useState(false);

  const [notifications, setNotifications] = useState([]);

  const [allOpcodeErrors, setAllErrors] = React.useState(0);
  const dispatch = useDispatch();

  const addOpcodeErrorCount = status => {
    let mounted = true;
    getAllOpcodeErrors(callback => {
      const opcodeErrorArr = callback.filter(function(el) {
        return el.cType === 'opcode';
      });
      var b = lodash.filter(callback, function(o) {
        if (o.noncategorizedcount > 0) return o;
      }).length;
      dispatch(setAllErrorsCount(b));
      if (opcodeErrorArr.length > 0) {
        setAllErrors(
          allErrors =>
            Number(allErrors) + Number(opcodeErrorArr[0].noncategorizedcount)
        );

        if (mounted) {
          if (opcodeErrorArr[0].noncategorizedcount > 0) {
            dispatch(setOpcodeError(true));
            dispatch(
              setOpcodeErrorCount(opcodeErrorArr[0].noncategorizedcount)
            );
          } else {
            dispatch(setOpcodeError(false));
            dispatch(setOpcodeErrorCount(0));
          }
        }
      } else {
        setNotifications([]);
        dispatch(setOpcodeError(false));
        dispatch(setOpcodeErrorCount(0));
      }
    });
  };

  useEffect(() => {
    history.listen(location => {
      setPath(location.search);
      dispatch(setPreviousPath(session.currentPath));
      dispatch(setCurrentPath(location.search));
    });
  }, [refreshStatusVal]);

  return (
    <Page className={classes.root} title="Opcode Categorizations">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Opcodes
          session={session}
          setOpcodeErrorCount={addOpcodeErrorCount}
          // opcategoryArr={opcategoryArr}
          history={history}
        />
      )}
    </Page>
  );
}
export default withKeycloak(Opcode);
