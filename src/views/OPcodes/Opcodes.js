/* eslint-disable react/default-props-match-prop-types */
/* eslint-disable react/jsx-no-duplicate-props */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-shadow */
/* eslint-disable react/static-property-placement */
/* eslint-disable no-unused-vars */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';
import ViewModuleIcon from '@material-ui/icons/ViewModule';
import Switch from '@material-ui/core/Switch';
//import Tooltip from '@material-ui/core/Tooltip';
import clsx from 'clsx';
import RestoreIcon from '@material-ui/icons/Restore';
import { Link as RouterLink } from 'react-router-dom';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';

// eslint-disable-next-line import/no-extraneous-dependencies
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  UPDATE_OPCODE,
  ADD_REFRESH_STATUS,
  UPDATE_REFRESH_STATUS,
  CLIENT_AUDITS,
  MV_REFRESH_STATUS
} from 'src/graphql/queries';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import makeApolloClientPostgresWrite from 'src/utils/apolloRootClientPostgresWrite';
import {
  getOpcodeDetails,
  getNonCPOpcodeDetails,
  getUniqueOpcodes
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import PropTypes from 'prop-types';
import { SET_REFRESH_STATUS, SET_DASHBOARD_RELOAD_STATUS } from 'src/actions';
import { connect } from 'react-redux';
import { withStyles } from '@material-ui/styles';

import CategoryChecboxRenderer from './CategoryChecboxRenderer';
import FixedRateRenderer from './FixedRateRenderer';
import OpCategoryRenderer from './OpCategoryRenderer';
import GridCheckboxRenderer from './GridCheckboxRenderer';
import DepartmentRenderer from './DepartmentRenderer';

import 'src/styles.css';
import { ReactSession } from 'react-client-session';
import { traceSpan } from 'src/utils/OTTTracing';
import { AlternateEmail } from '@material-ui/icons';
var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

class OPCodes extends React.Component {
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData();
  //     }
  //   }
  // }
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (
      this.state.pageType !== this.state.pageType &&
      this.props.location.pathname !== '/NonCPOpcodes'
    ) {
      this.setState({ toggleChecked: false });
      localStorage.setItem('toggleChecked', 'false'); // Reset toggle in storage
    }
    // if (ReactSession.get('serviceAdvisors') != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get('serviceAdvisors')
    //   );
    //   if (checkStatus == false) {
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ selectedOpcode: '' });
        this.setState({ storeChange: true });
        this.setState({ isOpcodeUpdated: false });
        this.setState({ isOpcodeRowUpdated: false });
        this.getAgGridData();
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
        this.setState({
          editedRowId: null
        });

        this.setState({
          isOpcodeEdited: false
        });
        this.gridApi.redrawRows();
        window.sortStateOpcode = {};
        window.colStateOpcode = {};
        window.filterStateOpcode = {};
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    // let selectedOpcode =
    //   this.props.history &&
    //   this.props.history.location &&
    //   this.props.history.location.state &&
    //   this.props.history.location.state.opcode
    //     ? this.props.history.location.state.opcode
    //     : '';

    this.addNewRow = this.addNewRow.bind(this);
    let selectedOpcode =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.opcodeSelected
        ? this.props.history.location.state.opcodeSelected
        : this.props.history &&
          this.props.history.location &&
          this.props.history.location.state &&
          this.props.history.location.state.opcode &&
          this.props.history.location.state.itemizationData != ''
        ? this.props.history.location.state.opcode
        : '';
    this.state = {
      toggleChecked: false,
      toggleChecked: false,
      storeChange: false,
      setReload: false,
      setMessage: '',
      showMessage: false,
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      selectedOpcode: selectedOpcode,
      showRefresh:
        this.props.session.dashboardReloadStatus == false ? 'none' : 'all',
      isExpanded:
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.isExpanded,
      roExpanded:
        this.props.history &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.roExpanded,
      disableTable: 'all',
      rawGridApi: {},
      oldRowArray: [],
      newRowArray: [],
      oldOpcodeArray: [],
      prevOpcodeArray: [],
      newOpcodeArray: [],
      gridExcludedValue: '',
      gridExcludedId: [],
      categorizedValue: '',
      editedRowId: null,
      categorizedIds: [],
      isOpcodeEdited: false,
      categorizedId: '',
      opcategoryArray: [],
      editedOpcodeArray: [],
      isOpcodeUpdated: false,
      isOpcodeRowUpdated: false,
      isReloaded: false,
      openSaveDialog: false,
      openConfirmDialog: false,
      openAlertDialog: false,
      paramsData: {},
      prevIndex: '',
      editIndex: '',
      fixedRateOld: '',
      fixedRateNew: null,
      fixedDateOld: '',
      fixedDateNew: null,
      partsFixedRateOld: '',
      partsFixedRateNew: null,
      partsFixedDateOld: '',
      partsFixedDateNew: null,
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      isOpcodeLoading: false,
      fixedRateError: null,
      partsFixedRateError: null,
      category: '',
      selectedOpcode: selectedOpcode,
      columnDefs: [
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          width: 100,
          minWidth: 100,
          field: 'opcode',
          editable: false,
          tooltipField: 'opcode',

          filter: 'agSetColumnFilter',
          cellClass: 'textAlign',
          suppressMenu: true,
          flex: 1,
          // tooltip: function(params) {
          //   if (params.data.department == 'Service') {
          //     return 'View Details';
          //   }
          // },
          unSortIcon: true,
          cellRenderer: function(params) {
            let value;
            value =
              params.value.length > 8
                ? params.value.substring(0, 8) + `...`
                : params.value;

            if (params.data.opcategory === 'N/A') {
              return value;
            }
            return startEdit.props &&
              startEdit.props.history &&
              startEdit.props.history.location &&
              startEdit.props.history.location.state &&
              (startEdit.props.history.location.state.isFrom ==
                '/LaborWorkMixAnalysis' ||
                startEdit.props.history.location.state.isFrom ==
                  '/PartsWorkMixAnalysis')
              ? value
              : `<a op-title="View Details" target="_blank" >${value}</a>`;
          },
          onCellClicked: function(params) {
            // if (params.data.opcategory === 'N/A') {
            //   return false;
            // }
            if (params.data.department == 'Service') {
              return startEdit.props &&
                startEdit.props.history &&
                startEdit.props.history.location &&
                startEdit.props.history.location.state &&
                (startEdit.props.history.location.state.isFrom ==
                  '/LaborWorkMixAnalysis' ||
                  startEdit.props.history.location.state.isFrom ==
                    '/PartsWorkMixAnalysis')
                ? ''
                : startEdit.openAlertDialogClick(params);
            }
          },
          cellStyle: function(params) {
            return {
              color: '#000000',
              fontWeight: 'bold',
              cursor:
                startEdit.props &&
                startEdit.props.history &&
                startEdit.props.history.location &&
                startEdit.props.history.location.state &&
                (startEdit.props.history.location.state.isFrom ==
                  '/LaborWorkMixAnalysis' ||
                  startEdit.props.history.location.state.isFrom ==
                    '/PartsWorkMixAnalysis')
                  ? 'default'
                  : params.data.department == 'Service'
                  ? 'pointer'
                  : 'default',
              // marginRight: '25px',
              // paddingLeft: '0px',
              textAlign: 'center',
              border: ' 0px white'
            };
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'opcodedescription',
          tooltipField: 'opcodedescription',
          width:
            typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
            this.props.keycloak.realmAccess.roles.includes('client') === true
              ? 185
              : 150,
          minWidth:
            typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
            this.props.keycloak.realmAccess.roles.includes('client') === true
              ? 185
              : 150,
          chartDataType: 'category',
          //  minWidth: 440,
          // flex: 1,

          resizable: true,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Op Category',
          chartDataType: 'series',
          width: 130,
          minWidth: 130,
          field: 'opcategory',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          cellRenderer: 'opCategoryRenderer',
          flex: 1,
          //cellEditor: 'agSelectCellEditor',

          // cellEditorParams: {
          //   values: startEdit.getOpCategoryForDrilldown()
          // },
          // filterParams: {
          //   valueFormatter: function(params) {
          //     return startEdit.lookupValue(
          //       startEdit.getOpCategoryForDrilldown(),
          //       params.value
          //     );
          //   }
          // },
          // valueFormatter: function(params) {
          //   return this.lookupValue(this.opCategories, params.value);
          // },
          // editable:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !(typeof this.props.keycloak.realmAccess.roles !== 'undefined'
          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true),

          cellClass: 'textAlign',
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          width: 90,
          minWidth: 90,
          chartDataType: 'series',
          editable: false,
          hide: true,
          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Date Last Used',
          field: 'lastUsedOn',
          width: 100,
          minWidth: 100,
          chartDataType: 'series',
          editable: false,

          suppressMenu: true,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          unSortIcon: true,
          flex: 1,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Last 12 Mth Count',
          field: 'usedLast12Mths',
          width: 120,
          minWidth: 120,
          chartDataType: 'series',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Action',
          //cellRenderer: 'buttonRenderer',
          filter: false,
          // tooltip(params) {
          //   return 'Edit';
          // },
          width: 120,
          minWidth: 120,
          editable: false,
          sortable: false,
          flex: 1,
          suppressMenu: true,
          hide:
            // eslint-disable-next-line react/destructuring-assignment
            !!(
              typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
              this.props.keycloak.realmAccess.roles.includes('client') === true
            ),
          cellStyle: params => {
            return startEdit.state.isReloaded
              ? {
                  'pointer-events': 'none',
                  opacity: '0.4',
                  textAlign: 'center'
                }
              : { textAlign: 'center' };
          },
          // cellStyle() {
          //   return { textAlign: 'center', border: ' 0px white' };
          // },
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            const index = params.rowIndex;
            const eDiv = document.createElement('div');
            // let context = this;
            let bgcolor;
            if (!startEdit.state.isOpcodeUpdated) {
              bgcolor = '#384163';
            } else {
              bgcolor = '#384163';
            }
            if (params.data.id === '' && params.data.name === '') {
              eDiv.innerHTML = `<button title="Edit" id="btneditOpcode${index}" style="background: ${bgcolor}; color: #fff; display:none;disabled; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-opcode"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancelOpcode${index}" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-opcode" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Confirm" id="btnupdateopcode${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-opcode" ><i class="fas fa-save"></i></button>`;
            } else {
              eDiv.innerHTML = `<button  title="Edit" id="btneditOpcode${index}" style="background: ${bgcolor}; color: #fff; border-radius: 3px; width: 30px;disabled; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-opcode"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancelOpcode${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-opcode" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Confirm" id="btnupdateopcode${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-opcode" ><i class="fas fa-save"></i></button>`;
            }
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button-opcode').attr('disabled', 'disabled');
                $('.edit-button-opcode').css('background', '#38416373');
              });
            }

            // if(props.keycloak && props.keycloak.realmAccess.roles.includes('admin') == false){
            //   $(document).ready(function() {
            //     $('.edit-button-opcode').attr('disabled', 'disabled');
            //     $('.edit-button-opcode').css('background', '#38416373');
            //   });
            // }
            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll('.edit-button-opcode')[0];
              const uButton = eDiv.querySelectorAll('.update-button-opcode')[0];
              const cButton = eDiv.querySelectorAll('.cancel-button-opcode')[0];
              const dButton = eDiv.querySelectorAll('.delete-button')[0];
              eButton.addEventListener('click', () => {
                $(`.cancel-button-opcode`).hide();
                $(`.update-button-opcode`).hide();
                $('.grid-excluded').attr('disabled', 'disabled');
                $('.maintenance-plan').attr('disabled', 'disabled');
                $('.menu-sales').attr('disabled', 'disabled');
                $('.mpi-item').attr('disabled', 'disabled');
                $('.categorized').attr('disabled', 'disabled');
                $('.fixedRate').attr('disabled', 'disabled');
                $('.fixedRateParts').attr('disabled', 'disabled');
                $('.partsFixedRateDate').attr('disabled', 'disabled');
                $('.partsFixedRateValue').attr('disabled', 'disabled');
                $('.opcategoryselect').hide();
                $('.fixedRateValue').attr('disabled', 'disabled');
                $('.fixedRateDate').attr('disabled', 'disabled');
                $('.opcategoryselect').hide();
                $(`.edit-button-opcode`).show();
                $(`#btncancelOpcode` + startEdit.state.editedRowId).click();
                startEdit.setState({
                  editedRowId: index
                });
                startEdit.setState({
                  deptNew: params.data.department
                });
                startEdit.setState({
                  isOpcodeEdited: true
                });
                localStorage.setItem('opcodeId', params.data.slno);
                var oldRow = [];
                var oldROArray = localStorage.getItem('oldRow');
                localStorage.setItem('oldOpcode', params.data.opcode);
                localStorage.setItem('oldOpcategory', params.data.opcategory);
                oldRow.push(params.data.slno);
                oldRow.push(params.data.opcode);
                oldRow.push(params.data.paytype);
                oldRow.push(params.data.opcategory);
                oldRow.push(params.data.menuSales);

                oldRow.push(params.data.gridExcluded);
                oldRow.push(params.data.categorized);
                oldRow.push(params.data.fixedRate);

                oldRow.push(params.data.fixedRateValue);
                oldRow.push(params.data.fixedRateDate);
                oldRow.push(params.data.fixedRateParts);

                oldRow.push(params.data.partsFixedRateValue);
                oldRow.push(params.data.partsFixedRateDate);
                startEdit.setState({
                  opCategoryNew: params.data.opcategory
                });
                startEdit.setState({
                  fixedRateNew: params.data.fixedRateValue
                });
                startEdit.setState({
                  fixedDateNew: params.data.fixedRateDate
                });
                startEdit.setState({
                  partsFixedRateNew: params.data.partsFixedRateValue
                });
                startEdit.setState({
                  partsFixedDateNew: params.data.partsFixedRateDate
                });
                oldRow.push(
                  JSON.parse(localStorage.getItem('selectedStoreId'))[0]
                );
                JSON.stringify(oldRow);
                var rowPrev = {
                  slno: params.data.slno,
                  opcode: params.data.opcode,
                  paytype: params.data.paytype,
                  opcategory: params.data.opcategory,
                  maintenancePlan: params.data.maintenancePlan,
                  mpiItem: params.data.mpiItem,
                  menuSales: params.data.menuSales,
                  gridExcluded: params.data.gridExcluded == true ? 1 : 0,
                  categorized: params.data.categorized,
                  department: params.data.department,
                  fixedRate: params.data.fixedRate,
                  fixedRateValue: params.data.fixedRateValue,
                  fixedRateDate: params.data.fixedRateDate,
                  fixedRateParts: params.data.fixedRateParts,
                  partsFixedRateValue: params.data.partsFixedRateValue,
                  partsFixedRateDate: params.data.partsFixedRateDate,
                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                };
                var rowPrevArray = startEdit.state.prevOpcodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ paytype, opcode }) =>
                    paytype === rowPrev.paytype && opcode === rowPrev.opcode
                );

                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }

                startEdit.setState({
                  prevOpcodeArray: rowPrevArray
                });

                var prevRow = [];
                prevRow.push({
                  slno: params.data.slno,
                  opcode: params.data.opcode,
                  paytype: params.data.paytype,
                  opcategory: params.data.opcategory,
                  maintenancePlan: params.data.maintenancePlan,
                  mpiItem: params.data.mpiItem,
                  menuSales: params.data.menuSales,
                  gridExcluded: params.data.gridExcluded == true ? 1 : 0,
                  categorized: params.data.categorized,
                  department: params.data.department,
                  fixedRate: params.data.fixedRate,
                  fixedRateValue: params.data.fixedRateValue,
                  fixedRateDate: params.data.fixedRateDate,
                  fixedRateParts: params.data.fixedRateParts,
                  partsFixedRateValue: params.data.partsFixedRateValue,
                  partsFixedRateDate: params.data.partsFixedRateDate,
                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                });
                startEdit.setState({
                  oldOpcodeArray: prevRow
                });
                localStorage.setItem('oldRow', oldRow);
                //startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);
                $(`#btneditOpcode${index}`).hide();
                $(`#btncancelOpcode${index}`).show();
                $(`#btnupdateopcode${index}`).show();
                //$('#gridExcluded' + index).prop('disabled', '');

                $('#categorized' + index).prop('disabled', '');
              });
              uButton.addEventListener('click', () => {
                if (
                  ((params.data.fixedRate == 1 &&
                    startEdit.state.fixedRateNew != null &&
                    startEdit.state.fixedRateNew != '' &&
                    startEdit.state.fixedRateNew != '.' &&
                    startEdit.state.fixedRateNew != 0 &&
                    startEdit.state.fixedDateNew != null &&
                    startEdit.state.fixedDateNew != '') ||
                    params.data.fixedRate == 0) &&
                  ((params.data.fixedRateParts == 1 &&
                    startEdit.state.partsFixedRateNew != null &&
                    startEdit.state.partsFixedRateNew != '' &&
                    startEdit.state.partsFixedRateNew != 'N/A' &&
                    startEdit.state.partsFixedRateNew.split(' ')[1] != '' &&
                    startEdit.state.partsFixedRateNew.split(' ')[1] != '.') ||
                    params.data.fixedRateParts == 0)
                ) {
                  startEdit.onBtStopEditing(index);

                  startEdit.setState({
                    isOpcodeEdited: false
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.setDataValue(
                    'opcategory',
                    startEdit.state.opCategoryNew
                  );

                  rowNode.data['fixedRateValue'] = startEdit.state.fixedRateNew;
                  rowNode.data['fixedRateDate'] = startEdit.state.fixedDateNew;
                  rowNode.data['partsFixedRateValue'] =
                    startEdit.state.partsFixedRateNew;
                  rowNode.data['partsFixedRateDate'] =
                    startEdit.state.partsFixedDateNew;

                  startEdit.setState({
                    prevIndex: ''
                  });

                  if (typeof params.data.opcategory == 'undefined') {
                    rowNode.setDataValue('opcategory', '-----');
                  }
                  localStorage.setItem('newOpcode', params.data.opcode);
                  localStorage.setItem('newOpcategory', params.data.opcategory);
                  const selectedId = params.data.slno;
                  const selectedOpcat = params.data.opcategory;
                  const selectedOpcode = params.data.opcode;
                  const selectedPayType = params.data.paytype;
                  const storeId = JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0];

                  var newRow = [];
                  newRow.push(
                    params.data.slno,
                    params.data.opcode,
                    params.data.paytype,
                    params.data.opcategory,
                    params.data.maintenancePlan,
                    params.data.mpiItem,
                    params.data.menuSales,
                    params.data.categorized,
                    params.data.department,
                    params.data.fixedRate,
                    params.data.fixedRateValue,
                    params.data.fixedRateDate,
                    params.data.fixedRateParts,
                    params.data.partsFixedRateValue,
                    params.data.partsFixedRateDate,
                    storeId
                  );
                  let oldDataArr = lodash.filter(
                    startEdit.state.prevOpcodeArray,
                    item => {
                      return (
                        item.paytype == params.data.paytype &&
                        item.opcode == params.data.opcode
                      );
                    }
                  );

                  var updatedRow = {
                    laboropcode: params.data.opcode,
                    pay_type: params.data.paytype,
                    oldopcategory:
                      oldDataArr[0].opcategory == '-----'
                        ? 'N/A'
                        : oldDataArr[0].opcategory,
                    newopcategory:
                      params.data.opcategory == '-----'
                        ? 'N/A'
                        : params.data.opcategory,
                    oldgridexcluded: oldDataArr[0].gridExcluded,
                    newgridexcluded: params.data.gridExcluded == true ? 1 : 0,
                    oldcategorized: oldDataArr[0].categorized,
                    newcategorized: 'Categorized',
                    oldmaintenance_plan: oldDataArr[0].maintenancePlan,
                    newmaintenance_plan: params.data.maintenancePlan,
                    oldmpi_item: oldDataArr[0].mpiItem,
                    newmpi_item: params.data.mpiItem,
                    oldmenu_sales: oldDataArr[0].menuSales,
                    olddepartment: oldDataArr[0].department,
                    newdepartment: params.data.department,
                    newmenu_sales: params.data.menuSales,

                    oldfixedrate: oldDataArr[0].fixedRate,
                    newfixedrate: params.data.fixedRate,
                    oldfixedratevalue: oldDataArr[0].fixedRateValue,
                    newfixedratevalue:
                      params.data.fixedRateValue == ''
                        ? null
                        : params.data.fixedRateValue,
                    oldfixedratedate:
                      oldDataArr[0].fixedRateDate == null
                        ? '1900-01-01'
                        : oldDataArr[0].fixedRateDate,
                    oldfixedrate: oldDataArr[0].fixedRate,
                    newfixedratedate: params.data.fixedRateDate,

                    oldparts_fixed_rate: oldDataArr[0].fixedRateParts,
                    newparts_fixed_rate: params.data.fixedRateParts,
                    oldparts_fixedratevalue: oldDataArr[0].partsFixedRateValue,
                    newparts_fixedratevalue:
                      params.data.partsFixedRateValue == ''
                        ? null
                        : params.data.partsFixedRateValue,
                    oldparts_fixedratedate:
                      oldDataArr[0].partsFixedRateDate == null
                        ? '1900-01-01'
                        : oldDataArr[0].partsFixedRateDate,
                    newparts_fixedratedate: params.data.partsFixedRateDate
                  };

                  var rowArray = startEdit.state.newOpcodeArray;
                  let indexVal = rowArray.findIndex(
                    ({ pay_type, laboropcode }) =>
                      pay_type == updatedRow.pay_type &&
                      laboropcode == updatedRow.laboropcode
                  );
                  let indexArr = lodash.filter(rowArray, item => {
                    return (
                      item.pay_type == updatedRow.pay_type &&
                      item.laboropcode == updatedRow.laboropcode
                    );
                  });
                  var ids = startEdit.state.categorizedIds;

                  if (indexVal === -1) {
                    if (
                      oldDataArr[0].opcategory != params.data.opcategory ||
                      oldDataArr[0].gridExcluded != params.data.gridExcluded ||
                      oldDataArr[0].categorized != params.data.categorized ||
                      oldDataArr[0].mpiItem != params.data.mpiItem ||
                      oldDataArr[0].menuSales != params.data.menuSales ||
                      oldDataArr[0].maintenancePlan !=
                        params.data.maintenancePlan ||
                      oldDataArr[0].department != params.data.department ||
                      oldDataArr[0].fixedRate != params.data.fixedRate ||
                      oldDataArr[0].fixedRateValue !=
                        params.data.fixedRateValue ||
                      oldDataArr[0].fixedRateDate !=
                        params.data.fixedRateDate ||
                      oldDataArr[0].fixedRateParts !=
                        params.data.fixedRateParts ||
                      oldDataArr[0].partsFixedRateValue !=
                        params.data.partsFixedRateValue ||
                      oldDataArr[0].partsFixedRateDate !=
                        params.data.partsFixedRateDate
                    ) {
                      rowArray.push(updatedRow);
                      ids.push(index);
                      startEdit.setState({
                        categorizedIds: ids
                      });
                    } else if (
                      oldDataArr[0].opcategory == 'N/A' &&
                      params.data.opcategory != 'N/A'
                    ) {
                      rowArray.push(updatedRow);
                      ids.push(index);
                      startEdit.setState({
                        categorizedIds: ids
                      });
                    }
                  } else {
                    rowArray[indexVal] = updatedRow;
                  }
                  // Update function

                  // Apply update function
                  const updatedData = startEdit.updateArray(rowArray);
                  startEdit.setState({
                    newOpcodeArray: updatedData
                  });

                  if (startEdit.state.newOpcodeArray.length > 0) {
                    startEdit.setState({
                      isOpcodeRowUpdated: true
                    });
                  }

                  localStorage.setItem('newRow', newRow);
                  // if (selectedId !== '') {
                  //   startEdit.updateOpcodes(
                  //     selectedOpcat,
                  //     selectedOpcode,
                  //     selectedPayType,true
                  //     storeId
                  //   );
                  // }

                  startEdit.gridApi.redrawRows();

                  startEdit.setState({
                    editedRowId: null
                  });
                  startEdit.setState({
                    fixedRateError: null
                  });
                  startEdit.setState({
                    partsFixedRateError: null
                  });
                  $(`#btneditOpcode${index}`).show();
                  $(`#btncancelOpcode${index}`).hide();
                  $(`#btnupdateopcode${index}`).hide();
                  $('#gridExcluded' + index).val(
                    startEdit.state.gridExcludedValue
                  );

                  $('#gridExcluded' + index).prop('disabled', 'disabled');
                  $('#categorized' + index).prop('disabled', 'disabled');
                } else {
                  if (
                    (params.data.fixedRate == 1 &&
                      (startEdit.state.fixedRateNew == null ||
                        startEdit.state.fixedRateNew == '' ||
                        startEdit.state.fixedRateNew == '0' ||
                        startEdit.state.fixedRateNew == '.')) ||
                    !(startEdit.state.fixedRateNew > 0)
                  ) {
                    startEdit.setState({
                      fixedRateError: index
                    });
                    var rowNode = params.api.getDisplayedRowAtIndex(index);
                    //rowNode.setDataValue('fixedRateValue', null);
                    rowNode.data['fixedRateValue'] = null;
                    params.api.refreshCells({
                      columns: ['fixedRate'],
                      rowNodes: [params.node],
                      force: true
                    });
                  }
                  if (
                    params.data.fixedRateParts == 1 &&
                    (startEdit.state.partsFixedRateNew == null ||
                      startEdit.state.partsFixedRateNew == '' ||
                      startEdit.state.partsFixedRateNew == 'N/A' ||
                      (startEdit.state.partsFixedRateNew != null &&
                        (startEdit.state.partsFixedRateNew.split(' ')[1] ==
                          '.' ||
                          startEdit.state.partsFixedRateNew.split(' ')[1] ==
                            '' ||
                          !(startEdit.state.partsFixedRateNew > 0))))
                  ) {
                    startEdit.setState({
                      partsFixedRateError: index
                    });
                    var rowNode = params.api.getDisplayedRowAtIndex(index);
                    console.log(
                      'startEdit.state.partsFixedRateNew',
                      startEdit.state.partsFixedRateNew
                    );
                    rowNode.data['partsFixedratevalue'] =
                      startEdit.state.partsFixedRateNew.split(' ')[0] + ' ';
                    params.api.refreshCells({
                      columns: ['fixedRateParts'],
                      rowNodes: [params.node],
                      force: true
                    });
                  }
                }
              });
              cButton.addEventListener('click', () => {
                // startEdit.getAgGridData();
                //

                startEdit.setState({
                  isOpcodeEdited: true
                });
                startEdit.setState({
                  fixedDateNew: null
                });
                startEdit.setState({
                  fixedRateNew: null
                });

                startEdit.setState({
                  partsFixedRateNew: null
                });
                startEdit.setState({
                  partsFixedDateNew: null
                });

                var rowNode = params.api.getDisplayedRowAtIndex(index);

                // let valArr = lodash.filter(
                //   startEdit.state.prevOpcodeArray,
                //   valArray => valArray.opcode != params.data.opcode
                // );
                // let valArr = lodash.map(
                //   startEdit.state.prevOpcodeArray,
                //   function(o) {
                //     if (
                //       o.opcode == params.data.opcode &&
                //       o.paytype == params.data.paytype
                //     )
                //       return o;
                //   }
                // );
                let catIds = startEdit.state.categorizedIds.filter(function(
                  obj
                ) {
                  return obj != index;
                });
                startEdit.setState({
                  categorizedIds: catIds
                });
                let valArr = startEdit.state.prevOpcodeArray.filter(function(
                  o
                ) {
                  return (
                    o.opcode == params.data.opcode &&
                    o.paytype == params.data.paytype
                  );
                });

                if (valArr.length > 0) {
                  rowNode.setDataValue('opcategory', valArr[0].opcategory);
                  rowNode.setDataValue('gridExcluded', valArr[0].gridExcluded);

                  rowNode.setDataValue('categorized', valArr[0].categorized);
                  rowNode.setDataValue('mpiItem', valArr[0].mpiItem);
                  rowNode.setDataValue('menuSales', valArr[0].menuSales);
                  rowNode.setDataValue('department', valArr[0].department);
                  rowNode.setDataValue('fixedRate', valArr[0].fixedRate);
                  rowNode.setDataValue(
                    'fixedRateParts',
                    valArr[0].fixedRateParts
                  );

                  rowNode.setDataValue(
                    'maintenancePlan',
                    valArr[0].maintenancePlan
                  );
                }

                let filteredArray = startEdit.state.newOpcodeArray.filter(
                  function(obj) {
                    return (
                      obj.laboropcode != params.data.opcode ||
                      obj.pay_type != params.data.paytype
                    );
                  }
                );

                startEdit.setState({
                  newOpcodeArray: filteredArray
                });
                if (startEdit.state.newOpcodeArray.length <= 0) {
                  startEdit.setState({
                    isOpcodeRowUpdated: false
                  });
                }
                startEdit.setState({
                  editedRowId: null
                });
                params.api.refreshCells({
                  columns: ['fixedRate', 'fixedRateParts'],
                  rowNodes: [rowNode],
                  force: true
                });
                // newvalArr = lodash.without(newvalArr, undefined);
                startEdit.onBtStopEditing(index);
                //rowNode.setData(newData);
                startEdit.gridApi.redrawRows();
                $(`#btneditOpcode${index}`).show();
                $(`#btncancelOpcode${index}`).hide();
                $(`#btnupdateopcode${index}`).hide();
                $('#gridExcluded' + index).prop('disabled', 'disabled');
                $('#categorized' + index).prop('disabled', 'disabled');
              });
            }
            return eDiv;
          }
        },
        {
          headerName: 'Labor Fixed Rate',
          field: 'fixedRate',
          width: 200,
          minWidth: 200,
          chartDataType: 'series',
          cellRenderer: 'fixedRateRenderer',
          editable: false,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        // {
        //   headerName: 'Fixed Rate ($)',
        //   field: 'fixedRateValue',
        //   width: 100,
        //   minWidth: 100,
        //   cellClass: 'textAlign',
        //   cellRenderer: 'fixedRateRenderer',
        //   flex: 1,
        //   chartDataType: 'series',
        //   editable: false,

        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle() {
        //     return { border: ' 0px white' };
        //   }
        // },
        // {
        //   headerName: 'Fixed Rt Install Date',
        //   field: 'fixedRateDate',
        //   width: 120,
        //   minWidth: 120,
        //   cellRenderer: 'fixedRateRenderer',
        //   chartDataType: 'series',
        //   editable: false,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   flex: 1,
        //   valueFormatter: this.formatCellValueDate,
        //   filterParams: {
        //     valueFormatter: this.formatCellValueDate
        //   },
        //   cellStyle() {
        //     return { border: ' 0px white' };
        //   }
        // },
        {
          headerName: 'Parts Fixed Rate',
          field: 'fixedRateParts',
          width: 250,
          chartDataType: 'series',
          cellRenderer: 'fixedRateRenderer',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        // {
        //   headerName: 'Parts Fixed Rate($)',
        //   field: 'partsFixedRateValue',
        //   width: 150,
        //   cellClass: 'textAlign',
        //   cellRenderer: 'fixedRateRenderer',

        //   chartDataType: 'series',
        //   editable: false,

        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle() {
        //     return { border: ' 0px white' };
        //   }
        // },
        // {
        //   headerName: 'Parts Fixed Rt Install Date',
        //   field: 'partsFixedRateDate',
        //   width: 120,
        //   cellRenderer: 'fixedRateRenderer',
        //   chartDataType: 'series',
        //   editable: false,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   valueFormatter: this.formatCellValueDate,
        //   filterParams: {
        //     valueFormatter: this.formatCellValueDate
        //   },
        //   cellStyle() {
        //     return { border: ' 0px white' };
        //   }
        // },
        {
          headerName: 'Maint. Plan',
          chartDataType: 'series',
          width: 100,
          minWidth: 100,
          flex: 1,
          field: 'maintenancePlan',
          editable: false,
          cellRenderer: 'gridCheckboxRenderer',
          // editable:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !(typeof this.props.keycloak.realmAccess.roles !== 'undefined'
          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true),
          suppressMenu: true,

          unSortIcon: true,
          cellClass: 'textAlign',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },

        {
          headerName: 'MPI Item',
          chartDataType: 'series',
          width: 100,
          minWidth: 100,
          flex: 1,
          field: 'mpiItem',
          editable: false,
          cellRenderer: 'gridCheckboxRenderer',
          // editable:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !(typeof this.props.keycloak.realmAccess.roles !== 'undefined'
          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true),
          suppressMenu: true,

          unSortIcon: true,
          cellClass: 'textAlign',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Menu Item',
          chartDataType: 'series',
          width: 100,
          minWidth: 100,
          cellRenderer: 'gridCheckboxRenderer',
          field: 'menuSales',
          editable: false,
          flex: 1,
          // editable:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !(typeof this.props.keycloak.realmAccess.roles !== 'undefined'
          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true),
          suppressMenu: true,

          unSortIcon: true,
          cellClass: 'textAlign',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'SLno',
          chartDataType: 'series',
          width: 100,
          minWidth: 100,
          field: 'slno',
          editable: false,
          flex: 1,
          hide: true,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Repair Compliance',
          chartDataType: 'series',
          width: 120,
          minWidth: 120,
          flex: 1,
          field: 'gridExcluded',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          cellRenderer: 'gridCheckboxRenderer',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Department',
          chartDataType: 'series',
          width: 130,
          minWidth: 130,
          field: 'department',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          //cellEditor: 'agSelectCellEditor',
          //hide: localStorage.getItem('dms') == 'dtk' ? true : false,
          hide: false,
          flex: 1,
          //flex: 2,
          cellRenderer: 'departmentRenderer',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },

        {
          headerName: 'Categorized',
          chartDataType: 'series',
          width: 150,
          minWidth: 150,
          flex: 1,
          field: 'categorized',
          editable: params => params.data.categorized == 'Non-Categorized',
          // editable:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !(typeof this.props.keycloak.realmAccess.roles !== 'undefined'
          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true),
          suppressMenu: true,

          unSortIcon: true,
          cellStyle: this.cellStyles,
          editable: false,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          cellRenderer: 'categoryChecboxRenderer'
          //valueFormatter: this.formatCellValue,
          //onCellClicked: this.setCategorized
          //cellRenderer: 'CategoryCheckboxRenderer'
          // cellRenderer: params => {
          //   var index = params.rowIndex;
          //   return `<input type='checkbox' value=${params.value}
          //   id="categorized${index}" disabled=disabled
          //   ${params.value == 'categorized' ? 'checked' : ''} />`;
          // }
        }
      ],
      context: { componentParent: this },
      frameworkComponents: {
        categoryChecboxRenderer: CategoryChecboxRenderer,
        gridCheckboxRenderer: GridCheckboxRenderer,
        fixedRateRenderer: FixedRateRenderer,
        opCategoryRenderer: OpCategoryRenderer,
        departmentRenderer: DepartmentRenderer
      },
      rowData: [],
      headerHeight: 45,
      getRowId: params => params.data.opcategory,
      rowClassRules: {
        // row style function
        'noncategorized-rows': params => {
          return params.data.categorized == 'non-categorized';
        },
        // row style expression
        'categorized-rows': params => {
          return params.data.categorized == 'categorized';
        }
      },
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        suppressKeyboardEvent: params => params.event.keyCode === 13,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        suppressMovable: false
      },
      editType: 'fullRow',
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  updateArray = data => {
    return data.map(item => {
      // Check and update `newfixedrate` related fields
      if (item.newfixedrate === 0) {
        item.newfixedratedate = null;
        item.newfixedratevalue = null;
      }

      // Check and update `newparts_fixed_rate` related fields
      if (item.newparts_fixed_rate === 0) {
        item.newparts_fixedratedate = null;
        item.newparts_fixedratevalue = null;
      }

      return item;
    });
  };
  formatCellValue = params => {
    if (params.value == 'categorized') {
      return 1;
    } else {
      return 0;
    }
  };
  onCellClickedOpcode = params => {
    if (params.data.department == 'Service') {
      window.sortStateOpcode = this.gridApi.getSortModel();
      window.colStateOpcode = this.state.gridcolumnApi.getColumnState();
      window.filterStateOpcode = this.gridApi.getFilterModel();
      let tabType =
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.tabType;
      let isLabor =
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.isLabor;
      {
        isLabor == false && tabType == 'Itemization'
          ? this.props.history.push({
              pathname: '/PartsWorkMixAnalysis',
              history: this.props.history,
              state: {
                opcode: params.value,
                tabselection: 'four',
                pageType: 'opcodes',
                isFrom: 'opcodes',
                itemizationData:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.data
                    ? this.props.history.location.state.data
                    : '',
                itemizationTab:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.itemizationTab
                    ? this.props.history.location.state.itemizationTab
                    : '',
                isLabor:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.isLabor
                    ? this.props.history.location.state.isLabor
                    : '',
                isExpanded: this.state.isExpanded,
                parent:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.parent
                    ? this.props.history.location.state.parent
                    : '',
                selectedButton:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.summaryTab
                    ? this.props.history.location.state.summaryTab
                    : ''
              }
            })
          : this.props.history.push({
              pathname: '/LaborWorkMixAnalysis',
              history: this.props.history,
              state: {
                opcode: params.value,
                tabselection: 'four',
                pageType: 'opcodes',
                isFrom: 'opcodes',
                itemizationData:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.data
                    ? this.props.history.location.state.data
                    : '',
                itemizationTab:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.itemizationTab
                    ? this.props.history.location.state.itemizationTab
                    : '',
                isLabor:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.isLabor
                    ? this.props.history.location.state.isLabor
                    : '',
                isExpanded: this.state.isExpanded,
                parent:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.parent
                    ? this.props.history.location.state.parent
                    : '',
                selectedButton:
                  this.props.history.location &&
                  this.props.history.location.state &&
                  this.props.history.location.state.summaryTab
                    ? this.props.history.location.state.summaryTab
                    : ''
              }
            });
      }
    }
  };
  getRowStyle = params => {
    if (params.data.categorized == 'Non-Categorized') {
      return { background: 'rgb(221, 234, 244)' };
    }

    if (
      params.data.recentUpdatedFlag == 'Y' ||
      this.state.editedOpcodeArray.includes(params.data.opcode)
    ) {
      return { background: 'rgba(255, 255, 153, 0.49)' };
    }
  };
  // componentDidMount() {
  //   this.getOpCategory();
  //   const toggleState = localStorage.getItem('toggleChecked') === 'true';
  //   this.setState({ toggleChecked: toggleState });
  // }
  componentDidMount() {
    this.getOpCategory();

    // Get toggle state from localStorage
    const toggleState = localStorage.getItem('toggleChecked') === 'true';

    // Check if user is on the correct page before enabling
    if (this.state.pageType === '/NonCPOpcodes') {
      this.setState({ toggleChecked: toggleState });
    } else {
      this.setState({ toggleChecked: false });
      localStorage.setItem('toggleChecked', 'false'); // Reset toggle if not on NonCPOpcodes
    }
  }

  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  // setData = () => {
  //   const coll = document.getElementsByClassName('edit-button-opcode');
  //   this.setState({ disableTable: 'all' });
  //   this.setState({ showRefresh: 'none' });
  //   this.setState({ setOpen: true });
  //   $('.edit-button-opcode').show();
  //   this.setState({ setOpenAlertError: false });
  //   this.setState({ setOpenAlert: false });
  //   if (!this.state.isOpcodeUpdated) {
  //     for (let i = 0, len = coll.length; i < len; i++) {
  //       coll[i].style['background-color'] = '#384163';
  //     }
  //   } else {
  //     for (let i = 0, len = coll.length; i < len; i++) {
  //       coll[i].style['background-color'] = '#ccc';
  //       coll[i].style['pointer-events'] = 'none';
  //     }
  //   }

  //   // eslint-disable-next-line react/destructuring-assignment
  //   this.props.handleReload(true);
  //   // eslint-disable-next-line react/destructuring-assignment
  //   this.props.handleRefresh(false);
  // };

  onBtStopEditing = () => {
    this.gridApi.stopEditing();

    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[2]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };
  onCellValueChanged = params => {
    if (params.column.getId() === 'fixedRate') {
      params.api.refreshCells({
        columns: ['fixedRate'],
        rowNodes: [params.node],
        force: true
      });
      // this.gridApi.refreshCells({ columns: ['fixedRateValue'] });
    }
  };
  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      // groupColumn[0]['editable'] = true;
      // groupColumn[0]['editable'] = true;
      // groupColumn[2]['editable'] = true;

      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = false;
      groupColumn[3]['editable'] = false;
      groupColumn[4]['editable'] = false;
      groupColumn[5]['editable'] = false;
      groupColumn[6]['editable'] = false;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    this.state.rawGridApi.setFocusedCell(index, 'opcategory', pinned);
    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'opcategory',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
    // eslint-disable-next-line react/destructuring-assignment
    // this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    // // eslint-disable-next-line react/destructuring-assignment
    // this.state.rawGridApi.setFocusedCell(index, 'opcategory', pinned);
    // // eslint-disable-next-line react/destructuring-assignment
    // this.state.rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'opcategory',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
  };
  setGridExcludeed = data => {
    // data
    // ? this.setState({advisorStatus: data.event.target.value})
    // : this.setState({advisorStatus: ''})

    data
      ? this.setState({
          gridExcludedValue: data.event.target.checked == true ? '1' : '0'
        })
      : this.setState({ gridExcludedValue: '' });
    if (data) {
      if (data.event.target.checked == true) {
        this.setState({
          gridExcludedId: [...this.state.gridExcludedId, data.event.target.id]
        });
      } else {
        this.setState({
          gridExcludedId: this.state.gridExcludedId.filter(function(id) {
            return id !== data.event.target.id;
          })
        });
      }
    } else {
      this.setState({ gridExcludedValue: '' });
    }
  };
  setCategorized = data => {
    // data
    // ? this.setState({advisorStatus: data.event.target.value})
    // : this.setState({advisorStatus: ''})
    data
      ? this.setState({
          categorizedValue:
            data.event.target.checked == true
              ? 'categorized'
              : 'non-categorized'
        })
      : this.setState({ categorizedValue: 'categorized' });

    // if (data) {
    //   if (data.event.target.checked == true) {
    //     this.setState({
    //       gridExcludedId: [...this.state.gridExcludedId, data.event.target.id]
    //     });
    //   } else {
    //     this.setState({
    //       gridExcludedId: this.state.gridExcludedId.filter(function(id) {
    //         return id !== data.event.target.id;
    //       })
    //     });
    //   }
    // } else {
    //   this.setState({ gridExcludedValue: '' });
    // }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    // this.getOpCategory();
    this.setState({ gridcolumnApi: params.columnApi });
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    if (this.props.history.location.state == undefined) {
      window.sortStateOpcode = {};
      window.filterStateOpcode = {};
    }
    this.gridApi.setSortModel(window.sortStateOpcode);
    this.gridApi.setFilterModel(window.filterStateOpcode);
    //this.gridApi.sizeColumnsToFit ();
    this.getAgGridData();
  };

  getAgGridData(resetData) {
    const filterModel = this.gridApi.getFilterModel(); // Store the filter state
    this.gridApi.showLoadingOverlay();
    if (resetData == 'reset') {
      this.setState({ isLoading: false });
      this.setState({ isOpcodeRowUpdated: false });
    } else {
      this.setState({ isLoading: true });
    }

    getOpcodeDetails(result => {
      this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRwGetRoOpcodesDetails.nodes) {
        var roData = lodash.orderBy(
          result.data.statelessCcPhysicalRwGetRoOpcodesDetails.nodes,
          ['categorized', 'recentUpdatedFlag'],
          ['desc', 'desc']
        );
        //remove extra newlines and spaces
        roData = roData.map(item => ({
          ...item,
          opcodedescription: item.opcodedescription
            ? item.opcodedescription.replace(/\r\n/g, ' ').trim()
            : ''
        }));

        this.setState({
          rowData: roData
        });
        this.gridApi.setFilterModel(filterModel);
        this.gridApi.onFilterChanged();
        if (this.state.selectedOpcode != '') {
          this.filterByOpCode(this.state.selectedOpcode.trim());
        }
        this.gridApi.hideOverlay();
        if (
          window.filterStateOpcode != undefined &&
          this.props.history.location.state.isFrom != '/LaborWorkMixAnalysis' &&
          this.props.history.location.state.isFrom != '/PartsWorkMixAnalysis'
        ) {
          this.filterByValue();
        }
      }
    });
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateOpcode);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  filterByOpCode = opcodes => {
    var opcodeFilterComponent = this.state.rawGridApi.getFilterInstance(
      'opcode'
    );
    // opcodeFilterComponent.setModel({
    //   filterType: 'text',
    //   type: 'Equals',
    //   filter: opcodes
    // });

    // opcodeFilterComponent.setModel({ values: opcodes });
    // this.gridApi.onFilterChanged();
    // this.state.rawGridApi.onFilterChanged();
    opcodeFilterComponent.setModel({ values: [opcodes] });
    this.gridApi.onFilterChanged();
  };
  handleClickSaveOpcode = () => {
    this.setState({
      openSaveDialog: true
    });
  };
  openAlertDialogClick = params => {
    if (this.state.isOpcodeRowUpdated) {
      this.setState({
        openAlertDialog: true
      });
      this.setState({
        paramsData: params
      });
    } else {
      this.onCellClickedOpcode(params);
    }
  };
  handleAlertCancel = () => {
    this.setState({
      openAlertDialog: false
    });
  };
  handleAlertSave = () => {
    this.onCellClickedOpcode(this.state.paramsData);
    this.setState({
      openAlertDialog: false
    });
  };
  handleCancel = () => {
    this.setState({
      openSaveDialog: false
    });
    this.setState({
      openConfirmDialog: false
    });
  };
  handleSave = () => {
    this.updateOpcodes();
    this.setState({
      openSaveDialog: false
    });
    this.setState({
      openConfirmDialog: false
    });
  };
  getOpCategory = () => {
    const filteredOpcategoryArry = [];
    getUniqueOpcodes(result => {
      if (result.data.statelessCcPhysicalRwGetOpcategoryCodes.nodes) {
        result.data.statelessCcPhysicalRwGetOpcategoryCodes.nodes.map(
          (item, i) => {
            filteredOpcategoryArry.push(
              result.data.statelessCcPhysicalRwGetOpcategoryCodes.nodes[i]
                .opcategory
            );
          }
        );
      }
      let unique = filteredOpcategoryArry.filter(
        (item, i, ar) => ar.indexOf(item) === i
      );

      localStorage.setItem('category', unique);
    });
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(new Date(params.value)).format('MM/DD/YY');
    }
  };
  getOpCategoryForDrilldown = () => {
    let data = localStorage.getItem('category');
    if (data) {
      const split_string = data.split(',');
      return split_string;
    }
  };
  geDeptForDrilldown = () => {
    let data = ['Service', 'Body Shop'];
    return data;
  };

  opCategories = {
    COMPETITIVE: 'COMPETITIVE',
    REPAIR: 'REPAIR',
    MAINTENANCE: 'MAINTENANCE',
    'N/A': 'N/A'
  };
  lookupValue = (mappings, key) => {
    return mappings[key];
  };
  extractValues = mappings => {
    return Object.keys(mappings);
  };
  opcategory = this.extractValues(this.opCategories);

  updateOpcodes = () => {
    const coll = document.getElementsByClassName('edit-button-opcode');
    this.setState({ showRefresh: 'none' });
    this.setState({ disableTable: 'none' });
    this.setState({ isOpcodeLoading: true });
    this.setState({ showMessage: false });
    const client = makeApolloClientPostgres;
    const apolloClient = makeApolloClientPostgresWrite;

    const userID = localStorage.getItem('userID');
    if (this.state.newOpcodeArray.length > 0) {
      this.setState({
        isOpcodeRowUpdated: false
      });
      let oldOpArr = [];
      this.state.prevOpcodeArray.map((obj, i) => {
        oldOpArr.push(Object.values(obj));
      });
      let newOpArr = [];
      this.state.newOpcodeArray.map((obj, i) => {
        newOpArr.push(Object.values(obj));
      });
      this.state.newOpcodeArray.map((obj, i) => {
        newOpArr.push(Object.values(obj));
      });
      let editedOpcodes = [];
      this.state.newOpcodeArray.map((obj, i) => {
        editedOpcodes.push(obj.laboropcode);
      });
      this.setState({
        editedOpcodeArray: editedOpcodes
      });
      console.log('cc===aa', this.state.newOpcodeArray);

      const start = new Date();
      apolloClient
        .mutate({
          mutation: UPDATE_OPCODE,
          variables: {
            p_opcode: JSON.stringify(this.state.newOpcodeArray),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
            user_id: userID
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/OPcodes',
            origin: '',
            event: 'Menu Load',
            is_from: 'UPDATE_OPCODE',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          if (
            result.data.statelessCcPhysicalRwUpdateRoopcodeMasterByOpcode
              .updateRoopcodeMasterStatuses[0].status == '1'
          ) {
            this.props.setOpcodeErrorCount(true);

            this.setState({ isOpcodeLoading: false });

            //this.gridApi.redrawRows();
            this.setState({ isLoading: true });
            //this.getAgGridData();
            this.setState({
              categorizedIds: []
            });
            this.setState({ isLoading: false });
            // this.setState({ showRefresh: 'all' });
            this.setState({ setMessage: `Opcodes Updated.` });
            this.gridApi.redrawRows();
            this.setState({ setOpenRow: true });

            this.getAgGridData('reset');
            for (var i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#384163';
            }
            const userIDs = localStorage.getItem('userID');
            const storeIds = JSON.parse(
              localStorage.getItem('selectedStoreId')
            )[0];
            const userRole = this.props.session.user.role;

            const start = new Date();
            apolloClient
              .mutate({
                mutation: CLIENT_AUDITS,
                variables: {
                  dmlaction: 'Update opcode',
                  newdata: JSON.stringify(newOpArr),
                  olddata: JSON.stringify(oldOpArr),
                  schemaname: 'stateless_cc_physical_rw',
                  storeId: storeIds,
                  tablename: 'ro_opcodes',
                  username: userIDs,
                  userrole: userRole
                }
              })
              .then(result => {
                const spanAttribute = {
                  pageUrl: '/OPcodes',
                  origin: '',
                  event: 'Menu Load',
                  is_from: 'CLIENT_AUDITS',
                  value: new Date() - start,
                  provenance: localStorage.getItem('provenance')
                };
                traceSpan('Menu Load', spanAttribute);
                // console.log("data result=",result);
              });

            this.setState({ oldOpcodeArray: [] });
            this.setState({ prevOpcodeArray: [] });
            this.setState({ newOpcodeArray: [] });
          } else {
            this.setState({ isLoading: true });
            this.getAgGridData();
            this.setState({
              setMessage: `Opcode update failed. Please contact support team to
            resolve.`
            });
            this.setState({ setOpenRow: true });
            this.setState({ isOpcodeLoading: false });
            this.setState({ isLoading: false });
          }
        });
    } else {
      this.setState({ disableTable: 'all' });
      for (var i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    }
  };

  refreshDisable = () => {
    this.setState({ showRefresh: 'none' });
  };

  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      columnKeys: [
        'opcode',
        'opcodedescription',
        'opcategory',
        //'paytype',
        'lastUsedOn',
        'usedLast12Mths',
        'fixedRate',
        'fixedRateValue',
        'fixedRateDate',
        'fixedRateParts',
        'maintenancePlan',
        'partsFixedRateDate',
        'partsFixedRateValue',
        'mpiItem',
        'menuSales',
        'gridExcluded',
        'department',
        'categorized'
      ],
      fileName: 'Opcode Categorizations',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Opcode Categorizations' },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: params => {
        // Format lastUsedOn to 'mm/dd/yy'
        if (params.column.getColId() === 'lastUsedOn' && params.value) {
          const date = new Date(params.value);
          if (!isNaN(date)) {
            const mm = String(date.getMonth() + 1).padStart(2, '0');
            const dd = String(date.getDate()).padStart(2, '0');
            const yy = String(date.getFullYear()).slice(-2);
            return `${mm}/${dd}/${yy}`;
          }
        }
        return params.value; // Default return for other columns
      }
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };

  handleClose = () => {
    this.setState({ setOpen: false });
  };

  handleCloseRow = () => {
    this.setState({ setOpenRow: false });
  };

  onBtClose = () => {
    this.setState({ setOpenAlert: false });
  };

  onBtCloseError = () => {
    this.setState({ setOpenAlertError: false });
  };

  addNewRow() {
    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.updateRowData({
      addIndex: 0,
      add: [{ id: '', name: '', serviceadvisor: '' }]
    });
    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.setFocusedCell(0, 'name', '');
  }
  onCellClicked = params => {
    const id = localStorage.getItem('opcodeId');
    $('.fixedRateDate').attr('readonly', 'true');
    let rowId = this.state.editedRowId;
    if (params.data.slno != id) {
      // this.gridApi.redrawRows();
      $(`.cancel-button-opcode`).hide();
      $(`.edit-button-opcode`).show();
      $(`.update-button-opcode`).hide();
      $(`#btncancelOpcode` + rowId).click();
      $('.grid-excluded').attr('disabled', 'disabled');
      $('.maintenance-plan').attr('disabled', 'disabled');
      $('.menu-sales').attr('disabled', 'disabled');
      $('.mpi-item').attr('disabled', 'disabled');
      $('.categorized').attr('disabled', 'disabled');
      $('.menu-sales').attr('disabled', 'disabled');

      $(`.opcategoryselect`).hide();
      $('.fixedRate').attr('disabled', 'disabled');
      $('.fixedRateValue').attr('disabled', 'disabled');
      $('.fixedRateParts').attr('disabled', 'disabled');
      $('.partsFixedRateValue').attr('disabled', 'disabled');
      // $('.fixedRateDate').attr('disabled', 'disabled');
    } else {
      $(`#btncancelOpcode${rowId}`).show();
      $(`#btnupdateopcode${rowId}`).show();
      $(`#btneditOpcode${rowId}`).hide();
    }
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: [
        'gridExcluded',
        'maintenancePlan',
        'mpiItem',
        'menuSales',
        'department',
        'opcategory',
        'fixedRate',

        'fixedRateParts'
      ],
      rowNodes: [params.node],
      force: true
    });
  }
  onFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      fixedRateOld: oldVal
    });

    this.setState({
      fixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onFixedDateChanged = (oldVal, newVal) => {
    console.log('onFixedDateChanged', oldVal, newVal);
    // const filterValues = e.api.getFilterModel();
    this.setState({
      fixedDateOld: oldVal
    });

    this.setState({
      fixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedRateOld: oldVal
    });

    this.setState({
      partsFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedDateChanged = (oldVal, newVal) => {
    console.log('onPartsFixedDateChanged', oldVal, newVal);
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedDateOld: oldVal
    });

    this.setState({
      partsFixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };

  onOpCategoryChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();

    this.setState({
      opCategoryOld: oldVal
    });

    this.setState({
      opCategoryNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  ondepartmentChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      deptOld: oldVal
    });
    this.setState({
      deptNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: [
        'gridExcluded',
        'maintenancePlan',
        'mpiItem',
        'menuSales',
        'department',
        'opcategory',
        'fixedRate',

        'fixedRateParts'
      ],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    this.setState({
      editedRowId: null
    });

    this.setState({
      isOpcodeEdited: false
    });
    this.gridApi.redrawRows();
  };
  onSortChanged = e => {
    $(`#btncancelOpcode` + this.state.editedRowId).click();
    this.setState({
      editedRowId: null
    });

    this.setState({
      isOpcodeEdited: false
    });
    this.gridApi.redrawRows();
  };

  resetReportGrid = () => {
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
    //this.gridApi.resetColumnState();
    window.sortStateOpcode = {};
    window.colStateOpcode = {};
    window.filterStateOpcode = {};
    this.gridApi.setColumnDefs(this.state.columnDefs);

    this.setState({ isOpcodeUpdated: false });
    this.setState({ isOpcodeRowUpdated: false });
    this.setState({
      selectedOpcode: ''
    });
    let rowId = this.state.editedRowId;
    if (rowId != null) {
      $(`#btncancelOpcode` + rowId).click();
    }
    console.log('ccc==pp', rowId);
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.setState({
      editedRowId: null
    });

    this.setState({
      isOpcodeEdited: false
    });
    this.state.gridcolumnApi.resetColumnState();
    this.gridApi.redrawRows();
    this.getAgGridData('reset');
  };
  renderBackButton = () => {
    this.props.history.push({
      pathname: this.props.history.location.state.isFrom
        ? this.props.history.location.state.isFrom
        : this.props.history.location.state.isLabor
        ? '/LaborItemization'
        : '/PartsItemization',
      state: {
        isFrom: 'opcodes',
        data: this.props.history.location.state.data
          ? this.props.history.location.state.data
          : this.props.history.location.state.itemizationData,
        tabSelection: this.props.history.location.state.itemizationTab
          ? this.props.history.location.state.itemizationTab
          : this.props.history.location.state.tabSelection,
        selectedButton: this.props.history.location.state.summaryTab
          ? this.props.history.location.state.summaryTab
          : '',
        parent: this.props.history.location.state.parent
          ? this.props.history.location.state.parent
          : '',
        opcode: this.props.history.location.state.opcode
          ? this.props.history.location.state.opcode
          : '',
        monthYear: this.props.history.location.state.monthYear
          ? this.props.history.location.state.monthYear
          : '',
        isExpanded: this.state.isExpanded,
        roExpanded: this.state.roExpanded,
        roNumber: this.props.history.location.state.roNumber
          ? this.props.history.location.state.roNumber
          : '',
        opcodePayType: this.props.history.location.state.opcodePayType
          ? this.props.history.location.state.opcodePayType
          : '',
        comparisonMonth1: this.props.history.location.state.comparisonMonth1
          ? this.props.history.location.state.comparisonMonth1
          : '',
        comparisonMonth2: this.props.history.location.state.comparisonMonth2
          ? this.props.history.location.state.comparisonMonth2
          : '',
        userHistory: this.props.history.location.state.userHistory
          ? this.props.history.location.state.userHistory
          : '',
        page: this.props.history.location.state.page
          ? this.props.history.location.state.page
          : ''
      }
    });
    this.gridApi.setFilterModel(null);
    // window.sortStateOpcode = {};
    // window.filterStateOpcode = {};
  };
  handleToggle = event => {
    // const newChecked = event.target.checked;
    const newChecked =
      event.target.textContent == 'View Non-CP Opcodes' ? true : false;
    this.setState({ toggleChecked: newChecked });

    // Store toggle state in localStorage
    localStorage.setItem('toggleChecked', newChecked);

    // Redirect to the appropriate page
    if (newChecked) {
      this.props.history.push('/NonCPOpcodes'); // Redirect to NonCPOpcodes when toggled ON
    }
  };
  render() {
    const { classes } = this.props;
    const {
      showRefresh,
      disableTable,
      setOpen,
      setOpenRow,
      setOpenAlert,
      setOpenAlertError,
      setMessage,
      showMessage
    } = this.state;

    //const coll = document.getElementsByClassName('edit-button-opcode');
    // if (disableTable === 'none') {
    //   for (let i = 0, len = coll.length; i < len; i++) {
    //     coll[i].style['background-color'] = '#ccc';
    //   }
    // }
    return (
      <div>
        <Dialog
          open={setOpenRow}
          onClose={this.handleCloseRow}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          // style={{ width: 350,height: 350 }}paddingLeft: '20px'
        >
          {/* <DialogTitle
            id="alert-dialog-title"
            style={{ paddingLeft: 108, paddingRight: 85 }}
          >
            OpCodes
          </DialogTitle> */}
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              //style={{ paddingLeft: 85, paddingRight: 85 }}
            >
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none',
                  paddingBottom: 10
                }}
              >
                {setMessage}
              </Typography>
              <Typography
                variant="h6"
                style={{
                  fontSize: 14,
                  color: '#003d6bc9 !important',
                  textTransform: 'none'
                }}
              >
                {showMessage
                  ? '(P.S : This edit does not require a site refresh as data would be reflected instantaneously.)'
                  : ''}
              </Typography>
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCloseRow} color="primary" autoFocus>
              OK
            </Button>
          </DialogActions>
        </Dialog>
        <Paper
          square
          style={{
            margin: 8

            // backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            // border: Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            // color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            showrefresh
            classes={{ flexContainer: classes.flexContainer }}
            aria-label="icon label tabs example"
            TabIndicatorProps={{ style: { display: 'none' } }}
          >
            <span
              style={{
                marginTop: 9,
                display:
                  this.props.history &&
                  this.props.history.location &&
                  this.props.history.location.state &&
                  (this.props.history.location.state.isFrom ==
                    '/LaborWorkMixAnalysis' ||
                    this.props.history.location.state.isFrom ==
                      '/PartsWorkMixAnalysis' ||
                    this.props.history.location.state.tabType) &&
                  // || this.props.history.location.state.itemizationTab
                  this.state.storeChange == false
                    ? 'block'
                    : 'none'
              }}
            >
              <Button
                variant="contained"
                className={'bck-btn'}
                onClick={this.renderBackButton}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button>
            </span>
            <Tab
              label={<div>Opcode Categorizations</div>}
              value="one"
              style={{
                pointerEvents: 'none',
                textTransform: 'none',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
            />
            <Button
              variant="outlined"
              style={
                this.state.isOpcodeRowUpdated
                  ? {
                      margin: 8,
                      padding: '0 6px !important',
                      pointerEvents: 'all',
                      color: '#fff',
                      float: 'right',
                      background:
                        process.env.REACT_APP_DEALER == 'Armatus'
                          ? '#003d6b'
                          : '#C2185B',
                      width: '8%',
                      height: 24,
                      fontSize: '12px',
                      position: 'relative',
                      textTransform: 'none'
                    }
                  : {
                      margin: 8,
                      padding: '0 6px !important',
                      pointerEvents: 'none',
                      color: '#ccc',
                      float: 'right',
                      background: '#fff',
                      width: '8%',
                      position: 'relative',
                      fontSize: '12px',
                      textTransform: 'none',
                      height: 24,
                      display: !!(
                        typeof this.props.keycloak.realmAccess.roles !==
                          'undefined' &&
                        this.props.keycloak.realmAccess.roles.includes(
                          'client'
                        ) === true
                      )
                        ? 'none'
                        : 'flex'
                    }
              }
              // style={{
              //   margin: 8,
              //   padding: 10,
              //   pointerEvents: 'all',
              //   color: '#fff',
              //   float: 'right',
              //   background: '#003d6b',
              //   width: '10%'
              // }}
              //disabled={this.state.newOpcodeArray <= 0 ? 'false' : 'true'handleClickSaveOpcode}
              onClick={this.handleClickSaveOpcode}
            >
              Save Changes
            </Button>

            <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{ paddingRight: 19, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
            {/* {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
            this.props.keycloak.realmAccess.roles.includes('client') != true ? (
              <Tooltip title="View Non-Customer Pay Opcodes">
                <Link
                  component={RouterLink}
                  to="/NonCPOpcodes"
                  underline="hover"
                  style={{
                    fontWeight: 'bold',
                    color: '#003d6b',
                    fontSize: 13,
                    display: 'flex',
                    alignItems: 'center',
                    gap: '5px' // Space between text and icon
                  }}
                >
                  <ViewModuleIcon fontSize="small" />
                </Link>
              </Tooltip>
            ) : (
              ''
            )} */}
            {/* {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
            this.props.keycloak.realmAccess.roles.includes('client') !==
              true ? (
              <Tooltip title="View Non-Customer Pay Opcodes">
                <Switch
                  checked={this.state.toggleChecked}
                  onChange={this.handleToggle}
                  color="primary"
                />
              </Tooltip>
            ) : (
              ''
            )} */}
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, classes.reset, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>
        {this.state.isLoading === true ||
        this.state.isOpcodeLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <span style={{ display: 'flex', justifyContent: 'space-between' }}>
          {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
          this.props.keycloak.realmAccess.roles.includes('client') != true ? (
            <>
              <Typography
                style={{
                  paddingLeft: '10px',
                  display: this.state.isLoading == true ? 'none' : 'block',
                  fontWeight: 'bold',
                  color: '#003d6b',
                  fontSize: 13
                }}
              >
                1) Please edit your opcode(s) here. You may edit as many as you
                like simultaneously, and then please click “Save Changes”.
              </Typography>
              {/* {this.state.isLoading !== true && (
              <Tooltip title="View Non-Customer Pay Opcodes">
                <Link
                  component={RouterLink}
                  to="/NonCPOpcodes"
                  underline="hover"
                  style={{
                    fontWeight: 'bold',
                    color: '#003d6b',
                    fontSize: 13,
                    paddingLeft: '10px'
                  }}
                >
                  Non-CP Opcodes
                </Link>
              </Tooltip>
            )} */}
            </>
          ) : (
            ''
          )}
          {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
          this.props.keycloak.realmAccess.roles.includes('client') !== true ? (
            // <Tooltip title="View Non-Customer Pay Opcodes">
            //   <Switch
            //     checked={this.state.toggleChecked}
            //     onChange={this.handleToggle}
            //     color="primary"
            //   />
            // </Tooltip>
            // <Link color="secondary" onChange={this.handleToggle}>
            <Link href="#" underline="always" onClick={this.handleToggle}>
              <Typography
                style={{
                  paddingLeft: '10px',
                  display: this.state.isLoading == true ? 'none' : 'block',
                  fontWeight: 'bold',
                  color: '#003d6b',
                  fontSize: 13,
                  marginRight: 20
                }}
              >
                View Non-CP Opcodes
              </Typography>
            </Link>
          ) : (
            ''
          )}
        </span>
        <div
          id={
            typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
            this.props.keycloak.realmAccess.roles.includes('client') === true
              ? 'data-tab_opcodes_client'
              : 'data-tab_opcodes'
          }
          className={clsx(
            classes.opcodetable,
            'ag-theme-balham',
            'opcode_table'
          )}
          style={{
            height: window.innerHeight - 200 + 'px',
            // width: !!(
            //   typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
            //   this.props.keycloak.realmAccess.roles.includes('client') === true
            // )
            //   ? '1110px'
            //   : '1200px',
            alignContent: 'center',
            marginLeft: '8px',
            paddingRight: '16px',
            display: this.state.isLoading == true ? 'none' : 'block'
            //pointerEvents: disableTable
          }}
        >
          <AgGridReact
            className="ag-theme-balham grid-cell-centered"
            style={{
              height: '500px',
              width: '100%',
              marginTop: '10px !important'
            }}
            overlayLoadingTemplate={
              '<span className="ag-overlay-loading-center">Please wait while your rows are loading...</span>'
            }
            getRowId={this.state.getRowId}
            getRowStyle={this.getRowStyle}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            onRowEditingStarted={this.onRowEditingStarted}
            onRowEditingStopped={this.onRowEditingStopped}
            // onCellValueChanged={this.onCellValueChanged}
            onFilterChanged={this.onFilterChanged}
            onSortChanged={this.onSortChanged}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            context={this.state.context}
            frameworkComponents={this.state.frameworkComponents}
            editType={this.state.editType}
            suppressClickEdit={true}
            onCellClicked={this.onCellClicked}
            floatingFilter={true}
            suppressColumnVirtualisation={true}
            enableRangeSelection={true}
            //animateRows={true}
            // enableCharts={true}
            //suppressRowClickSelection={true}
            alwaysShowHorizontalScroll
            headerHeight={this.state.headerHeight}
            suppressDragLeaveHidesColumns={true}
            //suppressHorizontalScroll={true}
            suppressContextMenu={true}
          />
        </div>
        <Dialog
          //fullWidth
          //maxWidth="sm"
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openSaveDialog}
        >
          {/* <DialogTitle id="confirmation-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Save changes
            </Typography>
          </DialogTitle> */}
          <DialogContent>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to continue?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={this.handleCancel}>
              Cancel
            </Button>
            <Button onClick={this.handleSave} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          //fullWidth
          //maxWidth="sm"
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openConfirmDialog}
        >
          {/* <DialogTitle id="confirmation-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Save changes
            </Typography>
          </DialogTitle> */}
          <DialogContent>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Please click ok to save your changes before continue.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={this.handleCancel}>
              Cancel
            </Button>
            <Button onClick={this.handleSave} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          //fullWidth
          //maxWidth="sm"
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openAlertDialog}
        >
          {/* <DialogTitle id="confirmation-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Save changes
            </Typography>
          </DialogTitle> */}
          <DialogContent>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Your changes are not saved yet. Do you want to proceed?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={this.handleAlertCancel}>
              Cancel
            </Button>
            <Button onClick={this.handleAlertSave} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setDashboardReloadStatus: data =>
      dispatch({ type: SET_DASHBOARD_RELOAD_STATUS, payload: data }),
    setRefreshStatus: data =>
      dispatch({ type: SET_REFRESH_STATUS, payload: data })
  };
}
OPCodes.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func,
  setDashboardReloadStatus: PropTypes.func
};
const styles = theme => ({
  back: {
    float: 'right',
    marginTop: '10px',
    marginRight: '17px'
  },
  reset: {
    width: 'auto',
    margin: '8px !important'
  },
  opcodetable: {
    '@media (max-width: 1920px)': {
      width: 'auto !important'
    },
    '@media (max-width: 1440px)': {
      width: '1140px !important'
    },
    '@media (max-width: 1280px)': {
      width: '1230px !important'
    },
    '@media (min-width: 2304px)': {
      width: '1730px !important'
    }
  },
  flexContainer: {
    alignItems: 'center'
  }
});
export default connect(mapStateToProps, mapDispatchToProps)(
  withStyles(styles)(withKeycloak(OPCodes)),
  OPCodes
);
