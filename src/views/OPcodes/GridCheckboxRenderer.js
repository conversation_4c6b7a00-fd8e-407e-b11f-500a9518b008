import React, { Component } from 'react';

export default class extends Component {
  constructor(props) {
    super(props);
    this.checkedHandler = this.checkedHandler.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'gridExcluded') {
      this.props.node.setDataValue(colId, checked == true ? 0 : 1);

      if (!checked) {
        var rowNode = this.props.api.getDisplayedRowAtIndex(
          this.props.context.componentParent.state.editedRowId
        );
        rowNode.setDataValue('fixedRate', 0);

        this.props.context.componentParent.onFixedDateChanged(
          this.props.data.fixedRateDate,
          null
        );
        this.props.context.componentParent.onFixedRateChanged(
          this.props.data.fixedRateValue,
          null
        );
      }
      this.props.api.refreshCells({
        columns: ['fixedRate', 'fixedRateParts'],
        rowNodes: [this.props.node],
        force: true
      });
      // this.props.api.refreshCells({
      //   columns: ['fixedRate', 'fixedRateValue', 'fixedRateDate'],
      //   rowNodes: [this.props.node],
      //   force: true
      // });
    } else if (this.props.column.colId == 'menuSales') {
      this.props.node.setDataValue(colId, checked == true ? 'Y' : 'N');
    } else {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
  }
  render() {
    return this.props.colDef.field == 'gridExcluded' ? (
      <input
        type="checkbox"
        className="grid-excluded"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : this.props.context.componentParent.state.opCategoryNew != 'REPAIR'
            ? true
            : this.props.context.componentParent.state.deptNew == 'Body Shop'
            ? true
            : false
        }
        onClick={this.checkedHandler}
        checked={this.props.value == 0 ? true : false}
      />
    ) : this.props.colDef.field == 'maintenancePlan' ? (
      <input
        type="checkbox"
        className="maintenance-plan"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : false
        }
        onClick={this.checkedHandler}
        checked={this.props.value == 1 ? true : false}
      />
    ) : this.props.colDef.field == 'mpiItem' ? (
      <input
        type="checkbox"
        className="mpi-item"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : false
        }
        onClick={this.checkedHandler}
        checked={this.props.value == 1 ? true : false}
      />
    ) : this.props.colDef.field == 'menuSales' ? (
      <input
        type="checkbox"
        className="menu-sales"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : false
        }
        onClick={this.checkedHandler}
        checked={this.props.value == 'Y' ? true : false}
      />
    ) : (
      ''
    );
  }
}
