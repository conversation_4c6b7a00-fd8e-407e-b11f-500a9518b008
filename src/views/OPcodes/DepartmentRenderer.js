import React, { Component } from 'react';
import { FormControl, TextField } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import clsx from 'clsx';

class DepartmentRenderer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      department: this.props.data.department
    };
    this.handledepartmentChange = this.handledepartmentChange.bind(this);
  }

  handledepartmentChange(event) {
    let colId = this.props.column.colId;
    this.setState({
      department: event.target.value
    });
    this.props.context.componentParent.ondepartmentChanged(
      this.props.data.department,
      event.target.value
    );
    // console.log('ccc---ccc', event.target.value);
    this.props.node.setDataValue('department', event.target.value);
    if (event.target.value == 'Service') {
      if (
        typeof this.props.context.componentParent.state.opCategoryNew !=
          'undefined' &&
        this.props.context.componentParent.state.opCategoryNew == 'REPAIR'
      ) {
        this.props.node.setDataValue('gridExcluded', 0);
      } else {
        this.props.node.setDataValue('gridExcluded', 1);
      }
      //this.props.node.setDataValue('fixedRate', 1);
      this.props.api.refreshCells({
        columns: [
          'gridExcluded',
          'fixedRate',
          'fixedRateValue',
          'fixedRateDate',
          'fixedRateParts'
        ],
        rowNodes: [this.props.node],
        force: true
      });
    } else {
      var prevOpcode = this.props.data.opcode;
      var prevPaytype = this.props.data.paytype;
      let valArr = this.props.context.componentParent.state.prevOpcodeArray.filter(
        function(o) {
          return o.opcode == prevOpcode && o.paytype == prevPaytype;
        }
      );

      this.props.node.setDataValue('gridExcluded', 1);
      //this.props.node.setDataValue('gridExcluded', valArr[0].gridExcluded);
      this.props.node.setDataValue('fixedRate', 0);
      this.props.node.setDataValue('fixedRateParts', 0);
      this.props.api.refreshCells({
        columns: [
          'gridExcluded',
          'fixedRate',
          'fixedRateValue',
          'fixedRateDate',
          'fixedRateParts'
        ],
        rowNodes: [this.props.node],
        force: true
      });
    }

    // if (this.props.data.fixedRate == 1 && checked == true) {
    //   this.setState({
    //     isFixedRateChecked: true
    //   });
    // }
  }
  render() {
    const { classes } = this.props;
    let data = localStorage.getItem('category');

    const split_string = data.split(',');
    return (
      <>
        <select
          style={{
            width: 100,
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          onBlur={e => this.handledepartmentChange(e)}
          onChange={e => this.handledepartmentChange(e)}
          value={this.state.department}
          className={clsx(
            classes.formControlSelect,

            'departmentselect'
          )}
          name="departmentselect"
          id={'departmentselect-' + this.props.rowIndex}
        >
          {/* <option disabled value={'-----'}>
            {'-----'}
          </option> */}
          <option value={'Service'}>{'Service'}</option>
          <option value={'Body Shop'}>{'Body Shop'}</option>
        </select>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                  this.props.rowIndex
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.department}
        </span>
      </>
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    width: 60,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: '8px !important',
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '75px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,
    width: 80,
    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 3
  }
});
export default withStyles(styles)(DepartmentRenderer);
