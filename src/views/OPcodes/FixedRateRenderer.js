import React, { Component } from 'react';
import { FormControl } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputAdornment from '@material-ui/core/InputAdornment';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import clsx from 'clsx';
import moment from 'moment';
import 'src/input-style.css';

class FixedRateRenderer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isFixedRateChecked: false,
      isValid:
        this.props.data.partsFixedRateValue != null &&
        this.props.data.partsFixedRateValue != 'N/A'
          ? this.props.data.partsFixedRateValue.split(' ')[0] == 'Cost' ||
            this.props.data.partsFixedRateValue.split(' ')[0] == 'List'
            ? true
            : false
          : true,
      fixedValue:
        this.props.data.fixedRateValue == null
          ? ''
          : this.props.data.fixedRateValue,
      partsMarkup:
        this.props.data.partsFixedRateValue != null &&
        this.props.data.partsFixedRateValue != 'N/A'
          ? this.props.data.partsFixedRateValue.split(' ')[0]
          : 'Cost',
      fixedDate:
        this.props.data.fixedRateDate != null
          ? this.props.data.fixedRateDate
          : new Date(),
      partsfixedValue:
        this.props.data.partsFixedRateValue != null &&
        this.props.data.fixedRateParts == 1
          ? this.props.data.partsFixedRateValue.split(' ')[1]
          : '',
      // partsfixedValue:
      //   this.props.data.partsFixedRateValue == null
      //     ? ''
      //     : this.props.data.partsFixedRateValue,
      partsfixedDate:
        this.props.data.partsFixedRateDate != null
          ? this.props.data.partsFixedRateDate
          : new Date()
    };
    this.checkedHandler = this.checkedHandler.bind(this);
    this.handleCallback = this.handleCallback.bind(this);
    this.handleFixedRateChange = this.handleFixedRateChange.bind(this);
    this.handleMarkupChange = this.handleMarkupChange.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;
    if (this.props.column.colId == 'fixedRate') {
      if (this.props.column.colId == 'fixedRate') {
        //this.props.api.refreshCells({ rowNodes: [this.props.node], columns: [colId] });
        this.props.node.setDataValue(colId, checked == true ? 1 : 0);
        this.props.api.refreshCells({
          rowNodes: [this.props.node],
          columns: [colId]
        });
      }
      if (this.props.data.fixedRate == 1 && checked == true) {
        this.props.api.refreshCells({
          columns: ['fixedRate'],
          rowNodes: [this.props.node],
          force: true
        });
        this.props.context.componentParent.onFixedDateChanged(
          this.props.data.fixedRateDate,
          this.props.data.fixedRateDate == null ||
            this.props.fixedRateDate == ''
            ? moment(new Date()).format('YYYY-MM-DD')
            : this.props.data.fixedRateDate
        );
        this.props.context.componentParent.onFixedRateChanged(
          this.props.data.fixedRateValue,
          this.props.data.fixedRateValue
        );
      } else {
        // this.props.api.refreshCells({
        //   columns: ['fixedRate'],
        //   rowNodes: [this.props.node],
        //   force: true
        // });
        this.props.context.componentParent.onFixedRateChanged(
          this.props.data.fixedRateValue,
          null
        );
      }
    } else if (this.props.column.colId == 'fixedRateParts') {
      if (this.props.column.colId == 'fixedRateParts') {
        this.props.node.setDataValue(colId, checked == true ? 1 : 0);
      }

      if (this.props.data.fixedRateParts == 1 && checked == true) {
        this.props.api.refreshCells({
          columns: ['fixedRateParts'],
          rowNodes: [this.props.node],
          force: true
        });

        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedRateDate,
          this.props.data.partsFixedRateDate == null ||
            this.props.partsFixedRateDate == ''
            ? moment(new Date()).format('YYYY-MM-DD')
            : this.props.data.partsFixedRateDate
        );
        // this.props.context.componentParent.onPartsFixedRateChanged(
        //   this.props.data.partsFixedRateValue,
        //   this.props.data.partsFixedRateValue
        // );
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedRateValue,
          'Cost'
        );
      } else {
        // this.props.api.refreshCells({
        //   columns: ['fixedRateParts'],
        //   rowNodes: [this.props.node],
        //   force: true
        // });
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedRateValue,
          'Cost'
        );
      }
    }
  }
  handleCallback = (start, end, label) => {
    let colId = this.props.column.colId;
    if (this.props.column.colId == 'fixedRate') {
      this.setState({
        fixedDate: start.format('MM/DD/YY')
      });

      if (this.props.data.fixedRate == 1) {
        this.props.context.componentParent.onFixedDateChanged(
          this.props.data.fixedRateDate,
          start.format('YYYY-MM-DD')
        );
      } else {
        this.props.context.componentParent.onFixedDateChanged(
          this.props.data.fixedRateDate,
          null
        );
      }
    } else if (this.props.column.colId == 'fixedRateParts') {
      this.setState({
        partsfixedDate: start.format('MM/DD/YY')
      });

      if (this.props.data.fixedRateParts == 1) {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedRateDate,
          start.format('YYYY-MM-DD')
        );
      } else {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedRateDate,
          null
        );
      }
    }
  };

  handleFixedRateChange(event) {
    let colId = this.props.column.colId;
    if (this.props.column.colId == 'fixedRate') {
      this.setState({
        fixedValue: event.target.value
      });

      if (this.props.data.fixedRate == 1) {
        this.props.context.componentParent.onFixedRateChanged(
          this.props.data.fixedRateValue,
          event.target.value
        );
      } else {
        this.props.context.componentParent.onFixedRateChanged(
          this.props.data.fixedRateValue,
          null
        );
      }
      if (this.props.data.fixedRate == 1) {
        this.props.context.componentParent.onFixedDateChanged(
          this.props.data.fixedRateDate,
          this.props.context.componentParent.state.fixedDateNew != ''
            ? this.props.context.componentParent.state.fixedDateNew
            : this.props.data.fixedRateDate == null ||
              this.props.fixedRateDate == ''
            ? moment(new Date()).format('YYYY-MM-DD')
            : this.props.data.fixedRateDate
        );
      }
    } else if (this.props.column.colId == 'fixedRateParts') {
      this.setState({
        partsfixedValue: event.target.value
      });

      if (this.props.data.fixedRateParts == 1) {
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedRateValue,
          (this.state.partsMarkup == 'N/A' ? 'Cost' : this.state.partsMarkup) +
            ' ' +
            (event.target.value == 'N/A' ? 'Cost' : event.target.value)
        );
      } else {
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedRateValue,
          null
        );
      }
      if (this.props.data.fixedRateParts == 1) {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedRateDate,
          typeof this.props.context.componentParent.state.partsFixedDateNew !=
            'undefined' &&
            this.props.context.componentParent.state.partsFixedDateNew != ''
            ? this.props.context.componentParent.state.partsFixedDateNew
            : this.props.data.partsFixedRateDate == null ||
              this.props.partsFixedRateDate == ''
            ? moment(new Date()).format('YYYY-MM-DD')
            : this.props.data.partsFixedRateDate
        );
      }
    }
  }
  handleMarkupChange(event) {
    this.setState({
      partsMarkup: event.target.value
    });

    if (this.props.data.fixedRateParts == 1) {
      if (event.target.value == 'Cost' || event.target.value == 'List') {
        this.setState({
          isValid: true
        });
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedRateValue,
          event.target.value == 'N/A' ? 'Cost' : event.target.value
        );
      } else {
        this.setState({
          isValid: false
        });
        var val =
          this.state.partsfixedValue != null && this.state.partsfixedValue != ''
            ? this.state.partsfixedValue
            : '';
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedRateValue,
          (event.target.value == 'N/A' ? 'Cost' : event.target.value) +
            ' ' +
            val
        );
      }
    } else {
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsFixedRateValue,
        null
      );
    }
    if (this.props.data.fixedRateParts == 1) {
      this.props.context.componentParent.onPartsFixedDateChanged(
        this.props.data.partsFixedratedate,
        this.props.context.componentParent.state.partsFixedDateNew != ''
          ? this.props.context.componentParent.state.partsFixedDateNew
          : this.props.data.partsFixedratedate == null ||
            this.props.partsFixedratedate == ''
          ? moment(new Date()).format('YYYY-MM-DD')
          : this.props.data.partsFixedratedate
      );
    }
  }
  render() {
    const { classes } = this.props;
    // var regExParts = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    var regExParts = /^\d{0,3}(\.\d{0,3})?$/;
    var regExfixedValue = /^\d{0,3}(\.\d{0,3})?$/;
    //var regExfixedValue = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    var decVal = /^(\d*\.{0,1}\d{0,2}$)/;

    return this.props.colDef.field == 'fixedRate' ? (
      <div
        style={{
          display: 'inline-flex',
          gap: 3,

          alignItems: 'center'
        }}
      >
        <input
          type="checkbox"
          className="fixedRate"
          disabled={
            this.props.context.componentParent.state.editedRowId == null
              ? true
              : this.props.context.componentParent.state.editedRowId !=
                this.props.rowIndex
              ? true
              : this.props.data.gridExcluded != 0
              ? true
              : false
          }
          onClick={this.checkedHandler}
          checked={
            this.props.value == 0
              ? false
              : this.props.data.gridExcluded != 0
              ? false
              : true
          }
        />

        <FormControl
          variant="outlined"
          style={{
            width: '50%',
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.fixedRate == 1 &&
              this.props.data.gridExcluded == 0
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
        >
          <OutlinedInput
            className={clsx(classes.formControl, 'fixedRateValue')}
            id="outlined-adornment-weight"
            title="Labor Fixed Rate($)"
            value={this.state.fixedValue}
            error={
              this.props.context.componentParent.state.fixedRateError == null
                ? false
                : this.props.context.componentParent.state.fixedRateError ==
                  this.props.rowIndex
                ? true
                : false
            }
            // onKeyPress={event => {
            //   if (!/[0-9]|\./.test(event.key)) {
            //     event.preventDefault();
            //   }
            // }}
            onBlur={this.handleFixedRateChange}
            onChange={e => {
              if (
                e.target.value == '' ||
                (regExfixedValue.test(e.target.value) &&
                  decVal.test(e.target.value))
              ) {
                this.setState({
                  fixedValue: e.target.value
                });
              }
            }}
            // onChange={this.onFixedRateChange}
            startAdornment={
              <InputAdornment
                classes={{ root: classes.adorment }}
                position="start"
                disableTypography={true}
              >
                $
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>

        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 0
                ? 'none'
                : this.props.context.componentParent.state.editedRowId ==
                    null && this.props.data.fixedRate == 1
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                  this.props.rowIndex
                ? 'block'
                : this.props.data.fixedRate == 1 &&
                  this.props.data.gridExcluded != 0
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.gridExcluded == 0 &&
            this.props.data.fixedRate == 1 &&
            this.props.data.fixedRateValue != null &&
            this.props.data.fixedRateValue != '' &&
            '$' + this.props.data.fixedRateValue}
        </span>

        <FormControl
          variant="outlined"
          margin="dense"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.fixedRate == 1 &&
              this.props.data.gridExcluded == 0
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          className={clsx(classes.formControlDate, 'input-container')}
        >
          <DateRangePicker
            initialSettings={{
              // maxDate: {
              //   date: new Date(),
              // },
              // minDate:{
              //   date: (this.props.selectedDates[1])
              // },
              locale: {
                format: 'MM/DD/YY',
                separator: ' - '
              },
              autoUpdateInput: true,
              showDropdowns: true,
              autoApply: true,
              singleDatePicker: true,
              // minDate: moment().toDate(),
              //maxDate: moment().toDate(),
              // alwaysShowCalendars: true,
              applyClass: clsx(classes.calButton, 'apply-btn'),
              cancelClass: clsx(classes.calButton, 'apply-btn'),
              startDate: new Date(this.state.fixedDate)
              //showDropdowns: true
            }}
            value={this.state.fixedDate}
            onCallback={this.handleCallback}
          >
            <input
              type="text"
              className={clsx(classes.fixeddatepicker, 'fixedRateDate')}
              id="picker"
              name="picker"
              aria-labelledby="label-picker"
            />
            {/* <TextField
          id="outlined-basic"
          label="Select Date"
          size="small"
          //onChange={}
          value={this.state.value}
          variant="outlined"
        /> */}
          </DateRangePicker>
          {/* <label class="labelpicker" for="picker" id="label-picker">
          <div class="textpicker">Select Date</div>
        </label> */}
        </FormControl>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 0
                ? 'none'
                : this.props.context.componentParent.state.editedRowId ==
                    null && this.props.data.fixedRate == 1
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                  this.props.rowIndex
                ? 'block'
                : this.props.data.fixedRate == 1 &&
                  this.props.data.gridExcluded != 0
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.gridExcluded == 0 &&
            this.props.data.fixedRate == 1 &&
            this.props.data.fixedRateDate != null &&
            moment(new Date(this.props.data.fixedRateDate)).format('MM/DD/YY')}
        </span>
      </div>
    ) : this.props.colDef.field == 'fixedRateParts' ? (
      <div
        style={{
          display: 'inline-flex',
          gap: 3,

          alignItems: 'center'
        }}
      >
        <input
          type="checkbox"
          className="fixedRateParts"
          disabled={
            this.props.context.componentParent.state.editedRowId == null
              ? true
              : this.props.context.componentParent.state.editedRowId !=
                this.props.rowIndex
              ? true
              : this.props.data.gridExcluded != 0
              ? true
              : false
          }
          onClick={this.checkedHandler}
          checked={
            this.props.value == 0
              ? false
              : this.props.data.gridExcluded != 0
              ? false
              : true
          }
        />
        <div
          style={{
            display: 'inline-flex',
            gap: 3,

            alignItems: 'center'
          }}
        >
          <select
            disabled={
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.fixedRateParts == 1
                ? false
                : 'true'
            }
            style={{
              display:
                this.props.context.componentParent.state.editedRowId != null &&
                this.props.data.fixedRateParts == 1 &&
                this.props.data.gridExcluded == 0
                  ? this.props.context.componentParent.state.editedRowId ==
                    this.props.rowIndex
                    ? 'block'
                    : 'none'
                  : 'none'
            }}
            onChange={e => this.handleMarkupChange(e)}
            value={this.state.partsMarkup}
            className={clsx(classes.formControlSelect, 'partsFixedRateSelect')}
            name="parts-markups"
            id="parts-markups"
          >
            <option value="Cost">Cost</option>
            <option value="Cost-">Cost-</option>
            <option value="Cost+">Cost+</option>
            <option value="Cost%">Cost%</option>
            <option value="List">List</option>
            <option value="List-">List-</option>
            <option value="List+">List+</option>
            <option value="List%">List%</option>
          </select>

          <FormControl
            variant="outlined"
            disabled={
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.fixedRateParts == 1 &&
              !this.state.isValid
                ? false
                : 'true'
            }
            className="partsFixedRateValue"
            style={{
              width: '36%',
              display:
                this.props.context.componentParent.state.editedRowId != null &&
                this.props.data.fixedRateParts == 1 &&
                this.props.data.gridExcluded == 0
                  ? this.props.context.componentParent.state.editedRowId ==
                    this.props.rowIndex
                    ? 'block'
                    : 'none'
                  : 'none'
            }}
          >
            <OutlinedInput
              className={clsx(classes.formControl, 'fixedRateValue')}
              classes={{ input: classes.adorment }}
              title="Parts Fixed Rate(%)"
              id="partsfixedValue"
              value={this.state.partsfixedValue}
              error={
                this.props.context.componentParent.state.partsFixedRateError ==
                null
                  ? false
                  : this.props.context.componentParent.state
                      .partsFixedRateError == this.props.rowIndex
                  ? true
                  : false
              }
              // onKeyPress={event => {
              //   if (!/[0-9]|\./.test(event.key)) {
              //     event.preventDefault();
              //   }
              // }}
              onBlur={this.handleFixedRateChange}
              // onChange={e => {
              //   this.setState({
              //     partsfixedValue: e.target.value
              //   });
              // }}
              onChange={e =>
                (e.target.value === '' ||
                  (regExParts.test(e.target.value) &&
                    decVal.test(e.target.value))) &&
                this.setState({
                  partsfixedValue: e.target.value
                })
              }
              endAdornment={
                <InputAdornment
                  classes={{ input: classes.adorment }}
                  position="end"
                  disableTypography={true}
                >
                  %
                </InputAdornment>
              }
              aria-describedby="outlined-weight-helper-text"
              inputProps={{
                'aria-label': 'Fixed Rate Value'
              }}
              labelWidth={0}
            />
          </FormControl>

          <span
            style={{
              display:
                this.props.context.componentParent.state.editedRowId == null &&
                this.props.data.fixedRateParts == 1
                  ? 'block'
                  : this.props.context.componentParent.state.editedRowId !=
                      this.props.rowIndex && this.props.data.fixedRateParts == 1
                  ? 'block'
                  : 'none'
            }}
          >
            {this.props.data.partsFixedRateValue != null &&
            this.props.data.partsFixedRateValue != '' &&
            this.props.data.partsFixedRateValue != 'N/A' &&
            this.props.data.partsFixedRateValue != 'Cost' &&
            this.props.data.partsFixedRateValue != 'List'
              ? this.props.data.partsFixedRateValue + '%'
              : this.props.data.partsFixedRateValue}
          </span>
          <FormControl
            variant="outlined"
            margin="dense"
            style={{
              display:
                this.props.context.componentParent.state.editedRowId != null &&
                this.props.data.fixedRateParts == 1 &&
                this.props.data.gridExcluded == 0
                  ? this.props.context.componentParent.state.editedRowId ==
                    this.props.rowIndex
                    ? 'block'
                    : 'none'
                  : 'none'
            }}
            className={clsx(classes.formControlDate, 'input-container')}
          >
            <DateRangePicker
              initialSettings={{
                // maxDate: {
                //   date: new Date(),
                // },
                // minDate:{
                //   date: (this.props.selectedDates[1])
                // },
                locale: {
                  format: 'MM/DD/YY',
                  separator: ' - '
                },
                autoUpdateInput: true,
                showDropdowns: true,
                autoApply: true,
                singleDatePicker: true,
                // minDate: moment().toDate(),
                //maxDate: moment().toDate(),
                // alwaysShowCalendars: true,
                applyClass: clsx(classes.calButton, 'apply-btn'),
                cancelClass: clsx(classes.calButton, 'apply-btn'),
                startDate: new Date(this.state.partsfixedDate)
                //showDropdowns: true
              }}
              value={this.state.partsfixedDate}
              onCallback={this.handleCallback}
            >
              <input
                type="text"
                className={clsx(classes.fixeddatepicker, 'partsFixedRateDate')}
                id="picker"
                name="picker"
                aria-labelledby="label-picker"
              />
              {/* <TextField
          id="outlined-basic"
          label="Select Date"
          size="small"
          //onChange={}
          value={this.state.value}
          variant="outlined"
        /> */}
            </DateRangePicker>
            {/* <label class="labelpicker" for="picker" id="label-picker">
          <div class="textpicker">Select Date</div>
        </label> */}
          </FormControl>
          <span
            style={{
              display:
                this.props.context.componentParent.state.editedRowId != null &&
                this.props.data.gridExcluded != 0
                  ? 'none'
                  : this.props.context.componentParent.state.editedRowId ==
                      null && this.props.data.fixedRateParts == 1
                  ? 'block'
                  : this.props.context.componentParent.state.editedRowId !=
                    this.props.rowIndex
                  ? 'block'
                  : this.props.data.fixedRateParts == 1 &&
                    this.props.data.gridExcluded != 0
                  ? 'block'
                  : 'none'
            }}
          >
            {this.props.data.gridExcluded == 0 &&
              this.props.data.fixedRateParts == 1 &&
              this.props.data.partsFixedRateDate != null &&
              moment(new Date(this.props.data.partsFixedRateDate)).format(
                'MM/DD/YY'
              )}
          </span>
        </div>
      </div>
    ) : (
      ''
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    backgroundColor: '#fff',
    marginTop: 2,
    paddingLeft: 8,
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '80px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,

    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 4
  }
});
export default withStyles(styles)(FixedRateRenderer);
