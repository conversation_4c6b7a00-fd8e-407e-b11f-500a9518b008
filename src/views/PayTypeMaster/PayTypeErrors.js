import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  Fade,
  LinearProgress,
  Typography,
  Paper
} from '@material-ui/core';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { connect } from 'react-redux';
import { SET_PAY_TYPE_ERROR, SET_PAY_TYPE_ERROR_COUNT } from 'src/actions';
import {
  UPDATE_PAY_TYPE_MASTER_BY_PAYTYPE,
  ADD_REFRESH_STATUS,
  CLIENT_AUDITS
} from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClient';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgresWrite';
import {
  getAllPayTypeErrors,
  getRefreshViewsStatus,
  getPayTypeMasterDetails,
  updateRefreshViewsData,
  updateRefreshStatus
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import moment from 'moment';
import PayTypeDrilldown from './PayTypeDrilldown';
import LoaderSkeleton from '../../components/LoaderSkeleton';
class PayTypeErrors extends React.Component {
  componentDidUpdate(prevProps) {
    if (
      prevProps.session.storeSelected &&
      JSON.parse(localStorage.getItem('selectedStoreId'))
    ) {
      if (
        JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
        JSON.parse(prevProps.session.storeSelected)[0]
      ) {
        this.getAgGridData();
      }
    }
  }
  static defaultProps = {};

  componentDidMount() {
    const user_id = this.props.session.user.email;
    var coll = document.getElementsByClassName('edit-button');

    if (
      this.props.keycloak.realmAccess.roles.includes('admin') == true ||
      this.props.keycloak.realmAccess.roles.includes('superadmin') == true
    ) {
      // if (this.props.disableTable == 'all' && this.props.showRefresh == 'all') {

      //   this.setState({ disableTable: 'all' });
      //   this.setState({ showRefresh: 'all' });
      //   setTimeout(function() {
      //     for (var i = 0, len = coll.length; i < len; i++) {
      //       coll[i].style['background-color'] = '#384163';
      //     }
      //   }, 500);
      // } else if (
      //   this.props.disableTable == 'all' &&
      //   this.props.showRefresh == 'none'
      // ) {

      //   this.setState({ disableTable: 'all' });
      //   this.setState({ showRefresh: 'none' });
      //   setTimeout(function() {
      //     for (var i = 0, len = coll.length; i < len; i++) {
      //       coll[i].style['background-color'] = '#384163';
      //     }
      //   }, 500);
      // } else if (
      //   this.props.disableTable == 'none' &&
      //   this.props.showRefresh == 'all'
      // ) {

      //   this.setState({ disableTable: 'none' });
      //   this.setState({ showRefresh: 'all' });
      // } else if (
      //   this.props.disableTable == 'none' &&
      //   this.props.showRefresh == 'none'
      // ) {

      //   setTimeout(function() {
      //     for (var i = 0, len = coll.length; i < len; i++) {
      //       coll[i].style['background-color'] = '#ccc';
      //     }
      //   }, 500);
      //   this.setState({ disableTable: 'none' });
      //   this.setState({ showRefresh: 'none' });
      // }

      // if (this.state.setRunningStatus == true) {

      //   var coll = document.getElementsByClassName('edit-button');
      //   let status = getRefreshViewsStatus('<EMAIL>');
      //   const interval = setInterval(() => {
      //     getRefreshViewsStatus(status, result => {

      //       if (
      //         result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
      //           .status == 'COMPLETE'
      //       ) {
      //         this.setState({ setRunningStatus: false });
      //         this.setState({ disableTable: 'all' });
      //         this.setState({ showRefresh: 'none' });
      //         this.setState({ setColor: '#ccc' });
      //         this.setState({ setBackground: '#fff' });
      //         $('.edit-button').show();
      //         this.setState({ setOpenAlert: false });
      //         setTimeout(function() {
      //           for (var i = 0, len = coll.length; i < len; i++) {
      //             coll[i].style['background-color'] = '#384163';
      //           }
      //         }, 1000);
      //         clearInterval(interval);
      //       } else if (
      //         result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
      //           .status == 'RUNNING' ||
      //         result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
      //           .status == 'REFRESHED' ||
      //         result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
      //           .status == 'SERVERSTARTED'
      //       ) {
      //         this.setState({ setRunningStatus: true });
      //       }
      //     });
      //   }, 30000);
      // }
      const user_id = localStorage.getItem('userID');
      let status = getRefreshViewsStatus(user_id);
      getRefreshViewsStatus(status, result => {
        if (
          result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
            .status != 'COMPLETE'
        ) {
          let rstatus = this.CheckRefreshStatus(result);
        }
      });
      const interval = setInterval(() => {
        getRefreshViewsStatus(status, result => {
          if (
            result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
              .status == 'COMPLETE'
          ) {
            var coll = document.getElementsByClassName('edit-button');
            localStorage.setItem('setRunningStatus', 0);
            this.setState({ disableTable: 'all' });
            this.setState({ showRefresh: 'none' });
            var eDiv = document.createElement('div');
            var eButton = eDiv.querySelectorAll('.edit-button')[0];
            $('.edit-button').show();
            this.setState({ setOpenAlert: false });
            setTimeout(function() {
              for (var i = 0, len = coll.length; i < len; i++) {
                coll[i].style['background-color'] = '#384163';
              }
            }, 500);
            clearInterval(interval);
          } else {
            let rstatus = this.CheckRefreshStatus(result);
            if (rstatus == 'COMPLETE') {
              clearInterval(interval);
            }
          }
        });
      }, 30000);
    }

    getAllPayTypeErrors(callback => {
      if (callback.length > 0) {
        this.props.setErrorCount(callback.length);
        this.props.setError(true);
      } else {
        this.props.setErrorCount(0);
        this.props.setError(false);
      }
    });
  }

  constructor(props) {
    super(props);
    let startEdit = this;
    this.state = {
      indexValue: '',
      PayTypeDrilldown: '',
      showDrillDown: false,
      setColor: '#ccc',
      setMessage: '',
      setOpenAlert: false,
      setOpen: false,
      setOpenRows: false,
      showRefresh: 'none',
      disableTable: 'all',
      fadein: true,
      rawGridApi: {},
      gridApi: {},
      laborPayTypeCount: '',
      PartsPayTypeCount: '',
      isLoading: true,
      isLoadingSave: false,
      columnDefs: [
        {
          headerName: 'Id',
          width: 150,
          field: 'id',
          hide: true,

          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'series',
          width: 150,
          field: 'payType',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          // flex: 1,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
          // cellEditor: 'agSelectCellEditor',
          // cellEditorParams: {
          //   values: paytyp
          // },
          // filterParams: {
          //   valueFormatter: function(params) {
          //     return lookupValue(paytypeMappings, params.value);
          //   }
          // },
          // valueFormatter: function(params) {
          //   return lookupValue(paytypeMappings, params.value);
          // }
        },
        {
          headerName: 'Pay Type Group',
          chartDataType: 'series',
          width: 150,
          field: 'payTypeCode',

          suppressMenu: true,
          unSortIcon: true,
          // flex: 1,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false,
          cellEditor: 'agSelectCellEditor',
          cellEditorParams: {
            values: paytypeC
          },
          filterParams: {
            valueFormatter: function(params) {
              return lookupValue(paytypeCodeMappings, params.value);
            }
          },
          valueFormatter: function(params) {
            return lookupValue(paytypeCodeMappings, params.value);
          }
          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   typeof this.props.keycloak.realmAccess.roles[1] == 'string' &&
          //   (this.props.keycloak.realmAccess.roles[1] == 'client') === true
          //     ? false
          //     : true
        },
        {
          headerName: 'Labor / Parts',
          width: 150,
          field: 'lbrOrPrts',
          hide: true,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false
          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   typeof this.props.keycloak.realmAccess.roles[1] == 'string' &&
          //   (this.props.keycloak.realmAccess.roles[1] == 'client') === true
          //     ? false
          //     : true
        },
        {
          headerName: 'Department',
          chartDataType: 'series',
          width: 150,

          hide: localStorage.getItem('dms') == 'dtk' ? true : false,
          field: 'department',
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellEditor: 'agSelectCellEditor',
          cellEditorParams: {
            values: depart
          },
          filterParams: {
            valueFormatter: function(params) {
              return lookupValue(departmentMappings, params.value);
            }
          },
          valueFormatter: function(params) {
            return lookupValue(departmentMappings, params.value);
          }
        },
        {
          headerName: 'View RO Details',
          cellRenderer: 'buttonRenderer',
          filter: false,
          sortable: false,

          suppressMenu: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false,
          hide:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            typeof this.props.keycloak.realmAccess.roles[0] == 'string' &&
            (this.props.keycloak.realmAccess.roles[0] == 'client' ||
              this.props.keycloak.realmAccess.roles.includes('client')) === true
              ? true
              : false,
          cellRenderer: function(params) {
            var index = params.rowIndex;
            var eDiv = document.createElement('div');
            eDiv.innerHTML =
              '<button title="Detail" id="btndetail' +
              index +
              '" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="detail-button" ><i class="fas fa-eye"></i></button>';
            if (index != undefined) {
              // var eButton = eDiv.querySelectorAll('.edit-button')[0];
              // var uButton = eDiv.querySelectorAll('.update-button')[0];
              // var cButton = eDiv.querySelectorAll('.cancel-button')[0];
              var dButton = eDiv.querySelectorAll('.detail-button')[0];

              dButton.addEventListener('click', () => {
                startEdit.showDetail(
                  index,
                  params.data.id,
                  params.data.payType
                );
              });
              // eButton.addEventListener('click', () => {
              //   localStorage.setItem('oldId', params.data.id);
              //   localStorage.setItem('oldPayType', params.data.payType);
              //   localStorage.setItem('oldPayTypeCode', params.data.payTypeCode);
              //   localStorage.setItem('oldDepartment', params.data.department);

              //   var oldRow = [];
              //   oldRow.push(params.data.id);
              //   oldRow.push(params.data.lbrOrPrts);
              //   oldRow.push(params.data.payType);
              //   oldRow.push(params.data.payTypeCode);
              //   oldRow.push(params.data.department);
              //   oldRow.push(params.data.storeId);
              //   JSON.stringify(oldRow);
              //   localStorage.setItem('oldRow', oldRow);
              //   startEdit.onBtStartEditing(index);
              //   $('#btnedit' + index).hide();
              //   $('#btncancel' + index).show();
              //   $('#btnupdate' + index).show();
              // });

              // uButton.addEventListener('click', () => {
              //   startEdit.onBtStopEditing(index);
              //   localStorage.setItem('newId', params.data.id);
              //   localStorage.setItem('newPayType', params.data.payType);
              //   localStorage.setItem('newPayTypeCode', params.data.payTypeCode);
              //   localStorage.setItem('newDepartment', params.data.department);
              //   var selectedId = params.data.id;
              //   var selectedPayType = params.data.payType;
              //   var selectedPayTypeCode = params.data.payTypeCode;
              //   var selectedPayTypeDept = params.data.department;
              //   const oldDepartment = localStorage.getItem('oldDepartment');
              //   const oldRow = localStorage.getItem('oldRow');
              //   const storeId = JSON.parse(
              //     localStorage.getItem('selectedStoreId')
              //   )[0];
              //   var newRow = [];
              //   newRow.push(selectedId);
              //   newRow.push(params.data.lbrOrPrts);
              //   newRow.push(selectedPayType);
              //   newRow.push(selectedPayTypeCode);
              //   newRow.push(selectedPayTypeDept);
              //   newRow.push(storeId);
              //   localStorage.setItem('newRow', newRow);
              //   startEdit.updatePayTypeMaster(
              //     selectedId,
              //     selectedPayType,
              //     selectedPayTypeCode,
              //     selectedPayTypeDept,
              //     oldDepartment,
              //     storeId
              //   );
              //   $('#btnedit' + index).show();
              //   $('#btncancel' + index).hide();
              //   $('#btnupdate' + index).hide();
              // });
              // cButton.addEventListener('click', function() {
              //   startEdit.getAgGridData();
              //   startEdit.onBtStopEditing(index);
              //   $('#btnedit' + index).show();
              //   $('#btncancel' + index).hide();
              //   $('#btnupdate' + index).hide();
              // });
            }
            return eDiv;
          }
        },
        {
          headerName: 'Edit',
          cellRenderer: 'buttonRenderer',
          filter: false,
          sortable: false,

          suppressMenu: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false,
          hide:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            typeof this.props.keycloak.realmAccess.roles[0] == 'string' &&
            (this.props.keycloak.realmAccess.roles[0] == 'client' ||
              this.props.keycloak.realmAccess.roles.includes('client')) === true
              ? true
              : false,
          cellRenderer: function(params) {
            var index = params.rowIndex;
            var eDiv = document.createElement('div');
            eDiv.innerHTML =
              '<button   title="Edit" id="btnedit' +
              index +
              '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button  title="Cancel" id="btncancel' +
              index +
              '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate' +
              index +
              '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>';
            // &nbsp;<button title="Detail" id="btndetail' +
            // index +
            // '" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="detail-button" ><i class="fas fa-eye"></i></button>';
            if (index != undefined) {
              var eButton = eDiv.querySelectorAll('.edit-button')[0];
              var uButton = eDiv.querySelectorAll('.update-button')[0];
              var cButton = eDiv.querySelectorAll('.cancel-button')[0];
              var dButton = eDiv.querySelectorAll('.detail-button')[0];

              // dButton.addEventListener('click', () => {
              //   console.log('params=', params.data);
              //   startEdit.showDetail(
              //     index,
              //     params.data.id,
              //     params.data.payType
              //   );
              // });
              eButton.addEventListener('click', () => {
                localStorage.setItem('oldId', params.data.id);
                localStorage.setItem('oldPayType', params.data.payType);
                localStorage.setItem('oldPayTypeCode', params.data.payTypeCode);
                localStorage.setItem('oldDepartment', params.data.department);

                var oldRow = [];
                oldRow.push(params.data.id);
                oldRow.push(params.data.lbrOrPrts);
                oldRow.push(params.data.payType);
                oldRow.push(params.data.payTypeCode);
                oldRow.push(params.data.department);
                oldRow.push(params.data.storeId);
                JSON.stringify(oldRow);
                localStorage.setItem('oldRow', oldRow);
                startEdit.onBtStartEditing(index);
                $('#btnedit' + index).hide();
                $('#btncancel' + index).show();
                $('#btnupdate' + index).show();
              });

              uButton.addEventListener('click', () => {
                startEdit.onBtStopEditing(index);
                localStorage.setItem('newId', params.data.id);
                localStorage.setItem('newPayType', params.data.payType);
                localStorage.setItem('newPayTypeCode', params.data.payTypeCode);
                localStorage.setItem('newDepartment', params.data.department);
                var selectedId = params.data.id;
                var selectedPayType = params.data.payType;
                var selectedPayTypeCode = params.data.payTypeCode;
                var selectedPayTypeDept = params.data.department;
                const oldDepartment = localStorage.getItem('oldDepartment');
                const oldRow = localStorage.getItem('oldRow');
                const storeId = JSON.parse(
                  localStorage.getItem('selectedStoreId')
                )[0];
                var newRow = [];
                newRow.push(selectedId);
                newRow.push(params.data.lbrOrPrts);
                newRow.push(selectedPayType);
                newRow.push(selectedPayTypeCode);
                newRow.push(selectedPayTypeDept);
                newRow.push(storeId);
                localStorage.setItem('newRow', newRow);
                startEdit.updatePayTypeMaster(
                  selectedId,
                  selectedPayType,
                  selectedPayTypeCode,
                  selectedPayTypeDept,
                  oldDepartment,
                  storeId
                );
                $('#btnedit' + index).show();
                $('#btncancel' + index).hide();
                $('#btnupdate' + index).hide();
              });
              cButton.addEventListener('click', function() {
                startEdit.getAgGridData();
                startEdit.onBtStopEditing(index);
                $('#btnedit' + index).show();
                $('#btncancel' + index).hide();
                $('#btnupdate' + index).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: true,
        editable: true,
        suppressMovable: false
      },
      editType: 'fullRow',
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },

        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  updatePayTypeMaster = (
    Id,
    payType,
    payTypeCode,
    dept,
    oldDepartment,
    storeId
  ) => {
    this.props.handleChange(0);

    const client = makeApolloClientPostgres;
    var coll = document.getElementsByClassName('edit-button');
    this.setState({ showRefresh: 'none' });
    this.setState({ disableTable: 'none' });
    const oldId = localStorage.getItem('oldId');
    const oldPayType = localStorage.getItem('oldPayType');
    const oldPayTypeCode = localStorage.getItem('oldPayTypeCode');
    const newId = localStorage.getItem('newId');
    const newPayType = localStorage.getItem('newPayType');
    const newPayTypeCode = localStorage.getItem('newPayTypeCode');
    let oldPayTypeCodes = '';
    let newPayTypeCodes = '';
    if (oldPayTypeCode == 'undefined') {
      oldPayTypeCodes = JSON.stringify(null);
      // JSON.stringify(newPayTypeCodes);
    } else {
      oldPayTypeCodes = oldPayTypeCode;
    }
    if (newPayTypeCode == 'undefined') {
      newPayTypeCodes = JSON.stringify(null);
      // JSON.stringify(newPayTypeCodes);
    } else {
      newPayTypeCodes = newPayTypeCode;
    }
    const newDepartment = localStorage.getItem('newDepartment');
    const oldDepart = localStorage.getItem('oldDepartment');
    for (let i = 0, len = coll.length; i < len; i++) {
      coll[i].style['background-color'] = '#ccc';
    }
    if (
      oldId != newId ||
      oldPayType != newPayType ||
      newPayTypeCodes != 'null' ||
      newPayTypeCodes != 'null' ||
      oldPayTypeCodes != newPayTypeCodes ||
      oldDepart != newDepartment
    ) {
      this.setState({ isLoadingSave: true });
      const coll = document.getElementsByClassName('edit-button');
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#ccc';
      }
      if (dept == '') {
        dept = null;
      }
      if (oldDepartment == '') {
        oldDepartment = null;
      }
      const userID = localStorage.getItem('userID');
      client
        .mutate({
          mutation: UPDATE_PAY_TYPE_MASTER_BY_PAYTYPE,
          variables: {
            id: Id,
            pay_type: payType,
            pay_type_code: payTypeCode,
            department:
              localStorage.getItem('dms') == 'dtk' ? oldDepartment : dept,
            old_department: oldDepartment,
            store_id: storeId,
            user_id: userID
          }
        })
        .then(result => {
          if (
            result.data.statelessCcPhysicalRwUpdatePaytypeMasterByPaytypecode
              .updatePaytypeMasterStatuses[0].status == 1
          ) {
            getAllPayTypeErrors(callback => {
              if (callback.length > 0) {
                this.props.setErrorCount(callback.length);
                this.props.setError(true);
              } else {
                this.props.setErrorCount(0);
                this.props.setError(false);
              }
            });
            // this.props.setError(false);
            this.props.handleChange(1);
            this.setState({ isLoadingSave: false });
            this.setState({ showRefresh: 'all' });
            this.setState({ disableTable: 'all' });
            this.setState({ setMessage: payType + ' updated.' });
            this.setState({ setOpenRows: true });
            for (var i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#384163';
            }
            const userIDs = localStorage.getItem('userID');
            const storeIds = JSON.parse(
              localStorage.getItem('selectedStoreId')
            )[0];
            const userRole = this.props.session.user.role;

            this.getAgGridData();
            client
              .mutate({
                mutation: CLIENT_AUDITS,
                variables: {
                  dmlaction: 'Update paytype master',
                  newdata: localStorage.getItem('newRow'),
                  olddata: localStorage.getItem('oldRow'),
                  schemaname: 'dms_physical_rw',
                  storeId: storeIds,
                  tablename: 'pay_type_master',
                  username: userIDs,
                  userrole: userRole
                }
              })
              .then(result => {
                // console.log("data result=",result);
              });
          } else {
            this.props.setError(true);
          }
        });
    } else {
      this.setState({ disableTable: 'all' });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    }
  };
  showDetail = (index, id, payType) => {
    const currentState = this.state.showDrillDown;
    if (this.state.indexValue == index) {
      this.setState({ showDrillDown: !currentState });
      this.setState({ PayTypeDrilldown: payType });
      this.setState({ indexValue: index });
    } else {
      this.setState({ showDrillDown: false });
      this.setState({ PayTypeDrilldown: payType });
      this.setState({ indexValue: index });
      this.setState({ showDrillDown: true });
    }
  };
  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = true;
      groupColumn[3]['editable'] = false;
      groupColumn[4]['editable'] = true;
      groupColumn[3]['editable'] = false;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    const { rawGridApi } = this.state;
    rawGridApi.setFocusedCell(index, 'payTypeCode', pinned);
    rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'payTypeCode',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });

    // this.state.rawGridApi.setFocusedCell(index, 'pay_type_code', pinned);
    // this.state.rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'pay_type_code',

    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();

    this.getAgGridData();
  };
  getDistinctPaytypeCode(resultData) {
    let laborPaytype = resultData.filter(pay => pay.lbr_or_prts == 'L');
    let partsPaytype = resultData.filter(pay => pay.lbr_or_prts == 'P');
    let distinctLaborPaytype = Array.from(
      new Set(laborPaytype.map(item => item.pay_type))
    );
    let distinctPartsPaytype = Array.from(
      new Set(partsPaytype.map(item => item.pay_type))
    );
    var laborPaytypeCount = distinctLaborPaytype.length;
    var partsPaytypeCount = distinctPartsPaytype.length;
    this.setState({
      laborPayTypeCount: laborPaytypeCount,
      PartsPayTypeCount: partsPaytypeCount
    });
  }
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'PaytypeMaster',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Paytype Master' },
            mergeAcross: 3
          }
        ]
      ]
    };
  };
  getAgGridData() {
    this.setState({ isLoading: true });
    getAllPayTypeErrors(result => {
      if (result) {
        this.setState({ isLoading: false });
        this.getDistinctPaytypeCode(result);
        this.setState({
          rowData: result
        });
        // this.onGroupExpandedOrCollapsed();
      }
    });
  }
  handleClose = () => {
    this.setState({ setOpen: false });
  };
  CheckRefreshStatus = result => {
    const user_id = localStorage.getItem('userID');
    let status = getRefreshViewsStatus(user_id);
    const hookUrl = process.env.REACT_APP_WEBHOOK_URL;
    const key = process.env.REACT_APP_WEBHOOK_KEY;
    const value = process.env.REACT_APP_WEBHOOK_VALUE;

    if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status == 'COMPLETE'
    ) {
      var coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 0);
      this.setState({ disableTable: 'all' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpen: true });
      var eDiv = document.createElement('div');
      var eButton = eDiv.querySelectorAll('.edit-button')[0];
      $('.edit-button').show();
      this.setState({ setOpenAlert: false });
      for (var i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
      this.props.handleChange(3);
    } else if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status == 'RUNNING'
    ) {
      this.props.handleChange(2);
      var coll = document.getElementsByClassName('edit-button');

      localStorage.setItem('setRunningStatus', 1);
      setTimeout(function() {
        for (var i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
      }, 500);
      this.setState({ disableTable: 'none' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpenAlert: true });
      this.props.handleChange(2);
    } else if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status == 'REFRESHED'
    ) {
      var coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 1);
      setTimeout(function() {
        for (var i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
      }, 500);
      this.setState({ disableTable: 'none' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpenAlert: true });
      const storeId =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .store_id;
      const user_id =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .refresh_by;
      fetch(hookUrl, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'text/plain',
          Pragma: 'no-cache',
          'Cache-Control': 'no-cache',
          'Hook-Auth': value,
          'Access-Control-Allow-Origin': '*',
          'Control-Allow-Methods': 'GET, PUT, POST, DELETE, HEAD, OPTIONS'
        }
      }).then(async response => {
        const data = response.json();
        if (response.statusText == 'OK') {
          const status = 'SERVERSTARTED';
          updateRefreshStatus(status, async result => {
            if (result) {
              this.cubeDataFetch(user_id, storeId);
            }
          });
        }
      });
    } else if (
      result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
        .status == 'SERVERSTARTED'
    ) {
      var coll = document.getElementsByClassName('edit-button');
      localStorage.setItem('setRunningStatus', 1);
      setTimeout(function() {
        for (var i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
      }, 500);
      this.setState({ disableTable: 'none' });
      this.setState({ showRefresh: 'none' });
      this.setState({ setOpenAlert: true });
      const storeId =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .store_id;
      const user_id =
        result.data.statelessCcPhysicalRwGetDataRefreshStatusLogs.nodes[0]
          .refresh_by;

      fetch(hookUrl, {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'text/plain',
          Pragma: 'no-cache',
          'Cache-Control': 'no-cache',
          'Hook-Auth': value,
          'Access-Control-Allow-Origin': '*',
          'Control-Allow-Methods': 'GET, PUT, POST, DELETE, HEAD, OPTIONS'
        }
      }).then(async response => {
        const data = response.json();

        if (response.statusText == 'OK') {
          const status = 'SERVERSTARTED';
          updateRefreshStatus(status, async result => {
            if (result) {
              await this.cubeDataFetch(user_id, storeId);
            }
          });
        }
      });
    }
  };
  addRereshStatus = async (storeId, user_id) => {
    const client = makeApolloClientPostgres;
    client
      .mutate({
        mutation: ADD_REFRESH_STATUS,
        variables: {
          status: 'RUNNING',
          refresh_by: user_id,
          store_id: storeId
        }
      })
      .then(result => {
        var coll = document.getElementsByClassName('edit-button');
        const hookUrl = process.env.REACT_APP_WEBHOOK_URL;
        const key = process.env.REACT_APP_WEBHOOK_KEY;
        const value = process.env.REACT_APP_WEBHOOK_VALUE;
        updateRefreshViewsData(user_id, result => {
          if (result.data.statelessCcPhysicalRwRetrunRefreshMviews != null) {
            if (
              result.data.statelessCcPhysicalRwRetrunRefreshMviews.nodes[0] !=
              undefined
            ) {
              if (
                result.data.statelessCcPhysicalRwRetrunRefreshMviews.nodes[0]
                  .status == 1
              ) {
                fetch(hookUrl, {
                  method: 'POST',
                  headers: {
                    Accept: 'application/json',
                    'Content-Type': 'text/plain',
                    Pragma: 'no-cache',
                    'Cache-Control': 'no-cache',
                    'Hook-Auth': value,
                    'Access-Control-Allow-Origin': '*',
                    'Control-Allow-Methods':
                      'GET, PUT, POST, DELETE, HEAD, OPTIONS'
                  }
                }).then(async response => {
                  const data = response.json();

                  if (response.statusText == 'OK') {
                    const status = 'SERVERSTARTED';
                    updateRefreshStatus(status, async result => {
                      if (result) {
                        await this.cubeDataFetch(user_id, storeId);
                      }
                    });
                  }
                });
                // Hook call
                Promise.resolve();
              } else {
                this.setState({ disableTable: 'none' });
                this.setState({ showRefresh: 'all' });
                Promise.reject();
              }
            }
          } else if (
            result.data.statelessCcPhysicalRwRetrunRefreshMviews == null
          ) {
            this.setState({ setOpenAlert: false });
            this.setState({ setOpenAlertError: true });
          }
        });
      });
  };

  refreshDisable = () => {
    this.setState({ showRefresh: 'none' });
  };
  cubeDataFetch = async (user_id, storeId) => {
    const token = localStorage.getItem('keycloakToken');

    const cubeReloadUrl = process.env.REACT_APP_CUBE_RELOAD_URL;
    fetch(cubeReloadUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: token ? `Bearer ${token}` : '',
        realm: localStorage.getItem('realms'),
        storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      },
      body: JSON.stringify({ query: { measures: ['CPRevenue.labor_revenue'] } })
    }).then(async response => {
      if (response) {
        var coll = document.getElementsByClassName('edit-button');
        const data = await response.json();

        if (data.data[0]) {
          const status = 'COMPLETE';
          updateRefreshStatus(status, async result => {
            if (result) {
              localStorage.setItem('setRunningStatus', 0);
              this.setState({ disableTable: 'all' });
              this.setState({ showRefresh: 'none' });
              this.setState({ setOpen: true });
              var eDiv = document.createElement('div');
              var eButton = eDiv.querySelectorAll('.edit-button')[0];
              $('.edit-button').show();
              this.setState({ setOpenAlert: false });
              for (var i = 0, len = coll.length; i < len; i++) {
                coll[i].style['background-color'] = '#384163';
              }
              this.props.handleChange(3);
              Promise.resolve();
            }
          });
        } else {
          Promise.reject();
        }
      }
    });
  };
  onBtDisable = () => {
    this.props.handleChange(2);
    this.props.setRefreshStatus(true);
    const { rowData } = this.state;
    const lengths = rowData.length;
    const user = this.props.session.user.role;
    const user_id = localStorage.getItem('userID');

    var coll = document.getElementsByClassName('edit-button');
    for (var i = 0, len = coll.length; i < len; i++) {
      coll[i].style['background-color'] = '#ccc';
    }
    const storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.setState({ setOpenAlert: true });
    this.setState({ showRefresh: 'none' });
    this.setState({ disableTable: 'none' });
    const date = new Date();
    const refresh_date = moment(date).format('YYYY-MM-DD');
    const refresh_end = moment(date).format('hh:mm:ss');
    const refresh_start = moment(date).format('hh:mm:ss');
    this.props.handleChange(2);
    this.addRereshStatus(storeId, user_id);
  };
  handleClose = () => {
    this.setState({ setOpen: false });
  };
  onBtCloseError = () => {
    this.setState({ setOpenAlertError: false });
  };
  handleCloseRow = () => {
    this.setState({ setOpenRows: false });
  };
  render() {
    const { classes } = this.props;

    const {
      showRefresh,
      disableTable,
      setColor,
      setBackground,
      setOpen,
      setOpenRows,
      setOpenAlert,
      setMessage
    } = this.state;

    var coll = document.getElementsByClassName('edit-button');

    if (disableTable == 'none') {
      for (var i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#ccc';
      }
    }
    return (
      <div>
        <Dialog
          open={setOpen}
          onClose={this.handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle
            id="alert-dialog-title"
            style={{ paddingLeft: 108, paddingRight: 85 }}
          >
            {'Paytype Master'}
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="alert-dialog-description">
              Dashboard refresh completed please reload the dashboard.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleClose} color="primary" autoFocus>
              OK
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog
          open={setOpenRows}
          onClose={this.handleCloseRow}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle
            id="alert-dialog-title"
            style={{ paddingLeft: 108, paddingRight: 85 }}
          >
            {'Paytype Master'}
          </DialogTitle>
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              style={{ paddingLeft: 85, paddingRight: 85 }}
            >
              {setMessage}
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCloseRow} color="primary" autoFocus>
              OK
            </Button>
          </DialogActions>
        </Dialog>
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <Fade in={this.state.fadein} timeout={1200}>
          <div
            id="data-tab"
            className="ag-theme-balham"
            style={{
              pointerEvents: disableTable,
              // height: window.innerHeight - 600 + 'px',
              width: '800px',
              alignContent: 'center',
              marginLeft: '8px',
              paddingRight: '16px',
              height: '100%',
              display: this.state.isLoading == true ? 'none' : 'block'
            }}
          >
            {this.state.isLoadingSave && (
              <div
                style={{
                  position: 'absolute',
                  left: '58%',
                  zIndex: 1,
                  top: '50%'
                }}
              >
                <LoaderSkeleton />
              </div>
            )}
            <AgGridReact
              className="ag-theme-balham grid-cell-centered "
              style={{
                // height: '50%',
                width: '100%'
              }}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader={true}
              rowData={this.state.rowData}
              excelStyles={this.state.excelStyles}
              domLayout="autoHeight"
              // floatingFilter={true}
              // enableRangeSelection={true}
              // animateRows={true}
              // enableCharts={true}
              // suppressRowClickSelection={true}
              editType={this.state.editType}
              suppressDragLeaveHidesColumns={true}
              // suppressClickEdit={true}
              suppressContextMenu={true}
            />
          </div>
        </Fade>
        {this.state.showDrillDown == true && (
          <Fade in={this.state.fadein} timeout={1200}>
            <div
              id="data-tab"
              className="ag-theme-balham"
              style={{
                // pointerEvents: disableTable,
                height: window.innerHeight - 200 + 'px',
                //height: '600px',
                width: '800px',
                alignContent: 'center',
                marginLeft: '8px'
              }}
            >
              <PayTypeDrilldown
                PayTypeDrilldown={this.state.PayTypeDrilldown}
              />
            </div>
          </Fade>
        )}
      </div>
    );
  }
}
export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withKeycloak(PayTypeErrors));

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setError: data => dispatch({ type: SET_PAY_TYPE_ERROR, payload: data }),
    setErrorCount: data =>
      dispatch({ type: SET_PAY_TYPE_ERROR_COUNT, payload: data })
  };
}
const departmentMappings = {
  'Body Shop': 'Body Shop',
  Service: 'Service'
};
const paytypeMappings = {
  CACC: 'CACC',
  CB: 'CB',
  CBACC: 'CBACC',
  CBF: 'CBF',
  CBM: 'CBM',
  CP: 'CP',
  CPD: 'CPD',
  CPE: 'CPE',
  CPL: 'CPL',
  CPM: 'CPM',
  CPQ: 'CPQ',
  CPT: 'CPT',
  CPW: 'CPW',
  CRENT: 'CRENT',
  CSC: 'CSC',
  IACC: 'IACC',
  IB: 'IB',
  IBACC: 'IBACC',
  IBL: 'IBL',
  IBNC: 'IBNC',
  IBNT: 'IBNT',
  IBR: 'IBR',
  IBS: 'IBS',
  IBUC: 'IBUC',
  IBUT: 'IBUT',
  ILDS: 'ILDS',
  INC: 'INC',
  INT: 'INT',
  IPDI: 'IPDI',
  IPV: 'IPV',
  IRENT: 'IRENT',
  IRM: 'IRM',
  ISB: 'ISB',
  ISV: 'ISV',
  IUC: 'IUC',
  IUT: 'IUT',
  W: 'W',
  WB: 'WB',
  WBGMP: 'WBGMP',
  WGMPP: 'WGMPP',
  WQL: 'WQL'
};
const paytypeCodeMappings = {
  C: 'C',
  E: 'E',
  F: 'F',
  I: 'I',
  M: 'M',
  W: 'W'
};
function extractValues(mappings) {
  return Object.keys(mappings);
}
const depart = extractValues(departmentMappings);
const paytyp = extractValues(paytypeMappings);
const paytypeC = extractValues(paytypeCodeMappings);
function lookupValue(mappings, key) {
  return mappings[key];
}
function lookupKey(mappings, name) {
  const keys = Object.keys(mappings);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    if (mappings[key] === name) {
      return key;
    }
  }
}
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  }
});
