import React, { Component } from 'react';
import { FormControl, TextField } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputAdornment from '@material-ui/core/InputAdornment';
import DateRangePicker from 'react-bootstrap-daterangepicker';

import 'bootstrap-daterangepicker/daterangepicker.css';
import clsx from 'clsx';
import moment from 'moment';
import 'src/input-style.css';
import { TransferWithinAStationOutlined } from '@material-ui/icons';

class FixedRateRenderer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isFixedRateChecked: false,
      isValid:
        this.props.data.partsFixedratevalue != null &&
        this.props.data.partsFixedratevalue != 'N/A'
          ? this.props.data.partsFixedratevalue.split(' ')[0] == 'Cost' ||
            this.props.data.partsFixedratevalue.split(' ')[0] == 'List'
            ? true
            : false
          : true,
      partsMarkup:
        this.props.data.partsFixedratevalue != null &&
        this.props.data.partsFixedratevalue != 'N/A'
          ? this.props.data.partsFixedratevalue.split(' ')[0]
          : 'Cost',

      laborfixedVal:
        this.props.data.laborFixedRate == 1
          ? this.props.data.laborFixedratevalue
          : '',
      laborfixedDate:
        this.props.data.laborFixedratedate != null &&
        this.props.data.laborFixedRate == 1
          ? this.props.data.laborFixedratedate
          : new Date(),
      partsfixedValue:
        this.props.data.partsFixedratevalue != null &&
        this.props.data.partsFixedRate == 1
          ? this.props.data.partsFixedratevalue.split(' ')[1]
          : '',
      partsfixedDate:
        this.props.data.partsFixedRate == 1 &&
        this.props.data.partsFixedratedate != null
          ? this.props.data.partsFixedratedate
          : new Date()
    };
    this.checkedHandler = this.checkedHandler.bind(this);
    this.partsCheckedHandler = this.partsCheckedHandler.bind(this);
    this.handleCallback = this.handleCallback.bind(this);
    this.handleFixedRateChange = this.handleFixedRateChange.bind(this);
    this.handleMarkupChange = this.handleMarkupChange.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'laborFixedRate') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
    if (this.props.data.laborFixedRate == 1 && checked == true) {
      this.props.context.componentParent.onLaborFixedRateChanged(
        this.props.data.laborFixedratevalue,
        this.state.laborfixedVal
      );
      this.props.api.refreshCells({
        columns: ['laborFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });

      // this.props.context.componentParent.onFixedDateChanged(
      //   this.props.data.fixedRateDate,
      //   this.props.data.fixedRateDate == null || this.props.fixedRateDate == ''
      //     ? moment(new Date()).format('MM/DD/YY')
      //     : this.props.data.fixedRateDate
      // );
      // this.props.context.componentParent.onFixedRateChanged(
      //   this.props.data.fixedRateValue,
      //   this.props.data.fixedRateValue
      // );
    } else {
      this.props.context.componentParent.onLaborFixedRateChanged(
        this.props.data.laborFixedratevalue,
        this.props.data.laborFixedratevalue
      );
      this.props.api.refreshCells({
        columns: ['laborFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  partsCheckedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'partsFixedRate') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
    if (this.props.data.partsFixedRate == 1 && checked == true) {
      this.props.api.refreshCells({
        columns: ['partsFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
      this.props.context.componentParent.onPartsFixedDateChanged(
        this.props.data.fixedRateDate,
        this.props.data.fixedRateDate == null || this.props.fixedRateDate == ''
          ? moment(new Date()).format('MM/DD/YY')
          : this.props.data.fixedRateDate
      );
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.fixedRateValue,
        'Cost'
      );
    } else {
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsFixedratevalue,
        this.props.data.partsFixedratevalue
      );
      this.props.context.componentParent.onPartsFixedDateChanged(
        this.props.data.partsFixedratedate,
        this.props.data.partsFixedratedate != null ||
          typeof this.props.partsFixedratedate != 'undefined'
          ? this.props.data.partsFixedratedate
          : null
      );
      this.props.api.refreshCells({
        columns: ['partsFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  handleCallback = (start, end, label) => {
    let colId = this.props.column.colId;
    this.setState({
      fixedDate: start.format('MM/DD/YY')
    });

    if (this.props.column.colId == 'laborFixedRate') {
      if (this.props.data.laborFixedRate == 1) {
        this.props.context.componentParent.onLaborFixedDateChanged(
          this.props.data.laborFixedratedate,
          start.format('YYYY-MM-DD')
        );
      } else {
        this.props.context.componentParent.onLaborFixedDateChanged(
          this.props.data.laborFixedratedate,
          null
        );
      }
    } else {
      if (this.props.data.partsFixedRate == 1) {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedratedate,
          start.format('YYYY-MM-DD')
        );
      } else {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedratedate,
          null
        );
      }
    }
  };
  handleMarkupChange(event) {
    this.setState({
      partsMarkup: event.target.value
    });

    if (this.props.data.partsFixedRate == 1) {
      if (event.target.value == 'Cost' || event.target.value == 'List') {
        this.setState({
          isValid: true
        });
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedratevalue,
          event.target.value == 'N/A' ? 'Cost' : event.target.value
        );
      } else {
        this.setState({
          isValid: false
        });
        var val =
          this.state.partsfixedValue != null && this.state.partsfixedValue != ''
            ? this.state.partsfixedValue
            : '';
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedratevalue,
          (event.target.value == 'N/A' ? 'Cost' : event.target.value) +
            ' ' +
            val
        );
      }
    } else {
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsFixedratevalue,
        null
      );
    }
    if (this.props.data.partsFixedRate == 1) {
      this.props.context.componentParent.onPartsFixedDateChanged(
        this.props.data.partsFixedratedate,
        this.props.context.componentParent.state.partsFixedDateNew != ''
          ? this.props.context.componentParent.state.partsFixedDateNew
          : this.props.data.partsFixedratedate == null ||
            this.props.partsFixedratedate == ''
          ? moment(new Date()).format('MM/DD/YY')
          : this.props.data.partsFixedratedate
      );
    }
  }
  handleFixedRateChange(event) {
    let colId = this.props.column.colId;

    this.setState({
      fixedValue: event.target.value
    });

    if (this.props.column.colId == 'laborFixedRate') {
      if (this.props.data.laborFixedRate == 1) {
        this.props.context.componentParent.onLaborFixedRateChanged(
          this.props.data.laborFixedratevalue,
          event.target.value
        );
      } else {
        this.props.context.componentParent.onLaborFixedRateChanged(
          this.props.data.laborFixedratevalue,
          ''
        );
      }
      if (this.props.data.laborFixedRate == 1) {
        this.props.context.componentParent.onLaborFixedDateChanged(
          this.props.data.laborFixedratedate,
          this.props.context.componentParent.state.laborFixedDateNew != '' &&
            this.props.context.componentParent.state.laborFixedDateNew != null
            ? this.props.context.componentParent.state.laborFixedDateNew
            : this.props.data.laborFixedratedate == null ||
              this.props.laborFixedratedate == ''
            ? moment(new Date()).format('MM/DD/YY')
            : this.props.data.laborFixedratedate
        );
      }
    } else {
      if (this.props.data.partsFixedRate == 1) {
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedratevalue,
          (this.state.partsMarkup == 'N/A' ? 'Cost' : this.state.partsMarkup) +
            ' ' +
            (this.state.partsfixedValue == 0 ? '' : this.state.partsfixedValue)
        );
      } else {
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedratevalue,
          null
        );
      }
      if (this.props.data.partsFixedRate == 1) {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedratedate,
          this.props.context.componentParent.state.partsFixedDateNew != '' &&
            this.props.context.componentParent.state.partsFixedDateNew != null
            ? this.props.context.componentParent.state.partsFixedDateNew
            : this.props.data.partsFixedratedate == null ||
              this.props.partsFixedratedate == ''
            ? moment(new Date()).format('MM/DD/YY')
            : this.props.data.partsFixedratedate
        );
      }
    }
  }

  render() {
    const { classes } = this.props;

    // var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    // var regExParts = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    var regEx = /^\d{0,3}(\.\d{0,3})?$/;
    var regExParts = /^\d{0,3}(\.\d{0,3})?$/;
    var decVal = /^(\d*\.{0,1}\d{0,2}$)/;
    return this.props.colDef.field == 'laborFixedRate' ? (
      <div
        style={{
          display: 'inline-flex',
          gap: 3,
          marginTop:
            this.props.context.componentParent.state.editedRowId != null
              ? '-2px'
              : '1px',
          alignItems: 'center'
        }}
      >
        <input
          type="checkbox"
          title="Labor Fixed Rate"
          className="laborFixedRate"
          disabled={
            this.props.context.componentParent.state.editedRowId == null
              ? true
              : this.props.context.componentParent.state.editedRowId !=
                this.props.rowIndex
              ? true
              : this.props.data.gridExcluded == 1
              ? true
              : false
          }
          onClick={this.checkedHandler}
          checked={this.props.data.laborFixedRate == 0 ? false : true}
        />

        <FormControl
          variant="outlined"
          className="laborFixedRateValue"
          disabled={
            this.props.context.componentParent.state.editedRowId != null &&
            this.props.data.laborFixedRate == 1
              ? false
              : 'true'
          }
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 1
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
        >
          <OutlinedInput
            className={clsx(classes.formControl, 'fixedRateValue')}
            id="outlined-adornment-regEx"
            title="Labor Fixed Rate($)"
            value={
              this.state.laborfixedVal != null ? this.state.laborfixedVal : ''
            }
            error={
              this.props.context.componentParent.state.laborfixedRateError ==
              null
                ? false
                : this.props.context.componentParent.state
                    .laborfixedRateError == this.props.rowIndex
                ? true
                : false
            }
            // onKeyPress={event => {
            //   if (!/[0-9]|\./.test(event.key)) {
            //     event.preventDefault();
            //   }
            // }}
            onBlur={this.handleFixedRateChange}
            //onChange={e => this.onChange(e)}
            onChange={e =>
              (e.target.value === '' ||
                e.target.value === null ||
                (regExParts.test(e.target.value) &&
                  decVal.test(e.target.value))) &&
              this.setState({
                laborfixedVal: e.target.value
              })
            }
            // onChange={this.handleFixedRateChange}
            startAdornment={
              <InputAdornment
                classes={{ root: classes.adorment }}
                position="start"
                disableTypography={true}
              >
                $
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null &&
              this.props.data.laborFixedRate == 1
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                    this.props.rowIndex && this.props.data.laborFixedRate == 1
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.laborFixedratevalue != null &&
            this.props.data.laborFixedratevalue != '' &&
            this.props.data.laborFixedratevalue != 0 &&
            '$' + this.props.data.laborFixedratevalue}
        </span>

        <FormControl
          variant="outlined"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 1
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          margin="dense"
          title="Effective From"
          className={clsx(
            classes.formControlDate,
            'input-container',
            'laborFixedRateDate'
          )}
        >
          <DateRangePicker
            initialSettings={{
              // maxDate: {
              //   date: new Date(),
              // },
              // minDate:{
              //   date: (this.props.selectedDates[1])
              // },
              locale: {
                format: 'MM/DD/YY',
                separator: ' - '
              },
              autoUpdateInput: true,
              showDropdowns: true,
              autoApply: true,
              singleDatePicker: true,
              // minDate: moment().toDate(),
              //maxDate: moment().toDate(),
              // alwaysShowCalendars: true,
              applyClass: clsx(classes.calButton, 'apply-btn'),
              cancelClass: clsx(classes.calButton, 'apply-btn'),
              startDate: new Date(this.state.laborfixedDate)
              //showDropdowns: true
            }}
            value={this.state.laborfixedDate}
            onCallback={this.handleCallback}
          >
            <input
              type="text"
              disabled={
                this.props.context.componentParent.state.editedRowId != null &&
                this.props.data.laborFixedRate == 1
                  ? false
                  : 'true'
              }
              className={clsx(classes.fixeddatepicker, 'fixedRateDate')}
              id="picker"
              name="picker"
              aria-labelledby="label-picker"
            />
            {/* <TextField
          id="outlined-basic"
          label="Select Date"
          size="small"
          //onChange={}
          value={this.state.value}
          variant="outlined"
        /> */}
          </DateRangePicker>
          {/* <label class="labelpicker" for="picker" id="label-picker">
          <div class="textpicker">Select Date</div>
        </label> */}
        </FormControl>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null &&
              this.props.data.laborFixedRate == 1
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                    this.props.rowIndex && this.props.data.laborFixedRate == 1
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.laborFixedratedate != null &&
            '[' +
              moment(new Date(this.props.data.laborFixedratedate)).format(
                'MM/DD/YY'
              ) +
              ']'}
        </span>
      </div>
    ) : this.props.colDef.field == 'partsFixedRate' ? (
      <div
        style={{
          display: 'inline-flex',
          gap: 3,
          marginTop:
            this.props.context.componentParent.state.editedRowId != null
              ? '-2px'
              : '1px',
          alignItems: 'center'
        }}
      >
        <input
          type="checkbox"
          title="Parts Fixed Rate"
          className="partsFixedRate"
          disabled={
            this.props.context.componentParent.state.editedRowId == null
              ? true
              : this.props.context.componentParent.state.editedRowId !=
                this.props.rowIndex
              ? true
              : this.props.data.gridExcluded == 1
              ? true
              : false
          }
          onClick={this.partsCheckedHandler}
          checked={this.props.data.partsFixedRate == 0 ? false : true}
        />
        <select
          disabled={
            this.props.context.componentParent.state.editedRowId != null &&
            this.props.data.partsFixedRate == 1
              ? false
              : 'true'
          }
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 1
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          onChange={e => this.handleMarkupChange(e)}
          value={this.state.partsMarkup}
          className={clsx(classes.formControlSelect, 'partsFixedRateSelect')}
          name="parts-markups"
          id="parts-markups"
        >
          <option value="Cost">Cost</option>
          <option value="Cost-">Cost-</option>
          <option value="Cost+">Cost+</option>
          <option value="Cost%">Cost%</option>
          <option value="List">List</option>
          <option value="List-">List-</option>
          <option value="List+">List+</option>
          <option value="List%">List%</option>
        </select>

        <FormControl
          variant="outlined"
          disabled={
            this.props.context.componentParent.state.editedRowId != null &&
            this.props.data.partsFixedRate == 1 &&
            !this.state.isValid
              ? false
              : 'true'
          }
          className="partsFixedRateValue"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 1
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
        >
          <OutlinedInput
            className={clsx(classes.formControl, 'fixedRateValue')}
            classes={{ input: classes.adorment }}
            title="Parts Fixed Rate(%)"
            id="outlined-adornment-weight"
            value={this.state.partsfixedValue}
            error={
              this.props.context.componentParent.state.partsfixedRateError ==
              null
                ? false
                : this.props.context.componentParent.state
                    .partsfixedRateError == this.props.rowIndex
                ? true
                : false
            }
            // onKeyPress={event => {
            //   if (!/[0-9]|\./.test(event.key)) {
            //     event.preventDefault();
            //   }
            // }}
            onBlur={this.handleFixedRateChange}
            onChange={e =>
              (e.target.value === '' ||
                (regExParts.test(e.target.value) &&
                  decVal.test(e.target.value))) &&
              this.setState({
                partsfixedValue: e.target.value
              })
            }
            // onChange={this.handleFixedRateChange}
            endAdornment={
              <InputAdornment
                classes={{ input: classes.adorment }}
                position="end"
                disableTypography={true}
              >
                %
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null &&
              this.props.data.partsFixedRate == 1
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                    this.props.rowIndex && this.props.data.partsFixedRate == 1
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.partsFixedratevalue != null &&
          this.props.data.partsFixedratevalue != '' &&
          this.props.data.partsFixedratevalue != 'N/A' &&
          this.props.data.partsFixedratevalue != 'Cost' &&
          this.props.data.partsFixedratevalue != 'List'
            ? this.props.data.partsFixedratevalue + '%'
            : this.props.data.partsFixedratevalue}
        </span>

        <FormControl
          variant="outlined"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 1
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          margin="dense"
          title="Effective From"
          className={clsx(
            classes.formControlDate,
            'input-container',
            'partsFixedRateDate'
          )}
        >
          <DateRangePicker
            initialSettings={{
              // maxDate: {
              //   date: new Date(),
              // },
              // minDate:{
              //   date: (this.props.selectedDates[1])
              // },
              locale: {
                format: 'MM/DD/YY',
                separator: ' - '
              },
              autoUpdateInput: true,
              showDropdowns: true,
              autoApply: true,
              singleDatePicker: true,
              // minDate: moment().toDate(),
              //maxDate: moment().toDate(),
              // alwaysShowCalendars: true,
              applyClass: clsx(classes.calButton, 'apply-btn'),
              cancelClass: clsx(classes.calButton, 'apply-btn'),
              startDate: new Date(this.state.partsfixedDate)
              //showDropdowns: true
            }}
            value={this.state.partsfixedDate}
            onCallback={this.handleCallback}
          >
            <input
              type="text"
              disabled={
                this.props.context.componentParent.state.editedRowId != null &&
                this.props.data.partsFixedRate == 1
                  ? false
                  : 'true'
              }
              className={clsx(classes.fixeddatepicker, 'fixedRateDate')}
              id="picker"
              name="picker"
              aria-labelledby="label-picker"
            />
            {/* <TextField
        id="outlined-basic"
        label="Select Date"
        size="small"
        //onChange={}
        value={this.state.value}
        variant="outlined"
      /> */}
          </DateRangePicker>
          {/* <label class="labelpicker" for="picker" id="label-picker">
        <div class="textpicker">Select Date</div>
      </label> */}
        </FormControl>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null &&
              this.props.data.partsFixedRate == 1
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                    this.props.rowIndex && this.props.data.partsFixedRate == 1
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.partsFixedratedate != null &&
            '[' +
              moment(new Date(this.props.data.partsFixedratedate)).format(
                'MM/DD/YY'
              ) +
              ']'}
        </span>
      </div>
    ) : (
      ''
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    width: 60,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: '8px !important',
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '75px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,
    width: 80,
    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 4
  }
});
export default withStyles(styles)(FixedRateRenderer);
