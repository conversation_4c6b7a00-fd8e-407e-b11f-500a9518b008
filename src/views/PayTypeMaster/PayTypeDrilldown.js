import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import { Box, LinearProgress, Typography } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import { AgGridReact } from '@ag-grid-community/react';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { getDrillDownDataForPayTypeErrors } from 'src/utils/hasuraServices';
import TooltipRenderer from 'src/views/AnalyzeData/Component/TooltipRenderer';
var lodash = require('lodash');

class PayTypeDrilldown extends React.Component {
  componentDidUpdate(prevProps) {
    if (
      prevProps.session.storeSelected &&
      JSON.parse(localStorage.getItem('selectedStoreId'))
    ) {
      if (
        JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
        JSON.parse(prevProps.session.storeSelected)[0]
      ) {
        this.getAgGridData();
      }
    }
  }
  componentDidUpdate() {}

  static defaultProps = {};

  constructor(props) {
    super(props);
    var drillDown = '';
    var category = '';
    var chartId = '';

    this.state = {
      selectedGroups: [],
      selectedFilters: [],
      isLoading: true,

      autoGroupColumnDef: [],

      rawGridApi: {},
      gridApi: {},

      columnDefsRevenue: [
        {
          headerName: 'RO',
          // filter: 'agNumberColumnFilter',
          field: 'ronumber',
          cellRenderer: 'agGroupCellRenderer',
          // cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          //cellRendererFramework: TooltipRenderer,
          tooltip: params => 'View RO',
          width: 83,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },

        {
          headerName: 'Month',
          hide: true,
          field: 'monthYear',
          chartDataType: 'category',
          width: 80,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          },
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Closed Date',
          field: 'closeddate',
          chartDataType: 'category',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          // filter: 'agDateColumnFilter',
          width: 88,
          suppressMenu: true,
          unSortIcon: true,
          filterParams: {
            browserDatePicker: true,
            comparator: function(filterLocalDateAtMidnight, cellValue) {
              var dateAsString = moment(cellValue).format('YYYY-MM-DD');
              if (dateAsString == null) return -1;

              var dateParts = dateAsString.split('-');
              var cellDate = new Date(
                Number(dateParts[0]),
                Number(dateParts[1]) - 1,
                Number(dateParts[2])
              );

              if (filterLocalDateAtMidnight.getTime() == cellDate.getTime()) {
                return 0;
              }

              if (cellDate < filterLocalDateAtMidnight) {
                return -1;
              }

              if (cellDate > filterLocalDateAtMidnight) {
                return 1;
              }
            }
          },
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Advisor',
          // filter: 'agNumberColumnFilter',
          field: 'serviceadvisor',
          chartDataType: 'category',
          cellClass: 'textAlign',
          width: 93,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Seq No.',
          // filter: 'agNumberColumnFilter',
          field: 'lbrsequenceno',
          width: 58,
          chartDataType: 'category',
          hide: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Labor Code',
          // filter: 'agTextColumnFilter',
          field: 'lbrlinecode',
          width: 81,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Pay Type',
          // filter: 'agTextColumnFilter',
          field: 'paytype',
          width: 75,
          chartDataType: 'category',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Opcode',
          // filter: 'agTextColumnFilter',
          field: 'lbropcode',
          width: 91,
          chartDataType: 'category',
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Op Category',
          // filter: 'agTextColumnFilter',
          field: 'opcategory',
          width: 110,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode Desc',
          field: 'lbropcodedesc',
          tooltipField: 'lbropcodedesc',
          chartDataType: 'category',
          minWidth: 150,
          resizable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Opcode Sub Cat',
          field: 'opsubcategory',
          chartDataType: 'category',
          width: 91,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        {
          headerName: 'Labor Sale',
          // filter: 'agNumberColumnFilter',
          field: 'lbrsale',
          width: 81,
          chartDataType: 'series',

          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          suppressMenu: true,
          unSortIcon: true,
          cellClass: 'twoDecimalPlacesWith$'
        },
        {
          headerName: 'Labor Cost',
          // filter: 'agNumberColumnFilter',
          field: 'lbrcost',
          width: 78,
          chartDataType: 'series',
          hide: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$'
        },
        {
          headerName: 'Sold Hours',
          // filter: 'agNumberColumnFilter',
          field: 'lbrsoldhours',
          width: 81,
          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'series',

          cellStyle: this.cellStyles
          // aggFunc: 'sum',
        },
        {
          headerName: 'Parts Sale',
          // filter: 'agNumberColumnFilter',
          field: 'prtextendedsale',
          width: 78,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$'
        },
        {
          headerName: 'Parts Cost',
          // filter: 'agNumberColumnFilter',
          field: 'prtextendedcost',
          width: 78,
          suppressMenu: true,
          unSortIcon: true,
          chartDataType: 'series',
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$'
          //  aggFunc: 'sum',
        }
      ],

      rowData: [],
      headerHeight: 45,
      autoGroupColumnDef: { minWidth: 200 },
      sideBar: 'columns',

      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        enableValue: true,
        sortable: true,
        resizable: false,
        filter: true
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 20,
            color: 'primary'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'twoDecimalPlacesWithOut$',
          numberFormat: { format: '#,##0.00' }
        },
        {
          id: 'oneDecimalPlace',
          numberFormat: { format: '#,##0.0' }
        },
        {
          id: 'twoDecimalPlacesDiscount',
          numberFormat: { format: '($#,##0.00)' }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/YY');
    } else {
      return '';
    }
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        parseFloat(Math.abs(params.value))
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        ')'
      );
    }
  };

  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(1)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  };
  formatCellValueMarkup = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(4)
        .toString();
    }
  };

  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: this.state.chartName,
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: this.state.chartName },
            mergeAcross: 6
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onGridReady = params => {
    // params.api.closeToolPanel();
    this.gridApi = params.api;
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });

    this.getAgGridData();
  };

  filterByPayTypeGroup = () => {
    var payTypeFilterComponent = this.state.rawGridApi.getFilterInstance(
      'paytypegroup'
    );
    payTypeFilterComponent.setModel({
      condition1: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter1
      },
      condition2: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter2
      },
      condition3: {
        type: 'startsWith',
        filter: this.state.selectedpayTypeFilter3
      },
      operator: 'OR'
    });
    this.state.rawGridApi.onFilterChanged();
  };
  filterByOpCategory = () => {
    var opCatFilterComponent = this.state.rawGridApi.getFilterInstance(
      'opcategory'
    );
    opCatFilterComponent.setModel({
      condition1: {
        type: 'startsWith',
        filter: this.state.selectedOpCategoryFilter1
      },
      condition2: {
        type: 'startsWith',
        filter: this.state.selectedOpCategoryFilter2
      },
      condition3: {
        type: 'startsWith',
        filter: this.state.selectedOpCategoryFilter3
      },
      operator: 'OR'
    });
    this.state.rawGridApi.onFilterChanged();
  };

  getAgGridData() {
    getDrillDownDataForPayTypeErrors(this.props.PayTypeDrilldown, callback => {
      if (
        callback.data.statelessCcDrillDownTotalRevenueDetailsPaytypeMapings
          .nodes
      ) {
        this.setState({ isLoading: false });
        this.setState({
          rowData:
            callback.data.statelessCcDrillDownTotalRevenueDetailsPaytypeMapings
              .nodes
        });
      }
    });
  }

  render() {
    const { classes } = this.props;
    return (
      <div>
        {this.state.isLoading && (
          <div
            style={{
              display: this.state.isLoading == true ? 'block' : 'none'
            }}
          >
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}
        <div style={{ height: '10px' }}></div>
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height:
              this.state.chartId == 1089 ||
              this.state.chartId == 1090 ||
              this.state.chartId == 1095 ||
              this.state.chartId == 1096 ||
              this.state.drillDown == 35 ||
              this.state.drillDown == 34
                ? window.innerHeight - 150 + 'px'
                : window.innerHeight - 210 + 'px',
            width: '98.8%',
            // margin: 8,
            display: this.state.isLoading == true ? 'none' : 'block'
            // display: this.state.tabSelection == 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            //columnDefs={this.state.columnDefs}
            // sideBar={this.state.sideBar}
            columnDefs={
              this.state.drillDown == 25 || this.state.drillDown == 26
                ? this.state.columnDefsAddOns
                : this.state.drillDown == 9
                ? this.state.columnDefsPrtsMarkup
                : this.state.drillDown == 18
                ? this.state.columnDefsSingleJob
                : this.state.drillDown == 22
                ? this.state.columnDefsActualHrs
                : this.state.drillDown == 14
                ? this.state.columnDefsMovingElr
                : this.state.drillDown == 15
                ? this.state.columnDefsMovingMarkup
                : this.state.drillDown == 16
                ? this.state.columnDefsItemization
                : this.state.drillDown == 17
                ? this.state.columnDefsPrtsItemization
                : this.state.drillDown == 32
                ? this.state.columnDefsDiscountSA
                : (this.state.drillDown == 30 &&
                    (this.state.category == 0 ||
                      this.state.category == 2 ||
                      this.state.category == 3)) ||
                  this.state.drillDown == 34 ||
                  (this.state.drillDown == 31 &&
                    this.state.category == ' Labor Discount') ||
                  (this.state.drillDown == 36 && this.state.category == 0)
                ? this.state.columnDefsDisSummaryLbr
                : (this.state.drillDown == 30 && this.state.category == 1) ||
                  this.state.drillDown == 35 ||
                  (this.state.drillDown == 31 &&
                    this.state.category == ' Parts Discount') ||
                  (this.state.drillDown == 36 && this.state.category == 1)
                ? this.state.columnDefsDisSummaryPrts
                : (this.state.drillDown == 33 && this.state.category == 16) ||
                  (this.state.drillDown == 30 &&
                    (this.state.chartId == 1123 || this.state.chartId == 1124))
                ? this.state.columnDefsDiscountByRO
                : this.state.columnDefsRevenue

              // : this.state.drillDown == 33
              // ? this.state.columnDefsDiscountJobRo
              // : this.state.drillDown == 31
              // ? this.state.columnDefsDiscount
            }
            defaultColDef={this.state.defaultColDef}
            //sideBar={true}
            excelStyles={this.state.excelStyles}
            onGridReady={this.onGridReady}
            rowData={this.state.rowData}
            headerHeight={this.state.headerHeight}
            tooltipShowDelay={0}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            // domLayout="autoHeight"
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  }
});

export default withStyles(styles)(PayTypeDrilldown);
