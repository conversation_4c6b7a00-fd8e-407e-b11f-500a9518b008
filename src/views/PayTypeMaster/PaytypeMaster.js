/* eslint-disable no-shadow */
/* eslint-disable react/default-props-match-prop-types */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  Fade,
  Grid,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import CallMergeIcon from '@material-ui/icons/CallMerge';
import ErrorIcon from '@material-ui/icons/Error';
import ExportIcon from '@material-ui/icons/GetApp';
import InsertDriveFileIcon from '@material-ui/icons/InsertDriveFile';
import { withKeycloak } from '@react-keycloak/web';
import PropTypes from 'prop-types';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import FixedRateRenderer from './FixedRateRenderer';
import { AgGridReact } from '@ag-grid-community/react';
import RestoreIcon from '@material-ui/icons/Restore';
import 'src/grid.css';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { connect } from 'react-redux';
import {
  SET_PAY_TYPE_TAB,
  SET_REFRESH_STATUS,
  SET_REFRESH_ERROR_STATUS,
  SET_PAY_TYPE_ERROR,
  SET_PAY_TYPE_ERROR_COUNT,
  SET_DASHBOARD_RELOAD_STATUS
} from 'src/actions';
import {
  ADD_REFRESH_STATUS,
  UPDATE_PAY_TYPE_MASTER_BY_PAYTYPE,
  CLIENT_AUDITS,
  MV_REFRESH_STATUS
} from 'src/graphql/queries';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import makeApolloClientPostgresWrite from 'src/utils/apolloRootClientPostgresWrite';
import {
  getPayTypeMasterDetails,
  getRefreshViewsStatus,
  getPayTypeRetailFlag
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import PayTypeErrors from './PayTypeErrors';
import PayTypeDrilldown from './PayTypeDrilldown';
import PaytpeList from '../../components/PaytypeList';
// eslint-disable-next-line import/no-cycle
import { store } from '../../App';
// eslint-disable-next-line no-unused-vars

import { withStyles } from '@material-ui/styles';
import LoaderSkeleton from '../../components/LoaderSkeleton.js';
import { ReactSession } from 'react-client-session';
import GridCheckboxRenderer from './GridCheckboxRenderer';
import 'src/styles.css';
import { traceSpan } from 'src/utils/OTTTracing';
var lodash = require('lodash');
class PaytypeMaster extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ isLoading: true });
        this.getAgGridData();
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
        this.setState({
          editedRowId: null
        });

        this.setState({
          isCodeEdited: false
        });
        this.gridApi.redrawRows();
      }
    }
  }
  // eslint-disable-next-line react/static-property-placement
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       this.getAgGridData();
  //     }
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    const { keycloak } = this.props;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      setOpenAlertLog: false,
      showRefresh:
        this.props.session.dashboardReloadStatus == false ? 'none' : 'all',
      disableTable: 'all',
      fadein: true,
      rawGridApi: {},
      gridApi: {},
      oldRowArray: [],
      newRowArray: [],
      oldCodeArray: [],
      prevCodeArray: [],
      newCodeArray: [],
      gridExcludedValue: '',
      gridExcludedId: [],
      categorizedValue: '',
      editedRowId: null,
      prevEditedRowId: null,
      prevElementId: null,
      isCodeEdited: false,
      categorizedId: '',
      opcategoryArray: [],
      isCodeUpdated: false,
      isCodeRowUpdated: false,
      openSaveDialog: false,
      isReloaded: false,
      laborPayTypeCount: '',
      PartsPayTypeCount: '',
      isLoading: true,
      isPaytypeLoading: false,
      isLoadingSave: false,
      laborFixedRateOld: '',
      laborFixedRateNew: null,
      laborFixedDateOld: '',
      laborFixedDateNew: null,
      partsFixedRateOld: '',
      partsFixedRateNew: null,
      partsFixedDateOld: '',
      partsFixedDateNew: null,
      allPaytypeNames: [],
      selectedPaytypeIds: ['C'],

      columnDefs: [
        {
          headerName: 'Id',
          // width: 170,
          minWidth: 170,
          field: 'id',
          flex: 1,
          hide: true,
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Dealer Pay Type',
          chartDataType: 'series',
          // width: 120,
          minWidth: 100,
          flex: 1,
          field: 'payType',
          editable: false,
          tooltipField: 'payType',
          suppressMenu: true,
          unSortIcon: true,
          //flex: 1,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'FOPC Group',
          chartDataType: 'series',
          // width: 90,
          minWidth: 98,
          flex: 1,
          field: 'payTypeCode',

          suppressMenu: true,
          unSortIcon: true,
          //flex: 1,
          editable: false,
          singleClickEdit: true,
          filter: 'agSetColumnFilter',
          filterParams: {
            applyMiniFilterWhileTyping: true
          },
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },
          // editable: !(
          //   typeof keycloak.realmAccess.roles !== 'undefined' &&
          //   keycloak.realmAccess.roles.includes('client') === true
          // ),
          cellEditor: 'agSelectCellEditor',
          cellEditorParams: {
            values: paytypeC
          },
          filterParams: {
            valueFormatter: function(params) {
              return lookupValue(paytypeCodeMappings, params.value);
            }
          }
          // valueFormatter: function(params) {
          //   return lookupValue(paytypeCodeMappings, params.value);
          // }
        },
        {
          headerName: 'Dept.',
          chartDataType: 'series',
          // width: 90,
          minWidth: 80,
          flex: 1,
          field: 'department',
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellEditor: 'agSelectCellEditor',
          //hide: localStorage.getItem('dms') == 'dtk' ? true : false,
          hide: false,
          //flex: 2,
          cellEditorParams: {
            values: depart
          },
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: function(params) {
              return lookupValue(departmentMappings, params.value);
            }
          },
          valueFormatter: function(params) {
            return lookupValue(departmentMappings, params.value);
          }
        },
        {
          headerName: 'Labor / Parts',
          // width: 150,
          minWidth: 150,
          flex: 1,
          field: 'lbrOrPrts',

          suppressMenu: true,
          unSortIcon: true,
          hide: true,
          editable: !(
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') === true
          ),
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Compliance Excluded',
          // width: 120,
          minWidth: 120,
          flex: 1,
          field: 'gridExcluded',

          cellRenderer: 'gridCheckboxRenderer',
          suppressMenu: true,
          unSortIcon: true,
          hide: false,
          editable: !(
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') === true
          ),
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Labor Fixed Rate',
          minWidth: 170,
          // width: 200,
          flex: 1,
          field: 'laborFixedRate',

          cellRenderer: 'fixedRateRenderer',
          suppressMenu: true,
          unSortIcon: false,
          sortable: false,
          hide: false,
          editable: !(
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') === true
          ),
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        // {
        //   headerName: 'Labor Fixed Rate ($)',
        //   field: 'id',
        //   width: 100,
        //   cellClass: 'textAlign',
        //   cellRenderer: 'gridCheckboxRenderer',

        //   chartDataType: 'series',
        //   editable: false,
        //
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle() {
        //     return { border: ' 0px white' };
        //   }
        // },
        {
          headerName: 'Parts Fixed Rate',
          // width: 240,
          minWidth: 235,
          flex: 1,
          field: 'partsFixedRate',

          cellRenderer: 'fixedRateRenderer',
          suppressMenu: true,
          unSortIcon: false,
          sortable: false,
          hide: false,
          editable: !(
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') === true
          ),
          cellStyle() {
            return { border: ' 0px white' };
          }
        },

        // {
        //   headerName: 'Parts Fixed Rate',
        //   width: 250,
        //   field: 'partsFixedRate',

        //
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellEditor: 'agSelectCellEditor',
        //   editable: !(
        //     typeof keycloak.realmAccess.roles !== 'undefined' &&
        //     keycloak.realmAccess.roles.includes('client') === true
        //   ),
        //   cellEditorParams: {
        //     values: partGrp
        //   },
        //   cellStyle() {
        //     return { textAlign: 'left', border: ' 0px white' };
        //   },
        //   filterParams: {
        //     valueFormatter: function(params) {
        //       return lookupValue(partGrpMappings, params.value);
        //     }
        //   },
        //   valueFormatter: function(params) {
        //     return lookupValue(partGrpMappings, params.value);
        //   }
        // },

        {
          headerName: 'Action',
          field: 'action',
          minWidth: 90,
          // width: 90,
          flex: 1,
          filter: false,
          sortable: false,
          editable: false,

          suppressMenu: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false,
          hide:
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') == true
              ? true
              : false,
          // hide: true,
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            let index = params.rowIndex;
            const eDiv = document.createElement('div');
            eDiv.innerHTML = `<button   title="Edit" id="btneditpaytype${index}" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-paytype"><i class="fas fa-pencil-alt"></i></button> <button  title="Cancel" id="btncancelpaytype${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-paytype" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatepaytype${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-paytype" ><i class="fas fa-save"></i></button>`;
            // if (
            //   props.keycloak &&
            //   props.keycloak.realmAccess.roles.includes('admin') == false &&
            //   props.keycloak.realmAccess.roles.includes('superadmin') ==
            //     false &&
            //   props.keycloak.realmAccess.roles.includes('user') == false
            // ) {
            //   $(document).ready(function() {
            //     $('.edit-button-paytype').attr('disabled', 'disabled');
            //     $('.edit-button-paytype').css('background', '#38416373');
            //     $('.edit-button-paytype').css('cursor', 'default');
            //   });
            // }

            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll('.edit-button-paytype')[0];
              const uButton = eDiv.querySelectorAll(
                '.update-button-paytype'
              )[0];
              const cButton = eDiv.querySelectorAll(
                '.cancel-button-paytype'
              )[0];
              eButton.addEventListener('click', () => {
                // params.columnApi.getDisplayedCenterColumns()[0].colId

                if (startEdit.state.editedRowId != null) {
                  var rowNode = params.api.getDisplayedRowAtIndex(
                    startEdit.state.editedRowId
                  );

                  startEdit.setState({
                    prevEditedRowId: startEdit.state.editedRowId
                  });

                  startEdit.setState({
                    editedRowId: null
                  });
                  startEdit.setState({
                    isCodeEdited: false
                  });
                  params.api.refreshCells({
                    columns: ['partsFixedRate', 'laborFixedRate'],
                    rowNodes: [rowNode],
                    force: true
                  });
                }

                $(`.cancel-button-paytype`).hide();
                $(`.update-button-paytype`).hide();
                $(`.edit-button-paytype`).show();

                $('.grid-excluded').attr('disabled', 'disabled');
                $('.laborFixedRate').attr('disabled', 'disabled');
                $('.laborFixedRateValue').hide();
                $('.laborFixedRateDate').hide();
                $('.partsFixedRate').attr('disabled', 'disabled');
                $('.partsFixedRateDate').hide();
                $('.partsFixedRateValue').hide();
                $('.partsFixedRateSelect').hide();
                // $('.grid-excluded').attr('disabled', 'disabled');
                localStorage.setItem('oldId', params.data.id);
                localStorage.setItem('oldPayType', params.data.payType);
                localStorage.setItem('oldPayTypeCode', params.data.payTypeCode);
                localStorage.setItem('oldDepartment', params.data.department);

                var oldRow = [];
                oldRow.push(params.data.id);
                oldRow.push(params.data.lbrOrPrts);
                oldRow.push(params.data.payType);
                oldRow.push(params.data.payTypeCode);
                oldRow.push(params.data.department);
                oldRow.push(params.data.storeId);
                JSON.stringify(oldRow);

                localStorage.setItem('oldRow', oldRow);

                startEdit.setState({
                  editedRowId: index
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowPrev = {
                  id: params.data.id,
                  paytype: params.data.payType,
                  payTypeCode: params.data.payTypeCode,
                  department: params.data.department,
                  gridExcluded: params.data.gridExcluded,
                  laborFixedRate: params.data.laborFixedRate,
                  laborFixedratedate: params.data.laborFixedratedate,
                  laborFixedratevalue: params.data.laborFixedratevalue,
                  partsFixedRate: params.data.partsFixedRate,
                  partsFixedratedate: params.data.partsFixedratedate,
                  partsFixedratevalue: params.data.partsFixedratevalue,
                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                };
                startEdit.setState({
                  laborFixedDateNew: params.data.laborFixedratedate,
                  laborFixedRateNew: params.data.laborFixedratevalue,
                  partsFixedDateNew: params.data.partsFixedratedate,
                  partsFixedRateNew: params.data.partsFixedratevalue
                });
                var rowPrevArray = startEdit.state.prevCodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ id, paytype }) =>
                    id === rowPrev.id && paytype === rowPrev.paytype
                );

                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }

                startEdit.setState({
                  prevCodeArray: rowPrevArray
                });

                var prevRow = [];
                prevRow.push({
                  id: params.data.id,
                  payType: params.data.payType,
                  payTypeCode: params.data.payTypeCode,
                  department: params.data.department,
                  gridExcluded: params.data.gridExcluded,
                  laborFixedRate: params.data.laborFixedRate,
                  laborFixedratedate: params.data.laborFixedratedate,
                  laborFixedratevalue: params.data.laborFixedratevalue,
                  partsFixedRate: params.data.partsFixedRate,
                  partsFixedratedate: params.data.partsFixedratedate,
                  partsFixedratevalue: params.data.partsFixedratevalue,
                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                });

                startEdit.setState({
                  oldCodeArray: prevRow
                });

                //startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);
                $(`#btneditpaytype${index}`).hide();
                $(`#btncancelpaytype${index}`).show();
                $(`#btnupdatepaytype${index}`).show();
              });
              uButton.addEventListener('click', () => {
                // index = params.rowIndex;

                if (
                  ((params.data.laborFixedRate == 1 &&
                    startEdit.state.laborFixedRateNew != null &&
                    startEdit.state.laborFixedRateNew != 0 &&
                    startEdit.state.laborFixedRateNew != '' &&
                    startEdit.state.laborFixedRateNew != '0' &&
                    startEdit.state.laborFixedRateNew != '.') ||
                    params.data.laborFixedRate == 0) &&
                  ((params.data.partsFixedRate == 1 &&
                    startEdit.state.partsFixedRateNew != null &&
                    startEdit.state.partsFixedRateNew != 'N/A' &&
                    startEdit.state.partsFixedRateNew.split(' ')[1] != '' &&
                    startEdit.state.partsFixedRateNew != '' &&
                    startEdit.state.partsFixedRateNew.split(' ')[1] != '.') ||
                    params.data.partsFixedRate == 0)
                ) {
                  startEdit.onBtStopEditing(index);
                  localStorage.setItem('newId', params.data.id);
                  localStorage.setItem('newPayType', params.data.payType);
                  localStorage.setItem(
                    'newPayTypeCode',
                    params.data.payTypeCode
                  );
                  localStorage.setItem('newDepartment', params.data.department);
                  const selectedId = params.data.id;
                  const selectedPayType = params.data.payType;
                  const selectedPayTypeCode = params.data.payTypeCode;
                  const selectedPayTypeDept = params.data.department;
                  const oldDepartment = localStorage.getItem('oldDepartment');
                  const oldRow = localStorage.getItem('oldRow');
                  const storeId = JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0];
                  startEdit.setState({
                    isCodeEdited: false
                  });

                  var rowNode = params.api.getDisplayedRowAtIndex(
                    params.rowIndex
                  );
                  if (rowNode) {
                    rowNode.data['laborFixedratedate'] =
                      startEdit.state.laborFixedDateNew;
                    rowNode.data['laborFixedratevalue'] =
                      startEdit.state.laborFixedRateNew;
                    rowNode.data['partsFixedratedate'] =
                      startEdit.state.partsFixedDateNew;
                    rowNode.data['partsFixedratevalue'] =
                      startEdit.state.partsFixedRateNew;
                  }

                  startEdit.setState({
                    prevIndex: ''
                  });

                  startEdit.setState({
                    prevIndex: ''
                  });
                  var newRow = [];
                  newRow.push(
                    params.data.id,
                    params.data.payTypeCode,
                    params.data.department,

                    storeId
                  );
                  let oldDataArr = lodash.filter(
                    startEdit.state.prevCodeArray,
                    item => {
                      return (
                        item.paytype == params.data.payType &&
                        item.id == params.data.id
                      );
                    }
                  );
                  var updatedRow = {
                    paytype: params.data.payType,

                    oldpaytypecode: oldDataArr[0].payTypeCode,
                    newpaytypecode: params.data.payTypeCode,
                    olddepartment: oldDataArr[0].department,
                    newdepartment: params.data.department,
                    oldgridexcluded: oldDataArr[0].gridExcluded,
                    newgridexcluded: params.data.gridExcluded,
                    oldlabor_fixedratevalue:
                      oldDataArr[0].laborFixedRate == 0 &&
                      params.data.laborFixedRate == 1
                        ? params.data.oldLaborFixedratevalue
                        : oldDataArr[0].laborFixedratevalue,
                    newlabor_fixedratevalue: params.data.laborFixedratevalue,
                    oldparts_fixedratevalue: oldDataArr[0].partsFixedratevalue,
                    newparts_fixedratevalue: params.data.partsFixedratevalue,
                    oldlabor_fixedratedate:
                      oldDataArr[0].laborFixedRate == 0 &&
                      params.data.laborFixedRate == 1
                        ? params.data.oldLaborFixedratedate
                        : oldDataArr[0].laborFixedratedate,
                    newlabor_fixedratedate: params.data.laborFixedratedate,
                    oldparts_fixedratedate: oldDataArr[0].partsFixedratedate,
                    newparts_fixedratedate: params.data.partsFixedratedate,
                    oldlabor_fixed_rate: oldDataArr[0].laborFixedRate,
                    newlabor_fixed_rate: params.data.laborFixedRate,
                    oldparts_fixed_rate: oldDataArr[0].partsFixedRate,
                    newparts_fixed_rate: params.data.partsFixedRate
                  };
                  var rowArray = startEdit.state.newCodeArray;
                  let index = rowArray.findIndex(
                    ({ paytype }) => paytype == updatedRow.paytype
                  );

                  if (index === -1) {
                    if (
                      oldDataArr[0].payTypeCode != params.data.payTypeCode ||
                      oldDataArr[0].department != params.data.department ||
                      oldDataArr[0].gridExcluded != params.data.gridExcluded ||
                      oldDataArr[0].laborFixedRate !=
                        params.data.laborFixedRate ||
                      oldDataArr[0].laborFixedratedate !=
                        params.data.laborFixedratedate ||
                      oldDataArr[0].laborFixedratevalue !=
                        params.data.laborFixedratevalue ||
                      oldDataArr[0].partsFixedRate !=
                        params.data.partsFixedRate ||
                      oldDataArr[0].partsFixedratedate !=
                        params.data.partsFixedratedate ||
                      oldDataArr[0].partsFixedratevalue !=
                        params.data.partsFixedratevalue
                    ) {
                      rowArray.push(updatedRow);
                    }
                  } else {
                    rowArray[index] = updatedRow;
                  }
                  startEdit.setState({
                    newCodeArray: rowArray
                  });
                  if (startEdit.state.newCodeArray.length > 0) {
                    startEdit.setState({
                      isCodeRowUpdated: true
                    });
                  }

                  startEdit.setState({
                    laborfixedRateError: null,
                    partsfixedRateError: null
                  });
                  localStorage.setItem('newRow', newRow);
                  // startEdit.updatePayTypeMaster(
                  //   selectedId,
                  //   selectedPayType,
                  //   selectedPayTypeCode,
                  //   selectedPayTypeDept,
                  //   oldDepartment,
                  //   storeId
                  // );
                  startEdit.setState({
                    editedRowId: null
                  });

                  startEdit.gridApi.redrawRows();
                  $(`#btneditpaytype${index}`).show();
                  $(`#btncancelpaytype${index}`).hide();
                  $(`#btnupdatepaytype${index}`).hide();
                } else {
                  if (
                    params.data.laborFixedRate == 1 &&
                    (startEdit.state.laborFixedRateNew == null ||
                      startEdit.state.laborFixedRateNew == '' ||
                      startEdit.state.laborFixedRateNew == '0' ||
                      startEdit.state.laborFixedRateNew == '.' ||
                      !(startEdit.state.laborFixedRateNew > 0))
                  ) {
                    var rowNode = params.api.getDisplayedRowAtIndex(index);
                    rowNode.data['laborFixedratevalue'] = null;
                    startEdit.setState({
                      laborfixedRateError: index
                    });
                    // var rowNode = params.api.getDisplayedRowAtIndex(index);
                    // rowNode.data['laborFixedratevalue'] = null;
                    params.api.refreshCells({
                      columns: ['laborFixedRate'],
                      rowNodes: [params.node],
                      force: true
                    });
                  }

                  if (
                    params.data.partsFixedRate == 1 &&
                    (startEdit.state.partsFixedRateNew == null ||
                      startEdit.state.partsFixedRateNew == '' ||
                      startEdit.state.partsFixedRateNew == 'N/A' ||
                      (startEdit.state.partsFixedRateNew != null &&
                        (startEdit.state.partsFixedRateNew.split(' ')[1] ==
                          '' ||
                          startEdit.state.partsFixedRateNew.split(' ')[1] ==
                            '.' ||
                          !(
                            startEdit.state.partsFixedRateNew.split(' ')[1] > 0
                          ))))
                  ) {
                    startEdit.setState({
                      partsfixedRateError: index
                    });
                    var rowNode = params.api.getDisplayedRowAtIndex(index);
                    rowNode.data['partsFixedratevalue'] =
                      startEdit.state.partsFixedRateNew.split(' ')[0] + ' ';
                    params.api.refreshCells({
                      columns: ['partsFixedRate'],
                      rowNodes: [params.node],
                      force: true
                    });
                  }
                }
              });
              cButton.addEventListener('click', () => {
                //startEdit.getAgGridData();
                startEdit.onBtStopEditing(index);

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return (
                    o.id == params.data.id && o.paytype == params.data.payType
                  );
                });

                if (valArr.length > 0) {
                  rowNode.setDataValue('payTypeCode', valArr[0].payTypeCode);
                  rowNode.setDataValue('department', valArr[0].department);
                  rowNode.setDataValue('gridExcluded', valArr[0].gridExcluded);
                  rowNode.setDataValue(
                    'laborFixedRate',
                    valArr[0].laborFixedRate
                  );
                  rowNode.setDataValue(
                    'partsFixedRate',
                    valArr[0].partsFixedRate
                  );
                  rowNode.data['laborFixedratedate'] =
                    valArr[0].laborFixedratedate;
                  rowNode.data['laborFixedratevalue'] =
                    valArr[0].laborFixedratevalue;
                  rowNode.data['partsFixedratedate'] =
                    valArr[0].partsFixedratedate;
                  rowNode.data['partsFixedratevalue'] =
                    valArr[0].partsFixedratevalue;
                }
                startEdit.setState({
                  editedRowId: null
                });
                params.api.refreshCells({
                  columns: ['partsFixedRate', 'laborFixedRate'],
                  rowNodes: [rowNode],
                  force: true
                });
                let filteredArray = startEdit.state.newCodeArray.filter(
                  function(obj) {
                    return obj.paytype != params.data.payType;
                  }
                );
                startEdit.setState({
                  newCodeArray: filteredArray
                });

                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                }

                $(`#btneditpaytype${index}`).show();
                $(`#btncancelpaytype${index}`).hide();
                $(`#btnupdatepaytype${index}`).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        suppressKeyboardEvent: params => params.event.keyCode === 13,
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: true,
        suppressMovable: false
        // editable: true
      },
      editType: 'fullRow',
      frameworkComponents: {
        gridCheckboxRenderer: GridCheckboxRenderer,
        fixedRateRenderer: FixedRateRenderer
      },
      context: { componentParent: this },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },

        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  componentDidMount() {
    const { setTab } = this.props;
    if (store.getState().session.menuSelected == 'Pay Types') {
      setTab('one');
    }

    const userID = localStorage.getItem('userID');
  }
  onFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      fixedRateOld: oldVal
    });

    this.setState({
      fixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  getAgGridData(reset) {
    const filterModel = this.gridApi.getFilterModel();

    if (reset != 'reset') {
      this.setState({ isLoading: true });
    }

    // this.setState({ isLoading: true });

    getPayTypeMasterDetails(result => {
      this.setState({
        rowData: []
      });
      if (result.data.statelessCcPhysicalRwGetPaytypeMasterDetails.nodes) {
        this.getDistinctPaytypeCode(
          result.data.statelessCcPhysicalRwGetPaytypeMasterDetails.nodes
        );

        var roData = lodash.orderBy(
          result.data.statelessCcPhysicalRwGetPaytypeMasterDetails.nodes,
          'payTypeCode',
          'desc'
        );

        this.setState({
          rowData: roData
        });
        this.gridApi.setFilterModel(filterModel);
        //  this.gridApi.onFilterChanged();
        this.setState({ isLoading: false });
      }
    });
    getPayTypeRetailFlag(result => {
      this.setState({
        allPaytypeNames: []
      });
      if (result.data.statelessCcPhysicalRwGetPaytypeRetailFlagSettings.nodes) {
        var resultData =
          result.data.statelessCcPhysicalRwGetPaytypeRetailFlagSettings.nodes;
        // var roData = lodash.orderBy(
        //   result.data.statelessCcPhysicalRwGetPaytypeMasterDetails.nodes,
        //   'payTypeCode',
        //   'desc'
        // );
        this.setState({
          allPaytypeNames: resultData
        });
        // this.setState({ isLoading: false });
      }
    });
  }

  getDistinctPaytypeCode(resultData) {
    const laborPaytype = resultData.filter(pay => pay.lbrOrPrts === 'L');
    const partsPaytype = resultData.filter(pay => pay.lbrOrPrts === 'P');
    const distinctLaborPaytype = Array.from(
      new Set(laborPaytype.map(item => item.payType))
    );
    const distinctPartsPaytype = Array.from(
      new Set(partsPaytype.map(item => item.payType))
    );
    const laborPaytypeCount = distinctLaborPaytype.length;
    const partsPaytypeCount = distinctPartsPaytype.length;
    this.setState({
      laborPayTypeCount: laborPaytypeCount,
      PartsPayTypeCount: partsPaytypeCount
    });
  }

  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;
    groupColumn[5]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  updatePayTypeMaster = () => {
    const client = makeApolloClientPostgres;
    const apolloClient = makeApolloClientPostgresWrite;

    const coll = document.getElementsByClassName('edit-button-paytype');
    this.setState({ showRefresh: 'none' });
    this.setState({ disableTable: 'none' });

    this.setState({ isPaytypeLoading: true });
    // const oldId = localStorage.getItem('oldId');
    // const oldPayType = localStorage.getItem('oldPayType');
    // const oldPayTypeCode = localStorage.getItem('oldPayTypeCode');
    // const newId = localStorage.getItem('newId');
    // const newPayType = localStorage.getItem('newPayType');
    // const newPayTypeCode = localStorage.getItem('newPayTypeCode');
    // const newDepartment = localStorage.getItem('newDepartment');
    for (let i = 0, len = coll.length; i < len; i++) {
      coll[i].style['background-color'] = '#ccc';
    }
    if (this.state.newCodeArray.length > 0) {
      this.setState({ isLoadingSave: true });
      const coll = document.getElementsByClassName('edit-button-paytype');
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#ccc';
        coll[i].disabled = true; // Disable the button
      }
      this.setState({
        isCodeRowUpdated: false
      });
      let oldPayArr = [];
      this.state.prevCodeArray.map((obj, i) => {
        oldPayArr.push(Object.values(obj));
      });
      let newPayArr = [];
      this.state.newCodeArray.map((obj, i) => {
        newPayArr.push(Object.values(obj));
      });
      const start = new Date();
      const userID = localStorage.getItem('userID');
      apolloClient
        .mutate({
          mutation: UPDATE_PAY_TYPE_MASTER_BY_PAYTYPE,
          variables: {
            p_paytype: JSON.stringify(this.state.newCodeArray),
            store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
            user_id: userID
          }
        })
        .then(result => {
          const spanAttribute = {
            pageUrl: '/PayTypeMaster',
            origin: '',
            event: 'Menu Load',
            is_from: 'UPDATE_PAY_TYPE_MASTER_BY_PAYTYPE',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          if (
            result.data.statelessCcPhysicalRwUpdatePaytypeMasterByPaytypecode
              .updatePaytypeMasterStatuses[0].status == '1'
          ) {
            this.setState({
              prevCodeArray: []
            });

            this.props.setPaytypeErrorCount(true);

            this.setState({ isLoadingSave: false });
            this.setState({ disableTable: 'all' });
            this.setState({ setMessage: `Pay Types Updated.` });
            this.setState({ setOpenRow: true });
            for (let i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#384163';
            }
            const userIDs = localStorage.getItem('userID');
            const storeIds = JSON.parse(
              localStorage.getItem('selectedStoreId')
            )[0];
            const userRole = this.props.session.user.role;
            this.setState({ isPaytypeLoading: false });
            this.getAgGridData();

            const start = new Date();
            apolloClient
              .mutate({
                mutation: CLIENT_AUDITS,
                variables: {
                  dmlaction: 'Update paytype master',
                  newdata: JSON.stringify(newPayArr),
                  olddata: JSON.stringify(oldPayArr),
                  schemaname: 'stateless_cc_physical_rw',
                  storeId: storeIds,
                  tablename: 'pay_type_master',
                  username: userIDs,
                  userrole: userRole
                }
              })
              .then(result => {
                const spanAttribute = {
                  pageUrl: '/PayTypeMaster',
                  origin: '',
                  event: 'Menu Load',
                  is_from: 'CLIENT_AUDITS',
                  value: new Date() - start,
                  provenance: localStorage.getItem('provenance')
                };
                traceSpan('Menu Load', spanAttribute);
              });
          } else {
            this.setState({ isLoadingSave: false });
            this.setState({ showRefresh: 'all' });
            this.setState({ disableTable: 'all' });
            this.setState({
              setMessage: `Pay type update failed. Please contact support team to
            resolve.`
            });
            this.setState({ setOpenRow: true });
            for (let i = 0, len = coll.length; i < len; i++) {
              coll[i].style['background-color'] = '#384163';
            }
          }
        });
    } else {
      this.setState({ isLoadingSave: false });
      this.setState({ disableTable: 'all' });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    }
  };
  getRowStyle = params => {
    if (params.data.payTypeCode == null && params.data.department == null) {
      return { background: 'rgb(221, 234, 244)' };
    }
  };
  onLaborFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedRateOld: oldVal
    });

    this.setState({
      laborFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onLaborFixedDateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedDateOld: oldVal
    });

    this.setState({
      laborFixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedRateOld: oldVal
    });

    this.setState({
      partsFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedDateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedDateOld: oldVal
    });

    this.setState({
      partsFixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: [
        'payTypeCode',
        'department',

        'gridExcluded',

        'laborFixedRate',
        'partsFixedRate'
      ],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: [
        'payTypeCode',
        'department',
        'gridExcluded',

        'laborFixedRate',
        'partsFixedRate'
      ],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApi.redrawRows();
  };
  onSortChanged = e => {
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApi.redrawRows();
  };

  resetReportGrid = () => {
    this.gridApi.setColumnDefs(this.state.columnDefs);

    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false,
      isCodeRowUpdated: false
    });
    this.gridApi.redrawRows();
    this.state.gridcolumnApi.resetColumnState();
    this.getAgGridData('reset');
  };

  refreshDisable = () => {
    this.setState({ showRefresh: 'none' });
  };

  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      if (localStorage.getItem('dms') == 'cdk') {
        groupColumn[3]['editable'] = true;
      } else {
        groupColumn[3]['editable'] = false;
      }
      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = true;

      groupColumn[4]['editable'] = false;
      groupColumn[5]['editable'] = false;
      groupColumn[6]['editable'] = false;
      groupColumn[7]['editable'] = false;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    const { rawGridApi } = this.state;
    this.state.rawGridApi.setFocusedCell(
      index,
      'payTypeCode',
      'department',
      pinned
    );
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'department',
      colKey: 'payTypeCode',
      //colKey: 'dbdName',

      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
    // rawGridApi.setFocusedCell(index, 'payTypeCode', pinned);
    // rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'payTypeCode',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
    // const { rawGridApi } = this.state;
    // rawGridApi.setFocusedCell(index, 'payTypeCode', pinned);
    // rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'payTypeCode',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridcolumnApi: params.columnApi });

    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  onBtExport = () => {
    const { rawGridApi } = this.state;
    const allColumnKeys = this.state.columnDefs
      .filter(col => col.field !== 'action' && !col.hide)
      .map(col => col.field);
    const params = {
      sheetName: 'Report',
      fileName:
        localStorage.getItem('dms') == 'cdk'
          ? 'Line Payment Method'
          : 'Pay Types',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value:
                localStorage.getItem('dms') == 'cdk'
                  ? 'Line Payment Method'
                  : 'Pay Types'
            },
            mergeAcross: 3
          }
        ]
      ],
      columnKeys: allColumnKeys
    };
    rawGridApi.exportDataAsExcel(params);
  };

  handleTabChange = (event, newValue) => {
    const { setTab } = this.props;
    // $('.edit-button-paytype').hide();

    setTab(newValue);
    const coll = document.getElementsByClassName('edit-button-paytype');
    // if (newValue === 'one') {
    //   for (let i = 0, len = coll.length; i < len; i++) {
    //     coll[i].style['background-color'] = '#ccc';
    //   }
    // }
    // $('.edit-button-paytype').hide();
    // $('.edit-button-paytype').css({
    //   'background-color': 'yellow',
    //   'font-size': '200%'
    // });
  };

  handleClose = () => {
    this.setState({ setOpen: false });
  };

  handleCloseRow = () => {
    this.setState({ setOpenRow: false });
  };

  onBtClose = () => {
    this.setState({ setOpenAlert: false });
  };

  onBtCloseError = () => {
    this.setState({ setOpenAlertError: false });
  };
  onBtCloseErrorLog = () => {
    this.setState({ setOpenAlertLog: false });
  };
  handleClickSaveOpcode = () => {
    this.setState({
      openSaveDialog: true
    });
  };
  handleCancel = () => {
    this.setState({
      openSaveDialog: false
    });
  };
  handleSave = () => {
    this.updatePayTypeMaster();
    this.setState({
      openSaveDialog: false
    });
  };
  handleChange = status => {
    const { handleReload, handleRefresh } = this.props;
    const coll = document.getElementsByClassName('edit-button-paytype');
    if (status === 0) {
      this.setState({ showRefresh: 'none' });
      this.setState({ disableTable: 'all' });
      this.setState({ setOpenRow: false });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    } else if (status === 1) {
      this.setState({ showRefresh: 'all' });
      this.setState({ disableTable: 'all' });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    } else if (status === 2) {
      handleReload(false);
      handleRefresh(true);
    } else if (status === 3) {
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
      handleReload(true);
      handleRefresh(false);
    }
  };
  onCellClicked = params => {
    const id = localStorage.getItem('oldId');

    let rowId = this.state.editedRowId;

    if (params.data.id != id) {
      $(`.edit-button-paytype`).show();
      $(`.update-button-paytype`).hide();
      $(`.cancel-button-paytype`).hide();
      $(`#btncancelpaytype` + rowId).click();

      $('.grid-excluded').attr('disabled', 'disabled');
      $('.laborFixedRate').attr('disabled', 'disabled');
      $('.laborFixedRateValue').hide();
      $('.laborFixedRateDate').hide();
      $('.partsFixedRate').attr('disabled', 'disabled');
      $('.partsFixedRateDate').hide();
      $('.partsFixedRateValue').hide();
      $('.partsFixedRateSelect').hide();
    }
  };
  updatePaytypeList = paytype => {
    // this.setState({ selectedPaytypeIds: paytype });
    this.getAgGridData('reset');
    this.setState({
      isCodeEdited: false,
      isCodeRowUpdated: false
    });
  };
  render() {
    const {
      showRefresh,
      disableTable,
      setOpen,
      setOpenRow,
      setOpenAlert,
      setMessage,
      setOpenAlertError,
      isLoading,
      fadein,
      autoGroupColumnDef,
      columnDefs,
      defaultColDef,
      rowData,
      excelStyles,
      laborPayTypeCount,
      PartsPayTypeCount,
      setOpenAlertLog
    } = this.state;
    const coll = document.getElementsByClassName('edit-button-paytype');
    if (disableTable === 'none') {
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#ccc';
      }
    }
    const { keycloak, classes } = this.props;
    let boxClass = ['ag-header-cell'];
    // if(this.state.addClass) {
    boxClass.push('add');
    // }
    {
      boxClass.join(' ');
    }
    let abc = localStorage.getItem('selectedStoreId');

    return (
      <div>
        <Dialog
          open={setOpenRow}
          onClose={this.handleCloseRow}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          // style={{ width: 350,height: 350 }}paddingLeft: '20px'
        >
          {/* <DialogTitle
            id="alert-dialog-title"
            style={{ paddingLeft: 108, paddingRight: 85 }}
          >
            Paytype Master
          </DialogTitle> */}
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              // style={{ paddingLeft: 85, paddingRight: 85 }}
            >
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                {setMessage}
              </Typography>
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCloseRow} color="primary" autoFocus>
              OK
            </Button>
          </DialogActions>
        </Dialog>

        <Paper
          square
          style={{ margin: 8 }}
          // style={{
          //   margin: 8,
          //   backgroundColor:
          //         process.env.REACT_APP_DEALER === 'Armatus'
          //           ? '#ddeaf4'
          //           : '#F4E1E7',
          //       border:
          //         process.env.REACT_APP_DEALER === 'Armatus'
          //           ? '1px solid #003d6b'
          //           : '1px solid #C2185B',
          // }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            classes={{ flexContainer: classes.flexContainer }}
            showrefresh
            aria-label="icon label tabs example"
            TabIndicatorProps={{ style: { display: 'none' } }}
          >
            <Tab
              label={
                <div>
                  {localStorage.getItem('dms') == 'cdk'
                    ? 'Line Payment Method'
                    : 'Pay Types'}
                </div>
              }
              value="one"
              style={{
                pointerEvents: 'none',
                textTransform: 'none',
                backgroundColor:
                  process.env.REACT_APP_DEALER === 'Armatus'
                    ? '#ddeaf4'
                    : '#F4E1E7',
                border:
                  process.env.REACT_APP_DEALER === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color:
                  process.env.REACT_APP_DEALER === 'Armatus'
                    ? '#003d6b'
                    : '#c2185b'
              }}
            />

            <Button
              variant="outlined"
              style={
                this.state.isCodeRowUpdated
                  ? {
                      margin: 8,
                      padding: '0 6px !important',
                      pointerEvents: 'all',
                      color: '#fff',
                      float: 'right',
                      background:
                        process.env.REACT_APP_DEALER == 'Armatus'
                          ? '#003d6b'
                          : '#C2185B',
                      width: '8%',
                      height: 24,
                      fontSize: '12px',
                      position: 'relative',
                      textTransform: 'none'
                    }
                  : {
                      margin: 8,
                      padding: '0 6px !important',
                      pointerEvents: 'none',
                      color: '#ccc',
                      float: 'right',
                      background: '#fff',
                      width: '8%',
                      position: 'relative',
                      textTransform: 'none',
                      fontSize: '12px',
                      height: 24,
                      display: !!(
                        typeof this.props.keycloak.realmAccess.roles !==
                          'undefined' &&
                        this.props.keycloak.realmAccess.roles.includes(
                          'client'
                        ) === true
                      )
                        ? 'none'
                        : 'flex'
                    }
              }
              // style={{
              //   margin: 8,
              //   padding: 10,
              //   pointerEvents: 'all',
              //   color: '#fff',
              //   float: 'right',
              //   background: '#003d6b',
              //   width: '10%'
              // }}
              //disabled={this.state.newOpcodeArray <= 0 ? 'false' : 'true'handleClickSaveOpcode}
              onClick={this.handleClickSaveOpcode}
            >
              Save Changes
            </Button>

            <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{ paddingRight: 19, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
            <Button
              variant="contained"
              id="reset-layout"
              style={{ margin: 8, marginTop: 11 }}
              className={clsx(
                classes.resetReportGridback,
                classes.reset,
                'reset-btn'
              )}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>
        {(typeof keycloak.realmAccess.roles !== 'undefined' &&
          keycloak.realmAccess.roles.includes('admin') === true) ||
        keycloak.realmAccess.roles.includes('superadmin') === true ||
        keycloak.realmAccess.roles.includes('user') === true ? (
          <>
            {store.getState().session.payTypeInitialTab === 'two' ? (
              <PayTypeErrors
                disableTable={disableTable}
                handleChange={this.handleChange}
                showRefresh={showRefresh}
                session={this.props.session}
              />
            ) : null}
            {store.getState().session.payTypeInitialTab === 'three' ? (
              <PayTypeDrilldown />
            ) : null}
          </>
        ) : null}

        <>
          {isLoading === true ? (
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          ) : null}
        </>

        {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
        this.props.keycloak.realmAccess.roles.includes('client') != true ? (
          <>
            <Typography
              style={{
                paddingLeft: '10px',
                display: this.state.isLoading == true ? 'none' : 'block',
                fontWeight: 'bold',
                color: '#003d6b',
                fontSize: 13
              }}
            >
              1) Please edit your Pay Type(s) here. You may edit as many as you
              like simultaneously, and then please click “Save Changes”.
            </Typography>

            {/* <Typography
              style={{
                paddingLeft: '10px',
                display:
                  this.state.isLoading != true &&
                  showRefresh == 'all' &&
                  this.state.isPaytypeLoading === false
                    ? 'block'
                    : 'none',
                fontWeight: 'bold',
                color: 'red',
                fontSize: 13
              }}
            >
              3) There is a pending reload to the Dashboard. Please click on
              Reprocess Data History to refresh the data before proceeding to
              edit pay types.
            </Typography> */}
          </>
        ) : (
          ''
        )}
        <Grid container>
          <Grid
            item
            xs={6}
            style={{
              padding: '8px',
              maxWidth: '100%',
              flexBasis: '100%'
            }}
          >
            <Grid container spacing={2}>
              <Grid item xs={12} sm={9}>
                <Fade in={fadein} timeout={1200}>
                  <div
                    id="data-tab"
                    className={clsx(
                      this.props.keycloak.realmAccess.roles.includes(
                        'client'
                      ) === true
                        ? classes.dataGridClient
                        : classes.dataGrid,
                      'ag-theme-balham'
                    )}
                    style={{
                      height: window.innerHeight - 200 + 'px',
                      alignContent: 'center',
                      marginLeft: '8px',
                      paddingRight: '16px',
                      display: this.state.isLoading == true ? 'none' : 'block'
                    }}
                  >
                    {this.state.isLoadingSave && (
                      <div
                        style={{
                          position: 'absolute',
                          left: '58%',
                          zIndex: 1,
                          top: '50%'
                        }}
                      >
                        <LoaderSkeleton />
                      </div>
                    )}
                    <AgGridReact
                      className="ag-theme-balham grid-cell-centered "
                      style={{
                        height: '500px',
                        width: '100%'
                      }}
                      onRowEditingStarted={this.onRowEditingStarted}
                      onRowEditingStopped={this.onRowEditingStopped}
                      onFilterChanged={this.onFilterChanged}
                      onSortChanged={this.onSortChanged}
                      getRowStyle={this.getRowStyle}
                      autoGroupColumnDef={autoGroupColumnDef}
                      getChartToolbarItems={this.getChartToolbarItems}
                      modules={AllModules}
                      columnDefs={columnDefs}
                      defaultColDef={defaultColDef}
                      onGridReady={this.onGridReady}
                      suppressAggFuncInHeader
                      rowData={rowData}
                      excelStyles={excelStyles}
                      editType={this.state.editType}
                      suppressClickEdit={true}
                      onCellClicked={this.onCellClicked}
                      floatingFilter={true}
                      context={this.state.context}
                      frameworkComponents={this.state.frameworkComponents}
                      suppressDragLeaveHidesColumns={true}
                      suppressContextMenu={true}
                    />
                  </div>
                </Fade>
              </Grid>
              <Grid
                item
                xs={12}
                sm={3}
                className="report-card-sidebar three-month-sidebar"
                style={{
                  display: this.state.isLoading == true ? 'none' : 'block'
                }}
              >
                <Box sx={{ mb: 1 }}></Box>
                <Box className="card-selectors" sx={{ mb: 1 }}>
                  <PaytpeList
                    allPaytypeNames={this.state.allPaytypeNames}
                    selectedPaytypeIds={this.state.selectedPaytypeIds}
                    updatePaytypeList={this.updatePaytypeList}
                    keycloak={this.props.keycloak}
                    isPaytypeLoading={this.state.isPaytypeLoading}
                  />
                </Box>
                {/* <Box className="card-selectors" sx={{ mb: 1 }}></Box>
            <Box className="card-selectors" sx={{ mb: 1 }}></Box> */}
              </Grid>
            </Grid>
          </Grid>
        </Grid>

        <Dialog
          //fullWidth
          //maxWidth="sm"
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openSaveDialog}
        >
          {/* <DialogTitle id="confirmation-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Save changes
            </Typography>
          </DialogTitle> */}
          <DialogContent>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to continue?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={this.handleCancel}>
              Cancel
            </Button>
            <Button onClick={this.handleSave} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}
const partGrpMappings = {
  'N/A': 'N/A',
  'Part List': 'Part List',
  'Part Cost': 'Part Cost'
};
const departmentMappings = {
  'Body Shop': 'Body Shop',
  Service: 'Service'
};
const paytypeMappings = {
  CACC: 'CACC',
  CB: 'CB',
  CBACC: 'CBACC',
  CBF: 'CBF',
  CBM: 'CBM',
  CP: 'CP',
  CPD: 'CPD',
  CPE: 'CPE',
  CPL: 'CPL',
  CPM: 'CPM',
  CPQ: 'CPQ',
  CPT: 'CPT',
  CPW: 'CPW',
  CRENT: 'CRENT',
  CSC: 'CSC',
  IACC: 'IACC',
  IB: 'IB',
  IBACC: 'IBACC',
  IBL: 'IBL',
  IBNC: 'IBNC',
  IBNT: 'IBNT',
  IBR: 'IBR',
  IBS: 'IBS',
  IBUC: 'IBUC',
  IBUT: 'IBUT',
  ILDS: 'ILDS',
  INC: 'INC',
  INT: 'INT',
  IPDI: 'IPDI',
  IPV: 'IPV',
  IRENT: 'IRENT',
  IRM: 'IRM',
  ISB: 'ISB',
  ISV: 'ISV',
  IUC: 'IUC',
  IUT: 'IUT',
  W: 'W',
  WB: 'WB',
  WGMPP: 'WGMPP',
  WQL: 'WQL'
};
const paytypeCodeMappings = {
  C: 'C',
  E: 'E',
  F: 'F',
  I: 'I',
  M: 'M',
  W: 'W'
};
function extractValues(mappings) {
  return Object.keys(mappings);
}
const partGrp = extractValues(partGrpMappings);
const depart = extractValues(departmentMappings);
const paytyp = extractValues(paytypeMappings);
const paytypeC = extractValues(paytypeCodeMappings);
function lookupValue(mappings, key) {
  return mappings[key];
}
function lookupKey(mappings, name) {
  const keys = Object.keys(mappings);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    if (mappings[key] === name) {
      return key;
    }
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  back: {
    float: 'right',
    marginTop: '8px',
    marginRight: '17px'
  },
  reset: {
    width: '130px',
    margin: '8px !important'
  },
  dataGrid: {
    // '@media (max-width: 1920px)': {
    //   width: 985
    //   //fontSize: 25,
    //   //marginLeft: '23%'
    // },
    // '@media (max-width: 1280px)': {
    //   width: 985
    //   //marginLeft: '25%'
    // },
    // '@media (min-width: 2304px)': {
    //   width: 985
    //   //fontSize: 30,
    //   // marginLeft: '45%'
    // }
  },
  dataGridClient: {
    // '@media (max-width: 1920px)': {
    //   width: 895
    //   //fontSize: 25,
    //   //marginLeft: '23%'
    // },
    // '@media (max-width: 1280px)': {
    //   width: 880
    //   //marginLeft: '25%'
    // },
    // '@media (min-width: 2304px)': {
    //   width: 880
    //   //fontSize: 30,
    //   // marginLeft: '45%'
    // }
  },
  flexContainer: {
    alignItems: 'center'
  }
});
PaytypeMaster.propTypes = {
  keycloak: PropTypes.object,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setTab: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func,
  setDashboardReloadStatus: PropTypes.func
};
export default connect(mapStateToProps, mapDispatchToProps)(
  withKeycloak(withStyles(styles)(PaytypeMaster)),
  PaytypeMaster
);

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setDashboardReloadStatus: data =>
      dispatch({ type: SET_DASHBOARD_RELOAD_STATUS, payload: data }),
    setTab: data => dispatch({ type: SET_PAY_TYPE_TAB, payload: data }),
    setRefreshStatus: data =>
      dispatch({ type: SET_REFRESH_STATUS, payload: data }),
    setRefreshErrorStatus: data =>
      dispatch({ type: SET_REFRESH_ERROR_STATUS, payload: data }),
    setError: data => dispatch({ type: SET_PAY_TYPE_ERROR, payload: data }),
    setErrorCount: data =>
      dispatch({ type: SET_PAY_TYPE_ERROR_COUNT, payload: data })
  };
}

const SummaryTitle = ({ title, value }) => (
  <Grid item xs>
    <Typography variant="h5" color="primary">
      {title}:
    </Typography>
    <Typography variant="subtitle1"> {value}</Typography>
  </Grid>
);

SummaryTitle.propTypes = {
  title: PropTypes.string,
  value: PropTypes.string
};
