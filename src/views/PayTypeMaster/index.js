import React, { useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import $ from 'jquery';
import PaytypeMaster from './PaytypeMaster';
import { useHistory } from 'react-router';
import { getAllOpcodeErrors } from 'src/utils/hasuraServices';
import {
  setRefreshStatus,
  setReloadStatus,
  setNavItems,
  setAllErrorsCount,
  setPayTypeError,
  setPayTypeErrorCount
} from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function PayTypeMasterEdit({ route }) {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const [notifications, setNotifications] = React.useState([]);
  // useEffect(() => {
  //   dispatch(setNavItems(['Reference / Setups']));
  // }, []);

  $(document).ready(function() {
    var element = document.getElementById('Pay Types');
    element.classList.add('active-menu');
  });

  const addPaytypeErrorCount = status => {
    let mounted = true;
    getAllOpcodeErrors(callback => {
      const paytypeErrorArr = callback.filter(function(el) {
        return el.cType === 'paytype_errors';
      });
      var err = callback.reduce(
        (acc, o) => acc + parseInt(o.noncategorizedcount),
        0
      );
      var b = lodash.filter(callback, function(o) {
        if (o.noncategorizedcount > 0) return o;
      }).length;
      dispatch(setAllErrorsCount(b));
      console.log('aaaa===', paytypeErrorArr);
      //dispatch(setAllErrorsCount(err));
      if (paytypeErrorArr.length > 0) {
        if (mounted) {
          if (paytypeErrorArr[0].noncategorizedcount > 0) {
            dispatch(setPayTypeError(true));
            dispatch(
              setPayTypeErrorCount(paytypeErrorArr[0].noncategorizedcount)
            );
          } else {
            dispatch(setPayTypeError(false));
            dispatch(setPayTypeErrorCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setPayTypeError(false));
        dispatch(setPayTypeErrorCount(0));
      }
    });
  };
  return (
    <Page className={classes.root} title="Pay Types">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <PaytypeMaster
          route={route}
          history={history}
          setPaytypeErrorCount={addPaytypeErrorCount}
          session={session}
        />
      )}
    </Page>
  );
}

export default PayTypeMasterEdit;
