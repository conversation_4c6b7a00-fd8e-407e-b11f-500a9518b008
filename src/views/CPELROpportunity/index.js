import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useSelector, useDispatch } from 'react-redux';
import Charts from './Charts';
import { useHistory } from 'react-router';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function CPELROpportunity() {
  const classes = useStyles();
  const history = useHistory();
  const session = useSelector(state => state.session);
  return (
    <Page
      className={classes.root}
      // title="CP ELR Opportunity"
      title={'“What If” Opportunity \n Effective Labor Rate'}
    >
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts session={session} history={history} />
      )}
    </Page>
  );
}

export default CPELROpportunity;
