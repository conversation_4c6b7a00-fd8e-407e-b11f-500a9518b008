import React, { useEffect } from 'react';
import { useApolloClient } from '@apollo/client';

import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { Redirect } from 'react-router-dom';
import Charts from './Charts';
// import client from 'src/utils/apolloRootClient';
// import {
//   GET_CP_LABOR__REVENUE_DATA,
//   GET_CP_LABOR_GP_DATA,
//   GET_CP_LABOR_GP_PERC_DATA,
//   GET_CP_LABOR_SOLDHOURS_DATA
// } from 'src/graphql/queries';

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function CPLaborOverview() {
  const classes = useStyles();
  const history = useHistory();
  // //const client = useApolloClient();

  // useEffect(() => {
  //   Promise.all([
  //     client.mutate({
  //       mutation: GET_CP_LABOR__REVENUE_DATA,
  //       variables: { advisor: 'All' } // <-- variable here
  //     }),
  //     client.mutate({
  //       mutation: GET_CP_LABOR_GP_DATA,
  //       variables: { advisor: 'All' } // <-- variable here
  //     }),
  //     client.mutate({
  //       mutation: GET_CP_LABOR_GP_PERC_DATA,
  //       variables: { advisor: 'All' } // <-- variable here
  //     }),
  //     client.mutate({
  //       mutation: GET_CP_LABOR_SOLDHOURS_DATA,
  //       variables: { advisor: 'All' } // <-- variable here
  //     })
  //   ])
  //     .then(
  //       ([
  //         get_CP_LABOR_REVENUE,
  //         get_CP_LABOR_GP,
  //         get_CP_LABOR_GP_PERC,
  //         get_CP_LABOR_SOLD_HOURS
  //       ]) => {
  //         console.log('Fetched User:', get_CP_LABOR_REVENUE.data);
  //         console.log('Created User:', get_CP_LABOR_GP.data);
  //         console.log('Fetched User: 1', get_CP_LABOR_GP_PERC.data);
  //         console.log('Created User: 1', get_CP_LABOR_SOLD_HOURS.data);
  //       }
  //     )
  //     .catch(console.error);
  // }, [client]);

  return (
    <Page className={classes.root} title="CP Labor Overview">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts history={history} />
      )}
    </Page>
  );
}

export default CPLaborOverview;
