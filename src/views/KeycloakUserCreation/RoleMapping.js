import * as React from 'react';
import {
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Checkbox,
  Grid,
  Card,
  CardHeader,
  Typography,
  CardContent,
  Collapse,
  Dialog
} from '@material-ui/core';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { element } from 'prop-types';
import { el } from 'date-fns/locale';
import { desc } from 'react-dom-factories';
import { useEffect } from 'react';
import {
  getRealmsRoles,
  getUsersAssignedRoles
} from 'src/utils/hasuraServices';
import { UPDATE_USER_PASSWORD } from 'src/graphql/queries';
import { theme } from 'highcharts';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { ASSIGN_USER_ROLE, REMOVE_USER_ROLE } from 'src/graphql/queries';
import Alert from '@material-ui/lab/Alert';
import CloseIcon from '@material-ui/icons/Close';
import IconButton from '@material-ui/core/IconButton';
import 'src/styles.css';
const label = { inputProps: { 'aria-label': 'Switch demo' } };

function not(a, b) {
  return a.filter(value => b.indexOf(value) === -1);
}

function intersection(a, b) {
  return a.filter(value => b.indexOf(value) !== -1);
}

function union(a, b) {
  return [...a, ...not(b, a)];
}
const useStyles = makeStyles(theme => ({
  mainLabel: {
    marginLeft: -20,
    display: 'flex',
    color: 'rgb(0, 61, 107)',
    fontSize: 12
  },
  roleMappingContent: {
    width: '70%',
    margin: 'auto'
  },
  titleLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  card: {
    width: '98%',
    marginLeft: '20px',
    marginTop: '28px',
    height: '400px'

    // border: 'solid 1px',
    // borderColor: '#084573'
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    marginTop: '16px'
  },
  txt: {
    width: '400px'
  },
  rmdata: {
    width: '30%',
    float: 'left',
    margin: '0 1%'
  },
  rmdatadesc: {
    border: '1px solid #a7a7a7',
    minHeight: '150px'
  },
  rolelabel: {
    padding: '8px 0'
  },
  rmbutton: {
    marginTop: '10px'
  },
  addremovebtn: {
    width: '100px',
    cursor: 'pointer'
  },
  selected: {
    backgroundColor: '#ddeaf4',
    '&:focus': {
      backgroundColor: '#ddeaf4'
    }
  },
  dialog: {
    position: 'absolute',
    top: 50
  }
}));
const RoleMapping = props => {
  const [checked, setChecked] = React.useState([]);
  const [left, setLeft] = React.useState([]);
  const [right, setRight] = React.useState([]);
  const [selectedIndex, setSelectedIndex] = React.useState(null);
  const [leftSelected, setLeftSelected] = React.useState([]);
  const [rightSelected, setRightSelected] = React.useState([]);
  const [selectedItem, setSelectedItem] = React.useState(null);
  const [openAlert, setOpenAlert] = React.useState(false);
  const [alertMsg, setAlertMsg] = React.useState('');
  const [alertType, setAlertType] = React.useState('');
  const [userName, setUserName] = React.useState('');
  const [roles, setRoles] = React.useState('');
  const [actualRoles, setActualRoles] = React.useState([
    { role: 'user', name: 'Group/Store Admin' },
    { role: 'admin', name: 'Armatus Admin' },
    { role: 'superadmin', name: 'Super Admin' },
    { role: 'client', name: 'Group/Store Viewer' }
  ]);
  const classes = useStyles();
  useEffect(() => {
    var username = props && props.session && props.session.userId;
    setUserName(username);

    getRealmsRole(username);
  }, []);

  const getRealmsRole = username => {
    let realm = localStorage.getItem('realm');
    var roleNames = [];
    getRealmsRoles(realm, result => {
      if (result.data.statelessKeycloakServiceGetRealmRoles) {
        var roData = JSON.parse(
          result.data.statelessKeycloakServiceGetRealmRoles
        );
        var roles = roData.filter(item => item.description == 'CUSTOM');
        roles.map(item => {
          roleNames.push(item.name);
        });
        setRoles(roleNames);
        var actualRoleNames = roleNames.map(
          e => actualRoles.find(o => o.role === e).name
        );
        setLeft(actualRoleNames);
        sessionStorage.setItem('role', actualRoleNames);
        if (username) {
          getAssignedUserRoles(username);
        }
      }
    });
  };
  const getAssignedUserRoles = username => {
    var roleNames = [];
    getUsersAssignedRoles(username, result => {
      if (result.data.statelessKeycloakServiceGetUserRealmRoles) {
        var roData = JSON.parse(
          result.data.statelessKeycloakServiceGetUserRealmRoles
        );
        // roData.filter(item => item.description == 'CUSTOM')
        roData.map(item => {
          if (!item.name.includes('-') && !item.name.includes('_'))
            roleNames.push(item.name);
        });
        var actualRoleNames = roleNames.map(
          e => actualRoles.find(o => o.role === e).name
        );
        setRight(actualRoleNames);
        const retrievedValue = sessionStorage.getItem('role');
        const converted = retrievedValue.split(',');
        const filteredArray = converted.filter(
          value => !actualRoleNames.includes(value)
        );
        setLeft(filteredArray);
      }
    });
  };
  const assignUserRole = () => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: ASSIGN_USER_ROLE,
        variables: {
          rolename: selectedItem,
          username: userName
        }
      })
      .then(result => {
        setOpenAlert(true);
        setAlertMsg('Role Mappings Updated');
        setAlertType('success');
        setTimeout(() => {
          setOpenAlert(false);
          //  setAlertMsg('');
        }, 2000);
      });
  };
  const removeUserRole = () => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: REMOVE_USER_ROLE,
        variables: {
          rolename: selectedItem,
          username: userName
        }
      })
      .then(result => {
        setOpenAlert(true);
        setAlertMsg('Role Mappings Updated');
        setAlertType('success');
        setTimeout(() => {
          setOpenAlert(false);
          // setAlertMsg('');
        }, 2000);
      });
  };
  const handleCheckedRight = () => {
    if (userName) {
      var isExist = right.some(element => leftSelected.includes(element));
      var filtered = left.filter(element => element != leftSelected);
      if (!isExist) {
        if (right.length == 0) {
          setRight(right.concat(leftSelected));
          assignUserRole();
          setLeft(filtered);
          setLeftSelected([]);
        } else {
          setOpenAlert(true);
          setAlertMsg(
            'Role already assigned.Please remove assigned role to add a new role.'
          );
          setAlertType('warning');
          setTimeout(() => {
            setOpenAlert(false);
          }, 3000);
        }
      }
    } else {
      setOpenAlert(true);
      setAlertMsg('Please Add User Details');
      setAlertType('warning');
      setTimeout(() => {
        setOpenAlert(false);
      }, 2000);
    }
  };

  const handleCheckedLeft = () => {
    if (userName) {
      var isExist = left.some(element => rightSelected.includes(element));
      var filtered = right.filter(element => element != rightSelected);
      var userID = props.session.userName;

      if (!isExist) {
        if (userID != userName) {
          setLeft(left.concat(rightSelected));
          setRight(filtered);
          removeUserRole();
          setRightSelected([]);
        } else {
          setOpenAlert(true);
          setAlertMsg('Cannot remove role of the logged in user ');
          setAlertType('warning');
          setTimeout(() => {
            setOpenAlert(false);
            // setAlertMsg('');
          }, 2000);
        }
      }
    } else {
      setOpenAlert(true);
      setAlertMsg('Please Add User Details');
      setAlertType('warning');
      setTimeout(() => {
        setOpenAlert(false);
        setAlertMsg('');
      }, 2000);
    }
  };
  const handleListItemClick = (event, index, item, title) => {
    setSelectedIndex(index);
    var actualRoleNames = actualRoles.find(o => o.name === item);
    setSelectedItem(actualRoleNames.role);
    setSelectedIndex(item);
    var selectedRole = [];
    selectedRole.push(item);
    if (title == 'Choices') {
      setLeftSelected(selectedRole);
      setRightSelected([]);
    } else {
      setRightSelected(selectedRole);
      setLeftSelected([]);
    }
  };
  const customList = (title, items) => (
    <div>
      <List
        sx={{
          width: 200,
          height: 230,
          bgcolor: 'background.paper',
          overflow: 'auto'
        }}
        dense
        component="div"
        role="list"
        style={{ paddingTop: 1, paddingBottom: 1 }}
      >
        {items.map((value, index) => {
          const labelId = `transfer-list-all-item-${value}-label`;

          return (
            <ListItem
              key={value}
              role="listitem"
              button
              onClick={event => handleListItemClick(event, index, value, title)}
              selected={selectedIndex === value}
              className={selectedIndex === value ? classes.selected : ''}
              id={'itemList' + title}
            >
              <ListItemText
                id={labelId}
                primary={value}
                // primaryTypographyProps={{ className: classes.listItemText }}
                classes={{ primary: classes.listItemText }}
                style={{ color: '#212121' }}
              />
            </ListItem>
          );
        })}
      </List>
    </div>
  );

  return (
    <Card sx={{ margin: '20px 0 0 0' }} className={classes.card}>
      <Dialog
        open={openAlert}
        classes={{
          paper: classes.dialog
        }}
      >
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Dialog>
      <div className={classes.roleMappingMain}>
        <div className={classes.roleMappingTitle}>
          <span>
            <Typography
              style={{ padding: 8 }}
              variant="h4"
              className={clsx(classes.titleLabel)}
            >
              Role Mappings
            </Typography>
          </span>
        </div>

        <div className={classes.roleMappingContent}>
          <div className={classes.rmdata}>
            <div className="rmdata-title">
              {' '}
              <Typography variant="h6" className={clsx(classes.rolelabel)}>
                Available Roles
              </Typography>
            </div>
            <div className={classes.rmdatadesc}>
              {' '}
              {customList('Choices', left)}
            </div>
            <div className={classes.rmbutton}>
              <Button
                sx={{ my: 0.5 }}
                variant="outlined"
                size="small"
                className={clsx('reset-btn', classes.addremovebtn)}
                onClick={handleCheckedRight}
                disabled={leftSelected.length === 0}
                aria-label="move selected right"
              >
                Add
              </Button>
            </div>
          </div>
          <div className={classes.rmdata}>
            <div className="rmdata-title">
              {' '}
              <Typography variant="h6" className={clsx(classes.rolelabel)}>
                Assigned Roles
              </Typography>
            </div>
            <div className={classes.rmdatadesc}>
              {' '}
              {customList('Chosen', right)}
            </div>
            <div className={classes.rmbutton}>
              <Button
                sx={{ my: 0.5 }}
                variant="outlined"
                size="small"
                className={clsx('reset-btn', classes.addremovebtn)}
                onClick={handleCheckedLeft}
                disabled={rightSelected.length === 0}
                aria-label="move selected right"
              >
                Remove
              </Button>
            </div>
          </div>
          <div className={classes.rmdata} style={{ display: 'none' }}>
            <div className="rmdata-title">
              {' '}
              <Typography variant="h6" className={clsx(classes.rolelabel)}>
                Effective Roles
              </Typography>
            </div>
            <div className={classes.rmdatadesc}>
              {' '}
              {customList('effective', right)}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};
export default RoleMapping;
