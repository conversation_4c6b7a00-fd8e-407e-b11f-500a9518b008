import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Typography,
  Snackbar,
  TextField,
  Tooltip,
  Switch
} from '@material-ui/core';

import { Alert } from '@material-ui/lab';
import DeleteDialog from 'src/views/KPIScoreCardGoalSettings/DeleteDialog';
import StoreDialog from 'src/views/KPIScoreCardGoalSettings/StoreDialog';
import Button from '@material-ui/core/Button';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import IconButton from '@material-ui/core/IconButton';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import CloseIcon from '@material-ui/icons/Close';
import 'react-grid-layout/css/styles.css';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import EditIcon from '@material-ui/icons/Edit';
import { setReloadStatus } from 'src/actions';
import { setUserDetails } from 'src/actions';
import { connect } from 'react-redux';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import {
  getUsersList,
  updateEnableStatus,
  enableMfaStatus
} from 'src/utils/hasuraServices';
import { DELETE_USER_ } from 'src/graphql/queries';
import { render } from 'react-dom';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import { withKeycloak } from '@react-keycloak/web';
import Autocomplete from '@material-ui/lab/Autocomplete';
import InputAdornment from '@material-ui/core/InputAdornment';
import SearchIcon from '@material-ui/icons/Search';
import { ReactSession } from 'react-client-session';
import { withStyles } from '@material-ui/styles';
import 'react-grid-layout/css/styles.css';
import { Link } from 'react-router-dom';

import VerifiedUserIcon from '@material-ui/icons/VerifiedUser';
import BlockIcon from '@material-ui/icons/Block';

import UserCreation from './UserCreation';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
const label = { inputProps: { 'aria-label': 'Size switch demo' } };

class UserListing extends React.Component {
  componentWillMount() {
    // this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }

  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    let startEdit = this;
    this.addNewRow = this.addNewRow.bind(this);
    let editClick = false;
    this.state = {
      openSnackbarMsg: false,
      openSnackbarSuccessMsg: '',
      cancel: false,
      openSnackbar: false,
      showCharts: false,
      selectValue: 'other',
      selectValueFor: this.props.category,
      selectValueType: this.props.reportType,
      rawGridApi: {},
      gridApi: {},
      setAlertType: '',
      isLoading: true,
      editClick: editClick,
      technicianStatus: '',
      success: false,
      setOpenAlert: false,
      setAlertMsg: '',
      openDelete: false,
      openStore: false,
      DataDeleteSet: '',
      isEdited: false,
      oldDataArr: [],
      newDataArr: [],
      prevDataArr: [],
      editedRowId: null,
      usersList: [],
      userEmail: '',

      columnDefs: [
        {
          headerName: ' Id',
          chartDataType: 'series',
          suppressMovable: false,
          width: 150,
          field: 'id',
          hide: true,
          editable: false
        },
        {
          headerName: 'Email',
          chartDataType: 'series',
          minWidth: 350,
          field: 'username',
          suppressMovable: false,
          editable: false,
          suppressMenu: true,
          flex: 1,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', marginLeft: '6px' };
          }
        },
        {
          headerName: 'Email',
          chartDataType: 'series',
          minWidth: 350,
          field: 'email',
          editable: false,
          hide: true,
          suppressMovable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', marginLeft: '6px' };
          }
        },
        {
          headerName: 'Last Name',
          chartDataType: 'series',
          minWidth: 150,
          field: 'lastName',
          suppressMovable: false,
          editable: false,
          suppressMenu: true,
          flex: 1,
          unSortIcon: true,
          sortable: true,
          sortingOrder: ['asc', 'desc'],
          comparator: function(valueA, valueB, nodeA, nodeB, isInverted) {
            // Convert both values to lowercase for case-insensitive comparison
            var lowerCaseA = (valueA && valueA.toLowerCase()) || '';
            var lowerCaseB = (valueB && valueB.toLowerCase()) || '';

            // Perform a standard string comparison on the lowercase values
            if (lowerCaseA === lowerCaseB) {
              return 0;
            } else if (lowerCaseA < lowerCaseB) {
              return -1;
            } else {
              return 1;
            }
          },
          cellStyle() {
            return { border: ' 0px white', marginLeft: '6px' };
          }
        },
        {
          headerName: 'First Name',
          chartDataType: 'series',
          minWidth: 150,
          suppressMovable: false,
          field: 'firstName',
          editable: false,
          suppressMenu: true,
          flex: 1,
          unSortIcon: true,
          sortable: true,
          sortingOrder: ['asc', 'desc'],
          comparator: function(valueA, valueB, nodeA, nodeB, isInverted) {
            // Convert both values to lowercase for case-insensitive comparison
            var lowerCaseA = (valueA && valueA.toLowerCase()) || '';
            var lowerCaseB = (valueB && valueB.toLowerCase()) || '';

            // Perform a standard string comparison on the lowercase values
            if (lowerCaseA === lowerCaseB) {
              return 0;
            } else if (lowerCaseA < lowerCaseB) {
              return -1;
            } else {
              return 1;
            }
          },
          cellStyle() {
            return { border: ' 0px white', marginLeft: '6px' };
          }
        },
        {
          headerName: 'Role',
          chartDataType: 'series',
          minWidth: 150,
          suppressMovable: false,
          field: 'roles',
          valueGetter: params => {
            const roleMapping = {
              user: 'Group/Store Admin',
              admin: 'Armatus Admin',
              superadmin: 'Super Admin',
              client: 'Group/Store Viewer'
            };

            const displayedRoles = params.data.roles
              .map(role => roleMapping[role.name] || role.name)
              .join(', ');
            return displayedRoles;
          },
          editable: false,
          suppressMenu: true,
          flex: 1,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white', marginLeft: '6px' };
          }
        },
        {
          headerName: 'Stores',
          chartDataType: 'series',
          minWidth: 80,
          suppressMovable: false,
          field: 'group',
          editable: false,
          suppressFilter: true,
          suppressMenu: true,
          flex: 1,
          sortable: false,
          cellRendererFramework: params => (
            <div
              style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100%'
              }}
            >
              <Tooltip title="View Stores">
                {/* {/ <IconButton size="medium" classes="infoIcon"> /} */}
                <OpenInNewOutlinedIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={'view' + params.rowIndex}
                  style={{
                    width: 16,
                    left: '8',
                    top: '65%',
                    cursor: 'pointer'
                  }}
                  onClick={() => this.showStoreModal(params.data)}
                />
                {/* {/ </IconButton> /} */}
              </Tooltip>
            </div>
          ),

          cellStyle() {
            return { border: ' 0px white', marginLeft: '6px' };
          }
        },
        {
          headerName: 'Actions',
          field: '',
          minWidth: 200,
          editable: false,
          suppressMenu: true,
          flex: 1,
          filter: 'agSetColumnFilter',
          sortable: false,
          suppressFilter: true,
          hide:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('superadmin') ===
              true
              ? false
              : true,
          cellRendererFramework: params => (
            <span style={{ display: 'flex', justifyContent: 'space-evenly' }}>
              <Tooltip title="Edit">
                <EditIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={'edit' + params.rowIndex}
                  style={{
                    width: 18,
                    left: '8',
                    top: '70%',
                    cursor: 'pointer'
                  }}
                  onClick={() => this.handleEditClick(params.data)}
                ></EditIcon>
              </Tooltip>
              <Tooltip title="Delete">
                <DeleteIcon
                  htmlColor="rgb(0, 61, 107)"
                  style={{
                    width: 18,
                    left: '8',
                    top: '70%',
                    cursor: 'pointer'
                  }}
                  id={'delete' + params.rowIndex}
                  onClick={() => this.showDeleteModal(params.data)}
                />
              </Tooltip>

              <Tooltip title={params.data.enabled ? 'Enabled' : 'Disabled'}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Switch
                    {...label}
                    size="small"
                    checked={params.data.enabled}
                    disabled={
                      params.data.username == localStorage.getItem('userID')
                    }
                    onChange={event =>
                      this.changeStatus(event.target.checked, params)
                    }
                  />
                </div>
              </Tooltip>

              <Tooltip
                title={
                  params.data.is_mfa_user
                    ? params.data.totp
                      ? 'Enabled MFA'
                      : 'Disabled MFA'
                    : 'Setting MFA for ADU users is disabled at Dealership level.'
                }
              >
                {params.data.is_mfa_user ? (
                  params.data.totp ? (
                    <VerifiedUserIcon
                      htmlColor="green"
                      style={{
                        width: 18,
                        cursor: 'pointer'
                      }}
                      id={'mfa-enable-' + params.rowIndex}
                      onClick={() => this.toggleMFA(params, false)}
                    />
                  ) : (
                    <BlockIcon
                      htmlColor="red"
                      style={{
                        width: 18,
                        cursor: 'pointer'
                      }}
                      id={'mfa-disable-' + params.rowIndex}
                      onClick={() => this.toggleMFA(params, true)}
                    />
                  )
                ) : (
                  <BlockIcon
                    htmlColor="grey"
                    style={{
                      width: 18,
                      cursor: 'not-allowed',
                      opacity: 0.5
                    }}
                    id={'mfa-disabled-' + params.rowIndex}
                  />
                )}
              </Tooltip>
            </span>
          ),
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        }
      ],

      rowData: [],
      editType: 'fullRow',
      chartName: null,
      context: { componentParent: this },

      // components: { singleClickEditRenderer: getRenderer() },
      defaultColDef: {
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        // resizable: true,
        editable: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        }
      }
    };
    console.log('alertmsg', this.state.alertType);
  }

  cellStyles = () => {
    return {
      textAlign: 'center',
      border: ' 0px white'
    };
  };
  toggleMFA = (params, status) => {
    let realm = localStorage.getItem('realm');
    let mfaKey =
      status == true
        ? 'statelessKeycloakServiceUpdateUserEnableMfa'
        : 'statelessKeycloakServiceUpdateUserDisableMfa';

    enableMfaStatus(params.data.username, realm, status, result => {
      if (!result || !result.data) {
        console.error('No data in result:', result);
        return;
      }

      if (!result.data[mfaKey] || !result.data[mfaKey].string) {
        console.error('Expected data structure not found:', result.data);
        return;
      }
      if (JSON.parse(result.data[mfaKey].string).status == 'success') {
        this.setState({
          openSnackbarSuccessMsg:
            status == true
              ? 'MFA Enabled Successfully'
              : 'MFA Disabled Successfully',
          openSnackbarMsg: true
        });
      } else {
        this.setState({
          openSnackbarSuccessMsg: 'Something went wrong',
          openSnackbarMsg: true
        });
      }

      let rowVals = [...this.state.rowData];
      const renderedNodes = this.gridApi.getRenderedNodes();
      const rowNode = renderedNodes.find(
        node => node.data.username === params.data.username
      );

      if (rowNode) {
        setTimeout(() => {
          rowVals[rowNode.id].totp = status;
          this.setState({ rowData: rowVals }, () => {
            this.gridApi.redrawRows();
          });
        }, 300);
      }
    });
  };

  changeStatus = (checked, params) => {
    const statusValue = checked == true ? 1 : 0;
    updateEnableStatus(params.data.username, statusValue, result => {
      if (!result || !result.data) {
        console.error('No data in result:', result);
        // setLoader(false);
        return;
      }

      if (
        !result.data.statelessKeycloakServiceEnableOrDisableUser ||
        !result.data.statelessKeycloakServiceEnableOrDisableUser.json
      ) {
        console.error('Expected data structure not found:', result.data);
        //setLoader(false);
        return;
      }
      const res = result.data.statelessKeycloakServiceEnableOrDisableUser.json;

      this.setState({
        openSnackbarSuccessMsg: JSON.parse(
          result.data.statelessKeycloakServiceEnableOrDisableUser.json
        ).message
      });
      this.setState({ openSnackbarMsg: true });
      let rowVals = this.state.rowData;
      let index = params.rowIndex;

      const renderedNodes = this.gridApi.getRenderedNodes();
      const rowNode = renderedNodes.find(
        node => node.data.username === params.data.username
      );
      rowVals[rowNode.id].enabled = checked;
      this.setState({
        rowData: rowVals
      });
      this.gridApi.redrawRows();
    });
  };
  showDeleteModal = data => {
    const userID = localStorage.getItem('userID');
    if (userID != data.username) {
      this.setState({ openDelete: true });
      this.setState({ DataDeleteSet: data.username });
    } else {
      this.setState({ setOpenAlert: true });
      this.setState({ setAlertMsg: 'Cannot delete the logged in user' });
      this.setState({ setAlertType: 'warning' });
    }
  };
  showStoreModal = data => {
    console.log('clicked', data.username);
    const userStore = data.username;
    this.setState({ userEmail: userStore });
    this.setState({ openStore: true });
  };

  confirmDelete = () => {
    setTimeout(() => {
      this.handleDeleteUser(this.state.DataDeleteSet);
    }, 500);
  };
  handleDeleteUser = userName => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: DELETE_USER_,
        variables: {
          username: userName
        }
      })
      .then(result => {
        this.setState({ openDelete: false });
        this.setState({ setOpenAlert: true });
        this.setState({ setAlertMsg: 'The User has been deleted' });
        this.setState({ setAlertType: 'success' });

        setTimeout(() => {
          this.setState({ setOpenAlert: false });

          // setAlertMsg('');
          this.getAgGridData();
        }, 2000);
      });
  };
  handleEditClick = params => {
    const { dispatch } = this.props;
    // console.log('params,', params.username);
    let id = params.username;
    const userData = {
      username: params.username,
      email: params.email,
      firstName: params.firstName,
      lastName: params.lastName
    };

    this.props.handleShowList();
    dispatch(setUserDetails(id));
    this.props.history.push({
      pathname: '/KeycloakUserCreation',
      editType: 'edit',
      userdetails: userData,
      usersList: this.state.usersList
    });
    window.sortStateUser = this.gridApi.getSortModel();
    window.filterStateUser = this.gridApi.getFilterModel();
  };

  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    this.gridApi.redrawRows();
  };
  onSortChanged = e => {
    this.gridApi.redrawRows();
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.setState({ gridColumnApi: params.columnApi });
    if (this.props.history.location.state == undefined) {
      window.sortStateUser = {};
      window.filterStateUser = {};
    }
    this.gridApi.setSortModel(window.sortStateUser);
    this.gridApi.setFilterModel(window.filterStateUser);
    if (
      this.props.keycloak &&
      this.props.keycloak.authenticated &&
      this.props.keycloak.token
    ) {
      this.getAgGridData();
    }
  };
  getRowStyle = params => {
    if (params.data.categorized == 0) {
      return { background: 'rgb(221, 234, 244)' };
    }
  };
  getAgGridData() {
    this.setState({ isLoading: true });
    var users = [];
    let realm = localStorage.getItem('realm');
    getUsersList(realm, result => {
      this.setState({ isLoading: false });
      if (result.data.statelessKeycloakServiceGetUsers) {
        var roData = JSON.parse(result.data.statelessKeycloakServiceGetUsers);

        const updatedArray = roData.map(obj => {
          if (
            obj.totp === true ||
            obj.requiredActions.includes('CONFIGURE_TOTP')
          ) {
            return { ...obj, totp: true };
          }
          return obj;
        });

        this.setState({
          rowData: updatedArray
        });
        updatedArray.map(e => {
          users.push(e.username);
        });
        this.setState({ usersList: users });
        this.state.gridColumnApi.resetColumnState();
        if (window.filterStateUser != undefined) {
          this.filterByValue();
        }
      }
    });
  }
  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStateUser);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  addNewRow() {
    this.state.rawGridApi.updateRowData({
      addIndex: 0,
      add: [{ id: '', name: '', serviceadvisor: '' }]
    });

    this.state.rawGridApi.setFocusedCell(0, 'name', '');
  }
  handleOk = () => {
    this.setState({ success: false });
  };
  resetReportGrid = () => {
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
  };

  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
  };

  hidesnackbarMsg = () => {
    this.setState({ openSnackbarMsg: false });
  };

  handleCloseDelete = () => {
    this.setState({ openDelete: false });
  };
  handleCloseStore = () => {
    this.setState({ openStore: false });
  };
  handleSelected = selected => {
    console.log('selected', selected);
    this.setState({ selectedDelete: selected });
  };
  handleStoreSelected = data => {
    const id = data.username;
    console.log('userID', id);
    // this.setState({ selectedStore: selected });
  };
  handleBtnClick = () => {
    this.props.handleShowList();
    sessionStorage.clear();
    this.props.history.push({
      pathname: '/KeycloakUserCreation',
      editType: 'new',
      usersList: this.state.usersList
    });
  };
  getContextMenuItems = params => {
    return []; // Return an empty array to hide the context menu
  };
  onCellContextMenu = event => {
    event.preventDefault();
    console.log('Right-click disabled');
  };
  render() {
    const { classes } = this.props;
    return (
      <>
        <div>
          <Paper
            square
            style={{
              margin: 8,

              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
            }}
          >
            <Dialog
              open={this.state.setOpenAlert}
              classes={{
                paper: classes.dialog
              }}
            >
              <Alert
                severity={
                  this.state.setAlertType == 'warning' ? 'warning' : 'success'
                }
                action={
                  <IconButton
                    aria-label="close"
                    color="inherit"
                    size="small"
                    onClick={() => {
                      this.setState({ setOpenAlert: false });
                    }}
                  >
                    <CloseIcon fontSize="inherit" />
                  </IconButton>
                }
                style={{ margin: '10px 20px' }}
              >
                {this.state.setAlertMsg}
              </Alert>
            </Dialog>
            <Tabs
              value={this.state.tabSelection}
              onChange={this.handleTabChange}
              variant="fullWidth"
              indicatorColor="secondary"
              textColor="secondary"
              aria-label="icon label tabs example"
            >
              <Tab
                style={{
                  pointerEvents: 'none',
                  textTransform: 'none'
                  // backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                  // border:
                  //   Dealer === 'Armatus'
                  //     ? '1px solid #003d6b'
                  //     : '1px solid #C2185B',
                  // color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
                label={<div>User Lookup</div>}
                value="one"
              />
              <Button
                variant="contained"
                id="reset-layout"
                style={{ margin: 8, marginTop: 11 }}
                className={clsx(
                  classes.resetReportGridback,
                  classes.reset,
                  'reset-btn'
                )}
                onClick={this.resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </Tabs>
          </Paper>{' '}
          {(this.props.keycloak.realmAccess.roles.includes('superadmin') ||
            this.props.keycloak.realmAccess.roles.includes('admin')) && (
            <Paper
              square
              style={{
                marginLeft: 8,
                marginRight: 8,
                paddingTop: 9,
                display: 'flex',
                height: '54px',
                justifyContent: 'space-between'
              }}
            >
              <div className={classes.btnContainer}>
                <label></label>

                {/* <Tooltip
                title="Add New User"
                onClick={this.handleBtnClick}
                placement="top"
              > */}
                {/* <Button
                variant="contained"
                className={clsx('reset-btn', classes.btnUser)}
                onClick={this.handleBtnClick}
                disabled={
                  typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
                  this.props.keycloak.realmAccess.roles.length >= 1 &&
                  this.props.keycloak.realmAccess.roles.includes(
                    'superadmin'
                  ) === true
                    ? false
                    : true
                }
              >
                Add New User
              </Button> */}

                <Button
                  variant="contained"
                  className={clsx('reset-btn', classes.btnUser)}
                  onClick={this.handleBtnClick}
                >
                  Add New User
                </Button>
              </div>
            </Paper>
          )}
          {/* </Tooltip> */}
          {/* </div>
          </Paper> */}
          {this.state.isLoading == true ? (
            <div>
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            </div>
          ) : null}
          {/* <div
            style={{ display: this.state.isLoading == true ? 'none' : 'block' }}
          > */}
          <div
            id="data-tab-user"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 230 + 'px',
              // width: '852px',
              width: '1100px',
              alignContent: 'center',
              marginLeft: '8px',
              margin: 8,
              display: this.state.isLoading ? 'none' : 'block'
            }}
            onCellContextMenu={this.onCellContextMenu}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              frameworkComponents={this.state.frameworkComponents}
              enableRangeSelection={true}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              animateRows={true}
              enableCharts={true}
              modules={AllModules}
              getRowStyle={this.getRowStyle}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader={true}
              // onCellValueChanged={this.onCellValueChanged.bind(this)}
              onFilterChanged={this.onFilterChanged}
              onSortChanged={this.onSortChanged}
              rowData={this.state.rowData}
              context={this.state.context}
              components={this.state.components}
              singleClickEdit={true}
              // onCellClicked={this.onCellClicked}
              floatingFilter={true}
              suppressRowClickSelection={true}
              suppressHorizontalScroll={true}
              editType={this.state.editType}
              suppressDragLeaveHidesColumns={true}
              getContextMenuItems={this.getContextMenuItems}
              suppressContextMenu={true}
            />
          </div>
          {/* </div> */}
        </div>
        <Snackbar
          open={this.state.openSnackbar}
          autoHideDuration={6000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbar} severity="success">
            User details updated!
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.openSnackbarMsg}
          autoHideDuration={1500}
          onClose={this.hidesnackbarMsg}
          style={{ marginBottom: 487 }}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'center'
          }}
        >
          <Alert onClose={this.hidesnackbarMsg} severity="success">
            {this.state.openSnackbarSuccessMsg}
          </Alert>
        </Snackbar>
        <DeleteDialog
          openPopup={this.state.openDelete}
          handlePopupClose={this.handleCloseDelete}
          handleDeleteEmailRow={this.confirmDelete}
          type={'User'}
          handleSelected={this.handleSelected.bind(this)}
        ></DeleteDialog>
        <StoreDialog
          openPopup={this.state.openStore}
          userEmail={this.state.userEmail}
          handlePopupClose={this.handleCloseStore}
          handlestoreSelect={this.handleStoreSelected}
          // handleSelected={this.handleStoreSelected.bind(this)}
        ></StoreDialog>
      </>
    );
  }
}

const styles = theme => ({
  formControl: {
    minWidth: 170,
    marginTop: -19
  },

  btnContainer: {
    display: 'flex',
    width: '845px',
    justifyContent: 'space-between',
    width: '100%',
    marginRight: 25
    // marginTop: '4px',
    // '@media (min-width: 2560px)': {
    //   width: '1701px'
    // },
    // '@media (max-width: 2304px)': {
    //   width:'753px'
    // },
    // '@media (max-width: 1920px)': {
    //   width: '753px'
    // },
    // '@media (max-width: 1440px)': {
    //   width: '753px'
    // }
  },
  btnUser: {
    height: '27px',
    fontSize: '12px !important',
    // border: '1px solid black !important',
    marginTop: '6px',
    width: 110,
    paddingRight: -29,
    paddingLeft: -18,
    left: 15
  },
  dialog: {
    position: 'absolute',
    top: 50
  },
  reset: {
    width: '110px',
    margin: '8px !important'
  }
});

export default connect()(withStyles(styles)(withKeycloak(UserListing)));
