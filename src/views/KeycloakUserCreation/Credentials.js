import React, { useState } from 'react';
import {
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Paper,
  Typography,
  Divider,
  OutlinedInput,
  FormHelperText,
  Dialog,
  Tooltip
} from '@material-ui/core';
import clsx from 'clsx';
import { withKeycloak } from '@react-keycloak/web';
import Alert from '@material-ui/lab/Alert';
import { makeStyles } from '@material-ui/styles';
import Switch from '@material-ui/core/Switch';
import InputAdornment from '@material-ui/core/InputAdornment';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import Visibility from '@material-ui/icons/Visibility';
import VisibilityOff from '@material-ui/icons/VisibilityOff';
import { UPDATE_USER_PASSWORD } from 'src/graphql/queries';
import { theme } from 'highcharts';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { useEffect } from 'react';
import { getUserCredentialsStatus } from 'src/utils/hasuraServices';
const label = { inputProps: { 'aria-label': 'Switch demo' } };

const useStyles = makeStyles(theme => ({
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  mainLabel1: {
    fontSize: 13,
    color: '#FF0000'
  },
  card: {
    width: '98%',
    marginLeft: '20px',
    marginTop: '28px',
    height: '400px'

    // border: 'solid 1px',
    // borderColor: '#084573'
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    marginTop: '16px'
  },
  toogleswitch: {
    marginTop: '8px !important;',
    marginLeft: -20
  },
  txt: {
    width: '400px',
    marginLeft: -11
  },
  btnDiv: {
    width: '120px',
    marginLeft: -9
  },
  labelPos: {
    textAlign: 'right'
  },
  contentcred: {
    width: '80%',
    margin: 'auto',
    marginLeft: -75
  },
  dialog: {
    position: 'absolute',
    top: 50
  },
  star: {
    color: 'red',
    padding: '5px'
  }
}));

const PasswordForm = props => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setshowConfirmPassword] = useState(false);
  const [switchChecked, setSwitchChecked] = useState(true);
  const [requiredText, setRequiredText] = useState(false);
  const [openAlert, setOpenAlert] = useState(false);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = React.useState('');
  const [userName, setUserName] = useState('');
  const [pwStatus, setpwStatus] = useState('');
  const classes = useStyles();

  const handlePasswordChange = event => {
    setPassword(event.target.value);
    setPasswordError(false);
  };
  useEffect(() => {
    var username = props && props.session && props.session.userId;
    setUserName(username);
    setPassword('');
    setConfirmPassword('');
    if (username) {
      getPasswordStatus(username);
    }
  }, []);
  const handleConfirmPasswordChange = event => {
    setConfirmPassword(event.target.value);
    setPasswordError(false);
  };
  const savePassword = () => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_USER_PASSWORD,
        variables: {
          username: userName,
          password: password,
          isTempPwdStatus: switchChecked
        }
      })
      .then(result => {
        setOpenAlert(true);
        setAlertMsg('User Credentials Saved Successfully!');
        setAlertType('success');
        const PasswdData = {
          password: password,
          confirmPassword: confirmPassword
        };
        sessionStorage.setItem('PasswdData', JSON.stringify(PasswdData));
        setTimeout(() => {
          setOpenAlert(false);
          // setAlertMsg('');
        }, 2000);
      });
  };
  const passwordetails = JSON.parse(sessionStorage.getItem('passwordetails'));
  const PasswdData = JSON.parse(sessionStorage.getItem('PasswdData'));
  const handleSubmit = event => {
    setPassword(password.trim());
    setConfirmPassword(confirmPassword.trim());
    const passwordetails = {
      password: password.trim(),
      confirmPassword: confirmPassword.trim()
    };
    sessionStorage.setItem('passwordetails', JSON.stringify(passwordetails));

    const userID = localStorage.getItem('userID');
    console.log('userID', userID);
    if (userName) {
      var errorStatus = false;
      if (password.trim() == undefined || password.trim() == '') {
        setRequiredText(true);
      }
      if (password.trim() !== confirmPassword.trim()) {
        setPasswordError(true);
        errorStatus = true;
      }
      if (confirmPassword.trim() == undefined || confirmPassword.trim() == '') {
        setRequiredText(true);
        errorStatus = true;
      }

      if (errorStatus == false) {
        if (
          passwordetails != null &&
          passwordetails != undefined &&
          JSON.stringify(passwordetails) == JSON.stringify(PasswdData)
        ) {
          errorStatus = true;
          setOpenAlert(true);
          setAlertMsg('Please Make Any Change');
          setAlertType('warning');
          setTimeout(() => {
            setOpenAlert(false);
          }, 2000);
        } else {
          savePassword();
          if (userID == userName) {
            props.keycloak.logout();
          }
        }
      }
    } else {
      setOpenAlert(true);
      setAlertMsg('Please add user details');
      setAlertType('warning');
      setTimeout(() => {
        setOpenAlert(false);
        // setAlertMsg('');
      }, 2000);
    }
  };
  const handleClickShowPassword = () => setShowPassword(show => !show);
  const handleClickConfirmShowPassword = () =>
    setshowConfirmPassword(show => !show);
  const handleMouseDownPassword = event => {
    event.preventDefault();
  };
  const getPasswordStatus = username => {
    getUserCredentialsStatus(username, result => {
      if (result.data.statelessKeycloakServiceGetUsersCredentials) {
        var roData = JSON.parse(
          result.data.statelessKeycloakServiceGetUsersCredentials
        );
        console.log('roData.length', roData.length);
        setpwStatus(roData.length);
      }
    });
  };
  return (
    <Card sx={{ margin: '20px 0 0 0' }} className={classes.card}>
      <Dialog
        open={openAlert}
        classes={{
          paper: classes.dialog
        }}
      >
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Dialog>
      <span style={{ display: 'flex', marginTop: '15px' }}>
        <Typography
          style={{ padding: 8 }}
          variant="h4"
          className={clsx(classes.mainLabel)}
        >
          Manage Credentials
        </Typography>
      </span>
      {/* <span style={{ display: 'flex', marginTop: '15px' }}>
        <Typography
          style={{ padding: 8 }}
          variant="h6"
          className={clsx(classes.mainLabel1)}
        >{pwStatus?
      'The password has already been set':'' } </Typography>
      </span> */}
      <CardContent className={classes.contentcred}>
        {console.log('history', props.session.userId)}
        <Grid container spacing={2}>
          <Grid item xs={12} sm={4} className={classes.labelPos}>
            <Typography variant="body1" className={classes.label}>
              Password
            </Typography>
            <span className={classes.star}>*</span>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl variant="outlined">
              <OutlinedInput
                value={password}
                required
                variant="outlined"
                className={classes.txt}
                onChange={handlePasswordChange}
                type={showPassword ? 'text' : 'password'}
                error={password == '' && requiredText}
                helperText={
                  password == '' && requiredText && 'This is required!'
                }
                autoComplete="no"
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowPassword}
                      onMouseDown={handleMouseDownPassword}
                      edge="end"
                    >
                      {showPassword ? (
                        <Tooltip title="Hide Password" arrow>
                          <VisibilityOff> </VisibilityOff>
                        </Tooltip>
                      ) : (
                        <Tooltip title="Show Password" arrow>
                          <Visibility> </Visibility>
                        </Tooltip>
                      )}
                    </IconButton>
                  </InputAdornment>
                }
              />
              {requiredText && password == '' && (
                <FormHelperText style={{ color: 'rgb(255,91,71)' }}>
                  This is required
                </FormHelperText>
              )}
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4} className={classes.labelPos}>
            <Typography variant="body1" className={classes.label}>
              Confirm Password
            </Typography>
            <span className={classes.star}>*</span>
          </Grid>
          <Grid item xs={12} sm={8}>
            <FormControl variant="outlined">
              <OutlinedInput
                className={classes.txt}
                required
                value={confirmPassword}
                variant="outlined"
                onChange={handleConfirmPasswordChange}
                error={passwordError || (confirmPassword == '' && requiredText)}
                type={showConfirmPassword ? 'text' : 'password'}
                autoComplete="no"
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickConfirmShowPassword}
                      onMouseDown={handleMouseDownPassword}
                      edge="end"
                    >
                      {showConfirmPassword ? (
                        <Tooltip title="Hide Password" arrow>
                          <VisibilityOff> </VisibilityOff>
                        </Tooltip>
                      ) : (
                        <Tooltip title="Show Password" arrow>
                          <Visibility> </Visibility>
                        </Tooltip>
                      )}
                    </IconButton>
                  </InputAdornment>
                }
              />
              {passwordError && confirmPassword != '' && (
                <FormHelperText style={{ color: 'rgb(255,91,71)' }}>
                  Passwords do not match
                </FormHelperText>
              )}
              {requiredText && confirmPassword == '' && (
                <FormHelperText style={{ color: 'rgb(255,91,71)' }}>
                  This is required
                </FormHelperText>
              )}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={4} className={classes.labelPos}>
            <Typography
              variant="body1"
              style={{ marginRight: 14 }}
              className={classes.label}
            >
              Temporary
            </Typography>
            <span className={classes.star1}></span>
          </Grid>
          <Grid item xs={12} sm={8}>
            <Switch
              {...label}
              checked={switchChecked}
              onChange={() => {
                setSwitchChecked(!switchChecked);
              }}
              className={clsx(classes.label, classes.toogleswitch)}
            />
          </Grid>
          <Grid item xs={12} sm={4}></Grid>
          <Grid item xs={12} sm={8}>
            <Button
              variant="contained"
              className={clsx('reset-btn', classes.btnDiv)}
              onClick={handleSubmit}
              color="primary"
            >
              {/* {pwStatus ? 'Reset Password' : 'Set Password'} */}
              {props.history.location &&
              props.history.location.editType == 'edit'
                ? 'Reset Password'
                : 'Set Password'}
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default withKeycloak(PasswordForm);
