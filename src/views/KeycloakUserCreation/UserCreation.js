import React, { useEffect, useState } from 'react';
import {
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Paper,
  Typography,
  Divider,
  Dialog
} from '@material-ui/core';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { INSERT_USER, UPDATE_USER_DETAILS } from 'src/graphql/queries';
import { theme } from 'highcharts';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import IconButton from '@material-ui/core/IconButton';
import Alert from '@material-ui/lab/Alert';
import CloseIcon from '@material-ui/icons/Close';
import { setUserDetails } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import { flat } from 'ag-charts-community';
const label = { inputProps: { 'aria-label': 'Switch demo' } };

const useStyles = makeStyles(theme => ({
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },

  card: {
    width: '98%',
    marginLeft: '20px',
    marginTop: '28px',
    height: '450px'

    // border: 'solid 1px',
    // borderColor: '#084573'
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    marginTop: '16px'
  },
  txt: {
    width: '400px',
    marginLeft: -11
  },
  btnDiv: {
    width: '100px',
    marginLeft: -9
  },
  labelPos: {
    textAlign: 'right'
  },
  star: {
    color: 'red',
    padding: '5px'
  },
  contentcred: {
    width: '90%',
    margin: 'auto'
  },
  dialog: {
    position: 'absolute',
    top: 50
  },
  error: {
    margin: 0,
    fontSize: '90%',
    color: 'tomato',
    marginTop: '20px',
    marginLeft: '15px'
  }
}));
const UserCreation = (history, props) => {
  const [edittype, setName] = useState('');
  const [isButtonDisabled, setIsButtonDisabled] = useState(
    history.history && history.history.editType == 'edit' ? true : false
  );
  const [email, setEmail] = useState('');
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [userName, setUserName] = useState('');
  const [userEnabled, setuserEnabled] = useState(true);
  const [openAlert, setOpenAlert] = useState(false);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = React.useState('');
  const [requiredText, setRequiredText] = useState(false);
  const [error, setError] = useState('');
  const classes = useStyles();
  const usehistory = useHistory();
  const dispatch = useDispatch();
  const handleSubmit = () => {
    var errorStatus = false;
    var updatedStatus = false;
    const userDataUpdated = {
      username: userName,
      email: email,
      firstName: firstName,
      lastName: lastName
    };
    if (
      history.history &&
      history.history.userdetails &&
      history.history.userdetails.username
    ) {
      if (
        JSON.stringify(userDataUpdated) ==
        JSON.stringify(history.history.userdetails)
      ) {
        errorStatus = true;
        setOpenAlert(true);
        setAlertMsg('Please Make Any Change');
        setAlertType('warning');
        setTimeout(() => {
          setOpenAlert(false);
        }, 2000);
      }
    }
    if (userName == undefined || (userName === '' && error == '')) {
      setRequiredText(true);
      errorStatus = true;
    }

    if (
      firstName == undefined ||
      firstName == 'undefined' ||
      (firstName === '' && error == '')
    ) {
      setRequiredText(true);
      errorStatus = true;
    }
    console.log('123', typeof firstName, error);
    var isValid = userName && validateEmail(userName);
    if (!isValid) {
      errorStatus = true;
    }

    if (
      (history.history &&
        history.history.editType == 'new' &&
        history.history.usersList &&
        userName &&
        history.history.usersList.includes(userName)) ||
      (history.history &&
        history.history.editType == 'edit' &&
        history.history.usersList &&
        userName &&
        history.history.usersList.includes(userName) &&
        history.history &&
        history.history.userdetails &&
        userName != history.history.userdetails.username)
    ) {
      errorStatus = true;
      setOpenAlert(true);
      setAlertMsg('User Exists with the Same Username');
      setAlertType('warning');
      setTimeout(() => {
        setOpenAlert(false);
        setUserName('');
      }, 2000);
    }
    if (errorStatus == false) {
      const sessionValue = JSON.parse(sessionStorage.getItem('userdetails'));
      console.log(
        'user',
        userName,
        sessionValue != null &&
          sessionValue != undefined &&
          sessionValue.username
      );
      if (
        (history.history &&
          history.history.userdetails &&
          history.history.userdetails.username) ||
        (sessionValue != null &&
          sessionValue != undefined &&
          // userName == sessionValue.username &&
          JSON.stringify(sessionValue) !== JSON.stringify(userDataUpdated))
      ) {
        updateUser();
      } else {
        if (
          sessionValue == null &&
          sessionValue == undefined
          // (sessionValue != null &&
          //   sessionValue != undefined &&
          //   userName != sessionValue.username)
        ) {
          saveUser();
        }
      }
      dispatch(setUserDetails(userName));
      usehistory.push({
        pathname: '/KeycloakUserCreation'
      });
      //  handleTabAfterSave();
      // clearTxt();
    }
  };
  useEffect(() => {
    handleSetUserDetails();
    const sessionValue = JSON.parse(sessionStorage.getItem('userdetails'));

    if (sessionValue !== null && sessionValue !== undefined) {
      setUserName(sessionValue.username);
      setFirstName(sessionValue.firstName);
      setLastName(sessionValue.lastName);
      setEmail(sessionValue.email);
    }
  }, []);
  const handleSetUserDetails = () => {
    if (history && history.history && history.history.userdetails) {
      const userDetail = history.history.userdetails;
      dispatch(setUserDetails(userDetail.username));
      setUserName(userDetail.username);
      setFirstName(userDetail.firstName);
      setLastName(userDetail.lastName);
      setEmail(userDetail.email);
    }
  };
  const validateEmail = e => {
    let error = null;
    var isEmail = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/.test(
      userName
    );

    if (!isEmail) {
      if (userName) {
        error = 'Email Address is not valid';
      } else {
        setError('');
      }
    }
    if (error) {
      setError(error);
      setUserName('');
      setRequiredText('');
      setAlertType('warning');
      return false;
    } else {
      setError('');
    }

    return true;
  };
  const saveUser = () => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: INSERT_USER,
        variables: {
          email: userName,
          firstname: firstName,
          lastname: lastName,
          username: userName
        }
      })
      .then(result => {
        setOpenAlert(true);
        setAlertMsg('User Saved Successfully!');
        setAlertType('success');
        const userData = {
          username: userName,
          email: userName,
          firstName: firstName,
          lastName: lastName
        };
        sessionStorage.setItem('userdetails', JSON.stringify(userData));
        setTimeout(() => {
          setOpenAlert(false);
          // setAlertMsg('');
        }, 1000);
      });
  };
  const updateUser = () => {
    const sessionValue = JSON.parse(sessionStorage.getItem('userdetails'));

    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_USER_DETAILS,
        variables: {
          username:
            history.history && history.history.userdetails
              ? history.history.userdetails.username
              : sessionValue != null && sessionValue != undefined
              ? sessionValue.username
              : userName,
          newUsername: userName,
          lastname: lastName,
          firstname: firstName,
          email: userName ? userName : ''
        }
      })
      .then(result => {
        setOpenAlert(true);
        setAlertMsg('User Details Updated Successfully!');
        setAlertType('success');
        const userData = {
          username: userName,
          email: userName,
          firstName: firstName,
          lastName: lastName
        };
        sessionStorage.setItem('userdetails', JSON.stringify(userData));
        console.log('userdetails', history.history.editType);
        setTimeout(() => {
          setOpenAlert(false);
          //  setAlertMsg('');
        }, 1000);
      });
  };
  const clearTxt = () => {
    setUserName('');
    setLastName('');
    setFirstName('');
    setEmail('');
    setRequiredText(false);
  };
  const onChangeEmail = e => {
    setUserName(e.target.value);
    if (e.target.value) setRequiredText(false);
    setError('');
  };

  return (
    // <Paper
    //   square
    //   style={{
    //     margin: 8,
    //     height: '800px'
    //   }}
    // >
    //   <Divider></Divider>

    <Card sx={{ margin: '20px 0 0 0' }} className={classes.card}>
      <Dialog
        open={openAlert}
        classes={{
          paper: classes.dialog
        }}
      >
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Dialog>
      <span style={{ display: 'flex', marginTop: '15px' }}>
        <Typography
          style={{ padding: 8 }}
          variant="h4"
          className={clsx(classes.mainLabel)}
        >
          {history.history && history.history.editType == 'new'
            ? 'Add User'
            : 'Edit User'}
        </Typography>
      </span>
      <CardContent className={classes.contentcred}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={2} className={classes.labelPos}>
            <Typography variant="body1" className={classes.label}>
              Email
            </Typography>
            <span className={classes.star}>*</span>
          </Grid>
          <Grid item xs={12} sm={10}>
            <TextField
              required
              value={userName}
              variant="outlined"
              className={classes.txt}
              error={requiredText && userName == ''}
              onBlur={validateEmail}
              onChange={onChangeEmail}
              helperText={requiredText && userName == '' && 'This is required!'}
              disabled={
                history.history && history.history.editType == 'new'
                  ? false
                  : true
              }
            />
            {error && <p className={classes.error}>{error}</p>}
          </Grid>
          {/* <Grid item xs={12} sm={2} className={classes.labelPos}>
            <Typography variant="body1" className={classes.label}>
              Email
            </Typography>
          </Grid>
          <Grid item xs={12} sm={10}>
            <TextField
              className={classes.txt}
              value={email}
              variant="outlined"
              onChange={event => setEmail(event.target.value)}
              onBlur={validateEmail}
              helperText={error && 'Email Address is not Valid'}
            />
          </Grid> */}
          <Grid item xs={12} sm={2} className={classes.labelPos}>
            <Typography variant="body1" className={classes.label}>
              First Name
            </Typography>
            <span className={classes.star}>*</span>
          </Grid>
          <Grid item xs={12} sm={10}>
            {/* <TextField
              value={firstName}
              required
              className={classes.txt}
              variant="outlined"
              onChange={event =>
                setFirstName(event.target.value.trim().replace(/[^a-z]/gi, ''))
              }
              helperText={
                requiredText && firstName == '' && 'This is required!'
              }
              error={requiredText && firstName == ''}
            /> */}
            <TextField
              value={firstName}
              required
              className={classes.txt}
              variant="outlined"
              onChange={event => {
                const input = event.target.value;
                const filtered = input.replace(/[^a-zA-Z\s]/g, ''); // only letters and space
                const trimmed = filtered.substring(0, 80); // limit to 80 characters
                setFirstName(trimmed);
              }}
              helperText={
                requiredText && firstName === '' && 'This is required!'
              }
              error={requiredText && firstName === ''}
            />
          </Grid>
          <Grid item xs={12} sm={2} className={classes.labelPos}>
            <Typography
              variant="body1"
              style={{ marginRight: 14 }}
              className={classes.label}
            >
              Last Name
            </Typography>
          </Grid>
          <Grid item xs={12} sm={10}>
            {/* <TextField
              variant="outlined"
              className={classes.txt}
              value={lastName}
              onChange={event =>
                setLastName(event.target.value.trim().replace(/[^a-z]/gi, ''))
              }
            /> */}
            <TextField
              variant="outlined"
              className={classes.txt}
              value={lastName}
              onChange={event => {
                const input = event.target.value;
                const filtered = input.replace(/[^a-zA-Z\s]/g, ''); // allow letters & space
                const trimmed = filtered.substring(0, 80); // max 80 characters
                setLastName(trimmed);
              }}
            />
          </Grid>

          {/* <Grid item xs={12} sm={2} className={classes.labelPos}>
            <Typography variant="body1" className={classes.label}>
              First Name
            </Typography>
            <span className={classes.star}>*</span>
          </Grid>
          <Grid item xs={12} sm={10}>
            <TextField
              value={firstName}
              required
              className={classes.txt}
              variant="outlined"
              onChange={event =>
                setFirstName(event.target.value.trim().replace(/[^a-z]/gi, ''))
              }
              helperText={
                requiredText && firstName == '' && 'This is required!'
              }
              error={requiredText && firstName == ''}
            />
          </Grid>
          <Grid item xs={12} sm={2} className={classes.labelPos}>
            <Typography
              variant="body1"
              style={{ marginRight: 14 }}
              className={classes.label}
            >
              Last Name
            </Typography>
          </Grid>
          <Grid item xs={12} sm={10}>
            <TextField
              variant="outlined"
              className={classes.txt}
              value={lastName}
              onChange={event =>
                setLastName(event.target.value.trim().replace(/[^a-z]/gi, ''))
              }
            />
          </Grid> */}
          <Grid item xs={12} sm={2}></Grid>
          <Grid item xs={12} sm={10}>
            <Button
              variant="contained"
              className={clsx('reset-btn', classes.btnDiv)}
              onClick={handleSubmit}
              color="primary"
            >
              Save
            </Button>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
    // </Paper>
  );
};

export default UserCreation;
