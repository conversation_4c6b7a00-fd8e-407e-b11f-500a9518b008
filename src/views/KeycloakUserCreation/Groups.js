import * as React from 'react';
import TreeView from '@material-ui/lab/TreeView';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import TreeItem from '@material-ui/lab/TreeItem';
import {
  TextField,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Card,
  CardContent,
  CardHeader,
  Paper,
  Typography,
  Divider,
  IconButton,
  Dialog
} from '@material-ui/core';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { element } from 'prop-types';
import { getGroups, getUsersAssignedGrps } from 'src/utils/hasuraServices';
import { ASSIGN_USER_GRP, REMOVE_USER_GRP } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { useEffect } from 'react';
import CloseIcon from '@material-ui/icons/Close';
import Alert from '@material-ui/lab/Alert';

import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
const useStyles = makeStyles(themes => ({
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  Paper: {
    width: '98%',
    marginLeft: '20px',
    marginTop: '28px',
    paddingBottom: '25px'
  },
  card: {
    width: '480px',
    marginLeft: '20px',
    marginTop: '28px',
    height: 350
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    marginTop: '16px'
  },
  txt: {
    width: '400px'
  },
  btnDiv: {
    width: 55,
    textAlign: 'right',
    height: '27px',
    fontSize: '13px',
    background: 'transparent',
    boxShadow: 'none'
  },
  labelPos: {
    textAlign: 'right'
  },
  // selected: {
  //   backgroundColor: 'lightblue'
  // },
  selectedBg: {
    background: 'transparent'
  },
  selected: {
    backgroundColor: 'lightblue',
    '&:focus': {
      backgroundColor: 'lightblue'
    }
  },
  listItemText: {
    color: 'rgb(0, 61, 107)',
    fontSize: 13,
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    padding: '7px 10px !important',
    marginBottom: -8
  },
  treeItem: {},
  containerGrp: {
    display: 'flex',
    width: '70%',
    margin: 'auto'
  },
  cardcontent: {
    padding: 0,
    height: '267px',
    overflow: 'auto'
  },
  treeviewul: {
    marginTop: '10px'
  },
  typtitle: {
    fontSize: '13px',
    fontWeight: 'bold',
    color: 'rgb(0, 61, 107)'
  },
  dialog: {
    position: 'absolute',
    top: 50
  }
}));
const Groups = props => {
  const [selectedNode, setSelectedNode] = React.useState([]);
  const [groups, setGroups] = React.useState([]);
  const [groupsAvailable, setGroupsAvailable] = React.useState([]);
  const [groupsAvailableStores, setGroupsAvailableStores] = React.useState([]);
  const [assignedStores, setAssignedStores] = React.useState([]);

  const [groupsAssigned, setGroupsAssigned] = React.useState([]);
  const [selectedIndex, setSelectedIndex] = React.useState(null);
  const [addedGrp, setAddedGrp] = React.useState([]);
  const [openAlert, setOpenAlert] = React.useState(false);
  const [alertMsg, setAlertMsg] = React.useState('');
  const [alertType, setAlertType] = React.useState('');
  const [userName, setUserName] = React.useState('');
  const [openDialog, setOpenDialog] = React.useState(false);
  const classes = useStyles();
  const handleNodeSelect = (event, node) => {
    if (node.length > 0) {
      setSelectedNode(node);
      setAddedGrp([]);
    } else {
      setSelectedNode(null);
    }
  };
  useEffect(() => {
    fetchData();
  }, []);

  // Function to fetch data
  const fetchData = async () => {
    getGroupNames();
    var username = props && props.session && props.session.userId;
    setUserName(username);
    if (username) {
      getGroupUserAssigned(username);
    }
  };

  const getGroupNames = () => {
    let realm = localStorage.getItem('realm');
    var grpNames = [];
    getGroups(realm, result => {
      if (result.data.statelessKeycloakServiceGetGroups) {
        var roData = JSON.parse(result.data.statelessKeycloakServiceGetGroups);

        roData.map(item => {
          grpNames.push({
            name: item.name,
            store: item.subGroups[0].name
          });
        });
        setGroupsAvailableStores(grpNames.store);
        // console.log('rodataGrp1',grpNames);
        setGroupsAvailable(grpNames);
      }
    });
  };
  const getGroupUserAssigned = username => {
    var grpNames = [];
    var grpwithId = [];
    var grpStore = [];
    getUsersAssignedGrps(username, result => {
      if (
        result.data.statelessKeycloakServiceGetAssignedGroupsOfUsers &&
        result.data.statelessKeycloakServiceGetAssignedGroupsOfUsers != '[null]'
      ) {
        var roData = JSON.parse(
          result.data.statelessKeycloakServiceGetAssignedGroupsOfUsers
        );
        const parsed = JSON.parse(roData);
        console.log('ppp', parsed);
        parsed.map(item => {
          grpNames.push(item.path);
          grpStore.push(item.name);
        });
        console.log('usergrp', grpNames);
        for (let index = 0; index < grpNames.length; index++) {
          let splitValue = grpNames[index].split('/');
          grpwithId.push(splitValue[1]);
        }

        setAssignedStores(grpStore);
        setGroupsAssigned(grpwithId);
      } else {
        setAssignedStores([]);
      }
    });
  };
  const assignUserGrp = (parent, subGrp) => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: ASSIGN_USER_GRP,
        variables: {
          parentGroupName: parent,
          subgroupName: subGrp,
          username: userName
        }
      })
      .then(result => {
        setOpenAlert(true);
        setAlertMsg('Added Group Membership');
        setAlertType('success');
        if (userName == localStorage.getItem('userID')) {
          setOpenDialog(true);
        } else {
          setOpenDialog(false);
        }
        setTimeout(() => {
          setOpenAlert(false);
          // setAlertMsg('');
        }, 1000);
        fetchData();
      });
  };

  const removeUserGrp = (parent, subGrp) => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: REMOVE_USER_GRP,
        variables: {
          parentGroupName: parent,
          subgroupName: subGrp,
          username: userName
        }
      })
      .then(result => {
        setOpenAlert(true);
        setAlertMsg('Removed Group Membership');
        setAlertType('success');
        if (userName == localStorage.getItem('userID')) {
          setOpenDialog(true);
        } else {
          setOpenDialog(false);
        }
        setTimeout(() => {
          setOpenAlert(false);
          //  setAlertMsg('');
        }, 1000);
        fetchData();
      });
  };
  const handleListItemClick = (event, index, item) => {
    setSelectedNode([]);
    setSelectedIndex(index);
    setAddedGrp(item);
  };
  const handleJoin = () => {
    setAddedGrp([]);
    setSelectedNode([]);
    if (userName) {
      var selectedGrp = [];

      var selectedStore = groupsAvailable.filter(e => e.store == selectedNode);
      // console.log('rodataGrp1', );
      var parentName = selectedStore[0].name;
      selectedGrp.push(parentName);
      if (!groupsAssigned.includes(parentName)) {
        var existingGrp = groupsAssigned;
        var newGrp = existingGrp.concat(selectedGrp);
        assignUserGrp(parentName, selectedNode);
        setGroupsAssigned(newGrp);
      }
    } else {
      setOpenAlert(true);
      setAlertMsg('Please add user details');
      setAlertType('warning');
      setTimeout(() => {
        setOpenAlert(false);
        //   setAlertMsg('');
      }, 2000);
    }
  };
  const handleLeave = () => {
    setAddedGrp([]);
    setSelectedNode([]);
    if (userName) {
      var remainingGrp = groupsAssigned.filter(element => element != addedGrp);
      setGroupsAssigned(remainingGrp);
      var parentName = groupsAvailable.filter(e => e.name == addedGrp);
      var subgroupName = parentName[0].store;
      removeUserGrp(addedGrp, subgroupName);
    } else {
      setOpenAlert(true);
      setAlertMsg('Please add user details');
      setAlertType('warning');
      setTimeout(() => {
        setOpenAlert(false);
        //  setAlertMsg('');
      }, 2000);
    }
  };
  const handleListItemBlur = () => {
    setSelectedIndex(null);
  };

  const handleCancel = () => {
    setOpenDialog(false);
  };
  const customList = (title, items) => {
    return (
      <div>
        {items.map((item, index) => {
          return (
            <List
              component="nav"
              style={{
                paddingTop: 1,
                paddingBottom: 1
              }}
            >
              <ListItem
                button
                selected={selectedIndex === index}
                onClick={event => handleListItemClick(event, index, item)}
                onBlur={handleListItemBlur}
                className={selectedIndex === index ? classes.selected : ''}
                style={{
                  paddingTop: 1,
                  paddingBottom: 1,
                  marginBottom: -10
                }}
              >
                <ListItemText
                  primaryTypographyProps={{
                    className: classes.listItemText
                  }}
                  primary={item}
                />
              </ListItem>
            </List>
          );
        })}
      </div>
    );
  };
  return (
    <Paper className={classes.Paper}>
      <Dialog
        open={openAlert}
        classes={{
          paper: classes.dialog
        }}
      >
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Dialog>
      <span style={{ display: 'flex', marginTop: '15px' }}>
        <Typography
          style={{ padding: 8 }}
          variant="h4"
          className={clsx(classes.mainLabel)}
        >
          Manage Groups
        </Typography>
      </span>

      <div className={classes.containerGrp}>
        <Card className={classes.card}>
          <CardHeader
            titleTypographyProps={classes.typtitle}
            title={'Available Groups'}
            action={
              <div>
                <Button
                  variant="contained"
                  className={clsx('reset-btn', classes.btnDiv)}
                  onClick={handleJoin}
                  color="primary"
                  disabled={selectedNode.length === 0}
                >
                  Add
                </Button>
              </div>
            }
          />
          <Divider />
          <CardContent className={classes.cardcontent}>
            <TreeView
              aria-label="multi-select"
              defaultCollapseIcon={<ExpandMoreIcon />}
              defaultExpandIcon={<ChevronRightIcon />}
              onNodeSelect={handleNodeSelect}
              className={classes.treeviewul}
              sx={{
                height: 216,
                flexGrow: 1,
                maxWidth: 400,
                overflowY: 'auto'
              }}
            >
              {console.log('assignedstores---------', assignedStores)}
              {console.log('assignedstores---------+++', groupsAvailable)}

              {groupsAvailable.map((value, index) => {
                if (assignedStores.includes(value.store)) {
                  // If the store is in the hiddenStores array, don't render the TreeItem
                  return null;
                } else {
                  return (
                    <TreeItem
                      nodeId={value.store}
                      className={classes.treeItem}
                      label={
                        <Typography
                          variant="body1"
                          className={classes.listItemText}
                        >
                          {value.name}
                        </Typography>
                      }
                    />
                  );
                }
              })}
            </TreeView>
          </CardContent>
        </Card>
        <Card className={classes.card}>
          <CardHeader
            titleTypographyProps={classes.typtitle}
            title={'Group Membership'}
            action={
              <div>
                <Button
                  variant="contained"
                  className={clsx('reset-btn', classes.btnDiv)}
                  onClick={handleLeave}
                  color="primary"
                  disabled={addedGrp.length === 0}
                >
                  Remove
                </Button>
              </div>
            }
          />
          <Divider />
          <CardContent className={classes.cardcontent}>
            {groupsAssigned.length > 0 && customList('title', groupsAssigned)}
          </CardContent>
        </Card>

        <Dialog aria-labelledby="confirmation-dialog-title" open={openDialog}>
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Please Logout to see the changes.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancel} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    </Paper>
  );
};
export default Groups;
