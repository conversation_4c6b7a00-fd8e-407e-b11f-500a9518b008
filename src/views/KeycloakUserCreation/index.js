import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import UserListing from './UserListing.js';
import { setRefreshStatus, setReloadStatus } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import Charts from './Charts.js';
import { Redirect } from 'react-router-dom';
import { withKeycloak } from '@react-keycloak/web';

// const useStyles = makeStyles(theme => ({
//   root: {
//     paddingTop: theme.spacing(0),
//     paddingBottom: theme.spacing(3)
//   },
//   statistics: {
//     marginTop: theme.spacing(3)
//   },
//   notifications: {
//     marginTop: theme.spacing(6)
//   },
//   projects: {
//     marginTop: theme.spacing(6)
//   },
//   todos: {
//     marginTop: theme.spacing(6)
//   }
// }));

function KeycloakUserCreation(props) {
  const session = useSelector(state => state.session);
  //  const classes = useStyles();
  //  const dispatch = useDispatch();
  const history = useHistory();

  return (
    <Page title="UserCreation">
      {props.keycloak.realmAccess.roles.includes('client') ||
      props.keycloak.realmAccess.roles.includes('user') ||
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts session={session} history={history} />
      )}
    </Page>
  );
}

export default withKeycloak(KeycloakUserCreation);
