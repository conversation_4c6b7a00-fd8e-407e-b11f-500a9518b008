import React, { Component } from 'react';

import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import clsx from 'clsx';
import 'src/styles.css';
import { withKeycloak } from '@react-keycloak/web';
import { withStyles } from '@material-ui/styles';
import UserCreation from './UserCreation';
import { Paper, Grid, Typography, Button } from '@material-ui/core';
import UserListing from './UserListing';
import Credentials from './Credentials';
import RoleMapping from './RoleMapping';
import Groups from './Groups';
import { connect } from 'react-redux';
import { setUserDetails } from 'src/actions';
class Charts extends Component {
  constructor(props) {
    super(props);
    console.log('chartjs', this.props);
    this.state = {
      tabSelection: 'one',
      showList: true,
      editType: ''
    };
  }

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  handleTabAfterSave = () => {
    this.setState({ tabSelection: 'two' });
  };
  handleShowList = () => {
    this.setState({ showList: false });
  };
  handleBackclick = () => {
    this.setState({ showList: true });
    const { dispatch } = this.props;
    dispatch(setUserDetails(''));
    this.setState({ tabSelection: 'one' });
    sessionStorage.clear();
    this.props.history.push({
      pathname: '/KeycloakUserCreation',
      state: {
        check: true
      }
    });
  };
  render() {
    const { classes } = this.props;
    console.log('props', this.props.history.location.editType);
    return (
      <div>
        {this.state.showList ? (
          <UserListing
            handleShowList={this.handleShowList}
            history={this.props.history}
          ></UserListing>
        ) : (
          <Paper
            square
            style={{
              margin: 8,
              height: '800px'
            }}
          >
            <Paper
              square
              style={{
                height: '50px'
              }}
            >
              <Button
                variant="contained"
                className={clsx('bck-btn', classes.btnback)}
                onClick={this.handleBackclick}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button>
              <div className={classes.typolabel}>
                <Typography variant="body1" className={classes.label}>
                  {this.props.session.userId}
                </Typography>
              </div>
            </Paper>
            <Grid container xs={12} className={'user details'}>
              <Tabs
                variant="standard"
                // scrollButtons="auto"
                value={this.state.tabSelection}
                onChange={this.handleTabChange}
                indicatorColor="secondary"
                textColor="secondary"
                TabIndicatorProps={{ style: { display: 'none' } }}
              >
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>User Details</div>}
                  value="one"
                  className={
                    this.state.tabSelection == 'one'
                      ? classes.tabSelected
                      : null
                  }
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Credentials</div>}
                  value="two"
                  className={
                    this.state.tabSelection == 'two'
                      ? classes.tabSelected
                      : null
                  }
                  // disabled={
                  //   this.props.history.location.editType == 'new' ? true : false
                  // }
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Role Mappings</div>}
                  value="three"
                  className={
                    this.state.tabSelection == 'three'
                      ? classes.tabSelected
                      : null
                  }
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Groups</div>}
                  value="four"
                  className={
                    this.state.tabSelection == 'four'
                      ? classes.tabSelected
                      : null
                  }
                />
              </Tabs>
            </Grid>
            {this.state.tabSelection == 'one' ? (
              <UserCreation
                history={this.props.history.location}
                session={this.props.session}
                // handleTabAfterSave={this.handleTabAfterSave()}
              ></UserCreation>
            ) : (
              ''
            )}
            {this.state.tabSelection == 'two' ? (
              <Credentials
                session={this.props.session}
                history={this.props.history}
              ></Credentials>
            ) : (
              ''
            )}
            {this.state.tabSelection == 'three' ? (
              <RoleMapping session={this.props.session}></RoleMapping>
            ) : (
              ''
            )}
            {this.state.tabSelection == 'four' ? (
              <Groups session={this.props.session}></Groups>
            ) : (
              ''
            )}
          </Paper>
        )}
      </div>
    );
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  btnback: {
    top: 16,
    left: 10
  },
  typolabel: {
    marginLeft: 80,
    marginTop: -6
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14
  }
});
export default connect()(withKeycloak(withStyles(styles)(Charts)));
