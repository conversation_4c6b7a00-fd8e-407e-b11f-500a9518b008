import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { getTimeZone } from '../../utils/Utils';
import { getGridorMatrixPayTypeDetails } from 'src/utils/hasuraServices';
import Kpi from './Kpi';
import { setLaborGridTypes, setPartsMatrixTypes } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { CircularProgress, Grid } from '@material-ui/core';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));

function KpiReport() {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const history = useHistory();
  var timezone = getTimeZone();
  const pathaname = history.location.pathname.split('/')[1];
  const [selectedPayType, setSelectedPayType] = useState('');
  const [matrixTypes, setMatrixType] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingParts, setLoadingParts] = useState(false);
  const dispatch = useDispatch();
  // useEffect(() => {
  //   fetchData();
  // }, []);

  // const fetchData = () => {
  //   getGridorMatrixPayTypeDetails('paytype_matrix', result => {
  //     if (result.length > 0) {
  //       setMatrixType(result);
  //       setSelectedPayType(result[0]);
  //     } else {
  //       setMatrixType(result);
  //       setSelectedPayType(result);
  //     }
  //   });
  // };
  return (
    <Page
      className={classes.root}
      title={
        pathaname == 'KPIReportComparative'
          ? 'KPI Advisor Comparative'
          : 'KPI Store Comparative'
      }
      // title={'KPI Report #2 - Advisor Comparative'}
    >
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      history &&
      history.location &&
      history.location.pathname &&
      history.location.pathname != '/KPIReportStoreComparative' ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Kpi history={history} />
      )}
    </Page>
  );
}

export default KpiReport;
