import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setSelectedAdvisors, setSelectedStores } from 'src/actions';
import { useHistory } from 'react-router';
import { Tooltip } from '@material-ui/core';
import ArrowForwardIosIcon from '@material-ui/icons/ArrowForwardIos';
import $ from 'jquery';

export default props => {
  const session = useSelector(state => state.session);
  const dispatch = useDispatch();
  const history = useHistory();

  const pathaname = history.location.pathname.split('/')[1];
  const [expandState, setExpandState] = useState(true);
  const [colArr, setColArr] = useState(props.context.visibleColHeader);
  const [hiddenGoals, setHiddenGoals] = useState(props.context.hideGoals);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(false);
    setColArr(props.context.visibleColHeader);
    setIsLoading(true);
  }, [props.context.visibleColHeader]);

  useEffect(() => {
    setHiddenGoals(props.context.hideGoals);
  }, [props.context.hideGoals]);

  useEffect(() => {
    setIsLoading(props.context.isLoading);
  }, [props.context.isLoading]);
  const expandOrCollapse = () => {
    let currentState = props.columnGroup.displayedChildren[0].colId;

    if (
      props.displayName != 'Total Shop' &&
      props.displayName != 'Total Selected' &&
      props.displayName != 'Group Roll-Up' &&
      props.displayName != 'Group Average'
    ) {
      if (pathaname == 'KPIReportComparative') {
        var advId = props.displayName.replace(/[^0-9]/g, '');
        // var advArr = session.selectedAdvisors;
        // if (!advArr.includes(advId)) {
        //   //checking weather array contain the id
        //   //adding to array because value doesnt exists

        //   advArr.push(advId);
        // } else {
        //   advArr.splice(advArr.indexOf(advId), 1); //deleting
        // }
        // dispatch(setSelectedAdvisors(advArr));

        props.context.setColDef(props, props.displayName, 'hide');
      } else {
        let colData = props.context.rowData[0].data;

        colData = colData.filter(e => e.res_storename == props.displayName);

        var storeid = colData[0].store_id;
        // var storeArr = session.selectedStores;
        // console.log('storeArr', storeArr, storeid);
        // if (!storeArr.includes(storeid)) {
        //   //checking weather array contain the id
        //   //adding to array because value doesnt exists

        //   storeArr.push(storeid);
        // } else {
        //   storeArr.splice(storeArr.indexOf(storeid), 1); //deleting
        // }
        // dispatch(setSelectedStores(storeArr));
        props.context.setColDef(props, props.displayName, 'hide');
      }
    }
    props.context.handleColumnHide();
    setExpandState(false);
    props.columnApi.setColumnVisible(currentState, false);
  };

  const showHideGoals = () => {
    props.context.hideGoalsColumn(props, props.displayName);
  };

  // useEffect(() => {
  //   props.columnGroup
  //     .getProvidedColumnGroup()
  //     .addEventListener('expandedChanged', syncExpandButtons);
  //   syncExpandButtons();

  //   return () => {
  //     props.columnGroup
  //       .getProvidedColumnGroup()
  //       .removeEventListener('expandedChanged', syncExpandButtons);
  //   };
  // }, []);

  function truncateDisplayName(name, maxLength = 25) {
    if (name.length > maxLength) {
      return name.substring(0, maxLength - 3) + '...';
    }
    return name;
  }

  return (
    <div
      className="ag-header-group-cell-label"
      style={{
        display: !colArr.includes(props.displayName) ? 'flex' : 'none',

        justifyContent: 'space-between',
        width: 220
      }}
    >
      <div style={{ display: 'flex' }}>
        <div
          style={{ pointerEvents: 'none' }}
          className={`customExpandButton ${expandState}`}
        >
          {/* <Tooltip title={<span style={{ fontSize: 11, height: 10 }}>Hide</span>}> */}
          <i
            onClick={() => expandOrCollapse()}
            className="fa fa-arrow-right column-heading"
          ></i>
          {/* </Tooltip> */}
        </div>
        <div className="customHeaderLabel" title={props.displayName}>
          {truncateDisplayName(props.displayName)}
        </div>
        {!hiddenGoals.includes(props.displayName) &&
        props.displayName != 'Total Selected' &&
        props.displayName != 'Group Average' &&
        props.displayName != 'Group Roll-Up' ? (
          <div className={`customExpandButton`} title={'Show/Hide'}>
            {/* <Tooltip title={<span style={{ fontSize: 11, height: 10 }}>Hide</span>}> */}
            <i
              onClick={() => showHideGoals()}
              className="fa fa-plus var-heading"
            ></i>
            {/* </Tooltip> */}
          </div>
        ) : props.displayName != 'Total Selected' &&
          props.displayName != 'Group Average' &&
          props.displayName != 'Group Roll-Up' ? (
          <div style={{ cursor: 'pointer' }} className={`customExpandButton`}>
            {/* <Tooltip title={<span style={{ fontSize: 11, height: 10 }}>Hide</span>}> */}
            <i
              onClick={() => showHideGoals()}
              className="fa fa-minus var-heading"
            ></i>
            {/* </Tooltip> */}
          </div>
        ) : (
          ''
        )}
      </div>

      <div
        style={{
          display:
            !hiddenGoals.includes(props.displayName) &&
            props.displayName != 'Total Selected' &&
            props.displayName != 'Group Average' &&
            props.displayName != 'Group Roll-Up'
              ? 'flex'
              : 'none'
        }}
        className="customHeaderLabel customHeaderLabelVar"
        title={'Goal / Var'}
      >
        {'Goal/Var'}
      </div>
    </div>
  );
};
