import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect
} from 'react';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Divider,
  FormControl,
  Grid,
  Button,
  TextField,
  InputLabel,
  Select,
  MenuItem
} from '@material-ui/core';
import clsx from 'clsx';
import { useHistory } from 'react-router';
import { makeStyles } from '@material-ui/styles';
import { AgGridReact } from '@ag-grid-community/react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import moment from 'moment';
import $ from 'jquery';
import './styles.css';
import PageHeader from 'src/components/PageHeader';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import SaveIcon from '@material-ui/icons/Save';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import Alert from '@material-ui/lab/Alert';
import {
  getKpiComparativeReport,
  getKpiComparativeStoreReport,
  getDrillDownMonthYears,
  getKpiToggleOptionsWithTimeZone,
  getLatestClosedDate,
  insertKPIReportName,
  getKpiSavedReports,
  getKpiReports
} from 'src/utils/hasuraServices';
import RestoreIcon from '@material-ui/icons/Restore';
import {
  setSelectedStores,
  setHiddenKpiRows,
  setSelectedAdvisors,
  setHiddenAdvisors,
  setRowSortOrder
} from 'src/actions';
import CustomHeaderGroup from './CustomHeaderGroup';
import KPIHeaderGroup from './KPIHeaderGroup';
import KPIColumnRenderer from './KPIColumnRenderer';
import TableCellRenderer from './TableCellRenderer';
import { getTimeZone, getYearValue } from 'src/utils/Utils';
import { useDispatch, useSelector } from 'react-redux';
import { exportToPDF } from './PrintDoc';
import SuccessSnackbar from './SuccessSnackbar';
import { TRUE } from 'sass';
import printDoc from './PrintDoc.js';
import CPPartsMarkupVsPartsCost from 'src/components/charts/CPPartsMarkupVsPartsCost';
import { TramOutlined } from '@material-ui/icons';

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'

    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  linkItem: {
    cursor: 'pointer'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  }
}));
const Kpi = () => {
  const gridRef = useRef();
  const classes = useStyles();
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const history = useHistory();

  const pathaname = history.location.pathname.split('/')[1];
  let startDate =
    session.kpiToggleStartDate != '' ? session.kpiToggleStartDate : '';
  let endDate = session.kpiToggleEndDate != '' ? session.kpiToggleEndDate : '';

  const containerStyle = useMemo(() => ({ height: '100%', marginTop: 8 }), []);
  const alertContainerStyle = useMemo(
    () => ({ height: '100%', padding: 12 }),
    []
  );

  const [visibleCol, setVisibleCol] = useState([]);
  const [visibleTotals, setVisibleTotals] = useState([]);
  const [rowData, setRowData] = useState([]);
  const [allData, setAllData] = useState([]);
  const [isFrom, setIsFrom] = useState(pathaname);
  const [sortOrderArr, setSortOrder] = useState([]);
  const [rowSortValue, setRowSortValue] = useState(0);
  const [advCount, setAdvCount] = useState(0);
  const [pickerChanged, setPickerChanged] = useState(false);
  const [reportChanged, setReportChanged] = useState(false);
  const [reportType, setReportType] = useState('SavedKpiReport');
  const [isFromSidebar, setIsFromSidebar] = useState(false);
  const [arrowClicked, setArrowClicked] = useState(false);
  const [arrowClickedFrom, setArrowClickedFrom] = useState('');
  const [filterStart, setFilterStart] = useState(startDate);
  const [filterEnd, setFilterEnd] = useState(endDate);
  const [filterChanged, setFilterChanged] = useState(false);
  const [advFilterChanged, setAdvFilterChanged] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [hiddenRowId, setHiddenRowId] = useState([]);
  const [clickHeader, setClickHeader] = useState(false);
  const [gridApi, setGridApi] = useState({});
  const [dates, setDates] = useState([]);
  const [selectedRowIds, setSelectedRowIds] = useState([]);
  const [lastWeek, setLastWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');
  const [hideGoals, setHideGoals] = useState([]);
  const [savedReports, setSavedReports] = useState([]);
  const [selAdvisors, setSelAdvisors] = useState([]);
  const [closedDate, setClosedDate] = useState('');
  const [openSaveDlg, setOpenSaveDlg] = useState('');
  const [reportName, setReportName] = useState();
  const [errorReport, setErrorReport] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [toggleChanged, setToggleChanged] = useState(false);
  const [requiredText, setRequiredText] = useState(false);
  const [gridOptions, setGridOptions] = useState({
    defaultColDef: {
      sortable: false,
      filter: 'agTextColumnFilter',
      resizable: true
    },

    columnDefs: columnDefs,
    enableSorting: false,
    enableFilter: true,
    pagination: false,

    getRowId: params => {
      return params;
    },
    rowSelection: 'multiple', // ADDED
    suppressRowClickSelection: true,
    isExternalFilterPresent: isExternalFilterPresent, // ADDED
    //doesExternalFilterPass: doesExternalFilterPass, // ADDED
    onSelectionChanged: onSelectionChanged // ADDED
  });
  const [columnDefs, setColumnDefs] = useState([
    {
      rowGroup: true,
      field: 'kpi_type',
      suppressMenu: true,
      headerClass: 'kpi-hide-header',
      hide: true,
      minWidth: 300,
      rowGroupIndex: 0,
      resizable: false,
      suppressSorting: false,
      pinned: 'left',
      suppressColumnsToolPanel: true

      // cellClass: rowCellClass
    }
  ]);
  useEffect(() => {
    getLatestClosedDate(result => {
      if (result) {
        var openDate = '';
        var Date1 = result[0].value;

        setClosedDate(Date1);

        localStorage.setItem('closedDate', Date1);
      }
    });
  }, [session.serviceAdvisor]);
  // let closedDate = localStorage.getItem('closedDate');

  const sideBar = useMemo(() => {
    return {
      toolPanels: [
        {
          id: 'columns',
          labelDefault: 'Columns',
          labelKey: 'columns',
          iconKey: 'columns',
          toolPanel: 'agColumnsToolPanel',

          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressColumnFilter: false,
            suppressColumnSelectAll: false,
            suppressColumnExpandAll: true,
            suppressColumnsToolPanel: true,
            allowDragFromColumnsToolPanel: true,
            suppressSyncLayoutWithGrid: false,
            // prevents columns being reordered from the Columns Tool Panel
            suppressColumnMove: false
          }
        }
      ],
      defaultToolPanel: 'columns'
    };
  }, []);
  const defaultColDef = useMemo(() => {
    return {
      sortable: false,
      resizable: false,
      suppressRowGroups: false,
      enableValue: true,
      enableRowGroup: true,
      enablePivot: true,
      groupHideOpenParents: true
    };
  }, []);
  useEffect(() => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);

        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        // setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        if (filterStart == '' && filterEnd == '') {
          // setFilterStart(moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'));
          // setFilterEnd(moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'));

          if (localStorage.getItem('kpiDataStatus') == 1) {
            setFilterStart(
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'));
            setPickerChanged(true);
            setFilterChanged(true);
          } else {
            setFilterStart(
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
            setPickerChanged(true);
            setFilterChanged(true);
          }
        }
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
        setIsPageLoading(true);
      }
    });
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        // setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date())
          .clone()
          .format('YYYY-MM-DD');
        //  setSelectedDates([endDate, endDate]);
      }
    });
  }, []);
  useEffect(() => {
    getKpiSavedReports(result => {
      if (result.data.statelessDbdKpiScorecardGetKpiSavedReportNames.nodes) {
        setSavedReports(
          result.data.statelessDbdKpiScorecardGetKpiSavedReportNames.nodes
        );
        setIsLoading(true);
      }
    });
  }, [session.storeSelected]);

  useEffect(() => {
    if (
      pickerChanged &&
      typeof filterStart != 'undefined' &&
      typeof filterEnd != 'undefined' &&
      reportChanged
    ) {
      var advisor = session.serviceAdvisor;
      console.log('advisor===', reportChanged);
      //setVisibleCol([]);
      setVisibleTotals([]);

      setIsFromSidebar(false);
      // dispatch(setSelectedAdvisors([]));
      dispatch(setSelectedStores([]));
      dispatch(setHiddenKpiRows([]));
      setIsLoading(false);

      if (reportType == 'advisor') {
        getKpiComparativeReport(
          filterStart,
          filterEnd,
          1,
          2,
          visibleCol,
          'KPI_SERV_COMP',
          getTimeZone(),

          null,
          '',
          'C',
          'Customer',
          'N',
          null,
          result => {
            if (
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails
            ) {
              setReportChanged(false);
              setIsLoading(true);
              setPickerChanged(false);
              var data = JSON.parse(
                result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                  .kpiScorecardDetails[0].jsonData
              );

              if (data[0].data != null) {
                setRowData(data);

                setAdvCount(data[0].data.length - 2);
                let advDataArr = Object.keys(data[0].data).map(function(k) {
                  return data[0].data[k].adv_or_tech_name;
                });

                setHideGoals(advDataArr);
                setArrowClickedFrom('page');
                if (visibleCol.length > 0) {
                  var columns = visibleCol;
                  let advDataArr = Object.keys(data[0].data).map(function(k) {
                    return data[0].data[k].adv_or_tech_name;
                  });

                  columns.map(function(element) {
                    gridApi.columnApi.setColumnVisible(element + '_1', false);
                  });
                } else {
                  setAllData(data);
                }
              } else {
                if (Object.keys(gridApi).length != 0) {
                  let colDefs = gridApi.api.columnController.columnDefs;

                  colDefs = colDefs.filter(e => e.field == 'kpi_type');

                  gridApi.api.setColumnDefs(colDefs);
                }
              }
            }
          }
        );
      } else if (isFrom == 'KPIReportStoreComparative') {
        getKpiComparativeStoreReport(
          filterStart,
          filterEnd,
          1,
          2,
          null,
          'KPI_STORE_COMP',
          getTimeZone(),

          '1',
          '',
          'C',
          'Customer',
          JSON.parse(localStorage.getItem('allPermittedStores')).join(','),
          'N',
          result => {
            if (
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardDetailsStorewise
                .kpiScorecardDetailsStorewises
            ) {
              setIsLoading(true);

              var data = JSON.parse(
                result.data
                  .statelessDbdKpiScorecardGetKpiScorecardDetailsStorewise
                  .kpiScorecardDetailsStorewises[0].jsonData
              );
              setPickerChanged(false);

              if (data[0].data != null) {
                setRowData(data);
              } else {
                if (Object.keys(gridApi).length != 0) {
                  let colDefs = gridApi.api.columnController.columnDefs;

                  colDefs = colDefs.filter(e => e.field == 'kpi_type');

                  gridApi.api.setColumnDefs(colDefs);
                }
              }
            }
          }
        );
      }
    }
  }, [reportChanged, reportType]);

  useEffect(() => {
    setVisibleCol([]);
    setVisibleTotals([]);

    setIsFromSidebar(false);
    dispatch(setSelectedAdvisors([]));
    dispatch(setSelectedStores([]));
  }, [isFrom]);
  useEffect(() => {
    if (
      typeof filterStart != 'undefined' &&
      filterStart != '' &&
      typeof filterEnd != 'undefined' &&
      filterEnd != ''
    ) {
      setVisibleCol([]);
      setVisibleTotals([]);
      setRowData([]);
      setPickerChanged(true);
      setIsFromSidebar(false);
      dispatch(setSelectedAdvisors([]));

      if (session.serviceAdvisor.includes('All')) {
        setAdvFilterChanged(false);
      } else {
        setAdvFilterChanged(true);
      }
    }
  }, [session.serviceAdvisor]);
  useEffect(() => {
    if (isLoading) {
      var roData = rowData;

      if (roData.length > 0) {
        if (Object.keys(gridApi).length != 0) {
          if (reportType == 'advisor') {
            dynamicallyConfigureColumnsFromObject(gridApi, roData, 'desc');
          } else {
            dynamicallyConfigureColumnsFromObjectStoreWise(
              gridApi,
              roData,
              'desc'
            );
          }

          setRowData(roData);
          setAllData(roData);
        }
      }
      // dynamicallyConfigureColumnsFromObject(gridApi, roData);
    }
  }, [rowData]);
  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
    // setTimeout(() => {
    //   this.setState({ goalFail: false });
    // }, 2000);
  };
  const handleRecalc = (params, arr, opt, advs) => {
    var permittedStores = localStorage.getItem('allPermittedStores');

    if (Object.keys(params).length != 0) {
      params.api.showLoadingOverlay();
    }
    setClickHeader(true);

    //gridApi.columnApi.setColumnVisible('Group Average_1', true);
    //dispatch(setHiddenKpiRows([]));
    if (reportType == 'advisor') {
      if (rowData.length > 0) {
        let numbers = Object.keys(rowData[0].data).map(function(k) {
          return rowData[0].data[k].adv_or_tech_id;
        });

        var advArr = session.selectedAdvisors;

        if (!isFromSidebar) {
          var arrAdv = numbers.filter(function(obj) {
            return (
              obj != 'Total Shop' &&
              obj != 'Total Selected' &&
              !advArr.includes(obj)
            );
          });
        } else {
          var arrAdv = numbers.filter(function(obj) {
            return (
              obj != 'Total Shop' &&
              obj != 'Total Selected' &&
              advArr.includes(obj)
            );
          });
        }
      }
      //console.log('toggleChanged==', toggleChanged, advs);
      getKpiComparativeReport(
        filterStart,
        filterEnd,
        1,
        2,
        advCount == arr.length
          ? advs
          : !advFilterChanged && arr.length <= 0
          ? null
          : toggleChanged
          ? advs
          : arrAdv.length > 0
          ? arrAdv
          : session.serviceAdvisor.length > 0
          ? session.serviceAdvisor
          : null,

        'KPI_SERV_COMP',
        getTimeZone(),
        null,
        '',
        'C',
        'Customer',
        'Y',
        null,
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
              .kpiScorecardDetails
          ) {
            setIsLoading(true);
            setPickerChanged(false);

            var data = JSON.parse(
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails[0].jsonData
            );

            rowData.forEach((originalItem, index) => {
              const replacementItem = data.find(
                replacement => replacement.kpi_slno === originalItem.kpi_slno
              );
              if (replacementItem) {
                const selectedIndex = originalItem.data.findIndex(
                  data => data.adv_or_tech_name === 'Total Selected'
                );
                if (selectedIndex !== -1) {
                  originalItem.data[selectedIndex] = replacementItem.data[0];
                }
              }
            });

            if (opt == '') {
              // let numbers = Object.keys(rowData[0].data).map(function(k) {
              //   return rowData[0].data[k].adv_or_tech_name;
              // });

              // var filteredAdv = numbers.filter(function(obj) {
              //   return obj != 'Total Shop' && obj != 'Total Selected';
              // });

              let selectedIndex;
              rowData.forEach((originalItem, index) => {
                if (reportType == 'advisor') {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.adv_or_tech_name === 'Total Selected'
                  );
                } else {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.res_storename === 'Group Average'
                  );
                }

                if (selectedIndex !== -1) {
                  originalItem.data[selectedIndex].kpi_data = '0/0/0';
                }
              });
              setIsFromSidebar(true);
              // dispatch(setSelectedAdvisors([]));
            }
            setRowData(rowData);
            setClickHeader(false);
            if (opt == 'hide') {
              var columns = visibleCol;

              columns.map(function(element) {
                gridApi.columnApi.setColumnVisible(element + '_1', false);
              });
            }
            if (opt != '' && arr.length == 0) {
              setIsFromSidebar(false);
              dispatch(setSelectedAdvisors([]));
            }

            if (
              // !isFromSidebar &&
              arr.length == 0 &&
              advCount == visibleCol.length
            ) {
              setVisibleCol([]);
            }

            params.api.refreshHeader();
            params.api.redrawRows();

            params.api.hideOverlay();
            $('input.ag-checkbox-input').removeAttr('disabled');

            // if (data[0].data != null) {
            //   setRowData(data);
            // } else {
            //   if (Object.keys(gridApi).length != 0) {
            //     let colDefs = gridApi.api.columnController.columnDefs;
            //     colDefs = colDefs.filter(e => e.field == 'kpi_type');
            //     gridApi.api.setColumnDefs(colDefs);
            //   }
            // }
          }
        }
      );
    } else {
      // if (JSON.parse(permittedStores).length != arr.length) {
      if (rowData.length > 0) {
        let numbers = Object.keys(rowData[0].data).map(function(k) {
          return rowData[0].data[k].store_id;
        });
        var storeArr = session.selectedStores;

        if (!isFromSidebar) {
          var arrstore = numbers.filter(function(obj) {
            return obj != null && !storeArr.includes(obj);
          });
        } else {
          var arrstore = numbers.filter(function(obj) {
            return obj != null && storeArr.includes(obj);
          });
        }
      }
      params.api.showLoadingOverlay();
      getKpiComparativeStoreReport(
        filterStart,
        filterEnd,
        1,
        2,
        null,
        'KPI_STORE_COMP',
        getTimeZone(),
        '1',
        '',
        'C',
        'Customer',
        storeArr.length <= 0 && arr.length <= 0
          ? JSON.parse(permittedStores).join(',')
          : arrstore.length <= 0
          ? 'NA'
          : arrstore.join(','),

        'Y',
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetKpiScorecardDetailsStorewise
              .kpiScorecardDetailsStorewises
          ) {
            setIsLoading(true);
            var data = JSON.parse(
              result.data
                .statelessDbdKpiScorecardGetKpiScorecardDetailsStorewise
                .kpiScorecardDetailsStorewises[0].jsonData
            );

            rowData.forEach((originalItem, index) => {
              const replacementItem = data.find(
                replacement => replacement.kpi_slno === originalItem.kpi_slno
              );

              if (replacementItem) {
                const selectedIndex = originalItem.data.findIndex(
                  data => data.res_storename === 'Group Average'
                );

                if (selectedIndex !== -1) {
                  originalItem.data[selectedIndex] =
                    replacementItem.data[selectedIndex];
                }
              }
            });
            if (opt == '') {
              // let numbers = Object.keys(rowData[0].data).map(function(k) {
              //   return rowData[0].data[k].adv_or_tech_name;
              // });

              // var filteredAdv = numbers.filter(function(obj) {
              //   return obj != 'Total Shop' && obj != 'Total Selected';
              // });

              let selectedIndex;
              rowData.forEach((originalItem, index) => {
                if (reportType == 'advisor') {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.adv_or_tech_name === 'Total Selected'
                  );
                } else {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.res_storename === 'Group Average'
                  );
                }

                if (selectedIndex !== -1) {
                  originalItem.data[selectedIndex].kpi_data = '0/0/0';
                }
              });
              setIsFromSidebar(true);
              // dispatch(setSelectedAdvisors([]));
            }
            setRowData(rowData);
            setClickHeader(false);
            if (opt == 'hide') {
              var columns = visibleCol;

              columns.map(function(element) {
                gridApi.columnApi.setColumnVisible(element + '_1', false);
              });
            }
            if (opt != '' && arr.length == 0) {
              setIsFromSidebar(false);
              dispatch(setSelectedStores([]));
              // setVisibleCol([]);
            }

            // if (
            //   arr.length == 0 &&
            //   JSON.parse(permittedStores).length == visibleCol.length
            // ) {
            //   console.log('000ppp');
            //   setVisibleCol([]);
            // }
            params.api.refreshHeader();
            params.api.redrawRows();

            if (params.visible == false && params.source != 'columnMenu') {
              params.api.hideOverlay();
              $('input.ag-checkbox-input').removeAttr('disabled');
            } else if (
              opt == '' &&
              params.visible == false &&
              params.source == 'columnMenu'
            ) {
              params.api.hideOverlay();
              $('input.ag-checkbox-input').removeAttr('disabled');
            } else if (
              opt == 'show' &&
              params.visible == true &&
              params.source == 'toolPanelUi'
            ) {
              params.api.hideOverlay();
              $('input.ag-checkbox-input').removeAttr('disabled');
            }
          }
        }
      );
      // } else {
      //   params.api.showLoadingOverlay();
      //   let selectedIndex;
      //   rowData.forEach((originalItem, index) => {
      //     if (reportType == 'advisor') {
      //       selectedIndex = originalItem.data.findIndex(
      //         data => data.adv_or_tech_name === 'Total Selected'
      //       );
      //     } else {
      //       selectedIndex = originalItem.data.findIndex(
      //         data => data.res_storename === 'Group Average'
      //       );
      //     }

      //     if (selectedIndex !== -1) {
      //       originalItem.data[selectedIndex].kpi_data = '0/0/0';
      //     }
      //   });

      //   setRowData(rowData);
      //   params.api.refreshHeader();
      //   params.api.redrawRows();
      //   params.api.hideOverlay();
      //   $('input.ag-checkbox-input').removeAttr('disabled');
      // }
    }
  };

  const onRowClicked = params => {
    // Check if the clicked row is a group row
    if (params.node.group) {
      // Toggle the expansion state of the group row
      if (params.node.expanded) {
        params.node.setExpanded(false);
      } else {
        params.node.setExpanded(true);
      }
    }
  };
  const customSortStorewise = (a, b) => {
    const indexA = sortOrderArr.indexOf(a.res_storename);
    const indexB = sortOrderArr.indexOf(b.res_storename);
    return indexA - indexB;
  };
  const customSort = (a, b) => {
    const indexA = sortOrderArr.indexOf(a.adv_or_tech_name);
    const indexB = sortOrderArr.indexOf(b.adv_or_tech_name);
    return indexA - indexB;
  };
  useEffect(() => {
    if (reportType == 'advisor') {
      rowData.forEach(kpi => {
        kpi.data.sort(customSort);
      });
    } else {
      rowData.forEach(kpi => {
        kpi.data.sort(customSortStorewise);
      });
    }

    if (Object.keys(gridApi).length != 0) {
      let colDefs = gridApi.api.columnController.columnDefs;

      colDefs = colDefs.filter(e => e.field == 'kpi_type');

      gridApi.api.setColumnDefs(colDefs);
      if (reportType == 'advisor') {
        dynamicallyConfigureColumnsFromObject(gridApi, rowData, 'desc');
      } else {
        dynamicallyConfigureColumnsFromObjectStoreWise(
          gridApi,
          rowData,
          'desc'
        );
      }

      var columns = visibleCol;

      columns.map(function(element) {
        gridApi.columnApi.setColumnVisible(element + '_1', false);
      });
      var totals = visibleTotals;

      totals.map(function(element) {
        gridApi.columnApi.setColumnVisible(element + '_1', false);
      });
      setRowData(rowData);

      //  $('input.ag-checkbox-input').removeAttr('disabled');
      gridApi.api.redrawRows();
      $('.ag-header-group-cell-label').css('pointer-events', 'none');
    }
  }, [sortOrderArr]);
  const callClickEventOfFirstRow = params => {
    if (params && rowData.length > 0) {
      const firstRowNode = params.api.getRowNode(0);

      if (firstRowNode) {
        setArrowClicked(false);
        // Trigger the click event for the first row
        onArrowClicked(
          params,
          firstRowNode.data.kpi_name,
          firstRowNode.data.kpi_slno,
          0,
          'kpi'
        );

        var labelLen = {
          row: firstRowNode.data.kpi_name,
          order: 0,
          slno: 1
        };
        dispatch(setRowSortOrder(labelLen));
      }
    }
  };
  const onArrowClicked = (params, kpi_name, kpi_slno, sortValue, isFromKPI) => {
    // Check if the clicked row is a group row

    var jsonData = rowData;
    // setIsSortApplied(true);
    const sortedData = lodash.map(jsonData, item => {
      if (kpi_name == item.kpi_name) {
        return lodash.assign({}, item, {
          data: lodash
            .sortBy(
              item.data,
              data =>
                data.kpi_data != null &&
                parseFloat(data.kpi_data.split('/')[sortValue])
            )
            .reverse()
        });
      } else {
        return lodash.assign({}, item, {
          data: item.data
        });
      }
    });

    var index = sortedData.findIndex(
      x => x.kpi_name === kpi_name && x.kpi_slno === kpi_slno
    );

    let numbers = [];
    if (reportType == 'advisor') {
      numbers = Object.keys(sortedData[0].data).map(function(k) {
        return sortedData[index].data[k].adv_or_tech_name;
      });
      if (
        numbers.indexOf('Total Shop') > 0 ||
        numbers.indexOf('Total Selected') > 0
      ) {
        numbers.splice(numbers.indexOf('Total Selected'), 1);
        numbers.unshift('Total Selected');
        numbers.splice(numbers.indexOf('Total Shop'), 1);
        numbers.unshift('Total Shop');
      }
    } else {
      numbers = Object.keys(sortedData[0].data).map(function(k) {
        return sortedData[index].data[k].res_storename;
      });

      if (
        numbers.indexOf('Group Average') > 0 ||
        numbers.indexOf('Group Roll-Up') > 0
      ) {
        numbers.splice(numbers.indexOf('Group Roll-Up'), 1);
        numbers.unshift('Group Roll-Up');
        numbers.splice(numbers.indexOf('Group Average'), 1);
        numbers.unshift('Group Average');
      }
    }
    if (isFromKPI != 'kpi') {
      setArrowClickedFrom('page');
    }

    setSortOrder(numbers);
  };
  // let gridOptions = {
  //   defaultColDef: {
  //     sortable: true,
  //     filter: 'agTextColumnFilter',
  //     resizable: true
  //   },

  //   columnDefs: columnDefs,
  //   enableSorting: true,
  //   enableFilter: true,
  //   pagination: true,

  //   getRowId: params => {
  //     return params;
  //   },
  //   rowSelection: 'multiple', // ADDED
  //   isExternalFilterPresent: isExternalFilterPresent, // ADDED
  //   doesExternalFilterPass: doesExternalFilterPass, // ADDED
  //   onSelectionChanged: onSelectionChanged // ADDED
  // };
  function isExternalFilterPresent(params) {
    // start filtering if selected rows have length

    return gridOptions.api.getSelectedRows().length > 0;
  }
  //ADDED
  const doesExternalFilterPass = useCallback(
    node => {
      // console.log('node===', !node.selected);
      return !node.selected;
    },
    [hiddenRowId]
  );

  //ADDED- NOT BEING USED THOUGH
  function onSelectionChanged() {
    var selectedRows = gridOptions.api.getSelectedRows();
  }

  function hideRow(params, val, type) {
    var kpi = rowData.findIndex(function(item) {
      return item.kpi_type == type && item.kpi_no == val;
    });
    var selRows = gridOptions.api.getSelectedRows();

    var arr = selRows.filter(function(item) {
      return item.kpi_type == type && item.kpi_no == val;
    });

    let rowNode = gridOptions.api.getRowNode(kpi);

    if (arr.length == 0) {
      rowNode.setSelected(true);
    } else {
      rowNode.setSelected(false);
    }
    if (kpi == 11 || kpi == 21 || kpi == 32) {
      let rowNode = gridOptions.api.getRowNode(kpi + 1);

      if (arr.length == 0) {
        rowNode.setSelected(true);
      } else {
        rowNode.setSelected(false);
      }
    }
    var rowValue = gridOptions.getRowId(params);

    let groupRow = gridOptions.api.getDisplayedRowAtIndex(rowValue.rowIndex);
    // console.log('groupRow', groupRow);
    gridOptions.api.redrawRows({ rowNodes: [groupRow] });
    gridOptions.api.onFilterChanged();
  }

  const onGridReady = params => {
    var gridConfig = gridOptions;
    gridConfig.api = params.api;
    setGridOptions(gridConfig);
    var data = rowData;

    setGridApi(params);
    if (data.length > 0) {
      if (reportType == 'advisor') {
        dynamicallyConfigureColumnsFromObject(params, data, 'asc');
      } else {
        dynamicallyConfigureColumnsFromObjectStoreWise(params, data, 'asc');
      }
      // const numbers = Object.keys(data[0].data).map(function(k) {
      //   return data[0].data[k].adv_or_tech_name;
      // });
      // var arr = numbers.filter(function(obj) {
      //   return obj != 'Total Shop' && obj != 'Total Selected';
      // });

      // setSelAdvisors(arr);
      //

      setRowData(data);
      callClickEventOfFirstRow(params);
    } else {
      params.api.setColumnDefs([]);
    }
    //params.columnApi.setColumnVisible('kpi_type', true);
    // var columnToolPanel = params.api.getToolPanelInstance('columns');
    // columnToolPanel.expandColumnGroups();
  };
  function dynamicallyConfigureColumnsFromObjectStoreWise(
    params,
    anObject,
    order
  ) {
    let colDefs = params.api.columnController.columnDefs;

    //const keys = Object.keys(anObject);
    colDefs = colDefs.filter(e => e.field == 'kpi_type');

    params.api.setColumnDefs(colDefs);
    var obj;
    for (var row = 0; row < anObject.length; row++) {
      // Find the index of an object with a specific property value
      // Check if the object with the specified property value exists in the array

      if (anObject[row].data != null) {
        if (order == 'asc') {
          var dataArr = anObject[row].data;
        } else {
          var dataArr = anObject[row].data;
        }
        dataArr.forEach((key, i) => {
          let index = colDefs.findIndex(
            item => item.headerName == key.res_storename
          );
          if (index === -1) {
            obj = {
              headerName: key.res_storename,
              headerClass: 'kpi-group-header',
              groupId: key.res_storename,
              headerGroupComponent: 'kpiColumnRenderer',
              minWidth: 220,
              children: [
                {
                  field: key.res_storename,
                  suppressMenu: true,
                  headerClass: 'kpi-hide-header',
                  cellRenderer: 'tableCellRenderer',

                  minWidth: 220,
                  resizable: false,
                  cellStyle() {
                    return { textAlign: 'left' };
                  },
                  valueGetter: params => {
                    return i;
                  }
                }
              ]
            };

            colDefs.push(obj);
          }
        });
      }
    }
    const firstItemIds = ['Group Roll-Up', 'Group Average'];
    colDefs.sort((a, b) => {
      const indexA = firstItemIds.indexOf(a.headerName);
      const indexB = firstItemIds.indexOf(b.headerName);

      // If both items are in the list, compare their order
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }

      // If only one of the items is in the list, prioritize it
      if (indexA !== -1) {
        return -1;
      }

      if (indexB !== -1) {
        return 1;
      }

      // If neither item is in the list, maintain the original order
      return 0;
    });

    params.api.setColumnDefs(colDefs);
  }
  function dynamicallyConfigureColumnsFromObject(params, anObject, order) {
    let colDefs = params.api.columnController.columnDefs;

    //const keys = Object.keys(anObject);
    //const keys = Object.keys(anObject);
    colDefs = colDefs.filter(e => e.field == 'kpi_type');

    params.api.setColumnDefs(colDefs);
    var obj;
    for (var row = 0; row < anObject.length; row++) {
      // Find the index of an object with a specific property value
      // Check if the object with the specified property value exists in the array

      if (anObject[row].data != null) {
        if (order == 'asc') {
          var dataArr = anObject[row].data.reverse();
        } else {
          var dataArr = anObject[row].data;
        }
        dataArr.forEach((key, i) => {
          let index = colDefs.findIndex(
            item => item.headerName == key.adv_or_tech_name
          );
          if (index === -1) {
            obj = {
              headerName: key.adv_or_tech_name,
              headerClass: 'kpi-group-header',
              groupId: key.adv_or_tech_name,
              headerGroupComponent: 'kpiColumnRenderer',
              minWidth:
                typeof params.columnGroup == 'undefined'
                  ? 230
                  : key.adv_or_tech_name != params.columnGroup.groupId &&
                    hideGoals.includes(params.columnGroup.groupId)
                  ? 230
                  : key.adv_or_tech_name == params.columnGroup.groupId &&
                    !hideGoals.includes(params.columnGroup.groupId)
                  ? 230
                  : 200,
              children: [
                {
                  field: key.adv_or_tech_name,
                  suppressMenu: true,
                  headerClass: 'kpi-hide-header',
                  cellRenderer: 'tableCellRenderer',

                  minWidth:
                    typeof params.columnGroup == 'undefined'
                      ? 230
                      : key.adv_or_tech_name != params.columnGroup.groupId &&
                        hideGoals.includes(params.columnGroup.groupId)
                      ? 230
                      : !hideGoals.includes(params.columnGroup.groupId)
                      ? 230
                      : 200,
                  resizable: false,
                  cellStyle() {
                    return { textAlign: 'left' };
                  },
                  valueGetter: params => {
                    return i;
                  }
                }
              ]
            };
            colDefs.push(obj);
          }
        });
      }
    }

    params.api.setColumnDefs(colDefs);
    // $('input.ag-checkbox-input').attr('disabled', true);
    //$('.ag-header-group-cell-label').css('pointer-events', '');
  }
  const getRowStyle = params => {
    if (params.node.allChildrenCount != null) {
      return {
        background: '#ffc000 !important',
        border: '1px solid',
        marginTop: '3px',
        height: '23px !important'
      };
    }

    if (typeof params.node.data != 'undefined') {
      if (
        params.node.data.kpi_slno == 17 ||
        params.node.data.kpi_slno == 23 ||
        params.node.data.kpi_slno == 28
      ) {
        return {
          height: '30px !important'
        };
      } else {
        return {
          height: '27px !important'
        };
      }
    }
    // if (typeof params.data != 'undefined' && hiddenRowId.length > 0) {
    //   if (
    //     params.data.kpi_type == hiddenRowId[0].type &&
    //     params.data.kpi_no == hiddenRowId[0].kpi
    //   ) {
    //     return { display: 'none' };
    //   }
    // }
  };
  function getData() {
    return rowData;
  }
  const cellClassRules = params => {
    //console.log('params.node.group', params.node, params.node.group);
    // Replace 'groupClass' with theparams.node actual CSS class name you want to apply
    return { 'custom-cell': params => params.node.allChildrenCount > 0 }; // Apply the class to row group cells
  };
  const rowCellClass = params => {
    if (typeof params.data != 'undefined' && params.data.kpi_no == '') {
      return 'custom-cell-no-display';
    }
  };
  const cellClass = params => {
    if (params.node.allChildrenCount === null) {
      if (
        typeof params.data != 'undefined' &&
        (params.data.kpi_slno == '17' ||
          params.data.kpi_slno == '23' ||
          params.data.kpi_slno == '28')
      ) {
        return 'custom-cell cell-no-border-bottom';
      } else if (
        typeof params.data != 'undefined' &&
        (params.data.kpi_slno == '18' ||
          params.data.kpi_slno == '24' ||
          params.data.kpi_slno == '29')
      ) {
        return 'custom-cell cell-no-border-top';
      } else {
        return 'custom-cell';
      }
    } else {
      return 'custom-header';
    }
  };
  const onCheck = params => {
    var arr = visibleCol;
    var clickFrom = arrowClickedFrom;

    $('input.ag-checkbox-input').attr('disabled', true);

    if (params.column == null) {
      gridApi.columnApi.setColumnVisible('kpi_type', false);
    }

    if (
      params.column != null &&
      params.column.colId != 'kpi_type' &&
      params.column.parent.groupId != 'Total Shop' &&
      params.column.parent.groupId != 'Total Selected' &&
      params.column.parent.groupId != 'Group Roll-Up' &&
      params.column.parent.groupId != 'Group Average'
    ) {
      if (params.visible == true) {
        if (reportType == 'advisor') {
          arr = arr.filter(function(item) {
            return item !== params.column.parent.groupId;
          });

          var advId = params.column.parent.groupId.replace(
            /(^.*\[|\].*$)/g,
            ''
          );

          var advArr = session.selectedAdvisors;
          //console.log('visibleCol',advArr)
          if (!advArr.includes(advId)) {
            //checking weather array contain the id
            //adding to array because value doesnt existsz

            advArr.push(advId);
          } else {
            advArr.splice(advArr.indexOf(advId), 1); //deleting
          }
          var visAdvisors = selAdvisors;
          if (!visAdvisors.includes(advId)) {
            //checking weather array contain the id
            //adding to array because value doesnt existsz

            visAdvisors.push(advId);
          }
          setSelAdvisors(visAdvisors);

          setArrowClickedFrom('header');
          setVisibleCol(arr);

          dispatch(setSelectedAdvisors(advArr));

          handleRecalc(params, arr, 'show', visAdvisors);

          setArrowClicked(true);
          params.api.refreshHeader();
        } else {
          arr = arr.filter(function(item) {
            return item !== params.column.parent.groupId;
          });

          let colData = rowData[0].data;

          colData = colData.filter(
            e => e.res_storename == params.column.parent.groupId
          );

          var storeid = colData[0].store_id;

          var advArr = session.selectedStores;
          if (!advArr.includes(storeid)) {
            //checking weather array contain the id
            //adding to array because value doesnt existsz

            advArr.push(storeid);
          } else {
            advArr.splice(advArr.indexOf(storeid), 1); //deleting
          }

          setVisibleCol(arr);
          setArrowClickedFrom('header');
          dispatch(setSelectedStores(advArr));

          handleRecalc(params, arr, 'show', []);

          setArrowClicked(false);
          params.api.refreshHeader();
        }
      } else {
        if (reportType == 'advisor') {
          var advId = params.column.parent.groupId.replace(
            /(^.*\[|\].*$)/g,
            ''
          );
          var advArr = session.selectedAdvisors;

          //  console.log('arr===', arr);

          if (!arr.includes(params.column.parent.groupId)) {
            //checking weather array contain the id
            //adding to array because value doesnt exists

            arr.push(params.column.parent.groupId);
          }
          var adVar = [];
          //setIsFromSidebar(false);
          Object.keys(rowData[0].data).map(function(k) {
            if (!arr.includes(rowData[0].data[k].adv_or_tech_name)) {
              var id = rowData[0].data[k].adv_or_tech_name.replace(
                /(^.*\[|\].*$)/g,
                ''
              );
              adVar.push(id);
            }
          });

          adVar = adVar.filter(function(obj) {
            return obj != 'Total Shop' && obj != 'Total Selected';
          });
          //  console.log('advaaaa', adVar, advId);
          // if (adVar.includes(advId)) {
          //   //checking weather array contain the id
          //   //adding to array because value doesnt exists

          //   adVar.splice(adVar.indexOf(advId), 1); //deleting
          // }
          //dispatch(setSelAdv(adVar));
          // console.log('advaaaa', adVar, advId);
          setSelAdvisors(adVar);

          // console.log('advaaaa==advId', selAdvisors);
          setVisibleCol(arr);
          dispatch(setHiddenAdvisors(arr));
          // console.log('params.source', params.source);
          if (
            params.source == 'toolPanelUi' ||
            (params.source == 'api' && arrowClickedFrom != 'page')
          ) {
            if (!advArr.includes(advId)) {
              //checking weather array contain the id
              //adding to array because value doesnt exists

              advArr.push(advId);
            } else {
              if (isFromSidebar) {
                advArr.splice(advArr.indexOf(advId), 1); //deleting
              }
            }

            dispatch(setSelectedAdvisors(advArr));
            handleRecalc(params, arr, 'hide', adVar);
            setArrowClickedFrom('header');
          } else if (params.source == 'api' && arrowClickedFrom == 'page') {
            $('input.ag-checkbox-input').removeAttr('disabled');
          }
          clickFrom = 'header';
          setArrowClicked(false);
          params.api.refreshHeader();
        } else {
          let colData = rowData[0].data;
          clickFrom = 'header';
          colData = colData.filter(
            e => e.res_storename == params.column.parent.groupId
          );

          var storeid = colData[0].store_id;

          var advArr = session.selectedStores;

          if (!arr.includes(params.column.parent.groupId)) {
            //checking weather array contain the id
            arr.push(params.column.parent.groupId); //adding to array because value doesnt exists
          }

          setVisibleCol(arr);
          if (!advArr.includes(storeid)) {
            //checking weather array contain the id
            //adding to array because value doesnt exists

            advArr.push(storeid);
          } else {
            if (isFromSidebar) {
              advArr.splice(advArr.indexOf(storeid), 1); //deleting
            }
          }

          dispatch(setSelectedStores(advArr));
          if (
            params.source == 'toolPanelUi' ||
            (params.source == 'api' && arrowClickedFrom != 'page')
          ) {
            setArrowClickedFrom('header');
            handleRecalc(params, arr, 'hide', []);
          } else if (params.source == 'api' && arrowClickedFrom == 'page') {
            $('input.ag-checkbox-input').removeAttr('disabled');
          }
          setArrowClicked(false);
          params.api.refreshHeader();
        }
      }
    } else {
      params.columnApi.setColumnVisible('kpi_type', false);
      // console.log('params.column', params);
      if (params.column == null) {
        if (reportType == 'advisor') {
          let numbers = Object.keys(rowData[0].data).map(function(k) {
            return rowData[0].data[k].adv_or_tech_name;
          });

          var filteredAdv = numbers.filter(function(obj) {
            return obj != 'Total Shop' && obj != 'Total Selected';
          });
          setVisibleTotals(['Total Shop', 'Total Selected']);
        } else {
          let numbers = Object.keys(rowData[0].data).map(function(k) {
            return rowData[0].data[k].res_storename;
          });

          var filteredAdv = numbers.filter(function(obj) {
            return obj != 'Group Roll-Up' && obj != 'Group Average';
          });
          setVisibleTotals(['Group Roll-Up', 'Group Average']);
        }

        $('input.ag-checkbox-input').attr('disabled', true);
        setVisibleCol(filteredAdv);

        setIsFromSidebar(true);
        params.api.showLoadingOverlay();
        let selectedIndex;
        if (reportType == 'advisor') {
          rowData.forEach((originalItem, index) => {
            selectedIndex = originalItem.data.findIndex(
              data => data.adv_or_tech_name === 'Total Selected'
            );

            if (selectedIndex !== -1) {
              originalItem.data[selectedIndex].kpi_data = '0/0/0';
            }
          });
        }
        dispatch(setSelectedStores([]));
        setRowData(rowData);
        params.api.redrawRows();

        handleRecalc(params, filteredAdv, '', []);

        // params.api.hideOverlay();
      }
      var arr = visibleTotals;
      if (params.column != null) {
        if (params.visible == true) {
          arr = arr.filter(function(item) {
            return item !== params.column.parent.groupId;
          });
        } else {
          if (
            !arr.includes(params.column.parent.groupId) &&
            params.column.parent.groupId != 0
          ) {
            //checking weather array contain the id
            //adding to array because value doesnt exists

            arr.push(params.column.parent.groupId);
          }
        }

        if (reportType == 'advisor' && params.column.parent.groupId == 0) {
          setVisibleCol([]);
          dispatch(setSelectedAdvisors([]));
        }

        if (
          isFrom != 'KPIReportComparative' &&
          params.column.parent.groupId != 'Group Roll-Up' &&
          params.column.parent.groupId != 'Group Average'
        ) {
          setVisibleCol([]);
          dispatch(setSelectedStores([]));
        }
        if (params.column.parent.groupId == 0) {
          setVisibleTotals([]);
        } else {
          setVisibleTotals(arr);
        }

        if (
          params.column.parent.groupId != 'Total Shop' &&
          params.column.parent.groupId != 'Total Selected' &&
          params.column.parent.groupId != 'Group Roll-Up' &&
          params.column.parent.groupId != 'Group Average'
        ) {
          handleRecalc(params, [], 'show', []);
        } else {
          $('input.ag-checkbox-input').removeAttr('disabled');
          $('.ag-header-group-cell-label').css('pointer-events', '');
        }

        params.api.refreshHeader();
      }
    }
  };
  // useEffect(() => {
  //   console.log('visibleCol==', visibleCol);
  // }, [session.hiddenAdvisors]);

  const handleRowShowHide = (params, val, type) => {
    let arr = {
      type: type,
      kpi: val
    };
    var rowsArr = [];
    var selrows = selectedRowIds;

    let rowNode = params.api.getDisplayedRowAtIndex(params.rowIndex); // get the clicked row
    rowsArr.push(rowNode);
    var hidRows = hiddenRowId;
    if (
      typeof hiddenRowId != 'undefined' &&
      hiddenRowId.some(function(el) {
        return el.kpi == val && el.type == type;
      })
    ) {
      hidRows = hidRows.filter(
        item => !(item.type === arr.type && item.kpi === arr.kpi)
      );
    } else {
      hidRows.push(arr);
    }
    //  console.log('hidRows===', hidRows);
    setHiddenRowId(hidRows);
    if (selrows.includes(val)) {
      selrows.splice(selrows.indexOf(val), 1);
    } else {
      selrows.push(val);
    }

    setSelectedRowIds(selrows);

    // params.api.redrawRows({ rowNodes: rowsArr });
    hideRow(params, val, type);

    //params.api.redrawRows();
  };
  const getRowClass = params => {
    return 'custom-row-cells'; // Add a custom class to each row
  };
  const handleCallback = (event, picker) => {
    setFilterChanged(true);
    setFilterStart(picker.startDate.format('YYYY-MM-DD'));
    setFilterEnd(picker.endDate.format('YYYY-MM-DD'));
    setIsLoading(false);
    setPickerChanged(true);
    setRowData([]);
  };
  const handleChangeReport = event => {
    var reportname = event.target.value;
    setIsLoading(false);
    getKpiReports(reportname, result => {
      if (result.data.statelessDbdKpiScorecardGetKpiSavedReportDetails.json) {
        var data = JSON.parse(
          result.data.statelessDbdKpiScorecardGetKpiSavedReportDetails.json
        );
        setReportName(data[0].report_name);
        setFilterChanged(true);
        setFilterStart(data[0].start_date);
        setFilterEnd(data[0].end_date);
        setVisibleCol(data[0].kpi_ids);
        setIsLoading(true);
        setPickerChanged(true);
        setReportChanged(true);
        setReportType(data[0].kpi_report_type);
        // if (data[0].kpi_report_type == 'advisor') {
        //   console.log('data', data);
        //   setIsFrom('KPIReportComparative');
        // }
        //setIsLoading(true);
      }
    });
  };
  const setColDef = (params, val, opt) => {
    var arr = visibleCol;

    if (!arr.includes(val)) {
      //checking weather array contain the id
      arr.push(val); //adding to array because value doesnt exists
    } else {
      arr.splice(arr.indexOf(val), 1); //deleting
    }

    setVisibleCol(arr);

    //handleRecalc(params, arr, );
    // dispatch(setSelectedAdvisors(advArr));
    dispatch(setHiddenAdvisors(arr));
  };
  const setResetDashboard = () => {
    setVisibleCol([]);
    setVisibleTotals([]);
    setPickerChanged(true);
    // dynamicallyConfigureColumnsFromObject(gridApi, roData);
  };
  const hideGoalsColumn = (params, val) => {
    var arr = hideGoals;

    if (!arr.includes(val)) {
      //checking weather array contain the id
      arr.push(val); //adding to array because value doesnt exists
    } else {
      arr.splice(arr.indexOf(val), 1); //deleting
    }
    console.log('aaa==', arr);
    setHideGoals(arr);
    dynamicallyConfigureColumnsFromObject(params, rowData, 'desc');
    params.api.refreshHeader();
    params.api.redrawRows();
    //handleRecalc(params, arr, );
    // dispatch(setSelectedAdvisors(advArr));
    //dispatch(setHiddenAdvisors(arr));
    // dynamicallyConfigureColumnsFromObject(gridApi, roData);
  };
  const handleColumnHide = () => {
    setArrowClicked(true);
    setArrowClickedFrom('header');
    // dynamicallyConfigureColumnsFromObject(gridApi, roData);
  };
  const handleSaveReport = () => {
    setOpenSaveDlg(true);
  };
  const handleOkSaveReport = () => {
    console.log('report--', reportName);
    var iKpiReportType;
    if (reportType == 'advisor') {
      iKpiReportType = 'advisor';
    } else {
      iKpiReportType = 'store';
    }
    var iKpiIds = session.selectedAdvisors;
    var iStoreId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    insertKPIReportName(
      reportName,
      filterStart,
      filterEnd,
      iKpiReportType,
      iKpiIds,
      iStoreId,
      result => {
        console.log('success data====', result);
        if (
          result.data.statelessDbdKpiScorecardInsertKpiSavedReport.string ==
          'Insert successful'
        ) {
          setErrorReport('');
          setRequiredText(false);
          setOpenSaveDlg(false);
          setOpenSnackbar(true);
        } else {
          setRequiredText(true);
          setErrorReport(
            result.data.statelessDbdKpiScorecardInsertKpiSavedReport.string
          );
        }
      }
    );
  };
  const handleCancelSaveReport = () => {
    setOpenSaveDlg(false);
  };
  const onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s]*$/;

    if (!nameRegex.test(e.target.value) && e.target.value) {
      setRequiredText('');
    } else {
      setErrorReport('');
      setReportName(e.target.value);

      setRequiredText(false);
    }
  };
  return (
    <React.Fragment>
      {/* <Kpi history={history} /> */}
      {isPageLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <Grid
              xs={12}
              className={clsx(classes.headerItem, 'main-title-kpi')}
            >
              <Grid className="adv-report">
                <Grid className="kpi-report-2-name">
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(classes.mainLabel, 'main-title')}
                  >
                    {reportType == 'advisor'
                      ? 'KPI Advisor Comparative'
                      : 'KPI Store Comparative'}
                    {typeof reportName != 'undefined' &&
                      ' - ' +
                        reportName +
                        '(' +
                        filterStart +
                        ' - ' +
                        filterEnd +
                        ')'}
                  </Typography>
                </Grid>

                <Grid className="picker-report">
                  <FormControl
                    margin="dense"
                    variant="outlined"
                    style={{
                      minWidth: 170,
                      marginLeft: 10,
                      paddingRight: 10
                    }}
                  >
                    <InputLabel
                      htmlFor="outlined-age-native-simple"
                      margin="dense"
                      style={{ marginTop: -4 }}
                    >
                      Available Reports
                    </InputLabel>
                    <Select
                      margin="dense"
                      variant="outlined"
                      label="Filter By"
                      name="duration"
                      className={'laborPartsGrid'}
                      // value={selectedGrid}
                      onChange={handleChangeReport}
                      MenuProps={{
                        getContentAnchorEl: null,
                        anchorOrigin: {
                          vertical: 'bottom',
                          horizontal: 'left'
                        },
                        transformOrigin: {
                          vertical: 'top',
                          horizontal: 'left'
                        }
                      }}
                    >
                      <MenuItem disabled value="">
                        <em>Please select</em>
                      </MenuItem>
                      {/* <MenuItem value={''}>{'Please Select'}</MenuItem> */}
                      {savedReports.length > 0 &&
                        savedReports.map(item => (
                          <MenuItem value={item.reportName}>
                            {item.reportName}
                          </MenuItem>
                        ))}
                    </Select>
                  </FormControl>
                  {/* <Tooltip title="Export To PDF">
                    <Link
                      className={classes.linkItem}
                      onClick={() =>
                        exportToPDF(gridApi, filterStart, filterEnd)
                      }
                    >
                      <ExportIcon />
                    </Link>
                  </Tooltip> */}
                </Grid>
              </Grid>
            </Grid>
            <Divider />
            {isLoading == false ? (
              <div>
                <Box className={classes.boxClass}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    className={classes.boxClass}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : typeof reportName != 'undefined' ? (
              <div id={'kpi-report-2'} style={containerStyle}>
                <div class="kpiscore-card-main">
                  <div class="custom-header-kpir">
                    {/* <div class="report-name-title">
                      <span class="report-num">KPI Report #1</span>
                      <span class="report-name">ABC Auto Group</span>
                      <span class="report-date">3/1/23 - 3/30/23</span>
                    </div> */}
                    {console.log('reportName', reportName)}
                    {typeof reportName != 'undefined' ? (
                      <div class="report-name-title">
                        <span class="report-num">
                          {reportType == 'advisor'
                            ? 'KPI Advisor Comparative'
                            : 'KPI Store Comparative'}
                        </span>
                        <span class="report-name">
                          {localStorage.getItem('storeGroup')}
                        </span>
                        <span className="report-date">
                          {filterStart != ''
                            ? moment(filterStart).format('MM/DD/YY')
                            : ''}
                          {' - '}
                          {filterEnd && filterEnd != ''
                            ? moment(filterEnd).format('MM/DD/YY')
                            : ''}
                        </span>
                      </div>
                    ) : (
                      <div class="report-name-title">
                        <span class="report-name">
                          {localStorage.getItem('storeGroup')}
                        </span>
                      </div>
                    )}
                    <div class="header-logo">
                      <img src="/images/logos/logo_armatus.png" />
                    </div>
                    <div class="fopc-main-title">
                      Fixed Ops Performance Center
                    </div>
                  </div>
                </div>
                {/* <div className={classes.kpiGrid}>
                  <div className="ag-header-group-cell-label">
                    <div className="kpi-header-cell">
                      <div className="customHeaderLabel report-title">
                        <Typography className={classes.kpiHeading}>
                          {'KPI Report#2'}
                        </Typography>
                      </div>
                      <div className="customHeaderLabel report-store">
                        <Typography
                          variant="h4"
                          className={classes.kpiStoreHeading}
                        >
                          {localStorage.getItem('selectedStoreName')}
                        </Typography>
                      </div>
                      <div className="customHeaderLabel report-date">
                        <Typography className={classes.kpiHeading}>
                          {'Last Qtr : Jul to Sep'}
                        </Typography>
                      </div>
                    </div>
                  </div>
                </div> */}
                <div
                  className={clsx(
                    classes.kpiTable,
                    'kpi-custom-table',
                    'ag-theme-balham'
                  )}
                  style={{
                    height: window.screen.availHeight - 250 + 'px',
                    alignContent: 'center',
                    marginLeft: '8px',
                    paddingRight: '16px',
                    paddingBottom: '16px'
                  }}
                >
                  <AgGridReact
                    ref={gridRef}
                    frameworkComponents={{
                      customHeaderGroup: CustomHeaderGroup,
                      kpiHeaderGroup: KPIHeaderGroup,
                      kpiColumnRenderer: KPIColumnRenderer,
                      tableCellRenderer: TableCellRenderer,
                      rowData: rowData
                    }}
                    sideBar={sideBar}
                    autoGroupColumnDef={{
                      headerName: '',
                      suppressMenu: true,
                      field: 'kpi_type',
                      pinned: 'left',
                      width: 380,
                      hide: true,
                      chartDataType: 'category',
                      cellRenderer: 'agGroupCellRenderer',
                      valueGetter: params => {
                        return params.data.kpi_name;
                      },
                      // rowSpan: function(params) {
                      //   //  console.log('ccc==', params);
                      //   if (typeof params.data != 'undefined') {
                      //     if (
                      //       params.data.kpi_type == 'Pricing' &&
                      //       params.data.kpi_no == 4
                      //     ) {
                      //       return 2;
                      //     } else {
                      //       return 1;
                      //     }
                      //   }
                      // },
                      cellClass: cellClass,

                      // provide extra params to the cellRenderer
                      cellRendererParams: {
                        // turn off the row count
                        suppressCount: true,
                        // turn off double click for expand
                        suppressDoubleClickExpand: true,
                        // enable checkbox selection
                        groupSuppressAutoColumn: true,
                        groupRemoveSingleChildren: true,
                        innerRenderer: 'kpiHeaderGroup'
                      }
                    }}
                    overlayLoadingTemplate={
                      '<span class="ag-overlay-loading-center">Processing...</span>'
                    }
                    context={{
                      visibleColHeader: visibleCol,
                      setColDef: setColDef,
                      handleRowShowHide: handleRowShowHide,
                      onRowClicked: onRowClicked,
                      onArrowClicked: onArrowClicked,
                      handleColumnHide: handleColumnHide,
                      hideGoals: hideGoals,
                      hideGoalsColumn: hideGoalsColumn,
                      rowData: rowData,
                      hiddenRowId: hiddenRowId,
                      selectedRowIds: selectedRowIds,
                      rowSortValue: rowSortValue,
                      isLoading: clickHeader
                    }}
                    onColumnVisible={onCheck}
                    //pivotMode={false}
                    //cellClassRules={cellClassRules}
                    gridOptions={gridOptions}
                    groupDisplayType={'groupRows'}
                    groupDefaultExpanded={1}
                    getRowStyle={getRowStyle}
                    getRowClass={getRowClass}
                    rowData={rowData}
                    columnDefs={columnDefs}
                    defaultColDef={defaultColDef}
                    groupHeaderHeight={30}
                    doesExternalFilterPass={doesExternalFilterPass}
                    //onColumnVisible={columnChanged}
                    //headerHeight={120}
                    // floatingFiltersHeight={50}
                    // pivotGroupHeaderHeight={50}
                    // pivotHeaderHeight={100}
                    onGridReady={onGridReady}
                    //onRowClicked={onRowClicked}
                    suppressContextMenu={true}
                  />
                </div>
              </div>
            ) : (
              <div style={alertContainerStyle}>
                <Alert
                  severity="info"
                  style={{ fontSize: 14, fontWeight: 'bold' }}
                >
                  No Report Selected{' '}
                </Alert>
              </div>
            )}
          </Paper>
          <Dialog
            fullWidth
            maxWidth="sm"
            aria-labelledby="confirmation-dialog-title"
            open={openSaveDlg}
          >
            <DialogTitle id="form-dialog-title">
              <Typography
                variant="h5"
                color="primary"
                style={{
                  textTransform: 'none'
                }}
              >
                Save KPI Report
              </Typography>
            </DialogTitle>
            {/* <DialogTitle id="confirmation-dialog-title">
                <Typography
                  variant="h5"
                  color="primary"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Sign Out
                </Typography>
              </DialogTitle> */}
            <DialogContent dividers>
              {/* <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                This option is not available from this page.
              </Typography> */}

              <TextField
                autoFocus
                onChange={onChangeReportName}
                value={reportName}
                helperText={
                  (reportName == undefined || reportName == '') && requiredText
                    ? 'This is required!'
                    : errorReport
                    ? errorReport
                    : ''
                }
                margin="dense"
                id="name"
                label="Report Name"
                type="text"
                fullWidth
              />
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCancelSaveReport} color="primary">
                Cancel
              </Button>
              <Button onClick={handleOkSaveReport} color="primary">
                Save
              </Button>
            </DialogActions>
          </Dialog>
          <SuccessSnackbar
            onClose={handleSnackbarClose}
            open={openSnackbar}
            msg={'Report saved successfully!'}
            //goalFail={this.state.goalFail}
          />
        </div>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </React.Fragment>
  );
};
export default Kpi;
