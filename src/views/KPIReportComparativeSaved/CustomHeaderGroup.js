import React, { useEffect, useState } from 'react';

export default props => {
  const { colGroupDef, displayName } = props;

  // if (props.columnGroup.children.groupId == '1') {
  //   return null;
  // }
  return (
    <div className="ag-header-group-cell-label">
      <div className="kpi-header-cell">
        <div className="customHeaderLabel report-title">{'KPI Report#1'}</div>
        <div className="customHeaderLabel report-store">
          {localStorage.getItem('selectedStoreName')}
        </div>
        <div className="customHeaderLabel report-date">
          {'3/1/23 - 3/30/23'}
        </div>
      </div>
    </div>
  );
};
