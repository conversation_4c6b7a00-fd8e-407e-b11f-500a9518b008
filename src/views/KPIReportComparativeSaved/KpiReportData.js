import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import {
  MenuItem,
  Select,
  Paper,
  FormControl,
  Divider,
  IconButton,
  Button
} from '@material-ui/core';
import moment from 'moment';
import {
  getDataForCWITotalcharts,
  getDataForKPIROSharechart,
  getDataForKPILineROLtSixtyK,
  getDataForKPILineROGtSixtyK,
  getDataForKPIAvgAgeMiles,
  getDataForKPIFlatRateHrs,
  getDataForKPILaborGpRo,
  getDataForKPIPartsGpRo,
  getDataForKPITotalGpRo,
  getDataForKPIWorkmix,
  getDataForKPILaborGrid,
  getDataForKPILineRO,
  getDataForKPIPartsGrid,
  getKpiToggleOptionsWithTimeZone,
  getKPIScorecardGoalAdvisor,
  getGridorMatrixPayTypeDetails
} from 'src/utils/hasuraServices';
import { useSelector, useDispatch } from 'react-redux';
import clsx from 'clsx';
import KpiSummary from './Kpi';
import { CircularProgress, Grid, Tooltip } from '@material-ui/core';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import { Alert } from '@material-ui/lab';
import PageHeader from 'src/components/PageHeader';
import { jsPDF } from 'jspdf';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { UPDATE_KPI_CURRENT_DATE } from 'src/graphql/queries';
import domtoimage from 'dom-to-image';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import { traceSpan } from 'src/utils/OTTTracing';
import { setKpiToggle } from 'src/actions';
import ArrowRightAltIcon from '@material-ui/icons/ArrowRightAlt';
import { getNextMonth, getYearValue } from 'src/utils/Utils';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import { withKeycloak } from '@react-keycloak/web';

var lodash = require('lodash');

const styles = theme => ({
  root: {
    flexGrow: 1,
    width: '99%',
    marginLeft: 5
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  paper1: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.primary.main,
    margin: '12px 6px',
    maxWidth: '99%'
  },
  card: {
    width: '100%',
    height: '50vh',
    maxWidth: '100%',
    overflow: 'visible',
    display: 'flex',
    position: 'relative',
    // backgroundColor: '#c0d1df',
    backgroundColor: theme.palette.primary.light,
    border: 'solid #003d6b',
    marginTop: '-3px',
    '& > *': {
      flexGrow: 1,
      flexBasis: '50%',
      width: '50%'
    }
  },
  content: {
    padding: theme.spacing(8, 4, 3, 4)
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: -12
  },
  h1: {
    color: '#000',
    position: 'absolute',
    textAlign: 'center',
    left: '28%',
    fontWeight: 'bold',
    marginTop: '45px'
  },
  p: {
    color: '#fff'
  },
  headerDropdown: {
    marginTop: -11
  },
  divider: {
    backgroundColor: '#9b8c8ca1',
    fontWeight: 'bold'
  },
  container: {
    gridGap: 10
  },
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b',
    margin: window.innerHeight / 5 + 'px'
  },
  dataAsOfLabel: {
    marginLeft: 'auto',
    marginTop: 10,
    marginRight: 10
  },
  alertInfo: {
    fontSize: 16,
    fontWeight: '500',
    color: '#242f48',
    width: '100%',
    height: '45px !important'
  },
  linkItem: {
    cursor: 'pointer',
    marginTop: 3,
    marginLeft: 4
  },
  iconTurnUp1: {
    '@media (min-width: 2560px)': {
      display: 'none !important'
    },
    '@media (max-width: 2304px)': {
      display: 'none !important'
    },
    '@media (max-width: 1920px)': {
      display: 'none !important'
    },
    '@media (max-width: 1680px)': {
      display: 'block !important'
    },
    '@media (max-width: 1440px)': {
      display: 'block !important'
    }
  },
  iconTurnUp2: {
    '@media (min-width: 2560px)': {
      display: 'block !important'
    },
    '@media (max-width: 2304px)': {
      display: 'block !important'
    },
    '@media (max-width: 1920px)': {
      display: 'block !important'
    },
    '@media (max-width: 1680px)': {
      display: 'none !important'
    },
    '@media (max-width: 1440px)': {
      display: 'none !important'
    }
  }
});

function KpiReportData(props) {
  const { classes } = props;
  var timeZone = props.timezone ? props.timezone : '+05:30';
  const [CWITData, setCWITData] = useState([]);
  const [CWITAllData, setCWITAllDataAll] = useState([]);
  const [ROShareData, setROShareData] = useState([]);
  const [LineROLtSixtyK, setLineROLtSixtyK] = useState([]);
  const [LineROGtSixtyK, setLineROGtSixtyK] = useState([]);
  const [AvgAgeMiles, setAvgAgeMiles] = useState([]);
  const [FlatRateHrs, setFlatRateHrs] = useState([]);
  const [LaborGpRo, setLaborGpRo] = useState([]);
  const [PartsGpRo, setPartsGpRo] = useState([]);
  const [TotalGpRo, setTotalGpRo] = useState([]);
  const [WorkMix, setWorkMix] = useState([]);
  const [LaborGrid, setLaborGrid] = useState([]);
  const [PartsGrid, setPartsGrid] = useState([]);
  const [LineRO, setLineRO] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [isLoadingComplete, setLoadingComplete] = useState(false);
  const [advisorStoreGoal, setAdvisorStoreGoal] = useState([]);
  const session = useSelector(state => state.session);
  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  const [closedDate, setClosedDate] = useState(
    localStorage.getItem('closedDate')
  );
  const dispatch = useDispatch();
  let kpiDataToggles =
    localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';
  var toggle =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.toggle
      ? props.history.location.state.toggle
      : session.kpiToggle;
  const [CWIDataAdvisor, setCWIDataAllAdvisor] = useState([]);
  const [LineROLtSixtyKAdvisor, setLineROLtSixtyKAdvisor] = useState([]);
  const [ROShareDataAdvisor, setROShareDataAdvisor] = useState([]);
  const [LineROGtSixtyKAdvisor, setLineROGtSixtyKAdvisor] = useState([]);
  const [AvgAgeMilesAdvisor, setAvgAgeMilesAdvisor] = useState([]);
  const [FlatRateHrsAdvisor, setFlatRateHrsAdvisor] = useState([]);
  const [LaborGpRoAdvisor, setLaborGpRoAdvisor] = useState([]);
  const [PartsGpRoAdvisor, setPartsGpRoAdvisor] = useState([]);
  const [TotalGpRoAdvisor, setTotalGpRoAdvisor] = useState([]);
  const [WorkMixAdvisor, setWorkMixAdvisor] = useState([]);
  const [LaborGridAdvisor, setLaborGridAdvisor] = useState([]);
  const [PartsGridAdvisor, setPartsGridAdvisor] = useState([]);
  const [loadPdf, setLoadPdf] = useState(false);
  const [toggleOptions, setToggleOptions] = useState([]);
  const [selectedToggle, setSelectedToggle] = useState(session.kpiToggle);
  const [toggleOption, setToggle] = useState(session.kpiToggle);
  const [payTypeList, setPayTypeList] = useState([]);
  const [laborGridInternal, setLaborGridInternal] = useState([]);
  const [laborGridRV, setLaborGridRV] = useState([]);
  const [advisorLaborGridInternal, setAdvisorLaborGridInternal] = useState([]);
  const [advisorLaborGridRV, setAdvisorLaborGridRV] = useState([]);
  const [partsMatrixInternal, setPartsMatrixInternal] = useState([]);
  const [advisorPartsMatrixInternal, setAdvisorPartsMatrixInternal] = useState(
    []
  );
  const [kpiDataToggle, setKpiDataToggle] = useState(kpiDataToggles);
  let payType = 'C';
  if (props.keycloak.realm == 'billknightag') {
    payType = session.internalKpiToggle.substring(0, 1);
  }
  let gridType = '';
  if (session.internalKpiToggle == 'C' && payTypeList.length > 0) {
    gridType = payTypeList[0];
  } else {
    gridType = session.internalKpiToggle;
  }
  let laborGridTypes =
    session.stoveValue[0] == localStorage.getItem('selectedStoreId')
      ? JSON.parse(localStorage.getItem('laborGridTypes'))
      : session.allLaborGridTypes;
  let partsMatrixTypes =
    session.stoveValue[0] == localStorage.getItem('selectedStoreId')
      ? JSON.parse(localStorage.getItem('partsMatrixTypes'))
      : session.allPartsMatrixTypes;
  // let laborGridTypes = session.allLaborGridTypes;
  // let partsMatrixTypes = session.allPartsMatrixTypes;
  const exportKpiReportGrid = () => {
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title: 'Home',
      from: 'KPI'
    };
    setLoadPdf(true);

    var node = document.getElementById('kpi-body');
    var options = {
      quality: 1.0,
      height: node.offsetHeight * scale + 60,
      style: {
        transform: `scale(${scale}) translate(${node.offsetWidth /
          2 /
          scale}px, ${node.offsetHeight / 2 / scale}px)`
      },
      width: node.offsetWidth * scale - 70
    };
    const scale = 2;

    domtoimage
      .toJpeg(node, {
        quality: 0.95,
        height: node.offsetHeight * scale,
        width: node.offsetWidth * scale,
        style: {
          transform: 'scale(' + scale + ')',
          transformOrigin: 'top left',
          width: node.offsetWidth + 'px',
          height: node.offsetHeight + 'px'
        }
      })
      .then(dataUrl => {
        var doc = new jsPDF();

        doc.addImage(dataUrl, 'PNG', 5, 10, 220, 200);
        doc.save(
          session.kpiAdvisor[0] != 'All'
            ? 'KPI Report #1 - Individual Advisor.pdf'
            : 'KPI Report #1 - All Advisors.pdf'
        );
      })
      .catch(error => {
        console.error('oops, something went wrong!', error);
      });
  };

  const updateCurrentDate = () => {
    const start = new Date();
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_KPI_CURRENT_DATE
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/KpiReport',
          origin: '',
          event: 'Menu Load',
          is_from: 'UPDATE_KPI_CURRENT_DATE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
      });
  };
  const getToggleOptions = () => {
    var timeZone = props.timezone ? props.timezone : '+05:30';
    getKpiToggleOptionsWithTimeZone(timeZone, result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;

        setToggleOptions(dataArr);
        getPeriodSelected(toggleOption, dataArr);
      }
    });
  };

  const checkInternalToggle = () => {
    getGridorMatrixPayTypeDetails('paytype_grid', '', result => {
      setPayTypeList(result);
    });
  };
  const getStoreAndAdvisorGoals = () => {
    let dataArr = [];
    getKPIScorecardGoalAdvisor(session.kpiAdvisor[0], timeZone, result => {
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
          .statelessDbdKpiScorecardKpiScorecardGoals
      ) {
        let dataSet =
          result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
            .statelessDbdKpiScorecardKpiScorecardGoals;
        dataSet.map(item => {
          if (item.goalname == 'Gross Profit % (Labor)') {
            dataArr[0] = item;
          } else if (item.goalname == 'Gross Profit % (Parts)') {
            dataArr[1] = item;
          } else if (item.goalname == 'Avg Hours Per Day') {
            dataArr[2] = item;
          } else if (item.goalname == 'CP Avg Hours Per RO') {
            dataArr[3] = item;
          } else if (item.goalname == '% of CP 1 Line ROs (Under 60K)') {
            dataArr[4] = item;
          } else if (item.goalname == '% of CP 1 Line ROs (Over 60K)') {
            dataArr[5] = item;
          } else if (item.goalname == 'CP Work Mix - Maintenance') {
            dataArr[6] = item;
          } else if (item.goalname == 'CP Work Mix - Repair') {
            dataArr[7] = item;
          } else if (
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Light Duty' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Standard' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Retail' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp)'
          ) {
            dataArr[8] = item;
          } else if (
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Heavy Duty' ||
            item.goalname ==
              'Int Repair Labor Price (Targets / Misses / % Non-Comp)' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Medium Duty' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Commercial' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Highline' ||
            (dataSet.find(
              e =>
                e.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet'
            ) &&
            dataSet.find(
              e =>
                e.goalname ==
                'Wty Repair Labor Price (Targets / Misses / % Non-Comp)'
            )
              ? item.goalname ==
                'Wty Repair Labor Price (Targets / Misses / % Non-Comp)'
              : item.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet')

            //  item.goalname == "Wty Repair Grid (Targets / Misses / % Non-Comp)"
          ) {
            dataArr[9] = item;
          } else if (
            item.goalname ==
            'CP Repair Parts Price (Targets / Misses / % Non-Comp)'
          ) {
            dataArr[10] = item;
          } else if (
            item.goalname ==
            'Int Repair Parts Price (Targets / Misses / % Non-Comp)'
          ) {
            dataArr[11] = item;
          } else if (
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - RV' ||
            //  item.goalname == "CP Repair Grid (Targets / Misses / % Non-Comp) - Fleet") {
            (dataSet.find(
              e =>
                e.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet'
            ) &&
            dataSet.find(
              e =>
                e.goalname ==
                'Wty Repair Labor Price (Targets / Misses / % Non-Comp)'
            )
              ? item.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet'
              : '')
          ) {
            dataArr[12] = item;
          }
        });
        setAdvisorStoreGoal(dataArr);
      }
    });
  };
  useEffect(() => {
    setToggle(session.kpiToggle);
    updateCurrentDate();
    getStoreAndAdvisorGoals();
    getToggleOptions();
    checkInternalToggle();
    // getStoreAndAdvisorGoals();
    let orderedData;
    if (chartList) {
      let filteredResult = chartList.filter(
        item => item.dbdName == 'KPI' && item.parentId == null
      );
      orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    }

    let laborGridTypes =
      session.stoveValue[0] == localStorage.getItem('selectedStoreId')
        ? JSON.parse(localStorage.getItem('laborGridTypes'))
        : session.allLaborGridTypes;
    let partsMatrixTypes =
      session.stoveValue[0] == localStorage.getItem('selectedStoreId')
        ? JSON.parse(localStorage.getItem('partsMatrixTypes'))
        : session.allPartsMatrixTypes;
    setCWITData([]);
    setLineROLtSixtyK([]);
    setLineROGtSixtyK([]);
    setAvgAgeMiles([]);
    setFlatRateHrs([]);
    setLaborGpRo([]);
    setPartsGpRo([]);
    setTotalGpRo([]);
    setWorkMix([]);
    setLaborGrid([]);
    setLineRO([]);
    setPartsGrid([]);
    setROShareData([]);
    setCWITAllDataAll([]);
    setCWIDataAllAdvisor([]);
    setLineROLtSixtyKAdvisor([]);
    setROShareDataAdvisor([]);
    setLineROGtSixtyKAdvisor([]);
    setAvgAgeMilesAdvisor([]);
    setFlatRateHrsAdvisor([]);
    setLaborGpRoAdvisor([]);
    setPartsGpRoAdvisor([]);
    setTotalGpRoAdvisor([]);
    setWorkMixAdvisor([]);
    setLaborGridAdvisor([]);
    setPartsGridAdvisor([]);
    if (orderedData.length > 0 && gridType != '') {
      orderedData.map(item => {
        item.chartId == 1335
          ? getDataForCWITotalcharts(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  setLoading(false);
                  setLoadingComplete(true);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var output = resultArr.map(s => ({ label: s[0], val: s[1] }));
                  setCWIDataAllAdvisor(output);
                  delete resultArr[3];
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setCWITData(filteredOutput);
                }
              }
            )
          : item.chartId == 1337
          ? getDataForKPILineROLtSixtyK(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLineROLtSixtyKAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1338
          ? getDataForKPILineROGtSixtyK(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLineROGtSixtyKAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1340
          ? getDataForKPIAvgAgeMiles(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setAvgAgeMilesAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1339
          ? getDataForKPIFlatRateHrs(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setFlatRateHrsAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1341
          ? getDataForKPILaborGpRo(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLaborGpRoAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1342
          ? getDataForKPIPartsGpRo(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setPartsGpRoAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1343
          ? getDataForKPITotalGpRo(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setTotalGpRoAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1344
          ? getDataForKPIWorkmix(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setWorkMixAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1346
          ? getDataForKPILaborGrid(
              toggleOption,
              session.kpiAdvisor,
              timeZone,
              payType,
              laborGridTypes[0],
              null,
              callback => {
                setLoading(true);
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  getGridAndMatrixTypeData(item.chartId, toggleOption);
                  setLoading(false);
                  setLoadingComplete(true);
                  let resultSet = callback;

                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLaborGridAdvisor(filteredOutput);
                }
              }
            )
          : item.chartId == 1351
          ? getDataForKPILineRO(
              toggleOption,
              session.kpiAdvisor,
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLineRO(filteredOutput);
                }
              }
            )
          : item.chartId == 1353
          ? getDataForKPIPartsGrid(
              toggleOption,
              session.kpiAdvisor,
              timeZone,
              partsMatrixTypes.length > 0 &&
                partsMatrixTypes[0].substring(0, 1),
              null,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  getGridAndMatrixTypeData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setPartsGridAdvisor(filteredOutput);
                }
              }
            )
          : getDataForKPIROSharechart(
              toggleOption,
              session.kpiAdvisor,
              ['All'],
              timeZone,
              callback => {
                if (callback) {
                  getKpiStoreData(item.chartId, toggleOption);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setROShareDataAdvisor(filteredOutput);
                }
              }
            );
      });
    }
  }, [
    toggleOption,
    session.kpiAdvisor,
    localStorage.getItem('selectedStoreId'),
    session.kpiToggle
  ]);

  const getKpiStoreData = (chartId, toggleOption) => {
    chartId == 1335
      ? getDataForCWITotalcharts(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              setLoading(false);
              setLoadingComplete(true);
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var output = resultArr.map(s => ({ label: s[0], val: s[1] }));
              setCWITAllDataAll(output);
              delete resultArr[3];
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setCWITData(filteredOutput);
            }
          }
        )
      : chartId == 1337
      ? getDataForKPILineROLtSixtyK(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setLineROLtSixtyK(filteredOutput);
            }
          }
        )
      : chartId == 1338
      ? getDataForKPILineROGtSixtyK(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setLineROGtSixtyK(filteredOutput);
            }
          }
        )
      : chartId == 1340
      ? getDataForKPIAvgAgeMiles(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setAvgAgeMiles(filteredOutput);
            }
          }
        )
      : chartId == 1339
      ? getDataForKPIFlatRateHrs(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setFlatRateHrs(filteredOutput);
            }
          }
        )
      : chartId == 1341
      ? getDataForKPILaborGpRo(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setLaborGpRo(filteredOutput);
            }
          }
        )
      : chartId == 1342
      ? getDataForKPIPartsGpRo(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setPartsGpRo(filteredOutput);
            }
          }
        )
      : chartId == 1343
      ? getDataForKPITotalGpRo(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setTotalGpRo(filteredOutput);
            }
          }
        )
      : chartId == 1344
      ? getDataForKPIWorkmix(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setWorkMix(filteredOutput);
            }
          }
        )
      : chartId == 1346
      ? getDataForKPILaborGrid(
          toggleOption,
          ['All'],
          timeZone,
          laborGridTypes[0] == 'Warranty' ? 'W' : payType,
          laborGridTypes[0],
          null,
          callback => {
            setLoading(true);
            if (callback) {
              setLoading(false);
              setLoadingComplete(true);
              getGridAndMatrixTypeData(chartId, toggleOption, ['All']);
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setLaborGrid(filteredOutput);
            }
          }
        )
      : chartId == 1351
      ? getDataForKPILineRO(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setLineRO(filteredOutput);
            }
          }
        )
      : chartId == 1353
      ? getDataForKPIPartsGrid(
          toggleOption,
          ['All'],
          timeZone,
          partsMatrixTypes.length > 0 && partsMatrixTypes[0].substring(0, 1),
          null,
          callback => {
            if (callback) {
              getGridAndMatrixTypeData(chartId, toggleOption, ['All']);
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setPartsGrid(filteredOutput);
            }
          }
        )
      : getDataForKPIROSharechart(
          toggleOption,
          ['All'],
          ['All'],
          timeZone,
          callback => {
            if (callback) {
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              setROShareData(filteredOutput);
            }
          }
        );
  };

  const getGridAndMatrixTypeData = (chartId, toggleOption, advisor) => {
    if (props.keycloak.realm == 'billknightag') {
      payType = laborGridTypes[1].substring(0, 1);
    } else {
      payType = 'C';
    }
    let advisorSelected = advisor ? advisor : session.kpiAdvisor;
    if (chartId == 1346 && laborGridTypes.length > 1) {
      laborGridTypes.map(item => {
        getDataForKPILaborGrid(
          toggleOption,
          advisorSelected,
          timeZone,
          item == 'Warranty' ? 'W' : payType,
          item,
          null,
          callback => {
            setLoading(true);
            if (callback) {
              setLoading(false);
              setLoadingComplete(true);
              let resultSet = callback;
              delete resultSet[0].__typename;
              var resultArr = Object.entries(resultSet[0]);
              var filteredOutput = resultArr.map(s => ({
                label: s[0],
                val: s[1]
              }));
              if (
                advisorSelected.includes('All') &&
                item == laborGridTypes[1]
              ) {
                setLaborGridInternal(filteredOutput);
              } else if (
                advisorSelected.includes('All') &&
                item == laborGridTypes[2]
              ) {
                setLaborGridRV(filteredOutput);
              } else if (
                !advisorSelected.includes('All') &&
                item == laborGridTypes[1]
              ) {
                setAdvisorLaborGridInternal(filteredOutput);
              } else if (
                !advisorSelected.includes('All') &&
                item == laborGridTypes[2]
              ) {
                setAdvisorLaborGridRV(filteredOutput);
              }
            }
          }
        );
      });
    } else if (chartId == 1353 && partsMatrixTypes.length > 1) {
      getDataForKPIPartsGrid(
        toggleOption,
        advisorSelected,
        timeZone,
        partsMatrixTypes[1].substring(0, 1),
        null,
        callback => {
          if (callback) {
            let resultSet = callback;
            delete resultSet[0].__typename;
            var resultArr = Object.entries(resultSet[0]);
            var filteredOutput = resultArr.map(s => ({
              label: s[0],
              val: s[1]
            }));
            // setPartsGrid(filteredOutput);
            if (advisorSelected.includes('All')) {
              setPartsMatrixInternal(filteredOutput);
            } else {
              setAdvisorPartsMatrixInternal(filteredOutput);
            }
          }
        }
      );
    } else {
      return null;
    }
  };

  const handleChange = event => {
    if (event.target.value) {
      setToggle(event.target.value);
      dispatch(setKpiToggle(event.target.value));
    }
    getPeriodSelected(event.target.value, toggleOptions);
  };
  const getPeriodSelected = (toggle, toggleData) => {
    if (toggleData.length > 0) {
      let yesterDay = moment(toggleData[0].yesterday).format("MMM DD ' YY");
      let lastWeek =
        moment(toggleData[0].lastweekstartdate).format('MMM DD') +
        ' to ' +
        moment(toggleData[0].lastweekenddate).format("MMM DD ' YY");
      let mtd =
        moment(toggleData[0].mtdstartdate).format('MMM DD') +
        ' to ' +
        moment(toggleData[0].mtdenddate).format("MMM DD ' YY");
      let lastMonth = moment(toggleData[0].lastmonthstartdate).format('MMM');
      let ytd =
        moment(toggleData[0].ytdstartdate).format('MMM DD') +
        ' to ' +
        moment(toggleData[0].ytdenddate).format("MMM DD ' YY");
      let lastQtr =
        moment(toggleData[0].lastquarterstartdate).format('MMM') +
        ' to ' +
        //getNextMonth(toggleData[0].lastquarterstartdate) + ' - ' +
        moment(toggleData[0].lastquarterenddate).format('MMM');
      let lastYear =
        moment(toggleData[0].lastyearstartdate).format('MMM') +
        ' to ' +
        moment(toggleData[0].lastyearenddate).format('MMM') +
        " ' " +
        getYearValue(toggleData[0].lastyearenddate);
      let dayBeforeYesterday = moment(toggleData[0].dayBeforeYesterday).format(
        "MMM DD ' YY"
      );
      let lastThreeMonths =
        moment(toggleData[0].lastthreemonthstartdate).format('MMM') +
        ' to ' +
        moment(toggleData[0].lastthreemonthenddate).format('MMM');
      let lastTwelveMonths =
        moment(toggleData[0].lasttwelvemonthstartdate).format("MMM ' YY") +
        ' to ' +
        moment(toggleData[0].lasttwelvemonthenddate).format("MMM ' YY");

      if (toggle == 'YESDT') {
        setSelectedToggle('Yesterday ' + yesterDay);
      } else if (toggle == 'DBYESDT') {
        setSelectedToggle('Day Before Yest. ' + dayBeforeYesterday);
      } else if (toggle == 'LWEEK') {
        setSelectedToggle('Last Week ' + lastWeek);
      } else if (toggle == 'MTD') {
        setSelectedToggle('Mth to Date ' + mtd);
      } else if (toggle == 'LMONTH') {
        setSelectedToggle('Last Mth ' + lastMonth);
      } else if (toggle == 'YTD') {
        setSelectedToggle('YTD ' + ytd);
      } else if (toggle == 'LQRTR') {
        setSelectedToggle('Last Qtr ' + lastQtr);
      } else if (toggle == 'LYEAR') {
        setSelectedToggle('Last Year ' + lastYear);
      } else if (toggle == 'PLMTHREE') {
        setSelectedToggle('Last 3 Mths ' + lastThreeMonths);
      } else if (toggle == 'PLYONE') {
        setSelectedToggle('Last 12 Mths ' + lastTwelveMonths);
      }
    }
  };
  let lastWeek = '';
  let yesterDay = '';
  let mtd = '';
  let lastMonth = '';
  let ytd = '';
  let lastQtr = '';
  let lastYear = '';
  let dayBeforeYesterday = '';
  let lastThreeMonths = '';
  let lastTwelveMonths = '';
  if (toggleOptions.length > 0) {
    yesterDay = moment(toggleOptions[0].yesterday).format("MMM DD ' YY");
    lastWeek =
      moment(toggleOptions[0].lastweekstartdate).format('MMM DD') +
      ' to ' +
      moment(toggleOptions[0].lastweekenddate).format("MMM DD ' YY");
    mtd =
      moment(toggleOptions[0].mtdstartdate).format('MMM DD') +
      ' to ' +
      moment(toggleOptions[0].mtdenddate).format("MMM DD ' YY");
    lastMonth = moment(toggleOptions[0].lastmonthstartdate).format('MMM');
    ytd =
      moment(toggleOptions[0].ytdstartdate).format('MMM DD') +
      ' to ' +
      moment(toggleOptions[0].ytdenddate).format("MMM DD ' YY");
    lastQtr =
      moment(toggleOptions[0].lastquarterstartdate).format('MMM') +
      ' to ' +
      // getNextMonth(toggleOptions[0].lastquarterstartdate) + ' - ' +
      moment(toggleOptions[0].lastquarterenddate).format('MMM');
    lastYear =
      moment(toggleOptions[0].lastyearstartdate).format('MMM') +
      ' to ' +
      moment(toggleOptions[0].lastyearenddate).format('MMM') +
      " ' " +
      getYearValue(toggleOptions[0].lastyearenddate);
    dayBeforeYesterday = moment(toggleOptions[0].dayBeforeYesterday).format(
      "MMM DD ' YY"
    );
    lastThreeMonths =
      moment(toggleOptions[0].lastthreemonthstartdate).format('MMM') +
      ' to ' +
      //getNextMonth(toggleOptions[0].lastthreemonthstartdate) + ' - ' +
      moment(toggleOptions[0].lastthreemonthenddate).format('MMM');
    lastTwelveMonths =
      moment(toggleOptions[0].lasttwelvemonthstartdate).format("MMM ' YY") +
      ' to ' +
      moment(toggleOptions[0].lasttwelvemonthenddate).format("MMM ' YY");
  }
  const exportKpiReport = () => {
    exportKpiReportGrid();
    // console.log('report', exportReport);
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to PDF',
      title: props.title ? props.title : '',
      from: props.isFrom ? props.isFrom : '',
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('Export to PDF', spanAttribute);
  };
  const handleclick = () => {
    props.history.push({
      pathname: '/ScoreCardGoalSetting',
      state: {
        parent: 'KpiReport',
        toggle: toggleOption,
        advisor: session.kpiAdvisor
      }
    });
  };
  return (
    <React.Fragment>
      {session.kpiAdvisor.length == 0 ? (
        <React.Fragment>
          <Paper square style={{ height: window.innerHeight - 110 + 'px' }}>
            {/* <Alert variant="outlined" severity="info" color="error" className={classes.alertInfo} >
                Please choose an advisor to see a report !
              </Alert> */}
            {/* <span style={{ margin: 10 }} className={'kpiReportAlert'}>
              <Alert severity="info" className={classes.alertInfo}>
                Please choose an advisor to see a report
              </Alert>
            </span> */}
            {/* <span style={{margin: 10}} className={"kpiReportAlert"}>
              <Alert severity="info" className={classes.alertInfo}>
                Please choose an advisor to see a report
                <IconButton
                            size="large">
                <ArrowRightAltIcon />
                </IconButton>
              </Alert>
              
            </span> */}
            <Grid justify="center" className={classes.loaderGrid}>
              No Reports Found
            </Grid>
          </Paper>
        </React.Fragment>
      ) : (
        <React.Fragment>
          {/* <Kpi history={history} /> */}
          <div className={classes.root}>
            <Paper className={classes.paper}>
              <div className={clsx(classes.headerItem, 'main-title-kpi')}>
                <PageHeader
                  title={'KPI Report #2 - Advisor Comparative'}
                  // title={session.kpiAdvisor[0] != 'All' ? 'KPI Report 1 - Individual Advisor' : 'KPI Report 1 - All Advisors'}
                  hideTitle={false}
                  isFrom={'details'}
                  // exportPDF={true}
                  // exportKpiReportGrid={exportKpiReportGrid}
                  //setResetDashboard={this.setResetDashboard}
                />
                {/* <div>
                  <Button
                    variant="contained"
                    className={'editGoals-Btn'}
                    onClick={handleclick}
                  >
                    <Typography variant="body1" align="left">
                      Edit Goals
                    </Typography>
                  </Button>
                </div> */}
                {/* <div className={classes.headerDropdown}>
                  <FormControl
                    sx={{ m: 1, minWidth: 120 }}
                    variant="outlined"
                    margin="dense"
                    className={classes.formControl}
                  >
                    <Select
                      value={toggleOption}
                      className={'selectedToggle'}
                      onClick={handleChange}
                      inputProps={{ 'aria-label': 'Without label' }}
                      style={{
                        height: '100%',
                        borderRadius: 0,
                        border: '1px solid #003d6b'
                      }}
                    >
                      <MenuItem value={'YESDT'}>
                        <span className={'toggleLabel'}>Yesterday</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{yesterDay}</span>
                      </MenuItem>
                      <MenuItem value={'DBYESDT'}>
                        <span className={'toggleLabel'}>Day Before Yest.</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {dayBeforeYesterday}
                        </span>
                      </MenuItem>
                      <MenuItem value={'LWEEK'}>
                        <span className={'toggleLabel'}>Last Week</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{lastWeek}</span>
                      </MenuItem>
                      <MenuItem
                        value={'MTD'}
                        disabled={kpiDataToggle == 'MTD' ? false : true}
                      >
                        <span className={'toggleLabel'}>Mth to Date</span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{mtd}</span>
                      </MenuItem>
                      <MenuItem value={'LMONTH'}>
                        <span className={'toggleLabel'}>Last Mth </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{lastMonth}</span>
                      </MenuItem>
                      <MenuItem value={'PLMTHREE'}>
                        <span className={'toggleLabel'}>Last 3 Mths </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{lastThreeMonths}</span>
                      </MenuItem>
                      <MenuItem value={'LQRTR'}>
                        <span className={'toggleLabel'}>Last Qtr </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{lastQtr}</span>
                      </MenuItem>
                      <MenuItem value={'YTD'}>
                        <span className={'toggleLabel'}>YTD </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{ytd}</span>
                      </MenuItem>
                      <MenuItem value={'PLYONE'}>
                        <span className={'toggleLabel'}>Last 12 Mths </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>
                          {lastTwelveMonths}
                        </span>
                      </MenuItem>
                      <MenuItem value={'LYEAR'}>
                        <span className={'toggleLabel'}>Last Year </span>
                        <span className={'toggleSeparator'}></span>
                        <span className={'toggleValue'}>{lastYear}</span>
                      </MenuItem>
                    </Select>
                  </FormControl>
                </div>
                <Tooltip title="Export To PDF">
                  <Link className={classes.linkItem} onClick={exportKpiReport}>
                    <ExportIcon />
                  </Link>
                </Tooltip> */}
              </div>
              <Divider />
              <KpiSummary
                toggleOption={toggleOption}
                CWITData={CWITData}
                allCWITData={CWITAllData}
                ROShareData={ROShareData}
                LineROLtSixtyK={LineROLtSixtyK}
                LineROGtSixtyK={LineROGtSixtyK}
                FlatRateHrs={FlatRateHrs}
                AvgAgeMiles={AvgAgeMiles}
                LaborGpRo={LaborGpRo}
                PartsGpRo={PartsGpRo}
                TotalGpRo={TotalGpRo}
                WorkMix={WorkMix}
                LaborGrid={LaborGrid}
                PartsGrid={PartsGrid}
                CWIDataAdvisor={CWIDataAdvisor}
                ROShareDataAdvisor={ROShareDataAdvisor}
                LineROLtSixtyKAdvisor={LineROLtSixtyKAdvisor}
                LineROGtSixtyKAdvisor={LineROGtSixtyKAdvisor}
                FlatRateHrsAdvisor={FlatRateHrsAdvisor}
                AvgAgeMilesAdvisor={AvgAgeMilesAdvisor}
                LaborGpRoAdvisor={LaborGpRoAdvisor}
                PartsGpRoAdvisor={PartsGpRoAdvisor}
                TotalGpRoAdvisor={TotalGpRoAdvisor}
                WorkMixAdvisor={WorkMixAdvisor}
                LaborGridAdvisor={LaborGridAdvisor}
                PartsGridAdvisor={PartsGridAdvisor}
                toggleOptions={toggleOptions}
                history={props.history}
                selectedToggle={selectedToggle}
                advisorStoreGoal={advisorStoreGoal}
                laborGridTypes={laborGridTypes}
                partsMatrixTypes={partsMatrixTypes}
                laborGridInternal={laborGridInternal}
                laborGridRV={laborGridRV}
                advisorLaborGridInternal={advisorLaborGridInternal}
                advisorLaborGridRV={advisorLaborGridRV}
                partsMatrixInternal={partsMatrixInternal}
                advisorPartsMatrixInternal={advisorPartsMatrixInternal}
                matrixTypes={props.matrixTypes}
                //  handleToggleChange={handleToggleChange}
              />
            </Paper>
          </div>
        </React.Fragment>
      )}
    </React.Fragment>
  );
}

KpiReportData.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withKeycloak(withStyles(styles)(KpiReportData));
