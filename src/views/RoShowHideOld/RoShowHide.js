import React, {
  useState,
  useMemo,
  useRef,
  useEffect,
  useCallback
} from 'react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AgGridReact } from '@ag-grid-community/react';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import { makeStyles } from '@material-ui/core/styles';
import {
  Typography,
  Paper,
  Button,
  Tab,
  Tabs,
  LinearProgress,
  Box
} from '@material-ui/core';
import RestoreIcon from '@material-ui/icons/Restore';
import AddRo from './AddRo';
import clsx from 'clsx';
import { getRoHideRules, insertRoHideRules } from 'src/utils/hasuraServices';
import { TrainRounded } from '@material-ui/icons';
import CancelIcon from '@material-ui/icons/Cancel';
import EditIcon from '@material-ui/icons/Edit';
import SaveIcon from '@material-ui/icons/Save';
const Dealer = process.env.REACT_APP_DEALER;
// Styles
const useStyles = makeStyles(theme => ({
  errorMsg: {
    width: '92%',
    paddingTop: '1px',
    marginRight: '-15px',
    marginBottom: '10px'
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 18,
    float: 'right',
    marginTop: -47,
    width: 110,
    height: 27
  },
  btnContainer: {
    display: 'flex',
    width: '845px',
    justifyContent: 'space-between',
    width: '100%',
    marginRight: 25
  },
  btnUser: {
    height: '27px',
    fontSize: '12px !important',
    marginTop: '4px',
    width: 110,
    paddingRight: -29,
    paddingLeft: -18,
    left: 15
  }
}));
const ActionRenderer = props => {
  const { api, node, editingRowIndex, setEditingRowIndex, gridApiRef } = props;
  console.log('ActionRenderer props:', props);
  console.log('API:', api);
  console.log('Node:', node);

  const isEditing = node && node.rowIndex === editingRowIndex;

  const onEdit = () => {
    if (node && api) {
      setEditingRowIndex(node.rowIndex); // Set the new row as the editing row
      api.startEditingCell({
        rowIndex: node.rowIndex,
        colKey: 'active' // Start editing at a specific column (optional)
      });
    } else {
      console.error('Node or API is not defined.');
    }
  };

  const onSave = () => {
    if (gridApiRef.current) {
      setEditingRowIndex(null);
      gridApiRef.current.stopEditing();
    } else {
      console.error('Grid API is not initialized.');
    }
  };

  const onCancel = () => {
    if (gridApiRef.current) {
      gridApiRef.current.stopEditing(true); // Cancel the changes
      gridApiRef.current.redrawRows(); // Force the grid to redraw rows to reflect changes
      setEditingRowIndex(null); // Clear the editing row index
    }
  };

  return isEditing ? (
    <div>
      <SaveIcon
        onClick={onSave}
        style={{ cursor: 'pointer', marginRight: 10 }}
      />
      <CancelIcon onClick={onCancel} style={{ cursor: 'pointer' }} />
    </div>
  ) : (
    <EditIcon onClick={onEdit} style={{ cursor: 'pointer' }} />
  );
};

const CheckboxRenderer = props => {
  const { value, node, column } = props;

  const checkedHandler = event => {
    let checked = event.target.checked;
    let colId = column.colId;
    node.setDataValue(colId, checked ? 1 : 0);
  };

  return (
    <input
      type="checkbox"
      checked={value === 1}
      onChange={checkedHandler} // Use onChange for checkboxes
    />
  );
};
// Custom cell renderer for the Status column
// const CheckboxRenderer = props => {
//   console.log('CheckboxRenderer props:', props);
//   const { value } = props;
//   const checkedHandler = event => {
//     let checked = event.target.checked;

//     let colId = props.column.colId;
//     console.log('checked===', checked, colId);
//     props.node.setDataValue(colId, checked == true ? 1 : 0);

//     // var teamtech = props.context.componentParent.state.teamtech;
//     // if (checked == false) {
//     //   teamtech = teamtech.filter(val => val !== this.props.data.lbrtechno);
//     //   this.props.context.componentParent.setTeamTech(teamtech, this.props.node);
//     // } else {
//     //   teamtech.push(this.props.data.lbrtechno);
//     //   this.props.context.componentParent.setTeamTech(teamtech, this.props.node);
//     // }
//     // console.log('event', this.props.node);
//     // if (checked == true) {
//     //   this.props.node.setDataValue('teamAssignedTo', 'None');
//     // }
//     // this.props.api.refreshCells({
//     //   columns: ['teamAssignedTo'],
//     //   rowNodes: [this.props.node],
//     //   force: true
//     // });
//   };
//   return (
//     <input
//       type="checkbox"
//       checked={value === 1}
//       onClick={checkedHandler}
//       // readOnly // Make checkbox read-only if you don't want to edit it
//     />
//   );
// };

const GridComponent = () => {
  const classes = useStyles();
  const [showRo, setShowRo] = useState(false);
  const [rowData, setRowData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const gridApiRef = useRef(null);
  const columnApiRef = useRef(null);
  const [editingRowIndex, setEditingRowIndex] = useState(null); // State to track currently edited row
  // Define the columns for the grid
  const columnDefs = useMemo(
    () => [
      { headerName: 'Id', field: 'id', hide: true },
      {
        headerName: 'Column Name',
        field: 'columnName',
        suppressMenu: true,
        suppressMovable: true,
        unSortIcon: true,
        editable: false
      },
      {
        headerName: 'createdTime',
        field: 'createdTime',
        suppressMenu: true,
        suppressMovable: true,
        unSortIcon: true,
        editable: false
      },
      {
        headerName: 'operator',
        field: 'operator',
        suppressMenu: true,
        suppressMovable: true,
        unSortIcon: true,
        editable: false
      },
      {
        headerName: 'operandValue',
        field: 'operandValue',
        suppressMenu: true,
        suppressMovable: true,
        unSortIcon: true,
        editable: false
      },
      {
        headerName: 'lastUpdatedBy',
        field: 'lastUpdatedBy',
        suppressMenu: true,
        suppressMovable: true,
        unSortIcon: true,
        editable: false
      },
      {
        headerName: 'Status',
        field: 'active',
        suppressMenu: true,
        suppressMovable: true,
        cellRenderer: 'checkboxRenderer',
        editable: false
      },
      {
        headerName: 'Action',
        suppressMenu: true,
        suppressMovable: true,
        cellRenderer: 'actionRenderer',
        cellRendererParams: {
          editingRowIndex, // Pass the currently editing row index to each row
          setEditingRowIndex // Pass the function to set the current editing row index
        }
      }
    ],
    [editingRowIndex] // Rerender if editingRowIndex changes
  );

  const defaultColDef = useMemo(
    () => ({
      sortable: true,
      resizable: true,
      filter: true
    }),
    []
  );

  const resetGrid = () => {
    if (gridApiRef.current) {
      gridApiRef.current.setFilterModel(null); // Reset filters
      gridApiRef.current.setSortModel(null); // Reset sorting
      columnApiRef.current.resetColumnState(); // Reset column state (order, size, visibility)
      gridApiRef.current.deselectAll(); // Deselect all rows
      gridApiRef.current.redrawRows(); // Redraw rows
    }
  };

  const onGridReady = params => {
    gridApiRef.current = params.api;
    columnApiRef.current = params.columnApi;
    console.log('Grid API:', gridApiRef.current);
    console.log('Column API:', columnApiRef.current);
    console.log('Initial row count:', params.api.getDisplayedRowCount());
  };

  useEffect(() => {
    setIsLoading(true);
    roData();
  }, []);
  useEffect(() => {
    console.log('Row Data:', rowData);
  }, [rowData]);
  const roData = () => {
    getRoHideRules(result => {
      let resultArr = [];
      if (
        result.data &&
        result.data.statelessCcPhysicalRwGetRoHideRules &&
        result.data.statelessCcPhysicalRwGetRoHideRules.showHideRoRules
      ) {
        setIsLoading(false);
        console.log(
          'getRoHideRules:',
          result.data.statelessCcPhysicalRwGetRoHideRules.showHideRoRules
        );
        setRowData(
          result.data.statelessCcPhysicalRwGetRoHideRules.showHideRoRules
        );
      }
    });
  };

  const addEmail = () => {
    setShowRo(true);
  };

  const closeEmail = () => {
    setShowRo(false);
  };

  const roHideShow = input => {
    console.log('roHideShow input:', input);
    setIsLoading(true);
    insertRoHideRules(input, result => {
      if (
        result.data.statelessCcPhysicalRwInsertRoHideRules.results &&
        result.data.statelessCcPhysicalRwInsertRoHideRules.results.length > 0 &&
        result.data.statelessCcPhysicalRwInsertRoHideRules.results[0].status !==
          0
      ) {
        alert(
          result.data.statelessCcPhysicalRwInsertRoHideRules.results[0].msg
        );
        roData();
        setShowRo(false);
      }
    });
  };

  return (
    <div>
      <Paper
        square
        style={{
          margin: 8,
          backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
          border:
            Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
          color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
          cursor: 'default'
        }}
      >
        <Tabs
          variant="fullWidth"
          indicatorColor="secondary"
          textColor="secondary"
          aria-label="icon label tabs example"
        >
          <Tab
            style={{
              textTransform: 'none',
              pointerEvents: 'none',
              borderColor: '#e7eef3',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
            }}
            label={<div>RO Show Or Hide</div>}
            value="one"
          />
        </Tabs>
      </Paper>
      <Button
        variant="contained"
        id="reset-layout"
        className={clsx(classes.back, 'reset-btn')}
        onClick={resetGrid}
      >
        <RestoreIcon />
        <Typography variant="body1" align="left">
          Reset Layout
        </Typography>
      </Button>
      <Paper
        square
        style={{
          marginLeft: 8,
          marginRight: 8,
          paddingTop: 9,
          display: 'flex',
          height: '54px',
          justifyContent: 'space-between'
        }}
      >
        <div className={classes.btnContainer}>
          <Button
            variant="contained"
            className={clsx('reset-btn', classes.btnUser)}
            onClick={addEmail}
          >
            Add Ro
          </Button>
        </div>
      </Paper>
      {isLoading && (
        <Box style={{ padding: 25 }}>
          <LinearProgress color="secondary" />
          <Typography
            variant="h6"
            align="center"
            style={{ padding: 25 }}
            color="primary"
          >
            Processing...
          </Typography>
        </Box>
      )}
      <div
        id="data-tab-advisor"
        className="ag-theme-balham"
        style={{
          height: window.innerHeight - 170 + 'px',
          width: '1405px',
          alignContent: 'center',
          marginLeft: '8px',
          marginTop: 15
        }}
      >
        <AgGridReact
          className="ag-theme-balham"
          style={{ width: '100%' }}
          modules={AllModules}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          rowData={rowData}
          onGridReady={onGridReady}
          // suppressClickEdit={true}
          // suppressColumnVirtualisation={true}
          // suppressChangeDetection={true}
          // suppressRowClickSelection={true}
          tooltipShowDelay={0}
          editType="fullRow"
          floatingFilter={true}
          groupDefaultExpanded={-1}
          animateRows={true}
          frameworkComponents={{
            checkboxRenderer: CheckboxRenderer,
            actionRenderer: ActionRenderer
          }}
          context={{ componentParent: this }}
        />
      </div>
      {showRo && (
        <AddRo
          openDialog={showRo}
          handleClose={closeEmail}
          handleSubmitForm={roHideShow}
        />
      )}
    </div>
  );
};

export default GridComponent;
