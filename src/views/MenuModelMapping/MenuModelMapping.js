import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import clsx from 'clsx';
import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback
} from 'react';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Tooltip,
  Paper,
  Grid,
  Dialog,
  CircularProgress
} from '@material-ui/core';
import { useHistory } from 'react-router-dom';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import { AgGridReact } from '@ag-grid-community/react';
import { getMenuModalData, crudMenuModels } from 'src/utils/hasuraServices';
import DeleteIcon from '@material-ui/icons/Delete';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
// import SuccessSnackbar from './SuccessSnackbar';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { makeStyles } from '@material-ui/core/styles';
import Model from 'src/views/Model/Model';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import { setNavItems, setMenuSelected } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { TrendingUpRounded } from '@material-ui/icons';
import { TRUE } from 'sass';
import Page from 'src/components/Page';
const useStyles = makeStyles(theme => ({
  back: {
    marginRight: 30,
    float: 'right',
    marginTop: -20,
    width: 105
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
}));

const MenuModelMapping = () => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const gridRef = useRef();
  const history = useHistory();
  const [expandedItems, setExpandedItems] = useState([]);
  const [gridTypes, setGridTypes] = useState('');
  const [expanded, setExpanded] = useState(0);
  const [leftRowData, setLeftRowData] = useState();
  const [rightRowData, setRightRowData] = useState();
  const [leftApi, setLeftApi] = useState(null);
  const [rightApi, setRightApi] = useState(null);
  const [leftColumnApi, setLeftColumnApi] = useState(null);
  const [loading, setLoading] = useState(true);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [successMsg, setSuccessMsg] = useState('');
  const [tabSelection, setTabSelection] = useState('one');
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = useState('');
  const [openAlert, setOpenAlert] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const session = useSelector(state => state.session);
  const rightColumnDefs = [
    {
      headerName: 'Menu Name',
      field: 'menuName',
      rowGroup: true,
      hide: true
    },
    {
      headerName: 'Make',
      field: 'make',
      hide: true,
      rowGroup: true,
      cellRenderer: params => (params.value ? params.value : '')
    },
    {
      headerName: 'Model',
      field: 'modelName',
      hide: true,
      cellRenderer: params => (params.value ? params.value : '')
    },
    {
      headerName: 'Actions',
      cellRendererFramework: params => {
        const make =
          params && params.node && params.node.data && params.node.data.make;
        if (
          params &&
          params.node &&
          !params.node.group &&
          make !== null &&
          make !== undefined &&
          make !== ''
        ) {
          return (
            <span style={{ display: 'flex', justifyContent: 'space-evenly' }}>
              <Tooltip title="Delete">
                <DeleteIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={'edit' + params.rowIndex}
                  style={{
                    width: 18,
                    left: '8',
                    marginTop: 8,
                    cursor: 'pointer'
                  }}
                  onClick={() => deleteModel(params)}
                />
              </Tooltip>
            </span>
          );
        } else if (
          params &&
          params.node &&
          params.node.field &&
          params.node.field === 'make'
        ) {
          return (
            <span style={{ display: 'flex', justifyContent: 'space-evenly' }}>
              <Tooltip title="Delete">
                <DeleteIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={'edit' + params.rowIndex}
                  style={{
                    width: 18,
                    left: '8',
                    marginTop: 8,
                    cursor: 'pointer'
                  }}
                  onClick={() => deleteMake(params)}
                />
              </Tooltip>
            </span>
          );
        } else {
          return null;
        }
      },
      suppressMenu: true,
      maxWidth: 60,
      suppressColumnsToolPanel: true,
      cellStyle: { border: 'none' },
      suppressNavigable: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      pinned: 'right'
    }
  ];
  useEffect(() => {
    if (history.location.state != null) {
      setTabSelection(history.location.state.tabSelection);
    } else {
      setTabSelection('one');
    }
  }, []);
  const leftColumnDefs = [
    {
      headerName: 'Make',
      field: 'make',
      rowGroup: true,
      hide: true,
      filter: false,
      suppressMoveWithinParent: true
    },
    {
      headerName: 'Model',
      field: 'modelName',
      hide: true,
      filter: false,
      suppressMoveWithinParent: true
    }
  ];

  const autoGroupColumnDef = useMemo(() => {
    return {
      headerName: 'Model',
      field: 'modelName',
      cellRenderer: 'agGroupCellRenderer',
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      cellRendererParams: {
        suppressCount: true,
        filter: false
        // checkbox: true
      },
      tooltipValueGetter: params => {
        if (params.data == undefined) return 'Expand';
      },
      rowDragText: (params, dragItemCount) => {
        let dragItemCounts =
          params.rowNode &&
          params.rowNode.selectionController &&
          params.rowNode.selectionController.selectedNodes;
        let dragItemArr = Object.values(dragItemCounts);
        const filteredArray = dragItemArr.filter(item => item !== undefined);
        const dragItemLen = filteredArray.length;
        if (dragItemLen > 1) {
          return dragItemLen + ' models';
        } else {
          return (
            params &&
            params.rowNode &&
            params.rowNode.data &&
            params.rowNode.data.modelName
          );
        }
      }
    };
  }, []);
  const autoGroupColumnDefright = useMemo(() => {
    return {
      headerName: 'Menu',
      field: 'modelName',
      cellRenderer: 'agGroupCellRenderer',
      // suppressMenu: true,
      // suppressColumnsToolPanel: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      tooltipValueGetter: params => {
        return 'Expand';
      },
      cellRendererParams: {
        suppressCount: true
      },
      menuTabs: []
    };
  }, []);
  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      checkboxSelection: params => {
        var displayedColumns = params.columnApi.getAllDisplayedColumns();
        var thisIsFirstColumn =
          displayedColumns[0].colId === params.column.colId;
        return thisIsFirstColumn;
      },
      rowDrag: params => {
        return !params.node.group;
      },
      tooltipValueGetter: params => {
        return 'Expand';
      },
      minWidth: 100,
      filter: false
    };
  }, []);
  const defaultColDefRight = useMemo(() => {
    return {
      flex: 1,
      minWidth: 100
    };
  }, []);
  useEffect(() => {
    dispatch(setMenuSelected('Model Mapping - \n Menu'));
    dispatch(setNavItems(['Armatus Admin']));
  }, []);
  useEffect(() => {
    if (!leftApi || !rightApi) {
      return;
    }
    const dropZoneParams =
      rightApi && rightApi.getRowDropZoneParams({ onDragStop });
    leftApi && leftApi.removeRowDropZone(dropZoneParams);
    leftApi && leftApi.addRowDropZone(dropZoneParams);
  }, [leftApi, rightApi, onDragStop]);

  useEffect(() => {
    setLoading(true);
    getAgGridData();
  }, [session.storeSelected]);

  const deleteModel = params => {
    let dataArr = [];
    const generatedJSON = {};
    generatedJSON.menuname = params.data && params.data.menuName;
    generatedJSON.make = params.data && params.data.make;
    generatedJSON.model = params.data && params.data.modelName;
    generatedJSON.p_process = 'unmap_model';
    dataArr.push(generatedJSON);
    const pValue = JSON.stringify(dataArr);
    crudMenuModels(pValue, result => {
      if (result.data.statelessCcPhysicalRwCrudMenuModels) {
        if (
          result.data.statelessCcPhysicalRwCrudMenuModels.string == 'Success'
        ) {
          setSuccessMsg('Model Unmapped Successfully!');
          setopenSnackbar(true);
          getAgGridData();
        }
      }
    });
  };
  const deleteMake = params => {
    let dataArr = [];
    const generatedJSON = {};
    generatedJSON.menuname = params.node.parent.key;
    generatedJSON.make = params.node.key;
    generatedJSON.model = '';
    generatedJSON.p_process = 'unmap_make';
    dataArr.push(generatedJSON);
    const pValue = JSON.stringify(dataArr);
    crudMenuModels(pValue, result => {
      if (result.data.statelessCcPhysicalRwCrudMenuModels) {
        if (
          result.data.statelessCcPhysicalRwCrudMenuModels.string == 'Success'
        ) {
          setSuccessMsg('Make Unmapped Successfully!');
          setopenSnackbar(true);
          getAgGridData();
        }
      }
    });
  };
  const getRowId = params => {
    return params && params.data && params.data.id ? params.data.id : null;
  };

  const handleDelete = props => {
    props.node.gridOptions.api.applyTransaction({ remove: [props.node.data] });
  };
  const onGridReady = (params, side) => {
    if (side == 0) {
      setLeftApi(params && params.api);
      setLeftColumnApi(params && params.columnApi);
    }
    if (side == 1) {
      setRightApi(params && params.api);
    }
  };
  const onRowClicked = params => {
    let title = params.node.key;
    let items = expandedItems;
    var rowNodeIndex = params.node.childIndex;
    setExpanded(rowNodeIndex);

    if (params.node.expanded == false) {
      items = items.filter(function(item) {
        return item !== title;
      });

      setExpandedItems(items);
    } else {
      items.unshift(title);
      setExpandedItems(items);
    }
  };
  const onRowClickedGrp = params => {
    if (params.node.group) {
      if (params.node.expanded) {
        params.node.setExpanded(false);
      } else {
        params.node.setExpanded(true);
      }
    }
  };
  const getAgGridData = () => {
    setIsLoading(false);
    getMenuModalData(result => {
      if (result.data.statelessCcPhysicalRwGetMenuModels.getModels) {
        const data = result.data.statelessCcPhysicalRwGetMenuModels.getModels;
        const updatedData = data.map(item => ({
          make: item.make === '' ? null : item.make,
          menuName: item.menuName === '' ? null : item.menuName,
          modelName: item.modelName === '' ? null : item.modelName
        }));
        const filteredGridType = updatedData.filter(item => item.menuName);
        const filteredData = updatedData.filter(item => item.menuName == null);
        const filteredDataGrid = updatedData.filter(
          item => item.menuName != null
        );
        const modifiedRightRowData = replaceNullDataWithMessage(
          filteredDataGrid
        );
        setGridTypes(filteredGridType);
        setLeftRowData(filteredData);
        setRightRowData(modifiedRightRowData);
        setIsLoading(false);
        rightApi && rightApi.hideOverlay();
      }
    });
  };
  const replaceNullDataWithMessage = rowData => {
    return rowData.map(item => ({
      ...item,
      modelName: item.modelName !== null ? item.modelName : 'No data available'
    }));
  };
  const onDragStop = useCallback(
    params => {
      const { nodes, overNode } = params;

      const category =
        (overNode && overNode.key) ||
        (overNode && overNode.parent && overNode.parent.key);
      if (
        category != null &&
        category != '' &&
        category != undefined &&
        overNode &&
        overNode.field == 'menuName'
      ) {
        const pValData =
          leftApi.getSelectedNodes().length > 0
            ? leftApi
                .getSelectedNodes()
                .map(node => generatePValData(node, category, 'map'))
            : generatePValDatanodes(params.nodes[0], category, 'map');
        const pValue = JSON.stringify(pValData);
        if (pValData.length > 0) {
          crudMenuModels(pValue, result => {
            if (result.data.statelessCcPhysicalRwCrudMenuModels) {
              rightApi && rightApi.showLoadingOverlay();
              if (
                result.data.statelessCcPhysicalRwCrudMenuModels.string ==
                'Success'
              ) {
                setSuccessMsg(category + ' Updated Successfully!');
                setopenSnackbar(true);
                setIsLoading(false);
              }
              getAgGridData();
            }
          });
        } else {
          const { node } = params;
          const rowData = node.data;
          const leftApi = gridRef.current.api;

          leftApi.applyTransaction({ remove: [rowData] });
          setOpenAlert(true);
          setAlertMsg('Please Check And Select At Least One Model');
          setAlertType('warning');
        }
      } else {
        const { node } = params;
        const rowData = node.data;
        const leftApi = gridRef.current.api;

        leftApi.applyTransaction({ remove: [rowData] });
        if (params && params.overNode == null) {
          setOpenAlert(true);
          setAlertMsg('Menu is not Available');
          setAlertType('warning');
        } else {
          setOpenAlert(true);
          setAlertMsg('Please Assign to Proper Menu');
          setAlertType('warning');
        }
      }
    },
    [leftApi]
  );
  const generatePValData = (node, menuName, map) => ({
    p_process: map,
    menuname: menuName,
    model: node && node.data && node.data.modelName,
    make: node && node.data && node.data.make
  });
  const generatePValDatanodes = (node, menuName, map) => [
    {
      p_process: map,
      menuname: menuName,
      model: node && node.data && node.data.modelName,
      make: node && node.data && node.data.make
    }
  ];
  var rowDrag = function(params) {
    return params.node.group;
  };

  const handleSelectionChanged = (params, gridId) => {
    const selectedNodes = leftApi.getSelectedNodes();
    const uniqueMakes = new Set();
    selectedNodes.forEach(node => {
      if (node.data && node.data.make) {
        uniqueMakes.add(node.data.make);
      }
    });
    uniqueMakes.forEach(make => {
      leftApi.forEachNode(node => {
        if (node.group && node.field === 'make' && node.key === make) {
          const isExpanded = node.expanded;
          if (
            selectedNodes.includes(node) ||
            hasSelectedChildren(node, selectedNodes)
          ) {
            leftApi.setRowNodeExpanded(node, true);
          }
        }
      });
    });
  };

  const hasSelectedChildren = (parentNode, selectedNodes) => {
    let hasSelectedChild = false;
    leftApi.forEachNode(childNode => {
      if (
        childNode.parent === parentNode &&
        selectedNodes.includes(childNode)
      ) {
        hasSelectedChild = true;
      }
    });
    return hasSelectedChild;
  };

  const onRowDragMove = params => {
    var movingNode = params && params.node;
    var overNode = params && params.overNode;
    if (
      movingNode.gridOptionsWrapper &&
      movingNode.gridOptionsWrapper.gridOptionsWrapper &&
      movingNode.gridOptionsWrapper.gridOptionsWrapper.gridApi ===
        overNode.gridOptionsWrapper &&
      overNode.gridOptionsWrapper.gridOptionsWrapper &&
      overNode.gridOptionsWrapper.gridOptionsWrapper.gridApi
    ) {
      params.api.clearRowDragStyle();
      return false;
    } else {
      var groupCountry;
      if (overNode && overNode.group) {
        groupCountry = overNode && overNode.key;
      } else {
        groupCountry = overNode && overNode.data && overNode.data.make;
      }
      var needToChangeParent =
        movingNode && movingNode.data && movingNode.data.make !== groupCountry;
      if (needToChangeParent && groupCountry != undefined) {
        rightApi && rightApi.showLoadingOverlay();
        onDragStop(params);
        console.log('onRowDragMove called.................needToChangeParent');
      } else {
        console.log('No need to change parent..........');
      }
    }
  };
  const hidesnackbar = () => {
    setopenSnackbar(false);
  };

  const getGridWrapper = id => (
    <div className="ag-theme-alpine" style={{ height: '78vh', width: '400px' }}>
      <AgGridReact
        className="ag-theme-balham"
        style={{
          height: '100%',
          width: '100%'
        }}
        defaultColDef={id == 0 ? defaultColDef : defaultColDefRight}
        getRowId={getRowId}
        rowDragEntireRow={true}
        onGridReady={params => onGridReady(params, id)}
        ref={gridRef}
        autoGroupColumnDef={
          id == 0 ? autoGroupColumnDef : autoGroupColumnDefright
        }
        rowSelection={id === 0 ? 'multiple' : undefined}
        rowData={id === 0 ? leftRowData : rightRowData}
        columnDefs={id === 0 ? leftColumnDefs : rightColumnDefs}
        rowDragManaged={true}
        rowDragMultiRow={true}
        suppressRowClickSelection={id === 0}
        suppressMoveWhenRowDragging={id === 0}
        groupSelectsChildren={id === 0 ? true : false}
        onRowDragEnd={params => onRowDragMove(params)}
        onSelectionChanged={params => handleSelectionChanged(params, id)}
        suppressContextMenu={true}
      />
    </div>
  );
  const handleTabChange = (event, newValue) => {
    setTabSelection(newValue);
  };
  return (
    <>
      {isLoading ? (
        <Grid
          container
          justify="center"
          alignItems="center"
          style={{ height: '70vh' }}
        >
          <CircularProgress size={60} />
        </Grid>
      ) : (
        <div>
          <Page className={classes.root} title="Model Mapping - Menu">
            <Paper square style={{ margin: 8 }}>
              <Tabs
                value={tabSelection}
                onChange={handleTabChange}
                variant="fullWidth"
                indicatorColor="secondary"
                textColor="secondary"
                showrefresh
                aria-label="icon label tabs example"
                TabIndicatorProps={{ style: { display: 'none' } }}
              >
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Model Mapping - Menu</div>}
                  value="one"
                  className={tabSelection == 'one' ? classes.tabSelected : null}
                />
              </Tabs>
            </Paper>
            {tabSelection == 'one' ? (
              <>
                <Grid container spacing={2} style={{ padding: 8 }}>
                  <Grid item>{getGridWrapper(0)}</Grid>

                  <Grid item>{getGridWrapper(1)}</Grid>
                </Grid>
              </>
            ) : (
              {
                /* <Model /> */
              }
            )}
            {successMsg && (
              <SuccessSnackbar
                onClose={hidesnackbar}
                open={openSnackbar}
                msg={successMsg}
              />
            )}
            <Dialog open={openAlert}>
              <Alert
                severity={alertType == 'warning' ? 'warning' : 'success'}
                action={
                  <IconButton
                    aria-label="close"
                    color="inherit"
                    size="small"
                    onClick={() => {
                      setOpenAlert(false);
                    }}
                  >
                    <CloseIcon fontSize="inherit" />
                  </IconButton>
                }
                style={{ margin: '10px 20px' }}
              >
                {alertMsg}
              </Alert>
            </Dialog>
          </Page>
        </div>
      )}
    </>
  );
};

export default MenuModelMapping;
