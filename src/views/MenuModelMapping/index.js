import React from 'react';
import Page from 'src/components/Page';
import MenuModelMapping from './MenuModelMapping';

import { Redirect } from 'react-router-dom';

import { withKeycloak } from '@react-keycloak/web';

const MenuModel = props => {
  return (
    <Page title="RO Count-Calendar">
      {props.keycloak.realmAccess.roles.includes('client') ||
      props.keycloak.realmAccess.roles.includes('user') ||
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <MenuModelMapping />
      )}
    </Page>
  );
};

export default withK<PERSON><PERSON>loak(MenuModel);
