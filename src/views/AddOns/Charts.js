import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import DashboardActions from 'src/components/DashboardActions';
import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import {
  getDataGridConfiguration,
  getLayoutConfiguration,
  saveLayoutConfiguration,
  getComparisonMonths
} from '../../utils/Utils';
import { Paper } from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ComparisonChartsGrid from './ServiceAdvisor/ComparisonChartsGrid';
import { withKeycloak } from '@react-keycloak/web';
import { withStyles } from '@material-ui/styles';

const ReactGridLayout = WidthProvider(RGL);
var lodash = require('lodash');

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 35,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      tabSelection: props.history.location.state
        ? props.history.location.state.tabSelection
        : 'one',
      filters: 2,
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-15') || {}
        )
      ),
      chartList: JSON.parse(global.localStorage.getItem('chart-master'))
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }

  setFilterCharts = value => {
    if (value) {
      this.setState({
        filters: value
      });
    }
    return this.state.filters;
  };

  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }
    return this.state.layout;
  };
  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-15');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }
  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  removeFav = value => {
    this.setState({
      isLoading: false
    });
  };
  handleClosePopup = value => {
    this.setState({
      popupChartId: '',
      open: false
    });
  };
  render() {
    let filteredResult = this.state.chartList.filter(
      item => item.dbdName == 'AddOns' && item.parentId == null
    );
    let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    let realm = this.props.keycloak.realm;
    let mon1 = getComparisonMonths()[0];
    let mon2 = getComparisonMonths()[1];
    const { classes } = this.props;
    return (
      <div>
        <DashboardActions
          resetDashboard={this.setResetDashboard}
          filterCharts={this.setFilterCharts}
          setTitle="Add On"
          noFilters={true}
        ></DashboardActions>
        <div>
          <Paper square style={{ margin: 8, marginTop: '20px' }}>
            <Tabs
              variant="fullWidth"
              scrollButtons="auto"
              value={this.state.tabSelection}
              onChange={this.handleTabChange}
              indicatorColor="secondary"
              textColor="secondary"
              TabIndicatorProps={{ style: { display: 'none' } }}
            >
              <Tab
                style={{ textTransform: 'none' }}
                label={<div>13 Month Trend</div>}
                value="one"
                className={
                  this.state.tabSelection == 'one' ? classes.tabSelected : null
                }
              />
              <Tab
                style={{ textTransform: 'none' }}
                label={<div>Service Advisor Performance</div>}
                value="two"
                className={
                  this.state.tabSelection == 'two' ? classes.tabSelected : null
                }
              />
            </Tabs>
          </Paper>
          {this.state.tabSelection == 'two' ? (
            <ComparisonChartsGrid
              mon1={mon1}
              mon2={mon2}
              removeFav={this.removeFav}
            />
          ) : null}
        </div>
        {this.state.tabSelection == 'one' ? (
          <ReactGridLayout
            {...this.props}
            layout={this.state.layout}
            onLayoutChange={this.onLayoutChange}
            isResizable={false}
          >
            {orderedData.map((item, index) => {
              return (
                <div
                  style={{ backgroundColor: '#FFF' }}
                  key={index}
                  data-grid={getDataGridConfiguration(index)}
                >
                  <DashboardBarRenderer
                    handleClosePopup={this.handleClosePopup}
                    chartId={Number(item.chartId)}
                    isFrom={item.dbdName}
                    removeFav={this.removeFav}
                  />
                </div>
              );
            })}
          </ReactGridLayout>
        ) : null}
      </div>
    );
  }
}

const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
});
export default withKeycloak(withStyles(styles)(Charts));
