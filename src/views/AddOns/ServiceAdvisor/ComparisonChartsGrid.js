import {
  Grid,
  Paper,
  FormControl,
  MenuItem,
  Select,
  InputLabel
} from '@material-ui/core';
import React, { useState, useEffect } from 'react';
import ColumnRenderer from './ColumnRenderer';
import { makeStyles } from '@material-ui/core/styles';
import moment from 'moment';
//import ServiceAdvisor from '../ServiceAdvisor';
import ServiceAdvisor from '../../AnalyzeData/ComparisonCharts/ColumnRenderer';
import { getLast13Months } from 'src/utils/Utils';
var lodash = require('lodash');

const useStyles = makeStyles({
  formControl: {
    padding: 8
  }
});
const ComparisonChartsGrid = ({ mon1, mon2, parentCallback, removeFav }) => {
  const classes = useStyles();
  const [queryMonth1, setQueryMonth1] = useState(mon1);
  const [queryMonth2, setQueryMonth2] = useState(mon2);
  const [charts] = useState([
    'addonvsnonaddonrevenue%',
    'addonrevenue',
    'addonjobcountsa',
    'addonlbrrevenuesa',
    'addonprtsrevenuesa'
  ]);

  const handleMonthchange1 = event => {
    setQueryMonth1(event.target.value);
  };
  const handleMonthchange2 = event => {
    setQueryMonth2(event.target.value);
  };

  var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  let filteredResult = chartList.filter(
    item =>
      item.dbdName == 'AddOns-Month Comparison' && item.parentId == item.chartId
  );
  let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
  const handleClosePopup = value => {};
  return (
    <Grid container style={{ padding: '5px' }}>
      <Grid container>
        <Grid
          item
          xs={6}
          display="inline"
          justify="flex-start"
          style={{ padding: '5px', maxWidth: '100%', flexBasis: '100%' }}
        >
          <Paper square>
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                Month1
              </InputLabel>

              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth1}
                onChange={handleMonthchange1}
              >
                {getLast13Months().map(val => (
                  <MenuItem value={val}>
                    {moment(val).format('MMM-YY')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
              style={{ marginLeft: '17px' }}
            >
              <InputLabel htmlFor="outlined-age-native-simple" margin="dense">
                Month2
              </InputLabel>
              <Select
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={queryMonth2}
                onChange={handleMonthchange2}
              >
                {getLast13Months().map(val => (
                  <MenuItem value={val}>
                    {moment(val).format('MMM-YY')}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Paper>
        </Grid>
      </Grid>

      <Grid container style={{ padding: '5px' }} spacing={12}>
        {charts.map((val, index) => {
          if (val === 'addonvsnonaddonrevenue%' || val === 'addonrevenue') {
            return (
              <Grid
                item
                xs={12}
                justify="flex-start"
                style={{ padding: '5px' }}
              >
                <ServiceAdvisor
                  isPartsCharts={false}
                  removeFav={removeFav}
                  datatype={val}
                  handleClosePopup={handleClosePopup}
                  month2={queryMonth1}
                  month1={queryMonth2}
                  parentCallback={parentCallback}
                  chartId={orderedData[index].chartId}
                />
              </Grid>
            );
          } else {
            return (
              <Grid item xs={6} justify="flex-start" style={{ padding: '5px' }}>
                <ColumnRenderer
                  datatype={val}
                  month1={queryMonth1}
                  handleClosePopup={handleClosePopup}
                  month2={queryMonth2}
                  chartId={orderedData[index].chartId}
                  parentCallback={parentCallback}
                  removeFav={removeFav}
                />
              </Grid>
            );
          }
        })}
      </Grid>
    </Grid>
  );
};

export default ComparisonChartsGrid;
