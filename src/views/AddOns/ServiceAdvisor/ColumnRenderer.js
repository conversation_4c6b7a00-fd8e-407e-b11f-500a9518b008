import {
  CircularProgress,
  Grid,
  Paper,
  Card,
  CardContent,
  CardHeader,
  Divider
} from '@material-ui/core';
import React, { useEffect, useState } from 'react';
import { getAddOnServiceAdvisorDetails } from 'src/utils/hasuraServices';
import {
  getChartName,
  getChartDrillDown,
  getSubHeader
} from 'src/components/ViewGraphDetailsAction';
import { getColorScheme, getComparisonMonths } from 'src/utils/Utils';
import ReactApexChart from 'react-apexcharts';
import { useHistory } from 'react-router';
import MoreActions from '../../../components/MoreActions';
import PropTypes from 'prop-types';
import moment from 'moment';
import ChartDialog from 'src/components/Dialog';

var lodash = require('lodash');

const ColumnRenderer = ({
  datatype,
  removeFav,
  month1,
  type,
  handleClosePopup,
  month2,
  chartId,
  isFrom,

  parentCallback
}) => {
  const [isLoading, setLoading] = useState(true);
  const [open, setOpen] = useState(false);
  const [chartoptions, setchartOptions] = useState({});
  const [series, setSeries] = useState([]);
  const history = useHistory();
  useEffect(() => {
    setLoading(true);
    getDataforColumnChart();
  }, [month1, month2, datatype]);

  const removeFavourite = val => {
    removeFav(val);
  };
  const chartPopup = val => {
    // handleChartPopup(val);
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
    handleClosePopup(1);
  };
  if (typeof chartId == 'undefined') {
    chartId = history.location.search
      .split('?chartId=')
      .pop()
      .split('?')[0];
  }
  if (datatype == '') {
    month1 = month1 ? month1 : getComparisonMonths()[0];
    month2 = month2 ? month2 : getComparisonMonths()[1];
    datatype = getDataType(Number(chartId));
  }
  const getDataforColumnChart = () => {
    if (datatype != 'addonvsnonaddonrevenue%' && datatype != 'addonrevenue') {
      getAddOnServiceAdvisorDetails(month1, month2, result => {
        if (
          result.data.statelessDbdAddonsGetChartsAddonsAllServiceadvisors.nodes
        ) {
          processData(
            result.data.statelessDbdAddonsGetChartsAddonsAllServiceadvisors
              .nodes,
            datatype
          );
        } else {
          setLoading(false);
        }
      });
    }
  };

  function getDataType(chartId) {
    var datatype = '';
    switch (chartId) {
      case 1118:
        datatype = 'addonjobcountsa';
        break;
      case 1119:
        datatype = 'addonlbrrevenuesa';
        break;
      case 1120:
        datatype = 'addonprtsrevenuesa';
        break;
    }
    return datatype;
  }

  const setactions = val => {
    if (val === 4) {
      history.push({
        pathname: '/GraphDetailsView',
        search: '?chartId=' + chartId,
        state: {
          month1: month1,
          month2: month2,
          datatype: datatype,
          tabSelection: 'two'
        },
        SelectedLocation: window.location.pathname
      });
    }
  };
  const processData = (data, dataType) => {
    data = lodash.orderBy(data, ['serviceadvisor'], ['desc']);
    data = lodash.orderBy(data, 'monthYear', 'desc');
    var lodashMonth = data[0].monthYear;
    var seriesData = [];
    var dataNonZeroArr = [];
    var dataZeroArr = [];
    var commonArr = [];
    if (dataType == 'addonjobcountsa') {
      data.map(item => {
        if (
          item.addonjobcount != 0 &&
          (item.monthYear == month1 || item.monthYear == month2)
        ) {
          dataNonZeroArr.push(item);
        }
        if (
          item.addonjobcount == 0 &&
          (item.monthYear == month1 || item.monthYear == month2)
        ) {
          dataZeroArr.push(item);
        }
      });
      dataNonZeroArr.map(item => {
        dataZeroArr.map(item1 => {
          if (item.serviceadvisor == item1.serviceadvisor) {
            commonArr.push(item1);
          }
        });
      });
      var resultentArr = dataNonZeroArr.concat(commonArr);
    } else if (dataType == 'addonlbrrevenuesa') {
      data.map(item => {
        if (
          item.addonlbrsale != 0 &&
          (item.monthYear == month1 || item.monthYear == month2)
        ) {
          dataNonZeroArr.push(item);
        }
        if (
          item.addonlbrsale == 0 &&
          (item.monthYear == month1 || item.monthYear == month2)
        ) {
          dataZeroArr.push(item);
        }
      });
      dataNonZeroArr.map(item => {
        dataZeroArr.map(item1 => {
          if (item.serviceadvisor == item1.serviceadvisor) {
            commonArr.push(item1);
          }
        });
      });
      var resultentArr = dataNonZeroArr.concat(commonArr);
    } else if (dataType == 'addonprtsrevenuesa') {
      data.map(item => {
        if (
          item.addonpartsrevenue != 0 &&
          (item.monthYear == month1 || item.monthYear == month2)
        ) {
          dataNonZeroArr.push(item);
        }
        if (
          item.addonpartsrevenue == 0 &&
          (item.monthYear == month1 || item.monthYear == month2)
        ) {
          dataZeroArr.push(item);
        }
      });
      dataNonZeroArr.map(item => {
        dataZeroArr.map(item1 => {
          if (item.serviceadvisor == item1.serviceadvisor) {
            commonArr.push(item1);
          }
        });
      });
      var resultentArr = dataNonZeroArr.concat(commonArr);
    }
    if (dataType == 'addonjobcountsa') {
      const result = lodash
        .chain(resultentArr)
        .orderBy('serviceadvisor')
        .groupBy('monthYear')
        .map(e => e.map(data => parseFloat(data.addonjobcount)))
        .value();

      lodashMonth != month1
        ? (seriesData = [
            { name: moment(month1).format('MMM-YY'), data: result[1] },
            { name: moment(month2).format('MMM-YY'), data: result[0] }
          ])
        : (seriesData = [
            { name: moment(month1).format('MMM-YY'), data: result[0] },
            { name: moment(month2).format('MMM-YY'), data: result[1] }
          ]);
      var serviceadvisor = lodash
        .chain(resultentArr)
        .uniqBy('serviceadvisor')
        .orderBy('serviceadvisor')
        .map(e => e.serviceadvisor)
        .value();
    } else if (dataType == 'addonlbrrevenuesa') {
      const result = lodash
        .chain(resultentArr)
        .orderBy('serviceadvisor')
        .groupBy('monthYear')
        .map(e => e.map(data => parseFloat(data.addonlbrsale)))
        .value();
      lodashMonth != month1
        ? (seriesData = [
            { name: moment(month1).format('MMM-YY'), data: result[1] },
            { name: moment(month2).format('MMM-YY'), data: result[0] }
          ])
        : (seriesData = [
            { name: moment(month1).format('MMM-YY'), data: result[0] },
            { name: moment(month2).format('MMM-YY'), data: result[1] }
          ]);
      var serviceadvisor = lodash
        .chain(resultentArr)
        .uniqBy('serviceadvisor')
        .orderBy('serviceadvisor')
        .map(e => e.serviceadvisor)
        .value();
    } else if (dataType == 'addonprtsrevenuesa') {
      const result = lodash
        .chain(resultentArr)
        .orderBy('serviceadvisor')
        .groupBy('monthYear')
        .map(e => e.map(data => parseFloat(data.addonpartsrevenue)))
        .value();
      lodashMonth != month1
        ? (seriesData = [
            { name: moment(month1).format('MMM-YY'), data: result[1] },
            { name: moment(month2).format('MMM-YY'), data: result[0] }
          ])
        : (seriesData = [
            { name: moment(month1).format('MMM-YY'), data: result[0] },
            { name: moment(month2).format('MMM-YY'), data: result[1] }
          ]);
      var serviceadvisor = lodash
        .chain(resultentArr)
        .uniqBy('serviceadvisor')
        .orderBy('serviceadvisor')
        .map(e => e.serviceadvisor)
        .value();
    }

    const options = {
      plotOptions: {
        colors: getColorScheme(1),

        bar: {
          horizontal: false,
          columnWidth: '55%',
          endingShape: 'flat'
        }
      },
      chart: {
        fontFamily: 'Roboto,Helvetica, Arial, sans-serif',
        toolbar: {
          show: false,
          autoSelected: 'zoom'
        },
        events: {
          dataPointSelection: function(event, chartContext, config) {
            var selectedIndex = config.dataPointIndex;
            var seriesIndex = config.seriesIndex;
            var selectedAdvisor =
              chartContext.w.config.xaxis.categories[selectedIndex];
            let monthYear = chartContext.w.config.series[
              seriesIndex
            ].name.split('-');
            let date = moment(monthYear[1], 'YY');
            let year = date.format('YYYY');
            let month = moment()
              .month(monthYear[0])
              .format('MM');
            var selectedMonth = year + '-' + month;
            //var selectedMonth = chartContext.w.config.series[seriesIndex].name;
            history.push({
              pathname: '/AnalyzeData',
              prevPath:
                window.location.pathname == '/GraphDetailsView'
                  ? window.location.pathname + '?chartId=' + chartId
                  : window.location.pathname,
              state: {
                chartId: chartId,
                x: selectedAdvisor,
                y: selectedMonth,
                drillDown: getChartDrillDown(Number(chartId), 'addOn'),
                chartName: getChartName(chartId),
                tabSelection:
                  window.location.pathname == '/GraphDetailsView' ? '' : 'two'
                // category: getChartDataIndex(chartId, dataIndex, 'addOn'),
              }
            });
          }
        }
      },
      dataLabels: {
        enabled: false
      },
      title: {
        //  text: getGraphName(datatype),
        align: 'left'
      },
      grid: {
        row: {
          colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
          opacity: 0.5
        }
      },
      theme: {
        palette: 'palette8' // upto palette10
      },
      colors: ['rgb(51,102,204)', 'rgb(220,57,18)'],
      yaxis: {
        labels: {
          formatter: function(value) {
            return dataType == 'addonjobcountsa'
              ? value
              : '$' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          }
        }
      },
      xaxis: {
        categories: serviceadvisor.length > 0 ? serviceadvisor : [0, 1]
        // categories:lodash.chain(data).uniqBy('serviceadvisor').orderBy('serviceadvisor').map(e=>e.serviceadvisor).value(),
      },
      tooltip: {
        shared: false,
        intersect: true
      }
    };

    setchartOptions(options);
    setSeries(seriesData);
    setTimeout(() => {
      setLoading(false);
    }, 200);
  };

  return (
    <Paper square style={{ margin: 0 }}>
      {/* {isLoading == true ? ( */}
        <Grid
          justify="center"
          style={{
            height: 300,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </Grid>
      {/* ) : (
        <Card
          bordered={false}
          style={{
            height: '100%',
            textAlign: 'left',
            borderRadius: 0,
            border: '1px solid #003d6b'
          }}
        >
          <CardHeader
            title={getChartName(chartId)}
            action={
              <MoreActions
                removeFavourite={removeFavourite}
                setActions={setactions}
                chartId={chartId}
                month1={month1}
                month2={month2}
                type={type}
                chartPopup={chartPopup}
                handleClose={handleClose}
                // favoritesDisabled={true}
              ></MoreActions>
            }
            subheader={getSubHeader(chartId)}
            style={{ borderBottom: '1px solid #003d6b' }}
          ></CardHeader>

          <Divider />
          <CardContent>
            <ReactApexChart
              options={chartoptions}
              series={series}
              type="bar"
              height={
                isFrom == 'source_page'
                  ? window.location.pathname == '/MyFavorites'
                    ? 255
                    : 215
                  : window.location.pathname == '/GraphDetailsView'
                  ? 200
                  : 280
              }
            />
          </CardContent>
        </Card>
      )} */}
      <ChartDialog
        open={open}
        chartId={chartId}
        mon1={month1}
        mon2={month2}
        datatype={datatype}
        chartType="addonComparison"
        realm={localStorage.getItem('realm')}
        handlePopupClose={handleClose}
      />
    </Paper>
  );
};
ColumnRenderer.propTypes = {
  removeFav: PropTypes.func
};
export default ColumnRenderer;
