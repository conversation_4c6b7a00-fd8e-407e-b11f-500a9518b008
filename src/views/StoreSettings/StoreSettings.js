/* eslint-disable react/default-props-match-prop-types */
/* eslint-disable react/jsx-no-duplicate-props */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-shadow */
/* eslint-disable react/static-property-placement */
/* eslint-disable no-unused-vars */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import { UPDATE_KPI_SCORECARD_GOAL } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgres';
import { getTimeZone } from 'src/utils/Utils';
import {
  Box,
  Divider,
  fade,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Checkbox,
  TextField,
  Snackbar
} from '@material-ui/core';

import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
// eslint-disable-next-line import/no-extraneous-dependencies
import $ from 'jquery';
import React from 'react';
import Page from 'src/components/Page';
import 'react-grid-layout/css/styles.css';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import {
  getUpdateStoreSettings,
  getStoreNickName
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import PropTypes from 'prop-types';
import { SET_REFRESH_STATUS } from 'src/actions';
import { connect } from 'react-redux';
import { setRefreshStatus } from '../../actions';
import { withStyles } from '@material-ui/styles';
import { TimePicker } from '@material-ui/pickers';
import { red } from '@material-ui/core/colors';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import TimezoneSelect from 'react-timezone-select';
import CustomHeaderGroup from 'src/components/CustomHeaderGroup';
import { logDOM } from '@testing-library/react';
import { ReactSession } from 'react-client-session';
import FormHelperText from '@material-ui/core/FormHelperText';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class StoreSetting extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ storeNickName: '' });
        this.setState({ storeNickNameStored: '' });
        this.setState({ storeFOPCMonthlyFee: 0 });
        this.setState({ storeDMSMonthlyFee: 0 });
        this.getAgGridData();
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }

  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    // let buttonClicked = false;
    this.state = {
      openWarn: false,
      timezone: '',
      rawGridApi: {},
      personName: [],
      items: [],
      prevItem: [],
      value: '',
      selected: [],
      gridApi: {},
      checked: false,
      emailValue: '',
      open: false,
      selectedTimezone: [],
      storeNickName: '',
      storeNickNameStored: '',
      storeFOPCMonthlyFee: 0,
      storeDMSMonthlyFee: 0,
      storeFOPCMonthlyFeeStored: 0,
      storeDMSMonthlyFeeStored: 0,
      isEdit: false,
      isInvalid: false,
      columnDefs: [
        {
          field: '',
          width: 36,
          suppressCellFlash: true,
          lockPosition: true,
          chartDataType: 'category',
          headerCheckboxSelection:
            this.props.keycloak.realmAccess.roles.includes('superadmin') ===
            true
              ? true
              : false,
          headerCheckboxSelectionFilteredOnly: true,
          checkboxSelection: true,
          cellStyle: params => {
            return this.props.keycloak.realmAccess.roles.includes(
              'superadmin'
            ) === false
              ? { 'pointer-events': 'none', opacity: '0.4' }
              : { cursor: 'pointer' };
          },
          // floatingFilterComponent: 'customHeaderGroupComponent',
          suppressMenu: true
          // cellStyle: function() {
          //   return {
          //     cursor: 'pointer'
          //   };
          // }
        },
        {
          headerName: 'Working Day',
          chartDataType: 'series',
          field: 'keyname',
          editable: false,
          width: 120,
          // flex:1,

          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          suppressFilter: true,
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white',
              fontSize: '14px'
            };
          }
        },

        {
          headerName: 'Select Type',
          field: 'keyvalue',
          width: 130,
          //
          // resizable: false,
          editable:
            this.props.keycloak.realmAccess.roles.includes('superadmin') ===
            true
              ? true
              : false,
          suppressMenu: true,
          singleClickEdit: true,
          cellEditor: 'agSelectCellEditor',
          cellEditorParams: {
            values: DayType
          },
          valueFormatter: params => {
            return lookupValue(DayTypeMapping, params.value);
          },
          filter: 'agSetColumnFilter',
          refData: DayTypeMapping,
          unSortIcon: true,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'left'
            };
          }
        },
        {
          field: 'active',
          width: 36,
          suppressMenu: true,
          hide: true,
          cellStyle: function() {
            return {
              cursor: 'pointer'
            };
          }
        }
      ],
      frameworkComponents: {
        customHeaderGroupComponent: CustomHeaderGroup
      },

      rowData: [],
      rowSelection: 'multiple',
      defaultColDef: {
        enableValue: true,
        enableFilter: false,
        // sortable: true,
        suppressMovable: true
      }
    };
  }

  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };
  onSelectionChanged = e => {
    var rowCount = this.state.rawGridApi.getSelectedRows();
    if (JSON.stringify(rowCount) != JSON.stringify(this.state.selectedData)) {
      this.setState({ isEdit: true });
      this.setState({ openWarn: false });
    }
  };

  onCellValueChanged = e => {
    var rowCount = this.state.rawGridApi.getSelectedRows();

    if (e.node.selected == true) {
      this.setState({ isEdit: true });
      this.setState({ openWarn: false });
    }
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };
  getAgGridData() {
    this.setState({ storeFOPCMonthlyFeeStored: 0 });
    this.setState({ storeDMSMonthlyFeeStored: 0 });
    getStoreNickName(result => {
      if (
        result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes &&
        result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0] !=
          null
      ) {
        console.log(
          'dddddddddd=======',
          result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0]
        );
        var nickName =
          result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0]
            .storeNickname;
        var fopcMonthlyFee =
          result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0]
            .fopcMonthlyFee;
        var dmsMonthlyFee =
          result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0]
            .dmsMonthlyFee;
        this.setState({ storeNickName: nickName });
        this.setState({ storeNickNameStored: nickName });

        this.setState({ storeFOPCMonthlyFee: fopcMonthlyFee });
        this.setState({ storeDMSMonthlyFee: dmsMonthlyFee });

        this.setState({ storeFOPCMonthlyFeeStored: fopcMonthlyFee });
        this.setState({ storeDMSMonthlyFeeStored: dmsMonthlyFee });
      } else {
        this.setState({ storeNickName: '' });
        this.setState({ storeNickNameStored: '' });
        this.setState({ storeFOPCMonthlyFee: 0 });
        this.setState({ storeDMSMonthlyFee: 0 });

        this.setState({ storeFOPCMonthlyFeeStored: 0 });
        this.setState({ storeDMSMonthlyFeeStored: 0 });
      }
    });
    getUpdateStoreSettings(
      'get',
      null,
      'timezone',
      this.state.timezone,
      localStorage.getItem('userID'),
      this.state.storeNickName,
      this.state.storeDMSMonthlyFee,
      this.state.storeFOPCMonthlyFee,
      result => {
        if (
          result.data.statelessCcPhysicalRwGetorsetStoreSettings
            .statelessCcPhysicalRwStoreSettings
        ) {
          var timezone =
            result.data.statelessCcPhysicalRwGetorsetStoreSettings
              .statelessCcPhysicalRwStoreSettings[0].keyvalue;
          this.setState({ selectedTimezone: timezone.split('(')[0] });
          this.setState({ timezone: timezone });
          this.setState({ timezonePrev: timezone.split('(')[0] });
        }
      }
    );
    getUpdateStoreSettings(
      'get',
      null,
      'working_days',
      null,
      localStorage.getItem('userID'),
      this.state.storeNickName,
      this.state.storeDMSMonthlyFee,
      this.state.storeFOPCMonthlyFee,
      result => {
        if (
          result.data.statelessCcPhysicalRwGetorsetStoreSettings
            .statelessCcPhysicalRwStoreSettings
        ) {
          const resultData =
            result.data.statelessCcPhysicalRwGetorsetStoreSettings
              .statelessCcPhysicalRwStoreSettings;
          //sort weekdays
          const map = {
            Sunday: 1,
            Monday: 2,
            Tuesday: 3,
            Wednesday: 4,
            Thursday: 5,
            Friday: 6,
            Saturday: 7
          };
          resultData.sort((a, b) => {
            return map[a.keyname] - map[b.keyname];
          });
          const nonWorkingDays = resultData.map(x =>
            x.keyvalue == '0' ? { ...x, keyvalue: '1' } : x
          );
          this.setState({ rowData: nonWorkingDays });
          this.setState({ rowDataPrev: nonWorkingDays });
        } else {
          this.setState({ rowData: createRowData() });
        }

        this.state.rawGridApi.forEachNode(node =>
          node.setSelected(!!node.data && node.data.active > 0)
        );
        this.setState({
          selectedData: this.state.rawGridApi.getSelectedRows()
        });
      }
    );
  }
  getDifference(array1, array2) {
    return array1.filter(object1 => {
      return !array2.some(object2 => {
        return object1.keyname === object2.keyname;
      });
    });
  }
  handleStoreSettingsSave = e => {
    let selectedRows;
    var rowDataFiltered = [];

    selectedRows = this.state.rawGridApi.getSelectedRows();

    selectedRows = selectedRows.map(x =>
      x.active == 0 ? { ...x, active: 1 } : x
    );
    let filteredRow = this.getDifference(this.state.rowData, selectedRows);
    rowDataFiltered = filteredRow.map(value => ({
      keyname: value.keyname,
      keyvalue: 0,
      active: 0
    }));
    console.log('wwwwwww=======', this.state.isEdit);
    if (
      this.state.isEdit === true ||
      JSON.stringify(this.state.timezonePrev) !=
        JSON.stringify(this.state.selectedTimezone)
    ) {
      console.log('wwwwwww=======1', this.state.isEdit);
      if (selectedRows.length > 0 && this.state.timezone != undefined) {
        console.log('wwwwwww=======2', this.state.isEdit);
        // timezone save
        getUpdateStoreSettings(
          'set',
          null,
          'timezone',
          this.state.timezone,
          localStorage.getItem('userID'),
          this.state.storeNickName,
          this.state.storeDMSMonthlyFee,
          this.state.storeFOPCMonthlyFee,
          result => {
            if (
              result.data.statelessCcPhysicalRwGetorsetStoreSettings
                .statelessCcPhysicalRwStoreSettings
            ) {
            }
          }
        );
        //working day save
        getUpdateStoreSettings(
          'set',
          JSON.stringify(selectedRows.concat(rowDataFiltered)),
          'working_days',
          null,
          localStorage.getItem('userID'),
          this.state.storeNickName
            ? this.state.storeNickName.trim()
            : this.state.storeNickName,
          this.state.storeDMSMonthlyFee,
          this.state.storeFOPCMonthlyFee,
          result => {
            if (
              result.data.statelessCcPhysicalRwGetorsetStoreSettings
                .statelessCcPhysicalRwStoreSettings
            ) {
              this.setState({ isEdit: false });
              this.setState({ open: true });
              this.setState({ isInvalidDms: false });
              this.setState({ isInvalidFopc: false });
              this.getAgGridData();
            }
          }
        );
      }
    } else {
      this.setState({ openWarn: true });
      console.log('wwwwwww========3', this.state.isEdit);
    }
  };
  onChangeTimeZone = e => {
    this.setState({ timezone: e.value + '' + e.label });
    this.setState({ selectedTimezone: e.value });
  };
  hidesnackbar = () => {
    this.setState({ open: false });
  };
  hidesnackbarWarn = () => {
    this.setState({ openWarn: false });
  };
  // handleFormChange = event => {
  //   const value = event.target.value.trim();
  //   console.log('yyyyyy---', value);
  //   console.log('yyyyyy---88888888', this.state.storeNickNameStored);
  //   const regex = /^[a-zA-Z0-9 ]*$/;

  //   if (!regex.test(value)) {
  //     // this.setState({ isInvalid: true });
  //     return;
  //   }

  //   if (value.length >= 26) {
  //     this.setState({ isInvalid: true });
  //     // this.setState({ storeNickName: '' });
  //   } else if (this.state.storeNickNameStored == value) {
  //     this.setState({ storeNickName: value });
  //     this.setState({ isEdit: false });
  //   } else {
  //     this.setState({ storeNickName: value });
  //     this.setState({ isEdit: true });
  //     this.setState({ isInvalid: false });
  //   }
  // };

  // handleFormChange = event => {
  //   const value = event.target.value;
  //   const regex = /^[a-zA-Z0-9]+( [a-zA-Z0-9]+)* ?$/;
  //   console.log('enter=1');
  //   if (!regex.test(value)) {
  //     console.log('enter=12');
  //     return; // Prevent updates if the value doesn't match the regex
  //   }

  //   if (value.length > 25) {
  //     console.log('enter=123');
  //     this.setState({ isInvalid: true });
  //   } else if (this.state.storeNickNameStored === value) {
  //     console.log('enter=1234');
  //     this.setState({
  //       storeNickName: value,
  //       isEdit: false,
  //       isInvalid: false
  //     });
  //   } else {
  //     console.log('enter=12345');
  //     this.setState({
  //       storeNickName: value,
  //       isEdit: true,
  //       isInvalid: false
  //     });
  //   }
  // };
  handleFormChange = event => {
    const value = event.target.value;
    const regex = /^[a-zA-Z0-9]+( [a-zA-Z0-9]+)* ?$/;

    // Allow updating state for empty value (handle deletion properly)
    if (value === '' || regex.test(value)) {
      if (value.length > 25) {
        console.log('enter=123');
        this.setState({ isInvalid: true });
      } else if (this.state.storeNickNameStored === value) {
        console.log('enter=1234');
        this.setState({
          storeNickName: value,
          isEdit: false,
          isInvalid: false
        });
      } else {
        console.log('enter=12345');
        this.setState({
          storeNickName: value,
          isEdit: true,
          isInvalid: false,
          openWarn: false
        });
      }
    } else {
      console.log('enter=12');
      // Optionally, you can handle invalid inputs here
    }
  };

  // handleFormChangeFOPC = event => {
  //   const value = event.target.value.trim();
  //   console.log('yyyyyy---', value);
  //   console.log('yyyyyy---88888888', this.state.storeFOPCMonthlyFee);

  //   if (value.length >= 26) {
  //     this.setState({ isInvalidFopc: true });
  //     // this.setState({ storeNickName: '' });
  //   } else if (this.state.storeFOPCMonthlyFeeStored == value) {
  //     this.setState({ storeFOPCMonthlyFee: value });
  //     this.setState({ isEdit: false });
  //   } else if (/^\d*\.?\d*$/.test(value)) {
  //     this.setState({ storeFOPCMonthlyFee: value });
  //     this.setState({ isEdit: true });
  //     this.setState({ isInvalidFopc: false });
  //   }
  // };
  handleFormChangeFOPC = event => {
    const value = event.target.value.trim();
    console.log('yyyyyy---', value);
    console.log('yyyyyy---88888888', this.state.storeFOPCMonthlyFee);
    const regex = /^\d{0,6}(\.\d{0,2})?$/;
    if (this.state.storeFOPCMonthlyFeeStored == value) {
      this.setState({ storeFOPCMonthlyFee: value });
      this.setState({ isEdit: false });
    } else if (regex.test(value)) {
      this.setState({ storeFOPCMonthlyFee: value });
      this.setState({ isEdit: true });
      this.setState({ openWarn: false });
      this.setState({ isInvalidFopc: false });
    }
  };
  handleFormChangeMonthlyDMS = event => {
    const value = event.target.value.trim();
    console.log('yyyyyy---', value);
    const regex = /^\d{0,6}(\.\d{0,2})?$/;
    if (this.state.storeDMSMonthlyFeeStored == value) {
      this.setState({ storeDMSMonthlyFee: value });
      this.setState({ isEdit: false });
    } else if (regex.test(value)) {
      this.setState({ storeDMSMonthlyFee: value });
      this.setState({ openWarn: false });
      this.setState({ isEdit: true });
      this.setState({ isInvalidDms: false });
    }
  };

  // handleFormChangeMonthlyDMS = event => {
  //   const value = event.target.value.trim();
  //   console.log('yyyyyy---', value);
  //   console.log('yyyyyy---88888888', this.state.storeDMSMonthlyFee);

  //   if (value.length >= 26) {
  //     console.log('enter=1');
  //     this.setState({ isInvalidDms: true });
  //     // this.setState({ storeNickName: '' });
  //   } else if (this.state.storeDMSMonthlyFeeStored == value) {
  //     console.log('enter=12');
  //     this.setState({ storeDMSMonthlyFee: value });
  //     this.setState({ isEdit: false });
  //   } else if (/^\d*\.?\d*$/.test(value)) {
  //     console.log('enter=123');
  //     this.setState({ storeDMSMonthlyFee: value });
  //     this.setState({ isEdit: true });
  //     this.setState({ isInvalidDms: false });
  //   }
  // };

  getContextMenuItems = params => {
    return []; // Return an empty array to hide the context menu
  };
  onCellContextMenu = event => {
    event.preventDefault();
    console.log('Right-click disabled');
  };
  render() {
    const { classes } = this.props;

    return (
      <div>
        <Paper
          square
          style={{
            margin: 8
          }}
        >
          <Typography
            style={{ padding: 8 }}
            variant="h4"
            className={clsx(classes.mainLabel)}
          >
            Store Settings
          </Typography>
          <Divider></Divider>
          <Snackbar
            open={this.state.open}
            autoHideDuration={2000}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'center'
            }}
            onClose={this.hidesnackbar}
          >
            <Alert
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => {
                    this.setState({ open: false });
                  }}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
              style={{ margin: '10px 20px' }}
            >
              Store Settings Saved Successfully!
            </Alert>
          </Snackbar>
          {/* <Snackbar
            open={this.state.openWarn}
            autoHideDuration={2000}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'center'
            }}
            onClose={this.hidesnackbarWarn}
          >
            <Alert
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => {
                    this.setState({ openWarn: false });
                  }}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
              severity="warning"
              style={{ margin: '10px 20px' }}
            >
              Please update any value!
            </Alert>
          </Snackbar> */}
          <Grid container spacing={3}>
            <Grid item xs={12} className={'gridgoalsettings'}>
              {/* {this.props.keycloak.realmAccess.roles.includes('superadmin') ===
              true ? (
                <div style={{ width: '350px' }}>
                  <Tooltip
                    title="Save All"
                    onClick={this.handleStoreSettingsSave}
                  >
                    <Button
                      variant="contained"
                      className={clsx(classes.edit, 'reset-btn', 'btnSettings')}
                      style={{ marginTop: '26px', marginRight: '-68px' }}
                      onClick={this.handleStoreSettingsSave}
                    >
                      Save All
                    </Button>
                  </Tooltip>
                </div>
              ) : null} */}
              <div
                style={{
                  float: 'left',
                  marginLeft: 15,
                  width: '100%',
                  marginTop:
                    this.props.keycloak.realmAccess.roles.includes(
                      'superadmin'
                    ) === true
                      ? 16
                      : 40
                }}
              >
                <Typography
                  variant="h14"
                  style={{
                    textTransform: 'none',
                    float: 'left',
                    marginTop: -12,
                    color: 'rgb(0, 61, 107)',
                    fontSize: 13,
                    fontFamily: 'Roboto',
                    fontWeight: 'bold'
                  }}
                >
                  Select Timezone
                </Typography>
                <div className={'select-wrapper'}>
                  <TimezoneSelect
                    className={
                      this.props.keycloak.realmAccess.roles.includes(
                        'superadmin'
                      ) === true
                        ? 'select-wrapperselect'
                        : 'select-wrapperselect-others'
                    }
                    value={this.state.selectedTimezone}
                    placeholder="Select"
                    onChange={this.onChangeTimeZone}
                    timezones={{
                      'Pacific/Honolulu': 'Hawaii',
                      'America/Juneau': 'Alaska',
                      'America/Los_Angeles': 'Pacific Time',
                      'America/Boise': 'Mountain Time',
                      'America/Chicago': 'Central Time',
                      'America/Detroit': 'Eastern Time'
                    }}
                  />
                </div>
                <Typography
                  variant="h14"
                  style={{
                    textTransform: 'none',
                    float: 'left',
                    color: 'rgb(0, 61, 107)',
                    fontSize: 13,
                    paddingTop: '4px',
                    fontFamily: 'Roboto',
                    fontWeight: 'bold'
                  }}
                >
                  Select Working Days
                </Typography>
                <div style={{ padding: '11px 20px 10px', display: 'flex' }}>
                  <div
                    id="data-tab-goal-storeset"
                    className={clsx('ag-theme-balham', 'gridgoalsettings')}
                    style={{
                      height: '250px',
                      width: '325px',
                      marginLeft: '-139px'
                      // display: this.state.isLoading == true ? 'none' : 'block',
                    }}
                    onCellContextMenu={this.onCellContextMenu}
                  >
                    <AgGridReact
                      className="ag-theme-balham"
                      style={{
                        height: '400px',
                        width: '100%'
                      }}
                      autoGroupColumnDef={this.state.autoGroupColumnDef}
                      frameworkComponents={this.state.frameworkComponents}
                      getChartToolbarItems={this.getChartToolbarItems}
                      rowSelection={this.state.rowSelection}
                      modules={AllModules}
                      columnDefs={this.state.columnDefs}
                      defaultColDef={this.state.defaultColDef}
                      onGridReady={this.onGridReady}
                      suppressAggFuncInHeader
                      rowData={this.state.rowData}
                      excelStyles={this.state.excelStyles}
                      suppressRowClickSelection={true}
                      tooltipShowDelay={0}
                      suppressHorizontalScroll={true}
                      floatingFilter={false}
                      animateRows={true}
                      enableCharts={true}
                      onSelectionChanged={this.onSelectionChanged}
                      onCellValueChanged={this.onCellValueChanged}
                      onRowSelected={this.onRowSelected}
                      suppressDragLeaveHidesColumns={true}
                      getContextMenuItems={this.getContextMenuItems}
                      suppressContextMenu={true}
                    />
                  </div>

                  {/* <FormControl className={classes.formControl}>
                    <InputLabel id="mutiple-select-label">Choose Working Days</InputLabel>
                    <Select
                      labelId="mutiple-select-label"
                      multiple
                      value={this.state.selected}
                      onChange={this.handleChange}
                      renderValue={(selected) => selected.join(", ")}
                    // MenuProps={MenuProps}
                    >
                      <MenuItem
                        value="all"
                        classes={{
                          // root: isAllSelected ? classes.selectedAll : ""
                        }}
                      >
                        <ListItemIcon>
                          <Checkbox
                            classes={{ indeterminate: classes.indeterminateColor }}
                            checked={this.state.options.length > 0 && this.state.selected.length === this.state.options.length}
                            indeterminate={
                              this.state.selected.length > 0 && this.state.selected.length < this.state.options.length
                            }
                          />
                        </ListItemIcon>
                        <ListItemText
                          classes={{ primary: classes.selectAllText }}
                          primary="Select All"
                        />
                      </MenuItem>
                      {this.state.options.map((option) => (
                        <MenuItem key={option} value={option}>
                          <ListItemIcon>
                            <Checkbox checked={this.state.selected.indexOf(option) > -1} />
                          </ListItemIcon>
                          <ListItemText primary={option} />
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl> */}
                </div>
                {/* <Typography
                  variant="h14"
                  style={{
                    textTransform: 'none',
                    float: 'left',
                    marginTop: -21,
                    color: 'rgb(0, 61, 107)',
                    fontSize: 13,
                    fontFamily: 'Roboto',
                    fontWeight: 'bold'
                  }}
                >
                  Store Nickname
                </Typography>

                <TextField
                  color="secondary"
                  classes={{ root: classes.TextField }}
                  size="small"
                  value={this.state.storeNickName}
                  onChange={this.handleFormChange}
                  error={this.state.isInvalid}
                  disabled={
                    !this.props.keycloak.realmAccess.roles.includes(
                      'superadmin'
                    )
                  }
                  helperText={
                    this.state.isInvalid
                      ? 'Store nickname cannot be more than 25 characters long.'
                      : ''
                  }
                  variant="outlined"
                  className={classes.input}
                />
                <Typography
                  variant="h14"
                  style={{
                    textTransform: 'none',
                    float: 'left',
                    marginTop: -21,
                    color: 'rgb(0, 61, 107)',
                    fontSize: 13,
                    fontFamily: 'Roboto',
                    fontWeight: 'bold'
                  }}
                >
                  Store Monthly Fee
                </Typography>

                <TextField
                  color="secondary"
                  classes={{ root: classes.TextField }}
                  size="small"
                  value={this.state.storeNickName}
                  onChange={this.handleFormChange}
                  error={this.state.isInvalid}
                  disabled={
                    !this.props.keycloak.realmAccess.roles.includes(
                      'superadmin'
                    )
                  }
                  helperText={
                    this.state.isInvalid
                      ? 'Store nickname cannot be more than 25 characters long.'
                      : ''
                  }
                  variant="outlined"
                  className={classes.input}
                />
                <Typography
                  variant="h14"
                  style={{
                    textTransform: 'none',
                    float: 'left',
                    marginTop: -21,
                    color: 'rgb(0, 61, 107)',
                    fontSize: 13,
                    fontFamily: 'Roboto',
                    fontWeight: 'bold'
                  }}
                >
                  Store DMS Fee
                </Typography>

                <TextField
                  color="secondary"
                  classes={{ root: classes.TextField }}
                  size="small"
                  value={this.state.storeNickName}
                  onChange={this.handleFormChange}
                  error={this.state.isInvalid}
                  disabled={
                    !this.props.keycloak.realmAccess.roles.includes(
                      'superadmin'
                    )
                  }
                  helperText={
                    this.state.isInvalid
                      ? 'Store nickname cannot be more than 25 characters long.'
                      : ''
                  }
                  variant="outlined"
                  className={classes.input}
                /> */}

                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '24px'
                  }}
                >
                  {/* Store Nickname */}
                  <div>
                    <Typography
                      variant="h14"
                      style={{
                        textTransform: 'none',
                        float: 'left',
                        marginTop: -21,
                        color: 'rgb(0, 61, 107)',
                        fontSize: 13,
                        fontFamily: 'Roboto',
                        fontWeight: 'bold'
                      }}
                    >
                      Store Nickname
                    </Typography>
                    <TextField
                      color="secondary"
                      classes={{ root: classes.TextField }}
                      size="small"
                      value={this.state.storeNickName}
                      onChange={this.handleFormChange}
                      error={this.state.isInvalid}
                      disabled={
                        !this.props.keycloak.realmAccess.roles.includes(
                          'superadmin'
                        )
                      }
                      helperText={
                        this.state.isInvalid
                          ? 'Store nickname cannot be more than 25 characters long.'
                          : ''
                      }
                      variant="outlined"
                      className={classes.input}
                    />
                  </div>

                  {/* Store Monthly Fee */}
                  <div>
                    <Typography
                      variant="h14"
                      style={{
                        textTransform: 'none',
                        float: 'left',
                        marginTop: -21,
                        color: 'rgb(0, 61, 107)',
                        fontSize: 13,
                        fontFamily: 'Roboto',
                        fontWeight: 'bold'
                      }}
                    >
                      Store Monthly Fee
                    </Typography>
                    {/* <TextField
                      color="secondary"
                      classes={{ root: classes.TextField }}
                      size="small"
                      value={this.state.storeFOPCMonthlyFee}
                      onChange={this.handleFormChangeFOPC}
                      onKeyPress={e => {
                        if (
                          !/[0-9.]/.test(e.key) ||
                          (e.key === '.' &&
                            this.state.storeFOPCMonthlyFee &&
                            this.state.storeFOPCMonthlyFee.includes('.'))
                        ) {
                          e.preventDefault();
                        }
                      }}
                      error={this.state.isInvalidFopc}
                      disabled={
                        !this.props.keycloak.realmAccess.roles.includes(
                          'superadmin'
                        )
                      }
                      helperText={
                        this.state.isInvalidFopc
                          ? 'Store monthly fee cannot be more than 25 characters long.'
                          : ''
                      }
                      variant="outlined"
                      className={classes.input}
                      InputProps={{
                        startAdornment: (
                          <span
                            style={{ marginRight: '8px', fontWeight: 'bold' }}
                          >
                            $
                          </span>
                        )
                      }}
                    /> */}
                    <TextField
                      color="secondary"
                      classes={{ root: classes.TextField }}
                      size="small"
                      value={this.state.storeFOPCMonthlyFee}
                      onChange={e => {
                        let inputValue = e.target.value;
                        const regex = /^\d{0,6}(\.\d{0,2})?$/;
                        // Remove leading zeros
                        if (inputValue !== '' && inputValue !== '0') {
                          inputValue = inputValue.replace(/^0+(?!\.)/, '');
                        }

                        // Handle empty value to reset to "0"
                        if (inputValue === '') {
                          inputValue = '0';
                        }
                        if (
                          this.state.storeFOPCMonthlyFeeStored == inputValue
                        ) {
                          this.setState({ storeFOPCMonthlyFee: inputValue });
                          this.setState({ isEdit: false });
                        } else if (regex.test(inputValue)) {
                          this.setState({ storeFOPCMonthlyFee: inputValue });
                          this.setState({ isEdit: true });
                          this.setState({ openWarn: false });
                          this.setState({ isInvalidFopc: false });
                        }
                        // this.setState({ storeFOPCMonthlyFee: inputValue });
                      }}
                      onKeyPress={e => {
                        // Allow digits and single decimal
                        if (
                          !/[0-9.]/.test(e.key) ||
                          (e.key === '.' &&
                            this.state.storeFOPCMonthlyFee.includes('.'))
                        ) {
                          e.preventDefault();
                        }
                      }}
                      error={this.state.isInvalidFopc}
                      disabled={
                        !this.props.keycloak.realmAccess.roles.includes(
                          'superadmin'
                        )
                      }
                      helperText={
                        this.state.isInvalidFopc
                          ? 'Store Monthly Fee must have max 6 digits before decimal and 2 digits after.'
                          : ''
                      }
                      variant="outlined"
                      className={classes.input}
                      InputProps={{
                        startAdornment: (
                          <span
                            style={{ marginRight: '8px', fontWeight: 'bold' }}
                          >
                            $
                          </span>
                        )
                      }}
                    />
                  </div>

                  {/* Store DMS Fee */}
                  <div>
                    <Typography
                      variant="h14"
                      style={{
                        textTransform: 'none',
                        float: 'left',
                        marginTop: -21,
                        color: 'rgb(0, 61, 107)',
                        fontSize: 13,
                        fontFamily: 'Roboto',
                        fontWeight: 'bold'
                      }}
                    >
                      Store DMS Fee
                    </Typography>
                    <TextField
                      color="secondary"
                      classes={{ root: classes.TextField }}
                      size="small"
                      value={this.state.storeDMSMonthlyFee}
                      onChange={e => {
                        let inputValue = e.target.value;

                        if (inputValue !== '' && inputValue !== '0') {
                          inputValue = inputValue.replace(/^0+(?!\.)/, '');
                        }

                        // Reset to "0" if the input is cleared
                        if (inputValue === '') {
                          inputValue = '0';
                        }
                        const regex = /^\d{0,6}(\.\d{0,2})?$/;
                        if (this.state.storeDMSMonthlyFeeStored == inputValue) {
                          this.setState({ storeDMSMonthlyFee: inputValue });
                          this.setState({ isEdit: false });
                        } else if (regex.test(inputValue)) {
                          this.setState({ storeDMSMonthlyFee: inputValue });
                          this.setState({ isEdit: true });
                          this.setState({ openWarn: false });
                          this.setState({ isInvalidDms: false });
                        }
                        // this.setState({ storeDMSMonthlyFee: inputValue });
                      }}
                      onKeyPress={e => {
                        // Allow digits and a single decimal point
                        if (
                          !/[0-9.]/.test(e.key) || // Block non-numeric and non-decimal characters
                          (e.key === '.' &&
                            this.state.storeDMSMonthlyFee?.includes('.')) // Block multiple decimals
                        ) {
                          e.preventDefault();
                        }
                      }}
                      error={this.state.isInvalidDms}
                      disabled={
                        !this.props.keycloak.realmAccess.roles.includes(
                          'superadmin'
                        )
                      }
                      helperText={
                        this.state.isInvalidDms
                          ? 'Store DMS Fee must have max 6 digits before decimal and 2 digits after.'
                          : ''
                      }
                      variant="outlined"
                      className={classes.input}
                      InputProps={{
                        startAdornment: (
                          <span
                            style={{ marginRight: '8px', fontWeight: 'bold' }}
                          >
                            $
                          </span>
                        )
                      }}
                    />
                  </div>
                </div>
              </div>
              {this.props.keycloak.realmAccess.roles.includes('superadmin') ===
              true ? (
                <div style={{ width: '350px' }}>
                  <>
                    {this.state.openWarn && (
                      <FormHelperText
                        style={{ color: 'rgb(255,91,71)', marginLeft: 16 }}
                      >
                        Please update any value!
                      </FormHelperText>
                    )}
                    <Tooltip
                      title="Save All"
                      onClick={this.handleStoreSettingsSave}
                    >
                      <Button
                        variant="contained"
                        className={clsx(
                          classes.edit,
                          'reset-btn',
                          'btnSettings'
                        )}
                        style={{ marginTop: '26px', marginRight: '0px' }}
                        onClick={this.handleStoreSettingsSave}
                      >
                        Save All
                      </Button>
                    </Tooltip>
                  </>
                </div>
              ) : null}
              <Divider className={'sep1'}></Divider>
            </Grid>
          </Grid>
        </Paper>
      </div>
    );
  }
}

StoreSetting.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};

function extractValues(mappings) {
  return Object.keys(mappings);
}
const DayTypeMapping = {
  1: 'Full Day',
  0.5: 'Half Day',
  0.25: 'Quarter Day',
  0.75: 'Three Quarter Day'
};

const DayType = extractValues(DayTypeMapping);

function createRowData() {
  var rowData = [];
  const daysInWeek = [
    'Sunday',
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday'
  ];
  daysInWeek.map(value => {
    rowData.push({
      keyname: value,
      keyvalue: '1',
      active: '1'
    });
  });

  return rowData;
}
function lookupValue(mappings, key) {
  return mappings[key];
}
const styles = theme => ({
  formControl: {
    minWidth: 200,
    marginTop: -19
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  back: {
    marginRight: 10,
    float: 'right'
  },
  input: {
    margin: '0px 0px',
    width: '324px'
  }
});

export default withStyles(styles)(withKeycloak(StoreSetting));
