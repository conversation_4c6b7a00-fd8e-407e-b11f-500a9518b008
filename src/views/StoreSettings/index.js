import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import StoreSetting from './StoreSettings';
import { setRefreshStatus, setReloadStatus } from 'src/actions';
import { useDispatch } from 'react-redux';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
// const useStyles = makeStyles(theme => ({
//   root: {
//     paddingTop: theme.spacing(0),
//     paddingBottom: theme.spacing(3)
//   },
//   statistics: {
//     marginTop: theme.spacing(3)
//   },
//   notifications: {
//     marginTop: theme.spacing(6)
//   },
//   projects: {
//     marginTop: theme.spacing(6)
//   },
//   todos: {
//     marginTop: theme.spacing(6)
//   }
// }));

function storeSetting(keycloak) {
  // const classes = useStyles();
  // const dispatch = useDispatch();
  const handleRefresh = status => {
    //dispatch(setRefreshStatus(status));
  };
  const handleReload = status => {
    // dispatch(setReloadStatus(status));
  };
  return (
    <Page title="Store Settings">
      {/* {keycloak.keycloak.realmAccess.roles.includes('client') ||
      keycloak.keycloak.realmAccess.roles.includes('user') ? (
        <Redirect to="/errors/error-404" />
      ) : ( */}
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <StoreSetting
          handleRefresh={handleRefresh.bind(this)}
          handleReload={handleReload.bind(this)}
        />
      )}
      {/* )} */}
    </Page>
  );
}

export default withKeycloak(storeSetting);
