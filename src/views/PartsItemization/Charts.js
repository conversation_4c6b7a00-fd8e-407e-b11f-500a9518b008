import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
// import CPELRVsLaborSoldHours from 'src/components/charts/CPELRVsLaborSoldHours';
import CPPartsMarkupVsPartsCost from 'src/components/charts/CPPartsMarkupVsPartsCost';
import {
  Paper,
  Divider,
  Tab,
  Tabs,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
  Grid
} from '@material-ui/core';

import moment from 'moment';
import clsx from 'clsx';
import 'src/styles.css';
import { withStyles } from '@material-ui/styles';

import RestoreIcon from '@material-ui/icons/Restore';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import {
  getDrillDownDataForPartsItemization,
  getLatestClosedDate
} from 'src/utils/hasuraServices';
import DashboardActions from 'src/components/DashboardActions';
import $ from 'jquery';

import PageHeader from 'src/components/PageHeader';
import { traceSpan } from 'src/utils/OTTTracing';

var lodash = require('lodash');
const originalLayout = getFromLS('layout') || {};
const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 40,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      chartData: [],
      closedDate: '',
      openDate: '',
      isTabChanged: false,
      isReloaded: false,
      layout: JSON.parse(JSON.stringify(originalLayout)),
      chartData: [],
      chartCompleteData: [],
      tabSelection:
        this.props.history && this.props.history.location.state
          ? this.props.history.location.state.tabSelection
          : this.props.tabSelection
          ? this.props.tabSelection
          : 'three',
      //  dashboardReset: false,
      elementClicked: false,
      popup: false,
      isReloaded: false,

      parent: this.props.parent ? this.props.parent : ''
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }
  componentDidMount() {
    this.getDataForPartsItemizationChart(this.state.tabSelection);
  }

  componentDidUpdate(prevProps, prevState) {
    if (
      prevState.tabSelection != this.state.tabSelection ||
      this.props.session.serviceAdvisor != prevProps.session.serviceAdvisor
    ) {
      this.setState({
        chartData: [],
        chartCompleteData: [],
        isLoading: true
      });
      this.getDataForPartsItemizationChart(this.state.tabSelection);
    }
    if (
      prevState.isReloaded == this.state.isReloaded &&
      this.state.isReloaded == true
    ) {
      this.setState({ isReloaded: false });
    }
  }

  getDataForPartsItemizationChart = tabSelection => {
    let filteredArr = [];
    let data = [];
    let allData = {};
    this.setState({ isLoading: true });
    this.setState({
      chartData: [],
      chartCompleteData: []
    });
    getDrillDownDataForPartsItemization(
      tabSelection,
      this.props.timeZone,
      result => {
        this.setState({ isLoading: false });
        if (
          result.data
            .statelessDbdPartsItemizationGetChartsPartsMarkupCostItemization
            .statelessDbdPartsItemizationChartsPartsMarkupCostItemizationDetails
        ) {
          let dataArr =
            result.data
              .statelessDbdPartsItemizationGetChartsPartsMarkupCostItemization
              .statelessDbdPartsItemizationChartsPartsMarkupCostItemizationDetails;

          if (this.props.session.serviceAdvisor.includes('All') == false) {
            dataArr.map(obj => {
              if (
                this.props.session.serviceAdvisor.indexOf(obj.serviceadvisor) !=
                -1
              ) {
                filteredArr.push(obj);
              }
            });
            data = filteredArr;
          } else {
            data = dataArr;
          }
          // setDataArr(data);
          // var orderedData = lodash
          //   .chain(data)
          //   .groupBy('opcategory')
          //   .map(value => {
          //     return value.map(data => data);
          //   })
          //   .value();
          data = lodash.orderBy(data, 'opcategory', 'asc');
          data = data.filter(item => item.opcategory != '');
          var orderedData1 = lodash
            .chain(data)
            .groupBy('opcategory')
            .map(value => {
              return value.map(data => [
                Number(data.prtscost) > 500 ? 500 : Number(data.prtscost),
                data.opcategory == 'COMPETITIVE'
                  ? Number(data.markupCompetitive)
                  : data.opcategory == 'MAINTENANCE'
                  ? Number(data.markupMaintenance)
                  : Number(data.markupRepair)
              ]);
            })
            .value();
          if (
            !Object.keys(
              lodash
                .chain(data)
                .groupBy('opcategory')
                .value()
            ).includes('COMPETITIVE')
          ) {
            var tmp = orderedData1[1];
            orderedData1[1] = orderedData1[0];
            orderedData1[2] = tmp;
            orderedData1[0] = null;
          }
          if (
            !Object.keys(
              lodash
                .chain(data)
                .groupBy('opcategory')
                .value()
            ).includes('MAINTENANCE')
          ) {
            var tmp = orderedData1[1];
            orderedData1[0] = orderedData1[0];
            orderedData1[2] = tmp;
            orderedData1[1] = null;
          }
          if (
            !Object.keys(
              lodash
                .chain(data)
                .groupBy('opcategory')
                .value()
            ).includes('REPAIR')
          ) {
            var tmp = orderedData1[1];
            orderedData1[0] = orderedData1[0];
            orderedData1[1] = tmp;
            orderedData1[2] = null;
          }

          if (data.length > 0) {
            allData = {
              //labels: data.map(c => c.lbrsoldhours),
              labels: data.map(c => c.opcategory),
              // labels: orderedData.categories().map(c => c.opcategory),
              datasets: [
                {
                  label: 'Markup Competitive',
                  data: data.map((s, index) => ({
                    y:
                      s.markupCompetitive !== '0.00'
                        ? s.markupCompetitive
                        : 'null',
                    label: s.prtscost,
                    category: 'Markup Competitive',
                    x: s.prtscost > 500 ? '500' : s.prtscost,
                    count: s.competitiveCount
                  }))
                },
                {
                  label: 'Markup Maintenance',
                  data: data.map((s, index) => ({
                    y:
                      s.markupMaintenance !== '0.00'
                        ? s.markupMaintenance
                        : 'null',
                    label: s.prtscost,
                    category: 'Markup Maintenance',
                    x: s.prtscost > 500 ? '500' : s.prtscost,
                    count: s.maintenanceCount
                  }))
                },
                {
                  label: 'Markup Repair',
                  data: data.map((s, index) => ({
                    y: s.markupRepair !== '0.00' ? s.markupRepair : 'null',
                    label: s.prtscost,
                    category: 'Markup Repair',
                    x: s.prtscost > 500 ? '500' : s.prtscost,
                    count: s.repairCount
                  }))
                }
              ]
            };
          } else {
            allData = {
              label: {},
              datasets: []
            };
            orderedData1[0] = [];
            orderedData1[1] = [];
            orderedData1[2] = [];
          }
          this.setState({
            isLoading: false,
            isTabChanged: false
          }); // console.log('getDrillDownDataForLaborItemization', allData);
          this.setState({ chartCompleteData: allData });
          this.setState({ chartData: orderedData1 });
        }
      }
    );
  };
  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: [],
        elementClicked: true
      });
    }
    return this.state.layout;
  };
  handleResetClicked = () => {
    this.setState({
      elementClicked: false
    });
  };
  onLayoutChange(layout) {
    /*eslint no-console: 0*/
    saveToLS('layout', layout);
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }
  handleClosePopup = value => {
    this.setState({
      popupChartId: '',
      open: false
    });
  };
  handleChartReset = value => {
    if (value == 1) {
      this.setState({
        isReloaded: false
      });
    }
  };
  resetLayout = () => {
    this.setState({
      layout: [],
      elementClicked: true,
      isReloaded: true
    });
  };
  handleTabChange = (event, newValue) => {
    if (this.state.tabSelection != newValue) {
      this.setState({
        chartData: [],
        chartCompleteData: []
      });

      const spanAttribute = {
        pageUrl: '',
        origin: '',
        event: 'ScatterPlotPartsTabs Click',
        value: newValue,
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('ScatterPlot-Parts Tabs', spanAttribute);
      this.setState({ tabSelection: newValue, isTabChanged: true });
      localStorage.setItem('itemTab', newValue);
    }
  };
  // setResetDashboard = value => {
  //   if (value) {
  //     setLayout([]);
  //     setAllData([]);
  //     setFilteredArr([]);
  //     setLegendArr([]);
  //     setElementClicked(true);
  //   }
  // };
  render() {
    const { classes } = this.props;
    var title =
      'Scatter Plot - Parts  ' +
      '\xa0\xa0\xa0' +
      '-' +
      '\xa0\xa0\xa0\xa0' +
      'Cost / Jobs / Markup';

    if (typeof this.state.chartData.length == 'undefined') {
      this.setState({ isReloaded: true });
    }
    return (
      // <div>
      //   {this.state.parent != 'Details' &&
      //   <DashboardActions
      //     resetDashboard={this.setResetDashboard}
      //     filterCharts={this.setFilterCharts}
      //     setTitle={
      //       <div>
      //         <span>
      //           {title}
      //         </span>
      //       </div>
      //     }
      //     noFilters={true}
      //   ></DashboardActions>
      // }

      //   <React.Fragment>

      <div
        className={
          this.state.parent != 'Details' ? classes.root : classes.rootDetail
        }
      >
        <Paper
          className={
            this.state.parent != 'Details' ? classes.paper : classes.paperDetail
          }
        >
          {this.state.popup != 'popup' && this.state.parent != 'Details' && (
            <div>
              <PageHeader
                title={'Scatter Plot - Parts - Cost / Jobs / Markup'}
                setResetDashboard={this.setResetDashboard}
              />

              <Divider />

              <Paper square className={classes.paperSub}>
                <Tabs
                  variant="scrollable"
                  scrollButtons="auto"
                  value={
                    this.state.tabSelection ? this.state.tabSelection : 'three'
                  }
                  className={classes.tabs}
                  onChange={this.handleTabChange}
                  indicatorColor="secondary"
                  textColor="secondary"
                  TabIndicatorProps={{ style: { display: 'none' } }}
                >
                  <Tab
                    style={{ textTransform: 'none' }}
                    label={<div>Last 6 Months</div>}
                    value="three"
                    id="Last 6 Months"
                    onClick={this.resetLayout}
                  />
                  <Tab
                    style={{ textTransform: 'none' }}
                    label={<div>Last 90 Days</div>}
                    value="two"
                    id="Last 90 Days"
                    onClick={this.resetLayout}
                  />
                  <Tab
                    style={{ textTransform: 'none' }}
                    label={<div>Last 30 Days</div>}
                    value="one"
                    id="Last 30 Days"
                    onClick={this.resetLayout}
                  />
                  <Tab
                    style={{ textTransform: 'none' }}
                    label={<div>Last 7 Days</div>}
                    value="four"
                    id="Last 7 Days"
                    onClick={this.resetLayout}
                  />
                </Tabs>
              </Paper>
            </div>
          )}
          {(this.state.isLoading &&
            this.state.isTabChanged &&
            this.state.chartCompleteData.length > 0) ||
          typeof this.state.chartCompleteData.datasets == 'undefined' ? (
            <Grid justify="center" className={classes.loaderGrid}>
              <CircularProgress size={60} />
            </Grid>
          ) : (
            <div
              className={
                this.state.parent != 'Details'
                  ? clsx(classes.container, 'diagram-section', 'container')
                  : clsx(classes.container, 'diagram-section-scatter')
              }
            >
              <CPPartsMarkupVsPartsCost
                data={this.state.chartData}
                completeData={this.state.chartCompleteData}
                handleClosePopup={this.handleClosePopup}
                tabSelection={this.state.tabSelection}
                // dashboardReset={this.state.dashboardReset}
                resetClicked={this.state.elementClicked}
                handleResetClicked={this.handleResetClicked}
                type={this.state.popup}
                parent={this.state.parent}
                layout={this.state.layout}
                isReloaded={this.state.isReloaded}
                handleChartReset={this.handleChartReset}
                timeZone={this.props.timeZone}
              />
            </div>
          )}
        </Paper>
      </div>
      //   </React.Fragment>

      // </div>
    );
  }
}
function getFromLS(key) {
  let ls = {};
  if (global.localStorage) {
    try {
      ls = JSON.parse(global.localStorage.getItem('fixed-ops-layout-9')) || {};
    } catch (e) {
      /*Ignore*/
    }
  }
  return ls[key];
}

function saveToLS(key, value) {
  if (global.localStorage) {
    global.localStorage.setItem(
      'fixed-ops-layout-9',
      JSON.stringify({
        [key]: value
      })
    );
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 2px',
    borderRightColor: theme.palette.primary.main,
    borderLeftColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  dividerStyle: {
    marginLeft: 0,
    marginRight: 0
  },
  root: {
    flexGrow: 1
    // width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  paperSub: {
    boxShadow: '0 3px 2px -2px #8080802b'
    // marginTop: '20px'
  },
  titleContainer: {
    alignItems: 'center',

    display: 'flex',
    justifyContent: 'space-between'
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    //justifyContent: 'space-between',
    width: '100%'
  },
  tabs: {
    // "& button[aria-selected='true']": {
    //   border: "5px solid red"
    // }
    '& button': {
      padding: 5
    },
    "& button[aria-selected='true']": {
      color: theme.palette.primary.main,
      textTransform: 'none',
      border: 'solid 1px',
      borderColor: theme.palette.primary.main,
      backgroundColor: theme.palette.primary.active
      // backgroundColor: theme.palette.primary.active
    }
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  sublLabel: {
    display: 'flex'
  },
  resetButton: {
    float: 'right'
  },
  paperDetail: {
    boxShadow: 'none',
    padding: 0
  },
  loaderGrid: {
    display: 'flex'
  }
});

export default withStyles(styles)(Charts);
