import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { useSelector, useDispatch } from 'react-redux';
import Charts from './Charts';
import { getTimeZone } from '../../utils/Utils';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  rootDetails: {
    paddingRight: 8
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function PartsItemization() {
  const classes = useStyles();
  const history = useHistory();
  const session = useSelector(state => state.session);
  var timezone = getTimeZone();

  return (
    <Page
      className={classes.root}
      //title="Parts Itemization"
      title={'Scatter Plot - Parts \n Cost / Jobs / Markup'}
    >
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts history={history} session={session} timeZone={timezone} />
      )}
    </Page>
  );
}

export default PartsItemization;
