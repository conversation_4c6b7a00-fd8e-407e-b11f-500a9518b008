import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import CPELRVsLaborSoldHours from 'src/components/charts/CPELRVsLaborSoldHours';
import {
  Paper,
  Divider,
  Tab,
  Tabs,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
  Grid
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';

import RestoreIcon from '@material-ui/icons/Restore';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import {
  getDrillDownDataForLaborItemization,
  getLatestClosedDate
} from 'src/utils/hasuraServices';
import moment from 'moment';
import clsx from 'clsx';
import DashboardActions from 'src/components/DashboardActions';
import 'src/styles.css';

import PageHeader from 'src/components/PageHeader';
import {traceSpan} from 'src/utils/OTTTracing';

var lodash = require('lodash');
const originalLayout = getFromLS('layout') || {};
const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 40,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      chartData: [],
      closedDate: '',
      openDate: '',

      isReloaded: false,
      layout: JSON.parse(JSON.stringify(originalLayout)),
      chartData: [],
      tabSelection:
        this.props.history && this.props.history.location.state
          ? this.props.history.location.state.tabSelection
          : this.props.tabSelection
          ? this.props.tabSelection
          : 'three',
      //  dashboardReset: false,
      elementClicked: false,
      popup: false,
      parent: this.props.parent ? this.props.parent : ''
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }
  componentDidMount() {
    this.getDataForLaborItemizationChart(this.state.tabSelection);
  }

  componentDidUpdate(prevProps, prevState) {
    if (
      prevState.tabSelection != this.state.tabSelection ||
      this.props.session.serviceAdvisor != prevProps.session.serviceAdvisor
    ) {
      this.getDataForLaborItemizationChart(this.state.tabSelection);
    }
    if (
      prevState.isReloaded == this.state.isReloaded &&
      this.state.isReloaded == true
    ) {
      this.setState({ isReloaded: false });
    }
  }

  getDataForLaborItemizationChart = tabSelection => {
    let filteredArr = [];
    let data = [];
    let allData = {};
    this.setState({ isLoading: true });
    getDrillDownDataForLaborItemization(tabSelection, result => {
      this.setState({ isLoading: false });
      if (
        result.data.dbdLaborItemizationGetChartsLaborElrSoldhoursItemization
          .nodes
      ) {
        let dataArr =
          result.data.dbdLaborItemizationGetChartsLaborElrSoldhoursItemization
            .nodes;

        if (this.props.session.serviceAdvisor.includes('All') == false) {
          dataArr.map(obj => {
            if (
              this.props.session.serviceAdvisor.indexOf(obj.serviceadvisor) !=
              -1
            ) {
              filteredArr.push(obj);
            }
          });
          data = filteredArr;
        } else {
          data = dataArr;
        }
        // setDataArr(data);
        var orderedData = lodash
          .chain(data)
          .groupBy('opcategory')
          .map(value => {
            return value.map(data => data);
          })
          .value();
        if (data.length > 0) {
          allData = {
            //labels: data.map(c => c.lbrsoldhours),
            labels: data.map(c => c.opcategory),
            // labels: orderedData.categories().map(c => c.opcategory),
            datasets: [
              {
                label: 'ELR Competitive',
                data: data.map((s, index) => ({
                  y: s.elrCompetitive !== '0.00' ? s.elrCompetitive : 'null',
                  label: s.lbrsoldhours,
                  category: 'ELR Competitive',
                  x: s.lbrsoldhours > 20 ? '20' : s.lbrsoldhours,
                  count: s.competitiveCount
                })),
                hidden: false,
                fill: true,
                fillOpacity: 0.3,
                backgroundColor: 'rgba(75,192,192,0.4)',
                borderColor: 'rgba(75,192,192,1)',
                borderCapStyle: 'butt',
                borderDash: [],
                borderDashOffset: 0.0,
                borderJoinStyle: 'miter',
                pointBorderColor: 'rgba(75,192,192,1)',
                pointBackgroundColor: '#0389fc61',
                pointBorderWidth: 0.5,
                pointHoverRadius: 5,
                pointHoverBackgroundColor: 'rgba(75,192,192,1)',
                pointHoverBorderColor: 'rgba(220,220,220,1)',
                pointHoverBorderWidth: 0,
                pointRadius: 5,
                pointHitRadius: 10,
                type: 'scatter',
                lineTension: 0
              },
              {
                label: 'ELR Maintenance',
                data: data.map((s, index) => ({
                  y: s.elrMaintenance !== '0.00' ? s.elrMaintenance : 'null',
                  label: s.lbrsoldhours,
                  category: 'ELR Maintenance',
                  x: s.lbrsoldhours > 20 ? '20' : s.lbrsoldhours,
                  count: s.maintenanceCount
                })),
                hidden: null,
                fill: true,
                fillOpacity: 0.3,
                backgroundColor: 'rgba(220,57,18,0.5)',
                borderColor: 'rgba(220,57,18,1)',
                borderCapStyle: 'butt',
                borderDash: [],
                borderDashOffset: 0.0,
                borderJoinStyle: 'miter',
                pointBorderColor: 'rgba(220,57,18,1)',
                pointBackgroundColor: '#f17f7fa8',
                pointBorderWidth: 0.5,
                pointHoverRadius: 5,
                pointHoverBackgroundColor: 'rgba(220,57,18,1)',
                pointHoverBorderColor: 'rgba(220,220,220,1)',
                pointHoverBorderWidth: 0,
                pointRadius: 5,
                pointHitRadius: 10,
                type: 'scatter',
                lineTension: 0,
                // hidden: 'boolean',
                pointStyle: 'rect'
              },
              {
                label: 'ELR Repair',
                data: data.map((s, index) => ({
                  y: s.elrRepair !== '0.00' ? s.elrRepair : 'null',
                  label: s.lbrsoldhours,
                  category: 'ELR Repair',
                  x: s.lbrsoldhours > 20 ? '20' : s.lbrsoldhours,
                  count: s.repairCount
                })),
                fill: true,
                hidden: false,
                fillOpacity: 0.3,
                backgroundColor: 'rgba(4, 147, 114,0.4)',
                borderColor: 'rgba(4, 147, 114, 1)',
                borderCapStyle: 'butt',
                borderDash: [],
                borderDashOffset: 0.0,
                borderJoinStyle: 'miter',
                pointBorderColor: 'rgba(4, 147, 114, 1)',
                pointBackgroundColor: '#35af557a',
                pointBorderWidth: 0.5,
                pointHoverRadius: 5,
                pointHoverBackgroundColor: 'rgba(4, 147, 114, 1)',
                pointHoverBorderColor: 'rgba(220,220,220,1)',
                pointHoverBorderWidth: 0,
                pointRadius: 5,
                pointHitRadius: 10,
                type: 'scatter',
                lineTension: 0,
                pointStyle: 'rectRot'
              }
            ]
          };
        } else {
          allData = {
            label: {},
            datasets: []
          };
        }
        this.setState({ isLoading: false });
        // console.log('getDrillDownDataForLaborItemization', allData);
        this.setState({ chartData: allData });
      }
    });
  };
  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: [],
        elementClicked: true
      });
    }
    return this.state.layout;
  };
  handleResetClicked = () => {
    this.setState({
      elementClicked: false
    });
  };
  onLayoutChange(layout) {
    /*eslint no-console: 0*/
    saveToLS('layout', layout);
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }
  handleClosePopup = value => {
    this.setState({
      popupChartId: '',
      open: false
    });
  };
  handleChartReset = value => {
    if (value == 1) {
      this.setState({
        isReloaded: false
      });
    }
  };
  handleTabChange = (event, newValue) => {
    const spanAttribute ={
      "pageUrl" : "",
      "origin" : "",
      "event" : "ScatterPlotLaborTabs Click",
      "value" : newValue
      }
    traceSpan('ScatterPlot-Labor Tabs',spanAttribute);
    this.setState({ tabSelection: newValue });
    localStorage.setItem('itemTab', newValue);
  };
  // setResetDashboard = value => {
  //   if (value) {
  //     setLayout([]);
  //     setAllData([]);
  //     setFilteredArr([]);
  //     setLegendArr([]);
  //     setElementClicked(true);
  //   }
  // };
  render() {
    const { classes } = this.props;
    var title =
      'Scatter Plot - Labor  ' +
      '\xa0\xa0\xa0' +
      '-' +
      '\xa0\xa0\xa0\xa0' +
      'Hours / Jobs / ELR';

    if (typeof this.state.chartData.length == 'undefined') {
      this.setState({ isReloaded: true });
    }

    return (
      // <div>
      //   {this.state.parent != 'Details' &&
      //   <DashboardActions
      //     resetDashboard={this.setResetDashboard}
      //     filterCharts={this.setFilterCharts}
      //     setTitle={
      //       <div>
      //         <span>
      //           {title}
      //         </span>
      //       </div>
      //     }
      //     noFilters={true}
      //   ></DashboardActions>
      // }

      //   <React.Fragment>

      <div className={classes.root}>
        <Paper
          className={
            this.state.parent != 'Details' ? classes.paper : classes.paperDetail
          }
        >
          {this.state.popup != 'popup' && this.state.parent != 'Details' && (
            <div>
              <PageHeader
                title={'Scatter Plot - Labor - Jobs / Hours / ELR'}
                setResetDashboard={this.setResetDashboard}
              />

              <Divider />

              <Paper square className={classes.paperSub}>
                <Tabs
                  variant="scrollable"
                  scrollButtons="auto"
                  value={
                    this.state.tabSelection ? this.state.tabSelection : 'three'
                  }
                  className={classes.tabs}
                  onChange={this.handleTabChange}
                  indicatorColor="secondary"
                  textColor="secondary"
                  TabIndicatorProps={{ style: { display: 'none' } }}
                >
                  <Tab
                    style={{ textTransform: 'none' }}
                    label={<div>Last Six Months</div>}
                    value="three"
                  />
                  <Tab
                    style={{ textTransform: 'none' }}
                    label={<div>Last 90 days</div>}
                    value="two"
                  />
                  <Tab
                    style={{ textTransform: 'none' }}
                    label={<div>Last 30 days</div>}
                    value="one"
                  />
                </Tabs>
              </Paper>
            </div>
          )}
          {this.state.isLoading ||
          typeof this.state.chartData.datasets == 'undefined' ? (
            <Grid justify="center" className={classes.loaderGrid}>
              <CircularProgress size={60} />
            </Grid>
          ) : (
            <div
              className={
                this.state.parent != 'Details'
                  ? clsx(classes.container, 'diagram-section', 'container')
                  : clsx(classes.container, 'diagram-section-scatter')
              }
            >
              <CPELRVsLaborSoldHours
                data={this.state.chartData}
                handleClosePopup={this.handleClosePopup}
                tabSelection={this.state.tabSelection}
                // dashboardReset={this.state.dashboardReset}
                resetClicked={this.state.elementClicked}
                handleResetClicked={this.handleResetClicked}
                type={this.state.popup}
                parent={this.state.parent}
                layout={this.state.layout}
                isReloaded={this.state.isReloaded}
                handleChartReset={this.handleChartReset}
              />
            </div>
          )}
        </Paper>
      </div>
      //   </React.Fragment>

      // </div>
    );
  }
}
function getFromLS(key) {
  let ls = {};
  if (global.localStorage) {
    try {
      ls = JSON.parse(global.localStorage.getItem('fixed-ops-layout-6')) || {};
    } catch (e) {
      /*Ignore*/
    }
  }
  return ls[key];
}

function saveToLS(key, value) {
  if (global.localStorage) {
    global.localStorage.setItem(
      'fixed-ops-layout-6',
      JSON.stringify({
        [key]: value
      })
    );
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 2px',
    borderRightColor: theme.palette.primary.main,
    borderLeftColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  dividerStyle: {
    marginLeft: 0,
    marginRight: 0
  },
  root: {
    flexGrow: 1,
    width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  paperSub: {
    boxShadow: '0 3px 2px -2px #8080802b'
    // marginTop: '20px'
  },
  titleContainer: {
    alignItems: 'center',

    display: 'flex',
    justifyContent: 'space-between'
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    //justifyContent: 'space-between',
    width: '100%'
  },
  tabs: {
    // "& button[aria-selected='true']": {
    //   border: "5px solid red"
    // }
    '& button': {
      padding: 5
    },
    "& button[aria-selected='true']": {
      color: theme.palette.primary.main,
      textTransform: 'none',
      border: 'solid 1px',
      borderColor: theme.palette.primary.main,
      backgroundColor: theme.palette.primary.active
      // backgroundColor: theme.palette.primary.active
    }
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  sublLabel: {
    display: 'flex'
  },
  resetButton: {
    float: 'right'
  },
  paperDetail: {
    boxShadow: 'none',
    padding: 0
  },
  loaderGrid: {
    display: 'flex'
  }
});

export default withStyles(styles)(Charts);
