import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Button
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { UPDATE_CHART_MASTER } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import {
  getAllChartDetailsForDisplay,
  getDbdDetails,
  getViewDetails
} from 'src/utils/hasuraServices';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import RestoreIcon from '@material-ui/icons/Restore';
import { ReactSession } from 'react-client-session';
import clsx from 'clsx';
import ActiveStatusRenderer from './ActiveStatusRenderer';
import { getAllChartDetails } from '../../utils/Utils';
import { traceSpan } from 'src/utils/OTTTracing';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class ChartMaster extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData();
      }
    }
  }
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=00==",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData();
  //     }
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };
  componentDidMount() {
    this.getDbdData();
    // this.getViewData();
  }

  constructor(props) {
    super(props);
    let startEdit = this;
    this.state = {
      cancel: false,
      showCharts: false,
      selectValue: 'other',
      selectValueFor: this.props.category,
      selectValueType: this.props.reportType,
      ddLabel: '< 2',
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      isEdited: false,
      dbdId: [],
      viewdata: [],
      success: false,
      oldDataArr: [],
      newDataArr: [],
      prevDataArr: [],
      editedRowId: null,
      columnDefs: [
        {
          headerName: 'Chart Id',
          chartDataType: 'series',
          width: 100,
          minWidth: 100,
          flex: 1,
          field: 'chartId',
          hide: false,
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            let chartIdA = parseInt(valueA);
            let chartIdB = parseInt(valueB);
            return chartIdA - chartIdB;
          }
        },
        {
          headerName: 'Chart Name',
          //chartDataType: 'series',
          width: 300,
          minWidth: 300,
          flex: 1,
          field: 'chartName',
          editable: false,

          singleClickEdit: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: function(valueA, valueB, nodeA, nodeB, isInverted) {
            valueA = valueA.trim();
            valueB = valueB.trim();
            // Convert both values to lowercase for case-insensitive comparison
            var lowerCaseA = valueA && valueA.toLowerCase();
            var lowerCaseB = valueB && valueB.toLowerCase();

            // Perform a standard string comparison on the lowercase values
            if (lowerCaseA === lowerCaseB) {
              return 0;
            } else if (lowerCaseA < lowerCaseB) {
              return -1;
            } else {
              return 1;
            }
          }

          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.length == 1

          //   && (this.props.keycloak.realmAccess.roles.includes('client')) === true
          //     ? false
          //     : true
        },
        {
          headerName: 'Site Position',
          chartDataType: 'series',
          width: 200,
          minWidth: 200,
          flex: 1,
          field: 'dbdName',
          editable: false,
          cellEditor: 'agSelectCellEditor',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          cellEditorParams: function(params) {
            return {
              values: startEdit.getDbdNames()
            };
          }
        },
        {
          headerName: 'Active / Inactive',
          chartDataType: 'series',
          width: 95,
          minWidth: 95,
          field: 'active',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          flex: 1,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          // cellRenderer: params => {
          //   var index = params.rowIndex;
          //   return `<input type='checkbox' value=${
          //     params.value
          //   } disabled="disabled"
          //   id="techStatus${index}"
          //   ${params.value == 1 ? 'checked' : ''} />`;
          // }
          cellRenderer: 'activeStatusRenderer'
        },
        {
          headerName: 'Description',
          chartDataType: 'series',
          width: 300,
          field: 'description',
          tooltipField: 'description',
          minWidth: 300,
          flex: 1,

          resizable: true,
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: (valueA, valueB) => {
            // Handle null or undefined values
            const trimmedA = valueA ? valueA.trim().toLowerCase() : '';
            const trimmedB = valueB ? valueB.trim().toLowerCase() : '';

            return trimmedA.localeCompare(trimmedB);
          }
          // typeof this.props.keycloak.realmAccess.roles != 'undefined'
          // && this.props.keycloak.realmAccess.roles.length >= 1

          // && (this.props.keycloak.realmAccess.roles.includes('client')) === true
          //   ? false
          //   : true
        },
        {
          headerName: 'View Name',
          width: 400,
          flex: 1,
          field: 'viewDetails',

          suppressMenu: true,
          unSortIcon: true,
          hide: true,
          editable:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('client') === true
              ? false
              : true,
          cellEditor: 'agSelectCellEditor',
          cellStyle() {
            return { border: ' 0px white' };
          },
          cellEditorParams: function(params) {
            return { values: startEdit.getViewNames(params) };
          }
        },
        {
          headerName: 'Dashboard Id',
          chartDataType: 'series',
          width: 350,
          field: 'dbdId',
          hide: true,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          }
        },
        {
          headerName: 'Action',
          field: 'action',
          cellRenderer: 'buttonRenderer',
          width: 100,
          filter: false,
          sortable: false,
          flex: 1,

          suppressMenu: true,
          // tooltip: function(params) {
          //  //return 'Edit';
          // },
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          hide:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('superadmin') ===
              true
              ? false
              : true,
          editable: false,
          cellRenderer: function(params) {
            var index = params.rowIndex;
            var eDiv = document.createElement('div');
            eDiv.innerHTML =
              '<button title="Edit" id="btnedit' +
              index +
              '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel' +
              index +
              '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate' +
              index +
              '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>';
            if (index != undefined) {
              var eButton = eDiv.querySelectorAll('.edit-button')[0];
              var uButton = eDiv.querySelectorAll('.update-button')[0];
              var cButton = eDiv.querySelectorAll('.cancel-button')[0];
              eButton.addEventListener('click', () => {
                startEdit.gridApi.redrawRows();
                localStorage.setItem('index', index);
                startEdit.setState({ cancel: false });
                startEdit.onBtStartEditing(index);
                localStorage.setItem('chartId', params.data.id);
                startEdit.setState({
                  editedRowId: index
                });
                let oldArr = {
                  index: index,
                  chartName: params.data.chartName,
                  description: params.data.description,
                  active: params.data.active
                };

                var rowPrevArray = [];
                let indexArr = rowPrevArray.findIndex(
                  ({ chartName }) => chartName == params.data.chartName
                );
                console.log('eee==', oldArr, rowPrevArray, indexArr);
                if (indexArr === -1) {
                  rowPrevArray.push(oldArr);
                }
                console.log('eee==', oldArr, rowPrevArray, indexArr);
                startEdit.setState({
                  oldDataArr: rowPrevArray
                });
                // startEdit.setState({
                //   oldDataArr: oldArr
                // });
                $('#btnedit' + index).hide();
                $('#btncancel' + index).show();
                $('#btnupdate' + index).show();
              });

              uButton.addEventListener('click', () => {
                startEdit.onBtStopEditing(index);
                /*var selectedChartId = params.data.chartId;
                var selectedChart = params.data.chartName;
                var selectedChartDescription = params.data.description;
                var selectedDbd = params.data.dbdName;
                var selectedView = params.data.viewDetails;
                startEdit.updateChartMaster(
                  selectedChartId,
                  selectedChart,
                  selectedChartDescription,
                  selectedDbd,
                  selectedView
                );*/
                startEdit.setState({ cancel: false });
                startEdit.setState({
                  editedRowId: null
                });
                let arr = startEdit.state.oldDataArr;
                // $('#btnedit' + index).show();
                // $('#btncancel' + index).hide();
                // $('#btnupdate' + index).hide();
                console.log('array=', arr, params.data);
                if (
                  arr[0].chartName != params.data.chartName ||
                  arr[0].active != params.data.active ||
                  arr[0].description != params.data.description
                ) {
                  if (
                    (arr[0].description == '' &&
                      params.data.description == undefined &&
                      arr[0].chartName == '' &&
                      params.data.chartName == undefined) ||
                    (params.data.chartName == '' &&
                      params.data.chartName == undefined) ||
                    ((params.data.description == undefined ||
                      arr[0].description == '') &&
                      (params.data.chartName == undefined ||
                        (arr[0].chartName == '' &&
                          params.data.chartName == undefined)))
                  ) {
                    $('#btnedit' + index).show();
                    $('#btncancel' + index).hide();
                    $('#btnupdate' + index).hide();
                  } else {
                    var selectedChartId = params.data.chartId;
                    var selectedChart = params.data.chartName;
                    var selectedChartDescription = params.data.description;
                    var selectedDbd = params.data.dbdName;
                    var selectedView = params.data.viewDetails;
                    startEdit.updateChartMaster(
                      selectedChartId,
                      selectedChart,
                      selectedChartDescription,
                      selectedDbd,
                      selectedView
                    );

                    let rowData = startEdit.state.oldDataArr;
                    rowData.map((object, i) => {
                      if (index != object.index) {
                        let rowNode = params.api.getDisplayedRowAtIndex(
                          object.index
                        );
                        rowNode.setDataValue('chartName', rowData[i].chartName);
                        rowNode.setDataValue('active', rowData[i].active);
                        rowNode.setDataValue(
                          'description',
                          rowData[i].description
                        );

                        console.log(
                          'selectedChart=',
                          selectedChart == rowData[i].chartName,
                          selectedChart,
                          rowData[i].chartName,
                          selectedChart.length,
                          rowData[i].chartName.length
                        );

                        $('#btnedit' + object.index).show();
                        $('#btncancel' + object.index).hide();
                        $('#btnupdate' + object.index).hide();
                      }
                      // $('#advisorStatus' + object.index).prop(
                      //   'checked',
                      //   rowData.active == 1 ? true : false
                      // );
                      // $('#advisorStatus' + object.index).prop('disabled', 'disabled');
                    });

                    startEdit.setState({
                      oldDataArr: []
                    });
                  }
                } else {
                  $('#btnedit' + index).show();
                  $('#btncancel' + index).hide();
                  $('#btnupdate' + index).hide();
                }
              });
              cButton.addEventListener('click', function() {
                startEdit.setState({ cancel: true });
                startEdit.onBtStopEditing(index);
                startEdit.setState({
                  editedRowId: null
                });
                let rowData = startEdit.state.oldDataArr;
                rowData.map((object, i) => {
                  let rowNode = params.api.getDisplayedRowAtIndex(object.index);
                  rowNode.setDataValue('chartName', rowData[i].chartName);
                  rowNode.setDataValue('active', rowData[i].active);
                  rowNode.setDataValue('description', rowData[i].description);
                  $('#btnedit' + object.index).show();
                  $('#btncancel' + object.index).hide();
                  $('#btnupdate' + object.index).hide();
                  // $('#advisorStatus' + object.index).prop(
                  //   'checked',
                  //   rowData.active == 1 ? true : false
                  // );
                  // $('#advisorStatus' + object.index).prop('disabled', 'disabled');
                });
                // var rowNode = params.api.getDisplayedRowAtIndex(index);
                // rowNode.setDataValue('chartName', rowData[0].chartName);
                // rowNode.setDataValue('active', rowData[0].active);
                // rowNode.setDataValue('description', rowData[0].description);
                // $('#btnedit' + index).show();
                // $('#btncancel' + index).hide();
                // $('#btnupdate' + index).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],

      editType: 'fullRow',
      chartName: null,
      context: { componentParent: this },
      frameworkComponents: {
        activeStatusRenderer: ActiveStatusRenderer
      },
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        suppressMovable: false,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        }
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },

        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[1]['editable'] = false;
    groupColumn[4]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  updateChartMaster = (
    chartId,
    chartName,
    description,
    dbdname,
    viewdetails
  ) => {
    var dashboardArr = this.state.dbdId;
    var dbdIdArr = [];
    for (let value of dashboardArr) {
      dbdIdArr.push({ id: value['dbdId'], dbd: value['dbdName'] });
    }
    var selectedId = dbdIdArr.filter(x => x.dbd === dbdname);
    if (selectedId.length > 0) {
      var selectedDbdId = selectedId[0]['id'];
    }
    const start = new Date();
    // if (this.state.isEdited) {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_CHART_MASTER,
        variables: {
          chart_id: chartId,
          chart_name: chartName,
          description: description,
          dbdname: dbdname,
          // dbd_Id:
          dbd_id: selectedDbdId,
          viewdetails: viewdetails,
          userid: localStorage.getItem('userID'),
          inUpdatedOnRealm: this.props.keycloak.realm
        }
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/ChartMaster',
          origin: '',
          event: 'Menu Load',
          is_from: 'UPDATE_CHART_MASTER',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        //alert('Chart information updated!');
        this.setState({ isLoading: true });
        this.setState({ success: true });
        getAllChartDetails();
        this.getAgGridData();
        //window.location.reload(false);

        // client
        //   .mutate({
        //     mutation: UPDATE_CHART_MASTER,
        //     variables: {
        //       chart_id: chartId,
        //       chart_name: chartName,
        //       description: description,
        //       dbdname: dbdname,
        //       dbd_id: selectedDbdId,
        //       viewdetails: viewdetails,
        //       userid: localStorage.getItem('userID'),
        //       inUpdatedOnRealm: this.props.keycloak.realm
        //     }
        //   })
        //   .then(result => {});
      });
    // }
    this.setState({ isEdited: false });
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['chartName', 'active', 'description'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['chartName', 'active', 'description'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    const filterValues = e.api.getFilterModel();
    this.gridApi.redrawRows();
  };

  onSortChanged = e => {
    this.gridApi.redrawRows();
  };

  onBtStartEditing = (index, key, char, pinned) => {
    // columnController
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[1]['editable'] = true;
    groupColumn[4]['editable'] = true;
    this.state.rawGridApi.setColumnDefs(groupColumn);
    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    this.state.rawGridApi.setFocusedCell(
      index,
      'description',
      'chartName',
      pinned
    );
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'chartName',
      colKey: 'description',
      //colKey: 'dbdName',

      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridColumnApi: params.columnApi });
    //  this.gridApi.sizeColumnsToFit();

    this.getAgGridData();
    this.getDbdData();
    // this.getViewData();
  };
  onCellValueChanged = params => {
    if (this.state.cancel == false) {
      if (params.newValue != params.oldValue) {
        this.setState({ isEdited: true });
        var selectedChartId = params.data.chartId;
        var selectedChart = params.data.chartName;
        var selectedChartDescription = params.data.description;
        var selectedDbd = params.data.dbdName;
        var selectedView = params.data.viewDetails;
        // this.updateChartMaster(
        //   selectedChartId,
        //   selectedChart,
        //   selectedChartDescription,
        //   selectedDbd,
        //   selectedView
        // );
      }
    }
  };
  // resetReportGrid = () => {
  //   this.gridApi.setColumnDefs([]);
  //   this.gridApi.setColumnDefs(this.state.columnDefs);
  // };
  resetReportGrid = () => {
    // this.gridApi.setColumnDefs([]);
    this.gridApi.setColumnDefs(this.state.columnDefs);

    this.setState({
      editedRowId: null
    });
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
    this.getAgGridData('reset');
  };
  getAgGridData(reset) {
    if (reset != 'reset') {
      this.setState({ isLoading: true });
    }
    this.setState({
      rowData: []
    });
    // this.setState({ isLoading: true });
    getAllChartDetailsForDisplay(result => {
      let orderedData = [];

      if (result.data.fdwStatefulServiceConfigurationChartMasters.nodes) {
        orderedData = lodash.orderBy(
          result.data.fdwStatefulServiceConfigurationChartMasters.nodes,
          'active',
          'desc'
        );
        this.setState({
          rowData: orderedData
        });
        this.setState({ isLoading: false });
        // this.onGroupExpandedOrCollapsed();
      }
    });
  }
  getDbdData() {
    getDbdDetails(result => {
      var dashboardArr = '';
      if (result.data.statelessCcPhysicalRwDashboardLists.nodes) {
        dashboardArr = result.data.statelessCcPhysicalRwDashboardLists.nodes;

        this.setState({ dbdId: dashboardArr });
      }
    });
  }
  getViewData() {
    getViewDetails(result => {
      if (result.data.dms_physical_rw_vw_tbl_views) {
        var viewArray = result.data.dms_physical_rw_vw_tbl_views;
      }
      this.setState({ viewdata: viewArray });
    });
  }

  getDbdNames = () => {
    var dbdNames = [];
    var dashboardArr = this.state.dbdId;

    for (let value of dashboardArr) {
      dbdNames.push(value['dbdName']);
    }

    return dbdNames;
  };
  getViewNames = Params => {
    var viewNames = [];
    var viewData = [];
    var dashboardArr = this.state.dbdId;
    var viewArray = this.state.viewdata;

    for (let value of viewArray) {
      viewData.push({ view: value['mvname'], schema: value['schemaname'] });
    }
    var selectedId = dashboardArr.filter(
      x => x.dbdName === Params.data.dbdName
    );
    if (selectedId.length > 0) {
      var schemaName = selectedId[0]['schemaname'];
      var schemaViews = viewData.filter(x => x.schema === schemaName);

      for (let value of schemaViews) {
        viewNames.push(value['view']);
      }
    }

    return viewNames;
  };
  onBtExport = () => {
    const allColumnKeys = this.state.columnDefs
      .filter(col => col.field !== 'action' && !col.hide)
      .map(col => col.field);

    const params = {
      sheetName: 'Report',
      fileName: 'Chartmaster',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Chart Master' },
            mergeAcross: 3
          }
        ]
      ],
      columnKeys: allColumnKeys
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  getDashboardIds = selectedDbd => {
    var schemaName = [];
  };
  handleOk = () => {
    this.setState({ success: false });
  };
  onCellClicked = params => {
    let index = localStorage.getItem('index');
    const id = localStorage.getItem('chartId');
    if (params.data.id != id) {
      $(`.edit-button`).show();
      $(`.update-button`).hide();
      $(`.cancel-button`).hide();

      this.setState({ cancel: true });
      this.onBtStopEditing(index);
      this.setState({ editedRowId: null });
      let rowData = this.state.oldDataArr;
      rowData.map((object, i) => {
        let rowNode = params.api.getDisplayedRowAtIndex(object.index);
        rowNode.setDataValue('chartName', rowData[i].chartName);
        rowNode.setDataValue('active', rowData[i].active);
        rowNode.setDataValue('description', rowData[i].description);
        $('#btnedit' + object.index).show();
        $('#btncancel' + object.index).hide();
        $('#btnupdate' + object.index).hide();
        $('#btndelete' + object.index).hide();
        $('#advisorStatus' + object.index).prop(
          'checked',
          rowData.active == 1 ? true : false
        );
        $('#advisorStatus' + object.index).prop('disabled', 'disabled');
      });
      this.gridApi.redrawRows();
      // var rowNode = params.api.getDisplayedRowAtIndex(index);
      // rowNode.setDataValue('chartName', rowData[0].chartName);
      // rowNode.setDataValue('active', rowData[0].active);
      // rowNode.setDataValue('description', rowData[0].description);
    }
  };

  render() {
    const { classes } = this.props;
    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,

            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Tab
              label={<div>Chart Master</div>}
              value="one"
              style={{ pointerEvents: 'none', textTransform: 'none' }}
            />
            {/* <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{ paddingTop: 9, paddingRight: 141, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
          </Tabs>
          <Button
            variant="contained"
            id="reset-layout"
            className={clsx(classes.back, 'reset-btn')}
            onClick={this.resetReportGrid}
          >
            <RestoreIcon />
            <Typography variant="body1" align="left">
              Reset Layout
            </Typography>
          </Button> */}

            <Link
              id="export-to-excel"
              style={{ paddingRight: 12, cursor: 'pointer', marginTop: 11 }}
              onClick={this.onBtExport}
            >
              <Tooltip title="Export To Excel">
                <ExportIcon />
              </Tooltip>
            </Link>
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.success}
        >
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Chart Information Updated!
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleOk} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: window.innerHeight - 170 + 'px',
            //height: '600px',
            width: '1116px',
            margin: 8,
            display: this.state.tabSelection == 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            // onCellValueChanged={this.onCellValueChanged.bind(this)}
            rowData={this.state.rowData}
            editType={this.state.editType}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            container="body"
            suppressClickEdit={true}
            onCellClicked={this.onCellClicked}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            context={this.state.context}
            components={this.state.components}
            onRowEditingStarted={this.onRowEditingStarted}
            onRowEditingStopped={this.onRowEditingStopped}
            onFilterChanged={this.onFilterChanged}
            onSortChanged={this.onSortChanged}
            frameworkComponents={this.state.frameworkComponents}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: 8,
    width: 'auto'
  }
});

export default withStyles(styles)(withKeycloak(ChartMaster));
