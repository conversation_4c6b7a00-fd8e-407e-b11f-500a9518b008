import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import ChartMaster from './ChartMaster';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { withKeycloak } from '@react-keycloak/web';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function ChartMasterEdit(keycloak) {
  const classes = useStyles();
  const session = useSelector(state => state.session);

  return (
    <Page className={classes.root} title="Chart Master">
      {keycloak.keycloak.realmAccess.roles.includes('client') ||
      keycloak.keycloak.realmAccess.roles.includes('user') ||
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <ChartMaster session={session} />
      )}

      {/* */}
    </Page>
  );
}

export default withKeycloak(ChartMasterEdit);
