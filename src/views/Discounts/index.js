import React, { useEffect, useState, useRef } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import Charts from './Charts';
import { setToken, setRefreshStatus } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { setNavItems, setMenuSelected } from 'src/actions';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function Discounts() {
  const classes = useStyles();
  const history = useHistory();
  const session = useSelector(state => state.session);
  const dispatch = useDispatch();
  useEffect(() => {
    // $('#navBarDiv .active-menu').removeClass('active-menu');
    // $('#navBarDiv')
    //   .find("a[id='Special Metrics']")
    //   .addClass('active-menu');

    dispatch(setNavItems(['Discount Metrics']));
    dispatch(setMenuSelected('Discount Metrics'));
  }, []);
  return (
    <Page className={classes.root} title="Discount">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts history={history} session={session} />
      )}
    </Page>
  );
}

export default Discounts;
