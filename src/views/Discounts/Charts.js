import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { Paper, Divider } from '@material-ui/core';
import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import DashboardActions from 'src/components/DashboardActions';
import Typography from '@material-ui/core/Typography';
import {
  getDataGridConfiguration,
  getLayoutConfiguration,
  saveLayoutConfiguration,
  getComparisonMonths
} from '../../utils/Utils';
import MasterNonDiscountJobDetails from '../AnalyzeData/MasterNonDiscountJobDetails';
import ComparisonChartsGrid from '../Discounts/ComparisonChartsGrid';
import DiscountSummary from './../AnalyzeData/DiscountSummary';
import HasuraDashboardBarRenderer from 'src/components/charts/HasuraDashboardBarRenderer';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
import { withStyles } from '@material-ui/styles';
import { withKeycloak } from '@react-keycloak/web';
import PageHeader from 'src/components/PageHeader';
import clsx from 'clsx';
import 'src/styles.css';
import { traceSpan } from 'src/utils/OTTTracing';
import moment from 'moment';
import Button from '@material-ui/core/Button';
import Grid from '@material-ui/core/Grid';
import $ from 'jquery';
import ChartDialog from 'src/components/Dialog';
import RestoreIcon from '@material-ui/icons/Restore';

const ReactGridLayout = WidthProvider(RGL);
var lodash = require('lodash');
class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 35,
    // popupChartId: '',
    onLayoutChange: function() {}
  };
  componentDidMount(prevProps, prevState) {
    if (this.props && this.props.history.location) {
      console.log('ccc--7', this.props.history.location);

      if (this.props.history.location.selectedGrid) {
        setTimeout(() => {
          this.handleHighlightContainer(
            this.props.history.location.handleHighlight
          );
        }, 100);
      }
    }
  }
  constructor(props) {
    super(props);
    this.state = {
      checked: false,
      resetLayout: false,
      selected: this.props.history.location.selectedGrid,
      selectedChartId: this.props.history.location.handleHighlight,
      tabSelection:
        props.history.location.state &&
        props.history.location.state.tabSelection &&
        props.history.location.state.tabSelection != ''
          ? props.history.location.state.tabSelection
          : localStorage.getItem('discountTabSelection')
          ? localStorage.getItem('discountTabSelection')
          : 'one',
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-14') || {}
        )
      ),
      chartList: JSON.parse(global.localStorage.getItem('chart-master')),
      reloadCounter: 0
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }
  resetReportGrid = value => {
    this.setState({ resetLayout: true });
    this.setState(prevState => ({
      reloadCounter: prevState.reloadCounter + 1
    }));

    if (value) {
      this.setState({
        layout: [],
        checked: false
      });
    }
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    const container = document.querySelector('.makeStyles-container-3');
    if (prevBtn) {
      $('#' + prevBtn).removeClass('button-selected');
    }
    if (selectedId) {
      $('#' + selectedId).removeClass('grid-selected');
    }
    this.setState({ selected: '' });
    this.setState({ selectedChartId: '' });
    return this.state.layout;
  };
  setResetDashboard = () => {
    this.setState({ resetLayout: false });
  };
  callbackFunction = params => {
    if (params) {
      if (params.type == 'laborDiscount') {
        this.setState({
          month_year: params.month_year,
          serviceAdvisor: params.serviceAdvisor,
          type: params.type
        });
        this.setState({ tabSelection: 'one' });
      } else if (params.type == 'partsDiscount') {
        this.setState({
          month_year: params.month_year,
          serviceAdvisor: params.serviceAdvisor,
          type: params.type
        });
        this.setState({ tabSelection: 'one' });
      }
    }
  };
  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-14');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }
  handleTabChange = (event, newValue) => {
    localStorage.setItem('discountTabSelection', newValue);
    this.setState({ tabSelection: newValue });
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'DiscountMetrics TabClick',
      value: newValue,
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('DiscountMetrics TabClick', spanAttribute);
  };
  removeFav = value => {
    this.setState({
      isLoading: false
    });
  };
  handleChartPopup = value => {
    this.setState({
      popupChartId: value,
      open: true
    });
    this.handleChange();
    window.scrollTo(0, 0);
  };

  handleClosePopup = value => {
    let prevBtn = $('.button-selected').attr('id');
    console.log('state===handleClosePopup');
    // if(prevBtn &&prevBtn.split("-")[1]== this.state.popupChartId){
    if (prevBtn) {
      let selectedChart = prevBtn.split('-')[1];
      $('#chartContainterId-' + selectedChart).addClass('grid-selected');
      this.setState({ selected: true });
      var element = document.getElementById(
        'chartContainterId-' + selectedChart
      );

      element.scrollIntoView({ block: 'center' });
    }
    this.setState({
      popupChartId: '',
      open: false
    });
  };
  handleChange = () => {
    this.setState({
      checked: true
    });
  };
  gotoVisualization = (chartId, e) => {
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    const container = document.querySelector('.makeStyles-container-3');
    if (prevBtn) {
      $('#' + prevBtn).removeClass('button-selected');
    }
    if (selectedId) {
      $('#' + selectedId).removeClass('grid-selected');
    }
    var element = document.getElementById('chartContainterId-' + chartId);
    element.scrollIntoView({ block: 'center' });
    this.setState({ selected: true });
    this.setState({ selectedChartId: chartId });
    $('#chartContainterId-' + chartId).addClass('grid-selected');
    $('#chartid-' + chartId).addClass('button-selected');

    //$('#visualization_' + chartId).css('display', 'block');
  };
  handleHighlightContainer = chartId => {
    var div = document.getElementById('chartContainterId' + chartId);
    $('#chartid-' + chartId).addClass('button-selected');
    $('#chartContainterId-' + chartId).addClass('grid-selected');
    var element = document.getElementById('chartContainterId-' + chartId);
    if (element)
      setTimeout(() => {
        element.scrollIntoView({ block: 'center' });
      }, 100);
  };
  backTobutton = chartId => {
    let selectedId = $('.grid-selected').attr('id');
    var element = document.getElementById('chartContainterId-' + chartId);
    let prevBtn = $('.button-selected').attr('id');
    this.setState({ selected: false });
    this.setState({ selectedChartId: '' });

    if (
      typeof prevBtn != 'undefined' &&
      prevBtn &&
      prevBtn.split('-')[1] == chartId
    ) {
      if (prevBtn) {
        window.scrollTo(0, 0);
        $('#' + prevBtn).removeClass('button-selected');
      }

      if (selectedId) {
        $('#' + selectedId).removeClass('grid-selected');
      }
    } else {
      window.scrollTo(0, 0);
    }

    // element.scrollIntoView({ behavior: 'smooth', block: 'center' });
  };
  getChartname = str => {
    var newStr = str.replace('CP', '');
    return newStr;
  };
  getHighlighedDiv = chartId => {
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    const container = document.querySelector('.makeStyles-container-3');

    if (prevBtn && prevBtn.split('-')[1] == chartId) {
      if (prevBtn) {
        $('#' + prevBtn).removeClass('button-selected');
      }
      if (selectedId) {
        $('#' + selectedId).removeClass('grid-selected');
      }
      var element = document.getElementById('chartContainterId-' + chartId);
      element.scrollIntoView({ block: 'center' });

      $('#chartContainterId-' + chartId).addClass('grid-selected');
      this.setState({ selected: true });
      $('#chartid-' + chartId).addClass('button-selected');
    }
  };
  render() {
    let realm = this.props.keycloak.realm;

    let filteredResult = this.state.chartList.filter(
      item => item.dbdName == 'Discounts' && item.parentId == null
    );
    let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    let discountComparisonMonths =
      this.props.history.location.state &&
      this.props.history.location.state.discountComparisonMonths
        ? this.props.history.location.state.discountComparisonMonths
        : '';
    let closedDate = localStorage.getItem('closedDate');
    localStorage.setItem('itemTab', this.state.tabSelection);
    const { classes } = this.props;
    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          <div className={classes.parentContainer}>
            <Typography
              variant="h4"
              color="primary"
              className={classes.mainLabel}
            >
              Discount Metrics
            </Typography>
            <div>
              {closedDate ? (
                <Typography
                  variant="body1"
                  color="secondary"
                  align="right"
                  style={{
                    fontWeight: 'bold'
                  }}
                  className={clsx(classes.dataLabel, 'date-asof')}
                >
                  {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
                </Typography>
              ) : (
                ''
              )}
              <Button
                variant="contained"
                id="reset-layout"
                className={'reset-btn'}
                onClick={this.resetReportGrid}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </div>
          </div>
          {/* <PageHeader
            title={'Discount Metrics'}
            closedDate={this.state.closedDate}
           setResetDashboard={this.setResetDashboard}
          /> */}

          <Divider />
          <div>
            <Paper square style={{ marginTop: '10px' }}>
              <Tabs
                variant="scrollable"
                scrollButtons="auto"
                value={this.state.tabSelection}
                onChange={this.handleTabChange}
                indicatorColor="secondary"
                textColor="secondary"
                TabIndicatorProps={{ style: { display: 'none' } }}
              >
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Summary View</div>}
                  value="one"
                  className={
                    this.state.tabSelection == 'one'
                      ? classes.tabSelected
                      : null
                  }
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>By Service Advisor</div>}
                  value="two"
                  className={
                    this.state.tabSelection == 'two'
                      ? classes.tabSelected
                      : null
                  }
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>13 Month Trend</div>}
                  value="three"
                  className={
                    this.state.tabSelection == 'three'
                      ? classes.tabSelected
                      : null
                  }
                />
              </Tabs>
            </Paper>

            {this.state.tabSelection == 'two' ? (
              <ComparisonChartsGrid
                mon1={
                  discountComparisonMonths != ''
                    ? discountComparisonMonths[0]
                    : getComparisonMonths()[0]
                }
                mon2={
                  discountComparisonMonths != ''
                    ? discountComparisonMonths[1]
                    : getComparisonMonths()[1]
                }
                removeFav={this.removeFav}
                isFrom={'Discounts'}
                tabSelection={this.state.tabSelection}
                resetReport={this.state.resetLayout}
                setResetDashboard={this.setResetDashboard}
                session={this.props.session}
                key={this.state.reloadCounter}
              />
            ) : null}
            {this.state.tabSelection == 'one' ? (
              <DiscountSummary
                history={this.props.history}
                resetReport={this.state.resetLayout}
                setResetDashboard={this.setResetDashboard}
                session={this.props.session}
              />
            ) : null}
            {this.state.tabSelection == 'four' ? (
              <MasterNonDiscountJobDetails
                monthYear={
                  this.state.month_year ? this.state.month_year : '2020-05'
                }
                parentCallback={this.callbackFunction}
                session={this.props.session}
              />
            ) : null}
          </div>
          <Divider />
          {this.state.tabSelection == 'three' && (
            <Grid item xs={12} xl={12} className={classes.rankedTableContainer}>
              <Paper className={classes.reportTopBar}>
                <div>
                  <Grid container style={{ margin: 0 }} justifyContent="center">
                    {orderedData.map((item, index) => (
                      <Button
                        className={classes.reportButton}
                        id={'chartid-' + item.chartId}
                        variant="outlined"
                        onClick={() => {
                          this.gotoVisualization(item.chartId);
                        }}
                      >
                        {this.getChartname(item.chartName)}
                      </Button>
                    ))}
                  </Grid>
                </div>
              </Paper>
            </Grid>
          )}
          {this.state.tabSelection == 'three' ? (
            <ReactGridLayout
              {...this.props}
              layout={this.state.layout}
              onLayoutChange={this.onLayoutChange}
              isResizable={false}
              isDraggable={false}
            >
              {orderedData.map((item, index) => {
                return (
                  <div
                    className={
                      this.state.parent != 'Details'
                        ? clsx('diagram-section')
                        : clsx('diagram-section')
                    }
                    id={'chartContainterId-' + item.chartId}
                    key={index}
                    data-grid={getDataGridConfiguration(index)}
                    //onClick= {()=>this.getHighlighedDiv(item.chartId)}
                  >
                    <DashboardBarRenderer
                      handleClosePopup={this.handleClosePopup}
                      chartId={parseInt(item.chartId)}
                      isFrom={item.dbdName}
                      removeFav={this.removeFav}
                      tabSelection={this.state.tabSelection}
                      backTobutton={this.backTobutton}
                      handleHighlightContainer={this.handleHighlightContainer}
                      selected={this.state.selected}
                      selectedChartId={this.state.selectedChartId}
                      headerClick={this.getHighlighedDiv}
                      session={this.props.session}
                      key={this.state.reloadCounter}
                    />
                  </div>
                );
              })}
            </ReactGridLayout>
          ) : null}
          {/* <ChartDialog
            // open={this.state.open}
            chartId={this.state.popupChartId}
            filter={this.state.filters}
            handlePopupClose={this.handleClosePopup}
            chartType="bar"
            realm={realm}
          /> */}
        </Paper>
      </div>
    );
  }
}

const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  dividerStyle: {
    marginLeft: 0,
    marginRight: 0
  },
  root: {
    flexGrow: 1
    //width: '99%'
  },

  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  paperSub: {
    boxShadow: '0 3px 2px -2px #8080802b'
    // marginTop: '20px'
  },
  container: {
    alignItems: 'center',
    margin: '5px 0px'
    //width: '85%',
  },

  tabs: {
    // "& button[aria-selected='true']": {
    //   border: "5px solid red"
    // }
    '& button': {
      padding: 5
    },
    "& button[aria-selected='true']": {
      color: theme.palette.primary.main,
      textTransform: 'none',
      border: 'solid 1px',
      borderColor: theme.palette.primary.main,
      backgroundColor: theme.palette.primary.active
      // backgroundColor: theme.palette.primary.active
    }
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  sublLabel: {
    display: 'flex'
  },
  resetButton: {
    float: 'right'
  },
  mainLabel: {
    marginBottom: 10,
    display: 'flex',
    color: '#242f48'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    marginTop: 4,
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  },
  parentContainer: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  reportButton: {
    height: 24,
    marginLeft: 8,
    marginTop: 5,
    color: '#757575',
    borderRadius: '4px',
    border: '2px solid #7d97aa',
    textTransform: 'none',
    '@media (max-width: 1920px)': {
      fontSize: 12
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (min-width: 2304px)': {
      fontSize: 16
    }
  },
  rankedTableContainer: {
    display: 'flex',
    alignItems: 'center',
    marginTop: 5
  },
  rankButtonGrid: {
    // justifyContent: 'space-between'
  },
  reportTopBar: {
    //margin: 8,
    padding: 5,
    // marginRight: 3,
    // marginLeft: 3,
    display: 'flex',
    alignItems: 'center'
  }
});
export default withKeycloak(withStyles(styles)(Charts));
