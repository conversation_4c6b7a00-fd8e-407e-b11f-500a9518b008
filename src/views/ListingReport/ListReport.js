import React from 'react';
import { AgGridReact } from 'ag-grid-react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { withStyles, makeStyles } from '@material-ui/styles';
import { withKeycloak } from '@react-keycloak/web';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import EditIcon from '@material-ui/icons/Edit';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
// import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import {
  Tooltip,
  Typography,
  Paper,
  Button,
  TextField
} from '@material-ui/core';
import PropTypes from 'prop-types';
import CheckboxRenderer from './CheckboxRenderer';
import {
  getKpiSavedReportDetails,
  insertKPIReportName,
  deleteKPIReport
} from 'src/utils/hasuraServices';
import { getTimeZone } from '../../utils/Utils';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import MuiTableCell from '@material-ui/core/TableCell';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Checkbox from '@material-ui/core/Checkbox';
import Fade from '@material-ui/core/Fade';
import moment from 'moment';
import SuccessSnackbar from '../KPIReportComparative/SuccessSnackbar';
import clsx from 'clsx';

var Dealer = process.env.REACT_APP_DEALER;
const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);

class ListReport extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);

    this.state = {
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      openSaveDlg: false,
      openSnackbar: false,
      selectedRow: [],
      privateChecked: true,
      errorReport: false,
      requiredText: false,
      reportName: '',
      deleteClicked: false,
      open: false,
      openDialogueDelete: false,
      columnDefs: [
        {
          headerName: 'Report Name',
          field: 'report_name',
          width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Type',
          field: 'kpi_report_type',
          width: 80,
          minWidth: 80,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Visibility',
          field: 'visibility',
          width: 80,
          minWidth: 80,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          cellRendererFramework: CheckboxRenderer
        },
        // {
        //   headerName: 'Toggle Duration',
        //   field: 'toggle_duration',
        //   width: 120,
        //   minWidth: 120,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'Start Date',
        //   field: 'start_date',
        //   width: 140,
        //   minWidth: 140,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        // {
        //   headerName: 'End Date',
        //   field: 'end_date',
        //   width: 120,
        //   minWidth: 120,
        //   cellClass: 'textAlign',
        //   suppressMenu: true,
        //   unSortIcon: true
        // },
        {
          headerName: 'Created By',
          field: 'created_by',
          width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Created On',
          field: 'created_on',
          width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
        },
        {
          headerName: 'Actions',
          field: '',
          minWidth: 200,
          editable: false,
          suppressMenu: true,
          flex: 1,
          filter: 'agSetColumnFilter',
          sortable: false,
          suppressFilter: true,
          hide:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('superadmin') ===
              true
              ? false
              : true,
          cellRendererFramework: params => (
            <span style={{ display: 'flex', justifyContent: 'space-evenly' }}>
              <Tooltip title="Edit">
                <EditIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={'edit' + params.rowIndex}
                  style={{
                    width: 18,
                    left: '8',
                    top: '70%',
                    cursor: 'pointer'
                  }}
                  onClick={() => this.handleEditClick(params.data)}
                ></EditIcon>
              </Tooltip>
              <Tooltip title="Copy">
                <FileCopyOutlinedIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={'edit' + params.rowIndex}
                  className={
                    params.data.created_by != localStorage.getItem('userID') &&
                    params.data.visibility == 'public'
                      ? this.props.classes.copyIcon
                      : this.props.classes.copyIconDisable
                  }
                  onClick={() => this.handleCopyClick(params.data)}
                ></FileCopyOutlinedIcon>
              </Tooltip>
              <Tooltip title="Delete">
                <DeleteIcon
                  htmlColor="rgb(0, 61, 107)"
                  className={
                    (params.data.created_by == localStorage.getItem('userID') &&
                      params.data.visibility == 'private') ||
                    ((params.data.created_by ==
                      localStorage.getItem('userID') ||
                      (this.props.keycloak &&
                        this.props.keycloak.realmAccess &&
                        this.props.keycloak.realmAccess.roles &&
                        this.props.keycloak.realmAccess.roles.includes(
                          'superadmin'
                        ))) &&
                      params.data.visibility == 'public')
                      ? this.props.classes.deleteIcon
                      : this.props.classes.deleteIconDisable
                  }
                  id={'delete' + params.rowIndex}
                  onClick={() => this.handleDeleteClick(params.data)}
                />
              </Tooltip>
            </span>
          ),
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        }
      ],

      rowData: [],

      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        // enableFilter: false,
        sortable: true,
        //  filter: true,
        //  resizable: false,
        editable: true,
        suppressMovable: true,
        filter: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        }
      },
      editType: 'fullRow'
    };
  }

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.setState({ gridcolumnApi: params.columnApi });
    this.getAgGridData();
  };

  getAgGridData() {
    let storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    let userRole =
      this.props.keycloak &&
      this.props.keycloak.realmAccess &&
      this.props.keycloak.realmAccess.roles
        ? this.props.keycloak.realmAccess.roles[0]
        : null;

    var offset = getTimeZone();
    const userID = localStorage.getItem('userID');
    console.log('userID', userID, storeId, userRole, offset);
    getKpiSavedReportDetails(storeId, userRole, userID, offset, result => {
      if (result.data.statelessDbdKpiScorecardGetKpiSavedReportDetails.json) {
        var roData = JSON.parse(
          result.data.statelessDbdKpiScorecardGetKpiSavedReportDetails.json
        );
        console.log('dataArray', roData);
        // const dataArray = roData.map(item => []);

        let filteredData = roData.filter(item => item.visibility == 'private');
        console.log('filteredData', filteredData);
        if (userRole == 'superadmin') {
          this.setState({
            rowData: roData
          });
        } else {
          this.setState({
            rowData: filteredData
          });
        }
      }
    });
  }

  handleEditClick(params) {
    if (params.kpi_report_type == 'store') {
      this.props.history.push('/KPIReportStoreComparative');
    } else {
      this.props.history.push('/KPIReportComparative');
    }
  }

  handleDeleteClick(params) {
    this.setState({ deleteClicked: true });
    this.setState({ open: true });
    this.setState({ selectedRow: params });
  }

  handleCopyClick(params) {
    this.setState({ openSaveDlg: true });
    this.setState({ selectedRow: params });
  }
  getRowStyle = params => {
    if (params.data.visibility == 'private') {
      return { background: '#f2f2f2' }; // Grey background for private rows
    }
    return null; // Default background for other rows
  };
  handleCheckboxChange = () => {
    this.setState({ privateChecked: false });
  };
  handleCancelSaveReport = () => {
    this.setState({ openSaveDlg: false });
  };
  handleOkSaveReport = () => {
    console.log('report--', this.state, this.state.selectedRow);

    var iStoreId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];

    insertKPIReportName(
      'insert',
      this.state.reportName,
      this.state.selectedRow.start_date,
      this.state.selectedRow.end_date,
      this.state.selectedRow.kpi_report_type,
      this.state.selectedRow.kpi_ids,
      iStoreId,
      this.props.keycloak.realmAccess.roles[0],
      this.state.privateChecked ? 'private' : 'public',
      this.state.selectedRow.toggle_duration,
      result => {
        console.log('success data====', result);
        // if (
        //   result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results ==
        //   'Insert successful'
        // ) {
        if (
          result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results[0]
            .status == 1
        ) {
          this.setState({ errorReport: '' });
          this.setState({ openSaveDlg: false });
          this.setState({ requiredText: false });
          this.setState({ openSnackbar: true });
          this.getAgGridData();
        } else {
          this.setState({ requiredText: true });
          this.setState({
            errorReport:
              result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                .results[0].msg
          });
        }
      }
    );
  };

  onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s]*$/;

    if (!nameRegex.test(e.target.value) && e.target.value) {
      this.setState({ requiredText: '' });
    } else {
      this.setState({ errorReport: '' });
      this.setState({ reportName: e.target.value });
      this.setState({ requiredText: false });
    }
  };

  handleSnackbarClose = () => {
    this.setState({ openSnackbar: false });
  };

  handleCloseDelete = () => {
    this.setState({ open: false });
  };
  handleOkDelete = () => {
    deleteKPIReport(
      'delete',
      this.state.selectedRow.report_name,
      this.state.selectedRow.kpi_report_type,
      this.state.selectedRow.store_id,
      result => {
        console.log('success data====', result);
        if (
          result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results[0]
            .status == 1
        ) {
          this.setState({ errorReport: '' });
          this.setState({ open: false });

          this.setState({ requiredText: false });
          this.setState({ openSnackbar: true });
          this.getAgGridData();
        } else {
          this.setState({ requiredText: true });
          this.setState({
            errorReport:
              result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                .results[0].msg
          });
        }
      }
    );
  };
  handleCloseDeleteDialog = () => {
    this.setState({ openDialogueDelete: false });
  };

  render() {
    const { classes } = this.props;

    return (
      <div>
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: `${window.innerHeight - 200}px`,
            width: '750px',
            margin: '6px',

            display: this.state.tabSelection === 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '400px',
              width: '100%'
            }}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            suppressHorizontalScroll={true}
            suppressClickEdit={true}
            animateRows={true}
            enableCharts={true}
            getRowStyle={this.getRowStyle}
            suppressContextMenu={true}
          />
        </div>

        <Dialog
          transition={Fade}
          classes={{
            paper: classes.paper
          }}
          BackdropProps={{
            classes: {
              root: classes.backDrop
            }
          }}
          //maxWidth="xl"
          style={{ maxWidth: 900, maxHeight: 700 }}
          open={this.state.openSaveDlg}
        >
          {/* <Dialog
            fullWidth
            maxWidth="sm"
            aria-labelledby="confirmation-dialog-title"
            open={openSaveDlg}
          > */}
          <DialogTitle id="form-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Copy - {this.state.selectedRow.report_name}
            </Typography>
          </DialogTitle>

          <DialogContent style={{ overflowX: 'hidden' }}>
            <TableContainer
              component={Paper}
              style={{
                margin: 4,
                padding: 1,
                display: 'block',
                width: '100%'
              }}
            >
              <Table
                className="email-table"
                id="maildetails"
                // style={{ minWidth: 300 }}
                size="small"
                aria-label="a dense table"
              >
                <TableHead
                  style={{
                    textAlign: 'center',
                    backgroundColor: '#003d6b'
                  }}
                ></TableHead>
                <TableRow key={'email'}>
                  <TableCell
                    align="left"
                    size="small"
                    style={{
                      fontSize: 14,
                      color: '#003d6b'
                    }}
                  >
                    Report Name
                  </TableCell>
                  <TableCell
                    align="left"
                    colSpan={2}
                    size="small"
                    style={{
                      fontSize: 14,
                      color: '#003d6b'
                    }}
                  >
                    <div style={{ display: 'flex' }}>
                      <div
                        style={{
                          display: 'inline-block',
                          width: 118,
                          marginLeft: 16
                        }}
                      >
                        Access Type
                      </div>
                    </div>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell height={10}>
                    <TextField
                      autoFocus
                      onChange={this.onChangeReportName}
                      value={this.state.reportName}
                      helperText={
                        (this.state.reportName == undefined ||
                          this.state.reportName == '') &&
                        this.state.requiredText
                          ? 'This is required!'
                          : this.state.errorReport
                          ? this.state.errorReport
                          : ''
                      }
                      margin="dense"
                      id="name"
                      type="text"
                      fullWidth
                    />
                  </TableCell>
                  <TableCell>
                    {/* <FormGroup style={{ marginTop: '19px' }}> */}
                    <Table>
                      <TableRow>
                        <TableCell
                          align="left"
                          size="small"
                          style={{
                            fontSize: 14,
                            color: '#003d6b'
                          }}
                        >
                          <FormControlLabel
                            control={
                              <Checkbox
                                defaultChecked
                                size="small"
                                color="primary"
                                checked={this.state.privateChecked}
                                onChange={this.handleCheckboxChange}
                              />
                            }
                            label={
                              <Typography className={classes.formControlLabel}>
                                Private
                              </Typography>
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <div>{moment().format('MM-DD-YYYY')}</div>
                        </TableCell>
                      </TableRow>
                    </Table>
                  </TableCell>
                </TableRow>
              </Table>
            </TableContainer>
            {/* {error && <p className="error">{error}</p>}
              {errorChecked && <p className="errorChk">{errorChecked}</p>} */}
          </DialogContent>
          <DialogActions>
            <Button
              variant="contained"
              className={clsx('reset-btn')}
              onClick={this.handleCancelSaveReport}
              color="primary"
            >
              Cancel
            </Button>

            <Button
              variant="contained"
              className={clsx('reset-btn')}
              onClick={this.handleOkSaveReport}
              color="primary"
              //  disabled={filterDisabled}
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>

        <SuccessSnackbar
          onClose={this.handleSnackbarClose}
          open={this.state.openSnackbar}
          msg={
            this.state.deleteClicked == true
              ? 'Report deleted successfully!'
              : 'Report Copied successfully!'
          }
          //goalFail={this.state.goalFail}
        />

        {this.state.deleteClicked == true ? (
          <Dialog
            open={this.state.open}
            onClose={this.handleCloseDeleteDialog}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                Are you sure you want to delete?
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.handleCloseDelete}>Cancel</Button>
              <Button onClick={this.handleOkDelete} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
      </div>
    );
  }
}

ListReport.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};
const styles = theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'

    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  linkItem: {
    cursor: 'pointer'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  },
  paper: {
    '@media (max-width: 1920px)': {
      maxWidth: '1150px',
      maxHeight: '700px'
    },

    '@media (max-width: 1440px)': {
      maxWidth: '1500px',
      maxHeight: '610px'
    },

    '@media (max-width: 1280px)': {
      maxWidth: '1500px',
      maxHeight: '600px'
    }
  },
  copyIcon: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  copyIconDisable: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer',
    pointerEvents: 'none',
    color: 'gray'
  },
  deleteIcon: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  deleteIconDisable: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer',
    pointerEvents: 'none',
    color: 'gray'
  }
});
export default withKeycloak(withStyles(styles)(ListReport));
