import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  Grid,
  Typography,
  CircularProgress,
  Box,
  Paper,
  TextField,
  InputLabel,
  MenuItem,
  Select,
  Divider,
  Button,
  IconButton,
  Tooltip
} from '@material-ui/core';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import { withStyles } from '@material-ui/core/styles';
import DashboardActions from 'src/components/DashboardActions';
import { GROUP_TYPE_2 } from 'src/utils/constants';
import {
  getDataGridConfiguration,
  getLayoutConfiguration,
  saveLayoutConfiguration
} from '../../utils/Utils';
import PartsOpportunityCharts from 'src/components/charts/OpportunityCharts';
import 'src/styles.css';
import { withKeycloak } from '@react-keycloak/web';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';
import OpportunityGrid from '../../components/OpportunityGrid';
import AdvisorOpportunityCharts from '../../components/charts/AdvisorOpportunityCharts';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
var lodash = require('lodash');

const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 35,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      openForm: false,
      formData: [],
      formState: [],
      chartData: [],
      baseline: '',
      editForm: false,
      totalOpportunity: '',
      refreshChart: false,
      isLoading: false,
      checkSuccess: false,
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-1') || {}
        )
      ),
      chartList: JSON.parse(global.localStorage.getItem('chart-master'))
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }
  componentDidMount = () => {
    getAllServiceAdvisors(result => {
      if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
        this.setState({
          names: ['All'].concat(
            result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.map(
              e => (e ? e.serviceadvisor.toString() : '')
            )
          )
        });

        this.setState({
          advisorNames: ['All'].concat(
            result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.map(
              e =>
                e
                  ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active
                  : ''
            )
          )
        });
      }
    });
  };
  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }
    return this.state.layout;
  };
  setFilterCharts = value => {
    if (value === 2) {
      this.setState({ isLoading: true });
    } else if (value === 3) {
      this.setState({ isLoading: false });
      this.setState({ checkSuccess: false });
    }
  };

  setBaseLinePrts = value => {
    this.setState({ baselineStatus: value });
  };
  setGoalSettings = value => {
    this.setState({ goalSettingsStatus: value });
  };

  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-1');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }

  handleChange = value => {
    if (value) {
      this.setState({ checkSuccess: value });
    }
  };
  removeFav = value => {
    this.setState({
      isLoading: false
    });
  };
  handleClosePopup = value => {
    console.log('state===handleClosePopup');
    this.setState({
      popupChartId: '',
      open: false
    });
  };

  render() {
    let realm = this.props.keycloak.realm;
    let filteredResult = this.state.chartList.filter(
      item =>
        item.dbdName == 'CP Parts Gross Volume Opportunity ' &&
        item.parentId == null
    );
    let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    const { classes } = this.props;

    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          {/* <span style={{display: 'flex', justifyContent: 'space-between'}}> */}
          <span style={{ display: 'flex' }}>
            <Typography
              variant="h4"
              color="primary"
              gutterBottom
              className={clsx(classes.mainLabel, 'main-title')}
            >
              “What If” Opportunity - Hrs Per RO & Parts GP%
            </Typography>
            {this.props.session.kpiAdvisor.includes('All') == false ? (
              <Typography
                variant="h6"
                color="primary"
                gutterBottom
                className={clsx(classes.mainLabel, 'main-title')}
                style={{ marginLeft: '25%', marginTop: 3 }}
              >
                <span>Advisor: </span>
                {this.state.names &&
                this.state.names.length > 0 &&
                this.state.advisorNames &&
                this.state.advisorNames.length > 0
                  ? this.props.session.kpiAdvisor.map(data => (
                      <span style={{ fontWeight: 400, marginLeft: 5 }}>
                        {this.state.names.indexOf(data) > -1 === true
                          ? this.state.advisorNames[
                              this.state.names.indexOf(data)
                            ].split('-status-')[0] +
                            ' [' +
                            data +
                            ']'
                          : data}
                      </span>
                    ))
                  : ''}
              </Typography>
            ) : null}
          </span>
          <Divider classes={{ root: classes.dividerRoot }} />
          <OpportunityGrid
            resetDashboard={this.setResetDashboard}
            filterCharts={this.setFilterCharts}
            setTitle="CP Labor Gross &amp; Volume Opportunity"
            restrictFilters={true}
            chartGroup={GROUP_TYPE_2}
            goalSettings={true}
            checkSuccess={this.handleChange}
            baselineStatusLbr={this.setBaseLinePrts}
            goalSettingsLbr={this.setGoalSettings}
            chartId={926}
          />
          {this.state.baselineStatus != false &&
          (this.state.goalSettingsStatus == undefined ||
            this.state.goalSettingsStatus == true) ? (
            this.state.isLoading === true || this.state.checkSuccess == true ? (
              <Grid justify="center" className={classes.loaderGrid}>
                <CircularProgress size={60} />
              </Grid>
            ) : (
              <Grid
                container
                id="part-opportunity-grid"
                className={clsx(classes.container, 'diagram-section')}
              >
                {orderedData.map((item, index) => {
                  return (
                    <Grid item xs={12}>
                      <AdvisorOpportunityCharts
                        chartId={Number(item.chartId)}
                        showTopBar={true}
                        isStacked={Number(item.chartId) === 935 ? true : false}
                        isFromMetrices={
                          Number(item.chartId) === 938 ? true : false
                        }
                        title={getChartName(item.chartId)}
                        realm={realm}
                        handleClose={this.handleClosePopup}
                        removeFavourite={this.removeFav}
                      />
                      {/* <PartsOpportunityCharts
                        handleClosePopup={this.handleClosePopup}
                        removeFav={this.removeFav}
                        chartId={Number(item.chartId)}
                        realm={realm}
                      /> */}
                    </Grid>
                  );
                })}
              </Grid>
            )
          ) : null}
        </Paper>
      </div>
    );
  }
}
const styles = theme => ({
  root: {
    flexGrow: 1
    //width: '99%'
  },
  dividerRoot: {
    backgroundColor: '#4a4646'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  input: {
    margin: '0px 5px',
    width: '28%'
  },
  container: {
    alignItems: 'center',
    margin: '20px 0px'
  },
  loaderGrid: {
    height: 250,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  }
});
export default withStyles(styles)(withKeycloak(Charts));
