/* eslint-disable no-shadow */
/* eslint-disable react/default-props-match-prop-types */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  Fade,
  Grid,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import PropTypes from 'prop-types';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { traceSpan } from 'src/utils/OTTTracing';
import { AgGridReact } from '@ag-grid-community/react';
import RestoreIcon from '@material-ui/icons/Restore';
import 'src/grid.css';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { connect } from 'react-redux';
import {
  SET_PAY_TYPE_TAB,
  SET_REFRESH_STATUS,
  SET_REFRESH_ERROR_STATUS,
  SET_PAY_TYPE_ERROR,
  SET_PAY_TYPE_ERROR_COUNT,
  SET_DASHBOARD_RELOAD_STATUS
} from 'src/actions';

import { getPayTypeWithoutStore } from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';

// eslint-disable-next-line import/no-cycle
import { store } from '../../App';
// eslint-disable-next-line no-unused-vars

import { withStyles } from '@material-ui/styles';
import LoaderSkeleton from '../../components/LoaderSkeleton.js';
import { ReactSession } from 'react-client-session';
import GridCheckboxRenderer from './GridCheckboxRenderer';
import 'src/styles.css';
import PayType from './PayType';
var lodash = require('lodash');
class StoreAdvisors extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps, prevState) {
    if (
      prevProps.resetState !== this.props.resetState &&
      this.props.resetState == true
    ) {
      this.resetReportGrid();
    }

    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ isLoading: true });
        this.getAgGridData();
        this.resetReportGrid();
      }
    }
  }
  // eslint-disable-next-line react/static-property-placement
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData();
  //     }
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    const { keycloak } = this.props;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      setOpenAlertLog: false,
      showRefresh:
        this.props.session.dashboardReloadStatus == false ? 'none' : 'all',
      disableTable: 'all',
      fadein: true,
      rawGridApi: {},
      gridApi: {},
      oldRowArray: [],
      newRowArray: [],
      oldCodeArray: [],
      prevCodeArray: [],
      newCodeArray: [],
      gridExcludedValue: '',
      gridExcludedId: [],
      categorizedValue: '',
      editedRowId: null,
      isCodeEdited: false,
      categorizedId: '',
      opcategoryArray: [],
      isCodeUpdated: false,
      isCodeRowUpdated: false,
      openSaveDialog: false,
      isReloaded: false,
      laborPayTypeCount: '',
      PartsPayTypeCount: '',
      isLoading: true,
      isPaytypeLoading: false,
      isLoadingSave: false,
      laborFixedRateOld: '',
      laborFixedRateNew: null,
      laborFixedDateOld: '',
      laborFixedDateNew: null,
      partsFixedRateOld: '',
      partsFixedRateNew: null,
      partsFixedDateOld: '',
      partsFixedDateNew: null,
      tabSelection: 'one',
      columnDefs: [
        {
          headerName: 'Brand',
          width: 100,
          field: 'brand',

          hide: false,
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Pay Type',
          chartDataType: 'series',
          width: 120,
          field: 'payType',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          //flex: 1,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Assign To Store',
          chartDataType: 'series',
          width: 100,
          field: 'ptRevertChange',

          cellRenderer: 'gridCheckboxRenderer',
          suppressMenu: true,
          unSortIcon: true,
          hide: false,
          editable: false,
          // editable: !(
          //   typeof keycloak.realmAccess.roles !== 'undefined' &&
          //   keycloak.realmAccess.roles.includes('client') === true
          // ),
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },

        {
          headerName: 'Action',
          width: 90,
          filter: false,
          sortable: false,
          editable: false,

          suppressMenu: true,
          cellStyle: params => {
            return startEdit.state.isReloaded
              ? {
                  'pointer-events': 'none',
                  opacity: '0.4',
                  textAlign: 'center'
                }
              : { textAlign: 'center' };
          },

          hide:
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') == true
              ? true
              : false,
          // hide: true,
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            let index = params.rowIndex;
            const eDiv = document.createElement('div');
            eDiv.innerHTML = `<button   title="Edit" id="btneditspaytype${index}" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; cursor: pointer; border: 0; font-size: 12px; line-height: 13px;" class="edit-button-s-paytype"><i class="fas fa-pencil-alt"></i></button> <button  title="Cancel" id="btncancelspaytype${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-s-paytype" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdatespaytype${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-s-paytype" ><i class="fas fa-save"></i></button>`;
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button-s-paytype').attr('disabled', 'disabled');
                $('.edit-button-s-paytype').css('background', '#38416373');
              });
            }

            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll(
                '.edit-button-s-paytype'
              )[0];
              const uButton = eDiv.querySelectorAll(
                '.update-button-s-paytype'
              )[0];
              const cButton = eDiv.querySelectorAll(
                '.cancel-button-s-paytype'
              )[0];
              eButton.addEventListener('click', () => {
                // params.columnApi.getDisplayedCenterColumns()[0].colId
                $(`.cancel-button-s-paytype`).hide();
                $(`.update-button-s-paytype`).hide();
                $(`.edit-button-s-paytype`).show();

                $('.ptRevertChange').attr('disabled', 'disabled');
                localStorage.setItem('oldIdpay', params.data.payType);
                var oldRow = [];
                oldRow.push(params.data.brand);
                oldRow.push(params.data.payType);
                oldRow.push(params.data.ptRevertChange);

                JSON.stringify(oldRow);

                localStorage.setItem('oldRow', oldRow);
                startEdit.setState({
                  editedRowId: index
                });
                startEdit.setState({
                  editedRowPaytype: params.data.payType
                });
                startEdit.setState({
                  isCodeEdited: true
                });
                var rowPrev = {
                  brand: params.data.brand,
                  payType: params.data.payType,
                  ptRevertChange: params.data.ptRevertChange
                };

                var rowPrevArray = startEdit.state.prevCodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ brand, payType }) =>
                    brand === rowPrev.brand && payType === rowPrev.payType
                );

                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }

                startEdit.setState({
                  prevCodeArray: rowPrevArray
                });

                var prevRow = [];
                prevRow.push({
                  brand: params.data.brand,
                  payType: params.data.payType,
                  ptRevertChange: params.data.ptRevertChange
                });

                startEdit.setState({
                  oldCodeArray: prevRow
                });

                //startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);
                $(`#btneditspaytype${index}`).hide();
                $(`#btncancelspaytype${index}`).show();
                $(`#btnupdatespaytype${index}`).show();
              });
              uButton.addEventListener('click', () => {
                // index = params.rowIndex;

                startEdit.onBtStopEditing(index);
                localStorage.setItem('brand', params.data.brand);
                localStorage.setItem('brand', params.data.payType);
                localStorage.setItem(
                  'ptRevertChange',
                  params.data.ptRevertChange
                );

                const storeId = JSON.parse(
                  localStorage.getItem('selectedStoreId')
                )[0];
                startEdit.setState({
                  isCodeEdited: false
                });

                var rowNode = params.api.getDisplayedRowAtIndex(
                  params.rowIndex
                );

                startEdit.setState({
                  prevIndex: ''
                });

                startEdit.setState({
                  prevIndex: ''
                });
                var newRow = [];
                newRow.push(
                  params.data.brand,
                  params.data.payType,
                  params.data.ptRevertChange
                );
                let oldDataArr = lodash.filter(
                  startEdit.state.prevCodeArray,
                  item => {
                    return (
                      item.brand == params.data.brand &&
                      item.payType == params.data.payType
                    );
                  }
                );

                var updatedRow = {
                  paytype: params.data.payType,
                  brand:
                    params.data.ptRevertChange == 1 ? params.data.brand : 'Unk',
                  update_status: params.data.ptRevertChange == 1 ? 0 : 1
                };
                var rowArray = startEdit.state.newCodeArray;
                let index = rowArray.findIndex(
                  ({ brand, paytype }) =>
                    brand == updatedRow.brand && paytype == updatedRow.paytype
                );

                if (index === -1) {
                  if (
                    oldDataArr[0].ptRevertChange != params.data.ptRevertChange
                  ) {
                    rowArray.push(updatedRow);
                  }
                } else {
                  rowArray[index] = updatedRow;
                }
                startEdit.setState({
                  newCodeArray: rowArray
                });

                if (startEdit.state.newCodeArray.length > 0) {
                  //
                  startEdit.props.handlePaytypeUpdates(
                    true,
                    startEdit.state.newCodeArray,
                    startEdit.state.prevCodeArray
                  );
                }

                localStorage.setItem('newRow', newRow);
                // startEdit.updatePayTypeMaster(
                //   selectedId,
                //   selectedPayType,
                //   selectedPayTypeCode,
                //   selectedPayTypeDept,
                //   oldDepartment,
                //   storeId
                // );
                startEdit.setState({
                  editedRowId: null
                });

                startEdit.gridApi.redrawRows();
                $(`#btneditspaytype${index}`).show();
                $(`#btncancelspaytype${index}`).hide();
                $(`#btnupdatespaytype${index}`).hide();
              });
              cButton.addEventListener('click', () => {
                //startEdit.getAgGridData();
                startEdit.onBtStopEditing(index);
                startEdit.setState({
                  editedRowId: null
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return (
                    o.brand == params.data.brand &&
                    o.payType == params.data.payType
                  );
                });
                console.log('ccc===valArr', valArr);
                if (valArr.length > 0) {
                  rowNode.setDataValue(
                    'ptRevertChange',
                    valArr[0].ptRevertChange
                  );
                }

                let filteredArray = startEdit.state.newCodeArray.filter(
                  function(obj) {
                    return obj.brand != params.data.brand;
                  }
                );
                startEdit.setState({
                  newCodeArray: filteredArray
                });

                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                  startEdit.props.handlePaytypeUpdates(
                    false,
                    startEdit.state.newCodeArray,
                    startEdit.state.prevCodeArray
                  );
                }

                $(`#btneditspaytype${index}`).show();
                $(`#btncancelspaytype${index}`).hide();
                $(`#btnupdatespaytype${index}`).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
        // editable: true
      },
      editType: 'fullRow',
      frameworkComponents: {
        gridCheckboxRenderer: GridCheckboxRenderer
      },
      context: { componentParent: this },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },

        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  componentDidMount() {
    const { setTab } = this.props;
    if (store.getState().session.menuSelected == 'Pay Types') {
      setTab('one');
    }
  }
  onFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      fixedRateOld: oldVal
    });

    this.setState({
      fixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  getAgGridData() {
    this.setState({ isLoading: true });
    getPayTypeWithoutStore(result => {
      if (
        result.data.statelessCdkSourceRawGetPayTypesWithoutStore
          .statelessCdkSourceRawPayTypesWithoutStores
      ) {
        this.getDistinctPaytypeCode(
          result.data.statelessCdkSourceRawGetPayTypesWithoutStore
            .statelessCdkSourceRawPayTypesWithoutStores
        );
        var roData = lodash.orderBy(
          result.data.statelessCdkSourceRawGetPayTypesWithoutStore
            .statelessCdkSourceRawPayTypesWithoutStores,
          'payType',
          'asc'
        );

        this.setState({
          rowData: roData
        });
        this.setState({ isLoading: false });
      }
    });
  }

  getDistinctPaytypeCode(resultData) {
    const laborPaytype = resultData.filter(pay => pay.lbrOrPrts === 'L');
    const partsPaytype = resultData.filter(pay => pay.lbrOrPrts === 'P');
    const distinctLaborPaytype = Array.from(
      new Set(laborPaytype.map(item => item.payType))
    );
    const distinctPartsPaytype = Array.from(
      new Set(partsPaytype.map(item => item.payType))
    );
    const laborPaytypeCount = distinctLaborPaytype.length;
    const partsPaytypeCount = distinctPartsPaytype.length;
    this.setState({
      laborPayTypeCount: laborPaytypeCount,
      PartsPayTypeCount: partsPaytypeCount
    });
  }

  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;

    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  getRowStyle = params => {
    if (params.data.payTypeCode == null && params.data.department == null) {
      return { background: 'rgb(221, 234, 244)' };
    }
  };
  onLaborFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedRateOld: oldVal
    });

    this.setState({
      laborFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onLaborFixedDateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedDateOld: oldVal
    });

    this.setState({
      laborFixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedRateOld: oldVal
    });

    this.setState({
      partsFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedDateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedDateOld: oldVal
    });

    this.setState({
      partsFixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['ptRevertChange'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['ptRevertChange'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApi.redrawRows();
  };
  onSortChanged = e => {
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApi.redrawRows();
  };

  resetReportGrid = () => {
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApi.redrawRows();
    this.props.handleResetPaytype();
  };

  refreshDisable = () => {
    this.setState({ showRefresh: 'none' });
  };

  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = false;
      groupColumn[3]['editable'] = false;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    const { rawGridApi } = this.state;
    this.state.rawGridApi.setFocusedCell(
      index,
      'ptRevertChange',

      pinned
    );
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'ptRevertChange',

      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
    // rawGridApi.setFocusedCell(index, 'payTypeCode', pinned);
    // rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'payTypeCode',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
    // const { rawGridApi } = this.state;
    // rawGridApi.setFocusedCell(index, 'payTypeCode', pinned);
    // rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'payTypeCode',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  onBtExport = () => {
    const { rawGridApi } = this.state;
    const params = {
      sheetName: 'Report',
      fileName: 'StoreAdvisors',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Store Advisors' },
            mergeAcross: 3
          }
        ]
      ]
    };
    rawGridApi.exportDataAsExcel(params);
  };

  handleTabChange = (event, newValue) => {
    const { setTab } = this.props;
    // $('.edit-button-s-paytype').hide();

    setTab(newValue);
    const coll = document.getElementsByClassName('edit-button-s-paytype');
    // if (newValue === 'one') {
    //   for (let i = 0, len = coll.length; i < len; i++) {
    //     coll[i].style['background-color'] = '#ccc';
    //   }
    // }
    // $('.edit-button-s-paytype').hide();
    // $('.edit-button-s-paytype').css({
    //   'background-color': 'yellow',
    //   'font-size': '200%'
    // });
  };

  onBtDisable = () => {
    this.props.setDashboardReloadStatus(false);
    const { handleReload, handleRefresh } = this.props;
    this.props.setRefreshStatus(true);
    handleReload(false);
    this.props.setRefreshErrorStatus(false);
    const userID = localStorage.getItem('userID');
    const coll = document.getElementsByClassName('edit-button-s-paytype');
    for (let i = 0, len = coll.length; i < len; i++) {
      coll[i].style['background-color'] = '#ccc';
    }
    const storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.setState({ setOpenAlert: true });
    this.setState({ showRefresh: 'none' });
    this.setState({ disableTable: 'none' });
    handleRefresh(true);
    this.addRereshStatus(storeId, userID);
  };

  handleClose = () => {
    this.setState({ setOpen: false });
  };

  handleCloseRow = () => {
    this.setState({ setOpenRow: false });
  };

  onBtClose = () => {
    this.setState({ setOpenAlert: false });
  };

  onBtCloseError = () => {
    this.setState({ setOpenAlertError: false });
  };
  onBtCloseErrorLog = () => {
    this.setState({ setOpenAlertLog: false });
  };
  handleClickSaveOpcode = () => {
    this.setState({
      openSaveDialog: true
    });
  };
  handleCancel = () => {
    this.setState({
      openSaveDialog: false
    });
  };
  handleSave = () => {
    this.updateStoreAdvisors();
    this.setState({
      openSaveDialog: false
    });
  };
  handleChange = status => {
    const { handleReload, handleRefresh } = this.props;
    const coll = document.getElementsByClassName('edit-button-s-paytype');
    if (status === 0) {
      this.setState({ showRefresh: 'none' });
      this.setState({ disableTable: 'all' });
      this.setState({ setOpenRow: false });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    } else if (status === 1) {
      this.setState({ showRefresh: 'all' });
      this.setState({ disableTable: 'all' });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    } else if (status === 2) {
      handleReload(false);
      handleRefresh(true);
    } else if (status === 3) {
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
      handleReload(true);
      handleRefresh(false);
    }
  };
  onCellClicked = params => {
    const id = localStorage.getItem('oldIdpay');

    let rowId = this.state.editedRowId;
    if (params.data.payType != id) {
      $(`.edit-button-s-paytype`).show();
      $(`.update-button-s-paytype`).hide();
      $(`.cancel-button-s-paytype`).hide();
      $(`#btncancelspaytype` + rowId).click();
      $('.ptRevertChange').attr('disabled', 'disabled');
    }
  };

  render() {
    const {
      isLoading,
      fadein,
      autoGroupColumnDef,
      columnDefs,
      defaultColDef,
      rowData,
      excelStyles
    } = this.state;
    // const coll = document.getElementsByClassName('edit-button-s-paytype');
    // if (disableTable === 'none') {
    //   for (let i = 0, len = coll.length; i < len; i++) {
    //     coll[i].style['background-color'] = '#ccc';
    //   }
    // }
    const { keycloak, classes } = this.props;
    let boxClass = ['ag-header-cell'];
    // if(this.state.addClass) {
    boxClass.push('add');
    // }
    {
      boxClass.join(' ');
    }
    let abc = localStorage.getItem('selectedStoreId');

    return (
      <div>
        {store.getState().session.payTypeInitialTab === 'one' ? (
          <>
            {isLoading === true ? (
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            ) : null}
          </>
        ) : null}

        {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
        this.props.keycloak.realmAccess.roles.includes('client') != true &&
        store.getState().session.payTypeInitialTab === 'one' ? (
          <>
            <Typography
              style={{
                paddingLeft: '10px',
                display: this.state.isLoading == true ? 'none' : 'block',
                fontWeight: 'bold',
                color: '#003d6b',
                fontSize: 13
              }}
            >
              1) Please assign your store(s) here. You may edit as many as you
              like simultaneously, and then please click “Save Changes”.
            </Typography>

            {/* <Typography
               style={{
                 paddingLeft: '10px',
                 display:
                   this.state.isLoading != true &&
                   showRefresh == 'all' &&
                   this.state.isOpcodeLoading === false
                     ? 'block'
                     : 'none',
                 fontWeight: 'bold',
                 color: 'red',
                 fontSize: 13
               }}
             >
               3) There is a pending reload to the Dashboard. Please click on
               Reprocess Data History to refresh the data before proceeding to
               edit Opcodes.
             </Typography> */}
          </>
        ) : (
          ''
        )}

        {store.getState().session.payTypeInitialTab === 'one' && (
          <div className={classes.gridDiv}>
            <Fade in={fadein} timeout={1200}>
              <div
                id="data-tab"
                className={clsx(
                  this.props.keycloak.realmAccess.roles.includes('client') ===
                    true
                    ? classes.dataGridClient
                    : classes.dataGrid,
                  'ag-theme-balham'
                )}
                style={{
                  // pointerEvents: disableTable,
                  height: window.innerHeight - 250 + 'px',
                  // height: '600px',
                  //width: '100%',

                  alignContent: 'center',
                  marginLeft: '8px',
                  paddingRight: '16px',
                  display: this.state.isLoading == true ? 'none' : 'block'
                }}
              >
                {this.props.isLoadingSave && (
                  <div
                    style={{
                      position: 'absolute',
                      left: '58%',
                      zIndex: 1,
                      top: '50%'
                    }}
                  >
                    <LoaderSkeleton />
                  </div>
                )}
                <AgGridReact
                  className="ag-theme-balham grid-cell-centered "
                  //className={boxClass.join(' ')}
                  style={{
                    height: '500px',
                    width: '100%'
                  }}
                  onRowEditingStarted={this.onRowEditingStarted}
                  onRowEditingStopped={this.onRowEditingStopped}
                  onFilterChanged={this.onFilterChanged}
                  onSortChanged={this.onSortChanged}
                  getRowStyle={this.getRowStyle}
                  autoGroupColumnDef={autoGroupColumnDef}
                  getChartToolbarItems={this.getChartToolbarItems}
                  modules={AllModules}
                  columnDefs={columnDefs}
                  defaultColDef={defaultColDef}
                  onGridReady={this.onGridReady}
                  suppressAggFuncInHeader
                  rowData={rowData}
                  excelStyles={excelStyles}
                  editType={this.state.editType}
                  suppressClickEdit={true}
                  onCellClicked={this.onCellClicked}
                  floatingFilter={true}
                  context={this.state.context}
                  frameworkComponents={this.state.frameworkComponents}
                  // enableRangeSelection={true}
                  // animateRows={true}
                  // enableCharts={true}
                  suppressRowClickSelection={true}
                  suppressHorizontalScroll={true}
                  suppressDragLeaveHidesColumns={true}
                  suppressContextMenu={true}
                />
              </div>
            </Fade>
          </div>
        )}

        <Dialog
          //fullWidth
          //maxWidth="sm"
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openSaveDialog}
        >
          {/* <DialogTitle id="confirmation-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Save changes
            </Typography>
          </DialogTitle> */}
          <DialogContent>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Are you sure you want to continue?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={this.handleCancel}>
              Cancel
            </Button>
            <Button onClick={this.handleSave} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}
const partGrpMappings = {
  'N/A': 'N/A',
  'Part List': 'Part List',
  'Part Cost': 'Part Cost'
};
const departmentMappings = {
  'Body Shop': 'Body Shop',
  Service: 'Service'
};
const paytypeMappings = {
  CACC: 'CACC',
  CB: 'CB',
  CBACC: 'CBACC',
  CBF: 'CBF',
  CBM: 'CBM',
  CP: 'CP',
  CPD: 'CPD',
  CPE: 'CPE',
  CPL: 'CPL',
  CPM: 'CPM',
  CPQ: 'CPQ',
  CPT: 'CPT',
  CPW: 'CPW',
  CRENT: 'CRENT',
  CSC: 'CSC',
  IACC: 'IACC',
  IB: 'IB',
  IBACC: 'IBACC',
  IBL: 'IBL',
  IBNC: 'IBNC',
  IBNT: 'IBNT',
  IBR: 'IBR',
  IBS: 'IBS',
  IBUC: 'IBUC',
  IBUT: 'IBUT',
  ILDS: 'ILDS',
  INC: 'INC',
  INT: 'INT',
  IPDI: 'IPDI',
  IPV: 'IPV',
  IRENT: 'IRENT',
  IRM: 'IRM',
  ISB: 'ISB',
  ISV: 'ISV',
  IUC: 'IUC',
  IUT: 'IUT',
  W: 'W',
  WB: 'WB',
  WGMPP: 'WGMPP',
  WQL: 'WQL'
};
const paytypeCodeMappings = {
  C: 'C',
  E: 'E',
  F: 'F',
  I: 'I',
  M: 'M',
  W: 'W'
};
function extractValues(mappings) {
  return Object.keys(mappings);
}
const partGrp = extractValues(partGrpMappings);
const depart = extractValues(departmentMappings);
const paytyp = extractValues(paytypeMappings);
const paytypeC = extractValues(paytypeCodeMappings);
function lookupValue(mappings, key) {
  return mappings[key];
}
function lookupKey(mappings, name) {
  const keys = Object.keys(mappings);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    if (mappings[key] === name) {
      return key;
    }
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  back: {
    float: 'right',
    marginTop: '8px',
    marginRight: '17px'
  },
  reset: {
    width: '130px',
    margin: '8px !important'
  },
  gridDiv: {
    '@media (max-width: 1920px)': {
      width: 457
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 451
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 450
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  dataGrid: {
    '@media (max-width: 1920px)': {
      width: 451
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 451
      //marginLeft: '25%'
    },
    '@media (max-width: 2304px)': {
      width: 441
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  dataGridClient: {
    '@media (max-width: 1920px)': {
      width: 895
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 880
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 880
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  flexContainer: {
    alignItems: 'center'
  }
});
StoreAdvisors.propTypes = {
  keycloak: PropTypes.object,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setTab: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func,
  setDashboardReloadStatus: PropTypes.func
};
export default connect(mapStateToProps, mapDispatchToProps)(
  withKeycloak(withStyles(styles)(StoreAdvisors)),
  StoreAdvisors
);

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setDashboardReloadStatus: data =>
      dispatch({ type: SET_DASHBOARD_RELOAD_STATUS, payload: data }),
    setTab: data => dispatch({ type: SET_PAY_TYPE_TAB, payload: data }),
    setRefreshStatus: data =>
      dispatch({ type: SET_REFRESH_STATUS, payload: data }),
    setRefreshErrorStatus: data =>
      dispatch({ type: SET_REFRESH_ERROR_STATUS, payload: data }),
    setError: data => dispatch({ type: SET_PAY_TYPE_ERROR, payload: data }),
    setErrorCount: data =>
      dispatch({ type: SET_PAY_TYPE_ERROR_COUNT, payload: data })
  };
}

const SummaryTitle = ({ title, value }) => (
  <Grid item xs>
    <Typography variant="h5" color="primary">
      {title}:
    </Typography>
    <Typography variant="subtitle1"> {value}</Typography>
  </Grid>
);

SummaryTitle.propTypes = {
  title: PropTypes.string,
  value: PropTypes.string
};
