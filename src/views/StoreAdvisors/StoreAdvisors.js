/* eslint-disable no-shadow */
/* eslint-disable react/default-props-match-prop-types */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  Fade,
  Grid,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import CallMergeIcon from '@material-ui/icons/CallMerge';
import ErrorIcon from '@material-ui/icons/Error';
import ExportIcon from '@material-ui/icons/GetApp';
import InsertDriveFileIcon from '@material-ui/icons/InsertDriveFile';
import { withKeycloak } from '@react-keycloak/web';
import PropTypes from 'prop-types';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { traceSpan } from 'src/utils/OTTTracing';
import { AgGridReact } from '@ag-grid-community/react';
import RestoreIcon from '@material-ui/icons/Restore';
import 'src/grid.css';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { connect } from 'react-redux';
import {
  SET_PAY_TYPE_TAB,
  SET_REFRESH_STATUS,
  SET_REFRESH_ERROR_STATUS,
  SET_PAY_TYPE_ERROR,
  SET_PAY_TYPE_ERROR_COUNT,
  SET_DASHBOARD_RELOAD_STATUS
} from 'src/actions';
import {
  CLIENT_AUDITS,
  MODIFY_STORE_ADVISOR_DETAILS,
  MODIFY_STORE_PAYTYPE_DETAILS
} from 'src/graphql/queries';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import {
  getStoreAdvisorDetails,
  getStoreAdvisorDetailsCDK
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';

import { setAdvisorStatus } from 'src/actions';
// eslint-disable-next-line import/no-cycle
import { store } from '../../App';
// eslint-disable-next-line no-unused-vars

import { withStyles } from '@material-ui/styles';
import LoaderSkeleton from '../../components/LoaderSkeleton.js';
import { ReactSession } from 'react-client-session';
import GridCheckboxRenderer from './GridCheckboxRenderer';
import 'src/styles.css';
import PayType from './PayType';
var lodash = require('lodash');
class StoreAdvisors extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );

      if (checkSt == false) {
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ isLoading: true });

        this.getAgGridData();
        this.resetReportGrid();
      }
    }
  }

  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    const { keycloak } = this.props;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      setOpenAlertLog: false,
      showRefresh:
        this.props.session.dashboardReloadStatus == false ? 'none' : 'all',
      disableTable: 'all',
      fadein: true,
      rawGridApi: {},
      gridApi: {},
      oldRowArray: [],
      newRowArray: [],
      newPayTypeRowArray: [],
      prevPaytypeArray: [],
      oldCodeArray: [],
      prevCodeArray: [],
      newCodeArray: [],
      gridExcludedValue: '',
      gridExcludedId: [],
      categorizedValue: '',
      editedRowId: null,
      isCodeEdited: false,
      categorizedId: '',
      opcategoryArray: [],
      isCodeUpdated: false,
      isCodeRowUpdated: false,
      isPayTypeRowUpdated: false,
      openSaveDialog: false,
      isReloaded: false,
      laborPayTypeCount: '',
      PartsPayTypeCount: '',
      isLoading: true,
      resetState: false,
      isPaytypeLoading: false,
      isLoadingSave: false,
      laborFixedRateOld: '',
      laborFixedRateNew: null,
      laborFixedDateOld: '',
      laborFixedDateNew: null,
      partsFixedRateOld: '',
      partsFixedRateNew: null,
      partsFixedDateOld: '',
      partsFixedDateNew: null,
      tabSelection:
        this.props.history.location.state != null
          ? this.props.history.location.state.tabSelection
          : localStorage.getItem('dms') == 'dtk'
          ? 'two'
          : 'one',
      columnDefs: [
        {
          headerName: 'Advisor Id',
          width: 100,
          field: 'empId',

          hide: false,
          suppressMenu: true,
          unSortIcon: true
        },

        {
          headerName: 'Advisor Name',
          chartDataType: 'series',
          width: 120,
          field: 'empName',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          //flex: 1,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Assign To Store',
          chartDataType: 'series',
          width: 100,
          field: 'empRevertChange',

          cellRenderer: 'gridCheckboxRenderer',
          suppressMenu: true,
          unSortIcon: true,
          hide: false,
          editable: false,
          // editable: !(
          //   typeof keycloak.realmAccess.roles !== 'undefined' &&
          //   keycloak.realmAccess.roles.includes('client') === true
          // ),
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },

        {
          headerName: 'Action',
          width: 90,
          filter: false,
          sortable: false,
          editable: false,

          suppressMenu: true,
          cellStyle: params => {
            return startEdit.state.isReloaded
              ? {
                  'pointer-events': 'none',
                  opacity: '0.4',
                  textAlign: 'center'
                }
              : { textAlign: 'center' };
          },

          hide:
            typeof keycloak.realmAccess.roles !== 'undefined' &&
            keycloak.realmAccess.roles.includes('client') == true
              ? true
              : false,
          // hide: true,
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            let index = params.rowIndex;
            const eDiv = document.createElement('div');
            eDiv.innerHTML = `<button   title="Edit" id="btneditsadvisor${index}" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; cursor: pointer; border: 0; font-size: 12px; line-height: 13px;" class="edit-button-s-advisors"><i class="fas fa-pencil-alt"></i></button> <button  title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-s-advisors" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-s-advisors" ><i class="fas fa-save"></i></button>`;
            // if (
            //   props.keycloak &&
            //   props.keycloak.realmAccess.roles.includes('admin') == false &&
            //   props.keycloak.realmAccess.roles.includes('superadmin') ==
            //     false &&
            //   props.keycloak.realmAccess.roles.includes('user') == false
            // ) {
            //   $(document).ready(function() {
            //     $('.edit-button-s-advisors').attr('disabled', 'disabled');
            //     $('.edit-button-s-advisors').css('background', '#38416373');
            //   });
            // }

            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll(
                '.edit-button-s-advisors'
              )[0];
              const uButton = eDiv.querySelectorAll(
                '.update-button-s-advisors'
              )[0];
              const cButton = eDiv.querySelectorAll(
                '.cancel-button-s-advisors'
              )[0];
              eButton.addEventListener('click', () => {
                // params.columnApi.getDisplayedCenterColumns()[0].colId
                $(`.cancel-button-s-advisors`).hide();
                $(`.update-button-s-advisors`).hide();
                $(`.edit-button-s-advisors`).show();

                $('.empRevertChange').attr('disabled', 'disabled');
                localStorage.setItem('oldId', params.data.empId);
                var oldRow = [];
                oldRow.push(params.data.empId);
                oldRow.push(params.data.empName);
                oldRow.push(params.data.empRevertChange);

                JSON.stringify(oldRow);

                localStorage.setItem('oldRow', oldRow);
                startEdit.setState({
                  editedRowId: index
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowPrev = {
                  empId: params.data.empId,
                  empName: params.data.empName,
                  empRevertChange: params.data.empRevertChange
                };

                var rowPrevArray = startEdit.state.prevCodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ empId, empName }) =>
                    empId === rowPrev.empId && empName === rowPrev.empName
                );

                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }

                startEdit.setState({
                  prevCodeArray: rowPrevArray
                });

                var prevRow = [];
                prevRow.push({
                  empId: params.data.empId,
                  empName: params.data.empName,
                  empRevertChange: params.data.empRevertChange
                });

                startEdit.setState({
                  oldCodeArray: prevRow
                });

                //startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);
                $(`#btneditsadvisor${index}`).hide();
                $(`#btncancel${index}`).show();
                $(`#btnupdate${index}`).show();
              });
              uButton.addEventListener('click', () => {
                // index = params.rowIndex;

                startEdit.onBtStopEditing(index);
                localStorage.setItem('empId', params.data.empId);
                localStorage.setItem('empId', params.data.empName);
                localStorage.setItem(
                  'empRevertChange',
                  params.data.empRevertChange
                );

                const storeId = JSON.parse(
                  localStorage.getItem('selectedStoreId')
                )[0];
                startEdit.setState({
                  isCodeEdited: false
                });

                var rowNode = params.api.getDisplayedRowAtIndex(
                  params.rowIndex
                );

                startEdit.setState({
                  prevIndex: ''
                });

                startEdit.setState({
                  prevIndex: ''
                });
                var newRow = [];
                newRow.push(
                  params.data.empId,
                  params.data.empName,
                  params.data.empRevertChange
                );
                let oldDataArr = lodash.filter(
                  startEdit.state.prevCodeArray,
                  item => {
                    return (
                      item.empId == params.data.empId &&
                      item.empName == params.data.empName
                    );
                  }
                );

                var updatedRow = {
                  id: params.data.empId,
                  update_status: params.data.empRevertChange == 1 ? 0 : 1
                };
                var rowArray = startEdit.state.newCodeArray;
                let index = rowArray.findIndex(({ id }) => id == updatedRow.id);

                if (index === -1) {
                  if (
                    oldDataArr[0].empRevertChange != params.data.empRevertChange
                  ) {
                    rowArray.push(updatedRow);
                  }
                } else {
                  rowArray[index] = updatedRow;
                }
                startEdit.setState({
                  newCodeArray: rowArray
                });

                if (startEdit.state.newCodeArray.length > 0) {
                  startEdit.setState({
                    isCodeRowUpdated: true
                  });
                }

                localStorage.setItem('newRow', newRow);
                // startEdit.updatePayTypeMaster(
                //   selectedId,
                //   selectedPayType,
                //   selectedPayTypeCode,
                //   selectedPayTypeDept,
                //   oldDepartment,
                //   storeId
                // );
                startEdit.setState({
                  editedRowId: null
                });

                startEdit.gridApi.redrawRows();
                $(`#btneditsadvisor${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
              });
              cButton.addEventListener('click', () => {
                //startEdit.getAgGridData();
                startEdit.onBtStopEditing(index);
                startEdit.setState({
                  editedRowId: null
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return (
                    o.empId == params.data.empId &&
                    o.empName == params.data.empName
                  );
                });

                if (valArr.length > 0) {
                  rowNode.setDataValue(
                    'empRevertChange',
                    valArr[0].empRevertChange
                  );
                }

                let filteredArray = startEdit.state.newCodeArray.filter(
                  function(obj) {
                    return obj.id != params.data.empId;
                  }
                );
                startEdit.setState({
                  newCodeArray: filteredArray
                });
                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                }

                $(`#btneditsadvisor${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
        // editable: true
      },
      editType: 'fullRow',
      frameworkComponents: {
        gridCheckboxRenderer: GridCheckboxRenderer
      },
      context: { componentParent: this },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },

        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  componentDidMount() {
    const { setTab } = this.props;
    if (store.getState().session.menuSelected == 'Pay Types') {
      setTab('one');
    }
  }
  onFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      fixedRateOld: oldVal
    });

    this.setState({
      fixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  getAgGridData() {
    if (localStorage.getItem('dms') == 'dtk') {
      this.setState({ isLoading: true });
      getStoreAdvisorDetails(result => {
        if (
          result.data.statelessDtkSourceRawGetEmployeesWithoutStore
            .statelessDtkSourceRawEmployeesWithoutStores
        ) {
          this.getDistinctPaytypeCode(
            result.data.statelessDtkSourceRawGetEmployeesWithoutStore
              .statelessDtkSourceRawEmployeesWithoutStores
          );
          var roData = lodash.orderBy(
            result.data.statelessDtkSourceRawGetEmployeesWithoutStore
              .statelessDtkSourceRawEmployeesWithoutStores,
            'empName',
            'asc'
          );

          this.setState({
            rowData: roData
          });
          this.setState({ isLoading: false });
        }
      });
    } else if (localStorage.getItem('dms') == 'cdk') {
      getStoreAdvisorDetailsCDK(result => {
        if (
          result.data.statelessCdkSourceRawGetServiceAdvisorsWithoutStore
            .statelessCdkSourceRawServiceAdvisorsWithoutStores
        ) {
          this.getDistinctPaytypeCode(
            result.data.statelessCdkSourceRawGetServiceAdvisorsWithoutStore
              .statelessCdkSourceRawServiceAdvisorsWithoutStores
          );
          var roData = lodash.orderBy(
            result.data.statelessCdkSourceRawGetServiceAdvisorsWithoutStore
              .statelessCdkSourceRawServiceAdvisorsWithoutStores,
            'empName',
            'asc'
          );

          this.setState({
            rowData: roData
          });
          this.setState({ isLoading: false });
        }
      });
    }
  }

  getDistinctPaytypeCode(resultData) {
    const laborPaytype = resultData.filter(pay => pay.lbrOrPrts === 'L');
    const partsPaytype = resultData.filter(pay => pay.lbrOrPrts === 'P');
    const distinctLaborPaytype = Array.from(
      new Set(laborPaytype.map(item => item.payType))
    );
    const distinctPartsPaytype = Array.from(
      new Set(partsPaytype.map(item => item.payType))
    );
    const laborPaytypeCount = distinctLaborPaytype.length;
    const partsPaytypeCount = distinctPartsPaytype.length;
    this.setState({
      laborPayTypeCount: laborPaytypeCount,
      PartsPayTypeCount: partsPaytypeCount
    });
  }

  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;

    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  updateStoreAdvisors = () => {
    const client = makeApolloClientPostgres;

    this.setState({ showRefresh: 'none' });

    if (
      this.state.newCodeArray.length > 0 ||
      this.state.newPayTypeRowArray.length > 0
    ) {
      this.setState({ isLoadingSave: true });
      const coll = document.getElementsByClassName('edit-button-s-advisors');
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#ccc';
      }
      this.setState({
        isCodeRowUpdated: false
      });
      let oldPayArr = [];
      this.state.prevCodeArray.map((obj, i) => {
        oldPayArr.push(Object.values(obj));
      });
      let newPayArr = [];
      this.state.newCodeArray.map((obj, i) => {
        newPayArr.push(Object.values(obj));
      });

      const start = new Date();
      const userID = localStorage.getItem('userID');
      if (this.state.tabSelection == 'two') {
        const coll = document.getElementsByClassName('edit-button-s-advisors');
        for (let i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
        this.setState({
          isCodeRowUpdated: false
        });
        let oldPayArr = [];
        this.state.prevCodeArray.map((obj, i) => {
          oldPayArr.push(Object.values(obj));
        });
        let newPayArr = [];
        this.state.newCodeArray.map((obj, i) => {
          newPayArr.push(Object.values(obj));
        });

        client
          .mutate({
            mutation: MODIFY_STORE_ADVISOR_DETAILS,
            variables: {
              p_Advisors: JSON.stringify(this.state.newCodeArray),
              store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
              user_id: userID
            }
          })
          .then(result => {
            if (
              result.data.statelessCcPhysicalRwUpdateRoStoreWithAdvisor
                .updateRoStoreWithAdvisorStatuses[0].status == 1
            ) {
              this.setState({
                prevCodeArray: []
              });
              var activeAdvisors = this.props.session.activeAdvisors;
              // this.props.dispatch(setAdvisorStatus(statusval));

              this.state.newCodeArray.map((obj, i) => {
                if (obj.update_status == 1) {
                  activeAdvisors.push(obj.id);
                } else {
                  activeAdvisors.unshift(obj.id);
                }
              });

              this.props.dispatch(setAdvisorStatus(activeAdvisors));

              this.props.setAddAdvisorErrorCount(true);

              this.setState({ isLoadingSave: false });
              this.setState({ disableTable: 'all' });
              this.setState({ setMessage: `Advisor Details Updated.` });
              this.setState({ setOpenRow: true });
              for (let i = 0, len = coll.length; i < len; i++) {
                coll[i].style['background-color'] = '#384163';
              }
              const userIDs = localStorage.getItem('userID');
              const storeIds = JSON.parse(
                localStorage.getItem('selectedStoreId')
              )[0];
              const userRole = this.props.session.user.role;
              this.setState({ isPaytypeLoading: false });
              this.getAgGridData();
              client
                .mutate({
                  mutation: CLIENT_AUDITS,
                  variables: {
                    dmlaction: 'Update Store Advisor Master',
                    newdata: JSON.stringify(newPayArr),
                    olddata: JSON.stringify(oldPayArr),
                    schemaname: 'stateless_cc_physical_rw',
                    storeId: storeIds,
                    tablename: 'pay_type_master',
                    username: userIDs,
                    userrole: userRole
                  }
                })
                .then(result => {
                  // console.log("data result=",result);
                });
            } else {
              this.setState({ isLoadingSave: false });
              this.setState({ showRefresh: 'all' });
              this.setState({ disableTable: 'all' });
              this.setState({
                setMessage: `Advisor  Update Failed. Please Contact Support Team To
            Resolve.`
              });
              this.setState({ setOpenRow: true });
              for (let i = 0, len = coll.length; i < len; i++) {
                coll[i].style['background-color'] = '#384163';
              }
            }
          });
      } else {
        const coll = document.getElementsByClassName('edit-button-s-paytype');
        for (let i = 0, len = coll.length; i < len; i++) {
          coll[i].style['background-color'] = '#ccc';
        }
        this.setState({
          isPayTypeRowUpdated: false
        });
        let oldPayArr = [];
        this.state.prevPaytypeArray.map((obj, i) => {
          oldPayArr.push(Object.values(obj));
        });
        let newPayArr = [];
        this.state.newPayTypeRowArray.map((obj, i) => {
          newPayArr.push(Object.values(obj));
        });

        client
          .mutate({
            mutation: MODIFY_STORE_PAYTYPE_DETAILS,
            variables: {
              p_Paytypes: JSON.stringify(this.state.newPayTypeRowArray),
              store_id: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
              user_id: userID
            }
          })
          .then(result => {
            if (
              result.data.statelessCcPhysicalRwUpdateRoStoreWithPaytype
                .updateRoStoreWithPaytypeStatuses[0].status == 1
            ) {
              this.setState({
                prevCodeArray: []
              });
              // var activeAdvisors = this.props.session.activeAdvisors;
              // // this.props.dispatch(setAdvisorStatus(statusval));
              // let newPayArr = [];
              // this.state.newCodeArray.map((obj, i) => {
              //   if (obj.update_status == 1) {
              //     activeAdvisors.push(obj.id);
              //   } else {
              //     activeAdvisors.unshift(obj.id);
              //   }
              // });

              // this.props.dispatch(setAdvisorStatus(activeAdvisors));

              this.props.setAddPaytypeErrorCount(true);
              // getAllPayTypeErrors(callback => {
              //   if (callback.length > 0) {
              //     this.props.setError(true);
              //     this.props.setErrorCount(callback.length);
              //   } else {
              //     this.props.setError(false);
              //     this.props.setErrorCount(0);
              //   }
              // });

              this.setState({ isLoadingSave: false });
              this.setState({ disableTable: 'all' });
              this.setState({ setMessage: `Paytype Details Updated.` });
              this.setState({ setOpenRow: true });
              for (let i = 0, len = coll.length; i < len; i++) {
                coll[i].style['background-color'] = '#384163';
              }
              const userIDs = localStorage.getItem('userID');
              const storeIds = JSON.parse(
                localStorage.getItem('selectedStoreId')
              )[0];
              const userRole = this.props.session.user.role;
              this.setState({ isPaytypeLoading: false });
              this.getAgGridData();
              client
                .mutate({
                  mutation: CLIENT_AUDITS,
                  variables: {
                    dmlaction: 'Update paytype master',
                    newdata: JSON.stringify(newPayArr),
                    olddata: JSON.stringify(oldPayArr),
                    schemaname: 'stateless_cc_physical_rw',
                    storeId: storeIds,
                    tablename: 'pay_type_master',
                    username: userIDs,
                    userrole: userRole
                  }
                })
                .then(result => {
                  // console.log("data result=",result);
                });
            } else {
              this.setState({ isLoadingSave: false });
              this.setState({ showRefresh: 'all' });
              this.setState({ disableTable: 'all' });
              this.setState({
                setMessage: `Pay Type  Update Failed. Please Contact Support Team To
            Resolve.`
              });
              this.setState({ setOpenRow: true });
              for (let i = 0, len = coll.length; i < len; i++) {
                coll[i].style['background-color'] = '#384163';
              }
            }
          });
      }
    } else {
      this.setState({ isLoadingSave: false });
      this.setState({ disableTable: 'all' });
      const collAdv = document.getElementsByClassName('edit-button-s-advisors');
      for (let i = 0, len = coll.length; i < len; i++) {
        collAdv[i].style['background-color'] = '#384163';
      }
      const coll = document.getElementsByClassName('edit-button-s-paytype');
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    }
  };
  getRowStyle = params => {
    if (params.data.payTypeCode == null && params.data.department == null) {
      return { background: 'rgb(221, 234, 244)' };
    }
  };
  setRefreshButtonStatus = () => {
    this.setState({ isOpcodeUpdated: true });
    this.setState({ disableTable: 'all' });
    this.setState({ showRefresh: 'all' });
  };
  onLaborFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedRateOld: oldVal
    });

    this.setState({
      laborFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onLaborFixedDateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedDateOld: oldVal
    });

    this.setState({
      laborFixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedRateOld: oldVal
    });

    this.setState({
      partsFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onPartsFixedDateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedDateOld: oldVal
    });

    this.setState({
      partsFixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['empRevertChange'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['empRevertChange'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApi.redrawRows();
  };
  onSortChanged = e => {
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApi.redrawRows();
  };

  resetReportGrid = () => {
    if (this.state.tabSelection == 'two') {
      this.gridApi.setSortModel(null);
      this.gridApi.setFilterModel(null);
      this.setState({
        editedRowId: null
      });

      this.setState({
        isCodeEdited: false
      });
      this.gridApi.redrawRows();
    } else {
      this.setState({
        resetState: true
      });
    }
  };

  refreshDisable = () => {
    this.setState({ showRefresh: 'none' });
  };

  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = false;
      groupColumn[3]['editable'] = false;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    const { rawGridApi } = this.state;
    this.state.rawGridApi.setFocusedCell(
      index,
      'empRevertChange',

      pinned
    );
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'empRevertChange',

      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
    // rawGridApi.setFocusedCell(index, 'payTypeCode', pinned);
    // rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'payTypeCode',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
    // const { rawGridApi } = this.state;
    // rawGridApi.setFocusedCell(index, 'payTypeCode', pinned);
    // rawGridApi.startEditingCell({
    //   rowIndex: index,
    //   colKey: 'payTypeCode',
    //   rowPinned: pinned,
    //   keyPress: key,
    //   charPress: char
    // });
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  onBtExport = () => {
    const { rawGridApi } = this.state;
    const params = {
      sheetName: 'Report',
      fileName: this.state.tabSelection == 'two' ? 'StoreAdvisors' : 'PayTypes',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Store Advisors' },
            mergeAcross: 3
          }
        ]
      ]
    };
    rawGridApi.exportDataAsExcel(params);
  };

  handleTabChange = (event, newValue) => {
    const { setTab } = this.props;
    var reqSource = '';
    if (newValue == 'two') {
      reqSource = 'NewAdvisors';
    } else {
      reqSource = 'NewPaytypes';
    }

    const client = makeApolloClientPostgres;
    const store_id = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userID = localStorage.getItem('userID');

    // $('.edit-button-s-advisors').hide();
    this.setState({ tabSelection: newValue });
    this.setState({
      isPayTypeRowUpdated: false
    });
    this.setState({
      newPayTypeRowArray: []
    });
    this.setState({
      prevPaytypeArray: []
    });
    setTab(newValue);
  };
  handlePaytypeUpdates = (status, newCodeArray, oldRowArray) => {
    if (newCodeArray.length > 0) {
      this.setState({
        isPayTypeRowUpdated: status
      });
      this.setState({
        newPayTypeRowArray: newCodeArray
      });
      this.setState({
        prevPaytypeArray: oldRowArray
      });
    } else {
      this.setState({
        isPayTypeRowUpdated: status
      });
      this.setState({
        newPayTypeRowArray: []
      });
      this.setState({
        prevPaytypeArray: []
      });
    }
  };

  handleClose = () => {
    this.setState({ setOpen: false });
  };

  handleCloseRow = () => {
    this.setState({ setOpenRow: false });
  };

  onBtClose = () => {
    this.setState({ setOpenAlert: false });
  };

  onBtCloseError = () => {
    this.setState({ setOpenAlertError: false });
  };
  onBtCloseErrorLog = () => {
    this.setState({ setOpenAlertLog: false });
  };
  handleClickSaveOpcode = () => {
    this.setState({
      openSaveDialog: true
    });
  };
  handleCancel = () => {
    this.setState({
      openSaveDialog: false
    });
  };
  handleSave = () => {
    this.updateStoreAdvisors();
    this.setState({
      openSaveDialog: false
    });
  };
  handleSiteRefreshButton = val => {
    this.setState({
      showRefresh: val
    });
  };
  handleResetPaytype = val => {
    this.setState({
      resetState: false
    });
  };
  handleChange = status => {
    const { handleReload, handleRefresh } = this.props;
    const coll = document.getElementsByClassName('edit-button-s-advisors');
    if (status === 0) {
      this.setState({ showRefresh: 'none' });
      this.setState({ disableTable: 'all' });
      this.setState({ setOpenRow: false });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    } else if (status === 1) {
      this.setState({ showRefresh: 'all' });
      this.setState({ disableTable: 'all' });
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
    } else if (status === 2) {
      handleReload(false);
      handleRefresh(true);
    } else if (status === 3) {
      for (let i = 0, len = coll.length; i < len; i++) {
        coll[i].style['background-color'] = '#384163';
      }
      handleReload(true);
      handleRefresh(false);
    }
  };
  onCellClicked = params => {
    const id = localStorage.getItem('oldId');

    let rowId = this.state.editedRowId;
    // if (params.data.id != id) {
    //   $(`.edit-button-s-advisors`).show();
    //   $(`.update-button-s-advisors`).hide();
    //   $(`.cancel-button-s-advisors`).hide();
    //   $(`#btncancel` + rowId).click();
    //   $('.grid-excluded').attr('disabled', 'disabled');
    //   $('.laborFixedRate').attr('disabled', 'disabled');
    //   $('.laborFixedRateValue').hide();
    //   $('.laborFixedRateDate').hide();
    //   $('.partsFixedRate').attr('disabled', 'disabled');
    //   $('.partsFixedRateDate').hide();
    //   $('.partsFixedRateValue').hide();
    //   $('.partsFixedRateSelect').hide();
    // }
  };

  render() {
    const {
      showRefresh,
      disableTable,
      setOpen,
      setOpenRow,
      setOpenAlert,
      setMessage,
      setOpenAlertError,
      isLoading,
      fadein,
      autoGroupColumnDef,
      columnDefs,
      defaultColDef,
      rowData,
      excelStyles,
      laborPayTypeCount,
      PartsPayTypeCount,
      setOpenAlertLog
    } = this.state;
    // const coll = document.getElementsByClassName('edit-button-s-advisors');
    // if (disableTable === 'none') {
    //   for (let i = 0, len = coll.length; i < len; i++) {
    //     coll[i].style['background-color'] = '#ccc';
    //   }
    // }
    const { keycloak, classes } = this.props;
    let boxClass = ['ag-header-cell'];
    // if(this.state.addClass) {
    boxClass.push('add');
    // }
    {
      boxClass.join(' ');
    }
    let abc = localStorage.getItem('selectedStoreId');

    return (
      <div>
        <Dialog
          open={setOpenRow}
          onClose={this.handleCloseRow}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          // style={{ width: 350,height: 350 }}paddingLeft: '20px'
        >
          {/* <DialogTitle
            id="alert-dialog-title"
            style={{ paddingLeft: 108, paddingRight: 85 }}
          >
            Paytype Master
          </DialogTitle> */}
          <DialogContent>
            <DialogContentText
              id="alert-dialog-description"
              // style={{ paddingLeft: 85, paddingRight: 85 }}
            >
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                {setMessage}
              </Typography>
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCloseRow} color="primary" autoFocus>
              OK
            </Button>
          </DialogActions>
        </Dialog>

        <Paper square style={{ margin: 8 }}>
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            classes={{ flexContainer: classes.flexContainer }}
            showrefresh
            aria-label="icon label tabs example"
            TabIndicatorProps={{ style: { display: 'none' } }}
          >
            {localStorage.getItem('dms') == 'cdk' ? (
              <Tab
                className={
                  this.state.tabSelection == 'one'
                    ? classes.tabSelected
                    : classes.tabButtons
                }
                label={<div>Pay Types</div>}
                value="one"
              />
            ) : (
              ''
            )}
            <Tab
              className={
                this.state.tabSelection == 'two'
                  ? classes.tabSelected
                  : classes.tabButtons
              }
              label={<div>Store Advisors</div>}
              value="two"
            />

            {/* <Tab
              className={
                this.state.tabSelection == 'two'
                  ? classes.tabSelected
                  : classes.tabButtons
              }
              label={<div>Pay Type Fixed Rates</div>}
              value="two"
            /> */}

            <Button
              variant="outlined"
              style={
                this.state.isCodeRowUpdated || this.state.isPayTypeRowUpdated
                  ? {
                      margin: 8,
                      padding: '0 6px !important',
                      pointerEvents: 'all',
                      color: '#fff',
                      float: 'right',
                      background:
                        process.env.REACT_APP_DEALER == 'Armatus'
                          ? '#003d6b'
                          : '#C2185B',
                      width: '8%',
                      height: 24,
                      fontSize: '12px',
                      position: 'relative',
                      textTransform: 'none'
                    }
                  : {
                      margin: 8,
                      padding: '0 6px !important',
                      pointerEvents: 'none',
                      color: '#ccc',
                      float: 'right',
                      background: '#fff',
                      width: '8%',
                      position: 'relative',
                      textTransform: 'none',
                      fontSize: '12px',
                      height: 24,
                      display: !!(
                        typeof this.props.keycloak.realmAccess.roles !==
                          'undefined' &&
                        this.props.keycloak.realmAccess.roles.includes(
                          'client'
                        ) === true
                      )
                        ? 'none'
                        : 'flex'
                    }
              }
              // style={{
              //   margin: 8,
              //   padding: 10,
              //   pointerEvents: 'all',
              //   color: '#fff',
              //   float: 'right',
              //   background: '#003d6b',
              //   width: '10%'
              // }}
              //disabled={this.state.newOpcodeArray <= 0 ? 'false' : 'true'handleClickSaveOpcode}
              onClick={this.handleClickSaveOpcode}
            >
              Save Changes
            </Button>

            <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{ paddingRight: 19, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
            <Button
              variant="contained"
              id="reset-layout"
              style={{ margin: 8, marginTop: 11 }}
              className={clsx(
                classes.resetReportGridback,
                classes.reset,
                'reset-btn'
              )}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>

        {this.state.tabSelection === 'two' ? (
          <>
            {isLoading === true ? (
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            ) : null}
          </>
        ) : null}
        {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
        this.props.keycloak.realmAccess.roles.includes('client') != true &&
        this.state.tabSelection === 'two' ? (
          <>
            <Typography
              style={{
                paddingLeft: '10px',
                display: this.state.isLoading == true ? 'none' : 'block',
                fontWeight: 'bold',
                color: '#003d6b',
                fontSize: 13
              }}
            >
              1) Please assign your store(s) here. You may edit as many as you
              like simultaneously, and then please click “Save Changes”.
            </Typography>

            {/* <Typography
              style={{
                paddingLeft: '10px',
                display:
                  this.state.isLoading != true &&
                  showRefresh == 'all' &&
                  this.state.isOpcodeLoading === false
                    ? 'block'
                    : 'none',
                fontWeight: 'bold',
                color: 'red',
                fontSize: 13
              }}
            >
              3) There is a pending reload to the Dashboard. Please click on
              Reprocess Data History to refresh the data before proceeding to
              edit Opcodes.
            </Typography> */}
          </>
        ) : (
          ''
        )}
        {this.state.tabSelection === 'two' && (
          <div className={classes.gridDiv}>
            <Fade in={fadein} timeout={1200}>
              <div
                id="data-tab"
                className={clsx(
                  this.props.keycloak.realmAccess.roles.includes('client') ===
                    true
                    ? classes.dataGridClient
                    : classes.dataGrid,
                  'ag-theme-balham'
                )}
                style={{
                  // pointerEvents: disableTable,
                  height: window.innerHeight - 250 + 'px',
                  // height: '600px',
                  //width: '100%',

                  alignContent: 'center',
                  marginLeft: '8px',
                  paddingRight: '16px',
                  display: this.state.isLoading == true ? 'none' : 'block'
                }}
              >
                {this.state.isLoadingSave && (
                  <div
                    style={{
                      position: 'absolute',
                      left: '58%',
                      zIndex: 1,
                      top: '50%'
                    }}
                  >
                    <LoaderSkeleton />
                  </div>
                )}
                <AgGridReact
                  className="ag-theme-balham grid-cell-centered "
                  //className={boxClass.join(' ')}
                  style={{
                    height: '500px',
                    width: '100%'
                  }}
                  onRowEditingStarted={this.onRowEditingStarted}
                  onRowEditingStopped={this.onRowEditingStopped}
                  onFilterChanged={this.onFilterChanged}
                  onSortChanged={this.onSortChanged}
                  getRowStyle={this.getRowStyle}
                  autoGroupColumnDef={autoGroupColumnDef}
                  getChartToolbarItems={this.getChartToolbarItems}
                  modules={AllModules}
                  columnDefs={columnDefs}
                  defaultColDef={defaultColDef}
                  onGridReady={this.onGridReady}
                  suppressAggFuncInHeader
                  rowData={rowData}
                  excelStyles={excelStyles}
                  editType={this.state.editType}
                  suppressClickEdit={true}
                  onCellClicked={this.onCellClicked}
                  floatingFilter={true}
                  context={this.state.context}
                  frameworkComponents={this.state.frameworkComponents}
                  // enableRangeSelection={true}
                  // animateRows={true}
                  // enableCharts={true}
                  suppressRowClickSelection={true}
                  suppressHorizontalScroll={true}
                  suppressDragLeaveHidesColumns={true}
                  suppressContextMenu={true}
                />
              </div>
            </Fade>
          </div>
        )}

        {this.state.tabSelection === 'one' ? (
          <PayType
            setRefreshButtonStatus={this.setRefreshButtonStatus}
            handleResetPaytype={this.handleResetPaytype}
            resetState={this.state.resetState}
            isLoadingSave={this.state.isLoadingSave}
            handleSiteRefreshButton={this.handleSiteRefreshButton}
            handlePaytypeUpdates={this.handlePaytypeUpdates}
          />
        ) : (
          ''
        )}
        {/* {store.getState().session.payTypeInitialTab === 'one' ? (
          <Fade in={fadein} timeout={1200}>
            <Paper square style={{ width: '60%',alignContent: 'center',marginLeft: '20%' }}>
              <div>
                <Grid container spacing={3} style={{ margin: -8 }}>
                  <>
                    <SummaryTitle
                      title="Labor Paytype Count"
                      value={laborPayTypeCount}
                    />
                    <SummaryTitle
                      title="Parts Paytype Count"
                      value={PartsPayTypeCount}
                    />
                  </>
                </Grid>
              </div>
            </Paper>
          </Fade>
        ) : null} */}
        <Dialog
          //fullWidth
          //maxWidth="sm"
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openSaveDialog}
        >
          {/* <DialogTitle id="confirmation-dialog-title">
            <Typography
              variant="h5"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Save changes
            </Typography>
          </DialogTitle> */}
          <DialogContent>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Are You Sure You Want To Continue?
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button autoFocus onClick={this.handleCancel}>
              Cancel
            </Button>
            <Button onClick={this.handleSave} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}
const partGrpMappings = {
  'N/A': 'N/A',
  'Part List': 'Part List',
  'Part Cost': 'Part Cost'
};
const departmentMappings = {
  'Body Shop': 'Body Shop',
  Service: 'Service'
};
const paytypeMappings = {
  CACC: 'CACC',
  CB: 'CB',
  CBACC: 'CBACC',
  CBF: 'CBF',
  CBM: 'CBM',
  CP: 'CP',
  CPD: 'CPD',
  CPE: 'CPE',
  CPL: 'CPL',
  CPM: 'CPM',
  CPQ: 'CPQ',
  CPT: 'CPT',
  CPW: 'CPW',
  CRENT: 'CRENT',
  CSC: 'CSC',
  IACC: 'IACC',
  IB: 'IB',
  IBACC: 'IBACC',
  IBL: 'IBL',
  IBNC: 'IBNC',
  IBNT: 'IBNT',
  IBR: 'IBR',
  IBS: 'IBS',
  IBUC: 'IBUC',
  IBUT: 'IBUT',
  ILDS: 'ILDS',
  INC: 'INC',
  INT: 'INT',
  IPDI: 'IPDI',
  IPV: 'IPV',
  IRENT: 'IRENT',
  IRM: 'IRM',
  ISB: 'ISB',
  ISV: 'ISV',
  IUC: 'IUC',
  IUT: 'IUT',
  W: 'W',
  WB: 'WB',
  WGMPP: 'WGMPP',
  WQL: 'WQL'
};
const paytypeCodeMappings = {
  C: 'C',
  E: 'E',
  F: 'F',
  I: 'I',
  M: 'M',
  W: 'W'
};
function extractValues(mappings) {
  return Object.keys(mappings);
}
const partGrp = extractValues(partGrpMappings);
const depart = extractValues(departmentMappings);
const paytyp = extractValues(paytypeMappings);
const paytypeC = extractValues(paytypeCodeMappings);
function lookupValue(mappings, key) {
  return mappings[key];
}
function lookupKey(mappings, name) {
  const keys = Object.keys(mappings);
  for (let i = 0; i < keys.length; i++) {
    const key = keys[i];
    if (mappings[key] === name) {
      return key;
    }
  }
}
const styles = theme => ({
  tabButtons: {
    textTransform: 'none'
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  back: {
    float: 'right',
    marginTop: '8px',
    marginRight: '17px'
  },
  reset: {
    width: '130px',
    margin: '8px !important'
  },
  gridDiv: {
    '@media (max-width: 1920px)': {
      width: 457
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 451
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 450
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  dataGrid: {
    '@media (max-width: 1920px)': {
      width: 451
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 451
      //marginLeft: '25%'
    },
    '@media (max-width: 2304px)': {
      width: 441
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  dataGridClient: {
    '@media (max-width: 1920px)': {
      width: 895
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 880
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 880
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  flexContainer: {
    alignItems: 'center'
  }
});
StoreAdvisors.propTypes = {
  keycloak: PropTypes.object,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setTab: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func,
  setDashboardReloadStatus: PropTypes.func
};
export default connect(mapStateToProps, mapDispatchToProps)(
  withKeycloak(withStyles(styles)(StoreAdvisors)),
  StoreAdvisors
);

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setDashboardReloadStatus: data =>
      dispatch({ type: SET_DASHBOARD_RELOAD_STATUS, payload: data }),
    setTab: data => dispatch({ type: SET_PAY_TYPE_TAB, payload: data }),
    setRefreshStatus: data =>
      dispatch({ type: SET_REFRESH_STATUS, payload: data }),
    setRefreshErrorStatus: data =>
      dispatch({ type: SET_REFRESH_ERROR_STATUS, payload: data }),
    setError: data => dispatch({ type: SET_PAY_TYPE_ERROR, payload: data }),
    setErrorCount: data =>
      dispatch({ type: SET_PAY_TYPE_ERROR_COUNT, payload: data })
  };
}

const SummaryTitle = ({ title, value }) => (
  <Grid item xs>
    <Typography variant="h5" color="primary">
      {title}:
    </Typography>
    <Typography variant="subtitle1"> {value}</Typography>
  </Grid>
);

SummaryTitle.propTypes = {
  title: PropTypes.string,
  value: PropTypes.string
};
