import React, { useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import $ from 'jquery';
import StoreAdvisors from './StoreAdvisors';
import { useHistory } from 'react-router';
import { getAllOpcodeErrors } from 'src/utils/hasuraServices';
import {
  setRefreshStatus,
  setReloadStatus,
  setPayTypeTab,
  setAllErrorsCount,
  setNewAdvisors,
  setNewAdvisorsErrorCount,
  setNewPaytype,
  setNewPaytypeErrorCount
} from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function StoreAdvisorsEdit({ route }) {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useDispatch();

  const [notifications, setNotifications] = React.useState([]);
  useEffect(() => {
    if (history.location.state != null) {
      dispatch(setPayTypeTab(history.location.state.tabSelection));
    } else {
      dispatch(setPayTypeTab('one'));
    }
    // dispatch(setPayTypeTab(['Reference / Setups']));
  }, []);
  const session = useSelector(state => state.session);
  $(document).ready(function() {
    var element = document.getElementById('Store Assignments');
    // element.classList.add('active-menu');
  });

  const addAdvisorErrorCount = status => {
    let mounted = true;
    getAllOpcodeErrors(callback => {
      const paytypeErrorArr = callback.filter(function(el) {
        return el.cType === 'advisor_without_store';
      });
      var err = callback.reduce(
        (acc, o) => acc + parseInt(o.noncategorizedcount),
        0
      );
      var b = lodash.filter(callback, function(o) {
        if (o.noncategorizedcount > 0) return o;
      }).length;
      dispatch(setAllErrorsCount(b));
      //dispatch(setAllErrorsCount(err));
      if (paytypeErrorArr.length > 0) {
        if (mounted) {
          if (paytypeErrorArr[0].noncategorizedcount > 0) {
            dispatch(setNewAdvisors(true));
            dispatch(
              setNewAdvisorsErrorCount(paytypeErrorArr[0].noncategorizedcount)
            );
          } else {
            dispatch(setNewAdvisors(true));
            dispatch(setNewAdvisorsErrorCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setNewAdvisors(true));
        dispatch(setNewAdvisorsErrorCount(0));
      }
    });
  };
  const addPaytypeErrorCount = status => {
    let mounted = true;
    getAllOpcodeErrors(callback => {
      const paytypeErrorArr = callback.filter(function(el) {
        return el.cType === 'paytype_without_store';
      });
      var err = callback.reduce(
        (acc, o) => acc + parseInt(o.noncategorizedcount),
        0
      );
      var b = lodash.filter(callback, function(o) {
        if (o.noncategorizedcount > 0) return o;
      }).length;
      dispatch(setAllErrorsCount(b));
      //dispatch(setAllErrorsCount(err));
      if (paytypeErrorArr.length > 0) {
        if (mounted) {
          if (paytypeErrorArr[0].noncategorizedcount > 0) {
            dispatch(setNewPaytype(true));
            dispatch(
              setNewPaytypeErrorCount(paytypeErrorArr[0].noncategorizedcount)
            );
          } else {
            dispatch(setNewPaytype(true));
            dispatch(setNewPaytypeErrorCount(0));
          }
        }
      } else {
        setNotifications([]);

        dispatch(setNewPaytype(true));
        dispatch(setNewPaytypeErrorCount(0));
      }
    });
  };
  return (
    <Page className={classes.root} title="Store Assignments">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ||
      JSON.parse(localStorage.getItem('selectedStoreId'))[0] == '211726444' ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <StoreAdvisors
          route={route}
          dispatch={dispatch}
          history={history}
          // setAddAdvisorErrorCount={addAdvisorErrorCount}
          setAddPaytypeErrorCount={addPaytypeErrorCount}
          session={session}
        />
      )}
    </Page>
  );
}

export default StoreAdvisorsEdit;
