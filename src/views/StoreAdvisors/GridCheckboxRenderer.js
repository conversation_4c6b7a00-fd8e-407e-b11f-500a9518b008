import React, { Component } from 'react';

export default class extends Component {
  constructor(props) {
    super(props);
    this.checkedHandler = this.checkedHandler.bind(this);
    this.checkedHandlerPaytype = this.checkedHandlerPaytype.bind(this);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'empRevertChange') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);

      this.props.api.refreshCells({
        columns: ['empRevertChange'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  checkedHandlerPaytype(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'ptRevertChange') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);

      this.props.api.refreshCells({
        columns: ['ptRevertChange'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  render() {
    return this.props.colDef.field == 'empRevertChange' ? (
      <input
        type="checkbox"
        className="empRevertChange"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : false
        }
        onClick={this.checkedHandler}
        checked={this.props.value == 1 ? true : false}
      />
    ) : this.props.colDef.field == 'ptRevertChange' ? (
      <input
        type="checkbox"
        className="ptRevertChange"
        disabled={
          this.props.context.componentParent.state.editedRowId == null
            ? true
            : this.props.context.componentParent.state.editedRowId !=
              this.props.rowIndex
            ? true
            : false
        }
        onClick={this.checkedHandlerPaytype}
        checked={this.props.value == 1 ? true : false}
      />
    ) : (
      ''
    );
  }
}
