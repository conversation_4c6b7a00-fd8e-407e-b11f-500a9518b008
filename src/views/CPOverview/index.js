import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import React, { memo, useEffect, useState } from 'react';
import $ from 'jquery';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import { setNavItems, setMenuSelected } from 'src/actions';
import Charts from './Charts';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { Redirect } from 'react-router-dom';

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function CPOverview() {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const session = useSelector(state => state.session);
  let chartList = JSON.parse(global.localStorage.getItem('chart-master'));
  const [chartData, setChartData] = useState(false);

  useEffect(() => {
    dispatch(setMenuSelected('CP Summary Overview'));
    dispatch(setNavItems(['CP Summary Overview']));
  }, []);
  useEffect(() => {
    window.sortState = {};
    window.filterState = {};
    if (chartList) {
      setChartData(true);
    }
  }, [chartList]);
  return (
    <Page className={classes.root} title="CP Summary Overview">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : chartData ? (
        <Charts history={history} session={session} />
      ) : (
        <LoaderSkeleton />
      )}
    </Page>
  );
}

export default CPOverview;
