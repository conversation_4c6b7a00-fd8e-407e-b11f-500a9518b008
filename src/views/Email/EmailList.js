import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Typography,
  withStyles
} from '@material-ui/core';
import Button from '@material-ui/core/Button';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { Alert, AlertTitle } from '@material-ui/lab';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import clsx from 'clsx';
import RestoreIcon from '@material-ui/icons/Restore';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { getEmail } from 'src/utils/hasuraServices';
import { withKeycloak } from '@react-keycloak/web';
import { traceSpan } from 'src/utils/OTTTracing';
import EmailCellRenderer from './EmailRenderer';
import AddEmail from './AddEmail';
import DeleteDialog from './DeleteDialog';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import { ReactSession } from 'react-client-session';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
class Email extends React.Component {
  componentWillMount() {
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.gridApi.setSortModel(null);
        this.gridApi.setFilterModel(null);
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData('NULL', 'NULL', 'view');
        this.setState({
          editedRowId: null
        });
      }
    }
  }
  constructor(props) {
    super(props);
    let startEdit = this;
    this.state = {
      errorStatus: false,
      errorMsg: '',
      emailArray: [],
      goalFail: false,
      successMsg: '',
      deleteEmail: '',
      showAddEmail: false,
      cancel: false,
      openSnackbar: false,
      showCharts: false,
      selectValue: 'other',
      selectValueFor: this.props.category,
      selectValueType: this.props.reportType,
      ddLabel: '< 2',
      rawGridApi: {},
      gridApi: {},
      isLoading: false,
      oldDataArr: [],
      newDataArr: [],
      prevDataArr: [],
      editedRowId: null,
      success: false,
      isEdited: false,
      openDelete: false,
      columnDefs: [
        {
          headerName: ' Id',
          chartDataType: 'series',
          width: 150,
          field: 'status',

          hide: true
          // editable: false,
        },
        {
          headerName: 'User Email',
          chartDataType: 'series',
          width: 250,
          suppressMenu: true,
          sortable: true,
          // suppressClickEdit: true,
          editable: false,
          unSortIcon: true,
          field: 'value',
          //cellRenderer: 'EmailRenderer',
          tooltipField: 'value',
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          cellStyle: this.cellStyles,
          width: 125,
          filter: false,
          suppressMenu: true,
          sortable: false,
          editable: false,
          cellRenderer: function(params) {
            var index = params.rowIndex;
            var eDiv = document.createElement('div');
            const isDisabled =
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false;

            if (params.data.id === '' && params.data.value === '') {
              eDiv.innerHTML =
                '<button title="Delete" id="btndelete' +
                index +
                '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px; ' +
                (isDisabled
                  ? 'background: #d3d3d3; cursor: not-allowed;'
                  : '') +
                '" class="delete-button" ' +
                (isDisabled ? 'disabled' : '') +
                '><i class="fas fa-trash-alt"></i></button>';
            } else {
              eDiv.innerHTML =
                '<button title="Delete" id="btndelete' +
                index +
                '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px; ' +
                (isDisabled
                  ? 'background: #d3d3d3; cursor: not-allowed;'
                  : '') +
                '" class="delete-button" ' +
                (isDisabled ? 'disabled' : '') +
                '><i class="fas fa-trash-alt"></i></button>';
            }

            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.delete-button').attr('disabled', 'disabled');
                $('.delete-button').css('background', '#38416373');
                $('.delete-button').css('cursor', 'default');
              });
              $('.editNickName').attr('disabled', true);

              // $(document).ready(function() {
              //   $('.delete-button').attr('disabled', 'disabled');
              //   $('.delete-button').css('background', '#38416373');
              //   $('.delete-button').css('cursor', 'default');
              // });
            }
            if (index != undefined) {
              var eButton = eDiv.querySelectorAll('.edit-button-adv')[0];
              var uButton = eDiv.querySelectorAll('.update-button-adv')[0];
              var cButton = eDiv.querySelectorAll('.cancel-button-adv')[0];
              var dButton = eDiv.querySelectorAll('.delete-button')[0];
              // eButton.addEventListener('click', () => {
              //   let oldIndex = localStorage.getItem('oldIndex');
              //   if (oldIndex) {
              //     startEdit.onBtStopEditing(oldIndex);
              //     let rowData = startEdit.state.oldDataArr;
              //     if (rowData.length > 1 && rowData[0].value) {
              //       var rowNode = params.api.getDisplayedRowAtIndex(oldIndex);
              //       rowNode.setDataValue('value', rowData[0].value);
              //     }
              //   }
              //   startEdit.gridApi.redrawRows();
              //   $(`.cancel-button-adv`).hide();
              //   $(`.edit-button-adv`).show();
              //   $(`.update-button-adv`).hide();
              //   //startEdit.gridApi.redrawRows();
              //   let rowData = startEdit.state.oldDataArr;
              //   startEdit.setState({
              //     editedRowId: null
              //   });
              //   // startEdit.gridApi.refreshCells({force : true});
              //   localStorage.setItem('index', index);
              //   startEdit.setState({ cancel: false });
              //   startEdit.onBtStartEditing(index);
              //   localStorage.setItem('emailId', params.data.status);
              //   let editingCells = params.api.getEditingCells();
              //   startEdit.setState({
              //     editedRowId: index
              //   });
              //   let oldArr = {
              //     index: index,
              //     value: params.data.value
              //   };
              //   var rowPrevArray = [];
              //   let indexArr = rowPrevArray.findIndex(
              //     ({ value }) => value == params.data.value
              //   );
              //   if (indexArr === -1) {
              //     rowPrevArray.push(oldArr);
              //   }
              //   startEdit.setState({
              //     oldDataArr: rowPrevArray
              //   });
              //   $('#btneditadvisor' + index).hide();
              //   $('#btncanceladvisor' + index).show();
              //   $('#btnupdateadvisor' + index).show();
              //   $('#advisorStatus' + index).prop('disabled', '');
              //   $('#btndelete' + index).hide();
              //   localStorage.setItem('oldIndex', index);
              // });

              // uButton.addEventListener('click', () => {
              //   startEdit.onBtStopEditing(index);
              //   console.log('ccccc1====', params.data);
              //   var selectedId = params.data.status;
              //   console.log('selectedId=', selectedId);
              //   var value = params.data.value;
              //   value = value ? value.trim() : '';
              //   startEdit.setState({ cancel: false });
              //   startEdit.setState({
              //     editedRowId: null
              //   });

              //   let arr = startEdit.state.oldDataArr;
              //   console.log('data==', arr[0].value);
              //   if (arr[0].value != params.data.value) {
              //     let arrValue = params.data.value
              //       ? params.data.value.trim()
              //       : '';
              //     let arrValue1 = arr[0].value ? arr[0].value.trim() : '';
              //     console.log('nick==', arrValue, '===', arrValue1);
              //     console.log('enter===1234');
              //     if (arrValue != arrValue1) {
              //       var regExp = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
              //       console.log('enter===12=', value);
              //       if (value != '' && regExp.test(value) == true) {
              //         console.log('enter===1');
              //         startEdit.handleEmailData(
              //           value ? value.trim() : '',
              //           arr[0].value,
              //           'update'
              //         );
              //         startEdit.setState({
              //           oldDataArr: []
              //         });
              //       } else {
              //         console.log('enter===1777');
              //         let rowData = startEdit.state.oldDataArr;
              //         var rowNode = params.api.getDisplayedRowAtIndex(index);
              //         rowNode.setDataValue('value', rowData[0].value);
              //       }
              //     }
              //   }
              //   startEdit.gridApi.redrawRows();
              //   $('#btneditadvisor' + index).show();
              //   $('#btncanceladvisor' + index).hide();
              //   $('#btnupdateadvisor' + index).hide();
              //   $('#btndelete' + index).hide();
              //   $('#advisorStatus' + index).prop('disabled', 'disabled');
              // });
              // cButton.addEventListener('click', function() {
              //   startEdit.setState({ cancel: true });
              //   startEdit.onBtStopEditing(index);
              //   let status = $('#advisorStatus' + index).prop('checked');
              //   startEdit.setState({
              //     editedRowId: null
              //   });
              //   let data =
              //     $('#advisorStatus' + index).val() == 1 ? true : false;
              //   let advisorStatus = status == data ? status : !status;
              //   let rowData = startEdit.state.oldDataArr;
              //   var rowNode = params.api.getDisplayedRowAtIndex(index);
              //   rowNode.setDataValue('value', rowData[0].value);
              //   $('#btneditadvisor' + index).show();
              //   $('#btncanceladvisor' + index).hide();
              //   $('#btnupdateadvisor' + index).hide();
              //   $('#btndelete' + index).show();
              // });
              dButton.addEventListener('click', function() {
                var selectedId = params.data.status;
                var value = params.data.value;
                value = value ? value.trim() : '';
                console.log('value=', value, '==', selectedId);
                if (selectedId != '') {
                  // startEdit.handleEmailData(value, value, 'delete');
                  startEdit.handleDelete(value);
                  startEdit.setState({
                    deleteEmail: value
                  });
                }
                $('#btneditadvisor' + index).hide();
                $('#btncanceladvisor' + index).hide();
                $('#btnupdateadvisor' + index).hide();
                $('#btndelete' + index).show();
              });
            }
            return eDiv;
          }
        }
      ],
      rowData: [],
      editType: 'fullRow',
      chartName: null,
      context: { componentParent: this },
      frameworkComponents: {
        EmailRenderer: EmailCellRenderer
      },
      defaultColDef: {
        enableValue: true,
        suppressKeyboardEvent: params => params.event.keyCode === 13,
        // enableRowGroup: true,
        suppressMovable: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        }
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'center',
      border: ' 0px white',
      top: '-5%',
      position: 'inherit'
    };
  };
  getAdvForDrilldown = () => {
    let data = [1, 0];
    return data;
  };
  objCategoryMappings = [
    {
      1: 'Active',
      0: 'Inactive'
    }
  ];
  extractValues(mappings) {
    return Object.keys(mappings[0]);
  }
  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['nickname', 'active'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['nickname', 'active'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    //const filterValues = e.api.getFilterModel();
    this.gridApi.redrawRows();
  };
  onBtStartEditing = (index, key, char, pinned) => {
    // columnController
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = true;
    this.state.rawGridApi.setColumnDefs(groupColumn);
    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    this.state.rawGridApi.setFocusedCell(index, 'value', pinned);
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'value',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onGridReady = params => {
    localStorage.removeItem('oldIndex');
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridColumnApi: params.columnApi });
    this.gridApi.sizeColumnsToFit();
    if (
      this.props.keycloak &&
      this.props.keycloak.authenticated &&
      this.props.keycloak.token
    ) {
      this.getAgGridData('NULL', 'NULL', 'view');
    }
  };
  getRowStyle = params => {
    console.log('editedRowId==', !this.state.editedRowId);
    if (
      params.data.categorized == 0 ||
      (params.data.categorized == 1 && params.data.active == 0)
    ) {
      return { background: 'rgb(221, 234, 244)' };
    } else {
      return { background: 'rgb(255, 255, 255)' };
    }
  };
  getAgGridData(pNewMailId, pOldMailId, pProcess) {
    const filterModel = this.gridApi.getFilterModel(); // Store the filter state
    this.setState({
      rowData: []
    });
    this.setState({ isLoading: true });
    getEmail(pNewMailId, pOldMailId, pProcess, 'email_setup', result => {
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
          0 &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results[0]
          .status != 0
      ) {
        const results =
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results;
        const sortedData = results.sort((a, b) => {
          if (a.value < b.value) return -1;
          if (a.value > b.value) return 1;
          return 0;
        });
        this.setState({
          rowData: sortedData
        });
        this.setState({ errorStatus: false });
        console.log('results==', results);
        const emailArray = results.map(record => record.value);
        console.log('emailArray', emailArray);
        this.setState({ emailArray: emailArray });
        this.setState({ isLoading: false });
        this.gridApi.setFilterModel(filterModel);
        this.gridApi.onFilterChanged();
      } else {
        this.setState({ isLoading: false });

        if (
          result &&
          result.data &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
            0 &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results[0]
            .status == 0
        ) {
          this.setState({ errorStatus: true });
          this.setState({
            errorMsg:
              result.data.statelessCcPhysicalRwGetorsetMailIdMaster
                .results[0] &&
              result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results[0]
                .value
          });
        } else {
          this.setState({
            rowData: []
          });
          this.setState({ errorStatus: false });
          this.setState({ emailArray: [] });
          this.setState({ isLoading: false });
        }
      }
    });
  }
  handleOk = () => {
    this.setState({ success: false });
  };
  resetReportGrid = () => {
    this.setState({
      editedRowId: null
    });
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
  };
  onSortChanged = e => {
    this.gridApi.redrawRows();
  };
  onCellClicked = params => {
    let index = localStorage.getItem('index');
    const id = localStorage.getItem('serviceId');
    let rowId = this.state.editedRowId;
    console.log('idd=', id, rowId, params.data.id);
    if (params.data.id != id) {
      $(`.edit-button-adv`).show();
      $(`.update-button-adv`).hide();
      $(`.cancel-button-adv`).hide();
      $(`#btncanceladvisor` + rowId).click();
      this.setState({ cancel: true });
      this.onBtStopEditing(index);
      let status = $('#advisorStatus' + index).prop('checked');
      this.setState({ editedRowId: null });
      let data = $('#advisorStatus' + index).val() == 1 ? true : false;
      let advisorStatus = status == data ? status : !status;
    } else {
      $(`#btncanceladvisor${rowId}`).show();
      $(`#btnupdateadvisor${rowId}`).show();
      $(`#btneditadvisor${rowId}`).hide();
    }
  };
  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
  };
  addEmail = () => {
    this.setState({ showAddEmail: true });
  };
  closeEmail = () => {
    this.setState({ showAddEmail: false });
  };
  handleEmailData = (pNewMailId, pOldMailId, pProcess) => {
    this.setState({ isLoading: true });
    getEmail(pNewMailId, pOldMailId, pProcess, 'email_setup', result => {
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
          0 &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results[0]
          .status != 0
      ) {
        this.setState({ openSnackbar: true });
        this.setState({ goalFail: false });
        if (pProcess == 'insert') {
          this.setState({ successMsg: 'Email Successfully Added.' });
          this.getAgGridData('NULL', 'NULL', 'view');
        } else if (pProcess == 'delete') {
          this.setState({ openDelete: false });
          this.setState({ successMsg: 'Email Successfully Deleted.' });
          this.getAgGridData('NULL', 'NULL', 'view');
        } else if (pProcess == 'update') {
          this.setState({ successMsg: 'Email Successfully Updated.' });
          this.getAgGridData('NULL', 'NULL', 'view');
        }
      } else {
        this.setState({ openSnackbar: true });
        this.setState({ goalFail: true });
        this.setState({
          successMsg:
            result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
              .results[0].msg
        });
        this.setState({ isLoading: false });
      }
      this.setState({ showAddEmail: false });
    });
  };
  handleDelete = mailId => {
    this.setState({ openDelete: true });
  };
  handleCloseDelete = mailId => {
    this.setState({ openDelete: false });
  };
  getContextMenuItems = params => {
    return []; // Return an empty array to hide the context menu
  };
  onCellContextMenu = event => {
    event.preventDefault();
    console.log('Right-click disabled');
  };
  render() {
    const { classes } = this.props;
    console.log('showAddEmail==', this.state.rowData);
    return (
      <>
        <div>
          <Paper
            square
            style={{
              margin: 8,
              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
              cursor: 'default'
            }}
          >
            <Tabs
              value={this.state.tabSelection}
              onChange={this.handleTabChange}
              variant="fullWidth"
              indicatorColor="secondary"
              textColor="secondary"
              aria-label="icon label tabs example"
            >
              <Tab
                style={{
                  textTransform: 'none',
                  pointerEvents: 'none',
                  borderColor: '#e7eef3',
                  color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
                label={<div>Email Setups</div>}
                value="one"
              />
            </Tabs>
          </Paper>
          {/* <Paper> */}
          <Button
            variant="contained"
            id="reset-layout"
            className={clsx(classes.back, 'reset-btn')}
            onClick={this.resetReportGrid}
          >
            <RestoreIcon />
            <Typography variant="body1" align="left">
              Reset Layout
            </Typography>
          </Button>
          {/* </Paper> */}
          {/* {(this.props.keycloak.realmAccess.roles.includes('superadmin') ||
            this.props.keycloak.realmAccess.roles.includes('admin')) && ( */}
          <Paper
            square
            style={{
              marginLeft: 8,
              marginRight: 8,
              paddingTop: 9,
              display: 'flex',
              height: '54px',
              justifyContent: 'space-between'
            }}
          >
            <div className={classes.btnContainer}>
              <Button
                variant="contained"
                className={clsx('reset-btn', classes.btnUser)}
                onClick={this.addEmail}
                disabled={
                  this.props.keycloak &&
                  this.props.keycloak.realmAccess.roles.includes('admin') ==
                    false &&
                  this.props.keycloak.realmAccess.roles.includes(
                    'superadmin'
                  ) == false &&
                  this.props.keycloak.realmAccess.roles.includes('user') ==
                    false
                    ? true
                    : false
                }
              >
                Add Email
              </Button>
              {this.state.errorStatus == true && (
                <Alert severity="error" className={classes.errorMsg}>
                  {/* <AlertTitle>Error</AlertTitle> */}
                  <span>{this.state.errorMsg}</span>
                </Alert>
              )}
              {/* <label></label> */}
            </div>
          </Paper>
          {/* )} */}
          {this.state.isLoading == true ? (
            <div>
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            </div>
          ) : null}

          <div
            id="data-tab-advisor"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 170 + 'px',
              width: '375px',
              alignContent: 'center',
              marginLeft: '8px',
              marginTop: 15,
              // margin: 8,
              display: this.state.isLoading == true ? 'none' : 'block'
            }}
            onCellContextMenu={this.onCellContextMenu}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              frameworkComponents={this.state.frameworkComponents}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              // getChartToolbarItems={this.getChartToolbarItems}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              getRowStyle={this.getRowStyle}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader={true}
              onRowEditingStarted={this.onRowEditingStarted}
              onRowEditingStopped={this.onRowEditingStopped}
              // onFilterChanged={this.onFilterChanged}
              // onSortChanged={this.onSortChanged}
              // onCellValueChanged={this.onCellValueChanged.bind(this)}
              rowData={this.state.rowData}
              context={this.state.context}
              components={this.state.components}
              suppressClickEdit={true}
              onCellClicked={this.onCellClicked}
              floatingFilter={true}
              enableRangeSelection={true}
              animateRows={true}
              enableCharts={true}
              suppressRowClickSelection={true}
              suppressHorizontalScroll={true}
              editType={this.state.editType}
              tooltipShowDelay={0}
              container="body"
              suppressDragLeaveHidesColumns={true}
              getContextMenuItems={this.getContextMenuItems}
              suppressContextMenu={true}
            />
          </div>
        </div>
        {this.state.showAddEmail && (
          <AddEmail
            openDialog={this.state.showAddEmail}
            handleClose={this.closeEmail}
            addEmail={this.handleEmailData}
            emailArray={this.state.emailArray}
          />
        )}
        <DeleteDialog
          openDelete={this.state.openDelete}
          openPopup={this.state.openDelete}
          deleteEmail={this.state.deleteEmail}
          handlePopupClose={this.handleCloseDelete}
          handleDelete={this.handleEmailData}
          // confirmDeleteInt={confirmDeleteInt}
        ></DeleteDialog>
        {this.state.successMsg && (
          <SuccessSnackbar
            open={this.state.openSnackbar}
            onClose={this.hidesnackbar}
            msg={this.state.successMsg}
            goalFail={this.state.goalFail}
          />
        )}
      </>
    );
  }
}

const styles = theme => ({
  errorMsg: {
    width: '92%',
    // marginTop: '-9px',
    paddingTop: '1px',
    marginRight: '-15px',
    marginBottom: '10px'
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 18,
    float: 'right',
    marginTop: -47,
    width: 110,
    height: 27
  },
  btnContainer: {
    display: 'flex',
    width: '845px',
    justifyContent: 'space-between',
    width: '100%',
    marginRight: 25
  },
  btnUser: {
    height: '27px',
    fontSize: '12px !important',
    // border: '1px solid black !important',
    marginTop: '4px',
    width: 110,
    paddingRight: -29,
    paddingLeft: -18,
    left: 15
  }
});

export default withStyles(styles)(withKeycloak(Email));
