import React, { useState } from 'react';
import { FormControl } from '@material-ui/core';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import clsx from 'clsx';
import { withStyles } from '@material-ui/styles';

const EmailCellRenderer = props => {
  const { value, node, column, context, rowIndex, classes } = props;
  const [email, setEmail] = useState(value);
  const [errorMessage, setErrorMessage] = useState('');

  const handleChange = event => {
    const email = event.target.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    let errorMessage = '';
    if (!emailRegex.test(email)) {
      errorMessage = 'Invalid email address';
    }
    setEmail(email);
    setErrorMessage(errorMessage);
    if (errorMessage === '') {
      node.setDataValue(column.colId, email);
    }
  };

  const isEditable =
    context.componentParent.state.editedRowId !== null &&
    context.componentParent.state.editedRowId === rowIndex;

  return (
    <>
      {isEditable ? (
        <>
          <FormControl
            variant="outlined"
            disabled={!isEditable}
            style={{ width: '36%' }}
          >
            <OutlinedInput
              className={clsx(classes.formControl)}
              classes={{ input: classes.adorment }}
              value={email}
              onChange={handleChange}
              aria-describedby="outlined-weight-helper-text"
              labelWidth={0}
            />
          </FormControl>
          {errorMessage && (
            <span className="error-message" style={{ color: 'red' }}>
              {errorMessage}
            </span>
          )}
        </>
      ) : (
        value
      )}
    </>
  );
};

const styles = theme => ({
  formControl: {
    height: 24,
    backgroundColor: '#fff',
    marginTop: 2,
    paddingLeft: 8,
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  }
});

export default withStyles(styles)(EmailCellRenderer);
