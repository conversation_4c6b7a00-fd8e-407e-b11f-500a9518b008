import React, { useState, useEffect, useCallback } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
  Typography,
  Button,
  Tooltip,
  TextField,
  Paper,
  Card
} from '@material-ui/core';
import { Formik, Form, Field, ErrorMessage, useFormikContext } from 'formik';
import { setMenuSelected, setNavItems } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Page from 'src/components/Page';
import IconButton from '@material-ui/core/IconButton';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import * as Yup from 'yup';
import styled from 'styled-components';

const Dealer = process.env.REACT_APP_DEALER;
const useStyles = makeStyles(theme => ({
  btnLabel: {
    marginTop: '3px'
  },
  root: {
    width: '99%',
    marginLeft: 8,
    maxHeight: '100%'
  },
  text: {
    fontSize: '13px !important',
    color: '#212121 !important'
  },
  headValue: {
    fontSize: '13px !important',
    color: '#212121 !important'
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  matrixType: {
    marginTop: -42,
    marginLeft: 150
  },
  loaderGrid: {
    marginLeft: 450,
    marginTop: 120
  },
  header: {
    textTransform: 'none',
    fontSize: '13px !important',
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  headerDes: {
    textTransform: 'none',
    fontSize: '13px !important',
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b',
    marginTop: -25
  },
  checkBox: {
    cursor: 'pointer',
    height: 20,
    position: 'absolute',
    left: 2
    // right: 45,
    // top: 35
  },
  checkBoxError: {
    cursor: 'pointer',
    height: 20,
    position: 'absolute',
    left: 2,
    // right: 45,
    // top: 35,
    color: '#e53935'
  },
  btnDiv: {
    width: 60,
    height: 27,
    float: 'right'
    // marginRight: 17,
    // right: 14
  },
  cards: {
    marginRight: '20px',
    marginLeft: '20px',
    marginTop: '35px'
  }
}));

const FormErrorMsg = styled.div`
  color: red;
  font-size: 12px;
  text-overflow: ellipsis;
  max-width: 100%;
  padding-top: 10px;
`;
export default function AddEmail(props) {
  const {
    handleSubmitForm,
    action,
    setAddGrid,
    cancel,
    openDialog,
    handleClose,
    addEmail,
    emailArray
  } = props;
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const [addMatrix, setAddMatrix] = React.useState(openDialog);
  const [successMsg, setSuccessMsg] = useState('');
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [openDialogs, setOpenDialogs] = useState(false);
  const [goalFail, setGoalFail] = useState('');
  useEffect(() => {
    dispatch(setMenuSelected('Email Setups'));
    dispatch(setNavItems(['Reference / Setups']));
    setOpenDialogs(props.openDialog);
  }, [openDialog]);
  useEffect(() => {
    var gridRateValueStart = props.gridRateValueStart;
    console.log('gridRateValueStart=======', props.gridRateValueStart);
  }, [session.storeSelected, addMatrix]);
  const showMenuModal = data => {
    setOpenDialogs(true);
    // setSelectedMenu(data);
  };
  const handleCloseses = event => {
    event.preventDefault();
    setOpenDialogs(false);
    props.handleClose(true);
  };
  const showPartsMatrix = event => {
    if (event == 'add') {
      setAddMatrix(true);
    } else {
      setAddMatrix(false);
    }
  };
  const hidesnackbar = () => {
    setopenSnackbar(false);
  };
  var regExp = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
  const validationSchema = Yup.object({
    value: Yup.string()
      .trim()
      .email('Invalid email')
      .required('Required')
      .test('unique-email', 'Email already exists', value => {
        return !emailArray.includes(value);
      })
      .test('valid-email', 'Invalid email', value => {
        return regExp.test(value);
      })
      .test('valid-email', 'Invalid email', value => {
        return regExp.test(value);
      })
  });
  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    console.log('values==', values);
    setSubmitting(true);
    try {
      let value = values.value.toLowerCase();
      value = value ? value.trim() : '';
      await addEmail(value, value, 'insert');
    } catch (error) {
      console.error('Failed to submit form:', error);
    } finally {
      setSubmitting(false);
    }
  };
  console.log('matrixData==s=', props, openDialog, addMatrix);
  return (
    <Page title="Email">
      <div>
        <Dialog
          open={openDialog}
          onClose={handleCloseses}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            style: {
              width: '29%',
              marginLeft: '20px',
              marginTop: '1px',
              height: 210,
              maxWidth: '29%',
              backgroundColor: '#F4F6F8',
              bottom: 120
            }
          }}
        >
          <Paper style={{ height: '100%', padding: 20, boxShadow: 'none' }}>
            {/* <Card sx={{ margin: '20px 0 0 0' }} className={classes.cards}> */}
            <DialogTitle id="alert-dialog-title" style={{ marginLeft: 10 }}>
              <Typography
                variant="h4"
                color="primary"
                style={{
                  textTransform: 'none'
                }}
              >
                {'Add Email'}
              </Typography>
              <Tooltip title="Close">
                <IconButton
                  onClick={handleCloseses}
                  aria-label="close"
                  style={{ float: 'inline-end', bottom: 42, left: 20 }}
                >
                  <HighlightOffIcon style={{ fontSize: 36 }} />
                </IconButton>
              </Tooltip>
            </DialogTitle>
            {addMatrix == true && (
              <>
                <DialogContent
                  style={{
                    display: 'flex',
                    flexDirection: 'row'
                    // marginTop: -15
                  }}
                >
                  <div className={classes.root}>
                    <Formik
                      initialValues={{
                        value: ''
                      }}
                      validationSchema={validationSchema}
                      onSubmit={handleSubmit}
                    >
                      {({ isSubmitting, values, setFieldValue }) => (
                        <Form>
                          <Grid container spacing={1}>
                            <Grid item xs={12} sm={4}>
                              <Typography
                                variant="body1"
                                style={{
                                  fontSize: '13px',
                                  padding: '15px 0px 0px 10px',
                                  color: 'rgb(0, 61, 107)',
                                  marginTop: '-7px',
                                  marginLeft: '-8px'
                                }}
                              >
                                Email<span className="mandatorySign">*</span>
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={8}>
                              <Field
                                name="value"
                                as={TextField}
                                variant="outlined"
                                size="small"
                                fullWidth
                                inputProps={{
                                  maxLength: 50 // Set maximum length here
                                }}
                                style={{
                                  '& .css-1n4twyu-MuiInputBase-input-MuiOutlinedInput-input': {
                                    height: '1em'
                                  },
                                  right: 10
                                }}
                                onChange={e => {
                                  let value = e.target.value;
                                  // Replace multiple spaces with a single space
                                  value = value.replace(/\s{2,}/g, ' ');
                                  // Trim leading spaces
                                  if (value[0] === ' ') {
                                    value = value.substring(1);
                                  }
                                  setFieldValue('value', value);
                                }}
                              />
                              <ErrorMessage
                                name="value"
                                component={FormErrorMsg}
                              />
                            </Grid>
                          </Grid>
                          <Grid
                            container
                            spacing={1}
                            justifyContent="center"
                            style={{ padding: '10px' }}
                          >
                            <Grid item xs={12} sm={4}></Grid>
                            <Grid item xs={12} sm={8}>
                              <Button
                                variant="contained"
                                type="save"
                                color="primary"
                                className={clsx('reset-btn', classes.btnDiv)}
                                disabled={isSubmitting}
                                classes={{ label: classes.btnLabel }}
                              >
                                Save
                              </Button>
                            </Grid>
                            {/* <Grid item>
                            <Button
                              variant="outlined"
                              color="primary"
                              onClick={handleCloseses}
                              disabled={isSubmitting}
                            >
                              Cancel
                            </Button>
                          </Grid> */}
                          </Grid>
                        </Form>
                      )}
                    </Formik>
                    {/* </CardContent> */}
                  </div>
                </DialogContent>
              </>
            )}
            {/* </Card> */}
          </Paper>
        </Dialog>

        {successMsg && (
          <SuccessSnackbar
            open={openSnackbar}
            onClose={hidesnackbar}
            msg={successMsg}
            goalFail={goalFail}
          />
        )}
      </div>
    </Page>
  );
}
