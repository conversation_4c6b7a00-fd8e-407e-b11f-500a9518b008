import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import EmailList from './EmailList';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { useHistory } from 'react-router';
import { setMenuSelected, setNavItems } from 'src/actions';
import { withKeycloak } from '@react-keycloak/web';
var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));
function Email(props) {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const session = useSelector(state => state.session);
  useEffect(() => {
    dispatch(setMenuSelected('Email Setups'));
    dispatch(setNavItems(['Reference / Setups']));
  }, []);
  return (
    <Page className={classes.root} title="Email Setups">
      {/* {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : ( */}
      <EmailList dispatch={dispatch} session={session} history={history} />
      {/* )} */}
    </Page>
  );
}

export default withKeycloak(Email);
