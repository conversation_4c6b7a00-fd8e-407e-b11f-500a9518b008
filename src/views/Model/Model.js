import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
// import React, { useMemo, useState } from 'react';
import '@fortawesome/fontawesome-free/css/all.min.css';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import {
  Box,
  Divider,
  Dialog,
  Paper,
  Typography,
  Toolbar,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Radio,
  RadioGroup,
  Select,
  SelectChangeEvent
} from '@material-ui/core';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import { AgGridReact } from '@ag-grid-community/react';
import Alert from '@material-ui/lab/Alert';
import Page from 'src/components/Page';
import {
  getLaborMissesModels,
  insertMakeDetails,
  getLaborGridTypes,
  getAllOpcodeErrors,
  GetGridTypes
} from 'src/utils/hasuraServices';
import React, {
  useMemo,
  useState,
  useEffect,
  useCallback,
  useRef
} from 'react';
import { Tooltip, CircularProgress } from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/Delete';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
// import SuccessSnackbar from '../Model/SuccessSnackbar';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import RestoreIcon from '@material-ui/icons/Restore';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import Button from '@material-ui/core/Button';
import { makeStyles } from '@material-ui/core/styles';
import { useDispatch, useSelector } from 'react-redux';
import {
  setAllErrorsCount,
  setNewModelErrorCount,
  setNewModel
} from 'src/actions';
var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  back: {
    marginRight: 30,
    float: 'right',
    marginTop: -20,
    width: 105
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  dropdownMenu: {
    top: '280px !important' // Set the top property to 280px
  },
}));
const Modal = props => {
  const [leftApi, setLeftApi] = useState(null);
  const [rightApi, setRightApi] = useState(null);
  const [rawData, setRawData] = useState([]);
  const [leftRowData, setLeftRowData] = useState(null);
  const [rightRowData, setRightRowData] = useState([]);

  const [radioChecked, setRadioChecked] = useState(0);
  const [checkBoxSelected, setCheckBoxSelected] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [successMsg, setSuccessMsg] = useState('');
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [alertMsg, setAlertMsg] = useState('');
  const [alertType, setAlertType] = useState('');
  const [openAlert, setOpenAlert] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const gridRef = useRef();
  const classes = useStyles();
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const [expandNodes, setExpandNodes] = useState();
  const [operation, setOperation] = useState('');
  const [moveGridType, setMoveGridType] = useState('');
  const [rightDeselectFlag, setRightDeselectFlag] = useState(false);
  const [leftDeselectFlag, setLeftDeselectFlag] = useState(false);
  const [gridTypesDropdown, setGridTypesDropdown] = useState([]);
  const [gridTypesDefaultList, setGridTypesDefaultList] = useState([]);
  const [selectedGrid, setSelectedGrid] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);
  const [uniqueMakes, setUniqueMakes] = useState(new Set());
  let dragSuccess = false;
  let deleteFlag = false;
  useEffect(() => {
    // getAgGridData();
    getLaborGridTypess();
  }, [session.storeSelected]);
  const leftColumns = [
    {
      headerName: 'Make',
      field: 'make',
      rowGroup: true,
      hide: true,
      suppressMoveWithinParent: true
    },
    {
      headerName: 'Model',
      field: 'model',
      hide: true,
      suppressMoveWithinParent: true
    }
  ];

  const rightColumns = [
    {
      headerName: 'Grid Type',
      field: 'gridType',
      rowGroup: true,
      hide: true
    },
    {
      headerName: 'Make',
      field: 'make',
      rowGroup: true,
      hide: true
    },
    { headerName: 'Model', field: 'model', hide: true }
  ];

  const defaultColDef = useMemo(() => {
    return {
      flex: 1,
      minWidth: 100

      // checkboxSelection: params => {
      //   var displayedColumns = params.columnApi.getAllDisplayedColumns();
      //   var thisIsFirstColumn =
      //     displayedColumns[0].colId === params.column.colId;
      //   return thisIsFirstColumn;
      // }
    };
  }, []);

  const defaultColDefRight = useMemo(() => {
    return {
      flex: 1,
      minWidth: 100
    };
  }, []);

  const autoGroupColumnDef = useMemo(() => {
    return {
      headerName: 'Model',
      field: 'model',
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      rowDrag: params => (params.data ? true : false),
      rowDragText: (params, dragItemCount) => {
        let dragItemCounts =
          params.rowNode &&
          params.rowNode.selectionController &&
          params.rowNode.selectionController.selectedNodes;
        let dragItemArr = Object.values(dragItemCounts);
        const filteredArray = dragItemArr.filter(item => item !== undefined);
        const dragItemLen = filteredArray.length;
        if (dragItemLen > 1) {
          return dragItemLen + ' models';
        } else {
          return (
            params &&
            params.rowNode &&
            params.rowNode.data &&
            params.rowNode.data.model
          );
        }
      },

      cellRenderer: 'agGroupCellRenderer',
      cellRendererParams: {
        suppressCount: true,
        getRowNodeId: params =>
          params && params.data ? params.data.model : null,
        checkbox: true
      },
      headerCheckboxSelection: leftRowData && leftRowData.length > 0 // Enable checkboxes for selecting all rows in the group from the header only if there is data
    };
  }, [leftRowData]);

  const autoGroupColumnDefright = useMemo(() => {
    return {
      headerName: 'Grid Type',
      field: 'model',
      // rowDrag: true,
      cellRenderer: 'agGroupCellRenderer',
      suppressMenu: true,
      suppressColumnsToolPanel: true,
      sortable: false,
      filter: false,
      unSortIcon: false,
      cellRendererParams: {
        suppressCount: true,
        getRowNodeId: params => (params.data ? params.data.model : null),
        // checkbox: params => (params.node.field == 'gridType' ? false : true)
        checkbox: params => {
          return params.node.field == 'gridType' ||
            (params.node.field != 'gridType' &&
              params.value == 'No data available')
            ? false
            : true;
        }
      }
    };
  }, []);
  const getLaborGridTypesPromise = () =>
    new Promise((resolve, reject) => {
      getLaborGridTypes('get', null, null, result => {
        if (result) resolve(result);
        else reject(new Error('Failed to fetch Labor Grid Types'));
      });
    });

  const getLaborMissesModelsPromise = () =>
    new Promise((resolve, reject) => {
      getLaborMissesModels(result => {
        if (result) resolve(result);
        else reject(new Error('Failed to fetch Labor Misses Models'));
      });
    });

  const getGridTypeOptionsPromise = () =>
    new Promise((resolve, reject) => {
      GetGridTypes(result => {
        if (result) resolve(result);
        else reject(new Error('Failed to fetch Grid Type Options'));
      });
    });
  const getLaborGridTypess = async () => {
    try {
      const [
        LaborGridTypes,
        LaborMissesModels,
        GridTypeWithDefaultList
      ] = await Promise.all([
        getLaborGridTypesPromise(),
        getLaborMissesModelsPromise(),
        getGridTypeOptionsPromise()
      ]);

      const gridTypeChoices =
        LaborGridTypes.data
          .statelessDbdKpiScorecardGetorsetLaborMissesGridTypeMaster
          .laborMissesGridTypes;

      const makeModels =
        LaborMissesModels.data.statelessDbdKpiScorecardGetLaborMissesModels
          .laborMissesModels;

      let gridTypesList = GridTypeWithDefaultList;
      const gridTypesListSorted = gridTypesList.sort((a, b) => {
        if (a.value < b.value) return -1;
        if (a.value > b.value) return 1;
        return 0;
      });

      setGridTypesDefaultList(gridTypesListSorted);
      setGridTypesDropdown(gridTypesListSorted);
      const tempGrid = makeModels.filter(item => item.categorized == 1);
      const tempNullGrid = makeModels.filter(item => item.categorized != 1);
      // Assign null value for the leftRowData GridType
      tempNullGrid.forEach(item => {
        item.gridType = null;
      });

      // Check if any category is missing in the rightRowData
      const gridTypesInRightTable = tempGrid.map(item => item.gridType);
      const gridTypesFromAPI = gridTypeChoices.map(item => item.gridType);
      const missingGridTypes = [...gridTypesFromAPI].filter(
        category => !gridTypesInRightTable.includes(category)
      );

      const filteredData = makeModels.filter(item => item.categorized != 1);
      const filteredDataGrid = makeModels.filter(item => item.categorized == 1);
      if (filteredData.length == 0 && dragSuccess) {
        let mounted = true;
        getAllOpcodeErrors(callback => {
          const modelNewArr = callback.filter(function(el) {
            return el.cType === 'labor_grid_models';
          });
          var b = lodash.filter(callback, function(o) {
            if (o.noncategorizedcount > 0) return o;
          }).length;

          dispatch(setAllErrorsCount(b));
          setNotifications([]);

          dispatch(setNewModel(false));
          dispatch(setNewModelErrorCount(0));

          if (modelNewArr.length > 0) {
            if (mounted) {
              if (modelNewArr[0].noncategorizedcount > 0) {
                dispatch(setNewModel(true));
                dispatch(
                  setNewModelErrorCount(modelNewArr[0].noncategorizedcount)
                );
              } else {
                dispatch(setNewModel(false));
                dispatch(setNewModelErrorCount(0));
              }
            }
          } else {
            setNotifications([]);

            dispatch(setNewModel(false));
            dispatch(setNewModelErrorCount(0));
          }
        });
      }
      if (filteredData.length > 0 && deleteFlag) {
        let mounted = true;
        getAllOpcodeErrors(callback => {
          const modelNewArr = callback.filter(function(el) {
            return el.cType === 'labor_grid_models';
          });
          var b = lodash.filter(callback, function(o) {
            if (o.noncategorizedcount > 0) return o;
          }).length;
          dispatch(setAllErrorsCount(b));
          if (modelNewArr.length > 0) {
            // setAllErrors(
            //   allErrors =>
            //     Number(allErrors) + Number(modelNewArr[0].noncategorizedcount)
            // );
            if (mounted) {
              if (modelNewArr[0].noncategorizedcount > 0) {
                dispatch(setNewModel(true));
                dispatch(
                  setNewModelErrorCount(modelNewArr[0].noncategorizedcount)
                );
              } else {
                dispatch(setNewModel(false));

                dispatch(setNewModelErrorCount(0));
              }
            }
          } else {
            setNotifications([]);
            dispatch(setNewModel(false));
            dispatch(setNewModelErrorCount(0));
          }
        })};


      // Create dummy rows for the missing gridTypes and add them to rightRowData
      const dummyRows = missingGridTypes.map(gridType => ({
        gridType: gridType,
        make: null,
        model: 'No data available',
        __typename: 'LaborMissesGridType'
      }));
      const updatedRightRowData = [...tempGrid, ...dummyRows].sort((a, b) => {
        if (a.gridType < b.gridType) return -1;
        if (a.gridType > b.gridType) return 1;
        return 0;
      });
      setLeftRowData(tempNullGrid);
      const modifiedRightRowData = replaceNullDataWithMessage(
        updatedRightRowData
      );
      setRightRowData(updatedRightRowData);
    } catch (error) {
      console.error('Error in fetchGridModels:', error);
    } finally {
      setIsLoading(false);
    }
  };
  const handleRadioChange = event => {
    event.target.value === 'delete' && setMoveGridType('');
    setOperation(event.target.value);
  };

  const handleChange = event => {
    setMoveGridType(event.target.value);
  };

  const replaceNullDataWithMessage = rowData => {
    return rowData.map(item => ({
      ...item,
      model:
        item.model !== null && item.model !== undefined && item.model !== ''
          ? item.model
          : 'No data available'
    }));
  };

  const getRowId = params => {
    return params && params.data && params.data.id ? params.data.id : null;
  };
  const onDragStop = useCallback(
    params => {
      const { nodes, overNode } = params;
      const { node } = params;
      const rowData = node.data;
      const leftApi = gridRef.current.api;
      if (selectedItems.length === 0) {
        leftApi.applyTransaction({ remove: [rowData] });
        setOpenAlert(true);
        setAlertMsg('Please select any item to move');
        setAlertType('warning');
        return;
      }
      // Ensure overNode is valid and has a field of 'gridType'
      if (
        overNode &&
        overNode.field === 'gridType' &&
        rightRowData &&
        rightRowData.length > 0
      ) {
        const category =
          overNode.key || (overNode.parent && overNode.parent.key);

        if (category) {
          const pValData = selectedItems.map(item =>
            generatePValData(item, category, 'map')
          );
          const pValue = JSON.stringify(pValData);
          params.api.showLoadingOverlay();
          updateModelMapping(pValue, false, params);
        }
      } else {
        const { node } = params;
        const rowData = node.data;
        const leftApi = gridRef.current.api;

        leftApi.applyTransaction({ remove: [rowData] });
        setOpenAlert(true);
        const statusMessage =
          overNode == null
            ? 'Grid Type is not Available'
            : 'Please Assign to Proper Grid Type';
        setAlertMsg('Please Assign to Proper Grid Type');
        setAlertType('warning');
      }
    },
    [selectedItems]
  );

  const hidesnackbar = () => {
    setopenSnackbar(false);
  };

  const generatePValData = (item, gridType, map) => ({
    p_process: map,
    gridtype: map === 'unmap' ? item.gridType : gridType,
    model: item.model,
    make: item.make
  });
  const generatePValDatanodes = (node, gridType, map) => [
    {
      p_process: map,
      gridtype: gridType,
      model: node && node.data && node.data.model,
      make: node && node.data && node.data.make
    }
  ];
  useEffect(() => {
    if (!leftApi || !rightApi) {
      return;
    }
    const dropZoneParams = rightApi.getRowDropZoneParams({ onDragStop });

    leftApi.removeRowDropZone(dropZoneParams);
    leftApi.addRowDropZone(dropZoneParams);
  }, [leftApi, rightApi, onDragStop]);

  const onGridReady = useCallback((params, side) => {
    if (side === 0) {
      setLeftApi(params.api);
    }
    if (side === 1) {
      setRightApi(params.api);
    }
  }, []);
  const handleSelectionChanged = (params, gridId) => {
    if (
      (gridId === 0 && leftDeselectFlag) ||
      (gridId === 1 && rightDeselectFlag)
    ) {
      setRightDeselectFlag(false);
      setLeftDeselectFlag(false);
      return;
    }
    gridId === 0 ? setSelectedGrid('left') : setSelectedGrid('right');
    // Determine which grid API to use
    const gridApi = gridId === 0 ? leftApi : rightApi;

    const selectedNodes =
      gridId === 0 ? leftApi.getSelectedNodes() : rightApi.getSelectedNodes();
    const selectedData = selectedNodes.map(node => ({
      make: node.data?.make,
      model: node.data?.model,
      gridType: node.data?.gridType
    }));

    // Filter out gridTypes present in nodes
    const selectedGridTypeNodes = selectedData.map(item => item.gridType);
    const filteredGridTypes = gridTypesDefaultList.filter(
      item => !selectedGridTypeNodes.includes(item.value)
    );
    // Filter out grid types with a gridCount of 0
    const validGridTypes = filteredGridTypes.filter(
      item => item.gridCount != 0
    );

    setGridTypesDropdown(validGridTypes);

    setSelectedItems(selectedData);
    if (selectedData.length === 0) {
      setOperation('');
      setMoveGridType('');
    }

    // Expand row groups for each unique 'make' value when selected
    const tempUniqueMakes = new Set(
      selectedNodes.map(node => node.data?.make).filter(Boolean)
    ); // A temporary set to show all selected makes
    const difference =
      uniqueMakes &&
      new Set(
        Array.from(tempUniqueMakes).filter(item => !uniqueMakes.has(item))
      ); // To get the last selected make
    tempUniqueMakes.forEach(make => {
      // For each selected makes
      gridApi.forEachNode(node => {
        // Get node for each make
        if (
          node.field === 'make' &&
          node.key === make &&
          difference?.has(make)
        ) {
          gridApi.setRowNodeExpanded(node, true); // Expand the now selected make
        }
      });
    });
    setUniqueMakes(tempUniqueMakes);
    if (gridId === 0) {
      setRightDeselectFlag(true);
      setLeftDeselectFlag(false);
      rightApi.deselectAll(true);
    } else {
      setRightDeselectFlag(false);
      setLeftDeselectFlag(true);
      leftApi.deselectAll(true);
    }
  };

  const MoveGridType = () => {
    if (operation === 'move') {
      const pValData = selectedItems.map(item =>
        generatePValData(item, moveGridType, 'update')
      );
      const pValue = JSON.stringify(pValData);
      rightApi && rightApi.showLoadingOverlay();
      updateModelMapping(pValue, false, '', 'update');
    } else if (operation === 'delete') {
      const pValData = selectedItems.map(item =>
        generatePValData(item, moveGridType, 'unmap')
      );
      const pValue = JSON.stringify(pValData);
      rightApi && rightApi.showLoadingOverlay();
      leftApi && leftApi.showLoadingOverlay();
      updateModelMapping(pValue, true);
    }
  };

  const updateModelMapping = async (pValData, resetFlag, props, action) => {
    try {
      rightApi && rightApi.showLoadingOverlay();
      action !== 'update' && leftApi && leftApi.showLoadingOverlay();
      insertMakeDetails(pValData, result => {
        if (
          result.data.statelessDbdKpiScorecardLaborMissesModelMapping.string ===
          'Success'
        ) {
          getLaborGridTypess();
          const statusMessage = resetFlag
            ? 'Unmapping successful'
            : pValData.length > 1
            ? 'Mappping successful'
            : 'mapped 1 make';
          setSuccessMsg(statusMessage);
          setopenSnackbar(true);
        } else {
          setSuccessMsg(statusMessage);
          setopenSnackbar(true);
          const statusMessage = result.string;
          rightApi && rightApi.hideOverlay();
          leftApi && leftApi.hideOverlay();
          props && props.api && props.api.hideOverlay();
        }
      });

      setSelectedItems([]);
      setOperation('');
      setMoveGridType('');
    } catch (error) {
      rightApi && rightApi.hideOverlay();
      leftApi && leftApi.hideOverlay();
      props && props.api && props.api.hideOverlay();
      const statusMessage = 'Something went wrong';
      setSuccessMsg(statusMessage);
      setopenSnackbar(true);
      console.log(error);
      setOperation('');
      setMoveGridType('');
    }
  };

  const hasSelectedChildren = (parentNode, selectedNodes) => {
    let hasSelectedChild = false;
    leftApi.forEachNode(childNode => {
      if (
        childNode.parent === parentNode &&
        selectedNodes.includes(childNode)
      ) {
        hasSelectedChild = true;
      }
    });
    return hasSelectedChild;
  };

  const getGridWrapper = id => (
    <div className="ag-theme-alpine grid-wrapper" style={{ height: '78vh' }}>
      <AgGridReact
        style={{ height: '100%' }}
        defaultColDef={id === 0 ? defaultColDef : defaultColDefRight}
        getRowId={getRowId}
        rowDragManaged={true}
        // rowSelection={id === 0 ? 'multiple' : undefined}
        rowDragMultiRow={true}
        // suppressRowClickSelection={id === 0}
        suppressMoveWhenRowDragging={id === 0}
        rowData={id === 0 ? leftRowData : rightRowData}
        columnDefs={id === 0 ? leftColumns : rightColumns}
        onGridReady={params => onGridReady(params, id)}
        rowDragEntireRow={true}
        animateRows={true}
        ref={gridRef}
        autoGroupColumnDef={
          id == 0 ? autoGroupColumnDef : autoGroupColumnDefright
        }
        groupSelectsChildren={true}
        onSelectionChanged={params => handleSelectionChanged(params, id)}
        // onCellClicked={onCellClicked}
        // onRowDragEnd={onRowDragEnd}
        suppressContextMenu={true}
        groupAllowUnbalanced
        rowSelection={'multiple'}
        suppressRowClickSelection={true}
        onRowGroupOpened={params => {
          const api = params.api;
          const groupNode = params.node;
          if (groupNode.expanded) {
            const rowIndex = groupNode.rowIndex; 
            api.ensureIndexVisible(rowIndex, 'top');
          }
        }}
      />
    </div>
  );

  return (
    <>
      {props.keycloak.realmAccess.roles.includes('client') ||
      props.keycloak.realmAccess.roles.includes('user') ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <>
          {isLoading ? (
            <Grid
              container
              justify="center"
              alignItems="center"
              style={{ height: '70vh' }}
            >
              <CircularProgress size={60} />
            </Grid>
          ) : (
            <>
              <Page className={classes.root} title="Model Mapping - Grid">
                <Paper square style={{ margin: 8 }}>
                  <Tabs
                    variant="fullWidth"
                    indicatorColor="secondary"
                    textColor="secondary"
                    showrefresh
                    aria-label="icon label tabs example"
                    TabIndicatorProps={{ style: { display: 'none' } }}
                  >
                    <Tab
                      style={{ textTransform: 'none' }}
                      label={<div>Model Mapping - Grid</div>}
                      value="one"
                      className={classes.tabSelected}
                    />
                  </Tabs>
                </Paper>

                <Grid container spacing={2} style={{ padding: 8 }}>
                  <Grid item>{getGridWrapper(0)}</Grid>

                  <Grid item>{getGridWrapper(1)}</Grid>

                  {selectedGrid === 'right' && selectedItems.length !== 0 && (
                    <Box
                      style={{
                        display: 'flex',
                        backgroundColor: '#fff',
                        border: '1px solid rgb(200, 200, 200)',
                        padding: '16px 24px',
                        height: 'fit-content',
                        flexDirection: 'column',
                        margin: '10px 20px',
                        width: '20vw'
                      }}
                    >
                      <Typography
                        id="modal-modal-title"
                        variant="h6"
                        style={{
                          fontSize: '12px',
                          fontWeight: 'bold',
                          marginBottom: '10px',
                          color: '#003d6b'
                        }}
                      >
                        Move / Delete
                      </Typography>
                      <RadioGroup
                        style={{
                          fontWeight: 'bold',
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'center'
                        }}
                        row
                        aria-labelledby="demo-row-radio-buttons-group-label"
                        name="row-radio-buttons-group"
                        value={operation}
                        onChange={handleRadioChange}
                      >
                        <Typography
                          variant="body2"
                          style={{
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: 'rgb(90, 90, 90)',
                            marginRight: '16px', // Increased spacing
                            opacity: gridTypesDropdown.length == 0 ? 0.5 : 1,
                            cursor:
                              gridTypesDropdown.length == 0
                                ? 'not-allowed'
                                : 'default',
                            pointerEvents:
                              gridTypesDropdown.length == 0 ? 'none' : 'auto'
                          }}
                        >
                          Action:
                        </Typography>
                        <FormControlLabel
                          control={
                            <Radio
                              style={{
                                padding: '2px',
                                transform: 'scale(0.8)' // Reduced size of radio button
                              }}
                            />
                          }
                          label={
                            <Typography
                              variant="body2"
                              style={{ fontSize: '12px' }}
                            >
                              Move
                            </Typography>
                          }
                          value="move"
                          disabled={gridTypesDropdown.length == 0}
                        />
                        <FormControlLabel
                          control={
                            <Radio
                              style={{
                                padding: '2px',
                                transform: 'scale(0.8)' // Reduced size of radio button
                              }}
                            />
                          }
                          label={
                            <Typography
                              variant="body2"
                              style={{ fontSize: '12px' }}
                            >
                              Delete
                            </Typography>
                          }
                          value="delete"
                        />
                      </RadioGroup>
                      <Box
                        style={{
                          fontWeight: 'bold',
                          display: 'flex',
                          flexDirection: 'row',
                          gap: 1,
                          alignItems: 'baseline',
                          justifyContent: 'space-between',
                          marginTop: '8px',
                          opacity: operation !== 'move' || gridTypesDropdown.length == 0 ? 0.5 : 1,
                          cursor:
                            operation !== 'move' || gridTypesDropdown.length == 0 ? 'not-allowed' : 'default',
                          pointerEvents: operation !== 'move' || gridTypesDropdown.length == 0 ? 'none' : 'auto'
                        }}
                      >
                        <Typography
                          variant="body2"
                          style={{
                            fontSize: '12px',
                            fontWeight: 'bold',
                            color: 'rgb(90, 90, 90)',
                            marginTop: 1
                          }}
                        >
                          Select Grid Type:{' '}
                        </Typography>

                        <FormControl
                          style={{ width: '60%', margin: 0 }}
                          size="small"
                          variant="outlined"
                        >
                          <InputLabel
                            id="demo-simple-select-small-outlined-label"
                            style={{
                              marginTop: '6px',
                              fontSize: '14px',
                              width: 'fit-content'
                            }}
                          >
                            Grid Type
                          </InputLabel>
                          <Select
                            autoWidth
                            variant="outlined"
                            labelId="demo-simple-select-small-outlined-label"
                            id="demo-simple-select-small-outlined"
                            value={moveGridType}
                            label="Grid Type"
                            name="grid-type"
                            onChange={handleChange}
                            style={{
                              height: '30px',
                              marginTop: '9px',
                              fontSize: '12px',
                              backgroundColor:
                                operation !== 'move' || gridTypesDropdown.length == 0 ? 'rgb(234, 234, 234)' : ''
                            }}
                            disabled={operation !== 'move' || gridTypesDropdown.length == 0}
                            MenuProps={{
                              PaperProps: {
                                className: classes.dropdownMenu
                              }
                            }}
                          >
                            {gridTypesDropdown.map(item => {
                              return (
                                item.gridCount != 0 && (
                                  <MenuItem
                                    style={{ fontSize: '12px' }}
                                    value={item.value}
                                  >
                                    {item.value}{' '}
                                    {item.isDefaultGridType && (
                                      <span
                                        style={{
                                          fontStyle: 'italic',
                                          opacity: 0.7,
                                          marginLeft: 4
                                        }}
                                      >
                                        {` (Default)`}
                                      </span>
                                    )}
                                  </MenuItem>
                                )
                              );
                            })}
                          </Select>
                        </FormControl>
                      </Box>
                      <Box
                        sx={{
                          display: 'flex',
                          justifyContent: 'flex-end',
                          mt: 1
                        }}
                      >
                        <Button
                          color="primary"
                          variant="contained"
                          disabled={
                            !operation ||
                            (operation === 'move' && !moveGridType)
                          }
                          style={{
                            width: 'fit-content',
                            padding: '4px 12px',
                            minWidth: 0,
                            fontSize: '12px',
                            height: '26px' // Set a fixed height for the button
                          }}
                          onClick={MoveGridType}
                          className={clsx('reset-btn', classes.btnDiv)}
                        >
                          Save
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Grid>
              </Page>
            </>
          )}
          {successMsg && (
            <SuccessSnackbar
              onClose={hidesnackbar}
              open={openSnackbar}
              msg={successMsg}
            />
          )}
          <Dialog
            open={openAlert}
            // classes={{
            //   paper: classes.dialog
            // }}
          >
            <Alert
              severity={alertType == 'warning' ? 'warning' : 'success'}
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => {
                    setOpenAlert(false);
                  }}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
              style={{ margin: '10px 20px' }}
            >
              {alertMsg}
            </Alert>
          </Dialog>
        </>
      )}
    </>
  );
};
export default withKeycloak(Modal);
