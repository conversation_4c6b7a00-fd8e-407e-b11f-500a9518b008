import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import DashboardLineRenderer from 'src/components/charts/DashboardLineRenderer';
import DashboardActions from 'src/components/DashboardActions';
import {
  getDataGridConfiguration,
  getLayoutConfiguration,
  saveLayoutConfiguration,
  isBarChart,
  isWormixChart,
  isTech<PERSON>hart,
  isAdvisorChart,
  isTechComparisonChart,
  isAdvisorComparison<PERSON>hart,
  isAdvisorOpcategoryChart,
  isMonthComparison<PERSON>hart,
  isDiscountComparisonApexChart,
  isPartsComparison<PERSON>hart,
  isAddonsComparison<PERSON>hart,
  isOpportunityChart
} from '../../utils/Utils';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import { Typography, Paper, IconButton } from '@material-ui/core';
import StarIcon from '@material-ui/icons/Star';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import WorkMixRenderer from 'src/views/AnalyzeData/WorkMixCharts/BarChartRenderer';
import TechnicianRender from 'src/views/AnalyzeData/TechEfficiency/BarChartRenderer';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
import AdvisorPerformanceRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/BarChartRenderer';
import TechnicianMonthComparison from 'src/views/AnalyzeData/TechEfficiency/ColumnRenderer';
import AdvisorComparisonCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/ColumnRenderer';
import AdvisorOpcategoryCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/OpCategoryChartRenderer';
import HighCharts from 'src/views/AnalyzeData/ComparisonCharts/ColumnRenderer';
import ApexChartsDiscount from 'src/views/Discounts/DiscountColumnRenderer';
import ApexCharts from 'src/views/AddOns/ServiceAdvisor/ColumnRenderer';
import OpportunityCharts from 'src/components/charts/OpportunityCharts';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import Header from './Header';
import { Grid } from '@material-ui/core';
const ReactGridLayout = WidthProvider(RGL);

export default class Charts extends React.PureComponent {
  
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 48,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      timePassed: false,
      favouriteData: [],
      filters: 2,
      isLoading: false,
      checkEmpty: false,
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-fav') || {}
        )
      )
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }

  setFilterCharts = value => {
    if (value) {
      this.setState({
        filters: value
      });
    }
    return this.state.filters;
  };
  removeFav = value => {
    console.log('remoValue', value);
    if (value == 2) {
      this.setState({
        isLoading: true
      });
    } else if (value == 3) {
      this.setState({
        isLoading: false
      });
    }
    // return this.state.layout;
  };
  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }
    return this.state.layout;
  };
  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-fav');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
    // window.location.reload();
  }
  handleChange = val => {
    this.setState({ checkEmpty: val });
  };
  handleChartPopup = value => {
    this.setState({
      popupChartId: value,
      open: true
    });
  };
  handleClose = val => {
    this.setState({ open: false });
  };
  render() {console.log("favouriteData=",this.props.favouriteData);
    setTimeout(() => {this.setState({timePassed: true})}, 1000);
    const favourites = this.props.favourites.filter(Number);
    const favouritesArr = favourites.filter(n => n);
    let realm = localStorage.getItem('realm');
    return (
      <div>
        {realm == 'demo_store' ? <Header /> : null}
        <DashboardActions
          resetDashboard={this.setResetDashboard}
          filterCharts={this.setFilterCharts}
          noFilters={true}
          setTitle="My Favorites"
        ></DashboardActions>
        {(this.props.favouriteData && this.props.favouriteData.length == 0) ||
          (this.props.favourites && this.props.favourites == 0)
        ? (
          <div>
            {this.state.timePassed == true ? (
            <Paper
              square
              style={
                localStorage.getItem('realm') == 'demo_store'
                  ? { margin: 8, marginTop: '10px' }
                  : { margin: 8 }
              }
            >
              <Tabs
                // value={this.state.tabSelection}
                // onChange={this.handleTabChange}
                variant="fullWidth"
                indicatorColor="secondary"
                textColor="secondary"
                aria-label="icon label tabs example"
              >
                <Tab
                  style={{
                    textTransform: 'none',
                    paddingRight: 18,
                    color:
                      process.env.REACT_APP_DEALER == 'Armatus'
                        ? '#003d6b'
                        : '#C2185B'
                  }}
                  label={
                    <div>
                      {'There are no favorites added. Please click on'}{' '}
                      <IconButton size="medium">
                        <StarBorderIcon />
                      </IconButton>{' '}
                      {'to add charts to favorites'}
                    </div>
                  }
                  value="one"
                />
              </Tabs>
            </Paper>
            ):(null)}
          </div>
        ) : (
          // {/* <Paper style={{ padding: 16 }}>
          //   <Typography>
          //     You do not have any charts in favorites. Add to favorites by clicking on the star icon appearing on the
          //     supported charts
          //   </Typography>
          // </Paper> */}
          <>
          <Grid container style={{ padding: '5px' }} spacing={12}>
          {favouritesArr.map((value, index) => {
            if (isBarChart(value) == false &&
            isWormixChart(value) == false &&
            isTechChart(value) == false &&
            isAdvisorChart(value) == false &&
            isTechComparisonChart(value) == false &&
            isAdvisorComparisonChart(value) == false &&
            isAdvisorOpcategoryChart(value) == false &&
            isMonthComparisonChart(value) == false &&
            isDiscountComparisonApexChart(value) == false &&
            isPartsComparisonChart(value) == false &&
            isAddonsComparisonChart(value) == false &&
            isOpportunityChart(value) == false ) {
              return (
                <Grid item xs={3} style={{ padding: '5px' }}>
                  <DashboardLineRenderer
                      removeFav={this.removeFav}
                      filterCharts={this.state.filters}
                      handleChartPopup={this.handleChartPopup}
                      handleClosePopup={this.handleClose}
                      handleClose={this.handleClose}
                      chartId={Number(value)}
                    />
                </Grid>
                ) 
              }
            })}
            {favouritesArr.map((value, index) => {  
              if( isWormixChart(value)) {
                return (
                  <Grid item xs={6} style={{ padding: '5px' }}>
                    <WorkMixRenderer
                      removeFav={this.removeFav}
                      chartId={Number(value)}
                      handleChartPopup={this.handleChartPopup}
                      handleClosePopup={this.handleClose}
                      handleClose={this.handleClose}
                      isFrom={'source_page'}
                    />

                    </Grid>
                  )
                }
              })} 
              {favouritesArr.map((value, _index) => { 
                if( isTechChart(value)) { 
                  return (
                    <Grid item xs={6} style={window.location.pathname == '/MyFavorites' ? { padding: '0px' } : { padding: '5px' }}>
                    <TechnicianRender
                      removeFav={this.removeFav}
                      chartId={Number(value)}
                      title={value}
                      handleClose={this.handleClose}
                      handleClosePopup={this.handleClose}
                      tech={localStorage.getItem('selectedTech')}
                      techName={localStorage.getItem('selectedTechName')}
                      checkEmpty={this.handleChange}
                      callFrom={0}
                      handleChartPopup={this.handleChartPopup}
                      chartTitle={getChartName(value)}
                      isFrom={'source_page'}
                    />
                    </Grid>
                  )
                  }
                })}
                {favouritesArr.map((value, index) => { 
                  if( isAdvisorChart(value)) {
                    return(
                      <Grid item xs={6} style={{ padding: '5px' }}>
                        <AdvisorPerformanceRenderer
                          removeFav={this.removeFav}
                          chartId={Number(value)}
                          title={1}
                          handleClosePopup={this.handleClose}
                          handleClose={this.handleClose}
                          handleChartPopup={this.handleChartPopup}
                          advisor={localStorage.getItem('selectedAdvisor')}
                          advisorName={localStorage.getItem('selectedAdvisorName')}
                          checkEmpty={this.handleChange}
                          isFrom={'source_page'}
                        />
                        </Grid>
                    )
                  }
                })}
                {favouritesArr.map((value, index) => { 
                  if( isAdvisorComparisonChart(value) ) {
                    return (
                      <Grid item xs={6} style={{ padding: '5px' }}>
                    <AdvisorComparisonCharts
                      removeFav={this.removeFav}
                      datatype={''}
                      handleChartPopup={this.handleChartPopup}
                      handleClosePopup={this.handleClose}
                      handleClose={this.handleClose}
                      month1={this.props.comparisonMonths[0]}
                      month2={this.props.comparisonMonths[1]}
                      chartId={Number(value)}
                      isFrom={'source_page'}
                    />
                    </Grid>
                    )
                    }
                  })}
                  {favouritesArr.map((value, index) => {   
                  if( isAdvisorOpcategoryChart(value) ){
                    return (
                      <Grid item xs={6} style={{ padding: '5px' }}>
                    <AdvisorOpcategoryCharts
                      removeFav={this.removeFav}
                      datatype={''}
                      handleChartPopup={this.handleChartPopup}
                      handleClosePopup={this.handleClose}
                      handleClose={this.handleClose}
                      month1={this.props.comparisonMonths[0]}
                      chartId={Number(value)}
                      isFrom={'source_page'}
                    />
                    </Grid>
                    )
                  }
                })}
                  {favouritesArr.map((value, index) => { 
                    if( isDiscountComparisonApexChart(value)) {
                    return(
                      <Grid item xs={6} style={{ padding: '5px' }}>
                    <ApexChartsDiscount
                      removeFav={this.removeFav}
                      datatype={''}
                      chartId={Number(value)}
                      handleClosePopup={this.handleClose}
                      handleChartPopup={this.handleChartPopup}
                      handleClose={this.handleClose}
                      month1={this.props.comparisonMonths[0]}
                      month2={this.props.comparisonMonths[1]}
                      isFrom={'source_page'}
                    />
                    </Grid>
                    )
                    }
                  })}
                  {favouritesArr.map((value, index) => { 
                  if( isAddonsComparisonChart(value) ) {
                    return(
                      <Grid item xs={6} style={{ padding: '5px' }}>
                    <ApexCharts
                      removeFav={this.removeFav}
                      datatype={''}
                      handleChartPopup={this.handleChartPopup}
                      handleClosePopup={this.handleClose}
                      handleClose={this.handleClose}
                      chartId={Number(value)}
                      month1={this.props.comparisonMonths[0]}
                      month2={this.props.comparisonMonths[1]}
                      isFrom={'source_page'}
                    />
                    </Grid>
                    )
                  }
                })}
                {favouritesArr.map((value, index) => { 
                  if( isOpportunityChart(value) ){
                    return (
                      <Grid item xs={6} style={{ padding: '5px' }}>
                    <OpportunityCharts
                      removeFav={this.removeFav}
                      handleChartPopup={this.handleChartPopup}
                      handleClosePopup={this.handleClose}
                      handleClose={this.handleClose}
                      chartId={Number(value)}
                    />
                    </Grid>
                    )
                     } else if(
                      (isBarChart(value) == false &&
                      isWormixChart(value) == false &&
                      isTechChart(value) == false &&
                      isAdvisorChart(value) == false &&
                      isTechComparisonChart(value) == false &&
                      isAdvisorComparisonChart(value) == false &&
                      isAdvisorOpcategoryChart(value) == false &&
                      isMonthComparisonChart(value) == false &&
                      isDiscountComparisonApexChart(value) == false &&
                      isPartsComparisonChart(value) == false &&
                      isAddonsComparisonChart(value) == false &&
                      isOpportunityChart(value) == false ) ||
                      (isWormixChart(value) ||
                      isTechChart(value) ||
                      isAdvisorChart(value) ||
                      isTechComparisonChart(value) ||
                      isAdvisorComparisonChart(value) ||
                      isAdvisorOpcategoryChart(value) ||
                      isMonthComparisonChart(value) ||
                      isPartsComparisonChart(value) ||
                      isDiscountComparisonApexChart(value) ||
                      isAddonsComparisonChart(value) ||
                      isOpportunityChart(value))

                     ) {
                     }else{
                    return (
                      <Grid item xs={6} style={{ padding: '5px' }}>
                    <DashboardBarRenderer
                      removeFav={this.removeFav}
                      handleChartPopup={this.handleChartPopup}
                      handleClosePopup={this.handleClose}
                      handleClose={this.handleClose}
                      chartId={Number(value)}
                    />
                    </Grid>
                       )
                     }
          })}
        </Grid>
        <Grid container style={{ padding: '5px' }} spacing={12}>
                {favouritesArr.map((value, index) => { 
                  if( isMonthComparisonChart(value) ||
                    isPartsComparisonChart(value) ){
                      return (
                        <Grid item xs={12} style={{ padding: '5px' }}>
                    <HighCharts
                      removeFav={this.removeFav}
                      isPartsCharts={
                        isPartsComparisonChart(value) ? true : false
                      }
                      datatype={''}
                      handleChartPopup={this.handleChartPopup}
                      handleClose={this.handleClose}
                      handleClosePopup={this.handleClose}
                      chartId={Number(value)}
                      month1={this.props.comparisonMonths[0]}
                      month2={this.props.comparisonMonths[1]}
                      isFrom={'source_page'}
                    />
                    </Grid>
                      )
                    }
                  })}
                  </Grid>
                  <Grid container style={{ padding: '5px' }} spacing={12}>
                {favouritesArr.map((value, index) => { 
                  if( isTechComparisonChart(value) ) {
                    return(
                      <Grid item xs={12}  style={{ padding: '5px' }}  >
                    <TechnicianMonthComparison
                      removeFav={this.removeFav}
                      datatype={''}
                      handleClosePopup={this.handleClose}
                      callFrom={0}
                      handleChartPopup={this.handleChartPopup}
                      month1={this.props.comparisonMonths[0]}
                      handleClose={this.handleClose}
                      month2={this.props.comparisonMonths[1]}
                      chartId={Number(value)}
                      source={'source_page'}
                    />
                    </Grid>
                    )
                  }
                })}
                </Grid>
        </>
          // <ReactGridLayout
          //   {...this.props}
          //   layout={this.state.layout}
          //   onLayoutChange={this.onLayoutChange}
          //   isResizable={false}
          // >
          //   {favouritesArr.map((value, index) => {
          //     //alert(isTechChart(value))
          //     return (
          //       <div
          //         style={
          //           localStorage.getItem('realm') == 'demo_store'
          //             ? { backgroundColor: '#FFF' }
          //             : { backgroundColor: '#FFF' }
          //         }
          //         key={index}
          //         data-grid={getDataGridConfiguration(index)}
          //       >
          //         {isBarChart(value) == false &&
          //         isWormixChart(value) == false &&
          //         isTechChart(value) == false &&
          //         isAdvisorChart(value) == false &&
          //         isTechComparisonChart(value) == false &&
          //         isAdvisorComparisonChart(value) == false &&
          //         isAdvisorOpcategoryChart(value) == false &&
          //         isMonthComparisonChart(value) == false &&
          //         isDiscountComparisonApexChart(value) == false &&
          //         isPartsComparisonChart(value) == false &&
          //         isAddonsComparisonChart(value) == false &&
          //         isOpportunityChart(value) == false ? (

          //           <DashboardLineRenderer
          //             removeFav={this.removeFav}
          //             filterCharts={this.state.filters}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             chartId={Number(value)}
          //           />
          //         ) : isWormixChart(value) ? (
          //           <WorkMixRenderer
          //             removeFav={this.removeFav}
          //             chartId={Number(value)}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             isFrom={'source_page'}
          //           />
          //         ) : isTechChart(value) ? (
          //           <TechnicianRender
          //             removeFav={this.removeFav}
          //             chartId={Number(value)}
          //             title={value}
          //             handleClose={this.handleClose}
          //             handleClosePopup={this.handleClose}
          //             tech={localStorage.getItem('selectedTech')}
          //             techName={localStorage.getItem('selectedTechName')}
          //             checkEmpty={this.handleChange}
          //             callFrom={0}
          //             handleChartPopup={this.handleChartPopup}
          //             chartTitle={getChartName(value)}
          //             isFrom={'source_page'}
          //           />
          //         ) : isAdvisorChart(value) ? (
          //           <AdvisorPerformanceRenderer
          //             removeFav={this.removeFav}
          //             chartId={Number(value)}
          //             title={1}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             handleChartPopup={this.handleChartPopup}
          //             advisor={localStorage.getItem('selectedAdvisor')}
          //             advisorName={localStorage.getItem('selectedAdvisorName')}
          //             checkEmpty={this.handleChange}
          //             isFrom={'source_page'}
          //           />
          //         ) : isTechComparisonChart(value) ? (
          //           <TechnicianMonthComparison
          //             removeFav={this.removeFav}
          //             datatype={''}
          //             handleClosePopup={this.handleClose}
          //             callFrom={0}
          //             handleChartPopup={this.handleChartPopup}
          //             month1={this.props.comparisonMonths[0]}
          //             handleClose={this.handleClose}
          //             month2={this.props.comparisonMonths[1]}
          //             chartId={Number(value)}
          //             source={'source_page'}
          //           />
          //         ) : isAdvisorComparisonChart(value) ? (
          //           <AdvisorComparisonCharts
          //             removeFav={this.removeFav}
          //             datatype={''}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             month1={this.props.comparisonMonths[0]}
          //             month2={this.props.comparisonMonths[1]}
          //             chartId={Number(value)}
          //             isFrom={'source_page'}
          //           />
          //         ) : isAdvisorOpcategoryChart(value) ? (
          //           <AdvisorOpcategoryCharts
          //             removeFav={this.removeFav}
          //             datatype={''}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             month1={this.props.comparisonMonths[0]}
          //             chartId={Number(value)}
          //             isFrom={'source_page'}
          //           />
          //         ) : isMonthComparisonChart(value) ||
          //           isPartsComparisonChart(value) ? (
          //           <HighCharts
          //             removeFav={this.removeFav}
          //             isPartsCharts={
          //               isPartsComparisonChart(value) ? true : false
          //             }
          //             datatype={''}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClose={this.handleClose}
          //             handleClosePopup={this.handleClose}
          //             chartId={Number(value)}
          //             month1={this.props.comparisonMonths[0]}
          //             month2={this.props.comparisonMonths[1]}
          //             isFrom={'source_page'}
          //           />
          //         ) : isDiscountComparisonApexChart(value) ? (
          //           <ApexChartsDiscount
          //             removeFav={this.removeFav}
          //             datatype={''}
          //             chartId={Number(value)}
          //             handleClosePopup={this.handleClose}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClose={this.handleClose}
          //             month1={this.props.comparisonMonths[0]}
          //             month2={this.props.comparisonMonths[1]}
          //             isFrom={'source_page'}
          //           />
          //         ) : isAddonsComparisonChart(value) ? (
          //           <ApexCharts
          //             removeFav={this.removeFav}
          //             datatype={''}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             chartId={Number(value)}
          //             month1={this.props.comparisonMonths[0]}
          //             month2={this.props.comparisonMonths[1]}
          //             isFrom={'source_page'}
          //           />
          //         ) : isOpportunityChart(value) ? (
          //           <OpportunityCharts
          //             removeFav={this.removeFav}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             chartId={Number(value)}
          //           />
          //         ) : (
          //           <DashboardBarRenderer
          //             removeFav={this.removeFav}
          //             handleChartPopup={this.handleChartPopup}
          //             handleClosePopup={this.handleClose}
          //             handleClose={this.handleClose}
          //             chartId={Number(value)}
          //           />
          //         )}
          //       </div>
          //     );
          //   })}
          // </ReactGridLayout>
        )}
      </div>
    );
  }
}
