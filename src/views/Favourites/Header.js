import React from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import {
  Card,
  CardContent,
} from '@material-ui/core';

const styles = theme => ({
  root: {
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(6, 2)
  },
  card: {
    width: '100%',
    height: '50vh',
    maxWidth: '100%',
    overflow: 'visible',
    display: 'flex',
    position: 'relative',
    // backgroundColor: '#c0d1df',
    backgroundColor: theme.palette.primary.light,
    border: 'solid #003d6b',
    marginTop: '-3px',
    '& > *': {
      flexGrow: 1,
      flexBasis: '50%',
      width: '50%'
    }
  },
  content: {
    padding: theme.spacing(8, 4, 3, 4)
  },
  h: {
    color: '#000',
    position:   'absolute',
    top:  '5px',
    left: '5px',
    textAlign: 'left'
  },
  h1: {
    color: '#000',
    position:   'absolute',
    textAlign: 'center',
    left: '28%',
    fontWeight: 'bold',
    marginTop: '45px'
  },
  p: {
    color: '#fff'
  }
});
function Header(props) {
  const { classes } = props;
  return (
    <Card className={classes.card}>
      <CardContent className={classes.content}>
        <Typography variant="h5" component="h3" className={classes.h}>
          KPI Scorecard
        </Typography>
        <Typography variant="h3" component="h3" className={classes.h1} >
          Content of this Section is a  Work-In-Progress
        </Typography>
      </CardContent>
    </Card>
  );
}

Header.propTypes = {
  classes: PropTypes.object.isRequired,
};

export default withStyles(styles)(Header);