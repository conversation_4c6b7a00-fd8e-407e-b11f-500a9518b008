import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import $ from 'jquery';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import Charts from './Charts';
import { useSelector } from 'react-redux';
import { getAllFavouriteCharts } from 'src/utils/hasuraServices';
var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function MyFavorites() {
  const [isLoading, setIsLoading] = useState(true);
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [favouriteData, setFavouriteData] = useState([]);
  let store = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  useEffect(() => {
   if(store){
    if (session.favouriteCharts.length > 0) {
      setIsLoading(false);
    }
    localStorage.setItem('userName', session.userName);
   }
    
  }, [session.favouriteCharts,store]);

  useEffect(() => {
    if(store){
      if (session.userName != null) {
        getAllFavouriteCharts(session.userName, callback => {
          if (callback) { 
            const favouriteData = callback.length;
            setFavouriteData(favouriteData);
          }
        });
      }
    }
  }, [session.userName,store]);
  return (
    <Page className={classes.root} title="MyFavorites">
      <Charts
        favourites={session.favouriteCharts.filter(
          (item, i, ar) => ar.indexOf(item) === i
        )}
        comparisonMonths={session.favouriteChartsComparisonMonth}
        favouriteData={favouriteData}
        session={session}
      />
    </Page>
  );
}

export default MyFavorites;
