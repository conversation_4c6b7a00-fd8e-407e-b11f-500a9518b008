import React from 'react';
import RG<PERSON>, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { Paper, Divider, IconButton } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import DashboardLineRenderer from 'src/components/charts/DashboardLineRenderer';
import 'src/styles.css';
import {
  getDataGridConfigurationFav,
  getDataGridConfigurationFavOneRow,
  getLayoutConfiguration,
  saveLayoutConfiguration,
  isBarChart,
  isWormixChart,
  isTechChart,
  isAdvisorChart,
  isTechComparisonChart,
  isAdvisorComparisonChart,
  isAdvisorOpcategoryChart,
  isMonthComparison<PERSON>hart,
  isDiscountComparison<PERSON><PERSON>x<PERSON><PERSON>,
  isPartsComparison<PERSON><PERSON>,
  isAddonsComparison<PERSON><PERSON>,
  isOpportunityChart
} from '../../utils/Utils';

import DashboardBarRenderer from 'src/components/charts/DashboardBarRenderer';
import WorkMixRenderer from 'src/views/AnalyzeData/WorkMixCharts/BarChartRenderer';
import TechnicianRender from 'src/views/AnalyzeData/TechEfficiency/BarChartRenderer';
import TechnicianRenderP from 'src/views/AnalyzeData/TechEfficiency/BarChartRendererPostgraphile';
import { getChartName } from 'src/components/ViewGraphDetailsAction';
import AdvisorPerformanceRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/AdvisorChartRenderer';
//import AdvisorPerformanceRenderer from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/BarChartRenderer';
import TechnicianMonthComparison from 'src/views/AnalyzeData/TechEfficiency/ColumnRenderer';
import AdvisorComparisonCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/ColumnRenderer';
import AdvisorOpcategoryCharts from 'src/views/AnalyzeData/ServiceAdvisorEfficiency/OpCategoryChartRenderer';
import HighCharts from 'src/views/AnalyzeData/ComparisonCharts/ColumnRenderer';
import ApexChartsDiscount from 'src/views/Discounts/DiscountColumnRenderer';
import ApexCharts from 'src/views/AddOns/ServiceAdvisor/ColumnRenderer';
import OpportunityCharts from 'src/components/charts/OpportunityCharts';
import 'src/styles.css';
import clsx from 'clsx';
import PageHeader from 'src/components/PageHeader';
import 'src/styles.css';

var lodash = require('lodash');

const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  componentDidUpdate(prevProps) {
    if (
      prevProps.session.storeSelected &&
      JSON.parse(localStorage.getItem('selectedStoreId'))
    ) {
      if (
        JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
        JSON.parse(prevProps.session.storeSelected)[0]
      ) {
        console.log(
          'stores=00==',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
            JSON.parse(prevProps.session.storeSelected)[0]
        );
        this.setResetDashboard();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 48,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      reloadCounter: 0,
      timePassed: false,
      favouriteData: [],
      filters: 2,
      isLoading: false,
      checkEmpty: false,
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-fav') || {}
        )
      )
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }
  componentDidMount() {
    this.setResetDashboard();
  }
  setFilterCharts = value => {
    if (value) {
      this.setState({
        filters: value
      });
    }
    return this.state.filters;
  };
  removeFav = value => {
    if (value == 2) {
      this.setState({
        isLoading: true
      });
    } else if (value == 3) {
      this.setState({
        isLoading: false
      });
    }
    // return this.state.layout;
    this.setResetDashboard();
  };
  setResetDashboard = value => {
    //alert();
    this.setState(prevState => ({
      reloadCounter: prevState.reloadCounter + 1
    }));
    if (value) {
      this.setState({
        layout: []
      });
    }

    return this.state.layout;
  };
  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-fav');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
    // window.location.reload();
  }
  handleChange = val => {
    this.setState({ checkEmpty: val });
  };
  handleChartPopup = value => {
    this.setState({
      popupChartId: value,
      open: true
    });
  };
  handleClose = val => {
    this.setState({ open: false });
  };

  render() {
    setTimeout(() => {
      this.setState({ timePassed: true });
    }, 1000);
    const favourites = this.props.favourites.filter(Number);
    const favouritesArr = favourites.filter(n => n);
    const { classes } = this.props;
    let oneRowCharts = [];
    let twoRowCharts = [];
    let commonCharts = [];
    let result = [];

    let realm = localStorage.getItem('realm');
    let chart_length = 0;
    if (favouritesArr.length > 0) {
      //this.setResetDashboard();
      favouritesArr.map((value, index) => {
        let config;
console.log('VALUE------------------------------------>',value);

        // Debug logging for chart 1288
        if (value == 1288) {
          console.log('Chart 1288 Debug:');
          console.log('isTechComparisonChart(1288):', isTechComparisonChart(value));
          console.log('isMonthComparisonChart(1288):', isMonthComparisonChart(value));
          console.log('isPartsComparisonChart(1288):', isPartsComparisonChart(value));
          console.log('isDiscountComparisonApexChart(1288):', isDiscountComparisonApexChart(value));
          console.log('isAddonsComparisonChart(1288):', isAddonsComparisonChart(value));
          console.log('isOpportunityChart(1288):', isOpportunityChart(value));
          console.log('isTechChart(1288):', isTechChart(value));
        }

        const isOneRowChart = (
          isTechComparisonChart(value) ||
          isMonthComparisonChart(value) ||
          isPartsComparisonChart(value) ||
          isDiscountComparisonApexChart(value) ||
          isAddonsComparisonChart(value) ||
          isOpportunityChart(value) ||
          (isTechChart(value) && value == 1352) ||
          (isTechChart(value) && value == 1363) ||
          (isTechChart(value) && value == 1358) ||
          (isTechChart(value) && value == 1345)
        );

        if (isOneRowChart) {
          console.log(`Chart ${value} classified as ONE ROW (full width)`);
          oneRowCharts.push(value);
          //config = 'tech';
        } else {
          console.log(`Chart ${value} classified as TWO ROW (half width)`);
          twoRowCharts.push(value);
          // config = 'common';
        }
      });
      // let commonChartUpdated = [];
      // let twoRowsChartUpdated = [];
      // if (commonCharts.length % 3 == 1 && twoRowCharts.length > 0) {
      //   commonChartUpdated = commonCharts.splice(0, 1);
      // } else if (commonCharts.length % 3 == 2 && twoRowCharts.length > 0) {
      //   commonChartUpdated = commonCharts.splice(0, 2);
      // }
      // twoRowCharts = commonChartUpdated.concat(twoRowCharts);
      // if (twoRowCharts.length % 2 == 1 && oneRowCharts.length > 0) {
      //   twoRowsChartUpdated = twoRowCharts.splice(0, 1);
      // }
      // oneRowCharts = twoRowsChartUpdated.concat(oneRowCharts);
    }

    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          <PageHeader
            title={'My Favorites'}
            setResetDashboard={this.setResetDashboard}
          />

          <Divider />
          {(this.props.favouriteData == 0 ||
            this.props.favouriteData.length == undefined) &&
          this.props.favourites &&
          (this.props.favourites == 0 || this.props.favourites.length == 0) ? (
            <div>
              {this.state.timePassed == true ? (
                <Paper
                  square
                  style={
                    localStorage.getItem('realm') == 'demo_store'
                      ? { margin: 8, marginTop: '10px' }
                      : { margin: 8 }
                  }
                >
                  <Tabs
                    // value={this.state.tabSelection}
                    // onChange={this.handleTabChange}
                    variant="fullWidth"
                    indicatorColor="secondary"
                    textColor="secondary"
                    aria-label="icon label tabs example"
                  >
                    <Tab
                      style={{
                        textTransform: 'none',
                        paddingRight: 18,
                        color:
                          process.env.REACT_APP_DEALER == 'Armatus'
                            ? '#003d6b'
                            : '#C2185B'
                      }}
                      label={
                        <div>
                          {'There are no favorites added. Please click on'}{' '}
                          <IconButton size="medium">
                            <StarBorderIcon />
                          </IconButton>{' '}
                          {'to add charts to favorites'}
                        </div>
                      }
                      value="one"
                    />
                  </Tabs>
                </Paper>
              ) : null}
            </div>
          ) : (
            <ReactGridLayout
              {...this.props}
              layout={this.state.layout}
              onLayoutChange={this.onLayoutChange}
              isResizable={false}
              className={'favgrid'}
              key={this.state.reloadCounter}
              isDraggable={false}
              // style={{ display: this.state.checked ? 'none' : 'block' }}
            >
              {twoRowCharts.map((value, index) => {
                return (
                  <div
                    className={clsx(
                      classes.gridContainer,
                      'diagram-section two'
                    )}
                    key={index}
                    data-grid={getDataGridConfigurationFav(index)}
                    id={'chart-' + value}
                  >
                    {isBarChart(value) == false &&
                    isWormixChart(value) == false &&
                    isTechChart(value) == false &&
                    isAdvisorChart(value) == false &&
                    isTechComparisonChart(value) == false &&
                    isAdvisorComparisonChart(value) == false &&
                    isAdvisorOpcategoryChart(value) == false &&
                    isMonthComparisonChart(value) == false &&
                    isDiscountComparisonApexChart(value) == false &&
                    isPartsComparisonChart(value) == false &&
                    isAddonsComparisonChart(value) == false &&
                    isOpportunityChart(value) == false ? (
                      <DashboardLineRenderer
                        filterCharts={this.state.filters}
                        removeFavourite={this.removeFav}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                        isFrom={'source_page'}
                        headerClick={this.getHighlighedDiv}
                        chartData={[]}
                        isDataLoaded={false}
                      />
                    ) : isWormixChart(value) ? (
                      <WorkMixRenderer
                        removeFav={this.removeFav}
                        chartId={Number(value)}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        isFrom={'source_page'}
                      />
                    ) : // ) : isTechChart(value) && Number(value) != 1352 ? (
                    //   <TechnicianRender
                    //     removeFav={this.removeFav}
                    //     chartId={Number(value)}
                    //     title={value}
                    //     handleClose={this.handleClose}
                    //     handleClosePopup={this.handleClose}
                    //     tech={
                    //       localStorage.getItem('selectedTech') == undefined
                    //         ? 'All'
                    //         : localStorage.getItem('selectedTech')
                    //     }
                    //     techName={localStorage.getItem('selectedTechName')}
                    //     checkEmpty={this.handleChange}
                    //     callFrom={0}
                    //     handleChartPopup={this.handleChartPopup}
                    //     chartTitle={getChartName(value)}
                    //     isFrom={'source_page'}
                    //   />
                    isTechChart(value) ? (
                      <TechnicianRenderP
                        removeFav={this.removeFav}
                        chartId={Number(value)}
                        title={value}
                        handleClose={this.handleClose}
                        handleClosePopup={this.handleClose}
                        tech={
                          localStorage.getItem('selectedTech') == undefined
                            ? 'All'
                            : localStorage.getItem('selectedTech')
                        }
                        techName={localStorage.getItem('selectedTechName')}
                        // tech={'All'}
                        // techName={'All'}
                        checkEmpty={this.handleChange}
                        callFrom={0}
                        handleChartPopup={this.handleChartPopup}
                        chartTitle={getChartName(value)}
                        isFrom={'source_page'}
                        session={this.props.session}
                      />
                    ) : isAdvisorChart(value) ? (
                      <AdvisorPerformanceRenderer
                        removeFav={this.removeFav}
                        chartId={Number(value)}
                        title={1}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        handleChartPopup={this.handleChartPopup}
                        advisor={localStorage.getItem('selectedAdvisor')}
                        advisorName={['All']}
                        serviceAdvisor={this.props.session.serviceAdvisor}
                        checkEmpty={this.handleChange}
                        isFrom={'source_page'}
                        session={this.props.session}
                      />
                    ) : isTechComparisonChart(value) ? (
                      <TechnicianMonthComparison
                        removeFav={this.removeFav}
                        datatype={''}
                        handleClosePopup={this.handleClose}
                        callFrom={0}
                        handleChartPopup={this.handleChartPopup}
                        month1={this.props.comparisonMonths[0]}
                        handleClose={this.handleClose}
                        month2={this.props.comparisonMonths[1]}
                        chartId={Number(value)}
                        source={'source_page'}
                        session={this.props.session}
                      />
                    ) : isAdvisorComparisonChart(value) ? (
                      <AdvisorComparisonCharts
                        removeFav={this.removeFav}
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        chartId={Number(value)}
                        isFrom={'source_page'}
                      />
                    ) : isAdvisorOpcategoryChart(value) ? (
                      <AdvisorOpcategoryCharts
                        removeFav={this.removeFav}
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        month1={this.props.comparisonMonths[0]}
                        chartId={Number(value)}
                        isFrom={'source_page'}
                      />
                    ) : isMonthComparisonChart(value) ||
                      isPartsComparisonChart(value) ? (
                      <HighCharts
                        removeFav={this.removeFav}
                        isPartsCharts={
                          isPartsComparisonChart(value) ? true : false
                        }
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClose={this.handleClose}
                        handleClosePopup={this.handleClose}
                        chartId={Number(value)}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        isFrom={'source_page'}
                      />
                    ) : isDiscountComparisonApexChart(value) ? (
                      <ApexChartsDiscount
                        removeFav={this.removeFav}
                        datatype={''}
                        chartId={Number(value)}
                        handleClosePopup={this.handleClose}
                        handleChartPopup={this.handleChartPopup}
                        handleClose={this.handleClose}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        isFrom={'source_page'}
                      />
                    ) : isAddonsComparisonChart(value) ? (
                      <ApexCharts
                        removeFav={this.removeFav}
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        isFrom={'source_page'}
                      />
                    ) : isOpportunityChart(value) ? (
                      <OpportunityCharts
                        removeFav={this.removeFav}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                      />
                    ) : (
                      <DashboardBarRenderer
                        removeFav={this.removeFav}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                        isFrom={'source_page'}
                      />
                    )}
                  </div>
                );
              })}
              {oneRowCharts.map((value, index) => {
                let config = 'common';
                console.log(
                  'value==',
                  getDataGridConfigurationFavOneRow(
                    index + twoRowCharts.length + 1
                  )
                );
                return (
                  <div
                    id={'chart-' + value}
                    className={clsx(
                      classes.gridContainer,
                      'diagram-section one'
                    )}
                    key={index + twoRowCharts.length + 1}
                    data-grid={getDataGridConfigurationFavOneRow(
                      index + twoRowCharts.length + 1
                    )}
                  >
                    {isBarChart(value) == false &&
                    isWormixChart(value) == false &&
                    isTechChart(value) == false &&
                    isAdvisorChart(value) == false &&
                    isTechComparisonChart(value) == false &&
                    isAdvisorComparisonChart(value) == false &&
                    isAdvisorOpcategoryChart(value) == false &&
                    isMonthComparisonChart(value) == false &&
                    isDiscountComparisonApexChart(value) == false &&
                    isPartsComparisonChart(value) == false &&
                    isAddonsComparisonChart(value) == false &&
                    isOpportunityChart(value) == false ? (
                      <DashboardLineRenderer
                        removeFavourite={this.removeFav}
                        filterCharts={this.state.filters}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                        isFrom={'source_page'}
                      />
                    ) : isWormixChart(value) ? (
                      <WorkMixRenderer
                        removeFav={this.removeFav}
                        chartId={Number(value)}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        isFrom={'source_page'}
                      />
                    ) : isTechChart(value) ? (
                      <TechnicianRenderP
                        removeFav={this.removeFav}
                        chartId={Number(value)}
                        title={value}
                        handleClose={this.handleClose}
                        handleClosePopup={this.handleClose}
                        tech={
                          localStorage.getItem('selectedTech') == undefined
                            ? 'All'
                            : localStorage.getItem('selectedTech')
                        }
                        techName={localStorage.getItem('selectedTechName')}
                        // tech={'All'}
                        // techName={'All'}
                        checkEmpty={this.handleChange}
                        callFrom={0}
                        handleChartPopup={this.handleChartPopup}
                        chartTitle={getChartName(value)}
                        isFrom={'source_page'}
                        session={this.props.session}
                      />
                    ) : isAdvisorChart(value) ? (
                      <AdvisorPerformanceRenderer
                        removeFav={this.removeFav}
                        chartId={Number(value)}
                        title={1}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        handleChartPopup={this.handleChartPopup}
                        advisor={localStorage.getItem('selectedAdvisor')}
                        advisorName={['All']}
                        serviceAdvisor={this.props.session.serviceAdvisor}
                        checkEmpty={this.handleChange}
                        session={this.props.session}
                        isFrom={'source_page'}
                      />
                    ) : isTechComparisonChart(value) ? (
                      <TechnicianMonthComparison
                        removeFav={this.removeFav}
                        datatype={''}
                        handleClosePopup={this.handleClose}
                        callFrom={0}
                        handleChartPopup={this.handleChartPopup}
                        month1={this.props.comparisonMonths[0]}
                        handleClose={this.handleClose}
                        month2={this.props.comparisonMonths[1]}
                        chartId={Number(value)}
                        source={'source_page'}
                        session={this.props.session}
                      />
                    ) : isAdvisorComparisonChart(value) ? (
                      <AdvisorComparisonCharts
                        removeFav={this.removeFav}
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        chartId={Number(value)}
                        isFrom={'source_page'}
                      />
                    ) : isAdvisorOpcategoryChart(value) ? (
                      <AdvisorOpcategoryCharts
                        removeFav={this.removeFav}
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        month1={this.props.comparisonMonths[0]}
                        chartId={Number(value)}
                        isFrom={'source_page'}
                      />
                    ) : isMonthComparisonChart(value) ||
                      isPartsComparisonChart(value) ? (
                      <HighCharts
                        removeFav={this.removeFav}
                        isPartsCharts={
                          isPartsComparisonChart(value) ? true : false
                        }
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClose={this.handleClose}
                        handleClosePopup={this.handleClose}
                        chartId={Number(value)}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        isFrom={'source_page'}
                      />
                    ) : isDiscountComparisonApexChart(value) ? (
                      <ApexChartsDiscount
                        removeFav={this.removeFav}
                        datatype={''}
                        chartId={Number(value)}
                        handleClosePopup={this.handleClose}
                        handleChartPopup={this.handleChartPopup}
                        handleClose={this.handleClose}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        isFrom={'source_page'}
                      />
                    ) : isAddonsComparisonChart(value) ? (
                      <ApexCharts
                        removeFav={this.removeFav}
                        datatype={''}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                        month1={this.props.comparisonMonths[0]}
                        month2={this.props.comparisonMonths[1]}
                        isFrom={'source_page'}
                      />
                    ) : isOpportunityChart(value) ? (
                      <OpportunityCharts
                        removeFav={this.removeFav}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                      />
                    ) : (
                      <DashboardBarRenderer
                        removeFav={this.removeFav}
                        handleChartPopup={this.handleChartPopup}
                        handleClosePopup={this.handleClose}
                        handleClose={this.handleClose}
                        chartId={Number(value)}
                      />
                    )}
                  </div>
                );
              })}
            </ReactGridLayout>
          )}
        </Paper>
      </div>
    );
  }
}
const styles = theme => ({
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  root: {
    flexGrow: 1
    //width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    //justifyContent: 'space-between',
    width: '100%'
  },
  gridContainer: {
    padding: 5
  }
});

//export default withStyles(styles)(withKeycloak(Charts));
export default withStyles(styles)(Charts);
