import React from 'react';
import { makeStyles } from '@material-ui/styles';
import {
  setServiceAdvisorErrors,
  setServiceAdvisorErrorsCount,
  setAllErrorsCount
} from 'src/actions';
import { getAllOpcodeErrors } from 'src/utils/hasuraServices';
import Page from 'src/components/Page';
import ServiceAdvisor from './ServiceAdvisor';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { useHistory } from 'react-router';

var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function ChartMasterEdit() {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();

  const [notifications, setNotifications] = React.useState([]);
  const session = useSelector(state => state.session);
  const [allOpcodeErrors, setAllErrors] = React.useState(0);
  const addErrorCount = status => {
    let mounted = true;
    getAllOpcodeErrors(callback => {
      const advisorErrorArr = callback.filter(function(el) {
        return el.cType === 'advisor';
      });
      var err = callback.reduce(
        (acc, o) => acc + parseInt(o.noncategorizedcount),
        0
      );
      var b = lodash.filter(callback, function(o) {
        if (o.noncategorizedcount > 0) return o;
      }).length;
      dispatch(setAllErrorsCount(b));
      //dispatch(setAllErrorsCount(err));
      if (advisorErrorArr.length > 0) {
        if (mounted) {
          if (advisorErrorArr[0].noncategorizedcount > 0) {
            dispatch(setServiceAdvisorErrors(true));
            dispatch(
              setServiceAdvisorErrorsCount(
                advisorErrorArr[0].noncategorizedcount
              )
            );
          } else {
            dispatch(setServiceAdvisorErrors(false));
            dispatch(setServiceAdvisorErrorsCount(0));
          }
        }
      } else {
        setNotifications([]);
        dispatch(setServiceAdvisorErrors(false));
        dispatch(setServiceAdvisorErrorsCount(0));
      }
    });
  };
  return (
    <Page className={classes.root} title="Service Advisor Setup">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <ServiceAdvisor
          dispatch={dispatch}
          setErrorCount={addErrorCount}
          session={session}
          history={history}
        />
      )}
    </Page>
  );
}

export default ChartMasterEdit;
