import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Typography,
  withStyles,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import Button from '@material-ui/core/Button';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import clsx from 'clsx';
import RestoreIcon from '@material-ui/icons/Restore';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  DELETE_SERVICE_ADVISOR,
  INSERT_SERVICE_ADVISOR,
  UPDATE_SERVICE_ADVISOR
} from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { getAllSADetails } from 'src/utils/hasuraServices';
import { render } from 'react-dom';
import { setAdvisorStatus, setAdvisorNickName } from 'src/actions';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import { withKeycloak } from '@react-keycloak/web';
import ActiveStatusRenderer from './ActiveStatusRenderer';
import { traceSpan } from 'src/utils/OTTTracing';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
class Advisor extends React.Component {
  componentDidUpdate(prevProps) {
    if (
      prevProps.session.storeSelected &&
      JSON.parse(localStorage.getItem('selectedStoreId'))
    ) {
      if (
        JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
        JSON.parse(prevProps.session.storeSelected)[0]
      ) {
        console.log(
          'stores=',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
            JSON.parse(prevProps.session.storeSelected)[0]
        );
        this.getAgGridData();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    let startEdit = this;
    this.addNewRow = this.addNewRow.bind(this);

    this.state = {
      nickError: false,
      cancel: false,
      openSnackbar: false,
      showCharts: false,
      selectValue: 'other',
      selectValueFor: this.props.category,
      selectValueType: this.props.reportType,
      ddLabel: '< 2',
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      oldDataArr: [],
      newDataArr: [],
      prevDataArr: [],
      editedRowId: null,
      advisorNewStatus: '',
      success: false,
      isEdited: false,
      columnDefs: [
        {
          headerName: ' Id',
          chartDataType: 'series',
          width: 150,
          field: 'id',

          hide: true
          // editable: false,
        },
        {
          headerName: 'Service Advisor',
          chartDataType: 'series',
          width: 150,
          suppressMenu: true,
          sortable: true,
          // suppressClickEdit: true,
          editable: false,
          unSortIcon: true,
          field: 'serviceadvisor',

          // comparator: function(valueA, valueB) {
          //   return valueA - valueB;
          // },
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          comparator: (valueA, valueB) => {
            // Helper function to extract numeric part from string
            const extractNumericPart = str => {
              const matches = str.match(/(\d+)/);
              return matches ? parseInt(matches[0], 10) : NaN;
            };

            // Helper function to extract non-numeric part from string
            const extractNonNumericPart = str => {
              return str.replace(/(\d+)/, '').toLowerCase();
            };

            // Extract numeric and non-numeric parts from values
            const numericPartA = extractNumericPart(valueA);
            const numericPartB = extractNumericPart(valueB);
            const nonNumericPartA = extractNonNumericPart(valueA);
            const nonNumericPartB = extractNonNumericPart(valueB);

            // Compare non-numeric parts
            if (nonNumericPartA !== nonNumericPartB) {
              return nonNumericPartA.localeCompare(nonNumericPartB);
            }

            // Compare numeric parts if both are numeric
            if (!isNaN(numericPartA) && !isNaN(numericPartB)) {
              return numericPartA - numericPartB;
            }

            // If one is numeric and the other is not, numeric comes first
            if (!isNaN(numericPartA)) {
              return -1;
            } else if (!isNaN(numericPartB)) {
              return 1;
            }

            // Fallback to comparing original values if both parts are non-numeric
            return valueA.localeCompare(valueB);
          }
          // editable: function(params) {
          //   if (params.data.id == '') {
          //     return true;
          //   } else {
          //     return false;
          //   }
          // }
        },
        {
          headerName: 'Name',
          chartDataType: 'series',
          width: 150,
          field: 'name',
          editable: false,

          suppressMenu: true,
          unSortIcon: true,
          tooltip: params => params.value,

          cellStyle() {
            return { border: ' 0px white' };
          },
          comparator: (valueA, valueB) => {
            // Trim leading/trailing spaces and compare
            const trimmedA = valueA ? valueA.trim() : '';
            const trimmedB = valueB ? valueB.trim() : '';

            // If both values are numbers, sort numerically
            const numA = Number(trimmedA);
            const numB = Number(trimmedB);

            if (!isNaN(numA) && !isNaN(numB)) {
              return numA - numB; // Numeric sort
            }

            // Otherwise, sort as strings
            return trimmedA.localeCompare(trimmedB);
          },
          filterParams: {
            comparator: (valueA, valueB) => {
              // Trim leading/trailing spaces and compare
              const trimmedA = valueA ? valueA.trim() : '';
              const trimmedB = valueB ? valueB.trim() : '';

              // If both values are numbers, sort numerically
              const numA = Number(trimmedA);
              const numB = Number(trimmedB);

              if (!isNaN(numA) && !isNaN(numB)) {
                return numA - numB; // Numeric sort
              }

              // Otherwise, sort as strings
              return trimmedA.localeCompare(trimmedB);
            }
          }
        },
        {
          headerName: 'Nick Name',
          chartDataType: 'series',
          width: 150,
          field: 'nickname',
          flex: 1,
          editable: false,

          singleClickEdit: true,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { border: ' 0px white' };
          },
          // cellStyle: params => {
          //   const regExp = /^[a-zA-Z\s]*$/;
          //   if (!regExp.test(params.value)) {
          //     return { border: '2px solid red' };
          //   }
          //   return { border: '0px white' };
          // },
          cellEditor: 'agTextCellEditor',
          onCellValueChanged: params => {
            const regExp = /^[a-zA-Z\s]*$/;
            if (!regExp.test(params.newValue)) {
              startEdit.setState({ nickError: true });
              // Set the border to red when invalid input is entered
              params.node.setDataValue('nickname', params.oldValue); // Reset to the old value
              params.api.refreshCells({ rowNodes: [params.node] }); // Refresh to update the cell style
            }
          }
          // editable: function(params) {
          //   return (
          //     props.keycloak &&
          //     (props.keycloak.realmAccess.roles.includes('admin') ||
          //       props.keycloak.realmAccess.roles.includes('user'))
          //   );
          // }
        },
        {
          headerName: 'Dept.',
          chartDataType: 'series',
          // width: 90,
          minWidth: 90,
          flex: 1,
          field: 'department',
          editable: true,
          suppressMenu: true,
          unSortIcon: true,
          cellEditor: 'agSelectCellEditor',
          //hide: localStorage.getItem('dms') == 'dtk' ? true : false,
          hide: false,
          //flex: 2,
          cellEditorParams: {
            values: depart
          },
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          },
          filterParams: {
            valueFormatter: function(params) {
              return lookupValue(departmentMappings, params.value);
            }
          },
          valueFormatter: function(params) {
            return lookupValue(departmentMappings, params.value);
          }
        },
        {
          headerName: 'Active / Inactive',
          chartDataType: 'series',
          width: 150,

          field: 'active',
          suppressMenu: true,
          unSortIcon: true,
          editable: true,
          //  cellStyle: this.cellStyles,
          // onCellClicked: this.setAdvisorStatus,
          // cellRenderer: function(params) {
          //   var input = document.createElement('input');
          //   input.type="checkbox";
          //   input.id="advisorStatus";
          //   input.checked=params.value == 1 ? 'checked' :'';
          //   input.value=params.value;
          //   input.addEventListener('click', function (event) {
          //     input.value=event.target.checked ? 1 : 0;
          //   });
          //   return input;
          // }
          // cellRenderer: params => {
          //   var index = params.rowIndex;
          //   return `<input type='checkbox' value=${params.value}
          //   id="advisorStatus${index}" disabled=disabled
          //   ${params.value == 1 ? 'checked' : ''} />`;
          // }
          cellEditor: 'agSelectCellEditor',
          //hide: localStorage.getItem('dms') == 'dtk' ? true : false,
          hide: false,
          //flex: 2,
          valueFormatter: function(params) {
            return params.value == 1
              ? 'Active'
              : params.value == null
              ? '----'
              : 'Inactive';
          },
          cellEditorParams: {
            values: this.extractValues(this.objCategoryMappings)
          },
          filterParams: {
            valueFormatter: function(params) {
              return params.value == 1
                ? 'Active'
                : params.value == null
                ? null
                : 'Inactive';
            }
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          cellStyle: this.cellStyles,
          width: 200,
          filter: false,

          suppressMenu: true,
          sortable: false,
          editable: false,
          // tooltip: function(params) {
          //   return 'Edit';
          // },
          cellRenderer: function(params) {
            var index = params.rowIndex;
            var eDiv = document.createElement('div');

            if (params.data.id == '' && params.data.name == '') {
              eDiv.innerHTML =
                '<button  title="Edit" id="btneditadvisor' +
                index +
                '" style="background: #384163; color: #fff; display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-adv"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncanceladvisor' +
                index +
                '" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-adv" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdateadvisor' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-adv" ><i class="fas fa-save"></i></button>&nbsp;<button title="delete" id="btndelete' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>';
            } else {
              eDiv.innerHTML =
                '<button  title="Edit" id="btneditadvisor' +
                index +
                '" style="background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-adv"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncanceladvisor' +
                index +
                '" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-adv" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdateadvisor' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-adv" ><i class="fas fa-save"></i></button>&nbsp;<button title="delete" id="btndelete' +
                index +
                '" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button" ><i class="fas fa-trash-alt"></i></button>';
            }
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button-adv').attr('disabled', 'disabled');
                $('.edit-button-adv').css('background', '#38416373');
                $('.edit-button-adv').css('cursor', 'default');
              });
              $('.editNickName').attr('disabled', true);
            }
            if (index != undefined) {
              var eButton = eDiv.querySelectorAll('.edit-button-adv')[0];
              var uButton = eDiv.querySelectorAll('.update-button-adv')[0];
              var cButton = eDiv.querySelectorAll('.cancel-button-adv')[0];
              var dButton = eDiv.querySelectorAll('.delete-button')[0];

              eButton.addEventListener('click', () => {
                let oldIndex = localStorage.getItem('oldIndex');
                if (oldIndex) {
                  startEdit.onBtStopEditing(oldIndex);
                  let rowData = startEdit.state.oldDataArr;
                  var rowNode = params.api.getDisplayedRowAtIndex(oldIndex);
                  rowNode.setDataValue('nickname', rowData[0].nickname);
                  rowNode.setDataValue('active', rowData[0].active);
                  rowNode.setDataValue('department', rowData[0].department);
                }
                startEdit.gridApi.redrawRows();
                $(`.cancel-button-adv`).hide();
                $(`.edit-button-adv`).show();
                $(`.update-button-adv`).hide();
                //startEdit.gridApi.redrawRows();
                let rowData = startEdit.state.oldDataArr;
                startEdit.setState({
                  editedRowId: null
                });
                // rowData.map((object, i) => {
                //   let rowNode = params.api.getDisplayedRowAtIndex(object.index);
                //   startEdit.setState({
                //     editedRowId: null
                //   });
                //   console.log('rowData=', index, object.index, rowNode);
                //   rowNode.setDataValue('nickname', rowData[i].nickname);
                //   rowNode.setDataValue('active', rowData[i].active);
                //   $('#btneditadvisor' + object.index).show();
                //   $('#btncanceladvisor' + object.index).hide();
                //   $('#btnupdateadvisor' + object.index).hide();
                //   $('#btndelete' + object.index).hide();
                //   $('#advisorStatus' + object.index).prop(
                //     'checked',
                //     rowData.active == 1 ? true : false
                //   );
                //   $('#advisorStatus' + object.index).prop(
                //     'disabled',
                //     'disabled'
                //   );
                // });

                // startEdit.gridApi.refreshCells({force : true});
                localStorage.setItem('index', index);
                startEdit.setState({ cancel: false });
                startEdit.onBtStartEditing(index);
                localStorage.setItem('serviceId', params.data.id);
                let editingCells = params.api.getEditingCells();
                // let isCurrentRowEditing = editingCells.some((cell) => {
                //   return cell.rowIndex === params.node.rowIndex;
                // });
                startEdit.setState({
                  editedRowId: index
                });
                let oldArr = {
                  index: index,
                  serviceadvisor: params.data.serviceadvisor,
                  nickname: params.data.nickname,
                  active: params.data.active,
                  department: params.data.department
                };

                var rowPrevArray = [];
                let indexArr = rowPrevArray.findIndex(
                  ({ serviceadvisor }) =>
                    serviceadvisor == params.data.serviceadvisor
                );

                if (indexArr === -1) {
                  rowPrevArray.push(oldArr);
                }

                startEdit.setState({
                  oldDataArr: rowPrevArray
                });

                // startEdit.setState({
                //   oldDataArr: oldArr
                // });
                // localStorage.setItem('status', params.data.active);

                $('#btneditadvisor' + index).hide();
                $('#btncanceladvisor' + index).show();
                $('#btnupdateadvisor' + index).show();
                $('#advisorStatus' + index).prop('disabled', '');
                localStorage.setItem('oldIndex', index);
                // $('#btndelete' + index).show();
              });

              uButton.addEventListener('click', () => {
                startEdit.onBtStopEditing(index);
                console.log('ccccc1====', params.data);
                var selectedId = params.data.id;
                console.log('selectedId=', selectedId);
                var selectedName = params.data.name;
                var selectedAdvisor = params.data.serviceadvisor;
                var nickName = params.data.nickname;
                nickName = nickName ? nickName.trim() : '';
                var advisorStatus = params.data.active;
                var selectedDepartment = params.data.department;
                startEdit.setState({ cancel: false });
                startEdit.setState({
                  editedRowId: null
                });
                // var status = localStorage.getItem('status');

                let arr = startEdit.state.oldDataArr;
                console.log(
                  'data==',
                  arr[0].nickname,
                  params.data.nickname,
                  arr[0].active,
                  params.data.active,
                  advisorStatus,
                  selectedDepartment
                );
                if (
                  arr[0].nickname != params.data.nickname ||
                  arr[0].active != params.data.active ||
                  arr[0].department != params.data.department
                ) {
                  let arrNickname = params.data.nickname
                    ? params.data.nickname.trim()
                    : '';
                  let arrNickname1 = arr[0].nickname
                    ? arr[0].nickname.trim()
                    : '';
                  console.log('nick==', arrNickname, '===', arrNickname1);
                  if (
                    arrNickname != arrNickname1 ||
                    arr[0].active != params.data.active ||
                    arr[0].department != params.data.department
                  ) {
                    var regExp = /^[a-zA-Z\s']+$/;
                    if (
                      (regExp.test(nickName) || nickName == '') &&
                      nickName.length <= 80
                    ) {
                      startEdit.updateServiceAdvisor(
                        selectedId,
                        selectedName,
                        selectedAdvisor,
                        nickName ? nickName.trim() : '',
                        advisorStatus,
                        selectedDepartment
                      );
                      startEdit.setState({
                        oldDataArr: []
                      });
                    } else {
                      let rowData = startEdit.state.oldDataArr;
                      var rowNode = params.api.getDisplayedRowAtIndex(index);
                      rowNode.setDataValue('nickname', rowData[0].nickname);
                      rowNode.setDataValue('active', rowData[0].active);
                      rowNode.setDataValue('department', rowData[0].department);
                    }
                  }
                }
                // if (
                //   selectedId != '' &&
                //   advisorStatus != startEdit.state.advisorNewStatus &&
                //   startEdit.state.advisorNewStatus != ''
                // ) {
                //   startEdit.updateServiceAdvisor(
                //     selectedId,
                //     selectedName,
                //     selectedAdvisor,
                //     nickName ? nickName.trim() : '',
                //     advisorStatus
                //   );
                // } else if (
                //   advisorStatus != startEdit.state.advisorNewStatus &&
                //   startEdit.state.advisorNewStatus != ''
                // ) {
                //   startEdit.insertServiceAdvisor(
                //     selectedId,
                //     selectedName,
                //     selectedAdvisor
                //   );
                // }
                startEdit.gridApi.redrawRows();
                $('#btneditadvisor' + index).show();
                $('#btncanceladvisor' + index).hide();
                $('#btnupdateadvisor' + index).hide();
                $('#btndelete' + index).hide();
                $('#advisorStatus' + index).prop('disabled', 'disabled');
              });
              cButton.addEventListener('click', function() {
                startEdit.setState({ cancel: true });
                startEdit.onBtStopEditing(index);
                let status = $('#advisorStatus' + index).prop('checked');
                startEdit.setState({
                  editedRowId: null
                });
                let data =
                  $('#advisorStatus' + index).val() == 1 ? true : false;
                let advisorStatus = status == data ? status : !status;
                let rowData = startEdit.state.oldDataArr;

                var rowNode = params.api.getDisplayedRowAtIndex(index);
                rowNode.setDataValue('nickname', rowData[0].nickname);
                rowNode.setDataValue('active', rowData[0].active);
                rowNode.setDataValue('department', rowData[0].department);
                $('#btneditadvisor' + index).show();
                $('#btncanceladvisor' + index).hide();
                $('#btnupdateadvisor' + index).hide();
                $('#btndelete' + index).hide();
                $('#advisorStatus' + index).prop(
                  'checked',
                  rowData.active == 1 ? true : false
                );
                $('#advisorStatus' + index).prop('disabled', 'disabled');
                // startEdit.getAgGridData();
              });
              dButton.addEventListener('click', function() {
                var selectedId = params.data.id;
                if (selectedId != '') {
                  startEdit.deleteServiceAdvisor(selectedId);
                }
                $('#btneditadvisor' + index).show();
                $('#btncanceladvisor' + index).hide();
                $('#btnupdateadvisor' + index).hide();
                $('#btndelete' + index).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      editType: 'fullRow',
      chartName: null,
      context: { componentParent: this },
      frameworkComponents: {
        activeStatusRenderer: ActiveStatusRenderer
      },
      // components: { singleClickEditRenderer: getRenderer() },
      defaultColDef: {
        enableValue: true,
        suppressKeyboardEvent: params => params.event.keyCode === 13,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: true,
        editable: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        suppressMovable: false
      }
    };
  }
  cellStyles = () => {
    return {
      textAlign: 'center',
      border: ' 0px white'
    };
  };
  // onCellValueChanged = params => {alert('params')
  //   console.log("params=",params.newValue != params.oldValue,params.newValue,params.oldValue);
  //   if (params.newValue != params.oldValue) {
  //     this.setState({ isEdited: true });
  //   }
  // };
  getAdvForDrilldown = () => {
    let data = [1, 0];
    return data;
  };
  objCategoryMappings = [
    {
      1: 'Active',
      0: 'Inactive'
    }
  ];
  extractValues(mappings) {
    return Object.keys(mappings[0]);
  }
  setAdvisorStatus = data => {
    data
      ? this.setState({
          advisorNewStatus: data.event.target.checked == true ? '1' : '0'
        })
      : this.setState({ advisorNewStatus: '' });
  };
  // onBtStopEditing = () => {
  //   this.gridApi.stopEditing();
  // };
  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    this.state.rawGridApi.setColumnDefs(groupColumn);
  };
  updateServiceAdvisor = (
    Id,
    Name,
    ServiceAdvisor,
    nickName,
    advisorStatus,
    pDepartment
  ) => {
    let statusval = this.state.advisorNewStatus;

    const start = new Date();
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_SERVICE_ADVISOR,
        variables: {
          id: Id,
          user_id: localStorage.getItem('userID'),
          advisor: ServiceAdvisor,
          pCategorized: advisorStatus == null ? 0 : 1,
          statusval:
            statusval == ''
              ? advisorStatus == null
                ? 0
                : advisorStatus
              : statusval,
          nickName: nickName ? nickName.trim() : null, //  store_id: this.state.storeId,
          pDepartment: pDepartment
          //name: Name,
        }
      })
      .then(result => {
        localStorage.removeItem('oldIndex');
        const spanAttribute = {
          pageUrl: '/ServiceAdvisors',
          origin: '',
          event: 'Menu Load',
          is_from: 'UPDATE_SERVICE_ADVISOR',
          value: new Date() - start
        };
        var arrSelecte = [];
        this.state.rawGridApi.forEachNode(node => {
          arrSelecte.push(node.data);
        });
        this.props.setErrorCount(true);
        this.state.rowData.push(arrSelecte[0]); // Add new Item
        this.getAgGridData();
        this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
        var advStatus =
          statusval == ''
            ? advisorStatus == null
              ? 0
              : advisorStatus
            : statusval;

        var activeAdvisors = this.props.session.activeAdvisors;
        // this.props.dispatch(setAdvisorStatus(statusval));
        if (advStatus == 1) {
          activeAdvisors.push(ServiceAdvisor);
        } else {
          activeAdvisors.unshift(ServiceAdvisor);
        }

        this.props.dispatch(setAdvisorStatus(activeAdvisors));
        this.props.dispatch(setAdvisorNickName(nickName));

        this.setState({ advisorNewStatus: '' });
        //alert('Servive Advisor Details Updated');
        // this.setState({ success: true });
        this.setState({ openSnackbar: true });
        // window.location.reload(false);
        //
      });
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['nickname', 'active'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['nickname', 'active'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFilterChanged = e => {
    //const filterValues = e.api.getFilterModel();
    this.gridApi.redrawRows();
  };
  insertServiceAdvisor = (Id, Name, ServiceAdvisor) => {
    const client = makeApolloClient;
    const start = new Date();
    client
      .mutate({
        mutation: INSERT_SERVICE_ADVISOR,
        variables: {
          name: Name,
          serviceadvisor: ServiceAdvisor
        }
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/ServiceAdvisors',
          origin: '',
          event: 'Menu Load',
          is_from: 'INSERT_SERVICE_ADVISOR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        var arrSelecte = [];
        this.state.rawGridApi.forEachNode(node => {
          arrSelecte.push(node.data);
        });
        this.state.rowData.push(arrSelecte[0]); // Add new Item
        this.getAgGridData();

        this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid

        alert('Row Added');
      });
  };
  deleteServiceAdvisor = Id => {
    const client = makeApolloClient;
    const start = new Date();
    client
      .mutate({
        mutation: DELETE_SERVICE_ADVISOR,
        variables: {
          id: Id
        }
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/ServiceAdvisors',
          origin: '',
          event: 'Menu Load',
          is_from: 'DELETE_SERVICE_ADVISOR',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        var selected = this.state.rawGridApi.getFocusedCell();
        this.state.rowData.splice(selected.rowIndex, 1);
        this.state.rawGridApi.setRowData(this.state.rowData);
        if (window.confirm('Are you sure you want to delete?')) {
          alert('Row deleted');
        }
      });
  };
  onBtStartEditing = (index, key, char, pinned) => {
    // columnController
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = true;
    this.state.rawGridApi.setColumnDefs(groupColumn);
    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    this.state.rawGridApi.setFocusedCell(index, 'nickname', 'active', pinned);
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'nickname',
      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  // onBtStartEditing = (index, key, char, pinned) => {
  //   // columnController

  //   this.state.rawGridApi.columnController.columnDefs[1].editable = true;

  //   this.state.rawGridApi.setFocusedCell(index, 'active', pinned);
  // this.state.rawGridApi.startEditingCell({
  //   rowIndex: index,
  //   colKey: 'name',
  //   // colKey: 'serviceadvisor',
  //   rowPinned: pinned,
  //   keyPress: key,
  //   charPress: char
  // });
  // };
  onGridReady = params => {
    localStorage.removeItem('oldIndex');
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridColumnApi: params.columnApi });
    this.gridApi.sizeColumnsToFit();

    if (
      this.props.keycloak &&
      this.props.keycloak.authenticated &&
      this.props.keycloak.token
    ) {
      this.getAgGridData();
    }

    // if(this.props.keycloak && this.props.keycloak.realmAccess.roles.includes('admin') == false){

    // this.state.rawGridApi.columnController.columnDefs[3].editable = false;
    // }
  };
  // getRowStyle = params => {
  //   if (
  //     params.data.categorized == 0 ||
  //     (params.data.categorized == 1 && params.data.active == 0)
  //   ) {
  //     return { background: 'rgb(221, 234, 244)' };
  //   }
  // };
  getRowStyle = params => {
    console.log('editedRowId==', !this.state.editedRowId);
    if (
      params.data.categorized == 0 ||
      (params.data.categorized == 1 && params.data.active == 0)
    ) {
      return { background: 'rgb(221, 234, 244)' };
    } else {
      return { background: 'rgb(255, 255, 255)' };
    }
  };
  getAgGridData() {
    const filterModel = this.gridApi.getFilterModel(); // Store the filter state
    this.setState({ isLoading: true });
    getAllSADetails(result => {
      this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
        var roData = lodash.orderBy(
          result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes,
          ['active', 'categorized'],
          ['desc', 'asc']
        );
        this.setState({
          rowData: roData
        });
        this.gridApi.setFilterModel(filterModel);
        this.gridApi.onFilterChanged();
      }
    });
  }
  addNewRow() {
    this.state.rawGridApi.updateRowData({
      addIndex: 0,
      add: [{ id: '', name: '', serviceadvisor: '', nickname: '', active: 0 }]
    });

    this.state.rawGridApi.setFocusedCell(0, 'name', '');
  }
  handleOk = () => {
    this.setState({ success: false });
  };
  resetReportGrid = () => {
    this.setState({
      editedRowId: null
    });
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
    this.gridApi.sizeColumnsToFit();
  };
  onSortChanged = e => {
    this.gridApi.redrawRows();
  };
  onCellClicked = params => {
    let index = localStorage.getItem('index');
    const id = localStorage.getItem('serviceId');

    let rowId = this.state.editedRowId;
    console.log('idd', id, rowId, params.data.id);
    if (params.data.id != id) {
      $(`.edit-button-adv`).show();
      $(`.update-button-adv`).hide();
      $(`.cancel-button-adv`).hide();
      $(`#btncanceladvisor` + rowId).click();
      this.setState({ cancel: true });
      this.onBtStopEditing(index);
      let status = $('#advisorStatus' + index).prop('checked');
      this.setState({ editedRowId: null });
      let data = $('#advisorStatus' + index).val() == 1 ? true : false;
      let advisorStatus = status == data ? status : !status;
    } else {
      $(`#btncanceladvisor${rowId}`).show();
      $(`#btnupdateadvisor${rowId}`).show();
      $(`#btneditadvisor${rowId}`).hide();
    }
  };
  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
    this.setState({ nickError: false });
  };

  onCellValueChanged = params => {
    console.log('params=', params.newValue != params.oldValue, params);
    if (this.state.cancel == false) {
      if (params.newValue != params.oldValue) {
        this.setState({ isEdited: true });

        var selectedId = params.data.id;
        console.log('selectedId=', selectedId);
        var selectedName = params.data.name;
        var selectedAdvisor = params.data.serviceadvisor;
        var nickName = params.data.nickname;
        nickName = nickName ? nickName.trim() : '';
        var advisorStatus = params.data.active;
        var selectedDepartment = params.data.department;
        if (selectedId != '') {
          this.updateServiceAdvisor(
            selectedId,
            selectedName,
            selectedAdvisor,
            nickName ? nickName.trim() : '',
            advisorStatus,
            selectedDepartment
          );
        } else {
          this.insertServiceAdvisor(selectedId, selectedName, selectedAdvisor);
        }
      }
    }
  };
  handleclick = () => {
    if (this.props.history.location.state.tabSelection) {
      this.props.history.push({
        pathname: this.props.history.location.state.isFrom,
        state: {
          selectedSubTab: this.props.history.location.state.selectedSubTab
            ? this.props.history.location.state.selectedSubTab
            : '',
          tabSelection: this.props.history.location.state.tabSelection
        }
      });
    } else {
      this.props.history.goBack();
    }
  };
  render() {
    const { classes } = this.props;
    return (
      <>
        <div>
          <Paper
            square
            style={{
              margin: 8,

              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
              cursor: 'default'
            }}
          >
            <Tabs
              value={this.state.tabSelection}
              onChange={this.handleTabChange}
              variant="fullWidth"
              indicatorColor="secondary"
              textColor="secondary"
              aria-label="icon label tabs example"
            >
              {this.props.history &&
                this.props.history.location &&
                this.props.history.location.state &&
                (this.props.history.location.state.isFrom ||
                  (this.props.history.location.state.parent &&
                    this.props.history.location.state.parent ==
                      '/TrendReport')) && (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={this.handleclick}
                    style={{ width: 'auto', top: '9px' }}
                    fullWidth={false}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                )}
              <Tab
                // style={{
                //   pointerEvents: 'none',
                //   textTransform: 'none',
                //   backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                //   border:
                //     Dealer === 'Armatus'
                //       ? '1px solid #003d6b'
                //       : '1px solid #C2185B',
                //   color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                // }}
                style={{
                  textTransform: 'none',
                  pointerEvents: 'none',
                  borderColor: '#e7eef3',
                  color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
                }}
                label={<div>Service Advisor Details</div>}
                value="one"
              />
            </Tabs>
          </Paper>
          <Paper>
            {' '}
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Paper>
          {this.state.isLoading == true ? (
            <div>
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            </div>
          ) : null}
          {/* <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.success}
        >
          <DialogContent dividers>
            <Typography
              variant="h6"
              style={{
                textTransform: 'none'
              }}
            >
              Service Advisor details updated!
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleOk} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog> */}
          <div
            id="data-tab-advisor"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 170 + 'px',
              width: '791px',
              alignContent: 'center',
              marginLeft: '8px',
              // margin: 8,
              display: this.state.tabSelection == 'two' ? 'none' : 'block'
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              frameworkComponents={this.state.frameworkComponents}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              getRowStyle={this.getRowStyle}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader={true}
              onRowEditingStarted={this.onRowEditingStarted}
              onRowEditingStopped={this.onRowEditingStopped}
              onFilterChanged={this.onFilterChanged}
              onSortChanged={this.onSortChanged}
              // onCellValueChanged={this.onCellValueChanged.bind(this)}
              rowData={this.state.rowData}
              context={this.state.context}
              components={this.state.components}
              suppressClickEdit={true}
              onCellClicked={this.onCellClicked}
              floatingFilter={true}
              enableRangeSelection={true}
              animateRows={true}
              enableCharts={true}
              suppressRowClickSelection={true}
              suppressHorizontalScroll={true}
              editType={this.state.editType}
              tooltipShowDelay={0}
              container="body"
              suppressDragLeaveHidesColumns={true}
              suppressContextMenu={true}
              suppressKeyboardEvent={params => {
                return true;
              }}
            />
          </div>
        </div>
        <Snackbar
          open={this.state.openSnackbar}
          autoHideDuration={6000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 487 }}
        >
          <Alert onClose={this.hidesnackbar} severity="success">
            Service Advisor Details Updated!
          </Alert>
        </Snackbar>
        <Snackbar
          open={this.state.nickError}
          autoHideDuration={6000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 750 }}
        >
          <Alert onClose={this.hidesnackbar} severity="warning">
            Please enter valid Nick Name
          </Alert>
        </Snackbar>
      </>
    );
  }
}
const departmentMappings = {
  'Body Shop': 'Body Shop',
  Service: 'Service'
};
function extractValues(mappings) {
  return Object.keys(mappings);
}
const depart = extractValues(departmentMappings);
function lookupValue(mappings, key) {
  return mappings[key];
}
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: -44
  }
});
function getRenderer(params) {
  function CellRenderer() {}
  CellRenderer.prototype.createGui = function() {
    const template =
      '<span><input type="checkbox" class="editNickName" id="changeNickName" style="cursor: pointer" title="Click to Edit" /><span id="changedNickNameValue" style="padding-left: 4px;"></span></span>';
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = template;
    this.eGui = tempDiv.firstElementChild;
  };
  CellRenderer.prototype.init = function(params) {
    this.createGui();
    this.params = params;
    const eValue = this.eGui.querySelector('#changedNickNameValue');

    eValue.innerHTML = typeof params.value != 'undefined' ? params.value : '';
    this.eButton = this.eGui.querySelector('#changeNickName');
    this.buttonClickListener = this.onButtonClicked.bind(this);
    this.eButton.addEventListener('click', this.buttonClickListener);
  };
  CellRenderer.prototype.onButtonClicked = function() {
    let index = this.params.rowIndex;
    $('#btneditadvisor' + index).hide();
    $('#btncanceladvisor' + index).show();
    $('#btnupdateadvisor' + index).show();
    const startEditingParams = {
      rowIndex: this.params.rowIndex,
      colKey: this.params.column.getId()
    };
    this.params.api.startEditingCell(startEditingParams);
  };
  CellRenderer.prototype.getGui = function() {
    return this.eGui;
  };
  CellRenderer.prototype.destroy = function() {
    this.eButton.removeEventListener('click', this.buttonClickListener);
  };
  return CellRenderer;
}
//render(<ChartMaster></ChartMaster>, document.querySelector('#root'));

export default withStyles(styles)(withKeycloak(Advisor));
