import { makeStyles, withStyles } from '@material-ui/styles';
import React from 'react';
import 'src/styles.css';
import {
  Card,
  CardContent,
  Divider,
  Typography,
  IconButton,
  Tooltip
} from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import getTooltipConentKPI from '../utils/KpiTooltips';
import ReactHtmlParser from 'react-html-parser';
// Functional component definition
const CardKpi = props => {
  const amountFormatter = (kpiName, kpiValue, type, groupIndex, itemIndex) => {
    kpiValue = kpiValue == '' || kpiValue == null ? 0 : kpiValue;

    let result = '';
    if (
      (kpiName.includes('$') ||
        kpiName.includes('Labor') ||
        kpiName.includes('Parts') ||
        kpiName.includes('Sold') ||
        kpiName.includes('Profit')) &&
      type == 0
    ) {
      result =
        '$' +
        parseFloat(kpiValue)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else if (type == 1) {
      result = (Number.isInteger(kpiValue) ? kpiValue : parseFloat(kpiValue))
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else if (type == 2) {
      var itemNameLength =
        kpiName && kpiName.replace(/<\/?[^>]+(>|$)/g, '').split('/').length;
      var itemValueLength = kpiValue && kpiValue.split('/').length;
      if (kpiValue && kpiValue.includes('<b>')) {
        kpiValue = kpiValue.replace(/<\/?b>/g, '');
      }
      if (itemNameLength != itemValueLength) {
        kpiValue =
          kpiValue && kpiValue.includes('/')
            ? kpiValue
                .split('/')
                .slice(0, itemNameLength)
                .join('/')
            : kpiValue;
      }

      if (typeof kpiValue === 'string' && kpiValue.includes('/')) {
        const formattedValues = kpiValue.split('/').map((value, index) => {
          // Append '$' to the second and third segments
          if ((index === 1 || index === 2) && groupIndex == 1) {
            return (
              '$' +
              parseFloat(value)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
            );
          } else if (groupIndex == 3) {
            return `${parseFloat(value)
              .toFixed(2)
              .replace(/\.00$/, '')
              .replace(/(\d)(?=(\d{3})+\.)/g, '$1,')}%`;
          } else if (index === 2 && groupIndex == 0) {
            return `${parseFloat(value)
              .toFixed(2)
              .replace(/\.00$/, '')
              .replace(/(\d)(?=(\d{3})+\.)/g, '$1,')}%`;
          } else {
            return parseFloat(value)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          }
        });
        result = formattedValues.join('/');
      } else {
        result = (Number.isInteger(kpiValue) ? kpiValue : parseFloat(kpiValue))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        if ((itemIndex === 0 || itemIndex === 1) && groupIndex == 2) {
          result = '$' + result;
        }
      }
    } else {
      if (typeof kpiValue === 'string' && kpiValue.includes('/')) {
        const formattedValues = kpiValue.split('/').map((value, index) => {
          // Append '$' to the second and third segments
          if ((itemIndex === 0 || itemIndex === 3) && index === 2) {
            return `${parseFloat(value)
              .toFixed(2)
              .replace(/\.00$/, '')
              .replace(/(\d)(?=(\d{3})+\.)/g, '$1,')}%`;
          } else if (itemIndex == 2 && index === 1) {
            return `${parseFloat(value)
              .toFixed(2)
              .replace(/\.00$/, '')
              .replace(/(\d)(?=(\d{3})+\.)/g, '$1,')}%`;
          } else {
            return parseFloat(value)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          }
        });
        result = formattedValues.join('/');
      } else {
        result = (Number.isInteger(kpiValue)
          ? kpiValue
          : kpiName &&
            (kpiName.includes('Upsell Potential') ||
              kpiName.includes('Sold $/% Collected') ||
              kpiName.includes('Hours Sold Per Completed'))
          ? parseFloat(kpiValue).toFixed(2)
          : parseFloat(kpiValue)
        )
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      }
    }

    return result;
  };

  const formatKpiName = kpiName => {
    const parts = kpiName.split(/<\/?b>/);
    return (
      <span>
        {parts.map((part, index) => {
          // Check if the part is between <b> tags
          if (index % 2 === 1) {
            return (
              <span key={index} style={{ color: '#003d6b' }}>
                {part}{' '}
              </span>
            );
          }
          return <span key={index}>{part}</span>;
        })}
      </span>
    );
  };

  const formatValueSpacing = (
    kpiValue,
    unformattedVal,
    kpiName,
    groupIndex
  ) => {
    const parts = kpiValue.split('/');
    return (
      <span className="spaced-text">
        {parts.map((part, index) => (
          <React.Fragment>
            {unformattedVal &&
            unformattedVal.includes('<b>') &&
            part.includes('%') ? (
              <span
                key={index}
                style={{ color: '#003d6b' }}
                className="boldValues"
              >
                {part}
              </span>
            ) : (
              <span
                key={index}
                className={
                  kpiName && kpiName.includes('Misses')
                    ? // ? 'spaced-text-val-misses'
                      'spaced-text-val0'
                    : kpiName && kpiName.includes('Work Mix')
                    ? 'spaced-text-val-work-mix'
                    : groupIndex == 2
                    ? 'spaced-text-val2'
                    : groupIndex == 3
                    ? 'spaced-text-val3'
                    : 'spaced-text-val'
                }
              >
                {parts.length == index + 1 && groupIndex != 3 ? (
                  <span className="spaced-text-item-values"> {part} </span>
                ) : (
                  part
                )}

                <span className="spaced-text-itemlast"></span>
              </span>
            )}

            {parts.length != index + 1 ? (
              <span className="spaced-text-item">/</span>
            ) : (
              ''
            )}
          </React.Fragment>
        ))}
      </span>
    );
  };

  const getKpiTooltips = (chartId, gridTitle) => {
    return (
      <HtmlTooltip
        title={getTooltipConentKPI(chartId, classes, gridTitle)}
        style={{ height: 10 }}
      >
        <IconButton size="medium" classes="infoIcon">
          <InfoOutlinedIcon />
        </IconButton>
      </HtmlTooltip>
    );
  };
  const HtmlTooltip = withStyles(theme => ({
    arrow: {
      color: theme.palette.common.black
    },
    tooltip: {
      minWidth: 470,
      maxWidth: 600,
      zIndex: '99',
      textAlign: 'left',
      fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
      fontSize: '14px',
      fontWeight: 'normal',
      color: '#003d6b',
      border: '1px solid #003d6b'
    }
  }))(Tooltip);
  const useStyles = makeStyles(theme => ({
    titleLabel: {
      display: 'flex',
      fontSize: 15,
      top: '10px',
      paddingBottom: '10px'
    },
    image: {
      width: 100,
      height: 75
    },
    img: {
      margin: 'auto',
      display: 'block',
      maxWidth: '100%',
      maxHeight: '100%'
    },
    icon: {
      maxWidth: '100%',
      maxHeight: '100%',
      //width: '40%'
      width: '100%'
    }
  }));

  const classes = useStyles();
  const itemsValues = props.kpishowndata;

  return (
    <>
      {props.valueKey != 'E' && (
        <Card class={props.cardClass}>
          <CardContent className="kpi-summary-block">
            <Typography
              gutterBottom
              color="primary"
              variant="subtitle1"
              className={classes.titleLabel}
            >
              <span className={props.valueKey === 'B' ? 'titleMain' : ''}>
                {props.valueKey}) {ReactHtmlParser(itemsValues.kpi_type)}
              </span>
            </Typography>

            {itemsValues.groups.map((group, groupIndex) => (
              <>
                <Grid
                  container
                  className={'kpi-grid-' + props.valueKey + '-' + groupIndex}
                >
                  <Grid item xs={1}>
                    {groupIndex === 0 && group[0].kpi_type_code === 'A' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/lbr-gp-ro.png`}
                        />
                      </div>
                    )}

                    {groupIndex === 1 && group[0].kpi_type_code === 'A' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/prts-gp-ro.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 2 && group[0].kpi_type_code === 'A' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/total-gp-ro.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 3 && group[0].kpi_type_code === 'A' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/ratio.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 0 && group[0].kpi_type_code === 'B' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/compliance-1.png`}
                        />
                      </div>
                    )}

                    {groupIndex === 1 && group[0].kpi_type_code === 'B' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/spare-parts-1.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 2 && group[0].kpi_type_code === 'B' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/compliance-1.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 3 && group[0].kpi_type_code === 'B' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/spare-parts-1.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 0 && group[0].kpi_type_code === 'C' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/c-w-i-total.png`}
                        />
                      </div>
                    )}

                    {groupIndex === 1 && group[0].kpi_type_code === 'C' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/ro-count-share.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 2 && group[0].kpi_type_code === 'C' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/avg-age.png`}
                        />
                      </div>
                    )}
                    {groupIndex === 0 && group[0].kpi_type_code === 'D' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/opportunity-1.png`}
                        />
                      </div>
                    )}

                    {groupIndex === 0 && group[0].kpi_type_code === 'G' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/opportunity-1.png`}
                        />
                      </div>
                    )}
                  </Grid>
                  <Grid
                    item
                    xs={11}
                    className={`${
                      groupIndex === 1 && group[0].kpi_type_code === 'B'
                        ? 'additionalClassMiddleTop'
                        : ''
                    }`}
                  >
                    {group.map((item, itemIndex) => {
                      const itemNameLength =
                        item.kpi_name && item.kpi_name.split('/').length;
                      const itemValueLength =
                        item.kpi_value && item.kpi_value.split('/').length;

                      if (
                        group[0].kpi_type_code === 'A' &&
                        groupIndex === 2 &&
                        itemIndex === 0
                      ) {
                        item.kpi_value =
                          item.kpi_value && item.kpi_value.split('/')[0];
                      }

                      return (
                        <Grid item xs={12} className={`main-cnt-custom-padd`}>
                          <div className={'mainSecondaryDiv'}>
                            <Grid
                              container
                              xs={12}
                              className={'scorecard-items'}
                            >
                              <Grid item sm={6} md={6} lg={7}>
                                <div className={'mainSecondarySubDiv'}>
                                  <Typography>
                                    <span
                                      className={`stylishSpan ${
                                        props.valueKey === 'B'
                                          ? 'valSubHeadMiddle'
                                          : 'valSubHead'
                                      }`}
                                    >
                                      {item.kpi_name.includes('<b>')
                                        ? formatKpiName(item.kpi_name)
                                        : ReactHtmlParser(item.kpi_name)}{' '}
                                      :
                                    </span>
                                  </Typography>
                                </div>
                              </Grid>

                              <Grid
                                item
                                xs={5}
                                md={5}
                                lg={4}
                                style={{ textAlign: 'right' }}
                              >
                                <div
                                  className={`mainSecondarySubDivRight ${
                                    group[0].kpi_type_code === 'B' &&
                                    groupIndex === 3
                                      ? 'maintRepair'
                                      : ''
                                  }`}
                                >
                                  <Typography>
                                    <span
                                      className={`stylishSpanValues ${
                                        props.valueKey === 'B'
                                          ? 'valSubHeadMiddle'
                                          : 'valSubHead'
                                      }`}
                                    >
                                      {(props.valueKey === 'A' ||
                                        props.valueKey === 'D' ||
                                        props.valueKey === 'G') &&
                                      item.kpi_value &&
                                      item.kpi_value.includes('/') ? (
                                        <span>
                                          {item.kpi_value.split('/').map(
                                            (value, i) =>
                                              (itemNameLength === 1
                                                ? i <= itemNameLength
                                                : itemNameLength > 1
                                                ? i < itemNameLength
                                                : itemNameLength === 0) && (
                                                <span key={i}>
                                                  {i > 0
                                                    ? '\u00A0\u00A0\u00A0\u00A0\u00A0'
                                                    : ''}
                                                  {i > 0 &&
                                                  item.kpi_name ===
                                                    'Parts to Labor Ratio'
                                                    ? '$'
                                                    : ''}
                                                  {amountFormatter(
                                                    item.kpi_name,
                                                    value,
                                                    i,
                                                    '',
                                                    ''
                                                  )}
                                                  {item.kpi_name ===
                                                    'Parts to Labor Ratio' &&
                                                  i === 0
                                                    ? '\u00A0\u00A0\u00A0to'
                                                    : ''}
                                                  {i > 0 &&
                                                  item.kpi_name !==
                                                    'Parts to Labor Ratio'
                                                    ? '%'
                                                    : ''}
                                                </span>
                                              )
                                          )}
                                        </span>
                                      ) : props.valueKey === 'C' ? (
                                        <span>
                                          {amountFormatter(
                                            item.kpi_name,
                                            item.kpi_value,
                                            1,
                                            '',
                                            ''
                                          )}
                                          {item.kpi_name.includes('%')
                                            ? '%'
                                            : ''}
                                        </span>
                                      ) : props.valueKey === 'B' ? (
                                        <span>
                                          {item.kpi_value &&
                                          item.kpi_value.includes('<b>')
                                            ? formatValueSpacing(
                                                amountFormatter(
                                                  item.kpi_name,
                                                  item.kpi_value,
                                                  2,
                                                  groupIndex,
                                                  itemIndex
                                                ),
                                                item.kpi_value,
                                                item.kpi_name,
                                                groupIndex
                                              )
                                            : formatValueSpacing(
                                                amountFormatter(
                                                  item.kpi_name,
                                                  item.kpi_value,
                                                  2,
                                                  groupIndex,
                                                  itemIndex
                                                ),
                                                item.kpi_value,
                                                item.kpi_name,
                                                groupIndex
                                              )}
                                        </span>
                                      ) : (
                                        // Default case
                                        amountFormatter(
                                          item.kpi_name,
                                          item.kpi_value,
                                          0,
                                          '',
                                          ''
                                        )
                                      )}
                                      &nbsp;&nbsp;
                                      {itemIndex === 0 && (
                                        <span className={`kpiHeader kpiIcons`}>
                                          <span
                                            className={classes.infoContainer}
                                          >
                                            {getKpiTooltips(1342)}
                                          </span>
                                        </span>
                                      )}
                                    </span>
                                  </Typography>
                                </div>
                              </Grid>
                            </Grid>
                          </div>
                        </Grid>
                      );
                    })}
                  </Grid>
                </Grid>
                {groupIndex < itemsValues.groups.length - 1 && (
                  <Divider
                    className={`${
                      props.valueKey === 'B' || props.valueKey === 'E'
                        ? 'dividerKPIMiddle'
                        : 'dividerKPI'
                    }`}
                  />
                )}
              </>
            ))}
          </CardContent>
        </Card>
      )}
      {props.valueKey == 'E' && (
        <Card class={props.cardClass}>
          <CardContent className="kpi-summary-block">
            {itemsValues.map((group, groupIndex) => (
              <>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="subtitle1"
                  className={classes.titleLabel}
                >
                  <span className={'titleMain'}>
                    {group[groupIndex].kpi_type_code}){' '}
                    {group[groupIndex].kpi_type}
                  </span>
                </Typography>

                <Grid
                  container
                  className={'kpi-grid-' + props.valueKey + '-' + groupIndex}
                >
                  <Grid item xs={1}>
                    {group[groupIndex].kpi_type_code === 'E' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/opportunity-1-E.png`}
                        />
                      </div>
                    )}
                    {group[groupIndex].kpi_type_code === 'F' && (
                      <div className={'imageDiv'}>
                        <img
                          className={classes.icon}
                          alt="Fixed Ops"
                          src={`/images/kpis/opportunity-1-E.png`}
                        />
                      </div>
                    )}
                  </Grid>
                  <Grid container xs={11}>
                    {group.map((item, itemIndex) => (
                      <Grid item xs={12} className={'main-cnt-custom-padd'}>
                        <div className={'mainSecondaryDiv'}>
                          <Grid container>
                            {/* <Grid item xs={8}> */}

                            <>
                              <Grid item xs={8}>
                                <div className={'mainSecondarySubDiv'}>
                                  <Typography>
                                    <>
                                      <span
                                        className={`valSubHeadMiddle stylishSpan`}
                                      >
                                        {ReactHtmlParser(item.kpi_name)}{' '}
                                        :&nbsp;&nbsp;&nbsp;&nbsp;
                                      </span>
                                    </>
                                    {/* ))} */}
                                  </Typography>
                                </div>
                              </Grid>

                              <Grid item xs={4}>
                                <div className={'mainSecondarySubDivRight'}>
                                  <Typography>
                                    {/* {group.map((item, itemIndex) => ( */}
                                    <>
                                      <span
                                        className={`valSubHeadMiddle stylishSpanValues`}
                                      >
                                        {amountFormatter(
                                          item.kpi_name,
                                          item.kpi_value,
                                          3,
                                          groupIndex,
                                          itemIndex
                                        )}
                                        &nbsp;&nbsp;
                                        {itemIndex == 0 && (
                                          <span
                                            className={`kpiHeader kpiIcons`}
                                          >
                                            <span
                                              className={classes.infoContainer}
                                            >
                                              {getKpiTooltips(1342)}
                                            </span>
                                          </span>
                                        )}
                                      </span>
                                    </>
                                  </Typography>
                                </div>
                              </Grid>
                            </>
                          </Grid>
                        </div>
                      </Grid>
                    ))}
                  </Grid>
                </Grid>

                {groupIndex < itemsValues.length - 1 && (
                  <Divider className={'dividerKPIMiddle'} />
                )}
              </>
            ))}
          </CardContent>
        </Card>
      )}
    </>
  );
};

export default CardKpi;
