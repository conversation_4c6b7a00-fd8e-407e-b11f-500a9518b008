import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import $ from 'jquery';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { useHistory } from 'react-router';

import { getTimeZone } from '../../utils/Utils';
import Header from './Header';
import Page from 'src/components/Page';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function Homenew() {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const history = useHistory();
  const [isLoading, setLoading] = useState(false);
  const [isLoadingParts, setLoadingParts] = useState(false);
  const dispatch = useDispatch();

  $('#navBarDiv .active-menu').removeClass('active-menu');
  $('#Homenew').addClass('active-menu');
  var timezone = getTimeZone();

  useEffect(() => {
    console.log('store-----changes---', session.storeSelected);
    // getPartsPayTypeList();
    let data = [];
    data.push('Customer');
    setLoading(false);
  }, [session.storeSelected]);

  return (
    <Page className={classes.root} title="Home">
      {localStorage.getItem('versionFlag') == 'FALSE' ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Header history={history} timezone={timezone} session={session} />
      )}
    </Page>
  );
}

export default Homenew;
