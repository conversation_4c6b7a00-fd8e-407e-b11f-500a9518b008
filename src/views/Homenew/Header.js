import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import {
  MenuItem,
  Select,
  Paper,
  FormControl,
  Divider,
  Tooltip,
  IconButton,
  Button
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import ExportIcon from '@material-ui/icons/GetApp';
import moment from 'moment';
import {
  getDataForCWITotalcharts,
  getDataForKPIROSharechart,
  getDataForKPILineROLtSixtyK,
  getDataForKPILineROGtSixtyK,
  getDataForKPIAvgAgeMiles,
  getDataForKPIFlatRateHrs,
  getDataForKPILaborGpRo,
  getDataForKPIPartsGpRo,
  getDataForKPITotalGpRo,
  getDataForKPIWorkmix,
  getDataForKPILaborGrid,
  getDataForKPILineRO,
  getDataForKPIPartsGrid,
  getLatestClosedDate,
  getDrillDownMonthYears,
  getKpiToggleOptionsWithTimeZone
} from 'src/utils/hasuraServices';
import { useSelector, useDispatch } from 'react-redux';
import clsx from 'clsx';
import KpiSummary from './KpiSummary';
import { CircularProgress, Grid } from '@material-ui/core';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { ReactSession } from 'react-client-session';

import {
  setKpiToggle,
  setInternalKpiToggle,
  setKpiHomeToggle,
  setKpiToggleStartDate,
  setKpiToggleEndDate
} from 'src/actions';
import { getNextMonth, getYearValue, getTimeZone } from 'src/utils/Utils';

import { withKeycloak } from '@react-keycloak/web';

import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import InsertChartOutlined from '@material-ui/icons/InsertChartOutlined';

import { useLocation } from 'react-router-dom';

var lodash = require('lodash');

const styles = theme => ({
  root: {
    flexGrow: 1,
    width: '99%',
    marginLeft: 5
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary,
    padding: '12px !important'
  },
  paper1: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.primary.main,
    margin: '12px 6px',
    maxWidth: '99%'
  },
  card: {
    width: '100%',
    height: '50vh',
    maxWidth: '100%',
    overflow: 'visible',
    display: 'flex',
    position: 'relative',
    // backgroundColor: '#c0d1df',
    backgroundColor: theme.palette.primary.light,
    border: 'solid #003d6b',
    marginTop: '-3px',
    '& > *': {
      flexGrow: 1,
      flexBasis: '50%',
      width: '50%'
    }
  },
  content: {
    padding: theme.spacing(8, 4, 3, 4)
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: -12
  },
  h1: {
    color: '#000',
    position: 'absolute',
    textAlign: 'center',
    left: '28%',
    fontWeight: 'bold',
    marginTop: '45px'
  },
  p: {
    color: '#fff'
  },
  headerDropdown: {
    // marginTop: -6
    marginTop: 3
  },
  divider: {
    backgroundColor: '#9b8c8ca1',
    fontWeight: 'bold'
  },
  container: {
    gridGap: 10
  },
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  },
  dataAsOfLabel: {
    marginLeft: 'auto',
    // marginTop: 10,
    // marginTop: 19,
    marginTop: 6,
    marginRight: 10
  },
  linkItem: {
    cursor: 'pointer',
    marginTop: 10
  },
  reportButton: {
    height: 24,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 136
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 122
    }
  },
  reportButtonSelect: {
    height: 24,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 136
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 122
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  ButtonSelect: {
    lineHeight: 1.5,
    '@media (min-width: 2560px)': {
      marginTop: '11px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '11px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 1920px)': {
      width: 183,
      marginTop: '11px !important',
      marginBottom: '3px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '11px !important',
      width: 154,
      marginBottom: '2x !important'
    }
  }
});
function Header(props) {
  const session = useSelector(state => state.session);
  // let laborGridTypes = JSON.parse(localStorage.getItem('laborGridTypes'));
  let laborGridTypes = session.allLaborGridTypes;
  let partsMatrixTypes = session.allPartsMatrixTypes;
  let kpiDataToggle =
    localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';
  // let partsMatrixTypes = JSON.parse(localStorage.getItem('partsMatrixTypes'));
  let toggle =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.toggleOptions
      ? props.history.location.state.toggleOptions
      : props.history &&
        props.history.location &&
        (props.history.location.state == undefined ||
          props.history.location.state == null)
      ? kpiDataToggle
      : props.selectedToggle
      ? props.selectedToggle
      : kpiDataToggle;

  let payType =
    props.history && props.history.location && props.history.location.state
      ? props.history.location.state.payType
      : // : payTypeListParts && payTypeListParts.length > 0
        // ? payTypeListParts[0].charAt(0)
        'C';
  let intToggle = false;
  if (payType == undefined) {
    payType = 'C';
  }
  if (payType == 'C') {
    intToggle = false;
  } else {
    intToggle = true;
  }
  let laborGrid = laborGridTypes;
  let gridTypeText =
    props.history && props.history.location && props.history.location.state
      ? props.history.location.state.gridType
      : laborGrid && laborGrid.length > 0
      ? laborGrid[0]
      : '';
  if (gridTypeText && laborGrid.length > 0) {
    if (laborGrid.includes(gridTypeText)) {
      gridTypeText = gridTypeText;
    } else {
      gridTypeText = laborGrid[0];
    }
  }
  if (gridTypeText == undefined) {
    gridTypeText = laborGrid && laborGrid.length > 0 ? laborGrid[0] : 'C';
  }
  let extendedStatus = props.parent == 'Home' ? false : true;
  let closedDateValue = localStorage.getItem('closedDate')
    ? localStorage.getItem('closedDate')
    : props.closedDate;
  if (
    localStorage.getItem('storeChange') == 'true' ||
    localStorage.getItem('storeChange') == true
  ) {
    session.kpiToggleStartDate = '';
    session.kpiToggleEndDate = '';
    if (
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.filterStart
    ) {
      props.history.location.state.filterStart = '';
    }
    if (
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.filterEnd
    ) {
      props.history.location.state.filterEnd = '';
    }
    localStorage.removeItem('storeChange');
  }
  let startDate =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.filterStart
      ? props.history.location.state.filterStart
      : session.kpiToggleStartDate != '' &&
        props.history.location.state != undefined
      ? session.kpiToggleStartDate
      : localStorage.getItem('kpiStartDate')
      ? localStorage.getItem('kpiStartDate')
      : '';

  let endDate =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.filterEnd
      ? props.history.location.state.filterEnd
      : session.kpiToggleEndDate != '' &&
        props.history.location.state != undefined
      ? session.kpiToggleEndDate
      : localStorage.getItem('kpiFilterEndDate')
      ? localStorage.getItem('kpiFilterEndDate')
      : '';

  const { classes } = props;
  const [toggleOption, setToggleOption] = useState(toggle);
  var timeZone = props.timezone ? props.timezone : '+05:30';
  const [CWITData, setCWITData] = useState([]);
  const [CWITAllData, setCWITAllDataAll] = useState([]);
  const [ROShareData, setROShareData] = useState([]);
  const [LineROLtSixtyK, setLineROLtSixtyK] = useState([]);
  const [LineROGtSixtyK, setLineROGtSixtyK] = useState([]);
  const [AvgAgeMiles, setAvgAgeMiles] = useState([]);
  const [FlatRateHrs, setFlatRateHrs] = useState([]);
  const [LaborGpRo, setLaborGpRo] = useState([]);
  const [PartsGpRo, setPartsGpRo] = useState([]);
  const [TotalGpRo, setTotalGpRo] = useState([]);
  const [WorkMix, setWorkMix] = useState([]);
  const [LaborGrid, setLaborGrid] = useState([]);
  const [PartsGrid, setPartsGrid] = useState([]);
  const [LineRO, setLineRO] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [parent, setParent] = useState(props.parent);
  const [isLoadingComplete, setLoadingComplete] = useState(false);
  // const session = useSelector(state => state.session);
  const [internalToggle, setInternalToggle] = useState(intToggle);
  const [internalMisses, setInternalMisses] = useState(intToggle);
  const [gridType, setGridType] = useState(gridTypeText);
  const [payTypeList, setPayTypeList] = useState(laborGridTypes);
  const [payTypeListParts, setPayTypeListParts] = useState(partsMatrixTypes);
  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  const [closedDate, setClosedDate] = useState(closedDateValue);
  const [expandedView, setExpandedView] = useState(extendedStatus);

  const [dates, setDates] = useState([]);
  const [thisWeek, setThisWeek] = useState('');
  const [lastWeek, setLastWeek] = useState('');
  const [lastTwoWeek, setLastTwoWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [today, setToday] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');
  const [filterStart, setFilterStart] = useState(startDate);
  const [filterEnd, setFilterEnd] = useState(endDate);
  const [filterChanged, setFilterChanged] = useState(true);

  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const dispatch = useDispatch();
  const [isPageLoading, setIsPageLoading] = useState(false);
  // localStorage.setItem('laborGridTypes',JSON.stringify(props.payTypeList));
  // localStorage.setItem('partsMatrixTypes',JSON.stringify(props.payTypeListParts));
  const HtmlTooltip = withStyles(theme => ({
    arrow: {
      color: theme.palette.common.black
    },
    tooltip: {
      minWidth: 100,
      maxWidth: 150,
      zIndex: '99',
      textAlign: 'left',
      fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
      fontSize: '14px',
      fontWeight: 'normal',
      color: '#003d6b',
      border: '1px solid #003d6b'
    }
  }))(Tooltip);
  useEffect(() => {
    let closedDateValue = localStorage.getItem('closedDate')
      ? localStorage.getItem('closedDate')
      : props.closedDate;
    setClosedDate(closedDateValue);
  }, [props.closedDate]);
  useEffect(() => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);
        setThisWeek(
          moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY")
        );
        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setLastTwoWeek(
          moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        if (
          (filterStart == '' && filterEnd == '') ||
          filterStart == '' ||
          filterEnd == ''
        ) {
          if (localStorage.getItem('kpiDataStatus') == 1) {
            setFilterStart(
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'));
            setToggleOption('MTD');
            localStorage.setItem(
              'kpiStartDate',
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            localStorage.setItem(
              'kpiFilterEndDate',
              moment(dataArr[0].mtdenddate).format('YYYY-MM-DD')
            );
          } else {
            setFilterStart(
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
            setToggleOption('LMONTH');
            localStorage.setItem(
              'kpiStartDate',
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            localStorage.setItem(
              'kpiFilterEndDate',
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
          }
        }
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
        setIsPageLoading(true);
      }
    });
    // getDrillDownMonthYears(result => {
    //   if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
    //     var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

    //     setMonths(monthArr);

    //     var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
    //       'YYYY-MM-DD'
    //     );
    //     var endDate = moment(new Date())
    //       .clone()
    //       .format('YYYY-MM-DD');
    //     setSelectedDates([endDate, endDate]);
    //   }
    // });
    dispatch(setKpiToggleStartDate(filterStart));
    dispatch(setKpiToggleEndDate(filterEnd));
    ReactSession.set('kpiToggleStartDate', filterStart);
    ReactSession.set('kpiToggleEndDate', filterEnd);
    localStorage.setItem('kpiStartDate', filterStart);
    localStorage.setItem('kpiFilterEndDate', filterEnd);
    if (
      props.history &&
      props.history.location &&
      props.history.location.state
    ) {
      ReactSession.set('kpiHomeToggle', ReactSession.get('kpiHomeToggle'));
      dispatch(setKpiHomeToggle(ReactSession.get('kpiHomeToggle')));
    } else {
      ReactSession.set('kpiHomeToggle', session.kpiHomeToggle);
      dispatch(setKpiHomeToggle(session.kpiHomeToggle));
    }
  }, []);

  const handleChange = event => {
    if (event.target.value) {
      setToggleOption(event.target.value);
      dispatch(setKpiHomeToggle(event.target.value));
      if (
        !session.serviceAdvisor.includes('All') &&
        session.serviceAdvisor.length == 1
      ) {
        dispatch(setKpiToggle(event.target.value));
      } else {
        //  dispatch(setKpiToggle('MTD'));
        dispatch(setKpiToggle(kpiDataToggle));
      }
    }
  };

  const handleInternalMisses = value => {
    setInternalToggle(value);
    setInternalMisses(value);
  };

  const handleGridType = value => {
    setGridType(value);
  };

  useEffect(() => {
    setFilterChanged(true);
  }, [session.serviceAdvisor, session.technician]);

  const checkForSunday = () => {
    var date = new Date(props.toggleOptions[0].yesterday);
    if (date.getDay() == 0) {
      return true;
    } else {
      return false;
    }
  };

  //   let currentDate = moment(props.toggleOptions[0].today).format('MMM-DD-YY');
  //   let yesterDay = moment(props.toggleOptions[0].yesterday).format(
  //     "MMM DD ' YY"
  //   );
  //   let lastWeek =
  //     moment(props.toggleOptions[0].lastweekstartdate).format('MMM DD') +
  //     ' to ' +
  //     moment(props.toggleOptions[0].lastweekenddate).format("MMM DD ' YY");
  //   // let mtd =
  //   //   moment(props.toggleOptions[0].mtdstartdate).format('MMM-DD-YY') +
  //   //   ' to ' +
  //   //   moment(props.toggleOptions[0].mtdenddate).format('MMM-DD-YY');
  //   let mtd =
  //     moment(props.toggleOptions[0].mtdstartdate).format('MMM DD') +
  //     ' to ' +
  //     moment(props.toggleOptions[0].mtdenddate).format("MMM DD ' YY");
  //   // let lastMonth =
  //   //   moment(props.toggleOptions[0].lastmonthstartdate).format('MMM-DD-YY') +
  //   //   ' to ' +
  //   //   moment(props.toggleOptions[0].lastmonthenddate).format('MMM-DD-YY');
  //   let lastMonth = moment(props.toggleOptions[0].lastmonthstartdate).format(
  //     'MMM'
  //   );
  //   let ytd =
  //     moment(props.toggleOptions[0].ytdstartdate).format('MMM DD') +
  //     ' to ' +
  //     moment(props.toggleOptions[0].ytdenddate).format("MMM DD ' YY");
  //   // let lastQtr =
  //   //   moment(props.toggleOptions[0].lastquarterstartdate).format('MMM-DD-YY') +
  //   //   ' to ' +
  //   //   moment(props.toggleOptions[0].lastquarterenddate).format('MMM-DD-YY');
  //   let lastQtr =
  //     moment(props.toggleOptions[0].lastquarterstartdate).format('MMM') +
  //     ' to ' +
  //     //getNextMonth(props.toggleOptions[0].lastquarterstartdate) + ' - ' +
  //     moment(props.toggleOptions[0].lastquarterenddate).format('MMM');
  //   // let lastYear =
  //   //   moment(props.toggleOptions[0].lastyearstartdate).format('MMM-DD-YY') +
  //   //   ' to ' +
  //   //   moment(props.toggleOptions[0].lastyearenddate).format('MMM-DD-YY');
  //   let lastYear =
  //     moment(props.toggleOptions[0].lastyearstartdate).format('MMM') +
  //     ' to ' +
  //     moment(props.toggleOptions[0].lastyearenddate).format('MMM') +
  //     " ' " +
  //     getYearValue(props.toggleOptions[0].lastyearenddate);
  //   let dayBeforeYesterday = moment(
  //     props.toggleOptions[0].dayBeforeYesterday
  //   ).format("MMM DD ' YY");
  //   // let lastThreeMonths =
  //   //   moment(props.toggleOptions[0].lastthreemonthstartdate).format('MMM-DD-YY') +
  //   //   ' to ' +
  //   //   moment(props.toggleOptions[0].lastthreemonthenddate).format('MMM-DD-YY');

  //   let lastThreeMonths =
  //     moment(props.toggleOptions[0].lastthreemonthstartdate).format('MMM') +
  //     ' to ' +
  //     // getNextMonth(props.toggleOptions[0].lastthreemonthstartdate) + ' - ' +
  //     moment(props.toggleOptions[0].lastthreemonthenddate).format('MMM');
  //   let lastTwelveMonths =
  //     moment(props.toggleOptions[0].lasttwelvemonthstartdate).format("MMM ' YY") +
  //     ' to ' +
  //     moment(props.toggleOptions[0].lasttwelvemonthenddate).format("MMM ' YY");
  // let lastTwelveMonths = moment(props.toggleOptions[0].lasttwelvemonthstartdate).format('MMM') + ' to ' +
  //   moment(props.toggleOptions[0].lasttwelvemonthenddate).format('MMM') + ' - ' +
  //   getYearValue(props.toggleOptions[0].lasttwelvemonthstartdate);

  const handleCallback = (event, picker) => {
    setFilterStart(picker.startDate.format('YYYY-MM-DD'));
    setFilterEnd(picker.endDate.format('YYYY-MM-DD'));
    getFilterText(picker.chosenLabel);
    // if (picker.chosenLabel.includes('Yesterday')) {
    //   setToggleOption('YESDT');
    // } else if (picker.chosenLabel.includes('Day Before Yest.')) {
    //   setToggleOption('DBYESDT');
    // } else if (picker.chosenLabel.includes('Last Week')) {
    //   setToggleOption('LWEEK');
    // } else if (picker.chosenLabel.includes('This Month')) {
    //   setToggleOption('MTD');
    // } else if (picker.chosenLabel.includes('Last Month')) {
    //   setToggleOption('LMONTH');
    // } else if (picker.chosenLabel.includes('Last 3 Mths')) {
    //   setToggleOption('PLMTHREE');
    // } else if (picker.chosenLabel.includes('Last Qtr')) {
    //   setToggleOption('LQRTR');
    // } else if (picker.chosenLabel.includes('YTD')) {
    //   setToggleOption('YTD');
    // } else if (picker.chosenLabel.includes('Last 12 Mths')) {
    //   setToggleOption('PLYONE');
    // } else if (picker.chosenLabel.includes('Last Year')) {
    //   setToggleOption('LYEAR');
    // } else {
    //   setToggleOption('MTD');
    // }

    dispatch(setKpiToggleStartDate(picker.startDate.format('YYYY-MM-DD')));
    dispatch(setKpiToggleEndDate(picker.endDate.format('YYYY-MM-DD')));
    ReactSession.set(
      'kpiToggleStartDate',
      picker.startDate.format('YYYY-MM-DD')
    );
    ReactSession.set('kpiToggleEndDate', picker.endDate.format('YYYY-MM-DD'));
    localStorage.setItem('kpiStartDate', picker.startDate.format('YYYY-MM-DD'));
    localStorage.setItem(
      'kpiFilterEndDate',
      picker.endDate.format('YYYY-MM-DD')
    );
    setFilterChanged(true);
  };
  const getFilterText = label => {
    var filterText;

    if (label.includes('Yesterday')) {
      filterText = 'YESDT';
    } else if (label.includes('Day Before Yest')) {
      filterText = 'DBYESDT';
    } else if (label.includes('Last Week')) {
      filterText = 'LWEEK';
    } else if (label.includes('This Month')) {
      filterText = 'MTD';
    } else if (label.includes('Last Month')) {
      filterText = 'LMONTH';
    } else if (label.includes('Last 3 Mths')) {
      filterText = 'PLMTHREE';
    } else if (label.includes('Last Qtr')) {
      filterText = 'LQRTR';
    } else if (label.includes('YTD')) {
      filterText = 'YTD';
    } else if (label.includes('Last 12 Mths')) {
      filterText = 'PLYONE';
    } else if (label.includes('Last Year')) {
      filterText = 'LYEAR';
    } else if (label.includes('Custom Range')) {
      filterText = 'CRANGE';
    }
    dispatch(setKpiHomeToggle(filterText));
    ReactSession.set('kpiHomeToggle', filterText);
    return filterText;
  };
  const getKpiVisualizations = () => {
    return (
      <Tooltip
        title={<span style={{ fontSize: 11.5 }}>Go to Visualization</span>}
      >
        <IconButton
          size="medium"
          classes="infoIcon"
          onClick={() => gotoVisualizations()}
        >
          <InsertChartOutlined />
        </IconButton>
      </Tooltip>
    );
  };
  const gotoVisualizations = () => {
    props.history.push({
      pathname: '/KpiGraphics',
      state: {
        parent: 'Home',
        filterStart: filterStart,
        filterEnd: filterEnd
      }
    });
  };
  const resetFilterState = val => {
    setFilterChanged(false);
  };
  const location = useLocation();
  const isHomePage = location.pathname === '/Home';

  return (
    <React.Fragment>
      <style>
        {isHomePage &&
          `
          .daterangepicker.opensright:after {
            right: 75px !important;
            left: unset;
          }
          .daterangepicker.opensright:before {
            right: 74px !important;
            left: unset;
          }
        `}
      </style>

      {isPageLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <div className={clsx(classes.headerItem, 'main-title-kpi')}>
              <Typography
                variant="h4"
                color="primary"
                gutterBottom
                className={clsx(classes.mainLabel, 'main-title-kpi')}
              >
                KPI Scorecard
              </Typography>
              {/* <span style={{display: process.env.REACT_APP_PRODUCTION == 'false' ? 'block' : 'none'}}> */}

              <span style={{ display: 'none' }}>
                <div className={classes.ButtonSelect} id="ButtonSelectHome">
                  <Button
                    className={
                      parent == 'Home'
                        ? classes.reportButtonSelect
                        : classes.reportButton
                    }
                    id="standardView"
                    variant="outlined"
                    style={{ textTransform: 'none' }}
                    //  onClick={() => handleExpandedView(false)}
                  >
                    Standard View
                  </Button>
                  <Button
                    className={
                      parent == 'Extended_View'
                        ? classes.reportButtonSelect
                        : classes.reportButton
                    }
                    id="expandedView"
                    variant="outlined"
                    style={{ textTransform: 'none' }}
                    //  onClick={() => handleExpandedView(true)}
                  >
                    Expanded View
                  </Button>
                </div>
              </span>

              <div className={classes.dataAsOfLabel}>
                <span className={classes.visualization1}>
                  {getKpiVisualizations()}
                </span>
                {/* <span>
                <div className={classes.ButtonSelect} id="ButtonSelectHome">
                  <Button
                    className={
                      parent == 'Home'
                        ? classes.reportButtonSelect
                        : classes.reportButton
                    }
                    id="standardView"
                    variant="outlined"
                    style={{
                      textTransform: 'none'
                    }}
                    onClick={() => gotoVisualizations()}
                  >
                    Visualizations
                  </Button>
                </div>
              </span> */}

                {closedDate || closedDateValue ? (
                  <Typography
                    variant="body1"
                    color="secondary"
                    align="right"
                    style={{
                      fontWeight: 'bold'
                    }}
                    className={clsx(classes.dataLabel, 'date-asof')}
                  >
                    {'Data as of: ' +
                      moment(closedDate ? closedDate : closedDateValue).format(
                        'MM/DD/YY'
                      )}
                  </Typography>
                ) : (
                  ''
                )}
              </div>
              <div className={classes.headerDropdown}>
                <FormControl
                  variant="outlined"
                  margin="dense"
                  className={clsx(classes.formControl, 'input-container')}
                >
                  <DateRangePicker
                    initialSettings={{
                      // maxDate: {
                      //   date: new Date(),
                      // },
                      // minDate:{
                      //   date: (selectedDates[1])
                      // },

                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },
                      ranges: {
                        // ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        // today]: [
                        //   moment(closedDate).toDate(),
                        //   moment(closedDate).toDate()
                        // ],
                        ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        yesterDay]: [
                          moment(dates[0] && dates[0].yesterday).toDate(),
                          moment(dates[0] && dates[0].yesterday).toDate()
                        ],
                        ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        dayBfYest]: [
                          moment(
                            dates[0] && dates[0].dayBeforeYesterday
                          ).toDate(),
                          moment(
                            dates[0] && dates[0].dayBeforeYesterday
                          ).toDate()
                        ],
                        ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        thisWeek]: [
                          moment(dates[0].thisweekstartdate).toDate(),
                          moment(dates[0].thisweekenddate).toDate()
                        ],
                        ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastWeek]: [
                          moment(dates[0].lastweekstartdate).toDate(),
                          moment(dates[0].lastweekenddate).toDate()
                        ],
                        ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastTwoWeek]: [
                          moment(dates[0].lasttwoweekstartdate).toDate(),
                          moment(dates[0].lasttwoweekenddate).toDate()
                        ],
                        // 'Last 7 Days': [
                        //   moment()
                        //     .subtract(6, 'days')
                        //     .toDate(),
                        //   moment().toDate()
                        // ],
                        // 'Last 30 Days': [
                        //   moment()
                        //     .subtract(29, 'days')
                        //     .toDate(),
                        //   moment().toDate()
                        // ],
                        ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        mtd]: [
                          moment(dates[0] && dates[0].mtdstartdate).toDate(),
                          moment(dates[0] && dates[0].mtdenddate).toDate()
                        ],
                        ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastMonth]: [
                          moment(
                            dates[0] && dates[0].lastmonthstartdate
                          ).toDate(),
                          moment(dates[0] && dates[0].lastmonthenddate).toDate()
                        ],
                        ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastThreeMonths]: [
                          moment(dates[0].lastthreemonthstartdate).toDate(),
                          moment(dates[0].lastthreemonthenddate).toDate()
                        ],
                        ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastQtr]: [
                          moment(dates[0].lastquarterstartdate).toDate(),
                          moment(dates[0].lastquarterenddate).toDate()
                        ],
                        ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        ytd]: [
                          moment(dates[0].ytdstartdate).toDate(),
                          moment(dates[0].ytdenddate).toDate()
                        ],
                        ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastTwelveMonths]: [
                          moment(dates[0].lasttwelvemonthstartdate).toDate(),
                          moment(dates[0].lasttwelvemonthenddate).toDate()
                        ],
                        ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                        lastYear]: [
                          moment(dates[0].lastyearstartdate).toDate(),
                          moment(dates[0].lastyearenddate).toDate()
                        ]
                      },
                      //  maxDate: moment().toDate(),
                      maxDate: moment(dates[0] && dates[0].today).toDate(),
                      // maxDate: '2023-12-29',
                      alwaysShowCalendars: false,
                      applyClass: clsx(classes.calButton, 'apply-btn'),
                      cancelClass: clsx(classes.calButton, 'apply-btn'),

                      startDate:
                        filterStart && filterStart != ''
                          ? moment(filterStart).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(dates[0].mtdstartdate).toDate()
                          : moment(dates[0].lastmonthstartdate).toDate(),
                      endDate:
                        filterEnd && filterEnd != ''
                          ? moment(filterEnd).toDate()
                          : localStorage.getItem('kpiDataStatus') == 1
                          ? moment(dates[0].mtdenddate).toDate()
                          : moment(dates[0].lastmonthenddate).toDate()
                      //showDropdowns: true
                    }}
                    // onEvent={this.handleEventCallback}
                    onApply={handleCallback}
                  >
                    <input
                      type="text"
                      className="datepicker"
                      id="picker"
                      name="picker"
                      aria-labelledby="label-picker"
                    />
                    {/* <TextField
                      id="outlined-basic"
                      label="Select Date"
                      size="small"
                      //onChange={}
                      value={this.state.value}
                      variant="outlined"
                    /> */}
                  </DateRangePicker>
                  <label class="labelpicker" for="picker" id="label-picker">
                    <div class="textpicker">Select Date</div>
                  </label>
                </FormControl>
              </div>
            </div>
            <Divider className={classes.divider} />
            {/* {parent == 'Home' ?  */}
            <KpiSummary
              toggleOption={toggleOption}
              filterStart={filterStart}
              filterEnd={filterEnd}
              filterChanged={filterChanged}
              resetFilterState={resetFilterState}
              // CWITData={CWITData}
              // allCWITData={CWITAllData}
              // ROShareData={ROShareData}
              // LineROLtSixtyK={LineROLtSixtyK}
              // LineROGtSixtyK={LineROGtSixtyK}
              // FlatRateHrs={FlatRateHrs}
              // AvgAgeMiles={AvgAgeMiles}
              // LaborGpRo={LaborGpRo}
              // PartsGpRo={PartsGpRo}
              // TotalGpRo={TotalGpRo}
              // WorkMix={WorkMix}
              // LaborGrid={LaborGrid}
              // PartsGrid={PartsGrid}
              // handleInternalMisses={handleInternalMisses}
              // internalMisses={internalMisses}
              // payType={payType}
              // internalToggleStatus={props.internalToggleStatus}
              // payTypeList={payTypeList}
              // handleGridType={handleGridType}
              // gridType={gridType}
              // payTypeListParts={payTypeListParts}
            />
          </Paper>
        </div>
      ) : (
        //   {expandedView == false ? (
        //     isLoading == true ? (
        //       <Grid justify="center" className={classes.loaderGrid}>
        //         {/* <CircularProgress size={60} /> */}
        //       </Grid>
        //     ) : isLoading == true ? (
        //       <Grid justify="center" className={classes.loaderGrid}>
        //         Processing...
        //       </Grid>
        //     ) : (
        //       <>
        //         {/* <Dashboard
        //             toggleOption={toggleOption}
        //             LaborGpRo={LaborGpRo}
        //             PartsGpRo={PartsGpRo}
        //             TotalGpRo={TotalGpRo}
        //             FlatRateHrs={FlatRateHrs}
        //             allCWITData={CWITAllData}
        //             ROShareData={ROShareData}
        //             AvgAgeMiles={AvgAgeMiles}
        //             LineROLtSixtyK={LineROLtSixtyK}
        //             LineROGtSixtyK={LineROGtSixtyK}
        //             WorkMix={WorkMix}
        //             LineRO={LineRO}
        //             LaborGrid={LaborGrid}
        //             PartsGrid={PartsGrid}
        //             internalMisses={internalMisses}
        //             parent={parent}
        //           /> */}
        //       </>
        //     )
        //   ) : null}
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </React.Fragment>
  );
}

Header.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withKeycloak(withStyles(styles)(Header));
