import React, { useState, useEffect } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import ReactHtmlParser from 'react-html-parser';
import { useHistory } from 'react-router';
import {
  Card,
  CardContent,
  Divider,
  Typography,
  IconButton,
  Tooltip,
  Grid
} from '@material-ui/core';
import clsx from 'clsx';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import { useDispatch, useSelector } from 'react-redux';
import { formatCellValueKPI, formatCellValueKPInew } from 'src/utils/Utils';
import getTooltipConentKPI from 'src/utils/KpiTooltips';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    minWidth: 370,
    maxWidth: 490,
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '12px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b'
    //padding: '4px 20px'
  }
}))(Tooltip);

const HtmlTooltipNoValue = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    minWidth: 100,
    maxWidth: 490,
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '12px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b'
    // padding: '4px 20px'
  }
}))(Tooltip);

function MiddleGrid(props) {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const history = useHistory();
  const [isLoading, setLoading] = useState(false);
  const [isLoadingParts, setLoadingParts] = useState(false);
  const [toggle, setToggle] = useState(props.toggleOption);

  const itemsValues = props.kpishowndata;
  var data;
  if (itemsValues.kpi_type_code) {
    data = props.kpishowndata.groups;
  } else {
    data = props.kpishowndata;
  }
  const dispatch = useDispatch();
  const gotoLaborGridMisses = kpino => {
    if (kpino == 1) {
      window.sortStateLbrMiss = {};
      window.filterStateLbrMiss = {};
    } else {
      window.sortStatePrtMiss = {};
      window.filterStatePrtMiss = {};
    }
    kpino == 1
      ? history.push({
          pathname: '/LaborMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Home',
            previousToggle: props.toggleOption,
            payType: 'C',
            previousPayType: 'C',
            gridType: 'Customer',
            previousGridType: 'Customer',
            filterStart: props.filterStart,
            filterEnd: props.filterEnd
          }
        })
      : history.push({
          pathname: '/PartsMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Home',
            previousToggle: props.toggleOption,
            payType: 'C',
            previousPayType: 'C',
            gridType: 'Customer',
            previousGridType: 'Customer',
            filterStart: props.filterStart,
            filterEnd: props.filterEnd
          }
        });
  };

  const gotoMPIStatDrilldown = type => {
    history.push({
      pathname: '/MPIStats',
      state: {
        selectedToggle: props.toggleOption,
        parent: 'Home',
        previousToggle: props.toggleOption,
        payType: 'C',
        previousPayType: 'C',
        gridType: 'Customer',
        previousGridType: 'Customer',
        filterStart: props.filterStart,
        filterEnd: props.filterEnd
      }
    });
  };
  const formatKpiValue = (kpiValue, kpino, kpi_slno, type) => {
    const value = kpiValue.split('/');
    if (value.length > 1) {
      if (type == 'B' && kpino == '1') {
        return (
          <Grid xs={12} className="flex-grid-m-block">
            <Grid xs={8}>
              <div class="content-value-block-1 content-value-inner">
                {formatCellValueKPI(value[0], null, 1)}
              </div>
              <div class="slash content-value-inner">/</div>
              <div class="content-value-block-2 content-value-inner">
                {formatCellValueKPI(value[1], null, 1)}
              </div>
            </Grid>
            <Grid
              xs={2}
              className="content-value-block-3 content-value-inner content-value-perc"
            >
              {formatCellValueKPI(value[2], '%', 1)}
            </Grid>

            <Grid xs={2}>
              <span className={'gridDrilldown'}>
                <Tooltip
                  title={<span style={{ fontSize: 11.5 }}>View Misses</span>}
                >
                  <IconButton
                    size="medium"
                    className="infoIcon"
                    onClick={() => gotoLaborGridMisses(kpi_slno)}
                  >
                    <OpenInNewOutlinedIcon className="drilldown-icon" />
                  </IconButton>
                </Tooltip>
              </span>
            </Grid>
          </Grid>
        );
      } else if (type == 'B' && kpino == '2') {
        return (
          <>
            <div class="content-value-block-1 b-block-1 content-value-inner">
              {formatCellValueKPI(value[0], null, 1)}
            </div>
            <div class="slash content-value-inner">/</div>
            <div class="content-value-block-2  b-block-2 content-value-inner">
              {formatCellValueKPI(value[1], '$', 1)}
            </div>
            <div class="slash content-value-inner">/</div>
            <div class="content-value-block-3  b-block-3 content-value-inner">
              {formatCellValueKPI(value[2], '$', 1)}
            </div>
          </>
        );
      } else if (type == 'B' && kpino == '3') {
        return (
          <>
            <div class="content-value-block-1 content-value-inner"></div>
            <div class="slash content-value-inner"></div>
            <div class="content-value-block-2 content-value-inner"></div>
            <div class="slash content-value-inner"></div>
            <div class="content-value-block-3 content-value-inner">
              {formatCellValueKPI(value[0], '$', 1)}
            </div>
          </>
        );
      } else if (type == 'B' && kpino == '4') {
        return (
          <>
            <div class="content-value-block-1 content-value-inner">
              {formatCellValueKPI(value[0], '%', 1)}
            </div>
            <div class="slash content-value-inner">/</div>
            <div class="content-value-block-2 content-value-inner">
              {formatCellValueKPI(value[1], '%', 1)}
            </div>
            <div class="slash content-value-inner"></div>
            <div class="content-value-block-3 content-value-inner"></div>
          </>
        );
      } else if (
        (type == 'E' || type == 'F') &&
        (kpi_slno == '1' || kpi_slno == '4')
      ) {
        return (
          <Grid xs={12} className="flex-grid-2-block">
            <Grid xs={7}>
              <div class="content-value-block-1 content-value-inner">
                {type == 'E' &&
                (session.mpiFlag == 'FALSE' || session.mpiFlag == false)
                  ? formatCellValueKPI(0, null, 1)
                  : type == 'F' &&
                    (session.menuFlag == 'FALSE' || session.menuFlag == false)
                  ? formatCellValueKPI(0, null, 1)
                  : kpi_slno == '1'
                  ? formatCellValueKPI(value[0], null, 1)
                  : formatCellValueKPInew(value[0], null, 1)}
              </div>
              <div class="slash content-value-inner">/</div>
              <div class="content-value-block-2 content-value-inner">
                {type == 'E' &&
                (session.mpiFlag == 'FALSE' || session.mpiFlag == false)
                  ? formatCellValueKPI(0, null, 1)
                  : type == 'F' &&
                    (session.menuFlag == 'FALSE' || session.menuFlag == false)
                  ? formatCellValueKPI(0, null, 1)
                  : kpi_slno == '1'
                  ? formatCellValueKPI(value[1], null, 1)
                  : formatCellValueKPInew(value[1], null, 1)}
              </div>
            </Grid>
            <Grid xs={2} className="content-value-block-3 content-value-inner">
              {type == 'E' &&
              (session.mpiFlag == 'FALSE' || session.mpiFlag == false)
                ? formatCellValueKPI(0, '%', 1)
                : type == 'F' &&
                  (session.menuFlag == 'FALSE' || session.menuFlag == false)
                ? formatCellValueKPI(0, '%', 1)
                : formatCellValueKPI(value[2], '%', 1)}
            </Grid>
            <Grid xs={2}>
              {type == 'E' && kpi_slno == 1 && (
                <span className={'gridDrilldown'}>
                  <Tooltip
                    title={
                      <span style={{ fontSize: 11.5 }}>Detailed View</span>
                    }
                  >
                    <IconButton
                      size="medium"
                      className="infoIcon"
                      onClick={() => gotoMPIStatDrilldown()}
                    >
                      <OpenInNewOutlinedIcon className="drilldown-icon" />
                    </IconButton>
                  </Tooltip>
                </span>
              )}
            </Grid>
          </Grid>
        );
      } else if ((type == 'E' || type == 'F') && kpi_slno == '3') {
        return (
          <Grid xs={12} className="flex-grid-2-block">
            <Grid xs={7}>
              <div class="content-value-block-1 content-value-inner">
                {type == 'E' &&
                (session.mpiFlag == 'FALSE' || session.mpiFlag == false)
                  ? formatCellValueKPI(0, '$', 1)
                  : type == 'F' &&
                    (session.menuFlag == 'FALSE' || session.menuFlag == false)
                  ? formatCellValueKPI(0, '$', 1)
                  : formatCellValueKPI(value[0], '$', 1)}
              </div>
              <div class="slash content-value-inner"></div>
              <div class="content-value-block-2 content-value-inner"></div>
            </Grid>
            <Grid xs={2} className="content-value-block-3 content-value-inner">
              {type == 'E' &&
              (session.mpiFlag == 'FALSE' || session.mpiFlag == false)
                ? formatCellValueKPI(0, '%', 1)
                : type == 'F' &&
                  (session.menuFlag == 'FALSE' || session.menuFlag == false)
                ? formatCellValueKPI(0, '%', 1)
                : formatCellValueKPI(value[1], '%', 1)}
            </Grid>
          </Grid>
        );
      } else {
        return (
          <span>
            {type == 'B'
              ? formatCellValueKPI(value[0], '$', 1)
              : formatCellValueKPI(value[0], null, 1)}
          </span>
        );
      }
    } else {
      let kpi_val;
      if (type == 'B') {
        if (kpino == 3) {
          kpi_val = formatCellValueKPI(value[0], '$', 1);
        } else {
          kpi_val = formatCellValueKPI(value[0], null, 1);
        }

        return (
          <>
            <div class="content-value-block-1 content-value-inner">
              {kpi_val}
            </div>
            <div class="slash content-value-inner"></div>
            <div class="content-value-block-2 content-value-inner"></div>
            <div class="slash content-value-inner"></div>
            <div class="content-value-block-3 content-value-inner">
              {type == 'B' && kpino == '1' && (
                <Grid xs={2}>
                  <span className={'gridDrilldown'}>
                    <Tooltip
                      title={
                        <span style={{ fontSize: 11.5 }}>View Misses</span>
                      }
                    >
                      <IconButton
                        size="medium"
                        className="infoIcon"
                        onClick={() => gotoLaborGridMisses(kpi_slno)}
                      >
                        <OpenInNewOutlinedIcon className="drilldown-icon" />
                      </IconButton>
                    </Tooltip>
                  </span>
                </Grid>
              )}
            </div>
          </>
        );
      } else if ((type == 'E' || type == 'F') && kpi_slno == '2') {
        return (
          <Grid xs={12} className="flex-grid-2-block">
            <Grid xs={8}>
              <div class="content-value-block-1 content-value-inner">
                {type == 'E' &&
                (session.mpiFlag == 'FALSE' || session.mpiFlag == false)
                  ? formatCellValueKPI(0, '$', 1)
                  : type == 'F' &&
                    (session.menuFlag == 'FALSE' || session.menuFlag == false)
                  ? formatCellValueKPI(0, '$', 1)
                  : formatCellValueKPI(value[0], '$', 1)}
              </div>
              <div class="slash content-value-inner"></div>
              <div class="content-value-block-2 content-value-inner"></div>
            </Grid>
          </Grid>
        );
      } else if ((type == 'E' || type == 'F') && kpi_slno == '5') {
        return (
          <Grid xs={12} className="flex-grid-2-block">
            <Grid xs={8}>
              <div class="content-value-block-1 content-value-inner">
                {type == 'E' &&
                (session.mpiFlag == 'FALSE' || session.mpiFlag == false)
                  ? formatCellValueKPI(0, null, 1)
                  : type == 'F' &&
                    (session.menuFlag == 'FALSE' || session.menuFlag == false)
                  ? formatCellValueKPI(0, null, 1)
                  : formatCellValueKPI(value[0], null, 2)}
              </div>
              <div class="slash content-value-inner"></div>
              <div class="content-value-block-2 content-value-inner"></div>
            </Grid>
          </Grid>
        );
      }
      // else if ((type == 'E') && kpi_slno == '1') {
      //   return (

      //       <>

      //       <Grid xs={2}>
      //           {type=='E' && kpi_slno== 1 &&
      //           <span className={'gridDrilldown'}>
      //             <Tooltip
      //               title={<span style={{ fontSize: 11.5 }}>View Misses</span>}
      //             >
      //               <IconButton
      //                 size="medium"
      //                 className="infoIcon"
      //                 onClick={() => gotoLaborGridMisses(kpi_slno)}
      //               >
      //                 <OpenInNewOutlinedIcon className="drilldown-icon" />
      //               </IconButton>
      //             </Tooltip>
      //           </span>

      //           }
      //         </Grid>
      //         </>
      //   );
      // }
    }
  };

  return (
    <div class="orange-block-content">
      {data.map((group, groupIndex) => (
        <>
          {groupIndex === 0 && group[0].kpi_type_code === 'B' && (
            <div class="content-row-title">
              <h2>
                {itemsValues.kpi_type_code + ') '}
                {ReactHtmlParser(itemsValues.kpi_type)}
              </h2>
            </div>
          )}

          {group[0].kpi_type_code === 'E' && group[0].kpi_no == 1 && (
            <div class="content-row-title e-block-title">
              <h2>
                {group[0].kpi_type_code + ') '}
                {ReactHtmlParser(group[0].kpi_type)}
              </h2>
            </div>
          )}
          {group[0].kpi_type_code === 'F' && group[0].kpi_no == 1 && (
            <div class="content-row-title middle-title">
              <h2>
                {group[0].kpi_type_code + ') '}
                {ReactHtmlParser(group[0].kpi_type)}
              </h2>
            </div>
          )}
          <div class="content-row">
            {/* <div class="info-icon">
              <img alt="" class="" src="/images/img/information-button-white.png" />
            </div> */}

            <div class="content-icon">
              {groupIndex === 0 && group[0].kpi_type_code === 'B' && (
                <div className={'imageDiv'}>
                  <img
                    className={classes.icon}
                    alt="Fixed Ops"
                    src={`/images/kpis/compliance-1.png`}
                  />
                </div>
              )}

              {groupIndex === 1 && group[0].kpi_type_code === 'B' && (
                <div className={'imageDiv'}>
                  <img
                    className={classes.icon}
                    alt="Fixed Ops"
                    src={`/images/kpis/spare-parts-1.png`}
                  />
                </div>
              )}
              {groupIndex === 2 && group[0].kpi_type_code === 'B' && (
                <div className={'imageDiv'}>
                  <img
                    className={classes.icon}
                    alt="Fixed Ops"
                    src={`/images/kpis/compliance-1.png`}
                  />
                </div>
              )}
              {groupIndex === 3 && group[0].kpi_type_code === 'B' && (
                <div className={'imageDiv'}>
                  <img
                    className={classes.icon}
                    alt="Fixed Ops"
                    src={`/images/kpis/spare-parts-1.png`}
                  />
                </div>
              )}
              {group[0].kpi_type_code === 'E' && (
                <div className={'imageDiv'}>
                  <img
                    className={classes.icon}
                    alt="Fixed Ops"
                    src={`/images/kpis/opportunity-1-E.png`}
                  />
                </div>
              )}
              {/* {group[0].kpi_type_code === 'F' && (
                <div className={'imageDiv'}>
                  <img
                    className={classes.icon}
                    alt="Fixed Ops"
                    src={`/images/kpis/opportunity-1-E.png`}
                  />
                </div>
              )} */}
            </div>

            <div
              className={clsx(
                'content-right',

                group[0].kpi_type_code == 'E' ? 'content-col-e-block' : ''
              )}
            >
              {group[0].kpi_type_code != 'E' && group[0].kpi_type_code != 'F'
                ? group.map((item, itemIndex) => (
                    <div
                      className={clsx(
                        'content-col',

                        item.kpi_name == 'Maintenance / Repair Work Mix'
                          ? 'single-content-col'
                          : ''
                      )}
                    >
                      <div
                        className={clsx(
                          'content-label',
                          item.kpi_no == 2 ? 'content-label-b-m-block' : '',
                          item.kpi_no != 2 ? 'content-label-b-block' : ''
                        )}
                      >
                        {ReactHtmlParser(item.kpi_name)}
                        {' :'}
                      </div>
                      {/* <div class="content-separator">:</div> */}
                      <div
                        className={clsx(
                          'content-value',
                          item.kpi_no == 2 ? 'content-value-b-m-block ' : '',
                          item.kpi_no != 2 ? 'content-value-b-block ' : ''
                        )}
                      >
                        {formatKpiValue(
                          item.kpi_value != null ? item.kpi_value : '0',
                          item.kpi_no,
                          item.kpi_slno,
                          item.kpi_type_code
                        )}
                      </div>
                      {itemIndex === 0 &&
                        (group[0].kpi_type_code == 'B' &&
                        (item.kpi_no == 2 || item.kpi_no == 3) ? (
                          <HtmlTooltipNoValue
                            title={ReactHtmlParser(group[0].tool_tip)}
                            placement="right"
                            classes={{
                              tooltip: 'middle-grid-tooltip'
                            }}
                            // style={{ height: 10 }}
                          >
                            <IconButton size="medium" className="infoIcon">
                              <InfoOutlinedIcon className="info-icon" />
                            </IconButton>
                          </HtmlTooltipNoValue>
                        ) : (
                          <HtmlTooltip
                            title={ReactHtmlParser(group[0].tool_tip)}
                            classes={{
                              tooltip: 'content-popper  middle-grid-tooltip'
                            }}
                            placement="right"
                            //open={true}
                            // style={{ height: 10 }}
                          >
                            <IconButton size="medium" className="infoIcon">
                              <InfoOutlinedIcon className="info-icon" />
                            </IconButton>
                          </HtmlTooltip>
                        ))}
                    </div>
                  ))
                : group.map((item, itemIndex) => (
                    <div
                      className={clsx(
                        'content-col',

                        item.kpi_type_code == 'E' ? 'e-first-block' : ''
                      )}
                    >
                      <div class="content-label ">
                        {ReactHtmlParser(item.kpi_name)} {' :'}
                      </div>{' '}
                      {/* <div class="content-separator">:</div> */}
                      <div class="content-value">
                        {formatKpiValue(
                          item.kpi_value != null ? item.kpi_value : '0',
                          item.kpi_no,
                          item.kpi_slno,
                          item.kpi_type_code
                        )}
                      </div>
                      {itemIndex === 0 &&
                        (item.kpi_type_code == 'E' ||
                        item.kpi_type_code == 'F' ? (
                          <HtmlTooltipNoValue
                            title={ReactHtmlParser(group[0].tool_tip)}
                            placement="right"
                            classes={{
                              tooltip: 'middle-grid-tooltip'
                            }}
                            // classes={{
                            //   tooltip: 'middle-grid-tooltip'
                            // }}
                            // open={true}
                            // style={{ height: 10 }}
                          >
                            <IconButton size="medium" className="infoIcon">
                              <InfoOutlinedIcon className="info-icon" />
                            </IconButton>
                          </HtmlTooltipNoValue>
                        ) : (
                          <HtmlTooltip
                            title={ReactHtmlParser(group[0].tool_tip)}
                            classes={{
                              tooltip: 'content-popper middle-grid-tooltip'
                            }}
                            //open={true}
                            placement="right"
                            // style={{ height: 10 }}
                          >
                            <IconButton size="medium" className="infoIcon">
                              <InfoOutlinedIcon className="info-icon" />
                            </IconButton>
                          </HtmlTooltip>
                        ))}
                    </div>
                  ))}
            </div>
          </div>
        </>
      ))}
    </div>
  );
}

export default MiddleGrid;
