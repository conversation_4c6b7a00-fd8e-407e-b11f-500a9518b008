import { makeStyles, withStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import React, { memo, useEffect, useState } from 'react';
import 'src/styles.css';
import { Divider, Tooltip } from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import { withKeycloak } from '@react-keycloak/web';

//import kpiTest from '../../mock/kpiTest.json';
import { getKPIScoredCardsValues } from 'src/utils/hasuraServices';

import CardKpi from '../../components/CardKpi';
import PaperLoader from '../../components/PaperLoader';

const useStyles = makeStyles(theme => ({
  containerCards: {
    alignItems: 'center',
    display: 'inline-block',
    marginTop: 5
  }
}));

const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    minWidth: 470,
    maxWidth: 600,
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b'
  }
}))(Tooltip);
function KpiSummary(props) {
  const [kpidata, setKpidata] = useState([]);
  const [kpishowndata, setKpishowndata] = useState([]);
  const [kpishowndataCombined, setKpishowndataCombined] = useState([]);

  useEffect(() => {
    getKPIScoredCardsValues(
      null,
      null,
      0,
      null,
      null,
      'kpi_scorecard',
      '+05:30',
      0,
      'LYEAR',
      'C',
      'Customer',
      callback => {
        if (
          callback.data.statelessDbdKpiScorecardGetKpiScorecardDetailsHomepage
            .kpiScorecardDetailsHomepages[0]
        ) {
          let dataArr = JSON.parse(
            callback.data.statelessDbdKpiScorecardGetKpiScorecardDetailsHomepage
              .kpiScorecardDetailsHomepages[0].kpiScorecardJson
          );
          //console.log('teeeeeeeeeeeeeee====', dataArr);

          setKpidata(dataArr);
        }
      }
    );
  }, [getKPIScoredCardsValues]);
  useEffect(() => {
    const groupedData = kpidata.reduce((result, entry) => {
      const { kpi_type, kpi_type_code, kpi_no, kpi_slno } = entry;
      const typeKey = `${kpi_type_code}`;

      if (!result[typeKey]) {
        result[typeKey] = {};
      }

      if (!result[typeKey][kpi_no]) {
        result[typeKey][kpi_no] = [];
      }

      result[typeKey][kpi_no].push(entry);

      return result;
    }, {});

    // Merge "Opportunities - MPI" and "Opportunities - Menu Sales" groups
    const mpiKey = 'E';
    const menuSalesKey = 'F';

    if (groupedData[mpiKey] && groupedData[menuSalesKey]) {
      Object.keys(groupedData[mpiKey]).forEach(kpiNo => {
        const mpiGroup = groupedData[mpiKey][kpiNo];
        const menuSalesGroup = groupedData[menuSalesKey][kpiNo];

        if (menuSalesGroup) {
          mpiGroup.push(...menuSalesGroup);
        }
      });
      delete groupedData[menuSalesKey];
    }

    // Convert the groupedData object into an array
    const groupedArray = Object.entries(groupedData).map(([type, groups]) => {
      const firstGroup = groups[1];
      const kpi_type =
        firstGroup && firstGroup.length > 0 ? firstGroup[0].kpi_type : null;

      return {
        kpi_type: kpi_type,
        kpi_type_code: type,
        groups: Object.values(groups)
      };
    });

    console.log('groupedArray----', groupedArray);

    setKpishowndata(groupedArray);
  }, [kpidata]);

  useEffect(() => {
    if (kpishowndata[4]) {
      const groupedDataCombined = {};
      kpishowndata[4].groups.forEach(group => {
        group.forEach(item => {
          const kpiType = item.kpi_type;

          if (!groupedDataCombined[kpiType]) {
            groupedDataCombined[kpiType] = [];
          }

          groupedDataCombined[kpiType].push(item);
        });
      });

      setKpishowndataCombined(Object.values(groupedDataCombined));
    }
  }, [kpishowndata]);

  //let toggleSelection = 'C';
  // if(props.internalMisses == true) {
  //   toggleSelection = 'I';
  // }
  let toggleSelection = props.gridType
    ? props.gridType
    : props.payTypeList && props.payTypeList.length > 0
    ? props.payTypeList[0]
    : '';
  if (toggleSelection && props.payTypeList.length > 0) {
    if (props.payTypeList.includes(toggleSelection)) {
      toggleSelection = toggleSelection;
    } else {
      toggleSelection = props.payTypeList[0];
    }
  }

  const classes = useStyles();

  return (
    <>
      <Grid
        container
        className={clsx(classes.containerCards)}
        id="kpiScoreCards"
      >
        {kpishowndata && kpishowndata.length > 0 ? (
          <>
            {kpishowndata[0] && (
              <CardKpi
                kpishowndata={kpishowndata[0]}
                cardClass="card card-2 labourPartsTotal"
                valueKey="A"
              />
            )}
            {kpishowndata[1] && (
              <CardKpi
                kpishowndata={kpishowndata[1]}
                cardClass="card-target card-3 repairTotalParts"
                valueKey="B"
              />
            )}
            {kpishowndata[2] && (
              <CardKpi
                kpishowndata={kpishowndata[2]}
                cardClass="card card-2 avgVolume"
                valueKey="C"
              />
            )}
            <Divider className={'kpiDivider'} />
            {kpishowndata[3] && (
              <CardKpi
                kpishowndata={kpishowndata[3]}
                cardClass="card card-1 countPartsTotal"
                valueKey="D"
              />
            )}
            {kpishowndataCombined && (
              <CardKpi
                kpishowndata={kpishowndataCombined}
                cardClass="card-target card-1 mpiHourMenu"
                valueKey="E"
              />
            )}
            {kpishowndata[5] && (
              <CardKpi
                kpishowndata={kpishowndata[5]}
                cardClass="card card-1 partsTotalAvg"
                valueKey="G"
              />
            )}
          </>
        ) : (
          <PaperLoader />
        )}
      </Grid>
    </>
  );
}

KpiSummary.propTypes = {
  className: PropTypes.string,
  filterCharts: PropTypes.func,
  setTitle: PropTypes.string,
  resetDashboard: PropTypes.func,
  checkSuccess: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default withKeycloak(memo(KpiSummary));
