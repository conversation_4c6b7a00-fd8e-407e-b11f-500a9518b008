import { makeStyles, withStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import React, { memo, useEffect, useState } from 'react';
import 'src/styles.css';
import {
  Card,
  CardContent,
  Divider,
  Typography,
  IconButton,
  Tooltip,
  Radio,
  Box,
  LinearProgress
} from '@material-ui/core';
import { CircularProgress, Grid } from '@material-ui/core';
import clsx from 'clsx';
import { withKeycloak } from '@react-keycloak/web';

//import kpiTest from '../../mock/kpiTest';
import { getKPIScoredCardsValues, getHomeKpis } from 'src/utils/hasuraServices';

//import CardKpi from '../../components/CardKpi';
import PaperLoader from '../../components/PaperLoader';
import { useHistory } from 'react-router';
import { useSelector, useDispatch } from 'react-redux';
import { setInternalKpiToggle } from 'src/actions';
import getTooltipConentKPI from '../../utils/KpiTooltips';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import InsertChartOutlined from '@material-ui/icons/InsertChartOutlined';
import $ from 'jquery';
import { getTimeZone, getYearValue } from 'src/utils/Utils';

import Switch from '@material-ui/core/Switch';
import { styled } from '@material-ui/styles';

import SideGrid from './SideGrid';
import MiddleGrid from './MiddleGrid';

const useStyles = makeStyles(theme => ({
  containerCards: {
    alignItems: 'center',
    display: 'inline-block',
    marginTop: 5,
    height: window.innerHeight
  },
  circularDiv: {
    marginTop: '20%'
  }
}));

const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    minWidth: 470,
    maxWidth: 600,
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b'
  }
}))(Tooltip);
function KpiSummary(props) {
  const [kpidata, setKpidata] = useState([]);
  const [kpishowndata, setKpishowndata] = useState([]);
  const [kpishowndataCombined, setKpishowndataCombined] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  //let toggleSelection = 'C';
  // if(props.internalMisses == true) {
  //   toggleSelection = 'I';
  // }
  let toggleSelection = props.gridType
    ? props.gridType
    : props.payTypeList && props.payTypeList.length > 0
    ? props.payTypeList[0]
    : '';
  if (toggleSelection && props.payTypeList.length > 0) {
    if (props.payTypeList.includes(toggleSelection)) {
      toggleSelection = toggleSelection;
    } else {
      toggleSelection = props.payTypeList[0];
    }
  }
  const [pickerChanged, setPickerChanged] = useState(false);
  const classes = useStyles();
  const [placement, setPlacement] = useState();
  const history = useHistory();
  const [checked, setToggle] = useState(props.internalMisses);
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const [selectedValue, setSelectedValue] = useState(toggleSelection);

  useEffect(() => {
    console.log('session', session);
    if (props.filterChanged) {
      setIsLoading(true);
      getHomeKpis(
        props.filterStart,
        props.filterEnd,
        session.serviceAdvisor.includes('All') ? null : session.serviceAdvisor,
        session.technician.includes('All') ? null : session.technician,
        2,
        'kpi_scorecard',
        getTimeZone(),
        1,
        null,
        'C',
        'Customer',
        callback => {
          if (
            callback.data.statelessDbdKpiScorecardGetKpiScorecardDetails
              .kpiScorecardDetails[0]
          ) {
            let dataArr = JSON.parse(
              callback.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails[0].jsonData
            );
            setIsLoading(false);

            setKpidata(dataArr);

            props.resetFilterState(false);
          }
        }
      );
    }
  }, [props.filterChanged, session.serviceAdvisor, session.technician]);
  useEffect(() => {
    const groupedData = kpidata.reduce((result, entry) => {
      const { kpi_type, kpi_type_code, kpi_no, kpi_slno } = entry;
      const typeKey = `${kpi_type_code}`;

      if (!result[typeKey]) {
        result[typeKey] = {};
      }

      if (!result[typeKey][kpi_no]) {
        result[typeKey][kpi_no] = [];
      }

      result[typeKey][kpi_no].push(entry);

      return result;
    }, {});

    // Merge "Opportunities - MPI" and "Opportunities - Menu Sales" groups
    const mpiKey = 'E';
    const menuSalesKey = 'F';

    if (groupedData[mpiKey] && groupedData[menuSalesKey]) {
      Object.keys(groupedData[mpiKey]).forEach(kpiNo => {
        const mpiGroup = groupedData[mpiKey][kpiNo];
        const menuSalesGroup = groupedData[menuSalesKey][kpiNo];

        if (menuSalesGroup) {
          mpiGroup.push(...menuSalesGroup);
        }
      });
      delete groupedData[menuSalesKey];
    }

    // Convert the groupedData object into an array
    const groupedArray = Object.entries(groupedData).map(([type, groups]) => {
      const firstGroup = groups[1];
      const kpi_type =
        firstGroup && firstGroup.length > 0 ? firstGroup[0].kpi_type : null;

      return {
        kpi_type: kpi_type,
        kpi_type_code: type,
        groups: Object.values(groups)
      };
    });

    setIsLoading(false);
    setKpishowndata(groupedArray);
  }, [kpidata]);

  useEffect(() => {
    if (kpishowndata[4]) {
      // alert(2);
      const groupedDataCombined = {};
      kpishowndata[4].groups.forEach(group => {
        group.forEach(item => {
          const kpiType = item.kpi_type;

          if (!groupedDataCombined[kpiType]) {
            groupedDataCombined[kpiType] = [];
          }

          groupedDataCombined[kpiType].push(item);
        });
      });
      setKpishowndataCombined(Object.values(groupedDataCombined));
    }
  }, [kpishowndata]);

  const formatSummaryValues = (value, status) => {
    return value == null
      ? 0
      : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };
  const dataLoader = () => {
    return (
      <div class="stage" style={{ marginLeft: '30%' }}>
        <div class="dot-pulse"></div>
      </div>
    );
  };
  const dataLoaderKpi = () => {
    return (
      <div class="stage" style={{ marginLeft: '30%' }}>
        <div class="dot-pulse-kpi"></div>
      </div>
    );
  };
  const gotoLaborGridMisses = type => {
    if (type == 'Labor') {
      window.sortStateLbrMiss = {};
      window.filterStateLbrMiss = {};
    } else {
      window.sortStatePrtMiss = {};
      window.filterStatePrtMiss = {};
    }
    type == 'Labor'
      ? history.push({
          pathname: '/LaborMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Home',
            previousToggle: props.toggleOption,
            payType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : props.keycloak.realm == 'suntrupag' &&
                  selectedValue == 'Warranty'
                ? 'W'
                : 'C',
            previousPayType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            gridType: selectedValue,
            previousGridType: selectedValue,
            filterStart: props.filterStart,
            filterEnd: props.filterEnd
          }
        })
      : history.push({
          pathname: '/PartsMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Home',
            previousToggle: props.toggleOption,
            payType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            previousPayType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            gridType: selectedValue,
            previousGridType: selectedValue
          }
        });
  };
  /* const viewInternalMisses1 = (event) => {
    if(selectedValue == "C") {
      setSelectedValue(event.target.value);
      setToggle(true);
      props.handleInternalMisses(true);
      if(!session.serviceAdvisor.includes('All') && session.serviceAdvisor.length == 1) {
        dispatch(setInternalKpiToggle('I'));
      } else {
        dispatch(setInternalKpiToggle('C'));
      }
    } else {
      setSelectedValue(event.target.value);
      setToggle(false);
      props.handleInternalMisses(false);
      dispatch(setInternalKpiToggle('C'));
    }
  }*/
  const viewInternalMisses1 = event => {
    if (selectedValue != event.target.value) {
      setSelectedValue(event.target.value);
      setToggle(true);
      if (event.target.value == 'Internal') {
        props.handleInternalMisses(true);
      } else {
        props.handleInternalMisses(false);
      }
      props.handleGridType(event.target.value);
      if (
        !session.serviceAdvisor.includes('All') &&
        session.serviceAdvisor.length == 1
      ) {
        dispatch(setInternalKpiToggle(event.target.value));
      }
      // else {
      //   dispatch(setInternalKpiToggle(event.target.value));
      // }
    } else {
      setSelectedValue(event.target.value);
      setToggle(false);
      if (event.target.value == 'Internal') {
        props.handleInternalMisses(true);
      } else {
        props.handleInternalMisses(false);
      }
      dispatch(setInternalKpiToggle(event.target.value));
    }
  };
  const viewInternalMisses = () => {
    if (checked == false) {
      setToggle(true);
      props.handleInternalMisses(true);
      if (
        !session.serviceAdvisor.includes('All') &&
        session.serviceAdvisor.length == 1
      ) {
        dispatch(setInternalKpiToggle('I'));
      } else {
        dispatch(setInternalKpiToggle('C'));
      }
    } else {
      setToggle(false);
      props.handleInternalMisses(false);
      dispatch(setInternalKpiToggle('C'));
    }
  };

  const gotoSpecialMetrics = () => {
    history.push({
      pathname: '/SpecialMetrics',
      state: {
        selectedToggle: props.toggleOption,
        parent: 'Home',
        payType: checked == true ? 'I' : 'C',
        gridType: selectedValue
      }
    });
  };
  const gotoTechMetrics = () => {
    history.push({
      pathname: '/TechnicianPerformance',
      state: {
        selectedToggle: props.toggleOption,
        parent: 'Home',
        payType: checked == true ? 'I' : 'C',
        gridType: selectedValue,
        tabSelection: 'seven',
        techNo: session.technician
      }
    });
  };
  const getKpiTooltips = (chartId, gridTitle) => {
    return (
      <HtmlTooltip
        title={getTooltipConentKPI(chartId, classes, gridTitle)}
        style={{ height: 10 }}
      >
        <IconButton size="medium" classes="infoIcon">
          <InfoOutlinedIcon />
        </IconButton>
      </HtmlTooltip>
    );
  };

  const getKpiVisualizations = chartId => {
    return (
      <Tooltip
        title={<span style={{ fontSize: 11.5 }}>Go to Visualization</span>}
      >
        <IconButton
          size="medium"
          classes="infoIcon"
          onClick={() => gotoVisualization(chartId)}
        >
          <InsertChartOutlined />
        </IconButton>
      </Tooltip>
    );
  };
  const gotoVisualization = chartId => {
    let selectedId = $(
      '.react-grid-item.react-draggable.cssTransforms.react-resizable.selected'
    ).attr('id');
    const container = document.querySelector('.makeStyles-container-3');
    if (selectedId) {
      $('#' + selectedId).removeClass('selected');
    }
    var element = document.getElementById('chartContainer_' + chartId);
    // element.scrollIntoView({
    //   behavior: 'smooth',
    //   block: 'center',
    //   inline: 'center'
    // });
    if (element) {
      element.scrollIntoView({ block: 'center' });
      $('#chartContainer_' + chartId).addClass('selected');
      $('#visualization_' + chartId).css('display', 'block');
    }
  };

  const AntSwitch = styled(Switch)(({ theme }) => ({
    width: 28,
    height: 16,
    padding: 0,
    display: 'flex',
    '&:active': {
      '& .MuiSwitch-thumb': {
        width: 15
      },
      '& .MuiSwitch-switchBase.Mui-checked': {
        transform: 'translateX(9px)'
      }
    },
    '& .MuiSwitch-switchBase': {
      padding: 2,
      '&.Mui-checked': {
        transform: 'translateX(12px)',
        color: '#fff',
        '& + .MuiSwitch-track': {
          opacity: 1
          //backgroundColor: theme.palette.mode === 'dark' ? '#2ECA45' : '#2ECA45',
        }
      }
    },
    '& .MuiSwitch-thumb': {
      boxShadow: '0 2px 4px 0 rgb(0 35 11 / 20%)',
      width: 12,
      height: 12,
      borderRadius: 6,
      transition: theme.transitions.create(['width'], {
        duration: 200
      })
    },
    '& .MuiSwitch-track': {
      borderRadius: 16 / 2,
      opacity: 1,
      backgroundColor:
        theme.palette.mode === 'dark'
          ? 'rgba(255,255,255,.35)'
          : 'rgba(0,0,0,.25)',
      boxSizing: 'border-box'
    }
  }));
  var gridTitle = selectedValue.substring(0, 1) == 'I' ? 'Int' : 'CP';
  // var gridTitleParts =
  //   props.payTypeListParts.length > 1 && selectedValue.substring(0, 1) == 'I'
  //     ? 'Int'
  //     : 'CP';

  return (
    <>
      {/* {isLoading == true ? (
        <div>
          <Box className={classes.boxClass}>
            <LinearProgress color="secondary" />
            <Typography
              variant="h6"
              align="center"
              className={classes.boxClass}
              color="primary"
            >
              Processing...
            </Typography>
          </Box>
        </div>
      ) : null} */}
      <Grid
        container
        className={clsx(classes.containerCards)}
        id="kpiScoreCards"
      >
        {kpishowndata &&
        kpishowndata.length > 0 &&
        kpishowndataCombined.length > 0 ? (
          <>
            {isLoading ? (
              <div className={classes.circularDiv}>
                {/* <Box className={classes.boxClass}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    className={classes.boxClass}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box> */}
                <CircularProgress size={60} />
              </div>
            ) : (
              <div id="">
                <div class="container-new">
                  <div class="blocks-outer-first">
                    <div class="ash-block  a-block">
                      <SideGrid
                        kpishowndata={kpishowndata[0]}
                        filterStart={props.filterStart}
                        filterEnd={props.filterEnd}
                      />
                    </div>
                    <div class="orange-block">
                      <MiddleGrid
                        kpishowndata={kpishowndata[1]}
                        toggleOption={props.toggleOption}
                        filterStart={props.filterStart}
                        filterEnd={props.filterEnd}
                      />
                    </div>
                    <div class="ash-block">
                      <SideGrid
                        kpishowndata={kpishowndata[2]}
                        filterStart={props.filterStart}
                        filterEnd={props.filterEnd}
                      />
                    </div>
                  </div>
                  <div class="blocks-outer-first second-block">
                    <div class="ash-block">
                      <SideGrid
                        kpishowndata={kpishowndata[3]}
                        filterStart={props.filterStart}
                        filterEnd={props.filterEnd}
                      />
                    </div>
                    <div class="orange-block">
                      <MiddleGrid
                        kpishowndata={kpishowndataCombined}
                        toggleOption={props.toggleOption}
                        filterStart={props.filterStart}
                        filterEnd={props.filterEnd}
                      />
                    </div>
                    <div class="ash-block g-block">
                      <SideGrid
                        kpishowndata={kpishowndata[5]}
                        filterStart={props.filterStart}
                        filterEnd={props.filterEnd}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
            {/* {kpishowndata[0] && (
              <CardKpi
                kpishowndata={kpishowndata[0]}
                cardClass="card card-2 labourPartsTotal"
                valueKey="A"
              />
            )}
            {kpishowndata[1] && (
              <CardKpi
                kpishowndata={kpishowndata[1]}
                cardClass="card-target card-3 repairTotalParts"
                valueKey="B"
              />
            )}
            {kpishowndata[2] && (
              <CardKpi
                kpishowndata={kpishowndata[2]}
                cardClass="card card-2 avgVolume"
                valueKey="C"
              />
            )}
            <Divider className={'kpiDivider'} />
            {kpishowndata[3] && (
              <CardKpi
                kpishowndata={kpishowndata[3]}
                cardClass="card card-1 countPartsTotal"
                valueKey="D"
              />
            )}
            {kpishowndataCombined && (
              <CardKpi
                kpishowndata={kpishowndataCombined}
                cardClass="card-target card-1 mpiHourMenu"
                valueKey="E"
              />
            )}
            {kpishowndata[5] && (
              <CardKpi
                kpishowndata={kpishowndata[5]}
                cardClass="card card-1 partsTotalAvg"
                valueKey="G"
              />
            )}*/}
          </>
        ) : (
          <PaperLoader />
        )}
      </Grid>
    </>
  );
}

KpiSummary.propTypes = {
  className: PropTypes.string,
  filterCharts: PropTypes.func,
  setTitle: PropTypes.string,
  resetDashboard: PropTypes.func,
  checkSuccess: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default withKeycloak(memo(KpiSummary));
