import React, { useState, useEffect } from 'react';
import { makeStyles, withStyles } from '@material-ui/styles';
import ReactHtmlParser from 'react-html-parser';
import {
  Card,
  CardContent,
  Divider,
  Typography,
  IconButton,
  Tooltip
} from '@material-ui/core';
import clsx from 'clsx';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import { useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import { formatCellValueKPI, formatCellValueKPInew } from 'src/utils/Utils';
import getTooltipConentKPI from 'src/utils/KpiTooltips';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import DrilldownOneLineRo from 'src/views/AnalyzeData/DrilldownOneLineRo';
import DrilldownReturnRate from '../AnalyzeData/DrilldownReturnRate';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  },
  infoIcon: {
    padding: '0px'
  },
  oneLineDrilldown: {
    position: 'absolute'
  }
}));
const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },

  tooltip: {
    minWidth: 370,
    maxWidth: 500,
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '12px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b'
    // padding: '4px 20px'
  }
}))(Tooltip);

const HtmlTooltipNoValue = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    minWidth: 100,
    maxWidth: 500,
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '12px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b'
    //paddingLeft: 20
  }
}))(Tooltip);

function SideGrid(props) {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const history = useHistory();
  const [isLoading, setLoading] = useState(false);
  const [isLoadingParts, setLoadingParts] = useState(false);
  const itemsValues = props.kpishowndata;

  const dispatch = useDispatch();

  const gotoOneLineDrilldown = type => {
    history.push({
      pathname: '/AnalyzeData',
      search: '?OneLine',
      state: {
        type: 'one_line_drilldown',
        selectedToggle: props.toggleOption,
        parent: 'Home',
        payType: 'C',
        gridType: 'Customer',
        tabSelection: 'seven',
        techNo: session.technician,
        oneLineType: type,
        session: session,
        filterStart: props.filterStart,
        filterEnd: props.filterEnd
      }
    });
  };

  const formatKpiValue = (kpiValue, name, type, kpino) => {
    const value = kpiValue.split('/');
    // console.log('name===', name, kpino, type, kpiValue);
    if (value.length > 1 && name != 'Labor + Parts Sales') {
      if (name == 'Parts to Labor Ratio') {
        return (
          <span>
            {Math.sign(value) > -1
              ? '$' +
                parseFloat(value[0])
                  // .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : '$' +
                Math.abs(parseFloat(value[0]))
                  // .toFixed(2)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            {'  to  '}
            {formatCellValueKPI(value[1], '$', 2)}
          </span>
        );
      } else if (name == '% of Vehicles Serviced') {
        return <span>{formatCellValueKPI(value[0], '%', 1)}</span>;
      } else {
        return (
          <div>
            <span
              className={clsx(
                'inner-data-label',

                type == 'D' && kpino == 1 ? 'd-first-block' : ''
              )}
            >
              {type == 'A'
                ? formatCellValueKPI(value[0], '$', 1)
                : formatCellValueKPI(value[0], null, 1)}
            </span>

            <span class="inner-data">
              {formatCellValueKPI(value[1], '%', 2)}
            </span>
          </div>
        );
      }
    } else {
      let kpi_val;
      if (type == 'A' || (type != 'C' && name.includes('Sold Per'))) {
        kpi_val = formatCellValueKPI(value[0], '$', 1);
      } else if (name == 'Average Vehicle Age') {
        kpi_val = formatCellValueKPI(value[0], null, 2) + ' years';
      } else if (type == 'C' && name == 'Average CP Hours Per Vehicle') {
        kpi_val = formatCellValueKPInew(value[0]);
      } else {
        kpi_val = formatCellValueKPI(value[0], null, 2);
      }

      return kpi_val;
    }
  };
  const getKpiTooltips = (chartId, gridTitle) => {
    return (
      <HtmlTooltip title={getTooltipConentKPI(chartId, classes, gridTitle)}>
        <div class="info-icon">
          <img alt="" class="" src="/images/img/information-button.png" />
        </div>
      </HtmlTooltip>
    );
  };
  return (
    <div class="ash-block-content">
      <div class="content-row-title">
        <h2>
          {itemsValues.kpi_type_code + ') '}
          {ReactHtmlParser(itemsValues.kpi_type)}
        </h2>
      </div>
      {itemsValues.groups.map((group, groupIndex) => (
        <div class="content-row">
          <div class="content-icon">
            {groupIndex === 0 && group[0].kpi_type_code === 'A' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/lbr-gp-ro.png`} />
              </div>
            )}

            {groupIndex === 1 && group[0].kpi_type_code === 'A' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/prts-gp-ro.png`} />
              </div>
            )}
            {groupIndex === 2 && group[0].kpi_type_code === 'A' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/total-gp-ro.png`} />
              </div>
            )}
            {groupIndex === 3 && group[0].kpi_type_code === 'A' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/ratio.png`} />
              </div>
            )}
            {groupIndex === 0 && group[0].kpi_type_code === 'C' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/c-w-i-total.png`} />
              </div>
            )}

            {groupIndex === 1 && group[0].kpi_type_code === 'C' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/ro-count-share.png`} />
              </div>
            )}
            {groupIndex === 2 && group[0].kpi_type_code === 'C' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/avg-age.png`} />
              </div>
            )}
            {groupIndex === 0 && group[0].kpi_type_code === 'D' && (
              <div>
                <img alt="Fixed Ops" src={`/images/kpis/opportunity-1.png`} />
              </div>
            )}
            {groupIndex === 0 && group[0].kpi_type_code === 'G' && (
              <div>
                <img
                  className={classes.icon}
                  alt="Fixed Ops"
                  src={`/images/kpis/opportunity-1.png`}
                />
              </div>
            )}
            {groupIndex === 1 && group[0].kpi_type_code === 'D' && (
              <div className={'no-img'}>
                <div></div>
              </div>
            )}

            {groupIndex === 2 && group[0].kpi_type_code === 'D' && (
              <div className={'no-img'}>
                <div></div>
              </div>
            )}
            {groupIndex === 1 && group[0].kpi_type_code === 'G' && (
              <div className={'no-img'}>
                <div></div>
              </div>
            )}

            {groupIndex === 2 && group[0].kpi_type_code === 'G' && (
              <div className={'no-img'}>
                <div></div>
              </div>
            )}
          </div>

          <div
            className={clsx(
              'content-right',

              group[0].kpi_type_code === 'D' ? 'content-right-d-block' : ''
            )}
          >
            {group.map((item, itemIndex) => (
              <>
                {((item.kpi_type_code == 'D' &&
                  item.kpi_no == 3 &&
                  item.kpi_slno == 1) ||
                  (item.kpi_type_code == 'G' &&
                    item.kpi_no == 3 &&
                    item.kpi_slno == 1)) && (
                  <div>
                    <div class="content-col">
                      <div class="content-label"></div>
                      <div class="content-value"></div>
                    </div>
                    <div class="content-col">
                      <div class="content-label"></div>
                      <div class="content-value"></div>
                    </div>
                    <div class="content-col">
                      <div class="content-label"></div>
                      <div class="content-value"></div>
                    </div>
                  </div>
                )}

                {((item.kpi_type_code == 'D' &&
                  item.kpi_no == 3 &&
                  item.kpi_slno == 4) ||
                  (item.kpi_type_code == 'G' &&
                    item.kpi_no == 3 &&
                    item.kpi_slno == 4)) && (
                  <div>
                    <div class="content-col">
                      <div class="content-label"></div>
                      <div class="content-value"></div>
                    </div>
                    <div class="content-col">
                      <div class="content-label"></div>
                      <div class="content-value"></div>
                    </div>
                    <div class="content-col">
                      <div class="content-label"></div>
                      <div class="content-value"></div>
                    </div>
                  </div>
                )}
                <div
                  className={clsx(
                    'content-col',
                    item.kpi_type_code === 'C' && item.kpi_no == 3
                      ? 'content-right-c-block'
                      : '',
                    item.kpi_name == 'Parts to Labor Ratio'
                      ? 'single-content-col'
                      : ''
                  )}
                >
                  <div class="content-label">
                    {ReactHtmlParser(item.kpi_name)} {' :'}
                  </div>
                  {/* <div class="content-separator">:</div> */}
                  <div class="content-value">
                    {formatKpiValue(
                      item.kpi_value != null ? item.kpi_value : '0',
                      item.kpi_name,
                      item.kpi_type_code,
                      item.kpi_no
                    )}
                  </div>
                  {(item.kpi_type_code == 'D' || item.kpi_type_code == 'G') &&
                    item.kpi_slno == 2 &&
                    item.kpi_no == 1 && (
                      //  &&
                      // JSON.parse(localStorage.getItem('selectedStoreId'))
                      //   .length == 1
                      // <span className={'gridDrilldown'}>
                      <span
                        className={clsx('gridDrilldown', 'gridDrilldownRo')}
                      >
                        <Tooltip
                          title={
                            <span style={{ fontSize: 11.5 }}>
                              Detailed View
                            </span>
                          }
                        >
                          <IconButton
                            size="medium"
                            className="infoIcon"
                            onClick={() =>
                              // !session.technician.includes('All')
                              //   ? gotoTechMetrics()
                              //   : gotoSpecialMetrics(
                              //       item.kpi_type_code == 'D'
                              //         ? 'Under 60K'
                              //         : 'Over 60K'
                              //     )
                              gotoOneLineDrilldown(
                                item.kpi_type_code == 'D'
                                  ? 'Under 60K'
                                  : 'Over 60K'
                              )
                            }
                          >
                            <OpenInNewOutlinedIcon className="drilldown-icon" />
                          </IconButton>
                        </Tooltip>
                      </span>
                    )}

                  {itemIndex === 0 &&
                    (item.kpi_type_code == 'A' && item.kpi_no == 4 ? (
                      <HtmlTooltipNoValue
                        title={ReactHtmlParser(group[0].tool_tip)}
                        placement="right"
                        //open={true}
                        // style={{ height: 10 }}
                      >
                        <IconButton size="medium" className="infoIcon">
                          <InfoOutlinedIcon className="info-icon" />
                        </IconButton>
                      </HtmlTooltipNoValue>
                    ) : (
                      <HtmlTooltip
                        title={ReactHtmlParser(group[0].tool_tip)}
                        classes={{
                          tooltip:
                            item.kpi_type_code === 'C' && item.kpi_no == 1
                              ? 'content-popper kpic-first-top'
                              : item.kpi_type_code === 'C' && item.kpi_no == 2
                              ? 'content-popper kpic-second-top'
                              : item.kpi_type_code === 'C' && item.kpi_no == 3
                              ? 'content-popper kpic-third-top'
                              : item.kpi_type_code === 'G' && item.kpi_no == 1
                              ? 'content-popper kpig-first-top'
                              : 'content-popper zero-top'
                        }}
                        // open={item.kpi_type_code === 'G' ? true : false}
                        PopperProps={{
                          class:
                            item.kpi_type_code === 'G' && item.kpi_no == 1
                              ? 'kpig-popper'
                              : ''
                        }}
                        placement={
                          item.kpi_type_code === 'C'
                            ? 'bottom'
                            : item.kpi_type_code === 'G'
                            ? 'top'
                            : 'right'
                        }
                        style={{
                          display:
                            (item.kpi_type_code == 'D' ||
                              item.kpi_type_code == 'G') &&
                            item.kpi_slno == 1 &&
                            item.kpi_no != 1
                              ? 'none'
                              : 'block'
                          // marginTop:
                          //   item.kpi_type_code === 'C' ||
                          //   item.kpi_type_code === 'G'
                          //     ? 50
                          //     : 0
                        }}
                      >
                        <IconButton size="medium" className="infoIcon">
                          <InfoOutlinedIcon className="info-icon" />
                        </IconButton>
                      </HtmlTooltip>
                    ))}
                </div>
              </>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

export default SideGrid;
