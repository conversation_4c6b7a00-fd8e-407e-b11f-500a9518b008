import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  InputLabel,
  Select,
  MenuItem,
  Typography
} from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import validate from 'validate.js';
import SuccessSnackbar from './SuccessSnackbar';
import { withKeycloak } from '@react-keycloak/web';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import Input from '@material-ui/core/Input';
import { getAllChartDetails, getTimeZone } from '../../utils/Utils';
import {
  getStoreDetails,
  getStoreNamesByStoreId
} from 'src/utils/hasuraServices';
import { GroupInstanceIdCreator } from '@ag-grid-enterprise/all-modules';
import {
  getLastThirteenMonths,
  getGridorMatrixPayTypeDetails,
  getAllStoreNames,
  getKpiScoreCardDataStatus,
  getVersionFlags,
  getLastThreeYears
} from 'src/utils/hasuraServices';
import { traceSpan } from 'src/utils/OTTTracing';
import moment from 'moment';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgresWrite';
import {
  setStoreId,
  setLaborGridTypes,
  setKpiToggle,
  setKpiHomeToggle,
  setVersionFlag,
  setMpiFlag,
  setMenuFlag
} from 'src/actions';
import { INSERT_LOGIN_DETAILS } from 'src/graphql/queries';
import { ReactSession } from 'react-client-session';
import { set } from 'date-fns';
var lodash = require('lodash');

const schema = {
  email: {
    presence: { allowEmpty: false, message: 'is required' },
    email: true
  },
  password: {
    presence: { allowEmpty: false, message: 'is required' }
  }
};

const useStyles = makeStyles(theme => ({
  root: {},
  fields: {
    margin: theme.spacing(-1),
    display: 'flex',
    flexWrap: 'wrap',
    '& > *': {
      flexGrow: 1,
      margin: theme.spacing(1)
    }
  },
  submitButton: {
    marginTop: theme.spacing(2),
    width: '100%'
  },
  chips: {
    display: 'flex',
    flexWrap: 'wrap'
  },
  chip: {
    margin: 2
  }
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  anchorOrigin: {
    vertical: 'bottom',
    horizontal: 'left'
  },
  transformOrigin: {
    vertical: 'top',
    horizontal: 'left'
  },
  getContentAnchorEl: null,
  PaperProps: {
    style: {
      maxHeight: 48 * 4.5 + 8,
      width: 250
    }
  }
};

function LoginForm({ className, keycloak, keycloakInitialized, ...rest }) {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useDispatch();

  const [formState, setFormState] = useState({
    isValid: false,
    values: {},
    touched: {},
    errors: {}
  });
  localStorage.removeItem('storeIdSelected');

  const [storeSlected, setstoreSlected] = useState([]);
  const [storeName, setstoreName] = useState([]);
  const [groupList, setGroups] = useState([]);
  const [userName, setUserName] = useState();
  const [openSnackBar, setSnackbar] = useState(false);
  const [storeLoaded, setStoreLoaded] = useState(false);
  const session = useSelector(state => state.session);

  if (keycloak.authenticated) {
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Loggedin',
      value: localStorage.getItem('userID'),
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('logged-in', spanAttribute);
  }

  const handleChange = event => {
    if (event.target.value == 0 && event.target.innerText == 'All Stores') {
      setstoreSlected('All Stores');
    } else {
      setstoreSlected(event.target.value);

      let storeId = [];
      // for (let i = 0; i < event.target.value.length; i++) {
      storeId.push(event.target.value);
      // }
      let groupListNew = groupList.filter(a => a.includes(event.target.value));

      let groupIds = [];
      console.log('ggg====', groupListNew);
      groupListNew.map(value => {
        var targetName = event.target.value.split(' ');
        const lastSlashIndex = value.lastIndexOf('/');

        let groupName = value.substring(0, lastSlashIndex); // "Fred Anderson Chevrolet/Cadillac"
        const selStoreId = value.substring(lastSlashIndex + 1);
        //var groupName = value.split('/')[1].split('-');
        groupName = groupName.split('-');

        if (groupName.length > 2) {
          groupName = [groupName[1] + '-' + groupName[2]];
        } else {
          groupName = [groupName[1]];
        }
        groupName[0] = groupName[0].trim();

        if (
          value.includes(event.target.value) &&
          targetName.length == groupName[0].split(' ').length
        ) {
          //groupIds.push(value.split('/')[2]);
          groupIds.push(value.split('/').pop());
        }

        // const lastSlashIndex = groupIds.lastIndexOf('/');

        // let store = groupIds.substring(0, lastSlashIndex); // "Fred Anderson Chevrolet/Cadillac"
        // const selStoreId = groupIds.substring(lastSlashIndex + 1);

        if (selStoreId.trim() == groupIds[0]) {
          // localStorage.setItem('selectedStoreName', groupIds[0]);
          localStorage.setItem('selectedStoreId', JSON.stringify(groupIds));
        }
      });
      localStorage.setItem('selectedStoreName', event.target.value);
      localStorage.setItem('storeSelected', event.target.value);
    }

    if (event.target.innerText == 'All Stores') {
      const extractedValues = groupList.map(str => str.split('/').pop());

      const distinctValues = [...new Set(extractedValues)];
      let groupIds = distinctValues;
      localStorage.setItem('storeSelected', 'All Stores');
      localStorage.setItem('selectedStoreName', 'All Stores');
      localStorage.setItem('selectedStoreId', JSON.stringify(groupIds));
    }

    checkInternalToggle();
  };

  const handleSubmit = async event => {
    event.preventDefault();
    let newDate = new Date();
    let date = newDate.getDate();
    Date().toLocaleString();

    let storeValue = JSON.parse(localStorage.getItem('selectedStoreId'));
    let store;
    var offset = getTimeZone();

    if (storeValue && event.target.innerText != 'SIGN IN TO DASHBOARD') {
      let store = Object.values(storeValue);

      if (keycloakInitialized == true && keycloak.authenticated == true) {
        const client = makeApolloClientPostgres;
        const start = new Date();
        client
          .mutate({
            mutation: INSERT_LOGIN_DETAILS,
            variables: {
              // username: localStorage.getItem('storeValue'),
              username: session.currentUser,
              storeid: store[0],
              logindate: moment().format('MM-DD-YYYY HH:mm:ss'),
              offset: offset,
              logInout: 'IN'
            }
          })
          .then(result => {
            const spanAttribute = {
              pageUrl: '/Home',
              origin: '',
              event: 'Menu Load',
              is_from: 'INSERT_LOGIN_DETAILS',
              value: new Date() - start,
              provenance: localStorage.getItem('provenance')
            };
            traceSpan('Menu Load', spanAttribute);
          });
      }
    }

    ReactSession.set(
      'selectedStoreId',
      localStorage.getItem('selectedStoreId')
    );
    dispatch(setStoreId(localStorage.getItem('selectedStoreId')));
    if (event.target.id == 'login') {
      if (keycloakInitialized) {
        if (!keycloak.authenticated) {
          keycloak.login();
        } else {
        }
      }
    } else {
      // store selection continue button clicked

      if (keycloakInitialized) {
        if (!keycloak.authenticated) {
          keycloak.login();
        } else {
          const spanAttribute = {
            pageUrl: '',
            origin: '',
            event: 'Store changed',
            value: storeSlected,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Store changed', spanAttribute);
          if (
            //  JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1 &&
            // keycloak.realm == 'demoenterprise' ||
            // keycloak.realm == 'firstteamag'
            session.versionFlag == 'TRUE' ||
            session.versionFlag == true ||
            (localStorage.getItem('versionFlag') &&
              localStorage.getItem('versionFlag') == 'TRUE')
          ) {
            history.push('/Home');
          } else if (
            // JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1 &&
            // keycloak.realm != 'demoenterprise' &&
            // keycloak.realm != 'firstteamag'
            session.versionFlag == 'FALSE' ||
            session.versionFlag == false ||
            (localStorage.getItem('versionFlag') &&
              localStorage.getItem('versionFlag') == 'FALSE')
          ) {
            history.push('/2.4.0/Home');
          } else {
            history.push('/CPOverview');
          }
        }
      }
    }
  };

  function checkInternalToggle() {
    getPartsPayTypeList();
    let data = [];
    data.push('Customer');
    getGridorMatrixPayTypeDetails('paytype_grid', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (result.includes('Highline')) {
          result.reverse();
        }
        if (
          result.includes('Diesel') ||
          result.includes('HD & Hybrid') ||
          result.includes('Electric')
        ) {
          result.reverse();
        }
        if (
          result.includes('Honda Grid') &&
          result.includes('Volvo Grid') &&
          result.includes('Merc-Benz Grid')
        ) {
          result = ['Honda Grid', 'Volvo Grid', 'Merc-Benz Grid'];
        }
        if (
          result.includes('25-5500/Dsl') &&
          result.includes('Car/Lt Trk') &&
          result.includes('Flt-Sptr')
        ) {
          result = ['Car/Lt Trk', '25-5500/Dsl', 'Flt-Sptr'];
        }
        if (
          result.includes('Hyundai') &&
          result.includes('Genesis') &&
          result.includes('Customer')
        ) {
          data = ['Hyundai', 'Genesis', 'Customer'];
        }
        if (
          result.includes('EV') &&
          result.includes('Medium/HD') &&
          result.includes('Standard')
        ) {
          data = ['Standard', 'Medium/HD', 'EV'];
        }
        if (result.includes('HD') && result.includes('Standard')) {
          data = ['Standard', 'HD'];
        }
        dispatch(setLaborGridTypes(result));
        localStorage.setItem('laborGridTypes', JSON.stringify(result));
      } else {
        dispatch(setLaborGridTypes(data));
        localStorage.setItem('laborGridTypes', JSON.stringify(data));
      }
    });
  }
  function getPartsPayTypeList() {
    let data = [];
    data.push('Customer');
    getGridorMatrixPayTypeDetails('paytype_matrix', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (result.includes('Highline')) {
          result.reverse();
        }
        localStorage.setItem('partsMatrixTypes', JSON.stringify(result));
      } else {
        localStorage.setItem('partsMatrixTypes', JSON.stringify(data));
      }
    });
  }
  const dataLoader = () => {
    return (
      <div class="stage" style={{ marginLeft: '50%' }}>
        <div class="dot-pulse-login"></div>
      </div>
    );
  };
  function deleteAllCookies() {
    var cookies = document.cookie.split(';');

    for (var i = 0; i < cookies.length; i++) {
      console.log('cookies[i]', cookies[i]);
      var cookie = cookies[i];
      var eqPos = cookie.indexOf('=');
      var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
      document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }
  }

  useEffect(() => {
    deleteAllCookies();
  }, []);
  const hasError = field =>
    !!(formState.touched[field] && formState.errors[field]);

  useEffect(() => {
    localStorage.setItem('numTabs', '0');
    localStorage.setItem('storeChange', false);
    // localStorage.setItem('showCurrentMonth', false);
    localStorage.setItem('showCurrentMonth', true);
    if (keycloakInitialized) {
      // getStoreNamesByStoreId(keycloak.tokenParsed.groupname, result => {
      //   setstoreName(result);
      // })
      if (keycloak.authenticated) {
        // getAllChartDetails();
        setSnackbar(true);

        if (keycloak.tokenParsed.groupname[0] != '') {
          let groups = keycloak.tokenParsed.groupname;
          setUserName(groups);
          let groupArray = [];
          let groupIds = [];
          groups =
            keycloak.realm != 'lupient' &&
            keycloak.realm != 'billknightag' &&
            keycloak.realm != 'karlflammerford'
              ? groups.sort()
              : groups;
          groups.map(value => {
            // groupArray.push(value.split('/')[1]);
            // groupIds.push(value.split('/')[2]);
            if (value.split('/').slice(1, -1).length > 1) {
              groupArray.push(
                value
                  .split('/')
                  .slice(1, -1)
                  .join('/')
              );
            } else {
              groupArray.push(value.split('/')[1]);
            }
            groupIds.push(value.split('/').pop());
          });

          setGroups(groups);
          localStorage.setItem('allPermittedStores', JSON.stringify(groupIds));
          let stores = lodash.uniqBy(groupArray);

          let storeData = [];
          //if (keycloak.realm == 'firstteamag') {
          getVersionFlags(result => {
            if (result.data.statelessCcPhysicalRwGetVersionFlags) {
              let data =
                result.data.statelessCcPhysicalRwGetVersionFlags.getVerFlags;
              if (data[0].margument == 'VRF') {
                dispatch(setVersionFlag(data[0].mvalue));
                ReactSession.set('versionFlag', data[0].mvalue);
                localStorage.setItem('versionFlag', data[0].mvalue);
                if (
                  session.versionFlag == 'TRUE' ||
                  session.versionFlag == true ||
                  (localStorage.getItem('versionFlag') &&
                    localStorage.getItem('versionFlag') == 'TRUE')
                ) {
                  getLastThreeYears(result => {
                    const startMonth = result[0];
                    const endMonth = result[35];
                    const datestr = endMonth.split('-');
                    const month = datestr[1];
                    const year = datestr[0];
                    const date = new Date(year, month, 0).getDate();

                    let dateRange = [startMonth + '-01', endMonth + '-' + date];

                    localStorage.setItem('3Years', dateRange);
                  });
                }
              }
              if (data[1].margument == 'MNF') {
                dispatch(setMenuFlag(data[1].mvalue));
                ReactSession.set('menuFlag', data[1].mvalue);
              }
              if (data[2].margument == 'MPF') {
                dispatch(setMpiFlag(data[2].mvalue));
                ReactSession.set('mapiFlag', data[2].mvalue);
              }
            }
          });
          getAllStoreNames(groupIds, result => {
            setStoreLoaded(true);
            if (result.length > 0) {
              result.map((val, i) => {
                storeData.push(stores[0].split('-')[0] + '-' + val.storeName);
              });
              console.log('ccc==', result[0], storeData);
              setstoreSlected(
                storeData[0].split('-').length > 3
                  ? storeData[0].replace(/.*?-/, '')
                  : storeData[0].split('-').length > 2
                  ? storeData[0].split('-')[
                      storeData[0].split('-').length - 2
                    ] +
                    '-' +
                    storeData[0].split('-')[storeData[0].split('-').length - 1]
                  : storeData[0].split('-')[1]
              );
              //stores = result;
              localStorage.setItem('storeGroup', stores[0].split('-')[0]);
              localStorage.setItem('storeSelected', result[0].storeName);
              // localStorage.setItem(
              //   'selectedStoreId',
              //   JSON.stringify(selectedStores)
              // );
              let storeArr = [result[0].storeId];
              localStorage.setItem('selectedStoreId', JSON.stringify(storeArr));
              localStorage.setItem('selectedStoreName', result[0].storeName);
              getAllChartDetails();
              checkInternalToggle();
              getLastThirteenMonths(result => {
                const startMonth = result[0];
                const endMonth = result[12];
                const datestr = endMonth.split('-');
                const month = datestr[1];
                const year = datestr[0];
                const date = new Date(year, month, 0).getDate();

                let dateRange = [startMonth + '-01', endMonth + '-' + date];
                let dateRange12Months = [
                  result[1] + '-01',
                  endMonth + '-' + date
                ];
                localStorage.setItem('12Months', dateRange12Months);
                localStorage.setItem('13Months', dateRange);
              });

              // let toggleSet = ['MTD', 'YESDT', 'DBYESDT', 'LWEEK'];
              let toggleSet = ['MTD'];
              toggleSet.map(item => {
                getKpiScoreCardDataStatus(item, result => {
                  if (result.data.statelessDbdKpiScorecardGetDataStatus) {
                    let data =
                      result.data.statelessDbdKpiScorecardGetDataStatus
                        .bigFloat;
                    if (item == 'MTD') {
                      localStorage.setItem(
                        'kpiDataStatus',
                        result.data.statelessDbdKpiScorecardGetDataStatus
                          .bigFloat
                      );
                    }
                    // if (item == 'YESDT') {
                    //   localStorage.setItem(
                    //     'kpiDataStatusYesdt',
                    //     result.data.statelessDbdKpiScorecardGetDataStatus
                    //       .bigFloat
                    //   );
                    // }
                    // if (item == 'DBYESDT') {
                    //   localStorage.setItem(
                    //     'kpiDataStatusDbYesdt',
                    //     result.data.statelessDbdKpiScorecardGetDataStatus
                    //       .bigFloat
                    //   );
                    // }
                    // if (item == 'LWEEK') {
                    //   localStorage.setItem(
                    //     'kpiDataStatusDbLweek',
                    //     result.data.statelessDbdKpiScorecardGetDataStatus
                    //       .bigFloat
                    //   );
                    // }
                    dispatch(setKpiToggle(data == 1 ? 'MTD' : 'LMONTH'));
                    dispatch(setKpiHomeToggle(data == 1 ? 'MTD' : 'LMONTH'));
                  }
                });
              });
              console.log('storeData=', result[0].storeName);
              setstoreName(storeData);
            } else {
              // localStorage.setItem('partsMatrixTypes', JSON.stringify(data));
            }
          });
          // } else {
          //   setstoreSlected(
          //     stores[0].split('-').length > 2
          //       ? stores[0].split('-')[stores[0].split('-').length - 2] +
          //           '-' +
          //           stores[0].split('-')[stores[0].split('-').length - 1]
          //       : stores[0].split('-')[1]
          //   );
          //   setStoreLoaded(false);
          //   setstoreName(stores);
          // }

          let selectedStores = lodash.uniqBy(groupIds);

          localStorage.setItem('storeGroup', stores[0].split('-')[0]);
          localStorage.setItem('storeSelected', stores[0].split('-')[1]);
          localStorage.setItem(
            'selectedStoreId',
            JSON.stringify(selectedStores[0].split())
          );
          // selectedStores.push(groupIds[0], groupIds[2])
          localStorage.setItem('realm', keycloak.realm);
        }

        setTimeout(() => {
          setSnackbar(false);
          // history.push('/CPOverview');
        }, 200);
      }
    } else {
      const spanAttribute = {
        pageUrl: '',
        origin: '',
        event: 'Page Loader',
        value: '',
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Login Page Loader', spanAttribute);
    }

    const errors = validate(formState.values, schema);

    setFormState(prevFormState => ({
      ...prevFormState,
      isValid: !errors,
      errors: errors || {}
    }));
  }, [formState.values, keycloak.authenticated, keycloakInitialized, keycloak]);

  return (
    <>
      {keycloakInitialized == true ? (
        <>
          <Typography gutterBottom variant="h3" id="auth-status">
            {keycloakInitialized == true && keycloak.authenticated == true
              ? ''
              : ' Sign in'}
          </Typography>
          <form
            {...rest}
            className={clsx(classes.root, className)}
            onSubmit={handleSubmit}
            id="auth-form"
          >
            {keycloakInitialized == true &&
            keycloak.authenticated == true &&
            keycloak.tokenParsed.groupname.length > 0 ? (
              <>
                <InputLabel
                  style={{ width: '100%', marginBottom: 16, fontSize: 16 }}
                  id="store"
                >
                  <b>Select Your Store</b>
                </InputLabel>

                {storeLoaded ? (
                  <Select
                    style={{ width: '100%' }}
                    labelId="store-selection"
                    id="store-select"
                    value={storeSlected}
                    onChange={handleChange}
                    input={<Input id="select-multiple-chip" />}
                    MenuProps={MenuProps}
                  >
                    {keycloak.tokenParsed.groupname.length > 0 &&
                      storeName.map((val, i) => {
                        const displayValue =
                          val.split('-').length > 3
                            ? val.replace(/.*?-/, '')
                            : val.split('-').length > 2
                            ? val.split('-')[val.split('-').length - 2] +
                              '-' +
                              val.split('-')[val.split('-').length - 1]
                            : val.split('-')[1];

                        return (
                          <MenuItem
                            key={i}
                            value={displayValue}
                            id={`store-item-${i}`}
                          >
                            {displayValue}
                          </MenuItem>
                        );
                      })}
                    {keycloak.tokenParsed.groupname.length > 1 &&
                      (session.versionFlag == 'TRUE' ||
                        (localStorage.getItem('versionFlag') &&
                          localStorage.getItem('versionFlag') == 'TRUE')) && (
                        <MenuItem
                          key="All Stores"
                          value="All Stores"
                          onClick={handleChange}
                          selected={true}
                          id="all-stores-item"
                        >
                          All Stores
                        </MenuItem>
                      )}
                  </Select>
                ) : (
                  dataLoader()
                )}
              </>
            ) : (
              <>
                {keycloakInitialized == true &&
                keycloak.authenticated == true ? (
                  <>
                    <InputLabel
                      style={{ width: '100%', marginBottom: 16 }}
                      id="store"
                    >
                      Select the Store to continue
                    </InputLabel>
                    <Select
                      style={{ width: '100%' }}
                      labelId="store-selection"
                      id="store-select"
                      value={storeSlected}
                      onChange={handleSubmit}
                    >
                      {keycloak.tokenParsed.groupname.length > 0 &&
                        keycloak.tokenParsed.groupname.map((val, i) => (
                          <MenuItem
                            key={keycloak.tokenParsed.groupname[i]}
                            value={keycloak.tokenParsed.groupname[i]}
                            id={`groupname-item-${i}`}
                          >
                            {val.substring(val.indexOf('/') + 1)}
                          </MenuItem>
                        ))}
                    </Select>
                  </>
                ) : null}
              </>
            )}

            <Button
              id="login"
              className={classes.submitButton}
              color="secondary"
              size="large"
              type="submit"
              disabled={
                storeLoaded
                  ? false
                  : keycloakInitialized == true &&
                    keycloak.authenticated == true
                  ? true
                  : false
              }
              variant="contained"
            >
              {keycloakInitialized == true && keycloak.authenticated == true
                ? 'View Dashboard'
                : ' Sign in to Dashboard'}
            </Button>

            {/* <SuccessSnackbar
            onClose={() => setSnackbar(false)}
            open={openSnackBar}
          /> */}
          </form>
        </>
      ) : (
        <LoaderSkeleton id="loader-skeleton"></LoaderSkeleton>
      )}
    </>
  );
}

LoginForm.propTypes = {
  className: PropTypes.string
};

export default withKeycloak(LoginForm);
