import React, { useEffect, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { makeStyles } from '@material-ui/styles';
import {
  Card,
  CardContent,
  CardMedia,
  Typography,
  Divider,
  Grid,
  Avatar
} from '@material-ui/core';
import LockIcon from '@material-ui/icons/Lock';
import Page from 'src/components/Page';
import gradients from 'src/utils/gradients';
import LoginForm from './LoginForm';
import { withKeycloak } from '@react-keycloak/web';
import { version, launchDate, versionDemo } from 'src/appversion.json';
import Marquee from 'react-fast-marquee';
import { GET_HOME_MESSAGE } from 'src/graphql/queries';
import makeApolloClientPostgresAnonymous from 'src/utils/apolloClientPostgresAnonymous';
import { traceSpan } from 'src/utils/OTTTracing';
import { getVersionFlagBeforeLogin } from 'src/utils/hasuraServices';
import { setVersionFlag, setMpiFlag, setMenuFlag } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { ReactSession } from 'react-client-session';

const useStyles = makeStyles(theme => ({
  root: {
    minHeight: '66vh', // Ensure the container fills the viewport heights
    display: 'flex',
    flexDirection: 'column', // Arrange children vertically
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(6, 2)
  },
  card: {
    width: '100%', // Set width to 100% to fit the container
    maxWidth: theme.breakpoints.values.md,
    overflow: 'visible',
    display: 'flex',
    position: 'relative',
    [theme.breakpoints.down('md')]: {
      flexDirection: 'column'
    }
  },
  content: {
    width: '100%', // Set width to 100% to fill the container
    padding: theme.spacing(8, 4, 3, 4)
  },
  media: {
    width: '100%', // Set width to 100% to fill the container
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
    padding: theme.spacing(3),
    color: theme.palette.common.white,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    [theme.breakpoints.down('md')]: {
      display: 'none'
    }
  },
  icon: {
    backgroundImage: gradients.green,
    color: theme.palette.common.white,
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(1),
    position: 'absolute',
    top: -32,
    left: theme.spacing(3),
    height: 64,
    width: 64,
    fontSize: 32
  },
  loginForm: {
    marginTop: theme.spacing(3)
  },
  divider: {
    margin: theme.spacing(2, 0)
  },
  person: {
    marginTop: theme.spacing(2),
    display: 'flex'
  },
  launch: {
    marginTop: theme.spacing(2),
    display: 'flex',
    marginLeft: 'auto'
  },
  avatar: {
    marginRight: theme.spacing(2)
  },
  note: {
    marginTop: 60,
    display: 'grid',
    justifyContent: 'center',
    color: 'red',
    textAlign: 'center'
  },

  // Media query for screens with maximum width 1280px
  '@media (max-width: 1280px)': {
    card: {
      flexDirection: 'column'
    },
    media: {
      display: 'block'
    }
  }
}));

function Login({ keycloak, keycloakInitialized }) {
  const dispatch = useDispatch();

  const classes = useStyles();
  const token = localStorage.getItem('tokenexpired');
  const [message, setMessage] = useState([]);
  const [versionFlags, setVersionFlags] = useState([]);
  const [currentVersion, setCurrentVersion] = useState('');
  const session = useSelector(state => state.session);

  const delete_cookie = function(name) {
    document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/';
  };
  if (token) {
    localStorage.removeItem('tokenexpired');
    var logoutOptions = { redirectUri: keycloak.redirectUri };

    keycloak
      .logout(logoutOptions)
      .then(success => {
        localStorage.removeItem('selectedStoreId');
        localStorage.clear();
        localStorage.setItem('showCurrentMonth', false);
        delete_cookie('otContext');
        delete_cookie('userIP');
        delete_cookie('userLocation');
        localStorage.removeItem('selectedTech');
        localStorage.removeItem('versionFlag');
        /*  setTimeout(() => {
        history.entries = [];
        // history.push() auto increments from the current index
        history.index = -1;
         history.push('/login');
        history.push('/auth/login');
        localStorage.setItem('isAuthenticated', false);
      }, 200);*/
        localStorage.setItem('isAuthenticated', false);
      })
      .catch(error => {
        console.log('--> log: logout error ', error);
      });
  }
  const clearCacheData = () => {
    // localStorage.clear();
    localStorage.removeItem('closedDate');
    localStorage.removeItem('openDate');
    localStorage.removeItem('allPermittedStores');
    Object.keys(localStorage).forEach(function(key, i) {
      if (
        localStorage.key(i) != null &&
        localStorage.key(i) != 'isAuthenticated' &&
        localStorage.key(i) != 'realm' &&
        localStorage.key(i) != 'versionFlag' &&
        localStorage.key(i) != 'keycloakToken' &&
        localStorage.key(i) != 'userID' &&
        localStorage.key(i) != 'provenance' &&
        localStorage.key(i) != 'selectedStoreId' &&
        !localStorage.key(i).includes('kc-callback')
      ) {
        var item = localStorage.key(i);

        localStorage.removeItem(item);
      }
    });
  };
  useEffect(() => {
    clearCacheData();
  }, [currentVersion]);
  useEffect(() => {
    const start = new Date();
    let cVersion = '';
    getVersionFlagBeforeLogin(result => {
      if (result.data.statefulServiceConfigurationGetConfigVersionFlags) {
        let data =
          result.data.statefulServiceConfigurationGetConfigVersionFlags.nodes;
        dispatch(setVersionFlag(data[0].mvalue));
        ReactSession.set('versionFlag', data[0].mvalue);
        cVersion = data[0].mvalue;
        console.log('mmm===111', localStorage.getItem('versionFlag'), cVersion);
        localStorage.setItem('versionFlag', data[0].mvalue);
        setCurrentVersion(data[0].mvalue);
        if (cVersion) {
          makeApolloClientPostgresAnonymous
            .query({
              query: GET_HOME_MESSAGE,
              fetchPolicy: 'no-cache'
            })
            .then(result => {
              const spanAttribute = {
                pageUrl: '/login',
                origin: '',
                event: 'Menu Load',
                is_from: 'GET_HOME_MESSAGE',
                value: new Date() - start,
                provenance: localStorage.getItem('provenance')
              };
              traceSpan('Menu Load', spanAttribute);
              if (
                result.data.statefulServiceConfigurationGetHomemessage.nodes
                  .length > 0
              ) {
                let msg =
                  result.data.statefulServiceConfigurationGetHomemessage
                    .nodes[0];
                localStorage.getItem('versionFlag') == 'TRUE'
                  ? setMessage(msg.split('|'))
                  : setMessage([]);
              }
            })
            .catch(error => {
              if (error.graphQLErrors) {
                error.graphQLErrors.map(({ message, locations, path }) =>
                  console.log(
                    `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
                  )
                );
              }
              return error;
            });
        }
      }
    });
  }, []);
  return (
    <>
      {message && (
        <Grid
          className={classes.note}
          dangerouslySetInnerHTML={{ __html: message }}
        >
          {/* {message.map((msg, i) => (
          <Typography color="inherit" variant="subtitle1">
            {msg}
          </Typography>
        ))} */}
        </Grid>
      )}

      <div className={classes.root}>
        <Card className={classes.card}>
          <CardContent className={classes.content}>
            <LockIcon className={classes.icon} />
            <LoginForm className={classes.loginForm} />
            <Divider
              className={classes.divider}
              style={{
                display:
                  keycloakInitialized === true &&
                  keycloak.authenticated === false
                    ? 'block'
                    : 'none'
              }}
            />
            {/* Add conditionally rendered Link here */}
          </CardContent>
          <CardMedia
            className={classes.media}
            image="/images/auth.png"
            title="Cover"
            id="card-media"
          >
            <div className={classes.person} id="person-container">
              {/* <Avatar
              alt="Person"
              className={classes.avatar}
              src="/images/avatars/avatar_2.png"
            /> */}
              <div>
                <Typography
                  color="inherit"
                  variant="body1"
                  id="dashboard-version"
                >
                  {version ? 'Current Dashboard' : ''}
                </Typography>
                &nbsp;
                {/* <Typography color="inherit" variant="body2" > */}
                <Typography color="inherit" variant="body1" id="version-info">
                  {currentVersion
                    ? currentVersion == 'TRUE'
                      ? 'Version ' + version
                      : 'Version ' + versionDemo
                    : ''}
                  &nbsp;
                </Typography>
              </div>
            </div>
            <div className={classes.launch} id="launch-container">
              {/* <Avatar
              alt="Person"
              className={classes.avatar}
              src="/images/avatars/avatar_2.png"
            /> */}
            </div>
            <div className={classes.launch} id="launch-placeholder"></div>
          </CardMedia>
        </Card>
      </div>
    </>
  );
}

export default withKeycloak(Login);
