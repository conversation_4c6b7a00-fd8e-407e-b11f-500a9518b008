import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  Select,
  Button,
  MenuItem,
  Tooltip,
  InputLabel
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React, { useMemo, useEffect, useState, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { GetDailyUpdateStatus } from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import { getLast13Months } from 'src/utils/Utils';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import { addDays } from 'date-fns';
import { withKeycloak } from '@react-keycloak/web';
import { setHistoryStatus } from 'src/actions';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import TooltipRendererMarkdown from 'src/views/AnalyzeData/Component/TooltipRendererMarkdown';
import clsx from 'clsx';
import Link from '@material-ui/core/Link';
import ExportIcon from '@material-ui/icons/GetApp';
import RestoreIcon from '@material-ui/icons/Restore';
import { Alert } from '@material-ui/lab';
var Dealer = process.env.REACT_APP_DEALER;
var defaultFilterParams = { readOnly: true };
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    border: 'thin solid #968989 !important',
    height: '35px !important'
  },
  reactDaterangePicker: {
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px'
  },
  back: {
    marginRight: 30,
    float: 'right',
    marginTop: 31
  }
});
function MyRenderer(params) {
  return (
    <span className="my-renderer">
      <img
        src="https://d1yk6z6emsz7qy.cloudfront.net/static/images/loading.gif"
        className="my-spinner"
      />
      {params.value}
    </span>
  );
}
function EditHistoryOld(props) {
  const { classes } = props;
  const dispatch = useDispatch();
  var initialQueryMonth = props.months[0].monthYear;
  const [tabSelection, setTabSelection] = useState(0);
  const [queryMonth, setQueryMonth] = useState(initialQueryMonth);
  const [value, setValue] = useState(new Date());
  const [rawGridApi, setRawGridApi] = useState({});
  const [gridColumnApi, setGridcolumnApi] = useState({});
  const [isLoading, setIsloading] = useState(false);
  const [rowData, setRowData] = useState();

  const [startDate, setStartDate] = useState(
    moment(new Date(new Date().getFullYear(), new Date().getMonth(), 1)).format(
      'YYYY-MM-DD'
    )
  );
  const [endDate, setEndDate] = useState(
    moment(new Date())
      .clone()
      .format('YYYY-MM-DD')
  );

  const [statusDate, setStatusDate] = useState(new Date());
  const [optionName, setOptionName] = useState('Advisor');
  const [columnName, setColumnName] = useState('Advisor');
  const [gridApi, setGridApi] = useState({});
  const session = useSelector(state => state.session);
  const [headerHeight, setHeaderHeight] = useState(45);
  const [filterDisabled, setFilterDisabled] = useState(true);

  const dateChange = e => {
    setValue(e);
    var startDate = moment(e).format('YYYY-MM-DD');
    setStatusDate(startDate);
    // getAgGridData(startDate,'Paytype');
    console.log('date=', e, '==', startDate);
    // rawGridApi.onFilterChanged();
  };
  const optionChange = e => {
    console.log('dateeeeeeee=', e.target.value);
    dispatch(setHistoryStatus(e.target.value));
    setOptionName(e.target.value);
    setFilterDisabled(false);
    // getAgGridData(startDate,'Paytype');
  };
  const filterData = () => {
    if (optionName == 'Opcode') {
      setHeaderHeight(75);
    } else {
      setHeaderHeight(45);
    }
    setColumnName(optionName);
    var startDate = moment(startDate).format('YYYY-MM-DD');
    var endDate = moment(endDate).format('YYYY-MM-DD');
    getAgGridData(startDate, endDate, optionName);
  };
  const columnDefsAdvisor = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'logdate',
        width: 185,
        minWidth: 185,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide:
          session.historyStatus == 'Advisor' ||
          session.historyStatus == 'Paytype'
            ? false
            : true,
        filter: true,
        // filterParams: {
        //   readOnly: true
        // },
        filterParams: defaultFilterParams
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true,
        filter: true
        // filter: 'agSetColumnFilter',
        // filterParams: {
        //   readOnly: true
        // },
      },
      {
        headerName: 'User Role',
        chartDataType: 'category',
        field: 'userRole',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true,
        filter: true
      },
      // {
      //   headerName: 'Service Advisor',
      //   chartDataType: 'category',
      //   field: 'advisorid',
      //   width: 100,
      //   minWidth: 100,
      //   cellClass: 'textAlign',
      //   suppressMenu: true,
      //   unSortIcon: true,
      //   suppressMovable: true,
      //   hide: session.historyStatus == 'Advisor' ? false : true
      // },
      {
        headerName: 'Service Advisor',
        chartDataType: 'category',
        field: 'advisorName',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true,
        filter: true
      },
      {
        headerName: 'Old Nickname',
        chartDataType: 'category',
        field: 'oldNickname',
        width: 110,
        minWidth: 110,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true,
        filter: true
      },
      {
        headerName: 'New Nickname',
        chartDataType: 'category',
        field: 'newNickname',
        width: 110,
        minWidth: 110,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true,
        filter: true
      },
      {
        headerName: 'Old Status',
        chartDataType: 'category',
        field: 'oldStatus',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true,
        filter: true
      },
      {
        headerName: 'New Status',
        chartDataType: 'category',
        field: 'newStatus',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true,
        filter: true
      }
    ],
    []
  );
  const columnDefsPaytype = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'logdate',
        width: 160,
        minWidth: 160,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        width: 180,
        minWidth: 180,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true
      },
      {
        headerName: 'User Role',
        chartDataType: 'category',
        field: 'userRole',
        width: 130,
        minWidth: 130,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true
      },
      {
        headerName: 'Pay Type',
        chartDataType: 'category',
        field: 'paytype',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true
      },
      {
        headerName: 'Old Paytype Group',
        chartDataType: 'category',
        field: 'oldPaytypecode',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true
      },
      {
        headerName: 'New Paytype Group',
        chartDataType: 'category',
        field: 'newPaytypecode',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true
      },
      {
        headerName: 'Old Department',
        chartDataType: 'category',
        field: 'oldPaytypedept',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Department',
        chartDataType: 'category',
        field: 'newPaytypedept',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      }
    ],
    []
  );
  const columnDefsTechnician = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'logdate',
        width: 180,
        minWidth: 180,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        editable: false,
        suppressMovable: true,
        hide: false
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'User Role',
        chartDataType: 'category',
        field: 'userRole',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      // {
      //   headerName: 'Tech Number',
      //   chartDataType: 'category',
      //   field: 'techid',
      //   width: 120,
      //   minWidth: 120,
      //   cellClass: 'textAlign',
      //   suppressMenu: true,
      //   unSortIcon: true,
      // editable: false,
      //   suppressMovable: true,
      //   hide: session.historyStatus == 'Advisor' ? false : true
      // },
      {
        headerName: 'Technician Name',
        chartDataType: 'category',
        field: 'techname',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old Nickname',
        chartDataType: 'category',
        field: 'oldNickname',
        width: 110,
        minWidth: 110,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Nickname',
        chartDataType: 'category',
        field: 'newNickname',
        width: 110,
        minWidth: 110,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },

      {
        headerName: 'Old Status',
        chartDataType: 'category',
        field: 'oldStatus',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Status',
        chartDataType: 'category',
        field: 'newStatus',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      }
    ],
    []
  );
  const columnDefsOpcode = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'logdate',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: false
        // filter: false
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
        // filter: false
      },
      {
        headerName: 'User Role',
        chartDataType: 'category',
        field: 'userRole',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Opcode',
        chartDataType: 'category',
        field: 'laboropcode',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
        // filter: false
      },
      {
        headerName: 'Old Opcategory',
        chartDataType: 'category',
        field: 'oldOpcategory',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Opcategory',
        chartDataType: 'category',
        field: 'newOpcategory',
        width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Log Status',
        chartDataType: 'category',
        field: 'logStatus',
        width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old Category',
        chartDataType: 'series',
        width: 120,
        minWidth: 120,
        field: 'oldcategorized',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'New Category',
        chartDataType: 'series',
        width: 120,
        minWidth: 120,
        field: 'newcategorized',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'Old MPI Item',
        chartDataType: 'series',
        width: 100,
        minWidth: 100,
        field: 'oldmpiItem',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'New MPI Item',
        chartDataType: 'series',
        width: 100,
        minWidth: 100,
        field: 'newmpiItem',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'Old Menu Sales',
        chartDataType: 'series',
        width: 100,
        minWidth: 100,
        field: 'oldmenuSales',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 'Y' ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'New Menu Sales',
        chartDataType: 'series',
        width: 100,
        minWidth: 100,
        field: 'newmenuSales',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 'Y' ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'Old Maintenance Plan',
        chartDataType: 'series',
        width: 120,
        minWidth: 120,
        field: 'oldmaintenancePlan',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'New Maintenance Plan',
        chartDataType: 'series',
        width: 120,
        minWidth: 120,
        field: 'newmaintenancePlan',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'Old Grid Excluded',
        chartDataType: 'series',
        width: 100,
        minWidth: 100,
        field: 'oldgridexcluded',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      },
      {
        headerName: 'New Grid Excluded',
        chartDataType: 'series',
        width: 100,
        minWidth: 100,
        field: 'newgridexcluded',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } />`;
        }
      }
    ],
    []
  );
  const columnDefsChartMaster = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'statusdate',
        // width: 120,
        minWidth: 60,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: false,
        flex: 1,
        valueFormatter: formatCellValueDate
        // filter: false
      },
      {
        headerName: 'Time(EST)',
        // chartDataType: 'category',
        field: 'statustime',
        // width: 200,
        minWidth: 60,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
        filter: true,
        flex: 1
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        tooltipField: 'userName',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true,
        // filter: false
      },
      {
        headerName: 'Role',
        chartDataType: 'category',
        field: 'userRole',
        // width: 120,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old Chart Name ',
        chartDataType: 'category',
        field: 'oldChartname',
        // width: 100,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        tooltipField: 'oldChartname'
        // hide: session.historyStatus == 'Advisor' ? false : true,
        // filter: false
      },
      {
        headerName: 'New Chart Name',
        chartDataType: 'category',
        field: 'newChartname',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        tooltipField: 'newChartname'
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old Chart Description',
        chartDataType: 'category',
        field: 'oldChartdescription',
        // width: 100,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        tooltipField: 'oldChartdescription'
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Chart Description',
        chartDataType: 'category',
        field: 'newChartdescription',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        tooltipField: 'newChartdescription'
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old Markdown Desc',
        chartDataType: 'category',
        field: 'oldmarkdowndesc',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        // tooltipField: 'oldmarkdowndesc',
        cellRendererFramework: TooltipRendererMarkdown
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Markdown Desc',
        chartDataType: 'category',
        field: 'newmarkdowndesc',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        // tooltipField: 'newmarkdowndesc',
        cellRendererFramework: TooltipRendererMarkdown
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old Status',
        chartDataType: 'series',
        // width: 100,
        minWidth: 100,
        field: 'oldStatus',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        flex: 1,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } disabled/>`;
        }
      }
    ],
    []
  );
  const columnDefsStoreSettings = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'statusdate',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: false,
        flex: 1,
        valueFormatter: formatCellValueDate
      },
      {
        headerName: 'Time(EST)',
        // chartDataType: 'category',
        field: 'statustime',
        // width: 200,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
        filter: true,
        flex: 1
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        tooltipField: 'userName',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'Role',
        chartDataType: 'category',
        field: 'userRole',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'KPI Number',
        chartDataType: 'category',
        field: 'kpiNo',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'Goal Name',
        chartDataType: 'category',
        field: 'goalName',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'Old Goal Value',
        chartDataType: 'series',
        // width: 100,
        minWidth: 100,
        field: 'oldGoalvalue',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        flex: 1
      },
      {
        headerName: 'New Goal Value',
        chartDataType: 'series',
        // width: 100,
        minWidth: 100,
        field: 'newGoalvalue',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        flex: 1
      }
    ],
    []
  );
  const columnDefsStoreGoal = useMemo(() => [], []);
  const columnDefsAdvisorGoal = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'statusdate',
        // width: 120,
        minWidth: 60,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: false,
        flex: 1,
        valueFormatter: formatCellValueDate
      },
      {
        headerName: 'Time(EST)',
        // chartDataType: 'category',
        field: 'statustime',
        // width: 200,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
        filter: true,
        flex: 1
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        tooltipField: 'userName',
        // width: 120,
        minWidth: 180,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'Role',
        chartDataType: 'category',
        field: 'userRole',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'KPI Number',
        chartDataType: 'category',
        field: 'kpiNo',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'Goal Name',
        chartDataType: 'category',
        field: 'goalName',
        // width: 120,
        minWidth: 150,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'Advisor',
        chartDataType: 'category',
        field: 'advisorName',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
      },
      {
        headerName: 'Old Goal Value',
        chartDataType: 'series',
        // width: 100,
        minWidth: 100,
        field: 'oldGoalvalue',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        flex: 1
      },
      {
        headerName: 'New Goal Value',
        chartDataType: 'series',
        // width: 100,
        minWidth: 100,
        field: 'newGoalvalue',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        flex: 1
      }
    ],
    []
  );
  const columnDefsGoalSettings = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'statusdate',
        // width: 120,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: false,
        flex: 1,
        valueFormatter: formatCellValueDate
        // filter: false
      },
      {
        headerName: 'Time(EST)',
        // chartDataType: 'category',
        field: 'statustime',
        // width: 200,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
        filter: true,
        flex: 1
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        tooltipField: 'userName',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true,
        // filter: false
      },
      {
        headerName: 'Role',
        chartDataType: 'category',
        field: 'userRole',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Goal Name',
        chartDataType: 'category',
        field: 'goalName',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        tooltipField: 'goalName'
        // hide: session.historyStatus == 'Advisor' ? false : true,
        // filter: false
      },
      {
        headerName: 'Old Goal Value',
        chartDataType: 'category',
        field: 'oldGoalvalue',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Goal Value',
        chartDataType: 'category',
        field: 'newGoalvalue',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      }
    ],
    []
  );
  const columnDefsGoalSettingsAdvisor = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'statusdate',
        // width: 120,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        hide: false,
        flex: 1,
        valueFormatter: formatCellValueDate
        // filter: false
      },
      {
        headerName: 'Time(EST)',
        // chartDataType: 'category',
        field: 'statustime',
        // width: 200,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
        filter: true,
        flex: 1
      },
      {
        headerName: 'User Name',
        chartDataType: 'category',
        field: 'userName',
        tooltipField: 'userName',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true,
        // filter: false
      },
      {
        headerName: 'Role',
        chartDataType: 'category',
        field: 'userRole',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },

      {
        headerName: 'Goal Name',
        chartDataType: 'category',
        field: 'goalName',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        tooltipField: 'goalName'
        // hide: session.historyStatus == 'Advisor' ? false : true,
        // filter: false
      },
      {
        headerName: 'Advisor Name',
        chartDataType: 'category',
        field: 'advisorName',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true,
        // filter: false
      },
      {
        headerName: 'Old Goal Value',
        chartDataType: 'category',
        field: 'oldGoalvalue',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New Goal Value',
        chartDataType: 'category',
        field: 'newGoalvalue',
        // width: 120,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      }
    ],
    []
  );
  const columnDefsFROpcode = useMemo(
    () => [
      {
        headerName: 'Date',
        chartDataType: 'category',
        field: 'statusdate',
        // width: 200,
        minWidth: 20,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
        filter: true,
        flex: 1,
        valueFormatter: formatCellValueDate
      },
      {
        headerName: 'Time(EST)',
        // chartDataType: 'category',
        field: 'statustime',
        // width: 200,
        minWidth: 120,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        // hide: session.historyStatus == 'Advisor' || session.historyStatus == 'Paytype' ? false : true,
        filter: true,
        flex: 1
      },
      {
        headerName: 'Labor Opcode',
        chartDataType: 'category',
        field: 'laboropcode',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old FR Value',
        chartDataType: 'category',
        field: 'oldfrvalue',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New FR Value',
        chartDataType: 'category',
        field: 'newfrvalue',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old FR Date',
        chartDataType: 'category',
        field: 'oldfrdate',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        valueFormatter: formatCellValueDate
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'New FR Date',
        chartDataType: 'category',
        field: 'newfrdate',
        // width: 100,
        minWidth: 100,
        cellClass: 'textAlign',
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        suppressMovable: true,
        flex: 1,
        valueFormatter: formatCellValueDate
        // hide: session.historyStatus == 'Advisor' ? false : true
      },
      {
        headerName: 'Old FR Status',
        chartDataType: 'series',
        // width: 100,
        minWidth: 100,
        field: 'oldfrstatus',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        flex: 1,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } disabled/>`;
        }
      },
      {
        headerName: 'New FR Status',
        chartDataType: 'series',
        // width: 100,
        minWidth: 100,
        field: 'newfrstatus',
        suppressMovable: true,
        suppressMenu: true,
        unSortIcon: true,
        editable: false,
        flex: 1,
        cellRenderer: params => {
          return `<input type='checkbox' ${
            params.value == 1 ? 'checked' : ''
          } disabled/>`;
        }
      }
    ],
    []
  );

  const overlayNoRowsTemplate = useMemo(
    () =>
      '<span style="padding: 10px; margin-top:50px;">No rows to show</span>',
    []
  );

  const defaultColDef = useMemo(() => {
    return {
      enableValue: true,
      sortable: true,
      filter: true,
      resizable: false,
      editable: true,
      flex: 1
      // floatingFilter: true,
    };
  }, []);
  const excelStyles = useMemo(() => {
    return [
      {
        id: 'bigHeader',
        font: {
          size: 25,
          color: 'primary'
        },
        alignment: {
          horizontal: 'Center',
          vertical: 'Center'
        }
      },
      {
        id: 'textAlign',
        alignment: {
          horizontal: 'Left'
        }
      },
      {
        id: 'greenBackground',
        interior: {
          color: '#d9f2d9',
          pattern: 'Solid'
        }
      },
      {
        id: 'header',
        interior: {
          color: '#009900',
          pattern: 'Solid'
        },
        font: {
          bold: true,
          color: '#ffffff'
        }
      }
    ];
  }, []);
  const sideBar = useMemo(
    () => ({
      toolPanels: [
        {
          id: 'columns',
          labelDefault: 'Columns',
          labelKey: 'columns',
          iconKey: 'columns',
          toolPanel: 'agColumnsToolPanel',
          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressColumnExpandAll: false
          }
        },
        {
          id: 'filters',
          labelDefault: 'Filters',
          labelKey: 'filters',
          iconKey: 'filter',
          toolPanel: 'agFiltersToolPanel'
        }
      ],
      defaultToolPanel: 'columns'
    }),
    []
  );
  useEffect(() => {
    console.log(
      'session=',
      session.historyStatus == 'Advisor' ? true : false,
      session.historyStatus
    );
    console.log('queryMonth=', queryMonth);
    var startDate = moment(statusDate).format('YYYY-MM-DD');
    var optionName = 'Paytype';
  }, [session.historyStatus]);
  const onGridReady = useCallback(params => {
    console.log('params=', params.api);
    setGridApi(params);
    setRawGridApi(params.api);
    setGridcolumnApi(params.columnApi);

    var startDate = moment(startDate).format('YYYY-MM-DD');
    var endDate = moment(endDate).format('YYYY-MM-DD');
    console.log('startdate=', startDate, endDate);
    getAgGridData(startDate, endDate, optionName);
  }, []);

  const getAgGridData = (startDate, endDate, optionName) => {
    setIsloading(true);
    var timezoneOffset = '+05:30';
    GetDailyUpdateStatus(
      startDate,
      endDate,
      optionName,
      timezoneOffset,
      result => {
        console.log('data===value', result);
        setIsloading(false);
        if (result.data.statelessCcPhysicalRwGetDailyUpdateStatus.nodes) {
          console.log(
            'data===',
            result.data.statelessCcPhysicalRwGetDailyUpdateStatus.nodes
          );
          var resultArr =
            result.data.statelessCcPhysicalRwGetDailyUpdateStatus.nodes;
          setRowData(resultArr);
        }
      }
    );
  };
  const handleEventCallback = (event, picker) => {
    console.log('picker===', picker);
  };
  const resetReportGrid = () => {
    gridColumnApi.resetColumnState();
    gridApi.setSortModel(null);
    gridApi.setFilterModel(null);
    gridApi.redrawRows();
  };
  const handleCallback = (event, picker) => {
    setFilterDisabled(false);
    setValue(
      picker.startDate.format('MM/DD/YY') +
        ' - ' +
        picker.endDate.format('MM/DD/YY')
    );
  };
  const formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(new Date(params.value)).format('MM/DD/YY');
    }
  };
  const onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'EditHistory',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Edit History' },
            mergeAcross: 3
          }
        ]
      ]
    };

    rawGridApi.exportDataAsExcel(params);
  };
  console.log(
    'result=',
    optionName == 'Advisor' ? 1 : optionName == 'Paytype' ? 2 : 3
  );
  return (
    <div>
      <Paper square style={{ margin: 8, marginTop: '20px' }}>
        <Tabs
          value={tabSelection}
          variant="fullWidth"
          indicatorColor="secondary"
          textColor="secondary"
          aria-label="icon label tabs example"
          style={{ pointerEvents: 'none' }}
        >
          <Tab
            style={{
              textTransform: 'none',
              paddingRight: 182,
              backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
              border:
                Dealer === 'Armatus'
                  ? '1px solid #003d6b'
                  : '1px solid #C2185B',
              color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
            }}
            label={<div>Edit History</div>}
            value="one"
          />
        </Tabs>
      </Paper>
      <Paper square style={{ margin: '0px 8px' }}>
        <Grid container spacing={12} style={{ alignItems: 'center' }}>
          <Grid
            item
            xs={8}
            style={{ padding: '5px', display: 'flex', alignItems: 'center' }}
          >
            <FormControl
              variant="outlined"
              margin="dense"
              className={clsx(classes.formControl, 'input-container')}
            >
              <DateRangePicker
                initialSettings={{
                  // maxDate: {
                  //   date: new Date(),
                  // },
                  // minDate:{
                  //   date: (this.props.selectedDates[1])
                  // },
                  locale: {
                    format: 'MM/DD/YY',
                    separator: ' - '
                  },
                  ranges: {
                    Today: [moment().toDate(), moment().toDate()],
                    Yesterday: [
                      moment()
                        .subtract(1, 'days')
                        .toDate(),
                      moment()
                        .subtract(1, 'days')
                        .toDate()
                    ],
                    'Last 7 Days': [
                      moment()
                        .subtract(6, 'days')
                        .toDate(),
                      moment().toDate()
                    ],
                    'Last 30 Days': [
                      moment()
                        .subtract(29, 'days')
                        .toDate(),
                      moment().toDate()
                    ],
                    'This Month': [
                      moment()
                        .startOf('month')
                        .toDate(),
                      moment()
                        .endOf('month')
                        .toDate()
                    ],
                    'Last Month': [
                      moment()
                        .subtract(1, 'month')
                        .startOf('month')
                        .toDate(),
                      moment()
                        .subtract(1, 'month')
                        .endOf('month')
                        .toDate()
                    ]
                  },
                  maxDate: moment().toDate(),
                  alwaysShowCalendars: true,
                  applyClass: clsx(classes.calButton, 'apply-btn'),
                  cancelClass: clsx(classes.calButton, 'apply-btn')

                  // startDate: '01/01/23',
                  // endDate: '03/01/23',
                  //showDropdowns: true
                }}
                onEvent={handleEventCallback}
                onApply={handleCallback}
              >
                <input
                  type="text"
                  className="datepicker"
                  id="picker"
                  name="picker"
                  aria-labelledby="label-picker"
                />
                {/* <TextField
                  id="outlined-basic"
                  label="Select Date"
                  size="small"
                  //onChange={}
                  value={value}
                  variant="outlined"
                /> */}
              </DateRangePicker>
              <label class="labelpicker" for="picker" id="label-picker">
                <div class="textpicker">Select Date</div>
              </label>
            </FormControl>
            <FormControl
              variant="outlined"
              margin="dense"
              className={classes.formControl}
            >
              <InputLabel
                htmlFor="outlined-age-native-simple"
                margin="dense"
                style={{
                  paddingRight: 4,
                  paddingLeft: 4,
                  marginTop: 1,
                  marginLeft: -4,
                  fontWeight: 'bold',
                  backgroundColor: '#fff'
                }}
              >
                History For
              </InputLabel>
              {/* <Typography style={{ fontSize: 12 }} sx={{ mt: 2, mb: 1 }}>
                History For
              </Typography> */}
              <Select
                MenuProps={{
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  },
                  getContentAnchorEl: null
                }}
                variant="outlined"
                label="Group By"
                name="group-by-type"
                value={optionName}
                className={classes.monthSelector}
                onChange={optionChange}
                id="advisor-dropdown"
                // sx={{
                //   width: 100,
                //   height: 40,
                //   marginRight: 15,
                //   border: "1px solid yellow",
                //   color: "#fff",
                //   "& .MuiSvgIcon-root MuiSelect-icon MuiSelect-iconOutlined": {
                //       color: "red",
                //   },
                // }}
              >
                <MenuItem value={'Chart'}>Chart Master</MenuItem>
                <MenuItem value={'fixedrate_opcode'}>
                  Fixed Rate Opcode
                </MenuItem>
                <MenuItem value={'advisor_goal'}>KPI Goals - Advisor</MenuItem>
                <MenuItem value={'store_goal'}>KPI Goals - Store</MenuItem>
                <MenuItem value={'GoalSettingsAdvisor'}>
                  Opportunity Goals - Advisor
                </MenuItem>
                <MenuItem value={'GoalSettings'}>
                  Opportunity Goals - Store
                </MenuItem>
                <MenuItem value={'Opcode'}>Opcode</MenuItem>
                <MenuItem value={'Paytype'}>Pay Type</MenuItem>
                <MenuItem value={'Advisor'}>Service Advisor</MenuItem>
                <MenuItem value={'Store_settings'}>Store Settings</MenuItem>
                <MenuItem value={'Technician'}>Technician</MenuItem>
              </Select>
            </FormControl>
            <Button
              title="Apply"
              className={clsx(classes.apply, 'apply-btn')}
              variant="contained"
              onClick={filterData}
              disabled={filterDisabled}
              color="primary"
              style={{ marginLeft: 8, width: 50, height: 30 }}
            >
              {/* <Typography
                variant="body1"
                align="left"
                style={{ color: '#fff !important' }}
              > */}
              Apply
              {/* </Typography> */}
            </Button>
          </Grid>
          <Grid
            item
            xs={4}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'end'
            }}
          >
            {/* <Button
              variant="contained"
              onClick={this.filterData}
              color="primary"
              style={{ marginTop: 30, height: 35, width: 20 }}
              disabled={filterDisabled}
            >
              Apply
            </Button> */}
            <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{
                  marginRight: 10,
                  marginTop: 10,

                  cursor: 'pointer'
                }}
                onClick={onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
            <Button
              variant="contained"
              id="reset-layout"
              className={clsx(classes.back, 'reset-btn')}
              onClick={resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Grid>
        </Grid>
      </Paper>
      {isLoading && (
        <div>
          <Box style={{ padding: 25 }}>
            <LinearProgress color="secondary" />
            <Typography
              variant="h6"
              align="center"
              style={{ padding: 25 }}
              color="primary"
            >
              Processing...
            </Typography>
          </Box>
        </div>
      )}
      <div
        // id={
        //   // columnName == 'Opcode'
        //   //   ? rowData.length == 0
        //   //     ? 'data-tab-opcode-zero'
        //   //     : 'data-tab-opcode'
        //   //   : ''
        // }
        className="ag-theme-balham"
        style={{
          height: window.innerHeight - 210 + 'px',
          // width: '1106px',
          width:
            columnName == 'Opcode'
              ? '98.8%'
              : columnName == 'Paytype'
              ? '1280px'
              : columnName == 'Chart'
              ? '1350px'
              : '1110px',
          margin: 8,
          display: isLoading == true ? 'none' : 'block'
        }}
      >
        <AgGridReact
          className="ag-theme-balham"
          style={{
            width: '100%',
            height: '410px'
            // minHeight: 500
          }}
          // domLayout="autoHeight"
          // isExternalFilterPresent={this.isExternalFilterPresent}
          // doesExternalFilterPass={this.doesExternalFilterPass}
          floatingFilter={true}
          defaultColDef={defaultColDef}
          headerHeight={headerHeight}
          // sideBar={
          //   columnName == 'Opcode' ? sideBar : ''
          // }
          modules={AllModules}
          columnDefs={
            columnName == 'Advisor'
              ? columnDefsAdvisor
              : columnName == 'Paytype'
              ? columnDefsPaytype
              : columnName == 'Opcode'
              ? columnDefsOpcode
              : columnName == 'Technician'
              ? columnDefsTechnician
              : columnName == 'Chart'
              ? columnDefsChartMaster
              : columnName == 'Store_settings'
              ? columnDefsStoreSettings
              : columnName == 'store_goal'
              ? columnDefsStoreGoal
              : columnName == 'advisor_goal'
              ? columnDefsAdvisorGoal
              : columnName == 'GoalSettings'
              ? columnDefsGoalSettings
              : columnName == 'GoalSettingsAdvisor'
              ? columnDefsGoalSettingsAdvisor
              : columnName == 'fixedrate_opcode'
              ? columnDefsFROpcode
              : ''
          }
          onGridReady={onGridReady}
          rowData={rowData}
          tooltipShowDelay={0}
          enableRangeSelection={true}
          animateRows={true}
          enableCharts={true}
          suppressRowClickSelection={true}
          overlayNoRowsTemplate={overlayNoRowsTemplate}
          excelStyles={excelStyles}
          suppressContextMenu={true}
        />
      </div>
    </div>
  );
}

//
// EditHistory.propTypes = {
//   classes: PropTypes.object.isRequired
// };

export default withKeycloak(withStyles(styles)(EditHistoryOld));
