import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useSelector, useDispatch } from 'react-redux';
import EditHistoryOld from './EditHistoryOld';
import { getDrillDownMonthYears } from 'src/utils/hasuraServices';
import moment from 'moment';
import { useHistory } from 'react-router';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';

//import queryString from 'query-string';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

const EditHistoryStatus = props => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  let drillDownType = '';
  const session = useSelector(state => state.session);
  if (props.location.pathname == '/WarrantyRatesLabor') {
    drillDownType = 'labor';
  } else {
    drillDownType = 'parts';
  }
  useEffect(() => {
    console.log('session=====', session.historyStatus);
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date(monthArr[0].monthYear + '-01'))
          .clone()
          .endOf('month')
          .format('YYYY-MM-DD');

        setSelectedDates([startDate, endDate]);
      }
    });
  }, [session.historyStatus]);
  if (months.length > 0 && selectedDates.length > 0 && session.historyStatus) {
    return (
      <Page className={classes.root} title="Edit History">
        {props.keycloak.realmAccess.roles.includes('client') ||
        props.keycloak.realmAccess.roles.includes('user') ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <EditHistoryOld
            history={history}
            drillDownType={drillDownType}
            months={months}
            selectedDates={selectedDates}
            historyStatus={session.historyStatus}
          />
        )}
      </Page>
    );
  } else {
    return <LoaderSkeleton></LoaderSkeleton>;
  }
};

export default withKeycloak(EditHistoryStatus);
