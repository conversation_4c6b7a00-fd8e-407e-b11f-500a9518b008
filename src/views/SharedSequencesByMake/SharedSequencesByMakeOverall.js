import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Toolbar,
  Grid
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { getSharedSequencesByMakeOverall } from 'src/utils/hasuraServices';
import SharedSequencesByMakeMonthly from './SharedSequencesByMakeMonthly';
import { withStyles } from '@material-ui/styles';
import { withKeycloak } from '@react-keycloak/web';
import { ReactSession } from 'react-client-session';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

const valueGetter = params => {
  return `${params.data[params.colDef.field]}`;
};

class SharedSequencesByMakeOverall extends React.Component {
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData();
  //     }
  //   }
  // }
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    if (this.state.resetReport != this.props.resetReport) {
      this.resetRowData();
    }

    if (this.props.exportReport == true) {
      this.onBtExport();
    }
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.setState({ serviceAdvisors: ReactSession.get("serviceAdvisors") });
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    let realm = localStorage.getItem('realm');
    const startEdit = this;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      showRefresh: 'none',
      disableTable: 'all',
      rawGridApi: {},
      tabSelection: 'two',
      resetReport: this.props.resetReport,
      exportReport: this.props.exportReport,
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      columnDefs: [
        {
          headerName: 'Make',
          chartDataType: 'series',
          field: 'make',
          // width: 120,
          width: realm === 'chevyland' ? 180 : 120,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'RO Count',
          chartDataType: 'series',
          field: 'rocount',
          width: 100,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          valueFormatter: this.formatCellValueCount,
          filterParams: {
            valueFormatter: this.formatCellValueCount
          }
        },
        {
          headerName: 'Total RO Count',
          chartDataType: 'series',
          field: 'totalRocount',
          width: 140,
          cellClass: 'textAlign',
          suppressMenu: true,

          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: this.formatCellValueCount,
          filterParams: {
            valueFormatter: this.formatCellValueCount
          }
        },
        {
          headerName: 'Percentage',
          chartDataType: 'series',
          field: 'percentage',
          width: 140,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          valueGetter: valueGetter,
          valueFormatter: this.formatCellValueGP,
          cellStyle() {
            return {
              textAlign: 'right',
              border: ' 0px white',
              paddingRight: '30px'
            };
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
        // editable: true
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridcolumnApi: params.columnApi });
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };
  formatCellValueCount = params => {
    if (params.value != null && params.value != '') {
      return params.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '';
    }
  };
  formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      if (params.value == '100.00') {
        return '100%';
      } else
        return (
          parseFloat(params.value)
            .toFixed(2)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
        );
    } else {
      return '0.0';
    }
  };
  getAgGridData() {
    this.setState({ isLoading: true });
    getSharedSequencesByMakeOverall(result => {
      this.setState({ isLoading: false });
      if (result.data.statelessDbdReferencesGetMakeCountPercentage.nodes) {
        this.setState({
          rowData:
            result.data.statelessDbdReferencesGetMakeCountPercentage.nodes
        });
      }
    });
  }

  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      // columnKeys: ['makesId', 'manufacturer', 'validMakes'],
      fileName: 'Shared Sequences By Make - Overall',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Shared Sequences By Make - Overall'
            },
            mergeAcross: 3
          }
        ]
      ],
      processCellCallback: params => {
        if (params.column.getColId() === 'percentage') {
          let formattedValue = params.value;
          // Check if the formatted value ends with .00
          if (formattedValue.endsWith('.00')) {
            return formattedValue.slice(0, -3) + '%'; // Remove .00 and add %
          } else {
            return formattedValue + '%'; // Add % without removing anything
          }
        }
        // For other columns, return the value as is
        return params.value;
      }
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  resetRowData = () => {
    this.setState({ resetReport: this.props.resetReport });
    this.props.handleResetReport();
    this.state.gridcolumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  render() {
    const { classes } = this.props;
    return (
      <React.Fragment>
        <div>
          {/* {this.state.tabSelection == "one" ? (
          <div>
            <Paper square style={{ marginLeft: 8, marginRight: 8, height: 29 }}>
              <Toolbar>
                <Grid container justify="flex-start" style={{ padding: '5px' }}>
                  <Tooltip title="Export To Excel">
                    <Link
                      style={{
                        marginTop: -28,
                        marginLeft: 'auto',
                        cursor: 'pointer'
                      }}
                      onClick={this.onBtExport}
                    >
                      <ExportIcon />
                    </Link>
                  </Tooltip>
                </Grid>
              </Toolbar>
            </Paper>
          </div>
        ) : null } */}
          {this.state.isLoading === true ? (
            <div>
              <Box style={{ padding: 25 }}>
                <LinearProgress color="secondary" />
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  Processing...
                </Typography>
              </Box>
            </div>
          ) : null}
          <div
            id="data-tab"
            className="ag-theme-balham"
            style={{
              height: window.innerHeight - 250 + 'px',
              width: '500px',
              alignContent: 'center',
              marginLeft: '8px',
              display: this.state.tabSelection === 'one' ? 'none' : 'block'
              // pointerEvents: disableTable
            }}
          >
            <AgGridReact
              className="ag-theme-balham"
              style={{
                height: '500px',
                width: '100%'
              }}
              autoGroupColumnDef={this.state.autoGroupColumnDef}
              getChartToolbarItems={this.getChartToolbarItems}
              modules={AllModules}
              columnDefs={this.state.columnDefs}
              defaultColDef={this.state.defaultColDef}
              onGridReady={this.onGridReady}
              suppressAggFuncInHeader
              rowData={this.state.rowData}
              excelStyles={this.state.excelStyles}
              tooltipShowDelay={0}
              floatingFilter={true}
              enableRangeSelection={true}
              animateRows={true}
              enableCharts={true}
              suppressRowClickSelection={true}
              suppressHorizontalScroll={true}
              suppressDragLeaveHidesColumns={true}
              suppressContextMenu={true}
            />
          </div>
        </div>
      </React.Fragment>
    );
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
});
export default withKeycloak(withStyles(styles)(SharedSequencesByMakeOverall));
// export default SharedSequencesByMakeOverall;
