.fullCalendar .fc-col-header-cell {
    background-color: #f1741f !important;
    color: white !important;
    font-weight: bold !important;
}


.fullCalendar .fc-daygrid-day-frame {
    height: 50px !important;
    position: relative;
}

/* Position day numbers */
.fullCalendar .fc-daygrid-day-number {
    position: absolute;
    top: 5px;
    right: 5px;
}


.fullCalendar .fc .fc-daygrid-day-frame:hover {
    background-color: transparent !important;
}

.fullCalendar .custom-grid-fullcalendar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    /* border: 0.5px solid black !important; */
    height: 28px !important;
}

.fullCalendar .custom-grid-fullcalendar-first {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;

}

.fullCalendar .grid-item-fullcalendar {

    color: #ffff;
    padding-top: 0px !important;
}

.fullCalendar .grid-item-fullcalendar-left>div {

    flex: 1 1 100%;
    max-width: 100%;
    text-align: center;
    background-color: #f0f0f0;

    box-sizing: border-box;
}

.fullCalendar .grid-item-fullcalendar-middle {

    padding-top: 5px !important;
}

.fullCalendar .custom-grid-fullcalendar>div {
    flex: 1 1 14.2857%;
    max-width: 14.2857%;
    text-align: center;
    background-color: #f0f0f0;

    box-sizing: border-box;
}

.fullCalendar .custom-grid-fullcalendar-first>div {
    flex: 1 1 14.2857%;
    /* max-width: 14.2857%; */
    text-align: center;
    background-color:#0E558B;
    padding: 4px;
    box-sizing: border-box;
    font-size: 18px;
    padding-top: 4px !important;
}


.fullCalendar .fc {
    max-height: calc(100vh - 250px);
    overflow: hidden;
}

@media (min-width: 1200px) {
    .fullCalendar .fc {
        max-height: calc(100vh - 250px);
    }
}


.fullCalendar .loaderOverlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}



.fullCalendar {
    font-family: 'Roboto', 'Helvetica', 'Arial', 'sans - serif';
}

.fullCalendar .fc-event:hover  {
    background: none;
}

.fullCalendar .titleClass {
    padding: 19px;
    display: grid;
    grid-template-columns: auto auto auto;
    gap: 2px;
}
.fullCalendar .titleClassnew {
    padding: 17px 0px 0px 28px;
    display: grid;
    grid-template-columns: auto auto auto;
    grid-gap: 2px;
    gap: 1px;
}

@media (max-width: 1280px) {
    .fullCalendar .titleClass {
        padding: 5px !important;
        grid-gap: 2px !important;
        gap: 2px !important;
        margin-top: 20px !important;
        font-size: 10px;
    }
    .fullCalendar .titleClassnew {
        padding: 9px 12px 4px 0px !important;
    }

    .fullCalendar .grid-item-fullcalendar-middle{
       font-size: 15px;
    }
    .fullCalendar .grid-item-fullcalendar{
        font-size: 15px !important;
    }
}

@media (min-width: 1281px) and (max-width: 1366px) {
    .fullCalendar .titleClass {
        padding: 4px !important;
        grid-gap: 2px !important;
        gap: 2px !important;
        margin-top: 13px;
    }
    .fullCalendar .titleClassnew {
        padding: 8px 0px 0px 5px !important;
    }
    .fullCalendar .grid-item-fullcalendar {
        font-size: 15px !important;
    }
}

@media (min-width: 1367px) and (max-width: 1440px) {
    .fullCalendar .titleClass {
        padding: 18px 0px 0px 5px !important;
        grid-gap: 3px !important;
            gap: 3px !important;
    }
}