import React, { useRef, useEffect, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import { formatDate } from '@fullcalendar/core';
import { withStyles } from '@material-ui/styles';
import { withKeycloak } from '@react-keycloak/web';
import './fullcalendar.css';
import { getROItems, getStoreNickName } from 'src/utils/hasuraServices';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import ArrowForward from '@material-ui/icons/ArrowForward';
import { useSelector } from 'react-redux';
import { Grid, CircularProgress, Paper, Divider } from '@material-ui/core';
import { DatePicker } from '@material-ui/pickers';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
var Dealer = process.env.REACT_APP_DEALER;

function ROCalendar({ classes }) {
  const calendarRef = useRef(null);
  const [customTitle, setCustomTitle] = useState('');
  const [events, setEvents] = useState([]);
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth() + 1);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());
  const [actualCurrentMonth] = useState(new Date().getMonth() + 1);
  const [actualCurrentYear] = useState(new Date().getFullYear());
  const [total, setTotal] = useState(0);
  const [icount, setIcount] = useState(0);
  const [wcount, setWcount] = useState(0);
  const [cpcount, setCpcount] = useState(0);
  const [loader, setLoader] = useState(false);
  const [storeNickName, setStoreNickName] = useState(
    localStorage.getItem('storeSelected')
  );
  const [openDatePicker, setOpenDatePicker] = useState(false);
  const [dispalyItem, setDispalyItem] = useState(true);
  const [rowCount, setRowCount] = useState(0);

  const session = useSelector(state => state.session);
  var serviceAdvisorSession = session.serviceAdvisor;
  if (serviceAdvisorSession == 'All') {
    serviceAdvisorSession = null;
  }

  const crMonth = new Date().getMonth() + 1; // getMonth() is zero-based
  const crYear = new Date().getFullYear();
  const isCurrentMonthAndYear =
    currentMonth === crMonth && currentYear === crYear;

  useEffect(() => {
    // Query all elements with the class .fc-daygrid-day-frame after the component mounts
    const dayFrames = document.querySelectorAll(
      '.fullCalendar .fc-daygrid-day-frame'
    );

    dayFrames.forEach(element => {
      if (element.offsetHeight > 50) {
        // Apply your desired style if height is greater than 50px
        element.style.overflowY = 'auto'; // Example: setting background color to red
      }
    });
  }, []); // Empty dependency array to run once after component mounts

  useEffect(() => {
    const calendarApi = calendarRef.current.getApi();
    updateCustomTitle(calendarApi);
    fetchROItems('');
  }, [session.storeSelected, serviceAdvisorSession]);

  useEffect(() => {
    const calendarApi = calendarRef.current.getApi();
    updateCustomTitle(calendarApi);
    fetchROItems('');
    const rows = document.querySelectorAll('.fc-scrollgrid-sync-table tr');
    setRowCount(rows.length);
  }, [currentMonth, currentYear]);

  const fetchROItems = val => {
    setLoader(true); // Start the loader
    let yearMonth = `${actualCurrentYear}-${actualCurrentMonth
      .toString()
      .padStart(2, '0')}`;
    if (val == '') {
      yearMonth = `${currentYear}-${currentMonth.toString().padStart(2, '0')}`;
    } else {
      setDispalyItem(true);
      setCurrentMonth(new Date().getMonth() + 1);
      setCurrentYear(new Date().getFullYear());
      setOpenDatePicker(false);

      const calendarApi = calendarRef.current.getApi();
      calendarApi.gotoDate(new Date());
    }
    getStoreNickName(result => {
      const nodes =
        result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes;
      const node = nodes && nodes[0];

      if (
        node != null &&
        node.storeNickname &&
        node.storeNickname.trim() !== ''
      ) {
        setStoreNickName(node.storeNickname);
      } else {
        setStoreNickName(localStorage.getItem('storeSelected'));
      }
    });

    getROItems(yearMonth, serviceAdvisorSession, result => {
      if (!result || !result.data) {
        console.error('No data in result:', result);
        setLoader(false);
        return;
      }

      if (
        !result.data.statelessCcPhysicalRwGetDailyRocountCalendar ||
        !result.data.statelessCcPhysicalRwGetDailyRocountCalendar.json
      ) {
        console.error('Expected data structure not found:', result.data);
        setLoader(false);
        return;
      }
      const apiData =
        result.data.statelessCcPhysicalRwGetDailyRocountCalendar.json;
      const parsedData = JSON.parse(apiData);
      const formattedEvents = formatEvents(parsedData.daily_rocount_paytype);
      const monthlyROcount = parsedData.monthly_rocount_paytype;
      setTotal(monthlyROcount.total);
      setIcount(monthlyROcount.Icount);
      setWcount(monthlyROcount.wcount);
      setCpcount(monthlyROcount.cpcount);
      setEvents(formattedEvents);
      setLoader(false); // Stop the loader
    });
  };

  const updateCustomTitle = calendar => {
    const view = calendar.view;
    const date = view.currentStart;
    const formattedDate = formatDate(date, {
      year: '2-digit',
      month: 'long'
    });
    const [month, year] = formattedDate.split(' ');
    setCustomTitle(`${month} '${year}`);
  };

  const formatEvents = data => {
    if (typeof data === 'string') {
      try {
        data = JSON.parse(data);
      } catch (error) {
        console.error('Failed to parse data:', error);
        return [];
      }
    }

    if (!Array.isArray(data)) {
      console.error('Data is not an array:', data);
      return [];
    }

    return data
      .map(event => {
        if (
          event.date === undefined ||
          event.cp_count === undefined ||
          event.W_count === undefined ||
          event.I_count === undefined ||
          event.total_rocount === undefined
        ) {
          console.error('Invalid event data:', event);
          return null;
        }

        const eventClass =
          document.querySelectorAll('.fc-scrollgrid-sync-table tr').length === 6
            ? 'titleClassnew'
            : 'titleClass';

        return {
          title: `
            <div class="${eventClass}">
              <p style="color: green; text-align: right; padding-right: 5px;">Customer Pay</p>
              <p style="color: green;">:</p>
              <p style="color: green; padding-left: 5px;">${event.cp_count}</p>
    
              <p style="color: blue; text-align: right; padding-right: 5px;">Warranty</p>
              <p style="color: blue;">:</p>
              <p style="color: blue; padding-left: 5px;">${event.W_count}</p>
    
              <p style="color: purple; text-align: right; padding-right: 5px;">Internal</p>
              <p style="color: purple;">:</p>
              <p style="color: purple; padding-left: 5px;">${event.I_count}</p>
    
              <p style="color: black; text-align: right; padding-right: 5px;">Total</p>
              <p style="color: black;">:</p>
              <p style="color: black; padding-left: 5px;">${event.total_rocount}</p>
            </div>
          `,
          start: event.date,
          display: 'list-item'
        };
      })
      .filter(event => event !== null && new Date(event.start) <= new Date());
  };

  const dayHeaderContent = args => {
    return args.text.replace(/^(\w{3})/, (match, abbrev) => {
      switch (abbrev) {
        case 'Mon':
          return 'Monday';
        case 'Tue':
          return 'Tuesday';
        case 'Wed':
          return 'Wednesday';
        case 'Thu':
          return 'Thursday';
        case 'Fri':
          return 'Friday';
        case 'Sat':
          return 'Saturday';
        case 'Sun':
          return 'Sunday';
          return abbrev;
      }
    });
  };

  const renderEventContent = eventInfo => {
    return <div dangerouslySetInnerHTML={{ __html: eventInfo.event.title }} />;
  };

  const handlePrevClick = () => {
    calendarRef.current.getApi().prev();
    if (currentMonth === 1) {
      setCurrentMonth(12);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
    setDispalyItem(true);
  };

  const handleNextClick = () => {
    const maxYear = new Date().getFullYear();
    const maxMonth = new Date().getMonth() + 1;
    if (
      currentYear < maxYear ||
      (currentYear === maxYear && currentMonth < maxMonth)
    ) {
      calendarRef.current.getApi().next();
      if (currentMonth === 12) {
        setCurrentMonth(1);
        setCurrentYear(currentYear + 1);
      } else {
        setCurrentMonth(currentMonth + 1);
      }
      setDispalyItem(true);
    } else {
      setDispalyItem(false);
    }
  };

  const handleDateChange = date => {
    setDispalyItem(true);
    const validDate = date instanceof Date ? date : new Date(date);

    if (validDate > new Date()) {
      setCurrentMonth(new Date().getMonth() + 1);
      setCurrentYear(new Date().getFullYear());
      console.warn('Future dates are disabled.');
      return;
    }

    setCurrentMonth(validDate.getMonth() + 1);
    setCurrentYear(validDate.getFullYear());
    setOpenDatePicker(false);

    const calendarApi = calendarRef.current.getApi();
    calendarApi.gotoDate(validDate);
  };
  return (
    <Paper
      square
      style={{
        margin: 8
      }}
    >
      <Grid item xs={12} style={{ justifyContent: 'left' }}>
        <Paper square style={{ margin: 8 }}>
          <Tabs
            value="0"
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Tab
              style={{
                pointerEvents: 'none',
                textTransform: 'none',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
              }}
              label={<div>RO Count - Calendar</div>}
              value="0"
            />
          </Tabs>
        </Paper>
        <Divider></Divider>
        <div className="fullCalendar">
          {loader && (
            <div className="loaderOverlay">
              <CircularProgress size={60} />
            </div>
          )}
          <div
            style={{
              padding: '9px',
              position: 'relative'
            }}
          >
            <div className="custom-grid-fullcalendar-first">
              {/* <div
                className="grid-item-fullcalendar"
                style={{
                  backgroundColor: '#0E558B',
                  textAlign: 'center',
                  fontSize: '21px',
                  paddingRight: '165px',
                  maxWidth: '25%'
                }}
              >
                <strong>FOPC</strong>
              </div> */}

              <div
                style={{
                  backgroundColor: '#0E558B',
                  color: '#ffff',
                  justifyContent: 'center',
                  textAlign: 'left',
                  maxWidth: '100%',
                  paddingLeft: '30px'
                }}
              >
                <strong>{storeNickName}</strong>
              </div>
            </div>
            <div className="custom-grid-fullcalendar">
              <div
                className="grid-item-fullcalendar"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f1741f',
                  textAlign: 'center',
                  fontSize: '18px',
                  borderRight: '1px solid #ccc'
                }}
              >
                <strong>RO Count - Calendar</strong>
              </div>

              <div className="grid-item-fullcalendar-middle">
                <strong style={{ color: 'green' }}>
                  Customer Pay : {cpcount}
                </strong>
              </div>
              <div className="grid-item-fullcalendar-middle">
                <strong style={{ color: 'blue' }}>Warranty : {wcount}</strong>
              </div>
              <div className="grid-item-fullcalendar-middle">
                <strong style={{ color: 'purple' }}>Internal : {icount}</strong>
              </div>
              <div className="grid-item-fullcalendar-middle">
                <strong style={{ color: 'black' }}>Total : {total}</strong>
              </div>
              <div
                className="grid-item-fullcalendar"
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f1741f',
                  textAlign: 'center',
                  fontSize: '18px',
                  maxWidth: '100%',
                  position: 'relative'
                }}
              >
                <ArrowBackIcon
                  onClick={handlePrevClick}
                  style={{
                    cursor: 'pointer',
                    marginRight: '200px',
                    position: 'absolute'
                  }}
                />
                <strong
                  style={{
                    cursor: 'pointer',
                    margin: '0 70px',
                    color: '#fff',
                    transition: 'text-decoration 0.3s'
                  }}
                  onClick={() => setOpenDatePicker(true)}
                  onMouseOver={e => (
                    (e.currentTarget.style.color = '#0056b3'),
                    (e.currentTarget.style.textDecoration = 'underline')
                  )}
                  onMouseOut={e => (
                    (e.currentTarget.style.color = '#fff'),
                    (e.currentTarget.style.textDecoration = 'none')
                  )}
                  aria-role="button"
                >
                  {customTitle}{' '}
                  <i className="fa fa-calendar" aria-hidden="true"></i>
                </strong>

                {!isCurrentMonthAndYear && (
                  <ArrowForward
                    onClick={handleNextClick}
                    style={{
                      cursor: 'pointer',
                      marginLeft: '200px',
                      position: 'absolute'
                    }}
                  />
                )}
              </div>
            </div>

            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin]}
              initialView="dayGridMonth"
              weekends={true}
              firstDay={1}
              headerToolbar={false}
              datesSet={arg => updateCustomTitle(arg.view.calendar)}
              events={events}
              dayHeaderContent={dayHeaderContent}
              eventContent={renderEventContent}
              datesAboveResources={true}
              // showNonCurrentDates={false}
              fixedWeekCount={false}
            />
          </div>

          {openDatePicker && (
            <DatePicker
              style={{ display: 'none' }}
              open={openDatePicker}
              onClose={() => setOpenDatePicker(false)}
              views={['year', 'month']}
              value={new Date(currentYear, currentMonth - 1)}
              onChange={handleDateChange}
              renderInput={params => null}
              emptyLabel={null}
              maxDate={
                new Date(new Date().getFullYear(), new Date().getMonth())
              }
            />
          )}
        </div>
      </Grid>
    </Paper>
  );
}

export default ROCalendar;
