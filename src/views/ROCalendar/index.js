import React from 'react';
import Page from 'src/components/Page';
import ROCalendar from './ROCalendar';

import { Redirect } from 'react-router-dom';

import { withKeycloak } from '@react-keycloak/web';

const Calendar = props => {
  return (
    <Page title="RO Count-Calendar">
      {props.keycloak.realmAccess.roles.includes('client') ||
      props.keycloak.realmAccess.roles.includes('user') ||
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <ROCalendar />
      )}
    </Page>
  );
};

export default withKeycloak(Calendar);
