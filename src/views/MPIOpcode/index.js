import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';

import { setRefreshStatus, setReloadStatus } from 'src/actions';
import MPIOpcodesList from './MPIOpcodes';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';

// const useStyles = makeStyles(theme => ({
//   root: {
//     paddingTop: theme.spacing(0),
//     paddingBottom: theme.spacing(3)
//   },
//   statistics: {
//     marginTop: theme.spacing(3)s
//   },
//   notifications: {
//     marginTop: theme.spacing(6)
//   },
//   projects: {
//     marginTop: theme.spacing(6)
//   },
//   todos: {
//     marginTop: theme.spacing(6)
//   }
// }));

function MPIOpcode() {
  // const classes = useStyles();
  // const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const handleRefresh = status => {
    //dispatch(setRefreshStatus(status));
  };
  const handleReload = status => {
    // dispatch(setReloadStatus(status));
  };
  return (
    <Page title="MPIOpcodes">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <MPIOpcodesList
          handleRefresh={handleRefresh.bind(this)}
          handleReload={handleReload.bind(this)}
          session={session}
        />
      )}
    </Page>
  );
}

export default MPIOpcode;
