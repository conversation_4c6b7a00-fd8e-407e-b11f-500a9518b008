import React, { useState, useEffect, useRef } from 'react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import {
  Button,
  Paper,
  Typography,
  Tab,
  Tabs,
  Dialog,
  IconButton
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'src/grid.css';
import moment from 'moment';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { getEmail } from 'src/utils/hasuraServices';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import { withKeycloak } from '@react-keycloak/web';
import { makeStyles } from '@material-ui/core/styles';
import { useDispatch, useSelector } from 'react-redux';
import Page from 'src/components/Page';
import { Redirect } from 'react-router-dom';
import { AgGridReact } from '@ag-grid-community/react';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import CloseIcon from '@material-ui/icons/Close';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import EditIcon from '@material-ui/icons/Edit';
import { Alert } from '@material-ui/lab';
import $ from 'jquery';

const Dealer = process.env.REACT_APP_DEALER;

const useStyles = makeStyles(theme => ({
  formControl: {
    minWidth: 170,
    marginTop: -19
  },
  btnContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    marginRight: 25
  },
  btnUser: {
    height: '27px',
    fontSize: '12px !important',
    marginTop: '6px',
    width: 110
  },
  dialog: {
    position: 'absolute',
    top: 50
  },
  reset: {
    width: '110px',
    margin: '8px !important'
  }
}));

const ActionCellRenderer = ({ data, rowIndex, onBtStartEditing }) => {
  const [editMode, setEditMode] = useState(false);

  const handleEdit = () => {
    const oldRow = [data.status, data.value];

    localStorage.setItem('oldStatus', data.status);
    localStorage.setItem('oldValue', data.value);
    localStorage.setItem('oldRow', JSON.stringify(oldRow));

    setEditMode(true);

    const rowPrev = {
      status: data.status,
      value: data.value,
      storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
    };

    const prevCodeArray =
      JSON.parse(localStorage.getItem('prevCodeArray')) || [];
    const indexArr = prevCodeArray.findIndex(
      item => item.status === rowPrev.status && item.value === rowPrev.value
    );

    if (indexArr === -1) {
      prevCodeArray.push(rowPrev);
    }

    localStorage.setItem('prevCodeArray', JSON.stringify(prevCodeArray));

    const prevRow = [
      {
        status: data.status,
        value: data.value,
        storeId: JSON.parse(localStorage.getItem('selectedStoreId'))[0]
      }
    ];

    localStorage.setItem('oldCodeArray', JSON.stringify(prevRow));
    localStorage.setItem('emailStatus', data.status);

    onBtStartEditing(rowIndex);
  };

  const handleSave = () => {
    // Save functionality here
    setEditMode(false);
  };

  const handleCancel = () => {
    // Cancel functionality here
    setEditMode(false);
  };

  const handleDelete = () => {
    // Delete functionality here
  };

  return (
    <div>
      {!editMode && (
        <>
          <Button title="Edit" onClick={handleEdit}>
            <i className="fas fa-pencil-alt"></i>
          </Button>
          <Button title="Delete" onClick={handleDelete}>
            <i className="fas fa-trash-alt"></i>
          </Button>
        </>
      )}
      {editMode && (
        <>
          <Button title="Cancel" onClick={handleCancel}>
            <i className="fas fa-ban"></i>
          </Button>
          <Button title="Save" onClick={handleSave}>
            <i className="fas fa-check"></i>
          </Button>
        </>
      )}
    </div>
  );
};

const EmailList = props => {
  const dispatch = useDispatch();
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [isLoading, setIsLoading] = useState(true);
  const [rowData, setRowData] = useState([]);
  const [columnDefs, setColumnDefs] = useState([
    {
      headerName: ' Id',
      chartDataType: 'series',
      field: 'status',
      hide: true,
      editable: false
    },
    {
      headerName: 'User Email',
      chartDataType: 'series',
      field: 'value',
      minWidth: 270,
      cellClass: 'textAlign',
      suppressMovable: false,
      editable: true,
      suppressMenu: true,
      unSortIcon: true,
      flex: 1,
      tooltipField: 'value',
      cellStyle: () => ({ border: '0px white', marginLeft: '6px' }),
      filter: 'agSetColumnFilter'
    },
    {
      headerName: 'Action',
      width: 120,
      filter: false,
      sortable: false,
      editable: false,
      suppressMenu: true,
      filter: 'agSetColumnFilter',
      flex: 1,
      cellStyle: () => ({ textAlign: 'center', border: '0px white' }),
      cellRendererFramework: params => (
        <ActionCellRenderer
          {...params}
          onBtStartEditing={onBtStartEditing} // Pass onBtStartEditing function
        />
      )
    }
  ]);
  //   const [defaultColDef, setDefaultColDef] = useState({
  //     enableValue: true,
  //     sortable: true,
  //     filter: true,
  //     filter: 'agSetColumnFilter',
  //     filterParams: { applyMiniFilterWhileTyping: true },
  //     resizable: false,
  //     suppressMovable: false
  //   });
  const [defaultColDef, setDefaultColDef] = useState({
    enableValue: true,
    sortable: true,
    filter: true,
    filter: 'agSetColumnFilter',
    filterParams: {
      applyMiniFilterWhileTyping: true
    },
    resizable: false,
    suppressMovable: false
  });
  const gridApiRef = useRef(null);

  useEffect(() => {
    setIsLoading(true);
    getEmailData('NULL', 'NULL', 'view');
  }, [session.storeSelected]);

  const getEmailData = (pNewMailId, pOldMailId, pProcess) => {
    setIsLoading(true);
    getEmail(pNewMailId, pOldMailId, pProcess, result => {
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        setRowData(
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results
        );
      }
      setIsLoading(false);
    });
  };

  const handleAddEmail = () => {
    getEmailData('<EMAIL>', '<EMAIL>', 'insert');
  };

  const handleDeleteEmail = () => {
    getEmailData('<EMAIL>', '<EMAIL>', 'delete');
  };

  const handleUpdateEmail = () => {
    getEmailData('<EMAIL>', '<EMAIL>', 'update');
  };

  const onGridReady = params => {
    gridApiRef.current = params.api;
  };

  const onBtStartEditing = (index, key, char, pinned) => {
    if (gridApiRef.current) {
      const updatedColumnDefs = [...columnDefs];
      console.log('updatedColumnDefs==', updatedColumnDefs);
      updatedColumnDefs[1].editable = true;

      gridApiRef.current.setColumnDefs(updatedColumnDefs);
      gridApiRef.current.setFocusedCell(index, 'value', pinned);
      gridApiRef.current.startEditingCell({
        rowIndex: index,
        colKey: 'value',
        rowPinned: pinned,
        keyPress: key,
        charPress: char
      });
    } else {
      console.error('Grid API is not initialized.');
    }
  };

  return props.keycloak.realmAccess.roles.includes('client') ||
    props.keycloak.realmAccess.roles.includes('user') ||
    JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
    <Redirect to="/errors/error-404" />
  ) : (
    <>
      <Paper
        square
        style={{
          margin: 8,
          backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
          border:
            Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
          color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
        }}
      >
        <Dialog classes={{ paper: classes.dialog }}>
          <Alert
            action={
              <IconButton aria-label="close" color="inherit" size="small">
                <CloseIcon fontSize="inherit" />
              </IconButton>
            }
            style={{ margin: '10px 20px' }}
          >
            {/* Alert Message */}
          </Alert>
        </Dialog>
        <Tabs
          variant="fullWidth"
          indicatorColor="secondary"
          textColor="secondary"
          aria-label="icon label tabs example"
        >
          <Tab
            style={{ pointerEvents: 'none', textTransform: 'none' }}
            label={<div>Email</div>}
            value="one"
          />
          <Button
            variant="contained"
            id="reset-layout"
            style={{ margin: 8, marginTop: 11 }}
            className={clsx(classes.reset, 'reset-btn')}
          >
            <RestoreIcon />
            <Typography variant="body1" align="left">
              Reset Layout
            </Typography>
          </Button>
        </Tabs>
      </Paper>
      {props.keycloak.realmAccess.roles.includes('superadmin') && (
        <Paper
          square
          style={{
            marginLeft: 8,
            marginRight: 8,
            paddingTop: 9,
            display: 'flex',
            height: '54px',
            justifyContent: 'space-between'
          }}
        >
          <div className={classes.btnContainer}>
            <Button
              variant="contained"
              className={clsx('reset-btn', classes.btnUser)}
              onClick={handleAddEmail}
            >
              Add Email
            </Button>
          </div>
        </Paper>
      )}
      <div
        id="data-tab-user"
        className="ag-theme-balham"
        style={{
          height: window.innerHeight - 230 + 'px',
          width: 400,
          alignContent: 'center',
          margin: 8,
          display: isLoading ? 'none' : 'block'
        }}
      >
        <AgGridReact
          className="ag-theme-balham"
          style={{ height: '500px', width: '100%' }}
          enableRangeSelection={true}
          animateRows={true}
          enableCharts={true}
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          rowData={rowData}
          singleClickEdit={true}
          floatingFilter={true}
          suppressRowClickSelection={true}
          suppressHorizontalScroll={true}
          suppressDragLeaveHidesColumns={true}
          onGridReady={onGridReady}
          suppressAggFuncInHeader={true}
          editType={'fullRow'}
          suppressContextMenu={true}
        />
      </div>
    </>
  );
};

export default withKeycloak(EmailList);
