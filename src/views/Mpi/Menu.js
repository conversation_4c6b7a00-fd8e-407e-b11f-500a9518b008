import React, { useState, useEffect, useCallback } from 'react';
import {
  Text<PERSON>ield,
  Button,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Checkbox,
  FormControlLabel
} from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import clsx from 'clsx';
import {
  getMpiValue,
  getMenuNames,
  getMenuData
} from 'src/utils/hasuraServices';
import { withKeycloak } from '@react-keycloak/web';
import MenuDetails from 'src/views/Mpi/MenuDetails';
import { ADD_MENU } from 'src/graphql/queries';
import makeApolloClientPostgresWrite from 'src/utils/apolloRootClientPostgresWrite';
import { traceSpan } from 'src/utils/OTTTracing';
import MenuData from './MenuData';
import SuccessSnackbar from 'src/views/KPIScoreCardGoalSettings/SuccessSnackbar';
import { useDispatch, useSelector } from 'react-redux';
import { ConsoleSpanExporter } from '@opentelemetry/sdk-trace-web';
const useStyles = makeStyles(theme => ({
  textContainerGrid: {
    gap: 10,
    paddingLeft: 23,
    paddingTop: 15
  },
  mainLabel: {
    display: 'flex',
    color: 'rgb(0, 61, 107)',
    width: '77%',
    marginLeft: 58
  },
  card: {
    marginLeft: '20px',
    marginTop: '28px',
    height: '450px',
    overflow: 'auto !important'
  },
  cards: {
    marginLeft: '20px',
    marginTop: '28px',
    height: '400px'
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 13,
    marginTop: '6px',
    width: '176px',
    paddingRight: 68,
    marginLeft: -19
  },
  txt: {
    width: '200px',
    marginLeft: 40
  },
  btnDiv: {
    width: '100px',
    marginLeft: 60
  },
  btnDivMenu: {
    width: '150px',
    marginRight: 75,
    float: 'inline-end',
    marginTop: 12,
    display: 'flex'
  },
  labelPos: {
    textAlign: 'right'
  },
  star: {
    color: 'red',
    padding: '5px'
  },
  contentcred: {
    width: '100%',
    margin: 'auto'
  },
  contentcredMenuData: {
    margin: 'auto',
    overflow: 'auto',
    height: 315
  },
  dialog: {
    position: 'absolute',
    top: 50
  },
  error: {
    margin: 0,
    fontSize: '90%',
    color: 'tomato',
    marginTop: '20px',
    marginLeft: '15px'
  }
}));
const Menu = props => {
  const classes = useStyles();
  const [isDisabledMenu, setIsDisabledMenu] = useState(false);
  const [showChild, setShowChild] = useState(false);
  const [value, setValue] = useState(new Date());
  const [maxMilege, setMaxMilege] = useState();
  const [milegeAfter, setMilegeAfter] = useState();
  const [milegeBefore, setMilegeBefore] = useState();
  const [milegeInterval, setMilegeInterval] = useState();
  const [menuName, setMenuName] = useState();
  const [menuNameCopy, setMenuNameCopy] = useState();
  const [error, setError] = useState();
  const [errorMenu, setErrorMenu] = useState(false);
  const [errorMlInt, setErrorMlInt] = useState(false);
  const [errorMlBf, setErrorMlBf] = useState(false);
  const [errorMlAf, setErrorMlAf] = useState(false);
  const [errorMlMax, setErrorMlMax] = useState(false);
  const [milegeData, setMilegeData] = useState('');
  const [milegeDataInterval, setMilegeDataInterval] = useState([]);
  const [showMilege, setShowMilege] = useState(false);
  const [requiredText, setRequiredText] = useState(false);
  const [allMenu, setAllMenu] = useState([]);
  const [successMsg, setSuccessMsg] = useState('');
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [minteval, setmInteval] = useState([]);
  const [mServicetype, setMServicetype] = useState('');
  const [mfrh, setMfrh] = useState('');
  const [mintervalData, setMintervalData] = useState('');
  const [mitems, setMitems] = useState('');
  const [mname, setMname] = useState('');
  const [mprice, setMprice] = useState('');
  const [mOpcodes, setMOpcodes] = useState([]);
  const [mSeries, setMSeries] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const session = useSelector(state => state.session);
  const [edit, setEdit] = useState(false);
  const [catArr, setCatArr] = useState([]);
  const [submitClicked, setSubmitClicked] = useState(false);
  const [isChecked, setIsChecked] = useState(true);
  useEffect(() => {
    setIsDisabledMenu(false);
    setShowMilege(false);
    menuNameData();
  }, [session.storeSelected]);
  const menuNameData = e => {
    getMenuNames(result => {
      setIsLoading(true);
      if (result.data.statelessCcPhysicalRwGetMenunames.getMenuNames) {
        var resultArr =
          result.data.statelessCcPhysicalRwGetMenunames.getMenuNames;
        setAllMenu(resultArr);
        setIsLoading(false);
      }
    });
  };
  // const onBlurMenuName = e => {
  //   if (e.target.value) {
  //     // Remove extra spaces and trim the value
  //     let multi = e.target.value.replace(/\s+/g, ' ').trim();

  //     // Check if the menu name already exists (case insensitive)
  //     const exists = allMenu.some(
  //       item => item.menuName.toLowerCase() === multi.toLowerCase()
  //     );

  //     if (exists) {
  //       setErrorMenu('Menu Name already exists.');
  //       setMenuName('');
  //     } else {
  //       setMenuName(multi); // Set the trimmed and formatted value
  //       setErrorMenu('');
  //     }
  //   } else {
  //     setMenuName(e.target.value); // Set empty value if input is cleared
  //     setErrorMenu('');
  //   }
  // };
  // const onChangeMenuName = e => {
  //   let value = e.target.value;
  //   setErrorMenu('');
  //   setMenuName(value);
  //   setMenuNameCopy(value);
  //   setRequiredText(false);
  // };
  const onBlurMenuName = e => {
    if (e.target.value) {
      // Remove extra spaces and trim the value
      let multi = e.target.value.replace(/\s+/g, ' ').trim();

      // Regex to check for special characters
      const specialCharRegex = /^[a-zA-Z0-9\s]*$/;

      if (!specialCharRegex.test(multi)) {
        setErrorMenu('Special characters are not allowed.');
        setMenuName('');
      } else {
        // Check if the menu name already exists (case insensitive)
        const exists = allMenu.some(
          item => item.menuName.toLowerCase() === multi.toLowerCase()
        );

        if (exists) {
          setErrorMenu('Menu Name already exists.');
          setMenuName('');
        } else {
          setMenuName(multi); // Set the trimmed and formatted value
          setErrorMenu('');
        }
      }
    } else {
      setMenuName(''); // Set empty value if input is cleared
      setErrorMenu('');
    }
  };

  const onChangeMenuName = e => {
    let value = e.target.value;

    // Regex to check for special characters
    const specialCharRegex = /^[a-zA-Z0-9\s]*$/;

    if (!specialCharRegex.test(value)) {
      setErrorMenu('Special characters are not allowed.');
    } else {
      setErrorMenu(''); // Clear the error if input is valid
      setMenuName(value);
      setMenuNameCopy(value);
    }
    setRequiredText(false);
  };

  // const onChangeMenuName = e => {
  //   let value = e.target.value;
  //   // const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s]*$/;
  //   const nameRegex = /^[a-zA-Z0-9\s]*$/; // Allow only letters, numbers, and spaces
  //   if (!nameRegex.test(value) && value) {
  //     let newValue = value.substring(0, value.length - 1);
  //     setRequiredText('');
  //     setMenuName(newValue);
  //     setMenuNameCopy(newValue);
  //   } else {
  //     setErrorMenu('');
  //     setMenuName(e.target.value);
  //     setMenuNameCopy(e.target.value);
  //     setRequiredText(false);
  //   }
  // };

  const formatIndianNumber = numericValue => {
    const formattedValue = new Intl.NumberFormat('en-US').format(numericValue);
    return formattedValue.replace(/^(\D+)(\d+)$/, '$2$1');
  };
  const OnChangeMlgeInterval = e => {
    const inputValue = e.target.value.replace(/[^\d]/g, '');
    if (!/^[1-9]\d*$/.test(inputValue)) {
      setErrorMlInt('Please enter a valid number.');
      setMilegeInterval('');
    } else {
      const numericValue = Number(inputValue);

      setErrorMlInt('');
      setMilegeInterval(formatIndianNumber(numericValue));
    }
  };
  const onChangeMilesBefore = e => {
    const inputValue = e.target.value.replace(/[^\d]/g, '');
    if (!/^[1-9]\d*$/.test(inputValue)) {
      setErrorMlBf('Please enter a valid number.');
      setMilegeBefore('');
    } else {
      const numericValue = Number(inputValue);

      setErrorMlBf('');
      setMilegeBefore(formatIndianNumber(numericValue));
    }
  };
  const onChangeMilesAfter = e => {
    const inputValue = e.target.value.replace(/[^\d]/g, '');
    if (!/^[1-9]\d*$/.test(inputValue)) {
      setErrorMlAf('Please enter a valid number.');
      setMilegeAfter('');
    } else {
      const numericValue = Number(inputValue);
      setErrorMlAf('');
      setMilegeAfter(formatIndianNumber(numericValue));
    }
  };
  const onChangeMaximumMlg = e => {
    const inputValue = e.target.value.replace(/[^\d]/g, '');
    if (!/^[1-9]\d*$/.test(inputValue)) {
      setErrorMlMax('Please enter a valid number.');
      setMaxMilege('');
    } else {
      const numericValue = Number(inputValue);
      setErrorMlMax('');
      setMaxMilege(formatIndianNumber(numericValue));
    }
  };
  const [rowData, setRowData] = useState([]);
  useEffect(() => {
    if (submitClicked) {
      submitData();
    }
  }, [submitClicked]);
  const submitData = event => {
    const apolloClient = makeApolloClientPostgresWrite;
    const start = new Date();
    if (
      maxMilege &&
      menuName &&
      milegeInterval &&
      milegeAfter &&
      milegeBefore &&
      !errorMenu &&
      !errorMlInt &&
      !errorMlBf &&
      !errorMlAf &&
      !errorMlMax &&
      !requiredText
    ) {
      setIsLoading(true);
      setSubmitClicked(false);
      apolloClient
        .mutate({
          mutation: ADD_MENU,
          variables: {
            storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
            username: localStorage.getItem('userID'),
            inMaxMiles: parseFloat(maxMilege.replace(/,/g, '')),
            inMenuname: menuName,
            inMenuInterval: parseFloat(milegeInterval.replace(/,/g, '')),
            inMilesAfter: parseFloat(milegeAfter.replace(/,/g, '')),
            inMilesBefore: parseFloat(milegeBefore.replace(/,/g, '')),
            isdefault: isChecked == true ? 1 : 0
          }
        })
        .then(result => {
          setRequiredText(false);
          setErrorMenu(false);
          setErrorMlInt(false);
          setErrorMlBf(false);
          setErrorMlAf(false);
          setErrorMlMax(false);
          const spanAttribute = {
            pageUrl: '/Mpi',
            origin: '',
            event: 'Menu Load',
            is_from: 'ADD_MENU',
            value: new Date() - start,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Menu Load', spanAttribute);
          setSuccessMsg(`Menu ` + menuName + ` Added Successfully!`);
          setIsLoading(false);
          setopenSnackbar(true);
          setError('');
          setMilegeInterval('');
          setMilegeBefore('');
          setMilegeAfter('');
          setMaxMilege('');
          setMenuName('');
          menuNameData();
        })
        .catch(error => {});
    } else {
      setMaxMilege(errorMlMax ? '' : maxMilege);
      setErrorMenu(true);
      setErrorMlInt(true);
      setErrorMlBf(errorMlBf ? errorMlBf : true);
      setErrorMlAf(errorMlAf ? errorMlAf : true);
      setErrorMlMax(errorMlMax ? errorMlMax : true);
      setSubmitClicked(false);
    }
  };
  const submitMenu = event => {
    const apolloClient = makeApolloClientPostgresWrite;
    const start = new Date();
    if (menuName == undefined || (menuName == '' && errorMenu == '')) {
      setRequiredText(true);
    }
    if (
      milegeInterval == undefined ||
      (milegeInterval == '' && errorMlInt == '')
    ) {
      setRequiredText(true);
    }
    if (milegeBefore == undefined || (milegeBefore == '' && errorMlBf == '')) {
      setRequiredText(true);
    }
    if (milegeAfter == undefined || (milegeAfter == '' && errorMlAf == '')) {
      setRequiredText(true);
    }
    if (maxMilege == undefined || (maxMilege == '' && errorMlMax == '')) {
      setRequiredText(true);
    }
    if (
      (maxMilege &&
        milegeInterval &&
        parseFloat(maxMilege.replace(/,/g, '')) <=
          parseFloat(milegeInterval.replace(/,/g, ''))) ||
      (maxMilege && parseFloat(maxMilege.replace(/,/g, '')) > 500000) ||
      (milegeBefore &&
        milegeInterval &&
        parseFloat(milegeBefore.replace(/,/g, '')) >=
          parseFloat(milegeInterval.replace(/,/g, '')) / 2) ||
      (milegeAfter &&
        milegeInterval &&
        parseFloat(milegeAfter.replace(/,/g, '')) >=
          parseFloat(milegeInterval.replace(/,/g, '')) / 2) ||
      (milegeAfter &&
        milegeInterval &&
        parseFloat(milegeAfter.replace(/,/g, '')) >=
          parseFloat(milegeInterval.replace(/,/g, '')) / 2)
    ) {
      if (
        maxMilege &&
        milegeInterval &&
        parseFloat(maxMilege.replace(/,/g, '')) <=
          parseFloat(milegeInterval.replace(/,/g, ''))
      ) {
        setErrorMlMax('Maximum Miles should be greater than Mileage Interval');
        setMaxMilege('');
        setError(true);
        setSubmitClicked(true);
      }
      if (maxMilege && parseFloat(maxMilege.replace(/,/g, '')) > 500000) {
        setErrorMlMax(' Maximum Miles should be Less than 5,00,000');
        setMaxMilege('');
        setError(true);
        setSubmitClicked(true);
      }
      if (
        milegeBefore &&
        milegeInterval &&
        parseFloat(milegeBefore.replace(/,/g, '')) >=
          parseFloat(milegeInterval.replace(/,/g, '')) / 2
      ) {
        setErrorMlBf(
          'Miles Before should be less than half of the Mileage Interval.'
        );
        setMilegeBefore('');
        setError(true);
        setSubmitClicked(true);
      }
      if (
        milegeAfter &&
        milegeInterval &&
        parseFloat(milegeAfter.replace(/,/g, '')) >=
          parseFloat(milegeInterval.replace(/,/g, '')) / 2
      ) {
        setErrorMlAf(
          'Miles After should be less than half of the Mileage Interval.'
        );
        setMilegeAfter('');
        setError(true);
        setSubmitClicked(true);
      }
    } else {
      setErrorMenu(false);
      setErrorMlInt(false);
      setErrorMlBf(false);
      setErrorMlAf(false);
      setErrorMlMax(false);
      setSubmitClicked(true);
      if (
        milegeInterval &&
        milegeBefore &&
        milegeAfter &&
        maxMilege &&
        menuName
      ) {
        setRequiredText(false);
      }
    }
  };

  const clickDeleteMenu = menuName => {
    setShowMilege(false);
    menuNameData();
  };
  const clickCancelMenu = menuName => {
    setIsDisabledMenu(false);
    setShowMilege(false);
  };
  const clickDetail = (menuName, serviceType) => {
    setIsDisabledMenu(true);
    setShowMilege(false);
    setMServicetype(serviceType);
    var serviceType = serviceType ? serviceType : 0;
    setEdit(serviceType == 0 ? false : true);
    getMenuData(menuName, serviceType, result => {
      if (result.data.statelessCcPhysicalRwGetMenuData.getMdata) {
        setmInteval(result.data.statelessCcPhysicalRwGetMenuData.getMdata[0]);
        let menuData = result.data.statelessCcPhysicalRwGetMenuData.getMdata;
        if (menuData[0]) {
          setShowChild(true);
          setCatArr(menuData[0].categoryList ? menuData[0].categoryList : []);
          setMfrh(menuData[0].mFrh == 0 ? '' : menuData[0].mFrh);
          setMintervalData(menuData[0].mInterval);
          setMitems(menuData[0].mItems == 0 ? '' : menuData[0].mItems);
          setMname(menuData[0].mName);
          setMprice(menuData[0].mPrice == 0 ? '' : menuData[0].mPrice);
          setMServicetype(menuData[0].mServicetype);
          if (menuData[0].mOpcodes) {
            setMOpcodes(
              menuData[0].mOpcodes ? JSON.parse(menuData[0].mOpcodes) : []
            );
          } else {
            setMOpcodes([]);
          }
          setMSeries(JSON.parse(menuData[0].mSeries));
          setShowMilege(true);
        }
      }
    });
  };
  const saveInterval = status => {
    if (status == true) {
      setIsDisabledMenu(false);
    }
    menuNameData();
    setShowMilege(false);
  };
  const hidesnackbar = () => {
    setopenSnackbar(false);
  };
  const handleCheckboxChange = event => {
    setIsChecked(event.target.checked);
  };
  return (
    <div>
      <Grid xs={12} className={classes.textContainerGrid} container spacing={2}>
        <Grid xs={4}>
          <Paper style={{ height: '100%', padding: 20, boxShadow: 'none' }}>
            <Card
              sx={{ margin: '20px 0 0 0' }}
              className={classes.card}
              style={{
                overflowY: requiredText || error ? 'auto' : ''
              }}
            >
              <span style={{ display: 'flex', marginTop: '15px' }}>
                <Typography
                  style={{ padding: 8, marginLeft: 135 }}
                  variant="h6"
                  className={clsx(classes.mainLabel)}
                >
                  {'Create Menu'}
                </Typography>
              </span>

              <CardContent className={classes.contentcred}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={2} className={classes.labelPos}>
                    <Typography variant="body1" className={classes.label}>
                      Menu Name <span className={classes.star}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <TextField
                      required
                      variant="outlined"
                      size="small"
                      className={classes.txt}
                      onChange={onChangeMenuName}
                      onBlur={onBlurMenuName}
                      value={menuName}
                      helperText={
                        (menuName == undefined || menuName == '') &&
                        requiredText
                          ? 'This is required!'
                          : errorMenu
                          ? errorMenu
                          : ''
                      }
                      inputProps={{ maxlength: 150 }}
                      InputProps={{
                        style: { borderColor: errorMenu ? 'red' : '' }
                      }}
                      FormHelperTextProps={{
                        style: {
                          width: '150px' // Adjust the width as needed
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={2} className={classes.labelPos}>
                    <Typography variant="body1" className={classes.label}>
                      Mileage Interval<span className={classes.star}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <TextField
                      required
                      variant="outlined"
                      size="small"
                      className={classes.txt}
                      onChange={OnChangeMlgeInterval}
                      value={milegeInterval}
                      helperText={
                        !milegeInterval && requiredText
                          ? 'This is required!'
                          : errorMlInt
                      }
                      InputProps={{
                        style: { borderColor: errorMlInt ? 'red' : undefined }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={2} className={classes.labelPos}>
                    <Typography variant="body1" className={classes.label}>
                      Miles Before <span className={classes.star}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <TextField
                      required
                      variant="outlined"
                      size="small"
                      className={classes.txt}
                      onChange={onChangeMilesBefore}
                      value={milegeBefore}
                      helperText={
                        !milegeBefore && requiredText && errorMlBf == true
                          ? 'This is required!'
                          : errorMlBf
                      }
                    />
                  </Grid>
                  <Grid item xs={12} sm={2} className={classes.labelPos}>
                    <Typography variant="body1" className={classes.label}>
                      Miles After <span className={classes.star}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <TextField
                      required
                      variant="outlined"
                      size="small"
                      className={classes.txt}
                      onChange={onChangeMilesAfter}
                      value={milegeAfter}
                      helperText={
                        !milegeAfter && requiredText && errorMlAf == true
                          ? 'This is required!'
                          : errorMlAf
                      }
                    />
                  </Grid>
                  <Grid item xs={12} sm={2} className={classes.labelPos}>
                    <Typography variant="body1" className={classes.label}>
                      Maximum Miles<span className={classes.star}>*</span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <TextField
                      required
                      variant="outlined"
                      size="small"
                      className={classes.txt}
                      onChange={onChangeMaximumMlg}
                      value={maxMilege}
                      helperText={
                        !maxMilege && requiredText && errorMlMax == true
                          ? 'This is required!'
                          : errorMlMax
                      }
                    />
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sm={2}
                    className={classes.labelPos}
                    style={{ paddingLeft: 0 }}
                  >
                    <Typography variant="body1" className={classes.label}>
                      Default Menu<span className={classes.star}></span>
                    </Typography>
                  </Grid>
                  <Grid item xs={12} sm={10}>
                    <Checkbox
                      checked={isChecked}
                      onChange={handleCheckboxChange}
                      color="primary"
                      style={{ left: 28, paddingTop: 7 }}
                      size="small"
                      disabled={allMenu.length == 0 ? true : false}
                    />
                  </Grid>

                  <Grid item xs={12} sm={2}></Grid>
                  <Grid item xs={12} sm={10}>
                    <Button
                      variant="contained"
                      className={clsx('reset-btn', classes.btnDiv)}
                      color="primary"
                      onClick={submitMenu}
                    >
                      Create
                    </Button>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Paper>
        </Grid>
        <Grid xs={7} sm={7}>
          <Paper style={{ height: '100%', padding: 20, boxShadow: 'none' }}>
            <Card sx={{ margin: '20px 0 0 0' }} className={classes.cards}>
              {' '}
              <Grid item xs={12}>
                <span style={{ display: 'flex', width: '100%' }}>
                  <Typography
                    style={{ padding: 8, marginLeft: '44%', marginTop: 15 }}
                    variant="h6"
                    className={clsx(classes.mainLabel)}
                  >
                    Available Menus
                  </Typography>
                </span>
              </Grid>
              <CardContent
                className={classes.contentcredMenuData}
                style={{
                  opacity: isDisabledMenu ? 0.5 : 1,
                  pointerEvents: isDisabledMenu ? 'none' : 'auto'
                }}
              >
                <MenuData
                  allMenu={allMenu}
                  clickDetail={clickDetail}
                  isLoading={isLoading}
                  clickDeleteMenu={clickDeleteMenu}
                />
              </CardContent>
            </Card>
          </Paper>
        </Grid>
      </Grid>
      {showMilege && (
        <MenuDetails
          session={props.session}
          milegeData={milegeData}
          milegeDataInterval={milegeDataInterval}
          showChild={showChild}
          menuName={menuNameCopy}
          clickDetail={clickDetail}
          saveInterval={saveInterval}
          mInterval={minteval}
          mfrh={mfrh}
          mintervalData={mintervalData}
          mitems={mitems}
          mname={mname}
          mprice={mprice}
          mServicetype={mServicetype}
          mOpcodes={mOpcodes}
          mSeries={mSeries}
          isLoading={isLoading}
          catArr={catArr}
          edit={edit}
          clickCancelMenu={clickCancelMenu}
        />
      )}
      {successMsg && !error && (
        <SuccessSnackbar
          onClose={hidesnackbar}
          open={openSnackbar}
          msg={successMsg}
        />
      )}
    </div>
  );
};

export default withKeycloak(Menu);
