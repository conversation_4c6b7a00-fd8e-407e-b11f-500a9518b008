import React from 'react';
import {
  <PERSON><PERSON>,
  Typo<PERSON>,
  Dialog,
  Grid,
  CircularProgress,
  Checkbox
} from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import Accordion from '@material-ui/core/Accordion';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { menuDetails } from 'src/utils/hasuraServices';
import clsx from 'clsx';
import {
  DELETE_MENU_MASTER,
  DELETE_MENU,
  EDIT_MENU
} from 'src/graphql/queries';
import Alert from '@material-ui/lab/Alert';
import CloseIcon from '@material-ui/icons/Close';
import IconButton from '@material-ui/core/IconButton';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import DeleteDialog from './DeleteDialog';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import EditIcon from '@material-ui/icons/Edit';
import Tooltip from '@material-ui/core/Tooltip';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import MenuDialog from 'src/views/Mpi/MenuDialog';
const useStyles = makeStyles(theme => ({
  checked: {
    '&.Material-ui-checked': {
      backgroundColor: 'red'
    }
  },
  root: {
    width: '100%'
  },
  noData: {
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  loaderGrid: {
    height: 300,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  headingSub: {
    fontSize: theme.typography.pxToRem(13),
    flexBasis: '19.33%',
    flexShrink: 0,
    color: 'rgb(0, 61, 107)',
    width: '17.8%',
    fontWeight: 550
  },
  headingSubData: {
    fontSize: theme.typography.pxToRem(13),
    flexBasis: '19.33%',
    flexShrink: 0,
    width: '17.8%'
  },
  headingSub1: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '99.33%',
    flexShrink: 0,
    color: 'rgb(0, 61, 107)'
  },
  headingSub2: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '99.33%',
    flexShrink: 0,
    color: 'rgb(0, 61, 107)'
  },
  heading: {
    fontSize: theme.typography.pxToRem(13),
    flexBasis: '61.33%',
    flexShrink: 0,
    color: 'rgb(0, 61, 107)',
    fontWeight: 550
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  frhValue: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '20.33%',
    flexShrink: 0,
    marginLeft: 11
  },
  category: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary,
    marginLeft: -16
  },
  accordDet: {
    marginTop: 5
  },
  accordDetail: {
    marginTop: 5,
    width: '100%'
  },
  btnDivMenu: {
    fontSize: 12
  },
  expandIcon: {
    marginBottom: '0.5px'
  },
  menuItem: {
    display: 'flex'
  },
  accItem: {
    padding: 0
  },
  menuData: {
    alignContent: 'center'
  }
}));

export default function ControlledAccordions(props) {
  const classes = useStyles();
  const [expanded, setExpanded] = React.useState(false);
  const [expandedSub, setExpandedSub] = React.useState(false);
  const [expandedSub1, setExpandedSub1] = React.useState(false);
  const [expandedSub2, setExpandedSub2] = React.useState(false);
  const [intervalList, setIntervalList] = React.useState([]);
  const [jsonGeneral, setJsonGeneral] = React.useState([]);
  const [jsonOpcodeList, setJsonOpcodeList] = React.useState([]);
  const [frh, setFrh] = React.useState('');
  const [items, setItems] = React.useState('');
  const [price, setPrice] = React.useState('');
  const [menu_name, setMenu_name] = React.useState('');
  const [service_type_id, setService_type_id] = React.useState('');
  const [milege_interval, setMilege_interval] = React.useState('');
  const [jsonGeneralData, setJsonGeneralData] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(true);
  const [openAlert, setOpenAlert] = React.useState(false);
  const [alertMsg, setAlertMsg] = React.useState('');
  const [openDelete, setOpenDelete] = React.useState(false);
  const [openDelInt, setOpenDelInt] = React.useState(false);
  const [menuIntName, setMenuIntName] = React.useState('');
  const [menuIntServTyp, setMenuIntServTyp] = React.useState('');
  const [menu, setMenu] = React.useState('');
  const [selectedTab, setSelectedTab] = React.useState();
  const [alertType, setAlertType] = React.useState('');
  const [openStore, setOpenStore] = React.useState(false);
  const [selectedMenu, setSelectedMenu] = React.useState('');
  const [isChecked, setIsChecked] = React.useState(false);
  const handleChange = panel => (event, isExpanded) => {
    setJsonGeneralData([]);
    setIsLoading(false);
    setFrh('');
    setItems('');
    setPrice('');
    setMenu_name('');
    setService_type_id('');
    setMilege_interval('');
    setJsonOpcodeList([]);
    setIntervalList([]);
    setExpanded(isExpanded ? panel : false);
    if (panel.menuName) {
      menuDetails(panel.menuName, result => {
        if (
          result.data.statelessCcPhysicalRwGetMenuDetails.getMenudetails &&
          result.data.statelessCcPhysicalRwGetMenuDetails.getMenudetails[0] !=
            null
        ) {
          var resultArr =
            result.data.statelessCcPhysicalRwGetMenuDetails.getMenudetails;
          if (resultArr[0]) {
            let jsonGeneralData = JSON.parse(
              resultArr[0].jsonGeneral ? resultArr[0].jsonGeneral : []
            );
            if (resultArr[0].intervalList) {
              setIntervalList(JSON.parse(resultArr[0].intervalList));
            }
            setIntervalList(
              resultArr[0].intervalList
                ? JSON.parse(resultArr[0].intervalList)
                : []
            );
            setJsonGeneral(
              resultArr[0].jsonGeneral
                ? JSON.parse(resultArr[0].jsonGeneral)
                : []
            );
            setJsonOpcodeList(
              resultArr[0].jsonOpcodeList
                ? JSON.parse(resultArr[0].jsonOpcodeList)
                : []
            );
            setJsonGeneralData(jsonGeneralData ? jsonGeneralData : []);
            setIsLoading(true);
            if (jsonGeneralData[0]) {
              setFrh(jsonGeneralData[0].frh);
              setItems(jsonGeneralData[0].items);
              setPrice(jsonGeneralData[0].price);
              setMenu_name(jsonGeneralData[0].menu_name);
              setService_type_id(jsonGeneralData[0].service_type_id);
              setMilege_interval(jsonGeneralData[0].milegae_interval);
            }
          }
        } else {
          setIsLoading(true);
        }
      });
    }
  };
  const handleChangeSub = panel => (event, isExpandedSub) => {
    setExpandedSub(isExpandedSub ? panel : false);
  };
  const handleChangeSub1 = panel => (event, isExpandedSub1) => {
    setExpandedSub1(isExpandedSub1 ? panel : false);
  };
  const handleChangeSub2 = panel => (event, isExpandedSub2) => {
    setExpandedSub2(isExpandedSub2 ? panel : false);
  };
  const handleCloseDelete = () => {
    setOpenDelete(false);
    setOpenDelInt(false);
  };
  const clickDetail = event => {
    props.clickDetail(event);
  };
  const confirmDeleteInt = () => {
    setTimeout(() => {
      handleDeleteMenuInt();
    }, 500);
  };
  const clickDelete = (menuNmae, serviceType, milegae_interval, event) => {
    setSelectedTab('inner');

    setOpenDelInt(true);
    setMenuIntName(menuNmae);
    setMenuIntServTyp(serviceType);
  };
  const handleDeleteMenuInt = () => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: DELETE_MENU_MASTER,
        variables: {
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          username: localStorage.getItem('userID'),
          inMenuname: menuIntName,
          serviceType: menuIntServTyp
        }
      })
      .then(result => {
        props.clickDeleteMenu(menuIntName);
        setOpenAlert(true);
        setOpenDelInt(false);
        setAlertMsg(menuIntName + ` Menu Interval Deleted`);
        setAlertType('success');
        setTimeout(() => {
          setOpenAlert(false);
        }, 2000);
      });
  };
  const confirmDelete = () => {
    setTimeout(() => {
      handleDeleteMenu();
    }, 500);
  };
  const handleDeleteMenu = () => {
    const client = makeApolloClient;
    client
      .mutate({
        mutation: DELETE_MENU,
        variables: {
          storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
          username: localStorage.getItem('userID'),
          inMenuname: menu
        }
      })
      .then(result => {
        props.clickDeleteMenu(menu);
        setOpenAlert(true);
        setOpenDelete(false);
        setAlertMsg(menu + ` Menu Deleted`);
        setAlertType('success');
        setTimeout(() => {
          setOpenAlert(false);
        }, 1000);
      });
  };
  const deleteInterval = (menuNmae, menuIsdefault, menuLength, event) => {
    setExpanded(expanded);
    if (menuIsdefault == '1' && menuLength != 1) {
      setOpenAlert(true);
      setOpenDelInt(false);
      setAlertMsg('You cannot delete the default menu.');
      setAlertType('warning');
      setTimeout(() => {
        setOpenAlert(false);
      }, 2000);
    } else {
      setMenu(menuNmae);
      setOpenDelete(true);
      setSelectedTab('');
    }
  };
  const clickEdit = (menuNmae, serviceType) => {
    props.clickDetail(menuNmae, serviceType);
  };
  const showMenuModal = data => {
    setOpenStore(true);
    setSelectedMenu(data);
  };
  const handleCloseStore = () => {
    setOpenStore(false);
  };
  const handleCheckboxChange = (event, menuIsdefault) => {
    setExpanded(expanded);
    let menuName = event.target.id;
    let isdefault = event.target.value == false ? 1 : 0;
    if (menuIsdefault == 0) {
      const client = makeApolloClient;
      client
        .mutate({
          mutation: EDIT_MENU,
          variables: {
            storeid: JSON.parse(localStorage.getItem('selectedStoreId'))[0],
            username: localStorage.getItem('userID'),
            inMenuname: menuName,
            isdefault: isdefault
          }
        })
        .then(result => {
          setOpenAlert(true);
          setOpenDelInt(false);
          setAlertMsg('Default Menu Updated');
          setAlertType('success');
          setTimeout(() => {
            setOpenAlert(false);
          }, 2000);
          props.clickDeleteMenu(menuName);
        });
    }
  };
  const trimMenu = menuName => {
    if (menuName.length > 20) {
      return menuName.substring(0, 20) + '...';
    }
    return menuName;
  };
  return (
    <>
      <Dialog
        open={openAlert}
        classes={{
          paper: classes.dialog
        }}
      >
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Dialog>

      <div className={classes.root}>
        {props.isLoading == true ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : props.allMenu.length > 0 && props.isLoading == false ? (
          props.allMenu.map((item, index) => (
            <Accordion
              expanded={expanded === item}
              onChange={handleChange(item)}
            >
              <AccordionSummary
                aria-controls="panel4bh-content"
                id="panel4bh-header"
                className={classes.accItem}
              >
                <Grid xs={12} className={classes.menuItem}>
                  <Grid xs={1}>
                    <ExpandMoreIcon className={classes.expandIcon} />
                  </Grid>
                  <Grid xs={7} className={classes.menuData}>
                    <Typography
                      title={item.menuName}
                      className={classes.heading}
                      variant="h6"
                    >
                      <p>{trimMenu(item.menuName)}</p>
                      <Tooltip title="View Menu Details">
                        <OpenInNewOutlinedIcon
                          htmlColor="rgb(0, 61, 107)"
                          style={{
                            borderRadius: '3px',
                            width: '14px',
                            height: '14px',
                            border: 0,
                            fontSize: '12px',
                            cursor: 'pointer',
                            lineHeight: '13px',
                            marginLeft: 5,
                            position: 'absolute',
                            marginTop: 2
                          }}
                          value={item.menuName}
                          onClick={() =>
                            showMenuModal(item.menuName ? item.menuName : '')
                          }
                        />
                      </Tooltip>
                    </Typography>
                  </Grid>

                  <Grid xs={4}>
                    <Tooltip title="Default Menu">
                      <Checkbox
                        checked={item.menuIsdefault == '1' ? true : false}
                        onChange={event =>
                          handleCheckboxChange(event, item.menuIsdefault)
                        }
                        value={item.menuIsdefault == '1' ? true : false}
                        id={item.menuName}
                        style={{
                          cursor: 'pointer',
                          height: 20,
                          position: 'absolute',
                          right: 204
                        }}
                        size="small"
                      />
                    </Tooltip>
                    <Button
                      variant="contained"
                      className={clsx('reset-btn', classes.btnDivMenu)}
                      color="primary"
                      value={item.menuName}
                      onClick={() => clickDetail(item.menuName)}
                      disabled={item.menuEnable == 0 ? true : false}
                      style={{
                        height: 24,
                        fontSize: 12,
                        position: 'absolute',
                        right: 110
                      }}
                    >
                      Create Interval
                    </Button>
                    <Tooltip title="Delete Menu">
                      <DeleteIcon
                        value={item.menuName}
                        onClick={() =>
                          deleteInterval(
                            item.menuName,
                            item.menuIsdefault,
                            props.allMenu.length
                          )
                        }
                        style={{
                          background: '#384163',
                          color: '#fff',
                          borderRadius: '3px',
                          width: '30px',
                          height: '24px',
                          border: 0,
                          fontSize: '12px',
                          cursor: 'pointer',
                          lineHeight: '13px',
                          marginLeft: 6,
                          position: 'absolute',
                          right: 75
                        }}
                      ></DeleteIcon>
                    </Tooltip>
                  </Grid>
                </Grid>
              </AccordionSummary>
              <AccordionDetails className={classes.accordDetail}>
                <Accordion
                  style={{ width: '100%', height: 'auto', marginTop: 5 }}
                >
                  <AccordionSummary
                    aria-controls="panel4bh-content"
                    id="panel4bh-header2"
                  >
                    <div style={{ width: '100%', marginTop: 30 }}>
                      {isLoading == false ? (
                        <Grid justify="center" className={classes.loaderGrid}>
                          <CircularProgress size={60} />
                        </Grid>
                      ) : jsonGeneralData.length > 0 && isLoading == true ? (
                        <Typography variant="subtitle1" style={{ height: 50 }}>
                          <Typography className={classes.headingSub}>
                            <span>Miles</span>{' '}
                          </Typography>
                          <Typography className={classes.headingSub}>
                            Category
                          </Typography>
                          <Typography className={classes.headingSub}>
                            $ Sales
                          </Typography>
                          <Typography className={classes.headingSub}>
                            Flat Rate Hours
                          </Typography>
                          <Typography className={classes.headingSub}>
                            Items
                          </Typography>
                        </Typography>
                      ) : (
                        <Typography
                          variant="subtitle1"
                          style={{ height: 50, textAlign: 'center' }}
                        >
                          <Typography className={classes.noInterval}>
                            No Intervals Created
                          </Typography>
                        </Typography>
                      )}
                      {jsonGeneralData.map((item, index) => (
                        <Typography variant="subtitle1" style={{ height: 50 }}>
                          <Typography className={classes.headingSubData}>
                            {item.milegae_interval}
                          </Typography>
                          <Typography className={classes.headingSubData}>
                            {item.service_type}
                          </Typography>
                          <Typography className={classes.headingSubData}>
                            ${item.price}
                          </Typography>
                          <Typography className={classes.headingSubData}>
                            {item.frh}
                          </Typography>
                          <Typography className={classes.headingSubData}>
                            {item.items}
                          </Typography>
                          <Tooltip title="Edit">
                            <EditIcon
                              value={item.menu_name}
                              onClick={() =>
                                clickEdit(item.menu_name, item.service_type_id)
                              }
                              style={{
                                background: '#384163',
                                color: '#fff',
                                borderRadius: '3px',
                                width: '30px',
                                height: '24px',
                                border: 0,
                                fontSize: '12px',
                                cursor: 'pointer',
                                lineHeight: '13px',
                                marginLeft: -15
                              }}
                            ></EditIcon>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <DeleteIcon
                              value={item.menu_name}
                              onClick={() =>
                                clickDelete(
                                  item.menu_name,
                                  item.service_type_id,
                                  item.milegae_interval
                                )
                              }
                              style={{
                                background: '#384163',
                                color: '#fff',
                                borderRadius: '3px',
                                width: '30px',
                                height: '24px',
                                border: 0,
                                fontSize: '12px',
                                cursor: 'pointer',
                                lineHeight: '13px',
                                marginLeft: 6
                              }}
                            ></DeleteIcon>
                          </Tooltip>
                        </Typography>
                      ))}
                    </div>
                  </AccordionSummary>
                </Accordion>
              </AccordionDetails>
            </Accordion>
          ))
        ) : (
          <Accordion>
            <AccordionSummary
              aria-controls="panel1a-content"
              id="panel1a-header"
            >
              <Typography className={clsx(classes.noData)}>
                No Menus Available
              </Typography>
            </AccordionSummary>
          </Accordion>
        )}
      </div>
      <DeleteDialog
        selectedTab={selectedTab}
        openDelInt={openDelInt}
        openPopup={openDelete}
        menuName={menu}
        handlePopupClose={handleCloseDelete}
        handleDeleteMenu={confirmDelete}
        confirmDeleteInt={confirmDeleteInt}
      ></DeleteDialog>
      <MenuDialog
        openPopup={openStore}
        handlePopupClose={handleCloseStore}
        menuName={selectedMenu}
      ></MenuDialog>
    </>
  );
}
