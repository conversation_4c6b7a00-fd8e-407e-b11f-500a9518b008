import React, { useState, useEffect } from 'react';
import {
  TextField,
  Button,
  Grid,
  Card,
  CardContent,
  Typography,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  InputLabel,
  Paper,
  Checkbox,
  Dialog,
  Tooltip
} from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import clsx from 'clsx';
import { getMenuServiceType } from 'src/utils/hasuraServices';
import { withKeycloak } from '@react-keycloak/web';
import MenuOpcode from 'src/views/Mpi/MenuOpcode';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { INSERT_MENU_DETAILS } from 'src/graphql/queries';
import Alert from '@material-ui/lab/Alert';
import CloseIcon from '@material-ui/icons/Close';
import IconButton from '@material-ui/core/IconButton';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import MenuDialog from 'src/views/Mpi/MenuDialog';
const useStyles = makeStyles(theme => ({
  textContainerGrid: {
    gap: 10,
    marginBottom: 5,
    paddingBottom: 5
  },
  mainLabel: {
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },

  card: {
    width: '91.2%',
    marginLeft: '11px',
    marginTop: '28px',
    height: '850px'
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 13,
    marginTop: '16px',
    width: '100px'
  },
  txt: {
    width: '200px',
    marginLeft: 30
  },
  btnDiv: {
    width: '100px',
    marginLeft: 1,
    marginTop: 20
  },
  labelPos: {
    textAlign: 'right'
  },
  star: {
    color: 'red',
    padding: '5px'
  },
  contentcred: {
    width: '100%',
    margin: 'auto'
  },
  dialog: {
    position: 'absolute',
    top: 50
  },
  error: {
    margin: 0,
    fontSize: '90%',
    color: 'tomato',
    marginTop: '20px',
    marginLeft: '15px'
  },
  rolelabel: {
    padding: '8px 0',
    color: '#001837',
    fontWeight: 500
  },
  TextField: {
    marginTop: 12,
    '& label': {
      color: '#212121 !important'
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#7575753d',
        borderRadius: 0
      },
      '&:hover fieldset': {
        borderColor: '#7575753d',
        borderRadius: 0
      },
      '&.Mui-focused fieldset': {
        borderColor: '#7575753d',
        borderRadius: 0
      }
    }
  }
}));
const MenuDetails = props => {
  const classes = useStyles();
  const [gridColumnApi, setGridColumnApi] = React.useState(null);
  const [value, setValue] = React.useState(new Date());
  const [isLoading, setIsLoading] = React.useState(true);
  const [rawGridApi, setRawGridApi] = React.useState({});
  const [gridApi, setGridApi] = React.useState({});
  const [sales, setSales] = React.useState(props.mprice ? props.mprice : '');
  const [frh, setFrh] = React.useState(props.mfrh ? props.mfrh : '');
  const [itemValue, setItemValue] = React.useState(
    props.mitems ? props.mitems : ''
  );
  const [values, setValues] = React.useState(parseInt(props.mServicetype));
  const [errorSale, setErrorSale] = React.useState('');
  const [errorFrh, setErrorFrh] = React.useState('');
  const [errorItem, setErrorItem] = React.useState('');
  const [rowData, setRowData] = React.useState([]);
  const [category, setCategory] = React.useState([]);
  const [checkedItems, setCheckedItems] = useState([]);
  const [checkedInterval, setCheckedInterval] = useState([]);
  const [opcodeData, setOpcodeData] = useState(null);
  const [assignedStatusValues, setAssignedStatusValues] = useState([]);
  const [disableStatusValues, setDisableStatusValues] = useState([]);
  const [openAlert, setOpenAlert] = React.useState(false);
  const [alertMsg, setAlertMsg] = React.useState('');
  const [alertType, setAlertType] = React.useState('');
  const [seriesArr, setSeriesArr] = useState([]);
  const [selectedOpcode, setSelectedOpcode] = useState([]);
  const [salesCheck, setSalesCheck] = React.useState(false);
  const [frhCheck, setFrhCheck] = React.useState(false);
  const [itemCheck, setItemCheck] = React.useState(false);
  const [intervalCheck, setIntervalCheck] = React.useState(false);
  const [openStore, setOpenStore] = React.useState(false);
  const [selectedMenu, setSelectedMenu] = React.useState('');
  const [checkOpcode, setCheckOpcode] = React.useState(false);
  const [disabled, setDisabled] = React.useState(false);
  useEffect(() => {
    if (props.catArr && props.catArr.length > 0) {
      let i = 0;
      category.map((cat, catIndex) => {
        if (props.edit == false) {
          if (props.catArr.includes(cat.id)) {
          } else {
            if (i == 0) {
              i++;
              return setValues(cat.id);
            }
          }
        }
      });
    }
  }, [props.catArr, category]);
  useEffect(() => {
    setSales(props.mprice);
    setFrh(props.mfrh);
    setItemValue(props.mitems);
    setValues(parseInt(props.mServicetype));
    let seriesArr = [];
    let selOpcode = [];
    if (props.mSeries) {
      seriesArr = props.mSeries.filter(item => item.ratings != null);
      setSeriesArr(seriesArr);
    }
    if (props.mSeries) {
      seriesArr = props.mSeries.filter(item => item.ratings != null);
      setSeriesArr(seriesArr);
    }
    if (props.mOpcodes) {
      setSelectedOpcode(props.mOpcodes);
      setOpcodeData(props.mOpcodes ? props.mOpcodes : []);
      if (props && props.mOpcodes) {
        const menuOpcodes = props.mOpcodes.map(item => item.menu_opcode);
        setOpcodeData(menuOpcodes);
      }
    }
    var position = window.innerHeight * (4 / 5);
    window.scrollTo({
      top: position,
      behavior: 'smooth'
    });
  }, [props.mprice, props.mSeries, props.mOpcodes]);

  useEffect(() => {
    getMenuServiceType(result => {
      if (result.data.statelessCcPhysicalRwGetMenuServiceType.nodes) {
        setCategory(result.data.statelessCcPhysicalRwGetMenuServiceType.nodes);
        if (result.data.statelessCcPhysicalRwGetMenuServiceType.nodes[0]) {
          if (result.data.statelessCcPhysicalRwGetMenuServiceType.nodes[0].id) {
            if (parseInt(props.mServicetype) == 0) {
              setValues(
                parseInt(
                  result.data.statelessCcPhysicalRwGetMenuServiceType.nodes[0]
                    .id
                )
              );
            }
          }
        }
      }
    });
  }, [props.mServicetype]);

  useEffect(() => {
    if (props.mSeries && props.mSeries.length > 0) {
      const filteredAssignedStatusValues = props.mSeries
        .filter(item => item.assigned_status === 1)
        .map(item => item.ratings);
      setAssignedStatusValues(filteredAssignedStatusValues);
      const FilterDisables = props.mSeries
        .filter(
          item =>
            item.assigned_status === 1 &&
            props.mServicetype !== item.service_type
        )
        .map(item => item.ratings);
      setDisableStatusValues(FilterDisables);
      setAssignedStatusValues(filteredAssignedStatusValues);
    }
  }, [props.mSeries]);
  const handleChangeSales = e => {
    const re = /^[+-]?(\d{1,5})(\.\d{0,2})?$/;
    if (re.test(e.target.value) || e.target.value === '') {
      setDisabled(false);
      setErrorSale('');
      setSales(e.target.value);
    } else {
      if (e.target.value === '.') {
        setSales('0.');
      } else {
        setErrorSale('Please enter valid Sales value');
        setSales('');
      }
    }
  };

  const handleChangeFrh = e => {
    const re = /^[+-]?(\d{1,5})(\.\d{0,2})?$/;
    if (re.test(e.target.value) || e.target.value === '') {
      setDisabled(false);
      setErrorFrh('');
      setFrh(e.target.value);
    } else {
      if (e.target.value === '.') {
        setFrh('0.');
      } else {
        setErrorFrh('Please enter valid Flat Rate Hours');
        setFrh('');
      }
    }
  };

  const handleChangeItem = e => {
    const re = /^[+-]?(\d{1,5})(\.\d{0,2})?$/;
    if (re.test(e.target.value) || e.target.value === '') {
      setDisabled(false);
      setErrorItem('');
      setItemValue(e.target.value);
    } else {
      if (e.target.value === '.') {
        setItemValue('0.');
      } else {
        setErrorItem('Please enter valid Item value');
        setItemValue('');
      }
    }
  };

  const handleChange = event => {
    let check = parseInt(event.target.value);
    setValues(check);
  };
  const handleCheckboxChange = (colIndex, column) => {
    setDisabled(false);
    var assignStatus = assignedStatusValues;
    var arr = assignedStatusValues;
    if (
      typeof assignStatus != 'undefined' &&
      assignStatus.some(function(el) {
        return el == column.ratings;
      })
    ) {
      arr = assignStatus.filter(function(obj) {
        return obj != column.ratings;
      });
    } else {
      arr.push(column.ratings);
    }
    var arrCheck = [];
    arrCheck = arr.filter(obj => !disableStatusValues.includes(obj));
    setAssignedStatusValues(arr);
    setCheckedInterval(arrCheck);
    const newCheckedItems = [...checkedItems];
    newCheckedItems[colIndex] = !newCheckedItems[colIndex];
    setCheckedItems(newCheckedItems);
  };
  const handleChildData = dataFromChild => {
    setDisabled(false);
    setOpcodeData(dataFromChild);
  };

  const areArraysEqualOpcode = (arr1, arr2) => {
    if (arr1.length !== arr2.length) {
      return false;
    }
    for (let i = 0; i < arr1.length; i++) {
      const obj1 = arr1[i];
      const obj2 = arr2[i];
      if (
        obj1.opcode !== obj2.menu_opcode ||
        obj1.category !== obj2.service_type
      ) {
        return false;
      }
    }
    return true;
  };
  const arraysEqual = (arr1, arr2) => {
    // Check if lengths are the same
    if (arr1.length !== arr2.length) return false;

    // Check each pair of objects
    for (let i = 0; i < arr1.length; i++) {
      const obj1 = arr1[i];
      const obj2 = arr2[i];

      // Check if `ratings` matches `series_freq` and `service_type` matches `category`
      if (
        obj1.ratings !== obj2.series_freq ||
        obj1.service_type !== obj2.category
      ) {
        return false;
      }
    }

    return true;
  };
  const handleSave = () => {
    const userName = localStorage.getItem('userID');
    const storeIds = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const numericPrice = parseFloat(props.mprice);
    const numericItems = parseFloat(itemValue);
    const generatedJSON = {};
    generatedJSON.menu_name = props.mname;
    generatedJSON.m_sales = parseFloat(sales);
    generatedJSON.m_frh = parseFloat(frh);
    generatedJSON.m_items = numericItems;
    generatedJSON.m_storeid = storeIds;
    generatedJSON.m_category = values;
    var opcodeObj = {};
    var opcodeArr = [];
    opcodeData &&
      opcodeData.map(item => {
        if (item.opcode) {
          var opcodeObj = {
            opcode: item.opcode,
            category: values
          };
          opcodeArr.push(opcodeObj);
        } else {
          var opcodeObj = {
            opcode: item,
            category: values
          };
          opcodeArr.push(opcodeObj);
        }
      });
    var seriesObj = {};
    var seriesValueArr = [];
    var menuDataArr = [];
    if (checkedInterval == null || checkedInterval.length == 0) {
      var arrCheckd = assignedStatusValues.filter(
        obj => !disableStatusValues.includes(obj)
      );
      arrCheckd &&
        arrCheckd.map(item => {
          var seriesObj = {
            series_freq: item,
            category: values
          };
          seriesValueArr.push(seriesObj);
        });
    } else {
      checkedInterval &&
        checkedInterval.map(item => {
          var seriesObj = {
            series_freq: item,
            category: values
          };
          seriesValueArr.push(seriesObj);
        });
    }
    generatedJSON.opcodes = opcodeArr;
    generatedJSON.m_series = seriesValueArr;
    const client = makeApolloClient;
    if (parseFloat(sales) > 0) {
    } else {
      setErrorSale('Please enter the sales value.');
      setSalesCheck(true);
    }
    if (parseFloat(frh) > 0) {
    } else {
      setErrorFrh('FRH value must be greater than 0');
      setFrhCheck(true);
    }
    if (numericItems > 0) {
    } else {
      setErrorItem('Items value must be greater than 0');
      setItemCheck(true);
    }
    if (parseFloat(sales) > 0 && parseFloat(frh) > 0 && numericItems > 0) {
      setSalesCheck(false);
      setFrhCheck(false);
      setItemCheck(false);
    }
    let seriesArr = [];
    if (props.mSeries) {
      seriesArr = props.mSeries.filter(item => item.ratings != null);
    }
    const opcodeEqual = areArraysEqualOpcode(opcodeArr, props.mOpcodes);
    const extractedData = seriesArr.filter(
      item => item.assigned_status === 1 && item.service_type === values
    );
    console.log('extractedData===', extractedData, seriesValueArr);
    const seriesEqual = arraysEqual(extractedData, seriesValueArr);
    // extractedData.length === seriesValueArr.length &&
    // extractedData.every((item, index) => {
    //   return (
    //     item.series_freq === seriesValueArr[index].ratings &&
    //     item.category === seriesValueArr[index].service_type
    //   );
    // });

    if (seriesValueArr.length > 0) {
      setIntervalCheck(false);
    } else {
      setIntervalCheck(true);
    }

    if (opcodeArr.length > 0) {
      setCheckOpcode(false);
    } else {
      setCheckOpcode(true);
    }
    console.log('opcodeEqual==', seriesEqual, extractedData, seriesValueArr);
    if (
      sales != props.mprice ||
      frh != props.mfrh ||
      itemValue != props.mitems ||
      opcodeEqual == false ||
      seriesEqual == false
    ) {
      if (
        seriesValueArr.length > 0 &&
        opcodeArr.length > 0 &&
        parseFloat(sales) > 0 &&
        parseFloat(frh) > 0 &&
        numericItems > 0
      ) {
        client
          .mutate({
            mutation: INSERT_MENU_DETAILS,
            variables: {
              menudata: JSON.stringify(generatedJSON),
              username: userName,
              storeid: storeIds
            }
          })
          .then(result => {
            setOpenAlert(true);
            setAlertMsg(props.mname + `  Interval Saved Successfully!`);
            setAlertType('success');
            setTimeout(() => {
              setOpenAlert(false);
              setAlertMsg('');
              props.saveInterval(true);
            }, 2000);
          });
      }
    } else {
      if (props.edit == true) {
        setOpenAlert(true);
        setAlertMsg('Please made any changes in menu interval.');
        setAlertType('warning');
        setTimeout(() => {
          setOpenAlert(false);
        }, 2000);
      }
    }
  };
  const clickCancelMenu = menuName => {
    props.clickCancelMenu(true);
  };
  const showMenuModal = data => {
    setOpenStore(true);
    setSelectedMenu(data);
  };
  const handleCloseStore = () => {
    setOpenStore(false);
  };
  const trimMenu = menuName => {
    if (menuName.length > 20) {
      return menuName.substring(0, 20) + '...';
    }
    return menuName;
  };
  return (
    <div>
      <Dialog
        open={openAlert}
        classes={{
          paper: classes.dialog
        }}
      >
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Dialog>
      <Grid xs={12} className={classes.textContainerGrid}>
        <Grid
          xs={12}
          className={classes.textContainerGrid}
          style={{ flexBasis: '1%' }}
        >
          <Card sx={{ margin: '20px 0 0 0' }} className={classes.card}>
            <span
              style={{ display: 'flex', marginTop: '15px', marginLeft: '15px' }}
            >
              <Typography
                style={{ padding: 8 }}
                variant="h6"
                className={clsx(classes.mainLabel)}
              >
                Mileage Interval -&nbsp;
                <span title={props.mname ? props.mname : ''}>
                  {trimMenu(props.mname ? props.mname : '')}
                </span>
              </Typography>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '100%',
                  marginTop: -3
                }}
              >
                <Tooltip title="View Menu Details">
                  <OpenInNewOutlinedIcon
                    htmlColor="rgb(0, 61, 107)"
                    style={{
                      width: 16,
                      left: '8',
                      top: '65%',
                      cursor: 'pointer',
                      marginTop: 8
                    }}
                    onClick={() =>
                      showMenuModal(props.mname ? props.mname : '')
                    }
                  />
                </Tooltip>
              </div>
            </span>

            <CardContent className={classes.contentcred}>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper
                    style={{ height: '100%', padding: 20, boxShadow: 'none' }}
                  >
                    <span
                      style={{
                        display: 'flex',
                        marginTop: '4px',
                        marginLeft: '-25px'
                      }}
                    >
                      <Typography
                        style={{ padding: 4 }}
                        variant="h6"
                        className={clsx(classes.mainLabel)}
                      >
                        <u>Miles</u>
                      </Typography>
                    </span>
                    <span
                      style={{
                        display: 'flex',
                        marginLeft: '-25px',
                        marginTop: '-8px'
                      }}
                    >
                      <Typography
                        style={{ padding: 4 }}
                        variant="h6"
                        className={clsx(classes.mainLabel)}
                      >
                        {props.mintervalData}
                      </Typography>
                    </span>
                    <Grid
                      container
                      spacing={2}
                      style={{ padding: '12px', marginLeft: '-38px' }}
                    >
                      <Grid
                        item
                        style={{
                          width: '90px',
                          paddingTop: '6px',
                          paddingLeft: '5px'
                        }}
                      >
                        <InputLabel shrink={false} htmlFor={'sales'}>
                          <Typography className={classes.label} variant="h6">
                            $ Sales<span style={{ color: 'red' }}>*</span>
                          </Typography>
                        </InputLabel>{' '}
                      </Grid>
                      <Grid
                        item
                        style={{
                          width: '90px',
                          paddingTop: '6px',
                          paddingLeft: '25px'
                        }}
                      >
                        <InputLabel shrink={false} htmlFor={'frh'}>
                          <Typography className={classes.label} variant="h6">
                            Flat Rate Hours
                            <span style={{ color: 'red' }}>*</span>
                          </Typography>
                        </InputLabel>{' '}
                      </Grid>
                      <Grid
                        item
                        style={{
                          width: '90px',
                          paddingTop: '6px',
                          paddingLeft: '43px'
                        }}
                      >
                        <InputLabel
                          shrink={false}
                          htmlFor={'item'}
                          variant="h6"
                        >
                          <Typography className={classes.label} variant="h6">
                            Items<span style={{ color: 'red' }}>*</span>
                          </Typography>
                        </InputLabel>
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      spacing={2}
                      style={{
                        padding: '12px',
                        marginTop: '-50px',
                        marginLeft: '-39px'
                      }}
                    >
                      <Grid
                        item
                        style={{
                          width: '78px',
                          paddingTop: '6px',
                          paddingLeft: '6px'
                        }}
                      >
                        <TextField
                          required
                          variant="outlined"
                          size="small"
                          id="sales"
                          margin="normal"
                          InputLabelProps={{
                            shrink: true
                          }}
                          InputProps={{
                            classes: { input: classes.inputs }
                          }}
                          name="sales"
                          autoComplete="sales"
                          classes={{ root: classes.TextField }}
                          value={sales}
                          onChange={handleChangeSales}
                          style={{
                            width: '110px'
                          }}
                          inputProps={{
                            maxLength: 10
                          }}
                        />
                      </Grid>
                      <Grid
                        item
                        style={{
                          width: '78px',
                          paddingTop: '6px',
                          paddingLeft: '37px'
                        }}
                      >
                        <TextField
                          id="frh"
                          margin="normal"
                          InputLabelProps={{
                            shrink: true
                          }}
                          classes={{ root: classes.TextField }}
                          InputProps={{
                            classes: { input: classes.inputs }
                          }}
                          name="frh"
                          autoComplete="frh"
                          value={frh}
                          variant="outlined"
                          onChange={handleChangeFrh}
                          style={{ width: '110px', height: '40px' }}
                          required
                          size="small"
                          step="0.1"
                          inputProps={{
                            maxLength: 10
                          }}
                        />
                      </Grid>
                      <Grid
                        items
                        style={{
                          width: '78px',
                          paddingTop: '6px',
                          paddingLeft: '68px'
                        }}
                      >
                        <TextField
                          id="item"
                          margin="normal"
                          InputLabelProps={{
                            shrink: true
                          }}
                          classes={{ root: classes.TextField }}
                          InputProps={{
                            classes: { input: classes.inputs }
                          }}
                          name="item"
                          autoComplete="item"
                          value={itemValue}
                          variant="outlined"
                          onChange={handleChangeItem}
                          style={{ width: '110px', height: '40px' }}
                          required
                          size="small"
                          onInput={e => {
                            const target = e.target;
                            target.value = e.target.value.replace(
                              /[^0-9]/g,
                              ''
                            );
                          }}
                          inputProps={{
                            maxLength: 10
                          }}
                        />
                      </Grid>
                    </Grid>
                    <Grid
                      container
                      spacing={2}
                      style={{
                        padding: '12px',
                        marginLeft: '-34px',
                        marginTop: '-60px'
                      }}
                    >
                      <InputLabel shrink={false} htmlFor={'item'}>
                        <Typography
                          style={{
                            marginTop: 22,
                            color: 'tomato',
                            marginLeft: 6,
                            fontSize: 11
                          }}
                        >
                          {errorSale
                            ? errorSale
                            : errorFrh
                            ? errorFrh
                            : errorItem
                            ? errorItem
                            : ''}
                        </Typography>
                      </InputLabel>
                    </Grid>
                    <Grid
                      container
                      spacing={2}
                      style={{
                        padding: '12px',
                        marginLeft: '-34px'
                      }}
                    >
                      <FormControl component="fieldset">
                        <FormLabel
                          variant="h6"
                          style={{ color: '#001837', fontWeight: 500 }}
                        >
                          Select a Category
                        </FormLabel>
                        <RadioGroup
                          aria-label="category"
                          name="category1"
                          value={values}
                          onChange={handleChange}
                          style={{ paddingLeft: 10 }}
                        >
                          {category.map((cat, catIndex) => (
                            <FormControlLabel
                              value={cat.id}
                              control={<Radio size="small" />}
                              label={cat.serviceType}
                              key={catIndex}
                              disabled={
                                props.edit == true
                                  ? true
                                  : props.catArr &&
                                    props.catArr.includes(cat.id)
                                  ? true
                                  : false
                              }
                            />
                          ))}
                        </RadioGroup>
                      </FormControl>
                    </Grid>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    style={{ height: '100%', padding: 20, boxShadow: 'none' }}
                  >
                    <MenuOpcode
                      session={props.session}
                      sales={sales}
                      frh={frh}
                      itemValue={itemValue}
                      category={values}
                      onChildData={handleChildData}
                      mOpcodes={selectedOpcode}
                      checkOpcode={checkOpcode}
                      menuNmae={props.mname ? props.mname : ''}
                    />
                  </Paper>
                </Grid>
              </Grid>
              {props.showChild ? (
                <Grid>
                  <span
                    style={{
                      display: 'flex',
                      marginTop: '15px',
                      marginLeft: '-2px'
                    }}
                  >
                    <Typography
                      variant="h6"
                      className={clsx(classes.rolelabel)}
                    >
                      {'Select Intervals this Service Applies to'}
                      <span style={{ color: 'red' }}>*</span>
                    </Typography>
                    {intervalCheck == true && (
                      <Typography
                        style={{
                          marginTop: 5,
                          color: 'tomato',
                          marginLeft: 6,
                          fontSize: 11,
                          letterSpacing: '0.33px'
                        }}
                      >
                        Please pick a time interval
                      </Typography>
                    )}
                  </span>
                  <Paper
                    style={{
                      height: '250px',
                      padding: 20,
                      overflowY: 'auto'
                    }}
                  >
                    <Grid container spacing={2}>
                      {seriesArr.length > 0 &&
                        seriesArr.map((column, colIndex) => (
                          <Grid item xs={1} key={column.ratings || colIndex}>
                            <div>
                              {column.ratings !== null &&
                                column.ratings !== undefined && (
                                  <FormControlLabel
                                    style={{ height: 25, width: 50 }}
                                    control={
                                      <Checkbox
                                        style={{ transform: 'scale(.6)' }}
                                        checked={assignedStatusValues.includes(
                                          column.ratings
                                        )}
                                        onChange={() =>
                                          handleCheckboxChange(colIndex, column)
                                        }
                                        disabled={
                                          props.mServicetype !==
                                            column.service_type &&
                                          column.assigned_status !== 0
                                        }
                                      />
                                    }
                                    label={
                                      <Typography
                                        style={{
                                          fontSize: '12px',
                                          backgroundColor:
                                            column.assigned_status == 0
                                              ? ''
                                              : '#bad49f'
                                        }}
                                      >
                                        {column.ratings.toLocaleString()}
                                      </Typography>
                                    }
                                  />
                                )}
                            </div>
                          </Grid>
                        ))}
                    </Grid>
                  </Paper>

                  <div style={{ float: 'right' }}>
                    <Button
                      variant="contained"
                      className={clsx('reset-btn', classes.btnDiv)}
                      color="primary"
                      onClick={clickCancelMenu}
                      fullWidth
                      style={{ marginRight: '5px' }}
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="contained"
                      className={clsx('reset-btn', classes.btnDiv)}
                      color="primary"
                      onClick={handleSave}
                      fullWidth
                      style={{ marginRight: '5px' }}
                      disabled={disabled}
                    >
                      Save
                    </Button>
                  </div>
                </Grid>
              ) : (
                <></>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      <>
        {' '}
        <MenuDialog
          openPopup={openStore}
          handlePopupClose={handleCloseStore}
          menuName={props.mname ? props.mname : ''}
        ></MenuDialog>
      </>
    </div>
  );
};

export default withKeycloak(MenuDetails);
