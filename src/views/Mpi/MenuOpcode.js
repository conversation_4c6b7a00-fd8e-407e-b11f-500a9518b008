import * as React from 'react';
import {
  List,
  ListItem,
  ListItemText,
  Typography,
  Dialog,
  Paper,
  Tooltip
} from '@material-ui/core';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { useEffect } from 'react';
import {
  getOpcodeDetails,
  getMenuOpcodesList,
  getFilteredOpcode
} from 'src/utils/hasuraServices';
import Alert from '@material-ui/lab/Alert';
import CloseIcon from '@material-ui/icons/Close';
import IconButton from '@material-ui/core/IconButton';
import 'src/styles.css';
import SwapHorizIcon from '@material-ui/icons/SwapHoriz';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  mainLabel: {
    marginLeft: -20,
    display: 'flex',
    color: 'rgb(0, 61, 107)',
    fontSize: 12
  },
  roleMappingContent: {
    width: '100%',
    margin: 'auto'
  },
  titleLabel: {
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  card: {
    width: '98%',
    marginLeft: '20px',
    marginTop: '28px',
    height: '400px'
  },
  label: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    marginTop: '16px'
  },
  txt: {
    width: '400px'
  },
  rmdata: {
    width: '39%',
    float: 'left',
    margin: '0 1%'
  },
  rmdataCenter: {
    width: '17%',
    float: 'left',
    border: 'none'
  },
  rmdatadescCenter: {
    minHeight: '150px',
    marginTop: '78px',
    textAlign: 'center'
  },
  rmdatadesc: {
    border: '1px solid #a7a7a7',
    minHeight: '150px'
  },
  rolelabel: {
    padding: '8px 0',
    color: '#001837',
    fontWeight: 500
  },
  rmbutton: {
    marginTop: '10px'
  },
  addremovebtn: {
    width: '100px',
    cursor: 'pointer'
  },
  selected: {
    backgroundColor: '#ddeaf4 !important',
    '&:focus': {
      backgroundColor: '#ddeaf4 '
    }
  },
  dialog: {
    position: 'absolute',
    top: 50
  },
  listItemText: {
    color: '#212121'
  }
}));

const MenuOpcode = props => {
  const [opcode, setOpcode] = React.useState([]);
  const [left, setLeft] = React.useState([]);
  const [right, setRight] = React.useState([]);
  const [selectedIndex, setSelectedIndex] = React.useState([]);
  const [leftSelected, setLeftSelected] = React.useState([]);
  const [rightSelected, setRightSelected] = React.useState([]);
  const [selectedItem, setSelectedItem] = React.useState(null);
  const [openAlert, setOpenAlert] = React.useState(false);
  const [alertMsg, setAlertMsg] = React.useState('');
  const [alertType, setAlertType] = React.useState('');
  const [userName, setUserName] = React.useState('');
  const classes = useStyles();
  useEffect(() => {
    var username = props && props.session && props.session.userId;
    setUserName(username);
    var opcodeName = [];
    var opcodeNameSelected = [];
    var correctedArray = [];
    getFilteredOpcode(props.menuNmae, result => {
      if (
        result.data.statelessCcPhysicalRwGetFilteredMenuopcodes
          .getFilteredMenuopcodesViews
      ) {
        if (
          result.data.statelessCcPhysicalRwGetFilteredMenuopcodes
            .getFilteredMenuopcodesViews[0] != null
        ) {
          if (
            result.data.statelessCcPhysicalRwGetFilteredMenuopcodes
              .getFilteredMenuopcodesViews[0].opcodeList
          ) {
            var roData = JSON.parse(
              result.data.statelessCcPhysicalRwGetFilteredMenuopcodes
                .getFilteredMenuopcodesViews[0].opcodeList
            );
            roData.map(item => {
              opcodeName.push(item.opcode);
            });
            const correctedArray = props.mOpcodes.map(item => ({
              opcode: item.menu_opcode,
              opcodedescription: item.opcodedescription,
              service_type: item.service_type
            }));
            opcodeNameSelected = correctedArray;
            const opcodeNameSel = opcodeName.filter(
              element => !opcodeNameSelected.includes(element)
            );
            const opcodeNameSelsss = roData.filter(
              element => !opcodeNameSelected.includes(element.opcode)
            );
            opcodeNameSelsss.sort((a, b) => {
              const opcodeA = parseInt(a.opcode) || 0;
              const opcodeB = parseInt(b.opcode) || 0;
              if (opcodeA < opcodeB) {
                return -1;
              }
              if (opcodeA > opcodeB) {
                return 1;
              }
              return 0;
            });
            setLeft(opcodeNameSelsss);
            opcodeNameSelected.sort((a, b) => {
              if (a.opcode < b.opcode) {
                return -1;
              }
              if (a.opcode > b.opcode) {
                return 1;
              }
              return 0;
            });
            setRight(opcodeNameSelected);
          }
        } else {
          const correctedArray = props.mOpcodes.map(item => ({
            opcode: item.menu_opcode,
            opcodedescription: item.opcodedescription,
            service_type: item.service_type
          }));
          setLeft([]);
          correctedArray.sort((a, b) => {
            if (a.opcode < b.opcode) {
              return -1;
            }
            if (a.opcode > b.opcode) {
              return 1;
            }
            return 0;
          });
          setRight(correctedArray);
        }
      }
    });
  }, [props.mOpcodes, props.menuNmae]);

  const handleCheckedRight = () => {
    var isExist = right.some(element => leftSelected.includes(element));
    var filtered = left.filter(item => !leftSelected.includes(item));
    if (!isExist) {
      setRight(right.concat(leftSelected));
      props.onChildData(right.concat(leftSelected));
      setLeft(filtered);
      setLeftSelected([]);
      setSelectedIndex([]);
    }
  };
  const handleCheckedLeft = () => {
    var isExist = left.some(element => rightSelected.includes(element));
    var filtered = right.filter(item => !rightSelected.includes(item));
    if (!isExist) {
      setLeft(left.concat(rightSelected));
      setRight(filtered);
      setRightSelected([]);
      props.onChildData(filtered);
      setSelectedIndex([]);
    }
  };
  const handleListItemClick = (event, index, item, title) => {
    let newSelected;
    if (title === 'Choices') {
      newSelected = [...leftSelected];
      const selectedIndexs = newSelected.indexOf(item);
      if (selectedIndexs === -1) {
        newSelected.push(item);
      } else {
        newSelected.splice(selectedIndexs, 1);
      }
      setSelectedIndex(newSelected);
      setLeftSelected(newSelected);
      setRightSelected([]);
    } else {
      newSelected = [...rightSelected];
      const selectedIndexs = newSelected.indexOf(item);
      if (selectedIndexs === -1) {
        newSelected.push(item);
      } else {
        newSelected.splice(selectedIndexs, 1);
      }
      setSelectedIndex(newSelected);
      setRightSelected(newSelected);
      setLeftSelected([]);
    }
  };
  const customList = (title, items) => (
    (items = items.sort((a, b) => a.opcode.localeCompare(b.opcode))),
    (
      <Paper style={{ height: '300px', overflow: 'auto' }}>
        <List
          sx={{
            width: 200,
            height: 230,
            bgcolor: 'background.paper',
            overflow: 'auto'
          }}
          dense
          component="div"
          role="list"
          style={{ paddingTop: 1, paddingBottom: 1 }}
        >
          {items.map((value, index) => {
            const labelId = `transfer-list-all-item-${value.opcode}-label`;
            return (
              <ListItem
                key={value.opcode}
                role="listitem"
                button
                onClick={event =>
                  handleListItemClick(event, index, value, title)
                }
                selected={selectedIndex.some(
                  item => item.opcode == value.opcode
                )}
                className={
                  selectedIndex.some(item => item.opcode == value.opcode)
                    ? classes.selected
                    : ''
                }
                id={'itemList' + title}
              >
                <ListItemText
                  id={labelId}
                  primary={value.opcode}
                  classes={{ primary: classes.listItemText }}
                />
                <Tooltip title={value.opcodedescription}>
                  <InfoOutlinedIcon
                    style={{
                      width: '12px',
                      height: '12px',
                      gap: '5 !important'
                    }}
                  />
                </Tooltip>
              </ListItem>
            );
          })}
        </List>
      </Paper>
    )
  );
  const saveData = () => {
    const generatedJSON = {};
    generatedJSON.sales = props.sales;
    generatedJSON.frh = props.frh;
    generatedJSON.item = props.itemValue;
    generatedJSON.category = props.category;
    const generatedArray = [];
    for (const opcode of right) {
      generatedArray.push({ opcode });
    }
    generatedJSON.selectedOpcode = generatedArray;
  };
  return (
    <>
      <Dialog
        open={openAlert}
        classes={{
          paper: classes.dialog
        }}
      >
        <Alert
          severity={alertType == 'warning' ? 'warning' : 'success'}
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Dialog>
      <div style={{ width: '100%' }}>
        <div className={classes.rmdata}>
          <div className="rmdata-title">
            {' '}
            <Typography variant="h6" className={clsx(classes.rolelabel)}>
              Select Menu Opcode<span style={{ color: 'red' }}>*</span>
              {props.checkOpcode == true && (
                <span
                  style={{
                    color: 'tomato',
                    fontSize: 11,
                    letterSpacing: '0.33px',
                    fontWeight: 400
                  }}
                >
                  {' '}
                  Please select an opcode
                </span>
              )}
            </Typography>
          </div>
          <div className={classes.rmdatadesc}>
            {' '}
            {customList('Choices', left)}
          </div>
        </div>
        <div className={classes.rmdataCenter}>
          <div className={classes.rmdatadescCenter}>
            <Typography variant="h6" className={clsx(classes.rolelabel)}>
              Add/Remove
            </Typography>
            {leftSelected.length > 0 && (
              <SwapHorizIcon
                onClick={handleCheckedRight}
                style={{ cursor: 'pointer' }}
              />
            )}
            {rightSelected.length > 0 && (
              <SwapHorizIcon
                onClick={handleCheckedLeft}
                style={{ cursor: 'pointer' }}
              />
            )}
            {leftSelected.length == 0 && rightSelected.length == 0 && (
              <SwapHorizIcon disabled={true} />
            )}
          </div>
        </div>
        <div className={classes.rmdata}>
          <div className="rmdata-title">
            {' '}
            <Typography variant="h6" className={clsx(classes.rolelabel)}>
              Selected Opcode
            </Typography>
          </div>
          <div className={classes.rmdatadesc}>
            {' '}
            {customList('Chosen', right)}
          </div>
        </div>
      </div>
    </>
  );
};
export default MenuOpcode;
