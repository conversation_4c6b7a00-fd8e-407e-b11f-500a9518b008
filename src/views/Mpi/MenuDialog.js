import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField,
  FormLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Tooltip
} from '@material-ui/core';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import $, { param } from 'jquery';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import { getMenuPopup } from 'src/utils/hasuraServices';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import _ from 'lodash';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {},
  smallRadioButton: {
    '& svg': {
      width: '0.7em',
      height: '0.7em'
    },
    paddingLeft: 0,
    paddingRight: 0,
    height: 18,
    marginLeft: -2,
    backgroundColor: 'transparent !important',
    width: 19,
    '@media (max-width: 1440px)': {
      width: '16px !important',
      marginLeft: -2
    }
  },
  listItemText: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    padding: '7px 10px !important',
    marginBottom: -8,
    fontWeight: 500,
    maxWidth: 220
  },
  loaderStore: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: '20% !important'
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function StoreDialog({ openPopup, handlePopupClose, menuName, userEmail }) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(openPopup);
  const [selected, setSelected] = useState('Both');
  const [groupsAvailable, setGroupsAvailable] = React.useState([]);
  const [loading, setLoading] = useState(true);
  const [menuNames, setMenuNames] = React.useState('');
  const [afterMiles, setAfterMiles] = React.useState('');
  const [beforeMiles, setBeforeMiles] = React.useState('');
  const [maxMiles, setMaxMiles] = React.useState('');
  const [milegaeInterval, setMilegaeInterval] = React.useState('');
  const [rowData, setRowData] = React.useState([]);
  useEffect(() => {
    setOpenDialog(openPopup);
    setMenuNames(menuName);
    if (menuName) {
      setRowData([]);
      setLoading(true);
      getMenuPopupDetails(menuName);
    }
  }, [openPopup, menuName]);
  const handleClose = () => {
    setOpenDialog(false);
    handlePopupClose();
  };
  const getMenuPopupDetails = menuName => {
    var grpNames = [];
    var grpwithId = [];
    getMenuPopup(menuName, result => {
      if (result.data.statelessCcPhysicalRwGetMenuPopup.getMenuPopupData) {
        var roData =
          result.data.statelessCcPhysicalRwGetMenuPopup.getMenuPopupData;
        setMenuNames(roData[0].menuName ? roData[0].menuName : '');
        setAfterMiles(roData[0].afterMiles);
        setBeforeMiles(roData[0].beforeMiles);
        setMaxMiles(roData[0].maxMiles);
        setMilegaeInterval(roData[0].milegaeInterval);
        setRowData(roData);
        setLoading(false);
      } else {
        setLoading(false);
        setRowData([]);
      }
    });
  };
  const handleChange = ev => {
    setSelected(ev.target.value);
  };
  const NumberFormat = number => {
    var nf = new Intl.NumberFormat();
    var value = nf.format(number);
    return value;
  };
  const customList = (title, items) => {
    return (
      <div>
        {items.map((item, index) => {
          return (
            <>
              <List component="nav" style={{ paddingTop: 1, paddingBottom: 1 }}>
                <ListItem
                  style={{ paddingTop: 1, paddingBottom: 1, marginBottom: -10 }}
                >
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Menu Name'}
                    style={{ width: 102, maxWidth: 250, fontSize: 15 }}
                  />
                  :
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={item.menuName}
                  />
                </ListItem>
              </List>
              <List component="nav" style={{ paddingTop: 1, paddingBottom: 1 }}>
                <ListItem
                  style={{ paddingTop: 1, paddingBottom: 1, marginBottom: -10 }}
                >
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Menu Interval'}
                    style={{ width: 100, maxWidth: 227, fontWeight: 500 }}
                  />
                  :
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={item.milegaeInterval}
                  />
                </ListItem>
              </List>
              <List component="nav" style={{ paddingTop: 1, paddingBottom: 1 }}>
                <ListItem
                  style={{ paddingTop: 1, paddingBottom: 1, marginBottom: -10 }}
                >
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Miles Before'}
                    style={{ width: 100, maxWidth: 228, fontWeight: 500 }}
                  />
                  :
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={item.beforeMiles}
                  />
                </ListItem>
              </List>
              <List component="nav" style={{ paddingTop: 1, paddingBottom: 1 }}>
                <ListItem
                  style={{ paddingTop: 1, paddingBottom: 1, marginBottom: -10 }}
                >
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Miles After'}
                    style={{ width: 100, maxWidth: 229, fontWeight: 500 }}
                  />
                  :
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={item.afterMiles}
                  />
                </ListItem>
              </List>
              <List component="nav" style={{ paddingTop: 1, paddingBottom: 1 }}>
                <ListItem
                  style={{ paddingTop: 1, paddingBottom: 1, marginBottom: -10 }}
                >
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Maximum Miles'}
                    style={{ width: 110, maxWidth: 230, fontWeight: 500 }}
                  />
                  :
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={item.maxMiles}
                  />
                </ListItem>
              </List>
            </>
          );
        })}
      </div>
    );
  };
  const trimMenu = menuName => {
    if (menuName.length > 15) {
      return menuName.substring(0, 15) + '...';
    }
    return menuName;
  };
  return (
    <div>
      <Dialog
        open={openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          style: {
            width: '430px',
            marginLeft: '20px',
            marginTop: '28px',
            height: 270
          }
        }}
      >
        <IconButton
          onClick={handleClose}
          aria-label="close"
          style={{ float: 'inline-end', justifyContent: 'right' }}
        >
          <Tooltip title="Close">
            <HighlightOffIcon style={{ fontSize: 30 }} />
          </Tooltip>
        </IconButton>
        <DialogContent style={{ display: 'flex', flexDirection: 'row' }}>
          {rowData.map((item, index) => {
            return (
              <>
                <div style={{ flex: 1 }}>
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Menu Name'}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Mileage Interval'}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Miles Before'}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Miles After'}
                    style={{ fontWeight: 500 }}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={'Maximum Miles'}
                    style={{ fontWeight: 500 }}
                  />
                </div>
                <div style={{ flex: 1 }}>
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={
                      <>
                        {`: ` + trimMenu(item.menuName)}
                        {item.menuName.length > 15 ? (
                          <Tooltip title={item.menuName}>
                            <InfoOutlinedIcon
                              style={{
                                width: '15px',
                                height: '12px',
                                gap: '5 !important'
                              }}
                            />
                          </Tooltip>
                        ) : (
                          ''
                        )}
                      </>
                    }
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.milegaeInterval)}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.beforeMiles)}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.afterMiles)}
                  />
                  <ListItemText
                    primaryTypographyProps={{ className: classes.listItemText }}
                    primary={`:   ` + NumberFormat(item.maxMiles)}
                  />
                </div>
              </>
            );
          })}
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default StoreDialog;
