import React, { useState, useEffect } from 'react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import {
  Grid,
  Paper,
  Button,
  Tooltip,
  Tab,
  Tabs,
  TextField,
  Divider
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'src/grid.css';
import moment from 'moment';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import { getMpiValue, insertMpiValue } from 'src/utils/hasuraServices';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import { withKeycloak } from '@react-keycloak/web';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/core/styles';
import Menu from './Menu';
import SuccessSnackbar from 'src/views/KPIScoreCardGoalSettings/SuccessSnackbar';
import { setNavItems, setMenuSelected } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import Page from 'src/components/Page';
import { Redirect } from 'react-router-dom';

const useStyles = makeStyles(theme => ({
  menu: {
    background: theme.palette.button.primary,
    color: '#fff',
    marginLeft: 20,
    alignSelf: 'self-end',
    marginTop: 10,
    width: 100,
    height: 'auto'
  },
  edit: {
    background: theme.palette.button.primary,
    color: '#fff',
    marginLeft: 10,
    alignSelf: 'self-end',
    bottom: 6,
    width: 50,
    height: 'auto'
  },
  TextField: {
    marginTop: 12,
    '& label': {
      color: '#212121 !important'
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#7575753d'
      },
      '&:hover fieldset': {
        borderColor: '#7575753d'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#7575753d'
      }
    }
  },
  TextFieldFrh: {
    marginTop: 12,
    marginLeft: 20,
    '& label': {
      color: '#212121 !important'
    },
    '& .MuiOutlinedInput-root': {
      '& fieldset': {
        borderColor: '#7575753d'
      },
      '&:hover fieldset': {
        borderColor: '#7575753d'
      },
      '&.Mui-focused fieldset': {
        borderColor: '#7575753d'
      }
    }
  },
  textContainer: {
    alignItems: 'start',
    display: 'flex'
  },
  sublLabel: {
    display: 'flex',
    marginLeft: 10,
    paddingTop: 20
  },
  container: {
    alignItems: 'center',
    margin: '20px 0px'
  },
  dataGridClient: {
    '@media (max-width: 1920px)': {
      width: 400
    },
    '@media (max-width: 1280px)': {
      width: 400
    },
    '@media (min-width: 2304px)': {
      width: 400
    }
  },
  textContainerGrid: {
    display: 'flex',
    gap: 10
  },
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    border: 'thin solid #968989 !important',
    height: '35px !important'
  },
  reactDaterangePicker: {
    width: '80px !important',
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px'
  },
  back: {
    marginRight: 30,
    float: 'right',
    marginTop: 10,
    width: 105
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  }
}));

const Mpi = props => {
  const [create, setCreate] = useState(false);
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [successMsg, setSuccessMsg] = useState('');
  const [disabled, setDisabled] = useState(true);
  const [mpiValue, setMpiValue] = useState(0);
  const [mpiValueInt, setMpiValueInt] = useState(0);
  const [error, setError] = useState('');
  const [gridColumnApi, setGridColumnApi] = useState(null);
  const [gridColumnApis, setGridColumnApis] = useState(null);
  const [value, setValue] = useState(new Date());
  const [showDrillDown, setShowDrillDown] = useState(false);
  const [fadein, setFadein] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [rawGridApi, setRawGridApi] = useState({});
  const [rawGridApis, setRawGridApis] = useState({});
  const [gridApi, setGridApi] = useState({});
  const [gridApis, setGridApis] = useState({});
  const [tabSelection, setTabSelection] = useState('one');
  const [rowData, setRowData] = useState([]);
  const dispatch = useDispatch();
  const classes = useStyles();
  const session = useSelector(state => state.session);

  useEffect(() => {
    dispatch(setMenuSelected('Menu / MPI Setups'));
    dispatch(setNavItems(['Armatus Admin']));
  }, []);
  useEffect(() => {
    getMpiValue(result => {
      setDisabled(true);
      setIsLoading(false);
      if (result.data.statelessCcPhysicalRwGetMpiSetup.mpidataSetups) {
        var resultArr =
          result.data.statelessCcPhysicalRwGetMpiSetup.mpidataSetups;
        if (resultArr.length > 0) {
          setMpiValueInt(resultArr[resultArr.length - 1].frh);
          setMpiValue(resultArr[resultArr.length - 1].frh);
        } else {
          setMpiValueInt(0);
          setMpiValue(0);
        }
      }
    });
  }, [session.storeSelected]);
  const submitMpiValue = event => {
    let newNumber = mpiValue;
    if (newNumber.endsWith('.')) {
      newNumber = newNumber.slice(0, -1);
    }
    setMpiValue(newNumber);
    if (mpiValueInt != mpiValue) {
      insertMpiValue(mpiValue, result => {
        if (result.data.statelessCcPhysicalRwInsertMpiSetup) {
          getMpiValue(result => {
            if (result.data.statelessCcPhysicalRwGetMpiSetup.mpidataSetups) {
              var resultArr =
                result.data.statelessCcPhysicalRwGetMpiSetup.mpidataSetups;
              setRowData(resultArr);
              setDisabled(true);
              setError('');
              setSuccessMsg('Flat Rate Hours Updated Successfully!');
              setopenSnackbar(true);
              setMpiValueInt(mpiValue);
              setMpiValue(mpiValue);
            }
          });
        }
      });
    } else if (mpiValue == '') {
      setError('Field cannot be empty');
      setMpiValue('');
    }
  };
  const editMpiValue = event => {
    setDisabled(false);
  };
  const handleFormChange = (event, i) => {
    const re = /^[+-]?(\d{1,5})(\.\d{0,2})?$/;
    event.persist();
    const prevVal = event.target.value;
    const regexPattern = /^\d+(\.\d{0,2})?$/;
    if (regexPattern.test(event.target.value) || event.target.value === '') {
      setMpiValue(event.target.value);
      if (re.test(event.target.value)) {
        setMpiValue(event.target.value);
        setDisabled(false);
        setError('');
      } else {
        setError('Field cannot be empty');
        setMpiValue('');
      }
    } else {
      if (event.target.value == '.') {
        console.log('enter=5');
        setError('');
        setMpiValue('0.');
      } else {
        console.log('enter=6');
        setError('Field cannot be empty');
        setMpiValue('');
      }
    }
  };
  const handleTabChange = (event, newValue) => {
    setTabSelection(newValue);
  };
  const hidesnackbar = () => {
    setopenSnackbar(false);
  };
  const onCreate = () => {
    setCreate(!create);
  };
  return props.keycloak.realmAccess.roles.includes('client') ||
    props.keycloak.realmAccess.roles.includes('user') ||
    JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
    <Redirect to="/errors/error-404" />
  ) : (
    <Page className={classes.root} title="MPI / Menu Setup">
      <div>
        <Paper
          square
          style={{
            margin: 8
          }}
        >
          <Tabs
            value={tabSelection}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
            style={{ pointerEvents: 'none', textTransform: 'none' }}
          >
            <Tab
              style={{ textTransform: 'none' }}
              label={<div>MPI Setup</div>}
              value="one"
              className={tabSelection == 'one' ? classes.tabSelected : null}
            />
          </Tabs>
        </Paper>
        {tabSelection == 'two' ? <Menu session={props.session} /> : null}
        <Divider classes={{ root: classes.dividerRoot }} />
        <React.Fragment>
          <Grid container className={clsx(classes.container, 'set-goal')}>
            <Grid item xs={8} className={classes.textContainer}>
              <TextField
                inputProps={{
                  id: 'mpi'
                }}
                onChange={e => handleFormChange(e)}
                classes={{ root: classes.TextFieldFrh }}
                size="small"
                name={'mpi'}
                label={'Flat Rate Hours'}
                value={mpiValue}
                id={'mpi'}
                variant="outlined"
                focus={true}
                error={Boolean(error)}
                disabled={disabled}
                InputProps={{
                  style: { borderColor: error ? 'red' : undefined }
                }}
                InputLabelProps={{
                  style: { color: error ? 'red' : undefined }
                }}
              />
              {disabled ? (
                <Tooltip title="Edit" onClick={editMpiValue}>
                  <Button
                    variant="contained"
                    id="editGoals"
                    className={clsx(classes.edit, 'reset-btn')}
                    onClick={editMpiValue}
                  >
                    Edit
                  </Button>
                </Tooltip>
              ) : (
                <>
                  <Tooltip title="save" onClick={submitMpiValue}>
                    <Button
                      variant="contained"
                      id="saveGoals"
                      className={clsx(classes.edit, 'reset-btn')}
                      onClick={submitMpiValue}
                    >
                      Save
                    </Button>
                  </Tooltip>
                </>
              )}
              {error && (
                <p
                  style={{
                    marginTop: 22,
                    color: 'tomato',
                    marginLeft: 6,
                    fontSize: '90'
                  }}
                >
                  Please enter valid Flat Rate Hour
                </p>
              )}
            </Grid>
            <Grid item xs={2} className={classes.flexItem}></Grid>
          </Grid>
          <Divider classes={{ root: classes.dividerRoot }} />
          <Paper
            square
            style={{
              margin: 8
            }}
          >
            <Tabs
              value={tabSelection}
              variant="fullWidth"
              indicatorColor="secondary"
              textColor="secondary"
              aria-label="icon label tabs example"
              style={{ pointerEvents: 'none', textTransform: 'none' }}
            >
              <Tab
                style={{ textTransform: 'none' }}
                label={<div>Menu Setup</div>}
                value="one"
                className={tabSelection == 'one' ? classes.tabSelected : null}
              />
            </Tabs>
          </Paper>
          <Menu session={props.session} />
          {successMsg && !error && (
            <SuccessSnackbar
              onClose={hidesnackbar}
              open={openSnackbar}
              msg={successMsg}
            />
          )}
        </React.Fragment>
      </div>
    </Page>
  );
};
export default withKeycloak(Mpi);
