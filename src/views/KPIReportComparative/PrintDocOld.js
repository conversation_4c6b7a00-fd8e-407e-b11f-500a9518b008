import { text } from '@fortawesome/fontawesome-svg-core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

/**
 * This function iterates over all of the columns to create a row of header cells
 */
export const usePDFExport = () => {
  const getHeaderToExport = (gridApi, hideGoals, visibleTotals) => {
    if (Object.keys(gridApi).length != 0) {
      const columns = gridApi.columnApi.getAllDisplayedColumns();

      return columns.map((column, i) => {
        //getCellToExport(column, node)
        const pathaname = window.location.pathname.split('/')[1];
        const { field } = column.getColDef();
        const sort = column.getSort();
        // Enables export when row grouping

        const headerName =
          column.colId == 'ag-Grid-AutoColumn'
            ? 'KPI Name'
            : column.parent.groupId ?? field;
        const headerNameUppercase =
          headerName[0].toUpperCase() + headerName.slice(1);

        let header;

        if (
          column.colId == 'ag-Grid-AutoColumn' ||
          column.colId == 'Total Selected_1'
        ) {
          header = [
            {
              text: headerNameUppercase + (sort ? ` (${sort})` : ''),
              alignment: 'center',
              fontSize: 8,
              bold: true,
              color: '#fff'
            }
          ];
        } else if (
          pathaname == 'KPIReportStoreComparative' &&
          (column.colId == 'Group Roll-Up_1' ||
            column.colId == 'Group Average_1')
        ) {
          //   console.log('column==pp', column.colId);
          header = [
            {
              text: headerNameUppercase + (sort ? ` (${sort})` : ''),
              alignment: 'center',
              fontSize: 8,
              bold: true,
              color: '#fff'
            }
          ];
        } else {
          header = [
            {
              text: headerNameUppercase + (sort ? ` (${sort})` : ''),
              alignment: 'center',
              fontSize: 8,
              bold: true,
              color: '#fff'
            },
            {
              text: 'Goal / Var',
              alignment: 'center',
              fontSize: 8,
              bold: true,
              color: '#fff'
            }
          ];
        }

        const headerCell = {
          // text:
          //   headerNameUppercase + (sort ? ` (${sort})` : '') + '       Goal/Var',
          text: header,

          color: '#fff',
          // styles

          margin: [0, 0, 0, 0]
        };
        //   console.log('headerCell', headerCell);
        return headerCell;
      });
    }
  };

  const formatCellValue = (val, opt, slno, dec) => {
    var formattedValue =
      val == 0 || val == null
        ? '0'
        : dec == 1
        ? parseInt(val)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : parseFloat(val)
            .toFixed(1)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    if (
      ((slno == 1 ||
        slno == 2 ||
        slno == 12 ||
        slno == 13 ||
        slno == 32 ||
        slno == 35 ||
        slno == 31 ||
        slno == 34 ||
        slno == 33 ||
        slno == 37) &&
        opt == 2) ||
      (slno == 8 && opt == 1) ||
      ((slno == 21 || slno == 22 || slno == 26 || slno == 27) && opt == 1) ||
      slno == 19
    ) {
      formattedValue = formattedValue + '%';
    } else if (
      slno == 1 ||
      slno == 2 ||
      slno == 3 ||
      slno == 4 ||
      slno == 5 ||
      slno == 6 ||
      slno == 18 ||
      slno == 23 ||
      slno == 24 ||
      slno == 28 ||
      slno == 29 ||
      slno == 32 ||
      slno == 35 ||
      ((slno == 14 || slno == 15 || slno == 16 || slno == 17) &&
        (opt == 1 || opt == 2))
    ) {
      if (Number(formattedValue.replace(/,/g, '')) < 0) {
        formattedValue = '-$' + Math.abs(formattedValue);
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      formattedValue = formattedValue;
    }

    return formattedValue;
  };
  /**
   * This function iterates over all of the rows and columns to create
   * a matrix of cells when pivoting is enabled
   */

  const getRowsToExportPivot = gridApi => {
    if (Object.keys(gridApi).length != 0) {
      const columns = gridApi.columnApi.getAllDisplayedColumns();

      // const getCellToExport = (column, node, i) => ({
      //   text: gridApi.getValue(column, node) ?? '',
      //   defaultStyle: {
      //     fontSize: 5
      //   },
      //   // styles
      //   ...column.getColDef().cellStyle
      // });

      const rowsToExport = [];
      gridApi.forEachNodeAfterFilterAndSort(node => {
        // console.log('node===', node);
        if (node.group) {
          const rowToExport = columns.map((column, i) => {
            return getCellToExport(gridApi, column, node, i);

            //getCellToExport(column, node)
          });
          rowsToExport.push(rowToExport);
        }
      });

      return rowsToExport;
    }
  };
  const getCellToExport = (gridApi, column, node, i, hideGoals) => {
    if (typeof node.data != 'undefined') {
      const pathaname = window.location.pathname.split('/')[1];
      let valArr;
      if (pathaname == 'KPIReportComparative') {
        valArr = node.data.data.filter(function(o) {
          return o.adv_or_tech_name == column.parent.groupId;
        });
      } else {
        valArr = node.data.data.filter(function(o) {
          return o.res_storename == column.parent.groupId;
        });
      }

      if (valArr.length > 0) {
        let data = valArr[0].kpi_data.split('/');
        let goal = valArr[0].goal;
        let variance = valArr[0].variance;
        if (
          data.length > 0 &&
          (node.data.kpi_slno == 12 ||
            node.data.kpi_slno == 13 ||
            node.data.kpi_slno == 9)
        ) {
          data.splice(-1);
        }
        if (
          node.data.kpi_name != 'Parts To Labor Ratio' &&
          node.data.kpi_name != 'CP & Wty - Avg Age / Miles Per Vehicle'
        ) {
          data = data
            .map((ele, i) => {
              return formatCellValue(
                node.data.kpi_slno >= 31 &&
                  node.data.kpi_slno <= 33 &&
                  node.data.kpi_slno >= 34 &&
                  node.data.kpi_slno <= 37
                  ? 0
                  : ele,
                i,
                node.data.kpi_slno,
                ((node.data.kpi_slno == 1 ||
                  node.data.kpi_slno == 2 ||
                  node.data.kpi_slno == 11) &&
                  i == 2) ||
                  ((node.data.kpi_slno == 33 || node.data.kpi_slno == 37) &&
                    (i == 3 || i == 2))
                  ? 2
                  : 1
              );
            })
            .join(' * ');
        } else if (node.data.kpi_name == 'Parts To Labor Ratio') {
          data =
            '$' +
            parseFloat(data[0])
              .toFixed(2)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' to ' +
            '$' +
            parseInt(data[1])
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        } else if (
          node.data.kpi_name == 'CP & Wty - Avg Age / Miles Per Vehicle'
        ) {
          data =
            parseFloat(data[0])
              .toFixed(1)
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' Years' +
            ' * ' +
            parseInt(data[1])
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
            ' Miles';
        }
        let value;
        if (
          pathaname == 'KPIReportComparative' &&
          column.colId == 'Total Selected_1'
        ) {
          value = [
            {
              text: data.length > 0 ? data : '',
              alignment: 'left',
              fontSize: 6

              //text: data.length > 0 ? data + '       Goal/Var' : '',
            }
          ];
        } else if (
          pathaname == 'KPIReportStoreComparative' &&
          (column.colId == 'Group Roll-Up_1' ||
            column.colId == 'Group Average_1')
        ) {
          value = [
            {
              text: data.length > 0 ? data : '',
              alignment: 'left',
              fontSize: 6

              //text: data.length > 0 ? data + '       Goal/Var' : '',
            }
          ];
        } else {
          // console.log('ccc===12', column.colId);
          let goalVal;
          if (
            node.data.kpi_slno != 9 &&
            node.data.kpi_slno != 11 &&
            node.data.kpi_slno != 33 &&
            node.data.kpi_slno != 37
          ) {
            goalVal = goal + '%/' + variance + '%';
          } else {
            goalVal = goal + '/' + variance;
          }

          value = [
            {
              text: data.length > 0 ? data : '',
              alignment: 'left',
              fontSize: 6

              //text: data.length > 0 ? data + '       Goal/Var' : '',
            },

            {
              text:
                goal != '' &&
                goal != 0 &&
                node.data.kpi_slno != 9 &&
                node.data.kpi_slno != 11 &&
                node.data.kpi_slno != 33 &&
                node.data.kpi_slno != 37
                  ? [
                      {
                        text: goal,
                        alignment: 'right',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: '% / ',
                        alignment: 'right',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: variance,
                        color:
                          variance > -10.0 &&
                          variance < 0.0 &&
                          node.data.kpi_slno != 27 &&
                          node.data.kpi_slno != 22 &&
                          node.data.kpi_slno != 12 &&
                          node.data.kpi_slno != 13
                            ? // ? 'brown'
                              'red'
                            : variance <= -10.0 &&
                              node.data.kpi_slno != 27 &&
                              node.data.kpi_slno != 22 &&
                              node.data.kpi_slno != 12 &&
                              node.data.kpi_slno != 13
                            ? 'red'
                            : variance > 0.0 &&
                              (node.data.kpi_slno == 27 ||
                                node.data.kpi_slno == 22 ||
                                node.data.kpi_slno == 12 ||
                                node.data.kpi_slno == 13)
                            ? 'red'
                            : goal != 0 && variance == 0
                            ? // ? '#fff'
                              '#000'
                            : 'green',
                        alignment: 'right',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: '%',
                        alignment: 'right',
                        color:
                          variance > -10.0 &&
                          variance < 0.0 &&
                          node.data.kpi_slno != 27 &&
                          node.data.kpi_slno != 22 &&
                          node.data.kpi_slno != 12 &&
                          node.data.kpi_slno != 13
                            ? // ? 'brown'
                              'red'
                            : variance <= -10.0 &&
                              node.data.kpi_slno != 27 &&
                              node.data.kpi_slno != 22 &&
                              node.data.kpi_slno != 12 &&
                              node.data.kpi_slno != 13
                            ? 'red'
                            : variance > 0.0 &&
                              (node.data.kpi_slno == 27 ||
                                node.data.kpi_slno == 22 ||
                                node.data.kpi_slno == 12 ||
                                node.data.kpi_slno == 13)
                            ? 'red'
                            : goal != 0 && variance == 0
                            ? // ? '#fff'
                              '#000'
                            : 'green',
                        fontSize: 6,
                        style: 'subheader'
                      }
                    ]
                  : goal != '' && goal != 0
                  ? [
                      {
                        text: goal,
                        alignment: 'right',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: ' / ',
                        alignment: 'right',
                        fontSize: 6,
                        style: 'subheader'
                      },
                      {
                        text: variance,
                        color:
                          variance > -10.0 &&
                          variance < 0.0 &&
                          node.data.kpi_slno != 27 &&
                          node.data.kpi_slno != 22 &&
                          node.data.kpi_slno != 12 &&
                          node.data.kpi_slno != 13
                            ? //? 'brown'
                              'red'
                            : variance <= -10.0 &&
                              node.data.kpi_slno != 27 &&
                              node.data.kpi_slno != 22 &&
                              node.data.kpi_slno != 12 &&
                              node.data.kpi_slno != 13
                            ? 'red'
                            : variance > 0.0 &&
                              (node.data.kpi_slno == 27 ||
                                node.data.kpi_slno == 22 ||
                                (node.data.kpi_slno == 12 &&
                                  node.data.kpi_slno == 13))
                            ? 'red'
                            : goal != 0 && variance == 0
                            ? // ? '#fff'
                              '#000'
                            : 'green',
                        alignment: 'right',
                        fontSize: 6,
                        style: 'subheader'
                      }
                    ]
                  : ''
            }
            // {
            //   text: goal != '' && goal != 0 ? goalVal : '',
            //   alignment: 'right',
            //   fontSize: 6,
            //   style: 'subheader'
            // }
          ];
        }
        return {
          text: value

          // styles
          //...column.getColDef().cellStyle
        };
      } else {
        return {
          text: [
            {
              text:
                typeof gridApi.api.getValue(column, node) == 'string'
                  ? node.data.kpi_no +
                    '   ' +
                    gridApi.api.getValue(column, node)
                  : '',
              fontSize: 6,
              color: '#fff',
              bold: true
            }
          ],
          fillColor: '#c65911'
          // styles
        };
      }
    } else {
      let value;
      const pathaname = window.location.pathname.split('/')[1];
      if (
        pathaname == 'KPIReportComparative' &&
        column.colId == 'Total Selected_1'
      ) {
        value = [
          {
            text:
              typeof gridApi.api.getValue(column, node) == 'string'
                ? typeof node.allLeafChildren[0].data.kpi_type_code !=
                  'undefined'
                  ? node.allLeafChildren[0].data.kpi_type_code +
                    ')   ' +
                    gridApi.api
                      .getValue(column, node)
                      .replace('<p>', ' ')
                      .replace('</p>', ' ')
                      .replace('<u>', ' ')
                      .replace('</u>', ' ')
                  : gridApi.api.getValue(column, node)
                : '',
            alignment: 'left',
            fontSize: 6

            //text: data.length > 0 ? data + '       Goal/Var' : '',
          }
        ];
      } else if (
        pathaname == 'KPIReportStoreComparative' &&
        (column.colId == 'Group Roll-Up_1' || column.colId == 'Group Average_1')
      ) {
        value = [
          {
            text:
              typeof gridApi.api.getValue(column, node) == 'string'
                ? typeof node.allLeafChildren[0].data.kpi_type_code !=
                  'undefined'
                  ? node.allLeafChildren[0].data.kpi_type_code +
                    ')   ' +
                    gridApi.api
                      .getValue(column, node)
                      .replace('<p>', ' ')
                      .replace('</p>', ' ')
                      .replace('<u>', ' ')
                      .replace('</u>', ' ')
                  : gridApi.api.getValue(column, node)
                : '',
            alignment: 'left',
            fontSize: 6

            //text: data.length > 0 ? data + '       Goal/Var' : '',
          }
        ];
      } else {
        value = [
          {
            text:
              typeof gridApi.api.getValue(column, node) == 'string'
                ? typeof node.allLeafChildren[0].data.kpi_type_code !=
                  'undefined'
                  ? node.allLeafChildren[0].data.kpi_type_code +
                    ')   ' +
                    gridApi.api
                      .getValue(column, node)
                      .replace('<p>', ' ')
                      .replace('</p>', ' ')
                      .replace('<u>', ' ')
                      .replace('</u>', ' ')
                  : gridApi.api.getValue(column, node)
                : '',
            alignment: 'left',
            fontSize: 6

            //text: data.length > 0 ? data + '       Goal/Var' : '',
          },
          {
            text: '',
            alignment: 'right',
            fontSize: 6,
            style: 'subheader'
          }
        ];
      }
      return {
        text: value

        // styles
        //...column.getColDef().cellStyle
      };
    }
  };

  /**
   * This function iterates over all of the rows and columns to create
   * a matrix of cells
   */
  const getRowsToExport = (gridApi, expandedKPIs, hideGoals) => {
    //Enables export when pivoting

    if (Object.keys(gridApi).length != 0) {
      if (gridApi.columnApi.isPivotMode()) {
        return getRowsToExportPivot(gridApi);
      }
      const columns = gridApi.columnApi.getAllDisplayedColumns();

      const rowsToExport = [];
      gridApi.api.forEachNodeAfterFilterAndSort(node => {
        if (
          !expandedKPIs.includes(node.key) &&
          typeof node.parent != 'undefined' &&
          !expandedKPIs.includes(node.parent.key)
        ) {
          const rowToExport = columns.map((column, i) => {
            return getCellToExport(gridApi, column, node, i, hideGoals);
          });

          rowsToExport.push(rowToExport);
        }
      });
      console.log('rowsToExport', rowsToExport);
      return rowsToExport;
    }
  };

  // Row colors
  const TITLE_ROW_COLOR = '#ffc000';
  const HEADER_ROW_COLOR = '#c65911';
  const EVEN_ROW_COLOR = '#fcfcfc';
  const ODD_ROW_COLOR = '#fff';

  const PDF_INNER_BORDER_COLOR = '#dde2eb';
  const PDF_OUTER_BORDER_COLOR = '#babfc7';

  const createLayout = (numberOfHeaderRows, rows) => ({
    fillColor: (rowIndex, node) => {
      if (rowIndex < numberOfHeaderRows) {
        return HEADER_ROW_COLOR;
      }
      if (rows.includes(rowIndex - 1)) {
        return TITLE_ROW_COLOR;
      }
      // return rowIndex % 2 === 0 ? EVEN_ROW_COLOR : ODD_ROW_COLOR;
    },

    //vLineHeight not used here.
    vLineWidth: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.widths.length ? 1 : 0,
    hLineColor: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.body.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR,
    vLineColor: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.widths.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR
  });

  /**
   * Returns a pdfMake shaped config for export, for more information
   * regarding pdfMake configuration, please see the pdfMake documentation.
   */
  function splitIntoGroups(array, groupSize) {
    const kpiElement = array[0]; // Assuming the first element is "KPI Name"
    const groups = [];

    // Start iterating from the second element
    for (let i = 1; i < array.length; i += groupSize - 1) {
      let group;
      // if (i == 1) {
      //   group = [kpiElement, ...array.slice(i, i + groupSize - 1)];
      // } else {
      //   if (kpiElement.text != 'KPI Name') {
      //     kpiElement.text.forEach(item => {
      //       if (item.text) {
      //         item.text = item.text + ' 1';
      //       }
      //     });
      //   } else {
      //     kpiElement.text = 'KPI Name 1';
      //   }

      //   group = [kpiElement, ...array.slice(i, i + groupSize - 1)];
      // }
      group = [kpiElement, ...array.slice(i, i + groupSize - 1)];
      groups.push(group);
    }

    return groups;
  }
  function toDataUrl(url, callback) {
    var xhr = new XMLHttpRequest();
    xhr.onload = function() {
      var reader = new FileReader();
      reader.onloadend = function() {
        callback(reader.result);
      };
      reader.readAsDataURL(xhr.response);
    };
    xhr.open('GET', url);
    xhr.responseType = 'blob';
    xhr.send();
  }
  const getTableContent = (
    gridApi,
    filterStart,
    filterEnd,
    images,
    expandedKPIs,
    hideGoals,
    visibleTotals
  ) => {
    if (Object.keys(gridApi).length != 0) {
      const columns = gridApi.columnApi.getAllDisplayedColumns();

      const groupedColumns = splitIntoGroups(columns, 4);
      const pathaname = window.location.pathname.split('/')[1];
      var con = [];

      var topbar = {
        style: 'tableExample',
        table: {
          layout: 'noBorders',
          widths: [180, 60, 273],
          heights: [30],
          body: [
            [
              {
                margin: [0, 4, 0, 0],
                alignment: 'left',
                fillColor: '#c65911',
                border: [false, false, false, false],
                text: [
                  {
                    text:
                      pathaname == 'KPIReportComparative'
                        ? 'KPI Advisor Comparative\n'
                        : 'KPI Store Comparative\n',
                    fontSize: 10,
                    color: '#fff'

                    // background: '#c65911'
                  },
                  {
                    text:
                      pathaname == 'KPIReportComparative'
                        ? localStorage.getItem('storeSelected') + '\n'
                        : localStorage.getItem('storeGroup') + '\n',
                    fontSize: 7,
                    color: '#fff'
                  },
                  {
                    text:
                      moment(filterStart).format('MM/DD/YY') +
                      ' - ' +
                      moment(filterEnd).format('MM/DD/YY'),
                    fontSize: 7,
                    color: '#fff'
                    //  background: '#c65911'
                  }
                ]
              },
              {
                image: images,
                fit: [50, 50],
                // marginRight: 1000,
                backgroundColor: 'none',
                // left: 100,
                fillColor: '#c65911',
                border: [false, false, false, false],
                margin: [0, 4, 20, 0],
                alignment: 'left'
              },
              {
                border: [false, false, false, false],
                alignment: 'left',
                color: '#fff',
                fillColor: '#c65911',
                fontSize: 18,
                margin: [0, 6, 0, 0],
                text: 'Fixed Ops Performance Center'
              }
            ]
          ]
        }
      };
      con.push(topbar);

      groupedColumns.map((col, i) => {
        const headerRow = getHeaderToExport(gridApi, hideGoals, visibleTotals);

        const groupedData = splitIntoGroups(headerRow, 4);

        const rows = getRowsToExport(gridApi, expandedKPIs, hideGoals);

        var grpRows = [];
        rows.map((row, i) => {
          grpRows.push(splitIntoGroups(row, 4));
        });

        var colWidth = [];

        // groupedData[i].map((ele, p) => {
        //   if (p == 0) {
        //     colWidth.push(160);
        //   } else {
        //     colWidth.push(120);
        //   }
        // });
        var head = groupedData[i].flatMap((item, index) => {
          if (index === 0) return item;

          return item.text.map(textItem => ({
            text: [textItem]
          }));
        });

        var elem = grpRows.map(function(grpItem) {
          const outputArray = grpItem[i].flatMap((item, index) => {
            if (index === 0) return item;

            return item.text.map(textItem => ({
              text: [textItem]
            }));
          });
          const filteredData = outputArray.slice(0, head.length);
          //  console.log('eleoutputArraym==item', i, head.length, filteredData);
          return filteredData;
        });
        console.log('vvv===', visibleTotals);
        elem[1].map((ele, p) => {
          if (i != 0) {
            if (p == 0) {
              colWidth.push(140);
            } else if (p % 2 == 0) {
              colWidth.push(34);
            } else {
              colWidth.push(80);
            }
          } else {
            if (p == 0) {
              colWidth.push(140);
            } else if (
              visibleTotals.includes('Total Selected') &&
              (p % 2 == 0 || p % 4 == 0 || p % 6 == 0)
            ) {
              colWidth.push(43);
            } else if (
              visibleTotals.includes('Group Roll-Up') &&
              visibleTotals.includes('Group Average') &&
              (p % 2 == 0 || p % 4 == 0 || p % 6 == 0)
            ) {
              colWidth.push(51);
            } else if (
              (visibleTotals.includes('Group Roll-Up') ||
                visibleTotals.includes('Group Average')) &&
              (p % 3 == 0 || p % 5 == 0)
            ) {
              colWidth.push(55);
            } else if (
              (pathaname == 'KPIReportComparative' &&
                ((p % 2 == 0 && p % 4 != 0) || p % 5 == 0)) ||
              (pathaname == 'KPIReportStoreComparative' && p % 5 == 0)
            ) {
              colWidth.push(55);
            } else {
              if (pathaname == 'KPIReportComparative') {
                colWidth.push(80);
              } else if (
                pathaname == 'KPIReportStoreComparative' &&
                !visibleTotals.includes('Group Roll-Up') &&
                !visibleTotals.includes('Group Average')
              ) {
                colWidth.push(90);
              } else {
                colWidth.push(80);
              }
            }
          }
        });

        //   console.log('elem==item', head, elem, colWidth);
        var indexArr = [];
        elem.forEach((group, groupIndex) => {
          group.forEach((item, itemIndex) => {
            if (item.text.length === 2) {
              indexArr.push(groupIndex);
            }
          });
        });

        var body = {
          table: {
            widths: colWidth,
            // the number of header rows
            headerRows: 1,

            // the width of each column, can be an array of widths
            //widths: `${100 / columns.length}%`,

            // all the rows to display, including the header rows
            body: [head, ...elem],

            // Header row is 40px, other rows are 15px
            heights: rowIndex => (rowIndex === 0 ? 10 : 10)
          },

          layout: createLayout(1, indexArr)
        };

        con.push(body);

        if (i + 1 != groupedColumns.length) {
          con.push({ text: '', pageBreak: 'before', style: 'subheader' });
        }
      });

      return con;
    }
  };
  const getDocument = (
    gridApi,
    filterStart,
    filterEnd,
    images,
    expandedKPIs,
    hideGoals,
    fileName,
    visibleTotals
  ) => {
    // const getDocument = (gridApi, filterStart, filterEnd) => {
    if (Object.keys(gridApi).length != 0) {
      return {
        pageOrientation: 'portrait', // can also be 'portrait'
        // pageSize: {
        //   width: 950,
        //   height: 'auto'
        // },
        content: getTableContent(
          gridApi,
          filterStart,
          filterEnd,
          images,
          expandedKPIs,
          hideGoals,
          visibleTotals
        ),

        pageMargins: [20, 45, 20, 20],
        styles: {
          subheader: {
            margin: [0, 0, 0, 0]
          }
        }
      };
    }
  };

  const exportToPDF = (
    gridApi,
    filterStart,
    filterEnd,
    fileName,
    expandedKPIs,
    hideGoals,
    base64Image,
    visibleTotals
  ) => {
    if (Object.keys(gridApi).length !== 0) {
      const doc = getDocument(
        gridApi,
        filterStart,
        filterEnd,
        base64Image,
        expandedKPIs,
        hideGoals,
        fileName,
        visibleTotals
      );
      pdfMake.createPdf(doc).download(fileName);
    }
  };
  return {
    exportToPDF
  };
};
