import React, { useEffect, useState } from 'react';
import ImportExportIcon from '@material-ui/icons/ImportExport';
import SwapHorizIcon from '@material-ui/icons/SwapHoriz';
import ReactHtmlParser from 'react-html-parser';
import { useDispatch, useSelector } from 'react-redux';
import { setRowSortOrder, setHiddenKpiRows } from 'src/actions';
import { Tooltip } from '@material-ui/core';
export default props => {
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const [sort, setSort] = useState(session.sortOrder.order);
  const [sortChanged, setSortChanged] = useState(false);
  const [hidnRows, setHidnRows] = useState(session.hiddenKpis);
  const [kpiName, setKpiName] = useState([]);
  const [kpiCount, setKpiCount] = useState([]);

  const handleLinkClick = () => {
    props.context.onRowClicked(props);
  };
  const handleArrowClick = () => {
    let itemlLen;
    if (props.value == 'Parts To Labor Ratio') {
      itemlLen = props.value.split('To');
    } else {
      itemlLen = props.value.split('/');
    }

    var sortValue = session.sortOrder.order;

    if (
      sortValue == itemlLen.length - 1 ||
      (session.sortOrder.row != '' && session.sortOrder.row != props.value)
    ) {
      var labelLen = {
        row: props.value,
        order: 0,
        slno: props.data.kpi_slno
      };
      setSort(0);
    } else {
      var labelLen = {
        row: props.value, // kpi row name
        order: sortValue + 1, // order of values, 0 in first click, 1 in second click..
        slno: props.data.kpi_slno // kpi number from data
      };
      setSort(sortValue + 1);
    }
    console.log('labelLen===', labelLen);
    setSortChanged(true);
    dispatch(setRowSortOrder(labelLen));
  };
  useEffect(() => {
    if (sortChanged && typeof props.data != 'undefined') {
      props.context.onArrowClicked(
        props,
        props.value,
        props.data.kpi_slno,
        sort,
        'child'
      );
      setSortChanged(false);
    }
  }, [sort, sortChanged]);
  const [kpiNo, setKpiNo] = useState();
  const handlePageClick = number => {
    var rowsArr = [];
    let rowNode = props.api.getDisplayedRowAtIndex(props.rowIndex); // get the clicked row

    rowsArr.push(rowNode);
    let arr = {
      type: props.value, // kpi group A, B, C ....
      kpi: number, // 1,2,3
      si_no: props.node.allLeafChildren[number - 1].data.kpi_slno // selected kpi number from data
    };
    var hidRows = session.hiddenKpis;

    if (
      typeof hidRows != 'undefined' &&
      hidRows.some(function(el) {
        return el.kpi == number && el.type == props.value;
      })
    ) {
      hidRows = hidRows.filter(
        item => !(item.type === arr.type && item.kpi === arr.kpi)
      );
    } else {
      hidRows.push(arr);
    }
    console.log('hidRows===', hidRows);
    setHidnRows(hidRows);
    dispatch(setHiddenKpiRows(hidRows));
    props.context.handleRowShowHide(props, number, props.value);
  };
  useEffect(() => {
    const filteredArr = props.context.rowData.filter(
      x => x.kpi_type == props.value
    );

    if (filteredArr.length > 0) {
      setKpiNo(filteredArr[0].kpi_type_code);
      setKpiCount(filteredArr.length);
    }
  }, []);
  useEffect(() => {
    //const kpi = props.value.split('/');
    let kpi;
    if (props.value == 'Parts To Labor Ratio') {
      kpi = props.value.split('To');
      var label = kpi[1].trim().split(' ');

      kpi = [kpi[0]].concat(label);
    } else {
      kpi = props.value.split('/');
    }
    setKpiName(kpi);
  }, []);

  if (
    props.node.allChildrenCount != null ||
    (props.node.allChildrenCount != null &&
      props.context.hiddenRowId.length > 0)
  ) {
    var arr = props.context.rowData.filter(
      ({ kpi_type }) => kpi_type == props.value
    );
    const numbers = Object.keys(arr).map(function(k) {
      return arr[k].kpi_no;
    });

    return (
      <div className="ag-header-group-cell-label">
        <div className="kpi-header-cell">
          <ImportExportIcon
            onClick={e => handleLinkClick()}
            className="kpi_expand"
          ></ImportExportIcon>

          <div className="customHeaderOpt">
            {typeof kpiNo != 'undefined' ? kpiNo : ''}
          </div>
          <div className="customGrpHeaderLabel">
            {ReactHtmlParser(props.value)}
          </div>

          <div className="pagination">
            {numbers.map(number => (
              <a
                // className={
                //   !props.context.hiddenRowId.some(
                //     el => el.kpi == number && el.type == props.value
                //   )
                //     ? 'hidrow'
                //     : 'showrow'
                // }
                style={{
                  textDecoration:
                    typeof hidnRows != 'undefined' &&
                    hidnRows.find(
                      el => el.kpi == number && el.type == props.value
                    )
                      ? 'line-through'
                      : 'underline'
                }}
                onClick={e => handlePageClick(number)}
                key={number}
              >
                {number}
              </a>
            ))}
          </div>
        </div>
      </div>
    );
  } else {
    return (
      <div className="ag-header-group-cell-label">
        <div className="kpi-value-cell">
          <div className="customHeaderOpt">{props.data.kpi_no}</div>

          <div className="customRowLabel">
            {kpiName.map((item, i) => (
              <div className="kpi-section">
                <span
                  className={
                    i == sort &&
                    session.sortOrder.row.includes(item) &&
                    props.data.kpi_slno == session.sortOrder.slno
                      ? 'kpi-label kpi-selected'
                      : 'kpi-label'
                  }
                >
                  {item}
                </span>
                {props.value != 'Parts To Labor Ratio' &&
                  i != kpiName.length - 1 && (
                    <span className="kpi-line">{'/'}</span>
                  )}
                {props.value == 'Parts To Labor Ratio' && i == 0 && (
                  <span className="kpi-line">{' To '}</span>
                )}
              </div>
            ))}
          </div>
          <Tooltip
            title={<span style={{ fontSize: 11, height: 10 }}>Sort</span>}
          >
            <SwapHorizIcon
              className="kpi_sort"
              onClick={handleArrowClick}
            ></SwapHorizIcon>
          </Tooltip>
        </div>
      </div>
    );
  }
};
