import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { getTimeZone } from '../../utils/Utils';
import { getGridorMatrixPayTypeDetails } from 'src/utils/hasuraServices';
import Kpi from './Kpi';
import { setLaborGridTypes, setPartsMatrixTypes } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { CircularProgress, Grid } from '@material-ui/core';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));

function KpiReport() {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const history = useHistory();
  var timezone = getTimeZone();
  const pathaname = history.location.pathname.split('/')[1];
  const [selectedPayType, setSelectedPayType] = useState('');
  const [matrixTypes, setMatrixType] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingParts, setLoadingParts] = useState(false);
  const [kpiReportData, setKpiReportData] = useState({});
  const dispatch = useDispatch();
  useEffect(() => {
    const historyState = history?.location?.state || {};
    console.log('hhh===', historyState.expanded_kpis);
    let visibility =
      history &&
      history.location &&
      history.location.state &&
      history.location.state.visibility
        ? history.location.state.visibility
        : 'private';
    const pickerStatus = true;
    const reportName = historyState.report_name || '';
    const startDate =
      historyState.start_date ||
      (session.kpiToggleStartDate !== '' ? session.kpiToggleStartDate : '');
    const endDate =
      historyState.end_date ||
      (session.kpiToggleEndDate !== '' ? session.kpiToggleEndDate : '');
    const kpiIds = historyState.kpi_ids || [];
    const expandedKPIs = historyState.expanded_kpis || [];
    const hiddenKpiNumbers = historyState.hidden_kpi_numb || [];
    const kpiSortOrder = historyState.kpi_sort_order || { row: '', order: 0 };
    const selectedGoals = historyState.selected_goals || [];
    const createdBy = historyState.created_by || '';
    const parentval = historyState.parent || '';

    const pathname = history?.location?.pathname.split('/')[1] || '';

    var data = {
      parent: parentval,
      parentSelected: parentval,
      pickerStatus: pickerStatus,
      report_name: reportName,
      startDate: startDate,
      endDate: endDate,
      kpi_ids: kpiIds,
      //selStores: kpiIds,
      expanded_kpis: expandedKPIs,
      hidden_kpi_numb: hiddenKpiNumbers,
      kpi_sort_order: kpiSortOrder,
      selected_goals: selectedGoals,
      created_by: createdBy,
      visibility: visibility,
      kpiHomeToggle: session.kpiHomeToggle,
      isFrom: pathname
    };
    setKpiReportData(data);
  }, []);

  // const fetchData = () => {
  //   getGridorMatrixPayTypeDetails('paytype_matrix', result => {
  //     if (result.length > 0) {
  //       setMatrixType(result);
  //       setSelectedPayType(result[0]);
  //     } else {
  //       setMatrixType(result);
  //       setSelectedPayType(result);
  //     }
  //   });
  // };
  return (
    <Page
      className={classes.root}
      title={
        pathaname == 'KPIReportComparative'
          ? 'KPI Advisor Comparative'
          : 'KPI Store Comparative'
      }
      // title={'KPI Report #2 - Advisor Comparative'}
    >
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      history &&
      history.location &&
      history.location.pathname &&
      history.location.pathname != '/KPIReportStoreComparative' ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Kpi history={history} kpiReportData={kpiReportData} />
      )}
    </Page>
  );
}

export default KpiReport;
