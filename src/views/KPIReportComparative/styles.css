/* .ag-header-cell-label {
    /*Necessary to allow for text to grow vertically*/
/* height: 100%;
    padding: 0 !important;
  }
  
  .ag-header-cell-label .ag-header-cell-text { */
/*Force the width corresponding to how much width
      we need once the text is laid out vertically*/
/* width: 55px;
    writing-mode: vertical-lr;
    -ms-writing-mode: tb-lr;
    line-height: 2em;
    margin-top: 60px;
  } */
/* .cell-container .ag-react-container .ag-header-group-cell-label {
    width: 100%
  } */
.kpi-header-cell {
  width: 100%;
  display: flex;
  justify-content: space-between;
  /* align-items: end; */
}
.kpi-value-cell {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: end;
}
.customGrpHeaderLabel {
  /* width: 100%; */
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 13px;
}
.customRowLabel {
  font-weight: bold;
  color: #fff;
  display: flex;
}
/* .ag-pivot-off .ag-header-group-cell {
    font-size: 20px;
  }
  
  .ag-pivot-on .ag-header-group-cell {
    font-size: 10px;
    color: green;
  } */

/* .ag-pivot-off .ag-header-cell-label {
    color: #8a6d3b;
  } */

#kpi-report-2 .cell-container .ag-react-container {
  width: 100%;
}
.report-title {
  font-size: 14px;
}
.report-store {
  font-size: 18px;
  padding-bottom: 12px;
}
.report-date {
  font-size: 11px !important;

  font-weight: bold;
}
.kpi-hide-header {
  display: none;
}

#kpi-report-2
  div[aria-rowindex='1']
  .ag-header-group-cell-no-group:first-of-type {
  width: 10px !important;
}
#kpi-report-2 div[col-id='0_0'] {
  left: 30px !important;
  width: 1150px !important;
}
/* #kpi-report-2  div[col-id="kpi_type"] {
     display: none !important;
     width: 0px !important;
  } */
#kpi-report-2 .ag-icon-tree-closed {
  display: none !important;
}
#kpi-report-2 .ag-icon-tree-open {
  display: none !important;
}
#kpi-report-2 .ag-theme-balham .ag-ltr .ag-column-select-add-group-indent {
  margin-left: 0px !important;
  padding-left: 4px;
  display: none;
}
/* #kpi-report-2 .ag-column-select-indent-0{
   
    display: flex !important;
   } */
.pagination {
  display: inline-block;
}
.cell-no-border-top {
  border-top: #c65911 !important;
}
.ag-row:has(.cell-no-border-top) {
  border-top: #c65911 !important;
  height: 26px !important;
}

.cell-no-border-bottom {
  border-bottom: #c65911 !important;
}
.ag-row:has(.cell-no-border-bottom) {
  border-bottom: #c65911 !important;
  height: 26px !important;
}
.pagination a {
  color: #000;
  float: left;
  padding: 0px 2px;
  text-decoration: none;
  /* font-weight: bold; */
  /* text-decoration: underline; */
}
.customHeaderOpt,
.customHeaderLabel {
  padding: 0px 4px;
  font-weight: bold;
}
.customHeaderLabel {
  font-weight: bold;
}
#kpi-report-2 .auto-group-style {
  background-color: #c65911;
}
#kpi-report-2 .custom-cell {
  background-color: #c65911;
  border: 1px solid #0000005e;
  border-width: 0.1em;
  padding-left: 0px;
  color: #fff;
}

#kpi-report-2 .custom-cell .ag-row-group-leaf-indent {
  margin-left: 0px !important;
  padding-left: 0px !important;
  display: flex;
}
#kpi-report-2 .custom-cell .ag-group-value {
  width: 100%;
}

.custom-header {
  padding-left: 0px !important;
  padding-right: 0px !important;
  line-height: 20px !important;
}

#kpi-report-2 .custom-header .ag-group-value {
  width: 100%;
}

.custom-no-data-row {
  padding-left: 0px !important;
  padding-right: 0px !important;
  line-height: 20px !important;
}

#kpi-report-2 .custom-no-data-row .ag-group-value {
  width: 100%;
}
.kpi-group-header {
  justify-content: left !important;
  padding-right: 0px !important;
}
#kpi-report-2 .ag-header-group-cell::after {
  background-color: var(--ag-header-column-separator-color, rgb(198, 89, 17));
}
#kpi-report-2 .kpi-group-header .ag-header-group-cell-label {
  justify-content: center;
}
#kpi-report-2 .kpi-group-header-goal {
  justify-content: center;
  padding-left: 12px !important;
}
#kpi-report-2 .customExpandButton {
  float: right;
  margin-top: 2px;
  /* margin-left: 3px; */
  color: #fff;
}
#kpi-report-2 .ag-cell-auto-height .ag-cell-value {
  width: 300px;
}
#kpi-report-2 .expanded {
  animation-name: toExpanded;
  animation-duration: 1s;
  -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
  transform: rotate(180deg);
}

#kpi-report-2 .collapsed {
  /* color: cornflowerblue; */
  animation-name: toCollapsed;
  animation-duration: 1s;
  -webkit-transform: rotate(0deg); /* Chrome, Safari, Opera */
  transform: rotate(0deg);
}

.customHeaderMenuButton,
.customHeaderLabel,
.customHeaderLabel,
.customSortDownLabel,
.customSortUpLabel,
.customSortRemoveLabel {
  margin-top: 2px;
  /* margin-left: 4px; */
  float: left;
}

.customSortDownLabel {
  margin-left: 10px;
}

.customSortUpLabel {
  margin-left: 1px;
}

.customSortRemoveLabel {
  float: left;
  font-size: 11px;
}

.active {
  /* color: cornflowerblue; */
}

@keyframes toExpanded {
  from {
    /* color: cornflowerblue; */
    -webkit-transform: rotate(0deg); /* Chrome, Safari, Opera */
    transform: rotate(0deg);
  }
  to {
    color: black;
    -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
    transform: rotate(180deg);
  }
}

@keyframes toCollapsed {
  from {
    color: black;
    -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
    transform: rotate(180deg);
  }
  to {
    /* color: cornflowerblue; */
    -webkit-transform: rotate(0deg); /* Chrome, Safari, Opera */
    transform: rotate(0deg);
  }
}
.kpi_expand {
  cursor: default;
}

/* #kpi-report-2 .ag-theme-balham .ag-icon-grip::before{
    display: none !important;
  } */

.kpi-custom-table
  .ag-header
  .ag-pinned-left-header
  .ag-header-row:nth-child(1)
  .ag-header-group-cell::after {
  display: none;
}
.kpi-custom-table .ag-pinned-left-cols-container svg,
.makeStyles-kpiTable-149 .pagination,
.kpi-custom-table .customExpandButton .fa-arrow-right {
  cursor: pointer;
}
/* .daterangepicker .ranges li {
    display: flex;
  
  }
  .daterangepicker .ranges li > div:first-of-type{
    margin-right: 8px;
    width:112px
  } */

.kpi-custom-table .ag-header-viewport {
  background-color: #fff !important;
}

.kpi-custom-table .ag-header-row-column-group {
  background-color: #c65911 !important;
  color: #fff !important;
}
.custom-header-kpir {
  background-color: #c65911;
  padding: 15px;
  display: inline-block;
  width: 100%;
  margin-bottom: -5px;
}
.report-name-title {
  display: grid;
  grid-gap: 4px;
  text-align: center;
}
.report-name-title span {
  display: block;
  color: #fff;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.report-num {
  text-decoration: underline;
}
.report-num,
.report-name {
  font-size: 15px;
  font-weight: bold;
}
.header-logo,
.report-name-title {
  float: left;
}
.header-logo img {
  width: 100px;
}
.header-logo {
  padding: 0 65px;
}
.fopc-main-title {
  float: left;
  color: #fff;
  font-size: 30px;
  font-weight: bold;
  margin-right: 15px;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
}
.fopc-main-title p::first-letter {
  color: #000;
}
.kpiscore-card-main {
  padding-left: 8px;
  padding-right: 16px;
}
.fopc-main-title {
  padding-top: 10px;
}
.report-name-title {
  padding-top: 5px;
}
.custom-cell-no-display {
  display: none;
}
.customGrpHeaderLabel > u {
  padding: 0px 3px;
}

#kpi-report-2 .ag-root-wrapper {
  border-top: none !important;
  border-left: solid 1px #003d6b !important;
}

#kpi-report-2 .ag-pinned-left-header {
  border: none !important;
}

#kpi-report-2 .ag-theme-balham .ag-row:not(.ag-row-first) {
  border-top-style: none;
  border-bottom-style: solid;
}

#kpi-report-2 .ag-header-row-column {
  height: 24px !important;
}

#kpi-report-2 div[ref='headerRoot'] {
  height: 32px !important;
  min-height: 36px !important;
}
#kpi-report-2 .ag-header-row {
  border-bottom: none !important;
}

#kpi-report-2 .ag-side-bar {
  top: 30px;
}
#kpi-report-2 .ag-side-buttons {
  padding-top: 0px;
  background-color: #fff;
}
#kpi-report-2 .ag-root-wrapper-body {
  background-color: #c65911 !important;
}
#kpi-report-2 .ag-unselectable {
  background-color: #fff !important;
}
.kpi_to {
  margin: 0px 4px;
}
.adv-report .report-header {
  padding: 16px 4px;
}
#kpi-report-2 .ag-theme-balham .ag-overlay-loading-wrapper {
  margin-top: 40px;
}
#kpi-report-2 .ag-overlay-loading-center {
  padding: 4px !important;
}
.kpi-selected {
  text-decoration: underline;
}
.ratio-label {
  margin-left: 4px;
  margin-right: 4px;
}
#kpi-report-2 .ag-column-select-list {
  padding-left: 0px !important;
}
.customHeaderLabelVar {
  margin-right: 0px;
}
.kpi-data-cell {
  display: flex;
  justify-content: space-between;
}
.kpi-all-data {
  display: flex;
  align-items: center;
  width: 225px;
}
.var-data-div {
  font-size: 10px;
  font-weight: bold;
}
