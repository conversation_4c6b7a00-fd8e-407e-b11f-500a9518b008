import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import LaborOpportunityCharts from 'src/components/charts/OpportunityCharts';
import DashboardActions from 'src/components/DashboardActions';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { GROUP_TYPE_1 } from 'src/utils/constants';
import {
  getDataGridConfiguration,
  getLayoutConfiguration,
  saveLayoutConfiguration,
  getDataGridConfigTotalOpportunity,
  getIndexForOpportunityCharts
} from '../../utils/Utils';
import { withKeycloak } from '@react-keycloak/web';

var lodash = require('lodash');

const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 33,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    this.state = {
      isLoading: false,
      checkSuccess: false,
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-2') || {}
        )
      ),
      chartList: JSON.parse(global.localStorage.getItem('chart-master'))
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }

  setFilterCharts = value => {
    if (value === 2) {
      this.setState({ isLoading: true });
    } else if (value === 3) {
      this.setState({ isLoading: false });
      this.setState({ checkSuccess: false });
    }
  };

  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }
  };

  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-2');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }

  handleChange = value => {
    if (value) {
      this.setState({ checkSuccess: value });
    }
  };

  removeFav = value => {
    this.setState({
      isLoading: false
    });
  };
  handleClosePopup = value => {
    console.log('state===handleClosePopup');
    this.setState({
      popupChartId: '',
      open: false
    });
  };
  render() {
    let realm = this.props.keycloak.realm;
    let filteredResult = this.state.chartList.filter(
      item =>
        item.dbdName == 'CP Labor Gross Volume Opportunity' &&
        item.parentId == null
    );
    let orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    return (
      <div>
        <DashboardActions
          resetDashboard={this.setResetDashboard}
          filterCharts={this.setFilterCharts}
          setTitle="CP Labor Gross &amp; Volume Opportunity"
          goalSettings={true}
          restrictFilters={true}
          chartGroup={GROUP_TYPE_1}
          checkSuccess={this.handleChange}
        ></DashboardActions>

        {this.state.isLoading === true || this.state.checkSuccess == true ? (
          <LoaderSkeleton></LoaderSkeleton>
        ) : (
          // <ReactGridLayout
          //   {...this.props}
          //   layout={this.state.layout}
          //   onLayoutChange={this.onLayoutChange}
          //   isResizable={false}
          // >
          orderedData.map(item => {
            return (
              <div
                style={{
                  backgroundColor: '#FFF',
                  margin: '50px',
                  //  width: '80%',
                  //marginLeft: '187px',
                  marginTop: '15px'
                }}
                // key={getIndexForOpportunityCharts(Number(item.chartId))}
                // data-grid={
                //   Number(item.chartId) != 931
                //     ? getDataGridConfiguration(
                //         getIndexForOpportunityCharts(Number(item.chartId))
                //       )
                //     : getDataGridConfigTotalOpportunity(
                //         getIndexForOpportunityCharts(Number(item.chartId))
                //       )
                // }
              ></div>
            );
          })
          // </ReactGridLayout>
        )}
      </div>
    );
  }
}

export default withKeycloak(Charts);
