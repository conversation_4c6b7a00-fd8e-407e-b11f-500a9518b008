import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { useSelector, useDispatch } from 'react-redux';
import { Redirect } from 'react-router-dom';
import Charts from './Charts';

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function LaborGrossAndVolumeOpportunity() {
  const classes = useStyles();
  const history = useHistory();
  const session = useSelector(state => state.session);
  return (
    <Page
      className={classes.root}
      // title="Labor Gross And Volume Opportunity"
      title={'“What If” Opportunity \n Hrs Per RO & Labor GP%'}
    >
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts history={history} session={session} />
      )}
    </Page>
  );
}

export default LaborGrossAndVolumeOpportunity;
