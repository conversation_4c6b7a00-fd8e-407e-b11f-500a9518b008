import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import WorkMixVolume from './WorkMixVolume';
import { useDispatch, useSelector } from 'react-redux';
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function WorkMixVolumeEdit() {
  const classes = useStyles();
  const session = useSelector(state => state.session);console.log("enter labor work mix volume=");
  return (
    <Page className={classes.root} title="Labor" >
      <WorkMixVolume session={session}/>
    </Page>
  );
}

export default WorkMixVolumeEdit;
