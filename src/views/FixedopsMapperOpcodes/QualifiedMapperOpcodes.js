import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Toolbar,
  Grid,
  Button
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import clsx from 'clsx';
import { withStyles } from '@material-ui/styles';
import RestoreIcon from '@material-ui/icons/Restore';
import {
  getQualifiedMapperOpcodes,
  getFixedopsVsMapperOpCategorization
} from 'src/utils/hasuraServices';
import NonQualifiedMapperOpcodes from './NonQualifiedMapperOpcodes';
import { ReactSession } from 'react-client-session';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class QualifiedMapperOpcodes extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData();
        // }
      }
    }
  }
  // componentDidUpdate(prevProps) {
  //   if(prevProps.session.storeSelected && JSON.parse(localStorage.getItem('selectedStoreId')) ){
  //     if(JSON.parse(localStorage.getItem('selectedStoreId'))[0]!=JSON.parse(prevProps.session.storeSelected)[0])
  //     {
  //       console.log("stores=",JSON.parse(localStorage.getItem('selectedStoreId'))[0] == JSON.parse(prevProps.session.storeSelected)[0]);
  //       this.getAgGridData();
  //     }
  //   }
  // }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      showRefresh: 'none',
      disableTable: 'all',
      rawGridApi: {},
      tabSelection: 'one',
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      columnDefs: [
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          field: 'lbropcode',
          cellClass: 'textAlignopcode',
          // width: 100,
          flex: 1,
          minWidth: 100,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode Desc',
          chartDataType: 'series',
          field: 'opdesc',
          // width: 300,
          minWidth: 300,
          flex: 1,
          tooltipField: 'opdesc',
          filterParams: {
            valueGetter: function(params) {
              console.log('params===', params);
              if (params.data.opdesc) {
                const data = params.data.opdesc;
                var count = 25;
                return (
                  data.slice(0, count) + (data.length > count ? '...' : '')
                );
              } else {
                return '';
              }
            }
          },
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Op Category',
          chartDataType: 'series',
          // width: 120,
          minWidth: 120,
          flex: 1,
          field: 'opcategory',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Count',
          chartDataType: 'series',
          field: 'count',
          // width: 87,
          minWidth: 87,
          flex: 1,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: 'Labor Sale',
          chartDataType: 'series',
          field: 'lbrsale',
          // width: 100,
          minWidth: 100,
          flex: 1,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$'
        },
        {
          headerName: 'Sold Hours',
          chartDataType: 'series',
          field: 'lbrsoldhours',
          // width: 90,
          minWidth: 90,
          flex: 1,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: this.formatCellValueHrs
        },
        {
          headerName: 'Mapper Desc',
          chartDataType: 'series',
          field: 'mapperDesc',
          tooltipField: 'mapperDesc',

          // width: 90,
          minWidth: 90,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Excluded',
          chartDataType: 'series',
          field: 'excluded',
          // width: 90,
          minWidth: 90,
          flex: 1,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Reason',
          chartDataType: 'category',
          field: 'reason',
          tooltipField: 'reason',
          // width: 113,
          minWidth: 113,
          flex: 1,
          filter: true,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true
          // cellStyle() {
          //   return { textAlign: 'center', border: ' 0px white' };
          // }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: false,
        suppressMovable: false
      },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'right'
          }
        },
        {
          id: 'textAlignopcode',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        }
      ]
    };
  }

  formatCellValue = params => {
    if (params.value != null && params.value !== 0) {
      const value = parseFloat(params.value);
      // Format with dollar sign and commas, handling negative values
      const formattedValue = `$${Math.abs(value)
        .toFixed(2)
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
      return value < 0 ? `-${formattedValue}` : formattedValue;
    } else {
      return '$0.00';
    }
  };

  formatCellValueHrs = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };
  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridColumnApi: params.columnApi });
    // this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    // getQualifiedMapperOpcodes((result) => {
    //     this.setState({ isLoading: false });
    //     if (result.data.dbdReferencesGetFixedOpsAndMapperQualifiedOpcodes.nodes) {
    //     this.setState({
    //       rowData: result.data.dbdReferencesGetFixedOpsAndMapperQualifiedOpcodes.nodes
    //     });
    //   }
    // });statelessDbdReferencesGetFixedopsVsMapperOpCategorization
    // getFixedopsVsMapperOpCategorization(result => {
    //   this.setState({ isLoading: false });
    //   if (
    //     result.data.statelessDbdReferencesGetFixedopsVsMapperOpCategorization
    //       .nodes
    //   ) {
    //     this.setState({
    //       rowData:
    //         result.data
    //           .statelessDbdReferencesGetFixedopsVsMapperOpCategorization.nodes
    //     });
    //   }
    // });
    getFixedopsVsMapperOpCategorization(result => {
      this.setState({ isLoading: false });
      const nodes = result?.data?.statelessDbdReferencesGetFixedopsVsMapperOpCategorization?.nodes;
      if (nodes) {
        const cleanedData = nodes.map(item => ({
          ...item,
          opdesc: item.opdesc ? item.opdesc.replace(/\r?\n|\r/g, ' ').trim() : '',
        }));
        this.setState({ rowData: cleanedData });
      }
    });
    
  }

  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      // columnKeys: ['makesId', 'manufacturer', 'validMakes'],
      fileName: 'Opcode Mapping with Statistics',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Opcode Mapping with Statistics'
            },
            mergeAcross: 3
          }
        ]
      ]
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  resetReportGrid = () => {
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,

            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
            cursor: 'default'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            TabIndicatorProps={{
              style: {
                backgroundColor: '#e7eef3'
              }
            }}
            id={'lineTab'}
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
            style={{ cursor: 'default' }}
          >
            <Tab
              label={<div>Opcode Mapping with Statistics</div>}
              value="one"
              style={{
                textTransform: 'none',
                pointerEvents: 'none',
                borderColor: '#e7eef3'
              }}
            />
            {/* <Tab
              style={{ textTransform: 'none', paddingRight: 182 }}
              label={<div>Non-Qualified Opcodes</div>}
              value="two"
            /> */}
            {/* <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{ paddingTop: 12, paddingRight: 156, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip> */}

            <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{
                  cursor: 'pointer',
                  marginTop: '10px',
                  marginBottom: '10px'
                }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
            <Button
              variant="contained"
              className={clsx(classes.back, 'reset-btn')}
              id="reset-layout"
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>
        {this.state.isLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: window.innerHeight - 190 + 'px',
            width: '1145px',
            margin: 8,
            display: this.state.tabSelection === 'two' ? 'none' : 'block'
            // pointerEvents: disableTable
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            sideBar={this.state.sideBar}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: 8,
    //width: '10%'
    width: 'auto'
  }
});
export default withStyles(styles)(QualifiedMapperOpcodes);
