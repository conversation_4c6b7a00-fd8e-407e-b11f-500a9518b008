import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Toolbar,
  Grid
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import ExportIcon from '@material-ui/icons/GetApp';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { getNonQualifiedMapperOpcodes } from 'src/utils/hasuraServices';

class NonQualifiedMapperOpcodes extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      showRefresh: 'none',
      disableTable: 'all',
      rawGridApi: {},
      tabSelection: 'one',
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      columnDefs: [
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          field: 'lbropcode',
          cellClass: 'textAlignopcode',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode Desc',
          chartDataType: 'series',
          field: 'opdesc',
          tooltipField: 'opdesc',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Op Category',
          chartDataType: 'series',

          field: 'opcategory',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Count',
          chartDataType: 'series',
          field: 'count',
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Labor Sale',
          chartDataType: 'series',
          field: 'lbrsale',
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: this.formatCellValue,
          cellClass: 'twoDecimalPlacesWith$'
        },
        {
          headerName: 'Sold Hours',
          chartDataType: 'series',
          field: 'lbrsoldhours',
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: this.formatCellValueHrs
        },
        {
          headerName: 'Mapper Desc',
          chartDataType: 'series',
          field: 'mapperDesc',
          tooltipField: 'mapperDesc',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Final Excluded',
          chartDataType: 'series',
          field: 'finalExcluded',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Reason',
          chartDataType: 'series',
          field: 'finalReason',
          tooltipField: 'finalReason',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'right'
          }
        },
        {
          id: 'textAlignopcode',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        }
      ]
    };
  }
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '$0.00';
    }
  };
  formatCellValueHrs = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    getNonQualifiedMapperOpcodes(result => {
      this.setState({ isLoading: false });
      if (
        result.data.dbdReferencesGetFixedOpsAndMapperNonQualifiedOpcodes.nodes
      ) {
        this.setState({
          rowData:
            result.data.dbdReferencesGetFixedOpsAndMapperNonQualifiedOpcodes
              .nodes
        });
      }
    });
  }
  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      fileName: 'Non Qualified Opcodes',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Non Qualified Opcodes' },
            mergeAcross: 3
          }
        ]
      ]
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  render() {
    return (
      <div>
        <div>
          <Paper square style={{ marginLeft: 8, marginRight: 8, height: 29 }}>
            <Toolbar>
              <Grid container justify="flex-start" style={{ padding: '5px' }}>
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      marginTop: -28,
                      marginLeft: 'auto',
                      cursor: 'pointer'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip>
              </Grid>
            </Toolbar>
          </Paper>
        </div>
        {this.state.isLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: `${window.innerHeight - 190}px`,
            width: '100%',
            margin: 8,
            display: this.state.tabSelection === 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

export default NonQualifiedMapperOpcodes;
