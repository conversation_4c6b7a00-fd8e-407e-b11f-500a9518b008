import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import {
  MenuItem,
  Select,
  Paper,
  FormControl,
  Divider,
  IconButton,
  Button
} from '@material-ui/core';
import moment from 'moment';
import {
  getDataForCWITotalcharts,
  getDataForKPIROSharechart,
  getDataForKPILineROLtSixtyK,
  getDataForKPILineROGtSixtyK,
  getDataForKPIAvgAgeMiles,
  getDataForKPIFlatRateHrs,
  getDataForKPILaborGpRo,
  getDataForKPIPartsGpRo,
  getDataForKPITotalGpRo,
  getDataForKPIWorkmix,
  getDataForKPILaborGrid,
  getDataForKPILineRO,
  getDataForKPIPartsGrid,
  getKpiToggleOptionsWithTimeZone,
  getKPIScorecardGoalAdvisor,
  getGridorMatrixPayTypeDetailsReport,
  getKpiComparativeReport,
  getDrillDownMonthYears
} from 'src/utils/hasuraServices';
import { useSelector, useDispatch } from 'react-redux';
import clsx from 'clsx';
import KpiSummary from './Kpi';
import { CircularProgress, Grid, Tooltip } from '@material-ui/core';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import { Alert } from '@material-ui/lab';
import PageHeader from 'src/components/PageHeader';
import { jsPDF } from 'jspdf';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { UPDATE_KPI_CURRENT_DATE } from 'src/graphql/queries';
import domtoimage from 'dom-to-image';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import { traceSpan } from 'src/utils/OTTTracing';
import { setKpiToggle } from 'src/actions';
import ArrowRightAltIcon from '@material-ui/icons/ArrowRightAlt';
import StarBorderIcon from '@material-ui/icons/StarBorder';
import { withKeycloak } from '@react-keycloak/web';
import { getTimeZone, getYearValue } from 'src/utils/Utils';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';

var lodash = require('lodash');

const styles = theme => ({
  root: {
    flexGrow: 1,
    width: '99%',
    marginLeft: 5
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  paper1: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.primary.main,
    margin: '12px 6px',
    maxWidth: '99%'
  },
  card: {
    width: '100%',
    height: '50vh',
    maxWidth: '100%',
    overflow: 'visible',
    display: 'flex',
    position: 'relative',
    // backgroundColor: '#c0d1df',
    backgroundColor: theme.palette.primary.light,
    border: 'solid #003d6b',
    marginTop: '-3px',
    '& > *': {
      flexGrow: 1,
      flexBasis: '50%',
      width: '50%'
    }
  },
  content: {
    padding: theme.spacing(8, 4, 3, 4)
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: -12
  },
  h1: {
    color: '#000',
    position: 'absolute',
    textAlign: 'center',
    left: '28%',
    fontWeight: 'bold',
    marginTop: '45px'
  },
  p: {
    color: '#fff'
  },
  headerDropdown: {
    marginTop: -11
  },
  divider: {
    backgroundColor: '#9b8c8ca1',
    fontWeight: 'bold'
  },
  container: {
    gridGap: 10
  },
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b',
    margin: window.innerHeight / 5 + 'px'
  },
  dataAsOfLabel: {
    marginLeft: 'auto',
    marginTop: 10,
    marginRight: 10
  },
  alertInfo: {
    fontSize: 16,
    fontWeight: '500',
    color: '#242f48',
    width: '100%',
    height: '45px !important'
  },
  linkItem: {
    cursor: 'pointer',
    marginTop: 3,
    marginLeft: 4
  },
  iconTurnUp1: {
    '@media (min-width: 2560px)': {
      display: 'none !important'
    },
    '@media (max-width: 2304px)': {
      display: 'none !important'
    },
    '@media (max-width: 1920px)': {
      display: 'none !important'
    },
    '@media (max-width: 1680px)': {
      display: 'block !important'
    },
    '@media (max-width: 1440px)': {
      display: 'block !important'
    }
  },
  iconTurnUp2: {
    '@media (min-width: 2560px)': {
      display: 'block !important'
    },
    '@media (max-width: 2304px)': {
      display: 'block !important'
    },
    '@media (max-width: 1920px)': {
      display: 'block !important'
    },
    '@media (max-width: 1680px)': {
      display: 'none !important'
    },
    '@media (max-width: 1440px)': {
      display: 'none !important'
    }
  }
});

function KpiReportData(props) {
  const session = useSelector(state => state.session);
  let pickerStatus =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.start_date
      ? true
      : false;
  let startDate =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.start_date
      ? props.history.location.state.start_date
      : props.history &&
        props.history.location &&
        props.history.location.start_date
      ? props.history.location.start_date
      : session.kpiToggleStartDate !== ''
      ? session.kpiToggleStartDate
      : '';
  let endDate =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.end_date
      ? props.history.location.state.end_date
      : props.history &&
        props.history.location &&
        props.history.location.end_date
      ? props.history.location.end_date
      : session.kpiToggleEndDate != ''
      ? session.kpiToggleEndDate
      : '';
  let kpiHomeToggle =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.toggle_duration
      ? props.history.location.state.toggle_duration
      : session.kpiHomeToggle != ''
      ? session.kpiHomeToggle
      : '';
  const { classes } = props;
  var timeZone = props.timezone ? props.timezone : '+05:30';
  const [CWITData, setCWITData] = useState([]);
  const [CWITAllData, setCWITAllDataAll] = useState([]);
  const [ROShareData, setROShareData] = useState([]);
  const [LineROLtSixtyK, setLineROLtSixtyK] = useState([]);
  const [LineROGtSixtyK, setLineROGtSixtyK] = useState([]);
  const [AvgAgeMiles, setAvgAgeMiles] = useState([]);
  const [FlatRateHrs, setFlatRateHrs] = useState([]);
  const [LaborGpRo, setLaborGpRo] = useState([]);
  const [PartsGpRo, setPartsGpRo] = useState([]);
  const [TotalGpRo, setTotalGpRo] = useState([]);
  const [WorkMix, setWorkMix] = useState([]);
  const [LaborGrid, setLaborGrid] = useState([]);
  const [PartsGrid, setPartsGrid] = useState([]);
  const [LineRO, setLineRO] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [isLoadingComplete, setLoadingComplete] = useState(false);
  const [advisorStoreGoal, setAdvisorStoreGoal] = useState([]);

  const [filterStart, setFilterStart] = useState(startDate);
  const [filterEnd, setFilterEnd] = useState(endDate);

  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  const [closedDate, setClosedDate] = useState(
    localStorage.getItem('closedDate')
  );
  const dispatch = useDispatch();
  let kpiDataToggles =
    localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';
  var toggle =
    props.history && props.history.location && props.history.location.toggle
      ? props.history.location.toggle
      : props.history &&
        props.history.location &&
        (props.history.location.state == undefined ||
          props.history.location.state == null)
      ? kpiDataToggles
      : session.kpiToggle;
  const [kpiData, setKpiData] = useState([]);
  const [CWIDataAdvisor, setCWIDataAllAdvisor] = useState([]);
  const [LineROLtSixtyKAdvisor, setLineROLtSixtyKAdvisor] = useState([]);
  const [ROShareDataAdvisor, setROShareDataAdvisor] = useState([]);
  const [LineROGtSixtyKAdvisor, setLineROGtSixtyKAdvisor] = useState([]);
  const [AvgAgeMilesAdvisor, setAvgAgeMilesAdvisor] = useState([]);
  const [FlatRateHrsAdvisor, setFlatRateHrsAdvisor] = useState([]);
  const [LaborGpRoAdvisor, setLaborGpRoAdvisor] = useState([]);
  const [PartsGpRoAdvisor, setPartsGpRoAdvisor] = useState([]);
  const [TotalGpRoAdvisor, setTotalGpRoAdvisor] = useState([]);
  const [WorkMixAdvisor, setWorkMixAdvisor] = useState([]);
  const [LaborGridAdvisor, setLaborGridAdvisor] = useState([]);
  const [PartsGridAdvisor, setPartsGridAdvisor] = useState([]);
  const [loadPdf, setLoadPdf] = useState(false);
  const [pickerChanged, setPickerChanged] = useState(pickerStatus);
  const [toggleChanged, setToggleChanged] = useState(false);
  const [filterChanged, setFilterChanged] = useState(false);
  const [advFilterChanged, setAdvFilterChanged] = useState(false);

  const [thisWeek, setThisWeek] = useState('');
  const [lastWeek, setLastWeek] = useState('');
  const [lastTwoWeek, setLastTwoWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');
  const [filterText, setFilterText] = useState(kpiHomeToggle);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [toggleOptions, setToggleOptions] = useState([]);
  const [selectedToggle, setSelectedToggle] = useState(toggle);
  const [toggleOption, setToggle] = useState(toggle);
  const [payTypeList, setPayTypeList] = useState([]);
  const [laborGridInternal, setLaborGridInternal] = useState([]);
  const [laborGridRV, setLaborGridRV] = useState([]);
  const [advisorLaborGridInternal, setAdvisorLaborGridInternal] = useState([]);
  const [advisorLaborGridRV, setAdvisorLaborGridRV] = useState([]);
  const [partsMatrixInternal, setPartsMatrixInternal] = useState([]);
  const [advisorPartsMatrixInternal, setAdvisorPartsMatrixInternal] = useState(
    []
  );
  const [kpiDataToggle, setKpiDataToggle] = useState(kpiDataToggles);
  let payType = 'C';
  if (props.keycloak.realm == 'billknightag') {
    payType = session.internalKpiToggle.substring(0, 1);
  }
  // let gridType = '';
  // if (session.internalKpiToggle == 'C' && payTypeList.length > 0) {
  //   gridType = payTypeList[0];
  // } else {
  //   gridType = session.internalKpiToggle;
  // }
  let laborGridTypes =
    session.stoveValue[0] == localStorage.getItem('selectedStoreId')
      ? JSON.parse(localStorage.getItem('laborGridTypes'))
      : session.allLaborGridTypes;
  let partsMatrixTypes =
    session.stoveValue[0] == localStorage.getItem('selectedStoreId')
      ? JSON.parse(localStorage.getItem('partsMatrixTypes'))
      : session.allPartsMatrixTypes;
  // let laborGridTypes = session.allLaborGridTypes;
  // let partsMatrixTypes = session.allPartsMatrixTypes;
  const exportKpiReportGrid = () => {
    const node = document.getElementById('kpi-body');
    const scale = 2;

    domtoimage
      .toJpeg(node, {
        quality: 0.95,
        height: node.offsetHeight * scale,
        width: node.offsetWidth * scale,
        style: {
          transform: `scale(${scale})`,
          transformOrigin: 'top left',
          width: `${node.offsetWidth}px`,
          height: `${node.offsetHeight}px`
        }
      })
      .then(dataUrl => {
        const pdf = new jsPDF({
          orientation: 'p', // Change to 'l' if landscape fits better
          unit: 'mm',
          format: 'a4'
        });

        const pageWidth = pdf.internal.pageSize.getWidth();
        const pageHeight = pdf.internal.pageSize.getHeight();

        const contentWidth = node.offsetWidth;
        const contentHeight = node.offsetHeight;
        const contentAspectRatio = contentWidth / contentHeight;
        const pageAspectRatio = pageWidth / pageHeight;

        let imgWidth, imgHeight;

        if (contentAspectRatio > pageAspectRatio) {
          // Fit width
          imgWidth = pageWidth;
          imgHeight = pageWidth / contentAspectRatio;
        } else {
          // Fit height
          imgHeight = pageHeight;
          imgWidth = pageHeight * contentAspectRatio;
        }

        const xOffset = (pageWidth - imgWidth) / 2; // Center horizontally
        const topMargin = 10; // Adjust this value as needed
        const yOffset = topMargin;

        pdf.addImage(dataUrl, 'JPEG', xOffset, yOffset, imgWidth, imgHeight);

        pdf.save('KPI_Single_Advisor.pdf');
      })
      .catch(error => {
        console.error('Error generating PDF:', error);
      });
  };

  const updateCurrentDate = () => {
    const start = new Date();
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_KPI_CURRENT_DATE
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/KpiReport',
          origin: '',
          event: 'Menu Load',
          is_from: 'UPDATE_KPI_CURRENT_DATE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
      });
  };
  const getToggleOptions = () => {
    var timeZone = props.timezone ? props.timezone : '+05:30';
    getKpiToggleOptionsWithTimeZone(timeZone, result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;

        setToggleOptions(dataArr);
        getPeriodSelected(toggleOption, dataArr);
      }
    });
  };

  useEffect(() => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setToggleOptions(dataArr);
        getPeriodSelected(selectedToggle, dataArr);
        setThisWeek(
          moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY")
        );
        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setLastTwoWeek(
          moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        if (filterStart == '' && filterEnd == '') {
          if (localStorage.getItem('kpiDataStatus') == 1) {
            setFilterStart(
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'));
          } else {
            setFilterStart(
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
          }
        }
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
        setIsPageLoading(true);
        setFilterChanged(true);
      }
    });
  }, []);

  useEffect(() => {
    if (filterStart != '' && filterEnd != '') {
      setFilterChanged(true);
    }
  }, [session.serviceAdvisor, session.technician]);

  const checkInternalToggle = () => {
    // getGridorMatrixPayTypeDetailsReport('paytype_grid', '', 'model', result => {
    //   setPayTypeList(result);
    // });
  };
  const getStoreAndAdvisorGoals = () => {
    let dataArr = [];
    getKPIScorecardGoalAdvisor(session.kpiAdvisor[0], timeZone, result => {
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
          .statelessDbdKpiScorecardKpiScorecardGoals
      ) {
        let dataSet =
          result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
            .statelessDbdKpiScorecardKpiScorecardGoals;
        dataSet.map(item => {
          if (item.goalname == 'Gross Profit % (Labor)') {
            dataArr[0] = item;
          } else if (item.goalname == 'Gross Profit % (Parts)') {
            dataArr[1] = item;
          } else if (item.goalname == 'Avg Hours Per Day') {
            dataArr[2] = item;
          } else if (item.goalname == 'CP Avg Hours Per RO') {
            dataArr[3] = item;
          } else if (item.goalname == '% of CP 1 Line ROs (Under 60K)') {
            dataArr[4] = item;
          } else if (item.goalname == '% of CP 1 Line ROs (Over 60K)') {
            dataArr[5] = item;
          } else if (item.goalname == 'CP Work Mix - Maintenance') {
            dataArr[6] = item;
          } else if (item.goalname == 'CP Work Mix - Repair') {
            dataArr[7] = item;
          } else if (
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Light Duty' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Standard' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Retail' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Hyundai' ||
            (item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp)' &&
              props.keycloak.realm != 'stiversag') ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Car-Lite Trk' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Honda Grid' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Car/Lt Trk' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Audi'
          ) {
            dataArr[8] = item;
          } else if (
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Heavy Duty' ||
            item.goalname ==
              'Int Repair Labor Price (Targets / Misses / % Non-Comp)' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Medium Duty' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Commercial' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Highline' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Diesel' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - HD & Hybrid' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Electric' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Volvo Grid' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Spec-Med Duty' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - 25-5500/Dsl' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Lamborghini' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Genesis' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - HD' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Medium/HD' ||
            (dataSet.find(
              e =>
                e.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet'
            ) &&
            dataSet.find(
              e =>
                e.goalname ==
                'Wty Repair Labor Price (Targets / Misses / % Non-Comp)'
            )
              ? item.goalname ==
                'Wty Repair Labor Price (Targets / Misses / % Non-Comp)'
              : item.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet')

            //  item.goalname == "Wty Repair Grid (Targets / Misses / % Non-Comp)"
          ) {
            dataArr[9] = item;
          } else if (
            item.goalname ==
            'CP Repair Parts Price (Targets / Misses / % Non-Comp)'
          ) {
            dataArr[10] = item;
          } else if (
            item.goalname ==
            'Int Repair Parts Price (Targets / Misses / % Non-Comp)'
          ) {
            dataArr[11] = item;
          } else if (
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Merc-Benz Grid' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Flt-Sptr' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - RV' ||
            item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp) - EV' ||
            (item.goalname ==
              'CP Repair Labor Price (Targets / Misses / % Non-Comp)' &&
              props.keycloak.realm == 'stiversag') ||
            //  item.goalname == "CP Repair Grid (Targets / Misses / % Non-Comp) - Fleet") {
            (dataSet.find(
              e =>
                e.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet'
            ) &&
            dataSet.find(
              e =>
                e.goalname ==
                'Wty Repair Labor Price (Targets / Misses / % Non-Comp)'
            )
              ? item.goalname ==
                'CP Repair Labor Price (Targets / Misses / % Non-Comp) - Fleet'
              : '')
          ) {
            dataArr[12] = item;
          }
        });
        //   setAdvisorStoreGoal(dataArr);
      }
    });
  };

  useEffect(() => {
    if (filterChanged) {
      //setToggle(session.kpiToggle);
      updateCurrentDate();
      // getStoreAndAdvisorGoals(); Removed because it is no longer in use.
      // getToggleOptions(); // defined separately in useEffect
      checkInternalToggle();
      // getStoreAndAdvisorGoals();
      let orderedData;
      if (chartList) {
        let filteredResult = chartList.filter(
          item => item.dbdName == 'KPI' && item.parentId == null
        );
        orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
      }

      let laborGridTypes =
        session.stoveValue[0] == localStorage.getItem('selectedStoreId')
          ? JSON.parse(localStorage.getItem('laborGridTypes'))
          : session.allLaborGridTypes
          ? session.allLaborGridTypes
          : 'C';
      let partsMatrixTypes =
        session.stoveValue[0] == localStorage.getItem('selectedStoreId')
          ? JSON.parse(localStorage.getItem('partsMatrixTypes'))
          : session.allPartsMatrixTypes
          ? session.allPartsMatrixTypes
          : 'C';
      let dataArr = [];
      setCWITData([]);
      setLineROLtSixtyK([]);
      setLineROGtSixtyK([]);
      setAvgAgeMiles([]);
      setFlatRateHrs([]);
      setLaborGpRo([]);
      setPartsGpRo([]);
      setTotalGpRo([]);
      setWorkMix([]);
      setLaborGrid([]);
      setLineRO([]);
      setPartsGrid([]);
      setROShareData([]);
      setCWITAllDataAll([]);
      setCWIDataAllAdvisor([]);
      setLineROLtSixtyKAdvisor([]);
      setROShareDataAdvisor([]);
      setLineROGtSixtyKAdvisor([]);
      setAvgAgeMilesAdvisor([]);
      setFlatRateHrsAdvisor([]);
      setLaborGpRoAdvisor([]);
      setPartsGpRoAdvisor([]);
      setTotalGpRoAdvisor([]);
      setWorkMixAdvisor([]);
      setLaborGridAdvisor([]);
      setPartsGridAdvisor([]);
      // let dataArr = [];
      setLoading(true);
      console.log('serviceAdvisor====', session.serviceAdvisor);
      dataArr.push('1706');
      getKpiComparativeReport(
        filterStart,
        filterEnd,
        1,
        2,
        session.serviceAdvisor,
        'KPI_SERV_COMP',
        getTimeZone(),

        null,
        'single_advisor',
        'C',
        'Customer',
        'N',
        session.kpiAdvisor.includes('All') ? null : session.kpiAdvisor,
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
              .kpiScorecardDetails
          ) {
            let data = JSON.parse(
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails[0].jsonData
            );
            setLoading(false);
            console.log('kpi data===A===', data);
            setFilterChanged(false);
            getReportsData(data);
          }
        }
      );
    }
  }, [
    // selectedToggle,
    session.kpiAdvisor,
    localStorage.getItem('selectedStoreId'),
    session.kpiToggle,
    filterChanged
  ]);

  const getReportsData = data => {
    let advisorIndex;
    let storeIndex;
    let filteredOutput = [];
    let filteredOutputAdvisor = [];
    let dataArr = [];
    if (data.length > 0) {
      console.log('DATA===', data);
      setKpiData(data);
      storeIndex = data[0].data.findIndex(
        item => item.adv_or_tech_id === 'Total Shop'
      );
      if (!session.kpiAdvisor.includes('All')) {
        advisorIndex = data[0].data.findIndex(
          item => item.adv_or_tech_id == session.kpiAdvisor[0]
        );
      }
      console.log(
        'kpi data index',
        data,
        storeIndex,
        session.serviceAdvisor,
        advisorIndex,
        session.kpiAdvisor
      );
      if (data[3].kpi_name == 'Labor Sales Per RO / GP $ Per RO') {
        dataArr[0] = {};
        filteredOutput = [
          {
            label: data[3].kpi_name.split(' / ')[0],
            val: data[3].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[3].kpi_name.split(' / ')[1],
            val: data[3].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: data[0].kpi_name.split(' / ')[2],
            val: Number(
              data[0].data[storeIndex].kpi_data.split('/')[2]
            ).toFixed(1)
          }
        ];
        dataArr[0].storegoalval = data[0].data[storeIndex].goal;
        dataArr[0].advisorgoalval = '';
        dataArr[0].storevariance = data[0].data[storeIndex].variance;
        dataArr[0].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[3].kpi_name.split(' / ')[0],
              val: data[3].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[3].kpi_name.split(' / ')[1],
              val: data[3].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: data[0].kpi_name.split(' / ')[2],
              val: Number(
                data[0].data[advisorIndex].kpi_data.split('/')[2]
              ).toFixed(1)
            }
          ];
          dataArr[0].advisorgoalval = data[0].data[advisorIndex].goal;
          dataArr[0].advisorvariance = data[0].data[advisorIndex].variance;
        }
        console.log('kpi data check>>>>', data, dataArr);

        setAdvisorStoreGoal(dataArr);

        setLaborGpRo(filteredOutput);
        setLaborGpRoAdvisor(filteredOutputAdvisor);
      }
      if (data[4].kpi_name == 'Parts Sales Per RO / GP $ Per RO') {
        dataArr[1] = {};
        filteredOutput = [
          {
            label: data[4].kpi_name.split(' / ')[0],
            val: data[4].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[4].kpi_name.split(' / ')[1],
            val: data[4].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: data[1].kpi_name.split(' / ')[2],
            val: Number(
              data[1].data[storeIndex].kpi_data.split('/')[2]
            ).toFixed(1)
          }
        ];
        dataArr[1].storegoalval = data[1].data[storeIndex].goal;
        dataArr[1].advisorgoalval = '';
        dataArr[1].storevariance = data[1].data[storeIndex].variance;
        dataArr[1].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[4].kpi_name.split(' / ')[0],
              val: data[4].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[4].kpi_name.split(' / ')[1],
              val: data[4].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: data[1].kpi_name.split(' / ')[2],
              val: Number(
                data[1].data[advisorIndex].kpi_data.split('/')[2]
              ).toFixed(1)
            }
          ];
          dataArr[1].advisorgoalval = data[1].data[advisorIndex].goal;
          dataArr[1].advisorvariance = data[1].data[advisorIndex].variance;
        }
        console.log('dataArr-0-------------------------------->', dataArr);
        setAdvisorStoreGoal(dataArr);
        setPartsGpRo(filteredOutput);
        setPartsGpRoAdvisor(filteredOutputAdvisor);
      }
      if (data[2].kpi_name == 'Labor & Parts Sales / Total GP $') {
        filteredOutput = [
          {
            label: data[2].kpi_name.split(' / ')[0],
            val: data[2].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[2].kpi_name.split(' / ')[1],
            val: data[2].data[storeIndex].kpi_data.split('/')[1]
          }
        ];

        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[2].kpi_name.split(' / ')[0],
              val: data[2].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[2].kpi_name.split(' / ')[1],
              val: data[2].data[advisorIndex].kpi_data.split('/')[1]
            }
          ];
        }
        console.log('dataArr-0-------------------------------->', dataArr);
        //  setAdvisorStoreGoal(dataArr);
        setTotalGpRo(filteredOutput);
        setTotalGpRoAdvisor(filteredOutputAdvisor);
      }

      if (
        data[18].kpi_name == 'Total Sold Hrs / Avg Per Day / CP Avg Hrs Per RO'
      ) {
        dataArr[2] = {};
        // dataArr[3] = {};
        filteredOutput = [
          {
            label: data[18].kpi_name.split(' / ')[0],
            val: data[18].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[18].kpi_name.split(' / ')[1],
            val: Number(
              parseInt(data[18].data[storeIndex].kpi_data.split('/')[1])
            )
          },
          {
            label: data[18].kpi_name.split(' / ')[2],
            val: data[18].data[storeIndex].kpi_data.split('/')[2]
          }
        ];
        dataArr[2].storegoalval = data[18].data[storeIndex].goal;
        dataArr[2].advisorgoalval = '';
        // dataArr[3].storegoalval = data[16].data[storeIndex].goal;
        // dataArr[3].advisorgoalval = '';
        dataArr[2].storevariance = data[18].data[storeIndex].variance;
        dataArr[2].advisorvariance = '';
        // dataArr[3].storevariance = data[16].data[storeIndex].variance;
        // dataArr[3].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[18].kpi_name.split(' / ')[0],
              val: data[18].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[18].kpi_name.split(' / ')[1],
              val: Number(
                parseInt(data[18].data[advisorIndex].kpi_data.split('/')[1])
              )
            },
            {
              label: data[18].kpi_name.split(' / ')[2],
              val: data[18].data[advisorIndex].kpi_data.split('/')[2]
            }
          ];
          dataArr[2].advisorgoalval = data[18].data[advisorIndex].goal;
          // dataArr[3].advisorgoalval = data[16].data[advisorIndex].goal;
          dataArr[2].advisorvariance = data[18].data[advisorIndex].variance;
          //dataArr[3].advisorvariance = data[16].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setFlatRateHrs(filteredOutput);
        setFlatRateHrsAdvisor(filteredOutputAdvisor);
      }
      if (data[20].kpi_name == '1 Line Count / % Under 60K') {
        dataArr[4] = {};
        filteredOutput = [
          {
            label: 'totalRos',
            val: data[19].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: 'onelineRos',
            val: data[20].data[storeIndex].kpi_data.split('/')[0]
          },
          // {
          //   label: 'multilineRos',
          //   val: 0
          // },
          {
            label: 'onelinePercentage',
            val: data[20].data[storeIndex].kpi_data.split('/')[1]
          },
          // {
          //   label: 'multilinePercentage',
          //   val: 0
          // },
          {
            label: 'onelineLbrsale',
            val: data[21].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: 'onelinePrtsale',
            val: data[21].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: 'onelineTotalsale',
            val: data[21].data[storeIndex].kpi_data.split('/')[2]
          },
          {
            label: 'multilineLbrsale',
            val: data[22].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: 'multilinePrtsale',
            val: data[22].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: 'multilineTotalsale',
            val: data[22].data[storeIndex].kpi_data.split('/')[2]
          },
          {
            label: 'multilineJobcount',
            val: Number(data[23].data[storeIndex].kpi_data).toFixed(1)
          }
        ];
        dataArr[4].storegoalval = data[20].data[storeIndex].goal;
        dataArr[4].advisorgoalval = '';
        dataArr[4].storevariance = data[20].data[storeIndex].variance;
        dataArr[4].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: 'totalRos',
              val: data[19].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: 'onelineRos',
              val: data[20].data[advisorIndex].kpi_data.split('/')[0]
            },
            // {
            //   label: 'multilineRos',
            //   val: 0
            // },
            {
              label: 'onelinePercentage',
              val: data[20].data[advisorIndex].kpi_data.split('/')[1]
            },
            // {
            //   label: 'multilinePercentage',
            //   val: 0
            // },4->3
            {
              label: 'onelineLbrsale',
              val: data[21].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: 'onelinePrtsale',
              val: data[21].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: 'onelineTotalsale',
              val: data[21].data[advisorIndex].kpi_data.split('/')[2]
            },
            {
              label: 'multilineLbrsale',
              val: data[22].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: 'multilinePrtsale',
              val: data[22].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: 'multilineTotalsale',
              val: data[22].data[advisorIndex].kpi_data.split('/')[2]
            },
            {
              label: 'multilineJobcount',
              val: Number(data[23].data[advisorIndex].kpi_data).toFixed(1)
            }
          ];
          dataArr[4].advisorgoalval = data[20].data[advisorIndex].goal;
          dataArr[4].advisorvariance = data[20].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setLineROLtSixtyK(filteredOutput);
        setLineROLtSixtyKAdvisor(filteredOutputAdvisor);
      }
      if (data[31].kpi_name == '1 Line Count / % Over 60K') {
        dataArr[5] = {};
        filteredOutput = [
          {
            label: 'totalRos',
            val: data[30].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: 'onelineRos',
            val: data[31].data[storeIndex].kpi_data.split('/')[0]
          },
          // {
          //   label: 'multilineRos',
          //   val: 0
          // },
          {
            label: 'onelinePercentage',
            val: data[31].data[storeIndex].kpi_data.split('/')[1]
          },
          // {
          //   label: 'multilinePercentage',
          //   val: 0
          // },
          {
            label: 'onelineLbrsale',
            val: data[32].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: 'onelinePrtsale',
            val: data[32].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: 'onelineTotalsale',
            val: data[32].data[storeIndex].kpi_data.split('/')[2]
          },
          {
            label: 'multilineLbrsale',
            val: data[33].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: 'multilinePrtsale',
            val: data[33].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: 'multilineTotalsale',
            val: data[33].data[storeIndex].kpi_data.split('/')[2]
          },
          {
            label: 'multilineJobcount',
            val: Number(data[34].data[storeIndex].kpi_data).toFixed(1)
          }
        ];
        dataArr[5].storegoalval = data[31].data[storeIndex].goal;
        dataArr[5].advisorgoalval = '';
        dataArr[5].storevariance = data[31].data[storeIndex].variance;
        dataArr[5].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: 'totalRos',
              val: data[30].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: 'onelineRos',
              val: data[31].data[advisorIndex].kpi_data.split('/')[0]
            },
            // {
            //   label: 'multilineRos',
            //   val: 0
            // },
            {
              label: 'onelinePercentage',
              val: data[31].data[advisorIndex].kpi_data.split('/')[1]
            },
            // {
            //   label: 'multilinePercentage',
            //   val: 0
            // },
            {
              label: 'onelineLbrsale',
              val: data[32].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: 'onelinePrtsale',
              val: data[32].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: 'onelineTotalsale',
              val: data[32].data[advisorIndex].kpi_data.split('/')[2]
            },
            {
              label: 'multilineLbrsale',
              val: data[33].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: 'multilinePrtsale',
              val: data[33].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: 'multilineTotalsale',
              val: data[33].data[advisorIndex].kpi_data.split('/')[2]
            },
            {
              label: 'multilineJobcount',
              val: Number(data[34].data[advisorIndex].kpi_data).toFixed(1)
            }
          ];
          dataArr[5].advisorgoalval = data[31].data[advisorIndex].goal;
          dataArr[5].advisorvariance = data[31].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setLineROGtSixtyK(filteredOutput);
        setLineROGtSixtyKAdvisor(filteredOutputAdvisor);
      }
      if (
        data[6].kpi_name ==
        'Repair Price Targets / Misses / % of Non-Compliance'
      ) {
        dataArr[8] = {};
        filteredOutput = [
          {
            label: data[6].kpi_name.split(' / ')[0],
            val: data[6].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[6].kpi_name.split(' / ')[1],
            val: data[6].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: data[6].kpi_name.split(' / ')[2],
            val: data[6].data[storeIndex].kpi_data.split('/')[2]
          }
        ];
        dataArr[8].storegoalval = data[6].data[storeIndex].goal;
        dataArr[8].advisorgoalval = '';
        dataArr[8].storevariance = data[6].data[storeIndex].variance;
        dataArr[8].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[6].kpi_name.split(' / ')[0],
              val: data[6].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[6].kpi_name.split(' / ')[1],
              val: data[6].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: data[6].kpi_name.split(' / ')[2],
              val: data[6].data[advisorIndex].kpi_data.split('/')[2]
            }
          ];
          dataArr[8].advisorgoalval = data[6].data[advisorIndex].goal;
          dataArr[8].advisorvariance = data[6].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setLaborGrid(filteredOutput);
        setLaborGridAdvisor(filteredOutputAdvisor);
      }
      if (
        data[7].kpi_name == 'Parts Price Targets / Misses / % of Non-Compliance'
      ) {
        dataArr[10] = {};
        filteredOutput = [
          {
            label: data[7].kpi_name.split(' / ')[0],
            val: data[7].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[7].kpi_name.split(' / ')[1],
            val: data[7].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: data[7].kpi_name.split(' / ')[2],
            val: data[7].data[storeIndex].kpi_data.split('/')[2]
          }
        ];
        dataArr[10].storegoalval = data[7].data[storeIndex].goal;
        dataArr[10].advisorgoalval = '';
        dataArr[10].storevariance = data[7].data[storeIndex].variance;
        dataArr[10].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[7].kpi_name.split(' / ')[0],
              val: data[7].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[7].kpi_name.split(' / ')[1],
              val: data[7].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: data[7].kpi_name.split(' / ')[2],
              val: data[7].data[advisorIndex].kpi_data.split('/')[2]
            }
          ];
          dataArr[10].advisorgoalval = data[7].data[advisorIndex].goal;
          dataArr[10].advisorvariance = data[7].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setPartsGrid(filteredOutput);
        setPartsGridAdvisor(filteredOutputAdvisor);
      }
      if (data[15].kpi_name == 'All ROs - Per Day Avg / % of Total Share') {
        dataArr[3] = {};
        filteredOutput = [
          {
            label: data[15].kpi_name.split(' / ')[0],
            val: data[15].data[storeIndex].kpi_data.split('/')[0]
          }
        ];
        dataArr[3].storegoalval = data[16].data[storeIndex].goal;
        dataArr[3].advisorgoalval = '';
        dataArr[3].storevariance = data[16].data[storeIndex].variance;
        dataArr[3].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[15].kpi_name.split(' / ')[0],
              val: data[15].data[advisorIndex].kpi_data.split('/')[0]
            }
          ];
          dataArr[3].advisorgoalval = data[16].data[advisorIndex].goal;
          dataArr[3].advisorvariance = data[16].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setROShareData(filteredOutput);
        setROShareDataAdvisor(filteredOutputAdvisor);
      }
      if (data[14].kpi_name == 'CP / Wty / Int / All Unique ROs') {
        dataArr[7] = {};
        filteredOutput = [
          {
            label: data[14].kpi_name.split(' / ')[0],
            val: data[14].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[14].kpi_name.split(' / ')[1],
            val: data[14].data[storeIndex].kpi_data.split('/')[1]
          },
          {
            label: data[14].kpi_name.split(' / ')[2],
            val: data[14].data[storeIndex].kpi_data.split('/')[2]
          },
          {
            label: data[14].kpi_name.split(' / ')[3],
            val: data[14].data[storeIndex].kpi_data.split('/')[3]
          }
        ];
        dataArr[7].storegoalval = data[14].data[storeIndex].goal;
        dataArr[7].advisorgoalval = '';
        dataArr[7].storevariance = data[14].data[storeIndex].variance;
        dataArr[7].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[14].kpi_name.split(' / ')[0],
              val: data[14].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[14].kpi_name.split(' / ')[1],
              val: data[14].data[advisorIndex].kpi_data.split('/')[1]
            },
            {
              label: data[14].kpi_name.split(' / ')[2],
              val: data[14].data[advisorIndex].kpi_data.split('/')[2]
            },
            {
              label: data[14].kpi_name.split(' / ')[3],
              val: data[14].data[advisorIndex].kpi_data.split('/')[3]
            }
          ];
          dataArr[7].advisorgoalval = data[14].data[advisorIndex].goal;
          dataArr[7].advisorvariance = data[14].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setCWITAllDataAll(filteredOutput);
        setCWIDataAllAdvisor(filteredOutputAdvisor);
      }
      if (data[13].kpi_name == 'Maintenance / Repair Work Mix') {
        dataArr[6] = {};
        filteredOutput = [
          {
            label: data[13].kpi_name.split(' / ')[0],
            val: data[13].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[13].kpi_name.split(' / ')[0],
            val: data[13].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[13].kpi_name.split(' / ')[1],
            val: data[13].data[storeIndex].kpi_data.split('/')[1]
          }
        ];
        dataArr[6].storegoalval = data[13].data[storeIndex].goal;
        dataArr[6].advisorgoalval = '';
        dataArr[6].storevariance = data[13].data[storeIndex].variance;
        dataArr[6].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[13].kpi_name.split(' / ')[0],
              val: data[13].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[13].kpi_name.split(' / ')[0],
              val: data[13].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[13].kpi_name.split(' / ')[1],
              val: data[13].data[advisorIndex].kpi_data.split('/')[1]
            }
          ];
          dataArr[6].advisorgoalval = data[13].data[advisorIndex].goal;
          dataArr[6].advisorvariance = data[13].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setWorkMix(filteredOutput);
        setWorkMixAdvisor(filteredOutputAdvisor);
      }
      if (data[17].kpi_name == 'CP & Wty - Avg Age / Miles Per Vehicle') {
        dataArr[11] = {};
        filteredOutput = [
          {
            label: data[17].kpi_name.split(' / ')[0],
            val: data[17].data[storeIndex].kpi_data.split('/')[0]
          },
          {
            label: data[17].kpi_name.split(' / ')[1],
            val: data[17].data[storeIndex].kpi_data.split('/')[1]
          }
        ];
        dataArr[11].storegoalval = data[17].data[storeIndex].goal;
        dataArr[11].advisorgoalval = '';
        dataArr[11].storevariance = data[17].data[storeIndex].variance;
        dataArr[11].advisorvariance = '';
        if (!session.kpiAdvisor.includes('All')) {
          filteredOutputAdvisor = [
            {
              label: data[17].kpi_name.split(' / ')[0],
              val: data[17].data[advisorIndex].kpi_data.split('/')[0]
            },
            {
              label: data[17].kpi_name.split(' / ')[1],
              val: data[17].data[advisorIndex].kpi_data.split('/')[1]
            }
          ];
          dataArr[11].advisorgoalval = data[17].data[advisorIndex].goal;
          dataArr[11].advisorvariance = data[17].data[advisorIndex].variance;
        }
        setAdvisorStoreGoal(dataArr);
        setAvgAgeMiles(filteredOutput);
        setAvgAgeMilesAdvisor(filteredOutputAdvisor);
      }
    }
  };

  // const getKpiStoreData = (chartId, toggleOption) => {
  //   chartId == 1335
  //     ? getDataForCWITotalcharts(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             setLoading(false);
  //             setLoadingComplete(true);
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var output = resultArr.map(s => ({ label: s[0], val: s[1] }));
  //             setCWITAllDataAll(output);
  //             delete resultArr[3];
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setCWITData(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1337
  //     ? getDataForKPILineROLtSixtyK(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setLineROLtSixtyK(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1338
  //     ? getDataForKPILineROGtSixtyK(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setLineROGtSixtyK(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1340
  //     ? getDataForKPIAvgAgeMiles(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setAvgAgeMiles(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1339
  //     ? getDataForKPIFlatRateHrs(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setFlatRateHrs(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1341
  //     ? getDataForKPILaborGpRo(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setLaborGpRo(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1342
  //     ? getDataForKPIPartsGpRo(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setPartsGpRo(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1343
  //     ? getDataForKPITotalGpRo(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setTotalGpRo(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1344
  //     ? getDataForKPIWorkmix(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setWorkMix(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1346
  //     ? getDataForKPILaborGrid(
  //         toggleOption,
  //         ['All'],
  //         timeZone,
  //         laborGridTypes[0] == 'Warranty' ? 'W' : payType,
  //         //laborGridTypes[0],
  //         null,
  //         null,
  //         callback => {
  //           setLoading(true);
  //           if (callback) {
  //             setLoading(false);
  //             setLoadingComplete(true);
  //             getGridAndMatrixTypeData(chartId, toggleOption, ['All']);
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setLaborGrid(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1351
  //     ? getDataForKPILineRO(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setLineRO(filteredOutput);
  //           }
  //         }
  //       )
  //     : chartId == 1353
  //     ? getDataForKPIPartsGrid(
  //         toggleOption,
  //         ['All'],
  //         timeZone,
  //         'C',
  //         // partsMatrixTypes.length > 0
  //         //   ? partsMatrixTypes[0].substring(0, 1)
  //         //   : null,
  //         null,
  //         callback => {
  //           if (callback) {
  //             getGridAndMatrixTypeData(chartId, toggleOption, ['All']);
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setPartsGrid(filteredOutput);
  //           }
  //         }
  //       )
  //     : getDataForKPIROSharechart(
  //         toggleOption,
  //         ['All'],
  //         ['All'],
  //         timeZone,
  //         callback => {
  //           if (callback) {
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             setROShareData(filteredOutput);
  //           }
  //         }
  //       );
  // };

  // const getGridAndMatrixTypeData = (chartId, toggleOption, advisor) => {
  //   if (props.keycloak.realm == 'billknightag') {
  //     payType = laborGridTypes[1].substring(0, 1);
  //   } else {
  //     payType = 'C';
  //   }
  //   let advisorSelected = advisor ? advisor : session.kpiAdvisor;
  //   if (chartId == 1346 && laborGridTypes.length > 1) {
  //     laborGridTypes.map(item => {
  //       getDataForKPILaborGrid(
  //         toggleOption,
  //         advisorSelected,
  //         timeZone,
  //         item == 'Warranty' ? 'W' : payType,
  //         //item,
  //         null,
  //         null,
  //         callback => {
  //           setLoading(true);
  //           if (callback) {
  //             setLoading(false);
  //             setLoadingComplete(true);
  //             let resultSet = callback;
  //             delete resultSet[0].__typename;
  //             var resultArr = Object.entries(resultSet[0]);
  //             var filteredOutput = resultArr.map(s => ({
  //               label: s[0],
  //               val: s[1]
  //             }));
  //             if (
  //               advisorSelected.includes('All') &&
  //               item == laborGridTypes[1]
  //             ) {
  //               setLaborGridInternal(filteredOutput);
  //             } else if (
  //               advisorSelected.includes('All') &&
  //               item == laborGridTypes[2]
  //             ) {
  //               setLaborGridRV(filteredOutput);
  //             } else if (
  //               !advisorSelected.includes('All') &&
  //               item == laborGridTypes[1]
  //             ) {
  //               setAdvisorLaborGridInternal(filteredOutput);
  //             } else if (
  //               !advisorSelected.includes('All') &&
  //               item == laborGridTypes[2]
  //             ) {
  //               setAdvisorLaborGridRV(filteredOutput);
  //             }
  //           }
  //         }
  //       );
  //     });
  //   } else if (chartId == 1353 && partsMatrixTypes.length > 1) {
  //     getDataForKPIPartsGrid(
  //       toggleOption,
  //       advisorSelected,
  //       timeZone,
  //       'C',
  //       // partsMatrixTypes.length > 0
  //       //   ? partsMatrixTypes[1].substring(0, 1)
  //       //   : null,
  //       null,
  //       callback => {
  //         if (callback) {
  //           let resultSet = callback;
  //           delete resultSet[0].__typename;
  //           var resultArr = Object.entries(resultSet[0]);
  //           var filteredOutput = resultArr.map(s => ({
  //             label: s[0],
  //             val: s[1]
  //           }));
  //           // setPartsGrid(filteredOutput);
  //           if (advisorSelected.includes('All')) {
  //             setPartsMatrixInternal(filteredOutput);
  //           } else {
  //             setAdvisorPartsMatrixInternal(filteredOutput);
  //           }
  //         }
  //       }
  //     );
  //   } else {
  //     return null;
  //   }
  // };
  const handleCallback = (event, picker) => {
    if (event.target.value) {
      // setToggle(event.target.value);
      dispatch(setKpiToggle(event.target.value));
    }
    setFilterStart(picker.startDate.format('YYYY-MM-DD'));
    setFilterEnd(picker.endDate.format('YYYY-MM-DD'));
    setLoading(true);
    setPickerChanged(true);
    setToggleChanged(true);
    setFilterChanged(true);
    getPeriodSelected(event.target.value, toggleOptions);
  };

  const getPeriodSelected = (toggle, toggleData) => {
    if (toggleData.length > 0) {
      const [startDate, endDate] = toggle
        .split(' - ')
        .map(date => moment(date, 'MM/DD/YY'));
      let yesterDay = moment(toggleData[0].yesterday).format("MMM DD 'YY");
      let lastWeekStart = moment(toggleData[0].lastweekstartdate);
      let lastWeekEnd = moment(toggleData[0].lastweekenddate);
      let mtdStart = moment(toggleData[0].mtdstartdate);
      let mtdEnd = moment(toggleData[0].mtdenddate);
      let lastMonthStart = moment(toggleData[0].lastmonthstartdate);
      let ytdStart = moment(toggleData[0].ytdstartdate);
      let ytdEnd = moment(toggleData[0].ytdenddate);
      let lastQtrStart = moment(toggleData[0].lastquarterstartdate);
      let lastQtrEnd = moment(toggleData[0].lastquarterenddate);
      let lastYearStart = moment(toggleData[0].lastyearstartdate);
      let lastYearEnd = moment(toggleData[0].lastyearenddate);
      let dayBeforeYesterday = moment(toggleData[0].dayBeforeYesterday).format(
        "MMM DD 'YY"
      );
      let lastThreeMonthsStart = moment(toggleData[0].lastthreemonthstartdate);
      let lastThreeMonthsEnd = moment(toggleData[0].lastthreemonthenddate);
      let lastTwelveMonthsStart = moment(
        toggleData[0].lasttwelvemonthstartdate
      );
      let lastTwelveMonthsEnd = moment(toggleData[0].lasttwelvemonthenddate);

      if (
        startDate.isSame(moment(toggleData[0].yesterday), 'day') &&
        endDate.isSame(moment(toggleData[0].yesterday), 'day')
      ) {
        setSelectedToggle('Yesterday ' + yesterDay);
      } else if (
        startDate.isSame(moment(toggleData[0].dayBeforeYesterday), 'day') &&
        endDate.isSame(moment(toggleData[0].dayBeforeYesterday), 'day')
      ) {
        setSelectedToggle('Day Before Yest. ' + dayBeforeYesterday);
      } else if (
        startDate.isSame(lastWeekStart, 'day') &&
        endDate.isSame(lastWeekEnd, 'day')
      ) {
        setSelectedToggle(
          'Last Week ' +
            lastWeekStart.format('MMM DD') +
            ' to ' +
            lastWeekEnd.format("MMM DD 'YY")
        );
      } else if (
        (startDate.isSame(mtdStart, 'day') && endDate.isSame(mtdEnd, 'day')) ||
        toggle == 'MTD'
      ) {
        setSelectedToggle(
          'This Month ' +
            mtdStart.format('MMM DD') +
            ' to ' +
            mtdEnd.format("MMM DD 'YY")
        );
      } else if (
        (startDate.isSame(lastMonthStart, 'day') &&
          endDate.isSame(lastMonthStart.endOf('month'), 'day')) ||
        toggle == 'LMONTH'
      ) {
        setSelectedToggle('Last Month ' + lastMonthStart.format('MMM'));
      } else if (
        startDate.isSame(ytdStart, 'day') &&
        endDate.isSame(ytdEnd, 'day')
      ) {
        setSelectedToggle(
          'YTD ' +
            ytdStart.format('MMM DD') +
            ' to ' +
            ytdEnd.format("MMM DD 'YY")
        );
      } else if (
        startDate.isSame(lastQtrStart, 'day') &&
        endDate.isSame(lastQtrEnd, 'day')
      ) {
        setSelectedToggle(
          'Last Qtr ' +
            lastQtrStart.format('MMM') +
            ' to ' +
            lastQtrEnd.format('MMM')
        );
      } else if (
        startDate.isSame(lastYearStart, 'day') &&
        endDate.isSame(lastYearEnd, 'day')
      ) {
        setSelectedToggle(
          'Last Year ' +
            lastYearStart.format('MMM') +
            ' to ' +
            lastYearEnd.format('MMM') +
            " ' " +
            getYearValue(toggleData[0].lastyearenddate)
        );
      } else if (
        startDate.isSame(lastThreeMonthsStart, 'day') &&
        endDate.isSame(lastThreeMonthsEnd, 'day')
      ) {
        setSelectedToggle(
          'Last 3 Mths ' +
            lastThreeMonthsStart.format('MMM') +
            ' to ' +
            lastThreeMonthsEnd.format('MMM')
        );
      } else if (
        startDate.isSame(lastTwelveMonthsStart, 'day') &&
        endDate.isSame(lastTwelveMonthsEnd, 'day')
      ) {
        setSelectedToggle(
          'Last 12 Mths ' +
            lastTwelveMonthsStart.format("MMM 'YY") +
            ' to ' +
            lastTwelveMonthsEnd.format("MMM 'YY")
        );
      } else {
        console.log('CUSTOM DATE.....123........');
        setSelectedToggle('Custom Range ' + toggle);
      }
    }
  };

  // let lastWeek = '';
  // let yesterDay = '';
  // let mtd = '';
  // let lastMonth = '';
  // let ytd = '';
  // let lastQtr = '';
  // let lastYear = '';
  // let dayBeforeYesterday = '';
  // let lastThreeMonths = '';
  // let lastTwelveMonths = '';
  // if (toggleOptions.length > 0) {
  //   yesterDay = moment(toggleOptions[0].yesterday).format("MMM DD ' YY");
  //   lastWeek =
  //     moment(toggleOptions[0].lastweekstartdate).format('MMM DD') +
  //     ' to ' +
  //     moment(toggleOptions[0].lastweekenddate).format("MMM DD ' YY");
  //   mtd =
  //     moment(toggleOptions[0].mtdstartdate).format('MMM DD') +
  //     ' to ' +
  //     moment(toggleOptions[0].mtdenddate).format("MMM DD ' YY");
  //   lastMonth = moment(toggleOptions[0].lastmonthstartdate).format('MMM');
  //   ytd =
  //     moment(toggleOptions[0].ytdstartdate).format('MMM DD') +
  //     ' to ' +
  //     moment(toggleOptions[0].ytdenddate).format("MMM DD ' YY");
  //   lastQtr =
  //     moment(toggleOptions[0].lastquarterstartdate).format('MMM') +
  //     ' to ' +
  //     // getNextMonth(toggleOptions[0].lastquarterstartdate) + ' - ' +
  //     moment(toggleOptions[0].lastquarterenddate).format('MMM');
  //   lastYear =
  //     moment(toggleOptions[0].lastyearstartdate).format('MMM') +
  //     ' to ' +
  //     moment(toggleOptions[0].lastyearenddate).format('MMM') +
  //     " ' " +
  //     getYearValue(toggleOptions[0].lastyearenddate);
  //   dayBeforeYesterday = moment(toggleOptions[0].dayBeforeYesterday).format(
  //     "MMM DD ' YY"
  //   );
  //   lastThreeMonths =
  //     moment(toggleOptions[0].lastthreemonthstartdate).format('MMM') +
  //     ' to ' +
  //     //getNextMonth(toggleOptions[0].lastthreemonthstartdate) + ' - ' +
  //     moment(toggleOptions[0].lastthreemonthenddate).format('MMM');
  //   lastTwelveMonths =
  //     moment(toggleOptions[0].lasttwelvemonthstartdate).format("MMM ' YY") +
  //     ' to ' +
  //     moment(toggleOptions[0].lasttwelvemonthenddate).format("MMM ' YY");
  // }

  const exportKpiReport = () => {
    exportKpiReportGrid();
    // console.log('report', exportReport);
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to PDF',
      title: props.title ? props.title : '',
      from: props.isFrom ? props.isFrom : ''
    };
    traceSpan('Export to PDF', spanAttribute);
  };
  const handleclick = () => {
    const formattedFilterStart = moment(filterStart).format('MM/DD/YY');
    const formattedFilterEnd = moment(filterEnd).format('MM/DD/YY');
    props.history.push({
      pathname: '/ScoreCardGoalSetting',
      state: {
        parent: 'KpiReport',
        toggle: `${formattedFilterStart} - ${formattedFilterEnd}`,
        end_date: filterEnd,
        start_date: filterStart,
        advisor: session.kpiAdvisor
      }
    });
  };
  console.log('kpiData=d=d=d===', kpiData);
  return (
    <React.Fragment>
      {session.kpiAdvisor.length == 0 ? (
        <React.Fragment>
          <Paper square style={{ height: window.innerHeight - 110 + 'px' }}>
            {/* <Alert variant="outlined" severity="info" color="error" className={classes.alertInfo} >
                Please choose an advisor to see a report !
              </Alert> */}
            <span style={{ margin: 10 }} className={'kpiReportAlert'}>
              <Alert severity="info" className={classes.alertInfo}>
                Please choose an advisor to see a report
              </Alert>
            </span>
            {/* <span style={{margin: 10}} className={"kpiReportAlert"}>
              <Alert severity="info" className={classes.alertInfo}>
                Please choose an advisor to see a report
                <IconButton
                            size="large">
                <ArrowRightAltIcon />
                </IconButton>
              </Alert>
              
            </span> */}
            <Grid justify="center" className={classes.loaderGrid}>
              No Reports Found
            </Grid>
          </Paper>
        </React.Fragment>
      ) : (
        <React.Fragment>
          {/* <Kpi history={history} /> */}
          <div className={classes.root}>
            {session.kpiAdvisor[0] == 'All' ? (
              <React.Fragment>
                <span className="KpiReportAlert">
                  <Alert
                    variant="outlined"
                    severity="info"
                    className={classes.alertInfo}
                  >
                    {/* <IconButton size="medium" style={{marginTop: -3}}>
                    <StarBorderIcon />
                  </IconButton>{' '} */}
                    {/* <span className="iconTargetReportIcon">
                    <img
                      class="iconTargetReport"
                      alt="Fixed Ops"
                      src={`/images/kpis/astrick.png`}
                    />
                  </span> */}
                    <span>
                      <h4>
                        Please choose an advisor from the drop down above.
                      </h4>
                    </span>
                    {/* <span className="iconTurnUp">
                    <img
                      className={classes.iconTurnUp1}
                      alt="Fixed Ops"
                      src={`/images/kpis/turn-up.png`}
                    />
                    <img
                      className={classes.iconTurnUp2}
                      alt="Fixed Ops"
                      src={`/images/kpis/turn-up(1).png`}
                    />
                  </span> */}
                  </Alert>
                </span>
                <div style={{ margin: 4 }}></div>
              </React.Fragment>
            ) : null}
            {isPageLoading && (
              <Paper className={classes.paper}>
                <div className={clsx(classes.headerItem, 'main-title-kpi')}>
                  <PageHeader
                    // title={'KPI Report #1 - Individual Advisor'}
                    title={'KPI Single Advisor'}
                    // title={session.kpiAdvisor[0] != 'All' ? 'KPI Report 1 - Individual Advisor' : 'KPI Report 1 - All Advisors'}
                    hideTitle={false}
                    isFrom={'details'}
                    // exportPDF={true}
                    // exportKpiReportGrid={exportKpiReportGrid}
                    //setResetDashboard={this.setResetDashboard}
                  />
                  {props.keycloak.realmAccess.roles.includes('client') !=
                  true ? (
                    <div>
                      <Button
                        variant="contained"
                        className={'editGoals-Btn'}
                        onClick={handleclick}
                      >
                        <Typography variant="body1" align="left">
                          Edit Goals
                        </Typography>
                      </Button>
                    </div>
                  ) : null}
                  <div className={classes.headerDropdown}>
                    <FormControl
                      variant="outlined"
                      margin="dense"
                      className={clsx(classes.formControl, 'input-container')}
                    >
                      <DateRangePicker
                        initialSettings={{
                          locale: {
                            format: 'MM/DD/YY',
                            separator: ' - '
                          },

                          ranges: {
                            ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            yesterDay]: [
                              moment(
                                toggleOptions[0] && toggleOptions[0].yesterday
                              ).toDate(),
                              moment(
                                toggleOptions[0] && toggleOptions[0].yesterday
                              ).toDate()
                            ],
                            ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            dayBfYest]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].dayBeforeYesterday
                              ).toDate(),
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].dayBeforeYesterday
                              ).toDate()
                            ],
                            ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            thisWeek]: [
                              moment(
                                toggleOptions[0].thisweekstartdate
                              ).toDate(),
                              moment(toggleOptions[0].thisweekenddate).toDate()
                            ],
                            ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            lastWeek]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastweekstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastweekenddate
                              ).toDate()
                            ],
                            ['Last 2 weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            lastTwoWeek]: [
                              moment(
                                toggleOptions[0].lasttwoweekstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0].lasttwoweekenddate
                              ).toDate()
                            ],

                            ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            mtd]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].mtdstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] && toggleOptions[0].mtdenddate
                              ).toDate()
                            ],
                            ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            lastMonth]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastmonthstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastmonthenddate
                              ).toDate()
                            ],
                            ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            lastThreeMonths]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastthreemonthstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastthreemonthenddate
                              ).toDate()
                            ],
                            ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            lastQtr]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastquarterstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastquarterenddate
                              ).toDate()
                            ],
                            ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            ytd]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].ytdstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] && toggleOptions[0].ytdenddate
                              ).toDate()
                            ],
                            ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            lastTwelveMonths]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lasttwelvemonthstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lasttwelvemonthenddate
                              ).toDate()
                            ],
                            ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                            lastYear]: [
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastyearstartdate
                              ).toDate(),
                              moment(
                                toggleOptions[0] &&
                                  toggleOptions[0].lastyearenddate
                              ).toDate()
                            ]
                          },

                          maxDate: moment(
                            toggleOptions[0] && toggleOptions[0].today
                          ).toDate(),
                          alwaysShowCalendars: false,
                          applyClass: clsx(classes.calButton, 'apply-btn'),
                          cancelClass: clsx(classes.calButton, 'apply-btn'),

                          startDate:
                            filterStart && filterStart != ''
                              ? moment(filterStart).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  toggleOptions[0] &&
                                    toggleOptions[0].mtdstartdate
                                ).toDate()
                              : moment(
                                  toggleOptions[0] &&
                                    toggleOptions[0].lastmonthstartdate
                                ).toDate(),
                          endDate:
                            filterEnd && filterEnd != ''
                              ? moment(filterEnd).toDate()
                              : localStorage.getItem('kpiDataStatus') == 1
                              ? moment(
                                  toggleOptions[0] &&
                                    toggleOptions[0].mtdenddate
                                ).toDate()
                              : moment(
                                  toggleOptions[0] &&
                                    toggleOptions[0].lastmonthenddate
                                ).toDate()
                        }}
                        onApply={handleCallback}
                      >
                        <input
                          type="text"
                          className="datepicker"
                          id="picker"
                          name="picker"
                          aria-labelledby="label-picker"
                        />
                      </DateRangePicker>
                      <label class="labelpicker" for="picker" id="label-picker">
                        <div class="textpicker">Select Date</div>
                      </label>
                    </FormControl>
                  </div>
                  <Tooltip title="Export To PDF">
                    <Link
                      className={classes.linkItem}
                      onClick={exportKpiReport}
                    >
                      <ExportIcon />
                    </Link>
                  </Tooltip>
                </div>
                <Divider />
                {isLoading !== false ? (
                  <Grid
                    justify="center"
                    style={{
                      height: 300,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <CircularProgress size={60} />
                  </Grid>
                ) : (
                  <KpiSummary
                    toggleOption={toggleOption}
                    CWITData={CWITData}
                    allCWITData={CWITAllData}
                    ROShareData={ROShareData}
                    LineROLtSixtyK={LineROLtSixtyK}
                    LineROGtSixtyK={LineROGtSixtyK}
                    FlatRateHrs={FlatRateHrs}
                    AvgAgeMiles={AvgAgeMiles}
                    LaborGpRo={LaborGpRo}
                    PartsGpRo={PartsGpRo}
                    TotalGpRo={TotalGpRo}
                    WorkMix={WorkMix}
                    LaborGrid={LaborGrid}
                    PartsGrid={PartsGrid}
                    CWIDataAdvisor={CWIDataAdvisor}
                    ROShareDataAdvisor={ROShareDataAdvisor}
                    LineROLtSixtyKAdvisor={LineROLtSixtyKAdvisor}
                    LineROGtSixtyKAdvisor={LineROGtSixtyKAdvisor}
                    FlatRateHrsAdvisor={FlatRateHrsAdvisor}
                    AvgAgeMilesAdvisor={AvgAgeMilesAdvisor}
                    LaborGpRoAdvisor={LaborGpRoAdvisor}
                    PartsGpRoAdvisor={PartsGpRoAdvisor}
                    TotalGpRoAdvisor={TotalGpRoAdvisor}
                    WorkMixAdvisor={WorkMixAdvisor}
                    LaborGridAdvisor={LaborGridAdvisor}
                    PartsGridAdvisor={PartsGridAdvisor}
                    toggleOptions={toggleOptions}
                    history={props.history}
                    selectedToggle={selectedToggle}
                    advisorStoreGoal={advisorStoreGoal}
                    filterStart={filterStart}
                    filterEnd={filterEnd}
                    // advisorStoreGoal={[]}
                    laborGridTypes={laborGridTypes}
                    partsMatrixTypes={partsMatrixTypes}
                    laborGridInternal={laborGridInternal}
                    laborGridRV={laborGridRV}
                    advisorLaborGridInternal={advisorLaborGridInternal}
                    advisorLaborGridRV={advisorLaborGridRV}
                    partsMatrixInternal={partsMatrixInternal}
                    advisorPartsMatrixInternal={advisorPartsMatrixInternal}
                    matrixTypes={props.matrixTypes}
                    realm={props.keycloak.realm}
                    kpiData={kpiData}
                    //  handleToggleChange={handleToggleChange}
                  />
                )}
              </Paper>
            )}
          </div>
        </React.Fragment>
      )}
    </React.Fragment>
  );
}

KpiReportData.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withKeycloak(withStyles(styles)(KpiReportData));
