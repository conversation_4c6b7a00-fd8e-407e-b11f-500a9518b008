import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { getTimeZone } from '../../utils/Utils';
import { getGridorMatrixPayTypeDetailsReport } from 'src/utils/hasuraServices';
import Kpi from './KpiReportData';
import { setLaborGridTypes, setPartsMatrixTypes } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { CircularProgress, Grid } from '@material-ui/core';
import { Redirect } from 'react-router-dom';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));

function KpiReport() {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const history = useHistory();
  var timezone = getTimeZone();
  const [selectedPayType, setSelectedPayType] = useState('');
  const [matrixTypes, setMatrixType] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingParts, setLoadingParts] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    // getPartsPayTypeList();
    let data = [];
    data.push('Customer');
    setLoading(true);
    // getGridorMatrixPayTypeDetailsReport('paytype_grid', '', 'model', result => {
    //   if (result.length > 0) {
    //     if (result.includes('Internal')) {
    //       result.sort();
    //     }
    //     if (
    //       result.includes('Heavy Duty') ||
    //       result.includes('Fleet') ||
    //       result.includes('Commercial')
    //     ) {
    //       if (
    //         result[0] == 'Heavy Duty' ||
    //         result[0] == 'Fleet' ||
    //         (result[0] == 'Commercial' && !result.includes('RV'))
    //       ) {
    //         result.reverse();
    //       }
    //     }
    //     if (
    //       result.includes('Commercial') &&
    //       result.includes('RV') &&
    //       result.includes('Retail')
    //     ) {
    //       result = ['Retail', 'Commercial', 'RV'];
    //     }
    //     if (
    //       result.includes('Warranty') &&
    //       result.includes('Standard') &&
    //       result.includes('Fleet')
    //     ) {
    //       result = ['Standard', 'Warranty', 'Fleet'];
    //     }
    //     if (result.includes('Highline')) {
    //       result.reverse();
    //     }
    //     if (
    //       result.includes('Diesel') ||
    //       result.includes('HD & Hybrid') ||
    //       result.includes('Electric')
    //     ) {
    //       result.reverse();
    //     }
    //     if (
    //       result.includes('Honda Grid') &&
    //       result.includes('Volvo Grid') &&
    //       result.includes('Merc-Benz Grid')
    //     ) {
    //       result = ['Honda Grid', 'Volvo Grid', 'Merc-Benz Grid'];
    //     }
    //     if (
    //       result.includes('25-5500/Dsl') &&
    //       result.includes('Car/Lt Trk') &&
    //       result.includes('Flt-Sptr')
    //     ) {
    //       result = ['Car/Lt Trk', '25-5500/Dsl', 'Flt-Sptr'];
    //     }
    //     if (
    //       result.includes('Hyundai') &&
    //       result.includes('Genesis') &&
    //       result.includes('Customer')
    //     ) {
    //       result = ['Hyundai', 'Genesis', 'Customer'];
    //     }

    //     if (
    //       result.includes('EV') &&
    //       result.includes('Medium/HD') &&
    //       result.includes('Standard')
    //     ) {
    //       result = ['Standard', 'Medium/HD', 'EV'];
    //     }
    //     localStorage.setItem(
    //       'laborGridTypes',
    //       JSON.stringify(lodash.uniq(result))
    //     );
    //     dispatch(setLaborGridTypes(lodash.uniq(result)));
    //     setLoading(true);
    //   } else {
    //     localStorage.setItem(
    //       'laborGridTypes',
    //       JSON.stringify(lodash.uniq(data))
    //     );
    //     dispatch(setLaborGridTypes(lodash.uniq(result)));
    //     setLoading(true);
    //   }
    // });
  }, [session.storeSelected]);
  function getPartsPayTypeList() {
    let data = [];
    data.push('Customer');
    setLoadingParts(false);
    getGridorMatrixPayTypeDetailsReport(
      'paytype_matrix',
      '',
      'source',
      result => {
        if (result.length > 0) {
          setMatrixType(result);
          if (result.includes('Internal')) {
            result.sort();
          }
          if (
            result.includes('Heavy Duty') ||
            result.includes('Fleet') ||
            result.includes('Commercial')
          ) {
            if (
              result[0] == 'Heavy Duty' ||
              result[0] == 'Fleet' ||
              (result[0] == 'Commercial' && !result.includes('RV'))
            ) {
              result.reverse();
            }
          }
          if (
            result.includes('Commercial') &&
            result.includes('RV') &&
            result.includes('Retail')
          ) {
            result = ['Retail', 'Commercial', 'RV'];
          }
          if (
            result.includes('Warranty') &&
            result.includes('Standard') &&
            result.includes('Fleet')
          ) {
            result = ['Standard', 'Warranty', 'Fleet'];
          }
          if (result.includes('Highline')) {
            result.reverse();
          }
          localStorage.setItem(
            'partsMatrixTypes',
            JSON.stringify(lodash.uniq(result))
          );
          dispatch(setPartsMatrixTypes(lodash.uniq(result)));
          setLoadingParts(true);
        } else {
          setMatrixType(result);
          localStorage.setItem(
            'partsMatrixTypes',
            JSON.stringify(lodash.uniq(data))
          );
          dispatch(setPartsMatrixTypes(lodash.uniq(result)));
          setLoadingParts(true);
        }
      }
    );
  }
  return (
    <Page className={classes.root} title={'KPI Advisor'}>
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Kpi history={history} timezone={timezone} matrixTypes={matrixTypes} />
      )}
    </Page>
  );
}

export default KpiReport;
