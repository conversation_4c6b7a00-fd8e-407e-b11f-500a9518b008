import React, { useEffect, useState } from 'react';
import { jsPDF } from 'jspdf';
import addHTML from 'html2canvas';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import 'src/styles.css';
import html2canvas from 'html2canvas';
import domtoimage from 'dom-to-image';
import { getAdvisorName } from 'src/utils/hasuraServices';
import {
  Paper,
  Divider,
  FormControl,
  Select,
  MenuItem
} from '@material-ui/core';
import { useDispatch, useSelector } from 'react-redux';
import moment from 'moment';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1,
    width: '99%'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  kpiHeading: {
    color: '#fff',
    fontSize: 13,
    fontWeight: 'bold',
    border: 'none !important',
    margin: 0,
    lineHeight: '22px',
    height: 35
  },
  kpiHeadingPdf: {
    color: '#fff',
    fontSize: 9,
    fontWeight: 'bold',
    border: 'none !important',
    margin: 0,
    lineHeight: '14px'
  },
  mainContainerGrid: {
    '@media (max-width: 1920px)': {
      width: '100%'
    },

    '@media (max-width: 1440px)': {
      width: '100%'
    },

    '@media (max-width: 1280px)': {
      width: '100%'
    },
    '@media (min-width: 2304px)': {
      width: '80%'
    }
  },
  varianceGreater: {
    backgroundColor: '#ffff00',
    display: 'block',
    marginLeft: 0
  },
  varianceLesser: {
    backgroundColor: '#ff0000',
    display: 'block',
    marginLeft: 0
  },
  varianceHigher: {
    backgroundColor: '#92d050',
    display: 'block',
    marginLeft: 0
  },
  varianceZero: {
    backgroundColor: '#fff',
    display: 'block',
    marginLeft: 0
  },
  varianceNull: {
    backgroundColor: '#d3d3d3',
    display: 'block',
    marginLeft: 0
  }
}));
function Kpi(props) {
  const classes = useStyles();
  const [loadPdf, setLoadPdf] = useState(false);
  const [advisorName, setAdvisorName] = useState([]);
  const session = useSelector(state => state.session);
  // const [toggleOption, setToggleOption] = useState('MTD');
  const formatSummaryValues = (value, status) => {
    return value == null || value == ''
      ? 0
      : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const formatAdvisorStoreValues = (value, type) => {
    if (type == '%') {
      return value == '' || value == 0 ? '' : value + '%';
    } else {
      return value == '' || value == 0
        ? ''
        : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
  };

  const formatVariance = value => {
    if (value != '' && value != 0) {
      return value > 0
        ? '+' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return 0.0;
    }
  };
  const dataLoaderKpi = () => {
    return (
      <div class="stage" style={{ marginRight: '20%' }}>
        <div class="dot-pulse-kpi"></div>
      </div>
    );
  };
  const stripHtmlTags = str => str.replace(/<[^>]*>/g, '');

  const escapeHtml = unsafe =>
    unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  const exportKpiReportGrid = () => {
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Export to Excel',
      title: 'Home',
      from: 'KPI'
    };
    setLoadPdf(true);

    var node = document.getElementById('kpi-body');
    var options = {
      quality: 1.0,
      height: node.offsetHeight * scale + 60,
      style: {
        transform: `scale(${scale}) translate(${node.offsetWidth /
          2 /
          scale}px, ${node.offsetHeight / 2 / scale}px)`
      },
      width: node.offsetWidth * scale - 70
    };
    const scale = 2;
    domtoimage
      .toPng(node, {
        quality: 0.95,
        height: node.offsetHeight * scale,
        width: node.offsetWidth * scale,
        style: {
          transform: 'scale(' + scale + ')',
          transformOrigin: 'top left',
          width: node.offsetWidth + 'px',
          height: node.offsetHeight + 'px'
        }
      })
      .then(dataUrl => {
        var doc = new jsPDF();

        doc.addImage(dataUrl, 'PNG', 5, 10, 220, 200);
        doc.save('KpiReport.pdf');
      })
      .catch(error => {});
  };

  useEffect(() => {
    getAdvisorName(session.kpiAdvisor[0], result => {
      if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
        setAdvisorName(
          (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes[0]
            .nickname
            ? result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes[0]
                .nickname
            : result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes[0]
                .name) +
            ' [' +
            result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes[0]
              .serviceadvisor +
            ']'
        );
      }
    });
  }, [session.kpiAdvisor, session.serviceAdvisor]);
  let gridText = 'CP';
  if (session.internalKpiToggle == 'Internal') {
    gridText = 'Int';
  } else if (
    session.internalKpiToggle == 'C' ||
    session.internalKpiToggle == 'Customer'
  ) {
    gridText = 'CP';
  } else {
    gridText = session.internalKpiToggle;
  }
  const DotValue = (kpi_data, kpi_slno) => {
    const parts = kpi_data.split('/');
    if (parts.length == 1) {
      if (kpi_slno == 25 || kpi_slno == 30) {
        const formattedData = `${Number(parts[0]).toLocaleString()} `;
        return formattedData;
      } else {
        const formattedData = `$${Number(parts[0]).toLocaleString()} `;
        return formattedData;
      }
    }
    if (parts.length == 2) {
      if (kpi_slno == 6) {
        const formattedData = `$${Number(
          parts[0]
        ).toLocaleString()} to $${Number(parts[1]).toLocaleString()} `;
        return formattedData;
      } else if (kpi_slno == 19) {
        const formattedData = `${Number(parts[0]).toLocaleString()}% ● ${Number(
          parts[1]
        ).toLocaleString()}% `;
        return formattedData;
      } else if (
        kpi_slno == 8 ||
        kpi_slno == 21 ||
        kpi_slno == 22 ||
        kpi_slno == 26 ||
        kpi_slno == 27
      ) {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● ${Number(
          parts[1]
        ).toLocaleString()}% `;
        return formattedData;
      } else if (kpi_slno == 9) {
        const formattedData = `${Number(parts[0]).toLocaleString()} `;
        return formattedData;
      } else if (kpi_slno == 10) {
        const formattedData = `${Number(
          parts[0]
        ).toLocaleString()} Years ● ${Number(parts[1]).toLocaleString()} Miles`;
        return formattedData;
      } else {
        const formattedData = `$${Number(
          parts[0]
        ).toLocaleString()} ● $${Number(parts[1]).toLocaleString()} `;
        return formattedData;
      }
    }
    if (parts.length == 3) {
      if (
        kpi_slno == 14 ||
        kpi_slno == 15 ||
        kpi_slno == 16 ||
        kpi_slno == 17
      ) {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● $${Number(
          parts[1]
        ).toLocaleString()} ● $${parts[2]}`;
        return formattedData;
      } else if (
        kpi_slno == 23 ||
        kpi_slno == 24 ||
        kpi_slno == 28 ||
        kpi_slno == 29
      ) {
        const formattedData = `$${Number(
          parts[0]
        ).toLocaleString()} ● $${Number(parts[1]).toLocaleString()} ● $${
          parts[2]
        }`;
        return formattedData;
      } else if (kpi_slno == 11) {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● ${Number(
          parts[1]
        ).toLocaleString()} ● ${parts[2]}`;
        return formattedData;
      } else if (kpi_slno == 31 || kpi_slno == 34) {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● ${Number(
          parts[1]
        ).toLocaleString()} ● ${parts[2]}%`;
        return formattedData;
      } else {
        const formattedData = `$${Number(
          parts[0]
        ).toLocaleString()} ● $${Number(parts[1]).toLocaleString()} ● ${
          parts[2]
        }%`;
        return formattedData;
      }
    }
    if (parts.length == 4) {
      if (kpi_slno == 7) {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● ${Number(
          parts[1]
        ).toLocaleString()} ● ${parts[2]} ● ${parts[3]}`;
        return formattedData;
      } else if (kpi_slno == 32) {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● ${Number(
          parts[1]
        ).toLocaleString()} ● ${parts[2]} ● ${parts[3]}`;
        return formattedData;
      } else if (kpi_slno == 33 || kpi_slno == 37) {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● ${Number(
          parts[1]
        ).toLocaleString()} ● ${parts[2]}% ● ${parts[3]}`;
        return formattedData;
      } else {
        const formattedData = `${Number(parts[0]).toLocaleString()} ● ${Number(
          parts[1]
        ).toLocaleString()} ● ${parts[2]}%`;
        return formattedData;
      }
    }
  };
  const VarianceValue = (variance, kpi_slno) => {
    if (kpi_slno == 1 || kpi_slno == 2 || kpi_slno == 22 || kpi_slno == 27) {
      const formattedData = `${Number(variance).toLocaleString()}%`;
      return formattedData;
    } else if (kpi_slno == 14 || kpi_slno == 16) {
      return variance;
    } else {
      return variance;
    }
  };
  const KpiData = ({ kpiData }) => {
    // Group data by kpi_type_code
    const groupedData = kpiData.reduce((acc, kpi) => {
      if (!acc[kpi.kpi_type_code]) {
        acc[kpi.kpi_type_code] = [];
      }
      acc[kpi.kpi_type_code].push(kpi);
      return acc;
    }, {});

    return (
      <table>
        {Object.entries(groupedData).map(([kpiTypeCode, kpis]) => (
          <React.Fragment key={kpiTypeCode}>
            {/* Group Header */}
            <tr
              align="left"
              className="custom-rows"
              style={{
                backgroundColor: '#ccc',
                color: '#000',
                fontSize: '14px',
                fontWeight: 'bold',
                lineHeight: '20px',
                textAlign: 'left'
              }}
            >
              <td colSpan="100%">Group: {kpiTypeCode}</td>
            </tr>

            {/* Grouped KPI Rows */}
            {kpis.map(kpi => (
              <tr
                key={kpi.kpi_no}
                align="left"
                className="custom-rows"
                style={{
                  color: '#3f3f3f',
                  fontSize: '13px',
                  margin: 0,
                  lineHeight: '20px',
                  textAlign: 'left'
                }}
              >
                <td width="2%" align="center">
                  <span style={{ fontWeight: 'bold' }}>{kpi.kpi_no})</span>
                </td>
                <td width="25%" style={{ color: '#686868' }}>
                  <span>{kpi.kpi_name}</span>
                </td>
                {kpi.data.map((entry, idx) => (
                  <React.Fragment key={idx}>
                    <td
                      bgcolor={idx % 2 === 0 ? '#d3d3d3' : ''}
                      width="7%"
                      align="right"
                    >
                      <span>
                        {entry.kpi_data
                          ? entry.kpi_data.split('/').map((val, i) => (
                              <span key={i} style={{ display: 'block' }}>
                                {val}
                              </span>
                            ))
                          : dataLoaderKpi()}
                      </span>
                    </td>
                    <td
                      bgcolor={idx % 2 === 0 ? '#d3d3d3' : ''}
                      width="4%"
                      align="right"
                    >
                      <span>
                        {entry.variance
                          ? `${Number(entry.variance).toFixed(1)}%`
                          : ''}
                      </span>
                    </td>
                  </React.Fragment>
                ))}
              </tr>
            ))}
            <tr
              align="left"
              className="custom-rows"
              bgcolor="#ee7600"
              style={{
                color: '#3f3f3f',
                fontSize: 7,
                fontWeight: 'normal',
                margin: 0,
                lineHeight: '15px',
                textAlign: 'left',
                height: 8
              }}
            >
              <td
                width="25%"
                style={{
                  marginRight: 0,
                  borderRight: 'none !important'
                }}
                colSpan={5}
              ></td>
              <td
                bgcolor="#1e3365"
                style={{
                  borderRight: 'none !important',
                  borderLeft: 'none !important',
                  borderBottom: 'none',
                  borderTop: 'none'
                }}
                width="1"
              ></td>
              <td
                width="3%"
                style={{
                  marginLeft: 0,
                  borderLeft: 'none !important'
                }}
                colSpan={3}
              ></td>
            </tr>
          </React.Fragment>
        ))}
      </table>
    );
  };
  let previousKpiTypeCode = null;
  return (
    <div className={classes.mainContainerGrid}>
      <div
        paddingwidth="0"
        paddingheight="0"
        class="bgBody"
        className={classes.mainContainerGrid}
        style={{
          opacity: 1,
          height: 'auto',
          padding: 0,
          margin: 0,
          backgroundRepeat: 'repeat',
          backgroundColor: '#fff',
          width: '70% !important',
          fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
          maxWidth: '1150px'
        }}
        id="kpi-body"
        offset="0"
        toppadding="0"
        leftpadding="0"
      >
        <table width={'100%'} cellspacing="0" cellpadding="0" class="bgBody">
          <tbody>
            <tr>
              <td>
                <table
                  width="100%"
                  border="0"
                  cellspacing="0"
                  cellpadding="0"
                  align="left"
                  className="MainContainer"
                >
                  <tbody>
                    <tr>
                      <td className="movableContentContainer">
                        <table
                          width="100%"
                          border="0"
                          cellspacing="0"
                          cellpadding="0"
                        >
                          <tbody>
                            <tr>
                              <td
                                bgcolor="#fff"
                                style={{
                                  padding: '10px 10px'
                                }}
                              >
                                <table
                                  width="100%"
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    marginLeft: 10
                                  }}
                                >
                                  <td width="55%" align="left">
                                    <img
                                      src="images/kpis/top-strip.jpg"
                                      width="100%"
                                      height="30"
                                    />
                                  </td>
                                  <td width="5%" align="left">
                                    <img
                                      src="images/kpis/logo.jpg"
                                      width="60"
                                      height="30"
                                    />
                                  </td>
                                  <td
                                    width="43%"
                                    align="center"
                                    style={{
                                      fontWeight: 'bold'
                                    }}
                                  ></td>
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>

                        <table
                          width="100%"
                          cellspacing="0"
                          cellpadding="0"
                          style={{
                            padding: '0px 20px'
                          }}
                        >
                          <tbody>
                            <tr>
                              <td bgcolor="#fff" width="100%">
                                <table
                                  align="left"
                                  className="custom-table table-border"
                                  width="100%"
                                  cellspacing="0"
                                  cellpadding="0"
                                  border="0"
                                  style={{
                                    borderCollapse: 'collapse',
                                    width: '100%'
                                  }}
                                >
                                  <tr
                                    align="left"
                                    bgcolor="#ee7600"
                                    className={classes.kpiHeading}
                                  >
                                    <td
                                      colSpan={8}
                                      bgcolor="#fff"
                                      style={{
                                        color: '#000',
                                        border: 'none !important'
                                      }}
                                    >
                                      <span style={{ marginRight: 6 }}>
                                        {localStorage.getItem('storeNames')} /
                                      </span>
                                      <span style={{ marginRight: 6 }}>
                                        Advisor :{' '}
                                        {session.kpiAdvisor[0] != 'All'
                                          ? advisorName
                                          : 'NA'}{' '}
                                        /
                                      </span>
                                      <span style={{ marginRight: 6 }}>
                                        Date Range :{' '}
                                        {props.filterStart != ''
                                          ? moment(props.filterStart).format(
                                              'MM/DD/YY'
                                            )
                                          : ''}
                                        {' - '}
                                        {props.filterEnd &&
                                        props.filterEnd != ''
                                          ? moment(props.filterEnd).format(
                                              'MM/DD/YY'
                                            )
                                          : ''}
                                      </span>
                                    </td>
                                  </tr>
                                  <tr>
                                    <td height="4"></td>
                                  </tr>
                                  <tr
                                    align="left"
                                    bgcolor="#1e3365"
                                    style={{
                                      color: '#fff',
                                      fontSize: 13,
                                      fontStyle: 'italic',
                                      fontWeight: 'bold',
                                      border: 'none !important',
                                      margin: 0,
                                      lineHeight: '22px',
                                      textAlign: 'left',
                                      borderColor: '#1e3365'
                                    }}
                                  >
                                    <td
                                      colSpan={2}
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span
                                        style={{
                                          marginLeft: 5,
                                          border: 'none !important'
                                        }}
                                      >
                                        {session.kpiAdvisor[0] != 'All'
                                          ? 'Individual Advisor Selected'
                                          : 'Advisors'}
                                      </span>
                                    </td>
                                    <td
                                      bgcolor="#fff"
                                      style={{
                                        marginLeft: 5,
                                        border: 'none !important'
                                      }}
                                    ></td>
                                    <td
                                      bgcolor="#fff"
                                      style={{
                                        marginLeft: 5,
                                        border: 'none !important'
                                      }}
                                    ></td>
                                    <td
                                      bgcolor="#fff"
                                      style={{
                                        marginLeft: 5,
                                        border: 'none !important'
                                      }}
                                    ></td>
                                    <td
                                      bgcolor="#fff"
                                      style={{
                                        marginLeft: 5,
                                        border: 'none !important'
                                      }}
                                    ></td>

                                    <td
                                      colSpan={1}
                                      style={{
                                        border: '3px solid #000'
                                      }}
                                    >
                                      <span
                                        style={{
                                          marginLeft: 5
                                        }}
                                      >
                                        Dealership
                                      </span>
                                    </td>
                                    <td
                                      bgcolor="#fff"
                                      style={{
                                        marginLeft: 5,
                                        border: 'none !important'
                                      }}
                                    ></td>
                                    <td
                                      bgcolor="#fff"
                                      style={{
                                        marginLeft: 5,
                                        border: 'none !important'
                                      }}
                                    ></td>
                                  </tr>
                                  <tr
                                    align="left"
                                    bgcolor="#ee7600"
                                    style={{
                                      color: '#fff',
                                      fontSize: 13,
                                      fontWeight: 'bold',
                                      margin: 0,
                                      lineHeight: '20px',
                                      textAlign: 'left'
                                      //border: '3px solid #000'
                                    }}
                                  >
                                    <td
                                      colSpan={2}
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span>Metric Measured</span>
                                    </td>
                                    <td
                                      colSpan={1}
                                      width="3%"
                                      align="center"
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span>Goal</span>
                                    </td>
                                    <td
                                      width="10%"
                                      align="center"
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span>Actual</span>
                                    </td>
                                    <td
                                      width="3%"
                                      align="center"
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span>Var</span>
                                    </td>
                                    <td
                                      bgcolor="#1e3365"
                                      //width="1"
                                      style={{
                                        borderTop: '3px solid #000',
                                        borderBottom: 'none',
                                        width: '0.5%'
                                      }}
                                    ></td>
                                    <td
                                      width="4%"
                                      // width="4%"
                                      align="center"
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span>Goal</span>
                                    </td>
                                    <td
                                      width="10%"
                                      align="center"
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span>Actual</span>
                                    </td>
                                    <td
                                      width="4%"
                                      align="center"
                                      style={{ border: '3px solid #000' }}
                                    >
                                      <span>Var</span>
                                    </td>
                                  </tr>
                                  {props.kpiData &&
                                    props.kpiData.map((kpi, index) => {
                                      const isNewKpiTypeCode =
                                        kpi.kpi_type_code !==
                                        previousKpiTypeCode;
                                      previousKpiTypeCode = kpi.kpi_type_code;
                                      const isLast =
                                        index === props.kpiData.length - 1;
                                      return (
                                        <React.Fragment key={index}>
                                          {isNewKpiTypeCode && (
                                            <tr
                                              align="left"
                                              className="custom-rows"
                                              bgcolor="#ee7600"
                                              style={{
                                                color: '#fff',
                                                fontSize: 13,
                                                fontWeight: 'bold',
                                                margin: 0,
                                                lineHeight: '20px',
                                                textAlign: 'left'
                                              }}
                                            >
                                              <td
                                                width="25%"
                                                style={{
                                                  marginRight: 0,
                                                  color: '#fff',
                                                  borderRight:
                                                    'none !important',
                                                  border: '3px solid #000'
                                                }}
                                                colSpan={5}
                                              >
                                                {kpi.kpi_type_code}
                                                &nbsp;&nbsp;&nbsp;
                                                <span
                                                  className="customGrpHeaderLabelSingle"
                                                  dangerouslySetInnerHTML={{
                                                    __html: escapeHtml(
                                                      stripHtmlTags(
                                                        kpi.kpi_type
                                                      )
                                                    )
                                                  }}
                                                />
                                              </td>

                                              <td
                                                bgcolor="#1e3365"
                                                style={{
                                                  borderRight:
                                                    'none !important',
                                                  borderLeft: 'none !important',
                                                  borderBottom: 'none',
                                                  borderTop: 'none'
                                                }}
                                                width="1"
                                              ></td>
                                              <td
                                                width="3%"
                                                style={{
                                                  marginLeft: 0,

                                                  borderLeft: 'none !important'
                                                }}
                                                colSpan={3}
                                              ></td>
                                            </tr>
                                          )}
                                          <tr
                                            align="left"
                                            className="custom-rows-kpi"
                                            style={{
                                              color: '#3f3f3f',
                                              fontSize: '13px',

                                              margin: 0,
                                              lineHeight: '20px',
                                              textAlign: 'left',
                                              borderBottom: isLast
                                                ? '3px solid #000'
                                                : ''
                                            }}
                                          >
                                            <td width="2%" align="center">
                                              <span
                                                style={{ fontWeight: 'bold' }}
                                              >
                                                {kpi.kpi_no}
                                                {kpi.kpi_no ? ')' : ''}
                                              </span>
                                            </td>
                                            <td
                                              width="17%"
                                              style={{ color: '#686868' }}
                                            >
                                              <span
                                                style={{
                                                  display: 'block'
                                                }}
                                              >
                                                {kpi.kpi_name}
                                              </span>
                                            </td>
                                            {kpi.data.map((entry, idVal) => (
                                              <React.Fragment key={idVal}>
                                                <React.Fragment key={idVal}>
                                                  {kpi.data.length == 1 && (
                                                    <>
                                                      <td
                                                        bgcolor={
                                                          idVal % 2 === 0
                                                            ? '#d3d3d3'
                                                            : ''
                                                        }
                                                        width="4%"
                                                        align="right"
                                                        style={{
                                                          padding: 0
                                                        }}
                                                      >
                                                        <span
                                                          style={{
                                                            display: 'block',
                                                            backgroundColor: entry.goal
                                                              ? '#fff'
                                                              : '',
                                                            marginLeft: 0
                                                          }}
                                                        >
                                                          <span
                                                            style={{
                                                              display: 'block',
                                                              lineHeight: '20px'
                                                            }}
                                                          >
                                                            {''}
                                                          </span>
                                                        </span>
                                                      </td>
                                                      <td
                                                        bgcolor={
                                                          idVal % 2 === 0
                                                            ? '#d3d3d3'
                                                            : ''
                                                        }
                                                        width="10%"
                                                        align="right"
                                                        style={{
                                                          padding: 0
                                                        }}
                                                      >
                                                        <span
                                                          style={{
                                                            display: 'block',
                                                            backgroundColor: entry.goal
                                                              ? '#fff'
                                                              : '#d3d3d3',
                                                            marginLeft: 0
                                                          }}
                                                        >
                                                          <span
                                                            style={{
                                                              display: 'block',
                                                              lineHeight: '20px'
                                                            }}
                                                          ></span>
                                                        </span>
                                                      </td>
                                                      <td
                                                        bgcolor={
                                                          idVal % 2 === 0
                                                            ? '#d3d3d3'
                                                            : ''
                                                        }
                                                        width="4%"
                                                        align="right"
                                                        style={{
                                                          padding: 0
                                                        }}
                                                      >
                                                        <span
                                                          className={
                                                            entry.variance
                                                              ? Number(
                                                                  entry.variance
                                                                ).toFixed(1) >
                                                                  -10.0 &&
                                                                Number(
                                                                  entry.variance
                                                                ).toFixed(1) <
                                                                  0.0
                                                                ? classes.varianceGreater
                                                                : Number(
                                                                    entry.variance
                                                                  ).toFixed(
                                                                    1
                                                                  ) <= -10.0
                                                                ? classes.varianceLesser
                                                                : Number(
                                                                    entry.variance
                                                                  ).toFixed(
                                                                    1
                                                                  ) == 0
                                                                ? classes.varianceZero
                                                                : classes.varianceHigher
                                                              : classes.varianceHigher
                                                          }
                                                        >
                                                          {''}
                                                        </span>
                                                      </td>
                                                      <td
                                                        bgcolor="#1e3365"
                                                        style={{
                                                          borderRight:
                                                            'none !important',
                                                          borderLeft:
                                                            'none !important',
                                                          borderBottom: isLast
                                                            ? '3px solid #000'
                                                            : 'none',
                                                          borderTop: 'none'
                                                        }}
                                                        width="1"
                                                      ></td>
                                                    </>
                                                  )}
                                                  {idVal == 1 && (
                                                    <td
                                                      bgcolor="#1e3365"
                                                      style={{
                                                        borderRight:
                                                          'none !important',
                                                        borderLeft:
                                                          'none !important',
                                                        borderBottom: isLast
                                                          ? '3px solid #000'
                                                          : 'none',
                                                        borderTop: 'none'
                                                      }}
                                                      width="1"
                                                    ></td>
                                                  )}
                                                  <td
                                                    bgcolor="#d3d3d3"
                                                    // width="8%"
                                                    width="3%"
                                                    align="right"
                                                    style={{ padding: 0 }}
                                                  >
                                                    <span
                                                      style={{
                                                        display: 'block',
                                                        backgroundColor: entry.goal
                                                          ? '#fff'
                                                          : '#d3d3d3',
                                                        marginLeft: 0
                                                      }}
                                                    >
                                                      {entry.goal}
                                                      {entry.goal ? '%' : ''}
                                                      &nbsp;
                                                    </span>
                                                  </td>
                                                  <td width="10%" align="right">
                                                    <span
                                                      style={{
                                                        height: 20,
                                                        display: 'block'
                                                      }}
                                                    >
                                                      {DotValue(
                                                        entry.kpi_data
                                                          ? entry.kpi_data
                                                          : '',
                                                        kpi.kpi_slno
                                                      )}
                                                      &nbsp;
                                                    </span>
                                                  </td>
                                                  <td
                                                    width="3%"
                                                    bgcolor="#d3d3d3"
                                                    align="right"
                                                    style={{ padding: 0 }}
                                                  >
                                                    <span
                                                      className={
                                                        Number(
                                                          entry.variance
                                                        ).toFixed(1) > -10.0 &&
                                                        Number(
                                                          entry.variance
                                                        ).toFixed(1) < 0.0
                                                          ? classes.varianceGreater
                                                          : Number(
                                                              entry.variance
                                                            ).toFixed(1) <=
                                                            -10.0
                                                          ? classes.varianceLesser
                                                          : entry.variance == ''
                                                          ? classes.varianceNull
                                                          : Number(
                                                              entry.variance
                                                            ).toFixed(1) == 0
                                                          ? classes.varianceZero
                                                          : classes.varianceHigher
                                                      }
                                                    >
                                                      {VarianceValue(
                                                        entry.variance,
                                                        kpi.kpi_slno
                                                      )}
                                                      &nbsp;
                                                    </span>
                                                  </td>
                                                </React.Fragment>
                                              </React.Fragment>
                                            ))}
                                          </tr>
                                        </React.Fragment>
                                      );
                                    })}
                                </table>
                              </td>
                            </tr>
                          </tbody>
                        </table>

                        <table>
                          <tbody>
                            <tr>
                              <td height="30"></td>
                            </tr>
                          </tbody>
                        </table>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default Kpi;
