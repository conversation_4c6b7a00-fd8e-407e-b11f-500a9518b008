#one-month-report-card table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-family: 'Roboto', 'Helvetica', 'Arial', 'sans - serif';
  /* //font-size: 12px; */
}
#one-month-report-card th,
#one-month-report-card td {
  /* border: 1px solid black; */
  padding: 8px;
  text-align: left;
  /* border-bottom: 0px; */
}
#one-month-report-card td:first-child {
  /* border-bottom: 1px solid black; */
  border-right: 1px solid black;
}
#one-month-report-card td:not(:first-child) {
  border-bottom: 1px solid black;
  border-right: 1px solid black;
}
#one-month-report-card th {
  background-color: #f2f2f2;
}
#one-month-report-card .header {
  text-align: center;
  background-color: #d9d9d9;
  font-weight: bold;
}
#one-month-report-card .sub-header {
  background-color: #d9d9d9;
}
#one-month-report-card .highlight {
  background-color: #e6f7e6;
}
#one-month-report-card .alert {
  background-color: #f2dede;
}
#one-month-report-card .small-text {
  font-size: 10px;
}
#one-month-report-card .no-border {
  border: none;
}
#one-month-report-card .header span {
  display: block;
}
#one-month-report-card .main-header {
  text-align: center;
}
#one-month-report-card .orange-bg {
  background-color: #be5014;
  color: #fff;
}
#one-month-report-card .green-bg {
  background-color: #3c7d22;
  color: #fff;
}
#one-month-report-card .yellow-bg-title {
  background-color: #ffc000;
  font-size: 22px;
}
#one-month-report-card .off-white {
  background-color: #fff;
}
#one-month-report-card .ash-bg {
  background-color: #fff;
}
#one-month-report-card .litegreen-bg {
  background-color: #daf2d0;
}
#one-month-report-card .green-text {
  color: #3c7d22;
}
#one-month-report-card .red-text {
  color: #ff0000;
}
#one-month-report-card .dark-red-text {
  color: #fff;
}
#one-month-report-card .grey-bg {
  background-color: #747474;
}
#one-month-report-card .separation {
  height: 3px;
}

#one-month-report-card .orange-sub-title td,
#one-month-report-card .orange-sub-header td {
  font-size: 15px;
  font-weight: bold;
}

#one-month-report-card .text-right {
  text-align: right !important;
}
#one-month-report-card .text-center {
  text-align: center !important;
}
#one-month-report-card .report-card {
  font-size: 17px !important;
  font-weight: 500;
}
#one-month-report-card .blue-bg {
  background-color: #e7f1fa;
}
#one-month-report-card .orange-main-title span {
  display: block;
  text-align: center;
}

#one-month-report-card .separation td {
  padding: 0;
}
#one-month-report-card .maroon-font {
  color: #9c0006;
}
#one-month-report-card .black-bg {
  background-color: black;
}
#one-month-report-card thead.header-section {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #000;
  font-family: 'Roboto', serif;
}
#one-month-report-card .thick-border-r {
  border-right: 7px #000 solid !important;
}
#one-month-report-card thead.header-section th {
  border-top: 7px solid #000;
  border-bottom: 7px solid #000;
  border-right: 1px solid #000;
}
.client-report-outer {
  border: 7px solid #000;
}
#one-month-report-card .no-border td {
  border: 0;
}
#one-month-report-card .title-border {
  border-top: 7px solid #000;
  border-bottom: 7px solid #000;
}
#one-month-report-card .lborder-zero {
  border-left: 0;
}
#one-month-report-card .rborder-zero {
  border-right: 0;
}
#one-month-report-card .no-border {
  border: 0;
}
#one-month-report-card .bborder-zero {
  border-bottom: 0;
}
#one-month-report-card .header-section button {
  /* cursor: pointer; */
  padding: 2px 10px;
  margin: 7px;
  border-radius: 3px;
  width: 150px;
  border: 1px #000 solid;
  pointer-events: none;
}
#one-month-report-card .whiteline {
  border-bottom: 1px solid #fff;
  padding-bottom: 3px;
  margin-bottom: 3px;
}
/* #one-month-report-card .roi {
  font-size: 23px;
  font-weight: 400;
} */
#one-month-report-card .based-text {
  font-size: 13px;
  font-weight: 400;
}
#one-month-report-card .based-text span {
  font-size: 17px;
  font-weight: 700;
  margin-top: 5px;
}

/* #one-month-report-card .main-title {
  font-size: 26px;
} */
/* #one-month-report-card .based-text-main {
  width: 65%;
  float: right;
} */
#one-month-report-card .baseline-small {
  font-size: 13px;
  font-weight: normal;
}

/* #one-month-report-card .datatable td {
  font-size: 17px;
  text-align: right;
  font-weight: 300;
} */
#one-month-report-card .orange-main-title td {
  vertical-align: bottom;
  font-size: 14px;
  font-weight: 400;
}
#one-month-report-card .rocount-title {
  position: relative;
}
#one-month-report-card .rocount-title span {
  border-bottom: 1px solid #000;
  position: absolute;
  right: 0;
  bottom: 0px;
  padding-right: 8px;
  padding-left: 10px;
  padding-bottom: 3px;
}
#one-month-report-card .blue-bg {
  font-weight: 700 !important;
}
#one-month-report-card .orange-sub-title {
  border-bottom: 2px solid #000;
  border-top: 2px solid #000;
}
#one-month-report-card .apply {
  background-color: #ee7600;
  border-radius: 3px;
  color: #fff;
  padding: 2px 10px;
  border: 0;
  cursor: pointer;
  float: right;
}
#one-month-report-card .client-report {
  border-collapse: separate;
  border-spacing: 0;
}

/* 27-12-2024 */
#one-month-report-card .orange-sub-header.orange-bg td:first-child {
  /* border-top: 1px solid #000; */
  border-bottom: 1px solid #000;
}
#one-month-report-card .report-card span:first-child {
  margin-bottom: 5px;
}
#one-month-report-card .report-card span:last-child {
  margin-top: 15px;
}
#one-month-report-card .datatable td {
  font-size: 14px;
  text-align: right;
  padding: 5px;
}
#one-month-report-card .text-right.lborder-zero.kpi-title {
  font-size: 25px !important;
  border-bottom: 1px solid #000;
  padding-right: 8px !important;
}
.report-card-sidebar {
  padding-top: 12px !important;
}
.orange-sub-title td {
  text-align: center !important;
  font-size: 19px;
  font-weight: 700;
}
.report-card-sidebar span.MuiButtonBase-root {
  padding-right: 5px;
}
.report-card-sidebar svg.MuiSvgIcon-root {
  width: 17px;
}
.report-card-sidebar span.MuiTypography-root.MuiFormControlLabel-label {
  line-height: 16px;
}
.report-card-sidebar .MuiFormControlLabel-root {
  height: 17px;
  margin: 4px 2px;
}
.report-card-sidebar .MuiFormControlLabel-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  width: 200px;
  font-size: 14px;
}
.noborder-title {
  border-bottom: 0 !important;
}
.orange-sub-title.orange-bg td {
  font-size: 17px !important;
}
.report-card {
  font-size: 17px !important;
  font-weight: 500;
}
.orange-main-title td {
  font-size: 14px !important;
}
#one-month-report-card .rocount-title {
  font-size: 16px !important;
  font-weight: 500 !important;
}
.roi {
  font-size: 22px;
  font-weight: 400;
}

.orange-main-title.orange-bg td {
  padding: 3px !important;
}
.rocount-bborder {
  border-bottom: 1px solid black !important;
}
.separation .grey-bg:first-child {
  border-bottom: 1px solid black !important;
}
.datatable tbody tr:last-child td:first-child span,
.datatable tbody tr:last-child td {
  border-bottom: 0 !important;
}
.text-right.lborder-zero.kpi-title {
  padding-right: 8px !important;
}
#one-month-report-card .based-text-main {
  width: 70%;
  float: right;
}
@media (max-width: 1785px) {
  #one-month-report-card .rocount-title {
    font-size: 14px !important;
  }
}
@media (max-width: 1780px) {
  #one-month-report-card .based-text {
    font-size: 12px;
  }
}

@media (max-width: 1660px) {
  #one-month-report-card .based-text {
    font-size: 11px;
  }
  #one-month-report-card .based-text-main {
    width: 75%;
  }
}
@media (max-width: 1450px) {
  #one-month-report-card .based-text-main {
    width: 100% !important;
  }
}

@media (max-width: 1650px) {
  #one-month-report-card .based-text {
    font-size: 11px;
  }
  #one-month-report-card .based-text span {
    font-size: 14px;
  }
  #one-month-report-card .report-card {
    font-size: 15px !important;
  }
  .orange-sub-title.orange-bg td {
    font-size: 16px !important;
  }
  #one-month-report-card .kpi-title {
    font-size: 20px !important;
  }
  .orange-main-title td {
    font-size: 11px !important;
  }
  .roi {
    font-size: 18px;
  }
  #one-month-report-card .rocount-title {
    font-size: 13px !important;
  }
}

@media (max-width: 1765px) {
  .orange-main-title td {
    font-size: 12px !important;
  }
}

@media (max-width: 1575px) {
  #one-month-report-card .report-card {
    font-size: 14px !important;
  }
  .orange-main-title td {
    font-size: 10px !important;
  }
  #one-month-report-card .rocount-title {
    font-size: 12px !important;
  }
}

@media (max-width: 1550px) {
  #one-month-report-card .based-text {
    font-size: 10px;
  }
  #one-month-report-card .datatable td {
    font-size: 12px;
  }
  #one-month-report-card .report-card {
    font-size: 12px !important;
  }
  #one-month-report-card .rocount-title {
    font-size: 12px !important;
  }
}
@media (max-width: 1500px) {
  #one-month-report-card .rocount-title {
    font-size: 11px !important;
  }
}

@media (max-width: 1450px) {
  #one-month-report-card .report-card {
    font-size: 10px !important;
  }
  #one-month-report-card .rocount-title {
    font-size: 11px !important;
  }
  .report-card-sidebar .MuiFormControlLabel-label {
    width: 160px;
  }
}

@media (max-width: 1440px) {
  .roi {
    text-align: left;
    padding-left: 20px;
  }
  #one-month-report-card .rocount-title {
    font-size: 10px !important;
  }
}
@media (max-width: 1390px) {
  #one-month-report-card .rocount-title {
    font-size: 10px !important;
  }
}
@media screen and (min-device-width: 1280px) and (max-device-width: 1390px) {
  #one-month-report-card tbody tr:nth-child(12) td.rocount-title span,
  #one-month-report-card tbody tr:nth-child(13) td.rocount-title span {
    padding-bottom: 0 !important;
  }
}

.report-card-sidebar label {
  padding-right: 7px;
}
.sidebar-view-title {
  margin-top: 15px !important;
}
.sidebar-box {
  padding: 9px;
  border-radius: 0 !important;
  border: 1px solid #ccc;
}
.orange-sub-title.orange-bg td:first-child {
  border-bottom: 1px solid #000;
  padding-right: 8px !important;
}
#one-month-report-card .orange-sub-title.orange-bg td {
  font-size: 18px !important;
  padding-bottom: 5px !important;
  padding-top: 5px !important;
}
.sidebar-checkbox {
  pointer-events: none;
}
.sidebar-checkbox:hover {
  background-color: rgba(246, 249, 251, 0);
}
.sidebar-input {
  pointer-events: auto;
}
.sidebar-input:hover {
  background-color: rgba(246, 249, 251, 0) !important;
}
.report-card-sidebar .MuiFormControl-marginDense {
  margin-bottom: 0 !important;
  width: 133px !important;
}

/* .report-card-sidebar span.MuiButtonBase-root:hover {
  background-color: transparent !important;
} */
@media (max-width: 1280px) {
  .roi {
    font-size: 15px;
  }
}

.sidebar-card .MuiCheckbox-root {
  padding: 0 !important;
  margin-right: 5px;
}
