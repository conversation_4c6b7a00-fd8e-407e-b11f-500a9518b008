import React from 'react';
import moment from 'moment';
import clsx from 'clsx';
import './OneMonthReportTable.css';

const OneMonthReportTable = ({
  clientReportCardDetails,
  selectedOptions,
  selectedWorkmixOptions
}) => {
  const summaryData = clientReportCardDetails.summaryData || {};
  const monthlyData = clientReportCardDetails.monthlyData;

  const formatToCustomDate = value => {
    // Check if the value is in the format YYYY-MM (e.g., 2023-09)
    ///if (/^\d{4}-\d{2}$/.test(value)) {

    return moment(value, 'YYYY-MM').format("MMM' YY");
    // }
  };
  const formatCellValue = (value, symbol) => {
    var formattedValue =
      value &&
      parseFloat(value)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    if (/^\d{4}-\d{2}$/.test(value)) {
      formattedValue = moment(value, 'YYYY-MM').format("MMM' YY");
    } else if (symbol == '%') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = '(' + cellValue + '%)';
      } else {
        formattedValue = formattedValue + '%';
      }
    } else if (symbol == '$') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = '($' + cellValue + ')';
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + ')';
      } else {
        formattedValue = formattedValue;
      }
    }

    return formattedValue;
  };
  const renderMonthlyDataRow = item => {
    const highlightNames = [
      'All Stores',
      'Competitive',
      'Maintenance',
      'Repair'
    ];
    const isHighlighted = highlightNames.includes(item.display_name);

    // Initially hide rows with kpi_id 'B', 'C', 'D'
    let showRow = true;
    // Check if the row's kpi_id should be hidden initially
    if (item.kpi_id === 'B' || item.kpi_id === 'C' || item.kpi_id === 'D') {
      showRow = false;
    }

    // If the corresponding selectedWorkmixOptions is checked, show the row with that kpi_id
    if (selectedWorkmixOptions.includes('Competitive') && item.kpi_id === 'B') {
      showRow = true;
    } else if (
      selectedWorkmixOptions.includes('Maintenance') &&
      item.kpi_id === 'C'
    ) {
      showRow = true;
    } else if (
      selectedWorkmixOptions.includes('Repair') &&
      item.kpi_id === 'D'
    ) {
      showRow = true;
    }

    // If the row should not be shown, return null
    if (!showRow) return null;
    // Check if the item is either GP% or Total Parts GP%
    const isGpPercent =
      item.display_name === 'Total Labor GP%' && item.kpi_id == 'A';
    const isTotalPartsGp =
      item.display_name === 'Total Parts GP%' && item.kpi_id == 'A';
    const totalPartsAndLaborGP =
      item.display_name === 'Total Parts & Labor GP' && item.kpi_id == 'A';
    const totalLaborSale =
      item.display_name === 'Total Labor Sale' && item.kpi_id != 'A';
    // Render the highlighted or normal row
    const row = isHighlighted ? (
      <tr class="orange-sub-title orange-bg">
        <td colspan="1" class="text-right lborder-zero cmr">
          {item.display_name}
        </td>
        <td colspan="3"></td>
      </tr>
    ) : (
      <tr>
        <td
          width="15%"
          class={
            isTotalPartsGp || totalPartsAndLaborGP || totalLaborSale
              ? 'text-right no-border rocount-title rocount-bborder'
              : 'text-right no-border rocount-title'
          }
        >
          <span
            className={
              isTotalPartsGp || totalPartsAndLaborGP || totalLaborSale
                ? 'noborder-title'
                : ''
            }
          >
            {item.display_name}
          </span>
        </td>
        <td width="15%" className="off-white">
          {formatCellValue(item.measured_mon, item.symbol)}
        </td>
        <td width="15%" className="off-white">
          {formatCellValue(item.prior_mon, item.symbol)}
        </td>
        <td
          width="10%"
          className={clsx(
            'blue-bg ',
            getVarianceClass(item.variance, item.kpi_id)
          )}
          //className={getVarianceClass(item.variance, item.kpi_id)}
        >
          {formatCellValue(item.variance, item.symbol)}
        </td>
      </tr>
    );

    // Add a separation row after GP% or Total Parts GP%
    return (
      <>
        {row}
        {(isGpPercent || isTotalPartsGp) && (
          <tr className="separation">
            <td className={isGpPercent ? '' : 'grey-bg'} />
            <td className="grey-bg" />
            <td className="grey-bg" />
            <td className="grey-bg" />
          </tr>
        )}
      </>
    );
  };

  const getVarianceClass = (variance, kpiId) => {
    // if (kpiId == 'D') {
    //   if (variance > 0) return ' red-text';
    //   if (variance == 0) return 'off-white';
    //   return 'green-text ';
    // } else {
    if (variance > 0) return 'green-text';
    if (variance == 0) return 'off-white';
    return ' red-text';
    // }
    // if (variance > 0) return 'off-white green-text';
    // if (variance == 0) return 'off-white';
    // return 'off-white red-text';
  };
  const getHeaderClass = value => {
    if (value < 0) return 'dark-red-text';

    return '';
  };
  const renderTableHeader = () => (
    <thead class="header-section">
      <tr class="no-border">
        <td colspan="1" class="header orange-bg text-center report-card">
          <span class="">
            Monthly FOPC:{' '}
            {formatCellValue(summaryData.monthly_fopc_fee || 0, '$')}
          </span>
          <span class="">
            Monthly DMS:{' '}
            {formatCellValue(summaryData.monthly_dms_fee || 0, '$')}
          </span>
          <span>
            Total:{' '}
            {formatCellValue(summaryData.total_monthly_fopc_dms_fee || 0, '$')}
          </span>
        </td>
        <td colspan="1" class="header green-bg text-center">
          <span
            style={{
              display: selectedOptions.includes('monthlyPts') ? 'block' : 'none'
            }}
            class="roi"
          >
            ROI ={' '}
            {/* <span
              className={getHeaderClass(summaryData.roi_parts_labor_gp || 0)}
              style={{ display: 'inline-block' }}
            > */}
            {formatCellValue(summaryData.roi_parts_labor_gp || 0, '%')}
            {/* </span> */}
          </span>
          {selectedOptions.includes('monthlyPts') && <br />}
          <span
            style={{
              display: selectedOptions.includes('monthlyElr') ? 'block' : 'none'
            }}
            class="roi"
          >
            ROI ={' '}
            {/* <span
              className={getHeaderClass(summaryData.roi_repair_elr || 0)}
              style={{ display: 'inline-block' }}
            > */}
            {formatCellValue(summaryData.roi_repair_elr || 0, '%')}
            {/* </span> */}
          </span>
        </td>
        <td colspan="2" class="header green-bg text-center">
          <div class="based-text-main">
            <div
              style={{
                display: selectedOptions.includes('monthlyPts')
                  ? 'block'
                  : 'none'
              }}
              // class="whiteline based-text"
              className={`based-text ${
                selectedOptions.includes('monthlyElr') &&
                selectedOptions.includes('monthlyPts')
                  ? 'whiteline'
                  : ''
              }`}
            >
              Based on Total Pts & Lbr GP Change of{' '}
              <span
              // className={getHeaderClass(summaryData.if_parts_labor_gp || 0)}
              >
                {formatCellValue(summaryData.if_parts_labor_gp || 0, '$')}
              </span>
            </div>
            <div
              style={{
                display: selectedOptions.includes('monthlyElr')
                  ? 'block'
                  : 'none'
              }}
              class="based-text"
            >
              Based on Repair ELR Change of{' '}
              <span
              // className={getHeaderClass(summaryData.if_repair_elr || 0)}
              >
                {formatCellValue(summaryData.if_repair_elr || 0, '$')}
              </span>
            </div>
          </div>
        </td>
      </tr>
      <tr class="title-border">
        <th
          colspan="1"
          class="main-header yellow-bg-title lborder-zero main-title"
        >
          Report Card
        </th>
        <th
          colspan="3"
          class="main-header yellow-bg-title rborder-zero main-title"
        >
          {localStorage.getItem('storeGroup')}
        </th>
      </tr>
      <tr class="orange-sub-title orange-bg">
        <td width="15%" class="text-right lborder-zero kpi-title">
          KPIs
        </td>
        <td width="15%">
          {formatCellValue(monthlyData && monthlyData[0].measured_mon)}
        </td>
        <td width="15%">
          {formatCellValue(monthlyData && monthlyData[0].prior_mon)}
        </td>
        <td width="10%" class="">
          {monthlyData && monthlyData[0].variance}
        </td>
      </tr>
    </thead>
  );

  return (
    <div>
      <title>Monthly Report</title>
      <div
        id={'one-month-report-card'}
        className="client-report-outer"
        // style={{ 'max-height': '680px', 'overflow-y': ' auto' }}
      >
        <table class="client-report-second datatable">
          {renderTableHeader(renderMonthlyDataRow)}

          <tbody>
            {/** Skip the 0th index of `monthlyData` to avoid rendering the first item, 
         as it is already handled elsewhere in the header section. */}
            {monthlyData && monthlyData.slice(1).map(renderMonthlyDataRow)}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default OneMonthReportTable;
