import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect
} from 'react';
import { Redirect } from 'react-router-dom';
import Link from '@material-ui/core/Link';
import { green } from '@mui/material/colors';
import { withKeycloak } from '@react-keycloak/web';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import SaveIcon from '@material-ui/icons/Save';
import RestoreIcon from '@material-ui/icons/Restore';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import moment from 'moment';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Divider,
  FormControl,
  Grid,
  Button,
  TextField,
  Radio,
  RadioGroup,
  InputLabel,
  Select,
  CircularProgress,
  FormHelperText
} from '@material-ui/core';
import {
  getLatestClosedDate,
  insertKPIMonthCardReportName,
  getEmail,
  getStoreNickName,
  getMonthlyClientReportCardDetails,
  getAllStoreNames,
  getOneMonthReportMonths
} from 'src/utils/hasuraServices';

import clsx from 'clsx';
import { useHistory } from 'react-router';
import { makeStyles, withStyles } from '@material-ui/styles';

import MailIcon from '@material-ui/icons/Mail';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';

import {
  getComparisonMonths,
  getLast13Months,
  getLastThreeYears,
  getTimeZone,
  getYearValue
} from 'src/utils/Utils';
import { useDispatch, useSelector } from 'react-redux';
import OneMonthReportTable from './OneMonthReportTable';
import IncludedStores from '../../../components/IncludedStores';
import ComparisonChartsGrid from 'src/views/AddOns/ServiceAdvisor/ComparisonChartsGrid';
import MonthComparison from './MonthComparison';
import Alert from '@material-ui/lab/Alert';
import {
  oneMonthRoiOption,
  oneMonthRoiSelectedOptions
} from 'src/utils/constants';
import ROIView from 'src/components/ROIView';
import WorkmixView from 'src/components/WorkmixView';
import Page from 'src/components/Page';
import SaveReportDialog from 'src/components/SaveReportDialog';
import SuccessSnackbar from '../SuccessSnackbar';
import { usePDFExport } from './printToPDF';
import EmailDialogKpi from 'src/components/EmailDialogKpi';
import { useExcelExport } from 'src/components/PrintExcelThreeMonth';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',

    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',

    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  linkItem: {
    cursor: 'pointer'
  },
  linkItemDisable: {
    pointerEvents: 'none',
    color: 'grey'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  }
}));
const OneMonthReportPage = props => {
  const session = useSelector(state => state.session);
  const { exportToPDF } = usePDFExport();
  // let isDisabled =
  //   createdBy != localStorage.getItem('userID') &&
  //   props.keycloak.realmAccess.roles.includes('superadmin');

  let created_by =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.created_by
      ? props.history.location.state.created_by
      : '';
  let isDisabled =
    created_by != localStorage.getItem('userID') &&
    props.keycloak.realmAccess.roles.includes('superadmin');

  let monthsArray = [];
  if (localStorage.getItem('versionFlag') === 'TRUE') {
    monthsArray = getLastThreeYears();
  } else {
    monthsArray = getLast13Months();
  }
  const { exportToExcel, tryExcelData } = useExcelExport();
  // const [queryMonth1, setQueryMonth1] = useState(
  //   measuredMTH || monthsArray[monthsArray.length - 1]
  // );
  // const [queryMonth2, setQueryMonth2] = useState(
  //   priorMTH || monthsArray[monthsArray.length - 1]
  // );
  const classes = useStyles();
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [isMonthLoading, setIsMonthLoading] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingIconExcel, setLoadingIconExcel] = useState(false);
  const [successExcel, setSuccessExcel] = useState(false);
  const [createdBy, setCreatedBy] = useState(created_by);
  const [loadingIconPDF, setLoadingIconPDF] = useState(false);
  const [closedDate, setClosedDate] = useState('');
  const [clientReportCardDetails, setClientReportCardDetails] = useState([]);
  const [allStoreNames, setAllStoreNames] = useState([]);
  const defaultStoreIds = JSON.parse(localStorage.getItem('selectedStoreId'));
  const permittedStoreIds = JSON.parse(
    localStorage.getItem('allPermittedStores')
  );
  const [selectedStoreIds, setSelectedStoreIds] = useState();
  const [measuredMTH, setMeasuredMTH] = useState();
  const [priorMTH, setPriorMTH] = useState();
  const [initialMeasuredMTH, setInitialMeasuredMTH] = useState();
  const [initialPriorMTH, setInitialPriorMTH] = useState();
  const [isFirstRender, setIsFirstRender] = useState(true);
  const [isStoreLoading, setIsStoreLoading] = useState(false);
  const [isEmpty, setIsEmpty] = useState(false);
  const [mnthErrMsg, setMnthErrMsg] = useState('');
  const [selectedOptions, setSelectedOptions] = useState();
  const [selectedWorkmixOptions, setSelectedWorkmixOptions] = useState([]);
  // let fileName = 'Client Report Card - 1 Month';
  //  const [selectedOptions, setSelectedOptions] = useState(
  //    oneMonthRoiSelectedOptions
  //  ); const [selectedStoreIds, setSelectedStoreIds] = useState(defaultStoreIds);
  // const [selectedStoreIds, setSelectedStoreIds] = useState(defaultStoreIds);
  // save report

  const [openSaveDlg, setOpenSaveDlg] = useState(false);
  const [parent, setParent] = useState(); //Todo: Need to set it from Props.
  const [copyFile, setCopyFile] = useState(false);
  const [copy, setCopy] = useState(false);
  const [reportName, setReportName] = useState('');
  const [errorReport, setErrorReport] = useState('');
  const [requiredText, setRequiredText] = useState(false);
  const [selectedType, setSelectedType] = useState('public');
  const [filterText, setFilterText] = useState('');
  const [reportNameCopy, setReportNameCopy] = useState('');
  const [visibile, setVisible] = useState();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const pathaname = props?.kpiReportData?.isFrom;
  const [image, setImage] = useState();
  const [openDialogue, setOpenDialogue] = useState(false);
  const [mailUsers, setMailUsers] = useState([]);
  const [openDilogAll, setOpenDilogAll] = useState(false);
  var iKpiReportType = 'Client_Report_Card_1_Month';
  const storeGroup = localStorage.getItem('storeGroup');
  // const storeName = localStorage.getItem('storeSelected');

  let selectStoreDetails =
    selectedStoreIds &&
    selectedStoreIds.length > 0 &&
    allStoreNames.filter(store => selectedStoreIds[0] == store.storeId);
  let storeName = '';
  if (selectStoreDetails?.[0]?.storeNickname) {
    storeName = selectStoreDetails[0].storeNickname;
  } else {
    storeName = selectStoreDetails?.[0]?.storeName;
  }
  let fileName = useMemo(() => {
    if (pathaname === 'OneMonthReport') {
      return reportNameCopy && parent == 'savedReports'
        ? `Client Report Card - 1 Month - Saved Reports - ${reportNameCopy}`
        : reportName && parent == 'savedReports'
        ? `Client Report Card - 1 Month - Saved Reports - ${reportName}`
        : 'Client Report Card - 1 Month';
    }
    return '';
  }, [pathaname, reportName, reportNameCopy]);
  useEffect(() => {
    if (props.kpiReportData) {
      setSelectedStoreIds(props.kpiReportData.selectedStoreIds);
      setMeasuredMTH(props.kpiReportData.measuredMTH);
      setPriorMTH(props.kpiReportData.priorMTH);
      setSelectedOptions(props.kpiReportData.selectedRoiView);
      setSelectedWorkmixOptions(props.kpiReportData.workmixview);
      setParent(props.kpiReportData.parent);
      setReportName(props.kpiReportData.reportName);
      setSelectedType(props.kpiReportData.visibility);
    }
  }, [props.kpiReportData]);
  let fileNameExport = useMemo(() => {
    if (selectedStoreIds && selectedStoreIds.length > 1) {
      return reportNameCopy
        ? `Client Report Card - 1 Month - ${storeGroup} - ${reportNameCopy}`
        : reportName
        ? `Client Report Card - 1 Month - ${storeGroup} - ${reportName}`
        : `Client Report Card - 1 Month - ${storeGroup}`;
    } else {
    }
    return reportNameCopy
      ? `Client Report Card - 1 Month - ${storeGroup} - ${storeName} - ${reportNameCopy}`
      : reportName
      ? `Client Report Card - 1 Month - ${storeGroup} -  ${storeName} - ${reportName}`
      : `Client Report Card - 1 Month - ${storeGroup} - ${storeName}`;
  }, [
    pathaname,
    reportName,
    reportNameCopy,
    selectStoreDetails,
    selectedStoreIds
  ]);

  const handleCancelSaveReport = () => {
    setSelectedType(props.kpiReportData.visibility);
    setOpenSaveDlg(false);
    setCopy(false);
    setRequiredText(false);
    setCopyFile(false);
    setErrorReport('');
    if (parent !== 'savedReports') {
      setReportName('');
    }
  };
  const handleCopyReport = () => {
    setOpenSaveDlg(true);
    setCopy(true);
    setCopyFile(true);
    if (reportName != '') {
      setReportNameCopy(reportName);
    }
    setReportName('');
  };
  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
    // setTimeout(() => {
    //   this.setState({ goalFail: false });
    // }, 2000);
  };
  const handleOkSaveReport = () => {
    var iStoreId =
      props &&
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.store_id
        ? props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');

    if (parent === 'savedReports') {
      insertKPIMonthCardReportName(
        'update',
        iStoreId,
        reportName.trim(),
        '',
        null,
        null,
        priorMTH,
        measuredMTH,
        userId,
        iKpiReportType,
        null,
        selectedType,
        userRole,
        selectedWorkmixOptions,
        selectedStoreIds,
        selectedOptions,

        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
              .results[0].status == 1
          ) {
            setErrorReport('');
            // setReportName('');
            setRequiredText(false);
            setOpenSaveDlg(false);
            setCopy(false);
            setOpenSnackbar(true);
          } else {
            setRequiredText(true);
            setErrorReport(
              result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
                .results[0].msg
            );
          }
        }
      );
    } else {
      insertKPIMonthCardReportName(
        'insert',
        iStoreId,
        reportName.trim(),
        '',
        null,
        null,
        priorMTH,
        measuredMTH,
        userId,
        iKpiReportType,
        null,
        selectedType,
        userRole,
        selectedWorkmixOptions,
        selectedStoreIds,
        selectedOptions,

        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
              .results[0].status == 1
          ) {
            setErrorReport('');
            setReportName('');
            setRequiredText(false);
            setOpenSaveDlg(false);
            setCopy(false);
            setOpenSnackbar(true);
          } else {
            setRequiredText(true);
            setErrorReport(
              result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
                .results[0].msg
            );
          }
        }
      );
    }
    // setOpenSaveDlg(false);
  };

  const handleSaveAsReport = () => {
    var iStoreId =
      props &&
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.store_id
        ? props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    var iKpiReportType = 'Client_Report_Card_1_Month';
    insertKPIMonthCardReportName(
      'insert',
      iStoreId,
      reportName.trim(),
      '',
      null,
      null,
      priorMTH,
      measuredMTH,
      userId,
      iKpiReportType,
      null,
      selectedType,
      userRole,
      selectedWorkmixOptions,
      selectedStoreIds,
      selectedOptions,

      result => {
        if (
          result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
            .results[0].status == 1
        ) {
          setErrorReport('');
          // setReportName('');
          setRequiredText(false);
          setOpenSaveDlg(false);
          setCopy(false);
          setOpenSnackbar(true);
        } else {
          setRequiredText(true);
          setErrorReport(
            result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
              .results[0].msg
          );
        }
      }
    );
  };

  const handleCheckboxChange = event => {
    setSelectedType(event.target.value);
  };
  const CancelDilogAll = () => {
    setOpenDilogAll(false);
  };
  const onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;
    if (!nameRegex.test(e.target.value) && e.target.value) {
      setRequiredText('');
    } else {
      setErrorReport('');
      setReportName(e.target.value);

      setRequiredText(false);
    }
  };
  const handleSaveReport = () => {
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      parent != 'savedReports'
    ) {
      setOpenDilogAll(true);
    } else {
      setOpenSaveDlg(true);
      if (parent == 'savedReports') {
        setReportName(props.kpiReportData.reportName);
      }
    }
  };

  //enf of save report
  const linkStyle = useMemo(() => {
    return {
      pointerEvents:
        createdBy !== '' && createdBy === localStorage.getItem('userID')
          ? 'auto'
          : createdBy !== '' &&
            createdBy !== localStorage.getItem('userID') &&
            !props.keycloak.realmAccess.roles.includes('superadmin')
          ? 'none'
          : 'auto',
      color:
        createdBy !== '' && createdBy === localStorage.getItem('userID')
          ? '#003d6b'
          : createdBy !== '' &&
            createdBy !== localStorage.getItem('userID') &&
            !props.keycloak.realmAccess.roles.includes('superadmin')
          ? 'grey'
          : '#003d6b'
    };
  }, [isDisabled, createdBy, session.storeSelected]);

  useEffect(() => {
    getLatestClosedDate(result => {
      if (result) {
        var openDate = '';
        var closedDate = result[0].value;
        setClosedDate(closedDate);
        localStorage.setItem('closedDate', closedDate);
        setIsPageLoading(true);
      }
    });
  }, [session.serviceAdvisor]);

  useEffect(() => {
    var storeid;
    if (defaultStoreIds.length == permittedStoreIds.length) {
      storeid = 'All';
    } else {
      storeid = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    }
    setIsMonthLoading(true);
    getOneMonthReportMonths(storeid, result => {
      var monthData =
        result.data.statelessCcPhysicalRwGetClientreportOneMonthDefaultDate
          .nodes;
      var measureMonthdata = monthData.filter(function(el) {
        return el.keyName == 'measured_month';
      });
      var priorMonthdata = monthData.filter(function(el) {
        return el.keyName == 'prior_month';
      });
      var mesMon =
        props &&
        props.history &&
        props.history.location &&
        props.history.location.state &&
        props.history.location.state.currentmonthyear;
      var priMon =
        props &&
        props.history &&
        props.history.location &&
        props.history.location.state &&
        props.history.location.state.previousmonthyear;

      let realm = localStorage.getItem('realm');
      if (realm == 'demoag' || realm == 'demoenterprise') {
        if (
          typeof measureMonthdata[0].keyValue != 'undefined' &&
          typeof priorMonthdata[0].keyValue != 'undefined' &&
          props.kpiReportData &&
          props.kpiReportData.parent != 'savedReports'
        ) {
          setMeasuredMTH(measureMonthdata[0].keyValue);
          setPriorMTH(priorMonthdata[0].keyValue);
          setInitialMeasuredMTH(measureMonthdata[0].keyValue);
          setInitialPriorMTH(priorMonthdata[0].keyValue);
        } else {
          setMeasuredMTH(mesMon);
          setPriorMTH(priMon);
          setInitialMeasuredMTH(mesMon);
          setInitialPriorMTH(priMon);
        }
      } else {
        if (props.kpiReportData.parent != 'savedReports') {
          var cDate = localStorage.getItem('closedDate');
          var mMth = moment(cDate)
            .subtract(1, 'months')
            .format('YYYY-MM');
          var pMth = moment(cDate)
            .subtract(2, 'months')
            .format('YYYY-MM');

          setMeasuredMTH(mMth);
          setPriorMTH(pMth);
          setInitialMeasuredMTH(mMth);
          setInitialPriorMTH(pMth);
        } else {
          setMeasuredMTH(mesMon);
          setPriorMTH(priMon);
          setInitialMeasuredMTH(mesMon);
          setInitialPriorMTH(priMon);
        }
      }

      setIsMonthLoading(false);
    });
  }, [session.storeSelected, props.kpiReportData]);

  useEffect(() => {
    if (
      session.storeSelected != '' &&
      session.storeSelected != selectedStoreIds
    ) {
      setSelectedStoreIds(JSON.parse(session.storeSelected));
      setIsStoreLoading(true);
      setParent('');

      setReportName('');
      setReportNameCopy('');
      setSelectedStoreIds(defaultStoreIds);
      setMeasuredMTH(initialMeasuredMTH); // Reset the first comparison month
      setPriorMTH(initialPriorMTH);
      setSelectedWorkmixOptions([]);
      setSelectedOptions(oneMonthRoiSelectedOptions);
    }
  }, [session.storeSelected]);

  useEffect(() => {
    console.log('ccc==11', isMonthLoading);

    const fetchData = async () => {
      try {
        setIsEmpty(false);
        if (isFirstRender) {
          // First render: Call both functions
          setIsStoreLoading(true);
          const [
            monthlyClientReportResult,
            allStoreNamesResult
          ] = await Promise.all([
            fetchMonthlyClientReportCardDetails(),
            getAllStoreName()
          ]);
          if (monthlyClientReportResult.error) {
            setIsEmpty(true);
          } else {
            setClientReportCardDetails(monthlyClientReportResult);
          }

          // Set client report card details

          // Extract store names and IDs
          const storeNames = allStoreNamesResult.map(item => ({
            storeName: item.storeName,
            storeId: item.storeId,
            storeNickname: item.storeNickname
          }));

          setAllStoreNames(storeNames);
          setIsFirstRender(false);
        } else {
          // Subsequent renders: Call only fetchMonthlyClientReportCardDetails
          setIsLoading(true);
          const monthlyClientReportResult = await fetchMonthlyClientReportCardDetails();

          if (monthlyClientReportResult.error) {
            setIsEmpty(true);
          } else {
            setClientReportCardDetails(monthlyClientReportResult);
          }
          // setClientReportCardDetails(monthlyClientReportResult);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
        setIsPageLoading(true);
        setIsStoreLoading(false);

        console.log('Fetch data operation complete');
      }
    };

    if (
      mnthErrMsg == '' &&
      !isMonthLoading &&
      measuredMTH != undefined &&
      priorMTH != undefined
    ) {
      fetchData();
    }
  }, [isMonthLoading, selectedStoreIds, measuredMTH, priorMTH]);

  const fetchMonthlyClientReportCardDetails = () => {
    return new Promise((resolve, reject) => {
      getMonthlyClientReportCardDetails(
        measuredMTH,
        priorMTH,
        selectedStoreIds,
        result => {
          const clientReportcardDetails =
            result.data
              ?.statelessDbdKpiScorecardGetMonthlyClientReportcardDetails
              ?.clientReportcardDetails;

          if (clientReportcardDetails) {
            const data = JSON.parse(clientReportcardDetails[0].jsonData);
            resolve(data); // Resolve with the parsed data
          } else {
            reject('No client report card details found');
          }
        }
      );
    });
  };

  const getAllStoreName = () => {
    return new Promise((resolve, reject) => {
      const groupIds = JSON.parse(localStorage.getItem('allPermittedStores'));
      getAllStoreNames(groupIds, result => {
        if (result) {
          resolve(result); // Resolve with the store names
        } else {
          reject('Failed to fetch store names');
        }
      });
    });
  };

  const setResetDashboard = () => {
    if (parent === 'savedReports') {
      setIsPageLoading(false);
      setSelectedStoreIds(props.kpiReportData.selectedStoreIds);
      setMeasuredMTH(props.kpiReportData.measuredMTH);
      setPriorMTH(props.kpiReportData.priorMTH);
      setSelectedOptions(props.kpiReportData.selectedRoiView);
      setSelectedWorkmixOptions(props.kpiReportData.workmixview);
      setParent(props.kpiReportData.parent);
      setReportName(props.kpiReportData.reportName);
      setSelectedType(props.kpiReportData.visibility);
      setTimeout(() => {
        setIsPageLoading(true);
      }, 500);
    } else {
      setIsPageLoading(false); // Reset page loading state
      setClientReportCardDetails([]); // Clear the report card data
      setSelectedStoreIds(defaultStoreIds);
      setMeasuredMTH(initialMeasuredMTH); // Reset the first comparison month
      setPriorMTH(initialPriorMTH); // Reset the second comparison month
      setIsStoreLoading(false); // Reset store loading state
      setIsFirstRender(true);
      setSelectedWorkmixOptions([]);
      setSelectedOptions(oneMonthRoiSelectedOptions);
      setMnthErrMsg('');
    }
  };
  const renderBackButton = () => {
    let expandedRpt =
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.isExpanded
        ? props.history.location.state.isExpanded
        : 0;
    //props.history.push('/ReportSaved', { selAdvisors });
    props.history.push({
      pathname: '/SavedReports',
      state: {
        isExpanded: expandedRpt,
        created_by:
          props &&
          props.location &&
          props.location.state &&
          props.location.state.created_by
      }
    });
  };
  const handlePDFExportClick = () => {
    exportToPDF(
      clientReportCardDetails,
      image,
      selectedWorkmixOptions,
      fileNameExport,
      selectedOptions
    );
  };
  const convertImageToBase64 = (imgSrc, callback) => {
    const img = new Image();
    img.src = imgSrc;
    img.crossOrigin = 'Anonymous';

    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      const dataURL = canvas.toDataURL('image/png');

      callback(dataURL); // Pass the result using a callback
    };
  };
  const handleMailClick = () => {
    setOpenDialogue(true);
    let userList = [];
    getEmail('NULL', 'NULL', 'view', 'Client_Report_Card_1_Month', result => {
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        userList.push(
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
            item => item.value
          )
        );
        setMailUsers(userList);
      } else {
        setMailUsers([]);
      }
    });
  };
  const handleCloseEmail = () => {
    setOpenDialogue(false);
  };
  const handleExcelExportClick = () => {
    if (clientReportCardDetails && selectedOptions && selectedWorkmixOptions) {
      exportToExcel(
        clientReportCardDetails,
        selectedOptions,
        selectedWorkmixOptions,
        'OneMonth',
        '',
        '',
        fileNameExport
      );
      // setSuccessExcel(true);
      // setLoadingIconExcel(false);
    }
  };
  return (
    <>
      {isPageLoading && !isMonthLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <Grid
              xs={12}
              className={clsx(classes.headerItem, 'main-title-kpi')}
            >
              {/* <Grid className="adv-report"></Grid> */}
              <Grid className="adv-report">
                <Grid className="kpi-report-2-name">
                  {parent == 'savedReports' && (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      // style={linkButtonStyle}
                      onClick={renderBackButton}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  )}
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(classes.mainLabel, 'main-title')}
                  >
                    {fileName}
                  </Typography>
                </Grid>

                <Grid className="picker-report-one-month">
                  {closedDate ? (
                    <Typography
                      variant="body1"
                      color="secondary"
                      align="right"
                      className={clsx(classes.dataLabel, 'date-asof')}
                    >
                      {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
                    </Typography>
                  ) : (
                    ''
                  )}
                  <Tooltip title="Save Report">
                    <Link
                      className={classes.linkItem}
                      style={{
                        ...linkStyle
                        // pointerEvents: 'none',
                        // opacity: 0.5
                      }}
                      onClick={handleSaveReport}
                    >
                      <SaveIcon
                        style={{
                          height: '0.9em',
                          weight: '0.8em',
                          marginBottom: '-2px',
                          marginRight: '-6px'
                        }}
                      />
                    </Link>
                  </Tooltip>
                  {parent == 'savedReports' && (
                    // {parent == 'savedReports' && (
                    <Tooltip title="Rename and Copy">
                      <Link
                        className={classes.linkItem}
                        // style={linkStyleCopy}
                        style={{
                          display:
                            (createdBy != '' &&
                              createdBy == localStorage.getItem('userID')) ||
                            selectedType == 'public' ||
                            props.keycloak.realmAccess.roles.includes(
                              'superadmin'
                            )
                              ? 'block'
                              : 'none'
                        }}
                        onClick={handleCopyReport}
                      >
                        <FileCopyOutlinedIcon
                          style={{
                            height: '0.73em',
                            weight: '0.8em',
                            marginRight: '-8px',
                            marginTop: '2px'

                            //                         height: 0.76em;
                            // margin-right: -8px;
                            // margin-top: 2px;
                            // display: allStore ? 'none' : 'block'
                          }}
                        />
                      </Link>
                    </Tooltip>
                  )}{' '}
                  <Tooltip title="Email Now">
                    <Link
                      className={classes.linkItem}
                      style={{
                        ...linkStyle
                      }}
                      onClick={() => handleMailClick()}
                    >
                      <MailIcon
                        style={{
                          height: '1.15em',
                          weight: '1.0em',
                          paddingTop: '2px',
                          marginRight: '-2px'
                        }}
                      />
                    </Link>
                  </Tooltip>
                  <Tooltip title="Export To PDF">
                    <div
                      style={{
                        position: 'relative',
                        display: 'inline-block'
                        // pointerEvents: 'none',
                        // opacity: 0.5
                      }}
                    >
                      <Link
                        className={classes.linkItem}
                        style={{ textDecoration: 'none' }}
                        onClick={() => handlePDFExportClick()}
                      >
                        <i
                          class="fas fa-file-pdf"
                          style={{
                            cursor: 'pointer',
                            color: successExcel ? '#c20a0a' : '#c20a0a'
                          }}
                        ></i>
                      </Link>
                      {loadingIconPDF && (
                        <CircularProgress
                          style={{
                            width: '23px',
                            height: '23px',
                            color: green[500],
                            position: 'absolute',
                            top: '-3px',
                            left: '-5px',
                            zIndex: 1
                          }}
                        />
                      )}
                    </div>
                  </Tooltip>
                  <Tooltip title="Export To Excel">
                    <div
                      style={{
                        position: 'relative',
                        display: 'inline-block'
                        // pointerEvents: 'none',
                        // opacity: 0.5
                      }}
                    >
                      <Link
                        className={classes.linkItem}
                        style={{ textDecoration: 'none' }}
                        onClick={() => handleExcelExportClick()}
                      >
                        <i
                          class="fas fa-file-excel"
                          style={{
                            cursor: 'pointer',
                            color: successExcel ? '#0e7c3e' : '#0e7c3e'
                          }}
                        ></i>
                      </Link>
                      {loadingIconExcel && (
                        <CircularProgress
                          style={{
                            width: '23px',
                            height: '23px',
                            color: green[500],
                            position: 'absolute',
                            top: '-3px',
                            left: '-5px',
                            zIndex: 1
                          }}
                        />
                      )}
                    </div>
                  </Tooltip>
                  <Button
                    variant="contained"
                    id="reset-layout-kpi"
                    className={'reset-btn'}
                    onClick={setResetDashboard}
                  >
                    <RestoreIcon style={{ height: '0.8em', weight: '0.8em' }} />
                    <Typography variant="body1" align="left">
                      Reset Layout
                    </Typography>
                  </Button>
                </Grid>
              </Grid>
            </Grid>
            <Divider />
            <Grid container>
              {/* <Grid
                item
                xs={6}
                style={{
                  maxWidth: '100%',
                  flexBasis: '100%'
                }}
              >
              
              </Grid> */}
              <Grid
                item
                xs={6}
                style={{
                  padding: '8px',
                  maxWidth: '100%',
                  flexBasis: '100%'
                }}
              >
                {isStoreLoading !== false ? (
                  <Grid
                    justify="center"
                    style={{
                      height: 300,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <CircularProgress size={60} />
                  </Grid>
                ) : (
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Paper
                        style={{
                          padding: '0px',
                          borderRadius: '0px',
                          boxShadow: 'none'
                          //border: '1px solid #ccc'
                          //  height: 680,
                        }}
                      >
                        {/* {isLoading !== false ? (
                          <Grid
                            justify="center"
                            style={{
                              height: 600,
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}
                          >
                            <CircularProgress size={60} />
                          </Grid>
                        ) : (
                          
                          <AuditTable
                            clientReportCardDetails={clientReportCardDetails}
                          />
                        )} */}
                        {isLoading !== false ? (
                          <Grid
                            justifyContent="center"
                            style={{
                              height: 680,
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}
                          >
                            <CircularProgress size={60} />
                          </Grid>
                        ) : isEmpty ? (
                          <Alert
                            severity="info"
                            style={{ fontSize: 14, fontWeight: 'bold' }}
                          >
                            Selected month has no data found during the time
                            period.
                          </Alert>
                        ) : (
                          <OneMonthReportTable
                            clientReportCardDetails={clientReportCardDetails}
                            selectedOptions={selectedOptions}
                            selectedWorkmixOptions={selectedWorkmixOptions}
                          />
                        )}
                      </Paper>
                      <SaveReportDialog
                        openSaveDlg={openSaveDlg}
                        parent={parent}
                        copyFile={copyFile}
                        copy={copy}
                        reportName={reportName}
                        errorReport={errorReport}
                        requiredText={requiredText}
                        selectedType={selectedType}
                        filterText={filterText}
                        onChangeReportName={onChangeReportName}
                        handleCancelSaveReport={handleCancelSaveReport}
                        handleOkSaveReport={handleOkSaveReport}
                        handleSaveAsReport={handleSaveAsReport}
                        handleCheckboxChange={handleCheckboxChange}
                        reportNameCopy={reportNameCopy}
                      />
                    </Grid>
                    <Grid
                      item
                      xs={12}
                      sm={6}
                      className="report-card-sidebar one-month-sidebar"
                    >
                      <MonthComparison
                        measuredMTH={measuredMTH}
                        priorMTH={priorMTH}
                        setMeasuredMTH={setMeasuredMTH}
                        setPriorMTH={setPriorMTH}
                        mnthErrMsg={mnthErrMsg}
                        setMnthErrMsg={setMnthErrMsg}
                        closedDate={moment(closedDate).format('YYYY-MM')}
                      />
                      {mnthErrMsg && (
                        <Box>
                          <FormHelperText
                            className={classes.helperText}
                            style={{
                              fontWeight: 500
                            }}
                          >
                            {mnthErrMsg}
                          </FormHelperText>
                        </Box>
                      )}
                      <Box className="card-selectors" sx={{ mb: 1 }}>
                        <IncludedStores
                          allStoreNames={allStoreNames}
                          selectedStoreIds={selectedStoreIds}
                          setSelectedStoreIds={setSelectedStoreIds}
                        />
                      </Box>
                      <Box className="card-selectors" sx={{ mb: 1 }}>
                        <ROIView
                          selectedOptions={selectedOptions}
                          setSelectedOptions={setSelectedOptions}
                          options={oneMonthRoiOption}
                        />
                      </Box>
                      <Box className="card-selectors" sx={{ mb: 1 }}>
                        <WorkmixView
                          selectedWorkmixOptions={selectedWorkmixOptions}
                          setSelectedWorkmixOptions={setSelectedWorkmixOptions}
                        />
                      </Box>
                      <SuccessSnackbar
                        onClose={handleSnackbarClose}
                        open={openSnackbar}
                        msg={'Report saved successfully!'}
                        //goalFail={this.state.goalFail}G
                      />

                      <EmailDialogKpi
                        open={openDialogue}
                        handlePopupClose={handleCloseEmail}
                        mailUsers={mailUsers}
                        clientReportCardDetails={clientReportCardDetails}
                        image={image}
                        selectedWorkmixOptions={selectedWorkmixOptions}
                        iKpiReportType={iKpiReportType}
                        measuredMTH={measuredMTH}
                        priorMTH={priorMTH}
                        selectedOptions={selectedOptions}
                        selectedStoreIds={selectedStoreIds}
                        reportName={reportName}
                        reportNameCopy={reportNameCopy}
                        selectStoreDetails={selectStoreDetails}
                        selectedStoreName={storeName}
                      ></EmailDialogKpi>
                      {openDilogAll ? (
                        <Dialog
                          open={openDilogAll}
                          aria-labelledby="alert-dialog-title"
                          aria-describedby="alert-dialog-description"
                        >
                          <DialogContent>
                            <Typography
                              variant="h6"
                              style={{ textTransform: 'none' }}
                            >
                              This option is not available at all stores.
                            </Typography>
                          </DialogContent>
                          <DialogActions>
                            <Button
                              onClick={CancelDilogAll}
                              autoFocus
                              color="primary"
                            >
                              Ok
                            </Button>
                          </DialogActions>
                        </Dialog>
                      ) : null}
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Paper>
        </div>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </>
  );
};
export default withKeycloak(OneMonthReportPage);
