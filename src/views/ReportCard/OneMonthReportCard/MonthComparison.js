import {
  Grid,
  FormControl,
  MenuItem,
  Select,
  InputLabel,
  FormHelperText
} from '@material-ui/core';
import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import moment from 'moment';
import { getLast13Months, getLastThreeYears } from 'src/utils/Utils';

const useStyles = makeStyles({
  formControl: {
    paddingRight: 14,
    width: 130
  },
  helperText: {
    color: 'red',
    fontSize: '0.75rem'
  }
});

const MonthComparison = ({
  measuredMTH,
  setMeasuredMTH,
  priorMTH,
  setPriorMTH,
  mnthErrMsg,
  setMnthErrMsg,
  closedDate
}) => {
  const classes = useStyles();
  const [priorMonth, setPriorMonth] = useState(priorMTH);
  const [measuredMonth, setMeasuredMonth] = useState(measuredMTH);

  const handleMonthchange1 = event => {
    setMeasuredMonth(event.target.value);
    setMeasuredMTH(event.target.value);
    validateMonths(event.target.value, priorMonth);
  };

  const handleMonthchange2 = event => {
    setPriorMonth(event.target.value);
    setPriorMTH(event.target.value);
    validateMonths(measuredMonth, event.target.value);
  };

  const validateMonths = (measuredMonth, priorMonth) => {
    if (
      measuredMonth &&
      priorMonth &&
      (!moment(priorMonth).isBefore(measuredMonth) ||
        moment(priorMonth).isSame(measuredMonth))
    ) {
      setMnthErrMsg('Prior MTH must be older than Measured MTH');
    } else {
      setMnthErrMsg('');
    }
  };
  useEffect(() => {
    if (measuredMTH) setMeasuredMonth(measuredMTH);
    if (priorMTH) setPriorMonth(priorMTH);
  }, [measuredMTH, priorMTH]);

  // Function to compare months
  const isPriorMonthValid = (priorMonth, measuredMonth) => {
    // Ensure prior month is strictly before the measured month
    return moment(priorMonth).isBefore(moment(measuredMonth), 'month');
  };

  // Updated `getValidMonths` function
  const getValidMonths = isPrior => {
    const availableMonths =
      localStorage.getItem('versionFlag') === 'TRUE'
        ? getLastThreeYears()
        : getLast13Months();

    return availableMonths.map(month => ({
      value: month,
      isValid: isPrior
        ? isPriorMonthValid(month, measuredMonth)
        : moment(month).isAfter(moment(measuredMonth), 'month')
    }));
  };

  return (
    <>
      <FormControl
        variant="outlined"
        margin="dense"
        className={classes.formControl}
        // error={!!error}
      >
        <InputLabel htmlFor="outlined-month-1" margin="dense">
          Measured MTH
        </InputLabel>

        <Select
          variant="outlined"
          label="measured mth"
          name="measured-mth"
          value={measuredMonth}
          onChange={handleMonthchange1}
          MenuProps={{
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left'
            },
            transformOrigin: {
              vertical: 'top',
              horizontal: 'left'
            },
            PaperProps: {
              style: {
                maxHeight: 500
                // marginTop: 120 // Creates space between select field and dropdown
              }
            },
            getContentAnchorEl: null, // Fixes dropdown position issue
            container: document.body // Ensures dropdown attaches to the body
          }}
        >
          {localStorage.getItem('realm') != 'demoag' &&
          localStorage.getItem('realm') != 'demoenterprise'
            ? localStorage.getItem('versionFlag') === 'TRUE'
              ? getLastThreeYears()
                  .filter(val => val !== closedDate)
                  .map(val => (
                    <MenuItem key={val} value={val}>
                      {moment(val).format('MMM-YY')}
                    </MenuItem>
                  ))
              : getLast13Months()
                  .filter(val => val !== closedDate)
                  .map(val => (
                    <MenuItem key={val} value={val}>
                      {moment(val).format('MMM-YY')}
                    </MenuItem>
                  ))
            : localStorage.getItem('versionFlag') === 'TRUE'
            ? getLastThreeYears()
                //.filter(val => val !== closedDate)
                .map(val => (
                  <MenuItem key={val} value={val}>
                    {moment(val).format('MMM-YY')}
                  </MenuItem>
                ))
            : getLast13Months()
                //.filter(val => val !== closedDate)
                .map(val => (
                  <MenuItem key={val} value={val}>
                    {moment(val).format('MMM-YY')}
                  </MenuItem>
                ))}
        </Select>
      </FormControl>

      <FormControl
        variant="outlined"
        margin="dense"
        className={classes.formControl}
        error={!!mnthErrMsg}
      >
        <InputLabel htmlFor="outlined-month-2" margin="dense">
          Prior MTH
        </InputLabel>
        <Select
          variant="outlined"
          label="prior mth"
          name="prior-mth"
          value={priorMonth}
          onChange={handleMonthchange2}
          MenuProps={{
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left'
            },
            transformOrigin: {
              vertical: 'top',
              horizontal: 'left'
            },
            PaperProps: {
              style: {
                maxHeight: 500
                // marginTop: 120 // Creates space between select field and dropdown
              }
            },
            getContentAnchorEl: null, // Fixes dropdown position issue
            container: document.body // Ensures dropdown attaches to the body
          }}
        >
          {localStorage.getItem('realm') != 'demoag' &&
          localStorage.getItem('realm') != 'demoenterprise'
            ? getValidMonths(true)
                .filter(({ value }) => value !== closedDate)
                .map(({ value, isValid }) => (
                  <MenuItem key={value} value={value} disabled={!isValid}>
                    {moment(value).format('MMM-YY')}
                  </MenuItem>
                ))
            : getValidMonths(true).map(({ value, isValid }) => (
                <MenuItem key={value} value={value} disabled={!isValid}>
                  {moment(value).format('MMM-YY')}
                </MenuItem>
              ))}
        </Select>
      </FormControl>
    </>
  );
};

export default MonthComparison;
