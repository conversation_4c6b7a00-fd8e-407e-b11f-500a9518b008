import { text } from '@fortawesome/fontawesome-svg-core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

/**
 * This function iterates over all of the columns to create a row of header cells
 */
export const usePDFExport = () => {
  // Row colors
  const formatToCustomDate = value => {
    return moment(value, 'YYYY-MM').format("MMM' YY");
  };
  function transformData(monthlyData) {
    var tableData = monthlyData.map((item, i) => {
      var content = [
        {
          text: [
            {
              text: item.kpi_name,
              fontSize: 8,
              color:
                item.kpi_name == 'Competitive' ||
                item.kpi_name == 'Maintenance' ||
                item.kpi_name == 'Repair'
                  ? '#fff'
                  : '#000',
              bold: true,
              alignment: 'right',
              marginTop: 6
            }
          ],
          fillColor:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? '#c65911'
              : '#fff'
        },
        ...['measured_mon', 'prior_mon', 'variance'].map(key => ({
          text:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? formatCategoryCellValue(item[key], item.symbol)
              : formatCellValue(item[key], item.symbol),

          border: [true, true, true, true],
          borderColor:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? key == 'variance'
                ? ['#c65911', '#000', '#000', '#000']
                : key == 'client_rptcard_1mon'
                ? ['#000', '#000', '#c65911', '#000']
                : ['#c65911', '#000', '#c65911', '#000']
              : ['#000', '#000', '#000', '#000'],
          alignment: 'right',
          fontSize: 8,
          color: key == 'variance' ? setVarianceColor(key, item) : '#000',
          fillColor:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? '#c65911'
              : '#fff'
        }))
      ];

      return content;
    });

    return tableData;
  }
  const setVarianceColor = (key, item) => {
    var color;
    if (key == 'variance') {
      if (item.variance > 0) {
        color = '#3c7d22';
      } else if (item.variance == 0) {
        color = '#000';
      } else {
        color = 'red';
      }
    }

    return color;
  };
  const setCategoryColor = item => {
    var color = '#c65911';

    return color;
  };
  const formatCellValue = (value, symbol) => {
    var formattedValue =
      value &&
      parseFloat(value)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    if (symbol == '%') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + '%)';
      } else {
        formattedValue = formattedValue + '%';
      }
    } else if (symbol == '$') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = '($' + cellValue + ')';
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + ')';
      } else {
        formattedValue = formattedValue;
      }
    }

    return formattedValue;
  };
  const formatCategoryCellValue = value => {
    return '';
  };

  const setHeaderSection = (colWidthSubHeader, monthlyData, page) => {
    return {
      style: 'subheaderCol',
      table: {
        layout: 'noBorders',
        widths: colWidthSubHeader,
        heights: [20],
        body: [
          [
            {
              margin: [0, 4, 0, 0],
              alignment: 'right',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#000', '#c65911', '#000', '#000'],
              text: 'KPIs',
              fontSize: 12,
              color: '#fff'
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#000', '#c65911', '#000', '#000'],
              text: formatToCustomDate(
                monthlyData && monthlyData[0]?.measured_mon
              ),
              fontSize: 12,
              color: '#fff'
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#000', '#c65911', '#000', '#000'],
              text: formatToCustomDate(
                monthlyData && monthlyData[0]?.prior_mon
              ),
              fontSize: 12,
              color: '#fff'
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#000', '#c65911', '#000', '#000'],
              text: 'Variance',
              fontSize: 12,
              color: '#fff'
            }
          ]
        ]
      },
      layout: {
        hLineWidth: function(i, node) {
          return i === 0 && page == 'second' ? 3 : 1;
        },
        vLineWidth: function(i, node) {
          return i === 0 || i === node.table.widths.length ? 3 : 1;
        },
        hLineColor: function(i, node) {
          return 'black';
        },
        vLineColor: function(i, node) {
          return 'black';
        }
      }
    };
  };
  function splitArrayAtIndex(arr, index) {
    return [arr.slice(0, index), arr.slice(index)];
  }

  async function downloadPDF(
    clientReportCardDetails,
    image,
    selectedWorkmixOptions,
    fileNameExport,
    selectedOptions
  ) {
    const imageDataUrl = image;
    const summaryData = clientReportCardDetails.summaryData || {};
    const monthlyData = clientReportCardDetails.monthlyData;
    var con = [];

    var colWidthTop = [130, 130, 170, 130, 170];

    var topbar = {
      style: 'summaryData',
      table: {
        layout: 'noBorders',

        widths: colWidthTop,
        heights: [30],
        body: [
          [
            {
              margin: [0, 8, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['', '', '', '#c65911'],
              text: [
                {
                  text:
                    'Monthly FOPC: ' +
                    formatCellValue(summaryData.monthly_fopc_fee, '$') +
                    '\n',
                  fontSize: 10,
                  color: '#fff'
                },
                {
                  text:
                    'Monthly DMS: ' +
                    formatCellValue(summaryData.monthly_dms_fee, '$') +
                    '\n',
                  fontSize: 10,
                  color: '#fff',
                  fillColor: '#c65911'
                }
              ],
              // rowSpan: 2,
              alignment: 'center',
              fillColor: '#c65911',
              color: '#fff',
              bold: true
            },

            {
              text: [
                selectedOptions.includes('monthlyPts')
                  ? {
                      text:
                        'ROI = ' +
                        formatCellValue(
                          summaryData.roi_parts_labor_gp || 0,
                          '%'
                        ),
                      fontSize: 13,
                      color: '#fff'

                      // lineHeight: 2
                    }
                  : null
              ],
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',

              borderColor: ['', '', '#3c7d22', '#3c7d22']
            },
            {
              text: [
                selectedOptions.includes('monthlyPts')
                  ? {
                      text: 'Based on Total Pts & Lbr GP Change of' + '\n',
                      fontSize: 7,
                      color: '#fff'
                    }
                  : null,
                selectedOptions.includes('monthlyPts')
                  ? {
                      text: formatCellValue(summaryData.if_parts_labor_gp, '$'),
                      fontSize: 10,

                      bold: true,
                      color: '#fff'
                    }
                  : null
              ],
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor:
                selectedOptions.includes('monthlyPts') &&
                selectedOptions.includes('monthlyElr')
                  ? ['#3c7d22', '', '', '#fff']
                  : ['#3c7d22', '', '', '#3c7d22']
            }
          ],
          [
            {
              margin: [0, 4, 0, 4],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['', '#c65911', '', ''],
              text: [
                {
                  text:
                    'Total: ' +
                    formatCellValue(
                      summaryData.total_monthly_fopc_dms_fee,
                      '$'
                    ),
                  fontSize: 10,
                  color: '#fff',
                  fillColor: '#c65911'
                }
              ],
              // rowSpan: 2,
              alignment: 'center',
              fillColor: '#c65911',
              color: '#fff',
              bold: true
            },

            {
              text: [
                selectedOptions.includes('monthlyElr')
                  ? {
                      text:
                        'ROI = ' +
                        formatCellValue(summaryData.roi_repair_elr || 0, '%'),
                      fontSize: 13,
                      color: '#fff'
                    }
                  : null
              ],
              margin: [0, 4, 0, 4],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor: ['', '', '#3c7d22', '']
            },
            {
              text: [
                selectedOptions.includes('monthlyElr')
                  ? {
                      text: 'Based on Repair ELR Change of' + '\n',
                      fontSize: 7,
                      color: '#fff'
                    }
                  : null,
                selectedOptions.includes('monthlyElr')
                  ? {
                      text:
                        formatCellValue(summaryData.if_repair_elr, '$') ||
                        0 + '\n',
                      fontSize: 10,
                      bold: true,
                      color: '#fff'
                    }
                  : null
              ],
              margin: [0, 4, 0, 4],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor: ['', '#3c7d22', '', '']
            }
          ]
        ]
      },
      layout: {
        hLineWidth: function(i, node) {
          return i === 0 || i === node.table.body.length ? 3 : 1;
        },
        vLineWidth: function(i, node) {
          return i === 0 || i === node.table.widths.length ? 3 : 3;
        },
        hLineColor: function(i, node) {
          return 'black';
        },
        vLineColor: function(i, node) {
          return 'black';
        }
      }
    };

    con.push(topbar);
    var colWidthTitle = [130, 313];
    var titlebar = {
      style: 'titleData',
      table: {
        layout: 'noBorders',
        // widths: [140, 60, 312],
        widths: colWidthTitle,
        heights: [20],
        body: [
          [
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#ffc000',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: 'Report Card',
                  fontSize: 13,
                  color: '#000'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#ffc000',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: localStorage.getItem('storeGroup'),
                  fontSize: 13,
                  color: '#000'

                  // background: '#c65911'
                }
              ]
            }
          ]
        ]
      },
      layout: {
        hLineWidth: function(i, node) {
          return i === node.table.body.length ? 3 : 1;
        },
        vLineWidth: function(i, node) {
          return i === 0 || i === node.table.widths.length ? 3 : 1;
        },
        hLineColor: function(i, node) {
          return 'black';
        },
        vLineColor: function(i, node) {
          return 'black';
        }
      }
    };
    con.push(titlebar);
    var colWidthSubHeader = [130, 100, 100, 95];
    var subheaderCol = setHeaderSection(
      colWidthSubHeader,
      monthlyData,
      'first'
    );
    con.push(subheaderCol);

    var tableData = clientReportCardDetails.monthlyData;
    var slicedTableData = tableData && tableData.slice(1);
    var filterdArr;
    if (selectedWorkmixOptions.length == 0) {
      filterdArr = slicedTableData.filter(function(el) {
        return el.kpi_id == 'A';
      });
    } else {
      if (
        selectedWorkmixOptions.includes('Competitive') &&
        selectedWorkmixOptions.includes('Maintenance') &&
        selectedWorkmixOptions.includes('Repair')
      ) {
        filterdArr = tableData ? tableData.slice(1) : [];
      } else if (
        selectedWorkmixOptions.includes('Competitive') &&
        selectedWorkmixOptions.includes('Maintenance')
      ) {
        filterdArr = slicedTableData.filter(
          el => el.kpi_id === 'A' || el.kpi_id === 'B' || el.kpi_id === 'C'
        );
      } else if (
        selectedWorkmixOptions.includes('Competitive') &&
        selectedWorkmixOptions.includes('Repair')
      ) {
        filterdArr = slicedTableData.filter(
          el => el.kpi_id === 'A' || el.kpi_id === 'B' || el.kpi_id === 'D'
        );
      } else if (
        selectedWorkmixOptions.includes('Maintenance') &&
        selectedWorkmixOptions.includes('Repair')
      ) {
        filterdArr = slicedTableData.filter(
          el => el.kpi_id === 'A' || el.kpi_id === 'C' || el.kpi_id === 'D'
        );
      } else if (selectedWorkmixOptions.includes('Competitive')) {
        filterdArr = slicedTableData.filter(function(el) {
          return el.kpi_id == 'A' || el.kpi_id == 'B';
        });
      } else if (selectedWorkmixOptions.includes('Maintenance')) {
        filterdArr = slicedTableData.filter(function(el) {
          return el.kpi_id == 'A' || el.kpi_id == 'C';
        });
      } else if (selectedWorkmixOptions.includes('Repair')) {
        filterdArr = slicedTableData.filter(function(el) {
          return el.kpi_id == 'A' || el.kpi_id == 'D';
        });
      }
    }

    var elem = transformData(filterdArr);

    var tableRow;
    if (selectedWorkmixOptions.length > 1) {
      var body = {
        style: 'headerCol',
        table: {
          layout: 'noBorders',
          // widths: [140, 60, 312],
          widths: colWidthSubHeader,
          //heights: [20],
          body: elem
        },
        layout: {
          hLineWidth: function(i, node) {
            return i === 0 || i === node.table.body.length ? 3 : 1;
          },
          vLineWidth: function(i, node) {
            return i === 0 || i === node.table.widths.length ? 3 : 1;
          },
          hLineColor: function(i, node) {
            return i != 9 ? 'black' : 'linear - gradient(white, lightblue)';
          },
          vLineColor: function(i, node) {
            return 'black';
          }
        }
      };
      con.push(body);
    } else {
      var tableRow = {
        style: 'headerCol',
        table: {
          layout: 'noBorders',
          // widths: [140, 60, 312],
          widths: colWidthSubHeader,
          //heights: [20],
          body: elem
        },
        layout: {
          hLineWidth: function(i, node) {
            return i === 0 || i === node.table.body.length ? 3 : 1;
          },
          vLineWidth: function(i, node) {
            return i === 0 || i === node.table.widths.length ? 3 : 1;
          },
          hLineColor: function(i, node) {
            return i != 9 ? 'black' : 'linear - gradient(white, lightblue)';
          },
          vLineColor: function(i, node) {
            return 'black';
          }
        }
      };

      con.push(tableRow);
    }

    var options = {
      pageOrientation: 'portrait', // can also be 'portrait'

      content: con,
      //pageSize: 'A4',
      // pageMargins: [30, 45, 30, 20],
      pageMargins: [60, 45, 30, 20],
      styles: {
        subheader: {
          margin: [0, 0, 0, 0]
        },
        divButton: {
          background: '#e9e9ed'
        }
      }
    };

    pdfMake.createPdf(options).download(fileNameExport);
  }

  const exportToPDF = (
    clientReportCardDetails,
    image,
    selectedWorkmixOptions,
    fileNameExport,
    selectedOptions
  ) => {
    downloadPDF(
      clientReportCardDetails,
      image,
      selectedWorkmixOptions,
      fileNameExport,
      selectedOptions
    );
  };

  return {
    exportToPDF
  };
};
