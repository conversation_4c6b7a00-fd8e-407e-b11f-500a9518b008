import React from 'react';
import clsx from 'clsx';
import './ThreeMonthReportTable.css';
import moment from 'moment';

const ThreeMonthReportTable = ({
  clientReportCardDetails,
  selectedOptions,
  selectedWorkmixOptions
}) => {
  const summaryData = clientReportCardDetails.summaryData || {};
  const monthlyData = clientReportCardDetails.monthlyData;
  const formatToCustomDate = value => {
    // Check if the value is in the format YYYY-MM (e.g., 2023-09)
    ///if (/^\d{4}-\d{2}$/.test(value)) {

    return moment(value, 'YYYY-MM').format("MMM' YY");
    // }
  };
  const formatCellValue = (value, symbol) => {
    var formattedValue =
      value &&
      parseFloat(value)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    if (symbol == '%') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + '%)';
      } else {
        formattedValue = formattedValue + '%';
      }
    } else if (symbol == '$') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = '($' + cellValue + ')';
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + ')';
      } else {
        formattedValue = formattedValue;
      }
    }

    return formattedValue;
  };

  const getVarianceClass = variance => {
    if (variance > 0) return ' green-text';
    if (variance == 0) return 'off-white';
    return 'red-text';
  };
  const getHeaderClass = value => {
    if (value < 0) return 'dark-red-text';

    return '';
  };
  const renderMonthlyDataRow = (data, index) => {
    const highlightNames = ['Competitive', 'Maintenance', 'Repair'];
    const isHighlighted = highlightNames.includes(data.kpi_name);

    // Initially hide rows with kpi_category 'Competitive', 'Maintenance', 'Repair'
    let showRow = true;
    // Check if the row's kpi_category should be hidden initially
    if (
      data.kpi_category === 'Competitive' ||
      data.kpi_category === 'Maintenance' ||
      data.kpi_category === 'Repair'
    ) {
      showRow = false;
    }

    // If the corresponding selectedWorkmixOptions is checked, show the row with that kpi_id
    if (
      selectedWorkmixOptions.includes('Competitive') &&
      data.kpi_category === 'Competitive'
    ) {
      showRow = true;
    } else if (
      selectedWorkmixOptions.includes('Maintenance') &&
      data.kpi_category === 'Maintenance'
    ) {
      showRow = true;
    } else if (
      selectedWorkmixOptions.includes('Repair') &&
      data.kpi_category === 'Repair'
    ) {
      showRow = true;
    }

    // If the row should not be shown, return null
    if (!showRow) return null;

    const isGpPercent =
      data.kpi_name === 'Total Parts GP%' && data.kpi_category != 'Total';
    const isTotalPartsGp =
      data.kpi_name === 'Total Parts GP' && data.kpi_category == 'Total';
    const laborAndPartsGP =
      data.kpi_name === 'Lbr & Pts GP' && data.kpi_category != 'Total';
    const totalLaborAndPartsGP =
      data.kpi_name === 'Total Lbr & Pts GP ' && data.kpi_category == 'Total';
    // Render the highlighted or normal row
    const row = isHighlighted ? (
      <tr key={index} className="orange-sub-header orange-bg">
        <td
          width="20%"
          className="text-right no-border rocount-title crm data-col-1"
        >
          {data && data.kpi_name}
        </td>
        <td className="text-center no-border rocount-title" colSpan="6"></td>
      </tr>
    ) : (
      <tr key={index}>
        <td
          class={
            isTotalPartsGp ||
            isGpPercent ||
            laborAndPartsGP ||
            totalLaborAndPartsGP
              ? 'text-right no-border rocount-title rocount-bborder data-col-1'
              : 'text-right no-border rocount-title'
          }
        >
          <span
            className={
              isTotalPartsGp ||
              isGpPercent ||
              laborAndPartsGP ||
              totalLaborAndPartsGP
                ? 'noborder-title'
                : 'test'
            }
          >
            {data && data.kpi_name}
          </span>
        </td>
        <td class="off-white data-col-2">
          {data && formatCellValue(data.three_mon_avg, data.kpi_symbol)}
        </td>

        <td class="off-white data-col-3">
          {data && formatCellValue(data.last_month, data.kpi_symbol)}
        </td>
        <td
          className={clsx(
            'blue-bg thick-border-r data-col-4',
            getVarianceClass(data.prior_variance)
          )}
        >
          {data && formatCellValue(data.prior_variance, data.kpi_symbol)}
        </td>
        <td class="off-white data-col-5">
          {data && formatCellValue(data.prior_annual_pace, data.kpi_symbol)}
        </td>
        <td class="off-white data-col-6">
          {data && formatCellValue(data.current_annual_pace, data.kpi_symbol)}
        </td>
        <td
          className={clsx(
            'blue-bg rborder-zero data-col-7',
            getVarianceClass(data.annual_variance)
          )}
        >
          {data && formatCellValue(data.annual_variance, data.kpi_symbol)}
        </td>
      </tr>
    );

    // Add a separation row after GP% or Total Parts GP%
    return (
      <>
        {row}
        {(isGpPercent || isTotalPartsGp) && (
          <tr class="separation">
            {/* {isGpPercent ? <td class=""></td> : <td class="grey-bg"></td>} */}
            <td class="grey-bg"></td>
            <td class="grey-bg"></td>
            <td class="grey-bg"></td>
            <td class="grey-bg"></td>
            <td class="grey-bg"></td>
            <td class="grey-bg"></td>
            <td class="grey-bg"></td>
          </tr>
        )}
      </>
    );
  };

  const renderTableHeader = () => (
    <tr class="no-border">
      <td colspan="1" class="header orange-bg text-center report-card">
        <span class="">
          Monthly FOPC: {formatCellValue(summaryData.monthly_fopc || 0, '$')}
        </span>
        <span class="">
          Monthly DMS: {formatCellValue(summaryData.monthly_dms || 0, '$')}
        </span>

        <span>
          Total: {formatCellValue(summaryData.total_monthly || 0, '$')}
        </span>
      </td>
      <td colspan="1" class="header green-bg text-center">
        <span
          style={{
            display: selectedOptions.includes('monthlyPts') ? 'block' : 'none'
          }}
          class="roi"
        >
          ROI ={' '}
          {/* <span
            className={getHeaderClass(summaryData.roi_prior_lbr_parts || 0)}
            style={{ display: 'inline-block' }}
          > */}
          {formatCellValue(summaryData.roi_prior_lbr_parts || 0, '%')}
          {/* </span> */}
        </span>
        <span
          style={{
            display:
              selectedOptions.includes('monthlyElr') ||
              selectedOptions.includes('monthlyPts')
                ? 'block'
                : 'none'
          }}
        >
          <button>Monthly</button>
        </span>
        <span
          style={{
            display: selectedOptions.includes('monthlyElr') ? 'block' : 'none'
          }}
          class="roi"
        >
          ROI ={' '}
          {/* <span
            className={getHeaderClass(summaryData.roi_prior_repair_elr || 0)}
            style={{ display: 'inline-block' }}
          > */}
          {formatCellValue(summaryData.roi_prior_repair_elr || 0, '%')}
          {/* </span> */}
        </span>
      </td>

      <td colspan="2" class="header green-bg text-center thick-border-r">
        <div class="based-text-main">
          <div
            style={{
              display: selectedOptions.includes('monthlyPts') ? 'block' : 'none'
            }}
            // class="whiteline based-text"
            className={`based-text ${
              selectedOptions.includes('monthlyElr') &&
              selectedOptions.includes('monthlyPts')
                ? 'whiteline'
                : ''
            }`}
          >
            Based on Total Pts & Lbr GP Change of
            {/* <span>${summaryData.if_prior_parts_labor_gp || 0}</span> */}
            <span>
              {formatCellValue(summaryData.if_prior_parts_labor_gp || 0, '$')}
            </span>
          </div>
          <div
            style={{
              display: selectedOptions.includes('monthlyElr') ? 'block' : 'none'
            }}
            class="based-text"
          >
            Based on Repair ELR Change of{' '}
            {/* <span>${summaryData.if_prior_repair_elr || 0}</span> */}
            <span>
              {formatCellValue(summaryData.if_prior_repair_elr || 0, '$')}
            </span>
          </div>
        </div>
      </td>

      <td colspan="1" class="header green-bg text-center">
        <span
          style={{
            display: selectedOptions.includes('annualizedPts')
              ? 'block'
              : 'none'
          }}
          class="roi"
        >
          ROI ={' '}
          {/* <span
            className={getHeaderClass(summaryData.roi_annual_lbr_parts || 0)}
            style={{ display: 'inline-block' }}
          > */}
          {formatCellValue(summaryData.roi_annual_lbr_parts || 0, '%')}
          {/* </span> */}
        </span>
        <span
          style={{
            display:
              selectedOptions.includes('annualizedPts') ||
              selectedOptions.includes('annualizedElr')
                ? 'block'
                : 'none'
          }}
        >
          <button>3 MTH Annualized</button>
        </span>
        <span
          style={{
            display: selectedOptions.includes('annualizedElr')
              ? 'block'
              : 'none'
          }}
          class="roi"
        >
          ROI ={' '}
          {/* <span
            className={getHeaderClass(summaryData.roi_annual_repair_elr || 0)}
            style={{ display: 'inline-block' }}
          > */}
          {formatCellValue(summaryData.roi_annual_repair_elr || 0, '%')}
          {/* </span> */}
        </span>
      </td>
      <td colspan="2" class="header green-bg text-center">
        <div class="based-text-main">
          <div
            style={{
              display: selectedOptions.includes('annualizedPts')
                ? 'block'
                : 'none'
            }}
            // class="whiteline based-text"
            className={`based-text ${
              selectedOptions.includes('annualizedPts') &&
              selectedOptions.includes('annualizedElr')
                ? 'whiteline'
                : ''
            }`}
          >
            Based on Total Pts & Lbr GP Change of{' '}
            <span
            // className={getHeaderClass(
            //   summaryData.if_annual_parts_labor_gp || 0
            // )}
            >
              {formatCellValue(summaryData.if_annual_parts_labor_gp || 0, '$')}
            </span>
          </div>
          <div
            style={{
              display: selectedOptions.includes('annualizedElr')
                ? 'block'
                : 'none'
            }}
            class="based-text"
          >
            Based on Repair ELR Change of{' '}
            <span
            // className={getHeaderClass(summaryData.if_annual_repair_elr || 0)}
            >
              {formatCellValue(summaryData.if_annual_repair_elr || 0, '$')}
            </span>
          </div>
        </div>
      </td>
    </tr>
  );

  const renderTableHeaderTitle = () => (
    <>
      <tr class="title-border">
        <th
          colspan="1"
          class="main-header yellow-bg-title lborder-zero main-title"
        >
          Report Card
        </th>
        <th
          colspan="6"
          class="main-header yellow-bg-title rborder-zero rborder-zero main-title"
        >
          {localStorage.getItem('storeGroup')}
        </th>
      </tr>
      <tr class="orange-main-title orange-bg">
        <td class="text-right lborder-zero"></td>
        <td class="text-center">
          {moment(summaryData.prior_to_fopc_start_date).format('MM/YY')} -{' '}
          {moment(summaryData.prior_to_fopc_end_date).format('MM/YY')}
          <span class="">Prior to FOPC</span>
        </td>
        <td colspan="2" class="text-center thick-border-r"></td>
        <td class="text-center">Baseline Annualized</td>
        <td class="text-center rborder-zero">
          {moment(summaryData.used_to_annul_cur_3_mon_start_date).format(
            'MM/YY'
          )}{' '}
          -{' '}
          {moment(summaryData.used_to_annul_cur_3_mon_end_date).format('MM/YY')}
          <span class="">Last 3 MTHs - Annualized</span>
        </td>
        <td class="text-center lborder-zero rborder-zero"></td>
      </tr>
      <tr class="orange-sub-title orange-bg">
        <td width="20%" class="text-right lborder-zero kpi-title">
          KPIs
        </td>
        <td width="15%">
          3 MTH Avg <span class="baseline-small">(Baseline)</span>
        </td>
        <td width="15%">
          {formatToCustomDate(summaryData.used_to_annul_cur_3_mon_end_date)}
        </td>
        <td width="10%" class="thick-border-r">
          Variance
        </td>
        <td width="15%">Prior Annual Pace</td>
        <td width="15%">Annual Pace</td>
        <td width="10%" class="rborder-zero">
          Variance
        </td>
      </tr>
    </>
  );

  return (
    <div id={'three-month-report-card'}>
      <title>3 Month Report</title>
      <div class="client-report-outer">
        <table className="client-report-second datatable">
          <thead class="header-section">
            {renderTableHeader()}
            {renderTableHeaderTitle()}
          </thead>
          <tbody>{monthlyData && monthlyData.map(renderMonthlyDataRow)}</tbody>
        </table>
      </div>
    </div>
  );
};

export default ThreeMonthReportTable;
