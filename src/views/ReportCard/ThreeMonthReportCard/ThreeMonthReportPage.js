import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect
} from 'react';

import Link from '@material-ui/core/Link';
import { green } from '@mui/material/colors';
import { withKeycloak } from '@react-keycloak/web';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import SaveIcon from '@material-ui/icons/Save';
import RestoreIcon from '@material-ui/icons/Restore';
import moment from 'moment';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import Alert from '@material-ui/lab/Alert';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import $ from 'jquery';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Divider,
  FormControl,
  Grid,
  Button,
  TextField,
  Radio,
  RadioGroup,
  InputLabel,
  Select,
  CircularProgress,
  Snackbar,
  IconButton,
  Backdrop
} from '@material-ui/core';
import CloseIcon from '@material-ui/icons/Close';
import { ReactSession } from 'react-client-session';
import {
  setKpiToggle,
  setInternalKpiToggle,
  setKpiHomeToggle,
  setKpiToggleStartDate,
  setKpiToggleEndDate
} from 'src/actions';
import {
  getKpiToggleOptionsWithTimeZone,
  getLatestClosedDate,
  insertKPIMonthCardReportName,
  getEmail,
  getStoreNickName,
  getMonthlyClientReportCardDetails,
  getAllStoreNames,
  getThreeMonthClientReportCardDetails,
  getOneMonthReportMonths
} from 'src/utils/hasuraServices';
import WorkmixView from 'src/components/WorkmixView';
import clsx from 'clsx';
import { useHistory } from 'react-router';
import { makeStyles, withStyles } from '@material-ui/styles';

import MailIcon from '@material-ui/icons/Mail';

import {
  getComparisonMonths,
  getLast13Months,
  getLastThreeYears,
  getTimeZone,
  getYearValue
} from 'src/utils/Utils';
import { useDispatch, useSelector } from 'react-redux';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import ThreeMonthReportTable from './ThreeMonthReportTable';
import IncludedStores from 'src/components/IncludedStores';
import ROIView from 'src/components/ROIView';
import {
  threeMonthRoiOption,
  threeMonthRoiSelectedOptions
} from 'src/utils/constants';
import MonthSelected from './MonthSelected';
import Page from 'src/components/Page';
import SaveReportDialog from 'src/components/SaveReportDialog';
import { usePDFExport } from './printToPDF';
// import { usePDFExport } from './PrintDoc';
// import { useExcelExport } from './PrintExcel';
import SuccessSnackbar from '../SuccessSnackbar';
import EmailDialogKpi from 'src/components/EmailDialogKpi';
import { useExcelExport } from 'src/components/PrintExcelThreeMonth';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',

    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',

    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  linkItem: {
    cursor: 'pointer'
  },
  linkItemDisable: {
    pointerEvents: 'none',
    color: 'grey'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  }
}));
const ThreeMonthReportPage = props => {
  const session = useSelector(state => state.session);
  const { exportToPDF } = usePDFExport();
  // let isDisabled =
  //   createdBy != localStorage.getItem('userID') &&
  //   props.keycloak.realmAccess.roles.includes('superadmin');

  let kpiDataToggle =
    localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';
  let toggle =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.toggleOptions
      ? props.history.location.state.toggleOptions
      : props.history &&
        props.history.location &&
        (props.history.location.state == undefined ||
          props.history.location.state == null)
      ? kpiDataToggle
      : props.selectedToggle
      ? props.selectedToggle
      : kpiDataToggle;

  let created_by =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.created_by
      ? props.history.location.state.created_by
      : '';
  let isDisabled =
    created_by != localStorage.getItem('userID') &&
    props.keycloak.realmAccess.roles.includes('superadmin');
  let monthsArray = [];
  if (localStorage.getItem('versionFlag') === 'TRUE') {
    monthsArray = getLastThreeYears();
  } else {
    monthsArray = getLast13Months();
  }
  let measuredMTH =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.previousmonthyear
      ? props.history.location.state.previousmonthyear
      : getComparisonMonths()[0] || monthsArray[monthsArray.length - 1];
  const { exportToExcel, tryExcelData } = useExcelExport();
  const pathaname = props?.kpiReportData?.isFrom;
  const classes = useStyles();
  const dispatch = useDispatch();
  const [image, setImage] = useState();
  const [imageAnnual, setImageAnnual] = useState();
  const [toggleOption, setToggleOption] = useState(toggle);
  const [dates, setDates] = useState([]);
  const [filterStart, setFilterStart] = useState();
  const [filterEnd, setFilterEnd] = useState();
  const [filterChanged, setFilterChanged] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingIconExcel, setLoadingIconExcel] = useState(false);
  const [successExcel, setSuccessExcel] = useState(false);
  const [createdBy, setCreatedBy] = useState(created_by);
  const [loadingIconPDF, setLoadingIconPDF] = useState(false);
  const [closedDate, setClosedDate] = useState('');
  const [clientReportCardDetails, setClientReportCardDetails] = useState([]);
  const [allStoreNames, setAllStoreNames] = useState([]);
  const defaultStoreIds = JSON.parse(localStorage.getItem('selectedStoreId'));
  const [selectedStoreIds, setSelectedStoreIds] = useState(defaultStoreIds);
  const [isStoreLoading, setIsStoreLoading] = useState(false);
  const [isFirstRender, setIsFirstRender] = useState(true);
  const [isEmpty, setIsEmpty] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [goalFail, setGoalFail] = useState('');
  const [selectedOptions, setSelectedOptions] = useState();
  const [mon1, setMon1] = useState(measuredMTH);
  const permittedStoreIds = JSON.parse(
    localStorage.getItem('allPermittedStores')
  );
  // const [mon1, setMon1] = useState();
  // const [mon1, setMon1] = useState();
  const [mon2, setMon2] = useState();
  const [dateRange, setDateRange] = useState();
  const [selectedWorkmixOptions, setSelectedWorkmixOptions] = useState([]);
  // let fileName = 'Client Report Card - 3 Month Total';

  // save report

  const [openSaveDlg, setOpenSaveDlg] = useState(false);
  const [parent, setParent] = useState(); //Todo: Need to set it from Props.
  const [copyFile, setCopyFile] = useState(false);
  const [copy, setCopy] = useState(false);
  const [reportName, setReportName] = useState('');
  const [errorReport, setErrorReport] = useState('');
  const [requiredText, setRequiredText] = useState(false);
  const [selectedType, setSelectedType] = useState();
  const [filterText, setFilterText] = useState('');
  const [reportNameCopy, setReportNameCopy] = useState('');
  const [openSusSnackbar, setopenSusSnackbar] = useState(false);
  const [openDialogue, setOpenDialogue] = useState(false);
  const [mailUsers, setMailUsers] = useState([]);
  const [openDilogAll, setOpenDilogAll] = useState(false);
  const iKpiReportType = 'Client_Report_Card_3_Month_Total';
  const storeGroup = localStorage.getItem('storeGroup');
  let selectStoreDetails =
    selectedStoreIds &&
    selectedStoreIds.length > 0 &&
    allStoreNames.filter(store => selectedStoreIds[0] == store.storeId);
  let storeName = '';
  if (selectStoreDetails?.[0]?.storeNickname) {
    storeName = selectStoreDetails[0].storeNickname;
  } else {
    storeName = selectStoreDetails?.[0]?.storeName;
  }
  let fileName = useMemo(() => {
    if (pathaname === 'ThreeMonthReport') {
      return reportNameCopy && parent == 'savedReports'
        ? `Client Report Card - 3 Month Total - Saved Reports - ${reportNameCopy}`
        : reportName && parent == 'savedReports'
        ? `Client Report Card - 3 Month Total - Saved Reports - ${reportName}`
        : 'Client Report Card - 3 Month Total';
    }
    return '';
  }, [pathaname, reportName, reportNameCopy]);
  useEffect(() => {
    if (props.kpiReportData) {
      setSelectedStoreIds(props.kpiReportData.selectedStoreIds);
      // setFilterStart(props.kpiReportData.startDate);
      // setFilterEnd(props.kpiReportData.endDate);
      setSelectedOptions(props.kpiReportData.selectedRoiView);
      setSelectedWorkmixOptions(props.kpiReportData.workmixview);
      setParent(props.kpiReportData.parent);
      setReportName(props.kpiReportData.reportName);
      setSelectedType(props.kpiReportData.visibility);
    }
  }, [props.kpiReportData]);
  let fileNameExport = useMemo(() => {
    if (selectedStoreIds && selectedStoreIds.length > 1) {
      return reportNameCopy
        ? `Client Report Card - 3 Month Total - ${storeGroup} - ${reportNameCopy}`
        : reportName
        ? `Client Report Card - 3 Month Total - ${storeGroup} - ${reportName}`
        : `Client Report Card - 3 Month Total - ${storeGroup}`;
    } else {
      return reportNameCopy
        ? `Client Report Card - 3 Month Total -  ${storeGroup} - ${storeName} - ${reportNameCopy}`
        : reportName
        ? `Client Report Card - 3 Month Total -  ${storeGroup} - ${storeName} - ${reportName}`
        : `Client Report Card - 3 Month Total -  ${storeGroup} - ${storeName}`;
    }
    return '';
  }, [
    pathaname,
    reportName,
    reportNameCopy,
    selectStoreDetails,
    selectedStoreIds
  ]);
  const handleCancelSaveReport = () => {
    setOpenSaveDlg(false);
    setSelectedType(props.kpiReportData.visibility);
    setCopy(false);
    setRequiredText(false);
    setCopyFile(false);
    setErrorReport('');
    if (parent !== 'savedReports') {
      setReportName('');
    }
  };

  const handleOkSaveReport = () => {
    var iStoreId =
      props &&
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.store_id
        ? props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');

    if (parent === 'savedReports') {
      insertKPIMonthCardReportName(
        'update',
        iStoreId,
        reportName.trim(),
        '',
        filterStart,
        filterEnd,
        mon1,
        null,
        userId,
        iKpiReportType,
        null,
        selectedType,
        userRole,
        selectedWorkmixOptions,
        selectedStoreIds,
        selectedOptions,

        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
              .results[0].status == 1
          ) {
            setErrorReport('');
            // setReportName('');
            setRequiredText(false);
            setOpenSaveDlg(false);
            setCopy(false);
            setopenSusSnackbar(true);
          } else {
            setRequiredText(true);
            setErrorReport(
              result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
                .results[0].msg
            );
          }
        }
      );
    } else {
      insertKPIMonthCardReportName(
        'insert',
        iStoreId,
        reportName.trim(),
        '',
        filterStart,
        filterEnd,
        mon1,
        null,
        userId,
        iKpiReportType,
        null,
        selectedType,
        userRole,
        selectedWorkmixOptions,
        selectedStoreIds,
        selectedOptions,

        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
              .results[0].status == 1
          ) {
            setErrorReport('');
            setReportName('');
            setRequiredText(false);
            setOpenSaveDlg(false);
            setCopy(false);
            setopenSusSnackbar(true);
          } else {
            setRequiredText(true);
            setErrorReport(
              result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
                .results[0].msg
            );
          }
        }
      );
    }
    // setOpenSaveDlg(false);
  };

  const handleSaveAsReport = () => {
    var iStoreId =
      props &&
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.store_id
        ? props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const userRole = props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    var iKpiReportType = 'Client_Report_Card_3_Month_Total';
    insertKPIMonthCardReportName(
      'insert',
      iStoreId,
      reportName.trim(),
      '',
      filterStart,
      filterEnd,
      mon1,
      null,
      userId,
      iKpiReportType,
      null,
      selectedType,
      userRole,
      selectedWorkmixOptions,
      selectedStoreIds,
      selectedOptions,

      result => {
        if (
          result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
            .results[0].status == 1
        ) {
          setErrorReport('');
          // setReportName('');
          setRequiredText(false);
          setOpenSaveDlg(false);
          setCopy(false);
          setopenSusSnackbar(true);
        } else {
          setRequiredText(true);
          setErrorReport(
            result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
              .results[0].msg
          );
        }
      }
    );
    // setOpenSaveDlg(false);
  };
  const CancelDilogAll = () => {
    setOpenDilogAll(false);
  };
  const handleCheckboxChange = event => {
    setSelectedType(event.target.value);
  };

  const onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;
    if (!nameRegex.test(e.target.value) && e.target.value) {
      setRequiredText('');
    } else {
      setErrorReport('');
      setReportName(e.target.value);

      setRequiredText(false);
    }
  };
  const handleSaveReport = () => {
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      parent != 'savedReports'
    ) {
      setOpenDilogAll(true);
    } else {
      setOpenSaveDlg(true);
      if (parent == 'savedReports') {
        setReportName(props.kpiReportData.reportName);
      }
    }
  };

  //enf of save report
  useEffect(() => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);
      }
    });
    var storeid;
    if (defaultStoreIds.length == permittedStoreIds.length) {
      storeid = 'All';
    } else {
      storeid = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    }
    getOneMonthReportMonths(storeid, result => {
      var monthData =
        result.data.statelessCcPhysicalRwGetClientreportOneMonthDefaultDate
          .nodes;
      var lastMonthdata = monthData.filter(function(el) {
        return el.keyName == 'last_month';
      });

      let realm = localStorage.getItem('realm');
      var priMon =
        props &&
        props.history &&
        props.history.location &&
        props.history.location.state &&
        props.history.location.state.previousmonthyear;
      if (realm == 'demoag' || realm == 'demoenterprise') {
        if (
          typeof lastMonthdata[0].keyValue != 'undefined' &&
          props.kpiReportData &&
          props.kpiReportData.parent != 'savedReports'
        ) {
          setMon1(lastMonthdata[0].keyValue);
          setMon2(lastMonthdata[0].keyValue);
        } else {
          setMon1(priMon);
          setMon2(priMon);
        }
      } else {
        if (
          props.kpiReportData &&
          props.kpiReportData.parent != 'savedReports'
        ) {
          var cDate = localStorage.getItem('closedDate');
          var mMth = moment(cDate)
            .subtract(1, 'months')
            .format('YYYY-MM');

          setMon1(mMth);
          setMon2(mMth);
        } else {
          setMon1(priMon);
          setMon2(priMon);
        }
      }

      //setIsMonthLoading(false);
    });
  }, [props.kpiReportData]);

  const linkStyle = useMemo(() => {
    return {
      pointerEvents:
        createdBy !== '' && createdBy === localStorage.getItem('userID')
          ? 'auto'
          : createdBy !== '' &&
            createdBy !== localStorage.getItem('userID') &&
            !props.keycloak.realmAccess.roles.includes('superadmin')
          ? 'none'
          : 'auto',
      color:
        createdBy !== '' && createdBy === localStorage.getItem('userID')
          ? '#003d6b'
          : createdBy !== '' &&
            createdBy !== localStorage.getItem('userID') &&
            !props.keycloak.realmAccess.roles.includes('superadmin')
          ? 'grey'
          : '#003d6b'
    };
  }, [isDisabled, createdBy, session.storeSelected]);

  useEffect(() => {
    getLatestClosedDate(result => {
      if (result) {
        var openDate = '';
        var Date1 = result[0].value;
        setClosedDate(Date1);
        var launchDate;
        let realm = localStorage.getItem('realm');
        if (realm == 'demoag' || realm == 'demoenterprise') {
          launchDate = localStorage.getItem('launchDate');
        } else {
          launchDate = localStorage.getItem('closedDate');
        }

        const ProcessedDate = getStartAndEndDate(launchDate);

        setDateRange(ProcessedDate);
        if (
          props.history &&
          props.history.location &&
          props.history.location.state &&
          props.history.location.state.parent == 'savedReports'
        ) {
          setFilterStart(props.history.location.state.startdate);
          setFilterEnd(props.history.location.state.enddate);
        } else {
          setFilterStart(ProcessedDate.startDate);
          setFilterEnd(ProcessedDate.endDate);
        }
        localStorage.setItem('closedDate', Date1);
        setIsPageLoading(true);
        setFilterChanged(true);
        setIsStoreLoading(false);
      }
    });
  }, [session.serviceAdvisor]);

  function getStartAndEndDate(closedDate) {
    const date = moment(closedDate, 'YYYY-MM-DD');
    const isMonthEnd = date.isSame(date.clone().endOf('month'), 'day');
    // let startDate, endDate;

    // if (isMonthEnd) {
    //   // For last day of the month
    //   startDate = date
    //     .clone()
    //     .startOf('month')
    //     .subtract(3, 'months');
    //   endDate = date
    //     .clone()
    //     .subtract(1, 'months')
    //     .endOf('month'); // Ensure it's the actual end of the month
    // } else {
    //   // For any other day
    //   startDate = date
    //     .clone()
    //     .startOf('month')
    //     .subtract(3, 'months');
    //   endDate = date
    //     .clone()
    //     .subtract(1, 'months')
    //     .endOf('month'); // Ensure it's the actual end of the month
    // }

    // Calculate the start date as September 1, three months prior to the given date's month
    let startDate;
    let endDate;
    let realm = localStorage.getItem('realm');
    if (realm == 'demoag' || realm == 'demoenterprise') {
      startDate = date
        .clone()
        .startOf('month')
        .subtract(3, 'months');

      // Calculate the end date as the last day of November, two months prior to the given date's month
      endDate = date
        .clone()
        .startOf('month')
        .subtract(1, 'months')
        .endOf('month');
    } else {
      startDate = date
        .clone()
        .startOf('month')
        .subtract(4, 'months');

      // Calculate the end date as the last day of November, two months prior to the given date's month
      endDate = date
        .clone()
        .startOf('month')
        .subtract(2, 'months')
        .endOf('month');
    }
    return {
      startDate: startDate.format('YYYY-MM-DD'),
      endDate: endDate.format('YYYY-MM-DD')
    };
  }

  useEffect(() => {
    if (
      session.storeSelected != '' &&
      session.storeSelected != selectedStoreIds
    ) {
      setSelectedStoreIds(JSON.parse(session.storeSelected));
      setIsStoreLoading(true);
      setParent('');
      setReportName('');
      setReportNameCopy('');
      setSelectedStoreIds(defaultStoreIds);
      // setFilterStart(dateRange.startDate);
      // setFilterEnd(dateRange.endDate);
      setMon1(
        mon2 || getComparisonMonths()[0] || monthsArray[monthsArray.length - 1]
      );
      setSelectedWorkmixOptions([]);
      setSelectedOptions(threeMonthRoiSelectedOptions);
    }
  }, [session.storeSelected]);

  useEffect(() => {
    if (
      filterStart !== undefined &&
      filterEnd !== undefined &&
      filterStart !== '' &&
      filterEnd !== ''
    ) {
      const fetchData = async () => {
        try {
          setIsEmpty(false);
          if (isFirstRender) {
            setIsStoreLoading(true);
            // First render: Call both functions
            const [
              ThreeMonthlClientReportResult,
              allStoreNamesResult
            ] = await Promise.all([
              fetchThreeMonthClientReportCardDetails(),
              getAllStoreName()
            ]);
            if (ThreeMonthlClientReportResult.error) {
              setIsEmpty(true);
            } else {
              setClientReportCardDetails(ThreeMonthlClientReportResult);
            }
            // Set client report card details
            // setClientReportCardDetails(ThreeMonthlClientReportResult);

            // Extract store names and IDs
            const storeNames = allStoreNamesResult.map(item => ({
              storeName: item.storeName,
              storeId: item.storeId,
              storeNickname: item.storeNickname
            }));

            setAllStoreNames(storeNames);
            setIsFirstRender(false);
          } else {
            // Subsequent renders: Call only fetchMonthlyClientReportCardDetails
            setIsLoading(true);
            const ThreeMonthlyClientReportResult = await fetchThreeMonthClientReportCardDetails();
            if (ThreeMonthlyClientReportResult.error) {
              setIsEmpty(true);
            } else {
              setClientReportCardDetails(ThreeMonthlyClientReportResult);
            }
            // setClientReportCardDetails(ThreeMonthlyClientReportResult);
          }
          // }
        } catch (error) {
          console.error('Error fetching data:', error);
        } finally {
          setIsLoading(false);
          setIsPageLoading(true);
          setIsStoreLoading(false);
          console.log('Fetch data operation complete');
        }
      };

      fetchData();
    }
  }, [selectedStoreIds, filterChanged, mon1]);
  const fetchThreeMonthClientReportCardDetails = () => {
    return new Promise((resolve, reject) => {
      getThreeMonthClientReportCardDetails(
        filterStart,
        filterEnd,
        mon1,
        selectedStoreIds,
        result => {
          const clientReportcardDetails =
            result.data?.statelessDbdKpiScorecardGetThreeMonthsClientReportCard
              ?.clientReportcardDetails;

          if (clientReportcardDetails) {
            setFilterChanged(false);
            const data = JSON.parse(clientReportcardDetails[0].jsonData);
            resolve(data); // Resolve with the parsed data
          } else {
            reject('No client report card details found');
          }
        }
      );
    });
  };

  const getAllStoreName = () => {
    return new Promise((resolve, reject) => {
      const groupIds = JSON.parse(localStorage.getItem('allPermittedStores'));
      getAllStoreNames(groupIds, result => {
        if (result) {
          resolve(result); // Resolve with the store names
        } else {
          reject('Failed to fetch store names');
        }
      });
    });
  };

  const handleCallback = (event, picker) => {
    const startDate = moment(picker.startDate);

    // Initialize an error message variable
    let errorMessage = '';
    setopenSnackbar(false);
    setGoalFail('');
    setErrorMessage('');

    // Check if the start date is the 1st of the month
    if (startDate.date() !== 1) {
      errorMessage =
        'Please select the first day of the selected month as the start date.';
      setopenSnackbar(true);
      setGoalFail(true);
      setErrorMessage(errorMessage);
    } else {
      // Calculate the end date: last day of the third month from the start month
      const calculatedEndDate = startDate
        .clone()
        .add(2, 'months')
        .endOf('month');
      const endDate = moment(picker.endDate);

      // Check if the end date matches the calculated end date
      if (!endDate.isSame(calculatedEndDate, 'day')) {
        errorMessage = `The end date must be ${calculatedEndDate.format(
          'MM/DD/YY'
        )}.`;
        setopenSnackbar(true);
        setGoalFail(true);
        setErrorMessage(errorMessage);
      } else {
        // If no errors, update the picker's dates
        picker.setStartDate(startDate);
        picker.setEndDate(calculatedEndDate);
      }
    }

    // If there is an error message, prevent default and log the error
    if (errorMessage) {
      event.preventDefault();
      console.error(errorMessage);
      // Optionally, display the error message in the UI
      setErrorMessage(errorMessage); // A state setter for UI error messages
      return;
    }

    // Clear any existing error messages
    setErrorMessage(''); // Clear the error message

    if (event.target.value) {
      dispatch(setKpiToggle(event.target.value)); // Dispatch the KPI toggle
    }
    setFilterStart(picker.startDate.format('YYYY-MM-DD')); // Update filter start
    setFilterEnd(picker.endDate.format('YYYY-MM-DD')); // Update filter end
    setFilterChanged(true); // Mark filter as changed
  };

  const hidesnackbar = () => {
    setFilterStart(filterStart);
    setFilterEnd(filterEnd);
    setopenSnackbar(false);

    // Manually update input field
    setTimeout(() => {
      const inputField = document.getElementById('picker');
      if (inputField) {
        inputField.value = `${moment(filterStart).format(
          'MM/DD/YY'
        )} - ${moment(filterEnd).format('MM/DD/YY')}`;
      }

      // Reset DateRangePicker manually
      $('#picker')
        .data('daterangepicker')
        .setStartDate(moment(filterStart).toDate());
      $('#picker')
        .data('daterangepicker')
        .setEndDate(moment(filterEnd).toDate());
    }, 100);
  };

  const handlePDFExportClick = () => {
    exportToPDF(
      clientReportCardDetails,
      image,
      selectedWorkmixOptions,
      fileNameExport,
      selectedOptions
    );
  };
  const setResetDashboard = () => {
    if (parent === 'savedReports') {
      setIsPageLoading(false);
      setSelectedStoreIds(props.kpiReportData.selectedStoreIds);
      setFilterStart(props.kpiReportData.startDate);
      setFilterEnd(props.kpiReportData.endDate);
      setSelectedOptions(props.kpiReportData.selectedRoiView);
      setSelectedWorkmixOptions(props.kpiReportData.workmixview);
      setParent(props.kpiReportData.parent);
      setReportName(props.kpiReportData.reportName);
      setSelectedType(props.kpiReportData.visibility);
      setTimeout(() => {
        setIsPageLoading(true);
      }, 500);
    } else {
      setErrorMessage('');
      setIsPageLoading(false); // Reset page loading state
      setClientReportCardDetails([]); // Clear the report card data
      setSelectedStoreIds(defaultStoreIds);
      setIsStoreLoading(false); // Reset store loading state
      setIsFirstRender(true);
      setFilterStart(dateRange.startDate);
      setFilterEnd(dateRange.endDate);
      // setMon1(getComparisonMonths()[0] || monthsArray[monthsArray.length - 1]); // Reset the first comparison month
      setMon1(
        mon2 || getComparisonMonths()[0] || monthsArray[monthsArray.length - 1]
      ); // Reset the first comparison month
      setSelectedWorkmixOptions([]);
      setSelectedOptions(threeMonthRoiSelectedOptions);
    }
  };
  const renderBackButton = () => {
    const expandedRpt = props.history?.location?.state?.isExpanded ?? 0; // Use optional chaining and nullish coalescing
    props.history.push({
      pathname: '/SavedReports',
      state: {
        isExpanded: expandedRpt,
        created_by:
          props &&
          props.location &&
          props.location.state &&
          props.location.state.created_by
      }
    });
  };
  const handleCopyReport = () => {
    setOpenSaveDlg(true);
    setCopy(true);
    setCopyFile(true);
    if (reportName != '') {
      setReportNameCopy(reportName);
    }
    setReportName('');
  };
  const handleSnackbarClose = () => {
    setopenSusSnackbar(false);
  };
  const handleMailClick = () => {
    setOpenDialogue(true);
    let userList = [];
    getEmail('NULL', 'NULL', 'view', 'Client_Report_Card_1_Month', result => {
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        userList.push(
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
            item => item.value
          )
        );
        setMailUsers(userList);
      } else {
        setMailUsers([]);
      }
    });
  };
  const handleCloseEmail = () => {
    setOpenDialogue(false);
  };
  const convertImageToBase64 = (imgSrc, callback) => {
    const img = new Image();
    img.src = imgSrc;
    img.crossOrigin = 'Anonymous';

    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      const dataURL = canvas.toDataURL('image/png');

      callback(dataURL); // Pass the result using a callback
    };
  };
  useEffect(() => {
    convertImageToBase64('/images/logos/Monthly.jpg', base64Image => {
      setImage(base64Image);
    });
    convertImageToBase64('/images/logos/finalized.png', base64Image => {
      setImageAnnual(base64Image);
    });
  }, []);
  const handleExcelExportClick = () => {
    if (clientReportCardDetails && selectedOptions && selectedWorkmixOptions) {
      exportToExcel(
        clientReportCardDetails,
        selectedOptions,
        selectedWorkmixOptions,
        'ThreeMonth',
        image,
        imageAnnual,
        fileNameExport
      );
      // setSuccessExcel(true);
      // setLoadingIconExcel(false);
    }
  };
  return (
    <>
      {isPageLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <Grid
              xs={12}
              className={clsx(classes.headerItem, 'main-title-kpi')}
            >
              <Grid className="adv-report">
                <Grid className="kpi-report-2-name">
                  {parent == 'savedReports' && (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      // style={linkButtonStyle}
                      onClick={renderBackButton}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  )}
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(classes.mainLabel, 'main-title')}
                  >
                    {fileName}
                  </Typography>
                </Grid>

                <Grid className="picker-report">
                  {closedDate ? (
                    <Typography
                      variant="body1"
                      color="secondary"
                      align="right"
                      style={{ paddingTop: '7px' }}
                      className={clsx(classes.dataLabel, 'date-asof')}
                    >
                      {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
                    </Typography>
                  ) : (
                    ''
                  )}
                  {dates.length > 0 && (
                    <div className={classes.headerDropdown}>
                      <FormControl
                        variant="outlined"
                        margin="dense"
                        className={clsx(classes.formControl, 'input-container')}
                      >
                        <DateRangePicker
                          initialSettings={{
                            locale: {
                              format: 'MM/DD/YY',
                              separator: ' - '
                            },
                            opens: 'left',
                            // maxDate: moment(
                            //   dates[0] && dates[0].today
                            // ).toDate(),
                            // maxDate: moment(
                            //   localStorage.getItem('closedDate')
                            // ).toDate(),
                            maxDate: (() => {
                              const closedDate = moment(
                                localStorage.getItem('closedDate')
                              );
                              const isLastDayOfMonth = closedDate.isSame(
                                closedDate.clone().endOf('month'),
                                'day'
                              );
                              // If the closed date is the last day of the month, use it as the max date
                              if (isLastDayOfMonth) {
                                return closedDate.toDate();
                              }
                              // Otherwise, use the last day of the previous month as the max date
                              return closedDate
                                .clone()
                                .subtract(1, 'months')
                                .endOf('month')
                                .toDate();
                            })(),
                            alwaysShowCalendars: false,
                            applyClass: clsx(classes.calButton, 'apply-btn'),
                            cancelClass: clsx(classes.calButton, 'apply-btn'),

                            startDate: moment(filterStart).toDate(),
                            endDate: moment(filterEnd).toDate()
                          }}
                          onApply={handleCallback}
                        >
                          <input
                            type="text"
                            className="datepicker"
                            id="picker"
                            name="picker"
                            aria-labelledby="label-picker"
                          />
                        </DateRangePicker>
                        <label
                          class="labelpicker"
                          for="picker"
                          id="label-picker"
                        >
                          <div class="textpicker">Select Baseline Date</div>
                        </label>
                      </FormControl>
                    </div>
                  )}
                  <Tooltip title="Save Report">
                    <Link
                      className={classes.linkItem}
                      style={{
                        ...linkStyle
                        // pointerEvents: 'none',
                        // opacity: 0.5
                      }}
                      onClick={handleSaveReport}
                    >
                      <SaveIcon className="saveicon" />
                    </Link>
                  </Tooltip>
                  {parent == 'savedReports' && (
                    // {parent == 'savedReports' && (
                    <Tooltip title="Rename and Copy">
                      <Link
                        className={classes.linkItem}
                        // style={linkStyleCopy}
                        style={{
                          display:
                            (createdBy != '' &&
                              createdBy == localStorage.getItem('userID')) ||
                            selectedType == 'public' ||
                            props.keycloak.realmAccess.roles.includes(
                              'superadmin'
                            )
                              ? 'block'
                              : 'none'
                        }}
                        onClick={handleCopyReport}
                      >
                        <FileCopyOutlinedIcon
                          style={{
                            height: '0.73em',
                            weight: '0.8em',
                            marginRight: '-8px',
                            marginTop: '2px'

                            //                         height: 0.76em;
                            // margin-right: -8px;
                            // margin-top: 2px;
                            // display: allStore ? 'none' : 'block'
                          }}
                        />
                      </Link>
                    </Tooltip>
                  )}{' '}
                  <Tooltip title="Email Now">
                    <Link
                      className={classes.linkItem}
                      style={{
                        ...linkStyle
                      }}
                      onClick={() => handleMailClick()}
                    >
                      <MailIcon
                        style={{
                          height: '1.15em',
                          weight: '1.0em',
                          paddingTop: '2px',
                          marginRight: '-2px'
                        }}
                      />
                    </Link>
                  </Tooltip>
                  <Tooltip title="Export To PDF">
                    <div
                      style={{
                        position: 'relative',
                        display: 'inline-block'
                      }}
                    >
                      <Link
                        className={classes.linkItem}
                        style={{ textDecoration: 'none' }}
                        onClick={() => handlePDFExportClick()}
                      >
                        <i
                          class="fas fa-file-pdf"
                          style={{
                            cursor: 'pointer',
                            color: successExcel ? '#c20a0a' : '#c20a0a'
                          }}
                        ></i>
                      </Link>
                      {loadingIconPDF && (
                        <CircularProgress
                          style={{
                            width: '23px',
                            height: '23px',
                            color: green[500],
                            position: 'absolute',
                            top: '-3px',
                            left: '-5px',
                            zIndex: 1
                          }}
                        />
                      )}
                    </div>
                  </Tooltip>
                  <Tooltip title="Export To Excel">
                    <div
                      style={{
                        position: 'relative',
                        display: 'inline-block'
                        // pointerEvents: 'none',
                        // opacity: 0.5
                      }}
                    >
                      <Link
                        // className={classes.linkItem}
                        onClick={() => handleExcelExportClick()}
                        style={{ textDecoration: 'none' }}
                      >
                        <i
                          class="fas fa-file-excel"
                          style={{
                            cursor: 'pointer',
                            color: successExcel ? '#0e7c3e' : '#0e7c3e'
                          }}
                        ></i>
                      </Link>
                      {loadingIconExcel && (
                        <CircularProgress
                          style={{
                            width: '23px',
                            height: '23px',
                            color: green[500],
                            position: 'absolute',
                            top: '-3px',
                            left: '-5px',
                            zIndex: 1
                          }}
                        />
                      )}
                    </div>
                  </Tooltip>
                  <Button
                    variant="contained"
                    id="reset-layout-kpi"
                    className={'reset-btn'}
                    onClick={setResetDashboard}
                  >
                    <RestoreIcon style={{ height: '0.8em', weight: '0.8em' }} />
                    <Typography variant="body1" align="left">
                      Reset Layout
                    </Typography>
                  </Button>
                </Grid>
              </Grid>
            </Grid>
            <Divider />
            <Grid container>
              <Grid
                item
                xs={6}
                style={{
                  padding: '8px',
                  maxWidth: '100%',
                  flexBasis: '100%'
                }}
              >
                {' '}
                {isStoreLoading !== false ? (
                  <Grid
                    justify="center"
                    style={{
                      height: 300,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center'
                    }}
                  >
                    <CircularProgress size={60} />
                  </Grid>
                ) : (
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={10}>
                      <Paper
                        style={{
                          borderRadius: '0px',
                          //border: '1px solid #ccc'
                          boxShadow: 'none'
                        }}
                      >
                        {/* {isLoading !== false ? (
                          <Grid
                            justify="center"
                            style={{
                              height: 680,
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}
                          >
                            <CircularProgress size={60} />
                          </Grid>
                        ) : (
                          <AuditTable
                            clientReportCardDetails={clientReportCardDetails}
                          />
                        )} */}
                        {isLoading !== false ? (
                          <Grid
                            justifyContent="center"
                            style={{
                              height: 680,
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center'
                            }}
                          >
                            <CircularProgress size={60} />
                          </Grid>
                        ) : isEmpty ? (
                          <Alert
                            severity="info"
                            style={{ fontSize: 14, fontWeight: 'bold' }}
                          >
                            Selected month has no data found during the time
                            period.
                          </Alert>
                        ) : (
                          <ThreeMonthReportTable
                            clientReportCardDetails={clientReportCardDetails}
                            selectedOptions={selectedOptions}
                            selectedWorkmixOptions={selectedWorkmixOptions}
                          />
                        )}
                      </Paper>
                    </Grid>
                    <Grid
                      item
                      xs={12}
                      sm={2}
                      className="report-card-sidebar three-month-sidebar"
                    >
                      <Box sx={{ mb: 1 }}>
                        <MonthSelected
                          mon1={mon1}
                          setMon1={setMon1}
                          closedDate={moment(closedDate).format('YYYY-MM')}
                        />
                      </Box>
                      <Box className="card-selectors" sx={{ mb: 1 }}>
                        <IncludedStores
                          allStoreNames={allStoreNames}
                          selectedStoreIds={selectedStoreIds}
                          setSelectedStoreIds={setSelectedStoreIds}
                        />
                      </Box>
                      <Box className="card-selectors" sx={{ mb: 1 }}>
                        <ROIView
                          selectedOptions={selectedOptions}
                          setSelectedOptions={setSelectedOptions}
                          options={threeMonthRoiOption}
                        />
                      </Box>
                      <Box className="card-selectors" sx={{ mb: 1 }}>
                        <WorkmixView
                          selectedWorkmixOptions={selectedWorkmixOptions}
                          setSelectedWorkmixOptions={setSelectedWorkmixOptions}
                        />
                      </Box>
                    </Grid>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Paper>
          <SaveReportDialog
            openSaveDlg={openSaveDlg}
            parent={parent}
            copyFile={copyFile}
            copy={copy}
            reportName={reportName}
            errorReport={errorReport}
            requiredText={requiredText}
            selectedType={selectedType}
            filterText={filterText}
            onChangeReportName={onChangeReportName}
            handleCancelSaveReport={handleCancelSaveReport}
            handleOkSaveReport={handleOkSaveReport}
            handleSaveAsReport={handleSaveAsReport}
            handleCheckboxChange={handleCheckboxChange}
            reportNameCopy={reportNameCopy}
          />
          {errorMessage && (
            <>
              <Backdrop open={openSnackbar} style={{ zIndex: 1300 }} />
              <Snackbar
                open={openSnackbar}
                anchorOrigin={{
                  vertical: 'top',
                  horizontal: 'center'
                }}
                onClose={hidesnackbar}
                autoHideDuration={null} // Prevent auto close
                ClickAwayListenerProps={{
                  onClickAway: e => e.stopPropagation()
                }} // Prevent closing on outside click
              >
                <Alert
                  severity="error"
                  action={
                    <IconButton
                      aria-label="close"
                      color="inherit"
                      size="small"
                      onClick={hidesnackbar}
                    >
                      <CloseIcon fontSize="inherit" />
                    </IconButton>
                  }
                  style={{ margin: '10px 20px' }}
                >
                  {errorMessage}
                </Alert>
              </Snackbar>
            </>
          )}

          <SuccessSnackbar
            onClose={handleSnackbarClose}
            open={openSusSnackbar}
            msg={'Report saved successfully!'}
            //goalFail={this.state.goalFail}
          />

          <EmailDialogKpi
            open={openDialogue}
            handlePopupClose={handleCloseEmail}
            mailUsers={mailUsers}
            clientReportCardDetails={clientReportCardDetails}
            image={image}
            selectedWorkmixOptions={selectedWorkmixOptions}
            iKpiReportType={iKpiReportType}
            measuredMTH={mon1}
            // priorMTH={priorMTH}
            selectedOptions={selectedOptions}
            selectedStoreIds={selectedStoreIds}
            reportName={reportName}
            reportNameCopy={reportNameCopy}
            filterStart={filterStart}
            filterEnd={filterEnd}
            selectStoreDetails={selectStoreDetails}
            selectedStoreName={storeName}
          ></EmailDialogKpi>
          {openDilogAll ? (
            <Dialog
              open={openDilogAll}
              aria-labelledby="alert-dialog-title"
              aria-describedby="alert-dialog-description"
            >
              <DialogContent>
                <Typography variant="h6" style={{ textTransform: 'none' }}>
                  This option is not available at all stores.
                </Typography>
              </DialogContent>
              <DialogActions>
                <Button onClick={CancelDilogAll} autoFocus color="primary">
                  Ok
                </Button>
              </DialogActions>
            </Dialog>
          ) : null}
        </div>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </>
  );
};
export default withKeycloak(ThreeMonthReportPage);
