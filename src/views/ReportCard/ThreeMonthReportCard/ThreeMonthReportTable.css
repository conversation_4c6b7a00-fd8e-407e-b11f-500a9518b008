#three-month-report-card table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-family: 'Roboto', 'Helvetica', 'Arial', 'sans - serif';
  font-size: 12px;
}
#three-month-report-card th,
#three-month-report-card td {
  /* border: 1px solid black; */
  padding: 8px;
  text-align: left;
  /* border-bottom: 0px; */
}
#three-month-report-card td:first-child {
  /* border-bottom: 1px solid black; */
  border-right: 1px solid black;
}
#three-month-report-card td.rocount-title.crm {
  padding-right: 8px !important;
}
#three-month-report-card td:not(:first-child) {
  border-bottom: 1px solid black;
  border-right: 1px solid black;
}
#three-month-report-card th {
  background-color: #f2f2f2;
}
#three-month-report-card .header {
  text-align: center;
  background-color: #d9d9d9;
  font-weight: bold;
}
#three-month-report-card .sub-header {
  background-color: #d9d9d9;
}
#three-month-report-card .highlight {
  background-color: #e6f7e6;
}
#three-month-report-card .alert {
  background-color: #f2dede;
}
#three-month-report-card .small-text {
  font-size: 10px;
}
#three-month-report-card .no-border {
  border: none;
}
#three-month-report-card .header span {
  display: block;
}
#three-month-report-card .main-header {
  text-align: center;
}
#three-month-report-card .orange-bg {
  background-color: #be5014;
  color: #fff;
}
#three-month-report-card .green-bg {
  background-color: #3c7d22;
  color: #fff;
}
#three-month-report-card .yellow-bg-title {
  background-color: #ffc000;
  font-size: 22px;
}
#three-month-report-card .off-white {
  background-color: #fff;
}
#three-month-report-card .ash-bg {
  background-color: #fff;
}
#three-month-report-card .litegreen-bg {
  background-color: #daf2d0;
}
#three-month-report-card .green-text {
  color: #3c7d22;
}
#three-month-report-card .red-text {
  color: #ff0000;
}
#three-month-report-card .dark-red-text {
  color: #fff;
}
#three-month-report-card .grey-bg {
  background-color: #747474;
}
#three-month-report-card .separation {
  height: 3px;
}

#three-month-report-card .orange-sub-title td,
.orange-sub-header td {
  font-size: 15px;
  font-weight: bold;
}

#three-month-report-card .text-right {
  text-align: right !important;
}
#three-month-report-card .text-center {
  text-align: center !important;
}

#three-month-report-card .blue-bg {
  background-color: #e7f1fa;
}
#three-month-report-card .orange-main-title span {
  display: block;
  text-align: center;
}

#three-month-report-card .separation td {
  padding: 0;
}
#three-month-report-card .maroon-font {
  color: #9c0006;
}
#three-month-report-card .black-bg {
  background-color: black;
}
.header-section {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #000;
}
thead.header-section th {
  border-top: 7px solid #000 !important;
  border-bottom: 7px solid #000 !important;
  border-right: 1px solid #000;
}
#three-month-report-card .client-report-outer {
  border: 7px solid #000;
}
#three-month-report-card .no-border td {
  border: 0;
}
#three-month-report-card .title-border {
  border-top: 7px solid #000;
  border-bottom: 7px solid #000;
}
#three-month-report-card .lborder-zero {
  border-left: 0;
}
#three-month-report-card .rborder-zero {
  border-right: 0 !important;
}
#three-month-report-card .no-border {
  border: 0;
}
#three-month-report-card .bborder-zero {
  border-bottom: 0;
}
#three-month-report-card .header-section button {
  /* cursor: pointer; */
  padding: 2px 10px;
  margin: 7px;
  border-radius: 3px;
  width: 150px;
  border: 1px #000 solid;
  pointer-events: none;
}
#three-month-report-card .whiteline {
  border-bottom: 1px solid #fff;
  padding-bottom: 3px;
  margin-bottom: 3px;
}
/* #three-month-report-card .roi {
  font-size: 23px;
  font-weight: 400;
} */

#three-month-report-card .based-text span {
  font-size: 17px;
  font-weight: 700;
  margin-top: 5px;
}
#three-month-report-card .thick-border-r {
  border-right: 7px #000 solid !important;
}
/* #three-month-report-card .main-title {
  font-size: 26px;
}
#three-month-report-card .based-text-main {
  width: 65%;
  float: right;
} */
#three-month-report-card .baseline-small {
  font-size: 13px;
  font-weight: normal;
}
.yellow-bg-title {
  padding: 4px !important;
}
#three-month-report-card .datatable td {
  font-size: 14px;
  text-align: right;
  /* font-weight: 300; */
  padding: 5px;
}
.separation td {
  padding: 0 !important;
}

#three-month-report-card .orange-main-title td {
  vertical-align: bottom;
  /* font-size: 14px; */
  font-weight: 700 !important;
}
#three-month-report-card .orange-sub-title td {
  text-align: center;
  /* font-size: 19px; */
  font-weight: 700;
}
#three-month-report-card .client-report {
  border-collapse: separate;
  border-spacing: 0;
}

#three-month-report-card .rocount-title {
  position: relative;
}
#three-month-report-card .rocount-title span {
  border-bottom: 1px solid #000;
  position: absolute;
  right: 0;
  bottom: 0px;
  padding-right: 8px;
  padding-left: 10px;
  padding-bottom: 4px;
}
#three-month-report-card .blue-bg {
  font-weight: 700 !important;
}
@media (min-width: 1025px) and (max-width: 1280px) {
  #three-month-report-card .report-card {
    font-size: 12px !important;
    font-weight: 500;
  }
  #three-month-report-card .roi {
    font-size: 17px;
    font-weight: 400;
  }
  #three-month-report-card .based-text-main {
    width: 100%;
  }
  #three-month-report-card .rocount-title {
    font-size: 14px !important;
    font-weight: 500 !important;
  }
  #three-month-report-card .datatable td {
    font-size: 14px;
  }
  #three-month-report-card .main-title {
    font-size: 20px !important;
  }
  #three-month-report-card .based-text span {
    font-size: 15px;
  }
}

.report-card-sidebar .card-selectors {
  margin-bottom: 0px !important;
}
.report-card-sidebar .card-selectors .sidebar-card {
  display: block;
}

/* 27-12-2024 */
#three-month-report-card .orange-sub-header.orange-bg td:first-child {
  /* border-top: 1px solid #000; */
  border-bottom: 1px solid #000;
}
#three-month-report-card .report-card span:first-child {
  margin-bottom: 5px;
}
#three-month-report-card .report-card span:last-child {
  margin-top: 15px;
}
#three-month-report-card .orange-sub-header td:last-child {
  border-right: 0 !important;
}
#three-month-report-card .based-text {
  font-size: 13px;
  font-weight: 400;
}
.orange-sub-title td {
  text-align: center;
  font-size: 19px;
  font-weight: 700;
}
.report-card-sidebar span.MuiButtonBase-root {
  padding-right: 5px;
}
.report-card-sidebar svg.MuiSvgIcon-root {
  width: 17px;
}
.report-card-sidebar span.MuiTypography-root.MuiFormControlLabel-label {
  line-height: 16px;
}
.report-card-sidebar .MuiFormControlLabel-root {
  height: 17px;
  margin: 4px 2px;
}
.report-card-sidebar .MuiFormControlLabel-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  width: 80%;
  font-size: 14px;
}
.noborder-title {
  border-bottom: 0 !important;
}
#three-month-report-card .kpi-title {
  font-size: 25px !important;
  border-bottom: 1px solid #000;
}
.orange-sub-title.orange-bg td {
  font-size: 17px !important;
}
#three-month-report-card .report-card {
  font-size: 17px !important;
  font-weight: 500;
}
.orange-main-title td {
  font-size: 14px !important;
}
.rocount-title {
  font-size: 16px !important;
  font-weight: 500 !important;
}
.roi {
  font-size: 22px;
  font-weight: 400;
}
#three-month-report-card .orange-sub-title.orange-bg td {
  padding-bottom: 5px !important;
  padding-top: 5px !important;
}
.orange-sub-title.orange-bg td {
  padding: 0 !important;
}
.orange-main-title.orange-bg td {
  padding: 3px !important;
}
.rocount-bborder {
  border-bottom: 1px solid black !important;
}
.separation .grey-bg:first-child {
  border-bottom: 1px solid black !important;
}
.datatable tbody tr:last-child td:first-child span,
.datatable tbody tr:last-child td {
  border-bottom: 0 !important;
}
.text-right.lborder-zero.kpi-title {
  padding-right: 8px !important;
}
#three-month-report-card .based-text-main {
  width: 75%;
  float: right;
}

@media (max-width: 1785px) {
  #three-month-report-card .rocount-title {
    font-size: 14px !important;
  }
}
@media (max-width: 1780px) {
  #three-month-report-card .based-text {
    font-size: 12px;
  }
}

@media (max-width: 1680px) {
  #three-month-report-card .based-text-main {
    width: 82%;
  }
}

@media (max-width: 1660px) {
  #three-month-report-card .based-text {
    font-size: 11px;
  }
  #three-month-report-card .based-text-main {
    width: 78%;
  }
}
@media (max-width: 1560px) {
  #three-month-report-card .based-text-main {
    width: 82%;
  }
}

@media (max-width: 1650px) {
  #three-month-report-card .based-text-main {
    width: 87%;
  }
  #three-month-report-card .based-text {
    font-size: 12px;
  }
  #three-month-report-card .based-text span {
    font-size: 14px;
  }
  #three-month-report-card .report-card {
    font-size: 15px !important;
  }
  .orange-sub-title.orange-bg td {
    font-size: 16px !important;
  }
  #three-month-report-card .kpi-title {
    font-size: 20px !important;
  }
  .orange-main-title td {
    font-size: 11px !important;
  }
  .roi {
    font-size: 18px;
  }
  #three-month-report-card .rocount-title {
    font-size: 13px !important;
  }
}

@media (max-width: 1765px) {
  .orange-main-title td {
    font-size: 12px !important;
  }
  .report-card-sidebar .MuiFormControlLabel-label {
    width: 170px !important;
  }
}
@media (max-width: 1600px) {
  .report-card-sidebar .MuiFormControlLabel-label {
    width: 150px !important;
  }
}
@media (max-width: 1575px) {
  #three-month-report-card .report-card {
    font-size: 14px !important;
  }
  .orange-main-title td {
    font-size: 10px !important;
  }
  #three-month-report-card .rocount-title {
    font-size: 12px !important;
  }
}

@media (max-width: 1550px) {
  #three-month-report-card .based-text {
    font-size: 10px;
  }
  #three-month-report-card .datatable td {
    font-size: 12px;
  }
  #three-month-report-card .report-card {
    font-size: 12px !important;
  }
  .rocount-title {
    font-size: 14px !important;
  }
  .report-card-sidebar .MuiFormControlLabel-label {
    width: 140px !important;
  }
  #three-month-report-card .rocount-title {
    font-size: 12px !important;
  }
}

@media (max-width: 1450px) {
  .rocount-title {
    font-size: 13px !important;
  }
  #three-month-report-card .based-text-main {
    width: 100% !important;
  }
  .report-card-sidebar .MuiFormControlLabel-label {
    width: 115px !important;
  }
  #three-month-report-card .rocount-title {
    font-size: 11px !important;
  }
}

/* 30-12-2024 */
.report-card-sidebar {
  padding-top: 0 !important;
}
.report-card-sidebar label {
  padding-right: 7px;
}
.sidebar-view-title {
  margin-top: 15px !important;
}

.sidebar-box {
  padding: 9px;
  border-radius: 0 !important;
  border: 1px solid #ccc;
}
.orange-sub-header.orange-bg td {
  font-size: 18px !important;
}
.sidebar-checkbox {
  pointer-events: none;
}
.sidebar-checkbox:hover {
  background-color: rgba(246, 249, 251, 0);
}
.sidebar-input {
  pointer-events: auto;
}
.sidebar-input:hover {
  background-color: rgba(246, 249, 251, 0) !important;
}
.picker-report .input-container {
  height: 30px;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.picker-report .textpicker {
  position: relative;
  top: 4px;
}

.report-card-sidebar .MuiFormControl-marginDense {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
  padding-left: 0 !important;
}
.report-card-sidebar span.MuiButtonBase-root:hover {
  background-color: transparent !important;
}

@media (max-width: 1440px) {
  #three-month-report-card .rocount-title {
    font-size: 10px !important;
  }
}
@media (max-width: 1390px) {
  #three-month-report-card .rocount-title {
    font-size: 10px !important;
  }
}
@media screen and (min-device-width: 1280px) and (max-device-width: 1390px) {
  #three-month-report-card tbody tr:nth-child(12) td.rocount-title span,
  #three-month-report-card tbody tr:nth-child(13) td.rocount-title span {
    padding-bottom: 0 !important;
  }
}

@media (max-width: 1280px) {
  #three-month-report-card .report-card {
    font-size: 10px !important;
  }
  #three-month-report-card .based-text {
    font-size: 9px;
  }
}

.three-month-sidebar .card-selectors:first-child .sidebar-view-title {
  margin-top: 0 !important;
}

.data-col-1 {
  min-width: 20%;
}
.data-col-2 {
  min-width: 15%;
}
.data-col-3 {
  width: 15%;
}
.data-col-4 {
  width: 10%;
}
.data-col-5 {
  width: 15%;
}
.data-col-6 {
  width: 15%;
}
.data-col-7 {
  width: 10%;
}

.sidebar-card .MuiCheckbox-root {
  padding: 0 !important;
  margin-right: 5px;
}

.month-label {
  padding-top: 12px;
}
