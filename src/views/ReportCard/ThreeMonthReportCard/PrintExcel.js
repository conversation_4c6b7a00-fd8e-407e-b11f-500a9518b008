import { text } from '@fortawesome/fontawesome-svg-core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { async } from 'validate.js';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

export const useExcelExport = () => {
  const formatCellValue = (value, symbol) => {
    var formattedValue =
      value &&
      parseFloat(value)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    if (symbol == '%') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + '%)';
      } else {
        formattedValue = formattedValue + '%';
      }
    } else if (symbol == '$') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = '($' + cellValue + ')';
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + ')';
      } else {
        formattedValue = formattedValue;
      }
    }

    return formattedValue;
  };
  const getDocument = async (
    clientReportCardDetails,
    image,
    selectedWorkmixOptions
  ) => {
    var allData = [];
    var excelData = [];
    //var kpiDataAll = allRowData;
    var summaryData = clientReportCardDetails.summaryData;

    const worksheet = XLSX.utils.json_to_sheet(
      clientReportCardDetails.monthlyData
    );
    let title = '3 Month Report';
    const centerAlignment = {
      vertical: 'middle',
      horizontal: 'center'
    };
    const json = XLSX.utils.sheet_to_json(worksheet);

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, title);

    const newWorkbook = new ExcelJS.Workbook();
    const newWorksheet = newWorkbook.addWorksheet(title);

    // newWorksheet.mergeCells('A1');
    newWorksheet.getCell('A1').value =
      'Monthly FOPC: ' + formatCellValue(summaryData.monthly_fopc);
    newWorksheet.getCell('A1').alignment = centerAlignment;
    newWorksheet.getCell('A2').alignment = centerAlignment;
    newWorksheet.getCell('A3').alignment = centerAlignment;

    newWorksheet.getCell('A2').value =
      'Monthly DMS: ' + formatCellValue(summaryData.monthly_dms);
    newWorksheet.getCell('A3').value =
      'Total: ' + formatCellValue(summaryData.total_monthly);

    newWorksheet.mergeCells(3, 2, 4, 2);

    newWorksheet.getCell('B1').value =
      'ROI = ' + formatCellValue(summaryData.roi_prior_lbr_parts);
    newWorksheet.getCell('B1').alignment = centerAlignment;
    newWorksheet.getCell('B2').alignment = centerAlignment;
    newWorksheet.getCell('B3').alignment = centerAlignment;
    newWorksheet.getCell('B2').value = 'Monthly';
    newWorksheet.getCell('B4').value =
      'ROI = ' + formatCellValue(summaryData.roi_prior_repair_elr);

    newWorksheet.mergeCells(2, 3, 3, 3);

    newWorksheet.getCell('C1').value =
      'Based on Total Pts & Lbr GP Change of \n' +
      formatCellValue(summaryData.if_prior_parts_labor_gp);

    newWorksheet.getCell('C1').alignment = centerAlignment;
    newWorksheet.getCell('C2').alignment = centerAlignment;
    newWorksheet.getCell('C3').alignment = centerAlignment;

    newWorksheet.getCell('C3').value =
      'Based on Repair ELR Change of \n' +
      formatCellValue(summaryData.if_prior_repair_elr);

    newWorksheet.mergeCells(1, 4, 2, 4);
    newWorksheet.mergeCells(3, 4, 4, 4);

    newWorksheet.getCell('D1').value =
      'ROI = ' + formatCellValue(summaryData.roi_annual_lbr_parts);
    newWorksheet.getCell('D1').alignment = centerAlignment;
    newWorksheet.getCell('D2').alignment = centerAlignment;
    newWorksheet.getCell('D3').alignment = centerAlignment;
    newWorksheet.getCell('D2').value = '3 MTH Annualized';
    newWorksheet.getCell('D4').value =
      'ROI = ' + formatCellValue(summaryData.roi_annual_repair_elr);

    newWorksheet.mergeCells(2, 5, 3, 5);

    newWorksheet.getCell('E1').value =
      'Based on Total Pts & Lbr GP Change of \n' +
      formatCellValue(summaryData.if_annual_parts_labor_gp);

    newWorksheet.getCell('E1').alignment = centerAlignment;
    newWorksheet.getCell('E2').alignment = centerAlignment;
    newWorksheet.getCell('E3').alignment = centerAlignment;

    newWorksheet.getCell('E3').value =
      'Based on Repair ELR Change of \n' +
      formatCellValue(summaryData.if_annual_repair_elr);

    newWorksheet.mergeCells(1, 6, 2, 6);
    newWorksheet.mergeCells(3, 6, 4, 6);

    //newWorksheet.mergeCells(1, 2, 3, 3);
    //console.log('endColumnIdx', endColumnIdx, totalRowCount);
    //newWorksheet.mergeCells('B1', 'AH2');
    newWorksheet.getRow(1).height = 20;
    newWorksheet.getRow(2).height = 20;
    newWorksheet.getRow(3).height = 20;
    // newWorksheet.getColumn(1).width = 30;
    // newWorksheet.getColumn(2).width = 45;
    // newWorksheet.getColumn(3).width = 45;
    var parentRow = [];
    json.forEach((row, rowIndex) => {
      const excelRow = newWorksheet.addRow(Object.values(row));

      let newRowValues = [];

      var newRows = newWorksheet.addRow(newRowValues);
    });

    // newWorksheet.getCell('B1').font = {
    //   color: { argb: 'FFFFFF' },
    //   size: 20,

    //   bold: true
    // };

    // newWorksheet.columns.forEach(function(column, i) {
    //   let maxLength = 0;
    //   if (i == 0) {
    //     column['eachCell']({ includeEmpty: true }, function(cell) {
    //       var columnLength = cell.value ? cell.value.toString().length : 6;
    //       if (columnLength > maxLength) {
    //         maxLength = columnLength;
    //       }
    //     });
    //     column.width = maxLength < 10 ? 6 : maxLength;
    //   } else {
    //     column.width = 12;
    //   }
    // });
    const buffer = await newWorkbook.xlsx.writeBuffer();
    const fileType =
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    const fileExtension = '.xlsx';

    const blob = new Blob([buffer], { type: fileType });

    saveAs(blob, 'test' + fileExtension);
    //gridApi.api.setFilterModel(null);
    // gridApi.api.redrawRows();
  };

  const exportToExcel = (
    clientReportCardDetails,
    image,
    selectedWorkmixOptions
  ) => {
    getDocument(clientReportCardDetails, image, selectedWorkmixOptions);
  };
  return {
    exportToExcel
  };
};
