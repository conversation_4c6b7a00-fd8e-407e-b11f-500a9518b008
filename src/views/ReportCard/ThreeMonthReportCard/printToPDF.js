import { text } from '@fortawesome/fontawesome-svg-core';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import moment from 'moment';
import React, { useState, useEffect } from 'react';
import { Line } from 'react-chartjs-2';
pdfMake.vfs = pdfFonts.pdfMake.vfs;

/**
 * This function iterates over all of the columns to create a row of header cells
 */
export const usePDFExport = () => {
  // Row colors
  const TITLE_ROW_COLOR = '#ffc000';
  const HEADER_ROW_COLOR = '#c65911';
  const EVEN_ROW_COLOR = '#fcfcfc';
  const ODD_ROW_COLOR = '#fff';

  const PDF_INNER_BORDER_COLOR = '#dde2eb';
  const PDF_OUTER_BORDER_COLOR = '#babfc7';

  const createLayout = numberOfHeaderRows => ({
    fillColor: (rowIndex, node) => {
      if (rowIndex < numberOfHeaderRows) {
        return HEADER_ROW_COLOR;
      }
      // if (rows.includes(rowIndex - 1)) {
      //   return TITLE_ROW_COLOR;
      // }
      // return rowIndex % 2 === 0 ? EVEN_ROW_COLOR : ODD_ROW_COLOR;
    },

    //vLineHeight not used here.
    vLineWidth: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.widths.length ? 1 : 0,
    hLineColor: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.body.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR,
    vLineColor: (rowIndex, node) =>
      rowIndex === 0 || rowIndex === node.table.widths.length
        ? PDF_OUTER_BORDER_COLOR
        : PDF_INNER_BORDER_COLOR
  });
  const formatToCustomDate = value => {
    return moment(value, 'YYYY-MM').format("MMM' YY");
  };
  function transformData(monthlyData) {
    var tableData = monthlyData.map((item, i) => {
      var content = [
        {
          text: [
            {
              text: item.kpi_name,
              fontSize: 8,
              color:
                item.kpi_name == 'Competitive' ||
                item.kpi_name == 'Maintenance' ||
                item.kpi_name == 'Repair'
                  ? '#fff'
                  : '#000',
              bold: true,
              alignment: 'right',
              marginTop: 6
            }
          ],
          //border: [true, false, true, false],
          //decoration: 'underline',
          fillColor:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? '#c65911'
              : '#fff'
        },
        ...[
          'three_mon_avg',
          'last_month',
          'prior_variance',
          'prior_annual_pace',
          'current_annual_pace',
          'annual_variance'
        ].map(key => ({
          text:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? formatCategoryCellValue(item[key], item.kpi_symbol)
              : formatCellValue(item[key], item.kpi_symbol),

          border: [true, true, true, true],
          borderColor:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? key == 'annual_variance'
                ? ['#c65911', '#000', '#000', '#000']
                : key == 'three_mon_avg'
                ? ['#000', '#000', '#c65911', '#000']
                : ['#c65911', '#000', '#c65911', '#000']
              : ['#000', '#000', '#000', '#000'],
          alignment: 'right',
          fontSize: 8,
          color:
            key == 'prior_variance' || key == 'annual_variance'
              ? setVarianceColor(key, item)
              : '#000',
          fillColor:
            item.kpi_name == 'Competitive' ||
            item.kpi_name == 'Maintenance' ||
            item.kpi_name == 'Repair'
              ? '#c65911'
              : '#fff'
        }))
      ];

      return content;
    });

    return tableData;
  }
  const setVarianceColor = (key, item) => {
    var color;
    if (key == 'prior_variance') {
      if (item.prior_variance > 0) {
        color = '#3c7d22';
      } else if (item.prior_variance == 0) {
        color = '#000';
      } else {
        color = 'red';
      }
    }
    if (key == 'annual_variance') {
      if (item.annual_variance > 0) {
        color = '#3c7d22';
      } else if (item.annual_variance == 0) {
        color = '#000';
      } else {
        color = 'red';
      }
    }

    return color;
  };
  const setCategoryColor = item => {
    var color = '#c65911';

    return color;
  };
  const formatCellValue = (value, symbol) => {
    var formattedValue =
      value &&
      parseFloat(value)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

    if (symbol == '%') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + '%)';
      } else {
        formattedValue = formattedValue + '%';
      }
    } else if (symbol == '$') {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        formattedValue = '($' + cellValue + ')';
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      if (Number(formattedValue && formattedValue.replace(/,/g, '')) < 0) {
        var cellValue = Math.abs(Number(formattedValue.replace(/,/g, '')))
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        formattedValue = '(' + cellValue + ')';
      } else {
        formattedValue = formattedValue;
      }
    }

    return formattedValue;
  };
  const formatCategoryCellValue = value => {
    return '';
  };

  const setHeaderSection = (colWidthSubHeader, summaryData, page) => {
    return {
      style: 'subheaderCol',
      table: {
        layout: 'noBorders',
        // widths: [140, 60, 312],
        widths: colWidthSubHeader,
        heights: [20],
        body: [
          [
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#000', '#000', '#000', '#c65911'],
              text: [
                {
                  text: '',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text:
                    moment(summaryData.prior_to_fopc_start_date).format(
                      'MM/YY'
                    ) +
                    ' - ' +
                    moment(summaryData.prior_to_fopc_end_date).format('MM/YY') +
                    '\n',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                },
                {
                  text: 'Prior to FOPC',
                  fontSize: 10,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#c65911', '#000', '#c65911', '#000'],
              text: [
                {
                  text: '',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#c65911', '#000', '#000', '#000'],
              text: [
                {
                  text: '',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: '\n',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                },
                {
                  text: 'Baseline Annualized',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#000', '#000', '#c65911', '#000'],
              text: [
                {
                  text:
                    moment(
                      summaryData.used_to_annul_cur_3_mon_start_date
                    ).format('MM/YY') +
                    ' - ' +
                    moment(summaryData.used_to_annul_cur_3_mon_end_date).format(
                      'MM/YY'
                    ) +
                    '\n',
                  fontSize: 9,
                  color: '#fff'
                },
                {
                  text: 'Last 3 MTHs - Annualized',
                  fontSize: 9,
                  color: '#fff'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#c65911', '#000', '#000', '#000'],
              text: [
                {
                  text: '',
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            }
          ],
          [
            {
              margin: [0, 4, 0, 0],
              alignment: 'right',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              borderColor: ['#000', '#c65911', '#000', '#000'],
              text: [
                {
                  text: 'KPIs',
                  fontSize: 15,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: '3 MTH Avg',
                  fontSize: 10,
                  color: '#fff'

                  // background: '#c65911'
                },
                {
                  text: '(Baseline)',
                  fontSize: 8,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, false, true],
              text: [
                {
                  text: formatToCustomDate(
                    summaryData.used_to_annul_cur_3_mon_end_date
                  ),
                  fontSize: 10,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: 'Variance',
                  fontSize: 10,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: 'Prior Annual Pace',
                  fontSize: 10,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, false, true],
              text: [
                {
                  text: 'Annual Pace',
                  fontSize: 10,
                  color: '#fff'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [false, true, true, true],
              text: [
                {
                  text: 'Variance',
                  fontSize: 10,
                  color: '#fff'

                  // background: '#c65911'
                }
              ]
            }
          ]
        ]
      },
      layout: {
        hLineWidth: function(i, node) {
          return i === 0 && page == 'second' ? 3 : 1;
        },
        vLineWidth: function(i, node) {
          return i === 0 || i === node.table.widths.length ? 3 : 1;
        },
        hLineColor: function(i, node) {
          return 'black';
        },
        vLineColor: function(i, node) {
          return 'black';
        }
      }
    };
  };
  function splitArrayAtIndex(arr, index) {
    return [arr.slice(0, index), arr.slice(index)];
  }

  async function downloadPDF(
    clientReportCardDetails,
    image,
    selectedWorkmixOptions,
    fileNameExport,
    selectedOptions
  ) {
    console.log('downloadPDF>>>>>>', selectedOptions);
    //getDocument(data);
    const imageDataUrl = image;
    //console.log('imageDataUrl', imageDataUrl);
    const summaryData = clientReportCardDetails.summaryData || {};
    var con = [];

    var colWidthTop = [130, 130, 170, 130, 170];
    var topbar = {
      style: 'summaryData',
      table: {
        layout: 'noBorders',
        // widths: [140, 60, 312],
        widths: colWidthTop,
        heights: [40],
        // body: [topbarContent]
        body: [
          [
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#c65911',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text:
                    'Monthly FOPC: ' +
                    formatCellValue(summaryData.monthly_fopc, '$') +
                    '\n',
                  fontSize: 10,
                  color: '#fff',
                  lineHeight: 1
                  // background: '#c65911'
                },
                {
                  text:
                    'Monthly DMS: ' +
                    formatCellValue(summaryData.monthly_dms, '$') +
                    '\n',
                  fontSize: 10,
                  color: '#fff'

                  // background: '#c65911'
                }
              ],

              alignment: 'center',
              fillColor: '#c65911',
              color: '#fff',
              bold: true
            },
            {
              margin: [0, 8, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,
              text: [
                selectedOptions.includes('monthlyPts')
                  ? {
                      text:
                        'ROI = ' +
                        formatCellValue(
                          summaryData.roi_prior_lbr_parts || 0,
                          '%'
                        ) +
                        '\n',
                      fontSize: 13,
                      color: '#fff',
                      lineHeight: 2
                    }
                  : null,
                selectedOptions.includes('monthlyPts') ||
                selectedOptions.includes('monthlyElr')
                  ? {
                      text: '   Monthly   ' + '\n',
                      fontSize: 9,
                      color: '#000',
                      //lineHeight: 1,
                      style: 'divButton'
                      // margin: [20, 20, 20, 20]
                    }
                  : null
              ],

              fillColor: '#3c7d22',
              borderColor: ['', '', '#3c7d22', '#3c7d22']
            },
            {
              margin: [0, 8, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,

              text: [
                selectedOptions.includes('monthlyPts')
                  ? {
                      text: 'Based on Total Pts & Lbr GP Change of' + '\n',
                      fontSize: 7,
                      color: '#fff'
                    }
                  : null,
                selectedOptions.includes('monthlyPts')
                  ? {
                      text: formatCellValue(
                        summaryData.if_prior_parts_labor_gp,
                        '$'
                      ),
                      fontSize: 10,
                      color: '#fff',
                      bold: true,
                      lineHeight: 1
                      // background: '#c65911'
                    }
                  : null
              ],
              alignment: 'center',
              fillColor: '#3c7d22',

              // border: [true, false, false, true], // Top, Right, Bottom, Left

              borderColor:
                selectedOptions.includes('monthlyPts') &&
                selectedOptions.includes('monthlyElr')
                  ? ['#3c7d22', '', '', '#fff']
                  : ['#3c7d22', '', '', '#3c7d22']
              // borderColor: ['#3c7d22', '', '', '#fff']
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,
              text: [
                selectedOptions.includes('annualizedPts')
                  ? {
                      text:
                        'ROI = ' +
                        formatCellValue(
                          summaryData.roi_annual_lbr_parts || 0,
                          '%'
                        ) +
                        '\n',
                      fontSize: 13,
                      color: '#fff',
                      lineHeight: 2
                      // background: '#c65911'
                    }
                  : null,
                selectedOptions.includes('annualizedElr') ||
                selectedOptions.includes('annualizedPts')
                  ? {
                      text: '  3 MTH Annualized  ' + '\n',
                      fontSize: 9,
                      color: '#000',
                      //lineHeight: 1,
                      style: 'divButton'
                      //margin: [20, 20, 20, 20]
                      // background: '#c65911'
                    }
                  : null
              ],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor: ['#3c7d22', '', '#3c7d22', '#3c7d22']
            },
            {
              margin: [0, 8, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,
              text: [
                selectedOptions.includes('annualizedPts')
                  ? {
                      text: 'Based on Total Pts & Lbr GP Change of' + '\n',
                      fontSize: 7,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null,
                selectedOptions.includes('annualizedPts')
                  ? {
                      text: formatCellValue(
                        summaryData.if_annual_parts_labor_gp,
                        '$'
                      ),
                      fontSize: 10,
                      bold: true,
                      lineHeight: 1,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null
              ],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor:
                selectedOptions.includes('annualizedElr') &&
                selectedOptions.includes('annualizedPts')
                  ? ['#3c7d22', '', '', '#fff']
                  : ['#3c7d22', '', '', '#3c7d22']
            }
          ],
          [
            {
              text: 'Total: ' + formatCellValue(summaryData.total_monthly, '$'),
              fontSize: 10,
              border: [true, true, true, true],
              borderColor: ['', '#c65911', '', ''],
              alignment: 'center',
              fillColor: '#c65911',
              color: '#fff',
              bold: true
              // background: '#c65911'
            },
            {
              text: [
                selectedOptions.includes('monthlyElr')
                  ? {
                      text:
                        'ROI = ' +
                        formatCellValue(
                          summaryData.roi_prior_repair_elr || 0,
                          '%'
                        ) +
                        '\n',
                      fontSize: 13,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null
              ],
              margin: [0, 8, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,

              borderColor: ['', '', '#3c7d22', '']
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,
              text: [
                {
                  text: '',
                  lineHeight: 1,
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                },
                selectedOptions.includes('monthlyElr')
                  ? {
                      text: 'Based on Repair ELR Change of' + '\n',
                      fontSize: 7,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null,
                selectedOptions.includes('monthlyElr')
                  ? {
                      text:
                        formatCellValue(summaryData.if_prior_repair_elr, '$') ||
                        0 + '\n\n\n',
                      fontSize: 13,
                      bold: true,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null
              ],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor: ['', '', '', '']
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,
              text: [
                selectedOptions.includes('annualizedElr')
                  ? {
                      text:
                        'ROI = ' +
                        formatCellValue(
                          summaryData.roi_annual_repair_elr || 0,
                          '%'
                        ) +
                        '\n',
                      fontSize: 13,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null
              ],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor: ['', '#3c7d22', '#3c7d22', '']
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#3c7d22',
              bold: true,
              text: [
                {
                  text: '',
                  lineHeight: 1,
                  fontSize: 9,
                  color: '#fff'

                  // background: '#c65911'
                },
                selectedOptions.includes('annualizedElr')
                  ? {
                      text: 'Based on Repair ELR Change of' + '\n',
                      fontSize: 7,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null,
                selectedOptions.includes('annualizedElr')
                  ? {
                      text:
                        formatCellValue(
                          summaryData.if_annual_repair_elr,
                          '$'
                        ) || 0 + '\n\n\n',
                      fontSize: 9,
                      color: '#fff'

                      // background: '#c65911'
                    }
                  : null
              ],
              alignment: 'center',
              fillColor: '#3c7d22',
              borderColor: ['#3c7d22', '#3c7d22', '', '']
            }
          ]
        ]
      },
      layout: {
        hLineWidth: function(i, node) {
          return i === 0 || i === node.table.body.length ? 3 : 1;
        },
        vLineWidth: function(i, node) {
          return i === 0 || i === node.table.widths.length ? 3 : 1;
        },
        hLineColor: function(i, node) {
          return 'black';
        },
        vLineColor: function(i, node) {
          return 'black';
        }
      }
    };
    console.log('topbar===', topbar);
    con.push(topbar);
    var colWidthTitle = [130, 627];
    var titlebar = {
      style: 'titleData',
      table: {
        layout: 'noBorders',
        // widths: [140, 60, 312],
        widths: colWidthTitle,
        heights: [20],
        body: [
          [
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#ffc000',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: 'Report Card',
                  fontSize: 15,
                  color: '#000'

                  // background: '#c65911'
                }
              ]
            },
            {
              margin: [0, 4, 0, 0],
              alignment: 'center',
              fillColor: '#ffc000',
              bold: true,
              border: [true, true, true, true],
              text: [
                {
                  text: localStorage.getItem('storeGroup'),
                  fontSize: 15,
                  color: '#000'

                  // background: '#c65911'
                }
              ]
            }
          ]
        ]
      },
      layout: {
        hLineWidth: function(i, node) {
          return i === node.table.body.length ? 3 : 1;
        },
        vLineWidth: function(i, node) {
          return i === 0 || i === node.table.widths.length ? 3 : 1;
        },
        hLineColor: function(i, node) {
          return 'black';
        },
        vLineColor: function(i, node) {
          return 'black';
        }
      }
    };
    con.push(titlebar);
    var colWidthSubHeader = [130, 100, 140, 50, 100, 140, 52];
    var subheaderCol = setHeaderSection(
      colWidthSubHeader,
      summaryData,
      'first'
    );
    con.push(subheaderCol);

    var tableData = clientReportCardDetails.monthlyData;

    var filterdArr;
    if (selectedWorkmixOptions.length == 0) {
      filterdArr = tableData.filter(function(el) {
        return el.kpi_category == 'Total';
      });
    } else {
      filterdArr = tableData.filter(function(el) {
        return (
          el.kpi_category == 'Total' ||
          selectedWorkmixOptions.includes(el.kpi_category)
        );
      });
    }

    var elem = transformData(filterdArr);

    var tableRow;
    if (selectedWorkmixOptions.length > 1) {
      var arrRows = splitArrayAtIndex(elem, 24);
      arrRows.map((arr, i) => {
        var body = {
          style: 'headerCol',
          table: {
            layout: 'noBorders',
            // widths: [140, 60, 312],
            widths: colWidthSubHeader,
            //heights: [20],
            body: arr
          },
          layout: {
            hLineWidth: function(i, node) {
              return i === node.table.body.length
                ? 3
                : i == 10 || i == 22 || i == 34 || i == 46
                ? 2
                : 1;
            },
            vLineWidth: function(i, node) {
              return i === 0 || i === node.table.widths.length ? 3 : 1;
            },
            hLineColor: function(i, node) {
              return i != 9 ? 'black' : 'linear - gradient(white, lightblue)';
            },
            vLineColor: function(i, node) {
              return 'black';
            }
          }
        };
        con.push(body);
        if (i == 0) {
          con.push({ text: '', pageBreak: 'before', style: 'subheader' });
          var subheaderCol = setHeaderSection(
            colWidthSubHeader,
            summaryData,
            'second'
          );
          con.push(subheaderCol);
        }
      });
    } else {
      var tableRow = {
        style: 'headerCol',
        table: {
          layout: 'noBorders',
          // widths: [140, 60, 312],
          widths: colWidthSubHeader,
          //heights: [20],
          body: elem
        },
        layout: {
          hLineWidth: function(i, node) {
            return i === node.table.body.length
              ? 3
              : i == 10 || i == 22 || i == 34 || i == 46
              ? 2
              : 1;
          },
          vLineWidth: function(i, node) {
            return i === 0 || i === node.table.widths.length ? 3 : 1;
          },
          hLineColor: function(i, node) {
            return i != 9 ? 'black' : 'linear - gradient(white, lightblue)';
          },
          vLineColor: function(i, node) {
            return 'black';
          }
        }
      };

      con.push(tableRow);
    }

    var options = {
      pageOrientation: 'landscape', // can also be 'portrait'

      content: con,
      //pageSize: 'A4',
      pageMargins: [30, 45, 30, 20],
      styles: {
        subheader: {
          margin: [0, 0, 0, 0]
        },
        divButton: {
          background: '#e9e9ed'
        }
      }
    };

    pdfMake.createPdf(options).download(fileNameExport);
  }

  const exportToPDF = (
    clientReportCardDetails,
    image,
    selectedWorkmixOptions,
    fileNameExport,
    selectedOptions
  ) => {
    downloadPDF(
      clientReportCardDetails,
      image,
      selectedWorkmixOptions,
      fileNameExport,
      selectedOptions
    );
  };

  return {
    exportToPDF
  };
};
