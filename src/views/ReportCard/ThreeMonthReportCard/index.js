import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { useDispatch, useSelector } from 'react-redux';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { setNavItems, setMenuSelected } from 'src/actions';
import { useHistory } from 'react-router';
import ThreeMonthReportPage from './ThreeMonthReportPage';
import {
  threeMonthRoiOption,
  threeMonthRoiSelectedOptions
} from 'src/utils/constants';
const ThreeMonthCardModule = props => {
  const session = useSelector(state => state.session);
  const history = useHistory();
  const dispatch = useDispatch();
  const [kpiReportData, setKpiReportData] = useState({});
  let params = '';
  if (
    typeof props.history.location.state != 'undefined' &&
    props.history.location.state != null
  ) {
    params = props.history.location.state.ronumber;
  }
  useEffect(() => {
    const historyState = history?.location?.state || {};
    let visibility =
      history &&
      history.location &&
      history.location.state &&
      history.location.state.visibility
        ? history.location.state.visibility
        : 'private';
    const pickerStatus = true;
    const reportName = historyState.reportname || '';
    const startDate = historyState.startdate || '';
    const endDate = historyState.enddate || '';
    const kpiIds = historyState.kpi_ids || [];
    const createdBy = historyState.created_by || '';
    const parentval = historyState.parent || '';

    const pathname = history?.location?.pathname.split('/')[1] || '';
    const measuredMTH = historyState.currentmonthyear || '';
    const priorMTH = historyState.previousmonthyear || '';
    const selectedStoreIds =
      historyState.storeselected ||
      JSON.parse(localStorage.getItem('selectedStoreId'));
    const selectedRoiView =
      historyState.roiview || threeMonthRoiSelectedOptions;
    const workmixview = historyState.workmixview || [];

    var data = {
      parent: parentval,
      parentSelected: parentval,
      pickerStatus: pickerStatus,
      reportName: reportName,
      startDate: startDate,
      endDate: endDate,
      kpi_ids: kpiIds,
      //selStores: kpiIds,
      created_by: createdBy,
      visibility: visibility,
      kpiHomeToggle: session.kpiHomeToggle,
      isFrom: pathname,
      measuredMTH: measuredMTH,
      priorMTH: priorMTH,
      selectedStoreIds: selectedStoreIds,
      selectedRoiView: selectedRoiView,
      workmixview: workmixview
    };
    setKpiReportData(data);
  }, []);
  return (
    <Page title="3 Month Total">
      {props.keycloak.realmAccess.roles.includes('client') ||
      props.keycloak.realmAccess.roles.includes('user') ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <ThreeMonthReportPage history={history} kpiReportData={kpiReportData} />
      )}
    </Page>
  );
};

export default withKeycloak(ThreeMonthCardModule);
