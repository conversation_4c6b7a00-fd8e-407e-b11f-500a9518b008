import {
  Grid,
  FormControl,
  MenuItem,
  Select,
  InputLabel
} from '@material-ui/core';
import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import moment from 'moment';
import { getLast13Months, getLastThreeYears } from 'src/utils/Utils';

const useStyles = makeStyles({
  formControl: {
    padding: 8
  },
  label: {
    marginTop: 10,
    backgroundColor: '#fff'
  }
});
const MonthSelected = ({ mon1, setMon1, closedDate }) => {
  const classes = useStyles();
  const [queryMonth1, setQueryMonth1] = useState(mon1);

  const handleMonthchange1 = event => {
    setQueryMonth1(event.target.value);
    setMon1(event.target.value);
  };

  useEffect(() => {
    if (mon1) setQueryMonth1(mon1);
  }, [mon1]);
  return (
    <>
      <FormControl
        variant="outlined"
        margin="dense"
        className={classes.formControl}
      >
        <InputLabel
          htmlFor="outlined-month-1"
          margin="dense"
          className="month-label"
          //className={classes.label}
        >
          Measured MTH
        </InputLabel>
        <Select
          variant="outlined"
          label="Month 1"
          name="month-1"
          value={queryMonth1}
          onChange={handleMonthchange1}
          MenuProps={{
            anchorOrigin: {
              vertical: 'bottom',
              horizontal: 'left'
            },
            transformOrigin: {
              vertical: 'top',
              horizontal: 'left'
            },
            PaperProps: {
              style: {
                maxHeight: 500
                // marginTop: 120 // Creates space between select field and dropdown
              }
            },
            getContentAnchorEl: null, // Fixes dropdown position issue
            container: document.body // Ensures dropdown attaches to the body
          }}
        >
          {localStorage.getItem('versionFlag') === 'TRUE'
            ? getLastThreeYears()
                .filter(val => val !== closedDate)
                .map(val => (
                  <MenuItem key={val} value={val}>
                    {moment(val).format('MMM-YY')}
                  </MenuItem>
                ))
            : getLast13Months()
                .filter(val => val !== closedDate)
                .map(val => (
                  <MenuItem key={val} value={val}>
                    {moment(val).format('MMM-YY')}
                  </MenuItem>
                ))}
        </Select>
      </FormControl>
    </>
  );
};

export default MonthSelected;
