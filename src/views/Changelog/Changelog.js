import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Button,
  Divider,
  fade,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  IconButton
} from '@material-ui/core';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import React from 'react';

import 'react-grid-layout/css/styles.css';

import PropTypes from 'prop-types';

import Markdown from 'markdown-to-jsx';
import { withStyles } from '@material-ui/styles';
import 'src/styles.css';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import PageHeader from 'src/components/PageHeader';
import { withKeycloak } from '@react-keycloak/web';
var Dealer = process.env.REACT_APP_DEALER;

class Changelog extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    this.state = { htmlStr: '', openErrorDlg: false };
  }
  // componentWillMount() {
  //   const readmePath =
  //     localStorage.getItem('versionFlag') &&
  //     localStorage.getItem('versionFlag') == 'TRUE'
  //       ? require('src/docs/Changelog.md')
  //       : require('src/docs/Changelog-demo.md');

  //   fetch(readmePath)
  //     .then(response => response.text())
  //     .then(text => {
  //       this.setState({ htmlStr: text });
  //     });
  // }
  componentWillMount() {
    const readmePath =
      localStorage.getItem('versionFlag') &&
      localStorage.getItem('versionFlag') === 'TRUE'
        ? require('src/docs/Changelog.md')
        : require('src/docs/Changelog-demo.md');

    fetch(readmePath)
      .then(response => response.text())
      .then(text => {
        const client = 1; // Replace this with the actual logic for determining the client
        if (
          this.props.keycloak &&
          this.props.keycloak.realmAccess &&
          this.props.keycloak.realmAccess.roles &&
          this.props.keycloak.realmAccess.roles.includes('admin') == false &&
          this.props.keycloak.realmAccess.roles.includes('superadmin') == false
        ) {
          // Use a regex to remove or replace the content
          const updatedText = text.replace(
            /- Armatus Admin.<i goTo="KeycloakUserCreation"><\/i>/g,
            ''
          );
          this.setState({ htmlStr: updatedText });
        } else {
          this.setState({ htmlStr: text });
        }
      });
  }

  handleCancelAllStores = () => {
    this.setState({
      openErrorDlg: false
    });
  };
  loadDrilldown = (params, tab, gridType) => {
    // if (
    //   localStorage.getItem('selectedStoreId') &&
    //   localStorage.getItem('selectedStoreId').length > 1
    // ) {
    //   if (
    //     params == 'KPIReportComparative' ||
    //     params == 'KPIReportStoreComparative'
    //   ) {
    //     this.props.history.push('/KPIReportStoreComparative');
    //   } else if (params != 'Home' || params != 'KpiGraphics') {
    //     this.props.history.push('/Home');
    //   }
    // } else {
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      params != 'Home' &&
      params != 'KpiGraphics' &&
      params != 'KPIReportStoreComparative' &&
      params != 'MPIStats'
    ) {
      this.setState({
        openErrorDlg: true
      });
    } else {
      this.props.history.push({
        pathname: '/' + params,
        state:
          typeof tab != 'undefined'
            ? {
                tabSelection: tab
              }
            : typeof gridType != 'undefined'
            ? {
                gridType: gridType
              }
            : {}
      });
    }
    //}
  };
  render() {
    const { classes } = this.props;
    const headSpan = ({ children, ...props }) => (
      <div
        style={{
          color: '#ee7600',
          padding: '10px 0px',
          margin: '10px 0px',
          fontSize: 14,
          borderBottom: '1px solid #eee',
          borderTop: '1px solid #eee'
        }}
        {...props}
      >
        {children}
      </div>
    );
    const pSpan = ({ children, ...props }) => (
      <div
        style={{ padding: '10px 0px', fontSize: 16, fontWeight: 'bold' }}
        {...props}
      >
        {children}
      </div>
    );
    const pTag = ({ children, ...props }) => (
      <div style={{ fontSize: 16, fontWeight: 'bold' }} {...props}>
        {children}
      </div>
    );
    const iTag = ({ children, ...props }) => (
      <Tooltip title={<span style={{ fontSize: 11.5 }}>View Change</span>}>
        <IconButton
          size="small"
          classes="infoIcon"
          onClick={() =>
            this.loadDrilldown(props.goTo, props.tab, props.gridType)
          }
        >
          {children}
          <OpenInNewOutlinedIcon />
        </IconButton>
      </Tooltip>
    );
    const grid = ({ children, ...props }) => (
      <Grid class="data-div" style={{ display: 'flex' }} {...props}>
        {children}
      </Grid>
    );
    const div = ({ children, ...props }) => (
      <Grid
        xs={12}
        style={{ marginRight: 30, fontSize: 13, marginBottom: 10 }}
        {...props}
      >
        {children}
      </Grid>
    );
    const bTag = ({ children, ...props }) => (
      <div style={{ fontSize: 13, marginTop: 8, display: 'flex' }} {...props}>
        {children}
      </div>
    );
    const hTag = ({ children, ...props }) => (
      <div
        style={{ fontSize: 13, marginRight: 4, fontWeight: 'bold' }}
        {...props}
      >
        {children}
      </div>
    );
    //console.log('htmlStr===', this.state.htmlStr);
    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,
            //marginTop: '40px',
            paddingTop: '6px'
            //  height: '40px',
          }}
        >
          <Grid item xs={12} style={{ justifyContent: 'left' }}>
            {/* <PageHeader
              hideTitle={false}
              title={'Changelog'}
              isFrom={'details'}
              changeLog={true}
            /> */}
            <Typography
              style={{ padding: 12, color: '#ee7600' }}
              variant="h4"
              className={clsx(classes.mainLabel, 'main-title')}
            >
              Site Release Log
            </Typography>
            {/* <Divider></Divider> */}
            <div className={clsx(classes.markdownText, 'markdown')}>
              <Markdown
                children={this.state.htmlStr}
                options={{
                  overrides: {
                    h1: {
                      component: headSpan
                    },
                    h3: {
                      component: pSpan
                    },
                    Grid: {
                      component: grid
                    },
                    div: {
                      component: div
                    },
                    p: {
                      component: pTag
                    },
                    i: {
                      component: iTag
                    },
                    body: {
                      component: bTag
                    },
                    h4: {
                      component: hTag
                    }
                  }
                }}
              />
              {/* <ReactMarkdown
                children=
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              /> */}
            </div>
          </Grid>
          <Dialog
            //fullWidth
            //maxWidth="sm"
            aria-labelledby="confirmation-dialog-title"
            open={this.state.openErrorDlg}
          >
            {/* <DialogTitle id="confirmation-dialog-title">
                <Typography
                  variant="h5"
                  color="primary"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Sign Out
                </Typography>
              </DialogTitle> */}
            <DialogContent dividers>
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                {/* This option is not available from this page. */}
                This page is disabled for All stores
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.handleCancelAllStores} color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        </Paper>
      </div>
    );
  }
}

Changelog.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};
const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  back: {
    marginRight: 10,
    float: 'right'
  },
  markdownText: {
    paddingLeft: 40,
    // paddingTop: 30,
    paddingBottom: 20,
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: 15
  }
});
// export default withStyles(styles)(Changelog);
export default withStyles(styles)(withKeycloak(Changelog));
