import React from 'react';
import Page from 'src/components/Page';
import Changelog from './Changelog';
import { useHistory } from 'react-router';

function Changelogs() {
  const history = useHistory();
  const handleRefresh = status => {
    //dispatch(setRefreshStatus(status));
  };
  const handleReload = status => {
    // dispatch(setReloadStatus(status));
  };
  return (
    <Page title="Changelog">
      <Changelog
        handleRefresh={handleRefresh.bind(this)}
        handleReload={handleReload.bind(this)}
        history={history}
      />
    </Page>
  );
}

export default Changelogs;
