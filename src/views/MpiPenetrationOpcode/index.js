import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import MpiPenetrationOpcode from './MpiPenetrationOpcode';
import { useDispatch, useSelector } from 'react-redux';
const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function MpiPenetrationOpcodes() {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  return (
    <Page className={classes.root} title="Fixedops Mapper Opcodes">
      <MpiPenetrationOpcode session={session}/>
    </Page>
  );
}

export default MpiPenetrationOpcodes;
