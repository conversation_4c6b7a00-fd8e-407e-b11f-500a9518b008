import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Toolbar,
  Grid
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getQualifiedMapperOpcodes,
  getFixedopsVsMapperOpCategorization,
  getMpiOpcodesDetails
} from 'src/utils/hasuraServices';
var Dealer = process.env.REACT_APP_DEALER;

class MpiPenetrationOpcode extends React.Component {
  componentDidUpdate(prevProps) {
    if (
      prevProps.session.storeSelected &&
      JSON.parse(localStorage.getItem('selectedStoreId'))
    ) {
      if (
        JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
        JSON.parse(prevProps.session.storeSelected)[0]
      ) {
        console.log(
          'stores=',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
            JSON.parse(prevProps.session.storeSelected)[0]
        );
        this.getAgGridData();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      showRefresh: 'none',
      disableTable: 'all',
      rawGridApi: {},
      tabSelection: 'one',
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      columnDefs: [
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          field: 'opcode',
          cellClass: 'textAlignopcode',
          width: 100,

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Opcode Desc',
          chartDataType: 'series',
          field: 'opcodeDescription',
          width: 300,
          flex: 1,
          tooltipField: 'opcodeDescription',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Op Category',
          chartDataType: 'series',
          width: 120,
          field: 'opcategory',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          }
        }
        // {
        //   headerName: 'Store Id',
        //   chartDataType: 'series',
        //   field: 'storeId',
        //   width: 87,
        //   cellClass: 'textAlign',
        //
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   comparator: function(valueA, valueB) {
        //     return valueA - valueB;
        //   },
        //   cellStyle() {
        //     return { textAlign: 'right', border: ' 0px white' };
        //   }
        // }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: false,
        suppressMovable: false
      },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'right'
          }
        },
        {
          id: 'textAlignopcode',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        }
      ]
    };
  }

  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '$0.00';
    }
  };
  formatCellValueHrs = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '$0.00';
    }
  };
  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    // this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    // getQualifiedMapperOpcodes((result) => {
    //     this.setState({ isLoading: false });
    //     if (result.data.dbdReferencesGetFixedOpsAndMapperQualifiedOpcodes.nodes) {
    //     this.setState({
    //       rowData: result.data.dbdReferencesGetFixedOpsAndMapperQualifiedOpcodes.nodes
    //     });
    //   }
    // });statelessDbdReferencesGetFixedopsVsMapperOpCategorization

    getMpiOpcodesDetails(result => {
      this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRwGetMpiOpcodesDetails.nodes) {
        console.log(
          'data values123=',
          result.data.statelessCcPhysicalRwGetMpiOpcodesDetails.nodes
        );
        this.setState({
          rowData: result.data.statelessCcPhysicalRwGetMpiOpcodesDetails.nodes
        });
      }
    });
  }

  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      // columnKeys: ['makesId', 'manufacturer', 'validMakes'],
      fileName: 'Fixed Ops Vs Mapper - Opcode Categorization',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Fixed Ops Vs Mapper - Opcode Categorization'
            },
            mergeAcross: 3
          }
        ]
      ]
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };

  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  render() {
    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,
            marginTop: '20px',
            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            indicatorColor="secondary"
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
          >
            <Tab label={<div>MPI Penetration Opcodes</div>} value="one" />
            {/* <Tab
              style={{ textTransform: 'none', paddingRight: 182 }}
              label={<div>Non-Qualified Opcodes</div>}
              value="two"
            /> */}
            <Tooltip title="Export To Excel">
              <Link
                style={{ paddingTop: 17, paddingRight: 19, cursor: 'pointer' }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>
          </Tabs>
        </Paper>
        {/* {this.state.tabSelection == "two"
            ? (
              <NonQualifiedMapperOpcodes />
            )
            : null} */}
        {/* {this.state.tabSelection == "one" ? (
          <div>
            <Paper square style={{ marginLeft: 8, marginRight: 8, height: 29 }}>
              <Toolbar>
                <Grid container justify="flex-start" style={{ padding: '5px' }}>
                  <Tooltip title="Export To Excel">
                    <Link
                      style={{
                        marginTop: -28,
                        marginLeft: 'auto',
                        cursor: 'pointer'
                      }}
                      onClick={this.onBtExport}
                    >
                      <ExportIcon />
                    </Link>
                  </Tooltip>
                </Grid>
              </Toolbar>
            </Paper>
          </div>
        ) : null } */}
        {this.state.isLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: window.innerHeight - 190 + 'px',
            width: '98.8%',
            margin: 8,
            display: this.state.tabSelection === 'two' ? 'none' : 'block'
            // pointerEvents: disableTable
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            sideBar={this.state.sideBar}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            floatingFilter={true}
            enableRangeSelection={true}
            animateRows={true}
            enableCharts={true}
            suppressRowClickSelection={true}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}

export default MpiPenetrationOpcode;
