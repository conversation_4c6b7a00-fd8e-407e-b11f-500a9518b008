import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  Divider,
  fade,
  LinearProgress,
  Paper,
  Tooltip,
  Typography
} from '@material-ui/core';

import React from 'react';

import 'react-grid-layout/css/styles.css';

import PropTypes from 'prop-types';

import Markdown from 'markdown-to-jsx';
import { withStyles } from '@material-ui/styles';
import 'src/styles.css';

import PageHeader from 'src/components/PageHeader';
var Dealer = process.env.REACT_APP_DEALER;

class Changelog extends React.Component {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    this.state = { htmlStr: '' };
  }
  componentWillMount() {
    const readmePath = require('src/docs/Changelog-internal.md');

    fetch(readmePath)
      .then(response => response.text())
      .then(text => {
        this.setState({ htmlStr: text });
      });
  }
  render() {
    const { classes } = this.props;
    const headSpan = ({ children, ...props }) => (
      <div
        style={{
          color: '#ee7600',
          padding: '10px 0px',
          margin: '10px 0px',
          fontSize: 13,
          borderBottom: '1px solid #eee',
          borderTop: '1px solid #eee'
        }}
        {...props}
      >
        {children}
      </div>
    );
    const pSpan = ({ children, ...props }) => (
      <div
        style={{ padding: '10px 0px', fontSize: 16, fontWeight: 'bold' }}
        {...props}
      >
        {children}
      </div>
    );
    const pTag = ({ children, ...props }) => (
      <div style={{ fontSize: 16, fontWeight: 'bold' }} {...props}>
        {children}
      </div>
    );
    const grid = ({ children, ...props }) => (
      <Grid class="data-div" style={{ display: 'flex' }} {...props}>
        {children}
      </Grid>
    );
    const div = ({ children, ...props }) => (
      <Grid
        xs={6}
        style={{ marginRight: 30, fontSize: 13, marginBottom: 10 }}
        {...props}
      >
        {children}
      </Grid>
    );
    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,
            //marginTop: '40px',
            paddingTop: '6px'
            //  height: '40px',
          }}
        >
          <Grid item xs={12} style={{ justifyContent: 'left' }}>
            {/* <PageHeader
              hideTitle={false}
              title={'Changelog'}
              isFrom={'details'}
              changeLog={true}
            /> */}
            <Typography
              style={{ padding: 12, color: '#ee7600' }}
              variant="h4"
              className={clsx(classes.mainLabel, 'main-title')}
            >
              Site Release Log
            </Typography>
            <Divider></Divider>
            <div className={clsx(classes.markdownText, 'markdown')}>
              <Markdown
                children={this.state.htmlStr}
                options={{
                  overrides: {
                    h1: {
                      component: headSpan
                    },
                    h3: {
                      component: pSpan
                    },
                    Grid: {
                      component: grid
                    },
                    div: {
                      component: div
                    },
                    p: {
                      component: pTag
                    }
                  }
                }}
              />
              {/* <ReactMarkdown
                children=
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeRaw]}
              /> */}
            </div>
          </Grid>
        </Paper>
      </div>
    );
  }
}

Changelog.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};
const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  back: {
    marginRight: 10,
    float: 'right'
  },
  markdownText: {
    paddingLeft: 40,
    paddingTop: 30,
    paddingBottom: 20,
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: 15
  }
});
export default withStyles(styles)(Changelog);
