import React from 'react';
import Page from 'src/components/Page';
import Changelog from './ChangelogInternal';

function changelog() {
  const handleRefresh = status => {
    //dispatch(setRefreshStatus(status));
  };
  const handleReload = status => {
    // dispatch(setReloadStatus(status));
  };
  return (
    <Page title="Changelog">
      <Changelog
        handleRefresh={handleRefresh.bind(this)}
        handleReload={handleReload.bind(this)}
      />
    </Page>
  );
}

export default changelog;
