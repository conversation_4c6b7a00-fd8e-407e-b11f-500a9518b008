import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tooltip,
  Link,
  FormControl,
  RadioGroup,
  InputLabel,
  Select,
  MenuItem
} from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import {
  getGridorMatrixDetails,
  getGridorMatrixPeriods,
  getGridorMatrixPayTypesByGridType
} from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { Button } from '@material-ui/core';
import RestoreIcon from '@material-ui/icons/Restore';
import ExportIcon from '@material-ui/icons/GetApp';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import moment from 'moment';
import { ReactSession } from 'react-client-session';
import { color } from 'highcharts';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class LaborGridPricing extends React.Component {
  componentWillMount() {
    console.log('enter=1');
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    console.log('enter=12');
    // if(ReactSession.get("serviceAdvisors") != undefined) {console.log("enter=123",ReactSession.get("serviceAdvisors"),this.state.serviceAdvisors);
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {console.log("enter=1234");
    //     this.setState({ serviceAdvisors: ReactSession.get("serviceAdvisors") });
    //     this.getAgGridData();
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      console.log('enter=12345');
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        console.log('enter=123456');
        this.setState({ isLoading: true });
        this.setState({ gridDoorRate: '' });
        this.setState({ selectedGrid: '' });
        if (this.props.history.location.state) {
          this.props.history.location.state.gridDoorRate = '';
          this.props.history.location.state.selectedGrid = '';
        }
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData();
      }
    }
  }
  componentDidMount() {
    console.log('enter=1234567');
    getGridorMatrixPeriods(
      'grid',
      this.state.selectedPayType,
      '',
      this.state.storeId,
      this.state.selectedGridFor,
      result => {
        if (result) {
          let resultArr = lodash.uniq(result);
          console.log('enter=1234567', resultArr);

          this.setState({
            gridPeriods: resultArr.sort(function(a, b) {
              return a - b;
            })
          });
        }
      }
    );
  }
  constructor(props) {
    super(props);
    let payTypeSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridType &&
      this.props.payTypes &&
      this.props.payTypes.length > 0 &&
      this.props.payTypes.includes(this.props.history.location.state.gridType)
        ? this.props.history.location.state.gridType
        : this.props.selectedPayType;
    let selectedGridFor =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedGridFor &&
      this.props.gridFor &&
      this.props.gridFor.length > 0 &&
      this.props.gridFor.includes(
        this.props.history.location.state.selectedGridFor
      )
        ? this.props.history.location.state.selectedGridFor
        : this.props.selectedGridFor;
    let gridDoorRate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridDoorRate
        ? this.props.history.location.state.gridDoorRate
        : '';
    let gridInstallDate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridDate
        ? this.props.history.location.state.gridDate
        : '';
    let selectedGrid =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      (this.props.history.location.state.selectedGrid ||
        this.props.history.location.state.selectedGrid == '')
        ? this.props.history.location.state.selectedGrid
        : 1;
    if (payTypeSelected == 'C') {
      payTypeSelected = 'Customer';
    } else if (payTypeSelected == 'I') {
      payTypeSelected = 'Internal';
    }
    let showAllJobs =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : false;
    let storeId =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.storeId
        ? this.props.history.location.state.storeId
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      selectedGrid: selectedGrid,
      selectedPayType: payTypeSelected,
      payTypes: this.props.payTypes,
      gridPeriods: [],
      gridDoorRate: gridDoorRate,
      gridInstallDate: gridInstallDate,
      showAllJobs: showAllJobs,
      storeId: storeId,
      selectedGridFor: selectedGridFor,
      gridFor: this.props.gridFor,
      columnDefs: [
        {
          headerName: 'Hours Sold',
          field: 'hours',
          width: 90,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        // {
        //   headerName: 'Door Rate',
        //   field: 'doorRate',
        //   width: 90,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'right', border: ' 0px white' };
        //   },
        // },
        {
          headerName: '0',
          field: 'col0OrpriceStartRange',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },

        {
          headerName: '0.1',
          field: 'col1OrpriceEndRange',
          width: 100,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.2',
          field: 'col2OraddPercentage',
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.3',
          field: 'col3',
          width: 100,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.4',
          field: 'col4',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.5',
          field: 'col5',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.6',
          field: 'col6',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.7',
          field: 'col7',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.8',
          field: 'col8',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: '0.9',
          field: 'col9',
          width: 100,
          suppressMenu: true,
          unSortIcon: true,
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      overlayNoRowsTemplate:
        '<span style="padding: 10px; font-size: 12.5px;">No data found for the selected Grid Type and Door Rate combination</span>',
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          dataType: 'DateTime',
          numberFormat: {
            format: 'mm/dd/yy'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  formatCellValueRate = params => {
    if (params.value && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };

  resetRawData = () => {
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
    window.sortState = {};
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };

  onBtExport = () => {
    var params = {
      sheetName: 'Labor Grid(s)',
      fileName: 'Labor Grid(s)',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Labor Grid(s)'
            },
            mergeAcross: 3
          }
        ],
        [
          {
            data: {
              type: 'String',
              value:
                this.state.selectedGrid == 1 ? 'Current Grid' : 'Prior Grid'
            },
            mergeAcross: 1
          },
          {
            data: {
              type: 'String',
              value: 'Grid / Door Install Date  : ' + this.state.gridDate
            },
            mergeAcross: 2
          },
          {
            data: {
              type: 'String',
              value: '1 Hour Door Rate  : ' + this.state.doorRate
            }
          }
        ],
        []
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  onGridReady = params => {
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridColumnApi: params.columnApi });
    if (
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.pageType &&
      this.props.history.location.state.pageType == 'FleetAccounts'
    ) {
      if (this.state.selectedPayType && this.state.payTypes.length > 0) {
        this.setState({
          payTypes: this.state.payTypes.filter(
            item => item == this.state.selectedPayType
          )
        });
      }
      if (this.state.selectedGridFor && this.state.gridFor.length > 0) {
        this.setState({
          gridFor: this.state.gridFor.filter(
            item => item.gridorpartsfor == this.state.selectedGridFor
          )
        });
      }
    }
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    let resultArr = [];
    let dataArr = [];
    var gridDate = '';
    var doorRate = '';
    let dataArrDoorRate = [];
    var storeDate = '';
    getGridorMatrixDetails(
      'grid',
      this.state.selectedPayType,
      '',
      this.state.storeId,
      this.state.selectedGridFor,
      result => {
        this.setState({ isLoading: false });

        if (
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        ) {
          resultArr =
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes;
          // if (this.state.selectedGrid) {
          //   dataArr = resultArr.filter(
          //     item => item.gridOrder == this.state.selectedGrid
          //   );
          // }

          if (this.state.gridDoorRate != '') {
            dataArrDoorRate = resultArr.filter(
              item => item.doorRate == this.state.gridDoorRate
            );

            if (
              this.state.gridInstallDate != '' &&
              dataArrDoorRate.length > 0
            ) {
              dataArrDoorRate = dataArrDoorRate.filter(
                item => item.createdDate == this.state.gridInstallDate
              );
            }

            if (dataArrDoorRate.length > 0 && this.state.selectedGrid == '') {
              const unique = [
                ...new Set(dataArrDoorRate.map(item => item.gridOrder))
              ];
              var min = Math.min(...unique);
              this.setState({ selectedGrid: min });
              dataArr = dataArrDoorRate.filter(item => item.gridOrder == min);
            } else if (dataArrDoorRate.length > 0 && this.state.selectedGrid) {
              dataArr = resultArr.filter(
                item => item.gridOrder == this.state.selectedGrid
              );
            } else if (resultArr.length > 0 && this.state.selectedGrid) {
              dataArr = resultArr.filter(
                item => item.gridOrder == this.state.selectedGrid
              );
            }
          } else if (
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.pageType == 'FleetAccounts' &&
            this.state.gridInstallDate
          ) {
            resultArr = resultArr.filter(
              item => item.createdDate == this.state.gridInstallDate
            );
            var min = '';
            if (resultArr.length > 0) {
              const unique = [
                ...new Set(resultArr.map(item => item.gridOrder))
              ];
              min = Math.min(...unique);
            } else {
              min = 1;

              this.setState({
                storeDate: this.state.gridInstallDate,
                gridDate: this.state.gridInstallDate
              });
            }
            this.setState({ selectedGrid: min });
            this.setState({
              gridPeriods: this.state.gridPeriods.filter(item => item == min)
            });
            dataArr = resultArr.filter(item => item.gridOrder == min);
          } else {
            if (this.state.selectedGrid) {
              dataArr = resultArr.filter(
                item => item.gridOrder == this.state.selectedGrid
              );
            } else {
              this.setState({ selectedGrid: this.state.gridPeriods[0] });
              dataArr = resultArr.filter(
                item => item.gridOrder == this.state.selectedGrid
              );
            }
          }
          if (dataArr.length > 0) {
            gridDate = moment(dataArr[0].createdDate).format('MM/DD/YY');
            doorRate = '$' + dataArr[0].doorRate;
            storeDate = moment(dataArr[0].storeInstallDate).format('MM/DD/YY');
          }
          this.setState({
            rowData: dataArr,
            gridDate: gridDate,
            doorRate: doorRate,
            storeDate: storeDate
          });
        }
      }
    );
  }

  handleclickBackBtn = params => {
    this.props.history.push({
      pathname: '/FleetAccounts',
      state: {}
    });
  };

  handleclick = params => {
    this.props.history.push({
      pathname:
        this.props.history.location.state &&
        this.props.history.location.state.pageType == 'FleetAccounts'
          ? '/FleetAccounts'
          : localStorage.getItem('versionFlag') == 'TRUE'
          ? '/LaborMisses'
          : '/LaborGridMisses',
      state: {
        selectedFilter: this.props.history.location.state.selectedFilter,
        selectedToggle: this.props.history.location.state.selectedToggle,
        selectedMonthYear: this.props.history.location.state.selectedMonthYear,
        parent: this.props.history.location.state.parent,
        previousToggle: this.props.history.location.state.previousToggle,
        payType: this.props.history.location.state.payType,
        gridType:
          this.props.history.location.state.pageType != 'FleetAccounts'
            ? this.props.history.location.state.gridType
            : this.props.history.location.state.gridRateValueStart,
        previousPayType: this.props.history.location.state.PrevPayType,
        previousGridType: this.props.history.location.state.PrevGridType,
        showAllJobs: this.state.showAllJobs,
        filterStart: this.props.history.location.state.filterStart,
        filterEnd: this.props.history.location.state.filterEnd,
        selectedGridType: this.props.history.location.state.selectedGridType,
        customerName: this.props.history.location.state.customerName
          ? this.props.history.location.state.customerName
          : this.props.history.location.state.fleetName,
        selectedPayType: this.props.history.location.state.selectedPayType
      }
    });
  };

  handleChange = event => {
    this.setState({ selectedGrid: event.target.value }, function() {
      this.getAgGridData();
    });
  };

  handleChangeGridType = event => {
    this.setState({ isLoading: true });
    this.setState({ selectedGridFor: event.target.value }, function() {
      getGridorMatrixPayTypesByGridType(
        'paytype_grid',
        event.target.value,
        this.state.storeId,
        result => {
          if (result) {
            let data = result;
            this.setState({
              selectedPayType: data[0],
              payTypes: data
            });
            // this.getAgGridData();
            this.getMetrixPeriods();
          }
        }
      );
    });
  };

  getMetrixPeriods() {
    getGridorMatrixPeriods(
      'grid',
      this.state.selectedPayType,
      '',
      this.state.storeId,
      this.state.selectedGridFor,
      result => {
        if (result) {
          let resultArr = lodash.uniq(result);
          let resultArrSorted = resultArr.sort(function(a, b) {
            return a - b;
          });
          this.setState({
            gridPeriods: resultArrSorted,
            selectedGrid: resultArrSorted.includes(3)
              ? this.state.selectedGrid
              : resultArrSorted[0],
            isLoading: false
          });
          this.getAgGridData();
        }
      }
    );
  }

  handleChangePayType = event => {
    this.setState({ isLoading: true });
    this.setState({ selectedPayType: event.target.value }, function() {
      // getGridorMatrixPeriods(
      //   'grid',
      //   this.state.selectedPayType,
      //   '',
      //   this.state.storeId,
      //   this.state.selectedGridFor,
      //   result => {
      //     if (result) {
      //       let resultArr = lodash.uniq(result);
      //       let resultArrSorted = resultArr.sort(function(a, b) {
      //         return a - b;
      //       });
      //       this.setState({
      //         gridPeriods: resultArrSorted,
      //         selectedGrid: resultArrSorted.includes(3)
      //           ? this.state.selectedGrid
      //           : resultArrSorted[0]
      //       });
      //     }
      //   }
      // );
      this.getMetrixPeriods();
      // this.getAgGridData();
    });
  };
  toPascalCase = str => {
    return str
      .replace(/(\w)(\w*)/g, (match, p1, p2) => {
        return p1.toUpperCase() + p2.toLowerCase();
      })
      .replace(/\s+/g, '');
  };
  render() {
    const { classes } = this.props;
    var gridDate = '';
    var doorRate = '';
    var storeDate = '';
    if (this.state.rowData.length > 0) {
      gridDate = moment(this.state.rowData[0].createdDate).format('MM/DD/YY');
      doorRate = '$' + this.state.rowData[0].doorRate;
      storeDate = moment(this.state.rowData[0].storeInstallDate).format(
        'MM/DD/YY'
      );
    }

    return (
      <div>
        <Page title={'Labor Grid(s)'}></Page>
        <Paper
          square
          style={{
            margin: 8,

            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
            cursor: 'default'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant="fullWidth"
            TabIndicatorProps={{
              style: {
                backgroundColor: '#e7eef3'
              }
            }}
            id={'lineTab'}
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
            style={{ cursor: 'default' }}
          >
            {this.props.history &&
              this.props.history.location.state &&
              (this.props.history.location.state.pageType == 'LaborMisses' ||
                this.props.history.location.state.pageType ==
                  'FleetAccounts') && (
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={this.handleclick}
                  style={{ width: 'auto', top: '9px' }}
                  fullWidth={false}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              )}

            <Tab
              label={<div style={{ color: '#084588' }}> Labor Grid(s)</div>}
              value="one"
              style={{
                textTransform: 'none',
                pointerEvents: 'none',
                borderColor: '#e7eef3'
              }}
            />

            <Button
              variant="contained"
              className={clsx(classes.back, 'reset-btn')}
              id="reset-layout"
              onClick={this.resetRawData}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>

        <Paper
          square
          style={{
            margin: 8,
            marginLeft: 8,
            height: 50,
            paddingTop: 4,
            paddingLeft: 4,
            display: 'flex'
          }}
        >
          <div>
            <FormControl
              margin="dense"
              variant="outlined"
              style={{
                minWidth: 120,
                marginLeft: 10,
                paddingRight: 10
              }}
            >
              <InputLabel
                htmlFor="outlined-age-native-simple"
                margin="dense"
                shrink={true}
                style={{
                  marginTop: 2,
                  backgroundColor: '#fff',
                  marginLeft: '-2px',
                  width: '72px'
                }}
              >
                Grid Period
              </InputLabel>
              <Select
                margin="dense"
                variant="outlined"
                label="Filter By"
                name="duration"
                className={'laborPartsGrid'}
                value={this.state.selectedGrid}
                onChange={this.handleChange}
                MenuProps={{
                  getContentAnchorEl: null,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  }
                }}
              >
                {this.state.gridPeriods.length > 0 ? (
                  this.state.gridPeriods.map(item => (
                    <MenuItem value={item}>
                      {item == 1 ? 'Current' : 'Prior ' + Number(item - 1)}
                    </MenuItem>
                  ))
                ) : (
                  <MenuItem value={1}>Current</MenuItem>
                )}
              </Select>
            </FormControl>

            <FormControl
              margin="dense"
              variant="outlined"
              style={{
                minWidth: 120,
                marginLeft: 10,
                paddingRight: 10
                // display:
                //   this.state.payTypes.length > 0 ? 'inline-block' : 'none'
              }}
            >
              <InputLabel
                htmlFor="outlined-age-native-simple"
                margin="dense"
                shrink={true}
                style={{
                  marginTop: 2,
                  backgroundColor: '#fff',
                  marginLeft: '-2px',
                  width: '62px'
                }}
              >
                Grid Type
              </InputLabel>
              <Select
                margin="dense"
                variant="outlined"
                label="Filter By"
                name="duration"
                className={'laborPartsGrid'}
                value={
                  // this.state.selectedGridFor.includes('cust_fleet')
                  //   ? 'Customer Fleet'
                  this.state.selectedGridFor
                }
                onChange={this.handleChangeGridType}
                MenuProps={{
                  getContentAnchorEl: null,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  }
                }}
              >
                {this.state.gridFor.map(item => (
                  <MenuItem value={item.gridorpartsfor}>
                    {item.dipsGridorpartsfor}
                    {/* {item.includes('cust_fleet')
                      ? 'Customer Fleet'
                      : item.includes('paytype_fleet')
                      ? 'Pay type Fleet'
                      : this.toPascalCase(item)} */}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl
              margin="dense"
              variant="outlined"
              style={{
                minWidth: 120,
                marginLeft: 10,
                paddingRight: 10,
                display:
                  this.state.payTypes.length > 0 ? 'inline-block' : 'none'
              }}
            >
              <InputLabel
                htmlFor="outlined-age-native-simple"
                margin="dense"
                shrink={true}
                style={{
                  marginTop: 2,
                  backgroundColor: '#fff',
                  marginLeft: '-2px',
                  width: '52px'
                }}
              >
                Grid For
              </InputLabel>
              <Select
                margin="dense"
                variant="outlined"
                label="Filter By"
                name="duration"
                className={'laborPartsGrid'}
                value={this.state.selectedPayType}
                onChange={this.handleChangePayType}
                MenuProps={{
                  getContentAnchorEl: null,
                  anchorOrigin: {
                    vertical: 'bottom',
                    horizontal: 'left'
                  },
                  transformOrigin: {
                    vertical: 'top',
                    horizontal: 'left'
                  }
                }}
              >
                {this.state.payTypes.map(item => (
                  <MenuItem value={item}>{item}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>

          <div className={clsx(classes.dataAsOf)}>
            {this.state.storeDate ? (
              <Typography
                variant="h6"
                align="right"
                style={{ fontSize: 12, color: '#7987a1', fontWeight: 'bold' }}
              >
                <div
                  style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <div>{'Store Install Date' + '\xa0\xa0'}</div>
                    <div style={{ width: 62 }}>{':'}</div>
                    <div className={classes.dataAsOfValue}>
                      {this.state.storeDate}
                    </div>
                  </div>
                </div>
              </Typography>
            ) : null}
            {this.state.gridDate ? (
              <Typography
                variant="h6"
                align="right"
                style={{ fontSize: 12, color: '#7987a1', fontWeight: 'bold' }}
              >
                <div
                  style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                  <div
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                  >
                    <div>{'FOPC Calculated Date From' + '\xa0\xa0' + ' :'}</div>
                    <div className={classes.dataAsOfValue}>
                      {this.state.gridDate}
                    </div>
                    <div>
                      {
                        '(This may be different from store install date if no prior pricing provided).'
                      }
                    </div>
                  </div>
                </div>
              </Typography>
            ) : null}
            {/* <Typography
              variant="h6"
              align="right"
              style={{ fontSize: 12, color: '#7987a1', fontWeight: 'bold' }}
            >
              <div
                 style={this.state.gridDate ? 
                  { display: 'flex', justifyContent: 'space-between' } :
                  { display: 'flex', justifyContent: 'space-between', marginTop: 11 }}
              >
                <div className={this.state.gridDate ? classes.inputText : classes.inputTextRate}>
                  {'1 Hour Door Rate'}
                </div>
                <div>{'\xa0\xa0'+':'+'\xa0\xa0'}</div>
                <div className={classes.inputValue}>
                  {this.state.doorRate ? this.state.doorRate: '$'+this.state.gridDoorRate}
                </div>
              </div>
            </Typography> */}
          </div>
        </Paper>

        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab-labor-pricing"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            height: window.innerHeight - 203 + 'px',
            // height:(window.innerHeight-215)+'px',
            alignContent: 'center',
            marginLeft: '8px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            enableRangeSelection={false}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            excelStyles={this.state.excelStyles}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            floatingFilter={true}
            suppressRowClickSelection={true}
            headerHeight={this.state.headerHeight}
            onFilterChanged={this.onFilterChanged}
            suppressContextMenu={true}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
            suppressDragLeaveHidesColumns={true}
          />
        </div>
      </div>
    );
  }
}

const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },

  dataAsOf: {
    marginRight: 32,
    float: 'left',
    marginTop: 1,
    marginLeft: '1%',
    '@media (max-width: 1440px)': {
      marginLeft: '0% !important'
    }
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3,
    marginRight: 8
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: 8,
    width: 'auto'
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  inputText: {
    width: 125,
    textAlign: 'left'
  },
  inputTextRate: {
    width: 97,
    textAlign: 'left'
  },
  inputValue: {
    marginRight: 11
  }
});

export default withStyles(styles)(LaborGridPricing);
