import React, { useState, useEffect } from 'react';
import Page from 'src/components/Page';
import LaborGridPricing from './LaborGridPricing';
import { useHistory } from 'react-router';
import {
  getGridorMatrixPayTypeDetails,
  getAllGridForDetails,
  getGridorMatrixPayTypesByGridType
} from 'src/utils/hasuraServices';
import { CircularProgress, Grid } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));

function KPILaborGridPricing() {
  const classes = useStyles();
  const history = useHistory();
  const [payTypes, setPayType] = useState([]);
  const [selectedPayType, setSelectedPayType] = useState('');
  const [isLoading, setLoading] = useState(true);
  const [isStoreLoading, setStoreLoading] = useState(false);
  const [gridForData, setGridForData] = useState([]);
  const [selectedGridFor, setSelectedGridFor] = useState('');
  const [allGridData, setAllGridData] = useState([]);

  const session = useSelector(state => state.session);
  let storeId =
    history.location && history.location.state && history.location.state.storeId
      ? history.location.state.storeId
      : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
  useEffect(() => {
    setStoreLoading(false);
    getAllGridForDetails('paytype_grid', storeId, results => {
      // let gridFor = lodash
      //   .uniqBy(results, 'gridorpartsfor')
      //   .map(obj => obj.gridorpartsfor);
      let gridFor = lodash.uniqBy(results, 'gridorpartsfor').map(obj => obj);
      if (
        history &&
        history.location &&
        history.location.state &&
        history.location.state.selectedGridFor
      ) {
        setSelectedGridFor(history.location.state.selectedGridFor);
      } else {
        setSelectedGridFor(gridFor[0].gridorpartsfor);
      }
      setGridForData(gridFor);
      setAllGridData(results);
    });
    if (selectedGridFor != '') {
      getGridorMatrixPayTypesByGridType(
        'paytype_grid',
        selectedGridFor,
        storeId,
        result => {
          let data = result;
          setPayType(data);
          setSelectedPayType(data[0]);
          setLoading(false);
          setStoreLoading(true);
        }
      );
    } else {
      setLoading(false);
      setStoreLoading(true);
    }

    // getGridorMatrixPayTypeDetails('paytype_grid', storeId, result => {
    //   let data = result;
    //   if (
    //     result.includes('Heavy Duty') ||
    //     result.includes('Fleet') ||
    //     result.includes('Commercial')
    //   ) {
    //     if (
    //       result[0] == 'Heavy Duty' ||
    //       result[0] == 'Fleet' ||
    //       (result[0] == 'Commercial' && !result.includes('RV'))
    //     ) {
    //       data = result.reverse();
    //     }
    //   }
    //   if (
    //     result.includes('Commercial') &&
    //     result.includes('RV') &&
    //     result.includes('Retail')
    //   ) {
    //     data = ['Retail', 'Commercial', 'RV'];
    //   }
    //   if (
    //     result.includes('Warranty') &&
    //     result.includes('Standard') &&
    //     result.includes('Fleet')
    //   ) {
    //     data = ['Standard', 'Warranty', 'Fleet'];
    //   }
    //   if (result.includes('Internal')) {
    //     data = result.sort();
    //   }
    //   if (result.includes('Highline')) {
    //     data = result.reverse();
    //   }
    //   if (
    //     result.includes('Diesel') ||
    //     result.includes('HD & Hybrid') ||
    //     result.includes('Electric')
    //   ) {
    //     data = result.reverse();
    //   }
    //   if (
    //     result.includes('Honda Grid') &&
    //     result.includes('Volvo Grid') &&
    //     result.includes('Merc-Benz Grid')
    //   ) {
    //     data = ['Honda Grid', 'Volvo Grid', 'Merc-Benz Grid'];
    //   }
    //   if (
    //     result.includes('25-5500/Dsl') &&
    //     result.includes('Car/Lt Trk') &&
    //     result.includes('Flt-Sptr')
    //   ) {
    //     data = ['Car/Lt Trk', '25-5500/Dsl', 'Flt-Sptr'];
    //   }
    //   if (
    //     result.includes('Hyundai') &&
    //     result.includes('Genesis') &&
    //     result.includes('Customer')
    //   ) {
    //     data = ['Hyundai', 'Genesis', 'Customer'];
    //   }
    //   if (
    //     result.includes('EV') &&
    //     result.includes('Medium/HD') &&
    //     result.includes('Standard')
    //   ) {
    //     data = ['Standard', 'Medium/HD', 'EV'];
    //   }
    //   setPayType(data);
    //   setSelectedPayType(data[0]);
    //   setLoading(false);
    //   setStoreLoading(true);
    // });
  }, [selectedPayType, selectedGridFor, session.storeSelected]);
  return (
    <Page title="Labor Grid(s)">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      ((history &&
        history.location.state &&
        history.location.state.pageType != 'LaborMisses') ||
        history.location.state == undefined) ? (
        <Redirect to="/errors/error-404" />
      ) : isLoading == true && selectedPayType == '' ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid>
      ) : !isStoreLoading ? null : (
        <LaborGridPricing
          history={history}
          payTypes={payTypes}
          gridFor={gridForData}
          selectedGridFor={selectedGridFor}
          selectedPayType={selectedPayType}
          session={session}
          storeId={storeId}
          allGridData={allGridData}
        />
      )}
    </Page>
  );
}

export default KPILaborGridPricing;
