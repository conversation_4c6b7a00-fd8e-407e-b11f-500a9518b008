import React from 'react';
import Tooltip from '@material-ui/core/Tooltip';

const CheckboxRenderer = props => {
  const handleChange = () => {
    // Handle checkbox change if needed
  };

  return (
    typeof props.data != 'undefined' && (
      <Tooltip title={props.value === 'private' ? 'Private' : 'Public'}>
        <div>
          <input
            type="checkbox"
            checked={props.value === 'private'}
            onChange={handleChange}
            disabled
          />
        </div>
      </Tooltip>
    )
  );
};

export default CheckboxRenderer;
