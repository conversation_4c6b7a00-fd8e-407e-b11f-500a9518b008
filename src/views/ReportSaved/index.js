import React from 'react';

import { useDispatch, useSelector } from 'react-redux';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { useHistory } from 'react-router';
import Page from 'src/components/Page';
import ReportSaved from './ReportSaved';
function ReportSavedIndex(props) {
  const session = useSelector(state => state.session);
  const history = useHistory();
  //console.log('ReportSavedIndex>>>>', props);
  return (
    <Page
      title={
        window.location.pathname === '/SavedReports'
          ? 'Saved Reports'
          : 'Reports Saved'
      }
    >
      {window.location.pathname === '/SavedReports' ? (
        props.keycloak.realmAccess.roles.includes('client') ||
        props.keycloak.realmAccess.roles.includes('user') ? (
          <Redirect to="/errors/error-404" />
        ) : (
          <ReportSaved session={session} history={history} />
        )
      ) : (
        <ReportSaved session={session} history={history} />
      )}

      {/* // <ReportSaved session={session} history={history} /> */}
    </Page>
  );
}

export default withKeycloak(ReportSavedIndex);
// export default ReportSavedIndex;
