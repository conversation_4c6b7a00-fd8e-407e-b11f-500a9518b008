import React from 'react';
import PropTypes from 'prop-types';
import { AgGridReact } from 'ag-grid-react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { withKeycloak } from '@react-keycloak/web';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Button,
  TextField,
  Radio,
  RadioGroup,
  FormControl
} from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import EditIcon from '@material-ui/icons/Edit';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import Checkbox<PERSON>enderer from './CheckboxRenderer'; // Import your updated CheckboxRenderer
import {
  getKpiSavedReportDetails,
  insertKPIReportName,
  deleteKPIReport,
  getEmail,
  getRecipientEmails,
  getKpiSavedReportCardDetails,
  insertKPIMonthCardReportName,
  insertLaborMissesSavededReport,
  insertKpiOnelineSavedReport,
  insertMpiStatsReportName,
  insertPartsMissesSavededReport
} from 'src/utils/hasuraServices';
import { getTimeZone } from '../../utils/Utils';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import MuiTableCell from '@material-ui/core/TableCell';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import RestoreIcon from '@material-ui/icons/Restore';
import Checkbox from '@material-ui/core/Checkbox';
import Fade from '@material-ui/core/Fade';
import moment from 'moment';
import SuccessSnackbar from '../KPIReportComparative/SuccessSnackbar';
import clsx from 'clsx';
import { withStyles, makeStyles } from '@material-ui/styles';
import EmailDialog from 'src/components/EmailDialogSavedReports';
import MailIcon from '@material-ui/icons/Mail';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import ViewEmailDialog from 'src/components/ViewEmailDialog';
import { ReactSession } from 'react-client-session';
import $ from 'jquery';

var lodash = require('lodash');

const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);
class ListReport extends React.Component {
  componentDidUpdate(prevProps) {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        window.sortStatesSavedRts = {};
        window.filterStatesSavedRts = {};
        const groupColumn = this.state.groupColumn;
        if (
          JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
          this.state.rawGridApi &&
          groupColumn
        ) {
          groupColumn[2]['hide'] = false;
          groupColumn[2]['suppressToolPanel'] = false;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          $('#data-tab').css('width', '1025px');
          if (window.sortStatesSavedRts && this.gridApi) {
            this.gridApi.setSortModel(window.sortStatesSavedRts);
          }
          if (window.colStatesSavedRts && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStatesSavedRts);
          }
        } else if (this.state.rawGridApi) {
          groupColumn[2]['hide'] = true;
          groupColumn[2]['suppressToolPanel'] = true;
          this.state.rawGridApi.setColumnDefs([]);
          this.state.rawGridApi.setColumnDefs(groupColumn);
          $('#data-tab').css('width', '880px');
          if (window.sortStatesSavedRts && this.gridApi) {
            this.gridApi.setSortModel(window.sortStatesSavedRts);
          }
          if (window.colStatesSavedRts && this.gridColumnApi) {
            this.gridColumnApi.setColumnState(window.colStatesSavedRts);
          }
        }
        this.getAgGridData();
      }
    }

    // if (
    //   prevProps.session.storeSelected &&
    //   JSON.parse(localStorage.getItem('selectedStoreId'))
    // ) {
    //   if (
    //     JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
    //     JSON.parse(prevProps.session.storeSelected)[0]
    //   ) {
    //     this.getAgGridData();
    //   }
    // }
  }
  componentDidMount() {
    let userList = [];
    getEmail('NULL', 'NULL', 'view', 'store', result => {
      this.setState({ isLoading: true });
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        userList.push(
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
            item => item.value
          )
        );
        this.setState({
          isLoading: false,
          mailUsers: userList
        });
      } else {
        this.setState({
          isLoading: false,
          mailUsers: []
        });
      }
    });
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);

    this.state = {
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      openSaveDlg: false,
      openSnackbar: false,
      selectedRow: [],
      privateChecked: true,
      errorReport: false,
      requiredText: false,
      reportName: '',
      deleteClicked: false,
      open: false,
      openDialogueDelete: false,
      openDialogue: false,
      mailUsers: [],
      emailListDialog: [],
      mailUserData: [],
      openMailDialogue: false,
      openAllStoresDialog: false,
      selectedType: 'private',
      store: localStorage.getItem('selectedStoreId'),
      expanded: 0,
      columnDefs: [
        {
          headerName: 'Created By',
          field: 'created_by',
          tooltipField: 'created_by',
          width: 150,
          minWidth: 150,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          rowGroup: true,
          hide: true,
          editable: false
        },
        {
          headerName: 'Report Name',
          field: 'report_name',
          width: 150,
          minWidth: 150,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          onCellClicked: this.handleCellClicked,
          editable: false,
          tooltipField: 'report_name',
          cellStyle: {
            color: '#003d6b',
            fontWeight: 'bold',
            cursor: 'pointer'
          },
          comparator: (valueA, valueB) => {
            // Check if values are defined and replace spaces
            const cleanValueA = valueA
              ? valueA.replace(/\s+/g, '').toLowerCase()
              : '';
            const cleanValueB = valueB
              ? valueB.replace(/\s+/g, '').toLowerCase()
              : '';

            if (cleanValueA < cleanValueB) {
              return -1;
            }
            if (cleanValueA > cleanValueB) {
              return 1;
            }
            return 0;
          }
        },
        {
          headerName: 'Store',
          field: 'store_name',
          width: 140,
          dataType: 'string',
          suppressMenu: true,
          unSortIcon: true,
          tooltipField: 'store_name',
          hide: true,
          suppressToolPanel: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },
        {
          headerName: 'Type',
          field: 'kpi_report_type',
          // width: 105,
          // minWidth: 105,
          width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          tooltipValueGetter: params => this.formatCellCasing(params),
          valueFormatter: this.formatCellCasing,
          filterParams: {
            valueFormatter: this.formatCellCasing
          }
        },
        {
          headerName: 'Private',
          field: 'visibility',
          width: 105,
          minWidth: 105,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          editable: false,
          valueFormatter: this.formatCellValue,
          filterParams: {
            valueFormatter: this.formatCellValue
          },
          comparator: (valueA, valueB) => {
            // Compare raw values, not formatted ones
            if (valueA === 'private' && valueB !== 'private') return 1;
            if (valueA !== 'private' && valueB === 'private') return -1;
            return 0;
          }
          // cellRendererFramework: CheckboxRenderer
        },
        {
          headerName: 'Created On',
          field: 'created_on',
          width: 120,
          minWidth: 120,
          cellClass: 'textAlign',
          suppressMenu: true,
          editable: false,
          unSortIcon: true,
          valueFormatter: this.formatCellValueMonthYear,
          filterParams: {
            valueFormatter: this.formatCellValueMonthYear
          }
        },
        {
          headerName: 'Actions',
          field: '',
          width: 200,
          minWidth: 200,
          editable: false,
          suppressMenu: true,
          flex: 1,
          filter: 'agSetColumnFilter',
          sortable: false,
          suppressFilter: true,
          //   hide:
          //     typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //     this.props.keycloak.realmAccess.roles.length >= 1 &&
          //     this.props.keycloak.realmAccess.roles.includes('superadmin') ===
          //       true
          //       ? false
          //       : true,
          cellRendererFramework: params =>
            typeof params.data != 'undefined' && (
              <span style={{ display: 'flex', justifyContent: 'space-evenly' }}>
                <Tooltip title="Edit">
                  <EditIcon
                    htmlColor="rgb(0, 61, 107)"
                    id={`edit${params.rowIndex}`}
                    className={
                      params.data?.created_by !==
                        localStorage.getItem('userID') &&
                      !this.props.keycloak?.realmAccess?.roles?.includes(
                        'superadmin'
                      )
                        ? this.props.classes.copyIconDisable
                        : this.props.classes.editIcon
                    }
                    onClick={() => this.handleEditClick(params.data)}
                  />
                </Tooltip>
                <Tooltip title="Rename and Copy">
                  <FileCopyOutlinedIcon
                    htmlColor="rgb(0, 61, 107)"
                    id={`edit${params.rowIndex}`}
                    className={
                      params.data?.created_by ===
                        localStorage.getItem('userID') ||
                      params.data?.visibility === 'public' ||
                      this.props.keycloak?.realmAccess?.roles?.includes(
                        'superadmin'
                      )
                        ? this.props.classes.copyIcon
                        : this.props.classes.copyIconDisable
                    }
                    onClick={() => this.handleCopyClick(params.data)}
                  />
                </Tooltip>
                <Tooltip title="Delete">
                  <DeleteIcon
                    htmlColor="rgb(0, 61, 107)"
                    className={
                      (params.data?.created_by ===
                        localStorage.getItem('userID') &&
                        (params.data.visibility === 'private' ||
                          params.data.visibility === 'public')) ||
                      this.props.keycloak?.realmAccess?.roles?.includes(
                        'superadmin'
                      )
                        ? this.props.classes.deleteIcon
                        : this.props.classes.deleteIconDisable
                    }
                    id={`delete${params.rowIndex}`}
                    onClick={() => this.handleDeleteClick(params.data)}
                  />
                </Tooltip>
                <Tooltip title="Email">
                  <MailIcon
                    htmlColor="rgb(0, 61, 107)"
                    className={
                      params.data &&
                      params.data.created_by !==
                        localStorage.getItem('userID') &&
                      this.props.keycloak?.realmAccess?.roles &&
                      !this.props.keycloak.realmAccess.roles.includes(
                        'superadmin'
                      )
                        ? this.props.classes.mailIconDisable
                        : this.props.classes.mailIcon
                    }
                    id={`mail${params.rowIndex}`}
                    onClick={() => this.handleMailClick(params.data)}
                  />
                </Tooltip>
                <Tooltip title="View Mail Details">
                  <OpenInNewOutlinedIcon
                    htmlColor="rgb(0, 61, 107)"
                    className={this.props.classes.viewMailIcon}
                    id={'view' + params.rowIndex}
                    onClick={() => this.handleViewMailClick(params.data)}
                  />
                </Tooltip>
              </span>
            ),
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        }
      ],
      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        enableValue: true,
        sortable: true,
        editable: true,
        suppressMovable: true,
        filter: true,
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        suppressKeyboardEvent: params => params.event.keyCode === 13
      },
      autoGroupColumnDef: {
        headerName: 'Created By', // Header name for the group column
        field: 'created_by', // Field to group by
        cellRendererParams: {
          suppressCount: true // Optional: Suppress the row count in the group header
        },
        editable: false,
        suppressMenu: true,
        unSortIcon: true,
        cellClass: 'textAlign',
        tooltipValueGetter: params => params.value,
        cellStyle() {
          return { border: '0px white' };
        }
      },
      editType: 'fullRow',
      groupDefaultExpanded: 0
    };
  }
  onRowGroupOpened = event => {
    var rowNodeIndex = event && event.node && event.node.childIndex;
    // factor in child nodes so we can scroll to correct position
    var childCount =
      event && event.node && event.node.childrenAfterSort
        ? event.node.childrenAfterSort.length
        : 0;
    var newIndex = rowNodeIndex + 10;
    this.gridApi.ensureIndexVisible(newIndex);
    this.setState({ expanded: rowNodeIndex });
    this.state.gridcolumnApi.resetColumnState();
    // this.gridApi.setSortModel(null);
    // this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
  };
  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    // this.gridApi.redrawRows();
    this.gridApi.sizeColumnsToFit();
    if (
      this.props.history.location.state == undefined ||
      this.props.history.location.state == null
    ) {
      window.sortStatesSavedRts = {};
      window.filterStatesSavedRts = {};
    }

    this.setState({ gridcolumnApi: params.columnApi });
    this.getAgGridData();
    this.state.gridcolumnApi.resetColumnState();
    this.gridApi.setSortModel(window.sortStatesSavedRts);
    this.gridApi.setFilterModel(window.filterStatesSavedRts);
    this.setState({
      groupColumn: params.api.columnController.columnDefs
    });
    if (window.colStatesSavedRts) {
      this.state.gridcolumnApi.setColumnState(window.colStatesSavedRts);
    }
    const groupColumn = this.state.groupColumn;
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      this.state.rawGridApi &&
      groupColumn
    ) {
      groupColumn[2]['hide'] = false;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      $('#data-tab').css('width', '1000px');
      if (window.sortStatesSavedRts && this.gridApi) {
        this.gridApi.setSortModel(window.sortStatesSavedRts);
      }
      if (window.colStatesSavedRts && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStatesSavedRts);
      }
    } else if (this.state.rawGridApi) {
      groupColumn[2]['hide'] = true;
      this.state.rawGridApi.setColumnDefs([]);
      this.state.rawGridApi.setColumnDefs(groupColumn);
      $('#data-tab').css('width', '880px');
      if (window.sortStatesSavedRts && this.gridApi) {
        this.gridApi.setSortModel(window.sortStatesSavedRts);
      }
      if (window.colStatesSavedRts && this.gridColumnApi) {
        this.gridColumnApi.setColumnState(window.colStatesSavedRts);
      }
    }
    this.gridApi.redrawRows();
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValue = params => {
    if (
      params.value != null &&
      params.value != '' &&
      params.value != undefined
    ) {
      if (params.value == 'private') {
        return 'Yes';
      } else {
        return 'No';
      }
    } else {
      return '';
    }
  };

  formatCellCasing = params => {
    if (params.value != null && params.value != '') {
      function toPascalCase(str) {
        return str
          .split('_')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join('');
      }

      if (
        params.value === 'Client_Report_Card_1_Month' ||
        params.value === 'CP_1-Line-RO_Count_Over_60k' ||
        params.value === 'CP_1-Line-RO_Count_Under_60k' ||
        params.value === 'Client_Report_Card_3_Month_Total' ||
        params.value === 'MPI_Stats' ||
        params.value === 'Labor_Target_Misses' ||
        params.value === 'Parts_Target_Misses' ||
        params.value === 'CP_1-Line-RO_Count_Over_60k' ||
        params.value === 'CP_1-Line-RO_Count_Under_60k'
      ) {
        return params.value.replace(/_/g, ' ');
      } else {
        const pascalCaseValue =
          params.value.charAt(0).toUpperCase() +
          params.value.slice(1).toLowerCase();
        return pascalCaseValue;
      }
    } else {
      return '';
    }
  };
  handleCancelAllStores = () => {
    this.setState({ openAllStoresDialog: false });
  };
  getAgGridData = () => {
    console.log('filterStatesSavedRts33 ', window.filterStatesSavedRts);
    this.setState({ isLoading: true });
    let storeId;
    if (JSON.parse(localStorage.getItem('selectedStoreId')).length > 1) {
      storeId = JSON.parse(localStorage.getItem('allPermittedStores')).join(
        ','
      );
    } else {
      storeId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    }

    let userRole =
      this.props.keycloak &&
      this.props.keycloak.realmAccess &&
      this.props.keycloak.realmAccess.roles
        ? this.props.keycloak.realmAccess.roles[0]
        : null;

    var offset = getTimeZone();
    const userID = localStorage.getItem('userID');
    if (window.location.pathname === '/SavedReports') {
      getKpiSavedReportCardDetails(
        storeId,
        userRole,
        userID,
        offset,
        result => {
          this.setState({ isLoading: false });
          if (
            result.data
              .statelessDbdKpiScorecardGetKpiReportcardSavedReportDetails.json
          ) {
            var roData = JSON.parse(
              result.data
                .statelessDbdKpiScorecardGetKpiReportcardSavedReportDetails.json
            );
            roData = roData.map(item => {
              if (item.created_on.includes('T')) {
                // Format created_on to only include the date part
                item.created_on = moment(item.created_on).format('YYYY-MM-DD');
              }
              return item;
            });
            let filteredData = roData.filter(
              item => item.visibility === 'public' || item.created_by === userID
            );
            let filterUser = roData.filter(item => item.created_by === userID);
            let otherData = roData.filter(item => item.created_by !== userID);
            let filteredDatavalue = filterUser.concat(otherData);
            let filterUserData = filteredData.filter(
              item => item.created_by !== userID
            );
            let filterUserData123 = filterUser.concat(filterUserData);

            // if (
            //   JSON.parse(localStorage.getItem('selectedStoreId')).length > 1
            // ) {
            if (userRole === 'superadmin') {
              this.setState({
                rowData: filteredDatavalue
              });
            } else {
              this.setState({
                rowData: filterUserData123
              });
            }
            // } else {
            //   if (userRole === 'superadmin') {
            //     this.setState({
            //       rowData: filteredDatavalue
            //       // rowData: roData
            //     });
            //   } else {
            //     this.setState({
            //       // rowData: filteredData
            //       rowData: filterUserData123
            //     });
            //   }
            // }

            if (window.filterStatesSavedRts != undefined) {
              this.filterByValue();
            }
            if (filterUser.length > 0) {
              this.onFirstDataRendered();
            }
          } else {
            this.setState({
              rowData: []
            });
          }
        }
      );
    } else {
      getKpiSavedReportDetails(storeId, userRole, userID, offset, result => {
        this.setState({ isLoading: false });
        if (result.data.statelessDbdKpiScorecardGetKpiSavedReportDetails.json) {
          var roData = JSON.parse(
            result.data.statelessDbdKpiScorecardGetKpiSavedReportDetails.json
          );
          roData = roData.map(item => {
            if (item.created_on.includes('T')) {
              // Format created_on to only include the date part
              item.created_on = moment(item.created_on).format('YYYY-MM-DD');
            }
            return item;
          });
          let filteredData = roData.filter(
            item => item.visibility === 'public' || item.created_by === userID
          );
          let filterUser = roData.filter(item => item.created_by === userID);
          let otherData = roData.filter(item => item.created_by !== userID);
          let filteredDatavalue = filterUser.concat(otherData);
          let filterUserData = filteredData.filter(
            item => item.created_by !== userID
          );
          let filterUserData123 = filterUser.concat(filterUserData);

          if (JSON.parse(localStorage.getItem('selectedStoreId')).length > 1) {
            if (userRole === 'superadmin') {
              let filtereStoreData = filteredDatavalue.filter(
                item => item.kpi_report_type === 'store'
              );
              this.setState({
                rowData: filtereStoreData
              });
            } else {
              let filtereStoreData = filterUserData123.filter(
                item => item.kpi_report_type === 'store'
              );
              this.setState({
                rowData: filtereStoreData
              });
            }
          } else {
            if (userRole === 'superadmin') {
              this.setState({
                rowData: filteredDatavalue
                // rowData: roData
              });
            } else {
              this.setState({
                // rowData: filteredData
                rowData: filterUserData123
              });
            }
          }
          if (window.filterStatesSavedRts != undefined) {
            this.filterByValue();
          }
          if (filterUser.length > 0) {
            this.onFirstDataRendered();
          }
        } else {
          this.setState({
            rowData: []
          });
        }
      });
    }
  };

  filterByValue = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterStatesSavedRts);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };

  handleEditClick(params) {
    window.sortStatesSavedRts = this.gridApi.getSortModel();
    window.filterStatesSavedRts = this.gridApi.getFilterModel();
    window.colStatesSavedRts = this.gridColumnApi.getColumnState();
    if (window.location.pathname === '/SavedReports') {
      if (params.kpi_report_type == 'Client_Report_Card_1_Month') {
        this.props.history.push({
          pathname: '/OneMonthReport',
          state: {
            reportname: params.report_name,
            toggleduration: params.toggle_duration,
            startdate: params.start_date,
            enddate: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            kpi_ids: params.kpi_ids,
            action: 'edit',
            visibility: params.visibility,
            created_by: params.created_by,
            kpireporttype: params.kpi_report_type,
            previousmonthyear: params.previous_month_year,
            currentmonthyear: params.current_month_year,
            storeselected: params.store_selected,
            roiview: params.roi_view,
            workmixview: params.workmix_view
          }
        });
      } else {
        this.props.history.push({
          pathname: '/ThreeMonthReport',
          state: {
            reportname: params.report_name,
            toggleduration: params.toggle_duration,
            startdate: params.start_date,
            enddate: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            kpi_ids: params.kpi_ids,
            action: 'edit',
            visibility: params.visibility,
            created_by: params.created_by,
            kpireporttype: params.kpi_report_type,
            previousmonthyear: params.previous_month_year,
            currentmonthyear: params.current_month_year,
            storeselected: params.store_selected,
            roiview: params.roi_view,
            workmixview: params.workmix_view
          }
        });
      }
    } else {
      if (params.kpi_report_type == 'store') {
        this.props.history.push({
          pathname: '/KPIReportStoreComparative',
          state: {
            report_name: params.report_name,
            toggle_duration: params.toggle_duration,
            start_date: params.start_date,
            end_date: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            kpi_ids: params.kpi_ids,
            expanded_kpis: params.hidden_kpi_section,
            hidden_kpi_numb: params.hidden_kpi_numb,
            kpi_sort_order:
              params.kpi_sort_order && params.kpi_sort_order[0] == null
                ? { row: '', order: 0 }
                : params.kpi_sort_order && params.kpi_sort_order[0]
                ? params.kpi_sort_order[0]
                : '',
            selected_goals: params.selected_goals,
            action: 'edit',
            visibility: params.visibility,
            created_by: params.created_by
          }
        });
      } else if (params.kpi_report_type == 'tech') {
        this.props.history.push({
          pathname: '/KPIReportTechComparative',
          state: {
            report_name: params.report_name,
            toggle_duration: params.toggle_duration,
            start_date: params.start_date,
            end_date: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            kpi_ids: params.kpi_ids,
            expanded_kpis: params.hidden_kpi_section,
            hidden_kpi_numb: params.hidden_kpi_numb,
            kpi_sort_order:
              params.kpi_sort_order && params.kpi_sort_order[0] == null
                ? { row: '', order: 0 }
                : params.kpi_sort_order && params.kpi_sort_order[0]
                ? params.kpi_sort_order[0]
                : '',
            selected_goals: params.selected_goals,
            action: 'edit',
            visibility: params.visibility,
            created_by: params.created_by
          }
        });
      } else if (params.kpi_report_type == 'advisor') {
        this.props.history.push({
          pathname: '/KPIReportComparative',
          state: {
            report_name: params.report_name,
            toggle_duration: params.toggle_duration,
            start_date: params.start_date,
            end_date: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            kpi_ids: params.kpi_ids,
            expanded_kpis: params.hidden_kpi_section,
            hidden_kpi_numb: params.hidden_kpi_numb,
            kpi_sort_order:
              params.kpi_sort_order && params.kpi_sort_order[0] == null
                ? { row: '', order: 0 }
                : params.kpi_sort_order && params.kpi_sort_order[0]
                ? params.kpi_sort_order[0]
                : '',
            selected_goals: params.selected_goals,
            action: 'edit',
            visibility: params.visibility,
            created_by: params.created_by
          }
        });
      } else if (params.kpi_report_type == 'MPI_Stats') {
        this.props.history.push({
          pathname: '/MPIStats',
          state: {
            report_name: params.report_name,
            filterStart: params.start_date,
            filterEnd: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            action: 'edit',
            created_by: params.created_by,
            visibility: params.visibility,
            // isExpanded: clickedCellGroupIndex,
            filterText: params.toggle_duration,
            reportType: params.kpi_report_type,
            draggedColumn: params.header_sort_list,
            filterColumns: params.filter_columns,
            sortColumns: params.kpi_sort_order,
            selectedAdvisor: params.service_advisor,
            selectedTech: params.technician,
            checkedColumns: params.display_columns
          }
        });
      } else if (
        params.kpi_report_type == 'CP_1-Line-RO_Count_Under_60k' ||
        params.kpi_report_type == 'CP_1-Line-RO_Count_Over_60k'
      ) {
        this.props.history.push({
          pathname: '/AnalyzeData',
          search: '?OneLine',
          state: {
            type: 'one_line_drilldown',
            report_name: params.report_name,
            filterStart: params.start_date,
            filterEnd: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            action: 'edit',
            created_by: params.created_by,
            visibility: params.visibility,
            // isExpanded: clickedCellGroupIndex,
            filterText: params.toggle_duration,
            reportType: params.kpi_report_type,
            draggedColumn: params.header_sort_list,
            filterColumns: params.filter_columns,
            sortColumns: params.kpi_sort_order,
            selectedAdvisor: params.service_advisor,
            selectedTech: params.technician,
            checkedColumns: params.display_columns,
            oneLineType:
              params.is_mileage_below == 'YES' ? 'Under 60K' : 'Over 60K'
          }
        });
      } else if (params.kpi_report_type == 'Labor_Target_Misses') {
        this.props.history.push({
          pathname: '/LaborMisses',
          state: {
            report_name: params.report_name,
            filterStart: params.start_date,
            filterEnd: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            action: 'edit',
            created_by: params.created_by,
            visibility: params.visibility,
            // isExpanded: clickedCellGroupIndex,
            filterText: params.toggle_duration,
            reportType: params.kpi_report_type,
            draggedColumn: params.header_sort_list,
            filterColumns: params.filter_columns,
            sortColumns: params.kpi_sort_order,
            selectedAdvisor: params.service_advisor,
            selectedTech: params.technician,
            checkedColumns: params.display_columns,
            jobType: params.job_type,
            gridType: params.grid_type,
            payType: params.paytype
          }
        });
      } else if (params.kpi_report_type == 'Parts_Target_Misses') {
        this.props.history.push({
          pathname: '/PartsMisses',
          state: {
            report_name: params.report_name,
            filterStart: params.start_date,
            filterEnd: params.end_date,
            parent: 'savedReports',
            store_id: params.store_id,
            action: 'edit',
            created_by: params.created_by,
            visibility: params.visibility,
            // isExpanded: clickedCellGroupIndex,
            filterText: params.toggle_duration,
            reportType: params.kpi_report_type,
            draggedColumn: params.header_sort_list,
            filterColumns: params.filter_columns,
            sortColumns: params.kpi_sort_order,
            selectedAdvisor: params.service_advisor,
            selectedTech: params.technician,
            checkedColumns: params.display_columns,
            jobType: params.job_type,
            gridType: params.grid_type,
            payType: params.paytype
          }
        });
      }
    }
  }

  handleDeleteClick(params) {
    this.setState({ deleteClicked: true });
    this.setState({ open: true });
    this.setState({ selectedRow: params });
  }

  handleCopyClick(params) {
    this.setState({ openSaveDlg: true });
    this.setState({ selectedRow: params });
    this.setState({ reportName: '' });
    this.setState({ selectedType: params.visibility });
  }
  getRowStyle = params => {
    if (params.data && params.data.visibility === 'private') {
      return { background: '#f2f2f2' }; // Grey background for private rows
    }
    return null; // Default background for other rows
  };
  handleCellClicked = params => {
    window.sortStatesSavedRts = this.gridApi.getSortModel();
    window.filterStatesSavedRts = this.gridApi.getFilterModel();
    window.colStatesSavedRts = this.gridColumnApi.getColumnState();
    const clickedCellGroupIndex = params.node.parent.rowIndex;
    if (JSON.parse(localStorage.getItem('selectedStoreId')).length > 1) {
      if (params.data.kpi_report_type == 'store') {
        this.props.history.push({
          pathname: '/KPIReportStoreComparative',
          state: {
            report_name: params.data.report_name,
            toggle_duration: params.data.toggle_duration,
            start_date: params.data.start_date,
            end_date: params.data.end_date,
            parent: 'savedReports',
            store_id: params.data.store_id,
            kpi_ids: params.data.kpi_ids,
            action: 'edit',
            expanded_kpis: params.data.hidden_kpi_section,
            hidden_kpi_numb: params.data.hidden_kpi_numb,
            kpi_sort_order:
              params.data.kpi_sort_order &&
              params.data.kpi_sort_order[0] == null
                ? { row: '', order: 0 }
                : params.data.kpi_sort_order && params.data.kpi_sort_order[0]
                ? params.data.kpi_sort_order[0]
                : '',
            selected_goals: params.data.selected_goals,
            created_by: params.data.created_by,
            visibility: params.data.visibility,
            isExpanded: clickedCellGroupIndex
          }
        });
      } else if (window.location.pathname === '/SavedReports') {
        if (params.data.kpi_report_type == 'Client_Report_Card_1_Month') {
          this.props.history.push({
            pathname: '/OneMonthReport',
            state: {
              reportname: params.data.report_name,
              toggleduration: params.data.toggle_duration,
              startdate: params.data.start_date,
              enddate: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              kpi_ids: params.data.kpi_ids,
              action: 'edit',
              visibility: params.data.visibility,
              created_by: params.data.created_by,
              kpireporttype: params.data.kpi_report_type,
              previousmonthyear: params.data.previous_month_year,
              currentmonthyear: params.data.current_month_year,
              storeselected: params.data.store_selected,
              roiview: params.data.roi_view,
              workmixview: params.data.workmix_view
            }
          });
        } else {
          this.props.history.push({
            pathname: '/ThreeMonthReport',
            state: {
              reportname: params.data.report_name,
              toggleduration: params.data.toggle_duration,
              startdate: params.data.start_date,
              enddate: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              kpi_ids: params.data.kpi_ids,
              action: 'edit',
              visibility: params.data.visibility,
              created_by: params.data.created_by,
              kpireporttype: params.data.kpi_report_type,
              previousmonthyear: params.data.previous_month_year,
              currentmonthyear: params.data.current_month_year,
              storeselected: params.data.store_selected,
              roiview: params.data.roi_view,
              workmixview: params.data.workmix_view
            }
          });
        }
      } else {
        this.setState({ openAllStoresDialog: true });
      }
    } else {
      if (window.location.pathname === '/SavedReports') {
        if (params.data.kpi_report_type == 'Client_Report_Card_1_Month') {
          this.props.history.push({
            pathname: '/OneMonthReport',
            state: {
              reportname: params.data.report_name,
              toggleduration: params.data.toggle_duration,
              startdate: params.data.start_date,
              enddate: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              kpi_ids: params.data.kpi_ids,
              action: 'edit',
              visibility: params.data.visibility,
              created_by: params.data.created_by,
              kpireporttype: params.data.kpi_report_type,
              previousmonthyear: params.data.previous_month_year,
              currentmonthyear: params.data.current_month_year,
              storeselected: params.data.store_selected,
              roiview: params.data.roi_view,
              workmixview: params.data.workmix_view
            }
          });
        } else {
          this.props.history.push({
            pathname: '/ThreeMonthReport',
            state: {
              reportname: params.data.report_name,
              toggleduration: params.data.toggle_duration,
              startdate: params.data.start_date,
              enddate: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              kpi_ids: params.data.kpi_ids,
              action: 'edit',
              visibility: params.data.visibility,
              created_by: params.data.created_by,
              kpireporttype: params.data.kpi_report_type,
              previousmonthyear: params.data.previous_month_year,
              currentmonthyear: params.data.current_month_year,
              storeselected: params.data.store_selected,
              roiview: params.data.roi_view,
              workmixview: params.data.workmix_view
            }
          });
        }
      } else {
        if (params.data.kpi_report_type == 'store') {
          this.props.history.push({
            pathname: '/KPIReportStoreComparative',
            state: {
              report_name: params.data.report_name,
              toggle_duration: params.data.toggle_duration,
              start_date: params.data.start_date,
              end_date: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              kpi_ids: params.data.kpi_ids,
              action: 'edit',
              expanded_kpis: params.data.hidden_kpi_section,
              hidden_kpi_numb: params.data.hidden_kpi_numb,
              kpi_sort_order:
                params.data.kpi_sort_order &&
                params.data.kpi_sort_order[0] == null
                  ? { row: '', order: 0 }
                  : params.data.kpi_sort_order && params.data.kpi_sort_order[0]
                  ? params.data.kpi_sort_order[0]
                  : '',
              selected_goals: params.data.selected_goals,
              created_by: params.data.created_by,
              visibility: params.data.visibility,
              isExpanded: clickedCellGroupIndex
            }
          });
        } else if (params.data.kpi_report_type == 'tech') {
          this.props.history.push({
            pathname: '/KPIReportTechComparative',
            state: {
              report_name: params.data.report_name,
              toggle_duration: params.data.toggle_duration,
              start_date: params.data.start_date,
              end_date: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              kpi_ids: params.data.kpi_ids,
              action: 'edit',
              expanded_kpis: params.data.hidden_kpi_section,
              hidden_kpi_numb: params.data.hidden_kpi_numb,
              kpi_sort_order:
                params.data.kpi_sort_order &&
                params.data.kpi_sort_order[0] == null
                  ? { row: '', order: 0 }
                  : params.data.kpi_sort_order && params.data.kpi_sort_order[0]
                  ? params.data.kpi_sort_order[0]
                  : '',
              selected_goals: params.data.selected_goals,
              created_by: params.data.created_by,
              visibility: params.data.visibility,
              isExpanded: clickedCellGroupIndex
            }
          });
        } else if (params.data.kpi_report_type == 'advisor') {
          this.props.history.push({
            pathname: '/KPIReportComparative',
            state: {
              report_name: params.data.report_name,
              toggle_duration: params.data.toggle_duration,
              start_date: params.data.start_date,
              end_date: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              kpi_ids: params.data.kpi_ids,
              action: 'edit',
              expanded_kpis: params.data.hidden_kpi_section,
              hidden_kpi_numb: params.data.hidden_kpi_numb,
              kpi_sort_order:
                params.data.kpi_sort_order &&
                params.data.kpi_sort_order[0] == null
                  ? { row: '', order: 0 }
                  : params.data.kpi_sort_order && params.data.kpi_sort_order[0]
                  ? params.data.kpi_sort_order[0]
                  : '',
              selected_goals: params.data.selected_goals,
              created_by: params.data.created_by,
              visibility: params.data.visibility,
              isExpanded: clickedCellGroupIndex
            }
          });
        } else if (params.data.kpi_report_type == 'MPI_Stats') {
          this.props.history.push({
            pathname: '/MPIStats',
            state: {
              report_name: params.data.report_name,
              filterStart: params.data.start_date,
              filterEnd: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              action: 'edit',
              created_by: params.data.created_by,
              visibility: params.data.visibility,
              isExpanded: clickedCellGroupIndex,
              filterText: params.data.toggle_duration,
              reportType: params.data.kpi_report_type,
              draggedColumn: params.data.header_sort_list,
              filterColumns: params.data.filter_columns,
              sortColumns: params.data.kpi_sort_order,
              selectedAdvisor: params.data.service_advisor,
              selectedTech: params.data.technician,
              checkedColumns: params.data.display_columns
            }
          });
        } else if (
          params.data.kpi_report_type == 'CP_1-Line-RO_Count_Under_60k' ||
          params.data.kpi_report_type == 'CP_1-Line-RO_Count_Over_60k'
        ) {
          this.props.history.push({
            pathname: '/AnalyzeData',
            search: '?OneLine',
            state: {
              type: 'one_line_drilldown',
              report_name: params.data.report_name,
              filterStart: params.data.start_date,
              filterEnd: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              action: 'edit',
              created_by: params.data.created_by,
              visibility: params.data.visibility,
              isExpanded: clickedCellGroupIndex,
              filterText: params.data.toggle_duration,
              reportType: params.data.kpi_report_type,
              draggedColumn: params.data.header_sort_list,
              filterColumns: params.data.filter_columns,
              sortColumns: params.data.kpi_sort_order,
              selectedAdvisor: params.data.service_advisor,
              selectedTech: params.data.technician,
              checkedColumns: params.data.display_columns,
              oneLineType:
                params.data.is_mileage_below == 'YES' ? 'Under 60K' : 'Over 60K'
            }
          });
        } else if (params.data.kpi_report_type == 'Labor_Target_Misses') {
          this.props.history.push({
            pathname: '/LaborMisses',
            state: {
              report_name: params.data.report_name,
              filterStart: params.data.start_date,
              filterEnd: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              action: 'edit',
              created_by: params.data.created_by,
              visibility: params.data.visibility,
              // isExpanded: clickedCellGroupIndex,
              filterText: params.data.toggle_duration,
              reportType: params.data.kpi_report_type,
              draggedColumn: params.data.header_sort_list,
              filterColumns: params.data.filter_columns,
              sortColumns: params.data.kpi_sort_order,
              selectedAdvisor: params.data.service_advisor,
              selectedTech: params.data.technician,
              checkedColumns: params.data.display_columns,
              jobType: params.data.job_type,
              gridType: params.data.grid_type,
              payType: params.data.paytype
            }
          });
        } else if (params.data.kpi_report_type == 'Parts_Target_Misses') {
          this.props.history.push({
            pathname: '/PartsMisses',
            state: {
              report_name: params.data.report_name,
              filterStart: params.data.start_date,
              filterEnd: params.data.end_date,
              parent: 'savedReports',
              store_id: params.data.store_id,
              action: 'edit',
              created_by: params.data.created_by,
              visibility: params.data.visibility,
              // isExpanded: clickedCellGroupIndex,
              filterText: params.data.toggle_duration,
              reportType: params.data.kpi_report_type,
              draggedColumn: params.data.header_sort_list,
              filterColumns: params.data.filter_columns,
              sortColumns: params.data.kpi_sort_order,
              selectedAdvisor: params.data.service_advisor,
              selectedTech: params.data.technician,
              checkedColumns: params.data.display_columns,
              jobType: params.data.job_type,
              gridType: params.data.grid_type,
              payType: params.data.paytype
            }
          });
        }
      }
    }
  };
  handleCheckboxChange = event => {
    // this.setState({ privateChecked: false });
    this.setState({ selectedType: event.target.value });
    if (event.target.value == 'private') {
      this.setState({ privateChecked: true });
    } else if (event.target.value == 'public') {
      this.setState({ privateChecked: false });
    }
  };
  handleCancelSaveReport = () => {
    this.setState({ openSaveDlg: false });
  };
  handleOkSaveReport = () => {
    var iStoreId = this.state.selectedRow.store_id
      ? this.state.selectedRow.store_id
      : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.setState({ deleteClicked: false });
    let store = this.state.reportName;
    const userId = localStorage.getItem('userID');
    const filterCol = JSON.stringify(this.state.selectedRow.filter_columns);
    const sortCol = JSON.stringify(this.state.selectedRow.kpi_sort_order);
    store = store.trim();
    if (store.length > 0) {
      if (
        this.state.selectedRow.kpi_report_type ===
          'Client_Report_Card_3_Month_Total' ||
        this.state.selectedRow.kpi_report_type === 'Client_Report_Card_1_Month'
      ) {
        const userRole =
          this.props &&
          this.props.keycloak &&
          this.props.keycloak.realmAccess &&
          this.props.keycloak.realmAccess.roles &&
          this.props.keycloak.realmAccess.roles[0];
        const userId = localStorage.getItem('userID');
        insertKPIMonthCardReportName(
          'insert',
          this.state.selectedRow.store_id,
          this.state.reportName,
          '',
          this.state.selectedRow.kpi_report_type ===
            'Client_Report_Card_3_Month_Total'
            ? this.state.selectedRow.start_date
            : null, // start date
          this.state.selectedRow.kpi_report_type ===
            'Client_Report_Card_3_Month_Total'
            ? this.state.selectedRow.end_date
            : null, // end date
          this.state.selectedRow.previous_month_year,
          this.state.selectedRow.current_month_year,
          userId,
          this.state.selectedRow.kpi_report_type,
          null,
          this.state.selectedType,
          userRole,
          this.state.selectedRow.workmix_view,
          this.state.selectedRow.store_selected,
          this.state.selectedRow.roi_view,

          result => {
            if (
              result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
                .results[0].status == 1
            ) {
              this.setState({ errorReport: '' });
              this.setState({ openSaveDlg: false });
              this.setState({ requiredText: false });
              this.setState({ openSnackbar: true });
              this.setState({ expanded: 0 });
              this.getAgGridData();
            } else {
              this.setState({ requiredText: true });
              this.setState({
                errorReport:
                  result.data
                    .statelessDbdKpiScorecardInsertKpireportcardSavedReport
                    .results[0].msg
              });
            }
          }
        );
      } else if (this.state.selectedRow.kpi_report_type == 'MPI_Stats') {
        insertMpiStatsReportName(
          'insert',
          iStoreId,
          this.state.reportName,
          userId,
          this.state.selectedRow.kpi_report_type,
          this.state.selectedType,
          this.props.keycloak.realmAccess.roles[0],
          sortCol,
          filterCol,
          this.state.selectedRow.technician,
          this.state.selectedRow.service_advisor,
          this.state.selectedRow.display_columns,
          this.state.selectedRow.header_sort_list,
          this.state.selectedRow.toggle_duration,
          result => {
            if (
              result.data
                .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
                .results[0].status == 1
            ) {
              this.setState({ errorReport: '' });
              this.setState({ openSaveDlg: false });
              this.setState({ requiredText: false });
              this.setState({ openSnackbar: true });
              this.setState({ expanded: 0 });
              this.getAgGridData();
            } else {
              this.setState({ requiredText: true });
              this.setState({
                errorReport:
                  result.data
                    .statelessDbdKpiScorecardInsertKpiScorecardMpiStatsSavedReport
                    .results[0].msg
              });
            }
          }
        );
      } else if (
        this.state.selectedRow.kpi_report_type ==
          'CP_1-Line-RO_Count_Over_60k' ||
        this.state.selectedRow.kpi_report_type ===
          'CP_1-Line-RO_Count_Under_60k'
      ) {
        const userRole =
          this.props &&
          this.props.keycloak &&
          this.props.keycloak.realmAccess &&
          this.props.keycloak.realmAccess.roles &&
          this.props.keycloak.realmAccess.roles[0];
        const userId = localStorage.getItem('userID');
        const filterCol = JSON.stringify(this.state.selectedRow.filter_columns);
        const sortCol = JSON.stringify(this.state.selectedRow.kpi_sort_order);
        insertKpiOnelineSavedReport(
          'insert',
          iStoreId,
          this.state.reportName,
          this.state.selectedRow.toggle_duration,
          this.state.selectedRow.kpi_report_type,
          this.state.selectedRow.service_advisor,
          this.state.selectedRow.technician,
          userId,
          this.state.selectedRow.kpi_report_type,

          this.state.selectedType,
          this.state.selectedRow.display_columns,
          sortCol,
          filterCol,
          this.state.selectedRow.header_sort_list,
          this.state.selectedRow.is_mileage_below,
          this.state.selectedRow.oneline_type,
          result => {
            if (
              result.data.statelessDbdKpiScorecardInsertKpiOnelineSavedReport
                .results[0].status == 1
            ) {
              this.setState({ errorReport: '' });
              this.setState({ openSaveDlg: false });
              this.setState({ requiredText: false });
              this.setState({ openSnackbar: true });
              this.setState({ expanded: 0 });
              this.getAgGridData();
            } else {
              this.setState({ requiredText: true });
              this.setState({
                errorReport:
                  result.data
                    .statelessDbdKpiScorecardInsertKpiOnelineSavedReport
                    .results[0].msg
              });
            }
          }
        );
      } else if (
        this.state.selectedRow.kpi_report_type == 'Labor_Target_Misses'
      ) {
        insertLaborMissesSavededReport(
          'insert',
          iStoreId,
          this.state.reportName,
          this.state.selectedRow.toggle_duration,
          userId,
          this.state.selectedRow.kpi_report_type,
          this.state.selectedType,
          this.props.keycloak.realmAccess.roles[0],
          sortCol,
          filterCol,
          this.state.selectedRow.technician,
          this.state.selectedRow.display_columns,
          this.state.selectedRow.grid_type,
          this.state.selectedRow.job_type,
          this.state.selectedRow.paytype,
          this.state.selectedRow.header_sort_list,
          this.state.selectedRow.service_advisor,
          result => {
            if (
              result.data.statelessDbdKpiScorecardInsertLabormissesSavedReport
                .results[0].status == 1
            ) {
              this.setState({ errorReport: '' });
              this.setState({ openSaveDlg: false });
              this.setState({ requiredText: false });
              this.setState({ openSnackbar: true });
              this.setState({ expanded: 0 });
              this.getAgGridData();
            } else {
              this.setState({ requiredText: true });
              this.setState({
                errorReport:
                  result.data
                    .statelessDbdKpiScorecardInsertLabormissesSavedReport
                    .results[0].msg
              });
            }
          }
        );
      } else if (
        this.state.selectedRow.kpi_report_type == 'Parts_Target_Misses'
      ) {
        insertPartsMissesSavededReport(
          'insert',
          iStoreId,
          this.state.reportName,
          this.state.selectedRow.toggle_duration,
          userId,
          this.state.selectedRow.kpi_report_type,
          this.state.selectedType,
          this.props.keycloak.realmAccess.roles[0],
          sortCol,
          filterCol,
          this.state.selectedRow.technician,
          this.state.selectedRow.display_columns,
          this.state.selectedRow.grid_type,
          this.state.selectedRow.job_type,
          this.state.selectedRow.paytype,
          this.state.selectedRow.header_sort_list,
          this.state.selectedRow.service_advisor,
          result => {
            if (
              result.data
                .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
                .results[0].status == 1
            ) {
              this.setState({ errorReport: '' });
              this.setState({ openSaveDlg: false });
              this.setState({ requiredText: false });
              this.setState({ openSnackbar: true });
              this.setState({ expanded: 0 });
              this.getAgGridData();
            } else {
              this.setState({ requiredText: true });
              this.setState({
                errorReport:
                  result.data
                    .statelessDbdKpiScorecardInsertKpiScorecardPartsTargetMissesSavedReport
                    .results[0].msg
              });
            }
          }
        );
      } else {
        insertKPIReportName(
          'insert',
          this.state.reportName,
          this.state.selectedRow.start_date,
          this.state.selectedRow.end_date,
          this.state.selectedRow.kpi_report_type,
          this.state.selectedRow.kpi_ids,
          iStoreId,
          this.props.keycloak.realmAccess.roles[0],
          this.state.selectedType,
          // this.state.privateChecked ? 'private' : 'public',
          this.state.selectedRow.toggle_duration,
          this.state.selectedRow.selected_goals,
          this.state.selectedRow.hidden_kpi_section,
          JSON.stringify(this.state.selectedRow.hidden_kpi_numb),
          JSON.stringify(this.state.selectedRow.kpi_sort_order),
          this.state.selectedRow.header_sort_list,
          result => {
            // if (
            //   result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results ==
            //   'Insert successful'
            // ) {
            if (
              result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                .results[0].status == 1
            ) {
              this.setState({ errorReport: '' });
              this.setState({ openSaveDlg: false });
              this.setState({ requiredText: false });
              this.setState({ openSnackbar: true });
              this.setState({ expanded: 0 });
              this.getAgGridData();
            } else {
              this.setState({ requiredText: true });
              this.setState({
                errorReport:
                  result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                    .results[0].msg
              });
            }
          }
        );
      }
    } else {
      this.setState({ requiredText: true });
      this.setState({
        errorReport: 'This is required!'
      });
    }
  };

  onChangeReportName = e => {
    // const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s]*$/;
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;

    if (!nameRegex.test(e.target.value) && e.target.value) {
      this.setState({ requiredText: '' });
    } else {
      this.setState({ errorReport: '' });
      this.setState({ reportName: e.target.value });
      this.setState({ requiredText: false });
    }
  };

  handleSnackbarClose = () => {
    this.setState({ openSnackbar: false });
  };

  handleCloseDelete = () => {
    this.setState({ open: false });
  };
  handleOkDelete = () => {
    window.sortStatesSavedRts = this.gridApi.getSortModel();
    window.filterStatesSavedRts = this.gridApi.getFilterModel();
    window.colStatesSavedRts = this.gridColumnApi.getColumnState();
    const userRole =
      this.props &&
      this.props.keycloak &&
      this.props.keycloak.realmAccess &&
      this.props.keycloak.realmAccess.roles &&
      this.props.keycloak.realmAccess.roles[0];
    const userId = localStorage.getItem('userID');
    if (window.location.pathname === '/SavedReports') {
      insertKPIMonthCardReportName(
        'delete',
        this.state.selectedRow.store_id,
        this.state.selectedRow.report_name,
        '',
        null,
        null,
        '',
        '',
        userId,
        this.state.selectedRow.kpi_report_type,
        null,
        '',
        userRole,
        '',
        '',
        '',

        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpireportcardSavedReport
              .results[0].status == 1
          ) {
            this.setState({ errorReport: '' });
            this.setState({ open: false });

            this.setState({ requiredText: false });
            this.setState({ openSnackbar: true });
            this.getAgGridData();
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                  .results[0].msg
            });
          }
        }
      );
    } else {
      deleteKPIReport(
        'delete',
        this.state.selectedRow.report_name,
        this.state.selectedRow.kpi_report_type,
        this.state.selectedRow.store_id,
        userRole,
        result => {
          if (
            result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results[0]
              .status == 1
          ) {
            this.setState({ errorReport: '' });
            this.setState({ open: false });

            this.setState({ requiredText: false });
            this.setState({ openSnackbar: true });
            this.getAgGridData();
          } else {
            this.setState({ requiredText: true });
            this.setState({
              errorReport:
                result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                  .results[0].msg
            });
          }
        }
      );
    }
  };
  handleCloseDeleteDialog = () => {
    this.setState({ openDialogueDelete: false, deleteClicked: false });
  };

  handleMailClick = params => {
    this.setState({ openDialogue: true });
    this.setState({ selectedRow: params });
    let userList = [];
    let type = params.kpi_report_type == 'store' ? 'store' : 'advisor';
    getEmail('NULL', 'NULL', 'view', type, result => {
      this.setState({ isLoading: true });
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        userList.push(
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
            item => item.value
          )
        );
        this.setState({
          isLoading: false,
          mailUsers: userList
        });
      } else {
        this.setState({
          isLoading: false,
          mailUsers: []
        });
      }
    });
    // getEmail('NULL', 'NULL', 'view', result => {
    //   this.setState({ isLoading: true });
    //   if (
    //     result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
    //     result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
    //   ) {
    //     userList.push(
    //       result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
    //         item => item.value
    //       )
    //     );
    //     this.setState({
    //       isLoading: false,
    //       mailUsers: userList
    //     });
    //   } else {
    //     this.setState({
    //       isLoading: false,
    //       mailUsers: []
    //     });
    //   }
    // });
  };

  handleViewMailClick = params => {
    let userList = [];
    let type = params.kpi_report_type == 'store' ? 'store' : 'advisor';

    getEmail('NULL', 'NULL', 'view', type, result => {
      this.setState({ isLoading: true });
      if (
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
        result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length > 0
      ) {
        userList.push(
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
            item => item.value
          )
        );
        this.setState({
          isLoading: false,
          mailUsers: userList
        });
      } else {
        this.setState({
          isLoading: false,
          mailUsers: []
        });
      }
    });
    this.setState({ selectedRow: params });
    getRecipientEmails(
      'view',
      params.store_id,
      params.report_name,
      params.kpi_report_type,
      null,
      null,
      null,
      null,
      null,
      null,
      null,
      result => {
        this.setState({ isLoading: true });
        if (
          result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
            .statelessDbdKpiScorecardKpiScorecardMailJobs
        ) {
          this.setState({
            openMailDialogue: true
          });
          this.setState({
            isLoading: false,
            mailUserData:
              result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
                .statelessDbdKpiScorecardKpiScorecardMailJobs
          });
        }
      }
    );
  };
  handleCloseEmail = () => {
    this.setState({ openDialogue: false });
    this.setState({ emailListDialog: [] });
  };
  handleCloseViewEmail = () => {
    this.setState({ openMailDialogue: false });
  };
  resetReportGrid = () => {
    if (this.gridApi) {
      this.gridApi.forEachNode(node => {
        if (node.group) {
          node.setExpanded(false);
        }
      });
    }

    window.sortStatesSavedRts = {};
    window.filterStatesSavedRts = {};
    this.state.gridcolumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.gridApi.redrawRows();
    const rowNode = this.gridApi.getDisplayedRowAtIndex(0);
    if (rowNode) {
      rowNode.setExpanded(true);
    }
  };

  onFirstDataRendered = () => {
    const rowNode = this.gridApi.getDisplayedRowAtIndex(0);
    if (
      rowNode &&
      rowNode.key &&
      rowNode.key == localStorage.getItem('userID') &&
      this.props &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state == undefined
    ) {
      rowNode.setExpanded(true);
    }
    if (
      this.props.history.location.state &&
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state
    ) {
      this.gridApi.forEachNode(node => {
        if (
          node.group &&
          node.key === this.props.history?.location?.state?.created_by
        ) {
          node.setExpanded(true);
        }
      });
    }
    this.gridApi.redrawRows();
  };
  getContextMenuItems = params => {
    return [];
  };
  onCellContextMenu = event => {
    event.preventDefault();
    console.log('Right-click disabled');
  };
  render() {
    const { classes } = this.props;
    const formattedStartDate = moment(this.state.selectedRow.start_date).format(
      'MM/DD/YY'
    );
    const formattedEndDate = moment(this.state.selectedRow.end_date).format(
      'MM/DD/YY'
    );

    return (
      <div>
        <Paper
          square
          style={{
            margin: 8,
            backgroundColor: '#ddeaf4',
            border: '1px solid #003d6b',
            color: '#003d6b'
          }}
        >
          <Tabs
            variant="fullWidth"
            TabIndicatorProps={{
              style: {
                backgroundColor: '#e7eef3'
              }
            }}
            id={'lineTab'}
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
            style={{ cursor: 'default' }}
          >
            <Tab
              label={
                <div style={{ color: '#003d78' }}>
                  {' '}
                  {window.location.pathname === '/SavedReports'
                    ? 'Saved Reports'
                    : 'Reports Saved'}
                </div>
              }
              value="one"
              style={{
                textTransform: 'none',
                pointerEvents: 'none',
                borderColor: '#e7eef3'
              }}
            />
            <Button
              variant="contained"
              id="reset-layout"
              style={{ paddingRight: 12, cursor: 'pointer', marginTop: 11 }}
              className={clsx(classes.back, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>

        {this.state.isLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: `${window.innerHeight - 200}px`,
            width: '880px',
            margin: '6px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
          onCellContextMenu={this.onCellContextMenu}
        >
          {' '}
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '400px',
              width: '100%'
            }}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            //            groupDefaultExpanded={this.state.groupDefaultExpanded}
            groupDefaultExpanded={this.state.expanded}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            suppressHorizontalScroll={true}
            suppressClickEdit={true}
            animateRows={true}
            enableCharts={true}
            getRowStyle={this.getRowStyle}
            floatingFilter={true}
            onFirstDataRendered={this.onFirstDataRendered}
            // onCellContextMenu={this.onCellContextMenu}
            getContextMenuItems={this.getContextMenuItems}
            suppressContextMenu={true}
            onRowGroupOpened={this.onRowGroupOpened.bind(this)}
            tooltipShowDelay={0}
          />
        </div>
        <Dialog
          transition={Fade}
          className={'copy-dlg'}
          classes={{
            paper: classes.paper
          }}
          BackdropProps={{
            classes: {
              root: classes.backDrop
            }
          }}
          //maxWidth="xl"
          style={{ maxWidth: 900, maxHeight: 700 }}
          open={this.state.openSaveDlg}
        >
          {/* <Dialog
            fullWidth
            maxWidth="sm"
            aria-labelledby="confirmation-dialog-title"
            open={openSaveDlg}
          > */}
          <DialogTitle id="form-dialog-title">
            <Typography
              variant="h6"
              color="primary"
              style={{ textTransform: 'none' }}
            >
              Rename and Copy - {this.state.selectedRow.report_name}
              {this.state.selectedRow.kpi_report_type !==
                'Client_Report_Card_3_Month_Total' &&
                this.state.selectedRow.kpi_report_type !==
                  'Client_Report_Card_1_Month' && (
                  <>
                    {' - '}
                    <Typography
                      variant="body2"
                      color="primary"
                      component="span"
                      style={{
                        textTransform: 'none',
                        fontSize: '17px',
                        fontWeight: 500
                      }}
                    >
                      {formattedStartDate} - {formattedEndDate}
                    </Typography>
                  </>
                )}
            </Typography>
          </DialogTitle>

          <DialogContent style={{ overflowX: 'hidden' }}>
            <TableContainer
              component={Paper}
              style={{
                margin: 4,
                padding: 1,
                display: 'block',
                width: '100%'
              }}
            >
              <Table
                className="email-table"
                id="maildetails"
                // style={{ minWidth: 300 }}
                size="small"
                aria-label="a dense table"
              >
                <TableHead
                  style={{
                    textAlign: 'center',
                    backgroundColor: '#003d6b'
                  }}
                ></TableHead>
                <TableRow key={'email'}>
                  <TableCell
                    align="left"
                    size="small"
                    style={{
                      fontSize: 14,
                      color: '#003d6b'
                    }}
                  >
                    Report Name
                  </TableCell>
                  <TableCell
                    align="left"
                    colSpan={2}
                    size="small"
                    style={{
                      fontSize: 14,
                      color: '#003d6b'
                    }}
                  >
                    <div style={{ display: 'flex' }}>
                      <div
                        style={{
                          display: 'inline-block',
                          width: 118,
                          marginLeft: 16
                        }}
                      >
                        Access Type
                      </div>
                      <div
                        style={{
                          display: 'inline-block',
                          width: 78,
                          marginLeft: 16
                        }}
                      >
                        Created On
                      </div>
                    </div>
                  </TableCell>
                </TableRow>

                <TableRow>
                  <TableCell height={10}>
                    <TextField
                      autoFocus
                      onChange={this.onChangeReportName}
                      value={this.state.reportName}
                      helperText={
                        (this.state.reportName == undefined ||
                          this.state.reportName == '') &&
                        this.state.requiredText
                          ? 'This is required!'
                          : this.state.errorReport
                          ? this.state.errorReport
                          : ''
                      }
                      margin="dense"
                      id="name"
                      type="text"
                      inputProps={{ maxLength: 25 }}
                      fullWidth
                      onInput={e => {
                        const value = e.target.value;
                        if (/[^a-zA-Z0-9 ]/.test(value)) {
                          e.target.value = value.replace(/[^a-zA-Z0-9 ]/g, '');
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    {/* <FormGroup style={{ marginTop: '19px' }}> */}
                    <Table>
                      <TableRow>
                        <TableCell
                          align="left"
                          size="small"
                          style={{
                            fontSize: 14,
                            color: '#003d6b'
                          }}
                        >
                          {/* <FormControlLabel
                            control={
                              <Checkbox
                                defaultChecked
                                size="small"
                                color="primary"
                                checked={this.state.privateChecked}
                                onChange={this.handleCheckboxChange}
                              />
                            }
                            label={
                              <Typography className={classes.formControlLabel}>
                                Private
                              </Typography>
                            }
                          /> */}
                          <FormControl>
                            <RadioGroup
                              aria-labelledby="demo-controlled-radio-buttons-group"
                              name="controlled-radio-buttons-group"
                              id="fixedratetypes"
                              value={this.state.selectedType}
                              onChange={this.handleCheckboxChange}
                            >
                              <FormControlLabel
                                value="public"
                                style={{ marginTop: -6 }}
                                control={<Radio size="small" />}
                                label="Public"
                              />
                              <FormControlLabel
                                value="private"
                                control={<Radio size="small" />}
                                label="Private"
                              />
                            </RadioGroup>
                          </FormControl>
                        </TableCell>
                        <TableCell>
                          <div>{moment().format('MM/DD/YY')}</div>
                        </TableCell>
                      </TableRow>
                    </Table>
                  </TableCell>
                </TableRow>
              </Table>
            </TableContainer>
            {/* {error && <p className="error">{error}</p>}
              {errorChecked && <p className="errorChk">{errorChecked}</p>} */}
          </DialogContent>
          <DialogActions
            style={{
              paddingRight: '19px', // Common paddingRight for both buttons
              justifyContent: 'flex-end' // Ensure buttons align to the right
            }}
          >
            <Button
              variant="contained"
              className={clsx('reset-btn')}
              onClick={this.handleCancelSaveReport}
              color="primary"
            >
              Cancel
            </Button>

            <Button
              variant="contained"
              className={clsx('reset-btn')}
              onClick={this.handleOkSaveReport}
              color="primary"
              //  disabled={filterDisabled}
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
        <SuccessSnackbar
          onClose={this.handleSnackbarClose}
          open={this.state.openSnackbar}
          msg={
            this.state.deleteClicked == true
              ? 'Report deleted successfully!'
              : 'Report Copied successfully!'
          }
          //goalFail={this.state.goalFail}
        />

        {this.state.deleteClicked == true ? (
          <Dialog
            open={this.state.open}
            onClose={this.handleCloseDeleteDialog}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                Are you sure you want to delete?
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.handleCloseDelete}>Cancel</Button>
              <Button onClick={this.handleOkDelete} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
        <EmailDialog
          open={this.state.openDialogue}
          emailListDialog={this.state.emailListDialog}
          handlePopupClose={this.handleCloseEmail}
          selectedRow={this.state.selectedRow}
          mailUsers={this.state.mailUsers}
        ></EmailDialog>
        <ViewEmailDialog
          open={this.state.openMailDialogue}
          handlePopupClose={this.handleCloseViewEmail}
          selectedRow={this.state.selectedRow}
          mailUserData={this.state.mailUserData}
          mailUsers={this.state.mailUsers}
          keycloak={this.props.keycloak}
        ></ViewEmailDialog>
        <Dialog
          aria-labelledby="confirmation-dialog-title"
          open={this.state.openAllStoresDialog}
        >
          <DialogContent dividers>
            <Typography variant="h6" style={{ transform: 'none' }}>
              This option is not available for all stores from this page
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={this.handleCancelAllStores} color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      </div>
    );
  }
}

ListReport.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};

const styles = theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'

    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  linkItem: {
    cursor: 'pointer'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  },
  paper: {
    '@media (max-width: 1920px)': {
      maxWidth: '1150px',
      maxHeight: '700px'
    },

    '@media (max-width: 1440px)': {
      maxWidth: '1500px',
      maxHeight: '610px'
    },

    '@media (max-width: 1280px)': {
      maxWidth: '1500px',
      maxHeight: '600px'
    }
  },
  copyIcon: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  copyIconDisable: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer',
    pointerEvents: 'none',
    color: 'gray'
  },
  editIcon: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  deleteIcon: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  deleteIconDisable: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer',
    pointerEvents: 'none',
    color: 'gray'
  },
  copyIconHide: {
    display: 'none'
  },
  mailIcon: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  viewMailIcon: {
    width: 16,
    left: '8',
    top: '70%',
    cursor: 'pointer'
  },
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: 8,
    width: 'auto'
  },
  mailIconDisable: {
    width: 18,
    left: '8',
    top: '70%',
    cursor: 'pointer',
    pointerEvents: 'none',
    color: 'gray'
  }
});
export default withKeycloak(withStyles(styles)(ListReport));
