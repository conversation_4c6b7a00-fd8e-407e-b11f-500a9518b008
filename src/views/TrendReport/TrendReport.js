import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import { withStyles } from '@material-ui/styles';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import {
  Box,
  LinearProgress,
  Paper,
  Typography,
  Grid,
  Button,
  Tooltip
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'src/views/AnalyzeData/Component/Aggrid.css';
import clsx from 'clsx';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React from 'react';
import {
  getLastThirteenMonths,
  getAxcessaReportSummary
} from 'src/utils/hasuraServices';
import { withRouter } from 'react-router-dom';
import { getLast13Months } from 'src/utils/Utils';
import { withKeycloak } from '@react-keycloak/web';
import { ReactSession } from 'react-client-session';
import RestoreIcon from '@material-ui/icons/Restore';

var lodash = require('lodash');

class Reports extends React.Component {
  componentDidMount() {
    window.sortState = {};
    window.filterState = {};
    if (ReactSession.get('serviceAdvisors')) {
      this.setState({ serviceAdvisors: ReactSession.get('serviceAdvisors') });
      this.getAgGridData(ReactSession.get('serviceAdvisors'));
    } else {
      this.setState({ serviceAdvisors: ['All'] });
      this.getAgGridData(['All']);
    }
  }

  componentDidUpdate() {
    if (ReactSession.get('serviceAdvisors') != undefined) {
      var checkStatus = lodash.isEqual(
        this.state.serviceAdvisors,
        ReactSession.get('serviceAdvisors')
      );
      // console.log(
      //   'selectedStoreId=qq=1',
      //   ReactSession.get('selectedStoreId'),
      //   ReactSession.get('serviceAdvisors'),
      //   checkStatus,
      //   localStorage.getItem('selectedStoreId'),
      //   this.state.sto
      // );
      if (checkStatus == false) {
        this.getAgGridData(ReactSession.get('serviceAdvisors'));
      }
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.getAgGridData(ReactSession.get('serviceAdvisors'));
        this.setState({ store: localStorage.getItem('selectedStoreId') });
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 15,
    isParts: false,
    reportType: 'Sales',
    isEfficiencyCharts: false
  };

  constructor(props) {
    super(props);
    this.state = {
      isLoading: true,
      gridApi: {},
      gridParams: {},
      rowData: [],
      chartName: null,
      reportName: null,
      // sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      columnDefs: [
        {
          field: 'paytypegroup',
          dataType: 'string',
          width: 150,
          rowGroup: true,
          suppressCount: true,
          suppressMenu: true,
          sortable: false,
          editable: false,
          hide: true,
          valueFormatter: this.formatCellValuePayType,
          filterParams: {
            valueFormatter: this.formatCellValuePayType
          },
          cellStyle: function(params) {
            return { textAlign: 'left' };
          }
        },
        {
          headerName: 'Total',
          field: 'totalData',
          width: 100,
          suppressMenu: true,
          sortable: true,
          enableValue: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: 'Average',
          field: 'averageData',
          width: 92,
          suppressMenu: true,
          sortable: true,
          enableValue: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Apr'20",
          field: 'mon13',
          width: 95,
          suppressMenu: true,
          chartDataType: 'category',
          editable: false,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Mar'20",
          field: 'mon12',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          editable: false,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Feb'20",
          field: 'mon11',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Jan'20",
          field: 'mon10',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Dec'19",
          field: 'mon9',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Nov'19",
          field: 'mon8',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Oct'19",
          field: 'mon7',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Sep'19",
          field: 'mon6',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Aug'19",
          field: 'mon5',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },

        {
          headerName: "Jul'19",
          field: 'mon4',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Jun'19",
          field: 'mon3',
          width: 95,
          chartDataType: 'category',
          suppressMenu: true,
          sortable: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "May'19",
          field: 'mon2',
          width: 95,
          suppressMenu: true,
          sortable: true,
          enableValue: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },
        {
          headerName: "Apr'19",
          field: 'mon1',
          width: 95,
          suppressMenu: true,
          sortable: true,
          enableValue: true,
          cellStyle: this.cellStyles,
          valueFormatter: this.formatCellValue,
          cellClass: this.getCellClass,
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        }
      ],
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        unSortIcon: true,
        resizable: false,
        filter: true,
        editable: false,
        suppressMovable: false
      },

      rowStyle: { background: 'white' },
      context: { componentParent: this },
      rowSelection: 'multiple',
      suppressRowClickSelection: true,
      pinnedTopRowData: createData(1, 'Top'),

      autoGroupColumnDef: {
        headerName: 'Pay Type',
        field: 'categoryName',
        maxWidth: 190,
        chartDataType: 'category',
        cellRendererParams: {
          suppressCount: true
        },
        suppressMenu: true,
        unSortIcon: false,
        sortable: false,
        editable: false,
        valueFormatter: this.formatCellValuePayType,
        filterParams: {
          valueFormatter: this.formatCellValuePayType
        },
        cellClass: 'textAlign',
        cellStyle: function() {
          return {
            color: '#000000',
            fontWeight: 'bold'
            // cursor: 'pointer'
          };
        }
      },
      excelStyles: [
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'smallHeader',
          font: {
            size: 14,
            color: '#003d6b'
          },
          alignment: {
            horizontal: 'Left',
            vertical: 'Bottom'
          }
        },
        {
          id: 'salesWith$',
          numberFormat: { format: '$#,##0' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'grossPercentage',
          numberFormat: { format: '#,##0' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'grossPercentageDecimal',
          numberFormat: { format: '#,##0.0' },
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };

  getCellClass = params => {
    if (params.data) {
      if (
        params.data.categoryName == 'Total Sales' ||
        params.data.categoryName == 'Gross'
      ) {
        return 'salesWith$';
      } else if (params.data.categoryName == 'GP %') {
        if (Number.isInteger(Number(params.value))) {
          return 'grossPercentage';
        } else {
          return 'grossPercentageDecimal';
        }
      }
    }
  };

  formatCellValuePayType = params => {
    if (params.value == 'C') {
      return 'Customer Pay';
    } else if (params.value == 'W') {
      return 'Warranty';
    } else if (params.value == 'I') {
      return 'Internal';
    } else if (params.value == 'M') {
      return 'Maintenance Plan';
    } else if (params.value == 'E') {
      return 'Extended Service Contract';
    } else if (params.value == 'F') {
      return 'Factory Service Contract';
    } else if (params.value == 'Total Sales') {
      return 'Sales';
    } else if (params.value == 'Gross') {
      return 'Gross Profit';
    } else if (params.value == 'GP %') {
      return 'Gross Profit %';
    }
  };

  formatCellValue = params => {
    if (params.data) {
      if (
        params.data.categoryName == 'Total Sales' ||
        params.data.categoryName == 'Gross'
      ) {
        return params.value == 0 || params.value == null
          ? '$0'
          : Math.sign(params.value) > -1
          ? '$' +
            (params.value.split('.')[1] == '00'
              ? Math.round(params.value)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : params.value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ','))
          : '-$' +
            (params.value.split('.')[1] == '00'
              ? Math.round(Math.abs(params.value))
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : Math.abs(params.value)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ','));
      } else if (params.data.categoryName == 'GP %') {
        return params.value == 0 || params.value == null
          ? '0%'
          : // : Math.round(params.value).toLocaleString();
            (Number.isInteger(Number(params.value))
              ? Math.round(params.value)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
              : parseFloat(params.value)
                  .toFixed(1)
                  .toString()
                  .replace(/\B(?=(\d{3})+(?!\d))/g, ',')) + '%';
      }
    }
    /* else if (
      params.value == null &&
      params.data != undefined &&
      (params.data.categoryName == 'Total Sales' ||
        params.data.categoryName == 'Gross')
    ) {
      return '$0';
    } else if (
      params.value == null &&
      params.data != undefined &&
      params.data.categoryName == 'GP %'
    ) {
      return '0%';
    }*/
  };

  getRowStyle = params => {
    if (params.node.allChildrenCount != null) {
      return { background: '#F3F1F0' };
    }
  };
  getCellStyle = params => {
    if (params.data != undefined) {
      let colorArr = [
        '#003d6b',
        '#054372',
        '#064677',
        '#0f5285',
        '#165a8e',
        '#1e6296',
        '#2772ac',
        '#347fb8',
        '#468cc1',
        '#72aeda',
        '#6baad8',
        '#88bce3',
        '#b5daf6'
      ];
      var bgColor = null;
      let dataArr = [];

      dataArr.push(
        params.data.mon1,
        params.data.mon2,
        params.data.mon3,
        params.data.mon4,
        params.data.mon5,
        params.data.mon6,
        params.data.mon7,
        params.data.mon8,
        params.data.mon9,
        params.data.mon10,
        params.data.mon11,
        params.data.mon12,
        params.data.mon13
      );
      var filtered = dataArr.filter(function(el) {
        return el != null;
      });

      filtered.sort((a, b) => b - a);

      if (params.value != 0) {
        var valueIndex = filtered.indexOf(params.value);

        bgColor = colorArr[valueIndex];
        console.log('params', valueIndex, params.value, bgColor);
      } else {
        bgColor = null;
      }
    }

    return {
      'background-color': bgColor,
      color: '#fff',
      textAlign: 'right'
    };
  };

  getMonthyear = () => {
    var monthValues = getLast13Months();
    var yearMonth = [];
    var months = [
      'none',
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    for (let index = 0; index < monthValues.length; index++) {
      var mon = monthValues[index];
      var res = mon.split('-');
      var month1 = res[1];
      month1 = month1.replace(/^0+/, '');

      yearMonth.push(months[month1] + " '" + res[0].slice(2));
    }
    yearMonth.reverse();
    this.setState({ yearMonthArr: yearMonth });
    return yearMonth;
  };

  onGridReady = params => {
    getLastThirteenMonths(result => {
      const startMonth = result[0];
      const endMonth = result[12];
      const datestr = endMonth.split('-');
      const month = datestr[1];
      const year = datestr[0];
      const date = new Date(year, month, 0).getDate();

      let dateRange = [startMonth + '-01', endMonth + '-' + date];
      let dateRange12Months = [result[1] + '-01', endMonth + '-' + date];
      localStorage.setItem('12Months', dateRange12Months);
      localStorage.setItem('13Months', dateRange);
    });
    this.gridApi = params.api;
    params.api.closeToolPanel();

    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params.api });
    this.setState({ gridColumnApi: params.columnApi });
    this.setState({ gridParams: params });
    this.gridApi.setSortModel(window.sortState);
    this.gridApi.setFilterModel(window.filterState);
    this.gridApi.expandAll();
    var monthYear = this.getMonthyear();
    this.setState({ monthYear: monthYear });
    var monthnames = [
      'mon13',
      'mon12',
      'mon11',
      'mon10',
      'mon9',
      'mon8',
      'mon7',
      'mon6',
      'mon5',
      'mon4',
      'mon3',
      'mon2',
      'mon1'
    ];

    for (let index = 0; index < monthnames.length; index++) {
      const headerName = monthnames[index];
      var makeCol = params.columnApi.getColumn(headerName);
      makeCol.colDef.headerName = monthYear[index];

      params.api.refreshHeader();
    }
    var defaultSortModel = [
      {
        colId: 'mon1',
        sort: 'desc'
      },
      {
        colId: 'mon2',
        sort: 'desc'
      },
      {
        colId: 'mon3',
        sort: 'desc'
      },
      {
        colId: 'mon4',
        sort: 'desc'
      },
      {
        colId: 'mon5',
        sort: 'desc'
      },
      {
        colId: 'mon6',
        sort: 'desc'
      },
      {
        colId: 'mon7',
        sort: 'desc'
      },
      {
        colId: 'mon8',
        sort: 'desc'
      },
      {
        colId: 'mon9',
        sort: 'desc'
      },
      {
        colId: 'mon10',
        sort: 'desc'
      },
      {
        colId: 'mon11',
        sort: 'desc'
      },
      {
        colId: 'mon12',
        sort: 'desc'
      },
      {
        colId: 'mon13',
        sort: 'desc'
      },
      {
        colId: 'averageData',
        sort: 'desc'
      },
      {
        colId: 'totalData',
        sort: 'desc'
      }
    ];
    // params.api.setSortModel(defaultSortModel);
    if (this.state.rowData && this.state.rowData.length > 0) {
      this.gridApi.setPinnedTopRowData([{ categoryName: ' Labor ' }]);
    } else {
      this.gridApi.setPinnedTopRowData([]);
    }
    //this.gridApi.setPinnedBottomRowData([]);
  };

  onGroupExpandedOrCollapsed = params => {
    params.api.forEachNode(node => {
      node.expanded = true;
    });
  };

  parseArray(filtererdArray) {
    let intArray = [];

    var res = filtererdArray.map(v => {
      intArray.push({
        opcategory: v.opcategory,
        opcode: v.opcode,
        opcodedesc: v.opcodedesc,
        lbrtechno: v.lbrtechno,
        advisorName: v.advisorName,
        serviceadvisor: v.serviceadvisor,
        lbrtechname: v.lbrtechname,
        isActive: v.isActive ? v.isActive : 0,
        mon1: parseFloat(v['mon1']) || 0,
        mon2: parseFloat(v['mon2']) || 0,
        mon3: parseFloat(v['mon3']) || 0,
        mon4: parseFloat(v['mon4']) || 0,
        mon5: parseFloat(v['mon5']) || 0,
        mon6: parseFloat(v['mon6']) || 0,
        mon7: parseFloat(v['mon7']) || 0,
        mon8: parseFloat(v['mon8']) || 0,
        mon9: parseFloat(v['mon9']) || 0,
        mon10: parseFloat(v['mon10'] || 0),
        mon11: parseFloat(v['mon11'] || 0),
        mon12: parseFloat(v['mon12'] || 0),
        mon13: parseFloat(v['mon13'] || 0),
        averageData: parseFloat(v['averageData'] || 0),
        totalData: parseFloat(v['totalData'] || 0)
      });
      // return res;
    });
    return intArray;
  }

  getAgGridData(advisor) {
    this.setState({ isLoading: true });
    this.setState({ rowData: [] });
    advisor = advisor ? advisor : ['All'];
    this.setState({ serviceAdvisors: advisor });
    getAxcessaReportSummary(advisor, result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessDbdLaborWorkmixGetAxcessaSummary
          .axcessaSummaryDetails
      ) {
        var resultData =
          result.data.statelessDbdLaborWorkmixGetAxcessaSummary
            .axcessaSummaryDetails;

        // lodash
        // .map(resultArr, 'colm' + i)
        // .every(item => dataArrMaint.push(item));
        //lodash.every(resultData => resultArr1.push('totalData'));
        let dataArr = [];
        resultData.map(item => {
          dataArr.push(item.totalData);
        });
        if (dataArr.every(element => element == '0.00')) {
          resultData = [];
        }

        this.setState({
          rowData: resultData
        });
      }
    });
  }
  sortArray = (arr1, arr2) => {
    arr2.sort((a, b) => {
      const aKey = Object.keys(a)[0];
      const bKey = Object.keys(b)[0];
      return arr1.indexOf(aKey) - arr1.indexOf(bKey);
    });
  };
  filterByValues = () => {
    var countryFilterComponent = '';
    var filterArr = Object.entries(window.filterState);
    filterArr.map(item => {
      countryFilterComponent = this.gridApi.getFilterInstance(item[0]);
      countryFilterComponent.setModel({ values: item[1].values });
    });
    this.gridApi.onFilterChanged();
  };
  handleclick = () => {
    this.props.history.push({
      pathname: '/CPLaborOverview',
      state: {
        prevPath: window.location.pathname
      }
    });
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Trend Report',
      fileName: 'Trend Report',
      processCellCallback: params => processCells(params),

      customHeader: [
        [],
        [
          {
            styleId: 'smallHeader',
            data: {
              type: 'String',
              value: localStorage.getItem('storeNames')
              //value: localStorage.getItem('storeSelected') + &ndash;
            }
            //mergeAcross: 1
          },
          {
            styleId: 'smallHeader',
            data: {
              type: 'String',
              value:
                'Trending -' +
                this.state.monthYear[0] +
                ' thru ' +
                this.state.monthYear[12]
            },
            mergeAcross: 2
          },
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Trend Report'
            },
            mergeAcross: 3
          }
        ]
      ]
      // processCellCallback: params => {
      //   //console.log(params.node.data.categoryName);
      //   processCells(params); // Call the processCells function

      //   if (params.node.data && params.node.data.categoryName == 'GP %') {
      //     let formattedValue = params.value;
      //     // Check if the formatted value ends with .00
      //     if (formattedValue != null) {
      //       if (formattedValue.endsWith('.00')) {
      //         return formattedValue.slice(0, -3) + '%'; // Remove .00 and add %
      //       } else {
      //         return formattedValue + '%'; // Add % without removing anything
      //       }
      //     }
      //   } else {
      //     return params.value;
      //   }
      //   // For other columns, return the value as is
      // }
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  resetRawData = () => {
    this.state.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };
  render() {
    const { classes } = this.props;
    let parent =
      this.props.history.location && this.props.history.location.isFrom
        ? this.props.history.location.isFrom
        : '';
    return (
      <React.Fragment>
        <Paper
          square
          style={{
            margin: 10,
            paddingTop: '6px',
            height: '40px',
            backgroundColor: '#ddeaf4',
            border: '1px solid #003d6b',
            color: '#003d6b',
            width: '100%',
            marginLeft: 2
          }}
        >
          <Grid
            container
            className={clsx(this.props.titleContainer, 'reset-dashboard')}
          >
            <Grid item xs={4} style={{ display: 'flex', alignItems: 'center' }}>
              {parent == 'LaborOverview' ||
              (this.props.history &&
                this.props.history.location &&
                this.props.history.location.state &&
                this.props.history.location.state.isFrom &&
                this.props.history.location.state.isFrom == 'LaborOverview') ? (
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={this.handleclick}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              ) : (
                ''
              )}
            </Grid>
            <Grid item xs={6} style={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="h4"
                color="#003d6b"
                className={classes.mainLabel}
              >
                {'Trend Report'}
              </Typography>
            </Grid>
            <Grid
              item
              xs={2}
              style={{ display: 'flex', justifyContent: 'end', marginLeft: -9 }}
            >
              <Tooltip title="Export To Excel">
                <Link
                  id="export-to-excel"
                  style={{
                    paddingRight: 6,
                    cursor: 'pointer',
                    float: 'right',
                    marginTop: 2
                  }}
                  onClick={this.onBtExport}
                >
                  <ExportIcon />
                </Link>
              </Tooltip>
              <Button
                variant="contained"
                id="reset-layout"
                style={{ marginTop: 0 }}
                className={clsx(classes.back, 'reset-btn')}
                onClick={this.resetRawData}
              >
                <RestoreIcon />
                <Typography variant="body1" align="left">
                  Reset Layout
                </Typography>
              </Button>
            </Grid>
          </Grid>
        </Paper>
        <Paper>
          <div className={this.state.isLoading != true ? 'containerMain' : ''}>
            {this.state.isLoading == true ? (
              <div>
                <Box style={{ padding: 25 }}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    style={{ padding: 25 }}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : (
              <React.Fragment>
                <Grid
                  container
                  xs={12}
                  className={classes.rankedTableContainer}
                >
                  {/* {parent == 'LaborOverview' ? 
              <Button
                variant="contained"
                className={clsx(classes.backButton, 'bck-btn')}
                onClick={this.handleclick}
                style={{marginTop: 10}}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button> : null } */}
                  {/* className={parent == '' ? '' : classes.storeName} */}
                  <Grid xs={12}>
                    <Button className={classes.summaryBlock}>
                      <Typography
                        variant="h6"
                        align="left"
                        className={classes.summaryBlockText}
                      >
                        <span style={{ color: '#003d6b' }}>
                          {localStorage.getItem('storeNames')}
                        </span>
                      </Typography>
                    </Button>
                  </Grid>
                  {/* className={parent == '' ? classes.trending : classes.trendingBck} */}
                  {this.state.monthYear && this.state.monthYear.length > 0 ? (
                    <Grid xs={12} className={classes.trending}>
                      <Button className={classes.summaryBlock}>
                        <Typography
                          variant="h6"
                          align="left"
                          className={classes.summaryBlockText}
                        >
                          <span style={{ color: '#003d6b' }}>
                            Trending - {this.state.monthYear[0]} thru{' '}
                            {this.state.monthYear[12]}
                          </span>
                        </Typography>
                      </Button>
                      {/* <Tooltip title="Export To Excel">
                      <Link
                        id="export-to-excel"
                        style={{
                          paddingRight: 10,
                          cursor: 'pointer',
                          float: 'right'
                        }}
                      onClick={this.onBtExport}
                      >
                        <ExportIcon />
                      </Link>
                    </Tooltip> */}
                    </Grid>
                  ) : null}
                </Grid>
                <div
                  id="data-tab-axcessa-report"
                  className="ag-theme-balham"
                  style={{
                    height: window.innerHeight - 200 + 'px',
                    //width: '1915px',
                    margin: 8,
                    display: this.state.isLoading ? 'none' : 'block'
                  }}
                >
                  <AgGridReact
                    className="ag-theme-balham"
                    style={{
                      height: '410px',
                      width: '100%'
                    }}
                    autoGroupColumnDef={this.state.autoGroupColumnDef}
                    animateRows={true}
                    headerHeight={this.state.headerHeight}
                    modules={AllModules}
                    columnDefs={this.state.columnDefs}
                    defaultColDef={this.state.defaultColDef}
                    onGridReady={this.onGridReady}
                    groupDefaultExpanded={1}
                    enableSorting={true}
                    rowData={this.state.rowData}
                    getRowStyle={this.getRowStyle}
                    excelStyles={this.state.excelStyles}
                    floatingFilter={true}
                    tooltipShowDelay={0}
                    suppressDragLeaveHidesColumns={true}
                    // pinnedTopRowData={this.state.pinnedTopRowData}
                    rowHeight={25}
                    suppressCellSelection={true}
                    suppressContextMenu={true}
                  />
                </div>
              </React.Fragment>
            )}
          </div>
        </Paper>
      </React.Fragment>
    );
  }
}

const processCells = params => {
  if (params.value !== undefined && params.value !== null) {
    if (params.value === 'Total Sales') {
      return 'Sales';
    } else if (params.value === ' -> C') {
      return ' -> Customer Pay';
    } else if (params.value === ' -> W') {
      return ' -> Warranty';
    } else if (params.value === ' -> I') {
      return ' -> Internal';
    } else if (params.value === ' -> M') {
      return ' -> Maintenance Plan';
    } else if (params.value === ' -> F') {
      return ' -> Factory Service Contract';
    } else if (params.value === ' -> E') {
      return ' -> Extended Service Contract';
    } else if (params.value === 'Gross') {
      return 'Gross Profit';
    } else if (params.value === 'GP %') {
      return 'Gross Profit %';
    } else {
      if (params.node.data && params.node.data.categoryName === 'GP %') {
        //  let formattedValue = params.value;
        let formattedValue = parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');

        if (formattedValue != null) {
          if (formattedValue.endsWith('.00')) {
            return formattedValue.slice(0, -3) + '%'; // Remove .00 and add %
          } else {
            return formattedValue + '%'; // Add % without removing anything
          }
        }
      } else {
        let formatted = params.value
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        return formatted;
        // return params.value;
      }
    }
  }
  return params.node.data && params.node.data.categoryName === 'GP %'
    ? '0%'
    : params.node.data && params.node.data.categoryName === 'Gross'
    ? '0'
    : params.node.data && params.node.data.categoryName === 'Total Sales'
    ? '0'
    : '';
};

const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1)
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  dividerRoot: {
    backgroundColor: '#4a4646',
    width: '100%'
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  storeName: {
    marginTop: -38,
    marginLeft: 51
  },
  trending: {
    marginTop: -8,
    marginBottom: -6
  },
  trendingBck: {
    marginTop: -17,
    marginBottom: -6,
    marginLeft: 51
  },
  backButton: {
    marginTop: '10px !important'
  },
  mainLabel: {
    width: '100%',
    marginLeft: '15%',
    fontSize: 14,
    color: '#003d6b'
  }
});
export default withKeycloak(withStyles(styles)(withRouter(Reports)));
function createData(count) {
  var result = [];
  for (var i = 0; i < count; i++) {
    result.push({
      categoryName: ' Labor '
    });
  }
  return result;
}
