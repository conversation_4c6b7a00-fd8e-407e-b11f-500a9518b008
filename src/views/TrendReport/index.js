import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { getTimeZone } from '../../utils/Utils';
import { getGridorMatrixPayTypeDetails } from 'src/utils/hasuraServices';
import TrendReportData from './TrendReport';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function TrendReport() {
  const classes = useStyles();
  const history = useHistory();
  var timezone = getTimeZone();
  const [selectedPayType, setSelectedPayType] = useState('');
  const [matrixTypes, setMatrixType] = useState([]);

  //   useEffect(() => {
  //     fetchData();
  //   }, []);

  //   const fetchData = () => {
  //     getGridorMatrixPayTypeDetails('paytype_matrix', result => {
  //       if (result.length > 0) {
  //         setMatrixType(result);
  //         setSelectedPayType(result[0]);
  //       } else {
  //         setMatrixType(result);
  //         setSelectedPayType(result);
  //       }
  //     });
  //   };
  return (
    <Page className={classes.root} title={'Trend Report'}>
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <TrendReportData history={history} />
      )}
    </Page>
  );
}

export default TrendReport;
