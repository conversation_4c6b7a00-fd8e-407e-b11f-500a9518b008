import React, { useState, useEffect } from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  Paper,
  Divider,
  Button,
  Typography,
  CircularProgress
} from '@material-ui/core';
import Zoom from '@material-ui/core/Zoom';
import { withStyles } from '@material-ui/styles';
import Grid from '@material-ui/core/Grid';
import { makeStyles } from '@material-ui/core/styles';
import $ from 'jquery';
import Collapse from '@material-ui/core/Collapse';
import DashboardLineRenderer from 'src/components/charts/DashboardLineRenderer';
import DashboardActions from 'src/components/DashboardActions';
import {
  getDataGridConfiguration,
  getLayoutConfiguration,
  saveLayoutConfiguration,
  removeFav
} from '../../utils/Utils';
import { getDataForPartsOverviewCharts } from 'src/utils/hasuraServices';
import ChartDialog from 'src/components/Dialog';
import { withKeycloak } from '@react-keycloak/web';
import PageHeader from 'src/components/PageHeader';
import clsx from 'clsx';
import 'src/styles.css';
import { useSelector } from 'react-redux';

const ReactGridLayout = WidthProvider(RGL);
var lodash = require('lodash');
const useStyles = makeStyles(theme => ({
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  root: {
    flexGrow: 1
    //width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    //justifyContent: 'space-between',
    width: '100%',
    boxShadow: 'none !important'
  },
  loaderGrid: {
    height: 800,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  reportButton: {
    height: 24,
    marginLeft: 5,
    color: '#757575',
    borderRadius: '4px',
    border: '2px solid #7d97aa',
    textTransform: 'none',
    '@media (max-width: 1920px)': {
      fontSize: 12
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (min-width: 2304px)': {
      fontSize: 16
    }
  },
  reportButtonSelect: {
    height: 24,
    marginLeft: 4,
    color: '#757575',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1440px)': {
      fontSize: 10
    },
    '@media (max-width: 1280px)': {
      fontSize: 10
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    borderColor: theme.palette.primary.main
  },
  rankedTableContainer: {
    // display: 'flex',
    // alignItems: 'center',
    marginTop: 5
  },
  rankButtonGrid: {
    // justifyContent: 'space-between'
  },
  reportTopBar: {
    display: 'contents',
    marginTop: 10,
    alignItems: 'center',
    justifyContent: 'space-around',

    boxShadow: '0 0 0 1px rgba(63,63,68,0.05), 0 1px 3px 0 rgba(63,63,68,0.15)'
  },
  rankButtonGrid: {
    // justifyContent: 'space-between'
  },

  reportTopBar: {
    //margin: 8,
    padding: 8,
    width: '100%',
    justifyContent: 'center',
    display: 'flex',
    alignItems: 'center'
  }
}));
const Charts = props => {
  const classes = useStyles();
  const { keycloak, history, onLayoutChange = () => {} } = props;
  const realm = keycloak.realm;
  const session = useSelector(state => state.session);
  const [reloadCounter, setReloadCounter] = useState(0);
  const [filters, setFilters] = useState(2);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const [zoomedChart, setZoomedChart] = useState('');
  const [partsChartsData, setPartsChartsData] = useState([]);
  const [orderedData, setOrderedData] = useState([]);
  const [checked, setChecked] = useState(false);
  const [zoomed, setZoomed] = useState(false);
  const [selected, setSelected] = useState(history.location.selectedGrid);
  const [selectedChartId, setSelectedChartId] = useState(
    history.location.handleHighlight
  );
  const [showCurrentMonth, setShowCurrentMonth] = useState(
    localStorage.getItem('showCurrentMonth')
      ? JSON.parse(localStorage.getItem('showCurrentMonth'))
      : false
  );
  const [layout, setLayout] = useState(
    JSON.parse(
      JSON.stringify(
        getLayoutConfiguration('layout', 'fixed-ops-layout-4') || {}
      )
    )
  );
  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  const [popupChartId, setPopupChartId] = useState('');
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (history.location && history.location.selectedGrid) {
      setTimeout(() => {
        handleHighlightContainer(history.location.handleHighlight);
      }, 100);
    }
    // eslint-disable-next-line
    // getDataForPartsOverviewCharts(session.serviceAdvisor, callback => {
    //   console.log('Data fetched for CP Parts Overview');
    // });
    let filteredResult = chartList.filter(
      item => item.dbdName == 'CP Parts Overview' && item.parentId == null
    );
    let orderedDataSet = lodash.orderBy(filteredResult, 'sort', 'asc');
    let orderedData = orderedDataSet.filter(function(item) {
      return item.chartId !== '956';
    });
    console.log('Ordered Data:', orderedData);
    setOrderedData(orderedData);

    // Assuming your data is stored in a variable called `input`

    // eslint-disable-next-line
  }, []);

  useEffect(() => {
    setIsDataLoaded(false);
    setPartsChartsData([]);
    getDataForPartsOverviewCharts(session.serviceAdvisor, 'All', callback => {
      const datasets = JSON.parse(callback[0].jsonData)[0].datasets;

      const labels = JSON.parse(callback[0].jsonData)[0].labels;

      // Group datasets by chartId
      const grouped = {};
      datasets.forEach(ds => {
        if (!grouped[ds.chartId]) grouped[ds.chartId] = [];
        grouped[ds.chartId].push({
          data: ds.data,
          label: ds.label
        });
      });

      // Form the final array
      const result = Object.entries(grouped).map(([chartId, datasets]) => ({
        chartId: parseInt(chartId, 10),
        datasets,
        labels
      }));
      setPartsChartsData(result);
      if (session.serviceAdvisor.includes('All') == false) {
        setIsDataLoaded(true);
      }
    });

    // Assuming your data is stored in a variable called `input`

    // eslint-disable-next-line
  }, [session.serviceAdvisor]);

  const setResetDashboard = value => {
    setReloadCounter(prev => prev + 1);
    if (value) {
      setLayout([]);
      setChecked(false);
    }
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn) $('#' + prevBtn).removeClass('button-selected');
    if (selectedId) $('#' + selectedId).removeClass('grid-selected');
    setSelected('');
    setSelectedChartId('');
    return layout;
  };

  const setFilterCharts = value => {
    if (value) setFilters(value);
    return filters;
  };

  const handleLayoutChange = layout => {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-3');
    setLayout(layout);
    onLayoutChange(layout);
  };

  const handleShowCurrentMonth = value => {
    setShowCurrentMonth(value);
    return value;
  };

  const handleRemoveFav = value => {
    // Placeholder for remove favorite
  };

  const handleChartPopup = value => {
    setPopupChartId(value);
    setOpen(true);
    setChecked(true);
    //window.scrollTo(0, 0);
  };

  const handleClosePopup = value => {
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn && prevBtn.split('-')[1] == popupChartId) {
      $('#chartContainterId-' + popupChartId).addClass('grid-selected');
      setSelected(true);
      var element = document.getElementById(
        'chartContainterId-' + popupChartId
      );
      if (element) element.scrollIntoView({ block: 'center' });
    }
    setPopupChartId('');
    setOpen(false);
  };

  const handleChange = () => {
    setChecked(true);
  };

  const gotoVisualization = (chartId, e) => {
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn) $('#' + prevBtn).removeClass('button-selected');
    if (selectedId) $('#' + selectedId).removeClass('grid-selected');
    var element = document.getElementById('chartContainterId-' + chartId);
    if (element) element.scrollIntoView({ block: 'center' });
    setSelected(true);
    setSelectedChartId(chartId);
    $('#chartContainterId-' + chartId).addClass('grid-selected');
    $('#chartid-' + chartId).addClass('button-selected');
  };

  const getChartName = str => {
    var newStr = str.replace('CP', '');
    var newString = 'Parts';
    if (newStr.includes(newString)) {
      var newStr2 = newStr.replace('Parts', '');
      return newStr2;
    } else {
      var newName = 'Maintenance and Competitive';
      if (newStr.includes(newName)) {
        var newStr21 = newStr.replace(
          'Maintenance and Competitive',
          'Maint and Comp'
        );
        return newStr21;
      } else {
        return newStr;
      }
    }
  };

  const handleHighlightContainer = chartId => {
    $('#chartid-' + chartId).addClass('button-selected');
    $('#chartContainterId-' + chartId).addClass('grid-selected');
    var element = document.getElementById('chartContainterId-' + chartId);
    if (element)
      setTimeout(() => {
        element.scrollIntoView({ block: 'center' });
      }, 100);
  };

  const backTobutton = chartId => {
    let selectedId = $('.grid-selected').attr('id');
    var element = document.getElementById('chartContainterId-' + chartId);
    let prevBtn = $('.button-selected').attr('id');
    setSelected(false);
    setSelectedChartId('');
    if (
      typeof prevBtn !== 'undefined' &&
      prevBtn &&
      prevBtn.split('-')[1] == chartId
    ) {
      if (prevBtn) {
        window.scrollTo(0, 0);
        $('#' + prevBtn).removeClass('button-selected');
      }
      if (selectedId) $('#' + selectedId).removeClass('grid-selected');
    } else {
      window.scrollTo(0, 0);
    }
  };

  const getHighlighedDiv = chartId => {
    let selectedId = $('.grid-selected').attr('id');
    let prevBtn = $('.button-selected').attr('id');
    if (prevBtn && prevBtn.split('-')[1] == chartId) {
      if (prevBtn) $('#' + prevBtn).removeClass('button-selected');
      if (selectedId) $('#' + selectedId).removeClass('grid-selected');
      var element = document.getElementById('chartContainterId-' + chartId);
      if (element) element.scrollIntoView({ block: 'center' });
      $('#chartContainterId-' + chartId).addClass('grid-selected');
      setSelected(true);
      $('#chartid-' + chartId).addClass('button-selected');
    }
  };

  // Data preparation

  return (
    <div className={classes.root}>
      <Paper className={classes.paper}>
        <PageHeader
          title={'CP Parts Overview'}
          setResetDashboard={setResetDashboard}
          showCurrentMonth={handleShowCurrentMonth}
        />
        <Divider />
        <Grid item xs={12} className={classes.rankedTableContainer}>
          <Paper className={classes.reportTopBar}>
            <div>
              <Grid
                container
                style={{ margin: 0, width: '100%' }}
                justifyContent="center"
              >
                {orderedData.map((item, index) => (
                  <Button
                    className={classes.reportButton}
                    id={'chartid-' + item.chartId}
                    variant="outlined"
                    key={item.chartId}
                    onClick={() => {
                      gotoVisualization(item.chartId);
                    }}
                  >
                    {getChartName(item.chartName)}
                  </Button>
                ))}
              </Grid>
            </div>
          </Paper>
        </Grid>
        {/* {partsChartsData.length == 0 ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : ( */}
        <ReactGridLayout
          className={'layout'}
          {...props}
          layout={layout}
          onLayoutChange={handleLayoutChange}
          isResizable={false}
          isDraggable={false}
          cols={12}
          rowHeight={30}
        >
          {orderedData.map((item, index) => (
            <div
              className={clsx('diagram-section')}
              id={'chartContainterId-' + item.chartId}
              key={index}
              data-grid={getDataGridConfiguration(index)}
            >
              <DashboardLineRenderer
                filterCharts={filters}
                chartId={Number(item.chartId)}
                isFrom={'retail'}
                showCurrentMonth={showCurrentMonth}
                dbdName={item.dbdName}
                realm={realm}
                handleClose={handleClosePopup}
                backTobutton={backTobutton}
                handleHighlight={handleHighlightContainer}
                selected={selected}
                selectedChartId={selectedChartId}
                removeFavourite={handleRemoveFav}
                headerClick={getHighlighedDiv}
                chartData={partsChartsData.filter(
                  chart => chart.chartId == item.chartId
                )}
                isDataLoaded={isDataLoaded}
                key={reloadCounter}
              />
            </div>
          ))}
        </ReactGridLayout>
        {/* //)} */}
        <ChartDialog
          chartId={popupChartId}
          filter={filters}
          handlePopupClose={handleClosePopup}
          chartType="line"
          realm={realm}
          chartData={partsChartsData}
          isDataLoaded={isDataLoaded}
        />
      </Paper>
    </div>
  );
};

export default withKeycloak(Charts);
