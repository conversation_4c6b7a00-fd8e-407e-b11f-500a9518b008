import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import Charts from './Charts';
import { Redirect } from 'react-router-dom';

const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function CPPartsOverview() {
  const classes = useStyles();
  const history = useHistory();

  return (
    <Page className={classes.root} title="CP Parts Overview">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <Charts history={history} />
      )}
    </Page>
  );
}

export default CPPartsOverview;
