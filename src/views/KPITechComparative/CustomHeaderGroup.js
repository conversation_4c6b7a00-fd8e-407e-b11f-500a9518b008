import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

export default props => {
  const session = useSelector(state => state.session);
  const [hiddenGoals, setHiddenGoals] = useState(props.context.hideGoals);

  useEffect(() => {
    setHiddenGoals(props.context.hideGoals);
  }, [props.context.hideGoals]);
  const formatCellValue = (val, opt, slno, dec) => {
    var formattedValue =
      val == 0 || val == null
        ? '0'
        : dec == 1
        ? parseInt(val)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : parseFloat(val)
            .toFixed(1)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    if (
      ((slno == 1 ||
        slno == 2 ||
        slno == 12 ||
        slno == 13 ||
        slno == 32 ||
        slno == 35 ||
        slno == 31 ||
        slno == 34 ||
        slno == 33 ||
        slno == 37) &&
        opt == 2) ||
      (slno == 8 && opt == 1) ||
      ((slno == 21 || slno == 22 || slno == 26 || slno == 27) && opt == 1) ||
      slno == 19
    ) {
      formattedValue = formattedValue + '%';
    } else if (
      slno == 1 ||
      slno == 2 ||
      slno == 3 ||
      slno == 4 ||
      slno == 5 ||
      slno == 6 ||
      slno == 18 ||
      slno == 23 ||
      slno == 24 ||
      slno == 28 ||
      slno == 29 ||
      slno == 32 ||
      slno == 35 ||
      ((slno == 14 || slno == 15 || slno == 16 || slno == 17) &&
        (opt == 1 || opt == 2))
    ) {
      if (Number(formattedValue.replace(/,/g, '')) < 0) {
        formattedValue = '-$' + Math.abs(formattedValue);
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      formattedValue = formattedValue;
    }

    return formattedValue;
  };
  // console.log('ccc====', props);
  if (props.rowIndex > 0) {
    var i = props.value;
    if (props.data && props.data.data != null && props.data.data[i]) {
      var goal = props.data.data[i].goal;
      var variance = props.data.data[i].variance;
      var goalVal;
      if (
        props.data.kpi_slno != 9 &&
        props.data.kpi_slno != 11 &&
        props.data.kpi_slno != 33 &&
        props.data.kpi_slno != 37
      ) {
        goalVal = goal + '%/' + variance + '%';
      } else {
        goalVal = goal + '/' + variance;
      }

      if (goal != '' && variance != '') {
        return (
          <div className="kpi-data-cell">
            <div className={'kpi-data-div'}>
              <div className="kpi-all-data">
                <span>{goalVal}</span>
              </div>
            </div>
          </div>
        );
      } else {
        return <div className={'kpi-data-div'}></div>;
      }
    } else {
      return <div className={'kpi-data-div'}></div>;
    }
  } else {
    return <div className={'kpi-data-div'}></div>;
  }
};
