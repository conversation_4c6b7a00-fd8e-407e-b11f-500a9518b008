import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

export default props => {
  const session = useSelector(state => state.session);
  const [hiddenGoals, setHiddenGoals] = useState(props.context.hideGoals);

  useEffect(() => {
    setHiddenGoals(props.context.hideGoals);
  }, [props.context.hideGoals]);
  const formatCellValue = (val, opt, slno, dec) => {
    var formattedValue =
      val == 0 || val == null
        ? '0'
        : dec == 1
        ? parseFloat(val)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : parseFloat(val)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    if (
      ((slno == 1 ||
        slno == 2 ||
        slno == 12 ||
        slno == 13 ||
        slno == 32 ||
        slno == 35 ||
        slno == 31 ||
        slno == 34 ||
        slno == 33 ||
        slno == 37) &&
        opt == 2) ||
      (slno == 8 && opt == 1) ||
      ((slno == 21 || slno == 22 || slno == 26 || slno == 27) && opt == 1) ||
      slno == 19
    ) {
      formattedValue = formattedValue + '%';
    } else if (
      slno == 1 ||
      slno == 2 ||
      slno == 3 ||
      slno == 4 ||
      slno == 5 ||
      slno == 6 ||
      slno == 18 ||
      slno == 23 ||
      slno == 24 ||
      slno == 28 ||
      slno == 29 ||
      slno == 32 ||
      slno == 35 ||
      ((slno == 14 || slno == 15 || slno == 16 || slno == 17) &&
        (opt == 1 || opt == 2))
    ) {
      if (Number(formattedValue.replace(/,/g, '')) < 0) {
        formattedValue = '-$' + Math.abs(formattedValue);
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      formattedValue = formattedValue;
    }

    return formattedValue;
  };

  if (props.rowIndex > 0) {
    var i = props.value;
    if (
      props.data &&
      props.data.data != null &&
      props.data.data[i] &&
      props.data.data[i].kpi_data != null
    ) {
      var kpi = props.data.data[i] && props.data.data[i].kpi_data.split('/');

      if (
        kpi.length > 0 &&
        (props.data.kpi_slno == 12 ||
          props.data.kpi_slno == 13 ||
          props.data.kpi_slno == 9)
      ) {
        kpi.splice(-1);
      }
      if (kpi.length > 0) {
        return (
          <div className="kpi-data-cell">
            <div className={'kpi-data-div'}>
              <div className="kpi-all-data">
                {props.data.kpi_name == 'Parts To Labor Ratio' && (
                  <>
                    <span
                      className={
                        session.sortOrder.order == 0 &&
                        props.data.kpi_slno == session.sortOrder.slno
                          ? 'kpi_data kpi-selected'
                          : 'kpi_data'
                      }
                    >
                      {'$' +
                        parseFloat(kpi[0])
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>

                    <span className="ratio-label">{' to '}</span>

                    <span
                      className={
                        session.sortOrder.order == 1 &&
                        props.data.kpi_slno == session.sortOrder.slno
                          ? 'kpi_data kpi-selected'
                          : 'kpi_data'
                      }
                    >
                      {'$' +
                        parseFloat(kpi[1])
                          .toString()
                          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    </span>
                  </>
                )}
                {props.data.kpi_name ==
                  'CP & Wty - Avg Age / Miles Per Vehicle' && (
                  <>
                    <span
                      className={
                        session.sortOrder.order == 0 &&
                        props.data.kpi_slno == session.sortOrder.slno
                          ? 'kpi_data kpi-selected'
                          : 'kpi_data'
                      }
                    >
                      {parseFloat(kpi[0])
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' Years'}
                    </span>

                    <span className="dot_kpi"></span>

                    <span
                      className={
                        session.sortOrder.order == 1 &&
                        props.data.kpi_slno == session.sortOrder.slno
                          ? 'kpi_data kpi-selected'
                          : 'kpi_data'
                      }
                    >
                      {parseFloat(kpi[1])
                        .toString()
                        .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' Miles'}
                    </span>
                  </>
                )}

                {kpi.map((val, i) => (
                  <>
                    {props.data.kpi_name != 'Parts To Labor Ratio' &&
                      props.data.kpi_name !=
                        'CP & Wty - Avg Age / Miles Per Vehicle' && (
                        <>
                          <span
                            className={
                              session.sortOrder.order == i &&
                              props.data.kpi_slno == session.sortOrder.slno
                                ? 'kpi_data kpi-selected'
                                : 'kpi_data'
                            }
                          >
                            {formatCellValue(
                              (props.data.kpi_slno >= 31 &&
                                props.data.kpi_slno <= 33 &&
                                (session.menuFlag == 'FALSE' ||
                                  session.menuFlag == false)) ||
                                (props.data.kpi_slno >= 34 &&
                                  props.data.kpi_slno <= 37 &&
                                  (session.mpiFlag == 'FALSE' ||
                                    session.mpiFlag == false))
                                ? 0
                                : val,
                              i,
                              props.data.kpi_slno,
                              ((props.data.kpi_slno == 1 ||
                                props.data.kpi_slno == 2 ||
                                props.data.kpi_slno == 11) &&
                                i == 2) ||
                                (i == 1 && props.data.kpi_slno == 11) ||
                                ((props.data.kpi_slno == 33 ||
                                  props.data.kpi_slno == 37) &&
                                  (i == 3 || i == 2))
                                ? 2
                                : 1
                            )}
                          </span>
                          {i != kpi.length - 1 && (
                            <span className="dot_kpi"></span>
                          )}
                        </>
                      )}
                  </>
                ))}
              </div>
              {props.data.data[i].adv_or_tech_name ==
                props.column.parent.groupId &&
              (props.data.kpi_slno == 1 ||
                props.data.kpi_slno == 2 ||
                props.data.kpi_slno == 9 ||
                props.data.kpi_slno == 11 ||
                props.data.kpi_slno == 19 ||
                props.data.kpi_slno == 22 ||
                props.data.kpi_slno == 12 ||
                props.data.kpi_slno == 13 ||
                props.data.kpi_slno == 31 ||
                props.data.kpi_slno == 32 ||
                props.data.kpi_slno == 33 ||
                props.data.kpi_slno == 37 ||
                props.data.kpi_slno == 27 ||
                props.data.kpi_slno == 34 ||
                props.data.kpi_slno == 35) ? (
                <div
                  style={{
                    display: !hiddenGoals.includes(props.column.parent.groupId)
                      ? 'flex'
                      : 'none'
                  }}
                  className={'var-data-div'}
                >
                  {props.data.data[i].goal != '' &&
                  props.data.data[i].variance != '' ? (
                    <>
                      <span
                        style={{
                          display:
                            (parseFloat(props.data.data[i].goal) == 0 &&
                              parseFloat(props.data.data[i].variance) == 0) ||
                            parseFloat(props.data.data[i].goal) == 0
                              ? 'none'
                              : 'block'
                        }}
                      >
                        {props.data.data[i].goal}
                        {props.data.kpi_slno != 9 &&
                        props.data.kpi_slno != 11 &&
                        props.data.kpi_slno != 33 &&
                        props.data.kpi_slno != 37
                          ? '% '
                          : ' '}
                        /
                        <span
                          style={{
                            color:
                              props.data.data[i].variance > -10.0 &&
                              props.data.data[i].variance < 0.0 &&
                              props.data.kpi_slno != 27 &&
                              props.data.kpi_slno != 22 &&
                              props.data.kpi_slno != 12 &&
                              props.data.kpi_slno != 13
                                ? //  ? 'rgb(153, 63, 3)'
                                  'red'
                                : props.data.data[i].variance <= -10.0 &&
                                  props.data.kpi_slno != 27 &&
                                  props.data.kpi_slno != 22 &&
                                  props.data.kpi_slno != 12 &&
                                  props.data.kpi_slno != 13
                                ? 'red'
                                : // : props.data.data[i].variance <= +10.0 &&
                                //   (props.data.kpi_slno == 27 ||
                                //     props.data.kpi_slno == 22 ||
                                //     props.data.kpi_slno == 12 ||
                                //     props.data.kpi_slno == 13)
                                // ? 'rgb(153, 63, 3)'
                                props.data.data[i].variance > 0.0 &&
                                  (props.data.kpi_slno == 27 ||
                                    props.data.kpi_slno == 22 ||
                                    props.data.kpi_slno == 12 ||
                                    props.data.kpi_slno == 13)
                                ? 'red'
                                : props.data.data[i].goal != 0 &&
                                  props.data.data[i].variance == 0
                                ? //  ? '#fff'
                                  '#000'
                                : 'green'
                          }}
                        >
                          {' '}
                          {props.data.data[i].variance}
                          {props.data.kpi_slno != 9 &&
                          props.data.kpi_slno != 11 &&
                          props.data.kpi_slno != 33 &&
                          props.data.kpi_slno != 37
                            ? '%'
                            : ''}{' '}
                        </span>
                      </span>
                    </>
                  ) : (
                    ''
                  )}
                </div>
              ) : (
                ''
              )}
              {props.data.data[i].res_storename ==
                props.column.parent.groupId &&
              (props.data.kpi_slno == 1 ||
                props.data.kpi_slno == 2 ||
                props.data.kpi_slno == 9 ||
                props.data.kpi_slno == 11 ||
                props.data.kpi_slno == 19 ||
                props.data.kpi_slno == 22 ||
                props.data.kpi_slno == 12 ||
                props.data.kpi_slno == 13 ||
                props.data.kpi_slno == 31 ||
                props.data.kpi_slno == 32 ||
                props.data.kpi_slno == 33 ||
                props.data.kpi_slno == 37 ||
                props.data.kpi_slno == 27 ||
                props.data.kpi_slno == 34 ||
                props.data.kpi_slno == 35) ? (
                <div
                  style={{
                    display: !hiddenGoals.includes(props.column.parent.groupId)
                      ? 'flex'
                      : 'none'
                  }}
                  className={'var-data-div'}
                >
                  {props.data.data[i].goal !== '' &&
                  props.data.data[i].variance !== '' ? (
                    <>
                      <span
                        style={{
                          display:
                            (parseFloat(props.data.data[i].goal) == 0 &&
                              parseFloat(props.data.data[i].variance) == 0) ||
                            parseFloat(props.data.data[i].goal) == 0
                              ? 'none'
                              : 'block'
                        }}
                      >
                        {props.data.data[i].goal}
                        {props.data.kpi_slno !== 9 &&
                          props.data.kpi_slno !== 11 &&
                          props.data.kpi_slno !== 33 &&
                          props.data.kpi_slno !== 37 &&
                          '% '}
                        /
                        <span
                          style={{
                            color:
                              props.data.data[i].variance > -10.0 &&
                              props.data.data[i].variance < 0.0 &&
                              props.data.kpi_slno != 27 &&
                              props.data.kpi_slno != 22 &&
                              props.data.kpi_slno != 12 &&
                              props.data.kpi_slno != 13
                                ? // ? 'rgb(153, 63, 3)'
                                  'red'
                                : props.data.data[i].variance <= -10.0 &&
                                  props.data.kpi_slno != 27 &&
                                  props.data.kpi_slno != 22 &&
                                  props.data.kpi_slno != 12 &&
                                  props.data.kpi_slno != 13
                                ? 'red'
                                : props.data.data[i].variance > 0.0 &&
                                  (props.data.kpi_slno == 27 ||
                                    props.data.kpi_slno == 22 ||
                                    props.data.kpi_slno == 12 ||
                                    props.data.kpi_slno == 13)
                                ? 'red'
                                : props.data.data[i].goal != 0 &&
                                  props.data.data[i].variance == 0
                                ? // ? '#fff'
                                  '#000'
                                : 'green'
                          }}
                        >
                          {' '}
                          {props.data.data[i].variance}
                          {props.data.kpi_slno !== 9 &&
                            props.data.kpi_slno !== 11 &&
                            props.data.kpi_slno !== 33 &&
                            props.data.kpi_slno !== 37 &&
                            '%'}{' '}
                        </span>
                      </span>
                    </>
                  ) : (
                    ''
                  )}
                </div>
              ) : (
                ''
              )}
            </div>
          </div>
        );
      }
    } else {
      return <div className={'kpi-data-div'}></div>;
    }
  } else {
    return <div className={'kpi-data-div'}></div>;
  }
};
