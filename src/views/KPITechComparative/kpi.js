import React, {
  useCallback,
  useMemo,
  useRef,
  useState,
  useEffect
} from 'react';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Divider,
  FormControl,
  Grid,
  Button,
  TextField,
  Radio,
  RadioGroup,
  InputLabel,
  Select,
  CircularProgress
} from '@material-ui/core';
import { green } from '@mui/material/colors';
import clsx from 'clsx';
import { useHistory } from 'react-router';
import { makeStyles, withStyles } from '@material-ui/styles';
import { AgGridReact } from '@ag-grid-community/react';
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import { ModuleRegistry } from '@ag-grid-community/core';
import { ExcelExportModule } from '@ag-grid-enterprise/excel-export';
import { MenuModule } from '@ag-grid-enterprise/menu';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import ExportIcon from '@material-ui/icons/GetApp';
import Link from '@material-ui/core/Link';
import moment from 'moment';
import $ from 'jquery';
import * as XLSX from 'xlsx';
import * as ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';

import './styles.css';
import PageHeader from 'src/components/PageHeader';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import SaveIcon from '@material-ui/icons/Save';
import DialogTitle from '@material-ui/core/DialogTitle';
import DialogContent from '@material-ui/core/DialogContent';
import DialogActions from '@material-ui/core/DialogActions';
import Dialog from '@material-ui/core/Dialog';
import {
  getKpiComparativeReport,
  getKpiComparativeStoreReport,
  getDrillDownMonthYears,
  getKpiToggleOptionsWithTimeZone,
  getLatestClosedDate,
  insertKPIReportName,
  getEmail,
  getStoreNickName
} from 'src/utils/hasuraServices';
import RestoreIcon from '@material-ui/icons/Restore';
import {
  setSelectedStores,
  setHiddenKpiRows,
  setSelectedTech,
  setHiddenAdvisors,
  setRowSortOrder,
  setSelAdv,
  setKpiDataAll,
  setSelStoreIds,
  setSelectedAdvisors
} from 'src/actions';
import CustomHeaderGroup from './CustomHeaderGroup';
import KPIHeaderGroup from './KPIHeaderGroup';
import KPIColumnRenderer from './KPIColumnRenderer';
import TableCellRenderer from './TableCellRenderer';

import { getTimeZone, getYearValue } from 'src/utils/Utils';
import { useDispatch, useSelector } from 'react-redux';
import { usePDFExport } from 'src/components/PrintDoc';
import { useExcelExport } from 'src/components/PrintExcel';
import SuccessSnackbar from './SuccessSnackbar';
import SuccessSnackbarWarn from './SuccessSnackbar';
import SuccessSnackbarAlert from './SuccessSnackbarAlert';
import { TRUE } from 'sass';
import CPPartsMarkupVsPartsCost from 'src/components/charts/CPPartsMarkupVsPartsCost';
import { TramOutlined } from '@material-ui/icons';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import MuiTableCell from '@material-ui/core/TableCell';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import Checkbox from '@material-ui/core/Checkbox';
import Fade from '@material-ui/core/Fade';
import { withKeycloak } from '@react-keycloak/web';
import { isNonNullType } from 'graphql';
import MailIcon from '@material-ui/icons/Mail';
import FileCopyOutlinedIcon from '@material-ui/icons/FileCopyOutlined';
import EmailDialogKpi from 'src/components/EmailDialogKpi';
import PictureAsPdfIcon from '@material-ui/icons/PictureAsPdf';
import { async } from 'validate.js';

ModuleRegistry.registerModules([
  ClientSideRowModelModule,
  ExcelExportModule,
  MenuModule
]);

var lodash = require('lodash');
const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  boxClass: {
    padding: 25
  },
  kpiGrid: {
    height: '60px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    border: '1px solid',
    marginLeft: '8px',
    paddingRight: '16px',
    display: 'flex',
    marginRight: 16
    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  kpiHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  kpiStoreHeading: {
    paddingLeft: '10px',
    display: 'block',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  kpiTable: {
    height: window.innerHeight - 200 + 'px',
    // width: !!(
    //   typeof keycloak.realmAccess.roles !== 'undefined' &&
    //   keycloak.realmAccess.roles.includes('client') === true
    // )
    //   ? '1110px'
    //   : '1200px',
    width: 'auto',
    alignContent: 'center',
    marginLeft: '8px',
    paddingRight: '16px'

    //display: this.state.isLoading == true ? 'none' : 'block'
    //pointerEvents: disableTable
  },
  calButton: {
    color: '#fff !important',
    width: '50px'
    // marginTop: 31
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  linkItem: {
    cursor: 'pointer'
  },
  linkItemDisable: {
    pointerEvents: 'none',
    color: 'grey'
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    },
    marginRight: 5
  }
  // paper: {
  //   '@media (max-width: 1920px)': {
  //     maxWidth: '1150px',
  //     maxHeight: '700px'
  //   },

  //   '@media (max-width: 1440px)': {
  //     maxWidth: '1500px',
  //     maxHeight: '610px'
  //   },

  //   '@media (max-width: 1280px)': {
  //     maxWidth: '1500px',
  //     maxHeight: '600px'
  //   }
  // }
  // paper: {
  //   '@media (max-width: 1920px)': {
  //     maxWidth: '1150px',
  //     maxHeight: '700px'
  //   },

  //   '@media (max-width: 1440px)': {
  //     maxWidth: '1500px',
  //     maxHeight: '610px'
  //   },

  //   '@media (max-width: 1280px)': {
  //     maxWidth: '1500px',
  //     maxHeight: '600px'
  //   }
  // }
}));
const getRows = () => [
  { cells: [] },
  {
    cells: [
      {
        styleId: 'coverHeading',
        data: {
          value: 'KPI Advisor Comparative',
          type: 'String',
          mergeAcross: 3
        }
      },
      {
        data: {
          value: 'Fixed Ops Performance Center',
          type: 'String',
          mergeAcross: 6,
          font: {
            size: 12
          }
        }
      }
    ]
  },

  { cells: [] }
];
const Kpi = props => {
  const { exportToPDF } = usePDFExport();
  const { exportToExcel, tryExcelData } = useExcelExport();
  const gridRef = useRef();
  const classes = useStyles();
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const history = useHistory();

  const pathaname = history.location.pathname.split('/')[1];
  let createdBy =
    props && props.kpiReportData && props.kpiReportData.created_by;

  const allStore =
    JSON.parse(localStorage.getItem('selectedStoreId')).length > 1;

  const [loadingIconExcel, setLoadingIconExcel] = useState(false);
  const [successExcel, setSuccessExcel] = useState(false);

  const [loadingIconPDF, setLoadingIconPDF] = useState(false);
  const [successPDF, setSuccessPDF] = useState(false);

  const [visibleCol, setVisibleCol] = useState([]);
  const [visibleTotals, setVisibleTotals] = useState([]);
  const [rowData, setRowData] = useState([]);
  const [allData, setAllData] = useState([]);
  const [kpiDataArr, setKpiDataArr] = useState([]);
  const [isFrom, setIsFrom] = useState(props.kpiReportData.isFrom);
  const [sortOrderArr, setSortOrder] = useState([]);
  const [rowSortValue, setRowSortValue] = useState(0);
  const [advCount, setAdvCount] = useState(0);
  const [pickerChanged, setPickerChanged] = useState(
    props.kpiReportData.pickerStatus
  );
  const [toggleChanged, setToggleChanged] = useState(false);
  const [sidebarClicked, setSidebarClicked] = useState(false);
  const [isFromSidebar, setIsFromSidebar] = useState(false);
  const [arrowClicked, setArrowClicked] = useState(false);
  const [arrowClickedFrom, setArrowClickedFrom] = useState('');
  const [filterStart, setFilterStart] = useState(props.kpiReportData.startDate);
  const [filterEnd, setFilterEnd] = useState(props.kpiReportData.endDate);
  const [kpiSortOrder, setKpiSortOrder] = useState(
    props.kpiReportData.kpi_sort_order
  );
  const [kpiHiddenNumbers, setKpiHiddenNumbers] = useState(
    props.kpiReportData.hidden_kpi_numb
  );
  const [filterChanged, setFilterChanged] = useState(false);
  const [advFilterChanged, setAdvFilterChanged] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isPageLoading, setIsPageLoading] = useState(false);
  const [hiddenRowId, setHiddenRowId] = useState([]);
  const [clickHeader, setClickHeader] = useState(false);
  const [gridApi, setGridApi] = useState({});
  const [dates, setDates] = useState([]);
  const [selectedRowIds, setSelectedRowIds] = useState([]);
  const [thisWeek, setThisWeek] = useState('');
  const [lastWeek, setLastWeek] = useState('');
  const [lastTwoWeek, setLastTwoWeek] = useState('');
  const [yesterDay, setYesterDay] = useState('');
  const [dayBfYest, setDayBfYest] = useState('');
  const [mtd, setMtd] = useState('');
  const [lastMonth, setLastMonth] = useState('');
  const [lastThreeMonths, setLastThreeMonths] = useState('');
  const [lastQtr, setLastQtr] = useState('');
  const [ytd, seTyd] = useState('');
  const [lastTwelveMonths, setLastTwelveMonths] = useState('');
  const [lastYear, setLastYear] = useState('');
  const [hideGoals, setHideGoals] = useState(
    props.kpiReportData.selected_goals
  );
  const [allGoals, setAllGoals] = useState([]);
  const [expandedKPIs, setExpandedKPIs] = useState(
    props.kpiReportData.expanded_kpis
  );
  const [selAdvisors, setSelAdvisors] = useState(props.kpiReportData.kpi_ids);
  const [selStores, setSelStores] = useState(props.kpiReportData.kpi_ids);
  const [closedDate, setClosedDate] = useState('');
  const [openSaveDlg, setOpenSaveDlg] = useState('');
  const [reportName, setReportName] = useState(props.kpiReportData.report_name);
  const [reportNameCopy, setReportNameCopy] = useState('');
  const [errorReport, setErrorReport] = useState(false);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [openSnackbarWarn, setOpenSnackbarWarn] = useState(false);
  const [requiredText, setRequiredText] = useState(false);
  const [privateChecked, setPrivateChecked] = useState(true);
  const [copy, setCopy] = useState(false);
  const [copyFile, setCopyFile] = useState(false);
  // const [filterText, setFilterText] = useState(session.kpiHomeToggle);
  const [filterText, setFilterText] = useState(
    props.kpiReportData.kpiHomeToggle
  );
  const [openDialogue, setOpenDialogue] = useState(false);
  const [mailUsers, setMailUsers] = useState([]);
  const [selectedType, setSelectedType] = useState(
    props.kpiReportData.visibility
  );
  const [selectedTypeUpdate, setSelectedTypeUpdate] = useState(
    props.kpiReportData.visibility
  );
  const [visibile, setVisible] = useState(props.kpiReportData.visibility);
  const [image, setImage] = useState();
  const [excelRowCount, setExcelRowCount] = useState(0);
  const [excelRowsCount, setExcelRowsCount] = useState(0);
  // const [createdBy, setCreatedBy] = useState(props.kpiReportData.created_by);
  const [openDilog, setOpenDilog] = useState(false);
  const [openDilogAll, setOpenDilogAll] = useState(false);
  const [openDilogPdf, setOpenDilogpdf] = useState(false);
  const [kpiSortedArray, setKpiSortedArray] = useState([]);
  const [parent, setParent] = useState(props.kpiReportData.parent);
  const [storeNickName, setStoreNickName] = useState('');
  const [selectedStore, setSelectedStore] = useState(
    localStorage.getItem('selectedStoreId')
  );

  const [columnDefs, setColumnDefs] = useState([
    {
      rowGroup: true,
      field: 'kpi_type',
      suppressMenu: true,
      headerClass: 'kpi-hide-header',
      hide: true,
      minWidth: 300,
      rowGroupIndex: 0,
      resizable: false,
      suppressSorting: false,
      pinned: 'left',
      suppressColumnsToolPanel: true,
      onCellClicked: params => {
        // Disable export when this cell is clicked
        //disableExport();
      }
      //cellClass: 'kpiHeader'
    }
  ]);
  const [gridOptions, setGridOptions] = useState({
    defaultColDef: {
      sortable: false,
      filter: 'agTextColumnFilter',
      resizable: true
    },

    columnDefs: columnDefs,
    enableSorting: false,
    enableFilter: true,
    pagination: false,

    getRowId: params => {
      return params;
    },
    rowSelection: 'multiple', // ADDED
    suppressRowClickSelection: true,
    isExternalFilterPresent: isExternalFilterPresent, // ADDED
    //doesExternalFilterPass: doesExternalFilterPass, // ADDED
    onSelectionChanged: onSelectionChanged // ADDED
  });
  let isDisabled =
    createdBy != localStorage.getItem('userID') &&
    props.keycloak.realmAccess.roles.includes('superadmin');
  const containerStyle = useMemo(() => ({ height: '100%', marginTop: 8 }), []);

  let fileName = useMemo(() => {
    if (pathaname === 'KPIReportTechComparative') {
      return reportNameCopy && parent == 'savedReports'
        ? `KPI Tech Comparative - Saved Reports - ${reportNameCopy}`
        : reportName && parent == 'savedReports'
        ? `KPI Tech Comparative - Saved Reports - ${reportName}`
        : 'KPI Tech Comparative';
    }
    return '';
  }, [pathaname, reportName, reportNameCopy]);

  let fileNameExport = useMemo(() => {
    if (pathaname === 'KPIReportTechComparative') {
      return reportNameCopy
        ? `KPI Tech Comparative - ${storeNickName} - ${reportNameCopy}`
        : reportName
        ? `KPI Tech Comparative - ${storeNickName} - ${reportName}`
        : `KPI Tech Comparative - ${storeNickName}`;
    }
    return '';
  }, [pathaname, reportName, reportNameCopy, storeNickName]);
  useEffect(() => {
    if (props.kpiReportData) {
      var kpiIds;
      if (
        typeof props.kpiReportData.kpi_ids != 'undefined' &&
        props.kpiReportData.kpi_ids.length != 0
      ) {
        kpiIds = props.kpiReportData.kpi_ids;
      } else {
        if (session.technician.includes('All')) {
          kpiIds = [];
        } else {
          kpiIds = session.technician;
          kpiIds.unshift('Total Shop', 'Total Selected');
        }
      }
      console.log('pickerchanged--1', props);
      setPickerChanged(props.kpiReportData.pickerStatus);
      setReportName(props.kpiReportData.report_name);
      setFilterStart(props.kpiReportData.startDate);
      setFilterEnd(props.kpiReportData.endDate);
      setSelAdvisors(kpiIds);
      setSelStores(props.kpiReportData.kpi_ids);
      setExpandedKPIs(props.kpiReportData.expanded_kpis);
      setKpiHiddenNumbers(props.kpiReportData.hidden_kpi_numb);
      setKpiSortOrder(props.kpiReportData.kpi_sort_order);
      setHideGoals(props.kpiReportData.selected_goals);
      // setCreatedBy(props.kpiReportData.createdBy);
      setParent(props.kpiReportData.parent);
      setSelectedType(props.kpiReportData.visibility);
      setSelectedTypeUpdate(props.kpiReportData.visibility);
      setVisible(props.kpiReportData.visibility);
      setFilterText(session.kpiHomeToggle);
      setIsFrom(props.kpiReportData.isFrom);
    }
  }, [props.kpiReportData]);

  useEffect(() => {
    if (session.storeSelected != '' && session.storeSelected != selectedStore) {
      setSelectedStore(localStorage.getItem('selectedStoreId'));
      setStoreNickName('');
      setReportName('');
      setReportNameCopy('');
      setParent('');
      // setCreatedBy('');

      setVisibleCol([]);
      setVisibleTotals([]);
      setSelAdv([]);
      setSelStores([]);
      setKpiSortOrder({ row: '', order: 0 });
      setKpiHiddenNumbers([]);
      setExpandedKPIs(['Pricing - Customer Pay']);
      setHideGoals([]);
      setSelAdvisors([]);
      console.log('pickerchanged--2');
      setPickerChanged(true);
      dispatch(setSelectedStores([]));
      dispatch(setRowSortOrder({ row: '', order: 0 }));
      dispatch(setHiddenKpiRows([]));
    }
  }, [session.storeSelected]);
  const getParams = () => ({
    fileName: fileNameExport,
    sheetName: 'Sheet1',

    processCellCallback(params) {
      let value;
      if (typeof params.node.data == 'undefined') {
        if (typeof params.value == 'string') {
          value =
            params.node.allLeafChildren[0].data.kpi_type_code +
            '   ' +
            params.node.allLeafChildren[0].data.kpi_type;
        } else {
          value = '';
        }
      } else {
        var valArr = params.node.data.data.filter(
          (item, i) => i == params.value
        );

        if (valArr.length > 0) {
          let data = valArr[0].kpi_data.split('/');
          let goal = valArr[0].goal;
          let variance = valArr[0].variance;
          if (
            params.node.data.kpi_name != 'Parts To Labor Ratio' &&
            params.node.data.kpi_name !=
              'CP & Wty - Avg Age / Miles Per Vehicle'
          ) {
            data = data
              .map((ele, i) => {
                return formatCellValue(
                  params.node.data.kpi_slno >= 31 &&
                    params.node.data.kpi_slno <= 33 &&
                    params.node.data.kpi_slno >= 34 &&
                    params.node.data.kpi_slno <= 37
                    ? 0
                    : ele,
                  i,
                  params.node.data.kpi_slno,
                  ((params.node.data.kpi_slno == 1 ||
                    params.node.data.kpi_slno == 2 ||
                    params.node.data.kpi_slno == 11) &&
                    i == 2) ||
                    ((params.node.data.kpi_slno == 33 ||
                      params.node.data.kpi_slno == 37) &&
                      (i == 3 || i == 2))
                    ? 2
                    : 1
                );
              })
              .join(' * ');
          } else if (params.node.data.kpi_name == 'Parts To Labor Ratio') {
            data =
              '$' +
              parseFloat(data[0])
                .toFixed(2)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
              ' to ' +
              '$' +
              parseInt(data[1])
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
          } else if (
            params.node.data.kpi_name ==
            'CP & Wty - Avg Age / Miles Per Vehicle'
          ) {
            data =
              parseFloat(data[0])
                .toFixed(1)
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
              ' Years' +
              ' * ' +
              parseInt(data[1])
                .toString()
                .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
              ' Miles';
          }

          // value = data;

          if (
            isFrom == 'KPIReportTechComparative' &&
            valArr[0].adv_or_tech_name == 'Total Selected'
          ) {
            value = data;
          } else if (
            isFrom == 'KPIReportStoreComparative' &&
            (valArr[0].res_storename == 'Group Roll-Up' ||
              valArr[0].res_storename == 'Group Average')
          ) {
            value = data;
          } else {
            // //console.log('ccc===12', column.colId);
            let goalVal;
            if (
              params.node.data.kpi_slno != 9 &&
              params.node.data.kpi_slno != 11 &&
              params.node.data.kpi_slno != 33 &&
              params.node.data.kpi_slno != 37
            ) {
              goalVal = goal + '%/' + variance + '%';
            } else {
              goalVal = goal + '/' + variance;
            }

            //goalVal = goal + '%/' + variance + '%';
            value =
              goal != '' && variance != ''
                ? data + '          ' + goalVal
                : data;
          }
        } else {
          value = params.node.data.kpi_no + '   ' + params.node.data.kpi_name;
        }
      }

      return value === undefined ? '' : value;
    },
    prependContent: getRows()
  });
  const defaultExcelExportParams = useMemo(() => {
    return getParams();
  }, [getParams]);
  const popupParent = useMemo(() => {
    return document.body;
  }, []);
  const excelStyles = useMemo(() => {
    return [
      {
        id: 'coverHeading',
        alignment: {
          vertical: 'Center'
        },
        interior: {
          color: '#c65911',
          pattern: 'Solid',
          patternColor: undefined
        },
        font: {
          color: '#ffffff',
          size: 12
        },
        borders: {
          borderBottom: {
            color: '#c65911',
            lineStyle: 'Continuous',
            weight: 1
          }
        }
      },
      {
        id: 'header',
        alignment: {
          vertical: 'Center'
        },
        interior: {
          color: '#c65911',
          pattern: 'Solid',
          patternColor: undefined
        },
        font: {
          color: '#ffffff',
          size: 12
        },
        borders: {
          borderBottom: {
            color: '#c65911',
            lineStyle: 'Continuous',
            weight: 1
          }
        }
      },
      {
        id: 'headerGroup',
        font: {
          bold: true
        }
      },

      {
        id: 'custom-no-data-row',
        interior: {
          color: '#ffc000',
          pattern: 'Solid'
        },
        font: {
          // color: '#ffffff',
          bold: true,
          size: 10
        }
      },
      {
        id: 'custom-cell',
        interior: {
          color: '#c65911',
          pattern: 'Solid'
        },
        font: {
          color: '#ffffff',
          size: 10
        }
      },
      {
        id: 'custom-cell cell-no-border-bottom',
        interior: {
          color: '#c65911',
          pattern: 'Solid'
        },
        font: {
          color: '#ffffff',
          size: 10
        }
      },
      {
        id: 'custom-cell cell-no-border-top',
        interior: {
          color: '#c65911',
          pattern: 'Solid'
        },
        font: {
          color: '#ffffff',
          size: 10
        }
      },

      {
        id: 'redFont',
        font: {
          fontName: 'Calibri Light',
          underline: 'Single',
          italic: true,
          color: '#BB0000'
        }
      },
      {
        id: 'darkGreyBackground',
        interior: {
          color: '#888888',
          pattern: 'Solid'
        },
        font: {
          fontName: 'Calibri Light',
          color: '#ffffff'
        }
      }
    ];
  }, []);
  useEffect(() => {
    if (
      localStorage.getItem('closedDate') == null &&
      localStorage.getItem('closedDate') == ''
    ) {
      getLatestClosedDate(result => {
        if (result) {
          var openDate = '';
          var Date1 = result[0].value;

          setClosedDate(Date1);

          localStorage.setItem('closedDate', Date1);
        }
      });
    } else {
      setClosedDate(localStorage.getItem('closedDate'));
    }
  }, [session.technician]);
  useEffect(() => {
    setExcelRowsCount(excelRowCount);
  }, [excelRowCount]);

  useEffect(() => {
    convertImageToBase64('/images/logos/logo_armatus.png', base64Image => {
      setImage(base64Image);
    });
  }, []);
  useEffect(() => {
    getStoreNickName(result => {
      if (
        result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes &&
        result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0] !=
          null &&
        result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0]
          .storeNickname != null &&
        result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0].storeNickname.trim() !=
          ''
      ) {
        var nickName =
          result.data.statelessCcPhysicalRoGetStoreNicknameDetails.nodes[0]
            .storeNickname;

        if (pathaname == 'KPIReportTechComparative') {
          setStoreNickName(nickName);
        } else {
          setStoreNickName(localStorage.getItem('storeGroup'));
        }
      } else {
        if (pathaname == 'KPIReportTechComparative') {
          setStoreNickName(localStorage.getItem('storeSelected'));
        } else {
          setStoreNickName(localStorage.getItem('storeGroup'));
        }
      }
    });
  }, [session.storeSelected]);
  // let closedDate = localStorage.getItem('closedDate');

  const sideBar = useMemo(() => {
    return {
      toolPanels: [
        {
          id: 'columns',
          labelDefault: 'Columns',
          labelKey: 'columns',
          iconKey: 'columns',
          toolPanel: 'agColumnsToolPanel',

          toolPanelParams: {
            suppressRowGroups: true,
            suppressValues: true,
            suppressPivots: true,
            suppressPivotMode: true,
            suppressColumnFilter: false,
            suppressColumnSelectAll: false,
            suppressColumnExpandAll: true,
            suppressColumnsToolPanel: true,
            allowDragFromColumnsToolPanel: true,
            suppressSyncLayoutWithGrid: false,
            // prevents columns being reordered from the Columns Tool Panel
            suppressColumnMove: false
          }
        }
      ],
      defaultToolPanel: 'columns'
    };
  }, []);
  const defaultColDef = useMemo(() => {
    return {
      sortable: false,
      resizable: false,
      suppressRowGroups: false,
      enableValue: true,
      enableRowGroup: true,
      enablePivot: true,
      groupHideOpenParents: true
    };
  }, []);
  useEffect(() => {
    getKpiToggleOptionsWithTimeZone(getTimeZone(), result => {
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;
        setDates(dataArr);
        setThisWeek(
          moment(dataArr[0].thisweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].thisweekenddate).format("MMM DD ' YY")
        );
        setLastWeek(
          moment(dataArr[0].lastweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lastweekenddate).format("MMM DD ' YY")
        );
        setLastTwoWeek(
          moment(dataArr[0].lasttwoweekstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].lasttwoweekenddate).format("MMM DD ' YY")
        );
        setYesterDay(moment(dataArr[0].yesterday).format("MMM DD ' YY"));
        // setToday(moment(dataArr[0].today).format("MMM DD ' YY"));
        setDayBfYest(
          moment(dataArr[0].dayBeforeYesterday).format("MMM DD ' YY")
        );
        setMtd(
          moment(dataArr[0].mtdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].mtdenddate).format("MMM DD ' YY")
        );
        setLastMonth(moment(dataArr[0].lastmonthstartdate).format('MMM'));
        setLastThreeMonths(
          moment(dataArr[0].lastthreemonthstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastthreemonthenddate).format('MMM')
        );
        setLastQtr(
          moment(dataArr[0].lastquarterstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastquarterenddate).format('MMM')
        );
        if (filterStart == '' && filterEnd == '') {
          // setFilterStart(moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD'));
          // setFilterEnd(moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'));

          if (localStorage.getItem('kpiDataStatus') == 1) {
            setFilterStart(
              moment(dataArr[0].mtdstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(moment(dataArr[0].mtdenddate).format('YYYY-MM-DD'));
            console.log('pickerchanged--3');
            setPickerChanged(true);
            setFilterChanged(true);
          } else {
            setFilterStart(
              moment(dataArr[0].lastmonthstartdate).format('YYYY-MM-DD')
            );
            setFilterEnd(
              moment(dataArr[0].lastmonthenddate).format('YYYY-MM-DD')
            );
            console.log('pickerchanged--4');
            setPickerChanged(true);
            setFilterChanged(true);
          }
        }
        seTyd(
          moment(dataArr[0].ytdstartdate).format('MMM DD') +
            ' to ' +
            moment(dataArr[0].ytdenddate).format("MMM DD ' YY")
        );
        setLastTwelveMonths(
          moment(dataArr[0].lasttwelvemonthstartdate).format("MMM ' YY") +
            ' to ' +
            moment(dataArr[0].lasttwelvemonthenddate).format("MMM ' YY")
        );
        setLastYear(
          moment(dataArr[0].lastyearstartdate).format('MMM') +
            ' to ' +
            moment(dataArr[0].lastyearenddate).format('MMM') +
            " ' " +
            getYearValue(dataArr[0].lastyearenddate)
        );
        setIsPageLoading(true);
      }
    });
    getDrillDownMonthYears(result => {
      if (result.data.statelessCcDrilldownGetMonthYears.nodes) {
        var monthArr = result.data.statelessCcDrilldownGetMonthYears.nodes;

        // setMonths(monthArr);

        var startDate = moment(new Date(monthArr[0].monthYear + '-01')).format(
          'YYYY-MM-DD'
        );
        var endDate = moment(new Date())
          .clone()
          .format('YYYY-MM-DD');
        //  setSelectedDates([endDate, endDate]);
      }
    });
  }, []);

  useEffect(() => {
    if (
      pickerChanged &&
      typeof filterStart != 'undefined' &&
      typeof filterEnd != 'undefined'
    ) {
      var advisor = session.technician;
      //setVisibleCol([]);
      //setVisibleTotals([]);

      setIsFromSidebar(false);
      // dispatch(setSelectedTech([]));
      //  dispatch(setSelectedStores([]));
      //   dispatch(setHiddenKpiRows([]));
      setIsLoading(false);

      if (isFrom == 'KPIReportTechComparative') {
        var arrAdv;
        const allowedValues = ['Total Selected', 'Total Shop'];
        const containsOtherValues = selAdvisors.some(
          value => !allowedValues.includes(value)
        );
        var allAdvArr = selAdvisors;

        if (
          parent == 'savedReports' ||
          advFilterChanged ||
          advisor[0] != 'All' ||
          (selAdvisors.length > 0 && containsOtherValues)
        ) {
          arrAdv = selAdvisors.filter(function(obj) {
            return obj != 'Total Selected' && obj != 'Total Shop';
          });
        } else {
          arrAdv = null;
        }
        // if (selAdvisors.length > 0 && containsOtherValues) {
        //   var arrAdv = selAdvisors.filter(function (obj) {
        //     return obj != 'Total Selected' && obj != 'Total Shop';
        //   });
        // } else {
        //   arrAdv = null;
        // }

        getKpiComparativeReport(
          filterStart,
          filterEnd,
          2,
          2,
          //advisor[0] == 'All' ? null : advisor,
          null,
          'KPI_TECH_COMP',
          getTimeZone(),

          null,
          '',
          'C',
          'Customer',
          'N',
          arrAdv != null && arrAdv.length > 0 && arrAdv[0] == 'All'
            ? null
            : arrAdv,
          result => {
            if (
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails
            ) {
              setIsLoading(true);
              setPickerChanged(false);
              var data = JSON.parse(
                result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                  .kpiScorecardDetails[0].jsonData
              );

              dispatch(setKpiDataAll(data));
              if (data[0].data != null) {
                let selectedIndex;
                if (parent == 'savedReports' && !containsOtherValues && sidebarClicked) {
                  data.forEach((originalItem, index) => {
                    if (isFrom == 'KPIReportTechComparative') {
                      selectedIndex = originalItem.data.findIndex(
                        data => data.adv_or_tech_name === 'Total Selected'
                      );
                    } else {
                      selectedIndex = originalItem.data.findIndex(
                        data => data.res_storename === 'Group Average'
                      );
                    }

                    if (selectedIndex !== -1) {
                      originalItem.data[selectedIndex].kpi_data = '0/0/0';
                    }
                  });
                }

                setRowData(data);

                setAllData(data);
                setAdvCount(
                  data && data[0] && data[0].data && data[0].data.length - 2
                );
                setArrowClickedFrom('page');
                let advDataArr = Object.keys(data[0].data).map(function(k) {
                  return data[0].data[k].adv_or_tech_name;
                });
                var selAdvsArr;
                // if (selAdvisors.length > 0 && containsOtherValues) {
                //   selAdvsArr = selAdvisors;
                // } else {
                //   selAdvsArr = []
                // }
                if (
                  parent == 'savedReports' ||
                  advFilterChanged ||
                  advisor[0] != 'All' ||
                  selAdvisors.length > 0
                ) {
                  console.log('pppp===');
                  selAdvsArr = selAdvisors;
                } else {
                  console.log('qqq===');
                  selAdvsArr = [];
                }

                if (selAdvsArr.length == 0) {
                  let advIdArr = Object.keys(data[0].data).map(function(k) {
                    return data[0].data[k].adv_or_tech_id;
                  });

                  setSelAdvisors(advIdArr);
                }
                const containsGoalValues = hideGoals.some(
                  value => !allowedValues.includes(value)
                );

                if (
                  parent == 'savedReports' ||
                  ((advFilterChanged ||
                    (selAdvisors.length == 0 && advisor[0] != 'All') ||
                    selAdvisors.length > 0) &&
                    hideGoals.length != 0)
                ) {
                  setHideGoals(hideGoals);
                } else {
                  setHideGoals(advDataArr);
                }

                if (allGoals.length == 0) {
                  setAllGoals(advDataArr);
                }

                if (selAdvsArr.length > 0) {
                  var columns = visibleCol;
                  var visColumns = [];

                  if (selAdvsArr[0] != 'All') {
                    Object.keys(data[0].data).map(function(k) {
                      if (!allAdvArr.includes(data[0].data[k].adv_or_tech_id)) {
                        visColumns.push(data[0].data[k].adv_or_tech_name);
                      }
                    });

                    if (advFilterChanged) {
                      visColumns = visColumns.filter(function(obj) {
                        return obj != 'Total Shop' && obj != 'Total Selected';
                      });
                    }

                    setVisibleCol(visColumns);
                    visColumns.map(function(element) {
                      gridApi.columnApi.setColumnVisible(element + '_1', false);
                    });

                    // if(isFromSidebar){
                    setIsFromSidebar(true);
                  }

                  //  }
                  // setIsFromSidebar(true);
                } else {
                  setVisibleCol([]);
                }
                if (Object.keys(gridApi).length != 0) {
                  gridRef.current.api.forEachNode(node => {
                    if (node.group && expandedKPIs.includes(node.key)) {
                      // node.setExpanded(false);
                      //  gridApi.api.redrawRows();
                      gridRef.current.api.setRowNodeExpanded(node, false);
                    }

                    // if (node.group)
                    //     node.setExpanded(value);
                  });

                  if (session.hiddenKpis.length > 0) {
                    session.hiddenKpis.map(kpis => {
                      hideRow(gridApi, kpis.kpi, kpis.type);
                    });
                  }
                  if (session.sortOrder.row != '') {
                    onArrowClicked(
                      gridApi,
                      session.sortOrder.row,
                      session.sortOrder.slno,
                      session.sortOrder.order,
                      'kpi'
                    );
                  }
                }
              } else {
                if (Object.keys(gridApi).length != 0) {
                  let colDefs = gridApi.api.columnController.columnDefs;

                  colDefs = colDefs.filter(e => e.field == 'kpi_type');

                  gridApi.api.setColumnDefs(colDefs);
                }
              }
            }
          }
        );
      } else {
        var arrAdv;
        if (selStores.length > 0) {
          var arrAdv = selStores;
        } else {
          arrAdv = null;
        }
        var allAdv = arrAdv;
        if (arrAdv != null) {
          arrAdv = arrAdv.filter(function(obj) {
            return obj != 'Group Average' && obj != 'Group Roll-Up' && obj != 0;
          });
        }
      }
    }
  }, [pickerChanged, session.storeSelected]);

  useEffect(() => {
    if (parent != 'savedReports') {
      //console.log('log--1');
      setSelAdvisors([]);

      setSelStores([]);
      setVisibleCol([]);
      setVisibleTotals([]);
    }
    setIsFromSidebar(false);
    var advisor = session.technician;

    if (advisor[0] == 'All') {
      if (parent == 'savedReports') {
        dispatch(setSelectedAdvisors(selAdvisors));
      } else {
        dispatch(setSelectedAdvisors([]));
        //console.log('log--2');
        setSelAdvisors([]);
      }
    } else {
      dispatch(setSelectedAdvisors(advisor));
      setSelAdvisors(advisor);
    }
    setAdvFilterChanged(false);
    setHideGoals([]);
    dispatch(setSelectedStores([]));
    dispatch(setRowSortOrder({ row: '', order: 0 }));
    dispatch(setHiddenKpiRows([]));
  }, [isFrom, session.storeSelected]);
  useEffect(() => {
    if (
      typeof filterStart != 'undefined' &&
      filterStart != '' &&
      typeof filterEnd != 'undefined' &&
      filterEnd != ''
    ) {
      setRowData([]);

      setIsFromSidebar(false);

      if (session.technician.includes('All')) {
        setAdvFilterChanged(false);
      } else {
        setAdvFilterChanged(true);
      }
      var advisor = session.technician;

      if (advisor[0] == 'All') {
        if (parent != 'savedReports') {
          dispatch(setSelectedAdvisors([]));

          setSelAdvisors([]);
        }
      } else {
        if (!advisor.includes('Total Selected')) {
          advisor.push('Total Selected');
        }
        if (!advisor.includes('Total Shop')) {
          advisor.push('Total Shop');
        }
        setSelAdvisors(advisor);
      }
      setToggleChanged(false);
      setPickerChanged(true);
    }
  }, [session.technician]);
  useEffect(() => {
    if (isLoading) {
      var roData = rowData;
      if (roData.length > 0) {
        if (Object.keys(gridApi).length != 0) {
          if (isFrom == 'KPIReportTechComparative') {
            dynamicallyConfigureColumnsFromObject(gridApi, roData, 'desc');
          } else {
            dynamicallyConfigureColumnsFromObjectStoreWise(
              gridApi,
              roData,
              'desc'
            );
          }

          setRowData(roData);
        }
      }
    }
  }, [rowData]);
  useEffect(() => {
    if (typeof kpiHiddenNumbers != 'undefined') {
      if (kpiHiddenNumbers.length > 0) {
        dispatch(setHiddenKpiRows(kpiHiddenNumbers));
      }

      if (kpiSortOrder.row != '') {
        var sort = kpiSortOrder;

        dispatch(setRowSortOrder(sort));
      }
    }
  }, [kpiHiddenNumbers, kpiSortOrder]);
  const handleSnackbarClose = () => {
    setOpenSnackbar(false);
    // setTimeout(() => {
    //   this.setState({ goalFail: false });
    // }, 2000);
  };
  const handleSnackbarCloseWarn = () => {
    setOpenSnackbarWarn(false);
  };
  const handleRecalc = (params, arr, opt, selIds) => {
    var permittedStores = localStorage.getItem('allPermittedStores');

    if (Object.keys(params).length != 0) {
      params.api.showLoadingOverlay();
    }
    setClickHeader(true);

    //gridApi.columnApi.setColumnVisible('Group Average_1', true);
    //dispatch(setHiddenKpiRows([]));
    if (isFrom == 'KPIReportTechComparative') {
      if (rowData.length > 0) {
        let numbers = Object.keys(rowData[0].data).map(function(k) {
          return rowData[0].data[k].adv_or_tech_id;
        });

        var advArr = session.selectedAdvisors;

        if (parent != 'savedReports' && !advFilterChanged) {
          if (!isFromSidebar) {
            var arrAdv = numbers.filter(function(obj) {
              return (
                obj != 'Total Shop' &&
                obj != 'Total Selected' &&
                !advArr.includes(obj)
              );
            });
          } else {
            var arrAdv = numbers.filter(function(obj) {
              return (
                obj != 'Total Shop' &&
                obj != 'Total Selected' &&
                advArr.includes(obj)
              );
            });
          }
          // }
        } else {
          var arrAdv = selIds.filter(function(obj) {
            return obj != 'Total Shop' && obj != 'Total Selected';
          });
        }
      }

      getKpiComparativeReport(
        filterStart,
        filterEnd,
        2,
        2,

        null,
        'KPI_TECH_COMP',
        getTimeZone(),
        advCount == arr.length
          ? 'NA'
          : isFromSidebar && arr.length <= 0
          ? null
          : !advFilterChanged && arr.length <= 0
          ? null
          : toggleChanged
          ? selIds.filter(function(obj) {
              return obj != 'Total Shop' && obj != 'Total Selected';
            })
          : arrAdv.length > 0
          ? arrAdv
          : session.technician.length > 0 && session.technician[0] != 'All'
          ? session.technician.filter(function(obj) {
              return obj != 'Total Shop' && obj != 'Total Selected';
            })
          : null,

        '',
        'C',
        'Customer',
        'Y',
        null,
        result => {
          if (
            result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
              .kpiScorecardDetails
          ) {
            setIsLoading(true);
            setPickerChanged(false);

            var data = JSON.parse(
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails[0].jsonData
            );
            rowData.forEach((originalItem, index) => {
              const replacementItem = data.find(
                replacement => replacement.kpi_slno === originalItem.kpi_slno
              );
              if (replacementItem) {
                const selectedIndex = originalItem.data.findIndex(
                  data => data.adv_or_tech_name === 'Total Selected'
                );
                if (selectedIndex !== -1) {
                  originalItem.data[selectedIndex] = replacementItem.data[0];
                }
              }
            });

            if (opt == '') {
              // let numbers = Object.keys(rowData[0].data).map(function(k) {
              //   return rowData[0].data[k].adv_or_tech_name;
              // });

              // var filteredAdv = numbers.filter(function(obj) {
              //   return obj != 'Total Shop' && obj != 'Total Selected';
              // });

              let selectedIndex;
              rowData.forEach((originalItem, index) => {
                if (isFrom == 'KPIReportTechComparative') {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.adv_or_tech_name === 'Total Selected'
                  );
                } else {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.res_storename === 'Group Average'
                  );
                }

                if (selectedIndex !== -1) {
                  originalItem.data[selectedIndex].kpi_data = '0/0/0';
                }
              });
              setIsFromSidebar(true);
              // dispatch(setSelectedTech([]));
            }

            setRowData(rowData);
            setClickHeader(false);
            ////console.log('params.column.colId===', params.column.colId);
            // if (opt == 'show' && params.column.colId == 'kpi_type') {
            //   var columns = visibleCol;
            //   gridApi.columnApi.setColumnVisible('Total Shop--1_0', false);
            //   columns.map(function(element) {
            //     gridApi.columnApi.setColumnVisible(element + '_1', false);
            //   });
            //   columns.map(function(element) {
            //     gridApi.columnApi.setColumnVisible(element + '_2', false);
            //   });
            //   var totals = visibleTotals;
            //   totals.map(function(element) {
            //     gridApi.columnApi.setColumnVisible(element + '_1', false);
            //   });
            // }
            if (opt == 'hide') {
              var columns = visibleCol;

              columns.map(function(element) {
                gridApi.columnApi.setColumnVisible(element + '_1', false);
              });
            }
            if (opt != '' && arr.length == 0) {
              setIsFromSidebar(false);
              dispatch(setSelectedAdvisors([]));
            }

            if (
              // !isFromSidebar &&
              arr.length == 0 &&
              advCount == visibleCol.length
            ) {
              //console.log('ccc==222');
              setVisibleCol([]);
            }
            //console.log('selStores===', selStores);
            if (selStores.length == 0) {
              let storeDataArr = Object.keys(data[0].data).map(function(k) {
                return data[0].data[k].store_id;
              });

              setSelStores(storeDataArr);
            }
            params.api.refreshHeader();
            params.api.redrawRows();

            params.api.hideOverlay();
            $('input.ag-checkbox-input').removeAttr('disabled');
            $('#reset-layout-kpi').removeAttr('disabled');
            // if (data[0].data != null) {
            //   setRowData(data);
            // } else {
            //   if (Object.keys(gridApi).length != 0) {
            //     let colDefs = gridApi.api.columnController.columnDefs;
            //     colDefs = colDefs.filter(e => e.field == 'kpi_type');
            //     gridApi.api.setColumnDefs(colDefs);
            //   }
            // }
          }
        }
      );
    } else {
      let arrstore;
      // if (JSON.parse(permittedStores).length != arr.length) {
      if (rowData.length > 0) {
        let numbers = Object.keys(rowData[0].data).map(function(k) {
          return rowData[0].data[k].store_id;
        });
        var storeArr = session.selectedStores;

        if (parent != 'savedReports') {
          if (!isFromSidebar) {
            arrstore = numbers.filter(function(obj) {
              return (
                obj != null &&
                !storeArr.includes(obj) &&
                obj != 'Group Average' &&
                obj != 'Group Roll-Up' &&
                obj != 0
              );
            });
          } else {
            arrstore = numbers.filter(function(obj) {
              return (
                obj != null &&
                storeArr.includes(obj) &&
                obj != 'Group Average' &&
                obj != 'Group Roll-Up' &&
                obj != 0
              );
            });
          }
        } else {
          selIds = selIds.filter(function(obj) {
            return obj != 'Group Average' && obj != 'Group Roll-Up' && obj != 0;
          });
        }
      }
      if (selIds.length >= 0) {
        selIds = selIds.filter(function(obj) {
          return obj != 'Group Average' && obj != 'Group Roll-Up' && obj != 0;
        });
      }

      //console.log('selIds===', selIds, storeArr, arrstore);

      params.api.showLoadingOverlay();
      // getKpiComparativeStoreReport(
      //   filterStart,
      //   filterEnd,
      //   1,
      //   2,
      //   null,
      //   'KPI_STORE_COMP',
      //   getTimeZone(),
      //   '1',
      //   '',
      //   'C',
      //   'Customer',
      //   selIds.length > 0
      //     ? selIds.join(',')
      //     : toggleChanged && selIds.length > 0
      //     ? selIds.join(',')
      //     : storeArr.length <= 0 && arr.length <= 0
      //     ? JSON.parse(permittedStores).join(',')
      //     : typeof arrstore == 'undefined'
      //     ? 'NA'
      //     : typeof arrstore != 'undefined' && arrstore.length <= 0
      //     ? 'NA'
      //     : arrstore.join(','),

      //   'Y',
      //   null,
      //   result => {
      //     if (
      //       result.data.statelessDbdKpiScorecardGetKpiScorecardDetailsStorewise
      //         .kpiScorecardDetailsStorewises
      //     ) {
      //       setIsLoading(true);
      //       var data = JSON.parse(
      //         result.data
      //           .statelessDbdKpiScorecardGetKpiScorecardDetailsStorewise
      //           .kpiScorecardDetailsStorewises[0].jsonData
      //       );

      //       rowData.forEach((originalItem, index) => {
      //         const replacementItem = data.find(
      //           replacement => replacement.kpi_slno === originalItem.kpi_slno
      //         );

      //         if (replacementItem) {
      //           const selectedIndex = originalItem.data.findIndex(
      //             data => data.res_storename === 'Group Average'
      //           );

      //           if (selectedIndex !== -1) {
      //             originalItem.data[selectedIndex] =
      //               replacementItem.data[selectedIndex];
      //           }
      //         }
      //       });
      //       if (opt == '') {
      //         // let numbers = Object.keys(rowData[0].data).map(function(k) {
      //         //   return rowData[0].data[k].adv_or_tech_name;
      //         // });

      //         // var filteredAdv = numbers.filter(function(obj) {
      //         //   return obj != 'Total Shop' && obj != 'Total Selected';
      //         // });

      //         let selectedIndex;
      //         rowData.forEach((originalItem, index) => {
      //           if (isFrom == 'KPIReportTechComparative') {
      //             selectedIndex = originalItem.data.findIndex(
      //               data => data.adv_or_tech_name === 'Total Selected'
      //             );
      //           } else {
      //             selectedIndex = originalItem.data.findIndex(
      //               data => data.res_storename === 'Group Average'
      //             );
      //           }

      //           if (selectedIndex !== -1) {
      //             originalItem.data[selectedIndex].kpi_data = '0/0/0';
      //           }
      //         });
      //         setIsFromSidebar(true);
      //         // dispatch(setSelectedTech([]));
      //       }

      //       setRowData(rowData);
      //       setClickHeader(false);
      //       if (opt == 'hide') {
      //         var columns = visibleCol;

      //         columns.map(function(element) {
      //           gridApi.columnApi.setColumnVisible(element + '_1', false);
      //         });
      //       }

      //       if (opt != '' && arr.length == 0) {
      //         setIsFromSidebar(false);
      //         dispatch(setSelectedStores([]));
      //         if (parent == 'savedReports') {
      //           setVisibleCol([]);
      //         }
      //         //
      //       }

      //       // if (
      //       //   arr.length == 0 &&
      //       //   JSON.parse(permittedStores).length == visibleCol.length
      //       // ) {
      //       //   //console.log('000ppp');
      //       //   setVisibleCol([]);
      //       // }
      //       params.api.refreshHeader();
      //       params.api.redrawRows();

      //       if (params.visible == false && params.source != 'columnMenu') {
      //         params.api.hideOverlay();
      //         $('input.ag-checkbox-input').removeAttr('disabled');
      //         $('#reset-layout-kpi').removeAttr('disabled');
      //       } else if (
      //         opt == '' &&
      //         params.visible == false &&
      //         params.source == 'columnMenu'
      //       ) {
      //         params.api.hideOverlay();
      //         $('input.ag-checkbox-input').removeAttr('disabled');
      //         $('#reset-layout-kpi').removeAttr('disabled');
      //       } else if (
      //         opt == 'show' &&
      //         params.visible == true &&
      //         params.source == 'toolPanelUi'
      //       ) {
      //         params.api.hideOverlay();
      //         $('input.ag-checkbox-input').removeAttr('disabled');
      //         $('#reset-layout-kpi').removeAttr('disabled');
      //       }
      //     }
      //   }
      // );
      // } else {
      //   params.api.showLoadingOverlay();
      //   let selectedIndex;
      //   rowData.forEach((originalItem, index) => {
      //     if (isFrom == 'KPIReportTechComparative') {
      //       selectedIndex = originalItem.data.findIndex(
      //         data => data.adv_or_tech_name === 'Total Selected'
      //       );
      //     } else {
      //       selectedIndex = originalItem.data.findIndex(
      //         data => data.res_storename === 'Group Average'
      //       );
      //     }

      //     if (selectedIndex !== -1) {
      //       originalItem.data[selectedIndex].kpi_data = '0/0/0';
      //     }
      //   });

      //   setRowData(rowData);
      //   params.api.refreshHeader();
      //   params.api.redrawRows();
      //   params.api.hideOverlay();
      //   $('input.ag-checkbox-input').removeAttr('disabled');
      // }
    }
  };
  const convertImageToBase64 = (imgSrc, callback) => {
    const img = new Image();
    img.src = imgSrc;
    img.crossOrigin = 'Anonymous';

    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      const dataURL = canvas.toDataURL('image/png');

      callback(dataURL); // Pass the result using a callback
    };
  };
  const formatCellValue = (val, opt, slno, dec) => {
    var formattedValue =
      val == 0 || val == null
        ? '0'
        : dec == 1
        ? parseInt(val)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        : parseFloat(val)
            .toFixed(1)
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    if (
      ((slno == 1 ||
        slno == 2 ||
        slno == 12 ||
        slno == 13 ||
        slno == 32 ||
        slno == 35 ||
        slno == 31 ||
        slno == 34 ||
        slno == 33 ||
        slno == 37) &&
        opt == 2) ||
      (slno == 8 && opt == 1) ||
      ((slno == 21 || slno == 22 || slno == 26 || slno == 27) && opt == 1) ||
      slno == 19
    ) {
      formattedValue = formattedValue + '%';
    } else if (
      slno == 1 ||
      slno == 2 ||
      slno == 3 ||
      slno == 4 ||
      slno == 5 ||
      slno == 6 ||
      slno == 18 ||
      slno == 23 ||
      slno == 24 ||
      slno == 28 ||
      slno == 29 ||
      slno == 32 ||
      slno == 35 ||
      ((slno == 14 || slno == 15 || slno == 16 || slno == 17) &&
        (opt == 1 || opt == 2))
    ) {
      if (Number(formattedValue.replace(/,/g, '')) < 0) {
        formattedValue = '-$' + Math.abs(formattedValue);
      } else {
        formattedValue = '$' + formattedValue;
      }
    } else {
      formattedValue = formattedValue;
    }

    return formattedValue;
  };
  const onRowClicked = params => {
    // Check if the clicked row is a group row
    if (params.node.group) {
      // Toggle the expansion state of the group row
      if (params.node.expanded) {
        params.node.setExpanded(false);
      } else {
        params.node.setExpanded(true);
      }
    }
  };
  const customSortStorewise = (a, b) => {
    const indexA = sortOrderArr.indexOf(a.res_storename);
    const indexB = sortOrderArr.indexOf(b.res_storename);
    return indexA - indexB;
  };
  const customSort = (a, b) => {
    const indexA = sortOrderArr.indexOf(a.adv_or_tech_name);
    const indexB = sortOrderArr.indexOf(b.adv_or_tech_name);
    return indexA - indexB;
  };

  useEffect(() => {
    if (isFrom == 'KPIReportTechComparative') {
      rowData.forEach(kpi => {
        kpi.data.sort(customSort);
      });
    } else {
      rowData.forEach(kpi => {
        kpi.data.sort(customSortStorewise);
      });
    }

    if (Object.keys(gridApi).length != 0) {
      let colDefs = gridApi.api.columnController.columnDefs;

      colDefs = colDefs.filter(e => e.field == 'kpi_type');

      gridApi.api.setColumnDefs(colDefs);
      if (isFrom == 'KPIReportTechComparative') {
        dynamicallyConfigureColumnsFromObject(gridApi, rowData, 'desc');
        var goalData = [];
        Object.keys(rowData[0].data).map(function(k) {
          var data;
          if (!hideGoals.includes(rowData[0].data[k].adv_or_tech_name)) {
            data = rowData[0].data[k].adv_or_tech_name;
            goalData.push(data);
          }
        });
        goalData.map(function(element) {
          gridApi.columnApi.setColumnVisible(element + '_2', true);
        });
      } else {
        dynamicallyConfigureColumnsFromObjectStoreWise(
          gridApi,
          rowData,
          'desc'
        );
        var goalData = [];
        Object.keys(rowData[0].data).map(function(k) {
          var data;
          if (!hideGoals.includes(rowData[0].data[k].adv_or_tech_name)) {
            data = rowData[0].data[k].res_storename;
            goalData.push(data);
          }
        });
        goalData.map(function(element) {
          gridApi.columnApi.setColumnVisible(element + '_2', true);
        });
      }
      // let numbers = Object.keys(rowData[0].data).map(function(k) {
      //   return rowData[0].data[k].adv_or_tech_name;
      // });
      // numbers.map(function(element) {
      //   gridApi.columnApi.setColumnVisible(element + '_2', true);sort
      // });
      //$('#ag-input-id-15').attr('checked', true);
      var columns = visibleCol;

      columns.map(function(element) {
        gridApi.columnApi.setColumnVisible(element + '_1', false);
      });
      var totals = visibleTotals;
      //console.log('totals===', columns, totals);
      totals.map(function(element) {
        gridApi.columnApi.setColumnVisible(element + '_1', false);
      });
      let numbers;
      if (isFrom == 'KPIReportTechComparative') {
        numbers = Object.keys(rowData[0].data).map(function(k) {
          return rowData[0].data[k].adv_or_tech_name;
        });
      } else {
        numbers = Object.keys(rowData[0].data).map(function(k) {
          return rowData[0].data[k].res_storename;
        });
      }
      setKpiSortedArray(numbers);

      setRowData(rowData);
      $('input.ag-checkbox-input').removeAttr('disabled');
      $('#reset-layout-kpi').removeAttr('disabled');
      gridApi.api.redrawRows();
    }
  }, [sortOrderArr]);

  const callClickEventOfFirstRow = params => {
    if (params && rowData.length > 0) {
      const firstRowNode = params.api.getRowNode(0);

      if (session.sortOrder.row == '') {
        if (firstRowNode) {
          setArrowClicked(false);
          // Trigger the click event for the first row
          onArrowClicked(
            params,
            firstRowNode.data.kpi_name,
            firstRowNode.data.kpi_slno,
            0,
            'kpi'
          );

          var labelLen = {
            row: firstRowNode.data.kpi_name,
            order: 0,
            slno: 1
          };
          dispatch(setRowSortOrder(labelLen));
        }
      }
    }
  };
  const onArrowClicked = (params, kpi_name, kpi_slno, sortValue, isFromKPI) => {
    // Check if the clicked row is a group row

    var jsonData = rowData;
    // setIsSortApplied(true);
    const sortedData = lodash.map(jsonData, item => {
      if (kpi_name == item.kpi_name) {
        return lodash.assign({}, item, {
          data: lodash
            .sortBy(
              item.data,
              data =>
                data.kpi_data != null &&
                parseFloat(data.kpi_data.split('/')[sortValue])
            )
            .reverse()
        });
      } else {
        return lodash.assign({}, item, {
          data: item.data
        });
      }
    });

    var index = sortedData.findIndex(
      x => x.kpi_name === kpi_name && x.kpi_slno === kpi_slno
    );

    let numbers = [];
    if (isFrom == 'KPIReportTechComparative') {
      numbers = Object.keys(sortedData[0].data).map(function(k) {
        return sortedData[index].data[k].adv_or_tech_name;
      });
      if (
        numbers.indexOf('Total Shop') > 0 ||
        numbers.indexOf('Total Selected') > 0
      ) {
        numbers.splice(numbers.indexOf('Total Selected'), 1);
        numbers.unshift('Total Selected');
        numbers.splice(numbers.indexOf('Total Shop'), 1);
        numbers.unshift('Total Shop');
      }
    } else {
      numbers = Object.keys(sortedData[0].data).map(function(k) {
        return sortedData[index].data[k].res_storename;
      });

      if (
        numbers.indexOf('Group Average') > 0 ||
        numbers.indexOf('Group Roll-Up') > 0
      ) {
        numbers.splice(numbers.indexOf('Group Roll-Up'), 1);
        numbers.unshift('Group Roll-Up');
        numbers.splice(numbers.indexOf('Group Average'), 1);
        numbers.unshift('Group Average');
      }
    }
    if (isFromKPI != 'kpi') {
      setArrowClickedFrom('page');
    }

    setSortOrder(numbers);
  };
  // let gridOptions = {
  //   defaultColDef: {
  //     sortable: true,
  //     filter: 'agTextColumnFilter',
  //     resizable: true
  //   },

  //   columnDefs: columnDefs,
  //   enableSorting: true,
  //   enableFilter: true,
  //   pagination: true,

  //   getRowId: params => {
  //     return params;
  //   },
  //   rowSelection: 'multiple', // ADDED
  //   isExternalFilterPresent: isExternalFilterPresent, // ADDED
  //   doesExternalFilterPass: doesExternalFilterPass, // ADDED
  //   onSelectionChanged: onSelectionChanged // ADDED
  // };
  function isExternalFilterPresent(params) {
    // start filtering if selected rows have length

    return gridOptions.api.getSelectedRows().length > 0;
  }
  //ADDED
  const doesExternalFilterPass = useCallback(
    node => {
      // //console.log('node===', !node.selected);
      return !node.selected;
    },
    [hiddenRowId]
  );

  //ADDED- NOT BEING USED THOUGH
  function onSelectionChanged() {
    var selectedRows = gridOptions.api.getSelectedRows();
  }

  function hideRow(params, val, type) {
    var kpi = rowData.findIndex(function(item) {
      return item.kpi_type == type && item.kpi_no == val;
    });

    var selRows = gridOptions.api.getSelectedRows();

    var arr = selRows.filter(function(item) {
      return item.kpi_type == type && item.kpi_no == val;
    });

    let rowNode = gridOptions.api.getRowNode(kpi);

    if (arr.length == 0) {
      rowNode.setSelected(true);
    } else {
      rowNode.setSelected(false);
    }
    if (kpi == 11 || kpi == 21 || kpi == 32) {
      let rowNode = gridOptions.api.getRowNode(kpi + 1);

      if (arr.length == 0) {
        rowNode.setSelected(true);
      } else {
        rowNode.setSelected(false);
      }
    }
    var rowValue = gridOptions.getRowId(params);

    let groupRow = gridOptions.api.getDisplayedRowAtIndex(rowValue.rowIndex);
    // //console.log('groupRow', groupRow);
    gridOptions.api.redrawRows({ rowNodes: [groupRow] });
    gridOptions.api.onFilterChanged();
  }

  const onGridReady = params => {
    // if (!toggleChanged) {
    params.api.closeToolPanel();
    // }
    var gridConfig = gridOptions;
    gridConfig.api = params.api;
    setGridOptions(gridConfig);
    var data = rowData;

    setGridApi(params);
    if (data.length > 0) {
      if (isFrom == 'KPIReportTechComparative') {
        dynamicallyConfigureColumnsFromObject(params, data, 'asc');
      } else {
        dynamicallyConfigureColumnsFromObjectStoreWise(params, data, 'asc');
      }

      // const numbers = Object.keys(data[0].data).map(function(k) {
      //   return data[0].data[k].adv_or_tech_name;
      // });
      // var arr = numbers.filter(function(obj) {
      //   return obj != 'Total Shop' && obj != 'Total Selected';
      // });

      // setSelAdvisors(arr);
      //

      setRowData(data);

      gridRef &&
        gridRef.current &&
        gridRef.current.api &&
        gridRef.current.api.forEachNode(node => {
          if (node.group && expandedKPIs.includes(node.key)) {
            // node.setExpanded(false);
            //  gridApi.api.redrawRows();
            gridRef.current.api.setRowNodeExpanded(node, false);
          }

          // if (node.group)
          //     node.setExpanded(value);
        });
      if (!toggleChanged) {
        if (session.hiddenKpis.length > 0) {
          session.hiddenKpis.map(kpis => {
            hideRow(gridApi, kpis.kpi, kpis.type);
          });
        }
      }

      if (session.sortOrder.row != '') {
        onArrowClicked(
          gridApi,
          session.sortOrder.row,
          session.sortOrder.slno,
          session.sortOrder.order,
          'kpi'
        );
      } else {
        callClickEventOfFirstRow(params);
      }

      checkSelectAllPivotOption(params.columnApi);
    } else {
      params.api.setColumnDefs([]);
    }
    //params.columnApi.setColumnVisible('kpi_type', true);
    // var columnToolPanel = params.api.getToolPanelInstance('columns');
    // columnToolPanel.expandColumnGroups();
  };
  function onPivotChanged(params) {
    checkSelectAllPivotOption(params.columnApi);
  }

  function checkSelectAllPivotOption(columnApi) {
    const allColumns = columnApi.getAllColumns();
    const pivotColumns = allColumns.filter(col => col.getColDef().pivot);
    const hiddenColumns = allColumns.filter(col => col.getColDef().hide);

    // Check if all pivot columns are selected
    const allPivotSelected = pivotColumns.every(col => !col.getColDef().hide);

    // Set select all option checked status
    setSelectAllPivotChecked(allPivotSelected);
  }

  function setSelectAllPivotChecked(checked) {
    const selectAllCheckbox = document.querySelector(
      '.ag-column-select-all .ag-input-field-input'
    );
    if (selectAllCheckbox) {
      selectAllCheckbox.checked = checked;
    }
  }
  function dynamicallyConfigureColumnsFromObjectStoreWise(
    params,
    anObject,
    order
  ) {
    let colDefs = params.api.columnController.columnDefs;

    //const keys = Object.keys(anObject);
    colDefs = colDefs.filter(e => e.field == 'kpi_type');

    params.api.setColumnDefs(colDefs);
    var obj;
    for (var row = 0; row < anObject.length; row++) {
      // Find the index of an object with a specific property value
      // Check if the object with the specified property value exists in the array

      if (anObject[row].data != null) {
        if (order == 'asc') {
          var dataArr = anObject[row].data;
        } else {
          var dataArr = anObject[row].data;
        }
        dataArr.forEach((key, i) => {
          let index = colDefs.findIndex(
            item => item.headerName == key.res_storename
          );
          if (index === -1) {
            obj = {
              headerName: key.res_storename,
              headerClass: 'kpi-group-header',
              groupId: key.res_storename,
              suppressMovable: true,
              headerGroupComponent: 'kpiColumnRenderer',
              minWidth: 240,
              children: [
                {
                  field: key.res_storename,
                  suppressMenu: true,
                  suppressMovable: true,
                  headerClass: 'kpi-hide-header',
                  cellRenderer: 'tableCellRenderer',

                  minWidth: 240,
                  resizable: false,
                  cellStyle() {
                    return { textAlign: 'left' };
                  },
                  valueGetter: params => {
                    return i;
                  },
                  onCellClicked: params => {
                    // Disable export when this cell is clicked
                    // disableExport();
                  }
                }
              ]
            };

            colDefs.push(obj);
          }
        });
      }
    }
    const firstItemIds = ['Group Roll-Up', 'Group Average'];
    colDefs.sort((a, b) => {
      const indexA = firstItemIds.indexOf(a.headerName);
      const indexB = firstItemIds.indexOf(b.headerName);

      // If both items are in the list, compare their order
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }

      // If only one of the items is in the list, prioritize it
      if (indexA !== -1) {
        return -1;
      }

      if (indexB !== -1) {
        return 1;
      }

      // If neither item is in the list, maintain the original order
      return 0;
    });

    params.api.setColumnDefs(colDefs);
  }
  function dynamicallyConfigureColumnsFromObject(params, anObject, order) {
    let colDefs = params.api.columnController.columnDefs;

    //const keys = Object.keys(anObject);
    //const keys = Object.keys(anObject);
    colDefs = colDefs.filter(e => e.field == 'kpi_type');

    params && params.api && params.api.setColumnDefs(colDefs);
    var obj;
    for (var row = 0; row < anObject.length; row++) {
      // Find the index of an object with a specific property value
      // Check if the object with the specified property value exists in the array

      if (anObject[row].data != null) {
        if (order == 'asc') {
          var dataArr = anObject[row].data.reverse();
        } else {
          var dataArr = anObject[row].data;
        }
        dataArr.forEach((key, i) => {
          let index = colDefs.findIndex(
            item => item.headerName == key.adv_or_tech_name
          );

          if (index === -1) {
            obj = {
              headerName: key.adv_or_tech_name,
              headerClass: 'kpi-group-header',
              groupId: key.adv_or_tech_name,
              suppressMovable: true,
              headerGroupComponent: 'kpiColumnRenderer',
              minWidth:
                typeof params.columnGroup == 'undefined'
                  ? 240
                  : key.adv_or_tech_name != params.columnGroup.groupId &&
                    hideGoals.includes(params.columnGroup.groupId)
                  ? 260
                  : key.adv_or_tech_name == params.columnGroup.groupId &&
                    !hideGoals.includes(params.columnGroup.groupId)
                  ? 240
                  : 200,
              children: [
                {
                  field: key.adv_or_tech_name,
                  suppressMenu: true,
                  headerClass: 'kpi-hide-header',
                  cellRenderer: 'tableCellRenderer',
                  suppressMovable: true,
                  //cellClass: cellClass,
                  minWidth:
                    typeof params.columnGroup == 'undefined'
                      ? 240
                      : key.adv_or_tech_name != params.columnGroup.groupId &&
                        hideGoals.includes(params.columnGroup.groupId)
                      ? 260
                      : !hideGoals.includes(params.columnGroup.groupId)
                      ? 240
                      : 200,
                  width: 190,
                  resizable: false,
                  cellStyle() {
                    return { textAlign: 'left' };
                  },
                  valueGetter: params => {
                    return i;
                  },
                  onCellClicked: params => {
                    // Disable export when this cell is clicked
                    // disableExport();
                  }
                }
              ]
            };
            colDefs.push(obj);
            //   if (i != 1) {
            //     const newObject = {
            //       headerName: 'Goal / Var',
            //       headerClass: 'kpi-group-header-goal',
            //       groupId: key.adv_or_tech_name + '-' + index,
            //       headerGroupComponent: 'kpiColumnRenderer',
            //       pivot: false,
            //       hide: true,
            //       suppressColumnsToolPanel: true,
            //       width: 120,
            //       width: 90,
            //       left: 185,
            //       cellStyle() {
            //         return { textAlign: 'left', paddingLeft: 0 };
            //       },
            //       children: [
            //         {
            //           field: key.adv_or_tech_name,
            //           width: 120,
            //           width: 90,
            //           left: 185,
            //           suppressMenu: true,
            //           cellRenderer: 'customHeaderGroup',
            //           pivot: false,
            //           hide: true,
            //           cellStyle() {
            //             return { textAlign: 'left', paddingLeft: 0 };
            //           },
            //           valueGetter: params => {
            //             return i;
            //           }
            //         }
            //       ]
            //     };
            //     colDefs.push(newObject);
            //   }
          }
        });
      }
    }
    params.api.setColumnDefs(colDefs);
  }
  const getRowStyle = params => {
    if (params.node.allChildrenCount != null) {
      return {
        background: '#ffc000 !important',
        border: '1px solid',
        marginTop: '3px',
        height: '23px !important'
      };
    }

    if (typeof params.node.data != 'undefined') {
      if (
        params.node.data.kpi_slno == 17 ||
        params.node.data.kpi_slno == 23 ||
        params.node.data.kpi_slno == 28
      ) {
        return {
          height: '30px !important'
        };
      } else {
        return {
          height: '27px !important'
        };
      }
    }
    // if (typeof params.data != 'undefined' && hiddenRowId.length > 0) {
    //   if (
    //     params.data.kpi_type == hiddenRowId[0].type &&
    //     params.data.kpi_no == hiddenRowId[0].kpi
    //   ) {
    //     return { display: 'none' };
    //   }
    // }
  };
  function getData() {
    return rowData;
  }
  const cellClassRules = params => {
    ////console.log('params.node.group', params.node, params.node.group);
    // Replace 'groupClass' with theparams.node actual CSS class name you want to apply
    return { 'custom-cell': params => params.node.allChildrenCount > 0 }; // Apply the class to row group cells
  };
  const rowCellClass = params => {
    if (typeof params.data != 'undefined' && params.data.kpi_no == '') {
      return 'custom-cell-no-display';
    }
  };
  const cellClass = params => {
    if (typeof params.data != 'undefined') {
      if (params.node.allChildrenCount === null) {
        if (
          typeof params.value == 'string' &&
          typeof params.data != 'undefined' &&
          (params.data.kpi_slno == '17' ||
            params.data.kpi_slno == '23' ||
            params.data.kpi_slno == '28')
        ) {
          return 'custom-cell cell-no-border-bottom';
        } else if (
          typeof params.value == 'string' &&
          typeof params.data != 'undefined' &&
          (params.data.kpi_slno == '18' ||
            params.data.kpi_slno == '24' ||
            params.data.kpi_slno == '29')
        ) {
          return 'custom-cell cell-no-border-top';
        } else {
          if (typeof params.value == 'string') {
            return 'custom-cell';
          }
        }
      } else {
        return 'custom-header';
      }
    } else {
      return 'custom-no-data-row';
    }
  };
  useEffect(() => {
    dispatch(setSelAdv(selAdvisors));
  }, [selAdvisors]);
  useEffect(() => {
    dispatch(setSelStoreIds(selStores));
  }, [selStores]);
  const onCheck = params => {
    var arr = visibleCol;
    var clickFrom = arrowClickedFrom;
    if (params.column != null && params.column.colId != 'ag-Grid-AutoColumn') {
      setSidebarClicked(true);
    }

    $('input.ag-checkbox-input').attr('disabled', true);
    $('#reset-layout-kpi').attr('disabled', true);
    if (params.column == null) {
      gridApi.columnApi.setColumnVisible('kpi_type', false);
    }

    if (
      typeof params.column != undefined &&
      params.column != null &&
      params.column.colId != 'kpi_type' &&
      params.column.parent.groupId != 'Total Shop' &&
      params.column.parent.groupId != 'Total Selected' &&
      params.column.parent.groupId != 'Group Roll-Up' &&
      params.column.parent.groupId != 'Group Average' &&
      params.column.colId.split('_')[1] == 1
    ) {
      if (params.visible == true) {
        if (isFrom == 'KPIReportTechComparative') {
          arr = arr.filter(function(item) {
            return item !== params.column.parent.groupId;
          });

          var advId = params.column.parent.groupId.replace(
            /(^.*\[|\].*$)/g,
            ''
          );

          var advArr = session.selectedAdvisors;

          if (!advArr.includes(advId)) {
            //checking weather array contain the id
            //adding to array because value doesnt existsz

            advArr.push(advId);
          } else {
            advArr.splice(advArr.indexOf(advId), 1); //deleting
          }
          var visAdvisors = selAdvisors;
          if (!visAdvisors.includes(advId)) {
            //checking weather array contain the id
            //adding to array because value doesnt existsz

            visAdvisors.push(advId);
          }

          setSelAdvisors(visAdvisors);

          setArrowClickedFrom('header');

          setVisibleCol(arr);

          dispatch(setSelectedAdvisors(advArr));

          handleRecalc(params, arr, 'show', visAdvisors);

          setArrowClicked(true);
          params.api.refreshHeader();
        } else {
          arr = arr.filter(function(item) {
            return item !== params.column.parent.groupId;
          });

          let colData = rowData[0].data;

          colData = colData.filter(
            e => e.res_storename == params.column.parent.groupId
          );

          var storeid = colData[0].store_id;

          var advArr = session.selectedStores;

          if (!advArr.includes(storeid)) {
            //checking weather array contain the id
            //adding to array because value doesnt existsz

            advArr.push(storeid);
          } else {
            advArr.splice(advArr.indexOf(storeid), 1); //deleting
          }

          var visStores = selStores;
          if (!visStores.includes(storeid)) {
            //checking weather array contain the id
            //adding to array because value doesnt existsz

            visStores.push(storeid);
          }

          setSelStores(visStores);

          setVisibleCol(arr);
          setArrowClickedFrom('header');
          dispatch(setSelectedStores(advArr));

          handleRecalc(params, arr, 'show', visStores);

          setArrowClicked(false);
          params.api.refreshHeader();
        }
      } else {
        if (isFrom == 'KPIReportTechComparative') {
          var advId = params.column.parent.groupId.replace(
            /(^.*\[|\].*$)/g,
            ''
          );
          var advArr = session.selectedAdvisors;

          if (!arr.includes(params.column.parent.groupId)) {
            //checking weather array contain the id
            //adding to array because value doesnt exists

            arr.push(params.column.parent.groupId);
          }
          var adVar = [];
          //setIsFromSidebar(false);

          Object.keys(rowData[0].data).map(function(k) {
            if (!arr.includes(rowData[0].data[k].adv_or_tech_name)) {
              var id = rowData[0].data[k].adv_or_tech_name.replace(
                /(^.*\[|\].*$)/g,
                ''
              );
              //  if (id != 'Total Shop' && id != 'Total Selected') {
              adVar.push(id);
              // }
            }
          });
          adVar = adVar.filter(function(obj) {
            return obj != null;
          });

          adVar = adVar.filter(function(val) {
            return visibleTotals.indexOf(val) == -1;
          });
          setSelAdvisors(adVar);

          setVisibleCol(arr);
          dispatch(setHiddenAdvisors(arr));
          // //console.log('params.source', params.source);
          if (
            params.source == 'toolPanelUi' ||
            (params.source == 'api' && arrowClickedFrom != 'page')
          ) {
            if (!advArr.includes(advId)) {
              //checking weather array contain the id
              //adding to array because value doesnt exists

              advArr.push(advId);
            } else {
              if (isFromSidebar) {
                advArr.splice(advArr.indexOf(advId), 1); //deleting
              }
            }

            if (parent == 'savedReports') {
              var selAdvArr = selAdvisors;
              if (selAdvArr.includes(advId)) {
                selAdvArr.splice(selAdvArr.indexOf(advId), 1); //deleting
              }
              handleRecalc(params, arr, 'hide', selAdvArr);
            } else {
              handleRecalc(params, arr, 'hide', adVar);
            }
            dispatch(setSelectedAdvisors(advArr));

            setArrowClickedFrom('header');
          } else if (params.source == 'api' && arrowClickedFrom == 'page') {
            $('input.ag-checkbox-input').removeAttr('disabled');
            $('#reset-layout-kpi').removeAttr('disabled');
          }
          clickFrom = 'header';
          setArrowClicked(false);
          params.api.refreshHeader();
        } else {
          let colData = rowData[0].data;
          clickFrom = 'header';
          colData = colData.filter(
            e => e.res_storename == params.column.parent.groupId
          );

          var storeid = colData[0].store_id;

          var advArr = session.selectedStores;

          if (!arr.includes(params.column.parent.groupId)) {
            //checking weather array contain the id
            arr.push(params.column.parent.groupId); //adding to array because value doesnt exists
          }

          setVisibleCol(arr);
          if (!advArr.includes(storeid)) {
            //checking weather array contain the id
            //adding to array because value doesnt exists

            advArr.push(storeid);
          } else {
            if (isFromSidebar) {
              advArr.splice(advArr.indexOf(storeid), 1); //deleting
            }
          }

          var storeVar = [];
          //setIsFromSidebar(false);

          Object.keys(rowData[0].data).map(function(k) {
            if (!arr.includes(rowData[0].data[k].res_storename)) {
              var id = rowData[0].data[k].store_id;

              if (id != storeid) {
                storeVar.push(id);
              }
            }
          });

          storeVar = storeVar.filter(function(obj) {
            return obj != null;
          });
          if (parent == 'savedReports') {
            storeVar = selStores.filter(function(obj) {
              return obj != storeid;
            });
          }
          storeVar = storeVar.filter(function(val) {
            return visibleTotals.indexOf(val) == -1;
          });
          // if (adVar.includes(advId)) {
          //   //checking weather array contain the id
          //   //adding to array because value doesnt exists

          //   adVar.splice(adVar.indexOf(advId), 1); //deleting
          // }
          //dispatch(setSelAdv(adVar));

          setSelStores(storeVar);
          dispatch(setSelectedStores(advArr));
          if (
            params.source == 'toolPanelUi' ||
            (params.source == 'api' && arrowClickedFrom != 'page')
          ) {
            setArrowClickedFrom('header');
            handleRecalc(params, arr, 'hide', storeVar);
          } else if (params.source == 'api' && arrowClickedFrom == 'page') {
            $('input.ag-checkbox-input').removeAttr('disabled');
            $('#reset-layout-kpi').removeAttr('disabled');
          }
          setArrowClicked(false);
          params.api.refreshHeader();
        }
      }
    } else {
      params.columnApi.setColumnVisible('kpi_type', false);

      // let numbers = Object.keys(rowData[0].data).map(function(k) {
      //   return rowData[0].data[k].adv_or_tech_name;
      // });
      // numbers.map(function(element) {
      //   gridApi.columnApi.setColumnVisible(element + '_2', false);
      // });
      if (params.column == null) {
        if (isFrom == 'KPIReportTechComparative') {
          let numbers = Object.keys(rowData[0].data).map(function(k) {
            return rowData[0].data[k].adv_or_tech_name;
          });

          var filteredAdv = numbers.filter(function(obj) {
            return obj != 'Total Shop' && obj != 'Total Selected';
          });
          setVisibleTotals(['Total Shop', 'Total Selected']);
        } else {
          let numbers = Object.keys(rowData[0].data).map(function(k) {
            return rowData[0].data[k].res_storename;
          });

          var filteredAdv = numbers.filter(function(obj) {
            return obj != 'Group Roll-Up' && obj != 'Group Average';
          });
          setVisibleTotals(['Group Roll-Up', 'Group Average']);
        }

        $('input.ag-checkbox-input').attr('disabled', true);
        $('#reset-layout-kpi').attr('disabled', true);
        setVisibleCol(filteredAdv);

        //  setVisibleCol([]);

        setIsFromSidebar(true);
        params.api.showLoadingOverlay();
        let selectedIndex;
        if (isFrom == 'KPIReportTechComparative') {
          rowData.forEach((originalItem, index) => {
            selectedIndex = originalItem.data.findIndex(
              data => data.adv_or_tech_name === 'Total Selected'
            );

            if (selectedIndex !== -1) {
              originalItem.data[selectedIndex].kpi_data = '0/0/0';
            }
          });
        }
        dispatch(setSelectedStores([]));
        if (params.visible == false) {
          //console.log('log--4');
          setSelAdvisors([]);

          setSelStores([]);
        }

        setAllData(rowData);
        setRowData(rowData);
        params.api.redrawRows();

        handleRecalc(params, filteredAdv, '', []);

        // params.api.hideOverlay();
      }
      var arr = visibleTotals;
      var selAdvs = selAdvisors;
      var selStoresArr = selStores;

      // if (params.column != null && params.column.colId.split('_')[1] == 1) {
      if (params.column != null) {
        var visibleColumns = visibleCol;
        //console.log('visibleColumns===vvv', arr, visibleColumns);
        if (params.visible == true) {
          if (parent == 'savedReports') {
            arr = arr.filter(function(item) {
              return item !== params.column.parent.groupId;
            });
          } else {
            if (isFrom == 'KPIReportTechComparative') {
              arr = arr.filter(function(item) {
                return item !== params.column.parent.groupId;
              });
            } else {
              //// fix group avg issue on toggle change => === to !==
              arr = arr.filter(function(item) {
                return item !== params.column.parent.groupId;
              });
            }
          }

          visibleColumns = visibleColumns.filter(function(item) {
            return item !== params.column.parent.groupId;
          });

          // if (parent == 'savedReports') {
          //   // setVisibleCol([]);
          // } else {

          setVisibleCol(visibleColumns);
          setVisibleTotals(arr);
          // }
        } else {
          if (
            !arr.includes(params.column.parent.groupId) &&
            params.column.parent.groupId != 0
          ) {
            //checking weather array contain the id
            //adding to array because value doesnt exists

            arr.push(params.column.parent.groupId);
          }
          // console.log(
          //   'visibleTotals===',
          //   arr,
          //   params.column.parent.groupId,
          //   params.visible
          // );
        }
        if (params.column.parent.groupId == 0) {
          arr = [];
          selAdvs = [];
          selStoresArr = [];
        }
        if (params.column.parent.groupId == 0) {
          setVisibleTotals([]);
        } else {
          var dataVar = arr.filter(function(obj) {
            return (
              obj == 'Group Average' ||
              obj == 'Group Roll-Up' ||
              obj == 'Total Shop' ||
              obj == 'Total Selected'
            );
          });

          setVisibleTotals(dataVar);
        }
        if (
          parent == 'savedReports' &&
          params.column.parent.groupId.split('_')[1] != 'PATH'
        ) {
          if (isFrom == 'KPIReportTechComparative') {
            var arrAdvs = selAdvisors;

            if (selAdvisors.length != 0) {
              // if (
              //   params.column.parent.groupId != 'Total Selected' &&
              //   params.column.parent.groupId != 'Total Shop'
              // ) {
              if (
                params.visible == true &&
                !selAdvisors.includes(params.column.parent.groupId)
              ) {
                //checking weather array contain the id
                //adding to array because value doesnt exists

                arrAdvs.push(params.column.parent.groupId);
              } else if (
                params.visible == false &&
                arrAdvs.includes(params.column.parent.groupId)
              ) {
                arrAdvs.splice(
                  arrAdvs.indexOf(params.column.parent.groupId),
                  1
                ); //deleting
              }
              //console.log('ccc===aaappp', arrAdvs);
              if (params.column.parent.groupId == 0) {
                let numbers = Object.keys(rowData[0].data).map(function(k) {
                  return rowData[0].data[k].adv_or_tech_id;
                });
                setSelAdvisors(numbers);
              } else {
                setSelAdvisors(arrAdvs);
              }

              //  }
            } else {
              let numbers = Object.keys(rowData[0].data).map(function(k) {
                return rowData[0].data[k].adv_or_tech_id;
              });
              var arrAdvs;
              // if (selAdvisors.length == 0) {
              //   arrAdvs = numbers.slice();
              // } else {
              //   arrAdvs = selAdvisors;
              // }
              if (arr.length == 0 && selAdvs.length == 0) {
                arrAdvs = numbers.slice();
              } else {
                arrAdvs = selAdvs;
              }
              if (!arrAdvs.includes(params.column.parent.groupId)) {
                //checking weather array contain the id
                //adding to array because value doesnt exists

                arrAdvs.push(params.column.parent.groupId);
              } else {
                arrAdvs.splice(
                  arrAdvs.indexOf(params.column.parent.groupId),
                  1
                ); //deleting
              }

              setSelAdvisors(arrAdvs);
            }
          } else {
            var arrAdvs = selStores;

            if (selStores.length != 0) {
              if (
                params.visible == true &&
                !arrAdvs.includes(params.column.parent.groupId)
              ) {
                //checking weather array contain the id
                //adding to array because value doesnt exists

                arrAdvs.push(params.column.parent.groupId);
              } else if (
                params.visible == false &&
                arrAdvs.includes(params.column.parent.groupId)
              ) {
                arrAdvs.splice(
                  arrAdvs.indexOf(params.column.parent.groupId),
                  1
                ); //deleting
              }
              if (params.column.parent.groupId == 0) {
                let numbers = Object.keys(rowData[0].data).map(function(k) {
                  return rowData[0].data[k].store_id;
                });
                setSelStores(numbers);
              } else {
                setSelStores(arrAdvs);
              }
            } else {
              let numbers = Object.keys(rowData[0].data).map(function(k) {
                return rowData[0].data[k].store_id;
              });
              var arrAdvs;
              if (arr.length == 0 && selStoresArr.length == 0) {
                arrAdvs = numbers.slice();
              } else {
                arrAdvs = selStoresArr;
              }
              if (!arrAdvs.includes(params.column.parent.groupId)) {
                //checking weather array contain the id
                //adding to array because value doesnt exists

                arrAdvs.push(params.column.parent.groupId);
              } else {
                arrAdvs.splice(
                  arrAdvs.indexOf(params.column.parent.groupId),
                  1
                ); //deleting
              }
              //console.log('cvvvv===111', arrAdvs);
              setSelStores(arrAdvs);
            }
          }
        } else if (params.column.parent.groupId.split('_')[1] != 'PATH') {
          let numbers;
          if (isFrom == 'KPIReportTechComparative') {
            numbers = Object.keys(rowData[0].data).map(function(k) {
              return rowData[0].data[k].adv_or_tech_id;
            });
            var arrAdvs;

            if (arr.length == 0 && selAdvs.length == 0) {
              arrAdvs = numbers.slice();
            } else if (arr.length != 0 && selAdvs.length == 0) {
              arrAdvs = arr;
            } else {
              arrAdvs = selAdvs;
            }

            if (arrAdvs.length != 0) {
              if (
                !arrAdvs.includes(params.column.parent.groupId) &&
                params.column.parent.groupId != 0
              ) {
                //checking weather array contain the id
                //adding to array because value doesnt exists
                if (
                  params.source == 'toolPanelUi' ||
                  (params.source == 'api' && arrowClickedFrom != 'page')
                ) {
                  arrAdvs.push(params.column.parent.groupId);
                }
              } else if (params.visible == false) {
                arrAdvs.splice(
                  arrAdvs.indexOf(params.column.parent.groupId),
                  1
                ); //deleting
              }

              setSelAdvisors(arrAdvs);
              setAdvFilterChanged(false);
            }
          } else {
            numbers = Object.keys(rowData[0].data).map(function(k) {
              return rowData[0].data[k].store_id;
            });
            var arrAdvs;

            if (arr.length == 0 && selStoresArr.length == 0) {
              arrAdvs = numbers.slice();
            } else if (arr.length != 0 && selStoresArr.length == 0) {
              arrAdvs = arr;
            } else {
              arrAdvs = selStoresArr;
            }

            if (arrAdvs.length != 0) {
              if (
                //visibleTotals.length != 2 &&
                !arrAdvs.includes(params.column.parent.groupId)
              ) {
                //checking weather array contain the id
                //adding to array because value doesnt exists
                if (
                  params.source == 'toolPanelUi' ||
                  (params.source == 'api' && arrowClickedFrom != 'page')
                ) {
                  arrAdvs.push(params.column.parent.groupId);
                }
              } else if (params.visible == false) {
                arrAdvs.splice(
                  arrAdvs.indexOf(params.column.parent.groupId),
                  1
                ); //deleting
              }

              setSelStores(arrAdvs);
              setSidebarClicked(false);
            }
            //console.log('selStores===5', visibleTotals);
          }
        }

        if (
          isFrom == 'KPIReportTechComparative' &&
          params.column.parent.groupId == 0
        ) {
          setVisibleCol([]);
          setSidebarClicked(false);

          // dispatch(setSelectedTech([]));
        }

        if (
          isFrom != 'KPIReportTechComparative' &&
          params.column.parent.groupId != 'Group Roll-Up' &&
          params.column.parent.groupId != 'Group Average' &&
          params.column.parent.groupId.split('_')[1] == 'PATH'
        ) {
          if (parent != 'savedReports') {
            setVisibleCol(visibleCol);
            setVisibleTotals(visibleTotals);
            dispatch(setSelectedStores(selStores));
          }
        }
        if (
          isFrom != 'KPIReportTechComparative' &&
          params.column.parent.groupId != 'Group Roll-Up' &&
          params.column.parent.groupId != 'Group Average' &&
          params.column.parent.groupId == 0
        ) {
          if (parent != 'savedReports') {
            setVisibleCol([]);
            setVisibleTotals([]);
            dispatch(setSelectedStores([]));
            setSidebarClicked(false);
          }
        }

        if (
          params.column.parent.groupId != 'Total Shop' &&
          params.column.parent.groupId != 'Total Selected' &&
          params.column.parent.groupId != 'Group Roll-Up' &&
          params.column.parent.groupId != 'Group Average' &&
          (params.column.parent.groupId == 0 ||
            params.column.colId.split('_')[1] == 1)
        ) {
          handleRecalc(params, [], 'show', []);
        } else {
          $('input.ag-checkbox-input').removeAttr('disabled');
          $('#reset-layout-kpi').removeAttr('disabled');
          $('.ag-header-group-cell-label').css('pointer-events', '');
        }
        // params.api.redrawRows();
        params && params.api && params.api.refreshHeader();
      } else {
        // setVisibleCol([]);
        $('input.ag-checkbox-input').removeAttr('disabled');
        //$('#reset-layout-kpi').removeAttr('disabled');
        $('.ag-header-group-cell-label').css('pointer-events', '');
      }
    }
  };
  // useEffect(() => {
  //   //console.log('visibleCol==', visibleCol);
  // }, [session.hiddenAdvisors]);

  const handleRowShowHide = (params, val, type) => {
    let arr = {
      type: type,
      kpi: val
    };
    var rowsArr = [];
    var selrows = selectedRowIds;

    let rowNode = params.api.getDisplayedRowAtIndex(params.rowIndex); // get the clicked row
    rowsArr.push(rowNode);
    var hidRows = hiddenRowId;
    if (
      typeof hiddenRowId != 'undefined' &&
      hiddenRowId.some(function(el) {
        return el.kpi == val && el.type == type;
      })
    ) {
      hidRows = hidRows.filter(
        item => !(item.type === arr.type && item.kpi === arr.kpi)
      );
    } else {
      hidRows.push(arr);
    }
    //  //console.log('hidRows===', hidRows);
    setHiddenRowId(hidRows);
    if (selrows.includes(val)) {
      selrows.splice(selrows.indexOf(val), 1);
    } else {
      selrows.push(val);
    }

    setSelectedRowIds(selrows);

    // params.api.redrawRows({ rowNodes: rowsArr });
    hideRow(params, val, type);

    //params.api.redrawRows();
  };
  const getRowClass = params => {
    if (typeof params.data != 'undefined') {
      return 'custom-row-cells'; // Add a custom class to each row
    } else {
      return 'custom-no-data-row no-data'; // Add a custom class to each row
    }
  };
  const handleCallback = (event, picker) => {
    getFilterText(picker.chosenLabel);
    setFilterChanged(true);

    if (!sidebarClicked) {
      setSelAdvisors([]);
    }
    setFilterStart(picker.startDate.format('YYYY-MM-DD'));
    setFilterEnd(picker.endDate.format('YYYY-MM-DD'));
    setIsLoading(false);
    setToggleChanged(true);
    console.log('pickerchanged--8');
    setPickerChanged(true);

    setRowData([]);
  };
  const getFilterText = label => {
    var filterText;

    if (label.includes('Yesterday')) {
      filterText = 'YESDT';
    } else if (label.includes('Day Before Yest')) {
      filterText = 'DBYESDT';
    } else if (label.includes('Last Week')) {
      filterText = 'LWEEK';
    } else if (label.includes('This Month')) {
      filterText = 'MTD';
    } else if (label.includes('Last Month')) {
      filterText = 'LMONTH';
    } else if (label.includes('Last 3 Mths')) {
      filterText = 'PLMTHREE';
    } else if (label.includes('Last Qtr')) {
      filterText = 'LQRTR';
    } else if (label.includes('YTD')) {
      filterText = 'YTD';
    } else if (label.includes('Last 12 Mths')) {
      filterText = 'PLYONE';
    } else if (label.includes('Last Year')) {
      filterText = 'LYEAR';
    } else if (label.includes('Custom Range')) {
      filterText = 'CRANGE';
    } else if (label.includes('This Week')) {
      filterText = 'THISWEEK';
    } else if (label.includes('Last 2 Weeks')) {
      filterText = 'LTWOWEEK';
    }
    setFilterText(filterText);
    return filterText;
  };
  const setColDef = (params, val, opt) => {
    var arr = visibleCol;

    if (!arr.includes(val)) {
      //checking weather array contain the id
      arr.push(val); //adding to array because value doesnt exists
    } else {
      arr.splice(arr.indexOf(val), 1); //deleting
    }

    setVisibleCol(arr);

    //handleRecalc(params, arr, );
    // dispatch(setSelectedTech(advArr));
    dispatch(setHiddenAdvisors(arr));
  };
  const setResetDashboard = () => {
    if (parent != 'savedReports') {
      setVisibleCol([]);
      setVisibleTotals([]);
      setSelAdv([]);
      setSelStores([]);
      setKpiSortOrder({ row: '', order: 0 });
      setKpiHiddenNumbers([]);
      setExpandedKPIs(['Pricing - Customer Pay']);
      setHideGoals([]);
      setSelAdvisors([]);

      dispatch(setSelectedAdvisors([]));
      dispatch(setRowSortOrder({ row: '', order: 0 }));
      dispatch(setHiddenKpiRows([]));
    }
    console.log('pickerchanged--9');
    setPickerChanged(true);
    // dynamicallyConfigureColumnsFromObject(gridApi, roData);
  };

  const hideGoalsColumn = (params, val) => {
    var arr = hideGoals;

    if (!arr.includes(val)) {
      //checking weather array contain the id
      arr.push(val); //adding to array because value doesnt exists
    } else {
      arr.splice(arr.indexOf(val), 1); //deleting
    }

    setHideGoals(arr);
    // dynamicallyConfigureColumnsFromObject(params, rowData, 'desc');
    params.api.refreshHeader();
    params.api.redrawRows();
    //handleRecalc(params, arr, );
    // dispatch(setSelectedTech(advArr));
    //dispatch(setHiddenAdvisors(arr));
    // dynamicallyConfigureColumnsFromObject(gridApi, roData);
  };
  const handleColumnHide = () => {
    setArrowClicked(true);
    setArrowClickedFrom('header');
    // dynamicallyConfigureColumnsFromObject(gridApi, roData);
  };
  const handleSaveReport = () => {
    var advisor = session.technician;
    var iKpiReportType;
    var iKpiIds;
    if (isFrom == 'KPIReportTechComparative') {
      iKpiReportType = 'tech';
      iKpiIds = advisor[0] != 'All' ? session.technician : selAdvisors;
    }
    console.log(
      'handleSaveReport',
      iKpiIds,
      expandedKPIs,
      expandedKPIs.length,
      selAdvisors.length
    );
    if (
      JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
      parent != 'savedReports'
    ) {
      setOpenDilogAll(true);
    } else {
      if (filterText == 'CRANGE') {
        setOpenDilog(true);
      } else {
        if (
          expandedKPIs.length >= 7 ||
          iKpiIds.length == 0 ||
          selAdvisors.length == 0
        ) {
          setOpenDilogpdf(true);
        } else {
          setOpenSaveDlg(true);

          if (parent == 'savedReports') {
            setReportName(props.kpiReportData.report_name);
          }
          // else {
          //   if (reportName != '') {
          //     setReportNameCopy(reportName);
          //   }
          //   setReportName('');
          // }
        }
      }
    }
  };

  function arraysEqual(arr1, arr2) {
    if (arr1.length !== arr2.length) return false;

    // Sort both arrays
    arr1.sort();
    arr2.sort();

    // Check if every element is equal
    return arr1.every((value, index) => value === arr2[index]);
  }

  const handleExportClick = () => {
    if (!loadingIconExcel) {
      setSuccessExcel(false);
      setLoadingIconExcel(true);
    }
    // if (filterText == 'CRANGE') {
    //   setOpenDilog(true);
    // } else {
    //console.log('expandedKPIs===', expandedKPIs, selAdvisors, selStores);
    var advisor = session.technician;
    var iKpiReportType;
    var iKpiIds;
    if (isFrom == 'KPIReportTechComparative') {
      iKpiReportType = 'tech';
      iKpiIds = advisor[0] != 'All' ? session.technician : selAdvisors;
    } else {
      iKpiReportType = 'store';
      iKpiIds = selStores;
    }
    if (
      expandedKPIs.length >= 7 ||
      iKpiIds.length == 0 ||
      selAdvisors.length == 0
    ) {
      setOpenDilogpdf(true);
      setLoadingIconExcel(false);
    } else {
      var advisor = session.technician;

      var filterFromTopbar = arraysEqual(selAdvisors, advisor);
      ////console.log('advisor===advisor', selAdvisors, advisor);
      var arrAdv;
      if (selAdvisors.length > 0) {
        var arr = selAdvisors.filter(function(obj) {
          return obj != 'Total Shop' && obj != 'Total Selected';
        });
        var arrAdv = arr;
      } else {
        arrAdv = null;
      }
      if (isFrom == 'KPIReportTechComparative') {
        getKpiComparativeReport(
          filterStart,
          filterEnd,
          2,
          2,
          null,
          // advisor[0] == 'All' ? null : filterFromTopbar ? advisor : null,
          'KPI_TECH_COMP',
          getTimeZone(),
          advisor[0] == 'All' ? null : filterFromTopbar ? advisor : null,
          // null,
          '',
          'C',
          'Customer',
          'N',
          arrAdv != null && arrAdv.length > 0 && arrAdv[0] == 'All'
            ? null
            : arrAdv,
          result => {
            if (
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails
            ) {
              var data = JSON.parse(
                result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                  .kpiScorecardDetails[0].jsonData
              );
              const allowedValues = ['Total Selected', 'Total Shop'];
              const containsOtherValues = selAdvisors.some(
                value => !allowedValues.includes(value)
              );
              //
              var iKpiIdsAdv;

              if (
                containsOtherValues &&
                (visibleTotals.length == 0 || visibleTotals.length == 2)
              ) {
                iKpiIdsAdv = selAdvisors;
              } else {
                iKpiIdsAdv = selAdvisors.filter(
                  item => !visibleTotals.includes(item)
                );
              }
              let selectedIndex;
              if (!containsOtherValues) {
                data.forEach((originalItem, index) => {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.adv_or_tech_name === 'Total Selected'
                  );

                  if (selectedIndex !== -1) {
                    originalItem.data[selectedIndex].kpi_data = '0/0/0';
                  }
                });
              }

              if (data[0].data != null) {
                const kpiArray = [];

                kpiSortedArray.forEach(item => {
                  kpiArray.push(item);
                  if (!hideGoals.includes(item)) {
                    kpiArray.push(item + '-goal');
                    kpiArray.push(item + '-var');
                  }
                });

                exportToExcel(
                  gridApi,
                  filterStart,
                  filterEnd,
                  fileNameExport,
                  expandedKPIs,
                  hideGoals,
                  image,
                  data,
                  isFrom,
                  parent == 'savedreports' ? session.technician : iKpiIdsAdv,
                  parent == 'savedreports' ? session.selStores : selStores,
                  image,
                  session,
                  kpiArray,
                  storeNickName
                );
                setSuccessExcel(true);
                setLoadingIconExcel(false);
              }
            }
          }
        );
      } else {
        var arrAdv;
        if (selStores.length > 0) {
          var arr = selStores.filter(function(obj) {
            return obj != 'Group Average' && obj != 'Group Roll-Up' && obj != 0;
          });
          var arrAdv = arr;
        } else {
          arrAdv = null;
        }
      }
    }
  };
  const handleCopyReport = () => {
    var advisor = session.technician;
    var iKpiReportType;
    var iKpiIds;
    if (isFrom == 'KPIReportTechComparative') {
      iKpiReportType = 'tech';
      iKpiIds = advisor[0] != 'All' ? session.technician : selAdvisors;
    } else {
      iKpiReportType = 'store';
      iKpiIds = selStores;
    }
    if (filterText == 'CRANGE') {
      setOpenDilog(true);
    } else {
      if (
        expandedKPIs.length >= 7 ||
        iKpiIds.length == 0 ||
        selAdvisors.length == 0
      ) {
        setOpenDilogpdf(true);
      } else {
        setOpenSaveDlg(true);
        setCopy(true);
        setCopyFile(true);
        if (reportName != '') {
          setReportNameCopy(reportName);
        }
        setReportName('');
      }
    }
  };
  const handleSaveAsReport = () => {
    var advisor = session.technician;
    var iKpiReportType;
    var iKpiIds;

    if (isFrom == 'KPIReportTechComparative') {
      iKpiReportType = 'tech';
      iKpiIds =
        selAdvisors.length > 0
          ? selAdvisors.filter(function(obj) {
              return obj != 0;
            })
          : session.technician;
    } else {
      iKpiReportType = 'store';
      iKpiIds = selStores.filter(function(obj) {
        return obj != 0;
      });
    }

    //console.log('iKpiIds===', iKpiIds);
    var iStoreId =
      props &&
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.store_id
        ? props.history.location.state.store_id
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    if (reportName.trim().length > 0) {
      insertKPIReportName(
        'insert',
        reportName.trim(),
        filterStart,
        filterEnd,
        iKpiReportType,
        iKpiIds.length == 0 ? ['All'] : iKpiIds,
        iStoreId,
        props.keycloak.realmAccess.roles[0],
        selectedType,
        // privateChecked ? 'private' : 'public',
        filterText,
        hideGoals,
        expandedKPIs,
        JSON.stringify(session.hiddenKpis),
        JSON.stringify([session.sortOrder]),
        kpiSortedArray,
        result => {
          // if (
          //   result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results ==
          //   'Insert successful'
          // ) {
          if (
            result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results[0]
              .status == 1
          ) {
            setErrorReport('');
            setReportName('');
            setRequiredText(false);
            setOpenSaveDlg(false);
            setCopy(false);
            setCopyFile(false);
            setOpenSnackbar(true);
          } else {
            setRequiredText(true);
            setErrorReport(
              result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                .results[0].msg
            );
          }
        }
      );
    } else {
      setRequiredText(true);
      setErrorReport('This is required!');
    }
  };
  const handleOkSaveReport = () => {
    var advisor = session.technician;
    var iKpiReportType;
    var iKpiIds;
    if (isFrom == 'KPIReportTechComparative') {
      iKpiReportType = 'tech';
      //iKpiIds = advisor[0] != 'All' ? session.technician : selAdvisors;
      const allowedValues = ['Total Selected', 'Total Shop'];
      const containsOtherValues = selAdvisors.some(
        value => !allowedValues.includes(value)
      );
      if (
        containsOtherValues &&
        (visibleTotals.length == 0 || visibleTotals.length == 2)
      ) {
        iKpiIds = selAdvisors;
      } else {
        iKpiIds = selAdvisors.filter(item => !visibleTotals.includes(item));
      }
    } else {
      iKpiReportType = 'store';
      const allowedValues = ['Group Average', 'Group Roll-Up'];
      const containsOtherValues = selStores.some(
        value => !allowedValues.includes(value)
      );

      if (containsOtherValues) {
        iKpiIds = selStores;
      } else {
        iKpiIds = selStores.filter(item => !visibleTotals.includes(item));
      }
    }
    var iStoreId = JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const fixedRateTypes = document.getElementById('fixedratetypes');
    const checkedRadio = fixedRateTypes.querySelector(
      'input[name="controlled-radio-buttons-group"]:checked'
    ).value;
    if (parent == 'savedReports') {
      var iStoreId =
        props &&
        props.history &&
        props.history.location &&
        props.history.location.state &&
        props.history.location.state.store_id
          ? props.history.location.state.store_id
          : JSON.parse(localStorage.getItem('selectedStoreId'))[0];

      //if (selectedTypeUpdate != checkedRadio) {
      setSelectedTypeUpdate(checkedRadio);

      insertKPIReportName(
        'update',
        reportName.trim(),
        filterStart,
        filterEnd,
        iKpiReportType,
        iKpiIds.length == 0 ? ['All'] : iKpiIds,
        iStoreId,
        props.keycloak.realmAccess.roles[0],
        selectedType,
        // privateChecked ? 'private' : 'public',
        filterText,
        hideGoals,
        expandedKPIs,
        JSON.stringify(session.hiddenKpis),
        JSON.stringify([session.sortOrder]),
        kpiSortedArray,
        result => {
          // if (
          //   result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results ==
          //   'Insert successful'
          // ) {
          if (
            result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results[0]
              .status == 1
          ) {
            setErrorReport('');
            // setReportName('');
            setRequiredText(false);
            setOpenSaveDlg(false);
            setCopy(false);
            setOpenSnackbar(true);
          } else {
            setRequiredText(true);
            setErrorReport(
              result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                .results[0].msg
            );
          }
        }
      );
      // } else {
      //   setOpenSnackbarWarn(true);
      // }
    } else {
      if (reportName.trim().length > 0) {
        insertKPIReportName(
          'insert',
          reportName.trim(),
          filterStart,
          filterEnd,
          iKpiReportType,
          iKpiIds.length == 0 ? ['All'] : iKpiIds,
          iStoreId,
          props.keycloak.realmAccess.roles[0],
          selectedType,
          // privateChecked ? 'private' : 'public',
          filterText,
          hideGoals,
          expandedKPIs,
          JSON.stringify(session.hiddenKpis),
          JSON.stringify([session.sortOrder]),
          kpiSortedArray,
          result => {
            // if (
            //   result.data.statelessDbdKpiScorecardInsertKpiSavedReport.results ==
            //   'Insert successful'
            // ) {
            if (
              result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                .results[0].status == 1
            ) {
              setErrorReport('');
              setReportName('');
              setRequiredText(false);
              setOpenSaveDlg(false);
              setOpenSnackbar(true);
            } else {
              setRequiredText(true);
              setErrorReport(
                result.data.statelessDbdKpiScorecardInsertKpiSavedReport
                  .results[0].msg
              );
            }
          }
        );
      } else {
        setRequiredText(true);
        setErrorReport('This is required!');
      }
    }
  };
  const handleCancelSaveReport = () => {
    setSelectedType(props.kpiReportData.visibility);
    setOpenSaveDlg(false);
    setCopy(false);
    setRequiredText(false);
    setCopyFile(false);
    setErrorReport('');
    setReportName(props.kpiReportData.report_name);
  };
  const onChangeReportName = e => {
    const nameRegex = /^[a-zA-Z0-9][a-zA-Z0-9\s!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|`~-]*$/;

    if (!nameRegex.test(e.target.value) && e.target.value) {
      setRequiredText('');
    } else {
      setErrorReport('');
      setReportName(e.target.value);

      setRequiredText(false);
    }
  };

  // const handleCheckboxChange = e => {
  //   setPrivateChecked(!privateChecked);
  // };

  const handleCheckboxChange = event => {
    setSelectedType(event.target.value);
    if (event.target.value == 'private') {
      setPrivateChecked(true);
    } else if (event.target.value == 'public') {
      setPrivateChecked(false);
    }
  };
  // const linkButtonStyle = useMemo(
  //   () => ({
  //     display: storeChanged ? 'block' : 'none'
  //   }),
  //   [storeIdSelected, storeChanged]
  // );

  // const renderBackButton = () => {
  //   props.history.push('/ReportSaved', { selAdvisors });
  // };
  const renderBackButton = () => {
    let expandedRpt =
      props.history &&
      props.history.location &&
      props.history.location.state &&
      props.history.location.state.isExpanded
        ? props.history.location.state.isExpanded
        : 0;
    //props.history.push('/ReportSaved', { selAdvisors });
    props.history.push({
      pathname: '/ReportSaved',
      state: {
        isExpanded: expandedRpt,
        created_by: props.history.location.state.created_by
      }
    });
  };

  const handleMailClick = () => {
    var advisor = session.technician;
    var iKpiReportType;
    var iKpiIds;
    if (isFrom == 'KPIReportTechComparative') {
      iKpiReportType = 'tech';
      iKpiIds = advisor[0] != 'All' ? session.technician : selAdvisors;
    } else {
      iKpiReportType = 'store';
      iKpiIds = selStores;
    }

    if (
      expandedKPIs.length >= 7 ||
      iKpiIds.length == 0 ||
      selAdvisors.length == 0
    ) {
      setOpenDilogpdf(true);
    } else {
      setOpenDialogue(true);
      let userList = [];
      let type = isFrom == 'KPIReportStoreComparative' ? 'store' : 'advisor';
      getEmail('NULL', 'NULL', 'view', type, result => {
        if (
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results &&
          result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.length >
            0
        ) {
          userList.push(
            result.data.statelessCcPhysicalRwGetorsetMailIdMaster.results.map(
              item => item.value
            )
          );
          setMailUsers(userList);
        } else {
          setMailUsers([]);
        }
      });
    }
  };
  const handlePdfClick = (
    gridApi,
    filterStart,
    filterEnd,
    fileNameExport,
    expandedKPIs,
    hideGoals,
    image,
    visibleTotals
  ) => {
    if (!loadingIconPDF) {
      setSuccessPDF(false);
      setLoadingIconPDF(true);
    }

    var advisor = session.technician;
    var iKpiReportType;
    var iKpiIds;
    if (isFrom == 'KPIReportTechComparative') {
      iKpiReportType = 'tech';
      iKpiIds = advisor[0] != 'All' ? session.technician : selAdvisors;
    } else {
      iKpiReportType = 'store';
      iKpiIds = selStores;
    }

    if (
      expandedKPIs.length >= 7 ||
      iKpiIds.length == 0 ||
      selAdvisors.length == 0
    ) {
      setOpenDilogpdf(true);
      setLoadingIconPDF(false);
    } else {
      // exportToPDF(
      //   gridApi,
      //   filterStart,
      //   filterEnd,
      //   fileNameExport,
      //   expandedKPIs,
      //   hideGoals,
      //   image,
      //   visibleTotals
      // );

      var filterFromTopbar = arraysEqual(selAdvisors, advisor);
      ////console.log('advisor===advisor', selAdvisors, advisor);
      var arrAdv;
      if (selAdvisors.length > 0) {
        var arr = selAdvisors.filter(function(obj) {
          return obj != 'Total Shop' && obj != 'Total Selected' && obj != 0;
        });
        var arrAdv = arr;
      } else {
        arrAdv = null;
      }
      if (isFrom == 'KPIReportTechComparative') {
        getKpiComparativeReport(
          filterStart,
          filterEnd,
          2,
          2,
          null,
          // advisor[0] == 'All' ? null : filterFromTopbar ? advisor : null,
          'KPI_TECH_COMP',
          getTimeZone(),
          advisor[0] == 'All' ? null : filterFromTopbar ? advisor : null,
          '',
          'C',
          'Customer',
          'N',
          arrAdv != null && arrAdv.length > 0 && arrAdv[0] == 'All'
            ? null
            : arrAdv,
          result => {
            if (
              result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                .kpiScorecardDetails
            ) {
              var data = JSON.parse(
                result.data.statelessDbdKpiScorecardGetKpiScorecardDetails
                  .kpiScorecardDetails[0].jsonData
              );

              const allowedValues = ['Total Selected', 'Total Shop'];
              const containsOtherValues = selAdvisors.some(
                value => !allowedValues.includes(value)
              );

              var iKpiIdsAdv;

              if (
                containsOtherValues &&
                (visibleTotals.length == 0 || visibleTotals.length == 2)
              ) {
                iKpiIdsAdv = selAdvisors;
              } else {
                iKpiIdsAdv = selAdvisors.filter(
                  item => !visibleTotals.includes(item)
                );
              }

              let selectedIndex;
              if (!containsOtherValues) {
                data.forEach((originalItem, index) => {
                  selectedIndex = originalItem.data.findIndex(
                    data => data.adv_or_tech_name === 'Total Selected'
                  );

                  if (selectedIndex !== -1) {
                    originalItem.data[selectedIndex].kpi_data = '0/0/0';
                  }
                });
              }

              if (data[0].data != null) {
                const kpiArray = [];

                kpiSortedArray.forEach(item => {
                  kpiArray.push(item);
                  if (!hideGoals.includes(item)) {
                    kpiArray.push(item + '-goal');
                    kpiArray.push(item + '-var');
                  }
                });
                //console.log('kpiArray', kpiArray, selAdvisors);

                exportToPDF(
                  gridApi,
                  filterStart,
                  filterEnd,
                  fileNameExport,
                  expandedKPIs,
                  hideGoals,
                  image,
                  data,
                  isFrom,
                  parent == 'savedreports'
                    ? session.selectedAdvisors
                    : iKpiIdsAdv,
                  parent == 'savedreports' ? session.selStores : selStores,

                  session,
                  kpiArray,
                  storeNickName
                );
                setSuccessPDF(true);
                setLoadingIconPDF(false);
              }
            }
          }
        );
      } else {
        var arrAdv;
        if (selStores.length > 0) {
          var arr = selStores.filter(function(obj) {
            return obj != 'Group Average' && obj != 'Group Roll-Up' && obj != 0;
          });
          var arrAdv = arr;
        } else {
          arrAdv = null;
        }
      }
    }
  };
  const handleCloseEmail = () => {
    setOpenDialogue(false);
  };
  const CancelDilog = () => {
    setOpenDilog(false);
    setSuccessPDF(true);
    setLoadingIconPDF(false);
    setSuccessExcel(true);
    setLoadingIconExcel(false);
  };
  const CancelDilogpdf = () => {
    setOpenDilogpdf(false);
    setSuccessPDF(true);
    setLoadingIconPDF(false);
    setSuccessExcel(true);
    setLoadingIconExcel(false);
  };
  const onCellClicked = () => {
    // setExportDisabled(true);
  };
  const CancelDilogAll = () => {
    setOpenDilogAll(false);
  };
  const onRowGroupOpened = params => {
    var kpiArr = expandedKPIs;
    // var kpiArr = [...expandedKPIs];
    if (typeof params.data == 'undefined') {
      if (!params.node.expanded) {
        if (!kpiArr.includes(params.node.key)) {
          kpiArr.push(params.node.key);
        }
      } else {
        const index = kpiArr.indexOf(params.node.key);
        if (index > -1) {
          // only splice array when item is found
          kpiArr.splice(index, 1); // 2nd parameter means remove one item only
        }
      }
    }

    setExpandedKPIs(kpiArr);
  };
  let title = 'KPI Store Comparative';
  if (
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.parent &&
    props.history.location.state.parent == 'savedReports'
  ) {
    if (isFrom === 'KPIReportTechComparative') {
      title = `KPI Advisor Comparative - Saved Reports - ${reportName}`;
    } else {
      title = `KPI Store Comparative - Saved Reports - ${reportName}`;
    }
  } else if (isFrom === 'KPIReportTechComparative') {
    title = 'KPI Advisor Comparative';
  } else {
    title = 'KPI Store Comparative';
  }
  const getContextMenuItems = params => {
    return []; // Return an empty array to hide the context menu
  };
  const onCellContextMenu = event => {
    event.preventDefault();
  };
  const displayText = useMemo(() => {
    if (
      // props.history &&
      // props.history.location &&
      // props.history.location.state &&
      // props.history.location.state.parent
      parent === 'savedReports'
    ) {
      return fileName;
    }
    return title;
  }, [props.history, fileName, title]);
  return (
    <React.Fragment>
      {/* <Kpi history={history} /> */}
      {isPageLoading ? (
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <Grid
              xs={12}
              className={clsx(classes.headerItem, 'main-title-kpi')}
            >
              <Grid className="adv-report">
                <Grid className="kpi-report-2-name">
                  {//props.history &&
                  // props.history.location &&
                  // props.history.location.state &&
                  // props.history.location.state.parent &&
                  // props.history.location.state.parent == 'savedReports'
                  parent == 'savedReports' && (
                    <Button
                      variant="contained"
                      className={'bck-btn'}
                      // style={linkButtonStyle}
                      onClick={renderBackButton}
                    >
                      <Typography variant="body1" align="left">
                        Back
                      </Typography>
                    </Button>
                  )}
                  <Typography
                    variant="h4"
                    color="primary"
                    className={clsx(classes.mainLabel, 'main-title')}
                  >
                    {/* {title} */}
                    {fileName}
                    {/* {displayText} */}
                  </Typography>
                </Grid>

                <Grid className="picker-report">
                  {closedDate ? (
                    <Typography
                      variant="body1"
                      color="secondary"
                      align="right"
                      className={clsx(classes.dataLabel, 'date-asof')}
                    >
                      {'Data as of: ' + moment(closedDate).format('MM/DD/YY')}
                    </Typography>
                  ) : (
                    ''
                  )}
                  <FormControl
                    variant="outlined"
                    margin="dense"
                    className={clsx(classes.formControl, 'input-container')}
                  >
                    <DateRangePicker
                      initialSettings={{
                        // maxDate: {
                        //   date: new Date(),
                        // },
                        // minDate:{
                        //   date: (selectedDates[1])
                        // },

                        locale: {
                          format: 'MM/DD/YY',
                          separator: ' - '
                        },
                        opens: 'left',
                        ranges: {
                          // ['Today&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          // today]: [
                          //   moment(closedDate).toDate(),
                          //   moment(closedDate).toDate()
                          // ],
                          ['Yesterday&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          yesterDay]: [
                            moment(dates[0] && dates[0].yesterday).toDate(),
                            moment(dates[0] && dates[0].yesterday).toDate()
                          ],
                          ['Day Before Yest.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          dayBfYest]: [
                            moment(
                              dates[0] && dates[0].dayBeforeYesterday
                            ).toDate(),
                            moment(
                              dates[0] && dates[0].dayBeforeYesterday
                            ).toDate()
                          ],
                          ['This Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          thisWeek]: [
                            moment(dates[0].thisweekstartdate).toDate(),
                            moment(dates[0].thisweekenddate).toDate()
                          ],
                          ['Last Week&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          lastWeek]: [
                            moment(dates[0].lastweekstartdate).toDate(),
                            moment(dates[0].lastweekenddate).toDate()
                          ],
                          ['Last 2 Weeks&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          lastTwoWeek]: [
                            moment(dates[0].lasttwoweekstartdate).toDate(),
                            moment(dates[0].lasttwoweekenddate).toDate()
                          ],
                          // 'Last 7 Days': [
                          //   moment()
                          //     .subtract(6, 'days')
                          //     .toDate(),
                          //   moment().toDate()
                          // ],
                          // 'Last 30 Days': [
                          //   moment()
                          //     .subtract(29, 'days')
                          //     .toDate(),
                          //   moment().toDate()
                          // ],
                          ['This Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          mtd]: [
                            moment(dates[0] && dates[0].mtdstartdate).toDate(),
                            moment(dates[0] && dates[0].mtdenddate).toDate()
                          ],
                          ['Last Month&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          lastMonth]: [
                            moment(
                              dates[0] && dates[0].lastmonthstartdate
                            ).toDate(),
                            moment(
                              dates[0] && dates[0].lastmonthenddate
                            ).toDate()
                          ],
                          ['Last 3 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          lastThreeMonths]: [
                            moment(dates[0].lastthreemonthstartdate).toDate(),
                            moment(dates[0].lastthreemonthenddate).toDate()
                          ],
                          ['Last Qtr&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          lastQtr]: [
                            moment(dates[0].lastquarterstartdate).toDate(),
                            moment(dates[0].lastquarterenddate).toDate()
                          ],
                          ['YTD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          ytd]: [
                            moment(dates[0].ytdstartdate).toDate(),
                            moment(dates[0].ytdenddate).toDate()
                          ],
                          ['Last 12 Mths&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          lastTwelveMonths]: [
                            moment(dates[0].lasttwelvemonthstartdate).toDate(),
                            moment(dates[0].lasttwelvemonthenddate).toDate()
                          ],
                          ['Last Year&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;' +
                          lastYear]: [
                            moment(dates[0].lastyearstartdate).toDate(),
                            moment(dates[0].lastyearenddate).toDate()
                          ]
                        },
                        maxDate: moment(dates[0] && dates[0].today).toDate(),
                        alwaysShowCalendars: false,
                        applyClass: clsx(classes.calButton, 'apply-btn'),
                        cancelClass: clsx(classes.calButton, 'apply-btn'),

                        // startDate:
                        //   filterStart && filterStart != ''
                        //     ? moment(filterStart).toDate()
                        //     : moment(dates[0].lastquarterstartdate).toDate(),
                        startDate:
                          filterStart && filterStart != ''
                            ? moment(filterStart).toDate()
                            : localStorage.getItem('kpiDataStatus') == 1
                            ? moment(dates[0] && dates[0].mtdstartdate).toDate()
                            : moment(
                                dates[0] && dates[0].lastmonthstartdate
                              ).toDate(),
                        endDate:
                          filterEnd && filterEnd != ''
                            ? moment(filterEnd).toDate()
                            : localStorage.getItem('kpiDataStatus') == 1
                            ? moment(dates[0] && dates[0].mtdenddate).toDate()
                            : moment(
                                dates[0] && dates[0].lastmonthenddate
                              ).toDate()
                        // endDate:
                        //   filterEnd && filterEnd != ''
                        //     ? moment(filterEnd).toDate()
                        //     : moment(dates[0].lastquarterenddate).toDate()
                        //showDropdowns: true
                      }}
                      // onEvent={this.handleEventCallback}
                      onApply={handleCallback}
                    >
                      <input
                        type="text"
                        className="datepicker"
                        id="picker"
                        name="picker"
                        aria-labelledby="label-picker"
                      />
                      {/* <TextField
                    id="outlined-basic"
                    label="Select Date"
                    size="small"
                    //onChange={}
                    value={this.state.value}
                    variant="outlined"
                  /> */}
                    </DateRangePicker>
                    <label class="labelpicker" for="picker" id="label-picker">
                      <div class="textpicker">Select Date</div>
                    </label>
                  </FormControl>
                  {/* <Button
                    variant="contained"
                    id="reset-layout"
                    className={'reset-btn'}
                    onClick={handleSaveReport}
                    disabled={
                      JSON.parse(localStorage.getItem('selectedStoreId'))
                        .length > 1
                    }
                  > */}
                  {JSON.parse(localStorage.getItem('selectedStoreId')).length <=
                    1 && (
                    <Tooltip title="Save Report">
                      <Link
                        // className={classes.linkItem}
                        style={{
                          pointerEvents:
                            createdBy !== '' &&
                            createdBy === localStorage.getItem('userID')
                              ? 'auto'
                              : createdBy !== '' &&
                                createdBy !== localStorage.getItem('userID') &&
                                !props.keycloak.realmAccess.roles.includes(
                                  'superadmin'
                                )
                              ? 'none'
                              : 'auto',
                          color:
                            createdBy !== '' &&
                            createdBy === localStorage.getItem('userID')
                              ? '#003d6b'
                              : createdBy !== '' &&
                                createdBy !== localStorage.getItem('userID') &&
                                !props.keycloak.realmAccess.roles.includes(
                                  'superadmin'
                                )
                              ? 'grey'
                              : '#003d6b',
                          cursor: 'pointer'
                        }}
                        //style={linkStyle}
                        onClick={handleSaveReport}
                      >
                        <SaveIcon
                          style={{
                            height: '0.9em',
                            weight: '0.8em',
                            marginBottom: '-2px',
                            marginRight:
                              parent == 'savedReports' ? '-7.5px' : '-6px'
                            // display: 'none'
                          }}
                        />
                      </Link>
                    </Tooltip>
                  )}
                  {/* </Button> */}
                  {parent == 'savedReports' && (
                    // {parent == 'savedReports' && (
                    <Tooltip title="Rename and Copy">
                      <Link
                        className={classes.linkItem}
                        // style={linkStyleCopy}
                        style={{
                          display:
                            (createdBy != '' &&
                              createdBy == localStorage.getItem('userID')) ||
                            visibile == 'public' ||
                            props.keycloak.realmAccess.roles.includes(
                              'superadmin'
                            )
                              ? 'block'
                              : 'none'
                        }}
                        onClick={handleCopyReport}
                      >
                        <FileCopyOutlinedIcon
                          style={{
                            height: '0.73em',
                            weight: '0.8em',
                            marginRight: '-8px',
                            marginTop: '2px'

                            //                         height: 0.76em;
                            // margin-right: -8px;
                            // margin-top: 2px;
                            // display: allStore ? 'none' : 'block'
                          }}
                        />
                      </Link>
                    </Tooltip>
                  )}{' '}
                  <Tooltip title="Email Now">
                    <Link
                      // className={classes.linkItem}
                      // style={linkStyle}
                      style={{
                        pointerEvents:
                          createdBy !== '' &&
                          createdBy === localStorage.getItem('userID')
                            ? 'auto'
                            : createdBy !== '' &&
                              createdBy !== localStorage.getItem('userID') &&
                              !props.keycloak.realmAccess.roles.includes(
                                'superadmin'
                              )
                            ? 'none'
                            : 'auto',
                        color:
                          createdBy !== '' &&
                          createdBy === localStorage.getItem('userID')
                            ? '#003d6b'
                            : createdBy !== '' &&
                              createdBy !== localStorage.getItem('userID') &&
                              !props.keycloak.realmAccess.roles.includes(
                                'superadmin'
                              )
                            ? 'grey'
                            : '#003d6b',
                        cursor: 'pointer'
                      }}
                      onClick={() =>
                        handleMailClick(gridApi, filterStart, filterEnd)
                      }
                    >
                      <MailIcon
                        style={{
                          height: '1.15em',
                          weight: '1.0em',
                          paddingTop: '2px',
                          marginRight: '-2px'
                        }}
                      />
                    </Link>
                  </Tooltip>
                  <Tooltip title="Export To PDF">
                    <div
                      style={{ position: 'relative', display: 'inline-block' }}
                    >
                      <Link
                        className={classes.linkItem}
                        onClick={() =>
                          handlePdfClick(
                            gridApi,
                            filterStart,
                            filterEnd,
                            fileNameExport,
                            expandedKPIs,
                            hideGoals,
                            image,
                            visibleTotals
                          )
                        }
                        style={{ textDecoration: 'none' }}
                      >
                        <i
                          class="fas fa-file-pdf"
                          style={{
                            cursor: 'pointer',
                            color: successExcel ? '#c20a0a' : '#c20a0a'
                          }} // Match the color and size
                        ></i>
                      </Link>
                      {loadingIconPDF && (
                        <CircularProgress
                          style={{
                            width: '23px',
                            height: '23px',
                            color: green[500],
                            position: 'absolute',
                            top: '-3px',
                            left: '-5px',
                            zIndex: 1
                          }}
                        />
                      )}
                    </div>
                    {/* <Link
                      className={classes.linkItem}
                      onClick={
                        () =>
                          handlePdfClick(
                            gridApi,
                            filterStart,
                            filterEnd,
                            fileNameExport,
                            expandedKPIs,
                            hideGoals,
                            image,
                            visibleTotals
                          )

                        // exportToPDF(
                        //   gridApi,
                        //   filterStart,
                        //   filterEnd,
                        //   fileName,
                        //   expandedKPIs,
                        //   hideGoals,
                        //   image,
                        //   visibleTotals
                        // )
                      }
                    >
                      
                      <i
                        class="fas fa-file-pdf"
                        style={{ color: '#c20a0a' }}
                      ></i>
                    </Link> */}
                  </Tooltip>
                  <Tooltip title="Export To Excel">
                    {/* <Link
                      className={classes.linkItem}
                      onClick={() => handleExportClick()}
                    >
                     
                      <i
                        class="fas fa-file-excel"
                        style={{ color: '#0e7c3e' }}
                      ></i>
                    </Link> */}
                    <div
                      style={{ position: 'relative', display: 'inline-block' }}
                    >
                      <Link
                        className={classes.linkItem}
                        onClick={() => handleExportClick()}
                        style={{ textDecoration: 'none' }}
                      >
                        <i
                          class="fas fa-file-excel"
                          style={{
                            cursor: 'pointer',
                            color: successExcel ? '#0e7c3e' : '#0e7c3e'
                          }} // Match the color and size
                        ></i>
                      </Link>
                      {loadingIconExcel && (
                        <CircularProgress
                          style={{
                            width: '23px',
                            height: '23px',
                            color: green[500],
                            position: 'absolute',
                            top: '-3px',
                            left: '-5px',
                            zIndex: 1
                          }}
                        />
                      )}
                    </div>
                  </Tooltip>
                  <Button
                    variant="contained"
                    id="reset-layout-kpi"
                    className={'reset-btn'}
                    onClick={setResetDashboard}
                  >
                    <RestoreIcon style={{ height: '0.8em', weight: '0.8em' }} />
                    <Typography variant="body1" align="left">
                      Reset Layout
                    </Typography>
                  </Button>
                </Grid>
              </Grid>
            </Grid>
            <Divider />
            {isLoading == false ? (
              <div>
                <Box className={classes.boxClass}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    className={classes.boxClass}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : (
              <div id={'kpi-report-2'} style={containerStyle}>
                <div class="kpiscore-card-main">
                  <div class="custom-header-kpir">
                    {/* <div class="report-name-title">
                      <span class="report-num">KPI Report #1</span>
                      <span class="report-name">ABC Auto Group</span>
                      <span class="report-date">3/1/23 - 3/30/23</span>
                    </div> */}
                    <div class="report-name-title">
                      <span class="report-num">
                        {isFrom == 'KPIReportTechComparative'
                          ? 'KPI Tech Comparative'
                          : 'KPI Store Comparative'}
                      </span>

                      <span class="report-name">
                        {pathaname == 'KPIReportTechComparative'
                          ? // ? localStorage.getItem('storeSelected')
                            storeNickName
                          : localStorage.getItem('storeGroup')}
                      </span>
                      <span className="report-date">
                        {filterStart != ''
                          ? moment(filterStart).format('MM/DD/YY')
                          : ''}
                        {' - '}
                        {filterEnd && filterEnd != ''
                          ? moment(filterEnd).format('MM/DD/YY')
                          : ''}
                      </span>
                    </div>
                    <div class="header-logo">
                      <img src="/images/logos/logo_armatus.png" />
                    </div>
                    <div class="fopc-main-title">
                      Fixed Ops Performance Center
                    </div>
                  </div>
                </div>
                {/* <div className={classes.kpiGrid}>
                  <div className="ag-header-group-cell-label">
                    <div className="kpi-header-cell">
                      <div className="customHeaderLabel report-title">
                        <Typography className={classes.kpiHeading}>
                          {'KPI Report#2'}
                        </Typography>
                      </div>
                      <div className="customHeaderLabel report-store">
                        <Typography
                          variant="h4"
                          className={classes.kpiStoreHeading}
                        >
                          {localStorage.getItem('selectedStoreName')}
                        </Typography>
                      </div>
                      <div className="customHeaderLabel report-date">
                        <Typography className={classes.kpiHeading}>
                          {'Last Qtr : Jul to Sep'}
                        </Typography>
                      </div>
                    </div>
                  </div>
                </div> */}
                <div
                  className={clsx(
                    classes.kpiTable,
                    'kpi-custom-table',
                    'ag-theme-balham'
                  )}
                  style={{
                    height: window.screen.availHeight - 250 + 'px',
                    alignContent: 'center',
                    marginLeft: '8px',
                    paddingRight: '16px',
                    paddingBottom: '16px'
                  }}
                  onCellContextMenu={onCellContextMenu}
                >
                  <AgGridReact
                    ref={gridRef}
                    frameworkComponents={{
                      customHeaderGroup: CustomHeaderGroup,
                      kpiHeaderGroup: KPIHeaderGroup,
                      kpiColumnRenderer: KPIColumnRenderer,
                      tableCellRenderer: TableCellRenderer,
                      // goalCellRenderer: GoalCellRenderer,
                      rowData: rowData
                    }}
                    onCellClicked={onCellClicked}
                    onRowClicked={onRowGroupOpened}
                    // domLayout="autoHeight"
                    sideBar={sideBar}
                    autoGroupColumnDef={{
                      headerName: '',
                      suppressMenu: true,
                      field: 'kpi_type',
                      pinned: 'left',
                      width: 380,
                      hide: true,
                      chartDataType: 'category',
                      cellRenderer: 'agGroupCellRenderer',
                      valueGetter: params => {
                        return params.data.kpi_name;
                      },
                      // rowSpan: function(params) {
                      //   //  //console.log('ccc==', params);
                      //   if (typeof params.data != 'undefined') {
                      //     if (
                      //       params.data.kpi_type == 'Pricing' &&
                      //       params.data.kpi_no == 4
                      //     ) {
                      //       return 2;
                      //     } else {
                      //       return 1;
                      //     }
                      //   }
                      // },
                      cellClass: cellClass,

                      // provide extra params to the cellRenderer
                      cellRendererParams: {
                        // turn off the row count
                        suppressCount: true,
                        // turn off double click for expand
                        suppressDoubleClickExpand: true,
                        // enable checkbox selection
                        groupSuppressAutoColumn: true,
                        groupRemoveSingleChildren: true,
                        innerRenderer: 'kpiHeaderGroup'
                      }
                    }}
                    overlayLoadingTemplate={
                      '<span class="ag-overlay-loading-center">Processing...</span>'
                    }
                    context={{
                      visibleColHeader: visibleCol,
                      setColDef: setColDef,
                      handleRowShowHide: handleRowShowHide,
                      onRowClicked: onRowClicked,
                      onArrowClicked: onArrowClicked,
                      handleColumnHide: handleColumnHide,
                      hideGoals: hideGoals,
                      hideGoalsColumn: hideGoalsColumn,
                      rowData: rowData,
                      hiddenRowId: hiddenRowId,
                      selectedRowIds: selectedRowIds,
                      rowSortValue: rowSortValue,
                      isLoading: clickHeader
                    }}
                    onColumnVisible={onCheck}
                    //pivotMode={false}
                    //cellClassRules={cellClassRules}
                    gridOptions={gridOptions}
                    groupDisplayType={'groupRows'}
                    groupDefaultExpanded={1}
                    getRowStyle={getRowStyle}
                    getRowClass={getRowClass}
                    rowData={rowData}
                    columnDefs={columnDefs}
                    defaultColDef={defaultColDef}
                    groupHeaderHeight={30}
                    defaultExcelExportParams={defaultExcelExportParams}
                    excelStyles={excelStyles}
                    popupParent={popupParent}
                    doesExternalFilterPass={doesExternalFilterPass}
                    //onColumnVisible={columnChanged}
                    //headerHeight={120}
                    // floatingFiltersHeight={50}
                    // pivotGroupHeaderHeight={50}
                    // pivotHeaderHeight={100}
                    onGridReady={onGridReady}
                    onFirstDataRendered={params => {
                      // Hide columns based on pivot state
                      const columnsToShow = params.columnApi.getAllDisplayedColumns();
                      params.columnApi.setColumnsVisible(columnsToShow, true);
                      params.columnApi.setColumnsVisible(
                        columnDefs
                          .filter(col => !columnsToShow.includes(col.field))
                          .map(col => col.field),
                        false
                      );
                    }}
                    getContextMenuItems={getContextMenuItems}
                    //onRowClicked={onRowClicked}
                    suppressContextMenu={true}
                  />
                </div>
              </div>
            )}
          </Paper>

          <Dialog
            transition={Fade}
            classes={{
              paper: classes.paper
            }}
            BackdropProps={{
              classes: {
                root: classes.backDrop
              }
            }}
            //maxWidth="xl"
            //style={{ maxWidth: 900, maxHeight: 700 }}
            open={openSaveDlg}
          >
            {/* <Dialog
            fullWidth
            maxWidth="sm"
            aria-labelledby="confirmation-dialog-title"
            open={openSaveDlg}
          > */}
            {parent == 'savedReports' && !copy ? (
              <DialogTitle id="form-dialog-title">
                <Typography
                  variant="h5"
                  color="primary"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Edit Report
                </Typography>
              </DialogTitle>
            ) : parent == 'savedReports' && copyFile ? (
              <DialogTitle id="form-dialog-title">
                <Typography
                  variant="h5"
                  color="primary"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Rename and Copy
                </Typography>
              </DialogTitle>
            ) : (
              <DialogTitle id="form-dialog-title">
                <Typography
                  variant="h5"
                  color="primary"
                  style={{
                    textTransform: 'none'
                  }}
                >
                  Save Report
                </Typography>
              </DialogTitle>
            )}

            <DialogContent style={{ overflowX: 'hidden' }}>
              <TableContainer
                component={Paper}
                style={{
                  margin: 4,
                  padding: 1,
                  display: 'block',
                  width: '100%'
                }}
              >
                <Table
                  className="email-table"
                  id="maildetails"
                  // style={{ minWidth: 300 }}
                  size="small"
                  aria-label="a dense table"
                >
                  <TableHead
                    style={{
                      textAlign: 'center',
                      backgroundColor: '#003d6b'
                    }}
                  ></TableHead>

                  <TableRow key={'email'}>
                    <TableCell
                      align="left"
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      Report Name
                    </TableCell>
                    <TableCell
                      align="left"
                      colSpan={1}
                      size="small"
                      style={{
                        fontSize: 14,
                        color: '#003d6b'
                      }}
                    >
                      <div style={{ display: 'flex' }}>
                        <div
                          style={{
                            display: 'inline-block',
                            width: 118,
                            marginLeft: 16
                          }}
                        >
                          Access Type
                        </div>
                        <div
                          style={{
                            display: 'inline-block',
                            width: 78,
                            marginLeft: 16
                          }}
                        >
                          Created On
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>

                  <TableRow>
                    <TableCell height={10}>
                      {parent == 'savedReports' && !copy ? (
                        <Typography
                          style={{
                            textTransform: 'none'
                          }}
                        >
                          {
                            // props &&
                            // props.history &&
                            // props.history.location &&
                            // props.history.location.state &&
                            // props.history.location.state.report_name &&
                            // props.history.location.state.report_name
                            reportName
                          }
                        </Typography>
                      ) : (
                        <TextField
                          autoFocus
                          onChange={onChangeReportName}
                          value={
                            // props &&
                            // props.history &&
                            // props.history.location &&
                            // props.history.location.state &&
                            // props.history.location.state.report_name &&
                            // !copy
                            //   ? props.history.location.state.report_name
                            //   :
                            reportName
                          }
                          helperText={
                            (reportName == undefined || reportName == '') &&
                            requiredText
                              ? 'This is required!'
                              : errorReport
                            // ? errorReport
                            // : filterText == 'CRANGE'
                            // ? 'Custom date range selection is not allowed'
                            // : ''
                          }
                          margin="dense"
                          id="name"
                          type="text"
                          fullWidth
                          inputProps={{ maxLength: 25 }}
                          onInput={e => {
                            const value = e.target.value;
                            if (/[^a-zA-Z0-9 ]/.test(value)) {
                              e.target.value = value.replace(
                                /[^a-zA-Z0-9 ]/g,
                                ''
                              );
                            }
                          }}
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      {/* <FormGroup style={{ marginTop: '19px' }}> */}
                      <Table>
                        <TableRow>
                          <TableCell
                            align="left"
                            size="small"
                            style={{
                              fontSize: 14,
                              color: '#003d6b'
                            }}
                          >
                            {/* <FormControlLabel
                              control={
                                <Checkbox
                                  defaultChecked
                                  size="small"
                                  color="primary"
                                  checked={privateChecked}
                                  onChange={handleCheckboxChange}
                                />
                              }
                              label={
                                <Typography
                                  className={classes.formControlLabel}
                                >
                                  Private
                                </Typography>
                              }
                            /> */}
                            <FormControl>
                              <RadioGroup
                                aria-labelledby="demo-controlled-radio-buttons-group"
                                name="controlled-radio-buttons-group"
                                id="fixedratetypes"
                                value={selectedType}
                                onChange={handleCheckboxChange}
                              >
                                <FormControlLabel
                                  value="public"
                                  style={{ marginTop: -6 }}
                                  control={<Radio size="small" />}
                                  label="Public"
                                />
                                <FormControlLabel
                                  value="private"
                                  control={<Radio size="small" />}
                                  label="Private"
                                />
                              </RadioGroup>
                            </FormControl>
                          </TableCell>
                          <TableCell>
                            <div>{moment().format('MM/DD/YY')}</div>
                          </TableCell>
                        </TableRow>
                      </Table>
                    </TableCell>
                  </TableRow>
                </Table>
              </TableContainer>
              {/* {error && <p className="error">{error}</p>}
              {errorChecked && <p className="errorChk">{errorChecked}</p>} */}
            </DialogContent>
            <DialogActions>
              <Button
                variant="contained"
                className={clsx('reset-btn')}
                onClick={handleCancelSaveReport}
                color="primary"
              >
                Cancel
              </Button>

              <Button
                variant="contained"
                className={clsx('reset-btn')}
                disabled={filterText == 'CRANGE'}
                onClick={
                  parent == 'savedReports' && copyFile
                    ? handleSaveAsReport
                    : handleOkSaveReport
                }
                color="primary"
                //  disabled={filterDisabled}
              >
                Save
              </Button>
            </DialogActions>
          </Dialog>

          <SuccessSnackbar
            onClose={handleSnackbarClose}
            open={openSnackbar}
            msg={'Report saved successfully!'}
            //goalFail={this.state.goalFail}
          />
          <SuccessSnackbarWarn
            onClose={handleSnackbarCloseWarn}
            open={openSnackbarWarn}
            msg={'You have made no changes'}
            //goalFail={this.state.goalFail}
          />
          <SuccessSnackbarAlert
            onClose={CancelDilogpdf}
            open={openDilogPdf}
            msg={'No data available'}
          />

          <EmailDialogKpi
            open={openDialogue}
            handlePopupClose={handleCloseEmail}
            mailUsers={mailUsers}
            selAdvisors={selAdvisors}
            selStores={selStores}
            filterStart={filterStart}
            filterEnd={filterEnd}
            hideGoals={hideGoals}
            visibleTotals={visibleTotals}
            expandedKPIs={expandedKPIs}
            hiddenKpis={session.hiddenKpis}
            sortOrder={session.sortOrder}
            reportName={props.kpiReportData.report_name}
            kpiSortedArray={kpiSortedArray}
            storeNickName={storeNickName}
          ></EmailDialogKpi>
        </div>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}

      {openDilog ? (
        <Dialog
          open={openDilog}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogContent>
            <Typography variant="h6" style={{ textTransform: 'none' }}>
              This option is not available for custom date range.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={CancelDilog} autoFocus color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      ) : null}
      {openDilogAll ? (
        <Dialog
          open={openDilogAll}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogContent>
            <Typography variant="h6" style={{ textTransform: 'none' }}>
              This option is not available at all stores.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={CancelDilogAll} autoFocus color="primary">
              Ok
            </Button>
          </DialogActions>
        </Dialog>
      ) : null}
    </React.Fragment>
  );
};
export default withKeycloak(Kpi);
