import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import Charts from './Charts';
import { setNavItems } from 'src/actions';

const useStyles = makeStyles(theme => ({
  root: {
    padding: 12
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function GraphDetailsView() {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useDispatch();

  const session = useSelector(state => state.session);
  const [title, setTitle] = useState('');

  const setNavItems = val => {
    let listItems = [];
    listItems.push(val);
   
    dispatch(setNavItems(listItems));
    
    //dispatch(setNavItems([val]));
  };

  return (
    <Page className={classes.root} title="Overview">
      
      <Charts history={history} session={session} setNavItems={setNavItems} />
    </Page>
  );
}

export default GraphDetailsView;
