import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import CPMovingELR from 'src/components/charts/CPMovingELR';
import CPMovingPartsMarkup from 'src/components/charts/CPMovingPartsMarkup';
import GraphDetails from 'src/components/charts/GraphDetails';
import DashboardDetailsActions from 'src/components/DashboardDetailsActions';
import GraphDetailsAction from 'src/components/GraphDetailsAction';
import LaborGrossAndVolumeOpportunity from 'src/components/charts/AdvisorOpportunityCharts';
import BarChartRendererPostgraphile from 'src/views/AnalyzeData/TechEfficiency/BarChartRendererPostgraphile';
import {
  getLayoutConfiguration,
  saveLayoutConfiguration,
  getComparisonMonths
} from '../../utils/Utils';
import $ from 'jquery';
import { Paper, Divider } from '@material-ui/core';
import HighCharts from './../../views/AnalyzeData/ComparisonCharts/ColumnRenderer';
import TechnicianMonthComparison from './../../views/AnalyzeData/TechEfficiency/ColumnRenderer';
import { withKeycloak } from '@react-keycloak/web';
import CPELRVsLaborSoldHours from 'src/views/LaborItemization/Charts';
import CPPartsMarkupVsPartsCost from 'src/views/PartsItemization/Charts';
import { getAllServiceAdvisors } from 'src/utils/hasuraServices';

import { withStyles } from '@material-ui/styles';
import clsx from 'clsx';
import PageHeader from 'src/components/PageHeader';
import 'src/styles.css';
import { ChartType } from '@ag-grid-enterprise/all-modules';

const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    if (this.props.history.location.SelectedLocation) {
      localStorage.setItem(
        'selectedLocation',
        this.props.history.location.SelectedLocation
      );
    } else if (
      this.props.history.location.state &&
      this.props.history.location.state.SelectedLocation
    ) {
      localStorage.setItem(
        'selectedLocation',
        this.props.history.location.state.SelectedLocation
      );
    }

    var parentId = this.props.history.location.search.split('?chartId=').pop();
    if (
      parentId != 1121 &&
      parentId != 1122 &&
      parentId != 1114 &&
      parentId != 1125 &&
      parentId != 1264 &&
      parentId != 1265
    ) {
      this.state = {
        layout: [],
        names: [],
        advisorNames: []
        // JSON.parse(
        //   JSON.stringify(
        //     getLayoutConfiguration('layout', 'fixed-ops-layout-13') || {}
        //   )
        // )
      };
    } else {
      this.state = {
        layout: [],
        names: [],
        advisorNames: []
        // JSON.parse(
        //   JSON.stringify(
        //     getLayoutConfiguration('layout', 'fixed-ops-layout-20') || {}
        //   )
        // )
      };
    }
    this.onLayoutChange = this.onLayoutChange.bind(this);
  }
  componentDidUpdate() {
    var parentId = this.props.history.location.search.split('?chartId=').pop();
  }
  componentDidMount = () => {
    let chartId = this.props.history.location.search.split('?chartId=').pop();
    if (
      (chartId == 931 || chartId == 926 || chartId == 921) &&
      this.props.session.kpiAdvisor.includes('All') == false
    ) {
      getAllServiceAdvisors(result => {
        if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
          this.setState({
            names: ['All'].concat(
              result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.map(
                e => (e ? e.serviceadvisor.toString() : '')
              )
            )
          });

          this.setState({
            advisorNames: ['All'].concat(
              result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.map(
                e =>
                  e
                    ? (e.nickname ? e.nickname : e.name) + '-status-' + e.active
                    : ''
              )
            )
          });
        }
      });
    }
  };
  redirectToHome = () => {
    console.log('cccc1', this.props, parentId);
    const { history } = this.props;
    let chartLoc;
    let parentId = history.location.search.split('?chartId=').pop();
    const showCurrentMonth = localStorage.getItem('showCurrentMonth');
    if (this.setChartType(parentId)) {
      chartLoc = 'PartsWorkMixAnalysis';
    } else {
      chartLoc = 'LaborWorkMixAnalysis';
    }
    let searchText = history.location.search
      .split('?title=')
      .pop()
      .split('?')[0];
    let tabSelection = history.location.state
      ? history.location.state.tabSelection
      : '';
    let SelectedLocation = history.location.state
      ? history.location.state.SelectedLocation
      : '';

    if (tabSelection && history.location.state.selectedSubTab) {
      history.push({
        pathname:
          SelectedLocation != ''
            ? SelectedLocation
            : localStorage.getItem('selectedLocation'),
        state: {
          tabSelection: history.location.state
            ? history.location.state.tabSelection
            : 'one',
          selectedSubTab: history.location.state
            ? history.location.state.selectedSubTab
            : 'one',
          datatype: history.location.state.datatype,
          month_year: history.location.state.month2
            ? history.location.state.month2
            : history.location.state.month1,
          prevMonth: history.location.state.month1,
          parent: history.location.state.parent
            ? history.location.state.parent
            : '',
          techNo: history.location.state.tech
            ? history.location.state.tech
            : '',
          techName: history.location.state.techName
            ? history.location.state.techName
            : ''
        }
      });
    } else if (
      (parentId >= 1264 && parentId <= 1275) ||
      parentId == 1349 ||
      parentId == 1350
    ) {
      console.log('ccc--2');
      history.push({
        pathname: '/TechnicianPerformance',
        state: {
          tabSelection: 'seven',
          isFrom: 'drillDown',
          selectedSubTab: 'two',
          comparisonMonth1: history.location.state
            ? history.location.state.month1
            : getComparisonMonths()[0],
          comparisonMonth2: history.location.state
            ? history.location.state.month2
            : getComparisonMonths()[1]
        }
      });
    } else if (
      (parentId >= 1259 && parentId <= 1263) ||
      (parentId >= 1309 && parentId <= 1313)
    ) {
      console.log('ccc--3');
      history.push({
        pathname: '/' + chartLoc,
        state: {
          tabSelection: 'two',
          isFrom: 'drillDown',
          drillDownType: 'workmix',
          comparisonMonth1: history.location.state
            ? history.location.state.month2
            : getComparisonMonths()[1],
          comparisonMonth2: history.location.state
            ? history.location.state.month1
            : getComparisonMonths()[0]
        }
      });
    } else if (
      parentId == 1114 ||
      parentId == 1125 ||
      parentId == 1124 ||
      parentId == 1126 ||
      parentId == 1118 ||
      parentId == 1119 ||
      parentId == 1120 ||
      parentId == 1121 ||
      parentId == 1122
    ) {
      console.log(
        'ccc--4',
        localStorage.getItem('selectedLocation'),
        history.location.state,
        history
      );
      history.push({
        pathname: localStorage.getItem('selectedLocation'),
        state: {
          tabSelection: 'two',
          discountComparisonMonths:
            history.location.state &&
            history.location.state.month1 &&
            history.location.state.month2
              ? [history.location.state.month1, history.location.state.month2]
              : history.location.state &&
                history.location.state.discountComparisonMonths
              ? history.location.state.discountComparisonMonths
              : [getComparisonMonths()[1], getComparisonMonths()[0]]
        }
      });
    } else if (showCurrentMonth != undefined && showCurrentMonth == true) {
      console.log('ccc--5');
      history.push({
        pathname: localStorage.getItem('selectedLocation'),
        state: {
          showCurrentMonth: showCurrentMonth
        }
      });
    } else {
      if (
        localStorage.getItem('itemTab') &&
        this.props.history.location &&
        this.props.history.location.state &&
        this.props.history.location.state.parent != 'Home'
      ) {
        console.log('ccc--6', this.props.history.location.isFrom);
        if (
          parentId == 1352 ||
          parentId == 1345 ||
          parentId == 1348 ||
          parentId == 1347 ||
          parentId == 1363
        ) {
          history.push(localStorage.getItem('selectedLocation'));
        } else if (
          this.props.history.location.isFrom &&
          this.props.history.location.isFrom == 'opportunity'
        ) {
          history.push({
            pathname: localStorage.getItem('selectedLocation'),
            handleHighlight: this.props.history.location.handleHighlight,
            selectedGrid: this.props.history.location.selectedGrid,
            isFrom: this.props.history.location.isFrom
              ? this.props.history.location.isFrom
              : '',
            prevPath: this.props.history.location.prevPath
              ? this.props.history.location.prevPath
              : this.props.history.location.userhistory
              ? this.props.history.location.userhistory
              : '',
            state: {
              tabSelection:
                this.props.history.location.isFrom &&
                this.props.history.location.isFrom == 'opportunity'
                  ? 'one'
                  : ''
            }
          });
        } else {
          history.push({
            pathname: localStorage.getItem('selectedLocation'),
            state: {
              tabSelection:
                tabSelection &&
                this.props.history.location.isFrom == 'workmixAdvisor'
                  ? tabSelection
                  : this.props.history.location.state.tabSelection == 'seven'
                  ? 'seven'
                  : localStorage.getItem('itemTab'),
              data:
                this.props.history &&
                this.props.history.location &&
                this.props.history.location.state &&
                this.props.history.location.state.data
                  ? this.props.history.location.state.data
                  : [],
              isFrom: 'detail'
            },
            handleHighlight: this.props.history.location.handleHighlight,
            selectedGrid: this.props.history.location.selectedGrid
          });
        }
      } else if (
        this.props.history.location.state &&
        this.props.history.location.state.parent
      ) {
        console.log('ccc--13', localStorage.getItem('selectedLocation'));
        history.push({
          pathname: localStorage.getItem('selectedLocation'),
          state: {
            parent: this.props.history.location.state.parent,
            selectedToggle: this.props.history.location.state.selectedToggle
          }
        });
      } else {
        console.log(
          'ccc--12',
          this.props,
          localStorage.getItem('selectedLocation')
        );
        history.push({
          pathname: localStorage.getItem('selectedLocation'),
          handleHighlight: this.props.history.location.handleHighlight,
          selectedGrid: this.props.history.location.selectedGrid,
          isFrom: this.props.history.location.isFrom
            ? this.props.history.location.isFrom
            : '',
          prevPath: this.props.history.location.prevPath
            ? this.props.history.location.prevPath
            : this.props.history.location.userhistory
            ? this.props.history.location.userhistory
            : '',
          state: {
            tabSelection:
              this.props.history.location.isFrom &&
              this.props.history.location.isFrom == 'opportunity'
                ? 'one'
                : localStorage.getItem('selectedLocation') ==
                  '/TechnicianPerformance'
                ? 'seven'
                : '',
            chartType: this.props.history.location.chartType
              ? this.props.history.location.chartType
              : ''
          }
        });
      }
    }
  };
  setChartTitle(param) {
    var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
    let filteredResult = chartList.filter(item => item.chartId == param);

    return filteredResult.length > 0 ? filteredResult[0].chartName : '';
  }
  setChartType(param) {
    var chartList = JSON.parse(global.localStorage.getItem('chart-master'));
    let filteredResult = chartList.filter(item => item.chartId == param);

    return filteredResult.length > 0 &&
      filteredResult[0].dbdName.indexOf('Parts') > -1
      ? true
      : false;
  }
  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }
    return this.state.layout;
  };

  onLayoutChange(layout) {
    var parentId = this.props.history.location.search.split('?chartId=').pop();
    if (
      parentId != 1121 &&
      parentId != 1122 &&
      parentId != 1114 &&
      parentId != 1125 &&
      parentId != 1264 &&
      parentId != 1265
    ) {
      saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-13');
    } else {
      saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-20');
    }
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }

  showCurrentMonth = value => {
    this.setState({
      showCurrentMonth: value
    });
    return this.state.showCurrentMonth;
  };

  render() {
    const { classes } = this.props;
    let realm = this.props.keycloak.realm;
    let searchText = this.props.history.location.search
      .split('?title=')
      .pop()
      .split('?')[0];
    var selectedLocation = this.props.history.location.SelectedLocation;
    var parentId = this.props.history.location.search.split('?chartId=').pop();
    let advisorName = '';
    if (parentId == 931 || parentId == 926 || parentId == 921) {
      if (
        this.state.names &&
        this.state.names.length > 0 &&
        this.state.advisorNames &&
        this.state.advisorNames.length > 0
      ) {
        this.props.session.kpiAdvisor.map(data => {
          if (this.state.names.indexOf(data) > -1 === true) {
            advisorName =
              this.state.advisorNames[this.state.names.indexOf(data)].split(
                '-status-'
              )[0] +
              ' [' +
              data +
              ']';
          } else {
            advisorName = data;
          }
        });
      }
    }
    var month1 =
      this.props.history.location.state &&
      this.props.history.location.state.month1
        ? this.props.history.location.state.month1
        : this.props.history.location.state &&
          this.props.history.location.state.discountComparisonMonths
        ? this.props.history.location.state.discountComparisonMonths[0]
        : '';

    var month2 =
      this.props.history.location.state &&
      this.props.history.location.state.month2
        ? this.props.history.location.state.month2
        : this.props.history.location.state &&
          this.props.history.location.state.discountComparisonMonths
        ? this.props.history.location.state.discountComparisonMonths[1]
        : '';

    var datatype = this.props.history.location.state
      ? this.props.history.location.state.datatype
      : '';
    var timeZone = this.props.history.location.state
      ? this.props.history.location.state.timeZone
      : '+05:30';
    var tech =
      this.props.history.location.state &&
      this.props.history.location.state.tech
        ? this.props.history.location.state.tech
        : '';
    var techName =
      this.props.history.location.state &&
      this.props.history.location.state.techName
        ? this.props.history.location.state.techName
        : '';
    if (parentId == 1090) {
      $('#Scatter-Plot-Labor').addClass('active-menu');
    } else if (parentId == 931) {
      $('#navBarDiv')
        .find("a[id='what-if-Labor']")
        .addClass('active-menu');
    } else if (parentId == 921) {
      $('#what-if-elr').addClass('active-menu');
    } else if (parentId == 1096) {
      $('#Scatter-Plot-Parts').addClass('active-menu');
    } else if (parentId == 926) {
      //  var element = document.getElementById('what-if-parts');

      $('#what-if-parts').addClass('active-menu');
    }
    if (selectedLocation && selectedLocation === '/LaborWorkMixAnalysis') {
      var element = document.getElementById('Labor Work Mix');
      element.classList.add('active-menu');
    }
    if (selectedLocation && selectedLocation === '/PartsWorkMixAnalysis') {
      var element = document.getElementById('Parts Work Mix');
      element.classList.add('active-menu');
    }
    if (selectedLocation && selectedLocation === '/Discounts') {
      var element = document.getElementById('Discount Metrics');
      element.classList.add('active-menu');
    }
    if (selectedLocation && selectedLocation === '/TechnicianPerformance') {
      var element = document.getElementById('Tech Metrics');
      element.classList.add('active-menu');
    }
    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          <PageHeader
            title={this.setChartTitle(parentId)}
            setResetDashboard={this.setResetDashboard}
            showCurrentMonth={this.showCurrentMonth}
            redirectHome={this.redirectToHome}
            isFrom={'details'}
            parent={
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.parent
                ? this.props.history.location.state.parent
                : ''
            }
            advisorName={advisorName}
          />

          <Divider />
          {/* <DashboardDetailsActions
          resetDashboard={this.setResetDashboard}
          redirectHome={this.redirectToHome}
          showCurrentMonth={this.showCurrentMonth}
          setTitle={this.setChartTitle(parentId)}
        ></DashboardDetailsActions> */}
          {parentId != 1121 &&
          parentId != 1122 &&
          parentId != 1114 &&
          parentId != 1125 &&
          parentId != 1089 &&
          parentId != 1259 &&
          parentId != 1260 &&
          parentId != 1261 &&
          parentId != 1262 &&
          parentId != 1263 &&
          parentId != 1264 &&
          parentId != 1265 &&
          parentId != 1309 &&
          parentId != 1310 &&
          parentId != 1311 &&
          parentId != 1312 &&
          parentId != 1313 &&
          parentId != 1095 &&
          parentId != 1090 &&
          parentId != 1096 &&
          parentId != 931 &&
          parentId != 1352 &&
          parentId != 1363 &&
          parentId != 926 &&
          parentId != 921 ? (
            <GraphDetailsAction
              showLoader={true}
              parentId={parentId}
              month1={month1}
              month2={month2}
              datatype={datatype}
              layout={this.state.layout}
              onLayoutChange={this.onLayoutChange}
              realm={realm}
              showCurrentMonth={this.state.showCurrentMonth}
              handleHighlight={this.props.history.location.handleHighlight}
              selectedGrid={this.props.history.location.selectedGrid}
              parent={
                this.props.history.location &&
                this.props.history.location.state &&
                this.props.history.location.state.parent
                  ? this.props.history.location.state.parent
                  : ''
              }
              selectedToggle={
                this.props.history.location &&
                this.props.history.location.state &&
                this.props.history.location.state.selectedToggle
                  ? this.props.history.location.state.selectedToggle
                  : ''
              }
              userhistory={
                this.props.history.location &&
                this.props.history.location.userhistory
                  ? this.props.history.location.userhistory
                  : ''
              }
            ></GraphDetailsAction>
          ) : (
            // <ReactGridLayout
            //   {...this.props}
            //   layout={this.state.layout}
            //   onLayoutChange={this.onLayoutChange}
            //   isResizable={false}
            // >
            <div>
              <div
                className={clsx(
                  'diagram-section',
                  parentId == 1090 || parentId == 1096
                    ? 'diagram-section-scatter-details'
                    : 'diagram-section-custom'
                )}
                key={1}
                data-grid={{
                  x: 0,
                  y: 0,
                  w: 12,
                  h:
                    parentId == 931 || parentId == 926
                      ? 4
                      : parentId == 1090 || parentId == 1096 || parentId == 921
                      ? 5
                      : 4,
                  minW: 5,
                  minH: 7,
                  isResizable: false,
                  isDraggable:
                    parentId == 1090 || parentId == 1096 ? false : true
                }}
              >
                {<GraphDetails />}
              </div>
              <div
                className={clsx(
                  'diagram-section',
                  parentId == 1090 || parentId == 1096
                    ? 'diagram-section-scatter'
                    : ''
                )}
                key={2}
                data-grid={{
                  x: 0,
                  y: 0,
                  w: 12,
                  h: 9,
                  minW: 5,
                  minH: 7,
                  isResizable: false,
                  isDraggable:
                    parentId == 1090 || parentId == 1096 ? false : true
                }}
              >
                {parentId == 1089 ? (
                  <CPMovingELR />
                ) : parentId == 1095 ? (
                  <CPMovingPartsMarkup history={this.props.history} />
                ) : parentId == 1090 ? (
                  <CPELRVsLaborSoldHours
                    parent={'Details'}
                    session={this.props.session}
                    tabSelection={localStorage.getItem('itemTab')}
                    timeZone={timeZone}
                  />
                ) : parentId == 1096 ? (
                  <CPPartsMarkupVsPartsCost
                    parent={'Details'}
                    session={this.props.session}
                    tabSelection={localStorage.getItem('itemTab')}
                    timeZone={timeZone}
                  />
                ) : parentId == 1264 || parentId == 1265 ? (
                  <TechnicianMonthComparison
                    datatype={datatype}
                    month1={month1}
                    month2={month2}
                    callFrom={1}
                    chartId={parentId}
                    session={this.props.session}
                  />
                ) : parentId == 931 ? (
                  <LaborGrossAndVolumeOpportunity
                    chartId={931}
                    isFrom={'details'}
                  />
                ) : parentId == 926 ? (
                  <LaborGrossAndVolumeOpportunity
                    chartId={926}
                    isFrom={'details'}
                  />
                ) : parentId == 921 ? (
                  <LaborGrossAndVolumeOpportunity
                    chartId={921}
                    isFrom={'details'}
                    // tabSelection={localStorage.getItem('itemTab')}
                  />
                ) : parentId == 1352 || parentId == 1363 ? (
                  <BarChartRendererPostgraphile
                    chartId={parentId}
                    session={this.props.session}
                    isFrom={'details'}
                    tech={tech}
                    techName={techName}
                    // chartTitle={chartName}
                  />
                ) : (
                  <HighCharts
                    isPartsCharts={this.setChartType(parentId)}
                    datatype={datatype}
                    chartId={Number(parentId)}
                    month1={month1}
                    month2={month2}
                    isFrom={'details'}
                  />
                )}
              </div>
            </div>
          )}
        </Paper>
      </div>
    );
  }
}
const styles = theme => ({
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  root: {
    flexGrow: 1,
    width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    //textAlign: 'center',
    color: theme.palette.text.secondary
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    //justifyContent: 'space-between',
    width: '100%'
  }
});

export default withStyles(styles)(withKeycloak(Charts));
