import React from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';

import Charts from './Charts';

const useStyles = makeStyles(theme => ({
  root: {
    padding: 12
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function PartsTranches() {
  const classes = useStyles();
  const history = useHistory();

  return (
    <Page className={classes.root} title="Parts Tranches">
      <Charts history={history} />
    </Page>
  );
}

export default PartsTranches;
