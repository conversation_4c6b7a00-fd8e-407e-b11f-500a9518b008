import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  Paper,
  Divider,
  Tab,
  Tabs,
  Typography,
  CircularProgress,
  IconButton,
  Tooltip,
  Grid
} from '@material-ui/core';
import moment from 'moment';
import clsx from 'clsx';

import RestoreIcon from '@material-ui/icons/Restore';
import { withStyles } from '@material-ui/styles';
import CPMovingPartsMarkup from 'src/components/charts/CPMovingPartsMarkup';
import DashboardActions from 'src/components/DashboardActions';
import { getLatestClosedDate } from 'src/utils/hasuraServices';
import 'src/styles.css';

import PageHeader from 'src/components/PageHeader';

const originalLayout = getFromLS('layout') || {};
const ReactGridLayout = WidthProvider(RGL);

class Charts extends React.PureComponent {
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);

    this.state = {
      layout: JSON.parse(JSON.stringify(originalLayout))
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }

  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }
    return this.state.layout;
  };

  onLayoutChange(layout) {
    /*eslint no-console: 0*/
    saveToLS('layout', layout);
    this.setState({ layout });
    this.props.onLayoutChange(layout);
  }
  handleClosePopup = value => {
    console.log('state===handleClosePopup');
    this.setState({
      popupChartId: '',
      open: false
    });
  };
  render() {
    const { classes } = this.props;
    return (
      <div className={classes.root}>
        <Paper className={classes.paper}>
          <PageHeader
            title={'Parts Tranches'}
            closedDate={this.state.closedDate}
            setResetDashboard={this.setResetDashboard}
          />

          <Divider />
          <div className={clsx(classes.container, 'diagram-section')}>
            <CPMovingPartsMarkup
              history={this.props.history}
              handleClosePopup={this.handleClosePopup}
            />
          </div>
        </Paper>
      </div>
    );
  }
}
function getFromLS(key) {
  let ls = {};
  if (global.localStorage) {
    try {
      ls = JSON.parse(global.localStorage.getItem('fixed-ops-layout-8')) || {};
    } catch (e) {
      /*Ignore*/
    }
  }
  return ls[key];
}

function saveToLS(key, value) {
  if (global.localStorage) {
    global.localStorage.setItem(
      'fixed-ops-layout-8',
      JSON.stringify({
        [key]: value
      })
    );
  }
}
const styles = theme => ({
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  dividerStyle: {
    marginLeft: 0,
    marginRight: 0
  },
  root: {
    flexGrow: 1,
    width: '99%'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: '5px 16px',
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  paperSub: {
    boxShadow: '0 3px 2px -2px #8080802b'
    // marginTop: '20px'
  },
  container: {
    alignItems: 'center',
    margin: '5px 0px'
    //width: '85%',
  },
  tabs: {
    // "& button[aria-selected='true']": {
    //   border: "5px solid red"
    // }
    '& button': {
      padding: 5
    },
    "& button[aria-selected='true']": {
      color: theme.palette.primary.main,
      textTransform: 'none',
      border: 'solid 1px',
      borderColor: theme.palette.primary.main,
      backgroundColor: theme.palette.primary.active
      // backgroundColor: theme.palette.primary.active
    }
  },
  dataLabel: {
    color: theme.palette.secondary.light,
    fontWeight: 'bold',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: 12
    },
    '@media (min-width: 2304px)': {
      fontSize: 17
    }
  },
  sublLabel: {
    display: 'flex'
  },
  resetButton: {
    float: 'right'
  }
});
export default withStyles(styles)(Charts);
