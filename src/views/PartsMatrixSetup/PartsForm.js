import React, { useRef } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { Formik, Form, Field, ErrorMessage, useFormikContext } from 'formik';
import * as Yup from 'yup';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import { DropzoneArea } from 'material-ui-dropzone';
import AddIcon from '@material-ui/icons/Add';
import { forwardRef, useImperativeHandle } from 'react';
import Autocomplete from '@material-ui/lab/Autocomplete';
import {
  Box,
  Button,
  Grid,
  LinearProgress,
  Paper,
  Tooltip,
  Divider,
  Typography,
  MenuItem,
  Select,
  ListItemText,
  IconButton,
  Checkbox
} from '@material-ui/core';
import { TextField, CardContent } from '@material-ui/core';
import { useState } from 'react';
import clsx from 'clsx';
import styled from 'styled-components';
import { CheckBox } from '@material-ui/icons';
import CreateMatrixName from './CreateMatrixName';
import { useEffect } from 'react';
import CSVDownload from 'src/components/CSVDownload';
import moment from 'moment';

const useStyles = makeStyles(theme => ({
  customDropzone: {},

  dropzoneImage: {
    width: '30px',
    height: '30px',
    objectFit: 'cover',
    borderRadius: '50%'
  },
  snackbarContainer: {
    '& .MuiSnackbar-root': {
      top: '30px',
      bottom: 'unset',
      left: '50%',
      transform: 'translateX(-50%)'
    }
  },
  root: {
    border: '2px dashed #ccc',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center',
    // width: '90% !important'
    height: '10% !important'
  },

  fileError: {
    border: '2px dashed #e53935',
    borderRadius: '8px',
    padding: '20px',
    textAlign: 'center'
    // width: '90% !important'
  },
  icon: {
    fontSize: '48px',
    marginBottom: '-8px !important',
    marginTop: '15px !important',
    height: 30
  },
  partsMatrixs: {
    '@media (min-width: 2560px)': {
      marginLeft: '-8% !important'
    },
    '@media (min-width: 2305px) and (max-width: 2559px)': {
      marginLeft: '-5% !important'
    },
    '@media (min-width: 1920px) and (max-width: 2304px)': {
      marginLeft: '-4% !important'
    },
    '@media (min-width: 1601px) and (max-width: 1919px)': {
      paddingLeft: '0% !important'
    },
    '@media (max-width: 1600px)': {
      paddingLeft: '10% !important'
    }
  },
  editBtn: {
    paddingRight: '55px !important',
    '@media (min-width: 1366px)': { paddingRight: '35px !important' },
    '@media (min-width: 1420px)': { paddingRight: '38px !important' },
    '@media (min-width: 1440px)': { paddingRight: '55px !important' },
    '@media (min-width: 2048px)': { paddingRight: '62px !important' },
    '@media (min-width: 2240px)': { paddingRight: '71px !important' },
    '@media (min-width: 2304px)': { paddingRight: '75px !important' },
    '@media (min-width: 2560px)': { paddingRight: '85px !important' },
    '@media (min-width: 3200px)': { paddingRight: '110px !important' },
    '@media (min-width: 3840px)': { paddingRight: '137px !important' }
  },
  editBtnOpCode: {
    paddingRight: '186px !important',
    '@media (max-width: 1280px)': {
      paddingRight: '100px !important'
    },
    '@media (min-width: 1281px) and (max-width: 1366px)': {
      paddingRight: '112px !important'
    },
    '@media (min-width: 1367px) and (max-width: 1440px)': {
      paddingRight: '125px !important'
    },
    '@media (min-width: 1920px)': {
      paddingRight: '200px !important'
    },
    '@media (min-width: 1921px) and (max-width: 2303px)': {
      paddingRight: '260px !important'
    },
    '@media (min-width: 2048px)': { paddingRight: '220px !important' },
    '@media (min-width: 2240px)': {
      paddingRight: '250px !important'
    },
    '@media (min-width: 2304px)': {
      paddingRight: '261px !important'
    },
    '@media (min-width: 2560px)': { paddingRight: '300px !important' },
    '@media (min-width: 3200px)': { paddingRight: '400px !important' },
    '@media (min-width: 3840px)': { paddingRight: '500px !important' }
  }
}));

const FormErrorMsg = styled.div`
  color: red;
  font-size: 12px;
  text-overflow: ellipsis;
  max-width: 100%;
  padding-top: 10px;
`;

const PartsForm = forwardRef((props, ref) => {
  const {
    handleSubmitForm,
    disableSubmitButton,
    setAddGrid,
    type,
    component,
    matrixSourceList,
    matrixTypeList,
    opcodeListChoice,
    fetchPartsMatrixRowData,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    action,
    enableSave,
    cancel,
    rowData,
    gridTypeChoice,
    getGridTypesList,
    defaultGridType,
    hasDefaultType,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    item,
    handleAlert,
    setIsEditFormValueChanged,
    isEditFormValueChanged
  } = props;

  const MatrixTypeChoice = [
    { id: 'Opcode', name: 'Opcode' },
    { id: 'Source Matrix', name: 'Source Matrix' }
  ];
  const [showCustomSourceInput, setShowCustomSourceInput] = useState(false);
  const [invalidDateMsg, setInvalidDateMsg] = useState('');
  const [selectedMatrixType, setSelectedMatrixType] = useState('');
  const [selectAll, setSelectAll] = useState(false);
  const classes = useStyles();
  const [localSource, setLocalSource] = useState([]);
  const [matrixName, setMatrixName] = useState('');
  const [submitFlag, setsubmitFlag] = useState(false);

  const formikRef = useRef(null);

  useImperativeHandle(ref, () => ({
    refresh: () => {
      if (formikRef.current) {
        formikRef.current.resetForm();
        setsubmitFlag(false);
        setSelectedMatrixType('');
        formikRef.current.setFieldValue('source', []);
        formikRef.current.setFieldValue('opcode', '');
        setLocalSource([]);
        setSelectAll(false);
      }
    }
  }));

  const validateInput = value => {
    if (!value) {
      return undefined; // Field is not required
    }
    if (value.length > 30) {
      return `Maximum 30 characters allowed. You entered ${value.length} characters.`;
    }

    const values = value.split(',').map(v => v.trim());
    const isAlreadyInList = values.some(val =>
      matrixSourceList.some(item => item.name === val)
    );
    if (isAlreadyInList) {
      return 'Please select from the source list. Item is already in the list.';
    }

    if (value.includes(' ')) {
      return 'No spaces allowed';
    }
    const items = value.split(',');
    const uniqueItems = new Set(items.map(item => item.toLowerCase()));
    if (items.length !== uniqueItems.size) {
      return 'No duplicate items allowed';
    }
    const isValid = /^[0-9a-zA-Z,]*$/.test(value);
    if (!isValid) {
      return 'Invalid format. Only numbers, alphabets, and commas are allowed';
    }
    return undefined;
  };

  const validationSchema = Yup.object({
    matrixType: Yup.mixed().test('matrixType', 'This is required!', value => {
      return action == 'edit' || (value && value.length > 0);
    }),
    opcode: Yup.mixed().test('opcode', 'This is required!', value => {
      return selectedMatrixType !== 'Opcode' || (value && value.length > 0);
    }),

    mType: Yup.mixed().test('mType', 'This is required!', value => {
      return (
        selectedMatrixType !== 'Source Matrix' || (value && value.length > 0)
      );
    }),
    source: Yup.array()
      .of(Yup.string())
      .test('source', 'This is required!', function(value) {
        const customSource = this.parent && this.parent.customSource;
        return (
          selectedMatrixType === 'Opcode' ||
          (customSource && customSource.length > 0) ||
          (value && value.length > 0) ||
          localStorage.getItem('dms') == 'rey' ||
          localStorage.getItem('dms') == 'tek'
        );
      }),
    customSource: Yup.string().test('customSource', function(value) {
      const error = validateInput(value);
      return error ? this.createError({ message: error }) : true;
    }),
    installDate: Yup.date().required('This is required!'),
    csvFile: Yup.mixed().test('csvFile', 'This is required!', value => {
      return type !== 'upload' || (value && value.length > 0);
    })
  });

  const handleFileChange = async (files, setFieldValue) => {
    setFieldValue('csvFile', files);
  };

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setSubmitting(true);
    try {
      const updatedValues = { ...values };
      if (updatedValues.matrixType === 'Opcode') {
        updatedValues.source = ['All'];
      }
      await handleSubmitForm(updatedValues);
    } catch (error) {
      console.error('Failed to submit form:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleMatrixNameCreated = name => {
    setMatrixName(name);
    console.log('Matrix Name Created:', name);
  };

  useEffect(() => {
    if (item && item.prtsource) {
      setLocalSource(item.prtsource);
      setSelectAll(checkIfAllSelected(item.prtsource));
    }
  }, [item]);

  const handleSelectAllChange = (event, setFieldValue) => {
    const { checked } = event.target;
    setSelectAll(checked);
    const newSource = checked
      ? matrixSourceList.map(option => option.name)
      : [];
    setLocalSource(newSource);
    setFieldValue('source', newSource);
  };

  const handleMultiSelectChange = (event, setFieldValue) => {
    const { value } = event.target;
    setLocalSource(value);
    setFieldValue('source', value);
    setSelectAll(checkIfAllSelected(value));
  };

  const checkIfAllSelected = selectedValues => {
    const allOptionNames = matrixSourceList.map(option => option.name);
    return allOptionNames.every(option => selectedValues.includes(option));
  };
  return (
    <>
      <CardContent style={{ overflowY: 'auto' }}>
        <Formik
          innerRef={formikRef}
          initialValues={{
            matrixType: '',
            mType: '',
            opcode: '',
            source: item && item.prtsource ? item.prtsource : [],
            customSource: '',
            installDate:
              item && item.storeInstallDate
                ? moment(item.storeInstallDate).format('MM-DD-YYYY')
                : '',
            csvFile: ''
          }}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting, values, setFieldValue }) => (
            <Form>
              <Grid container spacing={2}>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Matrix Type <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <Field
                    name="matrixType"
                    as={Select}
                    variant="outlined"
                    size="small"
                    fullWidth
                    onChange={e => {
                      setFieldValue('matrixType', e.target.value);
                      setFieldValue('source', []);
                      setFieldValue('opcode', '');
                      setSelectedMatrixType(e.target.value);
                    }}
                    style={{ height: '40px' }}
                    MenuProps={{
                      getContentAnchorEl: null, // Ensures the menu is aligned based on `anchorOrigin`
                      anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      PaperProps: {
                        style: {
                          marginTop: '50px' // Adjust the top margin
                        }
                      }
                    }}
                  >
                    <MenuItem disabled value="">
                      Select Matrix Type
                    </MenuItem>
                    {MatrixTypeChoice &&
                      MatrixTypeChoice.map(type => (
                        <MenuItem key={type.id} value={type.name}>
                          {type.name}
                        </MenuItem>
                      ))}
                  </Field>
                  {submitFlag && (
                    <ErrorMessage name="matrixType" component={FormErrorMsg} />
                  )}
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: selectedMatrixType === 'Opcode' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Opcodes <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: selectedMatrixType === 'Opcode' ? 'block' : 'none'
                  }}
                >
                  <Field
                    name="opcode"
                    render={({ field, form }) => (
                      <Autocomplete
                        options={opcodeListChoice}
                        getOptionLabel={option => option.name}
                        onChange={(event, value) => {
                          form.setFieldValue(
                            field.name,
                            value ? value.name : ''
                          );
                        }}
                        value={
                          (opcodeListChoice &&
                            opcodeListChoice.find(
                              option => option.name === field.value
                            )) ||
                          null
                        }
                        renderInput={params => (
                          <TextField
                            {...params}
                            placeholder="Select Opcode"
                            variant="outlined"
                            size="small"
                            fullWidth
                            style={{ height: '40px' }}
                            MenuProps={{
                              PaperProps: {
                                style: {
                                  maxHeight: '350px'
                                }
                              }
                            }}
                          />
                        )}
                      />
                    )}
                  />
                  {submitFlag && (
                    <ErrorMessage name="opcode" component={FormErrorMsg} />
                  )}
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display:
                      selectedMatrixType === 'Source Matrix' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Matrix Name <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display:
                      selectedMatrixType === 'Source Matrix' ? 'block' : 'none'
                  }}
                  Matrix
                  Name
                >
                  <Field
                    name="mType"
                    as={Select}
                    variant="outlined"
                    size="small"
                    fullWidth
                    style={{ height: '40px' }}
                    MenuProps={{
                      getContentAnchorEl: null, // Ensures the menu is aligned based on `anchorOrigin`
                      anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      PaperProps: {
                        style: {
                          marginTop: '50px', // Adjust the top margin
                          maxHeight: '350px'
                        }
                      }
                    }}
                  >
                    <MenuItem disabled value="">
                      Select Matrix Name
                    </MenuItem>
                    {matrixTypeList.map(type => (
                      <MenuItem key={type.id} value={type.name}>
                        {type.name}
                      </MenuItem>
                    ))}
                  </Field>
                  {submitFlag && (
                    <ErrorMessage name="mType" component={FormErrorMsg} />
                  )}
                </Grid>
                <Grid
                  xs={12}
                  sm={1}
                  style={{
                    display:
                      selectedMatrixType === 'Source Matrix' ? 'block' : 'none'
                  }}
                >
                  <CreateMatrixName
                    fetchPartsMatrixRowData={fetchPartsMatrixRowData}
                    onMatrixNameCreated={name => {
                      handleMatrixNameCreated(name);
                      setFieldValue('mType', name);
                    }}
                  />
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display:
                      ((action !== 'edit' &&
                        selectedMatrixType == 'Source Matrix') ||
                        (action == 'edit' && item.partsFor == 'source')) &&
                      localStorage.getItem('dms') !== 'rey' &&
                      localStorage.getItem('dms') !== 'tek'
                        ? 'block'
                        : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Source <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display:
                      ((action !== 'edit' &&
                        selectedMatrixType == 'Source Matrix') ||
                        (action == 'edit' && item.partsFor == 'source')) &&
                      localStorage.getItem('dms') !== 'rey' &&
                      localStorage.getItem('dms') !== 'tek'
                        ? 'block'
                        : 'none'
                  }}
                >
                  <Field
                    style={{ height: '40px' }}
                    name="source"
                    as={Select}
                    multiple
                    value={localSource}
                    onChange={e => handleMultiSelectChange(e, setFieldValue)}
                    variant="outlined"
                    size="small"
                    fullWidth
                    renderValue={selected => selected.join(', ')}
                    MenuProps={{
                      getContentAnchorEl: null, // Ensures the menu is aligned based on `anchorOrigin`
                      anchorOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      transformOrigin: {
                        vertical: 'top',
                        horizontal: 'left'
                      },
                      PaperProps: {
                        style: {
                          marginTop: '50px', // Adjust the top margin
                          maxHeight: '350px'
                        }
                      }
                    }}
                  >
                    {matrixSourceList.map(type => (
                      <MenuItem key={type.id} value={type.name}>
                        <ListItemText primary={type.name} />
                      </MenuItem>
                    ))}
                  </Field>
                  {submitFlag && (
                    <ErrorMessage name="source" component={FormErrorMsg} />
                  )}
                  <Checkbox
                    checked={selectAll}
                    onChange={e => handleSelectAllChange(e, setFieldValue)}
                    color="primary"
                  />
                  <Typography variant="body2">Select All</Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display:
                      ((action !== 'edit' &&
                        selectedMatrixType == 'Source Matrix') ||
                        (action == 'edit' && item.partsFor == 'source')) &&
                      localStorage.getItem('dms') !== 'rey' &&
                      localStorage.getItem('dms') !== 'tek'
                        ? 'block'
                        : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Add Custom Source
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display:
                      ((action !== 'edit' &&
                        selectedMatrixType == 'Source Matrix') ||
                        (action == 'edit' && item.partsFor == 'source')) &&
                      localStorage.getItem('dms') !== 'rey' &&
                      localStorage.getItem('dms') !== 'tek'
                        ? 'block'
                        : 'none'
                  }}
                >
                  <Field
                    name="customSource"
                    as={TextField}
                    variant="outlined"
                    size="small"
                    fullWidth
                    style={{ height: '40px' }}
                    onChange={e => {
                      let value = e.target.value;
                      // Replace multiple spaces with a single space
                      value = value.replace(/\s{2,}/g, ' ');
                      // Trim leading spaces
                      if (value[0] === ' ') {
                        value = value.substring(1);
                      }
                      setFieldValue('customSource', value);
                    }}
                  />
                  {submitFlag && (
                    <ErrorMessage
                      name="customSource"
                      component={FormErrorMsg}
                    />
                  )}
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Store Install Date <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{
                    display: action !== 'edit' ? 'block' : 'none'
                  }}
                >
                  <DateRangePicker
                    key={
                      values.installDate
                        ? moment(values.installDate).format('MM/DD/YY')
                        : moment().format('MM/DD/YY')
                    }
                    onApply={(e, picker) => {
                      setFieldValue(
                        'installDate',
                        picker.startDate.format('MM/DD/YY')
                      );
                    }}
                    initialSettings={{
                      locale: {
                        format: 'MM/DD/YY',
                        separator: ' - '
                      },
                      autoUpdateInput: true,
                      showDropdowns: true,
                      autoApply: true,
                      singleDatePicker: true,
                      startDate: values.installDate
                        ? moment(values.installDate).format('MM/DD/YY')
                        : moment().format('MM/DD/YY')
                    }}
                  >
                    <div className="custom-date-container">
                      <Field
                        name="installDate"
                        as={TextField}
                        variant="outlined"
                        size="small"
                        fullWidth
                        value={
                          values.installDate
                            ? moment(values.installDate).format('MM/DD/YY')
                            : ''
                        }
                        inputProps={{ readOnly: true }}
                        style={{ height: '40px' }}
                      />
                    </div>
                  </DateRangePicker>
                  {submitFlag && (
                    <ErrorMessage name="installDate" component={FormErrorMsg} />
                  )}
                </Grid>

                <Grid
                  item
                  xs={12}
                  sm={4}
                  style={{ display: type === 'upload' ? 'block' : 'none' }}
                >
                  <Typography
                    variant="body1"
                    style={{
                      fontSize: '13px',
                      padding: '15px 0px 0px 10px',
                      color: 'rgb(0, 61, 107)'
                    }}
                  >
                    Upload CSV File <span style={{ color: 'red' }}>*</span>
                  </Typography>
                </Grid>
                <Grid
                  item
                  xs={12}
                  sm={7}
                  style={{ display: type === 'upload' ? 'block' : 'none' }}
                  className={classes.snackbarContainer}
                >
                  <DropzoneArea
                    name="csvFile"
                    acceptedFiles={['.csv']}
                    maxFiles={1}
                    filesLimit={1}
                    dropzoneText={'Drag and drop file or click to upload'}
                    showAlerts={['error']}
                    dropzoneClass="matrix-upload"
                    showFileNames
                    classes={{
                      root: classes.root,
                      icon: classes.icon
                    }}
                    onChange={files => handleFileChange(files, setFieldValue)}
                    onAlert={handleAlert}
                  />
                  <CSVDownload typeFor={'matrix'} />
                  {submitFlag && (
                    <ErrorMessage name="csvFile" component={FormErrorMsg} />
                  )}
                </Grid>
              </Grid>
              <Grid
                container
                spacing={1}
                justifyContent="flex-end"
                style={{ padding: '10px' }}
                // className={classes.partsMatrixs}
                // className={
                //   action == 'edit' && item.partsFor !== 'source'
                //     ? classes.editBtn
                //     : undefined
                // }
              >
                <Grid item style={{ paddingRight: '10px' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    className={clsx('reset-btn', classes.btnDiv)}
                    fullWidth
                    onClick={() => {
                      if (action !== 'edit') {
                        setAddGrid('');
                      } else {
                        cancel();
                      }
                    }}
                    disabled={disableSubmitButton}
                  >
                    Cancel
                  </Button>
                </Grid>
                <Grid
                  item
                  className={
                    action === 'edit' &&
                    (item.partsFor !== 'source' ||
                      localStorage.getItem('dms') === 'rey' ||
                        localStorage.getItem('dms') === 'tek')
                      ? classes.editBtnOpCode
                      : classes.editBtn
                  }
                  // style={{ paddingRight: '55px' }}
                >
                  <Button
                    variant="contained"
                    type="submit"
                    color="primary"
                    disabled={disableSubmitButton}
                    className={clsx('reset-btn', classes.btnDiv)}
                    fullWidth
                    onClick={() => setsubmitFlag(true)}
                  >
                    Submit
                  </Button>
                </Grid>
              </Grid>
              <FormikValueObserver
                setIsEditFormValueChanged={setIsEditFormValueChanged}
                isEditFormValueChanged={isEditFormValueChanged}
              />
            </Form>
          )}
        </Formik>
      </CardContent>
    </>
  );
});
const FormikValueObserver = props => {
  const { setIsEditFormValueChanged, isEditFormValueChanged } = props;
  const { values, initialValues } = useFormikContext();
  // const [hasChanged, setHasChanged] = useState(false);

  const areArraysEqual = (arr1, arr2) => {
    if (arr1.length !== arr2.length) return false;
    return arr1.every((element, index) => element === arr2[index]);
  };

  useEffect(() => {
    const isChanged =
      values.customSource !== initialValues.customSource ||
      !areArraysEqual(values.source, initialValues.source);
    if (isChanged && !isEditFormValueChanged) {
      console.log('Values changed:', values);
      setIsEditFormValueChanged(true);
    } else if (!isChanged && isEditFormValueChanged) {
      setIsEditFormValueChanged(false);
    }
  }, [
    values.customSource,
    values.source,
    initialValues.customSource,
    initialValues.source,
    isEditFormValueChanged
  ]);

  return null;
};

export default PartsForm;
