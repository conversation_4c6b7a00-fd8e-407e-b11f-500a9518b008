import React, { useCallback, useEffect, useRef, useState } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { makeStyles } from '@material-ui/core/styles';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import clsx from 'clsx';
import EditIcon from '@material-ui/icons/Edit';
import { withKeycloak } from '@react-keycloak/web';

import {
  Paper,
  Typography,
  Button,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup,
  CircularProgress,
  Tooltip,
  Box,
  IconButton,
  LinearProgress
} from '@material-ui/core';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import MatrixForm from './MatrixForm';
import GridDefinitions from './GridDefinitions';
import Accordion from '@material-ui/core/Accordion';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import moment from 'moment';
import RestoreIcon from '@material-ui/icons/Restore';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import {
  GetGridTypeOptions,
  GetOpcodesList,
  InsertOrUpdatePartsMatrix,
  fetchKpiScorecardPartsMatrix,
  fetchKpiScorecardPartsMatrixDetails,
  getGridorMatrixPayTypeDetailsReport,
  getGridorMatrixPayTypesByGridType,
  getMatrixType,
  partsMatrixFileUploadNew
} from 'src/utils/hasuraServices';
import Page from 'src/components/Page';
import { i } from 'react-dom-factories';
import DeleteButton from './DeleteButton';
import { useSelector } from 'react-redux';
import EditGridOrMatrixNameDialog from 'src/components/EditGridOrMatrixNameDialog';
const useStyles = makeStyles(theme => ({
  root: {
    width: '99%',
    marginLeft: 8
  },
  text: {
    fontSize: 13
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  matrixType: {
    marginTop: -42,
    marginLeft: 150
  },
  back: {
    marginRight: 13,
    float: 'right',
    width: 'auto',
    marginTop: -40
  },
  loaderGrid: {
    marginLeft: 450,
    marginTop: 120
  },
  iconButton: {
    '&:hover': {
      backgroundColor: 'transparent'
    },
    '&:focus': {
      boxShadow: 'none'
    }
  },
  header: {
    textTransform: 'none',
    fontSize: 13,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  headerValue: {
    textTransform: 'none',
    fontSize: 13,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#7987a1',
    fontSize: 13
  },
  partSourcesMainList: {
    display: 'flex',
    marginTop: -23,
    width: '90%',
    marginLeft: 88,
    '@media (min-width: 2560px)': {
      marginLeft: 88
    }
  }
}));

var lodash = require('lodash');

const Dealer = process.env.REACT_APP_DEALER;
const NewMatrix = props => {
  const [disableSubmitButton, setDisableSubmitButton] = useState(false);
  let expand = props.history.state == undefined ? true : false;
  const session = useSelector(state => state.session);
  const ref = useRef(null);
  const [type, setType] = React.useState('paytype_matrix');
  const accordionRefs = useRef([]);
  const gridRef = useRef();
  const gridApi = useRef(null);
  const [listLoading, setListLoading] = useState(true);
  const [sourceLoading, setSourceLoading] = useState(false);
  const [initialRowData, setInitialRowData] = useState();
  const [expandedAccordionIndex, setExpandedAccordionIndex] = useState(null);
  const [formData, setFormData] = useState({});
  const [matrices, setMatrices] = useState([]);
  const [rowDataList, setRowDataList] = useState([]);
  const [rowData, setRowData] = useState();
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [edit, setEdit] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');
  const [installDateValue, setInstallDateValue] = useState();
  const [updateDate, setUpdateDate] = useState(false);
  const [isAccordionExpanded, setIsAccordionExpanded] = useState(false);
  const [enableSave, setEnableSave] = useState(false);
  const [isSuccessful, setIsSuccessful] = useState(true);
  const [msgType, setMsgType] = useState('success');
  const [matrixSource, setMatrixSource] = useState([]);
  const [matrixType, setMatrixType] = useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [matrixValidationMessage, setMatrixValidationMessage] = useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const [addGrid, setAddGrid] = useState('');
  const [saveClickedFlag, setSaveClickedFlag] = useState(false);
  const classes = useStyles();
  const [matrixSizeOnEdit, setMatrixSizeOnEdit] = useState();
  const [selectedSources, setSelectedSources] = useState([]);
  const [opcodeListChoice, setOpcodeListChoice] = useState([]);
  const [isEditValueChanged, setIsEditValueChanged] = useState(false);
  const [isEditFormValueChanged, setIsEditFormValueChanged] = useState(false);
  const [showBackBtn, setShowBackBtn] = useState(true);
  const [isLoadingGridType, setIsLoadingGridType] = useState(true);
  const [autoExpand, setAutoExpand] = useState(
    props.history.location.state == undefined ||
      props.history.location.state == null
      ? false
      : true
  );
  const {
    columnDefs,
    defaultColDef,
    formatCellValueDescription,
    commonDefaultProps,
    formatCellValueRate,
    formatCellValue,
    SetCellStyle
  } = GridDefinitions(saveClickedFlag);
  const firstRender = useRef(true);
  useEffect(() => {
    if (firstRender.current) {
      firstRender.current = false;
    } else {
      setShowBackBtn(false);
      setExpandedAccordionIndex(null);
    }

    setListLoading(true);
    setAddGrid('');
    fetchPartsMatrixRowData();
  }, [session.storeSelected]);

  const [openDlg, setOpenDlg] = useState(false);
  const [parent, setParent] = useState('matrix');
  const [matrixTypeDetails, setMatrixTypeDetails] = useState([]);
  const handleCancelDlg = () => {
    setOpenDlg(false);
  };

  const getGridTypeOptionsPromise = () =>
    new Promise((resolve, reject) => {
      GetGridTypeOptions(result => {
        if (result) resolve(result);
        else reject(new Error('Failed to fetch Grid Type Options'));
      });
    });

  const fetchGridOrMatrixTypes = async () => {
    try {
      setIsLoadingGridType(true);
      setOpenDlg(true);
      const [result] = await Promise.all([getGridTypeOptionsPromise()]);
      setMatrixTypeDetails(result);
      const typeChoice = result
        .filter(item => item?.value) // Filter out items where 'value' is null or undefined
        .map(({ value }) => ({
          id: value,
          name: value
        }));
      setMatrixType(typeChoice);
    } catch (error) {
      console.error('Error fetching grid/matrix types:', error);
    } finally {
      setIsLoadingGridType(false);
    }
  };

  const fetchPartsMatrixRowData = async (callType = 'matrix_type') => {
    const fetchKpiScorecardPartsMatrixPromise = input =>
      new Promise((resolve, reject) => {
        fetchKpiScorecardPartsMatrix(input, data => {
          if (data) resolve(data);
          else reject(new Error('Failed to fetch KPI Scorecard Parts Matrix'));
        });
      });

    const getGridTypeOptionsPromise = () =>
      new Promise((resolve, reject) => {
        GetGridTypeOptions(result => {
          if (result) resolve(result);
          else reject(new Error('Failed to fetch Grid Type Options'));
        });
      });

    const fetchOpcodeListPromise = () =>
      new Promise((resolve, reject) => {
        GetOpcodesList(result => {
          if (result) resolve(result);
          else reject(new Error('Failed to fetch Grid Type Options'));
        });
      });

    try {
      const input = {
        pCallType: callType,
        pMatrixType: '',
        pPrtsource: '',
        pCreatedDate: ''
      };
      const [result, res, opcodes] = await Promise.all([
        fetchKpiScorecardPartsMatrixPromise(input),
        getGridTypeOptionsPromise(),
        fetchOpcodeListPromise()
      ]);

      if (callType === 'prtsource_list') {
        const typeChoice = res
          .filter(item => item?.value) // Filter out items where 'value' is null or undefined
          .map(({ value }) => ({
            id: value,
            name: value
          }));
        setMatrixType(typeChoice);

        const choice = result
          .filter(item => item?.prtsourceList) // Filter out items where 'prtsourceList' is null or undefined
          .map(({ prtsourceList }) => ({
            id: prtsourceList,
            name: prtsourceList
          }))
          .sort((a, b) => parseInt(a.id) - parseInt(b.id));
        setMatrixSource(choice);
        const opcodeChoice = opcodes.map(item => ({
          id: item.opcode,
          name: item.opcode
        }));
        setOpcodeListChoice(opcodeChoice);
      } else {
        setRowDataList(result);

        if (autoExpand) {
          //code  for auto expand accordion
          const {
            matrixDate,
            matrixType,
            selectedPartsFor,
            partsSource
          } = props.history.location.state;

          const index = result.findIndex(item => {
            const isBaseConditionMet =
              item.createdDate === matrixDate &&
              item.matrixType === matrixType &&
              item.partsFor === selectedPartsFor;

            const isSourceConditionMet =
              selectedPartsFor === 'source'
                ? Array.isArray(item.prtsourceDisplay) &&
                  item.prtsourceDisplay.includes(partsSource)
                : true;

            return isBaseConditionMet && isSourceConditionMet;
          });

          const item = result[index];
          if (index !== -1) {
            fetchPartsMatrixRowDetails(item);
            setExpandedAccordionIndex(index);
            setAutoExpand(false);
          }
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setListLoading(false);
      setSourceLoading(false);
    }
  };
  useEffect(() => {
    // Scroll the expanded accordion into view when the data list or loading state changes
    if (accordionRefs.current[expandedAccordionIndex]) {
      setTimeout(() => {
        accordionRefs.current[expandedAccordionIndex] &&
          accordionRefs.current[expandedAccordionIndex].scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
      }, 1000);
    }
  }, [rowDataList, listLoading]);

  const fetchPartsMatrixRowDetails = rowData => {
    const input = {
      pMatrixType: rowData.matrixType,
      pCreatedDate: rowData.createdDate,
      pCallType: 'matrix',
      pPrtsource: rowData.prtsource
    };
    fetchKpiScorecardPartsMatrixDetails(input, result => {
      setRowData(result);
    });
  };

  const onGridReady = params => {
    gridRef.current = params && params;
    gridApi.current = params && params.api;
    const newEmptyRow = {
      id: 0,
      priceStartRange: '0.01',
      priceEndRange: '',
      calcBase: '',
      breakField: '',
      addPercentage: ''
    };
    setInitialRowData([newEmptyRow]);
    !rowData &&
      setTimeout(() => {
        onBtStartEditing();
      }, 100);
  };
  React.useEffect(() => {
    if (gridApi.current) {
      console.log('params........................111');
      gridApi.current.refreshCells({ force: true });
    }
  }, [saveClickedFlag]);

  const onBtStartEditing = useCallback((key, pinned) => {
    gridRef &&
      gridRef.current &&
      gridRef.current.api &&
      gridRef.current.api.setFocusedCell(0, 'priceEndRange', pinned);
    gridRef &&
      gridRef.current &&
      gridRef.current.api &&
      gridRef.current.api.startEditingCell({
        rowIndex: key ? key : 0,
        colKey: 'priceEndRange',
        rowPinned: pinned,
        key: key
      });
  }, []);

  function validatePriceRanges(data) {
    let isValid = true;
    setMatrixValidationMessage('');

    // Regex for 7 digits with optional decimal places
    const regex = /^-?(\d{1,7}|\.\d{1,2}|\d{1,7}\.\d{0,2})$/;

    for (const item of data) {
      const priceStart = parseFloat(item.priceStartRange);
      const priceEnd = parseFloat(item.priceEndRange);

      // Check for null, undefined, NaN, empty string, etc.
      if (
        item.addPercentage == null ||
        item.addPercentage === '' ||
        item.breakField == null ||
        item.breakField === '' ||
        item.calcBase == null ||
        item.calcBase === '' ||
        item.priceEndRange == null ||
        item.priceEndRange === '' ||
        item.priceStartRange == null ||
        item.priceStartRange === ''
      ) {
        isValid = false;
        setMatrixValidationMessage(
          'All fields must be filled out for each item.'
        );
        break;
      } else {
        // First, check for negative values
        if (
          (item.addPercentage !== null && item.addPercentage <= 0) ||
          (item.priceEndRange !== null && item.priceEndRange < 0)
        ) {
          isValid = false;
          setMatrixValidationMessage('Values must be greater than 0.');
          break;
        }
        // Then check if they are numeric
        else if (
          (item.addPercentage &&
            !item.addPercentage.match(/^-?[0-9]*\.?[0-9]+$/)) ||
          (item.priceEndRange &&
            !item.priceEndRange.match(/^-?[0-9]*\.?[0-9]+$/)) ||
          (item.priceStartRange && isNaN(item.priceStartRange)) ||
          isNaN(item.addPercentage) ||
          isNaN(item.priceEndRange)
        ) {
          isValid = false;
          setMatrixValidationMessage('Enter numeric values only.');
          break;
        }
        // Check for specific validation like digit length or decimals
        else if (
          (item.addPercentage && !regex.test(item.addPercentage)) ||
          (item.priceEndRange && !regex.test(item.priceEndRange))
        ) {
          isValid = false;
          setMatrixValidationMessage(
            'Value should contain only seven digits with up to two decimal places, e.g. 1234567.89'
          );
          break;
        }
        // Finally, check for range consistency
        else if (priceEnd <= priceStart) {
          isValid = false;
          setMatrixValidationMessage(
            'To value must be greater than From Value.'
          );
          break;
        }
      }
    }
    return isValid;
  }

  const handleSubmitPartsMatrix = async (values, pCall) => {
    let combinedSource = values && values.source ? [...values.source] : [];
    if (values && values.customSource) {
      combinedSource = combinedSource.concat(
        values.customSource.split(',').map(item => item.trim())
      );
    }

    if (pCall === 'upload') {
      const file = values.csvFile[0];
      const base64Content = await convertFileToBase64(file);
      const pValData = {
        base64Data: base64Content,
        inFileName: values.csvFile[0].name,
        inMatrixType:
          values.matrixType == 'Source Matrix' ? values.mType : values.opcode,
        inInstallationDate: moment(values.installDate).format('MM-DD-YYYY'),
        pMatrixOrFleet:
          values.matrixType == 'Source Matrix' ? 'source' : 'opcode',
        inPrtsource:
          localStorage.getItem('dms') == 'rey' ||
          localStorage.getItem('dms') == 'tek'
            ? ['']
            : combinedSource.sort((a, b) => parseInt(a, 10) - parseInt(b, 10))
      };
      setDisableSubmitButton(true);
      partsMatrixFileUploadNew(pValData, result => {
        const res =
          result.data
            .statelessCcPhysicalRwInsertGriddataDtlPartsMatrixFileUploadLog;

        if (res.results[0].status === 1) {
          setIsSuccessful(true);
          setMsgType('success');
          setAddGrid('');
          fetchPartsMatrixRowData();
        } else {
          setIsSuccessful(false);
          setMsgType('error');
        }
        setOpenSnackbar(true);
        setStatusMessage(res.results[0].msg);
        setDisableSubmitButton(false);
      });
    } else if (pCall === 'edit') {
      const isValidated = validatePriceRanges(rowData);

      if (isValidated) {
        setSaveClickedFlag(false);
        const data = {
          installDate: values.installDate,
          pCreatedDate: rowData[0].createdDate,
          matrixType: rowData[0].matrixType,
          source: combinedSource,
          partsFor: rowData[0].partsFor
        };
        const newMatrix = {
          tableData: [...rowData],
          formData: { ...data }
        };
        createOrUpdatePartsMatrix(newMatrix, 'update_count_change');
      }
    } else {
      setSaveClickedFlag(true);
      const newMatrix = {
        tableData: [...initialRowData],
        formData: { ...values }
      };
      const isPriceRangeValid = validatePriceRanges(newMatrix.tableData);
      if (isPriceRangeValid) {
        setMatrices([...matrices, newMatrix]);
        createOrUpdatePartsMatrix(newMatrix, 'insert');
        setFormData({});
      }
    }
  };
  const convertFileToBase64 = file =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result.split(',')[1]);
      reader.onerror = error => reject(error);
    });

  const createOrUpdatePartsMatrix = async (newMatrix, action) => {
    try {
      let combinedSource =
        newMatrix && newMatrix.formData && newMatrix.formData.source
          ? [...newMatrix.formData.source]
          : [];
      if (newMatrix && newMatrix.formData && newMatrix.formData.customSource) {
        combinedSource = combinedSource.concat(
          newMatrix.formData.customSource.split(',').map(item => item.trim())
        );
      }
      const formData = {
        storeId:
          newMatrix && newMatrix.formData ? newMatrix.formData.storeId : null,
        pNewPrtsource: combinedSource,
        pOldPrtsource:
          action === 'delete'
            ? combinedSource
            : rowData && rowData[0] && rowData[0].prtsource
            ? rowData[0].prtsource
            : combinedSource,
        p_matrix_type:
          action === 'delete'
            ? newMatrix && newMatrix.formData && newMatrix.formData.matrixType
            : newMatrix &&
              newMatrix.formData &&
              newMatrix.formData.matrixType == 'Source Matrix'
            ? newMatrix.formData.mType
            : newMatrix &&
              newMatrix.formData &&
              newMatrix.formData.matrixType == 'Opcode'
            ? newMatrix.formData.opcode
            : action === 'update_count_change'
            ? newMatrix.formData.matrixType
            : null,
        p_created_date:
          newMatrix && newMatrix.formData && newMatrix.formData.pCreatedDate
            ? newMatrix.formData.pCreatedDate
            : newMatrix.formData.installDate,
        p_store_install_date: newMatrix.formData.installDate,
        pMatrixOrFleet:
          newMatrix &&
          newMatrix.formData &&
          newMatrix.formData.matrixType &&
          newMatrix.formData.matrixType === 'Source Matrix'
            ? 'source'
            : newMatrix &&
              newMatrix.formData &&
              newMatrix.formData.matrixType &&
              newMatrix.formData.matrixType === 'Opcode'
            ? 'opcode'
            : action === 'update_count_change' || action === 'delete'
            ? newMatrix && newMatrix.formData && newMatrix.formData.partsFor
            : ''
      };
      const pMatrix =
        action !== 'delete'
          ? newMatrix && newMatrix.tableData
            ? newMatrix.tableData.map(item => ({
                price_start_range: item.priceStartRange,
                price_end_range: item.priceEndRange,
                add_percentage: item.addPercentage,
                calc_base: item ? item.calcBase : null,
                break_field: item ? item.breakField : null
              }))
            : []
          : null;
      setDisableSubmitButton(true);
      const handleInsertOrUpdate = () => {
        InsertOrUpdatePartsMatrix(
          action,
          formData.storeId,
          localStorage.getItem('dms') == 'rey' ||
            localStorage.getItem('dms') == 'tek'
            ? ['']
            : formData.pNewPrtsource.sort(
                (a, b) => parseInt(a, 10) - parseInt(b, 10)
              ),
          localStorage.getItem('dms') == 'rey' ||
            localStorage.getItem('dms') == 'tek'
            ? ['']
            : formData.pOldPrtsource.sort(
                (a, b) => parseInt(a, 10) - parseInt(b, 10)
              ),
          formData.p_matrix_type,
          formData.p_created_date,
          formData.p_store_install_date,
          pMatrix,
          formData.pMatrixOrFleet,
          result => {
            if (result?.[0]?.rStatus === 1) {
              setIsSuccessful(true);
              setMsgType('success');
              fetchPartsMatrixRowData();
              if (action === 'update_count_change') {
                fetchPartsMatrixRowDetails({
                  matrixType: formData.p_matrix_type,
                  createdDate: formData.p_created_date,
                  pStore: formData.storeId,
                  prtsource: combinedSource
                });
              }
              setAddGrid('');
              setMatrixValidationMessage('');
              setEdit(false);
              setEnableSave(false);
            } else {
              setIsSuccessful(false);
              setMsgType('error');
            }
            setDisableSubmitButton(false);
            setOpenSnackbar(true);
            setStatusMessage(result?.[0]?.msg ?? 'Error');
          }
        );
      };

      if (action === 'update_count_change') {
        if (isEditValueChanged || isEditFormValueChanged) {
          handleInsertOrUpdate();
        } else {
          setOpenSnackbar(true);
          setStatusMessage('Please make some changes to update');
          console.log('Please make some changes to update');
          const timer = setTimeout(() => {
            onBtStartEditing(undefined, undefined);
          }, 500);
          setMsgType('warning');
          setDisableSubmitButton(false);
        }
        setIsEditValueChanged(false);
        setIsEditFormValueChanged(false);
      } else {
        handleInsertOrUpdate();
      }
    } catch (error) {
      console.error('Error in createOrUpdatePartsMatrix:', error);
      setOpenSnackbar(true);
      setStatusMessage('An error occurred while updating the parts matrix.');
    }
  };

  const addEmptyRow = (data, setData) => {
    setSaveClickedFlag(true);
    gridApi.current.refreshCells({ force: true });
    const isValidated = validatePriceRanges(data);
    console.log('isValidated', isValidated);

    const lastIndexedData = data[data.length - 1];
    if (isValidated) {
      setSaveClickedFlag(false);
      const newId = data.length;
      const newEmptyRow = {
        id: newId,
        priceStartRange: (Number(lastIndexedData.priceEndRange) + 0.01)
          .toFixed(2)
          .toString(),
        calcBase: lastIndexedData.calcBase,
        breakField: lastIndexedData.breakField
      };

      setData(prevData => [...prevData, newEmptyRow]);
      // Focus on the first cell of the newly added row
      const timer = setTimeout(() => {
        onBtStartEditing(newId, undefined);
      }, 500);
    }
  };

  const addEmptyRowForCreate = () => {
    addEmptyRow(initialRowData, setInitialRowData);
  };

  const addEmptyRowForEdit = () => {
    addEmptyRow(rowData, setRowData);
  };

  const handleRemoveLastRow = (data, setData) => {
    setIsSuccessful(true);
    setMsgType('success');
    setOpenSnackbar(true);
    setMatrixValidationMessage('');
    setStatusMessage('Row removed successfully.');
    setIsEditValueChanged(true);
    const updatedRowData = [...data];
    updatedRowData.pop();
    setData(updatedRowData);
  };

  const handleRemoveLastRowForCreate = () => {
    handleRemoveLastRow(initialRowData, setInitialRowData);
  };
  const handleRemoveLastRowForEdit = () => {
    handleRemoveLastRow(rowData, setRowData);
  };

  const handleRadioChange = event => {
    setExpandedAccordionIndex(null);
    setAddGrid(event.target.value);
    setInstallDateValue('');
    setUpdateDate(false);
    ref && ref.current && ref.current.refresh();
    if (event.target.value === 'matrix') {
      fetchPartsMatrixRowData('prtsource_list');
      setSourceLoading(true);
      setSaveClickedFlag(false);
      setIsAccordionExpanded(false);
      setRowData(null);
      setExpandedAccordionIndex(null);
      setMatrixValidationMessage('');
    } else if (event.target.value === 'upload') {
      fetchPartsMatrixRowData('prtsource_list');
      setSourceLoading(true);
    }
  };

  const handleEditButton = params => {
    fetchPartsMatrixRowData('prtsource_list');
    setEdit(true);
    const timer = setTimeout(() => {
      onBtStartEditing(undefined, undefined);
    }, 500);
  };
  const formatCellValueDate = params => {
    if (params != null && params != 0) {
      return moment(params).format('MM/DD/YY');
    }
  };

  const cancel = () => {
    setEnableSave(false);
    setEdit(false);
    setExpandedAccordionIndex(null);
    setMatrixValidationMessage('');
  };
  const hidesnackbar = () => {
    setOpenSnackbar(false);
  };
  const getGridWrapper = (action, RowData) => {
    return (
      <>
        <div
          className="ag-theme-balham"
          style={{
            height: '30vh',
            width: '85%'
          }}
        >
          <AgGridReact
            columnDefs={columnDefs}
            editType="fullRow"
            onGridReady={params => onGridReady(params)}
            rowData={action === 'add' ? initialRowData : rowData}
            defaultColDef={defaultColDef}
            rowSelection="single"
            animateRows={true}
            ref={gridRef}
            singleClickEdit={true}
            suppressColumnVirtualisation={true}
            suppressChangeDetection={true}
            suppressKeyboardEvent={params => {
              return true;
            }}
            onCellValueChanged={params => {
              if (action == 'add') {
                const updatedData =
                  initialRowData &&
                  initialRowData.map(row =>
                    row.id === params.data.id ? { ...row, ...params.data } : row
                  );
                setInitialRowData(updatedData);
              } else {
                const updatedData =
                  rowData &&
                  rowData.map((row, index) =>
                    index === params.rowIndex ? { ...row, ...params.data } : row
                  );
                setRowData(updatedData);
                setIsEditValueChanged(true);
              }
            }}
            stopEditingWhenGridLosesFocus={true}
            onRowEditingStarted={() => setEnableSave(true)}
            suppressClickEdit={enableSave ? false : true}
            suppressDragLeaveHidesColumns={true}
            suppressMovableColumns={true}
          />
        </div>
      </>
    );
  };
  const findMatrixOrder = order => {
    const matrixOrder = order === 1 ? 'Current' : 'Prior' + Number(order - 1);
    return matrixOrder;
  };

  const handleclick = params => {
    props.history.push({
      pathname:
        props.history.location.state &&
        props.history.location.state.pageType == 'FleetAccounts'
          ? '/FleetAccounts'
          : localStorage.getItem('versionFlag') == 'TRUE'
          ? '/PartsMisses'
          : '/PartsTargetMisses',
      state: {
        selectedFilter: props.history.location.state.selectedFilter,
        selectedToggle: props.history.location.state.selectedToggle,
        selectedMonthYear: props.history.location.state.selectedMonthYear,
        parent: props.history.location.state.parent,
        previousToggle: props.history.location.state.previousToggle,
        payType: props.history.location.state.payType,
        previousPayType: props.history.location.state.PrevPayType,
        previousGridType: props.history.location.state.PrevGridType,
        // showAllJobs: showAllJobs
        showAllJobs: props.history.location.state.showAllJobs
          ? props.history.location.state.showAllJobs
          : false,
        filterStart: props.history.location.state.filterStart,
        filterEnd: props.history.location.state.filterEnd,
        gridType:
          props.history.location.state.pageType != 'FleetAccounts'
            ? props.history.location.state.gridType
            : props.history.location.state.gridRateValueStart,
        selectedPayType: props.history.location.state.selectedPayType
          ? props.history.location.state.selectedPayType
          : '',
        customerName: props.history.location.state.customerName,
        // ? props.history.location.state.customerName
        // : props.history.location.state.fleetName,
        selectedGridType: props.history.location.state.selectedGridType,
        selectedOpcode: props.history.location.state.selectedOpcode,
        report_name: props.history.location.state.reportName
      }
    });
  };

  function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
  }

  const getName = partsFor => {
    switch (partsFor) {
      case 'opcode':
        return 'Opcode Name';
      case 'source':
        return 'Matrix Name';
      case 'opcode_fleet':
        return 'Fleet Opcode';
      case 'paytype_fleet':
        return 'Fleet Pay Type';
      case 'cust_fleet':
        return 'Fleet Customer';
      default:
        return '';
    }
  };

  const getPadding = partsFor => {
    switch (partsFor) {
      case 'opcode':
        return '7px';
      case 'source':
        return '14px';
      case 'opcode_fleet':
        return '12px';
      case 'paytype_fleet':
        return '5px';
      default:
        return '0px'; // Default padding if none of the cases match
    }
  };

  const handleAlert = alert => {
    if (alert.includes('removed')) {
      setOpenSnackbar(true);
      setStatusMessage(alert);
    }
  };

  return (
    <>
      <SuccessSnackbar
        open={openSnackbar}
        onClose={() => hidesnackbar()}
        msg={statusMessage}
        goalFail={isSuccessful ? false : true}
        autoHideDuration={6000}
        type={msgType}
      />
      {disableSubmitButton && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'rgba(255, 255, 255, 0.91)',
            zIndex: '1000',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <CircularProgress size={60} />
        </div>
      )}
      <Page title="Parts Matrix(s)">
        <Paper
          square
          style={{
            // margin: 8,
            marginTop: '40px',
            paddingTop: '6px',
            height: '40px',
            margin: '8px 8px 8px',
            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
          }}
        >
          <Grid
            container
            className={clsx(props.titleContainer, 'reset-dashboard')}
          >
            <Grid item xs={5} style={{ display: 'flex', alignItems: 'center' }}>
              {showBackBtn &&
                props.history &&
                props.history.location.state &&
                (props.history.location.state.pageType == 'PartsMisses' ||
                  props.history.location.state.pageType == 'FleetAccounts') && (
                  <Button
                    variant="contained"
                    className={'bck-btn'}
                    onClick={handleclick}
                    style={{ width: 'auto' }}
                    fullWidth={false}
                  >
                    <Typography variant="body1" align="left">
                      Back
                    </Typography>
                  </Button>
                )}
            </Grid>
            <Grid item xs={5} style={{ display: 'flex', alignItems: 'center' }}>
              <Typography
                variant="h4"
                color="primary"
                className={clsx(props.mainLabel, 'main-title')}
              >
                Parts Matrix(s)
              </Typography>
            </Grid>
          </Grid>
        </Paper>
        <div>
          {props.keycloak.realmAccess.roles.includes('superadmin') === true && (
            <Paper
              square
              style={{
                border:
                  Dealer === 'Armatus'
                    ? '1px solid #003d6b'
                    : '1px solid #C2185B',
                color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
                cursor: 'default',
                backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
                fontSize: 14,
                padding: '5px',
                margin: 8,
                marginLeft: 8,
                paddingTop: 4,
                paddingLeft: 4
              }}
            >
              <Typography
                style={{
                  fontSize: 14,
                  color: '#084588',
                  textTransform: 'none',
                  pointerEvents: 'none',
                  borderColor: '#e7eef3',
                  marginLeft: '10px'
                }}
                variant="h6"
                color="primary"
              >
                Add a New Parts Matrix
              </Typography>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <RadioGroup
                  style={{ fontWeight: 'bold', marginLeft: '10px' }}
                  row
                  aria-labelledby="demo-row-radio-buttons-group-label"
                  name="row-radio-buttons-group"
                  value={addGrid}
                  onChange={handleRadioChange}
                >
                  <FormControlLabel
                    value={'matrix'}
                    control={
                      <Radio
                        size="small"
                        style={{
                          color: '#003d6b',
                          '&.Mui-checked': {
                            color: '#003d6b'
                          }
                        }}
                      />
                    }
                    label={'New Matrix'}
                  />
                  <FormControlLabel
                    value="upload"
                    control={
                      <Radio
                        size="small"
                        style={{
                          color: '#003d6b',
                          '&.Mui-checked': {
                            color: '#003d6b'
                          }
                        }}
                      />
                    }
                    label="Upload File"
                  />
                </RadioGroup>
                <Button
                  onClick={fetchGridOrMatrixTypes}
                  variant="outlined"
                  style={{
                    textTransform: 'none',
                    color: '#003d6b',
                    borderColor: '#003d6b',
                    marginLeft: 'auto',
                    marginTop: '-18px'
                  }}
                >
                  All Source Matrix Names
                </Button>
              </div>
              {addGrid && (
                <MatrixForm
                  type={addGrid}
                  component="partsMatrix"
                  getGridWrapper={getGridWrapper}
                  handleSubmitPartsMatrix={handleSubmitPartsMatrix}
                  setAddGrid={setAddGrid}
                  addEmptyRow={addEmptyRowForCreate}
                  handleRemoveLastRow={handleRemoveLastRowForCreate}
                  initialRowData={initialRowData}
                  matrixSourceList={matrixSource}
                  matrixTypeList={matrixType}
                  opcodeListChoice={opcodeListChoice}
                  incompleteError={matrixValidationMessage}
                  fetchPartsMatrixRowData={fetchPartsMatrixRowData}
                  listLoading={sourceLoading}
                  installDateValue={installDateValue}
                  setInstallDateValue={setInstallDateValue}
                  updateDate={updateDate}
                  setUpdateDate={setUpdateDate}
                  ref={ref}
                  disableSubmitButton={disableSubmitButton}
                  handleAlert={handleAlert}
                  setIsEditFormValueChanged={setIsEditFormValueChanged}
                  isEditFormValueChanged={isEditFormValueChanged}
                />
              )}
              <EditGridOrMatrixNameDialog
                openDlg={openDlg}
                parent={parent}
                handleCancelDlg={handleCancelDlg}
                gridOrMatrixDetails={matrixTypeDetails}
                fetchGridOrMatrixTypes={fetchGridOrMatrixTypes}
                isLoadingGridType={isLoadingGridType}
                setIsLoadingGridType={setIsLoadingGridType}
                getGridOrMatrixList={fetchPartsMatrixRowData}
              />
            </Paper>
          )}

          <Paper className={classes.root}>
            {listLoading ? (
              <div>
                <Box style={{ padding: 25 }}>
                  <LinearProgress color="secondary" />
                  <Typography
                    variant="h6"
                    align="center"
                    style={{ padding: 25 }}
                    color="primary"
                  >
                    Processing...
                  </Typography>
                </Box>
              </div>
            ) : rowDataList.length === 0 ? (
              <Box style={{ padding: 25 }}>
                <Typography
                  variant="h6"
                  align="center"
                  style={{ padding: 25 }}
                  color="primary"
                >
                  No Data To Display
                </Typography>
              </Box>
            ) : (
              <Paper>
                {rowDataList &&
                  rowDataList.map((item, index) => (
                    <Accordion
                      key={index}
                      expanded={index === expandedAccordionIndex}
                      onChange={(event, expanded) => {
                        setUpdateDate(false);
                        setInstallDateValue('');
                        setIsAccordionExpanded(expanded);
                        setAddGrid('');
                        setRowData(null);
                        setExpandedAccordionIndex(expanded ? index : null);
                        setEnableSave(false);
                        setEdit(false);
                        setMatrixValidationMessage('');
                        if (expanded) {
                          setSaveClickedFlag(false);
                          fetchPartsMatrixRowDetails(item);
                          fetchPartsMatrixRowData('prtsource_list');
                          setSelectedSources(item.prtsource);
                        } else {
                          setExpandedAccordionIndex(null);
                          setInstallDateValue('');
                        }
                      }}
                      ref={el => (accordionRefs.current[index] = el)}
                    >
                      <AccordionSummary
                        expandIcon={<ExpandMoreIcon />}
                        style={{ height: '110px' }}
                      >
                        <Grid container spacing={2}>
                          <Grid item xs={11}>
                            <Grid item xs={12}>
                              <Grid container spacing={1} alignItems="center">
                                <Grid item xs={3}>
                                  <Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      Matrix Period&nbsp;&nbsp;&nbsp;
                                    </Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      Matrix Type&nbsp;&nbsp;&nbsp;
                                    </Typography>
                                  </Typography>
                                  <Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.headerValue}
                                    >
                                      : {findMatrixOrder(item.matrixOrder)}
                                    </Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.headerValue}
                                    >
                                      :{' '}
                                      {capitalizeFirstLetter(
                                        item.partsForDisplay
                                      )}
                                    </Typography>
                                  </Typography>
                                </Grid>
                                <Grid
                                  item
                                  xs={3}
                                  // style={{ paddingLeft: '100px' }}
                                >
                                  <Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      Store Install Date&nbsp;&nbsp;&nbsp;
                                    </Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      FOPC Calculated Date&nbsp;&nbsp;&nbsp;
                                    </Typography>
                                  </Typography>
                                  <Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.headerValue}
                                    >
                                      :{' '}
                                      {formatCellValueDate(
                                        item.storeInstallDate
                                      )}
                                    </Typography>

                                    <Tooltip title="This may be different from store install date if no prior pricing provided">
                                      <Typography
                                        variant="subtitle1"
                                        className={classes.headerValue}
                                      >
                                        :{' '}
                                        {formatCellValueDate(item.createdDate)}
                                      </Typography>
                                    </Tooltip>
                                  </Typography>
                                </Grid>

                                <Grid
                                  item
                                  xs={3}
                                  style={{
                                    // paddingLeft: '50px',
                                    marginBottom: '30px'
                                  }}
                                >
                                  <Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      {getName(item.partsFor)}&nbsp;&nbsp;&nbsp;
                                    </Typography>
                                  </Typography>
                                  <Typography>
                                    <Tooltip title={item.matrixType}>
                                      <Typography
                                        variant="subtitle1"
                                        className={classes.headerValue}
                                        style={{
                                          paddingLeft: getPadding(item.partsFor)
                                        }}
                                      >
                                        :{' '}
                                        {formatCellValueDescription(
                                          item.matrixType
                                        )}
                                      </Typography>
                                    </Tooltip>
                                  </Typography>
                                </Grid>
                              </Grid>
                            </Grid>
                            <Grid item xs={11}>
                              <Typography>
                                <Typography
                                  variant="subtitle1"
                                  className={classes.header}
                                >
                                  Parts Source&nbsp;&nbsp;&nbsp;
                                </Typography>
                              </Typography>
                              <div className={classes.container}>
                                <div className={classes.partSourcesMainList}>
                                  <div>:&nbsp;</div>
                                  <div>
                                    {item.prtsourceDisplay.map(
                                      (items, indexs) => (
                                        <Typography
                                          key={indexs}
                                          className={classes.headerValue}
                                        >
                                          {items}
                                          {item.prtsourceDisplay.length ===
                                          indexs + 1
                                            ? ''
                                            : ',\u00A0'}
                                        </Typography>
                                      )
                                    )}
                                  </div>
                                </div>
                              </div>
                            </Grid>
                          </Grid>

                          {props.keycloak.realmAccess.roles.includes(
                            'superadmin'
                          ) === true && (
                            <Grid item xs={1}>
                              <div style={{ marginTop: '18px' }}>
                                {index === expandedAccordionIndex && (
                                  <Tooltip title="Edit">
                                    <IconButton
                                      className={classes.iconButton}
                                      aria-label="edit"
                                      disabled={enableSave}
                                      onClick={params => {
                                        handleEditButton(params);
                                        params.preventDefault();
                                        params.stopPropagation();
                                      }}
                                    >
                                      <EditIcon />
                                    </IconButton>
                                  </Tooltip>
                                )}
                                <DeleteButton
                                  createOrUpdatePartsMatrix={
                                    createOrUpdatePartsMatrix
                                  }
                                  cancel={cancel}
                                  RowData={item}
                                />
                              </div>
                            </Grid>
                          )}
                        </Grid>
                      </AccordionSummary>
                      {rowData && index === expandedAccordionIndex ? (
                        <AccordionDetails style={{ paddingTop: '40px' }}>
                          <Grid container>
                            <Grid item xs={6}>
                              {props.keycloak.realmAccess.roles.includes(
                                'superadmin'
                              ) === true && (
                                <Box style={{ flex: 1 }}>
                                  <Box
                                    style={{
                                      display: 'flex',
                                      flexDirection: 'row'
                                    }}
                                  >
                                    {enableSave && (
                                      <>
                                        <Button
                                          style={{
                                            textTransform: 'none',
                                            fontSize: '12px'
                                          }}
                                          onClick={() => {
                                            addEmptyRowForEdit();
                                          }}
                                        >
                                          <Typography
                                            style={{ color: '#1976D2' }}
                                          >
                                            Add row
                                          </Typography>
                                        </Button>
                                        <DeleteButton
                                          removeLastRow={
                                            handleRemoveLastRowForEdit
                                          }
                                          RowData={rowData}
                                          removeRow={true}
                                        />
                                        {matrixValidationMessage && (
                                          <Typography
                                            variant="h14"
                                            color="error"
                                            style={{
                                              textTransform: 'none',
                                              fontSize: 12,
                                              paddingTop: '8px'
                                            }}
                                          >
                                            {matrixValidationMessage}
                                          </Typography>
                                        )}
                                      </>
                                    )}
                                  </Box>
                                </Box>
                              )}

                              <Box>{getGridWrapper('view', item.gridData)}</Box>
                            </Grid>
                            {edit && index === expandedAccordionIndex && (
                              <Grid
                                item
                                xs={
                                  item.partsFor == 'source' &&
                                  localStorage.getItem('dms') !== 'rey' &&
                                  localStorage.getItem('dms') !== 'tek'
                                    ? 6
                                    : 7
                                }
                              >
                                <MatrixForm
                                  action={'edit'}
                                  item={item}
                                  rowData={rowData}
                                  handleSubmitPartsMatrix={values =>
                                    handleSubmitPartsMatrix(values, 'edit')
                                  }
                                  setAddGrid={setAddGrid}
                                  component={'partsMatrix'}
                                  addGrid={'matrix'}
                                  matrixSourceList={matrixSource}
                                  matrixTypeList={matrixType}
                                  fetchPartsMatrixRowData={
                                    fetchPartsMatrixRowData
                                  }
                                  enableSave={enableSave}
                                  cancel={cancel}
                                  setInstallDateValue={setInstallDateValue}
                                  installDateValue={installDateValue}
                                  updateDate={updateDate}
                                  setUpdateDate={setUpdateDate}
                                  disableSubmitButton={disableSubmitButton}
                                  setIsEditFormValueChanged={
                                    setIsEditFormValueChanged
                                  }
                                  isEditFormValueChanged={
                                    isEditFormValueChanged
                                  }
                                />
                              </Grid>
                            )}
                          </Grid>
                        </AccordionDetails>
                      ) : !rowData && index === expandedAccordionIndex ? (
                        <div>
                          <Box style={{ padding: 25 }}>
                            <LinearProgress color="secondary" />
                            <Typography
                              variant="h6"
                              align="center"
                              style={{ padding: 25 }}
                              color="primary"
                            >
                              Processing...
                            </Typography>
                          </Box>
                        </div>
                      ) : (
                        <></>
                      )}
                    </Accordion>
                  ))}
              </Paper>
            )}
          </Paper>
        </div>
      </Page>
    </>
  );
};

export default withKeycloak(NewMatrix);
