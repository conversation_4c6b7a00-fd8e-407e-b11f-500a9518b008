import React, { useState } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import {
  CircularProgress,
  IconButton,
  Tooltip,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Typography
} from '@material-ui/core';
import DeleteIcon from '@material-ui/icons/DeleteOutline';

const useStyles = makeStyles({
  iconButton: {
    '&:hover': {
      backgroundColor: 'transparent'
    },
    '&:focus': {
      boxShadow: 'none'
    }
  }
});
const DeleteButton = props => {
  const {
    createOrUpdatePartsMatrix,
    RowData,
    removeLastRow,
    removeRow,
    cancel
  } = props;
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleClick = event => {
    event.preventDefault();
    event.stopPropagation(); // Stop propagation to prevent accordion from expanding
    setOpen(true);
  };

  const handleDialogClose = event => {
    event.stopPropagation(); // Stop propagation to prevent accordion from expanding
    setOpen(false);
  };

  const handleConfirm = event => {
    event.stopPropagation(); // Stop propagation to prevent accordion from expanding
    setLoading(true);
    console.log('RowData--Delete--->', RowData);
    const values = {
      installDate: RowData.storeInstallDate,
      pCreatedDate: RowData.createdDate,
      matrixType: RowData.matrixType,
      source: RowData.prtsource,
      storeId: RowData.storeId, // Adjust as needed
      partsFor: RowData.partsFor
    };
    const newMatrix = {
      tableData: RowData,
      formData: { ...values }
    };
    createOrUpdatePartsMatrix(newMatrix, 'delete').finally(() => {
      setLoading(false);
      setOpen(false);
      cancel();
    });
  };

  const handleremoveRowConfirm = event => {
    event.stopPropagation(); // Stop propagation to prevent accordion from expanding
    removeLastRow();
    setOpen(false);
  };

  return (
    <>
      {!removeRow ? (
        <Tooltip title="Delete">
          <IconButton
            aria-label="delete"
            onClick={handleClick}
            className={classes.iconButton}
          >
            {loading ? <CircularProgress size={24} /> : <DeleteIcon />}
          </IconButton>
        </Tooltip>
      ) : (
        <Button
          style={{
            textTransform: 'none',
            fontSize: '12px',
            color: '#1976D2'
          }}
          onClick={handleClick}
          disabled={RowData?.length <= 1}
        >
          <Typography
            style={{ color: RowData?.length <= 1 ? '#B0BEC5' : '#1976D2' }}
          >
            Remove last row
          </Typography>
        </Button>
      )}

      <Dialog
        open={open}
        onClose={handleDialogClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        onClick={event => event.stopPropagation()} // Prevent click outside from closing the dialog
      >
        <DialogTitle id="alert-dialog-title">
          {removeRow ? 'Remove Last Row' : 'Delete Matrix'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {removeRow
              ? 'Are you sure you want to remove the last row?'
              : 'Are you sure you want to delete this matrix?'}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDialogClose}>Cancel</Button>
          <Button
            onClick={removeRow ? handleremoveRowConfirm : handleConfirm}
            autoFocus
          >
            Ok
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DeleteButton;
