import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { useDispatch, useSelector } from 'react-redux';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { setNavItems, setMenuSelected } from 'src/actions';
import NewMatrix from './NewMatrix';
import { useHistory } from 'react-router';
const PartsMatrixModule = props => {
  const session = useSelector(state => state.session);
  const history = useHistory();
  const dispatch = useDispatch();
  let params = '';
  if (
    typeof props.history.location.state != 'undefined' &&
    props.history.location.state != null
  ) {
    params = props.history.location.state.ronumber;
  }

  // useEffect(() => {
  //   dispatch(setMenuSelected('Parts Matrix'));
  //   dispatch(setNavItems(['Armatus Admin']));
  // }, []);
  return (
    <Page title="Parts Matrix">
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
       <NewMatrix  history={history}/>
      )}
    </Page>
  );
};

export default withKeycloak(PartsMatrixModule);
