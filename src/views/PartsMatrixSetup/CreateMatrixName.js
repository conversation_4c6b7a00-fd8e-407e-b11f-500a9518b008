import React, { forwardRef, useImperativeHandle } from 'react';
import AddIcon from '@material-ui/icons/Add';
import styled from 'styled-components';
import * as Yup from 'yup';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/core/styles';
import EditIcon from '@material-ui/icons/Edit';
import {
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  TextField,
  Tooltip,
  Typography,
  CircularProgress,
  Backdrop
} from '@material-ui/core';
import { createMatrixName } from 'src/utils/hasuraServices';
const FormErrorMsg = styled.div`
  color: red;
  font-size: 12px;
  text-overflow: ellipsis;
  max-width: 100%;
  padding-top: 10px;
`;
const CreateMatrixName = forwardRef(props => {
  const [open, setOpen] = React.useState(false);
  const [statusMessage, setStatusMessage] = React.useState('');
  const [openSnackbar, setOpenSnackbar] = React.useState(false);
  const [errorMsg, setErrorMsg] = React.useState('');
  const [isSuccessful, setIsSuccessful] = React.useState(true);
  const [loading, setLoading] = React.useState(false);
  const classes = makeStyles();
  const {
    fetchPartsMatrixRowData,
    onMatrixNameCreated,
    showSnackbar,
    isGridModal
  } = props;
  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
    setErrorMsg('');
  };

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setSubmitting(true);
    try {
      const matrixName = values.matrixName.trim();
      if (!matrixName || /\s{2,}/.test(matrixName)) {
        setErrorMsg('Invalid Matrix Name');
      } else {
        submitMatrixName(matrixName);
        setErrorMsg('');
      }
    } catch (error) {
      console.error('Failed to submit form:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const submitMatrixName = async matrixtname => {
    setLoading(true);
    const input = {
      inOldMatrixType: props.isEdit ? props.matrixName : matrixtname,
      inNewMatrixType: matrixtname,
      inActivity: props.isEdit ? 'Update' : 'Insert'
    };
    createMatrixName(input, result => {
      if (result && result[0] && result[0].status === 1) {
        if (props.isEdit) {
          showSnackbar(result[0].msg, true);
        } else {
          setIsSuccessful(true);
          if (onMatrixNameCreated) {
            onMatrixNameCreated(matrixtname);
          }
        }
        handleClose();
        fetchPartsMatrixRowData('prtsource_list');
      } else {
        if (props.isEdit) {
          showSnackbar(result[0].msg, false);
        } else {
          setIsSuccessful(false);
        }
      }
      setLoading(false);
      if (!props.isEdit) {
        setStatusMessage(result[0].msg);
        setOpenSnackbar(true);
      }
    });
  };
  const validateMatrixName = value => {
    if (!value) {
      return 'Please enter Matrix Name';
    }

    const trimmedValue = value.trim();
    if (trimmedValue !== value) {
      return 'Matrix Name cannot start or end with spaces';
    }

    if (/\s{2,}/.test(value)) {
      return 'Matrix Name cannot contain multiple consecutive spaces';
    }
    // if (/[^a-zA-Z0-9 ]/.test(value)) {
    //   return 'Matrix Name cannot contain special characters';
    // }
    if (trimmedValue.length > 30) {
      return 'Matrix Name cannot be longer than 30 characters';
    }

    return undefined;
  };
  const validationSchema = Yup.object({
    matrixName: Yup.string().test('matrixName', function(value) {
      const error = validateMatrixName(value);
      return error ? this.createError({ message: error }) : true;
    })
  });

  return (
    <>
      {isGridModal ? (
        ''
      ) : //removed the Add new matrix name button from here as we are not using it now.
      // <Button
      //   onClick={handleClickOpen}
      //   size="small"
      //   sx={{ borderRadius: 0, mt: 2 }}
      //   type="submit"
      //   variant="contained"
      //   color="primary"
      //   className={clsx('reset-btn', classes.btnDiv)}
      // >
      //   Add new matrix name
      // </Button>
      props.isEdit ? (
        <Tooltip title="Edit">
          <IconButton onClick={handleClickOpen} size="small" color="primary">
            <EditIcon />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Add Matrix Name">
          <IconButton onClick={handleClickOpen}>
            <AddIcon />
          </IconButton>
        </Tooltip>
      )}
      <Dialog
        fullWidth
        maxWidth="sm"
        open={open}
        onClose={(event, reason) => {
          if (reason !== 'backdropClick') {
            handleClose();
          }
        }}
      >
        <DialogTitle>
          {props.isEdit ? 'Edit Matrix Name' : 'Add Matrix Name'}
        </DialogTitle>
        <DialogContent style={{ overflow: 'hidden' }}>
          <Formik
            initialValues={{
              matrixName: props.isEdit ? props.matrixName : '',
              showField: ''
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting, values, setFieldValue, initialValues }) => (
              <Form>
                <Grid container spacing={2} direction="column">
                  <Grid item>
                    <Field
                      name="matrixName"
                      as={TextField}
                      variant="outlined"
                      size="small"
                      fullWidth
                      style={{ height: '40px' }}
                      onChange={e => {
                        let value = e.target.value;
                        value = value.replace(/\s{2,}/g, ' ');
                        if (value[0] === ' ') {
                          value = value.substring(1);
                        }
                        setFieldValue('matrixName', value);
                      }}
                    />
                    <ErrorMessage name="matrixName" component={FormErrorMsg} />
                  </Grid>
                  <Grid item container justifyContent="flex-end" spacing={2}>
                    <Grid item>
                      <Button
                        onClick={handleClose}
                        disabled={isSubmitting}
                        variant="contained"
                        color="primary"
                        className={clsx('reset-btn', classes.btnDiv)}
                      >
                        Cancel
                      </Button>
                    </Grid>
                    <Grid item>
                      <Button
                        type="submit"
                        disabled={
                          isSubmitting ||
                          (values.matrixName === initialValues.matrixName &&
                            values.isDefaultGrid ===
                              initialValues.isDefaultGrid)
                        }
                        color="primary"
                        className={clsx('reset-btn', classes.btnDiv)}
                        style={{
                          width: 'fit-content',
                          minWidth: 0,
                          fontSize: '14px',
                          height: '24px' // Set a fixed height for the button
                        }}
                      >
                        Submit
                      </Button>
                    </Grid>
                  </Grid>
                </Grid>
              </Form>
            )}
          </Formik>
          {loading && (
            <Backdrop
              open={loading}
              style={{
                zIndex: 1301,
                color: '#fff',
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%'
              }}
            >
              <CircularProgress color="inherit" />
            </Backdrop>
          )}
        </DialogContent>
      </Dialog>
      <SuccessSnackbar
        open={openSnackbar}
        onClose={() => setOpenSnackbar(false)}
        msg={statusMessage}
        goalFail={!isSuccessful}
        autoHideDuration={6000}
      />
    </>
  );
});

export default CreateMatrixName;
