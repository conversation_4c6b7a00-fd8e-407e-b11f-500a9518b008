import React, { useState } from 'react';
import { forwardRef, useImperativeHandle } from 'react';
import {
  Paper,
  Typography,
  Button,
  FormControlLabel,
  Grid,
  Radio,
  RadioGroup
} from '@material-ui/core';
import DeleteButton from './DeleteButton';
import PartsForm from './PartsForm';

const MatrixForm = forwardRef((props, ref) => {
  const {
    type,
    handleRadioChange,
    incompleteError,
    invalidError,
    getGridWrapper,
    handleSubmitLaborGrid,
    handleSubmitPartsMatrix,
    disableSubmitButton,
    setAddGrid,
    component,
    addEmptyRow,
    handleRemoveLastRow,
    initialRowData,
    matrixSourceList,
    matrixTypeList,
    fetchPartsMatrixRowData,
    listLoading,
    installDateValue,
    setInstallDateValue,
    updateDate,
    setUpdateDate,
    gridTypeChoice,
    getGridTypesList,
    hasDefaultType,
    defaultGridType,
    setStatusMessage,
    setOpenSnackbar,
    setStatusMessageType,
    item,
    action,
    rowData,
    cancel,
    opcodeListChoice,
    handleAlert,
    setIsEditFormValueChanged,
    isEditFormValueChanged
  } = props;

  return (
    <>
      <Paper
        style={{
          margin: '15px',
          padding: '15px',
          boxShadow: action === 'edit' ? 'none' : '0px 0px 0px 0px'
        }}
      >
        {type == 'matrix' && (
          <>
            <Button
              style={{
                textTransform: 'none',
                fontSize: '12px'
              }}
              onClick={() => {
                addEmptyRow();
              }}
            >
              <Typography style={{ color: '#1976D2' }}>Add row</Typography>
            </Button>

            <DeleteButton
              removeLastRow={handleRemoveLastRow}
              RowData={initialRowData}
              removeRow={true}
            />
            {incompleteError && (
              <Typography
                variant="h14"
                color="error"
                style={{
                  textTransform: 'none',
                  fontSize: 12
                }}
              >
                {incompleteError}
              </Typography>
            )}
          </>
        )}

        <Grid container>
          {type == 'matrix' && (
            <Grid item xs={6}>
              {getGridWrapper('add')}
            </Grid>
          )}
          <Grid
            item
            xs={action === 'edit' ? 12 : 6}
            display={'flex'}
            justifyContent={'center'}
            alignItems={'center'}
          >
            <PartsForm
              handleSubmitForm={values => handleSubmitPartsMatrix(values, type)}
              disableSubmitButton={disableSubmitButton}
              setAddGrid={setAddGrid}
              component={component}
              type={type}
              matrixSourceList={matrixSourceList}
              matrixTypeList={matrixTypeList}
              opcodeListChoice={opcodeListChoice}
              fetchPartsMatrixRowData={fetchPartsMatrixRowData}
              setInstallDateValue={setInstallDateValue}
              installDateValue={installDateValue}
              updateDate={updateDate}
              setUpdateDate={setUpdateDate}
              action={action}
              item={item}
              cancel={cancel}
              ref={ref}
              handleAlert={handleAlert}
              setIsEditFormValueChanged={setIsEditFormValueChanged}
              isEditFormValueChanged={isEditFormValueChanged}
            />
          </Grid>
        </Grid>
      </Paper>
    </>
  );
});
export default MatrixForm;
