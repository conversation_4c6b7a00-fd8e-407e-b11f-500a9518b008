import { useMemo } from 'react';
import { patterns } from 'src/utils/constants';

const GridDefinitions = saveClickedFlag => {
  const formatCellValueRate = params => {
    if (
      params &&
      params.value &&
      params.value !== '' &&
      patterns.numericWithOptionalDecimalRegex.test(params.value)
    ) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return params && params.value && '$' + params.value;
    }
  };

  const formatCellValue = params => {
    if (params && params.value && params.value !== '') {
      return params.value + '%';
    } else {
      return '';
    }
  };

  const SetCellStyle = () => {
    return { textAlign: 'right', border: '0px white', fontSize: '12px' };
  };

  const formatCellValueDescription = data => {
    return data.length > 100 ? data.substring(0, 100) + '...' : data;
  };

  const columnDefs = [
    {
      headerName: 'From',
      cellEditor: 'NumericEditor',
      editable: false,
      field: 'priceStartRange',
      width: 40,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',

      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      cellStyle: SetCellStyle
    },
    {
      headerName: 'To',
      field: 'priceEndRange',
      cellEditor: 'agTextCellEditor',
      width: 40,
      valueFormatter: formatCellValueRate,
      suppressMenu: true,
      unSortIcon: true,
      cellClass: 'twoDecimalPlacesWith$',

      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      cellStyle: SetCellStyle
    },
    {
      headerName: 'Base',
      cellEditor: 'agSelectCellEditor',

      cellStyle: SetCellStyle,
      cellEditorParams: {
        values: ['LIST-', 'COST-', 'LIST+', 'COST+', 'LIST%', 'COST%'],
      },
      field: 'calcBase',
      width: 180,
      suppressMenu: true,
      unSortIcon: true,
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      editable: true,
    },
    {
      headerName: 'Break Field',
      cellEditor: 'agSelectCellEditor',

      cellStyle: SetCellStyle,
      cellEditorParams: {
        values: ['LIST', 'COST']
      },
      field: 'breakField',
      width: 180,
      suppressMenu: true,
      unSortIcon: true,
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      editable: true
    },
    {
      headerName: 'Percentage',
      cellEditor: 'agTextCellEditor',
      field: 'addPercentage',
      valueFormatter: formatCellValue,
      cellClass: 'addPercentage',
      width: 180,
      suppressMenu: true,
      unSortIcon: true,

      comparator: function(valueA, valueB) {
        return valueA - valueB;
      },
      cellStyle: SetCellStyle,
      editable: true
    }
  ];

  const commonDefaultProps = {
    flex: 1,
    minWidth: 60,
    resizable: true,
    suppressMenu: true,
    enableValue: true
  };

  const defaultColDef = useMemo(() => {
    const lastRowEditable = function(params) {
      return params.node.rowIndex === params.api.getDisplayedRowCount() - 1;
    };
    return {
      ...commonDefaultProps,
      editable: lastRowEditable
    };
  }, []);

  return {
    columnDefs,
    defaultColDef,
    formatCellValueDescription,
    commonDefaultProps,
    formatCellValueRate,
    formatCellValue,
    SetCellStyle
  };
};

export default GridDefinitions;
