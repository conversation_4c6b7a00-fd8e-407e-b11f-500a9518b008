import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { useHistory } from 'react-router';
import { getTimeZone } from '../../utils/Utils';
import ScatterReport from './ScatterReport';
import { useSelector, useDispatch } from 'react-redux';
import { Redirect } from 'react-router-dom';
const useStyles = makeStyles(theme => ({
  root: {
    padding: '4px 8px'
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function JobCountGrid() {
  const session = useSelector(state => state.session);
  const history = useHistory();

  return (
    <Page title={'Job Count Grid'}>
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <ScatterReport session={session} history={history} />
      )}
    </Page>
  );
}

export default JobCountGrid;
