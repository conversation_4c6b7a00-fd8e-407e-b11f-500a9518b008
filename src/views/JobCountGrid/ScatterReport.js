import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tab,
  Tabs,
  Button
} from '@material-ui/core';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { getScatterPlotReportData } from 'src/utils/hasuraServices';
import { withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import { ReactSession } from 'react-client-session';
import ReferenceTopbar from 'src/components/charts/ReferenceTopbar';
import $ from 'jquery';

var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class ScatterReport extends React.Component {
  componentWillMount() {
    console.log('enter=1');
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps, prevState) {
    let type = '';
    if (this.state.tabSelection == 'three') {
      type = 'LM6';
    } else if (this.state.tabSelection == 'two') {
      type = 'LD90';
    } else if (this.state.tabSelection == 'one') {
      type = 'LD30';
    } else if (this.state.tabSelection == 'four') {
      type = 'LD7';
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      console.log('enter=12345', prevProps, prevState);
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        console.log('enter=123456');
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData(type);
      }
    }
    if (
      prevState.tabSelection != this.state.tabSelection ||
      (ReactSession.get('serviceAdvisors') != undefined &&
        lodash.isEqual(
          this.props.session.serviceAdvisor,
          prevProps.session.serviceAdvisor
        ) != true)
    ) {
      this.getAgGridData();
    }
  }

  constructor(props) {
    super(props);
    let tabSelection =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.tabSelection
        ? this.props.history.location.state.tabSelection
        : 'three';
    let compClicked =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.compLegendEnabled
        ? this.props.history.location.state.compLegendEnabled
        : false;
    let maintClicked =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.maintLegendEnabled
        ? this.props.history.location.state.maintLegendEnabled
        : false;
    let repClicked =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.repLegendEnabled
        ? this.props.history.location.state.repLegendEnabled
        : false;
    this.state = {
      rawGridApi: {},
      gridApi: {},
      isLoading: true,
      tabSelection: tabSelection,
      compClickedPrev: compClicked,
      maintClickedPrev: maintClicked,
      repairClickedPrev: repClicked,
      compClicked: compClicked,
      maintClicked: maintClicked,
      repairClicked: repClicked,
      allData: [],
      i: 0,
      j: 0,
      columnDefs: [
        {
          headerName: 'Hours Sold',
          field: 'coldesc',
          width: 48,
          suppressMenu: true,
          unSortIcon: false,
          valueFormatter: this.formatCellValueHours,
          cellClass: 'textAlign',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          }
        },
        {
          headerName: '0',
          field: 'col0',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 0),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.1',
          field: 'col1',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          valueFormatter: params => this.formatJobCount(params, 1),
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.2',
          field: 'col2',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          valueFormatter: params => this.formatJobCount(params, 2),
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.3',
          field: 'col3',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 3),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.4',
          field: 'col4',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 4),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.5',
          field: 'col5',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 5),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.6',
          field: 'col6',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 6),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.7',
          field: 'col7',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 7),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.8',
          field: 'col8',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 8),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        },
        {
          headerName: '0.9',
          field: 'col9',
          width: 155,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'jobCountCellRenderer',
          cellStyle: function(params) {
            return { textAlign: 'right', border: ' 0px white' };
          },
          valueFormatter: params => this.formatJobCount(params, 9),
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          },
          cellClass: 'textAlign'
        }
      ],

      rowData: [],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      components: {
        jobCountCellRenderer: jobCountCellRenderer
      },
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        // filter: 'agSetColumnFilter',
        // filterParams: {
        //   applyMiniFilterWhileTyping: true,
        // },
        enableValue: true,
        sortable: false,
        filter: false,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'smallHeader',
          font: {
            size: 14,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '#008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Right'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  formatJobCount = (params, value) => {
    if (
      params &&
      params.data['col' + value] &&
      params.data['colm' + value] &&
      params.data['colr' + value]
    ) {
      return (
        params.data['col' + value] +
        ' | ' +
        params.data['colm' + value] +
        ' | ' +
        params.data['colr' + value]
      );
    } else if (params.data['col' + value] == undefined) {
      return params.data['colm' + value] + ' | ' + params.data['colr' + value];
    } else if (params.data['colm' + value] == undefined) {
      return params.data['col' + value] + ' | ' + params.data['colr' + value];
    } else if (params.data['colr' + value] == undefined) {
      return params.data['col' + value] + ' | ' + params.data['colm' + value];
    } else if (
      params.data['col' + value] == undefined &&
      params.data['colm' + value] == undefined
    ) {
      return params.data['colr' + value];
    } else if (
      params.data['colm' + value] == undefined &&
      params.data['colr' + value] == undefined
    ) {
      return params.data['col' + value];
    } else if (
      params.data['col' + value] == undefined &&
      params.data['colr' + value] == undefined
    ) {
      return params.data['colm' + value];
    }
  };

  formatCellValuePayType = (params, value) => {
    if (params && params.data.col + '' + value) {
      return (
        params.data.col +
        '' +
        value +
        ' | ' +
        params.data.colm +
        '' +
        value +
        ' | ' +
        params.data.colr +
        '' +
        value
      );
    } else {
      return params.data.colm0 + ' | ' + params.data.colr0;
    }
  };
  formatCellValueHours = params => {
    if (params.value && params.value != '') {
      if (params.value == 20) {
        return '20+';
      } else {
        return params.value;
      }
    } else {
      return '';
    }
  };
  resetRawData = () => {
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
    this.gridColumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
    this.setState({ rowData: this.state.allData });
    this.setState({
      compClicked: false,
      maintClicked: false,
      repairClicked: false
    });
    $('#btnCompetitive').css('text-decoration', 'none');
    $('#btnMaintenance').css('text-decoration', 'none');
    $('#btnRepair').css('text-decoration', 'none');
  };

  onGridReady = params => {
    this.gridColumnApi = params.columnApi;
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    let resultArr = [];
    let type = '';
    if (this.state.tabSelection == 'three') {
      type = 'LM6';
    } else if (this.state.tabSelection == 'two') {
      type = 'LD90';
    } else if (this.state.tabSelection == 'one') {
      type = 'LD30';
    } else if (this.state.tabSelection == 'four') {
      type = 'LD7';
    }
    let advisor = this.props.session.serviceAdvisor.includes('All')
      ? null
      : this.props.session.serviceAdvisor;

    getScatterPlotReportData(type, advisor, result => {
      this.setState({ isLoading: false });
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardScatterplot
          .statelessDbdKpiScorecardKpiScorecardScatterplots
      ) {
        resultArr =
          result.data.statelessDbdKpiScorecardGetKpiScorecardScatterplot
            .statelessDbdKpiScorecardKpiScorecardScatterplots;

        let dataArrComp = [];
        let dataArrMaint = [];
        let dataArrRep = [];
        for (let i = 0; i <= 9; i++) {
          lodash
            .map(resultArr, 'col' + i)
            .every(item => dataArrComp.push(item));
          lodash
            .map(resultArr, 'colm' + i)
            .every(item => dataArrMaint.push(item));
          lodash
            .map(resultArr, 'colr' + i)
            .every(item => dataArrRep.push(item));
        }
        if (
          dataArrComp.every(element => element == 0) &&
          dataArrMaint.every(element => element == 0) &&
          dataArrRep.every(element => element == 0)
        ) {
          resultArr = [];
        }
        this.setState({
          rowData: resultArr,
          allData: resultArr
        });
        if (this.state.compClicked) {
          this.setState({
            compClicked: false
          });
          this.handleLegendClick('Competitive');
        }
        if (this.state.maintClicked) {
          this.setState({
            maintClicked: false
          });
          this.handleLegendClick('Maintenance');
        }
        if (this.state.repairClicked) {
          this.setState({
            repairClicked: false
          });
          this.handleLegendClick('Repair');
        }
      }
    });
  }
  handleTabChange = (event, Value) => {
    this.setState({ tabSelection: Value });
  };
  handleLegendClick = type => {
    if (type == 'Competitive') {
      $('#btnCompetitive').css('text-decoration', 'line-through');
      if (this.state.compClicked == false) {
        this.setState({ compClicked: true });
        let resultArr = this.state.rowData;
        if (resultArr.length > 0) {
          for (let i = 0; i <= 9; i++) {
            resultArr = resultArr.map(item => {
              return lodash.omit(item, 'col' + i);
            });
          }
        }
        this.setState({
          rowData: resultArr
        });
      } else {
        $('#btnCompetitive').css('text-decoration', 'none');
        this.setState({ compClicked: false });
        let resultArr = this.state.allData;
        if (resultArr.length > 0) {
          if (
            this.state.rowData[0].colm0 == undefined &&
            this.state.rowData[0].colr0 == undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, ['colm' + i, 'colr' + i]);
              });
            }
          } else if (
            this.state.rowData[0].colm0 == undefined &&
            this.state.rowData[0].colr0 != undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, 'colm' + i);
              });
            }
          } else if (
            this.state.rowData[0].colm0 != undefined &&
            this.state.rowData[0].colr0 == undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, 'colr' + i);
              });
            }
          }
        }
        this.setState({
          rowData: resultArr
        });
      }
    } else if (type == 'Maintenance') {
      $('#btnMaintenance').css('text-decoration', 'line-through');
      if (this.state.maintClicked == false) {
        this.setState({ maintClicked: true });
        let resultArr = this.state.rowData;
        if (resultArr.length > 0) {
          for (let i = 0; i <= 9; i++) {
            resultArr = resultArr.map(item => {
              return lodash.omit(item, 'colm' + i);
            });
          }
        }
        this.setState({
          rowData: resultArr
        });
      } else {
        $('#btnMaintenance').css('text-decoration', 'none');
        this.setState({ maintClicked: false });
        let resultArr = this.state.allData;
        if (resultArr.length > 0) {
          if (
            this.state.rowData[0].col0 == undefined &&
            this.state.rowData[0].colr0 == undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, ['col' + i, 'colr' + i]);
              });
            }
          } else if (
            this.state.rowData[0].col0 == undefined &&
            this.state.rowData[0].colr0 != undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, 'col' + i);
              });
            }
          } else if (
            this.state.rowData[0].col0 != undefined &&
            this.state.rowData[0].colr0 == undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, 'colr' + i);
              });
            }
          }
        }
        this.setState({
          rowData: resultArr
        });
      }
    } else if (type == 'Repair') {
      $('#btnRepair').css('text-decoration', 'line-through');
      if (this.state.repairClicked == false) {
        this.setState({ repairClicked: true });
        let resultArr = this.state.rowData;
        if (resultArr.length > 0) {
          for (let i = 0; i <= 9; i++) {
            resultArr = resultArr.map(item => {
              return lodash.omit(item, 'colr' + i);
            });
          }
        }
        this.setState({
          rowData: resultArr
        });
      } else {
        $('#btnRepair').css('text-decoration', 'none');
        this.setState({ repairClicked: false });
        let resultArr = this.state.allData;
        if (resultArr.length > 0) {
          if (
            this.state.rowData[0].colm0 == undefined &&
            this.state.rowData[0].col0 == undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, ['colm' + i, 'col' + i]);
              });
            }
          } else if (
            this.state.rowData[0].colm0 == undefined &&
            this.state.rowData[0].col0 != undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, 'colm' + i);
              });
            }
          } else if (
            this.state.rowData[0].colm0 != undefined &&
            this.state.rowData[0].col0 == undefined
          ) {
            for (let i = 0; i <= 9; i++) {
              resultArr = resultArr.map(item => {
                return lodash.omit(item, 'col' + i);
              });
            }
          }
        }
        this.setState({
          rowData: resultArr
        });
      }
    }
  };
  onBtExportThree = () => {
    var params = {
      sheetName: 'Job Count Grid - Last 6 Months',
      fileName: 'Job Count Grid - Last 6 Months',
      processCellCallback: params => this.processHoursSold(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Job Count Grid - Last 6 Months'
            },
            mergeAcross: 3
          },
          {
            styleId: 'smallHeader',
            data: {
              type: 'String',
              value:
                this.state.compClicked &&
                this.state.maintClicked &&
                this.state.repairClicked
                  ? ''
                  : this.state.compClicked && this.state.maintClicked
                  ? 'Repair'
                  : this.state.compClicked && this.state.repairClicked
                  ? 'Maintenance'
                  : this.state.repairClicked && this.state.maintClicked
                  ? 'Competitive'
                  : this.state.compClicked
                  ? 'Maintenance | Repair'
                  : this.state.maintClicked
                  ? 'Competitive | Repair'
                  : this.state.repairClicked
                  ? 'Competitive | Maintenance'
                  : 'Competitive | Maintenance | Repair'
            },
            mergeAcross: 6
          }
        ]
      ]
    };
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onBtExportTwo = () => {
    var params = {
      sheetName: 'Job Count Grid - Last 90 days',
      fileName: 'Job Count Grid - Last 90 days',
      processCellCallback: params => this.processHoursSold(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Job Count Grid - Last 90 days'
            },
            mergeAcross: 3
          },
          {
            styleId: 'smallHeader',
            data: {
              type: 'String',
              value:
                this.state.compClicked &&
                this.state.maintClicked &&
                this.state.repairClicked
                  ? ''
                  : this.state.compClicked && this.state.maintClicked
                  ? 'Repair'
                  : this.state.compClicked && this.state.repairClicked
                  ? 'Maintenance'
                  : this.state.repairClicked && this.state.maintClicked
                  ? 'Competitive'
                  : this.state.compClicked
                  ? 'Maintenance | Repair'
                  : this.state.maintClicked
                  ? 'Competitive | Repair'
                  : this.state.repairClicked
                  ? 'Competitive | Maintenance'
                  : 'Competitive | Maintenance | Repair'
            },
            mergeAcross: 6
          }
        ]
      ]
    };
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onBtExportOne = () => {
    var params = {
      sheetName: 'Job Count Grid - Last 30 days',
      fileName: 'Job Count Grid - Last 30 days',
      processCellCallback: params => this.processHoursSold(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Job Count Grid - Last 30 days'
            },
            mergeAcross: 3
          },
          {
            styleId: 'smallHeader',
            data: {
              type: 'String',
              value:
                this.state.compClicked &&
                this.state.maintClicked &&
                this.state.repairClicked
                  ? ''
                  : this.state.compClicked && this.state.maintClicked
                  ? 'Repair'
                  : this.state.compClicked && this.state.repairClicked
                  ? 'Maintenance'
                  : this.state.repairClicked && this.state.maintClicked
                  ? 'Competitive'
                  : this.state.compClicked
                  ? 'Maintenance | Repair'
                  : this.state.maintClicked
                  ? 'Competitive | Repair'
                  : this.state.repairClicked
                  ? 'Competitive | Maintenance'
                  : 'Competitive | Maintenance | Repair'
            },
            mergeAcross: 3
          }
        ]
      ]
    };
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  onBtExportFour = () => {
    var params = {
      sheetName: 'Job Count Grid - Last 7 days',
      fileName: 'Job Count Grid - Last 7 days',
      processCellCallback: params => this.processHoursSold(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Job Count Grid - Last 7 days'
            },
            mergeAcross: 3
          },
          {
            styleId: 'smallHeader',
            data: {
              type: 'String',
              value:
                this.state.compClicked &&
                this.state.maintClicked &&
                this.state.repairClicked
                  ? ''
                  : this.state.compClicked && this.state.maintClicked
                  ? 'Repair'
                  : this.state.compClicked && this.state.repairClicked
                  ? 'Maintenance'
                  : this.state.repairClicked && this.state.maintClicked
                  ? 'Competitive'
                  : this.state.compClicked
                  ? 'Maintenance | Repair'
                  : this.state.maintClicked
                  ? 'Competitive | Repair'
                  : this.state.repairClicked
                  ? 'Competitive | Maintenance'
                  : 'Competitive | Maintenance | Repair'
            },
            mergeAcross: 3
          }
        ]
      ]
    };
    this.state.rawGridApi.exportDataAsExcel(params);
  };

  processHoursSold = params => {
    if (params.column.colId == 'coldesc' && params.value == 20) {
      return '20+';
    } else if (params.column.colId == 'coldesc') {
      return params.value;
    } else if (
      params.column.colId == 'col0' ||
      params.column.colId == 'col1' ||
      params.column.colId == 'col2' ||
      params.column.colId == 'col3' ||
      params.column.colId == 'col4' ||
      params.column.colId == 'col5' ||
      params.column.colId == 'col6' ||
      params.column.colId == 'col7' ||
      params.column.colId == 'col8' ||
      params.column.colId == 'col9'
    ) {
      if (
        this.state.compClicked &&
        this.state.maintClicked &&
        this.state.repairClicked
      ) {
        return params.value;
      } else if (this.state.compClicked) {
        if (this.state.compClicked && this.state.maintClicked) {
          return params.node.data['colr' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        } else if (this.state.compClicked && this.state.repairClicked) {
          return (
            params.node.data['colm' + params.column.colId.split('col')[1]]
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' | '
          );
        }
        return (
          params.node.data['colm' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
          ' | ' +
          params.node.data['colr' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        );
      } else if (this.state.maintClicked) {
        if (this.state.maintClicked && this.state.compClicked) {
          return params.node.data['colr' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        } else if (this.state.maintClicked && this.state.repairClicked) {
          return (
            params.node.data['col' + params.column.colId.split('col')[1]]
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' | '
          );
        }

        return (
          params.node.data['col' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
          ' | ' +
          params.node.data['colr' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        );
      } else if (this.state.repairClicked) {
        if (this.state.repairClicked && this.state.compClicked) {
          return (
            params.node.data['colm' + params.column.colId.split('col')[1]]
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' | '
          );
        } else if (this.state.repairClicked && this.state.maintClicked) {
          return (
            params.node.data['col' + params.column.colId.split('col')[1]]
              .toString()
              .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' | '
          );
        }
        return (
          params.node.data['col' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
          ' | ' +
          params.node.data['colm' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
          ' | '
        );
      } else
        return (
          params.node.data['col' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
          ' | ' +
          params.node.data['colm' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
          ' | ' +
          params.node.data['colr' + params.column.colId.split('col')[1]]
            .toString()
            .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        );
    }
  };

  renderBackButton = () => {
    this.props.history.push({
      pathname: '/LaborItemization',
      state: {
        tabSelection: this.state.tabSelection,
        compClicked: this.state.compClickedPrev,
        maintClicked: this.state.maintClickedPrev,
        repairClicked: this.state.repairClickedPrev,
        data: this.props.history.location.state.itemizationData,
        isFrom: 'opcodes'
      }
    });
  };
  render() {
    const { classes } = this.props;
    let pageType =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.pageType &&
      this.props.history.location.state.pageType
        ? this.props.history.location.state.pageType
        : '';
    return (
      <div>
        <Page title={'Job Count Grid'}></Page>
        <Paper square style={{ margin: '0px 8px' }}>
          <ReferenceTopbar
            props={this.props}
            title={'Job Count Grid'}
            pageType={pageType}
            handleBackBtn={this.renderBackButton}
            exportReport={
              this.state.tabSelection == 'one'
                ? this.onBtExportOne
                : this.state.tabSelection == 'two'
                ? this.onBtExportTwo
                : this.state.tabSelection == 'three'
                ? this.onBtExportThree
                : this.onBtExportFour
            }
            setResetDashboard={this.resetRawData}
          />
        </Paper>
        <Paper
          square
          style={{
            margin: 8,
            marginLeft: 8,
            height: 50,
            paddingTop: 1,
            paddingLeft: 0,
            display: 'flex'
          }}
        >
          <Grid
            container
            className={clsx(this.props.titleContainer, 'reset-dashboard')}
          >
            <Grid item xs={8}>
              <Tabs
                variant="scrollable"
                scrollButtons="auto"
                value={
                  this.state.tabSelection ? this.state.tabSelection : 'three'
                }
                className={classes.tabs}
                onChange={this.handleTabChange}
                indicatorColor="secondary"
                textColor="secondary"
                TabIndicatorProps={{ style: { display: 'none' } }}
              >
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Last 6 Months</div>}
                  id="Last 6 Months"
                  value="three"
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Last 90 Days</div>}
                  id="Last 90 Days"
                  value="two"
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Last 30 Days</div>}
                  id="Last 30 Days"
                  value="one"
                />
                <Tab
                  style={{ textTransform: 'none' }}
                  label={<div>Last 7 Days</div>}
                  id="Last 7 Days"
                  value="four"
                />
              </Tabs>
            </Grid>

            <Grid item xs={4} className={classes.jobGrid}>
              <Button
                size="small"
                id="btnCompetitive"
                className={classes.btnCompetitive}
                onClick={() => this.handleLegendClick('Competitive')}
              >
                Competitive
              </Button>
              |
              <Button
                size="small"
                id="btnMaintenance"
                className={classes.btnMaintenance}
                onClick={() => this.handleLegendClick('Maintenance')}
              >
                Maintenance
              </Button>
              |
              <Button
                size="small"
                id="btnRepair"
                className={classes.btnRepair}
                onClick={() => this.handleLegendClick('Repair')}
              >
                Repair
              </Button>
              {/* <span onClick={this.handleLegendClick} style={{color: '#0389fc', fontWeight: 500, fontSize: 13, fontFamily: 'Roboto'}}> Competitive </span>| */}
              {/* <span style={{color: '#e14f4f', fontWeight: 500, fontSize: 13, fontFamily: 'Roboto'}}> Maintenance </span>|
                  <span style={{color: '#35af55', fontWeight: 500, fontSize: 13, fontFamily: 'Roboto'}}> Repair </span> */}
              {/* <span style={{color: 'blue'}}> Competitive </span>|
                  <span style={{color: 'red'}}> Maintenance </span>|
                  <span style={{color: 'green'}}> Repair </span> */}
            </Grid>
          </Grid>
        </Paper>
        {this.state.isLoading == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab-job-count"
          className="ag-theme-balham"
          style={{
            //height: '410px',
            height: window.innerHeight - 203 + 'px',
            // height:(window.innerHeight-215)+'px',
            alignContent: 'center',
            marginLeft: '8px',
            display: this.state.isLoading == true ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '410px',
              width: '100%'
            }}
            enableRangeSelection={false}
            animateRows={true}
            enableCharts={true}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            excelStyles={this.state.excelStyles}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader={true}
            rowData={this.state.rowData}
            sortingOrder={this.sortingOrder}
            tooltipShowDelay={0}
            // floatingFilter={true}
            suppressRowClickSelection={true}
            headerHeight={this.state.headerHeight}
            onFilterChanged={this.onFilterChanged}
            suppressContextMenu={true}
            components={this.state.components}
            overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
            suppressCellSelection={true}
            suppressDragLeaveHidesColumns={true}
          />
        </div>
      </div>
    );
  }
}
const jobCountCellRenderer = params => {
  if (params.valueFormatted != null && params.valueFormatted != '') {
    let data = params.valueFormatted.split('|');
    if (
      params.data.col0 == undefined &&
      params.data.colm0 != undefined &&
      params.data.colr0 != undefined
    ) {
      return (
        '<span style="color:#e14f4f; font-weight: 650; display: inline-block; width: 40px; text-align: center">' +
        data[0].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span> | <span style="color:#35af55; font-weight: 650; display: inline-block; width: 40px; text-align: center">' +
        data[1].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span>'
      );
    } else if (
      params.data.colm0 == undefined &&
      params.data.col0 != undefined &&
      params.data.colr0 != undefined
    ) {
      return (
        '<span style="color:#0389fc; font-weight: 650; display: inline-block; width: 40px; text-align: center">' +
        data[0].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span> | <span style="color:#35af55; font-weight: 650; display: inline-block; width: 40px; text-align: center">' +
        data[1].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span>'
      );
    } else if (
      params.data.colr0 == undefined &&
      params.data.colm0 != undefined &&
      params.data.col0 != undefined
    ) {
      return (
        '<span style="color:#0389fc; font-weight: 650; display: inline-block; width: 40px; text-align: center">' +
        data[0].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span> | <span style="color:#e14f4f; font-weight: 650; display: inline-block; width: 40px; text-align: center">' +
        data[1].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span>'
      );
    } else if (
      params.data.col0 == undefined &&
      params.data.colm0 == undefined &&
      params.data.colr0 != undefined
    ) {
      return (
        '<span style="color:#35af55; font-weight: 650; display: inline-block; width: 100px; text-align: center">' +
        data[1].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span>'
      );
    } else if (
      params.data.col0 == undefined &&
      params.data.colr0 == undefined &&
      params.data.colm0 != undefined
    ) {
      return (
        '<span style="color:#e14f4f; font-weight: 650; display: inline-block; width: 100px; text-align: center">' +
        data[0].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span>'
      );
    } else if (
      params.data.colm0 == undefined &&
      params.data.colr0 == undefined &&
      params.data.col0 != undefined
    ) {
      return (
        '<span style="color:#0389fc; font-weight: 650; display: inline-block; width: 100px; text-align: center">' +
        data[0].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span>'
      );
    } else if (
      params.data.colm0 != undefined &&
      params.data.colr0 != undefined &&
      params.data.col0 != undefined
    ) {
      // return '<span style="color:#0389fc; font-weight: 650;">'+ data[0]+'</span> | <span style="color:#e14f4f; font-weight: 650;">'+
      // data[1]+'</span> | <span style="color:#35af55; font-weight: 650; ">'+data[2]+'</span>';
      return (
        '<span style="color:#0389fc; font-weight: 650; display: inline-block; width: 38px; text-align: center ">' +
        data[0].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span> | ' +
        '<span style="color:#e14f4f; font-weight: 650; display: inline-block; width: 38px; text-align: center ">' +
        data[1].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span> | ' +
        '<span style="color:#35af55; font-weight: 650; display: inline-block; width: 38px; text-align: center ">' +
        data[2].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') +
        '</span>'
      );
    }
  }
};

const styles = theme => ({
  formControl: {
    margin: theme.spacing(4),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    marginTop: 7
  },
  dataAsOf: {
    marginRight: 32,
    float: 'left',
    marginTop: 1,
    marginLeft: '1%'
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3,
    marginRight: 8
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  inputText: {
    width: 155,
    textAlign: 'left'
  },
  inputTextRate: {
    width: 97,
    textAlign: 'left'
  },
  inputValue: {
    marginRight: 11
  },
  tabs: {
    // "& button[aria-selected='true']": {
    //   border: "5px solid red"
    // }
    '& button': {
      padding: 5
    },
    "& button[aria-selected='true']": {
      color: theme.palette.primary.main,
      textTransform: 'none',
      border: 'solid 1px',
      borderColor: theme.palette.primary.main,
      backgroundColor: theme.palette.primary.active
      // backgroundColor: theme.palette.primary.active
    }
  },
  jobGrid: {
    textAlign: 'start',
    marginTop: 10,
    '@media (min-width: 2560px)': {
      marginLeft: '-40% !important'
    },
    '@media (max-width: 2304px)': {
      marginLeft: '-30% !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '-21% !important'
    },
    '@media (max-width: 1680px)': {
      marginLeft: '-15% !important'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '-8% !important'
    }
  },
  btnCompetitive: {
    color: '#0389fc',
    fontWeight: 500,
    fontSize: 13,
    fontFamily: 'Roboto',
    textTransform: 'capitalize',
    backgroundColor: 'white',
    '&:hover': {
      backgroundColor: 'white'
    },
    '&:active': {
      textDecoration: 'line-through',
      backgroundColor: 'white'
    }
  },

  btnMaintenance: {
    color: '#e14f4f',
    fontWeight: 500,
    fontSize: 13,
    fontFamily: 'Roboto',
    textTransform: 'capitalize',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  btnRepair: {
    color: '#35af55',
    fontWeight: 500,
    fontSize: 13,
    fontFamily: 'Roboto',
    textTransform: 'capitalize',
    '&:hover': {
      backgroundColor: 'white'
    }
  }
});

export default withStyles(styles)(ScatterReport);
