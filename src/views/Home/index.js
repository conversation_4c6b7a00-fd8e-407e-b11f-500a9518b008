import React, { useState, useEffect } from 'react';
import { makeStyles } from '@material-ui/styles';
import $ from 'jquery';
import Page from 'src/components/Page';
import Charts from './Charts';
import { useHistory } from 'react-router';
import moment from 'moment';
import { getTimeZone } from '../../utils/Utils';
import { CircularProgress, Grid } from '@material-ui/core';
import { getGridorMatrixPayTypeDetails } from 'src/utils/hasuraServices';
import { setLaborGridTypes, setPartsMatrixTypes } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

function Home() {
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const history = useHistory();
  const [isLoading, setLoading] = useState(false);
  const [isLoadingParts, setLoadingParts] = useState(false);
  const dispatch = useDispatch();

  $('#navBarDiv .active-menu').removeClass('active-menu');
  $('#Home').addClass('active-menu');
  var timezone = getTimeZone();

  useEffect(() => {
    console.log('store-----changes---', session.storeSelected);
    getPartsPayTypeList();
    let data = [];
    data.push('Customer');
    setLoading(false);
    getGridorMatrixPayTypeDetails('paytype_grid', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (result.includes('Highline')) {
          result.reverse();
        }
        if (
          result.includes('Diesel') ||
          result.includes('HD & Hybrid') ||
          result.includes('Electric')
        ) {
          result.reverse();
        }
        if (
          result.includes('Honda Grid') &&
          result.includes('Volvo Grid') &&
          result.includes('Merc-Benz Grid')
        ) {
          result = ['Honda Grid', 'Volvo Grid', 'Merc-Benz Grid'];
        }
        if (
          result.includes('25-5500/Dsl') &&
          result.includes('Car/Lt Trk') &&
          result.includes('Flt-Sptr')
        ) {
          result = ['Car/Lt Trk', '25-5500/Dsl', 'Flt-Sptr'];
        }
        if (
          result.includes('Hyundai') &&
          result.includes('Genesis') &&
          result.includes('Customer')
        ) {
          result = ['Hyundai', 'Genesis', 'Customer'];
        }
        if (
          result.includes('EV') &&
          result.includes('Medium/HD') &&
          result.includes('Standard')
        ) {
          result = ['Standard', 'Medium/HD', 'EV'];
        }
        if (result.includes('HD') && result.includes('Standard')) {
          result = ['Standard', 'HD'];
        }
        localStorage.setItem('laborGridTypes', JSON.stringify(result));
        dispatch(setLaborGridTypes(result));
        setLoading(true);
      } else {
        localStorage.setItem('laborGridTypes', JSON.stringify(data));
        dispatch(setLaborGridTypes(result));
        setLoading(true);
      }
    });
  }, [session.storeSelected]);
  function getPartsPayTypeList() {
    let data = [];
    data.push('Customer');
    setLoadingParts(false);
    getGridorMatrixPayTypeDetails('paytype_matrix', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (result.includes('Highline')) {
          result.reverse();
        }
        localStorage.setItem('partsMatrixTypes', JSON.stringify(result));
        dispatch(setPartsMatrixTypes(result));
        setLoadingParts(true);
      } else {
        localStorage.setItem('partsMatrixTypes', JSON.stringify(data));
        dispatch(setPartsMatrixTypes(result));
        setLoadingParts(true);
      }
    });
  }
  return (
    <Page className={classes.root} title="Home">
      {localStorage.getItem('versionFlag') == 'TRUE' ? (
        <Redirect to="/errors/error-404" />
      ) : !isLoading || !isLoadingParts ? null : (
        <Charts history={history} timezone={timezone} session={session} />
      )}
    </Page>
  );
}

export default Home;
