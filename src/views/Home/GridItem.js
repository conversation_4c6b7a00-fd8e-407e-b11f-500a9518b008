import React from 'react';
import KPISummaryCharts from 'src/components/charts/KPISummaryCharts';
// import style from './styles.css'
const GridItem = ({ chartId, data, ...rest }) => {
  return (
    <span className="KpiCharts">
      <div id={'chartContainer_' + data.chartId} {...rest}>
        <div className="grid-item__graph">
          {(data.chartId == 1341 ||
            data.chartId == 1342 ||
            data.chartId == 1343) && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              LaborGpRo={data.data[0].LaborGpRo}
              PartsGpRo={data.data[0].PartsGpRo}
              TotalGpRo={data.data[0].TotalGpRo}
            />
          )}
          {(data.chartId == 1335 ||
            data.chartId == 1336 ||
            data.chartId == 1340) && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              allCWITData={data.data[0].allCWITData}
              ROShareData={data.data[0].ROShareData}
              AvgAgeMiles={data.data[0].AvgAgeMiles}
            />
          )}
          {data.chartId == 1346 && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              LaborGrid={data.data[0].LaborGrid}
              internalMisses={rest.input.props.internalMisses}
            />
          )}
          {data.chartId == 1353 && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              PartsGrid={data.data[0].PartsGrid}
              internalMisses={rest.input.props.internalMisses}
            />
          )}
          {(data.chartId == 1337 || data.chartId == 1338) && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              LineROLtSixtyK={data.data[0].LineROLtSixtyK}
              LineROGtSixtyK={data.data[0].LineROGtSixtyK}
            />
          )}
          {data.chartId == 1339 && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              FlatRateHrs={data.data[0].FlatRateHrs}
            />
          )}
          {data.chartId == 1344 && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              WorkMix={data.data[0].WorkMix}
            />
          )}
          {data.chartId == 1351 && (
            <KPISummaryCharts
              toggleOption={rest.input.props.toggleOption}
              chartId={data.chartId}
              LineRO={data.data[0].LineRO}
            />
          )}
          {/*  {data.chartId == 1339 && (
          <KPISummaryCharts
            toggleOption={rest.input.props.toggleOption}
            chartId={data.chartId}
            FlatRateHrs={data.data[0].FlatRateHrs}
          />
        )}
        {(data.chartId == 1337 || data.chartId == 1338) && (
          <KPISummaryCharts
            toggleOption={rest.input.props.toggleOption}
            chartId={data.chartId}
            LineROLtSixtyK={data.data[0].LineROLtSixtyK}
            LineROGtSixtyK={data.data[0].LineROGtSixtyK}
          />
        )}
        {data.chartId == 1351 && (
          <KPISummaryCharts
            toggleOption={rest.input.props.toggleOption}
            chartId={data.chartId}
            LineRO={data.data[0].LineRO}
          />
        )}
        {data.chartId == 1344 && (
          <KPISummaryCharts
            toggleOption={rest.input.props.toggleOption}
            chartId={data.chartId}
            WorkMix={data.data[0].WorkMix}
          />
        )}
        {data.chartId == 1346 && (
          <KPISummaryCharts
            toggleOption={rest.input.props.toggleOption}
            chartId={data.chartId}
            LaborGrid={data.data[0].LaborGrid}
            internalMisses={rest.input.props.internalMisses}
          />
        )}
        {data.chartId == 1353 && (
          <KPISummaryCharts
            toggleOption={rest.input.props.toggleOption}
            chartId={data.chartId}
            PartsGrid={data.data[0].PartsGrid}
            internalMisses={rest.input.props.internalMisses}
          />
        )} */}
        </div>
      </div>
    </span>
  );
};

export default GridItem;
