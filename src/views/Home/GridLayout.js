import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { connect } from 'react-redux';

import GridItemContainer from './GridItemContainer';

import { setBreakPoint } from './actions/app-actions';

const ResponsiveGridLayout = WidthProvider(Responsive);

class GridLayout extends Component {
  handleBreakPointChange = breakpoint => {
    this.props.setBreakPoint(breakpoint);
  };

  render() {
    const { data, layouts } = this.props;
    return (
      <ResponsiveGridLayout
        className={'layout'}
        layouts={layouts}
        onBreakpointChange={this.handleBreakPointChange}
        isDraggable
        isRearrangeable
        isResizable
        draggableHandle=".grid-item__title"
        //breakpoints={{ lg: 1280, md: 992, sm: 767, xs: 480, xxs: 0 }}
        breakpoints={{ lg: 1480, md: 1280, sm: 992, xs: 767, xxs: 480 }}
        cols={{ lg: 12, md: 10, sm: 10, xs: 6, xxs: 4 }}
        // cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
      >
        {Object.keys(data).map(item => (
          <GridItemContainer
            key={item}
            item={item}
            data={data[item]}
            input={this.props}
          />
        ))}
      </ResponsiveGridLayout>
    );
  }
}

GridLayout.propTypes = {
  data: PropTypes.arrayOf(PropTypes.string).isRequired,
  layouts: PropTypes.object.isRequired
};

export default connect(null, { setBreakPoint })(GridLayout);
