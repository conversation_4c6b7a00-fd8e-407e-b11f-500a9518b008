import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { withStyles } from '@material-ui/core/styles';
import Typography from '@material-ui/core/Typography';
import {
  MenuItem,
  Select,
  Paper,
  FormControl,
  Divider,
  Tooltip,
  IconButton,
  Button
} from '@material-ui/core';
import Link from '@material-ui/core/Link';
import ExportIcon from '@material-ui/icons/GetApp';
import moment from 'moment';
import {
  getDataForCWITotalcharts,
  getDataForKPIROSharechart,
  getDataForKPILineROLtSixtyK,
  getDataForKPILineROGtSixtyK,
  getDataForKPIAvgAgeMiles,
  getDataForKPIFlatRateHrs,
  getDataForKPILaborGpRo,
  getDataForKPIPartsGpRo,
  getDataForKPITotalGpRo,
  getDataForKPIWorkmix,
  getDataForKPILaborGrid,
  getDataForKPILineRO,
  getDataForKPIPartsGrid,
  getLatestClosedDate
} from 'src/utils/hasuraServices';
import { useSelector, useDispatch } from 'react-redux';
import clsx from 'clsx';
import KpiSummary from './KpiSummary';
import { CircularProgress, Grid } from '@material-ui/core';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Dashboard from './Dashboard';
import {
  setKpiToggle,
  setInternalKpiToggle,
  setKpiHomeToggle
} from 'src/actions';
import { getNextMonth, getYearValue } from 'src/utils/Utils';
import DashboardExtended from '../KPI/DashboardExtended';
import { withKeycloak } from '@react-keycloak/web';
import ZoomOutMapIcon from '@material-ui/icons/ZoomOutMap';
import $ from 'jquery';
import AppsIcon from '@material-ui/icons/Apps';
import FormatListBulletedIcon from '@material-ui/icons/FormatListBulleted';
import { i } from 'react-dom-factories';

var lodash = require('lodash');

const styles = theme => ({
  root: {
    flexGrow: 1,
    width: '99%',
    marginLeft: 5
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary,
    padding: '12px !important'
  },
  paper1: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.primary.main,
    margin: '12px 6px',
    maxWidth: '99%'
  },
  card: {
    width: '100%',
    height: '50vh',
    maxWidth: '100%',
    overflow: 'visible',
    display: 'flex',
    position: 'relative',
    // backgroundColor: '#c0d1df',
    backgroundColor: theme.palette.primary.light,
    border: 'solid #003d6b',
    marginTop: '-3px',
    '& > *': {
      flexGrow: 1,
      flexBasis: '50%',
      width: '50%'
    }
  },
  content: {
    padding: theme.spacing(8, 4, 3, 4)
  },
  headerItem: {
    display: 'flex',
    justifyContent: 'space-between',
    marginTop: -12
  },
  h1: {
    color: '#000',
    position: 'absolute',
    textAlign: 'center',
    left: '28%',
    fontWeight: 'bold',
    marginTop: '45px'
  },
  p: {
    color: '#fff'
  },
  headerDropdown: {
    // marginTop: -6
    marginTop: 3
  },
  divider: {
    backgroundColor: '#9b8c8ca1',
    fontWeight: 'bold'
  },
  container: {
    gridGap: 10
  },
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  },
  dataAsOfLabel: {
    marginLeft: 'auto',
    // marginTop: 10,
    marginTop: 19,
    marginRight: 10
  },
  linkItem: {
    cursor: 'pointer',
    marginTop: 10
  },
  reportButton: {
    height: 24,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 136
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 122
    }
  },
  reportButtonSelect: {
    height: 24,
    marginLeft: 4,
    color: '#757575',
    '@media (min-width: 2560px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 2304px)': {
      fontSize: 10,
      width: 136
    },
    '@media (max-width: 1920px)': {
      fontSize: 14,
      width: 136
    },
    '@media (max-width: 1440px)': {
      fontSize: 12,
      width: 122
    },
    borderColor: theme.palette.primary.main,
    background: 'rgb(221, 234, 244)',
    color: 'rgb(0, 61, 107)'
  },
  ButtonSelect: {
    lineHeight: 1.5,
    '@media (min-width: 2560px)': {
      marginTop: '3px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '2px !important',
      width: 180,
      marginBottom: '3px !important'
    },
    '@media (max-width: 1920px)': {
      width: 183,
      marginTop: '1px !important',
      marginBottom: '3px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '0px !important',
      width: 154,
      marginBottom: '2x !important'
    }
  }
});
function Header(props) {
  const session = useSelector(state => state.session);
  // let laborGridTypes = JSON.parse(localStorage.getItem('laborGridTypes'));
  let laborGridTypes = session.allLaborGridTypes;
  let partsMatrixTypes = session.allPartsMatrixTypes;
  let kpiDataToggle =
    localStorage.getItem('kpiDataStatus') == 1 ? 'MTD' : 'LMONTH';
  // let partsMatrixTypes = JSON.parse(localStorage.getItem('partsMatrixTypes'));
  let toggle =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.toggleOptions
      ? props.history.location.state.toggleOptions
      : props.history &&
        props.history.location &&
        (props.history.location.state == undefined ||
          props.history.location.state == null)
      ? kpiDataToggle
      : props.selectedToggle
      ? props.selectedToggle
      : kpiDataToggle;

  let payType =
    props.history && props.history.location && props.history.location.state
      ? props.history.location.state.payType
      : payTypeListParts && payTypeListParts.length > 0
      ? payTypeListParts[0].charAt(0)
      : 'C';
  let intToggle = false;
  if (payType == undefined) {
    payType = 'C';
  }
  if (payType == 'C') {
    intToggle = false;
  } else {
    intToggle = true;
  }
  let laborGrid = laborGridTypes;
  let gridTypeText =
    props.history && props.history.location && props.history.location.state
      ? props.history.location.state.gridType
      : laborGrid && laborGrid.length > 0
      ? laborGrid[0]
      : '';
  if (gridTypeText && laborGrid.length > 0) {
    if (laborGrid.includes(gridTypeText)) {
      gridTypeText = gridTypeText;
    } else {
      gridTypeText = laborGrid[0];
    }
  }
  if (gridTypeText == undefined) {
    gridTypeText = laborGrid && laborGrid.length > 0 ? laborGrid[0] : 'C';
  }
  let extendedStatus = props.parent == 'Home' ? false : true;
  let closedDateValue = localStorage.getItem('closedDate')
    ? localStorage.getItem('closedDate')
    : props.closedDate;
  const { classes } = props;
  const [toggleOption, setToggleOption] = useState(toggle);
  var timeZone = props.timezone ? props.timezone : '+05:30';
  const [CWITData, setCWITData] = useState([]);
  const [CWITAllData, setCWITAllDataAll] = useState([]);
  const [ROShareData, setROShareData] = useState([]);
  const [LineROLtSixtyK, setLineROLtSixtyK] = useState([]);
  const [LineROGtSixtyK, setLineROGtSixtyK] = useState([]);
  const [AvgAgeMiles, setAvgAgeMiles] = useState([]);
  const [FlatRateHrs, setFlatRateHrs] = useState([]);
  const [LaborGpRo, setLaborGpRo] = useState([]);
  const [PartsGpRo, setPartsGpRo] = useState([]);
  const [TotalGpRo, setTotalGpRo] = useState([]);
  const [WorkMix, setWorkMix] = useState([]);
  const [LaborGrid, setLaborGrid] = useState([]);
  const [PartsGrid, setPartsGrid] = useState([]);
  const [LineRO, setLineRO] = useState([]);
  const [isLoading, setLoading] = useState(true);
  const [parent, setParent] = useState(props.parent);
  const [isLoadingComplete, setLoadingComplete] = useState(false);
  // const session = useSelector(state => state.session);
  const [internalToggle, setInternalToggle] = useState(intToggle);
  const [internalMisses, setInternalMisses] = useState(intToggle);
  const [gridType, setGridType] = useState(gridTypeText);
  const [payTypeList, setPayTypeList] = useState(laborGridTypes);
  const [payTypeListParts, setPayTypeListParts] = useState(partsMatrixTypes);
  const [chartList, setChartList] = useState(
    JSON.parse(global.localStorage.getItem('chart-master'))
  );
  const [closedDate, setClosedDate] = useState(closedDateValue);
  const [expandedView, setExpandedView] = useState(extendedStatus);
  const dispatch = useDispatch();
  // localStorage.setItem('laborGridTypes',JSON.stringify(props.payTypeList));
  // localStorage.setItem('partsMatrixTypes',JSON.stringify(props.payTypeListParts));
  const HtmlTooltip = withStyles(theme => ({
    arrow: {
      color: theme.palette.common.black
    },
    tooltip: {
      minWidth: 100,
      maxWidth: 150,
      zIndex: '99',
      textAlign: 'left',
      fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
      fontSize: '14px',
      fontWeight: 'normal',
      color: '#003d6b',
      border: '1px solid #003d6b'
    }
  }))(Tooltip);

  useEffect(() => {
    let orderedData;
    if (chartList) {
      let filteredResult = chartList.filter(
        item => item.dbdName == 'KPI' && item.parentId == null
      );
      orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    }
    setCWITData([]);
    setLineROLtSixtyK([]);
    setLineROGtSixtyK([]);
    setAvgAgeMiles([]);
    setFlatRateHrs([]);
    setLaborGpRo([]);
    setTotalGpRo([]);
    setWorkMix([]);
    setLaborGrid([]);
    setLineRO([]);
    setPartsGrid([]);
    setROShareData([]);
    setCWITAllDataAll([]);
    setPartsGpRo([]);
    if (orderedData && orderedData.length > 0) {
      orderedData.map(item => {
        item.chartId == 1335
          ? getDataForCWITotalcharts(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                setLoading(true);
                if (callback) {
                  setLoading(false);
                  setLoadingComplete(true);
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var output = resultArr.map(s => ({ label: s[0], val: s[1] }));
                  setCWITAllDataAll(output);
                  delete resultArr[3];
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setCWITData(filteredOutput);
                  let resultAllZero = filteredOutput.every(i => {
                    return i.val == 0;
                  });
                  if (resultAllZero && isLoading == false) {
                    $('#standardView').click();
                    $('#ButtonSelectHome').css('display', 'none');
                  }
                }
              }
            )
          : item.chartId == 1337
          ? getDataForKPILineROLtSixtyK(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));

                  setLineROLtSixtyK(filteredOutput);
                }
              }
            )
          : item.chartId == 1338
          ? getDataForKPILineROGtSixtyK(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLineROGtSixtyK(filteredOutput);
                }
              }
            )
          : item.chartId == 1340
          ? getDataForKPIAvgAgeMiles(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setAvgAgeMiles(filteredOutput);
                }
              }
            )
          : item.chartId == 1339
          ? getDataForKPIFlatRateHrs(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setFlatRateHrs(filteredOutput);
                }
              }
            )
          : item.chartId == 1341
          ? getDataForKPILaborGpRo(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLaborGpRo(filteredOutput);
                }
              }
            )
          : item.chartId == 1342
          ? getDataForKPIPartsGpRo(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setPartsGpRo(filteredOutput);
                }
              }
            )
          : item.chartId == 1343
          ? getDataForKPITotalGpRo(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setTotalGpRo(filteredOutput);
                }
              }
            )
          : item.chartId == 1344
          ? getDataForKPIWorkmix(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setWorkMix(filteredOutput);
                }
              }
            )
          : item.chartId == 1346 && internalToggle == false
          ? getDataForKPILaborGrid(
              toggleOption,
              session.serviceAdvisor,
              timeZone,
              payType,
              gridType,
              session.technician,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));

                  setLaborGrid(filteredOutput);
                }
              }
            )
          : item.chartId == 1351
          ? getDataForKPILineRO(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setLineRO(filteredOutput);
                }
              }
            )
          : item.chartId == 1353 && internalToggle == false
          ? getDataForKPIPartsGrid(
              toggleOption,
              session.serviceAdvisor,
              timeZone,
              payType,
              session.technician,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setPartsGrid(filteredOutput);
                }
              }
            )
          : getDataForKPIROSharechart(
              toggleOption,
              session.serviceAdvisor,
              session.technician,
              timeZone,
              callback => {
                if (callback) {
                  let resultSet = callback;
                  delete resultSet[0].__typename;
                  var resultArr = Object.entries(resultSet[0]);
                  var filteredOutput = resultArr.map(s => ({
                    label: s[0],
                    val: s[1]
                  }));
                  setROShareData(filteredOutput);
                }
              }
            );
        if (
          !session.serviceAdvisor.includes('All') &&
          session.serviceAdvisor.length == 1
        ) {
          dispatch(setKpiToggle(toggleOption));
          let intToggleValue = 'C';
          if (internalToggle == true) {
            intToggleValue = 'I';
          }
          dispatch(setInternalKpiToggle(gridType ? gridType : 'C'));
          // dispatch(setInternalKpiToggle(intToggleValue));
        } else {
          // dispatch(setKpiToggle('MTD'));
          dispatch(setKpiToggle(kpiDataToggle));

          // dispatch(setInternalKpiToggle('C'));
          dispatch(setInternalKpiToggle(gridType ? gridType : 'C'));
        }
      });
    }
  }, [
    toggleOption,
    session.serviceAdvisor,
    session.technician,
    session.allLaborGridTypes
  ]);

  useEffect(() => {
    let orderedData;
    if (chartList) {
      let filteredResult = chartList.filter(
        item => item.dbdName == 'KPI' && item.parentId == null
      );
      orderedData = lodash.orderBy(filteredResult, 'sort', 'asc');
    }
    let payType = 'C';
    let payTypeParts = 'C';
    if (
      props.keycloak.realm == 'billknightag' ||
      props.keycloak.realm == 'suntrupag'
    ) {
      payType =
        gridType == 'Internal' ? 'I' : gridType == 'Warranty' ? 'W' : 'C';
    }
    if (
      (props.keycloak.realm == 'billknightag' ||
        props.keycloak.realm == 'suntrupag') &&
      payTypeListParts.length > 1
    ) {
      payTypeParts =
        gridType == 'Internal' ? 'I' : gridType == 'Warranty' ? 'W' : 'C';
    }
    setLaborGrid([]);
    if (payTypeListParts && payTypeListParts.length > 1) {
      setPartsGrid([]);
    }
    if (orderedData && orderedData.length > 0) {
      orderedData.map(item => {
        if (item.chartId == 1346) {
          getDataForKPILaborGrid(
            toggleOption,
            session.serviceAdvisor,
            timeZone,
            payType,
            gridType,
            session.technician,
            callback => {
              if (callback) {
                let resultSet = callback;
                delete resultSet[0].__typename;
                var resultArr = Object.entries(resultSet[0]);
                var filteredOutput = resultArr.map(s => ({
                  label: s[0],
                  val: s[1]
                }));
                setLaborGrid(filteredOutput);
              }
            }
          );
        } else if (item.chartId == 1353) {
          getDataForKPIPartsGrid(
            toggleOption,
            session.serviceAdvisor,
            timeZone,
            payTypeParts,
            session.technician,
            callback => {
              if (callback) {
                let resultSet = callback;
                delete resultSet[0].__typename;
                var resultArr = Object.entries(resultSet[0]);
                var filteredOutput = resultArr.map(s => ({
                  label: s[0],
                  val: s[1]
                }));
                setPartsGrid(filteredOutput);
              }
            }
          );
        }
      });
    }
  }, [gridType, toggleOption, session.serviceAdvisor, session.technician]);

  const handleExpandedView = status => {
    if (status == true) {
      setExpandedView(true);
      setParent('Extended_View');
    } else {
      setExpandedView(false);
      setParent('Home');
    }
  };

  const handleChange = event => {
    if (event.target.value) {
      setToggleOption(event.target.value);
      dispatch(setKpiHomeToggle(event.target.value));
      if (
        !session.serviceAdvisor.includes('All') &&
        session.serviceAdvisor.length == 1
      ) {
        dispatch(setKpiToggle(event.target.value));
      } else {
        //  dispatch(setKpiToggle('MTD'));
        dispatch(setKpiToggle(kpiDataToggle));
      }
    }
  };

  const handleInternalMisses = value => {
    setInternalToggle(value);
    setInternalMisses(value);
  };

  const handleGridType = value => {
    setGridType(value);
  };

  const checkForSunday = () => {
    var date = new Date(props.toggleOptions[0].yesterday);
    if (date.getDay() == 0) {
      return true;
    } else {
      return false;
    }
  };

  let currentDate = moment(props.toggleOptions[0].today).format('MMM-DD-YY');
  let yesterDay = moment(props.toggleOptions[0].yesterday).format(
    "MMM DD ' YY"
  );
  let lastWeek =
    moment(props.toggleOptions[0].lastweekstartdate).format('MMM DD') +
    ' to ' +
    moment(props.toggleOptions[0].lastweekenddate).format("MMM DD ' YY");
  // let mtd =
  //   moment(props.toggleOptions[0].mtdstartdate).format('MMM-DD-YY') +
  //   ' to ' +
  //   moment(props.toggleOptions[0].mtdenddate).format('MMM-DD-YY');
  let mtd =
    moment(props.toggleOptions[0].mtdstartdate).format('MMM DD') +
    ' to ' +
    moment(props.toggleOptions[0].mtdenddate).format("MMM DD ' YY");
  // let lastMonth =
  //   moment(props.toggleOptions[0].lastmonthstartdate).format('MMM-DD-YY') +
  //   ' to ' +
  //   moment(props.toggleOptions[0].lastmonthenddate).format('MMM-DD-YY');
  let lastMonth = moment(props.toggleOptions[0].lastmonthstartdate).format(
    'MMM'
  );
  let ytd =
    moment(props.toggleOptions[0].ytdstartdate).format('MMM DD') +
    ' to ' +
    moment(props.toggleOptions[0].ytdenddate).format("MMM DD ' YY");
  // let lastQtr =
  //   moment(props.toggleOptions[0].lastquarterstartdate).format('MMM-DD-YY') +
  //   ' to ' +
  //   moment(props.toggleOptions[0].lastquarterenddate).format('MMM-DD-YY');
  let lastQtr =
    moment(props.toggleOptions[0].lastquarterstartdate).format('MMM') +
    ' to ' +
    //getNextMonth(props.toggleOptions[0].lastquarterstartdate) + ' - ' +
    moment(props.toggleOptions[0].lastquarterenddate).format('MMM');
  // let lastYear =
  //   moment(props.toggleOptions[0].lastyearstartdate).format('MMM-DD-YY') +
  //   ' to ' +
  //   moment(props.toggleOptions[0].lastyearenddate).format('MMM-DD-YY');
  let lastYear =
    moment(props.toggleOptions[0].lastyearstartdate).format('MMM') +
    ' to ' +
    moment(props.toggleOptions[0].lastyearenddate).format('MMM') +
    " ' " +
    getYearValue(props.toggleOptions[0].lastyearenddate);
  let dayBeforeYesterday = moment(
    props.toggleOptions[0].dayBeforeYesterday
  ).format("MMM DD ' YY");
  // let lastThreeMonths =
  //   moment(props.toggleOptions[0].lastthreemonthstartdate).format('MMM-DD-YY') +
  //   ' to ' +
  //   moment(props.toggleOptions[0].lastthreemonthenddate).format('MMM-DD-YY');

  let lastThreeMonths =
    moment(props.toggleOptions[0].lastthreemonthstartdate).format('MMM') +
    ' to ' +
    // getNextMonth(props.toggleOptions[0].lastthreemonthstartdate) + ' - ' +
    moment(props.toggleOptions[0].lastthreemonthenddate).format('MMM');
  let lastTwelveMonths =
    moment(props.toggleOptions[0].lasttwelvemonthstartdate).format("MMM ' YY") +
    ' to ' +
    moment(props.toggleOptions[0].lasttwelvemonthenddate).format("MMM ' YY");
  // let lastTwelveMonths = moment(props.toggleOptions[0].lasttwelvemonthstartdate).format('MMM') + ' to ' +
  //   moment(props.toggleOptions[0].lasttwelvemonthenddate).format('MMM') + ' - ' +
  //   getYearValue(props.toggleOptions[0].lasttwelvemonthstartdate);
  let resultAllZero = CWITAllData.every(i => {
    return i.val == 0;
  });

  return (
    <React.Fragment>
      {/* {isLoading == true ? (
        <Grid justify="center" className={classes.loaderGrid}>
          <CircularProgress size={60} />
        </Grid> */}
      {/* ) : ( */}
      <React.Fragment>
        <div className={classes.root}>
          <Paper className={classes.paper}>
            <div className={clsx(classes.headerItem, 'main-title-kpi')}>
              <Typography
                variant="h4"
                color="primary"
                gutterBottom
                className={clsx(classes.mainLabel, 'main-title-kpi')}
              >
                KPI Score Card
              </Typography>
              {/* <span style={{display: process.env.REACT_APP_PRODUCTION == 'false' ? 'block' : 'none'}}> */}
              <span style={{ display: 'none' }}>
                <div className={classes.ButtonSelect} id="ButtonSelectHome">
                  <Button
                    className={
                      parent == 'Home'
                        ? classes.reportButtonSelect
                        : classes.reportButton
                    }
                    id="standardView"
                    variant="outlined"
                    style={{ textTransform: 'none' }}
                    onClick={() => handleExpandedView(false)}
                  >
                    Standard View
                  </Button>
                  <Button
                    className={
                      parent == 'Extended_View'
                        ? classes.reportButtonSelect
                        : classes.reportButton
                    }
                    id="expandedView"
                    variant="outlined"
                    style={{ textTransform: 'none' }}
                    onClick={() => handleExpandedView(true)}
                  >
                    Expanded View
                  </Button>
                </div>
                {/* <div style={{display: 'flex'}}>
                  <div>
                    <HtmlTooltip
                      title="Expanded View"
                      style={(resultAllZero && isLoading == false) ? { display: 'none'} : { display: 'inline-block', marginLeft: 5 }}
                      onClick={() => handleExpandedView(true)}
                    >
                      <IconButton
                        size="medium"
                        id="expandedView"
                        classes={{
                          root: classes.icon
                        }}
                      >
                        <AppsIcon />
                      </IconButton>
                    </HtmlTooltip>
                  </div>
                  <div>
                  <Divider className={classes.divider} />
                  </div>
                  <div>
                    <HtmlTooltip
                      title="Expanded View"
                      style={(resultAllZero && isLoading == false) ? { display: 'none'} : { display: 'inline-block', marginLeft: 5 }}
                      onClick={() => handleExpandedView(true)}
                    >
                      <IconButton
                        size="medium"
                        id="expandedView"
                        classes={{
                          root: classes.icon
                        }}
                      >
                        <FormatListBulletedIcon />
                      </IconButton>
                    </HtmlTooltip>
                  </div>
                </div> */}
                {/* {expandedView == false  ?
                  <div>
                    <HtmlTooltip
                      title="Expanded View"
                      style={(resultAllZero && isLoading == false) ? { display: 'none'} : { display: 'inline-block', marginLeft: 5 }}
                      onClick={() => handleExpandedView(true)}
                    >
                      <IconButton
                        size="medium"
                        id="expandedView"
                        classes={{
                          root: classes.icon
                        }}
                      >
                        <ZoomOutMapIcon />
                      </IconButton>
                    </HtmlTooltip>
                  </div>
                  : 
                  <div>
                    <HtmlTooltip
                      title="Standard View"
                      style={{ display: 'inline-block', marginLeft: 5 }}
                      onClick={() => handleExpandedView(false)}
                    >
                      <IconButton
                        size="medium"
                        id="standardView"
                        classes={{
                          root: classes.icon
                        }}
                      >
                       <img src="/images/zoom-in.png" style={{ height: 20, width: 20 }} />
                      </IconButton>
                    </HtmlTooltip>
                  </div>
                  } */}
              </span>
              <div className={classes.dataAsOfLabel}>
                {closedDate || closedDateValue ? (
                  <Typography
                    variant="body1"
                    color="secondary"
                    align="right"
                    style={{
                      fontWeight: 'bold'
                    }}
                    className={clsx(classes.dataLabel, 'date-asof')}
                  >
                    {'Data as of: ' +
                      moment(closedDate ? closedDate : closedDateValue).format(
                        'MM/DD/YY'
                      )}
                  </Typography>
                ) : (
                  ''
                )}
              </div>
              <div className={classes.headerDropdown}>
                <FormControl
                  sx={{ m: 1, minWidth: 120 }}
                  variant="outlined"
                  margin="dense"
                  className={classes.formControl}
                >
                  <Select
                    value={toggleOption}
                    className={'selectedToggle'}
                    onClick={handleChange}
                    inputProps={{ 'aria-label': 'Without label' }}
                    style={{
                      height: '100%',
                      borderRadius: 0,
                      border: '1px solid #003d6b'
                    }}
                  >
                    <MenuItem
                      value={'YESDT'}
                      disabled={
                        localStorage.getItem('kpiDataStatusYesdt') == 1
                          ? false
                          : true
                      }
                    >
                      <span className={'toggleLabel'}>Yesterday</span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{yesterDay}</span>
                    </MenuItem>
                    <MenuItem
                      value={'DBYESDT'}
                      disabled={
                        localStorage.getItem('kpiDataStatusDbYesdt') == 1
                          ? false
                          : true
                      }
                    >
                      <span className={'toggleLabel'}>Day Before Yest.</span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>
                        {dayBeforeYesterday}
                      </span>
                    </MenuItem>
                    <MenuItem value={'LWEEK'}>
                      <span className={'toggleLabel'}>Last Week</span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{lastWeek}</span>
                    </MenuItem>
                    <MenuItem
                      value={'MTD'}
                      disabled={kpiDataToggle == 'MTD' ? false : true}
                    >
                      <span className={'toggleLabel'}>Mth to Date</span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{mtd}</span>
                    </MenuItem>
                    <MenuItem value={'LMONTH'}>
                      <span className={'toggleLabel'}>Last Mth </span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{lastMonth}</span>
                    </MenuItem>
                    <MenuItem value={'PLMTHREE'}>
                      <span className={'toggleLabel'}>Last 3 Mths </span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{lastThreeMonths}</span>
                    </MenuItem>
                    <MenuItem value={'LQRTR'}>
                      <span className={'toggleLabel'}>Last Qtr </span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{lastQtr}</span>
                    </MenuItem>
                    <MenuItem value={'YTD'}>
                      <span className={'toggleLabel'}>YTD </span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{ytd}</span>
                    </MenuItem>
                    <MenuItem value={'PLYONE'}>
                      <span className={'toggleLabel'}>Last 12 Mths </span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{lastTwelveMonths}</span>
                    </MenuItem>
                    <MenuItem value={'LYEAR'}>
                      <span className={'toggleLabel'}>Last Year </span>
                      <span className={'toggleSeparator'}></span>
                      <span className={'toggleValue'}>{lastYear}</span>
                    </MenuItem>
                  </Select>
                </FormControl>
              </div>
              {/* <Tooltip title="Export To PDF">
                  <Link className={classes.linkItem} onClick={exportReportGrid}>
                    <ExportIcon />
                  </Link>
                </Tooltip> */}
            </div>
            <Divider className={classes.divider} />
            {/* {parent == 'Home' ?  */}
            {expandedView == false ? (
              <KpiSummary
                toggleOption={toggleOption}
                CWITData={CWITData}
                allCWITData={CWITAllData}
                ROShareData={ROShareData}
                LineROLtSixtyK={LineROLtSixtyK}
                LineROGtSixtyK={LineROGtSixtyK}
                FlatRateHrs={FlatRateHrs}
                AvgAgeMiles={AvgAgeMiles}
                LaborGpRo={LaborGpRo}
                PartsGpRo={PartsGpRo}
                TotalGpRo={TotalGpRo}
                WorkMix={WorkMix}
                LaborGrid={LaborGrid}
                PartsGrid={PartsGrid}
                handleInternalMisses={handleInternalMisses}
                internalMisses={internalMisses}
                payType={payType}
                internalToggleStatus={props.internalToggleStatus}
                payTypeList={payTypeList}
                handleGridType={handleGridType}
                gridType={gridType}
                payTypeListParts={payTypeListParts}
              />
            ) : (
              <DashboardExtended
                toggleOption={toggleOption}
                LaborGpRo={LaborGpRo}
                PartsGpRo={PartsGpRo}
                TotalGpRo={TotalGpRo}
                FlatRateHrs={FlatRateHrs}
                allCWITData={CWITAllData}
                ROShareData={ROShareData}
                AvgAgeMiles={AvgAgeMiles}
                LineROLtSixtyK={LineROLtSixtyK}
                LineROGtSixtyK={LineROGtSixtyK}
                WorkMix={WorkMix}
                LineRO={LineRO}
                LaborGrid={LaborGrid}
                PartsGrid={PartsGrid}
                handleInternalMisses={handleInternalMisses}
                internalMisses={internalMisses}
                payType={payType}
                internalToggleStatus={props.internalToggleStatus}
                payTypeList={payTypeList}
                handleGridType={handleGridType}
                gridType={gridType}
                payTypeListParts={payTypeListParts}
                parent={parent}
              />
            )}
          </Paper>
        </div>
        {/* {parent == 'Home' ?  */}
        {expandedView == false ? (
          //   (session.serviceAdvisor == 'All' && resultAllZero == true && isLoading == false && isLoadingComplete == true) ? (
          //     <Grid justify="center" className={classes.loaderGrid}>
          //       Processing...
          //     </Grid>
          //   ) :
          //   (session.serviceAdvisor != 'All' && resultAllZero) ||
          //  // (resultAllZero && isLoading == false && isLoadingComplete == true) ||
          //   (resultAllZero &&
          //     isLoading == false &&
          //     (toggleOption == 'YESDT' || toggleOption == 'DBYESDT')) ? (
          //     <Grid justify="center" className={classes.loaderGrid}>
          //       No Data to Display
          //     </Grid>
          //   ) : resultAllZero &&
          //     isLoading == false &&
          //     isLoadingComplete == false ? (
          //     <Grid justify="center" className={classes.loaderGrid}>
          //       Processing...
          //     </Grid>
          //   )
          isLoading == true ? (
            <Grid justify="center" className={classes.loaderGrid}>
              <CircularProgress size={60} />
            </Grid>
          ) : resultAllZero && isLoading == false ? (
            <Grid justify="center" className={classes.loaderGrid}>
              No Data to Display
            </Grid>
          ) : isLoading == true ? (
            <Grid justify="center" className={classes.loaderGrid}>
              Processing...
            </Grid>
          ) : (
            <>
              <Dashboard
                toggleOption={toggleOption}
                LaborGpRo={LaborGpRo}
                PartsGpRo={PartsGpRo}
                TotalGpRo={TotalGpRo}
                FlatRateHrs={FlatRateHrs}
                allCWITData={CWITAllData}
                ROShareData={ROShareData}
                AvgAgeMiles={AvgAgeMiles}
                LineROLtSixtyK={LineROLtSixtyK}
                LineROGtSixtyK={LineROGtSixtyK}
                WorkMix={WorkMix}
                LineRO={LineRO}
                LaborGrid={LaborGrid}
                PartsGrid={PartsGrid}
                internalMisses={internalMisses}
                parent={parent}
              />
            </>
          )
        ) : null}
      </React.Fragment>
      {/* )} */}
    </React.Fragment>
  );
}

Header.propTypes = {
  classes: PropTypes.object.isRequired
};

export default withKeycloak(withStyles(styles)(Header));
