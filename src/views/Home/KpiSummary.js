import { makeStyles, withStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import React, { memo, useState } from 'react';
import 'src/styles.css';
import {
  Card,
  CardContent,
  Divider,
  Typography,
  IconButton,
  Tooltip,
  Radio
} from '@material-ui/core';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import InfoOutlinedIcon from '@material-ui/icons/InfoOutlined';
import getTooltipConentKPI from '../../utils/KpiTooltips';
import InsertChartOutlined from '@material-ui/icons/InsertChartOutlined';
import $ from 'jquery';
import OpenInNewOutlinedIcon from '@material-ui/icons/OpenInNewOutlined';
import { useHistory } from 'react-router';
import ForwardIcon from '@material-ui/icons/Forward';
import Switch from '@material-ui/core/Switch';
import { styled } from '@material-ui/styles';
import FormGroup from '@material-ui/core/FormGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import { useSelector, useDispatch } from 'react-redux';
import { setInternalKpiToggle } from 'src/actions';
import CheckCircleIcon from '@material-ui/icons/CheckCircle';
import CircleIcon from '@material-ui/icons/Brightness1Rounded';
import { withKeycloak } from '@react-keycloak/web';

const useStyles = makeStyles(theme => ({
  titleLabel: {
    display: 'flex',
    fontSize: 15
  },
  valLabel: {
    display: 'flex'
  },
  valLabel1: {
    display: 'flex',
    marginLeft: 100
  },
  image: {
    width: 100,
    height: 75
  },
  img: {
    margin: 'auto',
    display: 'block',
    maxWidth: '100%',
    maxHeight: '100%'
  },
  icon: {
    maxWidth: '100%',
    maxHeight: '100%',
    //width: '40%'
    width: '100%'
  },
  iconTarget: {
    maxWidth: '100%',
    maxHeight: '100%',
    // width: '53%',
    marginTop: -4
  },
  divider: {
    backgroundColor: '#9b8c8ca1',
    fontWeight: 'bold'
  },
  // valSubHead: {
  //   display: 'flex',
  //   fontSize: 11,
  //   color: '#757575',
  //   lineHeight: '14px',
  //   fontWeight: 500
  //   //   marginLeft: 22
  // },
  // valSubHeadWorkMix: {
  //   fontSize: 11,
  //   color: '#757575',
  //   lineHeight: '14px',
  //   fontWeight: 500,
  //   wordBreak: 'break-all',
  //   //display: 'block',
  //   textAlign: 'left',
  //   display: 'flex',
  // },
  valSubHeadNone: {
    display: 'flex',
    fontSize: 11,
    color: '#757575',
    lineHeight: '14px',
    fontWeight: 500
  },
  HeaderComponent: {
    display: 'flex',
    marginBottom: 2,
    justifyContent: 'stretch',
    paddingTop: 2,
    paddingBottom: 2,
    height: 80
  },
  HeaderComponentBlock3: {
    display: 'flex',
    marginBottom: 2,
    justifyContent: 'stretch',
    paddingTop: 2,
    paddingBottom: 12,
    height: 80
  },
  imageComponent: {
    display: 'flex',
    height: 50,
    marginTop: 7
  },

  dataComponent: {
    width: '100%',
    '@media (max-width: 1920px)': {
      marginLeft: '10px !important'
    },
    '@media (max-width: 1280px)': {
      marginLeft: '10px !important'
    },
    '@media (min-width: 1400px)': {
      marginLeft: '25px !important'
    }
  },
  targetSub: {
    color: '#FFF'
  },
  targetSubLabel: {
    color: '#FFF'
  },
  targetSubValue: {
    color: '#FFF',
    display: 'inline-block',
    width: 27
  },
  kpiTargetData: {
    // marginLeft: 100,
    minHeight: 55
  },
  kpiTargetDataParts: {
    marginLeft: 100,
    minHeight: 55
  },
  kpiTargetDivider: {
    marginLeft: 100,
    backgroundColor: '#FFFFFF8F',
    fontWeight: 500,
    marginTop: -42
  },
  kpiTargetImage: {
    width: '62%',
    height: 78,
    display: 'flex',
    marginTop: -27
  },
  targetSubHead: {
    fontSize: 10
  },
  targetSubHeadPerct: {
    fontSize: 13
  },
  targetSubHead1: {
    fontSize: 13
  },
  HeaderComponent1: {
    paddingTop: 2,
    // marginBottom: 5,
    paddingBottom: 2
  },
  dividerTarget: {
    backgroundColor: '#FFFFFF8F',
    fontWeight: 'bold'
  },
  containerCards: {
    alignItems: 'center',
    display: 'inline-block',
    marginTop: 8
  },
  infoContainer: {
    //marginTop: -13,
  },
  dotSeparation: {
    fontSize: '1.5em',
    lineHeight: '21px'
  },
  dotSeparationLabelWorkmix: {
    fontSize: '1.9em'
  },
  dotSeparationLabel: {
    fontSize: '1.9em',
    '@media (max-width: 1920px)': {
      lineHeight: '15px !important'
    },
    '@media (max-width: 1280px)': {
      lineHeight: '15px !important'
    },
    '@media (min-width: 2304px)': {
      lineHeight: '14px !important'
    }
  },
  dotSeparationTarget: {
    fontSize: '1.6em',
    verticalAlign: 'middle',
    color: '#FFF'
  },
  dotSeparationTooltip: {
    fontSize: '1.6em',
    verticalAlign: 'middle'
  },
  valTooltip: {
    marginLeft: 20,
    textAlign: 'left',
    display: 'block'
  },
  tooltipContainer: {
    color: '#003d6b',
    border: '1px solid #003d6b',
    backgroundColor: '#ddeaf4;',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(',')
  },
  tooltipContent: {
    fontSize: 12,
    marginLeft: 7,
    width: 470,
    color: '#003d6b',
    lineHeight: '18px',
    marginTop: 10,
    marginBottom: 10
  },
  tooltipContentBlock3: {
    fontSize: 12,
    marginLeft: 7,
    width: 580,
    color: '#003d6b',
    lineHeight: '18px',
    marginTop: 10,
    marginBottom: 10
  },
  tooltipAction: {
    width: '0%',
    float: 'right',
    marginTop: -13
  },
  visualization: {
    marginLeft: 10
  },
  // kpiHeader: {
  //   marginTop: -12
  // },
  valSubHead: {
    display: 'flex',
    color: '#757575',
    lineHeight: '14px',
    fontWeight: 500,
    '@media (max-width: 1920px)': {
      fontSize: '12px !important'
    },
    '@media (max-width: 1440px)': {
      fontSize: '11.25px !important'
    },
    '@media (max-width: 1280px)': {
      lineHeight: '12px !important'
    },
    '@media (min-width: 2304px)': {
      lineHeight: '12.5px !important'
    }
  },
  valSubHeadWorkMix: {
    color: '#757575',
    lineHeight: '14px',
    fontWeight: 500,
    wordBreak: 'break-all',
    textAlign: 'left',
    display: 'flex',
    '@media (max-width: 1920px)': {
      fontSize: '12px !important'
    },
    '@media (max-width: 1440px)': {
      fontSize: '11.25px !important'
    },
    '@media (max-width: 1280px)': {
      lineHeight: '12px !important'
    },
    '@media (min-width: 2304px)': {
      lineHeight: '12.5px !important'
    }
  },
  valSubHeadTarget: {
    color: '#003d6b',
    lineHeight: '14px',
    fontWeight: 500,
    marginBottom: 3,
    marginLeft: 10,
    textAlign: 'left',
    display: 'block',
    '@media (max-width: 1920px)': {
      fontSize: '11px !important'
    },
    '@media (max-width: 1280px)': {
      lineHeight: '11px !important'
    },
    '@media (min-width: 2304px)': {
      lineHeight: '12.5px !important'
    }
  },
  targetTitle: {
    paddingRight: 20
  },
  // oneLineDrilldown: {
  //   marginRight: 10
  // },
  oneLineDrilldown: {
    position: 'absolute',
    right: 0,
    top: 73,
    '@media (min-width: 2560px)': {
      // marginRight: '5px !important',
      // marginTop: '-3px !important'

      position: 'absolute !important',
      top: '68px !important',
      right: '6px !important'
    },
    '@media (max-width: 2304px)': {
      // marginRight: '5px !important',
      // marginTop: '-3px !important'

      position: 'absolute !important',
      top: '72px !important',
      right: '5px !important'
    },
    '@media (max-width: 1920px)': {
      // marginRight: '5px !important',
      // marginTop: '-1px !important'
      position: 'absolute !important',
      top: '72px !important',
      right: '5px !important'
    },
    '@media (max-width: 1440px)': {
      // marginRight: '8px !important',
      // marginTop: '-1px !important'

      position: 'absolute !important',
      top: '72px !important',
      right: '1px !important'
    }
  },
  oneLineDrilldown1: {
    position: 'absolute',
    right: 0,
    top: 306,
    '@media (min-width: 2560px)': {
      // marginRight: '5px !important',
      // marginTop: '-3px !important'

      position: 'absolute !important',
      marginTop: '-14px !important',
      right: '6px !important'
    },
    '@media (max-width: 2304px)': {
      // marginRight: '5px !important',
      // marginTop: '-3px !important'
      position: 'absolute !important',
      bottom: '0px !important',
      right: '5px !important'
    },
    '@media (max-width: 1920px)': {
      // marginRight: '5px !important',
      // marginTop: '-1px !important'
      position: 'absolute !important',
      bottom: '0px !important',
      right: '5px !important'
    },
    '@media (max-width: 1440px)': {
      // marginRight: '8px !important',
      // marginTop: '-1px !important'

      position: 'absolute !important',
      bottom: '0px !important',
      right: '1px !important'
    }
  },
  smallRadioButton: {
    '& svg': {
      width: '0.5em',
      height: '0.5em'
    },
    paddingLeft: 0,
    paddingRight: 0,
    height: 18,
    marginLeft: -2,
    backgroundColor: 'transparent !important',
    width: 19,
    '@media (max-width: 1440px)': {
      width: '16px !important',
      marginLeft: -2
    }
  },
  smallRadioButtonI: {
    paddingLeft: 0,
    paddingRight: 0,
    width: 19,
    height: 18,
    '& svg': {
      width: '0.5em',
      height: '0.5em'
    },
    // marginLeft: -14,
    '@media (max-width: 2304px)': {
      marginLeft: '1px !important'
    },
    '@media (max-width: 1920px)': {
      marginLeft: '1px !important'
    },
    '@media (max-width: 1440px)': {
      marginLeft: '-4px !important'
    },
    backgroundColor: 'transparent !important'
  }
}));

const HtmlTooltip = withStyles(theme => ({
  arrow: {
    color: theme.palette.common.black
  },
  tooltip: {
    minWidth: 470,
    maxWidth: 600,
    zIndex: '99',
    textAlign: 'left',
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    fontSize: '14px',
    fontWeight: 'normal',
    color: '#003d6b',
    border: '1px solid #003d6b'
  }
}))(Tooltip);
function KpiSummary(props) {
  //let toggleSelection = 'C';
  // if(props.internalMisses == true) {
  //   toggleSelection = 'I';
  // }
  let toggleSelection = props.gridType
    ? props.gridType
    : props.payTypeList && props.payTypeList.length > 0
    ? props.payTypeList[0]
    : '';
  if (toggleSelection && props.payTypeList.length > 0) {
    if (props.payTypeList.includes(toggleSelection)) {
      toggleSelection = toggleSelection;
    } else {
      toggleSelection = props.payTypeList[0];
    }
  }

  const classes = useStyles();
  const [placement, setPlacement] = useState();
  const history = useHistory();
  const [checked, setToggle] = useState(props.internalMisses);
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const [selectedValue, setSelectedValue] = useState(toggleSelection);
  const formatSummaryValues = (value, status) => {
    return value == null
      ? 0
      : value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };
  const dataLoader = () => {
    return (
      <div class="stage" style={{ marginLeft: '30%' }}>
        <div class="dot-pulse"></div>
      </div>
    );
  };
  const dataLoaderKpi = () => {
    return (
      <div class="stage" style={{ marginLeft: '30%' }}>
        <div class="dot-pulse-kpi"></div>
      </div>
    );
  };
  const gotoLaborGridMisses = type => {
    if (type == 'Labor') {
      window.sortStateLbrMiss = {};
      window.filterStateLbrMiss = {};
    } else {
      window.sortStatePrtMiss = {};
      window.filterStatePrtMiss = {};
    }
    type == 'Labor'
      ? history.push({
          pathname: '/LaborGridMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Home',
            previousToggle: props.toggleOption,
            payType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : props.keycloak.realm == 'suntrupag' &&
                  selectedValue == 'Warranty'
                ? 'W'
                : 'C',
            previousPayType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            gridType: selectedValue,
            previousGridType: selectedValue
          }
        })
      : history.push({
          pathname: '/PartsTargetMisses',
          state: {
            selectedToggle: props.toggleOption,
            parent: 'Home',
            previousToggle: props.toggleOption,
            payType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            previousPayType:
              props.keycloak.realm == 'billknightag'
                ? selectedValue.substring(0, 1)
                : 'C',
            gridType: selectedValue,
            previousGridType: selectedValue
          }
        });
  };
  /* const viewInternalMisses1 = (event) => {
    if(selectedValue == "C") {
      setSelectedValue(event.target.value);
      setToggle(true);
      props.handleInternalMisses(true);
      if(!session.serviceAdvisor.includes('All') && session.serviceAdvisor.length == 1) {
        dispatch(setInternalKpiToggle('I'));
      } else {
        dispatch(setInternalKpiToggle('C'));
      }
    } else {
      setSelectedValue(event.target.value);
      setToggle(false);
      props.handleInternalMisses(false);
      dispatch(setInternalKpiToggle('C'));
    }
  }*/
  const viewInternalMisses1 = event => {
    if (selectedValue != event.target.value) {
      setSelectedValue(event.target.value);
      setToggle(true);
      if (event.target.value == 'Internal') {
        props.handleInternalMisses(true);
      } else {
        props.handleInternalMisses(false);
      }
      props.handleGridType(event.target.value);
      if (
        !session.serviceAdvisor.includes('All') &&
        session.serviceAdvisor.length == 1
      ) {
        dispatch(setInternalKpiToggle(event.target.value));
      }
      // else {
      //   dispatch(setInternalKpiToggle(event.target.value));
      // }
    } else {
      setSelectedValue(event.target.value);
      setToggle(false);
      if (event.target.value == 'Internal') {
        props.handleInternalMisses(true);
      } else {
        props.handleInternalMisses(false);
      }
      dispatch(setInternalKpiToggle(event.target.value));
    }
  };
  const viewInternalMisses = () => {
    if (checked == false) {
      setToggle(true);
      props.handleInternalMisses(true);
      if (
        !session.serviceAdvisor.includes('All') &&
        session.serviceAdvisor.length == 1
      ) {
        dispatch(setInternalKpiToggle('I'));
      } else {
        dispatch(setInternalKpiToggle('C'));
      }
    } else {
      setToggle(false);
      props.handleInternalMisses(false);
      dispatch(setInternalKpiToggle('C'));
    }
  };

  const gotoSpecialMetrics = () => {
    history.push({
      pathname: '/SpecialMetrics',
      state: {
        selectedToggle: props.toggleOption,
        parent: 'Home',
        payType: checked == true ? 'I' : 'C',
        gridType: selectedValue
      }
    });
  };
  const gotoTechMetrics = () => {
    history.push({
      pathname: '/TechnicianPerformance',
      state: {
        selectedToggle: props.toggleOption,
        parent: 'Home',
        payType: checked == true ? 'I' : 'C',
        gridType: selectedValue,
        tabSelection: 'seven',
        techNo: session.technician
      }
    });
  };
  const getKpiTooltips = (chartId, gridTitle) => {
    return (
      <HtmlTooltip
        title={getTooltipConentKPI(chartId, classes, gridTitle)}
        style={{ height: 10 }}
      >
        <IconButton size="medium" classes="infoIcon">
          <InfoOutlinedIcon />
        </IconButton>
      </HtmlTooltip>
    );
  };

  const getKpiVisualizations = chartId => {
    return (
      <Tooltip
        title={<span style={{ fontSize: 11.5 }}>Go to Visualization</span>}
      >
        <IconButton
          size="medium"
          classes="infoIcon"
          onClick={() => gotoVisualization(chartId)}
        >
          <InsertChartOutlined />
        </IconButton>
      </Tooltip>
    );
  };
  const gotoVisualization = chartId => {
    let selectedId = $(
      '.react-grid-item.react-draggable.cssTransforms.react-resizable.selected'
    ).attr('id');
    const container = document.querySelector('.makeStyles-container-3');
    if (selectedId) {
      $('#' + selectedId).removeClass('selected');
    }
    var element = document.getElementById('chartContainer_' + chartId);
    // element.scrollIntoView({
    //   behavior: 'smooth',
    //   block: 'center',
    //   inline: 'center'
    // });
    if (element) {
      element.scrollIntoView({ block: 'center' });
      $('#chartContainer_' + chartId).addClass('selected');
      $('#visualization_' + chartId).css('display', 'block');
    }
  };

  // const gotoVisualization = chartId => {
  //   history.push({
  //     pathname: '/KpiGraphics',
  //     state: {
  //       chartId: chartId,
  //       // selectedToggle: props.toggleOption,
  //       parent: 'Home'
  //       // payType: checked == true ? 'I' : 'C',
  //       // gridType: selectedValue
  //     }
  //   });
  // };

  const AntSwitch = styled(Switch)(({ theme }) => ({
    width: 28,
    height: 16,
    padding: 0,
    display: 'flex',
    '&:active': {
      '& .MuiSwitch-thumb': {
        width: 15
      },
      '& .MuiSwitch-switchBase.Mui-checked': {
        transform: 'translateX(9px)'
      }
    },
    '& .MuiSwitch-switchBase': {
      padding: 2,
      '&.Mui-checked': {
        transform: 'translateX(12px)',
        color: '#fff',
        '& + .MuiSwitch-track': {
          opacity: 1
          //backgroundColor: theme.palette.mode === 'dark' ? '#2ECA45' : '#2ECA45',
        }
      }
    },
    '& .MuiSwitch-thumb': {
      boxShadow: '0 2px 4px 0 rgb(0 35 11 / 20%)',
      width: 12,
      height: 12,
      borderRadius: 6,
      transition: theme.transitions.create(['width'], {
        duration: 200
      })
    },
    '& .MuiSwitch-track': {
      borderRadius: 16 / 2,
      opacity: 1,
      backgroundColor:
        theme.palette.mode === 'dark'
          ? 'rgba(255,255,255,.35)'
          : 'rgba(0,0,0,.25)',
      boxSizing: 'border-box'
    }
  }));
  var gridTitle = selectedValue.substring(0, 1) == 'I' ? 'Int' : 'CP';
  var gridTitleParts =
    props.payTypeListParts.length > 1 && selectedValue.substring(0, 1) == 'I'
      ? 'Int'
      : 'CP';
  return (
    <Grid container className={clsx(classes.containerCards)} id="kpiScoreCards">
      <Card class="card card-1">
        <CardContent className="kpi-summary-block">
          <div className={classes.HeaderComponent}>
            {/* <span className={'orderComponent'}>1</span> */}
            <span className={'orderComponent2'}>1</span>
            <span className={'orderComponent3'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/lbr-gp-ros.png`}
              />
            </div>

            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                <span>
                  CP Lbr Sls Per RO / GP $ / GP &nbsp;
                  <span className={classes.targetSubHeadPerct}>%</span>
                </span>
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1341)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1341)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.LaborGpRo.length > 0 ? (
                  <React.Fragment>
                    <span>$</span>
                    <span>{formatSummaryValues(props.LaborGpRo[0].val)}</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>$</span>
                    {formatSummaryValues(props.LaborGpRo[1].val)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>
                      {props.LaborGpRo[2].val ? props.LaborGpRo[2].val : 0}
                    </span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                  </React.Fragment>
                ) : (
                  ''
                )}
              </Typography>
              <Typography className={classes.valSubHead}>
                {props.LaborGpRo.length > 0 ? (
                  <React.Fragment>
                    <span>Lbr Sls = $</span>&nbsp;
                    {formatSummaryValues(props.LaborGpRo[3].val)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparationLabel}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>GP = $</span>&nbsp;
                    {formatSummaryValues(props.LaborGpRo[4].val)}
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
            </div>
          </div>
          <Divider className={classes.divider} />
          <div className={classes.HeaderComponent}>
            {/* <span className={'orderComponent'}>2</span> */}
            <span className={'orderComponent2'}>2</span>
            <span className={'orderComponent3'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/prts-gp-ros.png`}
              />
            </div>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                <span>
                  CP Pts Sls Per RO / GP $ / GP &nbsp;
                  <span className={classes.targetSubHeadPerct}>%</span>
                </span>
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1342)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1342)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.PartsGpRo.length > 0 ? (
                  <React.Fragment>
                    <span>$</span>
                    {formatSummaryValues(props.PartsGpRo[0].val)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>$</span>
                    {formatSummaryValues(props.PartsGpRo[1].val)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>
                      {props.PartsGpRo[2].val ? props.PartsGpRo[2].val : 0}
                    </span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                  </React.Fragment>
                ) : (
                  ''
                )}
              </Typography>
              <Typography className={classes.valSubHead}>
                {props.PartsGpRo.length > 0 ? (
                  <React.Fragment>
                    <span>Pts Sls = $</span>&nbsp;
                    {formatSummaryValues(props.PartsGpRo[3].val)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparationLabel}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>GP = $</span>&nbsp;
                    {formatSummaryValues(props.PartsGpRo[4].val)}
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
            </div>
          </div>
          <Divider className={classes.divider} />
          <div className={classes.HeaderComponent}>
            {/* <span className={'orderComponent'}>3</span> */}
            <span className={'orderComponent2'}>3</span>
            <span className={'orderComponent3'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/total-gp-ros.png`}
              />
            </div>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                CP Labor & Parts Sales $ Per RO / Total GP $
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1343)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1343)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.TotalGpRo.length > 0 ? (
                  <React.Fragment>
                    <span style={{ display: 'flex' }}>
                      <span>$</span>
                      {formatSummaryValues(props.TotalGpRo[0].val)}
                      &nbsp;&nbsp;&nbsp;
                      <span className={classes.dotSeparation}>&bull;</span>
                      &nbsp;&nbsp;&nbsp;
                      <span>$</span>
                      {formatSummaryValues(props.TotalGpRo[1].val)}
                    </span>
                  </React.Fragment>
                ) : (
                  ''
                )}
              </Typography>
              <Typography className={classes.valSubHead}>
                {props.TotalGpRo.length > 0 ? (
                  <React.Fragment>
                    <span>Comb Sls = $</span>&nbsp;
                    {formatSummaryValues(props.TotalGpRo[2].val)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparationLabel}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>GP = $</span>&nbsp;
                    {formatSummaryValues(props.TotalGpRo[3].val)}
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
            </div>
          </div>
          <Divider className={classes.divider} />
          <div className={classes.HeaderComponent}>
            {/* <span className={'orderComponent'}>4</span> */}
            <span className={'orderComponent2'}>4</span>
            <span className={'orderComponent3'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/hrs-per-ro.png`}
              />
            </div>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                All Sold Hrs / Avg Per Day / CP Avg Hrs Per RO
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1339)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1339)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.FlatRateHrs.length > 0 ? (
                  <React.Fragment>
                    <span>
                      {formatSummaryValues(props.FlatRateHrs[0].val, 1)}
                    </span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>
                      {props.FlatRateHrs[1].val ? props.FlatRateHrs[1].val : 0}
                    </span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>
                      {props.FlatRateHrs[2].val ? props.FlatRateHrs[2].val : 0}
                    </span>
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
              <Typography className={classes.valSubHeadNone}>{''}</Typography>
            </div>
          </div>
        </CardContent>
      </Card>
      <Card class="card-target card-1">
        <CardContent className="kpi-summary-block-target">
          <div className={classes.HeaderComponent1}>
            <Typography
              gutterBottom
              color="primary"
              variant="subtitle1"
              className={classes.titleLabel}
            >
              {/* <span className={'orderComponent'}>5</span> */}
              <span className={'orderComponent5'}>5</span>
              <span className={'orderComponent6'}>)</span>
              CP 1 Line ROs Under 60k Miles
              <span className={'kpiHeader'}>
                <span className={classes.infoContainer}>
                  {getKpiTooltips(1337)}
                </span>
                <span className={classes.visualization}>
                  {getKpiVisualizations(1337)}
                </span>
              </span>
            </Typography>
            <Typography
              variant="h5"
              color="primary"
              className={classes.valLabel}
            >
              {props.LineROLtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>1 Line ROs =</span>&nbsp;
                  <span>{props.LineROLtSixtyK[1].val}</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;
                  <span>
                    {props.LineROLtSixtyK[3].val == '100.00'
                      ? '100'
                      : props.LineROLtSixtyK[3].val == null
                      ? 0
                      : props.LineROLtSixtyK[3].val}
                  </span>
                  <span className={classes.targetSubHeadPerct}>%</span>
                </React.Fragment>
              ) : (
                dataLoader()
              )}
            </Typography>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROLtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>1 Line RO Value</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>L</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[5].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>P</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[6].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>T</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[7].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROLtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>Multi-Line RO Value</span>&nbsp;&nbsp;&nbsp;
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>L</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[8].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>P</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[9].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>T</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROLtSixtyK[10].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROLtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>Avg Jobs Per Multi</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.targetSubValue}>
                    {props.LineROLtSixtyK[11].val
                      ? props.LineROLtSixtyK[11].val
                      : 0}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            <span className={classes.oneLineDrilldown}>
              <Tooltip
                title={<span style={{ fontSize: 11.5 }}>Detailed View</span>}
              >
                <IconButton
                  size="medium"
                  classes="infoIcon"
                  onClick={() =>
                    !session.technician.includes('All')
                      ? gotoTechMetrics()
                      : gotoSpecialMetrics()
                  }
                >
                  <OpenInNewOutlinedIcon />
                </IconButton>
              </Tooltip>
            </span>
          </div>
          <Divider className={classes.dividerTarget} />
          <div className={classes.HeaderComponent1}>
            <div className={classes.kpiTargetData}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={clsx(classes.titleLabel, classes.targetTitle)}
              >
                {/* <span className={'orderComponent1'}>6</span> */}
                <span className={'orderComponent1'}>6</span>
                <span className={'orderComponent7'}>)</span>
                {gridTitle} Repair Labor Price Targets / Misses / %
                Non-Compliance
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1346, gridTitle)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1346)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel1}
                // className={classes.valLabel}
              >
                {props.LaborGrid.length > 0 ? (
                  <React.Fragment>
                    <span>{formatSummaryValues(props.LaborGrid[0].val)}</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>{formatSummaryValues(props.LaborGrid[1].val)}</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>{formatSummaryValues(props.LaborGrid[3].val)}</span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={'missesGrid'}>
                      {/* <span className={'gridDrilldown'}>
                      <Tooltip
                        title={<span style={{ fontSize: 11.5 }}>View Misses</span>}
                      >
                        <IconButton
                          size="medium"
                          classes="infoIcon"
                          onClick={()=> gotoLaborGridMisses('Labor')}
                        >
                          <OpenInNewOutlinedIcon/>
                        </IconButton>
                      </Tooltip>
                    </span>  */}
                      {props.payTypeList.length > 1 ? (
                        <span className={'gridInternal'}>
                          {props.payTypeList.map((item, index) => (
                            <Tooltip
                              title={
                                <span
                                  style={{ fontSize: 11.5, marginTop: 100 }}
                                >
                                  {'View ' + item + ' Misses'}
                                </span>
                              }
                            >
                              <Radio
                                checked={selectedValue == item}
                                onChange={viewInternalMisses1}
                                value={item}
                                name="radio-buttons"
                                inputProps={{ 'aria-label': 'A' }}
                                className={classes.smallRadioButton}
                                id={index}
                              />
                            </Tooltip>
                          ))}
                        </span>
                      ) : null}
                    </span>
                    <span className={'gridDrilldown'}>
                      <Tooltip
                        title={
                          <span style={{ fontSize: 11.5 }}>View Misses</span>
                        }
                      >
                        <IconButton
                          size="medium"
                          classes="infoIcon"
                          onClick={() => gotoLaborGridMisses('Labor')}
                        >
                          <OpenInNewOutlinedIcon />
                        </IconButton>
                      </Tooltip>
                    </span>
                  </React.Fragment>
                ) : (
                  dataLoader()
                )}
              </Typography>
            </div>
            <div className={classes.kpiTargetImage}>
              <img
                class="iconTarget"
                alt="Fixed Ops"
                src={`/images/kpis/target.png`}
              />
            </div>
            <Divider className={classes.kpiTargetDivider} />
            <div className={classes.kpiTargetDataParts}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={clsx(classes.titleLabel, classes.targetTitle)}
              >
                {/* <span className={'orderComponent8'}>7</span> */}
                {/* <span className={'orderComponent8'}>7</span> */}
                <span className={'orderComponent8'}>7</span>
                <span className={'orderComponent7'}>)</span>
                {gridTitleParts} Repair Parts Price Targets / Misses / %
                Non-Compliance
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1353, gridTitleParts)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1353)}
                  </span>
                </span>
              </Typography>
              {/* <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                <span>111</span>&nbsp;
                <span className={classes.dotSeparation}>&bull;</span>&nbsp;
                <span>11</span>&nbsp;
                <span className={classes.dotSeparation}>&bull;</span>&nbsp;
                <span>10</span>
                <span className={classes.targetSubHeadPerct}>%</span>
              </Typography> */}
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.PartsGrid.length > 0 ? (
                  <React.Fragment>
                    <span>{formatSummaryValues(props.PartsGrid[0].val)}</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>{formatSummaryValues(props.PartsGrid[1].val)}</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>{formatSummaryValues(props.PartsGrid[3].val)}</span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={'missesGrid'}>
                      <span className={'gridDrilldown'}>
                        <Tooltip
                          title={
                            <span style={{ fontSize: 11.5 }}>View Misses</span>
                          }
                        >
                          <IconButton
                            size="medium"
                            classes="infoIcon"
                            onClick={() => gotoLaborGridMisses('Parts')}
                          >
                            <OpenInNewOutlinedIcon />
                          </IconButton>
                        </Tooltip>
                      </span>
                      {props.payTypeListParts.length > 1 ? (
                        <span className={'gridInternal'}>
                          {props.payTypeListParts.map((item, index) => (
                            <Tooltip
                              title={
                                <span
                                  style={{ fontSize: 11.5, marginTop: 100 }}
                                >
                                  {'View ' + item + ' Misses'}
                                </span>
                              }
                            >
                              <Radio
                                checked={selectedValue == item}
                                onChange={viewInternalMisses1}
                                value={item}
                                name="radio-buttons"
                                inputProps={{ 'aria-label': 'A' }}
                                className={classes.smallRadioButton}
                                id={index}
                              />
                            </Tooltip>
                          ))}
                        </span>
                      ) : null}
                      {/* {props.internalToggleStatus == 'TRUE' ? 
                        <span className={'gridInternal'}>
                        <Tooltip
                          title={
                            <span style={{ fontSize: 11.5 }}>
                              View CP Misses
                            </span>
                          }
                        >
                         <Radio
                          checked={selectedValue == "C"}
                          onChange={viewInternalMisses1}
                          value="C"
                          name="radio-buttons"
                          inputProps={{ 'aria-label': 'A' }}
                          className={classes.smallRadioButton}
                        />
                        </Tooltip>
                        <Tooltip
                          title={
                            <span style={{ fontSize: 11.5 }}>
                              View Internal Misses
                            </span>
                          }
                        >
                          <Radio
                            checked={selectedValue == "I"}
                            onChange={viewInternalMisses1}
                            value="I"
                            name="radio-buttons"
                            inputProps={{ 'aria-label': 'A' }}
                            className={classes.smallRadioButtonI}
                          />
                        </Tooltip>
                      </span>
                    : null } */}
                    </span>
                  </React.Fragment>
                ) : (
                  dataLoader()
                )}
              </Typography>
            </div>
          </div>
          <Divider className={classes.dividerTarget} />
          <div className={classes.HeaderComponent1}>
            <Typography
              gutterBottom
              color="primary"
              variant="subtitle1"
              className={classes.titleLabel}
            >
              {/* <span className={'orderComponent'}>8</span> */}
              <span className={'orderComponent5'}>8</span>
              <span className={'orderComponent6'}>)</span>
              CP 1 Line ROs Over 60k Miles
              <span className={'kpiHeader'}>
                {/* <span className={classes.oneLineDrilldown}>
                  <Tooltip
                    title={<span style={{ fontSize: 11.5 }}>Detailed View</span>}
                  >
                    <IconButton
                      size="medium"
                      classes="infoIcon"
                      onClick={()=> gotoSpecialMetrics()}
                    >
                      <OpenInNewOutlinedIcon/>
                    </IconButton>
                  </Tooltip>
                </span>  */}
                <span className={classes.infoContainer}>
                  {getKpiTooltips(1338)}
                </span>
                <span className={classes.visualization}>
                  {getKpiVisualizations(1338)}
                </span>
              </span>
            </Typography>
            <Typography
              variant="h5"
              color="primary"
              className={classes.valLabel}
            >
              {props.LineROGtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>1 Line ROs =</span>&nbsp;
                  <span>{props.LineROGtSixtyK[1].val}</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;
                  <span>
                    {props.LineROGtSixtyK[3].val == '100.00'
                      ? '100'
                      : props.LineROGtSixtyK[3].val == null
                      ? 0
                      : props.LineROGtSixtyK[3].val}
                  </span>
                  <span className={classes.targetSubHeadPerct}>%</span>
                </React.Fragment>
              ) : (
                dataLoader()
              )}
            </Typography>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROGtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>1 Line RO Value</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>L</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[5].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>P</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[6].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>T</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[7].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROGtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>Multi-Line RO Value</span>&nbsp;&nbsp;&nbsp;
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span>L</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[8].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>P</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[9].val)}
                  </span>
                  &nbsp;
                  <span className={classes.dotSeparationTarget}>&bull;</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>T</span>&nbsp;
                  <span className={classes.targetSubLabel}>$</span>&nbsp;
                  <span className={classes.targetSubValue}>
                    {formatSummaryValues(props.LineROGtSixtyK[10].val)}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            <Typography className={classes.valSubHeadTarget}>
              {props.LineROGtSixtyK.length > 0 ? (
                <React.Fragment>
                  <span>Avg Jobs Per Multi</span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  <span>=</span>&nbsp;&nbsp;&nbsp;
                  <span className={classes.targetSubValue}>
                    {props.LineROGtSixtyK[11].val
                      ? props.LineROGtSixtyK[11].val
                      : 0}
                  </span>
                </React.Fragment>
              ) : (
                ''
              )}
            </Typography>
            <span className={classes.oneLineDrilldown1}>
              <Tooltip
                title={<span style={{ fontSize: 11.5 }}>Detailed View</span>}
              >
                <IconButton
                  size="medium"
                  classes="infoIcon"
                  onClick={() =>
                    !session.technician.includes('All')
                      ? gotoTechMetrics()
                      : gotoSpecialMetrics()
                  }
                >
                  <OpenInNewOutlinedIcon />
                </IconButton>
              </Tooltip>
            </span>
          </div>
        </CardContent>
      </Card>
      <Card class="card card-1">
        <CardContent className="kpi-summary-block block3">
          <div className={classes.HeaderComponentBlock3}>
            {/* <span className={'orderComponent'}>9</span> */}
            <span className={'orderComponent2'}>9</span>
            <span className={'orderComponent3'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/ro-count-shares.png`}
              />
            </div>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                All ROs Per Day Avg = % of Total Share
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1336)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1336)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.ROShareData.length > 0 ? (
                  <React.Fragment>
                    <span>
                      {props.ROShareData[1].val ? props.ROShareData[1].val : 0}
                    </span>
                    &nbsp;
                    <span>=</span>&nbsp;
                    <span>
                      {props.ROShareData[3].val &&
                      props.ROShareData[3].val == 100.0
                        ? '100'
                        : props.ROShareData[3].val &&
                          props.ROShareData[3].val != 100.0
                        ? props.ROShareData[3].val
                        : 0}
                    </span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
              <Typography className={classes.valSubHeadNone}>{''}</Typography>
            </div>
          </div>
          <Divider className={classes.divider} />
          <div className={classes.HeaderComponentBlock3}>
            {/* <span className={'orderComponent'}>10</span> */}
            <span className={'orderComponent2'}>10</span>
            <span className={'orderComponent4'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/c-w-i-totals.png`}
              />
            </div>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                CP / Wty / Int / All Unique ROs
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1335)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1335)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.allCWITData.length > 0 ? (
                  <React.Fragment>
                    {formatSummaryValues(props.allCWITData[0].val, 1)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    {formatSummaryValues(props.allCWITData[1].val, 1)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    {formatSummaryValues(props.allCWITData[2].val, 1)}
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    {formatSummaryValues(props.allCWITData[3].val, 1)}
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
              <Typography className={classes.valSubHeadNone}>{''}</Typography>
            </div>
          </div>
          <Divider className={classes.divider} />
          <div className={classes.HeaderComponent}>
            {/* <span className={'orderComponent'}>11</span> */}
            <span className={'orderComponent2'}>11</span>
            <span className={'orderComponent4'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/work-mix.png`}
              />
            </div>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                CP Work Mix
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1344)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1344)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.WorkMix.length > 0 ? (
                  <React.Fragment>
                    <span>C</span>&nbsp;
                    <span>
                      {props.WorkMix[0].val ? props.WorkMix[0].val : 0}
                    </span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>M</span>&nbsp;
                    <span>
                      {props.WorkMix[1].val ? props.WorkMix[1].val : 0}
                    </span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>R</span>&nbsp;
                    <span>
                      {props.WorkMix[2].val ? props.WorkMix[2].val : 0}
                    </span>
                    <span className={classes.targetSubHeadPerct}>%</span>
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
              <Typography className={classes.valSubHeadWorkMix}>
                {props.WorkMix.length > 0 ? (
                  <React.Fragment>
                    <span>C</span>&nbsp;
                    <span>$</span>&nbsp;
                    <span>{formatSummaryValues(props.WorkMix[3].val)}</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparationLabel}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>M</span>&nbsp;
                    <span>$</span>&nbsp;
                    <span>{formatSummaryValues(props.WorkMix[4].val)}</span>
                    &nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparationLabel}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>R</span>&nbsp;
                    <span>$</span>&nbsp;
                    <span>{formatSummaryValues(props.WorkMix[5].val)}</span>
                  </React.Fragment>
                ) : (
                  ''
                )}
              </Typography>
            </div>
          </div>
          <Divider className={classes.divider} />
          <div className={classes.HeaderComponent}>
            {/* <span className={'orderComponent'}>12</span> */}
            <span className={'orderComponent2'}>12</span>
            <span className={'orderComponent4'}>)</span>
            <div className={classes.imageComponent}>
              <img
                className={classes.icon}
                alt="Fixed Ops"
                src={`/images/kpis/avg-ages.png`}
              />
            </div>
            <div className={classes.dataComponent}>
              <Typography
                gutterBottom
                color="primary"
                variant="subtitle1"
                className={classes.titleLabel}
              >
                Avg Age / Miles for CP & Wty Vehicles
                <span className={'kpiHeader'}>
                  <span className={classes.infoContainer}>
                    {getKpiTooltips(1340)}
                  </span>
                  <span className={classes.visualization}>
                    {getKpiVisualizations(1340)}
                  </span>
                </span>
              </Typography>
              <Typography
                variant="h5"
                color="primary"
                className={classes.valLabel}
              >
                {props.AvgAgeMiles.length > 0 ? (
                  <React.Fragment>
                    <span>{Math.round(props.AvgAgeMiles[0].val * 2) / 2}</span>
                    <span>Yrs</span>&nbsp;&nbsp;&nbsp;
                    <span className={classes.dotSeparation}>&bull;</span>
                    &nbsp;&nbsp;&nbsp;
                    <span>{formatSummaryValues(props.AvgAgeMiles[1].val)}</span>
                  </React.Fragment>
                ) : (
                  dataLoaderKpi()
                )}
              </Typography>
              <Typography className={classes.valSubHeadNone}>{''}</Typography>
            </div>
          </div>
        </CardContent>
      </Card>
    </Grid>
  );
}

KpiSummary.propTypes = {
  className: PropTypes.string,
  filterCharts: PropTypes.func,
  setTitle: PropTypes.string,
  resetDashboard: PropTypes.func,
  checkSuccess: PropTypes.func,
  showCurrentMonth: PropTypes.func
};

export default withKeycloak(memo(KpiSummary));
