import React from 'react';
import RGL, { WidthProvider } from 'react-grid-layout';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import {
  getLayoutConfiguration,
  saveLayoutConfiguration
} from '../../utils/Utils';
import Header from './Header';
import { getAllChartDetails } from '../../utils/Utils';
import {
  getKpiToggleOptionsWithTimeZone,
  getLatestClosedDate,
  getClientId,
  checkInternalToggleExistance,
  getGridorMatrixPayTypeDetails
} from 'src/utils/hasuraServices';
import { UPDATE_KPI_CURRENT_DATE } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { CircularProgress, Grid } from '@material-ui/core';
import clsx from 'clsx';
import { withStyles } from '@material-ui/styles';
import { ReactSession } from 'react-client-session';
import { traceSpan } from 'src/utils/OTTTracing';
import { connect } from 'react-redux';

var lodash = require('lodash');

const ReactGridLayout = WidthProvider(RGL);
class Charts extends React.PureComponent {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps, prevState) {
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        console.log('enter12345678=');
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getToggleOptions();
        this.checkInternalToggle();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 48,
    isLoading: true,
    onLayoutChange: function() {}
  };

  constructor(props) {
    super(props);
    this.state = {
      timePassed: false,
      favouriteData: [],
      filters: 2,
      isLoading: true,
      checkEmpty: false,
      toggleOptions: [],
      layout: JSON.parse(
        JSON.stringify(
          getLayoutConfiguration('layout', 'fixed-ops-layout-fav') || {}
        )
      )
      //  chartList: JSON.parse(global.localStorage.getItem('chart-master'))
    };

    this.onLayoutChange = this.onLayoutChange.bind(this);
  }
  componentDidMount() {
    // this.checkInternalToggle();
    this.getLastClosedDate();
    this.updateCurrentDate();
    this.getToggleOptions();
    this.getClientId();
  }
  getLastClosedDate = () => {
    getLatestClosedDate(result => {
      if (result) {
        var Date1 = result[0].value;
        localStorage.setItem('closedDate', Date1);
        this.setState({ closedDate: Date1 });
      }
    });
  };
  updateCurrentDate = () => {
    const start = new Date();
    const client = makeApolloClient;
    client
      .mutate({
        mutation: UPDATE_KPI_CURRENT_DATE
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/Home',
          origin: '',
          event: 'Menu Load',
          is_from: 'UPDATE_KPI_CURRENT_DATE',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
      });
  };
  getToggleOptions = () => {
    this.setState({ isLoading: true });
    var timeZone = this.props.timezone ? this.props.timezone : '+05:30';
    getKpiToggleOptionsWithTimeZone(timeZone, result => {
      this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes) {
        let dataArr =
          result.data.statelessCcPhysicalRoGetToggleDurationsTimezone.nodes;

        this.setState({ toggleOptions: dataArr });
      }
    });
  };
  getClientId = () => {
    this.setState({ isLoading: true });
    getClientId(result => {
      this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRoGetClientMasterS.nodes) {
        let client =
          result.data.statelessCcPhysicalRoGetClientMasterS.nodes[0].clientId;
        // this.checkInternalToggle(client);
        this.setState({ clientId: client });
      }
    });
  };

  checkInternalToggle = clientId => {
    this.getPartsPayTypeList();
    let data = [];
    data.push('Customer');
    getGridorMatrixPayTypeDetails('paytype_grid', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (result.includes('Highline')) {
          result.reverse();
        }
        if (
          result.includes('Diesel') ||
          result.includes('HD & Hybrid') ||
          result.includes('Electric')
        ) {
          result.reverse();
        }
        if (
          result.includes('Honda Grid') &&
          result.includes('Volvo Grid') &&
          result.includes('Merc-Benz Grid')
        ) {
          result = ['Honda Grid', 'Volvo Grid', 'Merc-Benz Grid'];
        }
        if (
          result.includes('25-5500/Dsl') &&
          result.includes('Car/Lt Trk') &&
          result.includes('Flt-Sptr')
        ) {
          result = ['Car/Lt Trk', '25-5500/Dsl', 'Flt-Sptr'];
        }
        if (
          result.includes('Hyundai') &&
          result.includes('Genesis') &&
          result.includes('Customer')
        ) {
          result = ['Hyundai', 'Genesis', 'Customer'];
        }
        if (
          result.includes('EV') &&
          result.includes('Medium/HD') &&
          result.includes('Standard')
        ) {
          result = ['Standard', 'Medium/HD', 'EV'];
        }
        if (result.includes('HD') && result.includes('Standard')) {
          result = ['Standard', 'HD'];
        }
        this.setState({ laborGridTypes: JSON.stringify(result) });
        localStorage.setItem('laborGridTypes', JSON.stringify(result));
      } else {
        this.setState({ laborGridTypes: JSON.stringify(result) });
        localStorage.setItem('laborGridTypes', JSON.stringify(data));
      }
    });
  };
  getPartsPayTypeList = () => {
    let data = [];
    data.push('Customer');
    getGridorMatrixPayTypeDetails('paytype_matrix', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (result.includes('Highline')) {
          result.reverse();
        }
        localStorage.setItem('partsMatrixTypes', JSON.stringify(result));
      } else {
        localStorage.setItem('partsMatrixTypes', JSON.stringify(data));
      }
    });
  };

  setResetDashboard = value => {
    if (value) {
      this.setState({
        layout: []
      });
    }

    return this.state.layout;
  };
  onLayoutChange(layout) {
    saveLayoutConfiguration('layout', layout, 'fixed-ops-layout-fav');
    this.setState({ layout });
    this.props.onLayoutChange(layout);
    // window.location.reload();
  }
  render() {
    const { classes } = this.props;
    let parent =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.parent
        ? this.props.history.location.state.parent
        : 'Home';
    return (
      <div>
        {this.state.isLoading == true ? (
          <Grid justify="center" className={classes.loaderGrid}>
            <CircularProgress size={60} />
          </Grid>
        ) : (
          this.state.toggleOptions.length > 0 && (
            <Header
              toggleOptions={this.state.toggleOptions}
              history={this.props.history}
              timezone={this.props.timezone}
              internalToggleStatus={this.state.internalToggleStatus}
              parent={parent}
              closedDate={this.state.closedDate}
              selectedToggle={this.props.session.kpiHomeToggle}
            />
          )
        )}
      </div>
    );
  }
}
const styles = theme => ({
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
});
export default connect()(withStyles(styles)(Charts));
