import React from 'react';
import GridLayout from './GridLayout';
import layoutConfig from './layoutConfig';
const getInitialState = data => {
  let initialState1 = {
    data: {
      graph1: {
        chartId: 1341,
        data: [{ LaborGpRo: data.LaborGpRo }]
      },
      graph2: {
        chartId: 1342,
        data: [{ PartsGpRo: data.PartsGpRo }]
      },
      graph3: {
        chartId: 1343,
        data: [{ TotalGpRo: data.TotalGpRo }]
      },
      graph4: {
        chartId: 1335,
        data: [{ allCWITData: data.allCWITData }]
      },
      graph5: {
        chartId: 1340,
        data: [{ AvgAgeMiles: data.AvgAgeMiles }]
      },
      graph6: {
        chartId: 1339,
        data: [{ FlatRateHrs: data.FlatRateHrs }]
      },
      graph7: {
        chartId: 1336,
        data: [{ ROShareData: data.ROShareData }]
      },
      graph8: {
        chartId: 1337,
        data: [{ LineROLtSixtyK: data.LineROLtSixtyK }]
      },
      graph9: {
        chartId: 1338,
        data: [{ LineROGtSixtyK: data.LineROGtSixtyK }]
      },
      graph10: {
        chartId: 1351,
        data: [{ LineRO: data.LineRO }]
      },
      graph11: {
        chartId: 1344,
        data: [{ WorkMix: data.WorkMix }]
      },
      graph12: {
        chartId: 1346,
        data: [{ LaborGrid: data.LaborGrid }]
      },
      graph13: {
        chartId: 1353,
        data: [{ PartsGrid: data.PartsGrid }]
      }
    },

    layouts: layoutConfig,
    breakpoint: 'lg'
  };
  return initialState1;
};

const Dashboard = props => {
  const initialState = getInitialState(props);
  return (
    <div className="dashboard dashboard-grid-items">
      <GridLayout
        data={initialState.data}
        layouts={initialState.layouts}
        props={props}
        initialState={initialState}
        parent={props.parent}
      />
    </div>
  );
};
export default Dashboard;
