import React from 'react';
import GridItem from './GridItem';
import GridItemExtended from '../KPI/GridItemExtended';

const GridItemContainer = ({ chartId, data, children, item, ...props }) => {
  return props.input.parent == 'Home' ? (
    <GridItem chartId={chartId} data={data} root={item} {...props}>
      {children}
    </GridItem>
  ) : (
    <GridItemExtended chartId={chartId} data={data} root={item} {...props}>
      {children}
    </GridItemExtended>
  );
};

export default GridItemContainer;
