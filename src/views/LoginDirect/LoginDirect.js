import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  InputAdornment,
  IconButton
} from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import clsx from 'clsx';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router';
import validate from 'validate.js';
import SuccessSnackbar from './SuccessSnackbar';
import { withKeycloak } from '@react-keycloak/web';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import Input from '@material-ui/core/Input';
import { getAllChartDetails, getTimeZone } from '../../utils/Utils';
import {
  getStoreDetails,
  getStoreNamesByStoreId
} from 'src/utils/hasuraServices';
import { GroupInstanceIdCreator } from '@ag-grid-enterprise/all-modules';
import {
  getLastThirteenMonths,
  getGridorMatrixPayTypeDetails,
  getAllStoreNames
} from 'src/utils/hasuraServices';
import { traceSpan } from 'src/utils/OTTTracing';
import moment from 'moment';
import { setStoreId, setLaborGridTypes } from 'src/actions';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import { INSERT_LOGIN_DETAILS } from 'src/graphql/queries';
import { ReactSession } from 'react-client-session';
import Visibility from '@material-ui/icons/Visibility';
import VisibilityOff from '@material-ui/icons/VisibilityOff';
import { Link } from 'react-router-dom';
var lodash = require('lodash');

const schema = {
  email: {
    presence: { allowEmpty: false, message: 'is required' },
    email: true
  },
  password: {
    presence: { allowEmpty: false, message: 'is required' }
  }
};

const useStyles = makeStyles(theme => ({
  root: {},
  fields: {
    margin: theme.spacing(-1),
    display: 'flex',
    flexWrap: 'wrap',
    '& > *': {
      flexGrow: 1,
      margin: theme.spacing(1)
    }
  },
  submitButton: {
    marginTop: theme.spacing(2),
    width: '100%'
  },
  chips: {
    display: 'flex',
    flexWrap: 'wrap'
  },
  chip: {
    margin: 2
  }
}));
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250
    }
  }
};

function LoginDirect({ className, keycloak, keycloakInitialized, ...rest }) {
  const classes = useStyles();
  const history = useHistory();
  const dispatch = useDispatch();

  const [formState, setFormState] = useState({
    isValid: false,
    values: {},
    touched: {},
    errors: {}
  });
  localStorage.removeItem('storeIdSelected');

  const [storeSlected, setstoreSlected] = useState([]);
  const [storeName, setstoreName] = useState([]);
  const [groupList, setGroups] = useState([]);
  const [userName, setUserName] = useState();
  const [openSnackBar, setSnackbar] = useState(false);
  const [storeLoaded, setStoreLoaded] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const handleClickShowPassword = () => setShowPassword(!showPassword);
  const handleMouseDownPassword = () => setShowPassword(!showPassword);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [emailError, setEmailError] = useState(false);
  const [passwordError, setPasswordError] = useState(false);
  if (keycloak.authenticated) {
    const spanAttribute = {
      pageUrl: '',
      origin: '',
      event: 'Loggedin',
      value: localStorage.getItem('userID'),
      provenance: localStorage.getItem('provenance')
    };
    traceSpan('logged-in', spanAttribute);
  }

  const handleChange = event => {
    setstoreSlected(event.target.value);

    let storeId = [];
    // for (let i = 0; i < event.target.value.length; i++) {
    storeId.push(event.target.value);
    // }

    let groupIds = [];
    groupList.map(value => {
      var targetName = event.target.value.split(' ');
      var groupName = value
        .split('/')[1]
        .split('-')[1]
        .split(' ');
      console.log(
        'valuessss=',
        value,
        '=1=',
        value.split('/')[1],
        '=2=',
        value.split('/')[2],
        '===',
        value.split('/')[2] == groupIds[0]
      );
      if (
        value.includes(event.target.value) &&
        targetName.length == groupName.length
      ) {
        //groupIds.push(value.split('/')[2]);
        groupIds.push(value.split('/').pop());
      }
      if (value.split('/')[2] == groupIds[0]) {
        localStorage.setItem('selectedStoreName', groupIds[0]);
      }
    });
    localStorage.setItem('selectedStoreName', event.target.value);
    localStorage.setItem('storeSelected', event.target.value);
    localStorage.setItem('selectedStoreId', JSON.stringify(groupIds));
    checkInternalToggle();
  };

  const handleSubmit = async event => {
    event.preventDefault();
    let newDate = new Date();
    let date = newDate.getDate();
    Date().toLocaleString();

    let storeValue = JSON.parse(localStorage.getItem('selectedStoreId'));
    let store;
    var offset = getTimeZone();

    if (storeValue && event.target.innerText != 'SIGN IN TO DASHBOARD') {
      let store = Object.values(storeValue);

      if (keycloakInitialized == true && keycloak.authenticated == true) {
        const client = makeApolloClientPostgres;
        client
          .mutate({
            mutation: INSERT_LOGIN_DETAILS,
            variables: {
              // username: localStorage.getItem('storeValue')
              username: session.currentUser,
              storeid: store[0],
              logindate: moment().format('MM-DD-YYYY HH:mm:ss'),
              offset: offset,
              logInout: 'IN'
            }
          })
          .then(result => {});
      }
    }

    ReactSession.set(
      'selectedStoreId',
      localStorage.getItem('selectedStoreId')
    );
    dispatch(setStoreId(localStorage.getItem('selectedStoreId')));
    if (event.target.id == 'login') {
      if (keycloakInitialized) {
        if (!keycloak.authenticated) {
          keycloak.login();
        } else {
        }
      }
    } else {
      // store selection continue button clicked

      if (keycloakInitialized) {
        if (!keycloak.authenticated) {
          keycloak.login();
        } else {
          const spanAttribute = {
            pageUrl: '',
            origin: '',
            event: 'Store changed',
            value: storeSlected,
            provenance: localStorage.getItem('provenance')
          };
          traceSpan('Store changed', spanAttribute);
          if (JSON.parse(localStorage.getItem('selectedStoreId')).length <= 1) {
            history.push('/Home');
          } else {
            history.push('/CPOverview');
          }
        }
      }
    }
  };

  function checkInternalToggle() {
    getPartsPayTypeList();
    let data = [];
    data.push('Customer');
    getGridorMatrixPayTypeDetails('paytype_grid', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        if (
          result.includes('EV') &&
          result.includes('Medium/HD') &&
          result.includes('Standard')
        ) {
          result = ['Standard', 'Medium/HD', 'EV'];
        }
        dispatch(setLaborGridTypes(result));
        localStorage.setItem('laborGridTypes', JSON.stringify(result));
      } else {
        dispatch(setLaborGridTypes(data));
        localStorage.setItem('laborGridTypes', JSON.stringify(data));
      }
    });
  }
  function getPartsPayTypeList() {
    let data = [];
    data.push('Customer');
    getGridorMatrixPayTypeDetails('paytype_matrix', '', result => {
      if (result.length > 0) {
        if (result.includes('Internal')) {
          result.sort();
        }
        if (
          result.includes('Heavy Duty') ||
          result.includes('Fleet') ||
          result.includes('Commercial')
        ) {
          if (
            result[0] == 'Heavy Duty' ||
            result[0] == 'Fleet' ||
            (result[0] == 'Commercial' && !result.includes('RV'))
          ) {
            result.reverse();
          }
        }
        if (
          result.includes('Commercial') &&
          result.includes('RV') &&
          result.includes('Retail')
        ) {
          result = ['Retail', 'Commercial', 'RV'];
        }
        if (
          result.includes('Warranty') &&
          result.includes('Standard') &&
          result.includes('Fleet')
        ) {
          result = ['Standard', 'Warranty', 'Fleet'];
        }
        localStorage.setItem('partsMatrixTypes', JSON.stringify(result));
      } else {
        localStorage.setItem('partsMatrixTypes', JSON.stringify(data));
      }
    });
  }
  const dataLoader = () => {
    return (
      <div class="stage" style={{ marginLeft: '50%' }}>
        <div class="dot-pulse-login"></div>
      </div>
    );
  };
  function deleteAllCookies() {
    var cookies = document.cookie.split(';');

    for (var i = 0; i < cookies.length; i++) {
      console.log('cookies[i]', cookies[i]);
      var cookie = cookies[i];
      var eqPos = cookie.indexOf('=');
      var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
      document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:00 GMT';
    }
  }
  useEffect(() => {
    deleteAllCookies();
  }, []);
  const hasError = field =>
    !!(formState.touched[field] && formState.errors[field]);

  useEffect(() => {
    localStorage.setItem('numTabs', '0');
    localStorage.setItem('storeChange', false);
    // localStorage.setItem('showCurrentMonth', false);
    localStorage.setItem('showCurrentMonth', true);
    console.log(
      'keycloakInitialized======',
      keycloakInitialized == true && keycloak.authenticated == true,
      keycloakInitialized == true,
      keycloak.authenticated == true,
      keycloak
    );
    if (keycloakInitialized) {
      // getStoreNamesByStoreId(keycloak.tokenParsed.groupname, result => {
      //   setstoreName(result);
      // })
      if (keycloak.authenticated) {
        getAllChartDetails();
        setSnackbar(true);
        checkInternalToggle();
        getLastThirteenMonths(result => {
          const startMonth = result[0];
          const endMonth = result[12];
          const datestr = endMonth.split('-');
          const month = datestr[1];
          const year = datestr[0];
          const date = new Date(year, month, 0).getDate();

          let dateRange = [startMonth + '-01', endMonth + '-' + date];
          let dateRange12Months = [result[1] + '-01', endMonth + '-' + date];
          localStorage.setItem('12Months', dateRange12Months);
          localStorage.setItem('13Months', dateRange);
        });
        if (keycloak.tokenParsed.groupname[0] != '') {
          let groups = keycloak.tokenParsed.groupname;
          setUserName(groups);
          let groupArray = [];
          let groupIds = [];
          groups =
            keycloak.realm != 'lupient' &&
            keycloak.realm != 'billknightag' &&
            keycloak.realm != 'karlflammerford'
              ? groups.sort()
              : groups;
          groups.map(value => {
            // groupArray.push(value.split('/')[1]);
            // groupIds.push(value.split('/')[2]);
            if (value.split('/').slice(1, -1).length > 1) {
              groupArray.push(
                value
                  .split('/')
                  .slice(1, -1)
                  .join('/')
              );
            } else {
              groupArray.push(value.split('/')[1]);
            }
            groupIds.push(value.split('/').pop());
          });

          setGroups(groups);

          let stores = lodash.uniqBy(groupArray);

          let storeData = [];
          //if (keycloak.realm == 'firstteamag') {
          getAllStoreNames(groupIds, result => {
            setStoreLoaded(true);
            if (result.length > 0) {
              result.map((val, i) => {
                storeData.push(stores[0].split('-')[0] + '-' + val.storeName);
              });
              console.log('ccc==', result[0]);
              setstoreSlected(
                storeData[0].split('-').length > 2
                  ? storeData[0].split('-')[
                      storeData[0].split('-').length - 2
                    ] +
                      '-' +
                      storeData[0].split('-')[
                        storeData[0].split('-').length - 1
                      ]
                  : storeData[0].split('-')[1]
              );
              //stores = result;
              localStorage.setItem('storeGroup', stores[0].split('-')[0]);
              localStorage.setItem('storeSelected', result[0].storeName);
              // localStorage.setItem(
              //   'selectedStoreId',
              //   JSON.stringify(selectedStores)
              // );
              let storeArr = [result[0].storeId];
              localStorage.setItem('selectedStoreId', JSON.stringify(storeArr));
              localStorage.setItem('selectedStoreName', result[0].storeName);
              console.log(
                'storeData=',
                result[0].storeName,
                JSON.stringify(storeArr)
              );
              setstoreName(storeData);
            } else {
              // localStorage.setItem('partsMatrixTypes', JSON.stringify(data));
            }
          });
          // } else {
          //   setstoreSlected(
          //     stores[0].split('-').length > 2
          //       ? stores[0].split('-')[stores[0].split('-').length - 2] +
          //           '-' +
          //           stores[0].split('-')[stores[0].split('-').length - 1]
          //       : stores[0].split('-')[1]
          //   );
          //   setStoreLoaded(false);
          //   setstoreName(stores);
          // }

          let selectedStores = lodash.uniqBy(groupIds);

          localStorage.setItem('storeGroup', stores[0].split('-')[0]);
          localStorage.setItem('storeSelected', stores[0].split('-')[1]);
          localStorage.setItem(
            'selectedStoreId',
            JSON.stringify(selectedStores[0].split())
          );
          // selectedStores.push(groupIds[0], groupIds[2])
          localStorage.setItem('realm', keycloak.realm);
          keycloakInitialized == true &&
            keycloak.authenticated == true &&
            history.push('/Home');
        }

        setTimeout(() => {
          setSnackbar(false);

          // history.push('/CPOverview');
        }, 200);
      }
      // keycloakInitialized == true &&
      //   keycloak.authenticated == true &&
      //   history.push('/Home');
    } else {
      const spanAttribute = {
        pageUrl: '',
        origin: '',
        event: 'Page Loader',
        value: '',
        provenance: localStorage.getItem('provenance')
      };
      traceSpan('Login Page Loader', spanAttribute);
    }

    const errors = validate(formState.values, schema);

    setFormState(prevFormState => ({
      ...prevFormState,
      isValid: !errors,
      errors: errors || {}
    }));
    if (keycloakInitialized) {
      if (!keycloak.authenticated) {
        keycloak.login();
      }
    }
  }, [formState.values, keycloak.authenticated, keycloakInitialized, keycloak]);

  const handleSubmit1 = event => {
    console.log('handleSubmit1==', email, password);
    event.preventDefault();

    setEmailError(false);
    setPasswordError(false);

    if (email == '') {
      setEmailError(true);
    }
    if (password == '') {
      setPasswordError(true);
    }

    if (email && password) {
      console.log(email, password);
    }
  };
  const loadDrilldown = params => {
    history.push({
      pathname: '/forgotpassword',
      state: {}
    });
  };
  return (
    <>
      <LoaderSkeleton></LoaderSkeleton>
      {/* <React.Fragment>
        <form
          autoComplete="off"
          onSubmit={handleSubmit1}
          style={{
            textAlign: 'center',
            fontFamily: '"Open Sans",Helvetica,Arial,sans-serif'
          }}
        >
          <Typography
            style={{
              marginTop: '-11px',
              padding: 5,
              fontSize: 20
              // float: 'center'
            }}
          >
            Sign In
          </Typography>
          <TextField
            size="small"
            label="Username or Email"
            onChange={e => setEmail(e.target.value)}
            required
            variant="outlined"
            color="secondary"
            type="text"
            sx={{ mb: 3 }}
            fullWidth
            value={email}
            error={emailError}
            style={{
              marginTop: '20px',
              // padding: 5,
              float: 'left'
            }}
          />
          <TextField
            size="small"
            label="Password"
            onChange={e => setPassword(e.target.value)}
            required
            variant="outlined"
            color="secondary"
            value={password}
            error={passwordError}
            type={showPassword ? 'text' : 'password'}
            fullWidth
            sx={{ mb: 3 }}
            style={{
              marginTop: '10px',
              // padding: 5,
              float: 'left'
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={handleClickShowPassword}
                    onMouseDown={handleMouseDownPassword}
                  >
                    {showPassword ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                </InputAdornment>
              )
            }}
          />
          <Link
            style={{
              float: 'right',
              color: '#0088ce',
              textDecoration: 'none',
              fontFamily: 'Open Sans,Helvetica,Arial,sans-serif',
              fontSize: 12,
              lineHeight: 1.66666667
            }}
            onClick={() => loadDrilldown()}
          >
            Forgot Password?
          </Link>
          <Button
            // variant="outlined"
            color="secondary"
            type="submit"
            style={{
              marginTop: '10px',
              // padding: 5,
              float: 'left',
              marginLeft: '2px',
              width: '100%',
              borderRadius: 0,
              backgroundColor: '#276ca1',
              border: '#276ca1',
              color: '#fff',
              textTransform: 'none'
            }}
          >
            Sign In
          </Button>
        </form>
      </React.Fragment> */}
    </>
  );
}
{
  /* {keycloakInitialized == true ? (
        <>
          <Typography gutterBottom variant="h3">
            {keycloakInitialized == true && keycloak.authenticated == true
              ? ''
              : ' Sign in'}
          </Typography>
          <form
            {...rest}
            className={clsx(classes.root, className)}
            onSubmit={handleSubmit}
          >
            {keycloakInitialized == true &&
            keycloak.authenticated == true &&
            keycloak.tokenParsed.groupname.length > 0 ? (
              <>
                <InputLabel
                  style={{ width: '100%', marginBottom: 16, fontSize: 16 }}
                  id="store"
                >
                  <b>Select Your Store </b>
                </InputLabel>
                {console.log('set stores=', storeSlected)}
                {storeLoaded ? (
                  <Select
                    style={{ width: '100%' }}
                    labelId="store-selection"
                    id="store-select"
                    // multiple
                    value={storeSlected}
                    onChange={handleChange}
                    input={<Input id="select-multiple-chip" />}
                    MenuProps={MenuProps}
                  >
                    {keycloak.tokenParsed.groupname.length > 0 &&
                      storeName.map((val, i) => (
                        <MenuItem
                          key={
                            storeName[i].split('-').length > 2
                              ? storeName[i].split('-')[
                                  storeName[i].split('-').length - 2
                                ] +
                                '-' +
                                storeName[i].split('-')[
                                  storeName[i].split('-').length - 1
                                ]
                              : storeName[i].split('-')[1]
                          }
                          value={
                            storeName[i].split('-').length > 2
                              ? storeName[i].split('-')[
                                  storeName[i].split('-').length - 2
                                ] +
                                '-' +
                                storeName[i].split('-')[
                                  storeName[i].split('-').length - 1
                                ]
                              : storeName[i].split('-')[1]
                          }
                        > */
}
{
  /* {val.substring(val.indexOf('/') + 1)} */
}
{
  /* {val.split('-').length > 2
                            ? val.split('-')[val.split('-').length - 2] +
                              '-' +
                              val.split('-')[val.split('-').length - 1]
                            : val.split('-')[1]}
                        </MenuItem>
                      ))}
                  </Select>
                ) : (
                  dataLoader()
                )}
              </>
            ) : (
              <>
                {keycloakInitialized == true &&
                keycloak.authenticated == true ? (
                  <>
                    <InputLabel
                      style={{ width: '100%', marginBottom: 16 }}
                      id="store"
                    >
                      Select the Store to continue
                    </InputLabel>
                    <Select
                      style={{ width: '100%' }}
                      labelId="store-selection"
                      id="store-select"
                      value={storeSlected}
                      onChange={handleSubmit}
                    >
                      {keycloak.tokenParsed.groupname.length > 0 &&
                        keycloak.tokenParsed.groupname.map((val, i) => (
                          <MenuItem
                            key={keycloak.tokenParsed.groupname[i]}
                            value={keycloak.tokenParsed.groupname[i]}
                          >
                            {val.substring(val.indexOf('/') + 1)}
                          </MenuItem>
                        ))}
                    </Select>
                  </>
                ) : null}
              </>
            )}

            <Button
              id="login"
              className={classes.submitButton}
              color="secondary"
              size="large"
              type="submit"
              disabled={
                storeLoaded
                  ? false
                  : keycloakInitialized == true &&
                    keycloak.authenticated == true
                  ? true
                  : false
              }
              variant="contained"
            >
              {keycloakInitialized == true && keycloak.authenticated == true
                ? 'View Dashboard'
                : ' Sign in to Dashboard'}
            </Button> */
}

{
  /* <SuccessSnackbar
            onClose={() => setSnackbar(false)}
            open={openSnackBar}
          /> */
}
{
  /* </form>
        </>
      ) : (
        <LoaderSkeleton></LoaderSkeleton>
      )}
    </> */
}
{
  /* );
} */
}

// LoginFormTest.propTypes = {
//   className: PropTypes.string
// };

export default withKeycloak(LoginDirect);
