import React, { useEffect, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { makeStyles } from '@material-ui/styles';
import {
  Card,
  CardContent,
  CardMedia,
  Typography,
  Divider,
  Grid,
  Avatar
} from '@material-ui/core';
import LockIcon from '@material-ui/icons/Lock';
import Page from 'src/components/Page';
import gradients from 'src/utils/gradients';
import LoginDirect from './LoginDirect';
import { withKeycloak } from '@react-keycloak/web';
import { version, launchDate } from 'src/appversion.json';
import Marquee from 'react-fast-marquee';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';

const useStyles = makeStyles(theme => ({
  root: {
    marginTop: '10px',
    // height: '70%',
    // height: 30,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing(6, 2)
  },
  card: {
    width: theme.breakpoints.values.md,
    maxWidth: '100%',
    overflow: 'visible',
    display: 'flex',
    position: 'relative',
    '& > *': {
      flexGrow: 1,
      flexBasis: '50%',
      width: '50%'
    }
  },
  content: {
    padding: theme.spacing(8, 4, 3, 4)
  },
  media: {
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
    padding: theme.spacing(3),
    color: theme.palette.common.white,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-end',
    [theme.breakpoints.down('md')]: {
      display: 'none'
    }
  },
  icon: {
    backgroundImage: gradients.green,
    color: theme.palette.common.white,
    borderRadius: theme.shape.borderRadius,
    padding: theme.spacing(1),
    position: 'absolute',
    top: -32,
    left: theme.spacing(3),
    height: 64,
    width: 64,
    fontSize: 32
  },
  loginForm: {
    marginTop: theme.spacing(3)
  },
  divider: {
    margin: theme.spacing(2, 0)
  },
  person: {
    marginTop: theme.spacing(2),
    display: 'flex'
  },
  launch: {
    marginTop: theme.spacing(2),
    display: 'flex',
    marginLeft: 'auto'
  },
  avatar: {
    marginRight: theme.spacing(2)
  },
  note: {
    marginTop: 60,
    // fontWeight: 'bold',
    display: 'grid',
    justifyContent: 'center',
    color: 'red',
    textAlign: 'center'
  }
}));

function Login({ keycloak, keycloakInitialized }) {
  const classes = useStyles();
  const token = localStorage.getItem('tokenexpired');
  const [message, setMessage] = useState([]);
  const delete_cookie = function(name) {
    document.cookie = name + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/';
  };
  if (token) {
    localStorage.removeItem('tokenexpired');
    var logoutOptions = { redirectUri: keycloak.redirectUri };

    keycloak
      .logout(logoutOptions)
      .then(success => {
        localStorage.removeItem('selectedStoreId');
        localStorage.clear();
        localStorage.setItem('showCurrentMonth', false);
        delete_cookie('otContext');
        delete_cookie('userIP');
        delete_cookie('userLocation');
        localStorage.removeItem('selectedTech');
        /*  setTimeout(() => {
        history.entries = [];
        // history.push() auto increments from the current index
        history.index = -1;
         history.push('/login');
        history.push('/auth/login');
        localStorage.setItem('isAuthenticated', false);
      }, 200);*/
        localStorage.setItem('isAuthenticated', false);
      })
      .catch(error => {
        console.log('--> log: logout error ', error);
      });
  }
  return (
    <>
      <LoginDirect className={classes.loginForm} />
      {/* <Typography
        style={{
          paddingTop: '130px',
          overflow: 'visible',
          color: '#4d5258',
          padding: '8px',
          textAlign: 'center',
          fontSize: '29px',
          textTransform: 'uppercase',
          letterSpacing: '3px',
          lineHeight: '1.2em',
          padding: '62px 10px 20px px',
          whiteSpace: 'normal',
          fontFamily: '"Open Sans",Helvetica,Arial,sans-serif'
        }}
        variant="h4"
        // className={clsx(classes.mainLabel)}
      >
        Bill Knight Auto Group
      </Typography>
      <Page className={classes.root} title="Login">
        <Card className={classes.card}>
          <CardContent className={classes.content}>
            <LockIcon className={classes.icon} />
            <LoginFormTest className={classes.loginForm} />
            <Divider
              className={classes.divider}
              style={{
                display:
                  keycloakInitialized == true && keycloak.authenticated == false
                    ? 'block'
                    : 'none'
              }}
            />
          </CardContent>
          <CardMedia
            className={classes.media}
            image="/images/auth.png"
            title="Cover"
          >
            <div className={classes.person}>
              <div>
                <Typography color="inherit" variant="body1">
                  {version ? 'Current Dashboard' : ''}
                </Typography>
                &nbsp;
                <Typography color="inherit" variant="body1">
                  {version ? 'Version ' + version : ''}&nbsp;
                </Typography>
              </div>
            </div>
            <div className={classes.launch}></div>
          </CardMedia>
        </Card>
      </Page> */}
    </>
  );
}

export default withKeycloak(Login);
