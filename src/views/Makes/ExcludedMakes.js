import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import '@fortawesome/fontawesome-free/css/all.min.css';
import {
  Box,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  Toolbar,
  Grid
} from '@material-ui/core';
import clsx from 'clsx';
import { withStyles } from '@material-ui/styles';
import RestoreIcon from '@material-ui/icons/Restore';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import { UPDATE_OPCODE, ADD_REFRESH_STATUS } from 'src/graphql/queries';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import {
  getOpcodeDetails,
  updateRefreshViewsData,
  getRefreshViewsStatus,
  updateRefreshStatus,
  getIncludedMakesDetails,
  getExcludedMakesDetails
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import PropTypes from 'prop-types';
import { SET_REFRESH_STATUS } from 'src/actions';
import { connect } from 'react-redux';
import { setRefreshStatus } from '../../actions';

class ExcludedMakes extends React.Component {
  componentDidUpdate(prevProps) {
    if (
      prevProps.session.storeSelected &&
      JSON.parse(localStorage.getItem('selectedStoreId'))
    ) {
      if (
        JSON.parse(localStorage.getItem('selectedStoreId'))[0] !=
        JSON.parse(prevProps.session.storeSelected)[0]
      ) {
        console.log(
          'stores1=',
          JSON.parse(localStorage.getItem('selectedStoreId'))[0] ==
            JSON.parse(prevProps.session.storeSelected)[0]
        );
        this.getAgGridData();
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  constructor(props) {
    super(props);
    const startEdit = this;
    this.state = {
      setReload: false,
      setMessage: '',
      setOpenAlert: false,
      setOpenAlertError: false,
      setOpen: false,
      setOpenRow: false,
      showRefresh: 'none',
      disableTable: 'all',
      rawGridApi: {},
      tabSelection: 'one',
      // eslint-disable-next-line react/no-unused-state
      gridApi: {},
      isLoading: true,
      columnDefs: [
        {
          headerName: 'Manufacturer',
          chartDataType: 'series',
          width: 150,
          field: 'manufacturer',
          cellClass: 'textAlign',
          editable: false,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left' };
          }
        },
        {
          headerName: 'Makes',
          chartDataType: 'series',
          width: 220,
          field: 'excludedMakes',
          editable: false,
          cellClass: 'textAlign',

          suppressMenu: true,
          unSortIcon: true,
          cellStyle() {
            return { textAlign: 'left' };
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          hide: true,
          filter: false,
          sortable: false,
          width: 330,
          suppressMenu: true,

          cellStyle(params) {
            return { textAlign: 'center' };
          },
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            const index = params.rowIndex;
            const eDiv = document.createElement('div');

            if (params.data.id === '' && params.data.name === '') {
              eDiv.innerHTML = `<button title="Edit" id="btnedit${index}" style="background: #384163; color: #fff; display:none;disabled; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>`;
            } else {
              eDiv.innerHTML = `<button title="Edit" id="btnedit${index}" style="background: #38416373; color: #fff; border-radius: 3px; width: 30px;disabled; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button"><i class="fas fa-pencil-alt"></i></button> <button title="Cancel" id="btncancel${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button" ><i class="fas fa-ban"></i></button>&nbsp;<button title="Save" id="btnupdate${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button" ><i class="fas fa-save"></i></button>`;
            }

            const eButton = eDiv.querySelectorAll('.edit-button')[0];
            eButton.addEventListener('mouseover', () => {
              $('.edit-button').attr('disabled', 'disabled');
              $('.edit-button').css('background', '#38416373');
              $('.edit-button').css('cursor', 'default');
            });
            if (
              props.keycloak &&
              (props.keycloak.realmAccess.roles.includes('admin') == false ||
                props.keycloak.realmAccess.roles.includes('superadmin') ==
                  false)
            ) {
              $(document).ready(function() {
                $('.edit-button').attr('disabled', 'disabled');
                $('.edit-button').css('background', '#38416373');
                $('.edit-button').css('cursor', 'default');
              });
            }

            // const eButton = eDiv.querySelectorAll('.edit-button')[0];
            // eButton.addEventListener('mouseover', () => {
            //   $('.edit-button').attr('disabled', 'disabled');
            //   $('.edit-button').css('background', '#38416373');
            // });

            if (index !== undefined) {
              const eButton = eDiv.querySelectorAll('.edit-button')[0];
              const uButton = eDiv.querySelectorAll('.update-button')[0];
              const cButton = eDiv.querySelectorAll('.cancel-button')[0];
              const dButton = eDiv.querySelectorAll('.delete-button')[0];
              eButton.addEventListener('click', () => {
                startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);
                $(`#btnedit${index}`).hide();
                $(`#btncancel${index}`).show();
                $(`#btnupdate${index}`).show();
              });
              uButton.addEventListener('click', () => {
                const selectedId = params.data.slno;
                const selectedOpcat = params.data.opcategory;
                const selectedOpcode = params.data.opcode;
                const storeId = JSON.parse(
                  localStorage.getItem('selectedStoreId')
                )[0];
                if (selectedId !== '') {
                  startEdit.updateOpcodes(
                    selectedOpcat,
                    selectedOpcode,
                    storeId
                  );
                }
                $(`#btnedit${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
              });
              cButton.addEventListener('click', () => {
                $(`#btnedit${index}`).show();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
              });
            }
            return eDiv;
          }
        }
      ],

      rowData: [],
      defaultColDef: {
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        editable: true,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Center',
            vertical: 'Center'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Left'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }

  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.setState({ gridcolumnApi: params.columnApi });

    // this.gridApi.sizeColumnsToFit();
    this.getAgGridData();
  };

  getAgGridData() {
    this.setState({ isLoading: true });
    getExcludedMakesDetails(result => {
      this.setState({ isLoading: false });
      if (result.data.statelessCcPhysicalRwGetExcludedMakes.nodes) {
        this.setState({
          rowData: result.data.statelessCcPhysicalRwGetExcludedMakes.nodes
        });
      }
    });
  }
  onBtExport = () => {
    const params = {
      sheetName: 'Report',
      columnWidth: 150,
      columnKeys: ['makesId', 'manufacturer', 'excludedMakes'],
      fileName: 'Excluded Makes',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Excluded Makes' },
            mergeAcross: 3
          }
        ]
      ]
    };

    // eslint-disable-next-line react/destructuring-assignment
    this.state.rawGridApi.exportDataAsExcel(params);
  };
  resetReportGrid = () => {
    // this.gridApi.setColumnDefs([]);
    // this.gridApi.setColumnDefs(this.state.columnDefs);
    window.sortState = {};
    window.filterState = {};
    this.state.gridcolumnApi.resetColumnState();
    this.gridApi.setSortModel(null);
    this.gridApi.setFilterModel(null);
  };
  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        <div>
          <Paper
            square
            style={{
              marginLeft: 8,
              marginRight: 8,
              marginBottom: 8,
              height: 45
            }}
          >
            <Toolbar>
              <Grid container justify="flex-start" style={{ padding: '5px' }}>
                <Tooltip title="Export To Excel">
                  <Link
                    id="export-to-excel"
                    style={{
                      marginTop: -28,
                      marginLeft: 'auto',
                      cursor: 'pointer'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip>
                <Button
                  variant="contained"
                  id="reset-layout"
                  className={clsx(classes.back, 'reset-btn')}
                  onClick={this.resetReportGrid}
                >
                  <RestoreIcon />
                  <Typography variant="body1" align="left">
                    Reset Layout
                  </Typography>
                </Button>
              </Grid>
            </Toolbar>
          </Paper>
        </div>
        {this.state.isLoading === true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <div
          id="data-tab"
          className="ag-theme-balham"
          style={{
            height: `${window.innerHeight - 190}px`,
            width: '365px',
            alignContent: 'center',
            marginLeft: '8px',
            display: this.state.tabSelection === 'two' ? 'none' : 'block'
          }}
        >
          <AgGridReact
            className="ag-theme-balham"
            style={{
              height: '500px',
              width: '100%'
            }}
            autoGroupColumnDef={this.state.autoGroupColumnDef}
            getChartToolbarItems={this.getChartToolbarItems}
            modules={AllModules}
            columnDefs={this.state.columnDefs}
            defaultColDef={this.state.defaultColDef}
            onGridReady={this.onGridReady}
            suppressAggFuncInHeader
            rowData={this.state.rowData}
            excelStyles={this.state.excelStyles}
            tooltipShowDelay={0}
            floatingFilter={true}
            enableRangeSelection={true}
            suppressClickEdit={true}
            animateRows={true}
            enableCharts={true}
            suppressDragLeaveHidesColumns={true}
            suppressContextMenu={true}
          />
        </div>
      </div>
    );
  }
}
const styles = theme => ({
  back: {
    marginRight: 13,
    float: 'right',
    marginTop: -28
  }
});

export default withStyles(styles)(ExcludedMakes);
