import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import FixedRateGrid from './FixedRateGrid';
import { getDrillDownMonthYears } from 'src/utils/hasuraServices';
import moment from 'moment';
import { useHistory } from 'react-router';
import LoaderSkeleton from 'src/components/LoaderSkeleton';
import { withKeycloak } from '@react-keycloak/web';
import { Redirect } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
//import queryString from 'query-string';

const useStyles = makeStyles(theme => ({
  root: {
    paddingTop: theme.spacing(0),
    paddingBottom: theme.spacing(3)
  },
  statistics: {
    marginTop: theme.spacing(3)
  },
  notifications: {
    marginTop: theme.spacing(6)
  },
  projects: {
    marginTop: theme.spacing(6)
  },
  todos: {
    marginTop: theme.spacing(6)
  }
}));

const FixedRates = props => {
  const classes = useStyles();
  const dispatch = useDispatch();
  const history = useHistory();
  const [months, setMonths] = useState([]);
  const [selectedDates, setSelectedDates] = useState([]);
  const session = useSelector(state => state.session);
  let drillDownType = '';

  if (props.location.pathname == '/WarrantyRatesLabor') {
    drillDownType = 'labor';
  } else {
    drillDownType = 'parts';
  }

  return (
    <Page className={classes.root} title="Fixed Rate History">
      {/* {props.keycloak.realmAccess.roles.includes('client') ||
        props.keycloak.realmAccess.roles.includes('user') ? (
          <Redirect to="/errors/error-404" />
        ) : ( */}
      {(JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
        history &&
        history.location.state &&
        history.location.state.pageType != 'LaborMisses' &&
        history.location.state.pageType != 'PartsMisses') ||
      (JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 &&
        history.location.state == undefined) ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <FixedRateGrid
          history={history}
          drillDownType={drillDownType}
          session={session}
        />
      )}
      {/* )} */}
    </Page>
  );
};

export default withKeycloak(FixedRates);
