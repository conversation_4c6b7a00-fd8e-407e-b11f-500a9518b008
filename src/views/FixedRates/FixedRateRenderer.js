import React, { Component } from 'react';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tooltip,
  Link,
  FormControl,
  FormHelperText,
  TextField,
  InputLabel,
  Select,
  MenuItem
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputAdornment from '@material-ui/core/InputAdornment';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import clsx from 'clsx';
import moment from 'moment';
import 'src/input-style.css';

class FixedRateRenderer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isFixedRateChecked: false,
      fixedValue:
        this.props.data.fixedRateValue == null
          ? ''
          : this.props.data.fixedRateValue,
      fixedDate:
        this.props.data.fixedRateDate != null
          ? this.props.data.fixedRateDate
          : new Date()
    };

    this.handleCallback = this.handleCallback.bind(this);
    this.handleFixedRateChange = this.handleFixedRateChange.bind(this);
  }

  handleCallback = (start, end, label) => {
    let colId = this.props.column.colId;
    this.setState({
      fixedDate: start.format('MM/DD/YY')
    });

    if (this.props.column.colId == 'fixedRateDate') {
      this.props.context.componentParent.onFixedDateChanged(
        this.props.data.fixedRateDate,
        start.format('YYYY-MM-DD')
      );

      //this.props.node.setDataValue(colId, start.format('MM/DD/YY'));
    }
  };
  handleFixedRateChange(event) {
    let colId = this.props.column.colId;

    this.setState({
      fixedValue: event.target.value
    });

    if (this.props.column.colId == 'fixedRateValue') {
      this.props.context.componentParent.onFixedRateChanged(
        this.props.data.fixedRateValue,
        event.target.value
      );
    }
  }
  render() {
    const { classes } = this.props;

    var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    return this.props.colDef.field == 'fixedRateValue' ? (
      <>
        <FormControl
          variant="outlined"
          className="fixedRateValue"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? 'block'
                : 'none'
          }}
        >
          <OutlinedInput
            className={classes.formControl}
            id="outlined-adornment-weight"
            value={this.state.fixedValue}
            error={
              this.props.context.componentParent.state.laborfixedRateError ==
              null
                ? false
                : this.props.context.componentParent.state
                    .laborfixedRateError == this.props.rowIndex
                ? true
                : false
            }
            title="Fixed Rate($)"
            onBlur={this.handleFixedRateChange}
            onChange={e =>
              (e.target.value === '' || regEx.test(e.target.value)) &&
              this.setState({
                fixedValue: e.target.value
              })
            }
            // onChange={this.handleFixedRateChange}
            startAdornment={
              <InputAdornment
                classes={{ root: classes.adorment }}
                position="start"
                disableTypography={true}
              >
                $
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
          className="fixedRateValueText"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.fixedRateValue != null &&
            (/^\d/.test(this.props.data.fixedRateValue)
              ? '$' + this.props.data.fixedRateValue
              : this.props.data.fixedRateValue + '%')}
        </span>
      </>
    ) : // <input
    //   type="text"
    //   style={{ display: this.props.data.fixedRate == 1 ? 'block' : 'none' }}
    //   className="fixedRate-value"
    //   disabled={
    //     this.props.context.componentParent.state.editedRowId == null
    //       ? true
    //       : this.props.context.componentParent.state.editedRowId !=
    //         this.props.rowIndex
    //       ? true
    //       : false
    //   }
    //   // onClick={this.checkedHandler}
    // />
    this.props.colDef.field == 'fixedRateDate' ? (
      <>
        <FormControl
          variant="outlined"
          margin="dense"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? 'block'
                : 'none'
          }}
          className={clsx(classes.formControlDate, 'input-container')}
        >
          <DateRangePicker
            initialSettings={{
              // maxDate: {
              //   date: new Date(),
              // },
              // minDate:{
              //   date: (this.props.selectedDates[1])
              // },
              locale: {
                format: 'MM/DD/YY',
                separator: ' - '
              },
              autoUpdateInput: true,
              showDropdowns: true,
              autoApply: true,
              singleDatePicker: true,
              maxDate: moment().toDate(),
              // alwaysShowCalendars: true,
              applyClass: clsx(classes.calButton, 'apply-btn'),
              cancelClass: clsx(classes.calButton, 'apply-btn'),
              startDate: new Date(this.state.fixedDate)
              //showDropdowns: true
            }}
            value={this.state.fixedDate}
            onCallback={this.handleCallback}
          >
            <input
              type="text"
              className={classes.fixeddatepicker}
              id="picker"
              name="picker"
              aria-labelledby="label-picker"
            />
            {/* <TextField
          id="outlined-basic"
          label="Select Date"
          size="small"
          //onChange={}
          value={this.state.value}
          variant="outlined"
        /> */}
          </DateRangePicker>
          {/* <label class="labelpicker" for="picker" id="label-picker">
          <div class="textpicker">Select Date</div>
        </label> */}
        </FormControl>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.fixedRateDate != null &&
            this.props.data.fixedRateDate}
        </span>
      </>
    ) : (
      ''
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: 8,
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '80px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,

    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  }
});
export default withStyles(styles)(FixedRateRenderer);
