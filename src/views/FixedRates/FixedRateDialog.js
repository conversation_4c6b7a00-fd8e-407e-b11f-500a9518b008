import React, { useRef, useState, useEffect } from 'react';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField,
  RadioGroup,
  Radio
} from '@material-ui/core';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles, withStyles } from '@material-ui/styles';
import clsx from 'clsx';
import 'src/styles.css';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import MuiTableCell from '@material-ui/core/TableCell';
import FormHelperText from '@material-ui/core/FormHelperText';
import 'bootstrap-daterangepicker/daterangepicker.css';
import _ from 'lodash';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-date-picker/dist/DatePicker.css';
import 'react-calendar/dist/Calendar.css';
import { DatePicker } from '@material-ui/pickers';

require('lodash');
const TableCell = withStyles({
  root: {
    borderBottom: 'none'
  }
})(MuiTableCell);
const useStyles = makeStyles(theme => ({
  root: {},
  menuItem: {
    padding: 0
  },
  formControl: {
    minWidth: 120
  },
  formControl1: {
    minWidth: 160
  },
  cardControl: {
    padding: 0
  },
  formControlLabel: {
    // padding: theme.spacing(0.2, 1),
    fontSize: 12
  },
  container: {
    alignItems: 'center',
    margin: '10px 0px',
    //width: '85%',
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%'
  },
  closeButton: {
    position: 'absolute',
    //padding: 20,
    right: theme.spacing(1),
    top: theme.spacing(1),
    //bottom: theme.spacing(1),
    color: theme.palette.grey[500]
  },
  paper: {
    '@media (max-width: 1920px)': {
      maxWidth: '1150px',
      maxHeight: '700px'
    },

    '@media (max-width: 1440px)': {
      maxWidth: '1500px',
      maxHeight: '610px'
    },

    '@media (max-width: 1280px)': {
      maxWidth: '1500px',
      maxHeight: '600px'
    },
    '@media (max-width: 2304px)': {
      maxWidth: '1350px',
      maxHeight: '900px'
    }
  },
  paperTranches: {
    maxWidth: '1250px',
    maxHeight: '800px'
  },
  paperItemization: {
    maxWidth: '100%',
    maxHeight: '1400px'
  },
  flexItem: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  paperBar: {
    // maxWidth: '1300px',
    maxWidth: '100%',
    maxHeight: '1400px'
    // marginLeft: '17%',
    // marginTop: '8%',
    // marginRight: '0%'
  },
  divDisplay: {
    //marginBottom: 45
  },
  divDisplayAdv: {
    //marginTop: 47
  },
  textField1: {
    fontSize: '13px !important'
  },
  inputLabelOpcode: {
    fontSize: '12px !important',
    marginTop: '5px !important',
    marginLeft: '11px !important'
  },
  inputValueOpcode: {
    fontSize: '15px !important',
    marginLeft: '11px !important'
  },
  formControlSelect: {
    marginTop: '13px !important',
    marginRight: '5px !important'
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function FixedRateDialog({
  open,
  handlePopupClose,
  opcodes,
  insertFixedRateDetails,
  type,
  addSuccess
}) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(open);
  const [alertMsg, setAlertMsg] = useState('');
  const [openAlert, setOpenAlert] = React.useState(false);
  const [fixedRateValue, setFixedRateValue] = useState('');
  const [error, setError] = useState('');
  const [errorChecked, setErrorChecked] = useState('');
  const [requiredText, setRequiredText] = useState(false);
  const [requiredOpcodeSelect, setRequiredOpcodeSelect] = useState(false);
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [selectedOpcode, setSelectedOpcode] = useState('');
  const [selectedPaytype, setSelectedPaytype] = useState('Customer');
  const [selectedType, setSelectedType] = useState('labor');
  const [partsMarkup, setPartsMarkup] = useState('Cost');

  const label = { inputProps: { 'aria-label': 'Checkbox demo' } };
  useEffect(() => {
    setOpenDialog(open);
  }, [open]);

  useEffect(() => {
    if (addSuccess == true) {
      handleOk();
    }
  }, [addSuccess]);

  const handleOk = () => {
    setOpenDialog(false);
    handlePopupClose();
    setError('');
    setRequiredText(false);
    setRequiredOpcodeSelect(false);
    setFixedRateValue('');
    setSelectedOpcode('');
    setSelectedDate(new Date());
    setSelectedPaytype('Customer');
    setSelectedType('labor');
    setPartsMarkup('Cost');
  };
  const handleSave = params => {
    handleSaveFixedRate(params);
  };
  const handleSaveFixedRate = params => {
    if ((fixedRateValue == undefined || fixedRateValue == '') && error == '') {
      setError(false);
      setRequiredText(true);
    }
    if (selectedOpcode == undefined || selectedOpcode == '') {
      setRequiredOpcodeSelect(true);
    }
    if (requiredOpcodeSelect == false && requiredText == false) {
      let dateSelected = selectedDate;
      if (selectedDate['_d'] == undefined) {
        dateSelected = selectedDate.toLocaleDateString('en-US');
      } else {
        dateSelected = selectedDate['_d'].toLocaleDateString('en-US');
      }
      let arr = [];
      arr.push(
        selectedOpcode,
        selectedPaytype,
        fixedRateValue,
        dateSelected,
        selectedType,
        partsMarkup
      );
      insertFixedRateDetails(arr);
      if (addSuccess) {
        handleOk();
      }
    }
  };
  const onChangeEmail = e => {
    setFixedRateValue(e.target.value);
    if (e.target.value) {
      setRequiredText(false);
      setError('');
    } else {
      setRequiredText(true);
    }
  };

  const validateEmail = e => {
    setFixedRateValue(e.target.value);

    let error = null;
    var isEmail = /^\d*\.?\d*$/.test(e.target.value);
    if (!isEmail) {
      if (e.target.value) {
        error = 'Fixed Rate is not valid, Accept Only Numbers';
      } else {
        setError('');
      }
    }
    if (error) {
      setError(error);
      //setFixedRateValue('');
      setRequiredText(false);
      return false;
    } else {
      setError('');
      setFixedRateValue(e.target.value);
    }

    return true;
  };

  const dateChange = e => {
    setSelectedDate(e);
  };

  const handleOpcodeChange = event => {
    setSelectedOpcode(event.target.value);
    if (event.target.value) {
      setRequiredOpcodeSelect(false);
    }
  };

  const handleChangeTypes = event => {
    if (event.target.value) {
      setSelectedType(event.target.value);
      setError('');
      setRequiredText(false);
      setRequiredOpcodeSelect(false);
      setFixedRateValue('');
      setSelectedOpcode('');
      setSelectedDate(new Date());
    }
  };

  const handleMarkupChange = event => {
    if (event.target.value) {
      setPartsMarkup(event.target.value);
    }
  };
  return (
    <Dialog
      transition={Fade}
      classes={{
        paper: classes.paper
      }}
      BackdropProps={{
        classes: {
          root: classes.backDrop
        }
      }}
      //maxWidth="xl"
      // style={{ maxWidth: 900, maxHeight: 700 }}
      open={openDialog}
    >
      <DialogTitle id="confirmation-dialog-title">
        <Typography
          variant="h5"
          color="primary"
          style={{
            textTransform: 'none'
          }}
        >
          Add Fixed Rate
        </Typography>
      </DialogTitle>
      <Collapse in={openAlert}>
        <Alert
          action={
            <IconButton
              aria-label="close"
              color="inherit"
              size="small"
              onClick={() => {
                setOpenAlert(false);
              }}
            >
              <CloseIcon fontSize="inherit" />
            </IconButton>
          }
          style={{ margin: '10px 20px' }}
        >
          {alertMsg}
        </Alert>
      </Collapse>
      <DialogContent style={{ overflowX: 'hidden' }}>
        <TableContainer
          component={Paper}
          style={{
            margin: 4,
            padding: 1,
            display: 'block',
            width: '100%'
          }}
        >
          <Table
            className="email-table"
            id="maildetails"
            // style={{ minWidth: 300 }}
            size="small"
            aria-label="a dense table"
          >
            <TableHead
              style={{
                textAlign: 'center',
                backgroundColor: '#003d6b'
              }}
            ></TableHead>
            <TableRow key={'email'}>
              {type == 'payTypes' ? (
                <TableCell
                  align="left"
                  size="small"
                  style={{
                    fontSize: 14,
                    color: '#003d6b'
                  }}
                >
                  {''}
                </TableCell>
              ) : null}
              <TableCell
                align="left"
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                {type == 'opcodes' ? 'Opcode' : 'Pay Type'}
              </TableCell>
              {type == 'opcodes' ? (
                <TableCell
                  align="left"
                  size="small"
                  style={{
                    fontSize: 14,
                    color: '#003d6b'
                  }}
                >
                  Pay Type
                </TableCell>
              ) : null}
              <TableCell
                align="left"
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                Fixed Rate
              </TableCell>
              <TableCell
                align="left"
                size="small"
                style={{
                  fontSize: 14,
                  color: '#003d6b'
                }}
              >
                {type == 'opcodes' ? 'Install Date' : 'Effective From'}
              </TableCell>
            </TableRow>

            <TableRow>
              {type == 'payTypes' ? (
                <TableCell height={10} width={120}>
                  <FormControl>
                    <RadioGroup
                      aria-labelledby="demo-controlled-radio-buttons-group"
                      name="controlled-radio-buttons-group"
                      id="fixedratetypes"
                      value={selectedType}
                      onChange={handleChangeTypes}
                    >
                      <FormControlLabel
                        value="labor"
                        style={{ marginTop: -6 }}
                        control={<Radio size="small" />}
                        label="Labor"
                      />
                      <FormControlLabel
                        value="parts"
                        control={<Radio size="small" />}
                        label="Parts"
                      />
                    </RadioGroup>
                  </FormControl>
                </TableCell>
              ) : null}
              <TableCell height={10}>
                <FormControlLabel
                  control={
                    <div
                      style={{
                        display: 'block',
                        marginTop: type == 'opcodes' ? -48 : -32,
                        paddingTop: requiredOpcodeSelect ? 22 : ''
                      }}
                      id="store_schedule"
                      className={classes.divDisplay}
                    >
                      {type == 'opcodes' ? (
                        <FormControl
                          variant="standard"
                          sx={{ m: 1, width: 140 }}
                          className={classes.formControl}
                        >
                          <InputLabel
                            htmlFor="outlined-age-native-simple"
                            margin="dense"
                            className={classes.inputLabelOpcode}
                            style={{
                              display: selectedOpcode == '' ? 'block' : 'none'
                            }}
                          >
                            Choose Opcode
                          </InputLabel>
                          <Select
                            className={classes.inputValueOpcode}
                            value={selectedOpcode}
                            onChange={handleOpcodeChange}
                            style={{ fontSize: '14px !important' }}
                          >
                            {opcodes.map(val => (
                              <MenuItem value={val.opcode}>
                                {val.opcode}
                              </MenuItem>
                            ))}
                          </Select>
                          {requiredOpcodeSelect ? (
                            <FormHelperText
                              style={{
                                color: 'rgb(255,91,71)',
                                marginLeft: 12
                              }}
                            >
                              This is required!
                            </FormHelperText>
                          ) : (
                            ''
                          )}
                        </FormControl>
                      ) : (
                        <FormControl
                          variant="standard"
                          sx={{ m: 1, width: 140 }}
                          className={classes.formControl}
                        >
                          <InputLabel
                            htmlFor="outlined-age-native-simple"
                            margin="dense"
                            className={classes.inputLabelOpcode}
                            style={{
                              display: selectedOpcode == '' ? 'block' : 'none'
                            }}
                          >
                            Choose Pay Type
                          </InputLabel>
                          <Select
                            className={classes.inputValueOpcode}
                            value={selectedOpcode}
                            onChange={handleOpcodeChange}
                            style={{ fontSize: '14px !important' }}
                          >
                            {opcodes.map(val => (
                              <MenuItem value={val.payType}>
                                {val.payType}
                              </MenuItem>
                            ))}
                          </Select>
                          {requiredOpcodeSelect ? (
                            <FormHelperText
                              style={{
                                color: 'rgb(255,91,71)',
                                marginLeft: 12
                              }}
                            >
                              This is required!
                            </FormHelperText>
                          ) : (
                            ''
                          )}
                        </FormControl>
                      )}
                    </div>
                  }
                />
              </TableCell>
              {type == 'opcodes' ? (
                <TableCell height={10}>
                  <TextField
                    style={{
                      // width: '100px',
                      width: '70px',
                      bottom: 19
                    }}
                    variant="standard"
                    margin="normal"
                    required
                    fullWidth
                    disabled
                    id="paytype"
                    name="paytype"
                    value={'Customer'}
                    className={classes.textField1}
                    inputProps={{ style: { fontSize: 15 } }} // font size of input text
                    InputLabelProps={{ style: { fontSize: 15 } }}
                    InputProps={{
                      readOnly: true
                    }}
                  />
                </TableCell>
              ) : null}
              <TableCell height={10}>
                {type == 'payTypes' && selectedType == 'parts' ? (
                  <select
                    style={{
                      marginTop: '17px !important'
                    }}
                    title="Parts fixed rate"
                    onChange={handleMarkupChange}
                    value={partsMarkup}
                    className={clsx(
                      classes.formControlSelect,
                      'partsFixedRateSelect'
                    )}
                    name="parts-markups"
                    id="parts-markups"
                  >
                    <option value="Cost">Cost</option>
                    <option value="Cost-">Cost-</option>
                    <option value="Cost+">Cost+</option>
                    <option value="Cost%">Cost%</option>
                    <option value="List">List</option>
                    <option value="List-">List-</option>
                    <option value="List+">List+</option>
                    <option value="List%">List%</option>
                  </select>
                ) : null}
                <TextField
                  style={{
                    width: '72px',
                    bottom:
                      (type == 'payTypes' && !requiredText) ||
                      (type == 'opcodes' && requiredText)
                        ? 12
                        : type == 'payTypes' && requiredText
                        ? 3
                        : 21
                  }}
                  variant="standard"
                  margin="normal"
                  required
                  fullWidth
                  id="fixedRate"
                  name="fixedRate"
                  value={fixedRateValue}
                  onChange={validateEmail}
                  //onChange={onChangeEmail}
                  helperText={requiredText && 'This is required!'}
                  InputProps={{
                    readOnly: false
                  }}
                />
              </TableCell>
              <TableCell height={10} width={type == 'opcodes' ? 120 : 130}>
                <FormControl>
                  <DatePicker
                    autoOk
                    variant="inline"
                    disableToolbar={true}
                    value={selectedDate}
                    format="MM/DD/YY"
                    onChange={dateChange}
                    maxDate={new Date()}
                    className={
                      type == 'opcodes' ? 'fixedRates' : 'fixedRatesPaytypes'
                    }
                  />
                </FormControl>
              </TableCell>
            </TableRow>
          </Table>
        </TableContainer>
        {error && (
          <p className="error" style={{ fontFamily: 'Roboto' }}>
            {error}
          </p>
        )}
        {errorChecked && <p className="errorChk">{errorChecked}</p>}
      </DialogContent>
      <DialogActions>
        <Button
          variant="contained"
          className={clsx('reset-btn-fixed-rate')}
          onClick={handleOk}
          color="primary"
        >
          Cancel
        </Button>

        <Button
          variant="contained"
          className={clsx('reset-btn-fixed-rate')}
          onClick={handleSave}
          color="primary"
          disabled={error ? true : false}
        >
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function extractValues(mappings) {
  return Object.keys(mappings);
}
const DayTypeMapping = {
  daily: 'Daily',
  weekly: 'Weekly',
  biweekly: 'Biweekly',
  monthly: 'Monthly'
};

const DayType = extractValues(DayTypeMapping);
const lookupValue = (mappings, key) => {
  return mappings[key];
};

export default FixedRateDialog;
