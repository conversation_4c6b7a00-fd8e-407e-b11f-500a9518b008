import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import {
  Grid,
  Typography,
  LinearProgress,
  Box,
  Paper,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Toolbar,
  Button,
  Tooltip,
  Snackbar
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import $ from 'jquery';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import moment from 'moment';
import React from 'react';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import Link from '@material-ui/core/Link';
import {
  getFixedRateDetails,
  getOpcodeDetails
} from 'src/utils/hasuraServices';
import RestoreIcon from '@material-ui/icons/Restore';
import clsx from 'clsx';
import { withKeycloak } from '@react-keycloak/web';
import { withStyles } from '@material-ui/styles';
import Page from 'src/components/Page';

import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import { getVerificationDashboardBaseURL } from 'src/utils/Utils';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import { addDays } from 'date-fns';
import { ReactSession } from 'react-client-session';
import ExportIcon from '@material-ui/icons/GetApp';
import FixedRateRenderer from './FixedRateRenderer';
import { GET_FIXED_RATE_DETAILS, CLIENT_AUDITS } from 'src/graphql/queries';
import makeApolloClientPostgresWrite from 'src/utils/apolloRootClientPostgresWrite';
import LaborPartsGrid from './LaborandPartsGrid';
import AddIcon from '@material-ui/icons/Add';
import FixedRateDialog from './FixedRateDialog';
import { traceSpan } from 'src/utils/OTTTracing';

import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';

var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');

class FixedRateGrid extends React.Component {
  componentWillMount() {
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) {
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     this.getAgGridData( this.state.value ? this.state.value : new Date());
    //     this.setState({ value: this.state.value ? this.state.value : new Date() });
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ storeId: localStorage.getItem('selectedStoreId') });

        this.getAgGridData(
          this.state.value ? this.state.value : new Date(),
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.resetReportGrid();
        this.setState({
          value: this.state.value ? this.state.value : new Date()
        });
      }
    }
  }
  constructor(props) {
    super(props);
    const dateFormat = item => moment(item).format('YYYY-MM');
    //  var initialQueryMonth = this.props.months[0].monthYear;
    const startEdit = this;
    // var initialServiceAdvisor = this.props.serviceAdvisor.const startEdit = this;it('[')[1]
    //   ? this.props.serviceAdvisor.split('[')[1].split(']')[0]
    //   : this.props.serviceAdvisor;
    var location =
      getVerificationDashboardBaseURL() + '/FOC3_Searchbyro/ag-grid.html';
    var tabSelection =
      this.props.history &&
      this.props.history.location.state &&
      this.props.history.location.state.tabSelection
        ? this.props.history.location.state.tabSelection
        : 'one';
    let showAllJobs =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : false;
    let storeId =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.storeId
        ? this.props.history.location.state.storeId
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    this.state = {
      // previousLocation: this.props.history.location.prevPath ? this.props.history.location.prevPath :
      //   (this.props.history.location.state ? this.props.history.location.state.prevPath :  '/Discounts'),

      selectedServiceAdvisor: this.props.serviceAdvisor,
      serviceAdvisor: ['All'],
      isLoading: true,
      isRefresh: false,
      rawGridApi: {},
      gridApi: {},
      tabSelection: tabSelection,
      headerHeight: 45,
      overallbrcostmaxValue: '',
      overallbrsalemaxValue: '',
      overallrocountmaxValue: '',
      cpdiscountedrocountmaxValue: '',
      discountedlbrsalemaxValue: '',
      discountedvolumemaxValue: '',
      discountjobcountmaxValue: '',
      GppBeforeDiscount: '',
      GppAfterDiscount: '',
      monthYear: this.props.months,
      startDate: '',
      endDate: '',
      oldRowArray: [],
      newRowArray: [],
      oldCodeArray: [],
      prevCodeArray: [],
      newCodeArray: [],
      fixedRateOld: '',
      fixedRateNew: '',
      fixedDateOld: '',
      fixedDateNew: '',
      editedRowId: null,
      resetState: false,
      value: new Date(),
      openSnackbar: false,
      openDialogue: false,
      rowDataOpcodes: [],
      addSuccess: false,
      open: false,
      deleteClicked: false,
      showAllJobs: showAllJobs,
      storeId: storeId,
      rowDataParts: [],
      columnDefs: [
        {
          headerName: 'Opcode',
          chartDataType: 'series',
          field: 'opcode',
          width: 115,
          minWidth: 115,
          flex: 1,
          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,

          //valueFormatter: this.formatCellValueDate,
          editable: false,
          // aggFunc: this.nulValue,
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // rowGroup: true
        },
        {
          headerName: 'Pay Type',
          chartDataType: 'series',
          width: 110,
          minWidth: 110,
          flex: 1,
          field: 'paytype',
          suppressMenu: true,
          hide: true,
          editable: false,
          unSortIcon: true,

          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              // left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Fixed Rate($)',
          chartDataType: 'series',
          width: 110,
          minWidth: 110,
          flex: 1,
          field: 'fixedRateValue',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          sortable: true,
          comparator: (valueA, valueB, nodeA, nodeB, isDescending) => {
            if (Number(valueA) === Number(valueB)) return 0;
            return Number(valueA) > Number(valueB) ? 1 : -1;
          },
          cellRenderer: 'fixedRateRenderer',
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              // left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Install Date',
          chartDataType: 'series',
          width: 120,
          minWidth: 120,
          flex: 1,
          field: 'fixedRateDate',
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          //cellRenderer: 'fixedRateRenderer',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          cellStyle: function() {
            return {
              color: '#000000',
              // fontWeight: 'bold',
              // left: '0px',
              // cursor: 'pointer',
              border: ' 0px white',
              textAlign: 'left'
            };
          }
          // hide: true
        },
        {
          headerName: 'Period',
          chartDataType: 'series',
          field: 'fixrateOrder',
          valueFormatter: this.formatCellValuePeriod,
          width: 90,
          minWidth: 90,
          flex: 1,
          suppressMenu: true,

          unSortIcon: true,
          editable: false,
          filter: 'agSetColumnFilter',
          filterParams: {
            valueFormatter: params => {
              return params.value == 1
                ? 'Current'
                : 'Prior' + Number(params.value - 1);
            }
          }
        },
        {
          headerName: 'Action',
          cellRenderer: 'buttonRenderer',
          filter: false,
          width: 100,
          minWidth: 100,
          flex: 1,
          sortable: false,
          editable: false,

          suppressMenu: true,
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          editable: false,
          //hide: true,
          // hide:
          //   typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.includes('client') == true
          //     ? true
          //     : false,
          // hide: true,
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            const index = params.rowIndex;
            const eDiv = document.createElement('div');

            eDiv.innerHTML =
              `<button   title="Edit" id="btneditopfixedrate${index}" style="background: #384163; color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-opfixedrate"><i class="fas fa-pencil-alt"></i></button>&nbsp; ` +
              `<button  title="Cancel" id="btncancelopfixedrate${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-opfixedrate" ><i class="fas fa-ban"></i></button>&nbsp;` +
              `<button title="Save" id="btnupdateopfixedrate${index}" style="background: #384163;  color: #fff;display:none; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-opfixedrate" ><i class="fas fa-save"></i></button>` +
              `<button title="Delete" id="btndeleteopfixedrate${index}" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button-opfixedrate" ><i class="fas fa-trash-alt"></i></button>`;
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button-opfixedrate').attr('disabled', 'disabled');
                $('.edit-button-opfixedrate').css('background', '#38416373');
                $('.edit-button-opfixedrate').css('cursor', 'default');
              });
            }
            if (index !== undefined) {
              // const eButton = eDiv.querySelectorAll('.edit-button')[0];
              // const uButton = eDiv.querySelectorAll('.update-button')[0];
              // const cButton = eDiv.querySelectorAll('.cancel-button')[0];
              // const dButton = eDiv.querySelectorAll('.delete-button')[0];
              const eButton = eDiv.querySelectorAll(
                '.edit-button-opfixedrate'
              )[0];
              const uButton = eDiv.querySelectorAll(
                '.update-button-opfixedrate'
              )[0];
              const cButton = eDiv.querySelectorAll(
                '.cancel-button-opfixedrate'
              )[0];
              const dButton = eDiv.querySelectorAll(
                '.delete-button-opfixedrate'
              )[0];
              eButton.addEventListener('click', () => {
                // params.columnApi.getDisplayedCenterColumns()[0].colId
                $(`.cancel-button-opfixedrate`).hide();
                $(`.update-button-opfixedrate`).hide();

                $(`.edit-button-opfixedrate`).show();
                $('.fixedRateValue').hide();
                $('.fixedRateValueText').show();
                localStorage.setItem('oldId', params.data.id);
                localStorage.setItem('oldPayType', params.data.payType);
                localStorage.setItem('oldPayTypeCode', params.data.payTypeCode);
                localStorage.setItem('oldDepartment', params.data.department);

                var oldRow = [];
                oldRow.push(params.data.id);
                oldRow.push(params.data.fixedRateValue);
                oldRow.push(params.data.payType);
                oldRow.push(params.data.opcode);
                oldRow.push(params.data.fixedRateDate);
                oldRow.push(params.data.fixrateOrder);
                JSON.stringify(oldRow);

                localStorage.setItem('oldRow', oldRow);
                startEdit.setState({
                  editedRowId: index
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowPrev = {
                  // id: params.data.id,
                  opCode: params.data.opcode,
                  paytype: params.data.paytype,
                  fixedRateValue: params.data.fixedRateValue,
                  fixedRateDate: params.data.fixedRateDate,

                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                };
                var rowPrevArray = startEdit.state.prevCodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ opCode, paytype }) =>
                    opCode === rowPrev.opCode && paytype === rowPrev.paytype
                );

                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }

                startEdit.setState({
                  prevCodeArray: rowPrevArray
                });
                startEdit.setState({
                  fixedRateNew: params.data.fixedRateValue
                });
                var prevRow = [];
                prevRow.push({
                  opCode: params.data.opcode,
                  paytype: params.data.payType,
                  fixedRateValue: params.data.fixedRateValue,
                  fixedRateDate: params.data.fixedRateDate,

                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                });
                startEdit.setState({
                  oldCodeArray: prevRow
                });
                console.log(
                  'ccc===',
                  params.data,
                  startEdit.state.prevCodeArray,
                  startEdit.state.oldCodeArray
                );
                //startEdit.refreshDisable();
                startEdit.onBtStartEditing(index);

                $(`#btneditopfixedrate${index}`).hide();
                $(`#btncancelopfixedrate${index}`).show();
                $(`#btnupdateopfixedrate${index}`).show();
                $(`#btndeleteopfixedrate${index}`).hide();
              });
              uButton.addEventListener('click', () => {
                console.log('ffff===', startEdit.state.fixedRateNew);
                if (
                  startEdit.state.fixedRateNew != 0 &&
                  startEdit.state.fixedRateNew != '' &&
                  startEdit.state.fixedRateNew != '0'
                ) {
                  startEdit.onBtStopEditing(index);
                  startEdit.setState({
                    editedRowId: null
                  });

                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.setDataValue(
                    'fixedRateValue',
                    startEdit.state.fixedRateNew
                  );
                  // rowNode.setDataValue(
                  //   'fixedRateDate',
                  //   startEdit.state.fixedDateNew
                  // );
                  const storeId = JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0];
                  startEdit.setState({
                    isCodeEdited: false
                  });
                  startEdit.setState({
                    prevIndex: ''
                  });
                  var newRow = [];
                  newRow.push(
                    params.data.paytype,
                    params.data.opcode,
                    params.data.fixedRateValue,
                    params.data.fixedRateDate,
                    storeId
                  );

                  var updatedRow = {
                    paytype: params.data.paytype,
                    opcode: params.data.opcode,
                    fixedRateValue: params.data.fixedRateValue,
                    fixedRateDate: params.data.fixedRateDate,
                    gridType: params.context.gridType
                  };

                  startEdit.setState({
                    newCodeArray: updatedRow
                  });
                  if (startEdit.state.newCodeArray.length > 0) {
                    startEdit.setState({
                      isCodeRowUpdated: true
                    });
                  }
                  startEdit.setState({
                    laborfixedRateError: null
                  });
                  localStorage.setItem('newRow', newRow);
                  startEdit.updateFixedRateDetails('update');
                  startEdit.gridApi.redrawRows();
                  $(`#btneditopfixedrate${index}`).show();
                  $(`#btncancelopfixedrate${index}`).hide();
                  $(`#btnupdateopfixedrate${index}`).hide();
                  $(`#btndeleteopfixedrate${index}`).show();
                } else {
                  startEdit.setState({
                    laborfixedRateError: index
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.setDataValue('fixedRateValue', null);
                  params.api.refreshCells({
                    columns: ['fixedRateValue'],
                    rowNodes: [params.node],
                    force: true
                  });
                }
              });
              cButton.addEventListener('click', () => {
                //startEdit.getAgGridData();
                startEdit.onBtStopEditing(index);

                startEdit.setState({
                  editedRowId: null
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return (
                    o.opCode == params.data.opcode &&
                    o.paytype == params.data.paytype
                  );
                });

                if (valArr.length > 0) {
                  rowNode.setDataValue(
                    'fixedRateValue',
                    startEdit.state.fixedRateNew != ''
                      ? startEdit.state.fixedRateNew
                      : valArr[0].fixedRateValue
                  );
                  // rowNode.setDataValue(
                  //   'fixedRateDate',
                  //   valArr[0].fixedRateDate
                  // );
                }
                console.log(
                  'ffff===',
                  startEdit.state.fixedRateNew,
                  startEdit.state.prevCodeArray,
                  valArr,
                  rowNode
                );
                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                }

                $(`#btneditopfixedrate${index}`).show();
                $(`#btncancelopfixedrate${index}`).hide();
                $(`#btnupdateopfixedrate${index}`).hide();
                $(`#btndeleteopfixedrate${index}`).show();
                $('.fixedRateValue').hide();
                $('.fixedRateValueText').show();
              });

              dButton.addEventListener('click', function() {
                console.log('ddddddddddddddddd', params);
                var fixedRateValue = params.data.fixedRateValue;
                var updatedRow = {
                  paytype: params.data.paytype,
                  opcode: params.data.opcode,
                  fixedRateValue: params.data.fixedRateValue,
                  fixedRateDate: params.data.fixedRateDate,
                  gridType: params.context.gridType
                };

                startEdit.setState({
                  newCodeArray: updatedRow
                });
                if (fixedRateValue != '') {
                  startEdit.setState({ deleteClicked: true });
                  startEdit.setState({ open: true });
                }
                $(`#btnedit${index}`).hide();
                $(`#btncancel${index}`).hide();
                $(`#btnupdate${index}`).hide();
                $(`#btndelete${index}`).show();
              });
            }
            return eDiv;
          }
        }
      ],
      groupIncludeTotalFooter: true,
      context: { componentParent: this },
      frameworkComponents: {
        fixedRateRenderer: FixedRateRenderer
        // fixedRatePickerRenderer: FixedRatePickerRenderer
      },
      //autoGroupColumnDef: { minWidth: 200 },
      sideBar: {
        toolPanels: [
          {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
            toolPanelParams: {
              suppressRowGroups: true,
              suppressValues: true,
              suppressPivots: true,
              suppressPivotMode: true
              // suppressColumnFilter: true,
              // suppressColumnSelectAll: true,
              // suppressColumnExpandAll: true,
            }
          },
          {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          }
        ],
        defaultToolPanel: 'columns'
      },
      pivotMode: true,

      rowData: [],
      overlayNoRowsTemplate:
        '<span style="padding: 10px; margin-top:50px;">No rows to show</span>',
      defaultColDef: {
        enableValue: true,
        // enableRowGroup: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
        // editable: true
      }
    };
  }

  insertFixedRateDetails = data => {
    if (data[0] != '' && data[2] != '') {
      var updatedRow = {
        paytype: data[1],
        opcode: data[0],
        fixedRateValue: data[2],
        fixedRateDate: data[3]
      };
      this.setState({
        newCodeArray: updatedRow
      });
      this.updateFixedRateDetails('insert', updatedRow);
    }
  };

  updateFixedRateDetails = (type, data) => {
    console.log('sssssssssssssss', data);
    const client = makeApolloClientPostgresWrite;
    this.setState({
      isCodeRowUpdated: false
    });
    let oldPayArr = this.state.prevCodeArray;

    let newPayArr = this.state.newCodeArray.length;

    const userID = localStorage.getItem('userID');
    const start = new Date();
    client
      .mutate({
        mutation: GET_FIXED_RATE_DETAILS,
        variables: {
          pAction: type,
          pOpcode: data ? data.opcode : this.state.newCodeArray.opcode,
          pPaytype: data ? data.paytype : this.state.newCodeArray.paytype,
          fixedratevalue: data
            ? data.fixedRateValue
            : this.state.newCodeArray.fixedRateValue,
          fixedratedate: data
            ? data.fixedRateDate
            : this.state.newCodeArray.fixedRateDate,
          userid: localStorage.getItem('userID'),
          pLaborOrParts: this.state.newCodeArray.gridType,
          storeId: this.state.storeId
        }
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/FixedRates',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_FIXED_RATE_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        if (
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
            .statelessCcPhysicalRwFixedRateMasters &&
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
            .statelessCcPhysicalRwFixedRateMasters[0].opcode == 'Success'
        ) {
          this.setState({ openSnackbar: true });
          this.getAgGridData();
          this.setState({ openDialogue: false });
          this.setState({ addSuccess: true });

          this.setState({ deleteClickedLabor: false });
          this.setState({ deleteClickedParts: false });
          this.setState({
            openStatusMsgErr: '',
            openStatusMsg:
              type == 'insert'
                ? 'Fixed Rate Details Added!'
                : 'Fixed Rate Details Updated!'
          });
          client
            .mutate({
              mutation: CLIENT_AUDITS,
              variables: {
                dmlaction: 'Update paytype master',
                newdata: JSON.stringify(newPayArr),
                olddata: JSON.stringify(oldPayArr),
                schemaname: 'stateless_cc_physical_rw',
                storeId: localStorage.getItem('selectedStoreId'),
                tablename: 'pay_type_master',
                username: localStorage.getItem('userID'),
                userrole: this.props.session.user.role
              }
            })
            .then(result => {
              // console.log("data result=",result);
            });
        } else {
          if (
            result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
              .statelessCcPhysicalRwFixedRateMasters
          ) {
            this.setState({ openSnackbar: true });
            this.setState({
              openStatusMsg: '',
              openStatusMsgErr:
                result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
                  .statelessCcPhysicalRwFixedRateMasters[0].opcode
            });
          } else {
          }
        }
      });
  };

  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
  };

  cellStyles = () => {
    return {
      textAlign: 'right',
      border: ' 0px white'
    };
  };
  formatCellValuePeriod = params => {
    if (params.value && params.value != '') {
      return params.value == 1
        ? 'Current'
        : 'Prior' + (Number(params.value) - 1);
    } else {
      return '';
    }
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != 0) {
      return moment(new Date(params.value)).format('MM/DD/YY');
    }
  };
  formatCellValueMonthYear = params => {
    if (params.value != null && params.value != 0) {
      return moment(new Date(params.value)).format('MM/YY');
    }
  };
  formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        (
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString()
      );
    }
  };
  formatCellValueDiscount = params => {
    if (params.value != null && params.value != 0) {
      return (
        '($' +
        Math.abs(
          Math.round((Number(params.value) + Number.EPSILON) * 100) / 100
        ).toLocaleString() +
        ')'
      );
    }
  };
  formatCellValueWithOut$ = params => {
    if (params.value != null && params.value != 0) {
      return parseFloat(params.value)
        .toFixed(2)
        .toString()
        .replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    } else {
      return '0.00';
    }
  };

  onBtStartEditing = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = false;
      groupColumn[3]['editable'] = false;
      groupColumn[4]['editable'] = false;
    }
    this.state.rawGridApi.setColumnDefs(groupColumn);

    this.state.rawGridApi.columnController.columnDefs[1].editable = true;
    const { rawGridApi } = this.state;
    this.state.rawGridApi.setFocusedCell(
      index,
      'fixedRateValue',

      pinned
    );
    this.state.rawGridApi.startEditingCell({
      rowIndex: index,
      colKey: 'fixedRateValue',

      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onBtStopEditing = () => {
    this.gridApi.stopEditing();
    const groupColumn = this.state.rawGridApi.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;

    this.state.rawGridApi.setColumnDefs(groupColumn);
  };

  // onGridReady = params => {
  //   // params.api.closeToolPanel();
  //   this.gridApi = params.api;
  //   this.gridColumnApi = params.columnApi;

  //   this.setState({ rawGridApi: params.api });
  //   this.setState({ gridApi: params });
  //   this.setState({ gridcolumnApi: params.columnApi });

  //   // this.gridApi.sizeColumnsToFit();
  //   this.getAgGridData(this.state.value);
  // };

  onGridReadyLabor = params => {
    this.setState({ rawGridApiLabor: params.api });
    this.setState({ gridApiLabor: params });
    this.setState({ gridcolumnApi: params.columnApi });
    this.gridApiLabor = params.api;
    this.getAgGridData(this.state.value);
  };
  onGridReadyParts = params => {
    this.setState({ rawGridApiParts: params.api });
    this.setState({ gridApiParts: params });
    this.setState({ gridcolumnApiParts: params.columnApi });
    this.gridApiParts = params.api;
    this.getAgGridData(this.state.value);
  };

  getAgGridData(statusDate, storeId) {
    this.setState({ isLoading: true });
    this.getAllCategorizedOpcodes();

    getFixedRateDetails(
      'get',
      'labor',
      storeId ? storeId : this.state.storeId,
      result => {
        this.setState({ isLoading: false });

        if (
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
            .statelessCcPhysicalRwFixedRateMasters
        ) {
          var resultArr =
            result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
              .statelessCcPhysicalRwFixedRateMasters;
          this.setState({
            rowData: resultArr
          });
          if (
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.fixedRate
          ) {
            this.filterByFixedRate(
              'labor',
              this.props.history.location.state.fixedRate,
              this.props.history.location.state.opcode
            );
          }
          // var startDate = moment(new Date(queryMonth + '-01')).format(
          //   'YYYY-MM-DD'
          // );
          // var endDate = moment(new Date(queryMonth + '-01'))
          //   .clone()
          //   .endOf('month')
          //   .format('YYYY-MM-DD');
          // this.setState({
          //   value: [startDate, endDate]
          // });
        }
      }
    );
    getFixedRateDetails(
      'get',
      'parts',
      storeId ? storeId : this.state.storeId,
      result => {
        this.setState({ isLoading: false });

        if (
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
            .statelessCcPhysicalRwFixedRateMasters
        ) {
          var resultArr =
            result.data.statelessCcPhysicalRwGetOrSetFixedRateMaster
              .statelessCcPhysicalRwFixedRateMasters;
          this.setState({
            rowDataParts: resultArr
          });
          if (
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.fixedRate
          ) {
            this.filterByFixedRate(
              'parts',
              this.props.history.location.state.fixedRate,
              this.props.history.location.state.opcode
            );
          }
          // var startDate = moment(new Date(queryMonth + '-01')).format(
          //   'YYYY-MM-DD'
          // );
          // var endDate = moment(new Date(queryMonth + '-01'))
          //   .clone()
          //   .endOf('month')
          //   .format('YYYY-MM-DD');
          // this.setState({
          //   value: [startDate, endDate]
          // });
        }
      }
    );
  }
  // filterByFixedRate = (fixedRate, opcode) => {
  //   if (fixedRate && opcode) {
  //     var fixedRateFilterComponent = this.state.rawGridApi.getFilterInstance(
  //       'fixedRateValue'
  //     );
  //     fixedRateFilterComponent.setModel({ values: [fixedRate] });
  //     var opcodeFilterComponent = this.state.rawGridApi.getFilterInstance(
  //       'opcode'
  //     );
  //     opcodeFilterComponent.setModel({ values: [opcode] });
  //     this.gridApi.onFilterChanged();
  //   }
  // };

  filterByFixedRate = (type, fixedRate, opcode) => {
    if (type == 'labor') {
      if (fixedRate && opcode) {
        var fixedRateFilterComponentLabor = this.state.rawGridApiLabor.getFilterInstance(
          'fixedRateValue'
        );
        fixedRateFilterComponentLabor.setModel({ values: [fixedRate] });
        var paytypeFilterComponentLabor = this.state.rawGridApiLabor.getFilterInstance(
          'opcode'
        );
        paytypeFilterComponentLabor.setModel({ values: [opcode] });
        this.gridApiLabor.onFilterChanged();
      }
    } else {
      if (fixedRate && opcode) {
        var fixedRateFilterComponentParts = this.state.rawGridApiParts.getFilterInstance(
          'fixedRateDate'
        );
        fixedRateFilterComponentParts.setModel({ values: [fixedRate] });
        var paytypeFilterComponentParts = this.state.rawGridApiParts.getFilterInstance(
          'opcode'
        );
        paytypeFilterComponentParts.setModel({ values: [opcode] });
        this.gridApiParts.onFilterChanged();
      }
    }
  };
  getAllCategorizedOpcodes = () => {
    getOpcodeDetails(result => {
      if (result.data.statelessCcPhysicalRwGetRoOpcodesDetails.nodes) {
        let resultArr =
          result.data.statelessCcPhysicalRwGetRoOpcodesDetails.nodes;
        let opcodes = lodash.filter(resultArr, item => {
          return (
            item.categorized == 'Categorized' && item.paytype == 'Customer'
          );
        });
        if (opcodes.length > 0) {
          opcodes = lodash.uniqBy(opcodes, obj => obj.opcode);
          opcodes = lodash.sortBy(opcodes, 'opcode');
        }
        this.setState({
          rowDataOpcodes: opcodes
        });
      }
    });
  };
  nulValue = () => {
    return '';
  };
  renderBackButton = () => {
    {
      let chartId = this.props.chartId
        ? this.props.chartId
        : this.props.history.location.search.split('=')[1];
      let data = {
        month_year: this.state.queryMonth,
        type: '',
        //chartId: this.props.chartId ? this.props.chartId : this.props.history.location.search.split('=')[1],
        chartId: this.props.chartId,
        history: this.props.history,
        prevPath: this.state.previousLocation,
        drillDown:
          chartId == 1111
            ? 41
            : chartId == 1115
            ? 42
            : chartId == 1232
            ? 45
            : chartId == 1165
            ? 44
            : 43
      };
      this.props.parentCallback(data);
    }
  };
  externalFilterChanged = () => {
    //ageType = 'test';

    this.getAgGridData(this.state.value);
    this.state.rawGridApi.onFilterChanged();
  };
  isExternalFilterPresent = () => {
    return this.state.value;
  };

  doesExternalFilterPass = node => {
    if (node.alreadyRendered == false) {
      if (this.state.value != '') {
        let filteredUsers = this.state.rowData.filter(
          user =>
            moment(this.state.value).format('YYYY/MM/DD') <= user.logindate
        );
        this.setState({
          rowData: filteredUsers
        });
        return (
          node.data.closeddate >= moment(this.state.value).format('YYYY/MM/DD')
        );
      }
    }
  };
  onRowEditingStarted(params) {
    params.api.refreshCells({
      columns: ['fixedRateValue'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStopped(params) {
    params.api.refreshCells({
      columns: ['fixedRateValue'],
      rowNodes: [params.node],
      force: true
    });
  }
  onFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      fixedRateOld: oldVal
    });

    this.setState({
      fixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  onFixedDateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      fixedDateOld: oldVal
    });

    this.setState({
      fixedDateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  dateChanage = e => {
    this.setState({ value: e });
    this.getAgGridData(e);
    this.state.rawGridApi.onFilterChanged();
  };
  resetReportGrid = () => {
    // if (this.state.tabSelection == 'one') {
    //   this.gridApi.setSortModel(null);
    //   this.gridApi.setFilterModel(null);
    //   this.setState({
    //     editedRowId: null
    //   });

    //   this.setState({
    //     isCodeEdited: false
    //   });
    //   this.gridApi.redrawRows();
    //   this.state.gridcolumnApi.resetColumnState();
    // } else {
    //   this.setState({
    //     resetState: true
    //   });
    //   this.state.gridcolumnApi.resetColumnState();
    // }
    if (this.state.tabSelection == 'one') {
      this.gridApiLabor && this.gridApiLabor.setSortModel(null);
      this.gridApiLabor && this.gridApiLabor.setFilterModel(null);
      this.setState({
        editedRowId: null
      });

      this.setState({
        isCodeEdited: false
      });
      this.gridApiLabor && this.gridApiLabor.redrawRows();

      this.gridApiParts && this.gridApiParts.setSortModel(null);
      this.gridApiParts && this.gridApiParts.setFilterModel(null);
      this.gridApiParts && this.gridApiParts.redrawRows();
      this.state.gridcolumnApi && this.state.gridcolumnApi.resetColumnState();
      this.state.gridcolumnApiParts &&
        this.state.gridcolumnApiParts.resetColumnState();
    } else {
      this.setState({
        resetState: true
      });
    }
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Report',
      fileName: 'DailyDataImports',
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: { type: 'String', value: 'Daily Data Imports' },
            mergeAcross: 3
          }
        ]
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };
  handleTabChange = (event, newValue) => {
    this.setState({ tabSelection: newValue });
    this.resetReportGrid();
  };
  handleResetGrid = (event, newValue) => {
    this.setState({ resetState: false });
  };
  handleBackButton = params => {
    this.props.history.push({
      pathname:
        this.props.history &&
        this.props.history.location.state &&
        this.props.history.location.state.pageType == 'LaborMisses'
          ? localStorage.getItem('versionFlag') == 'TRUE'
            ? '/LaborMisses'
            : 'LaborGridMisses'
          : localStorage.getItem('versionFlag') == 'TRUE'
          ? '/PartsMisses'
          : 'PartsTargetMisses',
      state: {
        selectedFilter: this.props.history.location.state.selectedFilter,
        selectedToggle: this.props.history.location.state.selectedToggle,
        selectedMonthYear: this.props.history.location.state.selectedMonthYear,
        parent: this.props.history.location.state.parent,
        previousToggle: this.props.history.location.state.previousToggle,
        payType: this.props.history.location.state.payType,
        gridType: this.props.history.location.state.gridType,
        previousPayType: this.props.history.location.state.PrevPayType,
        previousGridType: this.props.history.location.state.PrevGridType,
        showAllJobs: this.state.showAllJobs,
        filterStart: this.props.history.location.state.filterStart,
        filterEnd: this.props.history.location.state.filterEnd,
        selectedGridType: this.props.history.location.state.selectedGridType
      }
    });
  };
  handleSaveEmail = () => {
    this.setState({ openDialogue: true });
  };

  handleClose = () => {
    this.setState({ openDialogue: false });
  };

  handleCloseDelete = () => {
    this.setState({ open: false });
  };
  handleOk = () => {
    this.setState({ open: false });
    this.updateFixedRateDetails('delete');
  };
  render() {
    const { classes } = this.props;
    return (
      <div>
        <Page title={'Fixed Rate History'}></Page>
        <Paper
          square
          style={{
            margin: 8,

            backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
            border:
              Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
            color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b',
            cursor: 'default'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            //  onChange={this.handleTabChange}
            variant="fullWidth"
            TabIndicatorProps={{
              style: {
                backgroundColor: '#e7eef3'
              }
            }}
            id={'lineTab'}
            textColor="secondary"
            showrefresh
            aria-label="icon label tabs example"
            style={{ cursor: 'default' }}
          >
            {this.props.history &&
              this.props.history.location.state &&
              (this.props.history.location.state.pageType == 'LaborMisses' ||
                this.props.history.location.state.pageType ==
                  'PartsMisses') && (
                <Button
                  variant="contained"
                  className={'bck-btn'}
                  onClick={this.handleBackButton}
                  style={{ width: 'auto', top: '9px' }}
                  fullWidth={false}
                >
                  <Typography variant="body1" align="left">
                    Back
                  </Typography>
                </Button>
              )}

            <Tab
              label={
                <div style={{ color: '#084588' }}> Fixed Rate History</div>
              }
              value="one"
              style={{
                textTransform: 'none',
                pointerEvents: 'none',
                borderColor: '#e7eef3'
              }}
            />

            <Button
              variant="contained"
              fullWidth={false}
              id="reset-layout"
              className={clsx(classes.back, 'reset-btn')}
              onClick={this.resetReportGrid}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button>
          </Tabs>
        </Paper>
        {this.state.tabSelection == 'one' ? (
          <Typography
            style={{
              paddingLeft: '10px',
              display: this.state.isLoading == true ? 'none' : 'block',
              fontWeight: 'bold',
              color: '#003d6b',
              fontSize: 13
            }}
          >
            This page displays the history of fixed rates applied to opcodes. If
            you do not find an opcode having a current fixed rate, this
            indicates that the fixed rate has been recently disabled for the
            opcode.
          </Typography>
        ) : (
          <Typography
            style={{
              paddingLeft: '10px',
              display: this.state.isLoading == true ? 'none' : 'block',
              fontWeight: 'bold',
              color: '#003d6b',
              fontSize: 13
            }}
          >
            This page displays the history of fixed rates applied to pay types.
            If you do not find a pay type having a current fixed rate, this
            indicates that the fixed rate has been recently disabled for the pay
            type.
          </Typography>
        )}

        <Paper
          square
          style={{
            margin: 8,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Tabs
            value={this.state.tabSelection}
            onChange={this.handleTabChange}
            variant=""
            indicatorColor="secondary"
            textColor="secondary"
            aria-label="icon label tabs example"
          >
            <Tab
              className={
                this.state.tabSelection == 'one'
                  ? classes.tabSelected
                  : classes.tabButtons
              }
              label={<div>Opcode</div>}
              value="one"
            />
            <Tab
              className={
                this.state.tabSelection == 'two'
                  ? classes.tabSelected
                  : classes.tabButtons
              }
              label={<div>Pay Type</div>}
              value="two"
            />
            {/* <Tooltip title="Export To Excel">
              <Link
                id="export-to-excel"
                style={{
                  marginRight: 30,
                  float: 'right',
                  marginTop: 31,
                  cursor: 'pointer'
                }}
                onClick={this.onBtExport}
              >
                <ExportIcon />
              </Link>
            </Tooltip>*/}
          </Tabs>
        </Paper>

        {this.state.isLoading && (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        )}

        <span
          style={{
            display: 'flex'
          }}
        >
          <Grid xs={12} className={classes.textContainerGrid}>
            {this.props &&
            this.props.history &&
            this.props.history.location &&
            this.props.history.location.state &&
            this.props.history.location.state.pageType == 'PartsMisses' ? (
              <div
                // id="data-tab-user-login"
                id="data-tab"
                className="ag-theme-balham"
                style={{
                  height: window.innerHeight - 280 + 'px',

                  margin: 8,
                  width: 540,
                  //this.state.isLoading == true ||
                  display: this.state.tabSelection == 'two' ? 'none' : 'block'
                }}
              >
                <Typography
                  variant="h5"
                  color="primary"
                  className={clsx(this.props.mainLabel, classes.subTitle)}
                >
                  Parts
                </Typography>
                <AgGridReact
                  className="ag-theme-balham"
                  style={{
                    height: '500px',
                    width: '100%'
                  }}
                  floatingFilter={true}
                  defaultColDef={this.state.defaultColDef}
                  headerHeight={this.state.headerHeight}
                  onRowEditingStarted={this.onRowEditingStarted}
                  onRowEditingStopped={this.onRowEditingStopped}
                  frameworkComponents={this.state.frameworkComponents}
                  // sideBar={this.state.sideBar}
                  editType={'fullRow'}
                  context={{
                    ...this.state.context,
                    gridType: 'parts'
                  }}
                  modules={AllModules}
                  columnDefs={this.state.columnDefs}
                  onGridReady={this.onGridReadyParts}
                  rowData={this.state.rowDataParts}
                  tooltipShowDelay={0}
                  enableRangeSelection={true}
                  animateRows={true}
                  enableCharts={true}
                  suppressRowClickSelection={true}
                  overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
                  suppressDragLeaveHidesColumns={true}
                  suppressContextMenu={true}
                />
              </div>
            ) : this.props &&
              this.props.history &&
              this.props.history.location &&
              this.props.history.location.state &&
              this.props.history.location.state.pageType === 'LaborMisses' ? (
              <div
                id="data-tab"
                className="ag-theme-balham"
                style={{
                  height: window.innerHeight - 280 + 'px',
                  margin: 8,
                  width: 540,
                  display: this.state.tabSelection === 'two' ? 'none' : 'block'
                }}
              >
                <Typography
                  variant="h5"
                  color="primary"
                  className={clsx(this.props.mainLabel, classes.subTitle)}
                >
                  Labor
                </Typography>
                <AgGridReact
                  className="ag-theme-balham"
                  style={{
                    height: '500px',
                    width: '50%'
                  }}
                  floatingFilter={true}
                  defaultColDef={this.state.defaultColDef}
                  headerHeight={this.state.headerHeight}
                  onRowEditingStarted={this.onRowEditingStarted}
                  onRowEditingStopped={this.onRowEditingStopped}
                  frameworkComponents={this.state.frameworkComponents}
                  editType={'fullRow'}
                  context={{
                    ...this.state.context,
                    gridType: 'labor'
                  }}
                  modules={AllModules}
                  columnDefs={this.state.columnDefs}
                  onGridReady={this.onGridReadyLabor}
                  rowData={this.state.rowData}
                  tooltipShowDelay={0}
                  enableRangeSelection={true}
                  animateRows={true}
                  enableCharts={true}
                  suppressRowClickSelection={true}
                  overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
                  suppressDragLeaveHidesColumns={true}
                  suppressContextMenu={true}
                />
              </div>
            ) : (
              <>
                <Grid>
                  <div
                    // id="data-tab-user-login"
                    id="data-tab"
                    className="ag-theme-balham"
                    style={{
                      height: window.innerHeight - 280 + 'px',
                      // height: '100%',
                      // height:
                      // (this.state.rowData.length > 0
                      //   ? window.innerHeight - ((this.state.rowData.length*-20)+540)
                      //   : window.innerHeight - 500) + 'px',
                      //width: '615px',
                      // this.props.keycloak.realmAccess.roles.includes('client') ===
                      // true
                      //   ? '615px'
                      //   : '730px',
                      //width: '730px',
                      margin: 8,
                      width: 540,
                      //this.state.isLoading == true ||
                      display:
                        this.state.tabSelection == 'two' ? 'none' : 'block'
                    }}
                  >
                    <Typography
                      variant="h5"
                      color="primary"
                      className={clsx(this.props.mainLabel, classes.subTitle)}
                    >
                      Labor
                    </Typography>
                    <AgGridReact
                      className="ag-theme-balham"
                      style={{
                        height: '500px',
                        width: '50%'
                      }}
                      // domLayout="autoHeight"
                      // isExternalFilterPresent={this.isExternalFilterPresent}
                      // doesExternalFilterPass={this.doesExternalFilterPass}
                      floatingFilter={true}
                      defaultColDef={this.state.defaultColDef}
                      headerHeight={this.state.headerHeight}
                      onRowEditingStarted={this.onRowEditingStarted}
                      onRowEditingStopped={this.onRowEditingStopped}
                      frameworkComponents={this.state.frameworkComponents}
                      // sideBar={this.state.sideBar}
                      editType={'fullRow'}
                      context={{
                        ...this.state.context,
                        gridType: 'labor'
                      }}
                      modules={AllModules}
                      columnDefs={this.state.columnDefs}
                      onGridReady={this.onGridReadyLabor}
                      rowData={this.state.rowData}
                      tooltipShowDelay={0}
                      enableRangeSelection={true}
                      animateRows={true}
                      enableCharts={true}
                      suppressRowClickSelection={true}
                      overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
                      suppressDragLeaveHidesColumns={true}
                      suppressContextMenu={true}
                    />
                  </div>
                </Grid>
                <Grid>
                  <div
                    // id="data-tab-user-login"
                    id="data-tab"
                    className="ag-theme-balham"
                    style={{
                      height: window.innerHeight - 280 + 'px',

                      margin: 8,
                      width: 540,
                      //this.state.isLoading == true ||
                      display:
                        this.state.tabSelection == 'two' ? 'none' : 'block'
                    }}
                  >
                    <Typography
                      variant="h5"
                      color="primary"
                      className={clsx(this.props.mainLabel, classes.subTitle)}
                    >
                      Parts
                    </Typography>
                    <AgGridReact
                      className="ag-theme-balham"
                      style={{
                        height: '500px',
                        width: '100%'
                      }}
                      floatingFilter={true}
                      defaultColDef={this.state.defaultColDef}
                      headerHeight={this.state.headerHeight}
                      onRowEditingStarted={this.onRowEditingStarted}
                      onRowEditingStopped={this.onRowEditingStopped}
                      frameworkComponents={this.state.frameworkComponents}
                      // sideBar={this.state.sideBar}
                      editType={'fullRow'}
                      context={{
                        ...this.state.context,
                        gridType: 'parts'
                      }}
                      modules={AllModules}
                      columnDefs={this.state.columnDefs}
                      onGridReady={this.onGridReadyParts}
                      rowData={this.state.rowDataParts}
                      tooltipShowDelay={0}
                      enableRangeSelection={true}
                      animateRows={true}
                      enableCharts={true}
                      suppressRowClickSelection={true}
                      overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
                      suppressDragLeaveHidesColumns={true}
                      suppressContextMenu={true}
                    />
                  </div>
                </Grid>
              </>
            )}
          </Grid>
          {this.state.tabSelection == 'one' ? (
            <div style={{ display: 'none' }}>
              {this.state.rowDataOpcodes.length > 0 &&
              typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
              this.props.keycloak.realmAccess.roles.includes('client') !=
                true ? (
                // <Tooltip
                //   title="Add Fixed Rate"
                //   // onClick={this.handleSaveEmail}
                //   placement="top"
                // >
                <Button
                  variant="contained"
                  style={{ display: 'none' }}
                  id={'btnRecipient'}
                  className={clsx('reset-btn', 'btnSaveFixedRate')}
                  onClick={this.handleSaveEmail}
                >
                  Add Fixed Rate
                </Button>
              ) : null}
            </div>
          ) : null}
        </span>
        <Snackbar
          open={this.state.openSnackbar}
          autoHideDuration={4000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 487 }}
        >
          <Alert
            onClose={this.hidesnackbar}
            severity={this.state.openStatusMsg != '' ? 'success' : 'error'}
          >
            {/* Fixed Rate Details Updated! */}
            {this.state.openStatusMsg != ''
              ? this.state.openStatusMsg
              : this.state.openStatusMsgErr}
          </Alert>
        </Snackbar>
        {this.state.deleteClicked == true ? (
          <Dialog
            open={this.state.open}
            onClose={this.handleClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                Are you sure you want to delete?
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.handleCloseDelete}>Cancel</Button>
              <Button onClick={this.handleOk} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
        <FixedRateDialog
          open={this.state.openDialogue}
          handlePopupClose={this.handleClose}
          opcodes={this.state.rowDataOpcodes}
          insertFixedRateDetails={this.insertFixedRateDetails}
          type={'opcodes'}
          addSuccess={this.state.addSuccess}
        ></FixedRateDialog>
        {this.state.tabSelection == 'two' ? (
          <div
            square
            style={{
              margin: 8,
              height: window.innerHeight - 250 + 'px',
              marginLeft: 8,
              display:
                this.state.isLoading == true || this.state.tabSelection == 'one'
                  ? 'none'
                  : 'block',
              paddingLeft: 4,
              paddingRight: 4
            }}
          >
            <LaborPartsGrid
              resetState={this.state.resetState}
              handleResetGrid={this.handleResetGrid}
              session={this.props.session}
              history={this.props.history}
            />
          </div>
        ) : null}
      </div>
    );
  }
}
const SummaryTitle = ({ title, value }) => {
  return (
    <Grid item xs>
      <Typography
        variant="h5"
        style={{ fontSize: '13px', minHeight: '58px' }}
        color="primary"
      >
        {title}:
      </Typography>
      <div>
        <Typography style={{ fontSize: '14px' }} variant="subtitle1">
          {' '}
          {value}
        </Typography>
      </div>
    </Grid>
  );
};
const styles = theme => ({
  loader: {
    width: '100%',
    '& > * + *': {
      marginTop: theme.spacing(2)
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120
  },
  selectEmpty: {
    marginTop: theme.spacing(2)
  },
  root: {
    flexGrow: 1
  },
  tabButtons: {
    textTransform: 'none'
  },
  tabSelected: {
    color: theme.palette.primary.main,
    textTransform: 'none',
    border: 'solid 1px',
    borderColor: theme.palette.primary.main,
    backgroundColor: theme.palette.primary.active,
    '& $icon': {
      color: theme.palette.primary.main
    }
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  monthSelector: {
    border: 'thin solid #968989 !important',
    height: '35px !important'
  },
  reactDaterangePicker: {
    width: '80px !important',
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px'
  },
  back: {
    marginRight: 30,
    float: 'right',
    width: 'auto',
    marginTop: 10
  },
  textContainerGrid: {
    display: 'flex',
    gap: 10
    //paddingLeft: 10
  },
  flexGrid: {
    display: 'flex',
    alignItems: 'center',
    gap: 5
  },
  alertInfo: {
    fontSize: 16,
    fontWeight: '500',
    color: '#242f48',
    width: '100%',
    marginTop: '2%',
    marginBottom: '5%',
    height: '45px !important'
    // '@media (max-width: 2560px)': {
    //   height: '45px !important'
    // },
    // '@media (max-width: 2304px)': {
    //   height: '45px !important'
    // },
    // '@media (max-width: 1920px)': {
    //   height: '50px !important'
    // }
  }
});

export default withStyles(styles)(withKeycloak(FixedRateGrid));
