import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
import {
  Typography,
  LinearProgress,
  Box,
  Snackbar,
  Button
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';

import Autocomplete from '@material-ui/lab/Autocomplete';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import $ from 'jquery';
import React from 'react';
import 'react-grid-layout/css/styles.css';

import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgresWrite';
import {
  getPaytypeFixedRateDetails,
  getPayTypeMasterDetails
} from 'src/utils/hasuraServices';
// import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import { withStyles } from '@material-ui/styles';
import {
  GET_PAYTYPE_FIXED_RATE_DETAILS,
  CLIENT_AUDITS
} from 'src/graphql/queries';
import { processCells } from 'src/components/ViewGraphDetailsAction';
import PaytypeFixedRateRenderer from './PaytypeFixedRateRenderer';
import 'react-datepicker/dist/react-datepicker.css';
import 'react-datepicker/dist/react-datepicker-cssmodules.css';
import moment from 'moment';
import { ReactSession } from 'react-client-session';
import FixedRateDialog from './FixedRateDialog';

import { traceSpan } from 'src/utils/OTTTracing';

import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';

var lodash = require('lodash');

var Dealer = process.env.REACT_APP_DEALER;

class LaborPartsGrid extends React.Component {
  componentWillMount() {
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate(prevProps, prevState) {
    if (
      //prevProps.resetState !== this.props.resetState &&
      this.props.resetState == true
    ) {
      this.resetGrids();
    }
    if (ReactSession.get('selectedStoreId') != undefined) {
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.getAgGridData(
          JSON.parse(localStorage.getItem('selectedStoreId'))[0]
        );
        this.resetGrids();
      }
    }
  }
  constructor(props) {
    super(props);
    let payTypeSelected =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridType
        ? this.props.history.location.state.gridType
        : this.props.selectedPayType;
    let gridDoorRate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.gridDoorRate
        ? this.props.history.location.state.gridDoorRate
        : '';
    let selectedGrid =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.selectedGrid == ''
        ? this.props.history.location.state.selectedGrid
        : 1;
    if (payTypeSelected == 'C') {
      payTypeSelected = 'Customer';
    } else if (payTypeSelected == 'I') {
      payTypeSelected = 'Internal';
    }

    let showAllJobs =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.showAllJobs
        ? this.props.history.location.state.showAllJobs
        : false;
    let storeId =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.storeId
        ? this.props.history.location.state.storeId
        : JSON.parse(localStorage.getItem('selectedStoreId'))[0];
    const startEdit = this;
    this.textInput = React.createRef();
    this.state = {
      rawGridApi: {},
      gridApi: {},
      isLoadingLabor: true,
      isLoadingParts: true,
      selectedGrid: selectedGrid,
      selectedPayType: payTypeSelected,
      payTypes: this.props.payTypes,
      gridPeriods: [],
      gridDoorRate: gridDoorRate,
      addNewRow: true,
      addNewFleet: false,
      isFleetLoaded: false,
      isFleetNamesLoaded: false,
      customerNames: { customerName: 'All' },
      selectedCustomer: '',
      selectedFleet: 'All',
      showAllJobs: showAllJobs,
      gridRateValue: 'fixedRate',
      matrixRateValue: 'partsFixedRate',
      partsSourceArray: [],
      laborGrid: false,
      fixedRate: true,
      doorRate: '',
      fixedDoorRate: '',
      fixedLaborRate: '',
      partsFixedRate: true,
      partsMatrix: false,
      doorRateChanged: false,
      fixedDoorRateChanged: false,
      fixedRateChanged: false,
      markupChanged: false,
      markupValueChanged: false,
      pSourceChanged: false,
      matrixChanged: false,
      markupValue: '',
      partsMarkup: 'Cost',
      partsSource: '',
      gridInstalledDate: new Date(),
      matrixInstalledDate: new Date(),
      selectedFleetError: false,
      setFleetOpen: false,
      fleetNameError: false,
      doorRateError: false,
      fixedLaborRateError: false,
      gridInstalledDateError: false,
      markupError: false,
      partsourceError: false,
      matrixInstalledDateError: false,
      oldRowArray: [],
      newRowArray: [],
      oldCodeArray: [],
      prevCodeArray: [],
      newCodeArray: [],
      laborFixedRateOld: '',
      laborFixedRateNew: '',
      partsFixedRateOld: '',
      partsFixedRateNew: '',
      errorItems: [],
      openSnackbar: false,
      rowDataPayTypes: [],
      openDialogue: false,
      addSuccess: false,
      open: false,
      deleteClickedLabor: false,
      deleteClickedParts: false,
      storeId: storeId,
      columnDefs: [
        // {
        //   headerName: 'Door Rate',
        //   field: 'doorRate',
        //   width: 90,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'right', border: ' 0px white' };
        //   },
        // },
        {
          headerName: 'Pay Type',
          field: 'paytype',
          // tooltipField: 'paytype',
          width: 106,
          minWidth: 106,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Fixed Rate($)',
          field: 'laborFixedratevalue',
          width: 115,
          minWidth: 115,
          flex: 1,
          suppressMenu: true,
          editable: false,
          unSortIcon: true,
          comparator: (valueA, valueB, nodeA, nodeB, isDescending) => {
            if (Number(valueA) === Number(valueB)) return 0;
            return Number(valueA) > Number(valueB) ? 1 : -1;
          },
          cellRenderer: 'fixedRateRenderer',
          valueFormatter: this.formatCellValueRate,
          cellClass: 'twoDecimalPlacesWith$',
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          },
          comparator: function(valueA, valueB) {
            return valueA - valueB;
          }
        },

        {
          headerName: 'Effective From',
          field: 'fixedRateDate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },
        {
          headerName: 'Period',
          field: 'fixrateOrder',
          valueFormatter: this.formatCellValuePeriod,
          width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          filter: 'agSetColumnFilter',
          filterParams: {
            valueFormatter: params => {
              return params.value == 1
                ? 'Current'
                : 'Prior' + Number(params.value - 1);
            }
          },
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Action',
          //cellRenderer: 'buttonRenderer',
          filter: false,
          // tooltip(params) {
          //   return 'Edit';
          // },
          width: 90,
          minWidth: 90,
          flex: 1,
          editable: false,
          sortable: false,

          suppressMenu: true,
          //hide: true,
          // hide:
          //   typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.includes('client') == true
          //     ? true
          //     : false,
          // hide:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !!(
          //     typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
          //     this.props.keycloak.realmAccess.roles.includes('client') === true
          //   ),
          // cellStyle: params => {
          //   return startEdit.state.isReloaded
          //     ? {
          //         'pointer-events': 'none',
          //         opacity: '0.4',
          //         textAlign: 'center'
          //       }
          //     : { textAlign: 'center' };
          // },
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          // cellStyle() {
          //   return { textAlign: 'center', border: ' 0px white' };
          // },
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            const index = params.rowIndex;
            const eDiv2 = document.createElement('div');
            eDiv2.innerHTML =
              `<button   title="Edit" id="btneditLabor${index}" style="background: #384163; color: #fff; display:none;border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-labor"><i class="fas fa-pencil-alt"></i></button>&nbsp;` +
              `<button  title="Cancel" id="btncancelLabor${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-labor" ><i class="fas fa-ban"></i></button>&nbsp;` +
              `<button title="Save" id="btnupdateLabor${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-labor" ><i class="fas fa-save"></i></button>` +
              `<button title="Delete" id="btndeleteLabor${index}" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button-labor" ><i class="fas fa-trash-alt"></i></button>`;
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button-labor').attr('disabled', 'disabled');
                $('.edit-button-labor').css('background', '#38416373');
                $('.edit-button-labor').css('cursor', 'default');
              });
            }
            if (index !== undefined) {
              const eButton = eDiv2.querySelectorAll('.edit-button-labor')[0];
              const uButton = eDiv2.querySelectorAll('.update-button-labor')[0];
              const cButton = eDiv2.querySelectorAll('.cancel-button-labor')[0];
              const dButton = eDiv2.querySelectorAll('.delete-button-labor')[0];
              eButton.addEventListener('click', () => {
                // params.columnApi.getDisplayedCenterColumns()[0].colId
                startEdit.setState({
                  editedRowId: null
                });
                $(`.cancel-button-labor`).hide();
                $(`.update-button-labor`).hide();
                $(`.edit-button-labor`).show();
                $(`.delete-button-labor`).show();
                $('.laborFixedratevalue').hide();
                $('.laborFixedratevaluetext').show();
                startEdit.setState({
                  editedRowId: index
                });

                startEdit.setState({
                  isCodeEdited: true
                });

                var rowPrev = {
                  // id: params.data.id,

                  paytype: params.data.paytype,
                  laborFixedratevalue: params.data.laborFixedratevalue,
                  fixedRateDate: params.data.fixedRateDate,

                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                };
                var rowPrevArray = startEdit.state.prevCodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ fixedRateDate, paytype }) =>
                    fixedRateDate === rowPrev.fixedRateDate &&
                    paytype === rowPrev.paytype
                );

                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }

                startEdit.setState({
                  prevCodeArray: rowPrevArray
                });

                var prevRow = [];
                prevRow.push({
                  paytype: params.data.paytype,
                  laborFixedratevalue: params.data.laborFixedratevalue,
                  fixedRateDate: params.data.fixedRateDate,

                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                });
                startEdit.setState({
                  oldCodeArray: prevRow
                });
                console.log(
                  'ccc===',
                  startEdit.state.prevCodeArray,
                  startEdit.state.oldCodeArray
                );
                //startEdit.refreshDisable();
                startEdit.onBtStartEditingLabor(index);
                $(`#btneditLabor${index}`).hide();
                $(`#btncancelLabor${index}`).show();
                $(`#btnupdateLabor${index}`).show();
                $(`#btndeleteLabor${index}`).hide();
              });
              uButton.addEventListener('click', () => {
                if (
                  startEdit.state.laborFixedRateNew != 0 &&
                  startEdit.state.laborFixedRateNew != '' &&
                  startEdit.state.laborFixedRateNew != '0'
                ) {
                  startEdit.onBtStopEditingLabor(index);
                  startEdit.setState({
                    editedRowId: null
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.setDataValue(
                    'laborFixedratevalue',
                    startEdit.state.laborFixedRateNew
                  );
                  // rowNode.setDataValue(
                  //   'fixedRateDate',
                  //   startEdit.state.fixedDateNew
                  // );
                  const storeId = JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0];
                  startEdit.setState({
                    isCodeEdited: false
                  });
                  startEdit.setState({
                    prevIndex: ''
                  });
                  var newRow = [];
                  newRow.push(
                    params.data.paytype,

                    params.data.laborFixedratevalue,
                    params.data.fixedRateDate,
                    storeId
                  );

                  var updatedRow = {
                    paytype: params.data.paytype,
                    laborFixedratevalue: params.data.laborFixedratevalue,
                    partsFixedratevalue: null,
                    fixedRateDate: params.data.fixedRateDate
                  };

                  startEdit.setState({
                    newCodeArray: updatedRow
                  });
                  if (startEdit.state.newCodeArray.length > 0) {
                    startEdit.setState({
                      isCodeRowUpdated: true
                    });
                  }
                  console.log(
                    'ccc==11',

                    startEdit.state.newCodeArray
                  );
                  startEdit.setState({
                    laborfixedRateError: null
                  });
                  localStorage.setItem('newRow', newRow);
                  startEdit.updateFixedRateDetails('labor', 'update');
                  startEdit.gridApiLabor.redrawRows();
                  $(`#btneditLabor${index}`).show();
                  $(`#btncancelLabor${index}`).hide();
                  $(`#btnupdateLabor${index}`).hide();
                  $(`#btndeleteLabor${index}`).show();
                } else {
                  startEdit.setState({
                    laborfixedRateError: index
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.setDataValue('laborFixedratevalue', null);
                  params.api.refreshCells({
                    columns: ['laborFixedratevalue'],
                    rowNodes: [params.node],
                    force: true
                  });
                }
              });
              cButton.addEventListener('click', () => {
                //startEdit.getAgGridData();
                startEdit.onBtStopEditingLabor(index);
                startEdit.setState({
                  editedRowId: null
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                startEdit.setState({
                  laborFixedRateNew: null
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return (
                    o.fixedRateDate == params.data.fixedRateDate &&
                    o.paytype == params.data.paytype
                  );
                });

                if (valArr.length > 0) {
                  rowNode.setDataValue(
                    'laborFixedratevalue',
                    valArr[0].laborFixedratevalue
                  );
                  // rowNode.setDataValue(
                  //   'fixedRateDate',
                  //   valArr[0].fixedRateDate
                  // );
                }

                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                }
                console.log(
                  'ccc==12',
                  params.data,
                  startEdit.state.newCodeArray
                );
                $(`#btneditLabor${index}`).show();
                $(`#btncancelLabor${index}`).hide();
                $(`#btnupdateLabor${index}`).hide();
                $(`#btndeleteLabor${index}`).show();
                $('.laborFixedratevalue').hide();
                $('.laborFixedratevaluetext').show();
              });

              dButton.addEventListener('click', function() {
                var fixedRateValue = params.data.fixedRateValue;
                var updatedRow = {
                  paytype: params.data.paytype,
                  opcode: params.data.opcode,
                  fixedRateValue: params.data.fixedRateValue,
                  fixedRateDate: params.data.fixedRateDate
                };

                startEdit.setState({
                  newCodeArray: updatedRow
                });
                if (fixedRateValue != '') {
                  // if (window.confirm('Are you sure you want to delete?')) {
                  //   alert('Row deleted');
                  //   startEdit.updateFixedRateDetails('labor', 'delete');
                  // }
                  startEdit.setState({ deleteClickedLabor: true });
                  startEdit.setState({ open: true });
                }
                $(`#btneditLabor${index}`).hide();
                $(`#btncancelLabor${index}`).hide();
                $(`#btnupdateLabor${index}`).hide();
                $(`#btndeleteLabor${index}`).show();
              });
            }
            return eDiv2;
          }
        }
      ],
      columnDefsParts: [
        // {
        //   headerName: 'Door Rate',
        //   field: 'doorRate',
        //   width: 90,
        //   suppressMenu: true,
        //   unSortIcon: true,
        //   cellStyle: function(params) {
        //     return { textAlign: 'right', border: ' 0px white' };
        //   },
        // },

        {
          headerName: 'Pay Type',
          field: 'paytype',
          // tooltipField: 'paytype',
          width: 106,
          minWidth: 106,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Fixed Rate($)',
          field: 'partsFixedratevalue',
          width: 115,
          minWidth: 115,
          flex: 1,
          cellRenderer: 'fixedRateRenderer',
          comparator: (valueA, valueB, nodeA, nodeB, isDescending) => {
            if (Number(valueA) === Number(valueB)) return 0;
            return Number(valueA) > Number(valueB) ? 1 : -1;
          },
          valueFormatter: this.formatCellValue,
          suppressMenu: true,
          unSortIcon: true,
          // sortable: false,
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Effective From',
          field: 'fixedRateDate',
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          cellStyle: function(params) {
            return { textAlign: 'center', border: ' 0px white' };
          }
        },

        {
          headerName: 'Period',
          field: 'fixrateOrder',
          valueFormatter: this.formatCellValuePeriod,
          width: 110,
          minWidth: 110,
          flex: 1,
          suppressMenu: true,
          unSortIcon: true,
          filter: 'agSetColumnFilter',
          filterParams: {
            valueFormatter: params => {
              return params.value == 1
                ? 'Current'
                : 'Prior' + Number(params.value - 1);
            }
          },
          cellStyle: function(params) {
            return { textAlign: 'left', border: ' 0px white' };
          }
        },

        {
          headerName: 'Action',
          //cellRenderer: 'buttonRenderer',
          filter: false,
          // tooltip(params) {
          //   return 'Edit';
          // },
          width: 90,
          minWidth: 90,
          flex: 1,
          editable: false,
          sortable: false,

          suppressMenu: true,
          //hide: true,
          // typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
          // this.props.keycloak.realmAccess.roles.includes('client') == true
          //   ? true
          //   : false,
          // hide:
          //   // eslint-disable-next-line react/destructuring-assignment
          //   !!(
          //     typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
          //     this.props.keycloak.realmAccess.roles.includes('client') === true
          //   ),
          // cellStyle: params => {
          //   return startEdit.state.isReloaded
          //     ? {
          //         'pointer-events': 'none',
          //         opacity: '0.4',
          //         textAlign: 'center'
          //       }
          //     : { textAlign: 'center' };
          // },
          cellStyle() {
            return { textAlign: 'center', border: ' 0px white' };
          },
          // cellStyle() {
          //   return { textAlign: 'center', border: ' 0px white' };
          // },
          // eslint-disable-next-line no-dupe-keys
          cellRenderer(params) {
            const index = params.rowIndex;
            const eDiv1 = document.createElement('div');
            eDiv1.innerHTML =
              `<button   title="Edit" id="btneditParts${index}" style="display:none;background: #384163; color: #fff; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="edit-button-parts"><i class="fas fa-pencil-alt"></i></button>` +
              ` <button  title="Cancel" id="btncancelParts${index}" style="background: #384163;color: #fff;display:none; border-radius: 3px; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px ;"  class="cancel-button-parts" ><i class="fas fa-ban"></i></button>&nbsp;` +
              `<button title="Save" id="btnupdateParts${index}" style="background: #384163;  color: #fff; border-radius: 3px; display:none; width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="update-button-parts" ><i class="fas fa-save"></i></button>` +
              `<button title="Delete" id="btndeleteParts${index}" style="background: #384163;  color: #fff; border-radius: 3px;  width: 30px; height: 22px; border: 0; font-size: 12px; cursor: pointer; line-height: 13px;" class="delete-button-parts" ><i class="fas fa-trash-alt"></i></button>`;
            if (
              props.keycloak &&
              props.keycloak.realmAccess.roles.includes('admin') == false &&
              props.keycloak.realmAccess.roles.includes('superadmin') ==
                false &&
              props.keycloak.realmAccess.roles.includes('user') == false
            ) {
              $(document).ready(function() {
                $('.edit-button-parts').attr('disabled', 'disabled');
                $('.edit-button-parts').css('background', '#38416373');
                $('.edit-button-parts').css('cursor', 'default');
              });
            }
            if (index !== undefined) {
              const eButton = eDiv1.querySelectorAll('.edit-button-parts')[0];
              const uButton = eDiv1.querySelectorAll('.update-button-parts')[0];
              const cButton = eDiv1.querySelectorAll('.cancel-button-parts')[0];
              const dButton = eDiv1.querySelectorAll('.delete-button-parts')[0];
              eButton.addEventListener('click', () => {
                // params.columnApi.getDisplayedCenterColumns()[0].colId
                $(`.cancel-button-parts`).hide();
                $(`.update-button-parts`).hide();
                $(`.edit-button-parts`).show();
                $(`.delete-button-parts`).show();
                $('.partsFixedRateSelect').hide();
                $('.partsFixedRateValueinput').hide();
                $('.partsFixedRateValuetext').show();

                startEdit.setState({
                  editedRowId: index
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                startEdit.setState({
                  partsFixedRateNew: params.data.partsFixedratevalue
                });
                var rowPrev = {
                  // id: params.data.id,

                  paytype: params.data.paytype,
                  partsFixedratevalue: params.data.partsFixedratevalue,
                  fixedRateDate: params.data.fixedRateDate,

                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                };
                var rowPrevArray = startEdit.state.prevCodeArray;
                let indexArr = rowPrevArray.findIndex(
                  ({ fixedRateDate, paytype }) =>
                    fixedRateDate === rowPrev.fixedRateDate &&
                    paytype === rowPrev.paytype
                );

                if (indexArr === -1) {
                  rowPrevArray.push(rowPrev);
                }

                startEdit.setState({
                  prevCodeArray: rowPrevArray
                });

                var prevRow = [];
                prevRow.push({
                  paytype: params.data.paytype,
                  partsFixedratevalue: params.data.partsFixedratevalue,
                  fixedRateDate: params.data.fixedRateDate,

                  storeId: JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0]
                });
                startEdit.setState({
                  oldCodeArray: prevRow
                });

                //startEdit.refreshDisable();
                startEdit.onBtStartEditingParts(index);
                $(`#btneditParts${index}`).hide();
                $(`#btncancelParts${index}`).show();
                $(`#btnupdateParts${index}`).show();
                $(`#btndeleteParts${index}`).hide();
              });
              uButton.addEventListener('click', () => {
                if (
                  startEdit.state.partsFixedRateNew != null &&
                  startEdit.state.partsFixedRateNew != 'N/A' &&
                  startEdit.state.partsFixedRateNew.split(' ')[1] != '' &&
                  startEdit.state.partsFixedRateNew != ''
                ) {
                  startEdit.onBtStopEditingParts(index);
                  startEdit.setState({
                    editedRowId: null
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.setDataValue(
                    'partsFixedratevalue',
                    startEdit.state.partsFixedRateNew
                  );
                  // rowNode.setDataValue(
                  //   'fixedRateDate',
                  //   startEdit.state.fixedDateNew
                  // );
                  const storeId = JSON.parse(
                    localStorage.getItem('selectedStoreId')
                  )[0];
                  startEdit.setState({
                    isCodeEdited: false
                  });
                  startEdit.setState({
                    prevIndex: ''
                  });
                  var newRow = [];
                  newRow.push(
                    params.data.paytype,
                    params.data.opcode,
                    params.data.partsFixedratevalue,
                    params.data.la,
                    storeId
                  );

                  var updatedRow = {
                    paytype: params.data.paytype,
                    laborFixedratevalue: null,
                    partsFixedratevalue: params.data.partsFixedratevalue,
                    fixedRateDate: params.data.fixedRateDate
                  };

                  startEdit.setState({
                    newCodeArray: updatedRow
                  });
                  if (startEdit.state.newCodeArray.length > 0) {
                    startEdit.setState({
                      isCodeRowUpdated: true
                    });
                  }
                  console.log(
                    'ccc==11',

                    startEdit.state.newCodeArray
                  );
                  startEdit.setState({
                    partsfixedRateError: null
                  });
                  localStorage.setItem('newRow', newRow);
                  startEdit.updateFixedRateDetails('parts', 'update');
                  startEdit.gridApiParts.redrawRows();
                  $(`#btneditParts${index}`).show();
                  $(`#btncancelParts${index}`).hide();
                  $(`#btnupdateParts${index}`).hide();
                  $(`#btndeleteParts${index}`).show();
                } else {
                  startEdit.setState({
                    partsfixedRateError: index
                  });
                  var rowNode = params.api.getDisplayedRowAtIndex(index);
                  rowNode.data['partsFixedratevalue'] =
                    startEdit.state.partsFixedRateNew.split(' ')[0] + ' ';
                  params.api.refreshCells({
                    columns: ['partsFixedratevalue'],
                    rowNodes: [params.node],
                    force: true
                  });
                }
              });
              cButton.addEventListener('click', () => {
                //startEdit.getAgGridData();
                startEdit.onBtStopEditingParts(index);
                startEdit.setState({
                  editedRowId: null
                });

                startEdit.setState({
                  isCodeEdited: true
                });
                var rowNode = params.api.getDisplayedRowAtIndex(index);
                let valArr = startEdit.state.prevCodeArray.filter(function(o) {
                  return (
                    o.fixedRateDate == params.data.fixedRateDate &&
                    o.paytype == params.data.paytype
                  );
                });

                if (valArr.length > 0) {
                  rowNode.setDataValue(
                    'partsFixedratevalue',
                    valArr[0].partsFixedratevalue
                  );
                  // rowNode.setDataValue(
                  //   'fixedRateDate',
                  //   valArr[0].fixedRateDate
                  // );
                }

                if (startEdit.state.newCodeArray.length <= 0) {
                  startEdit.setState({
                    isCodeRowUpdated: false
                  });
                }

                $(`#btneditParts${index}`).show();
                $(`#btncancelParts${index}`).hide();
                $(`#btnupdateParts${index}`).hide();
                $('.partsFixedRateSelect').hide();
                $(`#btndeleteParts${index}`).show();
                $('.partsFixedRateValueinput').hide();
                $('.partsFixedRateValuetext').show();
              });

              dButton.addEventListener('click', function() {
                var fixedRateValue = params.data.fixedRateValue;
                var updatedRow = {
                  paytype: params.data.paytype,
                  opcode: params.data.opcode,
                  fixedRateValue: params.data.fixedRateValue,
                  fixedRateDate: params.data.fixedRateDate
                };

                startEdit.setState({
                  newCodeArray: updatedRow
                });
                if (fixedRateValue != '') {
                  //  startEdit.updateFixedRateDetails('parts', 'delete');
                  startEdit.setState({ deleteClickedParts: true });
                  startEdit.setState({ open: true });
                }
                $(`#btneditParts${index}`).hide();
                $(`#btncancelParts${index}`).hide();
                $(`#btnupdateParts${index}`).hide();
                $(`#btndeleteParts${index}`).show();
              });
            }
            return eDiv1;
          }
        }
      ],
      rowDataLabor: [],
      rowDataParts: [],
      context: { componentParent: this },
      frameworkComponents: {
        fixedRateRenderer: PaytypeFixedRateRenderer
        // fixedRatePickerRenderer: FixedRatePickerRenderer
      },
      selectedFleetNames: [{ customerName: 'All' }],
      chartName: null,
      sortingOrder: ['asc', 'desc', null],
      headerHeight: 45,
      overlayNoRowsTemplateLabor:
        '<span style="padding: 10px; font-size: 12.5px;">No rows to show</span>',
      overlayNoRowsTemplate:
        '<span style="padding: 10px; font-size: 12.5px;">No rows to show</span>',
      defaultColDef: {
        cellClassRules: {
          greenBackground: function(params) {
            return params.rowIndex % 2 == 0;
          }
        },
        filter: 'agSetColumnFilter',
        filterParams: {
          applyMiniFilterWhileTyping: true
        },
        enableValue: true,
        sortable: true,
        filter: true,
        resizable: false,
        suppressMovable: false
      },
      excelStyles: [
        {
          id: 'bigHeader',
          font: {
            size: 25,
            color: 'primary'
          },
          alignment: {
            horizontal: 'Right',
            vertical: 'Center'
          }
        },
        {
          id: 'twoDecimalPlacesWith$',
          numberFormat: { format: '$#,##0.00' }
        },
        {
          id: 'redFont',
          font: {
            color: '#ff0000'
          }
        },
        {
          id: 'greenFont',
          font: {
            color: '008000'
          }
        },
        {
          id: 'textAlign',
          alignment: {
            horizontal: 'Center'
          }
        },
        {
          id: 'dateFormatter',
          dataType: 'DateTime',
          numberFormat: {
            format: 'mm/dd/yy'
          }
        },
        {
          id: 'greenBackground',
          interior: {
            color: '#d9f2d9',
            pattern: 'Solid'
          }
        },
        {
          id: 'header',
          interior: {
            color: '#009900',
            pattern: 'Solid'
          },
          font: {
            bold: true,
            color: '#ffffff'
          }
        }
      ]
    };
  }
  onPartsFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      partsFixedRateOld: oldVal
    });

    this.setState({
      partsFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };

  insertFixedRateDetails = data => {
    if (data[0] != '' && data[2] != '') {
      var updatedRow = {
        paytype: data[0],
        fixedRateValue: data[4] == 'labor' ? data[2] : data[5] + ' ' + data[2],
        fixedRateDate: data[3],
        type: data[4]
      };
      this.setState({
        newCodeArray: updatedRow
      });
      this.updateFixedRateDetails(data[4], 'insert', updatedRow);
    }
  };

  updateFixedRateDetails = (opt, type, data) => {
    const client = makeApolloClientPostgres;

    this.setState({
      isCodeRowUpdated: false
    });
    let oldPayArr = this.state.prevCodeArray;

    let newPayArr = this.state.newCodeArray;

    const userID = localStorage.getItem('userID');
    const start = new Date();
    client
      .mutate({
        mutation: GET_PAYTYPE_FIXED_RATE_DETAILS,
        variables: {
          pAction: type,
          pLaborOrParts: opt,
          pPartsFixedratevalue:
            data && data.type == 'parts'
              ? data.fixedRateValue
              : this.state.newCodeArray.partsFixedratevalue,
          pPaytype: data ? data.paytype : this.state.newCodeArray.paytype,
          pLaborFixedratevalue:
            data && data.type == 'labor'
              ? data.fixedRateValue
              : this.state.newCodeArray.laborFixedratevalue,
          pFixedratedate: data
            ? data.fixedRateDate
            : this.state.newCodeArray.fixedRateDate,
          userid: localStorage.getItem('userID'),
          storeId: this.state.storeId
        }
      })
      .then(result => {
        const spanAttribute = {
          pageUrl: '/FixedRates',
          origin: '',
          event: 'Menu Load',
          is_from: 'GET_PAYTYPE_FIXED_RATE_DETAILS',
          value: new Date() - start,
          provenance: localStorage.getItem('provenance')
        };
        traceSpan('Menu Load', spanAttribute);
        if (
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
            .statelessCcPhysicalRwFixedRateMasterPaytypes &&
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
            .statelessCcPhysicalRwFixedRateMasterPaytypes[0].paytype ==
            'Success'
        ) {
          this.setState({ openSnackbar: true });
          this.getAgGridData();
          this.setState({ openDialogue: false });
          this.setState({ addSuccess: true });

          this.setState({ deleteClickedLabor: false });
          this.setState({ deleteClickedParts: false });
          this.setState({
            openStatusMsgErr: '',
            openStatusMsg:
              type == 'insert'
                ? 'Fixed Rate Details Added!'
                : 'Fixed Rate Details Updated!'
          });
          client
            .mutate({
              mutation: CLIENT_AUDITS,
              variables: {
                dmlaction: 'Update paytype master',
                newdata: JSON.stringify(newPayArr),
                olddata: JSON.stringify(oldPayArr),
                schemaname: 'stateless_cc_physical_rw',
                storeId: localStorage.getItem('selectedStoreId'),
                tablename: 'pay_type_master',
                username: localStorage.getItem('userID'),
                userrole: this.props.session.user.role
              }
            })
            .then(result => {
              // console.log("data result=",result);
            });
        } else {
          if (
            result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
              .statelessCcPhysicalRwFixedRateMasterPaytypes
          ) {
            this.setState({ openSnackbar: true });
            this.setState({
              openStatusMsg: '',
              openStatusMsgErr:
                result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
                  .statelessCcPhysicalRwFixedRateMasterPaytypes[0].paytype
            });
          } else {
          }
        }
      });
  };

  hidesnackbar = () => {
    this.setState({ openSnackbar: false });
  };

  formatCellValue = params => {
    if (params.value && params.value != '') {
      return params.value + '%';
    } else {
      return '';
    }
  };

  formatCellValueRate = params => {
    if (params.value && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };
  onLaborFixedRateChanged = (oldVal, newVal) => {
    // const filterValues = e.api.getFilterModel();
    this.setState({
      laborFixedRateOld: oldVal
    });

    this.setState({
      laborFixedRateNew: newVal
    });
    // this.gridApi.redrawRows();
  };
  formatCellValueDate = params => {
    if (params.value != null && params.value != '') {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValuePeriod = params => {
    if (params.value && params.value != '') {
      return params.value == 1
        ? 'Current'
        : 'Prior' + (Number(params.value) - 1);
    } else {
      return '';
    }
  };
  onCellClickedLabor = params => {
    const id = localStorage.getItem('oldId');

    let rowId = this.state.editedRowId;
    if (params.data.id != id) {
      $(`.edit-button`).show();
      $(`.update-button`).hide();
      $(`.cancel-button`).hide();

      $('.laborFixedRateValue').hide();
    }
  };
  resetRawData = () => {
    this.gridApiLabor.setColumnDefs([]);
    this.gridApiLabor.setColumnDefs(this.state.columnDefs);
    this.gridApiParts.setColumnDefs([]);
    this.gridApiParts.setColumnDefs(this.state.columnDefsParts);
  };
  onBtStartEditingLabor = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = false;
      groupColumn[3]['editable'] = false;
      groupColumn[4]['editable'] = false;
    }
    this.state.rawGridApiLabor.setColumnDefs(groupColumn);

    this.state.rawGridApiLabor.columnController.columnDefs[1].editable = true;
    // const { rawGridApi } = this.state;
    this.state.rawGridApiLabor.setFocusedCell(
      index,
      'laborFixedratevalue',

      pinned
    );
    this.state.rawGridApiLabor.startEditingCell({
      rowIndex: index,
      colKey: 'laborFixedratevalue',

      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onBtStopEditingLabor = () => {
    this.gridApiLabor.stopEditing();
    const groupColumn = this.state.rawGridApiLabor.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;

    this.state.rawGridApiLabor.setColumnDefs(groupColumn);
  };
  onBtStartEditingParts = (index, key, char, pinned) => {
    const groupColumn = this.state.rawGridApiParts.columnController.columnDefs;
    if (
      typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
      this.props.keycloak.realmAccess.roles.length >= 1 &&
      this.props.keycloak.realmAccess.roles.includes('client') === false
    ) {
      groupColumn[0]['editable'] = false;
      groupColumn[1]['editable'] = false;
      groupColumn[2]['editable'] = false;
      groupColumn[3]['editable'] = false;
      groupColumn[4]['editable'] = false;
    }
    this.state.rawGridApiParts.setColumnDefs(groupColumn);

    this.state.rawGridApiParts.columnController.columnDefs[1].editable = true;
    // const { rawGridApi } = this.state;
    this.state.rawGridApiParts.setFocusedCell(
      index,
      'partsFixedratevalue',

      pinned
    );
    this.state.rawGridApiParts.startEditingCell({
      rowIndex: index,
      colKey: 'partsFixedratevalue',

      rowPinned: pinned,
      keyPress: key,
      charPress: char
    });
  };
  onBtStopEditingParts = () => {
    this.gridApiParts.stopEditing();
    const groupColumn = this.state.rawGridApiParts.columnController.columnDefs;
    groupColumn[0]['editable'] = false;
    groupColumn[1]['editable'] = false;
    groupColumn[2]['editable'] = false;
    groupColumn[3]['editable'] = false;
    groupColumn[4]['editable'] = false;

    this.state.rawGridApiParts.setColumnDefs(groupColumn);
  };
  onBtExport = () => {
    var params = {
      sheetName: 'Labor Grid(s)',
      fileName: 'Labor Grid(s)',
      processCellCallback: params => processCells(params),
      customHeader: [
        [],
        [
          {
            styleId: 'bigHeader',
            data: {
              type: 'String',
              value: 'Labor Grid(s)'
            },
            mergeAcross: 3
          }
        ],
        [
          {
            data: {
              type: 'String',
              value:
                this.state.selectedGrid == 1 ? 'Current Grid' : 'Prior Grid'
            },
            mergeAcross: 1
          },
          {
            data: {
              type: 'String',
              value: 'Grid / Door Install Date  : ' + this.state.gridDate
            },
            mergeAcross: 2
          },
          {
            data: {
              type: 'String',
              value: '1 Hour Door Rate  : ' + this.state.doorRate
            }
          }
        ],
        []
      ]
    };

    this.state.rawGridApi.exportDataAsExcel(params);
  };

  onGridReadyLabor = params => {
    this.setState({ rawGridApiLabor: params.api });
    this.setState({ gridApiLabor: params });
    this.setState({ gridcolumnApi: params.columnApi });
    this.gridApiLabor = params.api;
    this.getAgGridData();
  };
  onGridReadyParts = params => {
    this.setState({ rawGridApiParts: params.api });
    this.setState({ gridApiParts: params });
    this.setState({ gridcolumnApiParts: params.columnApi });
    this.gridApiParts = params.api;
    this.getAgGridData();
  };
  resetGrids = params => {
    this.gridApiLabor && this.gridApiLabor.setSortModel(null);
    this.gridApiLabor && this.gridApiLabor.setFilterModel(null);
    this.setState({
      editedRowId: null
    });

    this.setState({
      isCodeEdited: false
    });
    this.gridApiLabor && this.gridApiLabor.redrawRows();

    this.gridApiParts && this.gridApiParts.setSortModel(null);
    this.gridApiParts && this.gridApiParts.setFilterModel(null);
    this.gridApiParts && this.gridApiParts.redrawRows();
    this.props.handleResetGrid();
    this.state.gridcolumnApi && this.state.gridcolumnApi.resetColumnState();
    this.state.gridcolumnApiParts &&
      this.state.gridcolumnApiParts.resetColumnState();
  };

  getAgGridData(storeId) {
    this.setState({ isLoadingLabor: true });
    this.getAllPayTypes();
    getPaytypeFixedRateDetails(
      'get',
      'labor',
      storeId ? storeId : this.state.storeId,
      result => {
        this.setState({ isLoadingLabor: false });

        if (
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
            .statelessCcPhysicalRwFixedRateMasterPaytypes
        ) {
          var resultArr =
            result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
              .statelessCcPhysicalRwFixedRateMasterPaytypes;
          this.setState({
            rowDataLabor: resultArr
          });
          if (
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.fixedRate
          ) {
            this.filterByFixedRate(
              'labor',
              this.props.history.location.state.fixedRate,
              this.props.history.location.state.paytype
            );
          }
        }
      }
    );
    getPaytypeFixedRateDetails(
      'get',
      'parts',
      storeId ? storeId : this.state.storeId,
      result => {
        this.setState({ isLoadingParts: false });

        if (
          result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
            .statelessCcPhysicalRwFixedRateMasterPaytypes
        ) {
          var resultArr =
            result.data.statelessCcPhysicalRwGetOrSetFixedRateMasterPaytype
              .statelessCcPhysicalRwFixedRateMasterPaytypes;
          this.setState({
            rowDataParts: resultArr
          });
          if (
            this.props.history &&
            this.props.history.location.state &&
            this.props.history.location.state.fixedRate
          ) {
            this.filterByFixedRate(
              'parts',
              this.props.history.location.state.fixedRate,
              this.props.history.location.state.paytype
            );
          }
        }
      }
    );
  }

  getAllPayTypes = () => {
    getPayTypeMasterDetails(result => {
      if (result.data.statefulCcPhysicalRwPayTypeMasters.nodes) {
        if (result.data.statefulCcPhysicalRwPayTypeMasters.nodes) {
          let resultArr = result.data.statefulCcPhysicalRwPayTypeMasters.nodes;
          let payTypes = [];
          if (resultArr.length > 0) {
            payTypes = lodash.uniqBy(resultArr, obj => obj.payType);
            payTypes = lodash.sortBy(payTypes, 'payType');
          }
          this.setState({
            rowDataPayTypes: payTypes
          });
        }
      }
    });
  };

  filterByFixedRate = (type, fixedRate, paytype) => {
    if (type == 'labor') {
      if (fixedRate && paytype) {
        var fixedRateFilterComponentLabor = this.state.rawGridApiLabor.getFilterInstance(
          'laborFixedratevalue'
        );
        fixedRateFilterComponentLabor.setModel({ values: [fixedRate] });
        var paytypeFilterComponentLabor = this.state.rawGridApiLabor.getFilterInstance(
          'paytype'
        );
        paytypeFilterComponentLabor.setModel({ values: [paytype] });
        this.gridApiLabor.onFilterChanged();
      }
    } else {
      if (fixedRate && paytype) {
        var fixedRateFilterComponentParts = this.state.rawGridApiParts.getFilterInstance(
          'fixedRateDate'
        );
        fixedRateFilterComponentParts.setModel({ values: [fixedRate] });
        var paytypeFilterComponentParts = this.state.rawGridApiParts.getFilterInstance(
          'paytype'
        );
        paytypeFilterComponentParts.setModel({ values: [paytype] });
        this.gridApiParts.onFilterChanged();
      }
    }
  };
  handleclick = params => {
    this.props.history.push({
      pathname:
        localStorage.getItem('versionFlag') == 'TRUE'
          ? '/LaborMisses'
          : 'LaborGridMisses',
      state: {
        selectedFilter: this.props.history.location.state.selectedFilter,
        selectedToggle: this.props.history.location.state.selectedToggle,
        selectedMonthYear: this.props.history.location.state.selectedMonthYear,
        parent: this.props.history.location.state.parent,
        previousToggle: this.props.history.location.state.previousToggle,
        payType: this.props.history.location.state.payType,
        gridType: this.props.history.location.state.gridType,
        previousPayType: this.props.history.location.state.PrevPayType,
        previousGridType: this.props.history.location.state.PrevGridType,
        showAllJobs: this.state.showAllJobs,
        filterStart: this.props.history.location.state.filterStart,
        filterEnd: this.props.history.location.state.filterEnd,
        selectedGridType: this.props.history.location.state.selectedGridType
      }
    });
  };

  handleRadioChange = event => {
    this.setState({ gridRateValue: event.target.value });
    if (event.target.value == 'fixedRate') {
      this.setState({ fixedRate: true });
      this.setState({ laborGrid: false });
    } else {
      this.setState({ laborGrid: true });
      this.setState({ fixedRate: false });
    }
  };
  onRowEditingStartedLabor(params) {
    params.api.refreshCells({
      columns: ['laborFixedratevalue'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStoppedLabor(params) {
    params.api.refreshCells({
      columns: ['laborFixedratevalue'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStartedParts(params) {
    params.api.refreshCells({
      columns: ['partsFixedratevalue'],
      rowNodes: [params.node],
      force: true
    });
  }
  onRowEditingStoppedParts(params) {
    params.api.refreshCells({
      columns: ['partsFixedratevalue'],
      rowNodes: [params.node],
      force: true
    });
  }

  handleClose = () => {
    this.setState({ openDialogue: false });
  };
  handleSaveEmail = () => {
    this.setState({ openDialogue: true });
  };
  handleCloseDelete = () => {
    this.setState({ open: false });
  };
  handleOk = () => {
    this.setState({ open: false });
    if (this.state.deleteClickedLabor == true) {
      this.updateFixedRateDetails('labor', 'delete');
    } else {
      this.updateFixedRateDetails('parts', 'delete');
    }
  };
  render() {
    const { classes } = this.props;

    var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    // if (this.state.rowData.length > 0) {
    //   gridDate = moment(this.state.rowData[0].createdDate).format('MM/DD/YY');
    //   doorRate = '$' + this.state.rowData[0].doorRate;
    //   storeDate = moment(this.state.rowData[0].storeInstallDate).format(
    //     'MM/DD/YY'
    //   );
    // }

    return this.state.loading == false ? (
      <div>
        <Box style={{ padding: 25 }}>
          <LinearProgress color="secondary" />
          <Typography
            variant="h6"
            align="center"
            style={{ padding: 25 }}
            color="primary"
          >
            Processing...
          </Typography>
        </Box>
      </div>
    ) : (
      <div>
        {this.state.isLoadingLabor == true ||
        this.state.isLoadingParts == true ? (
          <div>
            <Box style={{ padding: 25 }}>
              <LinearProgress color="secondary" />
              <Typography
                variant="h6"
                align="center"
                style={{ padding: 25 }}
                color="primary"
              >
                Processing...
              </Typography>
            </Box>
          </div>
        ) : null}
        <span
          style={{
            display: 'flex'
          }}
        >
          <Grid xs={12} className={classes.textContainerGrid}>
            <Grid>
              <div
                className={clsx(
                  this.props.keycloak.realmAccess.roles.includes('client') ===
                    true
                    ? classes.dataGridClient
                    : classes.dataGrid,
                  'ag-theme-balham fleet-container'
                )}
                style={{
                  //height: '410px',//
                  width: 540,
                  // this.props.keycloak.realmAccess.roles.includes('client') ===
                  // true
                  //   ? 417
                  //   : 510,
                  height: window.innerHeight - 305 + 'px',
                  // height:(window.innerHeight-215)+'px',
                  alignContent: 'center',
                  marginLeft: '8px',
                  marginTop: 10,
                  display:
                    this.state.isLoading == true ||
                    (this.props.history &&
                      this.props.history.location.state &&
                      this.props.history.location.state.pageType ==
                        'PartsMisses')
                      ? 'none'
                      : 'block'
                }}
              >
                <Typography
                  variant="h5"
                  color="primary"
                  className={clsx(this.props.mainLabel, classes.subTitle)}
                >
                  Labor
                </Typography>
                <AgGridReact
                  className="ag-theme-balham"
                  style={{
                    height: '410px'
                  }}
                  onRowEditingStarted={this.onRowEditingStartedLabor}
                  onRowEditingStopped={this.onRowEditingStoppedLabor}
                  animateRows={true}
                  enableRangeSelection={false}
                  enableCharts={true}
                  modules={AllModules}
                  context={this.state.context}
                  frameworkComponents={this.state.frameworkComponents}
                  columnDefs={this.state.columnDefs}
                  excelStyles={this.state.excelStyles}
                  defaultColDef={this.state.defaultColDef}
                  onGridReady={this.onGridReadyLabor}
                  onCellClicked={this.onCellClickedLabor}
                  suppressAggFuncInHeader={true}
                  rowData={this.state.rowDataLabor}
                  sortingOrder={this.sortingOrder}
                  editType={'fullRow'}
                  tooltipShowDelay={0}
                  floatingFilter={true}
                  suppressRowClickSelection={true}
                  headerHeight={this.state.headerHeight}
                  onFilterChanged={this.onFilterChanged}
                  suppressContextMenu={true}
                  overlayNoRowsTemplate={this.state.overlayNoRowsTemplateLabor}
                  suppressDragLeaveHidesColumns={true}
                  //suppressHorizontalScroll={true}
                />
              </div>
            </Grid>
            <Grid>
              <div
                className={clsx(
                  this.props.keycloak.realmAccess.roles.includes('client') ===
                    true
                    ? classes.dataGridClient
                    : classes.dataGridParts,
                  'ag-theme-balham fleet-container'
                )}
                style={{
                  //height: '410px',//
                  // width: '100%',
                  width: 535,
                  // this.props.keycloak.realmAccess.roles.includes('client') ===
                  // true
                  //   ? 450
                  //   : 540,
                  height: window.innerHeight - 305 + 'px',
                  // height:(window.innerHeight-215)+'px',
                  alignContent: 'center',
                  marginLeft: '8px',
                  marginTop: 10,
                  display:
                    this.state.isLoading == true ||
                    (this.props.history &&
                      this.props.history.location.state &&
                      this.props.history.location.state.pageType ==
                        'LaborMisses')
                      ? 'none'
                      : 'block'
                }}
              >
                <Typography
                  variant="h5"
                  color="primary"
                  className={clsx(this.props.mainLabel, classes.subTitle)}
                >
                  Parts
                </Typography>
                <AgGridReact
                  className="ag-theme-balham"
                  style={{
                    height: '410px'
                  }}
                  onRowEditingStarted={this.onRowEditingStartedParts}
                  onRowEditingStopped={this.onRowEditingStoppedParts}
                  context={this.state.context}
                  frameworkComponents={this.state.frameworkComponents}
                  animateRows={true}
                  enableRangeSelection={false}
                  enableCharts={true}
                  modules={AllModules}
                  columnDefs={this.state.columnDefsParts}
                  excelStyles={this.state.excelStyles}
                  defaultColDef={this.state.defaultColDef}
                  onGridReady={this.onGridReadyParts}
                  suppressAggFuncInHeader={true}
                  rowData={this.state.rowDataParts}
                  editType={'fullRow'}
                  sortingOrder={this.sortingOrder}
                  tooltipShowDelay={0}
                  floatingFilter={true}
                  suppressRowClickSelection={true}
                  headerHeight={this.state.headerHeight}
                  onFilterChanged={this.onFilterChanged}
                  suppressContextMenu={true}
                  overlayNoRowsTemplate={this.state.overlayNoRowsTemplate}
                  //suppressHorizontalScroll={true}
                />
              </div>
            </Grid>
          </Grid>
          <div
            className={classes.btnSaveFixedRatePaytype}
            style={{ display: 'none' }}
            id={'btnSaveFixedRatePaytype'}
          >
            {this.state.rowDataPayTypes.length > 0 &&
            typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
            this.props.keycloak.realmAccess.roles.includes('client') != true ? (
              // <Tooltip
              //   title="Add Fixed Rate"
              //   // onClick={this.handleSaveEmail}
              //   placement="top"
              // >
              <Button
                variant="contained"
                className={clsx('reset-btn', 'btnSaveFixedRate')}
                onClick={this.handleSaveEmail}
              >
                Add Fixed Rate
              </Button>
            ) : null}
          </div>
        </span>
        <Snackbar
          open={this.state.openSnackbar}
          autoHideDuration={4000}
          onClose={this.hidesnackbar}
          style={{ marginBottom: 487 }}
        >
          {/* <Alert onClose={this.hidesnackbar} severity="success">
            {this.state.openStatusMsg}
          </Alert> */}
          <Alert
            onClose={this.hidesnackbar}
            severity={this.state.openStatusMsg != '' ? 'success' : 'error'}
          >
            {this.state.openStatusMsg != ''
              ? this.state.openStatusMsg
              : this.state.openStatusMsgErr}
          </Alert>
        </Snackbar>
        {this.state.deleteClickedLabor == true ||
        this.state.deleteClickedParts == true ? (
          <Dialog
            open={this.state.open}
            onClose={this.handleClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
          >
            <DialogContent>
              <Typography
                variant="h6"
                style={{
                  textTransform: 'none'
                }}
              >
                Are you sure you want to delete?
              </Typography>
            </DialogContent>
            <DialogActions>
              <Button onClick={this.handleCloseDelete}>Cancel</Button>
              <Button onClick={this.handleOk} autoFocus color="primary">
                Ok
              </Button>
            </DialogActions>
          </Dialog>
        ) : null}
        <FixedRateDialog
          open={this.state.openDialogue}
          handlePopupClose={this.handleClose}
          opcodes={this.state.rowDataPayTypes}
          insertFixedRateDetails={this.insertFixedRateDetails}
          type={'payTypes'}
          addSuccess={this.state.addSuccess}
        ></FixedRateDialog>
      </div>
    );
  }
}
const CustomAutocomplete = withStyles({
  inputRoot: {
    paddingRight: '5px !important',
    '&[class*="MuiFilledInput-root"]': {}
  }
})(Autocomplete);
const styles = theme => ({
  formControl: {
    margin: theme.spacing(1),
    minWidth: 110,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right'
    // marginTop: 7
  },
  dataAsOf: {
    marginRight: 32,
    float: 'left',
    marginTop: 1,
    marginLeft: '1%'
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3,
    marginRight: 8
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  inputText: {
    width: 125,
    textAlign: 'left'
  },
  inputTextRate: {
    width: 97,
    textAlign: 'left'
  },
  inputValue: {
    marginRight: 11
  },
  input: {
    margin: '0px 5px',
    width: '200px'
  },
  margin: {
    width: '22%',
    marginLeft: 5
  },
  dataContainer: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  textContainer: {
    alignItems: 'center',
    display: 'flex',
    gap: 10,
    paddingLeft: 10
  },
  textContainerGrid: {
    display: 'flex',
    gap: 10
    //paddingLeft: 10
  },
  flexGrid: {
    display: 'flex',
    alignItems: 'center',
    gap: 5
  },
  inputRoot: {
    paddingRight: 2
  },
  subTitle: {
    paddingRight: 5,
    paddingBottom: 5
  },
  datePickerInput: {
    padding: '10px',
    border: 'thin solid #ccc !important',
    borderRadius: '4px',
    height: '36px !important',
    fontFamily: 'sans-serif',
    fontSize: '14px',
    width: '100%',
    marginBottom: 20
  },
  dataGrid: {
    '@media (max-width: 1920px)': {
      width: 510
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 510
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 510
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  dataGridParts: {
    '@media (max-width: 1920px)': {
      width: 540
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 540
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 540
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },

  dataGridClient: {
    '@media (max-width: 1920px)': {
      width: 540
      //fontSize: 25,
      //marginLeft: '23%'
    },
    '@media (max-width: 1280px)': {
      width: 540
      //marginLeft: '25%'
    },
    '@media (min-width: 2304px)': {
      width: 540
      //fontSize: 30,
      // marginLeft: '45%'
    }
  },
  flexContainer: {
    alignItems: 'center'
  },
  btnSaveFixedRatePaytype: {
    '@media (min-width: 2560px)': {
      marginRight: '24% !important',
      width: '20% !important'
    },
    '@media (max-width: 2304px)': {
      marginTop: '-10px !important',
      marginLeft: '-118px !important'
    },
    '@media (max-width: 1920px)': {
      marginTop: '-10px !important',
      marginLeft: '-118px !important'
    },
    '@media (max-width: 1440px)': {
      marginTop: '-10px !important',
      marginLeft: '-118px !important'
    }
  }
});

export default withKeycloak(withStyles(styles)(LaborPartsGrid));
