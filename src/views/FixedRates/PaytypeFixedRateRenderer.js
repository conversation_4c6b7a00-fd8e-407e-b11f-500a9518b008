import React, { Component } from 'react';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tooltip,
  Link,
  FormControl,
  FormHelperText,
  TextField,
  InputLabel,
  Select,
  MenuItem
} from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputAdornment from '@material-ui/core/InputAdornment';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import 'bootstrap-daterangepicker/daterangepicker.css';
import clsx from 'clsx';
import moment from 'moment';
import 'src/input-style.css';

class PaytypeFixedRateRenderer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isFixedRateChecked: false,
      isValid:
        this.props.data.partsFixedratevalue != null &&
        (this.props.data.partsFixedratevalue.split(' ')[0] == 'Cost' ||
          this.props.data.partsFixedratevalue.split(' ')[0] == 'List')
          ? true
          : false,
      fixedValue: this.props.data.laborFixedratevalue,
      partsMarkup:
        this.props.data.partsFixedratevalue != null &&
        this.props.data.partsFixedratevalue != 'N/A'
          ? this.props.data.partsFixedratevalue.split(' ')[0]
          : 'Cost',
      partsfixedValue:
        this.props.data.partsFixedratevalue != null
          ? this.props.data.partsFixedratevalue.split(' ')[1]
          : ''
    };

    this.handleCallback = this.handleCallback.bind(this);
    this.handleLaborFixedRateChange = this.handleLaborFixedRateChange.bind(
      this
    );
    this.handleFixedRateChange = this.handleFixedRateChange.bind(this);
    this.handleMarkupChange = this.handleMarkupChange.bind(this);
  }

  handleCallback = (start, end, label) => {
    let colId = this.props.column.colId;
    this.setState({
      fixedDate: start.format('MM/DD/YY')
    });

    if (this.props.column.colId == 'fixedRateDate') {
      this.props.context.componentParent.onFixedDateChanged(
        this.props.data.fixedRateDate,
        start.format('YYYY-MM-DD')
      );

      //this.props.node.setDataValue(colId, start.format('MM/DD/YY'));
    }
  };
  handleLaborFixedRateChange(event) {
    let colId = this.props.column.colId;

    this.setState({
      fixedValue: event.target.value
    });

    if (this.props.column.colId == 'laborFixedratevalue') {
      this.props.context.componentParent.onLaborFixedRateChanged(
        this.props.data.laborFixedratevalue,
        event.target.value
      );
    }
  }
  handleMarkupChange(event) {
    this.setState({
      partsMarkup: event.target.value
    });
    if (event.target.value == 'Cost' || event.target.value == 'List') {
      this.setState({
        isValid: true
      });

      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsFixedratevalue,
        event.target.value == 'N/A' ? 'Cost' : event.target.value
      );
    } else {
      this.setState({
        isValid: false
      });
      var val =
        this.state.partsfixedValue != null && this.state.partsfixedValue != ''
          ? this.state.partsfixedValue
          : '';
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsFixedratevalue,
        (event.target.value == 'N/A' ? 'Cost' : event.target.value) + ' ' + val
      );
    }
  }
  handleFixedRateChange(event) {
    let colId = this.props.column.colId;

    this.setState({
      fixedValue: event.target.value
    });

    this.props.context.componentParent.onPartsFixedRateChanged(
      this.props.data.partsFixedratevalue,
      (this.state.partsMarkup == 'N/A' ? 'Cost' : this.state.partsMarkup) +
        ' ' +
        (this.state.partsfixedValue == 0 ? '' : this.state.partsfixedValue)
    );
  }
  render() {
    const { classes } = this.props;

    var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    var regExParts = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    return this.props.colDef.field == 'laborFixedratevalue' ? (
      <>
        <FormControl
          variant="outlined"
          className="laborFixedratevalue"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? 'block'
                : 'none'
          }}
        >
          <OutlinedInput
            className={classes.formControl}
            title="Labor fixed rate($)"
            id="outlined-adornment-weight"
            error={
              this.props.context.componentParent.state.laborfixedRateError ==
              null
                ? false
                : this.props.context.componentParent.state
                    .laborfixedRateError == this.props.rowIndex
                ? true
                : false
            }
            value={this.state.fixedValue}
            onBlur={this.handleLaborFixedRateChange}
            onChange={e =>
              (e.target.value === '' || regEx.test(e.target.value)) &&
              this.setState({
                fixedValue: e.target.value
              })
            }
            // onChange={this.handleFixedRateChange}
            startAdornment={
              <InputAdornment
                classes={{ root: classes.adorment }}
                position="start"
                disableTypography={true}
              >
                $
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
          className="laborFixedratevaluetext"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.laborFixedratevalue != null &&
            '$' + this.props.data.laborFixedratevalue}
        </span>
      </>
    ) : // <input
    //   type="text"
    //   style={{ display: this.props.data.fixedRate == 1 ? 'block' : 'none' }}
    //   className="fixedRate-value"
    //   disabled={
    //     this.props.context.componentParent.state.editedRowId == null
    //       ? true
    //       : this.props.context.componentParent.state.editedRowId !=
    //         this.props.rowIndex
    //       ? true
    //       : false
    //   }
    //   // onClick={this.checkedHandler}
    // />
    this.props.colDef.field == 'partsFixedratevalue' ? (
      <div
        className="partsFixedratevalue"
        style={{
          display: 'inline-flex',
          gap: 3,
          marginTop:
            this.props.context.componentParent.state.editedRowId != null
              ? '-2px'
              : '1px',
          alignItems: 'center'
        }}
      >
        <select
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? 'block'
                : 'none'
          }}
          title="Parts fixed rate"
          onChange={e => this.handleMarkupChange(e)}
          value={this.state.partsMarkup}
          className={clsx(classes.formControlSelect, 'partsFixedRateSelect')}
          name="parts-markups"
          id="parts-markups"
        >
          <option value="Cost">Cost</option>
          <option value="Cost-">Cost-</option>
          <option value="Cost+">Cost+</option>
          <option value="Cost%">Cost%</option>
          <option value="List">List</option>
          <option value="List-">List-</option>
          <option value="List+">List+</option>
          <option value="List%">List%</option>
        </select>

        <FormControl
          variant="outlined"
          className="partsFixedRateValueinput"
          disabled={!this.state.isValid ? false : 'true'}
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null
                ? 'block'
                : 'none'
          }}
        >
          <OutlinedInput
            className={clsx(classes.formControl, 'fixedRateValue')}
            classes={{ input: classes.adorment }}
            id="outlined-adornment-weight"
            title="Labor fixed rate(%)"
            value={!this.state.isValid ? this.state.partsfixedValue : 0}
            error={
              this.props.context.componentParent.state.partsfixedRateError ==
              null
                ? false
                : this.props.context.componentParent.state
                    .partsfixedRateError == this.props.rowIndex
                ? true
                : false
            }
            onBlur={this.handleFixedRateChange}
            onChange={e =>
              (e.target.value === '' || regExParts.test(e.target.value)) &&
              this.setState({
                partsfixedValue: e.target.value
              })
            }
            // onChange={this.handleFixedRateChange}
            endAdornment={
              <InputAdornment
                classes={{ input: classes.adorment }}
                position="end"
                disableTypography={true}
              >
                %
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
          className="partsFixedRateValuetext"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.partsFixedratevalue != null &&
            this.props.data.partsFixedratevalue != '' &&
            this.props.data.partsFixedratevalue != 'N/A' &&
            this.props.data.partsFixedratevalue + '%'}
        </span>
      </div>
    ) : (
      ''
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    width: 60,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: '8px !important',
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '80px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,

    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 0
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  }
});
export default withStyles(styles)(PaytypeFixedRateRenderer);
