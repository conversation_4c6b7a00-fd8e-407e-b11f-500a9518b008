import React from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';
import { Snackbar, SnackbarContent, colors } from '@material-ui/core';
import CheckCircleIcon from '@material-ui/icons/CheckCircleOutlined';
import { PermPhoneMsgTwoTone } from '@material-ui/icons';

const useStyles = makeStyles(theme => ({
  content: {
    backgroundColor: colors.green[600]
  },
  contentFail: {
    backgroundColor: colors.red[600]
  },
  message: {
    display: 'flex',
    alignItems: 'center'
  },
  icon: {
    marginRight: theme.spacing(2)
  },
  close: {
    padding: '0px',
    paddingLeft: '5px',
    color: '#fff'
  }
}));

function SuccessSnackbarHelp({ open, onClose, msg, goalFail }) {
  const classes = useStyles();
  return (
    <Snackbar
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'center'
      }}
      autoHideDuration={2000}
      onClose={onClose}
      open={open}
    >
      {goalFail == true ? (
        <SnackbarContent
          className={classes.contentFail}
          message={
            <span className={classes.message}>
              <CheckCircleIcon className={classes.icon} />
              {msg}
            </span>
          }
          variant="h6"
        />
      ) : (
        <SnackbarContent
          className={classes.content}
          message={
            <span className={classes.message}>
              <CheckCircleIcon className={classes.icon} />
              {msg}
            </span>
          }
          variant="h6"
        />
      )}
    </Snackbar>
  );
}

SuccessSnackbarHelp.propTypes = {
  onClose: PropTypes.func,
  open: PropTypes.bool
};

SuccessSnackbarHelp.defaultProps = {
  open: true,
  onClose: () => {}
};

export default SuccessSnackbarHelp;
