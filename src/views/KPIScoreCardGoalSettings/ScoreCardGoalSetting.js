/* eslint-disable react/default-props-match-prop-types */
/* eslint-disable react/jsx-no-duplicate-props */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-shadow */
/* eslint-disable react/static-property-placement */
/* eslint-disable no-unused-vars */
import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import Grid from '@material-ui/core/Grid';
import clsx from 'clsx';
// eslint-disable-next-line import/no-extraneous-dependencies
import '@fortawesome/fontawesome-free/css/all.min.css';
import { UPDATE_KPI_SCORECARD_GOAL } from 'src/graphql/queries';
import makeApolloClient from 'src/utils/apolloRootClientPostgresWrite';
import { getTimeZone } from 'src/utils/Utils';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import EditIcon from '@material-ui/icons/Edit';
import {
  Box,
  Divider,
  fade,
  LinearProgress,
  Paper,
  Tooltip,
  Typography,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  Checkbox,
  TextField,
  Snackbar
} from '@material-ui/core';
import ReactHtmlParser from 'react-html-parser';
import Link from '@material-ui/core/Link';
import Tab from '@material-ui/core/Tab';
import Tabs from '@material-ui/core/Tabs';
import ExportIcon from '@material-ui/icons/GetApp';
import { withKeycloak } from '@react-keycloak/web';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
// import { AgGridReact } from 'ag-grid-react/lib/agGridReact';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
// eslint-disable-next-line import/no-extraneous-dependencies
import $, { param } from 'jquery';
import React from 'react';
import Page from 'src/components/Page';
import 'react-grid-layout/css/styles.css';
import SuccessSnackbar from './SuccessSnackbar';
import makeApolloClientPostgres from 'src/utils/apolloRootClientPostgres';
import {
  getKPIScorecardGoal,
  getAllServiceAdvisors,
  getKPIScorecardGoalAdvisor,
  getOrSetKPIScorecardEmail,
  getAllTechnicians,
  getKPIScorecardGoalTechnician
} from 'src/utils/hasuraServices';
import Button from '@material-ui/core/Button';
import moment from 'moment';
import Alert from '@material-ui/lab/Alert';
import IconButton from '@material-ui/core/IconButton';
import Collapse from '@material-ui/core/Collapse';
import CloseIcon from '@material-ui/icons/Close';
import PropTypes from 'prop-types';
import { SET_REFRESH_STATUS } from 'src/actions';
import { connect } from 'react-redux';
import { setRefreshStatus } from '../../actions';
import { withStyles } from '@material-ui/styles';
import { TimePicker } from '@material-ui/pickers';
import { red } from '@material-ui/core/colors';
import CellRenderer from './CellRenderer.js';
import NumericCellEditor from './NumericCellEditor';
import InactiveEmailDialog from './InactiveEmailDialog';
import EmailDialog from 'src/components/EmailDialog';
import { i } from 'react-dom-factories';
import DeleteDialog from './DeleteDialog';
import { ReactSession } from 'react-client-session';
import { traceSpan } from 'src/utils/OTTTracing';
import Technician from '../Technicians/Technician';
var Dealer = process.env.REACT_APP_DEALER;
var lodash = require('lodash');
class ScoreCardGoalSetting extends React.Component {
  componentWillMount() {
    this.setState({ rowDataTech: [] });
    this.setState({ rowDataAdv: [] });
    this.setState({ serviceAdvisors: ['All'] });
    this.setState({ store: localStorage.getItem('selectedStoreId') });
  }
  componentDidUpdate() {
    // if(ReactSession.get("serviceAdvisors") != undefined) { console.log("goal=1=",ReactSession.get("serviceAdvisors"),this.state.serviceAdvisor);
    //   var checkStatus = lodash.isEqual(
    //     this.state.serviceAdvisors,
    //     ReactSession.get("serviceAdvisors")
    //   );
    //   if(checkStatus == false) {
    //     console.log("goal=2=",ReactSession.get("serviceAdvisors"),this.state.serviceAdvisor);
    //     this.setState({ serviceAdvisors: ReactSession.get("serviceAdvisors") });
    //     this.getAgGridData();
    //     getAllServiceAdvisors(result => {
    //       if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
    //         // if (this.state.kpiAdvisor.length > 0) {
    //         //   if (
    //         //     result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
    //         //       e => {
    //         //         if (e.serviceadvisor == this.state.kpiAdvisor[0]) {
    //         //           let advisorName =
    //         //             (e.nickname ? e.nickname : e.name) +
    //         //             ' [' +
    //         //             e.serviceadvisor.toString() +
    //         //             ']';
    //         //           this.setState({
    //         //             personName:
    //         //               (e.nickname ? e.nickname : e.name) +
    //         //               ' [' +
    //         //               e.serviceadvisor.toString() +
    //         //               ']'
    //         //           });
    //         //           this.setState({ kpiAdvisor: [] });
    //         //           const advisor = advisorName
    //         //             .split('[')[1]
    //         //             .split(']')[0]
    //         //             .toString();

    //         //           this.setState({ advisor: advisor });
    //         //           this.setState({ isLoadingAdvisor: true });
    //         //           this.getAdvisorData(advisor);
    //         //         }
    //         //       }
    //         //     )
    //         //   );
    //         // }
    //         this.setState({
    //           setAdvisorName:
    //             // ['Choose Advisor'].concat(
    //             result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.map(
    //               e =>
    //                 e
    //                   ? (e.nickname ? e.nickname : e.name) +
    //                     ' [' +
    //                     e.serviceadvisor.toString() +
    //                     ']' +
    //                     '-status-' +
    //                     e.active
    //                   : ''
    //             )
    //           // )
    //         });
    //       }
    //     });
    //   }
    // }
    if (ReactSession.get('selectedStoreId') != undefined) {
      console.log(
        'goal=3=',
        ReactSession.get('serviceAdvisors'),
        this.state.serviceAdvisor
      );
      var checkSt = lodash.isEqual(
        this.state.store,
        ReactSession.get('selectedStoreId')
      );
      if (checkSt == false) {
        console.log(
          'goal=4=',
          ReactSession.get('serviceAdvisors'),
          this.state.serviceAdvisor
        );
        this.setState({ personName: [] });
        this.setState({ personNameTech: [] });
        this.setState({ rowDataAdv: [] });
        this.setState({ rowDataTech: [] });
        this.setState({ rowDataEmailAdv: [] });
        this.setState({ isLoading: true });
        this.setState({ store: localStorage.getItem('selectedStoreId') });
        this.setState({ checked: false });
        this.setState({ tabSelect: 1 });
        this.getAgGridData();
        getAllServiceAdvisors(result => {
          if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
            var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
              e => e.active == 1
            );
            const advDepartment = advArr.filter(
              item => item.department === 'Service'
            );

            if (this.state.kpiAdvisor.length > 0) {
              if (
                advDepartment.filter(e => {
                  if (e.serviceadvisor == this.state.kpiAdvisor[0]) {
                    let advisorName =
                      (e.nickname ? e.nickname : e.name) +
                      ' [' +
                      e.serviceadvisor.toString() +
                      ']';
                    this.setState({
                      personName:
                        (e.nickname ? e.nickname : e.name) +
                        ' [' +
                        e.serviceadvisor.toString() +
                        ']'
                    });
                    this.setState({ kpiAdvisor: [] });
                    const advisor = advisorName
                      .split('[')[1]
                      .split(']')[0]
                      .toString();

                    this.setState({ advisor: advisor });
                    this.setState({ isLoadingAdvisor: true });
                    this.getAdvisorData(advisor, '');
                  }
                })
              );
            }
            this.setState({
              setAdvisorName:
                // ['Choose Advisor'].concat(
                advDepartment.map(e =>
                  e
                    ? (e.nickname ? e.nickname : e.name) +
                      ' [' +
                      e.serviceadvisor.toString() +
                      ']' +
                      '-status-' +
                      e.active
                    : ''
                )
              // )
            });
            // console.log(
            //   'data=',
            //   result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.map(
            //     e =>
            //       e
            //         ? (e.nickname ? e.nickname : e.name) +
            //           ' [' +
            //           e.serviceadvisor.toString() +
            //           ']' +
            //           '-status-' +
            //           e.active
            //         : ''
            //   )
            // );
          }
        });
        getAllTechnicians(result => {
          if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
            var techArr = result.data.statelessCcPhysicalRwGetTechnicians.nodes.filter(
              e => e.active == 1
            );
            const techDepartment = techArr.filter(
              item => item.department === 'Service'
            );
            if (this.state.kpiTechnician.length > 0) {
              if (
                techDepartment.filter(e => {
                  if (e.lbrtechno == this.state.kpiTechnician[0]) {
                    let techName =
                      (e.nickname ? e.nickname : e.name) +
                      ' [' +
                      e.lbrtechno.toString() +
                      ']';
                    this.setState({
                      personNameTech:
                        (e.nickname ? e.nickname : e.name) +
                        ' [' +
                        e.lbrtechno.toString() +
                        ']'
                    });
                    this.setState({ kpiTechnician: [] });
                    const technician = techName
                      .split('[')[1]
                      .split(']')[0]
                      .toString();

                    this.setState({ technician: technician });
                    this.setState({ isLoadingATechnician: true });
                    this.getTechnicianData(technician, '');
                  }
                })
              );
            }

            this.setState({
              setTechnicianName: techDepartment.map(e =>
                e
                  ? (e.nickname ? e.nickname : e.name) +
                    ' [' +
                    e.lbrtechno.toString() +
                    ']' +
                    '-status-' +
                    e.active
                  : ''
              )
            });
          }
        });
        this.getEmailList('store_goal');
      }
    }
  }
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };

  componentDidMount() {
    getAllServiceAdvisors(result => {
      if (result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes) {
        var advArr = result.data.statelessCcPhysicalRwGetTblServiceAdvisors.nodes.filter(
          e => e.active == 1
        );
        const advDepartment = advArr.filter(
          item => item.department === 'Service'
        );
        if (this.state.kpiAdvisor.length > 0) {
          if (
            advDepartment.filter(e => {
              if (e.serviceadvisor == this.state.kpiAdvisor[0]) {
                let advisorName =
                  (e.nickname ? e.nickname : e.name) +
                  ' [' +
                  e.serviceadvisor.toString() +
                  ']';
                this.setState({
                  personName:
                    (e.nickname ? e.nickname : e.name) +
                    ' [' +
                    e.serviceadvisor.toString() +
                    ']'
                });
                this.setState({ kpiAdvisor: [] });
                const advisor = advisorName
                  .split('[')[1]
                  .split(']')[0]
                  .toString();

                this.setState({ advisor: advisor });
                this.setState({ isLoadingAdvisor: true });
                this.getAdvisorData(advisor, '');
              }
            })
          );
        }
        this.setState({
          setAdvisorName:
            // ['Choose Advisor'].concat(
            advDepartment.map(e =>
              e
                ? (e.nickname ? e.nickname : e.name) +
                  ' [' +
                  e.serviceadvisor.toString() +
                  ']' +
                  '-status-' +
                  e.active
                : ''
            )
          // )
        });
      }
    });

    getAllTechnicians(result => {
      if (result.data.statelessCcPhysicalRwGetTechnicians.nodes) {
        var techArr = result.data.statelessCcPhysicalRwGetTechnicians.nodes.filter(
          e => e.active == 1
        );
        const techDepartment = techArr.filter(
          item => item.department === 'Service'
        );
        console.log('techArr===', techDepartment);
        if (this.state.kpiTechnician.length > 0) {
          console.log('enter====1==1==1=');
          if (
            techDepartment.filter(e => {
              if (e.lbrtechno == this.state.kpiTechnician[0]) {
                let techName =
                  (e.nickname ? e.nickname : e.name) +
                  ' [' +
                  e.lbrtechno.toString() +
                  ']';
                this.setState({
                  personNameTech:
                    (e.nickname ? e.nickname : e.name) +
                    ' [' +
                    e.lbrtechno.toString() +
                    ']'
                });
                this.setState({ kpiTechnician: [] });
                const technician = techName
                  .split('[')[1]
                  .split(']')[0]
                  .toString();

                this.setState({ technician: technician });
                this.setState({ isLoadingATechnician: true });
                this.getTechnicianData(technician, '');
              }
            })
          );
        }

        this.setState({
          setTechnicianName: techDepartment.map(e =>
            e
              ? (e.nickname ? e.nickname : e.name) +
                ' [' +
                e.lbrtechno.toString() +
                ']' +
                '-status-' +
                e.active
              : ''
          )
        });
      }
    });
  }
  constructor(props) {
    super(props);
    let advisor =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.advisor
        ? this.props.history.location.state.advisor
        : [];
    let technician =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.technician
        ? this.props.history.location.state.technician
        : [];
    this.state = {
      storeSave: false,
      advisorSave: false,
      technicianSave: false,
      tabSelect: 1,
      mailFrequency: '',
      mailStatus: '',
      recipientId: '',
      scheduledOn: '',
      serviceAdvisor: '',

      rawGridApi: {},
      personName: [],
      personNameTech: [],
      kpiAdvisor: advisor,
      kpiTechnician: technician,
      items: [],
      itemsStore: [],
      emailListDialog: [],
      flgInsert: false,
      prevItem: [],
      value: '',
      valueStore: '',
      gridApi: {},
      checked: false,
      openPopup: false,
      emailValue: '',
      emailId: [],
      emailIdAdv: [],
      open: false,
      error: '',
      deleteFlag: false,
      AdvGridDisplay: false,
      isLoading: true,
      editForm: true,
      editFormAdvisor: true,
      openSnackbar: false,
      advisorChange: false,
      goalFail: false,
      isLoadingAdvisor: false,
      isEdited: false,
      isEditedAdv: false,
      isEditedTech: false,
      openDialogue: false,
      openDelete: false,
      active: true,
      rowDataEmail: [],
      rowDataEmailAdv: [],
      selectedDelete: '',
      advisorSelected: false,
      selectedAdvisorId: '',
      columnDefs: [
        {
          headerName: 'KPI Number',
          // valueGetter: 'node.rowIndex + 1',
          editable: false,
          width: 60,
          valueFormatter: this.formatCellValueKPI,
          // minWidth: 15,

          field: 'kpino',
          //cellClass: 'textAlign',
          suppressMenu: true,
          // flex: 1,
          suppressFilter: true,
          rowGroup: false,
          hide: false,
          cellClass: this.getCellClass,
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 5px black',
              fontSize: '14px'
              // left: -10
            };
          }
        },

        {
          headerName: 'KPI Name',
          chartDataType: 'series',
          field: 'goalname',
          editable: false,
          width: 280,
          cellClass: this.getKPICellClass,
          cellRenderer: function(params) {
            if (params.value != undefined) return params.value;
          },
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,

          tooltipValueGetter: function(params) {
            // Force plain text representation
            return String(params.value).replace(/<[^>]*>/g, '');
          },

          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white',
              fontSize: '14px'
            };
          }
        },
        {
          headerName: 'Prior 12 \nMonths',
          field: 'prior12Val',
          width: 60,
          // width: 40,
          //  flex: 1,
          //
          // resizable: false,
          editable: false,
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          valueFormatter: this.formatCellValuePriorMonth,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Prior 3 \nMonths',
          field: 'prior3Val',
          width: 60,
          // width: 40,
          // flex: 1,
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          valueFormatter: this.formatCellValuePriorMonth,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Current Goal \nDate',
          field: 'currentgoaldate',
          width: 60,
          // flex: 1,
          valueFormatter: this.formatCellValueDate,
          filterParams: {
            valueFormatter: this.formatCellValueDate
          },
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'left'
            };
          }
        },
        {
          headerName: 'Current Goal',
          field: 'storegoalval',
          // width: 30,
          width: 60,
          editable:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('client') === true
              ? false
              : true,
          // flex: 1,
          suppressMenu: true,
          unSortIcon: false,
          cellRenderer: 'cellRenderer',
          //  cellEditorSelector: NumericCellEditor,
          valueParser: this.numberParser,
          cellEditor: 'numericCellEditor',
          cellStyle(params) {
            if (params.data == undefined) {
              return { display: 'none' };
            } else {
              return {
                border: ' 0px white',
                textAlign: 'center',
                fontSize: '14px'
              };
            }
          },
          cellRendererParams: {
            technician: false,
            store: true,
            advisor: false,
            onEditStart: this.handleEditStart
          },
          cellClassRules: {
            'green-bg': params => {
              return params.data;
            }
          }
        }
      ],
      columnDefsAdvisor: [
        {
          headerName: 'KPI Number',
          field: 'kpino',
          editable: false,
          // width: 120,
          minWidth: 100,
          valueFormatter: this.formatCellValueKPI,
          flex: 1,

          rowGroup: false,
          hide: false,
          cellClass: this.getCellClass,
          suppressFilter: true,
          suppressMenu: true,
          cellStyle() {
            return {
              // textAlign: 'left',
              border: ' 0px white',
              fontSize: '14px'
            };
          }
        },
        {
          headerName: 'KPI Name',
          chartDataType: 'series',
          field: 'goalname',
          editable: false,
          // width: 327,
          minWidth: 410,
          flex: 1,
          cellClass: this.getKPICellClass,
          //  cellClass: 'textAlign',
          cellRenderer: function(params) {
            if (params.value != undefined) return params.value;
          },
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          tooltipValueGetter: function(params) {
            // Force plain text representation
            return String(params.value).replace(/<[^>]*>/g, '');
          },
          cellStyle() {
            return { border: ' 0px white', fontSize: '14px' };
          }
        },
        {
          headerName: 'Store Goal',
          field: 'storegoalval',
          flex: 1,
          // width: 75,
          minWidth: 75,
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Prior 12 \nMonths',
          field: 'prior12Val',
          // flex: 1,
          // width: 130,
          minWidth: 60,
          width: 130,
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          valueFormatter: this.formatCellValuePriorMonth,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Prior 3 \nMonths',
          field: 'prior3Val',
          // flex: 1,
          // width: 130,
          width: 130,
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          valueFormatter: this.formatCellValuePriorMonth,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Current Goal \nDate',
          field: 'currentgoaldate',
          minWidth: 60,
          width: 150,
          // width: 110,
          flex: 1,
          valueFormatter: this.formatCellValueDateAdv,
          filterParams: {
            valueFormatter: this.formatCellValueDateAdv
          },
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'left'
            };
          }
        },
        {
          headerName: 'Current Goal',
          field: 'advisorgoalval',
          flex: 1,
          // width: 110,
          minWidth: 100,
          //
          // resizable: false,
          editable:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('client') === true
              ? false
              : true,
          // type: 'editableColumn',
          valueParser: this.numberParser,
          cellEditor: 'numericCellEditor',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          headerName: 'Current Goal',
          flex: 1,
          // width: 110,
          minWidth: 100,
          //
          // resizable: false,
          editable:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('client') === true
              ? false
              : true,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          cellRenderer: 'cellRenderer',
          cellEditor: 'numericCellEditor',
          cellStyle(params) {
            if (params.data == undefined) {
              return { display: 'none' };
            } else {
              return {
                border: ' 0px white',
                textAlign: 'center',
                fontSize: '14px'
              };
            }
          },
          cellRendererParams: {
            technician: false,
            store: false,
            advisor: true,
            onEditStart: this.handleEditStart
          },
          cellClassRules: {
            'green-bg': params => {
              return params.data;
            }
          },

          cellRenderer: 'cellRenderer',

          cellStyle(params) {
            if (params.data == undefined) {
              return { display: 'none' };
            } else {
              return {
                border: ' 0px white',
                textAlign: 'center',
                fontSize: '14px'
              };
            }
          },
          cellClassRules: {
            'green-bg': params => {
              return params.data;
            }
          },
          valueParser: this.numberParser
        }
      ],
      columnDefsEmail: [
        {
          headerName: 'Email',
          chartDataType: 'series',
          field: 'recipientId',
          editable: false,
          width: 300,
          // flex:1,

          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          suppressFilter: true,
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white',
              fontSize: '12px'
            };
          }
        },

        {
          headerName: 'Id',
          field: 'id',
          width: 150,
          hide: true,
          suppressMenu: true,
          filter: 'agSetColumnFilter',
          unSortIcon: true,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        },

        {
          headerName: 'Actions',
          field: '',
          width: 120,
          editable: false,
          suppressMenu: true,
          filter: 'agSetColumnFilter',
          unSortIcon: true,
          suppressFilter: true,
          hide:
            typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
            this.props.keycloak.realmAccess.roles.length >= 1 &&
            this.props.keycloak.realmAccess.roles.includes('superadmin') ===
              true
              ? false
              : true,
          cellRendererFramework: params => (
            <span style={{ display: 'flex', justifyContent: 'space-evenly' }}>
              <Tooltip title="Edit">
                <EditIcon
                  htmlColor="rgb(0, 61, 107)"
                  id={'edit' + params.rowIndex}
                  style={{ width: 18, left: '8', top: '70%' }}
                  onClick={() => this.handleEditEmailStore(params.data)}
                ></EditIcon>
              </Tooltip>
              <Tooltip title="Delete">
                <DeleteIcon
                  htmlColor="rgb(0, 61, 107)"
                  style={{ width: 18, left: '8', top: '70%' }}
                  id={'delete' + params.rowIndex}
                  onClick={() => this.showDeleteModal(params.data)}
                />
              </Tooltip>
            </span>
          ),
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        },
        {
          field: 'mailStatus',
          width: 36,
          suppressMenu: true,
          hide: true,
          cellStyle: function() {
            return {
              cursor: 'pointer'
            };
          }
        }
      ],
      columnDefsEmailAdv: [
        {
          headerName: 'Email',
          chartDataType: 'series',
          field: 'recipientId',
          editable: false,
          width: 150,
          // flex:1,

          cellClass: 'textAlign',
          suppressMenu: true,
          unSortIcon: true,
          suppressFilter: true,
          tooltipField: 'recipientId',
          cellStyle() {
            return {
              textAlign: 'left',
              border: ' 0px white',
              fontSize: '12px'
            };
          }
        },
        {
          headerName: 'Actions',
          field: ' ',
          width: 120,
          editable: false,
          suppressMenu: true,
          filter: 'agSetColumnFilter',
          unSortIcon: true,
          suppressFilter: true,
          cellRendererFramework: params => (
            <span style={{ display: 'flex', justifyContent: 'space-evenly' }}>
              <Tooltip title="Edit">
                <EditIcon
                  htmlColor="rgb(0, 61, 107)"
                  style={{ width: 18, left: '8', top: '70%' }}
                  onClick={() => this.handleEditEmail(params.data)}
                ></EditIcon>
              </Tooltip>
              <Tooltip title="Delete">
                <DeleteIcon
                  htmlColor="rgb(0, 61, 107)"
                  style={{ width: 18, left: '8', top: '70%' }}
                  onClick={() => this.showDeleteModalAdv(params.data)}
                />
              </Tooltip>
            </span>
          ),
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        },

        {
          headerName: 'Report Type',
          field: 'serviceAdvisor',
          width: 80,
          suppressMenu: true,
          filter: 'agSetColumnFilter',
          unSortIcon: true,
          suppressFilter: true,
          valueFormatter: this.formatCellValueSchedule,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        },
        {
          headerName: 'Id',
          field: 'id',
          width: 150,
          hide: true,
          suppressMenu: true,
          filter: 'agSetColumnFilter',
          unSortIcon: true,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '12px',
              textAlign: 'left'
            };
          }
        },

        {
          field: 'mailStatus',
          width: 36,
          suppressMenu: true,
          hide: true,
          cellStyle: function() {
            return {
              cursor: 'pointer'
            };
          }
        }
      ],
      rowData: [],
      rowDataAdv: [],
      rowDataTech: [],
      rowDataPrevAdv: [],
      frameworkComponents: {
        cellRenderer: CellRenderer,
        numericCellEditor: NumericCellEditor
      },
      defaultColDef: {
        suppressKeyboardEvent: params => params.event.keyCode === 13,
        cellClassRules: {
          greenBackground(params) {
            return params.rowIndex % 2 === 0;
          }
        },
        alwaysShowHorizontalScroll: true,
        stopEditingWhenCellsLoseFocus: true,
        // suppressHorizontalScroll: true,
        suppressClickEdit: true,
        suppressSorting: true,
        enableValue: true,
        enableFilter: false,
        sortable: true,
        suppressMovable: true

        //  filter: true,
        //  resizable: false,
        // editable: true
      },

      autoGroupColumnDef: {
        headerName: 'KPI Number',
        field: 'kpino',
        width: 55,
        suppressMenu: true,
        //  cellRenderer: 'agGroupCellRenderer',
        valueFormatter: this.formatCellValueKPI2,
        cellRendererParams: {
          suppressCount: true
        },
        cellStyle() {
          return {
            textAlign: 'left'
          };
        }
      },
      autoGroupColumnDefAdv: {
        headerName: 'KPI Number',
        field: 'kpino',
        minWidth: 90,
        suppressMenu: true,
        //  cellRenderer: 'agGroupCellRenderer',
        valueFormatter: this.formatCellValueKPI2,
        cellRendererParams: {
          suppressCount: true
        },
        cellStyle() {
          return {
            textAlign: 'left'
          };
        }
      },
      // editType: 'fullRow',
      autoGroupColumnDefTech: {
        headerName: 'KPI Number',
        field: 'kpino',
        minWidth: 90,
        suppressMenu: true,
        //  cellRenderer: 'agGroupCellRenderer',
        valueFormatter: this.formatCellValueKPI2,
        cellRendererParams: {
          suppressCount: true
        },
        cellStyle() {
          return {
            textAlign: 'left'
          };
        }
      },
      columnDefsTechnician: [
        {
          headerName: 'KPI Number',
          field: 'kpino',
          editable: false,
          // width: 120,
          minWidth: 100,
          valueFormatter: this.formatCellValueKPI,
          flex: 1,

          rowGroup: false,
          hide: false,
          cellClass: this.getCellClass,
          suppressFilter: true,
          suppressMenu: true,
          cellStyle() {
            return {
              // textAlign: 'left',
              border: ' 0px white',
              fontSize: '14px'
            };
          }
        },
        {
          headerName: 'KPI Name',
          chartDataType: 'series',
          field: 'goalname',
          editable: false,
          // width: 327,
          minWidth: 410,
          flex: 1,
          cellClass: this.getKPICellClass,
          //  cellClass: 'textAlign',
          cellRenderer: function(params) {
            if (params.value != undefined) return params.value;
          },
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          tooltipValueGetter: function(params) {
            // Force plain text representation
            return String(params.value).replace(/<[^>]*>/g, '');
          },
          cellStyle() {
            return { border: ' 0px white', fontSize: '14px' };
          }
        },
        {
          headerName: 'Store Goal',
          field: 'storegoalval',
          flex: 1,
          // width: 75,
          minWidth: 75,
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Prior 12 \nMonths',
          field: 'prior12Val',
          // flex: 1,
          // width: 130,
          minWidth: 60,
          width: 130,
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          valueFormatter: this.formatCellValuePriorMonth,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Prior 3 \nMonths',
          field: 'prior3Val',
          // flex: 1,
          // width: 130,
          width: 130,
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          valueFormatter: this.formatCellValuePriorMonth,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'right'
            };
          }
        },
        {
          headerName: 'Current Goal \nDate',
          field: 'currentgoaldate',
          minWidth: 60,
          width: 150,
          // width: 110,
          flex: 1,
          valueFormatter: this.formatCellValueDateAdv,
          filterParams: {
            valueFormatter: this.formatCellValueDateAdv
          },
          //
          // resizable: false,
          editable: false,
          type: 'editableColumn',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,
          cellStyle() {
            return {
              border: ' 0px white',
              fontSize: '14px',
              textAlign: 'left'
            };
          }
        },
        {
          headerName: 'Current Goal',
          field: 'advisorgoalval',
          flex: 1,
          // width: 110,
          minWidth: 100,
          //
          // resizable: false,
          // editable:
          //   typeof this.props.keycloak.realmAccess.roles != 'undefined' &&
          //   this.props.keycloak.realmAccess.roles.length >= 1 &&
          //   this.props.keycloak.realmAccess.roles.includes('client') === true
          //     ? false
          //     : true,
          editable: params => {
            // Disable editing if kpiTypeCode is 'B'
            if (params.data && params.data.kpiTypeCode === 'B') {
              return false;
            }

            // Check role access for other rows
            return (
              typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
              this.props.keycloak.realmAccess.roles.length >= 1 &&
              !this.props.keycloak.realmAccess.roles.includes('client')
            );
          },
          // type: 'editableColumn',
          valueParser: this.numberParser,
          cellEditor: 'numericCellEditor',
          suppressMenu: true,
          unSortIcon: false,
          suppressFilter: true,

          type: 'editableColumn',
          cellRenderer: 'cellRenderer',
          cellRendererParams: {
            technician: true,
            store: false,
            advisor: false,
            onEditStart: this.handleEditStart
          },
          cellStyle(params) {
            if (params.data == undefined) {
              return { display: 'none' };
            } else {
              return {
                border: ' 0px white',
                textAlign: 'center',
                fontSize: '14px'
              };
            }
          },
          cellClassRules: {
            'green-bg': params => {
              return params.data;
            }
          }
        }
      ]
    };
  }

  // eslint-disable-next-line react/sort-comp
  static defaultProps = {
    className: 'layout',
    cols: 12,
    rowHeight: 50,
    category: 'labor',
    reportType: 'workmix'
  };
  getCellClass = params => {
    if (params.value) {
      if (
        params.value != 'A' &&
        params.value != 'B' &&
        params.value != 'C' &&
        params.value != 'D' &&
        params.value != 'E' &&
        params.value != 'F' &&
        params.data.kpino != 'G'
      ) {
        return 'goal-data-cell';
      }
    }
  };
  getKPICellClass = params => {
    if (
      params.data.kpino == 'A' ||
      params.data.kpino == 'B' ||
      params.data.kpino == 'C' ||
      params.data.kpino == 'D' ||
      params.data.kpino == 'E' ||
      params.data.kpino == 'F' ||
      params.data.kpino == 'G'
    ) {
      return 'goal-title-cell';
    }
  };
  formatGoalName = params => {
    if (params.value != null && params.value != '') {
      console.log('====', ReactHtmlParser(params.value));
      return ReactHtmlParser(params.value);
    }
  };
  formatCellValueSchedule = params => {
    if (params.value != null && params.value != '') {
      if (params.value == 'store_goal') return 'Store';
      if (params.value == 'Both') return 'Both';
      else return 'Advisor';
    } else {
      return '';
    }
  };
  formatCellValueScheduleOn = params => {
    if (params.value != null && params.value != '') {
      return params.value.charAt(0).toUpperCase() + params.value.substr(1);
    } else {
      return '';
    }
  };
  getMonthtext = i => {
    if (i == 1 || i == 21 || i == 31) {
      return 'st of every month';
    }
    if (i == 2 || i == 22) {
      return 'nd of every month';
    }
    if (i == 3 || i == 23) {
      return 'rd of every month';
    } else return 'th of every month';
  };
  formatCellValueKPI = params => {
    if (params.value != null && params.value != '') {
      return params.value + ')';
    } else {
      return '';
    }
  };
  formatCellValuePriorMonth = params => {
    if (params.value != null && params.value != '') {
      //  if (params.data.goalname.includes('%')) return params.value + '%';
      if (
        this.containsPercentageInLastPortion(params.data.goalname) ||
        params.data.goalname.includes('Maintenance')
      )
        return params.value + '%';
      else return params.value;
    } else {
      return '';
    }
  };

  containsPercentageInLastPortion(str) {
    const segments = str.split('/');
    const lastSegment = segments[segments.length - 1].trim();
    return lastSegment.includes('%');
  }
  onDeleteEmail = deleteSet => {
    getOrSetKPIScorecardEmail('set', JSON.stringify(deleteSet), result => {
      if (
        result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
          .statelessDbdKpiScorecardKpiScorecardMailJobs
      ) {
        this.setState({ openDelete: false });
        this.setState({ successMsg: 'Email Recipient Deleted Successfully!' });

        this.setState({ openSnackbar: true });
        if (this.state.advisor) {
          setTimeout(() => {
            this.getEmailList(this.state.advisor);
          }, 500);
        } else {
          setTimeout(() => {
            this.getEmailList('store_goal');
          }, 1000);
        }
      }
    });
  };
  handleEditEmail = data => {
    // rowDataWithoutGrping
    console.log('email===', data);
    this.setState({ mailFrequency: data.mailFrequency });
    this.setState({ mailStatus: data.mailStatus });
    this.setState({ recipientId: data.recipientId });
    this.setState({ scheduledOn: data.scheduledOn });
    this.setState({ serviceAdvisor: data.serviceAdvisor });
    if (data.serviceAdvisor == 'Both') {
      let duplicaterow = this.state.rowDataWithoutGrping.filter(
        obj => obj.recipientId == data.recipientId
      );
      let index = duplicaterow.findIndex(obj => obj.serviceAdvisor == 'Both');
      duplicaterow[index].serviceAdvisor = 'store_goal';
      this.setState({ emailListDialog: duplicaterow });
      this.setState({ type: 'Both' });
    } else {
      this.setState({ emailListDialog: data });
      this.setState({ type: data.serviceAdvisor });
      if (this.state.advisor) {
        this.setState({ advisorSelected: true });
        this.setState({ selectedAdvisorId: this.state.advisor });
      }
    }

    this.setState({ openDialogue: true });
  };
  handleDeleteEmail = data => {
    var deleteSet = [];
    let index1 = 0;
    if (data.serviceAdvisor == 'Both') {
      let duplicaterow = this.state.rowDataWithoutGrping.filter(
        obj => obj.recipientId == data.recipientId
      );
      let index = duplicaterow.findIndex(obj => obj.serviceAdvisor == 'Both');
      duplicaterow[index].serviceAdvisor = 'store_goal';
      if (this.state.selectedDelete == 'Both') {
        duplicaterow.map(e => {
          deleteSet.push({
            recipientid: e.recipientId,
            mailfrequency: e.mailFrequency,
            scheduledon: e.scheduledOn,
            mailstatus: 'delete',
            id: e.id,
            serviceadvisor: e.serviceAdvisor,
            reporttype: 'advisor_individual_report'
          });
        });
      } else {
        if (this.state.selectedDelete == 'store_goal') {
          index1 = duplicaterow.findIndex(
            obj => obj.serviceAdvisor == 'store_goal'
          );
        } else {
          index1 = duplicaterow.findIndex(
            obj => obj.serviceAdvisor != 'store_goal'
          );
        }
        deleteSet.push({
          recipientid: duplicaterow[index1].recipientId,
          mailfrequency: duplicaterow[index1].mailFrequency,
          scheduledon: duplicaterow[index1].scheduledOn,
          mailstatus: 'delete',
          id: duplicaterow[index1].id,
          serviceadvisor: duplicaterow[index1].serviceAdvisor,
          reporttype: 'advisor_individual_report'
        });
      }
    } else {
      data.mailStatus = 'delete';
      deleteSet.push({
        recipientid: data.recipientId,
        mailfrequency: data.mailFrequency,
        scheduledon: data.scheduledOn,
        mailstatus: data.mailStatus,
        id: data.id,
        serviceadvisor: data.serviceAdvisor,
        reporttype: 'advisor_individual_report'
      });
    }
    this.onDeleteEmail(deleteSet);
  };
  handleEditEmailStore = data => {
    this.setState({ type: 'store_goal' });
    this.setState({ openDialogue: true });
    this.setState({ emailListDialog: data });
  };
  showDeleteModal = data => {
    this.setState({ type: 'store_goal' });
    this.setState({ openDelete: true });
    this.setState({ DataDeleteSet: data });
  };
  showDeleteModalAdv = data => {
    if (data.serviceAdvisor == 'Both') {
      this.setState({ type: 'Both' });
    } else {
      this.setState({ type: this.state.advisor });
    }
    this.setState({ openDelete: true });
    this.setState({ DataDeleteSet: data });
  };
  confirmDelete = () => {
    setTimeout(() => {
      this.handleDeleteEmail(this.state.DataDeleteSet);
    }, 500);
  };
  handleDeleteEmailStore = data => {
    var deleteSet = [];
    deleteSet.push({
      recipientid: data.recipientId,
      mailfrequency: data.mailFrequency,
      scheduledon: data.scheduledOn,
      mailstatus: 'delete',
      id: data.id
    });
    this.onDeleteEmail('store_goal', deleteSet);
  };
  formatCellValueKPI2 = params => {
    if (
      params.value != null &&
      params.value != '' &&
      params.node.childIndex == 0 &&
      params.node.parent.allChildrenCount === 1
    ) {
      return params.value + ')';
    } else if (
      params.node.childIndex === 0 &&
      (params.node.parent.allChildrenCount == 2 ||
        params.node.parent.allChildrenCount == 3)
    ) {
      return 'a)';
    } else if (params.node.childIndex == 1) {
      return 'b)';
    } else if (params.node.childIndex == 2) {
      return 'c)';
    }
  };

  formatCellValueDate = params => {
    if (
      params.value != null &&
      params.value != '' &&
      params.data.storegoalval != '0' &&
      params.storegoalval != '0.00'
    ) {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };
  formatCellValueDateAdv = params => {
    if (
      params.value != null &&
      params.value != '' &&
      params.data.advisorgoalval != '0' &&
      params.data.advisorgoalval != '0.00'
    ) {
      return moment(params.value).format('MM/DD/YY');
    } else {
      return '';
    }
  };

  numberParser(params) {
    if (isNaN(params.newValue)) {
      return '';
    } else {
      return Number(params.newValue);
    }
  }

  handleEditStart = values => {
    if (values.store == true) {
      console.log('store edited others need disable');
      this.setState({ storeSave: true });
      this.setState({ advisorSave: false });
      this.setState({ technicianSave: false });
      if (this.state.advisor != '') {
        this.getAdvisorData(this.state.advisor, 'reload');
        this.state.rawGridApiAdv &&
          this.state.rawGridApiAdv.setRowData(this.state.rowDataAdvOriginal);
        this.state.rawGridApiAdv && this.state.rawGridApiAdv.redrawRows();
      } else {
        this.setState({ AdvGridDisplay: false });
      }

      if (this.state.technician != '') {
        this.getTechnicianData(this.state.technician, 'reload');
        this.state.rawGridApiTech &&
          this.state.rawGridApiTech.setRowData(this.state.rowDataTechOriginal);
        this.state.rawGridApiTech && this.state.rawGridApiTech.redrawRows();
      } else {
        this.setState({ TechGridDisplay: false });
      }
    } else if (values.technician == true) {
      console.log('technician edited others need disable');
      this.setState({ storeSave: false });
      this.setState({ advisorSave: false });
      this.setState({ technicianSave: true });
      this.getAgGridData();
      this.state.rawGridApi &&
        this.state.rawGridApi.setRowData(this.state.rowDataOriginal);
      this.state.rawGridApi && this.state.rawGridApi.redrawRows();
      if (this.state.advisor != '') {
        this.getAdvisorData(this.state.advisor, 'reload');
        this.state.rawGridApiAdv &&
          this.state.rawGridApiAdv.setRowData(this.state.rowDataAdvOriginal);
        this.state.rawGridApiAdv && this.state.rawGridApiAdv.redrawRows();
      } else {
        this.setState({ AdvGridDisplay: false });
      }
    } else if (values.advisor == true) {
      console.log('advisor edited others need disable');
      this.setState({ storeSave: false });
      this.setState({ advisorSave: true });
      this.setState({ technicianSave: false });
      this.getAgGridData();
      this.state.rawGridApi &&
        this.state.rawGridApi.setRowData(this.state.rowDataOriginal);
      this.state.rawGridApi && this.state.rawGridApi.redrawRows();
      if (this.state.technician != '') {
        this.getTechnicianData(this.state.technician, 'reload');
        this.state.rawGridApiTech &&
          this.state.rawGridApiTech.setRowData(this.state.rowDataTechOriginal);
        this.state.rawGridApiTech && this.state.rawGridApiTech.redrawRows();
      } else {
        this.setState({ TechGridDisplay: false });
      }
    }
  };

  handleCheckboxChange = event => {
    this.setState({ checked: event.target.checked });
    this.setState({ type: this.state.advisor });
    if (this.state.rowDataEmailAdv.length > 0) {
      this.setState({ openPopup: true });
      if (!event.target.checked) {
        this.setState({ statusChange: 'inactive' });
        this.setState({ dataList: this.state.rowDataEmailAdv });
      } else {
        this.setState({ statusChange: 'active' });
        this.setState({ dataList: this.state.rowDataEmailAdv });
      }
    }
  };
  handleCheckboxChangeStore = event => {
    this.setState({ checkedStore: event.target.checked });
    this.setState({ type: 'store_goal' });

    if (this.state.rowDataEmail.length > 0) {
      this.setState({ openPopup: true });
      if (!event.target.checked) {
        this.setState({ statusChange: 'inactive' });
        this.setState({ dataList: this.state.rowDataEmail });
      } else {
        this.setState({ statusChange: 'active' });
        this.setState({ dataList: this.state.rowDataEmail });
      }
    }
  };

  onGridReady = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApi: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();

    this.getAgGridData();
  };
  onGridReadyAdv = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApiAdv: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();

    this.getAgGridData();
  };
  onGridReadyTech = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApiTech: params.api });
    this.setState({ gridApi: params });
    this.gridApi = params.api;
    this.gridApi.sizeColumnsToFit();

    this.getAgGridData();
  };
  onGridReadyEmail = params => {
    params.api.closeToolPanel();
    this.setState({ rawGridApiEmail: params.api });

    this.gridApi = params.api;
    params.api.resetRowHeights();
    // this.gridApi.sizeColumnsToFit();
  };

  getAgGridData() {
    var timeZone = getTimeZone();

    getKPIScorecardGoal(timeZone, result => {
      // this.setState({ isLoading: true });
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
          .statelessDbdKpiScorecardKpiScorecardGoals
      ) {
        this.setState({
          rowData:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        this.setState({
          rowDataOriginal:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        localStorage.setItem(
          'storeGoalList',
          JSON.stringify(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
          )
        );
        this.setState({
          rowDataPrev:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        this.setState({ isLoading: false });
      }
    });
    if (this.state.advisor == undefined) this.getEmailList('store_goal');
  }
  getAdvisorData = (advisor, type) => {
    var timeZone = getTimeZone();
    getKPIScorecardGoalAdvisor(advisor, timeZone, result => {
      this.setState({ isLoadingAdvisor: true });
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
          .statelessDbdKpiScorecardKpiScorecardGoals
      ) {
        this.getEmailList(advisor);
        this.setState({
          rowDataAdv:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        this.setState({
          rowDataAdvOriginal:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        localStorage.setItem(
          'advisorGoalList',
          JSON.stringify(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
          )
        );
        this.setState({
          rowDataPrevAdv:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        this.setState({ isLoadingAdvisor: false });
        if (type == '') {
          this.setState({ AdvGridDisplay: true });
          this.setState({ advisorChange: false });

          $('html, body').animate(
            {
              scrollTop: $(document).height() - $(window).height()
            },
            500
          );
        }
      }
    });
  };
  getTechnicianData = (technician, type) => {
    var timeZone = getTimeZone();
    getKPIScorecardGoalTechnician(technician, timeZone, result => {
      this.setState({ isLoadingTechnician: true });
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
          .statelessDbdKpiScorecardKpiScorecardGoals
      ) {
        this.getEmailList(technician);
        this.setState({
          rowDataTech:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        this.setState({
          rowDataTechOriginal:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });
        localStorage.setItem(
          'technicianGoalList',
          JSON.stringify(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
          )
        );
        this.setState({
          rowDataPrevTech:
            result.data.statelessDbdKpiScorecardGetKpiScorecardGoal
              .statelessDbdKpiScorecardKpiScorecardGoals
        });

        this.setState({ isLoadingTechnician: false });
        if (type == '') {
          this.setState({ TechGridDisplay: true });
          this.setState({ technicianChange: false });
          $('html, body').animate(
            {
              scrollTop: $(document).height() - $(window).height()
            },
            500
          );
        }
      }
    });
  };
  handleAdvisorChangeOver = () => {
    if (this.state.popOpened == true) {
      this.setState({ advisorChange: false });
    } else {
      this.setState({ advisorChange: true });
    }
  };

  popupOpened = () => {
    this.setState({ popOpened: true });
  };
  popupClosed = () => {
    this.setState({ popOpened: false });
  };
  handleAdvisorChangeOut = () => {
    this.setState({ advisorChange: false });
    // alert(this.state.advisorChange);
  };
  handleAdvisorChange = e => {
    this.setState({ personName: e.target.value });
    this.setState({ checked: false, rowDataEmailAdv: [] });
    this.setState({ editFormAdvisor: true });
    this.setState({ advisorChange: true });
    if (e.target.value != 'Choose Advisor') {
      this.setState({ isLoadingAdvisor: true });
      const advisor = e.target.value
        .split('[')[1]
        .split(']')[0]
        .toString();
      this.setState({ advisor: advisor });
      this.getAdvisorData(advisor, '');
    } else {
      this.setState({ AdvGridDisplay: false });
    }
  };
  handleTechnicianChangeOver = () => {
    if (this.state.popOpened == true) {
      this.setState({ technicianChange: false });
    } else {
      this.setState({ technicianChange: true });
    }
  };
  handleTechnicianChangeOut = () => {
    this.setState({ technicianChange: false });
  };
  handleTechnicianChange = e => {
    this.setState({ personNameTech: e.target.value });
    this.setState({ checkedTech: false, rowDataEmailTech: [] });
    this.setState({ editFormTechnician: true });
    this.setState({ technicianChange: true });
    if (e.target.value != 'Choose Technician') {
      this.setState({ isLoadingTechnician: true });
      const technician = e.target.value
        .split('[')[1]
        .split(']')[0]
        .toString();
      this.setState({ technician: technician });
      this.getTechnicianData(technician, '');
    } else {
      this.setState({ TechGridDisplay: false });
    }
  };
  handleFormEdit = () => {
    this.setState({ editForm: true });
    this.state.rawGridApi.stopEditing();
    if (this.state.advisor) {
      this.state.rawGridApiAdv.stopEditing();
    }
    if (this.state.technician) {
      this.state.rawGridApiTech.stopEditing();
    }

    setTimeout(() => {
      this.saveFormData(
        this.state.rowData,
        'store_goal',
        'advisor_individual_report'
      );
    }, 1000);
  };

  handleFormEditAdvisor = e => {
    this.state.rawGridApi.stopEditing();
    if (this.state.advisor) {
      this.state.rawGridApiAdv.stopEditing();
    }
    if (this.state.technician) {
      this.state.rawGridApiTech.stopEditing();
    }
    setTimeout(() => {
      this.saveFormData(
        this.state.rowDataAdv,
        this.state.advisor,
        'advisor_individual_report'
      );
    }, 1000);
  };
  handleFormEditTechnician = e => {
    this.state.rawGridApi.stopEditing();
    if (this.state.advisor) {
      this.state.rawGridApiAdv.stopEditing();
    }
    if (this.state.technician) {
      this.state.rawGridApiTech.stopEditing();
    }
    setTimeout(() => {
      this.saveFormDataTechnician(
        this.state.rowDataTech,
        this.state.technician,
        'tech_comparative_report'
      );
    }, 1000);
  };
  saveFormDataTechnician = (rowDataTech, technician, reportType) => {
    let rowData1 = [];
    var rowDataFiltered = [];
    let data1 = [];
    let nullString = false;
    let gridData = [];
    var rowDataPrevious = [];
    if (this.state.rowDataTech && technician == 'store_goal') {
      if (localStorage.getItem('storeGoalList')) {
        rowDataPrevious = JSON.parse(localStorage.getItem('storeGoalList')).map(
          value => ({
            goalname: value.goalname,
            goalval: value.storegoalval == '' ? 0 : value.storegoalval
          })
        );
      } else {
        rowDataPrevious = this.state.rowDataTech.map(value => ({
          goalname: value.goalname,
          goalval: value.storegoalval == '' ? 0 : value.storegoalval
        }));
      }
    } else if (localStorage.getItem('technicianGoalList')) {
      rowDataPrevious = JSON.parse(
        localStorage.getItem('technicianGoalList')
      ).map(value => ({
        goalname: value.goalname,
        goalval: value.advisorgoalval == '' ? 0 : value.advisorgoalval
      }));
    }

    if (technician == 'store_goal') {
      this.state.rawGridApi.forEachNode(node => {
        rowData1.push(node.data);
      });
      rowData1 = rowData1.filter(e => e != undefined);
      data1.push(rowData1);

      this.state.rawGridApi.setRowData(rowData1);

      rowDataFiltered = data1[0].map(value => ({
        goalname: value.goalname,
        goalval: value.storegoalval == '' ? 0 : value.storegoalval
      }));
    } else {
      this.state.rawGridApiTech.forEachNode(node => {
        rowData1.push(node.data);
      });
      rowData1 = rowData1.filter(e => e != undefined);
      data1.push(rowData1);
      this.state.rawGridApiTech.setRowData(rowData1);
      rowDataFiltered = data1[0].map(value => ({
        goalname: value.goalname,
        goalval: value.advisorgoalval == '' ? 0 : value.advisorgoalval
      }));
    }
    for (var key in rowDataFiltered) {
      if (rowDataFiltered[key].goalval === '') {
        nullString = true;
      }
    }
    let distinctArr = lodash.differenceWith(
      rowDataFiltered,
      rowDataPrevious,
      lodash.isEqual
    );
    if (this.state.isEdited == true || this.state.isEditedTech == true) {
      if (technician == 'store_goal') {
        this.setState({ isLoading: true });
      } else {
        this.setState({ isLoadingTechnician: true });
      }
      const jsonFormatted = JSON.stringify(distinctArr);
      const start = new Date();
      const client = makeApolloClient;
      client
        .mutate({
          mutation: UPDATE_KPI_SCORECARD_GOAL,
          variables: {
            pGoal: jsonFormatted,
            serviceadvisor: technician,
            reporttype: reportType,
            userid: localStorage.getItem('userID')
          }
        })
        .then(response => {
          if (
            response.data &&
            response.data.statelessDbdKpiScorecardInsertOrUpdateKpiScorecardGoal
          ) {
            const resultString =
              response.data
                .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardGoal.string;
            if (resultString === 'Success') {
              const spanAttribute = {
                pageUrl: '/ScoreCardGoalSetting',
                origin: '',
                event: 'Menu Load',
                is_from: 'UPDATE_KPI_SCORECARD_GOAL',
                value: new Date() - start,
                provenance: localStorage.getItem('provenance')
              };
              traceSpan('Menu Load', spanAttribute);
              if (technician == 'store_goal') {
                this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
                this.setState({ rowData: rowData1 });
                this.getAgGridData();
              } else {
                this.getTechnicianData(this.state.technician, '');
              }
              this.setState({ successMsg: 'Goals Updated Successfully!' });
              this.setState({ storeSave: false });
              this.setState({ advisorSave: false });
              this.setState({ technicianSave: false });
              if (this.state.technician) {
                this.getTechnicianData(this.state.technician, '');
              }
              this.setState({ isEditedTech: false });
              this.setState({ isEdited: false });
              this.setState({ openSnackbar: true });
            } else {
              this.setState({ goalFail: true });
              this.setState({ successMsg: resultString });
              this.setState({ openSnackbar: true });

              this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
              this.setState({ rowData: rowData1 });
              this.getAgGridData();
            }
          } else {
            console.error('Unexpected response format:', response);
          }
        });
    }
    this.setState({ isEdited: false });
  };
  saveFormData = (rowData, serviceadvisor, reportType) => {
    let rowData1 = [];
    var rowDataFiltered = [];
    let data1 = [];
    let nullString = false;
    let gridData = [];
    var rowDataPrevious = [];
    if (this.state.rowData && serviceadvisor == 'store_goal') {
      if (localStorage.getItem('storeGoalList')) {
        rowDataPrevious = JSON.parse(localStorage.getItem('storeGoalList')).map(
          value => ({
            goalname: value.goalname,
            goalval: value.storegoalval == '' ? 0 : value.storegoalval
          })
        );
      } else {
        rowDataPrevious = this.state.rowData.map(value => ({
          goalname: value.goalname,
          goalval: value.storegoalval == '' ? 0 : value.storegoalval
        }));
      }
    } else if (localStorage.getItem('advisorGoalList')) {
      rowDataPrevious = JSON.parse(localStorage.getItem('advisorGoalList')).map(
        value => ({
          goalname: value.goalname,
          goalval: value.advisorgoalval == '' ? 0 : value.advisorgoalval
        })
      );
    }

    if (serviceadvisor == 'store_goal') {
      //
      this.state.rawGridApi.forEachNode(node => {
        rowData1.push(node.data);
      });
      rowData1 = rowData1.filter(e => e != undefined);
      data1.push(rowData1);

      this.state.rawGridApi.setRowData(rowData1);

      rowDataFiltered = data1[0].map(value => ({
        goalname: value.goalname,
        goalval: value.storegoalval == '' ? 0 : value.storegoalval
      }));
    } else {
      this.state.rawGridApiAdv.forEachNode(node => {
        rowData1.push(node.data);
      });
      rowData1 = rowData1.filter(e => e != undefined);
      data1.push(rowData1);
      this.state.rawGridApiAdv.setRowData(rowData1); // Refresh grid
      rowDataFiltered = data1[0].map(value => ({
        goalname: value.goalname,
        goalval: value.advisorgoalval == '' ? 0 : value.advisorgoalval
      }));
    }
    for (var key in rowDataFiltered) {
      if (rowDataFiltered[key].goalval === '') {
        nullString = true;
      }
    }
    let distinctArr = lodash.differenceWith(
      rowDataFiltered,
      rowDataPrevious,
      lodash.isEqual
    );
    if (this.state.isEdited == true || this.state.isEditedAdv == true) {
      if (serviceadvisor == 'store_goal') {
        this.setState({ isLoading: true });
      } else {
        this.setState({ isLoadingAdvisor: true });
      }
      // if (!nullString) {
      //const jsonFormatted = JSON.stringify(rowDataFiltered);
      const jsonFormatted = JSON.stringify(distinctArr);
      const start = new Date();
      const client = makeApolloClient;
      client
        .mutate({
          mutation: UPDATE_KPI_SCORECARD_GOAL,
          variables: {
            pGoal: jsonFormatted,
            serviceadvisor: serviceadvisor,
            reporttype: reportType,
            userid: localStorage.getItem('userID')
          }
        })
        // .then(result => {
        //   const spanAttribute = {
        //     pageUrl: '/ScoreCardGoalSetting',
        //     origin: '',
        //     event: 'Menu Load',
        //     is_from: 'UPDATE_KPI_SCORECARD_GOAL',
        //     value: new Date() - start
        //   };
        //   traceSpan('Menu Load', spanAttribute);
        //   if (serviceadvisor == 'store_goal') {
        //     this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
        //     this.setState({ rowData: rowData1 });
        //     this.getAgGridData();
        //   } else {
        //     this.getAdvisorData(this.state.advisor);
        //   }
        //   this.setState({ successMsg: 'Goals Updated Successfully!' });

        //   this.setState({ openSnackbar: true });

        //   // this.setState({ isLoadingAdvisor: false });
        // });

        .then(response => {
          if (
            response.data &&
            response.data.statelessDbdKpiScorecardInsertOrUpdateKpiScorecardGoal
          ) {
            const resultString =
              response.data
                .statelessDbdKpiScorecardInsertOrUpdateKpiScorecardGoal.string;
            if (resultString === 'Success') {
              const spanAttribute = {
                pageUrl: '/ScoreCardGoalSetting',
                origin: '',
                event: 'Menu Load',
                is_from: 'UPDATE_KPI_SCORECARD_GOAL',
                value: new Date() - start,
                provenance: localStorage.getItem('provenance')
              };
              traceSpan('Menu Load', spanAttribute);
              if (serviceadvisor == 'store_goal') {
                this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
                this.setState({ rowData: rowData1 });
                this.getAgGridData();
              } else {
                this.getAdvisorData(this.state.advisor, '');
              }
              this.setState({ successMsg: 'Goals Updated Successfully!' });
              this.setState({ storeSave: false });
              this.setState({ advisorSave: false });
              this.setState({ technicianSave: false });
              if (this.state.advisor) {
                this.getAdvisorData(this.state.advisor, '');
              }
              if (this.state.technician) {
                this.getTechnicianData(this.state.technician, '');
              }
              this.setState({ isEditedAdv: false });
              this.setState({ isEdited: false });
              this.setState({ openSnackbar: true });
            } else {
              this.setState({ goalFail: true });
              this.setState({ successMsg: resultString });
              this.setState({ openSnackbar: true });

              this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
              this.setState({ rowData: rowData1 });
              this.getAgGridData();
            }
          } else {
            console.error('Unexpected response format:', response);
          }
        });
      // }
      // else {
      //   this.setState({ successMsg: 'Entered Goal value is not valid!' });
      //   this.setState({ goalFail: true });
      //   this.setState({ openSnackbar: true });

      //   if (serviceadvisor == 'store_goal') {
      //     this.setState({ rowData: rowData1 });
      //     this.state.rawGridApi.setRowData(this.state.rowData); // Refresh grid
      //     this.getAgGridData();
      //   } else {
      //     this.getAdvisorData(this.state.advisor);
      //   }
      // }
    }
    this.setState({ isEdited: false });
  };
  getEmailList = goalType => {
    let emailId = [];
    let emailIdAdv = [];
    let emaile = [];
    emaile.push({
      recipientid: null,
      mailfrequency: null,
      scheduledon: null,
      mailstatus: null,
      id: null,
      serviceadvisor: goalType,
      reporttype: 'advisor_individual_report'
    });
    /* getOrSetKPIScorecardEmail('get', JSON.stringify(emaile), result => {
      if (
        result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
          .statelessDbdKpiScorecardKpiScorecardMailJobs
      ) {
        var dataArr =
          result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
            .statelessDbdKpiScorecardKpiScorecardMailJobs;

        if (goalType == 'store_goal') {
          if (
            dataArr[0].recipientId != null &&
            dataArr[0].mailStatus != 'delete'
          ) {
            this.setState({
              rowDataEmailAdv:
                result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
                  .statelessDbdKpiScorecardKpiScorecardMailJobs
            });
          } else {
            this.setState({
              rowDataEmailAdv: []
            });
          }
          dataArr.map(e => {
            emailId.push(e.recipientId);
          });
          this.setState({ emailId: emailId });
        } else {
          if (
            dataArr[0].recipientId != null &&
            dataArr[0].mailStatus != 'delete'
          ) {
            this.setState({
              rowDataEmailAdv: this.combinedItems(dataArr)
            });
          } else {
            this.setState({
              rowDataEmailAdv: []
            });
          }

          dataArr.map(e => {
            emailIdAdv.push(e.recipientId);
          });

          this.setState({ emailIdAdv: emailIdAdv });
        }
      }
    });*/
  };
  handleSnackbarClose = () => {
    this.setState({ openSnackbar: false });
    setTimeout(() => {
      this.setState({ goalFail: false });
    }, 2000);
  };
  handleChange = newValue => {
    this.setState({ value: newValue });
  };
  handleChangeStore = newValue => {
    this.setState({ valueStore: newValue });
  };

  changeValue = value => {
    this.setState({
      emailValue: value
    });
  };
  handleSaveEmail = () => {
    var defaultList = [];

    this.getEmailList(this.state.advisor);
    defaultList.push({
      recipientid: '',
      mailfrequency: 'daily',
      scheduledon: 'daily'
    });
    this.setState({ emailListDialog: defaultList });
    this.setState({ flgInsert: true });
    if (this.state.advisor) {
      this.setState({ statusInsert: true });
    }

    if (this.state.advisor) {
      this.setState({ type: this.state.advisor });
    } else {
      this.setState({ type: 'store_goal' });
    }

    this.setState({ openDialogue: true });
  };
  handleSaveEmailStore = () => {
    this.getEmailList('store_goal');

    this.setState({ openDialogue: true });
  };

  hidesnackbar = () => {
    this.setState({ open: false });
  };
  onCellValueChanged = params => {
    if (params.newValue != params.oldValue) {
      this.setState({ isEdited: true });
    }
  };
  onCellValueChangedAdv = params => {
    if (params.newValue != params.oldValue) {
      this.setState({ isEditedAdv: true });
    }
  };
  onCellValueChangedTech = params => {
    if (params.newValue != params.oldValue) {
      this.setState({ isEditedTech: true });
    }
  };
  combinedItems = (arr = []) => {
    let duplicateArr = [];

    const res = arr.reduce((acc, obj) => {
      let found = false;
      for (let i = 0; i < acc.length; i++) {
        if (acc[i].recipientId === obj.recipientId) {
          duplicateArr.push(acc[i]);
          duplicateArr.push(obj);
          this.setState({ rowDataWithoutGrping: duplicateArr });

          acc[i].serviceAdvisor = 'Both';
          found = true;
          acc[i].count++;
        }
      }
      if (!found) {
        obj.count = 1;
        acc.push(obj);
      }
      return acc;
    }, []);

    return res;
  };
  handleclick = () => {
    let Toggle =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.toggle
        ? this.props.history.location.state.toggle
        : 'MTD';
    let EndDate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.end_date
        ? this.props.history.location.state.end_date
        : '';
    let StarteDate =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.start_date
        ? this.props.history.location.state.start_date
        : '';
    this.props.history.push({
      pathname: '/KpiReport',
      toggle: Toggle,
      end_date: EndDate,
      start_date: StarteDate
    });
  };
  handleClose = () => {
    this.setState({ openDialogue: false });
    this.setState({ emailListDialog: [] });
    this.setState({ statusInsert: false });
    this.setState({ flgInsert: false });
    this.setState({ advisorSelected: false });
    this.setState({ selectedAdvisorId: '' });
    if (this.state.advisor) {
      setTimeout(() => {
        this.getEmailList(this.state.advisor);
      }, 1000);
    } else {
      setTimeout(() => {
        this.getEmailList('store_goal');
      }, 800);
    }
  };
  // getRowStyle = params => {
  //   if (
  //     typeof params.data != 'undefined' &&
  //     params.data.kpiTypeCode == params.data.kpino
  //   ) {
  //     return {
  //       display: 'none'
  //     };
  //   }
  // };
  handleClosePopup = () => {
    this.setState({ openPopup: false });

    setTimeout(() => {
      this.getEmailList('store_goal');
    }, 100);
  };
  handleCloseDelete = () => {
    this.setState({ openDelete: false });
  };
  handleClosePopupCancel = () => {
    this.setState({ openPopup: false });
    if (this.state.type == 'store_goal') {
      if (this.state.checkedStore) {
        this.setState({ checkedStore: false });
      } else {
        this.setState({ checkedStore: true });
      }
    } else {
      if (this.state.checked) {
        this.setState({ checked: false });
      } else {
        this.setState({ checked: true });
      }
    }
  };
  handleSelected = selected => {
    //console.log('selected1', selected);
    this.setState({ selectedDelete: selected });
  };
  handleGoalTab = (goal, event) => {
    if (event) {
      event.preventDefault();
    }
    this.setState({ advisor: '' });
    this.setState({ technician: '' });
    this.setState({ tabSelect: goal });
    if (goal == 1) {
      // this.setState({ personName: [] });
      this.setState({ personNameTech: [] });
      this.setState({ checked: false, rowDataEmailAdv: [] });
      this.setState({ editFormAdvisor: true });

      // this.setState({
      //   rowDataAdv: []
      // });
      this.setState({
        rowDataTech: []
      });
      localStorage.setItem('advisorGoalList', []);
      this.setState({
        rowDataPrevAdv: []
      });

      this.setState({
        AdvGridDisplay: false
      });
      this.setState({ advisorChange: false });
      this.setState({ isLoadingAdvisor: false });
      // $('html, body').animate(
      //   {
      //     scrollTop: $(document).height() - $(window).height()
      //   },
      //   500
      // );

      // if (e.target.value != 'Choose Advisor') {
      //   this.setState({ isLoadingAdvisor: true });
      //   const advisor = e.target.value
      //     .split('[')[1]
      //     .split(']')[0]
      //     .toString();
      //   this.setState({ advisor: advisor });
      //   this.getAdvisorData(advisor);
      // } else {
      this.setState({ AdvGridDisplay: false });
      // }
    } else {
      this.setState({ personName: [] });
      // this.setState({ personNameTech: [] });
      this.setState({ checkedTech: false, rowDataEmailTech: [] });
      this.setState({ editFormTechnician: true });

      this.setState({
        rowDataAdv: []
      });
      localStorage.setItem('technicianGoalList', []);
      this.setState({
        rowDataPrevTech: []
      });

      this.setState({
        TechGridDisplay: false
      });
      this.setState({ technicianChange: false });
      this.setState({ isLoadingTechnician: false });

      // if (e.target.value != 'Choose Technician') {
      //   this.setState({ isLoadingTechnician: true });
      //   const technician = e.target.value
      //     .split('[')[1]
      //     .split(']')[0]
      //     .toString();
      //   this.setState({ technician: technician });
      //   this.getTechnicianData(technician);
      // } else {
      this.setState({ TechGridDisplay: false });
      // }
    }
  };
  render() {
    const { classes } = this.props;

    let Parent =
      this.props.history &&
      this.props.history.location &&
      this.props.history.location.state &&
      this.props.history.location.state.parent
        ? this.props.history.location.state.parent
        : '';
    console.log(
      'kpiAdvisor=======',
      this.state.kpiAdvisor,
      '===',
      this.state.setTechnicianName,
      '==',
      this.state.setAdvisorName,
      '-p===',
      this.state.rowDataTech
    );
    return (
      <div>
        <Paper
          square
          style={{
            margin: 8

            //  height: '40px',
          }}
        >
          <span style={{ display: 'flex' }}>
            {Parent == 'KpiReport' ? (
              <Button
                variant="contained"
                //className={'bck-btn'}
                className={clsx('bck-btn', classes.btnback)}
                onClick={this.handleclick}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button>
            ) : null}
            <Typography
              style={{ padding: 8 }}
              variant="h4"
              className={clsx(classes.mainLabel)}
            >
              {'Goal Settings - KPI Report'}
            </Typography>
          </span>

          <Divider></Divider>

          <Snackbar
            open={this.state.open}
            autoHideDuration={6000}
            anchorOrigin={{
              vertical: 'center',
              horizontal: 'center'
            }}
            style={{ marginTop: '-56px' }}
            onClose={this.hidesnackbar}
          >
            {/* <Collapse in={this.state.open}> */}
            <Alert
              action={
                <IconButton
                  aria-label="close"
                  color="inherit"
                  size="small"
                  onClick={() => {
                    this.setState({ open: false });
                  }}
                >
                  <CloseIcon fontSize="inherit" />
                </IconButton>
              }
              style={{ margin: '10px 20px' }}
            >
              {this.state.alertMsg}
            </Alert>
          </Snackbar>

          {/* </Collapse> */}
          <Grid container spacing={3}>
            <Grid item xs={12} className={'gridgoalsettings'}>
              <div className={'goaltitle'}>
                <div className={'goaltitlesub'}>
                  <Typography variant="h5" className={clsx(classes.mainLabel)}>
                    Store Goals
                  </Typography>
                </div>
              </div>
              <div
                className={clsx(
                  'goaltitlesub',
                  'goaleditbutton',
                  classes.buttonStore
                )}
                style={{
                  display: this.state.isLoading == true ? 'none' : 'block'
                }}
              >
                <div
                  style={{
                    display:
                      typeof this.props.keycloak.realmAccess.roles !=
                        'undefined' &&
                      this.props.keycloak.realmAccess.roles.length >= 1 &&
                      this.props.keycloak.realmAccess.roles.includes(
                        'client'
                      ) === true
                        ? 'none'
                        : 'block'
                  }}
                >
                  <Tooltip
                    title="Save Store Goals"
                    onClick={this.handleFormEdit}
                    placement="top"
                  >
                    <Button
                      variant="contained"
                      id="storeSave"
                      // disabled={
                      //   this.state.isEditedAdv === true ||
                      //   this.state.isEditedTech === true
                      // }
                      disabled={this.state.storeSave === false}
                      className={clsx(
                        classes.edit,
                        'reset-btn',
                        'btngoalSettings'
                      )}
                      onClick={this.handleFormEdit}
                    >
                      Save Goals
                    </Button>
                  </Tooltip>
                </div>
                {/* )} */}
              </div>
              {this.state.isLoading === true ? (
                <div>
                  <Box style={{ padding: 25 }}>
                    <LinearProgress color="secondary" />
                    <Typography
                      variant="h6"
                      align="center"
                      style={{ padding: 25 }}
                      color="primary"
                    >
                      Processing...
                    </Typography>
                  </Box>
                </div>
              ) : (
                <div>
                  <div className={'goalsettingsection'}>
                    <div style={{ display: 'flex' }}>
                      <div
                        id="data-tab-goal"
                        className={clsx('ag-theme-balham', 'gridgoalsettings')}
                        style={{
                          height: '484px',
                          width: '1035px',
                          display:
                            this.state.isLoading == true ? 'none' : 'block'
                        }}
                      >
                        <AgGridReact
                          className="ag-theme-balham"
                          style={{
                            height: '400px',
                            width: '100%'
                          }}
                          autoGroupColumnDef={this.state.autoGroupColumnDef}
                          getChartToolbarItems={this.getChartToolbarItems}
                          modules={AllModules}
                          columnDefs={this.state.columnDefs}
                          defaultColDef={this.state.defaultColDef}
                          onGridReady={this.onGridReady}
                          onCellValueChanged={this.onCellValueChanged}
                          frameworkComponents={this.state.frameworkComponents}
                          suppressAggFuncInHeader
                          rowData={this.state.rowData}
                          excelStyles={this.state.excelStyles}
                          tooltipShowDelay={0}
                          // suppressHorizontalScroll={true}
                          suppressClickEdit={this.state.editForm ? true : false}
                          onRowClicked={this.onRowClicked}
                          enableRangeSelection={true}
                          singleClickEdit={true}
                          animateRows={true}
                          enableCharts={true}
                          floatingFilter={false}
                          onCellEditRequest={this.onCellEditRequest}
                          groupDisplayType={'singleColumn'}
                          groupRemoveSingleChildren={true}
                          groupDefaultExpanded={-1}
                          showOpenedGroup={false}
                          suppressDragLeaveHidesColumns={true}
                          getRowStyle={this.getRowStyle}
                          /// suppressRowClickSelection={true}
                          suppressContextMenu={true}
                          suppressKeyboardEvent={params => {
                            const keysToSuppress = ['Enter', 'Tab', 'Escape'];
                            return keysToSuppress.includes(params.event.key);
                          }}
                        />
                      </div>
                      {/* <div
                        className={'emailsave'}
                        style={{
                          display:
                            typeof this.props.keycloak.realmAccess.roles !=
                              'undefined' &&
                            this.props.keycloak.realmAccess.roles.length >= 1 &&
                            this.props.keycloak.realmAccess.roles.includes(
                              'client'
                            ) === true
                              ? 'none'
                              : 'block'
                        }}
                      >
                        <div className={'emailtitlesec'}>
                          <div
                            style={{
                              display: !this.state.openPopup ? 'block' : 'none'
                            }}
                          >
                            <div
                              style={{
                                display: 'flex'
                              }}
                            >
                              <Typography
                                variant="body1"
                                style={{
                                  color: 'rgb(0, 61, 107)',
                                  fontSize: 11,
                                  fontWeight: 'bold'
                                }}
                              >
                                Current <br /> <u> Recipients</u>
                              </Typography>
                              <div className={classes.recepBtn}>
                                <Tooltip
                                  title="Add Email Recipient"
                                  onClick={this.handleSaveEmail}
                                  placement="top"
                                >
                                  <Button
                                    variant="contained"
                                    id="btnRecipient"
                                    className={clsx(
                                      'reset-btn',
                                      'btnSaveEmail'
                                    )}
                                    onClick={this.handleSaveEmail}
                                    disabled={
                                      typeof this.props.keycloak.realmAccess
                                        .roles != 'undefined' &&
                                      this.props.keycloak.realmAccess.roles
                                        .length >= 1 &&
                                      this.props.keycloak.realmAccess.roles.includes(
                                        'client'
                                      ) === true
                                        ? true
                                        : false
                                    }
                                  >
                                    Add Recipient
                                  </Button>
                                </Tooltip>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div
                          style={{
                            display: !this.state.openPopup ? 'block' : 'none'
                          }}
                          className={classes.gridemailContainer}
                        >
                          <div
                            id="data-tab-goal"
                            className={clsx(
                              classes.gridemail,
                              'ag-theme-balham',
                              'gridgoalsettings'
                            )}
                          >
                            <AgGridReact
                              className="ag-theme-balham"
                              style={{
                                height: '300px',
                                width: '600px'
                              }}
                              columnDefs={this.state.columnDefsEmailAdv}
                              defaultColDef={this.state.defaultColDef}
                              onGridReady={this.onGridReadyEmail}
                              rowData={this.state.rowDataEmailAdv}
                              suppressHorizontalScroll={false}
                              floatingFilter={false}
                              suppressDragLeaveHidesColumns={true}
                            />
                          </div>
                        </div>
                      </div> */}
                    </div>
                  </div>
                </div>
              )}
            </Grid>
            <Grid item xs={12} className={'gridgoalsettings'}>
              <div className={'goaltitle'}>
                <div className={'goaltitlesub'}>
                  <Typography
                    variant="h5"
                    className={clsx(
                      classes.mainLabel,
                      this.state.tabSelect === 1 ? 'active-tab-goal' : ''
                    )}
                    onClick={e => this.handleGoalTab(1, e)}
                    style={{ cursor: 'pointer' }}
                  >
                    Advisor Goals
                  </Typography>
                  <Typography
                    variant="h5"
                    className={clsx(
                      classes.mainLabel,
                      this.state.tabSelect === 2 ? 'active-tab-goal' : ''
                    )}
                    onClick={e => this.handleGoalTab(2, e)}
                    style={{ cursor: 'pointer' }}
                  >
                    Technician Goals
                  </Typography>
                </div>
              </div>
              {this.state.tabSelect == 1 ? (
                <>
                  <div className={'advgoalcontainer'}>
                    {/* {this.state.isLoading === false ? ( */}
                    <FormControl
                      variant="standard"
                      sx={{ m: 1, minWidth: 120 }}
                      className={classes.formControl}
                    >
                      <InputLabel id="demo-simple-select-filled-label">
                        Choose Advisor
                      </InputLabel>
                      <Tooltip
                        title={
                          this.state.personName &&
                          this.state.personName != '' &&
                          this.state.advisorChange == true
                            ? this.state.personName
                            : ''
                        }
                      >
                        <Select
                          labelId="demo-simple-select-standard-label"
                          id="demo-simple-select-standard"
                          label="Select Advisor"
                          name="Select Advisor"
                          value={this.state.personName}
                          onChange={this.handleAdvisorChange}
                          onMouseOver={this.handleAdvisorChangeOver}
                          onOpen={this.popupOpened}
                          onClose={this.popupClosed}
                          style={{
                            width: '350px'
                          }}
                        >
                          {this.state.setAdvisorName
                            ? this.state.setAdvisorName.map(e => (
                                <MenuItem
                                  onMouseOver={this.handleAdvisorChangeOut}
                                  value={e.split('-status-')[0]}
                                  style={{
                                    backgroundColor:
                                      e.split('-status-')[1] == 0
                                        ? Dealer === 'Armatus'
                                          ? '#ddeaf4'
                                          : '#F4E1E7'
                                        : '',
                                    color:
                                      e.split('-status-')[1] == 0
                                        ? Dealer === 'Armatus'
                                          ? '#969592'
                                          : '#F4E1E7'
                                        : ''
                                  }}
                                >
                                  {e.split('-status-')[0]}{' '}
                                </MenuItem>
                              ))
                            : null}
                        </Select>
                      </Tooltip>
                    </FormControl>
                    {/* ) : (
                   ''
                 )} */}
                    <Divider className={'sep1'}></Divider>
                    <div
                      className={'goaleditbuttonAdv'}
                      style={{
                        display: this.state.isLoadingAdvisor ? 'none' : 'block',
                        marginRight: '20px'
                      }}
                    >
                      <div
                        style={{
                          display:
                            typeof this.props.keycloak.realmAccess.roles !=
                              'undefined' &&
                            this.props.keycloak.realmAccess.roles.length >= 1 &&
                            this.props.keycloak.realmAccess.roles.includes(
                              'client'
                            ) === true
                              ? 'none'
                              : 'block'
                        }}
                      >
                        {this.state.personName != '' && (
                          <Tooltip
                            title="Save Advisor Goals"
                            onClick={this.handleFormEditAdvisor}
                            placement="top"
                          >
                            <Button
                              variant="contained"
                              id="advisorSave"
                              // disabled={this.state.isEdited === true}
                              disabled={this.state.advisorSave === false}
                              className={clsx(
                                classes.edit,
                                'reset-btn',
                                'btngoalSettings'
                              )}
                              onClick={this.handleFormEditAdvisor}
                            >
                              Save Goals
                            </Button>
                          </Tooltip>
                        )}
                      </div>
                    </div>
                  </div>
                  {this.state.isLoadingAdvisor === true ? (
                    <div>
                      <Box style={{ padding: 25 }}>
                        <LinearProgress color="secondary" />
                        <Typography
                          variant="h6"
                          align="center"
                          style={{ padding: 25 }}
                          color="primary"
                        >
                          Processing...
                        </Typography>
                      </Box>
                    </div>
                  ) : (
                    <div style={{ display: 'flex' }}>
                      <div
                        id="data-tab-goal-adv"
                        className={clsx(
                          'ag-theme-balham',
                          'gridgoalsettings',
                          'gridadvgoal'
                        )}
                        style={{
                          display: 'block',
                          width: '1035px',
                          height: '484px'
                        }}
                      >
                        <AgGridReact
                          className="ag-theme-balham"
                          style={{
                            height: '400px',
                            width: '100%'
                          }}
                          autoGroupColumnDef={this.state.autoGroupColumnDefAdv}
                          getChartToolbarItems={this.getChartToolbarItems}
                          modules={AllModules}
                          columnDefs={this.state.columnDefsAdvisor}
                          defaultColDef={this.state.defaultColDef}
                          onGridReady={this.onGridReadyAdv}
                          suppressAggFuncInHeader
                          frameworkComponents={this.state.frameworkComponents}
                          rowData={this.state.rowDataAdv}
                          excelStyles={this.state.excelStyles}
                          onCellValueChanged={this.onCellValueChangedAdv}
                          tooltipShowDelay={0}
                          editType={this.state.editType}
                          // suppressHorizontalScroll={true}
                          suppressClickEdit={
                            this.state.editFormAdvisor ? true : false
                          }
                          onCellClicked={this.onCellClicked}
                          enableRangeSelection={true}
                          animateRows={true}
                          singleClickEdit={true}
                          enableCharts={true}
                          floatingFilter={false}
                          suppressRowClickSelection={true}
                          onCellEditRequest={this.onCellEditRequest}
                          groupDisplayType={'singleColumn'}
                          groupRemoveSingleChildren={true}
                          groupDefaultExpanded={-1}
                          showOpenedGroup={false}
                          suppressDragLeaveHidesColumns={true}
                          suppressContextMenu={true}
                          suppressKeyboardEvent={params => {
                            const keysToSuppress = ['Enter', 'Tab', 'Escape'];
                            return keysToSuppress.includes(params.event.key);
                          }}
                        />
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <>
                  <div className={'advgoalcontainer'}>
                    {/* {this.state.isLoading === false ? ( */}
                    <FormControl
                      variant="standard"
                      sx={{ m: 1, minWidth: 120 }}
                      className={classes.formControl}
                    >
                      <InputLabel id="demo-simple-select-filled-label">
                        Choose Technician
                      </InputLabel>
                      <Tooltip
                        title={
                          this.state.personNameTech &&
                          this.state.personNameTech != '' &&
                          this.state.technicianChange == true
                            ? this.state.personNameTech
                            : ''
                        }
                      >
                        <Select
                          labelId="demo-simple-select-standard-label"
                          id="demo-simple-select-standard"
                          label="Select Technician"
                          name="Select Technician"
                          value={this.state.personNameTech}
                          onChange={this.handleTechnicianChange}
                          onMouseOver={this.handleTechnicianChangeOver}
                          onOpen={this.popupOpened}
                          onClose={this.popupClosed}
                          //onMouseOut={this.handleTechnicianChangeOut}
                          style={{
                            width: '350px'
                          }}
                        >
                          {this.state.setTechnicianName
                            ? this.state.setTechnicianName.map(e => (
                                <MenuItem
                                  value={e.split('-status-')[0]}
                                  style={{
                                    backgroundColor:
                                      e.split('-status-')[1] == 0
                                        ? Dealer === 'Armatus'
                                          ? '#ddeaf4'
                                          : '#F4E1E7'
                                        : '',
                                    color:
                                      e.split('-status-')[1] == 0
                                        ? Dealer === 'Armatus'
                                          ? '#969592'
                                          : '#F4E1E7'
                                        : ''
                                  }}
                                >
                                  {e.split('-status-')[0]}{' '}
                                </MenuItem>
                              ))
                            : null}
                        </Select>
                      </Tooltip>
                    </FormControl>
                    {/* ) : (
                   ''
                 )} */}
                    <Divider className={'sep1'}></Divider>
                    <div
                      className={'goaleditbuttonAdv'}
                      style={{
                        display: this.state.isLoadingTechnician
                          ? 'none'
                          : 'block',
                        marginRight: '20px'
                      }}
                    >
                      <div
                        style={{
                          display:
                            typeof this.props.keycloak.realmAccess.roles !=
                              'undefined' &&
                            this.props.keycloak.realmAccess.roles.length >= 1 &&
                            this.props.keycloak.realmAccess.roles.includes(
                              'client'
                            ) === true
                              ? 'none'
                              : 'block'
                        }}
                      >
                        {this.state.personNameTech != '' && (
                          <Tooltip
                            title="Save Technician Goals"
                            onClick={this.handleFormEditTechnician}
                            placement="top"
                          >
                            <Button
                              variant="contained"
                              id="technicianSave"
                              //disabled={this.state.isEdited === true}
                              disabled={this.state.technicianSave === false}
                              className={clsx(
                                classes.edit,
                                'reset-btn',
                                'btngoalSettings'
                              )}
                              onClick={this.handleFormEditTechnician}
                            >
                              Save Goals
                            </Button>
                          </Tooltip>
                        )}
                      </div>
                    </div>
                  </div>
                  {this.state.isLoadingTechnician === true ? (
                    <div>
                      <Box style={{ padding: 25 }}>
                        <LinearProgress color="secondary" />
                        <Typography
                          variant="h6"
                          align="center"
                          style={{ padding: 25 }}
                          color="primary"
                        >
                          Processing...
                        </Typography>
                      </Box>
                    </div>
                  ) : (
                    <div style={{ display: 'flex' }}>
                      <div
                        id="data-tab-goal-adv"
                        className={clsx(
                          'ag-theme-balham',
                          'gridgoalsettings',
                          'gridadvgoal'
                        )}
                        style={{
                          display: 'block',
                          width: '1035px',
                          height: '484px'
                        }}
                      >
                        <AgGridReact
                          className="ag-theme-balham"
                          style={{
                            height: '400px',
                            width: '100%'
                          }}
                          autoGroupColumnDef={this.state.autoGroupColumnDefTech}
                          getChartToolbarItems={this.getChartToolbarItems}
                          modules={AllModules}
                          columnDefs={this.state.columnDefsTechnician}
                          defaultColDef={this.state.defaultColDef}
                          onGridReady={this.onGridReadyTech}
                          suppressAggFuncInHeader
                          frameworkComponents={this.state.frameworkComponents}
                          rowData={this.state.rowDataTech}
                          excelStyles={this.state.excelStyles}
                          onCellValueChanged={this.onCellValueChangedTech}
                          tooltipShowDelay={0}
                          editType={this.state.editType}
                          // suppressHorizontalScroll={true}
                          suppressClickEdit={
                            this.state.editFormAdvisor ? true : false
                          }
                          onCellClicked={this.onCellClicked}
                          enableRangeSelection={true}
                          animateRows={true}
                          singleClickEdit={true}
                          enableCharts={true}
                          floatingFilter={false}
                          suppressRowClickSelection={true}
                          onCellEditRequest={this.onCellEditRequest}
                          groupDisplayType={'singleColumn'}
                          groupRemoveSingleChildren={true}
                          groupDefaultExpanded={-1}
                          showOpenedGroup={false}
                          suppressDragLeaveHidesColumns={true}
                          suppressContextMenu={true}
                          suppressKeyboardEvent={params => {
                            const keysToSuppress = ['Enter', 'Tab', 'Escape'];
                            return keysToSuppress.includes(params.event.key);
                          }}
                        />
                      </div>
                    </div>
                  )}
                </>
              )}
            </Grid>
          </Grid>
        </Paper>
        <SuccessSnackbar
          onClose={this.handleSnackbarClose}
          open={this.state.openSnackbar}
          msg={this.state.successMsg}
          goalFail={this.state.goalFail}
        />
        <EmailDialog
          open={this.state.openDialogue}
          emailListDialog={this.state.emailListDialog}
          handlePopupClose={this.handleClose}
          active={this.state.active}
          type={this.state.type}
          emailId={this.state.emailId}
          emailIdAdv={this.state.emailIdAdv}
          statusInsert={this.state.statusInsert}
          advisorSelected={this.state.advisorSelected}
          selectedAdvisorId={this.state.selectedAdvisorId}
          flgInsert={this.state.flgInsert}
          mailFrequency={this.state.mailFrequency}
          mailStatus={this.state.mailStatus}
          recipientId={this.state.recipientId}
          scheduledOn={this.state.scheduledOn}
          serviceAdvisor={this.state.serviceAdvisor}
        ></EmailDialog>
        <InactiveEmailDialog
          openPopup={this.state.openPopup}
          handlePopupClose={this.handleClosePopup}
          dataList={this.state.dataList}
          statusChange={this.state.statusChange}
          handleClosePopupCancel={this.handleClosePopupCancel}
          type={this.state.type}
        ></InactiveEmailDialog>
        <DeleteDialog
          openPopup={this.state.openDelete}
          handlePopupClose={this.handleCloseDelete}
          handleDeleteEmailRow={this.confirmDelete}
          handleSelected={this.handleSelected.bind(this)}
          type={this.state.type}
        ></DeleteDialog>
      </div>
    );
  }
}

ScoreCardGoalSetting.propTypes = {
  keycloak: PropTypes.any,
  handleReload: PropTypes.func,
  handleRefresh: PropTypes.func,
  setRefreshStatus: PropTypes.func,
  setRefreshErrorStatus: PropTypes.func
};

const styles = theme => ({
  formControl: {
    minWidth: 170,
    marginTop: -19
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    //marginTop: 10,
    display: 'flex',
    color: 'rgb(0, 61, 107)'
  },
  back: {
    marginRight: 10,
    float: 'right'
  },
  gridemailContainer: {
    '@media (max-width: 1920px)': {
      width: '355px'
    },
    '@media (min-width: 1920px)': {
      width: '372px'
    }
  },
  gridemail: {
    marginLeft: '4px',

    '@media (max-width: 2560px)': {
      width: '356px',
      height: '486px',
      marginTop: '14px'
    },
    '@media (min-width: 2304px)': {
      width: '354px',
      height: '484px',
      marginTop: '16px',
      marginLeft: '4px'
    },
    '@media (max-width: 1920px)': {
      width: 'auto',
      height: '486px',
      marginTop: '15px'
    },
    '@media (max-width: 1440px)': {
      width: '285px',
      height: '486px',
      marginTop: '14px'
    }
    // '@media (max-width: 1820px)': {
    //   width: '354px',
    //   height: '484px',
    //   marginTop: '14px'
    // }
  },
  buttonStore: {
    marginTop: -6,
    '@media (min-width: 2304px)': {
      width: '1020px'
    },
    '@media (max-width: 2560px)': {
      width: '1020px'
    },
    '@media (max-width: 2304px)': {
      width: '1004px'
    },
    '@media (max-width: 1920px)': {
      width: '1020px'
    },
    '@media (max-width: 1440px)': {
      width: '990px'
    }
  },
  recepBtn: {
    '@media (min-width: 2304px)': {
      width: '230px'
    },
    '@media (max-width: 2560px)': {
      width: '230px'
    },
    '@media (max-width: 2304px)': {
      width: '99px'
    },
    '@media (max-width: 1920px)': {
      width: '153px'
    },
    '@media (max-width: 1440px)': {
      width: '165px'
    }
  },
  btnback: {
    marginTop: '8px !important'
  }
});

export default withStyles(styles)(withKeycloak(ScoreCardGoalSetting));
