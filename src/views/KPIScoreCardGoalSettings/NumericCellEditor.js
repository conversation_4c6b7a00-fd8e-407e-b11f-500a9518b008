import React, { Component, createRef } from 'react';

const KEY_BACKSPACE = 'Backspace';
const KEY_DELETE = 'Delete';
const KEY_ENTER = 'Enter';
const KEY_TAB = 'Tab';

export default class NumericEditor extends Component {
  constructor(props) {
    super(props);

    this.inputRef = createRef(null);

    this.cancelBeforeStart =
      this.props.charPress && '1234567890'.indexOf(this.props.charPress) < 0;

    this.state = this.createInitialState(props);

    this.onKeyDown = this.onKeyDown.bind(this);
    this.handleChange = this.handleChange.bind(this);
  }

  componentDidMount() {
    this.setCaret();
  }

  render() {
    var regExfixedValue = /^\d{0,5}(\.\d{0,5})?$/;
    var decVal = /^(\d*\.{0,1}\d{0,2})$/;

    return (
      <input
        ref={this.inputRef}
        className={'simple-input-editor'}
        value={this.state.value === 0 ? '' : this.state.value}
        onKeyDown={this.onKeyDown}
        onChange={e => {
          let inputValue = e.target.value;
          // If the user enters just '.', convert it to '0.0'
          if (inputValue === '.') {
            inputValue = '0.0';
          }
          // If the input is '0.0' and a new number is added, replace the last zero with that number
          if (this.state.value === '0.0' && inputValue.length > 3) {
            inputValue = `0.${inputValue[3]}`;
          }
          if (
            inputValue === '' ||
            (regExfixedValue.test(inputValue) && decVal.test(inputValue))
          ) {
            this.setState({
              value: inputValue
            });
          }
        }}
      />
    );
  }

  /* Component Editor Lifecycle methods */
  // the final value to send to the grid, on completion of editing
  getValue() {
    return this.state.value;
  }

  // Gets called once before editing starts, to give editor a chance to
  // cancel the editing before it even starts.
  isCancelBeforeStart() {
    return this.cancelBeforeStart;
  }

  // Gets called once when editing is finished (eg if Enter is pressed).
  // If you return true, then the result of the edit will be ignored.
  isCancelAfterEnd() {
    // will reject the number if it greater than 1,000,000
    // not very practical, but demonstrates the method.
    return this.state.value > 1000000;
  }

  /* Utility methods */
  createInitialState(props) {
    let startValue;

    if (props.eventKey === KEY_BACKSPACE || props.eventKey === KEY_DELETE) {
      // if backspace or delete pressed, we clear the cell
      startValue = '';
    } else if (props.charPress) {
      // if a letter was pressed, we start with the letter
      startValue = props.charPress;
    } else {
      // otherwise we start with the current value
      startValue = props.value;
    }

    return {
      value: startValue
    };
  }

  onKeyDown(event) {
    if (this.isLeftOrRight(event) || this.deleteOrBackspace(event)) {
      event.stopPropagation();
      return;
    }

    if (
      !this.finishedEditingPressed(event) &&
      !this.isKeyPressedNumeric(event)
    ) {
      if (event.preventDefault) event.preventDefault();
    }
  }

  isLeftOrRight(event) {
    return ['ArrowLeft', 'ArrowRight'].indexOf(event.key) > -1;
  }

  handleChange(event) {
    this.setState({ value: event.target.value });
  }

  // isCharNumeric(charStr) {
  //   return !!/\d/.test(charStr);
  // }
  isCharNumeric(charStr) {
    return !!/^\d*\.?\d*$/.test(charStr);
  }

  isKeyPressedNumeric(event) {
    const charStr = event.key;
    return this.isCharNumeric(charStr);
  }

  deleteOrBackspace(event) {
    return [KEY_DELETE, KEY_BACKSPACE].indexOf(event.key) > -1;
  }

  finishedEditingPressed(event) {
    const key = event.key;
    return key === KEY_ENTER || key === KEY_TAB;
  }

  setCaret() {
    // https://github.com/facebook/react/issues/7835#issuecomment-395504863
    setTimeout(() => {
      const currentInput = this.inputRef.current;
      currentInput.focus();
    });
  }
}
