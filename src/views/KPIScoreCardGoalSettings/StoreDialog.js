import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField,
  FormLabel,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress
} from '@material-ui/core';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import $, { param } from 'jquery';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import {
  getOrSetKPIScorecardEmail,
  getGroups,
  getUsersAssignedGrps
} from 'src/utils/hasuraServices';

import DeleteIcon from '@material-ui/icons/DeleteOutline';
import _ from 'lodash';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {},
  smallRadioButton: {
    '& svg': {
      width: '0.7em',
      height: '0.7em'
    },
    paddingLeft: 0,
    paddingRight: 0,
    height: 18,
    marginLeft: -2,
    backgroundColor: 'transparent !important',
    width: 19,
    '@media (max-width: 1440px)': {
      width: '16px !important',
      marginLeft: -2
    }
  },
  listItemText: {
    color: 'rgb(0, 61, 107)',
    fontSize: 14,
    fontFamily: ['Roboto', 'Helvetica', 'Arial', 'sans - serif'].join(','),
    padding: '7px 10px !important',
    marginBottom: -8
  },
  loaderStore: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: '20% !important'
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function StoreDialog({ openPopup, handlePopupClose, type, userEmail }) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(openPopup);
  const [selected, setSelected] = useState('Both');
  const [userName, setUserName] = React.useState('');
  const [groupsAvailable, setGroupsAvailable] = React.useState([]);
  const [groupsAssigned, setGroupsAssigned] = React.useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    setOpenDialog(openPopup);
    var username = userEmail;
    setUserName(username);
    if (username) {
      setGroupsAssigned([]);
      setLoading(true);
      getGroupUserAssigned(username);
    }
  }, [openPopup]);
  const handleClose = () => {
    setOpenDialog(false);
    handlePopupClose();
  };
  const getGroupUserAssigned = username => {
    var grpNames = [];
    var grpwithId = [];
    getUsersAssignedGrps(username, result => {
      if (
        result &&
        result.data &&
        result.data.statelessKeycloakServiceGetAssignedGroupsOfUsers != '[null]'
      ) {
        var roData = JSON.parse(
          result.data.statelessKeycloakServiceGetAssignedGroupsOfUsers
        );
        const parsed = JSON.parse(roData);
        parsed.map(item => {
          grpNames.push(item.path);
        });
        for (let index = 0; index < grpNames.length; index++) {
          let splitValue = grpNames[index].split('/');
          grpwithId.push(splitValue[1]);
        }
        const splitStore = grpwithId.map(item => {
          if (typeof item === 'string' && item.includes('-')) {
            return item
              .split('-')
              .slice(1)
              .join('-');
          }
          return item;
        });
        setLoading(false);
        setGroupsAssigned(splitStore);
      } else {
        setLoading(false);
        setGroupsAssigned([]);
      }
    });
  };

  const handleChange = ev => {
    setSelected(ev.target.value);
  };
  const customList = (title, items) => {
    return (
      <div>
        {items.map((item, index) => {
          return (
            <List component="nav" style={{ paddingTop: 1, paddingBottom: 1 }}>
              <ListItem
                // button
                style={{ paddingTop: 1, paddingBottom: 1, marginBottom: -10 }}
              >
                <ListItemText
                  primaryTypographyProps={{ className: classes.listItemText }}
                  primary={item}
                />
              </ListItem>
            </List>
          );
        })}
      </div>
    );
  };
  return (
    <div>
      <Dialog
        open={openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          style: {
            width: '500px',
            marginLeft: '20px',
            marginTop: '28px',
            height: 350
          }
        }}
      >
        <DialogTitle id="alert-dialog-title">
          {/* Stores */}
          <Typography
            variant="h5"
            color="primary"
            style={{
              textTransform: 'none'
            }}
          >
            Stores
          </Typography>
        </DialogTitle>
        <DialogContent>
          {loading ? (
            <span className={classes.loaderStore}>
              <CircularProgress size={30} />
            </span>
          ) : (
            <div>
              {groupsAssigned.length === 0 ? (
                <div className={'divNoData'}>No Stores Assigned</div>
              ) : (
                customList('title', groupsAssigned)
              )}
              {/* {groupsAssigned.length > 0 && customList('title', groupsAssigned)} */}
            </div>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default StoreDialog;
