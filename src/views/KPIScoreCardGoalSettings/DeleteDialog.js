import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField,
  FormLabel
} from '@material-ui/core';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import $, { param } from 'jquery';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import { getOrSetKPIScorecardEmail } from 'src/utils/hasuraServices';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import _ from 'lodash';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({
  root: {},
  smallRadioButton: {
    '& svg': {
      width: '0.7em',
      height: '0.7em'
    },
    paddingLeft: 0,
    paddingRight: 0,
    height: 18,
    marginLeft: -2,
    backgroundColor: 'transparent !important',
    width: 19,
    '@media (max-width: 1440px)': {
      width: '16px !important',
      marginLeft: -2
    }
  }
}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function DeleteDialog({
  openPopup,
  handlePopupClose,
  handleDeleteEmailRow,
  type,
  handleSelected
}) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(openPopup);
  const [selected, setSelected] = useState('Both');
  var displaymsg;
  useEffect(() => {
    setOpenDialog(openPopup);
  }, [openPopup]);
  const handleClose = () => {
    setOpenDialog(false);
    handlePopupClose();
  };

  if (type == 'User') {
    displaymsg =
      'Are you sure, you want to permanently delete the selected user?';
  } else {
    displaymsg = 'Are you sure, you want to remove the email recipient?';
  }
  const handleDelete = () => {
    handleSelected(selected);
    handleDeleteEmailRow();
  };
  const handleChange = ev => {
    setSelected(ev.target.value);
  };
  return (
    <div>
      <Dialog
        open={openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {type == 'User' ? 'Remove the User? ' : 'Remove email recipient?'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {type == 'Both' ? (
              <div>
                <FormLabel id="demo-radio-buttons-group-label">
                  Select report type you want to delete
                </FormLabel>
                <RadioGroup
                  row
                  aria-labelledby="demo-radio-buttons-group-label"
                  defaultValue="Both"
                  onChange={handleChange}
                  name="radio-buttons-group"
                  style={{ marginLeft: '137px', marginTop: '8px' }}
                  value={selected}
                >
                  <FormControlLabel
                    value="store_goal"
                    control={
                      <Radio
                        className={classes.smallRadioButton}
                        size="small"
                      />
                    }
                    label="Store"
                  />
                  <FormControlLabel
                    value="advisor"
                    control={
                      <Radio
                        className={classes.smallRadioButton}
                        size="small"
                      />
                    }
                    label="Advisor"
                  />
                  <FormControlLabel
                    value="Both"
                    control={
                      <Radio
                        className={classes.smallRadioButton}
                        size="small"
                      />
                    }
                    label="Both"
                  />
                </RadioGroup>
              </div>
            ) : (
              displaymsg
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleDelete} autoFocus>
            Ok
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default DeleteDialog;
