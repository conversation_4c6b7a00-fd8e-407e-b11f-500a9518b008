import React, { useRef, useState, useEffect } from 'react';
import Box from '@material-ui/core/Box';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import {
  Button,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  Collapse,
  MenuItem,
  TextField
} from '@material-ui/core';
import $, { param } from 'jquery';
import Autocomplete from '@material-ui/lab/Autocomplete';
import Alert from '@material-ui/lab/Alert';
import { useDispatch, useSelector } from 'react-redux';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import DialogTitle from '@material-ui/core/DialogTitle';
import { AgGridReact } from '@ag-grid-community/react';
import Fade from '@material-ui/core/Fade';
import Slide from '@material-ui/core/Slide';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import 'src/styles.css';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import { getOrSetKPIScorecardEmail } from 'src/utils/hasuraServices';
import DeleteIcon from '@material-ui/icons/DeleteOutline';
import _ from 'lodash';
var lodash = require('lodash');

const useStyles = makeStyles(theme => ({}));
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});
function InactiveEmailDialog({
  openPopup,
  handlePopupClose,
  dataList,
  statusChange,
  handleClosePopupCancel,
  type
}) {
  const classes = useStyles();
  const session = useSelector(state => state.session);
  const [openDialog, setOpenDialog] = useState(openPopup);

  useEffect(() => {
    setOpenDialog(openPopup);
  }, [openPopup]);
  const handleClose = () => {
    setOpenDialog(false);
    handleClosePopupCancel();
  };
  var status = statusChange == 'active' ? 'activate' : 'inactivate';
  var displaymsg =
    'Are you sure, you want to ' + status + ' all the email recipients?';
  const handleSave = () => {
    var rowDataEmail = [];
    var currentStatus = statusChange;
    dataList.map(function(x) {
      x.mailStatus = currentStatus;
      return x;
    });
    dataList.map(arr =>
      rowDataEmail.push({
        recipientid: arr.recipientId,
        mailfrequency: arr.mailFrequency,
        scheduledon: arr.scheduledOn,
        mailstatus: arr.mailStatus,
        id: arr.id
      })
    );

    console.log('dataList', rowDataEmail);
    getOrSetKPIScorecardEmail(
      'set',
      type,
      JSON.stringify(rowDataEmail),
      result => {
        if (
          result.data.statelessDbdKpiScorecardGetorsetKpiScorecardMailJobs
            .statelessDbdKpiScorecardKpiScorecardMailJobs
        ) {
          setOpenDialog(false);
          handlePopupClose();
        }
      }
    );
  };

  return (
    <div>
      <Dialog
        open={openDialog}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {status.charAt(0).toUpperCase() +
            status.substr(1) +
            ' email recipients?'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {displaymsg}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} autoFocus>
            Ok
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}

export default InactiveEmailDialog;
