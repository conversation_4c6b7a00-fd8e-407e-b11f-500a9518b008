import React, { useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import Page from 'src/components/Page';
import ScoreCardGoalSetting from './ScoreCardGoalSetting';
import { setRefreshStatus, setReloadStatus } from 'src/actions';
import { withKeycloak } from '@react-keycloak/web';
import { useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import { Redirect } from 'react-router-dom';
import { setNavItems, setMenuSelected } from 'src/actions';
function ScoreCardGoalSettings(keycloak) {
  const history = useHistory();
  const session = useSelector(state => state.session);
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(setMenuSelected('Goal Settings \n KPI Report'));
    dispatch(setNavItems(['Reference / Setups']));
  }, []);
  return (
    <Page title="ScoreCardGoal">
      {' '}
      {JSON.parse(localStorage.getItem('selectedStoreId')).length > 1 ? (
        <Redirect to="/errors/error-404" />
      ) : (
        <ScoreCardGoalSetting history={history} session={session} />
      )}
    </Page>
  );
}

export default withKeycloak(ScoreCardGoalSettings);
