import React, { Component } from 'react';
import EditIcon from '@material-ui/icons/Edit';
import { Tooltip } from '@material-ui/core';
import { withKeycloak } from '@react-keycloak/web';

class CellRenderer extends Component {
  render() {
    return (
      <span
        style={{
          display: 'flex',
          justifyContent: 'space-between', // Ensures space between the value and the edit icon
          alignItems: 'center' // Vertically center the content
        }}
      >
        <span>
          {this.props.value == '0' || this.props.value == 0
            ? ''
            : this.props.value}
        </span>
        {typeof this.props.keycloak.realmAccess.roles !== 'undefined' &&
        this.props.keycloak.realmAccess.roles.includes('client') != true ? (
          <Tooltip
            title={
              this.props.technician == true &&
              this.props.data.kpiTypeCode == 'B'
                ? ''
                : 'Edit'
            }
          >
            <button
              style={{
                cursor:
                  this.props.technician == true &&
                  this.props.data.kpiTypeCode == 'B'
                    ? 'default'
                    : 'pointer',
                height: '32px',
                backgroundColor: 'transparent',
                border: 'none',
                display:
                  this.props.data.kpiTypeCode == this.props.data.kpino
                    ? 'none'
                    : 'block'
              }}
              onClick={() => this.onButtonClicked(this.props)}
            >
              <EditIcon
                htmlColor={
                  this.props.technician == true &&
                  this.props.data.kpiTypeCode == 'B'
                    ? '#38416373'
                    : '#003d6b'
                }
                style={{ width: 14 }}
              />
            </button>
          </Tooltip>
        ) : (
          ''
        )}
      </span>
    );
  }

  onButtonClicked(values) {
    console.log('vvvvvvvvvvvvv---', values);
    if (this.props.onEditStart) {
      this.props.onEditStart(this.props);
    }
    // start editing this cell. see the docs on the params that this method takes
    this.props.api.startEditingCell({
      rowIndex: this.props.rowIndex,
      colKey: this.props.column.getId()
    });
  }
}

export default withKeycloak(CellRenderer);
