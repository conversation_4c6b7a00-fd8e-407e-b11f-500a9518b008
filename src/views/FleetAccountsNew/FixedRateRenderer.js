import React, { Component } from 'react';
import { FormControl, TextField } from '@material-ui/core';
import { withStyles } from '@material-ui/styles';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import InputAdornment from '@material-ui/core/InputAdornment';
import DateRangePicker from 'react-bootstrap-daterangepicker';

import 'bootstrap-daterangepicker/daterangepicker.css';
import clsx from 'clsx';
import moment from 'moment';
import 'src/input-style.css';
import { TransferWithinAStationOutlined } from '@material-ui/icons';

class FixedRateRenderer extends Component {
  constructor(props) {
    // console.log('propssssss++++++++++++----', props);
    super(props);
    this.state = {
      isFixedRateChecked: false,
      isValid:
        this.props.data.partsmarkup &&
        this.props.data.partsmarkup != null &&
        this.props.data.partsmarkup != 'N/A'
          ? this.props.data.partsmarkup.split(' ')[0] == 'Cost' ||
            this.props.data.partsmarkup.split(' ')[0] == 'List'
            ? true
            : false
          : true,
      partsMarkup:
        this.props.data.partsmarkup &&
        this.props.data.partsmarkup != null &&
        this.props.data.partsmarkup != 'N/A'
          ? this.props.data.partsmarkup.split(' ')[0]
          : 'Cost',

      laborfixedVal:
        this.props.data.laborFixedRate == 1
          ? this.props.data.laborFixedratevalue
          : '',
      laborfixedDate:
        this.props.data.laborFixedratedate != null &&
        this.props.data.laborFixedRate == 1
          ? this.props.data.laborFixedratedate
          : new Date(),
      partsmarkup:
        this.props.data.partsmarkup && this.props.data.partsmarkup != null
          ? this.props.data.partsmarkup.split(' ')[1]
          : '',
      partsfixedDate:
        this.props.data.partsFixedRate == 1 &&
        this.props.data.partsFixedratedate != null
          ? this.props.data.partsFixedratedate
          : new Date()
    };
    this.checkedHandler = this.checkedHandler.bind(this);
    this.partsCheckedHandler = this.partsCheckedHandler.bind(this);
    this.handleCallback = this.handleCallback.bind(this);
    this.handleFixedRateChange = this.handleFixedRateChange.bind(this);
    this.handleMarkupChange = this.handleMarkupChange.bind(this);

    //alert(this.state.partsMarkup);
  }
  checkedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'laborFixedRate') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
    if (this.props.data.laborFixedRate == 1 && checked == true) {
      this.props.context.componentParent.onLaborFixedRateChanged(
        this.props.data.laborFixedratevalue,
        this.state.laborfixedVal
      );
      this.props.api.refreshCells({
        columns: ['laborFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });

      // this.props.context.componentParent.onFixedDateChanged(
      //   this.props.data.fixedRateDate,
      //   this.props.data.fixedRateDate == null || this.props.fixedRateDate == ''
      //     ? moment(new Date()).format('MM/DD/YY')
      //     : this.props.data.fixedRateDate
      // );
      // this.props.context.componentParent.onFixedRateChanged(
      //   this.props.data.fixedRateValue,
      //   this.props.data.fixedRateValue
      // );
    } else {
      this.props.context.componentParent.onLaborFixedRateChanged(
        this.props.data.laborFixedratevalue,
        this.props.data.laborFixedratevalue
      );
      this.props.api.refreshCells({
        columns: ['laborFixedRate'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  partsCheckedHandler(event) {
    let checked = event.target.checked;
    let colId = this.props.column.colId;

    if (this.props.column.colId == 'partsmarkup') {
      this.props.node.setDataValue(colId, checked == true ? 1 : 0);
    }
    if (checked == true) {
      this.props.api.refreshCells({
        columns: ['partsmarkup'],
        rowNodes: [this.props.node],
        force: true
      });

      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsmarkup,
        'Cost'
      );
    } else {
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsmarkup,
        this.props.data.partsmarkup
      );

      this.props.api.refreshCells({
        columns: ['partsmarkup'],
        rowNodes: [this.props.node],
        force: true
      });
    }
  }
  handleCallback = (start, end, label) => {
    let colId = this.props.column.colId;
    this.setState({
      fixedDate: start.format('MM/DD/YY')
    });

    if (this.props.column.colId == 'laborFixedRate') {
      if (this.props.data.laborFixedRate == 1) {
        this.props.context.componentParent.onLaborFixedDateChanged(
          this.props.data.laborFixedratedate,
          start.format('YYYY-MM-DD')
        );
      } else {
        this.props.context.componentParent.onLaborFixedDateChanged(
          this.props.data.laborFixedratedate,
          null
        );
      }
    } else {
      if (this.props.data.partsFixedRate == 1) {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedratedate,
          start.format('YYYY-MM-DD')
        );
      } else {
        this.props.context.componentParent.onPartsFixedDateChanged(
          this.props.data.partsFixedratedate,
          null
        );
      }
    }
  };
  handleMarkupChange(event) {
    this.setState({
      partsMarkup: event.target.value
    });

    // if (this.props.data.partsFixedRate == 1) {
    if (event.target.value == 'Cost' || event.target.value == 'List') {
      this.setState({
        partsmarkup: ''
      });
      this.setState({
        isValid: true
      });
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsmarkup,
        event.target.value == 'N/A' ? 'Cost' : event.target.value
      );
    } else {
      this.setState({
        isValid: false
      });
      var val =
        this.state.partsmarkup != null && this.state.partsmarkup != ''
          ? this.state.partsmarkup
          : '';
      this.props.context.componentParent.onPartsFixedRateChanged(
        this.props.data.partsmarkup,
        (event.target.value == 'N/A' ? 'Cost' : event.target.value) + ' ' + val
      );
    }
    // } else {
    //   this.props.context.componentParent.onPartsFixedRateChanged(
    //     this.props.data.partsFixedratevalue,
    //     null
    //   );
    // }
    // if (this.props.data.partsFixedRate == 1) {
    //   this.props.context.componentParent.onPartsFixedDateChanged(
    //     this.props.data.partsFixedratedate,
    //     this.props.context.componentParent.state.partsFixedDateNew != ''
    //       ? this.props.context.componentParent.state.partsFixedDateNew
    //       : this.props.data.partsFixedratedate == null ||
    //         this.props.partsFixedratedate == ''
    //       ? moment(new Date()).format('MM/DD/YY')
    //       : this.props.data.partsFixedratedate
    //   );
    // }
  }
  handleFixedRateChange(event) {
    let colId = this.props.column.colId;

    this.setState({
      fixedValue: event.target.value
    });

    if (this.props.column.colId == 'laborFixedRate') {
      if (this.props.data.laborFixedRate == 1) {
        this.props.context.componentParent.onLaborFixedRateChanged(
          this.props.data.laborFixedratevalue,
          event.target.value
        );
      } else {
        this.props.context.componentParent.onLaborFixedRateChanged(
          this.props.data.laborFixedratevalue,
          ''
        );
      }
      if (this.props.data.laborFixedRate == 1) {
        this.props.context.componentParent.onLaborFixedDateChanged(
          this.props.data.laborFixedratedate,
          this.props.context.componentParent.state.laborFixedDateNew != '' &&
            this.props.context.componentParent.state.laborFixedDateNew != null
            ? this.props.context.componentParent.state.laborFixedDateNew
            : this.props.data.laborFixedratedate == null ||
              this.props.laborFixedratedate == ''
            ? moment(new Date()).format('MM/DD/YY')
            : this.props.data.laborFixedratedate
        );
      }
    } else {
      if (this.props.data.partsFixedRate == 1) {
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsFixedratevalue,
          (this.state.partsMarkup == 'N/A' ? 'Cost' : this.state.partsMarkup) +
            ' ' +
            (this.state.partsfixedValue == 0 ? '' : this.state.partsfixedValue)
        );
      } else {
        this.props.context.componentParent.onPartsFixedRateChanged(
          this.props.data.partsmarkup,
          (this.state.partsMarkup == 'N/A' ? 'Cost' : this.state.partsMarkup) +
            ' ' +
            (this.state.partsmarkup == 0 ? '' : this.state.partsmarkup)
        );
      }
    }
  }
  handleKeyDown = e => {
    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
      e.stopPropagation(); // Prevent other handlers from interfering
    }
  };
  render() {
    const { classes } = this.props;

    var regEx = /^[+-]?([0-9]+\.?[0-9]*|\.[0-9]+)$/;
    var regExParts = /^\d{0,3}(\.\d{0,3})?$/;
    var decVal = /^(\d*\.{0,1}\d{0,2}$)/;
    return this.props.colDef.field == 'partsFixedRate' ||
      this.props.colDef.field == 'partsmarkup' ? (
      <div
        style={{
          display: 'inline-flex',
          gap: 3,
          marginTop:
            this.props.context.componentParent.state.editedRowId != null
              ? '-2px'
              : '1px',
          alignItems: 'center'
        }}
      >
        {/* <input
          type="checkbox"
          title="Parts Markup"
          className="partsmarkup"
          disabled={
            this.props.context.componentParent.state.editedRowId == null
              ? true
              : this.props.context.componentParent.state.editedRowId !=
                this.props.rowIndex
              ? true
              : this.props.data.gridExcluded == 1
              ? true
              : false
          }
          onClick={this.partsCheckedHandler}
          checked={this.props.data.partsFixedRate == 0 ? false : true}
        /> */}
        <select
          disabled={
            this.props.context.componentParent.state.editedRowId != null
              ? false
              : 'true'
          }
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 1
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
          onChange={e => this.handleMarkupChange(e)}
          value={this.state.partsMarkup}
          className={clsx(classes.formControlSelect, 'partsFixedRateSelect')}
          name="parts-markups"
          id="parts-markups"
        >
          <option value="Cost">Cost</option>
          <option value="Cost-">Cost-</option>
          <option value="Cost+">Cost+</option>
          <option value="Cost%">Cost%</option>
          <option value="List">List</option>
          <option value="List-">List-</option>
          <option value="List+">List+</option>
          <option value="List%">List%</option>
        </select>

        <FormControl
          variant="outlined"
          disabled={
            this.props.context.componentParent.state.editedRowId != null &&
            !this.state.isValid
              ? false
              : 'true'
          }
          className="partsmarkup"
          style={{
            display:
              this.props.context.componentParent.state.editedRowId != null &&
              this.props.data.gridExcluded != 1
                ? this.props.context.componentParent.state.editedRowId ==
                  this.props.rowIndex
                  ? 'block'
                  : 'none'
                : 'none'
          }}
        >
          <OutlinedInput
            autoFocus
            className={clsx(classes.formControl, 'partsmarkup')}
            classes={{ input: classes.adorment }}
            title="Parts Fixed Rate(%)"
            id="outlined-adornment-weight"
            value={this.state.partsmarkup}
            error={
              this.props.context.componentParent.state.partsfixedRateError ==
              null
                ? false
                : this.props.context.componentParent.state
                    .partsfixedRateError == this.props.rowIndex
                ? true
                : this.state.partsMarkup != 'Cost' &&
                  this.state.partsMarkup != 'List' &&
                  this.state.partsmarkup == ''
                ? true
                : false
            }
            // onKeyPress={event => {
            //   if (!/[0-9]|\./.test(event.key)) {
            //     event.preventDefault();
            //   }
            // }}
            onBlur={this.handleFixedRateChange}
            onKeyDown={this.handleKeyDown}
            onChange={e => {
              const value = e.target.value;
              const regExParts = /^\d{0,3}(\.\d{0,3})?$/;
              const decVal = /^\d*(\.\d{0,2})?$/;

              // Validate the input value using regular expressions
              if (
                value === '' ||
                (regExParts.test(value) && decVal.test(value))
              ) {
                // If the value is valid, update the state
                this.setState({ partsmarkup: value });
              }
            }}
            // onChange={this.handleFixedRateChange}
            endAdornment={
              <InputAdornment
                classes={{ input: classes.adorment }}
                position="end"
                disableTypography={true}
              >
                %
              </InputAdornment>
            }
            aria-describedby="outlined-weight-helper-text"
            inputProps={{
              'aria-label': 'Fixed Rate Value'
            }}
            labelWidth={0}
          />
        </FormControl>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                  this.props.rowIndex
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.partsmarkup != null &&
          this.props.data.partsmarkup != '' &&
          this.props.data.partsmarkup != 'N/A' &&
          this.props.data.partsmarkup != 'Cost' &&
          this.props.data.partsmarkup != 'List'
            ? this.props.data.partsmarkup + '%'
            : this.props.data.partsmarkup}
        </span>
        <span
          style={{
            display:
              this.props.context.componentParent.state.editedRowId == null &&
              this.props.data.partsFixedRate == 1
                ? 'block'
                : this.props.context.componentParent.state.editedRowId !=
                    this.props.rowIndex && this.props.data.partsFixedRate == 1
                ? 'block'
                : 'none'
          }}
        >
          {this.props.data.partsFixedratedate != null &&
            '[' +
              moment(new Date(this.props.data.partsFixedratedate)).format(
                'MM/DD/YY'
              ) +
              ']'}
        </span>
      </div>
    ) : (
      ''
    );
  }
}
const styles = theme => ({
  formControl: {
    height: 24,
    width: 60,
    backgroundColor: '#fff',
    marginTop: 3,
    paddingLeft: '8px !important',
    fontSize: 12
  },
  adorment: {
    fontSize: '12px !important',
    paddingLeft: '0px !important'
  },
  fixeddatepicker: {
    height: '24px',
    width: '75px',
    border: '1px solid #c0c0c0',
    borderRadius: '4px',
    boxSizing: 'border-box',
    paddingLeft: '5px',
    fontSize: 12
  },
  formControlDate: {
    height: 24,
    width: 80,
    marginTop: 1,
    marginBottom: 1,
    paddingLeft: 0,
    fontSize: 12
  },
  formControlSelect: {
    background: '#fff',
    height: '24px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    marginTop: '2px',
    paddingLeft: 4
  }
});
export default withStyles(styles)(FixedRateRenderer);
