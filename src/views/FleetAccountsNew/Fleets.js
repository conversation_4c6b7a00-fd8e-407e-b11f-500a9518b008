import React, { useEffect, useState } from 'react';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import Grid from '@material-ui/core/Grid';

import { useHistory } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import {
  Typography,
  LinearProgress,
  Box,
  Paper,
  Tooltip,
  Divider,
  FormControl,
  TextField,
  InputLabel,
  Select,
  MenuItem
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import OutlinedInput from '@material-ui/core/OutlinedInput';
import Checkbox from '@material-ui/core/Checkbox';
import Autocomplete from '@material-ui/lab/Autocomplete';
import InputAdornment from '@material-ui/core/InputAdornment';
import SearchIcon from '@material-ui/icons/Search';
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import { connect } from 'react-redux';
import { SET_SEARCH } from 'src/actions';
import 'src/grid.css';

import 'react-grid-layout/css/styles.css';
import {
  getFleetCustomerNames,
  getFleetListItems
} from 'src/utils/hasuraServices';

import Page from 'src/components/Page';
import { Button } from '@material-ui/core';

var lodash = require('lodash');
var Dealer = process.env.REACT_APP_DEALER;
const CustomAutocomplete = withStyles({
  inputRoot: {
    paddingRight: '5px !important',
    '&[class*="MuiFilledInput-root"]': {}
  }
})(Autocomplete);
const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1,
    width: '99%'
  },
  paper: {
    padding: theme.spacing(2),
    textAlign: 'center',
    color: theme.palette.text.secondary
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  mainLabelAlert: {
    marginTop: 10,
    display: 'flex'
  },
  container: {
    alignItems: 'center',
    margin: '20px 0px'
  },
  containerGridItem: {
    // margin: '10px 0px';
    display: 'flex',
    justifyContent: 'space-between'
    //gridGap: 20
  },
  containerGrid: {
    display: 'flex'
  },
  headerItem: {
    display: 'flex',
    //justifyContent: 'space-between'
    alignItems: 'center'
  },
  sublLabel: {
    display: 'flex',
    '@media (max-width: 1920px)': {
      fontSize: 14
    },
    '@media (max-width: 1280px)': {
      fontSize: '12px !important'
    },
    '@media (min-width: 2304px)': {
      fontSize: 15
    }
  },
  textContainer: {
    alignItems: 'start',
    display: 'flex'
  },
  edit: {
    background: theme.palette.button.primary,
    color: '#fff',
    marginLeft: 10,
    alignSelf: 'self-end'
  },
  back: {
    fontSize: 12,

    color: '#7987a1',
    border: '1px solid #7987a1',
    padding: '0 8px',
    borderRadius: 3,
    cursor: 'pointer',
    '&:hover': {
      background: '#7987a1',
      color: '#fff'
    }
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: 120,
    paddingLeft: 8,
    paddingRight: 4
  },

  titleContainer: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'space-between'
  },
  mainLabel: {
    marginTop: 10,
    display: 'flex',
    marginLeft: 20
  },
  back: {
    marginRight: 10,
    float: 'right',
    marginTop: 7
  },
  dataAsOf: {
    marginRight: 32,
    float: 'left',
    marginTop: 1,
    marginLeft: '1%'
  },
  dataAsOfText: {
    marginLeft: 45
  },
  dataAsOfValue: {
    marginLeft: 3,
    marginRight: 8
  },
  CurrentGridText: {
    marginLeft: 4
  },
  CurrentGridValue: {
    marginLeft: 3
  },
  TargetRateText: {
    marginLeft: 38
  },
  TargetRateValue: {
    marginLeft: 5
  },
  summaryBlock: {
    //width: '25%',
    height: 30,
    cursor: 'default',
    textTransform: 'none',
    marginLeft: 6,
    pointerEvents: 'none',
    '&:hover': {
      backgroundColor: 'white'
    }
  },
  summaryBlockText: {
    fontSize: 12,
    color: '#7987a1',
    fontWeight: 'bold'
  },
  inputText: {
    width: 125,
    textAlign: 'left'
  },
  inputTextRate: {
    width: 97,
    textAlign: 'left'
  },
  inputValue: {
    marginRight: 11
  },
  input: {
    margin: '0px 5px',
    width: '200px'
  },
  margin: {
    width: '10%',
    marginLeft: 5
  },
  dataContainer: {
    display: 'flex',
    justifyContent: 'space-between'
  },
  textContainer: {
    alignItems: 'start',
    display: 'flex'
  },
  inputRoot: {
    paddingRight: 2
  },
  tableRowRoot: {
    backgroundColor: '#dde2ef !important',
    '&:last-child': { borderBottom: 'none' }
  },
  tableRowRootParts: {
    backgroundColor: '#e5eaef !important',
    '&:last-child': { borderBottom: 'none' }
  },
  tableRowMain: {
    backgroundColor: '#bac1d4 !important'
  },
  tableRowSub: {
    backgroundColor: '#cbd6df !important'
  },
  tableRowSubHeader: {
    backgroundColor: '#c9cdd1 !important'
  },
  tableContainter: {
    borderRadius: 0,
    boxShadow: 'none'
  }
}));

const FleetAccounts = props => {
  const classes = useStyles();
  const dispatch = useDispatch();
  let textInput = React.createRef();
  const [isLoading, setIsLoading] = useState(false);
  const [customerNames, setCustomerNames] = useState('');
  const [fleetsLabor, setFleetsLabor] = useState('');
  const [fleetsParts, setFleetsParts] = useState('');
  const [newRow, setNewRow] = useState(false);
  const [isFleetLoaded, setIsFleetLoaded] = useState(false);
  const history = useHistory();

  const [state, setState] = useState({
    right: false
  });
  const handleAddRecord = event => {
    setNewRow(true);
  };
  const handleCancel = event => {
    setNewRow(false);
  };

  useEffect(() => {
    getFleetCustomerNames('', result => {
      let data = result;
      if (result.data.statelessCcAggregateGetCustomers.nodes.length > 0) {
        setIsFleetLoaded(true);
        setCustomerNames(result.data.statelessCcAggregateGetCustomers.nodes);
      }
    });
    getFleetListItems('OCONNOR,PAMELA', result => {
      let data = result;
      let laborArr = [];
      let partsArr = [];
      if (
        result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
          .length > 0
      ) {
        result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes.map(
          (obj, i) => {
            if (obj.laborOrParts == 'labor') {
              laborArr.push(obj);
            } else {
              partsArr.push(obj);
            }
          }
        );
        setFleetsLabor(laborArr);

        setFleetsParts(partsArr);

        // setFleets(
        //   result.data.statelessCcPhysicalRwGetLaborAndPartsFleetAccount.nodes
        // );
      }
    });
  }, []);
  const job = [];
  const dataByJob = {};
  const partByLbr = {};
  const partList = [];
  var lbrRepeat = [];
  var partListData = [];

  const windowWidth = window.innerWidth;
  const counter = 1;

  var tabType = '';
  const handleclick = params => {};

  return (
    <div>
      <Page title={'Fleet Accounts'}></Page>
      <Paper
        square
        style={{
          // margin: 8,
          marginTop: '40px',
          paddingTop: '6px',
          height: '40px',
          margin: '8px 8px 8px',
          backgroundColor: Dealer === 'Armatus' ? '#ddeaf4' : '#F4E1E7',
          border:
            Dealer === 'Armatus' ? '1px solid #003d6b' : '1px solid #C2185B',
          color: Dealer === 'Armatus' ? '#003d6b' : '#c2185b'
        }}
      >
        <Grid
          container
          className={clsx(props.titleContainer, 'reset-dashboard')}
        >
          <Grid item xs={5} style={{ display: 'flex', alignItems: 'center' }}>
            {props.history &&
            props.history.location.state &&
            props.history.location.state.pageType == 'LaborMisses' ? (
              <Button
                variant="contained"
                className={'bck-btn'}
                onClick={handleclick}
              >
                <Typography variant="body1" align="left">
                  Back
                </Typography>
              </Button>
            ) : (
              ''
            )}
          </Grid>
          <Grid item xs={5} style={{ display: 'flex', alignItems: 'center' }}>
            <Typography
              variant="h4"
              color="primary"
              className={clsx(props.mainLabel, 'main-title')}
            >
              Fleet Accounts
            </Typography>
          </Grid>
          <Grid item xs={2} style={{ display: 'flex', justifyContent: 'end' }}>
            {/* <div>
                <Tooltip title="Export To Excel">
                  <Link
                    style={{
                      paddingTop: 1,
                      paddingRight: 10,
                      cursor: 'pointer',
                      float: 'right'
                    }}
                    onClick={this.onBtExport}
                  >
                    <ExportIcon />
                  </Link>
                </Tooltip> 
              </div> */}
            {/* <Button
              variant="contained"
              id="reset-layout"
              style={{ marginTop: -2 }}
              className={clsx(classes.back, 'reset-btn')}
             // onClick={this.resetRawData}
            >
              <RestoreIcon />
              <Typography variant="body1" align="left">
                Reset Layout
              </Typography>
            </Button> */}
          </Grid>
        </Grid>
      </Paper>

      <Paper
        square
        style={{
          margin: 8,
          height: 55,
          marginLeft: 8,
          alignItems: 'center',
          paddingLeft: 4,
          display: 'flex',
          justifyContent: 'space-between'
        }}
      >
        {isFleetLoaded ? (
          <CustomAutocomplete
            id="free-solo-demo"
            freeSolo
            size="small"
            style={{
              width: '15%',

              fontSize: 12
            }}
            options={
              customerNames != '' &&
              customerNames.length > 0 &&
              customerNames.map((customerName, i) =>
                customerName != null && customerName.customerName != null
                  ? customerName.customerName
                  : ''
              )
            }
            renderInput={params => (
              <TextField
                {...params}
                label="Customer Name"
                variant="outlined"
                // autoComplete={true}
                InputProps={{
                  ...params.InputProps,

                  endAdornment: (
                    <InputAdornment position="end">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                  disableUnderline: true
                }}
              />
            )}
          />
        ) : (
          ''
        )}
        <Button
          className={clsx(classes.back, 'reset-btn')}
          style={{ marginTop: 4 }}
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={handleAddRecord}
        >
          Add New
        </Button>
      </Paper>
      {!newRow ? (
        <Paper
          square
          style={{
            margin: 8,
            height: 55,
            marginLeft: 8,
            alignItems: 'center',
            paddingLeft: 4,
            display: 'flex'
          }}
        >
          <Grid item xs={12} className={classes.textContainer}>
            <CustomAutocomplete
              id="free-solo-demo"
              freeSolo
              size="small"
              style={{
                width: '15%',

                fontSize: 12
              }}
              options={
                customerNames.length > 0 &&
                customerNames.map((customerName, i) =>
                  customerName != null && customerName.customerName != null
                    ? customerName.customerName
                    : ''
                )
              }
              renderInput={params => (
                <TextField
                  {...params}
                  label="Customer Name"
                  variant="outlined"
                  // autoComplete={true}
                  InputProps={{
                    ...params.InputProps,

                    endAdornment: (
                      <InputAdornment position="end">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                    disableUnderline: true
                  }}
                />
              )}
            />
            <FormControlLabel
              style={{ marginLeft: 0 }}
              control={<Checkbox name="checked" />}
              label="Labor Grid"
            />
            <FormControl
              size="small"
              className={classes.margin}
              variant="outlined"
            >
              <InputLabel htmlFor="outlined-adornment-amount">
                Fixed Labor Rate
              </InputLabel>
              <OutlinedInput
                id="outlined-adornment-amount"
                // value={values.amount}

                //onChange={handleChange('amount')}
                startAdornment={
                  <InputAdornment position="start">$</InputAdornment>
                }
                labelWidth={120}
              />
            </FormControl>
            <FormControl
              size="small"
              className={classes.margin}
              variant="outlined"
            >
              <InputLabel htmlFor="outlined-adornment-amount">
                Door Rate
              </InputLabel>
              <OutlinedInput
                id="outlined-adornment-amount"
                // value={values.amount}

                //onChange={handleChange('amount')}
                startAdornment={
                  <InputAdornment position="start">$</InputAdornment>
                }
                labelWidth={120}
              />
            </FormControl>

            <FormControlLabel
              style={{ marginLeft: 0 }}
              control={<Checkbox name="checked" />}
              label="Parts Metrics"
            />
            <TextField
              id="outlined-select-currency-native"
              select
              size="small"
              className={classes.margin}
              label="Parts Markup"
              SelectProps={{
                native: true
              }}
              //placeholder=" select your currency"
              variant="outlined"
            >
              <option key={1} value={1}>
                {'List +'}
              </option>
              <option key={2} value={2}>
                {'Cost +'}
              </option>
            </TextField>
            <FormControl
              size="small"
              className={classes.margin}
              style={{ marginRight: 5 }}
              variant="outlined"
            >
              <InputLabel htmlFor="outlined-adornment-amount">
                Part source
              </InputLabel>
              <OutlinedInput
                id="outlined-adornment-amount"
                // value={values.amount}

                //onChange={handleChange('amount')}

                labelWidth={120}
              />
            </FormControl>

            <FormControl
              size="small"
              className={classes.margin}
              style={{ marginRight: 5 }}
              variant="outlined"
            >
              <InputLabel htmlFor="outlined-adornment-amount">
                Installed Date
              </InputLabel>
              <OutlinedInput
                id="outlined-adornment-amount"
                // value={values.amount}

                //onChange={handleChange('amount')}

                labelWidth={120}
              />
            </FormControl>
            <Button
              className={clsx(classes.back, 'reset-btn')}
              style={{ marginTop: 4 }}
              variant="contained"
              color="primary"
            >
              Save
            </Button>
            {/* <Button
              className={clsx(classes.back, 'reset-btn')}
              style={{ marginTop: 4 }}
              variant="contained"
              color="primary"
              onClick={this.handleCancel}
            >
              Cancel
            </Button> */}
          </Grid>
        </Paper>
      ) : (
        ''
      )}
      {isLoading == true ? (
        <div>
          <Box style={{ padding: 25 }}>
            <LinearProgress color="secondary" />
            <Typography
              variant="h6"
              align="center"
              style={{ padding: 25 }}
              color="primary"
            >
              Processing...
            </Typography>
          </Box>
        </div>
      ) : (
        <Paper
          square
          style={{
            margin: 8,

            marginLeft: 8,
            alignItems: 'center',
            paddingLeft: 4,
            display: 'flex'
          }}
        >
          <Grid item xs={12} className={classes.textContainer}>
            <TableContainer
              component={Paper}
              className={clsx(
                classes.tableContainter,
                'repair-order-details-table'
              )}
              style={{ maxHeight: window.innerHeight - 292, padding: 10 }}
            >
              <Table stickyHeader aria-label="collapsible table">
                <TableBody>
                  <TableRow
                    className={clsx(
                      classes.root,
                      classes.tableRowSub,
                      'jobline-heading-tr'
                    )}
                  >
                    <TableCell colSpan={8}>
                      {/* <TableCell colSpan={( pageType == "labormisses" || pageType == "partsmisses" ) ? 8 : 6}> */}
                      <div className={clsx(classes.headerItem, 'main-title')}>
                        <Typography
                          variant="h6"
                          color="secondary"
                          gutterBottom
                          className={clsx(
                            classes.tableRowCommon,
                            classes.jobLineHeading
                          )}
                        >
                          FleetName: {'ABC'}
                        </Typography>
                      </div>
                    </TableCell>

                    <TableCell colSpan={3} align="right">
                      {/* <Button
            className={classes.descriptionButton}
            onClick={() => showJobDescription(individualRow.lbrlinecode)}
          >
            Job Description
          </Button> */}
                    </TableCell>
                  </TableRow>
                  <TableRow className={clsx(classes.labor)}>
                    <TableCell colSpan={8}>
                      {/* <TableCell colSpan={( pageType == "labormisses" || pageType == "partsmisses" ) ? 8 : 6}> */}
                      <div className={clsx(classes.headerItem, 'main-title')}>
                        <Typography
                          variant="h6"
                          color="secondary"
                          gutterBottom
                          className={clsx(
                            classes.tableRowCommon,
                            classes.jobLineHeading
                          )}
                        >
                          Labor
                        </Typography>
                      </div>
                    </TableCell>

                    <TableCell colSpan={3} align="right">
                      {/* <Button
            className={classes.descriptionButton}
            onClick={() => showJobDescription(individualRow.lbrlinecode)}
          >
            Job Description
          </Button> */}
                    </TableCell>
                  </TableRow>
                  <TableRow
                    className={clsx(
                      classes.table,
                      classes.tableRowMain,
                      'labor-opcode-title-section'
                    )}
                  >
                    {/* <TableCell width="10%" align="center"> */}

                    <TableCell colSpan={2} align="center">
                      Labor Grid
                    </TableCell>
                    {/* <TableCell width="10%" align="center"> */}
                    <TableCell colSpan={2} align="center">
                      Fixed Labor Rate
                    </TableCell>
                    {/* <TableCell width="12%" align="center"> */}
                    <TableCell colSpan={3} align="center">
                      Grid Rate
                    </TableCell>
                    <TableCell colSpan={3} align="center">
                      installed date
                    </TableCell>
                    <TableCell colSpan={2} align="center">
                      Action
                    </TableCell>
                  </TableRow>
                  {fleetsLabor.length > 0 &&
                    fleetsLabor.map((val, i) => (
                      <TableRow
                        className={clsx(
                          classes.table,
                          classes.tableRowMain,
                          'labor-opcode-title-section'
                        )}
                      >
                        {/* <TableCell width="10%" align="center"> */}

                        <TableCell colSpan={2} align="center">
                          {val.laborgridOrNot}
                        </TableCell>
                        {/* <TableCell width="10%" align="center"> */}
                        <TableCell colSpan={2} align="center">
                          {val.laborFixedRate}
                        </TableCell>
                        {/* <TableCell width="12%" align="center"> */}
                        <TableCell colSpan={3} align="center">
                          {val.doorRate}
                        </TableCell>
                        <TableCell colSpan={3} align="center">
                          {val.gridOrMatrixInstallDate}
                        </TableCell>
                        <TableCell colSpan={2} align="center">
                          Action
                        </TableCell>
                      </TableRow>
                    ))}
                  <TableRow className={clsx(classes.labor)}>
                    <TableCell colSpan={8}>
                      {/* <TableCell colSpan={( pageType == "labormisses" || pageType == "partsmisses" ) ? 8 : 6}> */}
                      <div className={clsx(classes.headerItem, 'main-title')}>
                        <Typography
                          variant="h6"
                          color="secondary"
                          gutterBottom
                          className={clsx(
                            classes.tableRowCommon,
                            classes.jobLineHeading
                          )}
                        >
                          Parts
                        </Typography>
                      </div>
                    </TableCell>

                    <TableCell colSpan={3} align="right">
                      {/* <Button
            className={classes.descriptionButton}
            onClick={() => showJobDescription(individualRow.lbrlinecode)}
          >
            Job Description
          </Button> */}
                    </TableCell>
                  </TableRow>
                  <TableRow
                    className={clsx(
                      classes.table,
                      classes.tableRowMain,
                      'labor-opcode-title-section'
                    )}
                  >
                    {/* <TableCell width="10%" align="center"> */}

                    <TableCell colSpan={2} align="center">
                      Parts Matrix
                    </TableCell>
                    {/* <TableCell width="10%" align="center"> */}
                    <TableCell colSpan={2} align="center">
                      Parts markup
                    </TableCell>
                    {/* <TableCell width="12%" align="center"> */}
                    <TableCell colSpan={3} align="center">
                      Matrix installed date
                    </TableCell>
                    <TableCell colSpan={3} align="center">
                      installed date
                    </TableCell>
                    <TableCell colSpan={2} align="center">
                      Action
                    </TableCell>
                  </TableRow>
                  {fleetsParts.length > 0 &&
                    fleetsParts.map((val, i) => (
                      <TableRow
                        className={clsx(
                          classes.table,
                          classes.tableRowMain,
                          'labor-opcode-title-section'
                        )}
                      >
                        {/* <TableCell width="10%" align="center"> */}

                        <TableCell colSpan={2} align="center">
                          {val.partsource}
                        </TableCell>
                        {/* <TableCell width="10%" align="center"> */}
                        <TableCell colSpan={2} align="center">
                          {val.partsmarkup}
                        </TableCell>
                        {/* <TableCell width="12%" align="center"> */}
                        <TableCell colSpan={3} align="center">
                          {val.gridOrMatrixInstallDate}
                        </TableCell>
                        <TableCell colSpan={3} align="center">
                          {val.gridOrder}
                        </TableCell>
                        <TableCell colSpan={2} align="center">
                          Action
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        </Paper>
      )}
    </div>
  );
};

export default connect(mapStateToProps, mapDispatchToProps)(FleetAccounts);

function mapStateToProps(state) {
  return state;
}

function mapDispatchToProps(dispatch) {
  return {
    setSearch: data => dispatch({ type: SET_SEARCH, payload: data })
  };
}

// export default SearchByRO;
