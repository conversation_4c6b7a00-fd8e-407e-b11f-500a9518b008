import React, { useState, useEffect } from 'react';
import Page from 'src/components/Page';
import FleetAccounts from './FleetAccounts';
import { useHistory } from 'react-router';
import { getFleetCustomerNames } from 'src/utils/hasuraServices';
import { CircularProgress, Grid } from '@material-ui/core';
import { useDispatch, useSelector } from 'react-redux';
import { makeStyles } from '@material-ui/styles';

const useStyles = makeStyles(theme => ({
  loaderGrid: {
    height: 150,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontFamily: 'Roboto',
    fontSize: 25,
    color: '#003d6b'
  }
}));

function Fleets() {
  const classes = useStyles();
  const history = useHistory();
  const [payTypes, setPayType] = useState([]);
  const [selectedPayType, setSelectedPayType] = useState('');
  const [isLoading, setLoading] = useState(true);
  const session = useSelector(state => state.session);

  return (
    <Page title="Fleet Accounts">
      <FleetAccounts
        history={history}
        payTypes={payTypes}
        selectedPayType={selectedPayType}
        session={session}
      />
    </Page>
  );
}

export default Fleets;
