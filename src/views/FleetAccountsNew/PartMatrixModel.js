import React, { useState, useEffect, useCallback } from 'react';

import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

import { makeStyles } from '@material-ui/core/styles';
import Accordion from '@material-ui/core/Accordion';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import moment from 'moment';
import {
  Grid,
  Typography,
  Button,
  Tooltip,
  CircularProgress,
  Checkbox,
  Paper,
  FormControl
} from '@material-ui/core';
import {
  getMatrixType,
  getMatrixDetail,
  getPartsGridList,
  insertFleetDetails
} from 'src/utils/hasuraServices';
import { setMenuSelected, setNavItems } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Page from 'src/components/Page';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import MatrixForm from './MatrixForm';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
import DateRangePicker from 'react-bootstrap-daterangepicker';
import SuccessSnackbar from 'src/components/SuccessSnackbar';
var lodash = require('lodash');

const Dealer = process.env.REACT_APP_DEALER;
const useStyles = makeStyles(theme => ({
  root: {
    width: '99%',
    marginLeft: 8,
    maxHeight: '100%',
    overflowY: 'auto'
  },
  text: {
    fontSize: '13px !important',
    color: '#212121 !important'
  },
  headValue: {
    fontSize: '13px !important',
    color: '#212121 !important'
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  matrixType: {
    marginTop: -42,
    marginLeft: 150
  },
  loaderGrid: {
    marginLeft: 450,
    marginTop: 120
  },
  header: {
    textTransform: 'none',
    fontSize: '13px !important',
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b'
  },
  headerDes: {
    textTransform: 'none',
    fontSize: '13px !important',
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b',
    marginTop: -25
  },
  checkBox: {
    cursor: 'pointer',
    height: 20,
    position: 'absolute',
    left: 2
    // right: 45,
    // top: 35
  },
  checkBoxError: {
    cursor: 'pointer',
    height: 20,
    position: 'absolute',
    left: 2,
    // right: 45,
    // top: 35,
    color: '#e53935'
  }
}));

export default function PartsMatrixModel(props) {
  let dateArray = [];
  let selectedSource =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.partsSource != ''
      ? props.history.location.state.partsSource
      : '';
  let selectedGridValue =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    (props.history.location.state.selectedGrid ||
      props.history.location.state.selectedGrid == '')
      ? props.history.location.state.selectedGrid
      : 1;
  let selectedPageType =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.pageType != ''
      ? props.history.location.state.pageType
      : '';
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const [addMatrix, setAddMatrix] = React.useState(false);
  const [expanded, setExpanded] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [type, setType] = React.useState(
    props.typeFor == 'matrix' ? 'matrix_fleet' : 'grid_fleet'
  );
  const [partSrc, setPartSrc] = React.useState(
    selectedSource ? selectedSource : null
  );
  const [payType, setPayType] = React.useState(
    selectedMatrixType ? selectedMatrixType : null
  );
  const [matrixTypeData, setMatrixTypeData] = React.useState([]);
  const [selectedMatrixType, setSelectedMatrixType] = React.useState(null);
  const [matrixData, setMatrixData] = React.useState([]);
  const [gridPeriods, setGridPeriods] = React.useState([]);
  const [selectedGrid, setSelectedGrid] = React.useState(
    selectedGridValue ? selectedGridValue : []
  );
  const [partsDetail, setPartsDetail] = React.useState([]);
  const [loading, setLoading] = useState(false);
  const [rowData, setRowData] = useState([]);
  const [matrixDataAll, setMatrixDataAll] = React.useState([]);
  const [openDialog, setOpenDialog] = useState(false);

  const [createdDate, setCreatedDate] = React.useState([]);
  const [selDate, setSelDate] = React.useState([]);
  const [partsSource, setPartsSource] = React.useState('');
  const [errorItems, setErrorItems] = React.useState([]);
  const [successMsg, setSuccessMsg] = useState('');
  const [openSnackbar, setopenSnackbar] = useState(false);
  const [goalFail, setGoalFail] = useState('');
  const [matrixTypeArr, setMatrixTypeArr] = useState([]);
  const [overlayNoRowsTemplate, setOverlayNoRowsTemplate] = useState(
    '<span style="padding: 10px;margin-top:50px; ">No rows to show</span>'
  );
  const [pGridorpartsfor, setpGridorpartsfor] = React.useState(
    props.gridRateValueStart
  );
  const [process, setProcess] = React.useState('');
  const [checkBoxError, setCheckBoxError] = React.useState(false);
  const [dateError, setDateError] = React.useState(false);
  const [dates, setDates] = useState([]);
  const [selDates, setSelDates] = useState([]);
  const [installedDateError, setInstalledDateError] = useState(false);
  const [installedDateItem, setInstalledDateItem] = useState('');

  useEffect(() => {
    console.log('gridRateValueStart=======', props.gridRateValueStart);
    console.log('props.openDialog==', props.openDialog, '===', props);
    dispatch(setMenuSelected('Fleet Accounts'));
    dispatch(setNavItems(['Reference / Setups']));
    setOpenDialog(props.openDialog);
  }, [props.openDialog]);

  useEffect(() => {
    var gridRateValueStart = props.gridRateValueStart;
    console.log('gridRateValueStart=======', props.gridRateValueStart);
    matrixType(type, partSrc, payType, null, pGridorpartsfor);
  }, [session.storeSelected, addMatrix]);
  const [defaultColDef, setDefaultColDef] = useState({
    enableValue: true,
    sortable: true,
    filter: true,
    resizable: false,
    suppressMovable: false
  });
  const [columnDefsLabor, setColumnDefsLabor] = useState([
    {
      headerName: 'Hours Sold',
      field: 'hours',
      width: 90,
      suppressMenu: true,
      unSortIcon: true,
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      }
    },
    // {
    //   headerName: 'Door Rate',
    //   field: 'doorRate',
    //   width: 90,
    //   suppressMenu: true,
    //   unSortIcon: true,
    //   cellStyle: function(params) {
    //     return { textAlign: 'right', border: ' 0px white' };
    //   },
    // },
    {
      headerName: '0',
      field: 'col0OrpriceStartRange',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },

    {
      headerName: '0.1',
      field: 'col1OrpriceEndRange',
      width: 100,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      suppressMenu: true,
      unSortIcon: true,
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.2',
      field: 'col2OraddPercentage',
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.3',
      field: 'col3',
      width: 100,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      suppressMenu: true,
      unSortIcon: true,
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.4',
      field: 'col4',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.5',
      field: 'col5',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.6',
      field: 'col6',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.7',
      field: 'col7',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.8',
      field: 'col8',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    },
    {
      headerName: '0.9',
      field: 'col9',
      width: 100,
      suppressMenu: true,
      unSortIcon: true,
      valueFormatter: formatCellValueRate,
      cellClass: 'twoDecimalPlacesWith$',
      cellStyle: function(params) {
        return { textAlign: 'right', border: ' 0px white' };
      },
      comparator: function(valueA, valueB) {
        return valueA - valueB;
      }
    }
  ]);
  const [columnDefs, setColumnDefs] = useState([
    {
      headerName: 'From',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'col0OrpriceStartRange',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      valueFormatter: params => formatCellValue(params),
      // cellClass: 'twoDecimalPlacesWith$',
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'To',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'col1OrpriceEndRange',
      valueFormatter: params => formatCellValue(params),
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Base',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'calcBase',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Break Field',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'breakField',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Percentage',
      field: 'col2OraddPercentage',
      // width: 130,
      minWidth: 80,
      valueFormatter: params => formatCellValueGP(params),
      cellClass: 'textAlign',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return {
          border: ' 0px white',
          textAlign: 'left'
        };
      }
    }
  ]);
  const formatCellValueRate = params => {
    if (params.value && params.value != '') {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '';
    }
  };
  const matrixType = (type, partSrc, payType, createddate, pGridorpartsfor) => {
    setIsLoading(true);
    getPartsGridList(
      type,
      partSrc,
      payType,
      // createddate,
      pGridorpartsfor,
      result => {
        console.log('result==', result);
        let resultArr = [];
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
          val => {
            resultArr.push(val.gridormatrixtype);
          }
        );
        if (
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
            .nodes &&
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
            .length > 0
        ) {
          const dateValue =
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes;

          var dateObj = {};
          var dateArr = [];
          dateValue &&
            dateValue.map((item, index) => {
              // Fix: Enclose the callback function in parentheses
              const dateData = new Date(item.createdDate);
              const options = {
                year: '2-digit',
                month: '2-digit',
                day: '2-digit'
              };
              var dateObj = {
                id: index,
                // date: new Date().toLocaleDateString('en-US')
                date: dateData.toLocaleDateString('en-US', options)
              };
              dateArr.push(dateObj);
            });
          console.log('dateObj==', dateArr, '==', dateValue);
          setDates(dateArr);
          setMatrixData(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes
          );
          setSelectedMatrixType(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes[0].gridormatrixtype
          );
          setMatrixTypeData(resultArr);
          // matrixDetails(
          //   'matrix',
          //   null,
          //   result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
          //     .nodes[0].gridormatrixtype
          // );
        } else {
          setIsLoading(false);
        }
      }
    );
  };
  const matrixDetails = (type, partSrc, payType) => {
    getMatrixType(type, payType, partSrc, result => {
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
      ) {
        // setMatrixDataAll(
        //   result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        // );
        const data =
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes;
        const distinctGridOrders = [
          ...new Set(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
              item => item.gridOrder
            )
          )
        ];
        // setSelectedGrid(distinctGridOrders[0]);
        // setMatrixData(
        //   data.filter(item => item.gridOrder === distinctGridOrders[0])
        // );
        // setGridPeriods(distinctGridOrders);
        const resultArrary = data.filter(
          item => item.gridOrder === distinctGridOrders[0]
        );
        const resultArr = filterUniqueArrays(resultArrary, 'partSource');
        if (selectedPageType == 'PartsMisses') {
          const filteredData = resultArr.filter(item =>
            item.partSource.includes(selectedSource)
          );
          setMatrixData(filteredData);
        } else {
          setMatrixData(resultArr);
        }
      }
    });
  };

  const filterUniqueArrays = (arr, prop) => {
    const uniqueArrays = [];

    return arr.filter(obj => {
      const arrStr = JSON.stringify(obj[prop]);
      if (!uniqueArrays.includes(arrStr)) {
        uniqueArrays.push(arrStr);
        return true;
      }
      return false;
    });
  };

  const handleChange = panel => (event, isExpanded) => {
    const type = props.typeFor;
    setLoading(true);
    setPartsDetail([]);
    getMatrixDetail(
      type,
      panel.partSource,
      panel.gridormatrixtype,
      panel.createdDate,
      pGridorpartsfor,
      result => {
        let resultArr = [];
        if (
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        ) {
          setLoading(false);
          setPartsDetail(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes
          );
        }
      }
    );
    setExpanded(isExpanded ? panel : false);
  };
  const formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '$0.00';
    }
  };
  const formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0%';
    }
  };
  const formatCellValueDate = params => {
    if (params != null && params != 0) {
      return moment(params).format('MM/DD/YY');
    }
  };

  const showMenuModal = data => {
    setOpenDialog(true);
    // setSelectedMenu(data);
  };
  const handleClose = event => {
    event.preventDefault();
    setOpenDialog(false);
    props.handleClose(true);
  };
  const changeProcess = event => {
    console.log('data=========', event.target.value);
    setProcess(event.target.value);
  };
  const handleCheckboxChange = (e, item, index) => {
    const isChecked = e.target.checked;

    if (isChecked == false) {
      setExpanded(false);
      var matrixObj = {
        id: index,
        gridormatrixtype: item.gridormatrixtype
        // date: dateData.toLocaleDateString('en-US', options)
      };

      console.log('matrixArr===', matrixObj, '==', matrixTypeArr);

      const updatedArray = matrixTypeArr.filter(
        obj =>
          !(
            obj.id === matrixObj.id &&
            obj.gridormatrixtype === matrixObj.gridormatrixtype
          )
      );
      setMatrixTypeArr(updatedArray);

      console.log('enter=1=1=1=1=11=1=1=1=1=', updatedArray);
      const filteredDates = createdDate.filter(
        date => date !== item.createdDate
      );
      // Update the state with the filtered array
      setCreatedDate(filteredDates);
      const filteredSelDates = selDate.filter(date => date !== dates[index]);
      setSelDate(filteredSelDates);
      console.log('filteredSelDates==', filteredSelDates, '==', selDate);
    } else {
      setExpanded(false);
      var matrixObj = {
        id: index,
        gridormatrixtype: item.gridormatrixtype
        // date: dateData.toLocaleDateString('en-US', options)
      };

      const newMatrixArray = [...matrixTypeArr, matrixObj];
      setMatrixTypeArr(newMatrixArray);
      // const createdDate = item.createdDate;
      const partsource = item.partsource;

      const newDataArray = [...createdDate, item.createdDate];
      setCreatedDate(newDataArray);
      const newSelArray = [...selDate, dates[index]];
      setSelDate(newSelArray);
      console.log(
        'filteredSelDates==2==',
        newSelArray,
        '==',
        selDate,
        '=1=',
        dates
      );
      // dateArray.push(item.createdDate);
      // console.log('dateArray===', dateArray);
      // setCreatedDate(item.createdDate);
      setPartsSource(item.partSource);
    }
    setExpanded(false);
  };
  const showPartsMatrix = event => {
    if (event == 'add') {
      setAddMatrix(true);
    } else {
      setAddMatrix(false);
    }
  };
  const validateUniqueDates = dateArray => {
    const dateSet = new Set();

    for (const item of dateArray) {
      // Normalize date format to ensure consistency
      const normalizedDate = new Date(item.date).toLocaleDateString('en-US');

      if (dateSet.has(normalizedDate)) {
        return false; // Duplicate date found
      }

      dateSet.add(normalizedDate);
    }

    return true; // No duplicates found
  };
  const handleSave = event => {
    console.log('matrixTypeArr===', matrixTypeArr, selDate);
    setopenSnackbar(false);
    setGoalFail('');
    setSuccessMsg('');
    console.log('selDate==****===', selDate);
    const isValid = validateUniqueDates(selDate);
    console.log('isValid===', isValid); // Output: false
    if (createdDate.length > 0 && isValid == true) {
      console.log('enter=1');
      setDateError(false);
      setCheckBoxError(false);
      let fleet_rate_grid = [];
      let fleet_rate_parts = [];
      let fleet_exists_labor_grid = [];
      let fleet_exists_parts_matrix = [];
      let errorArr = [];
      let typeChecking = props.fleetName;
      if (typeChecking == '' || typeChecking == 'All') {
        errorArr.push('fleetNameError');
      }
      setErrorItems(errorArr);
      const matrixType =
        props.gridRateValueStart == 'cust_fleet'
          ? 'Customer'
          : props.gridRateValueStart == 'paytype_fleet'
          ? 'Paytype'
          : 'Opcode';
      const fleettype =
        props.gridRateValueStart == 'cust_fleet'
          ? 'customer'
          : props.gridRateValueStart == 'paytype_fleet'
          ? 'paytype'
          : 'opcode';
      const userid = localStorage.getItem('userID');
      const process = 'insert';
      const fleetname = props.fleetName;
      let parts_for =
        props.gridRateValueStart == 'cust_fleet'
          ? 'cust_fleet'
          : props.gridRateValueStart == 'paytype_fleet'
          ? 'paytype_fleet'
          : 'opcode_fleet';
      let grid_for =
        props.gridRateValueStart == 'cust_fleet'
          ? 'cust_fleet'
          : props.gridRateValueStart == 'paytype_fleet'
          ? 'paytype_fleet'
          : 'opcode_fleet';
      let arr = ['200'];
      let arra = ['200'];
      console.log('fleettype==', fleettype);
      if (props.typeFor == 'matrix') {
        createdDate.map((createDat, index) =>
          fleet_exists_parts_matrix.push({
            process: process,
            fleettype: fleettype,
            fleetname: fleetname,
            fleetormatrixflag: 'Matrix',
            matrixinstalldate: selDate[index].date,
            created_date: createDat,
            partsource: partsSource,
            // matrix_type: matrixType,
            matrix_type: matrixTypeArr[index].gridormatrixtype,
            parts_for: parts_for
          })
        );
      }
      if (props.typeFor == 'grid') {
        createdDate.map((createDat, index) =>
          fleet_exists_labor_grid.push({
            process: process,
            fleettype: fleettype,
            fleetname: fleetname,
            fleetorgridflag: 'Grid',
            gridinstalldate: selDate[index].date,
            created_date: createDat,
            // grid_type: 'Standard',
            grid_type: matrixTypeArr[index].gridormatrixtype,
            grid_for: grid_for
          })
        );
      }
      if (errorArr.length <= 0) {
        insertFleetDetails(
          JSON.stringify(fleet_rate_grid),
          JSON.stringify(fleet_rate_parts),
          JSON.stringify(fleet_exists_labor_grid),
          JSON.stringify(fleet_exists_parts_matrix),
          result => {
            console.log('success data====', result);
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                .results[0].status == 1
            ) {
              setopenSnackbar(true);
              setGoalFail(false);
              props.onUpdateData(true);
              setSuccessMsg(
                result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                  .results[0].msg
              );
              // setSuccessMsg(
              //   props.typeFor == 'grid'
              //     ? 'Labor grid has been updated successfully.'
              //     : 'Parts matrix has been updated successfully.'
              // );
              setTimeout(() => {
                setOpenDialog(false);
                props.handleClose(true);
              }, 1500);
            } else {
              // setTimeout(() => {
              //   setOpenDialog(false);
              //   props.handleClose(true);
              // }, 1500);
              setopenSnackbar(true);
              setGoalFail(true);
              setSuccessMsg(
                result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                  .results[0].msg
              );
            }
          }
        );
      }
    } else {
      if (createdDate.length > 0) {
        setCheckBoxError(false);
        if (isValid == true) {
          setDateError(false);
          return;
        } else if (isValid == false) {
          setDateError(true);
          return;
        }
      } else if (createdDate.length < 1) {
        setCheckBoxError(true);
        if (isValid == true) {
          setDateError(false);
          return;
        } else if (isValid == false) {
          setDateError(false);
          return;
        }
      }
    }
  };
  const hidesnackbar = () => {
    setopenSnackbar(false);
  };
  const handleCallback = (e, index) => {
    setInstalledDateError(false);
    const date = e.format('MM/DD/YY');
    console.log('date===s=', date);
    // setDates(prevDates =>
    //   prevDates.map(item =>
    //     item.id === index ? { ...item, date: date } : item
    //   )
    // );
    const updatedData = dates.map(item =>
      item.id == index ? { ...item, date: date } : item
    );
    console.log('updatedData==', updatedData, date);
    setDates(updatedData);

    const updatedDatass = selDate.map(item =>
      item.id == index ? { ...item, date: date } : item
    );

    // const newSelArray = [...selDate, dates[index]];
    setSelDate(updatedDatass);
    // setSelDate(updatedData);
    console.log(
      'date===s=s===',
      dates,
      '==',
      date,
      '==',
      index,
      '=-=',
      updatedData,
      'updatedDatass==',
      updatedDatass
    );
  };
  const handleChangeDate = (event, item) => {
    const { value } = event.target;
    const isValid = moment(value, 'MM/DD/YY', true).isValid();
    if (!isValid) {
      setInstalledDateError(true);
      setInstalledDateItem(item);
    } else {
      setInstalledDateError(false);
      setInstalledDateItem('');
    }
  };
  const handleCalenderClick = (event, item) => {
    event.stopPropagation();
    setExpanded(false);
  };
  console.log('matrixData==s=', props);
  return (
    <Page title="Fleet Accounts">
      <div>
        <Dialog
          open={openDialog}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            style: {
              width: '90%',
              marginLeft: '20px',
              marginTop: '28px',
              height: 1050,
              maxWidth: '90%',
              backgroundColor: '#F4F6F8'
            }
          }}
        >
          <DialogTitle id="alert-dialog-title" style={{ marginLeft: 10 }}>
            <Typography
              variant="h4"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              {props.typeFor == 'matrix' ? 'Parts Matrix' : 'Labor Grid'}
            </Typography>
            <Tooltip title="Close">
              <IconButton
                onClick={handleClose}
                aria-label="close"
                style={{ float: 'inline-end' }}
              >
                <HighlightOffIcon style={{ fontSize: 36 }} />
              </IconButton>
            </Tooltip>
            <MatrixForm
              showPartsMatrix={showPartsMatrix}
              typeFor={props.typeFor}
              gridRateValueStart={props.gridRateValueStart}
              fleetName={props.fleetName}
              onUpdateData={props.onUpdateData}
              handleClose={props.handleClose}
            />
            {addMatrix == true &&
              props.typeFor != 'matrix' &&
              matrixData.length > 0 && (
                <Typography variant="subtitle1" className={classes.headerDes}>
                  All existing fleet grids are displayed below and you may
                  choose one of these. Please note that you can also choose to
                  add a different install date if required for your fleet
                  account. Or, if you wish to upload an entirely new grid,
                  please use the Upload Grid option.
                </Typography>
              )}
            {addMatrix == true &&
              props.typeFor == 'matrix' &&
              matrixData.length > 0 && (
                <Typography variant="subtitle1" className={classes.headerDes}>
                  All existing fleet matrix are displayed below and you may
                  choose one of these. Please note that you can also choose to
                  add a different install date if required for your fleet
                  account. Or, if you wish to upload an entirely new matrix,
                  please use the Upload Matrix option.
                </Typography>
              )}
          </DialogTitle>
          {addMatrix == true && (
            <>
              <DialogContent
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  marginTop: -15
                }}
              >
                <div className={classes.root}>
                  {matrixData.length > 0 ? (
                    matrixData.map((item, index) => (
                      <Accordion
                        expanded={expanded === item}
                        onChange={handleChange(item)}
                      >
                        <AccordionSummary
                          expandIcon={<ExpandMoreIcon />}
                          aria-controls="panel1bh-content"
                          id="panel1bh-header"
                          style={{ height: '90px' }}
                        >
                          <Grid container spacing={3}>
                            <Grid item xs={4}>
                              <FormControl
                                variant="outlined"
                                margin="dense"
                                className={clsx(
                                  classes.formControl,
                                  'input-container'
                                )}
                                style={{ width: '10%' }}
                              >
                                <Tooltip
                                  title={
                                    props.typeFor == 'matrix'
                                      ? 'Matrix'
                                      : 'Grid'
                                  }
                                >
                                  <Checkbox
                                    className={classes.root}
                                    onChange={e =>
                                      handleCheckboxChange(e, item, index)
                                    }
                                    className={classes.checkBox}
                                    size="small"
                                  />
                                </Tooltip>
                              </FormControl>
                              <Typography className={classes.header}>
                                {' '}
                                <Typography
                                  variant="subtitle1"
                                  className={classes.header}
                                >
                                  Store Install
                                  Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </Typography>
                                <Typography
                                  variant="subtitle1"
                                  className={classes.header}
                                >
                                  FOPC Calculated Date
                                  From&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                </Typography>
                              </Typography>
                              <Typography
                                style={{ marginLeft: 45, marginTop: -25 }}
                              >
                                {' '}
                                <Typography
                                  variant="subtitle1"
                                  className={classes.text}
                                >
                                  {' '}
                                  <Typography className={classes.text}>
                                    :{' '}
                                    {formatCellValueDate(item.storeInstallDate)}
                                  </Typography>
                                </Typography>
                                <Typography
                                  variant="subtitle1"
                                  className={classes.text}
                                >
                                  :{' '}
                                  <Typography className={classes.text}>
                                    {formatCellValueDate(item.createdDate)}
                                  </Typography>
                                </Typography>
                              </Typography>
                              {/* <Typography
                                className={classes.header}
                                variant="subtitle1"
                              >
                                {' '}
                                <Typography
                                // variant="subtitle1"
                                // className={classes.headValue}
                                >
                                  : {formatCellValueDate(item.storeInstallDate)}
                                </Typography>
                                <Typography
                                  // variant="subtitle1"
                                  className={classes.headValue}
                                >
                                  : {formatCellValueDate(item.createdDate)}
                                </Typography>
                              </Typography> */}
                            </Grid>
                            <Grid item xs={6}>
                              {props.typeFor == 'grid' && (
                                <>
                                  <Typography>
                                    {' '}
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      {props.gridRateValueStart == 'cust_fleet'
                                        ? 'Customer'
                                        : props.gridRateValueStart ==
                                          'paytype_fleet'
                                        ? 'Pay Type'
                                        : 'Opcode'}
                                    </Typography>
                                  </Typography>

                                  <Typography
                                    style={{ marginLeft: 45, marginTop: -25 }}
                                  >
                                    {' '}
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.text}
                                    >
                                      :{' '}
                                      <Typography className={classes.text}>
                                        {item.gridormatrixtype}
                                      </Typography>
                                    </Typography>
                                  </Typography>
                                </>
                              )}
                              {props.typeFor == 'matrix' && (
                                <>
                                  <Typography>
                                    {' '}
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      Parts Source
                                    </Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.header}
                                    >
                                      {props.gridRateValueStart == 'cust_fleet'
                                        ? 'Customer'
                                        : props.gridRateValueStart ==
                                          'paytype_fleet'
                                        ? 'Pay Type'
                                        : 'Opcode'}
                                    </Typography>
                                  </Typography>
                                  {/* <Typography
                                    style={{ marginLeft: 45, marginTop: -25 }}
                                  >
                                    {' '}
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.text}
                                    >
                                      {' '}
                                      {item.partSource &&
                                        item.partSource.length > 0 &&
                                        item.partSource.map((items, indexs) => (
                                          <>
                                            {indexs == 0 ? ':       ' : ''}
                                            {indexs < 25 ? (
                                              <Typography
                                                className={classes.text}
                                              >
                                                {items}
                                                {item.partSource.length ==
                                                indexs + 1
                                                  ? ''
                                                  : ','}
                                              </Typography>
                                            ) : (
                                              <>
                                                <Typography
                                                  className={classes.text}
                                                >
                                                  {items}
                                                  {item.partSource.length ==
                                                  indexs + 1
                                                    ? ''
                                                    : ','}
                                                </Typography>
                                              </>
                                            )}
                                          </>
                                        ))}
                                    </Typography>
                                  </Typography> */}
                                  <Typography
                                    style={{ marginLeft: 45, marginTop: -25 }}
                                  >
                                    {' '}
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.text}
                                    >
                                      {' '}
                                      <Typography className={classes.text}>
                                        :{' '}
                                        {item.partSource &&
                                          item.partSource.length > 0 &&
                                          item.partSource.map(
                                            (items, indexs) => <>{items}</>
                                          )}
                                      </Typography>
                                    </Typography>
                                    <Typography
                                      variant="subtitle1"
                                      className={classes.text}
                                    >
                                      :{' '}
                                      <Typography className={classes.text}>
                                        {item.gridormatrixtype}
                                      </Typography>
                                    </Typography>
                                  </Typography>
                                </>
                              )}
                              {/* <Tooltip title="Matrix">
                                <Checkbox
                                  className={classes.root}
                                  onChange={e => handleCheckboxChange(e, item)}
                                  className={
                                    checkBoxError && index == 0
                                      ? classes.checkBoxError
                                      : classes.checkBox
                                  }
                                  size="small"
                                />
                              </Tooltip>
                              {checkBoxError && index == 0 && (
                                <p
                                  style={{
                                    color: 'red',
                                    float: 'inline-end',
                                    paddingTop: 55,
                                    marginRight: -35
                                  }}
                                >
                                  Please select at least one checkbox.
                                </p>
                              )} */}
                            </Grid>
                            <Grid item xs={2}>
                              {installedDateError == true &&
                                item == installedDateItem && (
                                  <FormControl>
                                    <div
                                      style={{
                                        position: 'absolute',
                                        width: '250px',
                                        right: '40px',
                                        top: '15px',
                                        fontSize: '13px',
                                        color: 'red'
                                      }}
                                    >
                                      Enter Dates in MM/DD/YY Format
                                    </div>
                                  </FormControl>
                                )}
                              <FormControl
                                variant="outlined"
                                margin="dense"
                                className={clsx(
                                  classes.formControl,
                                  'input-container'
                                )}
                                style={{ width: '75%' }}
                              >
                                <DateRangePicker
                                  initialSettings={{
                                    locale: {
                                      format: 'MM/DD/YY',
                                      separator: ' - '
                                    },
                                    autoUpdateInput: true,
                                    showDropdowns: true,
                                    autoApply: true,
                                    singleDatePicker: true,

                                    startDate: moment(
                                      dates[index].date,
                                      'MM/DD/YY'
                                    ),
                                    applyClass: clsx(
                                      classes.calButton,
                                      'apply-btn'
                                    ),
                                    cancelClass: clsx(
                                      classes.calButton,
                                      'apply-btn'
                                    )
                                  }}
                                  key={dates}
                                  value={dates[index].date}
                                  onCallback={e => handleCallback(e, index)}
                                >
                                  <input
                                    // readOnly
                                    type="text"
                                    className="datepicker fleet-picker fleet-picker-cls"
                                    id="fleet-picker"
                                    name="picker"
                                    aria-labelledby="label-picker"
                                    style={{
                                      marginTop: -2,
                                      height: 40,
                                      width: '100%'
                                    }}
                                    onClick={e => handleCalenderClick(e, item)}
                                    onChange={e => handleChangeDate(e, item)}
                                    onBlur={e => handleChangeDate(e, item)}
                                  />
                                </DateRangePicker>
                                <label
                                  class="labelpicker fleet-picker"
                                  for="picker"
                                  id="label-picker"
                                >
                                  <div class="textpicker">
                                    Install Date{' '}
                                    <span className={classes.star}>*</span>
                                  </div>
                                </label>
                              </FormControl>
                            </Grid>
                            {/* <Grid item xs={1}>
                              <Tooltip title="Matrix">
                                <Checkbox
                                  className={classes.root}
                                  onChange={e => handleCheckboxChange(e, item)}
                                  className={
                                    checkBoxError && index == 0
                                      ? classes.checkBoxError
                                      : classes.checkBox
                                  }
                                  size="small"
                                />
                              </Tooltip>
                              {checkBoxError && index == 0 && (
                                <p
                                  style={{
                                    color: 'red',
                                    float: 'inline-end',
                                    paddingTop: 55,
                                    marginRight: -35
                                  }}
                                >
                                  Please select at least one checkbox.
                                </p>
                              )}
                            </Grid> */}
                          </Grid>
                        </AccordionSummary>
                        <AccordionDetails>
                          {loading ? (
                            <div
                              className={clsx(
                                classes.dataGrid,
                                'ag-theme-balham fleet-container'
                              )}
                              style={{
                                width: 600,
                                height: window.innerHeight - 505 + 'px',
                                alignContent: 'center',
                                marginLeft: '8px',
                                marginTop: 10
                              }}
                            >
                              <Grid
                                justify="center"
                                className={classes.loaderGrid}
                              >
                                <CircularProgress size={50} />
                              </Grid>
                            </div>
                          ) : (
                            <div
                              className={clsx(
                                classes.dataGrid,
                                'ag-theme-balham fleet-container'
                              )}
                              style={{
                                width: props.typeFor == 'matrix' ? 800 : 1110,
                                // height: window.innerHeight - 560 + 'px',
                                height: window.innerHeight - 400 + 'px',
                                alignContent: 'center',
                                marginLeft: '8px',
                                marginTop: 20
                              }}
                            >
                              <AgGridReact
                                className="ag-theme-balham"
                                style={{
                                  // height: '410px',
                                  width: '60%'
                                }}
                                floatingFilter={true}
                                defaultColDef={defaultColDef}
                                modules={AllModules}
                                columnDefs={
                                  props.typeFor == 'matrix'
                                    ? columnDefs
                                    : columnDefsLabor
                                }
                                rowData={partsDetail}
                                tooltipShowDelay={0}
                                enableRangeSelection={true}
                                animateRows={true}
                                enableCharts={true}
                                overlayNoRowsTemplate={overlayNoRowsTemplate}
                                suppressDragLeaveHidesColumns={true}
                                suppressNoRowsOverlay={false}
                                suppressContextMenu={true}
                              />
                            </div>
                          )}
                        </AccordionDetails>
                      </Accordion>
                    ))
                  ) : isLoading ? (
                    <Grid justify="center" className={classes.loaderGrid}>
                      <CircularProgress size={60} />
                    </Grid>
                  ) : (
                    <Accordion>
                      <AccordionSummary
                        // aria-controls="panel1bh-content"
                        // id="panel1bh-header"
                        style={{ height: '90px' }}
                      >
                        <Grid container spacing={1}>
                          <Grid item xs={12}>
                            {' '}
                            <Typography>
                              {props.typeFor == 'matrix'
                                ? 'Parts matrix data is not available.'
                                : 'Labor grid data is not available.'}
                            </Typography>
                          </Grid>
                        </Grid>
                      </AccordionSummary>
                    </Accordion>
                  )}
                  {/* {matrixData.length > 0 && (
                    <Grid item xs={5} style={{ marginTop: 25, float: 'right' }}>
                      <div>
                        {checkBoxError && (
                          <p
                            style={{
                              color: 'red',
                              // float: 'inline-end',
                              // paddingTop: 40,
                              // marginLeft: -124,
                              fontSize: 11
                            }}
                          >
                            {props.typeFor == 'matrix'
                              ? 'Please select atleast one matrix to save'
                              : 'Please select atleast one grid to save'}
                          </p>
                        )}
                        <Button
                          title="Save"
                          className={clsx('reset-btn', 'btnClass')}
                          variant="contained"
                          color="primary"
                          style={{
                            marginLeft: 5,
                            width: '55px',
                            height: '24px',
                            fontSize: '15px'
                          }}
                          onClick={handleSave}
                        >
                          Save
                        </Button>
                        <Button
                          title="Reset"
                          className={clsx('reset-btn')}
                          style={{ marginLeft: 5, width: '55px' }}
                          variant="contained"
                          onClick={handleClose}
                        >
                          Cancel
                        </Button>
                      </div>
                    </Grid>
                  )} */}
                </div>
              </DialogContent>
              <DialogTitle
                id="alert-dialog-title"
                style={{ marginLeft: 10, paddingRight: 48 }}
              >
                {matrixData.length > 0 && (
                  <Grid
                    container
                    style={{
                      marginTop: 1,
                      display: 'flex',
                      justifyContent: 'flex-end'
                    }}
                  >
                    <div>
                      {checkBoxError && (
                        <p
                          style={{
                            color: 'red',
                            fontSize: 11
                          }}
                        >
                          {props.typeFor === 'matrix'
                            ? 'Please select at least one matrix to save'
                            : 'Please select at least one grid to save'}
                        </p>
                      )}
                      {dateError && (
                        <p
                          style={{
                            color: 'red',
                            fontSize: 11
                          }}
                        >
                          {props.typeFor === 'matrix'
                            ? 'Duplicate install dates are not allowed'
                            : 'Duplicate install dates are not allowed'}
                        </p>
                      )}
                      <Button
                        title="Cancel"
                        className={clsx('reset-btn', 'btnClass')}
                        variant="contained"
                        color="primary"
                        style={{
                          marginLeft: 5,
                          width: '55px',
                          height: '24px',
                          fontSize: '15px',
                          marginTop: 1
                        }}
                        onClick={handleClose}
                      >
                        Cancel
                      </Button>
                      <Button
                        title="Save"
                        className={clsx('reset-btn')}
                        style={{
                          marginLeft: 5,
                          width: '55px',
                          height: '24px',
                          fontSize: '15px',
                          marginTop: 1
                        }}
                        variant="contained"
                        onClick={handleSave}
                        disabled={installedDateError == false ? false : true}
                      >
                        Save
                      </Button>
                    </div>
                  </Grid>
                )}
              </DialogTitle>
              {/* <DialogActions>
                <Button onClick={handleClose}>Cancel</Button>
              </DialogActions> */}
            </>
          )}
        </Dialog>
        {successMsg && (
          <SuccessSnackbar
            open={openSnackbar}
            onClose={hidesnackbar}
            msg={successMsg}
            goalFail={goalFail}
          />
        )}
      </div>
    </Page>
  );
}
