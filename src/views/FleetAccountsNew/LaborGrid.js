import React, { useState, useEffect, useCallback } from 'react';

import '@ag-grid-community/all-modules/dist/styles/ag-grid.css';
import '@ag-grid-community/all-modules/dist/styles/ag-theme-alpine.css';
import { AllModules } from '@ag-grid-enterprise/all-modules';
import 'ag-grid-community/dist/styles/ag-grid.css';
import 'ag-grid-community/dist/styles/ag-theme-material.css';
import 'ag-grid-community/dist/styles/ag-theme-balham.css';
import { AgGridReact } from '@ag-grid-community/react';
import 'src/grid.css';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

import { makeStyles } from '@material-ui/core/styles';
import Accordion from '@material-ui/core/Accordion';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import moment from 'moment';
import {
  Grid,
  Typography,
  Button,
  Tooltip,
  CircularProgress,
  Checkbox,
  Paper
} from '@material-ui/core';
import {
  getMatrixType,
  getMatrixDetail,
  getPartsGridList,
  insertFleetDetails
} from 'src/utils/hasuraServices';
import { setMenuSelected, setNavItems } from 'src/actions';
import { useDispatch, useSelector } from 'react-redux';
import clsx from 'clsx';
import Dialog from '@material-ui/core/Dialog';
import DialogActions from '@material-ui/core/DialogActions';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Page from 'src/components/Page';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import MatrixForm from './MatrixForm';
import IconButton from '@material-ui/core/IconButton';
import CloseIcon from '@material-ui/icons/Close';
import HighlightOffIcon from '@material-ui/icons/HighlightOff';
var lodash = require('lodash');

const Dealer = process.env.REACT_APP_DEALER;
const useStyles = makeStyles(theme => ({
  root: {
    width: '99%',
    marginLeft: 8,
    maxHeight: 700,
    overflowY: 'auto'
  },
  text: {
    fontSize: 13
  },
  heading: {
    fontSize: theme.typography.pxToRem(15),
    flexBasis: '33.33%',
    flexShrink: 0
  },
  secondaryHeading: {
    fontSize: theme.typography.pxToRem(15),
    color: theme.palette.text.secondary
  },
  matrixType: {
    marginTop: -42,
    marginLeft: 150
  },
  loaderGrid: {
    marginLeft: 450,
    marginTop: 120
  },
  header: {
    textTransform: 'none',
    fontSize: 13,
    fontFamily: 'Roboto',
    fontWeight: 'bold',
    color: '#003d6b',
    fontSize: 13
  },
  checkBox: {
    cursor: 'pointer',
    height: 20,
    position: 'absolute',
    right: 45,
    top: 35
  },
  checkBoxError: {
    cursor: 'pointer',
    height: 20,
    position: 'absolute',
    right: 45,
    top: 35,
    color: '#e53935'
  }
}));

export default function LaborGrid(props) {
  let dateArray = [];
  let selectedSource =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.partsSource != ''
      ? props.history.location.state.partsSource
      : '';
  let selectedGridValue =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    (props.history.location.state.selectedGrid ||
      props.history.location.state.selectedGrid == '')
      ? props.history.location.state.selectedGrid
      : 1;
  let selectedPageType =
    props.history &&
    props.history.location &&
    props.history.location.state &&
    props.history.location.state.pageType != ''
      ? props.history.location.state.pageType
      : '';
  const dispatch = useDispatch();
  const session = useSelector(state => state.session);
  const classes = useStyles();
  const [addMatrix, setAddMatrix] = React.useState(false);
  const [expanded, setExpanded] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [type, setType] = React.useState('grid_fleet');
  const [partSrc, setPartSrc] = React.useState(
    selectedSource ? selectedSource : null
  );
  const [payType, setPayType] = React.useState(
    selectedMatrixType ? selectedMatrixType : null
  );
  const [matrixTypeData, setMatrixTypeData] = React.useState([]);
  const [selectedMatrixType, setSelectedMatrixType] = React.useState(null);
  const [matrixData, setMatrixData] = React.useState([]);
  const [gridPeriods, setGridPeriods] = React.useState([]);
  const [selectedGrid, setSelectedGrid] = React.useState(
    selectedGridValue ? selectedGridValue : []
  );
  const [partsDetail, setPartsDetail] = React.useState([]);
  const [rowData, setRowData] = useState([]);
  const [matrixDataAll, setMatrixDataAll] = React.useState([]);
  const [openDialog, setOpenDialog] = useState(false);

  const [createdDate, setCreatedDate] = React.useState([]);
  const [partsSource, setPartsSource] = React.useState('');
  const [errorItems, setErrorItems] = React.useState([]);
  const [overlayNoRowsTemplate, setOverlayNoRowsTemplate] = useState(
    '<span style="padding: 10px;margin-top:50px; ">No rows to show</span>'
  );
  const [pGridorpartsfor, setpGridorpartsfor] = React.useState(
    props.gridRateValueStart
  );
  const [process, setProcess] = React.useState('');
  const [checkBoxError, setCheckBoxError] = React.useState(false);
  useEffect(() => {
    console.log('gridRateValueStart=======', props.gridRateValueStart);
    console.log('props.openDialog==', props.openDialog, '===', props);
    dispatch(setMenuSelected('Fleet Accounts'));
    dispatch(setNavItems(['Reference / Setups']));
    setOpenDialog(props.openDialog);
  }, [props.openDialog]);

  useEffect(() => {
    var gridRateValueStart = props.gridRateValueStart;
    console.log('gridRateValueStart=======', props.gridRateValueStart);
    matrixType(type, partSrc, payType, null, pGridorpartsfor);
  }, [session.storeSelected]);
  const [defaultColDef, setDefaultColDef] = useState({
    enableValue: true,
    sortable: true,
    filter: true,
    resizable: false,
    suppressMovable: false
  });
  const [columnDefs, setColumnDefs] = useState([
    {
      headerName: 'From',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'col0OrpriceStartRange',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      valueFormatter: params => formatCellValue(params),
      // cellClass: 'twoDecimalPlacesWith$',
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'To',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'col1OrpriceEndRange',
      valueFormatter: params => formatCellValue(params),
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Base',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'calcBase',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Break Field',
      chartDataType: 'series',
      // width: 140,
      minWidth: 80,
      field: 'breakField',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return { border: ' 0px white', textAlign: 'left' };
      }
    },
    {
      headerName: 'Percentage',
      field: 'col2OraddPercentage',
      // width: 130,
      minWidth: 80,
      valueFormatter: params => formatCellValueGP(params),
      cellClass: 'textAlign',
      suppressMenu: true,
      unSortIcon: true,
      editable: false,
      flex: 1,
      cellStyle() {
        return {
          border: ' 0px white',
          textAlign: 'left'
        };
      }
    }
  ]);
  const matrixType = (type, partSrc, payType, createddate, pGridorpartsfor) => {
    getPartsGridList(
      type,
      partSrc,
      payType,
      // createddate,
      pGridorpartsfor,
      result => {
        console.log('result==', result);
        let resultArr = [];
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
          val => {
            resultArr.push(val.gridormatrixtype);
          }
        );
        if (
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        ) {
          setMatrixData(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes
          );
          // setSelectedMatrixType(
          //   result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
          //     .nodes[0].gridormatrixtype
          // );
          // setMatrixTypeData(resultArr);
          // matrixDetails(
          //   'matrix',
          //   null,
          //   result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
          //     .nodes[0].gridormatrixtype
          // );
        }
      }
    );
  };
  const matrixDetails = (type, partSrc, payType) => {
    getMatrixType(type, payType, partSrc, result => {
      if (
        result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
      ) {
        // setMatrixDataAll(
        //   result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        // );
        const data =
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes;
        const distinctGridOrders = [
          ...new Set(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes.map(
              item => item.gridOrder
            )
          )
        ];
        // setSelectedGrid(distinctGridOrders[0]);
        // setMatrixData(
        //   data.filter(item => item.gridOrder === distinctGridOrders[0])
        // );
        // setGridPeriods(distinctGridOrders);
        const resultArrary = data.filter(
          item => item.gridOrder === distinctGridOrders[0]
        );
        const resultArr = filterUniqueArrays(resultArrary, 'partSource');
        if (selectedPageType == 'PartsMisses') {
          const filteredData = resultArr.filter(item =>
            item.partSource.includes(selectedSource)
          );
          setMatrixData(filteredData);
        } else {
          setMatrixData(resultArr);
        }
      }
    });
  };

  const filterUniqueArrays = (arr, prop) => {
    const uniqueArrays = [];

    return arr.filter(obj => {
      const arrStr = JSON.stringify(obj[prop]);
      if (!uniqueArrays.includes(arrStr)) {
        uniqueArrays.push(arrStr);
        return true;
      }
      return false;
    });
  };

  const handleChange = panel => (event, isExpanded) => {
    setPartsDetail([]);
    getMatrixDetail(
      'Grid',
      panel.partSource,
      selectedMatrixType,
      panel.createdDate,
      result => {
        let resultArr = [];
        if (
          result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix.nodes
        ) {
          setPartsDetail(
            result.data.statelessDbdKpiScorecardGetKpiScorecardGridormatrix
              .nodes
          );
        }
      }
    );
    setExpanded(isExpanded ? panel : false);
  };
  const formatCellValue = params => {
    if (params.value != null && params.value != 0) {
      return (
        '$' +
        parseFloat(params.value)
          .toFixed(2)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
      );
    } else {
      return '$0.00';
    }
  };
  const formatCellValueGP = params => {
    if (params.value != null && params.value != 0) {
      return (
        parseFloat(params.value)
          .toFixed(1)
          .toString()
          .replace(/\B(?=(\d{3})+(?!\d))/g, ',') + '%'
      );
    } else {
      return '0.0%';
    }
  };
  const formatCellValueDate = params => {
    if (params != null && params != 0) {
      return moment(params).format('MM/DD/YY');
    }
  };

  const showMenuModal = data => {
    setOpenDialog(true);
    // setSelectedMenu(data);
  };
  const handleClose = event => {
    setOpenDialog(false);
    props.handleClose(true);
  };
  const changeProcess = event => {
    console.log('data=========', event.target.value);
    setProcess(event.target.value);
  };
  const handleCheckboxChange = (e, item) => {
    const isChecked = e.target.checked;
    console.log('item===', e, item, '=====', isChecked);
    if (isChecked == false) {
      const filteredDates = createdDate.filter(
        date => date !== item.createdDate
      );
      // Update the state with the filtered array
      setCreatedDate(filteredDates);
    } else {
      // const createdDate = item.createdDate;
      const partsource = item.partsource;

      const newDataArray = [...createdDate, item.createdDate];
      setCreatedDate(newDataArray);
      // dateArray.push(item.createdDate);
      // console.log('dateArray===', dateArray);
      // setCreatedDate(item.createdDate);
      setPartsSource(item.partSource);
    }
    setExpanded(false);
  };
  const showPartsMatrix = event => {
    if (event == 'add') {
      setAddMatrix(true);
    } else {
      setAddMatrix(false);
    }
  };

  const handleSave = event => {
    console.log('createdDate===', createdDate, createdDate.length);

    if (createdDate.length > 0) {
      setCheckBoxError(false);
      let fleet_rate_grid = [];
      let fleet_rate_parts = [];
      let fleet_exists_labor_grid = [];
      let fleet_exists_parts_matrix = [];
      let errorArr = [];
      let typeChecking = props.fleetName;
      // if (props.pGridorpartsfor == 'cust_fleet') {
      //   typeChecking = this.state.selectedFleet;
      // } else {
      //   typeChecking = this.state.selectedPayTypeOptionType;
      // }
      if (typeChecking == '' || typeChecking == 'All') {
        errorArr.push('fleetNameError');
      }
      setErrorItems(errorArr);

      const matrixType =
        props.gridRateValueStart == 'cust_fleet' ? 'Customer' : 'Paytype';
      const fleettype =
        props.gridRateValueStart == 'cust_fleet' ? 'customer' : 'paytype';
      const userid = localStorage.getItem('userID');
      const process = 'insert';
      const fleetname = props.fleetName;

      console.log(
        'fleettype===',
        fleettype,
        '=',
        fleetname,
        '==',
        matrixType,
        '==='
      );
      // let matrixinstalldate = createdDate;
      // let created_date = createdDate;
      // let partsource = partsSource;
      // let matrix_type =
      //   ' <Grid Type value of the already existing Parts Matrix>';
      let parts_for = 'source';
      let arr = ['200'];
      let arra = ['200'];
      console.log('partsSource==', partsSource, '===', createdDate);

      createdDate.map((createDat, index) =>
        fleet_exists_labor_grid.push({
          process: process,
          fleettype: fleettype,
          fleetname: fleetname,
          fleetormatrixflag: 'Matrix',
          matrixinstalldate: createDat,
          created_date: createDat,
          partsource: partsSource,
          matrix_type: matrixType,
          parts_for: parts_for
        })
      );

      console.log(
        'fleet_exists_parts_matrix==',
        fleet_exists_parts_matrix,
        partsSource
      );
      console.log('fleet_exists_parts_matrix==', fleet_exists_parts_matrix);
      if (errorArr.length <= 0) {
        insertFleetDetails(
          JSON.stringify(fleet_rate_grid),
          JSON.stringify(fleet_rate_parts),
          JSON.stringify(fleet_exists_labor_grid),
          JSON.stringify(fleet_exists_parts_matrix),
          result => {
            console.log('success data====', result);
            if (
              result.data.statelessCcPhysicalRwInsertOrUpdateFleetAccount
                .string == 'Success'
            ) {
              // this.getAgGridData();
              // this.gridApiLabor.redrawRows();
              // this.gridApiParts.redrawRows();
              // this.clearLaborGrid();
              // this.clearPartsGrid();
              // this.setState({ openSnackbar: true });
            } else {
              // this.setState({ openSnackbarError: true });
            }
          }
        );
      }
    } else {
      setCheckBoxError(true);
    }
  };
  return (
    <Page title="Fleet Accounts">
      <div>
        <Dialog
          open={openDialog}
          onClose={handleClose}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
          PaperProps={{
            style: {
              width: '90%',
              marginLeft: '20px',
              marginTop: '28px',
              height: 1050,
              maxWidth: '90%',
              backgroundColor: '#F4F6F8'
            }
          }}
        >
          <DialogTitle id="alert-dialog-title" style={{ marginLeft: 10 }}>
            <Typography
              variant="h4"
              color="primary"
              style={{
                textTransform: 'none'
              }}
            >
              Labor Grid
            </Typography>
            <Tooltip title="Close">
              <IconButton
                onClick={handleClose}
                aria-label="close"
                style={{ float: 'inline-end' }}
              >
                <HighlightOffIcon style={{ fontSize: 36 }} />
              </IconButton>
            </Tooltip>
            <MatrixForm showPartsMatrix={showPartsMatrix} />
          </DialogTitle>
          {addMatrix == true && (
            <>
              <DialogContent style={{ display: 'flex', flexDirection: 'row' }}>
                <div className={classes.root}>
                  {matrixData.length > 0 ? (
                    matrixData.map(
                      (item, index) => (
                        // index < 1 && (
                        <Accordion
                          expanded={expanded === item}
                          onChange={handleChange(item)}
                        >
                          <AccordionSummary
                            expandIcon={<ExpandMoreIcon />}
                            aria-controls="panel1bh-content"
                            id="panel1bh-header"
                            style={{ height: '90px' }}
                          >
                            <Grid container spacing={2}>
                              <Grid item xs={3}>
                                <Typography>
                                  {' '}
                                  <Typography
                                    variant="subtitle1"
                                    className={classes.header}
                                    // variant="h14"
                                  >
                                    Store Install
                                    Date&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                  </Typography>
                                  <Typography
                                    variant="subtitle1"
                                    className={classes.header}
                                  >
                                    FOPC Calculated Date
                                    From&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                  </Typography>
                                </Typography>
                                <Typography>
                                  {' '}
                                  <Typography
                                    variant="subtitle1"
                                    className={classes.text}
                                  >
                                    :{' '}
                                    {formatCellValueDate(item.storeInstallDate)}
                                  </Typography>
                                  <Typography
                                    variant="subtitle1"
                                    className={classes.text}
                                  >
                                    : {formatCellValueDate(item.createdDate)}
                                  </Typography>
                                </Typography>
                              </Grid>
                              <Grid item xs={9}>
                                <Typography>
                                  {' '}
                                  <Typography
                                    variant="subtitle1"
                                    className={classes.header}
                                  >
                                    Parts Source
                                  </Typography>
                                </Typography>
                                <Typography
                                  style={{ marginLeft: 45, marginTop: -25 }}
                                >
                                  {' '}
                                  <Typography
                                    variant="subtitle1"
                                    className={classes.text}
                                  >
                                    {' '}
                                    {item.partSource &&
                                      item.partSource.length > 0 &&
                                      item.partSource.map((items, indexs) => (
                                        <>
                                          {indexs == 0 ? ':       ' : ''}
                                          {indexs < 25 ? (
                                            <Typography
                                              className={classes.text}
                                            >
                                              {items}
                                              {item.partSource.length ==
                                              indexs + 1
                                                ? ''
                                                : ','}
                                            </Typography>
                                          ) : (
                                            <>
                                              {/* {indexs == 25 ? '&nbsp;' : ''} */}

                                              <Typography
                                                className={classes.text}
                                              >
                                                {items}
                                                {item.partSource.length ==
                                                indexs + 1
                                                  ? ''
                                                  : ','}
                                              </Typography>
                                            </>
                                          )}
                                        </>
                                      ))}
                                  </Typography>
                                </Typography>
                                <Tooltip title="Matrix">
                                  <Checkbox
                                    className={classes.root}
                                    // type="checkbox"
                                    // checked={
                                    //   item.menuIsdefault == '1' ? true : false
                                    // }
                                    onChange={e =>
                                      handleCheckboxChange(e, item)
                                    }
                                    // value={
                                    //   item.menuIsdefault == '1' ? true : false
                                    // }
                                    // id={item.menuName}

                                    className={
                                      checkBoxError && index == 0
                                        ? classes.checkBoxError
                                        : classes.checkBox
                                    }
                                    size="small"
                                  />

                                  {/* {checkBoxError && index == 0 && (
                                    <p
                                      style={{
                                        marginTop: 10,
                                        color: 'tomato',
                                        marginLeft: 6,
                                        fontSize: '90'
                                      }}
                                    >
                                      Please select at least one checkbox.
                                    </p>
                                  )} */}
                                </Tooltip>
                                {checkBoxError && index == 0 && (
                                  <p
                                    style={{
                                      color: 'red',
                                      float: 'inline-end',
                                      paddingTop: 55,
                                      marginRight: -35
                                    }}
                                  >
                                    Please select at least one checkbox.
                                  </p>
                                )}
                              </Grid>
                            </Grid>
                          </AccordionSummary>
                          <AccordionDetails>
                            {partsDetail.length > 0 ? (
                              <div
                                className={clsx(
                                  classes.dataGrid,
                                  'ag-theme-balham fleet-container'
                                )}
                                style={{
                                  width: 800,
                                  height: window.innerHeight - 560 + 'px',
                                  alignContent: 'center',
                                  marginLeft: '8px',
                                  marginTop: 20
                                }}
                              >
                                <AgGridReact
                                  className="ag-theme-balham"
                                  style={{
                                    height: '410px',
                                    width: '60%'
                                  }}
                                  floatingFilter={true}
                                  defaultColDef={defaultColDef}
                                  // headerHeight={headerHeight}
                                  modules={AllModules}
                                  columnDefs={columnDefs}
                                  // onGridReady={onGridReady}
                                  rowData={partsDetail}
                                  tooltipShowDelay={0}
                                  enableRangeSelection={true}
                                  animateRows={true}
                                  enableCharts={true}
                                  overlayNoRowsTemplate={overlayNoRowsTemplate}
                                  // excelStyles={excelStyles}
                                  suppressDragLeaveHidesColumns={true}
                                  suppressNoRowsOverlay={false}
                                  suppressContextMenu={true}
                                />
                              </div>
                            ) : (
                              <div
                                className={clsx(
                                  classes.dataGrid,
                                  'ag-theme-balham fleet-container'
                                )}
                                style={{
                                  width: 600,
                                  height: window.innerHeight - 505 + 'px',
                                  alignContent: 'center',
                                  marginLeft: '8px',
                                  marginTop: 10
                                }}
                              >
                                <Grid
                                  justify="center"
                                  className={classes.loaderGrid}
                                >
                                  <CircularProgress size={50} />
                                </Grid>
                              </div>
                            )}
                          </AccordionDetails>
                        </Accordion>
                      )
                      // )
                    )
                  ) : (
                    <Grid justify="center" className={classes.loaderGrid}>
                      <CircularProgress size={60} />
                    </Grid>
                  )}
                  <Grid item xs={2} style={{ marginTop: 25 }}>
                    <div>
                      <Button
                        title="Save"
                        className={clsx('reset-btn', 'btnClass')}
                        variant="contained"
                        color="primary"
                        style={{
                          marginLeft: 5,
                          width: '55px',
                          height: '24px',
                          fontSize: '15px'
                        }}
                        // disabled={
                        //   this.state.doorRateChanged ||
                        //   this.state.fixedDoorRateChanged ||
                        //   this.state.fixedRateChanged ||
                        //   this.state.markupChanged ||
                        //   this.state.matrixChanged ||
                        //   this.state.markupValueChanged
                        //     ? false
                        //     : true
                        // }
                        onClick={handleSave}
                      >
                        Save
                      </Button>
                      <Button
                        title="Reset"
                        className={clsx('reset-btn')}
                        style={{ marginLeft: 5, width: '55px' }}
                        variant="contained"
                        onClick={handleClose}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Grid>
                </div>
              </DialogContent>

              <DialogActions>
                <Button onClick={handleClose}>Cancel</Button>
              </DialogActions>
            </>
          )}
        </Dialog>
      </div>
    </Page>
  );
}
